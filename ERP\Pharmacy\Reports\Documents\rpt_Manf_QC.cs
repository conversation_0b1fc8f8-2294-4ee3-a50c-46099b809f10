using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using DAL;
using System.Linq;
using System.Data;
using DevExpress.XtraEditors;

using System.Windows.Forms;

namespace Reports
{
    public partial class rpt_Manf_QC : DevExpress.XtraReports.UI.XtraReport
    {
        string serial, number, date,
            storeFrom, storeTo, storeFromManager, storeToManager, notes,
            userName, totalP, totalS, EmpName;
        DataTable dt_inv_details;

        public rpt_Manf_QC()
        {
            InitializeComponent();
        }

        //    Reports.rpt_IC_StoreMove r = new Reports.rpt_IC_StoreMove(txtInvoiceId.Text, txtInvoiceCode.Text, dtInvoiceDate.Text,
        //lkpStoreFrom.Text, lkpStoreTo.Text, storeFromManager, storeToManager, txtNotes.Text,
        //dt_PrintTable, Shared.UserName);

        public rpt_Manf_QC(string _serial, string _number, string _date,
            string _storeFrom, string _storeTo, string _storeFromManager, string _storeToManager, string _notes,
            DataTable dt, string userName, string _totalP, string _totalS,string _EmpName)
        {
            InitializeComponent();

            serial = _serial;
            number = _number;
            date = _date;
            storeFrom = _storeFrom;
            storeTo = _storeTo;
            storeFromManager = _storeFromManager;
            storeToManager = _storeToManager;
            notes = _notes;

            this.userName = userName;
            totalP = _totalP;
            totalS = _totalS;

            EmpName = _EmpName;

            dt_inv_details = dt;
            this.DataSource = dt_inv_details;
            getReportHeader();
            LoadData();
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                lblCompName.Text = comp.CmpNameAr;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        public void LoadData()
        {
            lbl_Serial.Text = serial;
            lbl_Number.Text = number;
            lbl_date.Text = date;
            lbl_storeFrom.Text = storeFrom;
            lbl_storeTo.Text = storeTo;
            lbl_notes.Text = notes;
            lbl_storeFromManager.Text = storeFromManager;
            lbl_storeToManager.Text = storeToManager;
            lbl_User.Text = userName;

            lbl_Total_P_Price.Text = totalP;
            lbl_Total_S_Price.Text = totalS;
            lblTotal_P_Words.Text = Shared.IsEnglish ? HelperAcc.ConvertMoneyToText(totalP, 0, Shared.lstCurrency) :
                       HelperAcc.ConvertMoneyToArabicText(totalP, 0, Shared.lstCurrency);
            lblTotal_S_Words.Text = Shared.IsEnglish ? HelperAcc.ConvertMoneyToText(totalS, 0, Shared.lstCurrency) :
                       HelperAcc.ConvertMoneyToArabicText(totalS, 0, Shared.lstCurrency);

            lbl_EmpName.Text = EmpName;

            this.DataSource = dt_inv_details;

            cell_code.DataBindings.Add("Text", this.DataSource, "ItemCode1");
            cell_code2.DataBindings.Add("Text", this.DataSource, "ItemCode2");
            cell_ItemName.DataBindings.Add("Text", this.DataSource, "ItemName");
            cell_UOM.DataBindings.Add("Text", this.DataSource, "UOM");
            cell_Qty.DataBindings.Add("Text", this.DataSource, "Qty");
            cell_Expire.DataBindings.Add("Text", this.DataSource, "Expire");
            cell_Batch.DataBindings.Add("Text", this.DataSource, "Batch");
            cell_P_Price.DataBindings.Add("Text", this.DataSource, "PurchasePrice");
            cell_S_Price.DataBindings.Add("Text", this.DataSource, "SellPrice");
            cell_Total_P_Price.DataBindings.Add("Text", this.DataSource, "TotalPurchasePrice");
            cell_Total_S_Price.DataBindings.Add("Text", this.DataSource, "TotalSellPrice");
        }
    }
}
