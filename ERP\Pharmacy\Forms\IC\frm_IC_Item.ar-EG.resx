﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="txtMediumUOMFactor.Location" type="System.Drawing.Point, System.Drawing">
    <value>445, 53</value>
  </data>
  <assembly alias="DevExpress.XtraEditors.v15.1" name="DevExpress.XtraEditors.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="txtMediumUOMFactor.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="txtMediumUOMFactor.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="labelControl15.Location" type="System.Drawing.Point, System.Drawing">
    <value>698, 43</value>
  </data>
  <data name="labelControl15.Size" type="System.Drawing.Size, System.Drawing">
    <value>21, 13</value>
  </data>
  <data name="labelControl15.Text" xml:space="preserve">
    <value>كود1</value>
  </data>
  <data name="txtItemCode1.Location" type="System.Drawing.Point, System.Drawing">
    <value>550, 39</value>
  </data>
  <data name="txtItemCode1.Size" type="System.Drawing.Size, System.Drawing">
    <value>142, 20</value>
  </data>
  <data name="barBtnHelp.Caption" xml:space="preserve">
    <value>مساعدة</value>
  </data>
  <data name="barBtnNew.Caption" xml:space="preserve">
    <value>جديد</value>
  </data>
  <data name="barBtnDelete.Caption" xml:space="preserve">
    <value>حذف</value>
  </data>
  <data name="barBtnSave.Caption" xml:space="preserve">
    <value>حفظ</value>
  </data>
  <data name="barBtnList.Caption" xml:space="preserve">
    <value>القائمه</value>
  </data>
  <data name="barBtnClose.Caption" xml:space="preserve">
    <value>غلق</value>
  </data>
  <data name="barDockControlTop.Size" type="System.Drawing.Size, System.Drawing">
    <value>782, 31</value>
  </data>
  <data name="barDockControlBottom.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 621</value>
  </data>
  <data name="barDockControlBottom.Size" type="System.Drawing.Size, System.Drawing">
    <value>782, 0</value>
  </data>
  <data name="barDockControlLeft.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 590</value>
  </data>
  <data name="barDockControlRight.Location" type="System.Drawing.Point, System.Drawing">
    <value>782, 31</value>
  </data>
  <data name="barDockControlRight.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 590</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>782, 621</value>
  </data>
  <data name="tab_Control1.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 89</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="tab_Control1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Inherit</value>
  </data>
  <data name="labelControl35.Size" type="System.Drawing.Size, System.Drawing">
    <value>60, 13</value>
  </data>
  <data name="labelControl35.Text" xml:space="preserve">
    <value>سعر الجمهور</value>
  </data>
  <data name="txtAudiancePrice.Location" type="System.Drawing.Point, System.Drawing">
    <value>342, 154</value>
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="tabExtraData.RightToLeftLayout" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>True</value>
  </data>
  <data name="labelControl27.Location" type="System.Drawing.Point, System.Drawing">
    <value>178, 26</value>
  </data>
  <data name="labelControl27.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 13</value>
  </data>
  <data name="labelControl27.Text" xml:space="preserve">
    <value>الحد الأقصي</value>
  </data>
  <data name="txtMinQty.Location" type="System.Drawing.Point, System.Drawing">
    <value>259, 22</value>
  </data>
  <data name="txtMinQty.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="txtMaxQty.Location" type="System.Drawing.Point, System.Drawing">
    <value>124, 22</value>
  </data>
  <data name="txtMaxQty.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="txtReorder.Location" type="System.Drawing.Point, System.Drawing">
    <value>383, 22</value>
  </data>
  <data name="txtReorder.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl8.Location" type="System.Drawing.Point, System.Drawing">
    <value>313, 26</value>
  </data>
  <data name="labelControl8.Size" type="System.Drawing.Size, System.Drawing">
    <value>51, 13</value>
  </data>
  <data name="labelControl8.Text" xml:space="preserve">
    <value>الحد الأدني</value>
  </data>
  <data name="labelControl28.Location" type="System.Drawing.Point, System.Drawing">
    <value>438, 26</value>
  </data>
  <data name="labelControl28.Size" type="System.Drawing.Size, System.Drawing">
    <value>44, 13</value>
  </data>
  <data name="labelControl28.Text" xml:space="preserve">
    <value>حد الطلب</value>
  </data>
  <data name="tab_InventoryLevels.Text" xml:space="preserve">
    <value>مستويات المخزون</value>
  </data>
  <data name="tabExtraData.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl26.Size" type="System.Drawing.Size, System.Drawing">
    <value>82, 13</value>
  </data>
  <data name="labelControl26.Text" xml:space="preserve">
    <value>عدد شهور الضمان</value>
  </data>
  <data name="tabWarranty.Text" xml:space="preserve">
    <value>الضمان</value>
  </data>
  <data name="cmbChangeSellPrice.Location" type="System.Drawing.Point, System.Drawing">
    <value>73, 34</value>
  </data>
  <data name="cmbChangeSellPrice.Properties.Items" xml:space="preserve">
    <value>نعم</value>
  </data>
  <data name="cmbChangeSellPrice.Properties.Items3" xml:space="preserve">
    <value>لا</value>
  </data>
  <data name="labelControl5.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 37</value>
  </data>
  <data name="labelControl5.Size" type="System.Drawing.Size, System.Drawing">
    <value>213, 13</value>
  </data>
  <data name="labelControl5.Text" xml:space="preserve">
    <value>تغيير سعر البيع بنفس نسبة تغيير سعر التكلفة</value>
  </data>
  <data name="cmbChangePriceMethod.Location" type="System.Drawing.Point, System.Drawing">
    <value>72, 9</value>
  </data>
  <data name="cmbChangePriceMethod.Properties.Items" xml:space="preserve">
    <value>اخر سعر</value>
  </data>
  <data name="cmbChangePriceMethod.Properties.Items3" xml:space="preserve">
    <value>سعر ثابت</value>
  </data>
  <data name="cmbChangePriceMethod.Properties.Items6" xml:space="preserve">
    <value>سعر متوسط</value>
  </data>
  <data name="labelControl10.Location" type="System.Drawing.Point, System.Drawing">
    <value>175, 12</value>
  </data>
  <data name="labelControl10.Size" type="System.Drawing.Size, System.Drawing">
    <value>232, 13</value>
  </data>
  <data name="labelControl10.Text" xml:space="preserve">
    <value>عند تغيير سعر التكلفه في فاتورة الشراء يتم اعتماد</value>
  </data>
  <data name="tabPriceChange.Text" xml:space="preserve">
    <value>تغير الأسعار</value>
  </data>
  <data name="labelControl22.Location" type="System.Drawing.Point, System.Drawing">
    <value>220, 14</value>
  </data>
  <data name="labelControl22.Size" type="System.Drawing.Size, System.Drawing">
    <value>26, 13</value>
  </data>
  <data name="labelControl22.Text" xml:space="preserve">
    <value>الطول</value>
  </data>
  <data name="txtLength.EditValue" type="System.Decimal, mscorlib">
    <value>1</value>
  </data>
  <data name="txtLength.Location" type="System.Drawing.Point, System.Drawing">
    <value>157, 11</value>
  </data>
  <data name="labelControl17.Location" type="System.Drawing.Point, System.Drawing">
    <value>327, 14</value>
  </data>
  <data name="labelControl17.Size" type="System.Drawing.Size, System.Drawing">
    <value>30, 13</value>
  </data>
  <data name="labelControl17.Text" xml:space="preserve">
    <value>العرض</value>
  </data>
  <data name="txtWidth.EditValue" type="System.Decimal, mscorlib">
    <value>1</value>
  </data>
  <data name="txtWidth.Location" type="System.Drawing.Point, System.Drawing">
    <value>264, 11</value>
  </data>
  <data name="labelControl12.Location" type="System.Drawing.Point, System.Drawing">
    <value>435, 14</value>
  </data>
  <data name="labelControl12.Size" type="System.Drawing.Size, System.Drawing">
    <value>33, 13</value>
  </data>
  <data name="labelControl12.Text" xml:space="preserve">
    <value>الإرتفاع</value>
  </data>
  <data name="txtHeight.EditValue" type="System.Decimal, mscorlib">
    <value>1</value>
  </data>
  <data name="txtHeight.Location" type="System.Drawing.Point, System.Drawing">
    <value>372, 11</value>
  </data>
  <data name="tabDimension.Text" xml:space="preserve">
    <value>الأبعاد</value>
  </data>
  <data name="lblSalesDiscountRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>178, 15</value>
  </data>
  <data name="lblSalesDiscountRatio.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 13</value>
  </data>
  <data name="lblSalesDiscountRatio.Text" xml:space="preserve">
    <value>ن خصم البيع</value>
  </data>
  <data name="labelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>376, 15</value>
  </data>
  <data name="labelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>87, 13</value>
  </data>
  <data name="labelControl1.Text" xml:space="preserve">
    <value>ن خصم عند الشراء</value>
  </data>
  <data name="txtSalesDiscRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>116, 12</value>
  </data>
  <data name="txtSalesDiscRatio.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 20</value>
  </data>
  <data name="labelControl7.Location" type="System.Drawing.Point, System.Drawing">
    <value>301, 15</value>
  </data>
  <data name="labelControl25.Location" type="System.Drawing.Point, System.Drawing">
    <value>103, 15</value>
  </data>
  <data name="txtPurchaseDiscRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>313, 12</value>
  </data>
  <data name="tabDiscount.Text" xml:space="preserve">
    <value>الخصومات</value>
  </data>
  <data name="txtCustomSalesTaxRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>290, 58</value>
  </data>
  <data name="txtCustomSalesTaxRatio.Size" type="System.Drawing.Size, System.Drawing">
    <value>76, 20</value>
  </data>
  <data name="labelControl31.Size" type="System.Drawing.Size, System.Drawing">
    <value>114, 13</value>
  </data>
  <data name="labelControl31.Text" xml:space="preserve">
    <value>ضريبة الجدول للمشتريات</value>
  </data>
  <data name="labelControl32.Location" type="System.Drawing.Point, System.Drawing">
    <value>278, 61</value>
  </data>
  <data name="labelControl33.Location" type="System.Drawing.Point, System.Drawing">
    <value>372, 61</value>
  </data>
  <data name="labelControl33.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 13</value>
  </data>
  <data name="labelControl33.Text" xml:space="preserve">
    <value>ضريبة الجدول للمبيعات</value>
  </data>
  <data name="chk_calcTaxBeforeDisc.Location" type="System.Drawing.Point, System.Drawing">
    <value>253, 95</value>
  </data>
  <data name="chk_calcTaxBeforeDisc.Properties.Caption" xml:space="preserve">
    <value>حساب الضريبة قبل الخصم</value>
  </data>
  <data name="txtSalesTaxRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>290, 31</value>
  </data>
  <data name="txtSalesTaxRatio.Properties.Mask.EditMask" xml:space="preserve">
    <value>n6</value>
  </data>
  <data name="txtSalesTaxRatio.Size" type="System.Drawing.Size, System.Drawing">
    <value>76, 20</value>
  </data>
  <data name="lblPrTaxRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>372, 9</value>
  </data>
  <data name="lblPrTaxRatio.Size" type="System.Drawing.Size, System.Drawing">
    <value>82, 13</value>
  </data>
  <data name="lblPrTaxRatio.Text" xml:space="preserve">
    <value>ن ضريبة مشتريات</value>
  </data>
  <data name="labelControl16.Location" type="System.Drawing.Point, System.Drawing">
    <value>273, 34</value>
  </data>
  <data name="lblPrTaxVal.Location" type="System.Drawing.Point, System.Drawing">
    <value>146, 9</value>
  </data>
  <data name="lblPrTaxVal.Size" type="System.Drawing.Size, System.Drawing">
    <value>85, 13</value>
  </data>
  <data name="lblPrTaxVal.Text" xml:space="preserve">
    <value>ق ضريبة مشتريات</value>
  </data>
  <data name="txtSalesTaxValue.Location" type="System.Drawing.Point, System.Drawing">
    <value>71, 31</value>
  </data>
  <data name="txtSalesTaxValue.Properties.Mask.EditMask" xml:space="preserve">
    <value>n3</value>
  </data>
  <data name="txtPurchaseTaxValue.Location" type="System.Drawing.Point, System.Drawing">
    <value>71, 6</value>
  </data>
  <data name="txtPurchaseTaxValue.Properties.Mask.EditMask" xml:space="preserve">
    <value>n3</value>
  </data>
  <data name="lblSalesTaxRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>372, 34</value>
  </data>
  <data name="lblSalesTaxRatio.Size" type="System.Drawing.Size, System.Drawing">
    <value>72, 13</value>
  </data>
  <data name="lblSalesTaxRatio.Text" xml:space="preserve">
    <value>ن ضريبة مبيعات</value>
  </data>
  <data name="labelControl14.Location" type="System.Drawing.Point, System.Drawing">
    <value>272, 9</value>
  </data>
  <data name="lblSalesTaxValue.Location" type="System.Drawing.Point, System.Drawing">
    <value>146, 34</value>
  </data>
  <data name="lblSalesTaxValue.Text" xml:space="preserve">
    <value>ق ضريبة مبيعات</value>
  </data>
  <data name="txtPurchaseTaxRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>290, 6</value>
  </data>
  <data name="txtPurchaseTaxRatio.Properties.Mask.EditMask" xml:space="preserve">
    <value>n6</value>
  </data>
  <data name="txtPurchaseTaxRatio.Size" type="System.Drawing.Size, System.Drawing">
    <value>76, 20</value>
  </data>
  <data name="tabTax.Text" xml:space="preserve">
    <value>الضرائب</value>
  </data>
  <data name="SubTaxId.Caption" xml:space="preserve">
    <value>الضريبة</value>
  </data>
  <data name="lkp_SubTaxes.Columns" xml:space="preserve">
    <value>DescriptionAr</value>
  </data>
  <data name="lkp_SubTaxes.Columns1" xml:space="preserve">
    <value>الضريبة</value>
  </data>
  <data name="lkp_SubTaxes.Columns3" xml:space="preserve">
    <value>الكود</value>
  </data>
  <data name="col_Rate.Caption" xml:space="preserve">
    <value>النسبة</value>
  </data>
  <data name="grd_SubTaxes.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="tab_SubTaxes.Text" xml:space="preserve">
    <value>الضرايب الفرعية</value>
  </data>
  <data name="grpExtra.Location" type="System.Drawing.Point, System.Drawing">
    <value>220, 328</value>
  </data>
  <data name="grpExtra.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="grpExtra.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="grpExtra.Text" xml:space="preserve">
    <value>بيانات أخرى</value>
  </data>
  <data name="labelControl9.Location" type="System.Drawing.Point, System.Drawing">
    <value>405, 53</value>
  </data>
  <data name="labelControl9.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 13</value>
  </data>
  <data name="labelControl9.Text" xml:space="preserve">
    <value>الوصف ج</value>
  </data>
  <data name="lkpCategory.Location" type="System.Drawing.Point, System.Drawing">
    <value>505, 91</value>
  </data>
  <data name="lkpCategory.Properties.Columns1" xml:space="preserve">
    <value>اسم الفئة</value>
  </data>
  <data name="lkpCategory.Properties.Columns8" xml:space="preserve">
    <value>اسم الفئة</value>
  </data>
  <data name="lkpCategory.Properties.Columns15" xml:space="preserve">
    <value>كود الفئة</value>
  </data>
  <data name="lkpCategory.Size" type="System.Drawing.Size, System.Drawing">
    <value>145, 20</value>
  </data>
  <data name="lkpCategory.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="labelControl11.Location" type="System.Drawing.Point, System.Drawing">
    <value>656, 94</value>
  </data>
  <data name="labelControl11.Size" type="System.Drawing.Size, System.Drawing">
    <value>47, 13</value>
  </data>
  <data name="labelControl11.Text" xml:space="preserve">
    <value>فئة الصنف</value>
  </data>
  <data name="labelControl18.Location" type="System.Drawing.Point, System.Drawing">
    <value>656, 53</value>
  </data>
  <data name="labelControl18.Size" type="System.Drawing.Size, System.Drawing">
    <value>30, 13</value>
  </data>
  <data name="labelControl18.Text" xml:space="preserve">
    <value>الوصف</value>
  </data>
  <data name="chk_VariableWeight.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="chk_VariableWeight.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 19</value>
  </data>
  <data name="chk_PricingWithSmall.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="chk_PricingWithSmall.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 155</value>
  </data>
  <data name="chk_PricingWithSmall.Properties.Caption" xml:space="preserve">
    <value>التسعير بالتجزئة</value>
  </data>
  <data name="chk_PricingWithSmall.Size" type="System.Drawing.Size, System.Drawing">
    <value>96, 19</value>
  </data>
  <data name="chk_IsLibra.Location" type="System.Drawing.Point, System.Drawing">
    <value>54, 133</value>
  </data>
  <data name="chk_IsLibra.Properties.Caption" xml:space="preserve">
    <value>ليبرا</value>
  </data>
  <data name="chk_IsLibra.Size" type="System.Drawing.Size, System.Drawing">
    <value>44, 19</value>
  </data>
  <data name="chk_IsPos.Location" type="System.Drawing.Point, System.Drawing">
    <value>98, 155</value>
  </data>
  <data name="chk_IsPos.Properties.Caption" xml:space="preserve">
    <value>صنف نقطة بيع</value>
  </data>
  <data name="chkIsExpire.Location" type="System.Drawing.Point, System.Drawing">
    <value>89, 130</value>
  </data>
  <data name="chkIsExpire.Properties.Caption" xml:space="preserve">
    <value> صنف صلاحية</value>
  </data>
  <data name="chkIsExpire.Size" type="System.Drawing.Size, System.Drawing">
    <value>107, 19</value>
  </data>
  <data name="chkIsExpire.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="rdoItemType.Location" type="System.Drawing.Point, System.Drawing">
    <value>49, 15</value>
  </data>
  <data name="rdoItemType.Properties.Items1" xml:space="preserve">
    <value>مخزني</value>
  </data>
  <data name="rdoItemType.Properties.Items3" xml:space="preserve">
    <value>خدمة</value>
  </data>
  <data name="rdoItemType.Properties.Items5" xml:space="preserve">
    <value>مجمع</value>
  </data>
  <data name="rdoItemType.Properties.Items7" xml:space="preserve">
    <value>مصفوفة</value>
  </data>
  <data name="rdoItemType.Properties.Items9" xml:space="preserve">
    <value>مجموع فرعي</value>
  </data>
  <data name="rdoItemType.Size" type="System.Drawing.Size, System.Drawing">
    <value>147, 112</value>
  </data>
  <data name="rdoItemType.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="grp_ItemType.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 7</value>
  </data>
  <data name="grp_ItemType.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="grp_ItemType.Size" type="System.Drawing.Size, System.Drawing">
    <value>211, 203</value>
  </data>
  <data name="grp_ItemType.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="grp_ItemType.Text" xml:space="preserve">
    <value>نوع الصنف</value>
  </data>
  <data name="btnAddComp.Location" type="System.Drawing.Point, System.Drawing">
    <value>231, 91</value>
  </data>
  <data name="btnAddComp.ToolTip" xml:space="preserve">
    <value>إضافة شركة منتجة الي قائمة الشركات المنتجة</value>
  </data>
  <data name="lstInternationalCodes.Location" type="System.Drawing.Point, System.Drawing">
    <value>56, 40</value>
  </data>
  <data name="lstInternationalCodes.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="btn_AddNewInter_Code.Location" type="System.Drawing.Point, System.Drawing">
    <value>28, 17</value>
  </data>
  <data name="btn_AddNewInter_Code.ToolTip" xml:space="preserve">
    <value>اضافة كود دولي للصنف</value>
  </data>
  <data name="txtInternationalCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>56, 17</value>
  </data>
  <data name="txtInternationalCode.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="btnDelete.Location" type="System.Drawing.Point, System.Drawing">
    <value>28, 40</value>
  </data>
  <data name="btnDelete.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="btnDelete.ToolTip" xml:space="preserve">
    <value>حذف كود دولي</value>
  </data>
  <data name="grp_international.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 328</value>
  </data>
  <data name="grp_international.Size" type="System.Drawing.Size, System.Drawing">
    <value>211, 121</value>
  </data>
  <data name="grp_international.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="grp_international.Text" xml:space="preserve">
    <value>الأكود الدولية</value>
  </data>
  <data name="btnAddCat.Location" type="System.Drawing.Point, System.Drawing">
    <value>479, 91</value>
  </data>
  <data name="btnAddCat.ToolTip" xml:space="preserve">
    <value>إضافة فئه الي قائمة الفئات</value>
  </data>
  <data name="labelControl3.Location" type="System.Drawing.Point, System.Drawing">
    <value>656, 18</value>
  </data>
  <data name="labelControl3.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 13</value>
  </data>
  <data name="labelControl3.Text" xml:space="preserve">
    <value>اسم الصنف ج</value>
  </data>
  <data name="lkpComp.Location" type="System.Drawing.Point, System.Drawing">
    <value>258, 91</value>
  </data>
  <data name="lkpComp.Properties.Columns1" xml:space="preserve">
    <value>اسم المجموعة ج</value>
  </data>
  <data name="lkpComp.Properties.Columns8" xml:space="preserve">
    <value>اسم المجموعة</value>
  </data>
  <data name="lkpComp.Properties.Columns14" xml:space="preserve">
    <value>CompanyCode</value>
  </data>
  <data name="lkpComp.Properties.Columns15" xml:space="preserve">
    <value>الكود</value>
  </data>
  <data name="lkpComp.Size" type="System.Drawing.Size, System.Drawing">
    <value>141, 20</value>
  </data>
  <data name="lkpComp.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="txtItemNameEn.Location" type="System.Drawing.Point, System.Drawing">
    <value>231, 15</value>
  </data>
  <data name="txtItemNameEn.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl36.Location" type="System.Drawing.Point, System.Drawing">
    <value>417, 131</value>
  </data>
  <data name="labelControl36.Size" type="System.Drawing.Size, System.Drawing">
    <value>108, 13</value>
  </data>
  <data name="labelControl36.Text" xml:space="preserve">
    <value>سعر شراء وحدة التجزئة</value>
  </data>
  <data name="labelControl6.Location" type="System.Drawing.Point, System.Drawing">
    <value>405, 94</value>
  </data>
  <data name="labelControl6.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 13</value>
  </data>
  <data name="labelControl6.Text" xml:space="preserve">
    <value>المجموعة</value>
  </data>
  <data name="chk_LargeIsStopped.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="chk_LargeIsStopped.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chk_LargeIsStopped.Location" type="System.Drawing.Point, System.Drawing">
    <value>217, 79</value>
  </data>
  <data name="chk_LargeIsStopped.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 19</value>
  </data>
  <data name="chk_MediumIsStopped.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="chk_MediumIsStopped.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chk_SmallIsStopped.Location" type="System.Drawing.Point, System.Drawing">
    <value>209, 28</value>
  </data>
  <data name="labelControl37.Location" type="System.Drawing.Point, System.Drawing">
    <value>219, 11</value>
  </data>
  <data name="labelControl37.Size" type="System.Drawing.Size, System.Drawing">
    <value>31, 13</value>
  </data>
  <data name="labelControl37.Text" xml:space="preserve">
    <value>موقوف</value>
  </data>
  <data name="lbl_WeightUnit.Location" type="System.Drawing.Point, System.Drawing">
    <value>140, 26</value>
  </data>
  <data name="lbl_WeightUnit.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 13</value>
  </data>
  <data name="lbl_WeightUnit.Text" xml:space="preserve">
    <value>وحدة الوزن</value>
  </data>
  <data name="cmb_WeightUnit.Properties.Items3" xml:space="preserve">
    <value>وحدة التجزئة</value>
  </data>
  <data name="cmb_WeightUnit.Properties.Items4" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="cmb_WeightUnit.Properties.Items6" xml:space="preserve">
    <value>وحدة فرعية 1</value>
  </data>
  <data name="cmb_WeightUnit.Properties.Items7" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="cmb_WeightUnit.Properties.Items9" xml:space="preserve">
    <value>وحدة فرعية 2</value>
  </data>
  <data name="cmb_WeightUnit.Properties.Items10" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="txt_LargeUOMCode.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_LargeUOMCode.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="labelControl30.Location" type="System.Drawing.Point, System.Drawing">
    <value>158, 81</value>
  </data>
  <data name="labelControl30.Size" type="System.Drawing.Size, System.Drawing">
    <value>15, 13</value>
  </data>
  <data name="labelControl30.Text" xml:space="preserve">
    <value>كود</value>
  </data>
  <data name="txt_MediumUOMCode.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_MediumUOMCode.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="labelControl29.Location" type="System.Drawing.Point, System.Drawing">
    <value>158, 52</value>
  </data>
  <data name="labelControl29.Size" type="System.Drawing.Size, System.Drawing">
    <value>15, 13</value>
  </data>
  <data name="labelControl29.Text" xml:space="preserve">
    <value>كود</value>
  </data>
  <data name="rdoPrchsUOM2.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rdoPrchsUOM2.Location" type="System.Drawing.Point, System.Drawing">
    <value>255, 78</value>
  </data>
  <data name="rdoPrchsUOM2.Properties.Caption" xml:space="preserve">
    <value>شراء</value>
  </data>
  <data name="rdoPrchsUOM2.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 19</value>
  </data>
  <data name="rdoPrchsUOM2.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="rdoPrchsUOM1.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rdoPrchsUOM1.Location" type="System.Drawing.Point, System.Drawing">
    <value>255, 53</value>
  </data>
  <data name="rdoPrchsUOM1.Properties.Caption" xml:space="preserve">
    <value>شراء</value>
  </data>
  <data name="rdoPrchsUOM1.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 19</value>
  </data>
  <data name="rdoPrchsUOM1.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="rdoPrchsUOM0.Location" type="System.Drawing.Point, System.Drawing">
    <value>255, 28</value>
  </data>
  <data name="rdoPrchsUOM0.Properties.Caption" xml:space="preserve">
    <value>شراء</value>
  </data>
  <data name="rdoPrchsUOM0.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 19</value>
  </data>
  <data name="rdoPrchsUOM0.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="rdoSellUOM2.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rdoSellUOM2.Location" type="System.Drawing.Point, System.Drawing">
    <value>306, 78</value>
  </data>
  <data name="rdoSellUOM2.Properties.Caption" xml:space="preserve">
    <value>بيع</value>
  </data>
  <data name="rdoSellUOM2.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 19</value>
  </data>
  <data name="rdoSellUOM2.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="rdoSellUOM1.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rdoSellUOM1.Location" type="System.Drawing.Point, System.Drawing">
    <value>306, 53</value>
  </data>
  <data name="rdoSellUOM1.Properties.Caption" xml:space="preserve">
    <value>بيع</value>
  </data>
  <data name="rdoSellUOM1.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 19</value>
  </data>
  <data name="rdoSellUOM1.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="labelControl24.Location" type="System.Drawing.Point, System.Drawing">
    <value>280, 11</value>
  </data>
  <data name="labelControl24.Size" type="System.Drawing.Size, System.Drawing">
    <value>65, 13</value>
  </data>
  <data name="labelControl24.Text" xml:space="preserve">
    <value>وحدة افتراضية</value>
  </data>
  <data name="rdoSellUOM0.Location" type="System.Drawing.Point, System.Drawing">
    <value>306, 28</value>
  </data>
  <data name="rdoSellUOM0.Properties.Caption" xml:space="preserve">
    <value>بيع</value>
  </data>
  <data name="rdoSellUOM0.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 19</value>
  </data>
  <data name="rdoSellUOM0.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="textEdit5.Location" type="System.Drawing.Point, System.Drawing">
    <value>445, 28</value>
  </data>
  <data name="textEdit5.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="txtLargeUOMFactor.Location" type="System.Drawing.Point, System.Drawing">
    <value>445, 78</value>
  </data>
  <data name="txtLargeUOMFactor.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtLargeUOMFactor.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="lkpSmallUOM.Location" type="System.Drawing.Point, System.Drawing">
    <value>569, 28</value>
  </data>
  <data name="lkpSmallUOM.Properties.Columns8" xml:space="preserve">
    <value>الوحدة</value>
  </data>
  <data name="lkpSmallUOM.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpLargeUOM.Location" type="System.Drawing.Point, System.Drawing">
    <value>569, 78</value>
  </data>
  <data name="lkpLargeUOM.Properties.Columns8" xml:space="preserve">
    <value>الوحدة</value>
  </data>
  <data name="lkpLargeUOM.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="labelControl13.Location" type="System.Drawing.Point, System.Drawing">
    <value>359, 9</value>
  </data>
  <data name="labelControl13.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 13</value>
  </data>
  <data name="labelControl13.Text" xml:space="preserve">
    <value>سعر البيع</value>
  </data>
  <data name="labelControl19.Location" type="System.Drawing.Point, System.Drawing">
    <value>686, 78</value>
  </data>
  <data name="labelControl19.Size" type="System.Drawing.Size, System.Drawing">
    <value>59, 13</value>
  </data>
  <data name="labelControl19.Text" xml:space="preserve">
    <value>وحدة فرعية2</value>
  </data>
  <data name="labelControl23.Location" type="System.Drawing.Point, System.Drawing">
    <value>683, 31</value>
  </data>
  <data name="labelControl23.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 13</value>
  </data>
  <data name="labelControl23.Text" xml:space="preserve">
    <value>وحدة التجزئه</value>
  </data>
  <data name="btnAddUOM.Location" type="System.Drawing.Point, System.Drawing">
    <value>541, 28</value>
  </data>
  <data name="btnAddUOM.ToolTip" xml:space="preserve">
    <value>إضافة وحدة قياس الي وحدات القياس</value>
  </data>
  <data name="labelControl21.Location" type="System.Drawing.Point, System.Drawing">
    <value>461, 9</value>
  </data>
  <data name="labelControl21.Size" type="System.Drawing.Size, System.Drawing">
    <value>36, 13</value>
  </data>
  <data name="labelControl21.Text" xml:space="preserve">
    <value>المعامل</value>
  </data>
  <data name="lkpMediumUOM.Location" type="System.Drawing.Point, System.Drawing">
    <value>569, 53</value>
  </data>
  <data name="lkpMediumUOM.Properties.Columns1" xml:space="preserve">
    <value>الوحدة</value>
  </data>
  <data name="lkpMediumUOM.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="labelControl20.Location" type="System.Drawing.Point, System.Drawing">
    <value>686, 56</value>
  </data>
  <data name="labelControl20.Size" type="System.Drawing.Size, System.Drawing">
    <value>59, 13</value>
  </data>
  <data name="labelControl20.Text" xml:space="preserve">
    <value>وحدة فرعية1</value>
  </data>
  <data name="txtSmallUOMPrice.Location" type="System.Drawing.Point, System.Drawing">
    <value>359, 28</value>
  </data>
  <data name="txtSmallUOMPrice.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="txtMediumUOMPrice.Location" type="System.Drawing.Point, System.Drawing">
    <value>359, 53</value>
  </data>
  <data name="txtMediumUOMPrice.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="txtLargeUOMPrice.Location" type="System.Drawing.Point, System.Drawing">
    <value>359, 78</value>
  </data>
  <data name="txtLargeUOMPrice.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="grp_UOM.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 216</value>
  </data>
  <data name="grp_UOM.Size" type="System.Drawing.Size, System.Drawing">
    <value>757, 106</value>
  </data>
  <data name="grp_UOM.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="grp_UOM.Text" xml:space="preserve">
    <value>وحدات القياس</value>
  </data>
  <data name="txtPurchasePrice.Location" type="System.Drawing.Point, System.Drawing">
    <value>342, 128</value>
  </data>
  <data name="txtPurchasePrice.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="txtDesc.Location" type="System.Drawing.Point, System.Drawing">
    <value>479, 39</value>
  </data>
  <data name="txtDesc.Size" type="System.Drawing.Size, System.Drawing">
    <value>171, 46</value>
  </data>
  <data name="txtDesc.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="txtDescEn.Location" type="System.Drawing.Point, System.Drawing">
    <value>231, 39</value>
  </data>
  <data name="txtDescEn.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="tab_MainInfo.Size" type="System.Drawing.Size, System.Drawing">
    <value>752, 501</value>
  </data>
  <data name="tab_MainInfo.Text" xml:space="preserve">
    <value>بيانات رئيسيه</value>
  </data>
  <data name="tab_Control1.Size" type="System.Drawing.Size, System.Drawing">
    <value>758, 529</value>
  </data>
  <data name="tab_Control1.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="gridLocation.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 16</value>
  </data>
  <data name="col_Location.Caption" xml:space="preserve">
    <value>الموقع</value>
  </data>
  <data name="col_Location.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="col_Location.Width" type="System.Int32, mscorlib">
    <value>208</value>
  </data>
  <data name="col_Store.Caption" xml:space="preserve">
    <value>المخزن</value>
  </data>
  <data name="col_Store.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_Store.Width" type="System.Int32, mscorlib">
    <value>149</value>
  </data>
  <data name="gridLocation.Size" type="System.Drawing.Size, System.Drawing">
    <value>357, 192</value>
  </data>
  <data name="groupBox5.Location" type="System.Drawing.Point, System.Drawing">
    <value>11, 246</value>
  </data>
  <data name="groupBox5.Size" type="System.Drawing.Size, System.Drawing">
    <value>363, 211</value>
  </data>
  <data name="groupBox5.Text" xml:space="preserve">
    <value>المواقع</value>
  </data>
  <data name="grd_Vendors.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="grd_Vendors.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="grd_Vendors.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 16</value>
  </data>
  <data name="col_Vnd_PurchasePrice.Caption" xml:space="preserve">
    <value>سعر الشراء</value>
  </data>
  <data name="col_Vnd_PurchasePrice.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="col_Vnd_VendorId.Caption" xml:space="preserve">
    <value>المورد</value>
  </data>
  <data name="rep_Vendors.Columns8" xml:space="preserve">
    <value>الاسم</value>
  </data>
  <data name="col_Vnd_VendorId.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="grd_Vendors.Size" type="System.Drawing.Size, System.Drawing">
    <value>357, 212</value>
  </data>
  <data name="groupBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>11, 9</value>
  </data>
  <data name="groupBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>363, 231</value>
  </data>
  <data name="groupBox1.Text" xml:space="preserve">
    <value>أسعار الموردين الرئيسيين</value>
  </data>
  <data name="grdQty.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 16</value>
  </data>
  <data name="gridColumn2.Caption" xml:space="preserve">
    <value>الكمية</value>
  </data>
  <data name="gridColumn2.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn4.Caption" xml:space="preserve">
    <value>المخزن</value>
  </data>
  <data name="gridColumn4.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="grdQty.Size" type="System.Drawing.Size, System.Drawing">
    <value>357, 212</value>
  </data>
  <data name="groupBox3.Location" type="System.Drawing.Point, System.Drawing">
    <value>385, 9</value>
  </data>
  <data name="groupBox3.Size" type="System.Drawing.Size, System.Drawing">
    <value>363, 231</value>
  </data>
  <data name="groupBox3.Text" xml:space="preserve">
    <value>الكمية الحالية</value>
  </data>
  <data name="tab_Other.Size" type="System.Drawing.Size, System.Drawing">
    <value>752, 501</value>
  </data>
  <data name="tab_Other.Text" xml:space="preserve">
    <value>أخرى</value>
  </data>
  <data name="btnRemovePic.ToolTip" xml:space="preserve">
    <value>ازالة صورة</value>
  </data>
  <data name="btnAddPicture.ToolTip" xml:space="preserve">
    <value>تحميل صورة</value>
  </data>
  <data name="itemPhoto.Properties.NullText" xml:space="preserve">
    <value>لايوجد صورة</value>
  </data>
  <data name="tab_image.Size" type="System.Drawing.Size, System.Drawing">
    <value>752, 501</value>
  </data>
  <data name="tab_image.Text" xml:space="preserve">
    <value>صورة</value>
  </data>
  <data name="grd_SalesPerQty.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 46</value>
  </data>
  <data name="col_SellPrice.Caption" xml:space="preserve">
    <value>سعر البيع</value>
  </data>
  <data name="col_QtyTo.Caption" xml:space="preserve">
    <value>الي الكمية</value>
  </data>
  <data name="col_QtyFrom.Caption" xml:space="preserve">
    <value>من الكمية</value>
  </data>
  <data name="grd_SalesPerQty.Size" type="System.Drawing.Size, System.Drawing">
    <value>745, 370</value>
  </data>
  <data name="tab_PricesPerQty.Size" type="System.Drawing.Size, System.Drawing">
    <value>752, 501</value>
  </data>
  <data name="tab_PricesPerQty.Text" xml:space="preserve">
    <value>أسعار البيع حسب الكمية</value>
  </data>
  <data name="colPlLargeUOMPrice.Caption" xml:space="preserve">
    <value>س بيع كبرى</value>
  </data>
  <data name="colPlMediumUOMPrice.Caption" xml:space="preserve">
    <value>س بيع متوسطة</value>
  </data>
  <data name="colPlSmallUOMPrice.Caption" xml:space="preserve">
    <value>س بيع صغري</value>
  </data>
  <data name="colPLName.Caption" xml:space="preserve">
    <value>قائمة سعر البيع</value>
  </data>
  <data name="grdSlPLevel.Size" type="System.Drawing.Size, System.Drawing">
    <value>426, 211</value>
  </data>
  <data name="groupBox4.Location" type="System.Drawing.Point, System.Drawing">
    <value>309, 49</value>
  </data>
  <data name="groupBox4.Size" type="System.Drawing.Size, System.Drawing">
    <value>438, 236</value>
  </data>
  <data name="groupBox4.Text" xml:space="preserve">
    <value>أسعار البيع</value>
  </data>
  <data name="colPrsmallUOMPrice.Caption" xml:space="preserve">
    <value>سعر الشراء</value>
  </data>
  <data name="colPrPLName.Caption" xml:space="preserve">
    <value>قائمة سعر الشراء</value>
  </data>
  <data name="grdPrPLevel.Size" type="System.Drawing.Size, System.Drawing">
    <value>288, 211</value>
  </data>
  <data name="groupBox2.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 49</value>
  </data>
  <data name="groupBox2.Size" type="System.Drawing.Size, System.Drawing">
    <value>300, 236</value>
  </data>
  <data name="groupBox2.Text" xml:space="preserve">
    <value>أسعار الشراء</value>
  </data>
  <data name="tab_PriceLevels.Size" type="System.Drawing.Size, System.Drawing">
    <value>752, 501</value>
  </data>
  <data name="tab_PriceLevels.Text" xml:space="preserve">
    <value>أسعار الصنف</value>
  </data>
  <data name="btn_MatrixPrint.Location" type="System.Drawing.Point, System.Drawing">
    <value>188, 6</value>
  </data>
  <data name="btn_MatrixPrint.Text" xml:space="preserve">
    <value>طباعة</value>
  </data>
  <data name="btn_GenMtrx.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 6</value>
  </data>
  <data name="btn_GenMtrx.Size" type="System.Drawing.Size, System.Drawing">
    <value>167, 23</value>
  </data>
  <data name="btn_GenMtrx.Text" xml:space="preserve">
    <value>أنشاء أصناف المصفوفة</value>
  </data>
  <data name="grd_Mtrx.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 34</value>
  </data>
  <data name="colLength.Caption" xml:space="preserve">
    <value>الطول</value>
  </data>
  <data name="colWidth.Caption" xml:space="preserve">
    <value>العرض</value>
  </data>
  <data name="colHeight.Caption" xml:space="preserve">
    <value>الارتفاع</value>
  </data>
  <data name="col_m_Attribute3.Caption" xml:space="preserve">
    <value>تصنيف3</value>
  </data>
  <data name="col_m_Attribute3.VisibleIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="col_m_Attribute3.Width" type="System.Int32, mscorlib">
    <value>68</value>
  </data>
  <data name="col_m_Attribute2.Caption" xml:space="preserve">
    <value>تصنيف2</value>
  </data>
  <data name="col_m_Attribute2.VisibleIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="col_m_Attribute2.Width" type="System.Int32, mscorlib">
    <value>77</value>
  </data>
  <data name="col_m_Attribute1.Caption" xml:space="preserve">
    <value>تصنيف1</value>
  </data>
  <data name="col_m_Attribute1.VisibleIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="col_m_Attribute1.Width" type="System.Int32, mscorlib">
    <value>71</value>
  </data>
  <data name="col_m_MinQty.Caption" xml:space="preserve">
    <value>حد أدنى</value>
  </data>
  <data name="col_m_MinQty.Width" type="System.Int32, mscorlib">
    <value>49</value>
  </data>
  <data name="col_m_MaxQty.Caption" xml:space="preserve">
    <value>حد أقصى</value>
  </data>
  <data name="col_m_MaxQty.Width" type="System.Int32, mscorlib">
    <value>53</value>
  </data>
  <data name="col_m_ReorderLevel.Caption" xml:space="preserve">
    <value>حد طلب</value>
  </data>
  <data name="col_m_ReorderLevel.Width" type="System.Int32, mscorlib">
    <value>47</value>
  </data>
  <data name="col_m_LargeUOMPrice.Caption" xml:space="preserve">
    <value>س بيع كبرى</value>
  </data>
  <data name="col_m_LargeUOMPrice.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_m_LargeUOMPrice.VisibleIndex" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="col_m_MediumUOMPrice.Caption" xml:space="preserve">
    <value>س بيع متوسطة</value>
  </data>
  <data name="col_m_MediumUOMPrice.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_m_MediumUOMPrice.VisibleIndex" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="col_m_SmallUOMPrice.Caption" xml:space="preserve">
    <value>س بيع صغرى</value>
  </data>
  <data name="col_m_SmallUOMPrice.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="col_m_PurchasePrice.Caption" xml:space="preserve">
    <value>سعر شراء</value>
  </data>
  <data name="col_m_PurchasePrice.VisibleIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="col_m_PurchasePrice.Width" type="System.Int32, mscorlib">
    <value>58</value>
  </data>
  <data name="col_m_name.Caption" xml:space="preserve">
    <value>اسم</value>
  </data>
  <data name="col_m_name.VisibleIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="col_m_name.Width" type="System.Int32, mscorlib">
    <value>44</value>
  </data>
  <data name="col_m_ItemCode2.Caption" xml:space="preserve">
    <value>كود2</value>
  </data>
  <data name="col_m_ItemCode2.VisibleIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="col_m_ItemCode2.Width" type="System.Int32, mscorlib">
    <value>64</value>
  </data>
  <data name="col_m_Code1.Caption" xml:space="preserve">
    <value>كود1</value>
  </data>
  <data name="grd_Mtrx.Size" type="System.Drawing.Size, System.Drawing">
    <value>744, 352</value>
  </data>
  <data name="tab_Matrix.Size" type="System.Drawing.Size, System.Drawing">
    <value>752, 501</value>
  </data>
  <data name="tab_Matrix.Text" xml:space="preserve">
    <value>مصفوفة الأصناف</value>
  </data>
  <data name="chk_IsDeleted.Location" type="System.Drawing.Point, System.Drawing">
    <value>241, 40</value>
  </data>
  <data name="chk_IsDeleted.Properties.Caption" xml:space="preserve">
    <value>موقوف</value>
  </data>
  <data name="chk_IsDeleted.Size" type="System.Drawing.Size, System.Drawing">
    <value>85, 19</value>
  </data>
  <data name="btnPrev.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 39</value>
  </data>
  <data name="btnPrev.ToolTip" xml:space="preserve">
    <value>السابق</value>
  </data>
  <data name="btnNext.Location" type="System.Drawing.Point, System.Drawing">
    <value>39, 39</value>
  </data>
  <data name="btnNext.ToolTip" xml:space="preserve">
    <value>التالي</value>
  </data>
  <data name="txtItemCode2.Location" type="System.Drawing.Point, System.Drawing">
    <value>353, 39</value>
  </data>
  <data name="txtItemCode2.Size" type="System.Drawing.Size, System.Drawing">
    <value>142, 20</value>
  </data>
  <data name="labelControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>501, 43</value>
  </data>
  <data name="labelControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>21, 13</value>
  </data>
  <data name="labelControl2.Text" xml:space="preserve">
    <value>كود2</value>
  </data>
  <data name="labelControl4.Location" type="System.Drawing.Point, System.Drawing">
    <value>699, 67</value>
  </data>
  <data name="labelControl4.Size" type="System.Drawing.Size, System.Drawing">
    <value>54, 13</value>
  </data>
  <data name="labelControl4.Text" xml:space="preserve">
    <value>اسم الصنف</value>
  </data>
  <data name="txtItemNameAr.Location" type="System.Drawing.Point, System.Drawing">
    <value>246, 64</value>
  </data>
  <data name="txtItemNameAr.Size" type="System.Drawing.Size, System.Drawing">
    <value>446, 20</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAA
        AAAfbf8AH2z/AB9r/x8fa/+rH2v/9h9r/9AfbP9HH2//AB9u/wAfcP8AAAAAAB9p/wAfZv8AH2D/Ah9j
        /2cfY//iH2P/7R9j/4YfY/8KH2P/AB9j/wAAAAAADw4QAA4NDwAODQ4ADQwORwwMDdAMCw32CwsMqwsL
        DB8MCw0ADAsNAB9u/wAfbf8WH23/qx9t//8fbv//H27//x9u/9gfb/89H3D/AB9y/wAfcf8AH23/AB9n
        /wAfZ/9cH2X/6x9j//8fY///H2P/9x9j/38fY/8GH2P/AB5e8QAPDhAADw4QAA4NDz0NDQ/YDQ0O/w0M
        Dv8MDA3/DAsNqwwLDRYMCw0AH2//FR9v/6Ifb///H3D//x9x//8fcf//H3H//x9x/9Mfcv82IHD/AiBw
        /wQfcP8DH2z/VB9q/+gfZ///H2X//x9j//8fY///H2P/9x9j/3cfY/8FH2P+ABAPEQAQDxE1Dw4Q0w8O
        EP8ODg//Dg0P/w0NDv8MDA3/DAsNogwLDRUfcP+MH3H//R9y//8ec///HnP//x50//8edP//HnP//x5z
        /9cec/+pH3L/qR9x/6wfbv/mH23//x9q//8faP//H2X//x9j//8fY///H2P/8h9j/04bTb8AERASERAQ
        ErsQEBL/EA8R/w8PEf8PDhD/Dg0P/w0ND/8MDA79DAwNjB5z/9QedP//HnX//x52//8edv//Hnb//x52
        //8edv//Hnb//x51//8edP//HnP//x9x//8fb///H23//x9r//8faP//H2X//x9j//8fY///H2P/gxg4
        hAASERM1ERET7hERE/8REBL/EBAS/xAPEf8PDhD/Dg4Q/w0ND/8NDA7THnX/wx52//8ed///Hnj//x54
        //8eef//Hnn//x54//8eeP/9Hnf/8h52//Iedf/zHnP//h9x//8fb///H23//x9q//8fZ///H2T//x9j
        //8fY/90GkGeABISFCYTEhThEhIU/xIRE/8RERP/EBAS/xAPEf8PDhD/Dg4Q/w4ND78ed/9UHnj/5x55
        //8eev//Hnr//x57//8ee///Hnr/+x56/5AeeP88Hnf/PB52/0Eedf+wHnP//x9x//8fb///H2z//x9p
        //8fZv//H2T/zB9j/yQdV9wAExIVAxMTFYMTEhX8ExIU/xISFP8RERP/ERAS/xAPEf8PDhDkDg4QTx50
        /wAeev9dHnv/7R58//8efP//Hn3//x59//0efP+UHnv/DB55/wAed/8AHnb/AB52/x8edf+5HnP//x9x
        //8fbv//H2v//x9o/9YfZf85H2f/AAAAAAAUExYAFBMWDRQTFpQUExX9ExIV/xISFP8SERP/ERAS6hAP
        EVYTDhQAHnr/AB58/wEefP9nHn3/7x5+//8efv/9Hn7/nR59/xAefP8AHnv/AB52/wAedf8AHnX/AB51
        /yUedP2+H3P//x9x//8fbPvbH2r/Px9o/wAfZv8AH2P/ABQTFgAUExYAFBQWEBQUFp0UExb9ExIV/xIS
        FOwRERNfIxkMABAQEQAee/8AHn3/AB5+/wcefv+xHn///x5//+oef/8tHn//AB59/wAefP8AAAAAAB52
        /wAeV/8AGkKGABk+eUYaQoPzG0KG/xo0ZZIbRZkAH2v/AB9o/wAAAAAAFBQWABQUFgAVFBcAFRQXLRUU
        F+oUExb/ExMVqxMTFAUSERQAEBASAAAAAAAegP8AHn//Ax6A/6kegP//HoD/5x6A/ycegP8AHoD/AAAA
        AAAAAAAAAAAAABpCgQAXFRcAFhMSOhcVFvAXFRb/GBUWjhgYHgAcO3gAAAAAAAAAAAAAAAAAFxYZABYV
        GAAWFRgnFRUX5xUUF/8UExajExIVAhQTFgAAAAAAHoD/AB6A/wAegP8IHoD/sx6B//8egf/rHoH/Lx6B
        /wAegv8AHoL/AAAAAAAXFhkAFBQXABcXGgAXFxpIGBca9BgYG/8ZGBuTGRkcABoaHQAbGx4AAAAAABkZ
        HAAYGBsAFxcZABcWGS8WFhjrFRUX/xUUF6wUFBYGFBMWABQTFQAef/8AHoD/Ah6A/20egf/xHoH//x6B
        //4egv+kHoL/Ex6C/wAegv8AFhYYABsZIAAXFxoAFxcaKRgXGsMYGBv/GRkc/xoZHN4bGh1FHBoeABwb
        HwAdHCAAGhkdABkZHAAYGBsTGBcapBcWGf4WFRj/FRQX7xQUFmYTEBMBExMVAB59/wIegP9kHoD/8B6B
        //8egv//HoL//x6C//8egv+cHoL/EB6B/wAaRX4AFxYZABcWGSQXFxq/GBgb/xkZHP8aGR3/Gxoe/xwb
        HtkcGx89HBwfABoYIQAbGh0AGxodEBkZHJwYGBv/Fxca/xYWGf8VFRf/FBQW7RQTFVwBABAAHn//Vx5/
        /+kegP//HoH//x6C//8egv//HoL//x6C//4egf+JHoD/BBgxUQAWFhgSFxYZsRgXGv8ZGBv/Ghkc/xsa
        Hf8cGx7/HBwf/x0cIM8eHSAmHRwgAB0cHwQbGh6JGhkc/hkYG/8YFxr/FxYZ/xYVGP8VFBf/ExMV5hMS
        FFIefv/EHn///x6A//8egf//HoL//x6C//8egv//HoL//x6B/+Megf8nGUB0ABYWGEwXFhn4GBca/xkY
        G/8aGh3/Gxse/xwcH/8dHCD/Hh0h/x4dIXUdHCAAHBsfJxwbHuMaGh3/GRkc/xgXGv8XFhn/FhUY/xUU
        F/8UExX/ExIUwR5+/9Uef///HoD//x6B//8egf//HoL//x6C//8egf//HoH/7h6B/zQaRX4AFhYZWhcW
        GfwYGBv/GRkc/xsaHf8cGx7/HRwg/x4dIf8fHiL/Hx4igx4dIQAdHB80HBsf7hsaHf8aGRz/GBgb/xcX
        Gf8WFRj/FRQX/xQTFf8TEhXSHn7/jR5///0egP//HoD//x6B//8egf//HoH//x6B//8egf+4HoD/EBk3
        YAAWFhkqFxYZ2xgYG/8ZGRz/Gxod/xwbHv8dHCD/Hh0h/x8eIu8gHyNKHx4iABwcHxAcGx64Gxod/xoZ
        HP8YGBv/Fxca/xYVGP8VFBf/FBMV/BMSFYgefv8VHn7/oh5///8egP//HoD//x6B//8egf//HoD/0B6A
        /zIegf8AFxIRABgaHQAXFxpQGBca5hkYHP8aGh3/Gxse/x0cH/8dHSD1Hh0hch8eIwQfHiIAGxseABwb
        HjIaGh3QGRkc/xgXG/8XFhn/FhUY/xUUF/8UExafExIVEx5+/wAefv8XHn7/qx5///8ef///Hn///x6A
        /9UegP85HoD/AB5//wAYIzUAFxcaABgYGwAYFxpXGRgb6hoZHP8bGh7/HBse+B0cH3weHSEFHh0hAB8e
        IgAbGh0AGxodABoZHDkZGBvVGBca/xcWGf8WFRj/FRQXqBQUFhUUExYAHn7/AB5+/wAefv8dHn7/yR5+
        //8efv/yHn//Sh5//wAef/8AHoD/AAAAAAAYFxoAGBcaABUXGgAZGBtUGRkcwxoaHc0bGh5yHBseCB0c
        HwAeHSEAAAAAABsaHQAaGRwAGRgbABgYG0oYFxryFxYZ/xYVGMUVFBcbFRQXABQUFgAefv8AHnz/AB56
        /wIefP+oHn3//x59/+ceff8lHn3/AB5//wAAAAAAAAAAAAAAAAAYGBoAGBgbABgXGgAaGRwNGhkdERkZ
        HAAbGh4AGxoeAAAAAAAAAAAAAAAAABkZHAAXFxoAFxcaJRcWGecWFRj/FRUXohMSFQEVFBcAFRQXAB50
        /wAed/8AHnn/CR56/7Qee///Hnv/6x57/y4eev8AHnj/AB53/wAAAAAAFRQXABYVGAAWFRgAFxYZJBgX
        GnEYFxp6GRgbNBkOEQAYGBsAGBcaAAAAAAAWFhgAFhYYABYWGQAXFhkuFhYY6xUVF/8VFBeuFBQWBxMT
        FQASERQAHnL/AB11/wMedv9zHnf/8x54//8eef/+Hnn/oR55/xEed/8AHnb/ABMSFgAbFx0AFRQXABUV
        Fy4WFhjJFxYZ/xgXGv8YFxreGBcaShcXGgAYFxoAFxYZABYVGAAWFRgAFhYYERYWGKEWFRj+FRQX/xQT
        FvETEhVuEhETAxIREwAgb/8CH3L/aR50//Iedf//Hnb//x53//8ed//+Hnf/lx52/w0edf8AGEOMABQT
        FgAUExYnFRQXxBUVF/8WFhj/FxYZ/xcXGv8XFxrdFxcaQRYWGQAWFRgAFhUYABYVGA0WFRiXFhUY/hUU
        F/8UExb/ExMV/xISFPARERNlEQ4RAh9t/1sfb//sH3H//x9y//8ec///HnT//x50//8edP/9HnT/hx9y
        /wUVKUwAEhIUFBMSFbYUExb/FBQW/xERE/8QEBL/FhUY/xYWGf8XFhnXFhYYYBYVGE4WFRhOFhUYmBUV
        F/wVFBf/FBMW/xMTFf8SEhT/ERET/xEQEuoQDxFXH2v/xh9s//8fbv//H2///x9w//8fcf//H3H//x9x
        //8fcf/kH3D/KBc3cQASERNOEhIU+BMSFf8SERT/DAwN/woJC/8QDxH/FRUX/xYVGP8WFRj7FRUX+BUU
        F/gVFBf/FBQW/xQTFv8TEhX/EhIU/xIRE/8REBL/EA8R/w8PEMMfZ//UH2n//x9r//8fbP//H23//x9u
        //8fbv//H27//x9u/+4fbv80Fzl6ABEQE1oRERP8EhIU/xERE/8ODg//DAwN/xAPEf8UExb/FBQW/xQU
        Fv8UFBb/FBMW/xQTFv8TExX/ExIU/xISFP8SERP/ERAS/xAQEv8PDxH/Dg4Q0R9l/4kfZf/8H2f//x9o
        //8faf//H2r//x9q//8fav//H2v/sh9r/w4VKlcAEBASKRAQEtoRERP/EhET/xEQEv8QEBL/EhIU/xMT
        Ff8TExXyExMVsxMTFagTEhWoExIU1BISFP8SERP/ERET/xEQEv8QDxH/Dw8R/w4OEPwODQ+EH2P/Ex9j
        /6AfZP//H2T//x9l//8fZv//H2b//x9n/8gfaP8qH2j/AA0CAAAREBIAEA8RThAQEuUREBL/ERET/xIR
        E/8SERP/EhEU9hISFHYTExUIEhEUAxMRFQISERMxERETzREQEv8QEBL/EA8R/w8OEP8ODhD/Dg0Pnw0N
        DxIfY/8AH2P/Fh9j/6kfY///H2P//x9j//8fY//MH2T/MB9m/wAfVP8AERgnABAPEQAQERMAEA8RVRAP
        EegQEBL/EBAS/xEQEvcRERN9EhETBhISFAATEhUAExIVABEREwAQEBI2EA8R0g8PEf8PDhD/Dg4P/w4N
        D6gNDQ8VDg0PAB9j/wAfY/8AH2P/HR9j/6QfY//nH2P/wh9j/zofZP8AH2P/AB9m/wAAAAAAEA8RAA8P
        EQAKDhIBDw8QYA8PEdcQDxHiEA8RgRAPEQkREBIAEhETAAAAAAARERMAEA8RABAPEQAPDhBADg4Qxg4N
        D+cODQ+iDQ0OHA0NDwAODQ8AACAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAGAIBw
        DgEAIAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAQAAHAOAAAgBAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgBAA=
</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>بيانات الصنف</value>
  </data>
</root>