using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using DAL;
using System.Linq;
using System.Data;
using DevExpress.XtraEditors;

using System.Windows.Forms;

namespace Reports
{
    public partial class rpt_JO_JobOrder : DevExpress.XtraReports.UI.XtraReport
    {
        string CustomerAr, CustomerEn, AttnMr, salesEmpAr, salesEmpEn, salesEmp_JobAr, salesEmp_JobEn, number, date, DueDate, job,
            notes, priority, status, dept, userName;

        DataTable dt_inv_details;
        DataTable dt_inv_details2;

        public rpt_JO_JobOrder()
        {
            InitializeComponent();
        }
        public rpt_JO_JobOrder(string _CustomerAr,string _CustomerEn ,string _AttnMr, string _salesEmpAr,string _salesEmpEn,
            string _salesEmp_JobAr,string _salesEmp_JobEn,
            string _number, string _date, string _dueDate,
            string _job, string _notes, string _priority, string _status, string _dept,
            DataTable dt, string userName, DataTable dt2)
        {
            InitializeComponent();
            
            CustomerAr = _CustomerAr;
            CustomerEn = _CustomerEn;
            AttnMr = _AttnMr;
            salesEmpAr = _salesEmpAr;
            salesEmpEn = _salesEmpEn;
            salesEmp_JobAr = _salesEmp_JobAr;
            salesEmp_JobEn = _salesEmp_JobEn;
            
            number = _number;
            date = _date;
            DueDate = _dueDate;
            job = _job;
            notes = _notes;            
            priority = _priority;
            status = _status;
            dept = _dept;
            this.userName = userName;

            dt_inv_details = dt;
            this.DataSource = dt_inv_details;
            dt_inv_details2 = dt2;
            getReportHeader();
            //LoadData();            
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                lblCompName.Text = comp.CmpNameAr;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        public void LoadData()
        {
            lbl_CustomerEn.Text = CustomerEn;
            lbl_salesEmpEn.Text = salesEmpEn;
            lbl_salesEmp_JobAr.Text = salesEmp_JobAr;
            lbl_salesEmp_JobEn.Text = salesEmp_JobEn;

            lbl_cust.Text = CustomerAr;
            lbl_Attn.Text = AttnMr;
            lbl_salesEmp.Text = salesEmpAr;

            lbl_Number.Text = number;
            lbl_date.Text = date;
            lbl_dueDate.Text = DueDate;
            lbl_Job.Text = job;
            lbl_notes.Text = notes;            
            lbl_Priority.Text = priority;
            lbl_status.Text = status;
            lbl_dept.Text = dept;

            lbl_User.Text = userName;

            this.DataSource = dt_inv_details;

            cell_ArCaption.DataBindings.Add("Text", this.DataSource, "ArCaption");
            cell_ArText.DataBindings.Add("Text", this.DataSource, "ArText");
            cell_EnCaption.DataBindings.Add("Text", this.DataSource, "EnCaption");
            cell_EnText.DataBindings.Add("Text", this.DataSource, "EnText");



            DetailReport.DataSource = dt_inv_details2;
            cell_code.DataBindings.Add("Text", dt_inv_details2, "ItemCode1");
            //cell_code2.DataBindings.Add("Text", dt_inv_details, "ItemCode2");
            cell_Disc.DataBindings.Add("Text", dt_inv_details2, "DiscountValue");
            cell_Price.DataBindings.Add("Text", dt_inv_details2, "SellPrice");
            cell_Qty.DataBindings.Add("Text", dt_inv_details2, "Qty");
            cell_Total.DataBindings.Add("Text", dt_inv_details2, "TotalSellPrice");
            cell_ItemName.DataBindings.Add("Text", dt_inv_details2, "ItemName");


            cell_UOM.DataBindings.Add("Text", dt_inv_details2, "UOM");
            //cell_Height.DataBindings.Add("Text", dt_inv_details, "Height");
            //cell_Width.DataBindings.Add("Text", dt_inv_details, "Width");
            //cell_Length.DataBindings.Add("Text", dt_inv_details, "Length");
            //cell_TotalQty.DataBindings.Add("Text", dt_inv_details, "TotalQty");
            //cell_Factor.DataBindings.Add("Text", dt_inv_details, "Factor");
            //Cell_MUOM.DataBindings.Add("Text", dt_inv_details, "MUOM");
            //Cell_MUOM_Factor.DataBindings.Add("Text", dt_inv_details, "MUOM_Factor");

        }
    }
}
