namespace Reports
{
    partial class rpt_PR_Invoice
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(rpt_PR_Invoice));
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.xrTable2 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow2 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_Total = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Disc = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Price = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_UOM = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Qty = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_ItemName = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_code = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_MediumUOMQty = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_MediumUOM = new DevExpress.XtraReports.UI.XRTableCell();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.Currency = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel11 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Updated = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel4 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_notes = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel6 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLine1 = new DevExpress.XtraReports.UI.XRLine();
            this.lbl_Paymethod = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel1 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Serial = new DevExpress.XtraReports.UI.XRLabel();
            this.lblReportName = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel12 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Drawer = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel8 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel7 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Vendor = new DevExpress.XtraReports.UI.XRLabel();
            this.lblCompName = new DevExpress.XtraReports.UI.XRLabel();
            this.picLogo = new DevExpress.XtraReports.UI.XRPictureBox();
            this.lbl_User = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Number = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_store = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_date = new DevExpress.XtraReports.UI.XRLabel();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.xrTable5 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow5 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_SalesTax = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_code2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Expire = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_DiscountRatio2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_DiscountRatio3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_SalesTaxRatio = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_PiecesCount = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_ManufactureDate = new DevExpress.XtraReports.UI.XRTableCell();
            this.lbl_salesEmp_Job = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_ExpensesR = new DevExpress.XtraReports.UI.XRLabel();
            this.xrTable3 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow3 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_Weight_KG = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_ItemDescription = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_AudiencePrice = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Height = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Width = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Length = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_TotalQty = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Serial = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Batch = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_DiscountRatio = new DevExpress.XtraReports.UI.XRTableCell();
            this.lbl_TaxR = new DevExpress.XtraReports.UI.XRLabel();
            this.xrTable4 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow4 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_Pack = new DevExpress.XtraReports.UI.XRTableCell();
            this.Cell_MUOM = new DevExpress.XtraReports.UI.XRTableCell();
            this.Cell_MUOM_Factor = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Factor = new DevExpress.XtraReports.UI.XRTableCell();
            this.lbl_DeductTaxV = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_DiscountR = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_DriverName = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_VehicleNumber = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Destination = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_ScaleWeightSerial = new DevExpress.XtraReports.UI.XRLabel();
            this.PageHeader = new DevExpress.XtraReports.UI.PageHeaderBand();
            this.xrTable1 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow1 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell9 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell7 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell5 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell6 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell8 = new DevExpress.XtraReports.UI.XRTableCell();
            this.ReportFooter = new DevExpress.XtraReports.UI.ReportFooterBand();
            this.lbl_TotalPacks = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_BalanceAfter = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_BalanceBefore = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_totalPieces = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_VendorAddress = new DevExpress.XtraReports.UI.XRLabel();
            this.xrSubreport2 = new DevExpress.XtraReports.UI.XRSubreport();
            this.lbl_TotalQty = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_VendorMobile = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_CusTax = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel10 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel3 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel2 = new DevExpress.XtraReports.UI.XRLabel();
            this.lblSubTotal = new DevExpress.XtraReports.UI.XRLabel();
            this.xrPageInfo1 = new DevExpress.XtraReports.UI.XRPageInfo();
            this.xrLabel5 = new DevExpress.XtraReports.UI.XRLabel();
            this.lblTotalWords = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Remains = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Total = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel16 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Tax = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Net = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Expenses = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel20 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel19 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_DiscountV = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel15 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel17 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Paied = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel24 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel23 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_AddTaxV = new DevExpress.XtraReports.UI.XRLabel();
            this.xrCrossBandLine6 = new DevExpress.XtraReports.UI.XRCrossBandLine();
            this.xrCrossBandLine3 = new DevExpress.XtraReports.UI.XRCrossBandLine();
            this.xrCrossBandBox2 = new DevExpress.XtraReports.UI.XRCrossBandBox();
            this.xrCrossBandLine7 = new DevExpress.XtraReports.UI.XRCrossBandLine();
            this.xrCrossBandLine8 = new DevExpress.XtraReports.UI.XRCrossBandLine();
            this.xrCrossBandLine10 = new DevExpress.XtraReports.UI.XRCrossBandLine();
            this.xrCrossBandLine1 = new DevExpress.XtraReports.UI.XRCrossBandLine();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable2});
            resources.ApplyResources(this.Detail, "Detail");
            this.Detail.Name = "Detail";
            this.Detail.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            // 
            // xrTable2
            // 
            this.xrTable2.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.xrTable2, "xrTable2");
            this.xrTable2.Name = "xrTable2";
            this.xrTable2.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow2});
            this.xrTable2.StylePriority.UseBorders = false;
            this.xrTable2.StylePriority.UseFont = false;
            this.xrTable2.StylePriority.UseTextAlignment = false;
            // 
            // xrTableRow2
            // 
            resources.ApplyResources(this.xrTableRow2, "xrTableRow2");
            this.xrTableRow2.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_Total,
            this.cell_Disc,
            this.cell_Price,
            this.cell_UOM,
            this.cell_Qty,
            this.cell_ItemName,
            this.cell_code});
            this.xrTableRow2.Name = "xrTableRow2";
            this.xrTableRow2.StylePriority.UseBackColor = false;
            this.xrTableRow2.StylePriority.UseFont = false;
            // 
            // cell_Total
            // 
            this.cell_Total.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            resources.ApplyResources(this.cell_Total, "cell_Total");
            this.cell_Total.Name = "cell_Total";
            this.cell_Total.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_Total.StylePriority.UseBorders = false;
            this.cell_Total.StylePriority.UseFont = false;
            this.cell_Total.StylePriority.UsePadding = false;
            this.cell_Total.StylePriority.UseTextAlignment = false;
            // 
            // cell_Disc
            // 
            this.cell_Disc.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            resources.ApplyResources(this.cell_Disc, "cell_Disc");
            this.cell_Disc.Name = "cell_Disc";
            this.cell_Disc.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_Disc.StylePriority.UseBorders = false;
            this.cell_Disc.StylePriority.UseFont = false;
            this.cell_Disc.StylePriority.UsePadding = false;
            this.cell_Disc.StylePriority.UseTextAlignment = false;
            // 
            // cell_Price
            // 
            this.cell_Price.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            resources.ApplyResources(this.cell_Price, "cell_Price");
            this.cell_Price.Name = "cell_Price";
            this.cell_Price.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_Price.StylePriority.UseBorders = false;
            this.cell_Price.StylePriority.UseFont = false;
            this.cell_Price.StylePriority.UsePadding = false;
            this.cell_Price.StylePriority.UseTextAlignment = false;
            // 
            // cell_UOM
            // 
            this.cell_UOM.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            resources.ApplyResources(this.cell_UOM, "cell_UOM");
            this.cell_UOM.Name = "cell_UOM";
            this.cell_UOM.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_UOM.StylePriority.UseBorders = false;
            this.cell_UOM.StylePriority.UseFont = false;
            this.cell_UOM.StylePriority.UsePadding = false;
            this.cell_UOM.StylePriority.UseTextAlignment = false;
            // 
            // cell_Qty
            // 
            this.cell_Qty.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            resources.ApplyResources(this.cell_Qty, "cell_Qty");
            this.cell_Qty.Name = "cell_Qty";
            this.cell_Qty.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_Qty.StylePriority.UseBorders = false;
            this.cell_Qty.StylePriority.UseFont = false;
            this.cell_Qty.StylePriority.UsePadding = false;
            this.cell_Qty.StylePriority.UseTextAlignment = false;
            // 
            // cell_ItemName
            // 
            this.cell_ItemName.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            resources.ApplyResources(this.cell_ItemName, "cell_ItemName");
            this.cell_ItemName.Name = "cell_ItemName";
            this.cell_ItemName.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_ItemName.StylePriority.UseBorders = false;
            this.cell_ItemName.StylePriority.UseFont = false;
            this.cell_ItemName.StylePriority.UsePadding = false;
            this.cell_ItemName.StylePriority.UseTextAlignment = false;
            // 
            // cell_code
            // 
            this.cell_code.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            resources.ApplyResources(this.cell_code, "cell_code");
            this.cell_code.Name = "cell_code";
            this.cell_code.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_code.StylePriority.UseBorders = false;
            this.cell_code.StylePriority.UseFont = false;
            this.cell_code.StylePriority.UsePadding = false;
            this.cell_code.StylePriority.UseTextAlignment = false;
            // 
            // cell_MediumUOMQty
            // 
            this.cell_MediumUOMQty.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            resources.ApplyResources(this.cell_MediumUOMQty, "cell_MediumUOMQty");
            this.cell_MediumUOMQty.Name = "cell_MediumUOMQty";
            this.cell_MediumUOMQty.StylePriority.UseBorders = false;
            this.cell_MediumUOMQty.StylePriority.UseFont = false;
            this.cell_MediumUOMQty.StylePriority.UsePadding = false;
            this.cell_MediumUOMQty.StylePriority.UseTextAlignment = false;
            // 
            // cell_MediumUOM
            // 
            this.cell_MediumUOM.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            resources.ApplyResources(this.cell_MediumUOM, "cell_MediumUOM");
            this.cell_MediumUOM.Name = "cell_MediumUOM";
            this.cell_MediumUOM.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_MediumUOM.StylePriority.UseBorders = false;
            this.cell_MediumUOM.StylePriority.UseFont = false;
            this.cell_MediumUOM.StylePriority.UsePadding = false;
            this.cell_MediumUOM.StylePriority.UseTextAlignment = false;
            // 
            // TopMargin
            // 
            this.TopMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.Currency,
            this.xrLabel11,
            this.lbl_Updated,
            this.xrLabel4,
            this.lbl_notes,
            this.xrLabel6,
            this.xrLine1,
            this.lbl_Paymethod,
            this.xrLabel1,
            this.lbl_Serial,
            this.lblReportName,
            this.xrLabel12,
            this.lbl_Drawer,
            this.xrLabel8,
            this.xrLabel7,
            this.lbl_Vendor,
            this.lblCompName,
            this.picLogo,
            this.lbl_User,
            this.lbl_Number,
            this.lbl_store,
            this.lbl_date});
            resources.ApplyResources(this.TopMargin, "TopMargin");
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            // 
            // Currency
            // 
            resources.ApplyResources(this.Currency, "Currency");
            this.Currency.Name = "Currency";
            this.Currency.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.Currency.StylePriority.UseFont = false;
            this.Currency.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel11
            // 
            resources.ApplyResources(this.xrLabel11, "xrLabel11");
            this.xrLabel11.Name = "xrLabel11";
            this.xrLabel11.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel11.StylePriority.UseFont = false;
            this.xrLabel11.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Updated
            // 
            this.lbl_Updated.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lbl_Updated, "lbl_Updated");
            this.lbl_Updated.Name = "lbl_Updated";
            this.lbl_Updated.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Updated.StylePriority.UseBorders = false;
            this.lbl_Updated.StylePriority.UseFont = false;
            this.lbl_Updated.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel4
            // 
            resources.ApplyResources(this.xrLabel4, "xrLabel4");
            this.xrLabel4.Name = "xrLabel4";
            this.xrLabel4.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel4.StylePriority.UseFont = false;
            this.xrLabel4.StylePriority.UseTextAlignment = false;
            // 
            // lbl_notes
            // 
            resources.ApplyResources(this.lbl_notes, "lbl_notes");
            this.lbl_notes.Multiline = true;
            this.lbl_notes.Name = "lbl_notes";
            this.lbl_notes.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_notes.StylePriority.UseFont = false;
            this.lbl_notes.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel6
            // 
            this.xrLabel6.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.xrLabel6, "xrLabel6");
            this.xrLabel6.Name = "xrLabel6";
            this.xrLabel6.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel6.StylePriority.UseBorders = false;
            this.xrLabel6.StylePriority.UseFont = false;
            this.xrLabel6.StylePriority.UseTextAlignment = false;
            // 
            // xrLine1
            // 
            resources.ApplyResources(this.xrLine1, "xrLine1");
            this.xrLine1.BorderWidth = 0F;
            this.xrLine1.LineWidth = 0;
            this.xrLine1.Name = "xrLine1";
            this.xrLine1.StylePriority.UseBackColor = false;
            this.xrLine1.StylePriority.UseBorderColor = false;
            this.xrLine1.StylePriority.UseBorderWidth = false;
            this.xrLine1.StylePriority.UseForeColor = false;
            // 
            // lbl_Paymethod
            // 
            resources.ApplyResources(this.lbl_Paymethod, "lbl_Paymethod");
            this.lbl_Paymethod.Name = "lbl_Paymethod";
            this.lbl_Paymethod.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Paymethod.StylePriority.UseFont = false;
            this.lbl_Paymethod.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel1
            // 
            resources.ApplyResources(this.xrLabel1, "xrLabel1");
            this.xrLabel1.Name = "xrLabel1";
            this.xrLabel1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel1.StylePriority.UseFont = false;
            this.xrLabel1.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Serial
            // 
            this.lbl_Serial.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.lbl_Serial, "lbl_Serial");
            this.lbl_Serial.Name = "lbl_Serial";
            this.lbl_Serial.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Serial.StylePriority.UseBorders = false;
            this.lbl_Serial.StylePriority.UseFont = false;
            this.lbl_Serial.StylePriority.UseTextAlignment = false;
            // 
            // lblReportName
            // 
            resources.ApplyResources(this.lblReportName, "lblReportName");
            this.lblReportName.Name = "lblReportName";
            this.lblReportName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblReportName.StylePriority.UseFont = false;
            this.lblReportName.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel12
            // 
            resources.ApplyResources(this.xrLabel12, "xrLabel12");
            this.xrLabel12.Name = "xrLabel12";
            this.xrLabel12.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel12.StylePriority.UseFont = false;
            this.xrLabel12.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Drawer
            // 
            resources.ApplyResources(this.lbl_Drawer, "lbl_Drawer");
            this.lbl_Drawer.Name = "lbl_Drawer";
            this.lbl_Drawer.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Drawer.StylePriority.UseFont = false;
            this.lbl_Drawer.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel8
            // 
            resources.ApplyResources(this.xrLabel8, "xrLabel8");
            this.xrLabel8.Name = "xrLabel8";
            this.xrLabel8.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel8.StylePriority.UseFont = false;
            this.xrLabel8.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel7
            // 
            resources.ApplyResources(this.xrLabel7, "xrLabel7");
            this.xrLabel7.Name = "xrLabel7";
            this.xrLabel7.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel7.StylePriority.UseFont = false;
            this.xrLabel7.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Vendor
            // 
            resources.ApplyResources(this.lbl_Vendor, "lbl_Vendor");
            this.lbl_Vendor.Name = "lbl_Vendor";
            this.lbl_Vendor.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Vendor.StylePriority.UseFont = false;
            this.lbl_Vendor.StylePriority.UseTextAlignment = false;
            // 
            // lblCompName
            // 
            resources.ApplyResources(this.lblCompName, "lblCompName");
            this.lblCompName.Name = "lblCompName";
            this.lblCompName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblCompName.StylePriority.UseFont = false;
            this.lblCompName.StylePriority.UseTextAlignment = false;
            // 
            // picLogo
            // 
            resources.ApplyResources(this.picLogo, "picLogo");
            this.picLogo.Name = "picLogo";
            this.picLogo.Sizing = DevExpress.XtraPrinting.ImageSizeMode.StretchImage;
            // 
            // lbl_User
            // 
            this.lbl_User.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lbl_User, "lbl_User");
            this.lbl_User.Name = "lbl_User";
            this.lbl_User.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_User.StylePriority.UseBorders = false;
            this.lbl_User.StylePriority.UseFont = false;
            this.lbl_User.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Number
            // 
            resources.ApplyResources(this.lbl_Number, "lbl_Number");
            this.lbl_Number.Name = "lbl_Number";
            this.lbl_Number.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Number.StylePriority.UseFont = false;
            this.lbl_Number.StylePriority.UseTextAlignment = false;
            // 
            // lbl_store
            // 
            resources.ApplyResources(this.lbl_store, "lbl_store");
            this.lbl_store.Name = "lbl_store";
            this.lbl_store.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_store.StylePriority.UseFont = false;
            this.lbl_store.StylePriority.UseTextAlignment = false;
            // 
            // lbl_date
            // 
            resources.ApplyResources(this.lbl_date, "lbl_date");
            this.lbl_date.Name = "lbl_date";
            this.lbl_date.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_date.StylePriority.UseFont = false;
            this.lbl_date.StylePriority.UseTextAlignment = false;
            // 
            // BottomMargin
            // 
            this.BottomMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable5});
            resources.ApplyResources(this.BottomMargin, "BottomMargin");
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            // 
            // xrTable5
            // 
            resources.ApplyResources(this.xrTable5, "xrTable5");
            this.xrTable5.Name = "xrTable5";
            this.xrTable5.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow5});
            this.xrTable5.StylePriority.UseTextAlignment = false;
            // 
            // xrTableRow5
            // 
            this.xrTableRow5.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_SalesTax,
            this.cell_code2,
            this.cell_Expire,
            this.cell_DiscountRatio2,
            this.cell_DiscountRatio3,
            this.cell_SalesTaxRatio,
            this.cell_PiecesCount,
            this.cell_ManufactureDate});
            resources.ApplyResources(this.xrTableRow5, "xrTableRow5");
            this.xrTableRow5.Name = "xrTableRow5";
            // 
            // cell_SalesTax
            // 
            resources.ApplyResources(this.cell_SalesTax, "cell_SalesTax");
            this.cell_SalesTax.Name = "cell_SalesTax";
            // 
            // cell_code2
            // 
            resources.ApplyResources(this.cell_code2, "cell_code2");
            this.cell_code2.Name = "cell_code2";
            // 
            // cell_Expire
            // 
            resources.ApplyResources(this.cell_Expire, "cell_Expire");
            this.cell_Expire.Name = "cell_Expire";
            // 
            // cell_DiscountRatio2
            // 
            resources.ApplyResources(this.cell_DiscountRatio2, "cell_DiscountRatio2");
            this.cell_DiscountRatio2.Name = "cell_DiscountRatio2";
            // 
            // cell_DiscountRatio3
            // 
            resources.ApplyResources(this.cell_DiscountRatio3, "cell_DiscountRatio3");
            this.cell_DiscountRatio3.Name = "cell_DiscountRatio3";
            // 
            // cell_SalesTaxRatio
            // 
            resources.ApplyResources(this.cell_SalesTaxRatio, "cell_SalesTaxRatio");
            this.cell_SalesTaxRatio.Name = "cell_SalesTaxRatio";
            // 
            // cell_PiecesCount
            // 
            resources.ApplyResources(this.cell_PiecesCount, "cell_PiecesCount");
            this.cell_PiecesCount.Name = "cell_PiecesCount";
            // 
            // cell_ManufactureDate
            // 
            resources.ApplyResources(this.cell_ManufactureDate, "cell_ManufactureDate");
            this.cell_ManufactureDate.Name = "cell_ManufactureDate";
            // 
            // lbl_salesEmp_Job
            // 
            resources.ApplyResources(this.lbl_salesEmp_Job, "lbl_salesEmp_Job");
            this.lbl_salesEmp_Job.Name = "lbl_salesEmp_Job";
            this.lbl_salesEmp_Job.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            // 
            // lbl_ExpensesR
            // 
            resources.ApplyResources(this.lbl_ExpensesR, "lbl_ExpensesR");
            this.lbl_ExpensesR.Name = "lbl_ExpensesR";
            this.lbl_ExpensesR.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            // 
            // xrTable3
            // 
            resources.ApplyResources(this.xrTable3, "xrTable3");
            this.xrTable3.Name = "xrTable3";
            this.xrTable3.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow3});
            this.xrTable3.StylePriority.UseTextAlignment = false;
            // 
            // xrTableRow3
            // 
            this.xrTableRow3.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_Weight_KG,
            this.cell_ItemDescription,
            this.cell_AudiencePrice,
            this.cell_Height,
            this.cell_Width,
            this.cell_Length,
            this.cell_TotalQty,
            this.cell_Serial,
            this.cell_Batch,
            this.cell_DiscountRatio,
            this.cell_MediumUOMQty,
            this.cell_MediumUOM});
            resources.ApplyResources(this.xrTableRow3, "xrTableRow3");
            this.xrTableRow3.Name = "xrTableRow3";
            // 
            // cell_Weight_KG
            // 
            resources.ApplyResources(this.cell_Weight_KG, "cell_Weight_KG");
            this.cell_Weight_KG.Name = "cell_Weight_KG";
            // 
            // cell_ItemDescription
            // 
            resources.ApplyResources(this.cell_ItemDescription, "cell_ItemDescription");
            this.cell_ItemDescription.Name = "cell_ItemDescription";
            // 
            // cell_AudiencePrice
            // 
            resources.ApplyResources(this.cell_AudiencePrice, "cell_AudiencePrice");
            this.cell_AudiencePrice.Name = "cell_AudiencePrice";
            // 
            // cell_Height
            // 
            resources.ApplyResources(this.cell_Height, "cell_Height");
            this.cell_Height.Name = "cell_Height";
            // 
            // cell_Width
            // 
            resources.ApplyResources(this.cell_Width, "cell_Width");
            this.cell_Width.Name = "cell_Width";
            // 
            // cell_Length
            // 
            resources.ApplyResources(this.cell_Length, "cell_Length");
            this.cell_Length.Name = "cell_Length";
            // 
            // cell_TotalQty
            // 
            resources.ApplyResources(this.cell_TotalQty, "cell_TotalQty");
            this.cell_TotalQty.Name = "cell_TotalQty";
            // 
            // cell_Serial
            // 
            resources.ApplyResources(this.cell_Serial, "cell_Serial");
            this.cell_Serial.Name = "cell_Serial";
            // 
            // cell_Batch
            // 
            resources.ApplyResources(this.cell_Batch, "cell_Batch");
            this.cell_Batch.Name = "cell_Batch";
            // 
            // cell_DiscountRatio
            // 
            resources.ApplyResources(this.cell_DiscountRatio, "cell_DiscountRatio");
            this.cell_DiscountRatio.Name = "cell_DiscountRatio";
            // 
            // lbl_TaxR
            // 
            resources.ApplyResources(this.lbl_TaxR, "lbl_TaxR");
            this.lbl_TaxR.Name = "lbl_TaxR";
            this.lbl_TaxR.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            // 
            // xrTable4
            // 
            resources.ApplyResources(this.xrTable4, "xrTable4");
            this.xrTable4.Name = "xrTable4";
            this.xrTable4.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow4});
            // 
            // xrTableRow4
            // 
            this.xrTableRow4.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_Pack,
            this.Cell_MUOM,
            this.Cell_MUOM_Factor,
            this.cell_Factor});
            resources.ApplyResources(this.xrTableRow4, "xrTableRow4");
            this.xrTableRow4.Name = "xrTableRow4";
            // 
            // cell_Pack
            // 
            resources.ApplyResources(this.cell_Pack, "cell_Pack");
            this.cell_Pack.Name = "cell_Pack";
            // 
            // Cell_MUOM
            // 
            resources.ApplyResources(this.Cell_MUOM, "Cell_MUOM");
            this.Cell_MUOM.Name = "Cell_MUOM";
            // 
            // Cell_MUOM_Factor
            // 
            resources.ApplyResources(this.Cell_MUOM_Factor, "Cell_MUOM_Factor");
            this.Cell_MUOM_Factor.Name = "Cell_MUOM_Factor";
            // 
            // cell_Factor
            // 
            resources.ApplyResources(this.cell_Factor, "cell_Factor");
            this.cell_Factor.Name = "cell_Factor";
            // 
            // lbl_DeductTaxV
            // 
            this.lbl_DeductTaxV.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.lbl_DeductTaxV, "lbl_DeductTaxV");
            this.lbl_DeductTaxV.Name = "lbl_DeductTaxV";
            this.lbl_DeductTaxV.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_DeductTaxV.StylePriority.UseBorders = false;
            this.lbl_DeductTaxV.StylePriority.UseFont = false;
            this.lbl_DeductTaxV.StylePriority.UseTextAlignment = false;
            // 
            // lbl_DiscountR
            // 
            this.lbl_DiscountR.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_DiscountR.CanGrow = false;
            resources.ApplyResources(this.lbl_DiscountR, "lbl_DiscountR");
            this.lbl_DiscountR.Name = "lbl_DiscountR";
            this.lbl_DiscountR.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_DiscountR.StylePriority.UseBorders = false;
            this.lbl_DiscountR.StylePriority.UseFont = false;
            this.lbl_DiscountR.StylePriority.UseTextAlignment = false;
            // 
            // lbl_DriverName
            // 
            this.lbl_DriverName.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lbl_DriverName, "lbl_DriverName");
            this.lbl_DriverName.Name = "lbl_DriverName";
            this.lbl_DriverName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_DriverName.StylePriority.UseBorders = false;
            this.lbl_DriverName.StylePriority.UseFont = false;
            this.lbl_DriverName.StylePriority.UseTextAlignment = false;
            // 
            // lbl_VehicleNumber
            // 
            this.lbl_VehicleNumber.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lbl_VehicleNumber, "lbl_VehicleNumber");
            this.lbl_VehicleNumber.Name = "lbl_VehicleNumber";
            this.lbl_VehicleNumber.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_VehicleNumber.StylePriority.UseBorders = false;
            this.lbl_VehicleNumber.StylePriority.UseFont = false;
            this.lbl_VehicleNumber.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Destination
            // 
            this.lbl_Destination.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lbl_Destination, "lbl_Destination");
            this.lbl_Destination.Name = "lbl_Destination";
            this.lbl_Destination.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Destination.StylePriority.UseBorders = false;
            this.lbl_Destination.StylePriority.UseFont = false;
            this.lbl_Destination.StylePriority.UseTextAlignment = false;
            // 
            // lbl_ScaleWeightSerial
            // 
            this.lbl_ScaleWeightSerial.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lbl_ScaleWeightSerial, "lbl_ScaleWeightSerial");
            this.lbl_ScaleWeightSerial.Name = "lbl_ScaleWeightSerial";
            this.lbl_ScaleWeightSerial.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_ScaleWeightSerial.StylePriority.UseBorders = false;
            this.lbl_ScaleWeightSerial.StylePriority.UseFont = false;
            this.lbl_ScaleWeightSerial.StylePriority.UseTextAlignment = false;
            // 
            // PageHeader
            // 
            this.PageHeader.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable1});
            resources.ApplyResources(this.PageHeader, "PageHeader");
            this.PageHeader.Name = "PageHeader";
            // 
            // xrTable1
            // 
            this.xrTable1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.xrTable1, "xrTable1");
            this.xrTable1.Name = "xrTable1";
            this.xrTable1.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow1});
            this.xrTable1.StylePriority.UseBorders = false;
            this.xrTable1.StylePriority.UseFont = false;
            this.xrTable1.StylePriority.UseTextAlignment = false;
            // 
            // xrTableRow1
            // 
            resources.ApplyResources(this.xrTableRow1, "xrTableRow1");
            this.xrTableRow1.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell1,
            this.xrTableCell9,
            this.xrTableCell7,
            this.xrTableCell5,
            this.xrTableCell6,
            this.xrTableCell3,
            this.xrTableCell8});
            this.xrTableRow1.Name = "xrTableRow1";
            this.xrTableRow1.StylePriority.UseBackColor = false;
            this.xrTableRow1.StylePriority.UseFont = false;
            // 
            // xrTableCell1
            // 
            resources.ApplyResources(this.xrTableCell1, "xrTableCell1");
            this.xrTableCell1.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell1.Name = "xrTableCell1";
            this.xrTableCell1.StylePriority.UseBackColor = false;
            this.xrTableCell1.StylePriority.UseBorders = false;
            // 
            // xrTableCell9
            // 
            resources.ApplyResources(this.xrTableCell9, "xrTableCell9");
            this.xrTableCell9.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell9.Name = "xrTableCell9";
            this.xrTableCell9.StylePriority.UseBackColor = false;
            this.xrTableCell9.StylePriority.UseBorders = false;
            // 
            // xrTableCell7
            // 
            resources.ApplyResources(this.xrTableCell7, "xrTableCell7");
            this.xrTableCell7.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell7.Name = "xrTableCell7";
            this.xrTableCell7.StylePriority.UseBackColor = false;
            this.xrTableCell7.StylePriority.UseBorders = false;
            // 
            // xrTableCell5
            // 
            resources.ApplyResources(this.xrTableCell5, "xrTableCell5");
            this.xrTableCell5.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell5.Name = "xrTableCell5";
            this.xrTableCell5.StylePriority.UseBackColor = false;
            this.xrTableCell5.StylePriority.UseBorders = false;
            // 
            // xrTableCell6
            // 
            resources.ApplyResources(this.xrTableCell6, "xrTableCell6");
            this.xrTableCell6.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell6.Name = "xrTableCell6";
            this.xrTableCell6.StylePriority.UseBackColor = false;
            this.xrTableCell6.StylePriority.UseBorders = false;
            // 
            // xrTableCell3
            // 
            resources.ApplyResources(this.xrTableCell3, "xrTableCell3");
            this.xrTableCell3.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell3.Name = "xrTableCell3";
            this.xrTableCell3.StylePriority.UseBackColor = false;
            this.xrTableCell3.StylePriority.UseBorders = false;
            // 
            // xrTableCell8
            // 
            resources.ApplyResources(this.xrTableCell8, "xrTableCell8");
            this.xrTableCell8.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell8.Name = "xrTableCell8";
            this.xrTableCell8.StylePriority.UseBackColor = false;
            this.xrTableCell8.StylePriority.UseBorders = false;
            // 
            // ReportFooter
            // 
            this.ReportFooter.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.lbl_TotalPacks,
            this.lbl_BalanceAfter,
            this.lbl_BalanceBefore,
            this.lbl_totalPieces,
            this.lbl_VendorAddress,
            this.xrSubreport2,
            this.lbl_TotalQty,
            this.lbl_VendorMobile,
            this.lbl_CusTax,
            this.xrLabel10,
            this.xrLabel3,
            this.xrLabel2,
            this.lblSubTotal,
            this.xrPageInfo1,
            this.xrLabel5,
            this.lblTotalWords,
            this.lbl_Remains,
            this.lbl_Total,
            this.xrLabel16,
            this.lbl_Tax,
            this.lbl_Net,
            this.lbl_Expenses,
            this.xrLabel20,
            this.xrLabel19,
            this.lbl_DiscountV,
            this.xrLabel15,
            this.xrLabel17,
            this.lbl_Paied,
            this.xrLabel24,
            this.xrLabel23,
            this.lbl_AddTaxV,
            this.lbl_Destination,
            this.lbl_VehicleNumber,
            this.lbl_DriverName,
            this.lbl_DiscountR,
            this.lbl_DeductTaxV,
            this.xrTable4,
            this.lbl_TaxR,
            this.xrTable3,
            this.lbl_ExpensesR,
            this.lbl_salesEmp_Job,
            this.lbl_ScaleWeightSerial});
            resources.ApplyResources(this.ReportFooter, "ReportFooter");
            this.ReportFooter.Name = "ReportFooter";
            this.ReportFooter.PrintAtBottom = true;
            // 
            // lbl_TotalPacks
            // 
            resources.ApplyResources(this.lbl_TotalPacks, "lbl_TotalPacks");
            this.lbl_TotalPacks.Name = "lbl_TotalPacks";
            this.lbl_TotalPacks.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            // 
            // lbl_BalanceAfter
            // 
            this.lbl_BalanceAfter.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_BalanceAfter.CanGrow = false;
            resources.ApplyResources(this.lbl_BalanceAfter, "lbl_BalanceAfter");
            this.lbl_BalanceAfter.Name = "lbl_BalanceAfter";
            this.lbl_BalanceAfter.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_BalanceAfter.StylePriority.UseBorders = false;
            this.lbl_BalanceAfter.StylePriority.UseFont = false;
            this.lbl_BalanceAfter.StylePriority.UseTextAlignment = false;
            // 
            // lbl_BalanceBefore
            // 
            this.lbl_BalanceBefore.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_BalanceBefore.CanGrow = false;
            resources.ApplyResources(this.lbl_BalanceBefore, "lbl_BalanceBefore");
            this.lbl_BalanceBefore.Name = "lbl_BalanceBefore";
            this.lbl_BalanceBefore.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_BalanceBefore.StylePriority.UseBorders = false;
            this.lbl_BalanceBefore.StylePriority.UseFont = false;
            this.lbl_BalanceBefore.StylePriority.UseTextAlignment = false;
            // 
            // lbl_totalPieces
            // 
            resources.ApplyResources(this.lbl_totalPieces, "lbl_totalPieces");
            this.lbl_totalPieces.Name = "lbl_totalPieces";
            this.lbl_totalPieces.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            // 
            // lbl_VendorAddress
            // 
            resources.ApplyResources(this.lbl_VendorAddress, "lbl_VendorAddress");
            this.lbl_VendorAddress.Name = "lbl_VendorAddress";
            this.lbl_VendorAddress.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            // 
            // xrSubreport2
            // 
            resources.ApplyResources(this.xrSubreport2, "xrSubreport2");
            this.xrSubreport2.Name = "xrSubreport2";
            // 
            // lbl_TotalQty
            // 
            resources.ApplyResources(this.lbl_TotalQty, "lbl_TotalQty");
            this.lbl_TotalQty.Name = "lbl_TotalQty";
            this.lbl_TotalQty.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            // 
            // lbl_VendorMobile
            // 
            resources.ApplyResources(this.lbl_VendorMobile, "lbl_VendorMobile");
            this.lbl_VendorMobile.Name = "lbl_VendorMobile";
            this.lbl_VendorMobile.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            // 
            // lbl_CusTax
            // 
            this.lbl_CusTax.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_CusTax.CanGrow = false;
            resources.ApplyResources(this.lbl_CusTax, "lbl_CusTax");
            this.lbl_CusTax.Name = "lbl_CusTax";
            this.lbl_CusTax.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_CusTax.StylePriority.UseBorders = false;
            this.lbl_CusTax.StylePriority.UseFont = false;
            this.lbl_CusTax.StylePriority.UsePadding = false;
            this.lbl_CusTax.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel10
            // 
            this.xrLabel10.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel10.CanGrow = false;
            resources.ApplyResources(this.xrLabel10, "xrLabel10");
            this.xrLabel10.Name = "xrLabel10";
            this.xrLabel10.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel10.StylePriority.UseBorders = false;
            this.xrLabel10.StylePriority.UseFont = false;
            this.xrLabel10.StylePriority.UsePadding = false;
            this.xrLabel10.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel3
            // 
            this.xrLabel3.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel3.CanGrow = false;
            resources.ApplyResources(this.xrLabel3, "xrLabel3");
            this.xrLabel3.Name = "xrLabel3";
            this.xrLabel3.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel3.StylePriority.UseBorders = false;
            this.xrLabel3.StylePriority.UseFont = false;
            this.xrLabel3.StylePriority.UsePadding = false;
            this.xrLabel3.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel2
            // 
            this.xrLabel2.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel2.CanGrow = false;
            resources.ApplyResources(this.xrLabel2, "xrLabel2");
            this.xrLabel2.Name = "xrLabel2";
            this.xrLabel2.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel2.StylePriority.UseBorders = false;
            this.xrLabel2.StylePriority.UseFont = false;
            this.xrLabel2.StylePriority.UsePadding = false;
            this.xrLabel2.StylePriority.UseTextAlignment = false;
            // 
            // lblSubTotal
            // 
            this.lblSubTotal.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lblSubTotal.CanGrow = false;
            resources.ApplyResources(this.lblSubTotal, "lblSubTotal");
            this.lblSubTotal.Name = "lblSubTotal";
            this.lblSubTotal.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lblSubTotal.StylePriority.UseBorders = false;
            this.lblSubTotal.StylePriority.UseFont = false;
            this.lblSubTotal.StylePriority.UsePadding = false;
            this.lblSubTotal.StylePriority.UseTextAlignment = false;
            // 
            // xrPageInfo1
            // 
            resources.ApplyResources(this.xrPageInfo1, "xrPageInfo1");
            this.xrPageInfo1.Name = "xrPageInfo1";
            this.xrPageInfo1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrPageInfo1.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel5
            // 
            resources.ApplyResources(this.xrLabel5, "xrLabel5");
            this.xrLabel5.Name = "xrLabel5";
            this.xrLabel5.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel5.StylePriority.UseBackColor = false;
            this.xrLabel5.StylePriority.UseFont = false;
            this.xrLabel5.StylePriority.UseTextAlignment = false;
            // 
            // lblTotalWords
            // 
            resources.ApplyResources(this.lblTotalWords, "lblTotalWords");
            this.lblTotalWords.Name = "lblTotalWords";
            this.lblTotalWords.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblTotalWords.StylePriority.UseBackColor = false;
            this.lblTotalWords.StylePriority.UseFont = false;
            this.lblTotalWords.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Remains
            // 
            this.lbl_Remains.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_Remains.CanGrow = false;
            resources.ApplyResources(this.lbl_Remains, "lbl_Remains");
            this.lbl_Remains.Name = "lbl_Remains";
            this.lbl_Remains.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_Remains.StylePriority.UseBorders = false;
            this.lbl_Remains.StylePriority.UseFont = false;
            this.lbl_Remains.StylePriority.UsePadding = false;
            this.lbl_Remains.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Total
            // 
            this.lbl_Total.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right)));
            this.lbl_Total.CanGrow = false;
            resources.ApplyResources(this.lbl_Total, "lbl_Total");
            this.lbl_Total.Name = "lbl_Total";
            this.lbl_Total.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_Total.StylePriority.UseBorders = false;
            this.lbl_Total.StylePriority.UseFont = false;
            this.lbl_Total.StylePriority.UsePadding = false;
            this.lbl_Total.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel16
            // 
            this.xrLabel16.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel16.CanGrow = false;
            resources.ApplyResources(this.xrLabel16, "xrLabel16");
            this.xrLabel16.Name = "xrLabel16";
            this.xrLabel16.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel16.StylePriority.UseBorders = false;
            this.xrLabel16.StylePriority.UseFont = false;
            this.xrLabel16.StylePriority.UsePadding = false;
            this.xrLabel16.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Tax
            // 
            this.lbl_Tax.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_Tax.CanGrow = false;
            resources.ApplyResources(this.lbl_Tax, "lbl_Tax");
            this.lbl_Tax.Name = "lbl_Tax";
            this.lbl_Tax.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_Tax.StylePriority.UseBorders = false;
            this.lbl_Tax.StylePriority.UseFont = false;
            this.lbl_Tax.StylePriority.UsePadding = false;
            this.lbl_Tax.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Net
            // 
            this.lbl_Net.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_Net.CanGrow = false;
            resources.ApplyResources(this.lbl_Net, "lbl_Net");
            this.lbl_Net.Name = "lbl_Net";
            this.lbl_Net.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_Net.StylePriority.UseBorders = false;
            this.lbl_Net.StylePriority.UseFont = false;
            this.lbl_Net.StylePriority.UsePadding = false;
            this.lbl_Net.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Expenses
            // 
            this.lbl_Expenses.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_Expenses.CanGrow = false;
            resources.ApplyResources(this.lbl_Expenses, "lbl_Expenses");
            this.lbl_Expenses.Name = "lbl_Expenses";
            this.lbl_Expenses.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_Expenses.StylePriority.UseBorders = false;
            this.lbl_Expenses.StylePriority.UseFont = false;
            this.lbl_Expenses.StylePriority.UsePadding = false;
            this.lbl_Expenses.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel20
            // 
            this.xrLabel20.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel20.CanGrow = false;
            resources.ApplyResources(this.xrLabel20, "xrLabel20");
            this.xrLabel20.Name = "xrLabel20";
            this.xrLabel20.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel20.StylePriority.UseBorders = false;
            this.xrLabel20.StylePriority.UseFont = false;
            this.xrLabel20.StylePriority.UsePadding = false;
            this.xrLabel20.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel19
            // 
            this.xrLabel19.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel19.CanGrow = false;
            resources.ApplyResources(this.xrLabel19, "xrLabel19");
            this.xrLabel19.Name = "xrLabel19";
            this.xrLabel19.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel19.StylePriority.UseBorders = false;
            this.xrLabel19.StylePriority.UseFont = false;
            this.xrLabel19.StylePriority.UseTextAlignment = false;
            // 
            // lbl_DiscountV
            // 
            this.lbl_DiscountV.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_DiscountV.CanGrow = false;
            resources.ApplyResources(this.lbl_DiscountV, "lbl_DiscountV");
            this.lbl_DiscountV.Name = "lbl_DiscountV";
            this.lbl_DiscountV.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_DiscountV.StylePriority.UseBorders = false;
            this.lbl_DiscountV.StylePriority.UseFont = false;
            this.lbl_DiscountV.StylePriority.UsePadding = false;
            this.lbl_DiscountV.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel15
            // 
            this.xrLabel15.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel15.CanGrow = false;
            resources.ApplyResources(this.xrLabel15, "xrLabel15");
            this.xrLabel15.Name = "xrLabel15";
            this.xrLabel15.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel15.StylePriority.UseBorders = false;
            this.xrLabel15.StylePriority.UseFont = false;
            this.xrLabel15.StylePriority.UsePadding = false;
            this.xrLabel15.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel17
            // 
            this.xrLabel17.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel17.CanGrow = false;
            resources.ApplyResources(this.xrLabel17, "xrLabel17");
            this.xrLabel17.Name = "xrLabel17";
            this.xrLabel17.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel17.StylePriority.UseBorders = false;
            this.xrLabel17.StylePriority.UseFont = false;
            this.xrLabel17.StylePriority.UsePadding = false;
            this.xrLabel17.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Paied
            // 
            this.lbl_Paied.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_Paied.CanGrow = false;
            resources.ApplyResources(this.lbl_Paied, "lbl_Paied");
            this.lbl_Paied.Name = "lbl_Paied";
            this.lbl_Paied.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_Paied.StylePriority.UseBorders = false;
            this.lbl_Paied.StylePriority.UseFont = false;
            this.lbl_Paied.StylePriority.UsePadding = false;
            this.lbl_Paied.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel24
            // 
            this.xrLabel24.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel24.CanGrow = false;
            resources.ApplyResources(this.xrLabel24, "xrLabel24");
            this.xrLabel24.Name = "xrLabel24";
            this.xrLabel24.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel24.StylePriority.UseBorders = false;
            this.xrLabel24.StylePriority.UseFont = false;
            this.xrLabel24.StylePriority.UsePadding = false;
            this.xrLabel24.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel23
            // 
            this.xrLabel23.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel23.CanGrow = false;
            resources.ApplyResources(this.xrLabel23, "xrLabel23");
            this.xrLabel23.Name = "xrLabel23";
            this.xrLabel23.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel23.StylePriority.UseBorders = false;
            this.xrLabel23.StylePriority.UseFont = false;
            this.xrLabel23.StylePriority.UsePadding = false;
            this.xrLabel23.StylePriority.UseTextAlignment = false;
            // 
            // lbl_AddTaxV
            // 
            this.lbl_AddTaxV.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.lbl_AddTaxV, "lbl_AddTaxV");
            this.lbl_AddTaxV.Name = "lbl_AddTaxV";
            this.lbl_AddTaxV.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_AddTaxV.StylePriority.UseBorders = false;
            this.lbl_AddTaxV.StylePriority.UseFont = false;
            this.lbl_AddTaxV.StylePriority.UseTextAlignment = false;
            // 
            // xrCrossBandLine6
            // 
            resources.ApplyResources(this.xrCrossBandLine6, "xrCrossBandLine6");
            this.xrCrossBandLine6.EndBand = this.ReportFooter;
            this.xrCrossBandLine6.Name = "xrCrossBandLine6";
            this.xrCrossBandLine6.StartBand = this.PageHeader;
            this.xrCrossBandLine6.WidthF = 1.041672F;
            // 
            // xrCrossBandLine3
            // 
            resources.ApplyResources(this.xrCrossBandLine3, "xrCrossBandLine3");
            this.xrCrossBandLine3.EndBand = this.ReportFooter;
            this.xrCrossBandLine3.Name = "xrCrossBandLine3";
            this.xrCrossBandLine3.StartBand = this.PageHeader;
            this.xrCrossBandLine3.WidthF = 1.041656F;
            // 
            // xrCrossBandBox2
            // 
            this.xrCrossBandBox2.BorderWidth = 1F;
            resources.ApplyResources(this.xrCrossBandBox2, "xrCrossBandBox2");
            this.xrCrossBandBox2.EndBand = this.ReportFooter;
            this.xrCrossBandBox2.Name = "xrCrossBandBox2";
            this.xrCrossBandBox2.StartBand = this.PageHeader;
            this.xrCrossBandBox2.WidthF = 743.4167F;
            // 
            // xrCrossBandLine7
            // 
            resources.ApplyResources(this.xrCrossBandLine7, "xrCrossBandLine7");
            this.xrCrossBandLine7.EndBand = this.ReportFooter;
            this.xrCrossBandLine7.Name = "xrCrossBandLine7";
            this.xrCrossBandLine7.StartBand = this.PageHeader;
            this.xrCrossBandLine7.WidthF = 1.041656F;
            // 
            // xrCrossBandLine8
            // 
            resources.ApplyResources(this.xrCrossBandLine8, "xrCrossBandLine8");
            this.xrCrossBandLine8.EndBand = this.ReportFooter;
            this.xrCrossBandLine8.Name = "xrCrossBandLine8";
            this.xrCrossBandLine8.StartBand = this.PageHeader;
            this.xrCrossBandLine8.WidthF = 1F;
            // 
            // xrCrossBandLine10
            // 
            resources.ApplyResources(this.xrCrossBandLine10, "xrCrossBandLine10");
            this.xrCrossBandLine10.EndBand = this.ReportFooter;
            this.xrCrossBandLine10.Name = "xrCrossBandLine10";
            this.xrCrossBandLine10.StartBand = this.PageHeader;
            this.xrCrossBandLine10.WidthF = 1.041687F;
            // 
            // xrCrossBandLine1
            // 
            resources.ApplyResources(this.xrCrossBandLine1, "xrCrossBandLine1");
            this.xrCrossBandLine1.EndBand = this.ReportFooter;
            this.xrCrossBandLine1.Name = "xrCrossBandLine1";
            this.xrCrossBandLine1.StartBand = this.PageHeader;
            this.xrCrossBandLine1.WidthF = 1.422409F;
            // 
            // rpt_PR_Invoice
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail,
            this.TopMargin,
            this.BottomMargin,
            this.PageHeader,
            this.ReportFooter});
            this.CrossBandControls.AddRange(new DevExpress.XtraReports.UI.XRCrossBandControl[] {
            this.xrCrossBandLine1,
            this.xrCrossBandLine10,
            this.xrCrossBandLine8,
            this.xrCrossBandLine7,
            this.xrCrossBandBox2,
            this.xrCrossBandLine3,
            this.xrCrossBandLine6});
            resources.ApplyResources(this, "$this");
            this.ShowPrintMarginsWarning = false;
            this.Version = "15.1";
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.XRLabel lbl_Vendor;
        private DevExpress.XtraReports.UI.PageHeaderBand PageHeader;
        private DevExpress.XtraReports.UI.ReportFooterBand ReportFooter;
        private DevExpress.XtraReports.UI.XRLabel lbl_User;
        private DevExpress.XtraReports.UI.XRLabel xrLabel4;
        private DevExpress.XtraReports.UI.XRLabel lbl_notes;
        private DevExpress.XtraReports.UI.XRLabel xrLabel6;
        private DevExpress.XtraReports.UI.XRLine xrLine1;
        private DevExpress.XtraReports.UI.XRLabel lbl_Paymethod;
        private DevExpress.XtraReports.UI.XRLabel xrLabel1;
        private DevExpress.XtraReports.UI.XRLabel lbl_Serial;
        private DevExpress.XtraReports.UI.XRLabel lblReportName;
        private DevExpress.XtraReports.UI.XRPictureBox picLogo;
        private DevExpress.XtraReports.UI.XRLabel lblCompName;
        private DevExpress.XtraReports.UI.XRLabel lbl_Number;
        private DevExpress.XtraReports.UI.XRLabel lbl_date;
        private DevExpress.XtraReports.UI.XRLabel xrLabel12;
        private DevExpress.XtraReports.UI.XRLabel lbl_Drawer;
        private DevExpress.XtraReports.UI.XRLabel xrLabel8;
        private DevExpress.XtraReports.UI.XRLabel xrLabel7;
        private DevExpress.XtraReports.UI.XRLabel lbl_store;
        private DevExpress.XtraReports.UI.XRLabel lbl_DeductTaxV;
        private DevExpress.XtraReports.UI.XRTable xrTable4;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow4;
        private DevExpress.XtraReports.UI.XRTableCell Cell_MUOM;
        private DevExpress.XtraReports.UI.XRTableCell Cell_MUOM_Factor;
        private DevExpress.XtraReports.UI.XRTableCell cell_Factor;
        private DevExpress.XtraReports.UI.XRTable xrTable5;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow5;
        private DevExpress.XtraReports.UI.XRTableCell cell_SalesTax;
        private DevExpress.XtraReports.UI.XRTableCell cell_code2;
        private DevExpress.XtraReports.UI.XRTableCell cell_Expire;
        private DevExpress.XtraReports.UI.XRTableCell cell_DiscountRatio2;
        private DevExpress.XtraReports.UI.XRTableCell cell_DiscountRatio3;
        private DevExpress.XtraReports.UI.XRTableCell cell_SalesTaxRatio;
        private DevExpress.XtraReports.UI.XRTable xrTable3;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow3;
        private DevExpress.XtraReports.UI.XRTableCell cell_ItemDescription;
        private DevExpress.XtraReports.UI.XRTableCell cell_Height;
        private DevExpress.XtraReports.UI.XRTableCell cell_Width;
        private DevExpress.XtraReports.UI.XRTableCell cell_Length;
        private DevExpress.XtraReports.UI.XRTableCell cell_TotalQty;
        private DevExpress.XtraReports.UI.XRLabel lbl_ExpensesR;
        private DevExpress.XtraReports.UI.XRLabel lbl_salesEmp_Job;
        private DevExpress.XtraReports.UI.XRLabel lbl_AddTaxV;
        private DevExpress.XtraReports.UI.XRLabel lbl_TaxR;
        private DevExpress.XtraReports.UI.XRLabel lbl_DiscountR;
        private DevExpress.XtraReports.UI.XRLabel lbl_Remains;
        private DevExpress.XtraReports.UI.XRLabel lbl_Total;
        private DevExpress.XtraReports.UI.XRLabel xrLabel16;
        private DevExpress.XtraReports.UI.XRLabel lbl_Tax;
        private DevExpress.XtraReports.UI.XRLabel lbl_Net;
        private DevExpress.XtraReports.UI.XRLabel lbl_Expenses;
        private DevExpress.XtraReports.UI.XRLabel xrLabel20;
        private DevExpress.XtraReports.UI.XRLabel xrLabel19;
        private DevExpress.XtraReports.UI.XRLabel lbl_DiscountV;
        private DevExpress.XtraReports.UI.XRLabel xrLabel15;
        private DevExpress.XtraReports.UI.XRLabel xrLabel17;
        private DevExpress.XtraReports.UI.XRLabel lbl_Paied;
        private DevExpress.XtraReports.UI.XRLabel xrLabel24;
        private DevExpress.XtraReports.UI.XRLabel xrLabel23;
        private DevExpress.XtraReports.UI.XRTable xrTable1;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow1;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell1;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell9;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell7;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell6;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell5;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell3;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell8;
        private DevExpress.XtraReports.UI.XRCrossBandLine xrCrossBandLine6;
        private DevExpress.XtraReports.UI.XRCrossBandLine xrCrossBandLine3;
        private DevExpress.XtraReports.UI.XRCrossBandBox xrCrossBandBox2;
        private DevExpress.XtraReports.UI.XRTable xrTable2;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow2;
        private DevExpress.XtraReports.UI.XRTableCell cell_Total;
        private DevExpress.XtraReports.UI.XRTableCell cell_Disc;
        private DevExpress.XtraReports.UI.XRTableCell cell_MediumUOMQty;
        private DevExpress.XtraReports.UI.XRTableCell cell_Price;
        private DevExpress.XtraReports.UI.XRTableCell cell_Qty;
        private DevExpress.XtraReports.UI.XRTableCell cell_UOM;
        private DevExpress.XtraReports.UI.XRTableCell cell_ItemName;
        private DevExpress.XtraReports.UI.XRTableCell cell_code;
        private DevExpress.XtraReports.UI.XRPageInfo xrPageInfo1;
        private DevExpress.XtraReports.UI.XRLabel xrLabel5;
        private DevExpress.XtraReports.UI.XRLabel lblTotalWords;
        private DevExpress.XtraReports.UI.XRLabel lbl_Destination;
        private DevExpress.XtraReports.UI.XRLabel lbl_VehicleNumber;
        private DevExpress.XtraReports.UI.XRLabel lbl_DriverName;
        private DevExpress.XtraReports.UI.XRLabel lbl_ScaleWeightSerial;
        private DevExpress.XtraReports.UI.XRTableCell cell_Serial;
        private DevExpress.XtraReports.UI.XRTableCell cell_MediumUOM;
        private DevExpress.XtraReports.UI.XRCrossBandLine xrCrossBandLine7;
        private DevExpress.XtraReports.UI.XRLabel xrLabel3;
        private DevExpress.XtraReports.UI.XRLabel xrLabel2;
        private DevExpress.XtraReports.UI.XRLabel lblSubTotal;
        private DevExpress.XtraReports.UI.XRTableCell cell_ManufactureDate;
        private DevExpress.XtraReports.UI.XRLabel lbl_CusTax;
        private DevExpress.XtraReports.UI.XRLabel xrLabel10;
        private DevExpress.XtraReports.UI.XRCrossBandLine xrCrossBandLine8;
        private DevExpress.XtraReports.UI.XRCrossBandLine xrCrossBandLine10;
        private DevExpress.XtraReports.UI.XRTableCell cell_Batch;
        private DevExpress.XtraReports.UI.XRTableCell cell_DiscountRatio;
        private DevExpress.XtraReports.UI.XRLabel lbl_TotalQty;
        private DevExpress.XtraReports.UI.XRLabel lbl_VendorMobile;
        private DevExpress.XtraReports.UI.XRTableCell cell_AudiencePrice;
        private DevExpress.XtraReports.UI.XRCrossBandLine xrCrossBandLine1;
        private DevExpress.XtraReports.UI.XRLabel lbl_Updated;
        private DevExpress.XtraReports.UI.XRSubreport xrSubreport2;
        private DevExpress.XtraReports.UI.XRLabel lbl_totalPieces;
        private DevExpress.XtraReports.UI.XRLabel lbl_VendorAddress;
        private DevExpress.XtraReports.UI.XRTableCell cell_Weight_KG;
        private DevExpress.XtraReports.UI.XRLabel lbl_BalanceAfter;
        private DevExpress.XtraReports.UI.XRLabel lbl_BalanceBefore;
        private DevExpress.XtraReports.UI.XRTableCell cell_PiecesCount;
        private DevExpress.XtraReports.UI.XRTableCell cell_Pack;
        private DevExpress.XtraReports.UI.XRLabel lbl_TotalPacks;
        private DevExpress.XtraReports.UI.XRLabel Currency;
        private DevExpress.XtraReports.UI.XRLabel xrLabel11;
    }
}
