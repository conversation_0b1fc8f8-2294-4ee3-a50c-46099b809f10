﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraPrinting;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraReports.UI;

namespace Pharmacy.Forms
{
    public partial class frm_ST_InvBooksList: DevExpress.XtraEditors.XtraForm
    {
        public frm_ST_InvBooksList()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

        }

        private void frm_ST_InvBooksList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            Get_Categories();
        }
        
        private void barBtn_New_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (ErpUtils.IsFormOpen(typeof(frm_ST_InvoiceBook)))
                Application.OpenForms["frm_ST_InvoiceBook"].Close();

            new frm_ST_InvoiceBook(0).Show();
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }
        
        private void barBtn_Refresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Get_Categories();
        }

        private void barBtn_Open_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Open_Selected_Category();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {

            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(this.Text, "", "", "", grdCategory, false).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }
     
        private void grdCategory_DoubleClick(object sender, EventArgs e)
        {
            Open_Selected_Category();
        }

        private void Open_Selected_Category()
        {
            var view = grdCategory.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;
            int inv_id = Convert.ToInt32(view.GetRowCellValue(focused_row_index, colInvoiceBookId));
            if (ErpUtils.IsFormOpen(typeof(frm_ST_InvoiceBook)))
                Application.OpenForms["frm_ST_InvoiceBook"].Close();

            new frm_ST_InvoiceBook(inv_id).Show();
        }

        private void Get_Categories()
        {
            int focusedIndex = (grdCategory.FocusedView as GridView).FocusedRowHandle;

            ERPDataContext DB = new ERPDataContext();
            var Categories = (from c in DB.ST_InvoiceBooks
                              join p in DB.LKP_Processes
                              on c.ProcessId equals p.ProcessId
                              select new
                              {                                  
                                  p.ProcessName,
                                  c.InvoiceBookId,
                                  c.InvoiceBookName,
                                  c.IsTaxable,
                                  c.PrintFileName,
                              }).ToList();

            grdCategory.DataSource = Categories;
            (grdCategory.FocusedView as GridView).FocusedRowHandle = focusedIndex;
        }        

        private void barBtn_Help_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            
        }
    }
}