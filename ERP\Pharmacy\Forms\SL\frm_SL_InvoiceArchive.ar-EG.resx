﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="barBtnHelp.Caption" xml:space="preserve">
    <value>مساعدة</value>
  </data>
  <data name="barBtnPayment.Caption" xml:space="preserve">
    <value>مقبوضات</value>
  </data>
  <data name="barBtn_CashNote.Caption" xml:space="preserve">
    <value>قبض نقدي</value>
  </data>
  <data name="barBtnNotesReceivable.Caption" xml:space="preserve">
    <value>اوراق القبض</value>
  </data>
  <data name="barSubItem1.Caption" xml:space="preserve">
    <value>تحميل</value>
  </data>
  <data name="barBtnLoad_SalesOrder.Caption" xml:space="preserve">
    <value>أمر بيع</value>
  </data>
  <data name="barBtnLoad_IC_OutTrns.Caption" xml:space="preserve">
    <value>اذن صرف</value>
  </data>
  <data name="barBtnLoad_Sl_Qoute.Caption" xml:space="preserve">
    <value>عرض أسعار</value>
  </data>
  <data name="barBtnLoad_JO.Caption" xml:space="preserve">
    <value>أمر عمل</value>
  </data>
  <data name="barBtnLoad_PR_Invoice.Caption" xml:space="preserve">
    <value>فاتورة مشتريات</value>
  </data>
  <data name="barBtnLoad_IC_Transfer.Caption" xml:space="preserve">
    <value>سند نقل</value>
  </data>
  <data name="barBtn_ConvertTo.Caption" xml:space="preserve">
    <value>تحويل الى</value>
  </data>
  <data name="barBtn_OutTrns.Caption" xml:space="preserve">
    <value>اذن صرف</value>
  </data>
  <data name="barSubItemPrint.Caption" xml:space="preserve">
    <value>طباعة</value>
  </data>
  <data name="barbtnPrint.Caption" xml:space="preserve">
    <value>طباعة</value>
  </data>
  <data name="barbtnPrintF.Caption" xml:space="preserve">
    <value>طباعة بالاسم الأجنبي</value>
  </data>
  <data name="barBtnNew.Caption" xml:space="preserve">
    <value>جديد</value>
  </data>
  <data name="barBtnDelete.Caption" xml:space="preserve">
    <value>حذف</value>
  </data>
  <data name="barBtnSave.Caption" xml:space="preserve">
    <value>حفظ</value>
  </data>
  <data name="batBtnList.Caption" xml:space="preserve">
    <value>القائمة</value>
  </data>
  <data name="barBtnClose.Caption" xml:space="preserve">
    <value>غلق</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="barDockControlTop.Size" type="System.Drawing.Size, System.Drawing">
    <value>1193, 31</value>
  </data>
  <data name="barDockControlBottom.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 533</value>
  </data>
  <data name="barDockControlLeft.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 31</value>
  </data>
  <data name="barDockControlLeft.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 502</value>
  </data>
  <data name="barDockControlRight.Location" type="System.Drawing.Point, System.Drawing">
    <value>1193, 31</value>
  </data>
  <data name="barDockControlRight.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 502</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>1193, 533</value>
  </data>
  <data name="labelControl27.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 448</value>
  </data>
  <data name="labelControl27.Size" type="System.Drawing.Size, System.Drawing">
    <value>10, 13</value>
  </data>
  <data name="labelControl27.Text" xml:space="preserve">
    <value>ق</value>
  </data>
  <data name="labelControl28.Location" type="System.Drawing.Point, System.Drawing">
    <value>188, 448</value>
  </data>
  <data name="labelControl28.Size" type="System.Drawing.Size, System.Drawing">
    <value>47, 13</value>
  </data>
  <data name="labelControl28.Text" xml:space="preserve">
    <value>ض الجدول</value>
  </data>
  <data name="txt_CusTaxV.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 445</value>
  </data>
  <data name="labelControl37.Location" type="System.Drawing.Point, System.Drawing">
    <value>188, 470</value>
  </data>
  <data name="labelControl37.Size" type="System.Drawing.Size, System.Drawing">
    <value>37, 13</value>
  </data>
  <data name="labelControl37.Text" xml:space="preserve">
    <value>الاحتفاظ</value>
  </data>
  <data name="labelControl38.Location" type="System.Drawing.Point, System.Drawing">
    <value>105, 468</value>
  </data>
  <data name="labelControl38.Size" type="System.Drawing.Size, System.Drawing">
    <value>10, 13</value>
  </data>
  <data name="labelControl38.Text" xml:space="preserve">
    <value>ق</value>
  </data>
  <data name="labelControl39.Location" type="System.Drawing.Point, System.Drawing">
    <value>105, 489</value>
  </data>
  <data name="labelControl39.Size" type="System.Drawing.Size, System.Drawing">
    <value>10, 13</value>
  </data>
  <data name="labelControl39.Text" xml:space="preserve">
    <value>ق</value>
  </data>
  <data name="labelControl42.Location" type="System.Drawing.Point, System.Drawing">
    <value>188, 491</value>
  </data>
  <data name="labelControl42.Size" type="System.Drawing.Size, System.Drawing">
    <value>55, 13</value>
  </data>
  <data name="labelControl42.Text" xml:space="preserve">
    <value>دفعة مقدمة</value>
  </data>
  <data name="labelControl43.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 470</value>
  </data>
  <data name="labelControl43.Text" xml:space="preserve">
    <value>ن</value>
  </data>
  <data name="labelControl44.Location" type="System.Drawing.Point, System.Drawing">
    <value>176, 491</value>
  </data>
  <data name="labelControl44.Text" xml:space="preserve">
    <value>ن</value>
  </data>
  <data name="labelControl45.Location" type="System.Drawing.Point, System.Drawing">
    <value>120, 469</value>
  </data>
  <data name="labelControl46.Location" type="System.Drawing.Point, System.Drawing">
    <value>120, 490</value>
  </data>
  <data name="txt_retentionR.Location" type="System.Drawing.Point, System.Drawing">
    <value>134, 467</value>
  </data>
  <data name="txt_RetentionV.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 467</value>
  </data>
  <data name="txt_AdvancePayR.Location" type="System.Drawing.Point, System.Drawing">
    <value>134, 488</value>
  </data>
  <data name="txt_AdvancePayV.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 488</value>
  </data>
  <data name="chk_IsOutTrns.Location" type="System.Drawing.Point, System.Drawing">
    <value>355, 32</value>
  </data>
  <data name="chk_IsOutTrns.Properties.Caption" xml:space="preserve">
    <value>تم الصرف من المخزن</value>
  </data>
  <data name="chk_IsOutTrns.Size" type="System.Drawing.Size, System.Drawing">
    <value>138, 19</value>
  </data>
  <data name="labelControl5.Location" type="System.Drawing.Point, System.Drawing">
    <value>105, 427</value>
  </data>
  <data name="labelControl5.Size" type="System.Drawing.Size, System.Drawing">
    <value>10, 13</value>
  </data>
  <data name="labelControl5.Text" xml:space="preserve">
    <value>ق</value>
  </data>
  <data name="labelControl3.Location" type="System.Drawing.Point, System.Drawing">
    <value>621, 35</value>
  </data>
  <data name="labelControl3.Size" type="System.Drawing.Size, System.Drawing">
    <value>50, 13</value>
  </data>
  <data name="labelControl3.Text" xml:space="preserve">
    <value>نوع السداد</value>
  </data>
  <data name="btn_AddMatrixItems.Location" type="System.Drawing.Point, System.Drawing">
    <value>886, 382</value>
  </data>
  <data name="btn_AddMatrixItems.Size" type="System.Drawing.Size, System.Drawing">
    <value>23, 19</value>
  </data>
  <data name="btn_AddMatrixItems.Text" xml:space="preserve">
    <value>اضافة اصناف مصفوفة</value>
  </data>
  <data name="btn_AddMatrixItems.ToolTip" xml:space="preserve">
    <value>اضافة اصناف مصفوفة</value>
  </data>
  <data name="labelControl8.Location" type="System.Drawing.Point, System.Drawing">
    <value>121, 427</value>
  </data>
  <data name="txt_AttnMr.Location" type="System.Drawing.Point, System.Drawing">
    <value>686, 31</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="txt_AttnMr.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="txt_AttnMr.Size" type="System.Drawing.Size, System.Drawing">
    <value>209, 20</value>
  </data>
  <data name="lkp_Drawers2.Location" type="System.Drawing.Point, System.Drawing">
    <value>181, 27</value>
  </data>
  <data name="lkp_Drawers2.Size" type="System.Drawing.Size, System.Drawing">
    <value>135, 20</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="lkp_Drawers2.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="txt_PayAcc1_Paid.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 4</value>
  </data>
  <data name="txt_PayAcc1_Paid.Size" type="System.Drawing.Size, System.Drawing">
    <value>93, 20</value>
  </data>
  <data name="txt_PayAcc1_Paid.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="labelControl30.Location" type="System.Drawing.Point, System.Drawing">
    <value>101, 30</value>
  </data>
  <data name="labelControl30.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 13</value>
  </data>
  <data name="labelControl30.Text" xml:space="preserve">
    <value>مدفوع</value>
  </data>
  <data name="labelControl31.Location" type="System.Drawing.Point, System.Drawing">
    <value>101, 8</value>
  </data>
  <data name="labelControl31.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 13</value>
  </data>
  <data name="labelControl31.Text" xml:space="preserve">
    <value>مدفوع</value>
  </data>
  <data name="lkp_Drawers.Location" type="System.Drawing.Point, System.Drawing">
    <value>181, 5</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns1" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns8" xml:space="preserve">
    <value>كود الحساب</value>
  </data>
  <data name="lkp_Drawers.Size" type="System.Drawing.Size, System.Drawing">
    <value>135, 20</value>
  </data>
  <data name="lkp_Drawers.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="labelControl17.Location" type="System.Drawing.Point, System.Drawing">
    <value>322, 8</value>
  </data>
  <data name="labelControl17.Size" type="System.Drawing.Size, System.Drawing">
    <value>68, 13</value>
  </data>
  <data name="labelControl17.Text" xml:space="preserve">
    <value>حساب سداد 1</value>
  </data>
  <data name="txt_PayAcc2_Paid.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 26</value>
  </data>
  <data name="txt_PayAcc2_Paid.Size" type="System.Drawing.Size, System.Drawing">
    <value>93, 20</value>
  </data>
  <data name="txt_PayAcc2_Paid.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="labelControl33.Location" type="System.Drawing.Point, System.Drawing">
    <value>322, 30</value>
  </data>
  <data name="labelControl33.Size" type="System.Drawing.Size, System.Drawing">
    <value>68, 13</value>
  </data>
  <data name="labelControl33.Text" xml:space="preserve">
    <value>حساب سداد 2</value>
  </data>
  <data name="txt_paid.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 48</value>
  </data>
  <data name="txt_paid.Size" type="System.Drawing.Size, System.Drawing">
    <value>93, 20</value>
  </data>
  <data name="lbl_Paid.Location" type="System.Drawing.Point, System.Drawing">
    <value>101, 51</value>
  </data>
  <data name="lbl_Paid.Size" type="System.Drawing.Size, System.Drawing">
    <value>66, 13</value>
  </data>
  <data name="lbl_Paid.Text" xml:space="preserve">
    <value>اجمالي مدفوع</value>
  </data>
  <data name="lbl_remains.Location" type="System.Drawing.Point, System.Drawing">
    <value>322, 51</value>
  </data>
  <data name="lbl_remains.Size" type="System.Drawing.Size, System.Drawing">
    <value>31, 13</value>
  </data>
  <data name="lbl_remains.Text" xml:space="preserve">
    <value>متبقي</value>
  </data>
  <data name="txt_Remains.Location" type="System.Drawing.Point, System.Drawing">
    <value>181, 48</value>
  </data>
  <data name="txt_Remains.Size" type="System.Drawing.Size, System.Drawing">
    <value>135, 20</value>
  </data>
  <data name="groupControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>264, 459</value>
  </data>
  <data name="groupControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>413, 71</value>
  </data>
  <data name="groupControl1.Text" xml:space="preserve">
    <value>مدفوع</value>
  </data>
  <data name="labelControl40.Location" type="System.Drawing.Point, System.Drawing">
    <value>912, 385</value>
  </data>
  <data name="labelControl40.Size" type="System.Drawing.Size, System.Drawing">
    <value>98, 13</value>
  </data>
  <data name="labelControl40.Text" xml:space="preserve">
    <value>اضافة اصناف مصفوفة</value>
  </data>
  <data name="xtraTabControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>681, 404</value>
  </data>
  <data name="txt_Balance_After.Location" type="System.Drawing.Point, System.Drawing">
    <value>155, 55</value>
  </data>
  <data name="txt_Balance_Before.Location" type="System.Drawing.Point, System.Drawing">
    <value>155, 32</value>
  </data>
  <data name="lbl_Validate_MaxLimit.Location" type="System.Drawing.Point, System.Drawing">
    <value>168, 75</value>
  </data>
  <data name="lbl_Validate_MaxLimit.Size" type="System.Drawing.Size, System.Drawing">
    <value>300, 19</value>
  </data>
  <data name="txt_MaxCredit.Location" type="System.Drawing.Point, System.Drawing">
    <value>155, 9</value>
  </data>
  <data name="lbl_IsCredit_After.Location" type="System.Drawing.Point, System.Drawing">
    <value>321, 55</value>
  </data>
  <data name="lbl_IsCredit_After.Size" type="System.Drawing.Size, System.Drawing">
    <value>19, 13</value>
  </data>
  <data name="lbl_IsCredit_After.Text" xml:space="preserve">
    <value>دائن</value>
  </data>
  <data name="lbl_IsCredit_Before.Location" type="System.Drawing.Point, System.Drawing">
    <value>321, 32</value>
  </data>
  <data name="lbl_IsCredit_Before.Size" type="System.Drawing.Size, System.Drawing">
    <value>19, 13</value>
  </data>
  <data name="lbl_IsCredit_Before.Text" xml:space="preserve">
    <value>دائن</value>
  </data>
  <data name="lblBlncAftr.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lblBlncAftr.Location" type="System.Drawing.Point, System.Drawing">
    <value>358, 55</value>
  </data>
  <data name="lblBlncAftr.Size" type="System.Drawing.Size, System.Drawing">
    <value>76, 13</value>
  </data>
  <data name="lblBlncAftr.Text" xml:space="preserve">
    <value>رصيد بعد الفاتورة</value>
  </data>
  <data name="labelControl10.Location" type="System.Drawing.Point, System.Drawing">
    <value>350, 9</value>
  </data>
  <data name="labelControl10.Size" type="System.Drawing.Size, System.Drawing">
    <value>77, 13</value>
  </data>
  <data name="labelControl10.Text" xml:space="preserve">
    <value>حد ائتمان العميل</value>
  </data>
  <data name="labelControl24.Location" type="System.Drawing.Point, System.Drawing">
    <value>350, 32</value>
  </data>
  <data name="labelControl24.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 13</value>
  </data>
  <data name="labelControl24.Text" xml:space="preserve">
    <value>رصيد سابق</value>
  </data>
  <data name="page_AccInfo.Size" type="System.Drawing.Size, System.Drawing">
    <value>498, 101</value>
  </data>
  <data name="page_AccInfo.Text" xml:space="preserve">
    <value>حساب العميل</value>
  </data>
  <data name="xtraTabControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>504, 129</value>
  </data>
  <data name="textEdit3.EditValue" xml:space="preserve">
    <value>الحالة</value>
  </data>
  <data name="lkp_JOStatus.Properties.Columns1" xml:space="preserve">
    <value>الحالة</value>
  </data>
  <data name="textEdit1.EditValue" xml:space="preserve">
    <value>الأهمية</value>
  </data>
  <data name="lkp_JOPriority.Properties.Columns1" xml:space="preserve">
    <value>الأهمية</value>
  </data>
  <data name="textEdit5.EditValue" xml:space="preserve">
    <value>مسئول البيع</value>
  </data>
  <data name="textEdit8.EditValue" xml:space="preserve">
    <value>الجهة</value>
  </data>
  <data name="lkp_JODept.Properties.Columns1" xml:space="preserve">
    <value>الجهة</value>
  </data>
  <data name="textEdit4.EditValue" xml:space="preserve">
    <value>العمل</value>
  </data>
  <data name="textEdit2.EditValue" xml:space="preserve">
    <value>تاريخ تسليم</value>
  </data>
  <data name="txt_JODeliveryDate.EditValue" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit7.EditValue" xml:space="preserve">
    <value>رقم أمر عمل</value>
  </data>
  <data name="textEdit6.EditValue" xml:space="preserve">
    <value>تاريخ تسجيل</value>
  </data>
  <data name="txt_JORegDate.EditValue" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="page_JobOrder.Size" type="System.Drawing.Size, System.Drawing">
    <value>498, 101</value>
  </data>
  <data name="page_JobOrder.Text" xml:space="preserve">
    <value>بيانات أمر العمل</value>
  </data>
  <data name="mi_frm_IC_Item.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 22</value>
  </data>
  <data name="mi_frm_IC_Item.Text" xml:space="preserve">
    <value>بيانات الصنف</value>
  </data>
  <data name="mi_CustLastPrices.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 22</value>
  </data>
  <data name="mi_CustLastPrices.Text" xml:space="preserve">
    <value>عرض اخر اسعار لعميل</value>
  </data>
  <data name="mi_LastPrices.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 22</value>
  </data>
  <data name="mi_LastPrices.Text" xml:space="preserve">
    <value>عرض اخر اسعار</value>
  </data>
  <data name="mi_PasteRows.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 22</value>
  </data>
  <data name="mi_PasteRows.Text" xml:space="preserve">
    <value>لصق الصفوف</value>
  </data>
  <data name="mi_ExportData.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 22</value>
  </data>
  <data name="mi_ExportData.Text" xml:space="preserve">
    <value>تصدير البيانات</value>
  </data>
  <data name="mi_InvoiceStaticDisc.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 22</value>
  </data>
  <data name="mi_InvoiceStaticDisc.Text" xml:space="preserve">
    <value>تثبيت خصومات الفاتورة</value>
  </data>
  <data name="mi_InvoiceStaticDimensions.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 22</value>
  </data>
  <data name="mi_InvoiceStaticDimensions.Text" xml:space="preserve">
    <value>تثبيت الأبعاد</value>
  </data>
  <data name="mi_ImportExcel.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 22</value>
  </data>
  <data name="mi_ImportExcel.Text" xml:space="preserve">
    <value>تحميل الأصناف من ملف اكسيل</value>
  </data>
  <data name="contextMenuStrip1.Size" type="System.Drawing.Size, System.Drawing">
    <value>227, 180</value>
  </data>
  <data name="colTotalPurchasePrice.Caption" xml:space="preserve">
    <value>الاجمالي</value>
  </data>
  <data name="gridColumn6.Caption" xml:space="preserve">
    <value>س بيع</value>
  </data>
  <data name="colUOM.Caption" xml:space="preserve">
    <value>الوحده</value>
  </data>
  <data name="colQty.Caption" xml:space="preserve">
    <value>كمية</value>
  </data>
  <data name="colCustNameAr.Caption" xml:space="preserve">
    <value>العميل</value>
  </data>
  <data name="colInvoiceDate.Caption" xml:space="preserve">
    <value>التاريخ</value>
  </data>
  <data name="colInvoiceCode.Caption" xml:space="preserve">
    <value>رقم الفاتورة</value>
  </data>
  <data name="grdLastPrices.Size" type="System.Drawing.Size, System.Drawing">
    <value>498, 101</value>
  </data>
  <data name="Page_LastPrices.Size" type="System.Drawing.Size, System.Drawing">
    <value>498, 101</value>
  </data>
  <data name="Page_LastPrices.Text" xml:space="preserve">
    <value>اخر اسعار</value>
  </data>
  <data name="txtScaleSerial.TabIndex" type="System.Int32, mscorlib">
    <value>242</value>
  </data>
  <data name="labelControl26.Size" type="System.Drawing.Size, System.Drawing">
    <value>42, 13</value>
  </data>
  <data name="labelControl26.Text" xml:space="preserve">
    <value>مسلسل</value>
  </data>
  <data name="txtDestination.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="lblDestination.Size" type="System.Drawing.Size, System.Drawing">
    <value>25, 13</value>
  </data>
  <data name="lblDestination.Text" xml:space="preserve">
    <value>الجهة</value>
  </data>
  <data name="txtVehicleNumber.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="lblVehicleNumber.Size" type="System.Drawing.Size, System.Drawing">
    <value>46, 13</value>
  </data>
  <data name="lblVehicleNumber.Text" xml:space="preserve">
    <value>رقم العربة</value>
  </data>
  <data name="txtDriverName.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="lblDriverName.Text" xml:space="preserve">
    <value>اسم السائق</value>
  </data>
  <data name="tabExtraData.Size" type="System.Drawing.Size, System.Drawing">
    <value>498, 101</value>
  </data>
  <data name="tabExtraData.Text" xml:space="preserve">
    <value>بيانات إضافية</value>
  </data>
  <data name="labelControl41.Location" type="System.Drawing.Point, System.Drawing">
    <value>898, 35</value>
  </data>
  <data name="labelControl41.Size" type="System.Drawing.Size, System.Drawing">
    <value>23, 13</value>
  </data>
  <data name="labelControl41.Text" xml:space="preserve">
    <value>عناية</value>
  </data>
  <data name="cmbPayMethod.Location" type="System.Drawing.Point, System.Drawing">
    <value>509, 32</value>
  </data>
  <data name="cmbPayMethod.Properties.Items" xml:space="preserve">
    <value>آجل</value>
  </data>
  <data name="cmbPayMethod.Properties.Items3" xml:space="preserve">
    <value>كاش</value>
  </data>
  <data name="cmbPayMethod.Properties.Items6" xml:space="preserve">
    <value>اجل/كاش</value>
  </data>
  <data name="btnAddCustomer.Location" type="System.Drawing.Point, System.Drawing">
    <value>936, 32</value>
  </data>
  <data name="btnAddCustomer.ToolTip" xml:space="preserve">
    <value>اضافة عميل</value>
  </data>
  <data name="btn_add_Item.Location" type="System.Drawing.Point, System.Drawing">
    <value>1050, 382</value>
  </data>
  <data name="btn_add_Item.ToolTip" xml:space="preserve">
    <value>اضافة صنف سريع</value>
  </data>
  <data name="labelControl29.Location" type="System.Drawing.Point, System.Drawing">
    <value>1075, 385</value>
  </data>
  <data name="labelControl29.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 13</value>
  </data>
  <data name="labelControl29.Text" xml:space="preserve">
    <value>اضافة صنف سريع</value>
  </data>
  <data name="labelControl16.Location" type="System.Drawing.Point, System.Drawing">
    <value>187, 385</value>
  </data>
  <data name="labelControl16.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 13</value>
  </data>
  <data name="labelControl16.Text" xml:space="preserve">
    <value>ض.خ</value>
  </data>
  <data name="labelControl15.Location" type="System.Drawing.Point, System.Drawing">
    <value>105, 384</value>
  </data>
  <data name="labelControl15.Size" type="System.Drawing.Size, System.Drawing">
    <value>10, 13</value>
  </data>
  <data name="labelControl15.Text" xml:space="preserve">
    <value>ق</value>
  </data>
  <data name="labelControl23.Location" type="System.Drawing.Point, System.Drawing">
    <value>105, 407</value>
  </data>
  <data name="labelControl23.Size" type="System.Drawing.Size, System.Drawing">
    <value>10, 13</value>
  </data>
  <data name="labelControl23.Text" xml:space="preserve">
    <value>ق</value>
  </data>
  <data name="labelControl25.Location" type="System.Drawing.Point, System.Drawing">
    <value>187, 407</value>
  </data>
  <data name="labelControl25.Size" type="System.Drawing.Size, System.Drawing">
    <value>20, 13</value>
  </data>
  <data name="labelControl25.Text" xml:space="preserve">
    <value>ض.أ</value>
  </data>
  <data name="labelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>187, 364</value>
  </data>
  <data name="labelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 13</value>
  </data>
  <data name="labelControl1.Text" xml:space="preserve">
    <value>ض.ع</value>
  </data>
  <data name="labelControl20.Location" type="System.Drawing.Point, System.Drawing">
    <value>172, 365</value>
  </data>
  <data name="labelControl20.Size" type="System.Drawing.Size, System.Drawing">
    <value>10, 13</value>
  </data>
  <data name="labelControl20.Text" xml:space="preserve">
    <value>ق</value>
  </data>
  <data name="labelControl7.Location" type="System.Drawing.Point, System.Drawing">
    <value>187, 345</value>
  </data>
  <data name="labelControl7.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 13</value>
  </data>
  <data name="labelControl7.Text" xml:space="preserve">
    <value>خصم</value>
  </data>
  <data name="col_TotalSellPrice.Caption" xml:space="preserve">
    <value>اجمالي</value>
  </data>
  <data name="col_TotalSellPrice.Width" type="System.Int32, mscorlib">
    <value>61</value>
  </data>
  <data name="gridColumn2.Caption" xml:space="preserve">
    <value>ق خصم</value>
  </data>
  <data name="gridColumn2.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn2.VisibleIndex" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="gridColumn2.Width" type="System.Int32, mscorlib">
    <value>65</value>
  </data>
  <data name="gridColumn1.Caption" xml:space="preserve">
    <value>ن خصم</value>
  </data>
  <data name="gridColumn1.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn1.VisibleIndex" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="gridColumn1.Width" type="System.Int32, mscorlib">
    <value>62</value>
  </data>
  <data name="col_SellPrice.Caption" xml:space="preserve">
    <value>س بيع</value>
  </data>
  <data name="col_SellPrice.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="col_SellPrice.Width" type="System.Int32, mscorlib">
    <value>66</value>
  </data>
  <data name="colPurchasePrice.Caption" xml:space="preserve">
    <value>س شراء</value>
  </data>
  <data name="colPurchasePrice.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colPurchasePrice.VisibleIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="colPurchasePrice.Width" type="System.Int32, mscorlib">
    <value>63</value>
  </data>
  <data name="gridColumn7.Caption" xml:space="preserve">
    <value>كمية</value>
  </data>
  <data name="gridColumn7.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn7.Width" type="System.Int32, mscorlib">
    <value>71</value>
  </data>
  <data name="gridColumn8.Caption" xml:space="preserve">
    <value>وحدة قياس</value>
  </data>
  <data name="gridColumn16.Caption" xml:space="preserve">
    <value>المعامل</value>
  </data>
  <data name="gridColumn17.Caption" xml:space="preserve">
    <value>الوحدة</value>
  </data>
  <data name="gridColumn8.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="gridColumn8.Width" type="System.Int32, mscorlib">
    <value>89</value>
  </data>
  <data name="gridColumn10.Caption" xml:space="preserve">
    <value>اسم الصنف</value>
  </data>
  <data name="gridColumn12.Caption" xml:space="preserve">
    <value>اسم الصنف ج</value>
  </data>
  <data name="gridColumn13.Caption" xml:space="preserve">
    <value>اسم الصنف</value>
  </data>
  <data name="gridColumn14.Caption" xml:space="preserve">
    <value>كود1</value>
  </data>
  <data name="gridColumn5.Caption" xml:space="preserve">
    <value>كود2</value>
  </data>
  <data name="gridColumn4.Caption" xml:space="preserve">
    <value>الوصف</value>
  </data>
  <data name="gridColumn25.Caption" xml:space="preserve">
    <value>سعر البيع</value>
  </data>
  <data name="col_CompanyNameAr.Caption" xml:space="preserve">
    <value>الشركة</value>
  </data>
  <data name="col_CategoryNameAr.Caption" xml:space="preserve">
    <value>الفئة</value>
  </data>
  <data name="gridColumn10.VisibleIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="gridColumn10.Width" type="System.Int32, mscorlib">
    <value>188</value>
  </data>
  <data name="grdcol_branch.Caption" xml:space="preserve">
    <value>الفرع</value>
  </data>
  <data name="lkp_storee.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="grdcol_branch.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="grdcol_branch.VisibleIndex" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="gridColumn11.Caption" xml:space="preserve">
    <value>كود2</value>
  </data>
  <data name="gridColumn11.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn11.VisibleIndex" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="gridColumn11.Width" type="System.Int32, mscorlib">
    <value>46</value>
  </data>
  <data name="gridColumn31.Caption" xml:space="preserve">
    <value>كود1</value>
  </data>
  <data name="gridColumn31.VisibleIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="gridColumn31.Width" type="System.Int32, mscorlib">
    <value>88</value>
  </data>
  <data name="col_Expire.Caption" xml:space="preserve">
    <value>تاريخ صلاحية</value>
  </data>
  <data name="gridColumn18.Caption" xml:space="preserve">
    <value>تاريخ صلاحية</value>
  </data>
  <data name="gridColumn23.Caption" xml:space="preserve">
    <value>التشغيلة</value>
  </data>
  <data name="gridColumn24.Caption" xml:space="preserve">
    <value>الكمية</value>
  </data>
  <data name="col_Expire.Width" type="System.Int32, mscorlib">
    <value>81</value>
  </data>
  <data name="col_Batch.Caption" xml:space="preserve">
    <value>التشغيلة</value>
  </data>
  <data name="gridColumn33.Caption" xml:space="preserve">
    <value>تشغيلة</value>
  </data>
  <data name="gridColumn34.Caption" xml:space="preserve">
    <value>كمية</value>
  </data>
  <data name="col_Batch.Width" type="System.Int32, mscorlib">
    <value>88</value>
  </data>
  <data name="col_Length.Caption" xml:space="preserve">
    <value>طول</value>
  </data>
  <data name="col_Width.Caption" xml:space="preserve">
    <value>عرض</value>
  </data>
  <data name="col_Height.Caption" xml:space="preserve">
    <value>ارتفاع</value>
  </data>
  <data name="col_TotalQty.Caption" xml:space="preserve">
    <value>اجمالي كمية</value>
  </data>
  <data name="col_PiecesCount.Caption" xml:space="preserve">
    <value>عدد القطع</value>
  </data>
  <data name="col_ItemDescription.Caption" xml:space="preserve">
    <value>وصف</value>
  </data>
  <data name="col_ItemDescriptionEn.Caption" xml:space="preserve">
    <value>وصف ج</value>
  </data>
  <data name="col_SalesTax.Caption" xml:space="preserve">
    <value>ض . ع</value>
  </data>
  <data name="col_DiscountRatio2.Caption" xml:space="preserve">
    <value>ن خصم 2</value>
  </data>
  <data name="col_DiscountRatio3.Caption" xml:space="preserve">
    <value>ن خصم 3</value>
  </data>
  <data name="col_CusTax.Caption" xml:space="preserve">
    <value>ق ض الجدول</value>
  </data>
  <data name="lkp_store.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="grdPrInvoice.Size" type="System.Drawing.Size, System.Drawing">
    <value>1165, 149</value>
  </data>
  <data name="panelControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>1169, 153</value>
  </data>
  <data name="labelControl14.Location" type="System.Drawing.Point, System.Drawing">
    <value>172, 385</value>
  </data>
  <data name="labelControl14.Text" xml:space="preserve">
    <value>ن</value>
  </data>
  <data name="labelControl22.Location" type="System.Drawing.Point, System.Drawing">
    <value>172, 408</value>
  </data>
  <data name="labelControl22.Text" xml:space="preserve">
    <value>ن</value>
  </data>
  <data name="labelControl6.Location" type="System.Drawing.Point, System.Drawing">
    <value>105, 346</value>
  </data>
  <data name="labelControl6.Size" type="System.Drawing.Size, System.Drawing">
    <value>10, 13</value>
  </data>
  <data name="labelControl6.Text" xml:space="preserve">
    <value>ق</value>
  </data>
  <data name="labelControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>120, 384</value>
  </data>
  <data name="labelControl21.Location" type="System.Drawing.Point, System.Drawing">
    <value>121, 407</value>
  </data>
  <data name="labelControl11.Location" type="System.Drawing.Point, System.Drawing">
    <value>172, 345</value>
  </data>
  <data name="labelControl11.Text" xml:space="preserve">
    <value>ن</value>
  </data>
  <data name="labelControl12.Location" type="System.Drawing.Point, System.Drawing">
    <value>172, 427</value>
  </data>
  <data name="labelControl12.Text" xml:space="preserve">
    <value>ن</value>
  </data>
  <data name="labelControl19.Location" type="System.Drawing.Point, System.Drawing">
    <value>122, 345</value>
  </data>
  <data name="txt_Total.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 321</value>
  </data>
  <data name="txt_Total.Size" type="System.Drawing.Size, System.Drawing">
    <value>155, 20</value>
  </data>
  <data name="labelControl18.Location" type="System.Drawing.Point, System.Drawing">
    <value>172, 324</value>
  </data>
  <data name="labelControl18.Size" type="System.Drawing.Size, System.Drawing">
    <value>47, 13</value>
  </data>
  <data name="labelControl18.Text" xml:space="preserve">
    <value>الاجمــالي</value>
  </data>
  <data name="txtNet.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 511</value>
  </data>
  <data name="flowLayoutPanel1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txtTserial.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txtTserial.EditValue" xml:space="preserve">
    <value>الدفتر</value>
  </data>
  <data name="txtTserial.Size" type="System.Drawing.Size, System.Drawing">
    <value>121, 22</value>
  </data>
  <data name="lkp_InvoiceBook.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns1" xml:space="preserve">
    <value>اسم الدفتر</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns8" xml:space="preserve">
    <value>خاضع للضريبة</value>
  </data>
  <data name="lkp_InvoiceBook.Size" type="System.Drawing.Size, System.Drawing">
    <value>121, 20</value>
  </data>
  <data name="pnlBook.Location" type="System.Drawing.Point, System.Drawing">
    <value>616, 1</value>
  </data>
  <data name="pnlBook.Size" type="System.Drawing.Size, System.Drawing">
    <value>125, 43</value>
  </data>
  <data name="txtTinvCode.EditValue" xml:space="preserve">
    <value>رقم الفاتورة</value>
  </data>
  <data name="txtTinvCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 2</value>
  </data>
  <data name="txtTinvCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 22</value>
  </data>
  <data name="txtInvoiceCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 23</value>
  </data>
  <data name="txtInvoiceCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 20</value>
  </data>
  <data name="pnlInvCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>531, 1</value>
  </data>
  <data name="txtTdate.EditValue" xml:space="preserve">
    <value>التاريخ</value>
  </data>
  <data name="dtInvoiceDate.EditValue" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="pnlDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>400, 1</value>
  </data>
  <data name="txtTstore.EditValue" xml:space="preserve">
    <value>الفرع</value>
  </data>
  <data name="txtTstore.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 2</value>
  </data>
  <data name="lkpStore.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 23</value>
  </data>
  <data name="lkpStore.Properties.Columns1" xml:space="preserve">
    <value>اسم الفرع</value>
  </data>
  <data name="lkpStore.Properties.Columns8" xml:space="preserve">
    <value>كود الفرع</value>
  </data>
  <data name="pnlBranch.Location" type="System.Drawing.Point, System.Drawing">
    <value>257, 1</value>
  </data>
  <data name="txtTdueDate.EditValue" xml:space="preserve">
    <value>تاريخ استحقاق</value>
  </data>
  <data name="txt_DueDate.EditValue" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="pnlAgeDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>126, 1</value>
  </data>
  <data name="txtTdeliverDate.EditValue" xml:space="preserve">
    <value>تاريخ تسليم</value>
  </data>
  <data name="dtDeliverDate.EditValue" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="pnlDeliveryDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>29, 1</value>
  </data>
  <data name="txtCurrency.EditValue" xml:space="preserve">
    <value>العملة</value>
  </data>
  <data name="txtCurrency.Size" type="System.Drawing.Size, System.Drawing">
    <value>121, 22</value>
  </data>
  <data name="pnlCurrency.Location" type="System.Drawing.Point, System.Drawing">
    <value>616, 47</value>
  </data>
  <data name="pnlCurrency.Size" type="System.Drawing.Size, System.Drawing">
    <value>125, 44</value>
  </data>
  <data name="chk_IsPosted.Properties.Caption" xml:space="preserve">
    <value>مصروف من المخزن</value>
  </data>
  <data name="pnlPostStore.Location" type="System.Drawing.Point, System.Drawing">
    <value>473, 49</value>
  </data>
  <data name="txtTpo.EditValue" xml:space="preserve">
    <value>أمر شراء</value>
  </data>
  <data name="txtTpo.Size" type="System.Drawing.Size, System.Drawing">
    <value>79, 22</value>
  </data>
  <data name="txt_PO_No.Size" type="System.Drawing.Size, System.Drawing">
    <value>79, 20</value>
  </data>
  <data name="pnlPO.Location" type="System.Drawing.Point, System.Drawing">
    <value>386, 47</value>
  </data>
  <data name="pnlPO.Size" type="System.Drawing.Size, System.Drawing">
    <value>83, 44</value>
  </data>
  <data name="txtTSalesEmp.EditValue" xml:space="preserve">
    <value>مسئول المبيعات</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns3" xml:space="preserve">
    <value>الكود</value>
  </data>
  <data name="pnlSalesEmp.Location" type="System.Drawing.Point, System.Drawing">
    <value>206, 47</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns8" xml:space="preserve">
    <value>مركز التكلفة</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns15" xml:space="preserve">
    <value>كود مركز تكلفة</value>
  </data>
  <data name="textEdit9.EditValue" xml:space="preserve">
    <value>مركز تكلفة</value>
  </data>
  <data name="pnlCostCenter.Location" type="System.Drawing.Point, System.Drawing">
    <value>63, 47</value>
  </data>
  <data name="txtSourceCode.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="cmdProcess.Properties.Items" xml:space="preserve">
    <value>عرض سعر</value>
  </data>
  <data name="cmdProcess.Properties.Items3" xml:space="preserve">
    <value>أمر بيع</value>
  </data>
  <data name="cmdProcess.Properties.Items6" xml:space="preserve">
    <value>أمر عمل</value>
  </data>
  <data name="cmdProcess.Properties.Items9" xml:space="preserve">
    <value>اذن صرف</value>
  </data>
  <data name="cmdProcess.Properties.Items12" xml:space="preserve">
    <value>فاتورة شراء</value>
  </data>
  <data name="cmdProcess.Properties.Items15" xml:space="preserve">
    <value>البسكول</value>
  </data>
  <data name="pnlSrcPrc.Location" type="System.Drawing.Point, System.Drawing">
    <value>643, 97</value>
  </data>
  <data name="flowLayoutPanel1.Location" type="System.Drawing.Point, System.Drawing">
    <value>422, 3</value>
  </data>
  <data name="flowLayoutPanel1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="flowLayoutPanel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>742, 99</value>
  </data>
  <data name="labelControl4.Location" type="System.Drawing.Point, System.Drawing">
    <value>226, 21</value>
  </data>
  <data name="labelControl4.Size" type="System.Drawing.Size, System.Drawing">
    <value>41, 13</value>
  </data>
  <data name="labelControl4.Text" xml:space="preserve">
    <value>ملاحظات</value>
  </data>
  <data name="lblShipTo.Location" type="System.Drawing.Point, System.Drawing">
    <value>228, 66</value>
  </data>
  <data name="lblShipTo.Size" type="System.Drawing.Size, System.Drawing">
    <value>46, 13</value>
  </data>
  <data name="lblShipTo.Text" xml:space="preserve">
    <value>شحن الى</value>
  </data>
  <data name="txtNotes.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 5</value>
  </data>
  <data name="txtNotes.Size" type="System.Drawing.Size, System.Drawing">
    <value>220, 44</value>
  </data>
  <data name="txt_Shipping.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 50</value>
  </data>
  <data name="txt_Shipping.Size" type="System.Drawing.Size, System.Drawing">
    <value>220, 44</value>
  </data>
  <data name="panelControl1.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="labelControl13.Location" type="System.Drawing.Point, System.Drawing">
    <value>172, 514</value>
  </data>
  <data name="labelControl13.Size" type="System.Drawing.Size, System.Drawing">
    <value>34, 13</value>
  </data>
  <data name="labelControl13.Text" xml:space="preserve">
    <value>الصافي</value>
  </data>
  <data name="labelControl36.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 13</value>
  </data>
  <data name="labelControl36.Text" xml:space="preserve">
    <value>فواتير البيع</value>
  </data>
  <data name="lkp_Customers.Location" type="System.Drawing.Point, System.Drawing">
    <value>960, 31</value>
  </data>
  <data name="gridColumn20.Caption" xml:space="preserve">
    <value>كود</value>
  </data>
  <data name="gridColumn20.Width" type="System.Int32, mscorlib">
    <value>110</value>
  </data>
  <data name="gridColumn21.Caption" xml:space="preserve">
    <value>الاسم</value>
  </data>
  <data name="gridColumn21.Width" type="System.Int32, mscorlib">
    <value>438</value>
  </data>
  <data name="gridColumn22.Caption" xml:space="preserve">
    <value>الاسم ج</value>
  </data>
  <data name="gridColumn26.Caption" xml:space="preserve">
    <value>الفئة</value>
  </data>
  <data name="gridColumn27.Caption" xml:space="preserve">
    <value>المدينة</value>
  </data>
  <data name="gridColumn30.Caption" xml:space="preserve">
    <value>المحمول</value>
  </data>
  <data name="gridColumn30.Width" type="System.Int32, mscorlib">
    <value>181</value>
  </data>
  <data name="lkp_Customers.Size" type="System.Drawing.Size, System.Drawing">
    <value>172, 20</value>
  </data>
  <data name="btnNext.ToolTip" xml:space="preserve">
    <value>التالي</value>
  </data>
  <data name="labelControl35.Location" type="System.Drawing.Point, System.Drawing">
    <value>1137, 35</value>
  </data>
  <data name="labelControl35.Size" type="System.Drawing.Size, System.Drawing">
    <value>31, 13</value>
  </data>
  <data name="labelControl35.Text" xml:space="preserve">
    <value>العميل</value>
  </data>
  <data name="btnPrevious.ToolTip" xml:space="preserve">
    <value>السابق</value>
  </data>
  <data name="labelControl9.Location" type="System.Drawing.Point, System.Drawing">
    <value>187, 427</value>
  </data>
  <data name="labelControl9.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 13</value>
  </data>
  <data name="labelControl9.Text" xml:space="preserve">
    <value>تكاليف أخرى</value>
  </data>
  <data name="txtExpenses.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 424</value>
  </data>
  <data name="txtDiscountRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>134, 342</value>
  </data>
  <data name="txtDiscountRatio.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="txtDiscountValue.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 342</value>
  </data>
  <data name="txtDiscountValue.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="txt_TaxValue.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_TaxValue.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 362</value>
  </data>
  <data name="txt_DeductTaxR.Location" type="System.Drawing.Point, System.Drawing">
    <value>134, 382</value>
  </data>
  <data name="txt_DeductTaxV.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 382</value>
  </data>
  <data name="txt_AddTaxR.Location" type="System.Drawing.Point, System.Drawing">
    <value>134, 404</value>
  </data>
  <data name="txt_AddTaxV.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 404</value>
  </data>
  <data name="txtExpensesR.Location" type="System.Drawing.Point, System.Drawing">
    <value>134, 424</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAA
        AAAfbf8AH2z/AB9r/x8fa/+rH2v/9h9r/9AfbP9HH2//AB9u/wAfcP8AAAAAAB9p/wAfZv8AH2D/Ah9j
        /2cfY//iH2P/7R9j/4YfY/8KH2P/AB9j/wAAAAAADw4QAA4NDwAODQ4ADQwORwwMDdAMCw32CwsMqwsL
        DB8MCw0ADAsNAB9u/wAfbf8WH23/qx9t//8fbv//H27//x9u/9gfb/89H3D/AB9y/wAfcf8AH23/AB9n
        /wAfZ/9cH2X/6x9j//8fY///H2P/9x9j/38fY/8GH2P/AB5e8QAPDhAADw4QAA4NDz0NDQ/YDQ0O/w0M
        Dv8MDA3/DAsNqwwLDRYMCw0AH2//FR9v/6Ifb///H3D//x9x//8fcf//H3H//x9x/9Mfcv82IHD/AiBw
        /wQfcP8DH2z/VB9q/+gfZ///H2X//x9j//8fY///H2P/9x9j/3cfY/8FH2P+ABAPEQAQDxE1Dw4Q0w8O
        EP8ODg//Dg0P/w0NDv8MDA3/DAsNogwLDRUfcP+MH3H//R9y//8ec///HnP//x50//8edP//HnP//x5z
        /9cec/+pH3L/qR9x/6wfbv/mH23//x9q//8faP//H2X//x9j//8fY///H2P/8h9j/04bTb8AERASERAQ
        ErsQEBL/EA8R/w8PEf8PDhD/Dg0P/w0ND/8MDA79DAwNjB5z/9QedP//HnX//x52//8edv//Hnb//x52
        //8edv//Hnb//x51//8edP//HnP//x9x//8fb///H23//x9r//8faP//H2X//x9j//8fY///H2P/gxg4
        hAASERM1ERET7hERE/8REBL/EBAS/xAPEf8PDhD/Dg4Q/w0ND/8NDA7THnX/wx52//8ed///Hnj//x54
        //8eef//Hnn//x54//8eeP/9Hnf/8h52//Iedf/zHnP//h9x//8fb///H23//x9q//8fZ///H2T//x9j
        //8fY/90GkGeABISFCYTEhThEhIU/xIRE/8RERP/EBAS/xAPEf8PDhD/Dg4Q/w4ND78ed/9UHnj/5x55
        //8eev//Hnr//x57//8ee///Hnr/+x56/5AeeP88Hnf/PB52/0Eedf+wHnP//x9x//8fb///H2z//x9p
        //8fZv//H2T/zB9j/yQdV9wAExIVAxMTFYMTEhX8ExIU/xISFP8RERP/ERAS/xAPEf8PDhDkDg4QTx50
        /wAeev9dHnv/7R58//8efP//Hn3//x59//0efP+UHnv/DB55/wAed/8AHnb/AB52/x8edf+5HnP//x9x
        //8fbv//H2v//x9o/9YfZf85H2f/AAAAAAAUExYAFBMWDRQTFpQUExX9ExIV/xISFP8SERP/ERAS6hAP
        EVYTDhQAHnr/AB58/wEefP9nHn3/7x5+//8efv/9Hn7/nR59/xAefP8AHnv/AB52/wAedf8AHnX/AB51
        /yUedP2+H3P//x9x//8fbPvbH2r/Px9o/wAfZv8AH2P/ABQTFgAUExYAFBQWEBQUFp0UExb9ExIV/xIS
        FOwRERNfIxkMABAQEQAee/8AHn3/AB5+/wcefv+xHn///x5//+oef/8tHn//AB59/wAefP8AAAAAAB52
        /wAeV/8AGkKGABk+eUYaQoPzG0KG/xo0ZZIbRZkAH2v/AB9o/wAAAAAAFBQWABQUFgAVFBcAFRQXLRUU
        F+oUExb/ExMVqxMTFAUSERQAEBASAAAAAAAegP8AHn//Ax6A/6kegP//HoD/5x6A/ycegP8AHoD/AAAA
        AAAAAAAAAAAAABpCgQAXFRcAFhMSOhcVFvAXFRb/GBUWjhgYHgAcO3gAAAAAAAAAAAAAAAAAFxYZABYV
        GAAWFRgnFRUX5xUUF/8UExajExIVAhQTFgAAAAAAHoD/AB6A/wAegP8IHoD/sx6B//8egf/rHoH/Lx6B
        /wAegv8AHoL/AAAAAAAXFhkAFBQXABcXGgAXFxpIGBca9BgYG/8ZGBuTGRkcABoaHQAbGx4AAAAAABkZ
        HAAYGBsAFxcZABcWGS8WFhjrFRUX/xUUF6wUFBYGFBMWABQTFQAef/8AHoD/Ah6A/20egf/xHoH//x6B
        //4egv+kHoL/Ex6C/wAegv8AFhYYABsZIAAXFxoAFxcaKRgXGsMYGBv/GRkc/xoZHN4bGh1FHBoeABwb
        HwAdHCAAGhkdABkZHAAYGBsTGBcapBcWGf4WFRj/FRQX7xQUFmYTEBMBExMVAB59/wIegP9kHoD/8B6B
        //8egv//HoL//x6C//8egv+cHoL/EB6B/wAaRX4AFxYZABcWGSQXFxq/GBgb/xkZHP8aGR3/Gxoe/xwb
        HtkcGx89HBwfABoYIQAbGh0AGxodEBkZHJwYGBv/Fxca/xYWGf8VFRf/FBQW7RQTFVwBABAAHn//Vx5/
        /+kegP//HoH//x6C//8egv//HoL//x6C//4egf+JHoD/BBgxUQAWFhgSFxYZsRgXGv8ZGBv/Ghkc/xsa
        Hf8cGx7/HBwf/x0cIM8eHSAmHRwgAB0cHwQbGh6JGhkc/hkYG/8YFxr/FxYZ/xYVGP8VFBf/ExMV5hMS
        FFIefv/EHn///x6A//8egf//HoL//x6C//8egv//HoL//x6B/+Megf8nGUB0ABYWGEwXFhn4GBca/xkY
        G/8aGh3/Gxse/xwcH/8dHCD/Hh0h/x4dIXUdHCAAHBsfJxwbHuMaGh3/GRkc/xgXGv8XFhn/FhUY/xUU
        F/8UExX/ExIUwR5+/9Uef///HoD//x6B//8egf//HoL//x6C//8egf//HoH/7h6B/zQaRX4AFhYZWhcW
        GfwYGBv/GRkc/xsaHf8cGx7/HRwg/x4dIf8fHiL/Hx4igx4dIQAdHB80HBsf7hsaHf8aGRz/GBgb/xcX
        Gf8WFRj/FRQX/xQTFf8TEhXSHn7/jR5///0egP//HoD//x6B//8egf//HoH//x6B//8egf+4HoD/EBk3
        YAAWFhkqFxYZ2xgYG/8ZGRz/Gxod/xwbHv8dHCD/Hh0h/x8eIu8gHyNKHx4iABwcHxAcGx64Gxod/xoZ
        HP8YGBv/Fxca/xYVGP8VFBf/FBMV/BMSFYgefv8VHn7/oh5///8egP//HoD//x6B//8egf//HoD/0B6A
        /zIegf8AFxIRABgaHQAXFxpQGBca5hkYHP8aGh3/Gxse/x0cH/8dHSD1Hh0hch8eIwQfHiIAGxseABwb
        HjIaGh3QGRkc/xgXG/8XFhn/FhUY/xUUF/8UExafExIVEx5+/wAefv8XHn7/qx5///8ef///Hn///x6A
        /9UegP85HoD/AB5//wAYIzUAFxcaABgYGwAYFxpXGRgb6hoZHP8bGh7/HBse+B0cH3weHSEFHh0hAB8e
        IgAbGh0AGxodABoZHDkZGBvVGBca/xcWGf8WFRj/FRQXqBQUFhUUExYAHn7/AB5+/wAefv8dHn7/yR5+
        //8efv/yHn//Sh5//wAef/8AHoD/AAAAAAAYFxoAGBcaABUXGgAZGBtUGRkcwxoaHc0bGh5yHBseCB0c
        HwAeHSEAAAAAABsaHQAaGRwAGRgbABgYG0oYFxryFxYZ/xYVGMUVFBcbFRQXABQUFgAefv8AHnz/AB56
        /wIefP+oHn3//x59/+ceff8lHn3/AB5//wAAAAAAAAAAAAAAAAAYGBoAGBgbABgXGgAaGRwNGhkdERkZ
        HAAbGh4AGxoeAAAAAAAAAAAAAAAAABkZHAAXFxoAFxcaJRcWGecWFRj/FRUXohMSFQEVFBcAFRQXAB50
        /wAed/8AHnn/CR56/7Qee///Hnv/6x57/y4eev8AHnj/AB53/wAAAAAAFRQXABYVGAAWFRgAFxYZJBgX
        GnEYFxp6GRgbNBkOEQAYGBsAGBcaAAAAAAAWFhgAFhYYABYWGQAXFhkuFhYY6xUVF/8VFBeuFBQWBxMT
        FQASERQAHnL/AB11/wMedv9zHnf/8x54//8eef/+Hnn/oR55/xEed/8AHnb/ABMSFgAbFx0AFRQXABUV
        Fy4WFhjJFxYZ/xgXGv8YFxreGBcaShcXGgAYFxoAFxYZABYVGAAWFRgAFhYYERYWGKEWFRj+FRQX/xQT
        FvETEhVuEhETAxIREwAgb/8CH3L/aR50//Iedf//Hnb//x53//8ed//+Hnf/lx52/w0edf8AGEOMABQT
        FgAUExYnFRQXxBUVF/8WFhj/FxYZ/xcXGv8XFxrdFxcaQRYWGQAWFRgAFhUYABYVGA0WFRiXFhUY/hUU
        F/8UExb/ExMV/xISFPARERNlEQ4RAh9t/1sfb//sH3H//x9y//8ec///HnT//x50//8edP/9HnT/hx9y
        /wUVKUwAEhIUFBMSFbYUExb/FBQW/xERE/8QEBL/FhUY/xYWGf8XFhnXFhYYYBYVGE4WFRhOFhUYmBUV
        F/wVFBf/FBMW/xMTFf8SEhT/ERET/xEQEuoQDxFXH2v/xh9s//8fbv//H2///x9w//8fcf//H3H//x9x
        //8fcf/kH3D/KBc3cQASERNOEhIU+BMSFf8SERT/DAwN/woJC/8QDxH/FRUX/xYVGP8WFRj7FRUX+BUU
        F/gVFBf/FBQW/xQTFv8TEhX/EhIU/xIRE/8REBL/EA8R/w8PEMMfZ//UH2n//x9r//8fbP//H23//x9u
        //8fbv//H27//x9u/+4fbv80Fzl6ABEQE1oRERP8EhIU/xERE/8ODg//DAwN/xAPEf8UExb/FBQW/xQU
        Fv8UFBb/FBMW/xQTFv8TExX/ExIU/xISFP8SERP/ERAS/xAQEv8PDxH/Dg4Q0R9l/4kfZf/8H2f//x9o
        //8faf//H2r//x9q//8fav//H2v/sh9r/w4VKlcAEBASKRAQEtoRERP/EhET/xEQEv8QEBL/EhIU/xMT
        Ff8TExXyExMVsxMTFagTEhWoExIU1BISFP8SERP/ERET/xEQEv8QDxH/Dw8R/w4OEPwODQ+EH2P/Ex9j
        /6AfZP//H2T//x9l//8fZv//H2b//x9n/8gfaP8qH2j/AA0CAAAREBIAEA8RThAQEuUREBL/ERET/xIR
        E/8SERP/EhEU9hISFHYTExUIEhEUAxMRFQISERMxERETzREQEv8QEBL/EA8R/w8OEP8ODhD/Dg0Pnw0N
        DxIfY/8AH2P/Fh9j/6kfY///H2P//x9j//8fY//MH2T/MB9m/wAfVP8AERgnABAPEQAQERMAEA8RVRAP
        EegQEBL/EBAS/xEQEvcRERN9EhETBhISFAATEhUAExIVABEREwAQEBI2EA8R0g8PEf8PDhD/Dg4P/w4N
        D6gNDQ8VDg0PAB9j/wAfY/8AH2P/HR9j/6QfY//nH2P/wh9j/zofZP8AH2P/AB9m/wAAAAAAEA8RAA8P
        EQAKDhIBDw8QYA8PEdcQDxHiEA8RgRAPEQkREBIAEhETAAAAAAARERMAEA8RABAPEQAPDhBADg4Qxg4N
        D+cODQ+iDQ0OHA0NDwAODQ8AACAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAGAIBw
        DgEAIAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAQAAHAOAAAgBAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgBAA=
</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>فاتورة البيع</value>
  </data>
</root>