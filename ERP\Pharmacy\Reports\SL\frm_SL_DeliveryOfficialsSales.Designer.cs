﻿namespace Reports.SL
{
    partial class frm_SL_DeliveryOfficialsSales
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_SL_DeliveryOfficialsSales));
            DevExpress.XtraGrid.GridLevelNode gridLevelNode1 = new DevExpress.XtraGrid.GridLevelNode();
            DevExpress.XtraCharts.Series series1 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.SeriesPoint seriesPoint1 = new DevExpress.XtraCharts.SeriesPoint("0", new object[] {
            ((object)(8.8D))}, 0);
            DevExpress.XtraCharts.SeriesPoint seriesPoint2 = new DevExpress.XtraCharts.SeriesPoint("1", new object[] {
            ((object)(2.2D))}, 1);
            DevExpress.XtraCharts.SeriesPoint seriesPoint3 = new DevExpress.XtraCharts.SeriesPoint("2", new object[] {
            ((object)(4D))}, 2);
            DevExpress.XtraCharts.SeriesPoint seriesPoint4 = new DevExpress.XtraCharts.SeriesPoint("3", new object[] {
            ((object)(9.2D))}, 3);
            DevExpress.XtraCharts.SeriesPoint seriesPoint5 = new DevExpress.XtraCharts.SeriesPoint("4", new object[] {
            ((object)(5.8D))}, 4);
            DevExpress.XtraCharts.PieSeriesView pieSeriesView1 = new DevExpress.XtraCharts.PieSeriesView();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_OutTrnsDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_OutTrnsCode = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CusNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Qtyd = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_TotalSellPrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.grd_data = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_Delegate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ItemNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Category = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Qty = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_UOM = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_PayMethod = new DevExpress.XtraEditors.Repository.RepositoryItemImageComboBox();
            this.repositoryItemImageComboBox1 = new DevExpress.XtraEditors.Repository.RepositoryItemImageComboBox();
            this.picLogo = new DevExpress.XtraEditors.PictureEdit();
            this.lblFilter = new DevExpress.XtraEditors.TextEdit();
            this.lblDateFilter = new DevExpress.XtraEditors.TextEdit();
            this.lblReportName = new DevExpress.XtraEditors.TextEdit();
            this.Report = new DevExpress.XtraTab.XtraTabControl();
            this.xtraTabPage1 = new DevExpress.XtraTab.XtraTabPage();
            this.xtraTabPage2 = new DevExpress.XtraTab.XtraTabPage();
            this.ch_Delegates = new DevExpress.XtraCharts.ChartControl();
            this.xtraTabPage3 = new DevExpress.XtraTab.XtraTabPage();
            this.ch_Report = new DevExpress.XtraCharts.ChartControl();
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtn_PreviewData = new DevExpress.XtraBars.BarButtonItem();
            this.barSubItem1 = new DevExpress.XtraBars.BarSubItem();
            this.barButtonItem5 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem6 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem7 = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.barBtn_Preview = new DevExpress.XtraBars.BarButtonItem();
            this.barBtn_Help = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem1 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem2 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem3 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem4 = new DevExpress.XtraBars.BarButtonItem();
            this.bar2 = new DevExpress.XtraBars.Bar();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grd_data)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_PayMethod)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemImageComboBox1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFilter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDateFilter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblReportName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Report)).BeginInit();
            this.Report.SuspendLayout();
            this.xtraTabPage1.SuspendLayout();
            this.xtraTabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ch_Delegates)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView1)).BeginInit();
            this.xtraTabPage3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.ch_Report)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            this.SuspendLayout();
            // 
            // gridView2
            // 
            this.gridView2.Appearance.HeaderPanel.Font = ((System.Drawing.Font)(resources.GetObject("gridView2.Appearance.HeaderPanel.Font")));
            this.gridView2.Appearance.HeaderPanel.Options.UseFont = true;
            this.gridView2.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView2.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView2.Appearance.Row.Options.UseTextOptions = true;
            this.gridView2.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView2.AppearancePrint.HeaderPanel.Font = ((System.Drawing.Font)(resources.GetObject("gridView2.AppearancePrint.HeaderPanel.Font")));
            this.gridView2.AppearancePrint.HeaderPanel.Options.UseFont = true;
            this.gridView2.AppearancePrint.HeaderPanel.Options.UseTextOptions = true;
            this.gridView2.AppearancePrint.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView2.ColumnPanelRowHeight = 50;
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_OutTrnsDate,
            this.col_OutTrnsCode,
            this.col_CusNameAr,
            this.col_Qtyd,
            this.col_TotalSellPrice});
            this.gridView2.GridControl = this.grd_data;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsBehavior.Editable = false;
            this.gridView2.OptionsCustomization.AllowGroup = false;
            this.gridView2.OptionsView.ShowAutoFilterRow = true;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            // 
            // col_OutTrnsDate
            // 
            resources.ApplyResources(this.col_OutTrnsDate, "col_OutTrnsDate");
            this.col_OutTrnsDate.FieldName = "OutTrnsDate";
            this.col_OutTrnsDate.Name = "col_OutTrnsDate";
            // 
            // col_OutTrnsCode
            // 
            resources.ApplyResources(this.col_OutTrnsCode, "col_OutTrnsCode");
            this.col_OutTrnsCode.FieldName = "OutTrnsCode";
            this.col_OutTrnsCode.Name = "col_OutTrnsCode";
            // 
            // col_CusNameAr
            // 
            resources.ApplyResources(this.col_CusNameAr, "col_CusNameAr");
            this.col_CusNameAr.FieldName = "CusNameAr";
            this.col_CusNameAr.Name = "col_CusNameAr";
            // 
            // col_Qtyd
            // 
            resources.ApplyResources(this.col_Qtyd, "col_Qtyd");
            this.col_Qtyd.DisplayFormat.FormatString = "n2";
            this.col_Qtyd.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_Qtyd.FieldName = "Qty";
            this.col_Qtyd.Name = "col_Qtyd";
            // 
            // col_TotalSellPrice
            // 
            resources.ApplyResources(this.col_TotalSellPrice, "col_TotalSellPrice");
            this.col_TotalSellPrice.DisplayFormat.FormatString = "n2";
            this.col_TotalSellPrice.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_TotalSellPrice.FieldName = "TotalSellPrice";
            this.col_TotalSellPrice.Name = "col_TotalSellPrice";
            // 
            // grd_data
            // 
            resources.ApplyResources(this.grd_data, "grd_data");
            gridLevelNode1.LevelTemplate = this.gridView2;
            gridLevelNode1.RelationName = "Detail";
            this.grd_data.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode1});
            this.grd_data.MainView = this.gridView1;
            this.grd_data.Name = "grd_data";
            this.grd_data.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.rep_PayMethod,
            this.repositoryItemImageComboBox1});
            this.grd_data.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1,
            this.gridView2});
            // 
            // gridView1
            // 
            this.gridView1.Appearance.HeaderPanel.Font = ((System.Drawing.Font)(resources.GetObject("gridView1.Appearance.HeaderPanel.Font")));
            this.gridView1.Appearance.HeaderPanel.Options.UseFont = true;
            this.gridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView1.Appearance.Row.Options.UseTextOptions = true;
            this.gridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.AppearancePrint.GroupRow.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupRow.BorderColor")));
            this.gridView1.AppearancePrint.GroupRow.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupRow.ForeColor")));
            this.gridView1.AppearancePrint.GroupRow.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.GroupRow.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.Font = ((System.Drawing.Font)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.Font")));
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.AppearancePrint.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.AppearancePrint.Row.Options.UseTextOptions = true;
            this.gridView1.AppearancePrint.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.ColumnPanelRowHeight = 50;
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_Delegate,
            this.col_ItemNameAr,
            this.col_Category,
            this.col_Qty,
            this.col_UOM});
            this.gridView1.GridControl = this.grd_data;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsBehavior.ReadOnly = true;
            this.gridView1.OptionsPrint.ExpandAllGroups = false;
            this.gridView1.OptionsView.ShowAutoFilterRow = true;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            // 
            // col_Delegate
            // 
            resources.ApplyResources(this.col_Delegate, "col_Delegate");
            this.col_Delegate.FieldName = "Delegate";
            this.col_Delegate.Name = "col_Delegate";
            this.col_Delegate.OptionsFilter.AllowFilter = false;
            // 
            // col_ItemNameAr
            // 
            resources.ApplyResources(this.col_ItemNameAr, "col_ItemNameAr");
            this.col_ItemNameAr.FieldName = "ItemNameAr";
            this.col_ItemNameAr.Name = "col_ItemNameAr";
            // 
            // col_Category
            // 
            resources.ApplyResources(this.col_Category, "col_Category");
            this.col_Category.FieldName = "Category";
            this.col_Category.Name = "col_Category";
            // 
            // col_Qty
            // 
            resources.ApplyResources(this.col_Qty, "col_Qty");
            this.col_Qty.DisplayFormat.FormatString = "n2";
            this.col_Qty.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_Qty.FieldName = "Qty";
            this.col_Qty.Name = "col_Qty";
            // 
            // col_UOM
            // 
            resources.ApplyResources(this.col_UOM, "col_UOM");
            this.col_UOM.FieldName = "UOM";
            this.col_UOM.Name = "col_UOM";
            // 
            // rep_PayMethod
            // 
            resources.ApplyResources(this.rep_PayMethod, "rep_PayMethod");
            this.rep_PayMethod.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_PayMethod.Buttons"))))});
            this.rep_PayMethod.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("rep_PayMethod.Items"), ((object)(resources.GetObject("rep_PayMethod.Items1"))), ((int)(resources.GetObject("rep_PayMethod.Items2")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("rep_PayMethod.Items3"), resources.GetString("rep_PayMethod.Items4"), ((int)(resources.GetObject("rep_PayMethod.Items5")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("rep_PayMethod.Items6"), ((object)(resources.GetObject("rep_PayMethod.Items7"))), ((int)(resources.GetObject("rep_PayMethod.Items8"))))});
            this.rep_PayMethod.Name = "rep_PayMethod";
            // 
            // repositoryItemImageComboBox1
            // 
            resources.ApplyResources(this.repositoryItemImageComboBox1, "repositoryItemImageComboBox1");
            this.repositoryItemImageComboBox1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repositoryItemImageComboBox1.Buttons"))))});
            this.repositoryItemImageComboBox1.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("repositoryItemImageComboBox1.Items"), ((object)(resources.GetObject("repositoryItemImageComboBox1.Items1"))), ((int)(resources.GetObject("repositoryItemImageComboBox1.Items2")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("repositoryItemImageComboBox1.Items3"), ((object)(resources.GetObject("repositoryItemImageComboBox1.Items4"))), ((int)(resources.GetObject("repositoryItemImageComboBox1.Items5")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("repositoryItemImageComboBox1.Items6"), ((object)(resources.GetObject("repositoryItemImageComboBox1.Items7"))), ((int)(resources.GetObject("repositoryItemImageComboBox1.Items8"))))});
            this.repositoryItemImageComboBox1.Name = "repositoryItemImageComboBox1";
            // 
            // picLogo
            // 
            resources.ApplyResources(this.picLogo, "picLogo");
            this.picLogo.Name = "picLogo";
            this.picLogo.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Stretch;
            // 
            // lblFilter
            // 
            resources.ApplyResources(this.lblFilter, "lblFilter");
            this.lblFilter.Name = "lblFilter";
            this.lblFilter.Properties.Appearance.Options.UseTextOptions = true;
            this.lblFilter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            // 
            // lblDateFilter
            // 
            resources.ApplyResources(this.lblDateFilter, "lblDateFilter");
            this.lblDateFilter.Name = "lblDateFilter";
            this.lblDateFilter.Properties.Appearance.Options.UseTextOptions = true;
            this.lblDateFilter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            // 
            // lblReportName
            // 
            resources.ApplyResources(this.lblReportName, "lblReportName");
            this.lblReportName.Name = "lblReportName";
            this.lblReportName.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("lblReportName.Properties.Appearance.Font")));
            this.lblReportName.Properties.Appearance.Options.UseFont = true;
            this.lblReportName.Properties.Appearance.Options.UseTextOptions = true;
            this.lblReportName.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            // 
            // Report
            // 
            resources.ApplyResources(this.Report, "Report");
            this.Report.Name = "Report";
            this.Report.SelectedTabPage = this.xtraTabPage1;
            this.Report.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.xtraTabPage1,
            this.xtraTabPage2,
            this.xtraTabPage3});
            // 
            // xtraTabPage1
            // 
            this.xtraTabPage1.Controls.Add(this.grd_data);
            this.xtraTabPage1.Name = "xtraTabPage1";
            resources.ApplyResources(this.xtraTabPage1, "xtraTabPage1");
            // 
            // xtraTabPage2
            // 
            this.xtraTabPage2.Controls.Add(this.ch_Delegates);
            this.xtraTabPage2.Name = "xtraTabPage2";
            this.xtraTabPage2.PageVisible = false;
            resources.ApplyResources(this.xtraTabPage2, "xtraTabPage2");
            // 
            // ch_Delegates
            // 
            resources.ApplyResources(this.ch_Delegates, "ch_Delegates");
            this.ch_Delegates.Name = "ch_Delegates";
            series1.CrosshairLabelPattern = "Delegates";
            series1.Points.AddRange(new DevExpress.XtraCharts.SeriesPoint[] {
            seriesPoint1,
            seriesPoint2,
            seriesPoint3,
            seriesPoint4,
            seriesPoint5});
            series1.View = pieSeriesView1;
            this.ch_Delegates.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series1};
            // 
            // xtraTabPage3
            // 
            this.xtraTabPage3.Controls.Add(this.ch_Report);
            this.xtraTabPage3.Name = "xtraTabPage3";
            resources.ApplyResources(this.xtraTabPage3, "xtraTabPage3");
            // 
            // ch_Report
            // 
            resources.ApplyResources(this.ch_Report, "ch_Report");
            this.ch_Report.Name = "ch_Report";
            this.ch_Report.SeriesSerializable = new DevExpress.XtraCharts.Series[0];
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowMoveBarOnToolbar = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtn_Preview,
            this.barBtn_Help,
            this.barBtnClose,
            this.barBtn_PreviewData,
            this.barButtonItem1,
            this.barButtonItem2,
            this.barButtonItem3,
            this.barButtonItem4,
            this.barSubItem1,
            this.barButtonItem5,
            this.barButtonItem6,
            this.barButtonItem7});
            this.barManager1.MaxItemId = 41;
            // 
            // bar1
            // 
            this.bar1.BarAppearance.Normal.Options.UseTextOptions = true;
            this.bar1.BarAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bar1.BarItemHorzIndent = 9;
            this.bar1.BarItemVertIndent = 0;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(61, 172);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barBtn_PreviewData, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barSubItem1, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.DrawSizeGrip = true;
            this.bar1.OptionsBar.MultiLine = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtn_PreviewData
            // 
            this.barBtn_PreviewData.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtn_PreviewData, "barBtn_PreviewData");
            this.barBtn_PreviewData.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barBtn_PreviewData.Id = 31;
            this.barBtn_PreviewData.Name = "barBtn_PreviewData";
            this.barBtn_PreviewData.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_PreviewData_ItemClick);
            // 
            // barSubItem1
            // 
            this.barSubItem1.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barSubItem1, "barSubItem1");
            this.barSubItem1.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barSubItem1.Id = 37;
            this.barSubItem1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem5),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem6),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem7)});
            this.barSubItem1.Name = "barSubItem1";
            // 
            // barButtonItem5
            // 
            resources.ApplyResources(this.barButtonItem5, "barButtonItem5");
            this.barButtonItem5.Id = 38;
            this.barButtonItem5.Name = "barButtonItem5";
            this.barButtonItem5.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem5_ItemClick);
            // 
            // barButtonItem6
            // 
            resources.ApplyResources(this.barButtonItem6, "barButtonItem6");
            this.barButtonItem6.Id = 39;
            this.barButtonItem6.Name = "barButtonItem6";
            this.barButtonItem6.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem6_ItemClick);
            // 
            // barButtonItem7
            // 
            resources.ApplyResources(this.barButtonItem7, "barButtonItem7");
            this.barButtonItem7.Id = 40;
            this.barButtonItem7.Name = "barButtonItem7";
            this.barButtonItem7.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem7_ItemClick);
            // 
            // barBtnClose
            // 
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 28;
            this.barBtnClose.ItemAppearance.Normal.Font = ((System.Drawing.Font)(resources.GetObject("barBtnClose.ItemAppearance.Normal.Font")));
            this.barBtnClose.ItemAppearance.Normal.Options.UseFont = true;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnClose_ItemClick);
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.Appearance.Options.UseTextOptions = true;
            this.barDockControlTop.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barDockControlTop.CausesValidation = false;
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            // 
            // barBtn_Preview
            // 
            this.barBtn_Preview.ActAsDropDown = true;
            this.barBtn_Preview.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtn_Preview.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.DropDown;
            resources.ApplyResources(this.barBtn_Preview, "barBtn_Preview");
            this.barBtn_Preview.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barBtn_Preview.Id = 1;
            this.barBtn_Preview.ItemAppearance.Normal.Font = ((System.Drawing.Font)(resources.GetObject("barBtn_Preview.ItemAppearance.Normal.Font")));
            this.barBtn_Preview.ItemAppearance.Normal.Options.UseFont = true;
            this.barBtn_Preview.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.O));
            this.barBtn_Preview.Name = "barBtn_Preview";
            this.barBtn_Preview.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            // 
            // barBtn_Help
            // 
            this.barBtn_Help.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtn_Help, "barBtn_Help");
            this.barBtn_Help.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtn_Help.Id = 2;
            this.barBtn_Help.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtn_Help.Name = "barBtn_Help";
            this.barBtn_Help.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            // 
            // barButtonItem1
            // 
            resources.ApplyResources(this.barButtonItem1, "barButtonItem1");
            this.barButtonItem1.Id = 33;
            this.barButtonItem1.Name = "barButtonItem1";
            // 
            // barButtonItem2
            // 
            resources.ApplyResources(this.barButtonItem2, "barButtonItem2");
            this.barButtonItem2.Id = 34;
            this.barButtonItem2.Name = "barButtonItem2";
            // 
            // barButtonItem3
            // 
            resources.ApplyResources(this.barButtonItem3, "barButtonItem3");
            this.barButtonItem3.Id = 35;
            this.barButtonItem3.Name = "barButtonItem3";
            // 
            // barButtonItem4
            // 
            resources.ApplyResources(this.barButtonItem4, "barButtonItem4");
            this.barButtonItem4.Id = 36;
            this.barButtonItem4.Name = "barButtonItem4";
            // 
            // bar2
            // 
            this.bar2.BarAppearance.Normal.Options.UseTextOptions = true;
            this.bar2.BarAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bar2.BarItemHorzIndent = 9;
            this.bar2.BarItemVertIndent = 0;
            this.bar2.BarName = "Tools";
            this.bar2.DockCol = 0;
            this.bar2.DockRow = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar2.FloatLocation = new System.Drawing.Point(61, 172);
            this.bar2.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barBtn_PreviewData, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_Preview),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar2.OptionsBar.AllowQuickCustomization = false;
            this.bar2.OptionsBar.DisableCustomization = true;
            this.bar2.OptionsBar.DrawSizeGrip = true;
            this.bar2.OptionsBar.MultiLine = true;
            this.bar2.OptionsBar.UseWholeRow = true;
            // 
            // frm_SL_DeliveryOfficialsSales
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.Report);
            this.Controls.Add(this.picLogo);
            this.Controls.Add(this.lblFilter);
            this.Controls.Add(this.lblDateFilter);
            this.Controls.Add(this.lblReportName);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.Name = "frm_SL_DeliveryOfficialsSales";
            this.Load += new System.EventHandler(this.frm_SL_DeliveryOfficialsSales_Load);
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grd_data)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_PayMethod)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemImageComboBox1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFilter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDateFilter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblReportName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Report)).EndInit();
            this.Report.ResumeLayout(false);
            this.xtraTabPage1.ResumeLayout(false);
            this.xtraTabPage2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(pieSeriesView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ch_Delegates)).EndInit();
            this.xtraTabPage3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.ch_Report)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.PictureEdit picLogo;
        private DevExpress.XtraEditors.TextEdit lblFilter;
        private DevExpress.XtraEditors.TextEdit lblDateFilter;
        private DevExpress.XtraEditors.TextEdit lblReportName;
        private DevExpress.XtraTab.XtraTabControl Report;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage1;
        private DevExpress.XtraGrid.GridControl grd_data;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn col_OutTrnsDate;
        private DevExpress.XtraGrid.Columns.GridColumn col_OutTrnsCode;
        private DevExpress.XtraGrid.Columns.GridColumn col_CusNameAr;
        private DevExpress.XtraGrid.Columns.GridColumn col_Qtyd;
        private DevExpress.XtraGrid.Columns.GridColumn col_TotalSellPrice;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn col_Delegate;
        private DevExpress.XtraGrid.Columns.GridColumn col_ItemNameAr;
        private DevExpress.XtraGrid.Columns.GridColumn col_Category;
        private DevExpress.XtraGrid.Columns.GridColumn col_Qty;
        private DevExpress.XtraGrid.Columns.GridColumn col_UOM;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage2;
        private DevExpress.XtraCharts.ChartControl ch_Delegates;
        private DevExpress.XtraTab.XtraTabPage xtraTabPage3;
        private DevExpress.XtraCharts.ChartControl ch_Report;
        private DevExpress.XtraEditors.Repository.RepositoryItemImageComboBox rep_PayMethod;
        private DevExpress.XtraEditors.Repository.RepositoryItemImageComboBox repositoryItemImageComboBox1;
        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtn_PreviewData;
        private DevExpress.XtraBars.BarButtonItem barBtn_Preview;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtn_Help;
        private DevExpress.XtraBars.BarButtonItem barButtonItem1;
        private DevExpress.XtraBars.BarButtonItem barButtonItem2;
        private DevExpress.XtraBars.BarButtonItem barButtonItem3;
        private DevExpress.XtraBars.BarButtonItem barButtonItem4;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraBars.BarSubItem barSubItem1;
        private DevExpress.XtraBars.BarButtonItem barButtonItem5;
        private DevExpress.XtraBars.BarButtonItem barButtonItem6;
        private DevExpress.XtraBars.BarButtonItem barButtonItem7;
    }
}