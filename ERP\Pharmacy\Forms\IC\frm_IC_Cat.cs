﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;
using DAL.Res;
using System.Linq;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Columns;
using Reports;
using DevExpress.XtraReports.UI;

namespace Pharmacy.Forms
{
    public partial class frm_IC_Category : DevExpress.XtraEditors.XtraForm
    {
        int catId;
        List<IC_Category> lstGrps = new List<IC_Category>();

        bool DataModified;
        FormAction action = FormAction.None;
        UserPriv prvlg;

        public frm_IC_Category()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(barManager1);
        }

        private void frm_SL_CustomerGroup_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            //check customers Account

            LoadPrivilege();
            LoadGrpTree();

            if (!Shared.ItemsPostingAvailable)
                barBtnPostingAccs.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;

            lkp_Account.Properties.DataSource = HelperAcc.LoadAccountsTree(HelperAcc.ExpensesAcc, false);
            lkp_Account.Properties.ValueMember = "AccId";
            lkp_Account.Properties.DisplayMember = "AccName";

        }

        private void frm_SL_CustomerGroup_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                e.Cancel = true;
        }

        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadGrpTree();
        }

        private void barBtn_Delete_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (action == FormAction.Add)
                return;


            if (catId > 0)
            {
                DAL.ERPDataContext DB = new DAL.ERPDataContext();

                var cat = (from d in DB.IC_Categories
                           where d.CategoryId == catId
                           select d).SingleOrDefault();

                if (cat.CatNumber == "1")
                    return;

                if (XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgDelCat : ResICAr.MsgDelCat,
                    Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2)
                    == DialogResult.Yes)
                {


                    var childCats = (from a in DB.IC_Categories
                                     where a.ParentId == cat.CategoryId
                                     select a.CategoryId).Count();

                    if (childCats > 0)
                        return;

                    var items = (from a in DB.IC_Items
                                 where a.Category == cat.CategoryId
                                 select a.ItemId).Count();

                    if (items > 0)
                    {
                        XtraMessageBox.Show(
                            Shared.IsEnglish == true ? ResICEn.MsgDelCatDenied : ResICAr.MsgDelCatDenied,//"عفواً، يوجد أصناف مرتبطة بهذه الفئة، لا يمكن حذف الفئة"
                            Shared.IsEnglish == true ? ResEn.MsgTInfo : ResAr.MsgTInfo,
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    DB.IC_Categories.DeleteOnSubmit(cat);

                    MyHelper.UpdateST_UserLog(DB, cat.CatNumber, cat.CategoryNameAr,
                        (int)FormAction.Delete, (int)FormsNames.Category);

                    DB.SubmitChanges();
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.MsgDel : ResAr.MsgDel
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    TreeNode parentNode = treeView1.Nodes.Find(catId.ToString(), true).First().Parent;
                    LoadGrpTree();
                    treeView1.Focus();

                    treeView1.SelectedNode = treeView1.Nodes.Find(parentNode.Name.ToString(), true).First();
                }
            }
        }

        private void barBtnNew_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            NewCustGroup();
        }

        private void NewCustGroup()
        {

            if (Shared.user.AccessType == (byte)AccessType.Admin)
            {
                txtCode.Enabled = true;
                txtCode2.Enabled = true;
            }



            ERPDataContext DB = new ERPDataContext();

            if (DB.IC_Items.Where(x => x.Category == Convert.ToInt32(treeView1.SelectedNode.Name)).Count() > 0)
            {
                XtraMessageBox.Show(Shared.IsEnglish ? ResICEn.subCat : ResICAr.subCat,
                  Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            Reset();
            action = FormAction.Add;

            txtName.Focus();

            lkp_ParentGrpId.EditValue = Convert.ToInt32(treeView1.SelectedNode.Name);
        }

        private void barBtn_Save_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (!ValidData())
                return;

            SaveData();
        }


        void Reset()
        {
            txtName.Text = string.Empty;
            txtFName.Text = string.Empty;
            txtCode.Text = string.Empty;
            txtCode2.Text = string.Empty;

            DataModified = false;

            DoValidate();
        }

        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                prvlg = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.Category).FirstOrDefault();

                if (!prvlg.CanDel)
                    barBtnDelete.Enabled = false;
                if (!prvlg.CanAdd)
                    barBtnNew.Enabled = false;
                if (!prvlg.CanPrint)
                    barBtnPrint.Enabled = false;
            }
        }

        DialogResult ChangesMade()
        {
            if (DataModified)
            {
                DialogResult r = XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResEn.MsgDataModified : ResAr.MsgDataModified,
                    Shared.IsEnglish == true ? ResEn.MsgTQues : ResAr.MsgTQues,
                    MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                if (r == DialogResult.Yes)
                {
                    barBtnSave.PerformClick();
                    return r;
                }
                else if (r == DialogResult.No)
                {
                    // no thing made, continue closing or do next or do previous
                    return r;
                }
                else if (r == DialogResult.Cancel)
                {
                    //cancel form action
                    return r;
                }
            }
            return DialogResult.No;
        }

        private void SaveData()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            if (action == FormAction.Add)
            {
                //get ParentAccID
                var parent = DB.IC_Categories.Where(x => x.CategoryId == Convert.ToInt32(lkp_ParentGrpId.EditValue)).First();

                //create group
                #region CatNumber
                IC_Category d = new IC_Category();
                d.CatNumber = MyHelper.CatNumGenerated(parent);
                var code_exist = DB.IC_Categories.Where(c => c.CatNumber.Trim() == txtCode.Text.Trim()).Count();
                if (code_exist > 0)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgCodeExist : ResSLAr.MsgCodeExist,
                        Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtName.Focus();
                    return;
                }
                #endregion
                #region Code2
                d.Code2 = string.IsNullOrEmpty(parent.Code2) ? null : MyHelper.CatNumGenerated(parent, 1);




                if (!string.IsNullOrEmpty(txtCode2.Text))
                {

                    var code2_exist = DB.IC_Categories.Where(c => c.Code2.Trim() == txtCode2.Text.Trim()).Count();
                    if (code2_exist > 0)
                    {
                        XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgCodeExist : ResSLAr.MsgCodeExist,
                            Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtCode2.Focus();
                        return;
                    }

                    if (checkCode(d, txtCode.Text, true) == false) return;

                    d.Code2 = txtCode2.Text;

                }
                #endregion
                d.CategoryNameAr = txtName.Text.Trim();
                d.CategoryNameEn = txtFName.Text.Trim();

                d.ParentId = parent.CategoryId;
                d.Level = parent.Level + 1;

                if (d.CatNumber == "-1")
                {
                    XtraMessageBox.Show(Shared.IsEnglish == true ? ResAccEn.MsgAccLevel : ResAccAr.MsgAccLevel
                , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                d.ExpensesAccount = lkp_Account.EditValue != null ? Convert.ToInt32(lkp_Account.EditValue) : (int?)null;

                DB.IC_Categories.InsertOnSubmit(d);

                MyHelper.UpdateST_UserLog(DB, d.CatNumber, d.CategoryNameAr,
                    (int)FormAction.Add, (int)FormsNames.Category);


                //update to enable admin creating category number
                if (txtCode.Text != "")
                {
                    if (checkCode(d, txtCode.Text, true) == false) return;

                    d.CatNumber = txtCode.Text;

                }
                if (txtCode2.Text != "")
                {
                    if (checkCode2(d, txtCode2.Text, true) == false) return;

                    d.Code2 = txtCode2.Text;

                }


                DB.SubmitChanges();

                catId = d.CategoryId;
            }
            else if (action == FormAction.Edit)
            {
                var d = (from i in DB.IC_Categories
                         where i.CategoryId == catId
                         select i).FirstOrDefault();

                d.CategoryNameAr = txtName.Text.Trim();
                d.CategoryNameEn = txtFName.Text.Trim();

                MyHelper.UpdateST_UserLog(DB, d.CatNumber, d.CategoryNameAr,
                    (int)FormAction.Edit, (int)FormsNames.Category);

                //update category number by admin
                var catSons = DB.IC_Categories.Where(c => c.ParentId == catId);
                if (Shared.user.AccessType == (byte)AccessType.Admin && catSons.Count() > 0 /*&& txtCode.Text != d.CatNumber*/)
                {
                    var result = MessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgChangeSubCode : ResSLAr.MsgChangeSubCode,
                  Shared.IsEnglish ? ResSLEn.MsgTQues : ResSLAr.MsgTQues, MessageBoxButtons.YesNo);
                    if (result == DialogResult.Yes)
                    {
                        test(catId, txtCode.Text, txtCode.Text, true);
                        test(catId, txtCode.Text, txtCode.Text, true, 1);

                    }
                    else
                    {
                        test(catId, txtCode.Text, txtCode.Text, false);
                        test(catId, txtCode.Text, txtCode.Text, false, 1);
                    }
                }
                else
                {
                    if (checkCode(d, txtCode.Text, true) == false)
                        return;
                    d.CatNumber = txtCode.Text;
                    d.Code2 = txtCode2.Text;
                    DB.SubmitChanges();
                }
                d.ExpensesAccount = lkp_Account.EditValue != null ? Convert.ToInt32(lkp_Account.EditValue) : (int?)null;

                #region update  

                //var catSons = DB.IC_Categories.Where(c => c.ParentId == catId);
                //var number = DB.IC_Categories.SingleOrDefault(c => c.CategoryId == catId)/*.CatNumber*/;

                ////if (Shared.user.AccessType == (byte)AccessType.Admin)
                ////{
                ////if (catSons.Count() > 0&&txtCode.Text!=d.CatNumber)
                ////{

                //if (Shared.user.AccessType == (byte)AccessType.Admin && catSons.Count() > 0 && txtCode.Text != d.CatNumber)
                //{
                //    var result = MessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgChangeSubCode : ResSLAr.MsgChangeSubCode,
                //  Shared.IsEnglish ? ResSLEn.MsgTQues : ResSLAr.MsgTQues, MessageBoxButtons.YesNo);
                //    if (result == DialogResult.Yes)
                //    {
                //        var counter = 1;
                //        foreach (var item in catSons)
                //        {

                //            string NewNumber = string.Empty;
                //            int level = number == null ? 1 : number.Level;
                //            for (int x = 0; x < level; x++)
                //                NewNumber += "0";
                //            item.CatNumber = txtCode.Text + NewNumber + counter;
                //            counter++;
                //        }
                //        // d.CatNumber = txtCode.Text;



                //    }
                //    else
                //    {
                //        // d.CatNumber = txtCode.Text;
                //        //DB.SubmitChanges();

                //        //return;
                //    }
                //}
                #endregion
                DB.SubmitChanges();

            }
            XtraMessageBox.Show(
                Shared.IsEnglish == true ? ResEn.MsgSave : ResAr.MsgSave
                , "", MessageBoxButtons.OK, MessageBoxIcon.Information);

            DataModified = false;
            DoValidate();
            action = FormAction.Edit;


            treeView1.Focus();
            LoadGrpTree();

            treeView1.SelectedNode = treeView1.Nodes.Find(catId.ToString(), true).First();

        }

        private bool ValidData()
        {
            if (action == FormAction.Add)
            {
                if (prvlg != null && !prvlg.CanAdd)
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.MsgPrvNew : ResAr.MsgPrvNew,
                        "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            if (action == FormAction.Edit)
            {
                if (prvlg != null && !prvlg.CanEdit)
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.MsgPrvEdit : ResAr.MsgPrvEdit
                        ,
                        "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }


            if (Validate__ArName() == false)
                return false;
            //update code when admin insert
            // if (txtCode.Text != "")
            if (Validate__Code() == false)
                return false;
            if (Validate__Code2() == false)
                return false;




            return true;
        }

        private void barBtnHelp_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "الخزانات");
        }

        private void txtDrawerNameAr_Modified(object sender, EventArgs e)
        {
            DataModified = true;
        }

        void DoValidate()
        {
            txtName.DoValidate();
            txtFName.DoValidate();
        }

        private bool Validate__ArName()
        {
            try
            {
                DAL.ERPDataContext pharm = new DAL.ERPDataContext();

                if (string.IsNullOrEmpty(txtName.Text))
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResEn.MsgNameRequired : ResAr.MsgNameRequired,
                        Shared.IsEnglish ? ResSLEn.MsgTWarn : ResAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtName.Focus();
                    return false;
                }

                if (action == FormAction.Add)
                {
                    var name = (from n in pharm.IC_Categories
                                where n.CategoryNameAr == txtName.Text.Trim()
                                select n.CategoryNameAr).Count();
                    if (name > 0)
                    {
                        XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgNameExist : ResSLAr.MsgNameExist,
                            Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtName.Focus();
                        return false;
                    }
                }

                //update 10/9/2017
                #region new
                if (action == FormAction.Edit)
                {
                    var cat = (from n in pharm.IC_Categories
                               where n.CategoryNameAr == txtName.Text.Trim()
                               && n.CategoryId != catId
                               select n.CategoryNameAr).Count();
                    if (cat > 0)
                    {
                        XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgNameExist : ResSLAr.MsgNameExist,
                            Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtCode.Focus();
                        return false;
                    }
                    else
                        return true;
                }

                #endregion

            }
            catch
            {
                XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgIncorrectData : ResSLAr.MsgIncorrectData,
                    Shared.IsEnglish ? ResSLEn.MsgTError : ResSLAr.MsgTError, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }
            return true;
        }

        private void LoadGrpTree()
        {
            lstGrps = new DAL.ERPDataContext().IC_Categories.OrderBy(x => x.CatNumber).Select(x => x)/*.OrderBy(x=>x.CategoryId)*/.ToList();
            treeView1.Nodes.Clear();
            foreach (var c in lstGrps)
            {
                if (c.ParentId == null)
                    treeView1.Nodes.Add(c.CategoryId.ToString(), Shared.IsEnglish ? c.CategoryNameEn : c.CategoryNameAr);
                else
                {
                    TreeNode node = treeView1.Nodes.Find(c.ParentId.Value.ToString(), true).First();
                    if (node.Nodes[c.CategoryId.ToString()] == null)
                        node.Nodes.Add(c.CategoryId.ToString(), Shared.IsEnglish ? c.CategoryNameEn : c.CategoryNameAr);
                }
            }
            treeView1.Nodes[0].Expand();


            lkp_ParentGrpId.Properties.DataSource = lstGrps;
            lkp_ParentGrpId.Properties.ValueMember = "CategoryId";
            lkp_ParentGrpId.Properties.DisplayMember = Shared.IsEnglish ? "CategoryNameEn" : "CategoryNameAr";
        }

        private void treeView1_AfterSelect(object sender, TreeViewEventArgs e)
        {
            if (Shared.user.AccessType == (byte)AccessType.Admin)
            {
                txtCode.Enabled = true;
                txtCode2.Enabled = true;
            }


            if (ChangesMade() == DialogResult.Cancel)
                return;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            if (e.Node.Name != "0")
            {
                var d = (from i in DB.IC_Categories
                         where i.CategoryId == Convert.ToInt32(e.Node.Name)
                         select i).Single();

                catId = d.CategoryId;

                txtCode.Text = d.CatNumber;
                txtCode2.Text = d.Code2;
                txtName.Text = d.CategoryNameAr;
                txtFName.Text = d.CategoryNameEn;
                lkp_ParentGrpId.EditValue = d.ParentId;
                lkp_Account.EditValue = d.ExpensesAccount;

                action = FormAction.Edit;
            }
            else
            {
                catId = 0;
                Reset();
                action = FormAction.Add;
            }

            DataModified = false;
            DoValidate();
        }

        private void barBtnPostingAccs_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (Shared.StockIsPeriodic)
                new frm_IC_CatPosting_Periodic().ShowDialog();
            else
                new frm_IC_CatPosting().ShowDialog();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            DataTable dt = new DataTable();

            dt.Columns.Add("serial");
            dt.Columns.Add("code");
            dt.Columns.Add("name");

            int serial = 1;
            foreach (var a in lstGrps)
            {
                dt.Rows.Add(serial, a.CatNumber, a.CategoryNameAr);
                serial++;
            }

            rpt_Sub_Tree r = new rpt_Sub_Tree(dt);
            new Reports.rpt_Template(this.Text, "", "", r).ShowPreview();
        }

        private bool Validate__Code()
        {
            try
            {
                DAL.ERPDataContext DB = new DAL.ERPDataContext();


                if (action == FormAction.Add)
                {

                    int cat;
                    if (txtCode.Text == "")
                    {
                        var parent = DB.IC_Categories.Where(x => x.CategoryId == Convert.ToInt32(lkp_ParentGrpId.EditValue)).First();
                        var code = MyHelper.CatNumGenerated(parent);

                        cat = (from n in DB.IC_Categories
                               where n.CatNumber == code
                               select n.CatNumber).Count();
                    }
                    else
                    {
                        cat = (from n in DB.IC_Categories
                               where n.CatNumber == txtCode.Text.Trim()
                               select n.CatNumber).Count();
                    }
                    if (cat > 0)
                    {
                        XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgCodeExist : ResSLAr.MsgCodeExist,
                            Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtCode.Focus();
                        return false;
                    }
                }


                if (action == FormAction.Edit)
                {
                    var cat = (from n in DB.IC_Categories
                               where n.CatNumber == txtCode.Text.Trim()
                               && n.CategoryId != catId
                               select n.CatNumber).Count();
                    if (cat > 0)
                    {
                        XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgCodeExist : ResSLAr.MsgCodeExist,
                            Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtCode.Focus();
                        return false;
                    }
                    else
                        return true;
                }


            }
            catch
            {
                XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgIncorrectData : ResSLAr.MsgIncorrectData,
                    Shared.IsEnglish ? ResSLEn.MsgTError : ResSLAr.MsgTError, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }
            return true;
        }

        private bool Validate__Code2()
        {
            try
            {
                DAL.ERPDataContext DB = new DAL.ERPDataContext();


                if (action == FormAction.Add)
                {

                    int cat;
                    if (!string.IsNullOrEmpty(txtCode2.Text))
                    {
                        cat = (from n in DB.IC_Categories
                               where n.Code2 == txtCode2.Text.Trim()
                               select n.Code2).Count();
                        if (cat > 0)
                        {
                            XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgCodeExist : ResSLAr.MsgCodeExist,
                                Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            txtCode2.Focus();
                            return false;
                        }
                    }
                }


                if (action == FormAction.Edit)
                {
                    var cat = (from n in DB.IC_Categories
                               where n.Code2 == txtCode2.Text.Trim()
                               && n.CategoryId != catId
                               select n.Code2).Count();
                    if (cat > 0)
                    {
                        XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgCodeExist : ResSLAr.MsgCodeExist,
                            Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtCode2.Focus();
                        return false;
                    }
                    else
                        return true;
                }


            }
            catch
            {
                XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgIncorrectData : ResSLAr.MsgIncorrectData,
                    Shared.IsEnglish ? ResSLEn.MsgTError : ResSLAr.MsgTError, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }
            return true;
        }

        private void test(int catId, string txtCode, string code, bool change, params byte[] iscode2)
        {
            ERPDataContext DB = new ERPDataContext();
            var d = (from i in DB.IC_Categories
                     where i.CategoryId == catId
                     select i).FirstOrDefault();


            d.CatNumber = txtCode;
            var catSons = DB.IC_Categories.Where(c => c.ParentId == catId);
            //var number = DB.IC_Categories.SingleOrDefault(c => c.CategoryId == catId)/*.CatNumber*/;
            string catNum;

            if (change == true)
            {
                if (checkCode2(d, txtCode, false) == false && iscode2.Count() > 0)
                {
                    return;
                }
                else if (checkCode(d, txtCode, false) == false)
                    return;
                var counter = 1;
                foreach (var item in catSons)
                {
                    string NewNumber = string.Empty;
                    int level = d == null ? 1 : d.Level;
                    for (int x = 0; x < level; x++)
                        NewNumber += "0";
                    catNum = txtCode + NewNumber + counter;
                    if (iscode2.Count() > 0)
                        item.Code2 = catNum;
                    else
                        item.CatNumber = catNum;
                    DB.SubmitChanges();

                    if (DB.IC_Categories.Where(c => c.ParentId == item.CategoryId).Count() > 0)
                    {
                        test(item.CategoryId, catNum, code, true, iscode2);
                    }
                    else
                    {
                        //return;
                        counter++;
                        continue;
                    }
                    counter++;
                }
                d.CatNumber = txtCode;

                DB.SubmitChanges();
            }
            else
            {
                if (checkCode(d, txtCode, true) == false)
                    return;

                d.CatNumber = txtCode;

            }
            DB.SubmitChanges();

        }

        private bool checkCode(IC_Category d, string txtCode, bool chckChild)
        {
            ERPDataContext DB = new ERPDataContext();
            if (d.ParentId != null)
            {
                var parentCode = DB.IC_Categories.SingleOrDefault(c => c.CategoryId == d.ParentId).CatNumber;
                // var t= d.CatNumber.CompareTo(parentCode) ;
                if (txtCode.CompareTo(parentCode) < 0)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.msgCheckSubCatCode : ResSLAr.msgCheckSubCatCode,
                   Shared.IsEnglish ? ResSLEn.MsgTError : ResSLAr.MsgTError, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }

            }
            if (chckChild == true)
            {
                var subCat = DB.IC_Categories.Where(c => c.ParentId == d.CategoryId).ToList();
                foreach (var item in subCat)
                {
                    // var subCode = DB.IC_Categories.SingleOrDefault(c => c.ParentId == item.CatNumber).CatNumber;
                    if (txtCode.CompareTo(item.CatNumber) > 0)
                    {
                        XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.msgCheckSubCatCode : ResSLAr.msgCheckSubCatCode,
                        Shared.IsEnglish ? ResSLEn.MsgTError : ResSLAr.MsgTError, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return false;
                    }
                }
            }
            return true;
        }



        private bool checkCode2(IC_Category d, string txtCode, bool chckChild)
        {
            ERPDataContext DB = new ERPDataContext();
            if (d.ParentId != null)
            {
                var parentCode = DB.IC_Categories.SingleOrDefault(c => c.CategoryId == d.ParentId).Code2;
                if (string.IsNullOrEmpty(parentCode)) return true;
                // var t= d.CatNumber.CompareTo(parentCode) ;
                if (txtCode.CompareTo(parentCode) < 0)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.msgCheckSubCatCode : ResSLAr.msgCheckSubCatCode,
                   Shared.IsEnglish ? ResSLEn.MsgTError : ResSLAr.MsgTError, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }

            }
            if (chckChild == true)
            {
                var subCat = DB.IC_Categories.Where(c => c.ParentId == d.CategoryId).ToList();
                foreach (var item in subCat)
                {
                    // var subCode = DB.IC_Categories.SingleOrDefault(c => c.ParentId == item.CatNumber).CatNumber;
                    if (txtCode.CompareTo(item.Code2) > 0)
                    {
                        XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.msgCheckSubCatCode : ResSLAr.msgCheckSubCatCode,
                        Shared.IsEnglish ? ResSLEn.MsgTError : ResSLAr.MsgTError, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return false;
                    }
                }
            }
            return true;
        }


    }
}