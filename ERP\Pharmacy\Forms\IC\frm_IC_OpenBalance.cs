﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraPrinting;
using System.Data.OleDb;
using DAL;
using DevExpress.XtraGrid.Columns;

namespace Pharmacy.Forms
{
    public partial class frm_IC_OpenBalance : DevExpress.XtraEditors.XtraForm
    {
        decimal totalPurchase = 0;        
        decimal totalDiscount = 0;

        DAL.ERPDataContext DB;
        List<DAL.IC_UOM> uom_list;
        DataTable dtOpenBalance = new DataTable();
        DataTable dtUOM = new DataTable();
        DataTable dtStores = new DataTable();        

        bool DataModified;

        public frm_IC_OpenBalance()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
        }

        private void frm_IC_OpenBalance_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);            

            grdOpenBalance.ProcessGridKey += new KeyEventHandler(grid_ProcessGridKey);
            ErpUtils.Allow_Incremental_Search(repItems);

            if (frmMain.st_Store.UseContainsToSearchItems)
                repItems.PopupFilterMode = PopupFilterMode.Contains;
            else
                repItems.PopupFilterMode = PopupFilterMode.Default;

            repItems.View.Columns["ItemNameAr"].OptionsFilter.AutoFilterCondition = AutoFilterCondition.Contains;
            repItems.View.Columns["ItemNameEn"].OptionsFilter.AutoFilterCondition = AutoFilterCondition.Contains;
            repItems.View.Columns["ItemCode1"].SortIndex = 0;

            DB = new ERPDataContext();
            uom_list = DB.IC_UOMs.ToList();
            
            #region Expire
            if (frmMain.st_Store.ExpireDate == true)
                col_Expire.Visible = true;
            else
                col_Expire.Visible = false;

            if (frmMain.st_Store.Batch)
                col_Batch.Visible = true;
            else
                col_Batch.Visible = false;

            #endregion

            BindDataSources();

            ErpUtils.Load_Grid_Layout(grdOpenBalance, this.Name.Replace("frm_", ""));
            ErpUtils.ColumnChooser(grdOpenBalance);
        }

        private void frm_IC_OpenBalance_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Insert)
            {                
                grdOpenBalance.Focus();
                var view = (grdOpenBalance.FocusedView as DevExpress.XtraGrid.Views.Grid.GridView);
                view.AddNewRow();
                view.FocusedColumn = view.Columns[frmMain.st_Store.InvoicesUseSearchItems ? "ItemId" : "ItemCode1"];//mahmoud
            }
            if (e.KeyCode == Keys.Home && e.Modifiers == Keys.Control)
            {
                lkpStore.Focus();
            }

            if (e.KeyCode == Keys.O && e.Control == true)
            {
                OpenFileDialog ofd = new OpenFileDialog();
                ofd.Filter = "Excel File(*.xls)|*.xls|Excel File(*.xlsx)|*.xlsx";
                if (ofd.ShowDialog() == DialogResult.OK)
                {
                    DataTable dt_Emp_Attendance = exceldata(ofd.FileName);
                    if (dt_Emp_Attendance != null)
                    {
                        Load_ItemsOnOpenBalances(dt_Emp_Attendance);
                    }
                    else
                        return;
                }
                else
                    return;            
            }
            if (e.KeyCode == Keys.D && e.Control == true && e.Shift == true)
            {
                dtOpenBalance.Rows.Clear();
            }
        }

        private void Load_ItemsOnOpenBalances(DataTable dt_Emp_Attendance)
        {
            foreach (DataRow dr in dt_Emp_Attendance.Rows)
            {
                if (dr["Code2"] == DBNull.Value || dr["Code2"].ToString() == string.Empty
                    || dr["ItemName"] == DBNull.Value || dr["ItemName"].ToString() == string.Empty)
                    continue;

                if ((dr["Pprice"] == DBNull.Value || dr["Pprice"].ToString() == string.Empty)
                    && (dr["Sprice"] == DBNull.Value || dr["Sprice"].ToString() == string.Empty))
                    continue;                
                
                var item = DB.IC_Items.Where(x => x.ItemCode2 == dr["Code2"].ToString()).FirstOrDefault();
                if (item != null)
                {
                    if (dr["Pprice"] != DBNull.Value && dr["Pprice"].ToString() != string.Empty)
                        item.PurchasePrice = Convert.ToDecimal(dr["Pprice"]);
                    else
                        item.PurchasePrice = 0;

                    if (dr["Sprice"] != DBNull.Value && dr["Sprice"].ToString() != string.Empty)
                        item.SmallUOMPrice = Convert.ToDecimal(dr["Sprice"]);
                    else
                        item.SmallUOMPrice = 0;
                    DB.SubmitChanges();
                }
                if (item == null)
                {
                    item = new IC_Item();
                    item.Category = 1;
                    item.ChangePriceMethod = 1;
                    item.ChangeSellPrice = true;
                    item.Company = 1;
                    item.Description = "";
                    item.ItemType = (int)ItemType.Inventory;
                    item.IsDeleted = false;
                    item.IsExpire = false;
                    item.ItemCode1 = DB.IC_Items.Select(x => x.ItemCode1).ToList().Max() + 1;
                    item.ItemCode2 = dr["Code2"].ToString();
                    item.ItemNameAr = dr["ItemName"].ToString();
                    item.ItemNameEn = "";
                    item.MaxQty = 0;
                    item.MinQty = 0;
                    item.ReorderLevel = 0;

                    if (dr["Pprice"] != DBNull.Value && dr["Pprice"].ToString() != string.Empty)
                        item.PurchasePrice = Convert.ToDecimal(dr["Pprice"]);
                    else
                        item.PurchasePrice = 0;

                    if (dr["Sprice"] != DBNull.Value && dr["Sprice"].ToString() != string.Empty)
                        item.SmallUOMPrice = Convert.ToDecimal(dr["Sprice"]);
                    else
                        item.SmallUOMPrice = 0;

                    item.SmallUOM = 1;
                    DB.IC_Items.InsertOnSubmit(item);
                    DB.SubmitChanges();
                }

                if (dr["Balance"] == DBNull.Value || dr["Balance"].ToString() == string.Empty
                    || Convert.ToDecimal(dr["Balance"]) <= 0)
                    continue;

                DataRow row = dtOpenBalance.NewRow();
                row["ItemId"] = item.ItemId;
                row["ItemCode1"] = item.ItemCode1;
                row["ItemCode2"] = item.ItemCode2;
                row["UOM"] = item.SmallUOM;
                row["UomIndex"] = 0;
                row["Qty"] = Convert.ToDecimal(dr["Balance"]);
                row["PurchasePrice"] = item.PurchasePrice;
                row["MainPrice"] = item.PurchasePrice;
                row["SellPrice"] = item.SmallUOMPrice;
                row["DiscountValue"] = 0;
                row["DiscountRatio"] = 0;                
                row["CurrentQty"] = 0;
                row["TotalPurchasePrice"] = item.PurchasePrice * Convert.ToDecimal(dr["Balance"]);
                row["TotalSellPrice"] = item.SmallUOMPrice * Convert.ToDecimal(dr["Balance"]);
                
                dtOpenBalance.Rows.Add(row);
            }

        }

        public static DataTable exceldata(string filelocation)
        {
            string sheetName = "Sheet1";
            string connStr = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + filelocation + ";Extended Properties=Excel 12.0;";

            OleDbConnection connection = new OleDbConnection(connStr);
            try
            {
                connection.Open();
                sheetName = GetExcelSheetNames(filelocation, connStr)[0];
            }
            catch
            {
                connStr = "Provider=Microsoft.Jet.OLEDB.4.0;Data Source=" + filelocation + ";Extended Properties=Excel 12.0;";
                connection.ConnectionString = connStr;
                sheetName = GetExcelSheetNames(filelocation, connStr)[0];
                try
                {
                    connection.Open();
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                    return null;
                }
            }

            OleDbCommand excelCommand = new OleDbCommand();
            OleDbDataAdapter excelDataAdapter = new OleDbDataAdapter();
            DataTable dt_Emp_Attendance = new DataTable();
            excelCommand = new OleDbCommand("SELECT * FROM [" + sheetName + "]", connection);
            excelDataAdapter.SelectCommand = excelCommand;

            try
            {
                excelDataAdapter.Fill(dt_Emp_Attendance);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                return null;
            }

            return dt_Emp_Attendance;
        }

        public static String[] GetExcelSheetNames(string filelocation, string connStr)
        {
            OleDbConnection objConn = null;
            System.Data.DataTable dt = null;
            try
            {
                objConn = new OleDbConnection(connStr);
                objConn.Open();
                dt = objConn.GetOleDbSchemaTable(OleDbSchemaGuid.Tables, null);
                if (dt == null)
                {
                    return null;
                }

                String[] excelSheets = new String[dt.Rows.Count];
                int i = 0;

                // Add the sheet name to the string array.
                foreach (DataRow row in dt.Rows)
                {
                    excelSheets[i] = row["TABLE_NAME"].ToString();
                    i++;
                }

                // Loop through all of the sheets if you want too...
                for (int j = 0; j < excelSheets.Length; j++)
                {
                    // Query each excel sheet.
                }

                return excelSheets;
            }
            catch
            {
                return null;
            }
            finally
            {
                // Clean up.
                if (objConn != null)
                {
                    objConn.Close();
                    objConn.Dispose();
                }
                if (dt != null)
                {
                    dt.Dispose();
                }
            }
        }


        private void frm_IC_OpenBalance_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                e.Cancel = true;
            else
                e.Cancel = false;

            ErpUtils.save_Grid_Layout(grdOpenBalance, this.Name.Replace("frm_", ""), false);
        }

        private void frm_IC_OpenBalance_Shown(object sender, EventArgs e)
        {
            grdOpenBalance.Focus();
            var view = (grdOpenBalance.FocusedView as DevExpress.XtraGrid.Views.Grid.GridView);
            view.AddNewRow();
            view.FocusedColumn = view.Columns[frmMain.st_Store.InvoicesUseSearchItems ? "ItemId" : "ItemCode1"];//mahmoud
        }


        private void gridView1_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            if (e.Column.FieldName == "MainPrice" || e.Column.FieldName == "TotalSellPrice" || e.Column.FieldName == "LargeUOMFactor"
                || e.Column.FieldName == "MediumUOMFactor" || e.Column.FieldName == "TotalPurchasePrice" || e.Column.FieldName == "CurrentQty"
                || e.Column.FieldName == "UomIndex")
                return;


            //DB = new Pharmacy.DAL.ERPDataContext();
            DAL.IC_Item item = null;

            GridView view = grdOpenBalance.FocusedView as GridView;
            DataRow row = view.GetFocusedDataRow();

            #region GetItem
            if (e.Column.FieldName == "ItemCode1")
            {
                if (view.GetFocusedRowCellValue("ItemCode1").ToString() != string.Empty)
                {
                    //---------mahmoud
                    int code1 = 0;
                    Int32.TryParse(view.GetFocusedRowCellValue("ItemCode1").ToString(), out code1);
                    if (code1 > 0)
                    {
                        item = (from i in DB.IC_Items
                                where i.ItemType != (int)ItemType.MatrixParent
                                && i.ItemType != (int)ItemType.Service
                                && i.ItemType != (int)ItemType.Subtotal
                                where i.ItemCode1 == code1
                                select i).FirstOrDefault();
                    }
                    //---------

                    if (item == null)
                    {
                        item = (from i in DB.IC_Items
                                where i.ItemType != (int)ItemType.MatrixParent
                                && i.ItemType != (int)ItemType.Service
                                && i.ItemType != (int)ItemType.Subtotal
                                where i.ItemCode2 == view.GetFocusedRowCellValue("ItemCode1").ToString()
                                select i).FirstOrDefault();

                        if (item == null)
                        {
                            item = (from i in DB.IC_Items
                                    where i.ItemType != (int)ItemType.MatrixParent
                                && i.ItemType != (int)ItemType.Service
                                && i.ItemType != (int)ItemType.Subtotal
                                    where i.IC_InternationalCodes.Select(x => x.InternationalCode)
                                .Contains(view.GetFocusedRowCellValue("ItemCode1").ToString())
                                    select i).FirstOrDefault();
                        }
                    }
                }
                else
                {
                    view.DeleteRow(e.RowHandle);
                }
            }
            if (e.Column.FieldName == "ItemCode2")
            {
                if (view.GetFocusedRowCellValue("ItemCode2").ToString() != string.Empty)
                {
                    item = (from i in DB.IC_Items
                            where i.ItemType != (int)ItemType.MatrixParent
                                && i.ItemType != (int)ItemType.Service
                                && i.ItemType != (int)ItemType.Subtotal
                            where i.ItemCode2.Contains(view.GetFocusedRowCellValue("ItemCode2").ToString())
                            select i).FirstOrDefault();
                }
                else
                {
                    view.DeleteRow(e.RowHandle);
                }
            }

            if (e.Column.FieldName == "ItemId")
            {
                if (view.GetFocusedRowCellValue("ItemId").ToString() != string.Empty)
                {
                    item = (from i in DB.IC_Items
                            where i.ItemId == Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"))
                            select i).SingleOrDefault();
                }
                else
                {
                    view.DeleteRow(e.RowHandle);
                }
            }

            if (e.Column.FieldName == "ItemCode1" || e.Column.FieldName == "ItemCode2"
                || e.Column.FieldName == "ItemId")
            {
                if (item != null && item.ItemId > 0)
                {
                    LoadItemRow(item, row);
                    //view.FocusedColumn = view.Columns["UOM"];
                }
                else
                {
                    view.DeleteRow(e.RowHandle);
                    //view.FocusedColumn = view.Columns["ItemId"];
                }
            }
            #endregion

            #region GetUomPrice
            if (e.Column.FieldName == "UOM")
            {
                if (view.GetRowCellValue(e.RowHandle, "UOM").ToString() != string.Empty &&
                    view.GetRowCellValue(e.RowHandle, "ItemId").ToString() != string.Empty)
                {
                    int itmId = Convert.ToInt32(view.GetRowCellValue(e.RowHandle, "ItemId"));
                    int uomIndex = repUOM.GetIndexByKeyValue(view.GetRowCellValue(e.RowHandle, "UOM"));

                    //get UOM and Factor, multiple by purchase and sell prices
                    item = (from i in DB.IC_Items
                            where i.ItemId == itmId
                            select i).SingleOrDefault();

                    if (uomIndex == 0)//small
                    {                        
                        view.SetRowCellValue(e.RowHandle, "SellPrice", item.SmallUOMPrice);
                        view.SetRowCellValue(e.RowHandle, "PurchasePrice", (item.PurchasePrice));
                    }
                    if (uomIndex == 1)//medium
                    {                        
                        view.SetRowCellValue(e.RowHandle, "PurchasePrice", decimal.ToDouble(item.PurchasePrice * MyHelper.FractionToDouble(item.MediumUOMFactor)));
                        view.SetRowCellValue(e.RowHandle, "SellPrice", decimal.ToDouble(item.MediumUOMPrice.Value));
                    }
                    if (uomIndex == 2)//large
                    {
                        view.SetRowCellValue(e.RowHandle, "PurchasePrice", decimal.ToDouble(item.PurchasePrice * MyHelper.FractionToDouble(item.LargeUOMFactor)));
                        view.SetRowCellValue(e.RowHandle, "SellPrice", decimal.ToDouble(item.LargeUOMPrice.Value));
                    }

                    view.GetDataRow(e.RowHandle)["UomIndex"] = uomIndex;                    
                }
            }
            #endregion

            #region Calculate Prices
            if (e.Column.FieldName == "PurchasePrice" || e.Column.FieldName == "SellPrice" || e.Column.FieldName == "DiscountValue"
                || e.Column.FieldName == "Qty" || e.Column.FieldName == "ItemId"
                || e.Column.FieldName == "ItemCode1" || e.Column.FieldName == "ItemCode2")
            {
                try
                {
                    if (!string.IsNullOrEmpty(view.GetRowCellValue(e.RowHandle, "PurchasePrice").ToString()) &&
                        !string.IsNullOrEmpty(view.GetRowCellValue(e.RowHandle, "DiscountValue").ToString()) &&
                        !string.IsNullOrEmpty(view.GetRowCellValue(e.RowHandle, "SellPrice").ToString()) &&
                        !string.IsNullOrEmpty(view.GetRowCellValue(e.RowHandle, "Qty").ToString()) &&
                        !string.IsNullOrEmpty(view.GetRowCellValue(e.RowHandle, "ItemId").ToString()))
                    {
                        decimal Qty = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "Qty"));                        
                        decimal SellPrice = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "SellPrice"));
                        decimal PurchasePrice = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "PurchasePrice"));
                        decimal DiscountValue = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "DiscountValue"));

                        decimal TotalMainPrice = (PurchasePrice * Qty) - DiscountValue;
                        decimal MainPrice = TotalMainPrice / Qty;
                        decimal TotalSellPrice = SellPrice * Qty;

                        int itmId = Convert.ToInt32(view.GetRowCellValue(e.RowHandle, "ItemId"));
                        //byte uomIndex = Convert.ToByte(view.GetRowCellValue(e.RowHandle, "UomIndex"));
                        int uomIndex = repUOM.GetIndexByKeyValue(view.GetRowCellValue(e.RowHandle, "UOM"));


                        view.GetDataRow(e.RowHandle)["TotalPurchasePrice"] = decimal.ToDouble(TotalMainPrice);
                        //view.SetRowCellValue(e.RowHandle, "TotalPurchasePrice", TotalMainPrice);
                        view.GetDataRow(e.RowHandle)["MainPrice"] = decimal.ToDouble(MainPrice);
                        //view.SetRowCellValue(e.RowHandle, "MainPrice", MainPrice);
                        view.GetDataRow(e.RowHandle)["TotalSellPrice"] = decimal.ToDouble(TotalSellPrice);
                        //view.SetRowCellValue(e.RowHandle, "TotalSellPrice", TotalSellPrice);
                        if (DiscountValue > 0 && TotalMainPrice > 0 && PurchasePrice > 0 && Qty > 0)
                            view.GetDataRow(e.RowHandle)["DiscountRatio"] = decimal.ToDouble(DiscountValue / (PurchasePrice * Qty));
                        //view.SetRowCellValue(e.RowHandle, "DiscountRatio", DiscountValue / (PurchasePrice * Qty));


                        //get store qty
                        decimal medium = 1;
                        decimal large = 1;
                        if (view.GetRowCellValue(e.RowHandle, "MediumUOMFactor").ToString() != string.Empty)
                            medium = MyHelper.FractionToDouble(view.GetRowCellValue(e.RowHandle, "MediumUOMFactor").ToString());
                        if (view.GetRowCellValue(e.RowHandle, "LargeUOMFactor").ToString() != string.Empty)
                            large = MyHelper.FractionToDouble(view.GetRowCellValue(e.RowHandle, "LargeUOMFactor").ToString());

                        decimal currentQty = MyHelper.GetItemQty(itmId, Convert.ToInt32(lkpStore.EditValue));
                        currentQty = MyHelper.getCalculatedUomQty(currentQty, (byte)uomIndex, medium, large);
                        view.GetDataRow(e.RowHandle)["CurrentQty"] = decimal.ToDouble(currentQty);
                        //view.SetRowCellValue(e.RowHandle, "CurrentQty", decimal.Round(currentQty, 4));
                        Get_TotalAccount();
                    }
                }
                catch { }
            }
            #endregion

            #region DiscountRatio_changed_by_User
            if (e.Column.FieldName == "DiscountRatio")
            {
                if (!string.IsNullOrEmpty(view.GetRowCellValue(e.RowHandle, "PurchasePrice").ToString()) &&
                          !string.IsNullOrEmpty(view.GetRowCellValue(e.RowHandle, "DiscountValue").ToString()) &&
                          !string.IsNullOrEmpty(view.GetRowCellValue(e.RowHandle, "DiscountRatio").ToString()) &&
                          !string.IsNullOrEmpty(view.GetRowCellValue(e.RowHandle, "Qty").ToString()))
                {
                    decimal DiscountValue = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "DiscountValue"));
                    
                    decimal Height = Convert.ToDecimal(view.GetFocusedRowCellValue("Height"));
                    decimal Length = Convert.ToDecimal(view.GetFocusedRowCellValue("Length"));
                    decimal Width = Convert.ToDecimal(view.GetFocusedRowCellValue("Width"));

                    decimal Qty = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "Qty"));
                    decimal PurchasePrice = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "PurchasePrice"));
                    decimal DiscountRatio = (Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "DiscountRatio")));
                    DiscountValue = DiscountRatio * PurchasePrice * Qty * Length * Width * Height;

                    decimal currentDiscValue = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "DiscountValue"));

                    //if (decimal.Round(DiscountValue, 4) != currentDiscValue)
                    if (DiscountValue != currentDiscValue)
                        view.SetRowCellValue(e.RowHandle, "DiscountValue", decimal.ToDouble(DiscountValue));
                }
            }
            #endregion

        }

        private void gridView1_FocusedColumnChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedColumnChangedEventArgs e)
        {           
            if (e.FocusedColumn != null && e.FocusedColumn.FieldName == "UOM")
            {
                GridView view = grdOpenBalance.FocusedView as GridView;
                DataRow row = view.GetFocusedDataRow();

                //DB = new Pharmacy.DAL.ERPDataContext();                
                DAL.IC_Item item = new DAL.IC_Item();

                if (row != null && row["ItemId"].ToString() != string.Empty)
                {
                    item = (from i in DB.IC_Items
                            where i.ItemId == Convert.ToInt32(row["ItemId"])
                            select i).SingleOrDefault();

                    MyHelper.GetUOMs(item, dtUOM, uom_list);

                    if (string.IsNullOrEmpty(row["UOM"].ToString()))
                        view.SetFocusedRowCellValue("UOM", dtUOM.Rows[0]["UomId"]);
                }

            }
        }

        private void gridView1_KeyDown(object sender, KeyEventArgs e)
        {
            GridView view = grdOpenBalance.FocusedView as GridView;
            if (e.KeyCode == Keys.Delete && e.Modifiers == Keys.Control)
            {
                if (MessageBox.Show(Shared.IsEnglish ? ResICEn.MsgDelRow : ResICAr.MsgDelRow, Shared.IsEnglish ? ResICEn.MsgTQues : ResICAr.MsgTQues, MessageBoxButtons.YesNo, MessageBoxIcon.Question) !=
                  DialogResult.Yes)
                    return;
                
                view.DeleteRow(view.FocusedRowHandle);                
            }
            if (e.KeyCode == Keys.Up && e.Control && e.Shift)
            {
                ErpUtils.Move_Row_Up(view);                
            }
            if (e.KeyCode == Keys.Down && e.Control && e.Shift)
            {
                ErpUtils.Move_Row_Down(view);                
            }
        }

        private void gridView1_ValidateRow(object sender, DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs e)
        {            
            try
            {
                ColumnView view = sender as ColumnView;

                if (view.GetRowCellValue(e.RowHandle, view.Columns["ItemId"]).ToString() == string.Empty)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["ItemId"], Shared.IsEnglish ? ResICEn.txtValidateItem : ResICAr.txtValidateItem);//"يجب اختيار الصنف";
                }

                if (view.GetRowCellValue(e.RowHandle, view.Columns["UOM"]).ToString() == string.Empty)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["UOM"], Shared.IsEnglish ? ResICEn.txtValidateUom : ResICAr.txtValidateUom);//"يجب اختيار وحدة القياس");
                }

                if ((view.GetRowCellValue(e.RowHandle, view.Columns["Qty"])).ToString() == string.Empty
                    || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Qty"])) <= 0)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["Qty"], Shared.IsEnglish ? ResICEn.txtValidateQty : ResICAr.txtValidateQty);//"الكمية يجب ان تكون اكبر من الصفر");
                }

                if ((view.GetRowCellValue(e.RowHandle, view.Columns["PurchasePrice"])).ToString() == string.Empty
                    || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["PurchasePrice"])) < 0)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["PurchasePrice"], Shared.IsEnglish ? ResICEn.txtValidatePPrice : ResICAr.txtValidatePPrice);//"سعر الشراء يجب أن يكون أكبر من الصفر");                    
                }

                if (view.GetRowCellValue(e.RowHandle, view.Columns["DiscountRatio"]).ToString() == string.Empty)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["DiscountRatio"], Shared.IsEnglish ? ResICEn.txtValidateDiscount : ResICAr.txtValidateDiscount);//"يجب تحديد الخصم");
                }
                if (view.GetRowCellValue(e.RowHandle, view.Columns["DiscountValue"]).ToString() == string.Empty)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["DiscountValue"], Shared.IsEnglish ? ResICEn.txtValidateDiscount : ResICAr.txtValidateDiscount);//"يجب تحديد الخصم");
                }
                if (view.GetRowCellValue(e.RowHandle, view.Columns["DiscountRatio"]).ToString() != string.Empty
                    && Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["DiscountRatio"])) > 1)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["DiscountRatio"], Shared.IsEnglish ? ResICEn.txtValidateMaxDiscount : ResICAr.txtValidateMaxDiscount);//"نسبة الخصم لايمكن ان تتجاوز المائة");
                }
            }
            catch 
            {
                e.Valid = false;
            }
        }

        private void gridView1_InvalidRowException(object sender, DevExpress.XtraGrid.Views.Base.InvalidRowExceptionEventArgs e)
        {
            if ((e.Row as DataRowView).Row["ItemId"] == DBNull.Value)
                e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.Ignore;
            else
                e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.NoAction;
        }

        private void gridView1_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            Get_TotalAccount();
            grd_FocusOnItemId(frmMain.st_Store.InvoicesUseSearchItems ? "ItemId" : "ItemCode1");//mahmoud
        }

        private void gridView1_CustomColumnDisplayText(object sender, CustomColumnDisplayTextEventArgs e)
        {               
            if (e.Column.FieldName == "DiscountValue" || e.Column.FieldName == "DiscountRatio"
                || e.Column.FieldName == "TotalPurchasePrice" || e.Column.FieldName == "SellPrice"
                || e.Column.FieldName == "PurchasePrice" || e.Column.FieldName == "CurrentQty"
                || e.Column.FieldName == "Qty")
            {
                if (e.Value != DBNull.Value)
                    e.DisplayText = decimal.ToDouble(Convert.ToDecimal(e.Value)).ToString();
            }            
        }

        private void grid_ProcessGridKey(object sender, KeyEventArgs e)
        {
            try
            {
                DevExpress.XtraGrid.GridControl grid = sender as DevExpress.XtraGrid.GridControl;
                var view = (grid.FocusedView as DevExpress.XtraGrid.Views.Grid.GridView);
                if (e.KeyCode == Keys.Enter)
                {
                    var focused_column = (grid.FocusedView as DevExpress.XtraGrid.Views.Base.ColumnView).FocusedColumn;
                    int focused_row_handle = (grid.FocusedView as DevExpress.XtraGrid.Views.Base.ColumnView).FocusedRowHandle;

                    if (view.FocusedColumn == view.Columns["ItemId"]
                        || view.FocusedColumn == view.Columns["ItemCode1"]
                        || view.FocusedColumn == view.Columns["ItemCode2"])
                    {
                        string temp = view.FocusedColumn.FieldName;
                        view.FocusedColumn = view.Columns["UOM"];
                        if (view.GetFocusedRowCellValue(temp) == null || string.IsNullOrEmpty(view.GetFocusedRowCellValue(temp).ToString()))
                            view.FocusedColumn = view.Columns[temp];
                        return;
                    }
                    if (view.FocusedColumn == view.Columns["UOM"])
                    {
                        view.FocusedColumn = view.Columns["Qty"];
                        return;
                    }

                    #region expiredate
                    bool IsExpire = Convert.ToBoolean(view.GetFocusedRowCellValue(col_IsExpire));
                    if (IsExpire == true)
                    {
                        if (view.FocusedColumn == view.Columns["Qty"])
                        {
                            view.FocusedColumn = view.Columns["Expire"];
                            return;
                        }
                        if (view.FocusedColumn == view.Columns["Expire"])
                        {
                            view.FocusedColumn = view.Columns["Batch"];
                            return;
                        }
                        if (view.FocusedColumn == view.Columns["Batch"])
                        {
                            view.FocusedColumn = view.Columns["PurchasePrice"];
                            return;
                        }
                    }
                    else
                    {
                        if (view.FocusedColumn == view.Columns["Qty"])
                        {
                            view.FocusedColumn = view.Columns["PurchasePrice"];
                            return;
                        }
                    }
                    #endregion

                    
                    if (view.FocusedColumn == view.Columns["SellPrice"]
                        || view.FocusedColumn == view.Columns["PurchasePrice"]
                        || view.FocusedColumn == view.Columns["DiscountRatio"]
                        || view.FocusedColumn == view.Columns["Batch"])

                    {
                        grid_ProcessGridKey(sender, new KeyEventArgs(Keys.Tab));
                    }

                    if (view.FocusedRowHandle < 0)//|| view.FocusedRowHandle == view.RowCount)
                    {
                        view.AddNewRow();
                        view.FocusedColumn = view.Columns[frmMain.st_Store.InvoicesUseSearchItems ? "ItemId" : "ItemCode1"];//mahmoud
                    }
                    else
                    {
                        view.FocusedRowHandle = focused_row_handle + 1;
                        view.FocusedColumn = view.Columns[frmMain.st_Store.InvoicesUseSearchItems ? "ItemId" : "ItemCode1"];                        
                    }

                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == Keys.Tab && e.Modifiers != Keys.Shift)
                {
                    if (view.FocusedColumn.VisibleIndex == 0)
                        view.FocusedColumn = view.VisibleColumns[view.VisibleColumns.Count - 1];
                    else
                        view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex - 1];
                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == Keys.Tab && e.Modifiers == Keys.Shift)
                {
                    if (view.FocusedColumn.VisibleIndex == view.VisibleColumns.Count)
                        view.FocusedColumn = view.VisibleColumns[0];
                    else
                        view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex + 1];
                    e.Handled = true;
                    return;
                }
                if ((view.GetFocusedRow() as DataRowView).IsNew == true && view.GetFocusedRowCellValue("ItemId").ToString() == string.Empty)
                {
                    if (e.KeyCode == Keys.Up)
                        view.DeleteRow(view.FocusedRowHandle);
                }
            }
            catch { }
        }


        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Save_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            GridView view = grdOpenBalance.FocusedView as GridView;           

            grdOpenBalance.RefreshDataSource();
            view.RefreshData();

            Get_TotalAccount();

            //delete store's current open balance
            var ic_StoreOpenBalance = DB.IC_OpenBalances.Where(i => i.StoreId == Convert.ToInt32(lkpStore.EditValue))
                .Select(i => i);

            var ic_openBalanceIds = DB.IC_OpenBalances.Where(i => i.StoreId == Convert.ToInt32(lkpStore.EditValue))
                .Select(i => i.OpenBalanceId).ToList();            


                var ic_ItemStore = DB.IC_ItemStores.Where(i => i.ProcessId == (int)Process.OpenBalance
                    && ic_openBalanceIds.Contains(i.SourceId));                

                DB.IC_ItemStores.DeleteAllOnSubmit(ic_ItemStore);
                DB.IC_OpenBalances.DeleteAllOnSubmit(ic_StoreOpenBalance);
                DB.SubmitChanges();

            int first_openbalance_id = 0;

            for (int x = 0; x < dtOpenBalance.Rows.Count; x++)
            {
                if (dtOpenBalance.Rows[x].RowState != DataRowState.Deleted)
                {
                    decimal MediumUOMFactor = 1;
                    decimal LargeUOMFactor = 1;
                    DAL.IC_OpenBalance open = new DAL.IC_OpenBalance();
                    open.OpenDate = dtDate.DateTime;
                    open.StoreId = Convert.ToInt32(lkpStore.EditValue);
                    open.ItemId = Convert.ToInt32(dtOpenBalance.Rows[x]["ItemId"]);
                    open.UOMId = Convert.ToByte(dtOpenBalance.Rows[x]["UOM"]);
                    open.UOMIndex = Convert.ToByte(dtOpenBalance.Rows[x]["UomIndex"]);

                    open.Qty = Convert.ToDecimal(dtOpenBalance.Rows[x]["Qty"]);

                    #region Expire
                    if (dtOpenBalance.Rows[x]["Expire"] == DBNull.Value)
                        open.Expire = null;
                    else
                    {
                        DateTime temp = Convert.ToDateTime(dtOpenBalance.Rows[x]["Expire"]);
                        temp = temp.AddDays(-temp.Day + 1);
                        open.Expire = temp;
                    }
                    if (dtOpenBalance.Rows[x]["Batch"] == DBNull.Value
                        || dtOpenBalance.Rows[x]["Batch"].ToString().Trim() == string.Empty)
                        open.Batch = null;
                    else
                        open.Batch = dtOpenBalance.Rows[x]["Batch"].ToString();
                    #endregion

                    open.PurchasePrice = Convert.ToDecimal(dtOpenBalance.Rows[x]["PurchasePrice"]);
                    open.SellPrice = Convert.ToDecimal(dtOpenBalance.Rows[x]["SellPrice"]);

                    open.DiscountValue = Convert.ToDecimal(dtOpenBalance.Rows[x]["DiscountValue"]);
                    open.DiscountRatio = Convert.ToDecimal(dtOpenBalance.Rows[x]["DiscountRatio"]);
                    open.TotalPurchasePrice = Convert.ToDecimal(dtOpenBalance.Rows[x]["TotalPurchasePrice"]);
                    open.TotalSellPrice = Convert.ToDecimal(dtOpenBalance.Rows[x]["TotalSellPrice"]);

                    if (dtOpenBalance.Rows[x]["MediumUOMFactor"].ToString() != string.Empty)
                        MediumUOMFactor = MyHelper.FractionToDouble(dtOpenBalance.Rows[x]["MediumUOMFactor"].ToString());
                    if (dtOpenBalance.Rows[x]["LargeUOMFactor"].ToString() != string.Empty)
                        LargeUOMFactor = MyHelper.FractionToDouble(dtOpenBalance.Rows[x]["LargeUOMFactor"].ToString());

                    open.UserId = Shared.UserId;

                    DB.IC_OpenBalances.InsertOnSubmit(open);
                    DB.SubmitChanges();

                    //Add Item To Store
                    #region Matrix_Data
                    int? ParentItemId = null, M1 = null, M2 = null, M3 = null;
                    if (!string.IsNullOrEmpty(dtOpenBalance.Rows[x]["ParentItemId"].ToString())
                        && Convert.ToInt32(dtOpenBalance.Rows[x]["ParentItemId"]) != 0)
                        ParentItemId = Convert.ToInt32(dtOpenBalance.Rows[x]["ParentItemId"]);
                    if (!string.IsNullOrEmpty(dtOpenBalance.Rows[x]["M1"].ToString()) && Convert.ToInt32(dtOpenBalance.Rows[x]["M1"]) != 0)
                        M1 = Convert.ToInt32(dtOpenBalance.Rows[x]["M1"]);
                    if (!string.IsNullOrEmpty(dtOpenBalance.Rows[x]["M2"].ToString()) && Convert.ToInt32(dtOpenBalance.Rows[x]["M2"]) != 0)
                        M2 = Convert.ToInt32(dtOpenBalance.Rows[x]["M2"]);
                    if (!string.IsNullOrEmpty(dtOpenBalance.Rows[x]["M3"].ToString()) && Convert.ToInt32(dtOpenBalance.Rows[x]["M3"]) != 0)
                        M3 = Convert.ToInt32(dtOpenBalance.Rows[x]["M3"]);
                    #endregion


                    MyHelper.AddItemToStore(open.ItemId, open.StoreId, open.Qty, open.UOMIndex,
                        MediumUOMFactor, LargeUOMFactor, null, open.OpenBalanceId, (int)Process.OpenBalance, true, open.TotalPurchasePrice, dtDate.DateTime,
                        open.Expire, open.Batch, 0, 0, 0, 0, ParentItemId, M1, M2, M3, 0, null);                    

                    if (first_openbalance_id == 0)
                        first_openbalance_id = open.OpenBalanceId;
                }
            }


            //var jrnlExists = (from jd in DB.ACC_JournalDetails
            //                  join j in DB.ACC_Journals
            //                  on jd.JournalId equals j.JournalId
            //                  where jd.AccountId == Convert.ToInt32(lkpStore.GetColumnValue("OpenInventoryAccount"))
            //                  && j.SourceId == Convert.ToInt32(lkpStore.EditValue)
            //                  && j.ProcessId == (int)Process.OpenBalance
            //                  select jd).FirstOrDefault();
            //int newJournalId = 0;
            //if (jrnlExists == null)
            //{
            //    DAL.ACC_Journal jornal = new DAL.ACC_Journal();
            //    jornal.InsertDate = dtDate.DateTime;
            //    jornal.InsertUser = Shared.UserId;
            //    jornal.JCode = HelperAcc.Get_Jornal_Code();
            //    jornal.JNotes = Shared.IsEnglish ? ResICEn.txtJornalOpenBalance : ResICAr.txtJornalOpenBalance;
            //    jornal.ProcessId = (int)Process.OpenBalance;
            //    jornal.SourceId = Convert.ToInt32(lkpStore.EditValue);
            //    DB.ACC_Journals.InsertOnSubmit(jornal);
            //    DB.SubmitChanges();
            //    newJournalId = jornal.JournalId;
            //}
            //else
            //{

            //    DAL.ACC_Journal jornal = DB.ACC_Journals.Where(x => x.JournalId == jrnlExists.JournalId).FirstOrDefault();
            //    jornal.InsertDate = dtDate.DateTime;
            //    jornal.LastUpdateDate = MyHelper.Get_Server_DateTime();
            //    jornal.LastUpdateUser = Shared.UserId;
            //    DB.SubmitChanges();

            //    var jDetails = DB.ACC_JournalDetails.Where(j => j.JournalId == jrnlExists.JournalId);
            //    DB.ACC_JournalDetails.DeleteAllOnSubmit(jDetails);
            //    DB.SubmitChanges();
            //}
            //int? costCenter = Convert.ToInt32(lkpStore.GetColumnValue("CostCenterId"));      // تحميل مركز تكلفة المخزن
            //if (costCenter == 0)
            //    costCenter = null;

            #region From open balance account
            //DAL.ACC_JournalDetail jdetail_1 = new DAL.ACC_JournalDetail();
            //jdetail_1.AccountId = Convert.ToInt32(lkpStore.GetColumnValue("OpenInventoryAccount"));      // حساب ارصدة افتتاحية المخزن
            //jdetail_1.CostCenter = costCenter;
            //jdetail_1.Credit = 0;
            //jdetail_1.Debit = totalPurchase;
            //jdetail_1.JournalId = jrnlExists == null ? newJournalId : jrnlExists.JournalId;
            //jdetail_1.Notes = Shared.IsEnglish ? ResICEn.txtJornalOpenBalance : ResICAr.txtJornalOpenBalance;
            //DB.ACC_JournalDetails.InsertOnSubmit(jdetail_1);
            #endregion

            #region To Inventory Account
            //DAL.ACC_JournalDetail jdetail_2 = new DAL.ACC_JournalDetail();
            //jdetail_2.AccountId = HelperAcc.InventoryAccId;
            //jdetail_2.Credit = totalPurchase;
            //jdetail_2.Debit = 0;
            //jdetail_2.JournalId = jrnlExists == null ? newJournalId : jrnlExists.JournalId;
            //jdetail_2.Notes = Shared.IsEnglish ? ResICEn.txtJornalOpenBalance : ResICAr.txtJornalOpenBalance;
            //DB.ACC_JournalDetails.InsertOnSubmit(jdetail_2);
            //DB.SubmitChanges();
            #endregion
            // print barcodes ??

            XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResICEn.MsgSave : ResICAr.MsgSave, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
            dtOpenBalance.AcceptChanges();
            DataModified = false;
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            PrintingSystem printSystem = new PrintingSystem();
            PrintableComponentLink printLink = new PrintableComponentLink();

            ((System.ComponentModel.ISupportInitialize)(printSystem)).BeginInit();

            printSystem.Links.AddRange(new object[] {
            printLink});
            printLink.Component = this.grdOpenBalance;

            printLink.PaperKind = System.Drawing.Printing.PaperKind.A4;
            printLink.Margins = new System.Drawing.Printing.Margins(5, 5, 115, 50);
            printLink.PrintingSystem = printSystem;
            printLink.PrintingSystemBase = printSystem;

            printLink.CreateMarginalHeaderArea +=
                new DevExpress.XtraPrinting.CreateAreaEventHandler(this.printableComponentLink1_CreateReportHeaderArea);

            printLink.CreateReportHeaderArea +=
                new DevExpress.XtraPrinting.CreateAreaEventHandler(this.printableComponentLink1_CreateReportMainInfo);

            ((System.ComponentModel.ISupportInitialize)(printSystem)).EndInit();

            printLink.CreateDocument();
            printLink.ShowPreview();
        }

        private void printableComponentLink1_CreateReportHeaderArea(object sender, DevExpress.XtraPrinting.CreateAreaEventArgs e)
        {            
            string ReportName = Shared.IsEnglish ? (ResICEn.txtIC_openbalance+ " " + ResICEn.txtStore) : (ResICAr.txtIC_openbalance+ " " + ResICAr.txtStore)
                + " " + lkpStore.Text.ToString();
            ErpUtils.CreateReportHeader(e, ReportName, string.Empty, string.Empty);
        }

        private void printableComponentLink1_CreateReportMainInfo(object sender, DevExpress.XtraPrinting.CreateAreaEventArgs e)
        {
            string CompName = string.Empty;
            Image image = null;
            ErpUtils.GetReportHeaderData(out CompName, out image);

            RectangleF recNotes = new RectangleF((float)395, (float)42, 355, 19);
            RectangleF recDate = new RectangleF((float)585, (float)17, 165, 19);
            RectangleF recStore = new RectangleF((float)395, (float)17, 165, 19);

            e.Graph.StringFormat = new BrickStringFormat(StringAlignment.Far);
            e.Graph.Font = new Font("Times New Roman", 13, FontStyle.Regular);
            e.Graph.BorderColor = Color.Gray;
            e.Graph.DefaultBrickStyle.BorderColor = Color.Gray;
            e.Graph.BackColor = Color.Snow;
            e.Graph.ForeColor = Color.Black;

            
            string date = (Shared.IsEnglish ? ResICEn.txtInvDate : ResICAr.txtInvDate )+ " " + dtDate.Text;
            string store = (Shared.IsEnglish ? ResICEn.txtStore : ResICAr.txtStore )+ " " + lkpStore.Text;

            e.Graph.DrawString(date, Color.Gray, recDate, BorderSide.All);
            e.Graph.DrawString(store, Color.Gray, recStore, BorderSide.All);

            e.Graph.BackColor = Color.White;
            e.Graph.DrawString("", Color.Black, new RectangleF((float)10, (float)124, 1, 15), BorderSide.None);
        }

        private void lkpStore_EditValueChanged(object sender, EventArgs e)
        {
            var OpenDate = DB.IC_OpenBalances.Where(x => x.StoreId == Convert.ToInt32(lkpStore.EditValue)).
                Select(x => x.OpenDate).FirstOrDefault();
            if (OpenDate == DateTime.MinValue)
                dtDate.DateTime = MyHelper.Get_Server_DateTime();
            else
                dtDate.DateTime = OpenDate;

            GetInvoiceDetails(Convert.ToInt32(lkpStore.EditValue));
        }

        private void lkpStore_Modified(object sender, EventArgs e)
        {
            DataModified = true;
        }


        void grd_FocusOnItemId(string columnName)
        {
            GridView view = grdOpenBalance.FocusedView as GridView;
            view.FocusedColumn = view.Columns[columnName];
        }

        private void Get_TotalAccount()
        {            
            totalPurchase = Convert.ToDecimal(col_TotalPurchasePrice.SummaryItem.SummaryValue);
            totalDiscount = Convert.ToDecimal(col_DiscountValue.SummaryItem.SummaryValue);
        }

        private void LoadItemRow(DAL.IC_Item item, DataRow row)
        {
            Get_Items();
            if (item != null && item.ItemId > 0)
            {
                row["ItemId"] = item.ItemId;
                row["ItemCode1"] = item.ItemCode1;
                row["ItemCode2"] = item.ItemCode2;
                row["PurchasePrice"] = Decimal.ToDouble(item.PurchasePrice);
                row["SellPrice"] = Decimal.ToDouble(item.SmallUOMPrice);
                row["DiscountRatio"] = "0";
                row["DiscountValue"] = "0";
                row["Qty"] = "1";
                row["MainPrice"] = "0";
                row["TotalPurchasePrice"] = "0";
                row["TotalSellPrice"] = "0";
                row["CurrentQty"] = "0";                
                row["MediumUOMFactor"] = MyHelper.FractionToDouble(item.MediumUOMFactor);                
                row["LargeUOMFactor"] = MyHelper.FractionToDouble(item.LargeUOMFactor);

                MyHelper.GetUOMs(item, dtUOM, uom_list);
                row["UOM"] = dtUOM.Rows[0]["UomId"];
                row["UomIndex"] = "0";
                
                #region Expire
                row["IsExpire"] = item.IsExpire;
                #endregion
                
                decimal currentQty = MyHelper.GetItemQty(item.ItemId, Convert.ToInt32(lkpStore.EditValue));
                currentQty = MyHelper.getCalculatedUomQty(currentQty, Convert.ToByte(row["UomIndex"]), MyHelper.FractionToDouble(item.MediumUOMFactor), MyHelper.FractionToDouble(item.LargeUOMFactor));
                row["CurrentQty"] = Math.Round(decimal.ToDouble(currentQty),4);

                row["ParentItemId"] = item.mtrxParentItem;
                row["M1"] = item.mtrxAttribute1;
                row["M2"] = item.mtrxAttribute2;
                row["M3"] = item.mtrxAttribute3;
            }
        }

        private void BindDataSources()
        {
            DB = new DAL.ERPDataContext();            

            #region OpenBalance
            dtOpenBalance.Columns.Add("OpenBalanceId");
            dtOpenBalance.Columns.Add("OpenDate");
            dtOpenBalance.Columns.Add("StoreId");
            dtOpenBalance.Columns.Add("ItemId");
            dtOpenBalance.Columns.Add("ItemCode1");
            dtOpenBalance.Columns.Add("ItemCode2");
            dtOpenBalance.Columns.Add("UOM");
            dtOpenBalance.Columns.Add("UomIndex");
            dtOpenBalance.Columns.Add("Qty");
            dtOpenBalance.Columns.Add("PurchasePrice");
            dtOpenBalance.Columns.Add("MainPrice");
            dtOpenBalance.Columns.Add("SellPrice");
            dtOpenBalance.Columns.Add("DiscountValue");
            dtOpenBalance.Columns.Add("DiscountRatio");
            dtOpenBalance.Columns.Add("TotalPurchasePrice");
            dtOpenBalance.Columns.Add("TotalSellPrice");
            dtOpenBalance.Columns.Add("CurrentQty");
            dtOpenBalance.Columns.Add("MediumUOMFactor");
            dtOpenBalance.Columns.Add("LargeUOMFactor");
            
            dtOpenBalance.Columns.Add("ParentItemId");
            dtOpenBalance.Columns.Add("M1");
            dtOpenBalance.Columns.Add("M2");
            dtOpenBalance.Columns.Add("M3");

            #region Expire
            dtOpenBalance.Columns.Add("Expire");
            dtOpenBalance.Columns.Add("Batch");
            dtOpenBalance.Columns.Add("IsExpire");
            #endregion

            grdOpenBalance.DataSource = dtOpenBalance;
            #endregion

            #region UOM
            repUOM.DisplayMember = "Uom";
            repUOM.ValueMember = "UomId";
            repUOM.DataSource = MyHelper.GetUomDataTable(dtUOM);
            #endregion
            
            #region Get Stores
            int defaultStoreId = 0;
            var stores_table = MyHelper.Get_Stores(true, out defaultStoreId, frmMain.st_Store.UserChangeStore, frmMain.st_Store.DefaultStore,
                Shared.UserId);
            lkpStore.Properties.DataSource = stores_table;

            lkpStore.Properties.DisplayMember = "StoreNameAr";
            lkpStore.Properties.ValueMember = "StoreId";
            lkpStore.EditValue = defaultStoreId;
            #endregion
        }

        private void GetInvoiceDetails(int storeId)
        {
            dtOpenBalance.Rows.Clear();
            DB = new DAL.ERPDataContext();
            Get_Items();
            var details = (from d in DB.IC_OpenBalances
                           where d.StoreId == storeId
                           join i in DB.IC_Items on d.ItemId equals i.ItemId
                           orderby d.OpenBalanceId

                           select new { detail = d, item = i }).ToList();

            foreach (var d in details)
            {
                DataRow row = dtOpenBalance.NewRow();
                row["OpenBalanceId"] = d.detail.OpenBalanceId;
                row["OpenDate"] = d.detail.OpenDate;
                row["StoreId"] = d.detail.StoreId;                
                row["ItemId"] = d.detail.ItemId;
                row["ItemCode1"] = d.item.ItemCode1;
                row["ItemCode2"] = d.item.ItemCode2;
                row["UOM"] = d.detail.UOMId;
                row["UomIndex"] = d.detail.UOMIndex;
                row["Qty"] = decimal.ToDouble(d.detail.Qty);
                row["PurchasePrice"] = decimal.ToDouble(d.detail.PurchasePrice);
                row["MainPrice"] = decimal.ToDouble(d.detail.PurchasePrice - d.detail.DiscountValue);
                row["SellPrice"] = decimal.ToDouble(d.detail.SellPrice);
                row["DiscountValue"] = decimal.ToDouble(d.detail.DiscountValue);
                row["DiscountRatio"] = decimal.ToDouble(d.detail.DiscountRatio);

                row["MediumUOMFactor"] = MyHelper.FractionToDouble(d.item.MediumUOMFactor);
                row["LargeUOMFactor"] = MyHelper.FractionToDouble(d.item.LargeUOMFactor);

                #region Expire
                if (d.detail.Expire.HasValue)
                    row["Expire"] = d.detail.Expire;
                row["Batch"] = d.detail.Batch;
                row["IsExpire"] = d.item.IsExpire;
                #endregion

                //get store qty                                
                decimal currentQty = MyHelper.GetItemQty(d.detail.ItemId, storeId);
                currentQty = MyHelper.getCalculatedUomQty(currentQty, d.detail.UOMIndex, MyHelper.FractionToDouble(d.item.MediumUOMFactor), MyHelper.FractionToDouble(d.item.LargeUOMFactor));
                row["CurrentQty"] = Math.Round(decimal.ToDouble(currentQty), 4);
                //row["CurrentQty"] = decimal.ToDouble(currentQty);
                row["TotalPurchasePrice"] = decimal.ToDouble(d.detail.TotalPurchasePrice);
                row["TotalSellPrice"] = decimal.ToDouble(d.detail.TotalSellPrice);

                row["ParentItemId"] = d.item.mtrxParentItem;
                row["M1"] = d.item.mtrxAttribute1;
                row["M2"] = d.item.mtrxAttribute2;
                row["M3"] = d.item.mtrxAttribute3;

                dtOpenBalance.Rows.Add(row);
            }
            dtOpenBalance.AcceptChanges();
            DataModified = false;
        }

        public void Get_Items()
        {
            #region Get Items
            if (linqServerModeSource1.QueryableSource == null)
            {                
                linqServerModeSource1.KeyExpression = "ItemId";
                linqServerModeSource1.QueryableSource = from i in new DAL.ERPDataContext().IC_Items
                                                        where i.ItemType != (int)ItemType.MatrixParent
                                                           && i.ItemType != (int)ItemType.Service
                                                           && i.ItemType != (int)ItemType.Subtotal
                                                        select new
                                                        {
                                                            ItemCode1 = i.ItemCode1,
                                                            ItemCode2 = i.ItemCode2,
                                                            ItemId = i.ItemId,
                                                            ItemNameAr = i.ItemNameAr,
                                                            ItemNameEn = i.ItemNameEn,
                                                            i.MinQty,
                                                            i.MaxQty,
                                                            i.ReorderLevel,
                                                            i.IsExpire
                                                        };
                repItems.ServerMode = true;
                repItems.DataSource = linqServerModeSource1;
                                
            }
            #endregion
        }

        private void repItems_Popup(object sender, EventArgs e)
        {
            (sender as GridLookUpEdit).Properties.View.ClearColumnsFilter();

            Get_Items();
        }

        private void repItems_Enter(object sender, EventArgs e)
        {
            Get_Items();
        }

        private void repUOM_CustomDisplayText(object sender, DevExpress.XtraEditors.Controls.CustomDisplayTextEventArgs e)
        {
            try
            {
                e.DisplayText = uom_list.Where(x => x.UOMId == Convert.ToInt32(e.Value)).FirstOrDefault().UOM;
            }
            catch
            {
            }
        }

        private bool ValidData()
        {
            if(dtDate.EditValue==null ||lkpStore.EditValue==null)
            {
                XtraMessageBox.Show(Shared.IsEnglish ? ResICEn.txtValidateStoreQty : ResICAr.txtValidateStoreQty, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return false;
            }
            return true;
        }

        DialogResult ChangesMade()
        {
            if (
                DataModified ||
                dtOpenBalance.GetChanges(DataRowState.Added) != null ||
                dtOpenBalance.GetChanges(DataRowState.Modified) != null ||
                dtOpenBalance.GetChanges(DataRowState.Deleted) != null
                )
            {
                DialogResult r = XtraMessageBox.Show(Shared.IsEnglish ? ResICEn.MsgDataModified : ResICAr.MsgDataModified, "", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                if (r == DialogResult.Yes)
                {
                    if (!ValidData())
                        return DialogResult.Cancel;

                    barBtn_Save.PerformClick();
                    return r;
                }
                else if (r == DialogResult.No)
                {
                    // no thing made, continue closing or do next or do previous
                    return r;
                }
                else if (r == DialogResult.Cancel)
                {
                    //cancel form action
                    return r;
                }
            }
            return DialogResult.No;
        }

        void DoValidate()
        {
            dtDate.DoValidate();
            lkpStore.DoValidate();            
            dtOpenBalance.AcceptChanges();
        }

        private void barBtn_Help_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "الأرصدة الافتتاحية");
        }

        private void rep_ExpireDate_CustomDisplayText(object sender, DevExpress.XtraEditors.Controls.CustomDisplayTextEventArgs e)
        {
            #region Expire
            if (e.Value == null)
                return;
            try
            {
                DateTime date = Convert.ToDateTime(e.Value);
                e.DisplayText = date.GetDateTimeFormats('y')[0];
            }
            catch
            { }
            #endregion
        }

        private void gridView1_ShowingEditor(object sender, CancelEventArgs e)
        {
            try
            {
                #region Expire
                if (gridView1.FocusedColumn == col_Expire)
                {
                    bool IsExpire = Convert.ToBoolean(gridView1.GetFocusedRowCellValue(col_IsExpire));
                    e.Cancel = !IsExpire;
                }
                #endregion
            }
            catch { }
        }

    }
}