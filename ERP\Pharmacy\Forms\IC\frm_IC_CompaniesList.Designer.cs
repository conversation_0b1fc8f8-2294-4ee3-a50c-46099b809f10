﻿namespace Pharmacy.Forms
{
    partial class frm_IC_CompaniesList
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_IC_CompaniesList));
            this.barManager1 = new DevExpress.XtraBars.BarManager();
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtn_Help = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnPrint = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnRefresh = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnOpen = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnNew = new DevExpress.XtraBars.BarButtonItem();
            this.barBtn_Close = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.grdCompany = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colCompanyNameEn = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colCompanyNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colCompanyCode = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colCompanyId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.navBarControl1 = new DevExpress.XtraNavBar.NavBarControl();
            this.NBG_Tasks = new DevExpress.XtraNavBar.NavBarGroup();
            this.NBI_Items = new DevExpress.XtraNavBar.NavBarItem();
            this.NBG_Reports = new DevExpress.XtraNavBar.NavBarGroup();
            this.NBI_rptItemQty = new DevExpress.XtraNavBar.NavBarItem();
            this.NBI_rptItemReorder = new DevExpress.XtraNavBar.NavBarItem();
            this.NBI_rptItemMinSell = new DevExpress.XtraNavBar.NavBarItem();
            this.NBI_rptItemMaxSell = new DevExpress.XtraNavBar.NavBarItem();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdCompany)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.navBarControl1)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnNew,
            this.barBtnOpen,
            this.barBtn_Help,
            this.barBtn_Close,
            this.barBtnRefresh,
            this.barBtnPrint});
            this.barManager1.MaxItemId = 28;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(567, 147);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_Help),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnPrint),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnRefresh),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnOpen),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnNew),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.KeyTip, this.barBtn_Close, "", false, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.Standard, "", "")});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtn_Help
            // 
            resources.ApplyResources(this.barBtn_Help, "barBtn_Help");
            this.barBtn_Help.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtn_Help.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtn_Help.Id = 2;
            this.barBtn_Help.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtn_Help.Name = "barBtn_Help";
            this.barBtn_Help.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtn_Help.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Help_ItemClick);
            // 
            // barBtnPrint
            // 
            resources.ApplyResources(this.barBtnPrint, "barBtnPrint");
            this.barBtnPrint.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnPrint.Glyph = global::Pharmacy.Properties.Resources.prnt;
            this.barBtnPrint.Id = 27;
            this.barBtnPrint.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.P));
            this.barBtnPrint.Name = "barBtnPrint";
            this.barBtnPrint.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnPrint.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnPrint_ItemClick);
            // 
            // barBtnRefresh
            // 
            resources.ApplyResources(this.barBtnRefresh, "barBtnRefresh");
            this.barBtnRefresh.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnRefresh.Glyph = global::Pharmacy.Properties.Resources.refresh;
            this.barBtnRefresh.Id = 26;
            this.barBtnRefresh.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.R));
            this.barBtnRefresh.Name = "barBtnRefresh";
            this.barBtnRefresh.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnRefresh.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Refresh_ItemClick);
            // 
            // barBtnOpen
            // 
            resources.ApplyResources(this.barBtnOpen, "barBtnOpen");
            this.barBtnOpen.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnOpen.Glyph = global::Pharmacy.Properties.Resources.open;
            this.barBtnOpen.Id = 1;
            this.barBtnOpen.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.O));
            this.barBtnOpen.Name = "barBtnOpen";
            this.barBtnOpen.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnOpen.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Open_ItemClick);
            // 
            // barBtnNew
            // 
            resources.ApplyResources(this.barBtnNew, "barBtnNew");
            this.barBtnNew.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnNew.Glyph = global::Pharmacy.Properties.Resources._new;
            this.barBtnNew.Id = 0;
            this.barBtnNew.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.N));
            this.barBtnNew.Name = "barBtnNew";
            this.barBtnNew.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnNew.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_New_ItemClick);
            // 
            // barBtn_Close
            // 
            resources.ApplyResources(this.barBtn_Close, "barBtn_Close");
            this.barBtn_Close.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtn_Close.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtn_Close.Id = 25;
            this.barBtn_Close.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtn_Close.Name = "barBtn_Close";
            this.barBtn_Close.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtn_Close.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Close_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlTop.Appearance.FontSizeDelta")));
            this.barDockControlTop.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlTop.Appearance.FontStyleDelta")));
            this.barDockControlTop.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlTop.Appearance.GradientMode")));
            this.barDockControlTop.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlTop.Appearance.Image")));
            this.barDockControlTop.CausesValidation = false;
            // 
            // barDockControlBottom
            // 
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlBottom.Appearance.FontSizeDelta")));
            this.barDockControlBottom.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlBottom.Appearance.FontStyleDelta")));
            this.barDockControlBottom.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlBottom.Appearance.GradientMode")));
            this.barDockControlBottom.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlBottom.Appearance.Image")));
            this.barDockControlBottom.CausesValidation = false;
            // 
            // barDockControlLeft
            // 
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlLeft.Appearance.FontSizeDelta")));
            this.barDockControlLeft.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlLeft.Appearance.FontStyleDelta")));
            this.barDockControlLeft.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlLeft.Appearance.GradientMode")));
            this.barDockControlLeft.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlLeft.Appearance.Image")));
            this.barDockControlLeft.CausesValidation = false;
            // 
            // barDockControlRight
            // 
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlRight.Appearance.FontSizeDelta")));
            this.barDockControlRight.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlRight.Appearance.FontStyleDelta")));
            this.barDockControlRight.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlRight.Appearance.GradientMode")));
            this.barDockControlRight.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlRight.Appearance.Image")));
            this.barDockControlRight.CausesValidation = false;
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemTextEdit1.Mask.AutoComplete")));
            this.repositoryItemTextEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.BeepOnError")));
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemTextEdit1.Mask.MaskType")));
            this.repositoryItemTextEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemTextEdit1.Mask.PlaceHolder")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // grdCompany
            // 
            resources.ApplyResources(this.grdCompany, "grdCompany");
            this.grdCompany.Cursor = System.Windows.Forms.Cursors.Default;
            this.grdCompany.EmbeddedNavigator.AccessibleDescription = resources.GetString("grdCompany.EmbeddedNavigator.AccessibleDescription");
            this.grdCompany.EmbeddedNavigator.AccessibleName = resources.GetString("grdCompany.EmbeddedNavigator.AccessibleName");
            this.grdCompany.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grdCompany.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grdCompany.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grdCompany.EmbeddedNavigator.Anchor")));
            this.grdCompany.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grdCompany.EmbeddedNavigator.BackgroundImage")));
            this.grdCompany.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grdCompany.EmbeddedNavigator.BackgroundImageLayout")));
            this.grdCompany.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grdCompany.EmbeddedNavigator.ImeMode")));
            this.grdCompany.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grdCompany.EmbeddedNavigator.MaximumSize")));
            this.grdCompany.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grdCompany.EmbeddedNavigator.TextLocation")));
            this.grdCompany.EmbeddedNavigator.ToolTip = resources.GetString("grdCompany.EmbeddedNavigator.ToolTip");
            this.grdCompany.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grdCompany.EmbeddedNavigator.ToolTipIconType")));
            this.grdCompany.EmbeddedNavigator.ToolTipTitle = resources.GetString("grdCompany.EmbeddedNavigator.ToolTipTitle");
            this.grdCompany.MainView = this.gridView1;
            this.grdCompany.MenuManager = this.barManager1;
            this.grdCompany.Name = "grdCompany";
            this.grdCompany.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            this.grdCompany.DoubleClick += new System.EventHandler(this.grdCompany_DoubleClick);
            // 
            // gridView1
            // 
            this.gridView1.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.HeaderPanel.FontSizeDelta")));
            this.gridView1.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.HeaderPanel.FontStyleDelta")));
            this.gridView1.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.HeaderPanel.GradientMode")));
            this.gridView1.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.HeaderPanel.Image")));
            this.gridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.Row.FontSizeDelta")));
            this.gridView1.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.Row.FontStyleDelta")));
            this.gridView1.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.Row.GradientMode")));
            this.gridView1.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.Row.Image")));
            this.gridView1.Appearance.Row.Options.UseTextOptions = true;
            this.gridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.AppearancePrint.FooterPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.BorderColor")));
            this.gridView1.AppearancePrint.FooterPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.FontSizeDelta")));
            this.gridView1.AppearancePrint.FooterPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.FontStyleDelta")));
            this.gridView1.AppearancePrint.FooterPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.ForeColor")));
            this.gridView1.AppearancePrint.FooterPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.GradientMode")));
            this.gridView1.AppearancePrint.FooterPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.Image")));
            this.gridView1.AppearancePrint.FooterPanel.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.FooterPanel.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.GroupFooter.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.BorderColor")));
            this.gridView1.AppearancePrint.GroupFooter.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.FontSizeDelta")));
            this.gridView1.AppearancePrint.GroupFooter.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.FontStyleDelta")));
            this.gridView1.AppearancePrint.GroupFooter.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.ForeColor")));
            this.gridView1.AppearancePrint.GroupFooter.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.GradientMode")));
            this.gridView1.AppearancePrint.GroupFooter.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.Image")));
            this.gridView1.AppearancePrint.GroupFooter.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.GroupFooter.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.GroupRow.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupRow.BorderColor")));
            this.gridView1.AppearancePrint.GroupRow.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.GroupRow.FontSizeDelta")));
            this.gridView1.AppearancePrint.GroupRow.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.GroupRow.FontStyleDelta")));
            this.gridView1.AppearancePrint.GroupRow.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupRow.ForeColor")));
            this.gridView1.AppearancePrint.GroupRow.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.GroupRow.GradientMode")));
            this.gridView1.AppearancePrint.GroupRow.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.GroupRow.Image")));
            this.gridView1.AppearancePrint.GroupRow.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.GroupRow.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.BorderColor")));
            this.gridView1.AppearancePrint.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.FontSizeDelta")));
            this.gridView1.AppearancePrint.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.FontStyleDelta")));
            this.gridView1.AppearancePrint.HeaderPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.ForeColor")));
            this.gridView1.AppearancePrint.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.GradientMode")));
            this.gridView1.AppearancePrint.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.Image")));
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.Lines.BackColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Lines.BackColor")));
            this.gridView1.AppearancePrint.Lines.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.Lines.FontSizeDelta")));
            this.gridView1.AppearancePrint.Lines.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.Lines.FontStyleDelta")));
            this.gridView1.AppearancePrint.Lines.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Lines.ForeColor")));
            this.gridView1.AppearancePrint.Lines.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.Lines.GradientMode")));
            this.gridView1.AppearancePrint.Lines.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.Lines.Image")));
            this.gridView1.AppearancePrint.Lines.Options.UseBackColor = true;
            this.gridView1.AppearancePrint.Lines.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.Row.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Row.BorderColor")));
            this.gridView1.AppearancePrint.Row.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.Row.FontSizeDelta")));
            this.gridView1.AppearancePrint.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.Row.FontStyleDelta")));
            this.gridView1.AppearancePrint.Row.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Row.ForeColor")));
            this.gridView1.AppearancePrint.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.Row.GradientMode")));
            this.gridView1.AppearancePrint.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.Row.Image")));
            this.gridView1.AppearancePrint.Row.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.Row.Options.UseForeColor = true;
            resources.ApplyResources(this.gridView1, "gridView1");
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colCompanyNameEn,
            this.colCompanyNameAr,
            this.colCompanyCode,
            this.colCompanyId});
            this.gridView1.GridControl = this.grdCompany;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView1.OptionsView.ShowAutoFilterRow = true;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.OptionsView.ShowIndicator = false;
            // 
            // colCompanyNameEn
            // 
            resources.ApplyResources(this.colCompanyNameEn, "colCompanyNameEn");
            this.colCompanyNameEn.FieldName = "CompanyNameEn";
            this.colCompanyNameEn.Name = "colCompanyNameEn";
            this.colCompanyNameEn.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // colCompanyNameAr
            // 
            resources.ApplyResources(this.colCompanyNameAr, "colCompanyNameAr");
            this.colCompanyNameAr.FieldName = "CompanyNameAr";
            this.colCompanyNameAr.Name = "colCompanyNameAr";
            this.colCompanyNameAr.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // colCompanyCode
            // 
            resources.ApplyResources(this.colCompanyCode, "colCompanyCode");
            this.colCompanyCode.FieldName = "CompanyCode";
            this.colCompanyCode.Name = "colCompanyCode";
            this.colCompanyCode.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.colCompanyCode.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // colCompanyId
            // 
            resources.ApplyResources(this.colCompanyId, "colCompanyId");
            this.colCompanyId.FieldName = "CompanyId";
            this.colCompanyId.Name = "colCompanyId";
            this.colCompanyId.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // navBarControl1
            // 
            resources.ApplyResources(this.navBarControl1, "navBarControl1");
            this.navBarControl1.ActiveGroup = this.NBG_Tasks;
            this.navBarControl1.Appearance.Background.FontSizeDelta = ((int)(resources.GetObject("navBarControl1.Appearance.Background.FontSizeDelta")));
            this.navBarControl1.Appearance.Background.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("navBarControl1.Appearance.Background.FontStyleDelta")));
            this.navBarControl1.Appearance.Background.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("navBarControl1.Appearance.Background.GradientMode")));
            this.navBarControl1.Appearance.Background.Image = ((System.Drawing.Image)(resources.GetObject("navBarControl1.Appearance.Background.Image")));
            this.navBarControl1.Appearance.Background.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.Background.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.Button.FontSizeDelta = ((int)(resources.GetObject("navBarControl1.Appearance.Button.FontSizeDelta")));
            this.navBarControl1.Appearance.Button.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("navBarControl1.Appearance.Button.FontStyleDelta")));
            this.navBarControl1.Appearance.Button.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("navBarControl1.Appearance.Button.GradientMode")));
            this.navBarControl1.Appearance.Button.Image = ((System.Drawing.Image)(resources.GetObject("navBarControl1.Appearance.Button.Image")));
            this.navBarControl1.Appearance.Button.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.Button.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.ButtonDisabled.FontSizeDelta = ((int)(resources.GetObject("navBarControl1.Appearance.ButtonDisabled.FontSizeDelta")));
            this.navBarControl1.Appearance.ButtonDisabled.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("navBarControl1.Appearance.ButtonDisabled.FontStyleDelta")));
            this.navBarControl1.Appearance.ButtonDisabled.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("navBarControl1.Appearance.ButtonDisabled.GradientMode")));
            this.navBarControl1.Appearance.ButtonDisabled.Image = ((System.Drawing.Image)(resources.GetObject("navBarControl1.Appearance.ButtonDisabled.Image")));
            this.navBarControl1.Appearance.ButtonDisabled.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.ButtonDisabled.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.ButtonHotTracked.FontSizeDelta = ((int)(resources.GetObject("navBarControl1.Appearance.ButtonHotTracked.FontSizeDelta")));
            this.navBarControl1.Appearance.ButtonHotTracked.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("navBarControl1.Appearance.ButtonHotTracked.FontStyleDelta")));
            this.navBarControl1.Appearance.ButtonHotTracked.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("navBarControl1.Appearance.ButtonHotTracked.GradientMode")));
            this.navBarControl1.Appearance.ButtonHotTracked.Image = ((System.Drawing.Image)(resources.GetObject("navBarControl1.Appearance.ButtonHotTracked.Image")));
            this.navBarControl1.Appearance.ButtonHotTracked.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.ButtonHotTracked.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.ButtonPressed.FontSizeDelta = ((int)(resources.GetObject("navBarControl1.Appearance.ButtonPressed.FontSizeDelta")));
            this.navBarControl1.Appearance.ButtonPressed.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("navBarControl1.Appearance.ButtonPressed.FontStyleDelta")));
            this.navBarControl1.Appearance.ButtonPressed.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("navBarControl1.Appearance.ButtonPressed.GradientMode")));
            this.navBarControl1.Appearance.ButtonPressed.Image = ((System.Drawing.Image)(resources.GetObject("navBarControl1.Appearance.ButtonPressed.Image")));
            this.navBarControl1.Appearance.ButtonPressed.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.ButtonPressed.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.GroupBackground.FontSizeDelta = ((int)(resources.GetObject("navBarControl1.Appearance.GroupBackground.FontSizeDelta")));
            this.navBarControl1.Appearance.GroupBackground.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("navBarControl1.Appearance.GroupBackground.FontStyleDelta")));
            this.navBarControl1.Appearance.GroupBackground.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("navBarControl1.Appearance.GroupBackground.GradientMode")));
            this.navBarControl1.Appearance.GroupBackground.Image = ((System.Drawing.Image)(resources.GetObject("navBarControl1.Appearance.GroupBackground.Image")));
            this.navBarControl1.Appearance.GroupBackground.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.GroupBackground.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.GroupHeader.FontSizeDelta = ((int)(resources.GetObject("navBarControl1.Appearance.GroupHeader.FontSizeDelta")));
            this.navBarControl1.Appearance.GroupHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("navBarControl1.Appearance.GroupHeader.FontStyleDelta")));
            this.navBarControl1.Appearance.GroupHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("navBarControl1.Appearance.GroupHeader.GradientMode")));
            this.navBarControl1.Appearance.GroupHeader.Image = ((System.Drawing.Image)(resources.GetObject("navBarControl1.Appearance.GroupHeader.Image")));
            this.navBarControl1.Appearance.GroupHeader.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.GroupHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.GroupHeaderActive.FontSizeDelta = ((int)(resources.GetObject("navBarControl1.Appearance.GroupHeaderActive.FontSizeDelta")));
            this.navBarControl1.Appearance.GroupHeaderActive.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("navBarControl1.Appearance.GroupHeaderActive.FontStyleDelta")));
            this.navBarControl1.Appearance.GroupHeaderActive.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("navBarControl1.Appearance.GroupHeaderActive.GradientMode")));
            this.navBarControl1.Appearance.GroupHeaderActive.Image = ((System.Drawing.Image)(resources.GetObject("navBarControl1.Appearance.GroupHeaderActive.Image")));
            this.navBarControl1.Appearance.GroupHeaderActive.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.GroupHeaderActive.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.GroupHeaderHotTracked.FontSizeDelta = ((int)(resources.GetObject("navBarControl1.Appearance.GroupHeaderHotTracked.FontSizeDelta")));
            this.navBarControl1.Appearance.GroupHeaderHotTracked.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("navBarControl1.Appearance.GroupHeaderHotTracked.FontStyleDelta")));
            this.navBarControl1.Appearance.GroupHeaderHotTracked.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("navBarControl1.Appearance.GroupHeaderHotTracked.GradientMode")));
            this.navBarControl1.Appearance.GroupHeaderHotTracked.Image = ((System.Drawing.Image)(resources.GetObject("navBarControl1.Appearance.GroupHeaderHotTracked.Image")));
            this.navBarControl1.Appearance.GroupHeaderHotTracked.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.GroupHeaderHotTracked.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.GroupHeaderPressed.FontSizeDelta = ((int)(resources.GetObject("navBarControl1.Appearance.GroupHeaderPressed.FontSizeDelta")));
            this.navBarControl1.Appearance.GroupHeaderPressed.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("navBarControl1.Appearance.GroupHeaderPressed.FontStyleDelta")));
            this.navBarControl1.Appearance.GroupHeaderPressed.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("navBarControl1.Appearance.GroupHeaderPressed.GradientMode")));
            this.navBarControl1.Appearance.GroupHeaderPressed.Image = ((System.Drawing.Image)(resources.GetObject("navBarControl1.Appearance.GroupHeaderPressed.Image")));
            this.navBarControl1.Appearance.GroupHeaderPressed.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.GroupHeaderPressed.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.Hint.FontSizeDelta = ((int)(resources.GetObject("navBarControl1.Appearance.Hint.FontSizeDelta")));
            this.navBarControl1.Appearance.Hint.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("navBarControl1.Appearance.Hint.FontStyleDelta")));
            this.navBarControl1.Appearance.Hint.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("navBarControl1.Appearance.Hint.GradientMode")));
            this.navBarControl1.Appearance.Hint.Image = ((System.Drawing.Image)(resources.GetObject("navBarControl1.Appearance.Hint.Image")));
            this.navBarControl1.Appearance.Hint.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.Hint.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.Item.FontSizeDelta = ((int)(resources.GetObject("navBarControl1.Appearance.Item.FontSizeDelta")));
            this.navBarControl1.Appearance.Item.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("navBarControl1.Appearance.Item.FontStyleDelta")));
            this.navBarControl1.Appearance.Item.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("navBarControl1.Appearance.Item.GradientMode")));
            this.navBarControl1.Appearance.Item.Image = ((System.Drawing.Image)(resources.GetObject("navBarControl1.Appearance.Item.Image")));
            this.navBarControl1.Appearance.Item.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.Item.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.ItemActive.FontSizeDelta = ((int)(resources.GetObject("navBarControl1.Appearance.ItemActive.FontSizeDelta")));
            this.navBarControl1.Appearance.ItemActive.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("navBarControl1.Appearance.ItemActive.FontStyleDelta")));
            this.navBarControl1.Appearance.ItemActive.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("navBarControl1.Appearance.ItemActive.GradientMode")));
            this.navBarControl1.Appearance.ItemActive.Image = ((System.Drawing.Image)(resources.GetObject("navBarControl1.Appearance.ItemActive.Image")));
            this.navBarControl1.Appearance.ItemActive.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.ItemActive.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.ItemDisabled.FontSizeDelta = ((int)(resources.GetObject("navBarControl1.Appearance.ItemDisabled.FontSizeDelta")));
            this.navBarControl1.Appearance.ItemDisabled.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("navBarControl1.Appearance.ItemDisabled.FontStyleDelta")));
            this.navBarControl1.Appearance.ItemDisabled.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("navBarControl1.Appearance.ItemDisabled.GradientMode")));
            this.navBarControl1.Appearance.ItemDisabled.Image = ((System.Drawing.Image)(resources.GetObject("navBarControl1.Appearance.ItemDisabled.Image")));
            this.navBarControl1.Appearance.ItemDisabled.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.ItemDisabled.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.ItemHotTracked.FontSizeDelta = ((int)(resources.GetObject("navBarControl1.Appearance.ItemHotTracked.FontSizeDelta")));
            this.navBarControl1.Appearance.ItemHotTracked.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("navBarControl1.Appearance.ItemHotTracked.FontStyleDelta")));
            this.navBarControl1.Appearance.ItemHotTracked.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("navBarControl1.Appearance.ItemHotTracked.GradientMode")));
            this.navBarControl1.Appearance.ItemHotTracked.Image = ((System.Drawing.Image)(resources.GetObject("navBarControl1.Appearance.ItemHotTracked.Image")));
            this.navBarControl1.Appearance.ItemHotTracked.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.ItemHotTracked.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.ItemPressed.FontSizeDelta = ((int)(resources.GetObject("navBarControl1.Appearance.ItemPressed.FontSizeDelta")));
            this.navBarControl1.Appearance.ItemPressed.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("navBarControl1.Appearance.ItemPressed.FontStyleDelta")));
            this.navBarControl1.Appearance.ItemPressed.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("navBarControl1.Appearance.ItemPressed.GradientMode")));
            this.navBarControl1.Appearance.ItemPressed.Image = ((System.Drawing.Image)(resources.GetObject("navBarControl1.Appearance.ItemPressed.Image")));
            this.navBarControl1.Appearance.ItemPressed.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.ItemPressed.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.LinkDropTarget.FontSizeDelta = ((int)(resources.GetObject("navBarControl1.Appearance.LinkDropTarget.FontSizeDelta")));
            this.navBarControl1.Appearance.LinkDropTarget.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("navBarControl1.Appearance.LinkDropTarget.FontStyleDelta")));
            this.navBarControl1.Appearance.LinkDropTarget.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("navBarControl1.Appearance.LinkDropTarget.GradientMode")));
            this.navBarControl1.Appearance.LinkDropTarget.Image = ((System.Drawing.Image)(resources.GetObject("navBarControl1.Appearance.LinkDropTarget.Image")));
            this.navBarControl1.Appearance.LinkDropTarget.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.LinkDropTarget.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.NavigationPaneHeader.FontSizeDelta = ((int)(resources.GetObject("navBarControl1.Appearance.NavigationPaneHeader.FontSizeDelta")));
            this.navBarControl1.Appearance.NavigationPaneHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("navBarControl1.Appearance.NavigationPaneHeader.FontStyleDelta")));
            this.navBarControl1.Appearance.NavigationPaneHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("navBarControl1.Appearance.NavigationPaneHeader.GradientMode")));
            this.navBarControl1.Appearance.NavigationPaneHeader.Image = ((System.Drawing.Image)(resources.GetObject("navBarControl1.Appearance.NavigationPaneHeader.Image")));
            this.navBarControl1.Appearance.NavigationPaneHeader.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.NavigationPaneHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.navBarControl1.Groups.AddRange(new DevExpress.XtraNavBar.NavBarGroup[] {
            this.NBG_Tasks,
            this.NBG_Reports});
            this.navBarControl1.Items.AddRange(new DevExpress.XtraNavBar.NavBarItem[] {
            this.NBI_rptItemQty,
            this.NBI_Items,
            this.NBI_rptItemReorder,
            this.NBI_rptItemMinSell,
            this.NBI_rptItemMaxSell});
            this.navBarControl1.Name = "navBarControl1";
            this.navBarControl1.OptionsNavPane.CollapsedWidth = ((int)(resources.GetObject("resource.CollapsedWidth")));
            this.navBarControl1.OptionsNavPane.ExpandedWidth = ((int)(resources.GetObject("resource.ExpandedWidth")));
            // 
            // NBG_Tasks
            // 
            resources.ApplyResources(this.NBG_Tasks, "NBG_Tasks");
            this.NBG_Tasks.Expanded = true;
            this.NBG_Tasks.GroupStyle = DevExpress.XtraNavBar.NavBarGroupStyle.LargeIconsText;
            this.NBG_Tasks.ItemLinks.AddRange(new DevExpress.XtraNavBar.NavBarItemLink[] {
            new DevExpress.XtraNavBar.NavBarItemLink(this.NBI_Items)});
            this.NBG_Tasks.Name = "NBG_Tasks";
            // 
            // NBI_Items
            // 
            resources.ApplyResources(this.NBI_Items, "NBI_Items");
            this.NBI_Items.Name = "NBI_Items";
            this.NBI_Items.LinkClicked += new DevExpress.XtraNavBar.NavBarLinkEventHandler(this.NBI_Items_LinkClicked);
            // 
            // NBG_Reports
            // 
            resources.ApplyResources(this.NBG_Reports, "NBG_Reports");
            this.NBG_Reports.Expanded = true;
            this.NBG_Reports.GroupStyle = DevExpress.XtraNavBar.NavBarGroupStyle.LargeIconsText;
            this.NBG_Reports.ItemLinks.AddRange(new DevExpress.XtraNavBar.NavBarItemLink[] {
            new DevExpress.XtraNavBar.NavBarItemLink(this.NBI_rptItemQty),
            new DevExpress.XtraNavBar.NavBarItemLink(this.NBI_rptItemReorder),
            new DevExpress.XtraNavBar.NavBarItemLink(this.NBI_rptItemMinSell),
            new DevExpress.XtraNavBar.NavBarItemLink(this.NBI_rptItemMaxSell)});
            this.NBG_Reports.Name = "NBG_Reports";
            // 
            // NBI_rptItemQty
            // 
            resources.ApplyResources(this.NBI_rptItemQty, "NBI_rptItemQty");
            this.NBI_rptItemQty.Name = "NBI_rptItemQty";
            this.NBI_rptItemQty.LinkClicked += new DevExpress.XtraNavBar.NavBarLinkEventHandler(this.NBI_LinkClicked);
            // 
            // NBI_rptItemReorder
            // 
            resources.ApplyResources(this.NBI_rptItemReorder, "NBI_rptItemReorder");
            this.NBI_rptItemReorder.Name = "NBI_rptItemReorder";
            this.NBI_rptItemReorder.LinkClicked += new DevExpress.XtraNavBar.NavBarLinkEventHandler(this.NBI_LinkClicked);
            // 
            // NBI_rptItemMinSell
            // 
            resources.ApplyResources(this.NBI_rptItemMinSell, "NBI_rptItemMinSell");
            this.NBI_rptItemMinSell.Name = "NBI_rptItemMinSell";
            this.NBI_rptItemMinSell.LinkClicked += new DevExpress.XtraNavBar.NavBarLinkEventHandler(this.NBI_LinkClicked);
            // 
            // NBI_rptItemMaxSell
            // 
            resources.ApplyResources(this.NBI_rptItemMaxSell, "NBI_rptItemMaxSell");
            this.NBI_rptItemMaxSell.Name = "NBI_rptItemMaxSell";
            this.NBI_rptItemMaxSell.LinkClicked += new DevExpress.XtraNavBar.NavBarLinkEventHandler(this.NBI_LinkClicked);
            // 
            // frm_IC_CompaniesList
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.navBarControl1);
            this.Controls.Add(this.grdCompany);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.Name = "frm_IC_CompaniesList";
            this.Load += new System.EventHandler(this.frm_IC_CompaniesList_Load);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdCompany)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.navBarControl1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnNew;
        private DevExpress.XtraBars.BarButtonItem barBtnOpen;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtn_Help;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraGrid.GridControl grdCompany;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn colCompanyNameEn;
        private DevExpress.XtraGrid.Columns.GridColumn colCompanyNameAr;
        private DevExpress.XtraGrid.Columns.GridColumn colCompanyCode;
        private DevExpress.XtraBars.BarButtonItem barBtn_Close;
        private DevExpress.XtraNavBar.NavBarControl navBarControl1;
        private DevExpress.XtraNavBar.NavBarGroup NBG_Tasks;
        private DevExpress.XtraNavBar.NavBarGroup NBG_Reports;
        private DevExpress.XtraNavBar.NavBarItem NBI_rptItemQty;
        private DevExpress.XtraBars.BarButtonItem barBtnRefresh;
        private DevExpress.XtraGrid.Columns.GridColumn colCompanyId;
        private DevExpress.XtraNavBar.NavBarItem NBI_Items;
        private DevExpress.XtraBars.BarButtonItem barBtnPrint;
        private DevExpress.XtraNavBar.NavBarItem NBI_rptItemReorder;
        private DevExpress.XtraNavBar.NavBarItem NBI_rptItemMinSell;
        private DevExpress.XtraNavBar.NavBarItem NBI_rptItemMaxSell;
    }
}