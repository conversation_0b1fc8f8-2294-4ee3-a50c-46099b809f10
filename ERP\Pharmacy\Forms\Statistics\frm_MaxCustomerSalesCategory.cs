﻿using DAL;
using DAL.Res;
using DevExpress.XtraPrinting;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Pharmacy.Forms
{
    public partial class frm_MaxCustomerSalesCategory : DevExpress.XtraEditors.XtraForm
    {
        ERPDataContext DB = new ERPDataContext();
        UserPriv prvlg;
        public frm_MaxCustomerSalesCategory()
        {
            InitializeComponent();
        }

        private void barBtnOk_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            DateTime From = Convert.ToDateTime(dtFromDate.EditValue);
            DateTime To = Convert.ToDateTime(dtToDate.EditValue);
            int Noofcategory = Convert.ToInt32(txt_No.EditValue);
            var Data = (from i in DB.SL_Invoices
                            //join c in DB.SL_Customers on i.CustomerId equals c.CustomerId
                        where dtFromDate.EditValue == null ? true : (i.InvoiceDate.Date >= From.Date)
                        where dtToDate.EditValue == null ? true : (i.InvoiceDate.Date <= To.Date)

                        //group i by i.CustomerId into grp
                        join c in DB.SL_Customers on i.CustomerId equals c.CustomerId
                        join cc in DB.SL_CustomerGroups on c.CategoryId equals cc.CustomerGroupId
                        group new { i, cc, c } by cc.CustomerGroupId into grp
                        select new
                        {
                            CustomerId = grp.Select(a => a.i.CustomerId).FirstOrDefault(),
                            Total = grp.Select(a => a.i.Net).Sum(),
                            cName = grp.Select(x => Shared.IsEnglish ? x.c.CusNameEn : x.c.CusNameAr).FirstOrDefault(),
                            CategoryName = grp.Select(x => Shared.IsEnglish ? x.cc.CGNameEn : x.cc.CGNameAr).FirstOrDefault(),
                            count = grp.Count(),

                        }
                        ).Distinct().OrderByDescending(x => x.Total).ToList();




            if (Noofcategory > 0)
            {
                //Result = Result.OrderByDescending(x => x.Total).Take(Noofcategory).ToList();
                var Result = (from n in Data.Take(Noofcategory)
                              group n by n.CategoryName into grp
                              let Total = Data.Take(Noofcategory).Select(a => a.Total).Sum()
                              select new
                              {
                                  Total = Math.Round((grp.Select(x => x.Total).First() / Total) * 100, 2),
                                  CategoryName = grp.Key
                              }).OrderByDescending(x => x.Total).ToList();

                chartControl1.DataSource = Result;
            }
            else
            {
                var Result = (from n in Data
                              let Total = Data.Select(a => a.Total).Sum()
                              select new
                              {
                                  Total = Math.Round((n.Total / Total) * 100, 2),
                                  CategoryName = n.CategoryName
                              }).OrderByDescending(x => x.Total).ToList();


                chartControl1.DataSource = Result;
            }

            //chartControl1.DataSource = Result;
        }

        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }
        private void printableComponentLink1_CreateReportHeaderArea(object sender, DevExpress.XtraPrinting.CreateAreaEventArgs e)
        {
            string ReportName = this.Text;
            string dateFilters = string.Empty;
            string otherFilters = string.Empty;

            //create filters line
            if (dtFromDate.EditValue != null && dtToDate.EditValue != null)
                dateFilters = (Shared.IsEnglish == true ? ResAccEn.txtFrom : ResAccAr.txtFrom) +
                    dtFromDate.DateTime.ToShortDateString() +
                    (Shared.IsEnglish == true ? ResAccEn.txtTo : ResAccAr.txtTo) +
                    dtToDate.DateTime.ToShortDateString();

            else if (dtFromDate.EditValue != null && dtToDate.EditValue == null)
                dateFilters =
                    (Shared.IsEnglish == true ? ResAccEn.txtFromDate : ResAccAr.txtFromDate) +
                    dtFromDate.DateTime.ToShortDateString();
            else if (dtFromDate.EditValue == null && dtToDate.EditValue != null)
                dateFilters = (Shared.IsEnglish == true ? ResAccEn.txtToDate : ResAccAr.txtToDate) +
                    dtToDate.DateTime.ToShortDateString();
            else
                dateFilters = "";


            ErpUtils.CreateReportHeader(e, ReportName, dateFilters, otherFilters);
        }
        private void printableComponentLink1_CreateReportFooter(object sender, DevExpress.XtraPrinting.CreateAreaEventArgs e)
        {
            RectangleF recTotal = new RectangleF((float)10, (float)17, 740, (float)25);

            e.Graph.StringFormat = Shared.IsEnglish ? new BrickStringFormat(StringAlignment.Near) : new BrickStringFormat(StringAlignment.Far);
            e.Graph.Font = new Font("Times New Roman", 13, FontStyle.Regular);
            e.Graph.ForeColor = Color.Black;
            e.Graph.DefaultBrickStyle.BorderColor = Color.Transparent;
            e.Graph.BackColor = Color.Snow;


            //string total = txtTotal.Text;

            //e.Graph.DrawString(total, recTotal);            
        }

        private void frm_MaxCustomerSalesCategory_Load(object sender, EventArgs e)
        {
            LoadPrivilege();
            dtFromDate.EditValue = MyHelper.Get_Server_DateTime();
            dtToDate.EditValue = MyHelper.Get_Server_DateTime();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (this.Width == 1376)
                chartControl1.Width = this.Width - 300;
            //chartControl1.Height = this.Height - 130;
            PrintingSystem printSystem = new PrintingSystem(this.components);
            PrintableComponentLink printLink;
            if (this.components == null)
                printLink = new PrintableComponentLink();
            else
                printLink = new PrintableComponentLink(this.components);

            ((System.ComponentModel.ISupportInitialize)(printSystem)).BeginInit();

            printSystem.Links.AddRange(new object[] {
            printLink});

            printLink.Component = this.chartControl1;

            printLink.PaperKind = System.Drawing.Printing.PaperKind.A4;
            printLink.Landscape = true;
            printLink.Margins = new System.Drawing.Printing.Margins(5, 5, 135, 50);
            printLink.PrintingSystem = printSystem;
            printLink.PrintingSystemBase = printSystem;

            printLink.CreateMarginalHeaderArea +=
                new DevExpress.XtraPrinting.CreateAreaEventHandler(this.printableComponentLink1_CreateReportHeaderArea);
            printLink.CreateReportFooterArea +=
                new DevExpress.XtraPrinting.CreateAreaEventHandler(this.printableComponentLink1_CreateReportFooter);

            ((System.ComponentModel.ISupportInitialize)(printSystem)).EndInit();

            printLink.CreateDocument();
            printLink.ShowPreview();
            chartControl1.Width = this.Width - 51;
        }

        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                prvlg = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.frm_MaxCustomerSalesCategory).FirstOrDefault();

                if (!prvlg.CanPrint)
                    barBtnPrint.Enabled = false;
                if (!prvlg.CanAdd)
                    barBtnOk.Enabled = false;
            }
        }
    }
}
