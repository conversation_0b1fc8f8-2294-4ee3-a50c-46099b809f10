﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraPrinting;
using DevExpress.XtraNavBar;
using Pharmacy.Reports;
using DevExpress.XtraReports.UI;

namespace Pharmacy.Forms
{
    public partial class frm_IC_Categories_old: DevExpress.XtraEditors.XtraForm
    {
        public frm_IC_Categories_old()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

        }

        private void frm_IC_CategoriesList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            Get_Categories();
            ErpUtils.Tab_Enter_Process(grdCategory);
            ErpUtils.ColumnChooser(grdCategory);
            LoadPrivilege();
        }       

        private void frm_IC_CategoriesList_Activated(object sender, EventArgs e)
        {
            Get_Categories();
        }

        
        private void barBtn_New_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (ErpUtils.IsFormOpen(typeof(frm_IC_Category)))
                Application.OpenForms["frm_IC_Category"].Close();

            new frm_IC_Category(0, FormAction.Add).Show();
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }
        
        private void barBtn_Refresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Get_Categories();
        }

        private void barBtn_Open_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Open_Selected_Category();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {

            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(this.Text, "", "", "", grdCategory).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        private void NBI_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            var view = grdCategory.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            int catId = Convert.ToInt32(view.GetFocusedRowCellValue(colCategoryId));
            string catName = view.GetFocusedRowCellValue(colCategoryNameAr).ToString();
            string catFltr = (Shared.IsEnglish == true ? ResICEn.txtCategory : ResICAr.txtCategory)//"الفئه: " 
                + catName;

            if (((NavBarItem)sender).Name == "NBI_rptItemQty")
            {
                DateTime today = MyHelper.Get_Server_DateTime().Date;
                frm_IC_ItemsQty rprt = new frm_IC_ItemsQty(
                     Shared.IsEnglish == true ? ResICEn.txtItemsBalance : ResICAr.txtItemsBalance //"أرصدة الأصناف"
                    , (Shared.IsEnglish ? ResRptEn.FDate : ResRptAr.FDate) + today.ToShortDateString()                                        
                    
                    , catFltr, 0, 0, 0, 0, 0,
                    1, catId, 0, 0, 0, today);
                if (rprt.UserCanOpen)
                {
                    rprt.BringToFront();
                    rprt.Show();
                }
            }
            if (((NavBarItem)sender).Name == "NBI_rptItemReorder")
            {
                frm_IC_ItemsReorder rprt = new frm_IC_ItemsReorder(
                    Shared.IsEnglish == true ? ResICEn.txtItemsReorder : ResICAr.txtItemsReorder//"أصناف وصلت لحد الطلب"
                    , string.Empty, catFltr, 0, 0, 0,
                    0, 0, 1, catId, 0, 0, 0);
                if (rprt.UserCanOpen)
                {
                    rprt.BringToFront();
                    rprt.Show();
                }
            }
            if (((NavBarItem)sender).Name == "NBI_rptItemMinSell")
            {
                frm_IC_ItemsMinSell rprt = new frm_IC_ItemsMinSell(
                    Shared.IsEnglish == true ? ResICEn.txtItemsLeastSell : ResICAr.txtItemsLeastSell//"الاصناف الاقل مبيعا"
                    , string.Empty, catFltr, 0, 0, 0,
                 0, 0, 1, catId, 0, 0, 0, 0, Shared.minDate, Shared.maxDate);
                if (rprt.UserCanOpen)
                {
                    rprt.BringToFront();
                    rprt.Show();
                }
            }
            if (((NavBarItem)sender).Name == "NBI_rptItemMaxSell")
            {
                frm_IC_ItemsMaxSell rprt = new frm_IC_ItemsMaxSell(
                    Shared.IsEnglish == true ? ResICEn.txtItemsBestSell : ResICAr.txtItemsBestSell//"الاصناف الاكثر مبيعا"
                    , string.Empty, catFltr, 0, 0, 0,
                    0, 0, 1, catId, 0, 0, 0, 0, Shared.minDate, Shared.maxDate);
                if (rprt.UserCanOpen)
                {
                    rprt.BringToFront();
                    rprt.Show();
                }
            }            
        }

        private void NBI_Items_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            var view = grdCategory.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            if (ErpUtils.IsFormOpen(typeof(frm_IC_ItemsList)))
                Application.OpenForms["frm_IC_ItemsList"].Close();

            new frm_IC_ItemsList(Convert.ToInt32(view.GetFocusedRowCellValue(colCategoryId)), 0, 0, 0).Show();
        }

        private void grdCategory_DoubleClick(object sender, EventArgs e)
        {
            Open_Selected_Category();
        }

        private void Open_Selected_Category()
        {
            var view = grdCategory.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;
            int inv_id = Convert.ToInt32(view.GetRowCellValue(focused_row_index, colCategoryId));
            if (ErpUtils.IsFormOpen(typeof(frm_IC_Category)))
                Application.OpenForms["frm_IC_Category"].Close();

            new frm_IC_Category(inv_id, FormAction.Edit).Show();
        }

        private void Get_Categories()
        {
            int focusedIndex = (grdCategory.FocusedView as GridView).FocusedRowHandle;

            DAL.ERPDataContext pharm = new DAL.ERPDataContext();
            var Categories = (from c in pharm.IC_Categories
                              select new
                              {
                                  c.CategoryCode,
                                  c.CategoryId,
                                  c.CategoryNameAr,
                                  c.CategoryNameEn,
                              }).ToList();

            grdCategory.DataSource = Categories;
            (grdCategory.FocusedView as GridView).FocusedRowHandle = focusedIndex;
        }

        void LoadPrivilege()
        {
            if (frmMain.LstUserPrvlg != null)
            {
                if (frmMain.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.Item).Count() < 1)
                {
                    NBI_Items.Enabled = false;
                }

                UserPriv p = frmMain.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.Category).FirstOrDefault();
                if (!p.CanAdd)
                    barBtnNew.Enabled = false;
                if (!p.CanPrint)
                    barBtnPrint.Enabled = false;
            }
        }

        private void barBtn_Help_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "الفئات");
        }
    }
}