﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using System.Linq;
using DAL;
using DAL.Res;
using DevExpress.XtraPrinting;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraReports.UI;

namespace Pharmacy.Forms
{
    public partial class frm_IC_ItemsList : DevExpress.XtraEditors.XtraForm
    {
        private int Category_id, Company_id, ScientificGroup, Usage;


        public frm_IC_ItemsList()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

        }

        public frm_IC_ItemsList(int category_id, int company_id, int scientificGroup, int usage)
        {
            RTL.EnCulture(Shared.IsEnglish);
            Category_id = category_id;
            Company_id = company_id;
            ScientificGroup = scientificGroup;
            Usage = usage;

            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

           
        }

        private void frm_IC_ItemsList_FormClosing(object sender, FormClosingEventArgs e)
        {
            ErpUtils.save_Grid_Layout(grd_Items, this.Name.Replace("frm_", ""), true);
        }

        private void frm_IC_ItemsList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            #region modules

            if (Shared.st_Store.ExpireDate == false)
            {
                colExpire.Visible = colExpire.OptionsColumn.ShowInCustomizationForm = false;
            }
            if (Shared.POSAvailable == false)
            {
                col_IsPos.Visible = col_IsPos.OptionsColumn.ShowInCustomizationForm = false;
            }
            if (Shared.st_Store.UseMediumUom == false)
            {
                col_MediumUOMCode.Visible = col_MediumUOMCode.OptionsColumn.ShowInCustomizationForm = false;
            }
            if (Shared.st_Store.UseLargeUom == false)
            {
                col_LargeUOMCode.Visible = col_LargeUOMCode.OptionsColumn.ShowInCustomizationForm = false;
            }
            if (!Shared.ItemMatrixAvailable)
                chk_ShowMatrixItems.Visible = false;
            #endregion

            LoadPrivilege();
            ErpUtils.Tab_Enter_Process(grd_Items);
            ErpUtils.Load_Grid_Layout(grd_Items, this.Name.Replace("frm_", ""));
            ErpUtils.ColumnChooser(grd_Items);


            Category.Caption = Shared.IsEnglish ? Shared.st_Store.CategoryEn : Shared.st_Store.CategoryAr;
            Company.Caption = Shared.IsEnglish ? Shared.st_Store.CompanyEn : Shared.st_Store.CompanyAr;

            Get_Items();
        }


        private void barBtn_New_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (ErpUtils.IsFormOpen(typeof(frm_IC_Item)))
                Application.OpenForms["frm_IC_Item"].Close();

            frm_IC_Item frm = new frm_IC_Item(0, FormAction.Add);
            frm.BringToFront();
            frm.Show();
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Open_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Open_Selected_item();
        }

        private void barBtn_Refresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Get_Items();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grd_Items.MinimumSize = grd_Items.Size;
            new Reports.rpt_Template(this.Text, "", "", "", grd_Items, false).ShowPreview();
            grd_Items.MinimumSize = new Size(0, 0);
        }


        private void grd_Items_DoubleClick(object sender, EventArgs e)
        {
            Open_Selected_item();
        }

        private void gridView1_ColumnFilterChanged(object sender, EventArgs e)
        {
            lblItemsCount.Text = grd_Items.FocusedView.RowCount.ToString();
        }

        private void NBI_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            var view = grd_Items.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            int itemId = Convert.ToInt32(view.GetFocusedRowCellValue(colItemId));
            string itemName = view.GetFocusedRowCellValue(colItemNameAr).ToString();
            string itmFltr = (Shared.IsEnglish == true ? ResICEn.txtItem : ResICAr.txtItem)//"الصنف: " 
                + itemName;

            //if (((NavBarItem)sender).Name == "NBI_ItemsMove")
            //{
            //    rpt_IC_ItemsTotals rprt = new rpt_IC_ItemsTotals(
            //        Shared.IsEnglish == true ? ResICEn.txtItemMovement : ResICAr.txtItemMovement //"حركة الاصناف"
            //        , string.Empty, itmFltr,
            //        0, 0, 0,
            //        0, 0,
            //        0, 0,
            //        1, itemId, 0);
            //    if (rprt.UserCanOpen)
            //        new Reports.rpt_Template(Shared.IsEnglish == true ? ResICEn.txtItemMovement: ResICAr.txtItemMovement, "", itmFltr, rprt).ShowPreview();                                                            
            //        //rprt.ShowPreview();
            //}
            if (((NavBarItem)sender).Name == "NBI_Itemsales")
            {
                frm_SL_ItemsSales rprt = new frm_SL_ItemsSales(
                    Shared.IsEnglish == true ? ResICEn.txtItemTotalSales : ResICAr.txtItemTotalSales //"اجمالي مبيعات صنف/أصناف"
                    , string.Empty, itmFltr,
                    0, 0, 0,
                    0, 0,
                    0, string.Empty,
                    1, itemId, 0,
                    0, Shared.minDate, Shared.maxDate, 0, 0, "");
                if (rprt.UserCanOpen)
                {
                    rprt.BringToFront();
                    rprt.Show();
                }
            }
            if (((NavBarItem)sender).Name == "NBI_ItemSaleReturn")
            {
                frm_SL_ItemsReturn rprt = new frm_SL_ItemsReturn(
                    Shared.IsEnglish == true ? ResICEn.txtItemTotalSalesReturn : ResICAr.txtItemTotalSalesReturn //"اجمالي مردود مبيعات صنف/أصناف"
                    , string.Empty, itmFltr,
                    0, 0, 0,
                    0, 0,
                    0, string.Empty,
                    1, itemId, 0,
                    0, Shared.minDate, Shared.maxDate, 0, 0, "");
                if (rprt.UserCanOpen)
                {
                    rprt.BringToFront();
                    rprt.Show();
                }
            }
         
        }

        private void Open_Selected_item()
        {
            var view = grd_Items.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            int itm_id = 0;
            if(chk_ShowMatrixItems.Checked)
               itm_id = Convert.ToInt32(view.GetRowCellValue(focused_row_index, col_mtrxParentItem));
            else
               itm_id = Convert.ToInt32(view.GetRowCellValue(focused_row_index, colItemId));            

            if (ErpUtils.IsFormOpen(typeof(frm_IC_Item)))
                Application.OpenForms["frm_IC_Item"].Close();

            new frm_IC_Item(itm_id, FormAction.Edit).Show();
        }

        private void Get_Items()
        {
            int focusedIndex = (grd_Items.FocusedView as GridView).FocusedRowHandle;


            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var data = from i in DB.IC_Items
                       where Category_id == 0 ? true : i.Category == Category_id
                       //where Company_id == 0 ? true : i.Company == Company_id
                       where chk_ShowMatrixItems.Checked ? i.ItemType != (int)ItemType.MatrixParent : i.ItemType != (int)ItemType.MatrixDetail
                       select new
                       {
                           Category = i.IC_Category.CategoryNameAr,
                           //Company = i.IC_Company.CompanyNameAr,
                           Description = i.Description,
                           ItemCode1 = i.ItemCode1,
                           ItemCode2 = i.ItemCode2,
                           ItemId = i.ItemId,
                           ItemNameAr = i.ItemNameAr,
                           ItemNameEn = i.ItemNameEn,
                           ItemType =
                           i.ItemType == 0 ? (Shared.IsEnglish == true ? ResICEn.txtRaw : ResICAr.txtRaw)//"اولي"
                           : i.ItemType == 1 ? (Shared.IsEnglish == true ? ResICEn.txtService : ResICAr.txtService)//"خدمه" 
                           : i.ItemType == 2 ? (Shared.IsEnglish == true ? ResICEn.txtAssembly : ResICAr.txtAssembly)//"تام"
                           : (Shared.IsEnglish == true ? ResICEn.txtMatrix : ResICAr.txtMatrix),//" مصفوفه"

                           Expire = i.IsExpire,
                           PurchasePrice = decimal.ToDouble(i.PurchasePrice),
                           SmallUOMPrice = decimal.ToDouble(i.SmallUOMPrice),
                           i.IsDeleted,
                           i.PicPath,
                           i.WarrantyMonths,
                           i.mtrxParentItem,
                           i.SalesTaxRatio,
                           i.SalesTaxValue,
                           i.PurchaseTaxRatio,
                           i.PurchaseTaxValue,
                           i.SalesDiscRatio,
                           i.PurchaseDiscRatio,
                           i.PricingWithSmall,
                           i.VariableWeight,
                           i.IsOffer,
                           i.is_libra,
                           i.Width,
                           i.Height,
                           i.Length
                       };

            grd_Items.DataSource = data;

            lblItemsCount.Text = grd_Items.FocusedView.RowCount.ToString();

            (grd_Items.FocusedView as GridView).FocusedRowHandle = focusedIndex;
        }

        private void grd_Items_Click(object sender, EventArgs e)
        {

        }

        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.Item).FirstOrDefault();
                if (!p.CanAdd)
                    barBtnNew.Enabled = false;
                if (!p.CanPrint)
                    barBtnPrint.Enabled = false;
            }
        }

        private void barBtn_Help_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "الأصناف");
        }

        private void chk_ShowMatrixItems_CheckedChanged(object sender, EventArgs e)
        {
            Get_Items();
        }
    }

}