﻿namespace Pharmacy.Forms
{
    partial class frm_IC_StoresList
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_IC_StoresList));
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtnHelp = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnPrint = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnRefresh = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnOpen = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnNew = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.grdStores = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colCostMethod = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repCostMethod = new DevExpress.XtraEditors.Repository.RepositoryItemImageComboBox();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repSpin = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.colStoreNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colStoreId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_store = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.col_CostCenter = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_IsStopped = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemCheckEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdStores)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repCostMethod)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repSpin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_store)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit1)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnNew,
            this.barBtnOpen,
            this.barBtnHelp,
            this.barBtnClose,
            this.barBtnRefresh,
            this.barBtnPrint});
            this.barManager1.MaxItemId = 28;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(567, 147);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnHelp),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnPrint),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnRefresh),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnOpen),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnNew),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtnHelp
            // 
            this.barBtnHelp.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnHelp, "barBtnHelp");
            this.barBtnHelp.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtnHelp.Id = 2;
            this.barBtnHelp.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtnHelp.Name = "barBtnHelp";
            this.barBtnHelp.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnHelp.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnHelp_ItemClick);
            // 
            // barBtnPrint
            // 
            this.barBtnPrint.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnPrint, "barBtnPrint");
            this.barBtnPrint.Glyph = global::Pharmacy.Properties.Resources.prnt;
            this.barBtnPrint.Id = 27;
            this.barBtnPrint.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.P));
            this.barBtnPrint.Name = "barBtnPrint";
            this.barBtnPrint.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnPrint.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnPrint_ItemClick);
            // 
            // barBtnRefresh
            // 
            this.barBtnRefresh.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnRefresh, "barBtnRefresh");
            this.barBtnRefresh.Glyph = global::Pharmacy.Properties.Resources.refresh;
            this.barBtnRefresh.Id = 26;
            this.barBtnRefresh.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.R));
            this.barBtnRefresh.Name = "barBtnRefresh";
            this.barBtnRefresh.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnRefresh.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Refresh_ItemClick);
            // 
            // barBtnOpen
            // 
            this.barBtnOpen.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnOpen, "barBtnOpen");
            this.barBtnOpen.Glyph = global::Pharmacy.Properties.Resources.open;
            this.barBtnOpen.Id = 1;
            this.barBtnOpen.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.O));
            this.barBtnOpen.Name = "barBtnOpen";
            this.barBtnOpen.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnOpen.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Open_ItemClick);
            // 
            // barBtnNew
            // 
            this.barBtnNew.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnNew, "barBtnNew");
            this.barBtnNew.Glyph = global::Pharmacy.Properties.Resources._new;
            this.barBtnNew.Id = 0;
            this.barBtnNew.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.N));
            this.barBtnNew.Name = "barBtnNew";
            this.barBtnNew.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnNew.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_New_ItemClick);
            // 
            // barBtnClose
            // 
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 25;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Close_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // grdStores
            // 
            this.grdStores.Cursor = System.Windows.Forms.Cursors.Default;
            resources.ApplyResources(this.grdStores, "grdStores");
            this.grdStores.MainView = this.gridView1;
            this.grdStores.MenuManager = this.barManager1;
            this.grdStores.Name = "grdStores";
            this.grdStores.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repSpin,
            this.rep_store,
            this.repCostMethod,
            this.repositoryItemCheckEdit1});
            this.grdStores.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            this.grdStores.DoubleClick += new System.EventHandler(this.grdStores_DoubleClick);
            // 
            // gridView1
            // 
            this.gridView1.Appearance.GroupPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.GroupPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.Row.Options.UseTextOptions = true;
            this.gridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.AppearancePrint.FooterPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.BorderColor")));
            this.gridView1.AppearancePrint.FooterPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.ForeColor")));
            this.gridView1.AppearancePrint.FooterPanel.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.FooterPanel.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.GroupFooter.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.BorderColor")));
            this.gridView1.AppearancePrint.GroupFooter.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.ForeColor")));
            this.gridView1.AppearancePrint.GroupFooter.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.GroupFooter.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.GroupRow.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupRow.BorderColor")));
            this.gridView1.AppearancePrint.GroupRow.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupRow.ForeColor")));
            this.gridView1.AppearancePrint.GroupRow.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.GroupRow.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.BorderColor")));
            this.gridView1.AppearancePrint.HeaderPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.ForeColor")));
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.Lines.BackColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Lines.BackColor")));
            this.gridView1.AppearancePrint.Lines.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Lines.ForeColor")));
            this.gridView1.AppearancePrint.Lines.Options.UseBackColor = true;
            this.gridView1.AppearancePrint.Lines.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.Row.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Row.BorderColor")));
            this.gridView1.AppearancePrint.Row.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Row.ForeColor")));
            this.gridView1.AppearancePrint.Row.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.Row.Options.UseForeColor = true;
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colCostMethod,
            this.gridColumn5,
            this.gridColumn4,
            this.gridColumn3,
            this.colStoreNameAr,
            this.gridColumn1,
            this.colStoreId,
            this.gridColumn2,
            this.col_CostCenter,
            this.col_IsStopped});
            this.gridView1.GridControl = this.grdStores;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView1.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsBehavior.ReadOnly = true;
            this.gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView1.OptionsView.ShowAutoFilterRow = true;
            this.gridView1.OptionsView.ShowIndicator = false;
            // 
            // colCostMethod
            // 
            resources.ApplyResources(this.colCostMethod, "colCostMethod");
            this.colCostMethod.ColumnEdit = this.repCostMethod;
            this.colCostMethod.FieldName = "CostMethod";
            this.colCostMethod.Name = "colCostMethod";
            // 
            // repCostMethod
            // 
            resources.ApplyResources(this.repCostMethod, "repCostMethod");
            this.repCostMethod.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repCostMethod.Buttons"))))});
            this.repCostMethod.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("repCostMethod.Items"), ((object)(resources.GetObject("repCostMethod.Items1"))), ((int)(resources.GetObject("repCostMethod.Items2")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("repCostMethod.Items3"), ((object)(resources.GetObject("repCostMethod.Items4"))), ((int)(resources.GetObject("repCostMethod.Items5")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("repCostMethod.Items6"), ((object)(resources.GetObject("repCostMethod.Items7"))), ((int)(resources.GetObject("repCostMethod.Items8"))))});
            this.repCostMethod.Name = "repCostMethod";
            // 
            // gridColumn5
            // 
            resources.ApplyResources(this.gridColumn5, "gridColumn5");
            this.gridColumn5.FieldName = "Address";
            this.gridColumn5.Name = "gridColumn5";
            // 
            // gridColumn4
            // 
            resources.ApplyResources(this.gridColumn4, "gridColumn4");
            this.gridColumn4.FieldName = "Tel";
            this.gridColumn4.Name = "gridColumn4";
            // 
            // gridColumn3
            // 
            resources.ApplyResources(this.gridColumn3, "gridColumn3");
            this.gridColumn3.ColumnEdit = this.repSpin;
            this.gridColumn3.FieldName = "StoreNameEn";
            this.gridColumn3.Name = "gridColumn3";
            // 
            // repSpin
            // 
            resources.ApplyResources(this.repSpin, "repSpin");
            this.repSpin.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.repSpin.Name = "repSpin";
            // 
            // colStoreNameAr
            // 
            resources.ApplyResources(this.colStoreNameAr, "colStoreNameAr");
            this.colStoreNameAr.FieldName = "StoreNameAr";
            this.colStoreNameAr.Name = "colStoreNameAr";
            // 
            // gridColumn1
            // 
            resources.ApplyResources(this.gridColumn1, "gridColumn1");
            this.gridColumn1.FieldName = "StoreCode";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // colStoreId
            // 
            resources.ApplyResources(this.colStoreId, "colStoreId");
            this.colStoreId.FieldName = "StoreId";
            this.colStoreId.Name = "colStoreId";
            this.colStoreId.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // gridColumn2
            // 
            resources.ApplyResources(this.gridColumn2, "gridColumn2");
            this.gridColumn2.ColumnEdit = this.rep_store;
            this.gridColumn2.FieldName = "ParentId";
            this.gridColumn2.Name = "gridColumn2";
            // 
            // rep_store
            // 
            this.rep_store.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_store.Buttons"))))});
            this.rep_store.DisplayMember = "StoreNameAr";
            this.rep_store.Name = "rep_store";
            resources.ApplyResources(this.rep_store, "rep_store");
            this.rep_store.ValueMember = "StoreId";
            // 
            // col_CostCenter
            // 
            resources.ApplyResources(this.col_CostCenter, "col_CostCenter");
            this.col_CostCenter.FieldName = "CostCenter";
            this.col_CostCenter.Name = "col_CostCenter";
            // 
            // col_IsStopped
            // 
            resources.ApplyResources(this.col_IsStopped, "col_IsStopped");
            this.col_IsStopped.ColumnEdit = this.repositoryItemCheckEdit1;
            this.col_IsStopped.FieldName = "IsStopped";
            this.col_IsStopped.Name = "col_IsStopped";
            // 
            // repositoryItemCheckEdit1
            // 
            resources.ApplyResources(this.repositoryItemCheckEdit1, "repositoryItemCheckEdit1");
            this.repositoryItemCheckEdit1.Name = "repositoryItemCheckEdit1";
            // 
            // frm_IC_StoresList
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.grdStores);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.Name = "frm_IC_StoresList";
            this.Load += new System.EventHandler(this.frm_IC_StoresList_Load);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdStores)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repCostMethod)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repSpin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_store)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnNew;
        private DevExpress.XtraBars.BarButtonItem barBtnOpen;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtnHelp;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraGrid.GridControl grdStores;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit repSpin;
        private DevExpress.XtraGrid.Columns.GridColumn colStoreNameAr;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraBars.BarButtonItem barBtnRefresh;
        private DevExpress.XtraGrid.Columns.GridColumn colStoreId;
        private DevExpress.XtraBars.BarButtonItem barBtnPrint;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;        
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit rep_store;
        private DevExpress.XtraGrid.Columns.GridColumn colCostMethod;
        private DevExpress.XtraEditors.Repository.RepositoryItemImageComboBox repCostMethod;
        private DevExpress.XtraGrid.Columns.GridColumn col_CostCenter;
        private DevExpress.XtraGrid.Columns.GridColumn col_IsStopped;
        private DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit repositoryItemCheckEdit1;
    }
}