﻿namespace Pharmacy.Forms
{
    partial class frm_SL_Return
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_SL_Return));
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtnHelp = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnPrint = new DevExpress.XtraBars.BarButtonItem();
            this.btnImport = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnDelete = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnNew = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnCancel = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnSave = new DevExpress.XtraBars.BarButtonItem();
            this.batBtnList = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.barbtnLoadSellInvoice = new DevExpress.XtraBars.BarButtonItem();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.txtInvoiceCode = new DevExpress.XtraEditors.TextEdit();
            this.btnPrevious = new DevExpress.XtraEditors.SimpleButton();
            this.btnNext = new DevExpress.XtraEditors.SimpleButton();
            this.dtInvoiceDate = new DevExpress.XtraEditors.DateEdit();
            this.labelControl35 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl36 = new DevExpress.XtraEditors.LabelControl();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.flowLayoutPanel1 = new System.Windows.Forms.FlowLayoutPanel();
            this.pnlInvCode = new System.Windows.Forms.Panel();
            this.textEdit7 = new DevExpress.XtraEditors.TextEdit();
            this.pnlDate = new System.Windows.Forms.Panel();
            this.textEdit6 = new DevExpress.XtraEditors.TextEdit();
            this.pnlBranch = new System.Windows.Forms.Panel();
            this.textEdit5 = new DevExpress.XtraEditors.TextEdit();
            this.lkpStore = new DevExpress.XtraEditors.LookUpEdit();
            this.pnlCostCenter = new System.Windows.Forms.Panel();
            this.textEdit1 = new DevExpress.XtraEditors.TextEdit();
            this.lkpCostCenter = new DevExpress.XtraEditors.GridLookUpEdit();
            this.gv_CostCenter = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.pnlCrncy = new System.Windows.Forms.Panel();
            this.txtCurrency = new DevExpress.XtraEditors.TextEdit();
            this.uc_Currency1 = new Pharmacy.Forms.uc_Currency();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.txtNotes = new DevExpress.XtraEditors.MemoEdit();
            this.labelControl17 = new DevExpress.XtraEditors.LabelControl();
            this.lkp_Drawers = new DevExpress.XtraEditors.LookUpEdit();
            this.panelControl2 = new DevExpress.XtraEditors.PanelControl();
            this.grdPrInvoice = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.mi_frm_IC_Item = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_InvoiceStaticDisc = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_InvoiceStaticDimensions = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_PasteRows = new System.Windows.Forms.ToolStripMenuItem();
            this.importFromExcelSheetToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_TotalSellPrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CurrentQty = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_vendors = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView5 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn39 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn40 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn42 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn43 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repDiscountRatio = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repSpin = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colPurchasePrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repUOM = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView4 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repItems = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.repositoryItemGridLookUpEdit1View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CompanyNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CategoryNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ItemNameF = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn38 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Expire = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_expireDate = new DevExpress.XtraEditors.Repository.RepositoryItemDateEdit();
            this.col_Batch = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Height = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Width = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Length = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_TotalQty = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_PiecesCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ItemDescription = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ItemDescriptionEn = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_SalesTax = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_DiscountRatio2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_DiscountRatio3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Serial = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Serial2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repManufactureDate = new DevExpress.XtraEditors.Repository.RepositoryItemDateEdit();
            this.col_CusTax = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_LibraQty = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_IsOffer = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_PricingWithSmall = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_VariableWeight = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_kg_Weight_libra = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Is_Libra = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Pack = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colbonusDiscount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colTaxType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repTaxTypes = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView6 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.DescriptionAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.Code = new DevExpress.XtraGrid.Columns.GridColumn();
            this.E_TaxableTypeId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colEtaxValue = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ETaxRatio = new DevExpress.XtraGrid.Columns.GridColumn();
            this.btn_AddTaxes = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_btnAddTaxes = new DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit();
            this.TotalTaxes = new DevExpress.XtraGrid.Columns.GridColumn();
            this.totalTaxesRatio = new DevExpress.XtraGrid.Columns.GridColumn();
            this.totalTableTaxes = new DevExpress.XtraGrid.Columns.GridColumn();
            this.salePriceWithTaxTable = new DevExpress.XtraGrid.Columns.GridColumn();
            this.addTaxValue = new DevExpress.XtraGrid.Columns.GridColumn();
            this.tableTaxValue = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_TaxValue = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_TotalSubCustomTax = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_TotalSubAddTax = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_TotalSubDiscountTax = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repExpireDate = new DevExpress.XtraEditors.Repository.RepositoryItemDateEdit();
            this.repLocation = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colDescription = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colLocationNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colLocationId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repExpireDate_txt = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.lkp_Customers = new DevExpress.XtraEditors.GridLookUpEdit();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.labelControl19 = new DevExpress.XtraEditors.LabelControl();
            this.txt_Total = new DevExpress.XtraEditors.TextEdit();
            this.labelControl18 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.txt_Remains = new DevExpress.XtraEditors.TextEdit();
            this.txtNet = new DevExpress.XtraEditors.TextEdit();
            this.lbl_remains = new DevExpress.XtraEditors.LabelControl();
            this.labelControl13 = new DevExpress.XtraEditors.LabelControl();
            this.lbl_Paid = new DevExpress.XtraEditors.LabelControl();
            this.txt_paid = new DevExpress.XtraEditors.TextEdit();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.btnAddCustomer = new DevExpress.XtraEditors.SimpleButton();
            this.txtExpenses = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl20 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.lkp_Drawers2 = new DevExpress.XtraEditors.LookUpEdit();
            this.txt_PayAcc1_Paid = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl28 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl26 = new DevExpress.XtraEditors.LabelControl();
            this.txt_PayAcc2_Paid = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl25 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.cmbPayMethod = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl12 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl14 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl15 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl16 = new DevExpress.XtraEditors.LabelControl();
            this.txtDiscountRatio = new DevExpress.XtraEditors.SpinEdit();
            this.txtDiscountValue = new DevExpress.XtraEditors.SpinEdit();
            this.txt_TaxValue = new DevExpress.XtraEditors.SpinEdit();
            this.txt_DeductTaxR = new DevExpress.XtraEditors.SpinEdit();
            this.txt_DeductTaxV = new DevExpress.XtraEditors.SpinEdit();
            this.txt_AddTaxR = new DevExpress.XtraEditors.SpinEdit();
            this.txt_AddTaxV = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl27 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl23 = new DevExpress.XtraEditors.LabelControl();
            this.txt_CusTaxV = new DevExpress.XtraEditors.SpinEdit();
            this.chk_IsInTrns = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl29 = new DevExpress.XtraEditors.LabelControl();
            this.txt_EtaxValue = new DevExpress.XtraEditors.SpinEdit();
            this.grd_SubTaxes = new DevExpress.XtraGrid.GridControl();
            this.gv_SubTaxes = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.Value = new DevExpress.XtraGrid.Columns.GridColumn();
            this.SubTaxId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.lkp_SubTaxes = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.col_Rate = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtInvoiceCode.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtInvoiceDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtInvoiceDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            this.flowLayoutPanel1.SuspendLayout();
            this.pnlInvCode.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit7.Properties)).BeginInit();
            this.pnlDate.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit6.Properties)).BeginInit();
            this.pnlBranch.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit5.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpStore.Properties)).BeginInit();
            this.pnlCostCenter.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpCostCenter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv_CostCenter)).BeginInit();
            this.pnlCrncy.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtCurrency.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNotes.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Drawers.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).BeginInit();
            this.panelControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grdPrInvoice)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_vendors)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repDiscountRatio)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repSpin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repUOM)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repItems)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_expireDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_expireDate.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repManufactureDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repManufactureDate.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repTaxTypes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_btnAddTaxes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repExpireDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repExpireDate.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repLocation)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repExpireDate_txt)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Customers.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Total.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Remains.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNet.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_paid.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtExpenses.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Drawers2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_PayAcc1_Paid.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_PayAcc2_Paid.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbPayMethod.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscountRatio.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscountValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_TaxValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DeductTaxR.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DeductTaxV.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_AddTaxR.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_AddTaxV.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_CusTaxV.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_IsInTrns.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_EtaxValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grd_SubTaxes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv_SubTaxes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_SubTaxes)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnSave,
            this.barBtnClose,
            this.barBtnHelp,
            this.batBtnList,
            this.barBtnNew,
            this.barBtnCancel,
            this.barBtnPrint,
            this.barbtnLoadSellInvoice,
            this.btnImport,
            this.barBtnDelete});
            this.barManager1.MaxItemId = 37;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(377, 152);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnHelp),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnPrint),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.btnImport, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barBtnDelete, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnNew),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnCancel),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnSave),
            new DevExpress.XtraBars.LinkPersistInfo(this.batBtnList),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtnHelp
            // 
            resources.ApplyResources(this.barBtnHelp, "barBtnHelp");
            this.barBtnHelp.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnHelp.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtnHelp.Id = 2;
            this.barBtnHelp.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtnHelp.Name = "barBtnHelp";
            this.barBtnHelp.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnHelp.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnHelp_ItemClick);
            // 
            // barBtnPrint
            // 
            resources.ApplyResources(this.barBtnPrint, "barBtnPrint");
            this.barBtnPrint.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnPrint.Glyph = global::Pharmacy.Properties.Resources.prnt;
            this.barBtnPrint.Id = 30;
            this.barBtnPrint.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.P));
            this.barBtnPrint.Name = "barBtnPrint";
            this.barBtnPrint.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnPrint.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnPrint_ItemClick);
            // 
            // btnImport
            // 
            resources.ApplyResources(this.btnImport, "btnImport");
            this.btnImport.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.btnImport.Glyph = global::Pharmacy.Properties.Resources._16_convert;
            this.btnImport.Id = 35;
            this.btnImport.Name = "btnImport";
            this.btnImport.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barbtnLoadSellInvoice_ItemClick);
            // 
            // barBtnDelete
            // 
            resources.ApplyResources(this.barBtnDelete, "barBtnDelete");
            this.barBtnDelete.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnDelete.Glyph = ((System.Drawing.Image)(resources.GetObject("barBtnDelete.Glyph")));
            this.barBtnDelete.Id = 36;
            this.barBtnDelete.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("barBtnDelete.LargeGlyph")));
            this.barBtnDelete.Name = "barBtnDelete";
            this.barBtnDelete.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnDelete_ItemClick);
            // 
            // barBtnNew
            // 
            resources.ApplyResources(this.barBtnNew, "barBtnNew");
            this.barBtnNew.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnNew.Glyph = global::Pharmacy.Properties.Resources._new;
            this.barBtnNew.Id = 26;
            this.barBtnNew.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.N));
            this.barBtnNew.Name = "barBtnNew";
            this.barBtnNew.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnNew.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnNew_ItemClick);
            // 
            // barBtnCancel
            // 
            resources.ApplyResources(this.barBtnCancel, "barBtnCancel");
            this.barBtnCancel.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnCancel.Glyph = ((System.Drawing.Image)(resources.GetObject("barBtnCancel.Glyph")));
            this.barBtnCancel.Id = 28;
            this.barBtnCancel.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.D));
            this.barBtnCancel.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("barBtnCancel.LargeGlyph")));
            this.barBtnCancel.Name = "barBtnCancel";
            this.barBtnCancel.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnCancel.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnCancel_ItemClick);
            // 
            // barBtnSave
            // 
            resources.ApplyResources(this.barBtnSave, "barBtnSave");
            this.barBtnSave.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnSave.Glyph = global::Pharmacy.Properties.Resources.sve;
            this.barBtnSave.Id = 0;
            this.barBtnSave.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.S));
            this.barBtnSave.Name = "barBtnSave";
            this.barBtnSave.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnSave.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Save_ItemClick);
            // 
            // batBtnList
            // 
            resources.ApplyResources(this.batBtnList, "batBtnList");
            this.batBtnList.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.batBtnList.Glyph = global::Pharmacy.Properties.Resources.list32;
            this.batBtnList.Id = 25;
            this.batBtnList.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.T));
            this.batBtnList.Name = "batBtnList";
            this.batBtnList.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.batBtnList.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.batBtnList_ItemClick);
            // 
            // barBtnClose
            // 
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 1;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnClose_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.BarAppearance.Normal.GradientMode")));
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.BarAppearance.Normal.Image")));
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.Dock.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.Dock.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.Dock.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.Dock.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.Dock.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.Dock.GradientMode")));
            this.barAndDockingController1.AppearancesBar.Dock.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.Dock.Image")));
            this.barAndDockingController1.AppearancesBar.Dock.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.Dock.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.GradientMode")));
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Image")));
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontStyleDelta" +
        "")));
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.GradientMode")));
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.Image")));
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontSizeDel" +
        "ta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontStyleDe" +
        "lta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.GradientMod" +
        "e")));
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontSizeDelt" +
        "a")));
            this.barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontStyleDel" +
        "ta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.GradientMode" +
        "")));
            this.barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuBar.GradientMode")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuBar.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.GradientMode")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStrip.GradientMode")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStrip.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontStyleDelta" +
        "")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.GradientMode")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.Panel.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesDocking.Panel.FontSizeDelta")));
            this.barAndDockingController1.AppearancesDocking.Panel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesDocking.Panel.FontStyleDelta")));
            this.barAndDockingController1.AppearancesDocking.Panel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesDocking.Panel.GradientMode")));
            this.barAndDockingController1.AppearancesDocking.Panel.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesDocking.Panel.Image")));
            this.barAndDockingController1.AppearancesDocking.Panel.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.Panel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.PanelCaption.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaption.FontSizeDelta")));
            this.barAndDockingController1.AppearancesDocking.PanelCaption.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaption.FontStyleDelta")));
            this.barAndDockingController1.AppearancesDocking.PanelCaption.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaption.GradientMode")));
            this.barAndDockingController1.AppearancesDocking.PanelCaption.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaption.Image")));
            this.barAndDockingController1.AppearancesDocking.PanelCaption.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.PanelCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesRibbon.Item.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesRibbon.Item.FontSizeDelta")));
            this.barAndDockingController1.AppearancesRibbon.Item.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesRibbon.Item.FontStyleDelta")));
            this.barAndDockingController1.AppearancesRibbon.Item.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesRibbon.Item.GradientMode")));
            this.barAndDockingController1.AppearancesRibbon.Item.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesRibbon.Item.Image")));
            this.barAndDockingController1.AppearancesRibbon.Item.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesRibbon.Item.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesRibbon.PageHeader.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesRibbon.PageHeader.FontSizeDelta")));
            this.barAndDockingController1.AppearancesRibbon.PageHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesRibbon.PageHeader.FontStyleDelta")));
            this.barAndDockingController1.AppearancesRibbon.PageHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesRibbon.PageHeader.GradientMode")));
            this.barAndDockingController1.AppearancesRibbon.PageHeader.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesRibbon.PageHeader.Image")));
            this.barAndDockingController1.AppearancesRibbon.PageHeader.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesRibbon.PageHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlTop.Appearance.FontSizeDelta")));
            this.barDockControlTop.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlTop.Appearance.FontStyleDelta")));
            this.barDockControlTop.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlTop.Appearance.GradientMode")));
            this.barDockControlTop.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlTop.Appearance.Image")));
            this.barDockControlTop.Appearance.Options.UseTextOptions = true;
            this.barDockControlTop.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barDockControlTop.CausesValidation = false;
            // 
            // barDockControlBottom
            // 
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlBottom.Appearance.FontSizeDelta")));
            this.barDockControlBottom.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlBottom.Appearance.FontStyleDelta")));
            this.barDockControlBottom.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlBottom.Appearance.GradientMode")));
            this.barDockControlBottom.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlBottom.Appearance.Image")));
            this.barDockControlBottom.CausesValidation = false;
            // 
            // barDockControlLeft
            // 
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlLeft.Appearance.FontSizeDelta")));
            this.barDockControlLeft.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlLeft.Appearance.FontStyleDelta")));
            this.barDockControlLeft.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlLeft.Appearance.GradientMode")));
            this.barDockControlLeft.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlLeft.Appearance.Image")));
            this.barDockControlLeft.CausesValidation = false;
            // 
            // barDockControlRight
            // 
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlRight.Appearance.FontSizeDelta")));
            this.barDockControlRight.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlRight.Appearance.FontStyleDelta")));
            this.barDockControlRight.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlRight.Appearance.GradientMode")));
            this.barDockControlRight.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlRight.Appearance.Image")));
            this.barDockControlRight.CausesValidation = false;
            // 
            // barbtnLoadSellInvoice
            // 
            resources.ApplyResources(this.barbtnLoadSellInvoice, "barbtnLoadSellInvoice");
            this.barbtnLoadSellInvoice.Glyph = global::Pharmacy.Properties.Resources.N_return1;
            this.barbtnLoadSellInvoice.Id = 31;
            this.barbtnLoadSellInvoice.Name = "barbtnLoadSellInvoice";
            this.barbtnLoadSellInvoice.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barbtnLoadSellInvoice.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barbtnLoadSellInvoice_ItemClick);
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemTextEdit1.Mask.AutoComplete")));
            this.repositoryItemTextEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.BeepOnError")));
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemTextEdit1.Mask.MaskType")));
            this.repositoryItemTextEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemTextEdit1.Mask.PlaceHolder")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // txtInvoiceCode
            // 
            resources.ApplyResources(this.txtInvoiceCode, "txtInvoiceCode");
            this.txtInvoiceCode.EnterMoveNextControl = true;
            this.txtInvoiceCode.Name = "txtInvoiceCode";
            this.txtInvoiceCode.Properties.AccessibleDescription = resources.GetString("txtInvoiceCode.Properties.AccessibleDescription");
            this.txtInvoiceCode.Properties.AccessibleName = resources.GetString("txtInvoiceCode.Properties.AccessibleName");
            this.txtInvoiceCode.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtInvoiceCode.Properties.Appearance.FontSizeDelta")));
            this.txtInvoiceCode.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtInvoiceCode.Properties.Appearance.FontStyleDelta")));
            this.txtInvoiceCode.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtInvoiceCode.Properties.Appearance.GradientMode")));
            this.txtInvoiceCode.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtInvoiceCode.Properties.Appearance.Image")));
            this.txtInvoiceCode.Properties.Appearance.Options.UseTextOptions = true;
            this.txtInvoiceCode.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtInvoiceCode.Properties.AutoHeight = ((bool)(resources.GetObject("txtInvoiceCode.Properties.AutoHeight")));
            this.txtInvoiceCode.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtInvoiceCode.Properties.Mask.AutoComplete")));
            this.txtInvoiceCode.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtInvoiceCode.Properties.Mask.BeepOnError")));
            this.txtInvoiceCode.Properties.Mask.EditMask = resources.GetString("txtInvoiceCode.Properties.Mask.EditMask");
            this.txtInvoiceCode.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtInvoiceCode.Properties.Mask.IgnoreMaskBlank")));
            this.txtInvoiceCode.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtInvoiceCode.Properties.Mask.MaskType")));
            this.txtInvoiceCode.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtInvoiceCode.Properties.Mask.PlaceHolder")));
            this.txtInvoiceCode.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtInvoiceCode.Properties.Mask.SaveLiteral")));
            this.txtInvoiceCode.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtInvoiceCode.Properties.Mask.ShowPlaceHolders")));
            this.txtInvoiceCode.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtInvoiceCode.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtInvoiceCode.Properties.NullValuePrompt = resources.GetString("txtInvoiceCode.Properties.NullValuePrompt");
            this.txtInvoiceCode.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtInvoiceCode.Properties.NullValuePromptShowForEmptyValue")));
            this.txtInvoiceCode.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // btnPrevious
            // 
            resources.ApplyResources(this.btnPrevious, "btnPrevious");
            this.btnPrevious.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnPrevious.Image = global::Pharmacy.Properties.Resources.prev32;
            this.btnPrevious.Name = "btnPrevious";
            this.btnPrevious.TabStop = false;
            this.btnPrevious.Click += new System.EventHandler(this.btnPrevious_Click);
            // 
            // btnNext
            // 
            resources.ApplyResources(this.btnNext, "btnNext");
            this.btnNext.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnNext.Image = global::Pharmacy.Properties.Resources.nxt;
            this.btnNext.Name = "btnNext";
            this.btnNext.TabStop = false;
            this.btnNext.Click += new System.EventHandler(this.btnNext_Click);
            // 
            // dtInvoiceDate
            // 
            resources.ApplyResources(this.dtInvoiceDate, "dtInvoiceDate");
            this.dtInvoiceDate.EnterMoveNextControl = true;
            this.dtInvoiceDate.MenuManager = this.barManager1;
            this.dtInvoiceDate.Name = "dtInvoiceDate";
            this.dtInvoiceDate.Properties.AccessibleDescription = resources.GetString("dtInvoiceDate.Properties.AccessibleDescription");
            this.dtInvoiceDate.Properties.AccessibleName = resources.GetString("dtInvoiceDate.Properties.AccessibleName");
            this.dtInvoiceDate.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.dtInvoiceDate.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("dtInvoiceDate.Properties.Appearance.FontSizeDelta")));
            this.dtInvoiceDate.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("dtInvoiceDate.Properties.Appearance.FontStyleDelta")));
            this.dtInvoiceDate.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("dtInvoiceDate.Properties.Appearance.GradientMode")));
            this.dtInvoiceDate.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("dtInvoiceDate.Properties.Appearance.Image")));
            this.dtInvoiceDate.Properties.Appearance.Options.UseTextOptions = true;
            this.dtInvoiceDate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.dtInvoiceDate.Properties.AutoHeight = ((bool)(resources.GetObject("dtInvoiceDate.Properties.AutoHeight")));
            this.dtInvoiceDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("dtInvoiceDate.Properties.Buttons"))))});
            this.dtInvoiceDate.Properties.CalendarTimeProperties.AccessibleDescription = resources.GetString("dtInvoiceDate.Properties.CalendarTimeProperties.AccessibleDescription");
            this.dtInvoiceDate.Properties.CalendarTimeProperties.AccessibleName = resources.GetString("dtInvoiceDate.Properties.CalendarTimeProperties.AccessibleName");
            this.dtInvoiceDate.Properties.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.AutoHeight")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.AutoComplete")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.BeepOnError = ((bool)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.BeepOnError")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.EditMask = resources.GetString("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.EditMask");
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.MaskType")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.PlaceHolder = ((char)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.PlaceHolder")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.SaveLiteral")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.NullValuePrompt = resources.GetString("dtInvoiceDate.Properties.CalendarTimeProperties.NullValuePrompt");
            this.dtInvoiceDate.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue")));
            this.dtInvoiceDate.Properties.DisplayFormat.FormatString = "g";
            this.dtInvoiceDate.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.dtInvoiceDate.Properties.EditFormat.FormatString = "g";
            this.dtInvoiceDate.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.dtInvoiceDate.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dtInvoiceDate.Properties.Mask.AutoComplete")));
            this.dtInvoiceDate.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("dtInvoiceDate.Properties.Mask.BeepOnError")));
            this.dtInvoiceDate.Properties.Mask.EditMask = resources.GetString("dtInvoiceDate.Properties.Mask.EditMask");
            this.dtInvoiceDate.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtInvoiceDate.Properties.Mask.IgnoreMaskBlank")));
            this.dtInvoiceDate.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtInvoiceDate.Properties.Mask.MaskType")));
            this.dtInvoiceDate.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("dtInvoiceDate.Properties.Mask.PlaceHolder")));
            this.dtInvoiceDate.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtInvoiceDate.Properties.Mask.SaveLiteral")));
            this.dtInvoiceDate.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtInvoiceDate.Properties.Mask.ShowPlaceHolders")));
            this.dtInvoiceDate.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtInvoiceDate.Properties.Mask.UseMaskAsDisplayFormat")));
            this.dtInvoiceDate.Properties.NullValuePrompt = resources.GetString("dtInvoiceDate.Properties.NullValuePrompt");
            this.dtInvoiceDate.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dtInvoiceDate.Properties.NullValuePromptShowForEmptyValue")));
            this.dtInvoiceDate.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // labelControl35
            // 
            resources.ApplyResources(this.labelControl35, "labelControl35");
            this.labelControl35.Name = "labelControl35";
            // 
            // labelControl36
            // 
            resources.ApplyResources(this.labelControl36, "labelControl36");
            this.labelControl36.Name = "labelControl36";
            // 
            // panelControl1
            // 
            resources.ApplyResources(this.panelControl1, "panelControl1");
            this.panelControl1.Controls.Add(this.flowLayoutPanel1);
            this.panelControl1.Controls.Add(this.labelControl2);
            this.panelControl1.Controls.Add(this.txtNotes);
            this.panelControl1.Name = "panelControl1";
            // 
            // flowLayoutPanel1
            // 
            resources.ApplyResources(this.flowLayoutPanel1, "flowLayoutPanel1");
            this.flowLayoutPanel1.Controls.Add(this.pnlInvCode);
            this.flowLayoutPanel1.Controls.Add(this.pnlDate);
            this.flowLayoutPanel1.Controls.Add(this.pnlBranch);
            this.flowLayoutPanel1.Controls.Add(this.pnlCostCenter);
            this.flowLayoutPanel1.Controls.Add(this.pnlCrncy);
            this.flowLayoutPanel1.Name = "flowLayoutPanel1";
            // 
            // pnlInvCode
            // 
            resources.ApplyResources(this.pnlInvCode, "pnlInvCode");
            this.pnlInvCode.Controls.Add(this.textEdit7);
            this.pnlInvCode.Controls.Add(this.txtInvoiceCode);
            this.pnlInvCode.Name = "pnlInvCode";
            // 
            // textEdit7
            // 
            resources.ApplyResources(this.textEdit7, "textEdit7");
            this.textEdit7.EnterMoveNextControl = true;
            this.textEdit7.MenuManager = this.barManager1;
            this.textEdit7.Name = "textEdit7";
            this.textEdit7.Properties.AccessibleDescription = resources.GetString("textEdit7.Properties.AccessibleDescription");
            this.textEdit7.Properties.AccessibleName = resources.GetString("textEdit7.Properties.AccessibleName");
            this.textEdit7.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("textEdit7.Properties.Appearance.BackColor")));
            this.textEdit7.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("textEdit7.Properties.Appearance.FontSizeDelta")));
            this.textEdit7.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("textEdit7.Properties.Appearance.FontStyleDelta")));
            this.textEdit7.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("textEdit7.Properties.Appearance.ForeColor")));
            this.textEdit7.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("textEdit7.Properties.Appearance.GradientMode")));
            this.textEdit7.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("textEdit7.Properties.Appearance.Image")));
            this.textEdit7.Properties.Appearance.Options.UseBackColor = true;
            this.textEdit7.Properties.Appearance.Options.UseForeColor = true;
            this.textEdit7.Properties.Appearance.Options.UseTextOptions = true;
            this.textEdit7.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.textEdit7.Properties.AutoHeight = ((bool)(resources.GetObject("textEdit7.Properties.AutoHeight")));
            this.textEdit7.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.textEdit7.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("textEdit7.Properties.Mask.AutoComplete")));
            this.textEdit7.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("textEdit7.Properties.Mask.BeepOnError")));
            this.textEdit7.Properties.Mask.EditMask = resources.GetString("textEdit7.Properties.Mask.EditMask");
            this.textEdit7.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("textEdit7.Properties.Mask.IgnoreMaskBlank")));
            this.textEdit7.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("textEdit7.Properties.Mask.MaskType")));
            this.textEdit7.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("textEdit7.Properties.Mask.PlaceHolder")));
            this.textEdit7.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("textEdit7.Properties.Mask.SaveLiteral")));
            this.textEdit7.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("textEdit7.Properties.Mask.ShowPlaceHolders")));
            this.textEdit7.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("textEdit7.Properties.Mask.UseMaskAsDisplayFormat")));
            this.textEdit7.Properties.MaxLength = 190;
            this.textEdit7.Properties.NullValuePrompt = resources.GetString("textEdit7.Properties.NullValuePrompt");
            this.textEdit7.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("textEdit7.Properties.NullValuePromptShowForEmptyValue")));
            this.textEdit7.TabStop = false;
            // 
            // pnlDate
            // 
            resources.ApplyResources(this.pnlDate, "pnlDate");
            this.pnlDate.Controls.Add(this.textEdit6);
            this.pnlDate.Controls.Add(this.dtInvoiceDate);
            this.pnlDate.Name = "pnlDate";
            // 
            // textEdit6
            // 
            resources.ApplyResources(this.textEdit6, "textEdit6");
            this.textEdit6.EnterMoveNextControl = true;
            this.textEdit6.MenuManager = this.barManager1;
            this.textEdit6.Name = "textEdit6";
            this.textEdit6.Properties.AccessibleDescription = resources.GetString("textEdit6.Properties.AccessibleDescription");
            this.textEdit6.Properties.AccessibleName = resources.GetString("textEdit6.Properties.AccessibleName");
            this.textEdit6.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("textEdit6.Properties.Appearance.BackColor")));
            this.textEdit6.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("textEdit6.Properties.Appearance.FontSizeDelta")));
            this.textEdit6.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("textEdit6.Properties.Appearance.FontStyleDelta")));
            this.textEdit6.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("textEdit6.Properties.Appearance.ForeColor")));
            this.textEdit6.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("textEdit6.Properties.Appearance.GradientMode")));
            this.textEdit6.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("textEdit6.Properties.Appearance.Image")));
            this.textEdit6.Properties.Appearance.Options.UseBackColor = true;
            this.textEdit6.Properties.Appearance.Options.UseForeColor = true;
            this.textEdit6.Properties.Appearance.Options.UseTextOptions = true;
            this.textEdit6.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.textEdit6.Properties.AutoHeight = ((bool)(resources.GetObject("textEdit6.Properties.AutoHeight")));
            this.textEdit6.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.textEdit6.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("textEdit6.Properties.Mask.AutoComplete")));
            this.textEdit6.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("textEdit6.Properties.Mask.BeepOnError")));
            this.textEdit6.Properties.Mask.EditMask = resources.GetString("textEdit6.Properties.Mask.EditMask");
            this.textEdit6.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("textEdit6.Properties.Mask.IgnoreMaskBlank")));
            this.textEdit6.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("textEdit6.Properties.Mask.MaskType")));
            this.textEdit6.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("textEdit6.Properties.Mask.PlaceHolder")));
            this.textEdit6.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("textEdit6.Properties.Mask.SaveLiteral")));
            this.textEdit6.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("textEdit6.Properties.Mask.ShowPlaceHolders")));
            this.textEdit6.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("textEdit6.Properties.Mask.UseMaskAsDisplayFormat")));
            this.textEdit6.Properties.MaxLength = 190;
            this.textEdit6.Properties.NullValuePrompt = resources.GetString("textEdit6.Properties.NullValuePrompt");
            this.textEdit6.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("textEdit6.Properties.NullValuePromptShowForEmptyValue")));
            this.textEdit6.TabStop = false;
            // 
            // pnlBranch
            // 
            resources.ApplyResources(this.pnlBranch, "pnlBranch");
            this.pnlBranch.Controls.Add(this.textEdit5);
            this.pnlBranch.Controls.Add(this.lkpStore);
            this.pnlBranch.Name = "pnlBranch";
            // 
            // textEdit5
            // 
            resources.ApplyResources(this.textEdit5, "textEdit5");
            this.textEdit5.EnterMoveNextControl = true;
            this.textEdit5.MenuManager = this.barManager1;
            this.textEdit5.Name = "textEdit5";
            this.textEdit5.Properties.AccessibleDescription = resources.GetString("textEdit5.Properties.AccessibleDescription");
            this.textEdit5.Properties.AccessibleName = resources.GetString("textEdit5.Properties.AccessibleName");
            this.textEdit5.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("textEdit5.Properties.Appearance.BackColor")));
            this.textEdit5.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("textEdit5.Properties.Appearance.FontSizeDelta")));
            this.textEdit5.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("textEdit5.Properties.Appearance.FontStyleDelta")));
            this.textEdit5.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("textEdit5.Properties.Appearance.ForeColor")));
            this.textEdit5.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("textEdit5.Properties.Appearance.GradientMode")));
            this.textEdit5.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("textEdit5.Properties.Appearance.Image")));
            this.textEdit5.Properties.Appearance.Options.UseBackColor = true;
            this.textEdit5.Properties.Appearance.Options.UseForeColor = true;
            this.textEdit5.Properties.Appearance.Options.UseTextOptions = true;
            this.textEdit5.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.textEdit5.Properties.AutoHeight = ((bool)(resources.GetObject("textEdit5.Properties.AutoHeight")));
            this.textEdit5.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.textEdit5.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("textEdit5.Properties.Mask.AutoComplete")));
            this.textEdit5.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("textEdit5.Properties.Mask.BeepOnError")));
            this.textEdit5.Properties.Mask.EditMask = resources.GetString("textEdit5.Properties.Mask.EditMask");
            this.textEdit5.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("textEdit5.Properties.Mask.IgnoreMaskBlank")));
            this.textEdit5.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("textEdit5.Properties.Mask.MaskType")));
            this.textEdit5.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("textEdit5.Properties.Mask.PlaceHolder")));
            this.textEdit5.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("textEdit5.Properties.Mask.SaveLiteral")));
            this.textEdit5.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("textEdit5.Properties.Mask.ShowPlaceHolders")));
            this.textEdit5.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("textEdit5.Properties.Mask.UseMaskAsDisplayFormat")));
            this.textEdit5.Properties.MaxLength = 190;
            this.textEdit5.Properties.NullValuePrompt = resources.GetString("textEdit5.Properties.NullValuePrompt");
            this.textEdit5.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("textEdit5.Properties.NullValuePromptShowForEmptyValue")));
            this.textEdit5.TabStop = false;
            // 
            // lkpStore
            // 
            resources.ApplyResources(this.lkpStore, "lkpStore");
            this.lkpStore.EnterMoveNextControl = true;
            this.lkpStore.MenuManager = this.barManager1;
            this.lkpStore.Name = "lkpStore";
            this.lkpStore.Properties.AccessibleDescription = resources.GetString("lkpStore.Properties.AccessibleDescription");
            this.lkpStore.Properties.AccessibleName = resources.GetString("lkpStore.Properties.AccessibleName");
            this.lkpStore.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkpStore.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkpStore.Properties.Appearance.FontSizeDelta")));
            this.lkpStore.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpStore.Properties.Appearance.FontStyleDelta")));
            this.lkpStore.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpStore.Properties.Appearance.GradientMode")));
            this.lkpStore.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkpStore.Properties.Appearance.Image")));
            this.lkpStore.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpStore.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpStore.Properties.AutoHeight = ((bool)(resources.GetObject("lkpStore.Properties.AutoHeight")));
            this.lkpStore.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpStore.Properties.Buttons"))))});
            this.lkpStore.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns"), resources.GetString("lkpStore.Properties.Columns1"), ((int)(resources.GetObject("lkpStore.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns3"))), resources.GetString("lkpStore.Properties.Columns4"), ((bool)(resources.GetObject("lkpStore.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns7"), resources.GetString("lkpStore.Properties.Columns8"), ((int)(resources.GetObject("lkpStore.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns10"))), resources.GetString("lkpStore.Properties.Columns11"), ((bool)(resources.GetObject("lkpStore.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns14"), resources.GetString("lkpStore.Properties.Columns15"), ((int)(resources.GetObject("lkpStore.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns17"))), resources.GetString("lkpStore.Properties.Columns18"), ((bool)(resources.GetObject("lkpStore.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns20")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns21"), resources.GetString("lkpStore.Properties.Columns22"), ((int)(resources.GetObject("lkpStore.Properties.Columns23"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns24"))), resources.GetString("lkpStore.Properties.Columns25"), ((bool)(resources.GetObject("lkpStore.Properties.Columns26"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns27")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns28"), resources.GetString("lkpStore.Properties.Columns29"), ((int)(resources.GetObject("lkpStore.Properties.Columns30"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns31"))), resources.GetString("lkpStore.Properties.Columns32"), ((bool)(resources.GetObject("lkpStore.Properties.Columns33"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns34")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns35"), resources.GetString("lkpStore.Properties.Columns36"), ((int)(resources.GetObject("lkpStore.Properties.Columns37"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns38"))), resources.GetString("lkpStore.Properties.Columns39"), ((bool)(resources.GetObject("lkpStore.Properties.Columns40"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns41")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns42"), resources.GetString("lkpStore.Properties.Columns43"), ((int)(resources.GetObject("lkpStore.Properties.Columns44"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns45"))), resources.GetString("lkpStore.Properties.Columns46"), ((bool)(resources.GetObject("lkpStore.Properties.Columns47"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns48"))))});
            this.lkpStore.Properties.NullText = resources.GetString("lkpStore.Properties.NullText");
            this.lkpStore.Properties.NullValuePrompt = resources.GetString("lkpStore.Properties.NullValuePrompt");
            this.lkpStore.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkpStore.Properties.NullValuePromptShowForEmptyValue")));
            this.lkpStore.EditValueChanged += new System.EventHandler(this.lkpStore_EditValueChanged);
            this.lkpStore.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.lkpStore.EditValueChanging += new DevExpress.XtraEditors.Controls.ChangingEventHandler(this.lkpStore_EditValueChanging);
            // 
            // pnlCostCenter
            // 
            resources.ApplyResources(this.pnlCostCenter, "pnlCostCenter");
            this.pnlCostCenter.Controls.Add(this.textEdit1);
            this.pnlCostCenter.Controls.Add(this.lkpCostCenter);
            this.pnlCostCenter.Name = "pnlCostCenter";
            // 
            // textEdit1
            // 
            resources.ApplyResources(this.textEdit1, "textEdit1");
            this.textEdit1.EnterMoveNextControl = true;
            this.textEdit1.MenuManager = this.barManager1;
            this.textEdit1.Name = "textEdit1";
            this.textEdit1.Properties.AccessibleDescription = resources.GetString("textEdit1.Properties.AccessibleDescription");
            this.textEdit1.Properties.AccessibleName = resources.GetString("textEdit1.Properties.AccessibleName");
            this.textEdit1.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("textEdit1.Properties.Appearance.BackColor")));
            this.textEdit1.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("textEdit1.Properties.Appearance.FontSizeDelta")));
            this.textEdit1.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("textEdit1.Properties.Appearance.FontStyleDelta")));
            this.textEdit1.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("textEdit1.Properties.Appearance.ForeColor")));
            this.textEdit1.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("textEdit1.Properties.Appearance.GradientMode")));
            this.textEdit1.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("textEdit1.Properties.Appearance.Image")));
            this.textEdit1.Properties.Appearance.Options.UseBackColor = true;
            this.textEdit1.Properties.Appearance.Options.UseForeColor = true;
            this.textEdit1.Properties.Appearance.Options.UseTextOptions = true;
            this.textEdit1.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.textEdit1.Properties.AutoHeight = ((bool)(resources.GetObject("textEdit1.Properties.AutoHeight")));
            this.textEdit1.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.textEdit1.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("textEdit1.Properties.Mask.AutoComplete")));
            this.textEdit1.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("textEdit1.Properties.Mask.BeepOnError")));
            this.textEdit1.Properties.Mask.EditMask = resources.GetString("textEdit1.Properties.Mask.EditMask");
            this.textEdit1.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("textEdit1.Properties.Mask.IgnoreMaskBlank")));
            this.textEdit1.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("textEdit1.Properties.Mask.MaskType")));
            this.textEdit1.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("textEdit1.Properties.Mask.PlaceHolder")));
            this.textEdit1.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("textEdit1.Properties.Mask.SaveLiteral")));
            this.textEdit1.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("textEdit1.Properties.Mask.ShowPlaceHolders")));
            this.textEdit1.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("textEdit1.Properties.Mask.UseMaskAsDisplayFormat")));
            this.textEdit1.Properties.MaxLength = 190;
            this.textEdit1.Properties.NullValuePrompt = resources.GetString("textEdit1.Properties.NullValuePrompt");
            this.textEdit1.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("textEdit1.Properties.NullValuePromptShowForEmptyValue")));
            this.textEdit1.TabStop = false;
            // 
            // lkpCostCenter
            // 
            resources.ApplyResources(this.lkpCostCenter, "lkpCostCenter");
            this.lkpCostCenter.MenuManager = this.barManager1;
            this.lkpCostCenter.Name = "lkpCostCenter";
            this.lkpCostCenter.Properties.AccessibleDescription = resources.GetString("lkpCostCenter.Properties.AccessibleDescription");
            this.lkpCostCenter.Properties.AccessibleName = resources.GetString("lkpCostCenter.Properties.AccessibleName");
            this.lkpCostCenter.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkpCostCenter.Properties.Appearance.FontSizeDelta")));
            this.lkpCostCenter.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpCostCenter.Properties.Appearance.FontStyleDelta")));
            this.lkpCostCenter.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpCostCenter.Properties.Appearance.GradientMode")));
            this.lkpCostCenter.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkpCostCenter.Properties.Appearance.Image")));
            this.lkpCostCenter.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpCostCenter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpCostCenter.Properties.AutoHeight = ((bool)(resources.GetObject("lkpCostCenter.Properties.AutoHeight")));
            this.lkpCostCenter.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkpCostCenter.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpCostCenter.Properties.Buttons"))))});
            this.lkpCostCenter.Properties.NullText = resources.GetString("lkpCostCenter.Properties.NullText");
            this.lkpCostCenter.Properties.NullValuePrompt = resources.GetString("lkpCostCenter.Properties.NullValuePrompt");
            this.lkpCostCenter.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkpCostCenter.Properties.NullValuePromptShowForEmptyValue")));
            this.lkpCostCenter.Properties.View = this.gv_CostCenter;
            this.lkpCostCenter.Popup += new System.EventHandler(this.lkpCostCenter_Popup);
            this.lkpCostCenter.CloseUp += new DevExpress.XtraEditors.Controls.CloseUpEventHandler(this.lkpCostCenter_CloseUp);
            // 
            // gv_CostCenter
            // 
            resources.ApplyResources(this.gv_CostCenter, "gv_CostCenter");
            this.gv_CostCenter.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn30,
            this.gridColumn27,
            this.gridColumn26});
            this.gv_CostCenter.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gv_CostCenter.Name = "gv_CostCenter";
            this.gv_CostCenter.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gv_CostCenter.OptionsSelection.MultiSelect = true;
            this.gv_CostCenter.OptionsSelection.MultiSelectMode = DevExpress.XtraGrid.Views.Grid.GridMultiSelectMode.CheckBoxRowSelect;
            this.gv_CostCenter.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn30
            // 
            resources.ApplyResources(this.gridColumn30, "gridColumn30");
            this.gridColumn30.FieldName = "CostCenterName";
            this.gridColumn30.Name = "gridColumn30";
            // 
            // gridColumn27
            // 
            resources.ApplyResources(this.gridColumn27, "gridColumn27");
            this.gridColumn27.FieldName = "CostCenterCode";
            this.gridColumn27.Name = "gridColumn27";
            // 
            // gridColumn26
            // 
            resources.ApplyResources(this.gridColumn26, "gridColumn26");
            this.gridColumn26.FieldName = "CostCenterId";
            this.gridColumn26.Name = "gridColumn26";
            // 
            // pnlCrncy
            // 
            resources.ApplyResources(this.pnlCrncy, "pnlCrncy");
            this.pnlCrncy.Controls.Add(this.txtCurrency);
            this.pnlCrncy.Controls.Add(this.uc_Currency1);
            this.pnlCrncy.Name = "pnlCrncy";
            // 
            // txtCurrency
            // 
            resources.ApplyResources(this.txtCurrency, "txtCurrency");
            this.txtCurrency.EnterMoveNextControl = true;
            this.txtCurrency.MenuManager = this.barManager1;
            this.txtCurrency.Name = "txtCurrency";
            this.txtCurrency.Properties.AccessibleDescription = resources.GetString("txtCurrency.Properties.AccessibleDescription");
            this.txtCurrency.Properties.AccessibleName = resources.GetString("txtCurrency.Properties.AccessibleName");
            this.txtCurrency.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txtCurrency.Properties.Appearance.BackColor")));
            this.txtCurrency.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtCurrency.Properties.Appearance.FontSizeDelta")));
            this.txtCurrency.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtCurrency.Properties.Appearance.FontStyleDelta")));
            this.txtCurrency.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("txtCurrency.Properties.Appearance.ForeColor")));
            this.txtCurrency.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtCurrency.Properties.Appearance.GradientMode")));
            this.txtCurrency.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtCurrency.Properties.Appearance.Image")));
            this.txtCurrency.Properties.Appearance.Options.UseBackColor = true;
            this.txtCurrency.Properties.Appearance.Options.UseForeColor = true;
            this.txtCurrency.Properties.Appearance.Options.UseTextOptions = true;
            this.txtCurrency.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtCurrency.Properties.AutoHeight = ((bool)(resources.GetObject("txtCurrency.Properties.AutoHeight")));
            this.txtCurrency.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.txtCurrency.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtCurrency.Properties.Mask.AutoComplete")));
            this.txtCurrency.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtCurrency.Properties.Mask.BeepOnError")));
            this.txtCurrency.Properties.Mask.EditMask = resources.GetString("txtCurrency.Properties.Mask.EditMask");
            this.txtCurrency.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtCurrency.Properties.Mask.IgnoreMaskBlank")));
            this.txtCurrency.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtCurrency.Properties.Mask.MaskType")));
            this.txtCurrency.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtCurrency.Properties.Mask.PlaceHolder")));
            this.txtCurrency.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtCurrency.Properties.Mask.SaveLiteral")));
            this.txtCurrency.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtCurrency.Properties.Mask.ShowPlaceHolders")));
            this.txtCurrency.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtCurrency.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtCurrency.Properties.MaxLength = 190;
            this.txtCurrency.Properties.NullValuePrompt = resources.GetString("txtCurrency.Properties.NullValuePrompt");
            this.txtCurrency.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtCurrency.Properties.NullValuePromptShowForEmptyValue")));
            this.txtCurrency.TabStop = false;
            // 
            // uc_Currency1
            // 
            resources.ApplyResources(this.uc_Currency1, "uc_Currency1");
            this.uc_Currency1.Name = "uc_Currency1";
            this.uc_Currency1.TabStop = false;
            // 
            // labelControl2
            // 
            resources.ApplyResources(this.labelControl2, "labelControl2");
            this.labelControl2.Name = "labelControl2";
            // 
            // txtNotes
            // 
            resources.ApplyResources(this.txtNotes, "txtNotes");
            this.txtNotes.MenuManager = this.barManager1;
            this.txtNotes.Name = "txtNotes";
            this.txtNotes.Properties.AccessibleDescription = resources.GetString("txtNotes.Properties.AccessibleDescription");
            this.txtNotes.Properties.AccessibleName = resources.GetString("txtNotes.Properties.AccessibleName");
            this.txtNotes.Properties.NullValuePrompt = resources.GetString("txtNotes.Properties.NullValuePrompt");
            this.txtNotes.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtNotes.Properties.NullValuePromptShowForEmptyValue")));
            this.txtNotes.Properties.ScrollBars = System.Windows.Forms.ScrollBars.Both;
            this.txtNotes.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // labelControl17
            // 
            resources.ApplyResources(this.labelControl17, "labelControl17");
            this.labelControl17.Name = "labelControl17";
            // 
            // lkp_Drawers
            // 
            resources.ApplyResources(this.lkp_Drawers, "lkp_Drawers");
            this.lkp_Drawers.EnterMoveNextControl = true;
            this.lkp_Drawers.MenuManager = this.barManager1;
            this.lkp_Drawers.Name = "lkp_Drawers";
            this.lkp_Drawers.Properties.AccessibleDescription = resources.GetString("lkp_Drawers.Properties.AccessibleDescription");
            this.lkp_Drawers.Properties.AccessibleName = resources.GetString("lkp_Drawers.Properties.AccessibleName");
            this.lkp_Drawers.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkp_Drawers.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_Drawers.Properties.Appearance.FontSizeDelta")));
            this.lkp_Drawers.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_Drawers.Properties.Appearance.FontStyleDelta")));
            this.lkp_Drawers.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_Drawers.Properties.Appearance.GradientMode")));
            this.lkp_Drawers.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_Drawers.Properties.Appearance.Image")));
            this.lkp_Drawers.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_Drawers.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_Drawers.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_Drawers.Properties.AutoHeight")));
            this.lkp_Drawers.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_Drawers.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_Drawers.Properties.Buttons"))))});
            this.lkp_Drawers.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Drawers.Properties.Columns"), resources.GetString("lkp_Drawers.Properties.Columns1"), ((int)(resources.GetObject("lkp_Drawers.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_Drawers.Properties.Columns3"))), resources.GetString("lkp_Drawers.Properties.Columns4"), ((bool)(resources.GetObject("lkp_Drawers.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_Drawers.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Drawers.Properties.Columns7"), resources.GetString("lkp_Drawers.Properties.Columns8"))});
            this.lkp_Drawers.Properties.DisplayMember = "AccountName";
            this.lkp_Drawers.Properties.NullText = resources.GetString("lkp_Drawers.Properties.NullText");
            this.lkp_Drawers.Properties.NullValuePrompt = resources.GetString("lkp_Drawers.Properties.NullValuePrompt");
            this.lkp_Drawers.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_Drawers.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_Drawers.Properties.ValueMember = "AccountId";
            this.lkp_Drawers.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // panelControl2
            // 
            resources.ApplyResources(this.panelControl2, "panelControl2");
            this.panelControl2.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.panelControl2.Controls.Add(this.grdPrInvoice);
            this.panelControl2.Name = "panelControl2";
            // 
            // grdPrInvoice
            // 
            resources.ApplyResources(this.grdPrInvoice, "grdPrInvoice");
            this.grdPrInvoice.ContextMenuStrip = this.contextMenuStrip1;
            this.grdPrInvoice.Cursor = System.Windows.Forms.Cursors.Default;
            this.grdPrInvoice.EmbeddedNavigator.AccessibleDescription = resources.GetString("grdPrInvoice.EmbeddedNavigator.AccessibleDescription");
            this.grdPrInvoice.EmbeddedNavigator.AccessibleName = resources.GetString("grdPrInvoice.EmbeddedNavigator.AccessibleName");
            this.grdPrInvoice.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grdPrInvoice.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.Anchor")));
            this.grdPrInvoice.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.BackgroundImage")));
            this.grdPrInvoice.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.BackgroundImageLayout")));
            this.grdPrInvoice.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.ImeMode")));
            this.grdPrInvoice.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.MaximumSize")));
            this.grdPrInvoice.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.TextLocation")));
            this.grdPrInvoice.EmbeddedNavigator.ToolTip = resources.GetString("grdPrInvoice.EmbeddedNavigator.ToolTip");
            this.grdPrInvoice.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.ToolTipIconType")));
            this.grdPrInvoice.EmbeddedNavigator.ToolTipTitle = resources.GetString("grdPrInvoice.EmbeddedNavigator.ToolTipTitle");
            this.grdPrInvoice.MainView = this.gridView2;
            this.grdPrInvoice.Name = "grdPrInvoice";
            this.grdPrInvoice.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repSpin,
            this.repItems,
            this.repUOM,
            this.repExpireDate,
            this.repLocation,
            this.repExpireDate_txt,
            this.rep_vendors,
            this.repDiscountRatio,
            this.rep_expireDate,
            this.repManufactureDate,
            this.repTaxTypes,
            this.rep_btnAddTaxes});
            this.grdPrInvoice.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView2});
            this.grdPrInvoice.ProcessGridKey += new System.Windows.Forms.KeyEventHandler(this.grid_ProcessGridKey);
            // 
            // contextMenuStrip1
            // 
            resources.ApplyResources(this.contextMenuStrip1, "contextMenuStrip1");
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.mi_frm_IC_Item,
            this.mi_InvoiceStaticDisc,
            this.mi_InvoiceStaticDimensions,
            this.mi_PasteRows,
            this.importFromExcelSheetToolStripMenuItem});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.Opened += new System.EventHandler(this.contextMenuStrip1_Opened);
            // 
            // mi_frm_IC_Item
            // 
            resources.ApplyResources(this.mi_frm_IC_Item, "mi_frm_IC_Item");
            this.mi_frm_IC_Item.Name = "mi_frm_IC_Item";
            this.mi_frm_IC_Item.Click += new System.EventHandler(this.mi_frm_IC_Item_Click);
            // 
            // mi_InvoiceStaticDisc
            // 
            resources.ApplyResources(this.mi_InvoiceStaticDisc, "mi_InvoiceStaticDisc");
            this.mi_InvoiceStaticDisc.Name = "mi_InvoiceStaticDisc";
            this.mi_InvoiceStaticDisc.Click += new System.EventHandler(this.mi_InvoiceStaticDisc_Click);
            // 
            // mi_InvoiceStaticDimensions
            // 
            resources.ApplyResources(this.mi_InvoiceStaticDimensions, "mi_InvoiceStaticDimensions");
            this.mi_InvoiceStaticDimensions.Name = "mi_InvoiceStaticDimensions";
            this.mi_InvoiceStaticDimensions.Click += new System.EventHandler(this.mi_InvoiceStaticDimensions_Click);
            // 
            // mi_PasteRows
            // 
            resources.ApplyResources(this.mi_PasteRows, "mi_PasteRows");
            this.mi_PasteRows.Name = "mi_PasteRows";
            this.mi_PasteRows.Click += new System.EventHandler(this.mi_PasteRows_Click);
            // 
            // importFromExcelSheetToolStripMenuItem
            // 
            resources.ApplyResources(this.importFromExcelSheetToolStripMenuItem, "importFromExcelSheetToolStripMenuItem");
            this.importFromExcelSheetToolStripMenuItem.Name = "importFromExcelSheetToolStripMenuItem";
            // 
            // gridView2
            // 
            this.gridView2.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView2.Appearance.HeaderPanel.FontSizeDelta")));
            this.gridView2.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView2.Appearance.HeaderPanel.FontStyleDelta")));
            this.gridView2.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView2.Appearance.HeaderPanel.GradientMode")));
            this.gridView2.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView2.Appearance.HeaderPanel.Image")));
            this.gridView2.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView2.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView2.Appearance.HeaderPanel.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridView2.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView2.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gridView2.Appearance.Row.FontSizeDelta")));
            this.gridView2.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView2.Appearance.Row.FontStyleDelta")));
            this.gridView2.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView2.Appearance.Row.GradientMode")));
            this.gridView2.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView2.Appearance.Row.Image")));
            this.gridView2.Appearance.Row.Options.UseTextOptions = true;
            this.gridView2.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView2.Appearance.Row.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridView2.Appearance.Row.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridView2, "gridView2");
            this.gridView2.ColumnPanelRowHeight = 50;
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn36,
            this.gridColumn35,
            this.gridColumn33,
            this.gridColumn29,
            this.gridColumn28,
            this.col_TotalSellPrice,
            this.col_CurrentQty,
            this.gridColumn23,
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn5,
            this.colPurchasePrice,
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn10,
            this.col_ItemNameF,
            this.gridColumn11,
            this.gridColumn31,
            this.gridColumn38,
            this.col_Expire,
            this.col_Batch,
            this.col_Height,
            this.col_Width,
            this.col_Length,
            this.col_TotalQty,
            this.col_PiecesCount,
            this.col_ItemDescription,
            this.col_ItemDescriptionEn,
            this.col_SalesTax,
            this.col_DiscountRatio2,
            this.col_DiscountRatio3,
            this.col_Serial,
            this.col_Serial2,
            this.gridColumn25,
            this.col_CusTax,
            this.col_LibraQty,
            this.col_IsOffer,
            this.col_PricingWithSmall,
            this.col_VariableWeight,
            this.col_kg_Weight_libra,
            this.col_Is_Libra,
            this.col_Pack,
            this.colbonusDiscount,
            this.colTaxType,
            this.colEtaxValue,
            this.col_ETaxRatio,
            this.btn_AddTaxes,
            this.TotalTaxes,
            this.totalTaxesRatio,
            this.totalTableTaxes,
            this.salePriceWithTaxTable,
            this.addTaxValue,
            this.tableTaxValue,
            this.col_TaxValue,
            this.col_TotalSubCustomTax,
            this.col_TotalSubAddTax,
            this.col_TotalSubDiscountTax});
            this.gridView2.CustomizationFormBounds = new System.Drawing.Rectangle(982, 128, 216, 388);
            this.gridView2.GridControl = this.grdPrInvoice;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView2.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.gridView2.OptionsView.EnableAppearanceOddRow = true;
            this.gridView2.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Bottom;
            this.gridView2.OptionsView.RowAutoHeight = true;
            this.gridView2.OptionsView.ShowFooter = true;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            this.gridView2.RowStyle += new DevExpress.XtraGrid.Views.Grid.RowStyleEventHandler(this.gridView2_RowStyle);
            this.gridView2.ShowingEditor += new System.ComponentModel.CancelEventHandler(this.gridView2_ShowingEditor);
            this.gridView2.InitNewRow += new DevExpress.XtraGrid.Views.Grid.InitNewRowEventHandler(this.gridView2_InitNewRow);
            this.gridView2.FocusedRowChanged += new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventHandler(this.gridView1_FocusedRowChanged);
            this.gridView2.FocusedColumnChanged += new DevExpress.XtraGrid.Views.Base.FocusedColumnChangedEventHandler(this.gridView1_FocusedColumnChanged);
            this.gridView2.CellValueChanged += new DevExpress.XtraGrid.Views.Base.CellValueChangedEventHandler(this.gridView1_CellValueChanged);
            this.gridView2.InvalidRowException += new DevExpress.XtraGrid.Views.Base.InvalidRowExceptionEventHandler(this.gridView1_InvalidRowException);
            this.gridView2.ValidateRow += new DevExpress.XtraGrid.Views.Base.ValidateRowEventHandler(this.gridView1_ValidateRow);
            this.gridView2.CustomUnboundColumnData += new DevExpress.XtraGrid.Views.Base.CustomColumnDataEventHandler(this.gridView2_CustomUnboundColumnData);
            this.gridView2.CustomColumnDisplayText += new DevExpress.XtraGrid.Views.Base.CustomColumnDisplayTextEventHandler(this.gridView1_CustomColumnDisplayText);
            this.gridView2.KeyDown += new System.Windows.Forms.KeyEventHandler(this.gridView1_KeyDown);
            // 
            // gridColumn36
            // 
            this.gridColumn36.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn36.AppearanceCell.FontSizeDelta")));
            this.gridColumn36.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn36.AppearanceCell.FontStyleDelta")));
            this.gridColumn36.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn36.AppearanceCell.GradientMode")));
            this.gridColumn36.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn36.AppearanceCell.Image")));
            this.gridColumn36.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn36.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn36.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn36.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn36.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn36.AppearanceHeader.FontSizeDelta")));
            this.gridColumn36.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn36.AppearanceHeader.FontStyleDelta")));
            this.gridColumn36.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn36.AppearanceHeader.GradientMode")));
            this.gridColumn36.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn36.AppearanceHeader.Image")));
            this.gridColumn36.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn36.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn36.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn36.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn36, "gridColumn36");
            this.gridColumn36.FieldName = "TotalSellPrice";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.OptionsColumn.AllowEdit = false;
            this.gridColumn36.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn36.OptionsColumn.ReadOnly = true;
            this.gridColumn36.OptionsColumn.ShowInCustomizationForm = false;
            this.gridColumn36.OptionsFilter.AllowFilter = false;
            this.gridColumn36.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // gridColumn35
            // 
            this.gridColumn35.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn35.AppearanceCell.FontSizeDelta")));
            this.gridColumn35.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn35.AppearanceCell.FontStyleDelta")));
            this.gridColumn35.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn35.AppearanceCell.GradientMode")));
            this.gridColumn35.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn35.AppearanceCell.Image")));
            this.gridColumn35.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn35.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn35.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn35.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn35.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn35.AppearanceHeader.FontSizeDelta")));
            this.gridColumn35.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn35.AppearanceHeader.FontStyleDelta")));
            this.gridColumn35.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn35.AppearanceHeader.GradientMode")));
            this.gridColumn35.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn35.AppearanceHeader.Image")));
            this.gridColumn35.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn35.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn35.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn35.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn35, "gridColumn35");
            this.gridColumn35.FieldName = "MainPrice";
            this.gridColumn35.Name = "gridColumn35";
            this.gridColumn35.OptionsColumn.AllowEdit = false;
            this.gridColumn35.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn35.OptionsColumn.ReadOnly = true;
            this.gridColumn35.OptionsColumn.ShowInCustomizationForm = false;
            this.gridColumn35.OptionsFilter.AllowFilter = false;
            // 
            // gridColumn33
            // 
            this.gridColumn33.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn33.AppearanceCell.FontSizeDelta")));
            this.gridColumn33.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn33.AppearanceCell.FontStyleDelta")));
            this.gridColumn33.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn33.AppearanceCell.GradientMode")));
            this.gridColumn33.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn33.AppearanceCell.Image")));
            this.gridColumn33.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn33.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn33.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn33.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn33.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn33.AppearanceHeader.FontSizeDelta")));
            this.gridColumn33.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn33.AppearanceHeader.FontStyleDelta")));
            this.gridColumn33.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn33.AppearanceHeader.GradientMode")));
            this.gridColumn33.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn33.AppearanceHeader.Image")));
            this.gridColumn33.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn33.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn33.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn33.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn33, "gridColumn33");
            this.gridColumn33.FieldName = "CompanyNameAr";
            this.gridColumn33.Name = "gridColumn33";
            this.gridColumn33.OptionsColumn.AllowEdit = false;
            this.gridColumn33.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn33.OptionsColumn.ReadOnly = true;
            this.gridColumn33.OptionsFilter.AllowFilter = false;
            // 
            // gridColumn29
            // 
            this.gridColumn29.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn29.AppearanceCell.FontSizeDelta")));
            this.gridColumn29.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn29.AppearanceCell.FontStyleDelta")));
            this.gridColumn29.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn29.AppearanceCell.GradientMode")));
            this.gridColumn29.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn29.AppearanceCell.Image")));
            this.gridColumn29.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn29.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn29.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn29.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn29.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn29.AppearanceHeader.FontSizeDelta")));
            this.gridColumn29.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn29.AppearanceHeader.FontStyleDelta")));
            this.gridColumn29.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn29.AppearanceHeader.GradientMode")));
            this.gridColumn29.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn29.AppearanceHeader.Image")));
            this.gridColumn29.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn29.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn29.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn29.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn29, "gridColumn29");
            this.gridColumn29.FieldName = "LargeUOMFactor";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.OptionsColumn.AllowEdit = false;
            this.gridColumn29.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn29.OptionsColumn.ReadOnly = true;
            this.gridColumn29.OptionsColumn.ShowInCustomizationForm = false;
            this.gridColumn29.OptionsFilter.AllowFilter = false;
            // 
            // gridColumn28
            // 
            this.gridColumn28.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn28.AppearanceCell.FontSizeDelta")));
            this.gridColumn28.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn28.AppearanceCell.FontStyleDelta")));
            this.gridColumn28.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn28.AppearanceCell.GradientMode")));
            this.gridColumn28.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn28.AppearanceCell.Image")));
            this.gridColumn28.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn28.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn28.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn28.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn28.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn28.AppearanceHeader.FontSizeDelta")));
            this.gridColumn28.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn28.AppearanceHeader.FontStyleDelta")));
            this.gridColumn28.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn28.AppearanceHeader.GradientMode")));
            this.gridColumn28.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn28.AppearanceHeader.Image")));
            this.gridColumn28.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn28.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn28.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn28.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn28, "gridColumn28");
            this.gridColumn28.FieldName = "MediumUOMFactor";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.OptionsColumn.AllowEdit = false;
            this.gridColumn28.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn28.OptionsColumn.ReadOnly = true;
            this.gridColumn28.OptionsColumn.ShowInCustomizationForm = false;
            this.gridColumn28.OptionsFilter.AllowFilter = false;
            // 
            // col_TotalSellPrice
            // 
            resources.ApplyResources(this.col_TotalSellPrice, "col_TotalSellPrice");
            this.col_TotalSellPrice.FieldName = "TotalSellPrice";
            this.col_TotalSellPrice.Name = "col_TotalSellPrice";
            this.col_TotalSellPrice.OptionsColumn.AllowEdit = false;
            this.col_TotalSellPrice.OptionsColumn.AllowFocus = false;
            this.col_TotalSellPrice.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_TotalSellPrice.OptionsColumn.ReadOnly = true;
            this.col_TotalSellPrice.OptionsFilter.AllowFilter = false;
            this.col_TotalSellPrice.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_TotalSellPrice.Summary"))))});
            // 
            // col_CurrentQty
            // 
            this.col_CurrentQty.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_CurrentQty.AppearanceCell.FontSizeDelta")));
            this.col_CurrentQty.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_CurrentQty.AppearanceCell.FontStyleDelta")));
            this.col_CurrentQty.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_CurrentQty.AppearanceCell.GradientMode")));
            this.col_CurrentQty.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_CurrentQty.AppearanceCell.Image")));
            this.col_CurrentQty.AppearanceCell.Options.UseTextOptions = true;
            this.col_CurrentQty.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_CurrentQty.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_CurrentQty.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_CurrentQty.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_CurrentQty.AppearanceHeader.FontSizeDelta")));
            this.col_CurrentQty.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_CurrentQty.AppearanceHeader.FontStyleDelta")));
            this.col_CurrentQty.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_CurrentQty.AppearanceHeader.GradientMode")));
            this.col_CurrentQty.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_CurrentQty.AppearanceHeader.Image")));
            this.col_CurrentQty.AppearanceHeader.Options.UseTextOptions = true;
            this.col_CurrentQty.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_CurrentQty.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_CurrentQty.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_CurrentQty, "col_CurrentQty");
            this.col_CurrentQty.FieldName = "CurrentQty";
            this.col_CurrentQty.Name = "col_CurrentQty";
            this.col_CurrentQty.OptionsColumn.AllowEdit = false;
            this.col_CurrentQty.OptionsColumn.AllowFocus = false;
            this.col_CurrentQty.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_CurrentQty.OptionsColumn.ReadOnly = true;
            this.col_CurrentQty.OptionsFilter.AllowFilter = false;
            // 
            // gridColumn23
            // 
            this.gridColumn23.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn23.AppearanceCell.FontSizeDelta")));
            this.gridColumn23.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn23.AppearanceCell.FontStyleDelta")));
            this.gridColumn23.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn23.AppearanceCell.GradientMode")));
            this.gridColumn23.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn23.AppearanceCell.Image")));
            this.gridColumn23.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn23.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn23.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn23.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn23.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn23.AppearanceHeader.FontSizeDelta")));
            this.gridColumn23.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn23.AppearanceHeader.FontStyleDelta")));
            this.gridColumn23.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn23.AppearanceHeader.GradientMode")));
            this.gridColumn23.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn23.AppearanceHeader.Image")));
            this.gridColumn23.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn23.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn23.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn23.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn23, "gridColumn23");
            this.gridColumn23.ColumnEdit = this.rep_vendors;
            this.gridColumn23.FieldName = "VendorId";
            this.gridColumn23.Name = "gridColumn23";
            this.gridColumn23.OptionsColumn.AllowEdit = false;
            this.gridColumn23.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn23.OptionsColumn.ReadOnly = true;
            this.gridColumn23.OptionsFilter.AllowFilter = false;
            // 
            // rep_vendors
            // 
            resources.ApplyResources(this.rep_vendors, "rep_vendors");
            this.rep_vendors.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_vendors.Buttons"))))});
            this.rep_vendors.Name = "rep_vendors";
            this.rep_vendors.View = this.gridView5;
            // 
            // gridView5
            // 
            this.gridView5.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView5.Appearance.HeaderPanel.FontSizeDelta")));
            this.gridView5.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView5.Appearance.HeaderPanel.FontStyleDelta")));
            this.gridView5.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView5.Appearance.HeaderPanel.GradientMode")));
            this.gridView5.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView5.Appearance.HeaderPanel.Image")));
            this.gridView5.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView5.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView5.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gridView5.Appearance.Row.FontSizeDelta")));
            this.gridView5.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView5.Appearance.Row.FontStyleDelta")));
            this.gridView5.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView5.Appearance.Row.GradientMode")));
            this.gridView5.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView5.Appearance.Row.Image")));
            this.gridView5.Appearance.Row.Options.UseTextOptions = true;
            this.gridView5.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.gridView5, "gridView5");
            this.gridView5.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn39,
            this.gridColumn40,
            this.gridColumn41,
            this.gridColumn42,
            this.gridColumn43});
            this.gridView5.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView5.Name = "gridView5";
            this.gridView5.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView5.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView5.OptionsView.EnableAppearanceOddRow = true;
            this.gridView5.OptionsView.ShowAutoFilterRow = true;
            this.gridView5.OptionsView.ShowGroupPanel = false;
            this.gridView5.OptionsView.ShowIndicator = false;
            // 
            // gridColumn39
            // 
            resources.ApplyResources(this.gridColumn39, "gridColumn39");
            this.gridColumn39.FieldName = "VendorId";
            this.gridColumn39.Name = "gridColumn39";
            // 
            // gridColumn40
            // 
            resources.ApplyResources(this.gridColumn40, "gridColumn40");
            this.gridColumn40.FieldName = "VenCode";
            this.gridColumn40.Name = "gridColumn40";
            // 
            // gridColumn41
            // 
            resources.ApplyResources(this.gridColumn41, "gridColumn41");
            this.gridColumn41.FieldName = "VenNameAr";
            this.gridColumn41.Name = "gridColumn41";
            // 
            // gridColumn42
            // 
            resources.ApplyResources(this.gridColumn42, "gridColumn42");
            this.gridColumn42.FieldName = "VenNameEn";
            this.gridColumn42.Name = "gridColumn42";
            // 
            // gridColumn43
            // 
            resources.ApplyResources(this.gridColumn43, "gridColumn43");
            this.gridColumn43.FieldName = "Credit";
            this.gridColumn43.Name = "gridColumn43";
            // 
            // gridColumn1
            // 
            this.gridColumn1.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn1.AppearanceCell.FontSizeDelta")));
            this.gridColumn1.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn1.AppearanceCell.FontStyleDelta")));
            this.gridColumn1.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn1.AppearanceCell.GradientMode")));
            this.gridColumn1.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn1.AppearanceCell.Image")));
            this.gridColumn1.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn1.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn1.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn1.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn1.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn1.AppearanceHeader.FontSizeDelta")));
            this.gridColumn1.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn1.AppearanceHeader.FontStyleDelta")));
            this.gridColumn1.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn1.AppearanceHeader.GradientMode")));
            this.gridColumn1.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn1.AppearanceHeader.Image")));
            this.gridColumn1.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn1.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn1.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn1.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn1, "gridColumn1");
            this.gridColumn1.ColumnEdit = this.repDiscountRatio;
            this.gridColumn1.FieldName = "DiscountRatio";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn1.OptionsFilter.AllowFilter = false;
            // 
            // repDiscountRatio
            // 
            resources.ApplyResources(this.repDiscountRatio, "repDiscountRatio");
            this.repDiscountRatio.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repDiscountRatio.Mask.AutoComplete")));
            this.repDiscountRatio.Mask.BeepOnError = ((bool)(resources.GetObject("repDiscountRatio.Mask.BeepOnError")));
            this.repDiscountRatio.Mask.EditMask = resources.GetString("repDiscountRatio.Mask.EditMask");
            this.repDiscountRatio.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repDiscountRatio.Mask.IgnoreMaskBlank")));
            this.repDiscountRatio.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repDiscountRatio.Mask.MaskType")));
            this.repDiscountRatio.Mask.PlaceHolder = ((char)(resources.GetObject("repDiscountRatio.Mask.PlaceHolder")));
            this.repDiscountRatio.Mask.SaveLiteral = ((bool)(resources.GetObject("repDiscountRatio.Mask.SaveLiteral")));
            this.repDiscountRatio.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repDiscountRatio.Mask.ShowPlaceHolders")));
            this.repDiscountRatio.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repDiscountRatio.Mask.UseMaskAsDisplayFormat")));
            this.repDiscountRatio.Name = "repDiscountRatio";
            // 
            // gridColumn2
            // 
            this.gridColumn2.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn2.AppearanceCell.FontSizeDelta")));
            this.gridColumn2.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn2.AppearanceCell.FontStyleDelta")));
            this.gridColumn2.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn2.AppearanceCell.GradientMode")));
            this.gridColumn2.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn2.AppearanceCell.Image")));
            this.gridColumn2.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn2.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn2.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn2.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn2.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn2.AppearanceHeader.FontSizeDelta")));
            this.gridColumn2.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn2.AppearanceHeader.FontStyleDelta")));
            this.gridColumn2.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn2.AppearanceHeader.GradientMode")));
            this.gridColumn2.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn2.AppearanceHeader.Image")));
            this.gridColumn2.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn2.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn2.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn2, "gridColumn2");
            this.gridColumn2.ColumnEdit = this.repSpin;
            this.gridColumn2.FieldName = "DiscountValue";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn2.OptionsFilter.AllowFilter = false;
            this.gridColumn2.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridColumn2.Summary"))), resources.GetString("gridColumn2.Summary1"), resources.GetString("gridColumn2.Summary2"))});
            // 
            // repSpin
            // 
            resources.ApplyResources(this.repSpin, "repSpin");
            this.repSpin.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.repSpin.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repSpin.Mask.AutoComplete")));
            this.repSpin.Mask.BeepOnError = ((bool)(resources.GetObject("repSpin.Mask.BeepOnError")));
            this.repSpin.Mask.EditMask = resources.GetString("repSpin.Mask.EditMask");
            this.repSpin.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repSpin.Mask.IgnoreMaskBlank")));
            this.repSpin.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repSpin.Mask.MaskType")));
            this.repSpin.Mask.PlaceHolder = ((char)(resources.GetObject("repSpin.Mask.PlaceHolder")));
            this.repSpin.Mask.SaveLiteral = ((bool)(resources.GetObject("repSpin.Mask.SaveLiteral")));
            this.repSpin.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repSpin.Mask.ShowPlaceHolders")));
            this.repSpin.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repSpin.Mask.UseMaskAsDisplayFormat")));
            this.repSpin.Name = "repSpin";
            // 
            // gridColumn5
            // 
            this.gridColumn5.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn5.AppearanceCell.FontSizeDelta")));
            this.gridColumn5.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn5.AppearanceCell.FontStyleDelta")));
            this.gridColumn5.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn5.AppearanceCell.GradientMode")));
            this.gridColumn5.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn5.AppearanceCell.Image")));
            this.gridColumn5.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn5.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn5.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn5.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn5.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn5.AppearanceHeader.FontSizeDelta")));
            this.gridColumn5.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn5.AppearanceHeader.FontStyleDelta")));
            this.gridColumn5.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn5.AppearanceHeader.GradientMode")));
            this.gridColumn5.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn5.AppearanceHeader.Image")));
            this.gridColumn5.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn5.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn5.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn5.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn5, "gridColumn5");
            this.gridColumn5.ColumnEdit = this.repSpin;
            this.gridColumn5.FieldName = "SellPrice";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn5.OptionsFilter.AllowFilter = false;
            // 
            // colPurchasePrice
            // 
            this.colPurchasePrice.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("colPurchasePrice.AppearanceCell.FontSizeDelta")));
            this.colPurchasePrice.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("colPurchasePrice.AppearanceCell.FontStyleDelta")));
            this.colPurchasePrice.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("colPurchasePrice.AppearanceCell.GradientMode")));
            this.colPurchasePrice.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("colPurchasePrice.AppearanceCell.Image")));
            this.colPurchasePrice.AppearanceCell.Options.UseTextOptions = true;
            this.colPurchasePrice.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.colPurchasePrice.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colPurchasePrice.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.colPurchasePrice.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("colPurchasePrice.AppearanceHeader.FontSizeDelta")));
            this.colPurchasePrice.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("colPurchasePrice.AppearanceHeader.FontStyleDelta")));
            this.colPurchasePrice.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("colPurchasePrice.AppearanceHeader.GradientMode")));
            this.colPurchasePrice.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("colPurchasePrice.AppearanceHeader.Image")));
            this.colPurchasePrice.AppearanceHeader.Options.UseTextOptions = true;
            this.colPurchasePrice.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.colPurchasePrice.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colPurchasePrice.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.colPurchasePrice, "colPurchasePrice");
            this.colPurchasePrice.ColumnEdit = this.repSpin;
            this.colPurchasePrice.FieldName = "PurchasePrice";
            this.colPurchasePrice.Name = "colPurchasePrice";
            this.colPurchasePrice.OptionsColumn.AllowEdit = false;
            this.colPurchasePrice.OptionsColumn.AllowFocus = false;
            this.colPurchasePrice.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colPurchasePrice.OptionsColumn.ReadOnly = true;
            this.colPurchasePrice.OptionsFilter.AllowFilter = false;
            // 
            // gridColumn7
            // 
            this.gridColumn7.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn7.AppearanceCell.FontSizeDelta")));
            this.gridColumn7.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn7.AppearanceCell.FontStyleDelta")));
            this.gridColumn7.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn7.AppearanceCell.GradientMode")));
            this.gridColumn7.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn7.AppearanceCell.Image")));
            this.gridColumn7.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn7.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn7.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn7.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn7.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn7.AppearanceHeader.FontSizeDelta")));
            this.gridColumn7.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn7.AppearanceHeader.FontStyleDelta")));
            this.gridColumn7.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn7.AppearanceHeader.GradientMode")));
            this.gridColumn7.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn7.AppearanceHeader.Image")));
            this.gridColumn7.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn7.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn7.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn7.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn7, "gridColumn7");
            this.gridColumn7.ColumnEdit = this.repSpin;
            this.gridColumn7.FieldName = "Qty";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn7.OptionsFilter.AllowFilter = false;
            // 
            // gridColumn8
            // 
            this.gridColumn8.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn8.AppearanceCell.FontSizeDelta")));
            this.gridColumn8.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn8.AppearanceCell.FontStyleDelta")));
            this.gridColumn8.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn8.AppearanceCell.GradientMode")));
            this.gridColumn8.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn8.AppearanceCell.Image")));
            this.gridColumn8.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn8.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn8.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn8.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn8.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn8.AppearanceHeader.FontSizeDelta")));
            this.gridColumn8.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn8.AppearanceHeader.FontStyleDelta")));
            this.gridColumn8.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn8.AppearanceHeader.GradientMode")));
            this.gridColumn8.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn8.AppearanceHeader.Image")));
            this.gridColumn8.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn8.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn8.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn8.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn8, "gridColumn8");
            this.gridColumn8.ColumnEdit = this.repUOM;
            this.gridColumn8.FieldName = "UOM";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.OptionsFilter.AllowFilter = false;
            // 
            // repUOM
            // 
            resources.ApplyResources(this.repUOM, "repUOM");
            this.repUOM.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repUOM.Buttons"))))});
            this.repUOM.Name = "repUOM";
            this.repUOM.View = this.gridView4;
            this.repUOM.CustomDisplayText += new DevExpress.XtraEditors.Controls.CustomDisplayTextEventHandler(this.repUOM_CustomDisplayText);
            // 
            // gridView4
            // 
            this.gridView4.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView4.Appearance.HeaderPanel.FontSizeDelta")));
            this.gridView4.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView4.Appearance.HeaderPanel.FontStyleDelta")));
            this.gridView4.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView4.Appearance.HeaderPanel.GradientMode")));
            this.gridView4.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView4.Appearance.HeaderPanel.Image")));
            this.gridView4.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView4.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView4.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gridView4.Appearance.Row.FontSizeDelta")));
            this.gridView4.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView4.Appearance.Row.FontStyleDelta")));
            this.gridView4.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView4.Appearance.Row.GradientMode")));
            this.gridView4.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView4.Appearance.Row.Image")));
            this.gridView4.Appearance.Row.Options.UseTextOptions = true;
            this.gridView4.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.gridView4, "gridView4");
            this.gridView4.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn9,
            this.gridColumn16,
            this.gridColumn17});
            this.gridView4.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView4.Name = "gridView4";
            this.gridView4.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView4.OptionsView.ShowDetailButtons = false;
            this.gridView4.OptionsView.ShowGroupExpandCollapseButtons = false;
            this.gridView4.OptionsView.ShowGroupPanel = false;
            this.gridView4.OptionsView.ShowIndicator = false;
            // 
            // gridColumn9
            // 
            resources.ApplyResources(this.gridColumn9, "gridColumn9");
            this.gridColumn9.FieldName = "Index";
            this.gridColumn9.Name = "gridColumn9";
            // 
            // gridColumn16
            // 
            resources.ApplyResources(this.gridColumn16, "gridColumn16");
            this.gridColumn16.FieldName = "Factor";
            this.gridColumn16.Name = "gridColumn16";
            // 
            // gridColumn17
            // 
            resources.ApplyResources(this.gridColumn17, "gridColumn17");
            this.gridColumn17.FieldName = "Uom";
            this.gridColumn17.Name = "gridColumn17";
            // 
            // gridColumn10
            // 
            this.gridColumn10.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn10.AppearanceCell.FontSizeDelta")));
            this.gridColumn10.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn10.AppearanceCell.FontStyleDelta")));
            this.gridColumn10.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn10.AppearanceCell.GradientMode")));
            this.gridColumn10.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn10.AppearanceCell.Image")));
            this.gridColumn10.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn10.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn10.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn10.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn10.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn10.AppearanceHeader.FontSizeDelta")));
            this.gridColumn10.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn10.AppearanceHeader.FontStyleDelta")));
            this.gridColumn10.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn10.AppearanceHeader.GradientMode")));
            this.gridColumn10.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn10.AppearanceHeader.Image")));
            this.gridColumn10.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn10.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn10.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn10.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn10, "gridColumn10");
            this.gridColumn10.ColumnEdit = this.repItems;
            this.gridColumn10.FieldName = "ItemId";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.OptionsFilter.AllowFilter = false;
            // 
            // repItems
            // 
            resources.ApplyResources(this.repItems, "repItems");
            this.repItems.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.repItems.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.repItems.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repItems.Buttons"))))});
            this.repItems.DisplayMember = "ItemNameAr";
            this.repItems.ImmediatePopup = true;
            this.repItems.Name = "repItems";
            this.repItems.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.repItems.ValueMember = "ItemId";
            this.repItems.View = this.repositoryItemGridLookUpEdit1View;
            this.repItems.Popup += new System.EventHandler(this.repItems_Popup);
            // 
            // repositoryItemGridLookUpEdit1View
            // 
            this.repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.FontSizeDelta")));
            this.repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.FontStyleDelta")));
            this.repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.GradientMode")));
            this.repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.Image")));
            this.repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.repositoryItemGridLookUpEdit1View.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("repositoryItemGridLookUpEdit1View.Appearance.Row.FontSizeDelta")));
            this.repositoryItemGridLookUpEdit1View.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("repositoryItemGridLookUpEdit1View.Appearance.Row.FontStyleDelta")));
            this.repositoryItemGridLookUpEdit1View.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("repositoryItemGridLookUpEdit1View.Appearance.Row.GradientMode")));
            this.repositoryItemGridLookUpEdit1View.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("repositoryItemGridLookUpEdit1View.Appearance.Row.Image")));
            this.repositoryItemGridLookUpEdit1View.Appearance.Row.Options.UseTextOptions = true;
            this.repositoryItemGridLookUpEdit1View.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.repositoryItemGridLookUpEdit1View, "repositoryItemGridLookUpEdit1View");
            this.repositoryItemGridLookUpEdit1View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn24,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn4,
            this.col_CompanyNameAr,
            this.col_CategoryNameAr});
            this.repositoryItemGridLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.repositoryItemGridLookUpEdit1View.Name = "repositoryItemGridLookUpEdit1View";
            this.repositoryItemGridLookUpEdit1View.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.repositoryItemGridLookUpEdit1View.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.repositoryItemGridLookUpEdit1View.OptionsBehavior.AutoSelectAllInEditor = false;
            this.repositoryItemGridLookUpEdit1View.OptionsBehavior.AutoUpdateTotalSummary = false;
            this.repositoryItemGridLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.repositoryItemGridLookUpEdit1View.OptionsSelection.UseIndicatorForSelection = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.BestFitMaxRowCount = 10;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowAutoFilterRow = true;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowDetailButtons = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowGroupPanel = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowIndicator = false;
            // 
            // gridColumn24
            // 
            resources.ApplyResources(this.gridColumn24, "gridColumn24");
            this.gridColumn24.DisplayFormat.FormatString = "n2";
            this.gridColumn24.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn24.FieldName = "SellPrice";
            this.gridColumn24.Name = "gridColumn24";
            this.gridColumn24.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // gridColumn12
            // 
            resources.ApplyResources(this.gridColumn12, "gridColumn12");
            this.gridColumn12.FieldName = "ItemNameEn";
            this.gridColumn12.Name = "gridColumn12";
            // 
            // gridColumn13
            // 
            resources.ApplyResources(this.gridColumn13, "gridColumn13");
            this.gridColumn13.FieldName = "ItemNameAr";
            this.gridColumn13.Name = "gridColumn13";
            // 
            // gridColumn14
            // 
            resources.ApplyResources(this.gridColumn14, "gridColumn14");
            this.gridColumn14.FieldName = "ItemCode1";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // gridColumn15
            // 
            resources.ApplyResources(this.gridColumn15, "gridColumn15");
            this.gridColumn15.FieldName = "ItemId";
            this.gridColumn15.Name = "gridColumn15";
            // 
            // gridColumn4
            // 
            resources.ApplyResources(this.gridColumn4, "gridColumn4");
            this.gridColumn4.FieldName = "ItemCode2";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // col_CompanyNameAr
            // 
            resources.ApplyResources(this.col_CompanyNameAr, "col_CompanyNameAr");
            this.col_CompanyNameAr.FieldName = "CompanyNameAr";
            this.col_CompanyNameAr.Name = "col_CompanyNameAr";
            // 
            // col_CategoryNameAr
            // 
            resources.ApplyResources(this.col_CategoryNameAr, "col_CategoryNameAr");
            this.col_CategoryNameAr.FieldName = "CategoryNameAr";
            this.col_CategoryNameAr.Name = "col_CategoryNameAr";
            // 
            // col_ItemNameF
            // 
            resources.ApplyResources(this.col_ItemNameF, "col_ItemNameF");
            this.col_ItemNameF.FieldName = "ItemIdF";
            this.col_ItemNameF.Name = "col_ItemNameF";
            this.col_ItemNameF.OptionsColumn.AllowEdit = false;
            this.col_ItemNameF.OptionsColumn.ReadOnly = true;
            // 
            // gridColumn11
            // 
            this.gridColumn11.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn11.AppearanceCell.FontSizeDelta")));
            this.gridColumn11.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn11.AppearanceCell.FontStyleDelta")));
            this.gridColumn11.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn11.AppearanceCell.GradientMode")));
            this.gridColumn11.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn11.AppearanceCell.Image")));
            this.gridColumn11.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn11.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn11.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn11.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn11.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn11.AppearanceHeader.FontSizeDelta")));
            this.gridColumn11.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn11.AppearanceHeader.FontStyleDelta")));
            this.gridColumn11.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn11.AppearanceHeader.GradientMode")));
            this.gridColumn11.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn11.AppearanceHeader.Image")));
            this.gridColumn11.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn11.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn11.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn11.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn11, "gridColumn11");
            this.gridColumn11.FieldName = "ItemCode2";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.OptionsColumn.AllowEdit = false;
            this.gridColumn11.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn11.OptionsColumn.ReadOnly = true;
            this.gridColumn11.OptionsFilter.AllowFilter = false;
            // 
            // gridColumn31
            // 
            this.gridColumn31.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn31.AppearanceCell.FontSizeDelta")));
            this.gridColumn31.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn31.AppearanceCell.FontStyleDelta")));
            this.gridColumn31.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn31.AppearanceCell.GradientMode")));
            this.gridColumn31.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn31.AppearanceCell.Image")));
            this.gridColumn31.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn31.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn31.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn31.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn31.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn31.AppearanceHeader.FontSizeDelta")));
            this.gridColumn31.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn31.AppearanceHeader.FontStyleDelta")));
            this.gridColumn31.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn31.AppearanceHeader.GradientMode")));
            this.gridColumn31.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn31.AppearanceHeader.Image")));
            this.gridColumn31.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn31.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn31.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn31.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn31, "gridColumn31");
            this.gridColumn31.FieldName = "ItemCode1";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.OptionsFilter.AllowFilter = false;
            // 
            // gridColumn38
            // 
            resources.ApplyResources(this.gridColumn38, "gridColumn38");
            this.gridColumn38.FieldName = "UomIndex";
            this.gridColumn38.Name = "gridColumn38";
            this.gridColumn38.OptionsColumn.AllowEdit = false;
            this.gridColumn38.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn38.OptionsColumn.ReadOnly = true;
            this.gridColumn38.OptionsColumn.ShowInCustomizationForm = false;
            this.gridColumn38.OptionsFilter.AllowFilter = false;
            // 
            // col_Expire
            // 
            this.col_Expire.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_Expire.AppearanceCell.FontSizeDelta")));
            this.col_Expire.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_Expire.AppearanceCell.FontStyleDelta")));
            this.col_Expire.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_Expire.AppearanceCell.GradientMode")));
            this.col_Expire.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_Expire.AppearanceCell.Image")));
            this.col_Expire.AppearanceCell.Options.UseTextOptions = true;
            this.col_Expire.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Expire.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Expire.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_Expire.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_Expire.AppearanceHeader.FontSizeDelta")));
            this.col_Expire.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_Expire.AppearanceHeader.FontStyleDelta")));
            this.col_Expire.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_Expire.AppearanceHeader.GradientMode")));
            this.col_Expire.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_Expire.AppearanceHeader.Image")));
            this.col_Expire.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Expire.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Expire.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Expire.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_Expire, "col_Expire");
            this.col_Expire.ColumnEdit = this.rep_expireDate;
            this.col_Expire.FieldName = "Expire";
            this.col_Expire.Name = "col_Expire";
            this.col_Expire.OptionsColumn.AllowEdit = false;
            this.col_Expire.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_Expire.OptionsColumn.ReadOnly = true;
            this.col_Expire.OptionsFilter.AllowFilter = false;
            // 
            // rep_expireDate
            // 
            resources.ApplyResources(this.rep_expireDate, "rep_expireDate");
            this.rep_expireDate.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_expireDate.Buttons"))))});
            this.rep_expireDate.CalendarTimeProperties.AccessibleDescription = resources.GetString("rep_expireDate.CalendarTimeProperties.AccessibleDescription");
            this.rep_expireDate.CalendarTimeProperties.AccessibleName = resources.GetString("rep_expireDate.CalendarTimeProperties.AccessibleName");
            this.rep_expireDate.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("rep_expireDate.CalendarTimeProperties.AutoHeight")));
            this.rep_expireDate.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.rep_expireDate.CalendarTimeProperties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("rep_expireDate.CalendarTimeProperties.Mask.AutoComplete")));
            this.rep_expireDate.CalendarTimeProperties.Mask.BeepOnError = ((bool)(resources.GetObject("rep_expireDate.CalendarTimeProperties.Mask.BeepOnError")));
            this.rep_expireDate.CalendarTimeProperties.Mask.EditMask = resources.GetString("rep_expireDate.CalendarTimeProperties.Mask.EditMask");
            this.rep_expireDate.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("rep_expireDate.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.rep_expireDate.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("rep_expireDate.CalendarTimeProperties.Mask.MaskType")));
            this.rep_expireDate.CalendarTimeProperties.Mask.PlaceHolder = ((char)(resources.GetObject("rep_expireDate.CalendarTimeProperties.Mask.PlaceHolder")));
            this.rep_expireDate.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("rep_expireDate.CalendarTimeProperties.Mask.SaveLiteral")));
            this.rep_expireDate.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("rep_expireDate.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.rep_expireDate.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("rep_expireDate.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat")));
            this.rep_expireDate.CalendarTimeProperties.NullValuePrompt = resources.GetString("rep_expireDate.CalendarTimeProperties.NullValuePrompt");
            this.rep_expireDate.CalendarTimeProperties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("rep_expireDate.CalendarTimeProperties.NullValuePromptShowForEmptyValue")));
            this.rep_expireDate.DisplayFormat.FormatString = "M-yyyy";
            this.rep_expireDate.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.rep_expireDate.EditFormat.FormatString = "M-yyyy";
            this.rep_expireDate.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.rep_expireDate.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("rep_expireDate.Mask.AutoComplete")));
            this.rep_expireDate.Mask.BeepOnError = ((bool)(resources.GetObject("rep_expireDate.Mask.BeepOnError")));
            this.rep_expireDate.Mask.EditMask = resources.GetString("rep_expireDate.Mask.EditMask");
            this.rep_expireDate.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("rep_expireDate.Mask.IgnoreMaskBlank")));
            this.rep_expireDate.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("rep_expireDate.Mask.MaskType")));
            this.rep_expireDate.Mask.PlaceHolder = ((char)(resources.GetObject("rep_expireDate.Mask.PlaceHolder")));
            this.rep_expireDate.Mask.SaveLiteral = ((bool)(resources.GetObject("rep_expireDate.Mask.SaveLiteral")));
            this.rep_expireDate.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("rep_expireDate.Mask.ShowPlaceHolders")));
            this.rep_expireDate.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("rep_expireDate.Mask.UseMaskAsDisplayFormat")));
            this.rep_expireDate.Name = "rep_expireDate";
            this.rep_expireDate.CustomDisplayText += new DevExpress.XtraEditors.Controls.CustomDisplayTextEventHandler(this.rep_expireDate_CustomDisplayText);
            // 
            // col_Batch
            // 
            this.col_Batch.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_Batch.AppearanceCell.FontSizeDelta")));
            this.col_Batch.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_Batch.AppearanceCell.FontStyleDelta")));
            this.col_Batch.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_Batch.AppearanceCell.GradientMode")));
            this.col_Batch.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_Batch.AppearanceCell.Image")));
            this.col_Batch.AppearanceCell.Options.UseTextOptions = true;
            this.col_Batch.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Batch.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Batch.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_Batch.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_Batch.AppearanceHeader.FontSizeDelta")));
            this.col_Batch.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_Batch.AppearanceHeader.FontStyleDelta")));
            this.col_Batch.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_Batch.AppearanceHeader.GradientMode")));
            this.col_Batch.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_Batch.AppearanceHeader.Image")));
            this.col_Batch.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Batch.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Batch.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Batch.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_Batch, "col_Batch");
            this.col_Batch.FieldName = "Batch";
            this.col_Batch.Name = "col_Batch";
            this.col_Batch.OptionsColumn.AllowEdit = false;
            this.col_Batch.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_Batch.OptionsColumn.ReadOnly = true;
            this.col_Batch.OptionsFilter.AllowFilter = false;
            // 
            // col_Height
            // 
            resources.ApplyResources(this.col_Height, "col_Height");
            this.col_Height.ColumnEdit = this.repSpin;
            this.col_Height.FieldName = "Height";
            this.col_Height.Name = "col_Height";
            this.col_Height.OptionsColumn.AllowEdit = false;
            this.col_Height.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_Height.OptionsColumn.ReadOnly = true;
            this.col_Height.OptionsFilter.AllowFilter = false;
            // 
            // col_Width
            // 
            resources.ApplyResources(this.col_Width, "col_Width");
            this.col_Width.ColumnEdit = this.repSpin;
            this.col_Width.FieldName = "Width";
            this.col_Width.Name = "col_Width";
            this.col_Width.OptionsColumn.AllowEdit = false;
            this.col_Width.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_Width.OptionsColumn.ReadOnly = true;
            this.col_Width.OptionsFilter.AllowFilter = false;
            // 
            // col_Length
            // 
            resources.ApplyResources(this.col_Length, "col_Length");
            this.col_Length.ColumnEdit = this.repSpin;
            this.col_Length.FieldName = "Length";
            this.col_Length.Name = "col_Length";
            this.col_Length.OptionsColumn.AllowEdit = false;
            this.col_Length.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_Length.OptionsColumn.ReadOnly = true;
            this.col_Length.OptionsFilter.AllowFilter = false;
            // 
            // col_TotalQty
            // 
            resources.ApplyResources(this.col_TotalQty, "col_TotalQty");
            this.col_TotalQty.FieldName = "TotalQty";
            this.col_TotalQty.Name = "col_TotalQty";
            this.col_TotalQty.OptionsColumn.AllowEdit = false;
            this.col_TotalQty.OptionsColumn.AllowFocus = false;
            this.col_TotalQty.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_TotalQty.OptionsColumn.ReadOnly = true;
            this.col_TotalQty.OptionsFilter.AllowFilter = false;
            this.col_TotalQty.UnboundType = DevExpress.Data.UnboundColumnType.Object;
            // 
            // col_PiecesCount
            // 
            this.col_PiecesCount.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_PiecesCount.AppearanceCell.FontSizeDelta")));
            this.col_PiecesCount.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_PiecesCount.AppearanceCell.FontStyleDelta")));
            this.col_PiecesCount.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_PiecesCount.AppearanceCell.GradientMode")));
            this.col_PiecesCount.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_PiecesCount.AppearanceCell.Image")));
            this.col_PiecesCount.AppearanceCell.Options.UseTextOptions = true;
            this.col_PiecesCount.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_PiecesCount.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_PiecesCount.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_PiecesCount.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_PiecesCount.AppearanceHeader.FontSizeDelta")));
            this.col_PiecesCount.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_PiecesCount.AppearanceHeader.FontStyleDelta")));
            this.col_PiecesCount.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_PiecesCount.AppearanceHeader.GradientMode")));
            this.col_PiecesCount.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_PiecesCount.AppearanceHeader.Image")));
            this.col_PiecesCount.AppearanceHeader.Options.UseTextOptions = true;
            this.col_PiecesCount.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_PiecesCount.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_PiecesCount.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_PiecesCount, "col_PiecesCount");
            this.col_PiecesCount.ColumnEdit = this.repSpin;
            this.col_PiecesCount.FieldName = "PiecesCount";
            this.col_PiecesCount.Name = "col_PiecesCount";
            this.col_PiecesCount.OptionsColumn.AllowEdit = false;
            this.col_PiecesCount.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_PiecesCount.OptionsColumn.ReadOnly = true;
            this.col_PiecesCount.OptionsFilter.AllowFilter = false;
            // 
            // col_ItemDescription
            // 
            this.col_ItemDescription.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_ItemDescription.AppearanceCell.FontSizeDelta")));
            this.col_ItemDescription.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_ItemDescription.AppearanceCell.FontStyleDelta")));
            this.col_ItemDescription.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_ItemDescription.AppearanceCell.GradientMode")));
            this.col_ItemDescription.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_ItemDescription.AppearanceCell.Image")));
            this.col_ItemDescription.AppearanceCell.Options.UseTextOptions = true;
            this.col_ItemDescription.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_ItemDescription.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_ItemDescription.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_ItemDescription.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_ItemDescription.AppearanceHeader.FontSizeDelta")));
            this.col_ItemDescription.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_ItemDescription.AppearanceHeader.FontStyleDelta")));
            this.col_ItemDescription.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_ItemDescription.AppearanceHeader.GradientMode")));
            this.col_ItemDescription.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_ItemDescription.AppearanceHeader.Image")));
            this.col_ItemDescription.AppearanceHeader.Options.UseTextOptions = true;
            this.col_ItemDescription.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_ItemDescription.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_ItemDescription.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_ItemDescription, "col_ItemDescription");
            this.col_ItemDescription.FieldName = "ItemDescription";
            this.col_ItemDescription.Name = "col_ItemDescription";
            this.col_ItemDescription.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.True;
            this.col_ItemDescription.OptionsFilter.AllowFilter = false;
            this.col_ItemDescription.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // col_ItemDescriptionEn
            // 
            this.col_ItemDescriptionEn.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_ItemDescriptionEn.AppearanceCell.FontSizeDelta")));
            this.col_ItemDescriptionEn.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_ItemDescriptionEn.AppearanceCell.FontStyleDelta")));
            this.col_ItemDescriptionEn.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_ItemDescriptionEn.AppearanceCell.GradientMode")));
            this.col_ItemDescriptionEn.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_ItemDescriptionEn.AppearanceCell.Image")));
            this.col_ItemDescriptionEn.AppearanceCell.Options.UseTextOptions = true;
            this.col_ItemDescriptionEn.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_ItemDescriptionEn.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_ItemDescriptionEn.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_ItemDescriptionEn.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_ItemDescriptionEn.AppearanceHeader.FontSizeDelta")));
            this.col_ItemDescriptionEn.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_ItemDescriptionEn.AppearanceHeader.FontStyleDelta")));
            this.col_ItemDescriptionEn.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_ItemDescriptionEn.AppearanceHeader.GradientMode")));
            this.col_ItemDescriptionEn.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_ItemDescriptionEn.AppearanceHeader.Image")));
            this.col_ItemDescriptionEn.AppearanceHeader.Options.UseTextOptions = true;
            this.col_ItemDescriptionEn.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_ItemDescriptionEn.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_ItemDescriptionEn.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_ItemDescriptionEn, "col_ItemDescriptionEn");
            this.col_ItemDescriptionEn.FieldName = "ItemDescriptionEn";
            this.col_ItemDescriptionEn.Name = "col_ItemDescriptionEn";
            this.col_ItemDescriptionEn.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.True;
            this.col_ItemDescriptionEn.OptionsFilter.AllowFilter = false;
            this.col_ItemDescriptionEn.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // col_SalesTax
            // 
            resources.ApplyResources(this.col_SalesTax, "col_SalesTax");
            this.col_SalesTax.ColumnEdit = this.repSpin;
            this.col_SalesTax.FieldName = "SalesTax";
            this.col_SalesTax.Name = "col_SalesTax";
            this.col_SalesTax.OptionsColumn.AllowEdit = false;
            this.col_SalesTax.OptionsColumn.AllowFocus = false;
            this.col_SalesTax.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_SalesTax.OptionsColumn.ReadOnly = true;
            this.col_SalesTax.OptionsFilter.AllowFilter = false;
            this.col_SalesTax.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // col_DiscountRatio2
            // 
            resources.ApplyResources(this.col_DiscountRatio2, "col_DiscountRatio2");
            this.col_DiscountRatio2.ColumnEdit = this.repDiscountRatio;
            this.col_DiscountRatio2.FieldName = "DiscountRatio2";
            this.col_DiscountRatio2.Name = "col_DiscountRatio2";
            this.col_DiscountRatio2.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_DiscountRatio2.OptionsFilter.AllowFilter = false;
            // 
            // col_DiscountRatio3
            // 
            resources.ApplyResources(this.col_DiscountRatio3, "col_DiscountRatio3");
            this.col_DiscountRatio3.ColumnEdit = this.repDiscountRatio;
            this.col_DiscountRatio3.FieldName = "DiscountRatio3";
            this.col_DiscountRatio3.Name = "col_DiscountRatio3";
            this.col_DiscountRatio3.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_DiscountRatio3.OptionsFilter.AllowFilter = false;
            // 
            // col_Serial
            // 
            this.col_Serial.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_Serial.AppearanceCell.FontSizeDelta")));
            this.col_Serial.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_Serial.AppearanceCell.FontStyleDelta")));
            this.col_Serial.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_Serial.AppearanceCell.GradientMode")));
            this.col_Serial.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_Serial.AppearanceCell.Image")));
            this.col_Serial.AppearanceCell.Options.UseTextOptions = true;
            this.col_Serial.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Serial.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_Serial.AppearanceHeader.FontSizeDelta")));
            this.col_Serial.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_Serial.AppearanceHeader.FontStyleDelta")));
            this.col_Serial.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_Serial.AppearanceHeader.GradientMode")));
            this.col_Serial.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_Serial.AppearanceHeader.Image")));
            this.col_Serial.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Serial.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.col_Serial, "col_Serial");
            this.col_Serial.FieldName = "Serial";
            this.col_Serial.Name = "col_Serial";
            this.col_Serial.OptionsColumn.AllowEdit = false;
            this.col_Serial.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_Serial.OptionsColumn.ReadOnly = true;
            // 
            // col_Serial2
            // 
            this.col_Serial2.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_Serial2.AppearanceCell.FontSizeDelta")));
            this.col_Serial2.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_Serial2.AppearanceCell.FontStyleDelta")));
            this.col_Serial2.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_Serial2.AppearanceCell.GradientMode")));
            this.col_Serial2.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_Serial2.AppearanceCell.Image")));
            this.col_Serial2.AppearanceCell.Options.UseTextOptions = true;
            this.col_Serial2.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Serial2.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_Serial2.AppearanceHeader.FontSizeDelta")));
            this.col_Serial2.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_Serial2.AppearanceHeader.FontStyleDelta")));
            this.col_Serial2.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_Serial2.AppearanceHeader.GradientMode")));
            this.col_Serial2.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_Serial2.AppearanceHeader.Image")));
            this.col_Serial2.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Serial2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.col_Serial2, "col_Serial2");
            this.col_Serial2.FieldName = "Serial2";
            this.col_Serial2.Name = "col_Serial2";
            this.col_Serial2.OptionsColumn.AllowEdit = false;
            this.col_Serial2.OptionsColumn.ReadOnly = true;
            // 
            // gridColumn25
            // 
            resources.ApplyResources(this.gridColumn25, "gridColumn25");
            this.gridColumn25.ColumnEdit = this.repManufactureDate;
            this.gridColumn25.FieldName = "ManufactureDate";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.OptionsColumn.AllowEdit = false;
            this.gridColumn25.OptionsColumn.ReadOnly = true;
            // 
            // repManufactureDate
            // 
            resources.ApplyResources(this.repManufactureDate, "repManufactureDate");
            this.repManufactureDate.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repManufactureDate.Buttons"))))});
            this.repManufactureDate.CalendarTimeProperties.AccessibleDescription = resources.GetString("repManufactureDate.CalendarTimeProperties.AccessibleDescription");
            this.repManufactureDate.CalendarTimeProperties.AccessibleName = resources.GetString("repManufactureDate.CalendarTimeProperties.AccessibleName");
            this.repManufactureDate.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("repManufactureDate.CalendarTimeProperties.AutoHeight")));
            this.repManufactureDate.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repManufactureDate.CalendarTimeProperties.Buttons"))))});
            this.repManufactureDate.CalendarTimeProperties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repManufactureDate.CalendarTimeProperties.Mask.AutoComplete")));
            this.repManufactureDate.CalendarTimeProperties.Mask.BeepOnError = ((bool)(resources.GetObject("repManufactureDate.CalendarTimeProperties.Mask.BeepOnError")));
            this.repManufactureDate.CalendarTimeProperties.Mask.EditMask = resources.GetString("repManufactureDate.CalendarTimeProperties.Mask.EditMask");
            this.repManufactureDate.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repManufactureDate.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.repManufactureDate.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repManufactureDate.CalendarTimeProperties.Mask.MaskType")));
            this.repManufactureDate.CalendarTimeProperties.Mask.PlaceHolder = ((char)(resources.GetObject("repManufactureDate.CalendarTimeProperties.Mask.PlaceHolder")));
            this.repManufactureDate.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("repManufactureDate.CalendarTimeProperties.Mask.SaveLiteral")));
            this.repManufactureDate.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repManufactureDate.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.repManufactureDate.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repManufactureDate.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat")));
            this.repManufactureDate.CalendarTimeProperties.NullValuePrompt = resources.GetString("repManufactureDate.CalendarTimeProperties.NullValuePrompt");
            this.repManufactureDate.CalendarTimeProperties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("repManufactureDate.CalendarTimeProperties.NullValuePromptShowForEmptyValue")));
            this.repManufactureDate.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repManufactureDate.Mask.AutoComplete")));
            this.repManufactureDate.Mask.BeepOnError = ((bool)(resources.GetObject("repManufactureDate.Mask.BeepOnError")));
            this.repManufactureDate.Mask.EditMask = resources.GetString("repManufactureDate.Mask.EditMask");
            this.repManufactureDate.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repManufactureDate.Mask.IgnoreMaskBlank")));
            this.repManufactureDate.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repManufactureDate.Mask.MaskType")));
            this.repManufactureDate.Mask.PlaceHolder = ((char)(resources.GetObject("repManufactureDate.Mask.PlaceHolder")));
            this.repManufactureDate.Mask.SaveLiteral = ((bool)(resources.GetObject("repManufactureDate.Mask.SaveLiteral")));
            this.repManufactureDate.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repManufactureDate.Mask.ShowPlaceHolders")));
            this.repManufactureDate.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repManufactureDate.Mask.UseMaskAsDisplayFormat")));
            this.repManufactureDate.Name = "repManufactureDate";
            // 
            // col_CusTax
            // 
            resources.ApplyResources(this.col_CusTax, "col_CusTax");
            this.col_CusTax.FieldName = "CustomTax";
            this.col_CusTax.Name = "col_CusTax";
            this.col_CusTax.OptionsColumn.AllowEdit = false;
            this.col_CusTax.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_CusTax.OptionsColumn.ReadOnly = true;
            this.col_CusTax.OptionsFilter.AllowFilter = false;
            this.col_CusTax.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // col_LibraQty
            // 
            resources.ApplyResources(this.col_LibraQty, "col_LibraQty");
            this.col_LibraQty.FieldName = "LibraQty";
            this.col_LibraQty.Name = "col_LibraQty";
            this.col_LibraQty.OptionsColumn.AllowEdit = false;
            this.col_LibraQty.OptionsColumn.ReadOnly = true;
            // 
            // col_IsOffer
            // 
            resources.ApplyResources(this.col_IsOffer, "col_IsOffer");
            this.col_IsOffer.FieldName = "IsOffer";
            this.col_IsOffer.Name = "col_IsOffer";
            this.col_IsOffer.OptionsColumn.AllowEdit = false;
            this.col_IsOffer.OptionsColumn.ReadOnly = true;
            this.col_IsOffer.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // col_PricingWithSmall
            // 
            resources.ApplyResources(this.col_PricingWithSmall, "col_PricingWithSmall");
            this.col_PricingWithSmall.FieldName = "PricingWithSmall";
            this.col_PricingWithSmall.Name = "col_PricingWithSmall";
            this.col_PricingWithSmall.OptionsColumn.AllowEdit = false;
            this.col_PricingWithSmall.OptionsColumn.ReadOnly = true;
            this.col_PricingWithSmall.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // col_VariableWeight
            // 
            resources.ApplyResources(this.col_VariableWeight, "col_VariableWeight");
            this.col_VariableWeight.FieldName = "VariableWeight";
            this.col_VariableWeight.Name = "col_VariableWeight";
            this.col_VariableWeight.OptionsColumn.AllowEdit = false;
            this.col_VariableWeight.OptionsColumn.ReadOnly = true;
            this.col_VariableWeight.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // col_kg_Weight_libra
            // 
            resources.ApplyResources(this.col_kg_Weight_libra, "col_kg_Weight_libra");
            this.col_kg_Weight_libra.FieldName = "kg_Weight_libra";
            this.col_kg_Weight_libra.Name = "col_kg_Weight_libra";
            this.col_kg_Weight_libra.OptionsColumn.AllowEdit = false;
            this.col_kg_Weight_libra.OptionsColumn.ReadOnly = true;
            // 
            // col_Is_Libra
            // 
            resources.ApplyResources(this.col_Is_Libra, "col_Is_Libra");
            this.col_Is_Libra.FieldName = "Is_Libra";
            this.col_Is_Libra.Name = "col_Is_Libra";
            this.col_Is_Libra.OptionsColumn.AllowEdit = false;
            this.col_Is_Libra.OptionsColumn.ReadOnly = true;
            this.col_Is_Libra.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // col_Pack
            // 
            resources.ApplyResources(this.col_Pack, "col_Pack");
            this.col_Pack.ColumnEdit = this.repSpin;
            this.col_Pack.FieldName = "Pack";
            this.col_Pack.Name = "col_Pack";
            this.col_Pack.OptionsColumn.AllowEdit = false;
            this.col_Pack.OptionsColumn.ReadOnly = true;
            // 
            // colbonusDiscount
            // 
            resources.ApplyResources(this.colbonusDiscount, "colbonusDiscount");
            this.colbonusDiscount.ColumnEdit = this.repSpin;
            this.colbonusDiscount.FieldName = "bonusDiscount";
            this.colbonusDiscount.Name = "colbonusDiscount";
            this.colbonusDiscount.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("colbonusDiscount.Summary"))), resources.GetString("colbonusDiscount.Summary1"), resources.GetString("colbonusDiscount.Summary2"))});
            // 
            // colTaxType
            // 
            resources.ApplyResources(this.colTaxType, "colTaxType");
            this.colTaxType.ColumnEdit = this.repTaxTypes;
            this.colTaxType.FieldName = "TaxType";
            this.colTaxType.Name = "colTaxType";
            // 
            // repTaxTypes
            // 
            resources.ApplyResources(this.repTaxTypes, "repTaxTypes");
            this.repTaxTypes.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repTaxTypes.Buttons"))))});
            this.repTaxTypes.Name = "repTaxTypes";
            this.repTaxTypes.View = this.gridView6;
            // 
            // gridView6
            // 
            resources.ApplyResources(this.gridView6, "gridView6");
            this.gridView6.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.DescriptionAr,
            this.Code,
            this.E_TaxableTypeId});
            this.gridView6.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView6.Name = "gridView6";
            this.gridView6.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView6.OptionsView.ShowGroupPanel = false;
            // 
            // DescriptionAr
            // 
            resources.ApplyResources(this.DescriptionAr, "DescriptionAr");
            this.DescriptionAr.FieldName = "DescriptionAr";
            this.DescriptionAr.Name = "DescriptionAr";
            // 
            // Code
            // 
            resources.ApplyResources(this.Code, "Code");
            this.Code.FieldName = "Code";
            this.Code.Name = "Code";
            // 
            // E_TaxableTypeId
            // 
            resources.ApplyResources(this.E_TaxableTypeId, "E_TaxableTypeId");
            this.E_TaxableTypeId.FieldName = "E_TaxableTypeId";
            this.E_TaxableTypeId.Name = "E_TaxableTypeId";
            // 
            // colEtaxValue
            // 
            resources.ApplyResources(this.colEtaxValue, "colEtaxValue");
            this.colEtaxValue.ColumnEdit = this.repSpin;
            this.colEtaxValue.FieldName = "EtaxValue";
            this.colEtaxValue.Name = "colEtaxValue";
            this.colEtaxValue.OptionsColumn.AllowEdit = false;
            this.colEtaxValue.OptionsColumn.ReadOnly = true;
            this.colEtaxValue.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("colEtaxValue.Summary"))), resources.GetString("colEtaxValue.Summary1"), resources.GetString("colEtaxValue.Summary2"))});
            // 
            // col_ETaxRatio
            // 
            resources.ApplyResources(this.col_ETaxRatio, "col_ETaxRatio");
            this.col_ETaxRatio.ColumnEdit = this.repSpin;
            this.col_ETaxRatio.FieldName = "ETaxRatio";
            this.col_ETaxRatio.Name = "col_ETaxRatio";
            // 
            // btn_AddTaxes
            // 
            resources.ApplyResources(this.btn_AddTaxes, "btn_AddTaxes");
            this.btn_AddTaxes.ColumnEdit = this.rep_btnAddTaxes;
            this.btn_AddTaxes.FieldName = "btn_AddTaxes";
            this.btn_AddTaxes.Name = "btn_AddTaxes";
            // 
            // rep_btnAddTaxes
            // 
            resources.ApplyResources(this.rep_btnAddTaxes, "rep_btnAddTaxes");
            this.rep_btnAddTaxes.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_btnAddTaxes.Buttons"))))});
            this.rep_btnAddTaxes.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("rep_btnAddTaxes.Mask.AutoComplete")));
            this.rep_btnAddTaxes.Mask.BeepOnError = ((bool)(resources.GetObject("rep_btnAddTaxes.Mask.BeepOnError")));
            this.rep_btnAddTaxes.Mask.EditMask = resources.GetString("rep_btnAddTaxes.Mask.EditMask");
            this.rep_btnAddTaxes.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("rep_btnAddTaxes.Mask.IgnoreMaskBlank")));
            this.rep_btnAddTaxes.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("rep_btnAddTaxes.Mask.MaskType")));
            this.rep_btnAddTaxes.Mask.PlaceHolder = ((char)(resources.GetObject("rep_btnAddTaxes.Mask.PlaceHolder")));
            this.rep_btnAddTaxes.Mask.SaveLiteral = ((bool)(resources.GetObject("rep_btnAddTaxes.Mask.SaveLiteral")));
            this.rep_btnAddTaxes.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("rep_btnAddTaxes.Mask.ShowPlaceHolders")));
            this.rep_btnAddTaxes.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("rep_btnAddTaxes.Mask.UseMaskAsDisplayFormat")));
            this.rep_btnAddTaxes.Name = "rep_btnAddTaxes";
            this.rep_btnAddTaxes.Click += new System.EventHandler(this.rep_btnAddTaxes_Click);
            // 
            // TotalTaxes
            // 
            resources.ApplyResources(this.TotalTaxes, "TotalTaxes");
            this.TotalTaxes.FieldName = "TotalTaxes";
            this.TotalTaxes.Name = "TotalTaxes";
            this.TotalTaxes.OptionsColumn.AllowEdit = false;
            this.TotalTaxes.OptionsColumn.ReadOnly = true;
            // 
            // totalTaxesRatio
            // 
            resources.ApplyResources(this.totalTaxesRatio, "totalTaxesRatio");
            this.totalTaxesRatio.FieldName = "totalTaxesRatio";
            this.totalTaxesRatio.Name = "totalTaxesRatio";
            this.totalTaxesRatio.OptionsColumn.AllowEdit = false;
            this.totalTaxesRatio.OptionsColumn.ReadOnly = true;
            // 
            // totalTableTaxes
            // 
            resources.ApplyResources(this.totalTableTaxes, "totalTableTaxes");
            this.totalTableTaxes.Name = "totalTableTaxes";
            // 
            // salePriceWithTaxTable
            // 
            resources.ApplyResources(this.salePriceWithTaxTable, "salePriceWithTaxTable");
            this.salePriceWithTaxTable.FieldName = "salePriceWithTaxTable";
            this.salePriceWithTaxTable.Name = "salePriceWithTaxTable";
            // 
            // addTaxValue
            // 
            resources.ApplyResources(this.addTaxValue, "addTaxValue");
            this.addTaxValue.FieldName = "addTaxValue";
            this.addTaxValue.Name = "addTaxValue";
            this.addTaxValue.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // tableTaxValue
            // 
            resources.ApplyResources(this.tableTaxValue, "tableTaxValue");
            this.tableTaxValue.FieldName = "tableTaxValue";
            this.tableTaxValue.Name = "tableTaxValue";
            this.tableTaxValue.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // col_TaxValue
            // 
            resources.ApplyResources(this.col_TaxValue, "col_TaxValue");
            this.col_TaxValue.FieldName = "TaxValue";
            this.col_TaxValue.Name = "col_TaxValue";
            this.col_TaxValue.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // col_TotalSubCustomTax
            // 
            resources.ApplyResources(this.col_TotalSubCustomTax, "col_TotalSubCustomTax");
            this.col_TotalSubCustomTax.FieldName = "TotalSubCustomTax";
            this.col_TotalSubCustomTax.Name = "col_TotalSubCustomTax";
            this.col_TotalSubCustomTax.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // col_TotalSubAddTax
            // 
            resources.ApplyResources(this.col_TotalSubAddTax, "col_TotalSubAddTax");
            this.col_TotalSubAddTax.FieldName = "TotalSubAddTax";
            this.col_TotalSubAddTax.Name = "col_TotalSubAddTax";
            this.col_TotalSubAddTax.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // col_TotalSubDiscountTax
            // 
            resources.ApplyResources(this.col_TotalSubDiscountTax, "col_TotalSubDiscountTax");
            this.col_TotalSubDiscountTax.FieldName = "TotalSubDiscountTax";
            this.col_TotalSubDiscountTax.Name = "col_TotalSubDiscountTax";
            this.col_TotalSubDiscountTax.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // repExpireDate
            // 
            resources.ApplyResources(this.repExpireDate, "repExpireDate");
            this.repExpireDate.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repExpireDate.Buttons"))))});
            this.repExpireDate.CalendarTimeProperties.AccessibleDescription = resources.GetString("repExpireDate.CalendarTimeProperties.AccessibleDescription");
            this.repExpireDate.CalendarTimeProperties.AccessibleName = resources.GetString("repExpireDate.CalendarTimeProperties.AccessibleName");
            this.repExpireDate.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("repExpireDate.CalendarTimeProperties.AutoHeight")));
            this.repExpireDate.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.repExpireDate.CalendarTimeProperties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repExpireDate.CalendarTimeProperties.Mask.AutoComplete")));
            this.repExpireDate.CalendarTimeProperties.Mask.BeepOnError = ((bool)(resources.GetObject("repExpireDate.CalendarTimeProperties.Mask.BeepOnError")));
            this.repExpireDate.CalendarTimeProperties.Mask.EditMask = resources.GetString("repExpireDate.CalendarTimeProperties.Mask.EditMask");
            this.repExpireDate.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repExpireDate.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.repExpireDate.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repExpireDate.CalendarTimeProperties.Mask.MaskType")));
            this.repExpireDate.CalendarTimeProperties.Mask.PlaceHolder = ((char)(resources.GetObject("repExpireDate.CalendarTimeProperties.Mask.PlaceHolder")));
            this.repExpireDate.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("repExpireDate.CalendarTimeProperties.Mask.SaveLiteral")));
            this.repExpireDate.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repExpireDate.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.repExpireDate.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repExpireDate.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat")));
            this.repExpireDate.CalendarTimeProperties.NullValuePrompt = resources.GetString("repExpireDate.CalendarTimeProperties.NullValuePrompt");
            this.repExpireDate.CalendarTimeProperties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("repExpireDate.CalendarTimeProperties.NullValuePromptShowForEmptyValue")));
            this.repExpireDate.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repExpireDate.Mask.AutoComplete")));
            this.repExpireDate.Mask.BeepOnError = ((bool)(resources.GetObject("repExpireDate.Mask.BeepOnError")));
            this.repExpireDate.Mask.EditMask = resources.GetString("repExpireDate.Mask.EditMask");
            this.repExpireDate.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repExpireDate.Mask.IgnoreMaskBlank")));
            this.repExpireDate.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repExpireDate.Mask.MaskType")));
            this.repExpireDate.Mask.PlaceHolder = ((char)(resources.GetObject("repExpireDate.Mask.PlaceHolder")));
            this.repExpireDate.Mask.SaveLiteral = ((bool)(resources.GetObject("repExpireDate.Mask.SaveLiteral")));
            this.repExpireDate.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repExpireDate.Mask.ShowPlaceHolders")));
            this.repExpireDate.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repExpireDate.Mask.UseMaskAsDisplayFormat")));
            this.repExpireDate.Name = "repExpireDate";
            // 
            // repLocation
            // 
            resources.ApplyResources(this.repLocation, "repLocation");
            this.repLocation.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repLocation.Buttons"))))});
            this.repLocation.Name = "repLocation";
            this.repLocation.View = this.gridView3;
            // 
            // gridView3
            // 
            this.gridView3.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView3.Appearance.HeaderPanel.FontSizeDelta")));
            this.gridView3.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView3.Appearance.HeaderPanel.FontStyleDelta")));
            this.gridView3.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView3.Appearance.HeaderPanel.GradientMode")));
            this.gridView3.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView3.Appearance.HeaderPanel.Image")));
            this.gridView3.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView3.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView3.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gridView3.Appearance.Row.FontSizeDelta")));
            this.gridView3.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView3.Appearance.Row.FontStyleDelta")));
            this.gridView3.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView3.Appearance.Row.GradientMode")));
            this.gridView3.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView3.Appearance.Row.Image")));
            this.gridView3.Appearance.Row.Options.UseTextOptions = true;
            this.gridView3.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.gridView3, "gridView3");
            this.gridView3.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colDescription,
            this.colLocationNameAr,
            this.colLocationId});
            this.gridView3.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            // 
            // colDescription
            // 
            resources.ApplyResources(this.colDescription, "colDescription");
            this.colDescription.FieldName = "Description";
            this.colDescription.Name = "colDescription";
            // 
            // colLocationNameAr
            // 
            resources.ApplyResources(this.colLocationNameAr, "colLocationNameAr");
            this.colLocationNameAr.FieldName = "LocationNameAr";
            this.colLocationNameAr.Name = "colLocationNameAr";
            // 
            // colLocationId
            // 
            resources.ApplyResources(this.colLocationId, "colLocationId");
            this.colLocationId.FieldName = "LocationId";
            this.colLocationId.Name = "colLocationId";
            // 
            // repExpireDate_txt
            // 
            resources.ApplyResources(this.repExpireDate_txt, "repExpireDate_txt");
            this.repExpireDate_txt.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repExpireDate_txt.Mask.AutoComplete")));
            this.repExpireDate_txt.Mask.BeepOnError = ((bool)(resources.GetObject("repExpireDate_txt.Mask.BeepOnError")));
            this.repExpireDate_txt.Mask.EditMask = resources.GetString("repExpireDate_txt.Mask.EditMask");
            this.repExpireDate_txt.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repExpireDate_txt.Mask.IgnoreMaskBlank")));
            this.repExpireDate_txt.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repExpireDate_txt.Mask.MaskType")));
            this.repExpireDate_txt.Mask.PlaceHolder = ((char)(resources.GetObject("repExpireDate_txt.Mask.PlaceHolder")));
            this.repExpireDate_txt.Mask.SaveLiteral = ((bool)(resources.GetObject("repExpireDate_txt.Mask.SaveLiteral")));
            this.repExpireDate_txt.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repExpireDate_txt.Mask.ShowPlaceHolders")));
            this.repExpireDate_txt.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repExpireDate_txt.Mask.UseMaskAsDisplayFormat")));
            this.repExpireDate_txt.Name = "repExpireDate_txt";
            // 
            // lkp_Customers
            // 
            resources.ApplyResources(this.lkp_Customers, "lkp_Customers");
            this.lkp_Customers.EnterMoveNextControl = true;
            this.lkp_Customers.MenuManager = this.barManager1;
            this.lkp_Customers.Name = "lkp_Customers";
            this.lkp_Customers.Properties.AccessibleDescription = resources.GetString("lkp_Customers.Properties.AccessibleDescription");
            this.lkp_Customers.Properties.AccessibleName = resources.GetString("lkp_Customers.Properties.AccessibleName");
            this.lkp_Customers.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkp_Customers.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_Customers.Properties.Appearance.FontSizeDelta")));
            this.lkp_Customers.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_Customers.Properties.Appearance.FontStyleDelta")));
            this.lkp_Customers.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_Customers.Properties.Appearance.GradientMode")));
            this.lkp_Customers.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_Customers.Properties.Appearance.Image")));
            this.lkp_Customers.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_Customers.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_Customers.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_Customers.Properties.AutoHeight")));
            this.lkp_Customers.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_Customers.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_Customers.Properties.Buttons"))))});
            this.lkp_Customers.Properties.ImmediatePopup = true;
            this.lkp_Customers.Properties.NullText = resources.GetString("lkp_Customers.Properties.NullText");
            this.lkp_Customers.Properties.NullValuePrompt = resources.GetString("lkp_Customers.Properties.NullValuePrompt");
            this.lkp_Customers.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_Customers.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_Customers.Properties.PopupFilterMode = DevExpress.XtraEditors.PopupFilterMode.Contains;
            this.lkp_Customers.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.lkp_Customers.Properties.View = this.gridView1;
            this.lkp_Customers.EditValueChanged += new System.EventHandler(this.lkp_Customers_EditValueChanged);
            this.lkp_Customers.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // gridView1
            // 
            this.gridView1.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.HeaderPanel.FontSizeDelta")));
            this.gridView1.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.HeaderPanel.FontStyleDelta")));
            this.gridView1.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.HeaderPanel.GradientMode")));
            this.gridView1.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.HeaderPanel.Image")));
            this.gridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.Row.FontSizeDelta")));
            this.gridView1.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.Row.FontStyleDelta")));
            this.gridView1.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.Row.GradientMode")));
            this.gridView1.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.Row.Image")));
            this.gridView1.Appearance.Row.Options.UseTextOptions = true;
            this.gridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.gridView1, "gridView1");
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn3,
            this.gridColumn6,
            this.gridColumn18});
            this.gridView1.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView1.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView1.OptionsView.EnableAppearanceOddRow = true;
            this.gridView1.OptionsView.ShowAutoFilterRow = true;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.OptionsView.ShowIndicator = false;
            // 
            // gridColumn19
            // 
            resources.ApplyResources(this.gridColumn19, "gridColumn19");
            this.gridColumn19.FieldName = "CustomerId";
            this.gridColumn19.Name = "gridColumn19";
            // 
            // gridColumn20
            // 
            resources.ApplyResources(this.gridColumn20, "gridColumn20");
            this.gridColumn20.FieldName = "CusCode";
            this.gridColumn20.Name = "gridColumn20";
            // 
            // gridColumn21
            // 
            resources.ApplyResources(this.gridColumn21, "gridColumn21");
            this.gridColumn21.FieldName = "CusNameAr";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // gridColumn22
            // 
            resources.ApplyResources(this.gridColumn22, "gridColumn22");
            this.gridColumn22.FieldName = "CusNameEn";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // gridColumn3
            // 
            resources.ApplyResources(this.gridColumn3, "gridColumn3");
            this.gridColumn3.FieldName = "City";
            this.gridColumn3.Name = "gridColumn3";
            // 
            // gridColumn6
            // 
            resources.ApplyResources(this.gridColumn6, "gridColumn6");
            this.gridColumn6.FieldName = "Mobile";
            this.gridColumn6.Name = "gridColumn6";
            // 
            // gridColumn18
            // 
            resources.ApplyResources(this.gridColumn18, "gridColumn18");
            this.gridColumn18.FieldName = "GroupId";
            this.gridColumn18.Name = "gridColumn18";
            // 
            // labelControl19
            // 
            resources.ApplyResources(this.labelControl19, "labelControl19");
            this.labelControl19.Name = "labelControl19";
            // 
            // txt_Total
            // 
            resources.ApplyResources(this.txt_Total, "txt_Total");
            this.txt_Total.EnterMoveNextControl = true;
            this.txt_Total.Name = "txt_Total";
            this.txt_Total.Properties.AccessibleDescription = resources.GetString("txt_Total.Properties.AccessibleDescription");
            this.txt_Total.Properties.AccessibleName = resources.GetString("txt_Total.Properties.AccessibleName");
            this.txt_Total.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txt_Total.Properties.Appearance.FontSizeDelta")));
            this.txt_Total.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txt_Total.Properties.Appearance.FontStyleDelta")));
            this.txt_Total.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txt_Total.Properties.Appearance.GradientMode")));
            this.txt_Total.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txt_Total.Properties.Appearance.Image")));
            this.txt_Total.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_Total.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_Total.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Total.Properties.AutoHeight")));
            this.txt_Total.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_Total.Properties.Mask.AutoComplete")));
            this.txt_Total.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_Total.Properties.Mask.BeepOnError")));
            this.txt_Total.Properties.Mask.EditMask = resources.GetString("txt_Total.Properties.Mask.EditMask");
            this.txt_Total.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Total.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Total.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Total.Properties.Mask.MaskType")));
            this.txt_Total.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_Total.Properties.Mask.PlaceHolder")));
            this.txt_Total.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Total.Properties.Mask.SaveLiteral")));
            this.txt_Total.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Total.Properties.Mask.ShowPlaceHolders")));
            this.txt_Total.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_Total.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_Total.Properties.NullValuePrompt = resources.GetString("txt_Total.Properties.NullValuePrompt");
            this.txt_Total.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_Total.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_Total.Properties.ReadOnly = true;
            this.txt_Total.TabStop = false;
            // 
            // labelControl18
            // 
            resources.ApplyResources(this.labelControl18, "labelControl18");
            this.labelControl18.Name = "labelControl18";
            // 
            // labelControl11
            // 
            resources.ApplyResources(this.labelControl11, "labelControl11");
            this.labelControl11.Name = "labelControl11";
            // 
            // txt_Remains
            // 
            resources.ApplyResources(this.txt_Remains, "txt_Remains");
            this.txt_Remains.EnterMoveNextControl = true;
            this.txt_Remains.Name = "txt_Remains";
            this.txt_Remains.Properties.AccessibleDescription = resources.GetString("txt_Remains.Properties.AccessibleDescription");
            this.txt_Remains.Properties.AccessibleName = resources.GetString("txt_Remains.Properties.AccessibleName");
            this.txt_Remains.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txt_Remains.Properties.Appearance.BackColor")));
            this.txt_Remains.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txt_Remains.Properties.Appearance.FontSizeDelta")));
            this.txt_Remains.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txt_Remains.Properties.Appearance.FontStyleDelta")));
            this.txt_Remains.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txt_Remains.Properties.Appearance.GradientMode")));
            this.txt_Remains.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txt_Remains.Properties.Appearance.Image")));
            this.txt_Remains.Properties.Appearance.Options.UseBackColor = true;
            this.txt_Remains.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_Remains.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_Remains.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Remains.Properties.AutoHeight")));
            this.txt_Remains.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_Remains.Properties.Mask.AutoComplete")));
            this.txt_Remains.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_Remains.Properties.Mask.BeepOnError")));
            this.txt_Remains.Properties.Mask.EditMask = resources.GetString("txt_Remains.Properties.Mask.EditMask");
            this.txt_Remains.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Remains.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Remains.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Remains.Properties.Mask.MaskType")));
            this.txt_Remains.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_Remains.Properties.Mask.PlaceHolder")));
            this.txt_Remains.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Remains.Properties.Mask.SaveLiteral")));
            this.txt_Remains.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Remains.Properties.Mask.ShowPlaceHolders")));
            this.txt_Remains.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_Remains.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_Remains.Properties.NullValuePrompt = resources.GetString("txt_Remains.Properties.NullValuePrompt");
            this.txt_Remains.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_Remains.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_Remains.EditValueChanged += new System.EventHandler(this.txt_paid_EditValueChanged);
            this.txt_Remains.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txt_Remains.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress);
            // 
            // txtNet
            // 
            resources.ApplyResources(this.txtNet, "txtNet");
            this.txtNet.EnterMoveNextControl = true;
            this.txtNet.Name = "txtNet";
            this.txtNet.Properties.AccessibleDescription = resources.GetString("txtNet.Properties.AccessibleDescription");
            this.txtNet.Properties.AccessibleName = resources.GetString("txtNet.Properties.AccessibleName");
            this.txtNet.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtNet.Properties.Appearance.FontSizeDelta")));
            this.txtNet.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtNet.Properties.Appearance.FontStyleDelta")));
            this.txtNet.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtNet.Properties.Appearance.GradientMode")));
            this.txtNet.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtNet.Properties.Appearance.Image")));
            this.txtNet.Properties.Appearance.Options.UseTextOptions = true;
            this.txtNet.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtNet.Properties.AutoHeight = ((bool)(resources.GetObject("txtNet.Properties.AutoHeight")));
            this.txtNet.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtNet.Properties.Mask.AutoComplete")));
            this.txtNet.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtNet.Properties.Mask.BeepOnError")));
            this.txtNet.Properties.Mask.EditMask = resources.GetString("txtNet.Properties.Mask.EditMask");
            this.txtNet.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtNet.Properties.Mask.IgnoreMaskBlank")));
            this.txtNet.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtNet.Properties.Mask.MaskType")));
            this.txtNet.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtNet.Properties.Mask.PlaceHolder")));
            this.txtNet.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtNet.Properties.Mask.SaveLiteral")));
            this.txtNet.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtNet.Properties.Mask.ShowPlaceHolders")));
            this.txtNet.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtNet.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtNet.Properties.NullValuePrompt = resources.GetString("txtNet.Properties.NullValuePrompt");
            this.txtNet.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtNet.Properties.NullValuePromptShowForEmptyValue")));
            this.txtNet.Properties.ReadOnly = true;
            this.txtNet.TabStop = false;
            // 
            // lbl_remains
            // 
            resources.ApplyResources(this.lbl_remains, "lbl_remains");
            this.lbl_remains.Name = "lbl_remains";
            // 
            // labelControl13
            // 
            resources.ApplyResources(this.labelControl13, "labelControl13");
            this.labelControl13.Name = "labelControl13";
            // 
            // lbl_Paid
            // 
            resources.ApplyResources(this.lbl_Paid, "lbl_Paid");
            this.lbl_Paid.Name = "lbl_Paid";
            // 
            // txt_paid
            // 
            resources.ApplyResources(this.txt_paid, "txt_paid");
            this.txt_paid.EnterMoveNextControl = true;
            this.txt_paid.Name = "txt_paid";
            this.txt_paid.Properties.AccessibleDescription = resources.GetString("txt_paid.Properties.AccessibleDescription");
            this.txt_paid.Properties.AccessibleName = resources.GetString("txt_paid.Properties.AccessibleName");
            this.txt_paid.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txt_paid.Properties.Appearance.BackColor")));
            this.txt_paid.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txt_paid.Properties.Appearance.FontSizeDelta")));
            this.txt_paid.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txt_paid.Properties.Appearance.FontStyleDelta")));
            this.txt_paid.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txt_paid.Properties.Appearance.GradientMode")));
            this.txt_paid.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txt_paid.Properties.Appearance.Image")));
            this.txt_paid.Properties.Appearance.Options.UseBackColor = true;
            this.txt_paid.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_paid.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_paid.Properties.AutoHeight = ((bool)(resources.GetObject("txt_paid.Properties.AutoHeight")));
            this.txt_paid.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_paid.Properties.Mask.AutoComplete")));
            this.txt_paid.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_paid.Properties.Mask.BeepOnError")));
            this.txt_paid.Properties.Mask.EditMask = resources.GetString("txt_paid.Properties.Mask.EditMask");
            this.txt_paid.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_paid.Properties.Mask.IgnoreMaskBlank")));
            this.txt_paid.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_paid.Properties.Mask.MaskType")));
            this.txt_paid.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_paid.Properties.Mask.PlaceHolder")));
            this.txt_paid.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_paid.Properties.Mask.SaveLiteral")));
            this.txt_paid.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_paid.Properties.Mask.ShowPlaceHolders")));
            this.txt_paid.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_paid.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_paid.Properties.NullValuePrompt = resources.GetString("txt_paid.Properties.NullValuePrompt");
            this.txt_paid.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_paid.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_paid.EditValueChanged += new System.EventHandler(this.txt_paid_EditValueChanged);
            this.txt_paid.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txt_paid.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress);
            // 
            // labelControl6
            // 
            resources.ApplyResources(this.labelControl6, "labelControl6");
            this.labelControl6.Name = "labelControl6";
            // 
            // labelControl7
            // 
            resources.ApplyResources(this.labelControl7, "labelControl7");
            this.labelControl7.Name = "labelControl7";
            // 
            // labelControl9
            // 
            resources.ApplyResources(this.labelControl9, "labelControl9");
            this.labelControl9.Name = "labelControl9";
            // 
            // btnAddCustomer
            // 
            resources.ApplyResources(this.btnAddCustomer, "btnAddCustomer");
            this.btnAddCustomer.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnAddCustomer.Image = global::Pharmacy.Properties.Resources.add32;
            this.btnAddCustomer.Name = "btnAddCustomer";
            this.btnAddCustomer.TabStop = false;
            this.btnAddCustomer.Click += new System.EventHandler(this.btnAddCustomer_Click);
            // 
            // txtExpenses
            // 
            resources.ApplyResources(this.txtExpenses, "txtExpenses");
            this.txtExpenses.EnterMoveNextControl = true;
            this.txtExpenses.Name = "txtExpenses";
            this.txtExpenses.Properties.AccessibleDescription = resources.GetString("txtExpenses.Properties.AccessibleDescription");
            this.txtExpenses.Properties.AccessibleName = resources.GetString("txtExpenses.Properties.AccessibleName");
            this.txtExpenses.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtExpenses.Properties.Appearance.FontSizeDelta")));
            this.txtExpenses.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtExpenses.Properties.Appearance.FontStyleDelta")));
            this.txtExpenses.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtExpenses.Properties.Appearance.GradientMode")));
            this.txtExpenses.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtExpenses.Properties.Appearance.Image")));
            this.txtExpenses.Properties.Appearance.Options.UseTextOptions = true;
            this.txtExpenses.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtExpenses.Properties.AutoHeight = ((bool)(resources.GetObject("txtExpenses.Properties.AutoHeight")));
            this.txtExpenses.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtExpenses.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtExpenses.Properties.Mask.AutoComplete")));
            this.txtExpenses.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtExpenses.Properties.Mask.BeepOnError")));
            this.txtExpenses.Properties.Mask.EditMask = resources.GetString("txtExpenses.Properties.Mask.EditMask");
            this.txtExpenses.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtExpenses.Properties.Mask.IgnoreMaskBlank")));
            this.txtExpenses.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtExpenses.Properties.Mask.MaskType")));
            this.txtExpenses.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtExpenses.Properties.Mask.PlaceHolder")));
            this.txtExpenses.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtExpenses.Properties.Mask.SaveLiteral")));
            this.txtExpenses.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtExpenses.Properties.Mask.ShowPlaceHolders")));
            this.txtExpenses.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtExpenses.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtExpenses.Properties.NullValuePrompt = resources.GetString("txtExpenses.Properties.NullValuePrompt");
            this.txtExpenses.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtExpenses.Properties.NullValuePromptShowForEmptyValue")));
            this.txtExpenses.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txtExpenses.Leave += new System.EventHandler(this.txtDiscountValue_Leave);
            // 
            // labelControl20
            // 
            resources.ApplyResources(this.labelControl20, "labelControl20");
            this.labelControl20.Name = "labelControl20";
            // 
            // groupControl1
            // 
            resources.ApplyResources(this.groupControl1, "groupControl1");
            this.groupControl1.AppearanceCaption.FontSizeDelta = ((int)(resources.GetObject("groupControl1.AppearanceCaption.FontSizeDelta")));
            this.groupControl1.AppearanceCaption.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("groupControl1.AppearanceCaption.FontStyleDelta")));
            this.groupControl1.AppearanceCaption.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("groupControl1.AppearanceCaption.GradientMode")));
            this.groupControl1.AppearanceCaption.Image = ((System.Drawing.Image)(resources.GetObject("groupControl1.AppearanceCaption.Image")));
            this.groupControl1.AppearanceCaption.Options.UseTextOptions = true;
            this.groupControl1.AppearanceCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.groupControl1.CaptionLocation = DevExpress.Utils.Locations.Right;
            this.groupControl1.Controls.Add(this.lkp_Drawers2);
            this.groupControl1.Controls.Add(this.labelControl17);
            this.groupControl1.Controls.Add(this.lkp_Drawers);
            this.groupControl1.Controls.Add(this.txt_PayAcc1_Paid);
            this.groupControl1.Controls.Add(this.labelControl28);
            this.groupControl1.Controls.Add(this.labelControl26);
            this.groupControl1.Controls.Add(this.txt_PayAcc2_Paid);
            this.groupControl1.Controls.Add(this.labelControl25);
            this.groupControl1.Controls.Add(this.txt_paid);
            this.groupControl1.Controls.Add(this.lbl_Paid);
            this.groupControl1.Controls.Add(this.txt_Remains);
            this.groupControl1.Controls.Add(this.lbl_remains);
            this.groupControl1.Name = "groupControl1";
            // 
            // lkp_Drawers2
            // 
            resources.ApplyResources(this.lkp_Drawers2, "lkp_Drawers2");
            this.lkp_Drawers2.EnterMoveNextControl = true;
            this.lkp_Drawers2.MenuManager = this.barManager1;
            this.lkp_Drawers2.Name = "lkp_Drawers2";
            this.lkp_Drawers2.Properties.AccessibleDescription = resources.GetString("lkp_Drawers2.Properties.AccessibleDescription");
            this.lkp_Drawers2.Properties.AccessibleName = resources.GetString("lkp_Drawers2.Properties.AccessibleName");
            this.lkp_Drawers2.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_Drawers2.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_Drawers2.Properties.Appearance.FontSizeDelta")));
            this.lkp_Drawers2.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_Drawers2.Properties.Appearance.FontStyleDelta")));
            this.lkp_Drawers2.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_Drawers2.Properties.Appearance.GradientMode")));
            this.lkp_Drawers2.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_Drawers2.Properties.Appearance.Image")));
            this.lkp_Drawers2.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_Drawers2.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_Drawers2.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_Drawers2.Properties.AutoHeight")));
            this.lkp_Drawers2.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_Drawers2.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_Drawers2.Properties.Buttons"))))});
            this.lkp_Drawers2.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Drawers2.Properties.Columns"), resources.GetString("lkp_Drawers2.Properties.Columns1"), ((int)(resources.GetObject("lkp_Drawers2.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_Drawers2.Properties.Columns3"))), resources.GetString("lkp_Drawers2.Properties.Columns4"), ((bool)(resources.GetObject("lkp_Drawers2.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_Drawers2.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Drawers2.Properties.Columns7"), resources.GetString("lkp_Drawers2.Properties.Columns8"))});
            this.lkp_Drawers2.Properties.DisplayMember = "AccountName";
            this.lkp_Drawers2.Properties.NullText = resources.GetString("lkp_Drawers2.Properties.NullText");
            this.lkp_Drawers2.Properties.NullValuePrompt = resources.GetString("lkp_Drawers2.Properties.NullValuePrompt");
            this.lkp_Drawers2.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_Drawers2.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_Drawers2.Properties.ValueMember = "AccountId";
            this.lkp_Drawers2.EditValueChanged += new System.EventHandler(this.lkp_Drawers2_EditValueChanged);
            this.lkp_Drawers2.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // txt_PayAcc1_Paid
            // 
            resources.ApplyResources(this.txt_PayAcc1_Paid, "txt_PayAcc1_Paid");
            this.txt_PayAcc1_Paid.EnterMoveNextControl = true;
            this.txt_PayAcc1_Paid.MenuManager = this.barManager1;
            this.txt_PayAcc1_Paid.Name = "txt_PayAcc1_Paid";
            this.txt_PayAcc1_Paid.Properties.AccessibleDescription = resources.GetString("txt_PayAcc1_Paid.Properties.AccessibleDescription");
            this.txt_PayAcc1_Paid.Properties.AccessibleName = resources.GetString("txt_PayAcc1_Paid.Properties.AccessibleName");
            this.txt_PayAcc1_Paid.Properties.AutoHeight = ((bool)(resources.GetObject("txt_PayAcc1_Paid.Properties.AutoHeight")));
            this.txt_PayAcc1_Paid.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txt_PayAcc1_Paid.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_PayAcc1_Paid.Properties.Mask.AutoComplete")));
            this.txt_PayAcc1_Paid.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_PayAcc1_Paid.Properties.Mask.BeepOnError")));
            this.txt_PayAcc1_Paid.Properties.Mask.EditMask = resources.GetString("txt_PayAcc1_Paid.Properties.Mask.EditMask");
            this.txt_PayAcc1_Paid.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_PayAcc1_Paid.Properties.Mask.IgnoreMaskBlank")));
            this.txt_PayAcc1_Paid.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_PayAcc1_Paid.Properties.Mask.MaskType")));
            this.txt_PayAcc1_Paid.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_PayAcc1_Paid.Properties.Mask.PlaceHolder")));
            this.txt_PayAcc1_Paid.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_PayAcc1_Paid.Properties.Mask.SaveLiteral")));
            this.txt_PayAcc1_Paid.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_PayAcc1_Paid.Properties.Mask.ShowPlaceHolders")));
            this.txt_PayAcc1_Paid.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_PayAcc1_Paid.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_PayAcc1_Paid.Properties.NullValuePrompt = resources.GetString("txt_PayAcc1_Paid.Properties.NullValuePrompt");
            this.txt_PayAcc1_Paid.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_PayAcc1_Paid.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_PayAcc1_Paid.EditValueChanged += new System.EventHandler(this.txt_PayAcc1_Paid_EditValueChanged);
            this.txt_PayAcc1_Paid.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // labelControl28
            // 
            resources.ApplyResources(this.labelControl28, "labelControl28");
            this.labelControl28.Name = "labelControl28";
            // 
            // labelControl26
            // 
            resources.ApplyResources(this.labelControl26, "labelControl26");
            this.labelControl26.Name = "labelControl26";
            // 
            // txt_PayAcc2_Paid
            // 
            resources.ApplyResources(this.txt_PayAcc2_Paid, "txt_PayAcc2_Paid");
            this.txt_PayAcc2_Paid.EnterMoveNextControl = true;
            this.txt_PayAcc2_Paid.Name = "txt_PayAcc2_Paid";
            this.txt_PayAcc2_Paid.Properties.AccessibleDescription = resources.GetString("txt_PayAcc2_Paid.Properties.AccessibleDescription");
            this.txt_PayAcc2_Paid.Properties.AccessibleName = resources.GetString("txt_PayAcc2_Paid.Properties.AccessibleName");
            this.txt_PayAcc2_Paid.Properties.AutoHeight = ((bool)(resources.GetObject("txt_PayAcc2_Paid.Properties.AutoHeight")));
            this.txt_PayAcc2_Paid.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txt_PayAcc2_Paid.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_PayAcc2_Paid.Properties.Mask.AutoComplete")));
            this.txt_PayAcc2_Paid.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_PayAcc2_Paid.Properties.Mask.BeepOnError")));
            this.txt_PayAcc2_Paid.Properties.Mask.EditMask = resources.GetString("txt_PayAcc2_Paid.Properties.Mask.EditMask");
            this.txt_PayAcc2_Paid.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_PayAcc2_Paid.Properties.Mask.IgnoreMaskBlank")));
            this.txt_PayAcc2_Paid.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_PayAcc2_Paid.Properties.Mask.MaskType")));
            this.txt_PayAcc2_Paid.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_PayAcc2_Paid.Properties.Mask.PlaceHolder")));
            this.txt_PayAcc2_Paid.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_PayAcc2_Paid.Properties.Mask.SaveLiteral")));
            this.txt_PayAcc2_Paid.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_PayAcc2_Paid.Properties.Mask.ShowPlaceHolders")));
            this.txt_PayAcc2_Paid.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_PayAcc2_Paid.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_PayAcc2_Paid.Properties.NullValuePrompt = resources.GetString("txt_PayAcc2_Paid.Properties.NullValuePrompt");
            this.txt_PayAcc2_Paid.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_PayAcc2_Paid.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_PayAcc2_Paid.EditValueChanged += new System.EventHandler(this.txt_PayAcc1_Paid_EditValueChanged);
            this.txt_PayAcc2_Paid.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // labelControl25
            // 
            resources.ApplyResources(this.labelControl25, "labelControl25");
            this.labelControl25.Name = "labelControl25";
            // 
            // labelControl5
            // 
            resources.ApplyResources(this.labelControl5, "labelControl5");
            this.labelControl5.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl5.Appearance.Font")));
            this.labelControl5.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl5.Appearance.FontSizeDelta")));
            this.labelControl5.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl5.Appearance.FontStyleDelta")));
            this.labelControl5.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl5.Appearance.GradientMode")));
            this.labelControl5.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl5.Appearance.Image")));
            this.labelControl5.Name = "labelControl5";
            // 
            // cmbPayMethod
            // 
            resources.ApplyResources(this.cmbPayMethod, "cmbPayMethod");
            this.cmbPayMethod.EnterMoveNextControl = true;
            this.cmbPayMethod.MenuManager = this.barManager1;
            this.cmbPayMethod.Name = "cmbPayMethod";
            this.cmbPayMethod.Properties.AccessibleDescription = resources.GetString("cmbPayMethod.Properties.AccessibleDescription");
            this.cmbPayMethod.Properties.AccessibleName = resources.GetString("cmbPayMethod.Properties.AccessibleName");
            this.cmbPayMethod.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.cmbPayMethod.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("cmbPayMethod.Properties.Appearance.BackColor")));
            this.cmbPayMethod.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("cmbPayMethod.Properties.Appearance.Font")));
            this.cmbPayMethod.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("cmbPayMethod.Properties.Appearance.FontSizeDelta")));
            this.cmbPayMethod.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("cmbPayMethod.Properties.Appearance.FontStyleDelta")));
            this.cmbPayMethod.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("cmbPayMethod.Properties.Appearance.ForeColor")));
            this.cmbPayMethod.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("cmbPayMethod.Properties.Appearance.GradientMode")));
            this.cmbPayMethod.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("cmbPayMethod.Properties.Appearance.Image")));
            this.cmbPayMethod.Properties.Appearance.Options.UseBackColor = true;
            this.cmbPayMethod.Properties.Appearance.Options.UseFont = true;
            this.cmbPayMethod.Properties.Appearance.Options.UseForeColor = true;
            this.cmbPayMethod.Properties.Appearance.Options.UseTextOptions = true;
            this.cmbPayMethod.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.cmbPayMethod.Properties.AppearanceDisabled.BackColor = ((System.Drawing.Color)(resources.GetObject("cmbPayMethod.Properties.AppearanceDisabled.BackColor")));
            this.cmbPayMethod.Properties.AppearanceDisabled.Font = ((System.Drawing.Font)(resources.GetObject("cmbPayMethod.Properties.AppearanceDisabled.Font")));
            this.cmbPayMethod.Properties.AppearanceDisabled.FontSizeDelta = ((int)(resources.GetObject("cmbPayMethod.Properties.AppearanceDisabled.FontSizeDelta")));
            this.cmbPayMethod.Properties.AppearanceDisabled.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("cmbPayMethod.Properties.AppearanceDisabled.FontStyleDelta")));
            this.cmbPayMethod.Properties.AppearanceDisabled.ForeColor = ((System.Drawing.Color)(resources.GetObject("cmbPayMethod.Properties.AppearanceDisabled.ForeColor")));
            this.cmbPayMethod.Properties.AppearanceDisabled.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("cmbPayMethod.Properties.AppearanceDisabled.GradientMode")));
            this.cmbPayMethod.Properties.AppearanceDisabled.Image = ((System.Drawing.Image)(resources.GetObject("cmbPayMethod.Properties.AppearanceDisabled.Image")));
            this.cmbPayMethod.Properties.AppearanceDisabled.Options.UseBackColor = true;
            this.cmbPayMethod.Properties.AppearanceDisabled.Options.UseFont = true;
            this.cmbPayMethod.Properties.AppearanceDisabled.Options.UseForeColor = true;
            this.cmbPayMethod.Properties.AutoHeight = ((bool)(resources.GetObject("cmbPayMethod.Properties.AutoHeight")));
            this.cmbPayMethod.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.cmbPayMethod.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("cmbPayMethod.Properties.Buttons"))))});
            this.cmbPayMethod.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("cmbPayMethod.Properties.GlyphAlignment")));
            this.cmbPayMethod.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbPayMethod.Properties.Items"), ((object)(resources.GetObject("cmbPayMethod.Properties.Items1"))), ((int)(resources.GetObject("cmbPayMethod.Properties.Items2")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbPayMethod.Properties.Items3"), ((object)(resources.GetObject("cmbPayMethod.Properties.Items4"))), ((int)(resources.GetObject("cmbPayMethod.Properties.Items5")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbPayMethod.Properties.Items6"), ((object)(resources.GetObject("cmbPayMethod.Properties.Items7"))), ((int)(resources.GetObject("cmbPayMethod.Properties.Items8"))))});
            this.cmbPayMethod.Properties.NullValuePrompt = resources.GetString("cmbPayMethod.Properties.NullValuePrompt");
            this.cmbPayMethod.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("cmbPayMethod.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl1
            // 
            resources.ApplyResources(this.labelControl1, "labelControl1");
            this.labelControl1.Name = "labelControl1";
            // 
            // labelControl3
            // 
            resources.ApplyResources(this.labelControl3, "labelControl3");
            this.labelControl3.Name = "labelControl3";
            // 
            // labelControl4
            // 
            resources.ApplyResources(this.labelControl4, "labelControl4");
            this.labelControl4.Name = "labelControl4";
            // 
            // labelControl8
            // 
            resources.ApplyResources(this.labelControl8, "labelControl8");
            this.labelControl8.Name = "labelControl8";
            // 
            // labelControl10
            // 
            resources.ApplyResources(this.labelControl10, "labelControl10");
            this.labelControl10.Name = "labelControl10";
            // 
            // labelControl12
            // 
            resources.ApplyResources(this.labelControl12, "labelControl12");
            this.labelControl12.Name = "labelControl12";
            // 
            // labelControl14
            // 
            resources.ApplyResources(this.labelControl14, "labelControl14");
            this.labelControl14.Name = "labelControl14";
            // 
            // labelControl15
            // 
            resources.ApplyResources(this.labelControl15, "labelControl15");
            this.labelControl15.Name = "labelControl15";
            // 
            // labelControl16
            // 
            resources.ApplyResources(this.labelControl16, "labelControl16");
            this.labelControl16.Name = "labelControl16";
            // 
            // txtDiscountRatio
            // 
            resources.ApplyResources(this.txtDiscountRatio, "txtDiscountRatio");
            this.txtDiscountRatio.EnterMoveNextControl = true;
            this.txtDiscountRatio.Name = "txtDiscountRatio";
            this.txtDiscountRatio.Properties.AccessibleDescription = resources.GetString("txtDiscountRatio.Properties.AccessibleDescription");
            this.txtDiscountRatio.Properties.AccessibleName = resources.GetString("txtDiscountRatio.Properties.AccessibleName");
            this.txtDiscountRatio.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtDiscountRatio.Properties.Appearance.FontSizeDelta")));
            this.txtDiscountRatio.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtDiscountRatio.Properties.Appearance.FontStyleDelta")));
            this.txtDiscountRatio.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtDiscountRatio.Properties.Appearance.GradientMode")));
            this.txtDiscountRatio.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtDiscountRatio.Properties.Appearance.Image")));
            this.txtDiscountRatio.Properties.Appearance.Options.UseTextOptions = true;
            this.txtDiscountRatio.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtDiscountRatio.Properties.AutoHeight = ((bool)(resources.GetObject("txtDiscountRatio.Properties.AutoHeight")));
            this.txtDiscountRatio.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtDiscountRatio.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtDiscountRatio.Properties.Mask.AutoComplete")));
            this.txtDiscountRatio.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtDiscountRatio.Properties.Mask.BeepOnError")));
            this.txtDiscountRatio.Properties.Mask.EditMask = resources.GetString("txtDiscountRatio.Properties.Mask.EditMask");
            this.txtDiscountRatio.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtDiscountRatio.Properties.Mask.IgnoreMaskBlank")));
            this.txtDiscountRatio.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtDiscountRatio.Properties.Mask.MaskType")));
            this.txtDiscountRatio.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtDiscountRatio.Properties.Mask.PlaceHolder")));
            this.txtDiscountRatio.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtDiscountRatio.Properties.Mask.SaveLiteral")));
            this.txtDiscountRatio.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtDiscountRatio.Properties.Mask.ShowPlaceHolders")));
            this.txtDiscountRatio.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtDiscountRatio.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtDiscountRatio.Properties.NullValuePrompt = resources.GetString("txtDiscountRatio.Properties.NullValuePrompt");
            this.txtDiscountRatio.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtDiscountRatio.Properties.NullValuePromptShowForEmptyValue")));
            this.txtDiscountRatio.EditValueChanged += new System.EventHandler(this.txtDiscountRatio_EditValueChanged);
            this.txtDiscountRatio.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txtDiscountRatio.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress);
            // 
            // txtDiscountValue
            // 
            resources.ApplyResources(this.txtDiscountValue, "txtDiscountValue");
            this.txtDiscountValue.EnterMoveNextControl = true;
            this.txtDiscountValue.Name = "txtDiscountValue";
            this.txtDiscountValue.Properties.AccessibleDescription = resources.GetString("txtDiscountValue.Properties.AccessibleDescription");
            this.txtDiscountValue.Properties.AccessibleName = resources.GetString("txtDiscountValue.Properties.AccessibleName");
            this.txtDiscountValue.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtDiscountValue.Properties.Appearance.FontSizeDelta")));
            this.txtDiscountValue.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtDiscountValue.Properties.Appearance.FontStyleDelta")));
            this.txtDiscountValue.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtDiscountValue.Properties.Appearance.GradientMode")));
            this.txtDiscountValue.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtDiscountValue.Properties.Appearance.Image")));
            this.txtDiscountValue.Properties.Appearance.Options.UseTextOptions = true;
            this.txtDiscountValue.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtDiscountValue.Properties.AutoHeight = ((bool)(resources.GetObject("txtDiscountValue.Properties.AutoHeight")));
            this.txtDiscountValue.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtDiscountValue.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtDiscountValue.Properties.Mask.AutoComplete")));
            this.txtDiscountValue.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtDiscountValue.Properties.Mask.BeepOnError")));
            this.txtDiscountValue.Properties.Mask.EditMask = resources.GetString("txtDiscountValue.Properties.Mask.EditMask");
            this.txtDiscountValue.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtDiscountValue.Properties.Mask.IgnoreMaskBlank")));
            this.txtDiscountValue.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtDiscountValue.Properties.Mask.MaskType")));
            this.txtDiscountValue.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtDiscountValue.Properties.Mask.PlaceHolder")));
            this.txtDiscountValue.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtDiscountValue.Properties.Mask.SaveLiteral")));
            this.txtDiscountValue.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtDiscountValue.Properties.Mask.ShowPlaceHolders")));
            this.txtDiscountValue.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtDiscountValue.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtDiscountValue.Properties.NullValuePrompt = resources.GetString("txtDiscountValue.Properties.NullValuePrompt");
            this.txtDiscountValue.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtDiscountValue.Properties.NullValuePromptShowForEmptyValue")));
            this.txtDiscountValue.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txtDiscountValue.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress);
            this.txtDiscountValue.Leave += new System.EventHandler(this.txtDiscountValue_Leave);
            // 
            // txt_TaxValue
            // 
            resources.ApplyResources(this.txt_TaxValue, "txt_TaxValue");
            this.txt_TaxValue.EnterMoveNextControl = true;
            this.txt_TaxValue.Name = "txt_TaxValue";
            this.txt_TaxValue.Properties.AccessibleDescription = resources.GetString("txt_TaxValue.Properties.AccessibleDescription");
            this.txt_TaxValue.Properties.AccessibleName = resources.GetString("txt_TaxValue.Properties.AccessibleName");
            this.txt_TaxValue.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txt_TaxValue.Properties.Appearance.FontSizeDelta")));
            this.txt_TaxValue.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txt_TaxValue.Properties.Appearance.FontStyleDelta")));
            this.txt_TaxValue.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txt_TaxValue.Properties.Appearance.GradientMode")));
            this.txt_TaxValue.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txt_TaxValue.Properties.Appearance.Image")));
            this.txt_TaxValue.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_TaxValue.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_TaxValue.Properties.AutoHeight = ((bool)(resources.GetObject("txt_TaxValue.Properties.AutoHeight")));
            this.txt_TaxValue.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_TaxValue.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_TaxValue.Properties.Mask.AutoComplete")));
            this.txt_TaxValue.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_TaxValue.Properties.Mask.BeepOnError")));
            this.txt_TaxValue.Properties.Mask.EditMask = resources.GetString("txt_TaxValue.Properties.Mask.EditMask");
            this.txt_TaxValue.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_TaxValue.Properties.Mask.IgnoreMaskBlank")));
            this.txt_TaxValue.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_TaxValue.Properties.Mask.MaskType")));
            this.txt_TaxValue.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_TaxValue.Properties.Mask.PlaceHolder")));
            this.txt_TaxValue.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_TaxValue.Properties.Mask.SaveLiteral")));
            this.txt_TaxValue.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_TaxValue.Properties.Mask.ShowPlaceHolders")));
            this.txt_TaxValue.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_TaxValue.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_TaxValue.Properties.NullValuePrompt = resources.GetString("txt_TaxValue.Properties.NullValuePrompt");
            this.txt_TaxValue.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_TaxValue.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_TaxValue.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txt_TaxValue.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress);
            // 
            // txt_DeductTaxR
            // 
            resources.ApplyResources(this.txt_DeductTaxR, "txt_DeductTaxR");
            this.txt_DeductTaxR.EnterMoveNextControl = true;
            this.txt_DeductTaxR.Name = "txt_DeductTaxR";
            this.txt_DeductTaxR.Properties.AccessibleDescription = resources.GetString("txt_DeductTaxR.Properties.AccessibleDescription");
            this.txt_DeductTaxR.Properties.AccessibleName = resources.GetString("txt_DeductTaxR.Properties.AccessibleName");
            this.txt_DeductTaxR.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txt_DeductTaxR.Properties.Appearance.FontSizeDelta")));
            this.txt_DeductTaxR.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txt_DeductTaxR.Properties.Appearance.FontStyleDelta")));
            this.txt_DeductTaxR.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txt_DeductTaxR.Properties.Appearance.GradientMode")));
            this.txt_DeductTaxR.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txt_DeductTaxR.Properties.Appearance.Image")));
            this.txt_DeductTaxR.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_DeductTaxR.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_DeductTaxR.Properties.AutoHeight = ((bool)(resources.GetObject("txt_DeductTaxR.Properties.AutoHeight")));
            this.txt_DeductTaxR.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_DeductTaxR.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_DeductTaxR.Properties.Mask.AutoComplete")));
            this.txt_DeductTaxR.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_DeductTaxR.Properties.Mask.BeepOnError")));
            this.txt_DeductTaxR.Properties.Mask.EditMask = resources.GetString("txt_DeductTaxR.Properties.Mask.EditMask");
            this.txt_DeductTaxR.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_DeductTaxR.Properties.Mask.IgnoreMaskBlank")));
            this.txt_DeductTaxR.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_DeductTaxR.Properties.Mask.MaskType")));
            this.txt_DeductTaxR.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_DeductTaxR.Properties.Mask.PlaceHolder")));
            this.txt_DeductTaxR.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_DeductTaxR.Properties.Mask.SaveLiteral")));
            this.txt_DeductTaxR.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_DeductTaxR.Properties.Mask.ShowPlaceHolders")));
            this.txt_DeductTaxR.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_DeductTaxR.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_DeductTaxR.Properties.NullValuePrompt = resources.GetString("txt_DeductTaxR.Properties.NullValuePrompt");
            this.txt_DeductTaxR.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_DeductTaxR.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_DeductTaxR.EditValueChanged += new System.EventHandler(this.txtDiscountRatio_EditValueChanged);
            this.txt_DeductTaxR.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txt_DeductTaxR.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress);
            // 
            // txt_DeductTaxV
            // 
            resources.ApplyResources(this.txt_DeductTaxV, "txt_DeductTaxV");
            this.txt_DeductTaxV.EnterMoveNextControl = true;
            this.txt_DeductTaxV.Name = "txt_DeductTaxV";
            this.txt_DeductTaxV.Properties.AccessibleDescription = resources.GetString("txt_DeductTaxV.Properties.AccessibleDescription");
            this.txt_DeductTaxV.Properties.AccessibleName = resources.GetString("txt_DeductTaxV.Properties.AccessibleName");
            this.txt_DeductTaxV.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txt_DeductTaxV.Properties.Appearance.FontSizeDelta")));
            this.txt_DeductTaxV.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txt_DeductTaxV.Properties.Appearance.FontStyleDelta")));
            this.txt_DeductTaxV.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txt_DeductTaxV.Properties.Appearance.GradientMode")));
            this.txt_DeductTaxV.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txt_DeductTaxV.Properties.Appearance.Image")));
            this.txt_DeductTaxV.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_DeductTaxV.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_DeductTaxV.Properties.AutoHeight = ((bool)(resources.GetObject("txt_DeductTaxV.Properties.AutoHeight")));
            this.txt_DeductTaxV.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_DeductTaxV.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_DeductTaxV.Properties.Mask.AutoComplete")));
            this.txt_DeductTaxV.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_DeductTaxV.Properties.Mask.BeepOnError")));
            this.txt_DeductTaxV.Properties.Mask.EditMask = resources.GetString("txt_DeductTaxV.Properties.Mask.EditMask");
            this.txt_DeductTaxV.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_DeductTaxV.Properties.Mask.IgnoreMaskBlank")));
            this.txt_DeductTaxV.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_DeductTaxV.Properties.Mask.MaskType")));
            this.txt_DeductTaxV.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_DeductTaxV.Properties.Mask.PlaceHolder")));
            this.txt_DeductTaxV.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_DeductTaxV.Properties.Mask.SaveLiteral")));
            this.txt_DeductTaxV.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_DeductTaxV.Properties.Mask.ShowPlaceHolders")));
            this.txt_DeductTaxV.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_DeductTaxV.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_DeductTaxV.Properties.NullValuePrompt = resources.GetString("txt_DeductTaxV.Properties.NullValuePrompt");
            this.txt_DeductTaxV.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_DeductTaxV.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_DeductTaxV.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txt_DeductTaxV.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress);
            this.txt_DeductTaxV.Leave += new System.EventHandler(this.txtDiscountValue_Leave);
            // 
            // txt_AddTaxR
            // 
            resources.ApplyResources(this.txt_AddTaxR, "txt_AddTaxR");
            this.txt_AddTaxR.EnterMoveNextControl = true;
            this.txt_AddTaxR.Name = "txt_AddTaxR";
            this.txt_AddTaxR.Properties.AccessibleDescription = resources.GetString("txt_AddTaxR.Properties.AccessibleDescription");
            this.txt_AddTaxR.Properties.AccessibleName = resources.GetString("txt_AddTaxR.Properties.AccessibleName");
            this.txt_AddTaxR.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txt_AddTaxR.Properties.Appearance.FontSizeDelta")));
            this.txt_AddTaxR.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txt_AddTaxR.Properties.Appearance.FontStyleDelta")));
            this.txt_AddTaxR.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txt_AddTaxR.Properties.Appearance.GradientMode")));
            this.txt_AddTaxR.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txt_AddTaxR.Properties.Appearance.Image")));
            this.txt_AddTaxR.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_AddTaxR.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_AddTaxR.Properties.AutoHeight = ((bool)(resources.GetObject("txt_AddTaxR.Properties.AutoHeight")));
            this.txt_AddTaxR.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_AddTaxR.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_AddTaxR.Properties.Mask.AutoComplete")));
            this.txt_AddTaxR.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_AddTaxR.Properties.Mask.BeepOnError")));
            this.txt_AddTaxR.Properties.Mask.EditMask = resources.GetString("txt_AddTaxR.Properties.Mask.EditMask");
            this.txt_AddTaxR.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_AddTaxR.Properties.Mask.IgnoreMaskBlank")));
            this.txt_AddTaxR.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_AddTaxR.Properties.Mask.MaskType")));
            this.txt_AddTaxR.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_AddTaxR.Properties.Mask.PlaceHolder")));
            this.txt_AddTaxR.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_AddTaxR.Properties.Mask.SaveLiteral")));
            this.txt_AddTaxR.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_AddTaxR.Properties.Mask.ShowPlaceHolders")));
            this.txt_AddTaxR.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_AddTaxR.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_AddTaxR.Properties.NullValuePrompt = resources.GetString("txt_AddTaxR.Properties.NullValuePrompt");
            this.txt_AddTaxR.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_AddTaxR.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_AddTaxR.EditValueChanged += new System.EventHandler(this.txtDiscountRatio_EditValueChanged);
            this.txt_AddTaxR.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txt_AddTaxR.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress);
            // 
            // txt_AddTaxV
            // 
            resources.ApplyResources(this.txt_AddTaxV, "txt_AddTaxV");
            this.txt_AddTaxV.EnterMoveNextControl = true;
            this.txt_AddTaxV.Name = "txt_AddTaxV";
            this.txt_AddTaxV.Properties.AccessibleDescription = resources.GetString("txt_AddTaxV.Properties.AccessibleDescription");
            this.txt_AddTaxV.Properties.AccessibleName = resources.GetString("txt_AddTaxV.Properties.AccessibleName");
            this.txt_AddTaxV.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txt_AddTaxV.Properties.Appearance.FontSizeDelta")));
            this.txt_AddTaxV.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txt_AddTaxV.Properties.Appearance.FontStyleDelta")));
            this.txt_AddTaxV.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txt_AddTaxV.Properties.Appearance.GradientMode")));
            this.txt_AddTaxV.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txt_AddTaxV.Properties.Appearance.Image")));
            this.txt_AddTaxV.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_AddTaxV.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_AddTaxV.Properties.AutoHeight = ((bool)(resources.GetObject("txt_AddTaxV.Properties.AutoHeight")));
            this.txt_AddTaxV.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_AddTaxV.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_AddTaxV.Properties.Mask.AutoComplete")));
            this.txt_AddTaxV.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_AddTaxV.Properties.Mask.BeepOnError")));
            this.txt_AddTaxV.Properties.Mask.EditMask = resources.GetString("txt_AddTaxV.Properties.Mask.EditMask");
            this.txt_AddTaxV.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_AddTaxV.Properties.Mask.IgnoreMaskBlank")));
            this.txt_AddTaxV.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_AddTaxV.Properties.Mask.MaskType")));
            this.txt_AddTaxV.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_AddTaxV.Properties.Mask.PlaceHolder")));
            this.txt_AddTaxV.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_AddTaxV.Properties.Mask.SaveLiteral")));
            this.txt_AddTaxV.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_AddTaxV.Properties.Mask.ShowPlaceHolders")));
            this.txt_AddTaxV.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_AddTaxV.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_AddTaxV.Properties.NullValuePrompt = resources.GetString("txt_AddTaxV.Properties.NullValuePrompt");
            this.txt_AddTaxV.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_AddTaxV.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_AddTaxV.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txt_AddTaxV.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress);
            this.txt_AddTaxV.Leave += new System.EventHandler(this.txtDiscountValue_Leave);
            // 
            // labelControl27
            // 
            resources.ApplyResources(this.labelControl27, "labelControl27");
            this.labelControl27.Name = "labelControl27";
            // 
            // labelControl23
            // 
            resources.ApplyResources(this.labelControl23, "labelControl23");
            this.labelControl23.Name = "labelControl23";
            // 
            // txt_CusTaxV
            // 
            resources.ApplyResources(this.txt_CusTaxV, "txt_CusTaxV");
            this.txt_CusTaxV.EnterMoveNextControl = true;
            this.txt_CusTaxV.Name = "txt_CusTaxV";
            this.txt_CusTaxV.Properties.AccessibleDescription = resources.GetString("txt_CusTaxV.Properties.AccessibleDescription");
            this.txt_CusTaxV.Properties.AccessibleName = resources.GetString("txt_CusTaxV.Properties.AccessibleName");
            this.txt_CusTaxV.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txt_CusTaxV.Properties.Appearance.FontSizeDelta")));
            this.txt_CusTaxV.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txt_CusTaxV.Properties.Appearance.FontStyleDelta")));
            this.txt_CusTaxV.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txt_CusTaxV.Properties.Appearance.GradientMode")));
            this.txt_CusTaxV.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txt_CusTaxV.Properties.Appearance.Image")));
            this.txt_CusTaxV.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_CusTaxV.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_CusTaxV.Properties.AutoHeight = ((bool)(resources.GetObject("txt_CusTaxV.Properties.AutoHeight")));
            this.txt_CusTaxV.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_CusTaxV.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_CusTaxV.Properties.Mask.AutoComplete")));
            this.txt_CusTaxV.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_CusTaxV.Properties.Mask.BeepOnError")));
            this.txt_CusTaxV.Properties.Mask.EditMask = resources.GetString("txt_CusTaxV.Properties.Mask.EditMask");
            this.txt_CusTaxV.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_CusTaxV.Properties.Mask.IgnoreMaskBlank")));
            this.txt_CusTaxV.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_CusTaxV.Properties.Mask.MaskType")));
            this.txt_CusTaxV.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_CusTaxV.Properties.Mask.PlaceHolder")));
            this.txt_CusTaxV.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_CusTaxV.Properties.Mask.SaveLiteral")));
            this.txt_CusTaxV.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_CusTaxV.Properties.Mask.ShowPlaceHolders")));
            this.txt_CusTaxV.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_CusTaxV.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_CusTaxV.Properties.NullValuePrompt = resources.GetString("txt_CusTaxV.Properties.NullValuePrompt");
            this.txt_CusTaxV.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_CusTaxV.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // chk_IsInTrns
            // 
            resources.ApplyResources(this.chk_IsInTrns, "chk_IsInTrns");
            this.chk_IsInTrns.Name = "chk_IsInTrns";
            this.chk_IsInTrns.Properties.AccessibleDescription = resources.GetString("chk_IsInTrns.Properties.AccessibleDescription");
            this.chk_IsInTrns.Properties.AccessibleName = resources.GetString("chk_IsInTrns.Properties.AccessibleName");
            this.chk_IsInTrns.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("chk_IsInTrns.Properties.Appearance.FontSizeDelta")));
            this.chk_IsInTrns.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("chk_IsInTrns.Properties.Appearance.FontStyleDelta")));
            this.chk_IsInTrns.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("chk_IsInTrns.Properties.Appearance.GradientMode")));
            this.chk_IsInTrns.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("chk_IsInTrns.Properties.Appearance.Image")));
            this.chk_IsInTrns.Properties.Appearance.Options.UseTextOptions = true;
            this.chk_IsInTrns.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chk_IsInTrns.Properties.AppearanceReadOnly.BackColor = ((System.Drawing.Color)(resources.GetObject("chk_IsInTrns.Properties.AppearanceReadOnly.BackColor")));
            this.chk_IsInTrns.Properties.AppearanceReadOnly.FontSizeDelta = ((int)(resources.GetObject("chk_IsInTrns.Properties.AppearanceReadOnly.FontSizeDelta")));
            this.chk_IsInTrns.Properties.AppearanceReadOnly.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("chk_IsInTrns.Properties.AppearanceReadOnly.FontStyleDelta")));
            this.chk_IsInTrns.Properties.AppearanceReadOnly.ForeColor = ((System.Drawing.Color)(resources.GetObject("chk_IsInTrns.Properties.AppearanceReadOnly.ForeColor")));
            this.chk_IsInTrns.Properties.AppearanceReadOnly.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("chk_IsInTrns.Properties.AppearanceReadOnly.GradientMode")));
            this.chk_IsInTrns.Properties.AppearanceReadOnly.Image = ((System.Drawing.Image)(resources.GetObject("chk_IsInTrns.Properties.AppearanceReadOnly.Image")));
            this.chk_IsInTrns.Properties.AppearanceReadOnly.Options.UseBackColor = true;
            this.chk_IsInTrns.Properties.AppearanceReadOnly.Options.UseForeColor = true;
            this.chk_IsInTrns.Properties.AutoHeight = ((bool)(resources.GetObject("chk_IsInTrns.Properties.AutoHeight")));
            this.chk_IsInTrns.Properties.Caption = resources.GetString("chk_IsInTrns.Properties.Caption");
            this.chk_IsInTrns.Properties.DisplayValueChecked = resources.GetString("chk_IsInTrns.Properties.DisplayValueChecked");
            this.chk_IsInTrns.Properties.DisplayValueGrayed = resources.GetString("chk_IsInTrns.Properties.DisplayValueGrayed");
            this.chk_IsInTrns.Properties.DisplayValueUnchecked = resources.GetString("chk_IsInTrns.Properties.DisplayValueUnchecked");
            this.chk_IsInTrns.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("chk_IsInTrns.Properties.GlyphAlignment")));
            this.chk_IsInTrns.Properties.ReadOnly = true;
            this.chk_IsInTrns.TabStop = false;
            // 
            // labelControl29
            // 
            resources.ApplyResources(this.labelControl29, "labelControl29");
            this.labelControl29.Name = "labelControl29";
            // 
            // txt_EtaxValue
            // 
            resources.ApplyResources(this.txt_EtaxValue, "txt_EtaxValue");
            this.txt_EtaxValue.EnterMoveNextControl = true;
            this.txt_EtaxValue.Name = "txt_EtaxValue";
            this.txt_EtaxValue.Properties.AccessibleDescription = resources.GetString("txt_EtaxValue.Properties.AccessibleDescription");
            this.txt_EtaxValue.Properties.AccessibleName = resources.GetString("txt_EtaxValue.Properties.AccessibleName");
            this.txt_EtaxValue.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txt_EtaxValue.Properties.Appearance.FontSizeDelta")));
            this.txt_EtaxValue.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txt_EtaxValue.Properties.Appearance.FontStyleDelta")));
            this.txt_EtaxValue.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txt_EtaxValue.Properties.Appearance.GradientMode")));
            this.txt_EtaxValue.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txt_EtaxValue.Properties.Appearance.Image")));
            this.txt_EtaxValue.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_EtaxValue.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_EtaxValue.Properties.AutoHeight = ((bool)(resources.GetObject("txt_EtaxValue.Properties.AutoHeight")));
            this.txt_EtaxValue.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_EtaxValue.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_EtaxValue.Properties.Mask.AutoComplete")));
            this.txt_EtaxValue.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_EtaxValue.Properties.Mask.BeepOnError")));
            this.txt_EtaxValue.Properties.Mask.EditMask = resources.GetString("txt_EtaxValue.Properties.Mask.EditMask");
            this.txt_EtaxValue.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_EtaxValue.Properties.Mask.IgnoreMaskBlank")));
            this.txt_EtaxValue.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_EtaxValue.Properties.Mask.MaskType")));
            this.txt_EtaxValue.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_EtaxValue.Properties.Mask.PlaceHolder")));
            this.txt_EtaxValue.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_EtaxValue.Properties.Mask.SaveLiteral")));
            this.txt_EtaxValue.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_EtaxValue.Properties.Mask.ShowPlaceHolders")));
            this.txt_EtaxValue.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_EtaxValue.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_EtaxValue.Properties.NullValuePrompt = resources.GetString("txt_EtaxValue.Properties.NullValuePrompt");
            this.txt_EtaxValue.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_EtaxValue.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // grd_SubTaxes
            // 
            resources.ApplyResources(this.grd_SubTaxes, "grd_SubTaxes");
            this.grd_SubTaxes.EmbeddedNavigator.AccessibleDescription = resources.GetString("grd_SubTaxes.EmbeddedNavigator.AccessibleDescription");
            this.grd_SubTaxes.EmbeddedNavigator.AccessibleName = resources.GetString("grd_SubTaxes.EmbeddedNavigator.AccessibleName");
            this.grd_SubTaxes.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grd_SubTaxes.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grd_SubTaxes.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grd_SubTaxes.EmbeddedNavigator.Anchor")));
            this.grd_SubTaxes.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grd_SubTaxes.EmbeddedNavigator.BackgroundImage")));
            this.grd_SubTaxes.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grd_SubTaxes.EmbeddedNavigator.BackgroundImageLayout")));
            this.grd_SubTaxes.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grd_SubTaxes.EmbeddedNavigator.ImeMode")));
            this.grd_SubTaxes.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grd_SubTaxes.EmbeddedNavigator.MaximumSize")));
            this.grd_SubTaxes.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grd_SubTaxes.EmbeddedNavigator.TextLocation")));
            this.grd_SubTaxes.EmbeddedNavigator.ToolTip = resources.GetString("grd_SubTaxes.EmbeddedNavigator.ToolTip");
            this.grd_SubTaxes.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grd_SubTaxes.EmbeddedNavigator.ToolTipIconType")));
            this.grd_SubTaxes.EmbeddedNavigator.ToolTipTitle = resources.GetString("grd_SubTaxes.EmbeddedNavigator.ToolTipTitle");
            this.grd_SubTaxes.MainView = this.gv_SubTaxes;
            this.grd_SubTaxes.Name = "grd_SubTaxes";
            this.grd_SubTaxes.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.lkp_SubTaxes});
            this.grd_SubTaxes.TabStop = false;
            this.grd_SubTaxes.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gv_SubTaxes});
            // 
            // gv_SubTaxes
            // 
            this.gv_SubTaxes.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gv_SubTaxes.Appearance.HeaderPanel.FontSizeDelta")));
            this.gv_SubTaxes.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gv_SubTaxes.Appearance.HeaderPanel.FontStyleDelta")));
            this.gv_SubTaxes.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gv_SubTaxes.Appearance.HeaderPanel.GradientMode")));
            this.gv_SubTaxes.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gv_SubTaxes.Appearance.HeaderPanel.Image")));
            this.gv_SubTaxes.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gv_SubTaxes.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gv_SubTaxes.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gv_SubTaxes.Appearance.Row.FontSizeDelta")));
            this.gv_SubTaxes.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gv_SubTaxes.Appearance.Row.FontStyleDelta")));
            this.gv_SubTaxes.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gv_SubTaxes.Appearance.Row.GradientMode")));
            this.gv_SubTaxes.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gv_SubTaxes.Appearance.Row.Image")));
            this.gv_SubTaxes.Appearance.Row.Options.UseTextOptions = true;
            this.gv_SubTaxes.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.gv_SubTaxes, "gv_SubTaxes");
            this.gv_SubTaxes.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.Value,
            this.SubTaxId,
            this.col_Rate});
            this.gv_SubTaxes.GridControl = this.grd_SubTaxes;
            this.gv_SubTaxes.HorzScrollStep = 2;
            this.gv_SubTaxes.Name = "gv_SubTaxes";
            this.gv_SubTaxes.OptionsBehavior.Editable = false;
            this.gv_SubTaxes.OptionsBehavior.ReadOnly = true;
            this.gv_SubTaxes.OptionsCustomization.AllowFilter = false;
            this.gv_SubTaxes.OptionsCustomization.AllowGroup = false;
            this.gv_SubTaxes.OptionsCustomization.AllowQuickHideColumns = false;
            this.gv_SubTaxes.OptionsMenu.EnableColumnMenu = false;
            this.gv_SubTaxes.OptionsMenu.EnableFooterMenu = false;
            this.gv_SubTaxes.OptionsMenu.EnableGroupPanelMenu = false;
            this.gv_SubTaxes.OptionsMenu.ShowDateTimeGroupIntervalItems = false;
            this.gv_SubTaxes.OptionsMenu.ShowGroupSortSummaryItems = false;
            this.gv_SubTaxes.OptionsNavigation.EnterMoveNextColumn = true;
            this.gv_SubTaxes.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gv_SubTaxes.OptionsView.AnimationType = DevExpress.XtraGrid.Views.Base.GridAnimationType.NeverAnimate;
            this.gv_SubTaxes.OptionsView.EnableAppearanceEvenRow = true;
            this.gv_SubTaxes.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Bottom;
            this.gv_SubTaxes.OptionsView.ShowDetailButtons = false;
            this.gv_SubTaxes.OptionsView.ShowGroupPanel = false;
            // 
            // Value
            // 
            resources.ApplyResources(this.Value, "Value");
            this.Value.FieldName = "Value";
            this.Value.Name = "Value";
            // 
            // SubTaxId
            // 
            this.SubTaxId.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("SubTaxId.AppearanceHeader.FontSizeDelta")));
            this.SubTaxId.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("SubTaxId.AppearanceHeader.FontStyleDelta")));
            this.SubTaxId.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("SubTaxId.AppearanceHeader.GradientMode")));
            this.SubTaxId.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("SubTaxId.AppearanceHeader.Image")));
            this.SubTaxId.AppearanceHeader.Options.UseTextOptions = true;
            this.SubTaxId.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.SubTaxId, "SubTaxId");
            this.SubTaxId.ColumnEdit = this.lkp_SubTaxes;
            this.SubTaxId.FieldName = "SubTaxId";
            this.SubTaxId.Name = "SubTaxId";
            // 
            // lkp_SubTaxes
            // 
            resources.ApplyResources(this.lkp_SubTaxes, "lkp_SubTaxes");
            this.lkp_SubTaxes.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_SubTaxes.Appearance.FontSizeDelta")));
            this.lkp_SubTaxes.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_SubTaxes.Appearance.FontStyleDelta")));
            this.lkp_SubTaxes.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_SubTaxes.Appearance.GradientMode")));
            this.lkp_SubTaxes.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_SubTaxes.Appearance.Image")));
            this.lkp_SubTaxes.Appearance.Options.UseTextOptions = true;
            this.lkp_SubTaxes.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkp_SubTaxes.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.lkp_SubTaxes.AppearanceDisabled.FontSizeDelta = ((int)(resources.GetObject("lkp_SubTaxes.AppearanceDisabled.FontSizeDelta")));
            this.lkp_SubTaxes.AppearanceDisabled.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_SubTaxes.AppearanceDisabled.FontStyleDelta")));
            this.lkp_SubTaxes.AppearanceDisabled.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_SubTaxes.AppearanceDisabled.GradientMode")));
            this.lkp_SubTaxes.AppearanceDisabled.Image = ((System.Drawing.Image)(resources.GetObject("lkp_SubTaxes.AppearanceDisabled.Image")));
            this.lkp_SubTaxes.AppearanceDisabled.Options.UseTextOptions = true;
            this.lkp_SubTaxes.AppearanceDisabled.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkp_SubTaxes.AppearanceDisabled.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.lkp_SubTaxes.AppearanceDisabled.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.lkp_SubTaxes.AppearanceDropDownHeader.FontSizeDelta = ((int)(resources.GetObject("lkp_SubTaxes.AppearanceDropDownHeader.FontSizeDelta")));
            this.lkp_SubTaxes.AppearanceDropDownHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_SubTaxes.AppearanceDropDownHeader.FontStyleDelta")));
            this.lkp_SubTaxes.AppearanceDropDownHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_SubTaxes.AppearanceDropDownHeader.GradientMode")));
            this.lkp_SubTaxes.AppearanceDropDownHeader.Image = ((System.Drawing.Image)(resources.GetObject("lkp_SubTaxes.AppearanceDropDownHeader.Image")));
            this.lkp_SubTaxes.AppearanceDropDownHeader.Options.UseTextOptions = true;
            this.lkp_SubTaxes.AppearanceDropDownHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkp_SubTaxes.AppearanceDropDownHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.lkp_SubTaxes.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_SubTaxes.Buttons"))))});
            this.lkp_SubTaxes.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_SubTaxes.Columns"), resources.GetString("lkp_SubTaxes.Columns1")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_SubTaxes.Columns2"), resources.GetString("lkp_SubTaxes.Columns3")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_SubTaxes.Columns4"), resources.GetString("lkp_SubTaxes.Columns5"), ((int)(resources.GetObject("lkp_SubTaxes.Columns6"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_SubTaxes.Columns7"))), resources.GetString("lkp_SubTaxes.Columns8"), ((bool)(resources.GetObject("lkp_SubTaxes.Columns9"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_SubTaxes.Columns10"))))});
            this.lkp_SubTaxes.Name = "lkp_SubTaxes";
            // 
            // col_Rate
            // 
            this.col_Rate.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_Rate.AppearanceHeader.FontSizeDelta")));
            this.col_Rate.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_Rate.AppearanceHeader.FontStyleDelta")));
            this.col_Rate.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_Rate.AppearanceHeader.GradientMode")));
            this.col_Rate.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_Rate.AppearanceHeader.Image")));
            this.col_Rate.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Rate.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.col_Rate, "col_Rate");
            this.col_Rate.FieldName = "Rate";
            this.col_Rate.Name = "col_Rate";
            // 
            // frm_SL_Return
            // 
            resources.ApplyResources(this, "$this");
            this.Appearance.FontSizeDelta = ((int)(resources.GetObject("frm_SL_Return.Appearance.FontSizeDelta")));
            this.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("frm_SL_Return.Appearance.FontStyleDelta")));
            this.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("frm_SL_Return.Appearance.GradientMode")));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("frm_SL_Return.Appearance.Image")));
            this.Appearance.Options.UseTextOptions = true;
            this.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.grd_SubTaxes);
            this.Controls.Add(this.labelControl29);
            this.Controls.Add(this.txt_EtaxValue);
            this.Controls.Add(this.labelControl27);
            this.Controls.Add(this.labelControl23);
            this.Controls.Add(this.txt_CusTaxV);
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.cmbPayMethod);
            this.Controls.Add(this.chk_IsInTrns);
            this.Controls.Add(this.btnAddCustomer);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.groupControl1);
            this.Controls.Add(this.txtNet);
            this.Controls.Add(this.labelControl19);
            this.Controls.Add(this.labelControl20);
            this.Controls.Add(this.labelControl10);
            this.Controls.Add(this.labelControl16);
            this.Controls.Add(this.lkp_Customers);
            this.Controls.Add(this.txt_Total);
            this.Controls.Add(this.labelControl18);
            this.Controls.Add(this.labelControl11);
            this.Controls.Add(this.labelControl8);
            this.Controls.Add(this.labelControl15);
            this.Controls.Add(this.panelControl2);
            this.Controls.Add(this.labelControl13);
            this.Controls.Add(this.labelControl6);
            this.Controls.Add(this.labelControl14);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.labelControl7);
            this.Controls.Add(this.labelControl12);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.labelControl9);
            this.Controls.Add(this.panelControl1);
            this.Controls.Add(this.labelControl36);
            this.Controls.Add(this.labelControl35);
            this.Controls.Add(this.btnNext);
            this.Controls.Add(this.btnPrevious);
            this.Controls.Add(this.txtExpenses);
            this.Controls.Add(this.txtDiscountRatio);
            this.Controls.Add(this.txtDiscountValue);
            this.Controls.Add(this.txt_TaxValue);
            this.Controls.Add(this.txt_DeductTaxR);
            this.Controls.Add(this.txt_DeductTaxV);
            this.Controls.Add(this.txt_AddTaxR);
            this.Controls.Add(this.txt_AddTaxV);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.KeyPreview = true;
            this.Name = "frm_SL_Return";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_SL_Return_FormClosing);
            this.Load += new System.EventHandler(this.frm_SL_Return_Load);
            this.Shown += new System.EventHandler(this.frm_SL_Return_Shown);
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.frm_SL_Return_KeyUp);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtInvoiceCode.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtInvoiceDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtInvoiceDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            this.panelControl1.PerformLayout();
            this.flowLayoutPanel1.ResumeLayout(false);
            this.pnlInvCode.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.textEdit7.Properties)).EndInit();
            this.pnlDate.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.textEdit6.Properties)).EndInit();
            this.pnlBranch.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.textEdit5.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpStore.Properties)).EndInit();
            this.pnlCostCenter.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.textEdit1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpCostCenter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv_CostCenter)).EndInit();
            this.pnlCrncy.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtCurrency.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNotes.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Drawers.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).EndInit();
            this.panelControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grdPrInvoice)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_vendors)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repDiscountRatio)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repSpin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repUOM)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repItems)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_expireDate.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_expireDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repManufactureDate.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repManufactureDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repTaxTypes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_btnAddTaxes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repExpireDate.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repExpireDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repLocation)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repExpireDate_txt)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Customers.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Total.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Remains.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNet.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_paid.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtExpenses.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Drawers2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_PayAcc1_Paid.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_PayAcc2_Paid.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbPayMethod.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscountRatio.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscountValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_TaxValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DeductTaxR.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DeductTaxV.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_AddTaxR.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_AddTaxV.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_CusTaxV.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_IsInTrns.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_EtaxValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grd_SubTaxes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv_SubTaxes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_SubTaxes)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnSave;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtnHelp;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraEditors.TextEdit txtInvoiceCode;
        private DevExpress.XtraEditors.SimpleButton btnNext;
        private DevExpress.XtraEditors.SimpleButton btnPrevious;
        private DevExpress.XtraBars.BarButtonItem batBtnList;
        private DevExpress.XtraBars.BarButtonItem barBtnNew;
        private DevExpress.XtraEditors.LabelControl labelControl35;
        private DevExpress.XtraEditors.DateEdit dtInvoiceDate;
        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl36;
        private DevExpress.XtraEditors.MemoEdit txtNotes;
        
        private DevExpress.XtraEditors.PanelControl panelControl2;
        private DevExpress.XtraGrid.GridControl grdPrInvoice;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit repSpin;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn colPurchasePrice;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit repLocation;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn colDescription;
        private DevExpress.XtraGrid.Columns.GridColumn colLocationNameAr;
        private DevExpress.XtraGrid.Columns.GridColumn colLocationId;
        private DevExpress.XtraEditors.Repository.RepositoryItemDateEdit repExpireDate;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit repUOM;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit repItems;
        private DevExpress.XtraGrid.Views.Grid.GridView repositoryItemGridLookUpEdit1View;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn col_CurrentQty;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repExpireDate_txt;
        private DevExpress.XtraEditors.LookUpEdit lkp_Drawers;
        private DevExpress.XtraEditors.LabelControl labelControl17;
        private DevExpress.XtraGrid.Columns.GridColumn col_TotalSellPrice;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn38;
        private DevExpress.XtraEditors.GridLookUpEdit lkp_Customers;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_vendors;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn39;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn40;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn42;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn43;
        private DevExpress.XtraEditors.LabelControl labelControl19;
        private DevExpress.XtraEditors.TextEdit txt_Total;
        private DevExpress.XtraEditors.LabelControl labelControl18;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private DevExpress.XtraEditors.TextEdit txt_Remains;
        private DevExpress.XtraEditors.TextEdit txtNet;
        private DevExpress.XtraEditors.LabelControl lbl_remains;
        private DevExpress.XtraEditors.LabelControl labelControl13;
        private DevExpress.XtraEditors.LabelControl lbl_Paid;
        private DevExpress.XtraEditors.TextEdit txt_paid;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraBars.BarButtonItem barBtnCancel;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repDiscountRatio;
        private DevExpress.XtraEditors.LookUpEdit lkpStore;
        private DevExpress.XtraEditors.SimpleButton btnAddCustomer;
        private DevExpress.XtraBars.BarButtonItem barBtnPrint;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem mi_frm_IC_Item;
        private DevExpress.XtraEditors.SpinEdit txtExpenses;
        private DevExpress.XtraEditors.LabelControl labelControl20;
        private DevExpress.XtraGrid.Columns.GridColumn col_Expire;
        private DevExpress.XtraGrid.Columns.GridColumn col_Batch;
        private DevExpress.XtraEditors.Repository.RepositoryItemDateEdit rep_expireDate;
        private DevExpress.XtraBars.BarButtonItem barbtnLoadSellInvoice;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.LookUpEdit lkp_Drawers2;
        private DevExpress.XtraEditors.SpinEdit txt_PayAcc1_Paid;
        private DevExpress.XtraEditors.LabelControl labelControl28;
        private DevExpress.XtraEditors.LabelControl labelControl26;
        private DevExpress.XtraEditors.SpinEdit txt_PayAcc2_Paid;
        private DevExpress.XtraEditors.LabelControl labelControl25;
        private DevExpress.XtraGrid.Columns.GridColumn col_Length;
        private DevExpress.XtraGrid.Columns.GridColumn col_Width;
        private DevExpress.XtraGrid.Columns.GridColumn col_Height;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.ImageComboBoxEdit cmbPayMethod;
        private DevExpress.XtraEditors.TextEdit textEdit7;
        private DevExpress.XtraEditors.TextEdit textEdit6;
        private DevExpress.XtraEditors.TextEdit textEdit5;
        private DevExpress.XtraGrid.Columns.GridColumn col_TotalQty;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn col_PiecesCount;
        private DevExpress.XtraGrid.Columns.GridColumn col_ItemDescription;
        private DevExpress.XtraGrid.Columns.GridColumn col_ItemDescriptionEn;
        private DevExpress.XtraGrid.Columns.GridColumn col_DiscountRatio2;
        private DevExpress.XtraGrid.Columns.GridColumn col_SalesTax;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraGrid.Columns.GridColumn col_DiscountRatio3;
        private DevExpress.XtraEditors.LabelControl labelControl16;
        private DevExpress.XtraEditors.LabelControl labelControl15;
        private DevExpress.XtraEditors.LabelControl labelControl14;
        private DevExpress.XtraEditors.LabelControl labelControl12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraEditors.SpinEdit txtDiscountRatio;
        private DevExpress.XtraEditors.SpinEdit txtDiscountValue;
        private DevExpress.XtraEditors.SpinEdit txt_TaxValue;
        private DevExpress.XtraEditors.SpinEdit txt_DeductTaxR;
        private DevExpress.XtraEditors.SpinEdit txt_DeductTaxV;
        private DevExpress.XtraEditors.SpinEdit txt_AddTaxR;
        private DevExpress.XtraEditors.SpinEdit txt_AddTaxV;
        private System.Windows.Forms.ToolStripMenuItem mi_InvoiceStaticDisc;
        private DevExpress.XtraEditors.TextEdit txtCurrency;
        private uc_Currency uc_Currency1;
        private System.Windows.Forms.Panel pnlDate;
        private System.Windows.Forms.Panel pnlInvCode;
        private System.Windows.Forms.Panel pnlBranch;
        private System.Windows.Forms.Panel pnlCrncy;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel1;
        private System.Windows.Forms.Panel pnlCostCenter;
        private DevExpress.XtraEditors.TextEdit textEdit1;
        private System.Windows.Forms.ToolStripMenuItem mi_InvoiceStaticDimensions;
        private System.Windows.Forms.ToolStripMenuItem mi_PasteRows;
        private DevExpress.XtraGrid.Columns.GridColumn col_CompanyNameAr;
        private DevExpress.XtraGrid.Columns.GridColumn col_CategoryNameAr;
        private DevExpress.XtraGrid.Columns.GridColumn col_Serial;
        private System.Windows.Forms.ToolStripMenuItem importFromExcelSheetToolStripMenuItem;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraEditors.Repository.RepositoryItemDateEdit repManufactureDate;
        private DevExpress.XtraGrid.Columns.GridColumn col_CusTax;
        private DevExpress.XtraEditors.LabelControl labelControl27;
        private DevExpress.XtraEditors.LabelControl labelControl23;
        private DevExpress.XtraEditors.SpinEdit txt_CusTaxV;
        private DevExpress.XtraGrid.Columns.GridColumn col_Serial2;
        private DevExpress.XtraGrid.Columns.GridColumn col_LibraQty;
        private DevExpress.XtraGrid.Columns.GridColumn col_IsOffer;
        private DevExpress.XtraGrid.Columns.GridColumn col_PricingWithSmall;
        private DevExpress.XtraGrid.Columns.GridColumn col_VariableWeight;
        private DevExpress.XtraGrid.Columns.GridColumn col_kg_Weight_libra;
        private DevExpress.XtraGrid.Columns.GridColumn col_Is_Libra;
        private DevExpress.XtraGrid.Columns.GridColumn col_Pack;
        private DevExpress.XtraEditors.GridLookUpEdit lkpCostCenter;
        private DevExpress.XtraGrid.Views.Grid.GridView gv_CostCenter;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn col_ItemNameF;
        private DevExpress.XtraEditors.CheckEdit chk_IsInTrns;
        private DevExpress.XtraBars.BarButtonItem btnImport;
        private DevExpress.XtraGrid.Columns.GridColumn colbonusDiscount;
        private DevExpress.XtraGrid.Columns.GridColumn colTaxType;
        private DevExpress.XtraGrid.Columns.GridColumn colEtaxValue;
        private DevExpress.XtraGrid.Columns.GridColumn col_ETaxRatio;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit repTaxTypes;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView6;
        private DevExpress.XtraGrid.Columns.GridColumn DescriptionAr;
        private DevExpress.XtraGrid.Columns.GridColumn Code;
        private DevExpress.XtraGrid.Columns.GridColumn E_TaxableTypeId;
        private DevExpress.XtraBars.BarButtonItem barBtnDelete;
        private DevExpress.XtraGrid.Columns.GridColumn btn_AddTaxes;
        private DevExpress.XtraGrid.Columns.GridColumn TotalTaxes;
        private DevExpress.XtraGrid.Columns.GridColumn totalTaxesRatio;
        private DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit rep_btnAddTaxes;
        private DevExpress.XtraEditors.LabelControl labelControl29;
        private DevExpress.XtraEditors.SpinEdit txt_EtaxValue;
        private DevExpress.XtraGrid.Columns.GridColumn totalTableTaxes;
        private DevExpress.XtraGrid.Columns.GridColumn salePriceWithTaxTable;
        private DevExpress.XtraGrid.GridControl grd_SubTaxes;
        private DevExpress.XtraGrid.Views.Grid.GridView gv_SubTaxes;
        private DevExpress.XtraGrid.Columns.GridColumn Value;
        private DevExpress.XtraGrid.Columns.GridColumn SubTaxId;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit lkp_SubTaxes;
        private DevExpress.XtraGrid.Columns.GridColumn col_Rate;
        private DevExpress.XtraGrid.Columns.GridColumn addTaxValue;
        private DevExpress.XtraGrid.Columns.GridColumn tableTaxValue;
        private DevExpress.XtraGrid.Columns.GridColumn col_TaxValue;
        private DevExpress.XtraGrid.Columns.GridColumn col_TotalSubCustomTax;
        private DevExpress.XtraGrid.Columns.GridColumn col_TotalSubAddTax;
        private DevExpress.XtraGrid.Columns.GridColumn col_TotalSubDiscountTax;
    }
}