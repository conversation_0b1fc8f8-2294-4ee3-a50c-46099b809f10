﻿using DAL;
using DAL.Res;
using DevExpress.Utils;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraLayout.Utils;
using DevExpress.XtraReports.UI;
using Reports.SL;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace Reports
{
    public partial class frm_ReportViewer : DevExpress.XtraEditors.XtraForm
    {
        List<reportListItem> lstReportListItem = new List<reportListItem>();
        //DataTable dtStores = new DataTable();
        List<SL_CustomerCategoryInfo> lst_CustGroup = new List<SL_CustomerCategoryInfo>();
        List<SL_Customer_Info> lst_Customers = new List<SL_Customer_Info>();
        //List<PR_VendorGroupInfo> lst_VenGroup = new List<PR_VendorGroupInfo>();
        //List<VendorInfo> lst_Vendors = new List<VendorInfo>();
        //List<acc> lst_Accounts = new List<acc>();
        //List<ACC_CostCenter> lst_CostCenters = new List<ACC_CostCenter>();

        List<IC_Category> lst_Cat = new List<IC_Category>();
        List<IC_Item> lst_Items = new List<IC_Item>();
        //List<IC_Item> lst_MtrxPrntItems = new List<IC_Item>();
        //List<IC_Matrix> lst_MtrxHeaders = new List<IC_Matrix>();
        //List<IC_MatrixDetail> lst_MtrxDetails = new List<IC_MatrixDetail>();

        //List<IC_MatrixDetail> lst_Mtrx1 = new List<IC_MatrixDetail>();
        //List<IC_MatrixDetail> lst_Mtrx2 = new List<IC_MatrixDetail>();
        //List<IC_MatrixDetail> lst_Mtrx3 = new List<IC_MatrixDetail>();

        //List<JO_Status> lst_Status = new List<JO_Status>();
        //List<JO_Priority> lst_Priority = new List<JO_Priority>();
        //List<JO_Dept> lst_Dept = new List<JO_Dept>();
        //List<ST_InvoiceBook> lst_invBooks = new List<ST_InvoiceBook>();
        List<int> lstPayAccounts = new List<int>();
        List<IC_Store> lstStores = new List<IC_Store>();
        //List<HR_Group> lstEmpGroup = new List<HR_Group>();

        DataTable dtCompanies = new DataTable();


        DataTable dtUsers = new DataTable();
        DataTable dtSalesEmp = new DataTable();

        DateTime dateFrom, dateTo;
        int itemId1, itemId2,//
            storeId1, storeId2,
            vendorId1, vendorId2,//
            customerId1, customerId2,//
            companyId,
            processType,
            costCenter,
            AccountId,
            customAccListId,
            userId,
            salesEmpId,
            custGroupId,
            VenGroupId,
            MtrxParentId,
            M1,
            M2,
            M3,
            StatusId,
            PriorityId,
            DeptId,
            EmpGroupId;

        string PlateNo;
        int? goldenVendor, goldenCustomer;

        byte FltrTyp_Item, FltrTyp_Date, FltrTyp_Store, FltrTyp_Vendor, FltrTyp_Customer,
            FltrTyp_Company, FltrTyp_Category, FltrTyp_User, FltrTyp_SalesEmp, FltrTyp_SellPrice,
            FltrTyp_InvBook, FltrTyp_EmpGroup, fltrtype_Car;

        List<int> Selected_Customers_Vendors = new List<int>();
        List<string> Selected_Accounts = new List<string>();
        List<int> Customers_Vendors_Categories = new List<int>();


        private void gridView9_SelectionChanged(object sender, DevExpress.Data.SelectionChangedEventArgs e)
        {
            int jid = Convert.ToInt32(gridView9.GetFocusedRowCellValue("CustomerGroupId"));
            //if (jid == 0 && e.Action != CollectionChangeAction.Refresh) return;
            if (e.Action == CollectionChangeAction.Refresh)
            {
                //select all
                if ((Customers_Vendors_Categories.Count > 0 && Customers_Vendors_Categories.Count < (lkpCustGroup.Properties.DataSource as IEnumerable<SL_CustomerCategoryInfo>).Count())
                    || Customers_Vendors_Categories.Count == 0)
                {
                    Customers_Vendors_Categories.Clear();
                    gridView9.SelectAll();
                    foreach (var r in gridView9.GetSelectedRows())
                        Customers_Vendors_Categories.Add(Convert.ToInt32(gridView9.GetRowCellValue(r, "CustomerGroupId")));
                    return;
                }
                //unselect all
                else if (Customers_Vendors_Categories.Count > 0 && Customers_Vendors_Categories.Count == (lkpCustGroup.Properties.DataSource as IEnumerable<SL_CustomerCategoryInfo>).Count())
                {
                    Customers_Vendors_Categories.Clear();
                    return;
                }
            }

            Add_Remove_Customers_Vendors_Categories(e, jid);
        }
        private void Add_Remove_Customers_Vendors_Categories(DevExpress.Data.SelectionChangedEventArgs e, int jid)
        {

            //{ Customers_Vendors_Categories.Clear(); return; }
            if (e.Action == CollectionChangeAction.Add)
            {
                if (!Customers_Vendors_Categories.Contains(jid))
                {
                    Customers_Vendors_Categories.Add(jid);
                }
            }
            else if (e.Action == CollectionChangeAction.Remove)
            {
                Customers_Vendors_Categories.Remove(jid);
            }
        }
        private void gridView17_SelectionChanged(object sender, DevExpress.Data.SelectionChangedEventArgs e)
        {
            int jid = Convert.ToInt32(gridView17.GetFocusedRowCellValue("VendorGroupId"));

            //if (jid == 0 && e.Action != CollectionChangeAction.Refresh) return;
            if (e.Action == CollectionChangeAction.Refresh)
            {
                //select all
                if ((Customers_Vendors_Categories.Count > 0 && Customers_Vendors_Categories.Count < (lkpVenGroup.Properties.DataSource as IEnumerable<PR_VendorGroupInfo>).Count())
                    || Customers_Vendors_Categories.Count == 0)
                {
                    Customers_Vendors_Categories.Clear();
                    gridView9.SelectAll();
                    foreach (var r in gridView17.GetSelectedRows())
                        Customers_Vendors_Categories.Add(Convert.ToInt32(gridView17.GetRowCellValue(r, "VendorGroupId")));
                    return;
                }
                //unselect all
                else if (Customers_Vendors_Categories.Count > 0 && Customers_Vendors_Categories.Count == (lkpVenGroup.Properties.DataSource as IEnumerable<PR_VendorGroupInfo>).Count())
                {
                    Customers_Vendors_Categories.Clear();
                    return;
                }
            }

            Add_Remove_Customers_Vendors_Categories(e, jid);
        }

        private void gridView1_SelectionChanged(object sender, DevExpress.Data.SelectionChangedEventArgs e)
        {
            int jid = Convert.ToInt32(gridView1.GetFocusedRowCellValue("VendorId"));
            Add_Remove_SelectedCustomers_Vendors(e, jid);
        }

        private void lkpCars_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                PlateNo = lkpCars.GetColumnValue("PlateNo").ToString();
            }
            catch { PlateNo = ""; }
        }

        private void gridView5_SelectionChanged(object sender, DevExpress.Data.SelectionChangedEventArgs e)
        {
            int jid = Convert.ToInt32(gridView5.GetFocusedRowCellValue("CustomerId"));
            Add_Remove_SelectedCustomers_Vendors(e, jid);
        }

        private void grdVAccount_SelectionChanged(object sender, DevExpress.Data.SelectionChangedEventArgs e)
        {

            string jid = Convert.ToString(grdVAccount.GetFocusedRowCellValue("AccNumber"));
            Add_Remove_SelectedAccounts(e, jid);
        }

        private void Add_Remove_SelectedCustomers_Vendors(DevExpress.Data.SelectionChangedEventArgs e, int jid)
        {
            if (e.Action == CollectionChangeAction.Add)
            {
                if (!Selected_Customers_Vendors.Contains(jid))
                {
                    Selected_Customers_Vendors.Add(jid);
                }
            }
            else if (e.Action == CollectionChangeAction.Remove)
            {
                Selected_Customers_Vendors.Remove(jid);
            }
        }
        private void Add_Remove_SelectedAccounts(DevExpress.Data.SelectionChangedEventArgs e, string jid)
        {
            if (e.Action == CollectionChangeAction.Add)
            {
                if (!Selected_Accounts.Contains(jid))
                {
                    Selected_Accounts.Add(jid);
                }
            }
            else if (e.Action == CollectionChangeAction.Remove)
            {
                Selected_Accounts.Remove(jid);
            }
        }

        private void lstBxReports_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (sender.GetType() == typeof(ImageListBoxControl))
            {
                if ((((ImageListBoxControl)sender).SelectedValue == "rpt_Acc_PR_AccountsBalancesWithNotes" ||
                         ((ImageListBoxControl)sender).SelectedValue == "rpt_Acc_PR_AccountsBalances" ||
                         ((ImageListBoxControl)sender).SelectedValue == "rpt_Acc_SL_AccountsBalancesWithNotes" ||
                         ((ImageListBoxControl)sender).SelectedValue == "rpt_Acc_SL_AccountsBalances"))
                {
                    lkpCustGroup.Properties.View.OptionsSelection.MultiSelect = true;
                    lkpCustGroup.Properties.View.OptionsSelection.MultiSelectMode = GridMultiSelectMode.CheckBoxRowSelect;
                    lkpVenGroup.Properties.View.OptionsSelection.MultiSelect = true;
                    lkpVenGroup.Properties.View.OptionsSelection.MultiSelectMode = GridMultiSelectMode.CheckBoxRowSelect;
                }
                else
                {
                    lkpCustGroup.Properties.View.OptionsSelection.MultiSelect = false;
                    lkpCustGroup.Properties.View.OptionsSelection.MultiSelectMode = GridMultiSelectMode.RowSelect;
                    lkpVenGroup.Properties.View.OptionsSelection.MultiSelect = false;
                    lkpVenGroup.Properties.View.OptionsSelection.MultiSelectMode = GridMultiSelectMode.RowSelect;
                }

                if (((ImageListBoxControl)sender).SelectedValue == "rpt_ACC_SubLedger")
                {
                    lkpAccount.Properties.View.OptionsSelection.MultiSelect = true;
                    lkpAccount.Properties.View.OptionsSelection.MultiSelectMode = GridMultiSelectMode.CheckBoxRowSelect;
                }
                else
                {
                    lkpAccount.Properties.View.OptionsSelection.MultiSelect = false;
                    lkpAccount.Properties.View.OptionsSelection.MultiSelectMode = GridMultiSelectMode.RowSelect;
                }

                if (cmbFltrTyp_Customer.Properties.Items.Count() < 4)
                {
                    if ((((ImageListBoxControl)sender).SelectedValue == "rpt_Acc_PR_AccountsBalancesWithNotes" ||
                          ((ImageListBoxControl)sender).SelectedValue == "rpt_Acc_PR_AccountsBalances" ||
                          ((ImageListBoxControl)sender).SelectedValue == "rpt_Acc_SL_AccountsBalancesWithNotes" ||
                          ((ImageListBoxControl)sender).SelectedValue == "rpt_Acc_SL_AccountsBalances"))
                    {
                        cmbFltrTyp_Vendor.Properties.Items.Add(new ImageComboBoxItem(Shared.IsEnglish ? "Each one of" : "كل من", (byte)3, 3));
                        cmbFltrTyp_Customer.Properties.Items.Add(new ImageComboBoxItem(Shared.IsEnglish ? "Each one of" : "كل من", (byte)3, 3));
                    }
                }
                else
                {
                    if ((((ImageListBoxControl)sender).SelectedValue != "rpt_Acc_PR_AccountsBalancesWithNotes" &&
                        ((ImageListBoxControl)sender).SelectedValue != "rpt_Acc_PR_AccountsBalances" &&
                        ((ImageListBoxControl)sender).SelectedValue != "rpt_Acc_SL_AccountsBalancesWithNotes" &&
                        ((ImageListBoxControl)sender).SelectedValue != "rpt_Acc_SL_AccountsBalances"))
                        if (cmbFltrTyp_Vendor.Properties.Items.Count() > 3 && cmbFltrTyp_Customer.Properties.Items.Count() > 3)
                        {
                            cmbFltrTyp_Vendor.Properties.Items.RemoveAt(3); cmbFltrTyp_Customer.Properties.Items.RemoveAt(3);
                            cmbFltrTyp_Vendor.SelectedIndex = cmbFltrTyp_Customer.SelectedIndex = 0;
                        }
                }
            }
            Selected_Customers_Vendors.Clear();
            Selected_Accounts.Clear();
            foreach (var r in gridView1.GetSelectedRows())
                gridView1.UnselectRow(r);
            foreach (var r in grdVAccount.GetSelectedRows())
                grdVAccount.UnselectRow(r);
        }
        byte? itemType;
        string dateFilter, otherFilters;
        string custGroupAccNumber, venGroupAccNumber, categoryNum, accNum;
        string InvBooks;

        AccountTypeToShow ShowAccount;//determine which accounts are available for each report

        ERPDataContext DB = new ERPDataContext();
        Action<int, int> OpenJobOrder;
        Action<int, int, int, bool> OpenSourceProcess;
        public frm_ReportViewer(Action<int, int, int, bool> OpenSourceProcess, Action<int, int> OpenJobOrder)
        {
            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            ReportsRTL.RTL_BarManager(this.barManager1);

            dateFrom = dateTo = Shared.minDate;
            categoryNum = string.Empty;
            this.OpenJobOrder = OpenJobOrder;
            this.OpenSourceProcess = OpenSourceProcess;
        }
        public frm_ReportViewer(Action<int, int, int, bool> OpenSourceProcess, bool hr)
        {
            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            ReportsRTL.RTL_BarManager(this.barManager1);

            dateFrom = dateTo = Shared.minDate;
            categoryNum = string.Empty;

            this.OpenSourceProcess = OpenSourceProcess;
        }
        public frm_ReportViewer(bool hr)
        {
            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            ReportsRTL.RTL_BarManager(this.barManager1);

            dateFrom = dateTo = Shared.minDate;
            categoryNum = string.Empty;

        }

        private void frm_ReportViewer_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                ReportsRTL.LTRLayout(this);

            //lstPayAccounts = DB.ACC_Drawers.Select(x => x.AccountId).Union(DB.ACC_Banks.Select(x => x.AccountId)).ToList();
            accNum = string.Empty;

            if (!Shared.ItemMatrixAvailable)
            {
                ParentItem1.Visibility = ParentItem2.Visibility = LayoutVisibility.Never;
                Mtrx0.Visibility = Mtrx1.Visibility =
                    Mtrx2.Visibility = Mtrx3.Visibility = LayoutVisibility.Never;

                lkpMtrxParentId.EditValue = lkpMtrxD1.EditValue = lkpMtrx2.EditValue =
                    lkpMtrx3.EditValue = null;
            }
            if (!Shared.st_Store.UseQC == false)
            {
                QC1.Visibility = QC2.Visibility = QC3.Visibility = QC4.Visibility = LayoutVisibility.Never;

                txtQC.Text = string.Empty;
            }

           
            dt1.EditValue = dt2.EditValue = dtExpDate.EditValue = null;
            ShowHideFilters(false, false, false, false, false, false, false, false, false, false,
                false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false);

            lkpItem1.Enabled = lkpItem2.Enabled = false;
            dt1.Enabled = dt2.Enabled = false;
            lkpStore1.Enabled = lkpStore2.Enabled = false;
            lkpVendor1.Enabled = lkpVendor2.Enabled = false;
            lkpCustomer1.Enabled = lkpCustomer2.Enabled = false;
            lkpCompany.Enabled = false;
            lkpCategory.Enabled = false;
            lkpUser.Enabled = false;
            lkpSalesEmp.Enabled = false;

            //ErpUtils.Allow_Incremental_Search(lkpCustomer1);
            //ErpUtils.Allow_Incremental_Search(lkpCustomer2);
            //ErpUtils.Allow_Incremental_Search(lkpItem1);
            //ErpUtils.Allow_Incremental_Search(lkpItem2);
            //ErpUtils.Allow_Incremental_Search(lkpStore1);
            //ErpUtils.Allow_Incremental_Search(lkpStore2);
            //ErpUtils.Allow_Incremental_Search(lkpUser);
            //ErpUtils.Allow_Incremental_Search(lkpVendor1);
            //ErpUtils.Allow_Incremental_Search(lkpVendor2);
            //ErpUtils.Allow_Incremental_Search(lkpAccount);

            //lkpCategory.Properties.TextEditStyle = lkpCompany.Properties.TextEditStyle =
            //lkpCostCenters.Properties.TextEditStyle = lkpAccount.Properties.TextEditStyle =
            //lkpCustomList.Properties.TextEditStyle = lkpSalesEmp.Properties.TextEditStyle = TextEditStyles.Standard;

        }


        private void NBI_IC_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            NbiAppearance("IC");
            //Fill Store Reports
            lstReportListItem.Clear();

            //lstReportListItem.Add(new reportListItem
            //{
            //    ReportName = "rprt_IC_Items",
            //    ReportCaption = "الأصناف",
            //    Description = "عرض كود1, اسم الصنف-ن, الفئة, المجموعة العلمية, الشركة, الكمية الحالية",
            //    TabIndex = 0,
            //    ItemFltr = false,
            //    DateFltr = false,
            //    ExpDateFltr = false,
            //    StoreFltr = false,
            //    VendorFltr = false,
            //    CustomerFltr = false,
            //    ComapnyFltr = false,
            //    CategoryFltr = false
            //});

            //lstReportListItem.Add(new reportListItem
            //{
            //    ReportName = "frm_rpt_IC_EditItemQty",
            //    ReportCaption = "أصناف تم تسوية كمياتها",
            //    Description = "عرض كود1, اسم الصنف-ن, الكميه القديمه والكميه الجديده",
            //    TabIndex = 0,
            //    ItemFltr = false,
            //    DateFltr = true,
            //    ExpDateFltr = false,
            //    StoreFltr = true,
            //    VendorFltr = false,
            //    CustomerFltr = false,
            //    ComapnyFltr = false,
            //    CategoryFltr = false
            //});

            //lstReportListItem.Add(new reportListItem
            //{
            //    ReportName = "rpt_IC_ItemsOpenBalance",
            //    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_IC_ItemsOpenBalance : ResRptAr.rpt_IC_ItemsOpenBalance, //الارصدة الافتتاحية
            //    Description = "عرض كود1, اسم الصنف-ن, الكميه,سعر التكلفه",
            //    TabIndex = 0,
            //    ItemFltr = true,
            //    DateFltr = false,
            //    ExpDateFltr = false,
            //    StoreFltr = true,
            //    VendorFltr = true,
            //    CustomerFltr = false,
            //    ComapnyFltr = false,
            //    CategoryFltr = false,
            //    ProcessFltr = false,
            //    CostCenterFltr = false,
            //    AccountFltr = false,
            //    CustmAccListFltr = false,
            //    UserFltr = false,
            //    SalesEmpFltr = false,
            //    BatchFltr = false,
            //    CutGroupFltr = false
            //});

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_Ic_ItemsQty).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_IC_ItemsQty",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_IC_ItemsQty : ResRptAr.rpt_IC_ItemsQty, //"أرصدة الأصناف",
                    Description = "عرض الأصناف وأرصدتها الموجوده في المخازن",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = true,
                    CategoryFltr = true,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = true,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });
            
            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_Ic_ItemsQty).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_IC_ItemsQtyImages",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_IC_ItemsQty+ " With images" : ResRptAr.rpt_IC_ItemsQty + " بالصور", //"أرصدة الأصناف",
                    Description = "عرض الأصناف وأرصدتها الموجوده في المخازن",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = true,
                    CategoryFltr = true,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = true,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            /*if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_Ic_ItemsQty).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_IC_ItemsQtywithImages",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_IC_ItemsQty : ResRptAr.rpt_IC_ItemsQty, //"أرصدة الأصناف",
                    Description = "عرض الأصناف بالصور وأرصدتها الموجوده في المخازن",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = true,
                    CategoryFltr = true,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = true,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });*/

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_IC_ItemsQtyDetails).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_IC_ItemQtyBatch",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_IC_ItemsQtyDetails : ResRptAr.rpt_IC_ItemsQtyDetails,
                    Description = "تقرير أرصدة الأصناف تفصيلي",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = false,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = true,
                    CutGroupFltr = false,
                    MtrxFltr = true,
                    DimensionFltr = true,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_IC_ItemsQtyH).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_IC_ItemsQtyH",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.frm_IC_ItemsQtyH : ResRptAr.frm_IC_ItemsQtyH,
                    Description = "عرض الأصناف وأرصدتها الموجوده في المخازن بشكل أفقي",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = true,
                    CategoryFltr = true,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = true,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_IC_ItemsQtyHWithoutCost).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_IC_ItemsQtyHWithoutCost",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.frm_IC_ItemsQtyHNoCost : ResRptAr.frm_IC_ItemsQtyHNoCost,
                    Description = "عرض الأصناف وأرصدتها الموجوده في المخازن بشكل أفقي بدون تكلفة",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = true,
                    CategoryFltr = true,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = true,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_IC_ItemsQtyWithPrices).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_IC_ItemsQtyWithPrices",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_IC_ItemsQtyWithPrices : ResRptAr.rpt_IC_ItemsQtyWithPrices, //"أرصدة الأصناف بالأسعار",
                    Description = "عرض الأصناف وأرصدتها و تقييمها بأسعار الشراء ، البيع و التكلفة",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = true,
                    CategoryFltr = true,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = true,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_IC_ItemsQtyWithSalesPrice).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_IC_ItemsQtyWithSalesPrice",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_IC_ItemsQtyWithSalesPrice : ResRptAr.rpt_IC_ItemsQtyWithSalesPrice,
                    Description = "عرض الأصناف وأرصدتها ومعامل وحدات القياس و تقييمها بأسعار البيع",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = true,
                    CategoryFltr = true,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_Ic_ItemsReorder).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_IC_ItemsReorder",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_IC_ItemsReorder : ResRptAr.rpt_IC_ItemsReorder, //"أصناف وصلت لحد الطلب",
                    Description = "عرض الأصناف التي وصلت لحد الطلب المحدد في كارت الصنف وأرصدتها الموجوده في المخازن",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = false,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = true,
                    CategoryFltr = true,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });


            //update 13/9/2017
            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
               ||
               (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
               Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_IC_ItemsMinLevel).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_IC_ItemsMinLevel",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_IC_ItemsMinLevel : ResRptAr.rpt_IC_ItemsMinLevel, //"أصناف وصلت للحد الأدنى للطلب",
                    Description = "عرض الأصناف التي وصلت للحد الأدنى للطلب المحدد في كارت الصنف وأرصدتها الموجوده في المخازن",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = false,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = true,
                    CategoryFltr = true,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });


            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_IC_ItemsNotSold).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_IC_ItemsNotSold",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_IC_ItemsNotSold : ResRptAr.rpt_IC_ItemsNotSold, //"أصناف لم تباع مطلقا",
                    Description = "عرض الأصناف التي تم شرائها ولكن لم يباع منها شئ حتي الان",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = true,
                    CategoryFltr = true,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_IC_ItemsMinSell).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_IC_ItemsMinSell",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_IC_ItemsMinSell : ResRptAr.rpt_IC_ItemsMinSell, //"الاصناف الاقل مبيعا",
                    Description = "عرض الأصناف الاقل مبيعا حتي الان",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = true,
                    CategoryFltr = true,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_IC_ItemsMaxSell).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_IC_ItemsMaxSell",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_IC_ItemsMaxSell : ResRptAr.rpt_IC_ItemsMaxSell, //"الاصناف الاكثر مبيعا",
                    Description = "عرض الأصناف الاكثر مبيعا حتي الان",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = true,
                    CategoryFltr = true,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            //if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
            //    ||
            //    (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
            //    Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_IC_ItemsTotals).Count() == 1))
            //    lstReportListItem.Add(new reportListItem
            //    {
            //        ReportName = "rpt_IC_ItemsTotals",
            //        ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_IC_ItemsTotals : ResRptAr.rpt_IC_ItemsTotals, //"حركة الاصناف",
            //        Description = "عرض اجمالي حركات التي تمت علي الصنف مثل البيع والشراء والمرتجعات",
            //        TabIndex = 0,
            //        ItemFltr = true,
            //        DateFltr = false,
            //        ExpDateFltr = false,
            //        StoreFltr = true,
            //        VendorFltr = false,
            //        CustomerFltr = false,
            //        ComapnyFltr = true,
            //        CategoryFltr = true,
            //        ProcessFltr = false,
            //        CostCenterFltr = false,
            //        AccountFltr = false,
            //        CustmAccListFltr = false,
            //        UserFltr = false,
            //        SalesEmpFltr = false,
            //        BatchFltr = false,
            //        CutGroupFltr = false,
            //        MtrxFltr = false,
            //        DimensionFltr = false,
            //        JobOrderFltr = false,
            //        ItemTypeFltr = false,
            //        SellPriceFltr = false,
            //        InvBookFltr = false,
            //        EmpGroupFltr = false,
            //        QcFltr = false
            //    });
            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
            ||
            (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
            Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_IC_InOutItems).Count() == 1))
            {
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_IC_InOutItems",
                    ReportCaption = Shared.IsEnglish ? "Purchase and Selling" : "حركات الصرف والاضافة", //"تقرير يعرض حركات الصرف والاضافة (المخازن) لموظف خلال فترة",
                    Description = "تقرير يعرض حركات الصرف والاضافة (المخازن) لموظف خلال فترة",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = true,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = true,
                    QcFltr = false,
                    VenGroupFltr = false
                });

                /*lkpSalesEmp.Properties.DisplayMember = "UserName";
                lkpSalesEmp.Properties.ValueMember = "UserId";
                lkpSalesEmp.Properties.DataSource = from x in DB.HR_Users select new { x.UserName, x.UserId };*/

            }

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_IC_ItemTransactions).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_IC_ItemTransactions",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_IC_ItemTransactions : ResRptAr.rpt_IC_ItemTransactions, //"حركة صنف تفصيلي",
                    Description = "عرض تفصيلي لجميع الحركات التي تمت علي الصنف بالتكلفة",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = true,
                    ProcessFltr = true,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = true,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = true,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_IC_ItemTransactionsNoCost).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_IC_ItemTransactionsNoCost",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_IC_ItemTransactionsNoCost : ResRptAr.rpt_IC_ItemTransactionsNoCost,
                    Description = "عرض تفصيلي لجميع الحركات التي تمت علي الصنف بدون تكلفة",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = true,
                    ProcessFltr = true,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = true,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = true,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_IC_ItemOpenInOutClose).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_IC_ItemOpenInOutClose",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_IC_ItemOpenInOutClose : ResRptAr.rpt_IC_ItemOpenInOutClose, //"تقرير وارد و صادر أصناف",
                    Description = "عرض تفصيلي لرصيد اول المدة والوارد والصادر ورصيد نهاية المدة للأصناف",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = true,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_IC_ItemOpenInOutCloseQty).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_IC_ItemOpenInOutCloseQty",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_IC_ItemOpenInOutCloseQty : ResRptAr.rpt_IC_ItemOpenInOutCloseQty,
                    Description = "تفاصيل لإجمالى حركات الصنف بالمخزن",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = true,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                }); if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_IC_Item_In_Out_Balance).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_IC_Item_In_Out_Balance",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_IC_Item_In_Out_Balance : ResRptAr.rpt_IC_Item_In_Out_Balance,
                    Description = "وارد وصادر عمليات الصنف بالمخزن",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = true,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_IC_SoldItemsCost).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_IC_SoldItemsCost",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_IC_SoldItemsCost : ResRptAr.rpt_IC_SoldItemsCost, //"تكلفة البضاعـــة المبـــاعة",
                    Description = "تكلفة البضاعـــة المبـــاعة",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = true,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });
            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_IC_SoldItemsandReturnCost).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_IC_SoldItemsandReturnCost",
                    ReportCaption = Shared.IsEnglish ? "Cost of sold and returned Items" : "تكلفة البضاعـــة المبـــاعة و المرتجعة", //"تكلفة البضاعـــة المبـــاعة",
                    Description = "تكلفة البضاعـــة المبـــاعة و المرتجعة",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = true,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });
            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_IC_ItemTransactionsDetails).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_IC_ItemTransactionsDetails",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_IC_ItemTransactionsDetails : ResRptAr.rpt_IC_ItemTransactionsDetails, //"جرد محتويات مخزن",
                    Description = "جرد محتويات مخزن",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.st_Store.ExpireDate == true)//mahmoud:expire, if expire available, show report
            {
                if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                    ||
                    (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                    Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_IC_ItemsExpired).Count() == 1))
                    lstReportListItem.Add(new reportListItem
                    {
                        ReportName = "rpt_IC_ItemsExpired",
                        ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_IC_ItemsExpired : ResRptAr.rpt_IC_ItemsExpired, //"أصناف تنتهي صلاحيتها",
                        Description = "أصناف تنتهي صلاحيتها",
                        TabIndex = 0,
                        ItemFltr = true,
                        DateFltr = false,
                        ExpDateFltr = true,
                        StoreFltr = true,
                        VendorFltr = false,
                        CustomerFltr = false,
                        CompanyFltr = true,
                        CategoryFltr = true,
                        ProcessFltr = false,
                        CostCenterFltr = false,
                        AccountFltr = false,
                        CustmAccListFltr = false,
                        UserFltr = false,
                        SalesEmpFltr = false,
                        BatchFltr = false,
                        CutGroupFltr = false,
                        MtrxFltr = false,
                        DimensionFltr = false,
                        JobOrderFltr = false,
                        ItemTypeFltr = false,
                        SellPriceFltr = false,
                        InvBookFltr = false,
                        EmpGroupFltr = false,
                        QcFltr = false,
                        VenGroupFltr = false
                    });
            }

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_IC_ItemsQtyDetails).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_IC_ItemsQtyDetails",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_IC_ItemsQtyDetails : ResRptAr.rpt_IC_ItemsQtyDetails,
                    Description = "تقرير أرصدة الأصناف تفصيلي",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = false,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = true,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_IC_ItemsTurnOver).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_IC_ItemsTurnOver",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.frm_IC_ItemsTurnOver : ResRptAr.frm_IC_ItemsTurnOver,
                    Description = "معدل دوران المخزون",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = true,
                    CategoryFltr = true,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = true,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_IC_ItemsQtyWithPrices).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_IC_ItemsQtyWithPricelevel",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_IC_ItemsQtyWithPrices : "قائمة اسعار مخزن", //"قائمة اسعار مخزن,
                    Description = "عرض قائمة اسعار مخزن",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = false,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });



            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_Ic_ItemsQty).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_IC_ItemsZeroQty",
                    ReportCaption = Shared.IsEnglish ? ResICEn.rpt_IC_ItemsZeroQty : ResICAr.rpt_IC_ItemsZeroQty, //"أرصدة الأصناف الصفرية",
                    Description = "عرض الأصناف الصفرية في المخازن",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = true,
                    CategoryFltr = true,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = true,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });


            this.lstBxReports.SelectedValueChanged -= new System.EventHandler(this.lstBxReports_SelectedValueChanged);
            lstBxReports.DataSource = lstReportListItem;
            lstBxReports.ValueMember = "ReportName";
            lstBxReports.DisplayMember = "ReportCaption";
            txtDescription.Text = string.Empty;
            lstBxReports.SelectedIndex = -1;

            ShowHideFilters(false, false, false, false, false, false, false, false, false,
                false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false);
            lstBxReports.Focus();
            this.lstBxReports.SelectedValueChanged += new System.EventHandler(this.lstBxReports_SelectedValueChanged);
        }

        private void NBI_SL_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            NbiAppearance("SL");
            lstReportListItem.Clear();

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
            ||
            (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
            Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_SL_InvoicesHeaders).Count() == 1))

                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_SL_InvoicesHeaders",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_SL_InvoicesHeaders : ResRptAr.rpt_SL_InvoicesHeaders, //"اجمالي فواتير المبيعات",
                    Description = "عرض اجماليات البيانات الرئيسيه لفواتير المبيعات",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = true,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = true,
                    BatchFltr = false,
                    CutGroupFltr = true,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = true,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });


            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
          ||
          (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
          Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_SL_E_Invoice).Count() == 1))

                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_SL_E_Invoice",
                    ReportCaption = Shared.IsEnglish ? " Sales E_Invoice" :"مبيعات الفواتير الالكترونية", //"اجمالي فواتير المبيعات",
                    Description = "مبيعات الفواتير الالكترونية",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = true,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = true,
                    BatchFltr = false,
                    CutGroupFltr = true,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = true,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });
            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
            ||
            (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
            Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_SL_ReturnHeaders).Count() == 1))

                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_SL_ReturnHeaders",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_SL_ReturnHeaders : ResRptAr.rpt_SL_ReturnHeaders, //"اجمالي فواتير مردود المبيعات",
                    Description = "عرض اجماليات البيانات الرئيسيه لفواتير مردود المبيعات",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = true,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = true,
                    BatchFltr = false,
                    CutGroupFltr = true,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = true,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

             if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
              ||
             (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
              Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_SubTaxNet).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_SubTaxNet",
                    ReportCaption = Shared.IsEnglish ? " Sub Tax Net" : "صافي الضرايب",
                    Description = "صافي الضرايب",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });
            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
              ||
              (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
               Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_SubTaxTotal).Count() == 1))
                  lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_SubTaxTotal",
                    ReportCaption = Shared.IsEnglish ? " Sub Tax Total" : "اجمالي مبيعات الضرايب",
                    Description = "اجمالي مبيعات الضرايب",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });
            //if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
            //||
            //(Shared.user.AccessType == (byte)AccessType.CustomAccess &&
            //Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_SL_ItemsSales).Count() == 1))

            //    lstReportListItem.Add(new reportListItem
            //    {
            //        ReportName = "rpt_SL_ItemsSales",
            //        ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_SL_ItemsSales : ResRptAr.rpt_SL_ItemsSales, //"اجمالي مبيعات صنف/أصناف",
            //        Description = "عرض اجمالي الكميه المباعه من الأصناف والرصيد الحالي",
            //        TabIndex = 0,
            //        ItemFltr = true,
            //        DateFltr = true,
            //        ExpDateFltr = false,
            //        StoreFltr = true,
            //        VendorFltr = false,
            //        CustomerFltr = false,
            //        CompanyFltr = true,
            //        CategoryFltr = true,
            //        ProcessFltr = false,
            //        CostCenterFltr = false,
            //        AccountFltr = false,
            //        CustmAccListFltr = false,
            //        UserFltr = false,
            //        SalesEmpFltr = true,
            //        BatchFltr = false,
            //        CutGroupFltr = false,
            //        MtrxFltr = false,
            //        DimensionFltr = false,
            //        JobOrderFltr = false,
            //        ItemTypeFltr = false,
            //        SellPriceFltr = false,
            //        InvBookFltr = true,
            //        EmpGroupFltr = false,
            //        QcFltr = false,
            //        VenGroupFltr = false
            //    });


            //if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
            //    ||
            //    (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
            //    Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_SL_ItemsReturn).Count() == 1))

            //    lstReportListItem.Add(new reportListItem
            //    {
            //        ReportName = "rpt_SL_ItemsReturn",
            //        ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_SL_ItemsReturn : ResRptAr.rpt_SL_ItemsReturn, //"اجمالي مردود مبيعات صنف/أصناف",
            //        Description = "عرض اجمالي كمية مردود مبيعات من الأصناف والرصيد الحالي",
            //        TabIndex = 0,
            //        ItemFltr = true,
            //        DateFltr = true,
            //        ExpDateFltr = false,
            //        StoreFltr = true,
            //        VendorFltr = false,
            //        CustomerFltr = false,
            //        CompanyFltr = true,
            //        CategoryFltr = true,
            //        ProcessFltr = false,
            //        CostCenterFltr = false,
            //        AccountFltr = false,
            //        CustmAccListFltr = false,
            //        UserFltr = false,
            //        SalesEmpFltr = true,
            //        BatchFltr = false,
            //        CutGroupFltr = false,
            //        MtrxFltr = false,
            //        DimensionFltr = false,
            //        JobOrderFltr = false,
            //        ItemTypeFltr = false,
            //        SellPriceFltr = false,
            //        InvBookFltr = true,
            //        EmpGroupFltr = false,
            //        QcFltr = false,
            //        VenGroupFltr = false
            //    });


            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
           ||
           (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
           Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_SL_CustomerTotal_Invoices).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_SL_CustomerTotal_Invoices",
                    ReportCaption = Shared.IsEnglish ? "Total Customers' Sales" : "اجمالي مبيعات العملاء",
                    Description = "عرض إجمالي مبيعات العملاء",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = true,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = true,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            //if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
            //||
            //(Shared.user.AccessType == (byte)AccessType.CustomAccess &&
            //Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_SL_ItemTrade).Count() == 1))

            //    lstReportListItem.Add(new reportListItem
            //    {
            //        ReportName = "rpt_SL_ItemTrade",
            //        ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_SL_ItemTrade : ResRptAr.rpt_SL_ItemTrade, // "ربح او خسارة صنف",
            //        Description = "عرض تقرير متاجرة الصنف، وحساب الربح او الخسارة",
            //        TabIndex = 0,
            //        ItemFltr = true,
            //        DateFltr = false,
            //        ExpDateFltr = false,
            //        StoreFltr = true,
            //        VendorFltr = false,
            //        CustomerFltr = false,
            //        CompanyFltr = false,
            //        CategoryFltr = false,
            //        ProcessFltr = false,
            //        CostCenterFltr = false,
            //        AccountFltr = false,
            //        CustmAccListFltr = false,
            //        UserFltr = false,
            //        SalesEmpFltr = false,
            //        BatchFltr = false,
            //        CutGroupFltr = false,
            //        MtrxFltr = false,
            //        DimensionFltr = false,
            //        JobOrderFltr = false,
            //        ItemTypeFltr = false,
            //        SellPriceFltr = false,
            //        InvBookFltr = false,
            //        EmpGroupFltr = false,
            //        QcFltr = false,
            //        VenGroupFltr = false
            //    });


            //Replaced by next report "Mahmoud Ismail 16-10-2014"
            //if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
            //    ||
            //    (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
            //    Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_Acc_SL_AccountDetails).Count() == 1))
            //    lstReportListItem.Add(new reportListItem
            //    {
            //        ReportName = "rpt_Acc_SL_AccountDetails",
            //        ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_Acc_SL_AccountDetails : ResRptAr.rpt_Acc_SL_AccountDetails, // "كشف حساب تفصيلي",
            //        Description = "كشف حساب تفصيلي، يعرض تفاصيل الفواتير أو أصناف الفواتير من ضمن التقرير",
            //        TabIndex = 0,
            //        ItemFltr = false,
            //        DateFltr = true,
            //        ExpDateFltr = false,
            //        StoreFltr = false,
            //        VendorFltr = false,
            //        CustomerFltr = true,
            //        ComapnyFltr = false,
            //        CategoryFltr = false,
            //        ProcessFltr = false,
            //        CostCenterFltr = false,
            //        AccountFltr = false,
            //        CustmAccListFltr = false,
            //        UserFltr = false,
            //        SalesEmpFltr = false,
            //        BatchFltr = false,
            //        CutGroupFltr = false,
            //        MtrxFltr = false,
            //        DimensionFltr = false,
            //        JobOrderFltr = false,
            //        ItemTypeFltr = false,
            //        SellPriceFltr = false,
            //        InvBookFltr = false,
            //        EmpGroupFltr = false,
            //        QcFltr = false
            //    });

           // if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
           //||
           //(Shared.user.AccessType == (byte)AccessType.CustomAccess &&
           //Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_frm_Acc_AccountDetails_Cust).Count() == 1))
           //     lstReportListItem.Add(new reportListItem
           //     {
           //         ReportName = "frm_Acc_AccountDetails",
           //         ReportCaption = Shared.IsEnglish ? ResRptEn.frm_Acc_AccountDetails : ResRptAr.frm_Acc_AccountDetails, // "كشف حساب تفصيلي",
           //         Description = "كشف حساب تفصيلي، يعرض تفاصيل الفواتير أو أصناف الفواتير من ضمن التقرير",
           //         TabIndex = 0,
           //         ItemFltr = false,
           //         DateFltr = true,
           //         ExpDateFltr = false,
           //         StoreFltr = false,
           //         VendorFltr = false,
           //         CustomerFltr = true,
           //         CompanyFltr = false,
           //         CategoryFltr = false,
           //         ProcessFltr = false,
           //         CostCenterFltr = false,
           //         AccountFltr = false,
           //         CustmAccListFltr = false,
           //         UserFltr = false,
           //         SalesEmpFltr = false,
           //         BatchFltr = false,
           //         CutGroupFltr = true,
           //         MtrxFltr = false,
           //         DimensionFltr = false,
           //         JobOrderFltr = false,
           //         ItemTypeFltr = false,
           //         SellPriceFltr = false,
           //         InvBookFltr = false,
           //         EmpGroupFltr = false,
           //         QcFltr = false,
           //         VenGroupFltr = false
           //     });

            //if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
            //    ||
            //    (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
            //    Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_Acc_SL_AccountsBalances).Count() == 1))
            //    lstReportListItem.Add(new reportListItem
            //    {
            //        ReportName = "rpt_Acc_SL_AccountsBalances",
            //        ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_Acc_SL_AccountsBalances : ResRptAr.rpt_Acc_SL_AccountsBalances, // "كشف اجمالى حسابات العملاء",
            //        Description = "يعرض اجمالي كشف الحساب لكل العملاء",
            //        TabIndex = 0,
            //        ItemFltr = false,
            //        DateFltr = true,
            //        ExpDateFltr = false,
            //        StoreFltr = false,
            //        VendorFltr = false,
            //        CustomerFltr = true,
            //        CompanyFltr = false,
            //        CategoryFltr = false,
            //        ProcessFltr = false,
            //        CostCenterFltr = false,
            //        AccountFltr = false,
            //        CustmAccListFltr = false,
            //        UserFltr = false,
            //        SalesEmpFltr = true,
            //        BatchFltr = false,
            //        CutGroupFltr = true,
            //        JobOrderFltr = false,
            //        ItemTypeFltr = false,
            //        SellPriceFltr = false,
            //        InvBookFltr = true,
            //        EmpGroupFltr = false,
            //        QcFltr = false,
            //        VenGroupFltr = false
            //    });

            //if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
            //||
            //(Shared.user.AccessType == (byte)AccessType.CustomAccess &&
            //Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_SL_Invoices_Due).Count() == 1))

            //    lstReportListItem.Add(new reportListItem
            //    {
            //        ReportName = "frm_SL_Invoices_Due",
            //        ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_SL_Invoices_Due : ResRptAr.rpt_SL_Invoices_Due, //"تاريخ استحقاق فواتير المبيعات",
            //        Description = "عرض تواريخ استحقاق فواتير المبيعات الآجلة",
            //        TabIndex = 0,
            //        ItemFltr = false,
            //        DateFltr = true,
            //        ExpDateFltr = false,
            //        StoreFltr = true,
            //        VendorFltr = false,
            //        CustomerFltr = true,
            //        CompanyFltr = false,
            //        CategoryFltr = false,
            //        ProcessFltr = false,
            //        CostCenterFltr = false,
            //        AccountFltr = false,
            //        CustmAccListFltr = false,
            //        UserFltr = false,
            //        SalesEmpFltr = true,
            //        BatchFltr = false,
            //        CutGroupFltr = true,
            //        MtrxFltr = false,
            //        DimensionFltr = false,
            //        JobOrderFltr = false,
            //        ItemTypeFltr = false,
            //        SellPriceFltr = false,
            //        InvBookFltr = true,
            //        EmpGroupFltr = false,
            //        QcFltr = false,
            //        VenGroupFltr = false
            //    });


            //if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
            //    ||
            //    (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
            //    Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_SL_Customer_Visits).Count() == 1))
            //    lstReportListItem.Add(new reportListItem
            //    {
            //        ReportName = "rpt_Acc_SL_CustomerVisits",
            //        ReportCaption = Shared.IsEnglish ? ResRptEn.CustomerVisits : ResRptAr.CustomerVisits, // "كشف زيارات العملاء",
            //        Description = "يعرض كشف زيارات العملاء",
            //        TabIndex = 0,
            //        ItemFltr = false,
            //        DateFltr = false,
            //        ExpDateFltr = false,
            //        StoreFltr = false,
            //        VendorFltr = false,
            //        CustomerFltr = true,
            //        CompanyFltr = false,
            //        CategoryFltr = false,
            //        ProcessFltr = false,
            //        CostCenterFltr = false,
            //        AccountFltr = false,
            //        CustmAccListFltr = false,
            //        UserFltr = false,
            //        SalesEmpFltr = true,
            //        BatchFltr = false,
            //        CutGroupFltr = false,
            //        JobOrderFltr = false,
            //        ItemTypeFltr = false,
            //        SellPriceFltr = false,
            //        InvBookFltr = false,
            //        EmpGroupFltr = false,
            //        QcFltr = false,
            //        VenGroupFltr = false
            //    });

            //if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
            //    ||
            //    (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
            //    Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_Acc_SL_AccountsBalancesWithNotes).Count() == 1))
            //    lstReportListItem.Add(new reportListItem
            //    {
            //        ReportName = "rpt_Acc_SL_AccountsBalancesWithNotes",
            //        ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_Acc_SL_AccountsBalancesWithNotes : ResRptAr.rpt_Acc_SL_AccountsBalancesWithNotes, //  كشف اجمالى حسابات العملاء و الأوراق
            //        Description = " يعرض كشف اجمالى حسابات العملاء بالأوراق التجارية",
            //        TabIndex = 0,
            //        ItemFltr = false,
            //        DateFltr = true,
            //        ExpDateFltr = false,
            //        StoreFltr = false,
            //        VendorFltr = false,
            //        CustomerFltr = true,
            //        CompanyFltr = false,
            //        CategoryFltr = false,
            //        ProcessFltr = false,
            //        CostCenterFltr = false,
            //        AccountFltr = false,
            //        CustmAccListFltr = false,
            //        UserFltr = false,
            //        SalesEmpFltr = true,
            //        BatchFltr = false,
            //        CutGroupFltr = true,
            //        JobOrderFltr = false,
            //        ItemTypeFltr = false,
            //        SellPriceFltr = false,
            //        InvBookFltr = false,
            //        EmpGroupFltr = false,
            //        QcFltr = false,
            //        VenGroupFltr = false
            //    });

            //if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
            //    ||
            //    (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
            //    Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_SL_CustomerDiscount).Count() == 1))
            //    lstReportListItem.Add(new reportListItem
            //    {
            //        ReportName = "rpt_SL_CustomerDiscount",
            //        ReportCaption = Shared.IsEnglish ? "Customer Discount Report" : "تقرير إجمالى خصومات العملاء", //"اجمالي خصومات العميل",
            //        Description = "يعرض خصومات العملاء",
            //        TabIndex = 0,
            //        ItemFltr = false,
            //        DateFltr = true,
            //        ExpDateFltr = false,
            //        StoreFltr = false,
            //        VendorFltr = false,
            //        CustomerFltr = true,
            //        CompanyFltr = false,
            //        CategoryFltr = false,
            //        ProcessFltr = false,
            //        CostCenterFltr = false,
            //        AccountFltr = false,
            //        CustmAccListFltr = false,
            //        UserFltr = false,
            //        SalesEmpFltr = true,
            //        BatchFltr = false,
            //        CutGroupFltr = true,
            //        MtrxFltr = false,
            //        DimensionFltr = false,
            //        JobOrderFltr = false,
            //        ItemTypeFltr = false,
            //        SellPriceFltr = false,
            //        InvBookFltr = true,
            //        EmpGroupFltr = false,
            //        QcFltr = false,
            //        VenGroupFltr = false
            //    });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_SL_CustomerItemsSales).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_SL_CustomerItemsSales",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_SL_CustomerItemsSales : ResRptAr.rpt_SL_CustomerItemsSales, //"اجمالي مبيعات صنف لعميل",
                    Description = "يعرض مبيعات الأصناف او مجموعة اصناف الى العملاء",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = true,
                    CompanyFltr = true,
                    CategoryFltr = true,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = true,
                    BatchFltr = false,
                    CutGroupFltr = true,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = true,
                    InvBookFltr = true,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_SL_CustomerItemsSalesReturn).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_SL_CustomerItemsSalesReturn",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_SL_CustomerItemsSalesReturn : ResRptAr.rpt_SL_CustomerItemsSalesReturn,
                    Description = "يعرض مردود مبيعات الأصناف او مجموعة اصناف من العملاء",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = true,
                    CompanyFltr = true,
                    CategoryFltr = true,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = true,
                    BatchFltr = false,
                    CutGroupFltr = true,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = true,
                    InvBookFltr = true,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            //if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
            //    ||
            //    (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
            //    Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_SL_CustomerTrans).Count() == 1))
            //    lstReportListItem.Add(new reportListItem
            //    {
            //        ReportName = "rpt_SL_CustomerTrans",
            //        ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_SL_CustomerTrans : ResRptAr.rpt_SL_CustomerTrans,
            //        Description = "يعرض بيان بجميع العمليات التي تمت مع عميل",
            //        TabIndex = 0,
            //        ItemFltr = false,
            //        DateFltr = true,
            //        ExpDateFltr = false,
            //        StoreFltr = false,
            //        VendorFltr = false,
            //        CustomerFltr = true,
            //        CompanyFltr = false,
            //        CategoryFltr = false,
            //        ProcessFltr = false,
            //        CostCenterFltr = false,
            //        AccountFltr = false,
            //        CustmAccListFltr = false,
            //        UserFltr = false,
            //        SalesEmpFltr = false,
            //        BatchFltr = false,
            //        CutGroupFltr = false,
            //        MtrxFltr = false,
            //        DimensionFltr = false,
            //        JobOrderFltr = false,
            //        ItemTypeFltr = false,
            //        SellPriceFltr = false,
            //        InvBookFltr = false,
            //        EmpGroupFltr = false,
            //        QcFltr = false,
            //        VenGroupFltr = false
            //    });

            //if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
            //    ||
            //    (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
            //    Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_SL_CustomerItemsSales_OutTrns).Count() == 1))
            //    lstReportListItem.Add(new reportListItem
            //    {
            //        ReportName = "rpt_SL_CustomerItemsSales_OutTrns",
            //        ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_SL_CustomerItemsSales_OutTrns : ResRptAr.rpt_SL_CustomerItemsSales_OutTrns,
            //        Description = "يعرض بيان بالمبيعات وتكلفتها",
            //        TabIndex = 0,
            //        ItemFltr = true,
            //        DateFltr = true,
            //        ExpDateFltr = false,
            //        StoreFltr = true,
            //        VendorFltr = false,
            //        CustomerFltr = true,
            //        CompanyFltr = true,
            //        CategoryFltr = false,
            //        ProcessFltr = false,
            //        CostCenterFltr = false,
            //        AccountFltr = false,
            //        CustmAccListFltr = false,
            //        UserFltr = false,
            //        SalesEmpFltr = false,
            //        BatchFltr = false,
            //        CutGroupFltr = true,
            //        JobOrderFltr = false,
            //        ItemTypeFltr = false,
            //        SellPriceFltr = false,
            //        InvBookFltr = false,
            //        EmpGroupFltr = false,
            //        QcFltr = false,
            //        VenGroupFltr = false
            //    });

            //if (Shared.SalesOrderAvailable)
            //{
            //    if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
            //            ||
            //            (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
            //            Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_OrderAndAchievement_Cust).Count() == 1))
            //        lstReportListItem.Add(new reportListItem
            //        {
            //            ReportName = "frm_OrderAndAchievement_Cust",
            //            ReportCaption = Shared.IsEnglish ? ResRptEn.frm_SalesOrderAndAchievement : ResRptAr.frm_SalesOrderAndAchievement,
            //            Description = "بيان بأوامر التوريد ونسب التحقيق من الفواتير",
            //            TabIndex = 0,
            //            ItemFltr = true,
            //            DateFltr = true,
            //            ExpDateFltr = false,
            //            StoreFltr = true,
            //            VendorFltr = false,
            //            CustomerFltr = true,
            //            CompanyFltr = true,
            //            CategoryFltr = true,
            //            ProcessFltr = false,
            //            CostCenterFltr = false,
            //            AccountFltr = false,
            //            CustmAccListFltr = false,
            //            UserFltr = false,
            //            SalesEmpFltr = true,
            //            BatchFltr = false,
            //            CutGroupFltr = true,
            //            JobOrderFltr = false,
            //            ItemTypeFltr = false,
            //            SellPriceFltr = false,
            //            InvBookFltr = true,
            //            EmpGroupFltr = false,
            //            QcFltr = false,
            //            VenGroupFltr = false
            //        });
            //}

            //if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
            //    ||
            //    (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
            //    Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_SL_Warranty).Count() == 1))
            //    lstReportListItem.Add(new reportListItem
            //    {
            //        ReportName = "frm_SL_Warranty",
            //        ReportCaption = Shared.IsEnglish ? ResRptEn.frm_SL_Warranty : ResRptAr.frm_SL_Warranty,
            //        Description = "مواعيد انتهاء ضمان الأصناف المباعة",
            //        TabIndex = 0,
            //        ItemFltr = true,
            //        DateFltr = true,
            //        ExpDateFltr = false,
            //        StoreFltr = true,
            //        VendorFltr = false,
            //        CustomerFltr = true,
            //        CompanyFltr = true,
            //        CategoryFltr = true,
            //        ProcessFltr = false,
            //        CostCenterFltr = false,
            //        AccountFltr = false,
            //        CustmAccListFltr = false,
            //        UserFltr = false,
            //        SalesEmpFltr = true,
            //        BatchFltr = false,
            //        CutGroupFltr = true,
            //        MtrxFltr = false,
            //        DimensionFltr = false,
            //        JobOrderFltr = false,
            //        ItemTypeFltr = false,
            //        SellPriceFltr = false,
            //        InvBookFltr = true,
            //        EmpGroupFltr = false,
            //        QcFltr = false,
            //        VenGroupFltr = false
            //    });

            //if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
            //               ||
            //               (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
            //               Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_SL_Car_Weights).Count() == 1))
            //    lstReportListItem.Add(new reportListItem
            //    {
            //        ReportName = "frm_SL_Car_Weights",
            //        ReportCaption = Shared.IsEnglish ? ResRptEn.frm_SL_Car_Weights : ResRptAr.frm_SL_Car_Weights,
            //        Description = "نقلات السيارات",
            //        TabIndex = 0,
            //        ItemFltr = true,
            //        DateFltr = true,
            //        ExpDateFltr = false,
            //        StoreFltr = true,
            //        VendorFltr = false,
            //        CustomerFltr = true,
            //        CompanyFltr = true,
            //        CategoryFltr = true,
            //        ProcessFltr = false,
            //        CostCenterFltr = false,
            //        AccountFltr = false,
            //        CustmAccListFltr = false,
            //        UserFltr = false,
            //        SalesEmpFltr = true,
            //        BatchFltr = false,
            //        CutGroupFltr = true,
            //        MtrxFltr = false,
            //        DimensionFltr = false,
            //        JobOrderFltr = false,
            //        ItemTypeFltr = false,
            //        SellPriceFltr = false,
            //        InvBookFltr = true,
            //        EmpGroupFltr = false,
            //        QcFltr = false,
            //        VenGroupFltr = false,
            //        CarsFltr = true
            //    });

       //     if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
       //||
       //(Shared.user.AccessType == (byte)AccessType.CustomAccess &&
       //Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_SL_DelegatesSales).Count() == 1))
       //     {
       //         lstReportListItem.Add(new reportListItem
       //         {
       //             ReportName = "frm_SL_DelegatesSales",
       //             ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_DelegatesSales : ResRptAr.rpt_DelegatesSales,
       //             Description = "تفرير مبيعات المندوبين",
       //             TabIndex = 0,
       //             ItemFltr = false,
       //             DateFltr = true,
       //             ExpDateFltr = false,
       //             StoreFltr = false,
       //             VendorFltr = false,
       //             CustomerFltr = false,
       //             CompanyFltr = false,
       //             CategoryFltr = false,
       //             ProcessFltr = false,
       //             CostCenterFltr = false,
       //             AccountFltr = false,
       //             CustmAccListFltr = false,
       //             UserFltr = false,
       //             SalesEmpFltr = true,
       //             BatchFltr = false,
       //             CutGroupFltr = false,
       //             MtrxFltr = false,
       //             DimensionFltr = false,
       //             JobOrderFltr = false,
       //             ItemTypeFltr = false,
       //             SellPriceFltr = false,
       //             InvBookFltr = false,
       //             EmpGroupFltr = false,
       //             QcFltr = false,
       //             VenGroupFltr = false


       //         });
       //         //    salesEmp = true;
       //     }


 //           if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
 //||
 //(Shared.user.AccessType == (byte)AccessType.CustomAccess &&
 //Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_SL_DeliveryOfficialsSales).Count() == 1))
 //           {
 //               lstReportListItem.Add(new reportListItem
 //               {
 //                   ReportName = "frm_SL_DeliveryOfficialsSales",
 //                   ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_SL_DeliveryOfficialsSales : ResRptAr.rpt_SL_DeliveryOfficialsSales,
 //                   Description = "تفرير مبيعات مسؤولي التسليم",
 //                   TabIndex = 0,
 //                   ItemFltr = false,
 //                   DateFltr = true,
 //                   ExpDateFltr = false,
 //                   StoreFltr = false,
 //                   VendorFltr = false,
 //                   CustomerFltr = false,
 //                   CompanyFltr = false,
 //                   CategoryFltr = false,
 //                   ProcessFltr = false,
 //                   CostCenterFltr = false,
 //                   AccountFltr = false,
 //                   CustmAccListFltr = false,
 //                   UserFltr = false,
 //                   SalesEmpFltr = true,
 //                   BatchFltr = false,
 //                   CutGroupFltr = false,
 //                   MtrxFltr = false,
 //                   DimensionFltr = false,
 //                   JobOrderFltr = false,
 //                   ItemTypeFltr = false,
 //                   SellPriceFltr = false,
 //                   InvBookFltr = false,
 //                   EmpGroupFltr = false,
 //                   QcFltr = false,
 //                   VenGroupFltr = false


 //               });
 //               //deliverEmp = true;
 //           }




   //         if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
   //||
   //(Shared.user.AccessType == (byte)AccessType.CustomAccess &&
   //Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_CapitalInDeferredInvoices).Count() == 1))
   //             lstReportListItem.Add(new reportListItem
   //             {
   //                 ReportName = "frm_CapitalInDeferredInvoices",
   //                 ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_PercentageOfCapitalInDeferredInvoices : ResRptAr.rpt_PercentageOfCapitalInDeferredInvoices,
   //                 Description = "نسبة راس المال في الفواتير المؤجلة",
   //                 TabIndex = 0,
   //                 ItemFltr = false,
   //                 DateFltr = true,
   //                 ExpDateFltr = false,
   //                 StoreFltr = false,
   //                 VendorFltr = false,
   //                 CustomerFltr = true,
   //                 CompanyFltr = false,
   //                 CategoryFltr = false,
   //                 ProcessFltr = false,
   //                 CostCenterFltr = false,
   //                 AccountFltr = false,
   //                 CustmAccListFltr = false,
   //                 UserFltr = false,
   //                 SalesEmpFltr = false,
   //                 BatchFltr = false,
   //                 CutGroupFltr = false,
   //                 MtrxFltr = false,
   //                 DimensionFltr = false,
   //                 JobOrderFltr = false,
   //                 ItemTypeFltr = false,
   //                 SellPriceFltr = false,
   //                 InvBookFltr = false,
   //                 EmpGroupFltr = false,
   //                 QcFltr = false,
   //                 VenGroupFltr = false


   //             });


   //         if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
   //||
   //(Shared.user.AccessType == (byte)AccessType.CustomAccess &&
   //Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_SL_Profit_Loss).Count() == 1))
   //             lstReportListItem.Add(new reportListItem
   //             {
   //                 ReportName = "frm_SL_Profit_Loss",
   //                 ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_SL_Profit_Loss : ResRptAr.rpt_SL_Profit_Loss,
   //                 Description = "ربح أو خسارة فواتير البيع",
   //                 TabIndex = 0,
   //                 ItemFltr = false,
   //                 DateFltr = true,
   //                 ExpDateFltr = false,
   //                 StoreFltr = true,
   //                 VendorFltr = false,
   //                 CustomerFltr = true,
   //                 CompanyFltr = false,
   //                 CategoryFltr = false,
   //                 ProcessFltr = false,
   //                 CostCenterFltr = false,
   //                 AccountFltr = false,
   //                 CustmAccListFltr = false,
   //                 UserFltr = false,
   //                 SalesEmpFltr = true,
   //                 BatchFltr = false,
   //                 CutGroupFltr = true,
   //                 MtrxFltr = false,
   //                 DimensionFltr = false,
   //                 JobOrderFltr = false,
   //                 ItemTypeFltr = false,
   //                 SellPriceFltr = false,
   //                 InvBookFltr = false,
   //                 EmpGroupFltr = false,
   //                 QcFltr = false,
   //                 VenGroupFltr = false


   //             });

            this.lstBxReports.SelectedValueChanged -= new System.EventHandler(this.lstBxReports_SelectedValueChanged);
            lstBxReports.DataSource = lstReportListItem;
            lstBxReports.ValueMember = "ReportName";
            lstBxReports.DisplayMember = "ReportCaption";
            txtDescription.Text = string.Empty;
            lstBxReports.SelectedIndex = -1;
            ShowHideFilters(false, false, false, false, false, false, false, false, false,
                false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false);
            //    DAL.MyHelper.GetSalesEmps(dtSalesEmp, deliverEmp, salesEmp, Shared.user.DefaultSalesRep);

            //if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
            //   ||
            //   (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
            //   Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_SL_averagesellingpriceoftheitems).Count() == 1))
            //    lstReportListItem.Add(new reportListItem
            //    {
            //        ReportName = "rpt_SL_averagesellingpriceoftheitems",
            //        ReportCaption = Shared.IsEnglish ? "The average selling price of the items" : "متوسط سعر البيع للاصناف", //"أرصدة الأصناف",
            //        Description = "متوسط سعر البيع للاصناف",
            //        TabIndex = 0,
            //        ItemFltr = true,
            //        DateFltr = true,
            //        ExpDateFltr = false,
            //        StoreFltr = true,
            //        VendorFltr = false,
            //        CustomerFltr = false,
            //        CompanyFltr = true,
            //        CategoryFltr = true,
            //        ProcessFltr = false,
            //        CostCenterFltr = false,
            //        AccountFltr = false,
            //        CustmAccListFltr = false,
            //        UserFltr = false,
            //        SalesEmpFltr = false,
            //        BatchFltr = false,
            //        CutGroupFltr = false,
            //        MtrxFltr = false,
            //        DimensionFltr = false,
            //        JobOrderFltr = false,
            //        ItemTypeFltr = false,
            //        SellPriceFltr = false,
            //        InvBookFltr = false,
            //        EmpGroupFltr = false,
            //        QcFltr = false,
            //        VenGroupFltr = false
            //    });

            //if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
            //              ||
            //              (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
            //              Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_Customers_Debit).Count() == 1))
            //    lstReportListItem.Add(new reportListItem
            //    {
            //        ReportName = "frm_Customers_Debit",
            //        ReportCaption = Shared.IsEnglish ? ResRptEn.frm_Customers_Debit : ResRptAr.frm_Customers_Debit, // "كشف اجمالى حسابات العملاء",
            //        Description = "يعرض إجمالي مديونية العملاء الذين تم إصدار فواتير لهم عن طريق المندوب",
            //        TabIndex = 0,
            //        ItemFltr = false,
            //        DateFltr = true,
            //        ExpDateFltr = false,
            //        StoreFltr = false,
            //        VendorFltr = false,
            //        CustomerFltr = true,
            //        CompanyFltr = false,
            //        CategoryFltr = false,
            //        ProcessFltr = false,
            //        CostCenterFltr = false,
            //        AccountFltr = false,
            //        CustmAccListFltr = false,
            //        UserFltr = false,
            //        SalesEmpFltr = true,
            //        BatchFltr = false,
            //        CutGroupFltr = true,
            //        JobOrderFltr = false,
            //        ItemTypeFltr = false,
            //        SellPriceFltr = false,
            //        InvBookFltr = false,
            //        EmpGroupFltr = false,
            //        QcFltr = false,
            //        VenGroupFltr = false
            //    });
            //if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
            //    ||
            //    (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
            //    Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_SL_JobOrderInv).Count() == 1))
            //    lstReportListItem.Add(new reportListItem
            //    {
            //        ReportName = "frm_SL_JobOrderInv",
            //        ReportCaption = Shared.IsEnglish ? ResRptEn.frm_SL_JobOrderInv : ResRptAr.frm_SL_JobOrderInv,
            //        Description = "بيان بمبيعات تمت لأوامر عمل",
            //        TabIndex = 0,
            //        ItemFltr = true,
            //        DateFltr = true,
            //        ExpDateFltr = false,
            //        StoreFltr = true,
            //        VendorFltr = false,
            //        CustomerFltr = false,
            //        CompanyFltr = true,
            //        CategoryFltr = true,
            //        ProcessFltr = false,
            //        CostCenterFltr = false,
            //        AccountFltr = false,
            //        CustmAccListFltr = false,
            //        UserFltr = false,
            //        SalesEmpFltr = true,
            //        BatchFltr = false,
            //        CutGroupFltr = false,
            //        MtrxFltr = false,
            //        DimensionFltr = false,
            //        JobOrderFltr = true,
            //        ItemTypeFltr = true,
            //        SellPriceFltr = false,
            //        InvBookFltr = false,
            //        EmpGroupFltr = false,
            //        QcFltr = false,
            //        VenGroupFltr = false
            //    });

            //if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
            //    ||
            //    (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
            //    Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_frm_SL_SalesOrderItems).Count() == 1))
            //    lstReportListItem.Add(new reportListItem
            //    {
            //        ReportName = "frm_SL_SalesOrderItems",
            //        ReportCaption = Shared.IsEnglish ? ResRptEn.frm_SL_SalesOrderItems : ResRptAr.frm_SL_SalesOrderItems,
            //        Description = "أصناف محجوزة بأوامر بيع لعملاء",
            //        TabIndex = 0,
            //        ItemFltr = true,
            //        DateFltr = true,
            //        ExpDateFltr = false,
            //        StoreFltr = true,
            //        VendorFltr = false,
            //        CustomerFltr = true,
            //        CompanyFltr = false,
            //        CategoryFltr = false,
            //        ProcessFltr = false,
            //        CostCenterFltr = false,
            //        AccountFltr = false,
            //        CustmAccListFltr = false,
            //        UserFltr = false,
            //        SalesEmpFltr = false,
            //        BatchFltr = false,
            //        CutGroupFltr = false,
            //        MtrxFltr = true,
            //        DimensionFltr = true,
            //        JobOrderFltr = false,
            //        ItemTypeFltr = false,
            //        SellPriceFltr = false,
            //        InvBookFltr = false,
            //        EmpGroupFltr = false,
            //        QcFltr = false,
            //        VenGroupFltr = false
            //    });

            //if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
            //    ||
            //    (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
            //    Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_frm_SL_SalesOrderItemsAndBalance).Count() == 1))
            //    lstReportListItem.Add(new reportListItem
            //    {
            //        ReportName = "frm_SL_SalesOrderItemsAndBalance",
            //        ReportCaption = Shared.IsEnglish ? ResRptEn.frm_SL_SalesOrderItemsAndBalance : ResRptAr.frm_SL_SalesOrderItemsAndBalance,
            //        Description = "أصناف أوامر بيع بالأرصدةالحالية ",
            //        TabIndex = 0,
            //        ItemFltr = true,
            //        DateFltr = true,
            //        ExpDateFltr = false,
            //        StoreFltr = true,
            //        VendorFltr = false,
            //        CustomerFltr = false,
            //        CompanyFltr = false,
            //        CategoryFltr = false,
            //        ProcessFltr = false,
            //        CostCenterFltr = false,
            //        AccountFltr = false,
            //        CustmAccListFltr = false,
            //        UserFltr = false,
            //        SalesEmpFltr = false,
            //        BatchFltr = false,
            //        CutGroupFltr = false,
            //        MtrxFltr = false,
            //        DimensionFltr = false,
            //        JobOrderFltr = false,
            //        ItemTypeFltr = false,
            //        SellPriceFltr = false,
            //        InvBookFltr = false,
            //        EmpGroupFltr = false,
            //        QcFltr = false,
            //        VenGroupFltr = false
            //    });

            //if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
            //    ||
            //    (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
            //    Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_SL_ItemsSalesDetails).Count() == 1))
            //    lstReportListItem.Add(new reportListItem
            //    {
            //        ReportName = "frm_SL_ItemsSalesDetails",
            //        ReportCaption = Shared.IsEnglish ? ResRptEn.frm_SL_ItemsSalesDetails : ResRptAr.frm_SL_ItemsSalesDetails,
            //        Description = "تقرير مييعات ومردود مبيعات على مستوى الصنف، يعرض قيمة البيع وصافي البيع ",
            //        TabIndex = 0,
            //        ItemFltr = true,
            //        DateFltr = true,
            //        ExpDateFltr = false,
            //        StoreFltr = true,
            //        VendorFltr = false,
            //        CustomerFltr = true,
            //        CompanyFltr = true,
            //        CategoryFltr = true,
            //        ProcessFltr = false,
            //        CostCenterFltr = false,
            //        AccountFltr = false,
            //        CustmAccListFltr = false,
            //        UserFltr = false,
            //        SalesEmpFltr = true,
            //        BatchFltr = false,
            //        CutGroupFltr = true,
            //        MtrxFltr = false,
            //        DimensionFltr = false,
            //        JobOrderFltr = false,
            //        ItemTypeFltr = false,
            //        SellPriceFltr = false,
            //        InvBookFltr = true,
            //        EmpGroupFltr = true,
            //        QcFltr = false,
            //        VenGroupFltr = false
            //    });

            //if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
            //    ||
            //    (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
            //    Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_SL_ItemsNetSalesDetails).Count() == 1))
            //    lstReportListItem.Add(new reportListItem
            //    {
            //        ReportName = "frm_SL_ItemsNetSalesDetails",
            //        ReportCaption = Shared.IsEnglish ? ResRptEn.frm_SL_ItemsNetSalesDetails : ResRptAr.frm_SL_ItemsNetSalesDetails,
            //        Description = "تقرير صافي مبيعات ومردود مبيعات",
            //        TabIndex = 0,
            //        ItemFltr = false,
            //        DateFltr = true,
            //        ExpDateFltr = false,
            //        StoreFltr = true,
            //        VendorFltr = false,
            //        CustomerFltr = true,
            //        CompanyFltr = false,
            //        CategoryFltr = false,
            //        ProcessFltr = false,
            //        CostCenterFltr = false,
            //        AccountFltr = false,
            //        CustmAccListFltr = false,
            //        UserFltr = false,
            //        SalesEmpFltr = true,
            //        BatchFltr = false,
            //        CutGroupFltr = true,
            //        MtrxFltr = false,
            //        DimensionFltr = false,
            //        JobOrderFltr = false,
            //        ItemTypeFltr = false,
            //        SellPriceFltr = false,
            //        InvBookFltr = true,
            //        EmpGroupFltr = true,
            //        QcFltr = false,
            //        VenGroupFltr = false
            //    });

          //  if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
          //      ||
          //      (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
          //      Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_SL_ItemsSalesTotals).Count() == 1))
          //      lstReportListItem.Add(new reportListItem
          //      {
          //          ReportName = "frm_SL_ItemsSalesTotals",
          //          ReportCaption = Shared.IsEnglish ? ResRptEn.frm_SL_ItemsSalesTotals : ResRptAr.frm_SL_ItemsSalesTotals,
          //          Description = "تقرير اجمالي مبيعات الأصناف من اجمالي المبيعات و ضرائب المبيعات و الخصومات والاجماليات",
          //          TabIndex = 0,
          //          ItemFltr = true,
          //          DateFltr = true,
          //          ExpDateFltr = false,
          //          StoreFltr = true,
          //          VendorFltr = false,
          //          CustomerFltr = true,
          //          CompanyFltr = true,
          //          CategoryFltr = true,
          //          ProcessFltr = false,
          //          CostCenterFltr = false,
          //          AccountFltr = false,
          //          CustmAccListFltr = false,
          //          UserFltr = false,
          //          SalesEmpFltr = true,
          //          BatchFltr = false,
          //          CutGroupFltr = true,
          //          MtrxFltr = false,
          //          DimensionFltr = false,
          //          JobOrderFltr = false,
          //          ItemTypeFltr = false,
          //          SellPriceFltr = false,
          //          InvBookFltr = true,
          //          EmpGroupFltr = true,
          //          QcFltr = false,
          //          VenGroupFltr = false
          //      });

          //  if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
          //     ||
          //     (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
          //     Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_SL_CustomerItemsSalesReturns).Count() == 1))
          //      lstReportListItem.Add(new reportListItem
          //      {
          //          ReportName = "frm_SL_CustomerItemsSalesReturns",
          //          ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_SL_CustomerItemsSalesReturns : ResRptAr.rpt_SL_CustomerItemsSalesReturns, //"اجمالي مبيعات و مردودات الأصناف لعملاء",
          //          Description = "يعرض مبيعات و مردودات الأصناف او مجموعة اصناف تفصيلي",
          //          TabIndex = 0,
          //          ItemFltr = true,
          //          DateFltr = true,
          //          ExpDateFltr = false,
          //          StoreFltr = true,
          //          VendorFltr = false,
          //          CustomerFltr = true,
          //          CompanyFltr = true,
          //          CategoryFltr = true,
          //          ProcessFltr = false,
          //          CostCenterFltr = false,
          //          AccountFltr = false,
          //          CustmAccListFltr = false,
          //          UserFltr = false,
          //          SalesEmpFltr = true,
          //          BatchFltr = false,
          //          CutGroupFltr = true,
          //          MtrxFltr = false,
          //          DimensionFltr = false,
          //          JobOrderFltr = false,
          //          ItemTypeFltr = false,
          //          SellPriceFltr = true,
          //          InvBookFltr = true,
          //          EmpGroupFltr = false,
          //          QcFltr = false,
          //          VenGroupFltr = false
          //      });

          //  if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
          // ||
          // (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
          // Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_IC_StoreSales).Count() == 1))
          //      lstReportListItem.Add(new reportListItem
          //      {
          //          ReportName = "frm_IC_StoreSales",
          //          ReportCaption = Shared.IsEnglish ? "Total Stores' Sales" : "اجمالي مبيعات المخازن",
          //          Description = "عرض إجمالي مبيعات المخازن",
          //          TabIndex = 0,
          //          ItemFltr = false,
          //          DateFltr = true,
          //          ExpDateFltr = false,
          //          StoreFltr = true,
          //          VendorFltr = false,
          //          CustomerFltr = false,
          //          CompanyFltr = false,
          //          CategoryFltr = false,
          //          ProcessFltr = false,
          //          CostCenterFltr = false,
          //          AccountFltr = false,
          //          CustmAccListFltr = false,
          //          UserFltr = false,
          //          SalesEmpFltr = false,
          //          BatchFltr = false,
          //          CutGroupFltr = true,
          //          MtrxFltr = false,
          //          DimensionFltr = false,
          //          JobOrderFltr = false,
          //          ItemTypeFltr = false,
          //          SellPriceFltr = false,
          //          InvBookFltr = false,
          //          EmpGroupFltr = false,
          //          QcFltr = false,
          //          VenGroupFltr = false
          //      });

          //  if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
          //      ||
          //      (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
          //      Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_SL_CustomerGroupItemsNet).Count() == 1))
          //      lstReportListItem.Add(new reportListItem
          //      {
          //          ReportName = "frm_SL_CustomerGroupItemsNet",
          //          ReportCaption = Shared.IsEnglish ? "Net items' sales per customer group - Pivot" : "صافي مبيعات الاصناف لمجموعات العملاء",
          //          Description = "تقرير مييعات ومردود مبيعات على حسب مجموعات العملاء ",
          //          TabIndex = 0,
          //          ItemFltr = true,
          //          DateFltr = true,
          //          ExpDateFltr = false,
          //          StoreFltr = true,
          //          VendorFltr = false,
          //          CustomerFltr = true,
          //          CompanyFltr = true,
          //          CategoryFltr = true,
          //          ProcessFltr = false,
          //          CostCenterFltr = false,
          //          AccountFltr = false,
          //          CustmAccListFltr = false,
          //          UserFltr = false,
          //          SalesEmpFltr = true,
          //          BatchFltr = false,
          //          CutGroupFltr = true,
          //          MtrxFltr = false,
          //          DimensionFltr = false,
          //          JobOrderFltr = false,
          //          ItemTypeFltr = false,
          //          SellPriceFltr = false,
          //          InvBookFltr = true,
          //          EmpGroupFltr = true,
          //          QcFltr = false,
          //          VenGroupFltr = false
          //      });


          //  if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
          //  ||
          //  (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
          //  Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_SL_CustomerInvoicesHeaders).Count() == 1))

          //      lstReportListItem.Add(new reportListItem
          //      {
          //          ReportName = "rpt_SL_CustomerInvoicesHeaders",
          //          ReportCaption = Shared.IsEnglish ? "Total Sales Info. for Customers" : "تقرير إجماليات عمليات البيع للعملاء", //"تقرير إجماليات عمليات البيع للعملاء",
          //          Description = " عرض  المبيعات للعملاء",
          //          TabIndex = 0,
          //          ItemFltr = false,
          //          DateFltr = true,
          //          ExpDateFltr = false,
          //          StoreFltr = true,
          //          VendorFltr = false,
          //          CustomerFltr = true,
          //          CompanyFltr = false,
          //          CategoryFltr = false,
          //          ProcessFltr = false,
          //          CostCenterFltr = false,
          //          AccountFltr = false,
          //          CustmAccListFltr = false,
          //          UserFltr = false,
          //          SalesEmpFltr = true,
          //          BatchFltr = false,
          //          CutGroupFltr = true,
          //          MtrxFltr = false,
          //          DimensionFltr = false,
          //          JobOrderFltr = false,
          //          ItemTypeFltr = false,
          //          SellPriceFltr = false,
          //          InvBookFltr = true,
          //          EmpGroupFltr = false,
          //          QcFltr = false,
          //          VenGroupFltr = false
          //      });
          //  if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
          //||
          //(Shared.user.AccessType == (byte)AccessType.CustomAccess &&
          //Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_InvoiceDetails).Count() == 1))

          //      lstReportListItem.Add(new reportListItem
          //      {
          //          ReportName = "rpt_InvoiceDetails",
          //          ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_InvoiceDetails : ResRptAr.rpt_InvoiceDetails, //"اجمالي فواتير المبيعات",
          //          Description = "عرض تفاصيل الفواتير",
          //          TabIndex = 0,
          //          ItemFltr = false,
          //          DateFltr = true,
          //          ExpDateFltr = false,
          //          StoreFltr = true,
          //          VendorFltr = false,
          //          CustomerFltr = true,
          //          CompanyFltr = false,
          //          CategoryFltr = false,
          //          ProcessFltr = false,
          //          CostCenterFltr = false,
          //          AccountFltr = false,
          //          CustmAccListFltr = false,
          //          UserFltr = false,
          //          SalesEmpFltr = true,
          //          BatchFltr = false,
          //          CutGroupFltr = true,
          //          MtrxFltr = false,
          //          DimensionFltr = false,
          //          JobOrderFltr = false,
          //          ItemTypeFltr = false,
          //          SellPriceFltr = false,
          //          InvBookFltr = true,
          //          EmpGroupFltr = false,
          //          QcFltr = false,
          //          VenGroupFltr = false
          //      });
            lstBxReports.Focus();
            this.lstBxReports.SelectedValueChanged += new System.EventHandler(this.lstBxReports_SelectedValueChanged);
        }

        private void NBI_PR_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            NbiAppearance("PR");
            lstReportListItem.Clear();

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_PR_InvoicesHeaders).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_PR_InvoicesHeaders",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_PR_InvoicesHeaders : ResRptAr.rpt_PR_InvoicesHeaders, //"اجمالي فواتير المشتريات",
                    Description = "عرض اجماليات البيانات الرئيسيه لفواتير المشتريات",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = true,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = true,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = true
                });
            /// new new
            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
               ||
               (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
               Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_PR_InvoicesDiscountTaxHeaders).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_PR_InvoicesDiscountTaxHeaders",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_PR_InvoicesDiscountTaxHeaders : ResRptAr.rpt_PR_InvoicesDiscountTaxHeaders, //"اجمالي فواتير المشتريات",
                    Description = " عرض اجماليات البيانات الرئيسيه لفواتير المشتريات قبل ضريبة الخصم",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = true,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = true,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = true
                });



            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_PR_ReturnHeaders).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_PR_ReturnHeaders",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_PR_ReturnHeaders : ResRptAr.rpt_PR_ReturnHeaders, //"اجمالي فواتير مردود المشتريات",
                    Description = "عرض اجماليات البيانات الرئيسيه لفواتير مردود المشتريات",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = true,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = true,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = true
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_PR_ItemsPurchases).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_PR_ItemsPurchases",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_PR_ItemsPurchases : ResRptAr.rpt_PR_ItemsPurchases, //"اجمالي مشتريات صنف/أصناف",
                    Description = "عرض اجمالي الكميه المشتراه من الأصناف وأرصدتها الحالية",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = true,
                    CustomerFltr = false,
                    CompanyFltr = true,
                    CategoryFltr = true,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = true,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_PR_ItemsReturns).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_PR_ItemsReturns",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_PR_ItemsReturns : ResRptAr.rpt_PR_ItemsReturns, //"اجمالي مردود مشتريات صنف/أصناف",
                    Description = "عرض اجمالي كمية مردود شراء الأصناف والرصيد الحالي",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = true,
                    CustomerFltr = false,
                    CompanyFltr = true,
                    CategoryFltr = true,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = true,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_ItemPriceChangings).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_ItemPriceChangings",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_ItemPriceChangings : ResRptAr.rpt_ItemPriceChangings, //"تغييرات أسعار الاصناف",
                    Description = "عرض تغيرات الاسعار التي حدثت علي صنف او مجموعة اصناف",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });
            //Replaced by next report "Mahmoud Ismail 16-10-2014"
            //if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
            //    ||
            //    (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
            //    Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_Acc_PR_AccountDetails).Count() == 1))
            //    lstReportListItem.Add(new reportListItem
            //    {
            //        ReportName = "rpt_Acc_PR_AccountDetails",
            //        ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_Acc_PR_AccountDetails : ResRptAr.rpt_Acc_PR_AccountDetails, //"كشف حساب تفصيلي",
            //        Description = "كشف حساب تفصيلي، يعرض تفاصيل الفواتير أو أصناف الفواتير من ضمن التقرير",
            //        TabIndex = 0,
            //        ItemFltr = false,
            //        DateFltr = true,
            //        ExpDateFltr = false,
            //        StoreFltr = false,
            //        VendorFltr = true,
            //        CustomerFltr = false,
            //        ComapnyFltr = false,
            //        CategoryFltr = false,
            //        ProcessFltr = false,
            //        CostCenterFltr = false,
            //        AccountFltr = false,
            //        CustmAccListFltr = false,
            //        UserFltr = false,
            //        SalesEmpFltr = false,
            //        BatchFltr = false,
            //        CutGroupFltr = false,
            //        MtrxFltr = false,
            //        DimensionFltr = false,
            //        JobOrderFltr = false,
            //        ItemTypeFltr = false,
            //        SellPriceFltr = false,
            //        InvBookFltr = false,
            //        EmpGroupFltr = false,
            //        QcFltr = false
            //    });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_frm_Acc_AccountDetails_Vend).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_Acc_PR_AccountDetails",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.frm_Acc_PR_AccountDetails : ResRptAr.frm_Acc_PR_AccountDetails, //"كشف حساب تفصيلي",
                    Description = "كشف حساب تفصيلي، يعرض تفاصيل الفواتير أو أصناف الفواتير من ضمن التقرير",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = true,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = true
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_Acc_PR_AccountsBalances).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_Acc_PR_AccountsBalances",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_Acc_PR_AccountsBalances : ResRptAr.rpt_Acc_PR_AccountsBalances, //"كشف اجمالى حسابات الموردين",
                    Description = "يعرض اجمالي كشف الحساب لكل الموردين",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = true,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = true,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = true
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_Acc_PR_AccountsBalancesWithNotes).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_Acc_PR_AccountsBalancesWithNotes",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_Acc_PR_AccountsBalancesWithNotes : ResRptAr.rpt_Acc_PR_AccountsBalancesWithNotes, //"كشف اجمالى حسابات الموردين",
                    Description = "يعرض كشف اجمالى حسابات الموردين بالأوراق التجارية",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = true,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = true
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_PR_VendorItemsPurchases).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_PR_VendorItemsPurchases",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_PR_VendorItemsPurchases : ResRptAr.rpt_PR_VendorItemsPurchases, //"اجمالي مشتربات صنف من مورد",
                    Description = "يعرض اجمالي مشتريات صنف او مجموعة اصناف من مورد",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = true,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = true,
                    ProcessFltr = false,
                    CostCenterFltr = true,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = true,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = true
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_PR_ItemsPurchasesReturn).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_PR_ItemsPurchasesReturn",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_PR_VendorItemsPurchasesReturns : ResRptAr.rpt_PR_VendorItemsPurchasesReturns, //"اجمالي مردودات مشتربات صنف لمورد",
                    Description = "يعرض اجمالي مردودات مشتريات صنف او مجموعة اصناف لمورد",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = true,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = true,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = true,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = true
                });

            if (Shared.SalesOrderAvailable)
            {
                if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                        ||
                        (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                        Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_OrderAndAchievement_Ven).Count() == 1))
                    lstReportListItem.Add(new reportListItem
                    {
                        ReportName = "frm_OrderAndAchievement_Ven",
                        ReportCaption = Shared.IsEnglish ? ResRptEn.frm_PurchaseOrderAndAchievement : ResRptAr.frm_PurchaseOrderAndAchievement,
                        Description = "بيان بأوامر التوريد ونسب التحقيق من الفواتير",
                        TabIndex = 0,
                        ItemFltr = true,
                        DateFltr = true,
                        ExpDateFltr = false,
                        StoreFltr = true,
                        VendorFltr = true,
                        CustomerFltr = false,
                        CompanyFltr = true,
                        CategoryFltr = true,
                        ProcessFltr = false,
                        CostCenterFltr = false,
                        AccountFltr = false,
                        CustmAccListFltr = false,
                        UserFltr = false,
                        SalesEmpFltr = false,
                        BatchFltr = false,
                        CutGroupFltr = false,
                        JobOrderFltr = false,
                        ItemTypeFltr = false,
                        SellPriceFltr = false,
                        InvBookFltr = false,
                        EmpGroupFltr = false,
                        QcFltr = false,
                        VenGroupFltr = true
                    });
            }

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                    ||
                    (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                    Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_ReceivingAndAchievement).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_ReceivingAndAchievement",
                    ReportCaption = "أذون الاستلام ونسب التحقيق",
                    Description = "بيان بأذون الاستلام ونسب التحقيق من الفواتير",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = true,
                    CustomerFltr = false,
                    CompanyFltr = true,
                    CategoryFltr = true,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = true
                });
            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_PR_VendorItemsPurchases_InTrns).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_PR_VendorItemsPurchases_InTrns",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_PR_VendorItemsPurchases_InTrns : ResRptAr.rpt_PR_VendorItemsPurchases_InTrns,
                    Description = "يعرض بيان بالمشتريات و اذون الاضافة",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = true,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_SL_ItemsNetPurchaseDetails).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_SL_ItemsNetPurchaseDetails",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.frm_SL_ItemsNetPurchaseDetails : ResRptAr.frm_SL_ItemsNetPurchaseDetails,
                    Description = "تقرير صافي المشتريات ومردود المشتريات",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = true,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = true
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
           ||
           (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
           Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_IC_StorePurchase).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_IC_StorePurchase",
                    ReportCaption = Shared.IsEnglish ? "Total Stores' Purchases" : "اجمالي مشتريات المخازن",
                    Description = "عرض إجمالي مشتريات المخازن",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = true
                });


            this.lstBxReports.SelectedValueChanged -= new System.EventHandler(this.lstBxReports_SelectedValueChanged);
            lstBxReports.DataSource = lstReportListItem;
            lstBxReports.ValueMember = "ReportName";
            lstBxReports.DisplayMember = "ReportCaption";
            txtDescription.Text = string.Empty;
            lstBxReports.SelectedIndex = -1;
            ShowHideFilters(false, false, false, false, false, false, false, false, false,
                false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false);
            lstBxReports.Focus();
            this.lstBxReports.SelectedValueChanged += new System.EventHandler(this.lstBxReports_SelectedValueChanged);
        }

        private void NBI_HR_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            NbiAppearance("HR");
            lstReportListItem.Clear();

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_IC_ManfCommision).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_HR_ManfCommision",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_HR_ManfCommision : ResRptAr.rpt_HR_ManfCommision,
                    Description = "تقرير انتاجية الموظف",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = true,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_SalesRep_DaySummary).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_SalesRep_DaySummary",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.frm_SalesRep_DaySummary : ResRptAr.frm_SalesRep_DaySummary,
                    Description = "تقرير يومية مندوب",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = true,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_SL_SalesEmpTargetCommission).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_HR_SalesEmpTargetCommission",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_HR_SalesEmpCommission : ResRptAr.rpt_HR_SalesEmpCommission,
                    Description = "يعرض عمولة المندوب للمبيعات والتحصيل حسب الهدف المحدد",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = true,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_SL_SalesCommision).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_HR_SalesEmpInvoiceCommision",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_HR_SalesCommision : ResRptAr.rpt_HR_SalesCommision,
                    Description = "يعرض مبيعات المندوب والنسب المستحقه حسب العمولة المحددة",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = true,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = true,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = true,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_SL_DeliveryCommision).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_HR_OutTrnsItemsCommision",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_HR_DeliveryCommision : ResRptAr.rpt_HR_DeliveryCommision,
                    Description = "يعرض عمولات مسئولي التسليم عن البضاعة المنصرفة من المخازن",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = true,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_HR_SalesInvItemsCommision).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_HR_SalesInvItemsCommision",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_HR_SalesInvItemsCommision : ResRptAr.rpt_HR_SalesInvItemsCommision,
                    Description = "يعرض عمولات مندوبي البيع عن البضاعة المباعة",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = true,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_HR_AllPays).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_HR_AllPays",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.frm_HR_AllPays : ResRptAr.frm_HR_AllPays,
                    Description = "كشف مسير الرواتب خلال فترة",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_HR_Insurance).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_HR_Insurance",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.frm_HR_Insurance : ResRptAr.frm_HR_Insurance,
                    Description = "كشف تامينات الموظفين",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_HR_AllExpectedPays).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_HR_AllExpectedPays",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.frm_HR_AllExpectedPays : ResRptAr.frm_HR_AllExpectedPays,
                    Description = "كشف رواتب متوقعة",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_HR_Att).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_HR_Att",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.frm_HR_Att : ResRptAr.frm_HR_Att,
                    Description = "كشف الحضور والانصراف",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_HR_Vacations).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_HR_Vacations",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.frm_HR_Vacations : ResRptAr.frm_HR_Vacations,
                    Description = "كشف الاجازات",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_HR_Emloyee_Nationality).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_HR_Emloyee_Nationality",
                    ReportCaption = "كشف جنسيات الموظفين"/*Shared.IsEnglish ? ResRptEn.frm_HR_Emloyee_Nationality : ResRptAr.frm_HR_Emloyee_Nationality*/,
                    Description = "كشف جنسيات الموظفين",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = false,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_HR_Employee_Report).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_HR_Employee_Report",
                    ReportCaption = "كشف الموظفين"/*Shared.IsEnglish ? ResRptEn.frm_HR_Emloyee_Nationality : ResRptAr.frm_HR_Emloyee_Nationality*/,
                    Description = "كشف الموظفين",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
    ||
    (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
    Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_HR_VacationBal).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_HR_VacationBal",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.frm_HR_VacationBal : ResRptAr.frm_HR_VacationBal,
                    Description = "ارصدة الاجازات",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = false,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });
            this.lstBxReports.SelectedValueChanged -= new System.EventHandler(this.lstBxReports_SelectedValueChanged);
            lstBxReports.DataSource = lstReportListItem;
            lstBxReports.ValueMember = "ReportName";
            lstBxReports.DisplayMember = "ReportCaption";
            txtDescription.Text = string.Empty;
            lstBxReports.SelectedIndex = -1;
            ShowHideFilters(false, false, false, false, false, false, false, false, false,
                false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false);
            lstBxReports.Focus();
            this.lstBxReports.SelectedValueChanged += new System.EventHandler(this.lstBxReports_SelectedValueChanged);
        }

        private void NBI_Acc_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            NbiAppearance("ACC");
            lstReportListItem.Clear();

            //lstReportListItem.Add(new reportListItem
            //{
            //    ReportName = "rpt_Acc_Income",
            //    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_Acc_Income : ResRptAr.rpt_Acc_Income + "(الحسابات الفرعية)", //"قائمة الدخل",
            //    Description = "قائمة الدخل",
            //    TabIndex = 0,
            //    ItemFltr = false,
            //    DateFltr = true,
            //    ExpDateFltr = false,
            //    StoreFltr = false,
            //    VendorFltr = false,
            //    CustomerFltr = false,
            //    CompanyFltr = false,
            //    CategoryFltr = false,
            //    ProcessFltr = false,
            //    CostCenterFltr = true,
            //    AccountFltr = false,
            //    CustmAccListFltr = false,
            //    UserFltr = false,
            //    SalesEmpFltr = false,
            //    BatchFltr = false,
            //    CutGroupFltr = false,
            //    MtrxFltr = false,
            //    DimensionFltr = false,
            //    JobOrderFltr = false,
            //    ItemTypeFltr = false,
            //    SellPriceFltr = false,
            //    InvBookFltr = false,
            //    EmpGroupFltr = false,
            //    QcFltr = false,
            //    VenGroupFltr = false
            //});

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_Acc_Income).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_Acc_IncomeT",
                    ReportCaption = (Shared.IsEnglish ? ResRptEn.rpt_Acc_Income : ResRptAr.rpt_Acc_Income) + " T ", //"قائمة الدخل",
                    Description = "قائمة الدخل T",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = true,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });
            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_Acc_Income).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_Acc_IncomeTwithArchive",
                    ReportCaption = (Shared.IsEnglish ? ResRptEn.rpt_Acc_Income : ResRptAr.rpt_Acc_Income) + " بالارشيف T ", //"قائمة الدخل",
                    Description = " قائمة الدخل بالأرشيف T",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = true,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            //lstReportListItem.Add(new reportListItem
            //{
            //    ReportName = "rpt_Acc_Expenses_RevenuesT",
            //    ReportCaption = (Shared.IsEnglish ? "Expenses And Revenues" : "حساب الايرادات و المصروفات") , //"حساب الايرادات و المصروفات",
            //    Description = "حساب الايرادات و المصروفات بالمقارنة بالمقرر في الميزانية",
            //    TabIndex = 0,
            //    ItemFltr = false,
            //    DateFltr = true,
            //    ExpDateFltr = false,
            //    StoreFltr = false,
            //    VendorFltr = false,
            //    CustomerFltr = false,
            //    ComapnyFltr = false,
            //    CategoryFltr = false,
            //    ProcessFltr = false,
            //    CostCenterFltr = true,
            //    AccountFltr = false,
            //    CustmAccListFltr = false,
            //    UserFltr = false,
            //    SalesEmpFltr = false,
            //    BatchFltr = false,
            //    CutGroupFltr = false
            //});

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_Acc_Balance).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_Acc_BalanceT",
                    ReportCaption = (Shared.IsEnglish ? ResRptEn.rpt_Acc_Balance : ResRptAr.rpt_Acc_Balance) + " T ", //"المركز المالي T",
                    Description = "المركز المالي T",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_Acc_CostCenter_AccDetails).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_Acc_CostCenter_AccDetails",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_Acc_CostCenter_AccDetails : ResRptAr.rpt_Acc_CostCenter_AccDetails, //"كشف حساب مجمع لمركز التكلفة",
                    Description = "كشف حساب مجمع لمركز التكلفة",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = true,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_Acc_Account_CostCenters).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_Acc_Account_CostCenters",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_Acc_Account_CostCenters : ResRptAr.rpt_Acc_Account_CostCenters, //"كشف مجمع لمراكز تكلفة حساب",
                    Description = "كشف مجمع لمراكز تكلفة حساب",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = true,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_Acc_CostCenterTotalBalances).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_Acc_CostCenterTotalBalances",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_Acc_CostCenterTotalBalances : ResRptAr.rpt_Acc_CostCenterTotalBalances, //"إجمالي أرصدة مراكز التكلفة",
                    Description = "إجمالي أرصدة مراكز التكلفة",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = false,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_Acc_CustomAccListDetails).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_Acc_CustomAccListDetails",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_Acc_CustomAccListDetails : ResRptAr.rpt_Acc_CustomAccListDetails, //"كشف تفصيلي لقائمة مخصصة",
                    Description = "كشف تفصيلي لقائمة مخصصة",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = true,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_Acc_CustomAccListBalances).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_Acc_CustomAccListBalances",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_Acc_CustomAccListBalances : ResRptAr.rpt_Acc_CustomAccListBalances, //"كشف اجمالى القوائم المخصصة",
                    Description = "كشف اجمالى القوائم المخصصة",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_Acc_DailyIncome).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_Acc_DailyIncome",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_Acc_DailyIncome : ResRptAr.rpt_Acc_DailyIncome,
                    Description = "كشف ايرادات يومية",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = true,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_Acc_DailyPayments).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_Acc_DailyPayments",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_Acc_DailyPayments : ResRptAr.rpt_Acc_DailyPayments,
                    Description = "كشف مدفوعات يومية",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = true,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });
            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_Acc_DailyPayments).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_Acc_DailyPayments",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_Acc_DailyPayments : ResRptAr.rpt_Acc_DailyPayments,
                    Description = "كشف مدفوعات",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = true,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });


            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_Acc_DailyPaymentsAndIncomePayments).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_Acc_DailyPaymentsAndIncomePayments",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_Acc_DailyPaymentsAndIncomePayments : ResRptAr.rpt_Acc_DailyPaymentsAndIncomePayments,
                    Description = "كشف مدفوعات و ايرادات يومية",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = true,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });
            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
               ||
               (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
               Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_Acc_Payments).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_Acc_Payments",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_Acc_Payments : ResRptAr.rpt_Acc_Payments,
                    Description = "كشف مدفوعات",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });
            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
              ||
              (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
              Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_ACC_SubLedger).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_ACC_SubLedger",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_ACC_SubLedger : ResRptAr.rpt_ACC_SubLedger,
                    Description = "دفتر الأستاذ",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = true,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });
            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                || (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                 Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_Acc_YearlyBudget).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_Acc_YearlyBudget",
                    ReportCaption = Shared.IsEnglish ? "Budget" : "الموازنة", //ResRptEn.rpt_Acc_DailyPayments : ResRptAr.rpt_Acc_DailyPayments,
                    Description = "الموازنة",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_ACC_Statement).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_ACC_Statement",
                    ReportCaption = Shared.IsEnglish ? "Account Statement" : "كشف حساب",
                    Description = "كشف حساب ",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = false,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_Acc_TrialBalance).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_ACC_TrialBalance",
                    ReportCaption = Shared.IsEnglish ? "Trial Balance for Accounts" : "ميزان مراجعة مجاميع وأرصدة",
                    Description = "ميزان مراجعة مجاميع وأرصدة ",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = false,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_ACC_CCTrialBalance).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_ACC_CCTrialBalance",
                    ReportCaption = Shared.IsEnglish ? "Trial Balance for Cost Centers" : "ميزان مراجعة مراكز تكلفة",
                    Description = "ميزان مراجعة مراكز تكلفة ",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = false,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });
            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
               ||
               (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
               Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_Acc_Account_CostCentersPivot).Count() == 1))  // rpt_Acc_Account_CostCenters صلاحية قديمة
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_Acc_Account_CostCentersPivot",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_Acc_Account_CostCenters : "جديد - كشف مجمع لمراكز التكلفة", //"كشف مجمع لمراكز تكلفة حساب",
                    Description = "جديد - كشف مجمع لمراكز تكلفة ",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_Acc_CostCenterOper).Count() == 1))//rpt_Acc_Account_CostCenters صلاحية قديمة
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_Acc_CostCenterOper",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.rpt_Acc_Account_CostCenters : "جديد - كشف حركات مراكز التكلفة", //"كشف مجمع لمراكز تكلفة حساب",
                    Description = "جديد - كشف حركات مراكز تكلفة ",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });



            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_ACC_IncomeSub).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_Acc_IncomeSub",
                    ReportCaption = Shared.IsEnglish ? "Income Sub-Accounts" : "قائمة الدخل للحسابات الفرعية",
                    Description = "قائمة الدخل للحسابات الفرعية",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_ACC_IncomeMain).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_Acc_IncomeMain",
                    ReportCaption = Shared.IsEnglish ? "Income Main Accounts" : "قائمة الدخل للحسابات الرئيسية",
                    Description = "قائمة الدخل للحسابات الرئيسية",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });


            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
            ||
            (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
            Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_CashFlow).Count() == 1))

                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_CashFlow",
                    ReportCaption = Shared.IsEnglish ? "CashFlow" : "متابعة تدفق الأموال",
                    Description = " متابعة تدفق الأموال",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            this.lstBxReports.SelectedValueChanged -= new System.EventHandler(this.lstBxReports_SelectedValueChanged);
            lstBxReports.DataSource = lstReportListItem;
            lstBxReports.ValueMember = "ReportName";
            lstBxReports.DisplayMember = "ReportCaption";
            txtDescription.Text = string.Empty;
            lstBxReports.SelectedIndex = -1;
            ShowHideFilters(false, false, false, false, false, false, false, false, false,
                false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false);
            lstBxReports.Focus();
            this.lstBxReports.SelectedValueChanged += new System.EventHandler(this.lstBxReports_SelectedValueChanged);
        }

        private void NBI_Special_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            NbiAppearance("Special");
            lstReportListItem.Clear();

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_UserDaySummary).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_UserDaySummary",
                    ReportCaption = Shared.IsEnglish ? "User's Shift Summary" : "يومية(شيفت) مستخدم",
                    Description = "يومية مستخدم",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = true,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });




            if (Shared.MarkettingAvailable)
            {
                if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                    ||
                    (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                    Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_MrAllSales).Count() == 1))
                    lstReportListItem.Add(new reportListItem
                    {
                        ReportName = "frm_MrAllSales",
                        ReportCaption = Shared.IsEnglish ? ResRptEn.frm_MrAllSales : ResRptAr.frm_MrAllSales,
                        Description = "توزيع الأصناف حسب فئة العميل",
                        TabIndex = 0,
                        ItemFltr = false,
                        DateFltr = true,
                        ExpDateFltr = false,
                        StoreFltr = false,
                        VendorFltr = false,
                        CustomerFltr = false,
                        CompanyFltr = false,
                        CategoryFltr = false,
                        ProcessFltr = false,
                        CostCenterFltr = false,
                        AccountFltr = false,
                        CustmAccListFltr = false,
                        UserFltr = false,
                        SalesEmpFltr = false,
                        BatchFltr = false,
                        CutGroupFltr = true,
                        JobOrderFltr = false,
                        ItemTypeFltr = false,
                        SellPriceFltr = false,
                        InvBookFltr = false,
                        EmpGroupFltr = false,
                        QcFltr = false,
                        VenGroupFltr = false
                    });
            }


            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                   ||
                   (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                   Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_ItemsPr_and_Sl).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_ItemsPr_and_Sl",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.frm_ItemsPr_and_Sl : ResRptAr.frm_ItemsPr_and_Sl,
                    Description = "مبيعات ومشتريات الأصناف",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = true,
                    CategoryFltr = true,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = true,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                   ||
                   (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                   Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_PR_ContractorExtract).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_PR_ContractorExtract",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.frm_PR_ContractorExtract : ResRptAr.frm_PR_ContractorExtract,
                    Description = "مستخلص مقاول",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = true,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = true,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                   ||
                   (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                   Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_frm_ManfItems).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_ManfItems",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.frm_ManfItems : ResRptAr.frm_ManfItems,
                    Description = "منتجات و خامات عمليات الانتاج",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });



            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
       ||
       (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
       Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_SL_Employee_Item_Target).Count() == 1))
            {
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_SL_Employee_Item_Target",
                    ReportCaption = Shared.IsEnglish ? "Items' sales Achievement per Rep. Report" : "تقرير تحقيق المندوبين لمبيعات الأصناف",
                    Description = "تقرير تحقيق المندوبين لمبيعات الأصناف",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = false,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false


                });
                //    salesEmp = true;
            }


            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
       ||
       (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
       Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_SL_DelegatesSales).Count() == 1))
            {
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_SL_DelegatesSales_ItemCategory",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.frm_SL_DelegatesSales_ItemCategory : ResRptAr.frm_SL_DelegatesSales_ItemCategory,
                    Description = "تفرير مبيعات المندوبين و مجموعات الأصناف",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = false,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false


                });
                //    salesEmp = true;
            }
            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
             ||
             (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
             Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.rpt_frm_ManfItems).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_ManfItems",
                    ReportCaption = Shared.IsEnglish ? ResRptEn.frm_ManfItems : ResRptAr.frm_ManfItems,
                    Description = "منتجات و خامات عمليات الانتاج",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });
            //=====================ImpExFines===============================================//
            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                  ||
                  (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                  Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_ImExp_FinesReport).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_ImExp_FinesReport",
                    ReportCaption = Shared.IsEnglish ? "Fines": "الغرامات",
                    Description = "الغرامات",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = true,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });
            //===============================================================================//
            //=======================================ImpExpContainer========================================//
            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                 ||
                 (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
                 Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_ImExp_Container_Report).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_ImExp_Container_Report",
                    ReportCaption = Shared.IsEnglish ? "Follow Containers" : "متابعة الحاويات",
                    Description = "متابعة الحاويات",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = true,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

            //=======================================ExportConfrmat========================================//
            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
              ||
              (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
              Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_ImExp_ExportConfirmat_Report).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_ImExp_ExportConfirmat_Report",
                    ReportCaption = Shared.IsEnglish ? "Follow Export Confirmations" : "متابعة الموافقات",
                    Description = "متابعة الموافقات",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = true,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });
            //=============================================Commission=====================================================//
            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
             ||
             (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
             Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_ImExp_Commission_Report).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_ImExp_Commission_Report",
                    ReportCaption = Shared.IsEnglish ? "Commission Report" : "كشف العمولات",
                    Description = "كشف العمولات",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = false,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = true,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });
            //============================================PreInvoices================================================================//
            
                 if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
             ||
             (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
             Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_ImExp_PreInvoice_Report).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_ImExp_PreInvoice_Report",
                    ReportCaption = Shared.IsEnglish ? "Follow Invoices" : "متابعة الفواتير",
                    Description = "متابعة الفواتير",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = true,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });
            //=====================================================================================================================//
            //============================================JobOrder================================================================//

            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
        ||
        (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
        Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_Sl_JobOrderStatus).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_Sl_JobOrderStatus",
                    ReportCaption = Shared.IsEnglish ? "Job Order Allows Attention" : "اوامر العمل المسموح فيها بالتنبية",
                    Description = "اوامر العمل المسموح فيها بالتنبية",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });
            //=====================================================================================================================//
            if (Shared.user.AccessType == (byte)AccessType.FullAccess || Shared.user.AccessType == (byte)AccessType.Admin
                ||
                 (Shared.user.AccessType == (byte)AccessType.CustomAccess &&
               Shared.LstUserPrvlg.Where(p => p.PId == (int)FormsNames.frm_CustomsCertifecationWarning).Count() == 1))
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "frm_CustomsCertifecationWarning",
                    ReportCaption = Shared.IsEnglish ? "Customs Certifecation Warning" : "تنبيهات الشهادات الجركية",
                    Description = "تنبيهات الشهادات الجركية",
                    TabIndex = 0,
                    ItemFltr = false,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    CompanyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    AccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false,
                    CutGroupFltr = false,
                    MtrxFltr = false,
                    DimensionFltr = false,
                    JobOrderFltr = false,
                    ItemTypeFltr = false,
                    SellPriceFltr = false,
                    InvBookFltr = false,
                    EmpGroupFltr = false,
                    QcFltr = false,
                    VenGroupFltr = false
                });

         
            //============================================================================================//
            this.lstBxReports.SelectedValueChanged -= new System.EventHandler(this.lstBxReports_SelectedValueChanged);
            lstBxReports.DataSource = lstReportListItem;
            lstBxReports.ValueMember = "ReportName";
            lstBxReports.DisplayMember = "ReportCaption";
            txtDescription.Text = string.Empty;
            lstBxReports.SelectedIndex = -1;
            ShowHideFilters(false, false, false, false, false, false, false, false, false,
                false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false);
            lstBxReports.Focus();
            this.lstBxReports.SelectedValueChanged += new System.EventHandler(this.lstBxReports_SelectedValueChanged);
        

        }

        void NbiAppearance(string sn)
        {
            if (sn == "SL")
            {
                NBI_SL.Appearance.Font = new Font("Tahoma", 9F, System.Drawing.FontStyle.Bold);
            }
            
        }

        private void lstBxReports_SelectedValueChanged(object sender, EventArgs e)
        {

            if (lstBxReports.SelectedItem != null)
            {
                bool deliverEmp = false;
                bool salesEmp = false;
                string reportName = ((reportListItem)lstBxReports.SelectedItem).ReportName;

                #region Accounts types to show in lookup
                if (reportName == "rpt_Acc_Account_CostCenters")
                {
                    ShowAccount = AccountTypeToShow.WithCostCenter;
                }
                if (reportName == "rpt_Acc_DailyPayments" || reportName == "rpt_Acc_DailyIncome")
                {
                    ShowAccount = AccountTypeToShow.PayAccounts;
                }
                grdVAccount.RefreshData();
                #endregion


                int tabIndex = ((reportListItem)lstBxReports.SelectedItem).TabIndex;
                txtDescription.Text = ((reportListItem)lstBxReports.SelectedItem).Description;
                bool itemFltr = ((reportListItem)lstBxReports.SelectedItem).ItemFltr;
                bool dateFltr = ((reportListItem)lstBxReports.SelectedItem).DateFltr;
                bool expDateFltr = ((reportListItem)lstBxReports.SelectedItem).ExpDateFltr;
                bool storeFltr = ((reportListItem)lstBxReports.SelectedItem).StoreFltr;
                bool vendorFltr = ((reportListItem)lstBxReports.SelectedItem).VendorFltr;
                bool customerFltr = ((reportListItem)lstBxReports.SelectedItem).CustomerFltr;
                bool comapyFltr = ((reportListItem)lstBxReports.SelectedItem).CompanyFltr;
                bool categoryFltr = ((reportListItem)lstBxReports.SelectedItem).CategoryFltr;
                bool processFltr = ((reportListItem)lstBxReports.SelectedItem).ProcessFltr;
                bool costCenterFltr = ((reportListItem)lstBxReports.SelectedItem).CostCenterFltr;
                bool costCenterAccountFltr = ((reportListItem)lstBxReports.SelectedItem).AccountFltr;
                bool customAccListFltr = ((reportListItem)lstBxReports.SelectedItem).CustmAccListFltr;
                bool userFltr = ((reportListItem)lstBxReports.SelectedItem).UserFltr;
                bool salesEmpFltr = ((reportListItem)lstBxReports.SelectedItem).SalesEmpFltr;
                bool batchFltr = ((reportListItem)lstBxReports.SelectedItem).BatchFltr;
                bool custGroupFltr = ((reportListItem)lstBxReports.SelectedItem).CutGroupFltr;

                bool MtrxFltr = ((reportListItem)lstBxReports.SelectedItem).MtrxFltr;
                bool DimensionFltr = ((reportListItem)lstBxReports.SelectedItem).DimensionFltr;
                bool JobOrderFltr = ((reportListItem)lstBxReports.SelectedItem).JobOrderFltr;
                bool ItemTypeFltr = ((reportListItem)lstBxReports.SelectedItem).ItemTypeFltr;
                bool SellPriceFltr = ((reportListItem)lstBxReports.SelectedItem).SellPriceFltr;
                bool InvBookFltr = ((reportListItem)lstBxReports.SelectedItem).InvBookFltr;
                bool EmpGroupFltr = ((reportListItem)lstBxReports.SelectedItem).EmpGroupFltr;
                bool QCFltr = ((reportListItem)lstBxReports.SelectedItem).QcFltr;
                bool VenGroupFltr = ((reportListItem)lstBxReports.SelectedItem).VenGroupFltr;
                bool CarFltr = ((reportListItem)lstBxReports.SelectedItem).CarsFltr;

                ShowHideFilters(itemFltr, dateFltr, expDateFltr, storeFltr, vendorFltr,
                    customerFltr, comapyFltr, categoryFltr, processFltr, costCenterFltr, costCenterAccountFltr,
                    customAccListFltr, userFltr, salesEmpFltr, batchFltr, custGroupFltr, MtrxFltr,
                    DimensionFltr, JobOrderFltr, ItemTypeFltr, SellPriceFltr, InvBookFltr, EmpGroupFltr, QCFltr, VenGroupFltr, CarFltr);

                #region Enable Disable Modules
                //enable all
                cmbFltrTyp_Item.Enabled = true;
                cmbFltrTyp_Date.Enabled = true;
                cmbFltrTyp_Store.Enabled = true;
                cmbFltrTyp_Vendor.Enabled = true;
                cmbFltrTyp_Customer.Enabled = true;
                cmbFltrTyp_SalesEmp.Enabled = true;

                if (reportName == "rpt_IC_ItemsQtyWithSalesPrice" ||
                    reportName == "rpt_IC_ItemsQtyWithPrices" ||
                    reportName == "rpt_IC_ItemsQty" ||
                    reportName == "frm_IC_ItemsQtyImages" ||
                    reportName == "rpt_IC_ItemsQtywithImages" ||
                    reportName == "rpt_IC_ItemsZeroQty" ||
                    reportName == "frm_IC_ItemsQtyH" ||
                    reportName == "frm_IC_ItemsQtyHWithoutCost")
                {
                    cmbFltrTyp_Date.SelectedIndex = 1;
                    cmbFltrTyp_Date.Enabled = false;
                    if (dt1.EditValue == null)
                        dt1.EditValue = MyHelper.Get_Server_DateTime().Date;
                }
                else if (reportName == "rpt_IC_ItemTransactions" ||
                    reportName == "rpt_IC_ItemTransactionsNoCost")
                {
                    //cmbFltrTyp_Item.SelectedIndex = 1;
                    //cmbFltrTyp_Store.SelectedIndex = 1;
                    //cmbFltrTyp_Item.Enabled = false;
                    //cmbFltrTyp_Store.Enabled = false;
                }
                else if (reportName == "rpt_SL_ItemTrade")
                {
                    cmbFltrTyp_Item.SelectedIndex = 1;
                    cmbFltrTyp_Store.SelectedIndex = 1;
                    cmbFltrTyp_Item.Enabled = false;
                    cmbFltrTyp_Store.Enabled = false;
                }
                else if (reportName == "frm_Acc_PR_AccountDetails")// 
                {
                    cmbFltrTyp_Vendor.SelectedIndex = 1;
                    cmbFltrTyp_Vendor.Enabled = false;
                    cmbFltrTyp_Date.SelectedIndex = 2;
                    cmbFltrTyp_Date.Enabled = false;
                }
                else if (reportName == "frm_Acc_AccountDetails")
                {
                    //cmbFltrTyp_Customer.SelectedIndex = 1;
                    //cmbFltrTyp_Customer.Enabled = false;
                    cmbFltrTyp_Date.SelectedIndex = 2;
                    cmbFltrTyp_Date.Enabled = false;
                }
                //else if (reportName == "rpt_IC_ItemTransactionsDetails")
                //{
                //    cmbFltrTyp_Store.SelectedIndex = 1;
                //    cmbFltrTyp_Store.Enabled = false;
                //    cmbFltrTyp_Date.SelectedIndex = 2;
                //    cmbFltrTyp_Date.Enabled = false;
                //}
                else if (reportName == "rpt_HR_SalesEmpTargetCommission")
                {
                    cmbFltrTyp_SalesEmp.SelectedIndex = 1;
                    cmbFltrTyp_SalesEmp.Enabled = false;
                }

                else if (reportName == "frm_SL_DelegatesSales" || reportName == "rpt_SL_InvoicesHeaders" || reportName == "rpt_SL_ReturnHeaders"
                    || reportName == "rpt_SL_ItemsSales" || reportName == "rpt_SL_ItemsReturn" || reportName == "rpt_Acc_SL_AccountsBalances"
                    || reportName == "rpt_Acc_SL_AccountsBalancesWithNotes" || reportName == "rpt_SL_CustomerItemsSales" || reportName == "rp\t_SL_CustomerItemsSalesReturn"
                    || reportName == "frm_OrderAndAchievement_Cust" || reportName == "frm_SL_Warranty" || reportName == "frm_SL_Invoices_Due"|| reportName == "frm_ImExp_Fines")

                {
                    salesEmp = true;
                }
                else if (reportName == "frm_SL_DeliveryOfficialsSales")
                {
                    deliverEmp = true;
                }


                #endregion
                if (Shared.user.UserChangeStore == false)// user can't see other stores' data
                {
                    cmbFltrTyp_Store.SelectedIndex = 1;
                    cmbFltrTyp_Store.Enabled = false;
                }

                //dtSalesEmp.Rows.Clear();
                //DAL.MyHelper.GetSalesEmps(dtSalesEmp, deliverEmp, salesEmp, Shared.user.DefaultSalesRep);

            }

        }


        private void cmbFltrTyp_Item_SelectedIndexChanged(object sender, EventArgs e)
        {

            if (((ImageComboBoxEdit)sender).Name == "cmbFltrTyp_Item")
            {
                if (cmbFltrTyp_Item.SelectedIndex == 0)
                {
                    FltrTyp_Item = 0;
                    lkpItem1.Enabled = lkpItem2.Enabled = false;
                    lkpItem1.EditValue = lkpItem2.EditValue = null;
                    itemId1 = itemId2 = 0;
                }
                else if (cmbFltrTyp_Item.SelectedIndex == 1)
                {
                    FltrTyp_Item = 1;
                    lkpItem2.Enabled = false;
                    lkpItem2.EditValue = null;
                    itemId2 = 0;

                    lkpItem1.Enabled = true;
                }
                else if (cmbFltrTyp_Item.SelectedIndex == 2)
                {
                    FltrTyp_Item = 2;
                    lkpItem2.Enabled = true;
                    lkpItem1.Enabled = true;
                }
            }
            else if (((ImageComboBoxEdit)sender).Name == "cmbFltrTyp_Date")
            {
                if (cmbFltrTyp_Date.SelectedIndex == 0)
                {
                    FltrTyp_Date = 0;
                    dt1.Enabled = dt2.Enabled = false;
                    dt1.EditValue = dt2.EditValue = null;
                    dateFrom = dateTo = Shared.minDate;
                }
                else if (cmbFltrTyp_Date.SelectedIndex == 1)
                {
                    FltrTyp_Date = 1;
                    dt2.Enabled = false;
                    dt2.EditValue = null;
                    dateTo = Shared.minDate;

                    dt1.Enabled = true;
                }
                else if (cmbFltrTyp_Date.SelectedIndex == 2)
                {
                    FltrTyp_Date = 2;
                    dt2.Enabled = true;
                    dt1.Enabled = true;
                }
            }
            else if (((ImageComboBoxEdit)sender).Name == "cmbFltrTyp_Store")
            {
                if (cmbFltrTyp_Store.SelectedIndex == 0)
                {
                    FltrTyp_Store = 0;
                    lkpStore1.Enabled = lkpStore2.Enabled = false;
                    lkpStore1.EditValue = lkpStore2.EditValue = null;
                    storeId1 = storeId2 = 0;
                }
                else if (cmbFltrTyp_Store.SelectedIndex == 1)
                {
                    FltrTyp_Store = 1;
                    lkpStore2.Enabled = false;
                    lkpStore2.EditValue = null;
                    storeId2 = 0;

                    lkpStore1.Enabled = true;

                    #region Get Stores
                    lkpStore1.Properties.DisplayMember = "StoreNameAr";
                    lkpStore1.Properties.ValueMember = "StoreId";
                    //lkpStore1.EditValue = defaultStoreId;
                    //lkpStore1.Properties.DataSource = dtStores;
                    #endregion
                }
                else if (cmbFltrTyp_Store.SelectedIndex == 2)
                {
                    FltrTyp_Store = 2;
                    lkpStore2.Enabled = true;
                    lkpStore1.Enabled = true;

                    #region Get Stores

                    lkpStore1.Properties.DisplayMember = "StoreNameAr";
                    lkpStore1.Properties.ValueMember = "StoreId";
                    //lkpStore1.EditValue = defaultStoreId;
                    //lkpStore1.Properties.DataSource = dtStores;

                    lkpStore2.Properties.DisplayMember = "StoreNameAr";
                    lkpStore2.Properties.ValueMember = "StoreId";
                    //lkpStore2.EditValue = defaultStoreId;
                    //lkpStore2.Properties.DataSource = dtStores;
                    #endregion
                }
            }
            else if (((ImageComboBoxEdit)sender).Name == "cmbFltrTyp_Vendor")
            {
                gridView1.OptionsSelection.MultiSelect = false;
                gridView1.OptionsSelection.MultiSelectMode = GridMultiSelectMode.RowSelect;

                if (cmbFltrTyp_Vendor.SelectedIndex == 0)
                {
                    FltrTyp_Vendor = 0;
                    lkpVendor1.Enabled = lkpVendor2.Enabled = false;
                    lkpVendor1.EditValue = lkpVendor2.EditValue = null;
                    vendorId1 = vendorId2 = 0;
                    Selected_Customers_Vendors.Clear();
                }
                else if (cmbFltrTyp_Vendor.SelectedIndex == 1)
                {
                    FltrTyp_Vendor = 1;
                    lkpVendor2.Enabled = false;
                    lkpVendor2.EditValue = null;
                    vendorId2 = 0;

                    lkpVendor1.Enabled = true;

                    #region Get Vendors
                    lkpVendor1.Properties.DisplayMember = "VenNameAr";
                    lkpVendor1.Properties.ValueMember = "VendorId";
                    lkpVendor1.EditValue = goldenVendor;
                    //lkpVendor1.Properties.DataSource = lst_Vendors;
                    #endregion
                    Selected_Customers_Vendors.Clear();
                }
                else if (cmbFltrTyp_Vendor.SelectedIndex == 2)
                {
                    FltrTyp_Vendor = 2;
                    lkpVendor2.Enabled = true;
                    lkpVendor1.Enabled = true;

                    #region Get Vendors
                    lkpVendor1.Properties.DisplayMember = "VenNameAr";
                    lkpVendor1.Properties.ValueMember = "VendorId";
                    lkpVendor1.EditValue = goldenVendor;
                    //lkpVendor1.Properties.DataSource = lst_Vendors;

                    lkpVendor2.Properties.DisplayMember = "VenNameAr";
                    lkpVendor2.Properties.ValueMember = "VendorId";
                    lkpVendor2.EditValue = goldenVendor;
                    //lkpVendor2.Properties.DataSource = lst_Vendors;
                    #endregion
                    Selected_Customers_Vendors.Clear();
                }
                else if (cmbFltrTyp_Vendor.SelectedIndex == 3)
                {
                    gridView1.OptionsSelection.MultiSelect = true;
                    gridView1.OptionsSelection.MultiSelectMode = GridMultiSelectMode.CheckBoxRowSelect;

                    FltrTyp_Vendor = 3;
                    lkpVendor2.Enabled = false;
                    lkpVendor2.EditValue = null;
                    vendorId2 = 0;

                    lkpVendor1.Enabled = true;

                    #region Get Vendors
                    lkpVendor1.Properties.DisplayMember = "VenNameAr";
                    lkpVendor1.Properties.ValueMember = "VendorId";
                    lkpVendor1.EditValue = goldenVendor;
                    //lkpVendor1.Properties.DataSource = lst_Vendors;
                    #endregion
                }
            }
            else if (((ImageComboBoxEdit)sender).Name == "cmbFltrTyp_Customer")
            {
                lkpCustomer1.Properties.View.OptionsSelection.MultiSelect = false;
                lkpCustomer1.Properties.View.OptionsSelection.MultiSelectMode = GridMultiSelectMode.RowSelect;

                if (cmbFltrTyp_Customer.SelectedIndex == 0)
                {
                    FltrTyp_Customer = 0;
                    lkpCustomer1.Enabled = lkpCustomer2.Enabled = false;
                    lkpCustomer1.EditValue = lkpCustomer2.EditValue = null;
                    customerId1 = customerId2 = 0;
                    Selected_Customers_Vendors.Clear();
                }
                else if (cmbFltrTyp_Customer.SelectedIndex == 1)
                {
                    FltrTyp_Customer = 1;
                    lkpCustomer2.Enabled = false;
                    lkpCustomer2.EditValue = null;
                    customerId2 = 0;

                    lkpCustomer1.Enabled = true;

                    #region Get Customers
                    lkpCustomer1.Properties.DisplayMember = "CusNameAr";
                    lkpCustomer1.Properties.ValueMember = "CustomerId";
                    lkpCustomer1.EditValue = goldenCustomer;
                    lkpCustomer1.Properties.DataSource = lst_Customers;
                    #endregion
                    Selected_Customers_Vendors.Clear();
                }
                else if (cmbFltrTyp_Customer.SelectedIndex == 2)
                {
                    FltrTyp_Customer = 2;
                    lkpCustomer2.Enabled = true;
                    lkpCustomer1.Enabled = true;

                    #region Get Customers
                    lkpCustomer1.Properties.DisplayMember = "CusNameAr";
                    lkpCustomer1.Properties.ValueMember = "CustomerId";
                    lkpCustomer1.EditValue = goldenCustomer;
                    lkpCustomer1.Properties.DataSource = lst_Customers;

                    lkpCustomer2.Properties.DisplayMember = "CusNameAr";
                    lkpCustomer2.Properties.ValueMember = "CustomerId";
                    lkpCustomer2.EditValue = goldenCustomer;
                    lkpCustomer2.Properties.DataSource = lst_Customers;
                    #endregion
                    Selected_Customers_Vendors.Clear();
                }
                else if (cmbFltrTyp_Customer.SelectedIndex == 3)
                {
                    lkpCustomer1.Properties.View.OptionsSelection.MultiSelect = true;
                    lkpCustomer1.Properties.View.OptionsSelection.MultiSelectMode = GridMultiSelectMode.CheckBoxRowSelect;

                    FltrTyp_Customer = 3;
                    lkpCustomer2.Enabled = false;
                    lkpCustomer2.EditValue = null;
                    customerId2 = 0;

                    lkpCustomer1.Enabled = true;

                    #region Get Customers
                    lkpCustomer1.Properties.DisplayMember = "CusNameAr";
                    lkpCustomer1.Properties.ValueMember = "CustomerId";
                    lkpCustomer1.EditValue = goldenCustomer;
                    lkpCustomer1.Properties.DataSource = lst_Customers;
                    #endregion
                }
            }
            else if (((ImageComboBoxEdit)sender).Name == "cmbFltrTyp_Comp")
            {
                if (cmbFltrTyp_Comp.SelectedIndex == 0)
                {
                    FltrTyp_Company = 0;
                    lkpCompany.Enabled = false;
                    lkpCompany.EditValue = null;
                    companyId = 0;
                }
                else if (cmbFltrTyp_Comp.SelectedIndex == 1)
                {
                    FltrTyp_Company = 1;
                    lkpCompany.Enabled = true;

                    #region Get Companies
                    lkpCompany.Properties.DisplayMember = "CompanyNameAr";
                    lkpCompany.Properties.ValueMember = "CompanyId";
                    lkpCompany.Properties.DataSource = dtCompanies;
                    #endregion
                }
            }
            else if (((ImageComboBoxEdit)sender).Name == "cmbFltrTyp_Cat")
            {
                if (cmbFltrTyp_Cat.SelectedIndex == 0)
                {
                    FltrTyp_Category = 0;
                    lkpCategory.Enabled = false;
                    lkpCategory.EditValue = null;
                    categoryNum = string.Empty;
                }
                else if (cmbFltrTyp_Cat.SelectedIndex == 1)
                {
                    FltrTyp_Category = 1;
                    lkpCategory.Enabled = true;

                    #region Get Categories
                    {
                        lkpCategory.Properties.DataSource = lst_Cat;
                    }
                    lkpCategory.Properties.DisplayMember = "CategoryNameAr";
                    lkpCategory.Properties.ValueMember = "CategoryId";
                  
                    #endregion
                }
            }
            else if (((ImageComboBoxEdit)sender).Name == "cmbFltrTyp_User")
            {
                if (cmbFltrTyp_User.SelectedIndex == 0)
                {
                    FltrTyp_User = 0;
                    lkpUser.Enabled = false;
                    lkpUser.EditValue = null;
                    userId = 0;
                }
                else if (cmbFltrTyp_User.SelectedIndex == 1)
                {
                    FltrTyp_User = 1;
                    lkpUser.Enabled = true;

                    #region Get users
                    lkpUser.Properties.DisplayMember = "UserName";
                    lkpUser.Properties.ValueMember = "UserId";
                    var userQuery = (from u in DB.HR_Users
                                     select new
                                     {
                                         u.UserId,
                                         u.UserName
                                     }).ToList();

                    lkpUser.Properties.DataSource = userQuery;
                    #endregion
                }
            }
            else if (((ImageComboBoxEdit)sender).Name == "cmbFltrTyp_SalesEmp")
            {
                if (cmbFltrTyp_SalesEmp.SelectedIndex == 0)
                {
                    FltrTyp_SalesEmp = 0;
                    lkpSalesEmp.Enabled = false;
                    lkpSalesEmp.EditValue = null;
                    salesEmpId = 0;
                }
                else if (cmbFltrTyp_SalesEmp.SelectedIndex == 1)
                {
                    FltrTyp_SalesEmp = 1;
                    lkpSalesEmp.Enabled = true;

                    #region Get sales emps
                    //dtSalesEmp.Clear();
                    //MyHelper.GetSalesEmps(dtSalesEmp, true, true, 0);
                    lkpSalesEmp.Properties.DisplayMember = "EmpName";
                    lkpSalesEmp.Properties.ValueMember = "EmpId";
                    lkpSalesEmp.Properties.DataSource = dtSalesEmp;
                    #endregion
                }
            }
            else if (((ImageComboBoxEdit)sender).Name == "cmbFltrTyp_SellPrice")
            {
                if (cmbFltrTyp_SellPrice.SelectedIndex == 0)
                {
                    FltrTyp_SellPrice = 0;
                    txtSellPrice1.Enabled = txtSellPrice2.Enabled = false;
                    txtSellPrice1.Text = txtSellPrice2.Text = "0";
                }
                else if (cmbFltrTyp_SellPrice.SelectedIndex == 1)
                {
                    FltrTyp_SellPrice = 1;
                    txtSellPrice1.Enabled = true;
                    txtSellPrice2.Enabled = false;
                    txtSellPrice2.Text = "0";
                }
                else if (cmbFltrTyp_SellPrice.SelectedIndex == 2)
                {
                    FltrTyp_SellPrice = 2;
                    txtSellPrice1.Enabled = txtSellPrice2.Enabled = true;
                }
            }
            else if (((ImageComboBoxEdit)sender).Name == "cmbFltrTyp_InvBook")
            {
                if (cmbFltrTyp_InvBook.SelectedIndex == 0)
                {
                    FltrTyp_InvBook = 0;
                    chkLstInvBooks.Enabled = false;
                    chkLstInvBooks.EditValue = null;
                    InvBooks = string.Empty;
                }
                else if (cmbFltrTyp_InvBook.SelectedIndex == 1)
                {
                    FltrTyp_InvBook = 1;
                    chkLstInvBooks.Enabled = true;

                    #region Get invoice book

                    chkLstInvBooks.Properties.DisplayMember = "InvoiceBookName";
                    chkLstInvBooks.Properties.ValueMember = "InvoiceBookId";
                    //chkLstInvBooks.Properties.DataSource = lst_invBooks;
                    #endregion
                }
            }
            else if (((ImageComboBoxEdit)sender).Name == "cmbFltrTyp_EmpGroup")
            {
                if (cmbFltrTyp_EmpGroup.SelectedIndex == 0)
                {
                    FltrTyp_EmpGroup = 0;
                    lkpEmpGroup.Enabled = false;
                    lkpEmpGroup.EditValue = null;
                    EmpGroupId = 0;
                }
                else if (cmbFltrTyp_EmpGroup.SelectedIndex == 1)
                {
                    FltrTyp_EmpGroup = 1;
                    lkpEmpGroup.Enabled = true;

                    #region Get sales emps
                    lkpEmpGroup.Properties.DisplayMember = "GroupNameAr";
                    lkpEmpGroup.Properties.ValueMember = "GroupId";
                    //lkpEmpGroup.Properties.DataSource = lstEmpGroup;
                    #endregion
                }
            }
            else if (((ImageComboBoxEdit)sender).Name == "cmbFltrTyp_Car")
            {
                if (cmbFltrTyp_Car.SelectedIndex == 0)
                {
                    fltrtype_Car = 0;
                    lkpCars.Enabled = false;
                    lkpCars.EditValue = null;
                    //categoryNum = string.Empty;
                }
                else if (cmbFltrTyp_Car.SelectedIndex == 1)
                {
                    fltrtype_Car = 1;
                    lkpCars.Enabled = true;

                    #region Get Cars

                    //lkpCars.Properties.DisplayMember = "PlateNo";
                    //lkpCars.Properties.ValueMember = "PlateNo";
                    //lkpCars.Properties.DataSource = lst_Cars = DB.ST_Cars.ToList();
                    #endregion
                }
            }
        }

        private void AllGridLkps_EditValueChanged(object sender, EventArgs e)
        {
            if (((GridLookUpEdit)sender).Name == "lkpItem1")
                itemId1 = lkpItem1.EditValue == null || lkpItem1.EditValue.ToString() == "" ? 0 : Convert.ToInt32(lkpItem1.EditValue);
            if (((GridLookUpEdit)sender).Name == "lkpItem2")
                itemId2 = lkpItem2.EditValue == null || lkpItem2.EditValue.ToString() == "" ? 0 : Convert.ToInt32(lkpItem2.EditValue);
            if (((GridLookUpEdit)sender).Name == "lkpStore1")
                storeId1 = lkpStore1.EditValue == null || lkpStore1.EditValue.ToString() == "" ? 0 : Convert.ToInt32(lkpStore1.EditValue);
            if (((GridLookUpEdit)sender).Name == "lkpStore2")
                storeId2 = lkpStore2.EditValue == null || lkpStore2.EditValue.ToString() == "" ? 0 : Convert.ToInt32(lkpStore2.EditValue);
            if (((GridLookUpEdit)sender).Name == "lkpVendor1")
                vendorId1 = lkpVendor1.EditValue == null || lkpVendor1.EditValue.ToString() == "" ? 0 : Convert.ToInt32(lkpVendor1.EditValue);
            if (((GridLookUpEdit)sender).Name == "lkpVendor2")
                vendorId2 = lkpVendor2.EditValue == null || lkpVendor2.EditValue.ToString() == "" ? 0 : Convert.ToInt32(lkpVendor2.EditValue);
            if (((GridLookUpEdit)sender).Name == "lkpCustomer1")
                customerId1 = lkpCustomer1.EditValue == null || lkpCustomer1.EditValue.ToString() == "" ? 0 : Convert.ToInt32(lkpCustomer1.EditValue);
            if (((GridLookUpEdit)sender).Name == "lkpCustomer2")
                customerId2 = lkpCustomer2.EditValue == null || lkpCustomer2.EditValue.ToString() == "" ? 0 : Convert.ToInt32(lkpCustomer2.EditValue);
            if (((GridLookUpEdit)sender).Name == "lkpAccount")
            {
                if (lkpAccount.EditValue == null)
                {
                    AccountId = 0;
                    accNum = string.Empty;
                }
                else
                {
                    AccountId = Convert.ToInt32(lkpAccount.EditValue);
                    //accNum = lst_Accounts.Where(x => x.AccId == Convert.ToInt32(lkpAccount.EditValue)).Select(x => x.AccNumber).First();
                }
            }

            #region matrix
            //if (((GridLookUpEdit)sender).Name == "lkpMtrxParentId")
            //{
            //    #region Mtrx Parent
            //    MtrxParentId = lkpMtrxParentId.EditValue == null ? 0 : Convert.ToInt32(lkpMtrxParentId.EditValue);

            //    if (lkpMtrxParentId.EditValue == null)
            //    {
            //        lkpMtrx1.EditValue = lkpMtrx2.EditValue = lkpMtrx3.EditValue = null;
            //    }
            //    else
            //    {
            //        var item = lst_MtrxPrntItems.Where(i => i.ItemId == Convert.ToInt32(lkpMtrxParentId.EditValue)).First();
            //        if (item.mtrxId1.HasValue)
            //        {
            //            lkpMtrx1.EditValue = item.mtrxId1.Value;
            //        }
            //        else
            //        {
            //            lkpMtrx1.EditValue = null;
            //        }

            //        if (item.mtrxId2.HasValue)
            //        {
            //            lkpMtrx2.EditValue = item.mtrxId2.Value;
            //        }
            //        else
            //        {
            //            Mtrx2.Text = Shared.IsEnglish ? ResRptEn.mtrx2 : ResRptAr.mtrx2;
            //            lkpMtrx2.EditValue = null;
            //        }

            //        if (item.mtrxId3.HasValue)
            //        {
            //            lkpMtrx3.EditValue = item.mtrxId3.Value;
            //        }
            //        else
            //        {
            //            lkpMtrx3.EditValue = null;
            //            Mtrx3.Text = Shared.IsEnglish ? ResRptEn.mtrx3 : ResRptAr.mtrx3;
            //        }
            //    }
            //    #endregion
            //}

            //if (((GridLookUpEdit)sender).Name == "lkpMtrx1")
            //{
            //    lkpMtrxD1.Properties.DisplayMember = "MDName";
            //    lkpMtrxD1.Properties.ValueMember = "MatrixDetailId";
            //    lkpMtrxD1.Properties.DataSource = lkpMtrx1.EditValue == null ? null : lst_MtrxDetails.Where(x => x.MatrixId == Convert.ToInt32(lkpMtrx1.EditValue)).ToList();
            //}
            //if (((GridLookUpEdit)sender).Name == "lkpMtrx2")
            //{
            //    lkpMtrxD2.Properties.DisplayMember = "MDName";
            //    lkpMtrxD2.Properties.ValueMember = "MatrixDetailId";
            //    lkpMtrxD2.Properties.DataSource = lkpMtrx2.EditValue == null ? null : lst_MtrxDetails.Where(x => x.MatrixId == Convert.ToInt32(lkpMtrx2.EditValue)).ToList();
            //}
            //if (((GridLookUpEdit)sender).Name == "lkpMtrx3")
            //{
            //    lkpMtrxD3.Properties.DisplayMember = "MDName";
            //    lkpMtrxD3.Properties.ValueMember = "MatrixDetailId";
            //    lkpMtrxD3.Properties.DataSource = lkpMtrx3.EditValue == null ? null : lst_MtrxDetails.Where(x => x.MatrixId == Convert.ToInt32(lkpMtrx3.EditValue)).ToList();
            //}

            if (((GridLookUpEdit)sender).Name == "lkpMtrxD1")
                M1 = lkpMtrxD1.EditValue == null ? 0 : Convert.ToInt32(lkpMtrxD1.EditValue);
            if (((GridLookUpEdit)sender).Name == "lkpMtrxD2")
                M2 = lkpMtrxD2.EditValue == null ? 0 : Convert.ToInt32(lkpMtrxD2.EditValue);
            if (((GridLookUpEdit)sender).Name == "lkpMtrxD3")
                M3 = lkpMtrxD3.EditValue == null ? 0 : Convert.ToInt32(lkpMtrxD3.EditValue);
            #endregion

            if (((GridLookUpEdit)sender).Name == "lkpJoPriority")
                PriorityId = lkpJoPriority.EditValue == null ? 0 : Convert.ToInt32(lkpJoPriority.EditValue);
            if (((GridLookUpEdit)sender).Name == "lkpJoState")
                StatusId = lkpJoState.EditValue == null ? 0 : Convert.ToInt32(lkpJoState.EditValue);
            if (((GridLookUpEdit)sender).Name == "lkpJoDept")
                DeptId = lkpJoDept.EditValue == null ? 0 : Convert.ToInt32(lkpJoDept.EditValue);
        }

        private void Lkps_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                try
                {
                    if (((GridLookUpEdit)sender).Name == "lkpUser")
                        userId = lkpUser.EditValue == null ? 0 : Convert.ToInt32(lkpUser.EditValue);
                }
                catch { }
                if (((LookUpEdit)sender).Name == "lkpCompany")
                    companyId = lkpCompany.EditValue == null ? 0 : Convert.ToInt32(lkpCompany.EditValue);
                if (((LookUpEdit)sender).Name == "lkpCostCenters")
                    costCenter = lkpCostCenters.EditValue == null ? 0 : Convert.ToInt32(lkpCostCenters.EditValue);
                if (((LookUpEdit)sender).Name == "lkpCustomList")
                    customAccListId = lkpCustomList.EditValue == null ? 0 : Convert.ToInt32(lkpCustomList.EditValue);

                if (((LookUpEdit)sender).Name == "lkpSalesEmp")
                    salesEmpId = lkpSalesEmp.EditValue == null ? 0 : Convert.ToInt32(lkpSalesEmp.EditValue);
                if (((LookUpEdit)sender).Name == "lkpEmpGroup")
                    EmpGroupId = lkpEmpGroup.EditValue == null ? 0 : Convert.ToInt32(lkpEmpGroup.EditValue);
            }
            catch { }
        }

        private void lkpCategory_EditValueChanged(object sender, EventArgs e)
        {
            if (lkpCategory.EditValue == null)
                categoryNum = string.Empty;
            else
                categoryNum = (from c in lst_Cat
                               where c.CategoryId == Convert.ToInt32(lkpCategory.EditValue)
                               select c.CatNumber).FirstOrDefault().ToString();

            List<IC_Item> lstItm = (from i in lst_Items
                                    join c in lst_Cat
                                    on i.Category equals c.CategoryId
                                    where c.CatNumber.StartsWith(categoryNum)
                                    select i).ToList();

            lkpItem1.Properties.DataSource = lstItm;
            lkpItem2.Properties.DataSource = lstItm;
            lkpItem2.Properties.View.Columns["ItemCode1"].SortIndex = 0;
        }

        private void lkpCustGroup_EditValueChanged(object sender, EventArgs e)
        {
            if (lkpCustGroup.Properties.View.OptionsSelection.MultiSelect == true)
            {

            }
            else if (lkpCustGroup.EditValue == null || lkpCustGroup.EditValue.ToString() == string.Empty
                || lkpCustGroup.EditValue.ToString() == "0")
            {
                custGroupId = 0;
                custGroupAccNumber = string.Empty;
            }
            else
            {
                custGroupId = Convert.ToInt32(lkpCustGroup.EditValue);
                custGroupAccNumber = lst_CustGroup.Where(x => x.CustomerGroupId == custGroupId).Select(x => x.AcNumber).FirstOrDefault();
            }
        }

        private void lkpVenGroup_EditValueChanged(object sender, EventArgs e)
        {
            if (lkpVenGroup.EditValue == null || lkpVenGroup.EditValue.ToString() == string.Empty
                  || lkpVenGroup.EditValue.ToString() == "0")
            {
                VenGroupId = 0;
                venGroupAccNumber = string.Empty;
            }
            else
            {
                VenGroupId = Convert.ToInt32(lkpVenGroup.EditValue);
                //venGroupAccNumber = lst_VenGroup.Where(x => x.VendorGroupId == VenGroupId).Select(x => x.AcNumber).FirstOrDefault();
            }
        }

        private void lkpCustomer_Popup(object sender, EventArgs e)
        {
            if (((GridLookUpEdit)sender).Name == "lkpCustomer1")
            {
                ((GridView)lkpCustomer1.Properties.View).RefreshData();
            }
            if (((GridLookUpEdit)sender).Name == "lkpCustomer2")
            {
                ((GridView)lkpCustomer2.Properties.View).RefreshData();
            }
        }

        private void chkLstInvBooks_EditValueChanged(object sender, EventArgs e)
        {
            InvBooks = chkLstInvBooks.EditValue + "";
        }

        private void dt1_EditValueChanged(object sender, EventArgs e)
        {
            if (((DateEdit)sender).Name == "dt1")
            {
                if (dt1.EditValue != null)
                    dateFrom = dt1.DateTime;
                else
                    dateFrom = Shared.minDate;
            }
            else if (((DateEdit)sender).Name == "dt2")
            {
                if (dt2.EditValue != null)
                    dateTo = dt2.DateTime.AddDays(1).AddSeconds(-1);
                else
                    dateTo = Shared.minDate;
            }
        }

        private void cmbProcess_SelectedIndexChanged(object sender, EventArgs e)
        {
            processType = Convert.ToInt32(cmbProcess.EditValue);
        }

        private void cmbItemType_EditValueChanged(object sender, EventArgs e)
        {
            if (cmbItemType.EditValue == null)
                itemType = null;
            else
                itemType = Convert.ToByte(cmbItemType.EditValue);

        }


        void ShowHideFilters(bool item, bool date, bool expDate, bool store, bool vendor, bool customer,
            bool company, bool category, bool process, bool costCenter, bool Account,
            bool customAccList, bool user, bool salesEmp, bool batch, bool customerGroup,
            bool matrix, bool dimensions, bool jobOrder, bool itemType, bool sellPrice, bool InvBookFltr,
            bool empGroupFltr, bool qcFltr, bool vendGroup, bool carFltr)
        {

            if (carFltr)
                cat5.Visibility = cat6.Visibility = cat7.Visibility = LayoutVisibility.Always;
            else
                cat5.Visibility = cat6.Visibility = cat7.Visibility = LayoutVisibility.Never;

            if (item || date || store || vendor)
            {
                lblFrom.Visibility = lblTo.Visibility = lblFltrType.Visibility = LayoutVisibility.Always;

               
            }
            else
            {
                lblFrom.Visibility = lblTo.Visibility = lblFltrType.Visibility = LayoutVisibility.Never;
            }

            if (category)
            {
                cat4.Visibility = LayoutVisibility.Always;
                cat3.Visibility = LayoutVisibility.Always;
                cat2.Visibility = LayoutVisibility.Always;
                cat1.Visibility = LayoutVisibility.Always;

                if (lst_Cat.Count == 0)
                {
                    lkpCategory.Properties.Columns.Clear();
                    lkpCategory.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
                new LookUpColumnInfo("CategoryId", "CategoryId", 20, FormatType.None, "", false, HorzAlignment.Far),
                new LookUpColumnInfo("CategoryNameEn",
                    Shared.IsEnglish? ResICEn.categoryFname : ResICAr.categoryFname//"اسم الفئه ج"
                    , 20, FormatType.None, "", true, HorzAlignment.Far),
                new LookUpColumnInfo("CategoryNameAr",
                    Shared.IsEnglish? ResICEn.categoryName : ResICAr.categoryName//"اسم الفئه"
                    , 20, FormatType.None, "", true, HorzAlignment.Far),
                new LookUpColumnInfo("CatNumber",
                    Shared.IsEnglish? ResICEn.CategoryCode : ResICAr.CategoryCode//"كود الفئه"
                    , 20, FormatType.None, "", true, HorzAlignment.Far)
                });

                    lst_Cat = DAL.MyHelper.GetAllCategoriesList();
                }
            }
            else
            {
                cat4.Visibility = LayoutVisibility.Never;
                cat3.Visibility = LayoutVisibility.Never;
                cat2.Visibility = LayoutVisibility.Never;
                cat1.Visibility = LayoutVisibility.Never;
            }

            if (item)
            {
                item4.Visibility = LayoutVisibility.Always;
                item3.Visibility = LayoutVisibility.Always;
                item2.Visibility = LayoutVisibility.Always;
                item1.Visibility = LayoutVisibility.Always;

                #region LoadItems
                if (lst_Items.Count == 0)
                {
                    lst_Items =
                        (from i in DB.IC_Items
                         where i.ItemType != (int)DAL.ItemType.MatrixParent &&
                         i.ItemType != (int)DAL.ItemType.Subtotal
                         select i).ToList();

                    lkpItem1.Properties.DataSource = lst_Items;
                    lkpItem2.Properties.DataSource = lst_Items;
                    lkpItem2.Properties.View.Columns["ItemCode1"].SortIndex = 0;
                }
                #endregion
            }
            else
            {
                item4.Visibility = LayoutVisibility.Never;
                item3.Visibility = LayoutVisibility.Never;
                item2.Visibility = LayoutVisibility.Never;
                item1.Visibility = LayoutVisibility.Never;
            }

            if (matrix && Shared.ItemMatrixAvailable)
            {
                ParentItem1.Visibility = LayoutVisibility.Always;
                ParentItem2.Visibility = LayoutVisibility.Always;
                ParentItem3.Visibility = LayoutVisibility.Always;

                Mtrx0.Visibility = LayoutVisibility.Always;
                Mtrx1.Visibility = LayoutVisibility.Always;
                Mtrx2.Visibility = LayoutVisibility.Always;
                Mtrx3.Visibility = LayoutVisibility.Always;

            }
            else
            {
                ParentItem1.Visibility = LayoutVisibility.Never;
                ParentItem2.Visibility = LayoutVisibility.Never;
                ParentItem3.Visibility = LayoutVisibility.Never;

                Mtrx0.Visibility = LayoutVisibility.Never;
                Mtrx1.Visibility = LayoutVisibility.Never;
                Mtrx2.Visibility = LayoutVisibility.Never;
                Mtrx3.Visibility = LayoutVisibility.Never;
            }

            if (dimensions && Shared.DimensionsAvailable)
            {
                dim1.Visibility = LayoutVisibility.Always;
                dim2.Visibility = LayoutVisibility.Always;
                dim3.Visibility = LayoutVisibility.Always;
                dim4.Visibility = LayoutVisibility.Always;
            }
            else
            {
                dim1.Visibility = LayoutVisibility.Never;
                dim2.Visibility = LayoutVisibility.Never;
                dim3.Visibility = LayoutVisibility.Never;
                dim4.Visibility = LayoutVisibility.Never;
            }

            if (date)
            {
                date4.Visibility = LayoutVisibility.Always;
                date3.Visibility = LayoutVisibility.Always;
                date2.Visibility = LayoutVisibility.Always;
                date1.Visibility = LayoutVisibility.Always;
            }
            else
            {
                date4.Visibility = LayoutVisibility.Never;
                date3.Visibility = LayoutVisibility.Never;
                date2.Visibility = LayoutVisibility.Never;
                date1.Visibility = LayoutVisibility.Never;
            }

            if (expDate && Shared.st_Store.ExpireDate == true)
            {
                expDate3.Visibility = LayoutVisibility.Always;
                expDate2.Visibility = LayoutVisibility.Always;
                expDate1.Visibility = LayoutVisibility.Always;
                dtExpDate.Visible = true;
                if (dtExpDate.EditValue == null)
                    dtExpDate.DateTime = MyHelper.Get_Server_DateTime();
            }
            else
            {
                expDate3.Visibility = LayoutVisibility.Never;
                expDate2.Visibility = LayoutVisibility.Never;
                expDate1.Visibility = LayoutVisibility.Never;
                dtExpDate.EditValue = null;
                dtExpDate.Visible = false;
            }

            if (store)
            {
                store4.Visibility = LayoutVisibility.Always;
                store3.Visibility = LayoutVisibility.Always;
                store2.Visibility = LayoutVisibility.Always;
                store1.Visibility = LayoutVisibility.Always;


                if (lstStores.Count == 0)
                {
                    lstStores = DAL.MyHelper.Get_Stores4Reports(Shared.user.UserChangeStore, Shared.user.DefaultStore);
                    lkpStore1.Properties.DataSource =
                    lkpStore2.Properties.DataSource = lstStores;
                }

                //disable selecting other stores for user
                if (Shared.user.UserChangeStore == false)
                {
                    cmbFltrTyp_Store.SelectedIndex = 1;
                    cmbFltrTyp_Store.Enabled = false;
                    lkpStore1.EditValue = lstStores[0].StoreId;
                    lkpStore1.Enabled = false;

                }
            }
            else
            {
                store4.Visibility = LayoutVisibility.Never;
                store3.Visibility = LayoutVisibility.Never;
                store2.Visibility = LayoutVisibility.Never;
                store1.Visibility = LayoutVisibility.Never;
            }

            if (vendor)
            {
                vendor4.Visibility = LayoutVisibility.Always;
                vendor3.Visibility = LayoutVisibility.Always;
                vendor2.Visibility = LayoutVisibility.Always;
                vendor1.Visibility = LayoutVisibility.Always;

                //if (lst_Vendors.Count() == 0)
                //{
                //    int lastVenId = 0;
                //    goldenVendor = DAL.MyHelper.GetVendors(out lst_Vendors, out lastVenId);
                //}
            }
            else
            {
                vendor4.Visibility = LayoutVisibility.Never;
                vendor3.Visibility = LayoutVisibility.Never;
                vendor2.Visibility = LayoutVisibility.Never;
                vendor1.Visibility = LayoutVisibility.Never;
            }

            if (customer)
            {
                Customer4.Visibility = LayoutVisibility.Always;
                Customer3.Visibility = LayoutVisibility.Always;
                Customer2.Visibility = LayoutVisibility.Always;
                Customer1.Visibility = LayoutVisibility.Always;

                if (lst_Customers.Count() == 0)
                {
                    int LastCustId = 0;
                    goldenCustomer = DAL.MyHelper.GetCustomers(out lst_Customers, 0, out LastCustId);
                }
            }
            else
            {
                Customer4.Visibility = LayoutVisibility.Never;
                Customer3.Visibility = LayoutVisibility.Never;
                Customer2.Visibility = LayoutVisibility.Never;
                Customer1.Visibility = LayoutVisibility.Never;
            }

            if (company)
            {
                comp4.Visibility = LayoutVisibility.Always;
                comp3.Visibility = LayoutVisibility.Always;
                comp2.Visibility = LayoutVisibility.Always;
                comp1.Visibility = LayoutVisibility.Always;

                if (dtCompanies.Rows.Count == 0)
                {
                    lkpCompany.Properties.Columns.Clear();
                    lkpCompany.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
                new LookUpColumnInfo("CompanyId", "CompanyId", 20, FormatType.None, "", false, HorzAlignment.Far),
                new LookUpColumnInfo("CompanyNameEn",
                    Shared.IsEnglish? ResICEn.companyFname : ResICAr.companyFname//"اسم الشركه ج"
                    , 20, FormatType.None, "", true, HorzAlignment.Far),
                new LookUpColumnInfo("CompanyNameAr",
                    Shared.IsEnglish? ResICEn.companyName : ResICAr.companyName//"اسم الشركه"
                    , 20, FormatType.None, "", true, HorzAlignment.Far),
                new LookUpColumnInfo("CompanyCode",
                    Shared.IsEnglish? ResICEn.CompanyCode : ResICAr.CompanyCode//"كود الشركه"
                    , 20, FormatType.None, "", true, HorzAlignment.Far)
                });
                    DAL.MyHelper.GetCompanies(dtCompanies);

                }
            }
            else
            {
                comp4.Visibility = LayoutVisibility.Never;
                comp3.Visibility = LayoutVisibility.Never;
                comp2.Visibility = LayoutVisibility.Never;
                comp1.Visibility = LayoutVisibility.Never;
            }



            if (process)
            {
                process3.Visibility = LayoutVisibility.Always;
                process2.Visibility = LayoutVisibility.Always;
                process1.Visibility = LayoutVisibility.Always;
            }
            else
            {
                process3.Visibility = LayoutVisibility.Never;
                process2.Visibility = LayoutVisibility.Never;
                process1.Visibility = LayoutVisibility.Never;
            }

            if (costCenter)
            {
                cost3.Visibility = LayoutVisibility.Always;
                cost2.Visibility = LayoutVisibility.Always;
                cost1.Visibility = LayoutVisibility.Always;

                //if (lst_CostCenters.Count == 0)
                //{
                //    lst_CostCenters = DAL.HelperAcc.GetCostCentersLst(false);
                //    lkpCostCenters.Properties.DisplayMember = "CostCenterName";
                //    lkpCostCenters.Properties.ValueMember = "CostCenterId";
                //    lkpCostCenters.Properties.DataSource = lst_CostCenters;
                //}
            }
            else
            {
                cost3.Visibility = LayoutVisibility.Never;
                cost2.Visibility = LayoutVisibility.Never;
                cost1.Visibility = LayoutVisibility.Never;
            }

            if (Account)
            {
                acc3.Visibility = LayoutVisibility.Always;
                acc2.Visibility = LayoutVisibility.Always;
                acc1.Visibility = LayoutVisibility.Always;

                //if (lst_Accounts.Count == 0)
                //{

                //    lkpAccount.Properties.DisplayMember = "AccName";
                //    lkpAccount.Properties.ValueMember = "AccId";

                //    lst_Accounts = HelperAcc.LoadAccountsTree(0, true);
                //    lkpAccount.Properties.DataSource = lst_Accounts;
                //}
            }
            else
            {
                acc3.Visibility = LayoutVisibility.Never;
                acc2.Visibility = LayoutVisibility.Never;
                acc1.Visibility = LayoutVisibility.Never;
            }

            if (customAccList)
            {
                cstmLst3.Visibility = LayoutVisibility.Always;
                cstmLst2.Visibility = LayoutVisibility.Always;
                cstmLst1.Visibility = LayoutVisibility.Always;

                if (lkpCustomList.Properties.DataSource == null)
                {
                    lkpCustomList.Properties.DisplayMember = "CustomAccListName";
                    lkpCustomList.Properties.ValueMember = "CustomAccListId";
                    lkpCustomList.Properties.DataSource =
                        new ERPDataContext().Acc_CustomAccLists.Select(x => x).ToList();
                }
            }
            else
            {
                cstmLst3.Visibility = LayoutVisibility.Never;
                cstmLst2.Visibility = LayoutVisibility.Never;
                cstmLst1.Visibility = LayoutVisibility.Never;
            }

            if (user)
            {
                user4.Visibility = LayoutVisibility.Always;
                user3.Visibility = LayoutVisibility.Always;
                user2.Visibility = LayoutVisibility.Always;
                user1.Visibility = LayoutVisibility.Always;

                if (lkpUser.Properties.DataSource == null)
                {
                    lkpUser.Properties.DisplayMember = "UserName";
                    lkpUser.Properties.ValueMember = "UserId";

                    var userQuery = (from u in DB.HR_Users
                                     select new
                                     {
                                         u.UserId,
                                         u.UserName
                                     }).ToList();

                    lkpUser.Properties.DataSource = userQuery;

                }
            }
            else
            {
                user4.Visibility = LayoutVisibility.Never;
                user3.Visibility = LayoutVisibility.Never;
                user2.Visibility = LayoutVisibility.Never;
                user1.Visibility = LayoutVisibility.Never;
            }

            if (salesEmp && Shared.SalesManAvailable)
            {
                salesEmp4.Visibility = LayoutVisibility.Always;
                salesEmp3.Visibility = LayoutVisibility.Always;
                salesEmp2.Visibility = LayoutVisibility.Always;
                salesEmp1.Visibility = LayoutVisibility.Always;

                if (dtSalesEmp.Rows.Count == 0)
                {
                    DAL.MyHelper.GetSalesEmps(dtSalesEmp, true, true, Shared.user.DefaultSalesRep);
                    lkpSalesEmp.Properties.DisplayMember = "EmpName";
                    lkpSalesEmp.Properties.ValueMember = "EmpId";
                    lkpSalesEmp.Properties.DataSource = dtSalesEmp;
                }
            }
            else
            {
                salesEmp4.Visibility = LayoutVisibility.Never;
                salesEmp3.Visibility = LayoutVisibility.Never;
                salesEmp2.Visibility = LayoutVisibility.Never;
                salesEmp1.Visibility = LayoutVisibility.Never;
            }

            if (batch)
            {
                Batch4.Visibility = LayoutVisibility.Always;
                Batch3.Visibility = LayoutVisibility.Always;
                Batch2.Visibility = LayoutVisibility.Always;
                Batch1.Visibility = LayoutVisibility.Always;
            }
            else
            {
                Batch4.Visibility = LayoutVisibility.Never;
                Batch3.Visibility = LayoutVisibility.Never;
                Batch2.Visibility = LayoutVisibility.Never;
                Batch1.Visibility = LayoutVisibility.Never;
            }

            if (customerGroup)
            {
                custGroup3.Visibility = LayoutVisibility.Always;
                custGroup2.Visibility = LayoutVisibility.Always;
                custGroup1.Visibility = LayoutVisibility.Always;

                if (lst_CustGroup.Count() == 0)
                {
                    lst_CustGroup = DAL.MyHelper.GetCustomersCategories();

                    lkpCustGroup.Properties.DisplayMember = "CGNameAr";
                    lkpCustGroup.Properties.ValueMember = "CustomerGroupId";
                    lkpCustGroup.Properties.DataSource = lst_CustGroup;
                }
            }
            else
            {
                custGroup3.Visibility = LayoutVisibility.Never;
                custGroup2.Visibility = LayoutVisibility.Never;
                custGroup1.Visibility = LayoutVisibility.Never;
            }

            if (jobOrder && Shared.JobOrderAvailable)
            {
                jo4.Visibility = LayoutVisibility.Always;
                jo3.Visibility = LayoutVisibility.Always;
                jo2.Visibility = LayoutVisibility.Always;
                jo1.Visibility = LayoutVisibility.Always;

                //if (lst_Priority.Count == 0)
                //{
                //    lst_Priority = DB.JO_Priorities.ToList();
                //    lst_Status = DB.JO_Status.ToList();
                //    lst_Dept = DB.JO_Depts.ToList();

                //    lkpJoPriority.Properties.DisplayMember = "Priority";
                //    lkpJoPriority.Properties.ValueMember = "PriorityId";
                //    lkpJoPriority.Properties.DataSource = lst_Priority;

                //    lkpJoState.Properties.DisplayMember = "Status";
                //    lkpJoState.Properties.ValueMember = "StatusId";
                //    lkpJoState.Properties.DataSource = lst_Status;

                //    lkpJoDept.Properties.DisplayMember = "Department";
                //    lkpJoDept.Properties.ValueMember = "DeptId";
                //    lkpJoDept.Properties.DataSource = lst_Dept;
                //}
            }
            else
            {
                jo4.Visibility = LayoutVisibility.Never;
                jo3.Visibility = LayoutVisibility.Never;
                jo2.Visibility = LayoutVisibility.Never;
                jo1.Visibility = LayoutVisibility.Never;
            }
            if (itemType)
            {
                itmTyp3.Visibility = LayoutVisibility.Always;
                itmTyp2.Visibility = LayoutVisibility.Always;
                itmTyp1.Visibility = LayoutVisibility.Always;
            }
            else
            {
                itmTyp3.Visibility = LayoutVisibility.Never;
                itmTyp2.Visibility = LayoutVisibility.Never;
                itmTyp1.Visibility = LayoutVisibility.Never;
            }

            if (sellPrice)
            {
                sell4.Visibility = LayoutVisibility.Always;
                sell3.Visibility = LayoutVisibility.Always;
                sell2.Visibility = LayoutVisibility.Always;
                sell1.Visibility = LayoutVisibility.Always;
            }
            else
            {
                sell4.Visibility = LayoutVisibility.Never;
                sell3.Visibility = LayoutVisibility.Never;
                sell2.Visibility = LayoutVisibility.Never;
                sell1.Visibility = LayoutVisibility.Never;
            }


            if (InvBookFltr)
            {
                InvoiceBook4.Visibility = LayoutVisibility.Always;
                InvoiceBook3.Visibility = LayoutVisibility.Always;
                InvoiceBook2.Visibility = LayoutVisibility.Always;
                InvoiceBook1.Visibility = LayoutVisibility.Always;

                //if (lst_invBooks.Count == 0)
                //{

                //    var invs = (from i in DB.ST_InvoiceBooks
                //                join p in DB.LKP_Processes
                //                on i.ProcessId equals p.ProcessId
                //                select new
                //                {
                //                    InvoiceBookId = i.InvoiceBookId,
                //                    InvoiceBookName = p.ProcessName + ": " + i.InvoiceBookName,
                //                    IsTaxable = i.IsTaxable,
                //                    Notes = i.Notes,
                //                    PrintFileName = i.PrintFileName,
                //                    ProcessId = i.ProcessId
                //                }).ToList();
                //    lst_invBooks.Clear();
                //    foreach (var i in invs)
                //    {
                //        lst_invBooks.Add(new ST_InvoiceBook
                //        {
                //            InvoiceBookId = i.InvoiceBookId,
                //            InvoiceBookName = i.InvoiceBookName,
                //            IsTaxable = i.IsTaxable,
                //            ProcessId = i.ProcessId,
                //            PrintFileName = i.PrintFileName,
                //            Notes = i.Notes
                //        });
                //    }

                //    chkLstInvBooks.Properties.DisplayMember = "InvoiceBookName";
                //    chkLstInvBooks.Properties.ValueMember = "InvoiceBookId";
                //    //chkLstInvBooks.Properties.DataSource = lst_invBooks;
                //}
            }
            else
            {
                InvoiceBook4.Visibility = LayoutVisibility.Never;
                InvoiceBook3.Visibility = LayoutVisibility.Never;
                InvoiceBook2.Visibility = LayoutVisibility.Never;
                InvoiceBook1.Visibility = LayoutVisibility.Never;
            }

            if (empGroupFltr && Shared.SalesManAvailable)
            {
                EmpGroup4.Visibility = LayoutVisibility.Always;
                EmpGroup3.Visibility = LayoutVisibility.Always;
                EmpGroup2.Visibility = LayoutVisibility.Always;
                EmpGroup1.Visibility = LayoutVisibility.Always;

                //if (lstEmpGroup.Count == 0)
                //{
                //    lkpEmpGroup.Properties.DisplayMember = "GroupNameAr";
                //    lkpEmpGroup.Properties.ValueMember = "GroupId";
                //    //lkpEmpGroup.Properties.DataSource = lstEmpGroup = DB.HR_Groups.ToList();
                //}
            }
            else
            {
                EmpGroup4.Visibility = LayoutVisibility.Never;
                EmpGroup3.Visibility = LayoutVisibility.Never;
                EmpGroup2.Visibility = LayoutVisibility.Never;
                EmpGroup1.Visibility = LayoutVisibility.Never;
            }

            if (qcFltr)
            {
                QC4.Visibility = LayoutVisibility.Always;
                QC3.Visibility = LayoutVisibility.Always;
                QC2.Visibility = LayoutVisibility.Always;
                QC1.Visibility = LayoutVisibility.Always;
            }
            else
            {
                QC4.Visibility = LayoutVisibility.Never;
                QC3.Visibility = LayoutVisibility.Never;
                QC2.Visibility = LayoutVisibility.Never;
                QC1.Visibility = LayoutVisibility.Never;
            }

            if (vendGroup)
            {
                VenGroup3.Visibility = LayoutVisibility.Always;
                VenGroup2.Visibility = LayoutVisibility.Always;
                VenGroup1.Visibility = LayoutVisibility.Always;

                //if (lst_VenGroup.Count() == 0)
                //{
                //    lst_VenGroup = DAL.MyHelper.GetVendorsGroups();

                //    lkpVenGroup.Properties.DisplayMember = "VGNameAr";
                //    lkpVenGroup.Properties.ValueMember = "VendorGroupId";
                //    lkpVenGroup.Properties.DataSource = lst_VenGroup;
                //}
            }
            else
            {
                VenGroup3.Visibility = LayoutVisibility.Never;
                VenGroup2.Visibility = LayoutVisibility.Never;
                VenGroup1.Visibility = LayoutVisibility.Never;
            }

        }

        void CreateFilterStrings(out string dateFilter, out string otherFilters)
        {
            reportListItem r =
            lstReportListItem.Find(x => x.ReportName == ((reportListItem)lstBxReports.SelectedItem).ReportName);

            otherFilters = "";
            dateFilter = "";

            if (r.DateFltr)
            {
                if (FltrTyp_Date == 1)
                {
                    dateFilter = (Shared.IsEnglish ? ResRptEn.FDate : ResRptAr.FDate) + dateFrom.ToShortDateString();
                }
                else if (FltrTyp_Date == 2)
                {
                    if (dateFrom != Shared.minDate && dateTo != Shared.minDate)
                        dateFilter = (Shared.IsEnglish ? ResRptEn.FFrom : ResRptAr.FFrom) + dateFrom.ToShortDateString()
                            + (Shared.IsEnglish ? ResRptEn.FTo : ResRptAr.FTo) + dateTo.ToShortDateString();
                    else if (dateFrom != Shared.minDate && dateTo == Shared.minDate)
                        dateFilter = (Shared.IsEnglish ? ResRptEn.FfromDate : ResRptAr.FfromDate) + dateFrom.ToShortDateString();
                    else if (dateFrom == Shared.minDate && dateTo != Shared.minDate)
                        dateFilter = (Shared.IsEnglish ? ResRptEn.FtoDate : ResRptAr.FtoDate) + dateTo.ToShortDateString();
                    else
                        dateFilter = "";
                }
                else
                {
                    dateFilter = string.Empty;
                }
            }

            if (r.ItemFltr)
            {
                if (FltrTyp_Item == 0)
                    otherFilters += (Shared.IsEnglish ? ResRptEn.FitemsAll : ResRptAr.FitemsAll) + " ; ";//"اسم الصنف: الكل"
                else if (FltrTyp_Item == 1)
                    otherFilters += (Shared.IsEnglish ? ResRptEn.FitemName : ResRptAr.FitemName) + lkpItem1.Text + " ; ";
            }
            if (r.StoreFltr)
            {
                if (FltrTyp_Store == 0)
                    otherFilters += (Shared.IsEnglish ? ResRptEn.FstoreAll : ResRptAr.FstoreAll) + " ; ";//"المخزن: الكل "
                else if (FltrTyp_Store == 1)
                    otherFilters += (Shared.IsEnglish ? ResRptEn.Fstore : ResRptAr.Fstore) + lkpStore1.Text + " ; ";
                else if (FltrTyp_Store == 2)
                {
                    var stores = DB.IC_Stores.ToList();
                    otherFilters += (Shared.IsEnglish ? ResRptEn.Fstore : ResRptAr.Fstore);
                    foreach (var store in stores)
                    {
                        if (store.StoreId <= (int)lkpStore2.EditValue && store.StoreId >= (int)lkpStore1.EditValue)
                        {
                            otherFilters += string.Format("{0}, ", Shared.IsEnglish ? store.StoreNameEn : store.StoreNameAr);
                        }
                    }
                    otherFilters += "; ";
                }
            }

            if (r.VendorFltr)
            {
                if (FltrTyp_Vendor == 0)
                    otherFilters += (Shared.IsEnglish ? ResRptEn.FvendorAll : ResRptAr.FvendorAll) + " ; ";
                else if (FltrTyp_Vendor == 1)
                    otherFilters += (Shared.IsEnglish ? ResRptEn.Fvendor : ResRptAr.Fvendor) + lkpVendor1.Text + " ; ";
            }

            if (r.CustomerFltr)
            {
                if (FltrTyp_Customer == 0)
                    otherFilters += (Shared.IsEnglish ? ResRptEn.FcustomerAll : ResRptAr.FcustomerAll) + " ; ";
                else if (FltrTyp_Customer == 1)
                    otherFilters += (Shared.IsEnglish ? ResRptEn.Fcustomer : ResRptAr.Fcustomer) + lkpCustomer1.Text + " ; ";
            }

            if (r.CompanyFltr)
            {
                if (FltrTyp_Company == 0)
                    otherFilters += (Shared.IsEnglish ? ResRptEn.FcompAll : ResRptAr.FcompAll) + " ; ";
                else if (FltrTyp_Company == 1)
                    otherFilters += (Shared.IsEnglish ? ResRptEn.Fcomp : ResRptAr.Fcomp) + lkpCompany.Text + " ; ";
            }

            if (r.CategoryFltr)
            {
                if (FltrTyp_Category == 0)
                    otherFilters += (Shared.IsEnglish ? ResRptEn.FcatAll : ResRptAr.FcatAll) + " ; ";
                else if (FltrTyp_Category == 1)
                    otherFilters += (Shared.IsEnglish ? ResRptEn.Fcat : ResRptAr.Fcat) + lkpCategory.Text + " ; ";
            }

            if (r.ExpDateFltr)
            {
                if (dtExpDate.Visible == true)
                    otherFilters += (Shared.IsEnglish ? ResRptEn.FdateBefore : ResRptAr.FdateBefore)
                        + dtExpDate.DateTime.Month.ToString() + "/" + dtExpDate.DateTime.Year.ToString() + " ; ";
            }

            if (r.CostCenterFltr)
            {
                otherFilters += (Shared.IsEnglish ? ResRptEn.FcostCenter : ResRptAr.FcostCenter) + lkpCostCenters.Text + " ; ";
            }
            if (r.AccountFltr)
            {
                otherFilters += (Shared.IsEnglish ? ResRptEn.Faccount : ResRptAr.Faccount) + lkpAccount.Text + " ; ";
            }
            if (r.CustmAccListFltr)
            {
                otherFilters += (Shared.IsEnglish ? ResRptEn.FcustomList : ResRptAr.FcustomList) + lkpCustomList.Text + " ; ";
            }

            if (r.UserFltr)
            {
                if (FltrTyp_User == 0)
                    otherFilters += (Shared.IsEnglish ? ResRptEn.FuserAll : ResRptAr.FuserAll) + " ; ";
                else if (FltrTyp_User == 1)
                    otherFilters += (Shared.IsEnglish ? ResRptEn.Fuser : ResRptAr.Fuser) + lkpUser.Text + " ; ";
            }

            if (r.EmpGroupFltr)
            {
                if (FltrTyp_EmpGroup == 0)
                    otherFilters += (Shared.IsEnglish ? ResRptEn.FempGroupAll : ResRptAr.FempGroupAll) + " ; ";
                else if (FltrTyp_EmpGroup == 1)
                    otherFilters += (Shared.IsEnglish ? ResRptEn.FempGroup : ResRptAr.FempGroup) + lkpEmpGroup.Text + " ; ";
            }

            if (r.SalesEmpFltr)
            {
                if (FltrTyp_SalesEmp == 0)
                    otherFilters += (Shared.IsEnglish ? ResRptEn.FsalesEmpAll : ResRptAr.FsalesEmpAll) + " ; ";
                else if (FltrTyp_SalesEmp == 1)
                    otherFilters += (Shared.IsEnglish ? ResRptEn.FsalesEmp : ResRptAr.FsalesEmp) + lkpSalesEmp.Text + " ; ";
            }

            if (r.BatchFltr)
            {
                otherFilters += (Shared.IsEnglish ? ResRptEn.Batch : ResRptAr.Batch) + txtBatch.Text + " ; ";
                if (!string.IsNullOrEmpty(txtBatch.Text))
                    otherFilters += (Shared.IsEnglish ? ResRptEn.Batch : ResRptAr.Batch) + txtBatch.Text + " ; ";
            }

            if (r.CutGroupFltr)
            {

                if (lkpCustGroup.Properties.View.OptionsSelection.MultiSelect == true)
                {
                    string CGroups = "";
                    var rows = gridView9.GetSelectedRows();
                    if (rows.Count() > 0)
                    {
                        foreach (var i in rows)
                        {
                            CGroups = string.Format("{1}{0}, ", CGroups, gridView9.GetRowCellValue(i, gridColumn17).ToString());
                        }
                        otherFilters += (Shared.IsEnglish ? ResRptEn.FCustGroup : ResRptAr.FCustGroup) + CGroups + " ; ";
                    }
                    else
                    {
                        otherFilters += (Shared.IsEnglish ? ResRptEn.FcustGroupAll : ResRptAr.FcustGroupAll) + " ; ";
                    }
                }
                else if (custGroupId == 0)
                    otherFilters += (Shared.IsEnglish ? ResRptEn.FcustGroupAll : ResRptAr.FcustGroupAll) + " ; ";
                else
                    otherFilters += (Shared.IsEnglish ? ResRptEn.FCustGroup : ResRptAr.FCustGroup) + lkpCustGroup.Text + " ; ";
            }
            if (r.JobOrderFltr)
            {
                if (PriorityId == 0)
                    otherFilters += (Shared.IsEnglish ? ResRptEn.FjoPriorityAll : ResRptAr.FjoPriorityAll) + " ; ";
                else
                    otherFilters += (Shared.IsEnglish ? ResRptEn.FjoPriority : ResRptAr.FjoPriority) + lkpJoPriority.Text + " ; ";

                if (StatusId == 0)
                    otherFilters += (Shared.IsEnglish ? ResRptEn.FjoStatusAll : ResRptAr.FjoStatusAll) + " ; ";
                else
                    otherFilters += (Shared.IsEnglish ? ResRptEn.FjoStatus : ResRptAr.FjoStatus) + lkpJoState.Text + " ; ";

                if (DeptId == 0)
                    otherFilters += (Shared.IsEnglish ? ResRptEn.FjoDeptAll : ResRptAr.FjoDeptAll) + " ; ";
                else
                    otherFilters += (Shared.IsEnglish ? ResRptEn.FjoDept : ResRptAr.FjoDept) + lkpJoDept.Text + " ; ";
            }
            if (r.ItemTypeFltr)
            {
                if (cmbItemType.SelectedIndex == 0)
                    otherFilters += (Shared.IsEnglish ? ResRptEn.FitemTypeAll : ResRptAr.FitemTypeAll) + " ; ";
                else
                    otherFilters += (Shared.IsEnglish ? ResRptEn.FitemType : ResRptAr.FitemType) + cmbItemType.Text + " ; ";
            }
            if (r.InvBookFltr)
            {
                if (FltrTyp_InvBook == 0)
                    otherFilters += (Shared.IsEnglish ? ResRptEn.FInvBookAll : ResRptAr.FInvBookAll) + " ; ";
                else if (FltrTyp_InvBook == 1)
                    otherFilters += (Shared.IsEnglish ? ResRptEn.FInvBook : ResRptAr.FInvBook) + chkLstInvBooks.Text + " ; ";
            }
            if (r.VenGroupFltr)
            {
                if (lkpVenGroup.Properties.View.OptionsSelection.MultiSelect == true)
                {
                    string CGroups = "";
                    var rows = gridView17.GetSelectedRows();
                    if (rows.Count() > 0)
                    {
                        foreach (var i in rows)
                        {
                            CGroups = string.Format("{1}{0}, ", CGroups, gridView9.GetRowCellValue(i, gridColumn39).ToString());
                        }
                        otherFilters += (Shared.IsEnglish ? ResRptEn.FvenGroup : ResRptAr.FvenGroup) + CGroups + " ; ";
                    }
                    else
                    {
                        otherFilters += (Shared.IsEnglish ? ResRptEn.FvenGroupAll : ResRptAr.FvenGroupAll) + " ; ";
                    }
                }
                else if (VenGroupId == 0)
                    otherFilters += (Shared.IsEnglish ? ResRptEn.FvenGroupAll : ResRptAr.FvenGroupAll) + " ; ";
                else
                    otherFilters += (Shared.IsEnglish ? ResRptEn.FvenGroup : ResRptAr.FvenGroup) + lkpVenGroup.Text + " ; ";
            }
        }

        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void lkp_Customer_CustomRowFilter(object sender, DevExpress.XtraGrid.Views.Base.RowFilterEventArgs e)
        {
            if (custGroupId > 0 && lst_Customers[e.ListSourceRow].GrpId != custGroupId)
            {
                e.Visible = false;
                e.Handled = true;
            }
        }

        private void grdVAccount_CustomRowFilter(object sender, DevExpress.XtraGrid.Views.Base.RowFilterEventArgs e)
        {
            //show pay accounts only
            //if (ShowAccount == AccountTypeToShow.PayAccounts &&
            //    !lstPayAccounts.Contains(lst_Accounts[e.ListSourceRow].AccId))
            //{
            //    e.Visible = false;
            //    e.Handled = true;
            //}

            //show accounts with cost centers
            //if (ShowAccount == AccountTypeToShow.WithCostCenter && lst_Accounts[e.ListSourceRow].CostCenter == null)
            //{
            //    e.Visible = false;
            //    e.Handled = true;
            //}
        }



        private void barBtn_Open_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                if (lstBxReports.SelectedIndex >= 0)
                {
                    layoutControl1.SelectNextControl(layoutControl1, false, false, true, true);
                    CreateFilterStrings(out dateFilter, out otherFilters);

                    txtDescription.Focus();
                    this.Focus();

                    string reportCaption = ((reportListItem)lstBxReports.SelectedItem).ReportCaption;

                    

                 

                    #region SL
                    if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_SL_InvoicesHeaders")
                    {
                        frm_SL_InvoicesHeaders rprt = new frm_SL_InvoicesHeaders(reportCaption, dateFilter, otherFilters,
                            FltrTyp_Store, storeId1, storeId2,
                            FltrTyp_Date, dateFrom, dateTo,
                            FltrTyp_Customer, customerId1, customerId2,
                            custGroupId, custGroupAccNumber, salesEmpId, FltrTyp_InvBook, InvBooks);
                        if (rprt.UserCanOpen)
                        {
                            rprt.BringToFront();
                            rprt.Show();
                        }
                    }
                    if (((reportListItem)lstBxReports.SelectedItem).ReportName == "frm_SL_E_Invoice")
                    {
                          frm_SL_E_Invoice rprt = new frm_SL_E_Invoice(reportCaption, dateFilter, otherFilters,
                            FltrTyp_Store, storeId1, storeId2,
                            FltrTyp_Date, dateFrom, dateTo,
                            FltrTyp_Customer, customerId1, customerId2,
                            custGroupId, custGroupAccNumber, salesEmpId, FltrTyp_InvBook, InvBooks);
                        if (rprt.UserCanOpen)
                        {
                            rprt.BringToFront();
                            rprt.Show();
                        }
                    }
                    else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_SL_CustomerInvoicesHeaders")
                    {
                        frm_SL_CustomerInvoicesHeaders rprt = new frm_SL_CustomerInvoicesHeaders(reportCaption, dateFilter, otherFilters,
                            FltrTyp_Store, storeId1, storeId2,
                            FltrTyp_Date, dateFrom, dateTo,
                            FltrTyp_Customer, customerId1, customerId2,
                            custGroupId, custGroupAccNumber, salesEmpId, FltrTyp_InvBook, InvBooks);
                        if (rprt.UserCanOpen)
                        {
                            rprt.BringToFront();
                            rprt.Show();
                        }
                    }
                    else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_SL_ReturnHeaders")
                    {
                        frm_SL_ReturnHeaders rprt = new frm_SL_ReturnHeaders(reportCaption, dateFilter, otherFilters,
                            FltrTyp_Store, storeId1, storeId2,
                            FltrTyp_Date, dateFrom, dateTo,
                            FltrTyp_Customer, customerId1, customerId2,
                            custGroupId, custGroupAccNumber,
                            salesEmpId, FltrTyp_InvBook, InvBooks);
                        if (rprt.UserCanOpen)
                        {
                            rprt.BringToFront();
                            rprt.Show();
                        }
                    }
                    else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "frm_SubTaxNet")
                    {
                        frm_SubTaxNet rprt = new frm_SubTaxNet(reportCaption, dateFilter, otherFilters,      
                            dateFrom, dateTo, FltrTyp_Date);
                        if (rprt.UserCanOpen)
                        {
                            rprt.BringToFront();
                            rprt.Show();
                        }
                    }
                    else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "frm_SubTaxTotal")
                    {
                        frm_SubTaxTotal rprt = new frm_SubTaxTotal(reportCaption, dateFilter, otherFilters,
                            dateFrom, dateTo, FltrTyp_Date);
                        if (rprt.UserCanOpen)
                        {
                            rprt.BringToFront();
                            rprt.Show();
                        }
                    }
                    //else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_SL_ItemsSales")
                    //{
                    //    frm_SL_ItemsSales rprt = new frm_SL_ItemsSales(reportCaption, dateFilter, otherFilters,
                    //        FltrTyp_Store, storeId1, storeId2,
                    //        FltrTyp_Company, companyId,
                    //        FltrTyp_Category, categoryNum,
                    //        FltrTyp_Item, itemId1, itemId2,
                    //        FltrTyp_Date, dateFrom, dateTo, salesEmpId, FltrTyp_InvBook, InvBooks);
                    //    if (rprt.UserCanOpen)
                    //    {
                    //        rprt.BringToFront();
                    //        rprt.Show();
                    //    }
                    //}
                    //else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_SL_ItemsReturn")
                    //{
                    //    frm_SL_ItemsReturn rprt = new frm_SL_ItemsReturn(reportCaption, dateFilter, otherFilters,
                    //        FltrTyp_Store, storeId1, storeId2,
                    //        FltrTyp_Company, companyId,
                    //        FltrTyp_Category, categoryNum,
                    //        FltrTyp_Item, itemId1, itemId2,
                    //        FltrTyp_Date, dateFrom, dateTo, salesEmpId, FltrTyp_InvBook, InvBooks);
                    //    if (rprt.UserCanOpen)
                    //    {
                    //        rprt.BringToFront();
                    //        rprt.Show();
                    //    }
                    //}

                    else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_SL_CustomerTotal_Invoices")
                    {
                        frm_SL_CustomerTotal_Invoices rprt = new frm_SL_CustomerTotal_Invoices(reportCaption, dateFilter, otherFilters,
                            FltrTyp_Store, storeId1, storeId2,
                            FltrTyp_Date, dateFrom, dateTo,
                            FltrTyp_Customer, customerId1, customerId2,
                            custGroupId, custGroupAccNumber, salesEmpId, FltrTyp_InvBook, InvBooks);
                        if (rprt.UserCanOpen)
                        {
                            rprt.BringToFront();
                            rprt.Show();
                        }
                    }

                    else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "frm_IC_StoreSales")
                    {
                        frm_IC_StoreSales rprt = new frm_IC_StoreSales(reportCaption, dateFilter, otherFilters,
                            FltrTyp_Store, storeId1, storeId2,
                            FltrTyp_Date, dateFrom, dateTo,
                            custGroupId, custGroupAccNumber);
                        if (rprt.UserCanOpen)
                        {
                            rprt.BringToFront();
                            rprt.Show();
                        }
                    }

                    else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "frm_IC_StoreSales")
                    {
                        frm_IC_StoreSales rprt = new frm_IC_StoreSales(reportCaption, dateFilter, otherFilters,
                            FltrTyp_Store, storeId1, storeId2,
                            FltrTyp_Date, dateFrom, dateTo,
                            VenGroupId, venGroupAccNumber);
                        if (rprt.UserCanOpen)
                        {
                            rprt.BringToFront();
                            rprt.Show();
                        }
                    }


                    else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_SL_ItemTrade")
                    {
                        if (lkpItem1.EditValue == null || string.IsNullOrEmpty(lkpItem1.EditValue.ToString().Trim()))
                        {
                            DevExpress.XtraEditors.XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.ValItem : ResRptAr.ValItem
                            , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            lkpItem1.Focus();
                            return;
                        }
                        if (lkpStore1.EditValue == null || string.IsNullOrEmpty(lkpStore1.EditValue.ToString().Trim())
                            || lkpStore1.EditValue.ToString() == "0")
                        {
                            DevExpress.XtraEditors.XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.ValStore : ResRptAr.ValStore
                            , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            lkpStore1.Focus();
                            return;
                        }
                        rpt_SL_ItemTrade rprt = new rpt_SL_ItemTrade(reportCaption, dateFilter, otherFilters,
                             storeId1, itemId1);
                        if (rprt.UserCanOpen)
                            new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                        //rprt.ShowPreview();
                    }

                   else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "frm_SL_Invoices_Due")
                    {
                        frm_SL_Invoices_Due rprt = new frm_SL_Invoices_Due(reportCaption, dateFilter, otherFilters,
                            FltrTyp_Store, storeId1, storeId2,
                            FltrTyp_Date, dateFrom, dateTo,
                            FltrTyp_Customer, customerId1, customerId2,
                            custGroupId, custGroupAccNumber, salesEmpId, FltrTyp_InvBook, InvBooks);
                        if (rprt.UserCanOpen)
                        {
                            rprt.BringToFront();
                            rprt.Show();
                        }
                    }

                  
                    //else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_SL_CustomerDiscount")
                    //{
                    //    rpt_SL_CustomerDiscount rprt = new rpt_SL_CustomerDiscount(reportCaption, dateFilter, otherFilters,
                    //        FltrTyp_Date, dateFrom, dateTo,
                    //        FltrTyp_Customer, customerId1, customerId2,
                    //        custGroupId, custGroupAccNumber,
                    //        salesEmpId, FltrTyp_InvBook, InvBooks);
                    //    if (rprt.UserCanOpen)
                    //        //new rpt_Template(reportCaption, dateFilter, otherFilters, rprt, true).ShowPreview();
                    //        rprt.Show();
                    //    //rprt.ShowPreview();
                    //}
                    else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_SL_CustomerItemsSales")
                    {
                        string accNum = string.Empty;
                        //if (lkpCostCenters.EditValue != null)
                        //    accNum = lst_CostCenters.Where(x => x.CostCenterId == Convert.ToInt32(lkpCostCenters.EditValue)).Select(x => x.ccNumber).FirstOrDefault();
                        frm_SL_CustomerItemsSales rprt = new frm_SL_CustomerItemsSales(reportCaption, dateFilter, otherFilters,
                            FltrTyp_Item, itemId1, itemId2,
                            FltrTyp_Date, dateFrom, dateTo,
                            FltrTyp_Customer, customerId1, customerId2,
                            FltrTyp_SellPrice, Convert.ToDecimal(txtSellPrice1.Text), Convert.ToDecimal(txtSellPrice2.Text),
                            FltrTyp_Category, categoryNum,
                            custGroupId, custGroupAccNumber,
                            FltrTyp_Store, storeId1, storeId2,
                            salesEmpId, Process.SellInvoice, FltrTyp_InvBook, InvBooks, FltrTyp_Company, companyId);
                        if (rprt.UserCanOpen)
                            //new rpt_Template(reportCaption, dateFilter, otherFilters, rprt, true).ShowPreview();
                            rprt.Show();
                        //rprt.ShowPreview();
                    }
                    else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_SL_CustomerItemsSalesReturn")
                    {
                        frm_SL_CustomerItemsSales rprt = new frm_SL_CustomerItemsSales(reportCaption, dateFilter, otherFilters,
                            FltrTyp_Item, itemId1, itemId2,
                            FltrTyp_Date, dateFrom, dateTo,
                            FltrTyp_Customer, customerId1, customerId2,
                            FltrTyp_SellPrice, Convert.ToDecimal(txtSellPrice1.Text), Convert.ToDecimal(txtSellPrice2.Text),
                            FltrTyp_Category, categoryNum,
                            custGroupId, custGroupAccNumber,
                            FltrTyp_Store, storeId1, storeId2,
                            salesEmpId, Process.SellReturn, FltrTyp_InvBook, InvBooks, FltrTyp_Company, companyId);
                        if (rprt.UserCanOpen)
                            //new rpt_Template(reportCaption, dateFilter, otherFilters, rprt, true).ShowPreview();
                            rprt.Show();
                        //rprt.ShowPreview();
                    }

                    else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_SL_CustomerTotal_Invoices")
                    {
                        frm_SL_CustomerTotal_Invoices rprt = new frm_SL_CustomerTotal_Invoices(reportCaption, dateFilter, otherFilters,
                            FltrTyp_Store, storeId1, storeId2,
                            FltrTyp_Date, dateFrom, dateTo,
                            FltrTyp_Customer, customerId1, customerId2,
                            custGroupId, custGroupAccNumber, salesEmpId, FltrTyp_InvBook, InvBooks);
                        if (rprt.UserCanOpen)
                        {
                            rprt.BringToFront();
                            rprt.Show();
                        }
                    }

                    else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_SL_CustomerTrans")
                    {
                        if (lkpCustomer1.EditValue == null || string.IsNullOrEmpty(lkpCustomer1.EditValue.ToString().Trim())
                             || lkpCustomer1.EditValue.ToString() == "0")
                        {
                            DevExpress.XtraEditors.XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.ValCustomer : ResRptAr.ValCustomer,
                            "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            lkpStore1.Focus();
                            return;
                        }

                        rpt_SL_CustomerTrans rprt = new rpt_SL_CustomerTrans(reportCaption, dateFilter, otherFilters,
                            FltrTyp_Date, dateFrom, dateTo, customerId1);
                        if (rprt.UserCanOpen)
                            new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    }
                    else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_SL_CustomerItemsSales_OutTrns")
                    {
                        frm_SL_CustomerItemsSales_OutTrns rprt = new frm_SL_CustomerItemsSales_OutTrns(reportCaption, dateFilter, otherFilters,
                            FltrTyp_Item, itemId1, itemId2,
                            FltrTyp_Date, dateFrom, dateTo, FltrTyp_Customer, customerId1, customerId2, custGroupId,
                            custGroupAccNumber, FltrTyp_Store, storeId1, storeId2, FltrTyp_Company, companyId);
                        if (rprt.UserCanOpen)
                        {
                            rprt.BringToFront();
                            rprt.Show();
                        }
                    }
                    else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "frm_SL_Warranty")
                    {
                        frm_SL_Warranty rprt = new frm_SL_Warranty(reportCaption, dateFilter, otherFilters,
                            FltrTyp_Item, itemId1, itemId2,
                            FltrTyp_Date, dateFrom, dateTo,
                            FltrTyp_Customer, customerId1, customerId2,
                            FltrTyp_Category, categoryNum,
                            custGroupId, custGroupAccNumber,
                            FltrTyp_Store, storeId1, storeId2,
                            salesEmpId, FltrTyp_InvBook, InvBooks, FltrTyp_Company, companyId);
                        if (rprt.UserCanOpen)
                            //new rpt_Template(reportCaption, dateFilter, otherFilters, rprt, true).ShowPreview();
                            rprt.Show();
                        //rprt.ShowPreview();
                    }
                    else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "frm_SL_Car_Weights")
                    {
                        frm_SL_Car_Weights rprt = new frm_SL_Car_Weights(reportCaption, dateFilter, otherFilters,
                            FltrTyp_Item, itemId1, itemId2,
                            FltrTyp_Date, dateFrom, dateTo,
                            FltrTyp_Customer, customerId1, customerId2,
                            FltrTyp_Category, categoryNum,
                            custGroupId, custGroupAccNumber,
                            FltrTyp_Store, storeId1, storeId2,
                            salesEmpId, FltrTyp_InvBook, InvBooks, FltrTyp_Company, companyId,
                            fltrtype_Car, PlateNo);
                        if (rprt.UserCanOpen)
                            //new rpt_Template(reportCaption, dateFilter, otherFilters, rprt, true).ShowPreview();
                            rprt.Show();
                        //rprt.ShowPreview();
                    }


                    else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "frm_SL_DelegatesSales")
                    {

                        frm_SL_DelegatesSales rprt = new frm_SL_DelegatesSales(reportCaption, dateFilter, otherFilters,
                        FltrTyp_Item, itemId1, itemId2,
                        FltrTyp_Date, dateFrom, dateTo,
                        FltrTyp_Customer, customerId1, customerId2,
                        FltrTyp_Category, categoryNum,
                        custGroupId, custGroupAccNumber,
                        FltrTyp_Store, storeId1, storeId2,
                        salesEmpId, FltrTyp_InvBook, InvBooks, FltrTyp_Company, companyId);
                        if (rprt.UserCanOpen)
                            //new rpt_Template(reportCaption, dateFilter, otherFilters, rprt, true).ShowPreview();
                            rprt.Show();
                        //rprt.ShowPreview();
                    }

                   else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "frm_SL_DeliveryOfficialsSales")
                    {
                        frm_SL_DeliveryOfficialsSales rprt = new frm_SL_DeliveryOfficialsSales(reportCaption, dateFilter, otherFilters,
                            FltrTyp_Item, itemId1, itemId2,
                            FltrTyp_Date, dateFrom, dateTo,
                            FltrTyp_Customer, customerId1, customerId2,
                            FltrTyp_Category, categoryNum,
                            custGroupId, custGroupAccNumber,
                            FltrTyp_Store, storeId1, storeId2,
                            salesEmpId, FltrTyp_InvBook, InvBooks, FltrTyp_Company, companyId);
                        if (rprt.UserCanOpen)
                            //new rpt_Template(reportCaption, dateFilter, otherFilters, rprt, true).ShowPreview();
                            rprt.Show();
                        //rprt.ShowPreview();
                    }

                    else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "frm_CapitalInDeferredInvoices")
                    {

                        frm_CapitalInDeferredInvoices rprt = new frm_CapitalInDeferredInvoices(reportCaption, dateFilter, otherFilters,
                        /*FltrTyp_Item, itemId1, itemId2,*/
                        FltrTyp_Date, dateFrom, dateTo,
                        FltrTyp_Customer, customerId1, customerId2
                        //,FltrTyp_Category, categoryNum,
                        //custGroupId, custGroupAccNumber,
                        //FltrTyp_Store, storeId1, storeId2,
                        //salesEmpId, FltrTyp_InvBook, InvBooks, FltrTyp_Company, companyId
                        );
                        if (rprt.UserCanOpen)
                        {
                            //new rpt_Template(reportCaption, dateFilter, otherFilters, rprt, true).ShowPreview();

                            rprt.Show();

                            //rprt.ShowPreview();
                        }

                    }

                    else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "frm_SL_Profit_Loss")
                    {

                        frm_SL_Profit_Loss rprt = new frm_SL_Profit_Loss(reportCaption, dateFilter, otherFilters,
                        FltrTyp_Store, storeId1, storeId2,
                        FltrTyp_Date, dateFrom, dateTo,
                        FltrTyp_Customer, customerId1, customerId2,
                        custGroupId, custGroupAccNumber, salesEmpId); ;

                        if (rprt.UserCanOpen)
                        {
                            //new rpt_Template(reportCaption, dateFilter, otherFilters, rprt, true).ShowPreview();

                            rprt.Show();

                            //rprt.ShowPreview();
                        }

                    }
                    //if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_SL_averagesellingpriceoftheitems")
                    //{
                    //    frm_SL_averagesellingpriceoftheitems rprt = new frm_SL_averagesellingpriceoftheitems(reportCaption, dateFilter, otherFilters, FltrTyp_Store, storeId1, storeId2,
                    //        FltrTyp_Company, companyId, FltrTyp_Category, categoryNum, FltrTyp_Item, itemId1, itemId2, dateFrom,
                    //        MtrxParentId, M1, M2, M3);
                    //    if (rprt.UserCanOpen)
                    //    {
                    //        rprt.BringToFront();
                    //        rprt.Show();
                    //    }
                    //}

                    //if (((reportListItem)lstBxReports.SelectedItem).ReportName == "frm_Customers_Debit")
                    //{
                    //    frm_Customers_Debit rprt = new frm_Customers_Debit(reportCaption, dateFilter, otherFilters,
                    //        dateFrom, dateTo, true, FltrTyp_Customer, customerId1, customerId2, custGroupAccNumber, salesEmpId, Selected_Customers_Vendors.ToArray(), Customers_Vendors_Categories.ToArray());
                    //    if (rprt.UserCanOpen)
                    //    {
                    //        rprt.BringToFront();
                    //        rprt.Show();
                    //    }
                    //}

                    else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_InvoiceDetails")
                    {
                        frm_SL_InvoiceDetails rprt = new frm_SL_InvoiceDetails(reportCaption, dateFilter, otherFilters,
                            FltrTyp_Store, storeId1, storeId2,
                            FltrTyp_Date, dateFrom, dateTo,
                            FltrTyp_Customer, customerId1, customerId2,
                            custGroupId, custGroupAccNumber, salesEmpId, FltrTyp_InvBook, InvBooks,OpenSourceProcess);
                        if (rprt.UserCanOpen)
                        {
                            rprt.BringToFront();
                            rprt.Show();
                        }
                    }
                    #endregion

                   

              

                }
            }
            catch (Exception ex) { var x = ex; }
        }
    }

    class reportListItem
    {
        string reportName;
        string reportCaption;
        string description;
        int tabIndex;
        bool itemFltr;
        bool dateFltr;
        bool expDateFltr;
        bool storeFltr;
        bool vendorFltr;
        bool customerFltr;
        bool comapnyFltr;
        bool categoryFltr;
        bool processFltr;
        bool costCenterFltr;
        bool accountFltr;
        bool custmAccListFltr;
        bool userFltr;
        bool salesEmpFltr;
        bool batchFltr;
        bool cutGroupFltr;
        bool mtrxFltr;
        bool dimensionFltr;
        bool jobOrderFltr;
        bool itemTypeFltr;
        bool sellPriceFltr;
        bool invBookFltr;
        bool empGroupFltr;
        bool qcFltr;
        bool venGroupFltr;
        bool cars = false;

        public bool VenGroupFltr
        {
            get { return venGroupFltr; }
            set { venGroupFltr = value; }
        }

        public bool QcFltr
        {
            get { return qcFltr; }
            set { qcFltr = value; }
        }

        public bool EmpGroupFltr
        {
            get { return empGroupFltr; }
            set { empGroupFltr = value; }
        }

        public bool InvBookFltr
        {
            get { return invBookFltr; }
            set { invBookFltr = value; }
        }

        public bool SellPriceFltr
        {
            get { return sellPriceFltr; }
            set { sellPriceFltr = value; }
        }

        public bool ItemTypeFltr
        {
            get { return itemTypeFltr; }
            set { itemTypeFltr = value; }
        }

        public bool JobOrderFltr
        {
            get { return jobOrderFltr; }
            set { jobOrderFltr = value; }
        }

        public bool MtrxFltr
        {
            get { return mtrxFltr; }
            set { mtrxFltr = value; }
        }

        public bool DimensionFltr
        {
            get { return dimensionFltr; }
            set { dimensionFltr = value; }
        }

        public bool CutGroupFltr
        {
            get { return cutGroupFltr; }
            set { cutGroupFltr = value; }
        }

        public bool BatchFltr
        {
            get { return batchFltr; }
            set { batchFltr = value; }
        }

        public bool SalesEmpFltr
        {
            get { return salesEmpFltr; }
            set { salesEmpFltr = value; }
        }

        public bool UserFltr
        {
            get { return userFltr; }
            set { userFltr = value; }
        }

        public bool ExpDateFltr
        {
            get { return expDateFltr; }
            set { expDateFltr = value; }
        }
        public bool CategoryFltr
        {
            get { return categoryFltr; }
            set { categoryFltr = value; }
        }
        public bool CompanyFltr
        {
            get { return comapnyFltr; }
            set { comapnyFltr = value; }
        }

        public string ReportName
        {
            get { return reportName; }
            set { reportName = value; }
        }

        public string ReportCaption
        {
            get { return reportCaption; }
            set { reportCaption = value; }
        }

        public string Description
        {
            get { return description; }
            set { description = value; }
        }

        public int TabIndex
        {
            get { return tabIndex; }
            set { tabIndex = value; }
        }

        public bool ItemFltr
        {
            get { return itemFltr; }
            set { itemFltr = value; }
        }

        public bool DateFltr
        {
            get { return dateFltr; }
            set { dateFltr = value; }
        }

        public bool StoreFltr
        {
            get { return storeFltr; }
            set { storeFltr = value; }
        }

        public bool VendorFltr
        {
            get { return vendorFltr; }
            set { vendorFltr = value; }
        }

        public bool CustomerFltr
        {
            get { return customerFltr; }
            set { customerFltr = value; }
        }

        public bool ProcessFltr
        {
            get { return processFltr; }
            set { processFltr = value; }
        }
        public bool AccountFltr
        {
            get { return accountFltr; }
            set { accountFltr = value; }
        }

        public bool CostCenterFltr
        {
            get { return costCenterFltr; }
            set { costCenterFltr = value; }
        }

        public bool CustmAccListFltr
        {
            get { return custmAccListFltr; }
            set { custmAccListFltr = value; }
        }

        public bool CarsFltr
        {
            get { return cars; }
            set { cars = value; }
        }
    }

    enum AccountTypeToShow
    {
        WithCostCenter = 0,
        PayAccounts = 1
    }
}