using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using DAL;
using System.Linq;
using System.Data;
using DevExpress.XtraEditors;

using System.Windows.Forms;

namespace Reports
{
    public partial class rpt_IC_InTrns : DevExpress.XtraReports.UI.XtraReport
    {
        string vendor, serial, number, date, store, totalP, totalS, notes,userName;

        DataTable dt_inv_details;
        int currId;
        double itms_totalQty;

        public rpt_IC_InTrns()
        {
            InitializeComponent();
        }
        public rpt_IC_InTrns(string _vendor, string _serial, string _number, string _date, string _store, string _totalP,
            string _totalS, string _notes, DataTable dt, string userName,int _currId,double itms_totalQty)
        {
            InitializeComponent();
            vendor = _vendor;
            serial = _serial;
            number = _number;
            date = _date;
            store = _store;
            notes = _notes;

            this.userName = userName;
            totalP = _totalP;
            totalS = _totalS;

            currId = _currId;

            this.itms_totalQty = itms_totalQty;

            dt_inv_details = dt;
            this.DataSource = dt_inv_details;
            LoadData();       
        }        

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                lblCompName.Text = comp.CmpNameAr;                
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }            
        }

        public void LoadData()
        {
            lbl_Vendor.Text = vendor;
            lbl_Serial.Text = serial;
            lbl_Number.Text = number;
            lbl_date.Text = date;
            lbl_store.Text = store;
            lbl_notes.Text = notes;
            lbl_User.Text = userName;

            lbl_Total_P_Price.Text = totalP;
            lbl_Total_S_Price.Text = totalS;
            lblTotal_P_Words.Text = Shared.IsEnglish ? HelperAcc.ConvertMoneyToText(totalP, currId, Shared.lstCurrency) :
                       HelperAcc.ConvertMoneyToArabicText(totalP, currId, Shared.lstCurrency);
            lblTotal_S_Words.Text = Shared.IsEnglish ? HelperAcc.ConvertMoneyToText(totalS, currId, Shared.lstCurrency) :
                       HelperAcc.ConvertMoneyToArabicText(totalS, currId, Shared.lstCurrency);

            this.DataSource = dt_inv_details;

            cell_code.DataBindings.Add("Text", this.DataSource, "ItemCode1");
            cell_code2.DataBindings.Add("Text", this.DataSource, "ItemCode2");
            cell_ItemName.DataBindings.Add("Text", this.DataSource, "ItemName");
            cell_UOM.DataBindings.Add("Text", this.DataSource, "UOM");
            cell_MediumUOM.DataBindings.Add("Text", this.DataSource, "MediumUOM");
            cell_MediumUOMFactor.DataBindings.Add("Text", this.DataSource, "MediumUOMQty");
            cell_Qty.DataBindings.Add("Text", this.DataSource, "Qty");
            cell_Expire.DataBindings.Add("Text", this.DataSource, "Expire");
            cell_Batch.DataBindings.Add("Text", this.DataSource, "Batch");
            cell_P_Price.DataBindings.Add("Text", this.DataSource, "PurchasePrice");
            cell_S_Price.DataBindings.Add("Text", this.DataSource, "SellPrice");
            cell_Total_P_Price.DataBindings.Add("Text", this.DataSource, "TotalPurchasePrice");
            cell_Total_S_Price.DataBindings.Add("Text", this.DataSource, "TotalSellPrice");

            cell_Height.DataBindings.Add("Text", this.DataSource, "Height");
            cell_Width.DataBindings.Add("Text", this.DataSource, "Width");
            cell_Length.DataBindings.Add("Text", this.DataSource, "Length");
            cell_TotalQty.DataBindings.Add("Text", this.DataSource, "TotalQty");
            cell_Serial.DataBindings.Add("Text", this.DataSource, "Serial");
            cell_ManufactureDate.DataBindings.Add("Text", this.DataSource, "ManufactureDate");
            cell_Weight.DataBindings.Add("Text", this.DataSource, "kg_Weight_libra");
            cell_PieceCount.DataBindings.Add("Text", this.DataSource, "PiecesCount");
            cell_Company.DataBindings.Add("Text", this.DataSource, "Company");
            cell_Category.DataBindings.Add("Text", this.DataSource, "Category");

            lbl_itms_totalQty.Text = itms_totalQty.ToString();

            decimal totalPc = 0;
            decimal total_Packs = 0;
            decimal total_Qtys = 0;
            //totalQty = dt_inv_details.Compute("Sum(Qty)", string.Empty);
            foreach (DataRow row in dt_inv_details.Rows)
            {
                totalPc += Convert.ToDecimal(row["PiecesCount"]);
                total_Qtys += Convert.ToDecimal(row["Qty"]);

                if (row["Pack"] != null && !string.IsNullOrEmpty(Convert.ToString(row["Pack"])) && row["Pack"] != DBNull.Value)
                    total_Packs += Convert.ToDecimal(row["Pack"]);
            }

            lbl_totalPieces.Text = totalPc.ToString();
            lbl_totalQtys.Text = total_Qtys.ToString();
            lbl_TotalPacks.Text = total_Packs.ToString();
            getReportHeader();
        }

    }
}
