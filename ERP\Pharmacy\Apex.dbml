﻿<?xml version="1.0" encoding="utf-8"?><Database Name="apexdata" Class="ApexDataContext" xmlns="http://schemas.microsoft.com/linqtosql/dbml/2007">
  <Connection Mode="AppSettings" ConnectionString="Data Source=.\sqlexpress;Initial Catalog=apexdata;Integrated Security=True" SettingsObjectName="Pharmacy.Properties.Settings" SettingsPropertyName="dataConnectionString" Provider="System.Data.SqlClient" />
  <Table Name="dbo.cust" Member="custs">
    <Type Name="cust">
      <Column Name="serial" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="code" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="descx" Type="System.String" DbType="NVarChar(250)" CanBeNull="true" />
      <Column Name="price" Type="System.Int16" DbType="SmallInt" CanBeNull="true" />
      <Column Name="ref" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="status" Type="System.Int16" DbType="SmallInt" CanBeNull="true" />
      <Column Name="type" Type="System.Int16" DbType="SmallInt" CanBeNull="true" />
      <Column Name="area" Type="System.Int16" DbType="SmallInt" CanBeNull="true" />
      <Column Name="tax" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="limit" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="typed" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="credit" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="sales" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.custb" Member="custbs">
    <Type Name="custb">
      <Column Name="serial" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="descx" Type="System.String" DbType="NVarChar(80)" CanBeNull="true" />
      <Column Name="address" Type="System.String" DbType="NVarChar(250)" CanBeNull="true" />
      <Column Name="phone1" Type="System.String" DbType="NVarChar(20)" CanBeNull="true" />
      <Column Name="phone2" Type="System.String" DbType="NVarChar(20)" CanBeNull="true" />
      <Column Name="phone3" Type="System.String" DbType="NVarChar(20)" CanBeNull="true" />
      <Column Name="fax1" Type="System.String" DbType="NVarChar(20)" CanBeNull="true" />
      <Column Name="fax2" Type="System.String" DbType="NVarChar(20)" CanBeNull="true" />
      <Column Name="fax3" Type="System.String" DbType="NVarChar(20)" CanBeNull="true" />
      <Column Name="zip" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="country" Type="System.String" DbType="NVarChar(20)" CanBeNull="true" />
      <Column Name="city" Type="System.String" DbType="NVarChar(20)" CanBeNull="true" />
      <Column Name="ctc1" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="ctc2" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="ctc3" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="ctm1" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="ctm2" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="ctm3" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="serx" Type="System.Int16" DbType="SmallInt" CanBeNull="true" />
      <Column Name="branch" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="area" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="serv" Type="System.String" DbType="NVarChar(250)" CanBeNull="true" />
      <Column Name="manager" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="email" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="type" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="typed" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="edate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="ddate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="cancel" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="cdate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="bank" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="swift" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="iban" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="rout" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="rights" Type="System.String" DbType="NVarChar(250)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.store" Member="stores">
    <Type Name="store">
      <Column Name="code" Type="System.String" DbType="NVarChar(10)" CanBeNull="true" />
      <Column Name="descx" Type="System.String" DbType="NVarChar(150)" CanBeNull="true" />
      <Column Name="address" Type="System.String" DbType="NVarChar(250)" CanBeNull="true" />
      <Column Name="type" Type="System.Int16" DbType="SmallInt" CanBeNull="true" />
      <Column Name="serial" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ref" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="area" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.supp" Member="supps">
    <Type Name="supp">
      <Column Name="serial" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="code" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="descx" Type="System.String" DbType="NVarChar(250)" CanBeNull="true" />
      <Column Name="price" Type="System.Int16" DbType="SmallInt" CanBeNull="true" />
      <Column Name="ref" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="status" Type="System.Int16" DbType="SmallInt" CanBeNull="true" />
      <Column Name="area" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="type" Type="System.Int16" DbType="SmallInt" CanBeNull="true" />
      <Column Name="typed" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.item" Member="items">
    <Type Name="item">
      <Column Name="serial" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="code" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="descx" Type="System.String" DbType="NVarChar(250)" CanBeNull="true" />
      <Column Name="store" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="logistic" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="properties" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="prod" Type="System.Boolean" DbType="Bit NOT NULL" CanBeNull="false" />
      <Column Name="useserial" Type="System.Int16" DbType="SmallInt" CanBeNull="true" />
      <Column Name="unit" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="specs" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="type" Type="System.Int16" DbType="SmallInt" CanBeNull="true" />
      <Column Name="tax" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="discp" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="discv" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="addp" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="addv" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="taxp" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="taxv" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="sprice" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="pprice" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="limit" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="ref" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="rorder" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="weight" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="publisher" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="author" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="orgin" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="way" Type="System.Int16" DbType="SmallInt" CanBeNull="true" />
      <Column Name="manuf" Type="System.Int16" DbType="SmallInt" CanBeNull="true" />
      <Column Name="qp" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="qb" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="itype" Type="System.Int16" DbType="SmallInt" CanBeNull="true" />
      <Column Name="nweight" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="uprice" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="ser" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="basic" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="barcode" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="status" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="classd" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="sdesc" Type="System.String" DbType="NVarChar(150)" CanBeNull="true" />
      <Column Name="compos" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="used" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="length" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="width" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="height" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="supp" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="points" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="maxbal" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="cdisc" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="ptax" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="mqty" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="curr" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="box" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="packet" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="maxd" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="mind" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="cprice" Type="System.Double" DbType="Float" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.asset" Member="assets">
    <Type Name="asset">
      <Column Name="serial" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ref" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="code" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="descx" Type="System.String" DbType="NVarChar(150)" CanBeNull="true" />
      <Column Name="date" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="price" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="dep" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="acc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="typed" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="supp" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="comment" Type="System.String" DbType="NVarChar(250)" CanBeNull="true" />
      <Column Name="dtax" Type="System.Double" DbType="Float" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.assetd" Member="assetds">
    <Type Name="assetd">
      <Column Name="serial" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="date" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="amount" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="[desc]" Member="desc" Type="System.String" DbType="NVarChar(150)" CanBeNull="true" />
      <Column Name="acc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="typed" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="supp" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="comment" Type="System.String" DbType="NVarChar(250)" CanBeNull="true" />
      <Column Name="serx" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="dtax" Type="System.Double" DbType="Float" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.enterprise" Member="enterprises">
    <Type Name="enterprise">
      <Column Name="code" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="descx" Type="System.String" DbType="NVarChar(150)" CanBeNull="true" />
      <Column Name="type" Type="System.Int16" DbType="SmallInt" CanBeNull="true" />
      <Column Name="ref" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="pamount" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="pqty" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="samount" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="sqty" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="value" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="profit" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="edate" Type="System.DateTime" DbType="DateTime" CanBeNull="true" />
      <Column Name="pid" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="serial" Type="System.Int32" DbType="Int NOT NULL IDENTITY" IsDbGenerated="true" CanBeNull="false" />
      <Column Name="[user]" Member="user" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="limit" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="limitd" Type="System.Int16" DbType="SmallInt" CanBeNull="true" />
      <Column Name="status" Type="System.Int16" DbType="SmallInt" CanBeNull="true" />
      <Column Name="ser" Type="System.Int16" DbType="SmallInt" CanBeNull="true" />
      <Column Name="profile" Type="System.String" DbType="NVarChar(500)" CanBeNull="true" />
      <Column Name="sprice" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="pprice" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="pprice1" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="sprice1" Type="System.Double" DbType="Float" CanBeNull="true" />
      <Column Name="acc" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="keyd" Type="System.String" DbType="NVarChar(250)" CanBeNull="true" />
      <Column Name="bind" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="ruled" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
  <Table Name="dbo.clientitem" Member="clientitems">
    <Type Name="clientitem">
      <Column Name="serial" Type="System.Int32" DbType="Int" CanBeNull="true" />
      <Column Name="ref" Type="System.String" DbType="NVarChar(50)" CanBeNull="true" />
      <Column Name="descx" Type="System.String" DbType="NVarChar(100)" CanBeNull="true" />
      <Column Name="client" Type="System.Int32" DbType="Int" CanBeNull="true" />
    </Type>
  </Table>
</Database>