﻿using DAL;
using DAL.Res;
using DevExpress.XtraCharts;
using DevExpress.XtraEditors;
using DevExpress.XtraReports.UI;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Reports.SL
{
    public partial class frm_SL_DeliveryOfficialsSales : Form
    {

        public bool UserCanOpen;

        string reportName, dateFilter, otherFilters;

        int itemId1, itemId2, customerId1, customerId2, custGroupId,
            storeId1, storeId2, salesEmpId;
        byte FltrTyp_item, fltrTyp_Date, FltrTyp_Customer, FltrTyp_Category,
            FltrTyp_Store, FltrTyp_InvBook;



        private void barButtonItem5_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grd_data.MinimumSize = grd_data.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grd_data, true).ShowPreview();
            grd_data.MinimumSize = new Size(0, 0);
        }

        private void barButtonItem6_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grd_data.MinimumSize = grd_data.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", ch_Report, true, true).ShowPreview();
            grd_data.MinimumSize = new Size(0, 0);
        }

        private void barButtonItem7_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grd_data.MinimumSize = grd_data.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", ch_Report, grd_data, true).ShowPreview();
            grd_data.MinimumSize = new Size(0, 0);
        }

        private void barBtn_PreviewData_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grd_data.MinimumSize = grd_data.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grd_data, true, true).ShowPreview();
                grd_data.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        string categoryNum;
        DateTime date1, date2;

        string custGroupAccNumber;
        List<int> lstStores = new List<int>();
        List<int> lst_invBooksId = new List<int>();
        public int count;

        byte FltrTyp_Company;
        int companyId;

        public frm_SL_DeliveryOfficialsSales(string reportName, string dateFilter, string otherFilters,
            byte fltrTyp_item, int itemId1, int itemId2,
            byte fltrTyp_Date, DateTime date1, DateTime date2,
            byte FltrTyp_Customer, int customerId1, int customerId2,
            byte FltrTyp_Category, string categoryNum,
            int custGroupId, string custGroupAccNumber,
            byte FltrTyp_Store, int storeId1, int storeId2,
            int salesEmpId,
            byte FltrTyp_InvBook, string InvBooks, byte FltrTyp_Company, int companyId)
        {
            UserCanOpen = LoadPrivilege();
            if (UserCanOpen == false)
                return;

            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();

            this.reportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;

            this.FltrTyp_Company = FltrTyp_Company;
            this.companyId = companyId;

            this.FltrTyp_item = fltrTyp_item;
            this.fltrTyp_Date = fltrTyp_Date;
            this.FltrTyp_Customer = FltrTyp_Customer;
            this.FltrTyp_Category = FltrTyp_Category;
            this.FltrTyp_Store = FltrTyp_Store;

            this.itemId1 = itemId1;
            this.itemId2 = itemId2;

            this.date1 = date1;
            this.date2 = date2;
            if (date2 == Shared.minDate)
                this.date2 = Shared.maxDate;

            this.customerId1 = customerId1;
            this.customerId2 = customerId2;
            this.categoryNum = categoryNum;

            this.custGroupId = custGroupId;
            this.custGroupAccNumber = custGroupAccNumber;
            this.storeId1 = storeId1;
            this.storeId2 = storeId2;
            this.salesEmpId = salesEmpId;

            this.FltrTyp_InvBook = FltrTyp_InvBook;
            Utilities.Get_ChkLst_Items(InvBooks, lst_invBooksId);

            getReportHeader();
        }

        private void frm_SL_DeliveryOfficialsSales_Load(object sender, EventArgs e)
        {

            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;

            ERPDataContext DB = new DAL.ERPDataContext();
            #region old slow
            //var data = (from d in DB.SL_InvoiceDetails
            //            join t in DB.IC_Items.DefaultIfEmpty()
            //            on d.ItemId equals t.ItemId

            //            join c in DB.IC_Categories
            //            on t.Category equals c.CategoryId

            //            join i in DB.SL_Invoices
            //            on d.SL_InvoiceId equals i.SL_InvoiceId


            //            where fltrTyp_Date == 1 ? i.InvoiceDate.Date == date1.Date : true
            //            where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
            //            i.InvoiceDate.Date >= date1.Date && i.InvoiceDate.Date <= date2.Date : true
            //            where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
            //            i.InvoiceDate.Date >= date1.Date : true
            //            where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
            //            i.InvoiceDate.Date <= date2.Date : true

            //            join m in DB.HR_Employees
            //            on i.SalesEmpId equals m.EmpId
            //            where m.DeliveryRep==true
            //            where salesEmpId == 0 ? true : i.SalesEmpId == salesEmpId



            //            group new { c, t, i, d, m/*, u*/ } by new { /*i.SL_InvoiceId,*/i.SalesEmpId, d.ItemId } into igroup

            //            let InvoiceId = igroup.Select(g => g.i.SL_InvoiceId).SingleOrDefault()
            //            let ItemId = igroup.Select(g => g.d.ItemId).SingleOrDefault()

            //            select new
            //            {
            //                ItemNameAr = igroup.Select(g => g.t.ItemNameAr).FirstOrDefault(),
            //                Category = igroup.Select(g => g.c.CategoryNameAr).FirstOrDefault(),
            //                Delegate = igroup.Select(g => g.m.EmpName).FirstOrDefault(),
            //                UOM = DB.IC_UOMs.FirstOrDefault(u => u.UOMId == igroup.Select(g => g.t.SmallUOM).FirstOrDefault()).UOM,
            //                #region new
            //                Qty = igroup.Sum(g => g.d.UOMIndex == 0 ? g.d.Qty : g.d.UOMIndex == 1 ? (g.d.Qty * Convert.ToDecimal(g.t.MediumUOMFactor)) : (g.d.Qty * Convert.ToDecimal(g.t.LargeUOMFactor))),

            //                #endregion

            //                Detail =
            //                from g in igroup
            //                group g by g.i.SL_InvoiceId into grp
            //                let customer = DB.SL_Customers
            //                select new
            //                {
            //                    InvoiceCode = grp.Where(x => x.i.SL_InvoiceId == grp.Key).Select(x => x.i.InvoiceCode).FirstOrDefault(),
            //                    Qty = grp.Where(x => x.i.SL_InvoiceId == grp.Key).Select(x => x.d.Qty).Sum(),
            //                    TotalSellPrice = grp.Where(x => x.i.SL_InvoiceId == grp.Key).Select(x => x.d.TotalSellPrice).Sum(),
            //                    PayMethod = grp.Where(x => x.i.SL_InvoiceId == grp.Key).Select(x => x.i.PayMethod).FirstOrDefault(),
            //                    InvoiceDate = grp.Where(x => x.i.SL_InvoiceId == grp.Key).Select(x => x.i.InvoiceDate).FirstOrDefault(),
            //                    CusNameAr = customer.Where(xy => xy.CustomerId == grp.Where(x => x.i.SL_InvoiceId == grp.Key).Select(x => x.i.CustomerId).FirstOrDefault()).Select(x => x.CusNameAr).FirstOrDefault()
            //                }
            //            }).Distinct().ToList();


            #endregion

            var defaultCategories = DB.IC_User_Categories.Where(a => a.UserId == Shared.UserId).Select(a => a.CategoryId).ToList();
            var data1 = (from d in DB.IC_OutTrnsDetails
                        
                         join t in DB.IC_Items.DefaultIfEmpty()
                         on d.ItemId equals t.ItemId
                         join ic in DB.IC_Categories on t.Category equals ic.CategoryId
                         where defaultCategories.Count() > 0 ? defaultCategories.Contains(ic.CategoryId) : true
                         join i in DB.IC_OutTrns
                        
                         on d.OutTrnsId equals i.OutTrnsId
                         where i.DeliveryEmpId !=null
                        // where i.ProcessId==2
                         #region update
                        
                         #endregion

                         where fltrTyp_Date == 1 ? i.OutTrnsDate.Date == date1.Date : true
                         where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                         i.OutTrnsDate.Date >= date1.Date && i.OutTrnsDate.Date <= date2.Date : true
                         where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                         i.OutTrnsDate.Date >= date1.Date : true
                         where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                         i.OutTrnsDate.Date <= date2.Date : true
                         where salesEmpId == 0 ? true : i.DeliveryEmpId == salesEmpId
                         join m in DB.HR_Employees
                            on i.DeliveryEmpId equals m.EmpId

                         group new { t, i, d, m } by new {i.DeliveryEmpId, d.ItemId } into igroup


                         select new
                         {
                           
                             ItemNameAr = igroup.Select(g => g.t.ItemNameAr).FirstOrDefault(),
                             Category = DB.IC_Categories.SingleOrDefault(c => c.CategoryId == igroup.Select(g => g.t.Category).FirstOrDefault()).CategoryNameAr,
                             UOM = DB.IC_UOMs.FirstOrDefault(u => u.UOMId == igroup.Select(g => g.t.SmallUOM).FirstOrDefault()).UOM,
                             Qty = igroup.Sum(g => g.d.UOMIndex == 0 ? g.d.Qty : g.d.UOMIndex == 1 ? (g.d.Qty * Convert.ToDecimal(g.t.MediumUOMFactor)) : (g.d.Qty * Convert.ToDecimal(g.t.LargeUOMFactor))),
                             Delegate = igroup.Select(g => g.m.EmpName).FirstOrDefault(),
                             ItemId = igroup.Select(g => g.t.ItemId).FirstOrDefault(),
                             DelegateId = igroup.Select(g => g.m.EmpId).FirstOrDefault()
                         }).Distinct().ToList();







            var data3 = (from d1 in data1
                         group d1 by new { d1.Delegate, d1.ItemId } into grp
                         let detail = from id in DB.IC_OutTrnsDetails
                                      join i in DB.IC_OutTrns on id.OutTrnsId equals i.OutTrnsId
                                      select new { id, i }

                         select new
                         {
                             grp.FirstOrDefault().ItemNameAr,
                             grp.FirstOrDefault().Category,
                             grp.FirstOrDefault().UOM,
                             Qty = grp.Where(x => x.Delegate == grp.Key.Delegate).Select(x => x.Qty).Sum(),
                             grp.FirstOrDefault().Delegate,
                             Detail = from d in detail
                                      where d.id.ItemId == grp.Key.ItemId
                                      // where d.i.SalesEmpId == grp.FirstOrDefault().DelegateId
                                      where d.i.DeliveryEmpId == grp.FirstOrDefault().DelegateId

                                      group d by new { d.i.OutTrnsId, d.id.ItemId } into igroup
                                      let item = DB.IC_Items.Where(t => t.ItemId == grp.Key.ItemId)

                                      select new
                                      {
                                          grp.FirstOrDefault().Delegate,
                                          igroup.FirstOrDefault().i.OutTrnsCode,
                                          igroup.FirstOrDefault().i.OutTrnsDate,
                                          //igroup.FirstOrDefault().i.PayMethod,
                                          Qty = igroup.Sum(g => g.id.UOMIndex == 0 ? g.id.Qty : g.id.UOMIndex == 1 ? (g.id.Qty * Convert.ToDecimal(item.SingleOrDefault().MediumUOMFactor)) : (g.id.Qty * Convert.ToDecimal(item.SingleOrDefault().LargeUOMFactor))),
                                          CusNameAr = DB.SL_Customers.SingleOrDefault(x => x.CustomerId == igroup.FirstOrDefault().i.CustomerId).CusNameAr,
                                          TotalSellPrice = igroup.Sum(g => g.id.SellPrice)


                                      }

                         }).Distinct().ToList();


            gridView1.DataController.AllowIEnumerableDetails = true;
            grd_data.DataSource = data3;


            var emp = DB.HR_Employees.Where(m => m.DeliveryRep == true && salesEmpId == 0 ? true : m.EmpId == salesEmpId);

            var counter = 0;
            foreach (var item in emp)
            {
                Series newSeries = new Series(item.EmpName.ToString(), ViewType.Bar);
                ch_Report.Series.Add(newSeries);
                //newSeries.ChangeView(ViewType.Bar);

                ch_Report.Series[counter].DataSource = data3.Where(x => x.Delegate == item.EmpName);

                ch_Report.Series[counter].ArgumentDataMember = "ItemNameAr";
                ch_Report.Series[counter].ValueDataMembers[0] = "Qty"/*+(count+1)*/;
                counter++;
            }



          

        }

        bool LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.rpt_SL_InvoicesHeaders).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            return true;
        }
        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                //lblCompName.Text = comp.CmpNameAr;
                lblReportName.Text = this.Text = reportName;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }
    }
}
