﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;
using DevExpress.XtraReports.UI;

namespace Reports
{
    public partial class frm_CustomsCertifecationWarning : DevExpress.XtraEditors.XtraForm
    {
        public bool UserCanOpen;

        string reportName, dateFilter, otherFilters;

        int vendorId1;
        int vendorId2;
        byte fltrTyp_Date, FltrTyp_Vendor;
        DateTime date1, date2;
        bool fromStart;
        Action<int,int> OpenJobOrder;
        public static  bool findData;



        public frm_CustomsCertifecationWarning(bool fromStart, string reportName, string dateFilter, string otherFilters,
            DateTime date1, DateTime date2, byte FltrTyp_Date ,Action<int, int> OpenJobOrder)
        {
            UserCanOpen = LoadPrivilege();
            if (UserCanOpen == false)
                return;

            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            this.OpenJobOrder = OpenJobOrder;
            this.reportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;

            this.date1 = date1;
            this.date2 = date2;
            this.fromStart = fromStart;         
            this.fltrTyp_Date = FltrTyp_Date;                
            LoadData();

            ReportsUtils.ColumnChooser(grdCustomsCertifecation);
        }

        void LoadData()
        {
            lblReportName.Text = reportName;
            if (date2 <= Shared.minDate)
                date2 = Shared.maxDate;

            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;
            lblDateFilter.Text = dateFilter;

            ERPDataContext DB = new ERPDataContext();

            //var data = (from v in DB.JO_JobOrders
            //            join t in DB.JO_JobOrderDetails on v.JobOrderId equals t.JoId into job
            //            from joborder in job.DefaultIfEmpty()
            //            join u in DB.HR_Users on v.UserId equals u.UserId
            //            where u.JO_Alert == true&& u.UserId==Shared.UserId
            //            where DB.JO_Status.Where(z => z.AllowAttention == true).Select(a => a.StatusId).Contains(v.Status)
            //            where fltrTyp_Date == 1 ? v.RegDate.Date == date1.Date : true
            //            where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
            //            v.RegDate.Date >= date1.Date && v.RegDate.Date <= date2.Date : true
            //            where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
            //            v.RegDate.Date >= date1.Date : true
            //            where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
            //            v.RegDate.Date <= date2.Date : true
            //            let Customer = Shared.IsEnglish ? DB.SL_Customers.Where(a => a.CustomerId == v.CustomerId).FirstOrDefault().CusNameEn : DB.SL_Customers.Where(a => a.CustomerId == v.CustomerId).FirstOrDefault().CusNameAr
            //            let SalesEmp = DB.HR_Employees.Where(a => a.EmpId == v.SalesEmp).FirstOrDefault().EmpName
            //            let Status = DB.JO_Status.Where(a => a.StatusId == v.Status).FirstOrDefault().Status
            //            let Priority = DB.JO_Priorities.Where(a => a.PriorityId == v.Priority).FirstOrDefault().Priority
            //            let DeliveryEmp = DB.HR_Employees.Where(x => x.DeliveryRep && x.EmpId == v.DeliveryEmp).FirstOrDefault().EmpName
            //            let DepartMent = DB.JO_Depts.Where(a => a.DeptId == v.Dept).FirstOrDefault().Department
            //            select new
            //            {   v.JobOrderId,
            //                v.JOCode,
            //                Customer,
            //                v.Job,
            //                v.RegDate,
            //                v.DeliveryDate,
            //                SalesEmp,
            //                Status,
            //                DeliveryEmp,
            //                DepartMent,
            //                joborder.ArCaption,
            //                joborder.ArText,
            //                joborder.EnCaption,
            //                joborder.EnText,
            //                u.UserName,
            //                v.CustomerId

            //            }).ToList();
            var data = (from v in DB.ImExp_Customs_Certificates
                        where
                        (v.RejectiondDate==null&&v.ReleaseDate==null&& v.DrawDate != null)                  
                        join b in DB.ImExp_Labs
                        on v.LabId equals b.LabId
                        where (b.PreiodTime!=0||b.PreiodTime!=null)&&(b.PeriodType!=null)
                        where fltrTyp_Date == 1 ? v.CustomsCertificateRegister.Date == date1.Date : true
                        where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                        v.CustomsCertificateRegister.Date >= date1.Date && v.CustomsCertificateRegister.Date <= date2.Date : true
                        where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                        v.CustomsCertificateRegister.Date >= date1.Date : true
                        where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                        v.CustomsCertificateRegister.Date <= date2.Date : true
                        where b.PeriodType==false?
                        Convert.ToDateTime(v.DrawDate).AddDays(Convert.ToDouble(b.PreiodTime)).Date<DateTime.Now.Date :
                        Convert.ToDateTime(v.DrawDate).AddMonths(Convert.ToInt32(b.PreiodTime)).Date < DateTime.Now.Date
                        select new
                        {   v.CustomsCertificateId,
                            v.CustomsCertificateCode,
                            v.CustomsCertificateRegister,
                            v.DrawDate,
                            b.NameAr,
                            ExpectedReleaseDate = b.PeriodType == false ? Convert.ToDateTime(v.DrawDate).AddDays(Convert.ToDouble(b.PreiodTime)) : Convert.ToDateTime(v.DrawDate).AddMonths(Convert.ToInt32(b.PreiodTime))
                        }).ToList();
                grdCustomsCertifecation.DataSource = data;
                if (data.Count > 0)
                   findData = true;
        
     
        }

   

        private void gridView1_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            if (e.ListSourceRowIndex < 0)
                return;

            if (e.Column.FieldName == "Index")
                e.Value = e.RowHandle() + 1;
        }

        public bool LoadPrivilege()
        {

            if (Shared.LstUserPrvlg != null)
            {
                /*TO BE DONE AMIR*/
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.frm_CustomsCertifecationWarning).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }

            }
            return true;
        }


        private void frm_ImExp_Fines_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                ReportsRTL.LTRLayout(this);

            ReportsUtils.Load_Grid_Layout(grdCustomsCertifecation, this.Name.Replace("frm_", "Rpt_"));
            LoadPrivilege();
        }

        private void frm_ImExp_Fines_FormClosing(object sender, FormClosingEventArgs e)
        {
           ReportsUtils.save_Grid_Layout(grdCustomsCertifecation, this.Name.Replace("frm_", "Rpt_"), true);
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void grdST_jobOrder_Click(object sender, EventArgs e)
        {

        }

        private void gridView1_RowClick(object sender, RowClickEventArgs e)
        {
            

       

        }

        private void gridView1_DoubleClick(object sender, EventArgs e)
        {
            var view = grdCustomsCertifecation.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            int col_CustomsCertificateId = Convert.ToInt32(view.GetRowCellValue(focused_row_index, "col_CustomsCertificateId"));
      
           // OpenJobOrder(col_CustomsCertificateId);
        }

        private void barBtn_Preview_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCustomsCertifecation.MinimumSize = grdCustomsCertifecation.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCustomsCertifecation, true).ShowPreview();
            grdCustomsCertifecation.MinimumSize = new Size(0, 0);
        }

        private void barBtnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadData();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grdCustomsCertifecation.MinimumSize = grdCustomsCertifecation.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grdCustomsCertifecation, true, true).ShowPreview();
                grdCustomsCertifecation.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

  
    }
}