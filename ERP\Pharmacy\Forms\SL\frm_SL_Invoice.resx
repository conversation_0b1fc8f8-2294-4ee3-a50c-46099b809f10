﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="barManager1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="barBtnHelp.Caption" xml:space="preserve">
    <value>Copy</value>
  </data>
  <data name="barBtnHelp.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="btnAttachments.Caption" xml:space="preserve">
    <value>Attachments</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnAttachments.Glyph" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAAA10RVh0VGl0
        bGUAQXR0YWNoO3wCt50AAALOSURBVDhPpZLbU1JBGMDXZBjFRDgckIuCCoqI3CVDRVFUlAYlvIF3RHTU
        YfKWKeLdvGRlaZpaPfqfNNP00JP/QA899WzP2/cdc8aXHpp25jd79vv2t/vtniWU0v/iX1oacO8ON805
        8Yk4xz6SCsAx9oE4Ri+II3aOqVsB+7Sy7j2jqffosrz/+MoYPlyDWDqXc8QviB0E+8gZsUXPiHX4PcQ5
        iWfuP8EaefrQZnl53xEt7dqLlQQ3XaVdB6f6zheHkEsnVpAsQ6fEMnBKzIMnnCzWVmYY+445odA3ZzSE
        D6k2sNqEi6GU75kqKe44+CU1t4mIaeAdKe875kBZqLZmGsJvaHHweVzTOGPRd72iuuAOlpUJ4JF4+d65
        em377k+B3CAgZZG3BAROvq80CvRdr2G3jXhe3aStOPSSFga2znKdfaai4D4eR5DnnW8vCOzQ3AdDEVyM
        wA6cnKUoE+hCB7TQvzYG4+yi9n2q8a+fS+3dloLALlV5nyYU7kSH2r9NWXtvFOaI0LuRoZSi9j2qaV4e
        V7onXZpH29/UvtSFxPzYhoLSM/tEXjXRnd+yRVlrBGUW4Cub19EnPI1/41LdsroD3xkq7+KwunWbig1+
        Jwpy9/S0rDLeo/JtUMbSMwJzxABf4V0lcoBksLosdevWdbbmoRQS6UJdPaNqTH1GQVadmGGd0YiicY2K
        TZ0xyDMo59aniMxzAxHpfTKVb/06U2HCsvBM+EDwxsUSRzQsb1ihOcbQKIw5WeZZJtK6JJHWJglbuwQh
        KELekLpiXVNYHoq8HGOHSuwY6YYdqNAQxEuVAHwUWPcikSA1yDMIg8BUxLy5dcnvEldiRWQb9DHO8YS0
        dolml7ahLEMZBQYEpvoGcfUCEVctECL1JHERfo454pbUzH9haxZ+MK7Zr1napjaIczvjZAYmo3AXjN02
        7u0DWYDwT49j7j//vRHyGw3t7WpPD3wwAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="btnAttachments.LargeGlyph" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAAA10RVh0VGl0
        bGUAQXR0YWNoO3wCt50AAAjlSURBVFhHxZZpUJNJGsc7QEAcrnAGUK5wHwkJgXCFcJ8JhCMQCBAOATnl
        EBQGHEFBLhHFA3S8BQFBUWd2xp0d1wMInuPuuGpN1dSucjif9vMeH3qfTiAEdI+q3XK76ledfvrp5/9/
        +33fzoswxv9XPkWjbELd1AaKDt9QUnx4BhUPbOamBqpYEQFyVcDafhWFQAFQPPgrVHX+sWPdxJvWuvHX
        itqrb/5SPfpKUXnpxzb5wB0nkFQaWTdACqyaULFafE1ME03hVXEirBTvI/0Mqrn8wqt67PVC7vAPOLr1
        JvYrHcERzddxxqACF51+Ni49MMEgumoDBX3XYSFhvRhBVRzEVnvNOXKlZJ2aXtLPoPy+W0aVV169TRl4
        hNlFR//sKe3ocU1uinBPb+v1q7z0Vti/gKW9d9pBVkttIL93Gsk10ShMfn8A5BAxaMr7mt8zjQi5vTeN
        aq6+nk3sVWBW4cA75/gKT5jfAlABHaaspzd43z2c2HXvCYmvG1gtQIx8YEYD9Txs9Y6T9w3rJ3+arbn6
        klTRkvXMGFWPvZoV9c9jZl7/oktcmRfEiThF8vlZIkPhV46E8Vru4qCGG3+FsaHaQG73lAbTKG8NMKVk
        dUzmlePeW0a7xt/Mph59gtklQ++yDk5wKy//OCfsn8M+sp5FRnQJEdcHKBnNp1Ha3mEio53S8VUPd893
        2DWz7ymMaWoDsq5J4BqSHfonkPnOCWWfA1daNfZ6VnTkEfYtHFzkVZwMKDr3QpHYO4e9pF2LjoJcHyip
        FE9tOoXEjSeIBEVyYEYS23FXway6jp1iyrshZqw2kN05qSRnlbVxNohKD44j6YGrKJv0h24YVVx5NSs8
        vIBZ8oFFbskxXtGXPyjiux9gj8yORbtQKRPKbQUoKQ1DKLn+KClPCclrMxUduq/gNdzCtoKyJTt2DBfi
        +moD8FqAEEB6EMsidIyhrPZRlLn/irIXt14yLr34+7kkuFJv2Gbfgr4g+elnitiu+9gtbd+ibWCqL5T6
        DNAS1R5Bwl0DpDQlSNZqKhl4qODWzmDrkOJlGoPHg7gpyVMbkLSPqdg/iiRfXEEZ+y4DF1FG20UyTUlu
        uWC84/zv5uK6H2Kv7K5FZk5nSN6pJwtRB36LXVJalmz8k9mQZwgon3ayhuAv2W2W0f9A4bfrOqYHyZeN
        HfyCIG4GKMXVBtJBML3tEkpvvYjSWi+gtM/PobQW1ZMbW3vcpODMi7noznvYXdKx6CltD80+vrAQuf83
        mCFqWqKz4zmQZ8xNrzPPHppXSAcekKq63IzdZqk99xXsmmls4Z+7bGTnGwxxC0A7TLpbKa42IG45j8TN
        Z5F475coZc8ZlNI0QsKU6OpjJnkjz+Yi27/HLrDNcJjwJYNzjwRtv8aOCfVLlt5R5F6asJMrLMg2BzTe
        xjZhpcuc1LqYlEN3F3yrrmFzjnTZwNY7FPIsAW2+ZBcKSa/ZaEDUOIJEu4eRsOEUSqpXPbURFYMm2Sce
        z/NBjCFsWmIIG8PSDj98FNLyDbaPqV4y9xD4Qx6NKSq3SO17oODWzWA63GPr4LyE2C++eepTPolNWekr
        n9E9wiDPCtAOTqtCQeJyFAhsMJBUexwl1h5DCTWDZEgJKeykZQ4q5oOav8UO8XVLFl6R/on7b98J2XcX
        20aUL5m6hgRAnqlXXJFlcvc95TZbBeYvW/FykqLbvn7qVTqGaT7iFX0rNwHk0QGdwJRyxEveiXiiUhQg
        3LHRQGzlAIqp6CM/KYH5B2hp/bPzAY1f4e1RVUvmnuFkm824u6beMHd/iy18k8m9NPeIKbBM6vx+wady
        Cptzs1cs/KVJEc23n3mWXMEmnqIVfQvncMizBnQChCXIP2kH4iYWI25CIeLGF240EFVGzgVE8ctsNhUe
        uqfgwCtjI9i5RHMNJdtsAugx0rveeFZ/jQUN40cZYdk28e3fLXjvnMCm7MwVM45EJNhz47l78WVs5J60
        ssXMKRLW2AA63AQiWoD84glyFXHyjQagUViiatPEg3BSVU9jenDhsomT8n2lAVqANj1A2m0T34r9qiZw
        9N4b2FV2EtOY6SumLLE4tHH6uWvhRWzoGv9ez9QhCvJtAR1ObAFix8qBfBUxhDwlmw3oRDVNHfeGB4fO
        L182tOOQ91V5WPDSa8k8ZRsnwcHQWTBg6BL1swEjnDBPY4J4/dRzZ/k5bOAc817PxC4acrcDVN+YXPQB
        0WvINhjQCizsi+LW3vzJJrH9nYkLXwoxc0DbP6UCcUXlyE9URvLI4aILkINku76lKyu49tpz5/yz2IAR
        +V7XeFsMxO0AKjMqFzGjZOtE5qwTQcjeYEAnYOf5Ex5lZOtLyOlDnlqqn7AUcRJLEBvuITuhCLHhwfGN
        KyD5WvaBGVahDdOPGbln8FbHCBC3jYO4UtwHRNbJVgGCm9E0oMvaOfHSoWAcW3PEzTA2YcUVIVZsIWLG
        FAD5iBmdB1eSS3K17ALSrIJrpx47yU5jfQcQN7JJgLg9QPWGwmrCCVIVAhVegiw1mgb0HDJP/M0+bwxb
        MROSYWzgHZkLRWTIC7bLKzyH5JDt197ml0wPqpl84iQdAfHwX6hG1okQdwConlBUSVjmRvgfZ4MBc37d
        H7ZnX8ZuKW29MFb+nXrws8gcESZvga5nSgufVzXxxCGLiAt+oRpaCyHuCFA9+BL0AaEZyD1UApD+QzQN
        UOHgGLYSHsEe8rMvHMMKyemlR+Kkdxfu4XCKTu/llI0+tc8cVokb0EUwRz6vqRsKh6hwC0n/t2ga0KY5
        8eIMPTP/uE18DLOKLrzwlZ/qYMmH93CKzx5nl46+dJMNY4vAUqxvrxYnn9W6bsFQjBC0RpoS1/8ATQPK
        10ufzqreyoj/k2VoPXaVDGHPglHMyBjClvw6vNUp4S3VzH1oVdyZ5LsEpSKXQDFy1mDz+F+haYA0pYkt
        Fp6pVAvvMV0r35916X5/14Oeau41qUdzLIF5V4AcTroMnhj9t2w2QBoxQb5mjAFymrkD5APTBSB/KlvX
        Fv0v+WjwU/LR4KcDo38ATm2tT1tVgIEAAAAASUVORK5CYII=
</value>
  </data>
  <data name="barSubItemPrint.Caption" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="barbtnPrint.Caption" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="barbtnPrintF.Caption" xml:space="preserve">
    <value>Print with Foriegn Name</value>
  </data>
  <data name="barbtnPrintStore.Caption" xml:space="preserve">
    <value>Print Delivery</value>
  </data>
  <data name="barbtnSamplesPr.Caption" xml:space="preserve">
    <value>Print using a sample</value>
  </data>
  <data name="barBtnDelete.Caption" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="barBtnDelete.Glyph" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAACl0RVh0VGl0
        bGUAUmVtb3ZlO0RlbGV0ZTtCYXJzO1JpYmJvbjtTdGFuZGFyZDtjVkgwAAACeElEQVQ4T6WTW0iTYRjH
        LXdw6nKtm0jaliWlkkrJKrtJb5KROYvMdEtrHqrNQ03zgI1ouF2kUnPqTSeJWVC2K01CyoEHyEGWkYEU
        HcjoYGu1Sq/+vc+3aV91Vw/8vu99/8/z/2/f9/KFAfgvgpdftZQhCN2XkBAqWoczhAzqcz1+AAmCh6WH
        zkwfK/s4uDv7CNvTMOmEcLRYZ6Te+OEiK9uLSecHhI8X623vr3RgfnIIU6fr0ZuZaWS6iIY9hfmmmfaz
        rOfBu0tOjOgL7EwX8gNEE0V63/dRNwK9bZgbvoGJk1W4nKau6tuTW/2yzYZ5bx8CN1sRGOjGxEG9j3kk
        /ACBa1u68YHJgK/udvi7rfhxrwf3K8rxzG7B3JgbX65aOX04X4tz8QnH6Uf5AfSc4s6k5MoR3T74upsx
        21WPQP8FfLt9EZ+6GjDbWYdBTQZssSozm40kDz+AikIkdsVa812tBh8cNXhrNTBKuHv/jq1olK+sZzPR
        DDqloPePgMjzqeraMYMeb6zleFWzf5FRXR5aUtIa2YyU8VcAZ25JSDV7dPmYaa3FdIkG06Uh2Pq19SiG
        8vbCtj558V/wA0TNaxLNd3Ky8cJShicFGZhiXFunxPWEeG5NPD9RiAFNFizKDQ3M89spRHcoEv1PTQcw
        mbsdj3LS0ROngFEQfcoojLL0xKkwqU3neFy0C05lkp95ZPyAiKYVKod78yZ4s9RwKVajUiSl540hTOKY
        JpdKCe/OLbiVshF1cmUH06P4AfRSYmqksQ6bTOmrCJqXhXSuZ4xY3sR6n6ulq5xsLyedH0BFg3S+MoYk
        tF+ohR4Z6QXShxX00uXfQdhPmOi/wI4pGN8AAAAASUVORK5CYII=
</value>
  </data>
  <data name="barBtnDelete.LargeGlyph" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAACl0RVh0VGl0
        bGUAUmVtb3ZlO0RlbGV0ZTtCYXJzO1JpYmJvbjtTdGFuZGFyZDtjVkgwAAAH1klEQVRYR8WWe1RVVR7H
        rzWTJSimiDQoXBB8XEF5KAqooMhDAVEBHQcZy1R8BKmAgoCggshDFDELRkB80SJsQT7CUBDE14gilK6k
        iIsP0CBFc6TVxPnO73cu5ybc66r+mX5rfdibffb+fb97n733uTIAfypaG/+fqP5ojz7EK91w/Y8E9+89
        XiPHywxIA/9K9O2G67/XCPd5lXiNeKMbzsFtPcZrMyAN7lu5cGGoMnlHpzI5qbMiIOA9anu9+1mfG4sX
        y5jaxYEqAlXwM+IvxBtlfvNDGhMTOxsTEjpL585dR239up+pTfQ2IIm/fm6Bf+SdvWn47706EeWeFJzx
        8wujZzwb0UTXg69kIq3Ml9SkFu93ytd3fdOuJPysvIafm2rwHdVPzfGNpmc63X1EE9oMvFbu57e+mcR/
        ulmNJ6WF6Dh+BD/VVaApIwWlvj1n8ktLveyX+/VUVYvrfObtE9aYnoTnV79A+6EP0JadimdVxWjcuR0n
        vLwjqA+vJL9ODQOvTJLLdRvi4zufXzuDjpLDeFyUK/KoKAfPa8rAiY97+fBKvDgTtXjxbK+wb1MT8J/L
        pWjLy8CDfUl4uDcRrRnb0PH5UdyKiu4ca2ioR315FTUMvOplMXLgxWUrnrQX5eOHgqxfOfoR2o98SIlP
        gQWKZ81mE7oEbzRG95jHrPCG5G14duEk2vanozWThbeidVc8WtJicTc5GtVLlz9zNjYZzFqE5goQffdZ
        2bxdGRTY1XYgE2353RzYg+/zmAz8eL4EDTu24hM3z3DqP5ApdPOIuJ20BT9WleBhVipadm3B/fQ43Cfh
        e8mbcGd7JM76enftVox/l/rzPtL6Cngp2Vm/3WMsV1YsWtj1IDsND/+1Ew+zmTQ8yCI+SsHTs0X4OiEO
        h6a7Rh1ycY26lbAZTys+ResHSbi/czPupsTg7g4STtyI5m3hOO3l2ZVsPnoN5e5P8JFmLQ0DHNL71E0Z
        MWp12fx5XS17t4uJuWyhZW3ZQ2Qk4DG905vx0SIdZYViGy8zz/ZOwgY0bw2HMm4dTrrNFBLk5nyMBxBq
        cQ4NAzkWCi64A3fsv93EfPXnPt5dd9PjcY/ZGYd7aTTDVNUsfyg+gEcnDlNbHM10A5Q0W2X8ejSRcGN0
        KEpcnIW4v5mEUC7eeLxXelxmGgay5SNlTyuOcVUyMSB+mHzNCU+PruakKNwhmmmGzYmRUNIsldsioNwS
        hiYWjV2L72JCSTgE325chSInRyFqiFEo5eB9ohbfa2RKhSo0DOwbZibr+KJA1nG6gP9Vm4geahRyzGGS
        cHtNEL6JCEbjhlVojFqDRkkwYiUa1r2L26FLcGtZAD62Hi9EDBr6Po3tId5ekifbPXQYVVWhYWDPW8ay
        RycOEvmyR8fzuUk8GYR+mL5hWKGtjVC/0BNfMgs8UB+goi7AHfX+7qjzd8MRxRjhPb3BfOEYENKl06et
        MEvG7BxsSP+qQsPASRc3Wfun+2Xtx/bzv7wCPJiTDMmwsd98cclifBU0Fzfmu3UzE7XzXFE7nxDLmaj6
        ewDSrOziacxQQjpyfR4czpQxqQPZlyo0DHw2xVX2fcE+rkrivHxvZlrbb7q0fCmUyVGoW+SF63Nn4Lov
        Mx3X5kzHdR8XKp1R4+OMbzatxvklQUi3nBBDY/nS4RUUTbTkpMk+mTCFqqrQMFDs4MKFJM4DB+0eNyG6
        +p1/oikhHLULPFHjTULe04ipqPGaiqte03B1NpUSs6bg6/DlqAxchFSFTSzlYBPqL+nHto5UqELDAEUP
        8XRL25iqoEA0xq1FrZ8b/k3JRTydcIXxYBy7ccAVdwdcdp+My26TcTNkCcoXBCBp1LjNlEufUO8HQoze
        BnqIpylsYug2RAMt6TVack5+RUxOIiRwaeZk5I0wFZiLMybhkqu9ihkTqZyIi1TWBy9C2bx5SDC3jKOc
        Q4geJrQZ4Hc+KGW0dcxZfz9ayhW01C6/Jic48QWXicgzkwsrdPRimQMWI4Rq5wnUbkfY4oKzLaqn2RG2
        uPGOH0q9vbHVbKxkgieoYYAb+B31T7SwCjvtMwe33n8bV2ZNpYSqxNXOlJw4T4lzTU2ENboDN1J/I2a1
        7sDIPPMRQtUUa5xXM16kymkcrv3DB8dd3RErH72B+vOtKH7KexvgS0f/qJ1zR11wIC7TO66eaoPzIpSQ
        qHKyRo7cWBLn88Q/ThgDNpFrZiqcc7BCJXFuMmOJc5NUXPbzQL6V4xPqy8dT/CZoNXDQ2qnt4mxnElO5
        r3JkKBklzTEZLrAQ9WNx6ecZw3WDVbp6UbmmcqHcXoGKiYT9GKjqY3BmoiVyFfbt3I/QaoATDYg0Hhlc
        aGMvVDqw87GoIMqJ/cbDeotLm4nhOrcNXamjtylXbiKcsRuFs0S5LZXE4VFWwjpD09XUh69n8Ui+aICD
        E/Em1I8wMl9bYGUjlNkpcGaCAllGRlrFa5cFyGqX+lO1hwlDNpE9fLhwevxIlI6zwEGLsUKogZx/RfHy
        SxeTSruXAX4gXr1hb5mt/FBu1U48CtbR4+85i6uPUbKevuxFuK37mbgSy3X0QjOHKx5nDlO0hxiY8I+R
        HlczoWGAQ0rCLnm38iCGl01yLg5+Sbw4/k2CvzwM19W3ISGGNgMcUhI+KvxKGK7/lrgUv3v8ywxIwZ1f
        5I/Gb45XG/jzgOx/2ZzKgxEwf90AAAAASUVORK5CYII=
</value>
  </data>
  <data name="barBtnNew.Caption" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="barBtnNew.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="barBtnCancel.Caption" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="barBtnCancel.Glyph" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGPC/xhBQAAABF0RVh0VGl0
        bGUAUmVzZXQ7VW5kbzsTgRb/AAAA70lEQVQ4T6XTMWoCURDG8W2EWGgRC7ERDF7AVrDwAN7BRgQheAev
        EbxDAilsFEQQtBQLu1WwsJVYis//LEx4+5jVQIrfsvLNfDwebuSc+5eoM5r4XvAJhxuGCGdS/B8FTKHL
        A/i5SV9esYIu95AazCKPCraQ5SwXHPANOVkRvwUzWEuPnNBCUlDFDtbgI3KqenIMlLGBBFd0oZnKoYEF
        tGTsD8hFriGBXGQffq5q0IJ9GMrlzKEl7whnhBZcrPCZOrQgtgaylNDGElrwYQ2GdDj0gzdrIWQtH9FE
        6lvIIgtnxPiC/M3zSHLzE/07F90Bmo0RPdcw7NUAAAAASUVORK5CYII=
</value>
  </data>
  <data name="barBtnCancel.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="barBtnCancel.LargeGlyph" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAABF0RVh0VGl0
        bGUAUmVzZXQ7VW5kbzsTgRb/AAABwklEQVRYR8XVsUuVURzGcSOQyAYjCUJwa3DQv0TbnGpqaGioIQIr
        UBF1cLWxoT/DpUla3BwiiKIlGhp0VEF9+z5xrzy/c39X33uV0/AZfofnnOfAfd/3jjRN81+lizWlizWl
        izWlizWlizWFYX51u585HKExy8iyF/I+CUO2AQs4hpevIMteyvskDMmGJziBl68iy7bifRKGIvwMp/Dy
        NZS5gXifhMGCL3AGL9+AZ4bifRKGTmgRXnwZPR/7+IFP2MIj3MZ5cZf3SRgI6PfNSoZxgCWMofUFNpEd
        dhVfMIVWF5B1ZAddxR5uIfRJGBTo0EemPOQtPONGcRfTeIrPKPe/QuiTMChg3qA85KJLuBv4CN/7FaFP
        wqBA4TX8EGl7iUmU35F73idhIJB5ifKb0PYSv+H7ZrxPwkCgn+cY5hK/4HtmvU/CQOA6PUD5P3Lf+yQM
        BK6LHsIP8PLvCH0SBgWGcBN6BScwC72GO/ByeYfQJ2FQYEBlST/fcAehT8KgwICystJPPMS/Pd4nYeiG
        BpAVdh3iPcZxvsf7JAwebKlbpr/kP9iFvoCPoeeiZ4/3Sc9CbeliTeliTeliTeliPc3IX9pfvQlNxdSA
        AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="barBtnSave.Caption" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="barBtnSave.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="batBtnList.Caption" xml:space="preserve">
    <value>List</value>
  </data>
  <data name="batBtnList.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="barBtnClose.Caption" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="barBtnClose.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="bar1.Text" xml:space="preserve">
    <value>Tools</value>
  </data>
  <metadata name="barAndDockingController1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>139, 17</value>
  </metadata>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="barDockControlTop.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="barDockControlTop.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="barDockControlTop.Size" type="System.Drawing.Size, System.Drawing">
    <value>1174, 31</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Name" xml:space="preserve">
    <value>barDockControlTop</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.ZOrder" xml:space="preserve">
    <value>87</value>
  </data>
  <data name="barDockControlBottom.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="barDockControlBottom.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 719</value>
  </data>
  <data name="barDockControlBottom.Size" type="System.Drawing.Size, System.Drawing">
    <value>1174, 0</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Name" xml:space="preserve">
    <value>barDockControlBottom</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.ZOrder" xml:space="preserve">
    <value>86</value>
  </data>
  <data name="barDockControlLeft.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="barDockControlLeft.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 31</value>
  </data>
  <data name="barDockControlLeft.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 688</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Name" xml:space="preserve">
    <value>barDockControlLeft</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.ZOrder" xml:space="preserve">
    <value>84</value>
  </data>
  <data name="barDockControlRight.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Right</value>
  </data>
  <data name="barDockControlRight.Location" type="System.Drawing.Point, System.Drawing">
    <value>1174, 31</value>
  </data>
  <data name="barDockControlRight.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 688</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Name" xml:space="preserve">
    <value>barDockControlRight</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.ZOrder" xml:space="preserve">
    <value>85</value>
  </data>
  <metadata name="$this.Language" type="System.Globalization.CultureInfo, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>ar-EG</value>
  </metadata>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>25</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>1174, 719</value>
  </data>
  <data name="labelControl57.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl57.Location" type="System.Drawing.Point, System.Drawing">
    <value>174, 458</value>
  </data>
  <data name="labelControl57.Size" type="System.Drawing.Size, System.Drawing">
    <value>67, 13</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="labelControl57.TabIndex" type="System.Int32, mscorlib">
    <value>347</value>
  </data>
  <data name="labelControl57.Text" xml:space="preserve">
    <value>Bouns Discout</value>
  </data>
  <data name="&gt;&gt;labelControl57.Name" xml:space="preserve">
    <value>labelControl57</value>
  </data>
  <data name="&gt;&gt;labelControl57.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl57.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl57.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="txt_bounsDiscount.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_bounsDiscount.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_bounsDiscount.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_bounsDiscount.Location" type="System.Drawing.Point, System.Drawing">
    <value>11, 455</value>
  </data>
  <data name="txt_bounsDiscount.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_bounsDiscount.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_bounsDiscount.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <assembly alias="DevExpress.XtraEditors.v15.1" name="DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="txt_bounsDiscount.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_bounsDiscount.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_bounsDiscount.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_bounsDiscount.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_bounsDiscount.Size" type="System.Drawing.Size, System.Drawing">
    <value>156, 20</value>
  </data>
  <data name="txt_bounsDiscount.TabIndex" type="System.Int32, mscorlib">
    <value>346</value>
  </data>
  <data name="&gt;&gt;txt_bounsDiscount.Name" xml:space="preserve">
    <value>txt_bounsDiscount</value>
  </data>
  <data name="&gt;&gt;txt_bounsDiscount.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_bounsDiscount.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_bounsDiscount.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="grd_SubTaxes.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left, Right</value>
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="grd_SubTaxes.EmbeddedNavigator.AllowHtmlTextInToolTip" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>True</value>
  </data>
  <data name="grd_SubTaxes.EmbeddedNavigator.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="grd_SubTaxes.EmbeddedNavigator.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="grd_SubTaxes.EmbeddedNavigator.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="grd_SubTaxes.EmbeddedNavigator.TextLocation" type="DevExpress.XtraEditors.NavigatorButtonsTextLocation, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <assembly alias="DevExpress.Utils.v15.1" name="DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="grd_SubTaxes.EmbeddedNavigator.ToolTipIconType" type="DevExpress.Utils.ToolTipIconType, DevExpress.Utils.v15.1">
    <value>Application</value>
  </data>
  <data name="grd_SubTaxes.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt</value>
  </data>
  <data name="grd_SubTaxes.Location" type="System.Drawing.Point, System.Drawing">
    <value>287, 547</value>
  </data>
  <data name="Value.Caption" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="Value.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="Value.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="Value.Width" type="System.Int32, mscorlib">
    <value>178</value>
  </data>
  <data name="SubTaxId.Caption" xml:space="preserve">
    <value>Sub Taxes</value>
  </data>
  <data name="lkp_SubTaxes.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_SubTaxes.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_SubTaxes.Columns" xml:space="preserve">
    <value>Description Ar</value>
  </data>
  <data name="lkp_SubTaxes.Columns1" xml:space="preserve">
    <value>DescriptionAr</value>
  </data>
  <data name="lkp_SubTaxes.Columns2" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="lkp_SubTaxes.Columns3" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="lkp_SubTaxes.Columns4" xml:space="preserve">
    <value>E_TaxableTypeId</value>
  </data>
  <data name="lkp_SubTaxes.Columns5" xml:space="preserve">
    <value>E_TaxableTypeId</value>
  </data>
  <data name="lkp_SubTaxes.Columns6" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_SubTaxes.Columns7" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_SubTaxes.Columns8" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_SubTaxes.Columns9" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_SubTaxes.Columns10" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkp_SubTaxes.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="SubTaxId.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="SubTaxId.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="SubTaxId.Width" type="System.Int32, mscorlib">
    <value>275</value>
  </data>
  <data name="col_Rate.Caption" xml:space="preserve">
    <value>Rate</value>
  </data>
  <data name="col_Rate.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_Rate.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="col_Rate.Width" type="System.Int32, mscorlib">
    <value>180</value>
  </data>
  <data name="grd_SubTaxes.Size" type="System.Drawing.Size, System.Drawing">
    <value>389, 153</value>
  </data>
  <data name="grd_SubTaxes.TabIndex" type="System.Int32, mscorlib">
    <value>341</value>
  </data>
  <data name="&gt;&gt;grd_SubTaxes.Name" xml:space="preserve">
    <value>grd_SubTaxes</value>
  </data>
  <data name="&gt;&gt;grd_SubTaxes.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.GridControl, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;grd_SubTaxes.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;grd_SubTaxes.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="labelControl56.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl56.Location" type="System.Drawing.Point, System.Drawing">
    <value>173, 646</value>
  </data>
  <data name="labelControl56.Size" type="System.Drawing.Size, System.Drawing">
    <value>77, 13</value>
  </data>
  <data name="labelControl56.TabIndex" type="System.Int32, mscorlib">
    <value>285</value>
  </data>
  <data name="labelControl56.Text" xml:space="preserve">
    <value>Total with taxes</value>
  </data>
  <data name="&gt;&gt;labelControl56.Name" xml:space="preserve">
    <value>labelControl56</value>
  </data>
  <data name="&gt;&gt;labelControl56.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl56.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl56.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="labelControl40.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl40.Location" type="System.Drawing.Point, System.Drawing">
    <value>174, 529</value>
  </data>
  <data name="labelControl40.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 13</value>
  </data>
  <data name="labelControl40.TabIndex" type="System.Int32, mscorlib">
    <value>133</value>
  </data>
  <data name="labelControl40.Text" xml:space="preserve">
    <value>Sub Total</value>
  </data>
  <data name="&gt;&gt;labelControl40.Name" xml:space="preserve">
    <value>labelControl40</value>
  </data>
  <data name="&gt;&gt;labelControl40.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl40.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl40.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="labelControl55.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl55.Location" type="System.Drawing.Point, System.Drawing">
    <value>183, 36</value>
  </data>
  <data name="labelControl55.Size" type="System.Drawing.Size, System.Drawing">
    <value>66, 13</value>
  </data>
  <data name="labelControl55.TabIndex" type="System.Int32, mscorlib">
    <value>325</value>
  </data>
  <data name="labelControl55.Text" xml:space="preserve">
    <value>Invoices Book</value>
  </data>
  <data name="&gt;&gt;labelControl55.Name" xml:space="preserve">
    <value>labelControl55</value>
  </data>
  <data name="&gt;&gt;labelControl55.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl55.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl55.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="chk_Approved.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="chk_Approved.Location" type="System.Drawing.Point, System.Drawing">
    <value>321, 34</value>
  </data>
  <data name="chk_Approved.Properties.AppearanceReadOnly.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>64, 64, 64</value>
  </data>
  <data name="chk_Approved.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chk_Approved.Properties.Caption" xml:space="preserve">
    <value>Approve</value>
  </data>
  <data name="chk_Approved.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="chk_Approved.Size" type="System.Drawing.Size, System.Drawing">
    <value>63, 19</value>
  </data>
  <data name="chk_Approved.TabIndex" type="System.Int32, mscorlib">
    <value>319</value>
  </data>
  <data name="&gt;&gt;chk_Approved.Name" xml:space="preserve">
    <value>chk_Approved</value>
  </data>
  <data name="&gt;&gt;chk_Approved.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;chk_Approved.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;chk_Approved.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="chk_Offer.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="chk_Offer.Location" type="System.Drawing.Point, System.Drawing">
    <value>255, 34</value>
  </data>
  <data name="chk_Offer.Properties.AppearanceReadOnly.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>64, 64, 64</value>
  </data>
  <data name="chk_Offer.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chk_Offer.Properties.Caption" xml:space="preserve">
    <value>Offer</value>
  </data>
  <data name="chk_Offer.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="chk_Offer.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 19</value>
  </data>
  <data name="chk_Offer.TabIndex" type="System.Int32, mscorlib">
    <value>277</value>
  </data>
  <data name="&gt;&gt;chk_Offer.Name" xml:space="preserve">
    <value>chk_Offer</value>
  </data>
  <data name="&gt;&gt;chk_Offer.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;chk_Offer.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;chk_Offer.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="chk_IsOutTrns.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="chk_IsOutTrns.Location" type="System.Drawing.Point, System.Drawing">
    <value>378, 33</value>
  </data>
  <data name="chk_IsOutTrns.Properties.AppearanceReadOnly.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>64, 64, 64</value>
  </data>
  <data name="chk_IsOutTrns.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chk_IsOutTrns.Properties.Caption" xml:space="preserve">
    <value>Outgoing from store</value>
  </data>
  <data name="chk_IsOutTrns.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="chk_IsOutTrns.Size" type="System.Drawing.Size, System.Drawing">
    <value>143, 19</value>
  </data>
  <data name="chk_IsOutTrns.TabIndex" type="System.Int32, mscorlib">
    <value>272</value>
  </data>
  <data name="&gt;&gt;chk_IsOutTrns.Name" xml:space="preserve">
    <value>chk_IsOutTrns</value>
  </data>
  <data name="&gt;&gt;chk_IsOutTrns.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;chk_IsOutTrns.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;chk_IsOutTrns.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="labelControl3.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl3.Appearance.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt, style=Underline</value>
  </data>
  <data name="labelControl3.Location" type="System.Drawing.Point, System.Drawing">
    <value>648, 36</value>
  </data>
  <data name="labelControl3.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 13</value>
  </data>
  <data name="labelControl3.TabIndex" type="System.Int32, mscorlib">
    <value>263</value>
  </data>
  <data name="labelControl3.Text" xml:space="preserve">
    <value>Pay Method</value>
  </data>
  <data name="&gt;&gt;labelControl3.Name" xml:space="preserve">
    <value>labelControl3</value>
  </data>
  <data name="&gt;&gt;labelControl3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl3.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="txt_AttnMr.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_AttnMr.Location" type="System.Drawing.Point, System.Drawing">
    <value>714, 32</value>
  </data>
  <data name="txt_AttnMr.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txt_AttnMr.Properties.Appearance.BackColor2" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txt_AttnMr.Properties.Appearance.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt, style=Underline</value>
  </data>
  <data name="txt_AttnMr.Properties.AppearanceDisabled.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txt_AttnMr.Properties.AppearanceFocused.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txt_AttnMr.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AttnMr.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_AttnMr.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AttnMr.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AttnMr.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AttnMr.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_AttnMr.Size" type="System.Drawing.Size, System.Drawing">
    <value>198, 20</value>
  </data>
  <data name="txt_AttnMr.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;txt_AttnMr.Name" xml:space="preserve">
    <value>txt_AttnMr</value>
  </data>
  <data name="&gt;&gt;txt_AttnMr.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_AttnMr.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_AttnMr.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="labelControl41.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl41.Location" type="System.Drawing.Point, System.Drawing">
    <value>918, 36</value>
  </data>
  <data name="labelControl41.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 13</value>
  </data>
  <data name="labelControl41.TabIndex" type="System.Int32, mscorlib">
    <value>242</value>
  </data>
  <data name="labelControl41.Text" xml:space="preserve">
    <value>Attn Mr.</value>
  </data>
  <data name="&gt;&gt;labelControl41.Name" xml:space="preserve">
    <value>labelControl41</value>
  </data>
  <data name="&gt;&gt;labelControl41.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl41.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl41.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="grp_ExtraRevenues.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="btnCeil.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 46</value>
  </data>
  <data name="btnCeil.Size" type="System.Drawing.Size, System.Drawing">
    <value>97, 23</value>
  </data>
  <data name="btnCeil.TabIndex" type="System.Int32, mscorlib">
    <value>238</value>
  </data>
  <data name="btnCeil.Text" xml:space="preserve">
    <value>Ceiling</value>
  </data>
  <data name="&gt;&gt;btnCeil.Name" xml:space="preserve">
    <value>btnCeil</value>
  </data>
  <data name="&gt;&gt;btnCeil.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;btnCeil.Parent" xml:space="preserve">
    <value>grp_ExtraRevenues</value>
  </data>
  <data name="&gt;&gt;btnCeil.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txt_ShiftAdd.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_ShiftAdd.Location" type="System.Drawing.Point, System.Drawing">
    <value>11, 18</value>
  </data>
  <data name="txt_ShiftAdd.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_ShiftAdd.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_ShiftAdd.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_ShiftAdd.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_ShiftAdd.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_ShiftAdd.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_ShiftAdd.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_ShiftAdd.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="txt_ShiftAdd.TabIndex" type="System.Int32, mscorlib">
    <value>102</value>
  </data>
  <data name="&gt;&gt;txt_ShiftAdd.Name" xml:space="preserve">
    <value>txt_ShiftAdd</value>
  </data>
  <data name="&gt;&gt;txt_ShiftAdd.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_ShiftAdd.Parent" xml:space="preserve">
    <value>grp_ExtraRevenues</value>
  </data>
  <data name="&gt;&gt;txt_ShiftAdd.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="labelControl53.Location" type="System.Drawing.Point, System.Drawing">
    <value>126, 20</value>
  </data>
  <data name="labelControl53.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 13</value>
  </data>
  <data name="labelControl53.TabIndex" type="System.Int32, mscorlib">
    <value>103</value>
  </data>
  <data name="&gt;&gt;labelControl53.Name" xml:space="preserve">
    <value>labelControl53</value>
  </data>
  <data name="&gt;&gt;labelControl53.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl53.Parent" xml:space="preserve">
    <value>grp_ExtraRevenues</value>
  </data>
  <data name="&gt;&gt;labelControl53.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="txt_transportation.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_transportation.Location" type="System.Drawing.Point, System.Drawing">
    <value>167, 14</value>
  </data>
  <data name="txt_transportation.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_transportation.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_transportation.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_transportation.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_transportation.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_transportation.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_transportation.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_transportation.Size" type="System.Drawing.Size, System.Drawing">
    <value>158, 20</value>
  </data>
  <data name="txt_transportation.TabIndex" type="System.Int32, mscorlib">
    <value>25</value>
  </data>
  <data name="&gt;&gt;txt_transportation.Name" xml:space="preserve">
    <value>txt_transportation</value>
  </data>
  <data name="&gt;&gt;txt_transportation.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_transportation.Parent" xml:space="preserve">
    <value>grp_ExtraRevenues</value>
  </data>
  <data name="&gt;&gt;txt_transportation.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="labelControl49.Location" type="System.Drawing.Point, System.Drawing">
    <value>331, 17</value>
  </data>
  <data name="labelControl49.Size" type="System.Drawing.Size, System.Drawing">
    <value>49, 13</value>
  </data>
  <data name="labelControl49.TabIndex" type="System.Int32, mscorlib">
    <value>90</value>
  </data>
  <data name="labelControl49.Text" xml:space="preserve">
    <value>تكاليف نقل</value>
  </data>
  <data name="&gt;&gt;labelControl49.Name" xml:space="preserve">
    <value>labelControl49</value>
  </data>
  <data name="&gt;&gt;labelControl49.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl49.Parent" xml:space="preserve">
    <value>grp_ExtraRevenues</value>
  </data>
  <data name="&gt;&gt;labelControl49.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="txt_Handing.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_Handing.Location" type="System.Drawing.Point, System.Drawing">
    <value>166, 49</value>
  </data>
  <data name="txt_Handing.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Handing.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Handing.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Handing.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_Handing.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Handing.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Handing.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Handing.Size" type="System.Drawing.Size, System.Drawing">
    <value>159, 20</value>
  </data>
  <data name="txt_Handing.TabIndex" type="System.Int32, mscorlib">
    <value>27</value>
  </data>
  <data name="&gt;&gt;txt_Handing.Name" xml:space="preserve">
    <value>txt_Handing</value>
  </data>
  <data name="&gt;&gt;txt_Handing.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_Handing.Parent" xml:space="preserve">
    <value>grp_ExtraRevenues</value>
  </data>
  <data name="&gt;&gt;txt_Handing.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="labelControl50.Location" type="System.Drawing.Point, System.Drawing">
    <value>330, 51</value>
  </data>
  <data name="labelControl50.Size" type="System.Drawing.Size, System.Drawing">
    <value>58, 13</value>
  </data>
  <data name="labelControl50.TabIndex" type="System.Int32, mscorlib">
    <value>101</value>
  </data>
  <data name="labelControl50.Text" xml:space="preserve">
    <value>تكاليف ناولون</value>
  </data>
  <data name="&gt;&gt;labelControl50.Name" xml:space="preserve">
    <value>labelControl50</value>
  </data>
  <data name="&gt;&gt;labelControl50.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl50.Parent" xml:space="preserve">
    <value>grp_ExtraRevenues</value>
  </data>
  <data name="&gt;&gt;labelControl50.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="grp_ExtraRevenues.Location" type="System.Drawing.Point, System.Drawing">
    <value>568, 395</value>
  </data>
  <data name="grp_ExtraRevenues.Size" type="System.Drawing.Size, System.Drawing">
    <value>411, 101</value>
  </data>
  <data name="grp_ExtraRevenues.TabIndex" type="System.Int32, mscorlib">
    <value>308</value>
  </data>
  <data name="grp_ExtraRevenues.Text" xml:space="preserve">
    <value>Paid</value>
  </data>
  <data name="&gt;&gt;grp_ExtraRevenues.Name" xml:space="preserve">
    <value>grp_ExtraRevenues</value>
  </data>
  <data name="&gt;&gt;grp_ExtraRevenues.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GroupControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;grp_ExtraRevenues.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;grp_ExtraRevenues.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="cmbPayMethod.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="cmbPayMethod.EditValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbPayMethod.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbPayMethod.Location" type="System.Drawing.Point, System.Drawing">
    <value>537, 33</value>
  </data>
  <data name="cmbPayMethod.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="cmbPayMethod.Properties.Appearance.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt, style=Bold</value>
  </data>
  <data name="cmbPayMethod.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="cmbPayMethod.Properties.AppearanceDisabled.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="cmbPayMethod.Properties.AppearanceDisabled.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt, style=Bold, Underline</value>
  </data>
  <data name="cmbPayMethod.Properties.AppearanceDisabled.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>64, 64, 64</value>
  </data>
  <data name="cmbPayMethod.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbPayMethod.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Down</value>
  </data>
  <data name="cmbPayMethod.Properties.Buttons1" xml:space="preserve">
    <value />
  </data>
  <data name="cmbPayMethod.Properties.Buttons2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbPayMethod.Properties.Buttons3" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cmbPayMethod.Properties.Buttons4" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbPayMethod.Properties.Buttons5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbPayMethod.Properties.Buttons6" type="DevExpress.XtraEditors.ImageLocation, DevExpress.XtraEditors.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="cmbPayMethod.Properties.Buttons7" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmbPayMethod.Properties.Buttons8" xml:space="preserve">
    <value />
  </data>
  <data name="cmbPayMethod.Properties.Buttons9" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmbPayMethod.Properties.Buttons10" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmbPayMethod.Properties.Buttons11" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cmbPayMethod.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="cmbPayMethod.Properties.Items" xml:space="preserve">
    <value>On Credit</value>
  </data>
  <data name="cmbPayMethod.Properties.Items1" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbPayMethod.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbPayMethod.Properties.Items3" xml:space="preserve">
    <value>Cash</value>
  </data>
  <data name="cmbPayMethod.Properties.Items4" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cmbPayMethod.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbPayMethod.Properties.Items6" xml:space="preserve">
    <value>Cash / On Credit</value>
  </data>
  <data name="cmbPayMethod.Properties.Items7" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmbPayMethod.Properties.Items8" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbPayMethod.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmbPayMethod.Size" type="System.Drawing.Size, System.Drawing">
    <value>105, 18</value>
  </data>
  <data name="cmbPayMethod.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;cmbPayMethod.Name" xml:space="preserve">
    <value>cmbPayMethod</value>
  </data>
  <data name="&gt;&gt;cmbPayMethod.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cmbPayMethod.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;cmbPayMethod.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="btnAddCustomer.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnAddCustomer.Location" type="System.Drawing.Point, System.Drawing">
    <value>967, 33</value>
  </data>
  <data name="btnAddCustomer.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 19</value>
  </data>
  <data name="btnAddCustomer.TabIndex" type="System.Int32, mscorlib">
    <value>149</value>
  </data>
  <data name="btnAddCustomer.Text" xml:space="preserve">
    <value>+</value>
  </data>
  <data name="btnAddCustomer.ToolTip" xml:space="preserve">
    <value>Add Customer</value>
  </data>
  <data name="&gt;&gt;btnAddCustomer.Name" xml:space="preserve">
    <value>btnAddCustomer</value>
  </data>
  <data name="&gt;&gt;btnAddCustomer.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;btnAddCustomer.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnAddCustomer.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="lkp_Customers.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_Customers.EditValue" xml:space="preserve">
    <value> </value>
  </data>
  <data name="lkp_Customers.Location" type="System.Drawing.Point, System.Drawing">
    <value>993, 32</value>
  </data>
  <data name="lkp_Customers.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_Customers.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_Customers.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Customers.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn19.Caption" xml:space="preserve">
    <value>CustomerId</value>
  </data>
  <data name="gridColumn20.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="gridColumn20.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn20.VisibleIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="gridColumn20.Width" type="System.Int32, mscorlib">
    <value>143</value>
  </data>
  <data name="gridColumn21.Caption" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="gridColumn21.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn21.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="gridColumn21.Width" type="System.Int32, mscorlib">
    <value>404</value>
  </data>
  <data name="gridColumn22.Caption" xml:space="preserve">
    <value>F Name </value>
  </data>
  <data name="gridColumn22.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn22.VisibleIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="gridColumn22.Width" type="System.Int32, mscorlib">
    <value>209</value>
  </data>
  <data name="gridColumn26.Caption" xml:space="preserve">
    <value>Group</value>
  </data>
  <data name="gridColumn26.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn26.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn26.Width" type="System.Int32, mscorlib">
    <value>196</value>
  </data>
  <data name="gridColumn27.Caption" xml:space="preserve">
    <value>City</value>
  </data>
  <data name="gridColumn27.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn27.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn27.Width" type="System.Int32, mscorlib">
    <value>182</value>
  </data>
  <data name="gridColumn30.Caption" xml:space="preserve">
    <value>Mobile</value>
  </data>
  <data name="gridColumn30.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn30.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn30.Width" type="System.Int32, mscorlib">
    <value>182</value>
  </data>
  <data name="gridColumn35.Caption" xml:space="preserve">
    <value>gridColumn35</value>
  </data>
  <data name="lkp_Customers.Size" type="System.Drawing.Size, System.Drawing">
    <value>181, 20</value>
  </data>
  <data name="lkp_Customers.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;lkp_Customers.Name" xml:space="preserve">
    <value>lkp_Customers</value>
  </data>
  <data name="&gt;&gt;lkp_Customers.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_Customers.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lkp_Customers.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="labelControl35.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl35.Location" type="System.Drawing.Point, System.Drawing">
    <value>1180, 36</value>
  </data>
  <data name="labelControl35.Size" type="System.Drawing.Size, System.Drawing">
    <value>46, 13</value>
  </data>
  <data name="labelControl35.TabIndex" type="System.Int32, mscorlib">
    <value>85</value>
  </data>
  <data name="labelControl35.Text" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="&gt;&gt;labelControl35.Name" xml:space="preserve">
    <value>labelControl35</value>
  </data>
  <data name="&gt;&gt;labelControl35.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl35.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl35.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="txt_Subtotal.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_Subtotal.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_Subtotal.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 528</value>
  </data>
  <data name="txt_Subtotal.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Subtotal.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="txt_Subtotal.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Subtotal.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_Subtotal.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Subtotal.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Subtotal.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Subtotal.Size" type="System.Drawing.Size, System.Drawing">
    <value>156, 20</value>
  </data>
  <data name="txt_Subtotal.TabIndex" type="System.Int32, mscorlib">
    <value>336</value>
  </data>
  <data name="&gt;&gt;txt_Subtotal.Name" xml:space="preserve">
    <value>txt_Subtotal</value>
  </data>
  <data name="&gt;&gt;txt_Subtotal.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_Subtotal.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_Subtotal.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="labelControl29.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl29.Location" type="System.Drawing.Point, System.Drawing">
    <value>174, 432</value>
  </data>
  <data name="labelControl29.Size" type="System.Drawing.Size, System.Drawing">
    <value>27, 13</value>
  </data>
  <data name="labelControl29.TabIndex" type="System.Int32, mscorlib">
    <value>331</value>
  </data>
  <data name="labelControl29.Text" xml:space="preserve">
    <value>Tax V</value>
  </data>
  <data name="&gt;&gt;labelControl29.Name" xml:space="preserve">
    <value>labelControl29</value>
  </data>
  <data name="&gt;&gt;labelControl29.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl29.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl29.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="txt_EtaxValue.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_EtaxValue.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_EtaxValue.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_EtaxValue.Location" type="System.Drawing.Point, System.Drawing">
    <value>11, 429</value>
  </data>
  <data name="txt_EtaxValue.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_EtaxValue.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_EtaxValue.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_EtaxValue.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_EtaxValue.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_EtaxValue.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_EtaxValue.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_EtaxValue.Size" type="System.Drawing.Size, System.Drawing">
    <value>156, 20</value>
  </data>
  <data name="txt_EtaxValue.TabIndex" type="System.Int32, mscorlib">
    <value>330</value>
  </data>
  <data name="&gt;&gt;txt_EtaxValue.Name" xml:space="preserve">
    <value>txt_EtaxValue</value>
  </data>
  <data name="&gt;&gt;txt_EtaxValue.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_EtaxValue.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_EtaxValue.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="bookId.EditValue" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="bookId.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 33</value>
  </data>
  <data name="bookId.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="bookId.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="bookId.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="bookId.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="bookId.Properties.Items1" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="bookId.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="bookId.Properties.Items3" xml:space="preserve">
    <value>Tax</value>
  </data>
  <data name="bookId.Properties.Items4" type="System.Byte, mscorlib">
    <value>2</value>
  </data>
  <data name="bookId.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="bookId.Properties.Items6" xml:space="preserve">
    <value>Non Tax</value>
  </data>
  <data name="bookId.Properties.Items7" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="bookId.Properties.Items8" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="bookId.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="bookId.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="bookId.TabIndex" type="System.Int32, mscorlib">
    <value>324</value>
  </data>
  <data name="&gt;&gt;bookId.Name" xml:space="preserve">
    <value>bookId</value>
  </data>
  <data name="&gt;&gt;bookId.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;bookId.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;bookId.ZOrder" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="labelControl37.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl37.Location" type="System.Drawing.Point, System.Drawing">
    <value>1020, 486</value>
  </data>
  <data name="labelControl37.Size" type="System.Drawing.Size, System.Drawing">
    <value>47, 13</value>
  </data>
  <data name="labelControl37.TabIndex" type="System.Int32, mscorlib">
    <value>298</value>
  </data>
  <data name="labelControl37.Text" xml:space="preserve">
    <value>Retention</value>
  </data>
  <data name="labelControl37.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl37.Name" xml:space="preserve">
    <value>labelControl37</value>
  </data>
  <data name="&gt;&gt;labelControl37.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl37.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl37.ZOrder" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="labelControl38.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl38.Location" type="System.Drawing.Point, System.Drawing">
    <value>1061, 486</value>
  </data>
  <data name="labelControl38.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="labelControl38.TabIndex" type="System.Int32, mscorlib">
    <value>296</value>
  </data>
  <data name="labelControl38.Text" xml:space="preserve">
    <value>V</value>
  </data>
  <data name="labelControl38.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl38.Name" xml:space="preserve">
    <value>labelControl38</value>
  </data>
  <data name="&gt;&gt;labelControl38.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl38.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl38.ZOrder" xml:space="preserve">
    <value>23</value>
  </data>
  <data name="labelControl39.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl39.Location" type="System.Drawing.Point, System.Drawing">
    <value>1061, 486</value>
  </data>
  <data name="labelControl39.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="labelControl39.TabIndex" type="System.Int32, mscorlib">
    <value>297</value>
  </data>
  <data name="labelControl39.Text" xml:space="preserve">
    <value>V</value>
  </data>
  <data name="labelControl39.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl39.Name" xml:space="preserve">
    <value>labelControl39</value>
  </data>
  <data name="&gt;&gt;labelControl39.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl39.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl39.ZOrder" xml:space="preserve">
    <value>24</value>
  </data>
  <data name="labelControl42.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl42.Location" type="System.Drawing.Point, System.Drawing">
    <value>1004, 486</value>
  </data>
  <data name="labelControl42.Size" type="System.Drawing.Size, System.Drawing">
    <value>63, 13</value>
  </data>
  <data name="labelControl42.TabIndex" type="System.Int32, mscorlib">
    <value>299</value>
  </data>
  <data name="labelControl42.Text" xml:space="preserve">
    <value>Advance Pay</value>
  </data>
  <data name="labelControl42.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl42.Name" xml:space="preserve">
    <value>labelControl42</value>
  </data>
  <data name="&gt;&gt;labelControl42.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl42.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl42.ZOrder" xml:space="preserve">
    <value>25</value>
  </data>
  <data name="labelControl43.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl43.Location" type="System.Drawing.Point, System.Drawing">
    <value>1060, 486</value>
  </data>
  <data name="labelControl43.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="labelControl43.TabIndex" type="System.Int32, mscorlib">
    <value>300</value>
  </data>
  <data name="labelControl43.Text" xml:space="preserve">
    <value>R</value>
  </data>
  <data name="labelControl43.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl43.Name" xml:space="preserve">
    <value>labelControl43</value>
  </data>
  <data name="&gt;&gt;labelControl43.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl43.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl43.ZOrder" xml:space="preserve">
    <value>26</value>
  </data>
  <data name="labelControl44.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl44.Location" type="System.Drawing.Point, System.Drawing">
    <value>1060, 486</value>
  </data>
  <data name="labelControl44.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="labelControl44.TabIndex" type="System.Int32, mscorlib">
    <value>301</value>
  </data>
  <data name="labelControl44.Text" xml:space="preserve">
    <value>R</value>
  </data>
  <data name="labelControl44.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl44.Name" xml:space="preserve">
    <value>labelControl44</value>
  </data>
  <data name="&gt;&gt;labelControl44.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl44.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl44.ZOrder" xml:space="preserve">
    <value>27</value>
  </data>
  <data name="labelControl45.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl45.Location" type="System.Drawing.Point, System.Drawing">
    <value>1056, 486</value>
  </data>
  <data name="labelControl45.Size" type="System.Drawing.Size, System.Drawing">
    <value>11, 13</value>
  </data>
  <data name="labelControl45.TabIndex" type="System.Int32, mscorlib">
    <value>302</value>
  </data>
  <data name="labelControl45.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="labelControl45.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl45.Name" xml:space="preserve">
    <value>labelControl45</value>
  </data>
  <data name="&gt;&gt;labelControl45.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl45.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl45.ZOrder" xml:space="preserve">
    <value>28</value>
  </data>
  <data name="labelControl46.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl46.Location" type="System.Drawing.Point, System.Drawing">
    <value>1056, 486</value>
  </data>
  <data name="labelControl46.Size" type="System.Drawing.Size, System.Drawing">
    <value>11, 13</value>
  </data>
  <data name="labelControl46.TabIndex" type="System.Int32, mscorlib">
    <value>303</value>
  </data>
  <data name="labelControl46.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="labelControl46.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl46.Name" xml:space="preserve">
    <value>labelControl46</value>
  </data>
  <data name="&gt;&gt;labelControl46.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl46.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl46.ZOrder" xml:space="preserve">
    <value>29</value>
  </data>
  <data name="txt_retentionR.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_retentionR.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_retentionR.Location" type="System.Drawing.Point, System.Drawing">
    <value>1032, 479</value>
  </data>
  <data name="txt_retentionR.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_retentionR.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_retentionR.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_retentionR.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_retentionR.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_retentionR.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_retentionR.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_retentionR.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 20</value>
  </data>
  <data name="txt_retentionR.TabIndex" type="System.Int32, mscorlib">
    <value>292</value>
  </data>
  <data name="txt_retentionR.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txt_retentionR.Name" xml:space="preserve">
    <value>txt_retentionR</value>
  </data>
  <data name="&gt;&gt;txt_retentionR.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_retentionR.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_retentionR.ZOrder" xml:space="preserve">
    <value>30</value>
  </data>
  <data name="txt_RetentionV.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_RetentionV.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_RetentionV.Location" type="System.Drawing.Point, System.Drawing">
    <value>979, 479</value>
  </data>
  <data name="txt_RetentionV.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_RetentionV.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="txt_RetentionV.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_RetentionV.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_RetentionV.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_RetentionV.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_RetentionV.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_RetentionV.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 20</value>
  </data>
  <data name="txt_RetentionV.TabIndex" type="System.Int32, mscorlib">
    <value>293</value>
  </data>
  <data name="txt_RetentionV.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txt_RetentionV.Name" xml:space="preserve">
    <value>txt_RetentionV</value>
  </data>
  <data name="&gt;&gt;txt_RetentionV.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_RetentionV.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_RetentionV.ZOrder" xml:space="preserve">
    <value>31</value>
  </data>
  <data name="txt_AdvancePayR.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_AdvancePayR.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_AdvancePayR.Location" type="System.Drawing.Point, System.Drawing">
    <value>1032, 479</value>
  </data>
  <data name="txt_AdvancePayR.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AdvancePayR.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_AdvancePayR.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AdvancePayR.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_AdvancePayR.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AdvancePayR.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AdvancePayR.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_AdvancePayR.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 20</value>
  </data>
  <data name="txt_AdvancePayR.TabIndex" type="System.Int32, mscorlib">
    <value>294</value>
  </data>
  <data name="txt_AdvancePayR.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txt_AdvancePayR.Name" xml:space="preserve">
    <value>txt_AdvancePayR</value>
  </data>
  <data name="&gt;&gt;txt_AdvancePayR.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_AdvancePayR.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_AdvancePayR.ZOrder" xml:space="preserve">
    <value>32</value>
  </data>
  <data name="txt_AdvancePayV.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_AdvancePayV.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_AdvancePayV.Location" type="System.Drawing.Point, System.Drawing">
    <value>979, 479</value>
  </data>
  <data name="txt_AdvancePayV.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AdvancePayV.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="txt_AdvancePayV.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AdvancePayV.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_AdvancePayV.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AdvancePayV.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AdvancePayV.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_AdvancePayV.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 20</value>
  </data>
  <data name="txt_AdvancePayV.TabIndex" type="System.Int32, mscorlib">
    <value>295</value>
  </data>
  <data name="txt_AdvancePayV.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txt_AdvancePayV.Name" xml:space="preserve">
    <value>txt_AdvancePayV</value>
  </data>
  <data name="&gt;&gt;txt_AdvancePayV.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_AdvancePayV.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_AdvancePayV.ZOrder" xml:space="preserve">
    <value>33</value>
  </data>
  <data name="labelControl27.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl27.Location" type="System.Drawing.Point, System.Drawing">
    <value>173, 623</value>
  </data>
  <data name="labelControl27.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="labelControl27.TabIndex" type="System.Int32, mscorlib">
    <value>284</value>
  </data>
  <data name="labelControl27.Text" xml:space="preserve">
    <value>V</value>
  </data>
  <data name="&gt;&gt;labelControl27.Name" xml:space="preserve">
    <value>labelControl27</value>
  </data>
  <data name="&gt;&gt;labelControl27.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl27.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl27.ZOrder" xml:space="preserve">
    <value>34</value>
  </data>
  <data name="labelControl28.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl28.Location" type="System.Drawing.Point, System.Drawing">
    <value>185, 623</value>
  </data>
  <data name="labelControl28.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 13</value>
  </data>
  <data name="labelControl28.TabIndex" type="System.Int32, mscorlib">
    <value>285</value>
  </data>
  <data name="labelControl28.Text" xml:space="preserve">
    <value>Cus Tax</value>
  </data>
  <data name="&gt;&gt;labelControl28.Name" xml:space="preserve">
    <value>labelControl28</value>
  </data>
  <data name="&gt;&gt;labelControl28.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl28.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl28.ZOrder" xml:space="preserve">
    <value>35</value>
  </data>
  <data name="labelControl32.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl32.Location" type="System.Drawing.Point, System.Drawing">
    <value>671, 376</value>
  </data>
  <data name="labelControl32.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="labelControl32.TabIndex" type="System.Int32, mscorlib">
    <value>286</value>
  </data>
  <data name="labelControl32.Text" xml:space="preserve">
    <value>R</value>
  </data>
  <data name="labelControl32.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl32.Name" xml:space="preserve">
    <value>labelControl32</value>
  </data>
  <data name="&gt;&gt;labelControl32.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl32.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl32.ZOrder" xml:space="preserve">
    <value>36</value>
  </data>
  <data name="labelControl34.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl34.Location" type="System.Drawing.Point, System.Drawing">
    <value>615, 375</value>
  </data>
  <data name="labelControl34.Size" type="System.Drawing.Size, System.Drawing">
    <value>11, 13</value>
  </data>
  <data name="labelControl34.TabIndex" type="System.Int32, mscorlib">
    <value>287</value>
  </data>
  <data name="labelControl34.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="labelControl34.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl34.Name" xml:space="preserve">
    <value>labelControl34</value>
  </data>
  <data name="&gt;&gt;labelControl34.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl34.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl34.ZOrder" xml:space="preserve">
    <value>37</value>
  </data>
  <data name="txt_CusTaxR.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_CusTaxR.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_CusTaxR.Location" type="System.Drawing.Point, System.Drawing">
    <value>629, 373</value>
  </data>
  <data name="txt_CusTaxR.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_CusTaxR.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_CusTaxR.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_CusTaxR.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_CusTaxR.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_CusTaxR.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_CusTaxR.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_CusTaxR.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 20</value>
  </data>
  <data name="txt_CusTaxR.TabIndex" type="System.Int32, mscorlib">
    <value>282</value>
  </data>
  <data name="txt_CusTaxR.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txt_CusTaxR.Name" xml:space="preserve">
    <value>txt_CusTaxR</value>
  </data>
  <data name="&gt;&gt;txt_CusTaxR.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_CusTaxR.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_CusTaxR.ZOrder" xml:space="preserve">
    <value>38</value>
  </data>
  <data name="txt_SubAfterTax.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_SubAfterTax.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_SubAfterTax.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_SubAfterTax.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 643</value>
  </data>
  <data name="txt_SubAfterTax.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_SubAfterTax.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="txt_SubAfterTax.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_SubAfterTax.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_SubAfterTax.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_SubAfterTax.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_SubAfterTax.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_SubAfterTax.Size" type="System.Drawing.Size, System.Drawing">
    <value>156, 20</value>
  </data>
  <data name="txt_SubAfterTax.TabIndex" type="System.Int32, mscorlib">
    <value>283</value>
  </data>
  <data name="&gt;&gt;txt_SubAfterTax.Name" xml:space="preserve">
    <value>txt_SubAfterTax</value>
  </data>
  <data name="&gt;&gt;txt_SubAfterTax.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_SubAfterTax.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_SubAfterTax.ZOrder" xml:space="preserve">
    <value>39</value>
  </data>
  <data name="txt_CusTaxV.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_CusTaxV.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_CusTaxV.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_CusTaxV.Location" type="System.Drawing.Point, System.Drawing">
    <value>11, 620</value>
  </data>
  <data name="txt_CusTaxV.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_CusTaxV.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="txt_CusTaxV.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_CusTaxV.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_CusTaxV.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_CusTaxV.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_CusTaxV.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_CusTaxV.Size" type="System.Drawing.Size, System.Drawing">
    <value>156, 20</value>
  </data>
  <data name="txt_CusTaxV.TabIndex" type="System.Int32, mscorlib">
    <value>283</value>
  </data>
  <data name="&gt;&gt;txt_CusTaxV.Name" xml:space="preserve">
    <value>txt_CusTaxV</value>
  </data>
  <data name="&gt;&gt;txt_CusTaxV.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_CusTaxV.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_CusTaxV.ZOrder" xml:space="preserve">
    <value>40</value>
  </data>
  <data name="labelControl5.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl5.Location" type="System.Drawing.Point, System.Drawing">
    <value>103, 671</value>
  </data>
  <data name="labelControl5.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="labelControl5.TabIndex" type="System.Int32, mscorlib">
    <value>269</value>
  </data>
  <data name="labelControl5.Text" xml:space="preserve">
    <value>V</value>
  </data>
  <data name="&gt;&gt;labelControl5.Name" xml:space="preserve">
    <value>labelControl5</value>
  </data>
  <data name="&gt;&gt;labelControl5.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl5.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl5.ZOrder" xml:space="preserve">
    <value>41</value>
  </data>
  <data name="labelControl8.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl8.Location" type="System.Drawing.Point, System.Drawing">
    <value>119, 671</value>
  </data>
  <data name="labelControl8.Size" type="System.Drawing.Size, System.Drawing">
    <value>11, 13</value>
  </data>
  <data name="labelControl8.TabIndex" type="System.Int32, mscorlib">
    <value>270</value>
  </data>
  <data name="labelControl8.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="&gt;&gt;labelControl8.Name" xml:space="preserve">
    <value>labelControl8</value>
  </data>
  <data name="&gt;&gt;labelControl8.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl8.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl8.ZOrder" xml:space="preserve">
    <value>42</value>
  </data>
  <data name="xtraTabControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left, Right</value>
  </data>
  <data name="xtraTabControl1.HeaderAutoFill" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>True</value>
  </data>
  <data name="xtraTabControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>681, 530</value>
  </data>
  <data name="btn_ShowAccStatement.Location" type="System.Drawing.Point, System.Drawing">
    <value>8, 26</value>
  </data>
  <data name="btn_ShowAccStatement.Size" type="System.Drawing.Size, System.Drawing">
    <value>162, 75</value>
  </data>
  <data name="btn_ShowAccStatement.TabIndex" type="System.Int32, mscorlib">
    <value>237</value>
  </data>
  <data name="btn_ShowAccStatement.Text" xml:space="preserve">
    <value>Show Account Statement</value>
  </data>
  <data name="btn_ShowAccStatement.ToolTip" xml:space="preserve">
    <value>Show Account Statement</value>
  </data>
  <data name="&gt;&gt;btn_ShowAccStatement.Name" xml:space="preserve">
    <value>btn_ShowAccStatement</value>
  </data>
  <data name="&gt;&gt;btn_ShowAccStatement.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;btn_ShowAccStatement.Parent" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="&gt;&gt;btn_ShowAccStatement.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txt_Balance_After.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_Balance_After.AutoSizeMode" type="DevExpress.XtraEditors.LabelAutoSizeMode, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_Balance_After.Location" type="System.Drawing.Point, System.Drawing">
    <value>112, 55</value>
  </data>
  <data name="txt_Balance_After.Size" type="System.Drawing.Size, System.Drawing">
    <value>140, 13</value>
  </data>
  <data name="txt_Balance_After.TabIndex" type="System.Int32, mscorlib">
    <value>220</value>
  </data>
  <data name="txt_Balance_After.Text" xml:space="preserve">
    <value>..</value>
  </data>
  <data name="&gt;&gt;txt_Balance_After.Name" xml:space="preserve">
    <value>txt_Balance_After</value>
  </data>
  <data name="&gt;&gt;txt_Balance_After.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_Balance_After.Parent" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="&gt;&gt;txt_Balance_After.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="txt_Balance_Before.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_Balance_Before.AutoSizeMode" type="DevExpress.XtraEditors.LabelAutoSizeMode, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_Balance_Before.Location" type="System.Drawing.Point, System.Drawing">
    <value>112, 32</value>
  </data>
  <data name="txt_Balance_Before.Size" type="System.Drawing.Size, System.Drawing">
    <value>140, 13</value>
  </data>
  <data name="txt_Balance_Before.TabIndex" type="System.Int32, mscorlib">
    <value>219</value>
  </data>
  <data name="txt_Balance_Before.Text" xml:space="preserve">
    <value>..</value>
  </data>
  <data name="&gt;&gt;txt_Balance_Before.Name" xml:space="preserve">
    <value>txt_Balance_Before</value>
  </data>
  <data name="&gt;&gt;txt_Balance_Before.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_Balance_Before.Parent" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="&gt;&gt;txt_Balance_Before.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="lbl_Validate_MaxLimit.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lbl_Validate_MaxLimit.Appearance.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 10pt, style=Bold</value>
  </data>
  <data name="lbl_Validate_MaxLimit.AutoSizeMode" type="DevExpress.XtraEditors.LabelAutoSizeMode, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lbl_Validate_MaxLimit.Location" type="System.Drawing.Point, System.Drawing">
    <value>67, 74</value>
  </data>
  <data name="lbl_Validate_MaxLimit.Size" type="System.Drawing.Size, System.Drawing">
    <value>352, 19</value>
  </data>
  <data name="lbl_Validate_MaxLimit.TabIndex" type="System.Int32, mscorlib">
    <value>175</value>
  </data>
  <data name="lbl_Validate_MaxLimit.Text" xml:space="preserve">
    <value>...</value>
  </data>
  <data name="&gt;&gt;lbl_Validate_MaxLimit.Name" xml:space="preserve">
    <value>lbl_Validate_MaxLimit</value>
  </data>
  <data name="&gt;&gt;lbl_Validate_MaxLimit.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lbl_Validate_MaxLimit.Parent" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="&gt;&gt;lbl_Validate_MaxLimit.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="txt_MaxCredit.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_MaxCredit.AutoSizeMode" type="DevExpress.XtraEditors.LabelAutoSizeMode, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_MaxCredit.Location" type="System.Drawing.Point, System.Drawing">
    <value>112, 9</value>
  </data>
  <data name="txt_MaxCredit.Size" type="System.Drawing.Size, System.Drawing">
    <value>140, 13</value>
  </data>
  <data name="txt_MaxCredit.TabIndex" type="System.Int32, mscorlib">
    <value>218</value>
  </data>
  <data name="txt_MaxCredit.Text" xml:space="preserve">
    <value>..</value>
  </data>
  <data name="&gt;&gt;txt_MaxCredit.Name" xml:space="preserve">
    <value>txt_MaxCredit</value>
  </data>
  <data name="&gt;&gt;txt_MaxCredit.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_MaxCredit.Parent" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="&gt;&gt;txt_MaxCredit.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="lbl_IsCredit_After.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lbl_IsCredit_After.Location" type="System.Drawing.Point, System.Drawing">
    <value>268, 55</value>
  </data>
  <data name="lbl_IsCredit_After.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 13</value>
  </data>
  <data name="lbl_IsCredit_After.TabIndex" type="System.Int32, mscorlib">
    <value>217</value>
  </data>
  <data name="lbl_IsCredit_After.Text" xml:space="preserve">
    <value>Credit</value>
  </data>
  <data name="&gt;&gt;lbl_IsCredit_After.Name" xml:space="preserve">
    <value>lbl_IsCredit_After</value>
  </data>
  <data name="&gt;&gt;lbl_IsCredit_After.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lbl_IsCredit_After.Parent" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="&gt;&gt;lbl_IsCredit_After.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="lbl_IsCredit_Before.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lbl_IsCredit_Before.Location" type="System.Drawing.Point, System.Drawing">
    <value>268, 32</value>
  </data>
  <data name="lbl_IsCredit_Before.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 13</value>
  </data>
  <data name="lbl_IsCredit_Before.TabIndex" type="System.Int32, mscorlib">
    <value>215</value>
  </data>
  <data name="lbl_IsCredit_Before.Text" xml:space="preserve">
    <value>Credit</value>
  </data>
  <data name="&gt;&gt;lbl_IsCredit_Before.Name" xml:space="preserve">
    <value>lbl_IsCredit_Before</value>
  </data>
  <data name="&gt;&gt;lbl_IsCredit_Before.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lbl_IsCredit_Before.Parent" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="&gt;&gt;lbl_IsCredit_Before.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="lblBlncAftr.Location" type="System.Drawing.Point, System.Drawing">
    <value>382, 55</value>
  </data>
  <data name="lblBlncAftr.Size" type="System.Drawing.Size, System.Drawing">
    <value>103, 13</value>
  </data>
  <data name="lblBlncAftr.TabIndex" type="System.Int32, mscorlib">
    <value>216</value>
  </data>
  <data name="lblBlncAftr.Text" xml:space="preserve">
    <value>Balance After Invoice</value>
  </data>
  <data name="&gt;&gt;lblBlncAftr.Name" xml:space="preserve">
    <value>lblBlncAftr</value>
  </data>
  <data name="&gt;&gt;lblBlncAftr.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lblBlncAftr.Parent" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="&gt;&gt;lblBlncAftr.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="labelControl10.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl10.Location" type="System.Drawing.Point, System.Drawing">
    <value>311, 9</value>
  </data>
  <data name="labelControl10.Size" type="System.Drawing.Size, System.Drawing">
    <value>52, 13</value>
  </data>
  <data name="labelControl10.TabIndex" type="System.Int32, mscorlib">
    <value>213</value>
  </data>
  <data name="labelControl10.Text" xml:space="preserve">
    <value>Max Credit</value>
  </data>
  <data name="&gt;&gt;labelControl10.Name" xml:space="preserve">
    <value>labelControl10</value>
  </data>
  <data name="&gt;&gt;labelControl10.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl10.Parent" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="&gt;&gt;labelControl10.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="labelControl24.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl24.Location" type="System.Drawing.Point, System.Drawing">
    <value>311, 32</value>
  </data>
  <data name="labelControl24.Size" type="System.Drawing.Size, System.Drawing">
    <value>61, 13</value>
  </data>
  <data name="labelControl24.TabIndex" type="System.Int32, mscorlib">
    <value>214</value>
  </data>
  <data name="labelControl24.Text" xml:space="preserve">
    <value>Past Balance</value>
  </data>
  <data name="&gt;&gt;labelControl24.Name" xml:space="preserve">
    <value>labelControl24</value>
  </data>
  <data name="&gt;&gt;labelControl24.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl24.Parent" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="&gt;&gt;labelControl24.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="groupControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="lkp_Drawers2.Location" type="System.Drawing.Point, System.Drawing">
    <value>168, 25</value>
  </data>
  <data name="lkp_Drawers2.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_Drawers2.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns" xml:space="preserve">
    <value>AccountName</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns1" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Drawers2.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns7" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="lkp_Drawers2.Properties.Columns8" xml:space="preserve">
    <value>كود الحساب</value>
  </data>
  <data name="lkp_Drawers2.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Drawers2.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Drawers2.Size" type="System.Drawing.Size, System.Drawing">
    <value>158, 20</value>
  </data>
  <data name="lkp_Drawers2.TabIndex" type="System.Int32, mscorlib">
    <value>26</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers2.Name" xml:space="preserve">
    <value>lkp_Drawers2</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers2.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers2.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txt_PayAcc1_Paid.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_PayAcc1_Paid.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 3</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_PayAcc1_Paid.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="txt_PayAcc1_Paid.TabIndex" type="System.Int32, mscorlib">
    <value>25</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc1_Paid.Name" xml:space="preserve">
    <value>txt_PayAcc1_Paid</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc1_Paid.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc1_Paid.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc1_Paid.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="labelControl30.Location" type="System.Drawing.Point, System.Drawing">
    <value>120, 29</value>
  </data>
  <data name="labelControl30.Size" type="System.Drawing.Size, System.Drawing">
    <value>20, 13</value>
  </data>
  <data name="labelControl30.TabIndex" type="System.Int32, mscorlib">
    <value>90</value>
  </data>
  <data name="labelControl30.Text" xml:space="preserve">
    <value>Paid</value>
  </data>
  <data name="&gt;&gt;labelControl30.Name" xml:space="preserve">
    <value>labelControl30</value>
  </data>
  <data name="&gt;&gt;labelControl30.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl30.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;labelControl30.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="labelControl31.Location" type="System.Drawing.Point, System.Drawing">
    <value>120, 7</value>
  </data>
  <data name="labelControl31.Size" type="System.Drawing.Size, System.Drawing">
    <value>20, 13</value>
  </data>
  <data name="labelControl31.TabIndex" type="System.Int32, mscorlib">
    <value>90</value>
  </data>
  <data name="labelControl31.Text" xml:space="preserve">
    <value>Paid</value>
  </data>
  <data name="&gt;&gt;labelControl31.Name" xml:space="preserve">
    <value>labelControl31</value>
  </data>
  <data name="&gt;&gt;labelControl31.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl31.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;labelControl31.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="lkp_Drawers.Location" type="System.Drawing.Point, System.Drawing">
    <value>168, 4</value>
  </data>
  <data name="lkp_Drawers.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_Drawers.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns" xml:space="preserve">
    <value>AccountName</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns1" xml:space="preserve">
    <value>Account Name</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Drawers.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns7" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns8" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="lkp_Drawers.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Drawers.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Drawers.Size" type="System.Drawing.Size, System.Drawing">
    <value>158, 20</value>
  </data>
  <data name="lkp_Drawers.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers.Name" xml:space="preserve">
    <value>lkp_Drawers</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;lkp_Drawers.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="labelControl17.Location" type="System.Drawing.Point, System.Drawing">
    <value>331, 6</value>
  </data>
  <data name="labelControl17.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 13</value>
  </data>
  <data name="labelControl17.TabIndex" type="System.Int32, mscorlib">
    <value>90</value>
  </data>
  <data name="labelControl17.Text" xml:space="preserve">
    <value>Pay Account 1</value>
  </data>
  <data name="&gt;&gt;labelControl17.Name" xml:space="preserve">
    <value>labelControl17</value>
  </data>
  <data name="&gt;&gt;labelControl17.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl17.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;labelControl17.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="txt_PayAcc2_Paid.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_PayAcc2_Paid.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_PayAcc2_Paid.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 25</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_PayAcc2_Paid.Size" type="System.Drawing.Size, System.Drawing">
    <value>101, 20</value>
  </data>
  <data name="txt_PayAcc2_Paid.TabIndex" type="System.Int32, mscorlib">
    <value>27</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc2_Paid.Name" xml:space="preserve">
    <value>txt_PayAcc2_Paid</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc2_Paid.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc2_Paid.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;txt_PayAcc2_Paid.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="labelControl33.Location" type="System.Drawing.Point, System.Drawing">
    <value>331, 27</value>
  </data>
  <data name="labelControl33.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 13</value>
  </data>
  <data name="labelControl33.TabIndex" type="System.Int32, mscorlib">
    <value>101</value>
  </data>
  <data name="labelControl33.Text" xml:space="preserve">
    <value>Pay Account 2</value>
  </data>
  <data name="&gt;&gt;labelControl33.Name" xml:space="preserve">
    <value>labelControl33</value>
  </data>
  <data name="&gt;&gt;labelControl33.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl33.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;labelControl33.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="txt_paid.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_paid.Location" type="System.Drawing.Point, System.Drawing">
    <value>14, 47</value>
  </data>
  <data name="txt_paid.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>247, 245, 241</value>
  </data>
  <data name="txt_paid.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_paid.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="txt_paid.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_paid.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="txt_paid.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_paid.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_paid.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_paid.Size" type="System.Drawing.Size, System.Drawing">
    <value>101, 20</value>
  </data>
  <data name="txt_paid.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="&gt;&gt;txt_paid.Name" xml:space="preserve">
    <value>txt_paid</value>
  </data>
  <data name="&gt;&gt;txt_paid.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_paid.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;txt_paid.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="lbl_Paid.Location" type="System.Drawing.Point, System.Drawing">
    <value>120, 50</value>
  </data>
  <data name="lbl_Paid.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 13</value>
  </data>
  <data name="lbl_Paid.TabIndex" type="System.Int32, mscorlib">
    <value>92</value>
  </data>
  <data name="lbl_Paid.Text" xml:space="preserve">
    <value>total Paid</value>
  </data>
  <data name="&gt;&gt;lbl_Paid.Name" xml:space="preserve">
    <value>lbl_Paid</value>
  </data>
  <data name="&gt;&gt;lbl_Paid.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lbl_Paid.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;lbl_Paid.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="lbl_remains.Location" type="System.Drawing.Point, System.Drawing">
    <value>330, 48</value>
  </data>
  <data name="lbl_remains.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 13</value>
  </data>
  <data name="lbl_remains.TabIndex" type="System.Int32, mscorlib">
    <value>94</value>
  </data>
  <data name="lbl_remains.Text" xml:space="preserve">
    <value>Remains</value>
  </data>
  <data name="&gt;&gt;lbl_remains.Name" xml:space="preserve">
    <value>lbl_remains</value>
  </data>
  <data name="&gt;&gt;lbl_remains.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lbl_remains.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;lbl_remains.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="txt_Remains.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Remains.Location" type="System.Drawing.Point, System.Drawing">
    <value>168, 46</value>
  </data>
  <data name="txt_Remains.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>247, 245, 241</value>
  </data>
  <data name="txt_Remains.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Remains.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="txt_Remains.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Remains.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="txt_Remains.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Remains.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Remains.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Remains.Size" type="System.Drawing.Size, System.Drawing">
    <value>158, 20</value>
  </data>
  <data name="txt_Remains.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="&gt;&gt;txt_Remains.Name" xml:space="preserve">
    <value>txt_Remains</value>
  </data>
  <data name="&gt;&gt;txt_Remains.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_Remains.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;txt_Remains.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="groupControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>265, 637</value>
  </data>
  <data name="groupControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>410, 70</value>
  </data>
  <data name="groupControl1.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="groupControl1.Text" xml:space="preserve">
    <value>Paid</value>
  </data>
  <data name="&gt;&gt;groupControl1.Name" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;groupControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GroupControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;groupControl1.Parent" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="&gt;&gt;groupControl1.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="page_AccInfo.Size" type="System.Drawing.Size, System.Drawing">
    <value>487, 149</value>
  </data>
  <data name="page_AccInfo.Text" xml:space="preserve">
    <value>Account Information</value>
  </data>
  <data name="&gt;&gt;page_AccInfo.Name" xml:space="preserve">
    <value>page_AccInfo</value>
  </data>
  <data name="&gt;&gt;page_AccInfo.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;page_AccInfo.Parent" xml:space="preserve">
    <value>xtraTabControl1</value>
  </data>
  <data name="&gt;&gt;page_AccInfo.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="xtraTabControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>493, 177</value>
  </data>
  <data name="xtraTabControl1.TabIndex" type="System.Int32, mscorlib">
    <value>231</value>
  </data>
  <data name="page_JobOrder.Size" type="System.Drawing.Size, System.Drawing">
    <value>487, 149</value>
  </data>
  <data name="page_JobOrder.Text" xml:space="preserve">
    <value>Job Order</value>
  </data>
  <data name="&gt;&gt;page_JobOrder.Name" xml:space="preserve">
    <value>page_JobOrder</value>
  </data>
  <data name="&gt;&gt;page_JobOrder.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;page_JobOrder.Parent" xml:space="preserve">
    <value>xtraTabControl1</value>
  </data>
  <data name="&gt;&gt;page_JobOrder.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <metadata name="contextMenuStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>337, 17</value>
  </metadata>
  <data name="mi_frm_IC_Item.Size" type="System.Drawing.Size, System.Drawing">
    <value>237, 22</value>
  </data>
  <data name="mi_frm_IC_Item.Text" xml:space="preserve">
    <value>Item Info</value>
  </data>
  <data name="mi_CustLastPrices.ShortcutKeys" type="System.Windows.Forms.Keys, System.Windows.Forms">
    <value>F12</value>
  </data>
  <data name="mi_CustLastPrices.Size" type="System.Drawing.Size, System.Drawing">
    <value>237, 22</value>
  </data>
  <data name="mi_CustLastPrices.Text" xml:space="preserve">
    <value>View Last Customer Prices</value>
  </data>
  <data name="mi_LastPrices.ShortcutKeys" type="System.Windows.Forms.Keys, System.Windows.Forms">
    <value>F11</value>
  </data>
  <data name="mi_LastPrices.Size" type="System.Drawing.Size, System.Drawing">
    <value>237, 22</value>
  </data>
  <data name="mi_LastPrices.Text" xml:space="preserve">
    <value>View Last Prices</value>
  </data>
  <data name="mi_PasteRows.Size" type="System.Drawing.Size, System.Drawing">
    <value>237, 22</value>
  </data>
  <data name="mi_PasteRows.Text" xml:space="preserve">
    <value>Paste Rows</value>
  </data>
  <data name="mi_ExportData.Size" type="System.Drawing.Size, System.Drawing">
    <value>237, 22</value>
  </data>
  <data name="mi_ExportData.Text" xml:space="preserve">
    <value>Export Data</value>
  </data>
  <data name="mi_InvoiceStaticDisc.Size" type="System.Drawing.Size, System.Drawing">
    <value>237, 22</value>
  </data>
  <data name="mi_InvoiceStaticDisc.Text" xml:space="preserve">
    <value>Static Invoice Discount</value>
  </data>
  <data name="mi_InvoiceStaticDimensions.Size" type="System.Drawing.Size, System.Drawing">
    <value>237, 22</value>
  </data>
  <data name="mi_InvoiceStaticDimensions.Text" xml:space="preserve">
    <value>Static Invoice Dimenstions</value>
  </data>
  <data name="mi_ImportExcel.Size" type="System.Drawing.Size, System.Drawing">
    <value>237, 22</value>
  </data>
  <data name="mi_ImportExcel.Text" xml:space="preserve">
    <value>Import from Excel Sheet</value>
  </data>
  <data name="contextMenuStrip1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="contextMenuStrip1.Size" type="System.Drawing.Size, System.Drawing">
    <value>238, 180</value>
  </data>
  <data name="&gt;&gt;contextMenuStrip1.Name" xml:space="preserve">
    <value>contextMenuStrip1</value>
  </data>
  <data name="&gt;&gt;contextMenuStrip1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ContextMenuStrip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="grdLastPrices.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="grdLastPrices.EmbeddedNavigator.AllowHtmlTextInToolTip" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>True</value>
  </data>
  <data name="grdLastPrices.EmbeddedNavigator.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="grdLastPrices.EmbeddedNavigator.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="grdLastPrices.EmbeddedNavigator.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="grdLastPrices.EmbeddedNavigator.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="grdLastPrices.EmbeddedNavigator.TextLocation" type="DevExpress.XtraEditors.NavigatorButtonsTextLocation, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="grdLastPrices.EmbeddedNavigator.ToolTipIconType" type="DevExpress.Utils.ToolTipIconType, DevExpress.Utils.v15.1">
    <value>Application</value>
  </data>
  <data name="grdLastPrices.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt</value>
  </data>
  <data name="grdLastPrices.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="colTotalPurchasePrice.Caption" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="colTotalPurchasePrice.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colTotalPurchasePrice.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="colTotalPurchasePrice.Width" type="System.Int32, mscorlib">
    <value>69</value>
  </data>
  <data name="gridColumn6.Caption" xml:space="preserve">
    <value>Sell P</value>
  </data>
  <data name="gridColumn6.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn6.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn6.Width" type="System.Int32, mscorlib">
    <value>65</value>
  </data>
  <data name="colUOM.Caption" xml:space="preserve">
    <value>Unit</value>
  </data>
  <data name="colUOM.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colUOM.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="colUOM.Width" type="System.Int32, mscorlib">
    <value>68</value>
  </data>
  <data name="colQty.Caption" xml:space="preserve">
    <value>Qty</value>
  </data>
  <data name="colQty.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colQty.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="colQty.Width" type="System.Int32, mscorlib">
    <value>65</value>
  </data>
  <data name="colCustNameAr.Caption" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="colCustNameAr.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colCustNameAr.VisibleIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="colCustNameAr.Width" type="System.Int32, mscorlib">
    <value>205</value>
  </data>
  <data name="colInvoiceDate.Caption" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="colInvoiceDate.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colInvoiceDate.VisibleIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="colInvoiceDate.Width" type="System.Int32, mscorlib">
    <value>68</value>
  </data>
  <data name="colInvoiceCode.Caption" xml:space="preserve">
    <value>Invoice Code</value>
  </data>
  <data name="colInvoiceCode.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colInvoiceCode.VisibleIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="colInvoiceCode.Width" type="System.Int32, mscorlib">
    <value>92</value>
  </data>
  <data name="grdLastPrices.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="grdLastPrices.Size" type="System.Drawing.Size, System.Drawing">
    <value>487, 149</value>
  </data>
  <data name="grdLastPrices.TabIndex" type="System.Int32, mscorlib">
    <value>106</value>
  </data>
  <data name="&gt;&gt;grdLastPrices.Name" xml:space="preserve">
    <value>grdLastPrices</value>
  </data>
  <data name="&gt;&gt;grdLastPrices.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.GridControl, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;grdLastPrices.Parent" xml:space="preserve">
    <value>Page_LastPrices</value>
  </data>
  <data name="&gt;&gt;grdLastPrices.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="Page_LastPrices.Size" type="System.Drawing.Size, System.Drawing">
    <value>487, 149</value>
  </data>
  <data name="Page_LastPrices.Text" xml:space="preserve">
    <value>Last Prices</value>
  </data>
  <data name="&gt;&gt;Page_LastPrices.Name" xml:space="preserve">
    <value>Page_LastPrices</value>
  </data>
  <data name="&gt;&gt;Page_LastPrices.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Page_LastPrices.Parent" xml:space="preserve">
    <value>xtraTabControl1</value>
  </data>
  <data name="&gt;&gt;Page_LastPrices.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lkp_Cars.Location" type="System.Drawing.Point, System.Drawing">
    <value>242, 54</value>
  </data>
  <data name="lkp_Cars.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_Cars.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_Cars.Properties.Columns" xml:space="preserve">
    <value>CarId</value>
  </data>
  <data name="lkp_Cars.Properties.Columns1" xml:space="preserve">
    <value>Name11</value>
  </data>
  <data name="lkp_Cars.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_Cars.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_Cars.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Cars.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_Cars.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkp_Cars.Properties.Columns7" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="lkp_Cars.Properties.Columns8" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="lkp_Cars.Properties.Columns9" xml:space="preserve">
    <value>PlateNo</value>
  </data>
  <data name="lkp_Cars.Properties.Columns10" xml:space="preserve">
    <value>Plate Number</value>
  </data>
  <data name="lkp_Cars.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Cars.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Cars.Size" type="System.Drawing.Size, System.Drawing">
    <value>172, 20</value>
  </data>
  <data name="lkp_Cars.TabIndex" type="System.Int32, mscorlib">
    <value>251</value>
  </data>
  <data name="lkp_Cars.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;lkp_Cars.Name" xml:space="preserve">
    <value>lkp_Cars</value>
  </data>
  <data name="&gt;&gt;lkp_Cars.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_Cars.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="&gt;&gt;lkp_Cars.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txtScaleSerial.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtScaleSerial.Location" type="System.Drawing.Point, System.Drawing">
    <value>242, 8</value>
  </data>
  <data name="txtScaleSerial.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtScaleSerial.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtScaleSerial.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtScaleSerial.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtScaleSerial.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtScaleSerial.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtScaleSerial.Size" type="System.Drawing.Size, System.Drawing">
    <value>172, 20</value>
  </data>
  <data name="txtScaleSerial.TabIndex" type="System.Int32, mscorlib">
    <value>249</value>
  </data>
  <data name="&gt;&gt;txtScaleSerial.Name" xml:space="preserve">
    <value>txtScaleSerial</value>
  </data>
  <data name="&gt;&gt;txtScaleSerial.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtScaleSerial.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="&gt;&gt;txtScaleSerial.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="labelControl26.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl26.Location" type="System.Drawing.Point, System.Drawing">
    <value>420, 11</value>
  </data>
  <data name="labelControl26.Size" type="System.Drawing.Size, System.Drawing">
    <value>26, 13</value>
  </data>
  <data name="labelControl26.TabIndex" type="System.Int32, mscorlib">
    <value>250</value>
  </data>
  <data name="labelControl26.Text" xml:space="preserve">
    <value>Serial</value>
  </data>
  <data name="&gt;&gt;labelControl26.Name" xml:space="preserve">
    <value>labelControl26</value>
  </data>
  <data name="&gt;&gt;labelControl26.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl26.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="&gt;&gt;labelControl26.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="txtDestination.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtDestination.Location" type="System.Drawing.Point, System.Drawing">
    <value>242, 77</value>
  </data>
  <data name="txtDestination.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDestination.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDestination.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDestination.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDestination.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDestination.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDestination.Size" type="System.Drawing.Size, System.Drawing">
    <value>172, 20</value>
  </data>
  <data name="txtDestination.TabIndex" type="System.Int32, mscorlib">
    <value>247</value>
  </data>
  <data name="&gt;&gt;txtDestination.Name" xml:space="preserve">
    <value>txtDestination</value>
  </data>
  <data name="&gt;&gt;txtDestination.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtDestination.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="&gt;&gt;txtDestination.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="lblDestination.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lblDestination.Location" type="System.Drawing.Point, System.Drawing">
    <value>420, 80</value>
  </data>
  <data name="lblDestination.Size" type="System.Drawing.Size, System.Drawing">
    <value>54, 13</value>
  </data>
  <data name="lblDestination.TabIndex" type="System.Int32, mscorlib">
    <value>248</value>
  </data>
  <data name="lblDestination.Text" xml:space="preserve">
    <value>Destination</value>
  </data>
  <data name="&gt;&gt;lblDestination.Name" xml:space="preserve">
    <value>lblDestination</value>
  </data>
  <data name="&gt;&gt;lblDestination.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lblDestination.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="&gt;&gt;lblDestination.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="txtVehicleNumber.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtVehicleNumber.Location" type="System.Drawing.Point, System.Drawing">
    <value>242, 54</value>
  </data>
  <data name="txtVehicleNumber.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtVehicleNumber.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtVehicleNumber.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtVehicleNumber.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtVehicleNumber.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtVehicleNumber.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtVehicleNumber.Size" type="System.Drawing.Size, System.Drawing">
    <value>172, 20</value>
  </data>
  <data name="txtVehicleNumber.TabIndex" type="System.Int32, mscorlib">
    <value>245</value>
  </data>
  <data name="&gt;&gt;txtVehicleNumber.Name" xml:space="preserve">
    <value>txtVehicleNumber</value>
  </data>
  <data name="&gt;&gt;txtVehicleNumber.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtVehicleNumber.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="&gt;&gt;txtVehicleNumber.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="lblVehicleNumber.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lblVehicleNumber.Location" type="System.Drawing.Point, System.Drawing">
    <value>420, 57</value>
  </data>
  <data name="lblVehicleNumber.Size" type="System.Drawing.Size, System.Drawing">
    <value>33, 13</value>
  </data>
  <data name="lblVehicleNumber.TabIndex" type="System.Int32, mscorlib">
    <value>246</value>
  </data>
  <data name="lblVehicleNumber.Text" xml:space="preserve">
    <value>Vehicle</value>
  </data>
  <data name="&gt;&gt;lblVehicleNumber.Name" xml:space="preserve">
    <value>lblVehicleNumber</value>
  </data>
  <data name="&gt;&gt;lblVehicleNumber.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lblVehicleNumber.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="&gt;&gt;lblVehicleNumber.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="txtDriverName.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtDriverName.Location" type="System.Drawing.Point, System.Drawing">
    <value>242, 31</value>
  </data>
  <data name="txtDriverName.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDriverName.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDriverName.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDriverName.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDriverName.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDriverName.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDriverName.Size" type="System.Drawing.Size, System.Drawing">
    <value>172, 20</value>
  </data>
  <data name="txtDriverName.TabIndex" type="System.Int32, mscorlib">
    <value>243</value>
  </data>
  <data name="&gt;&gt;txtDriverName.Name" xml:space="preserve">
    <value>txtDriverName</value>
  </data>
  <data name="&gt;&gt;txtDriverName.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtDriverName.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="&gt;&gt;txtDriverName.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="lblDriverName.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lblDriverName.Location" type="System.Drawing.Point, System.Drawing">
    <value>420, 34</value>
  </data>
  <data name="lblDriverName.Size" type="System.Drawing.Size, System.Drawing">
    <value>59, 13</value>
  </data>
  <data name="lblDriverName.TabIndex" type="System.Int32, mscorlib">
    <value>244</value>
  </data>
  <data name="lblDriverName.Text" xml:space="preserve">
    <value>Driver Name</value>
  </data>
  <data name="&gt;&gt;lblDriverName.Name" xml:space="preserve">
    <value>lblDriverName</value>
  </data>
  <data name="&gt;&gt;lblDriverName.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lblDriverName.Parent" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="&gt;&gt;lblDriverName.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="tabExtraData.Size" type="System.Drawing.Size, System.Drawing">
    <value>487, 149</value>
  </data>
  <data name="tabExtraData.Text" xml:space="preserve">
    <value>Extra Data</value>
  </data>
  <data name="&gt;&gt;tabExtraData.Name" xml:space="preserve">
    <value>tabExtraData</value>
  </data>
  <data name="&gt;&gt;tabExtraData.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;tabExtraData.Parent" xml:space="preserve">
    <value>xtraTabControl1</value>
  </data>
  <data name="&gt;&gt;tabExtraData.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="labelControl54.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl54.Location" type="System.Drawing.Point, System.Drawing">
    <value>188, 76</value>
  </data>
  <data name="labelControl54.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 13</value>
  </data>
  <data name="labelControl54.TabIndex" type="System.Int32, mscorlib">
    <value>224</value>
  </data>
  <data name="labelControl54.Text" xml:space="preserve">
    <value>Delivery</value>
  </data>
  <data name="&gt;&gt;labelControl54.Name" xml:space="preserve">
    <value>labelControl54</value>
  </data>
  <data name="&gt;&gt;labelControl54.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl54.Parent" xml:space="preserve">
    <value>tabpg_CustomerData</value>
  </data>
  <data name="&gt;&gt;labelControl54.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lkpDelivery.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="lkpDelivery.Location" type="System.Drawing.Point, System.Drawing">
    <value>21, 72</value>
  </data>
  <data name="lkpDelivery.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>245, 245, 247</value>
  </data>
  <data name="lkpDelivery.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpDelivery.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpDelivery.Properties.Columns" xml:space="preserve">
    <value>idDelivery</value>
  </data>
  <data name="lkpDelivery.Properties.Columns1" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="lkpDelivery.Properties.Columns2" xml:space="preserve">
    <value>Delivery</value>
  </data>
  <data name="lkpDelivery.Properties.Columns3" xml:space="preserve">
    <value>طريقة التسليم</value>
  </data>
  <data name="lkpDelivery.Properties.Columns4" xml:space="preserve">
    <value>DeliveryF</value>
  </data>
  <data name="lkpDelivery.Properties.Columns5" xml:space="preserve">
    <value>طريقة التسليم  ج</value>
  </data>
  <data name="lkpDelivery.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkpDelivery.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpDelivery.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="lkpDelivery.Size" type="System.Drawing.Size, System.Drawing">
    <value>155, 23</value>
  </data>
  <data name="lkpDelivery.TabIndex" type="System.Int32, mscorlib">
    <value>223</value>
  </data>
  <data name="&gt;&gt;lkpDelivery.Name" xml:space="preserve">
    <value>lkpDelivery</value>
  </data>
  <data name="&gt;&gt;lkpDelivery.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpDelivery.Parent" xml:space="preserve">
    <value>tabpg_CustomerData</value>
  </data>
  <data name="&gt;&gt;lkpDelivery.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="labelControl52.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl52.Location" type="System.Drawing.Point, System.Drawing">
    <value>425, 75</value>
  </data>
  <data name="labelControl52.Size" type="System.Drawing.Size, System.Drawing">
    <value>51, 13</value>
  </data>
  <data name="labelControl52.TabIndex" type="System.Int32, mscorlib">
    <value>123</value>
  </data>
  <data name="labelControl52.Text" xml:space="preserve">
    <value>Sales Rep.</value>
  </data>
  <data name="&gt;&gt;labelControl52.Name" xml:space="preserve">
    <value>labelControl52</value>
  </data>
  <data name="&gt;&gt;labelControl52.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl52.Parent" xml:space="preserve">
    <value>tabpg_CustomerData</value>
  </data>
  <data name="&gt;&gt;labelControl52.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="txt_Sales.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_Sales.Location" type="System.Drawing.Point, System.Drawing">
    <value>263, 72</value>
  </data>
  <data name="txt_Sales.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Sales.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="txt_Sales.Properties.Columns" xml:space="preserve">
    <value>EmpName</value>
  </data>
  <data name="txt_Sales.Properties.Columns1" xml:space="preserve">
    <value>اسم الموظف</value>
  </data>
  <data name="txt_Sales.Properties.Columns2" xml:space="preserve">
    <value>EmpCode</value>
  </data>
  <data name="txt_Sales.Properties.Columns3" xml:space="preserve">
    <value>الكود</value>
  </data>
  <data name="txt_Sales.Properties.Columns4" xml:space="preserve">
    <value>EmpId</value>
  </data>
  <data name="txt_Sales.Properties.Columns5" xml:space="preserve">
    <value>EmpId</value>
  </data>
  <data name="txt_Sales.Properties.Columns6" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="txt_Sales.Properties.Columns7" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="txt_Sales.Properties.Columns8" xml:space="preserve">
    <value />
  </data>
  <data name="txt_Sales.Properties.Columns9" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Sales.Properties.Columns10" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_Sales.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="txt_Sales.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Sales.Size" type="System.Drawing.Size, System.Drawing">
    <value>155, 20</value>
  </data>
  <data name="txt_Sales.TabIndex" type="System.Int32, mscorlib">
    <value>124</value>
  </data>
  <data name="&gt;&gt;txt_Sales.Name" xml:space="preserve">
    <value>txt_Sales</value>
  </data>
  <data name="&gt;&gt;txt_Sales.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_Sales.Parent" xml:space="preserve">
    <value>tabpg_CustomerData</value>
  </data>
  <data name="&gt;&gt;txt_Sales.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="txt_Address.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_Address.Location" type="System.Drawing.Point, System.Drawing">
    <value>21, 46</value>
  </data>
  <data name="txt_Address.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Address.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Address.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Address.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Address.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Address.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Address.Size" type="System.Drawing.Size, System.Drawing">
    <value>397, 20</value>
  </data>
  <data name="txt_Address.TabIndex" type="System.Int32, mscorlib">
    <value>122</value>
  </data>
  <data name="&gt;&gt;txt_Address.Name" xml:space="preserve">
    <value>txt_Address</value>
  </data>
  <data name="&gt;&gt;txt_Address.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_Address.Parent" xml:space="preserve">
    <value>tabpg_CustomerData</value>
  </data>
  <data name="&gt;&gt;txt_Address.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="labelControl51.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl51.Location" type="System.Drawing.Point, System.Drawing">
    <value>425, 49</value>
  </data>
  <data name="labelControl51.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 13</value>
  </data>
  <data name="labelControl51.TabIndex" type="System.Int32, mscorlib">
    <value>121</value>
  </data>
  <data name="labelControl51.Text" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="&gt;&gt;labelControl51.Name" xml:space="preserve">
    <value>labelControl51</value>
  </data>
  <data name="&gt;&gt;labelControl51.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl51.Parent" xml:space="preserve">
    <value>tabpg_CustomerData</value>
  </data>
  <data name="&gt;&gt;labelControl51.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="txt_Phone.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_Phone.Location" type="System.Drawing.Point, System.Drawing">
    <value>21, 20</value>
  </data>
  <data name="txt_Phone.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Phone.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Phone.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Phone.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Phone.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Phone.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Phone.Size" type="System.Drawing.Size, System.Drawing">
    <value>155, 20</value>
  </data>
  <data name="txt_Phone.TabIndex" type="System.Int32, mscorlib">
    <value>120</value>
  </data>
  <data name="&gt;&gt;txt_Phone.Name" xml:space="preserve">
    <value>txt_Phone</value>
  </data>
  <data name="&gt;&gt;txt_Phone.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_Phone.Parent" xml:space="preserve">
    <value>tabpg_CustomerData</value>
  </data>
  <data name="&gt;&gt;txt_Phone.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="labelControl48.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl48.Location" type="System.Drawing.Point, System.Drawing">
    <value>183, 23</value>
  </data>
  <data name="labelControl48.Size" type="System.Drawing.Size, System.Drawing">
    <value>30, 13</value>
  </data>
  <data name="labelControl48.TabIndex" type="System.Int32, mscorlib">
    <value>119</value>
  </data>
  <data name="labelControl48.Text" xml:space="preserve">
    <value>Phone</value>
  </data>
  <data name="&gt;&gt;labelControl48.Name" xml:space="preserve">
    <value>labelControl48</value>
  </data>
  <data name="&gt;&gt;labelControl48.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl48.Parent" xml:space="preserve">
    <value>tabpg_CustomerData</value>
  </data>
  <data name="&gt;&gt;labelControl48.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="txt_Mobile.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_Mobile.Location" type="System.Drawing.Point, System.Drawing">
    <value>263, 20</value>
  </data>
  <data name="txt_Mobile.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Mobile.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Mobile.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Mobile.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Mobile.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Mobile.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Mobile.Size" type="System.Drawing.Size, System.Drawing">
    <value>155, 20</value>
  </data>
  <data name="txt_Mobile.TabIndex" type="System.Int32, mscorlib">
    <value>118</value>
  </data>
  <data name="&gt;&gt;txt_Mobile.Name" xml:space="preserve">
    <value>txt_Mobile</value>
  </data>
  <data name="&gt;&gt;txt_Mobile.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_Mobile.Parent" xml:space="preserve">
    <value>tabpg_CustomerData</value>
  </data>
  <data name="&gt;&gt;txt_Mobile.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="labelControl47.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl47.Location" type="System.Drawing.Point, System.Drawing">
    <value>425, 23</value>
  </data>
  <data name="labelControl47.Size" type="System.Drawing.Size, System.Drawing">
    <value>30, 13</value>
  </data>
  <data name="labelControl47.TabIndex" type="System.Int32, mscorlib">
    <value>117</value>
  </data>
  <data name="labelControl47.Text" xml:space="preserve">
    <value>Mobile</value>
  </data>
  <data name="&gt;&gt;labelControl47.Name" xml:space="preserve">
    <value>labelControl47</value>
  </data>
  <data name="&gt;&gt;labelControl47.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl47.Parent" xml:space="preserve">
    <value>tabpg_CustomerData</value>
  </data>
  <data name="&gt;&gt;labelControl47.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="tabpg_CustomerData.Size" type="System.Drawing.Size, System.Drawing">
    <value>487, 149</value>
  </data>
  <data name="tabpg_CustomerData.Text" xml:space="preserve">
    <value>Customer Data</value>
  </data>
  <data name="&gt;&gt;tabpg_CustomerData.Name" xml:space="preserve">
    <value>tabpg_CustomerData</value>
  </data>
  <data name="&gt;&gt;tabpg_CustomerData.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;tabpg_CustomerData.Parent" xml:space="preserve">
    <value>xtraTabControl1</value>
  </data>
  <data name="&gt;&gt;tabpg_CustomerData.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;xtraTabControl1.Name" xml:space="preserve">
    <value>xtraTabControl1</value>
  </data>
  <data name="&gt;&gt;xtraTabControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;xtraTabControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;xtraTabControl1.ZOrder" xml:space="preserve">
    <value>43</value>
  </data>
  <data name="labelControl16.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl16.Location" type="System.Drawing.Point, System.Drawing">
    <value>186, 577</value>
  </data>
  <data name="labelControl16.Size" type="System.Drawing.Size, System.Drawing">
    <value>55, 13</value>
  </data>
  <data name="labelControl16.TabIndex" type="System.Int32, mscorlib">
    <value>191</value>
  </data>
  <data name="labelControl16.Text" xml:space="preserve">
    <value>Deduct Tax</value>
  </data>
  <data name="&gt;&gt;labelControl16.Name" xml:space="preserve">
    <value>labelControl16</value>
  </data>
  <data name="&gt;&gt;labelControl16.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl16.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl16.ZOrder" xml:space="preserve">
    <value>44</value>
  </data>
  <data name="labelControl15.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl15.Location" type="System.Drawing.Point, System.Drawing">
    <value>103, 575</value>
  </data>
  <data name="labelControl15.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="labelControl15.TabIndex" type="System.Int32, mscorlib">
    <value>190</value>
  </data>
  <data name="labelControl15.Text" xml:space="preserve">
    <value>V</value>
  </data>
  <data name="&gt;&gt;labelControl15.Name" xml:space="preserve">
    <value>labelControl15</value>
  </data>
  <data name="&gt;&gt;labelControl15.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl15.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl15.ZOrder" xml:space="preserve">
    <value>45</value>
  </data>
  <data name="labelControl23.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl23.Location" type="System.Drawing.Point, System.Drawing">
    <value>103, 598</value>
  </data>
  <data name="labelControl23.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="labelControl23.TabIndex" type="System.Int32, mscorlib">
    <value>190</value>
  </data>
  <data name="labelControl23.Text" xml:space="preserve">
    <value>V</value>
  </data>
  <data name="&gt;&gt;labelControl23.Name" xml:space="preserve">
    <value>labelControl23</value>
  </data>
  <data name="&gt;&gt;labelControl23.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl23.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl23.ZOrder" xml:space="preserve">
    <value>46</value>
  </data>
  <data name="labelControl25.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl25.Location" type="System.Drawing.Point, System.Drawing">
    <value>186, 600</value>
  </data>
  <data name="labelControl25.Size" type="System.Drawing.Size, System.Drawing">
    <value>40, 13</value>
  </data>
  <data name="labelControl25.TabIndex" type="System.Int32, mscorlib">
    <value>191</value>
  </data>
  <data name="labelControl25.Text" xml:space="preserve">
    <value>Add Tax</value>
  </data>
  <data name="&gt;&gt;labelControl25.Name" xml:space="preserve">
    <value>labelControl25</value>
  </data>
  <data name="&gt;&gt;labelControl25.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl25.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl25.ZOrder" xml:space="preserve">
    <value>47</value>
  </data>
  <data name="labelControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>186, 552</value>
  </data>
  <data name="labelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>31, 13</value>
  </data>
  <data name="labelControl1.TabIndex" type="System.Int32, mscorlib">
    <value>191</value>
  </data>
  <data name="labelControl1.Text" xml:space="preserve">
    <value>S. Tax</value>
  </data>
  <data name="&gt;&gt;labelControl1.Name" xml:space="preserve">
    <value>labelControl1</value>
  </data>
  <data name="&gt;&gt;labelControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl1.ZOrder" xml:space="preserve">
    <value>48</value>
  </data>
  <data name="labelControl20.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl20.Location" type="System.Drawing.Point, System.Drawing">
    <value>174, 552</value>
  </data>
  <data name="labelControl20.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="labelControl20.TabIndex" type="System.Int32, mscorlib">
    <value>190</value>
  </data>
  <data name="labelControl20.Text" xml:space="preserve">
    <value>V</value>
  </data>
  <data name="&gt;&gt;labelControl20.Name" xml:space="preserve">
    <value>labelControl20</value>
  </data>
  <data name="&gt;&gt;labelControl20.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl20.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl20.ZOrder" xml:space="preserve">
    <value>49</value>
  </data>
  <data name="labelControl7.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl7.Location" type="System.Drawing.Point, System.Drawing">
    <value>186, 507</value>
  </data>
  <data name="labelControl7.Size" type="System.Drawing.Size, System.Drawing">
    <value>19, 13</value>
  </data>
  <data name="labelControl7.TabIndex" type="System.Int32, mscorlib">
    <value>57</value>
  </data>
  <data name="labelControl7.Text" xml:space="preserve">
    <value>Disc</value>
  </data>
  <data name="&gt;&gt;labelControl7.Name" xml:space="preserve">
    <value>labelControl7</value>
  </data>
  <data name="&gt;&gt;labelControl7.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl7.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl7.ZOrder" xml:space="preserve">
    <value>50</value>
  </data>
  <data name="panelControl2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="grdPrInvoice.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.AllowHtmlTextInToolTip" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>True</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.TextLocation" type="DevExpress.XtraEditors.NavigatorButtonsTextLocation, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.ToolTipIconType" type="DevExpress.Utils.ToolTipIconType, DevExpress.Utils.v15.1">
    <value>Application</value>
  </data>
  <data name="grdPrInvoice.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="colETaxValue.Caption" xml:space="preserve">
    <value>Tax Value</value>
  </data>
  <data name="repSpin.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repSpin.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repSpin.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repSpin.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="repSpin.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repSpin.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="colETaxValue.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="colETaxValue.Summary1" xml:space="preserve">
    <value>EtaxValue</value>
  </data>
  <data name="colETaxValue.Summary2" xml:space="preserve">
    <value>{0:0.##}</value>
  </data>
  <data name="colETaxValue.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colETaxValue.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="colETaxRatio.Caption" xml:space="preserve">
    <value>Tax Ratio</value>
  </data>
  <data name="colETaxRatio.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colETaxRatio.VisibleIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="Col_ETaxType.Caption" xml:space="preserve">
    <value>TaxType</value>
  </data>
  <data name="repTaxTypes.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repTaxTypes.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="repTaxTypes.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="ColDescriptionAr.Caption" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="ColDescriptionAr.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="ColDescriptionAr.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="ColCode.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="ColCode.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="ColCode.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="colE_TaxableTypeId.Caption" xml:space="preserve">
    <value>TaxableTypeId</value>
  </data>
  <data name="Col_ETaxType.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="Col_ETaxType.VisibleIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="colBonusDiscount.Caption" xml:space="preserve">
    <value>Bonus discount</value>
  </data>
  <data name="colBonusDiscount.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="colBonusDiscount.Summary1" xml:space="preserve">
    <value>bonusDiscount</value>
  </data>
  <data name="colBonusDiscount.Summary2" xml:space="preserve">
    <value>{0:0.##}</value>
  </data>
  <data name="colBonusDiscount.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colBonusDiscount.VisibleIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="repCompany.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="repCompany.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="ColCompanyNameAr.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="ColCompanyNameAr.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="Col_CompanyCode.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="Col_CompanyCode.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="repCategory.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="repCategory.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="ColCatNumber.Caption" xml:space="preserve">
    <value>Category Number</value>
  </data>
  <data name="ColCatNumber.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="ColCatNumber.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="ColCategoryNameAr.Caption" xml:space="preserve">
    <value>Category Name</value>
  </data>
  <data name="ColCategoryNameAr.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="ColCategoryNameAr.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_QC.Caption" xml:space="preserve">
    <value>QC</value>
  </data>
  <data name="col_ActualPiecesCount.Caption" xml:space="preserve">
    <value>Actual Pieces Count</value>
  </data>
  <data name="gridColumn29.Caption" xml:space="preserve">
    <value>LargeUOMFactor</value>
  </data>
  <data name="gridColumn29.Width" type="System.Int32, mscorlib">
    <value>90</value>
  </data>
  <data name="gridColumn28.Caption" xml:space="preserve">
    <value>MediumUOMFactor</value>
  </data>
  <data name="gridColumn28.Width" type="System.Int32, mscorlib">
    <value>102</value>
  </data>
  <data name="col_TotalSellPrice.Caption" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="col_TotalSellPrice.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="col_TotalSellPrice.Summary1" xml:space="preserve">
    <value>TotalSellPrice</value>
  </data>
  <data name="col_TotalSellPrice.Summary2" xml:space="preserve">
    <value>{0:0.####}</value>
  </data>
  <data name="col_TotalSellPrice.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_TotalSellPrice.VisibleIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="col_TotalSellPrice.Width" type="System.Int32, mscorlib">
    <value>68</value>
  </data>
  <data name="col_CurrentQty.Caption" xml:space="preserve">
    <value>Current Qty</value>
  </data>
  <data name="col_CurrentQty.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_CurrentQty.VisibleIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="col_CurrentQty.Width" type="System.Int32, mscorlib">
    <value>97</value>
  </data>
  <data name="col_CommercialDiscountValue.Caption" xml:space="preserve">
    <value>Disc V</value>
  </data>
  <data name="col_CommercialDiscountValue.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="col_CommercialDiscountValue.Summary1" xml:space="preserve">
    <value>DiscountValue</value>
  </data>
  <data name="col_CommercialDiscountValue.Summary2" xml:space="preserve">
    <value>{0:0.##}</value>
  </data>
  <data name="col_CommercialDiscountValue.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_CommercialDiscountValue.VisibleIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="col_CommercialDiscountValue.Width" type="System.Int32, mscorlib">
    <value>71</value>
  </data>
  <data name="gridColumn1.Caption" xml:space="preserve">
    <value>Disc R</value>
  </data>
  <data name="repDiscountRatio.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repDiscountRatio.Mask.EditMask" xml:space="preserve">
    <value>p2</value>
  </data>
  <data name="repDiscountRatio.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repDiscountRatio.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="repDiscountRatio.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repDiscountRatio.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repDiscountRatio.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn1.Width" type="System.Int32, mscorlib">
    <value>71</value>
  </data>
  <data name="col_SellPrice.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_SellPrice.VisibleIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="col_SellPrice.Width" type="System.Int32, mscorlib">
    <value>76</value>
  </data>
  <data name="col_AudiencePrice.Caption" xml:space="preserve">
    <value>Audience Price</value>
  </data>
  <data name="col_AudiencePrice.Width" type="System.Int32, mscorlib">
    <value>61</value>
  </data>
  <data name="colPurchasePrice.Caption" xml:space="preserve">
    <value>Purchase Price</value>
  </data>
  <data name="colPurchasePrice.Width" type="System.Int32, mscorlib">
    <value>82</value>
  </data>
  <data name="gridColumn7.Caption" xml:space="preserve">
    <value>Qty</value>
  </data>
  <data name="gridColumn7.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="gridColumn7.Summary1" xml:space="preserve">
    <value>Qty</value>
  </data>
  <data name="gridColumn7.Summary2" xml:space="preserve">
    <value>{0:0.##}</value>
  </data>
  <data name="gridColumn7.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn7.VisibleIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="gridColumn7.Width" type="System.Int32, mscorlib">
    <value>83</value>
  </data>
  <data name="gridColumn8.Caption" xml:space="preserve">
    <value>Unit Of Measure</value>
  </data>
  <data name="repUOM.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="repUOM.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn9.Caption" xml:space="preserve">
    <value>Index</value>
  </data>
  <data name="gridColumn16.Caption" xml:space="preserve">
    <value>Factor</value>
  </data>
  <data name="gridColumn16.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn16.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn17.Caption" xml:space="preserve">
    <value>Unit</value>
  </data>
  <data name="gridColumn17.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn17.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn3.Caption" xml:space="preserve">
    <value>gridColumn3</value>
  </data>
  <data name="gridColumn8.Width" type="System.Int32, mscorlib">
    <value>86</value>
  </data>
  <data name="col_ItemNameF.Caption" xml:space="preserve">
    <value>اسم الصنف</value>
  </data>
  <data name="col_ItemNameF.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_ItemNameF.VisibleIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="gridColumn10.Caption" xml:space="preserve">
    <value>Item Name</value>
  </data>
  <data name="repItems.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="repItems.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn12.Caption" xml:space="preserve">
    <value>Item F Name</value>
  </data>
  <data name="gridColumn12.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn12.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="gridColumn12.Width" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="gridColumn13.Caption" xml:space="preserve">
    <value>Item Name</value>
  </data>
  <data name="gridColumn13.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn13.VisibleIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="gridColumn13.Width" type="System.Int32, mscorlib">
    <value>114</value>
  </data>
  <data name="gridColumn14.Caption" xml:space="preserve">
    <value>Code 1</value>
  </data>
  <data name="gridColumn14.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn14.VisibleIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="gridColumn14.Width" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="gridColumn15.Caption" xml:space="preserve">
    <value>itemId</value>
  </data>
  <data name="gridColumn5.Caption" xml:space="preserve">
    <value>Code 2</value>
  </data>
  <data name="gridColumn5.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn5.VisibleIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="gridColumn5.Width" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="gridColumn4.Caption" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="gridColumn4.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn4.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn4.Width" type="System.Int32, mscorlib">
    <value>47</value>
  </data>
  <data name="gridColumn25.Caption" xml:space="preserve">
    <value>Sell Price</value>
  </data>
  <data name="gridColumn25.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn25.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn25.Width" type="System.Int32, mscorlib">
    <value>79</value>
  </data>
  <data name="col_SellDiscountRatio.Caption" xml:space="preserve">
    <value>SellDiscountRatio</value>
  </data>
  <data name="col_SellDiscountRatio.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_SellDiscountRatio.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_SellDiscountRatio.Width" type="System.Int32, mscorlib">
    <value>96</value>
  </data>
  <data name="col_CompanyNameAr.Caption" xml:space="preserve">
    <value>Company</value>
  </data>
  <data name="col_CategoryNameAr.Caption" xml:space="preserve">
    <value>Category</value>
  </data>
  <data name="gridColumn10.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn10.VisibleIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="gridColumn10.Width" type="System.Int32, mscorlib">
    <value>200</value>
  </data>
  <data name="grdcol_branch.Caption" xml:space="preserve">
    <value>Store</value>
  </data>
  <data name="lkp_storee.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_storee.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="grdcol_branch.Width" type="System.Int32, mscorlib">
    <value>61</value>
  </data>
  <data name="gridColumn11.Caption" xml:space="preserve">
    <value>Code 2</value>
  </data>
  <data name="gridColumn11.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn11.VisibleIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="gridColumn11.Width" type="System.Int32, mscorlib">
    <value>64</value>
  </data>
  <data name="gridColumn31.Caption" xml:space="preserve">
    <value>Code 1</value>
  </data>
  <data name="gridColumn31.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn31.VisibleIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="gridColumn31.Width" type="System.Int32, mscorlib">
    <value>138</value>
  </data>
  <data name="gridColumn41.Caption" xml:space="preserve">
    <value>gridColumn41</value>
  </data>
  <data name="col_Expire.Caption" xml:space="preserve">
    <value>Expire Date</value>
  </data>
  <data name="rep_expireDate.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_expireDate.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="rep_expireDate.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn18.Caption" xml:space="preserve">
    <value>Expire Date</value>
  </data>
  <data name="gridColumn18.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn18.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn23.Caption" xml:space="preserve">
    <value>Batch</value>
  </data>
  <data name="gridColumn23.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn23.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn24.Caption" xml:space="preserve">
    <value>Qty</value>
  </data>
  <data name="gridColumn24.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn24.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_Expire.Width" type="System.Int32, mscorlib">
    <value>53</value>
  </data>
  <data name="col_Batch.Caption" xml:space="preserve">
    <value>Batch </value>
  </data>
  <data name="rep_Batch.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_Batch.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="rep_Batch.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn33.Caption" xml:space="preserve">
    <value>Batch</value>
  </data>
  <data name="gridColumn33.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn33.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn34.Caption" xml:space="preserve">
    <value>Qty</value>
  </data>
  <data name="gridColumn34.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn34.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_Batch.Width" type="System.Int32, mscorlib">
    <value>53</value>
  </data>
  <data name="col_Length.Caption" xml:space="preserve">
    <value>Length</value>
  </data>
  <data name="col_Length.Width" type="System.Int32, mscorlib">
    <value>53</value>
  </data>
  <data name="col_Width.Caption" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="col_Width.Width" type="System.Int32, mscorlib">
    <value>53</value>
  </data>
  <data name="col_Height.Caption" xml:space="preserve">
    <value>Height</value>
  </data>
  <data name="col_Height.Width" type="System.Int32, mscorlib">
    <value>53</value>
  </data>
  <data name="col_TotalQty.Caption" xml:space="preserve">
    <value>Total Qty</value>
  </data>
  <data name="col_TotalQty.Width" type="System.Int32, mscorlib">
    <value>53</value>
  </data>
  <data name="col_PiecesCount.Caption" xml:space="preserve">
    <value>Pieces Count</value>
  </data>
  <data name="repspin_PiecesCount.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repspin_PiecesCount.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repspin_PiecesCount.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="repspin_PiecesCount.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repspin_PiecesCount.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_PiecesCount.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="col_PiecesCount.Summary1" xml:space="preserve">
    <value>PiecesCount</value>
  </data>
  <data name="col_PiecesCount.Summary2" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_PiecesCount.Width" type="System.Int32, mscorlib">
    <value>60</value>
  </data>
  <data name="col_ItemDescription.Caption" xml:space="preserve">
    <value>Item Description</value>
  </data>
  <data name="col_ItemDescription.Width" type="System.Int32, mscorlib">
    <value>87</value>
  </data>
  <data name="col_ItemDescriptionEn.Caption" xml:space="preserve">
    <value>Item Description En</value>
  </data>
  <data name="col_SalesTax.Caption" xml:space="preserve">
    <value>Sales Tax</value>
  </data>
  <data name="col_SalesTax.Width" type="System.Int32, mscorlib">
    <value>94</value>
  </data>
  <data name="col_DiscountRatio2.Caption" xml:space="preserve">
    <value>Disc R2</value>
  </data>
  <data name="col_DiscountRatio2.Width" type="System.Int32, mscorlib">
    <value>71</value>
  </data>
  <data name="col_DiscountRatio3.Caption" xml:space="preserve">
    <value>Disc R3</value>
  </data>
  <data name="col_Serial.Caption" xml:space="preserve">
    <value>Serial</value>
  </data>
  <data name="col_Serial2.Caption" xml:space="preserve">
    <value>Serial2</value>
  </data>
  <data name="repManufactureDate.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repManufactureDate.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repManufactureDate.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repManufactureDate.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="repManufactureDate.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repManufactureDate.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repManufactureDate.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_CusTax.Caption" xml:space="preserve">
    <value>Custom V</value>
  </data>
  <data name="col_Location.Caption" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="col_Location.Width" type="System.Int32, mscorlib">
    <value>55</value>
  </data>
  <data name="col_Libra.Caption" xml:space="preserve">
    <value>Libra Quantity</value>
  </data>
  <data name="rep_Libra.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_Libra.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="rep_Libra.Mask.EditMask" xml:space="preserve">
    <value>n3</value>
  </data>
  <data name="rep_Libra.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_Libra.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="rep_Libra.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_Libra.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_Libra.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_Libra.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="col_Libra.Summary1" xml:space="preserve">
    <value>LibraQty</value>
  </data>
  <data name="col_Libra.Summary2" xml:space="preserve">
    <value>{0:0.##}</value>
  </data>
  <data name="col_Libra.Width" type="System.Int32, mscorlib">
    <value>64</value>
  </data>
  <data name="gridColumn36.Caption" xml:space="preserve">
    <value>IsOffer</value>
  </data>
  <data name="gridColumn37.Caption" xml:space="preserve">
    <value>VariableWeight</value>
  </data>
  <data name="gridColumn38.Caption" xml:space="preserve">
    <value>PricingWithSmall</value>
  </data>
  <data name="col_kg_Weight_libra.Caption" xml:space="preserve">
    <value>Weight (kg)</value>
  </data>
  <data name="col_kg_Weight_libra.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="col_kg_Weight_libra.Summary1" xml:space="preserve">
    <value>kg_Weight_libra</value>
  </data>
  <data name="col_kg_Weight_libra.Summary2" xml:space="preserve">
    <value>{0:0.##}</value>
  </data>
  <data name="col_kg_Weight_libra.Width" type="System.Int32, mscorlib">
    <value>61</value>
  </data>
  <data name="ol_Index.Caption" xml:space="preserve">
    <value>#</value>
  </data>
  <data name="ol_Index.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="ol_Index.VisibleIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="ol_Index.Width" type="System.Int32, mscorlib">
    <value>89</value>
  </data>
  <data name="col_Pack.Caption" xml:space="preserve">
    <value>Pack</value>
  </data>
  <data name="col_Pack.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="col_Pack.Width" type="System.Int32, mscorlib">
    <value>44</value>
  </data>
  <data name="btn_AddTaxes.Caption" xml:space="preserve">
    <value>Add Taxes</value>
  </data>
  <data name="rep_btnAddTaxes.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_btnAddTaxes.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Plus</value>
  </data>
  <data name="btn_AddTaxes.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="btn_AddTaxes.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="TotalTaxes.Caption" xml:space="preserve">
    <value>Total Taxes</value>
  </data>
  <data name="TotalTaxes.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="TotalTaxes.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="salePriceWithTaxTable.Caption" xml:space="preserve">
    <value>Sale Price With Tax Table</value>
  </data>
  <data name="salePriceWithTaxTable.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="salePriceWithTaxTable.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="addTaxValue.Caption" xml:space="preserve">
    <value>Add Tax Value</value>
  </data>
  <data name="addTaxValue.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="addTaxValue.VisibleIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="tableTaxValue.Caption" xml:space="preserve">
    <value>Table Tax Value</value>
  </data>
  <data name="tableTaxValue.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="tableTaxValue.VisibleIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="lkp_store.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_store.NullText" xml:space="preserve">
    <value>اختر الفرع</value>
  </data>
  <data name="repositoryItemSpinEdit1.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repositoryItemSpinEdit1.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="repositoryItemSpinEdit1.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repositoryItemSpinEdit1.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repositoryItemSpinEdit1.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="repositoryItemSpinEdit1.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repositoryItemSpinEdit1.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_Location.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_Location.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="rep_Location.NullText" xml:space="preserve">
    <value>اختر الفرع</value>
  </data>
  <data name="grdPrInvoice.Size" type="System.Drawing.Size, System.Drawing">
    <value>1144, 162</value>
  </data>
  <data name="grdPrInvoice.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="&gt;&gt;grdPrInvoice.Name" xml:space="preserve">
    <value>grdPrInvoice</value>
  </data>
  <data name="&gt;&gt;grdPrInvoice.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.GridControl, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;grdPrInvoice.Parent" xml:space="preserve">
    <value>panelControl2</value>
  </data>
  <data name="&gt;&gt;grdPrInvoice.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="panelControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>21, 183</value>
  </data>
  <data name="panelControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>1148, 166</value>
  </data>
  <data name="panelControl2.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="&gt;&gt;panelControl2.Name" xml:space="preserve">
    <value>panelControl2</value>
  </data>
  <data name="&gt;&gt;panelControl2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.PanelControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;panelControl2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;panelControl2.ZOrder" xml:space="preserve">
    <value>51</value>
  </data>
  <data name="labelControl14.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl14.Location" type="System.Drawing.Point, System.Drawing">
    <value>174, 577</value>
  </data>
  <data name="labelControl14.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="labelControl14.TabIndex" type="System.Int32, mscorlib">
    <value>192</value>
  </data>
  <data name="labelControl14.Text" xml:space="preserve">
    <value>R</value>
  </data>
  <data name="&gt;&gt;labelControl14.Name" xml:space="preserve">
    <value>labelControl14</value>
  </data>
  <data name="&gt;&gt;labelControl14.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl14.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl14.ZOrder" xml:space="preserve">
    <value>52</value>
  </data>
  <data name="labelControl22.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl22.Location" type="System.Drawing.Point, System.Drawing">
    <value>174, 600</value>
  </data>
  <data name="labelControl22.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="labelControl22.TabIndex" type="System.Int32, mscorlib">
    <value>192</value>
  </data>
  <data name="labelControl22.Text" xml:space="preserve">
    <value>R</value>
  </data>
  <data name="&gt;&gt;labelControl22.Name" xml:space="preserve">
    <value>labelControl22</value>
  </data>
  <data name="&gt;&gt;labelControl22.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl22.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl22.ZOrder" xml:space="preserve">
    <value>53</value>
  </data>
  <data name="labelControl6.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl6.Location" type="System.Drawing.Point, System.Drawing">
    <value>103, 507</value>
  </data>
  <data name="labelControl6.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="labelControl6.TabIndex" type="System.Int32, mscorlib">
    <value>55</value>
  </data>
  <data name="labelControl6.Text" xml:space="preserve">
    <value>V</value>
  </data>
  <data name="&gt;&gt;labelControl6.Name" xml:space="preserve">
    <value>labelControl6</value>
  </data>
  <data name="&gt;&gt;labelControl6.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl6.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl6.ZOrder" xml:space="preserve">
    <value>54</value>
  </data>
  <data name="labelControl2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>118, 576</value>
  </data>
  <data name="labelControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>11, 13</value>
  </data>
  <data name="labelControl2.TabIndex" type="System.Int32, mscorlib">
    <value>193</value>
  </data>
  <data name="labelControl2.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="&gt;&gt;labelControl2.Name" xml:space="preserve">
    <value>labelControl2</value>
  </data>
  <data name="&gt;&gt;labelControl2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl2.ZOrder" xml:space="preserve">
    <value>55</value>
  </data>
  <data name="labelControl21.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl21.Location" type="System.Drawing.Point, System.Drawing">
    <value>118, 599</value>
  </data>
  <data name="labelControl21.Size" type="System.Drawing.Size, System.Drawing">
    <value>11, 13</value>
  </data>
  <data name="labelControl21.TabIndex" type="System.Int32, mscorlib">
    <value>193</value>
  </data>
  <data name="labelControl21.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="&gt;&gt;labelControl21.Name" xml:space="preserve">
    <value>labelControl21</value>
  </data>
  <data name="&gt;&gt;labelControl21.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl21.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl21.ZOrder" xml:space="preserve">
    <value>56</value>
  </data>
  <data name="labelControl11.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl11.Location" type="System.Drawing.Point, System.Drawing">
    <value>173, 507</value>
  </data>
  <data name="labelControl11.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="labelControl11.TabIndex" type="System.Int32, mscorlib">
    <value>126</value>
  </data>
  <data name="labelControl11.Text" xml:space="preserve">
    <value>R</value>
  </data>
  <data name="&gt;&gt;labelControl11.Name" xml:space="preserve">
    <value>labelControl11</value>
  </data>
  <data name="&gt;&gt;labelControl11.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl11.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl11.ZOrder" xml:space="preserve">
    <value>57</value>
  </data>
  <data name="labelControl12.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl12.Location" type="System.Drawing.Point, System.Drawing">
    <value>173, 671</value>
  </data>
  <data name="labelControl12.Size" type="System.Drawing.Size, System.Drawing">
    <value>7, 13</value>
  </data>
  <data name="labelControl12.TabIndex" type="System.Int32, mscorlib">
    <value>126</value>
  </data>
  <data name="labelControl12.Text" xml:space="preserve">
    <value>R</value>
  </data>
  <data name="&gt;&gt;labelControl12.Name" xml:space="preserve">
    <value>labelControl12</value>
  </data>
  <data name="&gt;&gt;labelControl12.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl12.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl12.ZOrder" xml:space="preserve">
    <value>58</value>
  </data>
  <data name="labelControl19.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl19.Location" type="System.Drawing.Point, System.Drawing">
    <value>118, 506</value>
  </data>
  <data name="labelControl19.Size" type="System.Drawing.Size, System.Drawing">
    <value>11, 13</value>
  </data>
  <data name="labelControl19.TabIndex" type="System.Int32, mscorlib">
    <value>139</value>
  </data>
  <data name="labelControl19.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="&gt;&gt;labelControl19.Name" xml:space="preserve">
    <value>labelControl19</value>
  </data>
  <data name="&gt;&gt;labelControl19.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl19.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl19.ZOrder" xml:space="preserve">
    <value>59</value>
  </data>
  <data name="txt_CommercialDiscounts.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_CommercialDiscounts.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 379</value>
  </data>
  <data name="txt_CommercialDiscounts.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_CommercialDiscounts.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_CommercialDiscounts.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_CommercialDiscounts.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_CommercialDiscounts.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_CommercialDiscounts.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_CommercialDiscounts.Size" type="System.Drawing.Size, System.Drawing">
    <value>156, 20</value>
  </data>
  <data name="txt_CommercialDiscounts.TabIndex" type="System.Int32, mscorlib">
    <value>134</value>
  </data>
  <data name="&gt;&gt;txt_CommercialDiscounts.Name" xml:space="preserve">
    <value>txt_CommercialDiscounts</value>
  </data>
  <data name="&gt;&gt;txt_CommercialDiscounts.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_CommercialDiscounts.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_CommercialDiscounts.ZOrder" xml:space="preserve">
    <value>60</value>
  </data>
  <data name="txt_totalAfterCommercial_Disc.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_totalAfterCommercial_Disc.Location" type="System.Drawing.Point, System.Drawing">
    <value>11, 404</value>
  </data>
  <data name="txt_totalAfterCommercial_Disc.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_totalAfterCommercial_Disc.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_totalAfterCommercial_Disc.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_totalAfterCommercial_Disc.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_totalAfterCommercial_Disc.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_totalAfterCommercial_Disc.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_totalAfterCommercial_Disc.Size" type="System.Drawing.Size, System.Drawing">
    <value>156, 20</value>
  </data>
  <data name="txt_totalAfterCommercial_Disc.TabIndex" type="System.Int32, mscorlib">
    <value>134</value>
  </data>
  <data name="&gt;&gt;txt_totalAfterCommercial_Disc.Name" xml:space="preserve">
    <value>txt_totalAfterCommercial_Disc</value>
  </data>
  <data name="&gt;&gt;txt_totalAfterCommercial_Disc.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_totalAfterCommercial_Disc.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_totalAfterCommercial_Disc.ZOrder" xml:space="preserve">
    <value>61</value>
  </data>
  <data name="txt_total_b4_Discounts.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_total_b4_Discounts.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 355</value>
  </data>
  <data name="txt_total_b4_Discounts.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_total_b4_Discounts.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_total_b4_Discounts.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_total_b4_Discounts.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_total_b4_Discounts.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_total_b4_Discounts.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_total_b4_Discounts.Size" type="System.Drawing.Size, System.Drawing">
    <value>156, 20</value>
  </data>
  <data name="txt_total_b4_Discounts.TabIndex" type="System.Int32, mscorlib">
    <value>134</value>
  </data>
  <data name="&gt;&gt;txt_total_b4_Discounts.Name" xml:space="preserve">
    <value>txt_total_b4_Discounts</value>
  </data>
  <data name="&gt;&gt;txt_total_b4_Discounts.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_total_b4_Discounts.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_total_b4_Discounts.ZOrder" xml:space="preserve">
    <value>62</value>
  </data>
  <data name="txt_Total.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_Total.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 481</value>
  </data>
  <data name="txt_Total.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Total.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Total.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Total.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Total.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Total.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Total.Size" type="System.Drawing.Size, System.Drawing">
    <value>156, 20</value>
  </data>
  <data name="txt_Total.TabIndex" type="System.Int32, mscorlib">
    <value>134</value>
  </data>
  <data name="&gt;&gt;txt_Total.Name" xml:space="preserve">
    <value>txt_Total</value>
  </data>
  <data name="&gt;&gt;txt_Total.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_Total.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_Total.ZOrder" xml:space="preserve">
    <value>63</value>
  </data>
  <data name="lbl_totalAfterCommercial_Disc.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="lbl_totalAfterCommercial_Disc.Location" type="System.Drawing.Point, System.Drawing">
    <value>174, 406</value>
  </data>
  <data name="lbl_totalAfterCommercial_Disc.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 13</value>
  </data>
  <data name="lbl_totalAfterCommercial_Disc.TabIndex" type="System.Int32, mscorlib">
    <value>133</value>
  </data>
  <data name="lbl_totalAfterCommercial_Disc.Text" xml:space="preserve">
    <value>Total after Discounts</value>
  </data>
  <data name="&gt;&gt;lbl_totalAfterCommercial_Disc.Name" xml:space="preserve">
    <value>lbl_totalAfterCommercial_Disc</value>
  </data>
  <data name="&gt;&gt;lbl_totalAfterCommercial_Disc.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lbl_totalAfterCommercial_Disc.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lbl_totalAfterCommercial_Disc.ZOrder" xml:space="preserve">
    <value>64</value>
  </data>
  <data name="lbl_CommercialDiscounts.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="lbl_CommercialDiscounts.Location" type="System.Drawing.Point, System.Drawing">
    <value>174, 382</value>
  </data>
  <data name="lbl_CommercialDiscounts.Size" type="System.Drawing.Size, System.Drawing">
    <value>103, 13</value>
  </data>
  <data name="lbl_CommercialDiscounts.TabIndex" type="System.Int32, mscorlib">
    <value>133</value>
  </data>
  <data name="lbl_CommercialDiscounts.Text" xml:space="preserve">
    <value>Commercial Discounts</value>
  </data>
  <data name="&gt;&gt;lbl_CommercialDiscounts.Name" xml:space="preserve">
    <value>lbl_CommercialDiscounts</value>
  </data>
  <data name="&gt;&gt;lbl_CommercialDiscounts.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lbl_CommercialDiscounts.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lbl_CommercialDiscounts.ZOrder" xml:space="preserve">
    <value>65</value>
  </data>
  <data name="lbl_total_b4_Discounts.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="lbl_total_b4_Discounts.Location" type="System.Drawing.Point, System.Drawing">
    <value>174, 358</value>
  </data>
  <data name="lbl_total_b4_Discounts.Size" type="System.Drawing.Size, System.Drawing">
    <value>161, 13</value>
  </data>
  <data name="lbl_total_b4_Discounts.TabIndex" type="System.Int32, mscorlib">
    <value>133</value>
  </data>
  <data name="lbl_total_b4_Discounts.Text" xml:space="preserve">
    <value>Total before Discounts and Taxes</value>
  </data>
  <data name="&gt;&gt;lbl_total_b4_Discounts.Name" xml:space="preserve">
    <value>lbl_total_b4_Discounts</value>
  </data>
  <data name="&gt;&gt;lbl_total_b4_Discounts.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lbl_total_b4_Discounts.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lbl_total_b4_Discounts.ZOrder" xml:space="preserve">
    <value>66</value>
  </data>
  <data name="labelControl18.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl18.Location" type="System.Drawing.Point, System.Drawing">
    <value>173, 484</value>
  </data>
  <data name="labelControl18.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 13</value>
  </data>
  <data name="labelControl18.TabIndex" type="System.Int32, mscorlib">
    <value>133</value>
  </data>
  <data name="labelControl18.Text" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="&gt;&gt;labelControl18.Name" xml:space="preserve">
    <value>labelControl18</value>
  </data>
  <data name="&gt;&gt;labelControl18.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl18.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl18.ZOrder" xml:space="preserve">
    <value>67</value>
  </data>
  <data name="txtNet.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txtNet.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 692</value>
  </data>
  <data name="txtNet.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtNet.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtNet.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtNet.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtNet.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtNet.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtNet.Size" type="System.Drawing.Size, System.Drawing">
    <value>155, 20</value>
  </data>
  <data name="txtNet.TabIndex" type="System.Int32, mscorlib">
    <value>116</value>
  </data>
  <data name="&gt;&gt;txtNet.Name" xml:space="preserve">
    <value>txtNet</value>
  </data>
  <data name="&gt;&gt;txtNet.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtNet.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtNet.ZOrder" xml:space="preserve">
    <value>68</value>
  </data>
  <data name="panelControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="labelControl4.Location" type="System.Drawing.Point, System.Drawing">
    <value>236, 21</value>
  </data>
  <data name="labelControl4.Size" type="System.Drawing.Size, System.Drawing">
    <value>28, 13</value>
  </data>
  <data name="labelControl4.TabIndex" type="System.Int32, mscorlib">
    <value>269</value>
  </data>
  <data name="labelControl4.Text" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="&gt;&gt;labelControl4.Name" xml:space="preserve">
    <value>labelControl4</value>
  </data>
  <data name="&gt;&gt;labelControl4.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl4.Parent" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="&gt;&gt;labelControl4.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lblShipTo.Location" type="System.Drawing.Point, System.Drawing">
    <value>236, 67</value>
  </data>
  <data name="lblShipTo.Size" type="System.Drawing.Size, System.Drawing">
    <value>33, 13</value>
  </data>
  <data name="lblShipTo.TabIndex" type="System.Int32, mscorlib">
    <value>268</value>
  </data>
  <data name="lblShipTo.Text" xml:space="preserve">
    <value>Ship to</value>
  </data>
  <data name="&gt;&gt;lblShipTo.Name" xml:space="preserve">
    <value>lblShipTo</value>
  </data>
  <data name="&gt;&gt;lblShipTo.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lblShipTo.Parent" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="&gt;&gt;lblShipTo.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="flowLayoutPanel1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtTserial.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtTserial.EditValue" xml:space="preserve">
    <value>Book</value>
  </data>
  <data name="txtTserial.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTserial.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="txtTserial.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txtTserial.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="txtTserial.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTserial.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtTserial.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTserial.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTserial.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTserial.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtTserial.Size" type="System.Drawing.Size, System.Drawing">
    <value>98, 22</value>
  </data>
  <data name="txtTserial.TabIndex" type="System.Int32, mscorlib">
    <value>255</value>
  </data>
  <data name="&gt;&gt;txtTserial.Name" xml:space="preserve">
    <value>txtTserial</value>
  </data>
  <data name="&gt;&gt;txtTserial.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtTserial.Parent" xml:space="preserve">
    <value>pnlBook</value>
  </data>
  <data name="&gt;&gt;txtTserial.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lkp_InvoiceBook.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_InvoiceBook.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns" xml:space="preserve">
    <value>InvoiceBookName</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns1" xml:space="preserve">
    <value>Book Name</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns7" xml:space="preserve">
    <value>IsTaxable</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns8" xml:space="preserve">
    <value>Taxable</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns14" xml:space="preserve">
    <value>PrintFileName</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns15" xml:space="preserve">
    <value>PrintFileName</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_InvoiceBook.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_InvoiceBook.Size" type="System.Drawing.Size, System.Drawing">
    <value>98, 20</value>
  </data>
  <data name="lkp_InvoiceBook.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;lkp_InvoiceBook.Name" xml:space="preserve">
    <value>lkp_InvoiceBook</value>
  </data>
  <data name="&gt;&gt;lkp_InvoiceBook.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_InvoiceBook.Parent" xml:space="preserve">
    <value>pnlBook</value>
  </data>
  <data name="&gt;&gt;lkp_InvoiceBook.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pnlBook.Location" type="System.Drawing.Point, System.Drawing">
    <value>758, 1</value>
  </data>
  <data name="pnlBook.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="pnlBook.Size" type="System.Drawing.Size, System.Drawing">
    <value>102, 43</value>
  </data>
  <data name="pnlBook.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;pnlBook.Name" xml:space="preserve">
    <value>pnlBook</value>
  </data>
  <data name="&gt;&gt;pnlBook.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlBook.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;pnlBook.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txtTinvCode.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtTinvCode.EditValue" xml:space="preserve">
    <value>Invoice Code</value>
  </data>
  <data name="txtTinvCode.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTinvCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="txtTinvCode.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txtTinvCode.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="txtTinvCode.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTinvCode.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtTinvCode.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTinvCode.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTinvCode.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTinvCode.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtTinvCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>79, 22</value>
  </data>
  <data name="txtTinvCode.TabIndex" type="System.Int32, mscorlib">
    <value>254</value>
  </data>
  <data name="&gt;&gt;txtTinvCode.Name" xml:space="preserve">
    <value>txtTinvCode</value>
  </data>
  <data name="&gt;&gt;txtTinvCode.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtTinvCode.Parent" xml:space="preserve">
    <value>pnlInvCode</value>
  </data>
  <data name="&gt;&gt;txtTinvCode.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txtInvoiceCode.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtInvoiceCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="txtInvoiceCode.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtInvoiceCode.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtInvoiceCode.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtInvoiceCode.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtInvoiceCode.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtInvoiceCode.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtInvoiceCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>79, 20</value>
  </data>
  <data name="txtInvoiceCode.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;txtInvoiceCode.Name" xml:space="preserve">
    <value>txtInvoiceCode</value>
  </data>
  <data name="&gt;&gt;txtInvoiceCode.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtInvoiceCode.Parent" xml:space="preserve">
    <value>pnlInvCode</value>
  </data>
  <data name="&gt;&gt;txtInvoiceCode.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pnlInvCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>673, 1</value>
  </data>
  <data name="pnlInvCode.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="pnlInvCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>83, 44</value>
  </data>
  <data name="pnlInvCode.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;pnlInvCode.Name" xml:space="preserve">
    <value>pnlInvCode</value>
  </data>
  <data name="&gt;&gt;pnlInvCode.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlInvCode.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;pnlInvCode.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="txtTdate.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txtTdate.EditValue" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="txtTdate.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTdate.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="txtTdate.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txtTdate.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="txtTdate.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTdate.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtTdate.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTdate.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTdate.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTdate.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtTdate.Size" type="System.Drawing.Size, System.Drawing">
    <value>126, 22</value>
  </data>
  <data name="txtTdate.TabIndex" type="System.Int32, mscorlib">
    <value>253</value>
  </data>
  <data name="&gt;&gt;txtTdate.Name" xml:space="preserve">
    <value>txtTdate</value>
  </data>
  <data name="&gt;&gt;txtTdate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtTdate.Parent" xml:space="preserve">
    <value>pnlDate</value>
  </data>
  <data name="&gt;&gt;txtTdate.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="dtInvoiceDate.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="dtInvoiceDate.EditValue" type="System.DateTime, mscorlib">
    <value>2015-07-08</value>
  </data>
  <data name="dtInvoiceDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="dtInvoiceDate.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtInvoiceDate.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtInvoiceDate.Properties.Mask.EditMask" xml:space="preserve">
    <value>g</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dtInvoiceDate.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtInvoiceDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>126, 20</value>
  </data>
  <data name="dtInvoiceDate.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;dtInvoiceDate.Name" xml:space="preserve">
    <value>dtInvoiceDate</value>
  </data>
  <data name="&gt;&gt;dtInvoiceDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.DateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;dtInvoiceDate.Parent" xml:space="preserve">
    <value>pnlDate</value>
  </data>
  <data name="&gt;&gt;dtInvoiceDate.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pnlDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>542, 1</value>
  </data>
  <data name="pnlDate.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="pnlDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>129, 44</value>
  </data>
  <data name="pnlDate.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;pnlDate.Name" xml:space="preserve">
    <value>pnlDate</value>
  </data>
  <data name="&gt;&gt;pnlDate.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlDate.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;pnlDate.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="txtTstore.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtTstore.EditValue" xml:space="preserve">
    <value>Branch</value>
  </data>
  <data name="txtTstore.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTstore.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="txtTstore.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txtTstore.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="txtTstore.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTstore.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtTstore.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTstore.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTstore.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTstore.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtTstore.Size" type="System.Drawing.Size, System.Drawing">
    <value>138, 22</value>
  </data>
  <data name="txtTstore.TabIndex" type="System.Int32, mscorlib">
    <value>252</value>
  </data>
  <data name="&gt;&gt;txtTstore.Name" xml:space="preserve">
    <value>txtTstore</value>
  </data>
  <data name="&gt;&gt;txtTstore.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtTstore.Parent" xml:space="preserve">
    <value>pnlBranch</value>
  </data>
  <data name="&gt;&gt;txtTstore.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lkpStore.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkpStore.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="lkpStore.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpStore.Properties.Columns" xml:space="preserve">
    <value>StoreNameEn</value>
  </data>
  <data name="lkpStore.Properties.Columns1" xml:space="preserve">
    <value>Branch Name</value>
  </data>
  <data name="lkpStore.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpStore.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpStore.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpStore.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkpStore.Properties.Columns7" xml:space="preserve">
    <value>StoreCode</value>
  </data>
  <data name="lkpStore.Properties.Columns8" xml:space="preserve">
    <value>Branch Code</value>
  </data>
  <data name="lkpStore.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpStore.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpStore.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkpStore.Properties.Columns14" xml:space="preserve">
    <value>StoreId</value>
  </data>
  <data name="lkpStore.Properties.Columns15" xml:space="preserve">
    <value>StoreId</value>
  </data>
  <data name="lkpStore.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpStore.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpStore.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpStore.Properties.Columns21" xml:space="preserve">
    <value>PurchaseAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns22" xml:space="preserve">
    <value>PurchaseAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns23" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpStore.Properties.Columns24" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpStore.Properties.Columns25" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns26" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Properties.Columns27" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpStore.Properties.Columns28" xml:space="preserve">
    <value>PurchaseReturnAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns29" xml:space="preserve">
    <value>PurchaseReturnAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns30" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpStore.Properties.Columns31" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpStore.Properties.Columns32" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns33" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Properties.Columns34" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpStore.Properties.Columns35" xml:space="preserve">
    <value>SellAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns36" xml:space="preserve">
    <value>SellAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns37" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpStore.Properties.Columns38" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpStore.Properties.Columns39" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns40" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Properties.Columns41" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpStore.Properties.Columns42" xml:space="preserve">
    <value>SellReturnAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns43" xml:space="preserve">
    <value>SellReturnAccount</value>
  </data>
  <data name="lkpStore.Properties.Columns44" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpStore.Properties.Columns45" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpStore.Properties.Columns46" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns47" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Properties.Columns48" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpStore.Properties.Columns49" xml:space="preserve">
    <value>StoreNameAr</value>
  </data>
  <data name="lkpStore.Properties.Columns50" xml:space="preserve">
    <value>اسم الفرع بالعربية</value>
  </data>
  <data name="lkpStore.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpStore.Size" type="System.Drawing.Size, System.Drawing">
    <value>138, 20</value>
  </data>
  <data name="lkpStore.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="&gt;&gt;lkpStore.Name" xml:space="preserve">
    <value>lkpStore</value>
  </data>
  <data name="&gt;&gt;lkpStore.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpStore.Parent" xml:space="preserve">
    <value>pnlBranch</value>
  </data>
  <data name="&gt;&gt;lkpStore.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pnlBranch.Location" type="System.Drawing.Point, System.Drawing">
    <value>399, 1</value>
  </data>
  <data name="pnlBranch.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="pnlBranch.Size" type="System.Drawing.Size, System.Drawing">
    <value>141, 44</value>
  </data>
  <data name="pnlBranch.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="&gt;&gt;pnlBranch.Name" xml:space="preserve">
    <value>pnlBranch</value>
  </data>
  <data name="&gt;&gt;pnlBranch.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlBranch.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;pnlBranch.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="txtTdueDate.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txtTdueDate.EditValue" xml:space="preserve">
    <value>Due Date</value>
  </data>
  <data name="txtTdueDate.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTdueDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="txtTdueDate.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txtTdueDate.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="txtTdueDate.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTdueDate.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtTdueDate.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTdueDate.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTdueDate.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTdueDate.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtTdueDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>125, 22</value>
  </data>
  <data name="txtTdueDate.TabIndex" type="System.Int32, mscorlib">
    <value>249</value>
  </data>
  <data name="&gt;&gt;txtTdueDate.Name" xml:space="preserve">
    <value>txtTdueDate</value>
  </data>
  <data name="&gt;&gt;txtTdueDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtTdueDate.Parent" xml:space="preserve">
    <value>pnlAgeDate</value>
  </data>
  <data name="&gt;&gt;txtTdueDate.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txt_DueDays.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_DueDays.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_DueDays.Location" type="System.Drawing.Point, System.Drawing">
    <value>89, 23</value>
  </data>
  <data name="txt_DueDays.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DueDays.Properties.Mask.EditMask" xml:space="preserve">
    <value>N00</value>
  </data>
  <data name="txt_DueDays.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DueDays.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_DueDays.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DueDays.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DueDays.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_DueDays.Size" type="System.Drawing.Size, System.Drawing">
    <value>38, 20</value>
  </data>
  <data name="txt_DueDays.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;txt_DueDays.Name" xml:space="preserve">
    <value>txt_DueDays</value>
  </data>
  <data name="&gt;&gt;txt_DueDays.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_DueDays.Parent" xml:space="preserve">
    <value>pnlAgeDate</value>
  </data>
  <data name="&gt;&gt;txt_DueDays.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="txt_DueDate.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txt_DueDate.EditValue" type="System.DateTime, mscorlib">
    <value>2015-07-08</value>
  </data>
  <data name="txt_DueDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="txt_DueDate.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DueDate.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="txt_DueDate.Properties.CalendarTimeProperties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DueDate.Properties.CalendarTimeProperties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_DueDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DueDate.Properties.CalendarTimeProperties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_DueDate.Properties.CalendarTimeProperties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DueDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DueDate.Properties.CalendarTimeProperties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_DueDate.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_DueDate.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DueDate.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_DueDate.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DueDate.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DueDate.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_DueDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>86, 20</value>
  </data>
  <data name="txt_DueDate.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="&gt;&gt;txt_DueDate.Name" xml:space="preserve">
    <value>txt_DueDate</value>
  </data>
  <data name="&gt;&gt;txt_DueDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.DateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_DueDate.Parent" xml:space="preserve">
    <value>pnlAgeDate</value>
  </data>
  <data name="&gt;&gt;txt_DueDate.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="pnlAgeDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>268, 1</value>
  </data>
  <data name="pnlAgeDate.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="pnlAgeDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>129, 44</value>
  </data>
  <data name="pnlAgeDate.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;pnlAgeDate.Name" xml:space="preserve">
    <value>pnlAgeDate</value>
  </data>
  <data name="&gt;&gt;pnlAgeDate.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlAgeDate.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;pnlAgeDate.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="txtTdeliverDate.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txtTdeliverDate.EditValue" xml:space="preserve">
    <value>Deliver Date</value>
  </data>
  <data name="txtTdeliverDate.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTdeliverDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="txtTdeliverDate.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txtTdeliverDate.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="txtTdeliverDate.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTdeliverDate.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtTdeliverDate.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTdeliverDate.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTdeliverDate.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTdeliverDate.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtTdeliverDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>91, 22</value>
  </data>
  <data name="txtTdeliverDate.TabIndex" type="System.Int32, mscorlib">
    <value>251</value>
  </data>
  <data name="&gt;&gt;txtTdeliverDate.Name" xml:space="preserve">
    <value>txtTdeliverDate</value>
  </data>
  <data name="&gt;&gt;txtTdeliverDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtTdeliverDate.Parent" xml:space="preserve">
    <value>pnlDeliveryDate</value>
  </data>
  <data name="&gt;&gt;txtTdeliverDate.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="dtDeliverDate.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="dtDeliverDate.EditValue" type="System.DateTime, mscorlib">
    <value>2015-07-08</value>
  </data>
  <data name="dtDeliverDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="dtDeliverDate.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtDeliverDate.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="dtDeliverDate.Properties.CalendarTimeProperties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtDeliverDate.Properties.CalendarTimeProperties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtDeliverDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtDeliverDate.Properties.CalendarTimeProperties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="dtDeliverDate.Properties.CalendarTimeProperties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtDeliverDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtDeliverDate.Properties.CalendarTimeProperties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtDeliverDate.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtDeliverDate.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtDeliverDate.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="dtDeliverDate.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtDeliverDate.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtDeliverDate.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtDeliverDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>91, 20</value>
  </data>
  <data name="dtDeliverDate.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="&gt;&gt;dtDeliverDate.Name" xml:space="preserve">
    <value>dtDeliverDate</value>
  </data>
  <data name="&gt;&gt;dtDeliverDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.DateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;dtDeliverDate.Parent" xml:space="preserve">
    <value>pnlDeliveryDate</value>
  </data>
  <data name="&gt;&gt;dtDeliverDate.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pnlDeliveryDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>171, 1</value>
  </data>
  <data name="pnlDeliveryDate.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="pnlDeliveryDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>95, 44</value>
  </data>
  <data name="pnlDeliveryDate.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="&gt;&gt;pnlDeliveryDate.Name" xml:space="preserve">
    <value>pnlDeliveryDate</value>
  </data>
  <data name="&gt;&gt;pnlDeliveryDate.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlDeliveryDate.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;pnlDeliveryDate.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="uc_Currency1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 21</value>
  </data>
  <data name="uc_Currency1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="uc_Currency1.Size" type="System.Drawing.Size, System.Drawing">
    <value>130, 19</value>
  </data>
  <data name="uc_Currency1.TabIndex" type="System.Int32, mscorlib">
    <value>274</value>
  </data>
  <data name="&gt;&gt;uc_Currency1.Name" xml:space="preserve">
    <value>uc_Currency1</value>
  </data>
  <data name="&gt;&gt;uc_Currency1.Type" xml:space="preserve">
    <value>Pharmacy.Forms.uc_Currency, LinkIT ERP System, Version=2.24.9.18, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="&gt;&gt;uc_Currency1.Parent" xml:space="preserve">
    <value>pnlCurrency</value>
  </data>
  <data name="&gt;&gt;uc_Currency1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txtCurrency.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txtCurrency.EditValue" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="txtCurrency.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtCurrency.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 1</value>
  </data>
  <data name="txtCurrency.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txtCurrency.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="txtCurrency.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtCurrency.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtCurrency.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtCurrency.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtCurrency.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtCurrency.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtCurrency.Size" type="System.Drawing.Size, System.Drawing">
    <value>128, 22</value>
  </data>
  <data name="txtCurrency.TabIndex" type="System.Int32, mscorlib">
    <value>273</value>
  </data>
  <data name="&gt;&gt;txtCurrency.Name" xml:space="preserve">
    <value>txtCurrency</value>
  </data>
  <data name="&gt;&gt;txtCurrency.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtCurrency.Parent" xml:space="preserve">
    <value>pnlCurrency</value>
  </data>
  <data name="&gt;&gt;txtCurrency.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pnlCurrency.Location" type="System.Drawing.Point, System.Drawing">
    <value>36, 1</value>
  </data>
  <data name="pnlCurrency.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="pnlCurrency.Size" type="System.Drawing.Size, System.Drawing">
    <value>133, 43</value>
  </data>
  <data name="pnlCurrency.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="&gt;&gt;pnlCurrency.Name" xml:space="preserve">
    <value>pnlCurrency</value>
  </data>
  <data name="&gt;&gt;pnlCurrency.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlCurrency.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;pnlCurrency.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="txt_Post_Date.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txt_Post_Date.EditValue" type="System.DateTime, mscorlib">
    <value>2015-07-08</value>
  </data>
  <data name="txt_Post_Date.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 24</value>
  </data>
  <data name="txt_Post_Date.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Post_Date.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="txt_Post_Date.Properties.CalendarTimeProperties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Post_Date.Properties.CalendarTimeProperties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Post_Date.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Post_Date.Properties.CalendarTimeProperties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_Post_Date.Properties.CalendarTimeProperties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Post_Date.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Post_Date.Properties.CalendarTimeProperties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Post_Date.Properties.Mask.EditMask" xml:space="preserve">
    <value>g</value>
  </data>
  <data name="txt_Post_Date.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Post_Date.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_Post_Date.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Post_Date.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Post_Date.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Post_Date.Size" type="System.Drawing.Size, System.Drawing">
    <value>133, 20</value>
  </data>
  <data name="txt_Post_Date.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;txt_Post_Date.Name" xml:space="preserve">
    <value>txt_Post_Date</value>
  </data>
  <data name="&gt;&gt;txt_Post_Date.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.DateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_Post_Date.Parent" xml:space="preserve">
    <value>pnlPostStore</value>
  </data>
  <data name="&gt;&gt;txt_Post_Date.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="chk_IsPosted.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="chk_IsPosted.EditValue" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="chk_IsPosted.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 3</value>
  </data>
  <data name="chk_IsPosted.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chk_IsPosted.Properties.Caption" xml:space="preserve">
    <value>Post to Store</value>
  </data>
  <data name="chk_IsPosted.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="chk_IsPosted.Size" type="System.Drawing.Size, System.Drawing">
    <value>133, 19</value>
  </data>
  <data name="chk_IsPosted.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;chk_IsPosted.Name" xml:space="preserve">
    <value>chk_IsPosted</value>
  </data>
  <data name="&gt;&gt;chk_IsPosted.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;chk_IsPosted.Parent" xml:space="preserve">
    <value>pnlPostStore</value>
  </data>
  <data name="&gt;&gt;chk_IsPosted.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pnlPostStore.Location" type="System.Drawing.Point, System.Drawing">
    <value>719, 49</value>
  </data>
  <data name="pnlPostStore.Size" type="System.Drawing.Size, System.Drawing">
    <value>139, 44</value>
  </data>
  <data name="pnlPostStore.TabIndex" type="System.Int32, mscorlib">
    <value>301</value>
  </data>
  <data name="&gt;&gt;pnlPostStore.Name" xml:space="preserve">
    <value>pnlPostStore</value>
  </data>
  <data name="&gt;&gt;pnlPostStore.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlPostStore.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;pnlPostStore.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="txtTpo.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txtTpo.EditValue" xml:space="preserve">
    <value>P.O</value>
  </data>
  <data name="txtTpo.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTpo.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 2</value>
  </data>
  <data name="txtTpo.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txtTpo.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="txtTpo.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTpo.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtTpo.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTpo.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTpo.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTpo.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtTpo.Size" type="System.Drawing.Size, System.Drawing">
    <value>98, 22</value>
  </data>
  <data name="txtTpo.TabIndex" type="System.Int32, mscorlib">
    <value>271</value>
  </data>
  <data name="&gt;&gt;txtTpo.Name" xml:space="preserve">
    <value>txtTpo</value>
  </data>
  <data name="&gt;&gt;txtTpo.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtTpo.Parent" xml:space="preserve">
    <value>pnlPO</value>
  </data>
  <data name="&gt;&gt;txtTpo.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txt_PO_No.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txt_PO_No.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 23</value>
  </data>
  <data name="txt_PO_No.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="txt_PO_No.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_PO_No.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_PO_No.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_PO_No.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_PO_No.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_PO_No.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_PO_No.Size" type="System.Drawing.Size, System.Drawing">
    <value>98, 20</value>
  </data>
  <data name="txt_PO_No.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="&gt;&gt;txt_PO_No.Name" xml:space="preserve">
    <value>txt_PO_No</value>
  </data>
  <data name="&gt;&gt;txt_PO_No.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_PO_No.Parent" xml:space="preserve">
    <value>pnlPO</value>
  </data>
  <data name="&gt;&gt;txt_PO_No.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pnlPO.Location" type="System.Drawing.Point, System.Drawing">
    <value>613, 47</value>
  </data>
  <data name="pnlPO.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="pnlPO.Size" type="System.Drawing.Size, System.Drawing">
    <value>102, 44</value>
  </data>
  <data name="pnlPO.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="&gt;&gt;pnlPO.Name" xml:space="preserve">
    <value>pnlPO</value>
  </data>
  <data name="&gt;&gt;pnlPO.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlPO.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;pnlPO.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="txtTSalesEmp.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="txtTSalesEmp.EditValue" xml:space="preserve">
    <value>Sales Employee</value>
  </data>
  <data name="txtTSalesEmp.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="txtTSalesEmp.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txtTSalesEmp.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="txtTSalesEmp.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTSalesEmp.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtTSalesEmp.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTSalesEmp.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTSalesEmp.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTSalesEmp.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtTSalesEmp.Size" type="System.Drawing.Size, System.Drawing">
    <value>174, 22</value>
  </data>
  <data name="txtTSalesEmp.TabIndex" type="System.Int32, mscorlib">
    <value>256</value>
  </data>
  <data name="&gt;&gt;txtTSalesEmp.Name" xml:space="preserve">
    <value>txtTSalesEmp</value>
  </data>
  <data name="&gt;&gt;txtTSalesEmp.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtTSalesEmp.Parent" xml:space="preserve">
    <value>pnlSalesEmp</value>
  </data>
  <data name="&gt;&gt;txtTSalesEmp.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lkp_SalesEmp.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="lkp_SalesEmp.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="lkp_SalesEmp.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns" xml:space="preserve">
    <value>EmpFName</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns1" xml:space="preserve">
    <value>Employee</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns2" xml:space="preserve">
    <value>EmpCode</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns3" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns4" xml:space="preserve">
    <value>EmpId</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns5" xml:space="preserve">
    <value>EmpId</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns6" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns7" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns8" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_SalesEmp.Properties.Columns9" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns10" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkp_SalesEmp.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_SalesEmp.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_SalesEmp.Size" type="System.Drawing.Size, System.Drawing">
    <value>174, 20</value>
  </data>
  <data name="lkp_SalesEmp.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="&gt;&gt;lkp_SalesEmp.Name" xml:space="preserve">
    <value>lkp_SalesEmp</value>
  </data>
  <data name="&gt;&gt;lkp_SalesEmp.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_SalesEmp.Parent" xml:space="preserve">
    <value>pnlSalesEmp</value>
  </data>
  <data name="&gt;&gt;lkp_SalesEmp.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pnlSalesEmp.Location" type="System.Drawing.Point, System.Drawing">
    <value>433, 47</value>
  </data>
  <data name="pnlSalesEmp.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="pnlSalesEmp.Size" type="System.Drawing.Size, System.Drawing">
    <value>178, 44</value>
  </data>
  <data name="pnlSalesEmp.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="&gt;&gt;pnlSalesEmp.Name" xml:space="preserve">
    <value>pnlSalesEmp</value>
  </data>
  <data name="&gt;&gt;pnlSalesEmp.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlSalesEmp.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;pnlSalesEmp.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="textEdit9.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="textEdit9.EditValue" xml:space="preserve">
    <value>Cost Center</value>
  </data>
  <data name="textEdit9.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit9.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="textEdit9.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="textEdit9.Properties.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="textEdit9.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit9.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit9.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit9.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit9.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit9.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit9.Size" type="System.Drawing.Size, System.Drawing">
    <value>137, 22</value>
  </data>
  <data name="textEdit9.TabIndex" type="System.Int32, mscorlib">
    <value>259</value>
  </data>
  <data name="&gt;&gt;textEdit9.Name" xml:space="preserve">
    <value>textEdit9</value>
  </data>
  <data name="&gt;&gt;textEdit9.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;textEdit9.Parent" xml:space="preserve">
    <value>pnlCostCenter</value>
  </data>
  <data name="&gt;&gt;textEdit9.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lkpCostCenter.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="lkpCostCenter.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="lkpCostCenter.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpCostCenter.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpCostCenter.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkpCostCenter.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn43.Caption" xml:space="preserve">
    <value>CostCenter Code</value>
  </data>
  <data name="gridColumn43.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn43.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn42.Caption" xml:space="preserve">
    <value>CostCenter Name</value>
  </data>
  <data name="gridColumn42.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn42.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn40.Caption" xml:space="preserve">
    <value>CostCenterId</value>
  </data>
  <data name="lkpCostCenter.Size" type="System.Drawing.Size, System.Drawing">
    <value>137, 20</value>
  </data>
  <data name="lkpCostCenter.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;lkpCostCenter.Name" xml:space="preserve">
    <value>lkpCostCenter</value>
  </data>
  <data name="&gt;&gt;lkpCostCenter.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpCostCenter.Parent" xml:space="preserve">
    <value>pnlCostCenter</value>
  </data>
  <data name="&gt;&gt;lkpCostCenter.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="pnlCostCenter.Location" type="System.Drawing.Point, System.Drawing">
    <value>290, 47</value>
  </data>
  <data name="pnlCostCenter.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>1, 1, 1, 1</value>
  </data>
  <data name="pnlCostCenter.Size" type="System.Drawing.Size, System.Drawing">
    <value>141, 44</value>
  </data>
  <data name="pnlCostCenter.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="&gt;&gt;pnlCostCenter.Name" xml:space="preserve">
    <value>pnlCostCenter</value>
  </data>
  <data name="&gt;&gt;pnlCostCenter.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlCostCenter.Parent" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;pnlCostCenter.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="flowLayoutPanel1.FlowDirection" type="System.Windows.Forms.FlowDirection, System.Windows.Forms">
    <value>RightToLeft</value>
  </data>
  <data name="flowLayoutPanel1.Location" type="System.Drawing.Point, System.Drawing">
    <value>287, 0</value>
  </data>
  <data name="flowLayoutPanel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>861, 97</value>
  </data>
  <data name="flowLayoutPanel1.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;flowLayoutPanel1.Name" xml:space="preserve">
    <value>flowLayoutPanel1</value>
  </data>
  <data name="&gt;&gt;flowLayoutPanel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.FlowLayoutPanel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;flowLayoutPanel1.Parent" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="&gt;&gt;flowLayoutPanel1.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="txtNotes.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="txtNotes.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 5</value>
  </data>
  <data name="txtNotes.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txtNotes.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtNotes.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="txtNotes.Size" type="System.Drawing.Size, System.Drawing">
    <value>230, 44</value>
  </data>
  <data name="txtNotes.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="&gt;&gt;txtNotes.Name" xml:space="preserve">
    <value>txtNotes</value>
  </data>
  <data name="&gt;&gt;txtNotes.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.MemoEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtNotes.Parent" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="&gt;&gt;txtNotes.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="txt_Shipping.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 50</value>
  </data>
  <data name="txt_Shipping.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="txt_Shipping.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Shipping.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="txt_Shipping.Size" type="System.Drawing.Size, System.Drawing">
    <value>229, 44</value>
  </data>
  <data name="txt_Shipping.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="&gt;&gt;txt_Shipping.Name" xml:space="preserve">
    <value>txt_Shipping</value>
  </data>
  <data name="&gt;&gt;txt_Shipping.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.MemoEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_Shipping.Parent" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="&gt;&gt;txt_Shipping.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="panelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>21, 80</value>
  </data>
  <data name="panelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>1265, 105</value>
  </data>
  <data name="panelControl1.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;panelControl1.Name" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="&gt;&gt;panelControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.PanelControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;panelControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;panelControl1.ZOrder" xml:space="preserve">
    <value>69</value>
  </data>
  <data name="labelControl13.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl13.Location" type="System.Drawing.Point, System.Drawing">
    <value>173, 699</value>
  </data>
  <data name="labelControl13.Size" type="System.Drawing.Size, System.Drawing">
    <value>17, 13</value>
  </data>
  <data name="labelControl13.TabIndex" type="System.Int32, mscorlib">
    <value>115</value>
  </data>
  <data name="labelControl13.Text" xml:space="preserve">
    <value>Net</value>
  </data>
  <data name="&gt;&gt;labelControl13.Name" xml:space="preserve">
    <value>labelControl13</value>
  </data>
  <data name="&gt;&gt;labelControl13.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl13.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl13.ZOrder" xml:space="preserve">
    <value>70</value>
  </data>
  <data name="labelControl36.Location" type="System.Drawing.Point, System.Drawing">
    <value>79, 61</value>
  </data>
  <data name="labelControl36.Size" type="System.Drawing.Size, System.Drawing">
    <value>62, 13</value>
  </data>
  <data name="labelControl36.TabIndex" type="System.Int32, mscorlib">
    <value>87</value>
  </data>
  <data name="labelControl36.Text" xml:space="preserve">
    <value>Sell Invoices </value>
  </data>
  <data name="&gt;&gt;labelControl36.Name" xml:space="preserve">
    <value>labelControl36</value>
  </data>
  <data name="&gt;&gt;labelControl36.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl36.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl36.ZOrder" xml:space="preserve">
    <value>71</value>
  </data>
  <data name="btnNext.Location" type="System.Drawing.Point, System.Drawing">
    <value>46, 58</value>
  </data>
  <data name="btnNext.Size" type="System.Drawing.Size, System.Drawing">
    <value>27, 19</value>
  </data>
  <data name="btnNext.TabIndex" type="System.Int32, mscorlib">
    <value>72</value>
  </data>
  <data name="btnNext.ToolTip" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="&gt;&gt;btnNext.Name" xml:space="preserve">
    <value>btnNext</value>
  </data>
  <data name="&gt;&gt;btnNext.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;btnNext.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnNext.ZOrder" xml:space="preserve">
    <value>72</value>
  </data>
  <data name="btnPrevious.Location" type="System.Drawing.Point, System.Drawing">
    <value>15, 58</value>
  </data>
  <data name="btnPrevious.Size" type="System.Drawing.Size, System.Drawing">
    <value>25, 19</value>
  </data>
  <data name="btnPrevious.TabIndex" type="System.Int32, mscorlib">
    <value>71</value>
  </data>
  <data name="btnPrevious.Text" xml:space="preserve">
    <value>&lt;=</value>
  </data>
  <data name="btnPrevious.ToolTip" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="&gt;&gt;btnPrevious.Name" xml:space="preserve">
    <value>btnPrevious</value>
  </data>
  <data name="&gt;&gt;btnPrevious.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;btnPrevious.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnPrevious.ZOrder" xml:space="preserve">
    <value>73</value>
  </data>
  <data name="labelControl9.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelControl9.Location" type="System.Drawing.Point, System.Drawing">
    <value>185, 671</value>
  </data>
  <data name="labelControl9.Size" type="System.Drawing.Size, System.Drawing">
    <value>71, 13</value>
  </data>
  <data name="labelControl9.TabIndex" type="System.Int32, mscorlib">
    <value>90</value>
  </data>
  <data name="labelControl9.Text" xml:space="preserve">
    <value>Other Charges</value>
  </data>
  <data name="&gt;&gt;labelControl9.Name" xml:space="preserve">
    <value>labelControl9</value>
  </data>
  <data name="&gt;&gt;labelControl9.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl9.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl9.ZOrder" xml:space="preserve">
    <value>74</value>
  </data>
  <data name="txtExpenses.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txtExpenses.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txtExpenses.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 666</value>
  </data>
  <data name="txtExpenses.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtExpenses.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="txtExpenses.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtExpenses.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtExpenses.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtExpenses.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtExpenses.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtExpenses.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 20</value>
  </data>
  <data name="txtExpenses.TabIndex" type="System.Int32, mscorlib">
    <value>23</value>
  </data>
  <data name="&gt;&gt;txtExpenses.Name" xml:space="preserve">
    <value>txtExpenses</value>
  </data>
  <data name="&gt;&gt;txtExpenses.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtExpenses.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtExpenses.ZOrder" xml:space="preserve">
    <value>75</value>
  </data>
  <data name="txtDiscountRatio.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txtDiscountRatio.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txtDiscountRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>132, 505</value>
  </data>
  <data name="txtDiscountRatio.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDiscountRatio.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDiscountRatio.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDiscountRatio.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtDiscountRatio.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDiscountRatio.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDiscountRatio.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDiscountRatio.Size" type="System.Drawing.Size, System.Drawing">
    <value>36, 20</value>
  </data>
  <data name="txtDiscountRatio.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="&gt;&gt;txtDiscountRatio.Name" xml:space="preserve">
    <value>txtDiscountRatio</value>
  </data>
  <data name="&gt;&gt;txtDiscountRatio.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtDiscountRatio.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtDiscountRatio.ZOrder" xml:space="preserve">
    <value>76</value>
  </data>
  <data name="txtDiscountValue.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txtDiscountValue.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txtDiscountValue.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 505</value>
  </data>
  <data name="txtDiscountValue.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDiscountValue.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDiscountValue.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 20</value>
  </data>
  <data name="txtDiscountValue.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="&gt;&gt;txtDiscountValue.Name" xml:space="preserve">
    <value>txtDiscountValue</value>
  </data>
  <data name="&gt;&gt;txtDiscountValue.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtDiscountValue.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtDiscountValue.ZOrder" xml:space="preserve">
    <value>77</value>
  </data>
  <data name="txt_TaxValue.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_TaxValue.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_TaxValue.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_TaxValue.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 551</value>
  </data>
  <data name="txt_TaxValue.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_TaxValue.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_TaxValue.Size" type="System.Drawing.Size, System.Drawing">
    <value>155, 20</value>
  </data>
  <data name="txt_TaxValue.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="&gt;&gt;txt_TaxValue.Name" xml:space="preserve">
    <value>txt_TaxValue</value>
  </data>
  <data name="&gt;&gt;txt_TaxValue.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_TaxValue.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_TaxValue.ZOrder" xml:space="preserve">
    <value>78</value>
  </data>
  <data name="txt_DeductTaxR.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_DeductTaxR.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_DeductTaxR.Location" type="System.Drawing.Point, System.Drawing">
    <value>132, 574</value>
  </data>
  <data name="txt_DeductTaxR.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DeductTaxR.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_DeductTaxR.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 20</value>
  </data>
  <data name="txt_DeductTaxR.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxR.Name" xml:space="preserve">
    <value>txt_DeductTaxR</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxR.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxR.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxR.ZOrder" xml:space="preserve">
    <value>79</value>
  </data>
  <data name="txt_DeductTaxV.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_DeductTaxV.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_DeductTaxV.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 574</value>
  </data>
  <data name="txt_DeductTaxV.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DeductTaxV.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_DeductTaxV.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 20</value>
  </data>
  <data name="txt_DeductTaxV.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxV.Name" xml:space="preserve">
    <value>txt_DeductTaxV</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxV.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxV.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_DeductTaxV.ZOrder" xml:space="preserve">
    <value>80</value>
  </data>
  <data name="txt_AddTaxR.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_AddTaxR.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_AddTaxR.Location" type="System.Drawing.Point, System.Drawing">
    <value>132, 597</value>
  </data>
  <data name="txt_AddTaxR.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_AddTaxR.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AddTaxR.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_AddTaxR.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 20</value>
  </data>
  <data name="txt_AddTaxR.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxR.Name" xml:space="preserve">
    <value>txt_AddTaxR</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxR.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxR.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxR.ZOrder" xml:space="preserve">
    <value>81</value>
  </data>
  <data name="txt_AddTaxV.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txt_AddTaxV.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_AddTaxV.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 597</value>
  </data>
  <data name="txt_AddTaxV.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AddTaxV.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_AddTaxV.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 20</value>
  </data>
  <data name="txt_AddTaxV.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxV.Name" xml:space="preserve">
    <value>txt_AddTaxV</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxV.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxV.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_AddTaxV.ZOrder" xml:space="preserve">
    <value>82</value>
  </data>
  <data name="txtExpensesR.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="txtExpensesR.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txtExpensesR.Location" type="System.Drawing.Point, System.Drawing">
    <value>132, 668</value>
  </data>
  <data name="txtExpensesR.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtExpensesR.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtExpensesR.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtExpensesR.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtExpensesR.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtExpensesR.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtExpensesR.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtExpensesR.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 20</value>
  </data>
  <data name="txtExpensesR.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="&gt;&gt;txtExpensesR.Name" xml:space="preserve">
    <value>txtExpensesR</value>
  </data>
  <data name="&gt;&gt;txtExpensesR.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtExpensesR.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtExpensesR.ZOrder" xml:space="preserve">
    <value>83</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAA
        AAAfbf8AH2z/AB9r/x8fa/+rH2v/9h9r/9AfbP9HH2//AB9u/wAfcP8AAAAAAB9p/wAfZv8AH2D/Ah9j
        /2cfY//iH2P/7R9j/4YfY/8KH2P/AB9j/wAAAAAADw4QAA4NDwAODQ4ADQwORwwMDdAMCw32CwsMqwsL
        DB8MCw0ADAsNAB9u/wAfbf8WH23/qx9t//8fbv//H27//x9u/9gfb/89H3D/AB9y/wAfcf8AH23/AB9n
        /wAfZ/9cH2X/6x9j//8fY///H2P/9x9j/38fY/8GH2P/AB5e8QAPDhAADw4QAA4NDz0NDQ/YDQ0O/w0M
        Dv8MDA3/DAsNqwwLDRYMCw0AH2//FR9v/6Ifb///H3D//x9x//8fcf//H3H//x9x/9Mfcv82IHD/AiBw
        /wQfcP8DH2z/VB9q/+gfZ///H2X//x9j//8fY///H2P/9x9j/3cfY/8FH2P+ABAPEQAQDxE1Dw4Q0w8O
        EP8ODg//Dg0P/w0NDv8MDA3/DAsNogwLDRUfcP+MH3H//R9y//8ec///HnP//x50//8edP//HnP//x5z
        /9cec/+pH3L/qR9x/6wfbv/mH23//x9q//8faP//H2X//x9j//8fY///H2P/8h9j/04bTb8AERASERAQ
        ErsQEBL/EA8R/w8PEf8PDhD/Dg0P/w0ND/8MDA79DAwNjB5z/9QedP//HnX//x52//8edv//Hnb//x52
        //8edv//Hnb//x51//8edP//HnP//x9x//8fb///H23//x9r//8faP//H2X//x9j//8fY///H2P/gxg4
        hAASERM1ERET7hERE/8REBL/EBAS/xAPEf8PDhD/Dg4Q/w0ND/8NDA7THnX/wx52//8ed///Hnj//x54
        //8eef//Hnn//x54//8eeP/9Hnf/8h52//Iedf/zHnP//h9x//8fb///H23//x9q//8fZ///H2T//x9j
        //8fY/90GkGeABISFCYTEhThEhIU/xIRE/8RERP/EBAS/xAPEf8PDhD/Dg4Q/w4ND78ed/9UHnj/5x55
        //8eev//Hnr//x57//8ee///Hnr/+x56/5AeeP88Hnf/PB52/0Eedf+wHnP//x9x//8fb///H2z//x9p
        //8fZv//H2T/zB9j/yQdV9wAExIVAxMTFYMTEhX8ExIU/xISFP8RERP/ERAS/xAPEf8PDhDkDg4QTx50
        /wAeev9dHnv/7R58//8efP//Hn3//x59//0efP+UHnv/DB55/wAed/8AHnb/AB52/x8edf+5HnP//x9x
        //8fbv//H2v//x9o/9YfZf85H2f/AAAAAAAUExYAFBMWDRQTFpQUExX9ExIV/xISFP8SERP/ERAS6hAP
        EVYTDhQAHnr/AB58/wEefP9nHn3/7x5+//8efv/9Hn7/nR59/xAefP8AHnv/AB52/wAedf8AHnX/AB51
        /yUedP2+H3P//x9x//8fbPvbH2r/Px9o/wAfZv8AH2P/ABQTFgAUExYAFBQWEBQUFp0UExb9ExIV/xIS
        FOwRERNfIxkMABAQEQAee/8AHn3/AB5+/wcefv+xHn///x5//+oef/8tHn//AB59/wAefP8AAAAAAB52
        /wAeV/8AGkKGABk+eUYaQoPzG0KG/xo0ZZIbRZkAH2v/AB9o/wAAAAAAFBQWABQUFgAVFBcAFRQXLRUU
        F+oUExb/ExMVqxMTFAUSERQAEBASAAAAAAAegP8AHn//Ax6A/6kegP//HoD/5x6A/ycegP8AHoD/AAAA
        AAAAAAAAAAAAABpCgQAXFRcAFhMSOhcVFvAXFRb/GBUWjhgYHgAcO3gAAAAAAAAAAAAAAAAAFxYZABYV
        GAAWFRgnFRUX5xUUF/8UExajExIVAhQTFgAAAAAAHoD/AB6A/wAegP8IHoD/sx6B//8egf/rHoH/Lx6B
        /wAegv8AHoL/AAAAAAAXFhkAFBQXABcXGgAXFxpIGBca9BgYG/8ZGBuTGRkcABoaHQAbGx4AAAAAABkZ
        HAAYGBsAFxcZABcWGS8WFhjrFRUX/xUUF6wUFBYGFBMWABQTFQAef/8AHoD/Ah6A/20egf/xHoH//x6B
        //4egv+kHoL/Ex6C/wAegv8AFhYYABsZIAAXFxoAFxcaKRgXGsMYGBv/GRkc/xoZHN4bGh1FHBoeABwb
        HwAdHCAAGhkdABkZHAAYGBsTGBcapBcWGf4WFRj/FRQX7xQUFmYTEBMBExMVAB59/wIegP9kHoD/8B6B
        //8egv//HoL//x6C//8egv+cHoL/EB6B/wAaRX4AFxYZABcWGSQXFxq/GBgb/xkZHP8aGR3/Gxoe/xwb
        HtkcGx89HBwfABoYIQAbGh0AGxodEBkZHJwYGBv/Fxca/xYWGf8VFRf/FBQW7RQTFVwBABAAHn//Vx5/
        /+kegP//HoH//x6C//8egv//HoL//x6C//4egf+JHoD/BBgxUQAWFhgSFxYZsRgXGv8ZGBv/Ghkc/xsa
        Hf8cGx7/HBwf/x0cIM8eHSAmHRwgAB0cHwQbGh6JGhkc/hkYG/8YFxr/FxYZ/xYVGP8VFBf/ExMV5hMS
        FFIefv/EHn///x6A//8egf//HoL//x6C//8egv//HoL//x6B/+Megf8nGUB0ABYWGEwXFhn4GBca/xkY
        G/8aGh3/Gxse/xwcH/8dHCD/Hh0h/x4dIXUdHCAAHBsfJxwbHuMaGh3/GRkc/xgXGv8XFhn/FhUY/xUU
        F/8UExX/ExIUwR5+/9Uef///HoD//x6B//8egf//HoL//x6C//8egf//HoH/7h6B/zQaRX4AFhYZWhcW
        GfwYGBv/GRkc/xsaHf8cGx7/HRwg/x4dIf8fHiL/Hx4igx4dIQAdHB80HBsf7hsaHf8aGRz/GBgb/xcX
        Gf8WFRj/FRQX/xQTFf8TEhXSHn7/jR5///0egP//HoD//x6B//8egf//HoH//x6B//8egf+4HoD/EBk3
        YAAWFhkqFxYZ2xgYG/8ZGRz/Gxod/xwbHv8dHCD/Hh0h/x8eIu8gHyNKHx4iABwcHxAcGx64Gxod/xoZ
        HP8YGBv/Fxca/xYVGP8VFBf/FBMV/BMSFYgefv8VHn7/oh5///8egP//HoD//x6B//8egf//HoD/0B6A
        /zIegf8AFxIRABgaHQAXFxpQGBca5hkYHP8aGh3/Gxse/x0cH/8dHSD1Hh0hch8eIwQfHiIAGxseABwb
        HjIaGh3QGRkc/xgXG/8XFhn/FhUY/xUUF/8UExafExIVEx5+/wAefv8XHn7/qx5///8ef///Hn///x6A
        /9UegP85HoD/AB5//wAYIzUAFxcaABgYGwAYFxpXGRgb6hoZHP8bGh7/HBse+B0cH3weHSEFHh0hAB8e
        IgAbGh0AGxodABoZHDkZGBvVGBca/xcWGf8WFRj/FRQXqBQUFhUUExYAHn7/AB5+/wAefv8dHn7/yR5+
        //8efv/yHn//Sh5//wAef/8AHoD/AAAAAAAYFxoAGBcaABUXGgAZGBtUGRkcwxoaHc0bGh5yHBseCB0c
        HwAeHSEAAAAAABsaHQAaGRwAGRgbABgYG0oYFxryFxYZ/xYVGMUVFBcbFRQXABQUFgAefv8AHnz/AB56
        /wIefP+oHn3//x59/+ceff8lHn3/AB5//wAAAAAAAAAAAAAAAAAYGBoAGBgbABgXGgAaGRwNGhkdERkZ
        HAAbGh4AGxoeAAAAAAAAAAAAAAAAABkZHAAXFxoAFxcaJRcWGecWFRj/FRUXohMSFQEVFBcAFRQXAB50
        /wAed/8AHnn/CR56/7Qee///Hnv/6x57/y4eev8AHnj/AB53/wAAAAAAFRQXABYVGAAWFRgAFxYZJBgX
        GnEYFxp6GRgbNBkOEQAYGBsAGBcaAAAAAAAWFhgAFhYYABYWGQAXFhkuFhYY6xUVF/8VFBeuFBQWBxMT
        FQASERQAHnL/AB11/wMedv9zHnf/8x54//8eef/+Hnn/oR55/xEed/8AHnb/ABMSFgAbFx0AFRQXABUV
        Fy4WFhjJFxYZ/xgXGv8YFxreGBcaShcXGgAYFxoAFxYZABYVGAAWFRgAFhYYERYWGKEWFRj+FRQX/xQT
        FvETEhVuEhETAxIREwAgb/8CH3L/aR50//Iedf//Hnb//x53//8ed//+Hnf/lx52/w0edf8AGEOMABQT
        FgAUExYnFRQXxBUVF/8WFhj/FxYZ/xcXGv8XFxrdFxcaQRYWGQAWFRgAFhUYABYVGA0WFRiXFhUY/hUU
        F/8UExb/ExMV/xISFPARERNlEQ4RAh9t/1sfb//sH3H//x9y//8ec///HnT//x50//8edP/9HnT/hx9y
        /wUVKUwAEhIUFBMSFbYUExb/FBQW/xERE/8QEBL/FhUY/xYWGf8XFhnXFhYYYBYVGE4WFRhOFhUYmBUV
        F/wVFBf/FBMW/xMTFf8SEhT/ERET/xEQEuoQDxFXH2v/xh9s//8fbv//H2///x9w//8fcf//H3H//x9x
        //8fcf/kH3D/KBc3cQASERNOEhIU+BMSFf8SERT/DAwN/woJC/8QDxH/FRUX/xYVGP8WFRj7FRUX+BUU
        F/gVFBf/FBQW/xQTFv8TEhX/EhIU/xIRE/8REBL/EA8R/w8PEMMfZ//UH2n//x9r//8fbP//H23//x9u
        //8fbv//H27//x9u/+4fbv80Fzl6ABEQE1oRERP8EhIU/xERE/8ODg//DAwN/xAPEf8UExb/FBQW/xQU
        Fv8UFBb/FBMW/xQTFv8TExX/ExIU/xISFP8SERP/ERAS/xAQEv8PDxH/Dg4Q0R9l/4kfZf/8H2f//x9o
        //8faf//H2r//x9q//8fav//H2v/sh9r/w4VKlcAEBASKRAQEtoRERP/EhET/xEQEv8QEBL/EhIU/xMT
        Ff8TExXyExMVsxMTFagTEhWoExIU1BISFP8SERP/ERET/xEQEv8QDxH/Dw8R/w4OEPwODQ+EH2P/Ex9j
        /6AfZP//H2T//x9l//8fZv//H2b//x9n/8gfaP8qH2j/AA0CAAAREBIAEA8RThAQEuUREBL/ERET/xIR
        E/8SERP/EhEU9hISFHYTExUIEhEUAxMRFQISERMxERETzREQEv8QEBL/EA8R/w8OEP8ODhD/Dg0Pnw0N
        DxIfY/8AH2P/Fh9j/6kfY///H2P//x9j//8fY//MH2T/MB9m/wAfVP8AERgnABAPEQAQERMAEA8RVRAP
        EegQEBL/EBAS/xEQEvcRERN9EhETBhISFAATEhUAExIVABEREwAQEBI2EA8R0g8PEf8PDhD/Dg4P/w4N
        D6gNDQ8VDg0PAB9j/wAfY/8AH2P/HR9j/6QfY//nH2P/wh9j/zofZP8AH2P/AB9m/wAAAAAAEA8RAA8P
        EQAKDhIBDw8QYA8PEdcQDxHiEA8RgRAPEQkREBIAEhETAAAAAAARERMAEA8RABAPEQAPDhBADg4Qxg4N
        D+cODQ+iDQ0OHA0NDwAODQ8AACAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAGAIBw
        DgEAIAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAQAAHAOAAAgBAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgBAA=
</value>
  </data>
  <data name="$this.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Sales Invoice</value>
  </data>
  <data name="&gt;&gt;barManager1.Name" xml:space="preserve">
    <value>barManager1</value>
  </data>
  <data name="&gt;&gt;barManager1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarManager, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;bar1.Name" xml:space="preserve">
    <value>bar1</value>
  </data>
  <data name="&gt;&gt;bar1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Bar, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnHelp.Name" xml:space="preserve">
    <value>barBtnHelp</value>
  </data>
  <data name="&gt;&gt;barBtnHelp.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;btnAttachments.Name" xml:space="preserve">
    <value>btnAttachments</value>
  </data>
  <data name="&gt;&gt;btnAttachments.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barSubItemPrint.Name" xml:space="preserve">
    <value>barSubItemPrint</value>
  </data>
  <data name="&gt;&gt;barSubItemPrint.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarSubItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barbtnPrint.Name" xml:space="preserve">
    <value>barbtnPrint</value>
  </data>
  <data name="&gt;&gt;barbtnPrint.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barbtnPrintF.Name" xml:space="preserve">
    <value>barbtnPrintF</value>
  </data>
  <data name="&gt;&gt;barbtnPrintF.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barbtnPrintStore.Name" xml:space="preserve">
    <value>barbtnPrintStore</value>
  </data>
  <data name="&gt;&gt;barbtnPrintStore.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barbtnSamplesPr.Name" xml:space="preserve">
    <value>barbtnSamplesPr</value>
  </data>
  <data name="&gt;&gt;barbtnSamplesPr.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarSubItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnDelete.Name" xml:space="preserve">
    <value>barBtnDelete</value>
  </data>
  <data name="&gt;&gt;barBtnDelete.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnNew.Name" xml:space="preserve">
    <value>barBtnNew</value>
  </data>
  <data name="&gt;&gt;barBtnNew.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnCancel.Name" xml:space="preserve">
    <value>barBtnCancel</value>
  </data>
  <data name="&gt;&gt;barBtnCancel.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnSave.Name" xml:space="preserve">
    <value>barBtnSave</value>
  </data>
  <data name="&gt;&gt;barBtnSave.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;batBtnList.Name" xml:space="preserve">
    <value>batBtnList</value>
  </data>
  <data name="&gt;&gt;batBtnList.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnClose.Name" xml:space="preserve">
    <value>barBtnClose</value>
  </data>
  <data name="&gt;&gt;barBtnClose.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barAndDockingController1.Name" xml:space="preserve">
    <value>barAndDockingController1</value>
  </data>
  <data name="&gt;&gt;barAndDockingController1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarAndDockingController, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnNotesReceivable.Name" xml:space="preserve">
    <value>barBtnNotesReceivable</value>
  </data>
  <data name="&gt;&gt;barBtnNotesReceivable.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnLoad_Sl_Qoute.Name" xml:space="preserve">
    <value>barBtnLoad_Sl_Qoute</value>
  </data>
  <data name="&gt;&gt;barBtnLoad_Sl_Qoute.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnLoad_SalesOrder.Name" xml:space="preserve">
    <value>barBtnLoad_SalesOrder</value>
  </data>
  <data name="&gt;&gt;barBtnLoad_SalesOrder.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnLoad_IC_OutTrns.Name" xml:space="preserve">
    <value>barBtnLoad_IC_OutTrns</value>
  </data>
  <data name="&gt;&gt;barBtnLoad_IC_OutTrns.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnLoad_JO.Name" xml:space="preserve">
    <value>barBtnLoad_JO</value>
  </data>
  <data name="&gt;&gt;barBtnLoad_JO.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnLoad_PR_Invoice.Name" xml:space="preserve">
    <value>barBtnLoad_PR_Invoice</value>
  </data>
  <data name="&gt;&gt;barBtnLoad_PR_Invoice.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnLoad_IC_Transfer.Name" xml:space="preserve">
    <value>barBtnLoad_IC_Transfer</value>
  </data>
  <data name="&gt;&gt;barBtnLoad_IC_Transfer.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtn_OutTrns.Name" xml:space="preserve">
    <value>barBtn_OutTrns</value>
  </data>
  <data name="&gt;&gt;barBtn_OutTrns.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtn_CashNote.Name" xml:space="preserve">
    <value>barBtn_CashNote</value>
  </data>
  <data name="&gt;&gt;barBtn_CashNote.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barbtnvisanote.Name" xml:space="preserve">
    <value>barbtnvisanote</value>
  </data>
  <data name="&gt;&gt;barbtnvisanote.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barbtn_EditPerm.Name" xml:space="preserve">
    <value>barbtn_EditPerm</value>
  </data>
  <data name="&gt;&gt;barbtn_EditPerm.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barbtn_DelPerm.Name" xml:space="preserve">
    <value>barbtn_DelPerm</value>
  </data>
  <data name="&gt;&gt;barbtn_DelPerm.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barbtn_PrintPerm.Name" xml:space="preserve">
    <value>barbtn_PrintPerm</value>
  </data>
  <data name="&gt;&gt;barbtn_PrintPerm.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repositoryItemTextEdit1.Name" xml:space="preserve">
    <value>repositoryItemTextEdit1</value>
  </data>
  <data name="&gt;&gt;repositoryItemTextEdit1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemTextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;bar2.Name" xml:space="preserve">
    <value>bar2</value>
  </data>
  <data name="&gt;&gt;bar2.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Bar, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gv_CostCenter.Name" xml:space="preserve">
    <value>gv_CostCenter</value>
  </data>
  <data name="&gt;&gt;gv_CostCenter.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn43.Name" xml:space="preserve">
    <value>gridColumn43</value>
  </data>
  <data name="&gt;&gt;gridColumn43.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn42.Name" xml:space="preserve">
    <value>gridColumn42</value>
  </data>
  <data name="&gt;&gt;gridColumn42.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn40.Name" xml:space="preserve">
    <value>gridColumn40</value>
  </data>
  <data name="&gt;&gt;gridColumn40.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView1.Name" xml:space="preserve">
    <value>gridView1</value>
  </data>
  <data name="&gt;&gt;gridView1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn19.Name" xml:space="preserve">
    <value>gridColumn19</value>
  </data>
  <data name="&gt;&gt;gridColumn19.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn20.Name" xml:space="preserve">
    <value>gridColumn20</value>
  </data>
  <data name="&gt;&gt;gridColumn20.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn21.Name" xml:space="preserve">
    <value>gridColumn21</value>
  </data>
  <data name="&gt;&gt;gridColumn21.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn22.Name" xml:space="preserve">
    <value>gridColumn22</value>
  </data>
  <data name="&gt;&gt;gridColumn22.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn26.Name" xml:space="preserve">
    <value>gridColumn26</value>
  </data>
  <data name="&gt;&gt;gridColumn26.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn27.Name" xml:space="preserve">
    <value>gridColumn27</value>
  </data>
  <data name="&gt;&gt;gridColumn27.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn30.Name" xml:space="preserve">
    <value>gridColumn30</value>
  </data>
  <data name="&gt;&gt;gridColumn30.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn35.Name" xml:space="preserve">
    <value>gridColumn35</value>
  </data>
  <data name="&gt;&gt;gridColumn35.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;mi_frm_IC_Item.Name" xml:space="preserve">
    <value>mi_frm_IC_Item</value>
  </data>
  <data name="&gt;&gt;mi_frm_IC_Item.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mi_CustLastPrices.Name" xml:space="preserve">
    <value>mi_CustLastPrices</value>
  </data>
  <data name="&gt;&gt;mi_CustLastPrices.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mi_LastPrices.Name" xml:space="preserve">
    <value>mi_LastPrices</value>
  </data>
  <data name="&gt;&gt;mi_LastPrices.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mi_PasteRows.Name" xml:space="preserve">
    <value>mi_PasteRows</value>
  </data>
  <data name="&gt;&gt;mi_PasteRows.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mi_ExportData.Name" xml:space="preserve">
    <value>mi_ExportData</value>
  </data>
  <data name="&gt;&gt;mi_ExportData.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mi_InvoiceStaticDisc.Name" xml:space="preserve">
    <value>mi_InvoiceStaticDisc</value>
  </data>
  <data name="&gt;&gt;mi_InvoiceStaticDisc.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mi_InvoiceStaticDimensions.Name" xml:space="preserve">
    <value>mi_InvoiceStaticDimensions</value>
  </data>
  <data name="&gt;&gt;mi_InvoiceStaticDimensions.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mi_ImportExcel.Name" xml:space="preserve">
    <value>mi_ImportExcel</value>
  </data>
  <data name="&gt;&gt;mi_ImportExcel.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;gridView2.Name" xml:space="preserve">
    <value>gridView2</value>
  </data>
  <data name="&gt;&gt;gridView2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;DiscountTaxRatio.Name" xml:space="preserve">
    <value>DiscountTaxRatio</value>
  </data>
  <data name="&gt;&gt;DiscountTaxRatio.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;DiscountTax.Name" xml:space="preserve">
    <value>DiscountTax</value>
  </data>
  <data name="&gt;&gt;DiscountTax.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colETaxValue.Name" xml:space="preserve">
    <value>colETaxValue</value>
  </data>
  <data name="&gt;&gt;colETaxValue.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repSpin.Name" xml:space="preserve">
    <value>repSpin</value>
  </data>
  <data name="&gt;&gt;repSpin.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colETaxRatio.Name" xml:space="preserve">
    <value>colETaxRatio</value>
  </data>
  <data name="&gt;&gt;colETaxRatio.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Col_ETaxType.Name" xml:space="preserve">
    <value>Col_ETaxType</value>
  </data>
  <data name="&gt;&gt;Col_ETaxType.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repTaxTypes.Name" xml:space="preserve">
    <value>repTaxTypes</value>
  </data>
  <data name="&gt;&gt;repTaxTypes.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView9.Name" xml:space="preserve">
    <value>gridView9</value>
  </data>
  <data name="&gt;&gt;gridView9.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;ColDescriptionAr.Name" xml:space="preserve">
    <value>ColDescriptionAr</value>
  </data>
  <data name="&gt;&gt;ColDescriptionAr.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;ColCode.Name" xml:space="preserve">
    <value>ColCode</value>
  </data>
  <data name="&gt;&gt;ColCode.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colE_TaxableTypeId.Name" xml:space="preserve">
    <value>colE_TaxableTypeId</value>
  </data>
  <data name="&gt;&gt;colE_TaxableTypeId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colBonusDiscount.Name" xml:space="preserve">
    <value>colBonusDiscount</value>
  </data>
  <data name="&gt;&gt;colBonusDiscount.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Col_Company.Name" xml:space="preserve">
    <value>Col_Company</value>
  </data>
  <data name="&gt;&gt;Col_Company.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repCompany.Name" xml:space="preserve">
    <value>repCompany</value>
  </data>
  <data name="&gt;&gt;repCompany.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView8.Name" xml:space="preserve">
    <value>gridView8</value>
  </data>
  <data name="&gt;&gt;gridView8.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;ColCompanyNameAr.Name" xml:space="preserve">
    <value>ColCompanyNameAr</value>
  </data>
  <data name="&gt;&gt;ColCompanyNameAr.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Col_CompanyCode.Name" xml:space="preserve">
    <value>Col_CompanyCode</value>
  </data>
  <data name="&gt;&gt;Col_CompanyCode.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;CompanyId.Name" xml:space="preserve">
    <value>CompanyId</value>
  </data>
  <data name="&gt;&gt;CompanyId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Col_Category.Name" xml:space="preserve">
    <value>Col_Category</value>
  </data>
  <data name="&gt;&gt;Col_Category.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repCategory.Name" xml:space="preserve">
    <value>repCategory</value>
  </data>
  <data name="&gt;&gt;repCategory.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repositoryItemGridLookUpEdit2View.Name" xml:space="preserve">
    <value>repositoryItemGridLookUpEdit2View</value>
  </data>
  <data name="&gt;&gt;repositoryItemGridLookUpEdit2View.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;ColCatNumber.Name" xml:space="preserve">
    <value>ColCatNumber</value>
  </data>
  <data name="&gt;&gt;ColCatNumber.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;ColCategoryNameAr.Name" xml:space="preserve">
    <value>ColCategoryNameAr</value>
  </data>
  <data name="&gt;&gt;ColCategoryNameAr.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;ColCategoryId.Name" xml:space="preserve">
    <value>ColCategoryId</value>
  </data>
  <data name="&gt;&gt;ColCategoryId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_QC.Name" xml:space="preserve">
    <value>col_QC</value>
  </data>
  <data name="&gt;&gt;col_QC.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_ActualPiecesCount.Name" xml:space="preserve">
    <value>col_ActualPiecesCount</value>
  </data>
  <data name="&gt;&gt;col_ActualPiecesCount.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn29.Name" xml:space="preserve">
    <value>gridColumn29</value>
  </data>
  <data name="&gt;&gt;gridColumn29.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn28.Name" xml:space="preserve">
    <value>gridColumn28</value>
  </data>
  <data name="&gt;&gt;gridColumn28.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_TotalSellPrice.Name" xml:space="preserve">
    <value>col_TotalSellPrice</value>
  </data>
  <data name="&gt;&gt;col_TotalSellPrice.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_CurrentQty.Name" xml:space="preserve">
    <value>col_CurrentQty</value>
  </data>
  <data name="&gt;&gt;col_CurrentQty.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_CommercialDiscountValue.Name" xml:space="preserve">
    <value>col_CommercialDiscountValue</value>
  </data>
  <data name="&gt;&gt;col_CommercialDiscountValue.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn1.Name" xml:space="preserve">
    <value>gridColumn1</value>
  </data>
  <data name="&gt;&gt;gridColumn1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repDiscountRatio.Name" xml:space="preserve">
    <value>repDiscountRatio</value>
  </data>
  <data name="&gt;&gt;repDiscountRatio.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemTextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_SellPrice.Name" xml:space="preserve">
    <value>col_SellPrice</value>
  </data>
  <data name="&gt;&gt;col_SellPrice.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_AudiencePrice.Name" xml:space="preserve">
    <value>col_AudiencePrice</value>
  </data>
  <data name="&gt;&gt;col_AudiencePrice.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colPurchasePrice.Name" xml:space="preserve">
    <value>colPurchasePrice</value>
  </data>
  <data name="&gt;&gt;colPurchasePrice.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn7.Name" xml:space="preserve">
    <value>gridColumn7</value>
  </data>
  <data name="&gt;&gt;gridColumn7.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn8.Name" xml:space="preserve">
    <value>gridColumn8</value>
  </data>
  <data name="&gt;&gt;gridColumn8.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repUOM.Name" xml:space="preserve">
    <value>repUOM</value>
  </data>
  <data name="&gt;&gt;repUOM.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView4.Name" xml:space="preserve">
    <value>gridView4</value>
  </data>
  <data name="&gt;&gt;gridView4.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn9.Name" xml:space="preserve">
    <value>gridColumn9</value>
  </data>
  <data name="&gt;&gt;gridColumn9.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn16.Name" xml:space="preserve">
    <value>gridColumn16</value>
  </data>
  <data name="&gt;&gt;gridColumn16.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn17.Name" xml:space="preserve">
    <value>gridColumn17</value>
  </data>
  <data name="&gt;&gt;gridColumn17.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn3.Name" xml:space="preserve">
    <value>gridColumn3</value>
  </data>
  <data name="&gt;&gt;gridColumn3.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_ItemNameF.Name" xml:space="preserve">
    <value>col_ItemNameF</value>
  </data>
  <data name="&gt;&gt;col_ItemNameF.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn10.Name" xml:space="preserve">
    <value>gridColumn10</value>
  </data>
  <data name="&gt;&gt;gridColumn10.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repItems.Name" xml:space="preserve">
    <value>repItems</value>
  </data>
  <data name="&gt;&gt;repItems.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repositoryItemGridLookUpEdit1View.Name" xml:space="preserve">
    <value>repositoryItemGridLookUpEdit1View</value>
  </data>
  <data name="&gt;&gt;repositoryItemGridLookUpEdit1View.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn12.Name" xml:space="preserve">
    <value>gridColumn12</value>
  </data>
  <data name="&gt;&gt;gridColumn12.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn13.Name" xml:space="preserve">
    <value>gridColumn13</value>
  </data>
  <data name="&gt;&gt;gridColumn13.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn14.Name" xml:space="preserve">
    <value>gridColumn14</value>
  </data>
  <data name="&gt;&gt;gridColumn14.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn15.Name" xml:space="preserve">
    <value>gridColumn15</value>
  </data>
  <data name="&gt;&gt;gridColumn15.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn5.Name" xml:space="preserve">
    <value>gridColumn5</value>
  </data>
  <data name="&gt;&gt;gridColumn5.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn4.Name" xml:space="preserve">
    <value>gridColumn4</value>
  </data>
  <data name="&gt;&gt;gridColumn4.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn25.Name" xml:space="preserve">
    <value>gridColumn25</value>
  </data>
  <data name="&gt;&gt;gridColumn25.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_SellDiscountRatio.Name" xml:space="preserve">
    <value>col_SellDiscountRatio</value>
  </data>
  <data name="&gt;&gt;col_SellDiscountRatio.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_CompanyNameAr.Name" xml:space="preserve">
    <value>col_CompanyNameAr</value>
  </data>
  <data name="&gt;&gt;col_CompanyNameAr.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_CategoryNameAr.Name" xml:space="preserve">
    <value>col_CategoryNameAr</value>
  </data>
  <data name="&gt;&gt;col_CategoryNameAr.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;ColLength.Name" xml:space="preserve">
    <value>ColLength</value>
  </data>
  <data name="&gt;&gt;ColLength.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;ColWidth.Name" xml:space="preserve">
    <value>ColWidth</value>
  </data>
  <data name="&gt;&gt;ColWidth.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;ColHeight.Name" xml:space="preserve">
    <value>ColHeight</value>
  </data>
  <data name="&gt;&gt;ColHeight.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;grdcol_branch.Name" xml:space="preserve">
    <value>grdcol_branch</value>
  </data>
  <data name="&gt;&gt;grdcol_branch.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_storee.Name" xml:space="preserve">
    <value>lkp_storee</value>
  </data>
  <data name="&gt;&gt;lkp_storee.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView7.Name" xml:space="preserve">
    <value>gridView7</value>
  </data>
  <data name="&gt;&gt;gridView7.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn11.Name" xml:space="preserve">
    <value>gridColumn11</value>
  </data>
  <data name="&gt;&gt;gridColumn11.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn31.Name" xml:space="preserve">
    <value>gridColumn31</value>
  </data>
  <data name="&gt;&gt;gridColumn31.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn41.Name" xml:space="preserve">
    <value>gridColumn41</value>
  </data>
  <data name="&gt;&gt;gridColumn41.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Expire.Name" xml:space="preserve">
    <value>col_Expire</value>
  </data>
  <data name="&gt;&gt;col_Expire.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;rep_expireDate.Name" xml:space="preserve">
    <value>rep_expireDate</value>
  </data>
  <data name="&gt;&gt;rep_expireDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView5.Name" xml:space="preserve">
    <value>gridView5</value>
  </data>
  <data name="&gt;&gt;gridView5.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn18.Name" xml:space="preserve">
    <value>gridColumn18</value>
  </data>
  <data name="&gt;&gt;gridColumn18.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn23.Name" xml:space="preserve">
    <value>gridColumn23</value>
  </data>
  <data name="&gt;&gt;gridColumn23.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn24.Name" xml:space="preserve">
    <value>gridColumn24</value>
  </data>
  <data name="&gt;&gt;gridColumn24.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Batch.Name" xml:space="preserve">
    <value>col_Batch</value>
  </data>
  <data name="&gt;&gt;col_Batch.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;rep_Batch.Name" xml:space="preserve">
    <value>rep_Batch</value>
  </data>
  <data name="&gt;&gt;rep_Batch.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView6.Name" xml:space="preserve">
    <value>gridView6</value>
  </data>
  <data name="&gt;&gt;gridView6.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn33.Name" xml:space="preserve">
    <value>gridColumn33</value>
  </data>
  <data name="&gt;&gt;gridColumn33.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn34.Name" xml:space="preserve">
    <value>gridColumn34</value>
  </data>
  <data name="&gt;&gt;gridColumn34.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Length.Name" xml:space="preserve">
    <value>col_Length</value>
  </data>
  <data name="&gt;&gt;col_Length.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Width.Name" xml:space="preserve">
    <value>col_Width</value>
  </data>
  <data name="&gt;&gt;col_Width.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Height.Name" xml:space="preserve">
    <value>col_Height</value>
  </data>
  <data name="&gt;&gt;col_Height.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_TotalQty.Name" xml:space="preserve">
    <value>col_TotalQty</value>
  </data>
  <data name="&gt;&gt;col_TotalQty.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_PiecesCount.Name" xml:space="preserve">
    <value>col_PiecesCount</value>
  </data>
  <data name="&gt;&gt;col_PiecesCount.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repspin_PiecesCount.Name" xml:space="preserve">
    <value>repspin_PiecesCount</value>
  </data>
  <data name="&gt;&gt;repspin_PiecesCount.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_ItemDescription.Name" xml:space="preserve">
    <value>col_ItemDescription</value>
  </data>
  <data name="&gt;&gt;col_ItemDescription.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_ItemDescriptionEn.Name" xml:space="preserve">
    <value>col_ItemDescriptionEn</value>
  </data>
  <data name="&gt;&gt;col_ItemDescriptionEn.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_SalesTax.Name" xml:space="preserve">
    <value>col_SalesTax</value>
  </data>
  <data name="&gt;&gt;col_SalesTax.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_DiscountRatio2.Name" xml:space="preserve">
    <value>col_DiscountRatio2</value>
  </data>
  <data name="&gt;&gt;col_DiscountRatio2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_DiscountRatio3.Name" xml:space="preserve">
    <value>col_DiscountRatio3</value>
  </data>
  <data name="&gt;&gt;col_DiscountRatio3.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Serial.Name" xml:space="preserve">
    <value>col_Serial</value>
  </data>
  <data name="&gt;&gt;col_Serial.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Serial2.Name" xml:space="preserve">
    <value>col_Serial2</value>
  </data>
  <data name="&gt;&gt;col_Serial2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn32.Name" xml:space="preserve">
    <value>gridColumn32</value>
  </data>
  <data name="&gt;&gt;gridColumn32.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repManufactureDate.Name" xml:space="preserve">
    <value>repManufactureDate</value>
  </data>
  <data name="&gt;&gt;repManufactureDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemDateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_CusTax.Name" xml:space="preserve">
    <value>col_CusTax</value>
  </data>
  <data name="&gt;&gt;col_CusTax.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Location.Name" xml:space="preserve">
    <value>col_Location</value>
  </data>
  <data name="&gt;&gt;col_Location.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Libra.Name" xml:space="preserve">
    <value>col_Libra</value>
  </data>
  <data name="&gt;&gt;col_Libra.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;rep_Libra.Name" xml:space="preserve">
    <value>rep_Libra</value>
  </data>
  <data name="&gt;&gt;rep_Libra.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn36.Name" xml:space="preserve">
    <value>gridColumn36</value>
  </data>
  <data name="&gt;&gt;gridColumn36.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn37.Name" xml:space="preserve">
    <value>gridColumn37</value>
  </data>
  <data name="&gt;&gt;gridColumn37.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn38.Name" xml:space="preserve">
    <value>gridColumn38</value>
  </data>
  <data name="&gt;&gt;gridColumn38.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_kg_Weight_libra.Name" xml:space="preserve">
    <value>col_kg_Weight_libra</value>
  </data>
  <data name="&gt;&gt;col_kg_Weight_libra.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn39.Name" xml:space="preserve">
    <value>gridColumn39</value>
  </data>
  <data name="&gt;&gt;gridColumn39.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;ol_Index.Name" xml:space="preserve">
    <value>ol_Index</value>
  </data>
  <data name="&gt;&gt;ol_Index.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_itemperoffer.Name" xml:space="preserve">
    <value>col_itemperoffer</value>
  </data>
  <data name="&gt;&gt;col_itemperoffer.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Pack.Name" xml:space="preserve">
    <value>col_Pack</value>
  </data>
  <data name="&gt;&gt;col_Pack.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;btn_AddTaxes.Name" xml:space="preserve">
    <value>btn_AddTaxes</value>
  </data>
  <data name="&gt;&gt;btn_AddTaxes.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;rep_btnAddTaxes.Name" xml:space="preserve">
    <value>rep_btnAddTaxes</value>
  </data>
  <data name="&gt;&gt;rep_btnAddTaxes.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;TotalTaxes.Name" xml:space="preserve">
    <value>TotalTaxes</value>
  </data>
  <data name="&gt;&gt;TotalTaxes.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;totalTaxesRatio.Name" xml:space="preserve">
    <value>totalTaxesRatio</value>
  </data>
  <data name="&gt;&gt;totalTaxesRatio.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;salePriceWithTaxTable.Name" xml:space="preserve">
    <value>salePriceWithTaxTable</value>
  </data>
  <data name="&gt;&gt;salePriceWithTaxTable.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;totalTableTaxes.Name" xml:space="preserve">
    <value>totalTableTaxes</value>
  </data>
  <data name="&gt;&gt;totalTableTaxes.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;addTaxValue.Name" xml:space="preserve">
    <value>addTaxValue</value>
  </data>
  <data name="&gt;&gt;addTaxValue.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;tableTaxValue.Name" xml:space="preserve">
    <value>tableTaxValue</value>
  </data>
  <data name="&gt;&gt;tableTaxValue.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_TaxValue.Name" xml:space="preserve">
    <value>col_TaxValue</value>
  </data>
  <data name="&gt;&gt;col_TaxValue.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_TotalSubCustomTax.Name" xml:space="preserve">
    <value>col_TotalSubCustomTax</value>
  </data>
  <data name="&gt;&gt;col_TotalSubCustomTax.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_TotalSubAddTax.Name" xml:space="preserve">
    <value>col_TotalSubAddTax</value>
  </data>
  <data name="&gt;&gt;col_TotalSubAddTax.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_TotalSubDiscountTax.Name" xml:space="preserve">
    <value>col_TotalSubDiscountTax</value>
  </data>
  <data name="&gt;&gt;col_TotalSubDiscountTax.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_store.Name" xml:space="preserve">
    <value>lkp_store</value>
  </data>
  <data name="&gt;&gt;lkp_store.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repositoryItemSpinEdit1.Name" xml:space="preserve">
    <value>repositoryItemSpinEdit1</value>
  </data>
  <data name="&gt;&gt;repositoryItemSpinEdit1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;rep_Location.Name" xml:space="preserve">
    <value>rep_Location</value>
  </data>
  <data name="&gt;&gt;rep_Location.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView3.Name" xml:space="preserve">
    <value>gridView3</value>
  </data>
  <data name="&gt;&gt;gridView3.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colTotalPurchasePrice.Name" xml:space="preserve">
    <value>colTotalPurchasePrice</value>
  </data>
  <data name="&gt;&gt;colTotalPurchasePrice.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn6.Name" xml:space="preserve">
    <value>gridColumn6</value>
  </data>
  <data name="&gt;&gt;gridColumn6.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colUOM.Name" xml:space="preserve">
    <value>colUOM</value>
  </data>
  <data name="&gt;&gt;colUOM.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colQty.Name" xml:space="preserve">
    <value>colQty</value>
  </data>
  <data name="&gt;&gt;colQty.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colCustNameAr.Name" xml:space="preserve">
    <value>colCustNameAr</value>
  </data>
  <data name="&gt;&gt;colCustNameAr.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colInvoiceDate.Name" xml:space="preserve">
    <value>colInvoiceDate</value>
  </data>
  <data name="&gt;&gt;colInvoiceDate.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colInvoiceCode.Name" xml:space="preserve">
    <value>colInvoiceCode</value>
  </data>
  <data name="&gt;&gt;colInvoiceCode.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;timer1.Name" xml:space="preserve">
    <value>timer1</value>
  </data>
  <data name="&gt;&gt;timer1.Type" xml:space="preserve">
    <value>System.Windows.Forms.Timer, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;gv_SubTaxes.Name" xml:space="preserve">
    <value>gv_SubTaxes</value>
  </data>
  <data name="&gt;&gt;gv_SubTaxes.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Value.Name" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="&gt;&gt;Value.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;SubTaxId.Name" xml:space="preserve">
    <value>SubTaxId</value>
  </data>
  <data name="&gt;&gt;SubTaxId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_SubTaxes.Name" xml:space="preserve">
    <value>lkp_SubTaxes</value>
  </data>
  <data name="&gt;&gt;lkp_SubTaxes.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Rate.Name" xml:space="preserve">
    <value>col_Rate</value>
  </data>
  <data name="&gt;&gt;col_Rate.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>frm_SL_Invoice</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.XtraForm, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barBtnNotesReceivable.Caption" xml:space="preserve">
    <value>Receive Notes</value>
  </data>
  <data name="barBtnNotesReceivable.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="barBtnLoad_Sl_Qoute.Caption" xml:space="preserve">
    <value>Quote</value>
  </data>
  <data name="barBtnLoad_SalesOrder.Caption" xml:space="preserve">
    <value>Sales Order</value>
  </data>
  <data name="barBtnLoad_IC_OutTrns.Caption" xml:space="preserve">
    <value>Outgoing Bill</value>
  </data>
  <data name="barBtnLoad_JO.Caption" xml:space="preserve">
    <value>Job Order</value>
  </data>
  <data name="barBtnLoad_PR_Invoice.Caption" xml:space="preserve">
    <value>Purchase Invoice</value>
  </data>
  <data name="barBtnLoad_IC_Transfer.Caption" xml:space="preserve">
    <value>Transfer Bill</value>
  </data>
  <data name="barBtn_OutTrns.Caption" xml:space="preserve">
    <value>Outgoing Bill</value>
  </data>
  <data name="barBtn_CashNote.Caption" xml:space="preserve">
    <value>Cash Note</value>
  </data>
  <data name="barbtnvisanote.Caption" xml:space="preserve">
    <value>Visa</value>
  </data>
  <data name="barbtn_EditPerm.Caption" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="barbtn_DelPerm.Caption" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="barbtn_PrintPerm.Caption" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="repositoryItemTextEdit1.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repositoryItemTextEdit1.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="bar2.Text" xml:space="preserve">
    <value>Custom 3</value>
  </data>
  <data name="textEdit3.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="textEdit3.EditValue" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="textEdit3.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit3.Location" type="System.Drawing.Point, System.Drawing">
    <value>111, 49</value>
  </data>
  <data name="textEdit3.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit3.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit3.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit3.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit3.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit3.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit3.Size" type="System.Drawing.Size, System.Drawing">
    <value>99, 22</value>
  </data>
  <data name="textEdit3.TabIndex" type="System.Int32, mscorlib">
    <value>287</value>
  </data>
  <data name="&gt;&gt;textEdit3.Name" xml:space="preserve">
    <value>textEdit3</value>
  </data>
  <data name="&gt;&gt;textEdit3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_JOStatus.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_JOStatus.Location" type="System.Drawing.Point, System.Drawing">
    <value>111, 70</value>
  </data>
  <data name="lkp_JOStatus.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_JOStatus.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_JOStatus.Size" type="System.Drawing.Size, System.Drawing">
    <value>99, 20</value>
  </data>
  <data name="lkp_JOStatus.TabIndex" type="System.Int32, mscorlib">
    <value>278</value>
  </data>
  <data name="&gt;&gt;lkp_JOStatus.Name" xml:space="preserve">
    <value>lkp_JOStatus</value>
  </data>
  <data name="&gt;&gt;lkp_JOStatus.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="textEdit1.EditValue" xml:space="preserve">
    <value>Priority</value>
  </data>
  <data name="textEdit1.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit1.Location" type="System.Drawing.Point, System.Drawing">
    <value>211, 49</value>
  </data>
  <data name="textEdit1.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit1.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit1.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit1.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit1.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit1.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit1.Size" type="System.Drawing.Size, System.Drawing">
    <value>99, 22</value>
  </data>
  <data name="textEdit1.TabIndex" type="System.Int32, mscorlib">
    <value>286</value>
  </data>
  <data name="&gt;&gt;textEdit1.Name" xml:space="preserve">
    <value>textEdit1</value>
  </data>
  <data name="&gt;&gt;textEdit1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_JOPriority.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_JOPriority.Location" type="System.Drawing.Point, System.Drawing">
    <value>211, 70</value>
  </data>
  <data name="lkp_JOPriority.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_JOPriority.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_JOPriority.Size" type="System.Drawing.Size, System.Drawing">
    <value>99, 20</value>
  </data>
  <data name="lkp_JOPriority.TabIndex" type="System.Int32, mscorlib">
    <value>277</value>
  </data>
  <data name="&gt;&gt;lkp_JOPriority.Name" xml:space="preserve">
    <value>lkp_JOPriority</value>
  </data>
  <data name="&gt;&gt;lkp_JOPriority.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit5.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="textEdit5.EditValue" xml:space="preserve">
    <value>Sales Employee</value>
  </data>
  <data name="textEdit5.Location" type="System.Drawing.Point, System.Drawing">
    <value>311, 49</value>
  </data>
  <data name="textEdit5.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit5.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit5.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit5.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit5.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit5.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit5.Size" type="System.Drawing.Size, System.Drawing">
    <value>179, 22</value>
  </data>
  <data name="textEdit5.TabIndex" type="System.Int32, mscorlib">
    <value>285</value>
  </data>
  <data name="&gt;&gt;textEdit5.Name" xml:space="preserve">
    <value>textEdit5</value>
  </data>
  <data name="&gt;&gt;textEdit5.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_JOSalesEmp.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_JOSalesEmp.Location" type="System.Drawing.Point, System.Drawing">
    <value>311, 70</value>
  </data>
  <data name="lkp_JOSalesEmp.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_JOSalesEmp.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_JOSalesEmp.Size" type="System.Drawing.Size, System.Drawing">
    <value>179, 20</value>
  </data>
  <data name="lkp_JOSalesEmp.TabIndex" type="System.Int32, mscorlib">
    <value>276</value>
  </data>
  <data name="&gt;&gt;lkp_JOSalesEmp.Name" xml:space="preserve">
    <value>lkp_JOSalesEmp</value>
  </data>
  <data name="&gt;&gt;lkp_JOSalesEmp.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit8.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="textEdit8.EditValue" xml:space="preserve">
    <value>Department</value>
  </data>
  <data name="textEdit8.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit8.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 49</value>
  </data>
  <data name="textEdit8.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit8.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit8.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit8.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit8.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit8.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit8.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 22</value>
  </data>
  <data name="textEdit8.TabIndex" type="System.Int32, mscorlib">
    <value>284</value>
  </data>
  <data name="&gt;&gt;textEdit8.Name" xml:space="preserve">
    <value>textEdit8</value>
  </data>
  <data name="&gt;&gt;textEdit8.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_JODept.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_JODept.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 70</value>
  </data>
  <data name="lkp_JODept.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_JODept.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_JODept.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 20</value>
  </data>
  <data name="lkp_JODept.TabIndex" type="System.Int32, mscorlib">
    <value>279</value>
  </data>
  <data name="&gt;&gt;lkp_JODept.Name" xml:space="preserve">
    <value>lkp_JODept</value>
  </data>
  <data name="&gt;&gt;lkp_JODept.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit4.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="textEdit4.EditValue" xml:space="preserve">
    <value>Job</value>
  </data>
  <data name="textEdit4.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit4.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 5</value>
  </data>
  <data name="textEdit4.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit4.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit4.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit4.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit4.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit4.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit4.Size" type="System.Drawing.Size, System.Drawing">
    <value>205, 22</value>
  </data>
  <data name="textEdit4.TabIndex" type="System.Int32, mscorlib">
    <value>279</value>
  </data>
  <data name="&gt;&gt;textEdit4.Name" xml:space="preserve">
    <value>textEdit4</value>
  </data>
  <data name="&gt;&gt;textEdit4.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_JOJob.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_JOJob.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 26</value>
  </data>
  <data name="txt_JOJob.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_JOJob.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_JOJob.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_JOJob.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_JOJob.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_JOJob.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_JOJob.Size" type="System.Drawing.Size, System.Drawing">
    <value>205, 20</value>
  </data>
  <data name="txt_JOJob.TabIndex" type="System.Int32, mscorlib">
    <value>275</value>
  </data>
  <data name="&gt;&gt;txt_JOJob.Name" xml:space="preserve">
    <value>txt_JOJob</value>
  </data>
  <data name="&gt;&gt;txt_JOJob.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="textEdit2.EditValue" xml:space="preserve">
    <value>Delivery Date</value>
  </data>
  <data name="textEdit2.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit2.Location" type="System.Drawing.Point, System.Drawing">
    <value>211, 5</value>
  </data>
  <data name="textEdit2.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit2.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit2.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit2.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit2.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit2.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit2.Size" type="System.Drawing.Size, System.Drawing">
    <value>99, 22</value>
  </data>
  <data name="textEdit2.TabIndex" type="System.Int32, mscorlib">
    <value>278</value>
  </data>
  <data name="&gt;&gt;textEdit2.Name" xml:space="preserve">
    <value>textEdit2</value>
  </data>
  <data name="&gt;&gt;textEdit2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_JODeliveryDate.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_JODeliveryDate.EditValue" type="System.DateTime, mscorlib">
    <value>2015-07-08</value>
  </data>
  <data name="txt_JODeliveryDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>211, 26</value>
  </data>
  <data name="txt_JODeliveryDate.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_JODeliveryDate.Properties.CalendarTimeProperties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_JODeliveryDate.Properties.CalendarTimeProperties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_JODeliveryDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_JODeliveryDate.Properties.CalendarTimeProperties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_JODeliveryDate.Properties.CalendarTimeProperties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_JODeliveryDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_JODeliveryDate.Properties.CalendarTimeProperties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_JODeliveryDate.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_JODeliveryDate.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_JODeliveryDate.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_JODeliveryDate.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_JODeliveryDate.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_JODeliveryDate.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_JODeliveryDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>99, 20</value>
  </data>
  <data name="txt_JODeliveryDate.TabIndex" type="System.Int32, mscorlib">
    <value>274</value>
  </data>
  <data name="&gt;&gt;txt_JODeliveryDate.Name" xml:space="preserve">
    <value>txt_JODeliveryDate</value>
  </data>
  <data name="&gt;&gt;txt_JODeliveryDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.DateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit7.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="textEdit7.EditValue" xml:space="preserve">
    <value>JO Code</value>
  </data>
  <data name="textEdit7.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit7.Location" type="System.Drawing.Point, System.Drawing">
    <value>411, 5</value>
  </data>
  <data name="textEdit7.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit7.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit7.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit7.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit7.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit7.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit7.Size" type="System.Drawing.Size, System.Drawing">
    <value>79, 22</value>
  </data>
  <data name="textEdit7.TabIndex" type="System.Int32, mscorlib">
    <value>277</value>
  </data>
  <data name="&gt;&gt;textEdit7.Name" xml:space="preserve">
    <value>textEdit7</value>
  </data>
  <data name="&gt;&gt;textEdit7.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="textEdit6.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="textEdit6.EditValue" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="textEdit6.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit6.Location" type="System.Drawing.Point, System.Drawing">
    <value>311, 5</value>
  </data>
  <data name="textEdit6.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit6.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit6.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit6.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit6.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit6.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit6.Size" type="System.Drawing.Size, System.Drawing">
    <value>99, 22</value>
  </data>
  <data name="textEdit6.TabIndex" type="System.Int32, mscorlib">
    <value>276</value>
  </data>
  <data name="&gt;&gt;textEdit6.Name" xml:space="preserve">
    <value>textEdit6</value>
  </data>
  <data name="&gt;&gt;textEdit6.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_JORegDate.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_JORegDate.EditValue" type="System.DateTime, mscorlib">
    <value>2015-07-08</value>
  </data>
  <data name="txt_JORegDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>311, 26</value>
  </data>
  <data name="txt_JORegDate.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_JORegDate.Properties.CalendarTimeProperties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_JORegDate.Properties.CalendarTimeProperties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_JORegDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_JORegDate.Properties.CalendarTimeProperties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_JORegDate.Properties.CalendarTimeProperties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_JORegDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_JORegDate.Properties.CalendarTimeProperties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_JORegDate.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_JORegDate.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_JORegDate.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_JORegDate.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_JORegDate.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_JORegDate.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_JORegDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>99, 20</value>
  </data>
  <data name="txt_JORegDate.TabIndex" type="System.Int32, mscorlib">
    <value>273</value>
  </data>
  <data name="&gt;&gt;txt_JORegDate.Name" xml:space="preserve">
    <value>txt_JORegDate</value>
  </data>
  <data name="&gt;&gt;txt_JORegDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.DateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_JOCode.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_JOCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>411, 26</value>
  </data>
  <data name="txt_JOCode.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_JOCode.Properties.Mask.EditMask" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_JOCode.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_JOCode.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_JOCode.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_JOCode.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_JOCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>79, 20</value>
  </data>
  <data name="txt_JOCode.TabIndex" type="System.Int32, mscorlib">
    <value>272</value>
  </data>
  <data name="&gt;&gt;txt_JOCode.Name" xml:space="preserve">
    <value>txt_JOCode</value>
  </data>
  <data name="&gt;&gt;txt_JOCode.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <metadata name="timer1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>492, 17</value>
  </metadata>
</root>