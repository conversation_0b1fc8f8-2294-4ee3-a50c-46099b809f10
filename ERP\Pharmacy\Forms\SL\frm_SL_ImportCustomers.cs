﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Linq;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;
using DAL.Res;
using System.Data.OleDb;
using System.IO;
using ExcelDataReader;

namespace Pharmacy.Forms
{

    public partial class btnJournal : DevExpress.XtraEditors.XtraForm
    {
        DataTable dt = new DataTable();
        List<IC_Item> lstItems = new List<IC_Item>();
        List<IC_Category> lstCategories = new List<IC_Category>();
        ERPDataContext DB = new ERPDataContext();
        DataTable dtUOM = new DataTable();

        List<DAL.IC_UOM> uom_list;

        public btnJournal()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();

            if (Shared.IsEnglish)
                RTL.LTRLayout(this);
        }

        private void frm_SL_ImportCustomers_Load(object sender, EventArgs e)
        {
            uom_list = DB.IC_UOMs.ToList();
        }

        private void btn_Openfile_Click(object sender, EventArgs e)
        {
            OpenFileDialog ofd = new OpenFileDialog();
            ofd.Filter = "Excel File(*.xls)|*.xls|Excel File(*.xlsx)|*.xlsx";
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    //DataTable dt = ErpUtils.exceldata(ofd.FileName);

                    FileStream stream = File.Open(ofd.FileName, FileMode.Open, FileAccess.Read);

                    IExcelDataReader excelReader = ExcelReaderFactory.CreateOpenXmlReader(stream);
                    DataSet result = excelReader.AsDataSet();

                    progressBar1.Value = 0;
                    progressBar1.Maximum = result.Tables[0].Rows.Count;

                    int count = 1;
                    List<SL_Customer> csList = new List<SL_Customer>();
                    List<string> regions = new List<string>();
                    foreach (DataRow d in result.Tables[0].Rows)
                    {
                        try
                        {
                            SL_Customer customer = new SL_Customer();
                            if (string.IsNullOrEmpty(Convert.ToString(d[0])) || Convert.ToString(d[0]) == "اسم العميل")
                                continue;
                            customer.CusCode = count++;
                            customer.CusNameAr = Convert.ToString(d[0]);
                            customer.CusNameEn = string.IsNullOrEmpty(Convert.ToString(d[1])) ? "" : Convert.ToString(d[1]);
                            customer.Tel = string.IsNullOrEmpty(Convert.ToString(d[2])) ? null : Convert.ToString(d[2]);
                            customer.Mobile = string.IsNullOrEmpty(Convert.ToString(d[3])) ? null : Convert.ToString(d[3]);
                            customer.Address = string.IsNullOrEmpty(Convert.ToString(d[4])) ? null : Convert.ToString(d[4]);
                            customer.City = string.IsNullOrEmpty(Convert.ToString(d[5])) ? null : Convert.ToString(d[5]);
                            //if (!string.IsNullOrEmpty(Convert.ToString(d[5])))
                            //{
                            //    if (regions.Contains(Convert.ToString(d[5])))
                            //    {
                            //        customer.IdRegion = DB.SL_CustomerRegions.Where(x => x.RegionName == Convert.ToString(d[5])).Select(x => x.IdRegion).FirstOrDefault();
                            //    }
                            //    else
                            //    {
                            //        regions.Add(Convert.ToString(d[5]));
                            //        SL_CustomerRegion region = new SL_CustomerRegion();
                            //        region.RegionName = Convert.ToString(d[5]);
                            //        DB.SL_CustomerRegions.InsertOnSubmit(region);
                            //        DB.SubmitChanges();
                            //        customer.IdRegion = region.IdRegion;
                            //    }
                            //}
                            customer.Email = string.IsNullOrEmpty(Convert.ToString(d[6])) ? null : Convert.ToString(d[6]);
                            customer.Fax = string.IsNullOrEmpty(Convert.ToString(d[7])) ? null : Convert.ToString(d[7]);
                            customer.Zip = string.IsNullOrEmpty(Convert.ToString(d[8])) ? null : Convert.ToString(d[8]);
                            customer.Shipping = string.IsNullOrEmpty(Convert.ToString(d[9])) ? null : Convert.ToString(d[9]);
                            if (!string.IsNullOrEmpty(Convert.ToString(d[10])))
                                customer.csType = (Convert.ToString(d[10]) == "طبيعي" ? 0 : (Convert.ToString(d[10]) == "اعتباري" ? 1 : 2));
                            customer.Representative = string.IsNullOrEmpty(Convert.ToString(d[11])) ? null : Convert.ToString(d[11]);
                            //if (!string.IsNullOrEmpty(Convert.ToString(d[11])))
                            //{
                            //    customer.SalesEmpId = DB.HR_Employees.Where(x => x.EmpName == Convert.ToString(d[11])).Select(x => x.EmpId).FirstOrDefault();
                            //}

                            customer.IdNumber = string.IsNullOrEmpty(Convert.ToString(d[12])) ? null : Convert.ToString(d[12]);

                            customer.TradeRegistry = string.IsNullOrEmpty(Convert.ToString(d[13])) ? null : Convert.ToString(d[13]);

                            customer.TaxCardNumber = string.IsNullOrEmpty(Convert.ToString(d[14])) ? null : Convert.ToString(d[14]);
                            customer.TaxFileNumber = string.IsNullOrEmpty(Convert.ToString(d[15])) ? null : Convert.ToString(d[15]);
                            customer.Is_Active = true;
                            customer.Is_Blocked = false;
                            if (string.IsNullOrEmpty(Convert.ToString(d[16])))
                                customer.MaxCredit = 0;
                            else
                                customer.MaxCredit = Convert.ToDecimal(d[16]);

                            customer.BankName = string.IsNullOrEmpty(Convert.ToString(d[17])) ? null : Convert.ToString(d[17]);
                            customer.BankAccNum = string.IsNullOrEmpty(Convert.ToString(d[18])) ? null : Convert.ToString(d[18]);
                            if (string.IsNullOrEmpty(Convert.ToString(d[19])))
                                customer.IsTaxable = false;
                            else
                                customer.IsTaxable = (Convert.ToString(d[19]) == "ضريبي" ? true : false);

                            customer.HasSeparateAccount = true;
                            csList.Add(customer);
                            progressBar1.Value++;

                            customer.Governate = string.IsNullOrEmpty(Convert.ToString(d[21])) ? null : Convert.ToString(d[21]);
                            //==================//
                            customer.BuildingNumber = string.IsNullOrEmpty(Convert.ToString(d[22])) ? null : Convert.ToString(d[22]);
                            //==================//
                            if (!string.IsNullOrEmpty(Convert.ToString(d[23])))
                            {
                                var country = DB.HR_Countries.Where(x => x.CountryName == Convert.ToString(d[20])).FirstOrDefault();
                                customer.CountryId = Convert.ToInt32(country.CountryId);
                            }
                            else
                            {
                               
                                customer.CountryId = DB.HR_Countries.FirstOrDefault() ==null ?1 : DB.HR_Countries.FirstOrDefault().CountryId;
                            }
                            //==================//
                            customer.Street = string.IsNullOrEmpty(Convert.ToString(d[23])) ? null : Convert.ToString(d[23]);
                        }
                        catch (Exception ex)
                        {

                            XtraMessageBox.Show(
                                string.Format("حدث خطأ أثناء في تحميل العملاء")
                                , "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }

                    }
                    excelReader.Close();
                    DB.SL_Customers.InsertAllOnSubmit(csList);
                    DB.SubmitChanges();

                    //foreach (var c in csList)
                    //{
                    //    c.AccountId = HelperAcc.Get_CustomerAccount_Id(c.CustomerId, Shared.st_Store.CustomersAcc.Value, Shared.st_Store.CustomersAcc);
                    //}


                    //  DB.SubmitChanges();
                    XtraMessageBox.Show(
                        string.Format("تم تحميل العملاء بشكل سليم")
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {

                    XtraMessageBox.Show(
                        string.Format("حدث خطأ أثناء في تحميل الملف")
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
                return;
        }

        private void btnOpenVendorFile_Click(object sender, EventArgs e)
        {

            OpenFileDialog ofd = new OpenFileDialog();
            ofd.Filter = "Excel File(*.xls)|*.xls|Excel File(*.xlsx)|*.xlsx";
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    //DataTable dt = ErpUtils.exceldata(ofd.FileName);

                    FileStream stream = File.Open(ofd.FileName, FileMode.Open, FileAccess.Read);

                    IExcelDataReader excelReader = ExcelReaderFactory.CreateOpenXmlReader(stream);
                    DataSet result = excelReader.AsDataSet();

                    progressBar1.Value = 0;
                    progressBar1.Maximum = result.Tables[0].Rows.Count;

                    int count = 1;
                    List<PR_Vendor> venList = new List<PR_Vendor>();
                    foreach (DataRow d in result.Tables[0].Rows)
                    {
                        try
                        {
                            PR_Vendor vendor = new PR_Vendor();
                            if (string.IsNullOrEmpty(Convert.ToString(d[0])) || Convert.ToString(d[0]) == "اسم المورد")
                                continue;
                            vendor.VenCode = count++;
                            vendor.VenNameAr = Convert.ToString(d[0]);
                            vendor.VenNameEn = string.IsNullOrEmpty(Convert.ToString(d[1])) ? "" : Convert.ToString(d[1]);
                            vendor.Tel = string.IsNullOrEmpty(Convert.ToString(d[2])) ? null : Convert.ToString(d[2]);
                            vendor.Mobile = string.IsNullOrEmpty(Convert.ToString(d[3])) ? null : Convert.ToString(d[3]);
                            vendor.Address = string.IsNullOrEmpty(Convert.ToString(d[4])) ? null : Convert.ToString(d[4]);
                            vendor.City = string.IsNullOrEmpty(Convert.ToString(d[5])) ? null : Convert.ToString(d[5]);
                            vendor.Email = string.IsNullOrEmpty(Convert.ToString(d[6])) ? null : Convert.ToString(d[6]);
                            vendor.Fax = string.IsNullOrEmpty(Convert.ToString(d[7])) ? null : Convert.ToString(d[7]);
                            vendor.Zip = string.IsNullOrEmpty(Convert.ToString(d[8])) ? null : Convert.ToString(d[8]);
                            vendor.Shipping = string.IsNullOrEmpty(Convert.ToString(d[9])) ? null : Convert.ToString(d[9]);
                            
                            vendor.TradeRegistry = string.IsNullOrEmpty(Convert.ToString(d[10])) ? null : Convert.ToString(d[10]);
                            vendor.TaxCardNumber = string.IsNullOrEmpty(Convert.ToString(d[11])) ? null : Convert.ToString(d[11]);
                            vendor.TaxFileNumber = string.IsNullOrEmpty(Convert.ToString(d[12])) ? null : Convert.ToString(d[12]);
                            if (string.IsNullOrEmpty(Convert.ToString(d[13])))
                                vendor.MaxCredit = 0;
                            else
                                vendor.MaxCredit = Convert.ToDecimal(d[13]);

                            vendor.BankName = string.IsNullOrEmpty(Convert.ToString(d[14])) ? null : Convert.ToString(d[14]);
                            vendor.BankAccNum = string.IsNullOrEmpty(Convert.ToString(d[15])) ? null : Convert.ToString(d[15]);
                            if (string.IsNullOrEmpty(Convert.ToString(d[16])))
                                vendor.IsTaxable = false;
                            else
                                vendor.IsTaxable = (Convert.ToString(d[16]) == "ضريبي" ? true : false);
                            vendor.HasSeparateAccount = true;
                            venList.Add(vendor);
                            progressBar1.Value++;
                        }
                        catch (Exception ex)
                        {

                            XtraMessageBox.Show(
                                string.Format("حدث خطأ أثناء في تحميل الموردين")
                                , "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }

                    }
                    excelReader.Close();
                    DB.PR_Vendors.InsertAllOnSubmit(venList);
                    DB.SubmitChanges();


                    foreach (var v in venList)
                        HelperAcc.Get_VendorAccount_Id(v.VendorId, Shared.st_Store.VendorsAcc.Value, Shared.st_Store.VendorsAcc);

                    XtraMessageBox.Show(
                        string.Format("تم تحميل الموردين بشكل سليم")
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {

                    XtraMessageBox.Show(
                        string.Format("حدث خطأ أثناء في تحميل الملف")
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
                return;
        }

        private void btnImportItems_Click(object sender, EventArgs e)
        {

            OpenFileDialog ofd = new OpenFileDialog();
            ofd.Filter = "Excel File(*.xls)|*.xls|Excel File(*.xlsx)|*.xlsx";
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    FileStream stream = File.Open(ofd.FileName, FileMode.Open, FileAccess.Read);

                    IExcelDataReader excelReader = ExcelReaderFactory.CreateOpenXmlReader(stream);
                    DataSet result = excelReader.AsDataSet();

                    progressBar1.Value = 0;
                    progressBar1.Maximum = result.Tables[0].Rows.Count;

                    int count = 1;
                    List<IC_Item> ItemList = new List<IC_Item>();
                    List<string> CategoryList = new List<string>();
                    List<string> CompanyList = new List<string>();
                    List<string> UOMList = new List<string>();
                    foreach (DataRow d in result.Tables[0].Rows)
                    {
                        try
                        {
                            count++;
                            if (string.IsNullOrEmpty(Convert.ToString(d[2])) || Convert.ToString(d[2]) == "اسم الصنف")
                                continue;

                            #region Category
                            CategoryList = DB.IC_Categories.Select(x => x.CategoryNameAr).ToList();
                            string category = string.IsNullOrEmpty(Convert.ToString(d[4])) ? "الفئة العامة" : Convert.ToString(d[4]);
                            IC_Category cat = new IC_Category();
                            if (!CategoryList.Contains(category))
                            {
                                cat.CategoryNameAr = category;
                                cat.CategoryNameEn = string.Empty;
                                cat.CatNumber = (DB.IC_Categories.Count() + 1).ToString();
                                cat.Level = 2;
                                cat.ParentId = 2;
                                //cat.CategoryId = (DB.IC_Categories.Count() + 1);
                                DB.IC_Categories.InsertOnSubmit(cat);
                                DB.SubmitChanges();
                                CategoryList.Add(category);
                            }
                            else
                            {
                                cat = DB.IC_Categories.Where(c => c.CategoryNameAr == category).FirstOrDefault();
                            }
                            #endregion

                            #region Company
                            CompanyList = DB.IC_Companies.Select(x => x.CompanyNameAr).ToList();
                            string company = string.IsNullOrEmpty(Convert.ToString(d[5])) ? "شركه عامه" : Convert.ToString(d[5]);
                            IC_Company com = new IC_Company();
                            if (!CompanyList.Contains(company))
                            {
                                com.CompanyNameAr = company;
                                com.CompanyNameEn = string.Empty;
                                com.CompanyCode = (DB.IC_Companies.Count() + 1);
                                com.Address = string.Empty;
                                com.Tel = string.Empty;
                                DB.IC_Companies.InsertOnSubmit(com);
                                DB.SubmitChanges();
                                CompanyList.Add(company);
                            }
                            else
                            {
                                com = DB.IC_Companies.Where(c => c.CompanyNameAr == company).FirstOrDefault();
                            }
                            #endregion

                            #region UOM
                            // small uom
                            UOMList = DB.IC_UOMs.Select(x => x.UOM).ToList();
                            IC_UOM uom = new IC_UOM();
                            string um = string.IsNullOrEmpty(Convert.ToString(d[7])) ? "وحدة" : Convert.ToString(d[7]);
                            if (!UOMList.Contains(um))
                            {
                                uom.UOM = um;
                                DB.IC_UOMs.InsertOnSubmit(uom);
                               // UOMList.Add(um);
                                DB.SubmitChanges();
                            }
                            d[7] = um;
                            // mediuom uom
                            if (!string.IsNullOrEmpty(Convert.ToString(d[9])))
                            {
                               if(!UOMList.Contains(Convert.ToString(d[9])))
                               {
                                    IC_UOM uom2 = new IC_UOM();
                                    uom2.UOM = Convert.ToString(d[9]);
                                    DB.IC_UOMs.InsertOnSubmit(uom2);
                                   // UOMList.Add(Convert.ToString(d[9]));
                                    DB.SubmitChanges();

                               }
                             
                            }
                            // large uom
                            if (!string.IsNullOrEmpty(Convert.ToString(d[12])))
                            {
                                if (!UOMList.Contains(Convert.ToString(d[12])))
                                {
                                    IC_UOM uom3 = new IC_UOM();
                                    uom3.UOM = Convert.ToString(d[12]);
                                    DB.IC_UOMs.InsertOnSubmit(uom3);
                                   // UOMList.Add(Convert.ToString(d[12]));
                                    DB.SubmitChanges();
                                }
                            }
                            #endregion

                            DB.SubmitChanges();

                            #region Item
                            IC_Item item = new IC_Item();

                            item.ItemCode1 = string.IsNullOrEmpty(Convert.ToString(d[0])) ? (DB.IC_Items.OrderByDescending(x => x.ItemId).Select(x=>x.ItemCode1).FirstOrDefault() + 1) : int.Parse(Convert.ToString(d[0]));
                            item.ItemCode2 = string.IsNullOrEmpty(Convert.ToString(d[1])) ? null : Convert.ToString(d[1]);
                            item.ItemNameAr = Convert.ToString(d[2]);
                            item.ItemNameEn = string.IsNullOrEmpty(Convert.ToString(d[3])) ? "" : Convert.ToString(d[3]);
                            item.Category = cat.CategoryId;
                            item.Company = com.CompanyId;
                            item.PurchasePrice = string.IsNullOrEmpty(Convert.ToString(d[6])) ? 0 : decimal.Parse(Convert.ToString(d[6]));
                            item.SmallUOM = (byte)DB.IC_UOMs.Where(u => u.UOM == Convert.ToString(d[7])).Select(u => u.UOMId).FirstOrDefault();
                            item.SmallUOMPrice = string.IsNullOrEmpty(Convert.ToString(d[8])) ? 0 : decimal.Parse(Convert.ToString(d[8]));

                            if (!string.IsNullOrEmpty(Convert.ToString(d[9])))
                            {
                                item.MediumUOM = (byte)DB.IC_UOMs.Where(u => u.UOM == Convert.ToString(d[9])).Select(u => u.UOMId).FirstOrDefault();
                                item.MediumUOMPrice = string.IsNullOrEmpty(Convert.ToString(d[11])) ? 0 : decimal.Parse(Convert.ToString(d[11]));
                                item.MediumUOMFactor = string.IsNullOrEmpty(Convert.ToString(d[10])) ? "1" : Convert.ToString(d[10]);
                            }

                            if (!string.IsNullOrEmpty(Convert.ToString(d[12])))
                            {
                                item.LargeUOM = (byte)DB.IC_UOMs.Where(u => u.UOM == Convert.ToString(d[12])).Select(u => u.UOMId).FirstOrDefault();
                                item.LargeUOMPrice = string.IsNullOrEmpty(Convert.ToString(d[14])) ? 0 : decimal.Parse(Convert.ToString(d[14]));
                                item.LargeUOMFactor = string.IsNullOrEmpty(Convert.ToString(d[13])) ? "1" : Convert.ToString(d[13]);
                            }

                            item.Length = string.IsNullOrEmpty(Convert.ToString(d[15])) ? 1 : decimal.Parse(Convert.ToString(d[15]));
                            item.Width = string.IsNullOrEmpty(Convert.ToString(d[16])) ? 1 : decimal.Parse(Convert.ToString(d[16]));
                            item.Height = string.IsNullOrEmpty(Convert.ToString(d[17])) ? 1 : decimal.Parse(Convert.ToString(d[17]));

                            if (!string.IsNullOrEmpty(Convert.ToString(d[18])))
                                item.Expiry = (Convert.ToString(d[18]) == "نعم" ? 1 : 0);

                            item.ItemType = (Convert.ToString(d[19]) == "خام" ? (int)ItemType.Inventory : Convert.ToString(d[19]) == "تام" ? (int)ItemType.Assembly : Convert.ToString(d[19]) == "خدمة" ? (int)ItemType.Service : (int)ItemType.Inventory);

                            item.MinQty = string.IsNullOrEmpty(Convert.ToString(d[20])) ? 0 : int.Parse(Convert.ToString(d[20]));
                            item.ReorderLevel = string.IsNullOrEmpty(Convert.ToString(d[21])) ? 0 : int.Parse(Convert.ToString(d[21]));

                            if (string.IsNullOrEmpty(Convert.ToString(d[4]))) 
                            {

                                if (DB.IC_Items.Where(x => x.ItemNameAr == item.ItemNameAr).Count() > 0)
                                    continue;
                            }
                            else
                            {
                                if (DB.IC_Items.Where(x => x.ItemNameAr == item.ItemNameAr && x.Category == item.Category
                                                    && item.Company == x.Company && x.Length == item.Length && x.Width == item.Width
                                                    && x.Height == item.Height && item.ItemCode2 == x.ItemCode2).Count() > 0)
                                    continue;
                            }

                            DB.IC_Items.InsertOnSubmit(item);
                            #endregion

                            DB.SubmitChanges();
                            progressBar1.Value++;
                        }
                        catch (Exception ex)
                        {
                            XtraMessageBox.Show(
                                string.Format("حدث خطأ أثناء في تحميل الأصناف")
                                , "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            break;
                        }

                    }
                    excelReader.Close();
                    DB.IC_Items.InsertAllOnSubmit(ItemList);
                    DB.SubmitChanges();

                    XtraMessageBox.Show(
                        string.Format("تم تحميل الأصناف بشكل سليم")
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {

                    XtraMessageBox.Show(
                        string.Format("حدث خطأ أثناء في تحميل الملف")
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
                return;
        }

        public void OpenBalance()
        {

            OpenFileDialog ofd = new OpenFileDialog();
            ofd.Filter = "Excel File(*.xls)|*.xls|Excel File(*.xlsx)|*.xlsx";
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    FileStream stream = File.Open(ofd.FileName, FileMode.Open, FileAccess.Read);

                    IExcelDataReader excelReader = ExcelReaderFactory.CreateOpenXmlReader(stream);
                    DataSet result = excelReader.AsDataSet();

                    progressBar1.Value = 0;
                    progressBar1.Maximum = result.Tables[0].Rows.Count;

                    int count = 1;
                    List<string> Stores = new List<string>();
                    int IntrnsId = 0;
                    
                    foreach (DataRow d in result.Tables[0].Rows)
                    {
                        try
                        {
                            count++;
                            if (string.IsNullOrEmpty(Convert.ToString(d[2])) || Convert.ToString(d[2]) == "اسم الصنف" || string.IsNullOrEmpty(d[22].ToString()))
                                continue;
                            if (!Stores.Contains(Convert.ToString(d[23])))
                            {
                                Stores.Add(Convert.ToString(d[23]));
                                IC_InTrn ob = new IC_InTrn();
                                var store = DB.IC_Stores.Where(s => s.StoreNameAr == Convert.ToString(d[23])).FirstOrDefault();
                                if (store == null) continue;
                                var lastNumber = (from x in DB.IC_InTrns
                                                  where x.IsOpenBalance == true
                                                  join s in DB.IC_Stores on x.StoreId equals s.StoreId
                                                  where Shared.st_Store.AutoInvSerialForStore == true ? x.StoreId == Convert.ToInt32(store.StoreId) : true    //مستوى المخزن
                                                  where Shared.st_Store.AutoInvSerialForStore == null ? (int?)store.ParentId != null ?
                                                  s.ParentId.HasValue && s.ParentId.Value == (int?)store.ParentId : x.StoreId == Convert.ToInt32(store.StoreId) : true//مستوى الفرع
                                                  orderby x.InTrnsDate descending
                                                  orderby x.InTrnsDate descending
                                                  select x.InTrnsCode).FirstOrDefault();
                                ob.InTrnsCode = MyHelper.GetNextNumberInString(lastNumber);
                                ob.StoreId = store.StoreId;
                                ob.IsOpenBalance = true;
                                ob.InTrnsDate = MyHelper.Get_Server_DateTime();
                                ob.UserId = Shared.UserId;
                                ob.TotalPurchasePrice = 0;
                                ob.DetailIdBrcodPrntd = false;
                                ob.CrncId = 0;
                                ob.CrncRate = 1;
                                DB.IC_InTrns.InsertOnSubmit(ob);
                                DB.SubmitChanges();
                                IntrnsId = ob.InTrnsId;
                            }

                            IC_InTrnsDetail intrnsDetail = new IC_InTrnsDetail();
                            intrnsDetail.InTrnsId = IntrnsId;
                            IC_Item item = new IC_Item();
                            var cat = DB.IC_Categories.Where(x => x.CategoryNameAr == Convert.ToString(d[4])).FirstOrDefault();
                            if (cat != null)
                            {
                                var com = DB.IC_Companies.Where(x => x.CompanyNameAr == Convert.ToString(d[5])).FirstOrDefault();
                                if (com != null)
                                {
                                    if (string.IsNullOrEmpty(d[1].ToString()))
                                    {
                                        item = DB.IC_Items.Where(x => x.ItemNameAr == Convert.ToString(d[2]) && x.Category == cat.CategoryId && x.Company == com.CompanyId).FirstOrDefault();
                                        if (item == null) continue;
                                    }
                                    else
                                    {
                                        item = DB.IC_Items.Where(x => x.ItemCode2 == d[1].ToString() && x.ItemNameAr == Convert.ToString(d[2]) && x.Category == cat.CategoryId && x.Company == com.CompanyId).FirstOrDefault();
                                        if (item == null) continue;
                                    }
                                }
                                else
                                {
                                    if (string.IsNullOrEmpty(d[1].ToString()))
                                    {
                                        item = DB.IC_Items.Where(x => x.ItemNameAr == Convert.ToString(d[2]) && x.Category == cat.CategoryId).FirstOrDefault();
                                        if (item == null) continue;
                                    }
                                    else
                                    {
                                        item = DB.IC_Items.Where(x => x.ItemCode2 == d[1].ToString() && x.ItemNameAr == Convert.ToString(d[2]) && x.Category == cat.CategoryId).FirstOrDefault();
                                        if (item == null) continue;
                                    }
                                }
                            }
                            else
                            {
                                if (string.IsNullOrEmpty(d[1].ToString()))
                                {
                                    item = DB.IC_Items.Where(x => x.ItemNameAr == Convert.ToString(d[2])).FirstOrDefault();
                                    if (item == null) continue;
                                }
                                else
                                {
                                    item = DB.IC_Items.Where(x => x.ItemNameAr == Convert.ToString(d[2])).Where(x=>x.ItemCode2 == d[1].ToString()).FirstOrDefault();
                                    if (item == null) continue;
                                }
                            }
                            
                            intrnsDetail.ItemId = item.ItemId;
                            intrnsDetail.UOMIndex = item.DfltPrchsUomIndx;
                            intrnsDetail.UOMId = item.DfltPrchsUomIndx;
                            intrnsDetail.Height= string.IsNullOrEmpty(d[17].ToString()) ? 1 : Convert.ToDecimal(d[17].ToString());
                            intrnsDetail.Length= string.IsNullOrEmpty(d[15].ToString()) ? 1 : Convert.ToDecimal(d[15].ToString());
                            intrnsDetail.Width= string.IsNullOrEmpty(d[16].ToString()) ? 1 : Convert.ToDecimal(d[16].ToString());
                            intrnsDetail.Qty = intrnsDetail.PiecesCount = Convert.ToDecimal(d[22].ToString());
                            intrnsDetail.PurchasePrice = 0;
                            intrnsDetail.SellPrice = 0;
                            if (result.Tables[0].Columns.Count>24)
                            intrnsDetail.Batch = string.IsNullOrEmpty(d[24].ToString()) ? null : d[24].ToString();
                            intrnsDetail.TotalPurchasePrice = 0;
                            DB.IC_InTrnsDetails.InsertOnSubmit(intrnsDetail);

                            DB.SubmitChanges();
                            progressBar1.Value++;
                        }
                        catch (Exception ex)
                        {
                            XtraMessageBox.Show(
                                string.Format("حدث خطأ أثناء في تحميل الأصناف")
                                , "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            break;
                        }

                    }
                    excelReader.Close();
                    DB.SubmitChanges();

                    XtraMessageBox.Show(
                        string.Format("تم تحميل الأصناف بشكل سليم")
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {

                    XtraMessageBox.Show(
                        string.Format("حدث خطأ أثناء في تحميل الملف")
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
                return;
        }

        private void barbtnOpenBalance_Click(object sender, EventArgs e)
        {
            OpenBalance();
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            OpenJournals();
        }

        public void OpenJournals()
        {

            OpenFileDialog ofd = new OpenFileDialog();
            ofd.Filter = "Excel File(*.xls)|*.xls|Excel File(*.xlsx)|*.xlsx";
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    FileStream stream = File.Open(ofd.FileName, FileMode.Open, FileAccess.Read);

                    IExcelDataReader excelReader = ExcelReaderFactory.CreateOpenXmlReader(stream);
                    DataSet result = excelReader.AsDataSet();

                    progressBar1.Value = 0;
                    progressBar1.Maximum = result.Tables[0].Rows.Count;

                    int count = 1;
                    List<string> Stores = new List<string>();
                    string defaultserial = "", serial = "";
                    foreach (DataRow d in result.Tables[0].Rows)
                    {
                        try
                        {
                            count++;

                            if (string.IsNullOrEmpty(Convert.ToString(d[0])) || Convert.ToString(d[0]) == "Serial")
                                continue;
                            ACC_Journal jrnl = new ACC_Journal();
                            if (string.IsNullOrEmpty(serial) || serial != Convert.ToString(d[0]))
                            {
                                jrnl.SourceId = jrnl.JCode = DB.ACC_Journals.Count() + 1;
                                serial = Convert.ToString(d[0]);
                                defaultserial = string.Format("{0}{1}", jrnl.JCode, Convert.ToString(d[0]));
                                jrnl.InsertDate = Convert.ToDateTime(Convert.ToString(d[1]));
                                jrnl.InsertUser = Shared.UserId;
                                jrnl.JNumber = defaultserial;
                                jrnl.CrncId = string.IsNullOrEmpty(Convert.ToString(d[6])) ? -1 : Convert.ToInt32(d[6]);
                                jrnl.CrncRate = string.IsNullOrEmpty(Convert.ToString(d[6])) ? 1 : Convert.ToInt32(d[7]);
                                jrnl.IsPosted = true;
                                jrnl.ProcessId = 0;
                                jrnl.Monthly_Code = HelperAcc.Get_Jornal_Monthly_Code(jrnl.InsertDate, jrnl.ProcessId);
                                jrnl.JNotes = Convert.ToString(d[2]);
                                DB.ACC_Journals.InsertOnSubmit(jrnl);
                                DB.SubmitChanges();
                            }
                            else
                            {
                                jrnl = DB.ACC_Journals.Where(x => x.JNumber == defaultserial).FirstOrDefault();
                            }
                            if (jrnl == null) continue;

                            ACC_JournalDetail jDetail = new ACC_JournalDetail();
                            int AccountId = DB.ACC_Accounts.Where(x => x.AcNumber == Convert.ToString(d[3])).Select(x => x.AccountId).FirstOrDefault();
                            if(AccountId > 0)
                            {
                                jDetail.AccountId = AccountId;
                                jDetail.CostCenter = DB.ACC_CostCenters.Where(x=>x.CostCenterName == d[8].ToString()).Select(x=>x.CostCenterId).FirstOrDefault();
                                jDetail.Credit = string.IsNullOrEmpty(d[5].ToString()) ? 0 : Convert.ToDecimal(d[5].ToString());
                                jDetail.Debit = string.IsNullOrEmpty(d[4].ToString()) ? 0 : Convert.ToDecimal(d[4].ToString());
                                jDetail.CrncId = jrnl.CrncId;
                                jDetail.CrncRate = jrnl.CrncRate;
                                jDetail.JournalId = jrnl.JournalId;
                                jDetail.Notes = Convert.ToString(d[9]);

                                DB.ACC_JournalDetails.InsertOnSubmit(jDetail);
                            }

                            DB.SubmitChanges();
                            progressBar1.Value++;
                        }
                        catch (Exception ex)
                        {
                            XtraMessageBox.Show(
                                string.Format("حدث خطأ أثناء في تحميل القيود" + --count)
                                , "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            break;
                        }

                    }
                    excelReader.Close();
                    DB.SubmitChanges();

                    XtraMessageBox.Show(
                        string.Format("تم تحميل القيود بشكل سليم")
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {

                    XtraMessageBox.Show(
                        string.Format("حدث خطأ أثناء في تحميل الملف")
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
                return;
        }

        private void btn_OpenBalance_Click(object sender, EventArgs e)
        {
            OpenBalanceFromXlsx();
            //OpenCSBalanceFromXlsx();
            //OpenAccountsFromXlsx();
        }
        
        public void OpenBalanceFromXlsx()
        {

            OpenFileDialog ofd = new OpenFileDialog();
            ofd.Filter = "Excel File(*.xls)|*.xls|Excel File(*.xlsx)|*.xlsx";
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    FileStream stream = File.Open(ofd.FileName, FileMode.Open, FileAccess.Read);

                    IExcelDataReader excelReader = ExcelReaderFactory.CreateOpenXmlReader(stream);
                    DataSet result = excelReader.AsDataSet();

                    progressBar1.Value = 0;
                    progressBar1.Maximum = result.Tables[0].Rows.Count;

                    int count = 1;
                    List<string> Stores = new List<string>();
                    int IntrnsId = 0;

                    foreach (DataRow d in result.Tables[0].Rows)
                    {
                        try
                        {
                            count++;
                            if (string.IsNullOrEmpty(Convert.ToString(d[2])) || Convert.ToString(d[8]) == "المخزن" || string.IsNullOrEmpty(d[8].ToString()))
                                continue;
                            if (!Stores.Contains(Convert.ToString(d[8])))
                            {
                                Stores.Add(Convert.ToString(d[8]));
                                IC_InTrn ob = new IC_InTrn();
                                var store = DB.IC_Stores.Where(s => s.StoreNameAr == Convert.ToString(d[8])).FirstOrDefault();
                                if (store == null) continue;
                                var lastNumber = (from x in DB.IC_InTrns
                                                  where x.IsOpenBalance == true
                                                  join s in DB.IC_Stores on x.StoreId equals s.StoreId
                                                  where Shared.st_Store.AutoInvSerialForStore == true ? x.StoreId == Convert.ToInt32(store.StoreId) : true    //مستوى المخزن
                                                  where Shared.st_Store.AutoInvSerialForStore == null ? (int?)store.ParentId != null ?
                                                  s.ParentId.HasValue && s.ParentId.Value == (int?)store.ParentId : x.StoreId == Convert.ToInt32(store.StoreId) : true//مستوى الفرع
                                                  orderby x.InTrnsDate descending
                                                  orderby x.InTrnsDate descending
                                                  select x.InTrnsCode).FirstOrDefault();
                                ob.InTrnsCode = MyHelper.GetNextNumberInString(lastNumber);
                                ob.StoreId = store.StoreId;
                                ob.IsOpenBalance = true;
                                ob.InTrnsDate = MyHelper.Get_Server_DateTime();
                                ob.UserId = Shared.UserId;
                                ob.TotalPurchasePrice = 0;
                                ob.DetailIdBrcodPrntd = false;
                                ob.CrncId = 0;
                                ob.CrncRate = 1;
                                DB.IC_InTrns.InsertOnSubmit(ob);
                                DB.SubmitChanges();
                                IntrnsId = ob.InTrnsId;
                            }

                            IC_InTrnsDetail intrnsDetail = new IC_InTrnsDetail();
                            intrnsDetail.InTrnsId = IntrnsId;
                            IC_Item item = new IC_Item();
                            var cat = DB.IC_Categories.Where(x => x.CategoryNameAr == Convert.ToString(d[4])).FirstOrDefault();

                            item = DB.IC_Items.Where(x => x.ItemNameAr == Convert.ToString(d[2])).FirstOrDefault();
                            if (item == null) continue;

                            intrnsDetail.ItemId = item.ItemId;
                            intrnsDetail.UOMIndex = item.DfltPrchsUomIndx;
                            intrnsDetail.UOMId = item.DfltPrchsUomIndx;
                            intrnsDetail.Qty = intrnsDetail.PiecesCount = Convert.ToDecimal(d[3].ToString());
                            intrnsDetail.PurchasePrice = Convert.ToDecimal(d[4].ToString());
                            intrnsDetail.TotalPurchasePrice = intrnsDetail.Qty * intrnsDetail.PurchasePrice;
                            intrnsDetail.SellPrice = 0;
                            intrnsDetail.Batch = string.IsNullOrEmpty(d[7].ToString()) ? null : d[7].ToString();
                            intrnsDetail.TotalPurchasePrice = 0;
                            DB.IC_InTrnsDetails.InsertOnSubmit(intrnsDetail);

                            DB.SubmitChanges();
                            progressBar1.Value++;
                        }
                        catch (Exception ex)
                        {
                            XtraMessageBox.Show(
                                string.Format("حدث خطأ أثناء في تحميل الأصناف")
                                , "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            break;
                        }

                    }
                    excelReader.Close();
                    DB.SubmitChanges();

                    XtraMessageBox.Show(
                        string.Format("تم تحميل الأصناف بشكل سليم")
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {

                    XtraMessageBox.Show(
                        string.Format("حدث خطأ أثناء في تحميل الملف")
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
                return;
        }
        
        public void OpenAccountsFromXlsx()
        {

            OpenFileDialog ofd = new OpenFileDialog();
            ofd.Filter = "Excel File(*.xls)|*.xls|Excel File(*.xlsx)|*.xlsx";
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    FileStream stream = File.Open(ofd.FileName, FileMode.Open, FileAccess.Read);

                    IExcelDataReader excelReader = ExcelReaderFactory.CreateOpenXmlReader(stream);
                    DataSet result = excelReader.AsDataSet();

                    progressBar1.Value = 0;
                    progressBar1.Maximum = result.Tables[0].Rows.Count;

                    int count = 1;

                    foreach (DataRow d in result.Tables[0].Rows)
                    {
                        try
                        {
                            count++;
                            if (string.IsNullOrEmpty(Convert.ToString(d[0])) || string.IsNullOrEmpty(Convert.ToString(d[1])))
                                continue;

                            string parentName = Convert.ToString(d[0]);
                           ACC_Account parentAcc = DB.ACC_Accounts.Where(x => x.AcNameAr == parentName).FirstOrDefault();
                            if(parentAcc != null)
                            {
                                ACC_Account ac = new ACC_Account();
                                ac.AcNameAr = Convert.ToString(d[1]);
                                ac.AllowEdit = true;
                                ac.AllowChild = true;

                                ac.ParentActId = parentAcc.AccountId;
                                ac.Level = parentAcc.Level + 1;
                                ac.AcNumber = HelperAcc.AccNumGenerated(parentAcc);

                                ac.AcType = parentAcc.AcType;
                                DB.ACC_Accounts.InsertOnSubmit(ac);
                            DB.SubmitChanges();
                            }
                            progressBar1.Value++;
                        }
                        catch (Exception ex)
                        {
                            XtraMessageBox.Show(
                                string.Format("حدث خطأ أثناء في تحميل الأصناف")
                                , "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            break;
                        }

                    }
                    excelReader.Close();
                    DB.SubmitChanges();

                    XtraMessageBox.Show(
                        string.Format("تم تحميل الأصناف بشكل سليم")
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {

                    XtraMessageBox.Show(
                        string.Format("حدث خطأ أثناء في تحميل الملف")
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
                return;
        }
        
        public void OpenCSBalanceFromXlsx()
        {

            OpenFileDialog ofd = new OpenFileDialog();
            ofd.Filter = "Excel File(*.xls)|*.xls|Excel File(*.xlsx)|*.xlsx";
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    FileStream stream = File.Open(ofd.FileName, FileMode.Open, FileAccess.Read);

                    IExcelDataReader excelReader = ExcelReaderFactory.CreateOpenXmlReader(stream);
                    DataSet result = excelReader.AsDataSet();

                    progressBar1.Value = 0;
                    progressBar1.Maximum = result.Tables[0].Rows.Count;

                    ACC_Journal jrnl = new ACC_Journal();
                    jrnl.CrncId = 0;
                    jrnl.CrncRate = 1;
                    jrnl.BookId = DB.ACC_Journals.Count()+10;
                    jrnl.SourceId = 0;
                    jrnl.JCode = jrnl.BookId.GetValueOrDefault();
                    jrnl.JNotes = "رصيد العملاء الافتتاحى";
                    jrnl.JNumber = jrnl.BookId.ToString();
                    jrnl.ProcessId = 11;
                    jrnl.StoreId = DB.IC_Stores.Select(x => x.StoreId).FirstOrDefault();
                    jrnl.InsertUser = Shared.UserId;
                    jrnl.InsertDate = MyHelper.Get_Server_DateTime();
                    DB.ACC_Journals.InsertOnSubmit(jrnl);
                    DB.SubmitChanges();

                    int count = 1;
                    foreach (DataRow d in result.Tables[0].Rows)
                    {
                        try
                        {
                            count++;
                            if (result.Tables[0].Rows.IndexOf(d) == 0) { continue; }
                            if (Convert.ToDecimal(d[1]) != 0)
                            {
                                ACC_JournalDetail jdetail = new ACC_JournalDetail();
                                int accountId = DB.SL_Customers.Where(x => x.CusNameAr == Convert.ToString(d[0])).Select(x => x.AccountId).FirstOrDefault().GetValueOrDefault(0);
                                if (accountId > 0)
                                {
                                    jdetail.AccountId = accountId;
                                    jdetail.CostCenter = null;
                                    if (Convert.ToDecimal(d[1]) < 0)
                                        jdetail.Credit = Convert.ToDecimal(d[1]) * -1;
                                    else jdetail.Credit = 0;
                                    jdetail.CrncId = 0;
                                    jdetail.CrncRate = 1;
                                    if (Convert.ToDecimal(d[1]) > 0)
                                        jdetail.Debit = Convert.ToDecimal(d[1]);
                                    else jdetail.Debit = 0;
                                    jdetail.JournalId = jrnl.JournalId;
                                    jdetail.Notes = string.Format(@"رصيد افتتاحى {0}", Convert.ToString(d[0]));
                                    DB.ACC_JournalDetails.InsertOnSubmit(jdetail);
                                    DB.SubmitChanges();
                                }
                            }
                            
                            progressBar1.Value++;
                        }
                        catch (Exception ex)
                        {
                            XtraMessageBox.Show(
                                string.Format("حدث خطأ أثناء في تحميل الأصناف")
                                , "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            break;
                        }

                    }
                    excelReader.Close();
                    DB.SubmitChanges();

                    XtraMessageBox.Show(
                        string.Format("تم تحميل الأصناف بشكل سليم")
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {

                    XtraMessageBox.Show(
                        string.Format("حدث خطأ أثناء في تحميل الملف")
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
                return;
        }
        
        public void OpenSalesInvoicesFromXlsx()
        {

            OpenFileDialog ofd = new OpenFileDialog();
            ofd.Filter = "Excel File(*.xls)|*.xls|Excel File(*.xlsx)|*.xlsx";
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    FileStream stream = File.Open(ofd.FileName, FileMode.Open, FileAccess.Read);

                    IExcelDataReader excelReader = ExcelReaderFactory.CreateOpenXmlReader(stream);
                    DataSet result = excelReader.AsDataSet();

                    progressBar1.Value = 0;
                    progressBar1.Maximum = result.Tables[0].Rows.Count;

                    int count = 1;
                    string invCode = "";
                    int invId = 0;
                    decimal totalInvoice = 0;


                    foreach (DataRow d in result.Tables[0].Rows)
                    {
                        try
                        {
                            count++;
                            if (string.IsNullOrEmpty(Convert.ToString(d[0])) || Convert.ToString(d[0]) == @"رقم الفاتورة")
                                continue;
                            if (invCode == "" || invCode != Convert.ToString(d[0]))
                            {
                                SL_Invoice inv = new SL_Invoice();
                                // invoice Code
                                inv.InvoiceCode = Convert.ToString(d[0]);
                                // Customer
                                int csId = DB.SL_Customers.Where(x => x.CusNameAr == Convert.ToString(d[1])).Select(x => x.CustomerId).FirstOrDefault();
                                if (csId > 0)
                                {
                                    inv.CustomerId = csId;
                                }
                                else { continue; }
                                // Invoice Date
                                if (string.IsNullOrEmpty(Convert.ToString(d[2])))
                                    inv.InvoiceDate = MyHelper.Get_Server_DateTime();
                                else inv.InvoiceDate = Convert.ToDateTime(Convert.ToString(d[2]));
                                // Store
                                int storeId = DB.IC_Stores.Where(x => x.StoreNameAr == Convert.ToString(d[3])).Select(x => x.StoreId).FirstOrDefault();
                                if (storeId > 0)
                                {
                                    inv.StoreId = storeId;
                                }
                                else { continue; }
                                // CostCenter
                                if (!string.IsNullOrEmpty(Convert.ToString(d[4])))
                                {
                                    int ccId = DB.ACC_CostCenters.Where(x => x.CostCenterName == Convert.ToString(d[4])).Select(x => x.CostCenterId).FirstOrDefault();
                                    if (ccId > 0)
                                    {
                                        inv.CostCenterId = ccId;
                                    }
                                }
                                // Paid
                                if (!string.IsNullOrEmpty(Convert.ToString(d[8])))
                                {
                                    inv.Paid = Convert.ToDecimal(d[8]);
                                }
                                else { inv.Paid = 0; }

                                inv.Net = 0;
                                inv.JornalId = 0;

                                
                                DB.SL_Invoices.InsertOnSubmit(inv);
                                DB.SubmitChanges();
                                invId = inv.SL_InvoiceId;
                            }
                            else
                            {
                                frm_SL_Invoice frmsl = new frm_SL_Invoice(); 
                                frmsl.invoiceId = invId;
                                frmsl.LoadInvoice();
                                frmsl.Save_Invoice();
                                frmsl.Close();
                            }

                            {
                                if (string.IsNullOrEmpty(Convert.ToString(d[9]))) continue;

                                IC_Item Item = DB.IC_Items.Where(x => x.ItemNameAr == Convert.ToString(d[9])).FirstOrDefault();
                                if (Item != null)
                                {
                                    SL_InvoiceDetail detail = new SL_InvoiceDetail();
                                    detail.SL_InvoiceId = invId;
                                    detail.ItemId = Item.ItemId;
                                    detail.UOMIndex = Item.DfltSellUomIndx;
                                    MyHelper.GetUOMs(Item, dtUOM, uom_list);
                                    detail.UOMId = Convert.ToInt32(dtUOM.Rows[Item.DfltSellUomIndx]["UomId"]);
                                    if (string.IsNullOrEmpty(Convert.ToString(d[10])) || Convert.ToString(d[10]) == "0") continue;
                                    detail.Qty = Convert.ToDecimal(d[10]);

                                    detail.Length = string.IsNullOrEmpty(Convert.ToString(d[11])) ? Item.Length : decimal.Parse(Convert.ToString(d[11]));
                                    detail.Width = string.IsNullOrEmpty(Convert.ToString(d[12])) ? Item.Width : decimal.Parse(Convert.ToString(d[12]));
                                    detail.Height = string.IsNullOrEmpty(Convert.ToString(d[13])) ? Item.Height : decimal.Parse(Convert.ToString(d[13]));

                                    detail.SellPrice = string.IsNullOrEmpty(Convert.ToString(d[14])) ? 0 : decimal.Parse(Convert.ToString(d[14]));
                                    
                                    #region Discount on Item
                                    if (!string.IsNullOrEmpty(Convert.ToString(d[15])))
                                    {
                                        detail.DiscountRatio = string.IsNullOrEmpty(Convert.ToString(d[15])) ? 0 : decimal.Parse(Convert.ToString(d[15]));
                                        detail.DiscountValue = (detail.SellPrice * detail.Qty * detail.DiscountRatio) / 100;
                                    }
                                    else if (!string.IsNullOrEmpty(Convert.ToString(d[16])) || Convert.ToString(d[16]) != "0")
                                    {
                                        detail.DiscountValue = decimal.Parse(Convert.ToString(d[16]));
                                        detail.DiscountValue = (detail.SellPrice * detail.Qty / detail.DiscountValue) * 100;

                                    }
                                    else
                                    {
                                        detail.DiscountRatio = 0;
                                        detail.DiscountValue = 0;
                                    }
                                    #endregion

                                    #region Tax on Item

                                    #endregion

                                    totalInvoice += (detail.SellPrice * detail.Qty) - detail.DiscountValue;
                                    DB.SL_InvoiceDetails.InsertOnSubmit(detail);
                                    DB.SubmitChanges();
                                }
                                else { continue; }
                            }
                            progressBar1.Value++;
                        }
                        catch (Exception ex)
                        {
                            XtraMessageBox.Show(
                                string.Format("حدث خطأ أثناء في تحميل الأصناف")
                                , "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            break;
                        }

                    }
                    excelReader.Close();
                    DB.SubmitChanges();

                    XtraMessageBox.Show(
                        string.Format("تم تحميل الأصناف بشكل سليم")
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {

                    XtraMessageBox.Show(
                        string.Format("حدث خطأ أثناء في تحميل الملف")
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
                return;
        }

        

    }
}