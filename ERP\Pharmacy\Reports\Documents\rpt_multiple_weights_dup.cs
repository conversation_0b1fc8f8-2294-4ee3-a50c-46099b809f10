using System;
using System.Data;
using System.Data.Linq;
using System.Linq;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using DAL;

namespace Reports
{
    public partial class rpt_multiple_weights_dup : DevExpress.XtraReports.UI.XtraReport
    {
        DataTable dt;
        public rpt_multiple_weights_dup(DataTable dt_Weights)
        {
            InitializeComponent();

            this.dt = dt_Weights;
            this.DataSource = dt_Weights.AsEnumerable().GroupBy(r=>r.Field<String>("item")).Distinct().ToList();
            lbl_Item.DataBindings.Add("Text", this.DataSource, "Key");
        }

        private void xrSubreport2_BeforePrint(object sender, System.Drawing.Printing.PrintEventArgs e)
        {
            string item = Convert.ToString(GetCurrentColumnValue("Key"));
            xrSubreport2.ReportSource = new Reports.rpt_IC_Multiple_Weights_Sub(item, dt);
        }
    }
}
