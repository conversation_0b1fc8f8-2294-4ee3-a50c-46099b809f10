﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Linq;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;
using DAL.Res;
using System.Data.OleDb;

namespace Pharmacy.Forms
{

    public partial class frm_IC_ImportItems : DevExpress.XtraEditors.XtraForm
    {
        DataTable dt = new DataTable();
        List<IC_Item> lstItems = new List<IC_Item>();
        List<IC_Category> lstCategories = new List<IC_Category>();
        public frm_IC_ImportItems()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();

            if (Shared.IsEnglish)
                RTL.LTRLayout(this);
        }

        private void frm_IC_ImportItems_Load(object sender, EventArgs e)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            if (Shared.st_Store.ExpireDate == false)
                chkIsExpire.Enabled = false;

            dtInvoiceDate.DateTime = DateTime.Now;

            lstItems = DB.IC_Items.ToList();

            #region Get Stores
            int defaultStoreId = 0;
            var stores_table = MyHelper.Get_Stores(true, out defaultStoreId, Shared.user.UserChangeStore, Shared.user.DefaultStore,
                Shared.UserId);
            lkpStore.Properties.DataSource = stores_table;

            lkpStore.Properties.DisplayMember = "StoreNameAr";
            lkpStore.Properties.ValueMember = "StoreId";
            lkpStore.EditValue = defaultStoreId;
            #endregion

            lstCategories = MyHelper.GetChildCategoriesList();

            lkpCategory.Properties.DisplayMember = "CategoryNameAr";
            lkpCategory.Properties.ValueMember = "CategoryId";
            if (lstCategories.FirstOrDefault() != null)
                lkpCategory.EditValue = lstCategories.FirstOrDefault().CategoryId;
            lkpCategory.Properties.DataSource = lstCategories;

            var companies = (from i in DB.IC_Companies
                             select i).ToList();
            lkpComp.Properties.DisplayMember = "CompanyNameAr";
            lkpComp.Properties.ValueMember = "CompanyId";
            if (companies.FirstOrDefault() != null)
                lkpComp.EditValue = companies.FirstOrDefault().CompanyId;
            lkpComp.Properties.DataSource = companies;
        }

        private void btn_Openfile_Click(object sender, EventArgs e)
        {
            OpenFileDialog ofd = new OpenFileDialog();
            ofd.Filter = "Excel File(*.xls)|*.xls|Excel File(*.xlsx)|*.xlsx";
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    dt = ErpUtils.exceldata(ofd.FileName);
                    if (dt != null)
                    {
                        gridView1.Columns.Clear();

                        gridControl1.DataSource = dt;
                    }
                    else
                        return;
                }
                catch
                { }
            }
            else
                return;
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            ERPDataContext DB = new ERPDataContext();

            #region Validate Data
            foreach (DataRow dr in dt.Rows)
            {
                if (dr.RowState == DataRowState.Deleted)
                    continue;

                if (chkAutoCode1.Checked == false)
                {
                    if (dr["Code1"] == null || dr["Code1"] == DBNull.Value || dr["Code1"].ToString() == string.Empty)
                    {
                        MessageBox.Show("there's an item with no Code1: " + dr["ItemName"].ToString());
                        return;
                    }

                    int code1 = 0;
                    if (Int32.TryParse(dr["Code1"].ToString(), out code1) == false)
                    {
                        MessageBox.Show("there's an item with invalid Code1, code1 must be a number: " + dr["Code1"].ToString());
                        return;
                    }

                    if (lstItems.Where(x => x.ItemCode1 == code1 || x.ItemCode2 == dr["Code1"].ToString()).Count() > 0)
                    {
                        MessageBox.Show("This item is already registered: " + dr["Code1"].ToString());
                        return;
                    }
                }

                if (dr["Code2"] != null && dr["Code2"] != DBNull.Value && dr["Code2"].ToString() != string.Empty)
                {
                    int CodeIntgr = 0;
                    bool Parsed = int.TryParse(dr["Code2"].ToString(), out CodeIntgr);
                    if (Parsed)
                    {
                        if (lstItems.Where(x => x.ItemCode2 == dr["Code2"].ToString() || x.ItemCode1 == Convert.ToInt32(dr["Code2"].ToString())).Count() > 0)
                        {
                            MessageBox.Show("This item is already registered: code2=" + dr["Code2"].ToString());
                            return;
                        }
                    }
                    else
                    {
                        if (lstItems.Where(x => x.ItemCode2 == dr["Code2"].ToString()).Count() > 0)
                        {
                            MessageBox.Show("This item is already registered: code2=" + dr["Code2"].ToString());
                            return;
                        }
                    }

                }

                if (dr["ItemName"] == null || dr["ItemName"] == DBNull.Value || dr["ItemName"].ToString() == string.Empty)
                {
                    MessageBox.Show("there's an item with no name: " + dr["Code1"].ToString());
                    return;
                }
                if (lstItems.Where(x => x.ItemNameAr == dr["ItemName"].ToString()).Count() > 0)
                {
                    MessageBox.Show("This item is already registered: " + dr["ItemName"].ToString());
                    return;
                }

                decimal balance = 0;
                if (decimal.TryParse(dr["Balance"].ToString(), out balance) == false)
                {
                    MessageBox.Show("there's an item with invalid balance, balanae must be a number: " + dr["ItemName"].ToString());
                    return;
                }
                if (balance < 0)
                {
                    MessageBox.Show("balance can't be negative: " + dr["ItemName"].ToString());
                    return;
                }

                decimal PurchasePrice = 0;
                if (decimal.TryParse(dr["PurchasePrice"].ToString(), out PurchasePrice) == false)
                {
                    MessageBox.Show("there's an item with invalid PurchasePrice, PurchasePrice must be a number: " + dr["ItemName"].ToString());
                    return;
                }
                if (PurchasePrice < 0)
                {
                    MessageBox.Show("PurchasePrice can't be negative: " + dr["ItemName"].ToString());
                    return;
                }

                decimal SellPrice = 0;
                if (decimal.TryParse(dr["SellPrice"].ToString(), out SellPrice) == false)
                {
                    MessageBox.Show("there's an item with invalid SellPrice, SellPrice must be a number: " + dr["ItemName"].ToString());
                    return;
                }
                if (SellPrice < 0)
                {
                    MessageBox.Show("SellPrice can't be negative: " + dr["ItemName"].ToString());
                    return;
                }

                if (dr["ReorderLevel"] == null || dr["ReorderLevel"] == DBNull.Value || dr["ReorderLevel"].ToString() == string.Empty)
                {
                    MessageBox.Show("there's an item with no ReorderLevel: " + dr["ItemName"].ToString());
                    return;
                }

                int ReorderLevel = 0;
                if (Int32.TryParse(dr["ReorderLevel"].ToString(), out ReorderLevel) == false)
                {
                    MessageBox.Show("there's an item with invalid ReorderLevel, ReorderLevel must be a number: " + dr["ItemName"].ToString());
                    return;
                }

            }
            #endregion


            #region Insert Items
            List<ItemBalance> lstItemsBalance = new List<ItemBalance>();
            
            foreach (DataRow dr in dt.Rows)
            {
                if (dr.RowState == DataRowState.Deleted)
                    continue;

                #region IC_Item
                IC_Item item = new IC_Item();

                if (chkAutoCode1.Checked)
                {
                    if (Shared.st_Store.EncodeItemsPerCategory == false)
                        item.ItemCode1 = (DB.IC_Items.Select(x => x.ItemCode1).ToList().DefaultIfEmpty(0).Max() + 1);
                    else
                    {
                        item.ItemCode1 = Convert.ToInt32(MyHelper.ItemNumGenerated(
                            lstCategories.Where(x => x.CategoryId == Convert.ToInt32(lkpCategory.EditValue)).First()));
                    }
                }
                else
                    item.ItemCode1 = Convert.ToInt32(dr["Code1"].ToString());
                item.ItemCode2 = dr["Code2"].ToString();
                item.ItemNameAr = dr["ItemName"].ToString();
                item.ItemNameEn = "";

                item.Category = Convert.ToInt32(lkpCategory.EditValue);// lstCategories.Where(x => x.CategoryNameAr.Trim() == dr["Group"].ToString().Trim()).Select(x=> x.CategoryId).First();
                item.Company = Convert.ToInt32(lkpComp.EditValue);

                item.PurchasePrice = Convert.ToDecimal(dr["PurchasePrice"]);
                item.SmallUOM = 1;
                item.SmallUOMPrice = Convert.ToDecimal(dr["SellPrice"]);
                item.ReorderLevel = Convert.ToInt32(dr["ReorderLevel"]);
                item.MaxQty = 0;
                item.MinQty = 0;
                item.ChangePriceMethod = 1;
                item.ChangeSellPrice = true;
                item.IsExpire = chkIsExpire.Checked;
                item.IsDeleted = false;
                item.ItemType = (int)ItemType.Inventory;
                item.PurchaseDiscRatio = 0;
                item.PurchaseTaxValue = 0;
                item.SalesDiscRatio = 0;
                item.SalesTaxRatio = 0;
                item.SalesTaxValue = 0;
                item.PurchaseTaxRatio = 0;
                item.UsedInMarketing = false;
                item.Height = 1;
                item.Width = 1;
                item.Length = 1;
                item.DfltSellUomIndx = 0;
                item.DfltPrchsUomIndx = 0;

                if (Convert.ToInt32(dr["UOM"]) > 1)
                {
                    item.MediumUOM = 2;
                    item.MediumUOMFactor = Convert.ToInt32(dr["UOM"]).ToString();
                    item.MediumUOMFactorDecimal = Convert.ToDecimal(item.MediumUOMFactor );
                    item.MediumUOMPrice = 0;                     
                }

                DB.IC_Items.InsertOnSubmit(item);
                DB.SubmitChanges();

                #endregion

                //decimal openbalance = 0;
                //decimal.TryParse(dr["Balance"] + "", out openbalance);

                //if (openbalance > 0)
                //    lstItemsBalance.Add(new ItemBalance { ItemId = item.ItemId, Balance = openbalance, PurchasePrice = item.PurchasePrice });

            }

            #endregion

            //if (lstItemsBalance.Count > 0)
            //{
            //    #region ic_intrans
            //    IC_InTrn intrn = new IC_InTrn();
            //    intrn.InTrnsCode = MyHelper.GetNextNumberInString(
            //        (from x in DB.IC_InTrns
            //                      where x.IsOpenBalance == true
            //                      join s in DB.IC_Stores on x.StoreId equals s.StoreId
            //                      where Shared.st_Store.AutoInvSerialForStore == true ? x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true    //مستوى المخزن
            //                      where Shared.st_Store.AutoInvSerialForStore == null ? (int?)lkpStore.GetColumnValue("ParentId") != null ?
            //                      s.ParentId.HasValue && s.ParentId.Value == (int?)lkpStore.GetColumnValue("ParentId") : x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true//مستوى الفرع
            //                      orderby x.InTrnsDate descending
            //                      orderby x.InTrnsDate descending
            //                      select x.InTrnsCode).FirstOrDefault());                
                
            //    intrn.StoreId = Convert.ToInt32(lkpStore.EditValue);
            //    intrn.VendorId = null;
            //    intrn.InTrnsDate = dtInvoiceDate.DateTime;
            //    intrn.Notes = "الأرصدة الافتتاحية";
            //    intrn.UserId = Shared.UserId;
            //    intrn.TotalPurchasePrice = 0;
            //    intrn.ProcessId = null;
            //    intrn.SourceId = null;
            //    intrn.IsVendor = null;
            //    intrn.ItemId = null;
            //    intrn.PiecesCount = null;
            //    intrn.TotalQty = 0;
            //    intrn.DetailIdBrcodPrntd = false;
            //    intrn.CrncId = 1;
            //    intrn.CrncRate = 1;
            //    intrn.IsOpenBalance = true;
            //    intrn.JornalId = null;
            //    intrn.AccountId = null;
            //    intrn.CostCenterId = null;

            //    DB.IC_InTrns.InsertOnSubmit(intrn);
            //    DB.SubmitChanges();

            //    #endregion

            //    List<StoreItem> lst_outitems = new List<StoreItem>();
            //    int? vendorId = null;

            //    #region inTrnsDetails
            //    foreach (var i in lstItemsBalance)
            //    {
            //        IC_InTrnsDetail detail = new IC_InTrnsDetail();
            //        detail.InTrnsId = intrn.InTrnsId;
            //        detail.ItemId = i.ItemId;
            //        detail.UOMIndex = 0;
            //        detail.UOMId = 1;
            //        detail.Qty = i.Balance;
            //        detail.PurchasePrice = i.PurchasePrice;
            //        detail.SellPrice = 0;
            //        detail.TotalPurchasePrice = i.Balance * i.PurchasePrice;
            //        detail.Expire = null;
            //        detail.Batch = null;
            //        detail.Serial = null;
            //        detail.Height = 1;
            //        detail.Length = 1;
            //        detail.Width = 1;
            //        detail.PiecesCount = 0;
            //        detail.QC = null;
            //        DB.IC_InTrnsDetails.InsertOnSubmit(detail);

            //        lst_outitems.Add(new StoreItem(Shared.st_Store.MultiplyDimensions, detail.ItemId, (int)ItemType.Inventory, 
            //            detail.InTrnsDetailId, detail.UOMIndex, detail.Qty,
            //        0, 1, detail.Expire, detail.Batch, detail.QC, vendorId, detail.Length.Value, detail.Width.Value, detail.Height.Value,
            //        detail.PiecesCount, null, null, null, null, 0, detail.TotalPurchasePrice, detail, detail.Serial));
            //    }
            //    DB.SubmitChanges();                
            //    #endregion

            //    #region ItemStore
            //    lst_outitems.ForEach(x => x.SourceId = ((IC_InTrnsDetail)x.Source).InTrnsDetailId);
            //    var lst = MyHelper.Add_to_store(intrn.StoreId, (int)Process.OpenBalance, intrn.InTrnsDate, lst_outitems);
            //    DB.IC_ItemStores.InsertAllOnSubmit(lst);
            //    DB.SubmitChanges();
            //    #endregion
            //}

            MessageBox.Show("No.of Imported Items: "+ dt.Rows.Count.ToString() +"\r\n"+
                "No.of Items with Balane:"+ lstItemsBalance.Count.ToString());
            btnSave.Enabled = false;
        }
    }

    class ItemBalance
    {
        public int ItemId { get; set; }
        public decimal Balance { get; set; }
        public decimal PurchasePrice { get; set; }
    }
}