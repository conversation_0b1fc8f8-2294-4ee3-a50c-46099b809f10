﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using Pharmacy.Forms;
using Pharmacy;
using BL;
using DAL;
using System.Data.Linq;
using System.Linq;

namespace Pharmacy
{
    public partial class JOrder : DevExpress.XtraEditors.XtraForm
    {
        ERPDataContext DB = new ERPDataContext();

        public JOrder()
        {
            InitializeComponent();
        }

        private void JOrder_Load(object sender, EventArgs e)
        {
            var xx = DB.JO_JobOrderTemplates.Select(x => x);
            gridControl1.DataSource = xx;
        }

        private void JOrder_FormClosing(object sender, FormClosingEventArgs e)
        {
            DB.SubmitChanges();
        }
    }
}