﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;

using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;

using DevExpress.XtraReports.UI;

namespace Reports
{
    public partial class frm_SL_averagesellingpriceoftheitems : DevExpress.XtraEditors.XtraForm
    {
        public bool UserCanOpen;
        string reportName, dateFilter, otherFilters;

        int itemId1, itemId2, store_id1, store_id2, companyId;
        int MtrxParentId, M1, M2, M3;

        string categoryNum;
        byte FltrTyp_item, FltrTyp_Store, FltrTyp_Company, FltrTyp_Category;
        DateTime date1;

        private void btn_Portrait_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;

            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCategory, false).ShowPreview();

            grdCategory.MinimumSize = new Size(0, 0);
        }

        private void btn_Landscape_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;

            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCategory, true).ShowPreview();

            grdCategory.MinimumSize = new Size(0, 0);
        }

        List<int> lstStores = new List<int>();

        public frm_SL_averagesellingpriceoftheitems(string reportName, string dateFilter, string otherFilters,
            byte fltrTyp_Store, int store_id1, int store_id2,
            byte fltrTyp_company, int companyId,
            byte fltrTyp_category, string categoryNum,
            byte fltrTyp_item, int itemId1, int itemId2, DateTime date1,
            int MtrxParentId, int M1, int M2, int M3)
        {

            UserCanOpen = LoadPrivilege();
            if (UserCanOpen == false)
                return;
            
            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();

            this.reportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;

            this.FltrTyp_Store = fltrTyp_Store;
            this.FltrTyp_Company = fltrTyp_company;
            this.FltrTyp_Category = fltrTyp_category;
            this.FltrTyp_item = fltrTyp_item;


            this.itemId1 = itemId1;
            this.itemId2 = itemId2;
            this.store_id1 = store_id1;
            this.store_id2 = store_id2;
            this.companyId = companyId;
            this.categoryNum = categoryNum;

            this.date1 = date1.Date;

            this.MtrxParentId = MtrxParentId;
            this.M1 = M1;
            this.M2 = M2;
            this.M3 = M3;

            getReportHeader();

            //if (Shared.ItemMatrixAvailable == false)
            //{
            //    col_Mtrx1.OptionsColumn.ShowInCustomizationForm =
            //    col_Mtrx2.OptionsColumn.ShowInCustomizationForm =
            //    col_Mtrx3.OptionsColumn.ShowInCustomizationForm =
            //    col_MtrxParentId.OptionsColumn.ShowInCustomizationForm =

            //    col_Mtrx1.Visible =
            //    col_Mtrx2.Visible =
            //    col_Mtrx3.Visible =
            //    col_MtrxParentId.Visible = false;
            //}
            //else
            //{
            //    ERPDataContext DB = new ERPDataContext();
            //    rep_MtrxParent.DataSource = DB.IC_Items.
            //    Where(i => i.ItemType == (int)ItemType.MatrixParent).
            //    Select(x => new { x.ItemId, x.ItemNameAr }).ToList();
            //    rep_MtrxParent.ValueMember = "ItemId";
            //    rep_MtrxParent.DisplayMember = "ItemNameAr";

            //    rep_mtrx.DataSource = DB.IC_MatrixDetails.
            //       Select(x => new { x.MatrixDetailId, x.MDName }).ToList();
            //    rep_mtrx.ValueMember = "MatrixDetailId";
            //    rep_mtrx.DisplayMember = "MDName";

            //}
            LoadData();

            col_PiecesCount.OptionsColumn.ShowInCustomizationForm = Shared.st_Store.PiecesCount;
            col_MediumQty.OptionsColumn.ShowInCustomizationForm =
            col_MUom.OptionsColumn.ShowInCustomizationForm = Shared.st_Store.UseMediumUom;

            col_LargeQty.OptionsColumn.ShowInCustomizationForm =
            col_LUom.OptionsColumn.ShowInCustomizationForm = Shared.st_Store.UseLargeUom;

            //if (Shared.SalesOrderAvailable == false || Shared.st_Store.SalesOrderReserveGood == false)
            //{
            //    col_SalesOrder.OptionsColumn.ShowInCustomizationForm = false;
            //    col_BalancePlusSO.OptionsColumn.ShowInCustomizationForm = false;
            //}

            ReportsUtils.ColumnChooser(grdCategory);
        }

        private void frm_SL_InvoiceList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                ReportsRTL.LTRLayout(this);

            ReportsUtils.Load_Grid_Layout(grdCategory, this.Name.Replace("frm_", "Rpt_"));
            //LoadPrivilege();
            col_PiecesCount.Caption = Shared.IsEnglish ? Shared.st_Store.PiecesCountNameEn : Shared.st_Store.PiecesCountNameAr;
        }

        private void frm_Rep_FormClosing(object sender, FormClosingEventArgs e)
        {
            ReportsUtils.save_Grid_Layout(grdCategory, this.Name.Replace("frm_", "Rpt_"), true);
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Preview_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //grdCategory.MinimumSize = grdCategory.Size;

            //new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
            //        lblFilter.Text.Trim(), "", grdCategory, false).ShowPreview();
            
            //grdCategory.MinimumSize = new Size(0, 0);
        }

        private void barBtnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadData();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grdCategory.MinimumSize = grdCategory.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grdCategory, false, true).ShowPreview();
                grdCategory.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                //lblCompName.Text = comp.CmpNameAr;
                lblReportName.Text = this.Text = reportName;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        void LoadData()
        {
            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var stores = DB.IC_Stores.ToList();
            foreach (var store in stores)
            {
                if (FltrTyp_Store == 2)
                {
                    if (store.StoreId <= store_id2 && store.StoreId >= store_id1)
                    {
                        lstStores.Add(store.StoreId);
                    }
                }
                else if (FltrTyp_Store == 0)
                {
                    lstStores.Add(store.StoreId);
                }
                else if (store_id1 > 0 && (store.StoreId == store_id1 || store.ParentId == store_id1))
                    lstStores.Add(store.StoreId);
                //else if (store_id2 > 0 && (store.StoreId == store_id2 || store.ParentId == store_id2))
                //    lstStores.Add(store.StoreId);
            }
            var defaultCategories = DB.IC_User_Categories.Where(a => a.UserId == Shared.UserId).Select(a => a.CategoryId).ToList();
            var data1 = (from d in DB.IC_ItemStores
                         //where d.InsertTime.Date <= date1
                         join t in DB.IC_Items on d.ItemId equals t.ItemId
                         join c in DB.IC_Categories on t.Category equals c.CategoryId
                         where defaultCategories.Count() > 0 ? defaultCategories.Contains(c.CategoryId) : true
                         where FltrTyp_Category == 1 ? c.CatNumber.StartsWith(categoryNum) : true
                    
                        where FltrTyp_item == 1 ? t.ItemId == itemId1 : true
                        where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 != 0) ?
                        t.ItemId >= itemId1 && t.ItemId <= itemId2 : true
                        where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 == 0) ?
                        t.ItemId >= itemId1 : true
                        where (FltrTyp_item == 2 && itemId1 == 0 && itemId2 != 0) ?
                        t.ItemId <= itemId2 : true

                        where lstStores.Count > 0 ? lstStores.Contains(d.StoreId) : true
                         //where fltrTyp_Date == 1 ? d.InsertTime.Date == date1 : true
                         //where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                         //d.InsertTime.Date >= date1 && d.InsertTime.Date <= date2 : true
                         //where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                         //d.InsertTime.Date >= date1 : true
                         //where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                         //d.InsertTime.Date <= date2 : true
                         join s in DB.IC_Stores on d.StoreId equals s.StoreId
                         where d.ProcessId == (int)Process.SellInvoice
                              
                        select new
                        {
                            t.ItemNameAr,
                            d.ItemId,
                            t.Category,
                            c.CategoryNameAr,
                            t.ItemCode1,
                            t.ItemCode2,
                            d.ProcessId,
                            d.SourceId,
                            d.StoreId,
                            s.StoreNameAr,
                            t.SmallUOMPrice,
                            t.LargeUOMPrice,
                            t.MediumUOMPrice,
                            Qty = d.Qty,
                            SellPrice = d.SellPrice,
                            PurchasePrice = d.PurchasePrice,
                            SUom = DB.IC_UOMs.Where(x => x.UOMId == t.SmallUOM).Select(x => x.UOM).FirstOrDefault(),
                            MUom = DB.IC_UOMs.Where(x => x.UOMId == t.MediumUOM).Select(x => x.UOM).FirstOrDefault(),
                            LUom = DB.IC_UOMs.Where(x => x.UOMId == t.LargeUOM).Select(x => x.UOM).FirstOrDefault(),
                            t.MediumUOMFactor,
                            t.LargeUOMFactor,
                        }).OrderBy(x => x.Category).ThenBy(x => x.ItemCode1).ToList();


            var data = (from d in data1
                         group d by new { d.ItemId, d.ItemNameAr, d.ItemCode1, d.ItemCode2, d.CategoryNameAr, d.StoreId } into grp
                         select new
                         {
                             StoreId = grp.Key.StoreId,
                             StoreNameAr=grp.Select(x => x.StoreNameAr).FirstOrDefault(),
                             ItemName = grp.Key.ItemNameAr,
                             ItemId = grp.Key.ItemId,
                             grp.Key.ItemCode1,
                             grp.Key.ItemCode2,
                             ItemNameAr = grp.Key.ItemNameAr,
                             grp.Key.CategoryNameAr,
                             Qty = grp.Select(x => x.Qty).ToList().Sum(),
                             SellPrice = grp.Select(x => x.SellPrice).ToList().Sum(),
                             PurchasePrice = grp.Select(x => x.PurchasePrice).ToList().Sum(),
                             SUom = grp.Select(x => x.SUom).FirstOrDefault(),
                             MUom = grp.Select(x => x.MUom).FirstOrDefault(),
                             LUom = grp.Select(x => x.LUom).FirstOrDefault(),
                             MediumUOMFactor= grp.Select(x => x.MediumUOMFactor).FirstOrDefault(),
                             LargeUOMFactor = grp.Select(x => x.LargeUOMFactor).FirstOrDefault(),
                             MediumUOMPrice = grp.Select(x => x.MediumUOMPrice).FirstOrDefault(),
                             LargeUOMPrice = grp.Select(x => x.LargeUOMPrice).FirstOrDefault(),
                             SmallUOMPrice = grp.Select(x => x.SmallUOMPrice).FirstOrDefault(),

                             // grp.Key.ProcessId,
                             ItemSellPrice = grp.Select(x => x.SellPrice).ToList().Sum() / grp.Select(x => x.Qty).ToList().Sum(),
                             ItemCostPrice = grp.Select(x => x.PurchasePrice).ToList().Sum() / grp.Select(x => x.Qty).ToList().Sum(),
                            }).Where(q => q.Qty != 0).ToList();


            //var data = (from d in DB.IC_ItemStores
            //            where lstStores.Count > 0 ? lstStores.Contains(d.StoreId) : true
            //            where d.InsertTime.Date <= date1

            //            where FltrTyp_item == 1 ? d.ItemId == itemId1 : true
            //            where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 != 0) ?
            //            d.ItemId >= itemId1 && d.ItemId <= itemId2 : true
            //            where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 == 0) ?
            //            d.ItemId >= itemId1 : true
            //            where (FltrTyp_item == 2 && itemId1 == 0 && itemId2 != 0) ?
            //            d.ItemId <= itemId2 : true

            //            where MtrxParentId == 0 ? true : d.ParentItemId == MtrxParentId
            //            where M1 == 0 ? true : d.M1 == M1
            //            where M2 == 0 ? true : d.M2 == M2
            //            where M3 == 0 ? true : d.M3 == M3

            //            //  group d by new { d.ItemId, d.StoreId } into grp

            //            //join t in DB.IC_Items on grp.Key.ItemId equals t.ItemId
            //            //join s in DB.IC_Stores on grp.Key.StoreId equals s.StoreId
            //            join t in DB.IC_Items on d.ItemId equals t.ItemId
            //            join ca in DB.IC_Categories on t.Category equals ca.CategoryId
            //            join c in DB.IC_Categories on t.Category equals c.CategoryId
            //            where FltrTyp_Category == 1 ? c.CatNumber.StartsWith(categoryNum) : true
            //            where FltrTyp_Company == 1 ? t.Company == companyId : true
            //            where d.ProcessId == (int)Process.SellInvoice
            //            select new
            //            {
            //                StoreId = grp.Key.StoreId,
            //                s.StoreNameAr,

            //                ItemId = grp.Key.ItemId,
            //                t.ItemCode1,
            //                t.ItemCode2,
            //                ItemNameAr = t.ItemNameAr,
            //                Category = t.Category,
            //                c.CategoryNameAr,
            //                c.CatNumber,
            //                Qty = grp.Select(x => x.IsInTrns ? x.Qty : x.Qty * -1).Sum(),
            //                SalesOrders = grp.Select(x => x.ProcessId == (int)DAL.Process.SalesOrder ? x.Qty : 0).Sum(),
            //                BalancePlusSO = grp.Select(x => x.IsInTrns ? x.Qty : x.Qty * -1).Sum() +
            //                grp.Select(x => x.ProcessId == (int)DAL.Process.SalesOrder ? x.Qty : 0).Sum(),
            //                PiecesCount = grp.Select(x => x.IsInTrns ? x.PiecesCount : x.PiecesCount * -1).Sum(),

            //                t.MediumUOMFactor,
            //                t.LargeUOMFactor,

            //                SUom = DB.IC_UOMs.Where(x => x.UOMId == t.SmallUOM).Select(x => x.UOM).FirstOrDefault(),
            //                MUom = DB.IC_UOMs.Where(x => x.UOMId == t.MediumUOM).Select(x => x.UOM).FirstOrDefault(),
            //                LUom = DB.IC_UOMs.Where(x => x.UOMId == t.LargeUOM).Select(x => x.UOM).FirstOrDefault(),

            //                t.mtrxParentItem,
            //                t.mtrxAttribute1,
            //                t.mtrxAttribute2,
            //                t.mtrxAttribute3,
            //            }).Where(q => q.Qty != 0 || q.SalesOrders != 0)
            //                .OrderBy(x => x.StoreId).
            //                ThenBy(x => x.CatNumber).
            //                ThenBy(x => x.ItemCode1).ToList();
    

            grdCategory.DataSource = data;
        }

        private void gridView1_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            if (e.ListSourceRowIndex < 0)
                return;


            if (e.Column.FieldName == "colIndex")
                e.Value = e.RowHandle() + 1;
       
            decimal qty = Convert.ToDecimal(bandedGridView1.GetListSourceRowCellValue(e.ListSourceRowIndex, col_Qty));
            decimal Luom = MyHelper.FractionToDouble(bandedGridView1.GetListSourceRowCellValue(e.ListSourceRowIndex, "LargeUOMFactor") + "");
            decimal Muom = MyHelper.FractionToDouble(bandedGridView1.GetListSourceRowCellValue(e.ListSourceRowIndex, "MediumUOMFactor") + "");

            #region Old
            /*
            decimal lqty = 0, mqty = 0, sqty = 0;

            if (Shared.st_Store.UseLargeUom == true)
            {
                if (Luom > 1)
                    lqty = Math.Truncate(qty / Luom);
            }
            else
                lqty = 0;

            qty = qty - lqty * Luom;

            if (Shared.st_Store.UseMediumUom == true)
            {
                if (Muom > 1)
                    mqty = Math.Truncate(qty / Muom);
            }
            else
                mqty = 0;

            qty = qty - mqty * Muom;

            sqty = qty;
            */
            #endregion

            if (e.Column == col_SmallQty)
                e.Value = qty;

            if (e.Column == col_MediumQty && Muom != 1)
                e.Value = qty / Muom;

            if (e.Column == col_LargeQty && Luom != 1)
                e.Value = qty / Luom;
        }


        bool LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.rpt_Ic_ItemsQty).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            return true;
        }
    }
}