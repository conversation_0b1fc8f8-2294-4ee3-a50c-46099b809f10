﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;

namespace Pharmacy.Forms
{
    public partial class frm_IC_Category_old : DevExpress.XtraEditors.XtraForm
    {
        int CatgID, FirstCatgID, LastCatgID;

        string CategoryCode = "0";
        string CategoryNameAr = string.Empty;
        string CategoryNameEn = string.Empty;
        
        FormAction action = FormAction.None;
        UserPriv prvlg;

        public frm_IC_Category_old(int catgID, FormAction action)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            CatgID = catgID;
            this.action = action;
        }

        private void frm_IC_Category_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            LoadPrivilege();
            GetFirstAndLast();

            if (CatgID == 0)
            {
                if (action == FormAction.None)
                {
                    if (LastCatgID == 0 && FirstCatgID == 0)
                    {
                        action = FormAction.Add;
                        NewCat();
                    }
                    else if (LastCatgID >= FirstCatgID)
                    {
                        CatgID = LastCatgID;
                        LoadCat(false);                        
                    }
                }
                else if (action == FormAction.Add)
                    NewCat();
            }
            else
            {
                LoadCat(true);
            }
            //if (Category_ID > 0)
            //    lk_Search.EditValue = Category_ID;
            //else
            //{
            //    barBtnDelete.Enabled = barBtnNew.Enabled = barBtnEdit.Enabled = false;
            //    barBtnSave.Enabled = true;
            //    action = FormAction.Add;

            //    txtCategoryCode.Text =
            //        (new DAL.ERPDataContext().IC_Categories.Select(x => x.CategoryCode).ToList().DefaultIfEmpty(0).Max() + 1).ToString();
            //}
        }

        private void frm_IC_Category_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.PageUp)
            {
                btnPrev.PerformClick();
            }
            if (e.KeyCode == Keys.PageDown)
            {
                btnNext.PerformClick();
            }
        }

        private void frm_IC_Category_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                e.Cancel = true;
        }


        private void btnPrev_Click(object sender, EventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            if (FirstCatgID!= 0)
            {
                if (CatgID == FirstCatgID || CatgID == 0)
                {
                    CatgID = LastCatgID;
                    LoadCat(false);
                }
                else
                {
                    CatgID -= 1;
                    LoadCat(false);
                }
            }
        }

        private void btnNext_Click(object sender, EventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            if (LastCatgID != 0)
            {
                if (CatgID >= LastCatgID)
                {
                    CatgID = FirstCatgID;
                    LoadCat(true);
                }
                else
                {
                    CatgID += 1;
                    LoadCat(true);
                }
            }
        }


        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_List_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_IC_CategoriesList)))
            {
                frm_IC_CategoriesList frm = new frm_IC_CategoriesList();
                frm.BringToFront();
                frm.Show();
            }
            else
                Application.OpenForms["frm_IC_CategoriesList"].BringToFront();

        }

        private void barBtn_Delete_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (action == FormAction.Add)
                return;

            if (CatgID > 0)
            {
                DialogResult DR = XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgDelCat : ResICAr.MsgDelCat//"هل أنت متأكد أنك تريد حذف هذه الفئه"
                    ,
                    Shared.IsEnglish == true ? ResICEn.MsgTWarn : ResICAr.MsgTWarn//"تنبيه"
                    , MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2);
                if (DR == DialogResult.Yes)
                {
                    DAL.ERPDataContext pharm = new DAL.ERPDataContext();
                    var items = (from i in pharm.IC_Items
                                 where i.Category == CatgID
                                 select i).ToList();
                    if (items.Count > 0)
                        XtraMessageBox.Show(
                            Shared.IsEnglish == true ? ResICEn.MsgDelCatDenied : ResICAr.MsgDelCatDenied//"عفواً، يوجد أصناف مرتبطة بهذه الفئة، لا يمكن حذف الفئة"
                            , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    else
                    {
                        var comp = (from c in pharm.IC_Categories
                                    where c.CategoryId == CatgID
                                    select c).SingleOrDefault();
                        pharm.IC_Categories.DeleteOnSubmit(comp);
                        pharm.SubmitChanges();
                        XtraMessageBox.Show(
                            Shared.IsEnglish == true ? ResICEn.MsgDel : ResICAr.MsgDel//"تم الحذف بنجاح"
                            , "", MessageBoxButtons.OK, MessageBoxIcon.Information);

                        GetFirstAndLast();
                        if (LastCatgID == 0 && FirstCatgID == 0) // no records remains
                            Reset();
                        if (CatgID > LastCatgID)//when delete last record
                            btnPrev.PerformClick();
                        else
                            btnNext.PerformClick();
                    }
                }
            }
        }

        private void barBtnNew_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            NewCat();
        }

        private void barBtn_Save_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if(!ValidData())
                return;

            SaveData();
                        
        }


        private void txtCategoryNameEn_Leave(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(txtCategoryNameEn.Text) && action == FormAction.Add)
            {
                DAL.ERPDataContext pharm = new DAL.ERPDataContext();
                var name = (from n in pharm.IC_Categories
                            where n.CategoryNameEn == txtCategoryNameEn.Text
                            select n.CategoryNameEn).Count();
                if (name > 0)
                { XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgNameExist : ResICAr.MsgNameExist,//"هذا الاسم مسجل من قبل",
                    Shared.IsEnglish == true ? ResICEn.MsgTWarn : ResICAr.MsgTWarn//"تنبيه"
                    , MessageBoxButtons.OK, MessageBoxIcon.Warning); txtCategoryNameEn.Focus(); }
            }
        }


        private void NewCat()
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            Reset();
            action = FormAction.Add;
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            CatgID = 
                    new DAL.ERPDataContext().IC_Categories.Select(x => x.CategoryId).ToList().DefaultIfEmpty(0).Max() + 1;
            txtCategoryCode.Text = (new DAL.ERPDataContext().IC_Categories.Select(x => x.CategoryCode).ToList().DefaultIfEmpty(0).Max() + 1).ToString();
            txtCategoryCode.Focus();
        }

        void LoadCat(bool isNext)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            var d = DB.IC_Categories.Where(x => x.CategoryId == CatgID).SingleOrDefault();
            if (d == null)
            {
                if (isNext)
                    CatgID += 1;
                else
                    CatgID -= 1;

                LoadCat(isNext);
            }
            else
            {                
                CatgID = d.CategoryId;                
                txtCategoryCode.Text = d.CategoryCode.ToString();
                txtCategoryNameAr.Text = CategoryNameAr = d.CategoryNameAr;
                txtCategoryNameEn.Text = CategoryNameEn = d.CategoryNameEn;
                
                action = FormAction.Edit;
            }
        }
                       
        void Reset()
        {
            txtCategoryCode.Text = "0";
            txtCategoryNameAr.Text = CategoryNameAr = string.Empty;
            txtCategoryNameEn.Text = CategoryNameEn = string.Empty;                        
        }

        void GetFirstAndLast()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            FirstCatgID = DB.IC_Categories.Select(d => d.CategoryId).FirstOrDefault();
            LastCatgID = DB.IC_Categories.Select(d => d.CategoryId).ToList().DefaultIfEmpty(0).Max();
        }

        DialogResult ChangesMade()
        {
            if (CatgID > 0 &&
            (
            txtCategoryNameAr.Text.Trim() != CategoryNameAr.Trim() ||
            txtCategoryNameEn.Text != CategoryNameEn.Trim()            
            )
            )
            {
                DialogResult r = XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgDataModified : ResICAr.MsgDataModified//"لقد قمت بعمل بعض التغييرات, هل تريد الحفظ أولا "
                    , "", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                if (r == DialogResult.Yes)
                {
                    if (!ValidData())
                        return DialogResult.Cancel;

                    SaveData();
                    return r;
                }
                else if (r == DialogResult.No)
                {
                    // no thing made, continue closing or do next or do previous
                    return r;
                }
                else if (r == DialogResult.Cancel)
                {
                    //cancel form action
                    return r;
                }
            }
            return DialogResult.No;
        }

        private void SaveData()
        {

            DAL.ERPDataContext pharm = new DAL.ERPDataContext();

            if (action == FormAction.Add)
            {
                IC_Category cateog = new IC_Category();
                cateog.CategoryCode = Convert.ToInt32(txtCategoryCode.EditValue);
                cateog.CategoryNameAr = txtCategoryNameAr.Text;
                cateog.CategoryNameEn = txtCategoryNameEn.Text;
               
                pharm.IC_Categories.InsertOnSubmit(cateog);
                pharm.SubmitChanges();
                CatgID = cateog.CategoryId;

                GetFirstAndLast();
            }
            else if (action == FormAction.Edit)
            {
                var cateog = (from i in pharm.IC_Categories
                              where i.CategoryId == CatgID
                              select i).SingleOrDefault();
                cateog.CategoryCode = Convert.ToInt32(txtCategoryCode.EditValue);
                cateog.CategoryNameAr = txtCategoryNameAr.Text;
                cateog.CategoryNameEn = txtCategoryNameEn.Text;
                pharm.SubmitChanges();
            }

            XtraMessageBox.Show(
                Shared.IsEnglish == true ? ResICEn.MsgSave : ResICAr.MsgSave//"تم الحفظ بنجاح"
                , "", MessageBoxButtons.OK, MessageBoxIcon.Information);

            CategoryCode = txtCategoryCode.Text;
            CategoryNameAr = txtCategoryNameAr.Text.Trim();
            CategoryNameEn = txtCategoryNameEn.Text.Trim();

            action = FormAction.Edit;
        }

        private bool ValidData()
        {
            if (action == FormAction.Add)
            {
                if (prvlg != null && !prvlg.CanAdd)
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgPrvNew : ResICAr.MsgPrvNew//"عفوا, انت لا تمتلك صلاحية انشاء بيان جديد"
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            if (action == FormAction.Edit)
            {
                if (prvlg != null && !prvlg.CanEdit)
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgPrvEdit : ResICAr.MsgPrvEdit//"عفوا, انت لا تمتلك صلاحية تعديل هذا البيان"
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }

            if (Validate_Category_Code() == false)
                return false;
            if (Validate_Category_ArName() == false)
                return false;

            return true;
        }                     

        private bool Validate_Category_ArName()
        {
            try
            {
                DAL.ERPDataContext pharm = new DAL.ERPDataContext();
                if (string.IsNullOrEmpty(txtCategoryNameAr.Text))
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgCatName : ResICAr.MsgCatName//"يرجى إدخال اسم الفئة"
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCategoryNameAr.Focus();
                    return false;
                }
                if (action == FormAction.Add)
                {
                    var name = (from n in pharm.IC_Categories
                                where n.CategoryNameAr == txtCategoryNameAr.Text
                                select n.CategoryNameAr).Count();
                    if (name > 0)
                    {
                        XtraMessageBox.Show(
                            Shared.IsEnglish == true ? ResICEn.MsgNameExist : ResICAr.MsgNameExist//"هذا الاسم مسجل من قبل"
                            , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtCategoryNameAr.Focus();
                        return false;
                    }
                }
            }
            catch
            {
                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgIncorrectData : ResICAr.MsgIncorrectData//"تأكد من صحة البيانات"
                    , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCategoryNameAr.Focus();
                return false;
            }
            return true;
        }

        private bool Validate_Category_Code()
        {
            try
            {
                DAL.ERPDataContext pharm = new DAL.ERPDataContext();
                if (string.IsNullOrEmpty(txtCategoryCode.Text))
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgCatCode : ResICAr.MsgCatCode//"يرجى إدخال كود الفئة"
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCategoryCode.Focus();
                    return false;
                }

                int comp_code = Convert.ToInt32(txtCategoryCode.EditValue);
                if (comp_code == 0)
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgZeroCode : ResICAr.MsgZeroCode//"الكود لايمكن أن يساوي صفر"
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCategoryCode.Focus();
                    return false;
                }
                if (comp_code != 0 && action == FormAction.Add)
                {
                    var code_exist = pharm.IC_Categories.Where(c => c.CategoryCode == comp_code).Select(c => c.CategoryCode).Count();
                    if (code_exist > 0)
                    {
                        XtraMessageBox.Show(
                            Shared.IsEnglish == true ? ResICEn.MsgCodeExist : ResICAr.MsgCodeExist//"هذا الكود مسجل من قبل"
                            , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtCategoryCode.Focus();
                        return false;
                    }
                }
            }
            catch
            {
                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgIncorrectData : ResICAr.MsgIncorrectData//"تأكد من صحة البيانات"                    
                    , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCategoryCode.Focus();
                return false;
            }
            return true;
        }

        void LoadPrivilege()
        {
            if (frmMain.LstUserPrvlg != null)
            {
                prvlg = frmMain.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.Category).FirstOrDefault();
                
                if (!prvlg.CanDel)
                    barBtnDelete.Enabled = false;
                if (!prvlg.CanAdd)
                    barBtnNew.Enabled = false;
            }
        }

        private void barBtn_Help_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "الفئات");
        }
    }
}