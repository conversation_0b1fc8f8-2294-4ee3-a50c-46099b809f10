﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.Management;
using System.IO;
using Microsoft.Win32;
using Pharmacy;
using DAL;
using System.Xml;

namespace Pharmacy.Forms
{
    public partial class frmActivation : Form
    {
        public static bool Is_Activated = false;
        int tryCount;

        public frmActivation()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();            
        }

        private void frm_enter_license_code_Load(object sender, EventArgs e)
        {
            txt_HD_BytesNo.Text = GetHardwareSerials().ToString();
        }

        private void btn_activate_app_Click(object sender, EventArgs e)
        {
            tryCount++;

            if (tryCount > 3)
                Application.Exit();

            if (txt_serial_Code.Text.Trim() != string.Empty)
            {
                bool success = Check_Bios_Serial_Validation(Convert.ToDouble(txt_serial_Code.Text));
                if (success == true)
                {
                    MessageBox.Show("Activation done successfully");
                    Is_Activated = true;
                    Shared.TrialVersion = string.Empty;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("Activation Number Is Invalid", "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    Is_Activated = false;
                }
            }
        }

        private void txt_serial_Code_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (!char.IsNumber(e.KeyChar))
            {
                e.Handled = e.KeyChar != (char)Keys.Back;
            }
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            this.Close();
        }


        public double GetHardwareSerials()
        {
            double Num = 0;
            //ManagementObjectSearcher searcherNetwork = new ManagementObjectSearcher("SELECT * FROM  Win32_NetworkAdapter");
            ManagementObjectSearcher searcherBios = new ManagementObjectSearcher("SELECT * FROM  Win32_Bios");
            ManagementObjectSearcher searcherProcessor = new ManagementObjectSearcher("SELECT * FROM  Win32_Processor");

            //foreach (ManagementObject wmi in searcherNetwork.Get())
            //{
            //    try
            //    {
            //        foreach (PropertyData d in wmi.Properties)
            //        {
            //            if (d.Name == "MACAddress")
            //            {
            //                string mac = d.Value.ToString();
            //                long sum = 0;
            //                int index = 1;
            //                foreach (Char ch in mac)
            //                {
            //                    if (Char.IsDigit(ch))
            //                        sum += Convert.ToInt32(ch) * (index * 4);
            //                    else if (Char.IsLetter(ch))
            //                    {
            //                        switch (ch.ToString().ToUpper())
            //                        {
            //                            case "A":
            //                                sum += 15 * (index * 5);
            //                                break;
            //                            case "B":
            //                                sum += 16 * (index * 5);
            //                                break;
            //                            case "C":
            //                                sum += 17 * (index * 5);
            //                                break;
            //                            case "D":
            //                                sum += 18 * (index * 5);
            //                                break;
            //                            case "E":
            //                                sum += 19 * (index * 5);
            //                                break;
            //                            case "F":
            //                                sum += 20 * (index * 5);
            //                                break;
            //                        }
            //                    }
            //                    index += 1;
            //                }
            //                //return sum;
            //                Num += sum;
            //            }
            //        }
            //    }
            //    catch { }
            //}

            foreach (ManagementObject wmi in searcherBios.Get())
            {
                try
                {
                    foreach (PropertyData d in wmi.Properties)
                    {
                        if (d.Name == "BiosCharacteristics")
                        {
                            ushort[] bios = d.Value as ushort[];
                            long sum = 0;
                            int index = 1;
                            foreach (ushort ch in bios)
                            {
                                sum += Convert.ToInt32(ch) * index;
                                index += 1;
                            }
                            Num += sum;
                        }
                        if (d.Name == "SerialNumber")
                        {
                            string biosSerial = d.Value.ToString();
                            long sum = 0;
                            int index = 1;
                            char[] charArr = biosSerial.ToCharArray();
                            foreach (char ch in charArr)
                            {
                                sum += Convert.ToInt32(ch) * index;
                                index += 1;
                            }
                            Num += sum;
                        }
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                }
            }

            foreach (ManagementObject wmi in searcherProcessor.Get())
            {
                try
                {
                    foreach (PropertyData d in wmi.Properties)
                    {
                        if (d.Name == "ProcessorId")
                        {
                            string bios = d.Value.ToString();
                            long sum = 0;
                            int index = 1;
                            foreach (Char ch in bios)
                            {
                                if (Char.IsDigit(ch))
                                    sum += Convert.ToInt32(ch) + (index * 2);
                                else
                                {
                                    sum += Convert.ToInt32((int)ch) + (index * 2);
                                }
                                index += 1;
                            }
                            Num += sum;
                        }
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                }
            }
            return Num;
        }

        //public byte[] Convert_Serial_To_Ascii_Bytes(string SerialNO)
        //{
        //    var ascii = new System.Text.ASCIIEncoding();
        //    return ascii.GetBytes(SerialNO);
        //}

        public double Generate_Serial_Code(double x)
        {
            return Math.Truncate((x * x + 101 / x + 143 * (x / 3)));
        }

        private bool Check_Bios_Serial_Validation(double serial)
        {
            if (Generate_Serial_Code(GetHardwareSerials()) == serial)
            {
                #region old code writes to regitery

                //RegistryKey key = Registry.LocalMachine.OpenSubKey("Software", true);

                //key.CreateSubKey(Application.ProductName);
                //key = key.OpenSubKey(Application.ProductName, true);

                //key.SetValue("RegNumber", Generate_Serial_Code(GetHardwareSerials())); 
                #endregion


                XmlWriterSettings settings = new XmlWriterSettings();
                settings.Indent = true;

                XmlWriter writer = null;
                try
                {
                    string Folder_path = Environment.GetFolderPath(Environment.SpecialFolder.Personal) + "\\GlobalERP";
                    if (!System.IO.Directory.Exists(Folder_path))
                        System.IO.Directory.CreateDirectory(Folder_path);

                    writer = XmlWriter.Create(
                        Environment.GetFolderPath(Environment.SpecialFolder.Personal) + "\\" + "GlobalERP" + "\\" + "ERP_Profile2.xml", settings);

                    writer.WriteStartDocument();
                    writer.WriteComment("This file is generated by Global Grid ERP System");

                    writer.WriteStartElement("Settings");

                    writer.WriteElementString("App", serial.ToString());

                    writer.WriteEndElement();
                    writer.WriteEndDocument();
                    writer.Flush();
                    
                }
                catch
                { }
                finally
                {
                    writer.Close();
                }

                return true;
            }
            else
                return false;
        }
    }
}