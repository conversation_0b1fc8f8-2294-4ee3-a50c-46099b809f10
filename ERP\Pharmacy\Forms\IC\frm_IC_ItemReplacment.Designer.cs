﻿namespace Pharmacy.Forms
{
    partial class frm_IC_ItemReplacment
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_IC_ItemReplacment));
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtnHelp = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnPrint = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnDelete = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnCommit = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnSave = new DevExpress.XtraBars.BarButtonItem();
            this.batBtnList = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.linqServerModeSource1 = new DevExpress.Data.Linq.LinqServerModeSource();
            this.panelControl2 = new DevExpress.XtraEditors.PanelControl();
            this.grdPrInvoice = new DevExpress.XtraGrid.GridControl();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_Diff = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ActualWeight = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ActualPieces = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_LibraQty = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_PricingWithSmall = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_PiecesCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_VariableWeight = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_kg_Weight_libra = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repUOM = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView4 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repItems = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.repositoryItemGridLookUpEdit1View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumnPurchasePrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Batch = new DevExpress.XtraGrid.Columns.GridColumn();
            this.RepBatch = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_Serial = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repSerial = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.col_QC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repQC = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.col_Expire = new DevExpress.XtraGrid.Columns.GridColumn();
            this.RepXpireDate = new DevExpress.XtraEditors.Repository.RepositoryItemDateEdit();
            this.col_Vendor = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repVendor = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView5 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.RepXpire = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemLookUpEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.repVendorold = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.repositoryItemMarqueeProgressBar1 = new DevExpress.XtraEditors.Repository.RepositoryItemMarqueeProgressBar();
            this.r = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.lkp_Store = new DevExpress.XtraEditors.LookUpEdit();
            this.pnlAccount = new System.Windows.Forms.Panel();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.lkp_Account = new DevExpress.XtraEditors.GridLookUpEdit();
            this.gridView6 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.txtNote = new DevExpress.XtraEditors.TextEdit();
            this.dt_ST_Date = new DevExpress.XtraEditors.DateEdit();
            this.labelControl15 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.linqServerModeSource1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).BeginInit();
            this.panelControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grdPrInvoice)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repUOM)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repItems)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.RepBatch)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repSerial)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repQC)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.RepXpireDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.RepXpireDate.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repVendor)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.RepXpire)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemLookUpEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repVendorold)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMarqueeProgressBar1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.r)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Store.Properties)).BeginInit();
            this.pnlAccount.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Account.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNote.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dt_ST_Date.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dt_ST_Date.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnSave,
            this.barBtnClose,
            this.barBtnHelp,
            this.batBtnList,
            this.barBtnCommit,
            this.barBtnPrint,
            this.barBtnDelete});
            this.barManager1.MaxItemId = 29;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(377, 152);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnHelp),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnPrint),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnDelete),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnCommit),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnSave),
            new DevExpress.XtraBars.LinkPersistInfo(this.batBtnList),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtnHelp
            // 
            resources.ApplyResources(this.barBtnHelp, "barBtnHelp");
            this.barBtnHelp.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnHelp.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtnHelp.Id = 2;
            this.barBtnHelp.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtnHelp.Name = "barBtnHelp";
            this.barBtnHelp.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnHelp.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnHelp_ItemClick);
            // 
            // barBtnPrint
            // 
            resources.ApplyResources(this.barBtnPrint, "barBtnPrint");
            this.barBtnPrint.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnPrint.Glyph = global::Pharmacy.Properties.Resources.prnt;
            this.barBtnPrint.Id = 27;
            this.barBtnPrint.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.P));
            this.barBtnPrint.Name = "barBtnPrint";
            this.barBtnPrint.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnPrint.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnPrint_ItemClick);
            // 
            // barBtnDelete
            // 
            resources.ApplyResources(this.barBtnDelete, "barBtnDelete");
            this.barBtnDelete.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnDelete.Glyph = global::Pharmacy.Properties.Resources.del;
            this.barBtnDelete.Id = 28;
            this.barBtnDelete.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.D));
            this.barBtnDelete.Name = "barBtnDelete";
            this.barBtnDelete.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnDelete.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnDelete_ItemClick);
            // 
            // barBtnCommit
            // 
            resources.ApplyResources(this.barBtnCommit, "barBtnCommit");
            this.barBtnCommit.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnCommit.Glyph = global::Pharmacy.Properties.Resources.cmt;
            this.barBtnCommit.Id = 26;
            this.barBtnCommit.Name = "barBtnCommit";
            this.barBtnCommit.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnCommit.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnCommit_ItemClick);
            // 
            // barBtnSave
            // 
            resources.ApplyResources(this.barBtnSave, "barBtnSave");
            this.barBtnSave.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnSave.Glyph = global::Pharmacy.Properties.Resources.sve;
            this.barBtnSave.Id = 0;
            this.barBtnSave.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.S));
            this.barBtnSave.Name = "barBtnSave";
            this.barBtnSave.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnSave.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Save_ItemClick);
            // 
            // batBtnList
            // 
            resources.ApplyResources(this.batBtnList, "batBtnList");
            this.batBtnList.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.batBtnList.Glyph = global::Pharmacy.Properties.Resources.list32;
            this.batBtnList.Id = 25;
            this.batBtnList.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.T));
            this.batBtnList.Name = "batBtnList";
            this.batBtnList.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.batBtnList.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            // 
            // barBtnClose
            // 
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 1;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnClose_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.BarAppearance.Normal.GradientMode")));
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.BarAppearance.Normal.Image")));
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.Dock.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.Dock.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.Dock.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.Dock.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.Dock.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.Dock.GradientMode")));
            this.barAndDockingController1.AppearancesBar.Dock.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.Dock.Image")));
            this.barAndDockingController1.AppearancesBar.Dock.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.Dock.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.GradientMode")));
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Image")));
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontStyleDelta" +
        "")));
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.GradientMode")));
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.Image")));
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontSizeDel" +
        "ta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontStyleDe" +
        "lta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.GradientMod" +
        "e")));
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontSizeDelt" +
        "a")));
            this.barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontStyleDel" +
        "ta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.GradientMode" +
        "")));
            this.barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuBar.GradientMode")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuBar.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.GradientMode")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStrip.GradientMode")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStrip.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontStyleDelta" +
        "")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.GradientMode")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.Panel.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesDocking.Panel.FontSizeDelta")));
            this.barAndDockingController1.AppearancesDocking.Panel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesDocking.Panel.FontStyleDelta")));
            this.barAndDockingController1.AppearancesDocking.Panel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesDocking.Panel.GradientMode")));
            this.barAndDockingController1.AppearancesDocking.Panel.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesDocking.Panel.Image")));
            this.barAndDockingController1.AppearancesDocking.Panel.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.Panel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.PanelCaption.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaption.FontSizeDelta")));
            this.barAndDockingController1.AppearancesDocking.PanelCaption.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaption.FontStyleDelta")));
            this.barAndDockingController1.AppearancesDocking.PanelCaption.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaption.GradientMode")));
            this.barAndDockingController1.AppearancesDocking.PanelCaption.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaption.Image")));
            this.barAndDockingController1.AppearancesDocking.PanelCaption.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.PanelCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesRibbon.Item.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesRibbon.Item.FontSizeDelta")));
            this.barAndDockingController1.AppearancesRibbon.Item.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesRibbon.Item.FontStyleDelta")));
            this.barAndDockingController1.AppearancesRibbon.Item.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesRibbon.Item.GradientMode")));
            this.barAndDockingController1.AppearancesRibbon.Item.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesRibbon.Item.Image")));
            this.barAndDockingController1.AppearancesRibbon.Item.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesRibbon.Item.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesRibbon.PageHeader.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesRibbon.PageHeader.FontSizeDelta")));
            this.barAndDockingController1.AppearancesRibbon.PageHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesRibbon.PageHeader.FontStyleDelta")));
            this.barAndDockingController1.AppearancesRibbon.PageHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesRibbon.PageHeader.GradientMode")));
            this.barAndDockingController1.AppearancesRibbon.PageHeader.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesRibbon.PageHeader.Image")));
            this.barAndDockingController1.AppearancesRibbon.PageHeader.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesRibbon.PageHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlTop.Appearance.FontSizeDelta")));
            this.barDockControlTop.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlTop.Appearance.FontStyleDelta")));
            this.barDockControlTop.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlTop.Appearance.GradientMode")));
            this.barDockControlTop.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlTop.Appearance.Image")));
            this.barDockControlTop.Appearance.Options.UseTextOptions = true;
            this.barDockControlTop.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barDockControlTop.CausesValidation = false;
            // 
            // barDockControlBottom
            // 
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlBottom.Appearance.FontSizeDelta")));
            this.barDockControlBottom.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlBottom.Appearance.FontStyleDelta")));
            this.barDockControlBottom.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlBottom.Appearance.GradientMode")));
            this.barDockControlBottom.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlBottom.Appearance.Image")));
            this.barDockControlBottom.CausesValidation = false;
            // 
            // barDockControlLeft
            // 
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlLeft.Appearance.FontSizeDelta")));
            this.barDockControlLeft.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlLeft.Appearance.FontStyleDelta")));
            this.barDockControlLeft.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlLeft.Appearance.GradientMode")));
            this.barDockControlLeft.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlLeft.Appearance.Image")));
            this.barDockControlLeft.CausesValidation = false;
            // 
            // barDockControlRight
            // 
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlRight.Appearance.FontSizeDelta")));
            this.barDockControlRight.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlRight.Appearance.FontStyleDelta")));
            this.barDockControlRight.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlRight.Appearance.GradientMode")));
            this.barDockControlRight.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlRight.Appearance.Image")));
            this.barDockControlRight.CausesValidation = false;
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemTextEdit1.Mask.AutoComplete")));
            this.repositoryItemTextEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.BeepOnError")));
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemTextEdit1.Mask.MaskType")));
            this.repositoryItemTextEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemTextEdit1.Mask.PlaceHolder")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // linqServerModeSource1
            // 
            this.linqServerModeSource1.KeyExpression = "ItemId";
            // 
            // panelControl2
            // 
            resources.ApplyResources(this.panelControl2, "panelControl2");
            this.panelControl2.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.panelControl2.Controls.Add(this.grdPrInvoice);
            this.panelControl2.Name = "panelControl2";
            // 
            // grdPrInvoice
            // 
            resources.ApplyResources(this.grdPrInvoice, "grdPrInvoice");
            this.grdPrInvoice.EmbeddedNavigator.AccessibleDescription = resources.GetString("grdPrInvoice.EmbeddedNavigator.AccessibleDescription");
            this.grdPrInvoice.EmbeddedNavigator.AccessibleName = resources.GetString("grdPrInvoice.EmbeddedNavigator.AccessibleName");
            this.grdPrInvoice.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grdPrInvoice.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.Anchor")));
            this.grdPrInvoice.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.BackgroundImage")));
            this.grdPrInvoice.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.BackgroundImageLayout")));
            this.grdPrInvoice.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.ImeMode")));
            this.grdPrInvoice.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.MaximumSize")));
            this.grdPrInvoice.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.TextLocation")));
            this.grdPrInvoice.EmbeddedNavigator.ToolTip = resources.GetString("grdPrInvoice.EmbeddedNavigator.ToolTip");
            this.grdPrInvoice.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.ToolTipIconType")));
            this.grdPrInvoice.EmbeddedNavigator.ToolTipTitle = resources.GetString("grdPrInvoice.EmbeddedNavigator.ToolTipTitle");
            this.grdPrInvoice.MainView = this.gridView2;
            this.grdPrInvoice.MenuManager = this.barManager1;
            this.grdPrInvoice.Name = "grdPrInvoice";
            this.grdPrInvoice.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repItems,
            this.repUOM,
            this.RepXpire,
            this.RepBatch,
            this.repositoryItemLookUpEdit1,
            this.repSerial,
            this.repQC,
            this.repVendorold,
            this.repositoryItemMarqueeProgressBar1,
            this.r,
            this.repVendor,
            this.RepXpireDate});
            this.grdPrInvoice.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView2});
            // 
            // gridView2
            // 
            this.gridView2.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView2.Appearance.HeaderPanel.FontSizeDelta")));
            this.gridView2.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView2.Appearance.HeaderPanel.FontStyleDelta")));
            this.gridView2.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView2.Appearance.HeaderPanel.GradientMode")));
            this.gridView2.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView2.Appearance.HeaderPanel.Image")));
            this.gridView2.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView2.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView2.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView2.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gridView2.Appearance.Row.FontSizeDelta")));
            this.gridView2.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView2.Appearance.Row.FontStyleDelta")));
            this.gridView2.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView2.Appearance.Row.GradientMode")));
            this.gridView2.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView2.Appearance.Row.Image")));
            this.gridView2.Appearance.Row.Options.UseTextOptions = true;
            this.gridView2.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView2.AppearancePrint.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView2.AppearancePrint.HeaderPanel.FontSizeDelta")));
            this.gridView2.AppearancePrint.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView2.AppearancePrint.HeaderPanel.FontStyleDelta")));
            this.gridView2.AppearancePrint.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView2.AppearancePrint.HeaderPanel.GradientMode")));
            this.gridView2.AppearancePrint.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView2.AppearancePrint.HeaderPanel.Image")));
            this.gridView2.AppearancePrint.HeaderPanel.Options.UseTextOptions = true;
            this.gridView2.AppearancePrint.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridView2, "gridView2");
            this.gridView2.ColumnPanelRowHeight = 50;
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_Diff,
            this.col_ActualWeight,
            this.col_ActualPieces,
            this.col_LibraQty,
            this.col_PricingWithSmall,
            this.col_PiecesCount,
            this.col_VariableWeight,
            this.col_kg_Weight_libra,
            this.gridColumn1,
            this.gridColumn34,
            this.gridColumn29,
            this.gridColumn28,
            this.gridColumn8,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn31,
            this.gridColumnPurchasePrice,
            this.gridColumn5,
            this.col_Batch,
            this.col_Serial,
            this.col_QC,
            this.col_Expire,
            this.col_Vendor,
            this.gridColumn22});
            this.gridView2.CustomizationFormBounds = new System.Drawing.Rectangle(1015, 248, 210, 382);
            this.gridView2.GridControl = this.grdPrInvoice;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView2.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.gridView2.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView2.OptionsView.EnableAppearanceOddRow = true;
            this.gridView2.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Bottom;
            this.gridView2.OptionsView.RowAutoHeight = true;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            this.gridView2.ShowingEditor += new System.ComponentModel.CancelEventHandler(this.gridView2_ShowingEditor);
            this.gridView2.FocusedRowChanged += new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventHandler(this.gridView1_FocusedRowChanged);
            this.gridView2.FocusedColumnChanged += new DevExpress.XtraGrid.Views.Base.FocusedColumnChangedEventHandler(this.gridView1_FocusedColumnChanged);
            this.gridView2.CellValueChanged += new DevExpress.XtraGrid.Views.Base.CellValueChangedEventHandler(this.gridView1_CellValueChanged);
            this.gridView2.InvalidRowException += new DevExpress.XtraGrid.Views.Base.InvalidRowExceptionEventHandler(this.gridView1_InvalidRowException);
            this.gridView2.ValidateRow += new DevExpress.XtraGrid.Views.Base.ValidateRowEventHandler(this.gridView1_ValidateRow);
            this.gridView2.CustomUnboundColumnData += new DevExpress.XtraGrid.Views.Base.CustomColumnDataEventHandler(this.gridView2_CustomUnboundColumnData);
            this.gridView2.KeyDown += new System.Windows.Forms.KeyEventHandler(this.gridView1_KeyDown);
            // 
            // col_Diff
            // 
            this.col_Diff.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_Diff.AppearanceCell.FontSizeDelta")));
            this.col_Diff.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_Diff.AppearanceCell.FontStyleDelta")));
            this.col_Diff.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_Diff.AppearanceCell.GradientMode")));
            this.col_Diff.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_Diff.AppearanceCell.Image")));
            this.col_Diff.AppearanceCell.Options.UseTextOptions = true;
            this.col_Diff.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Diff.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_Diff.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_Diff.AppearanceHeader.FontSizeDelta")));
            this.col_Diff.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_Diff.AppearanceHeader.FontStyleDelta")));
            this.col_Diff.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_Diff.AppearanceHeader.GradientMode")));
            this.col_Diff.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_Diff.AppearanceHeader.Image")));
            this.col_Diff.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Diff.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Diff.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_Diff, "col_Diff");
            this.col_Diff.FieldName = "Diff";
            this.col_Diff.Name = "col_Diff";
            this.col_Diff.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            this.col_Diff.UnboundType = DevExpress.Data.UnboundColumnType.Decimal;
            // 
            // col_ActualWeight
            // 
            this.col_ActualWeight.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_ActualWeight.AppearanceCell.FontSizeDelta")));
            this.col_ActualWeight.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_ActualWeight.AppearanceCell.FontStyleDelta")));
            this.col_ActualWeight.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_ActualWeight.AppearanceCell.GradientMode")));
            this.col_ActualWeight.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_ActualWeight.AppearanceCell.Image")));
            this.col_ActualWeight.AppearanceCell.Options.UseTextOptions = true;
            this.col_ActualWeight.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_ActualWeight.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_ActualWeight.AppearanceHeader.FontSizeDelta")));
            this.col_ActualWeight.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_ActualWeight.AppearanceHeader.FontStyleDelta")));
            this.col_ActualWeight.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_ActualWeight.AppearanceHeader.GradientMode")));
            this.col_ActualWeight.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_ActualWeight.AppearanceHeader.Image")));
            this.col_ActualWeight.AppearanceHeader.Options.UseTextOptions = true;
            this.col_ActualWeight.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_ActualWeight, "col_ActualWeight");
            this.col_ActualWeight.FieldName = "ActualWeight";
            this.col_ActualWeight.Name = "col_ActualWeight";
            // 
            // col_ActualPieces
            // 
            this.col_ActualPieces.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_ActualPieces.AppearanceCell.FontSizeDelta")));
            this.col_ActualPieces.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_ActualPieces.AppearanceCell.FontStyleDelta")));
            this.col_ActualPieces.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_ActualPieces.AppearanceCell.GradientMode")));
            this.col_ActualPieces.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_ActualPieces.AppearanceCell.Image")));
            this.col_ActualPieces.AppearanceCell.Options.UseTextOptions = true;
            this.col_ActualPieces.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_ActualPieces.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_ActualPieces.AppearanceHeader.FontSizeDelta")));
            this.col_ActualPieces.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_ActualPieces.AppearanceHeader.FontStyleDelta")));
            this.col_ActualPieces.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_ActualPieces.AppearanceHeader.GradientMode")));
            this.col_ActualPieces.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_ActualPieces.AppearanceHeader.Image")));
            this.col_ActualPieces.AppearanceHeader.Options.UseTextOptions = true;
            this.col_ActualPieces.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_ActualPieces, "col_ActualPieces");
            this.col_ActualPieces.FieldName = "ActualPieces";
            this.col_ActualPieces.Name = "col_ActualPieces";
            // 
            // col_LibraQty
            // 
            this.col_LibraQty.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_LibraQty.AppearanceCell.FontSizeDelta")));
            this.col_LibraQty.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_LibraQty.AppearanceCell.FontStyleDelta")));
            this.col_LibraQty.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_LibraQty.AppearanceCell.GradientMode")));
            this.col_LibraQty.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_LibraQty.AppearanceCell.Image")));
            this.col_LibraQty.AppearanceCell.Options.UseTextOptions = true;
            this.col_LibraQty.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_LibraQty.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_LibraQty.AppearanceHeader.FontSizeDelta")));
            this.col_LibraQty.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_LibraQty.AppearanceHeader.FontStyleDelta")));
            this.col_LibraQty.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_LibraQty.AppearanceHeader.GradientMode")));
            this.col_LibraQty.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_LibraQty.AppearanceHeader.Image")));
            this.col_LibraQty.AppearanceHeader.Options.UseTextOptions = true;
            this.col_LibraQty.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_LibraQty, "col_LibraQty");
            this.col_LibraQty.FieldName = "LibraQty";
            this.col_LibraQty.Name = "col_LibraQty";
            // 
            // col_PricingWithSmall
            // 
            this.col_PricingWithSmall.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_PricingWithSmall.AppearanceCell.FontSizeDelta")));
            this.col_PricingWithSmall.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_PricingWithSmall.AppearanceCell.FontStyleDelta")));
            this.col_PricingWithSmall.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_PricingWithSmall.AppearanceCell.GradientMode")));
            this.col_PricingWithSmall.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_PricingWithSmall.AppearanceCell.Image")));
            this.col_PricingWithSmall.AppearanceCell.Options.UseTextOptions = true;
            this.col_PricingWithSmall.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_PricingWithSmall.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_PricingWithSmall.AppearanceHeader.FontSizeDelta")));
            this.col_PricingWithSmall.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_PricingWithSmall.AppearanceHeader.FontStyleDelta")));
            this.col_PricingWithSmall.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_PricingWithSmall.AppearanceHeader.GradientMode")));
            this.col_PricingWithSmall.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_PricingWithSmall.AppearanceHeader.Image")));
            this.col_PricingWithSmall.AppearanceHeader.Options.UseTextOptions = true;
            this.col_PricingWithSmall.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_PricingWithSmall, "col_PricingWithSmall");
            this.col_PricingWithSmall.FieldName = "PricingWithSmall";
            this.col_PricingWithSmall.Name = "col_PricingWithSmall";
            this.col_PricingWithSmall.OptionsColumn.AllowEdit = false;
            // 
            // col_PiecesCount
            // 
            this.col_PiecesCount.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_PiecesCount.AppearanceCell.FontSizeDelta")));
            this.col_PiecesCount.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_PiecesCount.AppearanceCell.FontStyleDelta")));
            this.col_PiecesCount.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_PiecesCount.AppearanceCell.GradientMode")));
            this.col_PiecesCount.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_PiecesCount.AppearanceCell.Image")));
            this.col_PiecesCount.AppearanceCell.Options.UseTextOptions = true;
            this.col_PiecesCount.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_PiecesCount.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_PiecesCount.AppearanceHeader.FontSizeDelta")));
            this.col_PiecesCount.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_PiecesCount.AppearanceHeader.FontStyleDelta")));
            this.col_PiecesCount.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_PiecesCount.AppearanceHeader.GradientMode")));
            this.col_PiecesCount.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_PiecesCount.AppearanceHeader.Image")));
            this.col_PiecesCount.AppearanceHeader.Options.UseTextOptions = true;
            this.col_PiecesCount.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_PiecesCount, "col_PiecesCount");
            this.col_PiecesCount.FieldName = "PiecesCount";
            this.col_PiecesCount.Name = "col_PiecesCount";
            this.col_PiecesCount.OptionsColumn.AllowEdit = false;
            // 
            // col_VariableWeight
            // 
            this.col_VariableWeight.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_VariableWeight.AppearanceCell.FontSizeDelta")));
            this.col_VariableWeight.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_VariableWeight.AppearanceCell.FontStyleDelta")));
            this.col_VariableWeight.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_VariableWeight.AppearanceCell.GradientMode")));
            this.col_VariableWeight.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_VariableWeight.AppearanceCell.Image")));
            this.col_VariableWeight.AppearanceCell.Options.UseTextOptions = true;
            this.col_VariableWeight.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_VariableWeight.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_VariableWeight.AppearanceHeader.FontSizeDelta")));
            this.col_VariableWeight.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_VariableWeight.AppearanceHeader.FontStyleDelta")));
            this.col_VariableWeight.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_VariableWeight.AppearanceHeader.GradientMode")));
            this.col_VariableWeight.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_VariableWeight.AppearanceHeader.Image")));
            this.col_VariableWeight.AppearanceHeader.Options.UseTextOptions = true;
            this.col_VariableWeight.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_VariableWeight, "col_VariableWeight");
            this.col_VariableWeight.FieldName = "VariableWeight";
            this.col_VariableWeight.Name = "col_VariableWeight";
            this.col_VariableWeight.OptionsColumn.AllowEdit = false;
            // 
            // col_kg_Weight_libra
            // 
            this.col_kg_Weight_libra.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_kg_Weight_libra.AppearanceCell.FontSizeDelta")));
            this.col_kg_Weight_libra.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_kg_Weight_libra.AppearanceCell.FontStyleDelta")));
            this.col_kg_Weight_libra.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_kg_Weight_libra.AppearanceCell.GradientMode")));
            this.col_kg_Weight_libra.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_kg_Weight_libra.AppearanceCell.Image")));
            this.col_kg_Weight_libra.AppearanceCell.Options.UseTextOptions = true;
            this.col_kg_Weight_libra.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_kg_Weight_libra.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_kg_Weight_libra.AppearanceHeader.FontSizeDelta")));
            this.col_kg_Weight_libra.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_kg_Weight_libra.AppearanceHeader.FontStyleDelta")));
            this.col_kg_Weight_libra.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_kg_Weight_libra.AppearanceHeader.GradientMode")));
            this.col_kg_Weight_libra.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_kg_Weight_libra.AppearanceHeader.Image")));
            this.col_kg_Weight_libra.AppearanceHeader.Options.UseTextOptions = true;
            this.col_kg_Weight_libra.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_kg_Weight_libra, "col_kg_Weight_libra");
            this.col_kg_Weight_libra.FieldName = "kg_Weight_libra";
            this.col_kg_Weight_libra.Name = "col_kg_Weight_libra";
            this.col_kg_Weight_libra.OptionsColumn.AllowEdit = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn1.AppearanceCell.FontSizeDelta")));
            this.gridColumn1.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn1.AppearanceCell.FontStyleDelta")));
            this.gridColumn1.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn1.AppearanceCell.GradientMode")));
            this.gridColumn1.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn1.AppearanceCell.Image")));
            this.gridColumn1.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn1.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn1.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn1.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn1.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn1.AppearanceHeader.FontSizeDelta")));
            this.gridColumn1.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn1.AppearanceHeader.FontStyleDelta")));
            this.gridColumn1.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn1.AppearanceHeader.GradientMode")));
            this.gridColumn1.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn1.AppearanceHeader.Image")));
            this.gridColumn1.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn1.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn1.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn1.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn1, "gridColumn1");
            this.gridColumn1.DisplayFormat.FormatString = "n2";
            this.gridColumn1.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn1.FieldName = "Supposed";
            this.gridColumn1.Name = "gridColumn1";
            // 
            // gridColumn34
            // 
            this.gridColumn34.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn34.AppearanceCell.FontSizeDelta")));
            this.gridColumn34.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn34.AppearanceCell.FontStyleDelta")));
            this.gridColumn34.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn34.AppearanceCell.GradientMode")));
            this.gridColumn34.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn34.AppearanceCell.Image")));
            this.gridColumn34.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn34.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn34.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn34.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn34.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn34.AppearanceHeader.FontSizeDelta")));
            this.gridColumn34.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn34.AppearanceHeader.FontStyleDelta")));
            this.gridColumn34.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn34.AppearanceHeader.GradientMode")));
            this.gridColumn34.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn34.AppearanceHeader.Image")));
            this.gridColumn34.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn34.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn34.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn34.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn34, "gridColumn34");
            this.gridColumn34.DisplayFormat.FormatString = "n2";
            this.gridColumn34.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn34.FieldName = "Qty";
            this.gridColumn34.Name = "gridColumn34";
            this.gridColumn34.OptionsColumn.AllowEdit = false;
            this.gridColumn34.OptionsColumn.AllowFocus = false;
            this.gridColumn34.OptionsColumn.ReadOnly = true;
            // 
            // gridColumn29
            // 
            this.gridColumn29.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn29.AppearanceCell.FontSizeDelta")));
            this.gridColumn29.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn29.AppearanceCell.FontStyleDelta")));
            this.gridColumn29.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn29.AppearanceCell.GradientMode")));
            this.gridColumn29.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn29.AppearanceCell.Image")));
            this.gridColumn29.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn29.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn29.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn29.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn29.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn29.AppearanceHeader.FontSizeDelta")));
            this.gridColumn29.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn29.AppearanceHeader.FontStyleDelta")));
            this.gridColumn29.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn29.AppearanceHeader.GradientMode")));
            this.gridColumn29.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn29.AppearanceHeader.Image")));
            this.gridColumn29.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn29.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn29.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn29.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn29, "gridColumn29");
            this.gridColumn29.FieldName = "LargeUOMFactor";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.OptionsColumn.AllowEdit = false;
            this.gridColumn29.OptionsColumn.ReadOnly = true;
            // 
            // gridColumn28
            // 
            this.gridColumn28.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn28.AppearanceCell.FontSizeDelta")));
            this.gridColumn28.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn28.AppearanceCell.FontStyleDelta")));
            this.gridColumn28.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn28.AppearanceCell.GradientMode")));
            this.gridColumn28.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn28.AppearanceCell.Image")));
            this.gridColumn28.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn28.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn28.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn28.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn28.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn28.AppearanceHeader.FontSizeDelta")));
            this.gridColumn28.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn28.AppearanceHeader.FontStyleDelta")));
            this.gridColumn28.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn28.AppearanceHeader.GradientMode")));
            this.gridColumn28.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn28.AppearanceHeader.Image")));
            this.gridColumn28.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn28.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn28.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn28.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn28, "gridColumn28");
            this.gridColumn28.FieldName = "MediumUOMFactor";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.OptionsColumn.AllowEdit = false;
            this.gridColumn28.OptionsColumn.ReadOnly = true;
            // 
            // gridColumn8
            // 
            this.gridColumn8.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn8.AppearanceCell.FontSizeDelta")));
            this.gridColumn8.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn8.AppearanceCell.FontStyleDelta")));
            this.gridColumn8.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn8.AppearanceCell.GradientMode")));
            this.gridColumn8.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn8.AppearanceCell.Image")));
            this.gridColumn8.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn8.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn8.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn8.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn8.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn8.AppearanceHeader.FontSizeDelta")));
            this.gridColumn8.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn8.AppearanceHeader.FontStyleDelta")));
            this.gridColumn8.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn8.AppearanceHeader.GradientMode")));
            this.gridColumn8.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn8.AppearanceHeader.Image")));
            this.gridColumn8.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn8.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn8.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn8.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn8, "gridColumn8");
            this.gridColumn8.ColumnEdit = this.repUOM;
            this.gridColumn8.FieldName = "UOM";
            this.gridColumn8.Name = "gridColumn8";
            // 
            // repUOM
            // 
            resources.ApplyResources(this.repUOM, "repUOM");
            this.repUOM.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repUOM.Buttons"))))});
            this.repUOM.Name = "repUOM";
            this.repUOM.View = this.gridView4;
            this.repUOM.CustomDisplayText += new DevExpress.XtraEditors.Controls.CustomDisplayTextEventHandler(this.repUOM_CustomDisplayText);
            // 
            // gridView4
            // 
            this.gridView4.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView4.Appearance.HeaderPanel.FontSizeDelta")));
            this.gridView4.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView4.Appearance.HeaderPanel.FontStyleDelta")));
            this.gridView4.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView4.Appearance.HeaderPanel.GradientMode")));
            this.gridView4.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView4.Appearance.HeaderPanel.Image")));
            this.gridView4.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView4.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView4.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gridView4.Appearance.Row.FontSizeDelta")));
            this.gridView4.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView4.Appearance.Row.FontStyleDelta")));
            this.gridView4.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView4.Appearance.Row.GradientMode")));
            this.gridView4.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView4.Appearance.Row.Image")));
            this.gridView4.Appearance.Row.Options.UseTextOptions = true;
            this.gridView4.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.gridView4, "gridView4");
            this.gridView4.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn9,
            this.gridColumn16,
            this.gridColumn17});
            this.gridView4.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView4.Name = "gridView4";
            this.gridView4.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView4.OptionsView.ShowDetailButtons = false;
            this.gridView4.OptionsView.ShowGroupExpandCollapseButtons = false;
            this.gridView4.OptionsView.ShowGroupPanel = false;
            this.gridView4.OptionsView.ShowIndicator = false;
            // 
            // gridColumn9
            // 
            resources.ApplyResources(this.gridColumn9, "gridColumn9");
            this.gridColumn9.FieldName = "Index";
            this.gridColumn9.Name = "gridColumn9";
            // 
            // gridColumn16
            // 
            resources.ApplyResources(this.gridColumn16, "gridColumn16");
            this.gridColumn16.FieldName = "Factor";
            this.gridColumn16.Name = "gridColumn16";
            // 
            // gridColumn17
            // 
            resources.ApplyResources(this.gridColumn17, "gridColumn17");
            this.gridColumn17.FieldName = "Uom";
            this.gridColumn17.Name = "gridColumn17";
            // 
            // gridColumn10
            // 
            this.gridColumn10.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn10.AppearanceCell.FontSizeDelta")));
            this.gridColumn10.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn10.AppearanceCell.FontStyleDelta")));
            this.gridColumn10.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn10.AppearanceCell.GradientMode")));
            this.gridColumn10.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn10.AppearanceCell.Image")));
            this.gridColumn10.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn10.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn10.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn10.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn10.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn10.AppearanceHeader.FontSizeDelta")));
            this.gridColumn10.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn10.AppearanceHeader.FontStyleDelta")));
            this.gridColumn10.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn10.AppearanceHeader.GradientMode")));
            this.gridColumn10.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn10.AppearanceHeader.Image")));
            this.gridColumn10.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn10.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn10.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn10.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn10, "gridColumn10");
            this.gridColumn10.ColumnEdit = this.repItems;
            this.gridColumn10.FieldName = "ItemId";
            this.gridColumn10.Name = "gridColumn10";
            // 
            // repItems
            // 
            resources.ApplyResources(this.repItems, "repItems");
            this.repItems.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.repItems.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.repItems.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repItems.Buttons"))))});
            this.repItems.ImmediatePopup = true;
            this.repItems.Name = "repItems";
            this.repItems.ShowFooter = false;
            this.repItems.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.repItems.View = this.repositoryItemGridLookUpEdit1View;
            // 
            // repositoryItemGridLookUpEdit1View
            // 
            resources.ApplyResources(this.repositoryItemGridLookUpEdit1View, "repositoryItemGridLookUpEdit1View");
            this.repositoryItemGridLookUpEdit1View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn7});
            this.repositoryItemGridLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.repositoryItemGridLookUpEdit1View.Name = "repositoryItemGridLookUpEdit1View";
            this.repositoryItemGridLookUpEdit1View.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.repositoryItemGridLookUpEdit1View.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.repositoryItemGridLookUpEdit1View.OptionsBehavior.AutoSelectAllInEditor = false;
            this.repositoryItemGridLookUpEdit1View.OptionsBehavior.AutoUpdateTotalSummary = false;
            this.repositoryItemGridLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.repositoryItemGridLookUpEdit1View.OptionsSelection.UseIndicatorForSelection = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.BestFitMaxRowCount = 10;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowAutoFilterRow = true;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowDetailButtons = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowGroupPanel = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowIndicator = false;
            // 
            // gridColumn12
            // 
            resources.ApplyResources(this.gridColumn12, "gridColumn12");
            this.gridColumn12.FieldName = "ItemNameEn";
            this.gridColumn12.Name = "gridColumn12";
            // 
            // gridColumn13
            // 
            resources.ApplyResources(this.gridColumn13, "gridColumn13");
            this.gridColumn13.FieldName = "ItemNameAr";
            this.gridColumn13.Name = "gridColumn13";
            // 
            // gridColumn14
            // 
            resources.ApplyResources(this.gridColumn14, "gridColumn14");
            this.gridColumn14.FieldName = "ItemCode1";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // gridColumn15
            // 
            resources.ApplyResources(this.gridColumn15, "gridColumn15");
            this.gridColumn15.FieldName = "ItemId";
            this.gridColumn15.Name = "gridColumn15";
            // 
            // gridColumn7
            // 
            resources.ApplyResources(this.gridColumn7, "gridColumn7");
            this.gridColumn7.FieldName = "ItemCode2";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // gridColumn11
            // 
            this.gridColumn11.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn11.AppearanceCell.FontSizeDelta")));
            this.gridColumn11.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn11.AppearanceCell.FontStyleDelta")));
            this.gridColumn11.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn11.AppearanceCell.GradientMode")));
            this.gridColumn11.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn11.AppearanceCell.Image")));
            this.gridColumn11.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn11.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn11.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn11.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn11.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn11.AppearanceHeader.FontSizeDelta")));
            this.gridColumn11.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn11.AppearanceHeader.FontStyleDelta")));
            this.gridColumn11.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn11.AppearanceHeader.GradientMode")));
            this.gridColumn11.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn11.AppearanceHeader.Image")));
            this.gridColumn11.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn11.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn11.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn11.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn11, "gridColumn11");
            this.gridColumn11.FieldName = "ItemCode2";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.OptionsColumn.AllowEdit = false;
            this.gridColumn11.OptionsColumn.ReadOnly = true;
            // 
            // gridColumn31
            // 
            this.gridColumn31.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn31.AppearanceCell.FontSizeDelta")));
            this.gridColumn31.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn31.AppearanceCell.FontStyleDelta")));
            this.gridColumn31.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn31.AppearanceCell.GradientMode")));
            this.gridColumn31.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn31.AppearanceCell.Image")));
            this.gridColumn31.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn31.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn31.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn31.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn31.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn31.AppearanceHeader.FontSizeDelta")));
            this.gridColumn31.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn31.AppearanceHeader.FontStyleDelta")));
            this.gridColumn31.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn31.AppearanceHeader.GradientMode")));
            this.gridColumn31.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn31.AppearanceHeader.Image")));
            this.gridColumn31.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn31.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn31.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn31.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn31, "gridColumn31");
            this.gridColumn31.FieldName = "ItemCode1";
            this.gridColumn31.Name = "gridColumn31";
            // 
            // gridColumnPurchasePrice
            // 
            this.gridColumnPurchasePrice.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumnPurchasePrice.AppearanceCell.FontSizeDelta")));
            this.gridColumnPurchasePrice.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumnPurchasePrice.AppearanceCell.FontStyleDelta")));
            this.gridColumnPurchasePrice.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumnPurchasePrice.AppearanceCell.GradientMode")));
            this.gridColumnPurchasePrice.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumnPurchasePrice.AppearanceCell.Image")));
            this.gridColumnPurchasePrice.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumnPurchasePrice.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumnPurchasePrice.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumnPurchasePrice.AppearanceHeader.FontSizeDelta")));
            this.gridColumnPurchasePrice.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumnPurchasePrice.AppearanceHeader.FontStyleDelta")));
            this.gridColumnPurchasePrice.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumnPurchasePrice.AppearanceHeader.GradientMode")));
            this.gridColumnPurchasePrice.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumnPurchasePrice.AppearanceHeader.Image")));
            this.gridColumnPurchasePrice.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumnPurchasePrice.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumnPurchasePrice, "gridColumnPurchasePrice");
            this.gridColumnPurchasePrice.FieldName = "PurchasePrice";
            this.gridColumnPurchasePrice.Name = "gridColumnPurchasePrice";
            // 
            // gridColumn5
            // 
            this.gridColumn5.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn5.AppearanceCell.FontSizeDelta")));
            this.gridColumn5.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn5.AppearanceCell.FontStyleDelta")));
            this.gridColumn5.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn5.AppearanceCell.GradientMode")));
            this.gridColumn5.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn5.AppearanceCell.Image")));
            this.gridColumn5.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn5.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn5.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn5.AppearanceHeader.FontSizeDelta")));
            this.gridColumn5.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn5.AppearanceHeader.FontStyleDelta")));
            this.gridColumn5.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn5.AppearanceHeader.GradientMode")));
            this.gridColumn5.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn5.AppearanceHeader.Image")));
            this.gridColumn5.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn5.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn5.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            resources.ApplyResources(this.gridColumn5, "gridColumn5");
            this.gridColumn5.FieldName = "UomIndex";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.OptionsColumn.AllowEdit = false;
            this.gridColumn5.OptionsColumn.ReadOnly = true;
            // 
            // col_Batch
            // 
            this.col_Batch.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_Batch.AppearanceCell.FontSizeDelta")));
            this.col_Batch.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_Batch.AppearanceCell.FontStyleDelta")));
            this.col_Batch.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_Batch.AppearanceCell.GradientMode")));
            this.col_Batch.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_Batch.AppearanceCell.Image")));
            this.col_Batch.AppearanceCell.Options.UseTextOptions = true;
            this.col_Batch.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_Batch.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_Batch.AppearanceHeader.FontSizeDelta")));
            this.col_Batch.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_Batch.AppearanceHeader.FontStyleDelta")));
            this.col_Batch.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_Batch.AppearanceHeader.GradientMode")));
            this.col_Batch.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_Batch.AppearanceHeader.Image")));
            this.col_Batch.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Batch.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_Batch, "col_Batch");
            this.col_Batch.ColumnEdit = this.RepBatch;
            this.col_Batch.FieldName = "Batch";
            this.col_Batch.Name = "col_Batch";
            // 
            // RepBatch
            // 
            resources.ApplyResources(this.RepBatch, "RepBatch");
            this.RepBatch.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("RepBatch.Buttons"))))});
            this.RepBatch.DisplayMember = "Batch";
            this.RepBatch.Name = "RepBatch";
            this.RepBatch.ValueMember = "Batch";
            this.RepBatch.View = this.gridView3;
            // 
            // gridView3
            // 
            resources.ApplyResources(this.gridView3, "gridView3");
            this.gridView3.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            // 
            // col_Serial
            // 
            this.col_Serial.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_Serial.AppearanceCell.FontSizeDelta")));
            this.col_Serial.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_Serial.AppearanceCell.FontStyleDelta")));
            this.col_Serial.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_Serial.AppearanceCell.GradientMode")));
            this.col_Serial.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_Serial.AppearanceCell.Image")));
            this.col_Serial.AppearanceCell.Options.UseTextOptions = true;
            this.col_Serial.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_Serial.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_Serial.AppearanceHeader.FontSizeDelta")));
            this.col_Serial.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_Serial.AppearanceHeader.FontStyleDelta")));
            this.col_Serial.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_Serial.AppearanceHeader.GradientMode")));
            this.col_Serial.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_Serial.AppearanceHeader.Image")));
            this.col_Serial.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Serial.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_Serial, "col_Serial");
            this.col_Serial.ColumnEdit = this.repSerial;
            this.col_Serial.FieldName = "Serial";
            this.col_Serial.Name = "col_Serial";
            // 
            // repSerial
            // 
            resources.ApplyResources(this.repSerial, "repSerial");
            this.repSerial.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repSerial.Buttons"))))});
            this.repSerial.Name = "repSerial";
            // 
            // col_QC
            // 
            this.col_QC.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_QC.AppearanceCell.FontSizeDelta")));
            this.col_QC.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_QC.AppearanceCell.FontStyleDelta")));
            this.col_QC.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_QC.AppearanceCell.GradientMode")));
            this.col_QC.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_QC.AppearanceCell.Image")));
            this.col_QC.AppearanceCell.Options.UseTextOptions = true;
            this.col_QC.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_QC.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_QC.AppearanceHeader.FontSizeDelta")));
            this.col_QC.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_QC.AppearanceHeader.FontStyleDelta")));
            this.col_QC.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_QC.AppearanceHeader.GradientMode")));
            this.col_QC.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_QC.AppearanceHeader.Image")));
            this.col_QC.AppearanceHeader.Options.UseTextOptions = true;
            this.col_QC.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_QC, "col_QC");
            this.col_QC.ColumnEdit = this.repQC;
            this.col_QC.FieldName = "Qc";
            this.col_QC.Name = "col_QC";
            // 
            // repQC
            // 
            resources.ApplyResources(this.repQC, "repQC");
            this.repQC.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repQC.Buttons"))))});
            this.repQC.Name = "repQC";
            // 
            // col_Expire
            // 
            this.col_Expire.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_Expire.AppearanceCell.FontSizeDelta")));
            this.col_Expire.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_Expire.AppearanceCell.FontStyleDelta")));
            this.col_Expire.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_Expire.AppearanceCell.GradientMode")));
            this.col_Expire.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_Expire.AppearanceCell.Image")));
            this.col_Expire.AppearanceCell.Options.UseTextOptions = true;
            this.col_Expire.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_Expire.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_Expire.AppearanceHeader.FontSizeDelta")));
            this.col_Expire.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_Expire.AppearanceHeader.FontStyleDelta")));
            this.col_Expire.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_Expire.AppearanceHeader.GradientMode")));
            this.col_Expire.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_Expire.AppearanceHeader.Image")));
            this.col_Expire.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Expire.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_Expire, "col_Expire");
            this.col_Expire.ColumnEdit = this.RepXpireDate;
            this.col_Expire.FieldName = "Expire";
            this.col_Expire.Name = "col_Expire";
            this.col_Expire.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // RepXpireDate
            // 
            resources.ApplyResources(this.RepXpireDate, "RepXpireDate");
            this.RepXpireDate.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("RepXpireDate.Buttons"))))});
            this.RepXpireDate.CalendarTimeProperties.AccessibleDescription = resources.GetString("RepXpireDate.CalendarTimeProperties.AccessibleDescription");
            this.RepXpireDate.CalendarTimeProperties.AccessibleName = resources.GetString("RepXpireDate.CalendarTimeProperties.AccessibleName");
            this.RepXpireDate.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("RepXpireDate.CalendarTimeProperties.AutoHeight")));
            this.RepXpireDate.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("RepXpireDate.CalendarTimeProperties.Buttons"))))});
            this.RepXpireDate.CalendarTimeProperties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("RepXpireDate.CalendarTimeProperties.Mask.AutoComplete")));
            this.RepXpireDate.CalendarTimeProperties.Mask.BeepOnError = ((bool)(resources.GetObject("RepXpireDate.CalendarTimeProperties.Mask.BeepOnError")));
            this.RepXpireDate.CalendarTimeProperties.Mask.EditMask = resources.GetString("RepXpireDate.CalendarTimeProperties.Mask.EditMask");
            this.RepXpireDate.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("RepXpireDate.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.RepXpireDate.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("RepXpireDate.CalendarTimeProperties.Mask.MaskType")));
            this.RepXpireDate.CalendarTimeProperties.Mask.PlaceHolder = ((char)(resources.GetObject("RepXpireDate.CalendarTimeProperties.Mask.PlaceHolder")));
            this.RepXpireDate.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("RepXpireDate.CalendarTimeProperties.Mask.SaveLiteral")));
            this.RepXpireDate.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("RepXpireDate.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.RepXpireDate.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("RepXpireDate.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat")));
            this.RepXpireDate.CalendarTimeProperties.NullValuePrompt = resources.GetString("RepXpireDate.CalendarTimeProperties.NullValuePrompt");
            this.RepXpireDate.CalendarTimeProperties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("RepXpireDate.CalendarTimeProperties.NullValuePromptShowForEmptyValue")));
            this.RepXpireDate.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("RepXpireDate.Mask.AutoComplete")));
            this.RepXpireDate.Mask.BeepOnError = ((bool)(resources.GetObject("RepXpireDate.Mask.BeepOnError")));
            this.RepXpireDate.Mask.EditMask = resources.GetString("RepXpireDate.Mask.EditMask");
            this.RepXpireDate.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("RepXpireDate.Mask.IgnoreMaskBlank")));
            this.RepXpireDate.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("RepXpireDate.Mask.MaskType")));
            this.RepXpireDate.Mask.PlaceHolder = ((char)(resources.GetObject("RepXpireDate.Mask.PlaceHolder")));
            this.RepXpireDate.Mask.SaveLiteral = ((bool)(resources.GetObject("RepXpireDate.Mask.SaveLiteral")));
            this.RepXpireDate.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("RepXpireDate.Mask.ShowPlaceHolders")));
            this.RepXpireDate.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("RepXpireDate.Mask.UseMaskAsDisplayFormat")));
            this.RepXpireDate.Name = "RepXpireDate";
            // 
            // col_Vendor
            // 
            this.col_Vendor.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_Vendor.AppearanceCell.FontSizeDelta")));
            this.col_Vendor.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_Vendor.AppearanceCell.FontStyleDelta")));
            this.col_Vendor.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_Vendor.AppearanceCell.GradientMode")));
            this.col_Vendor.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_Vendor.AppearanceCell.Image")));
            this.col_Vendor.AppearanceCell.Options.UseTextOptions = true;
            this.col_Vendor.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_Vendor.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_Vendor.AppearanceHeader.FontSizeDelta")));
            this.col_Vendor.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_Vendor.AppearanceHeader.FontStyleDelta")));
            this.col_Vendor.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_Vendor.AppearanceHeader.GradientMode")));
            this.col_Vendor.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_Vendor.AppearanceHeader.Image")));
            this.col_Vendor.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Vendor.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_Vendor, "col_Vendor");
            this.col_Vendor.ColumnEdit = this.repVendor;
            this.col_Vendor.FieldName = "VendorId";
            this.col_Vendor.Name = "col_Vendor";
            // 
            // repVendor
            // 
            resources.ApplyResources(this.repVendor, "repVendor");
            this.repVendor.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repVendor.Buttons"))))});
            this.repVendor.Name = "repVendor";
            this.repVendor.View = this.gridView5;
            // 
            // gridView5
            // 
            resources.ApplyResources(this.gridView5, "gridView5");
            this.gridView5.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn18});
            this.gridView5.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView5.Name = "gridView5";
            this.gridView5.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView5.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView5.OptionsBehavior.AutoSelectAllInEditor = false;
            this.gridView5.OptionsBehavior.AutoUpdateTotalSummary = false;
            this.gridView5.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView5.OptionsSelection.UseIndicatorForSelection = false;
            this.gridView5.OptionsView.ShowAutoFilterRow = true;
            this.gridView5.OptionsView.ShowDetailButtons = false;
            this.gridView5.OptionsView.ShowGroupPanel = false;
            this.gridView5.OptionsView.ShowIndicator = false;
            // 
            // gridColumn18
            // 
            resources.ApplyResources(this.gridColumn18, "gridColumn18");
            this.gridColumn18.FieldName = "VenNameAr";
            this.gridColumn18.Name = "gridColumn18";
            // 
            // gridColumn22
            // 
            this.gridColumn22.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn22.AppearanceCell.FontSizeDelta")));
            this.gridColumn22.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn22.AppearanceCell.FontStyleDelta")));
            this.gridColumn22.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn22.AppearanceCell.GradientMode")));
            this.gridColumn22.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn22.AppearanceCell.Image")));
            this.gridColumn22.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn22.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn22.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn22.AppearanceHeader.FontSizeDelta")));
            this.gridColumn22.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn22.AppearanceHeader.FontStyleDelta")));
            this.gridColumn22.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn22.AppearanceHeader.GradientMode")));
            this.gridColumn22.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn22.AppearanceHeader.Image")));
            this.gridColumn22.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn22.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn22, "gridColumn22");
            this.gridColumn22.FieldName = "Pack";
            this.gridColumn22.Name = "gridColumn22";
            // 
            // RepXpire
            // 
            resources.ApplyResources(this.RepXpire, "RepXpire");
            this.RepXpire.Appearance.FontSizeDelta = ((int)(resources.GetObject("RepXpire.Appearance.FontSizeDelta")));
            this.RepXpire.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("RepXpire.Appearance.FontStyleDelta")));
            this.RepXpire.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("RepXpire.Appearance.GradientMode")));
            this.RepXpire.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("RepXpire.Appearance.Image")));
            this.RepXpire.Appearance.Options.UseTextOptions = true;
            this.RepXpire.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.RepXpire.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("RepXpire.Buttons"))))});
            this.RepXpire.Name = "RepXpire";
            this.RepXpire.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.RepXpire.View = this.gridView1;
            this.RepXpire.CustomDisplayText += new DevExpress.XtraEditors.Controls.CustomDisplayTextEventHandler(this.RepXpireDate_CustomDisplayText);
            // 
            // gridView1
            // 
            resources.ApplyResources(this.gridView1, "gridView1");
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn6});
            this.gridView1.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.CustomRowFilter += new DevExpress.XtraGrid.Views.Base.RowFilterEventHandler(this.gridView1_CustomRowFilter);
            // 
            // gridColumn2
            // 
            this.gridColumn2.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn2.AppearanceCell.FontSizeDelta")));
            this.gridColumn2.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn2.AppearanceCell.FontStyleDelta")));
            this.gridColumn2.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn2.AppearanceCell.GradientMode")));
            this.gridColumn2.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn2.AppearanceCell.Image")));
            this.gridColumn2.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn2.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn2.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn2.AppearanceHeader.FontSizeDelta")));
            this.gridColumn2.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn2.AppearanceHeader.FontStyleDelta")));
            this.gridColumn2.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn2.AppearanceHeader.GradientMode")));
            this.gridColumn2.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn2.AppearanceHeader.Image")));
            this.gridColumn2.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.gridColumn2, "gridColumn2");
            this.gridColumn2.DisplayFormat.FormatString = "dd/MM/YYY";
            this.gridColumn2.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.gridColumn2.FieldName = "ExpireDate";
            this.gridColumn2.Name = "gridColumn2";
            // 
            // gridColumn3
            // 
            resources.ApplyResources(this.gridColumn3, "gridColumn3");
            this.gridColumn3.FieldName = "ItemId";
            this.gridColumn3.Name = "gridColumn3";
            // 
            // gridColumn4
            // 
            resources.ApplyResources(this.gridColumn4, "gridColumn4");
            this.gridColumn4.FieldName = "Visible";
            this.gridColumn4.Name = "gridColumn4";
            // 
            // gridColumn6
            // 
            this.gridColumn6.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn6.AppearanceCell.FontSizeDelta")));
            this.gridColumn6.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn6.AppearanceCell.FontStyleDelta")));
            this.gridColumn6.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn6.AppearanceCell.GradientMode")));
            this.gridColumn6.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn6.AppearanceCell.Image")));
            this.gridColumn6.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn6.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn6.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn6.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn6.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn6.AppearanceHeader.FontSizeDelta")));
            this.gridColumn6.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn6.AppearanceHeader.FontStyleDelta")));
            this.gridColumn6.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn6.AppearanceHeader.GradientMode")));
            this.gridColumn6.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn6.AppearanceHeader.Image")));
            this.gridColumn6.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn6.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn6.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            resources.ApplyResources(this.gridColumn6, "gridColumn6");
            this.gridColumn6.FieldName = "Qty";
            this.gridColumn6.Name = "gridColumn6";
            // 
            // repositoryItemLookUpEdit1
            // 
            resources.ApplyResources(this.repositoryItemLookUpEdit1, "repositoryItemLookUpEdit1");
            this.repositoryItemLookUpEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repositoryItemLookUpEdit1.Buttons"))))});
            this.repositoryItemLookUpEdit1.Name = "repositoryItemLookUpEdit1";
            // 
            // repVendorold
            // 
            resources.ApplyResources(this.repVendorold, "repVendorold");
            this.repVendorold.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repVendorold.Buttons"))))});
            this.repVendorold.DisplayMember = "VendorNameAR";
            this.repVendorold.Name = "repVendorold";
            this.repVendorold.ValueMember = "VendorId";
            // 
            // repositoryItemMarqueeProgressBar1
            // 
            resources.ApplyResources(this.repositoryItemMarqueeProgressBar1, "repositoryItemMarqueeProgressBar1");
            this.repositoryItemMarqueeProgressBar1.Name = "repositoryItemMarqueeProgressBar1";
            // 
            // r
            // 
            resources.ApplyResources(this.r, "r");
            this.r.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("r.Buttons"))))});
            this.r.Name = "r";
            // 
            // groupBox1
            // 
            resources.ApplyResources(this.groupBox1, "groupBox1");
            this.groupBox1.Controls.Add(this.lkp_Store);
            this.groupBox1.Controls.Add(this.pnlAccount);
            this.groupBox1.Controls.Add(this.labelControl2);
            this.groupBox1.Controls.Add(this.labelControl1);
            this.groupBox1.Controls.Add(this.txtNote);
            this.groupBox1.Controls.Add(this.dt_ST_Date);
            this.groupBox1.Controls.Add(this.labelControl15);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.TabStop = false;
            // 
            // lkp_Store
            // 
            resources.ApplyResources(this.lkp_Store, "lkp_Store");
            this.lkp_Store.EnterMoveNextControl = true;
            this.lkp_Store.MenuManager = this.barManager1;
            this.lkp_Store.Name = "lkp_Store";
            this.lkp_Store.Properties.AccessibleDescription = resources.GetString("lkp_Store.Properties.AccessibleDescription");
            this.lkp_Store.Properties.AccessibleName = resources.GetString("lkp_Store.Properties.AccessibleName");
            this.lkp_Store.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkp_Store.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_Store.Properties.Appearance.FontSizeDelta")));
            this.lkp_Store.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_Store.Properties.Appearance.FontStyleDelta")));
            this.lkp_Store.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_Store.Properties.Appearance.GradientMode")));
            this.lkp_Store.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_Store.Properties.Appearance.Image")));
            this.lkp_Store.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_Store.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_Store.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_Store.Properties.AutoHeight")));
            this.lkp_Store.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_Store.Properties.Buttons"))))});
            this.lkp_Store.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Store.Properties.Columns"), resources.GetString("lkp_Store.Properties.Columns1"), ((int)(resources.GetObject("lkp_Store.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_Store.Properties.Columns3"))), resources.GetString("lkp_Store.Properties.Columns4"), ((bool)(resources.GetObject("lkp_Store.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_Store.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Store.Properties.Columns7"), resources.GetString("lkp_Store.Properties.Columns8"), ((int)(resources.GetObject("lkp_Store.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_Store.Properties.Columns10"))), resources.GetString("lkp_Store.Properties.Columns11"), ((bool)(resources.GetObject("lkp_Store.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_Store.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Store.Properties.Columns14"), resources.GetString("lkp_Store.Properties.Columns15"), ((int)(resources.GetObject("lkp_Store.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_Store.Properties.Columns17"))), resources.GetString("lkp_Store.Properties.Columns18"), ((bool)(resources.GetObject("lkp_Store.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_Store.Properties.Columns20")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Store.Properties.Columns21"), resources.GetString("lkp_Store.Properties.Columns22"), ((int)(resources.GetObject("lkp_Store.Properties.Columns23"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_Store.Properties.Columns24"))), resources.GetString("lkp_Store.Properties.Columns25"), ((bool)(resources.GetObject("lkp_Store.Properties.Columns26"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_Store.Properties.Columns27")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Store.Properties.Columns28"), resources.GetString("lkp_Store.Properties.Columns29"), ((int)(resources.GetObject("lkp_Store.Properties.Columns30"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_Store.Properties.Columns31"))), resources.GetString("lkp_Store.Properties.Columns32"), ((bool)(resources.GetObject("lkp_Store.Properties.Columns33"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_Store.Properties.Columns34")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Store.Properties.Columns35"), resources.GetString("lkp_Store.Properties.Columns36"), ((int)(resources.GetObject("lkp_Store.Properties.Columns37"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_Store.Properties.Columns38"))), resources.GetString("lkp_Store.Properties.Columns39"), ((bool)(resources.GetObject("lkp_Store.Properties.Columns40"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_Store.Properties.Columns41")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Store.Properties.Columns42"), resources.GetString("lkp_Store.Properties.Columns43"), ((int)(resources.GetObject("lkp_Store.Properties.Columns44"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_Store.Properties.Columns45"))), resources.GetString("lkp_Store.Properties.Columns46"), ((bool)(resources.GetObject("lkp_Store.Properties.Columns47"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_Store.Properties.Columns48"))))});
            this.lkp_Store.Properties.NullText = resources.GetString("lkp_Store.Properties.NullText");
            this.lkp_Store.Properties.NullValuePrompt = resources.GetString("lkp_Store.Properties.NullValuePrompt");
            this.lkp_Store.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_Store.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // pnlAccount
            // 
            resources.ApplyResources(this.pnlAccount, "pnlAccount");
            this.pnlAccount.Controls.Add(this.labelControl3);
            this.pnlAccount.Controls.Add(this.lkp_Account);
            this.pnlAccount.Name = "pnlAccount";
            // 
            // labelControl3
            // 
            resources.ApplyResources(this.labelControl3, "labelControl3");
            this.labelControl3.Name = "labelControl3";
            // 
            // lkp_Account
            // 
            resources.ApplyResources(this.lkp_Account, "lkp_Account");
            this.lkp_Account.EnterMoveNextControl = true;
            this.lkp_Account.MenuManager = this.barManager1;
            this.lkp_Account.Name = "lkp_Account";
            this.lkp_Account.Properties.AccessibleDescription = resources.GetString("lkp_Account.Properties.AccessibleDescription");
            this.lkp_Account.Properties.AccessibleName = resources.GetString("lkp_Account.Properties.AccessibleName");
            this.lkp_Account.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkp_Account.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_Account.Properties.Appearance.FontSizeDelta")));
            this.lkp_Account.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_Account.Properties.Appearance.FontStyleDelta")));
            this.lkp_Account.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_Account.Properties.Appearance.GradientMode")));
            this.lkp_Account.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_Account.Properties.Appearance.Image")));
            this.lkp_Account.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_Account.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_Account.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_Account.Properties.AutoHeight")));
            this.lkp_Account.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_Account.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_Account.Properties.Buttons"))))});
            this.lkp_Account.Properties.ImmediatePopup = true;
            this.lkp_Account.Properties.NullText = resources.GetString("lkp_Account.Properties.NullText");
            this.lkp_Account.Properties.NullValuePrompt = resources.GetString("lkp_Account.Properties.NullValuePrompt");
            this.lkp_Account.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_Account.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_Account.Properties.View = this.gridView6;
            // 
            // gridView6
            // 
            this.gridView6.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView6.Appearance.HeaderPanel.FontSizeDelta")));
            this.gridView6.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView6.Appearance.HeaderPanel.FontStyleDelta")));
            this.gridView6.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView6.Appearance.HeaderPanel.GradientMode")));
            this.gridView6.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView6.Appearance.HeaderPanel.Image")));
            this.gridView6.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView6.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView6.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gridView6.Appearance.Row.FontSizeDelta")));
            this.gridView6.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView6.Appearance.Row.FontStyleDelta")));
            this.gridView6.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView6.Appearance.Row.GradientMode")));
            this.gridView6.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView6.Appearance.Row.Image")));
            this.gridView6.Appearance.Row.Options.UseTextOptions = true;
            this.gridView6.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.gridView6, "gridView6");
            this.gridView6.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21});
            this.gridView6.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView6.Name = "gridView6";
            this.gridView6.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView6.OptionsView.BestFitMaxRowCount = 10;
            this.gridView6.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView6.OptionsView.EnableAppearanceOddRow = true;
            this.gridView6.OptionsView.ShowAutoFilterRow = true;
            this.gridView6.OptionsView.ShowGroupPanel = false;
            this.gridView6.OptionsView.ShowIndicator = false;
            // 
            // gridColumn19
            // 
            resources.ApplyResources(this.gridColumn19, "gridColumn19");
            this.gridColumn19.FieldName = "AccId";
            this.gridColumn19.Name = "gridColumn19";
            // 
            // gridColumn20
            // 
            resources.ApplyResources(this.gridColumn20, "gridColumn20");
            this.gridColumn20.FieldName = "AccName";
            this.gridColumn20.Name = "gridColumn20";
            // 
            // gridColumn21
            // 
            resources.ApplyResources(this.gridColumn21, "gridColumn21");
            this.gridColumn21.FieldName = "AccNumber";
            this.gridColumn21.Name = "gridColumn21";
            // 
            // labelControl2
            // 
            resources.ApplyResources(this.labelControl2, "labelControl2");
            this.labelControl2.Name = "labelControl2";
            // 
            // labelControl1
            // 
            resources.ApplyResources(this.labelControl1, "labelControl1");
            this.labelControl1.Name = "labelControl1";
            // 
            // txtNote
            // 
            resources.ApplyResources(this.txtNote, "txtNote");
            this.txtNote.EnterMoveNextControl = true;
            this.txtNote.MenuManager = this.barManager1;
            this.txtNote.Name = "txtNote";
            this.txtNote.Properties.AccessibleDescription = resources.GetString("txtNote.Properties.AccessibleDescription");
            this.txtNote.Properties.AccessibleName = resources.GetString("txtNote.Properties.AccessibleName");
            this.txtNote.Properties.AutoHeight = ((bool)(resources.GetObject("txtNote.Properties.AutoHeight")));
            this.txtNote.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtNote.Properties.Mask.AutoComplete")));
            this.txtNote.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtNote.Properties.Mask.BeepOnError")));
            this.txtNote.Properties.Mask.EditMask = resources.GetString("txtNote.Properties.Mask.EditMask");
            this.txtNote.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtNote.Properties.Mask.IgnoreMaskBlank")));
            this.txtNote.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtNote.Properties.Mask.MaskType")));
            this.txtNote.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtNote.Properties.Mask.PlaceHolder")));
            this.txtNote.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtNote.Properties.Mask.SaveLiteral")));
            this.txtNote.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtNote.Properties.Mask.ShowPlaceHolders")));
            this.txtNote.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtNote.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtNote.Properties.NullValuePrompt = resources.GetString("txtNote.Properties.NullValuePrompt");
            this.txtNote.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtNote.Properties.NullValuePromptShowForEmptyValue")));
            this.txtNote.Modified += new System.EventHandler(this.lkp_Store_Modified);
            // 
            // dt_ST_Date
            // 
            resources.ApplyResources(this.dt_ST_Date, "dt_ST_Date");
            this.dt_ST_Date.EnterMoveNextControl = true;
            this.dt_ST_Date.MenuManager = this.barManager1;
            this.dt_ST_Date.Name = "dt_ST_Date";
            this.dt_ST_Date.Properties.AccessibleDescription = resources.GetString("dt_ST_Date.Properties.AccessibleDescription");
            this.dt_ST_Date.Properties.AccessibleName = resources.GetString("dt_ST_Date.Properties.AccessibleName");
            this.dt_ST_Date.Properties.AutoHeight = ((bool)(resources.GetObject("dt_ST_Date.Properties.AutoHeight")));
            this.dt_ST_Date.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("dt_ST_Date.Properties.Buttons"))))});
            this.dt_ST_Date.Properties.CalendarTimeProperties.AccessibleDescription = resources.GetString("dt_ST_Date.Properties.CalendarTimeProperties.AccessibleDescription");
            this.dt_ST_Date.Properties.CalendarTimeProperties.AccessibleName = resources.GetString("dt_ST_Date.Properties.CalendarTimeProperties.AccessibleName");
            this.dt_ST_Date.Properties.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("dt_ST_Date.Properties.CalendarTimeProperties.AutoHeight")));
            this.dt_ST_Date.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dt_ST_Date.Properties.CalendarTimeProperties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dt_ST_Date.Properties.CalendarTimeProperties.Mask.AutoComplete")));
            this.dt_ST_Date.Properties.CalendarTimeProperties.Mask.BeepOnError = ((bool)(resources.GetObject("dt_ST_Date.Properties.CalendarTimeProperties.Mask.BeepOnError")));
            this.dt_ST_Date.Properties.CalendarTimeProperties.Mask.EditMask = resources.GetString("dt_ST_Date.Properties.CalendarTimeProperties.Mask.EditMask");
            this.dt_ST_Date.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dt_ST_Date.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.dt_ST_Date.Properties.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dt_ST_Date.Properties.CalendarTimeProperties.Mask.MaskType")));
            this.dt_ST_Date.Properties.CalendarTimeProperties.Mask.PlaceHolder = ((char)(resources.GetObject("dt_ST_Date.Properties.CalendarTimeProperties.Mask.PlaceHolder")));
            this.dt_ST_Date.Properties.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("dt_ST_Date.Properties.CalendarTimeProperties.Mask.SaveLiteral")));
            this.dt_ST_Date.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dt_ST_Date.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.dt_ST_Date.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dt_ST_Date.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat")));
            this.dt_ST_Date.Properties.CalendarTimeProperties.NullValuePrompt = resources.GetString("dt_ST_Date.Properties.CalendarTimeProperties.NullValuePrompt");
            this.dt_ST_Date.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dt_ST_Date.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue")));
            this.dt_ST_Date.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dt_ST_Date.Properties.Mask.AutoComplete")));
            this.dt_ST_Date.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("dt_ST_Date.Properties.Mask.BeepOnError")));
            this.dt_ST_Date.Properties.Mask.EditMask = resources.GetString("dt_ST_Date.Properties.Mask.EditMask");
            this.dt_ST_Date.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dt_ST_Date.Properties.Mask.IgnoreMaskBlank")));
            this.dt_ST_Date.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dt_ST_Date.Properties.Mask.MaskType")));
            this.dt_ST_Date.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("dt_ST_Date.Properties.Mask.PlaceHolder")));
            this.dt_ST_Date.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("dt_ST_Date.Properties.Mask.SaveLiteral")));
            this.dt_ST_Date.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dt_ST_Date.Properties.Mask.ShowPlaceHolders")));
            this.dt_ST_Date.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dt_ST_Date.Properties.Mask.UseMaskAsDisplayFormat")));
            this.dt_ST_Date.Properties.NullValuePrompt = resources.GetString("dt_ST_Date.Properties.NullValuePrompt");
            this.dt_ST_Date.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dt_ST_Date.Properties.NullValuePromptShowForEmptyValue")));
            this.dt_ST_Date.Modified += new System.EventHandler(this.lkp_Store_Modified);
            // 
            // labelControl15
            // 
            resources.ApplyResources(this.labelControl15, "labelControl15");
            this.labelControl15.Name = "labelControl15";
            // 
            // frm_IC_ItemReplacment
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.panelControl2);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.KeyPreview = true;
            this.Name = "frm_IC_ItemReplacment";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_IC_ItemReplacment_FormClosing);
            this.Load += new System.EventHandler(this.frm_PR_Invoice_Load);
            this.Shown += new System.EventHandler(this.frm_IC_ItemReplacment_Shown);
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.frm_PR_Invoice_KeyUp);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.linqServerModeSource1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).EndInit();
            this.panelControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grdPrInvoice)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repUOM)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repItems)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.RepBatch)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repSerial)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repQC)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.RepXpireDate.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.RepXpireDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repVendor)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.RepXpire)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemLookUpEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repVendorold)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMarqueeProgressBar1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.r)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Store.Properties)).EndInit();
            this.pnlAccount.ResumeLayout(false);
            this.pnlAccount.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Account.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNote.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dt_ST_Date.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dt_ST_Date.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnSave;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtnHelp;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraBars.BarButtonItem batBtnList;
        private DevExpress.XtraBars.BarButtonItem barBtnCommit;
        private DevExpress.XtraEditors.PanelControl panelControl2;
        private DevExpress.XtraGrid.GridControl grdPrInvoice;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit repUOM;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit repItems;
        private DevExpress.XtraGrid.Views.Grid.GridView repositoryItemGridLookUpEdit1View;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private System.Windows.Forms.GroupBox groupBox1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.TextEdit txtNote;
        private DevExpress.XtraEditors.DateEdit dt_ST_Date;
        private DevExpress.XtraEditors.LabelControl labelControl15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit RepXpire;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumnPurchasePrice;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.Data.Linq.LinqServerModeSource linqServerModeSource1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraBars.BarButtonItem barBtnPrint;
        private DevExpress.XtraBars.BarButtonItem barBtnDelete;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit RepBatch;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repositoryItemLookUpEdit1;
        private DevExpress.XtraGrid.Columns.GridColumn col_Batch;
        private DevExpress.XtraGrid.Columns.GridColumn col_Serial;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repSerial;
        private DevExpress.XtraGrid.Columns.GridColumn col_QC;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repQC;
        private DevExpress.XtraGrid.Columns.GridColumn col_Vendor;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repVendorold;
        private DevExpress.XtraGrid.Columns.GridColumn col_Expire;
        private DevExpress.XtraEditors.Repository.RepositoryItemMarqueeProgressBar repositoryItemMarqueeProgressBar1;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit r;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit repVendor;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraEditors.Repository.RepositoryItemDateEdit RepXpireDate;
        private System.Windows.Forms.Panel pnlAccount;
        private DevExpress.XtraEditors.GridLookUpEdit lkp_Account;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraEditors.LookUpEdit lkp_Store;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraGrid.Columns.GridColumn col_LibraQty;
        private DevExpress.XtraGrid.Columns.GridColumn col_kg_Weight_libra;
        private DevExpress.XtraGrid.Columns.GridColumn col_VariableWeight;
        private DevExpress.XtraGrid.Columns.GridColumn col_PiecesCount;
        private DevExpress.XtraGrid.Columns.GridColumn col_PricingWithSmall;
        private DevExpress.XtraGrid.Columns.GridColumn col_ActualWeight;
        private DevExpress.XtraGrid.Columns.GridColumn col_ActualPieces;
        private DevExpress.XtraGrid.Columns.GridColumn col_Diff;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
    }
}