<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="DevExpress.Utils.v10.1" name="DevExpress.Utils.v10.1, Version=10.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="winControlContainer1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v10.1">
    <value>0, 0</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="winControlContainer1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>787, 194.1667</value>
  </data>
  <data name="grid_openBalance.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="gridColumn2.Caption" xml:space="preserve">
    <value>Value</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="gridColumn2.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn2.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn2.Width" type="System.Int32, mscorlib">
    <value>337</value>
  </data>
  <data name="gridColumn1.Caption" xml:space="preserve">
    <value>Account Name</value>
  </data>
  <data name="gridColumn1.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn1.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn1.Width" type="System.Int32, mscorlib">
    <value>384</value>
  </data>
  <data name="colIndex.Caption" xml:space="preserve">
    <value>#</value>
  </data>
  <data name="colIndex.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colIndex.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="colIndex.Width" type="System.Int32, mscorlib">
    <value>35</value>
  </data>
  <data name="gridView1.GroupPanelText" xml:space="preserve">
    <value>اسحب أحد الأعمدة لتجميع البيانات على أساسه</value>
  </data>
  <data name="grid_openBalance.Size" type="System.Drawing.Size, System.Drawing">
    <value>756, 186</value>
  </data>
  <data name="grid_openBalance.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;grid_openBalance.Name" xml:space="preserve">
    <value>grid_openBalance</value>
  </data>
  <data name="&gt;&gt;grid_openBalance.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.GridControl, DevExpress.XtraGrid.v10.1, Version=10.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="Detail.HeightF" type="System.Single, mscorlib">
    <value>268</value>
  </data>
  <assembly alias="DevExpress.Data.v10.1" name="DevExpress.Data.v10.1, Version=10.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="Detail.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v10.1">
    <value>TopLeft</value>
  </data>
  <data name="lblReportName.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lblReportName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v10.1">
    <value>87.5, 41.5</value>
  </data>
  <data name="lblReportName.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>600.0001, 19.00002</value>
  </data>
  <data name="lblReportName.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v10.1">
    <value>TopCenter</value>
  </data>
  <data name="lblDateFilter.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt</value>
  </data>
  <data name="lblDateFilter.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v10.1">
    <value>87.5, 63.5</value>
  </data>
  <data name="lblDateFilter.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>600.0001, 16.75</value>
  </data>
  <data name="lblDateFilter.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v10.1">
    <value>TopCenter</value>
  </data>
  <data name="picLogo.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v10.1">
    <value>12.5, 9.5</value>
  </data>
  <data name="picLogo.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>70, 70</value>
  </data>
  <data name="lblFilter.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt</value>
  </data>
  <data name="lblFilter.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v10.1">
    <value>12.5, 82.5</value>
  </data>
  <data name="lblFilter.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>764.5, 25.75</value>
  </data>
  <data name="lblFilter.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v10.1">
    <value>TopCenter</value>
  </data>
  <data name="lblCompName.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 18pt</value>
  </data>
  <data name="lblCompName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v10.1">
    <value>87.5, 9.5</value>
  </data>
  <data name="lblCompName.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>600.0001, 30</value>
  </data>
  <data name="lblCompName.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v10.1">
    <value>TopCenter</value>
  </data>
  <data name="TopMargin.HeightF" type="System.Single, mscorlib">
    <value>110</value>
  </data>
  <data name="TopMargin.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v10.1">
    <value>TopLeft</value>
  </data>
  <data name="xrPageInfo2.Format" xml:space="preserve">
    <value>Printed at {0:dd MM yyyy  h:mm tt}</value>
  </data>
  <data name="xrPageInfo2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v10.1">
    <value>12.5, 12.5</value>
  </data>
  <data name="xrPageInfo2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>217.6667, 23</value>
  </data>
  <data name="xrPageInfo2.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v10.1">
    <value>MiddleLeft</value>
  </data>
  <data name="xrPageInfo1.Format" xml:space="preserve">
    <value>Page {0} of {1} </value>
  </data>
  <data name="xrPageInfo1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v10.1">
    <value>337.5, 12.5</value>
  </data>
  <data name="xrPageInfo1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>109.375, 23</value>
  </data>
  <data name="xrPageInfo1.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v10.1">
    <value>MiddleCenter</value>
  </data>
  <data name="BottomMargin.HeightF" type="System.Single, mscorlib">
    <value>50</value>
  </data>
  <data name="BottomMargin.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v10.1">
    <value>TopLeft</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.Margins" type="System.Drawing.Printing.Margins, System.Drawing">
    <value>20, 20, 110, 50</value>
  </data>
</root>