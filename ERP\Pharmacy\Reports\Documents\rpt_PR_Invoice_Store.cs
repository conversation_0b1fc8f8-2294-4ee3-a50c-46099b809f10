using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using DAL;
using System.Linq;
using System.Data;
using DevExpress.XtraEditors;

using System.Windows.Forms;
using System.Collections.Generic;

namespace Reports
{
    public partial class rpt_PR_Invoice_Store : DevExpress.XtraReports.UI.XtraReport
    {
        string customer, serial, InvoiceCode, InvoiceDate, store, paymethod, drawer, notes,
            total, taxR, taxV, discountR, discountV, expensesR, expensesV, net, paied, remains, userName, SalesEmp,
            Shipping, PurchaseOrderNo, DeliverDate, salesEmp_Job, DeductTaxV, AddTaxV,
            BalanceBefore, BalanceAfter, DriverName, VehicleNumber, Destination, ProcessName, SourceCode, scalWeightSerial, CusTaxV, CostCenter, retention, advancepayment, dueDate;

        private void rpt_PR_Invoice_Store_BeforePrint(object sender, System.Drawing.Printing.PrintEventArgs e)
        {
                int BranchId = Convert.ToInt32(GetCurrentColumnValue("BranchId"));
                xrSubreport2.ReportSource = new Reports.rpt_PR_Invoice_Store_Sub(InvoiceId, BranchId);
        }

        DataTable dt_inv_details;
        int currId;
        int InvoiceId = 0;

        public rpt_PR_Invoice_Store()
        {
            InitializeComponent();
        }
        public rpt_PR_Invoice_Store(int InvoiceId, string _customer, string _serial, string _number, string _date, string _store, string _paymethod,
            string _drawer, string _notes, string _total, string _taxR, string _taxV, string _discountR, string _discountV,
            string _expensesV, string _net, string _paied, string _remains, DataTable dt, string userName,
            string _DeductTaxV,
            string _AddTaxV, int _currId, string _BalanceBefore, string _BalanceAfter, string DriverName, string VehicleNumber,
            string Destination, string ProcessName, string SourceCode, string scalWeightSerial, string _CusR, string _CostCenter, string _dueDate)
        {
            InitializeComponent();

            this.InvoiceId = InvoiceId;

            customer = _customer;
            serial = _serial;
            InvoiceCode = _number;
            InvoiceDate = _date;
            store = _store;
            paymethod = _paymethod;
            drawer = _drawer;
            notes = _notes;
            total = _total;
            taxR = _taxR;
            taxV = _taxV;
            discountR = _discountR;
            discountV = _discountV;
            expensesV = _expensesV;
            net = _net;
            paied = _paied;
            remains = _remains;
            this.userName = userName;
            dueDate = _dueDate; ;
            DeductTaxV = _DeductTaxV;
            AddTaxV = _AddTaxV;
            CostCenter = _CostCenter;
            this.DriverName = DriverName;
            this.VehicleNumber = VehicleNumber;
            this.Destination = Destination;
            this.ProcessName = ProcessName;
            this.SourceCode = SourceCode;
            this.scalWeightSerial = scalWeightSerial;

            currId = _currId;

            BalanceBefore = _BalanceBefore;
            BalanceAfter = _BalanceAfter;

            dt_inv_details = dt;

            getReportHeader();
            LoadData();
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;
            if (comp != null)
            {
                lblCompName.Text = Shared.IsEnglish ? comp.CmpNameEn : comp.CmpNameAr;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        public void LoadData()
        {
            ERPDataContext db = new ERPDataContext();


            var ddd = dt_inv_details.Select();
            var data = (from i in ddd
                        group i by new { BranchId = i["BranchId"], Branch = i["Branch"] } into grp
                        select new { Branch = grp.Key.Branch.ToString(), BranchId =Convert.ToInt32( grp.Key.BranchId )}
                       ).ToList();
            this.DataSource = data;


            lbl_date.Text = InvoiceDate;
            lbl_notes.Text = notes;
            lbl_Number.Text = InvoiceCode;
            lbl_store.Text = store;
            lbl_Customer.Text = customer;
            lbl_User.Text = userName;

            lbl_SalesEmp.Text = SalesEmp;

            lbl_DeliverDate.Text = DeliverDate;
            lbl_Shipping.Text = Shipping;


            lbl_StoreName.DataBindings.Add("Text", this.DataSource, "Branch");
        }

    }
}
