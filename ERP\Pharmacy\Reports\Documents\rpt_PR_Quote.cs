using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using DAL;
using System.Linq;
using System.Data;
using DevExpress.XtraEditors;

using System.Windows.Forms;

namespace Reports
{
    public partial class rpt_PR_Quote : DevExpress.XtraReports.UI.XtraReport
    {
        string vendor, number, date, store, notes,
            total, tax, discountR, discountV, net, userName, DeductTaxV, AddTaxV;

        DataTable dt_inv_details;

        int currId;

        public rpt_PR_Quote()
        {
            InitializeComponent();
        }
        public rpt_PR_Quote(string _vendor, string _number, string _date, string _store,
            string _notes, string _total, string _tax, string _discountR, string _discountV, string _net,
            DataTable dt, string userName, string _DeductTaxV, string _AddTaxV, int _currId)
        {
            InitializeComponent();
            vendor = _vendor;            
            number = _number;
            date = _date;
            store = _store;

            notes = _notes;
            total = _total;
            tax = _tax;
            discountR = _discountR;
            discountV = _discountV;

            net = _net;

            this.userName = userName;

            DeductTaxV = _DeductTaxV;
            AddTaxV = _AddTaxV;

            currId = _currId;

            dt_inv_details = dt;
            this.DataSource = dt_inv_details;
            //LoadData();            
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                lblCompName.Text = comp.CmpNameAr;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        public void LoadData()
        {
            lbl_date.Text = date;
            lbl_DiscountR.Text = discountR;
            lbl_DiscountV.Text = discountV;


            lbl_Net.Text = net;
            lbl_notes.Text = notes;
            lbl_Number.Text = number;
            
            lbl_store.Text = store;
            lbl_Tax.Text = tax;
            lbl_Total.Text = total;
            lbl_Vendor.Text = vendor;
            lbl_User.Text = userName;
            lbl_DeductTaxV.Text = DeductTaxV;
            lbl_AddTaxV.Text = AddTaxV;

            lblTotalWords.Text = Shared.IsEnglish ? HelperAcc.ConvertMoneyToText(net, currId, Shared.lstCurrency) :
                                   HelperAcc.ConvertMoneyToArabicText(net, currId, Shared.lstCurrency);

            this.DataSource = dt_inv_details;

            cell_code.DataBindings.Add("Text", this.DataSource, "ItemCode1");
            cell_code2.DataBindings.Add("Text", this.DataSource, "ItemCode2");
            cell_Disc.DataBindings.Add("Text", this.DataSource, "DiscountValue");
            cell_Expire.DataBindings.Add("Text", this.DataSource, "Expire");
            cell_Batch.DataBindings.Add("Text", this.DataSource, "Batch");
            cell_Price.DataBindings.Add("Text", this.DataSource, "PurchasePrice");
            cell_Qty.DataBindings.Add("Text", this.DataSource, "Qty");
            cell_Total.DataBindings.Add("Text", this.DataSource, "TotalPurchasePrice");
            cell_ItemName.DataBindings.Add("Text", this.DataSource, "ItemName");
            cell_UOM.DataBindings.Add("Text", this.DataSource, "UOM");
            cell_Height.DataBindings.Add("Text", this.DataSource, "Height");
            cell_Width.DataBindings.Add("Text", this.DataSource, "Width");
            cell_Length.DataBindings.Add("Text", this.DataSource, "Length");
            cell_TotalQty.DataBindings.Add("Text", this.DataSource, "TotalQty");
            cell_DiscountRatio.DataBindings.Add("Text", this.DataSource, "DiscountRatio");
            cell_DiscountRatio2.DataBindings.Add("Text", this.DataSource, "DiscountRatio2");
            cell_DiscountRatio3.DataBindings.Add("Text", this.DataSource, "DiscountRatio3");
            cell_SalesTaxRatio.DataBindings.Add("Text", this.DataSource, "SalesTaxRatio");
            cell_SalesTax.DataBindings.Add("Text", this.DataSource, "SalesTax");

            getReportHeader();
        }

    }
}
