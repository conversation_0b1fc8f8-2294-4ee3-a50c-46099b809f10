﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;
using DAL.Res;

using System.Data.Linq;
using System.Linq;
using DevExpress.XtraGrid.Views.Grid;
using Reports;

namespace Pharmacy.Forms
{
    public partial class frm_IC_ItemMatrixCreate : DevExpress.XtraEditors.XtraForm
    {
        IC_Item item;
        ERPDataContext DB = new ERPDataContext();
        List<MatrixAttribute> lstMD1 = new List<MatrixAttribute>();
        List<MatrixAttribute> lstMD2 = new List<MatrixAttribute>();
        List<MatrixAttribute> lstMD3 = new List<MatrixAttribute>();
        List<GenItem> lstGenItems = new List<GenItem>();

        List<IC_Item> lstSavedMtrxItems = new List<IC_Item>();
        public frm_IC_ItemMatrixCreate(IC_Item item)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            this.item = item;
        }

        private void frm_IC_ItemMatrixCreate_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            //bindDataSources
            var metrices = (from m in DB.IC_Matrixes
                            select new
                            {
                                MatrixId = m.MatrixId,
                                MatrixCode = m.MatrixCode,
                                MatrixName = m.MatrixName
                                //MatrixName = m.MatrixCode + " - " + m.MatrixName
                            }).ToList();
            lkpMatrix1.Properties.DataSource =
                lkpMatrix2.Properties.DataSource =
                lkpMatrix3.Properties.DataSource = metrices;
            lkpMatrix1.Properties.DisplayMember =
                lkpMatrix2.Properties.DisplayMember =
                lkpMatrix3.Properties.DisplayMember = "MatrixName";
            lkpMatrix1.Properties.ValueMember =
                lkpMatrix2.Properties.ValueMember =
                lkpMatrix3.Properties.ValueMember = "MatrixId";

            //load Parent Item Data
            txtParentItemName.Text = item.ItemNameAr;
            if (!string.IsNullOrEmpty(item.ItemCode2) && item.ItemCode2.Length > 5)
                txtMtrxCode0.Text = item.ItemCode2.Trim().Substring(0, 5);
            else if (!string.IsNullOrEmpty(item.ItemCode2) && item.ItemCode2.Length <= 5)
                txtMtrxCode0.Text = item.ItemCode2.Trim();

            txtMtrxSprtr0.Text = txtMtrxSprtr1.Text = txtMtrxSprtr2.Text = "-";

            if (item.mtrxId1 != null)
            {
                lkpMatrix1.EditValue = item.mtrxId1;

                //if parent item already defined, you can't change attributes any more!
                lkpMatrix1.Enabled = lkpMatrix2.Enabled = lkpMatrix3.Enabled = false;
            }
            if (item.mtrxId2 != null)
                lkpMatrix2.EditValue = item.mtrxId2;
            if (item.mtrxId3 != null)
                lkpMatrix3.EditValue = item.mtrxId3;


            if (!string.IsNullOrEmpty(item.mtrxCode0))
                txtMtrxCode0.Text = item.mtrxCode0;
            else
                txtMtrxCode0.Text = string.Empty;

            if (item.mtrxSprtr0 != null)
                txtMtrxSprtr0.Text = item.mtrxSprtr0.Value.ToString();
            else
                txtMtrxSprtr0.Text = string.Empty;

            if (item.mtrxSprtr1 != null)
                txtMtrxSprtr1.Text = item.mtrxSprtr1.Value.ToString();
            else
                txtMtrxSprtr1.Text = string.Empty;

            if (item.mtrxSprtr2 != null)
                txtMtrxSprtr2.Text = item.mtrxSprtr2.Value.ToString();
            else
                txtMtrxSprtr2.Text = string.Empty;

            if (item.mtrxSprtr3 != null)
                txtMtrxSprtr3.Text = item.mtrxSprtr3.Value.ToString();
            else
                txtMtrxSprtr3.Text = string.Empty;


            var mItems = DB.IC_Items.Where(i => i.mtrxParentItem == item.ItemId).Select(i => i.ItemId).ToList();

            if (mItems.Count < 1)
            {
                txtMtrxSprtr0.Text = txtMtrxSprtr1.Text = txtMtrxSprtr2.Text = "-";

                if (!string.IsNullOrEmpty(item.ItemCode2) && item.ItemCode2.Length > 5)
                    txtMtrxCode0.Text = item.ItemCode2.Trim().Substring(0, 5);
                else if (!string.IsNullOrEmpty(item.ItemCode2) && item.ItemCode2.Length <= 5)
                    txtMtrxCode0.Text = item.ItemCode2.Trim();
            }
        }

        private void lkpMatrix1_EditValueChanged(object sender, EventArgs e)
        {
            if ((sender as DevExpress.XtraEditors.LookUpEdit).Name == "lkpMatrix1")
            {
                int matrixId = Convert.ToInt32(lkpMatrix1.EditValue);
                txtMtrx1.Text = lkpMatrix1.Text;
                lstMD1 = (from m in DB.IC_MatrixDetails
                          from d in DB.IC_ItemMatrixDetails.
                          Where(x => x.MatrixDetailId == m.MatrixDetailId && x.ItemId == item.ItemId).DefaultIfEmpty()
                          where m.MatrixId == matrixId
                          select new MatrixAttribute
                          {
                              MatrixId = m.MatrixId,
                              MatrixDetailId = m.MatrixDetailId,
                              MDCode = m.MDCode,
                              MDName = m.MDName,
                              Available = d == null ? false : d.available,
                              Disabled = d == null ? false : (d.available? true: false)

                          }).ToList();
                grd_Mtrx1.DataSource = lstMD1;
            }
            else if ((sender as DevExpress.XtraEditors.LookUpEdit).Name == "lkpMatrix2")
            {
                int matrixId = Convert.ToInt32(lkpMatrix2.EditValue);
                txtMtrx2.Text = lkpMatrix2.Text;
                lstMD2 = (from m in DB.IC_MatrixDetails
                          from d in DB.IC_ItemMatrixDetails.
                          Where(x => x.MatrixDetailId == m.MatrixDetailId && x.ItemId == item.ItemId).DefaultIfEmpty()
                          where m.MatrixId == matrixId
                          select new MatrixAttribute
                          {
                              MatrixId = m.MatrixId,
                              MatrixDetailId = m.MatrixDetailId,
                              MDCode = m.MDCode,
                              MDName = m.MDName,
                              Available = d == null ? false : d.available,
                              Disabled = d == null ? false : (d.available ? true : false)
                          }).ToList();
                grd_Mtrx2.DataSource = lstMD2;
            }
            else if ((sender as DevExpress.XtraEditors.LookUpEdit).Name == "lkpMatrix3")
            {
                int matrixId = Convert.ToInt32(lkpMatrix3.EditValue);
                txtMtrx3.Text = lkpMatrix3.Text;
                lstMD3 = (from m in DB.IC_MatrixDetails
                          from d in DB.IC_ItemMatrixDetails.
                          Where(x => x.MatrixDetailId == m.MatrixDetailId && x.ItemId == item.ItemId).DefaultIfEmpty()
                          where m.MatrixId == matrixId
                          select new MatrixAttribute
                          {
                              MatrixId = m.MatrixId,
                              MatrixDetailId = m.MatrixDetailId,
                              MDCode = m.MDCode,
                              MDName = m.MDName,
                              Available = d == null ? false : d.available,
                              Disabled = d == null ? false : (d.available ? true : false)
                          }).ToList();
                grd_Mtrx3.DataSource = lstMD3;
            }
        }

        private void btn_GenerateItems_Click(object sender, EventArgs e)
        {
            LoadGeneratedItems();
        }

        private void LoadGeneratedItems()
        {
            //user should select at least, the first two metrices
            //user can't select matrix2, without selecting first matrix
            //user can't select matrix3, without selecting first two metrices            
            //user can't select a matrix twice

            if (                
                (lkpMatrix2.EditValue != null && lkpMatrix1.EditValue == null) ||
                (lkpMatrix3.EditValue != null && lkpMatrix2.EditValue == null) ||
                (lkpMatrix3.EditValue != null && (lkpMatrix1.EditValue == null || lkpMatrix2.EditValue == null)) ||
                (lkpMatrix1.EditValue != null && lkpMatrix2.EditValue != null && lkpMatrix1.EditValue.ToString() == lkpMatrix2.EditValue.ToString()) ||
                (lkpMatrix1.EditValue != null && lkpMatrix3.EditValue != null && lkpMatrix1.EditValue.ToString() == lkpMatrix3.EditValue.ToString()) ||
                (lkpMatrix2.EditValue != null && lkpMatrix3.EditValue != null && lkpMatrix2.EditValue.ToString() == lkpMatrix3.EditValue.ToString())
                )
            {
                XtraMessageBox.Show(
                            Shared.IsEnglish == true ? ResICEn.MsgMtrxValid : ResICAr.MsgMtrxValid
                            , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            lstGenItems.Clear();
            col_GenMatrix1.Caption = lkpMatrix1.Text;

            if (lkpMatrix2.EditValue != null)
                col_GenMatrix2.Caption = lkpMatrix2.Text;
            else
                col_GenMatrix2.Visible = false;

            if (lkpMatrix3.EditValue != null)
                col_GenMatrix3.Caption = lkpMatrix3.Text;
            else
                col_GenMatrix3.Visible = false;

            foreach (MatrixAttribute a in lstMD1.Where(x => x.Available))
            {
                if (lkpMatrix2.EditValue == null || lstMD2.Where(x => x.Available).Count() < 1)
                    lstGenItems.Add(new GenItem
                    {
                        ItemCode = txtMtrxCode0.Text.Trim() + txtMtrxSprtr0.Text + a.MDCode,
                        ItemName = txtParentItemName.Text.Trim() + txtMtrxSprtr0.Text + a.MDName,
                        Mtrix1 = a.MDName,
                        Mtrix2 = string.Empty,
                        Mtrix3 = string.Empty,
                        Attribute1 = a.MatrixDetailId,
                        Attribute2 = null,
                        Attribute3 = null
                    });
                else
                {
                    foreach (MatrixAttribute b in lstMD2.Where(x => x.Available))
                    {
                        if (lkpMatrix3.EditValue == null || lstMD3.Where(x => x.Available).Count() < 1)
                            lstGenItems.Add(new GenItem
                            {
                                ItemCode = txtMtrxCode0.Text.Trim() + txtMtrxSprtr0.Text + a.MDCode
                                     + txtMtrxSprtr1.Text + b.MDCode,
                                ItemName = txtParentItemName.Text.Trim() + txtMtrxSprtr0.Text + a.MDName
                                     + txtMtrxSprtr1.Text + b.MDName,
                                Mtrix1 = a.MDName,
                                Mtrix2 = b.MDName,
                                Mtrix3 = string.Empty,
                                Attribute1 = a.MatrixDetailId,
                                Attribute2 = b.MatrixDetailId,
                                Attribute3 = null
                            });
                        else
                        {
                            foreach (MatrixAttribute c in lstMD3.Where(x => x.Available))
                            {
                                lstGenItems.Add(new GenItem
                                {
                                    ItemCode = txtMtrxCode0.Text.Trim() + txtMtrxSprtr0.Text.Trim() + a.MDCode
                                         + txtMtrxSprtr1.Text + b.MDCode
                                         + txtMtrxSprtr2.Text + c.MDCode + txtMtrxSprtr3.Text,
                                    ItemName = txtParentItemName.Text.Trim() + txtMtrxSprtr0.Text + a.MDName
                                         + txtMtrxSprtr1.Text + b.MDName
                                         + txtMtrxSprtr2.Text + c.MDName + txtMtrxSprtr3.Text,
                                    Mtrix1 = a.MDName,
                                    Mtrix2 = b.MDName,
                                    Mtrix3 = c.MDName,
                                    Attribute1 = a.MatrixDetailId,
                                    Attribute2 = b.MatrixDetailId,
                                    Attribute3 = c.MatrixDetailId
                                });
                            }
                        }
                    }
                }
            }

            grd_genItems.DataSource = lstGenItems;
            grd_genItems.RefreshDataSource();
        }
        
        private void btn_Save_Click(object sender, EventArgs e)
        {
            if (lstGenItems.Count<1)
            {
                XtraMessageBox.Show(
                            Shared.IsEnglish == true ? ResICEn.MsgMtrxValid : ResICAr.MsgMtrxValid
                            , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            
            var parentItem = DB.IC_Items.Where(i => i.ItemId == item.ItemId).First();

            int firstItemCode1 = DB.IC_Items.Select(x => x.ItemCode1).ToList().DefaultIfEmpty(0).Max() + 1;

            #region update IC_ItemMatrixDetails            
            foreach (MatrixAttribute a in lstMD1)
            {
                var oldItemMatrixDetail = DB.IC_ItemMatrixDetails.Where(x => x.ItemId == item.ItemId && 
                    x.MatrixId == a.MatrixId && 
                    x.MatrixDetailId == a.MatrixDetailId).SingleOrDefault();

                if (oldItemMatrixDetail == null)
                {
                    DB.IC_ItemMatrixDetails.InsertOnSubmit(new IC_ItemMatrixDetail
                    {
                        ItemId = item.ItemId,
                        MatrixId = a.MatrixId,
                        MatrixDetailId = a.MatrixDetailId,
                        available = a.Available
                    });
                }
                else
                {
                    oldItemMatrixDetail.available = a.Available;
                }

            }
            foreach (MatrixAttribute a in lstMD2)
            {
                var oldItemMatrixDetail = DB.IC_ItemMatrixDetails.Where(x => x.ItemId == item.ItemId && 
                    x.MatrixId == a.MatrixId && 
                    x.MatrixDetailId == a.MatrixDetailId).SingleOrDefault();

                if (oldItemMatrixDetail == null)
                {
                    DB.IC_ItemMatrixDetails.InsertOnSubmit(new IC_ItemMatrixDetail
                    {
                        ItemId = item.ItemId,
                        MatrixId = a.MatrixId,
                        MatrixDetailId = a.MatrixDetailId,
                        available = a.Available
                    });
                }
                else
                {
                    oldItemMatrixDetail.available = a.Available;
                }
            }
            foreach (MatrixAttribute a in lstMD3)
            {
                var oldItemMatrixDetail = DB.IC_ItemMatrixDetails.Where(x => x.ItemId == item.ItemId && 
                    x.MatrixId == a.MatrixId && 
                    x.MatrixDetailId == a.MatrixDetailId).SingleOrDefault();

                if (oldItemMatrixDetail == null)
                {
                    DB.IC_ItemMatrixDetails.InsertOnSubmit(new IC_ItemMatrixDetail
                    {
                        ItemId = item.ItemId,
                        MatrixId = a.MatrixId,
                        MatrixDetailId = a.MatrixDetailId,
                        available = a.Available
                    });
                }
                else
                {
                    oldItemMatrixDetail.available = a.Available;
                }
            }
            #endregion

            #region insert matrix items
            foreach (GenItem i in lstGenItems)
            {
                bool isNew = false;
                var itm = DB.IC_Items.Where(x => x.mtrxParentItem == item.ItemId &&
                    (i.Attribute1.HasValue ? x.mtrxAttribute1 == i.Attribute1 : x.mtrxAttribute1 == null) &&
                    (i.Attribute2.HasValue ? x.mtrxAttribute2 == i.Attribute2 : x.mtrxAttribute2 == null) &&
                    (i.Attribute3.HasValue? x.mtrxAttribute3 == i.Attribute3: x.mtrxAttribute3 == null) ).Select(x=> x).SingleOrDefault();

                if (itm == null)
                {
                    itm = new IC_Item();
                    itm.ItemCode1 = firstItemCode1;
                    firstItemCode1++;
                    isNew = true;
                }

                itm.ItemCode2 = i.ItemCode;
                itm.ItemNameAr = i.ItemName;
                itm.ItemType = (int)DAL.ItemType.MatrixDetail;
                itm.ItemNameEn = parentItem.ItemNameEn;
                itm.Description = parentItem.Description;
                itm.DescriptionEn = parentItem.DescriptionEn;

                itm.Company = parentItem.Company;
                itm.Category = parentItem.Category;

                itm.PurchasePrice = parentItem.PurchasePrice;

                itm.PurchaseDiscRatio = parentItem.PurchaseDiscRatio;
                itm.SalesDiscRatio = parentItem.SalesDiscRatio;

                itm.PurchaseTaxRatio = parentItem.PurchaseTaxRatio;
                itm.PurchaseTaxValue = parentItem.PurchaseTaxValue;
                itm.SalesTaxRatio = parentItem.SalesTaxRatio;
                itm.SalesTaxValue = parentItem.SalesTaxValue;
                
                itm.Height = parentItem.Height;
                itm.Width = parentItem.Width;
                itm.Length = parentItem.Length;

                itm.SmallUOM = parentItem.SmallUOM;
                itm.SmallUOMPrice = parentItem.SmallUOMPrice;
                itm.MediumUOM = parentItem.MediumUOM;
                itm.MediumUOMFactor = parentItem.MediumUOMFactor;
                itm.MediumUOMFactorDecimal = MyHelper.FractionToDouble(itm.MediumUOMFactor);
                itm.MediumUOMPrice = parentItem.MediumUOMPrice;
                itm.LargeUOM = parentItem.LargeUOM;
                itm.LargeUOMFactor = parentItem.LargeUOMFactor;
                itm.LargeUOMFactorDecimal = MyHelper.FractionToDouble(itm.LargeUOMFactor);
                itm.LargeUOMPrice = parentItem.LargeUOMPrice;

                itm.ReorderLevel = parentItem.ReorderLevel;
                itm.MinQty = parentItem.MinQty;
                itm.MaxQty = parentItem.MaxQty;

                itm.ChangeSellPrice = parentItem.ChangeSellPrice;
                itm.ChangePriceMethod = parentItem.ChangePriceMethod;

                itm.IsExpire = parentItem.IsExpire;

                itm.mtrxParentItem = item.ItemId;

                if (i.Attribute1 == 0)
                    itm.mtrxAttribute1 = null;
                else
                    itm.mtrxAttribute1 = i.Attribute1;

                if (i.Attribute2 == 0)
                    itm.mtrxAttribute2 = null;
                else
                    itm.mtrxAttribute2 = i.Attribute2;

                if (i.Attribute3 == 0)
                    itm.mtrxAttribute3 = null;
                else
                    itm.mtrxAttribute3 = i.Attribute3;

                if (isNew == true)
                    DB.IC_Items.InsertOnSubmit(itm);
            }
            #endregion

            #region update parent item
            parentItem.mtrxId1 = Convert.ToInt32(lkpMatrix1.EditValue);
            
            if(lkpMatrix2.EditValue == null)
                parentItem.mtrxId2 = null;
            else
                parentItem.mtrxId2 = Convert.ToInt32(lkpMatrix2.EditValue);

            if (lkpMatrix3.EditValue == null)
                parentItem.mtrxId3 = null;
            else
                parentItem.mtrxId3 = Convert.ToInt32(lkpMatrix3.EditValue);

            parentItem.mtrxCode0 = txtMtrxCode0.Text.Trim();

            if(string.IsNullOrEmpty(txtMtrxSprtr0.Text.Trim()))
                parentItem.mtrxSprtr0 = null;
            else
                parentItem.mtrxSprtr0 = txtMtrxSprtr0.Text[0];

            if (string.IsNullOrEmpty(txtMtrxSprtr1.Text.Trim()))
                parentItem.mtrxSprtr1 = null;
            else
                parentItem.mtrxSprtr1 = txtMtrxSprtr1.Text[0];

            if (string.IsNullOrEmpty(txtMtrxSprtr2.Text.Trim()))
                parentItem.mtrxSprtr2 = null;
            else
                parentItem.mtrxSprtr2 = txtMtrxSprtr2.Text[0];

            if (string.IsNullOrEmpty(txtMtrxSprtr3.Text.Trim()))
                parentItem.mtrxSprtr3 = null;
            else
                parentItem.mtrxSprtr3 = txtMtrxSprtr3.Text[0];
            #endregion

            DB.SubmitChanges();

            XtraMessageBox.Show(
                Shared.IsEnglish == true ? ResICEn.MsgSave : ResICAr.MsgSave//"تم الحفظ"
                , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void gridView3_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            if (e.ListSourceRowIndex < 0)
                return;

            if (e.Column == col_Serial)
                e.Value = e.RowHandle() + 1;
        }

        //disable available option
        private void gridview_ShowingEditor(object sender, CancelEventArgs e)
        {
            var view = sender as GridView;

            if (view.FocusedColumn.FieldName == "Available")
            {
                if (Convert.ToBoolean(view.GetRowCellValue(view.FocusedRowHandle, "Disabled")) == true)
                {
                    e.Cancel = true;
                     
                }                
            }
        }
    }

    class MatrixAttribute
    {
        int matrixDetailId;
        int matrixId;
        string mDCode;
        string mDName;
        bool available;
        bool disabled;

        public bool Disabled
        {
            get { return disabled; }
            set { disabled = value; }
        }

        public int MatrixDetailId
        {
            get { return matrixDetailId; }
            set { matrixDetailId = value; }
        }
        public int MatrixId
        {
            get { return matrixId; }
            set { matrixId = value; }
        }
        public string MDCode
        {
            get { return mDCode; }
            set { mDCode = value; }
        }
        public string MDName
        {
            get { return mDName; }
            set { mDName = value; }
        }       
        public bool Available
        {
            get { return available; }
            set { available = value; }
        }
    }

    class GenItem
    {
        string itemCode;
        string itemName;
        string mtrix1;
        string mtrix2;
        string mtrix3;
        int? attribute1;
        int? attribute2;
        int? attribute3;

        public int? Attribute1
        {
            get { return attribute1; }
            set { attribute1 = value; }
        }
        public int? Attribute2
        {
            get { return attribute2; }
            set { attribute2 = value; }
        }        
        public int? Attribute3
        {
            get { return attribute3; }
            set { attribute3 = value; }
        }
        public string ItemCode
        {
            get { return itemCode; }
            set { itemCode = value; }
        }
        public string ItemName
        {
            get { return itemName; }
            set { itemName = value; }
        }
        public string Mtrix1
        {
            get { return mtrix1; }
            set { mtrix1 = value; }
        }
        public string Mtrix2
        {
            get { return mtrix2; }
            set { mtrix2 = value; }
        }        
        public string Mtrix3
        {
            get { return mtrix3; }
            set { mtrix3 = value; }
        }
    }

}