using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using DAL;
using System.Linq;
using System.Data;
using DevExpress.XtraEditors;
using System.Windows.Forms;

namespace Reports
{
    public partial class rpt_SL_Quote : DevExpress.XtraReports.UI.XtraReport
    {
        string customer, number, date, store, notes, total, discountR, discountV, expensesR, expensesV, net, userName, SalesEmp, DeliverDate,
                AttnMr, AttnMr_Job, taxR, taxV, salesEmp_Job, EndDate,Shipping, AdvancedPayment;

        DataTable dt_inv_details;
        int currId;

        public rpt_SL_Quote()
        {
            InitializeComponent();
        }
        public rpt_SL_Quote(string _customer, string _number, string _date, string _store,
                            string _notes, string _total, string _discountR, string _discountV, string _expensesR, string _expensesV, string _net,
                            DataTable dt, string userName, string _SalesEmp, string _DeliverDate,string _AttnMr,string _AttnMr_Job,
                            string _taxR, string _taxV, string _salesEmp_Job, string _EndDate,string _Shipping,int _currId,string AdvancedPayment)
        {
            InitializeComponent();            
            customer=_customer;
            number=_number; 
            date=_date;
            store=_store;            
            notes=_notes;
            total=_total;
            discountR = _discountR;
            discountV = _discountV;
            expensesR = _expensesR;
            expensesV = _expensesV;
            net=_net;            
            this.userName = userName;
            SalesEmp = _SalesEmp;
            DeliverDate = _DeliverDate;
            EndDate = _EndDate;
            AttnMr = _AttnMr;
            AttnMr_Job = _AttnMr_Job;
            
            taxR = _taxR;
            taxV = _taxV;
            this.AdvancedPayment = AdvancedPayment;

            salesEmp_Job = _salesEmp_Job;
            Shipping = _Shipping;
            dt_inv_details = dt;

            currId = _currId;

            this.DataSource = dt_inv_details;
            //getReportHeader();
            //LoadData();            
        }        

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                lblCompName.Text = comp.CmpNameAr;                
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }            
        }

        public void LoadData()
        {
            lbl_date.Text = date;
            lbl_DiscountR.Text = discountR;
            lbl_DiscountV.Text = discountV;
            lbl_ExpensesR.Text = expensesR;
            lbl_ExpensesV.Text = expensesV;
            lbl_Net.Text = net;
            lbl_notes.Text = notes;
            lbl_Number.Text = number;                        
            lbl_store.Text = store;            
            lbl_Total.Text = total;
            lbl_Customer.Text = customer;
            lbl_User.Text = userName;
            lbl_SalesEmp.Text = SalesEmp;
            lbl_salesEmp_Job.Text = salesEmp_Job;
            lbl_EndDate.Text = EndDate;
            lbl_DeliverDate.Text = DeliverDate;
            
            lbl_AttnMr.Text = AttnMr;
            lbl_AttnMr_Job.Text = AttnMr_Job;

            lbl_TaxR.Text = taxR;
            lbl_TaxV.Text = taxV;
            lbl_Shipping.Text = Shipping;
            lbl_AdvancedPayment.Text = AdvancedPayment;

            lblTotalWords.Text = Shared.IsEnglish ? HelperAcc.ConvertMoneyToText(net, currId, Shared.lstCurrency) :
                                   HelperAcc.ConvertMoneyToArabicText(net, currId, Shared.lstCurrency);

            this.DataSource = dt_inv_details;

            cell_code.DataBindings.Add("Text", this.DataSource, "ItemCode1");
            cell_code2.DataBindings.Add("Text", this.DataSource, "ItemCode2");
            cell_Disc.DataBindings.Add("Text", this.DataSource, "DiscountValue");
            cell_DiscountRatio.DataBindings.Add("Text", this.DataSource, "DiscountRatio");            
            cell_Price.DataBindings.Add("Text", this.DataSource, "SellPrice");
            cell_Qty.DataBindings.Add("Text", this.DataSource, "Qty");
            cell_Total.DataBindings.Add("Text", this.DataSource, "TotalSellPrice");
            cell_ItemName.DataBindings.Add("Text", this.DataSource, "ItemName");
            cell_UOM.DataBindings.Add("Text", this.DataSource, "UOM");
            cell_Factor.DataBindings.Add("Text", this.DataSource, "Factor");
            Cell_MUOM.DataBindings.Add("Text",this.DataSource,"MUOM");
            Cell_MUOM_Factor.DataBindings.Add("Text", this.DataSource, "MUOM_Factor");
      
            cell_Height.DataBindings.Add("Text", this.DataSource, "Height");
            cell_Width.DataBindings.Add("Text", this.DataSource, "Width");
            cell_Length.DataBindings.Add("Text", this.DataSource, "Length");
            cell_TotalQty.DataBindings.Add("Text", this.DataSource, "TotalQty");
            cell_ItemDescription.DataBindings.Add("Text", this.DataSource, "ItemDescription");
            cell_SalesTax.DataBindings.Add("Text", this.DataSource, "SalesTax");
            Cell_Location.DataBindings.Add("Text", this.DataSource, "Location");
            cell_namef.DataBindings.Add("Text", this.DataSource, "ItemIdF");
            getReportHeader();


        }

    }
}
