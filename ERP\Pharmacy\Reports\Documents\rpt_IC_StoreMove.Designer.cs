﻿namespace Reports
{
    partial class rpt_IC_StoreMove
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.xrTable2 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow2 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_ManufactureDate = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Total_S_Price = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Total_P_Price = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_S_Price = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_P_Price = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Batch = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Expire = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Qty = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_UOM = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_ItemName = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_code2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_code = new DevExpress.XtraReports.UI.XRTableCell();
            this.lbl_storeToManager = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel15 = new DevExpress.XtraReports.UI.XRLabel();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.lbl_User = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel6 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_notes = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel4 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_storeTo = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_storeFromManager = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel11 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel12 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_date = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_storeFrom = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel7 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel8 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Number = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Serial = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel3 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel2 = new DevExpress.XtraReports.UI.XRLabel();
            this.lblReportName = new DevExpress.XtraReports.UI.XRLabel();
            this.picLogo = new DevExpress.XtraReports.UI.XRPictureBox();
            this.lblCompName = new DevExpress.XtraReports.UI.XRLabel();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.xrPageInfo1 = new DevExpress.XtraReports.UI.XRPageInfo();
            this.PageHeader = new DevExpress.XtraReports.UI.PageHeaderBand();
            this.xrTable1 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow1 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell12 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell11 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell9 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell4 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell7 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell6 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell5 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell10 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell8 = new DevExpress.XtraReports.UI.XRTableCell();
            this.ReportFooter = new DevExpress.XtraReports.UI.ReportFooterBand();
            this.xrLabel10 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_totalPieces = new DevExpress.XtraReports.UI.XRLabel();
            this.xrTable3 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow3 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_Serial = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_LibraQty = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_PiecesCount = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_WeightKg = new DevExpress.XtraReports.UI.XRTableCell();
            this.lblTotal_S_Words = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel9 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Total_S_Price = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel1 = new DevExpress.XtraReports.UI.XRLabel();
            this.lblTotal_P_Words = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel5 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Total_P_Price = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel19 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel13 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_itms_totalQty = new DevExpress.XtraReports.UI.XRLabel();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable2});
            this.Detail.HeightF = 29.16667F;
            this.Detail.Name = "Detail";
            this.Detail.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Detail.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrTable2
            // 
            this.xrTable2.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable2.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable2.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.xrTable2.Name = "xrTable2";
            this.xrTable2.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow2});
            this.xrTable2.SizeF = new System.Drawing.SizeF(786F, 29.16667F);
            this.xrTable2.StylePriority.UseBorders = false;
            this.xrTable2.StylePriority.UseFont = false;
            this.xrTable2.StylePriority.UseTextAlignment = false;
            this.xrTable2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow2
            // 
            this.xrTableRow2.BackColor = System.Drawing.Color.Empty;
            this.xrTableRow2.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_ManufactureDate,
            this.cell_Total_S_Price,
            this.cell_Total_P_Price,
            this.cell_S_Price,
            this.cell_P_Price,
            this.cell_Batch,
            this.cell_Expire,
            this.cell_Qty,
            this.cell_UOM,
            this.cell_ItemName,
            this.cell_code2,
            this.cell_code});
            this.xrTableRow2.Font = new System.Drawing.Font("Times New Roman", 10F, System.Drawing.FontStyle.Bold);
            this.xrTableRow2.Name = "xrTableRow2";
            this.xrTableRow2.StylePriority.UseBackColor = false;
            this.xrTableRow2.StylePriority.UseFont = false;
            this.xrTableRow2.Weight = 0.54901959587545957D;
            // 
            // cell_ManufactureDate
            // 
            this.cell_ManufactureDate.Name = "cell_ManufactureDate";
            this.cell_ManufactureDate.Text = "تاريخ الصنع";
            this.cell_ManufactureDate.Weight = 0.15481870593005462D;
            // 
            // cell_Total_S_Price
            // 
            this.cell_Total_S_Price.Name = "cell_Total_S_Price";
            this.cell_Total_S_Price.Text = "اجمالي س بيع";
            this.cell_Total_S_Price.Weight = 0.15481870593005462D;
            // 
            // cell_Total_P_Price
            // 
            this.cell_Total_P_Price.Name = "cell_Total_P_Price";
            this.cell_Total_P_Price.Text = "اجمالي س شراء";
            this.cell_Total_P_Price.Weight = 0.15016757258932098D;
            // 
            // cell_S_Price
            // 
            this.cell_S_Price.Name = "cell_S_Price";
            this.cell_S_Price.Text = "س بيع";
            this.cell_S_Price.Weight = 0.15040489189497386D;
            // 
            // cell_P_Price
            // 
            this.cell_P_Price.Name = "cell_P_Price";
            this.cell_P_Price.Text = "س شراء";
            this.cell_P_Price.Weight = 0.1828487665598629D;
            // 
            // cell_Batch
            // 
            this.cell_Batch.Name = "cell_Batch";
            this.cell_Batch.Text = "التشغيلة";
            this.cell_Batch.Weight = 0.20968459398691888D;
            // 
            // cell_Expire
            // 
            this.cell_Expire.Name = "cell_Expire";
            this.cell_Expire.Text = "تاريخ الصلاحية";
            this.cell_Expire.Weight = 0.22316327713828055D;
            // 
            // cell_Qty
            // 
            this.cell_Qty.Name = "cell_Qty";
            this.cell_Qty.Text = "الكمية";
            this.cell_Qty.Weight = 0.20813447646512329D;
            // 
            // cell_UOM
            // 
            this.cell_UOM.Name = "cell_UOM";
            this.cell_UOM.Text = "وحدة القياس";
            this.cell_UOM.Weight = 0.21469517336546917D;
            // 
            // cell_ItemName
            // 
            this.cell_ItemName.Name = "cell_ItemName";
            this.cell_ItemName.Text = "اســـم الصنف";
            this.cell_ItemName.Weight = 0.35889772063967817D;
            // 
            // cell_code2
            // 
            this.cell_code2.Name = "cell_code2";
            this.cell_code2.Text = "كود2";
            this.cell_code2.Weight = 0.13823920736725762D;
            // 
            // cell_code
            // 
            this.cell_code.Name = "cell_code";
            this.cell_code.Text = "كود";
            this.cell_code.Weight = 0.10412696602891788D;
            // 
            // lbl_storeToManager
            // 
            this.lbl_storeToManager.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_storeToManager.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_storeToManager.LocationFloat = new DevExpress.Utils.PointFloat(159.0833F, 120.0418F);
            this.lbl_storeToManager.Name = "lbl_storeToManager";
            this.lbl_storeToManager.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_storeToManager.SizeF = new System.Drawing.SizeF(119.5F, 24.49998F);
            this.lbl_storeToManager.StylePriority.UseBorders = false;
            this.lbl_storeToManager.StylePriority.UseFont = false;
            this.lbl_storeToManager.StylePriority.UseTextAlignment = false;
            this.lbl_storeToManager.Text = " ";
            this.lbl_storeToManager.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel15
            // 
            this.xrLabel15.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel15.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.xrLabel15.LocationFloat = new DevExpress.Utils.PointFloat(278.5833F, 120.0418F);
            this.xrLabel15.Name = "xrLabel15";
            this.xrLabel15.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel15.SizeF = new System.Drawing.SizeF(85.95848F, 24.49998F);
            this.xrLabel15.StylePriority.UseBorders = false;
            this.xrLabel15.StylePriority.UseFont = false;
            this.xrLabel15.StylePriority.UseTextAlignment = false;
            this.xrLabel15.Text = "أمين المخزن";
            this.xrLabel15.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // TopMargin
            // 
            this.TopMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.lbl_User,
            this.xrLabel6,
            this.lbl_notes,
            this.xrLabel4,
            this.lbl_storeTo,
            this.lbl_storeFromManager,
            this.xrLabel11,
            this.xrLabel12,
            this.lbl_date,
            this.lbl_storeFrom,
            this.xrLabel7,
            this.xrLabel8,
            this.lbl_Number,
            this.lbl_Serial,
            this.xrLabel3,
            this.xrLabel2,
            this.lblReportName,
            this.picLogo,
            this.lblCompName,
            this.lbl_storeToManager,
            this.xrLabel15});
            this.TopMargin.HeightF = 245F;
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.TopMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // lbl_User
            // 
            this.lbl_User.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_User.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.lbl_User.LocationFloat = new DevExpress.Utils.PointFloat(14.58332F, 120.0417F);
            this.lbl_User.Name = "lbl_User";
            this.lbl_User.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_User.SizeF = new System.Drawing.SizeF(110.9167F, 24.49998F);
            this.lbl_User.StylePriority.UseBorders = false;
            this.lbl_User.StylePriority.UseFont = false;
            this.lbl_User.StylePriority.UseTextAlignment = false;
            this.lbl_User.Text = "..";
            this.lbl_User.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel6
            // 
            this.xrLabel6.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel6.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel6.LocationFloat = new DevExpress.Utils.PointFloat(14.58333F, 95.54173F);
            this.xrLabel6.Name = "xrLabel6";
            this.xrLabel6.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel6.SizeF = new System.Drawing.SizeF(110.9167F, 24.49998F);
            this.xrLabel6.StylePriority.UseBorders = false;
            this.xrLabel6.StylePriority.UseFont = false;
            this.xrLabel6.StylePriority.UseTextAlignment = false;
            this.xrLabel6.Text = "اسم المستخدم";
            this.xrLabel6.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // lbl_notes
            // 
            this.lbl_notes.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_notes.LocationFloat = new DevExpress.Utils.PointFloat(14.58332F, 169.0417F);
            this.lbl_notes.Multiline = true;
            this.lbl_notes.Name = "lbl_notes";
            this.lbl_notes.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_notes.SizeF = new System.Drawing.SizeF(670.9582F, 50.9583F);
            this.lbl_notes.StylePriority.UseFont = false;
            this.lbl_notes.StylePriority.UseTextAlignment = false;
            this.lbl_notes.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight;
            // 
            // xrLabel4
            // 
            this.xrLabel4.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.xrLabel4.LocationFloat = new DevExpress.Utils.PointFloat(685.5415F, 176.0417F);
            this.xrLabel4.Name = "xrLabel4";
            this.xrLabel4.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel4.SizeF = new System.Drawing.SizeF(86.4585F, 24.49998F);
            this.xrLabel4.StylePriority.UseFont = false;
            this.xrLabel4.StylePriority.UseTextAlignment = false;
            this.xrLabel4.Text = "ملاحظات";
            this.xrLabel4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // lbl_storeTo
            // 
            this.lbl_storeTo.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_storeTo.LocationFloat = new DevExpress.Utils.PointFloat(369.7083F, 120.0417F);
            this.lbl_storeTo.Name = "lbl_storeTo";
            this.lbl_storeTo.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_storeTo.SizeF = new System.Drawing.SizeF(119.5F, 24.49998F);
            this.lbl_storeTo.StylePriority.UseFont = false;
            this.lbl_storeTo.StylePriority.UseTextAlignment = false;
            this.lbl_storeTo.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // lbl_storeFromManager
            // 
            this.lbl_storeFromManager.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_storeFromManager.LocationFloat = new DevExpress.Utils.PointFloat(159.0833F, 95.54176F);
            this.lbl_storeFromManager.Name = "lbl_storeFromManager";
            this.lbl_storeFromManager.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_storeFromManager.SizeF = new System.Drawing.SizeF(119.5F, 24.49998F);
            this.lbl_storeFromManager.StylePriority.UseFont = false;
            this.lbl_storeFromManager.StylePriority.UseTextAlignment = false;
            this.lbl_storeFromManager.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel11
            // 
            this.xrLabel11.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.xrLabel11.LocationFloat = new DevExpress.Utils.PointFloat(489.2082F, 120.0417F);
            this.xrLabel11.Name = "xrLabel11";
            this.xrLabel11.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel11.SizeF = new System.Drawing.SizeF(86.4585F, 24.49999F);
            this.xrLabel11.StylePriority.UseFont = false;
            this.xrLabel11.StylePriority.UseTextAlignment = false;
            this.xrLabel11.Text = "الى مخزن";
            this.xrLabel11.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel12
            // 
            this.xrLabel12.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.xrLabel12.LocationFloat = new DevExpress.Utils.PointFloat(278.5833F, 95.54176F);
            this.xrLabel12.Name = "xrLabel12";
            this.xrLabel12.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel12.SizeF = new System.Drawing.SizeF(86.4585F, 24.49998F);
            this.xrLabel12.StylePriority.UseFont = false;
            this.xrLabel12.StylePriority.UseTextAlignment = false;
            this.xrLabel12.Text = "أمين المخزن";
            this.xrLabel12.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // lbl_date
            // 
            this.lbl_date.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_date.LocationFloat = new DevExpress.Utils.PointFloat(580.333F, 144.5417F);
            this.lbl_date.Name = "lbl_date";
            this.lbl_date.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_date.SizeF = new System.Drawing.SizeF(105.2084F, 24.5F);
            this.lbl_date.StylePriority.UseFont = false;
            this.lbl_date.StylePriority.UseTextAlignment = false;
            this.lbl_date.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // lbl_storeFrom
            // 
            this.lbl_storeFrom.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_storeFrom.LocationFloat = new DevExpress.Utils.PointFloat(369.7083F, 95.54173F);
            this.lbl_storeFrom.Name = "lbl_storeFrom";
            this.lbl_storeFrom.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_storeFrom.SizeF = new System.Drawing.SizeF(119.5F, 24.49999F);
            this.lbl_storeFrom.StylePriority.UseFont = false;
            this.lbl_storeFrom.StylePriority.UseTextAlignment = false;
            this.lbl_storeFrom.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel7
            // 
            this.xrLabel7.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.xrLabel7.LocationFloat = new DevExpress.Utils.PointFloat(685.5414F, 144.5417F);
            this.xrLabel7.Name = "xrLabel7";
            this.xrLabel7.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel7.SizeF = new System.Drawing.SizeF(86.4585F, 24.49999F);
            this.xrLabel7.StylePriority.UseFont = false;
            this.xrLabel7.StylePriority.UseTextAlignment = false;
            this.xrLabel7.Text = "التاريخ";
            this.xrLabel7.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel8
            // 
            this.xrLabel8.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.xrLabel8.LocationFloat = new DevExpress.Utils.PointFloat(489.2082F, 95.54173F);
            this.xrLabel8.Multiline = true;
            this.xrLabel8.Name = "xrLabel8";
            this.xrLabel8.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel8.SizeF = new System.Drawing.SizeF(86.4585F, 24.49998F);
            this.xrLabel8.StylePriority.UseFont = false;
            this.xrLabel8.StylePriority.UseTextAlignment = false;
            this.xrLabel8.Text = "من مخزن";
            this.xrLabel8.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // lbl_Number
            // 
            this.lbl_Number.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_Number.LocationFloat = new DevExpress.Utils.PointFloat(580.333F, 120.0417F);
            this.lbl_Number.Name = "lbl_Number";
            this.lbl_Number.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Number.SizeF = new System.Drawing.SizeF(105.2085F, 24.49998F);
            this.lbl_Number.StylePriority.UseFont = false;
            this.lbl_Number.StylePriority.UseTextAlignment = false;
            this.lbl_Number.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // lbl_Serial
            // 
            this.lbl_Serial.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_Serial.LocationFloat = new DevExpress.Utils.PointFloat(580.333F, 95.54173F);
            this.lbl_Serial.Name = "lbl_Serial";
            this.lbl_Serial.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Serial.SizeF = new System.Drawing.SizeF(105.2085F, 24.5F);
            this.lbl_Serial.StylePriority.UseFont = false;
            this.lbl_Serial.StylePriority.UseTextAlignment = false;
            this.lbl_Serial.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel3
            // 
            this.xrLabel3.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.xrLabel3.LocationFloat = new DevExpress.Utils.PointFloat(685.5415F, 120.0417F);
            this.xrLabel3.Name = "xrLabel3";
            this.xrLabel3.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel3.SizeF = new System.Drawing.SizeF(86.4585F, 24.49998F);
            this.xrLabel3.StylePriority.UseFont = false;
            this.xrLabel3.StylePriority.UseTextAlignment = false;
            this.xrLabel3.Text = "رقم السند";
            this.xrLabel3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel2
            // 
            this.xrLabel2.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.xrLabel2.LocationFloat = new DevExpress.Utils.PointFloat(685.5415F, 95.54173F);
            this.xrLabel2.Name = "xrLabel2";
            this.xrLabel2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel2.SizeF = new System.Drawing.SizeF(86.4585F, 24.49999F);
            this.xrLabel2.StylePriority.UseFont = false;
            this.xrLabel2.StylePriority.UseTextAlignment = false;
            this.xrLabel2.Text = "تسلسل";
            this.xrLabel2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // lblReportName
            // 
            this.lblReportName.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lblReportName.LocationFloat = new DevExpress.Utils.PointFloat(312.5F, 50F);
            this.lblReportName.Name = "lblReportName";
            this.lblReportName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblReportName.SizeF = new System.Drawing.SizeF(171.8751F, 24.49998F);
            this.lblReportName.StylePriority.UseFont = false;
            this.lblReportName.StylePriority.UseTextAlignment = false;
            this.lblReportName.Text = "سند نقل من مخزن";
            this.lblReportName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // picLogo
            // 
            this.picLogo.LocationFloat = new DevExpress.Utils.PointFloat(14.58333F, 10.00001F);
            this.picLogo.Name = "picLogo";
            this.picLogo.SizeF = new System.Drawing.SizeF(70F, 70F);
            this.picLogo.Sizing = DevExpress.XtraPrinting.ImageSizeMode.StretchImage;
            // 
            // lblCompName
            // 
            this.lblCompName.Font = new System.Drawing.Font("Times New Roman", 18F);
            this.lblCompName.LocationFloat = new DevExpress.Utils.PointFloat(89.58334F, 10.00001F);
            this.lblCompName.Name = "lblCompName";
            this.lblCompName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblCompName.SizeF = new System.Drawing.SizeF(600.0001F, 30F);
            this.lblCompName.StylePriority.UseFont = false;
            this.lblCompName.StylePriority.UseTextAlignment = false;
            this.lblCompName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // BottomMargin
            // 
            this.BottomMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrPageInfo1});
            this.BottomMargin.HeightF = 45F;
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.BottomMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrPageInfo1
            // 
            this.xrPageInfo1.Format = "Page {0} of {1} ";
            this.xrPageInfo1.LocationFloat = new DevExpress.Utils.PointFloat(337.5F, 12.5F);
            this.xrPageInfo1.Name = "xrPageInfo1";
            this.xrPageInfo1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrPageInfo1.SizeF = new System.Drawing.SizeF(109.375F, 23F);
            this.xrPageInfo1.StylePriority.UseTextAlignment = false;
            this.xrPageInfo1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // PageHeader
            // 
            this.PageHeader.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable1});
            this.PageHeader.HeightF = 53.125F;
            this.PageHeader.Name = "PageHeader";
            // 
            // xrTable1
            // 
            this.xrTable1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable1.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable1.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.xrTable1.Name = "xrTable1";
            this.xrTable1.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow1});
            this.xrTable1.SizeF = new System.Drawing.SizeF(786F, 53.125F);
            this.xrTable1.StylePriority.UseBorders = false;
            this.xrTable1.StylePriority.UseFont = false;
            this.xrTable1.StylePriority.UseTextAlignment = false;
            this.xrTable1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow1
            // 
            this.xrTableRow1.BackColor = System.Drawing.Color.Moccasin;
            this.xrTableRow1.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell12,
            this.xrTableCell11,
            this.xrTableCell1,
            this.xrTableCell9,
            this.xrTableCell4,
            this.xrTableCell2,
            this.xrTableCell7,
            this.xrTableCell6,
            this.xrTableCell5,
            this.xrTableCell3,
            this.xrTableCell10,
            this.xrTableCell8});
            this.xrTableRow1.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrTableRow1.Name = "xrTableRow1";
            this.xrTableRow1.StylePriority.UseBackColor = false;
            this.xrTableRow1.StylePriority.UseFont = false;
            this.xrTableRow1.Weight = 1D;
            // 
            // xrTableCell12
            // 
            this.xrTableCell12.Name = "xrTableCell12";
            this.xrTableCell12.Text = "تاريخ الصنع";
            this.xrTableCell12.Weight = 0.15481870593005462D;
            // 
            // xrTableCell11
            // 
            this.xrTableCell11.Name = "xrTableCell11";
            this.xrTableCell11.Text = "اجمالي س بيع";
            this.xrTableCell11.Weight = 0.15481870593005462D;
            // 
            // xrTableCell1
            // 
            this.xrTableCell1.Name = "xrTableCell1";
            this.xrTableCell1.Text = "اجمالي س شراء";
            this.xrTableCell1.Weight = 0.15016757258932098D;
            // 
            // xrTableCell9
            // 
            this.xrTableCell9.Name = "xrTableCell9";
            this.xrTableCell9.Text = "س بيع";
            this.xrTableCell9.Weight = 0.15040489189497386D;
            // 
            // xrTableCell4
            // 
            this.xrTableCell4.Name = "xrTableCell4";
            this.xrTableCell4.Text = "س شراء";
            this.xrTableCell4.Weight = 0.1828487665598629D;
            // 
            // xrTableCell2
            // 
            this.xrTableCell2.Name = "xrTableCell2";
            this.xrTableCell2.Text = "التشغيلة";
            this.xrTableCell2.Weight = 0.20968459398691888D;
            // 
            // xrTableCell7
            // 
            this.xrTableCell7.Name = "xrTableCell7";
            this.xrTableCell7.Text = "تاريخ الصلاحية";
            this.xrTableCell7.Weight = 0.22316327713828055D;
            // 
            // xrTableCell6
            // 
            this.xrTableCell6.Name = "xrTableCell6";
            this.xrTableCell6.Text = "الكمية";
            this.xrTableCell6.Weight = 0.20813447646512329D;
            // 
            // xrTableCell5
            // 
            this.xrTableCell5.Name = "xrTableCell5";
            this.xrTableCell5.Text = "وحدة القياس";
            this.xrTableCell5.Weight = 0.21469517336546917D;
            // 
            // xrTableCell3
            // 
            this.xrTableCell3.Name = "xrTableCell3";
            this.xrTableCell3.Text = "اســـم الصنف";
            this.xrTableCell3.Weight = 0.35889772063967817D;
            // 
            // xrTableCell10
            // 
            this.xrTableCell10.Name = "xrTableCell10";
            this.xrTableCell10.Text = "كود2";
            this.xrTableCell10.Weight = 0.13823920736725762D;
            // 
            // xrTableCell8
            // 
            this.xrTableCell8.Name = "xrTableCell8";
            this.xrTableCell8.Text = "كود";
            this.xrTableCell8.Weight = 0.10412696602891788D;
            // 
            // ReportFooter
            // 
            this.ReportFooter.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLabel13,
            this.lbl_itms_totalQty,
            this.xrLabel10,
            this.lbl_totalPieces,
            this.xrTable3,
            this.lblTotal_S_Words,
            this.xrLabel9,
            this.lbl_Total_S_Price,
            this.xrLabel1,
            this.lblTotal_P_Words,
            this.xrLabel5,
            this.lbl_Total_P_Price,
            this.xrLabel19});
            this.ReportFooter.HeightF = 211F;
            this.ReportFooter.Name = "ReportFooter";
            // 
            // xrLabel10
            // 
            this.xrLabel10.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel10.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.xrLabel10.LocationFloat = new DevExpress.Utils.PointFloat(590.7708F, 0F);
            this.xrLabel10.Name = "xrLabel10";
            this.xrLabel10.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel10.SizeF = new System.Drawing.SizeF(154.5F, 24.49995F);
            this.xrLabel10.StylePriority.UseBorders = false;
            this.xrLabel10.StylePriority.UseFont = false;
            this.xrLabel10.StylePriority.UseTextAlignment = false;
            this.xrLabel10.Text = "عدد الكراتين";
            this.xrLabel10.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // lbl_totalPieces
            // 
            this.lbl_totalPieces.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_totalPieces.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_totalPieces.LocationFloat = new DevExpress.Utils.PointFloat(449.0625F, 0F);
            this.lbl_totalPieces.Name = "lbl_totalPieces";
            this.lbl_totalPieces.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_totalPieces.SizeF = new System.Drawing.SizeF(141.7083F, 24.49995F);
            this.lbl_totalPieces.StylePriority.UseBorders = false;
            this.lbl_totalPieces.StylePriority.UseFont = false;
            this.lbl_totalPieces.StylePriority.UseTextAlignment = false;
            this.lbl_totalPieces.Text = "0";
            this.lbl_totalPieces.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTable3
            // 
            this.xrTable3.LocationFloat = new DevExpress.Utils.PointFloat(1.958328F, 157.4167F);
            this.xrTable3.Name = "xrTable3";
            this.xrTable3.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow3});
            this.xrTable3.SizeF = new System.Drawing.SizeF(573.7084F, 25F);
            this.xrTable3.Visible = false;
            // 
            // xrTableRow3
            // 
            this.xrTableRow3.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_Serial,
            this.cell_LibraQty,
            this.cell_PiecesCount,
            this.cell_WeightKg});
            this.xrTableRow3.Name = "xrTableRow3";
            this.xrTableRow3.Weight = 1D;
            // 
            // cell_Serial
            // 
            this.cell_Serial.Name = "cell_Serial";
            this.cell_Serial.Text = "cell_Serial";
            this.cell_Serial.Weight = 1.6979167175292971D;
            // 
            // cell_LibraQty
            // 
            this.cell_LibraQty.Name = "cell_LibraQty";
            this.cell_LibraQty.Text = "cell_LibraQty";
            this.cell_LibraQty.Weight = 1.2825D;
            // 
            // cell_PiecesCount
            // 
            this.cell_PiecesCount.Name = "cell_PiecesCount";
            this.cell_PiecesCount.Text = "cell_PiecesCount";
            this.cell_PiecesCount.Weight = 1.2825D;
            // 
            // cell_WeightKg
            // 
            this.cell_WeightKg.Name = "cell_WeightKg";
            this.cell_WeightKg.Text = "cell_WeightKg";
            this.cell_WeightKg.Weight = 1.4741674804687501D;
            // 
            // lblTotal_S_Words
            // 
            this.lblTotal_S_Words.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lblTotal_S_Words.LocationFloat = new DevExpress.Utils.PointFloat(0.999999F, 113.4999F);
            this.lblTotal_S_Words.Name = "lblTotal_S_Words";
            this.lblTotal_S_Words.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblTotal_S_Words.SizeF = new System.Drawing.SizeF(647.5833F, 24.49995F);
            this.lblTotal_S_Words.StylePriority.UseFont = false;
            this.lblTotal_S_Words.StylePriority.UseTextAlignment = false;
            this.lblTotal_S_Words.Text = "..";
            this.lblTotal_S_Words.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel9
            // 
            this.xrLabel9.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.xrLabel9.LocationFloat = new DevExpress.Utils.PointFloat(651.9166F, 94.49988F);
            this.xrLabel9.Name = "xrLabel9";
            this.xrLabel9.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel9.SizeF = new System.Drawing.SizeF(86.4585F, 24.49998F);
            this.xrLabel9.StylePriority.UseFont = false;
            this.xrLabel9.StylePriority.UseTextAlignment = false;
            this.xrLabel9.Text = "فقط وقدره";
            this.xrLabel9.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // lbl_Total_S_Price
            // 
            this.lbl_Total_S_Price.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_Total_S_Price.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_Total_S_Price.LocationFloat = new DevExpress.Utils.PointFloat(0F, 53.49996F);
            this.lbl_Total_S_Price.Name = "lbl_Total_S_Price";
            this.lbl_Total_S_Price.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Total_S_Price.SizeF = new System.Drawing.SizeF(141.7083F, 24.49995F);
            this.lbl_Total_S_Price.StylePriority.UseBorders = false;
            this.lbl_Total_S_Price.StylePriority.UseFont = false;
            this.lbl_Total_S_Price.StylePriority.UseTextAlignment = false;
            this.lbl_Total_S_Price.Text = " ";
            this.lbl_Total_S_Price.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel1
            // 
            this.xrLabel1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel1.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.xrLabel1.LocationFloat = new DevExpress.Utils.PointFloat(141.7083F, 53.49996F);
            this.xrLabel1.Name = "xrLabel1";
            this.xrLabel1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel1.SizeF = new System.Drawing.SizeF(154.5F, 24.49995F);
            this.xrLabel1.StylePriority.UseBorders = false;
            this.xrLabel1.StylePriority.UseFont = false;
            this.xrLabel1.StylePriority.UseTextAlignment = false;
            this.xrLabel1.Text = "اجمالي سعر بيع";
            this.xrLabel1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // lblTotal_P_Words
            // 
            this.lblTotal_P_Words.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lblTotal_P_Words.LocationFloat = new DevExpress.Utils.PointFloat(0F, 85.99992F);
            this.lblTotal_P_Words.Name = "lblTotal_P_Words";
            this.lblTotal_P_Words.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblTotal_P_Words.SizeF = new System.Drawing.SizeF(648.5833F, 24.49995F);
            this.lblTotal_P_Words.StylePriority.UseFont = false;
            this.lblTotal_P_Words.StylePriority.UseTextAlignment = false;
            this.lblTotal_P_Words.Text = "..";
            this.lblTotal_P_Words.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel5
            // 
            this.xrLabel5.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.xrLabel5.LocationFloat = new DevExpress.Utils.PointFloat(651.9166F, 66.99992F);
            this.xrLabel5.Name = "xrLabel5";
            this.xrLabel5.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel5.SizeF = new System.Drawing.SizeF(86.4585F, 24.49998F);
            this.xrLabel5.StylePriority.UseFont = false;
            this.xrLabel5.StylePriority.UseTextAlignment = false;
            this.xrLabel5.Text = "فقط وقدره";
            this.xrLabel5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // lbl_Total_P_Price
            // 
            this.lbl_Total_P_Price.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_Total_P_Price.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_Total_P_Price.LocationFloat = new DevExpress.Utils.PointFloat(0F, 29.00001F);
            this.lbl_Total_P_Price.Name = "lbl_Total_P_Price";
            this.lbl_Total_P_Price.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Total_P_Price.SizeF = new System.Drawing.SizeF(141.7083F, 24.49995F);
            this.lbl_Total_P_Price.StylePriority.UseBorders = false;
            this.lbl_Total_P_Price.StylePriority.UseFont = false;
            this.lbl_Total_P_Price.StylePriority.UseTextAlignment = false;
            this.lbl_Total_P_Price.Text = " ";
            this.lbl_Total_P_Price.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel19
            // 
            this.xrLabel19.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel19.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.xrLabel19.LocationFloat = new DevExpress.Utils.PointFloat(141.7083F, 29.00001F);
            this.xrLabel19.Name = "xrLabel19";
            this.xrLabel19.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel19.SizeF = new System.Drawing.SizeF(154.5F, 24.49995F);
            this.xrLabel19.StylePriority.UseBorders = false;
            this.xrLabel19.StylePriority.UseFont = false;
            this.xrLabel19.StylePriority.UseTextAlignment = false;
            this.xrLabel19.Text = "اجمالي سعر شراء";
            this.xrLabel19.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel13
            // 
            this.xrLabel13.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel13.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.xrLabel13.LocationFloat = new DevExpress.Utils.PointFloat(141.7083F, 0F);
            this.xrLabel13.Name = "xrLabel13";
            this.xrLabel13.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel13.SizeF = new System.Drawing.SizeF(154.5F, 24.49995F);
            this.xrLabel13.StylePriority.UseBorders = false;
            this.xrLabel13.StylePriority.UseFont = false;
            this.xrLabel13.StylePriority.UseTextAlignment = false;
            this.xrLabel13.Text = "إجمالي الكمية";
            this.xrLabel13.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // lbl_itms_totalQty
            // 
            this.lbl_itms_totalQty.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_itms_totalQty.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_itms_totalQty.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.lbl_itms_totalQty.Name = "lbl_itms_totalQty";
            this.lbl_itms_totalQty.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_itms_totalQty.SizeF = new System.Drawing.SizeF(141.7083F, 24.49995F);
            this.lbl_itms_totalQty.StylePriority.UseBorders = false;
            this.lbl_itms_totalQty.StylePriority.UseFont = false;
            this.lbl_itms_totalQty.StylePriority.UseTextAlignment = false;
            this.lbl_itms_totalQty.Text = " ";
            this.lbl_itms_totalQty.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // rpt_IC_StoreMove
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail,
            this.TopMargin,
            this.BottomMargin,
            this.PageHeader,
            this.ReportFooter});
            this.Margins = new System.Drawing.Printing.Margins(19, 22, 245, 45);
            this.PageHeight = 1169;
            this.PageWidth = 827;
            this.PaperKind = System.Drawing.Printing.PaperKind.A4;
            this.Version = "15.1";
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.XRLabel lblReportName;
        private DevExpress.XtraReports.UI.XRPictureBox picLogo;
        private DevExpress.XtraReports.UI.XRLabel lblCompName;
        private DevExpress.XtraReports.UI.XRPageInfo xrPageInfo1;
        private DevExpress.XtraReports.UI.XRLabel xrLabel3;
        private DevExpress.XtraReports.UI.XRLabel xrLabel2;
        private DevExpress.XtraReports.UI.XRLabel lbl_date;
        private DevExpress.XtraReports.UI.XRLabel lbl_storeFrom;
        private DevExpress.XtraReports.UI.XRLabel xrLabel7;
        private DevExpress.XtraReports.UI.XRLabel xrLabel8;
        private DevExpress.XtraReports.UI.XRLabel lbl_Number;
        private DevExpress.XtraReports.UI.XRLabel lbl_Serial;
        private DevExpress.XtraReports.UI.XRLabel lbl_storeTo;
        private DevExpress.XtraReports.UI.XRLabel lbl_storeFromManager;
        private DevExpress.XtraReports.UI.XRLabel xrLabel11;
        private DevExpress.XtraReports.UI.XRLabel xrLabel12;
        private DevExpress.XtraReports.UI.XRLabel xrLabel15;
        private DevExpress.XtraReports.UI.XRLabel lbl_storeToManager;
        private DevExpress.XtraReports.UI.PageHeaderBand PageHeader;
        private DevExpress.XtraReports.UI.XRTable xrTable1;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow1;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell1;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell4;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell2;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell5;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell3;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell6;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell9;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell7;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell8;
        private DevExpress.XtraReports.UI.XRTable xrTable2;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow2;
        private DevExpress.XtraReports.UI.XRTableCell cell_Total_P_Price;
        private DevExpress.XtraReports.UI.XRTableCell cell_S_Price;
        private DevExpress.XtraReports.UI.XRTableCell cell_P_Price;
        private DevExpress.XtraReports.UI.XRTableCell cell_Batch;
        private DevExpress.XtraReports.UI.XRTableCell cell_Expire;
        private DevExpress.XtraReports.UI.XRTableCell cell_Qty;
        private DevExpress.XtraReports.UI.XRTableCell cell_UOM;
        private DevExpress.XtraReports.UI.XRTableCell cell_ItemName;
        private DevExpress.XtraReports.UI.XRTableCell cell_code;
        private DevExpress.XtraReports.UI.XRLabel lbl_notes;
        private DevExpress.XtraReports.UI.XRLabel xrLabel4;
        private DevExpress.XtraReports.UI.ReportFooterBand ReportFooter;
        private DevExpress.XtraReports.UI.XRTableCell cell_code2;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell10;
        private DevExpress.XtraReports.UI.XRLabel lbl_User;
        private DevExpress.XtraReports.UI.XRLabel xrLabel6;
        private DevExpress.XtraReports.UI.XRTableCell cell_Total_S_Price;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell11;
        private DevExpress.XtraReports.UI.XRLabel lblTotal_S_Words;
        private DevExpress.XtraReports.UI.XRLabel xrLabel9;
        private DevExpress.XtraReports.UI.XRLabel lbl_Total_S_Price;
        private DevExpress.XtraReports.UI.XRLabel xrLabel1;
        private DevExpress.XtraReports.UI.XRLabel lblTotal_P_Words;
        private DevExpress.XtraReports.UI.XRLabel xrLabel5;
        private DevExpress.XtraReports.UI.XRLabel lbl_Total_P_Price;
        private DevExpress.XtraReports.UI.XRLabel xrLabel19;
        private DevExpress.XtraReports.UI.XRTable xrTable3;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow3;
        private DevExpress.XtraReports.UI.XRTableCell cell_Serial;
        private DevExpress.XtraReports.UI.XRTableCell cell_ManufactureDate;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell12;
        private DevExpress.XtraReports.UI.XRTableCell cell_WeightKg;
        private DevExpress.XtraReports.UI.XRLabel xrLabel10;
        private DevExpress.XtraReports.UI.XRLabel lbl_totalPieces;
        private DevExpress.XtraReports.UI.XRTableCell cell_PiecesCount;
        private DevExpress.XtraReports.UI.XRTableCell cell_LibraQty;
        private DevExpress.XtraReports.UI.XRLabel xrLabel13;
        private DevExpress.XtraReports.UI.XRLabel lbl_itms_totalQty;
    }
}
