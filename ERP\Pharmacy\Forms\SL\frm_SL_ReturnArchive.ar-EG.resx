﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="barBtnHelp.Caption" xml:space="preserve">
    <value>مساعدة</value>
  </data>
  <data name="barSubItem1.Caption" xml:space="preserve">
    <value>تحميل</value>
  </data>
  <data name="barbtnLoadSellInvoice.Caption" xml:space="preserve">
    <value>تحميل فاتورة بيع</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="barSubItem1.MenuAppearance.HeaderItemAppearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="barSubItem1.MenuAppearance.HeaderItemAppearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barSubItem1.MenuAppearance.HeaderItemAppearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="barSubItem1.MenuAppearance.HeaderItemAppearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barBtnPrint.Caption" xml:space="preserve">
    <value>طباعه</value>
  </data>
  <data name="barBtnNew.Caption" xml:space="preserve">
    <value>جديد</value>
  </data>
  <data name="barBtnDelete.Caption" xml:space="preserve">
    <value>حذف</value>
  </data>
  <data name="barBtnSave.Caption" xml:space="preserve">
    <value>حفظ</value>
  </data>
  <data name="batBtnList.Caption" xml:space="preserve">
    <value>القائمة</value>
  </data>
  <data name="barBtnClose.Caption" xml:space="preserve">
    <value>غلق</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.BarAppearance.Normal.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.BarAppearance.Normal.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesBar.Dock.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.Dock.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.Dock.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.Dock.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuBar.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuBar.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStrip.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStrip.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesDocking.Panel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barAndDockingController1.AppearancesDocking.Panel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barAndDockingController1.AppearancesDocking.Panel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barAndDockingController1.AppearancesDocking.Panel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesDocking.PanelCaption.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barAndDockingController1.AppearancesDocking.PanelCaption.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barAndDockingController1.AppearancesDocking.PanelCaption.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barAndDockingController1.AppearancesDocking.PanelCaption.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.Item.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.Item.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.Item.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.Item.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.PageHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.PageHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.PageHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barAndDockingController1.AppearancesRibbon.PageHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlTop.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlTop.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barDockControlTop.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barDockControlTop.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlBottom.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlBottom.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barDockControlBottom.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barDockControlBottom.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlLeft.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlLeft.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barDockControlLeft.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barDockControlLeft.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlRight.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlRight.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barDockControlRight.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barDockControlRight.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="frm_SL_ReturnArchive.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="frm_SL_ReturnArchive.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="frm_SL_ReturnArchive.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="frm_SL_ReturnArchive.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl5.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl5.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl5.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl5.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl5.Size" type="System.Drawing.Size, System.Drawing">
    <value>50, 13</value>
  </data>
  <data name="labelControl5.Text" xml:space="preserve">
    <value>نوع السداد</value>
  </data>
  <data name="xtraTabControl1.AppearancePage.Header.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="xtraTabControl1.AppearancePage.Header.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="xtraTabControl1.AppearancePage.Header.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="xtraTabControl1.AppearancePage.Header.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="xtraTabControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>706, 362</value>
  </data>
  <data name="txt_Balance_After.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_Balance_After.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_Balance_After.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_Balance_After.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Balance_After.Location" type="System.Drawing.Point, System.Drawing">
    <value>148, 55</value>
  </data>
  <data name="txt_Balance_Before.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_Balance_Before.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_Balance_Before.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_Balance_Before.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Balance_Before.Location" type="System.Drawing.Point, System.Drawing">
    <value>148, 32</value>
  </data>
  <data name="txt_MaxCredit.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_MaxCredit.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_MaxCredit.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_MaxCredit.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_MaxCredit.Location" type="System.Drawing.Point, System.Drawing">
    <value>148, 9</value>
  </data>
  <data name="lbl_IsCredit_After.Location" type="System.Drawing.Point, System.Drawing">
    <value>304, 55</value>
  </data>
  <data name="lbl_IsCredit_After.Size" type="System.Drawing.Size, System.Drawing">
    <value>19, 13</value>
  </data>
  <data name="lbl_IsCredit_After.Text" xml:space="preserve">
    <value>دائن</value>
  </data>
  <data name="lbl_IsCredit_Before.Location" type="System.Drawing.Point, System.Drawing">
    <value>304, 32</value>
  </data>
  <data name="lbl_IsCredit_Before.Size" type="System.Drawing.Size, System.Drawing">
    <value>19, 13</value>
  </data>
  <data name="lbl_IsCredit_Before.Text" xml:space="preserve">
    <value>دائن</value>
  </data>
  <data name="lblBlncAftr.Location" type="System.Drawing.Point, System.Drawing">
    <value>347, 55</value>
  </data>
  <data name="lblBlncAftr.Size" type="System.Drawing.Size, System.Drawing">
    <value>76, 13</value>
  </data>
  <data name="lblBlncAftr.Text" xml:space="preserve">
    <value>رصيد بعد الفاتورة</value>
  </data>
  <data name="labelControl21.Location" type="System.Drawing.Point, System.Drawing">
    <value>347, 9</value>
  </data>
  <data name="labelControl21.Size" type="System.Drawing.Size, System.Drawing">
    <value>50, 13</value>
  </data>
  <data name="labelControl21.Text" xml:space="preserve">
    <value>حد الإئتمان</value>
  </data>
  <data name="labelControl24.Location" type="System.Drawing.Point, System.Drawing">
    <value>347, 32</value>
  </data>
  <data name="labelControl24.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 13</value>
  </data>
  <data name="labelControl24.Text" xml:space="preserve">
    <value>رصيد سابق</value>
  </data>
  <data name="page_AccInfo.Size" type="System.Drawing.Size, System.Drawing">
    <value>458, 99</value>
  </data>
  <data name="page_AccInfo.Text" xml:space="preserve">
    <value>حساب العميل</value>
  </data>
  <data name="xtraTabControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>464, 127</value>
  </data>
  <data name="txtScaleSerial.Location" type="System.Drawing.Point, System.Drawing">
    <value>199, 6</value>
  </data>
  <data name="txtScaleSerial.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtScaleSerial.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtScaleSerial.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <assembly alias="DevExpress.XtraEditors.v15.1" name="DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="txtScaleSerial.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtScaleSerial.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtScaleSerial.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txtScaleSerial.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtScaleSerial.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtScaleSerial.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtScaleSerial.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtScaleSerial.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtScaleSerial.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtScaleSerial.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txtScaleSerial.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtScaleSerial.TabIndex" type="System.Int32, mscorlib">
    <value>242</value>
  </data>
  <data name="labelControl22.Location" type="System.Drawing.Point, System.Drawing">
    <value>377, 9</value>
  </data>
  <data name="labelControl22.Size" type="System.Drawing.Size, System.Drawing">
    <value>42, 13</value>
  </data>
  <data name="labelControl22.Text" xml:space="preserve">
    <value>مسلسل</value>
  </data>
  <data name="txtDestination.Location" type="System.Drawing.Point, System.Drawing">
    <value>199, 75</value>
  </data>
  <data name="txtDestination.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDestination.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDestination.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtDestination.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtDestination.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDestination.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txtDestination.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtDestination.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtDestination.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtDestination.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtDestination.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtDestination.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDestination.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txtDestination.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDestination.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="lblDestination.Location" type="System.Drawing.Point, System.Drawing">
    <value>377, 78</value>
  </data>
  <data name="lblDestination.Size" type="System.Drawing.Size, System.Drawing">
    <value>25, 13</value>
  </data>
  <data name="lblDestination.Text" xml:space="preserve">
    <value>الجهة</value>
  </data>
  <data name="txtVehicleNumber.Location" type="System.Drawing.Point, System.Drawing">
    <value>199, 52</value>
  </data>
  <data name="txtVehicleNumber.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtVehicleNumber.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtVehicleNumber.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtVehicleNumber.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtVehicleNumber.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtVehicleNumber.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txtVehicleNumber.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtVehicleNumber.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtVehicleNumber.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtVehicleNumber.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtVehicleNumber.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtVehicleNumber.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtVehicleNumber.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txtVehicleNumber.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtVehicleNumber.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="lblVehicleNumber.Location" type="System.Drawing.Point, System.Drawing">
    <value>377, 55</value>
  </data>
  <data name="lblVehicleNumber.Size" type="System.Drawing.Size, System.Drawing">
    <value>46, 13</value>
  </data>
  <data name="lblVehicleNumber.Text" xml:space="preserve">
    <value>رقم العربه</value>
  </data>
  <data name="txtDriverName.Location" type="System.Drawing.Point, System.Drawing">
    <value>199, 29</value>
  </data>
  <data name="txtDriverName.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDriverName.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDriverName.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtDriverName.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtDriverName.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDriverName.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txtDriverName.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtDriverName.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtDriverName.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtDriverName.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtDriverName.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtDriverName.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDriverName.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txtDriverName.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDriverName.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="lblDriverName.Location" type="System.Drawing.Point, System.Drawing">
    <value>377, 32</value>
  </data>
  <data name="lblDriverName.Text" xml:space="preserve">
    <value>اسم السائق</value>
  </data>
  <data name="tabExtraData.Size" type="System.Drawing.Size, System.Drawing">
    <value>458, 99</value>
  </data>
  <data name="tabExtraData.Text" xml:space="preserve">
    <value>بيانات اضافية</value>
  </data>
  <data name="cmbPayMethod.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmbPayMethod.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmbPayMethod.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbPayMethod.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="cmbPayMethod.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="cmbPayMethod.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmbPayMethod.Properties.AppearanceDisabled.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbPayMethod.Properties.AppearanceDisabled.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="cmbPayMethod.Properties.AppearanceDisabled.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="cmbPayMethod.Properties.AppearanceDisabled.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmbPayMethod.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="cmbPayMethod.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Near</value>
  </data>
  <data name="cmbPayMethod.Properties.Items" xml:space="preserve">
    <value>آجل</value>
  </data>
  <data name="cmbPayMethod.Properties.Items3" xml:space="preserve">
    <value>كاش</value>
  </data>
  <data name="cmbPayMethod.Properties.Items6" xml:space="preserve">
    <value>اجل/كاش</value>
  </data>
  <data name="cmbPayMethod.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="cmbPayMethod.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chk_IsInTrns.Location" type="System.Drawing.Point, System.Drawing">
    <value>547, 30</value>
  </data>
  <data name="chk_IsInTrns.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_IsInTrns.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_IsInTrns.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="chk_IsInTrns.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="chk_IsInTrns.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="chk_IsInTrns.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_IsInTrns.Properties.AppearanceReadOnly.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="chk_IsInTrns.Properties.AppearanceReadOnly.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="chk_IsInTrns.Properties.AppearanceReadOnly.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="chk_IsInTrns.Properties.AppearanceReadOnly.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_IsInTrns.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="chk_IsInTrns.Properties.Caption" xml:space="preserve">
    <value>تم الاضافة بالمخزن</value>
  </data>
  <data name="chk_IsInTrns.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="chk_IsInTrns.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="chk_IsInTrns.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="btnAddCustomer.ToolTip" xml:space="preserve">
    <value>اضافة عميل</value>
  </data>
  <data name="btn_AddMatrixItems.Location" type="System.Drawing.Point, System.Drawing">
    <value>1012, 340</value>
  </data>
  <data name="btn_AddMatrixItems.Text" xml:space="preserve">
    <value>اضافة اصناف مصفوفة </value>
  </data>
  <data name="btn_AddMatrixItems.ToolTip" xml:space="preserve">
    <value>اضافة اصناف مصفوفة </value>
  </data>
  <data name="labelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>150, 385</value>
  </data>
  <data name="labelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 13</value>
  </data>
  <data name="labelControl1.Text" xml:space="preserve">
    <value>ض.ع</value>
  </data>
  <data name="groupControl1.AppearanceCaption.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="groupControl1.AppearanceCaption.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="groupControl1.AppearanceCaption.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="groupControl1.AppearanceCaption.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Drawers2.Location" type="System.Drawing.Point, System.Drawing">
    <value>186, 25</value>
  </data>
  <data name="lkp_Drawers2.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Drawers2.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Drawers2.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_Drawers2.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_Drawers2.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_Drawers2.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Drawers2.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_Drawers2.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Drawers2.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl17.Location" type="System.Drawing.Point, System.Drawing">
    <value>351, 10</value>
  </data>
  <data name="labelControl17.Size" type="System.Drawing.Size, System.Drawing">
    <value>68, 13</value>
  </data>
  <data name="labelControl17.Text" xml:space="preserve">
    <value>حساب سداد 1</value>
  </data>
  <data name="lkp_Drawers.Location" type="System.Drawing.Point, System.Drawing">
    <value>186, 3</value>
  </data>
  <data name="lkp_Drawers.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Drawers.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Drawers.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_Drawers.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_Drawers.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_Drawers.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Drawers.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns1" xml:space="preserve">
    <value>اسم الحساب</value>
  </data>
  <data name="lkp_Drawers.Properties.Columns8" xml:space="preserve">
    <value>كود الحساب</value>
  </data>
  <data name="lkp_Drawers.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Drawers.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_PayAcc1_Paid.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 4</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_PayAcc1_Paid.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_PayAcc1_Paid.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_PayAcc1_Paid.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_PayAcc1_Paid.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl28.Location" type="System.Drawing.Point, System.Drawing">
    <value>109, 29</value>
  </data>
  <data name="labelControl28.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 13</value>
  </data>
  <data name="labelControl28.Text" xml:space="preserve">
    <value>مدفوع</value>
  </data>
  <data name="labelControl26.Location" type="System.Drawing.Point, System.Drawing">
    <value>109, 8</value>
  </data>
  <data name="labelControl26.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 13</value>
  </data>
  <data name="labelControl26.Text" xml:space="preserve">
    <value>مدفوع</value>
  </data>
  <data name="txt_PayAcc2_Paid.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 26</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_PayAcc2_Paid.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_PayAcc2_Paid.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_PayAcc2_Paid.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_PayAcc2_Paid.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl25.Location" type="System.Drawing.Point, System.Drawing">
    <value>351, 29</value>
  </data>
  <data name="labelControl25.Size" type="System.Drawing.Size, System.Drawing">
    <value>68, 13</value>
  </data>
  <data name="labelControl25.Text" xml:space="preserve">
    <value>حساب سداد 2</value>
  </data>
  <data name="txt_paid.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_paid.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 48</value>
  </data>
  <data name="txt_paid.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_paid.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_paid.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_paid.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_paid.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_paid.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_paid.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_paid.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_paid.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_paid.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_paid.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_paid.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_paid.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_paid.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_paid.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_paid.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_Paid.Location" type="System.Drawing.Point, System.Drawing">
    <value>109, 48</value>
  </data>
  <data name="lbl_Paid.Size" type="System.Drawing.Size, System.Drawing">
    <value>66, 13</value>
  </data>
  <data name="lbl_Paid.Text" xml:space="preserve">
    <value>اجمالي مدفوع</value>
  </data>
  <data name="txt_Remains.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Remains.Location" type="System.Drawing.Point, System.Drawing">
    <value>186, 46</value>
  </data>
  <data name="txt_Remains.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Remains.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Remains.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_Remains.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_Remains.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_Remains.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Remains.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Remains.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_Remains.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Remains.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Remains.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_Remains.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Remains.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Remains.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Remains.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_Remains.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_remains.Location" type="System.Drawing.Point, System.Drawing">
    <value>350, 47</value>
  </data>
  <data name="lbl_remains.Size" type="System.Drawing.Size, System.Drawing">
    <value>31, 13</value>
  </data>
  <data name="lbl_remains.Text" xml:space="preserve">
    <value>متبقي</value>
  </data>
  <data name="groupControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>257, 416</value>
  </data>
  <data name="groupControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>443, 70</value>
  </data>
  <data name="groupControl1.Text" xml:space="preserve">
    <value>مدفوع</value>
  </data>
  <data name="labelControl40.Location" type="System.Drawing.Point, System.Drawing">
    <value>1037, 343</value>
  </data>
  <data name="labelControl40.Size" type="System.Drawing.Size, System.Drawing">
    <value>101, 13</value>
  </data>
  <data name="labelControl40.Text" xml:space="preserve">
    <value>اضافة اصناف مصفوفة </value>
  </data>
  <data name="txtNet.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 467</value>
  </data>
  <data name="txtNet.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtNet.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtNet.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtNet.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtNet.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtNet.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtNet.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtNet.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtNet.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtNet.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txtNet.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtNet.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtNet.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtNet.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtNet.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtNet.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtNet.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txtNet.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl19.Location" type="System.Drawing.Point, System.Drawing">
    <value>78, 364</value>
  </data>
  <data name="labelControl20.Location" type="System.Drawing.Point, System.Drawing">
    <value>133, 385</value>
  </data>
  <data name="labelControl20.Size" type="System.Drawing.Size, System.Drawing">
    <value>10, 13</value>
  </data>
  <data name="labelControl20.Text" xml:space="preserve">
    <value>ق</value>
  </data>
  <data name="labelControl10.Location" type="System.Drawing.Point, System.Drawing">
    <value>81, 407</value>
  </data>
  <data name="labelControl16.Location" type="System.Drawing.Point, System.Drawing">
    <value>81, 427</value>
  </data>
  <data name="lkp_Customers.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Customers.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Customers.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_Customers.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_Customers.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_Customers.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Customers.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_Customers.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Customers.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridView1.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView1.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView1.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView1.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView1.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView1.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn20.Caption" xml:space="preserve">
    <value>الكود</value>
  </data>
  <data name="gridColumn21.Caption" xml:space="preserve">
    <value>الاسم</value>
  </data>
  <data name="gridColumn22.Caption" xml:space="preserve">
    <value>الاسم ج</value>
  </data>
  <data name="gridColumn3.Caption" xml:space="preserve">
    <value>المدينة</value>
  </data>
  <data name="gridColumn6.Caption" xml:space="preserve">
    <value>المحمول</value>
  </data>
  <data name="gridColumn18.Caption" xml:space="preserve">
    <value>الفئة</value>
  </data>
  <data name="txt_Total.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 340</value>
  </data>
  <data name="txt_Total.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Total.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Total.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_Total.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_Total.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_Total.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Total.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Total.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_Total.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Total.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txt_Total.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Total.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_Total.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_Total.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Total.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Total.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Total.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_Total.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Total.Size" type="System.Drawing.Size, System.Drawing">
    <value>117, 20</value>
  </data>
  <data name="labelControl18.Location" type="System.Drawing.Point, System.Drawing">
    <value>133, 343</value>
  </data>
  <data name="labelControl18.Size" type="System.Drawing.Size, System.Drawing">
    <value>47, 13</value>
  </data>
  <data name="labelControl18.Text" xml:space="preserve">
    <value>الاجمــالي</value>
  </data>
  <data name="labelControl11.Location" type="System.Drawing.Point, System.Drawing">
    <value>133, 364</value>
  </data>
  <data name="labelControl11.Text" xml:space="preserve">
    <value>ن</value>
  </data>
  <data name="labelControl8.Location" type="System.Drawing.Point, System.Drawing">
    <value>133, 406</value>
  </data>
  <data name="labelControl8.Text" xml:space="preserve">
    <value>ن</value>
  </data>
  <data name="labelControl15.Location" type="System.Drawing.Point, System.Drawing">
    <value>133, 427</value>
  </data>
  <data name="labelControl15.Text" xml:space="preserve">
    <value>ن</value>
  </data>
  <data name="mi_frm_IC_Item.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 22</value>
  </data>
  <data name="mi_frm_IC_Item.Text" xml:space="preserve">
    <value>بيانات الصنف</value>
  </data>
  <data name="mi_InvoiceStaticDisc.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 22</value>
  </data>
  <data name="mi_InvoiceStaticDisc.Text" xml:space="preserve">
    <value>تثبيت خصومات الفاتورة</value>
  </data>
  <data name="mi_InvoiceStaticDimensions.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 22</value>
  </data>
  <data name="mi_InvoiceStaticDimensions.Text" xml:space="preserve">
    <value>تثبيت الأبعاد</value>
  </data>
  <data name="mi_PasteRows.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 22</value>
  </data>
  <data name="mi_PasteRows.Text" xml:space="preserve">
    <value>لصق الصفوف</value>
  </data>
  <data name="importFromExcelSheetToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>226, 22</value>
  </data>
  <data name="importFromExcelSheetToolStripMenuItem.Text" xml:space="preserve">
    <value>تحميل الأصناف من ملف اكسيل</value>
  </data>
  <data name="contextMenuStrip1.Size" type="System.Drawing.Size, System.Drawing">
    <value>227, 114</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.AllowHtmlTextInToolTip" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.BackgroundImage" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>Tile</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>Inherit</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.MaximumSize" type="System.Drawing.Size, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.TextLocation" type="DevExpress.XtraEditors.NavigatorButtonsTextLocation, DevExpress.XtraEditors.v15.1">
    <value>Center</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.ToolTip" xml:space="preserve">
    <value />
  </data>
  <assembly alias="DevExpress.Utils.v15.1" name="DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="grdPrInvoice.EmbeddedNavigator.ToolTipIconType" type="DevExpress.Utils.ToolTipIconType, DevExpress.Utils.v15.1">
    <value>None</value>
  </data>
  <data name="grdPrInvoice.EmbeddedNavigator.ToolTipTitle" xml:space="preserve">
    <value />
  </data>
  <data name="gridView2.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView2.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView2.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView2.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView2.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView2.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView2.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView2.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn36.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn36.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn36.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn36.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn36.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn36.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn36.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn36.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn35.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn35.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn35.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn35.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn35.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn35.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn35.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn35.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn33.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn33.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn33.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn33.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn33.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn33.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn33.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn33.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn33.Caption" xml:space="preserve">
    <value>شركة منتجة</value>
  </data>
  <data name="gridColumn29.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn29.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn29.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn29.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn29.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn29.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn29.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn29.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn28.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn28.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn28.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn28.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn28.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn28.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn28.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn28.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_TotalSellPrice.Caption" xml:space="preserve">
    <value>الاجمالي</value>
  </data>
  <data name="col_TotalSellPrice.Width" type="System.Int32, mscorlib">
    <value>140</value>
  </data>
  <data name="gridColumn23.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn23.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn23.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn23.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn23.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn23.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn23.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn23.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn23.Caption" xml:space="preserve">
    <value>المورد</value>
  </data>
  <data name="gridView5.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView5.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView5.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView5.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView5.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView5.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView5.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView5.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn40.Caption" xml:space="preserve">
    <value>كود المورد</value>
  </data>
  <data name="gridColumn41.Caption" xml:space="preserve">
    <value>الاسم</value>
  </data>
  <data name="gridColumn42.Caption" xml:space="preserve">
    <value>الاسم ج</value>
  </data>
  <data name="gridColumn43.Caption" xml:space="preserve">
    <value>الرصيد</value>
  </data>
  <data name="gridColumn23.Width" type="System.Int32, mscorlib">
    <value>78</value>
  </data>
  <data name="gridColumn1.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn1.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn1.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn1.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn1.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn1.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn1.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn1.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn1.Caption" xml:space="preserve">
    <value>ن خصم</value>
  </data>
  <data name="repDiscountRatio.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="repDiscountRatio.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repDiscountRatio.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repDiscountRatio.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="repDiscountRatio.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repDiscountRatio.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn1.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn1.VisibleIndex" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="gridColumn1.Width" type="System.Int32, mscorlib">
    <value>43</value>
  </data>
  <data name="gridColumn2.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn2.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn2.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn2.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn2.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn2.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn2.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn2.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn2.Caption" xml:space="preserve">
    <value>ق خصم</value>
  </data>
  <data name="repSpin.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="repSpin.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repSpin.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="repSpin.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repSpin.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="repSpin.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="repSpin.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repSpin.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repSpin.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn2.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn2.VisibleIndex" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="gridColumn2.Width" type="System.Int32, mscorlib">
    <value>74</value>
  </data>
  <data name="gridColumn5.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn5.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn5.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn5.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn5.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn5.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn5.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn5.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn5.Caption" xml:space="preserve">
    <value>س بيع</value>
  </data>
  <data name="gridColumn5.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn5.Width" type="System.Int32, mscorlib">
    <value>112</value>
  </data>
  <data name="colPurchasePrice.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="colPurchasePrice.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="colPurchasePrice.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="colPurchasePrice.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="colPurchasePrice.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="colPurchasePrice.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="colPurchasePrice.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="colPurchasePrice.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="colPurchasePrice.Caption" xml:space="preserve">
    <value>س شراء</value>
  </data>
  <data name="colPurchasePrice.Width" type="System.Int32, mscorlib">
    <value>69</value>
  </data>
  <data name="gridColumn7.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn7.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn7.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn7.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn7.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn7.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn7.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn7.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn7.Caption" xml:space="preserve">
    <value>كمية</value>
  </data>
  <data name="gridColumn7.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn7.Width" type="System.Int32, mscorlib">
    <value>128</value>
  </data>
  <data name="gridColumn8.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn8.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn8.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn8.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn8.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn8.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn8.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn8.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn8.Caption" xml:space="preserve">
    <value>وحدة قياس</value>
  </data>
  <data name="gridView4.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView4.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView4.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView4.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView4.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView4.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView4.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView4.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn16.Caption" xml:space="preserve">
    <value>المعامل</value>
  </data>
  <data name="gridColumn17.Caption" xml:space="preserve">
    <value>الوحدة</value>
  </data>
  <data name="gridColumn8.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="gridColumn8.Width" type="System.Int32, mscorlib">
    <value>137</value>
  </data>
  <data name="gridColumn10.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn10.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn10.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn10.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn10.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn10.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn10.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn10.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn10.Caption" xml:space="preserve">
    <value>اسم الصنف</value>
  </data>
  <data name="repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repositoryItemGridLookUpEdit1View.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="repositoryItemGridLookUpEdit1View.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="repositoryItemGridLookUpEdit1View.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="repositoryItemGridLookUpEdit1View.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn24.Caption" xml:space="preserve">
    <value>س بيع</value>
  </data>
  <data name="gridColumn24.Width" type="System.Int32, mscorlib">
    <value>159</value>
  </data>
  <data name="gridColumn12.Caption" xml:space="preserve">
    <value>اسم الصنف ج</value>
  </data>
  <data name="gridColumn12.Width" type="System.Int32, mscorlib">
    <value>298</value>
  </data>
  <data name="gridColumn13.Caption" xml:space="preserve">
    <value>اسم الصنف </value>
  </data>
  <data name="gridColumn13.Width" type="System.Int32, mscorlib">
    <value>308</value>
  </data>
  <data name="gridColumn14.Caption" xml:space="preserve">
    <value>كود1</value>
  </data>
  <data name="gridColumn14.Width" type="System.Int32, mscorlib">
    <value>183</value>
  </data>
  <data name="gridColumn4.Caption" xml:space="preserve">
    <value>كود2</value>
  </data>
  <data name="gridColumn4.Width" type="System.Int32, mscorlib">
    <value>159</value>
  </data>
  <data name="col_CompanyNameAr.Caption" xml:space="preserve">
    <value>الشركة</value>
  </data>
  <data name="col_CategoryNameAr.Caption" xml:space="preserve">
    <value>الفئة</value>
  </data>
  <data name="gridColumn10.VisibleIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="gridColumn10.Width" type="System.Int32, mscorlib">
    <value>385</value>
  </data>
  <data name="gridColumn11.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn11.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn11.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn11.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn11.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn11.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn11.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn11.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn11.Caption" xml:space="preserve">
    <value>كود2</value>
  </data>
  <data name="gridColumn11.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn11.VisibleIndex" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="gridColumn11.Width" type="System.Int32, mscorlib">
    <value>30</value>
  </data>
  <data name="gridColumn31.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn31.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn31.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn31.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn31.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn31.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn31.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn31.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn31.Caption" xml:space="preserve">
    <value>كود1</value>
  </data>
  <data name="gridColumn31.VisibleIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="gridColumn31.Width" type="System.Int32, mscorlib">
    <value>111</value>
  </data>
  <data name="col_Expire.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_Expire.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_Expire.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_Expire.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_Expire.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_Expire.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_Expire.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_Expire.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_Expire.Caption" xml:space="preserve">
    <value>تاريخ صلاحية</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.Mask.EditMask" xml:space="preserve">
    <value>T</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="rep_expireDate.CalendarTimeProperties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_expireDate.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="rep_expireDate.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_expireDate.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rep_expireDate.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="rep_expireDate.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="rep_expireDate.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rep_expireDate.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rep_expireDate.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_Batch.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_Batch.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_Batch.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_Batch.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_Batch.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_Batch.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_Batch.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_Batch.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_Batch.Caption" xml:space="preserve">
    <value>التشغيلة</value>
  </data>
  <data name="col_Batch.Width" type="System.Int32, mscorlib">
    <value>56</value>
  </data>
  <data name="col_Height.Caption" xml:space="preserve">
    <value>ارتفاع</value>
  </data>
  <data name="col_Width.Caption" xml:space="preserve">
    <value>عرض</value>
  </data>
  <data name="col_Length.Caption" xml:space="preserve">
    <value>طول</value>
  </data>
  <data name="col_TotalQty.Caption" xml:space="preserve">
    <value>اجمالي كمية</value>
  </data>
  <data name="col_PiecesCount.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_PiecesCount.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_PiecesCount.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_PiecesCount.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_PiecesCount.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_PiecesCount.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_PiecesCount.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_PiecesCount.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_PiecesCount.Caption" xml:space="preserve">
    <value>عدد القطع</value>
  </data>
  <data name="col_ItemDescription.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_ItemDescription.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_ItemDescription.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_ItemDescription.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_ItemDescription.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_ItemDescription.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_ItemDescription.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_ItemDescription.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_ItemDescription.Caption" xml:space="preserve">
    <value>وصف</value>
  </data>
  <data name="col_ItemDescriptionEn.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_ItemDescriptionEn.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_ItemDescriptionEn.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_ItemDescriptionEn.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_ItemDescriptionEn.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_ItemDescriptionEn.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_ItemDescriptionEn.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_ItemDescriptionEn.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_ItemDescriptionEn.Caption" xml:space="preserve">
    <value>وصف ج</value>
  </data>
  <data name="col_SalesTax.Caption" xml:space="preserve">
    <value>ض . ع</value>
  </data>
  <data name="col_DiscountRatio2.Caption" xml:space="preserve">
    <value>ن خصم 2</value>
  </data>
  <data name="col_DiscountRatio2.Width" type="System.Int32, mscorlib">
    <value>48</value>
  </data>
  <data name="col_DiscountRatio3.Caption" xml:space="preserve">
    <value>ن خصم 3</value>
  </data>
  <data name="col_DiscountRatio3.Width" type="System.Int32, mscorlib">
    <value>55</value>
  </data>
  <data name="col_Serial.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_Serial.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_Serial.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_Serial.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_Serial.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_Serial.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_Serial.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_Serial.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.EditMask" xml:space="preserve">
    <value>T</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="repManufactureDate.CalendarTimeProperties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repManufactureDate.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="repManufactureDate.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repManufactureDate.Mask.EditMask" xml:space="preserve">
    <value>d</value>
  </data>
  <data name="repManufactureDate.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repManufactureDate.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="repManufactureDate.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="repManufactureDate.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repManufactureDate.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repExpireDate.CalendarTimeProperties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repExpireDate.CalendarTimeProperties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.Mask.EditMask" xml:space="preserve">
    <value>T</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repExpireDate.CalendarTimeProperties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="repExpireDate.CalendarTimeProperties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repExpireDate.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="repExpireDate.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repExpireDate.Mask.EditMask" xml:space="preserve">
    <value>d</value>
  </data>
  <data name="repExpireDate.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repExpireDate.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="repExpireDate.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="repExpireDate.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repExpireDate.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repExpireDate.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridView3.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView3.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView3.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView3.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView3.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView3.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView3.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView3.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="colDescription.Caption" xml:space="preserve">
    <value>الوصف</value>
  </data>
  <data name="colLocationNameAr.Caption" xml:space="preserve">
    <value>اسم الرف</value>
  </data>
  <data name="repExpireDate_txt.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="repExpireDate_txt.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repExpireDate_txt.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repExpireDate_txt.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="repExpireDate_txt.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repExpireDate_txt.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repExpireDate_txt.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="grdPrInvoice.Size" type="System.Drawing.Size, System.Drawing">
    <value>1154, 172</value>
  </data>
  <data name="panelControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 158</value>
  </data>
  <data name="panelControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>1158, 176</value>
  </data>
  <data name="labelControl13.Location" type="System.Drawing.Point, System.Drawing">
    <value>133, 470</value>
  </data>
  <data name="labelControl13.Size" type="System.Drawing.Size, System.Drawing">
    <value>34, 13</value>
  </data>
  <data name="labelControl13.Text" xml:space="preserve">
    <value>الصافي</value>
  </data>
  <data name="labelControl6.Location" type="System.Drawing.Point, System.Drawing">
    <value>62, 364</value>
  </data>
  <data name="labelControl6.Size" type="System.Drawing.Size, System.Drawing">
    <value>10, 13</value>
  </data>
  <data name="labelControl6.Text" xml:space="preserve">
    <value>ق</value>
  </data>
  <data name="labelControl14.Location" type="System.Drawing.Point, System.Drawing">
    <value>62, 427</value>
  </data>
  <data name="labelControl14.Size" type="System.Drawing.Size, System.Drawing">
    <value>10, 13</value>
  </data>
  <data name="labelControl14.Text" xml:space="preserve">
    <value>ق</value>
  </data>
  <data name="labelControl4.Location" type="System.Drawing.Point, System.Drawing">
    <value>62, 407</value>
  </data>
  <data name="labelControl4.Size" type="System.Drawing.Size, System.Drawing">
    <value>10, 13</value>
  </data>
  <data name="labelControl4.Text" xml:space="preserve">
    <value>ق</value>
  </data>
  <data name="labelControl7.Location" type="System.Drawing.Point, System.Drawing">
    <value>150, 364</value>
  </data>
  <data name="labelControl7.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 13</value>
  </data>
  <data name="labelControl7.Text" xml:space="preserve">
    <value>خصم</value>
  </data>
  <data name="labelControl12.Location" type="System.Drawing.Point, System.Drawing">
    <value>150, 427</value>
  </data>
  <data name="labelControl12.Size" type="System.Drawing.Size, System.Drawing">
    <value>20, 13</value>
  </data>
  <data name="labelControl12.Text" xml:space="preserve">
    <value>ض.أ</value>
  </data>
  <data name="labelControl3.Location" type="System.Drawing.Point, System.Drawing">
    <value>150, 406</value>
  </data>
  <data name="labelControl3.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 13</value>
  </data>
  <data name="labelControl3.Text" xml:space="preserve">
    <value>ض.خ</value>
  </data>
  <data name="labelControl9.Location" type="System.Drawing.Point, System.Drawing">
    <value>133, 448</value>
  </data>
  <data name="labelControl9.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 13</value>
  </data>
  <data name="labelControl9.Text" xml:space="preserve">
    <value>تكاليف على العميل</value>
  </data>
  <data name="flowLayoutPanel1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="textEdit8.EditValue" xml:space="preserve">
    <value>الدفتر</value>
  </data>
  <data name="textEdit8.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit8.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit8.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="textEdit8.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="textEdit8.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="textEdit8.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit8.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit8.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="textEdit8.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit8.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit8.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit8.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="textEdit8.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="textEdit8.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit8.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit8.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit8.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit8.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit8.Size" type="System.Drawing.Size, System.Drawing">
    <value>111, 22</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_InvoiceBook.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_InvoiceBook.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_InvoiceBook.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns1" xml:space="preserve">
    <value>اسم الدفتر</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.Columns8" xml:space="preserve">
    <value>خاضع للضريبة</value>
  </data>
  <data name="lkp_InvoiceBook.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_InvoiceBook.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_InvoiceBook.Size" type="System.Drawing.Size, System.Drawing">
    <value>111, 20</value>
  </data>
  <data name="pnlBook.Location" type="System.Drawing.Point, System.Drawing">
    <value>668, 1</value>
  </data>
  <data name="pnlBook.Size" type="System.Drawing.Size, System.Drawing">
    <value>117, 44</value>
  </data>
  <data name="textEdit7.EditValue" xml:space="preserve">
    <value>رقم الفاتورة</value>
  </data>
  <data name="textEdit7.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit7.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit7.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="textEdit7.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="textEdit7.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="textEdit7.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit7.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit7.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="textEdit7.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit7.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit7.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit7.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="textEdit7.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="textEdit7.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit7.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit7.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit7.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit7.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit7.Size" type="System.Drawing.Size, System.Drawing">
    <value>81, 22</value>
  </data>
  <data name="txtInvoiceCode.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtInvoiceCode.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtInvoiceCode.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtInvoiceCode.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtInvoiceCode.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtInvoiceCode.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtInvoiceCode.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtInvoiceCode.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtInvoiceCode.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtInvoiceCode.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txtInvoiceCode.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtInvoiceCode.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtInvoiceCode.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtInvoiceCode.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtInvoiceCode.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtInvoiceCode.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtInvoiceCode.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txtInvoiceCode.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtInvoiceCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>81, 20</value>
  </data>
  <data name="pnlInvCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>582, 1</value>
  </data>
  <data name="pnlInvCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>84, 44</value>
  </data>
  <data name="textEdit6.EditValue" xml:space="preserve">
    <value>التاريخ</value>
  </data>
  <data name="textEdit6.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit6.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit6.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="textEdit6.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="textEdit6.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="textEdit6.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit6.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit6.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="textEdit6.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit6.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit6.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit6.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="textEdit6.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="textEdit6.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit6.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit6.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit6.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit6.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtInvoiceDate.EditValue" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtInvoiceDate.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtInvoiceDate.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtInvoiceDate.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="dtInvoiceDate.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="dtInvoiceDate.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="dtInvoiceDate.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtInvoiceDate.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.EditMask" xml:space="preserve">
    <value>T</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="dtInvoiceDate.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dtInvoiceDate.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dtInvoiceDate.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="dtInvoiceDate.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="pnlDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>458, 1</value>
  </data>
  <data name="textEdit5.EditValue" xml:space="preserve">
    <value>الفرع</value>
  </data>
  <data name="textEdit5.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 2</value>
  </data>
  <data name="textEdit5.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit5.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit5.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="textEdit5.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="textEdit5.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="textEdit5.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit5.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit5.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="textEdit5.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit5.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit5.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit5.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="textEdit5.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="textEdit5.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit5.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit5.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit5.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit5.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 23</value>
  </data>
  <data name="lkpStore.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpStore.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpStore.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpStore.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpStore.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpStore.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpStore.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpStore.Properties.Columns1" xml:space="preserve">
    <value>اسم الفرع</value>
  </data>
  <data name="lkpStore.Properties.Columns8" xml:space="preserve">
    <value>كود الفرع</value>
  </data>
  <data name="lkpStore.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="pnlBranch.Location" type="System.Drawing.Point, System.Drawing">
    <value>314, 1</value>
  </data>
  <data name="pnlBranch.Size" type="System.Drawing.Size, System.Drawing">
    <value>142, 44</value>
  </data>
  <data name="txtCurrency.EditValue" xml:space="preserve">
    <value>العملة</value>
  </data>
  <data name="txtCurrency.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtCurrency.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtCurrency.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtCurrency.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtCurrency.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtCurrency.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtCurrency.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtCurrency.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtCurrency.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtCurrency.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txtCurrency.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtCurrency.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtCurrency.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtCurrency.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtCurrency.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtCurrency.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtCurrency.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txtCurrency.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtCurrency.Size" type="System.Drawing.Size, System.Drawing">
    <value>142, 22</value>
  </data>
  <data name="uc_Currency1.Size" type="System.Drawing.Size, System.Drawing">
    <value>143, 19</value>
  </data>
  <data name="pnlCrncy.Location" type="System.Drawing.Point, System.Drawing">
    <value>166, 1</value>
  </data>
  <data name="pnlCrncy.Size" type="System.Drawing.Size, System.Drawing">
    <value>146, 44</value>
  </data>
  <data name="textEdit3.EditValue" xml:space="preserve">
    <value>مسئول البيع</value>
  </data>
  <data name="textEdit3.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit3.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit3.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="textEdit3.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="textEdit3.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="textEdit3.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit3.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit3.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="textEdit3.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit3.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit3.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit3.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="textEdit3.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="textEdit3.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit3.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit3.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit3.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit3.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit3.Size" type="System.Drawing.Size, System.Drawing">
    <value>140, 22</value>
  </data>
  <data name="lkp_SalesEmp.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_SalesEmp.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_SalesEmp.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_SalesEmp.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns1" xml:space="preserve">
    <value>اسم الموظف</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns10" xml:space="preserve">
    <value>كود</value>
  </data>
  <data name="lkp_SalesEmp.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_SalesEmp.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_SalesEmp.Size" type="System.Drawing.Size, System.Drawing">
    <value>140, 20</value>
  </data>
  <data name="pnlSalesEmp.Location" type="System.Drawing.Point, System.Drawing">
    <value>19, 1</value>
  </data>
  <data name="pnlSalesEmp.Size" type="System.Drawing.Size, System.Drawing">
    <value>145, 44</value>
  </data>
  <data name="lkpCostCenter.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpCostCenter.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpCostCenter.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpCostCenter.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpCostCenter.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpCostCenter.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpCostCenter.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns8" xml:space="preserve">
    <value>مركز التكلفة</value>
  </data>
  <data name="lkpCostCenter.Properties.Columns15" xml:space="preserve">
    <value>كود مركز التكلفة</value>
  </data>
  <data name="lkpCostCenter.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkpCostCenter.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpCostCenter.Size" type="System.Drawing.Size, System.Drawing">
    <value>113, 20</value>
  </data>
  <data name="textEdit1.EditValue" xml:space="preserve">
    <value>مركز تكلفة</value>
  </data>
  <data name="textEdit1.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit1.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit1.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="textEdit1.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="textEdit1.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="textEdit1.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="textEdit1.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit1.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="textEdit1.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit1.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit1.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit1.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="textEdit1.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="textEdit1.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit1.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="textEdit1.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit1.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="textEdit1.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="textEdit1.Size" type="System.Drawing.Size, System.Drawing">
    <value>113, 22</value>
  </data>
  <data name="pnlCostCenter.Location" type="System.Drawing.Point, System.Drawing">
    <value>663, 47</value>
  </data>
  <data name="txtSourceCode.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtSourceCode.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtSourceCode.Properties.AppearanceReadOnly.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtSourceCode.Properties.AppearanceReadOnly.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtSourceCode.Properties.AppearanceReadOnly.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtSourceCode.Properties.AppearanceReadOnly.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtSourceCode.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtSourceCode.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtSourceCode.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtSourceCode.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txtSourceCode.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtSourceCode.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtSourceCode.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtSourceCode.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtSourceCode.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtSourceCode.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtSourceCode.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txtSourceCode.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="btnSourceId.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="btnSourceId.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="btnSourceId.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="btnSourceId.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="btnSourceId.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="btnSourceId.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="btnSourceId.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="btnSourceId.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="btnSourceId.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="btnSourceId.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="btnSourceId.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="btnSourceId.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="btnSourceId.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="btnSourceId.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmdProcess.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmdProcess.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmdProcess.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="cmdProcess.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="cmdProcess.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="cmdProcess.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmdProcess.Properties.AppearanceReadOnly.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="cmdProcess.Properties.AppearanceReadOnly.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="cmdProcess.Properties.AppearanceReadOnly.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="cmdProcess.Properties.AppearanceReadOnly.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmdProcess.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cmdProcess.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Near</value>
  </data>
  <data name="cmdProcess.Properties.Items" xml:space="preserve">
    <value>البسكول</value>
  </data>
  <data name="cmdProcess.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="cmdProcess.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="pnlSrcPrc.Location" type="System.Drawing.Point, System.Drawing">
    <value>564, 47</value>
  </data>
  <data name="flowLayoutPanel1.Location" type="System.Drawing.Point, System.Drawing">
    <value>369, 5</value>
  </data>
  <data name="flowLayoutPanel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>786, 92</value>
  </data>
  <data name="labelControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>41, 13</value>
  </data>
  <data name="labelControl2.Text" xml:space="preserve">
    <value>ملاحظات</value>
  </data>
  <data name="txtNotes.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtNotes.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtNotes.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txtNotes.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl36.Size" type="System.Drawing.Size, System.Drawing">
    <value>80, 13</value>
  </data>
  <data name="labelControl36.Text" xml:space="preserve">
    <value>مردودات المبيعات</value>
  </data>
  <data name="labelControl35.Size" type="System.Drawing.Size, System.Drawing">
    <value>31, 13</value>
  </data>
  <data name="labelControl35.Text" xml:space="preserve">
    <value>العميل</value>
  </data>
  <data name="btnNext.ToolTip" xml:space="preserve">
    <value>التالي</value>
  </data>
  <data name="btnPrevious.ToolTip" xml:space="preserve">
    <value>السابق</value>
  </data>
  <data name="txtExpenses.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 445</value>
  </data>
  <data name="txtExpenses.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtExpenses.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtExpenses.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtExpenses.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtExpenses.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtExpenses.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtExpenses.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtExpenses.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtExpenses.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtExpenses.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtExpenses.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="txtExpenses.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtExpenses.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtExpenses.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtExpenses.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtExpenses.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txtExpenses.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDiscountRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>95, 361</value>
  </data>
  <data name="txtDiscountRatio.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDiscountRatio.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDiscountRatio.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtDiscountRatio.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtDiscountRatio.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtDiscountRatio.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDiscountRatio.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtDiscountRatio.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtDiscountRatio.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDiscountRatio.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txtDiscountRatio.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtDiscountRatio.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtDiscountRatio.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtDiscountRatio.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtDiscountRatio.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDiscountRatio.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txtDiscountRatio.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDiscountValue.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 361</value>
  </data>
  <data name="txtDiscountValue.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDiscountValue.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDiscountValue.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtDiscountValue.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtDiscountValue.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtDiscountValue.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDiscountValue.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtDiscountValue.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDiscountValue.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txtDiscountValue.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_TaxValue.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 382</value>
  </data>
  <data name="txt_TaxValue.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_TaxValue.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_TaxValue.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_TaxValue.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_TaxValue.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_TaxValue.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_TaxValue.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_TaxValue.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_TaxValue.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_TaxValue.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DeductTaxR.Location" type="System.Drawing.Point, System.Drawing">
    <value>95, 403</value>
  </data>
  <data name="txt_DeductTaxR.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_DeductTaxR.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_DeductTaxR.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_DeductTaxR.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_DeductTaxR.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DeductTaxR.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_DeductTaxR.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DeductTaxV.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 404</value>
  </data>
  <data name="txt_DeductTaxV.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_DeductTaxV.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_DeductTaxV.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_DeductTaxV.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_DeductTaxV.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DeductTaxV.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_DeductTaxV.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AddTaxR.Location" type="System.Drawing.Point, System.Drawing">
    <value>95, 424</value>
  </data>
  <data name="txt_AddTaxR.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_AddTaxR.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_AddTaxR.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_AddTaxR.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_AddTaxR.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_AddTaxR.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_AddTaxR.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txt_AddTaxR.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_AddTaxR.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AddTaxR.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_AddTaxR.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AddTaxV.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 424</value>
  </data>
  <data name="txt_AddTaxV.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_AddTaxV.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_AddTaxV.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_AddTaxV.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_AddTaxV.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_AddTaxV.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_AddTaxV.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_AddTaxV.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_AddTaxV.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_AddTaxV.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAnJIAAAEAIABwbwEAFgAAACgAAACcAAAAJAEAAAEAIAAAAAAA4GMBAAAAAAAAAAAAAAAAAAAA
        AAD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8Anb3/Qi1y/7Mfaf/BH2n/wR9p/8FGg/+Z0OD/EP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AHih/2gfY//BH2P/wR9j
        /8EfY//Bnbr/Qv///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8AzMzNFDY1N6sLCwzVCwsM1QsLDNUaGhvFlJSUTP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A0OD/EB9p/8Efav/BH2r/wR9q//8fav//H2r//x9q//8fav//H2r/wR9q
        /8FGhP+Z////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AJ26
        /0IfY//BH2P/wR9j/8EfY///H2P//x9j//8fY///H2P/wR9j/8EfY//Bnbr/Qv///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////ADY2N6kMDA3TDAsN1QwLDf8MCw3/DAsN/wsL
        DP8LCwz/CwsM1QsLDNULCwzVzMzMFP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wAtc/+zH2r/wR9q//8fav//H2r//x9q
        //8fa///H2v//x9r//8fa///H2v//x9r//8fa//BH2v/wZ2+/0L///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8AH2P/wR9j/8EfY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j
        //8fY///H2P/wR9j/8H///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wCUlJVMDAwN0wwM
        DdMMDA3/DAwN/wwMDf8MCw3/DAsN/wwLDf8MCw3/CwsM/wsLDP8LCwz/CwsM1RoaG8X///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AB9q
        /8Efav/BH2v//x9r//8fa///H2v//x9r//8fa///H2v//x9s//8fbP//H2z//x9s//8fbP//H2z//x9s
        /8F4pv9o////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////ANDf/xAfY//BH2P/wR9j//8fY///H2P//x9j
        //8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j/8EfY//B0N//EP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AG1sbXQNDA7TDQwO/wwMDv8MDA3/DAwN/wwMDf8MDA3/DAwN/wwMDf8MCw3/DAsN/wwL
        Df8LCwz/CwsM/wsLDNULCwzV////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8AH2v/wR9r/8Efa///H2z//x9s//8fbP//H2z//x9s//8fbP//H2z//x9s
        //8fbP//H2z//x9s//8fbP//H2z//x9s//8fbP/BeKb/aP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A0N//EB9j
        /8EfY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j
        //8fY///H2P/wdDf/xD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8AbW1tcg0NDtMNDA7/DQwO/w0MDv8NDA7/DQwO/wwM
        Dv8MDA3/DAwN/wwMDf8MDA3/DAwN/wwLDf8MCw3/DAsN/wsLDP8LCwzVCwsM1f///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wAfbP/BH2z/wR9s//8fbP//H2z//x9s
        //8fbP//H23//x9t//8fbf//H23//x9t//8fbf//H23//x9t//8fbf//H23//x9t//8fbf//H23/wXin
        /2j///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wDQ3/8QH2T/wR9k//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j
        //8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j/8HQ3/8Q////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wBtbW5yDQ0P0w0N
        Dv8NDQ7/DQ0O/w0NDv8NDA7/DQwO/w0MDv8NDA7/DAwO/wwMDf8MDA3/DAwN/wwMDf8MCw3/DAsN/wwL
        Df8MCw3/CwsM1QsLDNX///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AB9s
        /8EfbP/BH2z//x9t//8fbf//H23//x9t//8fbf//H23//x9t//8fbf//H27//x9u//8fbv//H27//x9u
        //8fbv//H27//x9u//8fbv//H27//x9u/8F4p/9o////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////ANDf/xAfZv/BH2X//x9l//8fZP//H2T//x9j
        //8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j
        //8fY//B0N//EP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AG1tbnIODQ/TDQ0P/w0ND/8NDQ//DQ0O/w0NDv8NDQ7/DQ0O/w0MDv8NDA7/DQwO/w0M
        Dv8MDA7/DAwN/wwMDf8MDA3/DAwN/wwLDf8MCw3/DAsN/wsLDNULCwzV////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8AH2z/wR9t/8Efbf//H23//x9t//8fbf//H27//x9u//8fbv//H27//x9u
        //8fbv//H27//x9u//8fbv//H27//x9u//8fb///H2///x9v//8fb///H27//x9u//8fbv/BeKf/aP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A0N//EB9n
        /8EfZv//H2b//x9l//8fZf//H2T//x9k//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j
        //8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P/wdDf/xD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8AbW1ucg4ND9MODQ//Dg0P/w4ND/8ODQ//DQ0P/w0N
        D/8NDQ//DQ0O/w0NDv8NDQ7/DQwO/w0MDv8NDA7/DQwO/wwMDv8MDA3/DAwN/wwMDf8MDA3/DAsN/wwL
        Df8MCw3VCwsM1f///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wAfbf/BH23/wR9t//8fbv//H27//x9u
        //8fbv//H27//x9u//8fb///H2///x9v//8fb///H2///x9v//8fb///H2///x9v//8fb///H2///x9v
        //8fb///H2///x9v//8fb///H2//wXio/2j///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wDQ4P8QH2j/wR9n//8fZ///H2b//x9m//8fZv//H2X//x9l//8fZP//H2T//x9j
        //8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j
        /8HQ3/8Q////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wBtbW9yDg4Q0Q4O
        D/8ODg//Dg0P/w4ND/8ODQ//Dg0P/w4ND/8ODQ//DQ0P/w0ND/8NDQ7/DQ0O/w0NDv8NDA7/DQwO/w0M
        Dv8NDA7/DAwO/wwMDf8MDA3/DAwN/wwLDf8MCw3/DAsN1QsLDNX///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AB9t
        /8Efbv/BH27//x9u//8fbv//H27//x9v//8fb///H2///x9v//8fb///H2///x9v//8fcP//H3D//x9w
        //8fcP//H3D//x9w//8fcP//H3D//x9w//8fcP//H3D//x9w//8fcP//H3D//x9w/8F4qf9o////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////ANDg/xAfaf/BH2j//x9o//8faP//H2f//x9n
        //8fZv//H2b//x9l//8fZf//H2T//x9k//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j
        //8fY///H2P//x9j//8fY///H2P//x9j//8fY//B0N//EP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AG5tb3IPDhDRDg4Q/w4OEP8ODhD/Dg4Q/w4OD/8ODg//Dg0P/w4ND/8ODQ//Dg0P/w4N
        D/8NDQ//DQ0P/w0NDv8NDQ7/DQ0O/w0MDv8NDA7/DQwO/wwMDv8MDA3/DAwN/wwMDf8MDA3/DAsN/wwL
        DdUMCw3V////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8AH27/wR9u/8Efbv//H27//x9v//8fb///H2///x9v//8fb///H3D//x9w
        //8fcP//H3D//x9w//8fcP//H3D//x9w//8fcP//H3H//x9x//8fcf//H3H//x9x//8fcf//H3H//x9x
        //8fcP//H3D//x9w//8fcP/BeKn/aP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A0OD/EB9q
        /8Efav//H2n//x9p//8faP//H2j//x9n//8fZ///H2b//x9m//8fZf//H2X//x9k//8fZP//H2P//x9j
        //8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P/wdDf
        /xD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8Abm1vcg8OENEPDhD/Dw4Q/w8OEP8PDhD/Dg4Q/w4O
        EP8ODhD/Dg4Q/w4OD/8ODQ//Dg0P/w4ND/8ODQ//Dg0P/w0ND/8NDQ//DQ0O/w0NDv8NDQ7/DQwO/w0M
        Dv8NDA7/DAwO/wwMDf8MDA3/DAwN/wwLDf8MCw3VDAsN1f///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wAfbv/BH27/wR9v//8fb///H2///x9v
        //8fcP//H3D//x9w//8fcP//H3D//x9w//8fcf//H3H//x9x//8fcf//H3H//x9x//8fcf//H3H//x9x
        //8fcf//H3H//x9x//8fcf//H3H//x9x//8fcf//H3H//x9x//8fcf//H3H/wXip/2j///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wDQ4P8QH2v/wR9r//8fav//H2r//x9p//8faf//H2j//x9o//8fZ///H2f//x9m
        //8fZv//H2b//x9l//8fZP//H2T//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j
        //8fY///H2P//x9j//8fY///H2P//x9j/8HQ3/8Q////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wBubm9yDw8Q0Q8P
        EP8PDhD/Dw4Q/w8OEP8PDhD/Dw4Q/w8OEP8ODhD/Dg4Q/w4OEP8ODhD/Dg4P/w4ND/8ODQ//Dg0P/w4N
        D/8ODQ//DQ0P/w0ND/8NDQ7/DQ0O/w0NDv8NDA7/DQwO/wwMDv8MDA3/DAwN/wwMDf8MDA3/DAsN1QwL
        DdX///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AB9v
        /8Efb//BH2///x9v//8fcP//H3D//x9w//8fcP//H3D//x9x//8fcf//H3H//x9x//8fcf//H3H//x9x
        //8fcv//H3L//x9y//8fcv//H3L//x9y//8fcv//H3L//x9y//8fcv//H3L//x9y//8fcv//H3L//x9y
        //8fcv//H3L//x9x/8F4qf9o////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////ANDg/xAfbP/BH2z//x9r//8fa///H2r//x9q
        //8fav//H2n//x9p//8faP//H2j//x9n//8fZ///H2b//x9m//8fZf//H2X//x9k//8fY///H2P//x9j
        //8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY//B0N//EP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AG9ub3IPDxHRDw8R/w8PEf8PDxH/Dw8Q/w8PEP8PDhD/Dw4Q/w8OEP8PDhD/Dw4Q/w4O
        EP8ODhD/Dg4Q/w4OEP8ODg//Dg0P/w4ND/8ODQ//Dg0P/w0ND/8NDQ//DQ0O/w0NDv8NDQ7/DQwO/w0M
        Dv8NDA7/DAwO/wwMDf8MDA3/DAwN/wwLDdUMCw3V////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8AH2//wR9v/8Efb///H3D//x9w//8fcP//H3D//x9x//8fcf//H3H//x9x
        //8fcf//H3L//x9y//8fcv//H3L//x9y//8fcv//H3L//x9y//8fcv//H3L//x9y//8fcv//H3L//x9y
        //8fcv//H3L//x9y//8fcv//H3L//x9y//8fcv//H3L//x9y//8fcv/BeKr/aP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A0OH/EB9t
        /8Efbf//H2z//x9s//8fbP//H2v//x9r//8fav//H2r//x9p//8faf//H2j//x9o//8fZ///H2f//x9m
        //8fZv//H2X//x9l//8fZP//H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j
        //8fY///H2P//x9j//8fY///H2P/wdDf/xD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8Ab25vchAPEdEQDxH/EA8R/xAPEf8PDxH/Dw8R/w8P
        Ef8PDxH/Dw8Q/w8PEP8PDhD/Dw4Q/w8OEP8PDhD/Dw4Q/w4OEP8ODhD/Dg4Q/w4OD/8ODQ//Dg0P/w4N
        D/8ODQ//DQ0P/w0ND/8NDQ7/DQ0O/w0NDv8NDA7/DQwO/wwMDv8MDA3/DAwN/wwMDf8MCw3VDAsN1f//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wAteP+zH2//wR9w//8fcP//H3D//x9x
        //8fcf//H3H//x9x//8fcv//H3L//x9y//8fcv//H3L//x9y//8fcv//H3P//x5z//8ec///HnP//x5z
        //8ec///HnP//x5z//8ec///HnP//x5z//8ec///HnP//x5z//8ec///HnP//x5z//8ec///HnP//x9z
        //8fcv//H3L/wZ3B/0L///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8AH27/wR9u//8fbf//H23//x9t//8fbP//H2z//x9r//8fa///H2r//x9q
        //8fav//H2n//x9o//8faP//H2f//x9n//8fZv//H2b//x9l//8fZf//H2T//x9j//8fY///H2P//x9j
        //8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j/8H///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wCWlpdKEBASzxAP
        Ef8QDxH/EA8R/xAPEf8QDxH/EA8R/xAPEf8PDxH/Dw8R/w8PEf8PDxH/Dw8Q/w8OEP8PDhD/Dw4Q/w8O
        EP8PDhD/Dg4Q/w4OEP8ODhD/Dg4P/w4ND/8ODQ//Dg0P/w4ND/8NDQ//DQ0O/w0NDv8NDQ7/DQwO/w0M
        Dv8NDA7/DAwN/wwMDf8MDA3/DAwN0xsaHMX///8A////AP///wD///8A////AP///wD///8A////AJ3A
        /0IfcP/BH3D//x9w//8fcf//H3H//x9x//8fcf//H3L//x9y//8fcv//H3L//x9y//8ec///HnP//x5z
        //8ec///HnP//x5z//8ec///HnP//x5z//8edP//HnT//x50//8edP//HnT//x50//8edP//HnT//x50
        //8edP//HnP//x5z//8ec///HnP//x5z//8ec///HnP//x5z/8Eec//BH3L/wR9y/8Efcv/BH3L/wR9y
        /8Efcv/BH3H/wR9x/8Efcf/BH3D/wR9w/8EfcP/BH3D/wR9v/8Efb//BH2//wR9u//8fbv//H23//x9t
        //8fbf//H2z//x9s//8fbP//H2v//x9q//8fav//H2r//x9p//8faf//H2j//x9n//8fZ///H2b//x9m
        //8fZf//H2X//x9k//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j
        //8fY///H2P//x9j/8Etbf+z////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wAQEBLPEBAS/xAQEv8QEBL/EBAS/xAPEf8QDxH/EA8R/xAPEf8QDxH/EA8R/w8P
        Ef8PDxH/Dw8R/w8PEf8PDxD/Dw4Q/w8OEP8PDhD/Dw4Q/w4OEP8ODhD/Dg4Q/w4OD/8ODQ//Dg0P/w4N
        D/8ODQ//DQ0P/w0ND/8NDQ7/DQ0O/w0MDv8NDA7/DQwO/wwMDv8MDA3/DAwN/wwMDdOUlJVM////AP//
        /wD///8A////AP///wD///8A////AB9w/8EfcP//H3H//x9x//8fcf//H3L//x9y//8fcv//H3L//x9y
        //8ec///HnP//x5z//8ec///HnP//x5z//8edP//HnT//x50//8edP//HnT//x50//8edP//HnT//x50
        //8edP//HnT//x50//8edP//HnT//x50//8edP//HnT//x50//8edP//HnT//x50//8edP//HnP//x5z
        //8ec///HnP//x5z//8ec///H3L//x9y//8fcv//H3L//x9y//8fcf//H3H//x9x//8fcf//H3D//x9w
        //8fcP//H2///x9v//8fbv//H27//x9u//8fbf//H23//x9t//8fbP//H2z//x9r//8fa///H2r//x9q
        //8faf//H2n//x9o//8fZ///H2f//x9m//8fZv//H2X//x9l//8fZP//H2P//x9j//8fY///H2P//x9j
        //8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY//B////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////ADo5O6cREBLPEBAS/xAQEv8QEBL/EBAS/xAQ
        Ev8QEBL/EBAS/xAPEf8QDxH/EA8R/xAPEf8QDxH/Dw8R/w8PEf8PDxH/Dw8R/w8PEP8PDhD/Dw4Q/w8O
        EP8PDhD/Dg4Q/w4OEP8ODhD/Dg4P/w4ND/8ODQ//Dg0P/w0ND/8NDQ//DQ0O/w0NDv8NDQ7/DQwO/w0M
        Dv8MDA7/DAwN/wwMDf8MDA3T////AP///wD///8A////AP///wD///8AutP/Jh9x/8Efcf//H3H//x9y
        //8fcv//H3L//x9y//8ec///HnP//x5z//8ec///HnP//x50//8edP//HnT//x50//8edP//HnT//x50
        //8edP//HnX//x51//8edf//HnX//x51//8edf//HnX//x51//8edf//HnX//x51//8edf//HnX//x51
        //8edP//HnT//x50//8edP//HnT//x50//8edP//HnT//x5z//8ec///HnP//x5z//8ec///H3L//x9y
        //8fcv//H3L//x9x//8fcf//H3H//x9w//8fcP//H3D//x9v//8fb///H2///x9u//8fbv//H23//x9t
        //8fbf//H2z//x9s//8fa///H2v//x9q//8fav//H2n//x9p//8faP//H2j//x9n//8fZv//H2b//x9l
        //8fZf//H2T//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j
        //8fY//BLW3/s////wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////ABEQ
        Es8REBL/ERAS/xEQEv8REBL/ERAS/xAQEv8QEBL/EBAS/xAQEv8QEBL/EBAS/xAPEf8QDxH/EA8R/xAP
        Ef8QDxH/Dw8R/w8PEf8PDxH/Dw8Q/w8OEP8PDhD/Dw4Q/w8OEP8ODhD/Dg4Q/w4OD/8ODQ//Dg0P/w4N
        D/8ODQ//DQ0P/w0NDv8NDQ7/DQ0O/w0MDv8NDA7/DAwO/wwMDf8MDA3TtbW1Kv///wD///8A////AP//
        /wD///8ALXr/sx9x//8fcv//H3L//x9y//8fcv//HnP//x5z//8ec///HnP//x50//8edP//HnT//x50
        //8edP//HnT//x51//8edf//HnX//x51//8edf//HnX//x51//8edf//HnX//x51//8edf//HnX//x51
        //8edf//HnX//x51//8edf//HnX//x51//8edf//HnX//x51//8edf//HnX//x50//8edP//HnT//x50
        //8edP//HnT//x5z//8ec///HnP//x5z//8fcv//H3L//x9y//8fcv//H3H//x9x//8fcf//H3D//x9w
        //8fcP//H2///x9v//8fbv//H27//x9u//8fbf//H23//x9s//8fbP//H2v//x9r//8fav//H2r//x9p
        //8faf//H2j//x9o//8fZ///H2b//x9m//8fZf//H2X//x9k//8fY///H2P//x9j//8fY///H2P//x9j
        //8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P/wf///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A3t7fAhERE88RERP/ERAT/xEQE/8REBL/ERAS/xEQEv8REBL/ERAS/xAQ
        Ev8QEBL/EBAS/xAQEv8QEBL/EA8R/xAPEf8QDxH/EA8R/w8PEf8PDxH/Dw8R/w8PEP8PDhD/Dw4Q/w8O
        EP8PDhD/Dg4Q/w4OEP8ODhD/Dg4P/w4ND/8ODQ//Dg0P/w0ND/8NDQ//DQ0O/w0NDv8NDA7/DQwO/wwM
        Dv8MDA3/Gxscxf///wD///8A////AP///wD///8AH3L/wR9y//8fcv//H3L//x5z//8ec///HnP//x5z
        //8edP//HnT//x50//8edP//HnT//x51//8edf//HnX//x51//8edf//HnX//x51//8edv//Hnb//x52
        //8edv//Hnb//x52//8edv//Hnb//x52//8edv//Hnb//x52//8edv//Hnb//x52//8edv//HnX//x51
        //8edf//HnX//x51//8edf//HnX//x50//8edP//HnT//x50//8edP//HnP//x5z//8ec///HnP//x9y
        //8fcv//H3L//x9y//8fcf//H3H//x9x//8fcP//H3D//x9v//8fb///H2///x9u//8fbv//H23//x9t
        //8fbP//H2z//x9r//8fa///H2r//x9q//8faf//H2n//x9o//8fZ///H2f//x9m//8fZv//H2X//x9l
        //8fZP//H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P/wf//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AlpaXShERE/8RERP/ERET/xER
        E/8RERP/ERAT/xEQE/8REBL/ERAS/xEQEv8REBL/EBAS/xAQEv8QEBL/EBAS/xAQEv8QDxH/EA8R/xAP
        Ef8QDxH/Dw8R/w8PEf8PDxH/Dw8Q/w8OEP8PDhD/Dw4Q/w8OEP8ODhD/Dg4Q/w4OD/8ODQ//Dg0P/w4N
        D/8NDQ//DQ0P/w0NDv8NDQ7/DQwO/w0MDv8MDA7/DAwN0////wD///8A////AP///wD///8AH3L/wR9y
        //8ec///HnP//x5z//8ec///HnT//x50//8edP//HnT//x51//8edf//HnX//x51//8edf//HnX//x52
        //8edv//Hnb//x52//8edv//Hnb//x52//8edv//Hnb//x52//8ed///Hnf//x53//8ed///Hnb//x52
        //8edv//Hnb//x52//8edv//Hnb//x52//8edv//Hnb//x52//8edf//HnX//x51//8edf//HnX//x50
        //8edP//HnT//x50//8edP//HnP//x5z//8ec///H3L//x9y//8fcv//H3L//x9x//8fcf//H3D//x9w
        //8fcP//H2///x9v//8fbv//H27//x9t//8fbf//H2z//x9s//8fbP//H2v//x9q//8fav//H2n//x9p
        //8faP//H2f//x9n//8fZv//H2b//x9l//8fZP//H2T//x9j//8fY///H2P//x9j//8fY///H2P//x9j
        //8fY///H2P//x9j//8fY///H2P/wf///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8AISAivxIRE/8SERP/ERET/xERE/8RERP/ERET/xERE/8RERP/ERAT/xEQE/8REBL/ERAS/xEQ
        Ev8QEBL/EBAS/xAQEv8QEBL/EBAS/xAPEf8QDxH/EA8R/xAPEf8PDxH/Dw8R/w8PEf8PDhD/Dw4Q/w8O
        EP8PDhD/Dg4Q/w4OEP8ODg//Dg0P/w4ND/8ODQ//DQ0P/w0ND/8NDQ7/DQ0O/w0MDv8NDA7/DAwO0///
        /wD///8A////AP///wD///8AHnP/wR5z//8ec///HnP//x50//8edP//HnT//x50//8edf//HnX//x51
        //8edf//Hnb//x52//8edv//Hnb//x52//8edv//Hnb//x53//8ed///Hnf//x53//8ed///Hnf//x53
        //8ed///Hnf//x53//8ed///Hnf//x53//8ed///Hnf//x53//8ed///Hnf//x53//8edv//Hnb//x52
        //8edv//Hnb//x52//8edf//HnX//x51//8edf//HnX//x50//8edP//HnT//x50//8ec///HnP//x5z
        //8fcv//H3L//x9y//8fcf//H3H//x9x//8fcP//H3D//x9v//8fb///H27//x9u//8fbf//H23//x9t
        //8fbP//H2z//x9r//8fav//H2r//x9p//8faf//H2j//x9n//8fZ///H2b//x9m//8fZf//H2T//x9k
        //8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P/wf///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8AEhETzxIRE/8SERP/EhET/xIRE/8SERP/ERET/xER
        E/8RERP/ERET/xERE/8RERP/ERAT/xEQEv8REBL/ERAS/xAQEv8QEBL/EBAS/xAQEv8QEBL/EA8R/xAP
        Ef8QDxH/Dw8R/w8PEf8PDxH/Dw8Q/w8OEP8PDhD/Dw4Q/w4OEP8ODhD/Dg4Q/w4ND/8ODQ//Dg0P/w4N
        D/8NDQ//DQ0O/w0NDv8NDA7/DQwO0////wD///8A////AP///wD///8AHnP/wR5z//8edP//HnT//x50
        //8edP//HnX//x51//8edf//HnX//x52//8edv//Hnb//x52//8edv//Hnf//x53//8ed///Hnf//x53
        //8ed///Hnf//x53//8ed///Hnf//x53//8eeP//Hnj//x54//8eeP//Hnf//x53//8ed///Hnf//x53
        //8ed///Hnf//x53//8ed///Hnf//x53//8edv//Hnb//x52//8edv//Hnb//x52//8edf//HnX//x51
        //8edf//HnT//x50//8edP//HnP//x5z//8ec///H3P//x9y//8fcv//H3L//x9x//8fcf//H3D//x9w
        //8fb///H2///x9u//8fbv//H23//x9t//8fbf//H2z//x9s//8fa///H2r//x9q//8faf//H2n//x9o
        //8fZ///H2f//x9m//8fZv//H2X//x9k//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j
        //8fY///H2P/wf///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AEhIUzRIS
        FP8SERT/EhEU/xIRE/8SERP/EhET/xIRE/8SERP/ERET/xERE/8RERP/ERET/xERE/8REBP/ERAS/xEQ
        Ev8REBL/EBAS/xAQEv8QEBL/EBAS/xAPEf8QDxH/EA8R/xAPEf8PDxH/Dw8R/w8PEP8PDhD/Dw4Q/w8O
        EP8ODhD/Dg4Q/w4OEP8ODg//Dg0P/w4ND/8ODQ//DQ0P/w0NDv8NDQ7/DQwO0////wD///8A////AP//
        /wD///8AHnT/wR50//8edP//HnT//x51//8edf//HnX//x52//8edv//Hnb//x52//8edv//Hnf//x53
        //8ed///Hnf//x53//8ed///Hnf//x54//8eeP//Hnj//x54//8eeP//Hnj//x54//8eeP//Hnj//x54
        //8eeP//Hnj//x54//8eeP//Hnj//x54//8eeP//Hnj//x53//8ed///Hnf//x53//8ed///Hnf//x53
        //8edv//Hnb//x52//8edv//Hnb//x51//8edf//HnX//x51//8edP//HnT//x50//8ec///HnP//x5z
        //8fcv//H3L//x9y//8fcf//H3H//x9w//8fcP//H2///x9v//8fbv//H27//x9u//8fbf//H23//x9s
        //8fbP//H2v//x9q//8fav//H2n//x9p//8faP//H2f//x9n//8fZv//H2X//x9l//8fZP//H2P//x9j
        //8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P/wf///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8Al5eYSBISFP8SEhT/EhIU/xISFP8SERT/EhEU/xIRE/8SERP/EhET/xIR
        E/8RERP/ERET/xERE/8RERP/ERET/xEQE/8REBL/ERAS/xEQEv8QEBL/EBAS/xAQEv8QEBL/EA8R/xAP
        Ef8QDxH/Dw8R/w8PEf8PDxH/Dw4Q/w8OEP8PDhD/Dw4Q/w4OEP8ODhD/Dg4P/w4ND/8ODQ//Dg0P/w0N
        D/8NDQ7/DQ0O0////wD///8A////AP///wD///8AHnT/wR50//8edf//HnX//x51//8edv//Hnb//x52
        //8edv//Hnf//x53//8ed///Hnf//x53//8ed///Hnj//x54//8eeP//Hnj//x54//8eeP//Hnj//x54
        //8eeP//Hnj//x54//8eeP//Hnn//x55//8eeP//Hnj//x54//8eeP//Hnj//x54//8eeP//Hnj//x54
        //8eeP//Hnj//x54//8ed///Hnf//x53//8ed///Hnf//x53//8edv//Hnb//x52//8edv//HnX//x51
        //8edf//HnT//x50//8edP//HnP//x5z//8ec///H3L//x9y//8fcv//H3H//x9x//8fcP//H3D//x9v
        //8fb///H27//x9u//8fbf//H23//x9s//8fbP//H2v//x9r//8fav//H2r//x9p//8faP//H2j//x9n
        //8fZv//H2b//x9l//8fZf//H2T//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P/wf//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8Azs7OEhMSFM0SEhT/EhIU/xIS
        FP8SEhT/EhIU/xISFP8SERT/EhEU/xIRE/8SERP/EhET/xERE/8RERP/ERET/xERE/8RERP/ERAT/xEQ
        Ev8REBL/EBAS/xAQEv8QEBL/EBAS/xAPEf8QDxH/EA8R/w8PEf8PDxH/Dw8R/w8PEP8PDhD/Dw4Q/w8O
        EP8ODhD/Dg4Q/w4OD/8ODQ//Dg0P/w4ND/8NDQ//DQ0O0////wD///8A////AP///wD///8Aj7r/UB51
        //8edf//HnX//x52//8edv//Hnb//x53//8ed///Hnf//x53//8ed///Hnj//x54//8eeP//Hnj//x54
        //8eeP//Hnj//x55//8eef//Hnn//x55//8eef//Hnn//x55//8eef//Hnn//x55//8eef//Hnn//x55
        //8eef//Hnn//x55//8eef//Hnn//x54//8eeP//Hnj//x54//8eeP//Hnj//x54//8ed///Hnf//x53
        //8ed///Hnf//x52//8edv//Hnb//x52//8edf//HnX//x51//8edP//HnT//x50//8ec///HnP//x9z
        //8fcv//H3L//x9x//8fcf//H3D//x9w//8fb///H2///x9u//8fbv//H23//x9t//8fbP//H2z//x9r
        //8fa///H2r//x9q//8faf//H2j//x9o//8fZ///H2b//x9m//8fZf//H2T//x9k//8fY///H2P//x9j
        //8fY///H2P//x9j//8fY///H2P/wf///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////ABMSFc0TEhT/ExIU/xMSFP8SEhT/EhIU/xISFP8SEhT/EhIU/xISFP8SERT/EhET/xIR
        E/8SERP/ERET/xERE/8RERP/ERET/xEQE/8REBL/ERAS/xEQEv8QEBL/EBAS/xAQEv8QDxH/EA8R/xAP
        Ef8QDxH/Dw8R/w8PEf8PDxD/Dw4Q/w8OEP8PDhD/Dg4Q/w4OEP8ODg//Dg0P/w4ND/8ODQ/TtbW2Kv//
        /wD///8A////AP///wD///8A3er/Ah51/8Eedv//Hnb//x52//8ed///Hnf//x53//8ed///Hnf//x54
        //8eeP//Hnj//x54//8eeP//Hnj//x55//8eef//Hnn//x55//8eef//Hnn//x55//8eef//Hnn//x55
        //8eef//Hnr//x56//8eef//Hnn//x55//8eef//Hnn//x55//8eef//Hnn//x55//8eef//Hnn//x55
        //8eeP//Hnj//x54//8eeP//Hnj//x53//8ed///Hnf//x53//8ed///Hnb//x52//8edv//HnX//x51
        //8edf//HnT//x50//8edP//HnP//x5z//8ec///H3L//x9y//8fcf//H3H//x9w//8fcP//H2///x9v
        //8fbv//H27//x9t//8fbf//H2z//x9s//8fa///H2v//x9q//8faf//H2n//x9o//8fZ///H2f//x9m
        //8fZv//H2X//x9k//8fY///H2P//x9j//8fY///H2P//x9j//8fY//B0N//EP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////ACIhJL8TEhXNExIV/xMSFf8TEhT/ExIU/xMS
        FP8SEhT/EhIU/xISFP8SEhT/EhIU/xIRFP8SERP/EhET/xIRE/8RERP/ERET/xERE/8RERP/ERAT/xEQ
        Ev8REBL/EBAS/xAQEv8QEBL/EBAS/xAPEf8QDxH/EA8R/w8PEf8PDxH/Dw8Q/w8OEP8PDhD/Dw4Q/w4O
        EP8ODhD/Dg4P/w4ND/8ODQ/T////AP///wD///8A////AP///wD///8A////AEWO/5sedv/BHnb//x53
        //8ed///Hnf//x53//8eeP//Hnj//x54//8eeP//Hnj//x55//8eef//Hnn//x55//8eef//Hnn//x55
        //8eev//Hnr//x56//8eev//Hnr//x56//8eev//Hnr//x56//8eev//Hnr//x56//8eev//Hnr//x56
        //8eev//Hnr//x55//8eef//Hnn//x55/8Eeef/BHnn/wR55/8EeeP/BHnj/wR54/8EeeP/BHnf/wR53
        /8Eed//BHnf/wR53/8Eedv/BHnb/wR52/8Eedf/BHnX//x51//8edP//HnT//x5z//8ec///HnP//x9y
        //8fcv//H3H//x9x//8fcP//H3D//x9v//8fb///H27//x9u//8fbf//H23//x9s//8fbP//H2v//x9q
        //8fav//H2n//x9p//8faP//H2f//x9m//8fZv//H2X//x9l//8fZP//H2P//x9j//8fY///H2P//x9j
        /8EfY//B////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wATExXNExIV/xMSFf8TEhX/ExIV/xMSFP8TEhT/ExIU/xISFP8SEhT/EhIU/xISFP8SEhT/EhEU/xIR
        E/8SERP/EhET/xERE/8RERP/ERET/xEQE/8REBL/ERAS/xEQEv8QEBL/EBAS/xAQEv8QDxH/EA8R/xAP
        Ef8PDxH/Dw8R/w8PEP8PDhD/Dw4Q/w8OEP8ODhD/Dg4Q/w4OD9E4Nzip////AP///wD///8A////AP//
        /wD///8A////AP///wAed//BHnf//x53//8ed///Hnj//x54//8eeP//Hnj//x55//8eef//Hnn//x55
        //8eef//Hnn//x56//8eev//Hnr//x56//8eev//Hnr//x56//8eev//Hnr//x56//8eev//Hnr//x56
        //8eev//Hnr//x56//8eev//Hnr//x56//8eev//Hnr//x56//8eev//Hnr/wXeu/2j///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wDQ4/8QHnX/wR51
        //8edf//HnT//x50//8ec///HnP//x5z//8fcv//H3L//x9x//8fcf//H3D//x9w//8fb///H2///x9u
        //8fbv//H23//x9t//8fbP//H2z//x9r//8fav//H2r//x9p//8faP//H2j//x9n//8fZv//H2b//x9l
        //8fZP//H2T//x9j//8fY///H2P//x9j/8H///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wBwcHJwExMVzRMTFf8TExX/ExIV/xMSFf8TEhX/ExIV/xMS
        FP8TEhT/EhIU/xISFP8SEhT/EhIU/xIRFP8SERP/EhET/xIRE/8RERP/ERET/xERE/8RERP/ERAS/xEQ
        Ev8REBL/EBAS/xAQEv8QEBL/EA8R/xAPEf8QDxH/Dw8R/w8PEf8PDxD/Dw4Q/w8OEP8PDhD/Dg4Q0Q4O
        ENH///8A////AP///wD///8A////AP///wD///8A////AP///wDD2/8cHnf/wR54//8eeP//Hnj//x54
        //8eef//Hnn//x55//8eef//Hnn//x56//8eev//Hnr//x56//8eev//Hnr//x56//8eev//Hnv//x57
        //8ee///Hnv//x57//8ee///Hnv//x57//8ee///Hnv//x57//8ee///Hnv//x57//8eev//Hnr//x56
        //8eev/BLIL/s////wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8Ad6z/aB51/8Eedf//HnX//x50//8edP//HnP//x5z//8ec///H3L//x9y
        //8fcf//H3H//x9w//8fcP//H2///x9v//8fbv//H27//x9t//8fbf//H2z//x9r//8fa///H2r//x9q
        //8faf//H2j//x9n//8fZ///H2b//x9l//8fZf//H2T//x9j//8fY///H2P/wZ26/0L///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AIyIkvRMT
        Fc0TExX/ExMV/xMTFf8TEhX/ExIV/xMSFf8TEhX/ExIU/xMSFP8SEhT/EhIU/xISFP8SEhT/EhEU/xIR
        E/8SERP/EhET/xERE/8RERP/ERET/xEQE/8REBL/ERAS/xAQEv8QEBL/EBAS/xAPEf8QDxH/EA8R/w8P
        Ef8PDxH/Dw8Q/w8OEP8PDhD/Dw4Q0f///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8Aosf/Ph54/8EeeP//Hnj//x55//8eef//Hnn//x55//8eev//Hnr//x56//8eev//Hnr//x56
        //8ee///Hnv//x57//8ee///Hnv//x57//8ee///Hnv//x57//8ee///Hnv//x57//8ee///Hnv//x57
        //8ee///Hnv//x57//8ee///Hnv//x57/8Esg/+z////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AHes/2gedf/BHnX//x51
        //8edP//HnT//x5z//8ec///HnP//x9y//8fcv//H3H//x9x//8fcP//H3D//x9v//8fb///H27//x9u
        //8fbf//H2z//x9s//8fa///H2v//x9q//8faf//H2n//x9o//8fZ///H2b//x9m//8fZf//H2T//x9k
        //8fY//BeKH/aP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////ACMiJb0UExXNFBMV/xMTFf8TExX/ExMV/xMSFf8TEhX/ExIV/xMS
        Ff8TEhT/ExIU/xISFP8SEhT/EhIU/xIRFP8SERP/EhET/xIRE/8RERP/ERET/xERE/8REBP/ERAS/xEQ
        Ev8QEBL/EBAS/xAQEv8QDxH/EA8R/xAPEf8PDxH/Dw8R/w8PEP8PDhDRzc3NEv///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AKLH/z4eef/BHnn//x55//8eef//Hnr//x56
        //8eev//Hnr//x56//8ee///Hnv//x57//8ee///Hnv//x57//8ee///Hnv//x57//8ee///Hnv//x57
        //8efP//Hnz//x58//8efP//Hnz//x57//8ee///Hnv//x57//8ee///Hnv/wSyD/7P///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wB3rP9oHnb/wR51//8edf//HnT//x50//8ec///HnP//x5z//8fcv//H3L//x9x
        //8fcf//H3D//x9w//8fb///H2///x9u//8fbf//H23//x9s//8fbP//H2v//x9q//8fav//H2n//x9o
        //8faP//H2f//x9m//8fZv//H2X//x9k/8F4of9o////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wAjIiW9FBMWzRQT
        Fv8UExX/ExMV/xMTFf8TExX/ExIV/xMSFf8TEhX/ExIU/xMSFP8SEhT/EhIU/xISFP8SEhT/EhEU/xIR
        E/8SERP/ERET/xERE/8RERP/ERAT/xEQEv8REBL/EBAS/xAQEv8QEBL/EA8R/xAPEf8QDxH/Dw8R/w8P
        EdHNzc0S////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wCiyP8+Hnn/wR56//8eev//Hnr//x56//8eev//Hnv//x57//8ee///Hnv//x57//8ee///Hnv//x57
        //8efP//Hnz//x58//8efP//Hnz//x58//8efP//Hnz//x58//8efP//Hnz//x58//8efP//Hnz//x58
        //8efP/BLIP/s////wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8Ad6z/aB52/8Eedf//HnX//x50
        //8edP//HnP//x5z//8ec///H3L//x9y//8fcf//H3H//x9w//8fcP//H2///x9u//8fbv//H23//x9t
        //8fbP//H2z//x9r//8fav//H2r//x9p//8faP//H2f//x9n//8fZv//H2X/wXii/2j///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8AIyIlvRQTFs0UExb/FBMW/xQTFf8TExX/ExMV/xMTFf8TEhX/ExIV/xMS
        Ff8TEhT/ExIU/xISFP8SEhT/EhIU/xIRFP8SERP/EhET/xERE/8RERP/ERET/xEQE/8REBL/ERAS/xAQ
        Ev8QEBL/EBAS/xAPEf8QDxH/EA8R0c3NzhL///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8Aosj/Ph56/8Eeev//Hnr//x57//8ee///Hnv//x57
        //8ee///Hnv//x58//8efP//Hnz//x58//8efP//Hnz//x58//8efP//Hnz//x58//8efP//Hnz//x58
        //8efP//Hnz//x58//8efP//Hnz//x58/8EshP+z////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AHes/2gedv/BHnX//x51//8edP//HnT//x5z//8ec///H3L//x9y//8fcv//H3H//x9w
        //8fcP//H2///x9v//8fbv//H27//x9t//8fbP//H2z//x9r//8fav//H2r//x9p//8faf//H2j//x9n
        //8fZv/BeKP/aP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////ACMiJb0UExbNFBMW/xQT
        Fv8UExb/FBMV/xMTFf8TExX/ExMV/xMSFf8TEhX/ExIU/xMSFP8SEhT/EhIU/xISFP8SERT/EhET/xIR
        E/8RERP/ERET/xERE/8REBP/ERAS/xEQEv8QEBL/EBAS/xAQEv8QDxHRzc3OEv///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AKLI
        /z4ee//BHnv//x57//8ee///Hnv//x57//8efP//Hnz//x58//8efP//Hnz//x58//8efP//Hnz//x58
        //8eff//Hn3//x59//8eff//Hn3//x59//8eff//Hn3//x59//8eff//Hn3/wSyE/7P///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wB3rP9oHnX/wR51//8edf//HnT//x50
        //8ec///HnP//x9y//8fcv//H3H//x9x//8fcP//H3D//x9v//8fb///H27//x9t//8fbf//H2z//x9s
        //8fa///H2r//x9q//8faf//H2j//x9n/8F4o/9o////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wAjIyW9FBMWzRQTFv8UExb/FBMW/xQTFf8TExX/ExMV/xMTFf8TEhX/ExIV/xMS
        Ff8TEhT/EhIU/xISFP8SEhT/EhIU/xIRE/8SERP/EhET/xERE/8RERP/ERAT/xEQEv8REBL/EBAS/xAQ
        Es/Nzc4S////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wCiyf8+Hnv/wR57//8ee///Hnz//x58//8efP//Hnz//x58
        //8efP//Hnz//x59//8eff//Hn3//x59//8eff//Hn3//x59//8eff//Hn3//x59//8eff//Hn3//x59
        //8eff/BLIX/s////wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8Ad6z/aB51/8Eedf//HnX//x50//8edP//HnP//x5z//8fcv//H3L//x9x//8fcf//H3D//x9w
        //8fb///H27//x9u//8fbf//H23//x9s//8fa///H2v//x9q//8faf//H2n/wXik/2j///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AIyMlvRQUFssUExb/FBMW/xQT
        Fv8UExb/FBMV/xMTFf8TExX/ExIV/xMSFf8TEhX/ExIU/xMSFP8SEhT/EhIU/xISFP8SERP/EhET/xIR
        E/8RERP/ERET/xEQE/8REBL/ERASz83NzhL///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8Aosn/Ph58
        /8EefP//Hnz//x58//8efP//Hnz//x59//8eff//Hn3//x59//8eff//Hn3//x59//8eff//Hn3//x59
        //8eff//Hn3//x59//8eff//Hn3//x59/8Eshf+z////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AHes/2gedf/BHnX//x50//8edP//HnT//x5z
        //8ec///H3L//x9y//8fcf//H3H//x9w//8fb///H2///x9u//8fbv//H23//x9s//8fbP//H2v//x9q
        //8fav/BeKT/aP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////ACMjJb0UFBbLFBQW/xQTFv8UExb/FBMW/xQTFf8TExX/ExMV/xMTFf8TEhX/ExIV/xMS
        FP8TEhT/EhIU/xISFP8SEhT/EhET/xIRE/8SERP/ERET/xERE/8REBPPzs3OEv///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AKLJ/z4efP/BHnz//x58//8eff//Hn3//x59//8eff//Hn3//x59
        //8eff//Hn3//x5+//8efv//Hn7//x5+//8efv//Hn7//x5+//8efv//Hn7/wSyG/7P///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wB3rP9oHnX/wR51//8edP//HnT//x5z//8ec///H3L//x9y//8fcf//H3H//x9w//8fcP//H2///x9u
        //8fbv//H23//x9t//8fbP//H2v//x9r/8F4pf9o////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wAjIyW9FBQWyxQUFv8UExb/FBMW/xQT
        Fv8UExb/FBMV/xMTFf8TExX/ExIV/xMSFf8TEhT/ExIU/xISFP8SEhT/EhIU/xIRE/8SERP/EhET/xER
        E8/Ozs4S////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wCiyf8+Hn3/wR59
        //8eff//Hn3//x59//8eff//Hn3//x5+//8efv//Hn7//x5+//8efv//Hn7//x5+//8efv//Hn7//x5+
        //8efv/BLIb/s////wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8Ad6z/aB51/8Eedf//HnT//x50//8ec///HnP//x9y
        //8fcv//H3H//x9x//8fcP//H2///x9v//8fbv//H27//x9t//8fbP//H2z/wXim/2j///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8AJCMmvRQUFssUFBb/FBQW/xQTFv8UExb/FBMW/xQTFf8TExX/ExMV/xMSFf8TEhX/ExIU/xMS
        FP8SEhT/EhIU/xISFP8SERP/EhETz87OzhL///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8Aosn/Ph59/8Eeff//Hn3//x5+//8efv//Hn7//x5+//8efv//Hn7//x5+
        //8efv//Hn7//x5+//8efv//Hn7//x5+/8Eshv+z////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AHes
        /2gedf/BHnX//x50//8edP//HnP//x5z//8fcv//H3L//x9x//8fcP//H3D//x9v//8fb///H27//x9t
        //8fbf/BeKb/aP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////ACQjJr0UFBbLFBQW/xQUFv8UExb/FBMW/xQT
        Fv8UExX/ExMV/xMTFf8TEhX/ExIV/xMSFP8TEhT/EhIU/xISFM0SEhTNzs7OEv///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AMPc/xwefv/BHn7//x5+
        //8efv//Hn7//x5+//8efv//Hn7//x5+//8efv//Hn7//x5///8ef///Hn//wUWU/5v///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wCcwv9EHnX/wR50//8edP//HnP//x5z//8fcv//H3L//x9x
        //8fcf//H3D//x9v//8fb///H27//x9u/8G60v8m////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wA9PT+jFRQXyxQUFv8UFBb/FBMW/xQTFv8UExb/FBMV/xMTFf8TExX/ExIV/xMSFf8TEhT/ExIU/xIS
        FM3///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wC01P8sHn7//x5+//8efv//Hn7//x5+//8efv//Hn///x5///8ef///Hn///x5/
        //8ef///Hn//wf///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A0NzwEB1v
        8cMedP//HnT//x5z//8ec///H3L//x9y//8fcf//H3D//x9w//8fb///H2nx/xs9fsX///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8AFRQXyxUUF/8UFBb/FBQW/xQTFv8UExb/FBMW/xQT
        Ff8TExX/ExMV/xMSFf8TEhX/ExIUzd/e3wL///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wDD3f8cHn7//x5+//8efv//Hn///x5/
        //8ef///Hn///x5///8ef///Hn///x5///8ef///Hn//wf///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////ABYbJskaP33/HWLU/x5z//8ec///H3L//x9y//8fcf//H3H//x1a
        xv8aOG//GBwn/xcWGcn///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AFRQXyxUU
        F/8VFBf/FBQW/xQUFv8UExb/FBMW/xQTFv8UExX/ExMV/xMTFf8TEhX/ExIVzf///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wDD3f8cHn7//x5///8ef///Hn///x5///8ef///Hn///x5///8ef///Hn///x5///8ef///Hn//wf//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////ABYVGMsWFRj/FhUY/xYV
        GP8YLVL/GC1S/xktU/8ZJ0T/FxYZ/xcWGf8XFhn/FxYZ/xcWGcn///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8AFRQXyxUUF/8VFBf/FRQX/xQUFv8UFBb/FBMW/xQTFv8UExb/FBMV/xMT
        Ff8TExX/ExIVzf///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wDD3f8cHn///x5///8ef///Hn///x5///8ef///Hn///x5/
        //8ef///HoD//x6A//8egP//HoD/wf///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////ABYVGMsWFRj/FhYY/xYWGP8WFhn/FxYZ/xcWGf8XFhn/FxYZ/xcWGf8XFhn/FxYZ/xcX
        Gsn///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AFRUXyxUVF/8VFBf/FRQX/xUU
        F/8UFBb/FBQW/xQTFv8UExb/FBMW/xQTFf8TExX/ExMVzf///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wDD3f8cHn///x5/
        //8ef///Hn///x5///8ef///HoD//x6A//8egP//HoD//x6A//8egP//HoD/wf///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////ABYVGMsWFhj/FhYY/xcWGf8XFhn/FxYZ/xcW
        Gf8XFhn/FxYZ/xcXGv8XFxr/Fxca/xgXGsn///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8AFRUXyxUVF/8VFRf/FRQX/xUUF/8VFBf/FBQW/xQUFv8UExb/FBMW/xQTFv8UExX/ExMVzf//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wDD3f8cHn///x5///8ef///HoD//x6A//8egP//HoD//x6A//8egP//HoD//x6A
        //8egP//HoD/wf///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////ABYW
        GMkWFhn/FxYZ/xcWGf8XFhn/FxYZ/xcWGf8XFhn/Fxca/xcXGv8YFxr/GBca/xgXGsn///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8AFhUYyxUVF/8VFRf/FRUX/xUUF/8VFBf/FRQX/xQU
        Fv8UFBb/FBMW/xQTFv8UExb/ExMVzf///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wDD3f8cHn///x6A//8egP//HoD//x6A
        //8egP//HoD//x6A//8egP//HoD//x6A//8egP//HoD/wf///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////ABYWGckXFhn/FxYZ/xcWGf8XFhn/FxYZ/xcXGv8XFxr/GBca/xgX
        Gv8YFxr/GBca/xgXGsn///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AFhUYyxYV
        GP8VFRf/FRUX/xUVF/8VFBf/FRQX/xUUF/8UFBb/FBQW/xQTFv8UExb/FBMWzf///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wDD3f8cHoD//x6A//8egP//HoD//x6A//8egP//HoD//x6A//8egP//HoD//x6A//8egP//HoD/wf//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////ABcWGckXFhn/FxYZ/xcW
        Gf8XFxr/Fxca/xgXGv8YFxr/GBca/xgXGv8YFxr/GBca/xgXGsn///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8AFhUYyxYVGP8WFRj/FRUX/xUVF/8VFRf/FRQX/xUUF/8VFBf/FBQW/xQU
        Fv8UExb/FBMWzf///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wDD3f8cHoD//x6A//8egP//HoD//x6A//8egP//HoD//x6A
        //8egP//HoH//x6B//8egf//HoH/wf///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////ABcWGckXFhn/FxYZ/xcXGv8XFxr/GBca/xgXGv8YFxr/GBca/xgXGv8YFxr/GBgb/xgY
        G8f///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AFhYYyRYVGP8WFRj/FhUY/xUV
        F/8VFRf/FRQX/xUUF/8VFBf/FRQX/xQUFv8UExb/FBMWzf///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wDD3f8cHoD//x6A
        //8egP//HoD//x6A//8egP//HoH//x6B//8egf//HoH//x6B//8egf//HoH/wf///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////ABcWGckXFhn/Fxca/xcXGv8YFxr/GBca/xgX
        Gv8YFxr/GBca/xgYG/8YGBv/GRgb/xkYG8f///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8AFhYYyRYWGP8WFRj/FhUY/xYVGP8VFRf/FRUX/xUUF/8VFBf/FRQX/xQUFv8UFBb/FBMWzf//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wDD3f8cHoD//x6A//8egP//HoD//x6B//8egf//HoH//x6B//8egf//HoH//x6B
        //8egf//HoH/wf///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////ABcW
        GckXFxr/GBca/xgXGv8YFxr/GBca/xgXGv8YGBv/GBgb/xkYG/8ZGBv/GRgb/xkYG8f///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8AFxYZyRYWGP8WFhj/FhUY/xYVGP8WFRj/FRUX/xUV
        F/8VFBf/FRQX/xUUF/8UFBb/FBQWy////wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wDD3f8cHoD//x6A//8egP//HoH//x6B
        //8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoH/wf///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////ABcXGskYFxr/GBca/xgXGv8YFxr/GBca/xgYG/8YGBv/GRgb/xkY
        G/8ZGBv/GRgb/xkYG8f///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AFxYZyRcW
        Gf8WFhj/FhYY/xYVGP8WFRj/FhUY/xUVF/8VFRf/FRQX/xUUF/8VFBf/FBQWy////wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wC01f8sHoD//x6A//8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoH/wf//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8Az8/PEBgXGskYFxr/GBca/xgX
        Gv8YFxr/GBgb/xkYG/8ZGBv/GRgb/xkYG/8ZGBv/GRgc/xkZHMf///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8AFxYZyRcWGf8WFhn/FhYY/xYVGP8WFRj/FhUY/xUVF/8VFRf/FRQX/xUU
        F/8VFBf/FBQWy////wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////ANHl/w4egP/BHoD//x6B//8egf//HoH//x6B//8egf//HoH//x6B
        //8egf//HoH//x6B//8egf//HoH/wUWX/5v///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wCZmZpGGBcayRgXGv8YFxr/GBca/xgXGv8YGBv/GRgb/xkYG/8ZGBv/GRgb/xkYHP8ZGRz/GRkc/xoZ
        HMe5ubom////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wA/PkGhFxYZyRcWGf8XFhn/FhYZ/xYW
        GP8WFRj/FhUY/xYVGP8VFRf/FRUX/xUUF/8VFBf/FRQXyxQUFsv///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8Aosv/Ph6A/8EegP//HoH//x6B
        //8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6B/8Esif+z////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AHNzdWwXFxrJGBca/xgXGv8YFxr/GBca/xgYG/8ZGBv/GRgb/xkY
        G/8ZGBv/GRgc/xkZHP8ZGRz/Ghkc/xoZHP8aGRzHdXR2bP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////ACYm
        KLkXFxrJFxYZ/xcWGf8XFhn/FxYZ/xYWGP8WFhj/FhUY/xYVGP8WFRj/FRUX/xUVF/8VFBf/FRQX/xQU
        FssUFBbLzs7PEv///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wCiy/8+HoD/wR6A//8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6C
        //8egv//HoL//x6C//8egv/BLIr/s////wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8Ac3N1bBcXGskYFxr/GBca/xgX
        Gv8YFxr/GBgb/xkYG/8ZGBv/GRgb/xkYG/8ZGBz/GRkc/xoZHP8aGRz/Ghkc/xoZHP8aGR3/Ghkdx3V1
        d2r///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8AJiYouRgXGskYFxr/Fxca/xcWGf8XFhn/FxYZ/xcWGf8WFhj/FhUY/xYV
        GP8WFRj/FRUX/xUVF/8VFBf/FRQX/xUUF/8UFBb/FBQWy87OzxL///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AKLL/z4egP/BHoD//x6B//8egf//HoH//x6B//8egf//HoH//x6B
        //8egf//HoH//x6B//8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL/wSyK/7P///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wBzcnRuFxcayRgXGv8YFxr/GBca/xgXGv8YGBv/GRgb/xkYG/8ZGBv/GRgb/xkZHP8ZGRz/Ghkc/xoZ
        HP8aGRz/Ghkd/xoZHf8aGh3/Ghod/xsaHcV1dXdq////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wAmJii5GBcayRgXGv8YFxr/Fxca/xcX
        Gv8XFhn/FxYZ/xcWGf8WFhn/FhYY/xYVGP8WFRj/FhUY/xUVF/8VFRf/FRQX/xUUF/8UFBb/FBQW/xQT
        Fs3Ozs8S////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8Aosv/Ph6A/8EegP//HoD//x6B
        //8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C
        //8egv//HoL//x6C/8Esiv+z////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AHNydG4XFxrJGBca/xgXGv8YFxr/GBca/xgYG/8ZGBv/GRgb/xkY
        G/8ZGBv/GRkc/xkZHP8aGRz/Ghkc/xoZHP8aGR3/Ghkd/xoaHf8bGh3/Gxod/xsaHf8bGh3FdXV3av//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////ACcm
        KbkYGBvHGBca/xgXGv8YFxr/GBca/xcXGv8XFhn/FxYZ/xcWGf8XFhn/FhYY/xYWGP8WFRj/FhUY/xUV
        F/8VFRf/FRQX/xUUF/8VFBf/FBQW/xQUFv8UExbNzs7PEv///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wCiy/8+HoD/wR6A//8egP//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6B//8egv//HoL//x6C
        //8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C//8egv/BLIr/s////wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8Ac3J0bhcXGskXFxr/GBca/xgX
        Gv8YFxr/GBgb/xgYG/8ZGBv/GRgb/xkYG/8ZGRz/GRkc/xoZHP8aGRz/Ghkc/xoZHf8aGh3/Ghod/xsa
        Hf8bGh3/Gxod/xsaHv8bGh7/GxoexXV1d2r///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8AJyYpuRkYG8cZGBv/GBgb/xgXGv8YFxr/GBca/xgXGv8XFxr/FxYZ/xcW
        Gf8XFhn/FhYZ/xYWGP8WFRj/FhUY/xYVGP8VFRf/FRUX/xUUF/8VFBf/FBQW/xQUFv8UExb/FBMWzc7O
        zhL///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AKLL/z4egP/BHoD//x6A//8egP//HoH//x6B//8egf//HoH//x6B
        //8egf//HoH//x6C//8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C
        //8egv//HoL/wSyK/7P///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wBzcnRuFxYZyRcXGv8YFxr/GBca/xgXGv8YFxr/GBgb/xkYG/8ZGBv/GRgb/xkYHP8ZGRz/Ghkc/xoZ
        HP8aGRz/Ghkd/xoaHf8aGh3/Gxod/xsaHf8bGh7/Gxoe/xsaHv8bGx7/Gxse/xsbHsV2dXdq////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wAnJyq5GRgbxxkYG/8ZGBv/GBgb/xgY
        G/8YFxr/GBca/xgXGv8XFxr/FxYZ/xcWGf8XFhn/FxYZ/xYWGP8WFRj/FhUY/xYVGP8VFRf/FRUX/xUU
        F/8VFBf/FRQX/xQUFv8UExb/FBMW/xQTFc3Ozs4S////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8Aosv/Ph6A/8EegP//HoD//x6A
        //8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6C//8egv//HoL//x6C//8egv//HoL//x6C
        //8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C/8Esiv+z////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AHNydG4XFhnJFxca/xcXGv8YFxr/GBca/xgXGv8YGBv/GRgb/xkY
        G/8ZGBv/GRgc/xkZHP8aGRz/Ghkc/xoZHP8aGR3/Ghod/xoaHf8bGh3/Gxod/xsaHv8bGh7/Gxse/xsb
        Hv8cGx7/HBse/xwbHv8cGx7FdnV4av///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////ACgn
        KrkZGRzHGRgc/xkYG/8ZGBv/GRgb/xgYG/8YFxr/GBca/xgXGv8YFxr/Fxca/xcWGf8XFhn/FxYZ/xYW
        Gf8WFhj/FhUY/xYVGP8WFRj/FRUX/xUUF/8VFBf/FRQX/xQUFv8UFBb/FBMW/xQTFv8TExXNzs7OEv//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wCiyv8+HoD/wR6A//8egP//HoD//x6A//8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoL//x6C
        //8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C
        //8egv/BLIr/s////wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8Ac3J0bhcWGckXFhn/Fxca/xgX
        Gv8YFxr/GBca/xgYG/8YGBv/GRgb/xkYG/8ZGBv/GRkc/xoZHP8aGRz/Ghkc/xoZHf8aGh3/Ghod/xsa
        Hf8bGh3/Gxoe/xsaHv8bGx7/Gxse/xwbHv8cGx7/HBse/xwbH/8cGx//HBsfxXZ1eGr///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8AKCcquRoZHMcaGRz/GRkc/xkYHP8ZGBv/GRgb/xkYG/8YGBv/GBca/xgX
        Gv8YFxr/Fxca/xcWGf8XFhn/FxYZ/xcWGf8WFhj/FhUY/xYVGP8WFRj/FRUX/xUVF/8VFBf/FRQX/xQU
        Fv8UFBb/FBMW/xQTFv8UExX/ExMVzc7OzhL///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AKLK/z4ef//BHoD//x6A//8egP//HoD//x6A//8egf//HoH//x6B
        //8egf//HoH//x6B//8egf//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C
        //8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL/wSyK/7P///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wBzcnRuFxYZyRcWGf8XFxr/Fxca/xgXGv8YFxr/GBca/xgYG/8ZGBv/GRgb/xkYG/8ZGBz/GRkc/xoZ
        HP8aGRz/Ghkd/xoaHf8aGh3/Gxod/xsaHf8bGh7/Gxoe/xsbHv8cGx7/HBse/xwbHv8cGx//HBsf/xwb
        H/8cGx//HBsf/xwcH8N3dnhq////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wAoKCu3GhkdxxoZHP8aGRz/GRkc/xkZ
        HP8ZGBv/GRgb/xkYG/8YGBv/GBca/xgXGv8YFxr/GBca/xcXGv8XFhn/FxYZ/xcWGf8WFhn/FhYY/xYV
        GP8WFRj/FRUX/xUVF/8VFBf/FRQX/xUUF/8UFBb/FBMW/xQTFv8UExX/ExMV/xMTFc3Ozs4S////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8Aosr/Ph5//8Eef///HoD//x6A
        //8egP//HoD//x6A//8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoL//x6C//8egv//HoL//x6C
        //8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C
        /8Esif+z////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AHJydG4XFhnJFxYZ/xcWGf8XFxr/GBca/xgXGv8YFxr/GBgb/xgY
        G/8ZGBv/GRgb/xkYHP8ZGRz/Ghkc/xoZHP8aGRz/Ghkd/xoaHf8bGh3/Gxod/xsaHv8bGh7/Gxse/xwb
        Hv8cGx7/HBse/xwbH/8cGx//HBsf/xwcH/8dHB//HRwf/x0cH/8dHB/Dd3Z4av///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////ACko
        K7caGh3FGhkd/xoZHf8aGRz/Ghkc/xkZHP8ZGBz/GRgb/xkYG/8ZGBv/GBgb/xgXGv8YFxr/GBca/xcX
        Gv8XFhn/FxYZ/xcWGf8WFhn/FhYY/xYVGP8WFRj/FhUY/xUVF/8VFBf/FRQX/xUUF/8UFBb/FBQW/xQT
        Fv8UExb/ExMV/xMTFf8TEhXNzs7OEv///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wDD3f8cHn//wR5///8ef///HoD//x6A//8egP//HoD//x6A//8egf//HoH//x6B//8egf//HoH//x6B
        //8egf//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C
        //8egv//HoL//x6C//8egv//HoL//x6C//8egv/BLIn/s////wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AcnJzbhcWGckXFhn/FxYZ/xcW
        Gf8XFxr/GBca/xgXGv8YFxr/GBgb/xkYG/8ZGBv/GRgb/xkZHP8ZGRz/Ghkc/xoZHP8aGR3/Ghod/xsa
        Hf8bGh3/Gxoe/xsaHv8bGx7/HBse/xwbHv8cGx7/HBsf/xwbH/8cGx//HBwf/x0cH/8dHB//HRwg/x0c
        IP8dHCD/HRwgw5ybnUT///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8AKSgstxsaHcUbGh3/Ghod/xoZHf8aGRz/Ghkc/xoZHP8ZGRz/GRgb/xkY
        G/8ZGBv/GBgb/xgXGv8YFxr/GBca/xcXGv8XFxr/FxYZ/xcWGf8XFhn/FhYY/xYVGP8WFRj/FhUY/xUV
        F/8VFRf/FRQX/xUUF/8UFBb/FBQW/xQTFv8UExb/FBMV/xMTFf8TEhX/ExIVzf///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wAef//BHn///x5///8ef///HoD//x6A//8egP//HoD//x6A
        //8egf//HoH//x6B//8egf//HoH//x6B//8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C
        //8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoH/wXez
        /2j///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8AFhYYyRcWGf8XFhn/FxYZ/xcXGv8YFxr/GBca/xgXGv8YFxr/GBgb/xkYG/8ZGBv/GRgb/xkZ
        HP8aGRz/Ghkc/xoZHf8aGR3/Ghod/xsaHf8bGh3/Gxoe/xsbHv8bGx7/HBse/xwbHv8cGx//HBsf/xwc
        H/8dHB//HRwf/x0cIP8dHCD/HRwg/x0cIP8dHCD/HRwg/x0cIMP///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wB1dXdqGxoexRsaHv8bGh3/Gxod/xoa
        Hf8aGR3/Ghkc/xoZHP8ZGRz/GRgc/xkYG/8ZGBv/GBgb/xgYG/8YFxr/GBca/xgXGv8XFxr/FxYZ/xcW
        Gf8XFhn/FhYZ/xYWGP8WFRj/FhUY/xUVF/8VFRf/FRQX/xUUF/8VFBf/FBQW/xQTFv8UExb/FBMV/xMT
        Ff8TEhX/ExIVzRMSFM3///8A////AP///wD///8A////AP///wD///8A////AEWU/5sef//BHn///x5/
        //8ef///HoD//x6A//8egP//HoD//x6A//8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoL//x6C
        //8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C
        //8egv//HoL//x6C//8egv//HoH//x6B/8H///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wAWFhjJFhYZyRcWGf8XFhn/FxYZ/xcXGv8YFxr/GBca/xgX
        Gv8YGBv/GRgb/xkYG/8ZGBv/GRgc/xkZHP8aGRz/Ghkc/xoZHf8aGh3/Gxod/xsaHf8bGh7/Gxoe/xsb
        Hv8cGx7/HBse/xwbH/8cGx//HBwf/x0cH/8dHB//HRwg/x0cIP8dHCD/HRwg/x0cIP8dHSD/HR0g/x4d
        IMMeHSDD////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wAbGx7FGxse/xsaHv8bGh3/Gxod/xoaHf8aGR3/Ghkc/xoZHP8ZGRz/GRkc/xkYG/8ZGBv/GRgb/xgY
        G/8YFxr/GBca/xgXGv8XFxr/FxYZ/xcWGf8XFhn/FhYZ/xYWGP8WFRj/FhUY/xYVGP8VFRf/FRQX/xUU
        F/8VFBf/FBQW/xQTFv8UExb/FBMV/xMTFf8TExX/ExIV/xMSFM07Oz2l////AP///wD///8A////AP//
        /wD///8A3ez/Ah5+/8Eef///Hn///x5///8ef///HoD//x6A//8egP//HoD//x6A//8egf//HoH//x6B
        //8egf//HoH//x6B//8egf//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C
        //8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoH//x6B/8Esif+z////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////ALi3uCgWFhjJFhYZ/xcW
        Gf8XFhn/FxYZ/xcXGv8YFxr/GBca/xgXGv8YGBv/GRgb/xkYG/8ZGBv/GRkc/xoZHP8aGRz/Ghkc/xoZ
        Hf8aGh3/Gxod/xsaHv8bGh7/Gxse/xwbHv8cGx7/HBsf/xwbH/8cGx//HRwf/x0cH/8dHCD/HRwg/x0c
        IP8dHCD/HR0g/x4dIP8eHSH/Hh0h/x4dIf8eHSHD0NDREP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////ACopLLccGx7FGxse/xsaHv8bGh7/Gxod/xsaHf8aGh3/Ghkd/xoZ
        HP8aGRz/GRkc/xkYG/8ZGBv/GRgb/xgYG/8YFxr/GBca/xgXGv8XFxr/FxYZ/xcWGf8XFhn/FxYZ/xYW
        GP8WFRj/FhUY/xYVGP8VFRf/FRQX/xUUF/8VFBf/FBQW/xQUFv8UExb/FBMW/xMTFf8TExX/ExIV/xMS
        FP8SEhTN////AP///wD///8A////AP///wD///8Aj7//UB5+//8ef///Hn///x5///8ef///HoD//x6A
        //8egP//HoD//x6A//8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoL//x6C//8egv//HoL//x6C
        //8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C
        //8egf//HoH//x6B//8egf/B////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////ABYVGMsWFhj/FxYZ/xcWGf8XFhn/FxYZ/xcXGv8YFxr/GBca/xgXGv8YGBv/GRgb/xkY
        G/8ZGBz/GRkc/xoZHP8aGRz/Ghkd/xoaHf8bGh3/Gxod/xsaHv8bGh7/Gxse/xwbHv8cGx//HBsf/xwb
        H/8dHB//HRwf/x0cIP8dHCD/HRwg/x0dIP8eHSD/Hh0h/x4dIf8eHSH/Hh0h/x4dIf8eHSH/Hh0hw///
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////ABwbH8UcGx7/Gxse/xsb
        Hv8bGh7/Gxod/xsaHf8aGh3/Ghkd/xoZHP8aGRz/GRkc/xkYHP8ZGBv/GRgb/xgYG/8YGBv/GBca/xgX
        Gv8YFxr/Fxca/xcWGf8XFhn/FxYZ/xYWGP8WFRj/FhUY/xYVGP8VFRf/FRUX/xUUF/8VFBf/FBQW/xQU
        Fv8UExb/FBMW/xMTFf8TExX/ExIV/xMSFP8SEhTNt7e3KP///wD///8A////AP///wD///8AHn7/wR5+
        //8ef///Hn///x5///8ef///HoD//x6A//8egP//HoD//x6A//8egf//HoH//x6B//8egf//HoH//x6B
        //8egf//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C
        //8egv//HoL//x6C//8egv//HoL//x6C//8egf//HoH//x6B//8egf/B0OX/EP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////ABYVGMsWFhj/FxYZ/xcWGf8XFhn/Fxca/xgX
        Gv8YFxr/GBca/xgYG/8YGBv/GRgb/xkYG/8ZGBz/GRkc/xoZHP8aGRz/Ghkd/xoaHf8bGh3/Gxod/xsa
        Hv8bGx7/HBse/xwbHv8cGx//HBsf/xwcH/8dHB//HRwg/x0cIP8dHCD/HR0g/x4dIP8eHSH/Hh0h/x4d
        If8eHSH/Hh0h/x4dIf8eHSH/Hh0hw////wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A0NDQEBwbH8UcGx7/HBse/xsbHv8bGh7/Gxoe/xsaHf8aGh3/Ghkd/xoZHP8aGRz/Ghkc/xkZ
        HP8ZGBv/GRgb/xkYG/8YGBv/GBca/xgXGv8YFxr/Fxca/xcWGf8XFhn/FxYZ/xYWGf8WFhj/FhUY/xYV
        GP8VFRf/FRUX/xUUF/8VFBf/FBQW/xQUFv8UExb/FBMW/xMTFf8TExX/ExIV/xMSFf8TEhT/EhIUzf//
        /wD///8A////AP///wD///8AHn7/wR5+//8ef///Hn///x5///8ef///HoD//x6A//8egP//HoD//x6A
        //8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6C//8egv//HoL//x6C//8egv//HoL//x6C
        //8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6B//8egf//HoH//x6B
        //8egf//nMj/RP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////ABYW
        GMkWFhn/FxYZ/xcWGf8XFhn/Fxca/xgXGv8YFxr/GBca/xgYG/8ZGBv/GRgb/xkYG/8ZGRz/Ghkc/xoZ
        HP8aGRz/Ghkd/xoaHf8bGh3/Gxoe/xsaHv8bGx7/HBse/xwbHv8cGx//HBsf/x0cH/8dHCD/HRwg/x0c
        IP8dHSD/Hh0g/x4dIf8eHSH/Hh0h/x4dIf8eHSH/Hh4h/x8eIf8fHiH/Hh4hwf///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8Am5udRBwbH/8cGx//HBse/xsbHv8bGh7/Gxoe/xsa
        Hf8bGh3/Ghod/xoZHf8aGRz/Ghkc/xkZHP8ZGBv/GRgb/xkYG/8YGBv/GBca/xgXGv8YFxr/Fxca/xcW
        Gf8XFhn/FxYZ/xYWGf8WFhj/FhUY/xYVGP8VFRf/FRUX/xUUF/8VFBf/FRQX/xQUFv8UExb/FBMW/xQT
        Ff8TExX/ExIV/xMSFf8TEhT/EhIUzf///wD///8A////AP///wD///8AHn7/wR5+//8efv//Hn///x5/
        //8ef///Hn///x6A//8egP//HoD//x6A//8egP//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6C
        //8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C
        //8egv//HoL//x6B//8egf//HoH//x6B//8egf//HoH/wf///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8AuLe4KBYWGMkWFhn/FxYZ/xcWGf8XFhn/Fxca/xgXGv8YFxr/GBca/xgY
        G/8ZGBv/GRgb/xkYG/8ZGRz/Ghkc/xoZHP8aGR3/Ghod/xsaHf8bGh3/Gxoe/xsaHv8bGx7/HBse/xwb
        H/8cGx//HBwf/x0cH/8dHCD/HRwg/x0cIP8eHSD/Hh0h/x4dIf8eHSH/Hh0h/x4eIf8fHiH/Hx4i/x8e
        Iv8fHiL/Hx4iwf///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AHBwfwxwb
        H/8cGx//HBse/xwbHv8bGx7/Gxoe/xsaHf8bGh3/Ghod/xoZHf8aGRz/Ghkc/xkZHP8ZGBz/GRgb/xkY
        G/8YGBv/GBca/xgXGv8YFxr/Fxca/xcWGf8XFhn/FxYZ/xYWGf8WFhj/FhUY/xYVGP8WFRj/FRUX/xUU
        F/8VFBf/FRQX/xQUFv8UExb/FBMW/xQTFf8TExX/ExIV/xMSFf8TEhT/EhIUzf///wD///8A////AP//
        /wD///8AHn7/wR5+//8efv//Hn///x5///8ef///Hn///x6A//8egP//HoD//x6A//8egP//HoH//x6B
        //8egf//HoH//x6B//8egf//HoH//x6B//8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C
        //8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoH//x6B//8egf//HoH//x6B//8egf//HoH/wf//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AmZiaSBYWGP8WFhn/FxYZ/xcW
        Gf8XFhn/Fxca/xgXGv8YFxr/GBca/xgYG/8ZGBv/GRgb/xkYG/8ZGRz/Ghkc/xoZHP8aGR3/Ghod/xsa
        Hf8bGh3/Gxoe/xsbHv8cGx7/HBse/xwbH/8cGx//HBwf/x0cH/8dHCD/HRwg/x0dIP8eHSH/Hh0h/x4d
        If8eHSH/Hh4h/x8eIv8fHiL/Hx4i/x8eIv8fHiL/Hx4iwf///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8AHRwfwxwbH/8cGx//HBse/xwbHv8bGx7/Gxoe/xsaHf8bGh3/Ghod/xoZ
        Hf8aGRz/Ghkc/xkZHP8ZGBz/GRgb/xkYG/8YGBv/GBca/xgXGv8YFxr/Fxca/xcWGf8XFhn/FxYZ/xcW
        Gf8WFhj/FhUY/xYVGP8WFRj/FRUX/xUUF/8VFBf/FRQX/xQUFv8UExb/FBMW/xQTFf8TExX/ExIV/xMS
        Ff8TEhT/EhIUzf///wD///8A////AP///wD///8AHn7/wR5+//8efv//Hn///x5///8ef///Hn///x6A
        //8egP//HoD//x6A//8egP//HoD//x6B//8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoL//x6C
        //8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C//8egf//HoH//x6B
        //8egf//HoH//x6B//8egf//LIn/s////wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8AuLe4KBYWGMkWFhn/FxYZ/xcWGf8XFhn/Fxca/xgXGv8YFxr/GBca/xgYG/8ZGBv/GRgb/xkY
        HP8ZGRz/Ghkc/xoZHP8aGR3/Ghod/xsaHf8bGh3/Gxoe/xsbHv8cGx7/HBse/xwbH/8cGx//HRwf/x0c
        IP8dHCD/HRwg/x0dIP8eHSH/Hh0h/x4dIf8eHiH/Hx4h/x8eIv8fHiL/Hx4i/x8eIv8fHiL/Hx4iwf//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AKyottRwbH/8cGx//HBsf/xwb
        Hv8bGx7/Gxoe/xsaHv8bGh3/Ghod/xoZHf8aGRz/Ghkc/xkZHP8ZGBz/GRgb/xkYG/8YGBv/GBca/xgX
        Gv8YFxr/GBca/xcXGv8XFhn/FxYZ/xcWGf8WFhj/FhUY/xYVGP8WFRj/FRUX/xUUF/8VFBf/FRQX/xQU
        Fv8UExb/FBMW/xQTFf8TExX/ExIV/xMSFf8TEhT/EhIUzf///wD///8A////AP///wD///8AHn7/wR5+
        //8efv//Hn7//x5///8ef///Hn///x5///8egP//HoD//x6A//8egP//HoD//x6B//8egf//HoH//x6B
        //8egf//HoH//x6B//8egf//HoH//x6B//8egv//HoL//x6C//8egv//HoL//x6C//8egv//HoL//x6C
        //8egv//HoL//x6B//8egf//HoH//x6B//8egf//HoH//x6B//8egf/BnMj/RP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////ABYWGMkWFhn/FxYZ/xcWGf8XFhn/Fxca/xgX
        Gv8YFxr/GBca/xgYG/8ZGBv/GRgb/xkYHP8ZGRz/Ghkc/xoZHP8aGR3/Ghod/xsaHf8bGh7/Gxoe/xsb
        Hv8cGx7/HBse/xwbH/8cGx//HRwf/x0cIP8dHCD/HRwg/x4dIP8eHSH/Hh0h/x4dIf8eHiH/Hx4i/x8e
        Iv8fHiL/Hx4i/x8fIv8fHyL/Hx4iwf///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8AnJudRBwcH8McGx//HBsf/xwbHv8bGx7/Gxoe/xsaHv8bGh3/Ghod/xoZHf8aGRz/Ghkc/xkZ
        HP8ZGBz/GRgb/xkYG/8ZGBv/GBgb/xgXGv8YFxr/GBca/xcXGv8XFhn/FxYZ/xcWGf8WFhj/FhUY/xYV
        GP8WFRj/FRUX/xUUF/8VFBf/FRQX/xQUFv8UExb/FBMW/xQTFf8TExX/ExIV/xMSFf8TEhT/EhIUzf//
        /wD///8A////AP///wD///8ALIb/sx5+//8efv//Hn7//x5///8ef///Hn///x5///8egP//HoD//x6A
        //8egP//HoD//x6A//8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6C
        //8egv//HoL//x6C//8egv//HoL//x6C//8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6B
        //8egf/B////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////ABYW
        GMkWFhn/FxYZ/xcWGf8XFhn/Fxca/xgXGv8YFxr/GBca/xgYG/8ZGBv/GRgb/xkYHP8ZGRz/Ghkc/xoZ
        HP8aGR3/Ghod/xsaHf8bGh7/Gxoe/xsbHv8cGx7/HBsf/xwbH/8cGx//HRwf/x0cIP8dHCD/HRwg/x4d
        IP8eHSH/Hh0h/x4dIf8fHiH/Hx4i/x8eIv8fHiL/Hx8i/yAfI/8gHyP/IB8jwf///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////ABwcH8McGx//HBsf/xwbHv8bGx7/Gxoe/xsa
        Hv8bGh3/Ghod/xoZHf8aGRz/Ghkc/xkZHP8ZGRz/GRgb/xkYG/8ZGBv/GBgb/xgXGv8YFxr/GBca/xcX
        Gv8XFhn/FxYZ/xcWGf8WFhj/FhUY/xYVGP8WFRj/FRUX/xUUF/8VFBf/FRQX/xQUFv8UExb/FBMW/xQT
        Ff8TExX/ExMV/xMSFf8TEhT/ISEjv////wD///8A////AP///wD///8Aj77/UB5+/8Eefv//Hn7//x5/
        //8ef///Hn///x5///8ef///HoD//x6A//8egP//HoD//x6A//8egP//HoH//x6B//8egf//HoH//x6B
        //8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6B
        //8egf//HoH//x6B//8egf//HoH//x6B//8egf/B////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////ACUlJrsXFhn/FxYZ/xcWGf8XFhn/Fxca/xgXGv8YFxr/GBca/xgY
        G/8ZGBv/GRgb/xkYHP8ZGRz/Ghkc/xoZHP8aGR3/Ghod/xsaHf8bGh7/Gxoe/xsbHv8cGx7/HBsf/xwb
        H/8cHB//HRwf/x0cIP8dHCD/HRwg/x4dIP8eHSH/Hh0h/x4dIf8fHiH/Hx4i/x8eIv8fHiL/IB8j/yAf
        I/8gHyPBLi0xs////wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////ABwc
        H8McGx//HBsf/xwbHv8bGx7/Gxoe/xsaHv8bGh3/Ghod/xoZHf8aGRz/Ghkc/xoZHP8ZGRz/GRgb/xkY
        G/8ZGBv/GBgb/xgXGv8YFxr/GBca/xcXGv8XFhn/FxYZ/xcWGf8WFhj/FhUY/xYVGP8WFRj/FRUX/xUU
        F/8VFBf/FRQX/xQUFv8UExb/FBMW/xQTFv8TExX/ExMV/xMSFf8TEhTNt7e3KP///wD///8A////AP//
        /wD///8A////AB5+/8Eefv//Hn7//x5+//8ef///Hn///x5///8ef///HoD//x6A//8egP//HoD//x6A
        //8egP//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6B
        //8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6B/8FFlv+b////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AM/PzxAWFhnJFxYZ/xcW
        Gf8XFhn/Fxca/xgXGv8YFxr/GBca/xgYG/8ZGBv/GRgb/xkYHP8ZGRz/Ghkc/xoZHP8aGR3/Ghod/xsa
        Hf8bGh7/Gxoe/xsbHv8cGx7/HBsf/xwbH/8cGx//HRwf/x0cIP8dHCD/HRwg/x4dIP8eHSH/Hh0h/x4d
        If8fHiH/Hx4i/x8eIv8fHiL/Hx8i/yAfI/8gHyPB////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AENDRp0cGx/FHBsf/xwbHv8bGx7/Gxoe/xsaHv8bGh3/Ghod/xoZ
        Hf8aGRz/Ghkc/xkZHP8ZGRz/GRgb/xkYG/8ZGBv/GBgb/xgXGv8YFxr/GBca/xcXGv8XFhn/FxYZ/xcW
        Gf8WFhj/FhUY/xYVGP8WFRj/FRUX/xUUF/8VFBf/FRQX/xQUFv8UExb/FBMW/xQTFf8TExX/ExMV/xMS
        Ff8TEhTN////AP///wD///8A////AP///wD///8A////AHmy/2Yefv/BHn7//x5+//8ef///Hn///x5/
        //8ef///Hn///x6A//8egP//HoD//x6A//8egP//HoD//x6B//8egf//HoH//x6B//8egf//HoH//x6B
        //8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6B
        //8egf//HoH//x6A/8H///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wAWFhnJFxYZyRcWGf8XFhn/Fxca/xgXGv8YFxr/GBca/xgYG/8ZGBv/GRgb/xkY
        HP8ZGRz/Ghkc/xoZHP8aGR3/Ghod/xsaHf8bGh7/Gxoe/xsbHv8cGx7/HBse/xwbH/8cGx//HRwf/x0c
        IP8dHCD/HRwg/x4dIP8eHSH/Hh0h/x4dIf8eHiH/Hx4i/x8eIv8fHiL/Hx4i/x8fIsEtLTCz////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wAcGx/FHBsf/xwb
        Hv8bGx7/Gxoe/xsaHv8bGh3/Ghod/xoZHf8aGRz/Ghkc/xkZHP8ZGBz/GRgb/xkYG/8ZGBv/GBgb/xgX
        Gv8YFxr/GBca/xcXGv8XFhn/FxYZ/xcWGf8WFhj/FhUY/xYVGP8WFRj/FRUX/xUUF/8VFBf/FRQX/xQU
        Fv8UExb/FBMW/xQTFf8TExX/ExIV/xMSFc2Xl5hI////AP///wD///8A////AP///wD///8A////AP//
        /wAshv+zHn7//x5+//8efv//Hn///x5///8ef///Hn///x5///8egP//HoD//x6A//8egP//HoD//x6A
        //8egP//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6B
        //8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoD/wZzH/0T///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AFxYZyRcWGf8XFhn/Fxca/xgX
        Gv8YFxr/GBca/xgYG/8ZGBv/GRgb/xkYHP8ZGRz/Ghkc/xoZHP8aGR3/Ghod/xsaHf8bGh3/Gxoe/xsb
        Hv8cGx7/HBse/xwbH/8cGx//HRwf/x0cIP8dHCD/HRwg/x0dIP8eHSH/Hh0h/x4dIf8eHSH/Hx4h/x8e
        Iv8fHiL/Hx4i/x8eIsH///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wCbm51EHBsexRwbHv8bGx7/Gxoe/xsaHv8bGh3/Ghod/xoZHf8aGRz/Ghkc/xkZ
        HP8ZGBz/GRgb/xkYG/8YGBv/GBca/xgXGv8YFxr/GBca/xcXGv8XFhn/FxYZ/xcWGf8WFhj/FhUY/xYV
        GP8WFRj/FRUX/xUUF/8VFBf/FRQX/xQUFv8UExb/FBMW/xQTFf8TExX/ExIVzSIhJL////8A////AP//
        /wD///8A////AP///wD///8A////AP///wDd7P8CHn7/wR5+//8efv//Hn7//x5///8ef///Hn///x5/
        //8ef///HoD//x6A//8egP//HoD//x6A//8egP//HoD//x6B//8egf//HoH//x6B//8egf//HoH//x6B
        //8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6A//8egP/Bd7L/aP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8Az8/PEBcWGckXFhn/Fxca/xgXGv8YFxr/GBca/xgYG/8ZGBv/GRgb/xkYG/8ZGRz/Ghkc/xoZ
        HP8aGR3/Ghod/xsaHf8bGh3/Gxoe/xsbHv8cGx7/HBse/xwbH/8cGx//HBwf/x0cH/8dHCD/HRwg/x0d
        IP8eHSD/Hh0h/x4dIf8eHSH/Hh4h/x8eIf8fHiL/Hx4iwdDQ0RD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AdnV3ahwbHsUbGx7/Gxoe/xsa
        Hf8bGh3/Ghod/xoZHf8aGRz/Ghkc/xkZHP8ZGBz/GRgb/xkYG/8YGBv/GBca/xgXGv8YFxr/Fxca/xcW
        Gf8XFhn/FxYZ/xcWGf8WFhj/FhUY/xYVGP8WFRj/FRUX/xUUF/8VFBf/FRQX/xQUFv8UExb/FBMW/xQT
        Ff8TExXNExIVzf///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A3ez/Ah5+
        /8Eefv//Hn7//x5///8ef///Hn///x5///8ef///HoD//x6A//8egP//HoD//x6A//8egP//HoD//x6A
        //8egP//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6B//8egf//HoH//x6B
        //8egP//HoD//x6A/8F3sv9o////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AM/PzxAXFhnJFxca/xgXGv8YFxr/GBca/xgY
        G/8ZGBv/GRgb/xkYG/8ZGRz/Ghkc/xoZHP8aGR3/Ghod/xsaHf8bGh3/Gxoe/xsaHv8bGx7/HBse/xwb
        H/8cGx//HBsf/x0cH/8dHCD/HRwg/x0cIP8dHSD/Hh0h/x4dIf8eHSH/Hh0h/x4eIf8fHiHB0NDREP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AHV1d2obGx7FGxoe/xsaHf8bGh3/Ghod/xoZHf8aGRz/Ghkc/xkZHP8ZGBz/GRgb/xkY
        G/8YGBv/GBca/xgXGv8YFxr/Fxca/xcWGf8XFhn/FxYZ/xYWGf8WFhj/FhUY/xYVGP8VFRf/FRUX/xUU
        F/8VFBf/FRQX/xQUFv8UExb/FBMW/xQTFc0TExXN////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AN3s/wIefv/BHn7//x5+//8ef///Hn///x5///8ef///Hn///x5/
        //8egP//HoD//x6A//8egP//HoD//x6A//8egP//HoD//x6A//8egP//HoH//x6B//8egf//HoH//x6B
        //8egf//HoH//x6B//8egP//HoD//x6A//8egP//HoD/wXey/2j///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wDPz88QFxcayRgXGv8YFxr/GBca/xgYG/8ZGBv/GRgb/xkYG/8ZGRz/GRkc/xoZHP8aGRz/Ghkd/xoa
        Hf8bGh3/Gxoe/xsaHv8bGx7/HBse/xwbHv8cGx//HBsf/x0cH/8dHB//HRwg/x0cIP8dHCD/Hh0g/x4d
        If8eHSH/Hh0h/x4dIcPQ0NEQ////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wB1dXdqGxoexRsaHf8aGh3/Ghod/xoZ
        Hf8aGRz/Ghkc/xkZHP8ZGBv/GRgb/xkYG/8YGBv/GBca/xgXGv8YFxr/Fxca/xcWGf8XFhn/FxYZ/xYW
        Gf8WFhj/FhUY/xYVGP8VFRf/FRUX/xUUF/8VFBf/FBQW/xQUFv8UExb/FBMWzRQTFc3///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wDd7P8CHn7/wR5+
        //8efv//Hn///x5///8ef///Hn///x5///8ef///HoD//x6A//8egP//HoD//x6A//8egP//HoD//x6A
        //8egP//HoD//x6A//8egP//HoD//x6A//8egP//HoD//x6A//8egP//HoD//x6A//8egP/Bd7L/aP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8Az8/PEBgXGskYFxr/GBca/xgXGv8YGBv/GRgb/xkY
        G/8ZGBz/GRkc/xoZHP8aGRz/Ghkd/xoaHf8bGh3/Gxod/xsaHv8bGx7/HBse/xwbHv8cGx//HBsf/xwc
        H/8dHB//HRwg/x0cIP8dHCD/HR0g/x4dIP8eHSH/Hh0hw9DQ0RD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8AdXV3ahsaHcUaGh3/Ghkd/xoZHP8aGRz/GRkc/xkYHP8ZGBv/GRgb/xkYG/8YGBv/GBca/xgX
        Gv8YFxr/Fxca/xcWGf8XFhn/FxYZ/xYWGf8WFhj/FhUY/xYVGP8VFRf/FRUX/xUUF/8VFBf/FBQW/xQU
        Fv8UExbNFBMWzf///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A3ez/Ah5+/8Eefv//Hn7//x5///8ef///Hn///x5///8ef///Hn///x6A
        //8egP//HoD//x6A//8egP//HoD//x6A//8egP//HoD//x6A//8egP//HoD//x6A//8egP//HoD//x6A
        //8egP//HoD//x6A/8F3sv9o////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AM/P
        zxAYFxrJGBca/xgXGv8YGBv/GRgb/xkYG/8ZGBv/GRkc/xoZHP8aGRz/Ghkd/xoaHf8bGh3/Gxod/xsa
        Hv8bGh7/Gxse/xwbHv8cGx7/HBsf/xwbH/8cHB//HRwf/x0cIP8dHCD/HRwg/x0cIP8eHSDD0NDREP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AHV1d2oaGh3FGhkd/xoZHP8aGRz/GRkc/xkY
        HP8ZGBv/GRgb/xgYG/8YFxr/GBca/xgXGv8YFxr/Fxca/xcWGf8XFhn/FxYZ/xYWGP8WFRj/FhUY/xYV
        GP8VFRf/FRUX/xUUF/8VFBf/FBQW/xQUFssUExbN////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AN3s/wIefv/BHn7//x5+
        //8ef///Hn///x5///8ef///Hn///x5///8ef///HoD//x6A//8egP//HoD//x6A//8egP//HoD//x6A
        //8egP//HoD//x6A//8egP//HoD//x6A//8egP//HoD/wXey/2j///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wDPz88QGBcayRgXGv8YGBv/GRgb/xkYG/8ZGBv/GRkc/xoZ
        HP8aGRz/Ghkc/xoZHf8aGh3/Gxod/xsaHf8bGh7/Gxse/xwbHv8cGx7/HBsf/xwbH/8cGx//HRwf/x0c
        H/8dHCD/HRwg/x0cIMPQ0NEQ////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wB1dHdsGhkdxxoZHP8aGRz/GRkc/xkYG/8ZGBv/GRgb/xgYG/8YFxr/GBca/xgXGv8XFxr/FxYZ/xcW
        Gf8XFhn/FxYZ/xYWGP8WFRj/FhUY/xYVGP8VFRf/FRQX/xUUF/8VFBf/FBQWyxQUFsv///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wDd7P8CHn7/wR5+//8efv//Hn7//x5///8ef///Hn///x5///8ef///Hn///x5/
        //8egP//HoD//x6A//8egP//HoD//x6A//8egP//HoD//x6A//8egP//HoD//x6A//8egP/Bd7L/aP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8Az8/PEBgX
        GskYGBv/GBgb/xkYG/8ZGBv/GRgc/xkZHP8aGRz/Ghkc/xoZHf8aGh3/Gxod/xsaHf8bGh7/Gxoe/xsb
        Hv8cGx7/HBse/xwbH/8cGx//HBsf/x0cH/8dHB//HRwgw9DQ0RD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8AdXR2bBoZHMcZGRz/GRgc/xkYG/8ZGBv/GRgb/xgY
        G/8YFxr/GBca/xgXGv8XFxr/FxYZ/xcWGf8XFhn/FhYZ/xYWGP8WFRj/FhUY/xYVGP8VFRf/FRQX/xUU
        F/8VFBfLFBQWy////wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A3ez/Ah5+/8Eefv//Hn7//x5+
        //8ef///Hn///x5///8ef///Hn///x5///8ef///Hn///x5///8ef///HoD//x6A//8egP//HoD//x6A
        //8egP//HoD//x6A/8F3sv9o////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AM/PzxAYFxrJGBgb/xkYG/8ZGBv/GRgb/xkZHP8aGRz/Ghkc/xoZ
        HP8aGR3/Ghod/xsaHf8bGh3/Gxoe/xsaHv8bGx7/HBse/xwbHv8cGx//HBsf/xwbH/8cHB/D0NDQEP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AHV0
        dmwZGRzHGRgc/xkYG/8ZGBv/GBgb/xgXGv8YFxr/GBca/xgXGv8XFxr/FxYZ/xcWGf8XFhn/FhYZ/xYW
        GP8WFRj/FhUY/xUVF/8VFRf/FRQX/xUUF8sUFBbL////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AN3s/wIefv/BHn7//x5+//8efv//Hn7//x5///8ef///Hn///x5///8ef///Hn///x5/
        //8ef///Hn///x5///8ef///Hn///x5///8ef///Hn//wXey/2j///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wDPz88QGBgbxxkY
        G/8ZGBv/GRgb/xkYHP8ZGRz/Ghkc/xoZHP8aGR3/Ghod/xsaHf8bGh3/Gxoe/xsaHv8bGx7/Gxse/xwb
        Hv8cGx7/HBsf/xwbH8XQ0NAQ////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wB0dHZsGRgbxxkYG/8ZGBv/GBgb/xgXGv8YFxr/GBca/xcX
        Gv8XFhn/FxYZ/xcWGf8XFhn/FhYY/xYVGP8WFRj/FhUY/xUVF/8VFRf/FRQXyxUUF8v///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wDd7P8CHn7/wR5+//8efv//Hn7//x5+
        //8efv//Hn///x5///8ef///Hn///x5///8ef///Hn///x5///8ef///Hn///x5///8ef//Bd7L/aP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8Az8/QEBgYG8cZGBv/GRgb/xkYG/8ZGRz/Ghkc/xoZHP8aGRz/Ghkd/xoa
        Hf8bGh3/Gxod/xsaHv8bGh7/Gxse/xsbHv8cGx7/HBsexdDQ0BD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AdHN1bBkY
        G8cZGBv/GBgb/xgXGv8YFxr/GBca/xcXGv8XFhn/FxYZ/xcWGf8WFhn/FhYY/xYVGP8WFRj/FhUY/xUV
        F/8VFBfLFRQXy////wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A3ez/Ah5+/8Eefv/BHn7//x5+//8efv//Hn7//x5+//8ef///Hn///x5///8ef///Hn///x5/
        //8ef///Hn///x5//8F3sv9o////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AM/P0BAZGBvHGRgbxxkY
        G/8ZGBz/GRkc/xoZHP8aGRz/Ghkd/xoZHf8aGh3/Gxod/xsaHf8bGh7/Gxoe/xsbHsUbGx7F0NDQEP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AHRzdWwYGBvHGBca/xgXGv8YFxr/GBca/xcXGv8XFhn/FxYZ/xcW
        Gf8WFhn/FhYY/xYVGP8WFRj/FRUX/xUVF8sVFBfL////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wAshv+zHn7//x5+//8efv//Hn7//x5+
        //8efv//Hn7//x5+//8efv//Hn7//x5///8ef///Hn//wZzH/0T///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8AGRgbxxkYG8cZGBv/GRkc/xkZHP8aGRz/Ghkc/xoZHf8aGh3/Ghod/xsa
        Hf8bGh3/GxoexRsaHsX///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wCamptGGBcayRgX
        Gv8YFxr/Fxca/xcWGf8XFhn/FxYZ/xcWGf8WFhj/FhUY/xYVGP8WFRj/FRUXyyQkJrv///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wDD3P8cHn3//x5+//8efv//Hn7//x5+//8efv//Hn7//x5+//8efv//Hn7//x5+//8efv//Hn7/wf//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AJqam0YZGBvHGRgcxxkZ
        HMcaGRz/Ghkc/xoZHP8aGR3/GhodxRoaHcUbGh3Fm5qcRv///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8AGBcayRgXGv8YFxr/Fxca/xcWGf8XFhn/FxYZ/xYWGf8WFhj/FhUY/xYV
        GP8WFRj/FRUXy////wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wDD3P8cHn3//x59//8eff//Hn3//x5+//8efv//Hn7//x5+
        //8efv//Hn7//x5+//8efv//Hn7/wf///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AHRzdmwZGRzHGhkcxxoZHMcaGRzHdXR3bP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AGBcayRgXGv8XFxr/FxYZ/xcW
        Gf8XFhn/FxYZ/xYWGP8WFRj/FhUY/xYVGP8VFRf/FRUXy////wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wDD3P8cHn3//x59
        //8eff//Hn3//x59//8eff//Hn3//x5+//8efv//Hn7//x5+//8efv//Hn7/wf///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8AGBcayRcXGv8XFxr/FxYZ/xcWGf8XFhn/FhYZ/xYWGP8WFRj/FhUY/xYVGP8VFRf/FRQXy///
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wDD3P8cHnz//x59//8eff//Hn3//x59//8eff//Hn3//x59//8eff//Hn3//x59
        //8eff//Hn3/wf///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8AGBcayRcXGv8XFhn/FxYZ/xcWGf8XFhn/FhYY/xYV
        GP8WFRj/FhUY/xUVF/8VFRf/FRQXy////wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wDD3P8cHnz//x58//8efP//Hn3//x59
        //8eff//Hn3//x59//8eff//Hn3//x59//8eff//Hn3/wf///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AFxcayRcW
        Gf8XFhn/FxYZ/xcWGf8WFhn/FhYY/xYVGP8WFRj/FhUY/xUVF/8VFBf/FRQXy////wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wDD3P8cHnz//x58//8efP//Hnz//x58//8efP//Hnz//x59//8eff//Hn3//x59//8eff//Hn3/wf//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8AFxcayRcWGf8XFhn/FxYZ/xYWGf8WFhj/FhUY/xYVGP8WFRj/FRUX/xUV
        F/8VFBf/FRQXy////wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wDD3P8cHnv//x58//8efP//Hnz//x58//8efP//Hnz//x58
        //8efP//Hnz//x58//8efP//Hnz/wf///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AFxYZyRcWGf8XFhn/FxYZ/xYW
        GP8WFhj/FhUY/xYVGP8WFRj/FRUX/xUUF/8VFBf/FRQXy////wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wDD3P8cHnv//x57
        //8ee///Hnv//x58//8efP//Hnz//x58//8efP//Hnz//x58//8efP//Hnz/wf///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8AFxYZyRcWGf8XFhn/FhYZ/xYWGP8WFRj/FhUY/xYVGP8VFRf/FRUX/xUUF/8VFBf/FRQXy///
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wDD3P8cHnv//x57//8ee///Hnv//x57//8ee///Hnv//x57//8ee///Hnv//x58
        //8efP//Hnz/wf///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8AFxYZyRcWGf8WFhn/FhYY/xYVGP8WFRj/FhUY/xUV
        F/8VFRf/FRQX/xUUF/8VFBf/FBQWy////wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wDD3P8cHnr//x56//8eev//Hnr//x56
        //8eev//Hnv//x57//8ee///Hnv//x57//8ee///Hnv/wf///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wC4uLkoGBcayRgXGskYFxrJGBcayRgYG8cYGBvHGRgbxxkY
        G8e5uLko////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AFxYZyRYW
        Gf8WFhj/FhUY/xYVGP8WFRj/FhUY/xUVF/8VFBf/FRQX/xUUF/8VFBf/FBQWy////wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wCPvP9QHnn//x55//8eef//Hnr//x56//8eev//Hnr//x56//8eev//Hnr//x57//8ee///Hnv/wf//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AmZmaRhcWGckXFxrJFxca/xgX
        Gv8YFxr/GBca/xgXGv8YFxr/GBgb/xgYG/8ZGBvHGRgbx7m4uSj///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8AFhYZyRYWGP8WFRj/FhUY/xYVGP8WFRj/FRUX/xUVF/8VFBf/FRQX/xUU
        F/8UFBb/FBQWy7e3uCj///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AHmv/2YeeP/BHnj//x55//8eef//Hnn//x55//8eef//Hnr//x56
        //8eev//Hnr//x56//8eev//Hnr/wR56/8H///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wAmJSe7FxYZyRcWGf8XFhn/Fxca/xcXGv8YFxr/GBca/xgXGv8YFxr/GBca/xgYG/8YGBv/GRgb/xkY
        G8cnJim5////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wAWFhnJFhYYyRYWGP8WFRj/FhUY/xYV
        GP8VFRf/FRUX/xUUF/8VFBf/FRQX/xQUFv8UFBb/FBMW/xQTFs2Yl5hI////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AYqD/fh53/8EeeP//Hnj//x54
        //8eeP//Hnn//x55//8eef//Hnn//x55//8eef//Hnr//x56//8eev//Hnr//x56/8Eeev/B////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////ACUlJrsWFhnJFxYZ/xcWGf8XFhn/FxYZ/xcXGv8XFxr/GBca/xgX
        Gv8YFxr/GBca/xgXGv8YFxr/GBgb/xgYG/8YGBvHJyYpuf///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////ABYW
        GckWFhjJFhYY/xYVGP8WFRj/FhUY/xUVF/8VFRf/FRQX/xUUF/8VFBf/FBQW/xQUFv8UExb/FBMW/xQT
        Fv8TExXNcHBycP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wBin/9+Hnf/wR53//8ed///Hnf//x54//8eeP//Hnj//x54//8eeP//Hnn//x55//8eef//Hnn//x55
        //8eef//Hnn//x55//8eev/BHnr/wf///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AJSQmuxYVGMsWFhj/FhYZ/xcW
        Gf8XFhn/FxYZ/xcWGf8XFhn/Fxca/xcXGv8YFxr/GBca/xgXGv8YFxr/GBca/xgXGv8YGBv/GBgbxyYm
        Kbn///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8AFhYZyRYWGMkWFhj/FhUY/xYVGP8WFRj/FRUX/xUVF/8VFBf/FRQX/xUU
        F/8VFBf/FBQW/xQUFv8UExb/FBMW/xQTFf8TExX/ExMVzXBwcnD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AGKf/34edv/BHnb//x52//8ed///Hnf//x53//8ed///Hnj//x54
        //8eeP//Hnj//x54//8eeP//Hnn//x55//8eef//Hnn//x55//8eef//Hnn/wR55/8H///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wAlJCa7FhUYyxYVGP8WFRj/FhYY/xYWGf8XFhn/FxYZ/xcWGf8XFhn/FxYZ/xcXGv8XFxr/GBca/xgX
        Gv8YFxr/GBca/xgXGv8YFxr/GBca/xgXGskmJim5////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wAWFhnJFhYYyRYWGP8WFRj/FhUY/xYV
        GP8VFRf/FRUX/xUUF/8VFBf/FRQX/xUUF/8UFBb/FBQW/xQTFv8UExb/FBMV/xMTFf8TExX/ExIV/xMS
        Fc1wcHFw////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AYp//fh51/8Eedf//Hnb//x52
        //8edv//Hnb//x53//8ed///Hnf//x53//8ed///Hnj//x54//8eeP//Hnj//x54//8eeP//Hnj//x54
        //8eeP//Hnj//x55/8Eeef/B////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////ACQkJrsVFRfLFhUY/xYVGP8WFRj/FhUY/xYWGP8WFhj/FxYZ/xcW
        Gf8XFhn/FxYZ/xcWGf8XFhn/Fxca/xcXGv8YFxr/GBca/xgXGv8YFxr/GBca/xgXGv8YFxrJJiYouf//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////ABYW
        GckWFhjJFhUY/xYVGP8WFRj/FhUY/xUVF/8VFRf/FRQX/xUUF/8VFBf/FRQX/xQUFv8UFBb/FBMW/xQT
        Fv8UExb/ExMV/xMTFf8TEhX/ExIV/xMSFP8SEhTNcHBxcP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wBinv9+HnT/wR50//8edf//HnX//x51//8edv//Hnb//x52//8edv//Hnf//x53//8ed///Hnf//x53
        //8ed///Hnf//x54//8eeP//Hnj//x54//8eeP//Hnj//x54//8eeP/BHnj/wf///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AJCMmvRUUF8sVFRf/FRUX/xYV
        GP8WFRj/FhUY/xYVGP8WFhj/FhYY/xYWGf8XFhn/FxYZ/xcWGf8XFhn/FxYZ/xcWGf8XFxr/Fxca/xcX
        Gv8YFxr/GBca/xgXGv8YFxr/GBcaySYmKLn///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8AFhYYyRYWGMkWFRj/FhUY/xYVGP8WFRj/FRUX/xUVF/8VFBf/FRQX/xUU
        F/8VFBf/FBQW/xQUFv8UExb/FBMW/xQTFv8TExX/ExMV/xMSFf8TEhX/ExIU/xMSFP8SEhT/EhIUzXBv
        cXD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AGKd/34ec//BHnT//x50//8edP//HnT//x51//8edf//HnX//x52
        //8edv//Hnb//x52//8edv//Hnf//x53//8ed///Hnf//x53//8ed///Hnf//x53//8ed///Hnf//x53
        //8ed///Hnf/wR53/8H///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wAkIya9FRQXyxUUF/8VFBf/FRUX/xUVF/8WFRj/FhUY/xYVGP8WFRj/FhUY/xYWGP8WFhn/FxYZ/xcW
        Gf8XFhn/FxYZ/xcWGf8XFhn/FxYZ/xcXGv8XFxr/Fxca/xcXGv8YFxr/GBca/xgXGskmJii5////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wAWFhjJFhYYyRYVGP8WFRj/FhUY/xYV
        GP8VFRf/FRUX/xUUF/8VFBf/FRQX/xUUF/8UFBb/FBQW/xQTFv8UExb/FBMW/xQTFf8TExX/ExMV/xMS
        Ff8TEhX/ExIU/xISFP8SEhT/EhEU/xIRE89wb3Bw////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AY53/fB9y/8Eec///HnP//x5z
        //8edP//HnT//x50//8edP//HnX//x51//8edf//HnX//x52//8edv//Hnb//x52//8edv//Hnb//x53
        //8ed///Hnf//x53//8ed///Hnf//x53//8ed///Hnf//x53/8Eed//B////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////ACMjJb0UFBbLFRQX/xUUF/8VFBf/FRQX/xUVF/8VFRf/FRUX/xYV
        GP8WFRj/FhUY/xYVGP8WFhj/FhYY/xYWGf8XFhn/FxYZ/xcWGf8XFhn/FxYZ/xcWGf8XFhn/FxYZ/xcX
        Gv8XFxr/Fxca/xcXGv8XFxrJJiYouf///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////ABYW
        GMkWFRjLFhUY/xYVGP8WFRj/FhUY/xUVF/8VFRf/FRQX/xUUF/8VFBf/FRQX/xQUFv8UFBb/FBMW/xQT
        Fv8UExb/FBMV/xMTFf8TExX/ExIV/xMSFf8TEhT/EhIU/xISFP8SEhT/EhET/xIRE/8RERPPb29wcP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wBjnP98H3H/wR9y//8fcv//H3L//x5z//8ec///HnP//x50//8edP//HnT//x50//8edf//HnX//x51
        //8edf//HnX//x51//8edv//Hnb//x52//8edv//Hnb//x52//8edv//Hnb//x52//8edv//Hnb//x52
        //8edv/BHnb/wf///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AIyIlvRQTFs0UFBb/FBQW/xUU
        F/8VFBf/FRQX/xUUF/8VFRf/FRUX/xUVF/8WFRj/FhUY/xYVGP8WFRj/FhUY/xYWGP8WFhj/FhYZ/xcW
        Gf8XFhn/FxYZ/xcWGf8XFhn/FxYZ/xcWGf8XFhn/FxYZ/xcWGf8XFhn/FxcaySYmKLn///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8AFhUYyxYVGMsWFRj/FhUY/xYVGP8VFRf/FRUX/xUVF/8VFBf/FRQX/xUU
        F/8VFBf/FBQW/xQUFv8UExb/FBMW/xQTFv8UExX/ExMV/xMTFf8TEhX/ExIV/xMSFP8SEhT/EhIU/xIS
        FP8SERT/EhET/xERE/8RERP/ERETz29vcHD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AGOb/3wfcP/BH3H//x9x//8fcv//H3L//x9y//8fcv//HnP//x5z
        //8ec///HnT//x50//8edP//HnT//x50//8edf//HnX//x51//8edf//HnX//x51//8edf//HnX//x52
        //8edv//Hnb//x52//8edv//Hnb//x52//8edv//Hnb/wR52/8H///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wAjIiS9FBMWzRQTFv8UExb/FBQW/xQUFv8UFBb/FRQX/xUUF/8VFBf/FRQX/xUVF/8VFRf/FhUY/xYV
        GP8WFRj/FhUY/xYVGP8WFRj/FhYY/xYWGP8WFhn/FhYZ/xcWGf8XFhn/FxYZ/xcWGf8XFhn/FxYZ/xcW
        Gf8XFhn/FxYZ/xcWGckmJSe7////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wAWFRjLFhUYyxYVGP8WFRj/FhUY/xUV
        F/8VFRf/FRUX/xUUF/8VFBf/FRQX/xUUF/8UFBb/FBQW/xQTFv8UExb/FBMW/xQTFf8TExX/ExMV/xMS
        Ff8TEhX/ExIU/xISFP8SEhT/EhIU/xIRFP8SERP/EhET/xERE/8RERP/ERAT/xEQEs9vb3Bw////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AY5r/fB9v/8EfcP//H3D//x9x
        //8fcf//H3H//x9y//8fcv//H3L//x9y//8ec///HnP//x5z//8ec///HnT//x50//8edP//HnT//x50
        //8edP//HnX//x51//8edf//HnX//x51//8edf//HnX//x51//8edf//HnX//x51//8edf//HnX//x51
        /8Eedf/B////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////ACIiJL0TExXNFBMV/xQTFv8UExb/FBMW/xQUFv8UFBb/FBQW/xUU
        F/8VFBf/FRQX/xUUF/8VFRf/FRUX/xUVF/8WFRj/FhUY/xYVGP8WFRj/FhUY/xYVGP8WFhj/FhYY/xYW
        GP8WFhn/FhYZ/xcWGf8XFhn/FxYZ/xcWGf8XFhn/FxYZ/xcWGf8XFhnJJiUnu////wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////ABYV
        GMsWFRjLFhUY/xYVGP8VFRf/FRUX/xUVF/8VFBf/FRQX/xUUF/8VFBf/FBQW/xQUFv8UFBb/FBMW/xQT
        Fv8UExb/FBMV/xMTFf8TExX/ExIV/xMSFf8TEhT/ExIU/xISFP8SEhT/EhEU/xIRE/8SERP/ERET/xER
        E/8RERP/ERAS/xEQEv8QEBLPb29wcP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wCPt/9QH27/wR9v//8fb///H3D//x9w//8fcP//H3H//x9x//8fcf//H3L//x9y//8fcv//H3L//x5z
        //8ec///HnP//x5z//8ec///HnP//x50//8edP//HnT//x50//8edP//HnT//x50//8edP//HnT//x50
        //8edP//HnT//x50//8edP//HnT//x50//8edP/BHnT/wf///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AIiEkvxMSFc0TExX/ExMV/xMT
        Ff8UExb/FBMW/xQTFv8UExb/FBQW/xQUFv8VFBf/FRQX/xUUF/8VFBf/FRQX/xQTFf8ODg//CwsN/wsL
        DP8ODQ//FBMW/xYVGP8WFRj/FhUY/xYVGP8WFhj/FhYY/xYWGP8WFhj/FhYZ/xYWGf8XFhn/FxYZ/xcW
        Gf8XFhn/FxYZySYlJ7v///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8AFhUYyxYVGMsWFRj/FRUX/xUVF/8VFRf/FRQX/xUUF/8VFBf/FRQX/xUU
        F/8UFBb/FBQW/xQUFv8UExb/FBMW/xQTFv8UExX/ExMV/xMTFf8TEhX/ExIV/xMSFP8TEhT/EhIU/xIS
        FP8SERT/EhET/xIRE/8RERP/ERET/xERE/8REBL/ERAS/xAQEv8QEBL/EBASz7a2tir///8A////AP//
        /wD///8A////AP///wD///8A////ANHh/w4fbf/BH27//x9u//8fbv//H2///x9v//8fcP//H3D//x9w
        //8fcf//H3H//x9x//8fcv//H3L//x9y//8fcv//H3L//x5z//8ec///HnP//x5z//8ec///HnP//x5z
        //8ec///HnT//x50//8edP//HnT//x50//8edP//HnT//x50//8edP//HnT//x50//8edP//HnP/wSx8
        /7P///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wCXl5hIExIUzRMSFf8TEhX/ExMV/xMTFf8TExX/FBMV/xQTFv8UExb/FBMW/xQUFv8UFBb/FBQW/xUU
        F/8VFBf/ExIV/woKC/8JCQr/CAgJ/wgICf8IBwj/CAgJ/xMSFf8WFRj/FhUY/xYVGP8WFRj/FhUY/xYV
        GP8WFhj/FhYY/xYWGP8WFhj/FhYY/xYWGP8WFhj/FhYY/xYWGck+PkChmZmaRpmZmkaZmZpGmZmaRpmZ
        mkaZmZpGmZmaRpmYmkiZmJpImZiaSJmYmkiZmJpImZiaSJmYmkglJCa7FhUYyxUVF/8VFRf/FRUX/xUV
        F/8VFBf/FRQX/xUUF/8VFBf/FRQX/xQUFv8UFBb/FBMW/xQTFv8UExb/FBMW/xMTFf8TExX/ExMV/xMS
        Ff8TEhX/ExIU/xMSFP8SEhT/EhIU/xIRFP8SERP/EhET/xERE/8RERP/ERET/xEQE/8REBL/ERAS/xAQ
        Ev8QEBL/EA8R/xAPEdH///8A////AP///wD///8A////AP///wD///8A////AC11/7Mfbf//H23//x9t
        //8fbv//H27//x9v//8fb///H2///x9w//8fcP//H3D//x9x//8fcf//H3H//x9x//8fcv//H3L//x9y
        //8fcv//H3L//x9y//8fc///HnP//x5z//8ec///HnP//x5z//8ec///HnP//x5z//8ec///HnP//x5z
        //8ec///HnP//x5z//8ec///HnP//x5z/8HQ4v8Q////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wASEhTNEhIU/xMSFP8TEhX/ExIV/xMSFf8TExX/ExMV/xQT
        Ff8UExb/FBMW/xQTFv8UFBb/FBQW/xQUFv8TEhX/CwsM/woKC/8JCQr/CQkK/wkICf8ICAn/CAgJ/wgI
        Cf8TEhX/FhUY/xYVGP8WFRj/FhUY/xYVGP8WFRj/FhUY/xYVGP8WFRj/FhUY/xYVGP8WFRj/FhUY/xYW
        GP8WFhj/FhYY/xYVGP8WFRj/FhUY/xYVGP8WFRj/FhUY/xYVGP8WFRj/FhUY/xYVGP8WFRj/FhUY/xUV
        F/8VFRf/FRUX/xUVF/8VFRf/FRQX/xUUF/8VFBf/FRQX/xUUF/8UFBb/FBQW/xQUFv8UExb/FBMW/xQT
        Fv8UExX/ExMV/xMTFf8TExX/ExIV/xMSFf8TEhT/ExIU/xISFP8SEhT/EhEU/xIRE/8SERP/ERET/xER
        E/8RERP/ERAT/xEQEv8REBL/EBAS/xAQEv8QDxH/EA8R/xAPEdEeHiDB////AP///wD///8A////AP//
        /wD///8A0eH/Dh9s/8EfbP//H2z//x9t//8fbf//H27//x9u//8fbv//H2///x9v//8fb///H3D//x9w
        //8fcP//H3D//x9x//8fcf//H3H//x9x//8fcf//H3L//x9y//8fcv//H3L//x9y//8fcv//H3L//x9y
        //8fcv//H3L//x9z//8fc///H3P//x9z//8fcv//H3L//x9y//8fcv//H3L//x9y//8fcv/B////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AHBvcXASEhTNEhIU/xIS
        FP8TEhT/ExIV/xMSFf8TEhX/ExMV/xMTFf8UExX/FBMW/xQTFv8UExb/FBMW/xMSFP8MDA3/CwsM/woK
        C/8KCgv/CgkK/wkJCv8JCAn/CAgJ/wgICf8JCAr/ExMU/xUVF/8VFRf/FhUY/xYVGP8WFRj/FhUY/xYV
        GP8WFRj/FhUY/xYVGP8WFRj/FhUY/xYVGP8WFRj/FhUY/xYVGP8WFRj/FhUY/xYVGP8WFRj/FhUY/xYV
        GP8WFRj/FhUY/xUVF/8VFRf/FRUX/xUVF/8VFRf/FRQX/xUUF/8VFBf/FRQX/xUUF/8VFBf/FBQW/xQU
        Fv8UFBb/FBMW/xQTFv8UExb/FBMW/xQTFf8TExX/ExMV/xMSFf8TEhX/ExIV/xMSFP8SEhT/EhIU/xIS
        FP8SERT/EhET/xIRE/8RERP/ERET/xERE/8REBP/ERAS/xEQEv8QEBL/EBAS/xAQEv8QDxH/EA8R/w8P
        Ef8PDxHR////AP///wD///8A////AP///wD///8AY5f/fB9r//8fa///H2z//x9s//8fbf//H23//x9t
        //8fbv//H27//x9u//8fb///H2///x9v//8fb///H3D//x9w//8fcP//H3D//x9x//8fcf//H3H//x9x
        //8fcf//H3H//x9y//8fcv//H3L//x9y//8fcv//H3L//x9y//8fcv//H3L//x9y//8fcv//H3L//x9y
        //8fcv//H3L//x9x//8fcf/B////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////ABIRE88SERT/EhIU/xISFP8SEhT/ExIU/xMSFP8TEhX/ExIV/xMTFf8TExX/ExMV/xQT
        Ff8UExb/ExIV/w0MDv8MDA3/CwsM/wsLDP8LCgz/CgoL/woJC/8JCQr/CQkK/wkICf8ICAn/CQkK/xMS
        FP8VFRf/FRUX/xUVF/8VFRf/FRUX/xYVGP8WFRj/FhUY/xYVGP8WFRj/FhUY/xYVGP8WFRj/FhUY/xYV
        GP8WFRj/FhUY/xYVGP8WFRj/FRUX/xUVF/8VFRf/FRUX/xUVF/8VFRf/FRQX/xUUF/8VFBf/FRQX/xUU
        F/8VFBf/FRQX/xQUFv8UFBb/FBQW/xQUFv8UExb/FBMW/xQTFv8UExb/FBMV/xMTFf8TExX/ExIV/xMS
        Ff8TEhX/ExIU/xISFP8SEhT/EhIU/xIRFP8SERP/EhET/xERE/8RERP/ERET/xEQE/8REBL/ERAS/xAQ
        Ev8QEBL/EBAS/xAPEf8QDxH/Dw8R/w8PEf8PDxDRbm1vcv///wD///8A////AP///wD///8AH2r/wR9q
        //8fa///H2v//x9r//8fbP//H2z//x9t//8fbf//H23//x9u//8fbv//H27//x9u//8fb///H2///x9v
        //8fb///H3D//x9w//8fcP//H3D//x9w//8fcf//H3H//x9x//8fcf//H3H//x9x//8fcf//H3H//x9x
        //8fcf//H3H//x9x//8fcf//H3H//x9x//8fcf//H3H//x9x//8fcf/ButP/Jv///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////ABIRE88SERP/EhEU/xISFP8SEhT/EhIU/xMS
        FP8TEhT/ExIV/xMSFf8TExX/ExMV/xMTFf8TExX/Dg0P/w0NDv8MDA7/DAwN/wwLDf8LCwz/CwoM/woK
        C/8KCgv/CgkL/wkJCv8JCQr/CQkK/wsKC/8UExb/FRQX/xUUF/8VFRf/FRUX/xUVF/8VFRf/FRUX/xUV
        F/8VFRf/FRUX/xUVF/8VFRf/FRUX/xUVF/8VFRf/FRUX/xUVF/8VFRf/FRUX/xUVF/8VFRf/FRQX/xUU
        F/8VFBf/FRQX/xUUF/8VFBf/FRQX/xUUF/8UFBb/FBQW/xQUFv8UFBb/FBMW/xQTFv8UExb/FBMW/xQT
        Ff8TExX/ExMV/xMTFf8TEhX/ExIV/xMSFP8TEhT/EhIU/xISFP8SEhT/EhEU/xIRE/8SERP/ERET/xER
        E/8RERP/ERAT/xEQEv8REBL/EBAS/xAQEv8QEBL/EA8R/xAPEf8QDxH/Dw8R/w8PEf8PDhD/Dw4Q0f//
        /wD///8A////AP///wD///8AH2n/wR9p//8fav//H2r//x9r//8fa///H2z//x9s//8fbP//H23//x9t
        //8fbf//H23//x9u//8fbv//H27//x9u//8fb///H2///x9v//8fb///H2///x9w//8fcP//H3D//x9w
        //8fcP//H3D//x9w//8fcP//H3D//x9w//8fcP//H3D//x9w//8fcP//H3D//x9w//8fcP//H3D//x9w
        //8fcP//eKn/aP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A3t7fAhER
        E88SERP/EhET/xIRFP8SEhT/EhIU/xISFP8SEhT/ExIU/xMSFf8TEhX/ExIV/xMTFf8RERP/Dg0P/w0N
        D/8NDQ7/DQwO/wwMDf8MCw3/CwsM/wsLDP8LCgz/CgoL/woKC/8KCQv/CgkL/woJCv8REBL/FRQX/xUU
        F/8VFBf/FRQX/xUUF/8VFBf/FRQX/xUUF/8VFRf/FRUX/xUVF/8VFRf/FRUX/xUVF/8VFBf/FRQX/xUU
        F/8VFBf/FRQX/xUUF/8VFBf/FRQX/xUUF/8VFBf/FRQX/xUUF/8UFBb/FBQW/xQUFv8UFBb/FBQW/xQT
        Fv8UExb/FBMW/xQTFv8UExX/ExMV/xMTFf8TExX/ExIV/xMSFf8TEhX/ExIU/xMSFP8SEhT/EhIU/xIS
        FP8SERT/EhET/xIRE/8RERP/ERET/xERE/8REBP/ERAS/xEQEv8QEBL/EBAS/xAQEv8QDxH/EA8R/xAP
        Ef8PDxH/Dw8R/w8OEP8PDhD/Dw4Q0f///wD///8A////AP///wD///8AH2j/wR9p//8faf//H2r//x9q
        //8fav//H2v//x9r//8fbP//H2z//x9s//8fbP//H23//x9t//8fbf//H23//x9u//8fbv//H27//x9u
        //8fb///H2///x9v//8fb///H2///x9v//8fb///H2///x9w//8fcP//H3D//x9w//8fcP//H3D//x9w
        //8fcP//H3D//x9w//8fb///H2///x9v//8fb///H2//wf///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8AlpaXShERE/8RERP/EhET/xIRE/8SERP/EhIU/xISFP8SEhT/EhIU/xMS
        FP8TEhT/ExIV/xMSFf8REBP/Dg4Q/w4ND/8NDQ//DQ0O/w0MDv8MDA3/DAsN/wwLDf8LCwz/CwsM/wsK
        DP8LCgz/CgoL/woKC/8PDxH/FRQX/xUUF/8VFBf/FRQX/xUUF/8VFBf/FRQX/xUUF/8VFBf/FRQX/xUU
        F/8VFBf/FRQX/xUUF/8VFBf/FRQX/xUUF/8VFBf/FRQX/xUUF/8VFBf/FRQX/xUUF/8UFBb/FBQW/xQU
        Fv8UFBb/FBQW/xQTFv8UExb/FBMW/xQTFv8UExb/FBMV/xMTFf8TExX/ExMV/xMTFf8TEhX/ExIV/xMS
        Ff8TEhT/EhIU/xISFP8SEhT/EhIU/xIRFP8SERP/EhET/xERE/8RERP/ERET/xEQE/8REBL/ERAS/xAQ
        Ev8QEBL/EBAS/xAPEf8QDxH/EA8R/w8PEf8PDxH/Dw8Q/w8OEP8PDhD/Dg4Q0f///wD///8A////AP//
        /wD///8AH2f/wR9o//8faP//H2n//x9p//8fav//H2r//x9q//8fa///H2v//x9r//8fbP//H2z//x9s
        //8fbf//H23//x9t//8fbf//H23//x9u//8fbv//H27//x9u//8fbv//H27//x9v//8fb///H2///x9v
        //8fb///H2///x9v//8fb///H2///x9v//8fb///H2///x9v//8fb///H2///x9u//8fbv//H27/wf//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AlpaXShERE/8RERP/ERET/xIR
        E/8SERP/EhET/xIRFP8SEhT/EhIU/xISFP8TEhT/ExIU/xMSFf8RERP/Dw4Q/w4OEP8ODg//Dg0P/w0N
        Dv8NDA7/DAwO/wwMDf8MCw3/DAsN/wsLDP8LCwz/CwsM/wsLDP8QDxH/FBQW/xQUFv8UFBb/FBQW/xUU
        F/8VFBf/FRQX/xUUF/8VFBf/FRQX/xUUF/8VFBf/FRQX/xUUF/8VFBf/FRQX/xUUF/8VFBf/FRQX/xQU
        Fv8UFBb/FBQW/xQUFv8UFBb/FBQW/xQTFv8UExb/FBMW/xQTFv8UExb/FBMW/xQTFf8TExX/ExMV/xMT
        Ff8TExX/ExIV/xMSFf8TEhX/ExIU/xMSFP8SEhT/EhIU/xISFP8SERT/EhET/xIRE/8SERP/ERET/xER
        E/8RERP/ERAT/xEQEv8REBL/EBAS/xAQEv8QEBL/EA8R/xAPEf8QDxH/Dw8R/w8PEf8PDxD/Dw4Q/w8O
        EP8ODhD/Dg4Q0f///wD///8A////AP///wD///8AH2f/wR9n//8fZ///H2j//x9o//8faf//H2n//x9q
        //8fav//H2r//x9r//8fa///H2v//x9s//8fbP//H2z//x9s//8fbP//H23//x9t//8fbf//H23//x9t
        //8fbv//H27//x9u//8fbv//H27//x9u//8fbv//H27//x9u//8fbv//H27//x9u//8fbv//H27//x9u
        //8fbv//H27//x9u//8fbv//Rof/mf///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8Azs3OEhEQEs8RERP/ERET/xERE/8RERP/EhET/xIRE/8SERT/EhIU/xISFP8SEhT/EhIU/xMS
        FP8SERP/Dw8R/w8PEP8PDhD/Dg4Q/w4ND/8NDQ//DQ0O/w0MDv8MDA7/DAwN/wwMDf8MDA3/DAsN/wwL
        Df8SEhT/FBMW/xQUFv8UFBb/FBQW/xQUFv8UFBb/FBQW/xQUFv8UFBb/FBQW/xQUFv8UFBb/FBQW/xQU
        Fv8UFBb/FBQW/xQUFv8UFBb/FBQW/xQUFv8UFBb/FBMW/xQTFv8UExb/FBMW/xQTFv8UExb/FBMW/xQT
        Fv8UExX/ExMV/xMTFf8TExX/ExMV/xMSFf8TEhX/ExIV/xMSFP8TEhT/EhIU/xISFP8SEhT/EhIU/xIR
        FP8SERP/EhET/xIRE/8RERP/ERET/xERE/8REBP/ERAS/xEQEv8QEBL/EBAS/xAQEv8QDxH/EA8R/xAP
        Ef8PDxH/Dw8R/w8PEP8PDhD/Dw4Q/w4OEP8ODhD/Dg4Q0f///wD///8A////AP///wD///8AH2b/wR9m
        //8fZ///H2f//x9o//8faP//H2j//x9p//8faf//H2r//x9q//8fav//H2r//x9r//8fa///H2v//x9s
        //8fbP//H2z//x9s//8fbP//H2z//x9t//8fbf//H23//x9t//8fbf//H23//x9t//8fbf//H23//x9t
        //8fbf//H23//x9t//8fbf//H23//x9t//8fbf//H23//x9t//8fbf/Bnb//Qv///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////ABEQEs8REBL/ERAT/xERE/8RERP/ERET/xIR
        E/8SERP/EhET/xIRFP8SEhT/EhIU/xISFP8SEhT/ERAS/xAPEf8PDxH/Dw4Q/w4OEP8ODg//Dg0P/w0N
        D/8NDQ7/DQ0O/w0MDv8NDA7/DAwO/w8PEf8UExb/FBMW/xQTFv8UExb/FBMW/xQTFv8UExb/FBMW/xQU
        Fv8UFBb/FBQW/xQUFv8UFBb/FBQW/xQUFv8UFBb/FBMW/xQTFv8UExb/FBMW/xQTFv8UExb/FBMW/xQT
        Fv8UExb/FBMW/xQTFv8UExX/ExMV/xMTFf8TExX/ExMV/xMTFf8TEhX/ExIV/xMSFf8TEhT/ExIU/xMS
        FP8SEhT/EhIU/xISFP8SEhT/EhET/xIRE/8SERP/ERET/xERE/8RERP/ERET/xEQEv8REBL/ERAS/xAQ
        Ev8QEBL/EBAS/xAPEf8QDxH/EA8R/w8PEf8PDxH/Dw8Q/w8OEP8PDhD/Dw4Q/w4OEP8ODhD/Dg0P0///
        /wD///8A////AP///wD///8ARoD/mR9m//8fZv//H2b//x9n//8fZ///H2j//x9o//8faP//H2n//x9p
        //8faf//H2r//x9q//8fav//H2r//x9r//8fa///H2v//x9r//8fbP//H2z//x9s//8fbP//H2z//x9s
        //8fbP//H2z//x9t//8fbf//H23//x9t//8fbf//H23//x9t//8fbf//H23//x9s//8fbP//H2z//x9s
        //8fbP/B////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////ABAQ
        Es8REBL/ERAS/xEQE/8RERP/ERET/xERE/8RERP/EhET/xIRE/8SERT/EhIU/xISFP8SEhT/EhIU/xEQ
        Ev8QDxH/Dw8R/w8OEP8PDhD/Dg4Q/w4OD/8ODQ//Dg0P/w0ND/8NDQ//Dw8R/xQTFf8UExX/FBMW/xQT
        Fv8UExb/FBMW/xQTFv8UExb/FBMW/xQTFv8UExb/FBMW/xQTFv8UExb/FBMW/xQTFv8UExb/FBMW/xQT
        Fv8UExb/FBMW/xQTFv8UExb/FBMW/xQTFf8UExX/ExMV/xMTFf8TExX/ExMV/xMTFf8TEhX/ExIV/xMS
        Ff8TEhX/ExIU/xMSFP8TEhT/EhIU/xISFP8SEhT/EhIU/xIRFP8SERP/EhET/xIRE/8RERP/ERET/xER
        E/8REBP/ERAS/xEQEv8REBL/EBAS/xAQEv8QEBL/EA8R/xAPEf8QDxH/Dw8R/w8PEf8PDxD/Dw4Q/w8O
        EP8PDhD/Dg4Q/w4OEP8ODQ//ODc4qf///wD///8A////AP///wD///8AtMv/LB9l/8EfZf//H2b//x9m
        //8fZv//H2f//x9n//8fZ///H2j//x9o//8faP//H2n//x9p//8faf//H2r//x9q//8fav//H2r//x9q
        //8fa///H2v//x9r//8fa///H2v//x9s//8fbP//H2z//x9s//8fbP//H2z//x9s//8fbP//H2z//x9s
        //8fbP//H2z//x9s//8fbP//H2z//x9r//8fa//B////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////ADk5O6cQEBLPERAS/xEQEv8REBP/ERET/xERE/8RERP/ERET/xIR
        E/8SERP/EhET/xIRFP8SEhT/EhIU/xISFP8RERP/EA8R/xAPEf8PDxH/Dw4Q/w8OEP8ODhD/Dg4Q/w4O
        D/8QDxH/ExMV/xMTFf8TExX/ExMV/xMTFf8TExX/FBMV/xQTFf8UExX/FBMW/xQTFv8UExb/FBMW/xQT
        Fv8UExb/FBMW/xQTFv8UExb/FBMW/xQTFf8UExX/FBMV/xQTFf8TExX/ExMV/xMTFf8TExX/ExMV/xMT
        Ff8TEhX/ExIV/xMSFf8TEhX/ExIV/xMSFP8TEhT/EhIU/xISFP8SEhT/EhIU/xISFP8SERT/EhET/xIR
        E/8SERP/ERET/xERE/8RERP/ERET/xEQE/8REBL/ERAS/xAQEv8QEBL/EBAS/xAQEv8QDxH/EA8R/xAP
        Ef8PDxH/Dw8R/w8PEP8PDhD/Dw4Q/w8OEP8ODhD/Dg4Q/w4ND/8ODQ/T3t7eAv///wD///8A////AP//
        /wD///8A////AB9k/8EfZP//H2X//x9l//8fZv//H2b//x9m//8fZ///H2f//x9n//8faP//H2j//x9o
        //8faP//H2n//x9p//8faf//H2r//x9q//8fav//H2r//x9q//8fav//H2r//x9r//8fa///H2v//x9r
        //8fa///H2v//x9r//8fa///H2v//x9r//8fa///H2v//x9r//8fa///H2v//x9r/8Gdvv9C////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wAQEBLPEBAS/xEQ
        Ev8REBL/ERAS/xEQE/8RERP/ERET/xERE/8RERP/EhET/xIRE/8SERP/EhEU/xISFP8SEhT/ERET/xAQ
        Ev8QDxH/EA8R/w8PEf8PDxD/Dw4Q/xAQEv8TEhX/ExIV/xMSFf8TEhX/ExMV/xMTFf8TExX/ExMV/xMT
        Ff8TExX/ExMV/xMTFf8TExX/ExMV/xMTFf8TExX/ExMV/xMTFf8TExX/ExMV/xMTFf8TExX/ExMV/xMT
        Ff8TExX/ExMV/xMSFf8TEhX/ExIV/xMSFf8TEhX/ExIV/xMSFP8TEhT/ExIU/xISFP8SEhT/EhIU/xIS
        FP8SEhT/EhEU/xIRE/8SERP/EhET/xIRE/8RERP/ERET/xERE/8REBP/ERAS/xEQEv8REBL/EBAS/xAQ
        Ev8QEBL/EA8R/xAPEf8QDxH/EA8R/w8PEf8PDxH/Dw8Q/w8OEP8PDhD/Dw4Q/w4OEP8ODhD/Dg4P/w4N
        D/8ODQ/T////AP///wD///8A////AP///wD///8A////ALTL/ywfZP/BH2T//x9k//8fZf//H2X//x9m
        //8fZv//H2b//x9m//8fZ///H2f//x9n//8faP//H2j//x9o//8faP//H2n//x9p//8faf//H2n//x9p
        //8fav//H2r//x9q//8fav//H2r//x9q//8fav//H2r//x9q//8fav//H2r//x9q//8fav//H2r//x9q
        //8fav//H2r/wR9q/8H///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wA5OTunEBASzxAQEv8QEBL/ERAS/xEQEv8REBP/ERET/xERE/8RERP/ERET/xER
        E/8SERP/EhET/xIRE/8SERT/EhIU/xERE/8REBL/EBAS/xAQEv8QDxH/ERAS/xMSFP8TEhT/ExIU/xMS
        Ff8TEhX/ExIV/xMSFf8TEhX/ExIV/xMSFf8TEhX/ExMV/xMTFf8TExX/ExMV/xMTFc0TExXNExMVzRMT
        Fc0TExXNExMVzRMSFc0TEhXNExIVzRMSFc0TEhXNExIVzRMSFc0TEhXNExIVzRMSFM0TEhTNExIU/xIS
        FP8SEhT/EhIU/xISFP8SEhT/EhIU/xIRFP8SERP/EhET/xIRE/8SERP/ERET/xERE/8RERP/ERET/xEQ
        E/8REBL/ERAS/xEQEv8QEBL/EBAS/xAQEv8QDxH/EA8R/xAPEf8PDxH/Dw8R/w8PEf8PDxD/Dw4Q/w8O
        EP8PDhD/Dg4Q/w4OEP8ODg//Dg0P/w4ND9Pe3t4C////AP///wD///8A////AP///wD///8A////AP//
        /wBjkv98H2P/wR9k//8fZP//H2T//x9l//8fZf//H2X//x9m//8fZv//H2b//x9m//8fZ///H2f//x9n
        //8fZ///H2j//x9o//8faP//H2j//x9o//8faf//H2n//x9p//8faf//H2n//x9p//8faf//H2n//x9p
        //8faf//H2n//x9p//8faf//H2n//x9p//8faf//H2n/wf///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AEA8R0RAQEs8QEBL/EBAS/xEQ
        Ev8REBL/ERAS/xEQE/8RERP/ERET/xERE/8RERP/ERET/xIRE/8SERP/EhET/xIRFP8SERT/EhEU/xIS
        FP8SEhT/EhIU/xISFP8SEhT/ExIU/xMSFP8TEhT/ExIU/xMSFP8TEhX/ExIV/xMSFf8TEhX/ExIV/xMS
        Ff8TEhX/ExIV/xMSFc3///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wDAwMAgEhIUzRISFP8SEhT/EhIU/xISFP8SERT/EhET/xIRE/8SERP/EhET/xER
        E/8RERP/ERET/xERE/8REBP/ERAS/xEQEv8REBL/EBAS/xAQEv8QEBL/EBAS/xAPEf8QDxH/EA8R/w8P
        Ef8PDxH/Dw8R/w8OEP8PDhD/Dw4Q/w4OEP8ODhD/Dg4Q/w4OD/8ODQ//Dg0P021tbnL///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8ALW3/sx9j/8EfY///H2P//x9k//8fZP//H2T//x9l
        //8fZf//H2X//x9m//8fZv//H2b//x9m//8fZ///H2f//x9n//8fZ///H2f//x9o//8faP//H2j//x9o
        //8faP//H2j//x9o//8faP//H2j//x9o//8faP//H2j//x9o//8faP//H2j//x9o//8faP/B0OD/EP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////ABAPEdEQEBLPEBAS/xAQEv8QEBL/ERAS/xEQEv8REBL/ERAT/xERE/8RERP/ERET/xER
        E/8RERP/EhET/xIRE/8SERP/EhET/xIRFP8SERT/EhIU/xISFP8SEhT/EhIU/xISFP8SEhT/EhIU/xIS
        FP8TEhT/ExIU/xMSFP8TEhT/ExIU/xMSFP8TEhT/ExIUzdzc3AT///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AnZ2eQhISFM0SERT/EhET/xIR
        E/8SERP/EhET/xIRE/8RERP/ERET/xERE/8RERP/ERAT/xEQEv8REBL/ERAS/xAQEv8QEBL/EBAS/xAQ
        Ev8QDxH/EA8R/xAPEf8QDxH/Dw8R/w8PEf8PDxD/Dw4Q/w8OEP8PDhD/Dg4Q/w4OEP8ODhD/Dg0P/w4N
        D/8ODQ/THBwew////wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AC1t
        /7MfY//BH2P//x9j//8fY///H2T//x9k//8fZP//H2X//x9l//8fZf//H2X//x9m//8fZv//H2b//x9m
        //8fZv//H2b//x9n//8fZ///H2f//x9n//8fZ///H2f//x9n//8fZ///H2f//x9n//8fZ///H2f//x9n
        //8fZ///H2f//x9n/8HQ3/8Q////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wAQDxHREA8R0RAQEv8QEBL/EBAS/xAQ
        Ev8REBL/ERAS/xEQEv8REBP/ERET/xERE/8RERP/ERET/xERE/8SERP/EhET/xIRE/8SERP/EhET/xIR
        FP8SERT/EhIU/xISFP8SEhT/EhIU/xISFP8SEhT/EhIU/xISFP8SEhT/EhIU/xISFP8SEhTN3NzcBP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AJ2dnkISERPPEhET/xIRE/8RERP/ERET/xERE/8RERP/ERET/xEQE/8REBL/ERAS/xEQ
        Ev8REBL/EBAS/xAQEv8QEBL/EBAS/xAPEf8QDxH/EA8R/w8PEf8PDxH/Dw8R/w8PEP8PDhD/Dw4Q/w8O
        EP8ODhD/Dg4Q/w4OEP8ODQ//Dg0P/w4ND9McHB7D////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wAtbf+zH2P/wR9j//8fY///H2P//x9j//8fY///H2T//x9k
        //8fZP//H2T//x9l//8fZf//H2X//x9l//8fZv//H2b//x9m//8fZv//H2b//x9m//8fZv//H2b//x9m
        //8fZv//H2b//x9n//8fZ///H2f//x9n//8fZv//H2b/wdDf/xD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8AEA8R0RAPEdEQEBL/EBAS/xAQEv8QEBL/ERAS/xEQEv8REBL/ERAS/xEQE/8RERP/ERET/xER
        E/8RERP/ERET/xERE/8SERP/EhET/xIRE/8SERP/EhET/xIRE/8SERT/EhEU/xIRFP8SEhT/EhIU/xIS
        FP8SEhT/EhIU/xISFM3c3NwE////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wCdnZ5CERETzxERE/8RERP/ERET/xER
        E/8REBP/ERAS/xEQEv8REBL/ERAS/xAQEv8QEBL/EBAS/xAQEv8QDxH/EA8R/xAPEf8QDxH/Dw8R/w8P
        Ef8PDxH/Dw8Q/w8OEP8PDhD/Dw4Q/w4OEP8ODhD/Dg4P/w4ND/8ODQ//Dg0P0xwcHsP///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8ALW3/sx9j
        /8EfY///H2P//x9j//8fY///H2P//x9j//8fY///H2T//x9k//8fZP//H2T//x9k//8fZf//H2X//x9l
        //8fZf//H2X//x9l//8fZv//H2b//x9m//8fZv//H2b//x9m//8fZv//H2b//x9m//8fZv/B0N//EP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////ABAPEdEQDxHREA8R/xAQEv8QEBL/EBAS/xAQ
        Ev8REBL/ERAS/xEQEv8REBL/ERAT/xEQE/8RERP/ERET/xERE/8RERP/ERET/xERE/8RERP/EhET/xIR
        E/8SERP/EhET/xIRE/8SERP/EhET/xIRE/8SERT/EhEUz9zc3AT///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8AnZ2eQhERE88REBP/ERAT/xEQEv8REBL/ERAS/xEQEv8QEBL/EBAS/xAQEv8QEBL/EA8R/xAP
        Ef8QDxH/EA8R/w8PEf8PDxH/Dw8R/w8PEP8PDhD/Dw4Q/w8OEP8PDhD/Dg4Q/w4OEP8ODg//Dg0P/w4N
        D/8ODQ/THBwew////wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AC1t/7MfY//BH2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j
        //8fY///H2P//x9k//8fZP//H2T//x9k//8fZP//H2T//x9k//8fZf//H2X//x9l//8fZf//H2X//x9l
        //8fZf//H2X//x9l/8HQ3/8Q////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wAQDxHREA8R0RAPEf8QDxH/EBAS/xAQEv8QEBL/EBAS/xAQEv8REBL/ERAS/xEQEv8REBL/ERAT/xEQ
        E/8RERP/ERET/xERE/8RERP/ERET/xERE/8RERP/ERET/xIRE/8SERP/EhET/xIRE/8SERPP3NzcBP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AJ2dnkIREBLPERAS/xEQEv8REBL/EBAS/xAQ
        Ev8QEBL/EBAS/xAPEf8QDxH/EA8R/xAPEf8QDxH/Dw8R/w8PEf8PDxH/Dw8Q/w8OEP8PDhD/Dw4Q/w4O
        EP8ODhD/Dg4Q/w4OD/8ODQ//Dg0P/w4ND9McHB7D////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wAtbf+zH2P/wR9j
        //8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9k
        //8fZP//H2T//x9k//8fZP//H2T//x9k//8fZP//H2T/wdDf/xD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8AEA8R0RAPEdEQDxH/EA8R/xAPEf8QEBL/EBAS/xAQ
        Ev8QEBL/EBAS/xEQEv8REBL/ERAS/xEQEv8REBL/ERAT/xEQE/8RERP/ERET/xERE/8RERP/ERET/xER
        E/8RERP/ERET/xERE8/c3NwE////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wCdnZ1CEBASzxAQEv8QEBL/EBAS/xAQEv8QDxH/EA8R/xAPEf8QDxH/EA8R/w8PEf8PDxH/Dw8R/w8P
        EP8PDhD/Dw4Q/w8OEP8PDhD/Dg4Q/w4OEP8ODg//Dg0P/w4ND/8ODQ//Dg0P0xwcHsP///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8ALW3/sx9j/8EfY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j
        //8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY//B0N//EP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AA8P
        EdEQDxHREA8R/xAPEf8QDxH/EA8R/xAQEv8QEBL/EBAS/xAQEv8QEBL/EBAS/xEQEv8REBL/ERAS/xEQ
        Ev8REBL/ERAS/xEQE/8REBP/ERAT/xERE/8RERP/ERETz9zc3AT///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8AnZ2dQhAQEs8QEBL/EA8R/xAPEf8QDxH/EA8R/xAP
        Ef8PDxH/Dw8R/w8PEf8PDxD/Dw4Q/w8OEP8PDhD/Dw4Q/w4OEP8ODhD/Dg4Q/w4OD/8ODQ//Dg0P/w4N
        D/8NDQ/THBwew////wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AC1t/7MfY//BH2P//x9j
        //8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j
        //8fY///H2P//x9j/8HQ3/8Q////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wAPDxHRDw8R0RAPEf8QDxH/EA8R/xAPEf8QDxH/EA8R/xAQ
        Ev8QEBL/EBAS/xAQEv8QEBL/EBAS/xAQEv8REBL/ERAS/xEQEv8REBL/ERAS/xEQEv8REBLP3NvcBP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AJ2c
        nUQQDxHREA8R/xAPEf8QDxH/Dw8R/w8PEf8PDxH/Dw8Q/w8PEP8PDhD/Dw4Q/w8OEP8PDhD/Dg4Q/w4O
        EP8ODhD/Dg0P/w4ND/8ODQ//Dg0P/w0ND9McHB7D////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wAtbf+zH2P/wR9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j
        //8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P/wdDf/xD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8ADw8R0Q8P
        EdEPDxH/Dw8R/xAPEf8QDxH/EA8R/xAPEf8QDxH/EA8R/xAQEv8QEBL/EBAS/xAQEv8QEBL/EBAS/xAQ
        Ev8QEBL/ERAS/xEQEs/c29wE////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wCdnJ1EEA8R0Q8PEf8PDxH/Dw8R/w8PEP8PDxD/Dw4Q/w8O
        EP8PDhD/Dw4Q/w4OEP8ODhD/Dg4Q/w4OD/8ODQ//Dg0P/w4ND/8ODQ//DQ0P0xwcHcP///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8ALW3/sx9j/8EfY///H2P//x9j
        //8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY//B0N//EP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AA8PENEPDxHRDw8R/w8PEf8PDxH/EA8R/xAPEf8QDxH/EA8R/xAP
        Ef8QDxH/EA8R/xAQEv8QEBL/EBAS/xAQEv8QEBL/EBASz9vb3AT///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8AnJydRA8P
        EdEPDxD/Dw4Q/w8OEP8PDhD/Dw4Q/w8OEP8ODhD/Dg4Q/w4OEP8ODg//Dg0P/w4ND/8ODQ//Dg0P/w0N
        D/8NDQ/THBwdw////wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AC1t/7MfY//BH2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j
        //8fY///H2P/wR9j/8HQ3/8Q////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wAPDhDRDw8Q0Q8P
        EP8PDxH/Dw8R/w8PEf8PDxH/EA8R/xAPEf8QDxH/EA8R/xAPEf8QDxH/EA8R/xAPEdEQDxHR29vcBP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AJycnUQPDhDRDw4Q/w8OEP8PDhD/Dg4Q/w4OEP8ODhD/Dg4P/w4O
        D/8ODQ//Dg0P/w4ND/8ODQ//DQ0P/w0NDtMcHB3D////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wB5ov9mH2P/wR9j//8fY///H2P//x9j
        //8fY///H2P//x9j//8fY///H2P//x9j//8fY//BH2P/wf///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8AHh0fww8OENEPDhDRDw8Q/w8PEP8PDxH/Dw8R/w8PEf8PDxH/Dw8R/xAP
        Ef8QDxH/EA8R0Tk4Oqf///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wC/v78gDw4Q0Q4O
        ENEODhD/Dg4Q/w4OEP8ODg//Dg0P/w4ND/8ODQ//Dg0P/w0ND/8NDQ/TDQ0O05WVlUr///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A3uj/AkZ+//8fY///H2P//x9j//8fY///H2P//x9j//8fY///H2P//x9j///Q3/8Q////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wAeHR/DDw4Q/w8O
        EP8PDhD/Dw8Q/w8PEP8PDxH/Dw8R/w8PEf8eHiD/v7/AIP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AJycnUQODg//Dg4P/w4ND/8ODQ//Dg0P/w4ND/8NDQ//DQ0P/w0N
        Dv83Nzip////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP///wD///8A////AP//
        /wD///8A////AP///wD///8A////AP///wD/////////////////////////8P//////////////////
        ///////w///gP///////wP///////gP///D//wAP//////4AH//////4AH//8P/+AAP//////AAP////
        /+AAP//w//wAAf/////wAAP/////wAAf//D/+AAA/////+AAAf////+AAA//8P/wAAB/////wAAA////
        /wAAB//w/+AAAD////+AAAB////+AAAD//D/wAAAH////wAAAD////wAAAH/8P+AAAAP///+AAAAH///
        +AAAAP/w/wAAAAf///wAAAAP///wAAAAf/D+AAAAA///+AAAAAf//+AAAAA/8PwAAAAB///wAAAAA///
        wAAAAB/w+AAAAAD//+AAAAAB//+AAAAAD/DwAAAAAH//wAAAAAD//wAAAAAH8OAAAAAAP//AAAAAAP/+
        AAAAAAPwwAAAAAAAAAAAAAAAf/4AAAAAAfDAAAAAAAAAAAAAAAB//AAAAAAB8IAAAAAAAAAAAAAAAD/8
        AAAAAADwgAAAAAAAAAAAAAAAP/gAAAAAAPCAAAAAAAAAAAAAAAA/+AAAAAAA8IAAAAAAAAAAAAAAAD/4
        AAAAAADwgAAAAAAAAAAAAAAAP/gAAAAAAPCAAAAAAAAAAAAAAAA/+AAAAAAA8IAAAAAAAAAAAAAAAD/4
        AAAAAADwgAAAAAAAAAAAAAAAP/gAAAAAAPCAAAAAAAAAAAAAAAA//AAAAAAA8IAAAAAAAAAAAAAAAD/8
        AAAAAAHwwAAAAAAAAAAAAAAAf/4AAAAAAfDgAAAAAD//gAAAAAD//gAAAAAD8OAAAAAAf//AAAAAAP//
        AAAAAAfw8AAAAAD//+AAAAAB//+AAAAAB/D4AAAAAf//8AAAAAP//8AAAAAP8PwAAAAD///4AAAAB///
        4AAAAB/w/gAAAAf///wAAAAP///wAAAAP/D/AAAAD////gAAAB////gAAAB/8P+AAAAf////AAAAP///
        /AAAAP/w/8AAAD////+AAAB////+AAAB//D/4AAAf////8AAAP////8AAAP/8P/wAAD/////4AAB////
        /4AAB//w//gAAf/////wAAP/////wAAP//D//AAD//////gAB//////gAD//8P/+AAf//////AAP////
        //AAP//w//4AB//////+AA//////8AB///D//gAH//////4AD//////wAH//8P/+AAf//////gAP////
        //AAf//w//4AB//////+AA//////8AB///D//gAH//////4AD//////wAH//8P/+AAf//////gAP////
        //AAf//w//4AB//////+AA//////8AB///D//gAH//////4AD//////wAH//8P/+AAf//////gAP////
        //AAf//w//4AB//////+AA//////8AB///D//gAH//////4AD//////wAH//8P/+AAf//////AAP////
        //AAf//w//wAA//////4AAf/////4AA///D/+AAB//////AAA//////AAA//8P/wAAD/////4AAB////
        /4AAB//w/+AAAH/////AAAD/////AAAD//D/wAAAP////4AAAH////4AAAH/8P+AAAAf////AAAAP///
        /AAAAP/w/wAAAA////4AAAAf///4AAAAf/D+AAAAB////AAAAA////AAAAA/8PwAAAAD///4AAAAB///
        4AAAAB/w+AAAAAH///AAAAAD///AAAAAD/DwAAAAAP//4AAAAAH//4AAAAAH8OAAAAAAf//AAAAAAP//
        AAAAAAfw4AAAAAA//8AAAAAA//4AAAAAA/DAAAAAAD//gAAAAAB//gAAAAAB8IAAAAAAH/8AAAAAAD/8
        AAAAAAHwgAAAAAAf/wAAAAAAP/wAAAAAAPCAAAAAAA//AAAAAAA/+AAAAAAA8IAAAAAAD/8AAAAAAD/4
        AAAAAADwgAAAAAAP/gAAAAAAP/gAAAAAAPCAAAAAAA/+AAAAAAA/+AAAAAAA8IAAAAAAD/4AAAAAAD/4
        AAAAAADwgAAAAAAP/wAAAAAAP/gAAAAAAPCAAAAAAB//AAAAAAA//AAAAAAA8IAAAAAAH/8AAAAAAD/8
        AAAAAADwwAAAAAAf/wAAAAAAf/wAAAAAAfDAAAAAAD//gAAAAAB//gAAAAAB8OAAAAAAP//AAAAAAP/+
        AAAAAAPw4AAAAAB//8AAAAAA//8AAAAAB/DwAAAAAP//4AAAAAH//4AAAAAP8PgAAAAB///wAAAAA///
        wAAAAB/w/AAAAAP///gAAAAH///gAAAAP/D+AAAAB////AAAAA////AAAAB/8P8AAAAP///+AAAAH///
        +AAAAP/w/4AAAB////8AAAA////8AAAB//D/wAAAP////4AAAH////4AAAP/8P/gAAB/////wAAA////
        /wAAB//w//AAAP/////gAAH/////gAAP//D/+AAB//////AAA//////AAB//8P/+AAP//////AAP////
        /+AAP//w//4AB//////+AB//////8AB///D//gAH///////A///////wAH//8P/+AAf/////////////
        //AAf//w//4AB///////////////8AB///D//gAH///////////////wAH//8P/+AAf/////////////
        //AAf//w//4AB///////////////8AB///D//gAH///////////////wAH//8P/+AAf/////////////
        //AAf//w//4AB///////AD//////8AB///D//gAH//////wAD//////wAD//8P/8AAP/////+AAH////
        /+AAH//w//gAAf/////wAAP/////wAAP//D/8AAA/////+AAAf////+AAAf/8P/gAAB/////wAAA////
        /wAAA//w/8AAAD////+AAAB////+AAAB//D/gAAAH////wAAAD////wAAAD/8P8AAAAP///+AAAAH///
        +AAAAH/w/gAAAAf///wAAAAP///wAAAAP/D8AAAAA///+AAAAAf//+AAAAAf8PgAAAAB///wAAAAA///
        wAAAAA/w8AAAAAD//+AAAAAB//+AAAAAB/DgAAAAAH//wAAAAAD//wAAAAAD8MAAAAAAP/+AAAAAAAAA
        AAAAAAPwwAAAAAAf/4AAAAAAAAAAAAAAAfCAAAAAAB//AAAAAAAAAAAAAAAB8IAAAAAAH/8AAAAAAAAA
        AAAAAADwgAAAAAAP/wAAAAAAAAAAAAAAAPCAAAAAAA/+AAAAAAAAAAAAAAAA8IAAAAAAD/4AAAAAAAAA
        AAAAAADwgAAAAAAP/gAAAAAAAAAAAAAAAPCAAAAAAA/+AAAAAAAAAAAAAAAA8IAAAAAAD/8AAAAAAAAA
        AAAAAADwgAAAAAAf/wAAAAAAAAAAAAAAAPCAAAAAAB//AAAAAAAAAAAAAAAA8MAAAAAAH/+AAAAAAAAA
        AAAAAAHwwAAAAAA//4AAAAAAAAAAAAAAAfDgAAAAAH//wAAAAAD//gAAAAAD8PAAAAAAf//gAAAAAP//
        AAAAAAfw+AAAAAD///AAAAAB//+AAAAAD/D8AAAAAf//+AAAAAP//8AAAAAf8P4AAAAD///8AAAAB///
        4AAAAD/w/wAAAAf///4AAAAP///wAAAAf/D/gAAAD////wAAAB////gAAAD/8P/AAAAf////gAAAP///
        /AAAAf/w/+AAAD/////AAAB////+AAAD//D/8AAAf////+AAAP////8AAAf/8P/4AAD/////8AAB////
        /4AAD//w//wAAf/////4AAP/////wAAf//D//gAH//////wAD//////gAD//8P//AA///////wAf////
        //gA///w
</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>فاتورة مردود مبيعات</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="repositoryItemTextEdit1.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
</root>