﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Reports
{
    public static class ExtensionsHelper
    {
        public static int RowHandle(this DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            return e.Column.View.GetRowHandle(e.ListSourceRowIndex);
        }

        public static int RowHandle(this DevExpress.XtraGrid.Views.Base.CustomColumnDisplayTextEventArgs e)
        {
            return e.Column.View.GetRowHandle(e.ListSourceRowIndex);
        }
        
    }
}
