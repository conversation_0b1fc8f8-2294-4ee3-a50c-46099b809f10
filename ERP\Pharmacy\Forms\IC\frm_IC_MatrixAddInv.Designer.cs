﻿namespace Pharmacy.Forms
{
    partial class frm_IC_MatrixAddInv
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_IC_MatrixAddInv));
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject1 = new DevExpress.Utils.SerializableAppearanceObject();
            this.repositoryItemLookUpEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.rep_attr = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.pivotGridControl1 = new DevExpress.XtraPivotGrid.PivotGridControl();
            this.col_ItemId = new DevExpress.XtraPivotGrid.PivotGridField();
            this.col_MtrxAttribute1 = new DevExpress.XtraPivotGrid.PivotGridField();
            this.col_MtrxAttribute2 = new DevExpress.XtraPivotGrid.PivotGridField();
            this.col_MtrxAttribute3 = new DevExpress.XtraPivotGrid.PivotGridField();
            this.col_Qty = new DevExpress.XtraPivotGrid.PivotGridField();
            this.repositoryItemSpinEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.col_MtrxAttribute1_Id = new DevExpress.XtraPivotGrid.PivotGridField();
            this.col_MtrxAttribute2_Id = new DevExpress.XtraPivotGrid.PivotGridField();
            this.col_MtrxAttribute3_Id = new DevExpress.XtraPivotGrid.PivotGridField();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.lkpItems = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl15 = new DevExpress.XtraEditors.LabelControl();
            this.btAdd = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemLookUpEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_attr)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pivotGridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSpinEdit1)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lkpItems.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // repositoryItemLookUpEdit1
            // 
            resources.ApplyResources(this.repositoryItemLookUpEdit1, "repositoryItemLookUpEdit1");
            this.repositoryItemLookUpEdit1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.repositoryItemLookUpEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repositoryItemLookUpEdit1.Buttons"))))});
            this.repositoryItemLookUpEdit1.Name = "repositoryItemLookUpEdit1";
            // 
            // rep_attr
            // 
            resources.ApplyResources(this.rep_attr, "rep_attr");
            this.rep_attr.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.rep_attr.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_attr.Buttons"))))});
            this.rep_attr.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_attr.Columns"), resources.GetString("rep_attr.Columns1")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_attr.Columns2"), resources.GetString("rep_attr.Columns3"))});
            this.rep_attr.DisplayMember = "MDName";
            this.rep_attr.Name = "rep_attr";
            this.rep_attr.ValueMember = "MatrixDetailId";
            // 
            // pivotGridControl1
            // 
            resources.ApplyResources(this.pivotGridControl1, "pivotGridControl1");
            this.pivotGridControl1.Fields.AddRange(new DevExpress.XtraPivotGrid.PivotGridField[] {
            this.col_ItemId,
            this.col_MtrxAttribute1,
            this.col_MtrxAttribute2,
            this.col_MtrxAttribute3,
            this.col_Qty,
            this.col_MtrxAttribute1_Id,
            this.col_MtrxAttribute2_Id,
            this.col_MtrxAttribute3_Id});
            this.pivotGridControl1.Name = "pivotGridControl1";
            this.pivotGridControl1.OptionsCustomization.AllowDrag = false;
            this.pivotGridControl1.OptionsCustomization.AllowDragInCustomizationForm = false;
            this.pivotGridControl1.OptionsCustomization.AllowExpand = false;
            this.pivotGridControl1.OptionsCustomization.AllowFilter = false;
            this.pivotGridControl1.OptionsCustomization.AllowHideFields = DevExpress.XtraPivotGrid.AllowHideFieldsType.Never;
            this.pivotGridControl1.OptionsCustomization.AllowPrefilter = false;
            this.pivotGridControl1.OptionsCustomization.AllowSort = false;
            this.pivotGridControl1.OptionsDataField.Caption = resources.GetString("pivotGridControl1.OptionsDataField.Caption");
            this.pivotGridControl1.OptionsFilterPopup.AllowContextMenu = false;
            this.pivotGridControl1.OptionsSelection.EnableAppearanceFocusedCell = true;
            this.pivotGridControl1.OptionsSelection.MultiSelect = false;
            this.pivotGridControl1.OptionsView.ShowColumnGrandTotalHeader = false;
            this.pivotGridControl1.OptionsView.ShowColumnGrandTotals = false;
            this.pivotGridControl1.OptionsView.ShowColumnTotals = false;
            this.pivotGridControl1.OptionsView.ShowRowGrandTotalHeader = false;
            this.pivotGridControl1.OptionsView.ShowRowGrandTotals = false;
            this.pivotGridControl1.OptionsView.ShowRowTotals = false;
            this.pivotGridControl1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemSpinEdit1});
            this.pivotGridControl1.EditValueChanged += new DevExpress.XtraPivotGrid.EditValueChangedEventHandler(this.pivotGridControl1_EditValueChanged);
            // 
            // col_ItemId
            // 
            this.col_ItemId.Area = DevExpress.XtraPivotGrid.PivotArea.RowArea;
            this.col_ItemId.AreaIndex = 1;
            resources.ApplyResources(this.col_ItemId, "col_ItemId");
            this.col_ItemId.FieldEdit = this.repositoryItemLookUpEdit1;
            this.col_ItemId.FieldName = "ItemId";
            this.col_ItemId.Name = "col_ItemId";
            this.col_ItemId.Visible = false;
            // 
            // col_MtrxAttribute1
            // 
            this.col_MtrxAttribute1.Area = DevExpress.XtraPivotGrid.PivotArea.RowArea;
            this.col_MtrxAttribute1.AreaIndex = 0;
            resources.ApplyResources(this.col_MtrxAttribute1, "col_MtrxAttribute1");
            this.col_MtrxAttribute1.FieldEdit = this.rep_attr;
            this.col_MtrxAttribute1.FieldName = "MtrxAttribute1";
            this.col_MtrxAttribute1.Name = "col_MtrxAttribute1";
            // 
            // col_MtrxAttribute2
            // 
            this.col_MtrxAttribute2.Area = DevExpress.XtraPivotGrid.PivotArea.ColumnArea;
            this.col_MtrxAttribute2.AreaIndex = 0;
            resources.ApplyResources(this.col_MtrxAttribute2, "col_MtrxAttribute2");
            this.col_MtrxAttribute2.FieldEdit = this.rep_attr;
            this.col_MtrxAttribute2.FieldName = "MtrxAttribute2";
            this.col_MtrxAttribute2.Name = "col_MtrxAttribute2";
            // 
            // col_MtrxAttribute3
            // 
            this.col_MtrxAttribute3.Area = DevExpress.XtraPivotGrid.PivotArea.ColumnArea;
            this.col_MtrxAttribute3.AreaIndex = 1;
            resources.ApplyResources(this.col_MtrxAttribute3, "col_MtrxAttribute3");
            this.col_MtrxAttribute3.FieldEdit = this.rep_attr;
            this.col_MtrxAttribute3.FieldName = "MtrxAttribute3";
            this.col_MtrxAttribute3.Name = "col_MtrxAttribute3";
            // 
            // col_Qty
            // 
            this.col_Qty.Area = DevExpress.XtraPivotGrid.PivotArea.DataArea;
            this.col_Qty.AreaIndex = 0;
            resources.ApplyResources(this.col_Qty, "col_Qty");
            this.col_Qty.CellFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_Qty.FieldEdit = this.repositoryItemSpinEdit1;
            this.col_Qty.FieldName = "Qty";
            this.col_Qty.Name = "col_Qty";
            // 
            // repositoryItemSpinEdit1
            // 
            this.repositoryItemSpinEdit1.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            resources.ApplyResources(this.repositoryItemSpinEdit1, "repositoryItemSpinEdit1");
            this.repositoryItemSpinEdit1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.repositoryItemSpinEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repositoryItemSpinEdit1.Buttons"))), resources.GetString("repositoryItemSpinEdit1.Buttons1"), ((int)(resources.GetObject("repositoryItemSpinEdit1.Buttons2"))), ((bool)(resources.GetObject("repositoryItemSpinEdit1.Buttons3"))), ((bool)(resources.GetObject("repositoryItemSpinEdit1.Buttons4"))), ((bool)(resources.GetObject("repositoryItemSpinEdit1.Buttons5"))), ((DevExpress.XtraEditors.ImageLocation)(resources.GetObject("repositoryItemSpinEdit1.Buttons6"))), ((System.Drawing.Image)(resources.GetObject("repositoryItemSpinEdit1.Buttons7"))), new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject1, resources.GetString("repositoryItemSpinEdit1.Buttons8"), ((object)(resources.GetObject("repositoryItemSpinEdit1.Buttons9"))), ((DevExpress.Utils.SuperToolTip)(resources.GetObject("repositoryItemSpinEdit1.Buttons10"))), ((bool)(resources.GetObject("repositoryItemSpinEdit1.Buttons11"))))});
            this.repositoryItemSpinEdit1.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.repositoryItemSpinEdit1.Increment = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.repositoryItemSpinEdit1.MaxValue = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.repositoryItemSpinEdit1.Name = "repositoryItemSpinEdit1";
            this.repositoryItemSpinEdit1.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.repositoryItemSpinEdit1_Spin);
            // 
            // col_MtrxAttribute1_Id
            // 
            this.col_MtrxAttribute1_Id.Area = DevExpress.XtraPivotGrid.PivotArea.ColumnArea;
            this.col_MtrxAttribute1_Id.AreaIndex = 1;
            resources.ApplyResources(this.col_MtrxAttribute1_Id, "col_MtrxAttribute1_Id");
            this.col_MtrxAttribute1_Id.FieldName = "MtrxAttribute1_Id";
            this.col_MtrxAttribute1_Id.Name = "col_MtrxAttribute1_Id";
            this.col_MtrxAttribute1_Id.Visible = false;
            // 
            // col_MtrxAttribute2_Id
            // 
            this.col_MtrxAttribute2_Id.Area = DevExpress.XtraPivotGrid.PivotArea.ColumnArea;
            this.col_MtrxAttribute2_Id.AreaIndex = 1;
            resources.ApplyResources(this.col_MtrxAttribute2_Id, "col_MtrxAttribute2_Id");
            this.col_MtrxAttribute2_Id.FieldName = "MtrxAttribute2_Id";
            this.col_MtrxAttribute2_Id.Name = "col_MtrxAttribute2_Id";
            this.col_MtrxAttribute2_Id.Visible = false;
            // 
            // col_MtrxAttribute3_Id
            // 
            this.col_MtrxAttribute3_Id.Area = DevExpress.XtraPivotGrid.PivotArea.ColumnArea;
            this.col_MtrxAttribute3_Id.AreaIndex = 1;
            resources.ApplyResources(this.col_MtrxAttribute3_Id, "col_MtrxAttribute3_Id");
            this.col_MtrxAttribute3_Id.FieldName = "MtrxAttribute3_Id";
            this.col_MtrxAttribute3_Id.Name = "col_MtrxAttribute3_Id";
            this.col_MtrxAttribute3_Id.Visible = false;
            // 
            // groupBox1
            // 
            resources.ApplyResources(this.groupBox1, "groupBox1");
            this.groupBox1.Controls.Add(this.lkpItems);
            this.groupBox1.Controls.Add(this.labelControl15);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.TabStop = false;
            // 
            // lkpItems
            // 
            resources.ApplyResources(this.lkpItems, "lkpItems");
            this.lkpItems.EnterMoveNextControl = true;
            this.lkpItems.Name = "lkpItems";
            this.lkpItems.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkpItems.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpItems.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpItems.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpItems.Properties.Buttons"))))});
            this.lkpItems.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpItems.Properties.Columns"), resources.GetString("lkpItems.Properties.Columns1"))});
            this.lkpItems.Properties.DisplayMember = "ItemNameAr";
            this.lkpItems.Properties.NullText = resources.GetString("lkpItems.Properties.NullText");
            this.lkpItems.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.lkpItems.Properties.ValueMember = "ItemId";
            this.lkpItems.EditValueChanged += new System.EventHandler(this.lkpItems_EditValueChanged);
            // 
            // labelControl15
            // 
            resources.ApplyResources(this.labelControl15, "labelControl15");
            this.labelControl15.Name = "labelControl15";
            // 
            // btAdd
            // 
            resources.ApplyResources(this.btAdd, "btAdd");
            this.btAdd.Name = "btAdd";
            this.btAdd.Click += new System.EventHandler(this.btAdd_Click);
            // 
            // frm_IC_MatrixAddInv
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.btAdd);
            this.Controls.Add(this.pivotGridControl1);
            this.Name = "frm_IC_MatrixAddInv";
            this.Load += new System.EventHandler(this.frm_IC_MatrixAddInv_Load);
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemLookUpEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_attr)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pivotGridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSpinEdit1)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lkpItems.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraPivotGrid.PivotGridControl pivotGridControl1;
        private DevExpress.XtraPivotGrid.PivotGridField col_ItemId;
        private DevExpress.XtraPivotGrid.PivotGridField col_MtrxAttribute1;
        private DevExpress.XtraPivotGrid.PivotGridField col_MtrxAttribute2;
        private DevExpress.XtraPivotGrid.PivotGridField col_MtrxAttribute3;
        private DevExpress.XtraPivotGrid.PivotGridField col_Qty;
        private DevExpress.XtraPivotGrid.PivotGridField col_MtrxAttribute1_Id;
        private DevExpress.XtraPivotGrid.PivotGridField col_MtrxAttribute2_Id;
        private DevExpress.XtraPivotGrid.PivotGridField col_MtrxAttribute3_Id;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repositoryItemLookUpEdit1;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit rep_attr;
        private System.Windows.Forms.GroupBox groupBox1;
        private DevExpress.XtraEditors.SimpleButton btAdd;
        private DevExpress.XtraEditors.LabelControl labelControl15;
        private DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit repositoryItemSpinEdit1;
        private DevExpress.XtraEditors.LookUpEdit lkpItems;

    }
}