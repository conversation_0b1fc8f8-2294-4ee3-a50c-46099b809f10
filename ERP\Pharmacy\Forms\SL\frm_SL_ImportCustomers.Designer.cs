﻿namespace Pharmacy.Forms
{
    partial class btnJournal
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(btnJournal));
            this.btn_OpenCustomerfile = new DevExpress.XtraEditors.SimpleButton();
            this.progressBar1 = new System.Windows.Forms.ProgressBar();
            this.btnOpenVendorFile = new DevExpress.XtraEditors.SimpleButton();
            this.btnImportItems = new DevExpress.XtraEditors.SimpleButton();
            this.barbtnOpenBalance = new DevExpress.XtraEditors.SimpleButton();
            this.simpleButton1 = new DevExpress.XtraEditors.SimpleButton();
            this.btn_OpenBalance = new DevExpress.XtraEditors.SimpleButton();
            this.SuspendLayout();
            // 
            // btn_OpenCustomerfile
            // 
            resources.ApplyResources(this.btn_OpenCustomerfile, "btn_OpenCustomerfile");
            this.btn_OpenCustomerfile.Name = "btn_OpenCustomerfile";
            this.btn_OpenCustomerfile.Click += new System.EventHandler(this.btn_Openfile_Click);
            // 
            // progressBar1
            // 
            resources.ApplyResources(this.progressBar1, "progressBar1");
            this.progressBar1.Name = "progressBar1";
            this.progressBar1.Step = 1;
            // 
            // btnOpenVendorFile
            // 
            resources.ApplyResources(this.btnOpenVendorFile, "btnOpenVendorFile");
            this.btnOpenVendorFile.Name = "btnOpenVendorFile";
            this.btnOpenVendorFile.Click += new System.EventHandler(this.btnOpenVendorFile_Click);
            // 
            // btnImportItems
            // 
            resources.ApplyResources(this.btnImportItems, "btnImportItems");
            this.btnImportItems.Name = "btnImportItems";
            this.btnImportItems.Click += new System.EventHandler(this.btnImportItems_Click);
            // 
            // barbtnOpenBalance
            // 
            resources.ApplyResources(this.barbtnOpenBalance, "barbtnOpenBalance");
            this.barbtnOpenBalance.Name = "barbtnOpenBalance";
            this.barbtnOpenBalance.Click += new System.EventHandler(this.barbtnOpenBalance_Click);
            // 
            // simpleButton1
            // 
            resources.ApplyResources(this.simpleButton1, "simpleButton1");
            this.simpleButton1.Name = "simpleButton1";
            this.simpleButton1.Click += new System.EventHandler(this.simpleButton1_Click);
            // 
            // btn_OpenBalance
            // 
            resources.ApplyResources(this.btn_OpenBalance, "btn_OpenBalance");
            this.btn_OpenBalance.Name = "btn_OpenBalance";
            this.btn_OpenBalance.Click += new System.EventHandler(this.btn_OpenBalance_Click);
            // 
            // btnJournal
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.btn_OpenBalance);
            this.Controls.Add(this.simpleButton1);
            this.Controls.Add(this.barbtnOpenBalance);
            this.Controls.Add(this.btnImportItems);
            this.Controls.Add(this.btnOpenVendorFile);
            this.Controls.Add(this.progressBar1);
            this.Controls.Add(this.btn_OpenCustomerfile);
            this.Name = "btnJournal";
            this.Load += new System.EventHandler(this.frm_SL_ImportCustomers_Load);
            this.ResumeLayout(false);

        }

        #endregion
        private DevExpress.XtraEditors.SimpleButton btn_OpenCustomerfile;
        private System.Windows.Forms.ProgressBar progressBar1;
        private DevExpress.XtraEditors.SimpleButton btnOpenVendorFile;
        private DevExpress.XtraEditors.SimpleButton btnImportItems;
        private DevExpress.XtraEditors.SimpleButton barbtnOpenBalance;
        private DevExpress.XtraEditors.SimpleButton simpleButton1;
        private DevExpress.XtraEditors.SimpleButton btn_OpenBalance;
    }
}