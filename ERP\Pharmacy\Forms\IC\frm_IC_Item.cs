﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DevExpress.XtraGrid.Views.Grid;
using System.Collections;
using DevExpress.XtraEditors.DXErrorProvider;
using DevExpress.XtraEditors.Controls;
using DevExpress.Utils;
using DAL;
using DAL.Res;
using System.Data.Linq;
using DevExpress.XtraPrinting;
using DevExpress.XtraReports.UI;
using DevExpress.XtraGrid.Views.Base;

namespace Pharmacy.Forms
{
    public partial class frm_IC_Item : DevExpress.XtraEditors.XtraForm
    {
        int ItemID;
        bool LoadItemDataCompleted;
        FormAction action = FormAction.None;
        List<IC_InternationalCode> NewInternationalCodes = new List<IC_InternationalCode>();
        UserPriv prvlg;
        bool DataModified;

        DataTable dt_PriceList = new DataTable();
        DataTable dtUOM = new DataTable();
        List<DAL.IC_UOM> uom_list;
        List<IC_Category> lstCategories;
        string picPath;

        ERPDataContext DdBind = new ERPDataContext();
        ERPDataContext DB_Location = new ERPDataContext();

        DataTable dt_SubTax = new DataTable();
        public frm_IC_Item(int itemID, FormAction action)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            ItemID = itemID;
            this.action = action;

            chk_IsLibra.Visible = chk_PricingWithSmall.Visible = chk_VariableWeight.Visible = Shared.LibraAvailabe;
        }

        private void frm_IC_Item_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            tab_Matrix.PageVisible = false;

            #region Available License Modules
            if (!Shared.MaufacturingAvailable)
                rdoItemType.Properties.Items[2].Enabled = false;

            if (!Shared.POSAvailable)
                chk_IsPos.Visible = false;

            if (!Shared.ItemMatrixAvailable)
                rdoItemType.Properties.Items[3].Enabled = false;

            if (!Shared.PriceListAvailable)
                tab_PriceLevels.PageVisible = false;

            if (Shared.TaxAvailable == false)
                tabTax.PageVisible = false;
            #endregion


            if (Shared.st_Store.ExpireDate == false)
                chkIsExpire.Enabled = false;

            //UOMs
            if (Shared.st_Store.UseMediumUom == false)
                lkpMediumUOM.Enabled = txtMediumUOMFactor.Enabled = txtMediumUOMPrice.Enabled =
                    rdoSellUOM1.Enabled = rdoPrchsUOM1.Enabled= false;
            if (Shared.st_Store.UseLargeUom == false)
                lkpLargeUOM.Enabled = txtLargeUOMFactor.Enabled = txtLargeUOMPrice.Enabled =
                    rdoSellUOM1.Enabled = rdoPrchsUOM1.Enabled = false;

            if (Shared.user.SellPriceUponQtyAvailable == false)
                tab_PricesPerQty.PageVisible = false;

            if (Shared.st_Store.UseHeightDimension == false &&
                Shared.st_Store.UseWidthDimension == false &&
                Shared.st_Store.UseLengthDimension == false)
            {
                tabDimension.PageVisible =
                colHeight.Visible = colHeight.OptionsColumn.ShowInCustomizationForm =
                    colWidth.Visible = colWidth.OptionsColumn.ShowInCustomizationForm =
                    colLength.Visible = colLength.OptionsColumn.ShowInCustomizationForm = false;
            }
            else
            {
                if (Shared.st_Store.UseHeightDimension == false)
                {
                    txtHeight.Enabled = false;
                    colHeight.Visible = colHeight.OptionsColumn.ShowInCustomizationForm = false;
                }
                if (Shared.st_Store.UseWidthDimension == false)
                {
                    txtWidth.Enabled = false;
                    colWidth.Visible = colWidth.OptionsColumn.ShowInCustomizationForm = false;
                }
                if (Shared.st_Store.UseLengthDimension == false)
                {
                    txtLength.Enabled = false;
                    colLength.Visible = colLength.OptionsColumn.ShowInCustomizationForm = false;
                }
            }

            LoadPrivilege();
            BindDataSources();

            GetItemData();

            // Adel: Select weight measurement unit
            lbl_WeightUnit.Visible = cmb_WeightUnit.Visible = Shared.LibraAvailabe;
            // Adel: Disable editing on price level
            gridView7.OptionsBehavior.ReadOnly = Shared.LibraAvailabe;
            labelControl11.Text = Shared.IsEnglish ? Shared.st_Store.CategoryEn : Shared.st_Store.CategoryAr;
            labelControl6.Text = Shared.IsEnglish ? Shared.st_Store.CompanyEn : Shared.st_Store.CompanyAr;

            ErpUtils.Load_Grid_Layout(grd_Mtrx, this.Name.Replace("frm_", "") + "Mtrx");
            ErpUtils.ColumnChooser(grd_Mtrx);
            ErpUtils.ColumnChooser(grd_SalesPerQty);
            ErpUtils.ColumnChooser(grd_Vendors);
            ErpUtils.ColumnChooser(grdQty);
            ErpUtils.ColumnChooser(grdSlPLevel);
            ErpUtils.ColumnChooser(grdPrPLevel);
        }


        private void frm_IC_Item_KeyUp(object sender, KeyEventArgs e)

        {
            if (e.KeyCode == Keys.PageUp)
            {
                btnPrev.PerformClick();
            }
            if (e.KeyCode == Keys.PageDown)
            {
                btnNext.PerformClick();
            }
        }

        private void frm_IC_Item_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                e.Cancel = true;

            ErpUtils.save_Grid_Layout(grd_Mtrx, this.Name.Replace("frm_", "") + "Mtrx", true);

        }

        private void btnPrev_Click(object sender, EventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            ERPDataContext DB = new ERPDataContext();

            int lastItemId = (from x in DB.IC_Items
                              where x.ItemId < ItemID
                              where x.ItemType != (int)ItemType.MatrixDetail
                              orderby x.ItemId descending
                              select x.ItemId).FirstOrDefault();

            if (lastItemId != 0)
            {
                ItemID = lastItemId;
                GetItemData();
            }
            else
            {
                lastItemId = (from x in DB.IC_Items
                              where x.ItemType != (int)ItemType.MatrixDetail
                              orderby x.ItemId ascending
                              select x.ItemId).FirstOrDefault();

                if (lastItemId != 0)
                {
                    ItemID = lastItemId;
                    GetItemData();
                }
            }
        }

        private void btnNext_Click(object sender, EventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            ERPDataContext DB = new ERPDataContext();

            int lastItemId = (from x in DB.IC_Items
                              where x.ItemId > ItemID
                              where x.ItemType != (int)ItemType.MatrixDetail
                              orderby x.ItemId ascending
                              select x.ItemId).FirstOrDefault();

            if (lastItemId != 0)
            {
                ItemID = lastItemId;
                GetItemData();
            }
            else
            {
                lastItemId = (from x in DB.IC_Items
                              where x.ItemType != (int)ItemType.MatrixDetail
                              orderby x.ItemId ascending
                              select x.ItemId).FirstOrDefault();

                if (lastItemId != 0)
                {
                    ItemID = lastItemId;
                    GetItemData();
                }
            }
        }


        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtnList_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_IC_ItemsList)))
            {
                frm_IC_ItemsList frm = new frm_IC_ItemsList();
                frm.BringToFront();
                frm.Show();
            }
            else
                Application.OpenForms["frm_IC_ItemsList"].BringToFront();
        }

        private void barBtnDelete_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (action == FormAction.Add)
                return;

            if (ItemID > 0)
            {
                DAL.ERPDataContext DB = new DAL.ERPDataContext();
                if (XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgDelItem : ResICAr.MsgDelItem//"هل أنت متأكد أنك تريد حذف هذا الصنف"
                    , "", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2)
                    == DialogResult.Yes)
                {
                    //mahmoud: check if this item is in a price list.
                    //var priceList = DB.IC_PriceLevelDetails.Where(i => i.ItemId == ItemID).Count();
                    //if (priceList > 0)
                    //{
                    //    XtraMessageBox.Show(
                    //        Shared.IsEnglish == true ? ResICEn.MsgDelItemDenied3 : ResICAr.MsgDelItemDenied3//"لايمكن حذف الصنف لأنه موجود في قائمة أسعار"
                    //        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    //    return;
                    //}

                    ////mahmoud: check if this item is in Bill of Materials of other item.
                    //var bom = DB.IC_BOMDetails.Where(i => i.RawItemId == ItemID).Count();
                    //if (bom > 0)
                    //{
                    //    XtraMessageBox.Show(
                    //        Shared.IsEnglish == true ? ResICEn.MsgDelItemDenied1 : ResICAr.MsgDelItemDenied1//"لايمكن حذف الصنف لأنه موجود في قائمة مواد خام أصناف اخري "
                    //        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    //    return;
                    //}

                    //check if there is some of it in store
                    //var ItemStore = (from i in DB.IC_ItemStores
                    //                 where i.ItemId == ItemID
                    //                 select i.ItemId).Count();

                    //if (ItemStore > 0)
                    //{
                    //    XtraMessageBox.Show(
                    //        Shared.IsEnglish == true ? ResICEn.MsgDelItemDenied2 : ResICAr.MsgDelItemDenied2//"لايمكن حذف الصنف, يوجد كميات منه بالمخازن"
                    //        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    //    return;
                    //}

                    //deleting serive item constraints
                    //manufacturing
                    //if (DB.ManfDetails.Where
                    //    (i => i.ItemId == ItemID).Count() > 0)
                    //{
                    //    XtraMessageBox.Show(
                    //    Shared.IsEnglish == true ? ResICEn.MsgDelItemDenied4 : ResICAr.MsgDelItemDenied4//"لايمكن حذف الصنف, الصنف مسجل بفواتير سابقة"
                    //    , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    //    return;
                    //}

                    ////Purchase
                    //if (DB.PR_InvoiceDetails.Where
                    //    (i => i.ItemId == ItemID).Count() > 0)
                    //{
                    //    XtraMessageBox.Show(
                    //    Shared.IsEnglish == true ? ResICEn.MsgDelItemDenied4 : ResICAr.MsgDelItemDenied4//"لايمكن حذف الصنف, الصنف مسجل بفواتير سابقة"
                    //    , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    //    return;
                    //}
                    //if (DB.PR_ReturnDetails.Where
                    //    (i => i.ItemId == ItemID).Count() > 0)
                    //{
                    //    XtraMessageBox.Show(
                    //    Shared.IsEnglish == true ? ResICEn.MsgDelItemDenied4 : ResICAr.MsgDelItemDenied4//"لايمكن حذف الصنف, الصنف مسجل بفواتير سابقة"
                    //    , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    //    return;
                    //}
                    //if (DB.PR_PurchaseOrderDetails.Where
                    //    (i => i.ItemId == ItemID).Count() > 0)
                    //{
                    //    XtraMessageBox.Show(
                    //    Shared.IsEnglish == true ? ResICEn.MsgDelItemDenied4 : ResICAr.MsgDelItemDenied4//"لايمكن حذف الصنف, الصنف مسجل بفواتير سابقة"
                    //    , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    //    return;
                    //}


                    //Sell
                    if (DB.SL_InvoiceDetails.Where
                        (i => i.ItemId == ItemID).Count() > 0)
                    {
                        XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgDelItemDenied4 : ResICAr.MsgDelItemDenied4//"لايمكن حذف الصنف, الصنف مسجل بفواتير سابقة"
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }
                    if (DB.SL_ReturnDetails.Where
                        (i => i.ItemId == ItemID).Count() > 0)
                    {
                        XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgDelItemDenied4 : ResICAr.MsgDelItemDenied4//"لايمكن حذف الصنف, الصنف مسجل بفواتير سابقة"
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    //if (DB.SL_QuoteDetails.Where
                    //    (i => i.ItemId == ItemID).Count() > 0)
                    //{
                    //    XtraMessageBox.Show(
                    //    Shared.IsEnglish == true ? ResICEn.MsgDelItemDenied4 : ResICAr.MsgDelItemDenied4//"لايمكن حذف الصنف, الصنف مسجل بفواتير سابقة"
                    //    , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    //    return;
                    //}
                    //if (DB.SL_SalesOrderDetails.Where
                    //      (i => i.ItemId == ItemID).Count() > 0)
                    //{
                    //    XtraMessageBox.Show(
                    //    Shared.IsEnglish == true ? ResICEn.MsgDelItemDenied4 : ResICAr.MsgDelItemDenied4//"لايمكن حذف الصنف, الصنف مسجل بفواتير سابقة"
                    //    , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    //    return;
                    //}

                    //matrix
                    if (Convert.ToInt32(rdoItemType.EditValue) == (int)ItemType.MatrixParent)
                    {
                        bool itemsUsed = false;
                        var MtrxItems = DB.IC_Items.Where(i => i.mtrxParentItem == ItemID).ToList();

                        foreach (var Mitem in MtrxItems)
                        {
                            //check if there is some of it in store
                            var MItemStore = (from i in DB.SL_InvoiceDetails
                                              where i.ItemId == Mitem.ItemId
                                              select i.ItemId).Count();

                            if (MItemStore > 0)
                            {
                                itemsUsed = true;
                                break;
                            }
                        }

                        if (itemsUsed)
                        {
                            XtraMessageBox.Show(
                                Shared.IsEnglish == true ? ResICEn.MsgMtrxDelDenied : ResICAr.MsgMtrxDelDenied
                                , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            return;
                        }
                    }

                    //var mr_grpItems = (from a in DB.Mr_CustGrpItems
                    //                   where a.ItemId == ItemID
                    //                   select a.ItemId).Count();
                    //if (mr_grpItems > 0)
                    //{
                    //    XtraMessageBox.Show(
                    //        Shared.IsEnglish == true ? ResICEn.DelItemOnMr : ResICAr.DelItemOnMr,
                    //        Shared.IsEnglish == true ? ResEn.MsgTInfo : ResAr.MsgTInfo,
                    //        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    //    return;
                    //}

                    var item = (from b in DB.IC_Items
                                where b.ItemId == ItemID
                                select b).Single();

                    //var intCodes = DB.IC_InternationalCodes.Where(i => i.ItemId == ItemID);
                    //var matrixItems = DB.IC_Items.Where(i => i.mtrxParentItem == ItemID);
                    //var matrixDetails = DB.IC_ItemMatrixDetails.Where(i => i.ItemId == ItemID);
                    //var vendorsPrices = DB.IC_ItemVendors.Where(i => i.ItemId == ItemID);


                    #region item prices per qty
                    //DdBind.IC_ItemPricesPerQties.DeleteAllOnSubmit(
                    //DdBind.IC_ItemPricesPerQties.Where(b => b.ItemId == ItemID));
                    //DdBind.SubmitChanges();
                    #endregion

                    //DB.IC_InternationalCodes.DeleteAllOnSubmit(intCodes);
                    //DB.SubmitChanges();

                    //var ids = DB.IC_BOMs.Where(s => s.ProductItemId == ItemID).Select(s => s.BOMId).ToList();
                    //var bom_details = DB.IC_BOMDetails.Where(s => ids.Contains(s.BOMId));
                    //DB.IC_BOMDetails.DeleteAllOnSubmit(bom_details);
                    //DB.IC_BOMs.DeleteAllOnSubmit(DB.IC_BOMs.Where(i => i.ProductItemId == ItemID));
                    //DB.SubmitChanges();

                    //DB.IC_ItemMatrixDetails.DeleteAllOnSubmit(matrixDetails);
                    //DB.IC_Items.DeleteAllOnSubmit(matrixItems);
                    //DB.SubmitChanges();

                    //DB.IC_ItemVendors.DeleteAllOnSubmit(vendorsPrices);
                    //DB.SubmitChanges();

                    DB.IC_Items.DeleteOnSubmit(item);

                    MyHelper.UpdateST_UserLog(DB, txtItemCode1.Text, txtItemNameAr.Text,
                        (int)FormAction.Delete, (int)FormsNames.Item);

                    DB.SubmitChanges();

                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgDel : ResICAr.MsgDel//"تم الحذف بنجاح"
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    NewItem();
                }
            }
        }

        private void barBtnNew_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            NewItem();
        }

        private void barBtn_Save_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            (gridLocation.FocusedView as GridView).FocusedRowHandle++;

            if (!ValidData())
                return;

            SaveData();
        }


        private void btn_AddNewCategory_Click(object sender, EventArgs e)
        {
            int count = (lkpCategory.Properties.DataSource as IList).Count;
            int item_index = lkpCategory.ItemIndex;
            new frm_IC_Category().ShowDialog();

            lstCategories = MyHelper.GetChildCategoriesList();
            lkpCategory.Properties.DataSource = lstCategories;

            if ((lkpCategory.Properties.DataSource as IList).Count > count)
                lkpCategory.ItemIndex = (lkpCategory.Properties.DataSource as IList).Count - 1;
            else
                lkpCategory.ItemIndex = item_index;
        }

        private void btn_AddNewCompany_Click(object sender, EventArgs e)
        {
            int count = (lkpComp.Properties.DataSource as IList).Count;
            int item_index = lkpComp.ItemIndex;
            new frm_IC_Company(0, FormAction.Add).ShowDialog();

            lkpComp.Properties.DataSource = new ERPDataContext().IC_Companies.ToList();

            if ((lkpComp.Properties.DataSource as IList).Count > count)
                lkpComp.ItemIndex = (lkpComp.Properties.DataSource as IList).Count - 1;
            else
                lkpComp.ItemIndex = item_index;
        }

        private void rdoItemType_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.PageDown || e.KeyCode == Keys.PageUp)
                e.Handled = true;
        }

        private void btn_AddNewInter_Code_Click(object sender, EventArgs e)
        {
            txtInternationalCode.Properties.ReadOnly = false;
            txtInternationalCode.Focus();
        }

        private void btnDeleteInternationalCode_Click(object sender, EventArgs e)
        {
            if (lstInternationalCodes.Items.Count > 0)
            {
                DialogResult dr;
                if (lstInternationalCodes.Items.Count > 1)
                    dr = XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgDelBarcode : ResICAr.MsgDelBarcode//"هل تريد بالغعل حذف هذا الباركود الدولي"
                        , "", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning);
                else
                    dr = XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgDelBarcode2 : ResICAr.MsgDelBarcode2//"هل تريد بالغعل حذف آخر باركود دولي"
                        , "", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning);
                if (dr == DialogResult.OK)
                {
                    ERPDataContext pharm = new ERPDataContext();
                    var InternationalCode = (from ic in pharm.IC_InternationalCodes
                                             where ic.InternationalCode == lstInternationalCodes.SelectedItem.ToString()
                                             select ic).FirstOrDefault();
                    if (InternationalCode != null)
                    {
                        pharm.IC_InternationalCodes.DeleteOnSubmit(InternationalCode);
                        pharm.SubmitChanges();
                    }
                    lstInternationalCodes.Items.Remove(lstInternationalCodes.SelectedItem);
                }
            }
        }


        private void txtItemNameAr_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == Convert.ToChar(Keys.Enter))
                txtItemNameEn.Focus();
        }

        private void txtPurchasePrice_EditValueChanging(object sender, ChangingEventArgs e)
        {
            var value = ((TextEdit)sender).EditValue;
            try
            {
                if (Convert.ToDecimal(e.NewValue) < 0)
                    e.Cancel = true;
            }
            catch { }
        }

        private void txtInternationalCode_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyValue.ToString() == "13")
            {
                ValidateIntCode();
            }
        }

        private void txtNumberOnly_KeyPress(object sender, KeyPressEventArgs e)
        {
            //accepts only integer values           
            if (char.IsNumber(e.KeyChar) || e.KeyChar == '.')
            {
            }
            else
            {
                e.Handled = e.KeyChar != (char)Keys.Back;
            }
        }

        private void txtUOM_factor_KeyPress(object sender, KeyPressEventArgs e)
        {
            //accepts only 0,1,2..9 and . and /
            if (char.IsNumber(e.KeyChar) || e.KeyChar == '.' || e.KeyChar == '/')
            {
            }
            else
            {
                e.Handled = e.KeyChar != (char)Keys.Back;
            }
        }

        private void txtItemCode1_Leave(object sender, EventArgs e)
        {
            if (ItemID == 0)//New Item
            {
                var item = (from i in new DAL.ERPDataContext().IC_Items
                            where i.ItemCode1 == Convert.ToInt32(txtItemCode1.Text)
                            select i).SingleOrDefault();
                if (item != null)
                {
                    XtraMessageBox.Show(
                      Shared.IsEnglish == true ? ResICEn.MsgCodeExist : ResICAr.MsgCodeExist//"يوجد صنف لديه نفس الكود"
                      , "", MessageBoxButtons.OK, MessageBoxIcon.Warning); txtItemCode1.Focus();
                }
            }
        }

        private void Editors_Modified(object sender, EventArgs e)
        {
            DataModified = true;
        }


        private void lkpUOMs_EditValueChanging(object sender, ChangingEventArgs e)
        {
            //mahmoud: user cannot use the same uom twice.
            if (e.NewValue != null &&
                (Convert.ToInt32(e.NewValue) == Convert.ToInt32(lkpMediumUOM.EditValue) ||
                 Convert.ToInt32(e.NewValue) == Convert.ToInt32(lkpLargeUOM.EditValue) ||
                 Convert.ToInt32(e.NewValue) == Convert.ToInt32(lkpSmallUOM.EditValue)))
            {
                e.Cancel = true;
                return;
            }

            if (LoadItemDataCompleted == false)
                return;

            //when user editing item he cannot change uom used before.
            if (action == FormAction.Edit)
            {
                //user change this uom
                if (e.OldValue != null && e.OldValue != e.NewValue)
                {
                    ERPDataContext DB = new ERPDataContext();

                    //if (DB.IC_OpenBalances.Where
                    //     (i => i.ItemId == ItemID && i.UOMId == Convert.ToInt32(e.OldValue)).Count() > 0)
                    //{
                    //    e.Cancel = true;
                    //    return;
                    //}

                    ////price levels
                    //if (((LookUpEdit)sender).Name == "lkpMediumUOM")
                    //{
                    //    if (DB.IC_PriceLevelDetails.Where
                    //     (i => i.ItemId == ItemID && i.MediumUOMPrice != null).Count() > 0)
                    //    {
                    //        e.Cancel = true;
                    //        return;
                    //    }
                    //}

                    //if (((LookUpEdit)sender).Name == "lkpLargeUOM")
                    //{
                    //    if (DB.IC_PriceLevelDetails.Where
                    //     (i => i.ItemId == ItemID && i.LargeUOMPrice != null).Count() > 0)
                    //    {
                    //        e.Cancel = true;
                    //        return;
                    //    }
                    //}

                    ////Manufacturing
                    //if (DB.IC_BOMDetails.Where
                    //    (i => i.RawItemId == ItemID && i.UomId == Convert.ToInt32(e.OldValue)).Count() > 0)
                    //{
                    //    e.Cancel = true;
                    //    return;
                    //}
                    //if (DB.ManfDetails.Where
                    //    (i => i.ItemId == ItemID && i.UOMId == Convert.ToInt32(e.OldValue)).Count() > 0)
                    //{
                    //    e.Cancel = true;
                    //    return;
                    //}

                    //Purchase
                    //if (DB.PR_InvoiceDetails.Where
                    //    (i => i.ItemId == ItemID && i.UOMId == Convert.ToInt32(e.OldValue)).Count() > 0)
                    //{
                    //    e.Cancel = true;
                    //    return;
                    //}

                    //Sell
                    if (DB.SL_InvoiceDetails.Where
                        (i => i.ItemId == ItemID && i.UOMId == Convert.ToInt32(e.OldValue)).Count() > 0)
                    {
                        e.Cancel = true;
                        return;
                    }

                    //if (DB.PR_ReturnDetails.Where
                    //    (i => i.ItemId == ItemID && i.UOMId == Convert.ToInt32(e.OldValue)).Count() > 0)
                    //{
                    //    e.Cancel = true;
                    //    return;
                    //}

                    if (DB.SL_ReturnDetails.Where
                        (i => i.ItemId == ItemID && i.UOMId == Convert.ToInt32(e.OldValue)).Count() > 0)
                    {
                        e.Cancel = true;
                        return;
                    }
                    //if (DB.SL_QuoteDetails.Where
                    //    (i => i.ItemId == ItemID && i.UOMId == Convert.ToInt32(e.OldValue)).Count() > 0)
                    //{
                    //    e.Cancel = true;
                    //    return;
                    //}
                    //if (DB.SL_SalesOrderDetails.Where
                    //    (i => i.ItemId == ItemID && i.UOMId == Convert.ToInt32(e.OldValue)).Count() > 0)
                    //{
                    //    e.Cancel = true;
                    //    return;
                    //}


                    ////Ic
                    //if (DB.IC_InTrnsDetails.Where
                    //    (i => i.ItemId == ItemID && i.UOMId == Convert.ToInt32(e.OldValue)).Count() > 0)
                    //{
                    //    e.Cancel = true;
                    //    return;
                    //}
                    //if (DB.IC_OutTrnsDetails.Where
                    //    (i => i.ItemId == ItemID && i.UOMId == Convert.ToInt32(e.OldValue)).Count() > 0)
                    //{
                    //    e.Cancel = true;
                    //    return;
                    //}
                    //if (DB.IC_DamagedDetails.Where
                    //    (i => i.ItemId == ItemID && i.UOMId == Convert.ToInt32(e.OldValue)).Count() > 0)
                    //{
                    //    e.Cancel = true;
                    //    return;
                    //}
                    //if (DB.IC_StoreMoveDetails.Where
                    //   (i => i.ItemId == ItemID && i.UOMId == Convert.ToInt32(e.OldValue)).Count() > 0)
                    //{
                    //    e.Cancel = true;
                    //    return;
                    //}
                    //if (DB.IC_ItemQtyChanges.Where
                    //   (i => i.ItemId == ItemID && i.UOM == Convert.ToInt32(e.OldValue)).Count() > 0)
                    //{
                    //    e.Cancel = true;
                    //    return;
                    //}
                    //if (DB.IC_StockTaking_Details.Where
                    //   (i => i.ItemID == ItemID && i.UOM == Convert.ToInt32(e.OldValue)).Count() > 0)
                    //{
                    //    e.Cancel = true;
                    //    return;
                    //}
                }
            }
        }

        private void lkpMediumUOM_EditValueChanged(object sender, EventArgs e)
        {
            if (lkpMediumUOM.EditValue != null && Convert.ToInt32(lkpMediumUOM.EditValue) != 0)
            {
                txtMediumUOMFactor.Enabled = true;
                txtMediumUOMPrice.Enabled = true;
                rdoSellUOM1.Enabled = rdoPrchsUOM1.Enabled = txt_MediumUOMCode.Enabled = true;
                chk_MediumIsStopped.Enabled = true;
               
            }
            else
            {
                txtMediumUOMFactor.Enabled = false;
                txtMediumUOMPrice.Enabled = false;
                rdoSellUOM1.Enabled = rdoPrchsUOM1.Enabled = txt_MediumUOMCode.Enabled = false;

                txtMediumUOMFactor.Text = txtMediumUOMPrice.Text = string.Empty;
                txt_MediumUOMCode.EditValue = null;
                rdoSellUOM1.Checked = rdoPrchsUOM1.Checked = false;
                rdoSellUOM0.Checked = rdoPrchsUOM0.Checked = true;
                chk_MediumIsStopped.Enabled = false;
                chk_MediumIsStopped.EditValue = null;
            }
        }

        private void lkpLargeUOM_EditValueChanged(object sender, EventArgs e)
        {
            if (lkpLargeUOM.EditValue != null && Convert.ToInt32(lkpLargeUOM.EditValue) != 0)
            {
                txtLargeUOMFactor.Enabled = true;
                txtLargeUOMPrice.Enabled = true;
                rdoSellUOM2.Enabled = rdoPrchsUOM2.Enabled = txt_LargeUOMCode.Enabled = true;
                chk_LargeIsStopped.Enabled = true;
            }
            else
            {
                txtLargeUOMFactor.Enabled = false;
                txtLargeUOMPrice.Enabled = false;
                rdoSellUOM2.Enabled = rdoPrchsUOM2.Enabled = txt_LargeUOMCode.Enabled = false;

                txtLargeUOMFactor.Text = txtLargeUOMPrice.Text = string.Empty;
                txt_LargeUOMCode.EditValue = null;
                rdoSellUOM2.Checked = rdoPrchsUOM2.Checked = false;
                rdoSellUOM0.Checked = rdoPrchsUOM0.Checked = true;
                chk_LargeIsStopped.Enabled = false;
                chk_LargeIsStopped.EditValue = null;
            }
        }

        private void rdoItemType_EditValueChanging(object sender, ChangingEventArgs e)
        {
            if (LoadItemDataCompleted == false)
                return;

            //service can be converted to item at any time.
            //item can be converted to service, if it does not exist in store.
            if (action == FormAction.Edit)
            {
                ERPDataContext DB = new ERPDataContext();

                if (e.OldValue != e.NewValue)
                {
                    //user can't change subtotal after creation
                    if (action == FormAction.Edit && Convert.ToInt32(e.OldValue) == (int)ItemType.Subtotal)
                    {
                        e.Cancel = true;
                        return;
                    }

                    if (Convert.ToInt32(e.OldValue) == (int)ItemType.Inventory ||
                        Convert.ToInt32(e.OldValue) == (int)ItemType.Assembly)
                    {

                        if (DB.SL_InvoiceDetails.Where
                             (i => i.ItemId == ItemID).Count() > 0)
                        {
                            e.Cancel = true;
                            return;
                        }
                    }


                    //if (DB.IC_BOMs.Where(s => s.ProductItemId == ItemID).Count() +
                    //        DB.IC_BOMDetails.Where(s => s.RawItemId == ItemID).Count() > 0)
                    //{
                    //    XtraMessageBox.Show(
                    //        Shared.IsEnglish == true ? ResICEn.ValTxtItemBOMDel : ResICAr.ValTxtItemBOMDel
                    //        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    //    e.Cancel = true;
                    //    return;
                    //}

                    //matrix item changing not applicable, before deleting matrix items
                    if (Convert.ToInt32(e.OldValue) == (int)ItemType.MatrixParent &&
                        DB.IC_Items.Where(i => i.mtrxParentItem == ItemID).Count() > 0)
                    {
                        XtraMessageBox.Show(
                            Shared.IsEnglish == true ? ResICEn.MsgDelMtxItems : ResICAr.MsgDelMtxItems
                            , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        e.Cancel = true;
                        return;
                    }

                    //service or sutotal used before can't change
                    if (
                        (Convert.ToInt32(e.OldValue) == (int)ItemType.Service ||
                        Convert.ToInt32(e.OldValue) == (int)ItemType.Subtotal)
                        &&
                        (
                        DB.SL_InvoiceDetails.Where(i => i.ItemId == ItemID).Count() > 0 ||
                        //DB.SL_QuoteDetails.Where(i => i.ItemId == ItemID).Count() > 0 ||
                        //DB.SL_SalesOrderDetails.Where(i => i.ItemId == ItemID).Count() > 0 ||
                        DB.PR_InvoiceDetails.Where(i => i.ItemId == ItemID).Count() > 0 ||
                        //DB.PR_PurchaseOrderDetails.Where(i => i.ItemId == ItemID).Count() > 0 ||
                        DB.SL_Add_Details.Where(i => i.ItemId == ItemID).Count() > 0)
                        )
                    {
                        e.Cancel = true;
                        return;
                    }

                    //inventory item used in quantities equation can't change
                    //if (Convert.ToInt32(e.OldValue) == (int)ItemType.Inventory &&
                    //    DB.IC_ItemPricesPerQties.Where(i => i.ItemId == ItemID).Count() > 0)
                    //{
                    //    e.Cancel = true;
                    //    return;
                    //}
                }
            }
        }

        private void rdoItemType_EditValueChanged(object sender, EventArgs e)
        {
            if (Shared.PriceListAvailable)
                tab_PriceLevels.PageVisible = true;

            txtPurchasePrice.Enabled = txtAudiancePrice.Enabled = txtPurchaseDiscRatio.Enabled = txtPurchaseTaxValue.Enabled = true;
            txtSalesDiscRatio.Enabled = txt_WarrantyMonths.Enabled = true;
            tab_image.PageVisible = true;
            tab_Other.PageVisible = true;

            grp_international.Enabled = true;

            grp_UOM.Enabled = true;
            grpExtra.Enabled = true;

            //prices per qty
            if (
                (Convert.ToInt32(rdoItemType.EditValue) == (int)ItemType.Inventory
                || Convert.ToInt32(rdoItemType.EditValue) == (int)ItemType.Assembly)
                && Shared.user.SellPriceUponQtyAvailable)
                tab_PricesPerQty.PageVisible = true;
            else
                tab_PricesPerQty.PageVisible = false;

            //matrix
            if (Convert.ToInt32(rdoItemType.EditValue) == (int)ItemType.MatrixParent)
                tab_Matrix.PageVisible = true;
            else
                tab_Matrix.PageVisible = false;

            ////subtotal
            if (Convert.ToInt32(rdoItemType.EditValue) == (int)ItemType.Subtotal)
            {
                txtPurchasePrice.Enabled = txtAudiancePrice.Enabled = txtPurchaseDiscRatio.Enabled = txtPurchaseTaxValue.Enabled = false;
                txtSalesDiscRatio.Enabled = txt_WarrantyMonths.Enabled = false;
                tab_Matrix.PageVisible = false;
                tab_PricesPerQty.PageVisible = false;
                tab_image.PageVisible = false;
                tab_Other.PageVisible = false;
                tab_PriceLevels.PageVisible = false;

                grp_international.Enabled = false;
                grp_UOM.Enabled = false;

                grpExtra.Enabled = false;
            }
        }

        private void btnAddUOM_Click(object sender, EventArgs e)
        {
            new frm_AddUOM().ShowDialog();
            lkpSmallUOM.Properties.DataSource = lkpMediumUOM.Properties.DataSource =
                lkpLargeUOM.Properties.DataSource = new ERPDataContext().IC_UOMs.ToList();
        }


        private void NewItem()
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            action = FormAction.Add;
            Reset();
            txtItemCode1.Focus();

            ERPDataContext DB = new ERPDataContext();
            ItemID = DB.IC_Items.Select(x => x.ItemId).ToList().DefaultIfEmpty(0).Max() + 1;

            if (Shared.st_Store.EncodeItemsPerCategory == false)
                txtItemCode1.Text = (DB.IC_Items.Select(x => x.ItemCode1).ToList().DefaultIfEmpty(0).Max() + 1).ToString();
            else
            {
                txtItemCode1.Text = MyHelper.ItemNumGenerated(
                    lstCategories.Where(x => x.CategoryId == Convert.ToInt32(lkpCategory.EditValue)).First());
            }

            tab_Control1.SelectedTabPage = tab_MainInfo;
            rdoItemType.SelectedIndex = 0;
            txtItemCode1.Focus();


            NewInternationalCodes.Clear();

            #region data grid clearing
            //grd_SalesPerQty.DataSource = DdBind.IC_ItemPricesPerQties.Where(s => s.ItemId == 0);
            grd_Mtrx.DataSource = DdBind.IC_Items.Where(b => b.mtrxParentItem == 0);
            grd_Mtrx.RefreshDataSource();
            //grd_Vendors.DataSource = DdBind.IC_ItemVendors.Where(s => s.ItemId == 0);
            #endregion

            DoValidate();
            DataModified = false;
        }

        void DoValidate()
        {
            rdoItemType.DoValidate();

            txtItemCode1.DoValidate();
            txtItemCode2.DoValidate();
            txtItemNameAr.DoValidate();
            txtItemNameEn.DoValidate();
            txtDesc.DoValidate();
            txtDescEn.DoValidate();

            lkpCategory.DoValidate();
            lkpComp.DoValidate();

            txtPurchasePrice.DoValidate();
            txtAudiancePrice.DoValidate();
            txt_WarrantyMonths.DoValidate();
            txtPurchaseDiscRatio.DoValidate();
            txtSalesDiscRatio.DoValidate();

            txtPurchaseTaxRatio.DoValidate();
            txtCustomPurchasesTaxRatio.DoValidate();
            txtPurchaseTaxValue.DoValidate();
            txtSalesTaxRatio.DoValidate();
            txtCustomSalesTaxRatio.DoValidate();
            txtSalesTaxValue.DoValidate();
            chk_calcTaxBeforeDisc.DoValidate();

            lkpSmallUOM.DoValidate();
            txtSmallUOMPrice.DoValidate();

            rdoSellUOM0.DoValidate();
            rdoPrchsUOM0.DoValidate();

            lkpMediumUOM.DoValidate();
            txtMediumUOMFactor.DoValidate();
            txtMediumUOMPrice.DoValidate();
            txt_MediumUOMCode.DoValidate();
            rdoSellUOM1.DoValidate();
            rdoPrchsUOM1.DoValidate();

            lkpLargeUOM.DoValidate();
            txtLargeUOMFactor.DoValidate();
            txtLargeUOMPrice.DoValidate();
            txt_LargeUOMCode.DoValidate();
            rdoSellUOM2.DoValidate();
            rdoPrchsUOM2.DoValidate();

            txtReorder.DoValidate();
            txtMinQty.DoValidate();
            txtMaxQty.DoValidate();

            cmbChangePriceMethod.DoValidate();
            cmbChangeSellPrice.DoValidate();

            txtInternationalCode.DoValidate();

            itemPhoto.DoValidate();

            ChangeSet changes = DdBind.GetChangeSet();
            DdBind.Refresh(RefreshMode.OverwriteCurrentValues, changes.Updates);
            DdBind.Refresh(RefreshMode.OverwriteCurrentValues, changes.Inserts);
        }

        private void GetItemData()
        {
            ERPDataContext DB = new ERPDataContext();
            var Item = (from i in DB.IC_Items
                        where i.ItemId == ItemID
                        select i).SingleOrDefault();
            if (Item == null)
            {
                NewItem();
            }
            else
            {
                LoadItem(DB, Item);
            }
        }

        private void LoadItem(ERPDataContext DB, IC_Item Item)
        {
            Reset();

            LoadItemDataCompleted = false;
            txtItemCode1.Text = Item.ItemCode1.ToString();
            txtItemCode2.Text = Item.ItemCode2;
            txtItemNameAr.Text = Item.ItemNameAr;
            txtItemNameEn.Text = Item.ItemNameEn;
            txtDesc.Text = Item.Description;
            txtDescEn.Text = Item.DescriptionEn;

            rdoItemType.EditValue = Item.ItemType;

            lkpCategory.EditValue = Item.Category;
            lkpComp.EditValue = Item.Company;


            //var codes = (from c in DB.IC_InternationalCodes
            //             where c.ItemId == ItemID
            //             select c).ToList();
            //foreach (var c in codes)
            //    lstInternationalCodes.Items.Add(c.InternationalCode);

            #region UOM Data
            lkpSmallUOM.EditValue = Convert.ToInt32(Item.SmallUOM);
            txtSmallUOMPrice.Text = Item.SmallUOMPrice.ToString();
            chk_SmallIsStopped.EditValue = Item.SmallIsStopped;

            if (Item.MediumUOM.HasValue)
            {
                lkpMediumUOM.EditValue = Convert.ToInt32(Item.MediumUOM);
                txtMediumUOMFactor.Text = Item.MediumUOMFactor;
                txtMediumUOMPrice.Text = Item.MediumUOMPrice.Value.ToString();
                txt_MediumUOMCode.EditValue = Item.MediumUOMCode;
                chk_MediumIsStopped.EditValue = Item.MediumIsStopped;
            }
            else
            {
                lkpMediumUOM.EditValue = null;
                txtMediumUOMFactor.Text = string.Empty;
                txtMediumUOMPrice.Text = string.Empty;
                txt_MediumUOMCode.EditValue = null;
                chk_MediumIsStopped.EditValue = null;
            }
            if (Item.LargeUOM.HasValue)
            {
                lkpLargeUOM.EditValue = Convert.ToInt32(Item.LargeUOM);
                txtLargeUOMFactor.Text = Item.LargeUOMFactor;
                txtLargeUOMPrice.Text = Item.LargeUOMPrice.Value.ToString();
                txt_LargeUOMCode.EditValue = Item.LargeUOMCode;
                chk_LargeIsStopped.EditValue = Item.LargeIsStopped;

            }
            else
            {
                lkpLargeUOM.EditValue = null;
                txtLargeUOMFactor.Text = string.Empty;
                txtLargeUOMPrice.Text = string.Empty;
                txt_LargeUOMCode.EditValue = null;
                chk_LargeIsStopped.EditValue = null;
            }

            if (Item.DfltPrchsUomIndx == 0)
                rdoPrchsUOM0.Checked = true;
            else if (Item.DfltPrchsUomIndx == 1)
                rdoPrchsUOM1.Checked = true;
            else if (Item.DfltPrchsUomIndx == 2)
                rdoPrchsUOM2.Checked = true;

            if (Item.DfltSellUomIndx == 0)
                rdoSellUOM0.Checked = true;
            else if (Item.DfltSellUomIndx == 1)
                rdoSellUOM1.Checked = true;
            else if (Item.DfltSellUomIndx == 2)
                rdoSellUOM2.Checked = true;
            #endregion

            #region Other Data
            txtReorder.Text = Item.ReorderLevel.ToString();
            txtMaxQty.Text = Item.MaxQty.ToString();
            txtMinQty.Text = Item.MinQty.ToString();

            cmbChangePriceMethod.EditValue = Convert.ToByte(Item.ChangePriceMethod);
            cmbChangeSellPrice.EditValue = Convert.ToBoolean(Item.ChangeSellPrice);
            ////////LOAD THE CURRENT QNTY FROM STORES/////////

            //var qnty = from q in DB.IC_ItemStores
            //           where q.ItemId == ItemID
            //           group q by q.StoreId
            //               into mygrp
            //           join s in DB.IC_Stores
            //           on mygrp.Key equals s.StoreId
            //           select new
            //           {
            //               ItemId = ItemID,
            //               StoreId = mygrp.Key,
            //               StoreNameAr = s.StoreNameAr,
            //               Qnt = (mygrp.Where(c => c.IsInTrns == true).Count() > 0 ?
            //               mygrp.Where(c => c.IsInTrns == true).Sum(x => x.Qty) : 0)
            //                -
            //              (mygrp.Where(c => c.IsInTrns == false).Count() > 0 ?
            //              mygrp.Where(c => c.IsInTrns == false).Sum(x => x.Qty) : 0)
            //           };
            //grdQty.DataSource = qnty;

            /////LAOD ITEM PRICES///////

            txtPurchasePrice.EditValue = Item.PurchasePrice;
            txtAudiancePrice.EditValue = Item.AudiencePrice;
            txtPurchaseDiscRatio.EditValue = Item.PurchaseDiscRatio;
            txtSalesDiscRatio.EditValue = Item.SalesDiscRatio;

            txtPurchaseTaxValue.EditValue = Item.PurchaseTaxValue;
            txtPurchaseTaxRatio.EditValue = Item.PurchaseTaxRatio;
            txtCustomPurchasesTaxRatio.EditValue = Item.CustomPurchasesTaxRatio;
            txtSalesTaxValue.EditValue = Item.SalesTaxValue;
            txtSalesTaxRatio.EditValue = Item.SalesTaxRatio;
            txtCustomSalesTaxRatio.EditValue = Item.CustomSalesTaxRatio;
            chk_calcTaxBeforeDisc.Checked = Item.calcTaxBeforeDisc;

            //Dimensions
            txtHeight.EditValue = Item.Height;
            txtWidth.EditValue = Item.Width;
            txtLength.EditValue = Item.Length;

            txt_WarrantyMonths.EditValue = Item.WarrantyMonths;

            #endregion

            //expire:mahmoud
            chkIsExpire.EditValue = Item.IsExpire;
            chk_IsDeleted.Checked = Item.IsDeleted;
            chk_IsPos.Checked = Item.IsPos;

            //expire:mahmoud
            if (DB.SL_InvoiceDetails.Where(i => i.ItemId == Item.ItemId).Count() > 0)
            {
                chkIsExpire.Enabled = false;

                //Mohammad 01-07-2020
                chk_IsLibra.Enabled = chk_PricingWithSmall.Enabled = chk_VariableWeight.Enabled = false;
            }
            else
            {
                if (Shared.st_Store.ExpireDate == false)
                    chkIsExpire.Enabled = false;
                else
                    chkIsExpire.Enabled = true;
            }

            #region Pic
            picPath = Item.PicPath;

            if (!string.IsNullOrEmpty(picPath))
                try { itemPhoto.Image = Image.FromFile(picPath); } catch { }
            else
                itemPhoto.Image = null;
            #endregion

            ////item prices per qty & Matrix
            //if (Convert.ToInt32(rdoItemType.EditValue) == (int)ItemType.Inventory)
            //{
            //    grd_SalesPerQty.DataSource = DdBind.IC_ItemPricesPerQties.Where(b => b.ItemId == Item.ItemId);
            //    grd_SalesPerQty.RefreshDataSource();
            //}

            //vendors
            //grd_Vendors.DataSource = DdBind.IC_ItemVendors.Where(b => b.ItemId == Item.ItemId);
            //grd_Vendors.RefreshDataSource();

            //marix
            if (Convert.ToInt32(rdoItemType.EditValue) == (int)ItemType.MatrixParent)
                LoadMatrixGrid();

            //#region price levels
            //if (Shared.PriceListAvailable)
            //{
            //    #region purchasePrices
            //    var purchasePrices = ((from i in DB.IC_PrPriceLevelDetails
            //                           where i.ItemId == ItemID
            //                           && i.smallUOMPrice.HasValue
            //                           select new
            //                           {
            //                               i.PrPriceLevelId,
            //                               PrPLName = i.IC_PrPriceLevel.PrPLName,
            //                               smallUOMPrice = decimal.ToDouble(i.smallUOMPrice.Value),
            //                               ItemId = ItemID
            //                           }).Union(
            //             from i in DB.IC_PrPriceLevels
            //             where i.IsRatio == true
            //             select new
            //             {
            //                 i.PrPriceLevelId,
            //                 PrPLName = i.PrPLName,
            //                 smallUOMPrice = i.IsRatioIncrease ? decimal.ToDouble(Item.PurchasePrice + (Item.PurchasePrice * i.Ratio / 100)) :
            //                 decimal.ToDouble(Item.PurchasePrice - (Item.PurchasePrice * i.Ratio / 100)),
            //                 ItemId = ItemID
            //             }
            //             )).ToList();

            //    grdPrPLevel.DataSource = purchasePrices;

            //    decimal mediumUOM_Price = Item.MediumUOMPrice.HasValue ? Item.MediumUOMPrice.Value : 0;
            //    decimal largeUOM_Price = Item.LargeUOMPrice.HasValue ? Item.LargeUOMPrice.Value : 0;
            //    #endregion

            //    #region salesPrices
            //    dt_PriceList.Rows.Clear();
            //    var priceListWithRatio = (from i in DB.IC_PriceLevels
            //                              where i.IsRatio == true
            //                              select new
            //                              {
            //                                  i.PriceLevelId,
            //                                  i.PLName,
            //                                  smallUOMPrice = i.IsRatioIncrease ? decimal.ToDouble(Item.SmallUOMPrice + (Item.SmallUOMPrice * i.Ratio / 100)) :
            //                                  decimal.ToDouble(Item.SmallUOMPrice - (Item.SmallUOMPrice * i.Ratio / 100)),
            //                                  MediumUOMPrice = i.IsRatioIncrease ? decimal.ToDouble(mediumUOM_Price + (mediumUOM_Price * i.Ratio / 100)) :
            //                                  decimal.ToDouble(mediumUOM_Price - (mediumUOM_Price * i.Ratio / 100)),
            //                                  LargeUOMPrice = i.IsRatioIncrease ? decimal.ToDouble(largeUOM_Price + (largeUOM_Price * i.Ratio / 100)) :
            //                                  decimal.ToDouble(largeUOM_Price - (largeUOM_Price * i.Ratio / 100)),
            //                                  ItemId = ItemID
            //                              }).ToList();
            //    foreach (var pr in priceListWithRatio)
            //    {
            //        DataRow dr_pr = dt_PriceList.NewRow();
            //        dr_pr["PriceLevelId"] = pr.PriceLevelId;
            //        dr_pr["PLName"] = pr.PLName;
            //        dr_pr["smallUOMPrice"] = pr.smallUOMPrice;
            //        dr_pr["MediumUOMPrice"] = pr.MediumUOMPrice;
            //        dr_pr["LargeUOMPrice"] = pr.LargeUOMPrice;
            //        dr_pr["ItemId"] = pr.ItemId;
            //        dt_PriceList.Rows.Add(dr_pr);
            //    }

            //    var priceListWithoutRatio = DB.IC_PriceLevels.Where(i => !i.IsRatio);
            //    foreach (var pr in priceListWithoutRatio)
            //    {
            //        var query = (from i in DB.IC_PriceLevelDetails
            //                     where i.ItemId == ItemID
            //                     where i.PriceLevelId == pr.PriceLevelId
            //                     select new
            //                     {
            //                         i.PriceLevelId,
            //                         i.IC_PriceLevel.PLName,
            //                         smallUOMPrice = decimal.ToDouble(i.smallUOMPrice.HasValue ? i.smallUOMPrice.Value : Item.SmallUOMPrice),
            //                         MediumUOMPrice = decimal.ToDouble(i.MediumUOMPrice.HasValue ? i.MediumUOMPrice.Value : (Item.MediumUOMPrice.HasValue ? Item.MediumUOMPrice.Value : 0)),
            //                         LargeUOMPrice = decimal.ToDouble(i.LargeUOMPrice.HasValue ? i.LargeUOMPrice.Value : (Item.LargeUOMPrice.HasValue ? Item.LargeUOMPrice.Value : 0)),
            //                         i.ItemId
            //                     }).FirstOrDefault();
            //        if (query != null)
            //        {
            //            DataRow dr_pr = dt_PriceList.NewRow();
            //            dr_pr["PriceLevelId"] = query.PriceLevelId;
            //            dr_pr["PLName"] = pr.PLName;
            //            dr_pr["smallUOMPrice"] = query.smallUOMPrice;
            //            dr_pr["MediumUOMPrice"] = query.MediumUOMPrice;
            //            dr_pr["LargeUOMPrice"] = query.LargeUOMPrice;
            //            dr_pr["ItemId"] = query.ItemId;
            //            dt_PriceList.Rows.Add(dr_pr);
            //        }
            //        else
            //        {
            //            DataRow dr_pr = dt_PriceList.NewRow();
            //            dr_pr["PriceLevelId"] = pr.PriceLevelId;
            //            dr_pr["PLName"] = pr.PLName;
            //            dr_pr["smallUOMPrice"] = 0;
            //            dr_pr["MediumUOMPrice"] = 0;
            //            dr_pr["LargeUOMPrice"] = 0;
            //            dr_pr["ItemId"] = ItemID;
            //            dt_PriceList.Rows.Add(dr_pr);
            //        }
            //    }
            //    grdSlPLevel.DataSource = dt_PriceList;
            //    //var salesPrices = ((from i in DB.IC_PriceLevelDetails
            //    //                    where i.ItemId == ItemID
            //    //                    select new
            //    //                    {
            //    //                        i.PriceLevelId,
            //    //                        i.IC_PriceLevel.PLName,
            //    //                        smallUOMPrice = decimal.ToDouble(i.smallUOMPrice.HasValue ? i.smallUOMPrice.Value : Item.SmallUOMPrice),
            //    //                        MediumUOMPrice = decimal.ToDouble(i.MediumUOMPrice.HasValue ? i.MediumUOMPrice.Value : (Item.MediumUOMPrice.HasValue ? Item.MediumUOMPrice.Value : 0)),
            //    //                        LargeUOMPrice = decimal.ToDouble(i.LargeUOMPrice.HasValue ? i.LargeUOMPrice.Value : (Item.LargeUOMPrice.HasValue ? Item.LargeUOMPrice.Value : 0)),
            //    //                        i.ItemId
            //    //                    }).ToList().Union(
            //    //         from i in DB.IC_PriceLevels
            //    //         where i.IsRatio == true
            //    //         select new
            //    //         {
            //    //             i.PriceLevelId,
            //    //             i.PLName,
            //    //             smallUOMPrice = i.IsRatioIncrease ? decimal.ToDouble(Item.SmallUOMPrice + (Item.SmallUOMPrice * i.Ratio / 100)) :
            //    //             decimal.ToDouble(Item.SmallUOMPrice - (Item.SmallUOMPrice * i.Ratio / 100)),
            //    //             MediumUOMPrice = i.IsRatioIncrease ? decimal.ToDouble(mediumUOM_Price + (mediumUOM_Price * i.Ratio / 100)) :
            //    //             decimal.ToDouble(mediumUOM_Price - (mediumUOM_Price * i.Ratio / 100)),
            //    //             LargeUOMPrice = i.IsRatioIncrease ? decimal.ToDouble(largeUOM_Price + (largeUOM_Price * i.Ratio / 100)) :
            //    //             decimal.ToDouble(largeUOM_Price - (largeUOM_Price * i.Ratio / 100)),
            //    //             ItemId= ItemID
            //    //         }
            //    //         )).ToList();
            //    //grdSlPLevel.DataSource = salesPrices;
            //    #endregion

            //}
            //#endregion

            action = FormAction.Edit;
            DoValidate();
            DataModified = false;
            LoadItemDataCompleted = true;

            if (Item.is_libra.HasValue)
                chk_IsLibra.Checked = Item.is_libra.Value;
            if (Item.VariableWeight.HasValue)
                chk_VariableWeight.Checked = Item.VariableWeight.Value;
            if (Item.PricingWithSmall.HasValue)
                chk_PricingWithSmall.Checked = Item.PricingWithSmall.Value;

            cmb_WeightUnit.EditValue = Item.DfltWeightUnit;

            var subTaxes = DB.IC_ItemSubTaxes.Where(a => a.ItemId == Item.ItemId).ToList();
            if (subTaxes.Count() != 0)
            {
                dt_SubTax.Rows.Clear();
                foreach (var u in subTaxes)
                {
                    DataRow r = dt_SubTax.NewRow();
                    r["SubTaxId"] = u.SubTaxId;
                    r["Rate"] = u.Rate !=null?Convert.ToDouble(u.Rate):0;
                    dt_SubTax.Rows.Add(r);

                }
                dt_SubTax.AcceptChanges();
            }
            else
            {
                dt_SubTax.Rows.Clear();
            }
            //gridLocation.DataSource = DB_Location.IC_Item_Locations.Where(i => i.ItemId == ItemID);


        }

        void Reset()
        {
            txtItemCode1.Text =
            txtItemCode2.Text =
            txtItemNameAr.Text =
            txtItemNameEn.Text =
            txtDesc.Text =
            txtDescEn.Text = string.Empty;
            chk_SmallIsStopped.EditValue = null;
            lstInternationalCodes.Items.Clear();

            lkpSmallUOM.EditValue = 1;
            lkpMediumUOM.EditValue = lkpLargeUOM.EditValue = null;
            txtMediumUOMFactor.Text = txtLargeUOMFactor.Text = string.Empty;
            txt_MediumUOMCode.EditValue = txt_LargeUOMCode.EditValue = null;
            rdoSellUOM0.Checked = rdoPrchsUOM0.Checked = true;
            rdoSellUOM1.Checked = rdoPrchsUOM1.Checked = rdoSellUOM2.Checked = rdoPrchsUOM2.Checked = false;
            txtReorder.Text = txtMaxQty.Text = txtMinQty.Text = "0";

            txtPurchasePrice.Text =
            txtAudiancePrice.Text =
            txtLargeUOMPrice.Text = txtMediumUOMPrice.Text = txtSmallUOMPrice.Text =
            txtPurchaseDiscRatio.Text = txtSalesDiscRatio.Text =
            txtPurchaseTaxValue.Text = txtPurchaseTaxRatio.Text = txtCustomPurchasesTaxRatio.Text = txtCustomSalesTaxRatio.Text =
            txtSalesTaxRatio.Text = txtSalesTaxValue.Text = txt_WarrantyMonths.Text = string.Empty;
            chk_calcTaxBeforeDisc.Checked = false;

            cmbChangePriceMethod.SelectedIndex = 0;
            cmbChangeSellPrice.SelectedIndex = 0;

            grdQty.DataSource = null;

            //expire:mahmoud            
            if (Shared.st_Store.ExpireDate == false)
                chkIsExpire.Enabled = false;
            else
                chkIsExpire.Enabled = true;

            chkIsExpire.Checked = false;
            chk_IsDeleted.Checked = false;
            chk_IsPos.Checked = false;

            picPath = null;
            itemPhoto.Image = null;

            DataModified = false;

            txtLargeUOMFactor.ErrorText = string.Empty;
            txtMediumUOMFactor.ErrorText = string.Empty;

            txtHeight.EditValue = txtWidth.EditValue = txtLength.EditValue = 1;

            chk_VariableWeight.Enabled = chk_PricingWithSmall.Enabled = chk_IsLibra.Enabled = true;
            chk_IsLibra.Checked = false;
            chk_VariableWeight.Checked = false;
            chk_PricingWithSmall.Checked = false;

            cmb_WeightUnit.EditValue = null;
            gridLocation.DataSource = null;
            dt_SubTax.Rows.Clear();
        }

        bool ChkCodeDuplication(string codeStr)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            int CodeIntgr = 0;
            bool Parsed = int.TryParse(codeStr, out CodeIntgr);
            IC_Item item;
            if (Parsed)
            {
                item = (from i in DB.IC_Items
                        where i.ItemCode1 == CodeIntgr
                        || i.ItemCode2 == codeStr
                        || i.MediumUOMCode == codeStr
                        || i.LargeUOMCode == codeStr
                        select i).FirstOrDefault();
            }
            else
            {
                item = (from i in DB.IC_Items
                        where i.ItemCode2 == codeStr
                        || i.MediumUOMCode == codeStr
                        || i.LargeUOMCode == codeStr
                        select i).FirstOrDefault();
            }

            if (item != null && item.ItemId != ItemID)
                return true;
            return false;
        }

        DialogResult ChangesMade()
        {
            ChangeSet ch = DdBind.GetChangeSet();
            if ((
                DataModified ||
                ch.Deletes.Count > 0 ||
                ch.Updates.Count > 0 ||
                ch.Inserts.Count > 0)
                && ItemID > 0)
            {
                DialogResult r = XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgDataModified : ResICAr.MsgDataModified//"لقد قمت بعمل بعض التغييرات, هل تريد الحفظ أولا "                                        
                    , "", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                if (r == DialogResult.Yes)
                {
                    if (!ValidData())
                        return DialogResult.Cancel;

                    SaveData();
                    return r;
                }
                else if (r == DialogResult.No)
                {
                    // no thing made, continue closing or do next or do previous
                    return r;
                }
                else if (r == DialogResult.Cancel)
                {
                    //cancel form action
                    return r;
                }
            }
            return DialogResult.No;
        }

        private void SaveData()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            if (action == FormAction.Add)
            {
                IC_Item itm = new IC_Item();

                GetItemForSave(itm);

                DB.IC_Items.InsertOnSubmit(itm);
                DB.SubmitChanges();
                ItemID = itm.ItemId;

                MyHelper.UpdateST_UserLog(DB, txtItemCode1.Text, txtItemNameAr.Text,
    (int)FormAction.Add, (int)FormsNames.Item);
            }

            else if (action == FormAction.Edit)
            {
                var Item = (from i in DB.IC_Items
                            where i.ItemId == ItemID
                            select i).SingleOrDefault();
                GetItemForSave(Item);

                MyHelper.UpdateST_UserLog(DB, txtItemCode1.Text, txtItemNameAr.Text,
            (int)FormAction.Edit, (int)FormsNames.Item);
            }

            //Save new International Codes if Exist
            foreach (IC_InternationalCode IC in NewInternationalCodes)
            {
                IC.ItemId = ItemID;
                DB.IC_InternationalCodes.InsertOnSubmit(IC);
            }

            #region item prices per Qty, and vendors
            for (int x = 0; x < gv_SalesPerQty.RowCount; x++)
            {
                if (Convert.ToDecimal(gv_SalesPerQty.GetRowCellValue(x, col_QtyFrom)) <= 0 &&
                    Convert.ToDecimal(gv_SalesPerQty.GetRowCellValue(x, col_QtyTo)) <= 0 &&
                        Convert.ToDecimal(gv_SalesPerQty.GetRowCellValue(x, col_SellPrice)) <= 0)
                    gv_SalesPerQty.DeleteRow(x);

                gv_SalesPerQty.SetRowCellValue(x, gv_SalesPerQty.Columns["ItemId"], ItemID);
            }
            for (int x = 0; x < gv_Vendors.RowCount; x++)
                gv_Vendors.SetRowCellValue(x, gv_Vendors.Columns["ItemId"], ItemID);

            DdBind.SubmitChanges();
            #endregion

            #region update matrix items
            if (Convert.ToInt32(rdoItemType.EditValue) == (int)ItemType.MatrixParent)
            {
                var mtrxItems = DB.IC_Items.Where(i => i.mtrxParentItem == ItemID).ToList();

                foreach (IC_Item i in mtrxItems)
                {
                    i.Description = txtDesc.Text;
                    i.DescriptionEn = txtDescEn.Text;
                    i.IsDeleted = chk_IsDeleted.Checked;

                    i.PurchaseDiscRatio = Convert.ToDecimal(txtPurchaseDiscRatio.Text);
                    i.SalesDiscRatio = Convert.ToDecimal(txtSalesDiscRatio.Text);
                    i.PurchaseTaxRatio = Convert.ToDecimal(txtPurchaseTaxRatio.Text);
                    i.CustomPurchasesTaxRatio = Convert.ToDecimal(txtCustomPurchasesTaxRatio.Text);
                    i.SalesTaxRatio = Convert.ToDecimal(txtSalesTaxRatio.Text);
                    i.CustomSalesTaxRatio = Convert.ToDecimal(txtCustomSalesTaxRatio.Text);
                    i.calcTaxBeforeDisc = chk_calcTaxBeforeDisc.Checked;

                    i.SalesTaxValue = Convert.ToDecimal(txtSalesTaxValue.Text);
                    i.PurchaseTaxValue = Convert.ToDecimal(txtPurchaseTaxValue.Text);
                    i.calcTaxBeforeDisc = chk_calcTaxBeforeDisc.Checked;

                    i.SmallUOM = Convert.ToByte(lkpSmallUOM.EditValue);
                    if (chk_SmallIsStopped.EditValue != null)
                        i.SmallIsStopped = Convert.ToBoolean(chk_SmallIsStopped.EditValue);
                    if (lkpMediumUOM.EditValue != null)
                    {
                        i.MediumUOM = Convert.ToByte(lkpMediumUOM.EditValue);
                        i.MediumUOMFactor = txtMediumUOMFactor.Text.Trim();
                        i.MediumUOMFactorDecimal = MyHelper.FractionToDouble(i.MediumUOMFactor);
                        if (chk_MediumIsStopped.EditValue != null)
                            i.MediumIsStopped = Convert.ToBoolean(chk_MediumIsStopped.EditValue);


                    }
                    else
                    {
                        i.MediumUOM = null;
                        i.MediumUOMFactor = null;
                        i.MediumUOMFactorDecimal = null;
                        i.MediumUOMPrice = null;
                        i.MediumIsStopped = null;
                    }
                    if (lkpLargeUOM.EditValue != null)
                    {
                        i.LargeUOM = Convert.ToByte(lkpLargeUOM.EditValue);
                        i.LargeUOMFactor = txtLargeUOMFactor.Text.Trim();
                        i.LargeUOMFactorDecimal = MyHelper.FractionToDouble(i.LargeUOMFactor);
                        if (chk_LargeIsStopped.EditValue != null)
                            i.LargeIsStopped = Convert.ToBoolean(chk_LargeIsStopped.EditValue);
                    }
                    else
                    {
                        i.LargeUOM = null;
                        i.LargeUOMFactor = null;
                        i.LargeUOMFactorDecimal = null;
                        i.LargeUOMPrice = null;
                        i.LargeIsStopped = null;
                    }
                }
            }
            #endregion

            #region Price Sell Level
            foreach (DataRow dr_p in dt_PriceList.Rows)
            {
                int cellValue = Convert.ToInt32(dr_p["PriceLevelId"]);
                var q = DB.IC_PriceLevels.Where(x => x.IsRatio).Where(i => i.PriceLevelId == cellValue).FirstOrDefault();
                if (q != null) continue;
                IC_PriceLevelDetail itemPriceLevel = DB.IC_PriceLevelDetails.Where(i => i.PriceLevelId == cellValue).Where(i => i.ItemId == ItemID).FirstOrDefault();
                if (itemPriceLevel != null)
                {
                    if (Convert.ToDecimal(dr_p["smallUOMPrice"]) == 0 && Convert.ToDecimal(dr_p["MediumUOMPrice"]) == 0 && Convert.ToDecimal(dr_p["LargeUOMPrice"]) == 0)
                        DB.IC_PriceLevelDetails.DeleteOnSubmit(itemPriceLevel);
                    else
                    {
                        itemPriceLevel.smallUOMPrice = Convert.ToDecimal(dr_p["smallUOMPrice"]);
                        itemPriceLevel.MediumUOMPrice = Convert.ToDecimal(dr_p["MediumUOMPrice"]);
                        itemPriceLevel.LargeUOMPrice = Convert.ToDecimal(dr_p["LargeUOMPrice"]);
                    }
                }
                else
                {
                    if (Convert.ToDecimal(dr_p["smallUOMPrice"]) == 0 && Convert.ToDecimal(dr_p["MediumUOMPrice"]) == 0 && Convert.ToDecimal(dr_p["LargeUOMPrice"]) == 0)
                        continue;
                    IC_PriceLevelDetail itemPriceLvl = new IC_PriceLevelDetail();
                    itemPriceLvl.ItemId = ItemID;
                    itemPriceLvl.PriceLevelId = Convert.ToInt32(dr_p["PriceLevelId"]);
                    itemPriceLvl.smallUOMPrice = Convert.ToDecimal(dr_p["smallUOMPrice"]);
                    itemPriceLvl.MediumUOMPrice = Convert.ToDecimal(dr_p["MediumUOMPrice"]);
                    itemPriceLvl.LargeUOMPrice = Convert.ToDecimal(dr_p["LargeUOMPrice"]);
                    DB.IC_PriceLevelDetails.InsertOnSubmit(itemPriceLvl);
                }
            }
            #endregion
            #region subTax
            var data = DB.IC_ItemSubTaxes.Where(a => a.ItemId == ItemID).ToList();
            if (data.Count() != 0)
                DB.IC_ItemSubTaxes.DeleteAllOnSubmit(data);
            DB.SubmitChanges();
            for (int x = 0; x < dt_SubTax.Rows.Count; x++)
            {
                if (dt_SubTax.Rows[x].RowState == DataRowState.Deleted)
                    continue;

                IC_ItemSubTax detail = new IC_ItemSubTax();
                if (dt_SubTax.Rows[x]["SubTaxId"] == null)
                    continue;
                else
                    detail.SubTaxId =Convert.ToInt32(dt_SubTax.Rows[x]["SubTaxId"]);
                detail.Rate = Convert.ToInt32(dt_SubTax.Rows[x]["Rate"]);
                detail.ItemId = ItemID;
                DB.IC_ItemSubTaxes.InsertOnSubmit(detail);
                DB.SubmitChanges();
                dt_SubTax.AcceptChanges();
            }
            #endregion
            DB.SubmitChanges();
            try
            {
                DB_Location.SubmitChanges();
            }
         
            catch (Exception ex)
            {
                XtraMessageBox.Show(
                   Shared.IsEnglish == true ? "An error has occured while saving item's locations, please make sure not to duplicate the loaction"
                                    : "حدث خطأ أثناء حفظ مواقع الصنف، يرجى التأكد من عدم تكرار الموقع أكثر من مرة"
                   , "", MessageBoxButtons.OK, MessageBoxIcon.Information);

            }
            XtraMessageBox.Show(
                Shared.IsEnglish == true ? ResICEn.MsgSave : ResICAr.MsgSave//"تم الحفظ"
                , "", MessageBoxButtons.OK, MessageBoxIcon.Information);

            action = FormAction.Edit;
            DoValidate();
            DataModified = false;
            NewInternationalCodes.Clear();


        }

        private bool ValidData()
        {
            if (action == FormAction.Add)
            {
                if (prvlg != null && !prvlg.CanAdd)
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgPrvNew : ResICAr.MsgPrvNew//"عفوا, انت لا تمتلك صلاحية انشاء بيان جديد"                        
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            if (action == FormAction.Edit)
            {
                if (prvlg != null && !prvlg.CanEdit)
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgPrvEdit : ResICAr.MsgPrvEdit//"عفوا, انت لا تمتلك صلاحية تعديل هذا البيان"
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }

            //if (string.IsNullOrEmpty(txtPurchasePrice.Text))
            //{
            //    txtPurchasePrice.ErrorText = Shared.IsEnglish == true ? ResICEn.ValTxtItemPurchasePrice : ResICAr.ValTxtItemPurchasePrice;//"يجب تسجيل سعر الشراء"
            //    txtPurchasePrice.Focus();
            //    return false;
            //}
            //if (string.IsNullOrEmpty(txtSmallUOMPrice.Text))
            //{
            //    txtSmallUOMPrice.ErrorText = Shared.IsEnglish == true ? ResICEn.ValTxtItemSellPrice : ResICAr.ValTxtItemSellPrice;//"يجب تسجيل سعر بيع وحدة التجزئه"
            //    txtSmallUOMPrice.Focus();
            //    return false
            //}

            if (string.IsNullOrEmpty(txtItemCode1.Text.Trim()))
            {
                txtItemCode1.ErrorText = Shared.IsEnglish == true ? ResICEn.ValTxtItemCode1 : ResICAr.ValTxtItemCode1;//"يجب تسجيل كود1 للصنف"
                txtItemCode1.Focus();
                return false;
            }

            List<string> lst_codes = new List<string>();

            if (!string.IsNullOrEmpty(txtItemCode1.Text.Trim()))
                lst_codes.Add(txtItemCode1.Text.Trim());
            if (!string.IsNullOrEmpty(txtItemCode2.Text.Trim()))
                lst_codes.Add(txtItemCode2.Text.Trim());
            if (!string.IsNullOrEmpty(txt_MediumUOMCode.Text.Trim()))
                lst_codes.Add(txt_MediumUOMCode.Text.Trim());
            if (!string.IsNullOrEmpty(txt_LargeUOMCode.Text.Trim()))
                lst_codes.Add(txt_LargeUOMCode.Text.Trim());

            if (lst_codes.GroupBy(n => n).Any(c => c.Count() > 1))
            {
                XtraMessageBox.Show(
                   Shared.IsEnglish == true ? ResICEn.ItemCodesDuplicate : ResICAr.ItemCodesDuplicate
                   , "", MessageBoxButtons.OK, MessageBoxIcon.Warning); txtItemCode1.Focus();
                txtItemCode1.Focus();
                return false;
            }

            if (string.IsNullOrEmpty(txtItemNameAr.Text.Trim()))
            {
                txtItemNameAr.ErrorText = Shared.IsEnglish == true ? ResICEn.ValTxtItemName : ResICAr.ValTxtItemName;//"يجب تسجيل اسم الصنف"
                txtItemNameAr.Focus();
                return false;
            }

            if (ChkCodeDuplication(txtItemCode1.Text) == true)
            {
                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.ValTxtItemCode1Exist : ResICAr.ValTxtItemCode1Exist//"يوجد صنف لديه نفس الكود 1"
                    , "", MessageBoxButtons.OK, MessageBoxIcon.Warning); txtItemCode1.Focus();
                txtItemCode1.Focus();
                return false;
            }

            if (Shared.st_Store.PrintBarcodePerInventory == false && txtItemCode1.Text.Length > Shared.st_Store.BarcodeItemCodeLength)
            {
                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.ValItemCodeLength : ResICAr.ValItemCodeLength,
                    Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtItemCode1.Focus();

                return false;
            }

            if (!string.IsNullOrEmpty(txtItemCode2.Text))
            {
                if (ChkCodeDuplication(txtItemCode2.Text) == true)
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.ValTxtItemCode2Exist : ResICAr.ValTxtItemCode2Exist//"يوجد صنف لديه نفس الكود 2"
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Warning); txtItemCode1.Focus();
                    txtItemCode2.Focus();
                    return false;
                }
            }
            if (!string.IsNullOrEmpty(txt_MediumUOMCode.Text))
            {
                if (ChkCodeDuplication(txt_MediumUOMCode.Text) == true)
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.ValTxtItemCode2Exist : ResICAr.ValTxtItemCode2Exist//"يوجد صنف لديه نفس الكود 2"
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Warning); txtItemCode1.Focus();
                    txt_MediumUOMCode.Focus();
                    return false;
                }
            }
            if (!string.IsNullOrEmpty(txt_LargeUOMCode.Text))
            {
                if (ChkCodeDuplication(txt_LargeUOMCode.Text) == true)
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.ValTxtItemCode2Exist : ResICAr.ValTxtItemCode2Exist//"يوجد صنف لديه نفس الكود 2"
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Warning); txtItemCode1.Focus();
                    txt_LargeUOMCode.Focus();
                    return false;
                }
            }

            var itemName = (from i in new ERPDataContext().IC_Items
                            where i.ItemNameAr.Trim() == txtItemNameAr.Text.Trim()
                            && i.ItemId != ItemID
                            select i).FirstOrDefault();
            if (itemName != null)
            {
                XtraMessageBox.Show(
                     Shared.IsEnglish == true ? ResICEn.MsgNameExist : ResICAr.MsgNameExist
                     , "", MessageBoxButtons.OK, MessageBoxIcon.Warning); txtItemCode1.Focus();
                txtItemNameAr.Focus();
                return false;
            }

            var cat = lstCategories.Where(x => x.CategoryId == Convert.ToInt32(lkpCategory.EditValue)).FirstOrDefault();
            if(cat == null)
            {
                XtraMessageBox.Show(
      Shared.IsEnglish == true ? ResICEn.IcSubCat : ResICAr.IcSubCat
      , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                lkpCategory.Focus();

                return false;
            }
            if (cat.CatNumber == "1" || lstCategories.Where(x => x.ParentId == Convert.ToInt32(lkpCategory.EditValue)).Count() > 0)
            {
                XtraMessageBox.Show(
      Shared.IsEnglish == true ? ResICEn.IcSubCat : ResICAr.IcSubCat
      , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                lkpCategory.Focus();

                return false;
            }
            /*if (Convert.ToInt32(rdoItemType.EditValue) != (int)ItemType.Subtotal &&
                (txtPurchasePrice.EditValue == null || txtPurchasePrice.EditValue.ToString() == string.Empty
                || txtPurchasePrice.EditValue.ToString() == "0"))
            {
                tab_Control1.SelectedTabPage = tab_MainInfo;
                txtPurchasePrice.Focus();
                return false;
            }*/

            if (Convert.ToDecimal(txtPurchaseDiscRatio.Text) > 100)
            {
                tab_Control1.SelectedTabPage = tab_MainInfo;
                XtraMessageBox.Show(
                     Shared.IsEnglish == true ? ResICEn.txtValidateMaxDiscount : ResICAr.txtValidateMaxDiscount
                     , "", MessageBoxButtons.OK, MessageBoxIcon.Warning); txtItemCode1.Focus();
                txtPurchaseDiscRatio.Focus();
                return false;
            }

            if (Convert.ToDecimal(txtHeight.EditValue) <= 0)
            {
                tab_Control1.SelectedTabPage = tab_MainInfo;
                tabExtraData.SelectedTabPage = tabDimension;
                XtraMessageBox.Show(
                     Shared.IsEnglish == true ? ResICEn.MsgDimension : ResICAr.MsgDimension
                     , "", MessageBoxButtons.OK, MessageBoxIcon.Warning); txtItemCode1.Focus();
                txtHeight.Focus();
                return false;
            }
            if (Convert.ToDecimal(txtWidth.EditValue) <= 0)
            {
                tab_Control1.SelectedTabPage = tab_MainInfo;
                tabExtraData.SelectedTabPage = tabDimension;
                XtraMessageBox.Show(
                     Shared.IsEnglish == true ? ResICEn.MsgDimension : ResICAr.MsgDimension
                     , "", MessageBoxButtons.OK, MessageBoxIcon.Warning); txtItemCode1.Focus();
                txtWidth.Focus();
                return false;
            }
            if (Convert.ToDecimal(txtLength.EditValue) <= 0)
            {
                tab_Control1.SelectedTabPage = tab_MainInfo;
                tabExtraData.SelectedTabPage = tabDimension;
                XtraMessageBox.Show(
                     Shared.IsEnglish == true ? ResICEn.MsgDimension : ResICAr.MsgDimension
                     , "", MessageBoxButtons.OK, MessageBoxIcon.Warning); txtItemCode1.Focus();
                txtLength.Focus();
                return false;
            }

            /*if (Convert.ToInt32(rdoItemType.EditValue) != (int)ItemType.Subtotal && 
                (txtSmallUOMPrice.EditValue == null || txtSmallUOMPrice.EditValue.ToString() == string.Empty
                || txtSmallUOMPrice.EditValue.ToString() == "0"))
            {
                tab_Control1.SelectedTabPage = tab_MainInfo;
                txtSmallUOMPrice.Focus();
                return false;
            }*/

            if (Convert.ToInt32(rdoItemType.EditValue) != (int)ItemType.Subtotal && lkpSmallUOM.EditValue == null)
            {
                tab_Control1.SelectedTabPage = tab_MainInfo;
                lkpSmallUOM.Focus();
                return false;
            }

            #region UOM
            //===========================SAMAR=========================================//
            //========One Unit=========================================================//
            if((lkpSmallUOM.EditValue!=null&&lkpMediumUOM.EditValue==null&& lkpLargeUOM.EditValue == null)
                || (lkpMediumUOM.EditValue != null && lkpSmallUOM.EditValue == null && lkpLargeUOM.EditValue == null)||
                (lkpLargeUOM.EditValue != null && lkpSmallUOM.EditValue == null && lkpLargeUOM.EditValue == null)
                )
            {
                if (Convert.ToBoolean(chk_SmallIsStopped.EditValue) == true)
                {
                    XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResEn.ValidUom : ResAr.ValidUom
                  , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    chk_SmallIsStopped.Focus();
                    return false;
                }

                if (Convert.ToBoolean(chk_MediumIsStopped.EditValue) == true)
                {
                    XtraMessageBox.Show(
                      Shared.IsEnglish == true ? ResEn.ValidUom : ResAr.ValidUom
                    , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    chk_MediumIsStopped.Focus();
                    return false;
                }

                if (Convert.ToBoolean(chk_LargeIsStopped.EditValue) == true)
                {
                    XtraMessageBox.Show(
                   Shared.IsEnglish == true ? ResEn.ValidUom : ResAr.ValidUom
                 , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    chk_LargeIsStopped.Focus();
                    return false;
                }
            }
            //==========Two Unit===========================================================//
            if ((lkpSmallUOM.EditValue != null && lkpMediumUOM.EditValue != null && lkpLargeUOM.EditValue == null)
               || (lkpMediumUOM.EditValue == null && lkpSmallUOM.EditValue != null && lkpLargeUOM.EditValue != null) ||
               (lkpLargeUOM.EditValue != null && lkpSmallUOM.EditValue == null && lkpLargeUOM.EditValue != null)
               )
            {
                if (Convert.ToBoolean(chk_SmallIsStopped.EditValue) == true&&Convert.ToBoolean(chk_MediumIsStopped.EditValue) == true)
                {
                    XtraMessageBox.Show(
                     Shared.IsEnglish == true ? ResEn.ValidUom : ResAr.ValidUom
                      , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    chk_MediumIsStopped.Focus();
                    return false;
                }

                if (Convert.ToBoolean(chk_MediumIsStopped.EditValue) == true&& Convert.ToBoolean(chk_LargeIsStopped.EditValue) == true)
                {
                     XtraMessageBox.Show(
                     Shared.IsEnglish == true ? ResEn.ValidUom : ResAr.ValidUom
                      , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    chk_MediumIsStopped.Focus();
                    return false;
                }

                if (Convert.ToBoolean(chk_LargeIsStopped.EditValue) == true&& Convert.ToBoolean(chk_SmallIsStopped.EditValue))
                {
                    XtraMessageBox.Show(
                       Shared.IsEnglish == true ? ResEn.ValidUom : ResAr.ValidUom
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    chk_LargeIsStopped.Focus();
                    return false;
                }
            }
            //==========Tree Unit===========================================================//
            if (lkpSmallUOM.EditValue != null && lkpMediumUOM.EditValue != null && lkpLargeUOM.EditValue != null)                  
            {
                if (Convert.ToBoolean(chk_SmallIsStopped.EditValue) == true && Convert.ToBoolean(chk_MediumIsStopped.EditValue) == true
                    && Convert.ToBoolean(chk_LargeIsStopped.EditValue) == true)
                {
                    XtraMessageBox.Show(
                      Shared.IsEnglish == true ? ResEn.ValidUom : ResAr.ValidUom
                       , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }

              
            }
            //===================================================================//
            if (txtLargeUOMFactor.Text.Trim() != string.Empty && txtMediumUOMFactor.Text.Trim() == string.Empty)
            {
                XtraMessageBox.Show(
                     Shared.IsEnglish == true ? ResICEn.ValTxtItemUOM1 : ResICAr.ValTxtItemUOM1
                     , "", MessageBoxButtons.OK, MessageBoxIcon.Warning); txtItemCode1.Focus();

                tab_Control1.SelectedTabPage = tab_MainInfo;
                txtMediumUOMFactor.Focus();
                return false;
            }

            if (Convert.ToInt32(rdoItemType.EditValue) != (int)ItemType.Subtotal && lkpMediumUOM.EditValue != null)
            {
                if (string.IsNullOrEmpty(txtMediumUOMFactor.Text))
                {
                    tab_Control1.SelectedTabPage = tab_MainInfo;
                    txtMediumUOMFactor.Focus();
                    return false;
                }
                if (string.IsNullOrEmpty(txtMediumUOMPrice.Text))
                {
                    tab_Control1.SelectedTabPage = tab_MainInfo;
                    txtMediumUOMPrice.Focus();
                    return false;
                }
                if (Convert.ToDecimal(txtMediumUOMPrice.Text) == 0)
                {
                    //tab_Control1.SelectedTabPage = tab_MainInfo;
                    //txtMediumUOMPrice.Focus();
                    //return false;
                }
                if (txtMediumUOMFactor.Text.Contains('/'))
                {
                    try
                    {
                        decimal nominator = Convert.ToDecimal(txtMediumUOMFactor.Text.Substring(0, txtMediumUOMFactor.Text.IndexOf('/')));
                        decimal denominator = Convert.ToDecimal(txtMediumUOMFactor.Text.Substring(txtMediumUOMFactor.Text.IndexOf('/') + 1));

                        if (nominator == 0 || denominator == 0)
                        {
                            tab_Control1.SelectedTabPage = tab_MainInfo;
                            txtMediumUOMFactor.Focus();
                            return false;
                        }
                    }
                    catch
                    {
                        tab_Control1.SelectedTabPage = tab_MainInfo;
                        txtMediumUOMFactor.Focus();
                        return false;
                    }
                }
                else
                {
                    decimal MediumUOMFactor = 0;
                    bool parsed = decimal.TryParse(txtMediumUOMFactor.Text.Trim(), out MediumUOMFactor);
                    if (!parsed)
                    {
                        tab_Control1.SelectedTabPage = tab_MainInfo;
                        txtMediumUOMFactor.Focus();
                        return false;
                    }
                    else
                    {
                        if (Convert.ToDecimal(txtMediumUOMFactor.Text) == 0)
                        {
                            tab_Control1.SelectedTabPage = tab_MainInfo;
                            txtMediumUOMFactor.Focus();
                            return false;
                        }
                    }
                }
            }

            if (Convert.ToInt32(rdoItemType.EditValue) != (int)ItemType.Subtotal && lkpLargeUOM.EditValue != null)
            {
                if (string.IsNullOrEmpty(txtLargeUOMFactor.Text))
                {
                    tab_Control1.SelectedTabPage = tab_MainInfo;
                    txtLargeUOMFactor.Focus();
                    return false;
                }
                if (string.IsNullOrEmpty(txtLargeUOMPrice.Text))
                {
                    tab_Control1.SelectedTabPage = tab_MainInfo;
                    txtLargeUOMPrice.Focus();
                    return false;
                }
                if (Convert.ToDecimal(txtLargeUOMPrice.Text) == 0)
                {
                    //tab_Control1.SelectedTabPage = tab_MainInfo;
                    //txtLargeUOMPrice.Focus();
                    //return false;
                }

                if (txtLargeUOMFactor.Text.Contains('/'))
                {
                    try
                    {
                        decimal nominator = Convert.ToDecimal(txtLargeUOMFactor.Text.Substring(0, txtLargeUOMFactor.Text.IndexOf('/')));
                        decimal denominator = Convert.ToDecimal(txtLargeUOMFactor.Text.Substring(txtLargeUOMFactor.Text.IndexOf('/') + 1));

                        if (nominator == 0 || denominator == 0)
                        {
                            tab_Control1.SelectedTabPage = tab_MainInfo;
                            txtLargeUOMFactor.Focus();
                            return false;
                        }
                    }
                    catch
                    {
                        tab_Control1.SelectedTabPage = tab_MainInfo;
                        txtLargeUOMFactor.Focus();
                        return false;
                    }
                }
                else
                {
                    decimal LargeUOMFactor = 0;
                    bool parsed = decimal.TryParse(txtLargeUOMFactor.Text.Trim(), out LargeUOMFactor);
                    if (!parsed)
                    {
                        tab_Control1.SelectedTabPage = tab_MainInfo;
                        txtLargeUOMFactor.Focus();
                        return false;
                    }
                    else
                    {
                        if (Convert.ToDecimal(txtLargeUOMFactor.Text) == 0)
                        {
                            tab_Control1.SelectedTabPage = tab_MainInfo;
                            txtLargeUOMFactor.Focus();
                            return false;
                        }
                    }
                }

            }

            #endregion


            #region item prices per Qty, matrix , vendors

            gv_SalesPerQty.CloseEditor();
            gv_SalesPerQty.UpdateCurrentRow();
            gv_Matrix.CloseEditor();
            gv_Matrix.UpdateCurrentRow();
            gv_Vendors.CloseEditor();
            gv_Vendors.UpdateCurrentRow();

            bool salesPerQtyErrorExist = false;
            for (int x = 0; x < gv_SalesPerQty.RowCount; x++)
            {
                if (gv_SalesPerQty.GetRowCellValue(x, col_QtyFrom) == null &&
                    gv_SalesPerQty.GetRowCellValue(x, col_QtyTo) == null &&
                    gv_SalesPerQty.GetRowCellValue(x, col_SellPrice) == null)
                {
                    gv_SalesPerQty.DeleteRow(x);
                    continue;
                }

                if (Convert.ToDecimal(gv_SalesPerQty.GetRowCellValue(x, col_QtyFrom)) <= 0 ||
                    Convert.ToDecimal(gv_SalesPerQty.GetRowCellValue(x, col_QtyTo)) <= 0 ||
                    Convert.ToDecimal(gv_SalesPerQty.GetRowCellValue(x, col_SellPrice)) <= 0)
                {
                    salesPerQtyErrorExist = true;
                }
            }
            if (salesPerQtyErrorExist)
            {
                XtraMessageBox.Show(
                Shared.IsEnglish == true ? ResICEn.MsgSalePerQty : ResICAr.MsgSalePerQty
                    , "", MessageBoxButtons.OK, MessageBoxIcon.Warning); txtItemCode1.Focus();
                tab_Control1.SelectedTabPage = tab_PricesPerQty;
                return false;
            }

            bool mtrxErrorExist = false;
            for (int x = 0; x < gv_Matrix.RowCount; x++)
            {
                if (Convert.ToDecimal(gv_Matrix.GetRowCellValue(x, colHeight)) <= 0 ||
                    Convert.ToDecimal(gv_Matrix.GetRowCellValue(x, colWidth)) <= 0 ||
                    Convert.ToDecimal(gv_Matrix.GetRowCellValue(x, colLength)) <= 0)
                {
                    mtrxErrorExist = true;
                }
            }
            if (mtrxErrorExist)
            {
                XtraMessageBox.Show(
                Shared.IsEnglish == true ? ResICEn.MsgMtrxData : ResICAr.MsgMtrxData
                    , "", MessageBoxButtons.OK, MessageBoxIcon.Warning); txtItemCode1.Focus();
                tab_Control1.SelectedTabPage = tab_Matrix;
                return false;
            }
            #endregion

            if (chk_IsLibra.Checked)
            {
                if (lkpMediumUOM.EditValue == null)
                {
                    MessageBox.Show(Shared.IsEnglish ? "Make sure to select both the Medium UOM" : "تأكد من اختيار الوحدة الفرعية 1", Shared.IsEnglish ? "Warning" : "تحذير", MessageBoxButtons.OK);
                    lkpMediumUOM.Focus();
                    return false;
                }
                if (lkpLargeUOM.EditValue == null)
                {
                    MessageBox.Show(Shared.IsEnglish ? "Make sure to select both the Large UOM" : "تأكد من اختيار والوحدة الفرعية 2", Shared.IsEnglish ? "Warning" : "تحذير", MessageBoxButtons.OK);
                    lkpLargeUOM.Focus();
                    return false;
                }
            }

            if (Shared.LibraAvailabe)
            {
                if(cmb_WeightUnit.EditValue != null)
                {
                    switch (cmb_WeightUnit.EditValue.ToString())
                    {
                        case "1": break;
                        case "2":
                            if (string.IsNullOrEmpty(txtMediumUOMFactor.Text))
                            {

                                XtraMessageBox.Show(
                                     "يجب إضافة وزن وحدة فرعية 1"
                                     , "", MessageBoxButtons.OK, MessageBoxIcon.Warning); txtItemCode1.Focus();
                                return false;
                            }
                            break;
                        case "3":
                            if (string.IsNullOrEmpty(txtLargeUOMFactor.Text))
                            {

                                XtraMessageBox.Show(
                                     "يجب إضافة وزن وحدة فرعية 2"
                                     , "", MessageBoxButtons.OK, MessageBoxIcon.Warning); txtItemCode1.Focus();
                                return false;
                            }
                            break;
                        default: break;
                    }
                }
            }

            return true;
        }

        void ValidateIntCode()
        {
            if (string.IsNullOrEmpty(txtInternationalCode.Text) && txtInternationalCode.Properties.ReadOnly == false)
                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgInterCode : ResICAr.MsgInterCode);//"يرجى إدخال الكود الدولي بشكل صحيح"
            else
                if (!string.IsNullOrEmpty(txtInternationalCode.Text) && txtInternationalCode.Properties.ReadOnly == false)
            {
                string code = txtInternationalCode.Text.Trim();
                DAL.ERPDataContext pharm = new DAL.ERPDataContext();
                var isCodeExist = (from i in pharm.IC_InternationalCodes
                                   where i.InternationalCode == code
                                   select i).SingleOrDefault();

                if (isCodeExist == null && lstInternationalCodes.FindString(code) < 0)//this code never used before
                {
                    lstInternationalCodes.Items.Add(code);
                    //prepare a list of new International Codes to be saved
                    IC_InternationalCode IC = new IC_InternationalCode();
                    IC.InternationalCode = code;
                    NewInternationalCodes.Add(IC);

                    txtInternationalCode.Text = string.Empty;
                    txtInternationalCode.Properties.ReadOnly = true;
                }
                else//this Code has been used before for another Item
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgInterCodeExist : ResICAr.MsgInterCodeExist//"هذا الكود الدولي تم استخدامه مسبقا"
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtInternationalCode.Text = string.Empty;
                }
                btn_AddNewInter_Code.Focus();

            }
        }

        void BindDataSources()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            lstCategories = MyHelper.GetChildCategoriesList();

            lkpCategory.Properties.DisplayMember = "CategoryNameAr";
            lkpCategory.Properties.ValueMember = "CategoryId";
            if (lstCategories.FirstOrDefault() != null)
                lkpCategory.EditValue = lstCategories.FirstOrDefault().CategoryId;
            lkpCategory.Properties.DataSource = lstCategories;

            //var companies = (from i in DB.IC_Companies
            //                 select i).ToList();
            //lkpComp.Properties.DisplayMember = "CompanyNameAr";
            //lkpComp.Properties.ValueMember = "CompanyId";
            //if (companies.FirstOrDefault() != null)
            //    lkpComp.EditValue = companies.FirstOrDefault().CompanyId;
            //lkpComp.Properties.DataSource = companies;

            uom_list = (from u in new ERPDataContext().IC_UOMs
                        select u).ToList();
            lkpSmallUOM.Properties.DisplayMember = lkpMediumUOM.Properties.DisplayMember = lkpLargeUOM.Properties.DisplayMember = "UOM";
            lkpSmallUOM.Properties.ValueMember = lkpMediumUOM.Properties.ValueMember = lkpLargeUOM.Properties.ValueMember = "UOMId";
            lkpSmallUOM.EditValue = uom_list.FirstOrDefault().UOMId;
            lkpSmallUOM.Properties.DataSource = lkpMediumUOM.Properties.DataSource = lkpLargeUOM.Properties.DataSource = uom_list;

            //var attributes = (from i in DB.IC_MatrixDetails
            //                  select i).ToList();
            //rep_m_attribute.DisplayMember = "MDName";
            //rep_m_attribute.ValueMember = "MatrixDetailId";
            //rep_m_attribute.DataSource = attributes;

            //var vendors = (from v in DB.PR_Vendors
            //               select new
            //               {
            //                   v.VendorId,
            //                   v.VenNameAr
            //               }
            //                  ).ToList();
            //rep_Vendors.DisplayMember = "VenNameAr";
            //rep_Vendors.ValueMember = "VendorId";
            //rep_Vendors.DataSource = vendors;

            dt_PriceList.Columns.Add("PriceLevelId");
            dt_PriceList.Columns.Add("PLName");
            dt_PriceList.Columns.Add("smallUOMPrice");
            dt_PriceList.Columns.Add("MediumUOMPrice");
            dt_PriceList.Columns.Add("LargeUOMPrice");
            dt_PriceList.Columns.Add("ItemId");

            rep_stores.DataSource = DB.IC_Stores;
            rep_stores.ValueMember = "StoreId";
            rep_stores.DisplayMember = Shared.IsEnglish ? "StoreNameEn" : "StoreNameAr";
            #region SubTax
            lkp_SubTaxes.DataSource = DB.E_TaxableTypes.Where(x => x.ParentTaxId != null).ToList();
            lkp_SubTaxes.ValueMember = "E_TaxableTypeId";
            lkp_SubTaxes.DisplayMember = "DescriptionAr";
            dt_SubTax.Columns.Add("IC_ItemSubTaxesId");
            dt_SubTax.Columns.Add("SubTaxId");
            dt_SubTax.Columns.Add("ItemId");
            dt_SubTax.Columns.Add("Rate");
            grd_SubTaxes.DataSource = dt_SubTax;
            #endregion

        }

        private void GetItemForSave(IC_Item itm)
        {
            itm.ItemCode1 = Convert.ToInt32(txtItemCode1.Text);
            itm.ItemCode2 = string.IsNullOrEmpty(txtItemCode2.Text.Trim()) ? null : txtItemCode2.Text;
            itm.ItemNameAr = txtItemNameAr.Text;
            itm.ItemNameEn = string.IsNullOrEmpty(txtItemNameEn.Text.Trim()) ? "" : txtItemNameEn.Text;
            itm.Description = string.IsNullOrEmpty(txtDesc.Text.Trim()) ? "" : txtDesc.Text;
            itm.DescriptionEn = string.IsNullOrEmpty(txtDescEn.Text.Trim()) ? "" : txtDescEn.Text;

            itm.ItemType = Convert.ToInt32(rdoItemType.EditValue);
            itm.IsExpire = chkIsExpire.Checked;
            itm.IsPos = chk_IsPos.Checked;
            itm.IsDeleted = chk_IsDeleted.Checked;
            itm.Category = Convert.ToInt32(lkpCategory.EditValue);
            itm.Company = Convert.ToInt32(lkpComp.EditValue);

            itm.ChangePriceMethod = Convert.ToByte(cmbChangePriceMethod.EditValue);
            itm.ChangeSellPrice = Convert.ToBoolean(cmbChangeSellPrice.EditValue);

            if (Convert.ToInt32(rdoItemType.EditValue) == (int)ItemType.Subtotal)
            {
                txtPurchasePrice.EditValue = itm.PurchasePrice = 0;
                txtAudiancePrice.EditValue = itm.AudiencePrice = 0;
                txtPurchaseDiscRatio.EditValue = itm.PurchaseDiscRatio = 0;
                txtSalesDiscRatio.EditValue = itm.SalesDiscRatio = 0;
                txtPurchaseTaxValue.EditValue = itm.PurchaseTaxValue = 0;
                txtPurchaseTaxRatio.EditValue = itm.PurchaseTaxRatio = 0;
                txtCustomPurchasesTaxRatio.EditValue = itm.CustomPurchasesTaxRatio = 0;
                txtSalesTaxValue.EditValue = itm.SalesTaxValue = 0;
                txtSalesTaxRatio.EditValue = itm.SalesTaxRatio = 0;
                txtCustomSalesTaxRatio.EditValue = itm.CustomSalesTaxRatio = 0;
                chk_calcTaxBeforeDisc.Checked = itm.calcTaxBeforeDisc = false;
                txt_WarrantyMonths.EditValue = itm.WarrantyMonths = 0;

                txtHeight.EditValue = itm.Height = 1;
                txtWidth.EditValue = itm.Width = 1;
                txtLength.EditValue = itm.Length = 1;

                itm.SmallUOM = Convert.ToByte(lkpSmallUOM.EditValue);
                txtSmallUOMPrice.EditValue = itm.SmallUOMPrice = 0;

                itm.MediumUOM = null;
                itm.MediumUOMFactor = null;
                itm.MediumUOMFactorDecimal = null;
                itm.MediumUOMPrice = null;
                itm.MediumUOMCode = null;

                itm.LargeUOM = null;
                itm.LargeUOMFactor = null;
                itm.LargeUOMFactorDecimal = null;
                itm.LargeUOMPrice = null;
                itm.LargeUOMCode = null;

                itm.DfltSellUomIndx = itm.DfltPrchsUomIndx = (byte)0;

                itm.ReorderLevel = itm.MaxQty = itm.MinQty = 0;
            }
            else
            {
                itm.PurchasePrice = Convert.ToDecimal(txtPurchasePrice.Text);
                if (!string.IsNullOrEmpty(txtAudiancePrice.Text))
                    itm.AudiencePrice = Convert.ToDecimal(txtAudiancePrice.Text);
                itm.PurchaseDiscRatio = Convert.ToDecimal(txtPurchaseDiscRatio.Text);
                itm.SalesDiscRatio = Convert.ToDecimal(txtSalesDiscRatio.Text);
                itm.PurchaseTaxValue = Convert.ToDecimal(txtPurchaseTaxValue.Text);
                itm.PurchaseTaxRatio = Convert.ToDecimal(txtPurchaseTaxRatio.Text);
                itm.CustomPurchasesTaxRatio = Convert.ToDecimal(txtCustomPurchasesTaxRatio.Text);
                itm.SalesTaxValue = Convert.ToDecimal(txtSalesTaxValue.Text);
                itm.SalesTaxRatio = Convert.ToDecimal(txtSalesTaxRatio.Text);
                itm.CustomSalesTaxRatio = Convert.ToDecimal(txtCustomSalesTaxRatio.Text);
                itm.calcTaxBeforeDisc = chk_calcTaxBeforeDisc.Checked;

                itm.Height = Convert.ToDecimal(txtHeight.EditValue);
                itm.Width = Convert.ToDecimal(txtWidth.EditValue);
                itm.Length = Convert.ToDecimal(txtLength.EditValue);

                itm.WarrantyMonths = Convert.ToInt32(txt_WarrantyMonths.EditValue);

                itm.is_libra = chk_IsLibra.Checked;
                itm.VariableWeight = chk_VariableWeight.Checked;
                itm.PricingWithSmall = chk_PricingWithSmall.Checked;

                #region UOM Data
                itm.SmallUOM = Convert.ToByte(lkpSmallUOM.EditValue);

                itm.SmallUOMPrice = Convert.ToDecimal(txtSmallUOMPrice.Text);
                if (chk_SmallIsStopped.EditValue != null)
                    itm.SmallIsStopped =Convert.ToBoolean(chk_SmallIsStopped.EditValue);
                if (lkpMediumUOM.EditValue != null)
                {
                    itm.MediumUOM = Convert.ToByte(lkpMediumUOM.EditValue);
                    itm.MediumUOMFactor = txtMediumUOMFactor.Text.Trim();
                    itm.MediumUOMPrice = Convert.ToDecimal(txtMediumUOMPrice.Text);
                    itm.MediumUOMFactorDecimal = MyHelper.FractionToDouble(itm.MediumUOMFactor);
                    itm.MediumUOMCode = string.IsNullOrEmpty(txt_MediumUOMCode.Text.Trim()) ? null : txt_MediumUOMCode.Text;
                    if (chk_MediumIsStopped.EditValue != null)
                        itm.MediumIsStopped = Convert.ToBoolean(chk_MediumIsStopped.EditValue);
                }
                else
                {
                    itm.MediumUOM = null;
                    itm.MediumUOMFactor = null;
                    itm.MediumUOMFactorDecimal = null;
                    itm.MediumUOMPrice = null;
                    itm.MediumUOMCode = null;
                    itm.MediumIsStopped = null;
                }

                if (lkpLargeUOM.EditValue != null)
                {
                    itm.LargeUOM = Convert.ToByte(lkpLargeUOM.EditValue);
                    itm.LargeUOMFactor = txtLargeUOMFactor.Text.Trim();
                    itm.LargeUOMPrice = Convert.ToDecimal(txtLargeUOMPrice.Text);
                    itm.LargeUOMFactorDecimal = MyHelper.FractionToDouble(itm.LargeUOMFactor);
                    itm.LargeUOMCode = string.IsNullOrEmpty(txt_LargeUOMCode.Text.Trim()) ? null : txt_LargeUOMCode.Text;
                    if (chk_LargeIsStopped.EditValue != null)
                        itm.LargeIsStopped = Convert.ToBoolean(chk_LargeIsStopped.EditValue);
                }
                else
                {
                    itm.LargeUOM = null;
                    itm.LargeUOMFactor = null;
                    itm.LargeUOMFactorDecimal = null;
                    itm.LargeUOMPrice = null;
                    itm.LargeUOMCode = null;
                    itm.LargeIsStopped = null;
                }

                if (rdoSellUOM0.Checked)
                    itm.DfltSellUomIndx = (byte)0;
                else if (rdoSellUOM1.Checked)
                    itm.DfltSellUomIndx = (byte)1;
                else if (rdoSellUOM2.Checked)
                    itm.DfltSellUomIndx = (byte)2;

                if (rdoPrchsUOM0.Checked)
                    itm.DfltPrchsUomIndx = (byte)0;
                else if (rdoPrchsUOM1.Checked)
                    itm.DfltPrchsUomIndx = (byte)1;
                else if (rdoPrchsUOM2.Checked)
                    itm.DfltPrchsUomIndx = (byte)2;

                itm.ReorderLevel = Convert.ToInt32(txtReorder.Text);
                itm.MaxQty = Convert.ToInt32(txtMaxQty.Text);
                itm.MinQty = Convert.ToInt32(txtMinQty.Text);
                #endregion
            }

            //image
            itm.PicPath = picPath;
            if (cmb_WeightUnit.EditValue == null)
                itm.DfltWeightUnit = null;
            else
                itm.DfltWeightUnit = Convert.ToInt32(cmb_WeightUnit.EditValue);

        }

        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.Category).Count() < 1)
                {
                    btnAddCat.Enabled = false;
                }
                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.Company).Count() < 1)
                {
                    btnAddComp.Enabled = false;
                }
                prvlg = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.Item).FirstOrDefault();

                if (!prvlg.CanDel)
                    barBtnDelete.Enabled = false;
                if (!prvlg.CanAdd)
                    barBtnNew.Enabled = false;
            }
        }

        private void txt_WorkedHours_EditValueChanged(object sender, EventArgs e)
        {
            if (Convert.ToDecimal((sender as SpinEdit).EditValue) < 0)
                (sender as SpinEdit).EditValue = 0;
        }

        private void barBtnHelp_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "الأصناف");
        }

        #region Photos

        private void btnAddPicture_Click(object sender, EventArgs e)
        {
            OpenFileDialog OFD = new OpenFileDialog();
            OFD.Filter = "Images|*.jpg;*.jpeg;*.png;*.bmp;*.gif";
            if (OFD.ShowDialog() == DialogResult.OK && OFD.FileName != string.Empty)
            {

                try
                {
                    System.IO.FileInfo f = new System.IO.FileInfo(OFD.FileName);
                    if (f.Length > (10240 * 1024))
                    {
                        MessageBox.Show(
                            Shared.IsEnglish == true ? ResHREn.PicSize : ResHRAr.PicSize//"لا يمكن تحميل صوره اكبر من 10 ميجابايت"
                            , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    picPath = OFD.FileName;
                }
                catch
                {

                }
                itemPhoto.Image = Image.FromFile(picPath);
                DataModified = true;
            }
            else
                return;
        }

        private void btnRemovePic_Click(object sender, EventArgs e)
        {
            itemPhoto.Image = null;
            picPath = null;
            DataModified = true;
        }
        #endregion

        #region Sales Prices Per Quantity

        private void rep_SLQtyNums_Spin(object sender, SpinEventArgs e)
        {
            e.Handled = true;
        }

        private void grd_SalesPerQty_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Delete && e.Modifiers == Keys.Control)
            {
                if (MessageBox.Show(
                Shared.IsEnglish == true ? ResAccEn.MsgDelRow : ResAccAr.MsgDelRow, //"حذف صف ؟"
                Shared.IsEnglish == true ? ResICEn.MsgTQues : ResICAr.MsgTQues,
                MessageBoxButtons.YesNo, MessageBoxIcon.Question) !=
              DialogResult.Yes)
                    return;

                GridView view = grd_SalesPerQty.FocusedView as GridView;
                view.DeleteRow(view.FocusedRowHandle);
            }
        }


        private void grd_SalesPerQty_ProcessGridKey(object sender, KeyEventArgs e)
        {
            try
            {
                DevExpress.XtraGrid.GridControl grid = sender as DevExpress.XtraGrid.GridControl;
                var view = (grid.FocusedView as DevExpress.XtraGrid.Views.Grid.GridView);
                if (e.KeyCode == Keys.Enter)
                {
                    var focused_column = (grid.FocusedView as DevExpress.XtraGrid.Views.Base.ColumnView).FocusedColumn;
                    int focused_row_handle = (grid.FocusedView as DevExpress.XtraGrid.Views.Base.ColumnView).FocusedRowHandle;

                    if (view.FocusedColumn == col_QtyFrom)
                    {
                        view.FocusedColumn = col_QtyTo;
                        return;
                    }
                    if (view.FocusedColumn == col_QtyTo)
                    {
                        view.FocusedColumn = col_SellPrice;
                        return;
                    }
                    if (view.FocusedColumn == col_SellPrice)
                    {
                        grd_SalesPerQty_ProcessGridKey(sender, new KeyEventArgs(Keys.Tab));
                    }

                    if (view.FocusedRowHandle < 0)//|| view.FocusedRowHandle == view.RowCount)
                    {
                        view.AddNewRow();
                        view.FocusedColumn = col_QtyFrom;
                    }
                    else
                    {
                        view.FocusedRowHandle = focused_row_handle + 1;
                        view.FocusedColumn = focused_column;
                    }

                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == Keys.Tab && e.Modifiers != Keys.Shift)
                {
                    if (view.FocusedColumn.VisibleIndex == 0)
                        view.FocusedColumn = view.VisibleColumns[view.VisibleColumns.Count - 1];
                    else
                        view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex - 1];
                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == Keys.Tab && e.Modifiers == Keys.Shift)
                {
                    if (view.FocusedColumn.VisibleIndex == view.VisibleColumns.Count)
                        view.FocusedColumn = view.VisibleColumns[0];
                    else
                        view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex + 1];
                    e.Handled = true;
                    return;
                }
            }
            catch { }
        }
        #endregion

        #region Vendors

        private void grd_Vendors_KeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                if (e.KeyCode == Keys.Delete && e.Modifiers == Keys.Control)
                {
                    if (MessageBox.Show(
                    Shared.IsEnglish == true ? ResAccEn.MsgDelRow : ResAccAr.MsgDelRow, //"حذف صف ؟"
                    Shared.IsEnglish == true ? ResICEn.MsgTQues : ResICAr.MsgTQues,
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) !=
                  DialogResult.Yes)
                        return;

                    gv_Vendors.DeleteRow(gv_Vendors.FocusedRowHandle);
                }

                if (e.KeyCode == Keys.Enter)
                {
                    var focused_column = gv_Vendors.FocusedColumn;
                    int focused_row_handle = gv_Vendors.FocusedRowHandle;

                    if (gv_Vendors.FocusedColumn == col_Vnd_VendorId)
                    {
                        gv_Vendors.FocusedColumn = col_Vnd_PurchasePrice;
                        return;
                    }
                    if (gv_Vendors.FocusedColumn == col_Vnd_PurchasePrice)
                    {
                        grd_Vendors_KeyDown(sender, new KeyEventArgs(Keys.Tab));
                    }

                    if (gv_Vendors.FocusedRowHandle < 0)//|| view.FocusedRowHandle == view.RowCount)
                    {
                        gv_Vendors.AddNewRow();
                        gv_Vendors.FocusedColumn = col_QtyFrom;
                    }
                    else
                    {
                        gv_Vendors.FocusedRowHandle = focused_row_handle + 1;
                        gv_Vendors.FocusedColumn = focused_column;
                    }

                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == Keys.Tab && e.Modifiers != Keys.Shift)
                {
                    if (gv_Vendors.FocusedColumn.VisibleIndex == 0)
                        gv_Vendors.FocusedColumn = gv_Vendors.VisibleColumns[gv_Vendors.VisibleColumns.Count - 1];
                    else
                        gv_Vendors.FocusedColumn = gv_Vendors.VisibleColumns[gv_Vendors.FocusedColumn.VisibleIndex - 1];
                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == Keys.Tab && e.Modifiers == Keys.Shift)
                {
                    if (gv_Vendors.FocusedColumn.VisibleIndex == gv_Vendors.VisibleColumns.Count)
                        gv_Vendors.FocusedColumn = gv_Vendors.VisibleColumns[0];
                    else
                        gv_Vendors.FocusedColumn = gv_Vendors.VisibleColumns[gv_Vendors.FocusedColumn.VisibleIndex + 1];
                    e.Handled = true;
                    return;
                }
            }
            catch { }
        }


        private void gv_Vendors_ValidateRow(object sender, DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs e)
        {
            if (gv_Vendors.GetRowCellValue(e.RowHandle, "VendorId").ToString() != string.Empty)
            {
                for (int i = 0; i < gv_Vendors.RowCount - 1; i++)
                {
                    if (e.RowHandle == i)
                        continue;
                    if (gv_Vendors.GetRowCellValue(i, "VendorId").ToString() ==
                        gv_Vendors.GetRowCellValue(e.RowHandle, "VendorId").ToString())
                    {
                        e.Valid = false;
                        gv_Vendors.SetColumnError(gv_Vendors.Columns["VendorId"], Shared.IsEnglish ? ResICEn.VendorExist : ResICAr.VendorExist);
                        return;
                    }
                }
            }
        }

        private void gv_Vendors_InvalidRowException(object sender, DevExpress.XtraGrid.Views.Base.InvalidRowExceptionEventArgs e)
        {
            e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.NoAction;
        }
        #endregion

        #region Matrix
        private void btn_GenMtrx_Click(object sender, EventArgs e)
        {
            if (action == FormAction.Edit &&
                Convert.ToInt32(rdoItemType.EditValue) == (int)ItemType.MatrixParent)
            {
                new frm_IC_ItemMatrixCreate(
                    new ERPDataContext().IC_Items.Where(i => i.ItemId == ItemID).Single()).ShowDialog();
                LoadMatrixGrid();
            }
            else
                MessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgSaveItemFirst : ResICAr.MsgSaveItemFirst,
                    Shared.IsEnglish == true ? ResICEn.MsgTInfo : ResICAr.MsgTInfo
                    , MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void LoadMatrixGrid()
        {
            ERPDataContext DB = new ERPDataContext();
            var item = DB.IC_Items.Where(i => i.ItemId == ItemID).First();

            if (item == null)
                return;

            var metrices = DB.IC_Matrixes.ToList();
            grd_Mtrx.DataSource = DdBind.IC_Items.Where(b => b.mtrxParentItem == ItemID);
            grd_Mtrx.RefreshDataSource();

            #region rename attributes captions
            if (item.mtrxId1 != null)
            {
                col_m_Attribute1.Caption = metrices.Where(i => i.MatrixId == item.mtrxId1).Select(i => i.MatrixName).First();
                col_m_Attribute1.Visible = true;
                //col_m_Attribute1.VisibleIndex = 0;
            }
            else
                col_m_Attribute1.Visible = false;

            if (item.mtrxId2 != null)
            {
                col_m_Attribute2.Caption = metrices.Where(i => i.MatrixId == item.mtrxId2).Select(i => i.MatrixName).First();
                col_m_Attribute2.Visible = true;
                //col_m_Attribute2.VisibleIndex = 1;
            }
            else
                col_m_Attribute2.Visible = false;

            if (item.mtrxId3 != null)
            {
                col_m_Attribute3.Caption = metrices.Where(i => i.MatrixId == item.mtrxId3).Select(i => i.MatrixName).First();
                col_m_Attribute3.Visible = true;
                //col_m_Attribute3.VisibleIndex = 2;
            }
            else
                col_m_Attribute3.Visible = false;
            #endregion

            #region enable & disable UOM Columns
            if (item.MediumUOM == null)
                col_m_MediumUOMPrice.OptionsColumn.AllowEdit = false;
            else
                col_m_MediumUOMPrice.OptionsColumn.AllowEdit = true;

            if (item.LargeUOM == null)
                col_m_LargeUOMPrice.OptionsColumn.AllowEdit = false;
            else
                col_m_LargeUOMPrice.OptionsColumn.AllowEdit = true;
            #endregion
        }

        private void tab_Matrix_Enter(object sender, EventArgs e)
        {
            if (action == FormAction.Edit &&
                Convert.ToInt32(rdoItemType.EditValue) == (int)ItemType.MatrixParent)
                LoadMatrixGrid();
        }

        #endregion

        private void txtPurchaseTaxRatio_Leave(object sender, EventArgs e)
        {
            if (((SpinEdit)sender).Name == "txtPurchasePrice")
                txtPurchaseTaxValue.Text = (Convert.ToDouble(txtPurchaseTaxRatio.Text) / 100 * Convert.ToDouble(txtPurchasePrice.Text)).ToString();

            if (((SpinEdit)sender).Name == "txtPurchaseTaxRatio")
                txtPurchaseTaxValue.Text = (Convert.ToDouble(txtPurchaseTaxRatio.Text) / 100 * Convert.ToDouble(txtPurchasePrice.Text)).ToString();
            if (((SpinEdit)sender).Name == "txtPurchaseTaxValue")
                txtPurchaseTaxRatio.Text = (Convert.ToDouble(txtPurchaseTaxValue.Text) / Convert.ToDouble(txtPurchasePrice.Text) * 100).ToString();

            if (((SpinEdit)sender).Name == "txtSmallUOMPrice")
                txtSalesTaxValue.Text = (Convert.ToDouble(txtSalesTaxRatio.Text) / 100 * Convert.ToDouble(txtSmallUOMPrice.Text)).ToString();

            if (((SpinEdit)sender).Name == "txtSalesTaxRatio")
                txtSalesTaxValue.Text = (Convert.ToDouble(txtSalesTaxRatio.Text) / 100 * Convert.ToDouble(txtSmallUOMPrice.Text)).ToString();
            if (((SpinEdit)sender).Name == "txtSalesTaxValue")
                txtSalesTaxRatio.Text = (Convert.ToDouble(txtSalesTaxValue.Text) / Convert.ToDouble(txtSmallUOMPrice.Text) * 100).ToString();
        }

        private void lkpCategory_EditValueChanged(object sender, EventArgs e)
        {
            if (action == FormAction.Add && Shared.st_Store.EncodeItemsPerCategory)
            {
                txtItemCode1.Text = MyHelper.ItemNumGenerated(
                  lstCategories.Where(x => x.CategoryId == Convert.ToInt32(lkpCategory.EditValue)).First());
                txtItemCode2.Text = MyHelper.ItemNumGenerated(
                  lstCategories.Where(x => x.CategoryId == Convert.ToInt32(lkpCategory.EditValue)).First(), 1);
            }
        }

        private void chk_calcTaxBeforeDisc_EditValueChanging(object sender, ChangingEventArgs e)
        {
            if (LoadItemDataCompleted == false)
                return;

            // can't change inventory, assembly, service after usage in sales and purchases
            //can't change matrix parent if childs used in invoices

            if (action == FormAction.Edit)
            {
                ERPDataContext DB = new ERPDataContext();
                IC_Item item = DB.IC_Items.Where(x => x.ItemId == ItemID).First();

                //service or sutotal used before can't change
                if (
                    (item.ItemType == (int)ItemType.Inventory ||
                    item.ItemType == (int)ItemType.Assembly ||
                    item.ItemType == (int)ItemType.Service) &&
                    (DB.SL_InvoiceDetails.Where(i => i.ItemId == ItemID).Count() > 0 ||
                    DB.SL_QuoteDetails.Where(i => i.ItemId == ItemID).Count() > 0 ||
                    DB.SL_SalesOrderDetails.Where(i => i.ItemId == ItemID).Count() > 0 ||
                    DB.PR_InvoiceDetails.Where(i => i.ItemId == ItemID).Count() > 0 ||
                    DB.PR_PurchaseOrderDetails.Where(i => i.ItemId == ItemID).Count() > 0 ||
                    DB.PR_ReturnDetails.Where(i => i.ItemId == ItemID).Count() > 0 ||
                    DB.PR_QuoteDetails.Where(i => i.ItemId == ItemID).Count() > 0))
                {
                    e.Cancel = true;
                    return;
                }

                if (
                        (item.ItemType == (int)ItemType.MatrixParent) &&
                        (DB.SL_InvoiceDetails.Join(DB.IC_Items, d => d.ItemId, i => i.ItemId, (d, i) => new { d, i }).Where(x => x.i.mtrxParentItem == ItemID).Count() > 0 ||
                        DB.SL_QuoteDetails.Join(DB.IC_Items, d => d.ItemId, i => i.ItemId, (d, i) => new { d, i }).Where(x => x.i.mtrxParentItem == ItemID).Count() > 0 ||
                        DB.SL_SalesOrderDetails.Join(DB.IC_Items, d => d.ItemId, i => i.ItemId, (d, i) => new { d, i }).Where(x => x.i.mtrxParentItem == ItemID).Count() > 0 ||
                        DB.PR_InvoiceDetails.Join(DB.IC_Items, d => d.ItemId, i => i.ItemId, (d, i) => new { d, i }).Where(x => x.i.mtrxParentItem == ItemID).Count() > 0 ||
                        DB.PR_PurchaseOrderDetails.Join(DB.IC_Items, d => d.ItemId, i => i.ItemId, (d, i) => new { d, i }).Where(x => x.i.mtrxParentItem == ItemID).Count() > 0 ||
                        DB.PR_ReturnDetails.Join(DB.IC_Items, d => d.ItemId, i => i.ItemId, (d, i) => new { d, i }).Where(x => x.i.mtrxParentItem == ItemID).Count() > 0 ||
                        DB.PR_QuoteDetails.Join(DB.IC_Items, d => d.ItemId, i => i.ItemId, (d, i) => new { d, i }).Where(x => x.i.mtrxParentItem == ItemID).Count() > 0))
                {
                    e.Cancel = true;
                    return;
                }
            }
        }

        private void btn_MatrixPrint_Click(object sender, EventArgs e)
        {
            grd_Mtrx.MinimumSize = grd_Mtrx.Size;
            new Reports.rpt_Template(this.Text, "", txtItemCode1.Text + "-" + txtItemNameAr.Text, "", grd_Mtrx, false).ShowPreview();
            grd_Mtrx.MinimumSize = new Size(0, 0);
        }

        private void grdSlPLevel_DoubleClick(object sender, EventArgs e)
        {
            Open_Selected_PL();
        }


        private void Open_Selected_PL()
        {
            //var view = grdSlPLevel.FocusedView as GridView;
            //int focused_row_index = view.FocusedRowHandle;
            //if (focused_row_index < 0)
            //    return;

            //int priceLevelId = Convert.ToInt32(view.GetRowCellValue(focused_row_index, colPriceLevelId));

            //var itemid = view.GetFocusedRowCellValue("ItemId");
            //if (itemid == null || itemid == DBNull.Value) return;

            //if (Shared.LibraAvailabe)
            //{
            //    if (ErpUtils.IsFormOpen(typeof(frm_IC_PriceLibraLevel)))
            //        Application.OpenForms["frm_IC_PriceLibraLevel"].Close();
            //    new frm_IC_PriceLibraLevel(priceLevelId).ShowDialog();
            //}
            //else
            //{

            //    if (ErpUtils.IsFormOpen(typeof(frm_IC_PricelLevel)))
            //        Application.OpenForms["frm_IC_PricelLevel"].Close();
            //    new frm_IC_PricelLevel(priceLevelId, Convert.ToInt32(itemid)).ShowDialog();
            //}
        }

        private void gridView7_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {

        }

        private void gridView7_ValidatingEditor(object sender, BaseContainerValidateEditorEventArgs e)
        {
            ERPDataContext DB = new ERPDataContext();
            GridView view = grdSlPLevel.FocusedView as GridView;
            DataRow row = view.GetFocusedDataRow();

            int cellValue = Convert.ToInt32(row["PriceLevelId"]);
            var q = DB.IC_PriceLevels.Where(x => x.IsRatio).Where(i => i.PriceLevelId == cellValue).FirstOrDefault();
            if (q != null) e.Valid = false;
        }

        private void gridView7_InvalidValueException(object sender, InvalidValueExceptionEventArgs e)
        {
            ColumnView view = sender as ColumnView;
            if (view == null) return;
            e.ExceptionMode = ExceptionMode.DisplayError;
            e.WindowCaption = "خطأ";
            e.ErrorText = "لا يمكن التعديل على قائمة الاسعار بنسبة محددة";
            // Destroy the editor and discard the changes made within the edited cell.
            view.HideEditor();
        }

        private void grdSlPRLevel_DoubleClick(object sender, EventArgs e)
        {
            Open_Selected_PRL();
        }


        private void Open_Selected_PRL()
        {
            //var view = grdPrPLevel.FocusedView as GridView;
            //int focused_row_index = view.FocusedRowHandle;
            //if (focused_row_index < 0)
            //    return;

            //int PrPriceLevelId = Convert.ToInt32(view.GetRowCellValue(focused_row_index, colPrPriceLevelId));

            //var itemid = view.GetFocusedRowCellValue("ItemId");
            //if (itemid == null || itemid == DBNull.Value) return;

            //if (ErpUtils.IsFormOpen(typeof(frm_IC_PrPricelLevel)))
            //    Application.OpenForms["frm_IC_PrPricelLevel"].Close();
            //new frm_IC_PrPricelLevel(PrPriceLevelId, Convert.ToInt32(itemid)).ShowDialog();
        }

        private void gridView1_InitNewRow(object sender, InitNewRowEventArgs e)
        {
            GridView view = gridLocation.FocusedView as GridView;
            view.SetFocusedRowCellValue("ItemId", ItemID);
        }

        private void gridView1_ValidateRow(object sender, ValidateRowEventArgs e)
        {
            GridView view = gridLocation.FocusedView as GridView;
            var location = view.GetFocusedRowCellValue("Location");
            if (location == null || location == DBNull.Value || string.IsNullOrEmpty(location.ToString()))
            {
                e.ErrorText = Shared.IsEnglish ? "Insert the location" : "يجب إدخال موقع الصنف";
                e.Valid = false;
            }
        }

        private void chk_IsLibra_CheckedChanged(object sender, EventArgs e)
        {
            if (chk_IsLibra.Checked)
            {
                //if (lkpSmallUOM.EditValue == null)
                lkpSmallUOM.EditValue = Shared.st_Store.Libra_UOM_Id;
                //if (lkpMediumUOM.EditValue == null)
                {
                    lkpMediumUOM.EditValue = Shared.st_Store.KG_PR_UOM_Id;
                    rdoPrchsUOM1.Checked = true;
                    txtMediumUOMFactor.EditValue = Shared.st_Store.KG_PR_Factor;
                }
                //if (lkpLargeUOM.EditValue == null)
                {
                    lkpLargeUOM.EditValue = Shared.st_Store.KG_SL_UOM_Id;
                    rdoSellUOM2.Checked = true;
                    txtLargeUOMFactor.EditValue = Shared.st_Store.KG_SL_Factor;
                }
                chk_PricingWithSmall.Checked = chk_VariableWeight.Checked = false ;
                chk_PricingWithSmall.Enabled = chk_VariableWeight.Enabled = false;

            }
            else
            {
                lkpSmallUOM.EditValue = 1;
                lkpMediumUOM.EditValue = lkpLargeUOM.EditValue = null;

                txtMediumUOMFactor.EditValue = txtLargeUOMFactor.EditValue = null;
            }
        }

        private void chk_VariableWeight_CheckedChanged(object sender, EventArgs e)
        {
            if (chk_VariableWeight.Checked)
            {
                chk_PricingWithSmall.Checked = chk_IsLibra.Checked = false;
                chk_PricingWithSmall.Enabled = chk_IsLibra.Enabled = false;
            }
            else{
                chk_PricingWithSmall.Enabled = chk_IsLibra.Enabled = true;
            }
        }

        private void chk_PricingWithSmall_CheckedChanged(object sender, EventArgs e)
        {
            if (chk_PricingWithSmall.Checked)
            {
                chk_VariableWeight.Checked = chk_IsLibra.Checked = false;
                chk_VariableWeight.Enabled = chk_IsLibra.Enabled = false;
            }
            else{
                chk_VariableWeight.Enabled = chk_IsLibra.Enabled = true;
            }
        }

        private void chk_LargeIsStopped_CheckedChanged(object sender, EventArgs e)
        {

        }

        private void txtItemCode2_EditValueChanged(object sender, EventArgs e)
        {

        }

        private void grd_SubTaxes_Click(object sender, EventArgs e)
        {

        }

        private void gv_SubTaxes_ValidateRow(object sender, ValidateRowEventArgs e)

        {
            try
            {
                ColumnView view = sender as ColumnView;
               
                foreach (DataRow dr_p in dt_SubTax.Rows)
                {
                    
                    int SubTaxId = Convert.ToInt32(dr_p["SubTaxId"]);
                    
                   
                    if (view.GetRowCellValue(e.RowHandle, "SubTaxId") != null && view.GetRowCellValue(e.RowHandle, "SubTaxId") != DBNull.Value)
                    {
                        if (Convert.ToInt32(view.GetRowCellValue(e.RowHandle, "SubTaxId")) == SubTaxId && e.RowHandle!= dt_SubTax.Rows.IndexOf(dr_p))
                        {
                            e.Valid = false;
                            view.SetColumnError(view.Columns["SubTaxId"], Shared.IsEnglish ? "Please,SubTax Is Repeated " : "الضريبة مكررة");
                        }
                    }
                    
                }
                if (view.GetRowCellValue(e.RowHandle, "SubTaxId") == null || view.GetRowCellValue(e.RowHandle, "SubTaxId") == DBNull.Value)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["SubTaxId"], Shared.IsEnglish ? "Please,Insert SubTax " : " من فضلك ادخل الضريبة");

                }

                if (view.GetRowCellValue(e.RowHandle, "Rate") == null || view.GetRowCellValue(e.RowHandle, "Rate") == DBNull.Value)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["Rate"], Shared.IsEnglish ? "Please,Insert  Rate " : " من فضلك ادخل  النسبة ");

                }

            }
            catch
            {
                e.Valid = false;
            }
        }

        private void gv_SubTaxes_InvalidRowException(object sender, InvalidRowExceptionEventArgs e)
        {
            e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.NoAction;
        }

        private void gv_SubTaxes_InitNewRow(object sender, InitNewRowEventArgs e)
        {
            GridView view = sender as GridView;
            if (Shared.st_Store.E_AllowMoreThanTax==false|| Shared.st_Store.E_AllowMoreThanTax == null)
            {
                var rowNum = dt_SubTax.Rows.Count;
                if (rowNum == 1)
                {
                    gv_SubTaxes.OptionsBehavior.AllowAddRows = DefaultBoolean.False;
                    gv_SubTaxes.OptionsBehavior.CancelUpdate();
                    gv_SubTaxes.DeleteRow(view.FocusedRowHandle);
                    dt_SubTax.AcceptChanges();
                    //gv_SubTaxes.OptionsBehavior.ReadOnly = true;

                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ?" Don`t Allow Add More Than One Tax " :"لا يسمح بادخال اكتر من ضريبة فرعية للصنف"
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

            }
        }

        private void gv_SubTaxes_KeyDown(object sender, KeyEventArgs e)
        {
            GridView view = sender as GridView;
            ERPDataContext DB = new ERPDataContext();
            try
            {

                if (e.KeyCode == Keys.Delete && e.Modifiers == Keys.Control)
                {

                  
                  if (MessageBox.Show(
                  Shared.IsEnglish == true ? ResICEn.MsgDelRow : ResICAr.MsgDelRow, //"حذف صف ؟"
                  Shared.IsEnglish == true ? ResICEn.MsgTQues : ResICAr.MsgTQues,
                  MessageBoxButtons.YesNo, MessageBoxIcon.Question) !=
                  DialogResult.Yes)
                     return;
                  if (view.GetFocusedRowCellValue(SubTaxId) == null)
                     return;

                  else
                  {
                     view.DeleteRow(view.FocusedRowHandle);
                     MyHelper.UpdateST_UserLog(DB, "", "",
                   (int)FormAction.Delete, (int)FormsNames.Item);
                  }
                      
                
                }

            }
            catch (Exception exc) { MessageBox.Show(exc.Message); }
        }
    }
}