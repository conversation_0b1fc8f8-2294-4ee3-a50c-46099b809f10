﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;

namespace Pharmacy.Forms
{
    public partial class frm_InvoiceDiscs : XtraForm
    {
        Process ProcessId;

        public static decimal
            PRInv_DiscR1 = 0, PRInv_DiscR2 = 0, PRInv_DiscR3 = 0,
            PRRet_DiscR1 = 0, PRRet_DiscR2 = 0, PRRet_DiscR3 = 0,
            SLInv_DiscR1 = 0, SLInv_DiscR2 = 0, SLInv_DiscR3 = 0,
            SLRet_DiscR1 = 0, SLRet_DiscR2 = 0, SLRet_DiscR3 = 0;

        public frm_InvoiceDiscs(Process _ProcessId)
        {
            ProcessId = _ProcessId;

            if (ProcessId == Process.SellInvoice)
            {
                Form frm = Application.OpenForms["frm_SL_Invoice"];

                this.Location = new Point((frm.Width / 2 - this.Width) + frm.Location.X,
                    (frm.Location.Y) + 95);
            }
            if (ProcessId == Process.SellReturn)
            {
                Form frm = Application.OpenForms["frm_SL_Return"];

                this.Location = new Point((frm.Width / 2 - this.Width) + frm.Location.X,
                    (frm.Location.Y) + 70);
            }
            if (ProcessId == Process.PurchaseInvoice)
            {
                Form frm = Application.OpenForms["frm_PR_Invoice"];

                this.Location = new Point((frm.Width / 2 - this.Width) + frm.Location.X,
                    (frm.Location.Y) + 70);
            }
            if (ProcessId == Process.PurchaseReturn)
            {
                Form frm = Application.OpenForms["frm_PR_Return"];

                this.Location = new Point((frm.Width / 2 - this.Width) + frm.Location.X,
                    (frm.Location.Y) + 70);
            }

            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();

            if (ProcessId == Process.PurchaseInvoice)
            {
                txt_DiscR1.EditValue = PRInv_DiscR1;
                txt_DiscR2.EditValue = PRInv_DiscR2;
                txt_DiscR3.EditValue = PRInv_DiscR3;
            }

            if (ProcessId == Process.PurchaseReturn)
            {
                txt_DiscR1.EditValue = PRRet_DiscR1;
                txt_DiscR2.EditValue = PRRet_DiscR2;
                txt_DiscR3.EditValue = PRRet_DiscR3;
            }

            if (ProcessId == Process.SellInvoice)
            {
                txt_DiscR1.EditValue = SLInv_DiscR1;
                txt_DiscR2.EditValue = SLInv_DiscR2;
                txt_DiscR3.EditValue = SLInv_DiscR3;
            }

            if (ProcessId == Process.SellReturn)
            {
                txt_DiscR1.EditValue = SLRet_DiscR1;
                txt_DiscR2.EditValue = SLRet_DiscR2;
                txt_DiscR3.EditValue = SLRet_DiscR3;
            }
        }

        private void txt_DiscR1_EditValueChanged(object sender, EventArgs e)
        {
            if (ProcessId == Process.PurchaseInvoice)
                PRInv_DiscR1 = Convert.ToDecimal(txt_DiscR1.EditValue);

            if (ProcessId == Process.PurchaseReturn)
                PRRet_DiscR1 = Convert.ToDecimal(txt_DiscR1.EditValue);

            if (ProcessId == Process.SellInvoice)
                SLInv_DiscR1 = Convert.ToDecimal(txt_DiscR1.EditValue);

            if (ProcessId == Process.SellReturn)
                SLRet_DiscR1 = Convert.ToDecimal(txt_DiscR1.EditValue);
        }

        private void txt_DiscR2_EditValueChanged(object sender, EventArgs e)
        {
            if (ProcessId == Process.PurchaseInvoice)
                PRInv_DiscR2 = Convert.ToDecimal(txt_DiscR2.EditValue);

            if (ProcessId == Process.PurchaseReturn)
                PRRet_DiscR2 = Convert.ToDecimal(txt_DiscR2.EditValue);

            if (ProcessId == Process.SellInvoice)
                SLInv_DiscR2 = Convert.ToDecimal(txt_DiscR2.EditValue);

            if (ProcessId == Process.SellReturn)
                SLRet_DiscR2 = Convert.ToDecimal(txt_DiscR2.EditValue);
        }

        private void txt_DiscR3_EditValueChanged(object sender, EventArgs e)
        {
            if (ProcessId == Process.PurchaseInvoice)
                PRInv_DiscR3 = Convert.ToDecimal(txt_DiscR3.EditValue);

            if (ProcessId == Process.PurchaseReturn)
                PRRet_DiscR3 = Convert.ToDecimal(txt_DiscR3.EditValue);

            if (ProcessId == Process.SellInvoice)
                SLInv_DiscR3 = Convert.ToDecimal(txt_DiscR3.EditValue);

            if (ProcessId == Process.SellReturn)
                SLRet_DiscR3 = Convert.ToDecimal(txt_DiscR3.EditValue);
        }

        private void frm_InvoiceDiscs_FormClosing(object sender, FormClosingEventArgs e)
        {
            PRInv_DiscR1 = 0; PRInv_DiscR2 = 0; PRInv_DiscR3 = 0;
            PRRet_DiscR1 = 0; PRRet_DiscR2 = 0; PRRet_DiscR3 = 0;
            SLInv_DiscR1 = 0; SLInv_DiscR2 = 0; SLInv_DiscR3 = 0;
            SLRet_DiscR1 = 0; SLRet_DiscR2 = 0; SLRet_DiscR3 = 0;
        }

        private void txt_DiscR3_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter || e.KeyCode == Keys.Tab)
            {
                if (ProcessId == Process.SellInvoice)
                {
                    Application.OpenForms["frm_SL_Invoice"].BringToFront();
                }
                if (ProcessId == Process.SellReturn)
                {
                    Application.OpenForms["frm_SL_Return"].BringToFront();
                }
                if (ProcessId == Process.PurchaseInvoice)
                {
                    Application.OpenForms["frm_PR_Invoice"].BringToFront();
                }
                if (ProcessId == Process.PurchaseReturn)
                {
                    Application.OpenForms["frm_PR_Return"].BringToFront();
                }
            }
        }
    }
}
