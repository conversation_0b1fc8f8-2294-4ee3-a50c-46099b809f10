﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Base;
using DAL;
using DAL.Res;
using DevExpress.XtraPrinting;

namespace Pharmacy.Forms
{
    public partial class frm_Sl_Return_Add_Taxes : DevExpress.XtraEditors.XtraForm
    {
        ERPDataContext DB = new ERPDataContext();
        public DataTable Dt_Rows = new DataTable();
        DataTable temp = new DataTable();
        int invoiceId = 0;
         int ItemId = 0;
        int rowhandle = 0;
        decimal SellPrice = 0, Qty = 0;

        public frm_Sl_Return_Add_Taxes()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            grdPrInvoice.DataSource = Dt_Rows;
            Dt_Rows.AcceptChanges();
            GridColumn colCounter = gridView1.Columns.AddVisible("RowHandle");
            colCounter.Caption = "";
            colCounter.UnboundType = DevExpress.Data.UnboundColumnType.Integer;
            colCounter.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;

            gridView1.CustomUnboundColumnData += (sender, e) =>
            {
                GridView view = sender as GridView;
                if (e.Column.FieldName == "RowHandle" && e.IsGetData)
                    e.Value = view.GetRowHandle(e.ListSourceRowIndex) + 1;
            };
        }


        public frm_Sl_Return_Add_Taxes(int invoiceId, DataTable dt, int _ItemId, decimal _SellPrice, decimal _Qty,int _rowhandle)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
            GridColumn colCounter = gridView1.Columns.AddVisible("RowHandle");
            colCounter.Caption = "";
            colCounter.UnboundType = DevExpress.Data.UnboundColumnType.Integer;
            colCounter.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;

            gridView1.CustomUnboundColumnData += (sender, e) =>
            {
                GridView view = sender as GridView;
                if (e.Column.FieldName == "RowHandle" && e.IsGetData)
                    e.Value = view.GetRowHandle(e.ListSourceRowIndex) + 1;
            };
            Dt_Rows = dt;
            ItemId = _ItemId;
            SellPrice = _SellPrice;
            Qty = _Qty;
            this.invoiceId = invoiceId;
            rowhandle = _rowhandle;
            //grdPrInvoice.DataSource = Dt_Rows;
            if (invoiceId != 0)
            {
                var invoice = DB.SL_Returns.Where(a => a.SL_ReturnId == invoiceId).FirstOrDefault();

                if (invoice.uuid != null && invoice.Estatus == "Valid")
                {
                    gridView1.OptionsBehavior.ReadOnly = true;
                    gridView1.OptionsBehavior.Editable = false;
                    lkpItem.Enabled = false;
                    lkpSubTax.Enabled = false;
                    txt_Percenage.Enabled = false;
                }
            }
        }
        private void frm_Sl_Return_Add_Taxes_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            ErpUtils.Load_Grid_Layout(grdPrInvoice, this.Name.Replace("frm_", ""));

                var Taxes = (from i in DB.E_TaxableTypes
                            select new
                            {
                                TaxId = i.E_TaxableTypeId,
                                Tax = Shared.IsEnglish ? i.DescriptionEn : i.DescriptionAr
                            }).ToList();
                lkpItem.Properties.ValueMember = "TaxId";
                lkpItem.Properties.DisplayMember = "Tax";
                lkpItem.Properties.DataSource = Taxes;


            lkp_Tax.ValueMember = "TaxId";
            lkp_Tax.DisplayMember = "Tax";
            lkp_Tax.DataSource = Taxes;

            var SubTypes = (from i in DB.E_TaxableTypes
                            where i.ParentTaxId!=null
                            select new
                        {
                            SubTaxId = i.E_TaxableTypeId,
                            SubTax = Shared.IsEnglish ? i.DescriptionEn : i.DescriptionAr,
                            subTaxParent=i.ParentTaxId,
                        }).ToList();
            lkp_SubTax.ValueMember = "SubTaxId";
            lkp_SubTax.DisplayMember = "SubTax";
            lkp_SubTax.DataSource = SubTypes;


            lkpSubTax.Properties.ValueMember = "SubTaxId";
            lkpSubTax.Properties.DisplayMember = "SubTax";
            lkpSubTax.Properties.DataSource = SubTypes;


            if (temp.Columns.Count < 1)
            {
                temp.Columns.Add("ItemId");
                temp.Columns.Add("SellPrice");
                temp.Columns.Add("Qty");
                temp.Columns.Add("Tax");
                temp.Columns.Add("SubTax");
                temp.Columns.Add("Percentage");
                temp.Columns.Add("TaxValue");
                temp.Columns.Add("RowHandle", typeof(int));
            }

            temp.Rows.Clear();
            foreach (DataRow dd in Dt_Rows.AsEnumerable()
                            .Where(a => a.RowState != DataRowState.Deleted)
                            .ToList())
            {
                if (Convert.ToInt32(dd["ItemId"]) != ItemId) continue;
                //if (Convert.ToDecimal(dd["SellPrice"]) != SellPrice) continue;
                //if (Convert.ToDecimal(dd["Qty"]) != Qty) continue;
                if (Convert.ToInt32(dd["RowHandle"]) != rowhandle) continue;

                DataRow dr = temp.NewRow();
                dr["ItemId"] = ItemId;
                dr["SellPrice"] = SellPrice;
                dr["Qty"] = Qty;
                dr["RowHandle"] = rowhandle;
                dr["Tax"] = dd["Tax"];
                dr["SubTax"] = dd["SubTax"];
                dr["Percentage"] = dd["Percentage"];

                temp.Rows.Add(dr);
            }
            grdPrInvoice.DataSource = temp;


        }

        private void barBtnSave_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdPrInvoice.Views[0].UpdateCurrentRow();
            temp.AcceptChanges();
            var x = Dt_Rows.Rows;
            var query = Dt_Rows.AsEnumerable()
                        .Where(
                                r => r.Field<int>("ItemId") == ItemId
                                //&& r.Field<decimal>("SellPrice") == SellPrice
                                  && r.Field<int>("RowHandle") == rowhandle
                              //  && r.Field<decimal>("Qty") == Qty
                                );

            foreach (var row in query.ToList())
                row.Delete();

            foreach (DataRow dr in temp.AsEnumerable()
             .Where(a => a.RowState != DataRowState.Deleted).ToList())
            {
                bool newTax = true;
                int tax = Convert.ToInt32(dr["Tax"]);
                int subtax = Convert.ToInt32(dr["SubTax"]);
                decimal percentage = Convert.ToDecimal(dr["Percentage"]);


                AddNewRow(subtax, tax, percentage);
                
            }
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void barBtnHelp_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {

        }

        private void txtNumberOnly_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (lkpItem.EditValue == null) return;
            if (lkpSubTax.EditValue == null) return;
            if (txt_Percenage.EditValue == null) return;

            if ((sender as SpinEdit).Name == "txt_Percenage")
            {
                if (e.KeyChar == Convert.ToChar(Keys.Enter))
                {
                    if (Convert.ToDecimal(txt_Percenage.EditValue) <= 0)
                    {
                        MessageBox.Show(Shared.IsEnglish ? "Please enter a valid Percentage" : "من فضلك أدخل نسبة بطريقة صحيحة");
                        txt_Percenage.Focus();
                        return;
                    }

                    var data = temp.Select(String.Format("SubTax = {0}", Convert.ToInt32(lkpSubTax.EditValue)));
                    if (data.Count() > 0) return;

                    DataRow dr = temp.NewRow();
                    dr["ItemId"] = ItemId;
                    dr["SellPrice"] = SellPrice;
                    dr["Qty"] = Qty;
                    dr["Tax"] = lkpItem.EditValue;
                    dr["SubTax"] = lkpSubTax.EditValue;
                    dr["Percentage"] = txt_Percenage.Text;
                    dr["RowHandle"] = rowhandle;
                    temp.Rows.Add(dr);

                    lkpSubTax.EditValue = null;
                    lkpItem.EditValue = null;
                    txt_Percenage.EditValue = null;
                }
            }
        }

        private void lkpItem_EditValueChanged(object sender, EventArgs e)
        {
            ErpUtils.save_Grid_Layout(grdPrInvoice, this.Name.Replace("frm_", ""), true);
            if (lkpItem.EditValue != null)
           {
                var E_TaxSubtypes = (from ts in DB.E_TaxableTypes
                                    .Where(x => x.ParentTaxId == Convert.ToInt32(lkpItem.EditValue))
                                    select new
                                    {
                                        SubTaxId = ts.E_TaxableTypeId,
                                        SubTax = Shared.IsEnglish ? ts.DescriptionEn : ts.DescriptionAr
                                    }
                                    ).ToList();
                lkpSubTax.Properties.DataSource = E_TaxSubtypes;

            }
        }

        private void barButtonItem1_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {

            
            Dt_Rows.Rows.Clear();
            this.DialogResult = DialogResult.Cancel;
            Close();
        }

        private void gridView1_CustomUnboundColumnData(object sender, CustomColumnDataEventArgs e)
        {
            if (e.Column.FieldName == "index")
                e.Value = e.ListSourceRowIndex + 1;
        }

        private void grdPrInvoice_ProcessGridKey(object sender, KeyEventArgs e)
        {
            GridView view = grdPrInvoice.FocusedView as GridView;
            if (e.KeyCode == Keys.Delete && e.Modifiers == Keys.Control)
            {
                view.DeleteSelectedRows();
                Dt_Rows.AcceptChanges();
                e.Handled = true;
            }
        }

            public void AddNewRow(int subTax, int tax, decimal percentage)
            {

                DataRow dd = Dt_Rows.NewRow();
                dd["ItemId"] = ItemId;
                dd["SellPrice"] = SellPrice;
                dd["Qty"] = Qty;
                dd["RowHandle"] = rowhandle;
                dd["Tax"] = tax;
                dd["SubTax"] = subTax;
                dd["Percentage"] = percentage;

                Dt_Rows.Rows.Add(dd);
            }
    }


}