﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="xrLine2.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <assembly alias="DevExpress.Utils.v15.1" name="DevExpress.Utils.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="xrLine2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>125, 30</value>
  </data>
  <data name="xrLine2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>650, 5</value>
  </data>
  <data name="lbl_ProcessName.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14.25pt</value>
  </data>
  <data name="lbl_ProcessName.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="lbl_ProcessName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>592.5, 3</value>
  </data>
  <data name="lbl_ProcessName.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>183.208313, 25.5833435</value>
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="lbl_ProcessName.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrSubreport2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>125, 122</value>
  </data>
  <data name="xrSubreport2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>562.499939, 12.6666565</value>
  </data>
  <data name="xrTable2.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTable2.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTable2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>125, 87.5</value>
  </data>
  <data name="lbl_Notes.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>196, 220, 255</value>
  </data>
  <data name="lbl_Notes.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_Notes.Text" xml:space="preserve">
    <value>lbl_Notes</value>
  </data>
  <data name="lbl_Notes.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrTableCell16.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="xrTableCell16.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell16.Text" xml:space="preserve">
    <value>الملاحظات</value>
  </data>
  <data name="xrTable2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>297.083466, 33.25003</value>
  </data>
  <data name="xrTable2.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrTable1.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTable1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTable1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>125, 37.5</value>
  </data>
  <data name="xrTableCell12.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="xrTableCell12.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell12.Text" xml:space="preserve">
    <value>الصافي</value>
  </data>
  <data name="xrTableCell10.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="xrTableCell10.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell10.Text" xml:space="preserve">
    <value>ق خصم</value>
  </data>
  <data name="xrTableCell8.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="xrTableCell8.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell8.Text" xml:space="preserve">
    <value>ن خصم </value>
  </data>
  <data name="xrTableCell6.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="xrTableCell6.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell6.Text" xml:space="preserve">
    <value>الإجمالي</value>
  </data>
  <data name="xrTableCell4.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="xrTableCell4.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell4.Text" xml:space="preserve">
    <value>المندوب</value>
  </data>
  <data name="xrTableCell1.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="xrTableCell1.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell1.Text" xml:space="preserve">
    <value>التاريخ</value>
  </data>
  <data name="xrTableCell2.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="xrTableCell2.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell2.Text" xml:space="preserve">
    <value>الكود</value>
  </data>
  <data name="xrTableCell3.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="xrTableCell3.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell3.Text" xml:space="preserve">
    <value>الفرع</value>
  </data>
  <data name="lbl_Net.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>196, 220, 255</value>
  </data>
  <data name="lbl_Net.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_Net.Text" xml:space="preserve">
    <value>lbl_Net</value>
  </data>
  <data name="lbl_DiscValue.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>196, 220, 255</value>
  </data>
  <data name="lbl_DiscValue.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_DiscValue.Text" xml:space="preserve">
    <value>lbl_DiscValue</value>
  </data>
  <data name="lbl_DiscRatio.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>196, 220, 255</value>
  </data>
  <data name="lbl_DiscRatio.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_DiscRatio.Text" xml:space="preserve">
    <value>lbl_DiscRatio</value>
  </data>
  <data name="lbl_Total.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>196, 220, 255</value>
  </data>
  <data name="lbl_Total.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_Total.Text" xml:space="preserve">
    <value>lbl_Total</value>
  </data>
  <data name="lbl_Emp.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>196, 220, 255</value>
  </data>
  <data name="lbl_Emp.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_Emp.Text" xml:space="preserve">
    <value>lbl_Emp</value>
  </data>
  <data name="lbl_Date.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>196, 220, 255</value>
  </data>
  <data name="lbl_Date.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_Date.Text" xml:space="preserve">
    <value>lbl_Date</value>
  </data>
  <data name="lbl_Code.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>196, 220, 255</value>
  </data>
  <data name="lbl_Code.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lbl_Code.Text" xml:space="preserve">
    <value>lbl_Code</value>
  </data>
  <data name="lbl_Branch.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>196, 220, 255</value>
  </data>
  <data name="lbl_Branch.Text" xml:space="preserve">
    <value>lbl_Branch</value>
  </data>
  <data name="xrTable1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>649.999939, 50</value>
  </data>
  <data name="xrTable1.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="Detail.HeightF" type="System.Single, mscorlib">
    <value>137</value>
  </data>
  <data name="lblDateFilter.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>187.5, 72.50001</value>
  </data>
  <data name="lblDateFilter.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>401.041779, 23.75</value>
  </data>
  <data name="lblReportName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>187.5, 47.5000076</value>
  </data>
  <data name="lblReportName.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>401.041779, 24.4999847</value>
  </data>
  <data name="lblCompName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>187.5, 10.0000067</value>
  </data>
  <data name="lblCompName.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>401.041779, 30</value>
  </data>
  <data name="lblFilter.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>112.5, 97.50001</value>
  </data>
  <data name="lblFilter.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>565.5416, 22.6250076</value>
  </data>
  <data name="TopMargin.HeightF" type="System.Single, mscorlib">
    <value>129</value>
  </data>
  <data name="xrPageInfo2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>12.5, 6.5</value>
  </data>
  <data name="xrPageInfo1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>337.5, 6.5</value>
  </data>
  <data name="BottomMargin.HeightF" type="System.Single, mscorlib">
    <value>38</value>
  </data>
  <data name="xrTable7.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>12.5, 189.5</value>
  </data>
  <data name="xrTableCell22.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="xrTableCell22.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell22.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell22.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell22.Text" xml:space="preserve">
    <value>الرصيد</value>
  </data>
  <data name="xrTableCell22.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableCell25.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="xrTableCell25.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell25.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell25.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell25.Text" xml:space="preserve">
    <value>دائن</value>
  </data>
  <data name="xrTableCell25.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableCell26.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="xrTableCell26.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell26.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell26.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell26.Text" xml:space="preserve">
    <value>مدين</value>
  </data>
  <data name="xrTableCell26.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lblCredit.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Gray</value>
  </data>
  <data name="lblCredit.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="lblCredit.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lblDebit.BackColor" type="System.Drawing.Color, System.Drawing">
    <value />
  </data>
  <data name="lblDebit.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Gray</value>
  </data>
  <data name="lblDebit.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="lblDebit.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="lblDebit.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTable6.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>237.5, 385.5</value>
  </data>
  <data name="xrTable6.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>300, 13.5416565</value>
  </data>
  <data name="xrLine4.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>OrangeRed</value>
  </data>
  <data name="xrLine4.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>19.583313, 430.916656</value>
  </data>
  <data name="xrLabel3.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 15.75pt, style=Bold</value>
  </data>
  <data name="xrLabel3.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>OrangeRed</value>
  </data>
  <data name="xrLabel3.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>597.791565, 404.3333</value>
  </data>
  <data name="xrLabel3.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>183.208313, 25.5833435</value>
  </data>
  <data name="xrLabel3.Text" xml:space="preserve">
    <value>سجل العمليات</value>
  </data>
  <data name="xrLabel3.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrTable5.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>229.083435, 442.999969</value>
  </data>
  <data name="xrTable5.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>300, 12.000061</value>
  </data>
  <data name="xrLabel2.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 15pt, style=Bold</value>
  </data>
  <data name="xrLabel2.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>OrangeRed</value>
  </data>
  <data name="xrLabel2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>597.791565, 272.9583</value>
  </data>
  <data name="xrLabel2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>183.208313, 25.5833588</value>
  </data>
  <data name="xrLabel2.Text" xml:space="preserve">
    <value>أوراق قبض مستحقة</value>
  </data>
  <data name="xrLabel2.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLine3.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>OrangeRed</value>
  </data>
  <data name="xrLine3.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>15.291626, 301.958344</value>
  </data>
  <data name="winControlContainer1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>0, 310.958344</value>
  </data>
  <data name="winControlContainer1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>661.2081, 73.33331</value>
  </data>
  <data name="col_Notes.Caption" xml:space="preserve">
    <value>ملاحظات</value>
  </data>
  <data name="col_Notes.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_Notes.Width" type="System.Int32, mscorlib">
    <value>355</value>
  </data>
  <data name="col_Amount.Caption" xml:space="preserve">
    <value>المبلغ</value>
  </data>
  <data name="col_Amount.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_Amount.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="col_Amount.Width" type="System.Int32, mscorlib">
    <value>197</value>
  </data>
  <data name="col_DueDate.Caption" xml:space="preserve">
    <value>تاريخ الإستحقاق</value>
  </data>
  <data name="col_DueDate.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_DueDate.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="col_DueDate.Width" type="System.Int32, mscorlib">
    <value>176</value>
  </data>
  <data name="col_RegDate.Caption" xml:space="preserve">
    <value>تاريخ التسجيل</value>
  </data>
  <data name="col_RegDate.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_RegDate.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="col_RegDate.Width" type="System.Int32, mscorlib">
    <value>169</value>
  </data>
  <data name="col_NoteType.Caption" xml:space="preserve">
    <value>نوع الورقة</value>
  </data>
  <data name="col_NoteType.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_NoteType.VisibleIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="col_NoteType.Width" type="System.Int32, mscorlib">
    <value>193</value>
  </data>
  <data name="gridControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>635, 70</value>
  </data>
  <data name="xrLine1.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>OrangeRed</value>
  </data>
  <data name="xrLine1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>14.583333, 39.0000038</value>
  </data>
  <data name="xrLine1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>765.7084, 9</value>
  </data>
  <data name="xrLabel1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 18pt, style=Bold</value>
  </data>
  <data name="xrLabel1.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>OrangeRed</value>
  </data>
  <data name="xrLabel1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>597.0834, 4</value>
  </data>
  <data name="xrLabel1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>183.208313, 30.5833588</value>
  </data>
  <data name="xrLabel1.Text" xml:space="preserve">
    <value>بيانات العميل</value>
  </data>
  <data name="xrLabel1.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>BottomRight</value>
  </data>
  <data name="xrTable4.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>12.5, 62.5</value>
  </data>
  <data name="xrTableCell23.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="xrTableCell23.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell23.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell23.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell23.Text" xml:space="preserve">
    <value>بيانات الحساب</value>
  </data>
  <data name="xrTableCell23.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lbl_TotalSell.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="lbl_TotalSell.Text" xml:space="preserve">
    <value>lbl_TotalSell</value>
  </data>
  <data name="lbl_TotalSell.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableCell27.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="xrTableCell27.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell27.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell27.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell27.Text" xml:space="preserve">
    <value>إجمالي فواتير بيع</value>
  </data>
  <data name="xrTableCell27.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_TotalSellReturn.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="lbl_TotalSellReturn.Text" xml:space="preserve">
    <value>lbl_TotalSellReturn</value>
  </data>
  <data name="lbl_TotalSellReturn.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableCell24.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="xrTableCell24.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell24.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell24.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell24.Text" xml:space="preserve">
    <value>اجمالي مردود بيع</value>
  </data>
  <data name="xrTableCell24.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_totalCashIn.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="lbl_totalCashIn.Text" xml:space="preserve">
    <value>lbl_totalCashIn</value>
  </data>
  <data name="lbl_totalCashIn.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableCell29.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="xrTableCell29.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell29.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell29.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell29.Text" xml:space="preserve">
    <value>اجمالي قبض نقدي</value>
  </data>
  <data name="xrTableCell29.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_totalDueNotes.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="lbl_totalDueNotes.Text" xml:space="preserve">
    <value>xrTableCell15</value>
  </data>
  <data name="lbl_totalDueNotes.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableCell31.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="xrTableCell31.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell31.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell31.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell31.Text" xml:space="preserve">
    <value>اجمالي اوراق مستحقة</value>
  </data>
  <data name="xrTableCell31.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrTable4.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>246.073837, 125.000008</value>
  </data>
  <data name="xrTable3.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>480.2917, 61.8749924</value>
  </data>
  <data name="lbl_CustCode.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="lbl_CustCode.Text" xml:space="preserve">
    <value>lbl_CustCode</value>
  </data>
  <data name="lbl_CustCode.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrTableCell9.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="xrTableCell9.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell9.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell9.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell9.Text" xml:space="preserve">
    <value>:كود العميل</value>
  </data>
  <data name="xrTableCell9.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_CustName.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="lbl_CustName.Text" xml:space="preserve">
    <value>lbl_CustName</value>
  </data>
  <data name="lbl_CustName.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrTableCell11.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="xrTableCell11.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell11.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell11.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell11.Text" xml:space="preserve">
    <value>:اسم العميل</value>
  </data>
  <data name="xrTableCell11.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_Group.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="lbl_Group.Text" xml:space="preserve">
    <value>lbl_Group</value>
  </data>
  <data name="lbl_Group.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrTableCell19.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="xrTableCell19.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell19.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell19.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell19.Text" xml:space="preserve">
    <value>:الفئة</value>
  </data>
  <data name="xrTableCell19.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_Mobile.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="lbl_Mobile.Text" xml:space="preserve">
    <value>lbl_Mobile</value>
  </data>
  <data name="lbl_Mobile.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrTableCell14.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="xrTableCell14.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell14.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell14.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell14.Text" xml:space="preserve">
    <value>:الهاتف</value>
  </data>
  <data name="xrTableCell14.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_City.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="lbl_City.Text" xml:space="preserve">
    <value>lbl_City</value>
  </data>
  <data name="lbl_City.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrTableCell17.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="xrTableCell17.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell17.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell17.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell17.Text" xml:space="preserve">
    <value>:المدينة</value>
  </data>
  <data name="xrTableCell17.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_Address.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="lbl_Address.Text" xml:space="preserve">
    <value>lbl_Address</value>
  </data>
  <data name="lbl_Address.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrTableCell21.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>84, 132, 213</value>
  </data>
  <data name="xrTableCell21.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell21.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTableCell21.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="xrTableCell21.Text" xml:space="preserve">
    <value>:العنوان</value>
  </data>
  <data name="xrTableCell21.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrTable3.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>300, 166.666672</value>
  </data>
  <data name="ReportHeader.HeightF" type="System.Single, mscorlib">
    <value>455.000031</value>
  </data>
  <data name="$this.Margins" type="System.Drawing.Printing.Margins, System.Drawing">
    <value>19, 22, 129, 38</value>
  </data>
</root>