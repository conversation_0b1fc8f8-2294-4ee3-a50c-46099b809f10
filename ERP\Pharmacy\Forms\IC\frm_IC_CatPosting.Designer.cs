﻿namespace Pharmacy.Forms
{
    partial class frm_IC_CatPosting
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_IC_CatPosting));
            this.barManager1 = new DevExpress.XtraBars.BarManager();
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtnHelp = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnSave = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem1 = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.panelControl2 = new DevExpress.XtraEditors.PanelControl();
            this.grdPrInvoice = new DevExpress.XtraGrid.GridControl();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_CategoryNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_SellAcc = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_Acc = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.repositoryItemGridLookUpEdit1View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_COGSAcc = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_SellReturnAcc = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_InvAcc = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).BeginInit();
            this.panelControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grdPrInvoice)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Acc)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnSave,
            this.barBtnHelp,
            this.barButtonItem1});
            this.barManager1.MaxItemId = 32;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(377, 152);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnHelp),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnSave),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem1)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtnHelp
            // 
            resources.ApplyResources(this.barBtnHelp, "barBtnHelp");
            this.barBtnHelp.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnHelp.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtnHelp.Id = 2;
            this.barBtnHelp.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtnHelp.Name = "barBtnHelp";
            this.barBtnHelp.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            // 
            // barBtnSave
            // 
            resources.ApplyResources(this.barBtnSave, "barBtnSave");
            this.barBtnSave.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnSave.Glyph = global::Pharmacy.Properties.Resources.sve;
            this.barBtnSave.Id = 0;
            this.barBtnSave.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.S));
            this.barBtnSave.Name = "barBtnSave";
            this.barBtnSave.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnSave.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnSave_ItemClick);
            // 
            // barButtonItem1
            // 
            resources.ApplyResources(this.barButtonItem1, "barButtonItem1");
            this.barButtonItem1.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barButtonItem1.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barButtonItem1.Id = 31;
            this.barButtonItem1.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barButtonItem1.Name = "barButtonItem1";
            this.barButtonItem1.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barButtonItem1.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem1_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.BarAppearance.Normal.GradientMode")));
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.BarAppearance.Normal.Image")));
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.Dock.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.Dock.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.Dock.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.Dock.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.Dock.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.Dock.GradientMode")));
            this.barAndDockingController1.AppearancesBar.Dock.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.Dock.Image")));
            this.barAndDockingController1.AppearancesBar.Dock.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.Dock.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.GradientMode")));
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Image")));
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontStyleDelta" +
        "")));
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.GradientMode")));
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.Image")));
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontSizeDel" +
        "ta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontStyleDe" +
        "lta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.GradientMod" +
        "e")));
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontSizeDelt" +
        "a")));
            this.barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontStyleDel" +
        "ta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.GradientMode" +
        "")));
            this.barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuBar.GradientMode")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuBar.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.GradientMode")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStrip.GradientMode")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStrip.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontStyleDelta" +
        "")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.GradientMode")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.Panel.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesDocking.Panel.FontSizeDelta")));
            this.barAndDockingController1.AppearancesDocking.Panel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesDocking.Panel.FontStyleDelta")));
            this.barAndDockingController1.AppearancesDocking.Panel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesDocking.Panel.GradientMode")));
            this.barAndDockingController1.AppearancesDocking.Panel.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesDocking.Panel.Image")));
            this.barAndDockingController1.AppearancesDocking.Panel.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.Panel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.PanelCaption.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaption.FontSizeDelta")));
            this.barAndDockingController1.AppearancesDocking.PanelCaption.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaption.FontStyleDelta")));
            this.barAndDockingController1.AppearancesDocking.PanelCaption.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaption.GradientMode")));
            this.barAndDockingController1.AppearancesDocking.PanelCaption.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaption.Image")));
            this.barAndDockingController1.AppearancesDocking.PanelCaption.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.PanelCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesRibbon.Item.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesRibbon.Item.FontSizeDelta")));
            this.barAndDockingController1.AppearancesRibbon.Item.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesRibbon.Item.FontStyleDelta")));
            this.barAndDockingController1.AppearancesRibbon.Item.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesRibbon.Item.GradientMode")));
            this.barAndDockingController1.AppearancesRibbon.Item.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesRibbon.Item.Image")));
            this.barAndDockingController1.AppearancesRibbon.Item.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesRibbon.Item.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesRibbon.PageHeader.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesRibbon.PageHeader.FontSizeDelta")));
            this.barAndDockingController1.AppearancesRibbon.PageHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesRibbon.PageHeader.FontStyleDelta")));
            this.barAndDockingController1.AppearancesRibbon.PageHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesRibbon.PageHeader.GradientMode")));
            this.barAndDockingController1.AppearancesRibbon.PageHeader.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesRibbon.PageHeader.Image")));
            this.barAndDockingController1.AppearancesRibbon.PageHeader.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesRibbon.PageHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlTop.Appearance.FontSizeDelta")));
            this.barDockControlTop.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlTop.Appearance.FontStyleDelta")));
            this.barDockControlTop.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlTop.Appearance.GradientMode")));
            this.barDockControlTop.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlTop.Appearance.Image")));
            this.barDockControlTop.Appearance.Options.UseTextOptions = true;
            this.barDockControlTop.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barDockControlTop.CausesValidation = false;
            // 
            // barDockControlBottom
            // 
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlBottom.Appearance.FontSizeDelta")));
            this.barDockControlBottom.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlBottom.Appearance.FontStyleDelta")));
            this.barDockControlBottom.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlBottom.Appearance.GradientMode")));
            this.barDockControlBottom.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlBottom.Appearance.Image")));
            this.barDockControlBottom.CausesValidation = false;
            // 
            // barDockControlLeft
            // 
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlLeft.Appearance.FontSizeDelta")));
            this.barDockControlLeft.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlLeft.Appearance.FontStyleDelta")));
            this.barDockControlLeft.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlLeft.Appearance.GradientMode")));
            this.barDockControlLeft.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlLeft.Appearance.Image")));
            this.barDockControlLeft.CausesValidation = false;
            // 
            // barDockControlRight
            // 
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlRight.Appearance.FontSizeDelta")));
            this.barDockControlRight.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlRight.Appearance.FontStyleDelta")));
            this.barDockControlRight.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlRight.Appearance.GradientMode")));
            this.barDockControlRight.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlRight.Appearance.Image")));
            this.barDockControlRight.CausesValidation = false;
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemTextEdit1.Mask.AutoComplete")));
            this.repositoryItemTextEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.BeepOnError")));
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemTextEdit1.Mask.MaskType")));
            this.repositoryItemTextEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemTextEdit1.Mask.PlaceHolder")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // panelControl2
            // 
            resources.ApplyResources(this.panelControl2, "panelControl2");
            this.panelControl2.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.panelControl2.Controls.Add(this.grdPrInvoice);
            this.panelControl2.Name = "panelControl2";
            // 
            // grdPrInvoice
            // 
            resources.ApplyResources(this.grdPrInvoice, "grdPrInvoice");
            this.grdPrInvoice.Cursor = System.Windows.Forms.Cursors.Default;
            this.grdPrInvoice.EmbeddedNavigator.AccessibleDescription = resources.GetString("grdPrInvoice.EmbeddedNavigator.AccessibleDescription");
            this.grdPrInvoice.EmbeddedNavigator.AccessibleName = resources.GetString("grdPrInvoice.EmbeddedNavigator.AccessibleName");
            this.grdPrInvoice.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grdPrInvoice.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.Anchor")));
            this.grdPrInvoice.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.BackgroundImage")));
            this.grdPrInvoice.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.BackgroundImageLayout")));
            this.grdPrInvoice.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.ImeMode")));
            this.grdPrInvoice.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.MaximumSize")));
            this.grdPrInvoice.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.TextLocation")));
            this.grdPrInvoice.EmbeddedNavigator.ToolTip = resources.GetString("grdPrInvoice.EmbeddedNavigator.ToolTip");
            this.grdPrInvoice.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.ToolTipIconType")));
            this.grdPrInvoice.EmbeddedNavigator.ToolTipTitle = resources.GetString("grdPrInvoice.EmbeddedNavigator.ToolTipTitle");
            this.grdPrInvoice.MainView = this.gridView2;
            this.grdPrInvoice.MenuManager = this.barManager1;
            this.grdPrInvoice.Name = "grdPrInvoice";
            this.grdPrInvoice.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.rep_Acc});
            this.grdPrInvoice.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView2});
            // 
            // gridView2
            // 
            this.gridView2.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView2.Appearance.HeaderPanel.FontSizeDelta")));
            this.gridView2.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView2.Appearance.HeaderPanel.FontStyleDelta")));
            this.gridView2.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView2.Appearance.HeaderPanel.GradientMode")));
            this.gridView2.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView2.Appearance.HeaderPanel.Image")));
            this.gridView2.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView2.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView2.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gridView2.Appearance.Row.FontSizeDelta")));
            this.gridView2.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView2.Appearance.Row.FontStyleDelta")));
            this.gridView2.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView2.Appearance.Row.GradientMode")));
            this.gridView2.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView2.Appearance.Row.Image")));
            this.gridView2.Appearance.Row.Options.UseTextOptions = true;
            this.gridView2.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.gridView2, "gridView2");
            this.gridView2.ColumnPanelRowHeight = 60;
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_CategoryNameAr,
            this.col_SellAcc,
            this.col_COGSAcc,
            this.col_SellReturnAcc,
            this.col_InvAcc});
            this.gridView2.GridControl = this.grdPrInvoice;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.gridView2.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView2.OptionsView.RowAutoHeight = true;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            // 
            // col_CategoryNameAr
            // 
            this.col_CategoryNameAr.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_CategoryNameAr.AppearanceCell.FontSizeDelta")));
            this.col_CategoryNameAr.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_CategoryNameAr.AppearanceCell.FontStyleDelta")));
            this.col_CategoryNameAr.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_CategoryNameAr.AppearanceCell.GradientMode")));
            this.col_CategoryNameAr.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_CategoryNameAr.AppearanceCell.Image")));
            this.col_CategoryNameAr.AppearanceCell.Options.UseTextOptions = true;
            this.col_CategoryNameAr.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_CategoryNameAr.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_CategoryNameAr.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_CategoryNameAr.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_CategoryNameAr.AppearanceHeader.FontSizeDelta")));
            this.col_CategoryNameAr.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_CategoryNameAr.AppearanceHeader.FontStyleDelta")));
            this.col_CategoryNameAr.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_CategoryNameAr.AppearanceHeader.GradientMode")));
            this.col_CategoryNameAr.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_CategoryNameAr.AppearanceHeader.Image")));
            this.col_CategoryNameAr.AppearanceHeader.Options.UseTextOptions = true;
            this.col_CategoryNameAr.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_CategoryNameAr.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_CategoryNameAr.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_CategoryNameAr, "col_CategoryNameAr");
            this.col_CategoryNameAr.FieldName = "CategoryNameAr";
            this.col_CategoryNameAr.Name = "col_CategoryNameAr";
            this.col_CategoryNameAr.OptionsColumn.AllowEdit = false;
            this.col_CategoryNameAr.OptionsColumn.AllowFocus = false;
            // 
            // col_SellAcc
            // 
            this.col_SellAcc.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_SellAcc.AppearanceCell.FontSizeDelta")));
            this.col_SellAcc.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_SellAcc.AppearanceCell.FontStyleDelta")));
            this.col_SellAcc.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_SellAcc.AppearanceCell.GradientMode")));
            this.col_SellAcc.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_SellAcc.AppearanceCell.Image")));
            this.col_SellAcc.AppearanceCell.Options.UseTextOptions = true;
            this.col_SellAcc.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_SellAcc.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_SellAcc.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_SellAcc.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_SellAcc.AppearanceHeader.FontSizeDelta")));
            this.col_SellAcc.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_SellAcc.AppearanceHeader.FontStyleDelta")));
            this.col_SellAcc.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_SellAcc.AppearanceHeader.GradientMode")));
            this.col_SellAcc.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_SellAcc.AppearanceHeader.Image")));
            this.col_SellAcc.AppearanceHeader.Options.UseTextOptions = true;
            this.col_SellAcc.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_SellAcc.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_SellAcc.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_SellAcc, "col_SellAcc");
            this.col_SellAcc.ColumnEdit = this.rep_Acc;
            this.col_SellAcc.FieldName = "SellAcc";
            this.col_SellAcc.Name = "col_SellAcc";
            // 
            // rep_Acc
            // 
            resources.ApplyResources(this.rep_Acc, "rep_Acc");
            this.rep_Acc.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.rep_Acc.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.rep_Acc.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Acc.Buttons"))))});
            this.rep_Acc.Name = "rep_Acc";
            this.rep_Acc.PopupFilterMode = DevExpress.XtraEditors.PopupFilterMode.Contains;
            this.rep_Acc.ShowFooter = false;
            this.rep_Acc.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.rep_Acc.View = this.repositoryItemGridLookUpEdit1View;
            // 
            // repositoryItemGridLookUpEdit1View
            // 
            this.repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.FontSizeDelta")));
            this.repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.FontStyleDelta")));
            this.repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.GradientMode")));
            this.repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.Image")));
            this.repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.repositoryItemGridLookUpEdit1View.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("repositoryItemGridLookUpEdit1View.Appearance.Row.FontSizeDelta")));
            this.repositoryItemGridLookUpEdit1View.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("repositoryItemGridLookUpEdit1View.Appearance.Row.FontStyleDelta")));
            this.repositoryItemGridLookUpEdit1View.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("repositoryItemGridLookUpEdit1View.Appearance.Row.GradientMode")));
            this.repositoryItemGridLookUpEdit1View.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("repositoryItemGridLookUpEdit1View.Appearance.Row.Image")));
            this.repositoryItemGridLookUpEdit1View.Appearance.Row.Options.UseTextOptions = true;
            this.repositoryItemGridLookUpEdit1View.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.repositoryItemGridLookUpEdit1View, "repositoryItemGridLookUpEdit1View");
            this.repositoryItemGridLookUpEdit1View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn1});
            this.repositoryItemGridLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.repositoryItemGridLookUpEdit1View.Name = "repositoryItemGridLookUpEdit1View";
            this.repositoryItemGridLookUpEdit1View.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.repositoryItemGridLookUpEdit1View.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.repositoryItemGridLookUpEdit1View.OptionsBehavior.AutoSelectAllInEditor = false;
            this.repositoryItemGridLookUpEdit1View.OptionsBehavior.AutoUpdateTotalSummary = false;
            this.repositoryItemGridLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.repositoryItemGridLookUpEdit1View.OptionsSelection.UseIndicatorForSelection = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.BestFitMaxRowCount = 10;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowAutoFilterRow = true;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowDetailButtons = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowGroupPanel = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowIndicator = false;
            // 
            // gridColumn14
            // 
            resources.ApplyResources(this.gridColumn14, "gridColumn14");
            this.gridColumn14.FieldName = "AccName";
            this.gridColumn14.Name = "gridColumn14";
            // 
            // gridColumn15
            // 
            resources.ApplyResources(this.gridColumn15, "gridColumn15");
            this.gridColumn15.FieldName = "AccId";
            this.gridColumn15.Name = "gridColumn15";
            // 
            // gridColumn1
            // 
            resources.ApplyResources(this.gridColumn1, "gridColumn1");
            this.gridColumn1.FieldName = "AccNumber";
            this.gridColumn1.Name = "gridColumn1";
            // 
            // col_COGSAcc
            // 
            resources.ApplyResources(this.col_COGSAcc, "col_COGSAcc");
            this.col_COGSAcc.ColumnEdit = this.rep_Acc;
            this.col_COGSAcc.FieldName = "COGSAcc";
            this.col_COGSAcc.Name = "col_COGSAcc";
            // 
            // col_SellReturnAcc
            // 
            resources.ApplyResources(this.col_SellReturnAcc, "col_SellReturnAcc");
            this.col_SellReturnAcc.ColumnEdit = this.rep_Acc;
            this.col_SellReturnAcc.FieldName = "SellReturnAcc";
            this.col_SellReturnAcc.Name = "col_SellReturnAcc";
            // 
            // col_InvAcc
            // 
            resources.ApplyResources(this.col_InvAcc, "col_InvAcc");
            this.col_InvAcc.ColumnEdit = this.rep_Acc;
            this.col_InvAcc.FieldName = "InvAcc";
            this.col_InvAcc.Name = "col_InvAcc";
            // 
            // frm_IC_CatPosting
            // 
            resources.ApplyResources(this, "$this");
            this.Appearance.FontSizeDelta = ((int)(resources.GetObject("frm_IC_CatPosting.Appearance.FontSizeDelta")));
            this.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("frm_IC_CatPosting.Appearance.FontStyleDelta")));
            this.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("frm_IC_CatPosting.Appearance.GradientMode")));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("frm_IC_CatPosting.Appearance.Image")));
            this.Appearance.Options.UseTextOptions = true;
            this.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.panelControl2);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.KeyPreview = true;
            this.Name = "frm_IC_CatPosting";
            this.Load += new System.EventHandler(this.frm_InvChangePrices_Load);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).EndInit();
            this.panelControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grdPrInvoice)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Acc)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnSave;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtnHelp;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        
        private DevExpress.XtraEditors.PanelControl panelControl2;
        private DevExpress.XtraGrid.GridControl grdPrInvoice;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn col_SellAcc;
        
        private DevExpress.XtraGrid.Views.Grid.GridView repositoryItemGridLookUpEdit1View;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn col_CategoryNameAr;
        private DevExpress.XtraGrid.Columns.GridColumn col_COGSAcc;
        private DevExpress.XtraGrid.Columns.GridColumn col_SellReturnAcc;
        private DevExpress.XtraGrid.Columns.GridColumn col_InvAcc;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_Acc;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraBars.BarButtonItem barButtonItem1;
    }
}