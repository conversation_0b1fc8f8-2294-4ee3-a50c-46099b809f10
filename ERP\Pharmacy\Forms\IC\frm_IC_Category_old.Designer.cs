﻿namespace Pharmacy.Forms
{
    partial class frm_IC_Category_old
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_IC_Category));
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtn_Help = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnNew = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnDelete = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnSave = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnList = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.txtCategoryNameAr = new DevExpress.XtraEditors.TextEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.txtCategoryNameEn = new DevExpress.XtraEditors.TextEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.txtCategoryCode = new DevExpress.XtraEditors.TextEdit();
            this.btnNext = new DevExpress.XtraEditors.SimpleButton();
            this.btnPrev = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCategoryNameAr.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCategoryNameEn.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCategoryCode.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnSave,
            this.barBtnDelete,
            this.barBtn_Help,
            this.barBtnList,
            this.barBtnClose,
            this.barBtnNew});
            this.barManager1.MaxItemId = 29;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(377, 152);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_Help),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnNew),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnDelete),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnSave),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnList),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtn_Help
            // 
            this.barBtn_Help.AccessibleDescription = null;
            this.barBtn_Help.AccessibleName = null;
            this.barBtn_Help.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtn_Help, "barBtn_Help");
            this.barBtn_Help.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtn_Help.Id = 2;
            this.barBtn_Help.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtn_Help.Name = "barBtn_Help";
            this.barBtn_Help.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtn_Help.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Help_ItemClick);
            // 
            // barBtnNew
            // 
            this.barBtnNew.AccessibleDescription = null;
            this.barBtnNew.AccessibleName = null;
            this.barBtnNew.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnNew, "barBtnNew");
            this.barBtnNew.Glyph = global::Pharmacy.Properties.Resources._new;
            this.barBtnNew.Id = 27;
            this.barBtnNew.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.N));
            this.barBtnNew.Name = "barBtnNew";
            this.barBtnNew.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnNew.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnNew_ItemClick);
            // 
            // barBtnDelete
            // 
            this.barBtnDelete.AccessibleDescription = null;
            this.barBtnDelete.AccessibleName = null;
            this.barBtnDelete.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnDelete, "barBtnDelete");
            this.barBtnDelete.Glyph = global::Pharmacy.Properties.Resources.del;
            this.barBtnDelete.Id = 1;
            this.barBtnDelete.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.D));
            this.barBtnDelete.Name = "barBtnDelete";
            this.barBtnDelete.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnDelete.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Delete_ItemClick);
            // 
            // barBtnSave
            // 
            this.barBtnSave.AccessibleDescription = null;
            this.barBtnSave.AccessibleName = null;
            this.barBtnSave.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnSave, "barBtnSave");
            this.barBtnSave.Glyph = global::Pharmacy.Properties.Resources.sve;
            this.barBtnSave.Id = 0;
            this.barBtnSave.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.S));
            this.barBtnSave.Name = "barBtnSave";
            this.barBtnSave.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnSave.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Save_ItemClick);
            // 
            // barBtnList
            // 
            this.barBtnList.AccessibleDescription = null;
            this.barBtnList.AccessibleName = null;
            this.barBtnList.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnList, "barBtnList");
            this.barBtnList.Glyph = global::Pharmacy.Properties.Resources.list32;
            this.barBtnList.Id = 25;
            this.barBtnList.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.T));
            this.barBtnList.Name = "barBtnList";
            this.barBtnList.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnList.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_List_ItemClick);
            // 
            // barBtnClose
            // 
            this.barBtnClose.AccessibleDescription = null;
            this.barBtnClose.AccessibleName = null;
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 26;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnClose_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.AccessibleDescription = null;
            this.barDockControlTop.AccessibleName = null;
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Font = null;
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.AccessibleDescription = null;
            this.barDockControlBottom.AccessibleName = null;
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Font = null;
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.AccessibleDescription = null;
            this.barDockControlLeft.AccessibleName = null;
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Font = null;
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.AccessibleDescription = null;
            this.barDockControlRight.AccessibleName = null;
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Font = null;
            // 
            // repositoryItemTextEdit1
            // 
            this.repositoryItemTextEdit1.AccessibleDescription = null;
            this.repositoryItemTextEdit1.AccessibleName = null;
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemTextEdit1.Mask.AutoComplete")));
            this.repositoryItemTextEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.BeepOnError")));
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemTextEdit1.Mask.MaskType")));
            this.repositoryItemTextEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemTextEdit1.Mask.PlaceHolder")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // labelControl4
            // 
            this.labelControl4.AccessibleDescription = null;
            this.labelControl4.AccessibleName = null;
            resources.ApplyResources(this.labelControl4, "labelControl4");
            this.labelControl4.Name = "labelControl4";
            // 
            // txtCategoryNameAr
            // 
            resources.ApplyResources(this.txtCategoryNameAr, "txtCategoryNameAr");
            this.txtCategoryNameAr.BackgroundImage = null;
            this.txtCategoryNameAr.EditValue = null;
            this.txtCategoryNameAr.EnterMoveNextControl = true;
            this.txtCategoryNameAr.Name = "txtCategoryNameAr";
            this.txtCategoryNameAr.Properties.AccessibleDescription = null;
            this.txtCategoryNameAr.Properties.AccessibleName = null;
            this.txtCategoryNameAr.Properties.Appearance.Options.UseTextOptions = true;
            this.txtCategoryNameAr.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtCategoryNameAr.Properties.AutoHeight = ((bool)(resources.GetObject("txtCategoryNameAr.Properties.AutoHeight")));
            this.txtCategoryNameAr.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtCategoryNameAr.Properties.Mask.AutoComplete")));
            this.txtCategoryNameAr.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtCategoryNameAr.Properties.Mask.BeepOnError")));
            this.txtCategoryNameAr.Properties.Mask.EditMask = resources.GetString("txtCategoryNameAr.Properties.Mask.EditMask");
            this.txtCategoryNameAr.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtCategoryNameAr.Properties.Mask.IgnoreMaskBlank")));
            this.txtCategoryNameAr.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtCategoryNameAr.Properties.Mask.MaskType")));
            this.txtCategoryNameAr.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtCategoryNameAr.Properties.Mask.PlaceHolder")));
            this.txtCategoryNameAr.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtCategoryNameAr.Properties.Mask.SaveLiteral")));
            this.txtCategoryNameAr.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtCategoryNameAr.Properties.Mask.ShowPlaceHolders")));
            this.txtCategoryNameAr.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtCategoryNameAr.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtCategoryNameAr.Properties.MaxLength = 50;
            this.txtCategoryNameAr.Properties.NullValuePrompt = resources.GetString("txtCategoryNameAr.Properties.NullValuePrompt");
            this.txtCategoryNameAr.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtCategoryNameAr.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl3
            // 
            this.labelControl3.AccessibleDescription = null;
            this.labelControl3.AccessibleName = null;
            resources.ApplyResources(this.labelControl3, "labelControl3");
            this.labelControl3.Name = "labelControl3";
            // 
            // txtCategoryNameEn
            // 
            resources.ApplyResources(this.txtCategoryNameEn, "txtCategoryNameEn");
            this.txtCategoryNameEn.BackgroundImage = null;
            this.txtCategoryNameEn.EditValue = null;
            this.txtCategoryNameEn.EnterMoveNextControl = true;
            this.txtCategoryNameEn.Name = "txtCategoryNameEn";
            this.txtCategoryNameEn.Properties.AccessibleDescription = null;
            this.txtCategoryNameEn.Properties.AccessibleName = null;
            this.txtCategoryNameEn.Properties.Appearance.Options.UseTextOptions = true;
            this.txtCategoryNameEn.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtCategoryNameEn.Properties.AutoHeight = ((bool)(resources.GetObject("txtCategoryNameEn.Properties.AutoHeight")));
            this.txtCategoryNameEn.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtCategoryNameEn.Properties.Mask.AutoComplete")));
            this.txtCategoryNameEn.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtCategoryNameEn.Properties.Mask.BeepOnError")));
            this.txtCategoryNameEn.Properties.Mask.EditMask = resources.GetString("txtCategoryNameEn.Properties.Mask.EditMask");
            this.txtCategoryNameEn.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtCategoryNameEn.Properties.Mask.IgnoreMaskBlank")));
            this.txtCategoryNameEn.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtCategoryNameEn.Properties.Mask.MaskType")));
            this.txtCategoryNameEn.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtCategoryNameEn.Properties.Mask.PlaceHolder")));
            this.txtCategoryNameEn.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtCategoryNameEn.Properties.Mask.SaveLiteral")));
            this.txtCategoryNameEn.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtCategoryNameEn.Properties.Mask.ShowPlaceHolders")));
            this.txtCategoryNameEn.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtCategoryNameEn.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtCategoryNameEn.Properties.MaxLength = 50;
            this.txtCategoryNameEn.Properties.NullValuePrompt = resources.GetString("txtCategoryNameEn.Properties.NullValuePrompt");
            this.txtCategoryNameEn.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtCategoryNameEn.Properties.NullValuePromptShowForEmptyValue")));
            this.txtCategoryNameEn.Leave += new System.EventHandler(this.txtCategoryNameEn_Leave);
            // 
            // labelControl1
            // 
            this.labelControl1.AccessibleDescription = null;
            this.labelControl1.AccessibleName = null;
            resources.ApplyResources(this.labelControl1, "labelControl1");
            this.labelControl1.Name = "labelControl1";
            // 
            // txtCategoryCode
            // 
            resources.ApplyResources(this.txtCategoryCode, "txtCategoryCode");
            this.txtCategoryCode.BackgroundImage = null;
            this.txtCategoryCode.EditValue = null;
            this.txtCategoryCode.EnterMoveNextControl = true;
            this.txtCategoryCode.Name = "txtCategoryCode";
            this.txtCategoryCode.Properties.AccessibleDescription = null;
            this.txtCategoryCode.Properties.AccessibleName = null;
            this.txtCategoryCode.Properties.Appearance.BackColor = System.Drawing.Color.White;
            this.txtCategoryCode.Properties.Appearance.Options.UseBackColor = true;
            this.txtCategoryCode.Properties.Appearance.Options.UseTextOptions = true;
            this.txtCategoryCode.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtCategoryCode.Properties.AutoHeight = ((bool)(resources.GetObject("txtCategoryCode.Properties.AutoHeight")));
            this.txtCategoryCode.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtCategoryCode.Properties.Mask.AutoComplete")));
            this.txtCategoryCode.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtCategoryCode.Properties.Mask.BeepOnError")));
            this.txtCategoryCode.Properties.Mask.EditMask = resources.GetString("txtCategoryCode.Properties.Mask.EditMask");
            this.txtCategoryCode.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtCategoryCode.Properties.Mask.IgnoreMaskBlank")));
            this.txtCategoryCode.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtCategoryCode.Properties.Mask.MaskType")));
            this.txtCategoryCode.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtCategoryCode.Properties.Mask.PlaceHolder")));
            this.txtCategoryCode.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtCategoryCode.Properties.Mask.SaveLiteral")));
            this.txtCategoryCode.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtCategoryCode.Properties.Mask.ShowPlaceHolders")));
            this.txtCategoryCode.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtCategoryCode.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtCategoryCode.Properties.NullValuePrompt = resources.GetString("txtCategoryCode.Properties.NullValuePrompt");
            this.txtCategoryCode.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtCategoryCode.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // btnNext
            // 
            this.btnNext.AccessibleDescription = null;
            this.btnNext.AccessibleName = null;
            resources.ApplyResources(this.btnNext, "btnNext");
            this.btnNext.BackgroundImage = null;
            this.btnNext.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnNext.Image = global::Pharmacy.Properties.Resources.nxt;
            this.btnNext.Name = "btnNext";
            this.btnNext.Click += new System.EventHandler(this.btnNext_Click);
            // 
            // btnPrev
            // 
            this.btnPrev.AccessibleDescription = null;
            this.btnPrev.AccessibleName = null;
            resources.ApplyResources(this.btnPrev, "btnPrev");
            this.btnPrev.BackgroundImage = null;
            this.btnPrev.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnPrev.Image = global::Pharmacy.Properties.Resources.prev32;
            this.btnPrev.Name = "btnPrev";
            this.btnPrev.Click += new System.EventHandler(this.btnPrev_Click);
            // 
            // frm_IC_Category
            // 
            this.AccessibleDescription = null;
            this.AccessibleName = null;
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.btnNext);
            this.Controls.Add(this.btnPrev);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.txtCategoryCode);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.txtCategoryNameEn);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.txtCategoryNameAr);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.KeyPreview = true;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "frm_IC_Category";
            this.Load += new System.EventHandler(this.frm_IC_Category_Load);
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.frm_IC_Category_KeyUp);
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_IC_Category_FormClosing);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCategoryNameAr.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCategoryNameEn.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCategoryCode.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnSave;
        private DevExpress.XtraBars.BarButtonItem barBtnDelete;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtn_Help;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.TextEdit txtCategoryNameAr;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.TextEdit txtCategoryNameEn;
        private DevExpress.XtraBars.BarButtonItem barBtnList;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.TextEdit txtCategoryCode;
        private DevExpress.XtraEditors.SimpleButton btnNext;
        private DevExpress.XtraEditors.SimpleButton btnPrev;
        private DevExpress.XtraBars.BarButtonItem barBtnNew;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
    }
}