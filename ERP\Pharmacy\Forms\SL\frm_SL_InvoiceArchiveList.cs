﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;
using DevExpress.XtraReports.UI;

namespace Pharmacy.Forms
{
    public partial class frm_SL_InvoiceArchiveList : DevExpress.XtraEditors.XtraForm
    {
        int customerId;
        DateTime dateFrom, dateTo;

        bool Is_OpenForSelect = false;
        public static int SelectedInvId = 0;
        public static string SelectedInvCode;

        public frm_SL_InvoiceArchiveList()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
        }

        public frm_SL_InvoiceArchiveList(bool _is_OpenForSelect)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            Is_OpenForSelect = _is_OpenForSelect;
        }
        
        public frm_SL_InvoiceArchiveList(int customerId)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            this.customerId = customerId;
        }

        private void frm_SL_InvoiceList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            #region Get Date Range
            if (Shared.SL_I_FromDate != null)
                dt1.DateTime = dateFrom = Shared.SL_I_FromDate.Value;
            else
            {
                dateFrom = Shared.minDate;
                dt1.EditValue = null;
            }

            if (Shared.SL_I_ToDate != null)
                dt2.DateTime = dateTo = Shared.SL_I_ToDate.Value;
            else
            {
                dateTo = Shared.maxDate;
                dt2.EditValue = null;
            } 
            #endregion

            GetInvoices();

            #region SalesEmp
            DataTable dt_SalesEmps = new DataTable();
            //MyHelper.GetSalesEmps(dt_SalesEmps, false, true, Shared.user.DefaultSalesRep);
            rep_salesEmp.DataSource = dt_SalesEmps;
            rep_salesEmp.DisplayMember = "EmpName";
            rep_salesEmp.ValueMember = "EmpId";
            #endregion

            #region CustomersGroups
            ERPDataContext DB = new ERPDataContext();
            rep_groupId.DataSource = DB.SL_CustomerGroups.Select(x => new { x.CustomerGroupId, x.CGNameAr }).ToList();
            rep_groupId.DisplayMember = "CGNameAr";
            rep_groupId.ValueMember = "CustomerGroupId";
            #endregion

            #region invoice bbok            
            rep_InvoiceBook.DataSource = DB.ST_InvoiceBooks.Where(x=> x.ProcessId == (int)Process.SellInvoice)
                .Select(x => new { x.InvoiceBookId, x.InvoiceBookName}).ToList();
            rep_InvoiceBook.DisplayMember = "InvoiceBookName";
            rep_InvoiceBook.ValueMember = "InvoiceBookId";
            #endregion

            #region Currencies
            repCrncy.DataSource = Shared.lstCurrency;
            repCrncy.ValueMember = "CrncId";
            repCrncy.DisplayMember = "crncName";
            #endregion

            if (Shared.InvoicePostToStore)
                col_Is_OutTrans.Visible = false;

            ErpUtils.Load_Grid_Layout(grdCategory, this.Name.Replace("frm_", ""));
            ErpUtils.ColumnChooser(grdCategory);

            if (Shared.user.HidePurchasePrice)
            {
                col_Profit.Visible = col_Profit.OptionsColumn.ShowInCustomizationForm = 
                    col_TotalCostPrice.Visible = col_TotalCostPrice.OptionsColumn.ShowInCustomizationForm = 
                    col_ProfitRatio.Visible = col_ProfitRatio.OptionsColumn.ShowInCustomizationForm = false;
            }

            ErpUtils.ColumnChooser(grdCategory);
            LoadPrivilege();
        }

        private void frm_SL_InvoiceList_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Home && e.Modifiers == Keys.Control)
            {
                dt1.Focus();
            }
            if (e.KeyCode == Keys.Insert)
            {
                grdCategory.Focus();
            }
        }

        private void frm_SL_InvoiceList_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (dt1.EditValue != null)
                Shared.SL_I_FromDate = dateFrom;
            else
                Shared.SL_I_FromDate = null;

            if (dt2.EditValue != null)
                Shared.SL_I_ToDate = dateTo;
            else
                Shared.SL_I_ToDate = null;

            ErpUtils.save_Grid_Layout(grdCategory, this.Name.Replace("frm_", ""), true);
        }

        private void dt1_EditValueChanged(object sender, EventArgs e)
        {
            if (dateFrom != DateTime.MinValue && dateTo != DateTime.MinValue)
            {
                if (dt1.DateTime != DateTime.MinValue)
                    dateFrom = dt1.DateTime;
                else
                    dateFrom = Shared.minDate;

                if (dt2.DateTime != DateTime.MinValue)
                    dateTo = dt2.DateTime;
                else
                {
                    dateTo = Shared.maxDate;
                }
            }
        }

        private void btnClearSearch_Click(object sender, EventArgs e)
        {
            dateFrom = Shared.minDate;
            dateTo = Shared.maxDate;
            dt1.EditValue = null;
            dt2.EditValue = null;
            barBtnRefresh.PerformClick();
        }
        private void barBtn_New_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (ErpUtils.IsFormOpen(typeof(frm_SL_Invoice)))
                Application.OpenForms["frm_SL_Invoice"].Close();

            if (ErpUtils.IsFormOpen(typeof(frm_SL_Invoice)))
                Application.OpenForms["frm_SL_Invoice"].BringToFront();
            else
                new frm_SL_Invoice().Show();
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Refresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            GetInvoices();
        }

        private void barBtn_Open_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Open_Selected_Invoice();
        }


        private void grdCategory_DoubleClick(object sender, EventArgs e)
        {
            Open_Selected_Invoice();
        }
        
        private void NBI_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            if (((NavBarItem)sender).Name == "NBI_Customers")
            {
                frmMain.OpenSL_Customer();
            }

            var view = grdCategory.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            int strId = Convert.ToInt32(view.GetFocusedRowCellValue(colStore));
            string strName = view.GetFocusedRowCellDisplayText(col_StoreId).ToString();
            string strFltr = (Shared.IsEnglish == true ? ResSLEn.txtStore : ResSLAr.txtStore)//"المخزن: " 
                + strName;

            
        }

        private void Open_Selected_Invoice()
        {
            var view = grdCategory.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            int inv_id = Convert.ToInt32(view.GetRowCellValue(focused_row_index, col_SL_InvoiceId));

            if (Is_OpenForSelect == true)
            {
                SelectedInvId = inv_id;
                SelectedInvCode = view.GetRowCellValue(focused_row_index, col_InvoiceCode).ToString();

                this.Close();
                return;
            }
            if (ErpUtils.IsFormOpen(typeof(frm_SL_InvoiceArchive)))
                Application.OpenForms["frm_SL_InvoiceArchive"].Close();

            if (ErpUtils.IsFormOpen(typeof(frm_SL_InvoiceArchive)))
                Application.OpenForms["frm_SL_InvoiceArchive"].BringToFront();
            else
                new frm_SL_InvoiceArchive(inv_id).Show();
        }

        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.SL_Customer).Count() < 1)
                {
                    mi_OpenDealer.Enabled = false;
                }

                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.SL_Invoice).FirstOrDefault();
                if (!p.CanAdd)
                    barBtnNew.Enabled = false;
                if (!p.CanPrint)
                    barMnu_Print.Enabled = barBtn_Print1.Enabled  = barBtn_PrintData.Enabled = false;
            }
        }

        private void GetInvoices()
        {
            int focusedIndex = (grdCategory.FocusedView as GridView).FocusedRowHandle;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            var invoices = (from c in DB.SL_InvoiceArchives
                            //update
                            join d in DB.SL_InvoiceDetailarchives on c.SL_InvoiceId equals d.SL_InvoiceArchiveId

                            join v in DB.SL_Customers on c.CustomerId equals v.CustomerId
                            // join s in DB.IC_Stores on c.StoreId equals s.StoreId
                             where customerId == 0 ? true : c.CustomerId == customerId
                            where c.InvoiceDate.Date >= dateFrom && c.InvoiceDate.Date <= dateTo
                            where Shared.user.UserChangeStore ? true : c.StoreId == Shared.user.DefaultStore
                            where Shared.user.AccessOtherUserTrns ? true : c.UserId == Shared.UserId

                            from lkp in DB.LKP_Processes.Where(lkp => lkp.ProcessId == c.ProcessId).DefaultIfEmpty()
                            from outTrns in DB.IC_OutTrns.Where(outTrns => c.ProcessId == (int)Process.OutTrns && outTrns.OutTrnsId == c.SourceId).DefaultIfEmpty()
                            from so in DB.SL_SalesOrders.Where(so => c.ProcessId == (int)Process.SalesOrder && so.SL_SalesOrderId == c.SourceId).DefaultIfEmpty()
                            orderby c.InvoiceDate
                            select new
                            {
                                Process = lkp == null? null : (Shared.IsEnglish ? lkp.ProcessEnglishName : lkp.ProcessName),
                                SourceCode = (lkp == null? null : (lkp.ProcessId == (int)Process.OutTrns ? outTrns.OutTrnsCode : so.SalesOrderCode)),
                                DiscountRatio = c.DiscountRatio,
                                DiscountValue = c.DiscountValue,
                                Expenses = c.Expenses,
                                Net = c.Net,
                                Paid = c.Paid,
                                Remains = c.Remains,
                                c.InvoiceCode,
                                c.InvoiceDate,
                                c.JornalId,
                                c.Notes,
                                c.PayMethod,
                                c.SL_InvoiceId,
                                //StoreId = s.StoreNameAr,
                                StoreId=c.StoreId==0?DB.IC_Stores.FirstOrDefault(s=>s.StoreId==d.StoreId).StoreNameAr: DB.IC_Stores.FirstOrDefault(s => s.StoreId == c.StoreId).StoreNameAr,
                                //store = s.StoreId,
                                store = c.StoreId == 0 ? d.StoreId: c.StoreId,

                                c.UserId,
                                CustomerId = v.CusNameAr,
                                CustId = v.CustomerId,
                                c.SalesEmpId,
                                TotalCostPrice = c.TotalCostPrice,
                                Profit = c.Net - c.TotalCostPrice,
                                GroupId = v.CategoryId,
                                c.Is_OutTrans,
                                c.InvoiceBookId,
                                c.AddTaxValue,
                                c.DeductTaxValue,
                                c.TaxValue,
                                c.DueDate,
                                c.CrncId,
                                c.CrncRate,
                                c.DriverName,
                                c.VehicleNumber,
                                c.Destination,
                                profitRatio = c.TotalCostPrice == 0 ? 1 : (c.Net > 0 ? (c.Net - c.TotalCostPrice) / c.Net : 0),
                                ProfitCostRatio = c.TotalCostPrice == 0? 1 :(c.TotalCostPrice > 0 ? (c.Net - c.TotalCostPrice) / c.TotalCostPrice : 0)
                            }).Distinct().ToList();

            grdCategory.DataSource = invoices;

            (grdCategory.FocusedView as GridView).FocusedRowHandle = focusedIndex;

            //try
            //{
            //    decimal profit = Convert.ToDecimal(col_Profit.SummaryItem.SummaryValue);
            //    decimal total = Convert.ToDecimal(col_Net.SummaryItem.SummaryValue);
            //    decimal Cost = Convert.ToDecimal(col_TotalCostPrice.SummaryItem.SummaryValue);

            //    decimal profitRatio = 0;
            //    if (Cost == 0)
            //        profitRatio = 1;
            //    else
            //    {
            //        if (total == 0)
            //            profitRatio = 0;
            //        else
            //            profitRatio = profit / total;
            //    }

            //    col_ProfitRatio.SummaryItem.SetSummary(DevExpress.Data.SummaryItemType.Custom, profitRatio.ToString("p2"));

            //    decimal profitCostRatio = 0;
            //    if (Cost == 0)
            //        profitCostRatio = 1;
            //    else
            //    {
            //        if (Cost == 0)
            //            profitCostRatio = 0;
            //        else
            //            profitCostRatio = profit / Cost;
            //    }

            //    col_ProfitCostRatio.SummaryItem.SetSummary(DevExpress.Data.SummaryItemType.Custom, profitCostRatio.ToString("p2"));
            //}
            //catch
            //{ }
        }

        private void barBtn_Help_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "فاتورة مبيعات جديدة");
        }

        private void mi_OpenDealer_Click(object sender, EventArgs e)
        {
            var view = grdCategory.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            int DealerId = Convert.ToInt32(view.GetRowCellValue(focused_row_index, col_CustId));

            if (ErpUtils.IsFormOpen(typeof(frm_SL_Customer)))
                Application.OpenForms["frm_SL_Customer"].Close();

            new frm_SL_Customer(DealerId).Show();
        }

        private void barBtn_Print1_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(this.Text, "", "", "", grdCategory, false).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }


        private void barBtn_PrintData_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(this.Text, "", "", "", grdCategory, false, true).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }       
    }
}