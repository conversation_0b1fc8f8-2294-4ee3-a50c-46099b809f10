﻿namespace Reports
{
    partial class frm_SL_ItemsSalesDetails
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_SL_ItemsSalesDetails));
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtnPrint = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnPreview = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnRefresh = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.grdCategory = new DevExpress.XtraGrid.GridControl();
            this.bandedGridView1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridBand5 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.col_NetTotalPrice_Local = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.colNetAmountPlusNetTax = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_NetAmount = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_NetTotalSalesTaxValue = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_TotalBonusRatio = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_TotalBonus = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_ExtraBonus = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_BonusRatio = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_NetBonus = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_NetPiecesCount = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_NetQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand3 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.col_ReturnAmount = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_ReturnTotalSalesTaxValue = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.colCurrencyRt = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.rep_Currency = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.colExchangeRate_Rt = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_ReturnBonus = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.colReturnkgWeight = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_ReturnLibraQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_ReturnPiecesCount = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_ReturnQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand1 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.col_SoldAmount = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_SoldTotalSalesTaxValue = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.colCurrencySL = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.colExchangeRate_Sl = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_SoldLibraQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_SoldkgWeight = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_SoldPiecesCount = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_SoldBonus = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_SoldQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand2 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.col_SalesTaxRatio = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.rep_Ratio = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.col_Category = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_width = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_length = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_height = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_ItemNameAr = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_ItemCode2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_ItemCode1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.picLogo = new DevExpress.XtraEditors.PictureEdit();
            this.lblReportName = new DevExpress.XtraEditors.TextEdit();
            this.lblDateFilter = new DevExpress.XtraEditors.TextEdit();
            this.lblFilter = new DevExpress.XtraEditors.TextEdit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdCategory)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Currency)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Ratio)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblReportName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDateFilter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFilter.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnPreview,
            this.barBtnClose,
            this.barBtnPrint,
            this.barBtnRefresh});
            this.barManager1.MaxItemId = 29;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(567, 147);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnPrint),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnPreview),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnRefresh),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtnPrint
            // 
            this.barBtnPrint.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnPrint, "barBtnPrint");
            this.barBtnPrint.Glyph = ((System.Drawing.Image)(resources.GetObject("barBtnPrint.Glyph")));
            this.barBtnPrint.Id = 27;
            this.barBtnPrint.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.P));
            this.barBtnPrint.Name = "barBtnPrint";
            this.barBtnPrint.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnPrint.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnPrint_ItemClick);
            // 
            // barBtnPreview
            // 
            this.barBtnPreview.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnPreview, "barBtnPreview");
            this.barBtnPreview.Glyph = ((System.Drawing.Image)(resources.GetObject("barBtnPreview.Glyph")));
            this.barBtnPreview.Id = 1;
            this.barBtnPreview.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.O));
            this.barBtnPreview.Name = "barBtnPreview";
            this.barBtnPreview.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnPreview.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Preview_ItemClick);
            // 
            // barBtnRefresh
            // 
            this.barBtnRefresh.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnRefresh, "barBtnRefresh");
            this.barBtnRefresh.Glyph = ((System.Drawing.Image)(resources.GetObject("barBtnRefresh.Glyph")));
            this.barBtnRefresh.Id = 28;
            this.barBtnRefresh.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.R));
            this.barBtnRefresh.Name = "barBtnRefresh";
            this.barBtnRefresh.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnRefresh.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnRefresh_ItemClick);
            // 
            // barBtnClose
            // 
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Glyph = ((System.Drawing.Image)(resources.GetObject("barBtnClose.Glyph")));
            this.barBtnClose.Id = 25;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Close_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // grdCategory
            // 
            resources.ApplyResources(this.grdCategory, "grdCategory");
            this.grdCategory.MainView = this.bandedGridView1;
            this.grdCategory.Name = "grdCategory";
            this.grdCategory.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.rep_Ratio,
            this.rep_Currency});
            this.grdCategory.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView1});
            // 
            // bandedGridView1
            // 
            this.bandedGridView1.Appearance.BandPanel.Options.UseTextOptions = true;
            this.bandedGridView1.Appearance.BandPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bandedGridView1.Appearance.BandPanel.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.bandedGridView1.Appearance.BandPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.bandedGridView1.Appearance.FooterPanel.Options.UseTextOptions = true;
            this.bandedGridView1.Appearance.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bandedGridView1.Appearance.GroupPanel.Options.UseTextOptions = true;
            this.bandedGridView1.Appearance.GroupPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bandedGridView1.Appearance.GroupRow.Options.UseTextOptions = true;
            this.bandedGridView1.Appearance.GroupRow.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bandedGridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.bandedGridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bandedGridView1.Appearance.HeaderPanel.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.bandedGridView1.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.bandedGridView1.Appearance.Row.Options.UseTextOptions = true;
            this.bandedGridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bandedGridView1.Appearance.Row.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.bandedGridView1.Appearance.Row.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.bandedGridView1.AppearancePrint.BandPanel.BackColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.BandPanel.BackColor")));
            this.bandedGridView1.AppearancePrint.BandPanel.Font = ((System.Drawing.Font)(resources.GetObject("bandedGridView1.AppearancePrint.BandPanel.Font")));
            this.bandedGridView1.AppearancePrint.BandPanel.Options.UseBackColor = true;
            this.bandedGridView1.AppearancePrint.BandPanel.Options.UseFont = true;
            this.bandedGridView1.AppearancePrint.FooterPanel.BackColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.FooterPanel.BackColor")));
            this.bandedGridView1.AppearancePrint.FooterPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.FooterPanel.BorderColor")));
            this.bandedGridView1.AppearancePrint.FooterPanel.Font = ((System.Drawing.Font)(resources.GetObject("bandedGridView1.AppearancePrint.FooterPanel.Font")));
            this.bandedGridView1.AppearancePrint.FooterPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.FooterPanel.ForeColor")));
            this.bandedGridView1.AppearancePrint.FooterPanel.Options.UseBackColor = true;
            this.bandedGridView1.AppearancePrint.FooterPanel.Options.UseBorderColor = true;
            this.bandedGridView1.AppearancePrint.FooterPanel.Options.UseFont = true;
            this.bandedGridView1.AppearancePrint.FooterPanel.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.FooterPanel.Options.UseTextOptions = true;
            this.bandedGridView1.AppearancePrint.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bandedGridView1.AppearancePrint.GroupFooter.BorderColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.GroupFooter.BorderColor")));
            this.bandedGridView1.AppearancePrint.GroupFooter.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.GroupFooter.ForeColor")));
            this.bandedGridView1.AppearancePrint.GroupFooter.Options.UseBorderColor = true;
            this.bandedGridView1.AppearancePrint.GroupFooter.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.GroupFooter.Options.UseTextOptions = true;
            this.bandedGridView1.AppearancePrint.GroupFooter.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bandedGridView1.AppearancePrint.GroupRow.BorderColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.GroupRow.BorderColor")));
            this.bandedGridView1.AppearancePrint.GroupRow.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.GroupRow.ForeColor")));
            this.bandedGridView1.AppearancePrint.GroupRow.Options.UseBorderColor = true;
            this.bandedGridView1.AppearancePrint.GroupRow.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.GroupRow.Options.UseTextOptions = true;
            this.bandedGridView1.AppearancePrint.GroupRow.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bandedGridView1.AppearancePrint.HeaderPanel.BackColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.HeaderPanel.BackColor")));
            this.bandedGridView1.AppearancePrint.HeaderPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.HeaderPanel.BorderColor")));
            this.bandedGridView1.AppearancePrint.HeaderPanel.Font = ((System.Drawing.Font)(resources.GetObject("bandedGridView1.AppearancePrint.HeaderPanel.Font")));
            this.bandedGridView1.AppearancePrint.HeaderPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.HeaderPanel.ForeColor")));
            this.bandedGridView1.AppearancePrint.HeaderPanel.Options.UseBackColor = true;
            this.bandedGridView1.AppearancePrint.HeaderPanel.Options.UseBorderColor = true;
            this.bandedGridView1.AppearancePrint.HeaderPanel.Options.UseFont = true;
            this.bandedGridView1.AppearancePrint.HeaderPanel.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.HeaderPanel.Options.UseTextOptions = true;
            this.bandedGridView1.AppearancePrint.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bandedGridView1.AppearancePrint.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.bandedGridView1.AppearancePrint.Lines.BackColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.Lines.BackColor")));
            this.bandedGridView1.AppearancePrint.Lines.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.Lines.ForeColor")));
            this.bandedGridView1.AppearancePrint.Lines.Options.UseBackColor = true;
            this.bandedGridView1.AppearancePrint.Lines.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.Row.BorderColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.Row.BorderColor")));
            this.bandedGridView1.AppearancePrint.Row.Font = ((System.Drawing.Font)(resources.GetObject("bandedGridView1.AppearancePrint.Row.Font")));
            this.bandedGridView1.AppearancePrint.Row.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.Row.ForeColor")));
            this.bandedGridView1.AppearancePrint.Row.Options.UseBorderColor = true;
            this.bandedGridView1.AppearancePrint.Row.Options.UseFont = true;
            this.bandedGridView1.AppearancePrint.Row.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.Row.Options.UseTextOptions = true;
            this.bandedGridView1.AppearancePrint.Row.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.bandedGridView1.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand5,
            this.gridBand3,
            this.gridBand1,
            this.gridBand2});
            this.bandedGridView1.ColumnPanelRowHeight = 40;
            this.bandedGridView1.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.col_width,
            this.col_height,
            this.col_length,
            this.col_ItemNameAr,
            this.col_ItemCode2,
            this.col_ItemCode1,
            this.col_SoldQty,
            this.col_SoldAmount,
            this.col_ReturnQty,
            this.col_ReturnAmount,
            this.col_SoldBonus,
            this.col_SoldTotalSalesTaxValue,
            this.col_ReturnTotalSalesTaxValue,
            this.col_NetQty,
            this.col_NetAmount,
            this.col_NetTotalSalesTaxValue,
            this.col_ReturnBonus,
            this.col_NetBonus,
            this.col_SalesTaxRatio,
            this.colNetAmountPlusNetTax,
            this.col_ExtraBonus,
            this.col_TotalBonus,
            this.col_BonusRatio,
            this.col_TotalBonusRatio,
            this.bandedGridColumn1,
            this.colCurrencySL,
            this.colCurrencyRt,
            this.colExchangeRate_Sl,
            this.colExchangeRate_Rt,
            this.col_NetTotalPrice_Local,
            this.colReturnkgWeight,
            this.col_ReturnLibraQty,
            this.col_SoldkgWeight,
            this.col_SoldLibraQty,
            this.col_Category,
            this.col_SoldPiecesCount,
            this.col_ReturnPiecesCount,
            this.col_NetPiecesCount});
            this.bandedGridView1.CustomizationFormBounds = new System.Drawing.Rectangle(1150, 331, 216, 348);
            this.bandedGridView1.GridControl = this.grdCategory;
            this.bandedGridView1.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("bandedGridView1.GroupSummary"))), resources.GetString("bandedGridView1.GroupSummary1"), this.col_SoldAmount, resources.GetString("bandedGridView1.GroupSummary2")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("bandedGridView1.GroupSummary3"))), resources.GetString("bandedGridView1.GroupSummary4"), this.col_NetAmount, resources.GetString("bandedGridView1.GroupSummary5"))});
            this.bandedGridView1.Name = "bandedGridView1";
            this.bandedGridView1.OptionsBehavior.Editable = false;
            this.bandedGridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.bandedGridView1.OptionsSelection.MultiSelect = true;
            this.bandedGridView1.OptionsView.EnableAppearanceEvenRow = true;
            this.bandedGridView1.OptionsView.ShowAutoFilterRow = true;
            this.bandedGridView1.OptionsView.ShowFooter = true;
            this.bandedGridView1.OptionsView.ShowIndicator = false;
            // 
            // gridBand5
            // 
            resources.ApplyResources(this.gridBand5, "gridBand5");
            this.gridBand5.Columns.Add(this.col_NetTotalPrice_Local);
            this.gridBand5.Columns.Add(this.colNetAmountPlusNetTax);
            this.gridBand5.Columns.Add(this.col_NetAmount);
            this.gridBand5.Columns.Add(this.col_NetTotalSalesTaxValue);
            this.gridBand5.Columns.Add(this.col_TotalBonusRatio);
            this.gridBand5.Columns.Add(this.col_TotalBonus);
            this.gridBand5.Columns.Add(this.col_ExtraBonus);
            this.gridBand5.Columns.Add(this.col_BonusRatio);
            this.gridBand5.Columns.Add(this.col_NetBonus);
            this.gridBand5.Columns.Add(this.col_NetPiecesCount);
            this.gridBand5.Columns.Add(this.col_NetQty);
            this.gridBand5.VisibleIndex = 0;
            // 
            // col_NetTotalPrice_Local
            // 
            this.col_NetTotalPrice_Local.FieldName = "NetTotalPrice_Local";
            this.col_NetTotalPrice_Local.Name = "col_NetTotalPrice_Local";
            this.col_NetTotalPrice_Local.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_NetTotalPrice_Local.Summary"))), resources.GetString("col_NetTotalPrice_Local.Summary1"), resources.GetString("col_NetTotalPrice_Local.Summary2"))});
            resources.ApplyResources(this.col_NetTotalPrice_Local, "col_NetTotalPrice_Local");
            // 
            // colNetAmountPlusNetTax
            // 
            resources.ApplyResources(this.colNetAmountPlusNetTax, "colNetAmountPlusNetTax");
            this.colNetAmountPlusNetTax.DisplayFormat.FormatString = "n2";
            this.colNetAmountPlusNetTax.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colNetAmountPlusNetTax.FieldName = "NetAmountPlusNetTax";
            this.colNetAmountPlusNetTax.Name = "colNetAmountPlusNetTax";
            this.colNetAmountPlusNetTax.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.colNetAmountPlusNetTax.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("colNetAmountPlusNetTax.Summary"))), resources.GetString("colNetAmountPlusNetTax.Summary1"), resources.GetString("colNetAmountPlusNetTax.Summary2"))});
            // 
            // col_NetAmount
            // 
            resources.ApplyResources(this.col_NetAmount, "col_NetAmount");
            this.col_NetAmount.DisplayFormat.FormatString = "n2";
            this.col_NetAmount.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_NetAmount.FieldName = "NetAmount";
            this.col_NetAmount.Name = "col_NetAmount";
            this.col_NetAmount.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_NetAmount.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_NetAmount.Summary"))), resources.GetString("col_NetAmount.Summary1"), resources.GetString("col_NetAmount.Summary2"))});
            // 
            // col_NetTotalSalesTaxValue
            // 
            resources.ApplyResources(this.col_NetTotalSalesTaxValue, "col_NetTotalSalesTaxValue");
            this.col_NetTotalSalesTaxValue.DisplayFormat.FormatString = "n2";
            this.col_NetTotalSalesTaxValue.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_NetTotalSalesTaxValue.FieldName = "NetTotalSalesTaxValue";
            this.col_NetTotalSalesTaxValue.Name = "col_NetTotalSalesTaxValue";
            this.col_NetTotalSalesTaxValue.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_NetTotalSalesTaxValue.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_NetTotalSalesTaxValue.Summary"))), resources.GetString("col_NetTotalSalesTaxValue.Summary1"), resources.GetString("col_NetTotalSalesTaxValue.Summary2"))});
            // 
            // col_TotalBonusRatio
            // 
            resources.ApplyResources(this.col_TotalBonusRatio, "col_TotalBonusRatio");
            this.col_TotalBonusRatio.DisplayFormat.FormatString = "p2";
            this.col_TotalBonusRatio.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_TotalBonusRatio.FieldName = "TotalBonusRatio";
            this.col_TotalBonusRatio.Name = "col_TotalBonusRatio";
            // 
            // col_TotalBonus
            // 
            resources.ApplyResources(this.col_TotalBonus, "col_TotalBonus");
            this.col_TotalBonus.DisplayFormat.FormatString = "n2";
            this.col_TotalBonus.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_TotalBonus.FieldName = "TotalBonus";
            this.col_TotalBonus.Name = "col_TotalBonus";
            this.col_TotalBonus.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_TotalBonus.Summary"))), resources.GetString("col_TotalBonus.Summary1"), resources.GetString("col_TotalBonus.Summary2"))});
            // 
            // col_ExtraBonus
            // 
            resources.ApplyResources(this.col_ExtraBonus, "col_ExtraBonus");
            this.col_ExtraBonus.DisplayFormat.FormatString = "n2";
            this.col_ExtraBonus.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_ExtraBonus.FieldName = "ExtraBonus";
            this.col_ExtraBonus.Name = "col_ExtraBonus";
            this.col_ExtraBonus.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_ExtraBonus.Summary"))), resources.GetString("col_ExtraBonus.Summary1"), resources.GetString("col_ExtraBonus.Summary2"))});
            // 
            // col_BonusRatio
            // 
            resources.ApplyResources(this.col_BonusRatio, "col_BonusRatio");
            this.col_BonusRatio.DisplayFormat.FormatString = "p2";
            this.col_BonusRatio.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_BonusRatio.FieldName = "BonusRatio";
            this.col_BonusRatio.Name = "col_BonusRatio";
            // 
            // col_NetBonus
            // 
            resources.ApplyResources(this.col_NetBonus, "col_NetBonus");
            this.col_NetBonus.DisplayFormat.FormatString = "n2";
            this.col_NetBonus.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_NetBonus.FieldName = "NetBonus";
            this.col_NetBonus.Name = "col_NetBonus";
            this.col_NetBonus.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_NetBonus.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_NetBonus.Summary"))), resources.GetString("col_NetBonus.Summary1"), resources.GetString("col_NetBonus.Summary2"))});
            // 
            // col_NetPiecesCount
            // 
            resources.ApplyResources(this.col_NetPiecesCount, "col_NetPiecesCount");
            this.col_NetPiecesCount.DisplayFormat.FormatString = "n2";
            this.col_NetPiecesCount.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_NetPiecesCount.FieldName = "NetPiecesCount";
            this.col_NetPiecesCount.Name = "col_NetPiecesCount";
            // 
            // col_NetQty
            // 
            resources.ApplyResources(this.col_NetQty, "col_NetQty");
            this.col_NetQty.DisplayFormat.FormatString = "n2";
            this.col_NetQty.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_NetQty.FieldName = "NetQty";
            this.col_NetQty.Name = "col_NetQty";
            this.col_NetQty.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_NetQty.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_NetQty.Summary"))), resources.GetString("col_NetQty.Summary1"), resources.GetString("col_NetQty.Summary2"))});
            // 
            // gridBand3
            // 
            resources.ApplyResources(this.gridBand3, "gridBand3");
            this.gridBand3.Columns.Add(this.col_ReturnAmount);
            this.gridBand3.Columns.Add(this.col_ReturnTotalSalesTaxValue);
            this.gridBand3.Columns.Add(this.colCurrencyRt);
            this.gridBand3.Columns.Add(this.colExchangeRate_Rt);
            this.gridBand3.Columns.Add(this.col_ReturnBonus);
            this.gridBand3.Columns.Add(this.colReturnkgWeight);
            this.gridBand3.Columns.Add(this.col_ReturnLibraQty);
            this.gridBand3.Columns.Add(this.col_ReturnPiecesCount);
            this.gridBand3.Columns.Add(this.col_ReturnQty);
            this.gridBand3.VisibleIndex = 1;
            // 
            // col_ReturnAmount
            // 
            resources.ApplyResources(this.col_ReturnAmount, "col_ReturnAmount");
            this.col_ReturnAmount.DisplayFormat.FormatString = "n2";
            this.col_ReturnAmount.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_ReturnAmount.FieldName = "ReturnAmount";
            this.col_ReturnAmount.Name = "col_ReturnAmount";
            this.col_ReturnAmount.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_ReturnAmount.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_ReturnAmount.Summary"))), resources.GetString("col_ReturnAmount.Summary1"), resources.GetString("col_ReturnAmount.Summary2"))});
            // 
            // col_ReturnTotalSalesTaxValue
            // 
            resources.ApplyResources(this.col_ReturnTotalSalesTaxValue, "col_ReturnTotalSalesTaxValue");
            this.col_ReturnTotalSalesTaxValue.DisplayFormat.FormatString = "n2";
            this.col_ReturnTotalSalesTaxValue.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_ReturnTotalSalesTaxValue.FieldName = "ReturnTotalSalesTaxValue";
            this.col_ReturnTotalSalesTaxValue.Name = "col_ReturnTotalSalesTaxValue";
            this.col_ReturnTotalSalesTaxValue.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_ReturnTotalSalesTaxValue.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_ReturnTotalSalesTaxValue.Summary"))), resources.GetString("col_ReturnTotalSalesTaxValue.Summary1"), resources.GetString("col_ReturnTotalSalesTaxValue.Summary2"))});
            // 
            // colCurrencyRt
            // 
            resources.ApplyResources(this.colCurrencyRt, "colCurrencyRt");
            this.colCurrencyRt.ColumnEdit = this.rep_Currency;
            this.colCurrencyRt.FieldName = "ReturnCrncId";
            this.colCurrencyRt.Name = "colCurrencyRt";
            // 
            // rep_Currency
            // 
            resources.ApplyResources(this.rep_Currency, "rep_Currency");
            this.rep_Currency.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Currency.Buttons"))))});
            this.rep_Currency.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_Currency.Columns"), resources.GetString("rep_Currency.Columns1")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_Currency.Columns2"), resources.GetString("rep_Currency.Columns3"), ((int)(resources.GetObject("rep_Currency.Columns4"))), ((DevExpress.Utils.FormatType)(resources.GetObject("rep_Currency.Columns5"))), resources.GetString("rep_Currency.Columns6"), ((bool)(resources.GetObject("rep_Currency.Columns7"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("rep_Currency.Columns8"))))});
            this.rep_Currency.Name = "rep_Currency";
            // 
            // colExchangeRate_Rt
            // 
            resources.ApplyResources(this.colExchangeRate_Rt, "colExchangeRate_Rt");
            this.colExchangeRate_Rt.FieldName = "ReturnCrncRate";
            this.colExchangeRate_Rt.Name = "colExchangeRate_Rt";
            // 
            // col_ReturnBonus
            // 
            resources.ApplyResources(this.col_ReturnBonus, "col_ReturnBonus");
            this.col_ReturnBonus.DisplayFormat.FormatString = "n2";
            this.col_ReturnBonus.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_ReturnBonus.FieldName = "ReturnBonus";
            this.col_ReturnBonus.Name = "col_ReturnBonus";
            this.col_ReturnBonus.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_ReturnBonus.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_ReturnBonus.Summary"))), resources.GetString("col_ReturnBonus.Summary1"), resources.GetString("col_ReturnBonus.Summary2"))});
            // 
            // colReturnkgWeight
            // 
            resources.ApplyResources(this.colReturnkgWeight, "colReturnkgWeight");
            this.colReturnkgWeight.DisplayFormat.FormatString = "n2";
            this.colReturnkgWeight.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colReturnkgWeight.FieldName = "ReturnkgWeight";
            this.colReturnkgWeight.Name = "colReturnkgWeight";
            // 
            // col_ReturnLibraQty
            // 
            resources.ApplyResources(this.col_ReturnLibraQty, "col_ReturnLibraQty");
            this.col_ReturnLibraQty.DisplayFormat.FormatString = "n2";
            this.col_ReturnLibraQty.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_ReturnLibraQty.FieldName = "ReturnLibraQty";
            this.col_ReturnLibraQty.Name = "col_ReturnLibraQty";
            // 
            // col_ReturnPiecesCount
            // 
            resources.ApplyResources(this.col_ReturnPiecesCount, "col_ReturnPiecesCount");
            this.col_ReturnPiecesCount.DisplayFormat.FormatString = "n2";
            this.col_ReturnPiecesCount.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_ReturnPiecesCount.FieldName = "ReturnPiecesCount";
            this.col_ReturnPiecesCount.Name = "col_ReturnPiecesCount";
            // 
            // col_ReturnQty
            // 
            resources.ApplyResources(this.col_ReturnQty, "col_ReturnQty");
            this.col_ReturnQty.DisplayFormat.FormatString = "n2";
            this.col_ReturnQty.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_ReturnQty.FieldName = "ReturnQty";
            this.col_ReturnQty.Name = "col_ReturnQty";
            this.col_ReturnQty.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_ReturnQty.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_ReturnQty.Summary"))), resources.GetString("col_ReturnQty.Summary1"), resources.GetString("col_ReturnQty.Summary2"))});
            // 
            // gridBand1
            // 
            resources.ApplyResources(this.gridBand1, "gridBand1");
            this.gridBand1.Columns.Add(this.col_SoldAmount);
            this.gridBand1.Columns.Add(this.col_SoldTotalSalesTaxValue);
            this.gridBand1.Columns.Add(this.colCurrencySL);
            this.gridBand1.Columns.Add(this.colExchangeRate_Sl);
            this.gridBand1.Columns.Add(this.col_SoldLibraQty);
            this.gridBand1.Columns.Add(this.col_SoldkgWeight);
            this.gridBand1.Columns.Add(this.col_SoldPiecesCount);
            this.gridBand1.Columns.Add(this.col_SoldBonus);
            this.gridBand1.Columns.Add(this.col_SoldQty);
            this.gridBand1.VisibleIndex = 2;
            // 
            // col_SoldAmount
            // 
            resources.ApplyResources(this.col_SoldAmount, "col_SoldAmount");
            this.col_SoldAmount.DisplayFormat.FormatString = "n2";
            this.col_SoldAmount.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_SoldAmount.FieldName = "SoldAmount";
            this.col_SoldAmount.Name = "col_SoldAmount";
            this.col_SoldAmount.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_SoldAmount.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_SoldAmount.Summary"))), resources.GetString("col_SoldAmount.Summary1"), resources.GetString("col_SoldAmount.Summary2"))});
            // 
            // col_SoldTotalSalesTaxValue
            // 
            resources.ApplyResources(this.col_SoldTotalSalesTaxValue, "col_SoldTotalSalesTaxValue");
            this.col_SoldTotalSalesTaxValue.DisplayFormat.FormatString = "n2";
            this.col_SoldTotalSalesTaxValue.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_SoldTotalSalesTaxValue.FieldName = "SoldTotalSalesTaxValue";
            this.col_SoldTotalSalesTaxValue.Name = "col_SoldTotalSalesTaxValue";
            this.col_SoldTotalSalesTaxValue.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_SoldTotalSalesTaxValue.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_SoldTotalSalesTaxValue.Summary"))), resources.GetString("col_SoldTotalSalesTaxValue.Summary1"), resources.GetString("col_SoldTotalSalesTaxValue.Summary2"))});
            // 
            // colCurrencySL
            // 
            resources.ApplyResources(this.colCurrencySL, "colCurrencySL");
            this.colCurrencySL.ColumnEdit = this.rep_Currency;
            this.colCurrencySL.FieldName = "SellCrncId";
            this.colCurrencySL.Name = "colCurrencySL";
            // 
            // colExchangeRate_Sl
            // 
            resources.ApplyResources(this.colExchangeRate_Sl, "colExchangeRate_Sl");
            this.colExchangeRate_Sl.FieldName = "SellCrncRate";
            this.colExchangeRate_Sl.Name = "colExchangeRate_Sl";
            // 
            // col_SoldLibraQty
            // 
            resources.ApplyResources(this.col_SoldLibraQty, "col_SoldLibraQty");
            this.col_SoldLibraQty.DisplayFormat.FormatString = "n2";
            this.col_SoldLibraQty.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_SoldLibraQty.FieldName = "SoldLibraQty";
            this.col_SoldLibraQty.Name = "col_SoldLibraQty";
            // 
            // col_SoldkgWeight
            // 
            resources.ApplyResources(this.col_SoldkgWeight, "col_SoldkgWeight");
            this.col_SoldkgWeight.DisplayFormat.FormatString = "n2";
            this.col_SoldkgWeight.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_SoldkgWeight.FieldName = "SoldkgWeight";
            this.col_SoldkgWeight.Name = "col_SoldkgWeight";
            // 
            // col_SoldPiecesCount
            // 
            resources.ApplyResources(this.col_SoldPiecesCount, "col_SoldPiecesCount");
            this.col_SoldPiecesCount.DisplayFormat.FormatString = "n2";
            this.col_SoldPiecesCount.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_SoldPiecesCount.FieldName = "SoldPiecesCount";
            this.col_SoldPiecesCount.Name = "col_SoldPiecesCount";
            // 
            // col_SoldBonus
            // 
            resources.ApplyResources(this.col_SoldBonus, "col_SoldBonus");
            this.col_SoldBonus.DisplayFormat.FormatString = "n2";
            this.col_SoldBonus.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_SoldBonus.FieldName = "SoldBonus";
            this.col_SoldBonus.Name = "col_SoldBonus";
            this.col_SoldBonus.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_SoldBonus.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_SoldBonus.Summary"))), resources.GetString("col_SoldBonus.Summary1"), resources.GetString("col_SoldBonus.Summary2"))});
            // 
            // col_SoldQty
            // 
            resources.ApplyResources(this.col_SoldQty, "col_SoldQty");
            this.col_SoldQty.DisplayFormat.FormatString = "n2";
            this.col_SoldQty.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_SoldQty.FieldName = "SoldQty";
            this.col_SoldQty.Name = "col_SoldQty";
            this.col_SoldQty.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_SoldQty.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_SoldQty.Summary"))), resources.GetString("col_SoldQty.Summary1"), resources.GetString("col_SoldQty.Summary2"))});
            // 
            // gridBand2
            // 
            resources.ApplyResources(this.gridBand2, "gridBand2");
            this.gridBand2.Columns.Add(this.col_SalesTaxRatio);
            this.gridBand2.Columns.Add(this.col_Category);
            this.gridBand2.Columns.Add(this.col_width);
            this.gridBand2.Columns.Add(this.col_length);
            this.gridBand2.Columns.Add(this.col_height);
            this.gridBand2.Columns.Add(this.col_ItemNameAr);
            this.gridBand2.Columns.Add(this.col_ItemCode2);
            this.gridBand2.Columns.Add(this.col_ItemCode1);
            this.gridBand2.VisibleIndex = 3;
            // 
            // col_SalesTaxRatio
            // 
            resources.ApplyResources(this.col_SalesTaxRatio, "col_SalesTaxRatio");
            this.col_SalesTaxRatio.ColumnEdit = this.rep_Ratio;
            this.col_SalesTaxRatio.FieldName = "SalesTaxRatio";
            this.col_SalesTaxRatio.Name = "col_SalesTaxRatio";
            this.col_SalesTaxRatio.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // rep_Ratio
            // 
            resources.ApplyResources(this.rep_Ratio, "rep_Ratio");
            this.rep_Ratio.Mask.EditMask = resources.GetString("rep_Ratio.Mask.EditMask");
            this.rep_Ratio.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("rep_Ratio.Mask.MaskType")));
            this.rep_Ratio.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("rep_Ratio.Mask.UseMaskAsDisplayFormat")));
            this.rep_Ratio.Name = "rep_Ratio";
            // 
            // col_Category
            // 
            this.col_Category.FieldName = "Category";
            this.col_Category.Name = "col_Category";
            // 
            // col_width
            // 
            resources.ApplyResources(this.col_width, "col_width");
            this.col_width.FieldName = "Width";
            this.col_width.Name = "col_width";
            // 
            // col_length
            // 
            resources.ApplyResources(this.col_length, "col_length");
            this.col_length.FieldName = "Length";
            this.col_length.Name = "col_length";
            // 
            // col_height
            // 
            resources.ApplyResources(this.col_height, "col_height");
            this.col_height.FieldName = "Height";
            this.col_height.Name = "col_height";
            // 
            // col_ItemNameAr
            // 
            resources.ApplyResources(this.col_ItemNameAr, "col_ItemNameAr");
            this.col_ItemNameAr.FieldName = "ItemNameAr";
            this.col_ItemNameAr.Name = "col_ItemNameAr";
            this.col_ItemNameAr.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col_ItemNameAr.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // col_ItemCode2
            // 
            resources.ApplyResources(this.col_ItemCode2, "col_ItemCode2");
            this.col_ItemCode2.FieldName = "ItemCode2";
            this.col_ItemCode2.Name = "col_ItemCode2";
            this.col_ItemCode2.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col_ItemCode2.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // col_ItemCode1
            // 
            resources.ApplyResources(this.col_ItemCode1, "col_ItemCode1");
            this.col_ItemCode1.FieldName = "ItemCode1";
            this.col_ItemCode1.Name = "col_ItemCode1";
            this.col_ItemCode1.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col_ItemCode1.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // bandedGridColumn1
            // 
            resources.ApplyResources(this.bandedGridColumn1, "bandedGridColumn1");
            this.bandedGridColumn1.Name = "bandedGridColumn1";
            // 
            // picLogo
            // 
            resources.ApplyResources(this.picLogo, "picLogo");
            this.picLogo.MenuManager = this.barManager1;
            this.picLogo.Name = "picLogo";
            this.picLogo.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Stretch;
            // 
            // lblReportName
            // 
            resources.ApplyResources(this.lblReportName, "lblReportName");
            this.lblReportName.MenuManager = this.barManager1;
            this.lblReportName.Name = "lblReportName";
            this.lblReportName.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("lblReportName.Properties.Appearance.Font")));
            this.lblReportName.Properties.Appearance.Options.UseFont = true;
            this.lblReportName.Properties.Appearance.Options.UseTextOptions = true;
            this.lblReportName.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            // 
            // lblDateFilter
            // 
            resources.ApplyResources(this.lblDateFilter, "lblDateFilter");
            this.lblDateFilter.MenuManager = this.barManager1;
            this.lblDateFilter.Name = "lblDateFilter";
            this.lblDateFilter.Properties.Appearance.Options.UseTextOptions = true;
            this.lblDateFilter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            // 
            // lblFilter
            // 
            resources.ApplyResources(this.lblFilter, "lblFilter");
            this.lblFilter.MenuManager = this.barManager1;
            this.lblFilter.Name = "lblFilter";
            this.lblFilter.Properties.Appearance.Options.UseTextOptions = true;
            this.lblFilter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            // 
            // frm_SL_ItemsSalesDetails
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.picLogo);
            this.Controls.Add(this.grdCategory);
            this.Controls.Add(this.lblFilter);
            this.Controls.Add(this.lblDateFilter);
            this.Controls.Add(this.lblReportName);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.KeyPreview = true;
            this.Name = "frm_SL_ItemsSalesDetails";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_Rep_FormClosing);
            this.Load += new System.EventHandler(this.frm_SL_InvoiceList_Load);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdCategory)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Currency)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Ratio)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblReportName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDateFilter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFilter.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnPreview;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraGrid.GridControl grdCategory;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraBars.BarButtonItem barBtnPrint;
        private DevExpress.XtraEditors.TextEdit lblDateFilter;
        private DevExpress.XtraEditors.TextEdit lblReportName;
        private DevExpress.XtraEditors.PictureEdit picLogo;
        private DevExpress.XtraEditors.TextEdit lblFilter;
        private DevExpress.XtraBars.BarButtonItem barBtnRefresh;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_ItemNameAr;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_ItemCode2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_ItemCode1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_SoldQty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_SoldAmount;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_ReturnAmount;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_ReturnQty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_SoldBonus;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_NetTotalSalesTaxValue;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_NetAmount;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_NetQty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_ReturnTotalSalesTaxValue;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_SoldTotalSalesTaxValue;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_ReturnBonus;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_NetBonus;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_SalesTaxRatio;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit rep_Ratio;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn colNetAmountPlusNetTax;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_ExtraBonus;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_TotalBonus;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_BonusRatio;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_TotalBonusRatio;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_NetTotalPrice_Local;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn colCurrencyRt;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn colExchangeRate_Rt;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn colCurrencySL;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn colExchangeRate_Sl;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn1;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit rep_Currency;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_Category;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_width;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_height;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_length;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn colReturnkgWeight;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_ReturnLibraQty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_SoldLibraQty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_SoldkgWeight;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand5;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_NetPiecesCount;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_ReturnPiecesCount;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_SoldPiecesCount;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand2;
    }
}