<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="cell_TotalQty.Text" xml:space="preserve">
    <value>TotalQty</value>
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="lbl_DiscountR.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>112.5, 51.0000229</value>
  </data>
  <data name="xrTableCell7.Text" xml:space="preserve">
    <value>تاريخ الصلاحية</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="xrLabel9.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="cell_Batch.Weight" type="System.Double, mscorlib">
    <value>0.25298175374970178</value>
  </data>
  <data name="lbl_Remains.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lbl_Remains.Text" xml:space="preserve">
    <value> </value>
  </data>
  <data name="cell_Price.Text" xml:space="preserve">
    <value>السعر</value>
  </data>
  <data name="xrLabel1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="cell_Pack.Weight" type="System.Double, mscorlib">
    <value>0.1875</value>
  </data>
  <data name="TopMargin.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="cell_Total.Text" xml:space="preserve">
    <value>الاجمالي</value>
  </data>
  <data name="lbl_VehicleNumber.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="xrTableCell8.Text" xml:space="preserve">
    <value>كود</value>
  </data>
  <data name="cell_Qty.Weight" type="System.Double, mscorlib">
    <value>0.20813447646512329</value>
  </data>
  <data name="xrLabel8.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lbl_Total.Text" xml:space="preserve">
    <value> </value>
  </data>
  <data name="lbl_DiscountV.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrLabel7.Text" xml:space="preserve">
    <value>التاريخ</value>
  </data>
  <data name="xrLabel6.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>134.0833, 95.54167</value>
  </data>
  <data name="lbl_DiscountR.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lbl_Destination.Text" xml:space="preserve">
    <value>..</value>
  </data>
  <data name="txt_custax.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>134.083313, 24.4999771</value>
  </data>
  <data name="lblReportName.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>171.8751, 24.49998</value>
  </data>
  <data name="xrTableRow1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrTableCell9.Text" xml:space="preserve">
    <value>قيمة خصم</value>
  </data>
  <data name="xrLabel23.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>134.083313, 124.5001</value>
  </data>
  <data name="lbl_Tax.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrLabel8.Text" xml:space="preserve">
    <value>المخزن</value>
  </data>
  <data name="lbl_DeductTaxV.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrLabel16.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lbl_DiscountR.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrTableCell1.Text" xml:space="preserve">
    <value>الاجمالي</value>
  </data>
  <data name="xrLabel1.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="cell_Serial.Text" xml:space="preserve">
    <value>cell_Serial</value>
  </data>
  <data name="lbl_DiscountV.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>81.95837, 24.499958</value>
  </data>
  <data name="lbl_Number.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>580.333, 144.5417</value>
  </data>
  <data name="xrLabel7.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>86.4585, 24.49999</value>
  </data>
  <data name="lblReportName.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel6.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrLabel16.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>81.25018, 24.49998</value>
  </data>
  <data name="xrTableCell1.Weight" type="System.Double, mscorlib">
    <value>0.18439886224178867</value>
  </data>
  <data name="cell_Batch.Text" xml:space="preserve">
    <value>التشغيلة</value>
  </data>
  <data name="lbl_AddTaxV.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="cell_DiscountRatio3.Weight" type="System.Double, mscorlib">
    <value>0.615416259765625</value>
  </data>
  <data name="cell_SalesTax.Weight" type="System.Double, mscorlib">
    <value>0.375</value>
  </data>
  <data name="cell_Length.Text" xml:space="preserve">
    <value>Length</value>
  </data>
  <data name="lbl_Destination.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="xrLabel6.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrLabel11.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>134.0833, 120.0417</value>
  </data>
  <data name="cell_Width.Weight" type="System.Double, mscorlib">
    <value>0.74999999999999989</value>
  </data>
  <data name="xrTableCell7.Weight" type="System.Double, mscorlib">
    <value>0.22316327713828055</value>
  </data>
  <data name="lbl_Expenses.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 100.0001</value>
  </data>
  <data name="xrLabel3.Text" xml:space="preserve">
    <value>رقم الفاتورة</value>
  </data>
  <data name="lbl_ScaleWeightSerial.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>559.4585, 87.5</value>
  </data>
  <data name="lbl_DriverName.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_Destination.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>492.7502, 87.5</value>
  </data>
  <data name="lbl_Destination.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="Detail.HeightF" type="System.Single, mscorlib">
    <value>29.16667</value>
  </data>
  <data name="xrTable1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_notes.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>14.58332, 169.0417</value>
  </data>
  <data name="lblCompName.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 18pt</value>
  </data>
  <data name="lbl_AddTaxV.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>141.7083, 24.49998</value>
  </data>
  <data name="lbl_AddTaxV.Text" xml:space="preserve">
    <value>AddTaxV</value>
  </data>
  <data name="lbl_Drawer.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>14.58333, 144.5417</value>
  </data>
  <data name="xrLabel4.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>685.5415, 176.0417</value>
  </data>
  <data name="lbl_DeductTaxV.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lbl_Drawer.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>119.5, 24.49998</value>
  </data>
  <data name="lbl_date.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>112.5, 24.5</value>
  </data>
  <data name="xrTable3.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_AddTaxV.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lbl_Remains.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>134.083313, 24.49997</value>
  </data>
  <data name="lbl_ScaleWeightSerial.Text" xml:space="preserve">
    <value>..</value>
  </data>
  <data name="txt_custax.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 75.49998</value>
  </data>
  <data name="lbl_Paied.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="cell_Height.Text" xml:space="preserve">
    <value>Height</value>
  </data>
  <data name="txt_custax.Text" xml:space="preserve">
    <value> </value>
  </data>
  <data name="lbl_DeductTaxV.Text" xml:space="preserve">
    <value>DeductTaxV</value>
  </data>
  <data name="lbl_Expenses.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lbl_DiscountR.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>21.583313, 24.4999542</value>
  </data>
  <data name="lbl_Net.Text" xml:space="preserve">
    <value> </value>
  </data>
  <data name="cell_UOM.Text" xml:space="preserve">
    <value>وحدة القياس</value>
  </data>
  <data name="lblTotalWords.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel15.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lbl_VehicleNumber.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="xrTable3.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>486, 1.500099</value>
  </data>
  <data name="lbl_date.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrTable1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>786, 53.125</value>
  </data>
  <data name="xrLabel16.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableRow1.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Moccasin</value>
  </data>
  <data name="lbl_ScaleWeightSerial.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_Tax.Text" xml:space="preserve">
    <value> </value>
  </data>
  <data name="lbl_notes.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="lbl_Vendor.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>312.5, 95.54167</value>
  </data>
  <data name="lbl_Paymethod.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>119.5, 24.49998</value>
  </data>
  <data name="cell_SalesTax.Text" xml:space="preserve">
    <value>SalesTax</value>
  </data>
  <data name="cell_Disc.Weight" type="System.Double, mscorlib">
    <value>0.19942742267637759</value>
  </data>
  <data name="lbl_AddTaxV.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="xrTable1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 0</value>
  </data>
  <data name="lbl_store.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>112.5, 24.49998</value>
  </data>
  <data name="xrLabel19.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lbl_store.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel9.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>30.541626, 24.4999542</value>
  </data>
  <data name="xrTableCell5.Text" xml:space="preserve">
    <value>وحدة القياس</value>
  </data>
  <data name="xrLabel10.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>81.25018, 24.49998</value>
  </data>
  <data name="lbl_TotalPacks.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>356.469879, 151.833313</value>
  </data>
  <data name="xrLabel10.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lbl_Number.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrPageInfo1.Format" xml:space="preserve">
    <value>Page {0} of {1} </value>
  </data>
  <data name="xrLabel1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>86.4585, 24.49998</value>
  </data>
  <data name="xrLabel4.Text" xml:space="preserve">
    <value>ملاحظات</value>
  </data>
  <data name="lbl_Paied.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableCell3.Weight" type="System.Double, mscorlib">
    <value>0.40958945805789854</value>
  </data>
  <data name="cell_PiecesCount.Weight" type="System.Double, mscorlib">
    <value>0.75</value>
  </data>
  <data name="cell_DiscountRatio2.Text" xml:space="preserve">
    <value>DiscountRatio2</value>
  </data>
  <data name="xrLabel5.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="cell_Disc.Text" xml:space="preserve">
    <value>قيمة خصم</value>
  </data>
  <data name="lbl_store.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>312.5, 144.5417</value>
  </data>
  <data name="xrLabel8.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>425, 144.5417</value>
  </data>
  <data name="lbl_Total.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>134.083313, 24.49999</value>
  </data>
  <data name="xrLabel17.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="cell_Height.Weight" type="System.Double, mscorlib">
    <value>0.75</value>
  </data>
  <data name="xrTable4.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>388.4584, 25</value>
  </data>
  <data name="lbl_Net.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="txt_custax.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lbl_DriverName.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel19.Text" xml:space="preserve">
    <value> خصم ن</value>
  </data>
  <data name="lbl_Destination.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lblTotalWords.Text" xml:space="preserve">
    <value>..</value>
  </data>
  <data name="xrTable4.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lbl_Tax.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="$this.PaperKind" type="System.Drawing.Printing.PaperKind, System.Drawing">
    <value>A4</value>
  </data>
  <data name="xrLabel5.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>86.4585, 24.49998</value>
  </data>
  <data name="xrLabel10.Text" xml:space="preserve">
    <value>ض الجدول</value>
  </data>
  <data name="xrLabel20.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lbl_Net.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>134.083313, 24.5000229</value>
  </data>
  <data name="lbl_DiscountV.Text" xml:space="preserve">
    <value> </value>
  </data>
  <data name="cell_code.Weight" type="System.Double, mscorlib">
    <value>0.16650757534813337</value>
  </data>
  <data name="lbl_Drawer.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableRow2.Weight" type="System.Double, mscorlib">
    <value>0.54901959587545957</value>
  </data>
  <data name="lbl_User.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>14.58337, 95.54173</value>
  </data>
  <data name="xrLabel12.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>134.0833, 144.5417</value>
  </data>
  <data name="lbl_notes.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel11.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTable2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>786, 29.16667</value>
  </data>
  <data name="xrLabel24.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lbl_Serial.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>105.2085, 24.5</value>
  </data>
  <data name="lbl_totalPieces.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>392.999969, 126.374908</value>
  </data>
  <data name="lbl_Total.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lbl_date.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrLabel23.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel4.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lbl_Paymethod.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="cell_UOM.Weight" type="System.Double, mscorlib">
    <value>0.21469517336546917</value>
  </data>
  <data name="xrLabel4.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>86.4585, 24.49998</value>
  </data>
  <data name="xrTableCell2.Text" xml:space="preserve">
    <value>التشغيلة</value>
  </data>
  <data name="lbl_Vendor.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="$this.PageWidth" type="System.Int32, mscorlib">
    <value>827</value>
  </data>
  <data name="lbl_Remains.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrLabel7.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lbl_ScaleWeightSerial.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_Number.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>105.2085, 24.49998</value>
  </data>
  <data name="lblCompName.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopCenter</value>
  </data>
  <data name="xrLabel12.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="cell_DiscountRatio.Text" xml:space="preserve">
    <value>DiscountRatio</value>
  </data>
  <data name="xrLabel12.Text" xml:space="preserve">
    <value>الخزينة</value>
  </data>
  <data name="lblReportName.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lbl_Expenses.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>134.083313, 24.49997</value>
  </data>
  <data name="lbl_VehicleNumber.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrTable2.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lbl_AddTaxV.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>446.875, 51.50013</value>
  </data>
  <data name="xrLabel3.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrLabel2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>86.4585, 24.49999</value>
  </data>
  <data name="xrLabel23.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrLabel5.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel20.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>134.083313, 100.0001</value>
  </data>
  <data name="cell_SalesTaxRatio.Text" xml:space="preserve">
    <value>SalesTaxRatio</value>
  </data>
  <data name="xrLabel19.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>81.25018, 24.49997</value>
  </data>
  <data name="lbl_Tax.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 26.50013</value>
  </data>
  <data name="lblCompName.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>600.0001, 30</value>
  </data>
  <data name="xrLabel24.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="cell_DiscountRatio3.Text" xml:space="preserve">
    <value>DiscountRatio3</value>
  </data>
  <data name="lbl_date.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>312.5, 120.0417</value>
  </data>
  <data name="xrLabel2.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lbl_DiscountR.Text" xml:space="preserve">
    <value> </value>
  </data>
  <data name="lbl_Tax.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>134.083313, 24.49998</value>
  </data>
  <data name="xrLabel7.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>425, 120.0417</value>
  </data>
  <data name="xrLabel1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>685.5415, 95.54167</value>
  </data>
  <data name="xrLabel16.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>134.083313, 26.50013</value>
  </data>
  <data name="xrTableRow2.BackColor" type="System.Drawing.Color, System.Drawing">
    <value />
  </data>
  <data name="xrLabel15.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="cell_Price.Weight" type="System.Double, mscorlib">
    <value>0.21111649229326321</value>
  </data>
  <data name="lbl_Net.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="cell_ItemName.Text" xml:space="preserve">
    <value>اســـم الصنف</value>
  </data>
  <data name="BottomMargin.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="lbl_Paymethod.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>14.58333, 120.0417</value>
  </data>
  <data name="xrLabel17.Text" xml:space="preserve">
    <value>متبقي</value>
  </data>
  <data name="lbl_VehicleNumber.Text" xml:space="preserve">
    <value>..</value>
  </data>
  <data name="xrPageInfo1.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrLabel12.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTableRow2.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt, style=Bold</value>
  </data>
  <data name="lbl_DeductTaxV.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>141.7083, 24.49998</value>
  </data>
  <data name="cell_code2.Text" xml:space="preserve">
    <value>كود2</value>
  </data>
  <data name="lbl_ScaleWeightSerial.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>45.12448, 24.50003</value>
  </data>
  <data name="lbl_Total.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lbl_Paied.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>134.083313, 24.49997</value>
  </data>
  <data name="lbl_Serial.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrLabel17.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>134.083313, 173.5001</value>
  </data>
  <data name="lbl_Net.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 124.500084</value>
  </data>
  <data name="xrLabel2.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrTable4.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>353.3124, 26.50013</value>
  </data>
  <data name="lbl_User.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_totalPieces.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>101.7917, 25.45837</value>
  </data>
  <data name="xrLabel16.Text" xml:space="preserve">
    <value>ض ع</value>
  </data>
  <data name="lbl_Paied.Text" xml:space="preserve">
    <value> </value>
  </data>
  <data name="lblReportName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>312.5, 50</value>
  </data>
  <data name="lbl_User.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>119.5, 24.49998</value>
  </data>
  <data name="cell_ManufactureDate.Text" xml:space="preserve">
    <value>ManufactureDate</value>
  </data>
  <data name="xrLabel12.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>86.4585, 24.49998</value>
  </data>
  <data name="cell_DiscountRatio.Weight" type="System.Double, mscorlib">
    <value>0.375</value>
  </data>
  <data name="lbl_Paied.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 148.999969</value>
  </data>
  <data name="xrTableCell6.Text" xml:space="preserve">
    <value>الكمية</value>
  </data>
  <data name="xrLabel11.Text" xml:space="preserve">
    <value>نوع السداد</value>
  </data>
  <data name="cell_DiscountRatio2.Weight" type="System.Double, mscorlib">
    <value>0.74999999999999989</value>
  </data>
  <data name="xrLabel17.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrTable4.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cell_ManufactureDate.Weight" type="System.Double, mscorlib">
    <value>0.884583740234375</value>
  </data>
  <data name="cell_Serial.Weight" type="System.Double, mscorlib">
    <value>0.44229187011718751</value>
  </data>
  <data name="xrLabel8.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>86.4585, 24.49998</value>
  </data>
  <data name="lbl_DriverName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>380.2502, 87.5</value>
  </data>
  <data name="lbl_Vendor.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>373.0415, 24.49998</value>
  </data>
  <data name="xrLabel6.Text" xml:space="preserve">
    <value>اسم المستخدم</value>
  </data>
  <data name="xrTableCell3.Text" xml:space="preserve">
    <value>اســـم الصنف</value>
  </data>
  <data name="lblTotalWords.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 230.1667</value>
  </data>
  <data name="xrTableCell9.Weight" type="System.Double, mscorlib">
    <value>0.19942742267637759</value>
  </data>
  <data name="lbl_DeductTaxV.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_ScaleWeightSerial.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrTableCell2.Weight" type="System.Double, mscorlib">
    <value>0.25298175374970178</value>
  </data>
  <data name="xrLabel4.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrLabel11.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrTable2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 0</value>
  </data>
  <data name="xrTable3.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrSubreport2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>137.5, 150</value>
  </data>
  <data name="lbl_DriverName.Text" xml:space="preserve">
    <value>..</value>
  </data>
  <data name="lblCompName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>89.58334, 10.00001</value>
  </data>
  <data name="xrLabel9.Text" xml:space="preserve">
    <value>ق</value>
  </data>
  <data name="lbl_store.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lbl_VehicleNumber.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>430.2502, 87.5</value>
  </data>
  <data name="xrLabel9.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrLabel17.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>81.25018, 24.49997</value>
  </data>
  <data name="xrLabel1.Text" xml:space="preserve">
    <value>المورد</value>
  </data>
  <data name="xrLabel6.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>86.4585, 24.49998</value>
  </data>
  <data name="xrLabel20.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>81.25018, 24.49994</value>
  </data>
  <data name="xrTable2.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="xrLabel24.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>134.083313, 149</value>
  </data>
  <data name="$this.Margins" type="System.Drawing.Printing.Margins, System.Drawing">
    <value>19, 22, 220, 45</value>
  </data>
  <data name="Detail.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="xrTableCell8.Weight" type="System.Double, mscorlib">
    <value>0.16650757534813337</value>
  </data>
  <data name="lbl_User.Text" xml:space="preserve">
    <value>..</value>
  </data>
  <data name="lblTotalWords.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>697.5416, 24.49995</value>
  </data>
  <data name="xrLabel15.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>134.083313, 2.000109</value>
  </data>
  <data name="lbl_Drawer.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="cell_code.Text" xml:space="preserve">
    <value>كود</value>
  </data>
  <data name="lbl_TotalQty.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>291.208344, 126.374908</value>
  </data>
  <data name="xrLabel2.Text" xml:space="preserve">
    <value>تسلسل</value>
  </data>
  <data name="xrLabel23.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>81.25018, 24.5</value>
  </data>
  <data name="PageHeader.HeightF" type="System.Single, mscorlib">
    <value>53.125</value>
  </data>
  <data name="cell_SalesTaxRatio.Weight" type="System.Double, mscorlib">
    <value>0.884583740234375</value>
  </data>
  <data name="lbl_Paymethod.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="cell_Length.Weight" type="System.Double, mscorlib">
    <value>0.615416259765625</value>
  </data>
  <data name="cell_TotalQty.Weight" type="System.Double, mscorlib">
    <value>0.44229187011718751</value>
  </data>
  <data name="$this.PageHeight" type="System.Int32, mscorlib">
    <value>1169</value>
  </data>
  <data name="xrLabel15.Text" xml:space="preserve">
    <value>الاجمالي</value>
  </data>
  <data name="xrTable3.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>300, 25</value>
  </data>
  <data name="xrLabel11.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>86.4585, 24.49999</value>
  </data>
  <data name="xrLabel20.Text" xml:space="preserve">
    <value>مصاريف</value>
  </data>
  <data name="xrLabel5.Text" xml:space="preserve">
    <value>فقط وقدره</value>
  </data>
  <data name="lbl_Expenses.Text" xml:space="preserve">
    <value> </value>
  </data>
  <data name="xrTableRow1.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="lbl_DriverName.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>45.12448, 24.50003</value>
  </data>
  <data name="cell_Width.Text" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="xrLabel3.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>685.5415, 144.5417</value>
  </data>
  <data name="xrLabel20.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="txt_custax.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lbl_TotalQty.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>101.7917, 25.45837</value>
  </data>
  <data name="xrLabel7.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="picLogo.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>14.58333, 10.00001</value>
  </data>
  <data name="xrLabel23.Text" xml:space="preserve">
    <value>الصافي </value>
  </data>
  <data name="lbl_VehicleNumber.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>45.12448, 24.50003</value>
  </data>
  <data name="xrTableCell11.Weight" type="System.Double, mscorlib">
    <value>0.75</value>
  </data>
  <data name="picLogo.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>70, 70</value>
  </data>
  <data name="xrLabel5.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>698, 230.1667</value>
  </data>
  <data name="xrLabel19.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>134.083313, 51.00002</value>
  </data>
  <data name="xrLabel19.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="cell_Expire.Weight" type="System.Double, mscorlib">
    <value>0.22316327713828055</value>
  </data>
  <data name="xrTable1.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lblTotalWords.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lbl_DiscountV.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 51.00012</value>
  </data>
  <data name="cell_Qty.Text" xml:space="preserve">
    <value>الكمية</value>
  </data>
  <data name="lbl_Total.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 2.00010943</value>
  </data>
  <data name="lbl_TotalPacks.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>101.7917, 25.45837</value>
  </data>
  <data name="xrLabel3.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>86.4585, 24.49998</value>
  </data>
  <data name="lbl_Serial.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrTableCell10.Weight" type="System.Double, mscorlib">
    <value>0.17998550866396373</value>
  </data>
  <data name="xrLabel10.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>134.083313, 75.49998</value>
  </data>
  <data name="xrLabel10.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lbl_Destination.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>45.12448, 24.50003</value>
  </data>
  <data name="xrLabel24.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>81.25018, 24.49997</value>
  </data>
  <data name="ReportFooter.HeightF" type="System.Single, mscorlib">
    <value>303.6666</value>
  </data>
  <data name="xrTableCell5.Weight" type="System.Double, mscorlib">
    <value>0.21469517336546917</value>
  </data>
  <data name="lbl_Remains.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 173.500122</value>
  </data>
  <data name="lblReportName.Text" xml:space="preserve">
    <value>فاتورة مردود مشتريات</value>
  </data>
  <data name="cell_Total.Weight" type="System.Double, mscorlib">
    <value>0.18439886224178867</value>
  </data>
  <data name="lbl_Number.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>685.5415, 120.0417</value>
  </data>
  <data name="lbl_DeductTaxV.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>596.875, 51.50016</value>
  </data>
  <data name="lbl_Serial.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>580.333, 120.0417</value>
  </data>
  <data name="TopMargin.HeightF" type="System.Single, mscorlib">
    <value>220</value>
  </data>
  <data name="lbl_User.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="lbl_notes.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>670.9582, 50.9583</value>
  </data>
  <data name="xrTableCell6.Weight" type="System.Double, mscorlib">
    <value>0.20813447646512329</value>
  </data>
  <data name="xrSubreport2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>519.1196, 12.6666565</value>
  </data>
  <data name="xrPageInfo1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>109.375, 23</value>
  </data>
  <data name="cell_ItemName.Weight" type="System.Double, mscorlib">
    <value>0.40958945805789854</value>
  </data>
  <data name="xrLabel15.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>81.25018, 24.49998</value>
  </data>
  <data name="cell_Expire.Text" xml:space="preserve">
    <value>تاريخ الصلاحية</value>
  </data>
  <data name="BottomMargin.HeightF" type="System.Single, mscorlib">
    <value>45</value>
  </data>
  <data name="xrLabel24.Text" xml:space="preserve">
    <value>مدفوع</value>
  </data>
  <data name="xrTableCell4.Weight" type="System.Double, mscorlib">
    <value>0.21111649229326321</value>
  </data>
  <data name="xrLabel8.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lbl_DriverName.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="xrPageInfo1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>337.5, 12.5</value>
  </data>
  <data name="xrTableRow4.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="xrLabel9.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>81.958374, 51.0000229</value>
  </data>
  <data name="xrTableCell10.Text" xml:space="preserve">
    <value>كود2</value>
  </data>
  <data name="lbl_Expenses.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrTableCell4.Text" xml:space="preserve">
    <value>السعر</value>
  </data>
  <data name="lbl_Vendor.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel3.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lbl_DiscountV.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrTableRow3.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="cell_code2.Weight" type="System.Double, mscorlib">
    <value>0.17998550866396373</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.Language" type="System.Globalization.CultureInfo, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>ar-EG</value>
  </metadata>
</root>