﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{37902FB4-38C2-4CFD-8266-8424B8234E92}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Reports</RootNamespace>
    <AssemblyName>Reports</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Accessibility" />
    <Reference Include="DevExpress.Charts.v15.1.Core, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Data.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Office.v15.1.Core, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.PivotGrid.v15.1.Core, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Printing.v15.1.Core, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.RichEdit.v15.1.Core, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Sparkline.v15.1.Core, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Utils.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Utils.v15.1.UI, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraBars.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraCharts.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraCharts.v15.1.UI, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraCharts.v15.1.Wizard, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraEditors.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraGauges.v15.1.Core, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraLayout.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraNavBar.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraPivotGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraPrinting.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraReports.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraReports.v15.1.Extensions, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraRichEdit.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="PresentationCore" />
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.Entity" />
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.DirectoryServices" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Runtime.Remoting" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Runtime.Serialization.Formatters.Soap" />
    <Reference Include="System.Security" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Windows.Input.Manipulations" />
    <Reference Include="System.Xaml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="UIAutomationClient" />
    <Reference Include="UIAutomationProvider" />
    <Reference Include="UIAutomationTypes" />
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ACC\rpt_CustomizedIncomeReport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ACC\rpt_CustomizedIncomeReport.Designer.cs">
      <DependentUpon>rpt_CustomizedIncomeReport.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\frm_Acc_IncomeTwithArchive.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ACC\frm_Acc_IncomeTwithArchive.Designer.cs">
      <DependentUpon>frm_Acc_IncomeTwithArchive.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\frm_ACC_CustomizedIncomeReport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ACC\frm_ACC_CustomizedIncomeReport.Designer.cs">
      <DependentUpon>frm_ACC_CustomizedIncomeReport.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\rpt_ACC_CCTrialBalance.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ACC\rpt_ACC_CCTrialBalance.Designer.cs">
      <DependentUpon>rpt_ACC_CCTrialBalance.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\frm_Acc_IncomeMain.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ACC\frm_Acc_IncomeMain.Designer.cs">
      <DependentUpon>frm_Acc_IncomeMain.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\frm_Acc_IncomeSub.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ACC\frm_Acc_IncomeSub.Designer.cs">
      <DependentUpon>frm_Acc_IncomeSub.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\frm_Acc_CostCenterOper.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ACC\frm_Acc_CostCenterOper.Designer.cs">
      <DependentUpon>frm_Acc_CostCenterOper.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\frm_Acc_Account_CostCentersPivot.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ACC\frm_Acc_Account_CostCentersPivot.Designer.cs">
      <DependentUpon>frm_Acc_Account_CostCentersPivot.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\frm_Acc_Income.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ACC\frm_Acc_Income.Designer.cs">
      <DependentUpon>frm_Acc_Income.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\frm_Acc_CashFlow.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ACC\frm_Acc_CashFlow.Designer.cs">
      <DependentUpon>frm_Acc_CashFlow.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\rpt_ACC_Statement.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ACC\rpt_ACC_Statement.Designer.cs">
      <DependentUpon>rpt_ACC_Statement.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\rpt_ACC_TrialBalance.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ACC\rpt_ACC_TrialBalance.Designer.cs">
      <DependentUpon>rpt_ACC_TrialBalance.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\rpt_Acc_T_SubRpt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ACC\rpt_Acc_T_SubRpt.designer.cs">
      <DependentUpon>rpt_Acc_T_SubRpt.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_ACC_Journal.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_ACC_Journal.Designer.cs">
      <DependentUpon>rpt_ACC_Journal.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_ACC_Statment.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_ACC_Statment.Designer.cs">
      <DependentUpon>rpt_ACC_Statment.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_CashNote.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_CashNote.Designer.cs">
      <DependentUpon>rpt_CashNote.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_CashTransfer.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_CashTransfer.Designer.cs">
      <DependentUpon>rpt_CashTransfer.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_EmployeeDetails.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_EmployeeDetails.Designer.cs">
      <DependentUpon>rpt_EmployeeDetails.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_EmployeeStroy.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_EmployeeStroy.designer.cs">
      <DependentUpon>rpt_EmployeeStroy.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_HR_Penalty.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_HR_Penalty.Designer.cs">
      <DependentUpon>rpt_HR_Penalty.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_HR_Reward.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_HR_Reward.Designer.cs">
      <DependentUpon>rpt_HR_Reward.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_IC_BOM.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_IC_BOM.Designer.cs">
      <DependentUpon>rpt_IC_BOM.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_IC_Damaged.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_IC_Damaged.Designer.cs">
      <DependentUpon>rpt_IC_Damaged.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_IC_InTrns.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_IC_InTrns.Designer.cs">
      <DependentUpon>rpt_IC_InTrns.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_IC_OutTrns.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_IC_OutTrns.Designer.cs">
      <DependentUpon>rpt_IC_OutTrns.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_IC_StoreMove.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_IC_StoreMove.Designer.cs">
      <DependentUpon>rpt_IC_StoreMove.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_JO_JobOrder.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_JO_JobOrder.Designer.cs">
      <DependentUpon>rpt_JO_JobOrder.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_JO_JobOrderListCustomer.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_JO_JobOrderListCustomer.Designer.cs">
      <DependentUpon>rpt_JO_JobOrderListCustomer.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_JO_JobOrderListDept.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_JO_JobOrderListDept.Designer.cs">
      <DependentUpon>rpt_JO_JobOrderListDept.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_Manf_QC.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_Manf_QC.Designer.cs">
      <DependentUpon>rpt_Manf_QC.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_manufacture.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_manufacture.Designer.cs">
      <DependentUpon>rpt_manufacture.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_manufacture_actualRows.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_manufacture_actualRows.Designer.cs">
      <DependentUpon>rpt_manufacture_actualRows.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_multiple_weights_dup.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_multiple_weights_dup.Designer.cs">
      <DependentUpon>rpt_multiple_weights_dup.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_multiple_weights.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_multiple_weights.Designer.cs">
      <DependentUpon>rpt_multiple_weights.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_PayNotes.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_PayNotes.Designer.cs">
      <DependentUpon>rpt_PayNotes.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_PriceLevel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_PriceLevel.Designer.cs">
      <DependentUpon>rpt_PriceLevel.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_Printed_Receipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_Printed_Receipt.designer.cs">
      <DependentUpon>rpt_Printed_Receipt.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_PR_Invoice.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_PR_Invoice.Designer.cs">
      <DependentUpon>rpt_PR_Invoice.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_PR_Invoice_Store.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_PR_Invoice_Store.Designer.cs">
      <DependentUpon>rpt_PR_Invoice_Store.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_PR_Invoice_Store_Sub.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_PR_Invoice_Store_Sub.Designer.cs">
      <DependentUpon>rpt_PR_Invoice_Store_Sub.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_PR_PriceLevel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_PR_PriceLevel.Designer.cs">
      <DependentUpon>rpt_PR_PriceLevel.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_PR_PurchaseOrder.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_PR_PurchaseOrder.Designer.cs">
      <DependentUpon>rpt_PR_PurchaseOrder.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_PR_Quote.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_PR_Quote.Designer.cs">
      <DependentUpon>rpt_PR_Quote.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_PR_Request.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_PR_Request.Designer.cs">
      <DependentUpon>rpt_PR_Request.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_PR_ReturnInvoice.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_PR_ReturnInvoice.Designer.cs">
      <DependentUpon>rpt_PR_ReturnInvoice.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_RecieveNotes.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_RecieveNotes.Designer.cs">
      <DependentUpon>rpt_RecieveNotes.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_RevExp.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_RevExp.Designer.cs">
      <DependentUpon>rpt_RevExp.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_SL_Invoice.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_SL_Invoice.Designer.cs">
      <DependentUpon>rpt_SL_Invoice.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_SL_Invoice_Store.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_SL_Invoice_Store.Designer.cs">
      <DependentUpon>rpt_SL_Invoice_Store.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_SL_Invoice_Store_Sub.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_SL_Invoice_Store_Sub.Designer.cs">
      <DependentUpon>rpt_SL_Invoice_Store_Sub.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_SL_Quote.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_SL_Quote.Designer.cs">
      <DependentUpon>rpt_SL_Quote.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_SL_ReturnInvoice.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_SL_ReturnInvoice.Designer.cs">
      <DependentUpon>rpt_SL_ReturnInvoice.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_SL_SalesOrder.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_SL_SalesOrder.Designer.cs">
      <DependentUpon>rpt_SL_SalesOrder.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_SL_SalesOrder_Manufacturing.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_SL_SalesOrder_Manufacturing.Designer.cs">
      <DependentUpon>rpt_SL_SalesOrder_Manufacturing.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_Weight.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_Weight.Designer.cs">
      <DependentUpon>rpt_Weight.cs</DependentUpon>
    </Compile>
    <Compile Include="ExtensionsHelper.cs" />
    <Compile Include="HR\rpt_HR_PayRoll.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="HR\rpt_HR_PayRoll.Designer.cs">
      <DependentUpon>rpt_HR_PayRoll.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsQtyImages.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsQtyImages.Designer.cs">
      <DependentUpon>frm_IC_ItemsQtyImages.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsQtywithImages.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsQtywithImages.Designer.cs">
      <DependentUpon>frm_IC_ItemsQtywithImages.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsQtyHWithoutCost.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsQtyHWithoutCost.Designer.cs">
      <DependentUpon>frm_IC_ItemsQtyHWithoutCost.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\frm_IC_Item_In_Out_Balance.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IC\frm_IC_Item_In_Out_Balance.Designer.cs">
      <DependentUpon>frm_IC_Item_In_Out_Balance.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsQtyWithPricelevel.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsQtyWithPricelevel.Designer.cs">
      <DependentUpon>frm_IC_ItemsQtyWithPricelevel.cs</DependentUpon>
    </Compile>
    <Compile Include="PR\frm_IC_StorePurchase.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PR\frm_IC_StorePurchase.Designer.cs">
      <DependentUpon>frm_IC_StorePurchase.cs</DependentUpon>
    </Compile>
    <Compile Include="PR\frm_ReceivingAndAchievement.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PR\frm_ReceivingAndAchievement.Designer.cs">
      <DependentUpon>frm_ReceivingAndAchievement.cs</DependentUpon>
    </Compile>
    <Compile Include="rpt_Acc_CostCenter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rpt_Acc_CostCenter.Designer.cs">
      <DependentUpon>rpt_Acc_CostCenter.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_CustomsCertifecationWarning.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_CustomsCertifecationWarning.Designer.cs">
      <DependentUpon>frm_CustomsCertifecationWarning.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_SL_InvoiceDetails.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_SL_InvoiceDetails.Designer.cs">
      <DependentUpon>frm_SL_InvoiceDetails.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_Sl_JobOrderStatus.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_Sl_JobOrderStatus.Designer.cs">
      <DependentUpon>frm_Sl_JobOrderStatus.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_SL_CustomerGroupItemsNet.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_SL_CustomerGroupItemsNet.Designer.cs">
      <DependentUpon>frm_SL_CustomerGroupItemsNet.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_IC_StoreSales.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_IC_StoreSales.Designer.cs">
      <DependentUpon>frm_IC_StoreSales.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\rpt_IC_InOutItems.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IC\rpt_IC_InOutItems.Designer.cs">
      <DependentUpon>rpt_IC_InOutItems.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\rpt_IC_Item_Store_PriceLevel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="IC\rpt_IC_Item_Store_PriceLevel.Designer.cs">
      <DependentUpon>rpt_IC_Item_Store_PriceLevel.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\rpt_IC_ItemQty.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="IC\rpt_IC_ItemQty.Designer.cs">
      <DependentUpon>rpt_IC_ItemQty.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_SL_CustomerTotal_Invoices.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_SL_CustomerTotal_Invoices.Designer.cs">
      <DependentUpon>frm_SL_CustomerTotal_Invoices.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_SL_CustomerInvoicesHeaders.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_SL_CustomerInvoicesHeaders.Designer.cs">
      <DependentUpon>frm_SL_CustomerInvoicesHeaders.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_SL_Invoices_Due.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_SL_Invoices_Due.Designer.cs">
      <DependentUpon>frm_SL_Invoices_Due.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\rpt_SL_CustomerSale.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\rpt_SL_CustomerSale.Designer.cs">
      <DependentUpon>rpt_SL_CustomerSale.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_SL_Customer_Visits.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_SL_Customer_Visits.Designer.cs">
      <DependentUpon>frm_SL_Customer_Visits.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\frm_Acc_AccountsBalances.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ACC\frm_Acc_AccountsBalances.Designer.cs">
      <DependentUpon>frm_Acc_AccountsBalances.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\frm_Acc_AccountsBalancesMonths.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ACC\frm_Acc_AccountsBalancesMonths.Designer.cs">
      <DependentUpon>frm_Acc_AccountsBalancesMonths.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\frm_ACC_SubLedger.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ACC\frm_ACC_SubLedger.Designer.cs">
      <DependentUpon>frm_ACC_SubLedger.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\frm_Acc_AccountsBalancesWithNotes.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ACC\frm_Acc_AccountsBalancesWithNotes.Designer.cs">
      <DependentUpon>frm_Acc_AccountsBalancesWithNotes.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\frm_Acc_Account_CostCenters.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ACC\frm_Acc_Account_CostCenters.Designer.cs">
      <DependentUpon>frm_Acc_Account_CostCenters.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\frm_Acc_BalanceT.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ACC\frm_Acc_BalanceT.Designer.cs">
      <DependentUpon>frm_Acc_BalanceT.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\frm_Acc_CostCenterTotalBalances.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ACC\frm_Acc_CostCenterTotalBalances.Designer.cs">
      <DependentUpon>frm_Acc_CostCenterTotalBalances.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\frm_Acc_CostCenter_AccDetails.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ACC\frm_Acc_CostCenter_AccDetails.Designer.cs">
      <DependentUpon>frm_Acc_CostCenter_AccDetails.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\frm_Acc_DailyPaymentsAndDailyIncome.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ACC\frm_Acc_DailyPaymentsAndDailyIncome.Designer.cs">
      <DependentUpon>frm_Acc_DailyPaymentsAndDailyIncome.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\frm_Acc_YearlyBudget.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ACC\frm_Acc_YearlyBudget.Designer.cs">
      <DependentUpon>frm_Acc_YearlyBudget.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\frm_Acc_DailyIncome.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ACC\frm_Acc_DailyIncome.Designer.cs">
      <DependentUpon>frm_Acc_DailyIncome.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\frm_Acc_DailyPayments.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ACC\frm_Acc_DailyPayments.Designer.cs">
      <DependentUpon>frm_Acc_DailyPayments.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\frm_Acc_IncomeT.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ACC\frm_Acc_IncomeT.Designer.cs">
      <DependentUpon>frm_Acc_IncomeT.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\rpt_Acc_IncomeT.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ACC\rpt_Acc_IncomeT.Designer.cs">
      <DependentUpon>rpt_Acc_IncomeT.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\rpt_Acc_BalanceT.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ACC\rpt_Acc_BalanceT.Designer.cs">
      <DependentUpon>rpt_Acc_BalanceT.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\rpt_Acc_CustomAccListBalances.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ACC\rpt_Acc_CustomAccListBalances.Designer.cs">
      <DependentUpon>rpt_Acc_CustomAccListBalances.cs</DependentUpon>
    </Compile>
    <Compile Include="ACC\rpt_ACC_DrawerDaySummary.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ACC\rpt_ACC_DrawerDaySummary.Designer.cs">
      <DependentUpon>rpt_ACC_DrawerDaySummary.cs</DependentUpon>
    </Compile>
    <Compile Include="HR\frm_HR_Emloyee_Nationality.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="HR\frm_HR_Emloyee_Nationality.Designer.cs">
      <DependentUpon>frm_HR_Emloyee_Nationality.cs</DependentUpon>
    </Compile>
    <Compile Include="HR\frm_HR_Employee_Report.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="HR\frm_HR_Employee_Report.Designer.cs">
      <DependentUpon>frm_HR_Employee_Report.cs</DependentUpon>
    </Compile>
    <Compile Include="HR\frm_HR_Att.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="HR\frm_HR_Att.Designer.cs">
      <DependentUpon>frm_HR_Att.cs</DependentUpon>
    </Compile>
    <Compile Include="HR\frm_HR_Vacations.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="HR\frm_HR_Vacations.Designer.cs">
      <DependentUpon>frm_HR_Vacations.cs</DependentUpon>
    </Compile>
    <Compile Include="HR\frm_SalesRep_DaySummary.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="HR\frm_SalesRep_DaySummary.Designer.cs">
      <DependentUpon>frm_SalesRep_DaySummary.cs</DependentUpon>
    </Compile>
    <Compile Include="HR\rpt_SalesRep_DaySummary.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="HR\rpt_SalesRep_DaySummary.Designer.cs">
      <DependentUpon>rpt_SalesRep_DaySummary.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsMinLevel.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsMinLevel.Designer.cs">
      <DependentUpon>frm_IC_ItemsMinLevel.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\frm_IC_SoldItemsAndReturnCost.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IC\frm_IC_SoldItemsAndReturnCost.Designer.cs">
      <DependentUpon>frm_IC_SoldItemsAndReturnCost.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\Resources1.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="PR\frm_PR_InvoicesDiscountTaxHeaders.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PR\frm_PR_InvoicesDiscountTaxHeaders.Designer.cs">
      <DependentUpon>frm_PR_InvoicesDiscountTaxHeaders.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportsUtils.cs" />
    <Compile Include="frm_ReportViewer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frm_ReportViewer.Designer.cs">
      <DependentUpon>frm_ReportViewer.cs</DependentUpon>
    </Compile>
    <Compile Include="HR\frm_HR_AllExpectedPays.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="HR\frm_HR_AllExpectedPays.Designer.cs">
      <DependentUpon>frm_HR_AllExpectedPays.cs</DependentUpon>
    </Compile>
    <Compile Include="HR\frm_HR_AllPays.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="HR\frm_HR_AllPays.Designer.cs">
      <DependentUpon>frm_HR_AllPays.cs</DependentUpon>
    </Compile>
    <Compile Include="HR\frm_HR_Insurance.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="HR\frm_HR_Insurance.Designer.cs">
      <DependentUpon>frm_HR_Insurance.cs</DependentUpon>
    </Compile>
    <Compile Include="HR\frm_HR_ManfCommision.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="HR\frm_HR_ManfCommision.Designer.cs">
      <DependentUpon>frm_HR_ManfCommision.cs</DependentUpon>
    </Compile>
    <Compile Include="HR\frm_HR_OutTrnsItemsCommision.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="HR\frm_HR_OutTrnsItemsCommision.Designer.cs">
      <DependentUpon>frm_HR_OutTrnsItemsCommision.cs</DependentUpon>
    </Compile>
    <Compile Include="HR\frm_HR_SalesEmpInvoiceCommision.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="HR\frm_HR_SalesEmpInvoiceCommision.Designer.cs">
      <DependentUpon>frm_HR_SalesEmpInvoiceCommision.cs</DependentUpon>
    </Compile>
    <Compile Include="HR\frm_HR_SalesInvItemsCommision.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="HR\frm_HR_SalesInvItemsCommision.Designer.cs">
      <DependentUpon>frm_HR_SalesInvItemsCommision.cs</DependentUpon>
    </Compile>
    <Compile Include="HR\frm_HR_VacationBal.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="HR\frm_HR_VacationBal.Designer.cs">
      <DependentUpon>frm_HR_VacationBal.cs</DependentUpon>
    </Compile>
    <Compile Include="HR\rpt_HR_EmpAdvAtt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="HR\rpt_HR_EmpAdvAtt.Designer.cs">
      <DependentUpon>rpt_HR_EmpAdvAtt.cs</DependentUpon>
    </Compile>
    <Compile Include="HR\rpt_HR_Pay.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="HR\rpt_HR_Pay.Designer.cs">
      <DependentUpon>rpt_HR_Pay.cs</DependentUpon>
    </Compile>
    <Compile Include="HR\rpt_HR_SalesEmpTargetCommission.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="HR\rpt_HR_SalesEmpTargetCommission.Designer.cs">
      <DependentUpon>rpt_HR_SalesEmpTargetCommission.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\frm_IC_ItemOpenInOutClose.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IC\frm_IC_ItemOpenInOutClose.Designer.cs">
      <DependentUpon>frm_IC_ItemOpenInOutClose.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\frm_IC_ItemOpenInOutCloseQty.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IC\frm_IC_ItemOpenInOutCloseQty.Designer.cs">
      <DependentUpon>frm_IC_ItemOpenInOutCloseQty.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsEvaluation.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsEvaluation.Designer.cs">
      <DependentUpon>frm_IC_ItemsEvaluation.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsExpired.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsExpired.Designer.cs">
      <DependentUpon>frm_IC_ItemsExpired.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsMaxSell.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsMaxSell.Designer.cs">
      <DependentUpon>frm_IC_ItemsMaxSell.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsMinSell.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsMinSell.Designer.cs">
      <DependentUpon>frm_IC_ItemsMinSell.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsNotSold.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsNotSold.Designer.cs">
      <DependentUpon>frm_IC_ItemsNotSold.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsQty.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsQty.Designer.cs">
      <DependentUpon>frm_IC_ItemsQty.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsQtyDetails.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsQtyDetails.Designer.cs">
      <DependentUpon>frm_IC_ItemsQtyDetails.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsQtyH.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsQtyH.Designer.cs">
      <DependentUpon>frm_IC_ItemsQtyH.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsQtyWithPrices.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsQtyWithPrices.Designer.cs">
      <DependentUpon>frm_IC_ItemsQtyWithPrices.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsQtyWithSalesPrice.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsQtyWithSalesPrice.Designer.cs">
      <DependentUpon>frm_IC_ItemsQtyWithSalesPrice.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsReorder.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsReorder.Designer.cs">
      <DependentUpon>frm_IC_ItemsReorder.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsTurnOver.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IC\frm_IC_ItemsTurnOver.Designer.cs">
      <DependentUpon>frm_IC_ItemsTurnOver.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\frm_IC_ItemTransactions.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IC\frm_IC_ItemTransactions.Designer.cs">
      <DependentUpon>frm_IC_ItemTransactions.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\frm_IC_SoldItemsCost.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IC\frm_IC_SoldItemsCost.Designer.cs">
      <DependentUpon>frm_IC_SoldItemsCost.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\rpt_IC_ItemTransactionsDetails.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="IC\rpt_IC_ItemTransactionsDetails.Designer.cs">
      <DependentUpon>rpt_IC_ItemTransactionsDetails.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\rpt_IC_ItemTransactionsDetails_SubRpt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="IC\rpt_IC_ItemTransactionsDetails_SubRpt.Designer.cs">
      <DependentUpon>rpt_IC_ItemTransactionsDetails_SubRpt.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="PR\frm_PR_InvoicesHeaders.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PR\frm_PR_InvoicesHeaders.Designer.cs">
      <DependentUpon>frm_PR_InvoicesHeaders.cs</DependentUpon>
    </Compile>
    <Compile Include="PR\frm_PR_ItemsPurchases.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PR\frm_PR_ItemsPurchases.Designer.cs">
      <DependentUpon>frm_PR_ItemsPurchases.cs</DependentUpon>
    </Compile>
    <Compile Include="PR\frm_PR_ItemsReturns.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PR\frm_PR_ItemsReturns.Designer.cs">
      <DependentUpon>frm_PR_ItemsReturns.cs</DependentUpon>
    </Compile>
    <Compile Include="PR\frm_PR_ReturnHeaders.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PR\frm_PR_ReturnHeaders.Designer.cs">
      <DependentUpon>frm_PR_ReturnHeaders.cs</DependentUpon>
    </Compile>
    <Compile Include="PR\frm_PR_VendorItemsPurchases.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PR\frm_PR_VendorItemsPurchases.Designer.cs">
      <DependentUpon>frm_PR_VendorItemsPurchases.cs</DependentUpon>
    </Compile>
    <Compile Include="PR\frm_PR_VendorItemsPurchases_InTrns.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PR\frm_PR_VendorItemsPurchases_InTrns.Designer.cs">
      <DependentUpon>frm_PR_VendorItemsPurchases_InTrns.cs</DependentUpon>
    </Compile>
    <Compile Include="rpt_barcodeLabels.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rpt_barcodeLabels.Designer.cs">
      <DependentUpon>rpt_barcodeLabels.cs</DependentUpon>
    </Compile>
    <Compile Include="rpt_ItemPriceChangings.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rpt_ItemPriceChangings.Designer.cs">
      <DependentUpon>rpt_ItemPriceChangings.cs</DependentUpon>
    </Compile>
    <Compile Include="rpt_Sub_Tree.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rpt_Sub_Tree.Designer.cs">
      <DependentUpon>rpt_Sub_Tree.cs</DependentUpon>
    </Compile>
    <Compile Include="rpt_Sub_AccTree.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rpt_Sub_AccTree.Designer.cs">
      <DependentUpon>rpt_Sub_AccTree.cs</DependentUpon>
    </Compile>
    <Compile Include="rpt_Template2.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rpt_Template2.Designer.cs">
      <DependentUpon>rpt_Template2.cs</DependentUpon>
    </Compile>
    <Compile Include="rpt_Template.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="rpt_Template.Designer.cs">
      <DependentUpon>rpt_Template.cs</DependentUpon>
    </Compile>
    <Compile Include="ReportsRTL.cs" />
    <Compile Include="SL\frm_CapitalInDeferredInvoices.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_CapitalInDeferredInvoices.Designer.cs">
      <DependentUpon>frm_CapitalInDeferredInvoices.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_SL_averagesellingpriceoftheitems.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_SL_averagesellingpriceoftheitems.Designer.cs">
      <DependentUpon>frm_SL_averagesellingpriceoftheitems.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_MrAllSales.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_MrAllSales.Designer.cs">
      <DependentUpon>frm_MrAllSales.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_SL_CustomerItemsSales.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_SL_CustomerItemsSales.Designer.cs">
      <DependentUpon>frm_SL_CustomerItemsSales.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_SL_CustomerItemsSales_OutTrns.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_SL_CustomerItemsSales_OutTrns.Designer.cs">
      <DependentUpon>frm_SL_CustomerItemsSales_OutTrns.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_SL_DelegatesSales.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_SL_DelegatesSales.Designer.cs">
      <DependentUpon>frm_SL_DelegatesSales.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_SL_DeliveryOfficialsSales.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_SL_DeliveryOfficialsSales.Designer.cs">
      <DependentUpon>frm_SL_DeliveryOfficialsSales.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_SL_InvoicesHeaders.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_SL_InvoicesHeaders.Designer.cs">
      <DependentUpon>frm_SL_InvoicesHeaders.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_SL_ItemsReturn.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_SL_ItemsReturn.Designer.cs">
      <DependentUpon>frm_SL_ItemsReturn.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_SL_ItemsSales.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_SL_ItemsSales.Designer.cs">
      <DependentUpon>frm_SL_ItemsSales.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_SL_Profit_Loss.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_SL_Profit_Loss.Designer.cs">
      <DependentUpon>frm_SL_Profit_Loss.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_SL_ReturnHeaders.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_SL_ReturnHeaders.Designer.cs">
      <DependentUpon>frm_SL_ReturnHeaders.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_SL_Car_Weights.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_SL_Car_Weights.Designer.cs">
      <DependentUpon>frm_SL_Car_Weights.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_SL_Warranty.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_SL_Warranty.Designer.cs">
      <DependentUpon>frm_SL_Warranty.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\rpt_SL_CustomerTrans.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="SL\rpt_SL_CustomerTrans.Designer.cs">
      <DependentUpon>rpt_SL_CustomerTrans.cs</DependentUpon>
    </Compile>
    <Compile Include="Documents\rpt_IC_Multiple_Weights_Sub.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Documents\rpt_IC_Multiple_Weights_Sub.Designer.cs">
      <DependentUpon>rpt_IC_Multiple_Weights_Sub.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\rpt_SL_CustomerTrans_Sub.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="SL\rpt_SL_CustomerTrans_Sub.Designer.cs">
      <DependentUpon>rpt_SL_CustomerTrans_Sub.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\rpt_SL_ItemTrade.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="SL\rpt_SL_ItemTrade.Designer.cs">
      <DependentUpon>rpt_SL_ItemTrade.cs</DependentUpon>
    </Compile>
    <Compile Include="Special\frm_Acc_AccountArchiveDetails.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Special\frm_Acc_AccountArchiveDetails.Designer.cs">
      <DependentUpon>frm_Acc_AccountArchiveDetails.cs</DependentUpon>
    </Compile>
    <Compile Include="Special\frm_Acc_AccountDetails.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Special\frm_Acc_AccountDetails.Designer.cs">
      <DependentUpon>frm_Acc_AccountDetails.cs</DependentUpon>
    </Compile>
    <Compile Include="Special\frm_Acc_CustomAccListDetails.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Special\frm_Acc_CustomAccListDetails.Designer.cs">
      <DependentUpon>frm_Acc_CustomAccListDetails.cs</DependentUpon>
    </Compile>
    <Compile Include="IC\frm_IC_ItemQtyBatch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IC\frm_IC_ItemQtyBatch.Designer.cs">
      <DependentUpon>frm_IC_ItemQtyBatch.cs</DependentUpon>
    </Compile>
    <Compile Include="Special\frm_ImExp_Container_Report.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Special\frm_ImExp_Container_Report.Designer.cs">
      <DependentUpon>frm_ImExp_Container_Report.cs</DependentUpon>
    </Compile>
    <Compile Include="Special\frm_ImExp_PreInvoice_Report .cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Special\frm_ImExp_PreInvoice_Report .Designer.cs">
      <DependentUpon>frm_ImExp_PreInvoice_Report .cs</DependentUpon>
    </Compile>
    <Compile Include="Special\frm_ImExp_ExportConfirmat_Report.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Special\frm_ImExp_ExportConfirmat_Report.Designer.cs">
      <DependentUpon>frm_ImExp_ExportConfirmat_Report.cs</DependentUpon>
    </Compile>
    <Compile Include="Special\frm_ImExp_Commission_Report.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Special\frm_ImExp_Commission_Report.Designer.cs">
      <DependentUpon>frm_ImExp_Commission_Report.cs</DependentUpon>
    </Compile>
    <Compile Include="Special\frm_ImExp_FinesReport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Special\frm_ImExp_FinesReport.Designer.cs">
      <DependentUpon>frm_ImExp_FinesReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Special\frm_SL_Employee_Item_Target.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Special\frm_SL_Employee_Item_Target.Designer.cs">
      <DependentUpon>frm_SL_Employee_Item_Target.cs</DependentUpon>
    </Compile>
    <Compile Include="Special\frm_ItemsPr_and_Sl.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Special\frm_ItemsPr_and_Sl.Designer.cs">
      <DependentUpon>frm_ItemsPr_and_Sl.cs</DependentUpon>
    </Compile>
    <Compile Include="Special\frm_ManfItems.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Special\frm_ManfItems.Designer.cs">
      <DependentUpon>frm_ManfItems.cs</DependentUpon>
    </Compile>
    <Compile Include="Special\frm_OrderAndAchievement.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Special\frm_OrderAndAchievement.Designer.cs">
      <DependentUpon>frm_OrderAndAchievement.cs</DependentUpon>
    </Compile>
    <Compile Include="Special\frm_PR_ContractorExtract.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Special\frm_PR_ContractorExtract.Designer.cs">
      <DependentUpon>frm_PR_ContractorExtract.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_SL_CustomerItemsSalesReturns.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_SL_CustomerItemsSalesReturns.Designer.cs">
      <DependentUpon>frm_SL_CustomerItemsSalesReturns.cs</DependentUpon>
    </Compile>
    <Compile Include="PR\frm_SL_ItemsNetPurchaseDetails.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PR\frm_SL_ItemsNetPurchaseDetails.Designer.cs">
      <DependentUpon>frm_SL_ItemsNetPurchaseDetails.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_SL_ItemsNetSalesDetails.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_SL_ItemsNetSalesDetails.Designer.cs">
      <DependentUpon>frm_SL_ItemsNetSalesDetails.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_SL_ItemsSalesDetails.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_SL_ItemsSalesDetails.Designer.cs">
      <DependentUpon>frm_SL_ItemsSalesDetails.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_SL_ItemsSalesTotals.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_SL_ItemsSalesTotals.Designer.cs">
      <DependentUpon>frm_SL_ItemsSalesTotals.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_SL_JobOrderInv.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_SL_JobOrderInv.Designer.cs">
      <DependentUpon>frm_SL_JobOrderInv.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_SL_SalesOrderItems.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_SL_SalesOrderItems.Designer.cs">
      <DependentUpon>frm_SL_SalesOrderItems.cs</DependentUpon>
    </Compile>
    <Compile Include="SL\frm_SL_SalesOrderItemsAndBalance.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SL\frm_SL_SalesOrderItemsAndBalance.Designer.cs">
      <DependentUpon>frm_SL_SalesOrderItemsAndBalance.cs</DependentUpon>
    </Compile>
    <Compile Include="Special\frm_SL_DelegatesSales_ItemCategory.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Special\frm_SL_DelegatesSales_ItemCategory.Designer.cs">
      <DependentUpon>frm_SL_DelegatesSales_ItemCategory.cs</DependentUpon>
    </Compile>
    <Compile Include="Special\rpt_UserDaySummary.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Special\rpt_UserDaySummary.Designer.cs">
      <DependentUpon>rpt_UserDaySummary.cs</DependentUpon>
    </Compile>
    <Compile Include="uc_Currency.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="uc_Currency.Designer.cs">
      <DependentUpon>uc_Currency.cs</DependentUpon>
    </Compile>
    <Compile Include="uc_CurrencyValue.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="uc_CurrencyValue.Designer.cs">
      <DependentUpon>uc_CurrencyValue.cs</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="ACC\rpt_CustomizedIncomeReport.ar-EG.resx">
      <DependentUpon>rpt_CustomizedIncomeReport.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\rpt_CustomizedIncomeReport.resx">
      <DependentUpon>rpt_CustomizedIncomeReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_IncomeTwithArchive.ar-EG.resx">
      <DependentUpon>frm_Acc_IncomeTwithArchive.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_IncomeTwithArchive.resx">
      <DependentUpon>frm_Acc_IncomeTwithArchive.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_ACC_CustomizedIncomeReport.ar-EG.resx">
      <DependentUpon>frm_ACC_CustomizedIncomeReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_ACC_CustomizedIncomeReport.resx">
      <DependentUpon>frm_ACC_CustomizedIncomeReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\rpt_ACC_CCTrialBalance.ar-EG.resx">
      <DependentUpon>rpt_ACC_CCTrialBalance.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\rpt_ACC_CCTrialBalance.resx">
      <DependentUpon>rpt_ACC_CCTrialBalance.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_IncomeMain.ar-EG.resx">
      <DependentUpon>frm_Acc_IncomeMain.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_IncomeMain.resx">
      <DependentUpon>frm_Acc_IncomeMain.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_IncomeSub.ar-EG.resx">
      <DependentUpon>frm_Acc_IncomeSub.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_IncomeSub.resx">
      <DependentUpon>frm_Acc_IncomeSub.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_CostCenterOper.ar-EG.resx">
      <DependentUpon>frm_Acc_CostCenterOper.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_CostCenterOper.resx">
      <DependentUpon>frm_Acc_CostCenterOper.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_Account_CostCentersPivot.ar-EG.resx">
      <DependentUpon>frm_Acc_Account_CostCentersPivot.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_Account_CostCentersPivot.resx">
      <DependentUpon>frm_Acc_Account_CostCentersPivot.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_Income.ar-EG.resx">
      <DependentUpon>frm_Acc_Income.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_Income.resx">
      <DependentUpon>frm_Acc_Income.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_CashFlow.ar-EG.resx">
      <DependentUpon>frm_Acc_CashFlow.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_CashFlow.resx">
      <DependentUpon>frm_Acc_CashFlow.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\rpt_ACC_Statement.ar-EG.resx">
      <DependentUpon>rpt_ACC_Statement.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\rpt_ACC_Statement.resx">
      <DependentUpon>rpt_ACC_Statement.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\rpt_ACC_TrialBalance.ar-EG.resx">
      <DependentUpon>rpt_ACC_TrialBalance.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\rpt_ACC_TrialBalance.resx">
      <DependentUpon>rpt_ACC_TrialBalance.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\rpt_Acc_T_SubRpt.resx">
      <DependentUpon>rpt_Acc_T_SubRpt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_ACC_Journal.resx">
      <DependentUpon>rpt_ACC_Journal.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_ACC_Statment.ar-EG.resx">
      <DependentUpon>rpt_ACC_Statment.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_ACC_Statment.resx">
      <DependentUpon>rpt_ACC_Statment.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_CashNote.resx">
      <DependentUpon>rpt_CashNote.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_CashTransfer.resx">
      <DependentUpon>rpt_CashTransfer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_EmployeeDetails.ar-EG.resx">
      <DependentUpon>rpt_EmployeeDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_EmployeeDetails.resx">
      <DependentUpon>rpt_EmployeeDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_EmployeeStroy.ar-EG.resx">
      <DependentUpon>rpt_EmployeeStroy.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_EmployeeStroy.resx">
      <DependentUpon>rpt_EmployeeStroy.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_HR_Penalty.resx">
      <DependentUpon>rpt_HR_Penalty.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_HR_Reward.resx">
      <DependentUpon>rpt_HR_Reward.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_IC_BOM.resx">
      <DependentUpon>rpt_IC_BOM.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_IC_Damaged.resx">
      <DependentUpon>rpt_IC_Damaged.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_IC_InTrns.resx">
      <DependentUpon>rpt_IC_InTrns.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_IC_OutTrns.resx">
      <DependentUpon>rpt_IC_OutTrns.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_IC_StoreMove.resx">
      <DependentUpon>rpt_IC_StoreMove.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_JO_JobOrder.resx">
      <DependentUpon>rpt_JO_JobOrder.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_JO_JobOrderListCustomer.resx">
      <DependentUpon>rpt_JO_JobOrderListCustomer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_JO_JobOrderListDept.resx">
      <DependentUpon>rpt_JO_JobOrderListDept.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_Manf_QC.resx">
      <DependentUpon>rpt_Manf_QC.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_manufacture.resx">
      <DependentUpon>rpt_manufacture.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_manufacture_actualRows.resx">
      <DependentUpon>rpt_manufacture_actualRows.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_multiple_weights_dup.ar-EG.resx">
      <DependentUpon>rpt_multiple_weights_dup.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_multiple_weights_dup.resx">
      <DependentUpon>rpt_multiple_weights_dup.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_multiple_weights.ar-EG.resx">
      <DependentUpon>rpt_multiple_weights.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_multiple_weights.resx">
      <DependentUpon>rpt_multiple_weights.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_PayNotes.resx">
      <DependentUpon>rpt_PayNotes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_PriceLevel.resx">
      <DependentUpon>rpt_PriceLevel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_Printed_Receipt.resx">
      <DependentUpon>rpt_Printed_Receipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_PR_Invoice.ar-EG.resx">
      <DependentUpon>rpt_PR_Invoice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_PR_Invoice.resx">
      <DependentUpon>rpt_PR_Invoice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_PR_Invoice_Store.ar-EG.resx">
      <DependentUpon>rpt_PR_Invoice_Store.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_PR_Invoice_Store.resx">
      <DependentUpon>rpt_PR_Invoice_Store.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_PR_Invoice_Store_Sub.resx">
      <DependentUpon>rpt_PR_Invoice_Store_Sub.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_PR_PriceLevel.resx">
      <DependentUpon>rpt_PR_PriceLevel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_PR_PurchaseOrder.ar-EG.resx">
      <DependentUpon>rpt_PR_PurchaseOrder.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_PR_PurchaseOrder.resx">
      <DependentUpon>rpt_PR_PurchaseOrder.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_PR_Quote.resx">
      <DependentUpon>rpt_PR_Quote.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_PR_Request.resx">
      <DependentUpon>rpt_PR_Request.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_PR_ReturnInvoice.ar-EG.resx">
      <DependentUpon>rpt_PR_ReturnInvoice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_PR_ReturnInvoice.resx">
      <DependentUpon>rpt_PR_ReturnInvoice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_RecieveNotes.resx">
      <DependentUpon>rpt_RecieveNotes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_RevExp.resx">
      <DependentUpon>rpt_RevExp.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_SL_Invoice.ar-EG.resx">
      <DependentUpon>rpt_SL_Invoice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_SL_Invoice.resx">
      <DependentUpon>rpt_SL_Invoice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_SL_Invoice_Store.ar-EG.resx">
      <DependentUpon>rpt_SL_Invoice_Store.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_SL_Invoice_Store.resx">
      <DependentUpon>rpt_SL_Invoice_Store.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_SL_Invoice_Store_Sub.resx">
      <DependentUpon>rpt_SL_Invoice_Store_Sub.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_SL_Quote.resx">
      <DependentUpon>rpt_SL_Quote.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_SL_ReturnInvoice.ar-EG.resx">
      <DependentUpon>rpt_SL_ReturnInvoice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_SL_ReturnInvoice.resx">
      <DependentUpon>rpt_SL_ReturnInvoice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_SL_SalesOrder.resx">
      <DependentUpon>rpt_SL_SalesOrder.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_SL_SalesOrder_Manufacturing.resx">
      <DependentUpon>rpt_SL_SalesOrder_Manufacturing.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_Weight.resx">
      <DependentUpon>rpt_Weight.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\rpt_HR_PayRoll.ar-EG.resx">
      <DependentUpon>rpt_HR_PayRoll.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\rpt_HR_PayRoll.resx">
      <DependentUpon>rpt_HR_PayRoll.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsQtyImages.ar-EG.resx">
      <DependentUpon>frm_IC_ItemsQtyImages.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsQtyImages.resx">
      <DependentUpon>frm_IC_ItemsQtyImages.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsQtywithImages.ar-EG.resx">
      <DependentUpon>frm_IC_ItemsQtywithImages.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsQtywithImages.resx">
      <DependentUpon>frm_IC_ItemsQtywithImages.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsQtyHWithoutCost.ar-EG.resx">
      <DependentUpon>frm_IC_ItemsQtyHWithoutCost.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsQtyHWithoutCost.resx">
      <DependentUpon>frm_IC_ItemsQtyHWithoutCost.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_Item_In_Out_Balance.ar-EG.resx">
      <DependentUpon>frm_IC_Item_In_Out_Balance.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_Item_In_Out_Balance.resx">
      <DependentUpon>frm_IC_Item_In_Out_Balance.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsQtyWithPricelevel.ar-EG.resx">
      <DependentUpon>frm_IC_ItemsQtyWithPricelevel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsQtyWithPricelevel.resx">
      <DependentUpon>frm_IC_ItemsQtyWithPricelevel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PR\frm_IC_StorePurchase.ar-EG.resx">
      <DependentUpon>frm_IC_StorePurchase.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PR\frm_IC_StorePurchase.resx">
      <DependentUpon>frm_IC_StorePurchase.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PR\frm_ReceivingAndAchievement.ar-EG.resx">
      <DependentUpon>frm_ReceivingAndAchievement.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PR\frm_ReceivingAndAchievement.resx">
      <DependentUpon>frm_ReceivingAndAchievement.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="rpt_Acc_CostCenter.resx">
      <DependentUpon>rpt_Acc_CostCenter.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_CustomsCertifecationWarning.ar-EG.resx">
      <DependentUpon>frm_CustomsCertifecationWarning.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_CustomsCertifecationWarning.resx">
      <DependentUpon>frm_CustomsCertifecationWarning.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_InvoiceDetails.ar-EG.resx">
      <DependentUpon>frm_SL_InvoiceDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_InvoiceDetails.resx">
      <DependentUpon>frm_SL_InvoiceDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_Sl_JobOrderStatus.ar-EG.resx">
      <DependentUpon>frm_Sl_JobOrderStatus.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_Sl_JobOrderStatus.resx">
      <DependentUpon>frm_Sl_JobOrderStatus.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_CustomerGroupItemsNet.ar-EG.resx">
      <DependentUpon>frm_SL_CustomerGroupItemsNet.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_CustomerGroupItemsNet.resx">
      <DependentUpon>frm_SL_CustomerGroupItemsNet.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_IC_StoreSales.ar-EG.resx">
      <DependentUpon>frm_IC_StoreSales.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_IC_StoreSales.resx">
      <DependentUpon>frm_IC_StoreSales.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\rpt_IC_InOutItems.ar-EG.resx">
      <DependentUpon>rpt_IC_InOutItems.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\rpt_IC_InOutItems.resx">
      <DependentUpon>rpt_IC_InOutItems.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\rpt_IC_Item_Store_PriceLevel.ar-EG.resx">
      <DependentUpon>rpt_IC_Item_Store_PriceLevel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\rpt_IC_Item_Store_PriceLevel.resx">
      <DependentUpon>rpt_IC_Item_Store_PriceLevel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\rpt_IC_ItemQty.ar-EG.resx">
      <DependentUpon>rpt_IC_ItemQty.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\rpt_IC_ItemQty.resx">
      <DependentUpon>rpt_IC_ItemQty.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_CustomerTotal_Invoices.ar-EG.resx">
      <DependentUpon>frm_SL_CustomerTotal_Invoices.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_CustomerTotal_Invoices.resx">
      <DependentUpon>frm_SL_CustomerTotal_Invoices.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_CustomerInvoicesHeaders.ar-EG.resx">
      <DependentUpon>frm_SL_CustomerInvoicesHeaders.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_CustomerInvoicesHeaders.resx">
      <DependentUpon>frm_SL_CustomerInvoicesHeaders.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_Invoices_Due.ar-EG.resx">
      <DependentUpon>frm_SL_Invoices_Due.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_Invoices_Due.resx">
      <DependentUpon>frm_SL_Invoices_Due.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\rpt_SL_CustomerSale.ar-EG.resx">
      <DependentUpon>rpt_SL_CustomerSale.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\rpt_SL_CustomerSale.resx">
      <DependentUpon>rpt_SL_CustomerSale.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_Customer_Visits.ar-EG.resx">
      <DependentUpon>frm_SL_Customer_Visits.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_Customer_Visits.resx">
      <DependentUpon>frm_SL_Customer_Visits.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_AccountsBalances.ar-EG.resx">
      <DependentUpon>frm_Acc_AccountsBalances.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_AccountsBalances.resx">
      <DependentUpon>frm_Acc_AccountsBalances.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_AccountsBalancesMonths.ar-EG.resx">
      <DependentUpon>frm_Acc_AccountsBalancesMonths.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_AccountsBalancesMonths.resx">
      <DependentUpon>frm_Acc_AccountsBalancesMonths.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_ACC_SubLedger.ar-EG.resx">
      <DependentUpon>frm_ACC_SubLedger.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_ACC_SubLedger.resx">
      <DependentUpon>frm_ACC_SubLedger.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_AccountsBalancesWithNotes.ar-EG.resx">
      <DependentUpon>frm_Acc_AccountsBalancesWithNotes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_AccountsBalancesWithNotes.resx">
      <DependentUpon>frm_Acc_AccountsBalancesWithNotes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_Account_CostCenters.ar-EG.resx">
      <DependentUpon>frm_Acc_Account_CostCenters.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_Account_CostCenters.resx">
      <DependentUpon>frm_Acc_Account_CostCenters.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_BalanceT.ar-EG.resx">
      <DependentUpon>frm_Acc_BalanceT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_BalanceT.resx">
      <DependentUpon>frm_Acc_BalanceT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_CostCenterTotalBalances.ar-EG.resx">
      <DependentUpon>frm_Acc_CostCenterTotalBalances.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_CostCenterTotalBalances.resx">
      <DependentUpon>frm_Acc_CostCenterTotalBalances.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_CostCenter_AccDetails.ar-EG.resx">
      <DependentUpon>frm_Acc_CostCenter_AccDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_CostCenter_AccDetails.resx">
      <DependentUpon>frm_Acc_CostCenter_AccDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_DailyPaymentsAndDailyIncome.ar-EG.resx">
      <DependentUpon>frm_Acc_DailyPaymentsAndDailyIncome.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_DailyPaymentsAndDailyIncome.resx">
      <DependentUpon>frm_Acc_DailyPaymentsAndDailyIncome.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_YearlyBudget.ar-EG.resx">
      <DependentUpon>frm_Acc_YearlyBudget.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_YearlyBudget.resx">
      <DependentUpon>frm_Acc_YearlyBudget.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_DailyIncome.ar-EG.resx">
      <DependentUpon>frm_Acc_DailyIncome.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_DailyIncome.resx">
      <DependentUpon>frm_Acc_DailyIncome.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_DailyPayments.ar-EG.resx">
      <DependentUpon>frm_Acc_DailyPayments.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_DailyPayments.resx">
      <DependentUpon>frm_Acc_DailyPayments.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_IncomeT.ar-EG.resx">
      <DependentUpon>frm_Acc_IncomeT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\frm_Acc_IncomeT.resx">
      <DependentUpon>frm_Acc_IncomeT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\rpt_Acc_IncomeT.ar-EG.resx">
      <DependentUpon>rpt_Acc_IncomeT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\rpt_Acc_IncomeT.resx">
      <DependentUpon>rpt_Acc_IncomeT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\rpt_Acc_BalanceT.ar-EG.resx">
      <DependentUpon>rpt_Acc_BalanceT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\rpt_Acc_BalanceT.resx">
      <DependentUpon>rpt_Acc_BalanceT.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\rpt_Acc_CustomAccListBalances.ar-EG.resx">
      <DependentUpon>rpt_Acc_CustomAccListBalances.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\rpt_Acc_CustomAccListBalances.resx">
      <DependentUpon>rpt_Acc_CustomAccListBalances.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\rpt_ACC_DrawerDaySummary.ar-EG.resx">
      <DependentUpon>rpt_ACC_DrawerDaySummary.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ACC\rpt_ACC_DrawerDaySummary.resx">
      <DependentUpon>rpt_ACC_DrawerDaySummary.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frm_ReportViewer.ar-EG.resx">
      <DependentUpon>frm_ReportViewer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frm_ReportViewer.resx">
      <DependentUpon>frm_ReportViewer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\frm_HR_AllExpectedPays.ar-EG.resx">
      <DependentUpon>frm_HR_AllExpectedPays.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\frm_HR_AllExpectedPays.resx">
      <DependentUpon>frm_HR_AllExpectedPays.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\frm_HR_Emloyee_Nationality.ar-EG.resx">
      <DependentUpon>frm_HR_Emloyee_Nationality.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\frm_HR_Emloyee_Nationality.resx">
      <DependentUpon>frm_HR_Emloyee_Nationality.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\frm_HR_Employee_Report.ar-EG.resx">
      <DependentUpon>frm_HR_Employee_Report.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\frm_HR_Employee_Report.resx">
      <DependentUpon>frm_HR_Employee_Report.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\frm_HR_Att.ar-EG.resx">
      <DependentUpon>frm_HR_Att.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\frm_HR_Att.resx">
      <DependentUpon>frm_HR_Att.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\frm_HR_AllPays.ar-EG.resx">
      <DependentUpon>frm_HR_AllPays.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\frm_HR_AllPays.resx">
      <DependentUpon>frm_HR_AllPays.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\frm_HR_Insurance.ar-EG.resx">
      <DependentUpon>frm_HR_Insurance.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\frm_HR_Insurance.resx">
      <DependentUpon>frm_HR_Insurance.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\frm_HR_ManfCommision.ar-EG.resx">
      <DependentUpon>frm_HR_ManfCommision.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\frm_HR_ManfCommision.resx">
      <DependentUpon>frm_HR_ManfCommision.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\frm_HR_OutTrnsItemsCommision.ar-EG.resx">
      <DependentUpon>frm_HR_OutTrnsItemsCommision.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\frm_HR_OutTrnsItemsCommision.resx">
      <DependentUpon>frm_HR_OutTrnsItemsCommision.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\frm_HR_SalesEmpInvoiceCommision.ar-EG.resx">
      <DependentUpon>frm_HR_SalesEmpInvoiceCommision.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\frm_HR_SalesEmpInvoiceCommision.resx">
      <DependentUpon>frm_HR_SalesEmpInvoiceCommision.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\frm_HR_SalesInvItemsCommision.ar-EG.resx">
      <DependentUpon>frm_HR_SalesInvItemsCommision.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\frm_HR_SalesInvItemsCommision.resx">
      <DependentUpon>frm_HR_SalesInvItemsCommision.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\frm_HR_VacationBal.ar-EG.resx">
      <DependentUpon>frm_HR_VacationBal.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\frm_HR_VacationBal.resx">
      <DependentUpon>frm_HR_VacationBal.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\frm_HR_Vacations.ar-EG.resx">
      <DependentUpon>frm_HR_Vacations.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\frm_HR_Vacations.resx">
      <DependentUpon>frm_HR_Vacations.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\frm_SalesRep_DaySummary.ar-EG.resx">
      <DependentUpon>frm_SalesRep_DaySummary.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\frm_SalesRep_DaySummary.resx">
      <DependentUpon>frm_SalesRep_DaySummary.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\rpt_HR_EmpAdvAtt.ar-EG.resx">
      <DependentUpon>rpt_HR_EmpAdvAtt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\rpt_HR_EmpAdvAtt.resx">
      <DependentUpon>rpt_HR_EmpAdvAtt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\rpt_HR_Pay.ar-EG.resx">
      <DependentUpon>rpt_HR_Pay.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\rpt_HR_Pay.resx">
      <DependentUpon>rpt_HR_Pay.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\rpt_HR_SalesEmpTargetCommission.resx">
      <DependentUpon>rpt_HR_SalesEmpTargetCommission.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\rpt_SalesRep_DaySummary.ar-EG.resx">
      <DependentUpon>rpt_SalesRep_DaySummary.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="HR\rpt_SalesRep_DaySummary.resx">
      <DependentUpon>rpt_SalesRep_DaySummary.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemOpenInOutClose.ar-EG.resx">
      <DependentUpon>frm_IC_ItemOpenInOutClose.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemOpenInOutClose.resx">
      <DependentUpon>frm_IC_ItemOpenInOutClose.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemOpenInOutCloseQty.ar-EG.resx">
      <DependentUpon>frm_IC_ItemOpenInOutCloseQty.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemOpenInOutCloseQty.resx">
      <DependentUpon>frm_IC_ItemOpenInOutCloseQty.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsEvaluation.ar-EG.resx">
      <DependentUpon>frm_IC_ItemsEvaluation.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsEvaluation.resx">
      <DependentUpon>frm_IC_ItemsEvaluation.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsExpired.ar-EG.resx">
      <DependentUpon>frm_IC_ItemsExpired.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsExpired.resx">
      <DependentUpon>frm_IC_ItemsExpired.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsMaxSell.ar-EG.resx">
      <DependentUpon>frm_IC_ItemsMaxSell.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsMaxSell.resx">
      <DependentUpon>frm_IC_ItemsMaxSell.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsMinLevel.ar-EG.resx">
      <DependentUpon>frm_IC_ItemsMinLevel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsMinLevel.resx">
      <DependentUpon>frm_IC_ItemsMinLevel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsMinSell.ar-EG.resx">
      <DependentUpon>frm_IC_ItemsMinSell.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsMinSell.resx">
      <DependentUpon>frm_IC_ItemsMinSell.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsNotSold.ar-EG.resx">
      <DependentUpon>frm_IC_ItemsNotSold.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsNotSold.resx">
      <DependentUpon>frm_IC_ItemsNotSold.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsQty.ar-EG.resx">
      <DependentUpon>frm_IC_ItemsQty.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsQty.resx">
      <DependentUpon>frm_IC_ItemsQty.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsQtyDetails.ar-EG.resx">
      <DependentUpon>frm_IC_ItemsQtyDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsQtyDetails.resx">
      <DependentUpon>frm_IC_ItemsQtyDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsQtyH.ar-EG.resx">
      <DependentUpon>frm_IC_ItemsQtyH.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsQtyH.resx">
      <DependentUpon>frm_IC_ItemsQtyH.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsQtyWithPrices.ar-EG.resx">
      <DependentUpon>frm_IC_ItemsQtyWithPrices.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsQtyWithPrices.resx">
      <DependentUpon>frm_IC_ItemsQtyWithPrices.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsQtyWithSalesPrice.ar-EG.resx">
      <DependentUpon>frm_IC_ItemsQtyWithSalesPrice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsQtyWithSalesPrice.resx">
      <DependentUpon>frm_IC_ItemsQtyWithSalesPrice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsReorder.ar-EG.resx">
      <DependentUpon>frm_IC_ItemsReorder.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsReorder.resx">
      <DependentUpon>frm_IC_ItemsReorder.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsTurnOver.ar-EG.resx">
      <DependentUpon>frm_IC_ItemsTurnOver.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemsTurnOver.resx">
      <DependentUpon>frm_IC_ItemsTurnOver.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemTransactions.ar-EG.resx">
      <DependentUpon>frm_IC_ItemTransactions.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemTransactions.resx">
      <DependentUpon>frm_IC_ItemTransactions.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_SoldItemsAndReturnCost.ar-EG.resx">
      <DependentUpon>frm_IC_SoldItemsAndReturnCost.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_SoldItemsAndReturnCost.resx">
      <DependentUpon>frm_IC_SoldItemsAndReturnCost.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_SoldItemsCost.ar-EG.resx">
      <DependentUpon>frm_IC_SoldItemsCost.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_SoldItemsCost.resx">
      <DependentUpon>frm_IC_SoldItemsCost.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\rpt_IC_ItemTransactionsDetails.ar-EG.resx">
      <DependentUpon>rpt_IC_ItemTransactionsDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\rpt_IC_ItemTransactionsDetails.resx">
      <DependentUpon>rpt_IC_ItemTransactionsDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\rpt_IC_ItemTransactionsDetails_SubRpt.ar-EG.resx">
      <DependentUpon>rpt_IC_ItemTransactionsDetails_SubRpt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\rpt_IC_ItemTransactionsDetails_SubRpt.resx">
      <DependentUpon>rpt_IC_ItemTransactionsDetails_SubRpt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources1.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="PR\frm_PR_InvoicesDiscountTaxHeaders.ar-EG.resx">
      <DependentUpon>frm_PR_InvoicesDiscountTaxHeaders.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PR\frm_PR_InvoicesDiscountTaxHeaders.resx">
      <DependentUpon>frm_PR_InvoicesDiscountTaxHeaders.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PR\frm_PR_InvoicesHeaders.ar-EG.resx">
      <DependentUpon>frm_PR_InvoicesHeaders.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PR\frm_PR_InvoicesHeaders.resx">
      <DependentUpon>frm_PR_InvoicesHeaders.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PR\frm_PR_ItemsPurchases.ar-EG.resx">
      <DependentUpon>frm_PR_ItemsPurchases.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PR\frm_PR_ItemsPurchases.resx">
      <DependentUpon>frm_PR_ItemsPurchases.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PR\frm_PR_ItemsReturns.ar-EG.resx">
      <DependentUpon>frm_PR_ItemsReturns.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PR\frm_PR_ItemsReturns.resx">
      <DependentUpon>frm_PR_ItemsReturns.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PR\frm_PR_ReturnHeaders.ar-EG.resx">
      <DependentUpon>frm_PR_ReturnHeaders.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PR\frm_PR_ReturnHeaders.resx">
      <DependentUpon>frm_PR_ReturnHeaders.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PR\frm_PR_VendorItemsPurchases.ar-EG.resx">
      <DependentUpon>frm_PR_VendorItemsPurchases.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PR\frm_PR_VendorItemsPurchases.resx">
      <DependentUpon>frm_PR_VendorItemsPurchases.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PR\frm_PR_VendorItemsPurchases_InTrns.ar-EG.resx">
      <DependentUpon>frm_PR_VendorItemsPurchases_InTrns.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PR\frm_PR_VendorItemsPurchases_InTrns.resx">
      <DependentUpon>frm_PR_VendorItemsPurchases_InTrns.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="rpt_barcodeLabels.resx">
      <DependentUpon>rpt_barcodeLabels.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="rpt_ItemPriceChangings.ar-EG.resx">
      <DependentUpon>rpt_ItemPriceChangings.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="rpt_ItemPriceChangings.resx">
      <DependentUpon>rpt_ItemPriceChangings.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="rpt_Sub_Tree.resx">
      <DependentUpon>rpt_Sub_Tree.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="rpt_Sub_AccTree.resx">
      <DependentUpon>rpt_Sub_AccTree.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="rpt_Template2.resx">
      <DependentUpon>rpt_Template2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="rpt_Template.resx">
      <DependentUpon>rpt_Template.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_CapitalInDeferredInvoices.ar-EG.resx">
      <DependentUpon>frm_CapitalInDeferredInvoices.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_CapitalInDeferredInvoices.resx">
      <DependentUpon>frm_CapitalInDeferredInvoices.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_averagesellingpriceoftheitems.ar-EG.resx">
      <DependentUpon>frm_SL_averagesellingpriceoftheitems.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_averagesellingpriceoftheitems.resx">
      <DependentUpon>frm_SL_averagesellingpriceoftheitems.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_MrAllSales.ar-EG.resx">
      <DependentUpon>frm_MrAllSales.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_MrAllSales.resx">
      <DependentUpon>frm_MrAllSales.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_CustomerItemsSales.ar-EG.resx">
      <DependentUpon>frm_SL_CustomerItemsSales.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_CustomerItemsSales.resx">
      <DependentUpon>frm_SL_CustomerItemsSales.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_CustomerItemsSales_OutTrns.ar-EG.resx">
      <DependentUpon>frm_SL_CustomerItemsSales_OutTrns.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_CustomerItemsSales_OutTrns.resx">
      <DependentUpon>frm_SL_CustomerItemsSales_OutTrns.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_DelegatesSales.ar-EG.resx">
      <DependentUpon>frm_SL_DelegatesSales.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_DelegatesSales.resx">
      <DependentUpon>frm_SL_DelegatesSales.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_DeliveryOfficialsSales.ar-EG.resx">
      <DependentUpon>frm_SL_DeliveryOfficialsSales.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_DeliveryOfficialsSales.resx">
      <DependentUpon>frm_SL_DeliveryOfficialsSales.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_InvoicesHeaders.ar-EG.resx">
      <DependentUpon>frm_SL_InvoicesHeaders.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_InvoicesHeaders.resx">
      <DependentUpon>frm_SL_InvoicesHeaders.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_ItemsReturn.ar-EG.resx">
      <DependentUpon>frm_SL_ItemsReturn.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_ItemsReturn.resx">
      <DependentUpon>frm_SL_ItemsReturn.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_ItemsSales.ar-EG.resx">
      <DependentUpon>frm_SL_ItemsSales.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_ItemsSales.resx">
      <DependentUpon>frm_SL_ItemsSales.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_Profit_Loss.ar-EG.resx">
      <DependentUpon>frm_SL_Profit_Loss.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_Profit_Loss.resx">
      <DependentUpon>frm_SL_Profit_Loss.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_ReturnHeaders.ar-EG.resx">
      <DependentUpon>frm_SL_ReturnHeaders.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_ReturnHeaders.resx">
      <DependentUpon>frm_SL_ReturnHeaders.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_Car_Weights.ar-EG.resx">
      <DependentUpon>frm_SL_Car_Weights.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_Car_Weights.resx">
      <DependentUpon>frm_SL_Car_Weights.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_Warranty.ar-EG.resx">
      <DependentUpon>frm_SL_Warranty.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_Warranty.resx">
      <DependentUpon>frm_SL_Warranty.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\rpt_SL_CustomerTrans.ar-EG.resx">
      <DependentUpon>rpt_SL_CustomerTrans.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\rpt_SL_CustomerTrans.resx">
      <DependentUpon>rpt_SL_CustomerTrans.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Documents\rpt_IC_Multiple_Weights_Sub.resx">
      <DependentUpon>rpt_IC_Multiple_Weights_Sub.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\rpt_SL_CustomerTrans_Sub.resx">
      <DependentUpon>rpt_SL_CustomerTrans_Sub.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\rpt_SL_ItemTrade.ar-EG.resx">
      <DependentUpon>rpt_SL_ItemTrade.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\rpt_SL_ItemTrade.resx">
      <DependentUpon>rpt_SL_ItemTrade.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\frm_Acc_AccountArchiveDetails.ar-EG.resx">
      <DependentUpon>frm_Acc_AccountArchiveDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\frm_Acc_AccountArchiveDetails.resx">
      <DependentUpon>frm_Acc_AccountArchiveDetails.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\frm_Acc_AccountDetails.ar-EG.resx">
      <DependentUpon>frm_Acc_AccountDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\frm_Acc_AccountDetails.resx">
      <DependentUpon>frm_Acc_AccountDetails.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\frm_Acc_CustomAccListDetails.ar-EG.resx">
      <DependentUpon>frm_Acc_CustomAccListDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\frm_Acc_CustomAccListDetails.resx">
      <DependentUpon>frm_Acc_CustomAccListDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemQtyBatch.ar-EG.resx">
      <DependentUpon>frm_IC_ItemQtyBatch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IC\frm_IC_ItemQtyBatch.resx">
      <DependentUpon>frm_IC_ItemQtyBatch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\frm_ImExp_Container_Report.ar-EG.resx">
      <DependentUpon>frm_ImExp_Container_Report.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\frm_ImExp_Container_Report.resx">
      <DependentUpon>frm_ImExp_Container_Report.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\frm_ImExp_PreInvoice_Report .ar-EG.resx">
      <DependentUpon>frm_ImExp_PreInvoice_Report .cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\frm_ImExp_PreInvoice_Report .resx">
      <DependentUpon>frm_ImExp_PreInvoice_Report .cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\frm_ImExp_ExportConfirmat_Report.ar-EG.resx">
      <DependentUpon>frm_ImExp_ExportConfirmat_Report.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\frm_ImExp_ExportConfirmat_Report.resx">
      <DependentUpon>frm_ImExp_ExportConfirmat_Report.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\frm_ImExp_Commission_Report.ar-EG.resx">
      <DependentUpon>frm_ImExp_Commission_Report.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\frm_ImExp_Commission_Report.resx">
      <DependentUpon>frm_ImExp_Commission_Report.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\frm_ImExp_FinesReport.ar-EG.resx">
      <DependentUpon>frm_ImExp_FinesReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\frm_ImExp_FinesReport.resx">
      <DependentUpon>frm_ImExp_FinesReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\frm_SL_Employee_Item_Target.ar-EG.resx">
      <DependentUpon>frm_SL_Employee_Item_Target.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\frm_SL_Employee_Item_Target.resx">
      <DependentUpon>frm_SL_Employee_Item_Target.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\frm_ItemsPr_and_Sl.ar-EG.resx">
      <DependentUpon>frm_ItemsPr_and_Sl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\frm_ItemsPr_and_Sl.resx">
      <DependentUpon>frm_ItemsPr_and_Sl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\frm_ManfItems.ar-EG.resx">
      <DependentUpon>frm_ManfItems.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\frm_ManfItems.resx">
      <DependentUpon>frm_ManfItems.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\frm_OrderAndAchievement.ar-EG.resx">
      <DependentUpon>frm_OrderAndAchievement.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\frm_OrderAndAchievement.resx">
      <DependentUpon>frm_OrderAndAchievement.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\frm_PR_ContractorExtract.ar-EG.resx">
      <DependentUpon>frm_PR_ContractorExtract.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\frm_PR_ContractorExtract.resx">
      <DependentUpon>frm_PR_ContractorExtract.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_CustomerItemsSalesReturns.ar-EG.resx">
      <DependentUpon>frm_SL_CustomerItemsSalesReturns.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_CustomerItemsSalesReturns.resx">
      <DependentUpon>frm_SL_CustomerItemsSalesReturns.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PR\frm_SL_ItemsNetPurchaseDetails.ar-EG.resx">
      <DependentUpon>frm_SL_ItemsNetPurchaseDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PR\frm_SL_ItemsNetPurchaseDetails.resx">
      <DependentUpon>frm_SL_ItemsNetPurchaseDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_ItemsNetSalesDetails.ar-EG.resx">
      <DependentUpon>frm_SL_ItemsNetSalesDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_ItemsNetSalesDetails.resx">
      <DependentUpon>frm_SL_ItemsNetSalesDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_ItemsSalesDetails.ar-EG.resx">
      <DependentUpon>frm_SL_ItemsSalesDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_ItemsSalesDetails.resx">
      <DependentUpon>frm_SL_ItemsSalesDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_ItemsSalesTotals.ar-EG.resx">
      <DependentUpon>frm_SL_ItemsSalesTotals.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_ItemsSalesTotals.resx">
      <DependentUpon>frm_SL_ItemsSalesTotals.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_JobOrderInv.ar-EG.resx">
      <DependentUpon>frm_SL_JobOrderInv.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_JobOrderInv.resx">
      <DependentUpon>frm_SL_JobOrderInv.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_SalesOrderItems.ar-EG.resx">
      <DependentUpon>frm_SL_SalesOrderItems.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_SalesOrderItems.resx">
      <DependentUpon>frm_SL_SalesOrderItems.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_SalesOrderItemsAndBalance.ar-EG.resx">
      <DependentUpon>frm_SL_SalesOrderItemsAndBalance.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SL\frm_SL_SalesOrderItemsAndBalance.resx">
      <DependentUpon>frm_SL_SalesOrderItemsAndBalance.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\frm_SL_DelegatesSales_ItemCategory.ar-EG.resx">
      <DependentUpon>frm_SL_DelegatesSales_ItemCategory.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\frm_SL_DelegatesSales_ItemCategory.resx">
      <DependentUpon>frm_SL_DelegatesSales_ItemCategory.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\rpt_UserDaySummary.ar-EG.resx">
      <DependentUpon>rpt_UserDaySummary.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Special\rpt_UserDaySummary.resx">
      <DependentUpon>rpt_UserDaySummary.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="uc_Currency.ar-EG.resx">
      <DependentUpon>uc_Currency.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="uc_Currency.resx">
      <DependentUpon>uc_Currency.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="uc_CurrencyValue.ar-EG.resx">
      <DependentUpon>uc_CurrencyValue.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="uc_CurrencyValue.resx">
      <DependentUpon>uc_CurrencyValue.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Resources\16 convert.png" />
    <Content Include="Resources\16 prchs.png" />
    <Content Include="Resources\16 PurOrder.png" />
    <Content Include="Resources\16 sales_order.png" />
    <Content Include="Resources\16 sell.png" />
    <Content Include="Resources\16convert.png" />
    <Content Include="Resources\16_CashIn.png" />
    <Content Include="Resources\16_CashOut.png" />
    <Content Include="Resources\16_cv.png" />
    <Content Include="Resources\16_PrOrder.png" />
    <Content Include="Resources\16_transfer.png" />
    <Content Include="Resources\52896.png" />
    <Content Include="Resources\add.png" />
    <Content Include="Resources\bom2.png" />
    <Content Include="Resources\cancel.png" />
    <Content Include="Resources\chart.png" />
    <Content Include="Resources\clse.png" />
    <Content Include="Resources\cmt.png" />
    <Content Include="Resources\damage.png" />
    <Content Include="Resources\del.png" />
    <Content Include="Resources\edit.png" />
    <Content Include="Resources\ggIco.ico" />
    <Content Include="Resources\Header.jpg" />
    <Content Include="Resources\hlp.png" />
    <Content Include="Resources\icon.ico" />
    <Content Include="Resources\ic__.ico" />
    <Content Include="Resources\in-trns-lst.png" />
    <Content Include="Resources\in-trns.png" />
    <Content Include="Resources\in-trns16.png" />
    <Content Include="Resources\job_order.png" />
    <Content Include="Resources\list.png" />
    <Content Include="Resources\loginLogo.png" />
    <Content Include="Resources\LOGO2.png" />
    <Content Include="Resources\manf_Plan.png" />
    <Content Include="Resources\MrData.png" />
    <Content Include="Resources\mtrx.png" />
    <Content Include="Resources\new.png" />
    <Content Include="Resources\nxt.png" />
    <Content Include="Resources\N_absence2.png" />
    <Content Include="Resources\N_AccTree.png" />
    <Content Include="Resources\N_assembly.png" />
    <Content Include="Resources\N_Attend2.png" />
    <Content Include="Resources\N_balance sheet.png" />
    <Content Include="Resources\N_Bank.png" />
    <Content Include="Resources\N_barcode.png" />
    <Content Include="Resources\N_BuyList.png" />
    <Content Include="Resources\N_Cash.png" />
    <Content Include="Resources\N_cashInLst.png" />
    <Content Include="Resources\N_CashOut.png" />
    <Content Include="Resources\N_cashOutLst.png" />
    <Content Include="Resources\N_category.png" />
    <Content Include="Resources\N_Check.png" />
    <Content Include="Resources\N_CheckIn16.png" />
    <Content Include="Resources\N_checkInLst.png" />
    <Content Include="Resources\N_CheckOut.png" />
    <Content Include="Resources\N_CheckOut16.png" />
    <Content Include="Resources\N_checkOutLst.png" />
    <Content Include="Resources\N_Comp.png" />
    <Content Include="Resources\N_Company.png" />
    <Content Include="Resources\N_customers.png" />
    <Content Include="Resources\N_delay2.png" />
    <Content Include="Resources\N_Drawer.png" />
    <Content Include="Resources\N_Drawer2.png" />
    <Content Include="Resources\N_EditQty.png" />
    <Content Include="Resources\N_expenses.png" />
    <Content Include="Resources\N_Income.png" />
    <Content Include="Resources\N_items.png" />
    <Content Include="Resources\N_journal.png" />
    <Content Include="Resources\N_ManList.jpg" />
    <Content Include="Resources\N_ManList.png" />
    <Content Include="Resources\N_Manufacuring.png" />
    <Content Include="Resources\N_OpenBalance.png" />
    <Content Include="Resources\N_OverTime2.png" />
    <Content Include="Resources\N_Pay2.png" />
    <Content Include="Resources\N_prchs.png" />
    <Content Include="Resources\N_print.png" />
    <Content Include="Resources\N_Prqoute.png" />
    <Content Include="Resources\N_PrReturn.png" />
    <Content Include="Resources\N_purchase.png" />
    <Content Include="Resources\N_purchase2.png" />
    <Content Include="Resources\N_PurOrder.png" />
    <Content Include="Resources\N_PurOrderLst.png" />
    <Content Include="Resources\N_qoute.png" />
    <Content Include="Resources\N_reports.png" />
    <Content Include="Resources\N_reports2.png" />
    <Content Include="Resources\N_return1.png" />
    <Content Include="Resources\N_return2.png" />
    <Content Include="Resources\N_return3.png" />
    <Content Include="Resources\N_revenue.png" />
    <Content Include="Resources\N_sell.png" />
    <Content Include="Resources\N_SellList.png" />
    <Content Include="Resources\N_Settings.png" />
    <Content Include="Resources\N_statment.png" />
    <Content Include="Resources\N_StockTaking.png" />
    <Content Include="Resources\N_Store.png" />
    <Content Include="Resources\N_Style.png" />
    <Content Include="Resources\N_Trade.png" />
    <Content Include="Resources\N_Users.png" />
    <Content Include="Resources\N_Vac16.png" />
    <Content Include="Resources\N_Vacation2.png" />
    <Content Include="Resources\N_vendor.png" />
    <Content Include="Resources\N_vendos.png" />
    <Content Include="Resources\open.png" />
    <Content Include="Resources\out-trns-lst.png" />
    <Content Include="Resources\out-trns.png" />
    <Content Include="Resources\out-trns16.png" />
    <Content Include="Resources\pos.png" />
    <Content Include="Resources\pr price level.png" />
    <Content Include="Resources\prchs.png" />
    <Content Include="Resources\prev.png" />
    <Content Include="Resources\priceList.png" />
    <Content Include="Resources\prnt.png" />
    <Content Include="Resources\PurOrder.png" />
    <Content Include="Resources\P_return.png" />
    <Content Include="Resources\QC1.png" />
    <Content Include="Resources\QCLst1.png" />
    <Content Include="Resources\qoute.png" />
    <Content Include="Resources\refresh.png" />
    <Content Include="Resources\rfrsh.png" />
    <Content Include="Resources\salesOrderLst.png" />
    <Content Include="Resources\sales_order.png" />
    <Content Include="Resources\sell.png" />
    <Content Include="Resources\Splash-pos.jpg" />
    <Content Include="Resources\Splash-pos.png" />
    <Content Include="Resources\Splash.jpg" />
    <Content Include="Resources\Splash2.jpg" />
    <Content Include="Resources\srch.png" />
    <Content Include="Resources\sve.png" />
    <Content Include="Resources\tool16.png" />
    <Content Include="Resources\transfer-lst.png" />
    <Content Include="Resources\transfer.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Thumbs.db" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\DalBL\DAL.csproj">
      <Project>{f862ce1a-2da2-4148-8a18-1df544113f03}</Project>
      <Name>DAL</Name>
      <Private>False</Private>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>