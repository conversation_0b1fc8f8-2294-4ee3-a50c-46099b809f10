﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraPrinting;
using Reports;
using DevExpress.XtraNavBar;
using DevExpress.XtraReports.UI;

namespace Pharmacy.Forms
{
    public partial class frm_IC_CompaniesList: DevExpress.XtraEditors.XtraForm
    {
        public frm_IC_CompaniesList()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

        }

        private void frm_IC_CompaniesList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            ErpUtils.Tab_Enter_Process(grdCompany);
            ErpUtils.ColumnChooser(grdCompany);
            LoadPrivilege();
            Get_Companies();
        }

        private void barBtn_New_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (ErpUtils.IsFormOpen(typeof(frm_IC_Company)))
                Application.OpenForms["frm_IC_Company"].Close();

            new frm_IC_Company(0, FormAction.Add).Show();            
        }

        private void barBtn_Open_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Open_Selected_company();
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }
        
        private void barBtn_Refresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Get_Companies();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCompany.MinimumSize = grdCompany.Size;
            new Reports.rpt_Template(this.Text, "", "", "", grdCompany, false).ShowPreview();
            grdCompany.MinimumSize = new Size(0, 0);
        }

        private void NBI_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            var view = grdCompany.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            int cmpId = Convert.ToInt32(view.GetFocusedRowCellValue(colCompanyId));
            string cmpName = view.GetFocusedRowCellValue(colCompanyNameAr).ToString();
            string cmpFltr = (Shared.IsEnglish == true ? ResICEn.txtComapny : ResICAr.txtComapny)//"الشركه: " 
                + cmpName;

        }


        private void grdCompany_DoubleClick(object sender, EventArgs e)
        {
            Open_Selected_company();            
        }

        private void NBI_Items_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            var view = grdCompany.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            if (ErpUtils.IsFormOpen(typeof(frm_IC_ItemsList)))
                Application.OpenForms["frm_IC_ItemsList"].Close();

            new frm_IC_ItemsList(0, Convert.ToInt32(view.GetFocusedRowCellValue(colCompanyId)), 0,0).Show();
        }


        private void Get_Companies()
        {
            int focusedIndex = (grdCompany.FocusedView as GridView).FocusedRowHandle;
            DAL.ERPDataContext pharm = new DAL.ERPDataContext();
            var Companies = (from c in pharm.IC_Companies
                             select new
                             {
                                 c.Address,
                                 c.CompanyCode,
                                 c.CompanyId,
                                 c.CompanyNameAr,
                                 c.CompanyNameEn,
                                 c.Tel,
                             }).ToList();

            grdCompany.DataSource = Companies;
            (grdCompany.FocusedView as GridView).FocusedRowHandle = focusedIndex;
        }

        private void Open_Selected_company()
        {
            var view = grdCompany.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;
            int inv_id = Convert.ToInt32(view.GetRowCellValue(focused_row_index, colCompanyId));
            if (ErpUtils.IsFormOpen(typeof(frm_IC_Company)))
                Application.OpenForms["frm_IC_Company"].Close();

            new frm_IC_Company(inv_id, FormAction.Edit).Show();
        }

        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.Item).Count() < 1)
                {
                    NBI_Items.Enabled = false;
                }

                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.Company).FirstOrDefault();
                if (!p.CanAdd)
                    barBtnNew.Enabled = false;                
                if (!p.CanPrint)
                    barBtnPrint.Enabled = false;
            }
        }

        private void barBtn_Help_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "الشركات");
        }        
    }
}