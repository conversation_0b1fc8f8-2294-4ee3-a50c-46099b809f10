﻿namespace Reports
{
    partial class rpt_PR_Request
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.xrTable2 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow2 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_Qty = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_UOM = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_ItemName = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_code = new DevExpress.XtraReports.UI.XRTableCell();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.lbl_User = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel4 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_notes = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel6 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLine1 = new DevExpress.XtraReports.UI.XRLine();
            this.lblReportName = new DevExpress.XtraReports.UI.XRLabel();
            this.picLogo = new DevExpress.XtraReports.UI.XRPictureBox();
            this.lblCompName = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Number = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_date = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel8 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel7 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_store = new DevExpress.XtraReports.UI.XRLabel();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.PageHeader = new DevExpress.XtraReports.UI.PageHeaderBand();
            this.xrTable1 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow1 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell6 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell5 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell8 = new DevExpress.XtraReports.UI.XRTableCell();
            this.ReportFooter = new DevExpress.XtraReports.UI.ReportFooterBand();
            this.xrPageInfo1 = new DevExpress.XtraReports.UI.XRPageInfo();
            this.xrTable4 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow4 = new DevExpress.XtraReports.UI.XRTableRow();
            this.Cell_MUOM = new DevExpress.XtraReports.UI.XRTableCell();
            this.Cell_MUOM_Factor = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Factor = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTable5 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow5 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_code2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTable3 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow3 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_ItemDescription = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Height = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Width = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Length = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_TotalQty = new DevExpress.XtraReports.UI.XRTableCell();
            this.Cell_ItemDescriptionEn = new DevExpress.XtraReports.UI.XRTableCell();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable2});
            this.Detail.HeightF = 29.16667F;
            this.Detail.Name = "Detail";
            this.Detail.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Detail.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrTable2
            // 
            this.xrTable2.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top)
                        | DevExpress.XtraPrinting.BorderSide.Right)
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable2.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable2.LocationFloat = new DevExpress.Utils.PointFloat(8.58332F, 0F);
            this.xrTable2.Name = "xrTable2";
            this.xrTable2.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow2});
            this.xrTable2.SizeF = new System.Drawing.SizeF(742.3751F, 29.16667F);
            this.xrTable2.StylePriority.UseBorders = false;
            this.xrTable2.StylePriority.UseFont = false;
            this.xrTable2.StylePriority.UseTextAlignment = false;
            this.xrTable2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow2
            // 
            this.xrTableRow2.BackColor = System.Drawing.Color.Empty;
            this.xrTableRow2.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_Qty,
            this.cell_UOM,
            this.cell_ItemName,
            this.cell_code});
            this.xrTableRow2.Font = new System.Drawing.Font("Times New Roman", 10F, System.Drawing.FontStyle.Bold);
            this.xrTableRow2.Name = "xrTableRow2";
            this.xrTableRow2.StylePriority.UseBackColor = false;
            this.xrTableRow2.StylePriority.UseFont = false;
            this.xrTableRow2.Weight = 0.54901959587545957;
            // 
            // cell_Qty
            // 
            this.cell_Qty.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.cell_Qty.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_Qty.Name = "cell_Qty";
            this.cell_Qty.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_Qty.StylePriority.UseBorders = false;
            this.cell_Qty.StylePriority.UseFont = false;
            this.cell_Qty.StylePriority.UsePadding = false;
            this.cell_Qty.StylePriority.UseTextAlignment = false;
            this.cell_Qty.Text = "كمية";
            this.cell_Qty.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_Qty.Weight = 0.37142178161675687;
            // 
            // cell_UOM
            // 
            this.cell_UOM.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.cell_UOM.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_UOM.Name = "cell_UOM";
            this.cell_UOM.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_UOM.StylePriority.UseBorders = false;
            this.cell_UOM.StylePriority.UseFont = false;
            this.cell_UOM.StylePriority.UsePadding = false;
            this.cell_UOM.StylePriority.UseTextAlignment = false;
            this.cell_UOM.Text = "وحدة قياس";
            this.cell_UOM.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_UOM.Weight = 0.35436540930743288;
            // 
            // cell_ItemName
            // 
            this.cell_ItemName.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.cell_ItemName.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_ItemName.Name = "cell_ItemName";
            this.cell_ItemName.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_ItemName.StylePriority.UseBorders = false;
            this.cell_ItemName.StylePriority.UseFont = false;
            this.cell_ItemName.StylePriority.UsePadding = false;
            this.cell_ItemName.StylePriority.UseTextAlignment = false;
            this.cell_ItemName.Text = "اســـم الصنف";
            this.cell_ItemName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_ItemName.Weight = 1.2564411513361165;
            // 
            // cell_code
            // 
            this.cell_code.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.cell_code.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_code.Name = "cell_code";
            this.cell_code.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_code.StylePriority.UseBorders = false;
            this.cell_code.StylePriority.UseFont = false;
            this.cell_code.StylePriority.UsePadding = false;
            this.cell_code.StylePriority.UseTextAlignment = false;
            this.cell_code.Text = "كود";
            this.cell_code.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_code.Weight = 0.14289107483095259;
            // 
            // TopMargin
            // 
            this.TopMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.lbl_User,
            this.xrLabel4,
            this.lbl_notes,
            this.xrLabel6,
            this.xrLine1,
            this.lblReportName,
            this.picLogo,
            this.lblCompName,
            this.lbl_Number,
            this.lbl_date,
            this.xrLabel8,
            this.xrLabel7,
            this.lbl_store});
            this.TopMargin.HeightF = 257F;
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.TopMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // lbl_User
            // 
            this.lbl_User.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_User.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_User.LocationFloat = new DevExpress.Utils.PointFloat(6.5F, 134F);
            this.lbl_User.Name = "lbl_User";
            this.lbl_User.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_User.SizeF = new System.Drawing.SizeF(173.6254F, 24.49998F);
            this.lbl_User.StylePriority.UseBorders = false;
            this.lbl_User.StylePriority.UseFont = false;
            this.lbl_User.StylePriority.UseTextAlignment = false;
            this.lbl_User.Text = "..";
            this.lbl_User.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel4
            // 
            this.xrLabel4.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel4.LocationFloat = new DevExpress.Utils.PointFloat(681.5F, 196F);
            this.xrLabel4.Name = "xrLabel4";
            this.xrLabel4.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel4.SizeF = new System.Drawing.SizeF(68.45856F, 24.49998F);
            this.xrLabel4.StylePriority.UseFont = false;
            this.xrLabel4.StylePriority.UseTextAlignment = false;
            this.xrLabel4.Text = "ملاحظات";
            this.xrLabel4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_notes
            // 
            this.lbl_notes.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_notes.LocationFloat = new DevExpress.Utils.PointFloat(294F, 162.5417F);
            this.lbl_notes.Multiline = true;
            this.lbl_notes.Name = "lbl_notes";
            this.lbl_notes.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_notes.SizeF = new System.Drawing.SizeF(387.5F, 94.45825F);
            this.lbl_notes.StylePriority.UseFont = false;
            this.lbl_notes.StylePriority.UseTextAlignment = false;
            this.lbl_notes.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight;
            // 
            // xrLabel6
            // 
            this.xrLabel6.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel6.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel6.LocationFloat = new DevExpress.Utils.PointFloat(181.5F, 134F);
            this.xrLabel6.Name = "xrLabel6";
            this.xrLabel6.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel6.SizeF = new System.Drawing.SizeF(80.83298F, 24.49998F);
            this.xrLabel6.StylePriority.UseBorders = false;
            this.xrLabel6.StylePriority.UseFont = false;
            this.xrLabel6.StylePriority.UseTextAlignment = false;
            this.xrLabel6.Text = "المستخدم";
            this.xrLabel6.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLine1
            // 
            this.xrLine1.BackColor = System.Drawing.Color.DarkGray;
            this.xrLine1.BorderColor = System.Drawing.Color.DarkGray;
            this.xrLine1.BorderWidth = 0;
            this.xrLine1.ForeColor = System.Drawing.Color.DarkGray;
            this.xrLine1.LineWidth = 0;
            this.xrLine1.LocationFloat = new DevExpress.Utils.PointFloat(6.49999F, 159F);
            this.xrLine1.Name = "xrLine1";
            this.xrLine1.SizeF = new System.Drawing.SizeF(743.7915F, 3.541748F);
            this.xrLine1.StylePriority.UseBackColor = false;
            this.xrLine1.StylePriority.UseBorderColor = false;
            this.xrLine1.StylePriority.UseBorderWidth = false;
            this.xrLine1.StylePriority.UseForeColor = false;
            // 
            // lblReportName
            // 
            this.lblReportName.Font = new System.Drawing.Font("Times New Roman", 14F, System.Drawing.FontStyle.Bold);
            this.lblReportName.LocationFloat = new DevExpress.Utils.PointFloat(141.5F, 46.5F);
            this.lblReportName.Name = "lblReportName";
            this.lblReportName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblReportName.SizeF = new System.Drawing.SizeF(120.6249F, 30F);
            this.lblReportName.StylePriority.UseFont = false;
            this.lblReportName.StylePriority.UseTextAlignment = false;
            this.lblReportName.Text = "طلب شراء";
            this.lblReportName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // picLogo
            // 
            this.picLogo.LocationFloat = new DevExpress.Utils.PointFloat(608.5833F, 83.99999F);
            this.picLogo.Name = "picLogo";
            this.picLogo.SizeF = new System.Drawing.SizeF(141.7083F, 70.00001F);
            this.picLogo.Sizing = DevExpress.XtraPrinting.ImageSizeMode.StretchImage;
            // 
            // lblCompName
            // 
            this.lblCompName.Font = new System.Drawing.Font("Times New Roman", 16F, System.Drawing.FontStyle.Bold);
            this.lblCompName.LocationFloat = new DevExpress.Utils.PointFloat(294F, 46.5F);
            this.lblCompName.Name = "lblCompName";
            this.lblCompName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblCompName.SizeF = new System.Drawing.SizeF(456.2916F, 30F);
            this.lblCompName.StylePriority.UseFont = false;
            this.lblCompName.StylePriority.UseTextAlignment = false;
            this.lblCompName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight;
            // 
            // lbl_Number
            // 
            this.lbl_Number.Font = new System.Drawing.Font("Times New Roman", 16F, System.Drawing.FontStyle.Bold);
            this.lbl_Number.LocationFloat = new DevExpress.Utils.PointFloat(6.5F, 46.5F);
            this.lbl_Number.Name = "lbl_Number";
            this.lbl_Number.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Number.SizeF = new System.Drawing.SizeF(131.8333F, 30F);
            this.lbl_Number.StylePriority.UseFont = false;
            this.lbl_Number.StylePriority.UseTextAlignment = false;
            this.lbl_Number.Text = "123";
            this.lbl_Number.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_date
            // 
            this.lbl_date.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_date.LocationFloat = new DevExpress.Utils.PointFloat(6.5F, 84F);
            this.lbl_date.Name = "lbl_date";
            this.lbl_date.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_date.SizeF = new System.Drawing.SizeF(173.6254F, 24.49998F);
            this.lbl_date.StylePriority.UseFont = false;
            this.lbl_date.StylePriority.UseTextAlignment = false;
            this.lbl_date.Text = "1/2/2013";
            this.lbl_date.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel8
            // 
            this.xrLabel8.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel8.LocationFloat = new DevExpress.Utils.PointFloat(181.5F, 109F);
            this.xrLabel8.Name = "xrLabel8";
            this.xrLabel8.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel8.SizeF = new System.Drawing.SizeF(80.83298F, 24.49998F);
            this.xrLabel8.StylePriority.UseFont = false;
            this.xrLabel8.StylePriority.UseTextAlignment = false;
            this.xrLabel8.Text = "الفرع";
            this.xrLabel8.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel7
            // 
            this.xrLabel7.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel7.LocationFloat = new DevExpress.Utils.PointFloat(181.5F, 84F);
            this.xrLabel7.Name = "xrLabel7";
            this.xrLabel7.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel7.SizeF = new System.Drawing.SizeF(80.83298F, 24.49998F);
            this.xrLabel7.StylePriority.UseFont = false;
            this.xrLabel7.StylePriority.UseTextAlignment = false;
            this.xrLabel7.Text = "التاريخ";
            this.xrLabel7.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_store
            // 
            this.lbl_store.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_store.LocationFloat = new DevExpress.Utils.PointFloat(6.5F, 109F);
            this.lbl_store.Name = "lbl_store";
            this.lbl_store.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_store.SizeF = new System.Drawing.SizeF(173.6254F, 24.49998F);
            this.lbl_store.StylePriority.UseFont = false;
            this.lbl_store.StylePriority.UseTextAlignment = false;
            this.lbl_store.Text = "الرئيسي";
            this.lbl_store.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // BottomMargin
            // 
            this.BottomMargin.HeightF = 47F;
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.BottomMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // PageHeader
            // 
            this.PageHeader.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable1});
            this.PageHeader.HeightF = 36.125F;
            this.PageHeader.Name = "PageHeader";
            // 
            // xrTable1
            // 
            this.xrTable1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top)
                        | DevExpress.XtraPrinting.BorderSide.Right)
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable1.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable1.LocationFloat = new DevExpress.Utils.PointFloat(8.583322F, 0F);
            this.xrTable1.Name = "xrTable1";
            this.xrTable1.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow1});
            this.xrTable1.SizeF = new System.Drawing.SizeF(743.4167F, 36.125F);
            this.xrTable1.StylePriority.UseBorders = false;
            this.xrTable1.StylePriority.UseFont = false;
            this.xrTable1.StylePriority.UseTextAlignment = false;
            this.xrTable1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow1
            // 
            this.xrTableRow1.BackColor = System.Drawing.Color.Moccasin;
            this.xrTableRow1.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell6,
            this.xrTableCell5,
            this.xrTableCell3,
            this.xrTableCell8});
            this.xrTableRow1.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrTableRow1.Name = "xrTableRow1";
            this.xrTableRow1.StylePriority.UseBackColor = false;
            this.xrTableRow1.StylePriority.UseFont = false;
            this.xrTableRow1.Weight = 1;
            // 
            // xrTableCell6
            // 
            this.xrTableCell6.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell6.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell6.Name = "xrTableCell6";
            this.xrTableCell6.StylePriority.UseBackColor = false;
            this.xrTableCell6.StylePriority.UseBorders = false;
            this.xrTableCell6.Text = "كمية";
            this.xrTableCell6.Weight = 0.37440584051355252;
            // 
            // xrTableCell5
            // 
            this.xrTableCell5.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell5.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell5.Name = "xrTableCell5";
            this.xrTableCell5.StylePriority.UseBackColor = false;
            this.xrTableCell5.StylePriority.UseBorders = false;
            this.xrTableCell5.Text = "وحدة قياس";
            this.xrTableCell5.Weight = 0.35721247546450641;
            // 
            // xrTableCell3
            // 
            this.xrTableCell3.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell3.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell3.Name = "xrTableCell3";
            this.xrTableCell3.StylePriority.UseBackColor = false;
            this.xrTableCell3.StylePriority.UseBorders = false;
            this.xrTableCell3.Text = "اســـم الصنف";
            this.xrTableCell3.Weight = 1.2677394692393278;
            // 
            // xrTableCell8
            // 
            this.xrTableCell8.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell8.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell8.Name = "xrTableCell8";
            this.xrTableCell8.StylePriority.UseBackColor = false;
            this.xrTableCell8.StylePriority.UseBorders = false;
            this.xrTableCell8.Text = "كود";
            this.xrTableCell8.Weight = 0.14584151168169221;
            // 
            // ReportFooter
            // 
            this.ReportFooter.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrPageInfo1,
            this.xrTable4,
            this.xrTable5,
            this.xrTable3});
            this.ReportFooter.HeightF = 241F;
            this.ReportFooter.Name = "ReportFooter";
            this.ReportFooter.PrintAtBottom = true;
            // 
            // xrPageInfo1
            // 
            this.xrPageInfo1.Format = "Page {0} of {1} ";
            this.xrPageInfo1.LocationFloat = new DevExpress.Utils.PointFloat(646.0833F, 197.9584F);
            this.xrPageInfo1.Name = "xrPageInfo1";
            this.xrPageInfo1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrPageInfo1.SizeF = new System.Drawing.SizeF(104.8751F, 23.00002F);
            this.xrPageInfo1.StylePriority.UseTextAlignment = false;
            this.xrPageInfo1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrTable4
            // 
            this.xrTable4.LocationFloat = new DevExpress.Utils.PointFloat(366.9167F, 52.95849F);
            this.xrTable4.Name = "xrTable4";
            this.xrTable4.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow4});
            this.xrTable4.SizeF = new System.Drawing.SizeF(126.7083F, 25F);
            // 
            // xrTableRow4
            // 
            this.xrTableRow4.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.Cell_MUOM,
            this.Cell_MUOM_Factor,
            this.cell_Factor});
            this.xrTableRow4.Name = "xrTableRow4";
            this.xrTableRow4.Visible = false;
            this.xrTableRow4.Weight = 1;
            // 
            // Cell_MUOM
            // 
            this.Cell_MUOM.Name = "Cell_MUOM";
            this.Cell_MUOM.Weight = 0.41126075938999929;
            // 
            // Cell_MUOM_Factor
            // 
            this.Cell_MUOM_Factor.Name = "Cell_MUOM_Factor";
            this.Cell_MUOM_Factor.Weight = 0.36499161053801621;
            // 
            // cell_Factor
            // 
            this.cell_Factor.Name = "cell_Factor";
            this.cell_Factor.Weight = 0.37855708265249177;
            // 
            // xrTable5
            // 
            this.xrTable5.LocationFloat = new DevExpress.Utils.PointFloat(493.625F, 53.41693F);
            this.xrTable5.Name = "xrTable5";
            this.xrTable5.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow5});
            this.xrTable5.SizeF = new System.Drawing.SizeF(124.4792F, 25F);
            this.xrTable5.StylePriority.UseTextAlignment = false;
            this.xrTable5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrTable5.Visible = false;
            // 
            // xrTableRow5
            // 
            this.xrTableRow5.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_code2});
            this.xrTableRow5.Name = "xrTableRow5";
            this.xrTableRow5.Weight = 1;
            // 
            // cell_code2
            // 
            this.cell_code2.Name = "cell_code2";
            this.cell_code2.Text = "code2";
            this.cell_code2.Weight = 3.96875;
            // 
            // xrTable3
            // 
            this.xrTable3.LocationFloat = new DevExpress.Utils.PointFloat(366.9167F, 27.95849F);
            this.xrTable3.Name = "xrTable3";
            this.xrTable3.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow3});
            this.xrTable3.SizeF = new System.Drawing.SizeF(373.9583F, 25F);
            this.xrTable3.StylePriority.UseTextAlignment = false;
            this.xrTable3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrTable3.Visible = false;
            // 
            // xrTableRow3
            // 
            this.xrTableRow3.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.Cell_ItemDescriptionEn,
            this.cell_ItemDescription,
            this.cell_Height,
            this.cell_Width,
            this.cell_Length,
            this.cell_TotalQty});
            this.xrTableRow3.Name = "xrTableRow3";
            this.xrTableRow3.Weight = 1;
            // 
            // cell_ItemDescription
            // 
            this.cell_ItemDescription.Name = "cell_ItemDescription";
            this.cell_ItemDescription.Text = "ItemDescription";
            this.cell_ItemDescription.Weight = 0.37239585876464842;
            // 
            // cell_Height
            // 
            this.cell_Height.Name = "cell_Height";
            this.cell_Height.Text = "Height";
            this.cell_Height.Weight = 0.74479171752929685;
            // 
            // cell_Width
            // 
            this.cell_Width.Name = "cell_Width";
            this.cell_Width.Text = "Width";
            this.cell_Width.Weight = 0.74999999999999989;
            // 
            // cell_Length
            // 
            this.cell_Length.Name = "cell_Length";
            this.cell_Length.Text = "Length";
            this.cell_Length.Weight = 0.615416259765625;
            // 
            // cell_TotalQty
            // 
            this.cell_TotalQty.Name = "cell_TotalQty";
            this.cell_TotalQty.Text = "TotalQty";
            this.cell_TotalQty.Weight = 0.884583740234375;
            // 
            // Cell_ItemDescriptionEn
            // 
            this.Cell_ItemDescriptionEn.Name = "Cell_ItemDescriptionEn";
            this.Cell_ItemDescriptionEn.Text = "ItemDescriptionEn";
            this.Cell_ItemDescriptionEn.Weight = 0.37239585876464842;
            // 
            // rpt_PR_Request
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail,
            this.TopMargin,
            this.BottomMargin,
            this.PageHeader,
            this.ReportFooter});
            this.Margins = new System.Drawing.Printing.Margins(29, 36, 257, 47);
            this.PageHeight = 1169;
            this.PageWidth = 827;
            this.PaperKind = System.Drawing.Printing.PaperKind.A4;
            this.ShowPrintMarginsWarning = false;
            this.Version = "10.1";
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.PageHeaderBand PageHeader;
        private DevExpress.XtraReports.UI.ReportFooterBand ReportFooter;
        private DevExpress.XtraReports.UI.XRLabel lbl_User;
        private DevExpress.XtraReports.UI.XRLabel xrLabel4;
        private DevExpress.XtraReports.UI.XRLabel lbl_notes;
        private DevExpress.XtraReports.UI.XRLabel xrLabel6;
        private DevExpress.XtraReports.UI.XRLine xrLine1;
        private DevExpress.XtraReports.UI.XRLabel lblReportName;
        private DevExpress.XtraReports.UI.XRPictureBox picLogo;
        private DevExpress.XtraReports.UI.XRLabel lblCompName;
        private DevExpress.XtraReports.UI.XRLabel lbl_Number;
        private DevExpress.XtraReports.UI.XRLabel lbl_date;
        private DevExpress.XtraReports.UI.XRLabel xrLabel8;
        private DevExpress.XtraReports.UI.XRLabel xrLabel7;
        private DevExpress.XtraReports.UI.XRLabel lbl_store;
        private DevExpress.XtraReports.UI.XRTable xrTable4;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow4;
        private DevExpress.XtraReports.UI.XRTableCell Cell_MUOM;
        private DevExpress.XtraReports.UI.XRTableCell Cell_MUOM_Factor;
        private DevExpress.XtraReports.UI.XRTableCell cell_Factor;
        private DevExpress.XtraReports.UI.XRTable xrTable5;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow5;
        private DevExpress.XtraReports.UI.XRTableCell cell_code2;
        private DevExpress.XtraReports.UI.XRTable xrTable3;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow3;
        private DevExpress.XtraReports.UI.XRTableCell cell_ItemDescription;
        private DevExpress.XtraReports.UI.XRTableCell cell_Height;
        private DevExpress.XtraReports.UI.XRTableCell cell_Width;
        private DevExpress.XtraReports.UI.XRTableCell cell_Length;
        private DevExpress.XtraReports.UI.XRTableCell cell_TotalQty;
        private DevExpress.XtraReports.UI.XRTable xrTable1;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow1;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell6;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell5;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell3;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell8;
        private DevExpress.XtraReports.UI.XRTable xrTable2;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow2;
        private DevExpress.XtraReports.UI.XRTableCell cell_Qty;
        private DevExpress.XtraReports.UI.XRTableCell cell_UOM;
        private DevExpress.XtraReports.UI.XRTableCell cell_ItemName;
        private DevExpress.XtraReports.UI.XRTableCell cell_code;
        private DevExpress.XtraReports.UI.XRPageInfo xrPageInfo1;
        private DevExpress.XtraReports.UI.XRTableCell Cell_ItemDescriptionEn;
    }
}
