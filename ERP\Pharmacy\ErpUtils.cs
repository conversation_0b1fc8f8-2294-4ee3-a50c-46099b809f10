using System;
using System.Linq;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using System.IO;
using System.Drawing.Imaging;
using System.Drawing;
using System.Diagnostics;
using DAL;
using System.Data.SqlClient;
using DevExpress.XtraEditors;
using DevExpress.XtraPrinting;
using System.Security.Cryptography;
using System.Net.Sockets;
using System.Net;
using DevExpress.XtraGrid;
using Pharmacy.Forms;
using DevExpress.XtraGrid.Views.Grid;
using System.Data;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraEditors.Repository;
using Microsoft.Win32;
using DevExpress.Utils.Menu;
using DevExpress.XtraGrid.Localization;
using DevExpress.XtraGrid.Columns;
using System.Xml;
using DevExpress.Utils;
using DAL.Res;
using System.Data.OleDb;

namespace Pharmacy
{
    public class ErpUtils
    {

        /// <summary>
        /// Child Form should be opened once
        /// </summary>
        /// <param name="t"> Form name</param>
        /// <returns></returns>  
        public static bool IsFormOpen(Type t)
        {
            if (!t.IsSubclassOf(typeof(Form)) && !(t == typeof(Form)))
                throw new ArgumentException("Type is not a form", "t");
            try
            {
                for (int i1 = 0; i1 < Application.OpenForms.Count; i1++)
                {
                    Form f = Application.OpenForms[i1];
                    if (t.IsInstanceOfType(f))
                        return true;
                }
            }
            catch (IndexOutOfRangeException)
            {
                //This can change if they close/open a form while code is running. Just throw it away
            }
            return false;
        }

        public static void RestoreForm(string form_Name)
        {
            Form f = Application.OpenForms[form_Name];
            f.WindowState = FormWindowState.Normal;
            f.BringToFront();
        }

        public static byte[] imageToByteArray(System.Drawing.Image imageIn)
        {
            MemoryStream ms = new MemoryStream();
            imageIn.Save(ms, ImageFormat.Jpeg);
            return ms.ToArray();
        }

        public static Image byteArrayToImage(byte[] byteArrayIn)
        {
            MemoryStream ms = new MemoryStream(byteArrayIn);
            Image returnImage = Image.FromStream(ms);
            return returnImage;
        }

        public static Color GetColorFromString(string colorString)
        {
            Color color = Color.Empty;
            ColorConverter converter = new ColorConverter();
            try
            {
                color = (Color)converter.ConvertFromString(colorString);
            }
            catch
            { }
            return color;
        }

        /// <summary>
        /// Write log entries to Windows Event Viewer
        /// </summary>
        /// <param name="message"></param>
        /// <param name="logType"> information, warning, error ...etc</param>
        public static void WriteWindowsLog(string message, EventLogEntryType logType)
        {
            //See if the Source exists. 
            if (!(EventLog.SourceExists("Pharmacy")))
            {
                EventSourceCreationData data = new EventSourceCreationData("Pharmacy", "Pharmacy");
                data.MachineName = System.Environment.MachineName;
                EventLog.CreateEventSource(data);
            }

            EventLog ev = new EventLog("Pharmacy", System.Environment.MachineName, "Pharmacy");

            ev.WriteEntry(message, logType);
            ev.Close();
        }

        /// <summary>
        /// Clear all log entries, should be used on every new year.
        /// </summary>
        public static void ClearWindowsLog()
        {
            EventLog ev = new EventLog("Pharmacy", System.Environment.MachineName);
            ev.Clear();
            ev.Close();
        }

        /// <summary>
        /// write log messages to Log folder exists in application folder,
        /// we write each day to a new file.
        /// </summary>
        /// <param name="message"></param>
        public static void WriteLocalLog(String message)
        {
            if (!Directory.Exists("Log"))
                Directory.CreateDirectory("Log");

            DateTime date = DateTime.Now;
            String filePath = "Log\\Log" + date.ToString("dd-MM") + ".log";
            if (!File.Exists(filePath))
            {
                FileStream files = File.Create(filePath);
                files.Close();
            }

            try
            {
                StreamWriter sw = File.AppendText(filePath);
                sw.WriteLine(date.ToString("MM/dd hh:mm") + "> " + message);
                sw.Flush();
                sw.Close();
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message.ToString());
            }
        }

        public static void grid_ProcessGridKey(object sender, KeyEventArgs e)
        {
            try
            {
                DevExpress.XtraGrid.GridControl grid = sender as DevExpress.XtraGrid.GridControl;

                if (e.KeyCode == Keys.Enter)
                {
                    (grid.FocusedView as DevExpress.XtraGrid.Views.Base.ColumnView).FocusedRowHandle++;
                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == Keys.Tab && e.Modifiers != Keys.Shift)
                {
                    var view = (grid.FocusedView as DevExpress.XtraGrid.Views.Grid.GridView);
                    if (view.FocusedColumn.VisibleIndex == 0)
                        view.FocusedColumn = view.VisibleColumns[view.VisibleColumns.Count - 1];
                    else
                        view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex - 1];
                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == Keys.Tab && e.Modifiers == Keys.Shift)
                {
                    var view = (grid.FocusedView as DevExpress.XtraGrid.Views.Grid.GridView);
                    if (view.FocusedColumn.VisibleIndex == view.VisibleColumns.Count)
                        view.FocusedColumn = view.VisibleColumns[0];
                    else
                        view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex + 1];
                    e.Handled = true;
                    return;
                }
            }
            catch
            { }
        }
        public static void Tab_Enter_Process(DevExpress.XtraGrid.GridControl grid)
        {
            grid.ProcessGridKey += new System.Windows.Forms.KeyEventHandler(grid_ProcessGridKey);
        }

        public static void ColumnChooser(DevExpress.XtraGrid.GridControl grid)
        {
            ((GridView)grid.FocusedView).OptionsCustomization.AllowQuickHideColumns = Shared.user.ColumnChooserAvailable;
            ((GridView)grid.FocusedView).PopupMenuShowing += new DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventHandler(grid_ShowGridMenu);
        }
        public static void grid_ShowGridMenu(object sender, DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs e)
        {
            if (e.MenuType == GridMenuType.Column) //Hide Column Chooser
            {
                DXMenuItem miColumnChooser = ErpUtils.GetGridMenuItemByStringId(e.Menu, GridStringId.MenuColumnColumnCustomization);

                if (miColumnChooser != null)
                    miColumnChooser.Visible = Shared.user.ColumnChooserAvailable;
                //---
                DXMenuItem miHideThisColumn = ErpUtils.GetGridMenuItemByStringId(e.Menu, GridStringId.MenuColumnRemoveColumn);

                if (miHideThisColumn != null)
                    miHideThisColumn.Visible = Shared.user.ColumnChooserAvailable;
            }
        }
        /// <summary>
        /// Used to hide grid column menu items
        /// </summary>
        /// <param name="menu"></param>
        /// <param name="id"></param>
        /// <returns></returns>
        public static DXMenuItem GetGridMenuItemByStringId(DXPopupMenu menu, GridStringId id)
        {
            foreach (DXMenuItem item in menu.Items)
                if (item.Caption == GridLocalizer.Active.GetLocalizedString(id))
                    return item;

            return null;
        }


        public static bool backup_Database(string DataBase_Name, string Path, string ConnectionString)
        {
            try
            {
                //Connect to DB 
                SqlConnection connect = new SqlConnection(ConnectionString);
                connect.Open();

                //Execute SQL--------------- 

                SqlCommand command;
                string command_text = "backup database " + DataBase_Name + "  to disk = '" + Path + ".bak'";
                command = new SqlCommand(command_text, connect);
                command.ExecuteNonQuery();
                connect.Close();

                DAL.ERPDataContext DB = new DAL.ERPDataContext();
                ST_BackupDate b = new ST_BackupDate();
                b.BackupDate = DAL.MyHelper.Get_Server_DateTime();
                DB.ST_BackupDates.InsertOnSubmit(b);
                DB.SubmitChanges();

                return true;
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message);
                return false;
            }
        }

        public static bool restore_Database(string DataBase_Name, string Path, string ConnectionString)
        {
            try
            {

                //Connect to DB 
                SqlConnection connect = new SqlConnection(ConnectionString);
                connect.Open();

                //Execute SQL--------------- 

                string Alter1 = @"ALTER DATABASE [" + DataBase_Name + "] SET Single_User WITH Rollback Immediate";
                SqlCommand Alter1Cmd = new SqlCommand(Alter1, connect);
                Alter1Cmd.ExecuteNonQuery();

                SqlCommand command;
                string command_text = "RESTORE DATABASE  " + DataBase_Name + "  FROM DISK  = '" + Path + "' WITH REPLACE";
                command = new SqlCommand(command_text, connect);
                command.ExecuteNonQuery();

                string Alter2 = @"ALTER DATABASE [" + DataBase_Name + "] SET Multi_User";
                SqlCommand Alter2Cmd = new SqlCommand(Alter2, connect);
                Alter2Cmd.ExecuteNonQuery();

                connect.Close();
                return true;
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message);
                return false;
            }
        }


        /// <summary>
        /// Used for Drawing Headers of reports
        /// </summary>
        /// <param name="CompName">company name</param>
        /// <param name="image">logo</param>
        public static void GetReportHeaderData(out string CompName, out Image logo)
        {
            CompName = string.Empty;
            logo = null;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            var comp = DB.ST_CompanyInfos.FirstOrDefault();
            if (comp != null)
            {
                CompName = comp.CmpNameAr;
                if (comp.Logo != null)
                    logo = ErpUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        /// <summary>
        /// Create Report Header
        /// </summary>
        /// <param name="e">EventArgs</param>
        /// <param name="ReportName"> in most cases, it is screen name</param>
        /// <param name="dateFilters"> </param>
        public static void CreateReportHeader(CreateAreaEventArgs e, string ReportName, string dateFilters, string otherFilters)
        {
            string CompName = string.Empty;
            Image image = null;
            ErpUtils.GetReportHeaderData(out CompName, out image);

            RectangleF recCompName = new RectangleF((float)87.5, (float)9.5, 600, 30);
            RectangleF recReportName = new RectangleF((float)87.5, (float)41.5, 600, 25);
            RectangleF recDateFilters = new RectangleF((float)87.5, (float)63.5, 600, (float)16.75);
            RectangleF recOtherFilters = new RectangleF((float)12.5, (float)82.5, (float)764.5, (float)25.75);
            RectangleF recImage = new RectangleF((float)12.5, (float)9.5, 70, 70);

            e.Graph.DefaultBrickStyle.SetAlignment(DevExpress.Utils.HorzAlignment.Center, DevExpress.Utils.VertAlignment.Center);
            e.Graph.StringFormat = new BrickStringFormat(StringAlignment.Center, StringAlignment.Center);

            e.Graph.Font = new Font("Times New Roman", 20, FontStyle.Regular);
            e.Graph.DrawString(CompName, Color.Black, recCompName, BorderSide.None);


            e.Graph.Font = new Font("Times New Roman", 14, FontStyle.Regular);
            e.Graph.DrawString(ReportName, Color.Black, recReportName, BorderSide.None);

            e.Graph.Font = new Font("Times New Roman", 10, FontStyle.Regular);
            e.Graph.DrawString(dateFilters, Color.Black, recDateFilters, BorderSide.None);
            e.Graph.DrawString(otherFilters, Color.Black, recOtherFilters, BorderSide.None);
            if (image != null)
                e.Graph.DrawImage(image, recImage, BorderSide.None, Color.Transparent);
        }

        public static string GetLocalIP()
        {
            string _IP = null;
            // Resolves a host name or IP address to an IPHostEntry instance.
            // IPHostEntry - Provides a container class for Internet host address information. 
            System.Net.IPHostEntry _IPHostEntry = System.Net.Dns.GetHostEntry(System.Net.Dns.GetHostName());
            // IPAddress class contains the address of a computer on an IP network. 
            foreach (System.Net.IPAddress _IPAddress in _IPHostEntry.AddressList)
            {
                // InterNetwork indicates that an IP version 4 address is expected 
                // when a Socket connects to an endpoint
                if (_IPAddress.AddressFamily.ToString() == "InterNetwork")
                {
                    _IP = _IPAddress.ToString();
                }
            }
            return _IP;
        }

        public static bool GetResolvedConnecionIPAddress(string serverNameOrURL,
           out IPAddress resolvedIPAddress)
        {
            bool isResolved = false;
            IPHostEntry hostEntry = null;
            IPAddress resolvIP = null;
            try
            {
                if (!IPAddress.TryParse(serverNameOrURL, out resolvIP))
                {
                    hostEntry = Dns.GetHostEntry(serverNameOrURL);

                    if (hostEntry != null && hostEntry.AddressList != null
                                 && hostEntry.AddressList.Length > 0)
                    {
                        if (hostEntry.AddressList.Length == 1)
                        {
                            resolvIP = hostEntry.AddressList[0];
                            isResolved = true;
                        }
                        else
                        {
                            foreach (IPAddress var in hostEntry.AddressList)
                            {
                                if (var.AddressFamily == AddressFamily.InterNetwork)
                                {
                                    resolvIP = var;
                                    isResolved = true;
                                    break;
                                }
                            }
                        }
                    }
                }
                else
                {
                    isResolved = true;
                }
            }
            catch
            {
                isResolved = false;
                resolvIP = null;
            }
            finally
            {
                resolvedIPAddress = resolvIP;
            }

            return isResolved;
        }

        public static string TrnaslateByGoogle(string sourceText, string sourceLanguage, string DestinationLanguage)
        {
            RavSoft.GoogleTranslator.Translator t = new RavSoft.GoogleTranslator.Translator();
            t.SourceLanguage = sourceLanguage;
            t.TargetLanguage = DestinationLanguage;
            t.SourceText = sourceText;
            // Translate the text
            try
            {
                t.Translate();
                return t.Translation;
            }
            catch
            {
                return sourceText;
            }
        }

        public static void save_Grid_Layout(GridControl grid, string File_name, bool Save)
        {
            if (Save == false)
                return;

            try
            {
                //get connected db name
                System.Data.SqlClient.SqlConnectionStringBuilder builder = new System.Data.SqlClient.SqlConnectionStringBuilder();
                builder.ConnectionString = DAL.Config.ConnectionString;

                string globalERP_path = Environment.GetFolderPath(Environment.SpecialFolder.Personal) + "\\GlobalERP" + "\\" + builder.InitialCatalog;
                if (!System.IO.Directory.Exists(globalERP_path))
                    System.IO.Directory.CreateDirectory(globalERP_path);

                string grid_layout_path = globalERP_path + "\\" + File_name + ".xml";

                if (Shared.user.SaveGridFilters == false)
                    (grid.MainView as GridView).ActiveFilter.Clear();

                grid.MainView.SaveLayoutToXml(grid_layout_path);

            }
            catch { }
        }

        public static void Load_Grid_Layout(GridControl grid, string File_name)
        {
            try
            {
                //get connected db name
                System.Data.SqlClient.SqlConnectionStringBuilder builder = new System.Data.SqlClient.SqlConnectionStringBuilder();
                builder.ConnectionString = DAL.Config.ConnectionString;

                string Folder_path = Environment.GetFolderPath(Environment.SpecialFolder.Personal) + "\\GlobalERP" + "\\" + builder.InitialCatalog;
                string grid_layout_path = Folder_path + "\\" + File_name + ".xml";
                grid.MainView.RestoreLayoutFromXml(grid_layout_path);

            }
            catch { }
        }

        public static void save_MemoryStream_Layout(MemoryStream layout, string File_name)
        {
            try
            {
                //get connected db name
                System.Data.SqlClient.SqlConnectionStringBuilder builder = new System.Data.SqlClient.SqlConnectionStringBuilder();
                builder.ConnectionString = DAL.Config.ConnectionString;

                string globalERP_path = Environment.GetFolderPath(Environment.SpecialFolder.Personal) + "\\GlobalERP" + "\\" + builder.InitialCatalog;
                if (!System.IO.Directory.Exists(globalERP_path))
                    System.IO.Directory.CreateDirectory(globalERP_path);

                string grid_layout_path = globalERP_path + "\\" + File_name + ".xml";

                XmlDocument doc = new XmlDocument();
                layout.Seek(0, System.IO.SeekOrigin.Begin);
                doc.Load(layout);
                doc.Save(grid_layout_path);
            }
            catch { }
        }



        public static void Load_MemoryStream_Layout(MemoryStream layout, string File_name)
        {
            try
            {
                //get connected db name
                System.Data.SqlClient.SqlConnectionStringBuilder builder = new System.Data.SqlClient.SqlConnectionStringBuilder();
                builder.ConnectionString = DAL.Config.ConnectionString;

                string Folder_path = Environment.GetFolderPath(Environment.SpecialFolder.Personal) + "\\GlobalERP" + "\\" + builder.InitialCatalog;
                string grid_layout_path = Folder_path + "\\" + File_name + ".xml";

                XmlDocument doc = new XmlDocument();
                doc.Load(grid_layout_path);
                doc.Save(layout);
            }
            catch { }
        }

        public static void Save_DataTable_To_XML(DataTable dt)
        {
            try
            {
                SaveFileDialog sfd = new SaveFileDialog();
                sfd.ShowDialog();
                if (sfd.FileName == string.Empty)
                    return;

                DataSet dataSet;
                if (dt.DataSet == null)
                {
                    dataSet = new DataSet();
                    dataSet.Tables.Add(dt);
                }
                else
                {
                    dataSet = dt.DataSet;
                }

                dataSet.WriteXml(sfd.FileName + ".xml", XmlWriteMode.WriteSchema);
            }
            catch { }
        }

        public static DataTable Load_DataTable_From_XML()
        {
            try
            {
                OpenFileDialog ofd = new OpenFileDialog();
                ofd.ShowDialog();
                if (ofd.FileName == string.Empty)
                    return null;

                DataSet dataSet = new DataSet();
                dataSet.ReadXml(ofd.FileName);
                return dataSet.Tables[0];
            }
            catch { return null; }
        }

        public static void Move_Row_Up(GridView view)
        {
            try
            {
                int index = view.FocusedRowHandle;
                if (index <= 0) return;

                DataRow row1 = view.GetDataRow(index);
                DataRow row2 = view.GetDataRow(index - 1);
                var temp = row1.ItemArray;
                row1.ItemArray = row2.ItemArray;
                row2.ItemArray = temp;
                view.FocusedRowHandle = index;
            }
            catch { }
        }

        public static void Move_Row_Down(GridView view)
        {
            try
            {
                int index = view.FocusedRowHandle;
                if (index >= view.DataRowCount - 1) return;

                DataRow row1 = view.GetDataRow(index);
                DataRow row2 = view.GetDataRow(index + 1);
                var temp = row1.ItemArray;
                row1.ItemArray = row2.ItemArray;
                row2.ItemArray = temp;
                view.FocusedRowHandle = index;
            }
            catch { }
        }

        public static DataTable Source_dt = new DataTable();
        public static void Copy_Data_Table(DataTable Destination_dt)
        {
            foreach (DataRow dr in Source_dt.Rows)
            {
                DataRow new_dr = Destination_dt.NewRow();
                foreach (DataColumn dc in Source_dt.Columns)
                    if (Destination_dt.Columns.Contains(dc.ColumnName))
                        new_dr[dc.ColumnName] = dr[dc.ColumnName];

                Destination_dt.Rows.Add(new_dr);
            }
            Source_dt = new DataTable();
        }

        public static DataTable dt_Copied_Rows = Init_Dt_Copied_Rows();

        private static DataTable Init_Dt_Copied_Rows()
        {
            dt_Copied_Rows = new DataTable();
            dt_Copied_Rows.Columns.Clear();
            dt_Copied_Rows.Columns.Add("ItemId");
            dt_Copied_Rows.Columns.Add("Qty");
            dt_Copied_Rows.Columns.Add("Length");
            dt_Copied_Rows.Columns.Add("Width");
            dt_Copied_Rows.Columns.Add("Height");
            dt_Copied_Rows.Columns.Add("PiecesCount");
            dt_Copied_Rows.Columns.Add("Expire");
            dt_Copied_Rows.Columns.Add("Batch");
            dt_Copied_Rows.Columns.Add("SellPrice");
            dt_Copied_Rows.Columns.Add("Serial");

            return dt_Copied_Rows;
        }


        /// <summary>
        /// if My Documents folder exist use it, else use second path which can be network path
        /// </summary>
        /// <param name="st_store"></param>
        /// <returns></returns>
        public static string GetReportsPath(ST_Store st_store)
        {
            string path = string.Empty;
            if (!string.IsNullOrEmpty(st_store.MyDocReportPathFolder))
            {
                //check folder existence            
                if (!System.IO.Directory.Exists(
                    Environment.GetFolderPath(Environment.SpecialFolder.Personal) + "\\" + "GlobalERP" + "\\" + st_store.MyDocReportPathFolder))
                {
                    //if not exist create it
                    //System.IO.Directory.CreateDirectory(Environment.GetFolderPath(Environment.SpecialFolder.Personal) + "\\" + "GlobalERP" + "\\" + st_store.MyDocReportPathFolder);                    
                    if (!string.IsNullOrEmpty(st_store.SecondReportPath))
                        path = st_store.SecondReportPath + "\\";
                }
                else
                {
                    //set it as report path
                    path = Environment.GetFolderPath(Environment.SpecialFolder.Personal) + "\\" + "GlobalERP" + "\\" + st_store.MyDocReportPathFolder + "\\";
                }
            }
            else if (!string.IsNullOrEmpty(st_store.SecondReportPath))
            {
                path = st_store.SecondReportPath + "\\";
            }
            else
                path = string.Empty;

            return path;
        }
        public static string GetAttachmentsPath(ST_Store st_store)
        {
            string path = string.Empty;
            if (!string.IsNullOrEmpty(st_store.AttachmentPath))
            {
                path = st_store.AttachmentPath + "\\";
            }
            else
                path = string.Empty;

            return path;
        }

        public static void Allow_Incremental_Search(RepositoryItemGridLookUpEdit lkp)
        {
            //lkp.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            lkp.View.CustomDrawCell += new RowCellCustomDrawEventHandler(View_CustomDrawCell);
        }

        public static void Allow_Incremental_Search(GridLookUpEdit lkp)
        {
            //lkp.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;            
            lkp.Properties.View.CustomDrawCell += new RowCellCustomDrawEventHandler(View_CustomDrawCell);
        }

        public static void View_CustomDrawCell(object sender, RowCellCustomDrawEventArgs e)
        {
            e.Appearance.FillRectangle(e.Cache, e.Bounds);
            e.Appearance.DrawString(e.Cache, e.DisplayText, e.Bounds);
            e.Handled = true;
        }

        public static void Check_Application_Validation()
        {
            //Copy_Application_Activation();
            try
            {
                ERPDataContext DB = new ERPDataContext();

                var code = (from s in DB.ST_CompanyInfos
                            select new
                            {
                                s.Processes
                            }).SingleOrDefault();

                #region old code
                //RegistryKey key = Registry.LocalMachine.OpenSubKey("Software", true);

                //key.OpenSubKey(Application.ProductName);
                //key = key.OpenSubKey(Application.ProductName, true);

                //if (key != null && key.GetValue("RegNumber") != null)
                //{
                //    string val = key.GetValue("RegNumber").ToString(); 
                #endregion

                XmlReader reader = null;
                string actvCode = string.Empty;
                try
                {
                    if (File.Exists(Environment.GetFolderPath(Environment.SpecialFolder.Personal) + "\\" + "GlobalERP" + "\\" + "ERP_Profile2.xml"))
                    {
                        reader = XmlReader.Create(Environment.GetFolderPath(Environment.SpecialFolder.Personal) + "\\" + "GlobalERP" + "\\" + "ERP_Profile2.xml");

                        while (reader.Read())
                        {
                            if (reader.NodeType == XmlNodeType.Element && reader.Name == "App")
                            {
                                actvCode = reader.ReadElementContentAsString();
                            }
                        }
                    }
                }
                catch
                {
                }
                finally
                {

                    if (reader != null)
                        reader.Close();
                }

                if (actvCode != string.Empty)
                {
                    frmActivation Activation = new frmActivation();
                    double macSerial = Activation.GetHardwareSerials();
                    double genSerial = Activation.Generate_Serial_Code(macSerial);

                    if ((genSerial + 1 + 2 + 3 + 4 + 5 + 6 + 7 + 8 + 9 + 10 + 11 - 12 - 11 + 12) != Convert.ToDouble(actvCode) + 1 + 2 + 3 + 4 + 5 + 6 + 7 + 8 + 9 + 10 + 11 - 12 - 11 + 12)
                    {
                        MessageBox.Show("يوجد خطأ في تسجيل البرنامج، يرجى مراجعة مدير النظام", "", MessageBoxButtons.OK, MessageBoxIcon.Stop);
                        frmActivation.Is_Activated = false;
                        new frmActivation().ShowDialog();
                        Application.Exit();
                    }
                    else
                    {
                        frmActivation.Is_Activated = true;
                    }
                }
                else
                {
                    int processesCount = 0;
                    if (code.Processes == null || code.Processes.Trim() == string.Empty)
                        processesCount = 100;
                    else
                    {
                        try
                        {
                            processesCount = Convert.ToInt32(Crypto.DecryptStringAES(code.Processes, Crypto.Key));
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show(ex.Message);
                            MessageBox.Show(ex.InnerException?.Message);
                            processesCount = 100;
                        }
                    }

                    var storeCount = (from c in DB.SL_Invoices
                                      select c).Count();
                    //var jrnlCount = (from c in DB.ACC_Journals
                    //                 select c).Count();
                    if (storeCount >= processesCount * 2 )
                    {
                        MessageBox.Show("انتهت صلاحية البرنامج، لكي تتمكن من المتابعة يرجى التسجيل أولا ", "تفعيل البرنامج", MessageBoxButtons.OK, MessageBoxIcon.Stop);
                        frmActivation.Is_Activated = false;
                        new frmActivation().ShowDialog();
                        Application.Exit();
                    }
                    else
                    {
                        Shared.TrialVersion = "نسخة تجريبية";
                        frmActivation.Is_Activated = true;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                Application.Exit();
            }
        }

        public static void Copy_Application_Activation()
        {
            RegistryKey key = Registry.LocalMachine.OpenSubKey("Software", true);

            key.OpenSubKey("Global ERP System");
            key = key.OpenSubKey("Global ERP System", true);

            if (key != null && key.GetValue("RegNumber") != null)
            {
                string val = key.GetValue("RegNumber").ToString();

                XmlWriterSettings settings = new XmlWriterSettings();
                settings.Indent = true;

                XmlWriter writer = null;
                try
                {
                    string Folder_path = Environment.GetFolderPath(Environment.SpecialFolder.Personal) + "\\GlobalERP";
                    if (!System.IO.Directory.Exists(Folder_path))
                        System.IO.Directory.CreateDirectory(Folder_path);

                    writer = XmlWriter.Create(
                        Environment.GetFolderPath(Environment.SpecialFolder.Personal) + "\\" + "GlobalERP" + "\\" + "ERP_Profile2.xml", settings);

                    writer.WriteStartDocument();
                    writer.WriteComment("This file is generated by Global Grid ERP System");

                    writer.WriteStartElement("Settings");

                    writer.WriteElementString("App", val);

                    writer.WriteEndElement();
                    writer.WriteEndDocument();
                    writer.Flush();
                    writer.Close();
                }
                catch
                { }
                finally
                {
                    writer.Close();
                }
            }
            Console.WriteLine("Process Done Successfully");
            Console.ReadLine();
        }

        /// <summary>
        /// load all accounts tree, or child accounts in a tree style
        /// </summary>
        /// <returns></returns>
        public static List<acc> LoadAccountsTree_Mahmood(int mainAccount, bool asTree)
        {
            List<ACC_Account> lstAllAccounts = new ERPDataContext().ACC_Accounts.Select(x => x).ToList();
            TreeView treeBalance = new TreeView();
            List<acc> lstAccounts = new List<acc>();

            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            //Load tree
            treeBalance.Nodes.Clear();
            int BlncCounter = 0;
            foreach (var c in lstAllAccounts)
            {
                if (mainAccount == 0 && c.ParentActId == null)
                {
                    treeBalance.Nodes.Add(c.AccountId.ToString(), c.AcNameAr);
                    BlncCounter += 1;
                }
                else if (mainAccount != 0 && c.AccountId == mainAccount)
                {
                    treeBalance.Nodes.Add(c.AccountId.ToString(), c.AcNameAr);
                    BlncCounter += 1;
                }
                else
                {
                    if (c.ParentActId != null)
                    {
                        TreeNode node = treeBalance.Nodes.Find(c.ParentActId.Value.ToString(), true).FirstOrDefault();
                        if (node != null)
                        {
                            if (node.Nodes[c.AccountId.ToString()] == null)
                            {
                                node.Nodes.Add(c.AccountId.ToString(), c.AcNameAr, (c.ParentActId == null ? -1 : c.ParentActId.Value));
                                BlncCounter += 1;
                            }
                        }
                    }
                }
            }
            treeBalance.ExpandAll();

            //load Grid

            lstAccounts.Clear();
            TreeNode currentNode = treeBalance.Nodes[0];
            for (int x = 0; x < BlncCounter; x++)
            {
                string spaces = "";
                if (currentNode != null)
                {
                    if (asTree)
                    {
                        for (int y = 0; y < currentNode.Level; y++)
                        {
                            spaces += "  ";
                        }
                    }
                }

                lstAccounts.Add(new acc
                {
                    AccId = Convert.ToInt32(currentNode.Name),
                    AccName = Shared.IsEnglish ? spaces + currentNode.Text : currentNode.Text + spaces,
                    ParentId = currentNode.ImageIndex,
                    NodeLevel = currentNode.Level,
                    CostCenter = lstAllAccounts.Where(y => y.AccountId == Convert.ToInt32(currentNode.Name)).Select(y => y.CostCenter).FirstOrDefault(),
                    AccSecurityLevel = lstAllAccounts.Where(y => y.AccountId == Convert.ToInt32(currentNode.Name)).Select(y => y.AccSecurityLevel).FirstOrDefault(),
                });
                if (currentNode != null && currentNode.NextVisibleNode != null && currentNode.NextVisibleNode != currentNode)
                {
                    currentNode = currentNode.NextVisibleNode;
                }
            }
            return lstAccounts;
        }


        public static object GetGridLookUpValue(RepositoryItemGridLookUpEdit lkp, object keyvalue, string FieldName)
        {
            if (keyvalue == null || lkp.DataSource == null)
                return null;

            object val = null;

            try
            {
                if ((lkp.GetRowByKeyValue(keyvalue) as DataRowView) != null)
                    val = (lkp.GetRowByKeyValue(keyvalue) as DataRowView).Row[FieldName];
                else
                    val = (lkp.GetRowByKeyValue(keyvalue)).GetType().GetProperty(FieldName).GetValue(lkp.GetRowByKeyValue(keyvalue), null);
            }
            catch { }

            return val;
        }

        public static object GetGridLookUpValue(GridLookUpEdit lkp, object keyvalue, string FieldName)
        {
            if (keyvalue == null || lkp.Properties.DataSource == null)
                return null;

            object val = null;

            try
            {
                if ((lkp.Properties.GetRowByKeyValue(keyvalue) as DataRowView) != null)
                    val = (lkp.Properties.GetRowByKeyValue(keyvalue) as DataRowView).Row[FieldName];
                else
                    val = (lkp.Properties.GetRowByKeyValue(keyvalue)).GetType().GetProperty(FieldName).GetValue(lkp.Properties.GetRowByKeyValue(keyvalue), null);
            }
            catch { }

            return val;
        }

        public static bool CheckDocumentSaved(int Id, bool DataModified, DataTable dt)
        {
            if (Id == 0)
            {
                XtraMessageBox.Show(Shared.IsEnglish ? ResEn.MsgAskToSave : ResAr.MsgAskToSave, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return false;
            }

            if (DataModified)
            {
                XtraMessageBox.Show(Shared.IsEnglish ? ResEn.MsgAskToSave : ResAr.MsgAskToSave, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return false;
            }

            if (dt != null && (dt.GetChanges(DataRowState.Added) != null ||
                dt.GetChanges(DataRowState.Modified) != null ||
                dt.GetChanges(DataRowState.Deleted) != null))
            {
                XtraMessageBox.Show(Shared.IsEnglish ? ResEn.MsgAskToSave : ResAr.MsgAskToSave, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return false;
            }

            return true;
        }

        public static DateTime? GetExpireDate(object expire)
        {
            if (expire == DBNull.Value || expire == null)
                return null;
            else
            {
                DateTime temp = Convert.ToDateTime(expire);
                temp = temp.AddDays(-temp.Day + 1);
                return temp;
            }
        }

        public static string GetBatch(object batch)
        {
            if (batch == DBNull.Value || batch == null
                    || batch.ToString().Trim() == string.Empty)
                return null;
            else
                return batch.ToString();
        }

        public static int? GetMtrxVal(object val)
        {
            if (val == null || val == DBNull.Value || string.IsNullOrEmpty(val.ToString())
                || Convert.ToInt32(val) == 0)
                return null;
            else
                return Convert.ToInt32(val);
        }

        public static DataTable exceldata(string filelocation)
        {
            string sheetName = "Sheet1";
            string connStr = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + filelocation + ";Extended Properties=Excel 12.0;";

            OleDbConnection connection = new OleDbConnection(connStr);
            try
            {
                connection.Open();
                sheetName = GetExcelSheetNames(filelocation, connStr)[0];
            }
            catch
            {
                connStr = "Provider=Microsoft.Jet.OLEDB.4.0;Data Source=" + filelocation + ";Extended Properties='Excel 12.0;HDR=Yes;IMEX=1'";
                //  connStr = "Provider=Microsoft.Jet.OLEDB.4.0;Data Source=" + filelocation + ";Extended Properties='Excel 8.0;HDR=Yes'";

                connection.ConnectionString = connStr;
                sheetName = GetExcelSheetNames(filelocation, connStr)[0];
                try
                {
                    connection.Open();
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                    return null;
                }
            }

            OleDbCommand excelCommand = new OleDbCommand();
            excelCommand.Connection = connection;
            OleDbDataAdapter excelDataAdapter = new OleDbDataAdapter();
            DataTable dt_Emp_Attendance = new DataTable();
            excelCommand = new OleDbCommand("SELECT * FROM [" + sheetName + "]", connection);
            excelDataAdapter.SelectCommand = excelCommand;

            try
            {
                var nn = excelCommand.ExecuteNonQuery();
                excelDataAdapter.Fill(dt_Emp_Attendance);
                connection.Close();

            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                connection.Close();

                return null;
            }

            return dt_Emp_Attendance;
        }

        public static String[] GetExcelSheetNames(string filelocation, string connStr)
        {
            OleDbConnection objConn = null;
            System.Data.DataTable dt = null;
            try
            {
                objConn = new OleDbConnection(connStr);
                objConn.Open();
                dt = objConn.GetOleDbSchemaTable(OleDbSchemaGuid.Tables, null);
                if (dt == null)
                {
                    return null;
                }

                String[] excelSheets = new String[dt.Rows.Count];
                int i = 0;

                // Add the sheet name to the string array.
                foreach (DataRow row in dt.Rows)
                {
                    excelSheets[i] = row["TABLE_NAME"].ToString();
                    i++;
                }

                // Loop through all of the sheets if you want too...
                for (int j = 0; j < excelSheets.Length; j++)
                {
                    // Query each excel sheet.
                }

                return excelSheets;
            }
            catch
            {
                return null;
            }
            finally
            {
                // Clean up.
                if (objConn != null)
                {
                    objConn.Close();
                    objConn.Dispose();
                }
                if (dt != null)
                {
                    dt.Dispose();
                }
            }
        }

        public static void LoadInvItemsFromExcel(ref DataTable dtSL_Details, int invoiceId, Action<IC_Item, DataRow> loadItemRow, DAL.Process process)
        {
            OpenFileDialog ofd = new OpenFileDialog();
            DataTable dt = new DataTable();
            ofd.Filter = "Excel File(*.xls)|*.xls|Excel File(*.xlsx)|*.xlsx";
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    dt = ErpUtils.exceldata(ofd.FileName);
                    if (dt != null)
                    {
                        ERPDataContext DB = new ERPDataContext();
                        List<int> errorRowsLst = new List<int>();

                        for (int x = 0; x < dt.Rows.Count; x++)
                        {
                            //get item
                            IC_Item item = null;
                            string code1 = string.Empty;

                            #region Barcode_Init_Detail_PR

                            Detail_PR detail_PR = new Detail_PR
                            {
                                ItemId = (int)0,
                                Batch = "",
                                Expire = (DateTime?)DateTime.Now,
                                Length = (decimal?)1,
                                Width = (decimal?)1,
                                Height = (decimal?)1,
                                PiecesCount = (decimal)0,
                                PurchasePrice = (decimal)0,
                                TotalPurchasePrice = (decimal)0,
                                SellPrice = (decimal)0,
                                Qty = (decimal)0,
                                UOMId = (int)0,
                                UOMIndex = (byte)0,
                                VendorId = (int?)0
                            };
                            detail_PR = null;
                            #endregion

                            int barcodeTemplateCode = 0;
                            decimal barcodeTemplateQty = 0;
                            string barcodeBatch = string.Empty;

                            if (dt.Rows[x]["code"] != DBNull.Value && dt.Rows[x]["code"].ToString().Trim() != string.Empty)
                            {
                                code1 = dt.Rows[x]["code"].ToString().Trim();

                                item = MyHelper.SearchItem(DB, code1, detail_PR, ref barcodeTemplateCode, ref barcodeTemplateQty, ref barcodeBatch, invoiceId,
                                    Shared.st_Store);
                            }

                            if (item == null)
                            {
                                errorRowsLst.Add(x + 1);
                                continue;
                            }

                            #region get qty, pcs count, expire, batch, price
                            decimal qty = 0, pcsCount = 0, price = 0;
                            DateTime expire = DateTime.MinValue;
                            string batch = string.Empty, serial = string.Empty, serial2 = string.Empty;

                            if (Shared.st_Store.PrintBarcodePerInventory == false && barcodeTemplateCode > 0 && barcodeTemplateQty > 0)
                            {
                                qty = barcodeTemplateQty;
                                batch = barcodeBatch;
                                pcsCount = 1;
                            }
                            else
                            {
                                if (dt.Rows[x]["qty"] == DBNull.Value || decimal.TryParse(dt.Rows[x]["qty"].ToString(), out qty) == false)
                                {
                                    errorRowsLst.Add(x + 1);
                                    continue;
                                }

                                if (Shared.st_Store.PiecesCount && (dt.Rows[x]["PiecesCount"] == DBNull.Value || decimal.TryParse(dt.Rows[x]["PiecesCount"].ToString(), out pcsCount) == false))
                                {
                                    errorRowsLst.Add(x + 1);
                                    continue;
                                }

                                if (Shared.st_Store.Batch && dt.Rows[x]["batch"] != DBNull.Value && dt.Rows[x]["batch"].ToString().Trim() != string.Empty)
                                    batch = dt.Rows[x]["batch"].ToString().Trim();
                                if (Shared.st_Store.Serial && dt.Rows[x]["Serial"] != DBNull.Value && dt.Rows[x]["Serial"].ToString().Trim() != string.Empty)
                                    serial = dt.Rows[x]["Serial"].ToString().Trim();

                                if (Shared.st_Store.Serial && dt.Rows[x]["Serial2"] != DBNull.Value && dt.Rows[x]["Serial2"].ToString().Trim() != string.Empty)
                                    serial2 = dt.Rows[x]["Serial2"].ToString().Trim();

                            }

                            if (Shared.st_Store.ExpireDate && (dt.Rows[x]["Expire"] != DBNull.Value && dt.Rows[x]["Expire"].ToString().Trim() != string.Empty &&
                                 DateTime.TryParse(dt.Rows[x]["Expire"].ToString(), out expire) == true))
                            {
                                expire = Convert.ToDateTime(dt.Rows[x]["Expire"].ToString());
                            }

                            if (dt.Rows[x]["price"] != DBNull.Value && decimal.TryParse(dt.Rows[x]["price"].ToString(), out price) == false)
                            {
                                errorRowsLst.Add(x + 1);
                                continue;
                            }

                            #endregion

                            #region load item row
                            DataRow row = dtSL_Details.NewRow();
                            loadItemRow(item, row);

                            row["Qty"] = Convert.ToDouble(qty);
                            if (pcsCount > 0)
                                row["PiecesCount"] = pcsCount;
                            if (expire != DateTime.MinValue)
                            {
                                if (dtSL_Details.Columns.Contains("ExpireDate"))
                                    row["ExpireDate"] = expire;
                                row["Expire"] = expire;
                            }
                            if (batch != string.Empty)
                                row["Batch"] = batch;
                            if (serial != string.Empty)
                                row["Serial"] = serial;
                            if (serial2 != string.Empty)
                                row["Serial2"] = serial2;

                            if (process == DAL.Process.SellInvoice)
                            {
                                if (price > 0)
                                    row["SellPrice"] = price;
                                else
                                    row["SellPrice"] = item.SmallUOMPrice;

                                row["TotalSellPrice"] = decimal.ToDouble(Convert.ToDecimal(row["SellPrice"]) * qty);
                            }
                            else if (process == DAL.Process.OutTrns)
                            {
                                if (price > 0)
                                    row["SellPrice"] = price;
                                else
                                    row["SellPrice"] = item.SmallUOMPrice;

                                row["TotalSell"] = decimal.ToDouble(Convert.ToDecimal(row["SellPrice"]) * qty);
                            }
                            else if (process == DAL.Process.PurchaseInvoice || process == DAL.Process.InTrns)
                            {
                                if (price > 0)
                                    row["PurchasePrice"] = price;
                                else
                                    row["PurchasePrice"] = item.PurchasePrice;

                                row["TotalPurchasePrice"] = decimal.ToDouble(Convert.ToDecimal(row["PurchasePrice"]) * qty);
                            }

                            //update 6/9/2017
                            else if (process == DAL.Process.PurchaseReturn)
                            {
                                if (price > 0)
                                    row["PurchasePrice"] = price;
                                else
                                    row["PurchasePrice"] = item.PurchasePrice;

                                row["TotalPurchasePrice"] = decimal.ToDouble(Convert.ToDecimal(row["PurchasePrice"]) * qty);
                            }
                            else if (process == DAL.Process.SellReturn)
                            {
                                if (price > 0)
                                    row["SellPrice"] = price;
                                else
                                    row["SellPrice"] = item.PurchasePrice;

                                row["TotalSellPrice"] = decimal.ToDouble(Convert.ToDecimal(row["SellPrice"]) * qty);
                            }
                            #endregion

                            dtSL_Details.Rows.Add(row);
                        }

                        if (errorRowsLst.Count > 0)
                        {
                            string rows = string.Empty;
                            foreach (int s in errorRowsLst)
                                rows += " " + s.ToString();

                            MessageBox.Show("The following rows didn't load:" + rows);
                        }
                    }
                    else
                        return;
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                }
            }
            else
                return;
        }
    }
}