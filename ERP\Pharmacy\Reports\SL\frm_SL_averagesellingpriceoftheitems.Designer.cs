﻿namespace Reports
{
    partial class frm_SL_averagesellingpriceoftheitems
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_SL_averagesellingpriceoftheitems));
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtnPrint = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnPreview = new DevExpress.XtraBars.BarButtonItem();
            this.popupMenu1 = new DevExpress.XtraBars.PopupMenu(this.components);
            this.btn_Landscape = new DevExpress.XtraBars.BarButtonItem();
            this.btn_Portrait = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnRefresh = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.rep_MtrxParent = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.repositoryItemGridLookUpEdit1View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_mtrx = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView4 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.grdCategory = new DevExpress.XtraGrid.GridControl();
            this.bandedGridView1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridBand1 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_Qty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_PiecesCount = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_SUom = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_SmallQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_MUom = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_MediumQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_LUom = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_LargeQty = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand2 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.bandedGridColumn3 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.bandedGridColumn4 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_ItemId = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_CategoryNameAr = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.colStoreNameAr = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.colIndex = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_ItemCode1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_ItemCode2 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.rep_cat = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_comp = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_Vendor = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView5 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.picLogo = new DevExpress.XtraEditors.PictureEdit();
            this.lblReportName = new DevExpress.XtraEditors.TextEdit();
            this.lblDateFilter = new DevExpress.XtraEditors.TextEdit();
            this.lblFilter = new DevExpress.XtraEditors.TextEdit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenu1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_MtrxParent)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_mtrx)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdCategory)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_cat)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_comp)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Vendor)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblReportName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDateFilter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFilter.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnPreview,
            this.barBtnClose,
            this.barBtnPrint,
            this.barBtnRefresh,
            this.btn_Landscape,
            this.btn_Portrait});
            this.barManager1.MaxItemId = 31;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1,
            this.rep_MtrxParent,
            this.rep_mtrx});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(567, 147);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnPrint),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnPreview),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnRefresh),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtnPrint
            // 
            resources.ApplyResources(this.barBtnPrint, "barBtnPrint");
            this.barBtnPrint.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnPrint.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barBtnPrint.Id = 27;
            this.barBtnPrint.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.P));
            this.barBtnPrint.Name = "barBtnPrint";
            this.barBtnPrint.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnPrint.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnPrint_ItemClick);
            // 
            // barBtnPreview
            // 
            resources.ApplyResources(this.barBtnPreview, "barBtnPreview");
            this.barBtnPreview.ActAsDropDown = true;
            this.barBtnPreview.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnPreview.ButtonStyle = DevExpress.XtraBars.BarButtonStyle.DropDown;
            this.barBtnPreview.DropDownControl = this.popupMenu1;
            this.barBtnPreview.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barBtnPreview.Id = 1;
            this.barBtnPreview.Name = "barBtnPreview";
            this.barBtnPreview.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            // 
            // popupMenu1
            // 
            this.popupMenu1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.btn_Landscape),
            new DevExpress.XtraBars.LinkPersistInfo(this.btn_Portrait)});
            this.popupMenu1.Manager = this.barManager1;
            this.popupMenu1.MenuAppearance.HeaderItemAppearance.FontSizeDelta = ((int)(resources.GetObject("popupMenu1.MenuAppearance.HeaderItemAppearance.FontSizeDelta")));
            this.popupMenu1.MenuAppearance.HeaderItemAppearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("popupMenu1.MenuAppearance.HeaderItemAppearance.FontStyleDelta")));
            this.popupMenu1.MenuAppearance.HeaderItemAppearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("popupMenu1.MenuAppearance.HeaderItemAppearance.GradientMode")));
            this.popupMenu1.MenuAppearance.HeaderItemAppearance.Image = ((System.Drawing.Image)(resources.GetObject("popupMenu1.MenuAppearance.HeaderItemAppearance.Image")));
            this.popupMenu1.Name = "popupMenu1";
            // 
            // btn_Landscape
            // 
            resources.ApplyResources(this.btn_Landscape, "btn_Landscape");
            this.btn_Landscape.Id = 29;
            this.btn_Landscape.Name = "btn_Landscape";
            this.btn_Landscape.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btn_Landscape_ItemClick);
            // 
            // btn_Portrait
            // 
            resources.ApplyResources(this.btn_Portrait, "btn_Portrait");
            this.btn_Portrait.Id = 30;
            this.btn_Portrait.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.O));
            this.btn_Portrait.Name = "btn_Portrait";
            this.btn_Portrait.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btn_Portrait_ItemClick);
            // 
            // barBtnRefresh
            // 
            resources.ApplyResources(this.barBtnRefresh, "barBtnRefresh");
            this.barBtnRefresh.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnRefresh.Glyph = global::Pharmacy.Properties.Resources.refresh;
            this.barBtnRefresh.Id = 28;
            this.barBtnRefresh.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.R));
            this.barBtnRefresh.Name = "barBtnRefresh";
            this.barBtnRefresh.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnRefresh.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnRefresh_ItemClick);
            // 
            // barBtnClose
            // 
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 25;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Close_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlTop.Appearance.FontSizeDelta")));
            this.barDockControlTop.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlTop.Appearance.FontStyleDelta")));
            this.barDockControlTop.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlTop.Appearance.GradientMode")));
            this.barDockControlTop.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlTop.Appearance.Image")));
            this.barDockControlTop.CausesValidation = false;
            // 
            // barDockControlBottom
            // 
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlBottom.Appearance.FontSizeDelta")));
            this.barDockControlBottom.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlBottom.Appearance.FontStyleDelta")));
            this.barDockControlBottom.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlBottom.Appearance.GradientMode")));
            this.barDockControlBottom.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlBottom.Appearance.Image")));
            this.barDockControlBottom.CausesValidation = false;
            // 
            // barDockControlLeft
            // 
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlLeft.Appearance.FontSizeDelta")));
            this.barDockControlLeft.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlLeft.Appearance.FontStyleDelta")));
            this.barDockControlLeft.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlLeft.Appearance.GradientMode")));
            this.barDockControlLeft.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlLeft.Appearance.Image")));
            this.barDockControlLeft.CausesValidation = false;
            // 
            // barDockControlRight
            // 
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlRight.Appearance.FontSizeDelta")));
            this.barDockControlRight.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlRight.Appearance.FontStyleDelta")));
            this.barDockControlRight.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlRight.Appearance.GradientMode")));
            this.barDockControlRight.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlRight.Appearance.Image")));
            this.barDockControlRight.CausesValidation = false;
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemTextEdit1.Mask.AutoComplete")));
            this.repositoryItemTextEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.BeepOnError")));
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemTextEdit1.Mask.MaskType")));
            this.repositoryItemTextEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemTextEdit1.Mask.PlaceHolder")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // rep_MtrxParent
            // 
            resources.ApplyResources(this.rep_MtrxParent, "rep_MtrxParent");
            this.rep_MtrxParent.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_MtrxParent.Buttons"))))});
            this.rep_MtrxParent.Name = "rep_MtrxParent";
            this.rep_MtrxParent.View = this.repositoryItemGridLookUpEdit1View;
            // 
            // repositoryItemGridLookUpEdit1View
            // 
            resources.ApplyResources(this.repositoryItemGridLookUpEdit1View, "repositoryItemGridLookUpEdit1View");
            this.repositoryItemGridLookUpEdit1View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2});
            this.repositoryItemGridLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.repositoryItemGridLookUpEdit1View.Name = "repositoryItemGridLookUpEdit1View";
            this.repositoryItemGridLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            resources.ApplyResources(this.gridColumn1, "gridColumn1");
            this.gridColumn1.FieldName = "ItemNameAr";
            this.gridColumn1.Name = "gridColumn1";
            // 
            // gridColumn2
            // 
            resources.ApplyResources(this.gridColumn2, "gridColumn2");
            this.gridColumn2.FieldName = "ItemId";
            this.gridColumn2.Name = "gridColumn2";
            // 
            // rep_mtrx
            // 
            resources.ApplyResources(this.rep_mtrx, "rep_mtrx");
            this.rep_mtrx.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_mtrx.Buttons"))))});
            this.rep_mtrx.Name = "rep_mtrx";
            this.rep_mtrx.ShowDropDown = DevExpress.XtraEditors.Controls.ShowDropDown.Never;
            this.rep_mtrx.View = this.gridView4;
            // 
            // gridView4
            // 
            resources.ApplyResources(this.gridView4, "gridView4");
            this.gridView4.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn7,
            this.gridColumn8});
            this.gridView4.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView4.Name = "gridView4";
            this.gridView4.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView4.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn7
            // 
            resources.ApplyResources(this.gridColumn7, "gridColumn7");
            this.gridColumn7.FieldName = "MDName";
            this.gridColumn7.Name = "gridColumn7";
            // 
            // gridColumn8
            // 
            resources.ApplyResources(this.gridColumn8, "gridColumn8");
            this.gridColumn8.FieldName = "MatrixDetailId";
            this.gridColumn8.Name = "gridColumn8";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // grdCategory
            // 
            resources.ApplyResources(this.grdCategory, "grdCategory");
            this.grdCategory.Cursor = System.Windows.Forms.Cursors.Default;
            this.grdCategory.EmbeddedNavigator.AccessibleDescription = resources.GetString("grdCategory.EmbeddedNavigator.AccessibleDescription");
            this.grdCategory.EmbeddedNavigator.AccessibleName = resources.GetString("grdCategory.EmbeddedNavigator.AccessibleName");
            this.grdCategory.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grdCategory.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grdCategory.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grdCategory.EmbeddedNavigator.Anchor")));
            this.grdCategory.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grdCategory.EmbeddedNavigator.BackgroundImage")));
            this.grdCategory.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grdCategory.EmbeddedNavigator.BackgroundImageLayout")));
            this.grdCategory.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grdCategory.EmbeddedNavigator.ImeMode")));
            this.grdCategory.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grdCategory.EmbeddedNavigator.MaximumSize")));
            this.grdCategory.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grdCategory.EmbeddedNavigator.TextLocation")));
            this.grdCategory.EmbeddedNavigator.ToolTip = resources.GetString("grdCategory.EmbeddedNavigator.ToolTip");
            this.grdCategory.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grdCategory.EmbeddedNavigator.ToolTipIconType")));
            this.grdCategory.EmbeddedNavigator.ToolTipTitle = resources.GetString("grdCategory.EmbeddedNavigator.ToolTipTitle");
            this.grdCategory.MainView = this.bandedGridView1;
            this.grdCategory.Name = "grdCategory";
            this.grdCategory.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.rep_MtrxParent,
            this.rep_cat,
            this.rep_comp,
            this.rep_mtrx,
            this.rep_Vendor});
            this.grdCategory.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView1});
            // 
            // bandedGridView1
            // 
            this.bandedGridView1.Appearance.GroupPanel.FontSizeDelta = ((int)(resources.GetObject("bandedGridView1.Appearance.GroupPanel.FontSizeDelta")));
            this.bandedGridView1.Appearance.GroupPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("bandedGridView1.Appearance.GroupPanel.FontStyleDelta")));
            this.bandedGridView1.Appearance.GroupPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("bandedGridView1.Appearance.GroupPanel.GradientMode")));
            this.bandedGridView1.Appearance.GroupPanel.Image = ((System.Drawing.Image)(resources.GetObject("bandedGridView1.Appearance.GroupPanel.Image")));
            this.bandedGridView1.Appearance.GroupPanel.Options.UseTextOptions = true;
            this.bandedGridView1.Appearance.GroupPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bandedGridView1.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("bandedGridView1.Appearance.HeaderPanel.FontSizeDelta")));
            this.bandedGridView1.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("bandedGridView1.Appearance.HeaderPanel.FontStyleDelta")));
            this.bandedGridView1.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("bandedGridView1.Appearance.HeaderPanel.GradientMode")));
            this.bandedGridView1.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("bandedGridView1.Appearance.HeaderPanel.Image")));
            this.bandedGridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.bandedGridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bandedGridView1.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.bandedGridView1.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("bandedGridView1.Appearance.Row.FontSizeDelta")));
            this.bandedGridView1.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("bandedGridView1.Appearance.Row.FontStyleDelta")));
            this.bandedGridView1.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("bandedGridView1.Appearance.Row.GradientMode")));
            this.bandedGridView1.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("bandedGridView1.Appearance.Row.Image")));
            this.bandedGridView1.Appearance.Row.Options.UseTextOptions = true;
            this.bandedGridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bandedGridView1.AppearancePrint.BandPanel.BackColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.BandPanel.BackColor")));
            this.bandedGridView1.AppearancePrint.BandPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.BandPanel.BorderColor")));
            this.bandedGridView1.AppearancePrint.BandPanel.Font = ((System.Drawing.Font)(resources.GetObject("bandedGridView1.AppearancePrint.BandPanel.Font")));
            this.bandedGridView1.AppearancePrint.BandPanel.FontSizeDelta = ((int)(resources.GetObject("bandedGridView1.AppearancePrint.BandPanel.FontSizeDelta")));
            this.bandedGridView1.AppearancePrint.BandPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("bandedGridView1.AppearancePrint.BandPanel.FontStyleDelta")));
            this.bandedGridView1.AppearancePrint.BandPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.BandPanel.ForeColor")));
            this.bandedGridView1.AppearancePrint.BandPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("bandedGridView1.AppearancePrint.BandPanel.GradientMode")));
            this.bandedGridView1.AppearancePrint.BandPanel.Image = ((System.Drawing.Image)(resources.GetObject("bandedGridView1.AppearancePrint.BandPanel.Image")));
            this.bandedGridView1.AppearancePrint.BandPanel.Options.UseBackColor = true;
            this.bandedGridView1.AppearancePrint.BandPanel.Options.UseBorderColor = true;
            this.bandedGridView1.AppearancePrint.BandPanel.Options.UseFont = true;
            this.bandedGridView1.AppearancePrint.BandPanel.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.BandPanel.Options.UseTextOptions = true;
            this.bandedGridView1.AppearancePrint.BandPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bandedGridView1.AppearancePrint.BandPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.bandedGridView1.AppearancePrint.FooterPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.FooterPanel.BorderColor")));
            this.bandedGridView1.AppearancePrint.FooterPanel.Font = ((System.Drawing.Font)(resources.GetObject("bandedGridView1.AppearancePrint.FooterPanel.Font")));
            this.bandedGridView1.AppearancePrint.FooterPanel.FontSizeDelta = ((int)(resources.GetObject("bandedGridView1.AppearancePrint.FooterPanel.FontSizeDelta")));
            this.bandedGridView1.AppearancePrint.FooterPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("bandedGridView1.AppearancePrint.FooterPanel.FontStyleDelta")));
            this.bandedGridView1.AppearancePrint.FooterPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.FooterPanel.ForeColor")));
            this.bandedGridView1.AppearancePrint.FooterPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("bandedGridView1.AppearancePrint.FooterPanel.GradientMode")));
            this.bandedGridView1.AppearancePrint.FooterPanel.Image = ((System.Drawing.Image)(resources.GetObject("bandedGridView1.AppearancePrint.FooterPanel.Image")));
            this.bandedGridView1.AppearancePrint.FooterPanel.Options.UseBorderColor = true;
            this.bandedGridView1.AppearancePrint.FooterPanel.Options.UseFont = true;
            this.bandedGridView1.AppearancePrint.FooterPanel.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.GroupFooter.BorderColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.GroupFooter.BorderColor")));
            this.bandedGridView1.AppearancePrint.GroupFooter.FontSizeDelta = ((int)(resources.GetObject("bandedGridView1.AppearancePrint.GroupFooter.FontSizeDelta")));
            this.bandedGridView1.AppearancePrint.GroupFooter.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("bandedGridView1.AppearancePrint.GroupFooter.FontStyleDelta")));
            this.bandedGridView1.AppearancePrint.GroupFooter.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.GroupFooter.ForeColor")));
            this.bandedGridView1.AppearancePrint.GroupFooter.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("bandedGridView1.AppearancePrint.GroupFooter.GradientMode")));
            this.bandedGridView1.AppearancePrint.GroupFooter.Image = ((System.Drawing.Image)(resources.GetObject("bandedGridView1.AppearancePrint.GroupFooter.Image")));
            this.bandedGridView1.AppearancePrint.GroupFooter.Options.UseBorderColor = true;
            this.bandedGridView1.AppearancePrint.GroupFooter.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.GroupRow.BorderColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.GroupRow.BorderColor")));
            this.bandedGridView1.AppearancePrint.GroupRow.FontSizeDelta = ((int)(resources.GetObject("bandedGridView1.AppearancePrint.GroupRow.FontSizeDelta")));
            this.bandedGridView1.AppearancePrint.GroupRow.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("bandedGridView1.AppearancePrint.GroupRow.FontStyleDelta")));
            this.bandedGridView1.AppearancePrint.GroupRow.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.GroupRow.ForeColor")));
            this.bandedGridView1.AppearancePrint.GroupRow.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("bandedGridView1.AppearancePrint.GroupRow.GradientMode")));
            this.bandedGridView1.AppearancePrint.GroupRow.Image = ((System.Drawing.Image)(resources.GetObject("bandedGridView1.AppearancePrint.GroupRow.Image")));
            this.bandedGridView1.AppearancePrint.GroupRow.Options.UseBorderColor = true;
            this.bandedGridView1.AppearancePrint.GroupRow.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.HeaderPanel.BackColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.HeaderPanel.BackColor")));
            this.bandedGridView1.AppearancePrint.HeaderPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.HeaderPanel.BorderColor")));
            this.bandedGridView1.AppearancePrint.HeaderPanel.Font = ((System.Drawing.Font)(resources.GetObject("bandedGridView1.AppearancePrint.HeaderPanel.Font")));
            this.bandedGridView1.AppearancePrint.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("bandedGridView1.AppearancePrint.HeaderPanel.FontSizeDelta")));
            this.bandedGridView1.AppearancePrint.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("bandedGridView1.AppearancePrint.HeaderPanel.FontStyleDelta")));
            this.bandedGridView1.AppearancePrint.HeaderPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.HeaderPanel.ForeColor")));
            this.bandedGridView1.AppearancePrint.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("bandedGridView1.AppearancePrint.HeaderPanel.GradientMode")));
            this.bandedGridView1.AppearancePrint.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("bandedGridView1.AppearancePrint.HeaderPanel.Image")));
            this.bandedGridView1.AppearancePrint.HeaderPanel.Options.UseBackColor = true;
            this.bandedGridView1.AppearancePrint.HeaderPanel.Options.UseBorderColor = true;
            this.bandedGridView1.AppearancePrint.HeaderPanel.Options.UseFont = true;
            this.bandedGridView1.AppearancePrint.HeaderPanel.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.HeaderPanel.Options.UseTextOptions = true;
            this.bandedGridView1.AppearancePrint.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bandedGridView1.AppearancePrint.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.bandedGridView1.AppearancePrint.Lines.BackColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.Lines.BackColor")));
            this.bandedGridView1.AppearancePrint.Lines.FontSizeDelta = ((int)(resources.GetObject("bandedGridView1.AppearancePrint.Lines.FontSizeDelta")));
            this.bandedGridView1.AppearancePrint.Lines.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("bandedGridView1.AppearancePrint.Lines.FontStyleDelta")));
            this.bandedGridView1.AppearancePrint.Lines.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.Lines.ForeColor")));
            this.bandedGridView1.AppearancePrint.Lines.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("bandedGridView1.AppearancePrint.Lines.GradientMode")));
            this.bandedGridView1.AppearancePrint.Lines.Image = ((System.Drawing.Image)(resources.GetObject("bandedGridView1.AppearancePrint.Lines.Image")));
            this.bandedGridView1.AppearancePrint.Lines.Options.UseBackColor = true;
            this.bandedGridView1.AppearancePrint.Lines.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.Row.BorderColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.Row.BorderColor")));
            this.bandedGridView1.AppearancePrint.Row.Font = ((System.Drawing.Font)(resources.GetObject("bandedGridView1.AppearancePrint.Row.Font")));
            this.bandedGridView1.AppearancePrint.Row.FontSizeDelta = ((int)(resources.GetObject("bandedGridView1.AppearancePrint.Row.FontSizeDelta")));
            this.bandedGridView1.AppearancePrint.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("bandedGridView1.AppearancePrint.Row.FontStyleDelta")));
            this.bandedGridView1.AppearancePrint.Row.ForeColor = ((System.Drawing.Color)(resources.GetObject("bandedGridView1.AppearancePrint.Row.ForeColor")));
            this.bandedGridView1.AppearancePrint.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("bandedGridView1.AppearancePrint.Row.GradientMode")));
            this.bandedGridView1.AppearancePrint.Row.Image = ((System.Drawing.Image)(resources.GetObject("bandedGridView1.AppearancePrint.Row.Image")));
            this.bandedGridView1.AppearancePrint.Row.Options.UseBorderColor = true;
            this.bandedGridView1.AppearancePrint.Row.Options.UseFont = true;
            this.bandedGridView1.AppearancePrint.Row.Options.UseForeColor = true;
            this.bandedGridView1.AppearancePrint.Row.Options.UseTextOptions = true;
            this.bandedGridView1.AppearancePrint.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bandedGridView1.BandPanelRowHeight = 35;
            this.bandedGridView1.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand1,
            this.gridBand2});
            resources.ApplyResources(this.bandedGridView1, "bandedGridView1");
            this.bandedGridView1.ColumnPanelRowHeight = 45;
            this.bandedGridView1.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.bandedGridColumn1,
            this.bandedGridColumn2,
            this.col_Qty,
            this.col_LargeQty,
            this.col_MediumQty,
            this.col_SmallQty,
            this.col_ItemId,
            this.colIndex,
            this.col_SUom,
            this.col_MUom,
            this.col_LUom,
            this.col_PiecesCount,
            this.col_ItemCode1,
            this.col_ItemCode2,
            this.col_CategoryNameAr,
            this.colStoreNameAr,
            this.bandedGridColumn3,
            this.bandedGridColumn4});
            this.bandedGridView1.CustomizationFormBounds = new System.Drawing.Rectangle(946, 293, 216, 383);
            this.bandedGridView1.GridControl = this.grdCategory;
            this.bandedGridView1.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("bandedGridView1.GroupSummary"))), resources.GetString("bandedGridView1.GroupSummary1"), this.col_LargeQty, resources.GetString("bandedGridView1.GroupSummary2")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("bandedGridView1.GroupSummary3"))), resources.GetString("bandedGridView1.GroupSummary4"), this.col_MediumQty, resources.GetString("bandedGridView1.GroupSummary5")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("bandedGridView1.GroupSummary6"))), resources.GetString("bandedGridView1.GroupSummary7"), this.col_SmallQty, resources.GetString("bandedGridView1.GroupSummary8")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("bandedGridView1.GroupSummary9"))), resources.GetString("bandedGridView1.GroupSummary10"), ((DevExpress.XtraGrid.Columns.GridColumn)(resources.GetObject("bandedGridView1.GroupSummary11"))), resources.GetString("bandedGridView1.GroupSummary12")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("bandedGridView1.GroupSummary13"))), resources.GetString("bandedGridView1.GroupSummary14"), this.col_PiecesCount, resources.GetString("bandedGridView1.GroupSummary15")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("bandedGridView1.GroupSummary16"))), resources.GetString("bandedGridView1.GroupSummary17"), ((DevExpress.XtraGrid.Columns.GridColumn)(resources.GetObject("bandedGridView1.GroupSummary18"))), resources.GetString("bandedGridView1.GroupSummary19"))});
            this.bandedGridView1.Name = "bandedGridView1";
            this.bandedGridView1.OptionsBehavior.AutoExpandAllGroups = true;
            this.bandedGridView1.OptionsBehavior.Editable = false;
            this.bandedGridView1.OptionsBehavior.ReadOnly = true;
            this.bandedGridView1.OptionsCustomization.AllowChangeColumnParent = true;
            this.bandedGridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.bandedGridView1.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.bandedGridView1.OptionsView.EnableAppearanceOddRow = true;
            this.bandedGridView1.OptionsView.GroupFooterShowMode = DevExpress.XtraGrid.Views.Grid.GroupFooterShowMode.VisibleAlways;
            this.bandedGridView1.OptionsView.RowAutoHeight = true;
            this.bandedGridView1.OptionsView.ShowAutoFilterRow = true;
            this.bandedGridView1.OptionsView.ShowFooter = true;
            this.bandedGridView1.OptionsView.ShowIndicator = false;
            this.bandedGridView1.CustomUnboundColumnData += new DevExpress.XtraGrid.Views.Base.CustomColumnDataEventHandler(this.gridView1_CustomUnboundColumnData);
            // 
            // gridBand1
            // 
            this.gridBand1.AppearanceHeader.Font = ((System.Drawing.Font)(resources.GetObject("gridBand1.AppearanceHeader.Font")));
            this.gridBand1.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridBand1.AppearanceHeader.FontSizeDelta")));
            this.gridBand1.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridBand1.AppearanceHeader.FontStyleDelta")));
            this.gridBand1.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridBand1.AppearanceHeader.GradientMode")));
            this.gridBand1.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridBand1.AppearanceHeader.Image")));
            this.gridBand1.AppearanceHeader.Options.UseFont = true;
            this.gridBand1.AppearanceHeader.Options.UseTextOptions = true;
            this.gridBand1.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.gridBand1, "gridBand1");
            this.gridBand1.Columns.Add(this.bandedGridColumn1);
            this.gridBand1.Columns.Add(this.bandedGridColumn2);
            this.gridBand1.Columns.Add(this.col_Qty);
            this.gridBand1.Columns.Add(this.col_PiecesCount);
            this.gridBand1.Columns.Add(this.col_SUom);
            this.gridBand1.Columns.Add(this.col_SmallQty);
            this.gridBand1.Columns.Add(this.col_MUom);
            this.gridBand1.Columns.Add(this.col_MediumQty);
            this.gridBand1.Columns.Add(this.col_LUom);
            this.gridBand1.Columns.Add(this.col_LargeQty);
            this.gridBand1.VisibleIndex = 0;
            // 
            // bandedGridColumn1
            // 
            resources.ApplyResources(this.bandedGridColumn1, "bandedGridColumn1");
            this.bandedGridColumn1.FieldName = "LargeUOMFactor";
            this.bandedGridColumn1.Name = "bandedGridColumn1";
            this.bandedGridColumn1.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // bandedGridColumn2
            // 
            resources.ApplyResources(this.bandedGridColumn2, "bandedGridColumn2");
            this.bandedGridColumn2.FieldName = "MediumUOMFactor";
            this.bandedGridColumn2.Name = "bandedGridColumn2";
            this.bandedGridColumn2.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // col_Qty
            // 
            resources.ApplyResources(this.col_Qty, "col_Qty");
            this.col_Qty.FieldName = "Qty";
            this.col_Qty.Name = "col_Qty";
            this.col_Qty.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_Qty.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_Qty.Summary"))), resources.GetString("col_Qty.Summary1"), resources.GetString("col_Qty.Summary2"))});
            // 
            // col_PiecesCount
            // 
            resources.ApplyResources(this.col_PiecesCount, "col_PiecesCount");
            this.col_PiecesCount.DisplayFormat.FormatString = "n2";
            this.col_PiecesCount.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_PiecesCount.FieldName = "PiecesCount";
            this.col_PiecesCount.Name = "col_PiecesCount";
            this.col_PiecesCount.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_PiecesCount.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_PiecesCount.Summary"))), resources.GetString("col_PiecesCount.Summary1"), resources.GetString("col_PiecesCount.Summary2"))});
            // 
            // col_SUom
            // 
            resources.ApplyResources(this.col_SUom, "col_SUom");
            this.col_SUom.FieldName = "SUom";
            this.col_SUom.Name = "col_SUom";
            this.col_SUom.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // col_SmallQty
            // 
            resources.ApplyResources(this.col_SmallQty, "col_SmallQty");
            this.col_SmallQty.DisplayFormat.FormatString = "n3";
            this.col_SmallQty.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_SmallQty.FieldName = "SmallQty";
            this.col_SmallQty.Name = "col_SmallQty";
            this.col_SmallQty.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col_SmallQty.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_SmallQty.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_SmallQty.Summary"))), resources.GetString("col_SmallQty.Summary1"), resources.GetString("col_SmallQty.Summary2"))});
            this.col_SmallQty.UnboundType = DevExpress.Data.UnboundColumnType.Object;
            // 
            // col_MUom
            // 
            resources.ApplyResources(this.col_MUom, "col_MUom");
            this.col_MUom.FieldName = "MUom";
            this.col_MUom.Name = "col_MUom";
            this.col_MUom.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // col_MediumQty
            // 
            resources.ApplyResources(this.col_MediumQty, "col_MediumQty");
            this.col_MediumQty.DisplayFormat.FormatString = "n3";
            this.col_MediumQty.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_MediumQty.FieldName = "MediumQty";
            this.col_MediumQty.Name = "col_MediumQty";
            this.col_MediumQty.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_MediumQty.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_MediumQty.Summary"))), resources.GetString("col_MediumQty.Summary1"), resources.GetString("col_MediumQty.Summary2"))});
            this.col_MediumQty.UnboundType = DevExpress.Data.UnboundColumnType.Object;
            // 
            // col_LUom
            // 
            resources.ApplyResources(this.col_LUom, "col_LUom");
            this.col_LUom.FieldName = "LUom";
            this.col_LUom.Name = "col_LUom";
            this.col_LUom.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // col_LargeQty
            // 
            resources.ApplyResources(this.col_LargeQty, "col_LargeQty");
            this.col_LargeQty.DisplayFormat.FormatString = "n3";
            this.col_LargeQty.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_LargeQty.FieldName = "LargeQty";
            this.col_LargeQty.Name = "col_LargeQty";
            this.col_LargeQty.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_LargeQty.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_LargeQty.Summary"))), resources.GetString("col_LargeQty.Summary1"), resources.GetString("col_LargeQty.Summary2"))});
            this.col_LargeQty.UnboundType = DevExpress.Data.UnboundColumnType.Object;
            // 
            // gridBand2
            // 
            resources.ApplyResources(this.gridBand2, "gridBand2");
            this.gridBand2.Columns.Add(this.bandedGridColumn3);
            this.gridBand2.Columns.Add(this.bandedGridColumn4);
            this.gridBand2.Columns.Add(this.col_ItemId);
            this.gridBand2.Columns.Add(this.col_CategoryNameAr);
            this.gridBand2.Columns.Add(this.colStoreNameAr);
            this.gridBand2.Columns.Add(this.colIndex);
            this.gridBand2.VisibleIndex = 1;
            // 
            // bandedGridColumn3
            // 
            resources.ApplyResources(this.bandedGridColumn3, "bandedGridColumn3");
            this.bandedGridColumn3.DisplayFormat.FormatString = "n2";
            this.bandedGridColumn3.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.bandedGridColumn3.FieldName = "ItemSellPrice";
            this.bandedGridColumn3.Name = "bandedGridColumn3";
            // 
            // bandedGridColumn4
            // 
            resources.ApplyResources(this.bandedGridColumn4, "bandedGridColumn4");
            this.bandedGridColumn4.FieldName = "SmallUOMPrice";
            this.bandedGridColumn4.Name = "bandedGridColumn4";
            // 
            // col_ItemId
            // 
            resources.ApplyResources(this.col_ItemId, "col_ItemId");
            this.col_ItemId.FieldName = "ItemNameAr";
            this.col_ItemId.Name = "col_ItemId";
            this.col_ItemId.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col_ItemId.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            this.col_ItemId.OptionsFilter.FilterPopupMode = DevExpress.XtraGrid.Columns.FilterPopupMode.CheckedList;
            // 
            // col_CategoryNameAr
            // 
            resources.ApplyResources(this.col_CategoryNameAr, "col_CategoryNameAr");
            this.col_CategoryNameAr.FieldName = "CategoryNameAr";
            this.col_CategoryNameAr.Name = "col_CategoryNameAr";
            this.col_CategoryNameAr.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            this.col_CategoryNameAr.OptionsFilter.FilterPopupMode = DevExpress.XtraGrid.Columns.FilterPopupMode.CheckedList;
            // 
            // colStoreNameAr
            // 
            resources.ApplyResources(this.colStoreNameAr, "colStoreNameAr");
            this.colStoreNameAr.FieldName = "StoreNameAr";
            this.colStoreNameAr.Name = "colStoreNameAr";
            this.colStoreNameAr.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            this.colStoreNameAr.OptionsFilter.FilterPopupMode = DevExpress.XtraGrid.Columns.FilterPopupMode.CheckedList;
            // 
            // colIndex
            // 
            resources.ApplyResources(this.colIndex, "colIndex");
            this.colIndex.FieldName = "colIndex";
            this.colIndex.Name = "colIndex";
            this.colIndex.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.colIndex.UnboundType = DevExpress.Data.UnboundColumnType.Integer;
            // 
            // col_ItemCode1
            // 
            this.col_ItemCode1.AppearanceCell.Font = ((System.Drawing.Font)(resources.GetObject("col_ItemCode1.AppearanceCell.Font")));
            this.col_ItemCode1.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_ItemCode1.AppearanceCell.FontSizeDelta")));
            this.col_ItemCode1.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_ItemCode1.AppearanceCell.FontStyleDelta")));
            this.col_ItemCode1.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_ItemCode1.AppearanceCell.GradientMode")));
            this.col_ItemCode1.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_ItemCode1.AppearanceCell.Image")));
            this.col_ItemCode1.AppearanceCell.Options.UseFont = true;
            this.col_ItemCode1.AppearanceCell.Options.UseTextOptions = true;
            this.col_ItemCode1.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_ItemCode1.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_ItemCode1.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_ItemCode1.AppearanceHeader.Font = ((System.Drawing.Font)(resources.GetObject("col_ItemCode1.AppearanceHeader.Font")));
            this.col_ItemCode1.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_ItemCode1.AppearanceHeader.FontSizeDelta")));
            this.col_ItemCode1.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_ItemCode1.AppearanceHeader.FontStyleDelta")));
            this.col_ItemCode1.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_ItemCode1.AppearanceHeader.GradientMode")));
            this.col_ItemCode1.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_ItemCode1.AppearanceHeader.Image")));
            this.col_ItemCode1.AppearanceHeader.Options.UseFont = true;
            this.col_ItemCode1.AppearanceHeader.Options.UseTextOptions = true;
            this.col_ItemCode1.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_ItemCode1.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_ItemCode1.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_ItemCode1, "col_ItemCode1");
            this.col_ItemCode1.FieldName = "ItemCode1";
            this.col_ItemCode1.Name = "col_ItemCode1";
            // 
            // col_ItemCode2
            // 
            this.col_ItemCode2.AppearanceCell.Font = ((System.Drawing.Font)(resources.GetObject("col_ItemCode2.AppearanceCell.Font")));
            this.col_ItemCode2.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_ItemCode2.AppearanceCell.FontSizeDelta")));
            this.col_ItemCode2.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_ItemCode2.AppearanceCell.FontStyleDelta")));
            this.col_ItemCode2.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_ItemCode2.AppearanceCell.GradientMode")));
            this.col_ItemCode2.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_ItemCode2.AppearanceCell.Image")));
            this.col_ItemCode2.AppearanceCell.Options.UseFont = true;
            this.col_ItemCode2.AppearanceCell.Options.UseTextOptions = true;
            this.col_ItemCode2.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_ItemCode2.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_ItemCode2.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_ItemCode2.AppearanceHeader.Font = ((System.Drawing.Font)(resources.GetObject("col_ItemCode2.AppearanceHeader.Font")));
            this.col_ItemCode2.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_ItemCode2.AppearanceHeader.FontSizeDelta")));
            this.col_ItemCode2.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_ItemCode2.AppearanceHeader.FontStyleDelta")));
            this.col_ItemCode2.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_ItemCode2.AppearanceHeader.GradientMode")));
            this.col_ItemCode2.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_ItemCode2.AppearanceHeader.Image")));
            this.col_ItemCode2.AppearanceHeader.Options.UseFont = true;
            this.col_ItemCode2.AppearanceHeader.Options.UseTextOptions = true;
            this.col_ItemCode2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_ItemCode2.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_ItemCode2.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_ItemCode2, "col_ItemCode2");
            this.col_ItemCode2.FieldName = "ItemCode2";
            this.col_ItemCode2.Name = "col_ItemCode2";
            // 
            // rep_cat
            // 
            resources.ApplyResources(this.rep_cat, "rep_cat");
            this.rep_cat.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_cat.Buttons"))))});
            this.rep_cat.Name = "rep_cat";
            this.rep_cat.View = this.gridView2;
            // 
            // gridView2
            // 
            resources.ApplyResources(this.gridView2, "gridView2");
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn3,
            this.gridColumn4});
            this.gridView2.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn3
            // 
            resources.ApplyResources(this.gridColumn3, "gridColumn3");
            this.gridColumn3.FieldName = "CategoryNameAr";
            this.gridColumn3.Name = "gridColumn3";
            // 
            // gridColumn4
            // 
            resources.ApplyResources(this.gridColumn4, "gridColumn4");
            this.gridColumn4.FieldName = "CategoryId";
            this.gridColumn4.Name = "gridColumn4";
            // 
            // rep_comp
            // 
            resources.ApplyResources(this.rep_comp, "rep_comp");
            this.rep_comp.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_comp.Buttons"))))});
            this.rep_comp.Name = "rep_comp";
            this.rep_comp.View = this.gridView3;
            // 
            // gridView3
            // 
            resources.ApplyResources(this.gridView3, "gridView3");
            this.gridView3.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn5,
            this.gridColumn6});
            this.gridView3.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn5
            // 
            resources.ApplyResources(this.gridColumn5, "gridColumn5");
            this.gridColumn5.FieldName = "CompanyNameAr";
            this.gridColumn5.Name = "gridColumn5";
            // 
            // gridColumn6
            // 
            resources.ApplyResources(this.gridColumn6, "gridColumn6");
            this.gridColumn6.FieldName = "CompanyId";
            this.gridColumn6.Name = "gridColumn6";
            // 
            // rep_Vendor
            // 
            resources.ApplyResources(this.rep_Vendor, "rep_Vendor");
            this.rep_Vendor.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Vendor.Buttons"))))});
            this.rep_Vendor.Name = "rep_Vendor";
            this.rep_Vendor.View = this.gridView5;
            // 
            // gridView5
            // 
            resources.ApplyResources(this.gridView5, "gridView5");
            this.gridView5.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn9,
            this.gridColumn10});
            this.gridView5.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView5.Name = "gridView5";
            this.gridView5.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView5.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn9
            // 
            resources.ApplyResources(this.gridColumn9, "gridColumn9");
            this.gridColumn9.FieldName = "VenNameAr";
            this.gridColumn9.Name = "gridColumn9";
            // 
            // gridColumn10
            // 
            resources.ApplyResources(this.gridColumn10, "gridColumn10");
            this.gridColumn10.FieldName = "VendorId";
            this.gridColumn10.Name = "gridColumn10";
            // 
            // picLogo
            // 
            resources.ApplyResources(this.picLogo, "picLogo");
            this.picLogo.MenuManager = this.barManager1;
            this.picLogo.Name = "picLogo";
            this.picLogo.Properties.AccessibleDescription = resources.GetString("picLogo.Properties.AccessibleDescription");
            this.picLogo.Properties.AccessibleName = resources.GetString("picLogo.Properties.AccessibleName");
            this.picLogo.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Stretch;
            // 
            // lblReportName
            // 
            resources.ApplyResources(this.lblReportName, "lblReportName");
            this.lblReportName.MenuManager = this.barManager1;
            this.lblReportName.Name = "lblReportName";
            this.lblReportName.Properties.AccessibleDescription = resources.GetString("lblReportName.Properties.AccessibleDescription");
            this.lblReportName.Properties.AccessibleName = resources.GetString("lblReportName.Properties.AccessibleName");
            this.lblReportName.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("lblReportName.Properties.Appearance.Font")));
            this.lblReportName.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lblReportName.Properties.Appearance.FontSizeDelta")));
            this.lblReportName.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lblReportName.Properties.Appearance.FontStyleDelta")));
            this.lblReportName.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lblReportName.Properties.Appearance.GradientMode")));
            this.lblReportName.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lblReportName.Properties.Appearance.Image")));
            this.lblReportName.Properties.Appearance.Options.UseFont = true;
            this.lblReportName.Properties.Appearance.Options.UseTextOptions = true;
            this.lblReportName.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblReportName.Properties.AutoHeight = ((bool)(resources.GetObject("lblReportName.Properties.AutoHeight")));
            this.lblReportName.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("lblReportName.Properties.Mask.AutoComplete")));
            this.lblReportName.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("lblReportName.Properties.Mask.BeepOnError")));
            this.lblReportName.Properties.Mask.EditMask = resources.GetString("lblReportName.Properties.Mask.EditMask");
            this.lblReportName.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("lblReportName.Properties.Mask.IgnoreMaskBlank")));
            this.lblReportName.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("lblReportName.Properties.Mask.MaskType")));
            this.lblReportName.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("lblReportName.Properties.Mask.PlaceHolder")));
            this.lblReportName.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("lblReportName.Properties.Mask.SaveLiteral")));
            this.lblReportName.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("lblReportName.Properties.Mask.ShowPlaceHolders")));
            this.lblReportName.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("lblReportName.Properties.Mask.UseMaskAsDisplayFormat")));
            this.lblReportName.Properties.NullValuePrompt = resources.GetString("lblReportName.Properties.NullValuePrompt");
            this.lblReportName.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lblReportName.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // lblDateFilter
            // 
            resources.ApplyResources(this.lblDateFilter, "lblDateFilter");
            this.lblDateFilter.MenuManager = this.barManager1;
            this.lblDateFilter.Name = "lblDateFilter";
            this.lblDateFilter.Properties.AccessibleDescription = resources.GetString("lblDateFilter.Properties.AccessibleDescription");
            this.lblDateFilter.Properties.AccessibleName = resources.GetString("lblDateFilter.Properties.AccessibleName");
            this.lblDateFilter.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lblDateFilter.Properties.Appearance.FontSizeDelta")));
            this.lblDateFilter.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lblDateFilter.Properties.Appearance.FontStyleDelta")));
            this.lblDateFilter.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lblDateFilter.Properties.Appearance.GradientMode")));
            this.lblDateFilter.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lblDateFilter.Properties.Appearance.Image")));
            this.lblDateFilter.Properties.Appearance.Options.UseTextOptions = true;
            this.lblDateFilter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblDateFilter.Properties.AutoHeight = ((bool)(resources.GetObject("lblDateFilter.Properties.AutoHeight")));
            this.lblDateFilter.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("lblDateFilter.Properties.Mask.AutoComplete")));
            this.lblDateFilter.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.BeepOnError")));
            this.lblDateFilter.Properties.Mask.EditMask = resources.GetString("lblDateFilter.Properties.Mask.EditMask");
            this.lblDateFilter.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.IgnoreMaskBlank")));
            this.lblDateFilter.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("lblDateFilter.Properties.Mask.MaskType")));
            this.lblDateFilter.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("lblDateFilter.Properties.Mask.PlaceHolder")));
            this.lblDateFilter.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.SaveLiteral")));
            this.lblDateFilter.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.ShowPlaceHolders")));
            this.lblDateFilter.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.UseMaskAsDisplayFormat")));
            this.lblDateFilter.Properties.NullValuePrompt = resources.GetString("lblDateFilter.Properties.NullValuePrompt");
            this.lblDateFilter.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lblDateFilter.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // lblFilter
            // 
            resources.ApplyResources(this.lblFilter, "lblFilter");
            this.lblFilter.MenuManager = this.barManager1;
            this.lblFilter.Name = "lblFilter";
            this.lblFilter.Properties.AccessibleDescription = resources.GetString("lblFilter.Properties.AccessibleDescription");
            this.lblFilter.Properties.AccessibleName = resources.GetString("lblFilter.Properties.AccessibleName");
            this.lblFilter.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lblFilter.Properties.Appearance.FontSizeDelta")));
            this.lblFilter.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lblFilter.Properties.Appearance.FontStyleDelta")));
            this.lblFilter.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lblFilter.Properties.Appearance.GradientMode")));
            this.lblFilter.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lblFilter.Properties.Appearance.Image")));
            this.lblFilter.Properties.Appearance.Options.UseTextOptions = true;
            this.lblFilter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblFilter.Properties.AutoHeight = ((bool)(resources.GetObject("lblFilter.Properties.AutoHeight")));
            this.lblFilter.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("lblFilter.Properties.Mask.AutoComplete")));
            this.lblFilter.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("lblFilter.Properties.Mask.BeepOnError")));
            this.lblFilter.Properties.Mask.EditMask = resources.GetString("lblFilter.Properties.Mask.EditMask");
            this.lblFilter.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("lblFilter.Properties.Mask.IgnoreMaskBlank")));
            this.lblFilter.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("lblFilter.Properties.Mask.MaskType")));
            this.lblFilter.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("lblFilter.Properties.Mask.PlaceHolder")));
            this.lblFilter.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("lblFilter.Properties.Mask.SaveLiteral")));
            this.lblFilter.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("lblFilter.Properties.Mask.ShowPlaceHolders")));
            this.lblFilter.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("lblFilter.Properties.Mask.UseMaskAsDisplayFormat")));
            this.lblFilter.Properties.NullValuePrompt = resources.GetString("lblFilter.Properties.NullValuePrompt");
            this.lblFilter.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lblFilter.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // frm_SL_averagesellingpriceoftheitems
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.picLogo);
            this.Controls.Add(this.grdCategory);
            this.Controls.Add(this.lblFilter);
            this.Controls.Add(this.lblDateFilter);
            this.Controls.Add(this.lblReportName);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.KeyPreview = true;
            this.Name = "frm_SL_averagesellingpriceoftheitems";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_Rep_FormClosing);
            this.Load += new System.EventHandler(this.frm_SL_InvoiceList_Load);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenu1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_MtrxParent)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_mtrx)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdCategory)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_cat)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_comp)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Vendor)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblReportName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDateFilter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFilter.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnPreview;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraGrid.GridControl grdCategory;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraBars.BarButtonItem barBtnPrint;
        private DevExpress.XtraEditors.TextEdit lblDateFilter;
        private DevExpress.XtraEditors.TextEdit lblReportName;
        private DevExpress.XtraEditors.PictureEdit picLogo;
        private DevExpress.XtraEditors.TextEdit lblFilter;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_MtrxParent;
        private DevExpress.XtraGrid.Views.Grid.GridView repositoryItemGridLookUpEdit1View;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_comp;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_cat;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_mtrx;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraBars.BarButtonItem barBtnRefresh;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_Vendor;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_ItemId;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn colIndex;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_Qty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_PiecesCount;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_SUom;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_SmallQty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_MUom;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_MediumQty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_LUom;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_LargeQty;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_ItemCode1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_ItemCode2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_CategoryNameAr;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn colStoreNameAr;

        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand1;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand2;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn3;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn bandedGridColumn4;
        private DevExpress.XtraBars.PopupMenu popupMenu1;
        private DevExpress.XtraBars.BarButtonItem btn_Landscape;
        private DevExpress.XtraBars.BarButtonItem btn_Portrait;
    }
}