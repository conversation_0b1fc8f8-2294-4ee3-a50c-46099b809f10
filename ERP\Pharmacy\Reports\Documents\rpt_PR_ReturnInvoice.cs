using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using DAL;
using System.Linq;
using System.Data;
using DevExpress.XtraEditors;

using System.Windows.Forms;

namespace Reports
{
    public partial class rpt_PR_ReturnInvoice : DevExpress.XtraReports.UI.XtraReport
    {
        string vendor, serial, number, date, store, paymethod, drawer, notes,custax,
            total, tax, discountR, discountV, expenses, net, paied, remains, userName, DeductTaxV, AddTaxV
            , DriverName, VehicleNumber, Destination, scalWeightSerial;

        DataTable dt_inv_details;
        DataTable dt_Weights;
        int currId;

        public rpt_PR_ReturnInvoice()
        {
            InitializeComponent();
        }
        public rpt_PR_ReturnInvoice(string _vendor, string _serial, string _number, string _date, string _store, string _paymethod,
            string _drawer, string _notes, string _total, string _tax, string _discountR, string _discountV, string _expenses, string _net, string _paied, string _remains,
            DataTable dt, string userName, string _DeductTaxV, string _AddTaxV, int _currId, string DriverName, string VehicleNumber,
            string Destination, string scalWeightSerial,string _CustomTaxV, DataTable _dt_Weights)
        {
            InitializeComponent();            
            vendor=_vendor;
            serial=_serial;
            number=_number; 
            date=_date;
            store=_store;
            paymethod=_paymethod;
            drawer=_drawer;
            notes=_notes;
            total=_total; 
            tax=_tax;
            discountR = _discountR;
            discountV = _discountV;
            expenses=_expenses;
            net=_net;
            paied = _paied;
            remains=_remains;
            custax = _CustomTaxV;
            this.userName = userName;

            DeductTaxV = _DeductTaxV;
            AddTaxV = _AddTaxV;

            this.DriverName = DriverName;
            this.VehicleNumber = VehicleNumber;
            this.Destination = Destination;
            this.scalWeightSerial = scalWeightSerial;
            currId = _currId;

            dt_inv_details = dt;
            dt_Weights = _dt_Weights;
            this.DataSource = dt_inv_details;
            getReportHeader();
            //LoadData();            
        }        

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                lblCompName.Text = comp.CmpNameAr;                
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }            
        }

        public void LoadData()
        {
            lbl_date.Text = date;
            lbl_DiscountR.Text = discountR;
            lbl_DiscountV.Text = discountV;
            lbl_Drawer.Text = drawer;
            lbl_Expenses.Text = expenses;
            lbl_Net.Text = net;
            lbl_notes.Text = notes;
            lbl_Number.Text = number;
            lbl_Paied.Text = paied;
            lbl_Paymethod.Text = paymethod;
            lbl_Remains.Text = remains;
            lbl_Serial.Text = serial;
            lbl_store.Text = store;
            lbl_Tax.Text = tax;
            lbl_Total.Text = total;
            lbl_Vendor.Text = vendor;
            txt_custax.Text = custax;
            lblTotalWords.Text = Shared.IsEnglish ? HelperAcc.ConvertMoneyToText(net,currId, Shared.lstCurrency) :
                                   HelperAcc.ConvertMoneyToArabicText(net, currId, Shared.lstCurrency);
            this.DataSource = dt_inv_details;
            lbl_User.Text = userName;
            lbl_DeductTaxV.Text = DeductTaxV;
            lbl_AddTaxV.Text = AddTaxV;

            lbl_DriverName.Text = DriverName;
            lbl_VehicleNumber.Text = VehicleNumber;
            lbl_Destination.Text = Destination;
            lbl_ScaleWeightSerial.Text = scalWeightSerial;

            decimal totalQty = 0;
            decimal totalP = 0;
            decimal total_Packs = 0;
            //totalQty = dt_inv_details.Compute("Sum(Qty)", string.Empty);
            foreach (DataRow row in dt_inv_details.Rows)
            {
                totalQty += Convert.ToDecimal(row["Qty"]);
                totalP += Convert.ToDecimal(row["PiecesCount"]);

                if (row["Pack"] != null && row["Pack"] != DBNull.Value)
                    total_Packs += Convert.ToDecimal(row["Pack"]);
            }

            lbl_TotalQty.Text = totalQty.ToString();
            lbl_totalPieces.Text = totalP.ToString();
            lbl_TotalPacks.Text = total_Packs.ToString();

            cell_code.DataBindings.Add("Text", this.DataSource, "ItemCode1");
            cell_code2.DataBindings.Add("Text", this.DataSource, "ItemCode2");
            cell_Disc.DataBindings.Add("Text", this.DataSource, "DiscountValue");
            cell_Expire.DataBindings.Add("Text", this.DataSource, "Expire");
            cell_Batch.DataBindings.Add("Text", this.DataSource, "Batch");
            cell_Price.DataBindings.Add("Text", this.DataSource, "PurchasePrice");
            cell_Qty.DataBindings.Add("Text", this.DataSource, "Qty");
            cell_Total.DataBindings.Add("Text", this.DataSource, "TotalPurchasePrice");
            cell_ItemName.DataBindings.Add("Text", this.DataSource, "ItemName");
            cell_UOM.DataBindings.Add("Text", this.DataSource, "UOM");
            cell_Height.DataBindings.Add("Text", this.DataSource, "Height");
            cell_Width.DataBindings.Add("Text", this.DataSource, "Width");
            cell_Length.DataBindings.Add("Text", this.DataSource, "Length");
            cell_TotalQty.DataBindings.Add("Text", this.DataSource, "TotalQty");
            cell_DiscountRatio.DataBindings.Add("Text", this.DataSource, "DiscountRatio");
            cell_DiscountRatio2.DataBindings.Add("Text", this.DataSource, "DiscountRatio2");
            cell_DiscountRatio3.DataBindings.Add("Text", this.DataSource, "DiscountRatio3");
            cell_SalesTaxRatio.DataBindings.Add("Text", this.DataSource, "SalesTaxRatio");
            cell_SalesTax.DataBindings.Add("Text", this.DataSource, "SalesTax");
            cell_ManufactureDate.DataBindings.Add("Text", this.DataSource, "ManufactureDate");
            cell_Pack.DataBindings.Add("Text", this.DataSource, "Pack");
            cell_PiecesCount.DataBindings.Add("Text", this.DataSource, "PiecesCount");

            if (dt_Weights.Rows.Count > 0)
                xrSubreport2.ReportSource = new rpt_multiple_weights(dt_Weights);
        }
        
    }
}
