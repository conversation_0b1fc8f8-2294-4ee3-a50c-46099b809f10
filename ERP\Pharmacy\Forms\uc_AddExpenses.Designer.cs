﻿namespace Pharmacy.Forms
{
    partial class uc_AddExpenses
    {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(uc_AddExpenses));
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_ExpenseAccountId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_ExpenseAccount = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.col_ExpenseNote = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Amount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_Amount = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_ExpenseAccount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Amount)).BeginInit();
            this.SuspendLayout();
            // 
            // gridControl1
            // 
            this.gridControl1.AccessibleDescription = null;
            this.gridControl1.AccessibleName = null;
            resources.ApplyResources(this.gridControl1, "gridControl1");
            this.gridControl1.BackgroundImage = null;
            this.gridControl1.EmbeddedNavigator.AccessibleDescription = null;
            this.gridControl1.EmbeddedNavigator.AccessibleName = null;
            this.gridControl1.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("gridControl1.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.gridControl1.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("gridControl1.EmbeddedNavigator.Anchor")));
            this.gridControl1.EmbeddedNavigator.BackgroundImage = null;
            this.gridControl1.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("gridControl1.EmbeddedNavigator.BackgroundImageLayout")));
            this.gridControl1.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("gridControl1.EmbeddedNavigator.ImeMode")));
            this.gridControl1.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("gridControl1.EmbeddedNavigator.TextLocation")));
            this.gridControl1.EmbeddedNavigator.ToolTip = resources.GetString("gridControl1.EmbeddedNavigator.ToolTip");
            this.gridControl1.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("gridControl1.EmbeddedNavigator.ToolTipIconType")));
            this.gridControl1.EmbeddedNavigator.ToolTipTitle = resources.GetString("gridControl1.EmbeddedNavigator.ToolTipTitle");
            this.gridControl1.Font = null;
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.rep_ExpenseAccount,
            this.rep_Amount});
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // gridView1
            // 
            this.gridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.HeaderPanel.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridView1.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView1.Appearance.Row.Options.UseTextOptions = true;
            this.gridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.Row.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridView1.Appearance.Row.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridView1, "gridView1");
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_ExpenseAccountId,
            this.col_ExpenseNote,
            this.col_Amount});
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.True;
            this.gridView1.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.True;
            this.gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView1.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.gridView1.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Bottom;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.KeyDown += new System.Windows.Forms.KeyEventHandler(this.gridView1_KeyDown);
            this.gridView1.CustomColumnDisplayText += new DevExpress.XtraGrid.Views.Base.CustomColumnDisplayTextEventHandler(this.gridView1_CustomColumnDisplayText);
            this.gridView1.InvalidRowException += new DevExpress.XtraGrid.Views.Base.InvalidRowExceptionEventHandler(this.gridView1_InvalidRowException);
            this.gridView1.ValidateRow += new DevExpress.XtraGrid.Views.Base.ValidateRowEventHandler(this.gridView1_ValidateRow);
            // 
            // col_ExpenseAccountId
            // 
            resources.ApplyResources(this.col_ExpenseAccountId, "col_ExpenseAccountId");
            this.col_ExpenseAccountId.ColumnEdit = this.rep_ExpenseAccount;
            this.col_ExpenseAccountId.FieldName = "ExpenseAccountId";
            this.col_ExpenseAccountId.Name = "col_ExpenseAccountId";
            // 
            // rep_ExpenseAccount
            // 
            this.rep_ExpenseAccount.AccessibleDescription = null;
            this.rep_ExpenseAccount.AccessibleName = null;
            resources.ApplyResources(this.rep_ExpenseAccount, "rep_ExpenseAccount");
            this.rep_ExpenseAccount.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_ExpenseAccount.Buttons"))))});
            this.rep_ExpenseAccount.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_ExpenseAccount.Columns"), resources.GetString("rep_ExpenseAccount.Columns1"))});
            this.rep_ExpenseAccount.Name = "rep_ExpenseAccount";
            this.rep_ExpenseAccount.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            // 
            // col_ExpenseNote
            // 
            resources.ApplyResources(this.col_ExpenseNote, "col_ExpenseNote");
            this.col_ExpenseNote.FieldName = "ExpenseNote";
            this.col_ExpenseNote.Name = "col_ExpenseNote";
            // 
            // col_Amount
            // 
            resources.ApplyResources(this.col_Amount, "col_Amount");
            this.col_Amount.ColumnEdit = this.rep_Amount;
            this.col_Amount.FieldName = "Amount";
            this.col_Amount.Name = "col_Amount";
            // 
            // rep_Amount
            // 
            this.rep_Amount.AccessibleDescription = null;
            this.rep_Amount.AccessibleName = null;
            resources.ApplyResources(this.rep_Amount, "rep_Amount");
            this.rep_Amount.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.rep_Amount.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("rep_Amount.Mask.AutoComplete")));
            this.rep_Amount.Mask.BeepOnError = ((bool)(resources.GetObject("rep_Amount.Mask.BeepOnError")));
            this.rep_Amount.Mask.EditMask = resources.GetString("rep_Amount.Mask.EditMask");
            this.rep_Amount.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("rep_Amount.Mask.IgnoreMaskBlank")));
            this.rep_Amount.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("rep_Amount.Mask.MaskType")));
            this.rep_Amount.Mask.PlaceHolder = ((char)(resources.GetObject("rep_Amount.Mask.PlaceHolder")));
            this.rep_Amount.Mask.SaveLiteral = ((bool)(resources.GetObject("rep_Amount.Mask.SaveLiteral")));
            this.rep_Amount.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("rep_Amount.Mask.ShowPlaceHolders")));
            this.rep_Amount.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("rep_Amount.Mask.UseMaskAsDisplayFormat")));
            this.rep_Amount.Name = "rep_Amount";
            // 
            // uc_AddExpenses
            // 
            this.AccessibleDescription = null;
            this.AccessibleName = null;
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackgroundImage = null;
            this.Controls.Add(this.gridControl1);
            this.Name = "uc_AddExpenses";
            this.Load += new System.EventHandler(this.uc_AddExpenses_Load);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_ExpenseAccount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Amount)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn col_ExpenseAccountId;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit rep_ExpenseAccount;
        private DevExpress.XtraGrid.Columns.GridColumn col_ExpenseNote;
        private DevExpress.XtraGrid.Columns.GridColumn col_Amount;
        private DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit rep_Amount;
    }
}
