﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="lblCloseDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>375, 113</value>
  </data>
  <data name="lblCloseDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>59, 13</value>
  </data>
  <data name="lblCloseDate.Text" xml:space="preserve">
    <value>تاريخ الاغلاق</value>
  </data>
  <data name="barBtnHelp.Caption" xml:space="preserve">
    <value>مساعدة</value>
  </data>
  <data name="barBtnNew.Caption" xml:space="preserve">
    <value>جديد</value>
  </data>
  <data name="barBtnDelete.Caption" xml:space="preserve">
    <value>حذف</value>
  </data>
  <data name="barBtnSave.Caption" xml:space="preserve">
    <value>حفظ</value>
  </data>
  <data name="barBtnList.Caption" xml:space="preserve">
    <value>القائمة</value>
  </data>
  <data name="barBtnClose.Caption" xml:space="preserve">
    <value>غلق</value>
  </data>
  <data name="barDockControlTop.Size" type="System.Drawing.Size, System.Drawing">
    <value>757, 28</value>
  </data>
  <data name="barDockControlBottom.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 601</value>
  </data>
  <data name="barDockControlBottom.Size" type="System.Drawing.Size, System.Drawing">
    <value>757, 0</value>
  </data>
  <data name="barDockControlLeft.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 28</value>
  </data>
  <data name="barDockControlLeft.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 573</value>
  </data>
  <data name="barDockControlRight.Location" type="System.Drawing.Point, System.Drawing">
    <value>757, 28</value>
  </data>
  <data name="barDockControlRight.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 573</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>757, 601</value>
  </data>
  <data name="xtraTabControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 52</value>
  </data>
  <data name="cmbStatus.Location" type="System.Drawing.Point, System.Drawing">
    <value>267, 10</value>
  </data>
  <data name="cmbStatus.Properties.Items" xml:space="preserve">
    <value>مفتوح</value>
  </data>
  <data name="cmbStatus.Properties.Items3" xml:space="preserve">
    <value>مغلق</value>
  </data>
  <data name="cmbStatus.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="lblLcIsOpen.Location" type="System.Drawing.Point, System.Drawing">
    <value>373, 13</value>
  </data>
  <data name="lblLcIsOpen.Size" type="System.Drawing.Size, System.Drawing">
    <value>58, 13</value>
  </data>
  <data name="lblLcIsOpen.Text" xml:space="preserve">
    <value>حالة الإعتماد</value>
  </data>
  <data name="txtBillOfLading.Location" type="System.Drawing.Point, System.Drawing">
    <value>268, 235</value>
  </data>
  <data name="txtBillOfLading.Size" type="System.Drawing.Size, System.Drawing">
    <value>344, 20</value>
  </data>
  <data name="lblBillOfLanding.Location" type="System.Drawing.Point, System.Drawing">
    <value>619, 238</value>
  </data>
  <data name="lblBillOfLanding.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 13</value>
  </data>
  <data name="lblBillOfLanding.Text" xml:space="preserve">
    <value>رقم البوليصة</value>
  </data>
  <data name="txtPayMethod.Location" type="System.Drawing.Point, System.Drawing">
    <value>269, 210</value>
  </data>
  <data name="txtPayMethod.Size" type="System.Drawing.Size, System.Drawing">
    <value>344, 20</value>
  </data>
  <data name="lblPayMethod.Location" type="System.Drawing.Point, System.Drawing">
    <value>619, 213</value>
  </data>
  <data name="lblPayMethod.Size" type="System.Drawing.Size, System.Drawing">
    <value>62, 13</value>
  </data>
  <data name="lblPayMethod.Text" xml:space="preserve">
    <value>طريقة السداد</value>
  </data>
  <data name="txtShipMethod.Location" type="System.Drawing.Point, System.Drawing">
    <value>268, 185</value>
  </data>
  <data name="txtShipMethod.Size" type="System.Drawing.Size, System.Drawing">
    <value>344, 20</value>
  </data>
  <data name="lblShipMethod.Location" type="System.Drawing.Point, System.Drawing">
    <value>619, 188</value>
  </data>
  <data name="lblShipMethod.Size" type="System.Drawing.Size, System.Drawing">
    <value>63, 13</value>
  </data>
  <data name="lblShipMethod.Text" xml:space="preserve">
    <value>طريقة الشحن</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="dtDeliverDate.EditValue" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtDeliverDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>267, 135</value>
  </data>
  <data name="dtDeliverDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="dtShipDate.EditValue" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtShipDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>512, 135</value>
  </data>
  <data name="dtShipDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="lblDeliverDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>375, 138</value>
  </data>
  <data name="lblDeliverDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 13</value>
  </data>
  <data name="lblDeliverDate.Text" xml:space="preserve">
    <value>تاريخ الوصول</value>
  </data>
  <data name="lblShipDAte.Location" type="System.Drawing.Point, System.Drawing">
    <value>619, 138</value>
  </data>
  <data name="lblShipDAte.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 13</value>
  </data>
  <data name="lblShipDAte.Text" xml:space="preserve">
    <value>تاريخ الشحن</value>
  </data>
  <data name="lkp_Crnc.Location" type="System.Drawing.Point, System.Drawing">
    <value>267, 85</value>
  </data>
  <data name="lkp_Crnc.Properties.Columns8" xml:space="preserve">
    <value>العملة</value>
  </data>
  <data name="lkp_Crnc.Size" type="System.Drawing.Size, System.Drawing">
    <value>101, 20</value>
  </data>
  <data name="lblCrnc.Location" type="System.Drawing.Point, System.Drawing">
    <value>375, 88</value>
  </data>
  <data name="lblCrnc.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 13</value>
  </data>
  <data name="lblCrnc.Text" xml:space="preserve">
    <value>العملة</value>
  </data>
  <data name="lblVendor.Location" type="System.Drawing.Point, System.Drawing">
    <value>619, 63</value>
  </data>
  <data name="lblVendor.Size" type="System.Drawing.Size, System.Drawing">
    <value>27, 13</value>
  </data>
  <data name="lblVendor.Text" xml:space="preserve">
    <value>المورد</value>
  </data>
  <data name="lkpVendor.Location" type="System.Drawing.Point, System.Drawing">
    <value>268, 60</value>
  </data>
  <data name="gridColumn20.Caption" xml:space="preserve">
    <value>كود المورد</value>
  </data>
  <data name="gridColumn21.Caption" xml:space="preserve">
    <value>اسم المورد</value>
  </data>
  <data name="lkpVendor.Size" type="System.Drawing.Size, System.Drawing">
    <value>344, 20</value>
  </data>
  <data name="groupBox2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="grdDepr.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 17</value>
  </data>
  <data name="colSerial.Caption" xml:space="preserve">
    <value>م</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="colSerial.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="colSerial.Width" type="System.Int32, mscorlib">
    <value>25</value>
  </data>
  <data name="colDate.Caption" xml:space="preserve">
    <value>التاريخ</value>
  </data>
  <data name="colDate.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="colDate.Width" type="System.Int32, mscorlib">
    <value>62</value>
  </data>
  <data name="colJournalcode.Caption" xml:space="preserve">
    <value>رقم القيد</value>
  </data>
  <data name="colJournalcode.VisibleIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="colJournalcode.Width" type="System.Int32, mscorlib">
    <value>46</value>
  </data>
  <data name="colNotes.Caption" xml:space="preserve">
    <value>البيان</value>
  </data>
  <data name="colNotes.VisibleIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="colNotes.Width" type="System.Int32, mscorlib">
    <value>122</value>
  </data>
  <data name="colDebit.Caption" xml:space="preserve">
    <value>دائن أجنبي</value>
  </data>
  <data name="colDebit.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="colDebit.Width" type="System.Int32, mscorlib">
    <value>48</value>
  </data>
  <data name="colCredit.Caption" xml:space="preserve">
    <value>مدين أجنبي</value>
  </data>
  <data name="colCredit.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="colCredit.Width" type="System.Int32, mscorlib">
    <value>49</value>
  </data>
  <data name="colCurrency.Caption" xml:space="preserve">
    <value>العملة</value>
  </data>
  <data name="repCrnc.Columns1" xml:space="preserve">
    <value>العملة</value>
  </data>
  <data name="colCurrency.VisibleIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="colCurrency.Width" type="System.Int32, mscorlib">
    <value>56</value>
  </data>
  <data name="colCrncRate.Caption" xml:space="preserve">
    <value>معامل التحويل</value>
  </data>
  <data name="colCrncRate.VisibleIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="colCrncRate.Width" type="System.Int32, mscorlib">
    <value>60</value>
  </data>
  <data name="colTotalDebit.Caption" xml:space="preserve">
    <value>مدين محلي</value>
  </data>
  <data name="colTotalDebit.VisibleIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="colTotalDebit.Width" type="System.Int32, mscorlib">
    <value>58</value>
  </data>
  <data name="colTotalCredit.Caption" xml:space="preserve">
    <value>دائن محلي</value>
  </data>
  <data name="colTotalCredit.VisibleIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="colTotalCredit.Width" type="System.Int32, mscorlib">
    <value>58</value>
  </data>
  <data name="grdDepr.Size" type="System.Drawing.Size, System.Drawing">
    <value>691, 208</value>
  </data>
  <data name="groupBox2.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 303</value>
  </data>
  <data name="groupBox2.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="groupBox2.Size" type="System.Drawing.Size, System.Drawing">
    <value>703, 229</value>
  </data>
  <data name="groupBox2.Text" xml:space="preserve">
    <value>القيود اليومية</value>
  </data>
  <data name="dtCloseDate.EditValue" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtCloseDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>267, 110</value>
  </data>
  <data name="dtCloseDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="dtOpenDate.EditValue" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtOpenDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>512, 110</value>
  </data>
  <data name="dtOpenDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="txtLcCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>526, 10</value>
  </data>
  <data name="txtLcCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>86, 20</value>
  </data>
  <data name="txtNotes.Location" type="System.Drawing.Point, System.Drawing">
    <value>268, 260</value>
  </data>
  <data name="txtNotes.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Inherit</value>
  </data>
  <data name="lblNotes.Location" type="System.Drawing.Point, System.Drawing">
    <value>619, 274</value>
  </data>
  <data name="lblNotes.Size" type="System.Drawing.Size, System.Drawing">
    <value>41, 13</value>
  </data>
  <data name="lblNotes.Text" xml:space="preserve">
    <value>ملاحظات</value>
  </data>
  <data name="txtName.Location" type="System.Drawing.Point, System.Drawing">
    <value>268, 35</value>
  </data>
  <data name="txtName.Size" type="System.Drawing.Size, System.Drawing">
    <value>344, 20</value>
  </data>
  <data name="lblName.Location" type="System.Drawing.Point, System.Drawing">
    <value>619, 38</value>
  </data>
  <data name="lblName.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 13</value>
  </data>
  <data name="lblName.Text" xml:space="preserve">
    <value>اسم الاعتماد </value>
  </data>
  <data name="txtPort.Location" type="System.Drawing.Point, System.Drawing">
    <value>268, 160</value>
  </data>
  <data name="txtPort.Size" type="System.Drawing.Size, System.Drawing">
    <value>344, 20</value>
  </data>
  <data name="lblShipPort.Location" type="System.Drawing.Point, System.Drawing">
    <value>619, 163</value>
  </data>
  <data name="lblShipPort.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 13</value>
  </data>
  <data name="lblShipPort.Text" xml:space="preserve">
    <value>ميناء الشحن</value>
  </data>
  <data name="lblCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>619, 13</value>
  </data>
  <data name="lblCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>15, 13</value>
  </data>
  <data name="lblCode.Text" xml:space="preserve">
    <value>كود</value>
  </data>
  <data name="lblOpenDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>619, 113</value>
  </data>
  <data name="lblOpenDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 13</value>
  </data>
  <data name="lblOpenDate.Text" xml:space="preserve">
    <value>تاريخ الفتح</value>
  </data>
  <data name="lblAmount.Location" type="System.Drawing.Point, System.Drawing">
    <value>619, 88</value>
  </data>
  <data name="lblAmount.Size" type="System.Drawing.Size, System.Drawing">
    <value>61, 13</value>
  </data>
  <data name="lblAmount.Text" xml:space="preserve">
    <value>قيمة الاعتماد</value>
  </data>
  <data name="txtAmount.Location" type="System.Drawing.Point, System.Drawing">
    <value>512, 85</value>
  </data>
  <data name="txtAmount.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="tab_main.Size" type="System.Drawing.Size, System.Drawing">
    <value>713, 538</value>
  </data>
  <data name="tab_main.Text" xml:space="preserve">
    <value>البيانات الرئيسية</value>
  </data>
  <data name="xtraTabControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>742, 546</value>
  </data>
  <data name="btnEditPhoto.Location" type="System.Drawing.Point, System.Drawing">
    <value>182, 86</value>
  </data>
  <data name="btnEditPhoto.ToolTip" xml:space="preserve">
    <value>تعديل الصورة</value>
  </data>
  <data name="btnAddEmpPhoho.Location" type="System.Drawing.Point, System.Drawing">
    <value>429, 54</value>
  </data>
  <data name="btnAddEmpPhoho.ToolTip" xml:space="preserve">
    <value>تحميل الصورة</value>
  </data>
  <data name="btnDeleteEmpPhoto.Location" type="System.Drawing.Point, System.Drawing">
    <value>182, 115</value>
  </data>
  <data name="btnDeleteEmpPhoto.ToolTip" xml:space="preserve">
    <value>حذف الصورة</value>
  </data>
  <data name="btnShowPhotoes.Location" type="System.Drawing.Point, System.Drawing">
    <value>182, 144</value>
  </data>
  <data name="btnShowPhotoes.Text" xml:space="preserve">
    <value>عرض
 الصور</value>
  </data>
  <data name="labelControl22.Location" type="System.Drawing.Point, System.Drawing">
    <value>636, 28</value>
  </data>
  <data name="labelControl22.Size" type="System.Drawing.Size, System.Drawing">
    <value>32, 13</value>
  </data>
  <data name="labelControl22.Text" xml:space="preserve">
    <value>المسار</value>
  </data>
  <data name="txtImagePath.Location" type="System.Drawing.Point, System.Drawing">
    <value>460, 24</value>
  </data>
  <data name="lstPhotos.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 24</value>
  </data>
  <data name="lstPhotos.Size" type="System.Drawing.Size, System.Drawing">
    <value>171, 160</value>
  </data>
  <data name="btnBrowse.Location" type="System.Drawing.Point, System.Drawing">
    <value>429, 24</value>
  </data>
  <data name="btnBrowse.ToolTip" xml:space="preserve">
    <value>استعراض</value>
  </data>
  <data name="labelControl23.Location" type="System.Drawing.Point, System.Drawing">
    <value>636, 54</value>
  </data>
  <data name="labelControl23.Size" type="System.Drawing.Size, System.Drawing">
    <value>54, 13</value>
  </data>
  <data name="labelControl23.Text" xml:space="preserve">
    <value>اسم الصورة</value>
  </data>
  <data name="txtImageDesc.Location" type="System.Drawing.Point, System.Drawing">
    <value>460, 50</value>
  </data>
  <data name="groupControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 7</value>
  </data>
  <data name="groupControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>706, 192</value>
  </data>
  <data name="groupControl2.Text" xml:space="preserve">
    <value>صور المرفقات</value>
  </data>
  <data name="tab_docs.Size" type="System.Drawing.Size, System.Drawing">
    <value>714, 540</value>
  </data>
  <data name="tab_docs.Text" xml:space="preserve">
    <value>مرفقات</value>
  </data>
  <data name="btnNext.Location" type="System.Drawing.Point, System.Drawing">
    <value>66, 32</value>
  </data>
  <data name="btnNext.ToolTip" xml:space="preserve">
    <value>التالي</value>
  </data>
  <data name="btnPrev.Location" type="System.Drawing.Point, System.Drawing">
    <value>36, 32</value>
  </data>
  <data name="btnPrev.ToolTip" xml:space="preserve">
    <value>السابق</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAA
        AAAfbf8AH2z/AB9r/x8fa/+rH2v/9h9r/9AfbP9HH2//AB9u/wAfcP8AAAAAAB9p/wAfZv8AH2D/Ah9j
        /2cfY//iH2P/7R9j/4YfY/8KH2P/AB9j/wAAAAAADw4QAA4NDwAODQ4ADQwORwwMDdAMCw32CwsMqwsL
        DB8MCw0ADAsNAB9u/wAfbf8WH23/qx9t//8fbv//H27//x9u/9gfb/89H3D/AB9y/wAfcf8AH23/AB9n
        /wAfZ/9cH2X/6x9j//8fY///H2P/9x9j/38fY/8GH2P/AB5e8QAPDhAADw4QAA4NDz0NDQ/YDQ0O/w0M
        Dv8MDA3/DAsNqwwLDRYMCw0AH2//FR9v/6Ifb///H3D//x9x//8fcf//H3H//x9x/9Mfcv82IHD/AiBw
        /wQfcP8DH2z/VB9q/+gfZ///H2X//x9j//8fY///H2P/9x9j/3cfY/8FH2P+ABAPEQAQDxE1Dw4Q0w8O
        EP8ODg//Dg0P/w0NDv8MDA3/DAsNogwLDRUfcP+MH3H//R9y//8ec///HnP//x50//8edP//HnP//x5z
        /9cec/+pH3L/qR9x/6wfbv/mH23//x9q//8faP//H2X//x9j//8fY///H2P/8h9j/04bTb8AERASERAQ
        ErsQEBL/EA8R/w8PEf8PDhD/Dg0P/w0ND/8MDA79DAwNjB5z/9QedP//HnX//x52//8edv//Hnb//x52
        //8edv//Hnb//x51//8edP//HnP//x9x//8fb///H23//x9r//8faP//H2X//x9j//8fY///H2P/gxg4
        hAASERM1ERET7hERE/8REBL/EBAS/xAPEf8PDhD/Dg4Q/w0ND/8NDA7THnX/wx52//8ed///Hnj//x54
        //8eef//Hnn//x54//8eeP/9Hnf/8h52//Iedf/zHnP//h9x//8fb///H23//x9q//8fZ///H2T//x9j
        //8fY/90GkGeABISFCYTEhThEhIU/xIRE/8RERP/EBAS/xAPEf8PDhD/Dg4Q/w4ND78ed/9UHnj/5x55
        //8eev//Hnr//x57//8ee///Hnr/+x56/5AeeP88Hnf/PB52/0Eedf+wHnP//x9x//8fb///H2z//x9p
        //8fZv//H2T/zB9j/yQdV9wAExIVAxMTFYMTEhX8ExIU/xISFP8RERP/ERAS/xAPEf8PDhDkDg4QTx50
        /wAeev9dHnv/7R58//8efP//Hn3//x59//0efP+UHnv/DB55/wAed/8AHnb/AB52/x8edf+5HnP//x9x
        //8fbv//H2v//x9o/9YfZf85H2f/AAAAAAAUExYAFBMWDRQTFpQUExX9ExIV/xISFP8SERP/ERAS6hAP
        EVYTDhQAHnr/AB58/wEefP9nHn3/7x5+//8efv/9Hn7/nR59/xAefP8AHnv/AB52/wAedf8AHnX/AB51
        /yUedP2+H3P//x9x//8fbPvbH2r/Px9o/wAfZv8AH2P/ABQTFgAUExYAFBQWEBQUFp0UExb9ExIV/xIS
        FOwRERNfIxkMABAQEQAee/8AHn3/AB5+/wcefv+xHn///x5//+oef/8tHn//AB59/wAefP8AAAAAAB52
        /wAeV/8AGkKGABk+eUYaQoPzG0KG/xo0ZZIbRZkAH2v/AB9o/wAAAAAAFBQWABQUFgAVFBcAFRQXLRUU
        F+oUExb/ExMVqxMTFAUSERQAEBASAAAAAAAegP8AHn//Ax6A/6kegP//HoD/5x6A/ycegP8AHoD/AAAA
        AAAAAAAAAAAAABpCgQAXFRcAFhMSOhcVFvAXFRb/GBUWjhgYHgAcO3gAAAAAAAAAAAAAAAAAFxYZABYV
        GAAWFRgnFRUX5xUUF/8UExajExIVAhQTFgAAAAAAHoD/AB6A/wAegP8IHoD/sx6B//8egf/rHoH/Lx6B
        /wAegv8AHoL/AAAAAAAXFhkAFBQXABcXGgAXFxpIGBca9BgYG/8ZGBuTGRkcABoaHQAbGx4AAAAAABkZ
        HAAYGBsAFxcZABcWGS8WFhjrFRUX/xUUF6wUFBYGFBMWABQTFQAef/8AHoD/Ah6A/20egf/xHoH//x6B
        //4egv+kHoL/Ex6C/wAegv8AFhYYABsZIAAXFxoAFxcaKRgXGsMYGBv/GRkc/xoZHN4bGh1FHBoeABwb
        HwAdHCAAGhkdABkZHAAYGBsTGBcapBcWGf4WFRj/FRQX7xQUFmYTEBMBExMVAB59/wIegP9kHoD/8B6B
        //8egv//HoL//x6C//8egv+cHoL/EB6B/wAaRX4AFxYZABcWGSQXFxq/GBgb/xkZHP8aGR3/Gxoe/xwb
        HtkcGx89HBwfABoYIQAbGh0AGxodEBkZHJwYGBv/Fxca/xYWGf8VFRf/FBQW7RQTFVwBABAAHn//Vx5/
        /+kegP//HoH//x6C//8egv//HoL//x6C//4egf+JHoD/BBgxUQAWFhgSFxYZsRgXGv8ZGBv/Ghkc/xsa
        Hf8cGx7/HBwf/x0cIM8eHSAmHRwgAB0cHwQbGh6JGhkc/hkYG/8YFxr/FxYZ/xYVGP8VFBf/ExMV5hMS
        FFIefv/EHn///x6A//8egf//HoL//x6C//8egv//HoL//x6B/+Megf8nGUB0ABYWGEwXFhn4GBca/xkY
        G/8aGh3/Gxse/xwcH/8dHCD/Hh0h/x4dIXUdHCAAHBsfJxwbHuMaGh3/GRkc/xgXGv8XFhn/FhUY/xUU
        F/8UExX/ExIUwR5+/9Uef///HoD//x6B//8egf//HoL//x6C//8egf//HoH/7h6B/zQaRX4AFhYZWhcW
        GfwYGBv/GRkc/xsaHf8cGx7/HRwg/x4dIf8fHiL/Hx4igx4dIQAdHB80HBsf7hsaHf8aGRz/GBgb/xcX
        Gf8WFRj/FRQX/xQTFf8TEhXSHn7/jR5///0egP//HoD//x6B//8egf//HoH//x6B//8egf+4HoD/EBk3
        YAAWFhkqFxYZ2xgYG/8ZGRz/Gxod/xwbHv8dHCD/Hh0h/x8eIu8gHyNKHx4iABwcHxAcGx64Gxod/xoZ
        HP8YGBv/Fxca/xYVGP8VFBf/FBMV/BMSFYgefv8VHn7/oh5///8egP//HoD//x6B//8egf//HoD/0B6A
        /zIegf8AFxIRABgaHQAXFxpQGBca5hkYHP8aGh3/Gxse/x0cH/8dHSD1Hh0hch8eIwQfHiIAGxseABwb
        HjIaGh3QGRkc/xgXG/8XFhn/FhUY/xUUF/8UExafExIVEx5+/wAefv8XHn7/qx5///8ef///Hn///x6A
        /9UegP85HoD/AB5//wAYIzUAFxcaABgYGwAYFxpXGRgb6hoZHP8bGh7/HBse+B0cH3weHSEFHh0hAB8e
        IgAbGh0AGxodABoZHDkZGBvVGBca/xcWGf8WFRj/FRQXqBQUFhUUExYAHn7/AB5+/wAefv8dHn7/yR5+
        //8efv/yHn//Sh5//wAef/8AHoD/AAAAAAAYFxoAGBcaABUXGgAZGBtUGRkcwxoaHc0bGh5yHBseCB0c
        HwAeHSEAAAAAABsaHQAaGRwAGRgbABgYG0oYFxryFxYZ/xYVGMUVFBcbFRQXABQUFgAefv8AHnz/AB56
        /wIefP+oHn3//x59/+ceff8lHn3/AB5//wAAAAAAAAAAAAAAAAAYGBoAGBgbABgXGgAaGRwNGhkdERkZ
        HAAbGh4AGxoeAAAAAAAAAAAAAAAAABkZHAAXFxoAFxcaJRcWGecWFRj/FRUXohMSFQEVFBcAFRQXAB50
        /wAed/8AHnn/CR56/7Qee///Hnv/6x57/y4eev8AHnj/AB53/wAAAAAAFRQXABYVGAAWFRgAFxYZJBgX
        GnEYFxp6GRgbNBkOEQAYGBsAGBcaAAAAAAAWFhgAFhYYABYWGQAXFhkuFhYY6xUVF/8VFBeuFBQWBxMT
        FQASERQAHnL/AB11/wMedv9zHnf/8x54//8eef/+Hnn/oR55/xEed/8AHnb/ABMSFgAbFx0AFRQXABUV
        Fy4WFhjJFxYZ/xgXGv8YFxreGBcaShcXGgAYFxoAFxYZABYVGAAWFRgAFhYYERYWGKEWFRj+FRQX/xQT
        FvETEhVuEhETAxIREwAgb/8CH3L/aR50//Iedf//Hnb//x53//8ed//+Hnf/lx52/w0edf8AGEOMABQT
        FgAUExYnFRQXxBUVF/8WFhj/FxYZ/xcXGv8XFxrdFxcaQRYWGQAWFRgAFhUYABYVGA0WFRiXFhUY/hUU
        F/8UExb/ExMV/xISFPARERNlEQ4RAh9t/1sfb//sH3H//x9y//8ec///HnT//x50//8edP/9HnT/hx9y
        /wUVKUwAEhIUFBMSFbYUExb/FBQW/xERE/8QEBL/FhUY/xYWGf8XFhnXFhYYYBYVGE4WFRhOFhUYmBUV
        F/wVFBf/FBMW/xMTFf8SEhT/ERET/xEQEuoQDxFXH2v/xh9s//8fbv//H2///x9w//8fcf//H3H//x9x
        //8fcf/kH3D/KBc3cQASERNOEhIU+BMSFf8SERT/DAwN/woJC/8QDxH/FRUX/xYVGP8WFRj7FRUX+BUU
        F/gVFBf/FBQW/xQTFv8TEhX/EhIU/xIRE/8REBL/EA8R/w8PEMMfZ//UH2n//x9r//8fbP//H23//x9u
        //8fbv//H27//x9u/+4fbv80Fzl6ABEQE1oRERP8EhIU/xERE/8ODg//DAwN/xAPEf8UExb/FBQW/xQU
        Fv8UFBb/FBMW/xQTFv8TExX/ExIU/xISFP8SERP/ERAS/xAQEv8PDxH/Dg4Q0R9l/4kfZf/8H2f//x9o
        //8faf//H2r//x9q//8fav//H2v/sh9r/w4VKlcAEBASKRAQEtoRERP/EhET/xEQEv8QEBL/EhIU/xMT
        Ff8TExXyExMVsxMTFagTEhWoExIU1BISFP8SERP/ERET/xEQEv8QDxH/Dw8R/w4OEPwODQ+EH2P/Ex9j
        /6AfZP//H2T//x9l//8fZv//H2b//x9n/8gfaP8qH2j/AA0CAAAREBIAEA8RThAQEuUREBL/ERET/xIR
        E/8SERP/EhEU9hISFHYTExUIEhEUAxMRFQISERMxERETzREQEv8QEBL/EA8R/w8OEP8ODhD/Dg0Pnw0N
        DxIfY/8AH2P/Fh9j/6kfY///H2P//x9j//8fY//MH2T/MB9m/wAfVP8AERgnABAPEQAQERMAEA8RVRAP
        EegQEBL/EBAS/xEQEvcRERN9EhETBhISFAATEhUAExIVABEREwAQEBI2EA8R0g8PEf8PDhD/Dg4P/w4N
        D6gNDQ8VDg0PAB9j/wAfY/8AH2P/HR9j/6QfY//nH2P/wh9j/zofZP8AH2P/AB9m/wAAAAAAEA8RAA8P
        EQAKDhIBDw8QYA8PEdcQDxHiEA8RgRAPEQkREBIAEhETAAAAAAARERMAEA8RABAPEQAPDhBADg4Qxg4N
        D+cODQ+iDQ0OHA0NDwAODQ8AACAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAGAIBw
        DgEAIAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAQAAHAOAAAgBAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgBAA=
</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>اعتماد مستندي</value>
  </data>
</root>