﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;

namespace Reports
{
    public partial class uc_Currency_Reports : DevExpress.XtraEditors.XtraUserControl
    {
        public bool dataModified;

        public uc_Currency_Reports()
        {
            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();

            lkp_Crnc.Properties.DataSource = Shared.lstCurrency;
            lkp_Crnc.Properties.ValueMember = "CrncId";
            lkp_Crnc.Properties.DisplayMember = "crncName";
        }

        private void uc_LinkAccount_Load(object sender, EventArgs e)
        {
           
            if (Shared.CurrencyAvailable == false)
            {
                this.TabStop = false;
                lkp_Crnc.Enabled = txtRate.Enabled = false;
                lkp_Crnc.TabStop = txtRate.TabStop = false;
            }
                     
            lkp_Crnc.EditValue = 0;//default one
        }

        private void lkp_Crnc_EditValueChanged(object sender, EventArgs e)
        {
            int crncId = Convert.ToInt32(lkp_Crnc.EditValue);
            if (crncId == 0)//default currency
            {
                txtRate.EditValue = 1;
                txtRate.Properties.ReadOnly = true;
                txtRate.TabStop = false;
            }
            else
            {
                decimal rate = Shared.lstCurrency.Where(x => x.CrncId == crncId).Select(x => x.LastRate).SingleOrDefault();
                txtRate.EditValue = rate == 0 ? 1 : rate;
                txtRate.Properties.ReadOnly = false;
                txtRate.TabStop = true;
                txtRate.EnterMoveNextControl = true;
            }
        }

        private void data_Modified(object sender, EventArgs e)
        {
            dataModified = true;
        }

        public void DoValidate()
        {
            lkp_Crnc.DoValidate();
            txtRate.DoValidate();
            dataModified = false;
        }

        private void txtRate_Spin(object sender, DevExpress.XtraEditors.Controls.SpinEventArgs e)
        {
            e.Handled = true;
        }
        
    }
}
