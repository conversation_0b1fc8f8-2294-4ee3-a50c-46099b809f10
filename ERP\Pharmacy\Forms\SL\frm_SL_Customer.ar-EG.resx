<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="labelControl15.Location" type="System.Drawing.Point, System.Drawing">
    <value>196, 108</value>
  </data>
  <data name="labelControl15.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 13</value>
  </data>
  <data name="labelControl15.Text" xml:space="preserve">
    <value>هاتف</value>
  </data>
  <data name="txtTel.Location" type="System.Drawing.Point, System.Drawing">
    <value>44, 105</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="txtTel.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="barBtnHelp.Caption" xml:space="preserve">
    <value>مساعدة</value>
  </data>
  <data name="barBtnNew.Caption" xml:space="preserve">
    <value>جديد</value>
  </data>
  <data name="barBtnDelete.Caption" xml:space="preserve">
    <value>حذف</value>
  </data>
  <data name="barBtnSave.Caption" xml:space="preserve">
    <value>حفظ</value>
  </data>
  <data name="barBtnList.Caption" xml:space="preserve">
    <value>القائمة</value>
  </data>
  <data name="barBtnClose.Caption" xml:space="preserve">
    <value>غلق</value>
  </data>
  <data name="barDockControlBottom.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 793</value>
  </data>
  <data name="barDockControlLeft.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 765</value>
  </data>
  <data name="barDockControlRight.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 765</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>558, 793</value>
  </data>
  <data name="xtraTabControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>21, 57</value>
  </data>
  <data name="labelControl50.Location" type="System.Drawing.Point, System.Drawing">
    <value>262, 183</value>
  </data>
  <data name="labelControl48.Location" type="System.Drawing.Point, System.Drawing">
    <value>31, 209</value>
  </data>
  <data name="labelControl47.Location" type="System.Drawing.Point, System.Drawing">
    <value>31, 234</value>
  </data>
  <data name="labelControl46.Location" type="System.Drawing.Point, System.Drawing">
    <value>262, 241</value>
  </data>
  <data name="labelControl45.Location" type="System.Drawing.Point, System.Drawing">
    <value>262, 213</value>
  </data>
  <data name="labelControl44.Location" type="System.Drawing.Point, System.Drawing">
    <value>31, 183</value>
  </data>
  <data name="labelControl43.Location" type="System.Drawing.Point, System.Drawing">
    <value>33, 62</value>
  </data>
  <data name="labelControl42.Location" type="System.Drawing.Point, System.Drawing">
    <value>33, 7</value>
  </data>
  <data name="labelControl41.Location" type="System.Drawing.Point, System.Drawing">
    <value>408, 209</value>
  </data>
  <data name="labelControl41.Size" type="System.Drawing.Size, System.Drawing">
    <value>26, 13</value>
  </data>
  <data name="labelControl41.Text" xml:space="preserve">
    <value>الدولة</value>
  </data>
  <data name="lkp_country.Location" type="System.Drawing.Point, System.Drawing">
    <value>274, 206</value>
  </data>
  <data name="lkp_country.Properties.Columns8" xml:space="preserve">
    <value>اسم الدولة</value>
  </data>
  <data name="lkp_country.Size" type="System.Drawing.Size, System.Drawing">
    <value>123, 20</value>
  </data>
  <data name="txt_Governate.Location" type="System.Drawing.Point, System.Drawing">
    <value>44, 206</value>
  </data>
  <data name="txt_Governate.Size" type="System.Drawing.Size, System.Drawing">
    <value>147, 20</value>
  </data>
  <data name="Governate.Location" type="System.Drawing.Point, System.Drawing">
    <value>197, 209</value>
  </data>
  <data name="Governate.Size" type="System.Drawing.Size, System.Drawing">
    <value>42, 13</value>
  </data>
  <data name="Governate.Text" xml:space="preserve">
    <value>المحافظة</value>
  </data>
  <data name="txt_BuildingNumber.Location" type="System.Drawing.Point, System.Drawing">
    <value>274, 234</value>
  </data>
  <data name="txt_BuildingNumber.Size" type="System.Drawing.Size, System.Drawing">
    <value>123, 20</value>
  </data>
  <data name="BuildingNumber.Location" type="System.Drawing.Point, System.Drawing">
    <value>403, 237</value>
  </data>
  <data name="BuildingNumber.Size" type="System.Drawing.Size, System.Drawing">
    <value>51, 13</value>
  </data>
  <data name="BuildingNumber.Text" xml:space="preserve">
    <value>رقم العمارة</value>
  </data>
  <data name="txt_Street.Location" type="System.Drawing.Point, System.Drawing">
    <value>44, 231</value>
  </data>
  <data name="txt_Street.Size" type="System.Drawing.Size, System.Drawing">
    <value>147, 20</value>
  </data>
  <data name="txt_Neighborhood.Location" type="System.Drawing.Point, System.Drawing">
    <value>274, 260</value>
  </data>
  <data name="txt_Neighborhood.Size" type="System.Drawing.Size, System.Drawing">
    <value>123, 20</value>
  </data>
  <data name="labelControl39.Location" type="System.Drawing.Point, System.Drawing">
    <value>198, 234</value>
  </data>
  <data name="labelControl39.Size" type="System.Drawing.Size, System.Drawing">
    <value>32, 13</value>
  </data>
  <data name="labelControl39.Text" xml:space="preserve">
    <value>الشارع</value>
  </data>
  <data name="labelControl40.Location" type="System.Drawing.Point, System.Drawing">
    <value>404, 266</value>
  </data>
  <data name="labelControl40.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 13</value>
  </data>
  <data name="labelControl40.Text" xml:space="preserve">
    <value>الحي</value>
  </data>
  <data name="lkp_CollectEmp.Location" type="System.Drawing.Point, System.Drawing">
    <value>45, 524</value>
  </data>
  <data name="lkp_CollectEmp.Size" type="System.Drawing.Size, System.Drawing">
    <value>353, 20</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="lkp_CollectEmp.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl38.Location" type="System.Drawing.Point, System.Drawing">
    <value>404, 527</value>
  </data>
  <data name="labelControl38.Size" type="System.Drawing.Size, System.Drawing">
    <value>71, 13</value>
  </data>
  <data name="labelControl38.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="labelControl38.Text" xml:space="preserve">
    <value>مندوب التحصيل</value>
  </data>
  <data name="btn_AddRegion.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 410</value>
  </data>
  <data name="labelControl36.Location" type="System.Drawing.Point, System.Drawing">
    <value>197, 317</value>
  </data>
  <data name="labelControl36.Size" type="System.Drawing.Size, System.Drawing">
    <value>37, 13</value>
  </data>
  <data name="labelControl36.Text" xml:space="preserve">
    <value>المنطقة</value>
  </data>
  <data name="lkp_Regions.Location" type="System.Drawing.Point, System.Drawing">
    <value>43, 312</value>
  </data>
  <data name="lkp_Regions.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Inherit</value>
  </data>
  <data name="labelControl37.Location" type="System.Drawing.Point, System.Drawing">
    <value>197, 6</value>
  </data>
  <data name="labelControl37.Size" type="System.Drawing.Size, System.Drawing">
    <value>49, 13</value>
  </data>
  <data name="labelControl37.Text" xml:space="preserve">
    <value>نوع العميل</value>
  </data>
  <data name="cmbCsType.Location" type="System.Drawing.Point, System.Drawing">
    <value>45, 4</value>
  </data>
  <data name="cmbCsType.Properties.Items" xml:space="preserve">
    <value>طبيعى</value>
  </data>
  <data name="cmbCsType.Properties.Items3" xml:space="preserve">
    <value>اعتبارى</value>
  </data>
  <data name="cmbCsType.Properties.Items6" xml:space="preserve">
    <value>أجنبي</value>
  </data>
  <data name="labelControl35.Location" type="System.Drawing.Point, System.Drawing">
    <value>403, 317</value>
  </data>
  <data name="labelControl35.Size" type="System.Drawing.Size, System.Drawing">
    <value>38, 13</value>
  </data>
  <data name="labelControl35.Text" xml:space="preserve">
    <value>التسليم</value>
  </data>
  <data name="lkpDelivery.Location" type="System.Drawing.Point, System.Drawing">
    <value>251, 314</value>
  </data>
  <data name="txt_Rep_ID.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_Rep_ID.Location" type="System.Drawing.Point, System.Drawing">
    <value>46, 468</value>
  </data>
  <data name="txt_Rep_ID.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="labelControl33.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl33.Location" type="System.Drawing.Point, System.Drawing">
    <value>198, 471</value>
  </data>
  <data name="labelControl33.Size" type="System.Drawing.Size, System.Drawing">
    <value>52, 13</value>
  </data>
  <data name="labelControl33.Text" xml:space="preserve">
    <value>رقم البطاقة</value>
  </data>
  <data name="txt_Rep_Phone.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_Rep_Phone.Location" type="System.Drawing.Point, System.Drawing">
    <value>251, 468</value>
  </data>
  <data name="txt_Rep_Phone.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="labelControl34.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl34.Location" type="System.Drawing.Point, System.Drawing">
    <value>405, 471</value>
  </data>
  <data name="labelControl34.Size" type="System.Drawing.Size, System.Drawing">
    <value>86, 13</value>
  </data>
  <data name="labelControl34.Text" xml:space="preserve">
    <value>هاتف ممثل العميل</value>
  </data>
  <data name="labelControl32.Location" type="System.Drawing.Point, System.Drawing">
    <value>197, 32</value>
  </data>
  <data name="labelControl32.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 13</value>
  </data>
  <data name="labelControl32.Text" xml:space="preserve">
    <value>المجموعة</value>
  </data>
  <data name="lkp_Group.Location" type="System.Drawing.Point, System.Drawing">
    <value>44, 29</value>
  </data>
  <data name="lkp_Group.Properties.Columns" xml:space="preserve">
    <value>NameAr</value>
  </data>
  <data name="txt_Bank.Location" type="System.Drawing.Point, System.Drawing">
    <value>251, 363</value>
  </data>
  <data name="labelControl28.Location" type="System.Drawing.Point, System.Drawing">
    <value>406, 366</value>
  </data>
  <data name="labelControl28.Text" xml:space="preserve">
    <value>البنك</value>
  </data>
  <data name="txt_BankAccNum.Location" type="System.Drawing.Point, System.Drawing">
    <value>46, 363</value>
  </data>
  <data name="labelControl31.Location" type="System.Drawing.Point, System.Drawing">
    <value>204, 366</value>
  </data>
  <data name="labelControl31.Size" type="System.Drawing.Size, System.Drawing">
    <value>31, 13</value>
  </data>
  <data name="labelControl31.Text" xml:space="preserve">
    <value>حساب</value>
  </data>
  <data name="txt_IdNumber.Location" type="System.Drawing.Point, System.Drawing">
    <value>273, 180</value>
  </data>
  <data name="txt_IdNumber.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="txt_IdNumber.Size" type="System.Drawing.Size, System.Drawing">
    <value>124, 20</value>
  </data>
  <data name="labelControl26.Location" type="System.Drawing.Point, System.Drawing">
    <value>404, 183</value>
  </data>
  <data name="labelControl26.Size" type="System.Drawing.Size, System.Drawing">
    <value>60, 13</value>
  </data>
  <data name="labelControl26.Text" xml:space="preserve">
    <value>الرقم المدني</value>
  </data>
  <data name="lblOpenAmount.Location" type="System.Drawing.Point, System.Drawing">
    <value>96, 5</value>
  </data>
  <data name="lblOpenAmount.Size" type="System.Drawing.Size, System.Drawing">
    <value>27, 13</value>
  </data>
  <data name="lblOpenAmount.Text" xml:space="preserve">
    <value>المبلغ</value>
  </data>
  <data name="lblOpenDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>223, 8</value>
  </data>
  <data name="lblOpenDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>25, 13</value>
  </data>
  <data name="lblOpenDate.Text" xml:space="preserve">
    <value>بتاريخ</value>
  </data>
  <data name="txtOpenAmount.Location" type="System.Drawing.Point, System.Drawing">
    <value>9, 5</value>
  </data>
  <data name="txtOpenAmount.Size" type="System.Drawing.Size, System.Drawing">
    <value>81, 20</value>
  </data>
  <data name="dtOpenBalance.Location" type="System.Drawing.Point, System.Drawing">
    <value>129, 5</value>
  </data>
  <data name="cmbIsCredit.Location" type="System.Drawing.Point, System.Drawing">
    <value>279, 5</value>
  </data>
  <data name="cmbIsCredit.Properties.Items" xml:space="preserve">
    <value>مدين</value>
  </data>
  <data name="cmbIsCredit.Properties.Items3" xml:space="preserve">
    <value>دائن</value>
  </data>
  <data name="cmbIsCredit.Size" type="System.Drawing.Size, System.Drawing">
    <value>71, 20</value>
  </data>
  <data name="pnlOpenBlnce.Location" type="System.Drawing.Point, System.Drawing">
    <value>31, 608</value>
  </data>
  <data name="pnlOpenBlnce.Size" type="System.Drawing.Size, System.Drawing">
    <value>371, 33</value>
  </data>
  <data name="lblOpenBalance.Location" type="System.Drawing.Point, System.Drawing">
    <value>408, 620</value>
  </data>
  <data name="lblOpenBalance.Size" type="System.Drawing.Size, System.Drawing">
    <value>63, 13</value>
  </data>
  <data name="lblOpenBalance.Text" xml:space="preserve">
    <value>رصيد افتتاحي</value>
  </data>
  <data name="labelControl24.Location" type="System.Drawing.Point, System.Drawing">
    <value>401, 32</value>
  </data>
  <data name="labelControl24.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 13</value>
  </data>
  <data name="labelControl24.Text" xml:space="preserve">
    <value>الفئة</value>
  </data>
  <data name="lkp_Category.Location" type="System.Drawing.Point, System.Drawing">
    <value>249, 29</value>
  </data>
  <data name="uc_LinkAccount1.Location" type="System.Drawing.Point, System.Drawing">
    <value>25, 642</value>
  </data>
  <data name="uc_LinkAccount1.Size" type="System.Drawing.Size, System.Drawing">
    <value>386, 73</value>
  </data>
  <data name="txtCusCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>249, 3</value>
  </data>
  <data name="txtCusCode.Properties.Mask.EditMask" xml:space="preserve">
    <value>d</value>
  </data>
  <data name="txtRepFName.Location" type="System.Drawing.Point, System.Drawing">
    <value>46, 415</value>
  </data>
  <data name="txtRepFName.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="txtShipping.Location" type="System.Drawing.Point, System.Drawing">
    <value>46, 338</value>
  </data>
  <data name="txtShipping.Size" type="System.Drawing.Size, System.Drawing">
    <value>353, 22</value>
  </data>
  <data name="labelControl20.Location" type="System.Drawing.Point, System.Drawing">
    <value>198, 418</value>
  </data>
  <data name="labelControl20.Size" type="System.Drawing.Size, System.Drawing">
    <value>32, 13</value>
  </data>
  <data name="labelControl20.Text" xml:space="preserve">
    <value>اسم ج</value>
  </data>
  <data name="txtAddress.Location" type="System.Drawing.Point, System.Drawing">
    <value>46, 286</value>
  </data>
  <data name="txtAddress.Size" type="System.Drawing.Size, System.Drawing">
    <value>353, 22</value>
  </data>
  <data name="lkp_SalesEmp.Location" type="System.Drawing.Point, System.Drawing">
    <value>46, 492</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns1" xml:space="preserve">
    <value> المندوب</value>
  </data>
  <data name="lkp_SalesEmp.Size" type="System.Drawing.Size, System.Drawing">
    <value>353, 20</value>
  </data>
  <data name="txt_DueDays.Location" type="System.Drawing.Point, System.Drawing">
    <value>329, 582</value>
  </data>
  <data name="txt_DueDays.Size" type="System.Drawing.Size, System.Drawing">
    <value>71, 20</value>
  </data>
  <data name="labelControl25.Location" type="System.Drawing.Point, System.Drawing">
    <value>405, 495</value>
  </data>
  <data name="labelControl25.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 13</value>
  </data>
  <data name="labelControl25.Text" xml:space="preserve">
    <value>مندوب البيع</value>
  </data>
  <data name="labelControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>404, 288</value>
  </data>
  <data name="labelControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>31, 13</value>
  </data>
  <data name="labelControl2.Text" xml:space="preserve">
    <value>العنوان</value>
  </data>
  <data name="labelControl16.Location" type="System.Drawing.Point, System.Drawing">
    <value>406, 585</value>
  </data>
  <data name="labelControl16.Size" type="System.Drawing.Size, System.Drawing">
    <value>71, 13</value>
  </data>
  <data name="labelControl16.Text" xml:space="preserve">
    <value>مدة الإستحقاق</value>
  </data>
  <data name="txt_RepFJob.Location" type="System.Drawing.Point, System.Drawing">
    <value>46, 442</value>
  </data>
  <data name="txt_RepFJob.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="txt_RepFJob.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 20</value>
  </data>
  <data name="labelControl21.Location" type="System.Drawing.Point, System.Drawing">
    <value>198, 445</value>
  </data>
  <data name="labelControl21.Size" type="System.Drawing.Size, System.Drawing">
    <value>44, 13</value>
  </data>
  <data name="labelControl21.Text" xml:space="preserve">
    <value>الوظيفة ج</value>
  </data>
  <data name="txtCusNameAr.Location" type="System.Drawing.Point, System.Drawing">
    <value>44, 55</value>
  </data>
  <data name="txtCusNameAr.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="txtCusNameAr.Size" type="System.Drawing.Size, System.Drawing">
    <value>353, 20</value>
  </data>
  <data name="txt_RepresentativeJob.Location" type="System.Drawing.Point, System.Drawing">
    <value>251, 442</value>
  </data>
  <data name="txt_RepresentativeJob.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="labelControl4.Location" type="System.Drawing.Point, System.Drawing">
    <value>401, 58</value>
  </data>
  <data name="labelControl4.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 13</value>
  </data>
  <data name="labelControl4.Text" xml:space="preserve">
    <value>اسم العميل</value>
  </data>
  <data name="labelControl18.Location" type="System.Drawing.Point, System.Drawing">
    <value>405, 445</value>
  </data>
  <data name="labelControl18.Size" type="System.Drawing.Size, System.Drawing">
    <value>34, 13</value>
  </data>
  <data name="labelControl18.Text" xml:space="preserve">
    <value>الوظيفة</value>
  </data>
  <data name="txtCusNameEn.Location" type="System.Drawing.Point, System.Drawing">
    <value>44, 80</value>
  </data>
  <data name="txtCusNameEn.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="txtCusNameEn.Size" type="System.Drawing.Size, System.Drawing">
    <value>353, 20</value>
  </data>
  <data name="txt_Representative.Location" type="System.Drawing.Point, System.Drawing">
    <value>251, 416</value>
  </data>
  <data name="txt_Representative.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="labelControl3.Location" type="System.Drawing.Point, System.Drawing">
    <value>401, 83</value>
  </data>
  <data name="labelControl3.Size" type="System.Drawing.Size, System.Drawing">
    <value>66, 13</value>
  </data>
  <data name="labelControl3.Text" xml:space="preserve">
    <value>اسم العميل ج</value>
  </data>
  <data name="labelControl17.Location" type="System.Drawing.Point, System.Drawing">
    <value>405, 419</value>
  </data>
  <data name="labelControl17.Size" type="System.Drawing.Size, System.Drawing">
    <value>84, 13</value>
  </data>
  <data name="labelControl17.Text" xml:space="preserve">
    <value>اسم ممثل العميل</value>
  </data>
  <data name="labelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>401, 6</value>
  </data>
  <data name="labelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>49, 13</value>
  </data>
  <data name="labelControl1.Text" xml:space="preserve">
    <value>كود العميل</value>
  </data>
  <data name="txtManagerName.Location" type="System.Drawing.Point, System.Drawing">
    <value>46, 389</value>
  </data>
  <data name="txtManagerName.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="txtManagerName.Size" type="System.Drawing.Size, System.Drawing">
    <value>353, 20</value>
  </data>
  <data name="labelControl5.Location" type="System.Drawing.Point, System.Drawing">
    <value>401, 108</value>
  </data>
  <data name="labelControl5.Size" type="System.Drawing.Size, System.Drawing">
    <value>32, 13</value>
  </data>
  <data name="labelControl5.Text" xml:space="preserve">
    <value>محمول</value>
  </data>
  <data name="labelControl14.Location" type="System.Drawing.Point, System.Drawing">
    <value>403, 392</value>
  </data>
  <data name="labelControl14.Size" type="System.Drawing.Size, System.Drawing">
    <value>27, 13</value>
  </data>
  <data name="labelControl14.Text" xml:space="preserve">
    <value>المدير</value>
  </data>
  <data name="txtMobile.Location" type="System.Drawing.Point, System.Drawing">
    <value>249, 105</value>
  </data>
  <data name="txtMobile.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="labelControl13.Location" type="System.Drawing.Point, System.Drawing">
    <value>408, 344</value>
  </data>
  <data name="labelControl13.Text" xml:space="preserve">
    <value>الشحن</value>
  </data>
  <data name="labelControl7.Location" type="System.Drawing.Point, System.Drawing">
    <value>134, 578</value>
  </data>
  <data name="labelControl7.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 13</value>
  </data>
  <data name="labelControl7.Text" xml:space="preserve">
    <value>حد الائتمان </value>
  </data>
  <data name="txtEmail.Location" type="System.Drawing.Point, System.Drawing">
    <value>44, 155</value>
  </data>
  <data name="txtEmail.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="txtEmail.Size" type="System.Drawing.Size, System.Drawing">
    <value>353, 20</value>
  </data>
  <data name="txtMaxCredit.Location" type="System.Drawing.Point, System.Drawing">
    <value>43, 575</value>
  </data>
  <data name="txtMaxCredit.Size" type="System.Drawing.Size, System.Drawing">
    <value>85, 20</value>
  </data>
  <data name="labelControl12.Location" type="System.Drawing.Point, System.Drawing">
    <value>401, 158</value>
  </data>
  <data name="labelControl12.Size" type="System.Drawing.Size, System.Drawing">
    <value>25, 13</value>
  </data>
  <data name="labelControl12.Text" xml:space="preserve">
    <value>ايميل</value>
  </data>
  <data name="txtZip.Location" type="System.Drawing.Point, System.Drawing">
    <value>44, 130</value>
  </data>
  <data name="txtZip.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="labelControl10.Location" type="System.Drawing.Point, System.Drawing">
    <value>196, 133</value>
  </data>
  <data name="labelControl10.Size" type="System.Drawing.Size, System.Drawing">
    <value>26, 13</value>
  </data>
  <data name="labelControl10.Text" xml:space="preserve">
    <value>ص.ب</value>
  </data>
  <data name="txtFax.Location" type="System.Drawing.Point, System.Drawing">
    <value>249, 130</value>
  </data>
  <data name="txtFax.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="labelControl8.Location" type="System.Drawing.Point, System.Drawing">
    <value>134, 553</value>
  </data>
  <data name="labelControl8.Size" type="System.Drawing.Size, System.Drawing">
    <value>34, 13</value>
  </data>
  <data name="labelControl8.Text" xml:space="preserve">
    <value>ن خصم</value>
  </data>
  <data name="labelControl11.Location" type="System.Drawing.Point, System.Drawing">
    <value>403, 133</value>
  </data>
  <data name="labelControl11.Size" type="System.Drawing.Size, System.Drawing">
    <value>27, 13</value>
  </data>
  <data name="labelControl11.Text" xml:space="preserve">
    <value>فاكس</value>
  </data>
  <data name="txtDiscRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>44, 550</value>
  </data>
  <data name="txtDiscRatio.Size" type="System.Drawing.Size, System.Drawing">
    <value>84, 20</value>
  </data>
  <data name="labelControl19.Location" type="System.Drawing.Point, System.Drawing">
    <value>28, 523</value>
  </data>
  <data name="txtCity.Location" type="System.Drawing.Point, System.Drawing">
    <value>43, 180</value>
  </data>
  <data name="txtCity.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="labelControl6.Location" type="System.Drawing.Point, System.Drawing">
    <value>406, 557</value>
  </data>
  <data name="labelControl6.Size" type="System.Drawing.Size, System.Drawing">
    <value>61, 13</value>
  </data>
  <data name="labelControl6.Text" xml:space="preserve">
    <value>قائمة الأسعار</value>
  </data>
  <data name="labelControl9.Location" type="System.Drawing.Point, System.Drawing">
    <value>196, 183</value>
  </data>
  <data name="labelControl9.Size" type="System.Drawing.Size, System.Drawing">
    <value>32, 13</value>
  </data>
  <data name="labelControl9.Text" xml:space="preserve">
    <value>المدينة</value>
  </data>
  <data name="lkpPriceLevel.Location" type="System.Drawing.Point, System.Drawing">
    <value>252, 554</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns1" xml:space="preserve">
    <value>تفاصيل</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns8" xml:space="preserve">
    <value>النوع</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns15" xml:space="preserve">
    <value>اسم قائمة الأسعار</value>
  </data>
  <data name="tab_main.Size" type="System.Drawing.Size, System.Drawing">
    <value>510, 769</value>
  </data>
  <data name="tab_main.Text" xml:space="preserve">
    <value>البيانات الرئيسية</value>
  </data>
  <data name="xtraTabControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>534, 773</value>
  </data>
  <data name="btnCustomerItems.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="btnCustomerItems.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 387</value>
  </data>
  <data name="btnCustomerItems.Size" type="System.Drawing.Size, System.Drawing">
    <value>498, 23</value>
  </data>
  <data name="btnCustomerItems.Text" xml:space="preserve">
    <value>اضغط هنا لربط العميل بالأصناف</value>
  </data>
  <data name="gridVisits.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 548</value>
  </data>
  <data name="colDayName.Caption" xml:space="preserve">
    <value>اليوم</value>
  </data>
  <data name="colDayName.Width" type="System.Int32, mscorlib">
    <value>115</value>
  </data>
  <data name="colId.Caption" xml:space="preserve">
    <value>زيارة</value>
  </data>
  <data name="gridBand1.Width" type="System.Int32, mscorlib">
    <value>190</value>
  </data>
  <data name="gridBand2.Caption" xml:space="preserve">
    <value>طبيعة الزيارة</value>
  </data>
  <data name="col_Field.Caption" xml:space="preserve">
    <value>ميدانية</value>
  </data>
  <data name="col_Telephone.Caption" xml:space="preserve">
    <value>تليفونيا</value>
  </data>
  <data name="gridBand3.Caption" xml:space="preserve">
    <value>هدف الزيارة</value>
  </data>
  <data name="col_Collect.Caption" xml:space="preserve">
    <value>تحصيل</value>
  </data>
  <data name="col_Sell.Caption" xml:space="preserve">
    <value>بيع</value>
  </data>
  <data name="col_Sell.Width" type="System.Int32, mscorlib">
    <value>95</value>
  </data>
  <data name="gridBand3.Width" type="System.Int32, mscorlib">
    <value>170</value>
  </data>
  <data name="gridBand4.Caption" xml:space="preserve">
    <value>وقت الزيارة</value>
  </data>
  <data name="col_AM.Caption" xml:space="preserve">
    <value>صباحا</value>
  </data>
  <data name="col_PM.Caption" xml:space="preserve">
    <value>مساء</value>
  </data>
  <data name="col_Notes.Caption" xml:space="preserve">
    <value>ملاحظات</value>
  </data>
  <data name="gridVisits.Size" type="System.Drawing.Size, System.Drawing">
    <value>510, 221</value>
  </data>
  <data name="chk_IsActive.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="chk_IsActive.Location" type="System.Drawing.Point, System.Drawing">
    <value>150, 332</value>
  </data>
  <data name="chk_IsActive.Properties.Caption" xml:space="preserve">
    <value>عميل نشط</value>
  </data>
  <data name="chk_IsActive.Size" type="System.Drawing.Size, System.Drawing">
    <value>74, 19</value>
  </data>
  <data name="chk_IsBlocked.Location" type="System.Drawing.Point, System.Drawing">
    <value>426, 332</value>
  </data>
  <data name="chk_IsBlocked.Properties.Caption" xml:space="preserve">
    <value>حظر العميل</value>
  </data>
  <data name="chk_IsBlocked.Size" type="System.Drawing.Size, System.Drawing">
    <value>76, 19</value>
  </data>
  <data name="labelControl49.Location" type="System.Drawing.Point, System.Drawing">
    <value>245, 35</value>
  </data>
  <data name="labelControl30.Location" type="System.Drawing.Point, System.Drawing">
    <value>130, 60</value>
  </data>
  <data name="labelControl30.Size" type="System.Drawing.Size, System.Drawing">
    <value>70, 13</value>
  </data>
  <data name="labelControl30.Text" xml:space="preserve">
    <value>السجل التجاري</value>
  </data>
  <data name="labelControl27.Location" type="System.Drawing.Point, System.Drawing">
    <value>381, 35</value>
  </data>
  <data name="labelControl27.Size" type="System.Drawing.Size, System.Drawing">
    <value>72, 13</value>
  </data>
  <data name="labelControl27.Text" xml:space="preserve">
    <value>البطاقة الضريبية</value>
  </data>
  <data name="chk_IsTaxable.Location" type="System.Drawing.Point, System.Drawing">
    <value>158, 89</value>
  </data>
  <data name="chk_IsTaxable.Properties.Caption" xml:space="preserve">
    <value>العميل خاضع لضريبة المبيعات</value>
  </data>
  <data name="chk_IsTaxable.Size" type="System.Drawing.Size, System.Drawing">
    <value>221, 19</value>
  </data>
  <data name="txtTradeRegistry.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 57</value>
  </data>
  <data name="txtTradeRegistry.Size" type="System.Drawing.Size, System.Drawing">
    <value>119, 20</value>
  </data>
  <data name="txtTaxCardNumber.Location" type="System.Drawing.Point, System.Drawing">
    <value>257, 32</value>
  </data>
  <data name="txtTaxCardNumber.Size" type="System.Drawing.Size, System.Drawing">
    <value>119, 20</value>
  </data>
  <data name="txtTaxDepartment.Location" type="System.Drawing.Point, System.Drawing">
    <value>257, 57</value>
  </data>
  <data name="txtTaxDepartment.Size" type="System.Drawing.Size, System.Drawing">
    <value>119, 20</value>
  </data>
  <data name="lblTaxDepartment.Location" type="System.Drawing.Point, System.Drawing">
    <value>384, 60</value>
  </data>
  <data name="lblTaxDepartment.Size" type="System.Drawing.Size, System.Drawing">
    <value>72, 13</value>
  </data>
  <data name="lblTaxDepartment.Text" xml:space="preserve">
    <value>مأمورية الضرائب</value>
  </data>
  <data name="txtTaxFileNumber.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 32</value>
  </data>
  <data name="txtTaxFileNumber.Size" type="System.Drawing.Size, System.Drawing">
    <value>119, 20</value>
  </data>
  <data name="labelControl29.Location" type="System.Drawing.Point, System.Drawing">
    <value>130, 35</value>
  </data>
  <data name="labelControl29.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 13</value>
  </data>
  <data name="labelControl29.Text" xml:space="preserve">
    <value>رقم الملف الضريبي</value>
  </data>
  <data name="groupControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 9</value>
  </data>
  <data name="groupControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>495, 115</value>
  </data>
  <data name="groupControl1.Text" xml:space="preserve">
    <value>الضريبة</value>
  </data>
  <data name="btnEditPhoto.Location" type="System.Drawing.Point, System.Drawing">
    <value>181, 63</value>
  </data>
  <data name="btnEditPhoto.ToolTip" xml:space="preserve">
    <value>تعديل الصورة</value>
  </data>
  <data name="btnAddEmpPhoho.Location" type="System.Drawing.Point, System.Drawing">
    <value>181, 26</value>
  </data>
  <data name="btnAddEmpPhoho.ToolTip" xml:space="preserve">
    <value>تحميل الصورة</value>
  </data>
  <data name="btnDeleteEmpPhoto.Location" type="System.Drawing.Point, System.Drawing">
    <value>181, 100</value>
  </data>
  <data name="btnDeleteEmpPhoto.ToolTip" xml:space="preserve">
    <value>حذف الصورة</value>
  </data>
  <data name="btnShowPhotoes.Location" type="System.Drawing.Point, System.Drawing">
    <value>181, 148</value>
  </data>
  <data name="btnShowPhotoes.Text" xml:space="preserve">
    <value>عرض
 الصور</value>
  </data>
  <data name="labelControl22.Location" type="System.Drawing.Point, System.Drawing">
    <value>431, 30</value>
  </data>
  <data name="labelControl22.Size" type="System.Drawing.Size, System.Drawing">
    <value>32, 13</value>
  </data>
  <data name="labelControl22.Text" xml:space="preserve">
    <value>المسار</value>
  </data>
  <data name="txtImagePath.Location" type="System.Drawing.Point, System.Drawing">
    <value>255, 26</value>
  </data>
  <data name="lstPhotos.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 26</value>
  </data>
  <data name="lstPhotos.Size" type="System.Drawing.Size, System.Drawing">
    <value>171, 160</value>
  </data>
  <data name="btnBrowse.Location" type="System.Drawing.Point, System.Drawing">
    <value>224, 26</value>
  </data>
  <data name="btnBrowse.ToolTip" xml:space="preserve">
    <value>استعراض</value>
  </data>
  <data name="labelControl23.Location" type="System.Drawing.Point, System.Drawing">
    <value>431, 56</value>
  </data>
  <data name="labelControl23.Size" type="System.Drawing.Size, System.Drawing">
    <value>54, 13</value>
  </data>
  <data name="labelControl23.Text" xml:space="preserve">
    <value>اسم الصورة</value>
  </data>
  <data name="txtImageDesc.Location" type="System.Drawing.Point, System.Drawing">
    <value>255, 52</value>
  </data>
  <data name="groupControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 134</value>
  </data>
  <data name="groupControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>499, 192</value>
  </data>
  <data name="groupControl2.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="groupControl2.Text" xml:space="preserve">
    <value>صور المرفقات</value>
  </data>
  <data name="tab_docs.Size" type="System.Drawing.Size, System.Drawing">
    <value>510, 769</value>
  </data>
  <data name="tab_docs.Text" xml:space="preserve">
    <value>صفحة 2</value>
  </data>
  <data name="btnNext.Location" type="System.Drawing.Point, System.Drawing">
    <value>75, 32</value>
  </data>
  <data name="btnNext.ToolTip" xml:space="preserve">
    <value>التالي</value>
  </data>
  <data name="btnPrev.Location" type="System.Drawing.Point, System.Drawing">
    <value>45, 32</value>
  </data>
  <data name="btnPrev.ToolTip" xml:space="preserve">
    <value>السابق</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAA
        AAAfbf8AH2z/AB9r/x8fa/+rH2v/9h9r/9AfbP9HH2//AB9u/wAfcP8AAAAAAB9p/wAfZv8AH2D/Ah9j
        /2cfY//iH2P/7R9j/4YfY/8KH2P/AB9j/wAAAAAADw4QAA4NDwAODQ4ADQwORwwMDdAMCw32CwsMqwsL
        DB8MCw0ADAsNAB9u/wAfbf8WH23/qx9t//8fbv//H27//x9u/9gfb/89H3D/AB9y/wAfcf8AH23/AB9n
        /wAfZ/9cH2X/6x9j//8fY///H2P/9x9j/38fY/8GH2P/AB5e8QAPDhAADw4QAA4NDz0NDQ/YDQ0O/w0M
        Dv8MDA3/DAsNqwwLDRYMCw0AH2//FR9v/6Ifb///H3D//x9x//8fcf//H3H//x9x/9Mfcv82IHD/AiBw
        /wQfcP8DH2z/VB9q/+gfZ///H2X//x9j//8fY///H2P/9x9j/3cfY/8FH2P+ABAPEQAQDxE1Dw4Q0w8O
        EP8ODg//Dg0P/w0NDv8MDA3/DAsNogwLDRUfcP+MH3H//R9y//8ec///HnP//x50//8edP//HnP//x5z
        /9cec/+pH3L/qR9x/6wfbv/mH23//x9q//8faP//H2X//x9j//8fY///H2P/8h9j/04bTb8AERASERAQ
        ErsQEBL/EA8R/w8PEf8PDhD/Dg0P/w0ND/8MDA79DAwNjB5z/9QedP//HnX//x52//8edv//Hnb//x52
        //8edv//Hnb//x51//8edP//HnP//x9x//8fb///H23//x9r//8faP//H2X//x9j//8fY///H2P/gxg4
        hAASERM1ERET7hERE/8REBL/EBAS/xAPEf8PDhD/Dg4Q/w0ND/8NDA7THnX/wx52//8ed///Hnj//x54
        //8eef//Hnn//x54//8eeP/9Hnf/8h52//Iedf/zHnP//h9x//8fb///H23//x9q//8fZ///H2T//x9j
        //8fY/90GkGeABISFCYTEhThEhIU/xIRE/8RERP/EBAS/xAPEf8PDhD/Dg4Q/w4ND78ed/9UHnj/5x55
        //8eev//Hnr//x57//8ee///Hnr/+x56/5AeeP88Hnf/PB52/0Eedf+wHnP//x9x//8fb///H2z//x9p
        //8fZv//H2T/zB9j/yQdV9wAExIVAxMTFYMTEhX8ExIU/xISFP8RERP/ERAS/xAPEf8PDhDkDg4QTx50
        /wAeev9dHnv/7R58//8efP//Hn3//x59//0efP+UHnv/DB55/wAed/8AHnb/AB52/x8edf+5HnP//x9x
        //8fbv//H2v//x9o/9YfZf85H2f/AAAAAAAUExYAFBMWDRQTFpQUExX9ExIV/xISFP8SERP/ERAS6hAP
        EVYTDhQAHnr/AB58/wEefP9nHn3/7x5+//8efv/9Hn7/nR59/xAefP8AHnv/AB52/wAedf8AHnX/AB51
        /yUedP2+H3P//x9x//8fbPvbH2r/Px9o/wAfZv8AH2P/ABQTFgAUExYAFBQWEBQUFp0UExb9ExIV/xIS
        FOwRERNfIxkMABAQEQAee/8AHn3/AB5+/wcefv+xHn///x5//+oef/8tHn//AB59/wAefP8AAAAAAB52
        /wAeV/8AGkKGABk+eUYaQoPzG0KG/xo0ZZIbRZkAH2v/AB9o/wAAAAAAFBQWABQUFgAVFBcAFRQXLRUU
        F+oUExb/ExMVqxMTFAUSERQAEBASAAAAAAAegP8AHn//Ax6A/6kegP//HoD/5x6A/ycegP8AHoD/AAAA
        AAAAAAAAAAAAABpCgQAXFRcAFhMSOhcVFvAXFRb/GBUWjhgYHgAcO3gAAAAAAAAAAAAAAAAAFxYZABYV
        GAAWFRgnFRUX5xUUF/8UExajExIVAhQTFgAAAAAAHoD/AB6A/wAegP8IHoD/sx6B//8egf/rHoH/Lx6B
        /wAegv8AHoL/AAAAAAAXFhkAFBQXABcXGgAXFxpIGBca9BgYG/8ZGBuTGRkcABoaHQAbGx4AAAAAABkZ
        HAAYGBsAFxcZABcWGS8WFhjrFRUX/xUUF6wUFBYGFBMWABQTFQAef/8AHoD/Ah6A/20egf/xHoH//x6B
        //4egv+kHoL/Ex6C/wAegv8AFhYYABsZIAAXFxoAFxcaKRgXGsMYGBv/GRkc/xoZHN4bGh1FHBoeABwb
        HwAdHCAAGhkdABkZHAAYGBsTGBcapBcWGf4WFRj/FRQX7xQUFmYTEBMBExMVAB59/wIegP9kHoD/8B6B
        //8egv//HoL//x6C//8egv+cHoL/EB6B/wAaRX4AFxYZABcWGSQXFxq/GBgb/xkZHP8aGR3/Gxoe/xwb
        HtkcGx89HBwfABoYIQAbGh0AGxodEBkZHJwYGBv/Fxca/xYWGf8VFRf/FBQW7RQTFVwBABAAHn//Vx5/
        /+kegP//HoH//x6C//8egv//HoL//x6C//4egf+JHoD/BBgxUQAWFhgSFxYZsRgXGv8ZGBv/Ghkc/xsa
        Hf8cGx7/HBwf/x0cIM8eHSAmHRwgAB0cHwQbGh6JGhkc/hkYG/8YFxr/FxYZ/xYVGP8VFBf/ExMV5hMS
        FFIefv/EHn///x6A//8egf//HoL//x6C//8egv//HoL//x6B/+Megf8nGUB0ABYWGEwXFhn4GBca/xkY
        G/8aGh3/Gxse/xwcH/8dHCD/Hh0h/x4dIXUdHCAAHBsfJxwbHuMaGh3/GRkc/xgXGv8XFhn/FhUY/xUU
        F/8UExX/ExIUwR5+/9Uef///HoD//x6B//8egf//HoL//x6C//8egf//HoH/7h6B/zQaRX4AFhYZWhcW
        GfwYGBv/GRkc/xsaHf8cGx7/HRwg/x4dIf8fHiL/Hx4igx4dIQAdHB80HBsf7hsaHf8aGRz/GBgb/xcX
        Gf8WFRj/FRQX/xQTFf8TEhXSHn7/jR5///0egP//HoD//x6B//8egf//HoH//x6B//8egf+4HoD/EBk3
        YAAWFhkqFxYZ2xgYG/8ZGRz/Gxod/xwbHv8dHCD/Hh0h/x8eIu8gHyNKHx4iABwcHxAcGx64Gxod/xoZ
        HP8YGBv/Fxca/xYVGP8VFBf/FBMV/BMSFYgefv8VHn7/oh5///8egP//HoD//x6B//8egf//HoD/0B6A
        /zIegf8AFxIRABgaHQAXFxpQGBca5hkYHP8aGh3/Gxse/x0cH/8dHSD1Hh0hch8eIwQfHiIAGxseABwb
        HjIaGh3QGRkc/xgXG/8XFhn/FhUY/xUUF/8UExafExIVEx5+/wAefv8XHn7/qx5///8ef///Hn///x6A
        /9UegP85HoD/AB5//wAYIzUAFxcaABgYGwAYFxpXGRgb6hoZHP8bGh7/HBse+B0cH3weHSEFHh0hAB8e
        IgAbGh0AGxodABoZHDkZGBvVGBca/xcWGf8WFRj/FRQXqBQUFhUUExYAHn7/AB5+/wAefv8dHn7/yR5+
        //8efv/yHn//Sh5//wAef/8AHoD/AAAAAAAYFxoAGBcaABUXGgAZGBtUGRkcwxoaHc0bGh5yHBseCB0c
        HwAeHSEAAAAAABsaHQAaGRwAGRgbABgYG0oYFxryFxYZ/xYVGMUVFBcbFRQXABQUFgAefv8AHnz/AB56
        /wIefP+oHn3//x59/+ceff8lHn3/AB5//wAAAAAAAAAAAAAAAAAYGBoAGBgbABgXGgAaGRwNGhkdERkZ
        HAAbGh4AGxoeAAAAAAAAAAAAAAAAABkZHAAXFxoAFxcaJRcWGecWFRj/FRUXohMSFQEVFBcAFRQXAB50
        /wAed/8AHnn/CR56/7Qee///Hnv/6x57/y4eev8AHnj/AB53/wAAAAAAFRQXABYVGAAWFRgAFxYZJBgX
        GnEYFxp6GRgbNBkOEQAYGBsAGBcaAAAAAAAWFhgAFhYYABYWGQAXFhkuFhYY6xUVF/8VFBeuFBQWBxMT
        FQASERQAHnL/AB11/wMedv9zHnf/8x54//8eef/+Hnn/oR55/xEed/8AHnb/ABMSFgAbFx0AFRQXABUV
        Fy4WFhjJFxYZ/xgXGv8YFxreGBcaShcXGgAYFxoAFxYZABYVGAAWFRgAFhYYERYWGKEWFRj+FRQX/xQT
        FvETEhVuEhETAxIREwAgb/8CH3L/aR50//Iedf//Hnb//x53//8ed//+Hnf/lx52/w0edf8AGEOMABQT
        FgAUExYnFRQXxBUVF/8WFhj/FxYZ/xcXGv8XFxrdFxcaQRYWGQAWFRgAFhUYABYVGA0WFRiXFhUY/hUU
        F/8UExb/ExMV/xISFPARERNlEQ4RAh9t/1sfb//sH3H//x9y//8ec///HnT//x50//8edP/9HnT/hx9y
        /wUVKUwAEhIUFBMSFbYUExb/FBQW/xERE/8QEBL/FhUY/xYWGf8XFhnXFhYYYBYVGE4WFRhOFhUYmBUV
        F/wVFBf/FBMW/xMTFf8SEhT/ERET/xEQEuoQDxFXH2v/xh9s//8fbv//H2///x9w//8fcf//H3H//x9x
        //8fcf/kH3D/KBc3cQASERNOEhIU+BMSFf8SERT/DAwN/woJC/8QDxH/FRUX/xYVGP8WFRj7FRUX+BUU
        F/gVFBf/FBQW/xQTFv8TEhX/EhIU/xIRE/8REBL/EA8R/w8PEMMfZ//UH2n//x9r//8fbP//H23//x9u
        //8fbv//H27//x9u/+4fbv80Fzl6ABEQE1oRERP8EhIU/xERE/8ODg//DAwN/xAPEf8UExb/FBQW/xQU
        Fv8UFBb/FBMW/xQTFv8TExX/ExIU/xISFP8SERP/ERAS/xAQEv8PDxH/Dg4Q0R9l/4kfZf/8H2f//x9o
        //8faf//H2r//x9q//8fav//H2v/sh9r/w4VKlcAEBASKRAQEtoRERP/EhET/xEQEv8QEBL/EhIU/xMT
        Ff8TExXyExMVsxMTFagTEhWoExIU1BISFP8SERP/ERET/xEQEv8QDxH/Dw8R/w4OEPwODQ+EH2P/Ex9j
        /6AfZP//H2T//x9l//8fZv//H2b//x9n/8gfaP8qH2j/AA0CAAAREBIAEA8RThAQEuUREBL/ERET/xIR
        E/8SERP/EhEU9hISFHYTExUIEhEUAxMRFQISERMxERETzREQEv8QEBL/EA8R/w8OEP8ODhD/Dg0Pnw0N
        DxIfY/8AH2P/Fh9j/6kfY///H2P//x9j//8fY//MH2T/MB9m/wAfVP8AERgnABAPEQAQERMAEA8RVRAP
        EegQEBL/EBAS/xEQEvcRERN9EhETBhISFAATEhUAExIVABEREwAQEBI2EA8R0g8PEf8PDhD/Dg4P/w4N
        D6gNDQ8VDg0PAB9j/wAfY/8AH2P/HR9j/6QfY//nH2P/wh9j/zofZP8AH2P/AB9m/wAAAAAAEA8RAA8P
        EQAKDhIBDw8QYA8PEdcQDxHiEA8RgRAPEQkREBIAEhETAAAAAAARERMAEA8RABAPEQAPDhBADg4Qxg4N
        D+cODQ+iDQ0OHA0NDwAODQ8AACAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAGAIBw
        DgEAIAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAQAAHAOAAAgBAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgBAA=
</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>بيانات العميل</value>
  </data>
</root>