using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using DAL;
using System.Linq;
using System.Data;
using DevExpress.XtraEditors;

using System.Windows.Forms;

namespace Reports
{
    public partial class rpt_EmployeeDetails : DevExpress.XtraReports.UI.XtraReport
    {
        string empCode, empStatus, empName, empNameF, dep, group, job,
            Qualification, birthDate, gender, nationality, nationalId, Insturance, Driving, InsturanceDate, nationalEndDate, Religion,
            birthPlace, Marital, contract, contractEndDdate, Address, MilitryStatus, mob1;
        string date = DateTime.Now.ToShortDateString();
        DataTable dt_inv_details;

        int currId;

        public rpt_EmployeeDetails()
        {
            InitializeComponent();
        }
        public rpt_EmployeeDetails(string _empCode, string _empStatus, string _empName, string _empNameF, string _dep, string _group,
            string _job, string _Qualification, string _birthDate, string _birthPlace, string _gender, string _nationality, string _nationalId, string _nationalEndDate,
            string _Insturance, string _InsturanceDate, string _Religion, string _Marital, string _contract, string _contractEndDdate, string _Address, string _MilitryStatus, string _mob1)
        {
            InitializeComponent();
            empCode = _empCode;
            empStatus = _empStatus;
            empName = _empName;
            empNameF = _empNameF;
            dep = _dep;
            group = _group;
            job = _job;
            Qualification = _Qualification;
            birthDate = _birthDate;
            birthPlace = _birthPlace;
            gender = _gender;
            nationality = _nationality;
            nationalId = _nationalId;
            nationalEndDate = _nationalEndDate;
            Insturance = _Insturance;
            InsturanceDate = _InsturanceDate;
            Religion = _Religion;
           Marital = _Marital;
            mob1 = _mob1;
            contract = _contract;
            contractEndDdate = _contractEndDdate;
            Address = _Address;
            MilitryStatus = _MilitryStatus;
            getReportHeader();
            LoadData();            
        }
        
        

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                lblCompName.Text = Shared.IsEnglish ? "comp.CmpNameEn" : comp.CmpNameAr;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        public void LoadData()
        {
            lbl_date.Text = date;

            lbl_empCode.Text = empCode;
            lbl_empStatus.Text = empStatus;
            lbl_empName.Text = empName;
            lbl_empNameF.Text = empNameF;
            lbl_dep.Text = dep;
            lbl_group.Text = group;
            lbl_job.Text = job;
            lbl_Qualification.Text = Qualification;
            lbl_birthDate.Text = birthDate;
            lbl_birthPlace.Text = birthPlace;
            lbl_gender.Text = gender;
            lbl_nationality.Text = nationality;
            lbl_nationalId.Text = nationalId;
            lbl_nationalEndDate.Text = nationalEndDate;
            lbl_Insturance.Text = Insturance;
            lbl_InsturanceDate.Text = InsturanceDate;
            lbl_Religion.Text = Religion;
            lbl_Marital.Text = Marital;

            lbl_contract.Text = contract;
            lbl_contractEndDdate.Text = contractEndDdate;
            lbl_Address.Text = Address;
            lbl_MilitryStatus.Text = MilitryStatus;
            txtMobile.Text = mob1;
            getReportHeader();
        }

    }
}
