﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraPrinting;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraReports.UI;

namespace Pharmacy.Forms
{
    public partial class frm_IC_StoresList : DevExpress.XtraEditors.XtraForm
    {
        public frm_IC_StoresList()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

        }

        private void frm_IC_StoresList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            LoadPrivilege();
            ErpUtils.Tab_Enter_Process(grdStores);
            ErpUtils.ColumnChooser(grdStores);

            if (!Shared.ItemsPostingAvailable)
                col_CostCenter.Visible = col_CostCenter.OptionsColumn.ShowInCustomizationForm = false;

            Get_stores();
        }


        private void barBtn_New_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_IC_Store)))
            {
                frm_IC_Store frm = new frm_IC_Store(0, FormAction.Add);
                frm.BringToFront();
                frm.Show();
            }
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Open_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Open_Selected_Store();
        }
        
        private void barBtn_Refresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Get_stores();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {            
            grdStores.MinimumSize = grdStores.Size;
            new Reports.rpt_Template(this.Text, "", "", "", grdStores, false).ShowPreview();
            grdStores.MinimumSize = new Size(0, 0);
        }

        private void grdStores_DoubleClick(object sender, EventArgs e)
        {
            Open_Selected_Store();
        }


        private void Open_Selected_Store()
        {
            var view = grdStores.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            int inv_id = Convert.ToInt32(view.GetRowCellValue(focused_row_index, colStoreId));

            if (ErpUtils.IsFormOpen(typeof(frm_IC_Store)))
                Application.OpenForms["frm_IC_Store"].Close();

            new frm_IC_Store(inv_id, FormAction.Edit).Show();
        }

        private void Get_stores()
        {
            int focusedIndex = (grdStores.FocusedView as GridView).FocusedRowHandle;
            DAL.ERPDataContext pharm = new DAL.ERPDataContext();
            var Stores = (from c in pharm.IC_Stores
                          //from cc in pharm.ACC_CostCenters.Where(cc=> cc.CostCenterId == c.CostCenter).DefaultIfEmpty()
                          orderby c.StoreCode
                          select new
                          {
                              c.Address,                              
                              c.StoreCode,
                              c.StoreId,
                              c.StoreNameAr,
                              c.StoreNameEn,
                              c.Tel,
                              c.ParentId,
                              c.CostMethod,
                              c.IsStopped,
                              //CostCenter = cc == null? null : cc.CostCenterName
                          }).ToList();

            grdStores.DataSource = Stores;
            rep_store.DataSource = Stores;

            rep_store.ValueMember = "StoreId";
            rep_store.DisplayMember = "StoreNameAr";

            (grdStores.FocusedView as GridView).FocusedRowHandle = focusedIndex;
        }

        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {

                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.Store).FirstOrDefault();
                if (!p.CanAdd)
                    barBtnNew.Enabled = false;
                if (!p.CanPrint)
                    barBtnPrint.Enabled = false;
            }
        }

        private void barBtnHelp_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "قائمة المخازن");
        }
    }
}