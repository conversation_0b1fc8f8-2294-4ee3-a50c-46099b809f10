﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;
using DevExpress.XtraGrid.Views.Grid;

namespace Pharmacy.Forms
{
    public partial class frm_CloseInventory : DevExpress.XtraEditors.XtraForm
    {
        DateTime today;
        DataTable dtJDetails = new DataTable();
        List<acc> lstAccounts = new List<acc>();
        List<ACC_CostCenter> lstCostCenters = new List<ACC_CostCenter>();

        public frm_CloseInventory()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
        }

        private void frm_SL_PostInvoices_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            today = MyHelper.Get_Server_DateTime();

            BindDataSources();

            Load_Data();
        }

        private void BindDataSources()
        {
            dtJDetails.Columns.Clear();
            dtJDetails.Columns.Add("JDetailId");
            dtJDetails.Columns.Add("JournalId");
            dtJDetails.Columns.Add("AccountId");
            dtJDetails.Columns.Add("Debit", typeof(Decimal));
            dtJDetails.Columns.Add("Credit", typeof(Decimal));
            dtJDetails.Columns.Add("Notes");
            dtJDetails.Columns.Add("Balance");
            dtJDetails.Columns.Add("CostCenter");
            dtJDetails.Columns.Add("CostCenterType");
            dtJDetails.Columns.Add("DueDate", typeof(DateTime));
            dtJDetails.Columns.Add("AcNumber");

            dtJDetails.Columns["Debit"].DefaultValue = 0;
            dtJDetails.Columns["Credit"].DefaultValue = 0;

            grdJDetails.DataSource = dtJDetails;

            #region Get Cost Centers
            lstCostCenters = HelperAcc.GetCostCentersLst(true );
            rep_CostCenter.DataSource = lstCostCenters;
            rep_CostCenter.DisplayMember = "CostCenterName";
            rep_CostCenter.ValueMember = "CostCenterId";
            #endregion

            lstAccounts = HelperAcc.LoadAccountsTree(0, false);
            rep_Account.DataSource = lstAccounts;
            rep_Account.ValueMember = "AccId";
            rep_Account.DisplayMember = "AccName";            
        }

        private void Load_Data()
        {
            ERPDataContext DB = new ERPDataContext();
            var data = from d in DB.IC_Stores
                       join ac in DB.ACC_Accounts on d.CloseInventoryAccount equals ac.AccountId
                       where d.ParentId == null
                       let ItemStoreBalance = (from x in DB.IC_ItemStores
                                               join s in DB.IC_Stores on x.StoreId equals s.StoreId
                                               where x.StoreId == d.StoreId || s.ParentId == d.ParentId
                                               select x).DefaultIfEmpty().Sum(x => x == null ? 0 : x.IsInTrns ? x.PurchasePrice : x.PurchasePrice * -1)
                       let CloseAccBalnce = DB.ACC_JournalDetails.Where(x => x.AccountId == d.CloseInventoryAccount).DefaultIfEmpty().Sum(x => x == null ? 0 : x.Credit - x.Debit)
                       select new
                       {
                           d.StoreId,
                           d.StoreNameAr,
                           d.CloseInventoryAccount,
                           ItemStoreBalance = ItemStoreBalance,
                           CloseAccBalnce = CloseAccBalnce,
                           Difference = ItemStoreBalance - CloseAccBalnce,
                           ac.AcNumber,
                       };

            grid_Stores.DataSource = data;


            #region JournalDetails
            dtInsertDate.EditValue = today;
            dtJDetails.Rows.Clear();

            decimal total_inventory = data.Select(x => x.Difference).Sum();

            if (total_inventory != 0 && Shared.st_Store.InventoryAcc != null && Shared.st_Store.CloseInventoryAcc != null)
            {
                #region Inventory_Journal_Detail
                if (total_inventory > 0)
                {
                    DataRow dr = dtJDetails.NewRow();
                    var inventory_acc = DB.ACC_Accounts.Where(x => x.AccountId == Shared.st_Store.InventoryAcc).FirstOrDefault();
                    dr["AccountId"] = Shared.st_Store.InventoryAcc;
                    dr["Debit"] = total_inventory > 0 ? total_inventory : 0;
                    dr["Credit"] = total_inventory < 0 ? total_inventory * -1 : 0;
                    dr["Notes"] = "";
                    dr["CostCenterType"] = inventory_acc.CostCenter;
                    dr["CostCenter"] = null;
                    dr["AcNumber"] = inventory_acc.AcNumber;
                    dtJDetails.Rows.Add(dr);
                }
                #endregion

                #region CloseInventory_Journal_Detail
                foreach (var d in data)
                {
                    if (d.Difference == 0)
                        continue;

                    DataRow dr = dtJDetails.NewRow();
                    dr["AccountId"] = d.CloseInventoryAccount;
                    dr["Debit"] = d.Difference < 0 ? d.Difference * -1 : 0;
                    dr["Credit"] = d.Difference > 0 ? d.Difference : 0;
                    dr["Notes"] = "";
                    //dr["CostCenterType"] = d.CostCenterId == null ? (bool?)null : false;
                    //dr["CostCenter"] = d.CostCenterId;
                    dr["AcNumber"] = d.AcNumber;
                    dtJDetails.Rows.Add(dr);
                }
                #endregion

                #region Inventory_Journal_Detail
                if (total_inventory < 0)
                {
                    DataRow dr = dtJDetails.NewRow();
                    var inventory_acc = DB.ACC_Accounts.Where(x => x.AccountId == Shared.st_Store.InventoryAcc).FirstOrDefault();
                    dr["AccountId"] = Shared.st_Store.InventoryAcc;
                    dr["Debit"] = total_inventory > 0 ? total_inventory : 0;
                    dr["Credit"] = total_inventory < 0 ? total_inventory * -1 : 0;
                    dr["Notes"] = "";
                    dr["CostCenterType"] = inventory_acc.CostCenter;
                    dr["CostCenter"] = null;
                    dr["AcNumber"] = inventory_acc.AcNumber;
                    dtJDetails.Rows.Add(dr);
                }
                #endregion
            }

            grdJDetails.DataSource = dtJDetails;
            #endregion
        }

        private void barBtn_Save_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            ERPDataContext DB = new ERPDataContext();

            ACC_Journal j = new ACC_Journal();
            j.JCode = HelperAcc.Get_Jornal_Code(); //MyHelper.Get_Server_DateTime());
            j.InsertDate = today;
            j.JNotes = txtJNotes.Text.Trim();
            j.ProcessId = (int)Process.DailyJournal;
            j.SourceId = 0;
            j.InsertUser = Shared.UserId;
            j.IsPosted = !Shared.OfflinePostToGL;
            j.CrncId = 0;
            j.CrncRate = 1;
            j.IsPosted = Shared.st_Store.AutoPostSales;
            j.StoreId = null;

            j.Monthly_Code = HelperAcc.Get_Jornal_Monthly_Code(j.InsertDate, j.ProcessId);

            DB.ACC_Journals.InsertOnSubmit(j);
            DB.SubmitChanges();

            for (int x = 0; x < dtJDetails.Rows.Count; x++)
            {
                if (dtJDetails.Rows[x].RowState == DataRowState.Deleted)
                    continue;

                DAL.ACC_JournalDetail d = new DAL.ACC_JournalDetail();
                d.JournalId = j.JournalId;
                d.AccountId = Convert.ToInt32(dtJDetails.Rows[x]["AccountId"]);
                d.Debit = Convert.ToDecimal(dtJDetails.Rows[x]["Debit"]);
                d.Credit = Convert.ToDecimal(dtJDetails.Rows[x]["Credit"]);
                d.Notes = dtJDetails.Rows[x]["Notes"].ToString();

                if (dtJDetails.Rows[x]["CostCenter"] == null || dtJDetails.Rows[x]["CostCenter"].ToString().Trim() == string.Empty)
                    d.CostCenter = null;
                else
                    d.CostCenter = Convert.ToInt32(dtJDetails.Rows[x]["CostCenter"]);

                if (dtJDetails.Rows[x]["DueDate"] == DBNull.Value || dtJDetails.Rows[x]["DueDate"] == null || dtJDetails.Rows[x]["DueDate"].ToString() == string.Empty)
                    d.DueDate = null;
                else
                    d.DueDate = Convert.ToDateTime(dtJDetails.Rows[x]["DueDate"]).Date;

                d.CrncId = 0;
                d.CrncRate = 1;

                DB.ACC_JournalDetails.InsertOnSubmit(d);
                DB.SubmitChanges();
            }

            XtraMessageBox.Show(
                Shared.IsEnglish == true ? ResAccEn.MsgSave : ResAccAr.MsgSave,//"تم الحفظ بنجاح"
                "", MessageBoxButtons.OK, MessageBoxIcon.Information);

            Load_Data();

        }        

        private bool ValidData()
        {
            //can't post in closed period
            if (ErpHelper.CanSaveInClsedPeriod(dtInsertDate.DateTime.Date,
                Shared.st_Store.ClosePeriodDate, Shared.user.EditInClosedPeriod) == false)
                return false;
            
            GridView view = grdJDetails.FocusedView as GridView;
            view.FocusedRowHandle += 1;

            grdJDetails.RefreshDataSource();
            view.RefreshData();

            if (dtJDetails.Rows.Count < 1)
            {
                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResAccEn.Msg19 : ResAccAr.Msg19,//"يجب تسجيل القيود"
                    "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return false;
            }
            if (!ValidateDebitCredit())
            {
                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResAccEn.Msg20 : ResAccAr.Msg20,//"يجب ان يتساوي الطرف الدائن مع الطرف المدين"
                    "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return false;
            }            
            
            return true;
        }

        private bool ValidateDebitCredit()
        {
            decimal debit = 0;
            decimal credit = 0;

            foreach (DataRow dr in dtJDetails.Rows)
            {
                if (dr.RowState == DataRowState.Deleted)
                    continue;

                debit += Convert.ToDecimal(dr["Debit"]);
                credit += Convert.ToDecimal(dr["Credit"]);
            }
            if (debit == credit)
                return true;
            else
                return false;

        }

        private void barButtonClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Help_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "إعدادات طباعة الباركود");
        }

        
    }
}