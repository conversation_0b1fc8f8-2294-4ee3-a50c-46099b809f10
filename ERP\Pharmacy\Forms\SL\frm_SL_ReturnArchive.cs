﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraPrinting;
using DAL;
using DAL.Res;
using DevExpress.XtraReports.UI;

namespace Pharmacy.Forms
{
    public partial class frm_SL_ReturnArchive : DevExpress.XtraEditors.XtraForm
    {
        System.IO.MemoryStream rep_layout = new System.IO.MemoryStream();

        UserPriv prvlg;
        bool DataModified;
        List<IC_Store> stores_table;
        List<DAL.IC_UOM> uom_list;
        List<SL_Customer_Info> lst_Customers = new List<SL_Customer_Info>();
        List<VendorInfo> lst_Vendors = new List<VendorInfo>();
        List<ItemLkp> lstItems = new List<ItemLkp>();

        DataTable dtSLReturn_Details = new DataTable();
        DataTable dtCompanies = new DataTable();
        DataTable dtUOM = new DataTable();
        DataTable dt_SalesEmps = new DataTable();

        DataTable dtPayAccounts = new DataTable();
        List<IC_Category> lst_Cat = new List<IC_Category>();

        DAL.ERPDataContext DB;

        int invoiceId = 0;
        int userId = 0;
        int customerId = 0;

        int SrcProcessId;
        int SourceId;
        string sourceCode;

        DAL.IC_PriceLevel CustomerPriceLevel = null;
        bool? IsTaxable = null;

        public frm_SL_ReturnArchive()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
        }

        public frm_SL_ReturnArchive(int invoiceId)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            this.invoiceId = invoiceId;
        }

        public frm_SL_ReturnArchive(int invoiceId, int CustomerId)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            this.invoiceId = invoiceId;
            this.customerId = CustomerId;
        }

        public frm_SL_ReturnArchive(int SrcProcessId, int SourceId, string sourceCode)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            cmdProcess.EditValue = this.SrcProcessId = SrcProcessId;
            btnSourceId.EditValue = this.SourceId = SourceId;
            txtSourceCode.Text = this.sourceCode = sourceCode;
        }

        private void frm_SL_Return_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.PageUp)
            {
                btnPrevious.PerformClick();
            }
            if (e.KeyCode == Keys.PageDown)
            {
                btnNext.PerformClick();
            }

            if (e.KeyCode == Keys.Home && e.Modifiers == Keys.Control)
            {
                lkp_Customers.Focus();
            }
            if (e.KeyCode == Keys.Insert)
            {
                txtNotes.Focus();
                FocusItemCode1(true);
            }
            if (e.KeyCode == Keys.End && e.Modifiers == Keys.Control)
            {
                txtDiscountRatio.Focus();
            }
        }

        private void frm_SL_Return_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            if (Shared.ItemMatrixAvailable == false)
                btn_AddMatrixItems.Enabled = false;

            LoadPrivilege();
            lkp_Customers.Properties.View.Columns["CusNameAr"].OptionsFilter.AutoFilterCondition = AutoFilterCondition.Contains;

            //ErpUtils.Allow_Incremental_Search(repItems);
            if (Shared.user.UseContainsToSearchItems)
                repItems.PopupFilterMode = PopupFilterMode.Contains;
            else
                repItems.PopupFilterMode = PopupFilterMode.Default;
            //ErpUtils.Allow_Incremental_Search(repUOM);
            //ErpUtils.Allow_Incremental_Search(lkp_Customers);
            lkp_Drawers.Properties.TextEditStyle = lkp_Drawers2.Properties.TextEditStyle =
                lkpStore.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            repItems.View.Columns["ItemNameAr"].OptionsFilter.AutoFilterCondition = AutoFilterCondition.Contains;
            repItems.View.Columns["ItemNameEn"].OptionsFilter.AutoFilterCondition = AutoFilterCondition.Contains;
            repItems.View.Columns["ItemCode1"].SortIndex = 0;

            DB = new ERPDataContext();

            Reset();

            if (Shared.user.SL_Return_PayMethod == false)
            {
                groupControl1.Visible = false;
                cmbPayMethod.Properties.Items.RemoveAt(2);
                cmbPayMethod.Enabled = true;
                cmbPayMethod.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Default;
                cmbPayMethod.Properties.Buttons[0].Visible = true;
                lbl_Paid.Visible = txt_paid.Visible = lbl_remains.Visible = txt_Remains.Visible = false;
            }

            if (Shared.user.SL_Return_PayMethod == true &&
                Shared.user.UserEditTransactionDate == false)
                dtInvoiceDate.Enabled = false;

            if (Shared.InvoicePostToStore)
                chk_IsInTrns.Visible = false;

            #region Modules Available
            if (Shared.SalesManAvailable == false)
                pnlSalesEmp.Visible = false;
            if (Shared.CurrencyAvailable == false)
                pnlCrncy.Visible = false;
            if (Shared.user.UserChangeCostCenterInInv == false)
                pnlCostCenter.Visible = false;
            if (Shared.ItemsPostingAvailable)
                txtExpenses.Enabled = false;
            #endregion

            BindDataSources();
            LoadInvoice();

            if (this.SrcProcessId > 0 && this.SrcProcessId == (int)Process.Scale && this.SourceId > 0)
            {
                Load_ScaleWeightData(this.SourceId);

                pnlSrcPrc.Visible = true;
                cmdProcess.EditValue = this.SrcProcessId;
                btnSourceId.EditValue = this.SourceId;
                txtSourceCode.Text = this.sourceCode;
            }

            repItems.CloseUp += new DevExpress.XtraEditors.Controls.CloseUpEventHandler(this.repItems_CloseUp);

            ErpUtils.Load_Grid_Layout(grdPrInvoice, this.Name.Replace("frm_", ""));
            ErpUtils.Load_MemoryStream_Layout(rep_layout, this.Name + ".repItems");

            ErpUtils.ColumnChooser(grdPrInvoice);

            #region Hide_Show_Columns
            if (!Shared.st_Store.UseHeightDimension)
                col_Height.OptionsColumn.ShowInCustomizationForm = col_Height.Visible = false;
            if (!Shared.st_Store.UseWidthDimension)
                col_Width.OptionsColumn.ShowInCustomizationForm = col_Width.Visible = false;
            if (!Shared.st_Store.UseLengthDimension)
                col_Length.OptionsColumn.ShowInCustomizationForm = col_Length.Visible = false;
            if (!Shared.st_Store.UseHeightDimension && !Shared.st_Store.UseWidthDimension && !Shared.st_Store.UseLengthDimension)
                col_TotalQty.OptionsColumn.ShowInCustomizationForm = col_TotalQty.Visible = false;
            if (!Shared.st_Store.PiecesCount)
                col_PiecesCount.OptionsColumn.ShowInCustomizationForm = col_PiecesCount.Visible = false;
            if (!Shared.st_Store.ExpireDate)
                col_Expire.OptionsColumn.ShowInCustomizationForm = col_Expire.Visible = false;
            if (!Shared.st_Store.Batch)
                col_Batch.OptionsColumn.ShowInCustomizationForm = col_Batch.Visible = false;
            if (Shared.user.HidePurchasePrice)
                colPurchasePrice.OptionsColumn.ShowInCustomizationForm = colPurchasePrice.Visible = false;
            if (Shared.user.HideItemDiscount)
            {
                gridView2.Columns["DiscountRatio"].OptionsColumn.ShowInCustomizationForm = gridView2.Columns["DiscountRatio"].Visible = false;
                gridView2.Columns["DiscountValue"].OptionsColumn.ShowInCustomizationForm = gridView2.Columns["DiscountValue"].Visible = false;
            }
            if (!Shared.TaxAvailable)
            {
                col_SalesTax.OptionsColumn.ShowInCustomizationForm = col_SalesTax.Visible = false;
            }

            if (!Shared.st_Store.Serial)
                col_Serial.OptionsColumn.ShowInCustomizationForm = col_Serial.Visible = false;

            col_Serial.Caption = Shared.IsEnglish ? Shared.st_Store.SerialNameEn : Shared.st_Store.SerialNameAr;
            col_Batch.Caption = Shared.IsEnglish ? Shared.st_Store.BatchNameEn : Shared.st_Store.BatchNameAr;
            #endregion

            if (Shared.st_Store.SalesDeductTaxAccount == null || Shared.TaxAvailable == false)
                txt_DeductTaxR.Enabled = txt_DeductTaxV.Enabled = false;

            if (Shared.st_Store.SalesAddTaxAccount == null || Shared.TaxAvailable == false)
                txt_AddTaxR.Enabled = txt_AddTaxV.Enabled = false;

            txt_TaxValue.BackColor = Color.White;
            txt_TaxValue.ForeColor = Color.DimGray;

            txtInvoiceCode.Leave += new EventHandler(txtInvoiceCode_Leave);
            col_PiecesCount.Caption = Shared.IsEnglish ? Shared.st_Store.PiecesCountNameEn : Shared.st_Store.PiecesCountNameAr;
        }

        private void frm_SL_Return_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                e.Cancel = true;
            else
                e.Cancel = false;

            ErpUtils.save_Grid_Layout(grdPrInvoice, this.Name.Replace("frm_", ""), true);
            ErpUtils.save_MemoryStream_Layout(rep_layout, this.Name + ".repItems");
        }

        private void gridView1_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            if (e.Column.FieldName == "CompanyNameAr" || e.Column.FieldName == "LargeUOMFactor"
                || e.Column.FieldName == "MediumUOMFactor" || e.Column.FieldName == "TotalSellPrice" /*|| e.Column.FieldName == "CurrentQty"*/
                || e.Column.FieldName == "IsAssembly" || e.Column.FieldName == "UomIndex"
                || e.Column.FieldName == "VendorId"
                || e.Column.FieldName == "Expire" || e.Column.FieldName == "Batch")
                return;

            DB = new DAL.ERPDataContext();
            DAL.IC_Item item = null;

            GridView view = grdPrInvoice.FocusedView as GridView;
            DataRow row = view.GetFocusedDataRow();

            #region Barcode_Init_Detail_PR

            Detail_PR detail_PR = new Detail_PR
            {
                ItemId = (int)0,
                Batch = "",
                Expire = (DateTime?)DateTime.Now,
                Length = (decimal?)1,
                Width = (decimal?)1,
                Height = (decimal?)1,
                PiecesCount = (decimal)0,
                PurchasePrice = (decimal)0,
                TotalPurchasePrice = (decimal)0,
                SellPrice = (decimal)0,
                Qty = (decimal)0,
                UOMId = (int)0,
                UOMIndex = (byte)0,
                VendorId = (int?)0
            };
            detail_PR = null;
            #endregion

            int barcodeTemplateCode = 0;
            decimal barcodeTemplateQty = 0;
            string barcodeBatch = string.Empty;
            string code1 = string.Empty;


            #region GetItem
            if (e.Column.FieldName == "ItemCode1")
            {
                if (view.GetFocusedRowCellValue("ItemCode1") != null && view.GetFocusedRowCellValue("ItemCode1").ToString() != string.Empty)
                {
                    code1 = view.GetFocusedRowCellValue("ItemCode1").ToString();

                    item = MyHelper.SearchItem(DB, code1, detail_PR, ref barcodeTemplateCode, ref barcodeTemplateQty, ref barcodeBatch, invoiceId,
                        Shared.st_Store);
                }
                else
                {
                    view.DeleteRow(e.RowHandle);
                }
            }

            if (e.Column.FieldName == "ItemCode2")
            {
                if (view.GetFocusedRowCellValue("ItemCode2").ToString() != string.Empty)
                {
                    item = (from i in DB.IC_Items
                            where i.ItemType != (int)DAL.ItemType.MatrixParent
                            where i.ItemCode2.Contains(view.GetFocusedRowCellValue("ItemCode2").ToString())
                            where invoiceId == 0 ? i.IsDeleted == false : true
                            select i).FirstOrDefault();
                }
                else
                {
                    view.DeleteRow(e.RowHandle);
                }
            }

            if (e.Column.FieldName == "ItemId")
            {
                if (view.GetFocusedRowCellValue("ItemId").ToString() != string.Empty)
                {
                    item = (from i in DB.IC_Items
                            where i.ItemType != (int)DAL.ItemType.MatrixParent
                            where i.ItemId == Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"))
                            where invoiceId == 0 ? i.IsDeleted == false : true
                            select i).SingleOrDefault();
                }
                else
                {
                    view.DeleteRow(e.RowHandle);
                }
            }

            if (e.Column.FieldName == "ItemCode1" || e.Column.FieldName == "ItemCode2"
                || e.Column.FieldName == "ItemId")
            {
                if (item != null && item.ItemId > 0)
                {
                    if (item.ItemType == (int)ItemType.Subtotal)
                    {
                        row["ItemId"] = item.ItemId;
                        row["ItemCode1"] = item.ItemCode1;
                        row["ItemCode2"] = item.ItemCode2;
                        row["ItemType"] = item.ItemType;
                        row["IsExpire"] = item.IsExpire;
                        row["ItemDescription"] = item.Description;
                        row["ItemDescriptionEn"] = item.DescriptionEn;
                        row["CategoryId"] = item.Category;
                        Get_SubTotal_RowData(row, view, e.RowHandle);
                    }
                    else
                    {
                        LoadItemRow(item, row);
                        if (Shared.st_Store.PrintBarcodePerInventory && detail_PR != null)
                        {
                            row["PurchasePrice"] = Decimal.ToDouble(detail_PR.TotalPurchasePrice / (detail_PR.Qty));
                            row["VendorId"] = detail_PR.VendorId;
                            row["PiecesCount"] = detail_PR.PiecesCount;
                            row["Length"] = detail_PR.Length.HasValue ? decimal.ToDouble(detail_PR.Length.Value) : 1;
                            row["Width"] = detail_PR.Width.HasValue ? decimal.ToDouble(detail_PR.Width.Value) : 1;
                            row["Height"] = detail_PR.Height.HasValue ? decimal.ToDouble(detail_PR.Height.Value) : 1;
                            if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish)
                                row["Qty"] = decimal.ToDouble(detail_PR.Qty);
                            row["Batch"] = detail_PR.Batch;

                            if (detail_PR.Expire.HasValue)
                            {
                                row["Expire"] = detail_PR.Expire.Value;
                            }
                            else
                            {
                                row["Expire"] = DBNull.Value;
                            }
                        }

                        if (Shared.st_Store.PrintBarcodePerInventory == false && barcodeTemplateCode > 0 && barcodeTemplateQty > 0)
                        {
                            row["Qty"] = decimal.ToDouble(barcodeTemplateQty);
                            row["Batch"] = barcodeBatch;
                            row["PiecesCount"] = 1;
                        }
                    }
                }
                else
                {
                    view.DeleteRow(e.RowHandle);
                    return;
                }
            }
            #endregion

            if (view.GetRowCellValue(e.RowHandle, "ItemType") != null && Convert.ToInt32(view.GetRowCellValue(e.RowHandle, "ItemType")) == (int)ItemType.Subtotal)
            {
                Update_First_SubTotal(view, e.RowHandle);
                return;
            }

            #region GetUomPrice
            if (e.Column.FieldName == "UOM")
            {
                if (view.GetFocusedRowCellValue("UOM").ToString() != string.Empty &&
                    view.GetFocusedRowCellValue("ItemId").ToString() != string.Empty)
                {
                    int itmId = Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"));
                    int uomIndex = Convert.ToInt32(ErpUtils.GetGridLookUpValue(repUOM, view.GetRowCellValue(e.RowHandle, "UOM"), "Index"));

                    //get UOM and Factor, multiple by purchase and sell prices
                    item = (from i in DB.IC_Items
                            where i.ItemId == itmId
                            select i).SingleOrDefault();

                    decimal uom_price = MyHelper.GetPriceLevelSellPrice(CustomerPriceLevel, item, uomIndex);
                    view.GetDataRow(e.RowHandle)["SellPrice"] = decimal.ToDouble(uom_price);

                    if (uomIndex == 0)//small                    
                        view.GetDataRow(e.RowHandle)["PurchasePrice"] = decimal.ToDouble(item.PurchasePrice);
                    if (uomIndex == 1)//medium                                           
                        view.GetDataRow(e.RowHandle)["PurchasePrice"] = decimal.ToDouble(item.PurchasePrice * MyHelper.FractionToDouble(item.MediumUOMFactor));
                    if (uomIndex == 2)//large
                        view.GetDataRow(e.RowHandle)["PurchasePrice"] = decimal.ToDouble(item.PurchasePrice * MyHelper.FractionToDouble(item.LargeUOMFactor));

                    view.GetDataRow(e.RowHandle)["UomIndex"] = uomIndex;
                }
            }
            #endregion

            #region Calculate Prices
            if (e.Column.FieldName == "DiscountValue" || e.Column.FieldName == "SellPrice"
                || e.Column.FieldName == "Qty" || e.Column.FieldName == "ItemId"
                || e.Column.FieldName == "ItemCode1" || e.Column.FieldName == "ItemCode2"
                || e.Column.FieldName == "UOM"
                || e.Column == col_Height || e.Column == col_Width || e.Column == col_Length)
            {
                try
                {
                    if (!string.IsNullOrEmpty(view.GetFocusedRowCellValue("SellPrice").ToString()) &&
                        !string.IsNullOrEmpty(view.GetFocusedRowCellValue("DiscountValue").ToString()) &&
                        !string.IsNullOrEmpty(view.GetFocusedRowCellValue("Qty").ToString()) &&
                        !string.IsNullOrEmpty(view.GetFocusedRowCellValue("ItemId").ToString()))
                    {
                        decimal Height = Convert.ToDecimal(view.GetFocusedRowCellValue("Height"));
                        decimal Length = Convert.ToDecimal(view.GetFocusedRowCellValue("Length"));
                        decimal Width = Convert.ToDecimal(view.GetFocusedRowCellValue("Width"));
                        if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish)
                            Height = Length = Width = 1;

                        decimal Qty = Convert.ToDecimal(view.GetFocusedRowCellValue("Qty"));
                        decimal TotalQty = Qty * Height * Width * Length;
                        decimal SellPrice = Convert.ToDecimal(view.GetFocusedRowCellValue("SellPrice"));
                        decimal PurchasePrice = Convert.ToDecimal(view.GetFocusedRowCellValue("PurchasePrice"));
                        int itmId = Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"));
                        byte uomIndex = Convert.ToByte(view.GetFocusedRowCellValue("UomIndex"));
                        decimal SalesTaxRatio = Convert.ToDecimal(view.GetFocusedDataRow()["SalesTaxRatio"]);
                        bool calcTaxBeforeDisc = Convert.ToBoolean(view.GetDataRow(e.RowHandle)["calcTaxBeforeDisc"]);

                        decimal DiscV = Convert.ToDecimal(view.GetFocusedDataRow()["DiscountValue"]);
                        decimal TotalSellPrice = (TotalQty * SellPrice) - DiscV;

                        if (Shared.st_Store.PriceIncludeSalesTax)/*السعر شامل الضريبة*/
                        {
                            decimal temp = calcTaxBeforeDisc ? (SalesTaxRatio * TotalQty * SellPrice) / (1 + SalesTaxRatio) :
                                (SalesTaxRatio * TotalSellPrice) / (1 + SalesTaxRatio);
                            view.SetFocusedRowCellValue("SalesTax", decimal.ToDouble(temp));
                            view.GetDataRow(e.RowHandle)["TotalSellPrice"] = decimal.ToDouble(TotalSellPrice - temp);// السعر الاجمالي شامل الضريبة                            
                        }
                        else                                            /*السعر غير شامل الضريبة*/
                        {
                            decimal temp = calcTaxBeforeDisc ? (SalesTaxRatio * TotalQty * SellPrice) : (SalesTaxRatio * TotalSellPrice);
                            view.SetFocusedRowCellValue("SalesTax", decimal.ToDouble(temp));
                            view.GetDataRow(e.RowHandle)["TotalSellPrice"] = decimal.ToDouble(TotalSellPrice);//ضيف الضريبة على السعر الاجمالي                            
                        }

                        if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.DistinguishAndMultiply)
                            view.GetDataRow(e.RowHandle)["PiecesCount"] = Qty;

                        //get store qty
                        decimal medium = 1;
                        decimal large = 1;
                        medium = MyHelper.FractionToDouble(view.GetFocusedRowCellValue("MediumUOMFactor").ToString());
                        large = MyHelper.FractionToDouble(view.GetFocusedRowCellValue("LargeUOMFactor").ToString());

                        //decimal currentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, itmId, Convert.ToInt32(lkpStore.EditValue));
                        //currentQty = MyHelper.getCalculatedUomQty(currentQty, uomIndex, medium, large);
                        //view.SetFocusedRowCellValue("CurrentQty", decimal.ToDouble(currentQty));

                        Get_TotalAccount();
                    }
                }
                catch { }
            }
            #endregion

            #region DiscountRation_changed_by_User
            if (e.Column.FieldName == "DiscountRatio" || e.Column.FieldName == "DiscountRatio2" || e.Column.FieldName == "DiscountRatio3"
                || e.Column.FieldName == "Qty" || e.Column.FieldName == "SellPrice" || e.Column.FieldName == "UOM")
            {
                try
                {
                    if (!string.IsNullOrEmpty(view.GetFocusedRowCellValue(e.Column.FieldName).ToString()))
                    {
                        decimal Height = Convert.ToDecimal(view.GetFocusedRowCellValue("Height"));
                        decimal Length = Convert.ToDecimal(view.GetFocusedRowCellValue("Length"));
                        decimal Width = Convert.ToDecimal(view.GetFocusedRowCellValue("Width"));
                        if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish)
                            Height = Length = Width = 1;

                        decimal Qty = Convert.ToDecimal(view.GetFocusedRowCellValue("Qty"));
                        decimal TotalQty = Qty * Height * Width * Length;

                        decimal SellPrice = Convert.ToDecimal(view.GetFocusedRowCellValue("SellPrice"));
                        decimal DiscR1 = Convert.ToDecimal(view.GetFocusedDataRow()["DiscountRatio"]);
                        decimal DiscR2 = Convert.ToDecimal(view.GetFocusedDataRow()["DiscountRatio2"]);
                        decimal DiscR3 = Convert.ToDecimal(view.GetFocusedDataRow()["DiscountRatio3"]);

                        bool calcTaxBeforeDisc = Convert.ToBoolean(view.GetDataRow(e.RowHandle)["calcTaxBeforeDisc"]);
                        decimal salesTax = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["SalesTax"]);
                        decimal totalSellP = calcTaxBeforeDisc ? ((SellPrice * TotalQty) - salesTax) : SellPrice * TotalQty;

                        decimal DiscountValue = Utilities.Calc_DiscountValue(DiscR1, DiscR2, DiscR3, totalSellP);
                        view.SetFocusedRowCellValue("DiscountValue", decimal.ToDouble(DiscountValue));
                    }
                }
                catch { }
            }
            #endregion

            Update_First_SubTotal(view, e.RowHandle);
        }

        private void gridView1_FocusedColumnChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedColumnChangedEventArgs e)
        {
            if (e.FocusedColumn != null && e.FocusedColumn.FieldName == "UOM")
            {
                GridView view = grdPrInvoice.FocusedView as GridView;
                DataRow row = view.GetFocusedDataRow();

                DB = new DAL.ERPDataContext();
                DAL.IC_Item item = new DAL.IC_Item();

                if (row != null && row["ItemId"].ToString() != string.Empty)
                {
                    item = (from i in DB.IC_Items
                            where i.ItemId == Convert.ToInt32(row["ItemId"])
                            where invoiceId == 0 ? i.IsDeleted == false : true
                            select i).SingleOrDefault();

                    MyHelper.GetUOMs(item, dtUOM, uom_list);

                    if (string.IsNullOrEmpty(row["UOM"].ToString()))
                        view.SetFocusedRowCellValue("UOM", dtUOM.Rows[0]["UomId"]);
                }
            }

        }

        private void gridView1_KeyDown(object sender, KeyEventArgs e)
        {
            GridView view = sender as GridView;
            if (e.KeyCode == Keys.Delete && e.Modifiers == Keys.Control)
            {
                //if (MessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgDelRow : ResSLAr.MsgDelRow,
                //    Shared.IsEnglish ? ResSLEn.MsgTQues : ResSLAr.MsgTQues, MessageBoxButtons.YesNo, MessageBoxIcon.Question) !=
                //  DialogResult.Yes)
                //    return;

                view.DeleteRow(view.FocusedRowHandle);

                Update_First_SubTotal(view, view.FocusedRowHandle);
                Get_TotalAccount();
            }
            if (e.KeyCode == Keys.Up && e.Control && e.Shift)
            {
                ErpUtils.Move_Row_Up(view);
                Update_First_SubTotal(view, view.FocusedRowHandle - 1);
                Update_First_SubTotal(view, view.FocusedRowHandle + 1);
            }
            if (e.KeyCode == Keys.Down && e.Control && e.Shift)
            {
                ErpUtils.Move_Row_Down(view);
                Update_First_SubTotal(view, view.FocusedRowHandle - 1);
                Update_First_SubTotal(view, view.FocusedRowHandle + 2);
            }
        }

        private void gridView1_ValidateRow(object sender, DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs e)
        {
            try
            {
                ColumnView view = sender as ColumnView;
                if (Convert.ToInt32(view.GetDataRow(e.RowHandle)["ItemType"]) == (int)ItemType.Subtotal)
                    return;

                if (view.GetRowCellValue(e.RowHandle, view.Columns["ItemId"]).ToString() == string.Empty)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["ItemId"], Shared.IsEnglish ? ResSLEn.txtValidateItem : ResSLAr.txtValidateItem);//"يجب اختيار الصنف";
                }
                if (view.GetRowCellValue(e.RowHandle, view.Columns["UOM"]).ToString() == string.Empty)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["UOM"], Shared.IsEnglish ? ResSLEn.txtValidateUom : ResSLAr.txtValidateUom);//"يجب اختيار وحدة القياس");
                }
                if ((view.GetRowCellValue(e.RowHandle, view.Columns["Qty"])).ToString() == string.Empty
                    || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Qty"])) <= 0)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["Qty"], Shared.IsEnglish ? ResSLEn.txtValidateQty : ResSLAr.txtValidateQty);//"الكمية يجب ان تكون اكبر من الصفر");
                }

                #region Validate Height Length and Width
                if ((view.GetRowCellValue(e.RowHandle, view.Columns["Height"])).ToString() == string.Empty
                    || (Shared.st_Store.MultiplyDimensions != (byte)Dimensions.Distinguish && Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Height"])) <= 0)
                    || (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish && Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Height"])) < 0))
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["Height"], Shared.IsEnglish ? ResSLEn.txtValidateQty : ResSLAr.txtValidateQty);//"الكمية يجب ان تكون اكبر من الصفر");
                }
                if ((view.GetRowCellValue(e.RowHandle, view.Columns["Length"])).ToString() == string.Empty
                    || (Shared.st_Store.MultiplyDimensions != (byte)Dimensions.Distinguish && Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Length"])) <= 0)
                    || (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish && Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Length"])) < 0))
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["Length"], Shared.IsEnglish ? ResSLEn.txtValidateQty : ResSLAr.txtValidateQty);//"الكمية يجب ان تكون اكبر من الصفر");
                }
                if ((view.GetRowCellValue(e.RowHandle, view.Columns["Width"])).ToString() == string.Empty
                    || (Shared.st_Store.MultiplyDimensions != (byte)Dimensions.Distinguish && Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Width"])) <= 0)
                    || (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish && Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Width"])) < 0))
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["Width"], Shared.IsEnglish ? ResSLEn.txtValidateQty : ResSLAr.txtValidateQty);//"الكمية يجب ان تكون اكبر من الصفر");
                }
                if ((view.GetRowCellValue(e.RowHandle, view.Columns["PiecesCount"])).ToString() == string.Empty
                    || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["PiecesCount"])) < 0)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["PiecesCount"], Shared.IsEnglish ? ResSLEn.txtValidateQty : ResSLAr.txtValidateQty);//"الكمية يجب ان تكون اكبر من الصفر");
                }

                #endregion

                if (view.GetRowCellValue(e.RowHandle, view.Columns["SellPrice"]).ToString() == string.Empty
                    || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["SellPrice"])) < 0)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["SellPrice"], Shared.IsEnglish ? ResSLEn.txtValidateSPrice : ResSLAr.txtValidateSPrice);//"سعر البيع يجب أن يكون أكبر من الصفر");
                    return;
                }

                if (view.GetRowCellValue(e.RowHandle, view.Columns["DiscountValue"]).ToString() == string.Empty)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["DiscountValue"], Shared.IsEnglish ? ResSLEn.txtValidateDiscount : ResSLAr.txtValidateDiscount);//"يجب تحديد الخصم");
                }
                if (view.GetRowCellValue(e.RowHandle, view.Columns["DiscountRatio"]).ToString() == string.Empty
                    || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["DiscountRatio"])) > 1)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["DiscountRatio"], Shared.IsEnglish ? ResSLEn.txtValidateMaxDiscount : ResSLAr.txtValidateMaxDiscount);// "نسبة الخصم لايمكن ان تتجاوز المائة";
                }
                if (view.GetRowCellValue(e.RowHandle, view.Columns["DiscountRatio2"]).ToString() == string.Empty
                    || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["DiscountRatio2"])) > 1)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["DiscountRatio2"], Shared.IsEnglish ? ResSLEn.txtValidateMaxDiscount : ResSLAr.txtValidateMaxDiscount);// "نسبة الخصم لايمكن ان تتجاوز المائة";
                }
                if (view.GetRowCellValue(e.RowHandle, view.Columns["DiscountRatio3"]).ToString() == string.Empty
                    || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["DiscountRatio3"])) > 1)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["DiscountRatio3"], Shared.IsEnglish ? ResSLEn.txtValidateMaxDiscount : ResSLAr.txtValidateMaxDiscount);// "نسبة الخصم لايمكن ان تتجاوز المائة";
                }
            }
            catch
            {
                e.Valid = false;
            }
        }

        private void gridView1_InvalidRowException(object sender, DevExpress.XtraGrid.Views.Base.InvalidRowExceptionEventArgs e)
        {
            if ((e.Row as DataRowView).Row["ItemId"] == DBNull.Value)
                e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.Ignore;
            else
                e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.NoAction;
        }

        private void gridView1_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            Get_TotalAccount();
            grd_FocusOnItemId(Shared.user.InvoicesUseSearchItems ? "ItemId" : "ItemCode1");//mahmoud
        }


        private void btnNext_Click(object sender, EventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            DB = new DAL.ERPDataContext();
            int lastInvId = (from inv in DB.SL_ReturnArchives
                             where inv.SL_ReturnId > invoiceId
                             where Shared.user.UserChangeStore ? true : inv.StoreId == Shared.user.DefaultStore
                             where Shared.user.AccessOtherUserTrns ? true : inv.UserId == Shared.UserId

                             orderby inv.SL_ReturnId ascending
                             select inv.SL_ReturnId).FirstOrDefault();

            if (lastInvId != 0)
            {
                invoiceId = lastInvId;
                LoadInvoice();
            }
            else
            {
                lastInvId = (from inv in DB.SL_ReturnArchives
                             where Shared.user.UserChangeStore ? true : inv.StoreId == Shared.user.DefaultStore
                             where Shared.user.AccessOtherUserTrns ? true : inv.UserId == Shared.UserId

                             orderby inv.SL_ReturnId ascending
                             select inv.SL_ReturnId).FirstOrDefault();

                if (lastInvId != 0)
                {
                    invoiceId = lastInvId;
                    LoadInvoice();
                }
            }
        }

        private void btnPrevious_Click(object sender, EventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;
            DB = new DAL.ERPDataContext();
            int lastInvId = (from inv in DB.SL_ReturnArchives
                             where inv.SL_ReturnId < invoiceId
                             where Shared.user.UserChangeStore ? true : inv.StoreId == Shared.user.DefaultStore
                             where Shared.user.AccessOtherUserTrns ? true : inv.UserId == Shared.UserId

                             orderby inv.SL_ReturnId descending
                             select inv.SL_ReturnId).FirstOrDefault();

            if (lastInvId != 0)
            {
                invoiceId = lastInvId;
                LoadInvoice();
            }
            else
            {
                lastInvId = (from inv in DB.SL_ReturnArchives
                             where Shared.user.UserChangeStore ? true : inv.StoreId == Shared.user.DefaultStore
                             where Shared.user.AccessOtherUserTrns ? true : inv.UserId == Shared.UserId

                             orderby inv.SL_ReturnId descending
                             select inv.SL_ReturnId).FirstOrDefault();

                if (lastInvId != 0)
                {
                    invoiceId = lastInvId;
                    LoadInvoice();
                }
            }
        }


        private void grid_ProcessGridKey(object sender, KeyEventArgs e)
        {
            try
            {
                DevExpress.XtraGrid.GridControl grid = sender as DevExpress.XtraGrid.GridControl;
                var view = (grid.FocusedView as DevExpress.XtraGrid.Views.Grid.GridView);

                if (e.KeyCode == Keys.Enter)
                {
                    var focused_column = (grid.FocusedView as DevExpress.XtraGrid.Views.Base.ColumnView).FocusedColumn;
                    int focused_row_handle = (grid.FocusedView as DevExpress.XtraGrid.Views.Base.ColumnView).FocusedRowHandle;

                    /*if (view.FocusedColumn == view.Columns["ItemId"]
                        || view.FocusedColumn == view.Columns["ItemCode1"]
                        || view.FocusedColumn == view.Columns["ItemCode2"])
                    {
                        if (Shared.user.UseBarcodeScanner)
                        {
                            grid_ProcessGridKey(sender, new KeyEventArgs(Keys.Tab));
                        }
                        else
                        {
                            string temp = view.FocusedColumn.FieldName;
                            view.FocusedColumn = view.VisibleColumns[view.Columns["ItemId"].VisibleIndex - 1];
                            if (view.GetFocusedRowCellValue(temp) == null || string.IsNullOrEmpty(view.GetFocusedRowCellValue(temp).ToString()))
                                view.FocusedColumn = view.Columns[temp];
                            return;
                        }
                    }*/

                    if (view.FocusedColumn == view.Columns["ItemId"]
                        || view.FocusedColumn == view.Columns["ItemCode1"]
                        || view.FocusedColumn == view.Columns["ItemCode2"]
                        || view.FocusedColumn == view.Columns["UOM"]
                        || view.FocusedColumn == view.Columns["Qty"]
                        || view.FocusedColumn == view.Columns["Length"]
                        || view.FocusedColumn == view.Columns["Width"]
                        || view.FocusedColumn == view.Columns["Height"]
                        || view.FocusedColumn == view.Columns["Expire"]
                        || view.FocusedColumn == view.Columns["Batch"]
                        || view.FocusedColumn == view.Columns["ItemDescription"]
                        || view.FocusedColumn == view.Columns["ItemDescriptionEn"]
                        || view.FocusedColumn == view.Columns["PiecesCount"]
                        || view.FocusedColumn == view.Columns["SellPrice"]
                        || view.FocusedColumn == view.Columns["PurchasePrice"]
                        || view.FocusedColumn == view.Columns["DiscountRatio"]
                        || view.FocusedColumn == view.Columns["DiscountRatio2"]
                        || view.FocusedColumn == view.Columns["DiscountRatio3"]
                        || view.FocusedColumn == view.Columns["DiscountValue"]
                        || view.FocusedColumn == view.Columns["Batch"]
                        || view.FocusedColumn == view.Columns["Serial"])
                    {
                        if (view.FocusedColumn.VisibleIndex - 1 >= 0 && view.VisibleColumns[view.FocusedColumn.VisibleIndex - 1].OptionsColumn.AllowFocus)
                        {
                            view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex - 1];
                            return;
                        }
                        else if (view.FocusedColumn.VisibleIndex - 2 >= 0 && view.VisibleColumns[view.FocusedColumn.VisibleIndex - 2].OptionsColumn.AllowFocus)
                        {
                            view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex - 2];
                            return;
                        }
                        else
                            grid_ProcessGridKey(sender, new KeyEventArgs(Keys.Tab));
                    }


                    if (view.FocusedRowHandle < 0)//|| view.FocusedRowHandle == view.RowCount)
                    {
                        view.AddNewRow();
                        view.FocusedColumn = view.Columns[Shared.user.InvoicesUseSearchItems ? "ItemId" : "ItemCode1"];//mahmoud                        
                    }
                    else
                    {
                        view.FocusedRowHandle = focused_row_handle + 1;
                        view.FocusedColumn = view.Columns[Shared.user.InvoicesUseSearchItems ? "ItemId" : "ItemCode1"];
                    }

                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == Keys.Tab && e.Modifiers != Keys.Shift)
                {
                    if (view.FocusedColumn.VisibleIndex == 0)
                        view.FocusedColumn = view.VisibleColumns[view.VisibleColumns.Count - 1];
                    else
                        view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex - 1];
                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == Keys.Tab && e.Modifiers == Keys.Shift)
                {
                    if (view.FocusedColumn.VisibleIndex == view.VisibleColumns.Count)
                        view.FocusedColumn = view.VisibleColumns[0];
                    else
                        view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex + 1];
                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == Keys.Up
                   && view.GetFocusedRow() != null
                   && (view.GetFocusedRow() as DataRowView).IsNew == true
                   && (view.GetFocusedRowCellValue("ItemId") == null || view.GetFocusedRowCellValue("ItemId").ToString() == string.Empty))
                {
                    view.DeleteRow(view.FocusedRowHandle);
                }

            }
            catch
            { }
        }

        private void gridView1_CustomColumnDisplayText(object sender, CustomColumnDisplayTextEventArgs e)
        {
            if (e.Column.FieldName == "DiscountValue"
                || e.Column.FieldName == "TotalSellPrice" || e.Column.FieldName == "SellPrice"
                || e.Column.FieldName == "PurchasePrice" /*|| e.Column.FieldName == "CurrentQty"*/
                || e.Column.FieldName == "SalesTax")
            {
                if (e.Value != DBNull.Value && e.Value != null)
                    e.DisplayText = decimal.Round(Convert.ToDecimal(e.Value), 4).ToString();
            }
            else if (e.Column.FieldName == "ManufactureDate")
            {
                if (e.Value != null && e.Value != DBNull.Value)
                    e.DisplayText = Convert.ToDateTime(e.Value).ToShortDateString();
            }
        }


        private void txtDiscountRatio_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (!char.IsNumber(e.KeyChar) && e.KeyChar != '.')
                e.Handled = e.KeyChar != (char)Keys.Back;
        }

        private void repUOM_CustomDisplayText(object sender, DevExpress.XtraEditors.Controls.CustomDisplayTextEventArgs e)
        {
            if (e.Value == null || e.Value == DBNull.Value)
                return;
            try
            {
                e.DisplayText = uom_list.Where(x => x.UOMId == Convert.ToInt32(e.Value)).FirstOrDefault().UOM;
            }
            catch
            {
            }
        }


        private void barBtnNew_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            invoiceId = userId = 0;
            invoice_remains = 0;
            LoadInvoice();
            lkp_Customers_EditValueChanged(null, EventArgs.Empty);
            txtNotes.Focus();
            FocusItemCode1(Shared.user.FocusGridInInvoices);
        }

        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void batBtnList_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_SL_ReturnList)))
            {
                frm_SL_ReturnArchiveList frm = new frm_SL_ReturnArchiveList();
                frm.BringToFront();
                frm.Show();
            }
            else
                Application.OpenForms["frm_SL_ReturnArchiveList"].BringToFront();
        }

        private void barBtnDelete_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            /*
             *Delete all items added to store using this invoice 
             *Delete all invoice details 
             *Delete Jornal_Details 
             *Delete Jornal 
             *Delete Invoice
             */
            if (invoiceId == 0)
                return;

            //can't delete in closed period
            if (ErpHelper.CanSaveInClsedPeriod(dtInvoiceDate.DateTime.Date,
                Shared.st_Store.ClosePeriodDate, Shared.user.EditInClosedPeriod) == false)
                return;

            if (XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgDeleteInv : ResSLAr.MsgDeleteInv,
                Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) == DialogResult.No)
                return;

            if (ErpHelper.CanEditPostedBill(invoiceId, (int)Process.SellReturn,
                Shared.OfflinePostToGL, Shared.user.UserEditPostedBills) == false)
                return;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            //var invoice_details_ids = DB.SL_ReturnDetails.Where(s => s.SL_ReturnId == invoiceId).Select(s => s.SL_ReturnDetailId).ToList();
            //var invoice_itemstores = DB.IC_ItemStores.Where(s => s.ProcessId == (int)Process.SellReturn
            //    && invoice_details_ids.Contains(s.SourceId));
            var invoice_itemstores = (from i in DB.IC_ItemStores
                                      join s in DB.SL_ReturnDetails
                                      on i.SourceId equals s.SL_ReturnDetailId
                                      where i.ProcessId == (int)Process.SellReturn &&
                                      s.SL_ReturnId == invoiceId
                                      select i).ToList();

            DB.IC_ItemStores.DeleteAllOnSubmit(invoice_itemstores);

            DB.SL_ReturnDetails.DeleteAllOnSubmit(DB.SL_ReturnDetails.Where(s => s.SL_ReturnId == invoiceId));

            int jornal_id = DB.ACC_Journals.Where(s => s.ProcessId == (int)Process.SellReturn && s.SourceId == invoiceId).Select(s => s.JournalId).FirstOrDefault();
            DB.ACC_JournalDetails.DeleteAllOnSubmit(DB.ACC_JournalDetails.Where(s => s.JournalId == jornal_id));
            DB.ACC_Journals.DeleteAllOnSubmit(DB.ACC_Journals.Where(s => s.JournalId == jornal_id));

            DB.SL_Returns.DeleteAllOnSubmit(DB.SL_Returns.Where(s => s.SL_ReturnId == invoiceId));

            MyHelper.UpdateST_UserLog(DB, txtInvoiceCode.Text, lkp_Customers.Text,
                (int)FormAction.Delete, (int)FormsNames.SL_Return);

            DB.SubmitChanges();

            invoiceId = userId = 0;
            LoadInvoice();
            txtNotes.Focus();
            FocusItemCode1(Shared.user.FocusGridInInvoices);

            XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgDel : ResSLAr.MsgDel, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void barBtn_Save_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (!ValidData())
                return;
            if (invoiceId == 0)
            {
                Save_Invoice();
                XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResPrEn.MsgSave : ResPrAr.MsgSave, "", MessageBoxButtons.OK, MessageBoxIcon.Information);

                #region Ask_To_Create_InTrnsBill
                //if (Shared.InvoicePostToStore == false)
                //{
                //    if (XtraMessageBox.Show(Shared.IsEnglish == true ? ResSLEn.MsgAskCreateInTrns : ResSLAr.MsgAskCreateInTrns,
                //    Shared.IsEnglish == true ? ResSLEn.MsgTQues : ResSLAr.MsgTQues,
                //    MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) == DialogResult.Yes)
                //    {
                //        if (ErpUtils.IsFormOpen(typeof(frm_IC_InTrns)))
                //            Application.OpenForms["frm_IC_InTrns"].Close();

                //        if (ErpUtils.IsFormOpen(typeof(frm_IC_OutTrns)))
                //            Application.OpenForms["frm_IC_InTrns"].BringToFront();
                //        else
                //        {
                //            new frm_IC_InTrns((int)Process.SellReturn, invoiceId, txtInvoiceCode.Text).Show();
                //        }
                //    }
                //}
                #endregion
            }
            else
            {
                Save_Invoice();
                XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResPrEn.MsgSave : ResPrAr.MsgSave, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            invoice_remains = Convert.ToDouble(txt_Remains.EditValue);

            lkp_Customers_EditValueChanged(null, EventArgs.Empty);
        }


        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (ErpUtils.CheckDocumentSaved(invoiceId, DataModified, dtSLReturn_Details) == false)
                return;

            DataTable dt_PrintTable = new DataTable();
            dt_PrintTable.Columns.Add("ItemCode1");
            dt_PrintTable.Columns.Add("ItemCode2");
            dt_PrintTable.Columns.Add("DiscountValue");
            dt_PrintTable.Columns.Add("Expire");
            dt_PrintTable.Columns.Add("Batch");
            dt_PrintTable.Columns.Add("SellPrice");
            dt_PrintTable.Columns.Add("Qty");
            dt_PrintTable.Columns.Add("TotalSellPrice");
            dt_PrintTable.Columns.Add("ItemName");
            dt_PrintTable.Columns.Add("UOM");
            dt_PrintTable.Columns.Add("Height");
            dt_PrintTable.Columns.Add("Width");
            dt_PrintTable.Columns.Add("Length");
            dt_PrintTable.Columns.Add("TotalQty");
            dt_PrintTable.Columns.Add("ItemType");
            dt_PrintTable.Columns.Add("ItemDescription");
            dt_PrintTable.Columns.Add("ItemDescriptionEn");
            dt_PrintTable.Columns.Add("PiecesCount");

            dt_PrintTable.Columns.Add("SalesTax");
            dt_PrintTable.Columns.Add("DiscountRatio");
            dt_PrintTable.Columns.Add("DiscountRatio2");
            dt_PrintTable.Columns.Add("DiscountRatio3");
            dt_PrintTable.Columns.Add("SalesTaxRatio");
            dt_PrintTable.Columns.Add("PicPath");
            dt_PrintTable.Columns.Add("Serial");
            dt_PrintTable.Columns.Add("ManufactureDate");
            DataTable dt_PrintTableSubTaxDetails = new DataTable();

            var view = grdPrInvoice.FocusedView as GridView;
            for (int i = 0; i < view.RowCount; i++)
            {
                if (view.GetRowCellDisplayText(i, "ItemCode1") == string.Empty)
                    continue;

                DataRow dr = dt_PrintTable.NewRow();
                dr["ItemCode1"] = view.GetRowCellDisplayText(i, "ItemCode1");
                dr["ItemCode2"] = view.GetRowCellDisplayText(i, "ItemCode2");
                dr["DiscountValue"] = view.GetRowCellDisplayText(i, "DiscountValue") == "0" ? "" : view.GetRowCellDisplayText(i, "DiscountValue"); //mahmoud:18-12-2012, naser azemi issue
                dr["Expire"] = view.GetRowCellDisplayText(i, "Expire");
                dr["Batch"] = view.GetRowCellDisplayText(i, "Batch");
                dr["SellPrice"] = view.GetRowCellDisplayText(i, "SellPrice");
                dr["Qty"] = view.GetRowCellDisplayText(i, "Qty");
                dr["TotalSellPrice"] = view.GetRowCellDisplayText(i, "TotalSellPrice");
                dr["ItemName"] = view.GetRowCellDisplayText(i, "ItemId");
                dr["UOM"] = view.GetRowCellDisplayText(i, "UOM");
                dr["Height"] = view.GetRowCellDisplayText(i, "Height");
                dr["Width"] = view.GetRowCellDisplayText(i, "Width");
                dr["Length"] = view.GetRowCellDisplayText(i, "Length");
                dr["TotalQty"] = view.GetRowCellDisplayText(i, "TotalQty");
                dr["ItemType"] = view.GetRowCellValue(i, "ItemType");
                dr["ItemDescription"] = view.GetRowCellDisplayText(i, "ItemDescription");
                dr["ItemDescriptionEn"] = view.GetRowCellDisplayText(i, "ItemDescriptionEn");
                dr["PiecesCount"] = view.GetRowCellDisplayText(i, "PiecesCount");

                dr["SalesTax"] = view.GetRowCellDisplayText(i, "SalesTax");
                dr["DiscountRatio"] = view.GetRowCellDisplayText(i, "DiscountRatio");
                dr["DiscountRatio2"] = view.GetRowCellDisplayText(i, "DiscountRatio2");
                dr["DiscountRatio3"] = view.GetRowCellDisplayText(i, "DiscountRatio3");
                dr["SalesTaxRatio"] = view.GetDataRow(i)["SalesTaxRatio"];
                dr["PicPath"] = ErpUtils.GetGridLookUpValue(repItems, view.GetRowCellValue(i, "ItemId"), "PicPath");
                dr["Serial"] = view.GetRowCellDisplayText(i, "Serial");
                dr["ManufactureDate"] = view.GetRowCellDisplayText(i, "ManufactureDate");

                dt_PrintTable.Rows.Add(dr);
            }
            Reports.rpt_SL_ReturnInvoice r = new Reports.rpt_SL_ReturnInvoice
                (lkp_Customers.Text, lkp_InvoiceBook.Text, txtInvoiceCode.Text, dtInvoiceDate.Text, lkpStore.Text,
                cmbPayMethod.Text, lkp_Drawers.Text, txtNotes.Text, txt_Total.Text, txt_TaxValue.Text, txtDiscountRatio.Text,
                txtDiscountValue.Text, txtExpenses.Text, txtNet.Text, txt_paid.Text, txt_Remains.Text, dt_PrintTable,
                                Shared.lst_Users.Where(x => x.UserId == userId).Select(x => x.Name).FirstOrDefault(),
                txt_DeductTaxV.Text, txt_AddTaxV.Text, Convert.ToInt32(uc_Currency1.lkp_Crnc.EditValue),
                txtDriverName.Text, txtVehicleNumber.Text, txtDestination.Text, txtScaleSerial.Text, "", "", "", "", "", null, dt_PrintTableSubTaxDetails,"");

            string TemplateName = "rpt_SL_ReturnInvoice";
            if (lkp_InvoiceBook.GetColumnValue("PrintFileName") != null && lkp_InvoiceBook.GetColumnValue("PrintFileName").ToString().Trim() != string.Empty)
                TemplateName = lkp_InvoiceBook.GetColumnValue("PrintFileName").ToString();

            if (System.IO.File.Exists(Shared.ReportsPath + TemplateName + ".repx"))
                r.LoadLayout(Shared.ReportsPath + TemplateName + ".repx");

            r.LoadData();

            if (Shared.user.ShowPrintPreview == false)
                r.PrintDialog();
            else
                r.ShowPreview();

        }

        private void btnAddCustomer_Click(object sender, EventArgs e)
        {
            int customers_count = lkp_Customers.Properties.View.RowCount;
            int LastCustId = 0;

            new frm_SL_Customer().ShowDialog();
            int? goldencustomer = MyHelper.GetCustomers(out lst_Customers, Shared.user.DefaultCustGrp, out LastCustId);
            if (lst_Customers.Count > customers_count)
            {
                lkp_Customers.Properties.DataSource = lst_Customers;
                //lkp_Customers.EditValue = lkp_Customers.Properties.GetKeyValue(lkp_Customers.Properties.View.RowCount - 1);
                lkp_Customers.EditValue = LastCustId;
            }
        }



        private void lkp_Customers_Modified(object sender, EventArgs e)
        {
            DataModified = true;
        }

        private void Get_Items()
        {
            #region Get Items
            DB = new ERPDataContext();
            lstItems = (from i in DB.IC_Items
                        where i.ItemType != (int)ItemType.MatrixParent

                        where Shared.st_Store.SellRawMaterial == false ?
                                                       i.ItemType == (int)ItemType.Assembly : true
                        where invoiceId == 0 ? i.IsDeleted == false : true
                        select new ItemLkp
                        {
                            ItemCode1 = i.ItemCode1,
                            ItemCode2 = i.ItemCode2,
                            ItemId = i.ItemId,
                            ItemNameAr = i.ItemNameAr,
                            ItemNameEn = i.ItemNameEn,
                            MaxQty = i.MaxQty,
                            MinQty = i.MinQty,
                            ReorderLevel = i.ReorderLevel,
                            IsExpire = i.IsExpire,
                            PurchasePrice = i.PurchasePrice,
                            SellPrice = i.SmallUOMPrice,
                            PicPath = i.PicPath,
                            MediumUOM = i.MediumUOM,
                            LargeUOM = i.LargeUOM,
                            CategoryNameAr = i.IC_Category.CategoryNameAr,
                            CompanyNameAr = i.IC_Company.CompanyNameAr,
                            MediumUOMFactorDecimal = i.MediumUOMFactorDecimal,
                            LargeUOMFactorDecimal = i.LargeUOMFactorDecimal
                        }).ToList();

            repItems.DataSource = lstItems;
            repItems.DisplayMember = "ItemNameAr";
            repItems.ValueMember = "ItemId";
            #endregion
        }

        void grd_FocusOnItemId(string columnName)
        {
            GridView view = grdPrInvoice.FocusedView as GridView;
            view.FocusedColumn = view.Columns[columnName];

        }

        private void Reset()
        {
            chk_IsInTrns.Checked = false;

            txtNotes.EditValue = null;
            txtExpenses.EditValue = txtDiscountRatio.EditValue = txtDiscountValue.EditValue = 0;

            txt_TaxValue.EditValue = 0;
            txt_DeductTaxR.EditValue = txt_DeductTaxV.EditValue = 0;
            txt_AddTaxR.EditValue = txt_AddTaxV.EditValue = 0;

            txt_Total.EditValue = 0.0;
            txt_paid.EditValue = 0.0;
            txtNet.EditValue = 0.0;
            txt_PayAcc1_Paid.EditValue = 0.0;
            txt_PayAcc2_Paid.EditValue = 0.0;
            lkp_Drawers2.EditValue = null;

            txt_Remains.EditValue = 0.0;

            dtInvoiceDate.DateTime = MyHelper.Get_Server_DateTime();

            dtSLReturn_Details.Rows.Clear();

            cmbPayMethod.EditValue = Shared.user.SL_Return_PayMethod;

            txtInvoiceCode.ResetBackColor();
            txtInvoiceCode.ToolTipIconType = DevExpress.Utils.ToolTipIconType.None;
            txtInvoiceCode.ToolTip = string.Empty;
            txtInvoiceCode.ErrorText = string.Empty;
            txtDriverName.Text = txtVehicleNumber.Text = txtDestination.Text = txtScaleSerial.Text = string.Empty;

            pnlSrcPrc.Visible = false;
            btnSourceId.EditValue = null;
            txtSourceCode.Text = string.Empty;
            cmdProcess.EditValue = null;
        }

        private void BindDataSources()
        {
            DB = new DAL.ERPDataContext();
            //disable on credit invoice
            if (Shared.user.UserMakeOnCreditInv == false)
                txt_paid.Enabled = txt_Remains.Enabled = false;

            if (Shared.ItemsPostingAvailable)
            {
                if (Shared.StockIsPeriodic)
                {
                    lst_Cat = MyHelper.GetChildCategoriesList_Periodic();
                    if (lst_Cat.Where(x => x.SellAcc.HasValue == false || x.SellReturnAcc.HasValue == false
                                   || x.PurchaseAcc.HasValue == false || x.PurchaseReturnAcc.HasValue == false
                                   || x.OpenInventoryAcc.HasValue == false || x.CloseInventoryAcc.HasValue == false).Count() > 0)
                    {
                        XtraMessageBox.Show(
                            Shared.IsEnglish == true ? ResEn.ItemPosting : ResAr.ItemPosting,
                            Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        this.BeginInvoke(new MethodInvoker(this.Close));
                    }
                }
                else
                {
                    lst_Cat = MyHelper.GetChildCategoriesList();
                    if (lst_Cat.Where(x => x.SellAcc.HasValue == false || x.SellReturnAcc.HasValue == false || x.COGSAcc.HasValue == false ||
                        x.InvAcc.HasValue == false).Count() > 0)
                    {
                        XtraMessageBox.Show(
                            Shared.IsEnglish == true ? ResEn.ItemPosting : ResAr.ItemPosting,
                            Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        this.BeginInvoke(new MethodInvoker(this.Close));
                    }
                }
            }



            #region Get Stores
            int defaultStoreId = 0;

            if (Shared.InvoicePostToStore)
                stores_table = MyHelper.Get_Stores(true, out defaultStoreId, Shared.user.UserChangeStore, Shared.user.DefaultStore,
                Shared.UserId);
            else
                stores_table = MyHelper.Get_Stores(false, out defaultStoreId, Shared.user.UserChangeStore, Shared.user.DefaultStore,
                Shared.UserId);

            lkpStore.Properties.DataSource = stores_table;

            lkpStore.Properties.DisplayMember = "StoreNameAr";
            lkpStore.Properties.ValueMember = "StoreId";
            lkpStore.EditValue = defaultStoreId;
            #endregion

            #region LoadInvoicesBooks
            var empty_book = new
            {
                InvoiceBookId = (int?)null,
                InvoiceBookName = "",
                IsTaxable = (bool?)null,
                PrintFileName = (string)null,
            };

            var books = (from b in DB.ST_InvoiceBooks
                         where b.ProcessId == (int)Process.SellReturn
                         select new
                         {
                             InvoiceBookId = (int?)b.InvoiceBookId,
                             b.InvoiceBookName,
                             b.IsTaxable,
                             b.PrintFileName,
                         }).ToList();

            if (books.Count == 0)
                pnlBook.Visible = false;

            books.Insert(0, empty_book);
            lkp_InvoiceBook.Properties.DataSource = books;
            lkp_InvoiceBook.Properties.DisplayMember = "InvoiceBookName";
            lkp_InvoiceBook.Properties.ValueMember = "InvoiceBookId";
            lkp_InvoiceBook.EditValue = Shared.user.DefaultSLRet_InvBookId;
            #endregion

            Get_Items();

            #region Get Cost Centers
            lkpCostCenter.Properties.DataSource = HelperAcc.GetCostCentersLst(true);
            lkpCostCenter.Properties.DisplayMember = "CostCenterName";
            lkpCostCenter.Properties.ValueMember = "CostCenterId";
            #endregion

            #region Get_PayAccounts
            int defaultAcc = HelperAcc.LoadPayAccounts(dtPayAccounts, Shared.user.UserChangeDrawer, Shared.user.DefaultDrawer,
                Shared.IsEnglish);

            lkp_Drawers.Properties.ValueMember = "AccountId";
            lkp_Drawers.Properties.DisplayMember = "AccountName";
            lkp_Drawers.EditValue = defaultAcc;
            lkp_Drawers.Properties.DataSource = dtPayAccounts;

            lkp_Drawers2.Properties.ValueMember = "AccountId";
            lkp_Drawers2.Properties.DisplayMember = "AccountName";
            lkp_Drawers2.EditValue = null;
            lkp_Drawers2.Properties.DataSource = dtPayAccounts;
            #endregion

            #region dtPR_Details
            dtSLReturn_Details.Columns.Clear();
            dtSLReturn_Details.Columns.Add("PR_InvoiceDetailId");
            dtSLReturn_Details.Columns.Add("PR_InvoiceId");
            dtSLReturn_Details.Columns.Add("ItemId");
            dtSLReturn_Details.Columns.Add("ItemCode1");
            dtSLReturn_Details.Columns.Add("ItemCode2");
            dtSLReturn_Details.Columns.Add("ItemType").DefaultValue = 0;
            dtSLReturn_Details.Columns.Add("UOM");
            dtSLReturn_Details.Columns.Add("Qty").DefaultValue = 1;
            dtSLReturn_Details.Columns.Add("PurchasePrice").DefaultValue = 0;
            dtSLReturn_Details.Columns.Add("SellPrice").DefaultValue = 0;
            dtSLReturn_Details.Columns.Add("DiscountValue").DefaultValue = 0;
            dtSLReturn_Details.Columns.Add("DiscountRatio").DefaultValue = 0;
            dtSLReturn_Details.Columns.Add("DiscountRatio2").DefaultValue = 0;
            dtSLReturn_Details.Columns.Add("DiscountRatio3").DefaultValue = 0;
            dtSLReturn_Details.Columns.Add("TotalSellPrice").DefaultValue = 0;
            //dtSLReturn_Details.Columns.Add("CurrentQty");

            dtSLReturn_Details.Columns.Add("MediumUOMFactor");
            dtSLReturn_Details.Columns.Add("LargeUOMFactor");

            dtSLReturn_Details.Columns.Add("CompanyNameAr");
            dtSLReturn_Details.Columns.Add("UomIndex");
            dtSLReturn_Details.Columns.Add("VendorId");

            dtSLReturn_Details.Columns.Add("Length").DefaultValue = 1;
            dtSLReturn_Details.Columns.Add("Width").DefaultValue = 1;
            dtSLReturn_Details.Columns.Add("Height").DefaultValue = 1;
            dtSLReturn_Details.Columns.Add("PiecesCount").DefaultValue = 0;

            dtSLReturn_Details.Columns.Add("SalesTax").DefaultValue = 0;
            dtSLReturn_Details.Columns.Add("SalesTaxRatio").DefaultValue = 0;

            dtSLReturn_Details.Columns.Add("ItemDescription");
            dtSLReturn_Details.Columns.Add("ItemDescriptionEn");

            dtSLReturn_Details.Columns.Add("ParentItemId");
            dtSLReturn_Details.Columns.Add("M1");
            dtSLReturn_Details.Columns.Add("M2");
            dtSLReturn_Details.Columns.Add("M3");
            dtSLReturn_Details.Columns.Add("Serial");

            //for Elm Dawa2..Doesn't affect Qty, Mohammad 17-03-2018 
            dtSLReturn_Details.Columns.Add("ManufactureDate");

            #region Expire
            dtSLReturn_Details.Columns.Add("Expire");
            dtSLReturn_Details.Columns.Add("Batch");
            dtSLReturn_Details.Columns.Add("IsExpire");
            #endregion

            dtSLReturn_Details.Columns.Add("calcTaxBeforeDisc");

            dtSLReturn_Details.Columns.Add("CategoryId");

            dtSLReturn_Details.TableNewRow += new DataTableNewRowEventHandler(dt_TableNewRow);
            dtSLReturn_Details.RowDeleted += new DataRowChangeEventHandler(dt_RowChanged);
            dtSLReturn_Details.RowChanged += new DataRowChangeEventHandler(dt_RowChanged);
            grdPrInvoice.DataSource = dtSLReturn_Details;
            #endregion

            #region Get Vendors
            int lastVenId = 0;
            int? goldenVendor = MyHelper.GetVendors(out lst_Vendors, out lastVenId);
            rep_vendors.DisplayMember = "VenNameAr";
            rep_vendors.ValueMember = "VendorId";
            rep_vendors.DataSource = lst_Vendors;
            #endregion


            #region GetCustomers
            int LastCustId = 0;
            int? goldencustomer = MyHelper.GetCustomers(out lst_Customers, Shared.user.DefaultCustGrp, out LastCustId);

            lkp_Customers.Properties.DisplayMember = "CusNameAr";
            lkp_Customers.Properties.ValueMember = "CustomerId";
            lkp_Customers.Properties.DataSource = lst_Customers;
            lkp_Customers.EditValue = goldencustomer;
            #endregion

            #region UOM
            uom_list = DB.IC_UOMs.ToList();
            repUOM.DisplayMember = "Uom";
            repUOM.ValueMember = "UomId";
            repUOM.DataSource = MyHelper.GetUomDataTable(dtUOM);
            #endregion

            #region SalesEmp
            int? defaultEmp = MyHelper.GetSalesEmps(dt_SalesEmps, false, true, Shared.user.DefaultSalesRep);
            lkp_SalesEmp.Properties.ValueMember = "EmpId";
            lkp_SalesEmp.Properties.DisplayMember = "EmpName";
            lkp_SalesEmp.Properties.DataSource = dt_SalesEmps;
            if (lkp_SalesEmp.EditValue == null)
                lkp_SalesEmp.EditValue = defaultEmp;
            #endregion
        }

        private void LoadItemRow(DAL.IC_Item item, DataRow row)
        {


            if (item != null && item.ItemId > 0)
            {
                row["ItemId"] = item.ItemId;
                row["CategoryId"] = item.Category;
                row["ItemCode1"] = item.ItemCode1;
                row["ItemCode2"] = item.ItemCode2;
                row["ItemType"] = item.ItemType;
                row["PurchasePrice"] = Decimal.ToDouble(item.PurchasePrice);

                MyHelper.GetUOMs(item, dtUOM, uom_list);
                row["UOM"] = dtUOM.Rows[item.DfltSellUomIndx]["UomId"];
                row["UomIndex"] = item.DfltSellUomIndx;

                decimal uom_price = MyHelper.GetPriceLevelSellPrice(CustomerPriceLevel, item, item.DfltSellUomIndx);
                row["SellPrice"] = decimal.ToDouble(uom_price);

                if (frm_InvoiceDiscs.SLRet_DiscR1 > 0 ||
                    frm_InvoiceDiscs.SLRet_DiscR2 > 0 ||
                    frm_InvoiceDiscs.SLRet_DiscR3 > 0)
                {
                    if (frm_InvoiceDiscs.SLRet_DiscR1 > 0)
                        row["DiscountRatio"] = decimal.ToDouble(frm_InvoiceDiscs.SLRet_DiscR1);
                    if (frm_InvoiceDiscs.SLRet_DiscR2 > 0)
                        row["DiscountRatio2"] = decimal.ToDouble(frm_InvoiceDiscs.SLRet_DiscR2);
                    if (frm_InvoiceDiscs.SLRet_DiscR3 > 0)
                        row["DiscountRatio3"] = decimal.ToDouble(frm_InvoiceDiscs.SLRet_DiscR3);
                }
                else
                {
                    row["DiscountRatio"] = decimal.ToDouble(item.SalesDiscRatio / 100);
                    row["DiscountRatio2"] = decimal.ToDouble(0);
                    row["DiscountRatio3"] = decimal.ToDouble(0);
                }

                row["DiscountValue"] = decimal.ToDouble(Utilities.Calc_DiscountValue(Convert.ToDecimal(row["DiscountRatio"]),
                    Convert.ToDecimal(row["DiscountRatio2"]), Convert.ToDecimal(row["DiscountRatio3"]), Convert.ToDecimal(row["SellPrice"])));

                if (IsTaxable == null && Convert.ToBoolean(ErpUtils.GetGridLookUpValue(lkp_Customers, lkp_Customers.EditValue, "IsTaxable")))
                    row["SalesTaxRatio"] = item.SalesTaxRatio / 100;
                else if (IsTaxable == true)
                    row["SalesTaxRatio"] = item.SalesTaxRatio / 100;
                else
                    row["SalesTaxRatio"] = 0;

                row["calcTaxBeforeDisc"] = item.calcTaxBeforeDisc;

                row["Qty"] = "1";

                row["Length"] = Decimal.ToDouble(item.Length);
                row["Width"] = Decimal.ToDouble(item.Width);
                row["Height"] = Decimal.ToDouble(item.Height);

                if (frm_InvoiceDimenstions.SLRet_Height > 0)
                    row["Height"] = decimal.ToDouble(frm_InvoiceDimenstions.SLRet_Height);
                if (frm_InvoiceDimenstions.SLRet_Width > 0)
                    row["Width"] = decimal.ToDouble(frm_InvoiceDimenstions.SLRet_Width);
                if (frm_InvoiceDimenstions.SLRet_Length > 0)
                    row["Length"] = decimal.ToDouble(frm_InvoiceDimenstions.SLRet_Length);

                row["TotalSellPrice"] = "0";
                row["MediumUOMFactor"] = MyHelper.FractionToDouble(item.MediumUOMFactor);
                row["LargeUOMFactor"] = MyHelper.FractionToDouble(item.LargeUOMFactor);



                if (item.DfltSellUomIndx == 0)
                    row["PurchasePrice"] = Decimal.ToDouble(item.PurchasePrice);
                else if (item.DfltSellUomIndx == 1)
                    row["PurchasePrice"] = Decimal.ToDouble(item.PurchasePrice) * decimal.ToDouble(MyHelper.FractionToDouble(item.MediumUOMFactor));
                else if (item.DfltSellUomIndx == 2)
                    row["PurchasePrice"] = Decimal.ToDouble(item.PurchasePrice) * decimal.ToDouble(MyHelper.FractionToDouble(item.LargeUOMFactor));

                row["IsExpire"] = item.IsExpire;

                //get comapny name
                DB = new DAL.ERPDataContext();
                var compName = DB.IC_Companies.Where(c => c.CompanyId == item.Company).Select(c => c.CompanyNameAr).Single();
                row["CompanyNameAr"] = compName;

                //decimal currentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, item.ItemId, Convert.ToInt32(lkpStore.EditValue));
                //currentQty = MyHelper.getCalculatedUomQty(currentQty, Convert.ToByte(row["UomIndex"]), MyHelper.FractionToDouble(item.MediumUOMFactor), MyHelper.FractionToDouble(item.LargeUOMFactor));
                //row["CurrentQty"] = decimal.ToDouble(currentQty);

                row["ItemDescription"] = item.Description;
                row["ItemDescriptionEn"] = item.DescriptionEn;

                row["ParentItemId"] = item.mtrxParentItem;
                row["M1"] = item.mtrxAttribute1;
                row["M2"] = item.mtrxAttribute2;
                row["M3"] = item.mtrxAttribute3;
            }
        }


        double invoice_remains = 0;
        private void GetInvoice(int invId)
        {
            DB = new DAL.ERPDataContext();

            var inv = DB.SL_ReturnArchives.Where(v => v.SL_ReturnId == invId).SingleOrDefault();
            if (inv != null)
            {
                lkp_InvoiceBook.EditValue = inv.InvoiceBookId;
                invoice_remains = decimal.ToDouble(inv.Remains);
                lkp_Drawers.EditValue = inv.DrawerAccountId.ToString();

                lkp_Customers.EditValue = inv.CustomerId;
                txtInvoiceCode.Text = inv.ReturnCode;
                dtInvoiceDate.EditValue = inv.ReturnDate;
                lkpStore.EditValue = inv.StoreId;
                lkpCostCenter.EditValue = inv.CostCenterId;
                txtNotes.Text = inv.Notes;
                lkp_SalesEmp.EditValue = inv.SalesEmpId;

                chk_IsInTrns.Checked = (inv.Is_InTrans == true);

                uc_Currency1.lkp_Crnc.EditValue = inv.CrncId;
                uc_Currency1.txtRate.EditValue = inv.CrncRate;

                LoadSourceData(inv.ProcessId, inv.SourceId);

                GetInvoiceDetails(invId);

                txtExpenses.EditValue = decimal.ToDouble(inv.Expenses);

                txtDiscountRatio.EditValue = decimal.ToDouble(decimal.Round(inv.DiscountRatio * 100, 4));
                txt_DeductTaxR.EditValue = decimal.ToDouble(decimal.Round(inv.DeductTaxRatio * 100, 4));
                txt_AddTaxR.EditValue = decimal.ToDouble(decimal.Round(inv.AddTaxRatio * 100, 4));

                txtDiscountValue.EditValue = decimal.ToDouble(inv.DiscountValue);
                txt_TaxValue.EditValue = decimal.ToDouble(inv.TaxValue);
                txt_DeductTaxV.EditValue = decimal.ToDouble(inv.DeductTaxValue);
                txt_AddTaxV.EditValue = decimal.ToDouble(inv.AddTaxValue);

                txt_Total.EditValue = decimal.ToDouble(inv.Net - inv.TaxValue - inv.Expenses + inv.DiscountValue);
                txtNet.EditValue = decimal.ToDouble(inv.Net);
                txt_PayAcc1_Paid.EditValue = decimal.ToDouble(inv.Paid);

                if (inv.PayAccountId2.HasValue)
                    lkp_Drawers2.EditValue = inv.PayAccountId2.ToString();
                else
                    lkp_Drawers2.EditValue = null;

                if (inv.PayAcc2_Paid.HasValue)
                    txt_PayAcc2_Paid.EditValue = decimal.ToDouble(inv.PayAcc2_Paid.Value);
                else
                    txt_PayAcc2_Paid.EditValue = 0;
                txt_Remains.EditValue = decimal.ToDouble(inv.Remains);

                cmbPayMethod.EditValue = inv.PayMethod;

                txtScaleSerial.Text = inv.ScaleWeightSerial;
                txtDriverName.Text = inv.DriverName;
                txtVehicleNumber.Text = inv.VehicleNumber;
                txtDestination.Text = inv.Destination;
            }
            else
                invoice_remains = 0;
        }

        private void GetInvoiceDetails(int invoiceId)
        {
            dtSLReturn_Details.Rows.Clear();
            DB = new DAL.ERPDataContext();
            var details = (from d in DB.SL_ReturnDetailArchives
                           where d.SL_ReturnId == invoiceId
                           join i in DB.IC_Items on d.ItemId equals i.ItemId
                           orderby d.SL_ReturnDetailId

                           select new { detail = d, item = i }).ToList();

            foreach (var d in details)
            {
                DataRow row = dtSLReturn_Details.NewRow();

                row["PR_InvoiceDetailId"] = d.detail.SL_ReturnDetailId;
                row["PR_InvoiceId"] = d.detail.SL_ReturnId;
                row["ItemId"] = d.detail.ItemId;
                row["CategoryId"] = d.item.Category;
                row["ItemCode1"] = d.item.ItemCode1;
                row["ItemCode2"] = d.item.ItemCode2;
                row["ItemType"] = d.item.ItemType;
                row["UOM"] = d.detail.UOMId;
                row["Qty"] = decimal.ToDouble(d.detail.Qty);

                if (d.detail.Height != null)
                    row["Height"] = decimal.ToDouble(d.detail.Height.Value);
                if (d.detail.Length != null)
                    row["Length"] = decimal.ToDouble(d.detail.Length.Value);
                if (d.detail.Width != null)
                    row["Width"] = decimal.ToDouble(d.detail.Width.Value);
                row["PiecesCount"] = decimal.ToDouble(d.detail.PiecesCount);

                row["PurchasePrice"] = decimal.ToDouble(d.detail.PurchasePrice);
                row["SellPrice"] = decimal.ToDouble(d.detail.SellPrice);

                row["SalesTaxRatio"] = d.detail.SalesTaxRatio;
                row["SalesTax"] = decimal.ToDouble(d.detail.SalesTax);
                row["calcTaxBeforeDisc"] = d.item.calcTaxBeforeDisc;

                row["DiscountValue"] = decimal.ToDouble(d.detail.DiscountValue);
                row["DiscountRatio"] = decimal.ToDouble(d.detail.DiscountRatio);
                row["DiscountRatio2"] = decimal.ToDouble(d.detail.DiscountRatio2);
                row["DiscountRatio3"] = decimal.ToDouble(d.detail.DiscountRatio3);

                row["CompanyNameAr"] = d.item.IC_Company.CompanyNameAr;
                row["MediumUOMFactor"] = MyHelper.FractionToDouble(d.item.MediumUOMFactor);
                row["LargeUOMFactor"] = MyHelper.FractionToDouble(d.item.LargeUOMFactor);

                if (d.detail.Expire.HasValue)
                    row["Expire"] = d.detail.Expire;
                row["Batch"] = d.detail.Batch;
                row["Serial"] = d.detail.Serial;
                row["IsExpire"] = d.item.IsExpire;

                row["VendorId"] = d.detail.VendorId;

                //get store qty                                
                //decimal currentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, d.detail.ItemId, d.detail.SL_Return.StoreId);
                //currentQty = MyHelper.getCalculatedUomQty(currentQty, d.detail.UOMIndex, MyHelper.FractionToDouble(d.item.MediumUOMFactor), MyHelper.FractionToDouble(d.item.LargeUOMFactor));
                //row["CurrentQty"] = decimal.ToDouble(currentQty);

                row["TotalSellPrice"] = decimal.ToDouble(d.detail.TotalSellPrice);
                row["UomIndex"] = d.detail.UOMIndex;

                row["ItemDescription"] = d.detail.ItemDescription;
                row["ItemDescriptionEn"] = d.detail.ItemDescriptionEn;

                row["ParentItemId"] = d.item.mtrxParentItem;
                row["M1"] = d.item.mtrxAttribute1;
                row["M2"] = d.item.mtrxAttribute2;
                row["M3"] = d.item.mtrxAttribute3;

                row["ManufactureDate"] = d.detail.ManufactureDate;

                dtSLReturn_Details.Rows.Add(row);
            }
            dtSLReturn_Details.AcceptChanges();
            DataModified = false;

        }

        private void LoadInvoice()
        {
            Reset();

            if (invoiceId > 0)
            {
                GetInvoice(invoiceId);
            }
            else
            {
                txtNotes.Text = Shared.user.InvoicesNotes;

                lkpStore_EditValueChanged(lkpStore, EventArgs.Empty);

                FocusItemCode1(Shared.user.FocusGridInInvoices);

                if (customerId != 0)
                    lkp_Customers.EditValue = customerId;

            }
        }

        private void FocusItemCode1(bool focusGrid)
        {
            if (focusGrid)
            {
                grdPrInvoice.Focus();
                var view = (grdPrInvoice.FocusedView as DevExpress.XtraGrid.Views.Grid.GridView);
                view.FocusedColumn = view.Columns[Shared.user.InvoicesUseSearchItems ? "ItemId" : "ItemCode1"];//mahmoud                        
            }
            else
                lkp_Customers.Focus();
        }


        public void Save_Invoice()
        {
            GridView view = grdPrInvoice.FocusedView as GridView;
            grdPrInvoice.RefreshDataSource();
            view.RefreshData();
            Get_TotalAccount();

            //save invoice
            DB = new DAL.ERPDataContext();
            DAL.SL_Return pr;
            if (invoiceId > 0)
            {
                pr = DB.SL_Returns.Where(x => x.SL_ReturnId == invoiceId).FirstOrDefault();
                pr.LastUpdateUserId = Shared.UserId;
                pr.LastUpdateDate = DateTime.Now;

                MyHelper.UpdateST_UserLog(DB, txtInvoiceCode.Text, lkp_Customers.Text,
                (int)FormAction.Edit, (int)FormsNames.SL_Return);
            }
            else
            {
                pr = new SL_Return();
                pr.UserId = Shared.UserId;
                pr.JornalId = 0;
                DB.SL_Returns.InsertOnSubmit(pr);

                MyHelper.UpdateST_UserLog(DB, txtInvoiceCode.Text, lkp_Customers.Text,
                (int)FormAction.Add, (int)FormsNames.SL_Return);
            }

            #region SL Return
            if (lkp_InvoiceBook.EditValue == null || Convert.ToInt32(lkp_InvoiceBook.EditValue) == 0)
                pr.InvoiceBookId = null;
            else
                pr.InvoiceBookId = Convert.ToInt32(lkp_InvoiceBook.EditValue);

            if (lkpCostCenter.EditValue == null || lkpCostCenter.EditValue.ToString() == string.Empty || Convert.ToInt32(lkpCostCenter.EditValue) == 0)
                pr.CostCenterId = null;
            else
                pr.CostCenterId = Convert.ToInt32(lkpCostCenter.EditValue);

            pr.CustomerId = Convert.ToInt32(lkp_Customers.EditValue);
            pr.ReturnCode = txtInvoiceCode.Text.Trim();
            pr.ReturnDate = dtInvoiceDate.DateTime;
            pr.StoreId = Convert.ToInt32(lkpStore.EditValue);
            pr.Notes = txtNotes.Text;

            if (cmdProcess.EditValue == null)
                pr.ProcessId = null;
            else
                pr.ProcessId = Convert.ToInt32(cmdProcess.EditValue);

            if (btnSourceId.EditValue == null || btnSourceId.EditValue.ToString() == string.Empty)
                pr.SourceId = null;
            else
                pr.SourceId = Convert.ToInt32(btnSourceId.EditValue);

            //if (Shared.InvoicePostToStore)
            //    pr.Is_InTrans = true;
            //else
            //    pr.Is_InTrans = chk_IsInTrns.Checked;

            pr.PayMethod = (bool?)cmbPayMethod.EditValue;

            pr.DiscountRatio = Convert.ToDecimal(txtDiscountRatio.EditValue) / 100;
            pr.DiscountValue = Convert.ToDecimal(txtDiscountValue.EditValue);
            pr.Expenses = Convert.ToDecimal(txtExpenses.EditValue);

            pr.TaxValue = Convert.ToDecimal(txt_TaxValue.EditValue);
            pr.DeductTaxValue = Convert.ToDecimal(txt_DeductTaxV.EditValue);
            pr.DeductTaxRatio = Convert.ToDecimal(txt_DeductTaxR.EditValue) / 100;

            pr.AddTaxValue = Convert.ToDecimal(txt_AddTaxV.EditValue);
            pr.AddTaxRatio = Convert.ToDecimal(txt_AddTaxR.EditValue) / 100;

            pr.Net = Convert.ToDecimal(txtNet.EditValue);
            pr.Paid = Convert.ToDecimal(txt_PayAcc1_Paid.EditValue);

            if (lkp_Drawers2.EditValue != null)
                pr.PayAccountId2 = Convert.ToInt32(lkp_Drawers2.EditValue);
            else
                pr.PayAccountId2 = null;
            pr.PayAcc2_Paid = Convert.ToDecimal(txt_PayAcc2_Paid.EditValue);
            pr.Remains = Convert.ToDecimal(txt_Remains.EditValue);

            if (lkp_SalesEmp.EditValue == null)
                pr.SalesEmpId = null;
            else
                pr.SalesEmpId = Convert.ToInt32(lkp_SalesEmp.EditValue);

            pr.DrawerAccountId = Convert.ToInt32(lkp_Drawers.EditValue);

            pr.CrncId = Convert.ToInt32(uc_Currency1.lkp_Crnc.EditValue);
            pr.CrncRate = Convert.ToDecimal(uc_Currency1.txtRate.EditValue);

            pr.ScaleWeightSerial = txtScaleSerial.Text;
            pr.DriverName = txtDriverName.Text;
            pr.VehicleNumber = txtVehicleNumber.Text;
            pr.Destination = txtDestination.Text;
            #endregion
            userId = pr.UserId;
            DB.SubmitChanges();

            #region Delete ItemStore & Sl RetuenDetail
            var Return_details_ids = pr.SL_ReturnDetails.Select(s => s.SL_ReturnDetailId).ToList();
            var invoice_itemstores = DB.IC_ItemStores.Where(s => s.ProcessId == (int)Process.SellReturn
                && Return_details_ids.Contains(s.SourceId));
            DB.IC_ItemStores.DeleteAllOnSubmit(invoice_itemstores);
            DB.SL_ReturnDetails.DeleteAllOnSubmit(pr.SL_ReturnDetails);
            #endregion

            decimal CostOfSoldGoods = 0;//used for continual inventory
            List<StoreItem> lst_outitems = new List<StoreItem>();
            //int[] store_id = new int[dtSLReturn_Details.Rows.Count];
            for (int x = 0; x < dtSLReturn_Details.Rows.Count; x++)
            {
                #region SL ReturnDetail
                if (dtSLReturn_Details.Rows[x].RowState == DataRowState.Deleted)
                    continue;

                decimal MediumUOMFactor = 1;
                decimal LargeUOMFactor = 1;

                DAL.SL_ReturnDetail detail = new DAL.SL_ReturnDetail();
                detail.SL_ReturnId = pr.SL_ReturnId;
                detail.ItemId = Convert.ToInt32(dtSLReturn_Details.Rows[x]["ItemId"]);
                detail.UOMId = Convert.ToInt32(dtSLReturn_Details.Rows[x]["UOM"]);
                detail.UOMIndex = Convert.ToByte(dtSLReturn_Details.Rows[x]["UomIndex"]);
                detail.Qty = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["Qty"]);

                detail.Height = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["Height"]);
                detail.Length = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["Length"]);
                detail.Width = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["Width"]);
                detail.PiecesCount = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["PiecesCount"]);

                #region Expire
                if (dtSLReturn_Details.Rows[x]["Expire"] == DBNull.Value)
                    detail.Expire = null;
                else
                {
                    DateTime temp = Convert.ToDateTime(dtSLReturn_Details.Rows[x]["Expire"]);
                    temp = temp.AddDays(-temp.Day + 1);
                    detail.Expire = temp;
                }

                if (dtSLReturn_Details.Rows[x]["Batch"] == DBNull.Value
                    || dtSLReturn_Details.Rows[x]["Batch"].ToString().Trim() == string.Empty)
                    detail.Batch = null;
                else
                    detail.Batch = dtSLReturn_Details.Rows[x]["Batch"].ToString();

                if (dtSLReturn_Details.Rows[x]["Serial"] == DBNull.Value
                    || dtSLReturn_Details.Rows[x]["Serial"].ToString().Trim() == string.Empty)
                    detail.Serial = null;
                else
                    detail.Serial = dtSLReturn_Details.Rows[x]["Serial"].ToString();
                #endregion

                detail.SellPrice = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["SellPrice"]);
                detail.PurchasePrice = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["PurchasePrice"]);
                detail.SalesTax = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["SalesTax"]);
                detail.SalesTaxRatio = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["SalesTaxRatio"]);

                detail.DiscountRatio = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["DiscountRatio"]);
                detail.DiscountRatio2 = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["DiscountRatio2"]);
                detail.DiscountRatio3 = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["DiscountRatio3"]);
                detail.DiscountValue = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["DiscountValue"]);

                if (dtSLReturn_Details.Rows[x]["MediumUOMFactor"].ToString() != string.Empty)
                    MediumUOMFactor = MyHelper.FractionToDouble(dtSLReturn_Details.Rows[x]["MediumUOMFactor"].ToString());
                if (dtSLReturn_Details.Rows[x]["LargeUOMFactor"].ToString() != string.Empty)
                    LargeUOMFactor = MyHelper.FractionToDouble(dtSLReturn_Details.Rows[x]["LargeUOMFactor"].ToString());

                if (dtSLReturn_Details.Rows[x]["VendorId"].ToString() != string.Empty)
                    detail.VendorId = Convert.ToInt32(dtSLReturn_Details.Rows[x]["VendorId"]);
                else
                    detail.VendorId = null;

                detail.TotalSellPrice = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["TotalSellPrice"]);
                detail.ItemDescription = dtSLReturn_Details.Rows[x]["ItemDescription"].ToString();
                detail.ItemDescriptionEn = dtSLReturn_Details.Rows[x]["ItemDescriptionEn"].ToString();
                //store_id[x] = pr.StoreId;
                if (dtSLReturn_Details.Rows[x]["ManufactureDate"] != null && dtSLReturn_Details.Rows[x]["ManufactureDate"] != DBNull.Value)
                    detail.ManufactureDate = Convert.ToDateTime(dtSLReturn_Details.Rows[x]["ManufactureDate"]);

                #endregion

                DB.SL_ReturnDetails.InsertOnSubmit(detail);

                #region Add Item To Store
                /*
                if ((Shared.InvoicePostToStore == true && Shared.StockIsPeriodic) ||
                    (Shared.InvoicePostToStore == true && Shared.StockIsPeriodic == false))
                {
                    //check if item is not service subtract from store
                    //decimal item_PurchasePrice = MyHelper.GetAveragePriceForSalesReturn(detail.UOMIndex, MediumUOMFactor, LargeUOMFactor, detail.ItemId,
                    //                                    pr.StoreId, pr.ReturnDate, detail.PurchasePrice);                    

                    decimal total_Qty = (detail.Height.HasValue ? detail.Height.Value : 1)
                                    * (detail.Length.HasValue ? detail.Length.Value : 1)
                                    * (detail.Width.HasValue ? detail.Width.Value : 1)
                                    * detail.Qty;

                    if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish)
                        total_Qty = detail.Qty;

                    decimal item_PurchasePrice = detail.TotalSellPrice / total_Qty;

                    if (Convert.ToInt32(dtSLReturn_Details.Rows[x]["ItemType"]) != (int)ItemType.Service
                        && Convert.ToInt32(dtSLReturn_Details.Rows[x]["ItemType"]) != (int)ItemType.Subtotal)
                    {
                        if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Multiply)
                        {
                            MyHelper.AddItemToStore(detail.ItemId, pr.StoreId, total_Qty, detail.UOMIndex,
                                MediumUOMFactor, LargeUOMFactor, detail.VendorId, detail.SL_ReturnDetailId, (int)Process.SellReturn,
                                true, total_Qty * item_PurchasePrice * pr.CrncRate, pr.ReturnDate, detail.Expire, detail.Batch, 0, 0, 0, detail.PiecesCount,
                                ParentItemId, M1, M2, M3, 0, null);
                            CostOfSoldGoods += total_Qty * item_PurchasePrice * pr.CrncRate;
                        }
                        else
                        {
                            MyHelper.AddItemToStore(detail.ItemId, pr.StoreId, total_Qty, detail.UOMIndex,
                                MediumUOMFactor, LargeUOMFactor, detail.VendorId, detail.SL_ReturnDetailId, (int)Process.SellReturn,
                                true, total_Qty * item_PurchasePrice * pr.CrncRate, pr.ReturnDate, detail.Expire, detail.Batch,
                                detail.Length.Value, detail.Width.Value, detail.Height.Value, detail.PiecesCount,
                                ParentItemId, M1, M2, M3, 0, null);
                            CostOfSoldGoods += total_Qty * item_PurchasePrice * pr.CrncRate;
                        }
                    }
                }
                else if (Shared.InvoicePostToStore == false && Shared.StockIsPeriodic == false)//just calc cost of sold goods
                {
                    //check if item is not service subtract from store
                    decimal item_PurchasePrice = MyHelper.GetAveragePriceForSalesReturn(detail.UOMIndex, MediumUOMFactor, LargeUOMFactor, detail.ItemId,
                                                        pr.StoreId, pr.ReturnDate, detail.PurchasePrice);

                    decimal total_Qty = (detail.Height.HasValue ? detail.Height.Value : 1)
                                    * (detail.Length.HasValue ? detail.Length.Value : 1)
                                    * (detail.Width.HasValue ? detail.Width.Value : 1)
                                    * detail.Qty;

                    if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish)
                        total_Qty = detail.Qty;

                    if (Convert.ToInt32(dtSLReturn_Details.Rows[x]["ItemType"]) != (int)ItemType.Service
                        && Convert.ToInt32(dtSLReturn_Details.Rows[x]["ItemType"]) != (int)ItemType.Subtotal)
                    {
                        CostOfSoldGoods += total_Qty * item_PurchasePrice * pr.CrncRate;
                    }
                }
                */
                #endregion

                int itemType = Convert.ToInt32(dtSLReturn_Details.Rows[x]["ItemType"]);
                /*Add to store*/
                if (itemType != (int)ItemType.Subtotal)
                {
                    if (dtSLReturn_Details.Rows[x]["MediumUOMFactor"].ToString() != string.Empty)
                        MediumUOMFactor = MyHelper.FractionToDouble(dtSLReturn_Details.Rows[x]["MediumUOMFactor"].ToString());
                    if (dtSLReturn_Details.Rows[x]["LargeUOMFactor"].ToString() != string.Empty)
                        LargeUOMFactor = MyHelper.FractionToDouble(dtSLReturn_Details.Rows[x]["LargeUOMFactor"].ToString());

                    int? ParentItemId = ErpUtils.GetMtrxVal(dtSLReturn_Details.Rows[x]["ParentItemId"]);
                    int? M1 = ErpUtils.GetMtrxVal(dtSLReturn_Details.Rows[x]["M1"]);
                    int? M2 = ErpUtils.GetMtrxVal(dtSLReturn_Details.Rows[x]["M2"]);
                    int? M3 = ErpUtils.GetMtrxVal(dtSLReturn_Details.Rows[x]["M3"]);

                    decimal CostPrice = MyHelper.GetLastCostPrice(lstItems.Where(z => z.ItemId == detail.ItemId).First(), detail.UOMIndex, DB,pr.ReturnDate,pr.StoreId);
                    lst_outitems.Add(new StoreItem(Shared.st_Store.MultiplyDimensions, detail.ItemId, itemType, detail.SL_ReturnDetailId, detail.UOMIndex, detail.Qty,
                        MediumUOMFactor, LargeUOMFactor, detail.Expire, detail.Batch, null, detail.VendorId, detail.Length.Value, detail.Width.Value, detail.Height.Value,
                        detail.PiecesCount, ParentItemId, M1, M2, M3, detail.TotalSellPrice * pr.CrncRate, CostPrice * detail.Qty, detail, detail.Serial, detail.Serial2,
                        Convert.ToInt32(dtSLReturn_Details.Rows[x]["CategoryId"]), detail.Pack));
                }
            }

            DB.SubmitChanges();
            lst_outitems.ForEach(x => x.SourceId = ((SL_ReturnDetail)x.Source).SL_ReturnDetailId);
            var lst = MyHelper.Add_to_store((int)Process.SellReturn, pr.ReturnDate, lst_outitems, pr.StoreId);
            CostOfSoldGoods = lst.Select(x => x.PurchasePrice).ToList().DefaultIfEmpty(0).Sum();
            if (Shared.InvoicePostToStore)
                DB.IC_ItemStores.InsertAllOnSubmit(lst);
            DB.SubmitChanges();


            invoiceId = pr.SL_ReturnId;

            if (cmdProcess.EditValue != null && Convert.ToInt32(cmdProcess.EditValue) == (int)Process.Scale &&
                btnSourceId.EditValue != null)
            {
                var sc = (from q in DB.ScaleWeights
                          where q.ScaleWeightId == Convert.ToInt32(btnSourceId.EditValue)
                          select q).FirstOrDefault();
                if (sc != null)
                    sc.InvoiceId = pr.SL_ReturnId;
            }
            DB.SubmitChanges();

            CreateJournal(DB, pr, CostOfSoldGoods,
                lst_Cat, lst_outitems, lst);

            DoValidate();
            DataModified = false;
            dtSLReturn_Details.AcceptChanges();

            //XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.MsgSave : ResSLAr.MsgSave, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void CreateJournal(ERPDataContext DB, DAL.SL_Return pr, decimal CostOfSoldGoods,
            List<IC_Category> lstCats, List<StoreItem> lstSoldItems, List<IC_ItemStore> lstInvItems)
        {
            string note =
                (Shared.IsEnglish == true ? ResSLEn.txtSLReturnInvNumber : ResSLAr.txtSLReturnInvNumber) + " " + pr.ReturnCode + "  - " +
                    (Shared.IsEnglish == true ? "from " : "من ") + " " + lkp_Customers.Text;

            int BranchId = Convert.ToInt32(lkpStore.EditValue);
            if (lkpStore.GetColumnValue("ParentId") != null)
                BranchId = Convert.ToInt32(lkpStore.GetColumnValue("ParentId"));

            int CustomerAccountId = lst_Customers.Where(x => x.CustomerId == pr.CustomerId).Select(x => x.AccountId.Value).FirstOrDefault();

            #region Save_Jornal
            DAL.ACC_Journal jornal;
            if (pr.JornalId > 0)
                jornal = DB.ACC_Journals.Where(x => x.JournalId == pr.JornalId).FirstOrDefault();
            else
            {
                jornal = new ACC_Journal();
                jornal.InsertDate = dtInvoiceDate.DateTime;
                jornal.InsertUser = Shared.UserId;
                jornal.JCode = HelperAcc.Get_Jornal_Code();
                jornal.IsPosted = !Shared.OfflinePostToGL;
                jornal.Monthly_Code = HelperAcc.Get_Jornal_Monthly_Code(jornal.InsertDate, jornal.ProcessId);
                DB.ACC_Journals.InsertOnSubmit(jornal);
            }

            jornal.JNumber = pr.ReturnCode;
            jornal.LastUpdateDate = MyHelper.Get_Server_DateTime();
            jornal.LastUpdateUser = Shared.UserId;
            jornal.JNotes = note;
            jornal.ProcessId = (int)Process.SellReturn;
            jornal.SourceId = pr.SL_ReturnId;
            jornal.StoreId = BranchId;
            jornal.InsertDate = dtInvoiceDate.DateTime;
            jornal.CrncId = pr.CrncId;
            jornal.CrncRate = pr.CrncRate;
            #endregion

            DB.SubmitChanges();
            pr.JornalId = jornal.JournalId;

            DB.ACC_JournalDetails.DeleteAllOnSubmit(DB.ACC_JournalDetails.Where(x => x.JournalId == pr.JornalId));

            decimal total_Returns = pr.Net - pr.TaxValue + pr.DiscountValue + pr.Expenses + pr.DeductTaxValue - pr.AddTaxValue;

            int? costCenter = pr.CostCenterId;
            //int? costCenter = Convert.ToInt32(lkpStore.GetColumnValue("CostCenterId"));      // تحميل مركز تكلفة المخزن
            //if (costCenter == 0)
            //    costCenter = null;

            #region Sales Return
            /*قيد ارتجاع النقدية */
            /*من حساب مردودات و مسموحات المبيعات*/
            if (!Shared.ItemsPostingAvailable)
            {
                DAL.ACC_JournalDetail jornal_Detail_1 = new DAL.ACC_JournalDetail();
                jornal_Detail_1.JournalId = jornal.JournalId;
                jornal_Detail_1.AccountId = Convert.ToInt32(lkpStore.GetColumnValue("SellReturnAccount"));      // حساب مردودات و مسموحات مبيعات المخزن
                jornal_Detail_1.CostCenter = costCenter;
                jornal_Detail_1.Credit = 0;
                jornal_Detail_1.Debit = total_Returns - pr.Expenses;
                jornal_Detail_1.Notes = note;
                jornal_Detail_1.CrncId = pr.CrncId;
                jornal_Detail_1.CrncRate = pr.CrncRate;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_1);
            }
            else
            {
                //get sold and store data
                var soldItems1 = (from x in lstSoldItems
                                  group x by new { x.CategoryId, x.ItemId } into grp
                                  select new
                                  {
                                      CatId = grp.Key.CategoryId,
                                      ItemId = grp.Key.ItemId,
                                      TotalCost = 0,
                                      TotalSell = grp.Sum(z => z.TotalLocalSellPrice)
                                  }).ToList();

                var storeItems1 = (from x in lstInvItems
                                   group x by x.ItemId into grp
                                   select new
                                   {
                                       ItemId = grp.Key,
                                       catId = soldItems1.Where(x => x.ItemId == grp.Key).Select(x => x.CatId).First(),
                                       TotalCost = grp.Sum(z => z.PurchasePrice),
                                       TotalSell = 0
                                   }).ToList();

                //group by category
                var soldItems2 = (from x in soldItems1
                                  group x by x.CatId into grp
                                  select new ItemPosting
                                  {
                                      catId = grp.Key,
                                      Cost = 0,
                                      Price = grp.Sum(z => z.TotalSell)
                                  }).ToList();

                var storeItems2 = (from x in storeItems1
                                   group x by x.catId into grp
                                   select new ItemPosting
                                   {
                                       catId = grp.Key,
                                       Cost = grp.Sum(z => z.TotalCost),
                                       Price = 0
                                   }).ToList();

                //summary rows per category
                List<ItemPosting> lstRows;
                if (!Shared.StockIsPeriodic)
                    lstRows = (from x in soldItems2.Union(storeItems2)
                               group x by x.catId into grp
                               select new ItemPosting
                               {
                                   catId = grp.Key,
                                   Cost = grp.Sum(x => x.Cost),
                                   Price = grp.Sum(x => x.Price),
                                   SellReturnAcc = lstCats.Where(x => x.CategoryId == grp.Key).Select(x => x.SellReturnAcc.Value).First(),
                                   COGSAcc = lstCats.Where(x => x.CategoryId == grp.Key).Select(x => x.COGSAcc.Value).First(),
                                   InvAcc = lstCats.Where(x => x.CategoryId == grp.Key).Select(x => x.InvAcc.Value).First(),
                               }).ToList();
                else
                    lstRows = (from x in soldItems2.Union(storeItems2)
                               group x by x.catId into grp
                               select new ItemPosting
                               {
                                   catId = grp.Key,
                                   Cost = grp.Sum(x => x.Cost),
                                   Price = grp.Sum(x => x.Price),
                                   SellReturnAcc = lstCats.Where(x => x.CategoryId == grp.Key).Select(x => x.SellReturnAcc.Value).First(),
                               }).ToList();
                //post sales
                var salesRows = from x in lstRows
                                    //where x.Price > 0
                                group x by x.SellReturnAcc into grp
                                select new
                                {
                                    SellReturnAcc = grp.Key,
                                    Price = grp.Sum(x => x.Price)
                                };
                foreach (var r in salesRows)
                {

                    DAL.ACC_JournalDetail jornal_Detail_1 = new DAL.ACC_JournalDetail();
                    jornal_Detail_1.JournalId = jornal.JournalId;
                    jornal_Detail_1.AccountId = r.SellReturnAcc;
                    jornal_Detail_1.CostCenter = costCenter;
                    jornal_Detail_1.Credit = 0;
                    jornal_Detail_1.Debit = r.Price / pr.CrncRate;
                    jornal_Detail_1.Notes = note;
                    jornal_Detail_1.CrncId = pr.CrncId;
                    jornal_Detail_1.CrncRate = pr.CrncRate;
                    DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_1);

                }

                //post inv
                var invRows = from x in lstRows
                                  //where x.Cost > 0
                              group x by x.InvAcc into grp
                              select new
                              {
                                  InvAcc = grp.Key,
                                  Cost = grp.Sum(x => x.Cost)
                              };
                foreach (var r in invRows)
                {
                    DAL.ACC_JournalDetail jdCost2 = new DAL.ACC_JournalDetail();
                    jdCost2.JournalId = jornal.JournalId;
                    jdCost2.AccountId = r.InvAcc;
                    jdCost2.Credit = 0;
                    jdCost2.Debit = r.Cost / pr.CrncRate;
                    jdCost2.CostCenter = costCenter;
                    jdCost2.Notes = note;
                    jdCost2.CrncId = pr.CrncId;
                    jdCost2.CrncRate = pr.CrncRate;
                    DB.ACC_JournalDetails.InsertOnSubmit(jdCost2);
                }

                //post cogs
                var cogsRows = from x in lstRows
                                   //where x.Cost > 0
                               group x by x.COGSAcc into grp
                               select new
                               {
                                   InvAcc = grp.Key,
                                   Cost = grp.Sum(x => x.Cost)
                               };
                foreach (var r in cogsRows)
                {
                    DAL.ACC_JournalDetail jdCost1 = new DAL.ACC_JournalDetail();
                    jdCost1.JournalId = jornal.JournalId;
                    jdCost1.AccountId = r.InvAcc;
                    jdCost1.Credit = r.Cost / pr.CrncRate;
                    jdCost1.Debit = 0;
                    jdCost1.Notes = note;
                    jdCost1.CrncId = pr.CrncId;
                    jdCost1.CrncRate = pr.CrncRate;
                    jdCost1.CostCenter = costCenter;
                    DB.ACC_JournalDetails.InsertOnSubmit(jdCost1);
                }
            }
            #endregion

            /*من حساب ض ع */
            if (pr.TaxValue > 0)
            {
                DAL.ACC_JournalDetail jornal_Detail_Tax = new DAL.ACC_JournalDetail();
                jornal_Detail_Tax.JournalId = jornal.JournalId;
                jornal_Detail_Tax.AccountId = Shared.st_Store.TaxAcc.Value;//حساب ضريبة المبيعات
                jornal_Detail_Tax.CostCenter = costCenter;
                jornal_Detail_Tax.Credit = 0;
                jornal_Detail_Tax.Debit = pr.TaxValue;
                jornal_Detail_Tax.Notes = note + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Tax : ResSLAr.txt_Tax);
                jornal_Detail_Tax.CrncId = pr.CrncId;
                jornal_Detail_Tax.CrncRate = pr.CrncRate;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_Tax);
            }

            /*من حساب ض الاضافة */
            if (pr.AddTaxValue > 0)
            {
                DAL.ACC_JournalDetail jornal_Detail_Tax = new DAL.ACC_JournalDetail();
                jornal_Detail_Tax.JournalId = jornal.JournalId;
                jornal_Detail_Tax.AccountId = Shared.st_Store.SalesAddTaxAccount.Value;//حساب ضريبة الاضافة
                jornal_Detail_Tax.CostCenter = costCenter;
                jornal_Detail_Tax.Credit = 0;
                jornal_Detail_Tax.Debit = pr.AddTaxValue;
                jornal_Detail_Tax.Notes = note + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Tax : ResSLAr.txt_Tax);
                jornal_Detail_Tax.CrncId = pr.CrncId;
                jornal_Detail_Tax.CrncRate = pr.CrncRate;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_Tax);
            }

            /*الى حســاب كل من*/
            /* حساب العميل*/
            DAL.ACC_JournalDetail jornal_Detail_2 = new DAL.ACC_JournalDetail();
            jornal_Detail_2.JournalId = jornal.JournalId;
            jornal_Detail_2.AccountId = CustomerAccountId;          //حساب عميل  
            jornal_Detail_2.CostCenter = costCenter;
            jornal_Detail_2.Credit = total_Returns - pr.Expenses + pr.TaxValue - pr.DeductTaxValue + pr.AddTaxValue;
            jornal_Detail_2.Debit = 0;
            jornal_Detail_2.Notes = note;
            jornal_Detail_2.CrncId = pr.CrncId;
            jornal_Detail_2.CrncRate = pr.CrncRate;
            DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_2);

            /*الى حساب ضريبة الخصم*/
            #region Deduct_Tax_Value
            if (pr.DeductTaxValue > 0)
            {
                DAL.ACC_JournalDetail jornal_Detail_deduct_Tax = new DAL.ACC_JournalDetail();
                jornal_Detail_deduct_Tax.JournalId = jornal.JournalId;
                jornal_Detail_deduct_Tax.AccountId = Shared.st_Store.SalesDeductTaxAccount.Value;
                jornal_Detail_deduct_Tax.CostCenter = costCenter;
                jornal_Detail_deduct_Tax.Credit = pr.DeductTaxValue;
                jornal_Detail_deduct_Tax.Debit = 0;
                jornal_Detail_deduct_Tax.Notes = note;
                jornal_Detail_deduct_Tax.CrncId = pr.CrncId;
                jornal_Detail_deduct_Tax.CrncRate = pr.CrncRate;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_deduct_Tax);
            }
            #endregion

            #region Discount
            /*قيد الخصم*/
            if (pr.DiscountValue > 0)
            {
                DAL.ACC_JournalDetail jornal_Detail_5 = new DAL.ACC_JournalDetail();
                jornal_Detail_5.JournalId = jornal.JournalId;//العميل                
                jornal_Detail_5.AccountId = CustomerAccountId;//حساب عميل  
                jornal_Detail_5.CostCenter = costCenter;
                jornal_Detail_5.Credit = 0;
                jornal_Detail_5.Debit = pr.DiscountValue;
                jornal_Detail_5.Notes = note + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Discount : ResSLAr.txt_Discount);
                jornal_Detail_5.CrncId = pr.CrncId;
                jornal_Detail_5.CrncRate = pr.CrncRate;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_5);

                /* الى حساب الخصم المكتسب*/
                DAL.ACC_JournalDetail jornal_Detail_6 = new DAL.ACC_JournalDetail();
                jornal_Detail_6.JournalId = jornal.JournalId;
                jornal_Detail_6.AccountId = Shared.ItemsPostingAvailable ? Shared.st_Store.SalesDiscountAcc.Value : stores_table.Where(x => x.StoreId == Convert.ToInt32(lkpStore.EditValue)).Select(x => x.PurchaseDiscountAcc.Value).First();//  28 حساب الخصم النقدي المكتسب
                jornal_Detail_6.CostCenter = costCenter;
                jornal_Detail_6.Credit = pr.DiscountValue;
                jornal_Detail_6.Debit = 0;
                jornal_Detail_6.Notes = note + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Discount : ResSLAr.txt_Discount);
                jornal_Detail_6.CrncId = pr.CrncId;
                jornal_Detail_6.CrncRate = pr.CrncRate;
                jornal_Detail_6.CostCenter = costCenter;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_6);
            }
            #endregion            

            #region CostOfSoldGoods
            if (Shared.ItemsPostingAvailable == false && Shared.StockIsPeriodic == false)
            {
                CostOfSoldGoods = CostOfSoldGoods / pr.CrncRate;            //Convert to Journal Currency
                DAL.ACC_JournalDetail jdCost1 = new DAL.ACC_JournalDetail();
                jdCost1.JournalId = jornal.JournalId;
                jdCost1.AccountId = stores_table.Where(x => x.StoreId == Convert.ToInt32(lkpStore.EditValue)).Select(x => x.PurchaseAccount).First();//حساب تكلفة البضاعة المباعة
                jdCost1.CostCenter = costCenter;
                jdCost1.Credit = 0;
                jdCost1.Debit = CostOfSoldGoods;
                jdCost1.Notes = note;// +"\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Discount : ResSLAr.txt_Discount);
                jdCost1.CrncId = pr.CrncId;
                jdCost1.CrncRate = pr.CrncRate;
                DB.ACC_JournalDetails.InsertOnSubmit(jdCost1);

                DAL.ACC_JournalDetail jdCost2 = new DAL.ACC_JournalDetail();
                jdCost2.JournalId = jornal.JournalId;
                jdCost2.AccountId = stores_table.Where(x => x.StoreId == Convert.ToInt32(lkpStore.EditValue)).Select(x => x.CostOfSoldGoodsAcc.Value).First();//المخزون
                jdCost2.CostCenter = costCenter;
                jdCost2.Credit = CostOfSoldGoods;
                jdCost2.Debit = 0;
                jdCost2.Notes = note;// +"\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Discount : ResSLAr.txt_Discount);
                jdCost2.CrncId = pr.CrncId;
                jdCost2.CrncRate = pr.CrncRate;
                jdCost2.CostCenter = costCenter;
                DB.ACC_JournalDetails.InsertOnSubmit(jdCost2);
            }
            #endregion

            #region Paid
            if (pr.Paid > 0 || (pr.PayAccountId2.HasValue && pr.PayAcc2_Paid.HasValue && pr.PayAcc2_Paid.Value > 0))
            {
                /*قيد السداد*/
                /* من حساب العميل*/
                DAL.ACC_JournalDetail jornal_Detail_6 = new DAL.ACC_JournalDetail();
                jornal_Detail_6.JournalId = jornal.JournalId;
                jornal_Detail_6.AccountId = CustomerAccountId;//حساب عميل  
                jornal_Detail_6.CostCenter = costCenter;
                jornal_Detail_6.Credit = 0;
                jornal_Detail_6.Debit = pr.Paid + (pr.PayAcc2_Paid.HasValue ? pr.PayAcc2_Paid.Value : 0);
                jornal_Detail_6.Notes = note + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Paid : ResSLAr.txt_Paid);
                jornal_Detail_6.CrncId = pr.CrncId;
                jornal_Detail_6.CrncRate = pr.CrncRate;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_6);

                /* الى حساب الخزينة*/
                if (pr.Paid > 0)
                {
                    DAL.ACC_JournalDetail jornal_Detail_4 = new DAL.ACC_JournalDetail();
                    jornal_Detail_4.JournalId = jornal.JournalId;
                    jornal_Detail_4.AccountId = Convert.ToInt32(lkp_Drawers.EditValue);          // حساب الخزينة
                    jornal_Detail_4.CostCenter = costCenter;
                    jornal_Detail_4.Credit = pr.Paid;
                    jornal_Detail_4.Debit = 0;
                    jornal_Detail_4.Notes = note + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Paid : ResSLAr.txt_Paid);
                    jornal_Detail_4.CrncId = pr.CrncId;
                    jornal_Detail_4.CrncRate = pr.CrncRate;
                    DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_4);
                }
                /* الى حساب الخزينة2*/
                if (pr.PayAccountId2 != null && pr.PayAcc2_Paid.HasValue && pr.PayAcc2_Paid.Value > 0)
                {
                    DAL.ACC_JournalDetail jornal_Detail_5 = new DAL.ACC_JournalDetail();
                    jornal_Detail_5.JournalId = jornal.JournalId;
                    jornal_Detail_5.CostCenter = costCenter;
                    jornal_Detail_5.AccountId = pr.PayAccountId2.Value;          // حساب الخزينة
                    jornal_Detail_5.Credit = pr.PayAcc2_Paid.Value;
                    jornal_Detail_5.Debit = 0;
                    jornal_Detail_5.Notes = note + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Paid : ResSLAr.txt_Paid);
                    jornal_Detail_5.CrncId = pr.CrncId;
                    jornal_Detail_5.CrncRate = pr.CrncRate;
                    DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_5);
                }
            }
            #endregion

            DB.SubmitChanges();
        }


        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.SL_Customer).Count() < 1)
                {
                    btnAddCustomer.Enabled = false;
                }
                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.Item).Count() < 1)
                    mi_frm_IC_Item.Enabled = false;

                prvlg = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.SL_Return).FirstOrDefault();

                if (!prvlg.CanDel)
                    barBtnDelete.Enabled = false;
                if (!prvlg.CanAdd)
                    barBtnNew.Enabled = false;
                if (!prvlg.CanPrint)
                    barBtnPrint.Enabled = false;

                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.SL_Invoice).Count() < 1)
                    barbtnLoadSellInvoice.Enabled = false;
            }
        }

        void DoValidate()
        {
            txt_paid.DoValidate();
            txt_Remains.DoValidate();
            txtDiscountRatio.DoValidate();
            txtDiscountValue.DoValidate();
            txtExpenses.DoValidate();
            txtInvoiceCode.DoValidate();

            txtNotes.DoValidate();
            lkp_Drawers.DoValidate();
            lkpStore.DoValidate();
            lkp_Customers.DoValidate();

            dtInvoiceDate.DoValidate();
            dtSLReturn_Details.AcceptChanges();
        }

        DialogResult ChangesMade()
        {
            if (
                DataModified ||
                dtSLReturn_Details.GetChanges(DataRowState.Added) != null ||
                dtSLReturn_Details.GetChanges(DataRowState.Modified) != null ||
                dtSLReturn_Details.GetChanges(DataRowState.Deleted) != null
                )
            {
                DialogResult r = XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgDataModified : ResSLAr.MsgDataModified, "", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                if (r == DialogResult.Yes)
                {
                    if (!ValidData())
                        return DialogResult.Cancel;

                    barBtnSave.PerformClick();
                    return r;
                }
                else if (r == DialogResult.No)
                {
                    return r;
                }
                else if (r == DialogResult.Cancel)
                {
                    //cancel form action
                    return r;
                }
            }
            return DialogResult.No;
        }

        private bool ValidData()
        {
            DB = new ERPDataContext();

            //can't post in closed period
            if (ErpHelper.CanSaveInClsedPeriod(dtInvoiceDate.DateTime.Date, Shared.st_Store.ClosePeriodDate, Shared.user.EditInClosedPeriod) == false)
                return false;

            ((GridView)grdPrInvoice.FocusedView).FocusedRowHandle += 1;
            if (invoiceId == 0)
            {
                if (prvlg != null && !prvlg.CanAdd)
                {
                    // "عفوا, انت لا تمتلك صلاحية انشاء بيان جديد"
                    XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgPrvNew : ResSLAr.MsgPrvNew, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            if (invoiceId > 0)
            {
                if (prvlg != null && !prvlg.CanEdit)
                {
                    //"عفوا, انت لا تمتلك صلاحية تعديل هذا البيان"
                    XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgPrvEdit : ResSLAr.MsgPrvEdit, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
                if (ErpHelper.CanEditPostedBill(invoiceId, (int)Process.SellReturn,
                    Shared.OfflinePostToGL, Shared.user.UserEditPostedBills) == false)
                    return false;
            }

            if (lkp_Customers.EditValue == null)
            {
                XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.MsgSelectCustomer : ResAr.MsgSelectCustomer,
                        Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                lkp_Customers.Focus();

                return false;
            }

            if (Convert.ToDecimal(txt_TaxValue.EditValue) > 0 && Shared.st_Store.TaxAcc.HasValue == false)
            {
                //يجب تحديد حساب الضرائب
                //check sales tax Account

                XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.MsgSalesTaxAcc : ResAr.MsgSalesTaxAcc,
                        Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);

                return false;
            }

            if (Shared.StockIsPeriodic && Convert.ToDecimal(txtDiscountValue.EditValue) > 0 && Shared.st_Store.PurchaseDiscountAcc.HasValue == false)
            {
                //check sales discount Account

                XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.MsgDiscountAcc : ResAr.MsgDiscountAcc,
                        Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);

                return false;
            }

            if (string.IsNullOrEmpty(txtInvoiceCode.Text.Trim()))
            {
                XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.txtValidateInvNumber : ResSLAr.txtValidateInvNumber, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                txtInvoiceCode.Focus();
                return false;
            }

            if (Shared.st_Store.InvoicesCodeRedundancy == false)
            {
                bool code_exist = InvCodeExist();
                if (invoiceId == 0 && code_exist && Shared.st_Store.GenerateNewInvCodeOnSave == true)
                {
                    lkpStore_EditValueChanged(lkpStore, EventArgs.Empty);
                    return ValidData();
                }
                if (ErpHelper.ValidateInvCodeExist(code_exist, txtInvoiceCode) == false)
                    return false;
            }

            if (Shared.SalesManAvailable && Shared.st_Store.SalesEmpMandatory && lkp_SalesEmp.EditValue == null)
            {
                XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.SalesEmpMandatory : ResSLAr.SalesEmpMandatory,
                    Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                lkp_SalesEmp.Focus();
                return false;
            }

            if (dtSLReturn_Details.Rows.Count <= 0)
            {
                //يجب تسجيل صنف علي الاقل في الفاتوره
                XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.txtValidateNoRows : ResSLAr.txtValidateNoRows, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                grdPrInvoice.Focus();
                return false;
            }

            if (dtSLReturn_Details.Rows[dtSLReturn_Details.Rows.Count - 1].RowState != DataRowState.Deleted
                && dtSLReturn_Details.Rows[dtSLReturn_Details.Rows.Count - 1]["ItemId"] == DBNull.Value)
            {
                dtSLReturn_Details.Rows[dtSLReturn_Details.Rows.Count - 1].Delete();
                grdPrInvoice.RefreshDataSource();
            }


            return true;
        }

        private void lkpStore_EditValueChanged(object sender, EventArgs e)
        {
            if ((lkp_InvoiceBook.EditValue == null || Convert.ToInt32(lkp_InvoiceBook.EditValue) == 0) && invoiceId < 1)
            {
                #region GetNextInvNumber
                var lastNumber = (from x in DB.SL_Returns
                                  join s in DB.IC_Stores on x.StoreId equals s.StoreId
                                  where Shared.st_Store.AutoInvSerialForStore == true ? x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true    //مستوى المخزن
                                  where Shared.st_Store.AutoInvSerialForStore == null ? (int?)lkpStore.GetColumnValue("ParentId") != null ?
                                  s.ParentId.HasValue && s.ParentId.Value == (int?)lkpStore.GetColumnValue("ParentId") : x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true//مستوى الفرع
                                  where x.InvoiceBookId == null
                                  orderby x.ReturnDate descending
                                  orderby x.SL_ReturnId descending
                                  select x.ReturnCode).FirstOrDefault();
                txtInvoiceCode.Text = MyHelper.GetNextNumberInString(lastNumber);
                #endregion
            }
            else if (lkp_InvoiceBook.EditValue != null && Convert.ToInt32(lkp_InvoiceBook.EditValue) != 0 && invoiceId < 1)
            {
                #region GetNextInvNumber
                var lastNumber = (from x in DB.SL_ReturnArchives
                                  where x.InvoiceBookId == Convert.ToInt32(lkp_InvoiceBook.EditValue)
                                  orderby x.ReturnDate descending
                                  orderby x.SL_ReturnId descending
                                  select x.ReturnCode).FirstOrDefault();
                txtInvoiceCode.Text = MyHelper.GetNextNumberInString(lastNumber);
                #endregion
            }

            IsTaxable = (bool?)lkp_InvoiceBook.GetColumnValue("IsTaxable");

            if (lkpStore.EditValue != null)
                lkpCostCenter.EditValue = stores_table.Where(x => x.StoreId == Convert.ToInt32(lkpStore.EditValue)).Select(x => x.CostCenter).First();
        }
        private void contextMenuStrip1_Opened(object sender, EventArgs e)
        {
            var view = grdPrInvoice.FocusedView as GridView;
            var item_id = view.GetFocusedRowCellValue("ItemId");
            if (item_id == null || item_id == DBNull.Value || Convert.ToInt32(item_id) <= 0)
                mi_frm_IC_Item.Enabled = false;
            else
            {
                if (Shared.LstUserPrvlg == null || Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.Item).Count() > 0)
                    mi_frm_IC_Item.Enabled = true;
            }
        }

        private void mi_frm_IC_Item_Click(object sender, EventArgs e)
        {
            var view = grdPrInvoice.FocusedView as GridView;
            var item_id = view.GetFocusedRowCellValue("ItemId");
            if (item_id == null || item_id == DBNull.Value || Convert.ToInt32(item_id) <= 0)
                return;


            new frm_IC_Item(Convert.ToInt32(item_id), FormAction.Edit).ShowDialog();
        }

        private void frm_SL_Return_Shown(object sender, EventArgs e)
        {
            txtNotes.Focus();
            FocusItemCode1(Shared.user.FocusGridInInvoices);
        }

        private void barBtnHelp_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "فاتورة مردود مبيعات جديدة");
        }

        bool cust_IsDebit = false;
        private void lkp_Customers_EditValueChanged(object sender, EventArgs e)
        {
            cust_IsDebit = false;
            int? accountId = null;
            double maxcredit = 0;
            double balance = 0;
            txt_MaxCredit.Text = string.Empty;

            var selected_customer = lst_Customers.Where(x => x.CustomerId == Convert.ToInt32(lkp_Customers.EditValue)).FirstOrDefault();

            if (selected_customer != null)
            {
                accountId = selected_customer.AccountId;
                if (selected_customer.PriceLevelId.HasValue)
                    CustomerPriceLevel = DB.IC_PriceLevels.Where(x => x.PriceLevelId == selected_customer.PriceLevelId).FirstOrDefault();
                else
                    CustomerPriceLevel = null;

                #region Balance_Before_and_After
                DateTime? end_date = (dtInvoiceDate.DateTime == DateTime.MinValue) ? (DateTime?)null : dtInvoiceDate.DateTime;
                if (accountId != null)
                {
                    balance = decimal.ToDouble(HelperAcc.Get_account_balance(accountId.Value, Shared.minDate, end_date));
                    if (invoiceId == 0)
                    {
                        txt_Balance_Before.Text = Math.Abs(balance).ToString("0,0.00");
                        if (balance > 0)
                            lbl_IsCredit_Before.Text = Shared.IsEnglish ? ResSLEn.txtCredit : ResSLAr.txtCredit;
                        else if (balance < 0)
                            lbl_IsCredit_Before.Text = Shared.IsEnglish ? ResSLEn.txtDebit : ResSLAr.txtDebit;
                        else
                            lbl_IsCredit_Before.Text = "";

                        double balance_after = balance + Convert.ToDouble(txt_Remains.EditValue);
                        txt_Balance_After.Text = Math.Abs(balance_after).ToString("0,0.00");
                        if (balance_after > 0)
                            lbl_IsCredit_After.Text = Shared.IsEnglish ? ResSLEn.txtCredit : ResSLAr.txtCredit;
                        else if (balance_after < 0)
                            lbl_IsCredit_After.Text = Shared.IsEnglish ? ResSLEn.txtDebit : ResSLAr.txtDebit;
                        else
                            lbl_IsCredit_After.Text = "";
                    }
                    else
                    {
                        txt_Balance_Before.Text = Math.Abs(balance - invoice_remains).ToString("0,0.00");
                        if ((balance - invoice_remains) > 0)
                            lbl_IsCredit_Before.Text = Shared.IsEnglish ? ResSLEn.txtCredit : ResSLAr.txtCredit;
                        else if ((balance - invoice_remains) < 0)
                            lbl_IsCredit_Before.Text = Shared.IsEnglish ? ResSLEn.txtDebit : ResSLAr.txtDebit;
                        else
                            lbl_IsCredit_Before.Text = "";

                        double balance_after = (balance - invoice_remains) + Convert.ToDouble(txt_Remains.EditValue);
                        txt_Balance_After.Text = Math.Abs(balance_after).ToString("0,0.00");
                        if (balance_after > 0)
                            lbl_IsCredit_After.Text = Shared.IsEnglish ? ResSLEn.txtCredit : ResSLAr.txtCredit;
                        else if (balance_after < 0)
                            lbl_IsCredit_After.Text = Shared.IsEnglish ? ResSLEn.txtDebit : ResSLAr.txtDebit;
                        else
                            lbl_IsCredit_After.Text = "";
                    }
                }
                else
                {
                    txt_Balance_Before.Text = "0";
                    txt_Balance_After.Text = "0";
                }
                #endregion

                maxcredit = selected_customer.MaxCredit;
                txt_MaxCredit.Text = maxcredit.ToString("0,0.00");

                if (balance <= 0)
                    cust_IsDebit = true;
                else
                    cust_IsDebit = false;

                if (invoiceId == 0)
                {
                    if (selected_customer.SalesEmpId.HasValue)
                        lkp_SalesEmp.EditValue = selected_customer.SalesEmpId;
                    else
                        lkp_SalesEmp.EditValue = null;
                }
            }
        }

        private void gridView2_ShowingEditor(object sender, CancelEventArgs e)
        {
            try
            {
                #region Expire
                if (gridView2.FocusedColumn == col_Expire
                && gridView2.GetFocusedRowCellValue("IsExpire") != null
                && gridView2.GetFocusedRowCellValue("IsExpire") != DBNull.Value)
                {
                    bool IsExpire = Convert.ToBoolean(gridView2.GetFocusedRowCellValue("IsExpire"));
                    e.Cancel = !IsExpire;
                }
                #endregion
            }
            catch { }
        }

        private void rep_expireDate_CustomDisplayText(object sender, DevExpress.XtraEditors.Controls.CustomDisplayTextEventArgs e)
        {
            #region Expire
            if (e.Value == null || e.Value == DBNull.Value)
                return;
            try
            {
                DateTime date = Convert.ToDateTime(e.Value);
                e.DisplayText = date.Month + "-" + date.Year;
            }
            catch
            { }

            #endregion
        }

        private void Load_SellInvoiceData(int sellInvoiceId)
        {

            dtSLReturn_Details.Rows.Clear();

            var details = (from d in DB.SL_InvoiceDetails
                           where d.SL_InvoiceId == sellInvoiceId
                           join i in DB.IC_Items on d.ItemId equals i.ItemId
                           select new { detail = d, item = i }).ToList();

            lkp_Customers.EditValue = details.FirstOrDefault().detail.SL_Invoice.CustomerId;
            lkpStore.EditValue = details.FirstOrDefault().detail.SL_Invoice.StoreId;
            lkp_SalesEmp.EditValue = details.FirstOrDefault().detail.SL_Invoice.SalesEmpId;

            foreach (var d in details)
            {
                DataRow row = dtSLReturn_Details.NewRow();
                row["ItemId"] = d.detail.ItemId;
                row["CategoryId"] = d.item.Category;
                row["ItemCode1"] = d.item.ItemCode1;
                row["ItemCode2"] = d.item.ItemCode2;
                row["UOM"] = d.detail.UOMId;
                row["Qty"] = decimal.ToDouble(d.detail.Qty);

                if (d.detail.Height != null)
                    row["Height"] = decimal.ToDouble(d.detail.Height.Value);
                if (d.detail.Length != null)
                    row["Length"] = decimal.ToDouble(d.detail.Length.Value);
                if (d.detail.Width != null)
                    row["Width"] = decimal.ToDouble(d.detail.Width.Value);
                row["PiecesCount"] = decimal.ToDouble(d.detail.PiecesCount);

                if (d.detail.UOMIndex == 0)
                    row["PurchasePrice"] = decimal.ToDouble(d.item.PurchasePrice);
                else if (d.detail.UOMIndex == 1)
                    row["PurchasePrice"] = decimal.ToDouble(d.item.PurchasePrice / MyHelper.FractionToDouble(d.item.MediumUOMFactor));
                else if (d.detail.UOMIndex == 2)
                    row["PurchasePrice"] = decimal.ToDouble(d.item.PurchasePrice / MyHelper.FractionToDouble(d.item.LargeUOMFactor));

                row["SellPrice"] = decimal.ToDouble(d.detail.SellPrice);
                row["SalesTaxRatio"] = d.detail.SalesTaxRatio;
                row["SalesTax"] = decimal.ToDouble(d.detail.SalesTax);
                row["calcTaxBeforeDisc"] = d.item.calcTaxBeforeDisc;


                row["DiscountValue"] = decimal.ToDouble(d.detail.DiscountValue);
                row["DiscountRatio"] = decimal.ToDouble(d.detail.DiscountRatio);
                row["DiscountRatio2"] = decimal.ToDouble(d.detail.DiscountRatio2);
                row["DiscountRatio3"] = decimal.ToDouble(d.detail.DiscountRatio3);

                row["CompanyNameAr"] = d.item.IC_Company.CompanyNameAr;
                row["MediumUOMFactor"] = MyHelper.FractionToDouble(d.item.MediumUOMFactor);
                row["LargeUOMFactor"] = MyHelper.FractionToDouble(d.item.LargeUOMFactor);

                #region Expire
                if (d.detail.Expire.HasValue)
                    row["Expire"] = d.detail.Expire;
                row["Batch"] = d.detail.Batch;
                row["Serial"] = d.detail.Serial;

                row["IsExpire"] = d.item.IsExpire;
                #endregion

                //get store qty                                
                //decimal currentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, d.detail.ItemId, d.detail.SL_Invoice.StoreId);
                //currentQty = MyHelper.getCalculatedUomQty(currentQty, d.detail.UOMIndex, MyHelper.FractionToDouble(d.item.MediumUOMFactor), MyHelper.FractionToDouble(d.item.LargeUOMFactor));
                //row["CurrentQty"] = decimal.ToDouble(currentQty);

                row["TotalSellPrice"] = decimal.ToDouble(d.detail.TotalSellPrice);
                row["UomIndex"] = d.detail.UOMIndex;
                row["ItemType"] = d.item.ItemType;

                row["ItemDescription"] = d.detail.ItemDescription;
                row["ItemDescriptionEn"] = d.detail.ItemDescriptionEn;

                row["ParentItemId"] = d.item.mtrxParentItem;
                row["M1"] = d.item.mtrxAttribute1;
                row["M2"] = d.item.mtrxAttribute2;
                row["M3"] = d.item.mtrxAttribute3;

                dtSLReturn_Details.Rows.Add(row);
            }
            txtDiscountRatio.EditValue = Convert.ToDouble(details.FirstOrDefault().detail.SL_Invoice.DiscountRatio * 100);
            txtDiscountValue.EditValue = Convert.ToDouble(details.FirstOrDefault().detail.SL_Invoice.DiscountValue);

            txt_TaxValue.EditValue = Convert.ToDouble(details.FirstOrDefault().detail.SL_Invoice.TaxValue);
            Get_TotalAccount();
        }

        private void barbtnLoadSellInvoice_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            new frm_SL_InvoiceList(true).ShowDialog();
            int SelectedInvId = frm_SL_InvoiceList.SelectedInvId;
            if (SelectedInvId > 0)
            {
                Reset();
                Load_SellInvoiceData(SelectedInvId);
                frm_SL_InvoiceList.SelectedInvId = 0;
            }
        }

        private void lkp_Drawers2_EditValueChanged(object sender, EventArgs e)
        {
            if (lkp_Drawers2.EditValue != null && Convert.ToInt32(lkp_Drawers2.EditValue) == Convert.ToInt32(lkp_Drawers.EditValue))
                lkp_Drawers2.EditValue = null;

            if (lkp_Drawers2.EditValue == null)
            {
                txt_PayAcc2_Paid.EditValue = 0;
                txt_PayAcc2_Paid.Enabled = false;
            }
            else
            {
                txt_PayAcc2_Paid.Enabled = true;
            }
        }

        private void btn_AddMatrixItems_Click(object sender, EventArgs e)
        {

            new frm_IC_MatrixAddInv().ShowDialog();

            foreach (var d in frm_IC_MatrixAddInv.lst_InvMatrixItems)
            {
                var item = DB.IC_Items.Where(x => x.ItemId == d.ItemId).FirstOrDefault();
                DataRow row = dtSLReturn_Details.NewRow();
                row["ItemId"] = item.ItemId;
                row["ItemCode1"] = item.ItemCode1;
                row["ItemCode2"] = item.ItemCode2;
                row["ItemType"] = item.ItemType;
                row["PurchasePrice"] = Decimal.ToDouble(item.PurchasePrice);
                row["SellPrice"] = Decimal.ToDouble(item.SmallUOMPrice);
                row["DiscountRatio"] = "0";
                row["DiscountValue"] = "0";

                if (IsTaxable == null && Convert.ToBoolean(ErpUtils.GetGridLookUpValue(lkp_Customers, lkp_Customers.EditValue, "IsTaxable")))
                    row["SalesTaxRatio"] = item.SalesTaxRatio / 100;
                else if (IsTaxable == true)
                    row["SalesTaxRatio"] = item.SalesTaxRatio / 100;
                else
                    row["SalesTaxRatio"] = 0;

                row["calcTaxBeforeDisc"] = item.calcTaxBeforeDisc;

                row["Qty"] = decimal.ToDouble(d.Qty);

                decimal salestaxratio = Convert.ToDecimal(row["SalesTaxRatio"]);
                decimal DiscR1 = Convert.ToDecimal(row["DiscountRatio"]);
                decimal DiscR2 = Convert.ToDecimal(row["DiscountRatio2"]);
                decimal DiscR3 = Convert.ToDecimal(row["DiscountRatio3"]);
                decimal TotalSellPrice = d.Qty * Convert.ToDecimal(row["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);

                if (Shared.st_Store.PriceIncludeSalesTax)    /*السعر شامل الضريبة*/
                {
                    decimal temp = item.calcTaxBeforeDisc ? (salestaxratio * d.Qty * Convert.ToDecimal(row["SellPrice"])) / (1 + salestaxratio) :
                        (salestaxratio * TotalSellPrice) / (1 + salestaxratio);
                    row["SalesTax"] = decimal.ToDouble(temp);
                    row["TotalSellPrice"] = decimal.ToDouble(TotalSellPrice - temp);// السعر الاجمالي شامل الضريبة                            
                }
                else
                {
                    decimal temp = item.calcTaxBeforeDisc ? (salestaxratio * d.Qty * Convert.ToDecimal(row["SellPrice"])) : (salestaxratio * TotalSellPrice);
                    row["SalesTax"] = decimal.ToDouble(temp);
                    row["TotalSellPrice"] = decimal.ToDouble(TotalSellPrice);//ضيف الضريبة على السعر الاجمالي                            
                }

                row["MediumUOMFactor"] = MyHelper.FractionToDouble(item.MediumUOMFactor);
                row["LargeUOMFactor"] = MyHelper.FractionToDouble(item.LargeUOMFactor);

                MyHelper.GetUOMs(item, dtUOM, uom_list);
                row["UOM"] = dtUOM.Rows[0]["UomId"];
                row["UomIndex"] = "0";
                row["IsExpire"] = item.IsExpire;
                var compName = DB.IC_Companies.Where(c => c.CompanyId == item.Company).Select(c => c.CompanyNameAr).Single();
                row["CompanyNameAr"] = compName;

                //if (Shared.user.Sell_ShowCrntQty == true)
                //{
                //    decimal currentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, d.ItemId, Convert.ToInt32(lkpStore.EditValue));
                //    row["CurrentQty"] = decimal.ToDouble(currentQty);
                //}
                //else
                //{
                //    row["CurrentQty"] = decimal.ToDouble(0);
                //}

                row["ItemDescription"] = item.Description;
                row["ItemDescriptionEn"] = item.DescriptionEn;

                row["ParentItemId"] = item.mtrxParentItem;
                row["M1"] = item.mtrxAttribute1;
                row["M2"] = item.mtrxAttribute2;
                row["M3"] = item.mtrxAttribute3;

                row["Length"] = Decimal.ToDouble(item.Length);
                row["Width"] = Decimal.ToDouble(item.Width);
                row["Height"] = Decimal.ToDouble(item.Height);

                dtSLReturn_Details.Rows.Add(row);
            }
        }

        private void gridView2_CustomUnboundColumnData(object sender, CustomColumnDataEventArgs e)
        {
            var view = ((sender) as GridView);
            decimal totalqty = 0;
            try
            {
                if (e.Column == col_TotalQty
    && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "ItemType") != null && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "ItemType") != DBNull.Value
    && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Qty") != null && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Qty") != DBNull.Value
    && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Length") != null && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Length") != DBNull.Value
    && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Width") != null && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Width") != DBNull.Value
    && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Height") != null && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Height") != DBNull.Value)
                {
                    if (Convert.ToInt32(view.GetListSourceRowCellValue(e.ListSourceRowIndex, "ItemType")) != (int)ItemType.Subtotal)
                    {
                        totalqty = Convert.ToDecimal(view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Qty"))
                            * Convert.ToDecimal(view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Length"))
                            * Convert.ToDecimal(view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Width"))
                            * Convert.ToDecimal(view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Height"));
                    }
                    else
                    {
                        int i = e.ListSourceRowIndex < 0 ? view.RowCount - 1 : e.ListSourceRowIndex - 1;
                        while (Convert.ToInt32(view.GetListSourceRowCellValue(i, "ItemType")) != (int)ItemType.Subtotal && i >= 0)
                        {
                            if (Shared.st_Store.MultiplyDimensions != (byte)Dimensions.Distinguish)
                                totalqty += Convert.ToDecimal(view.GetListSourceRowCellValue(i, "Length")) *
                                Convert.ToDecimal(view.GetListSourceRowCellValue(i, "Height")) *
                                Convert.ToDecimal(view.GetListSourceRowCellValue(i, "Width")) *
                                Convert.ToDecimal(view.GetListSourceRowCellValue(i, "Qty"));
                            i--;
                        }
                    }
                    e.Value = decimal.ToDouble(totalqty);
                }
            }
            catch { }
        }

        private void Get_SubTotal_RowData(DataRow row, GridView view, int CurrentRowHandle)
        {
            row["Qty"] = 0;
            row["Length"] = 0;
            row["Width"] = 0;
            row["Height"] = 0;
            row["DiscountRatio"] = 0;
            row["UOM"] = 0;
            row["UomIndex"] = 0;
            //row["CurrentQty"] = 0;
            row["ItemDescription"] = "";
            row["ItemDescriptionEn"] = "";

            int i = CurrentRowHandle < 0 ? view.RowCount - 1 : CurrentRowHandle - 1;
            decimal TotalSellPrice = 0, TotalDiscountValue = 0, TotalSalesTax = 0,
                TotalPiecesCount = 0, TotalQty = 0, SellPrice = 0;

            while (Convert.ToInt32(view.GetRowCellValue(i, "ItemType")) != (int)ItemType.Subtotal && i >= 0)
            {
                TotalQty += Convert.ToDecimal(view.GetRowCellValue(i, "Qty"));
                TotalSellPrice += Convert.ToDecimal(view.GetRowCellValue(i, "TotalSellPrice"));
                TotalDiscountValue += Convert.ToDecimal(view.GetRowCellValue(i, "DiscountValue"));

                decimal totalrowqty = Convert.ToDecimal(view.GetRowCellValue(i, "Qty"));
                if (Shared.st_Store.MultiplyDimensions != (byte)Dimensions.Distinguish)
                    totalrowqty = Convert.ToDecimal(view.GetRowCellValue(i, "Length")) *
                    Convert.ToDecimal(view.GetRowCellValue(i, "Height")) *
                    Convert.ToDecimal(view.GetRowCellValue(i, "Width")) *
                    Convert.ToDecimal(view.GetRowCellValue(i, "Qty"));

                TotalSalesTax += (Convert.ToDecimal(view.GetRowCellValue(i, "SalesTax")));
                SellPrice += (Convert.ToDecimal(view.GetRowCellValue(i, "SellPrice")) * totalrowqty);
                TotalPiecesCount += Convert.ToDecimal(view.GetRowCellValue(i, "PiecesCount"));
                i--;
            }

            row["Qty"] = decimal.ToDouble(TotalQty);
            row["TotalSellPrice"] = decimal.ToDouble(TotalSellPrice);
            row["DiscountValue"] = decimal.ToDouble(TotalDiscountValue);
            row["SalesTax"] = decimal.ToDouble(TotalSalesTax);
            row["SellPrice"] = decimal.ToDouble(SellPrice);
            row["PiecesCount"] = decimal.ToDouble(TotalPiecesCount);

        }
        private void Update_First_SubTotal(GridView view, int CurrentRowHandle)
        {
            if (CurrentRowHandle >= 0)
            {
                for (int i = CurrentRowHandle; i < view.RowCount; i++)
                {
                    if (Convert.ToInt32(view.GetRowCellValue(i, "ItemType")) == (int)ItemType.Subtotal)
                    {
                        Get_SubTotal_RowData(view.GetDataRow(i), view, i);
                        return;
                    }
                }
            }
            else if (Convert.ToInt32(view.GetFocusedRowCellValue("ItemType")) == (int)ItemType.Subtotal)
                Get_SubTotal_RowData(view.GetFocusedDataRow(), view, CurrentRowHandle);
        }

        private void gridView2_RowStyle(object sender, RowStyleEventArgs e)
        {
            if (e.RowHandle >= 0 && Convert.ToInt32(gridView2.GetRowCellValue(e.RowHandle, "ItemType")) == (int)ItemType.Subtotal)
            {
                e.HighPriority = true;
                e.Appearance.BackColor = Shared.user.SubtotalBackcolor == null ? Color.Yellow : Color.FromName(Shared.user.SubtotalBackcolor);
            }
        }

        private void txtDiscountValue_Leave(object sender, EventArgs e)
        {
            Get_TotalAccount();
        }


        private void txtDiscountRatio_EditValueChanged(object sender, EventArgs e)
        {
            Get_TotalAccount();
        }

        private void Get_TotalAccount()
        {
            gridView1.RefreshData();
            try
            {
                decimal total_sell = 0;
                decimal total_salestax = 0;
                decimal net = 0;
                decimal discount_ratio = 0;
                decimal Discount_value = 0;

                foreach (DataRow dr in dtSLReturn_Details.Rows)
                {
                    if (dr.RowState == DataRowState.Deleted)
                        continue;
                    if (Convert.ToInt32(dr["ItemType"]) != (int)ItemType.Subtotal)
                    {
                        total_sell += Convert.ToDecimal(dr["TotalSellPrice"]);
                        total_salestax += (Convert.ToDecimal(dr["SalesTax"]));
                    }
                }

                #region Discount
                discount_ratio = Convert.ToDecimal(txtDiscountRatio.EditValue) / 100;
                Discount_value = Convert.ToDecimal(txtDiscountValue.EditValue);

                if (discount_ratio > 0)
                {
                    if (Utilities.ValuesNotEqual(total_sell * discount_ratio, Discount_value))
                        Discount_value = total_sell * discount_ratio;
                }
                //else
                //    Discount_value = 0;
                #endregion

                #region Deduct_Tax
                decimal deductTax_ratio = Convert.ToDecimal(txt_DeductTaxR.EditValue) / 100;
                decimal deductTax_value = Convert.ToDecimal(txt_DeductTaxV.EditValue);

                if (deductTax_ratio > 0)
                {
                    if (Utilities.ValuesNotEqual(total_sell * deductTax_ratio, deductTax_value))
                        deductTax_value = total_sell * deductTax_ratio;
                }
                //else
                //    deductTax_value = 0;
                #endregion

                #region Add_Tax
                decimal addTax_ratio = Convert.ToDecimal(txt_AddTaxR.EditValue) / 100;
                decimal addTax_value = Convert.ToDecimal(txt_AddTaxV.EditValue);

                if (addTax_ratio > 0)
                {
                    if (Utilities.ValuesNotEqual(total_sell * addTax_ratio, addTax_value))
                        addTax_value = total_sell * addTax_ratio;
                }
                //else
                //    addTax_value = 0;
                #endregion

                total_sell = decimal.Round(total_sell, 4);
                total_salestax = decimal.Round(total_salestax, 4);
                deductTax_value = decimal.Round(deductTax_value, 4);
                addTax_value = decimal.Round(addTax_value, 4);
                Discount_value = decimal.Round(Discount_value, 4);

                net = total_sell + total_salestax + addTax_value - deductTax_value - Discount_value - Convert.ToDecimal(txtExpenses.EditValue);

                txtNet.EditValue = decimal.ToDouble(net);
                txtDiscountValue.EditValue = decimal.ToDouble(Discount_value);
                txt_DeductTaxV.EditValue = decimal.ToDouble(deductTax_value);
                txt_AddTaxV.EditValue = decimal.ToDouble(addTax_value);
                txt_TaxValue.EditValue = decimal.ToDouble(total_salestax);

                txt_Total.EditValue = decimal.ToDouble(total_sell);

                if (Shared.user.SL_Return_PayMethod == false)          //اجل
                {
                    if (Convert.ToDecimal(txt_paid.EditValue) == 0)
                        txt_Remains.EditValue = decimal.ToDouble(net);
                    txt_Remains.EditValue = decimal.ToDouble(net - Convert.ToDecimal(txt_paid.EditValue));
                }
                else
                {
                    if (Convert.ToDecimal(txt_Remains.EditValue) == 0 && Convert.ToDecimal(txt_PayAcc2_Paid.EditValue) == 0)
                        txt_PayAcc1_Paid.EditValue = decimal.ToDouble(net);
                    txt_Remains.EditValue = decimal.ToDouble(net - Convert.ToDecimal(txt_paid.EditValue));
                }
            }
            catch { }
        }

        private void txt_paid_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                if (Shared.user.SL_Return_PayMethod == true)
                {
                    if (Convert.ToDecimal(txt_Remains.EditValue) <= 0)
                    {
                        cmbPayMethod.EditValue = true;
                    }
                    else if (Convert.ToDecimal(txt_paid.EditValue) == 0)
                    {
                        cmbPayMethod.EditValue = false;
                    }
                    else
                        cmbPayMethod.EditValue = null;
                }

                if ((sender as TextEdit).Name == "txt_paid")
                    txt_Remains.EditValue = decimal.ToDouble(Convert.ToDecimal(txtNet.EditValue) - Convert.ToDecimal(txt_paid.EditValue));
                else if ((sender as TextEdit).Name == "txt_Remains")
                {
                    txt_paid.EditValue = decimal.ToDouble(Convert.ToDecimal(txtNet.EditValue) - Convert.ToDecimal(txt_Remains.EditValue));

                    double balance_before = Convert.ToDouble(txt_Balance_Before.Text);
                    if (cust_IsDebit == true)
                        balance_before = balance_before * -1;

                    double balance_after = balance_before + Convert.ToDouble(txt_Remains.EditValue);
                    txt_Balance_After.Text = Math.Abs(balance_after).ToString("0,0.00");
                    if (balance_after > 0)
                        lbl_IsCredit_After.Text = Shared.IsEnglish ? ResSLEn.txtCredit : ResSLAr.txtCredit;
                    else if (balance_after < 0)
                        lbl_IsCredit_After.Text = Shared.IsEnglish ? ResSLEn.txtDebit : ResSLAr.txtDebit;
                    else
                        lbl_IsCredit_After.Text = "";
                }
            }
            catch
            { }

        }

        private void txt_PayAcc1_Paid_EditValueChanged(object sender, EventArgs e)
        {
            if (Convert.ToDecimal(txt_PayAcc1_Paid.EditValue) < 0)
                txt_PayAcc1_Paid.EditValue = 0;
            if (Convert.ToDecimal(txt_PayAcc2_Paid.EditValue) < 0)
                txt_PayAcc2_Paid.EditValue = 0;

            txt_paid.EditValue = Decimal.ToDouble(Convert.ToDecimal(txt_PayAcc1_Paid.EditValue) + Convert.ToDecimal(txt_PayAcc2_Paid.EditValue));
        }

        void dt_TableNewRow(object sender, DataTableNewRowEventArgs e)
        {
            Get_TotalAccount();
        }
        void dt_RowChanged(object sender, DataRowChangeEventArgs e)
        {
            Get_TotalAccount();
        }

        private void mi_InvoiceStaticDisc_Click(object sender, EventArgs e)
        {
            if (ErpUtils.IsFormOpen(typeof(frm_InvoiceDiscs)))
                Application.OpenForms["frm_InvoiceDiscs"].Close();
            else
                new frm_InvoiceDiscs(Process.SellReturn).Show();
        }

        private void mi_InvoiceStaticDimensions_Click(object sender, EventArgs e)
        {
            if (ErpUtils.IsFormOpen(typeof(frm_InvoiceDimenstions)))
                Application.OpenForms["frm_InvoiceDimenstions"].Close();
            else
                new frm_InvoiceDimenstions(Process.SellReturn).Show();
        }

        void txtInvoiceCode_Leave(object sender, EventArgs e)
        {
            if (Shared.st_Store.InvoicesCodeRedundancy == null)
                return;

            bool code_exist = InvCodeExist();
            ErpHelper.ValidateInvCodeExist(code_exist, txtInvoiceCode);
        }

        public bool InvCodeExist()
        {
            int? InvbookId = (int?)lkp_InvoiceBook.EditValue;
            return (from x in DB.SL_ReturnArchives
                    join s in DB.IC_Stores on x.StoreId equals s.StoreId
                    where InvbookId == null && Shared.st_Store.AutoInvSerialForStore == true ? x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true    //مستوى المخزن
                    where InvbookId == null && Shared.st_Store.AutoInvSerialForStore == null ? (int?)lkpStore.GetColumnValue("ParentId") != null ?
                       s.ParentId.HasValue && s.ParentId.Value == (int?)lkpStore.GetColumnValue("ParentId") : x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true//مستوى الفرع
                    where x.InvoiceBookId == null ? true : x.InvoiceBookId == Convert.ToInt32(lkp_InvoiceBook.EditValue)
                    where x.ReturnCode == txtInvoiceCode.Text
                    where x.SL_ReturnId != invoiceId

                    select x.ReturnCode).Count() > 0;
        }

        public void Load_ScaleWeightData(int Id)
        {

            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var weight = DB.ScaleWeights.Where(x => x.ScaleWeightId == Id).FirstOrDefault();
            if (weight == null)
                return;

            lkp_Customers.EditValue = weight.DealerId;
            txtScaleSerial.Text = weight.Serial.ToString();
            txtDriverName.Text = weight.DriverName;
            txtVehicleNumber.Text = weight.CarNo;
            txtDestination.Text = weight.Destination;
            dtInvoiceDate.EditValue = weight.FirstDateTime;

            var item = DB.IC_Items.Where(i => i.ItemId == weight.ItemId).FirstOrDefault();
            decimal qty = Math.Abs(weight.BillSecondWeight - weight.BillFirstWeight);
            if (qty < 0)
                return;

            DataRow row = dtSLReturn_Details.NewRow();
            LoadItemRow(item, row);
            dtSLReturn_Details.Rows.Add(row);
            gridView2.SetRowCellValue(0, "Qty", decimal.ToDouble(qty));

            Get_TotalAccount();
            dtSLReturn_Details.AcceptChanges();
            DataModified = true;
            this.BringToFront();
        }

        private void LoadSourceData(int? ProcessId, int? SourceId)
        {
            if (ProcessId.HasValue)
            {
                cmdProcess.EditValue = ProcessId.Value;
                btnSourceId.EditValue = SourceId;
                pnlSrcPrc.Visible = true;


                if (ProcessId.Value == (int)Process.Scale)
                {
                    var code = DB.ScaleWeights.Where(x => x.ScaleWeightId == SourceId).Select(x => x.Serial).FirstOrDefault();
                    if (code != 0)
                        txtSourceCode.Text = code.ToString();
                    else
                    {
                        cmdProcess.EditValue = null;
                        btnSourceId.EditValue = null;
                        txtSourceCode.Text = string.Empty;
                        pnlSrcPrc.Visible = false;
                    }
                    return;
                }
            }
            else
            {
                cmdProcess.EditValue = null;
                btnSourceId.EditValue = null;
                txtSourceCode.Text = string.Empty;
                pnlSrcPrc.Visible = false;
            }
        }

        private void btnSourceId_Click(object sender, EventArgs e)
        {
            if (btnSourceId.EditValue == null || btnSourceId.EditValue.ToString() == string.Empty)
                return;
            if (cmdProcess.EditValue == null)
                return;

        }

        private void mi_PasteRows_Click(object sender, EventArgs e)
        {
            foreach (DataRow dr in ErpUtils.dt_Copied_Rows.Rows)
            {
                decimal totalqty = Convert.ToDecimal(dr["Qty"]);
                if (Shared.st_Store.MultiplyDimensions != (byte)Dimensions.Distinguish)
                    totalqty = Convert.ToDecimal(dr["Qty"]) * Convert.ToDecimal(dr["Length"]) * Convert.ToDecimal(dr["Width"])
                        * Convert.ToDecimal(dr["Height"]);

                DataRow row = dtSLReturn_Details.NewRow();
                int itemId = Convert.ToInt32(dr["ItemId"]);
                var item = DB.IC_Items.Where(x => x.ItemId == itemId).FirstOrDefault();
                LoadItemRow(item, row);
                row["Qty"] = Convert.ToDouble(dr["Qty"]);
                row["Height"] = dr["Height"];
                row["Length"] = dr["Length"];
                row["Width"] = dr["Width"];
                row["PiecesCount"] = dr["PiecesCount"];
                row["Expire"] = dr["Expire"];
                row["Batch"] = dr["Batch"];
                row["Serial"] = dr["Serial"];
                if (dr["SellPrice"] != DBNull.Value)
                    row["SellPrice"] = Convert.ToDouble(dr["SellPrice"]);

                row["TotalSellPrice"] = decimal.ToDouble(Convert.ToDecimal(row["SellPrice"]) * totalqty);
                dtSLReturn_Details.Rows.Add(row);
            }
        }

        private void repItems_Popup(object sender, EventArgs e)
        {
            if (rep_layout.Length > 0)
            {
                rep_layout.Seek(0, System.IO.SeekOrigin.Begin);
                (sender as GridLookUpEdit).Properties.View.RestoreLayoutFromStream(rep_layout);
            }

            (sender as GridLookUpEdit).Properties.View.ClearColumnsFilter();

        }

        private void repItems_CloseUp(object sender, DevExpress.XtraEditors.Controls.CloseUpEventArgs e)
        {
            rep_layout = new System.IO.MemoryStream();
            (sender as GridLookUpEdit).Properties.View.SaveLayoutToStream(rep_layout);
        }

        private void importFromExcelSheetToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ErpUtils.LoadInvItemsFromExcel(ref dtSLReturn_Details, invoiceId, LoadItemRow, Process.SellReturn);
        }
    }
}