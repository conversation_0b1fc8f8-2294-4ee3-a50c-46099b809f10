﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="&gt;&gt;txt_MarginRight.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="labelControl4.Text" xml:space="preserve">
    <value>Second</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="groupBox3.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="txt_Paperwidth.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="&gt;&gt;chkIsDefaultTemplate.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="&gt;&gt;labelControl3.Name" xml:space="preserve">
    <value>labelControl3</value>
  </data>
  <data name="txtColumnsCount.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;chklst_Line2.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="&gt;&gt;labelControl17.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="groupBox1.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="&gt;&gt;lbl_Line_4.Parent" xml:space="preserve">
    <value>panel2</value>
  </data>
  <data name="&gt;&gt;chklst_Line1.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="txt_QtyPrefix.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl16.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;groupBox3.Name" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;txt_BatchPrefix.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;chk_3.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="labelControl18.TabIndex" type="System.Int32, mscorlib">
    <value>36</value>
  </data>
  <data name="txtCurrency.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_QtyPrefix.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_MarginTop.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="spn_3.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txtColumnsCount.EditValue" type="System.Decimal, mscorlib">
    <value>1</value>
  </data>
  <data name="txtColumnsCount.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="&gt;&gt;txt_QtyPrefix.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txtColumnsCount.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl4.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="cb_Fonts.Location" type="System.Drawing.Point, System.Drawing">
    <value>23, 13</value>
  </data>
  <data name="&gt;&gt;txt_Paperwidth.Name" xml:space="preserve">
    <value>txt_Paperwidth</value>
  </data>
  <data name="spn_1.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_MarginBottom.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="panel2.TabIndex" type="System.Int32, mscorlib">
    <value>100</value>
  </data>
  <data name="labelControl10.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="groupBox4.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 116</value>
  </data>
  <data name="&gt;&gt;labelControl24.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;chklst_Line1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckedComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lbl_line_2.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 28</value>
  </data>
  <data name="labelControl10.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl17.TabIndex" type="System.Int32, mscorlib">
    <value>33</value>
  </data>
  <data name="labelControl26.TabIndex" type="System.Int32, mscorlib">
    <value>27</value>
  </data>
  <data name="txt_Paperheight.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="labelControl25.Text" xml:space="preserve">
    <value>mm</value>
  </data>
  <data name="&gt;&gt;txt_Paperheight.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="chk_ShowBacrodeText.Location" type="System.Drawing.Point, System.Drawing">
    <value>387, 154</value>
  </data>
  <data name="labelControl13.Text" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="lbl_Line_1.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_QtyPrefix.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Line_4.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_BatchPrefix.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl16.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;chk_4.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="cb_Papers.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Paperwidth.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtColumnsCount.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;txtColumnsCount.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="txt_Paperwidth.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;Lbl_Line_3.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <assembly alias="DevExpress.XtraEditors.v15.1" name="DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="txt_MarginTop.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;txt_BatchPrefix.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;bar2.Name" xml:space="preserve">
    <value>bar2</value>
  </data>
  <data name="txt_Line_4.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl13.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="groupBox2.Size" type="System.Drawing.Size, System.Drawing">
    <value>593, 176</value>
  </data>
  <data name="&gt;&gt;spn_1.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="chk_2.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="lbl_Line_4.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="labelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>74, 13</value>
  </data>
  <data name="txtRowsCount.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_Line_4.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;btnNext.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="labelControl6.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtRowsCount.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtColumnsCount.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="labelControl2.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;groupBox5.Name" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="barDockControlRight.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Right</value>
  </data>
  <data name="txt_MarginTop.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;bar1.Name" xml:space="preserve">
    <value>bar1</value>
  </data>
  <data name="&gt;&gt;groupBox4.Name" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="txtColumnsCount.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txt_QtyPrefix.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Paperwidth.EditValue" type="System.Decimal, mscorlib">
    <value>21</value>
  </data>
  <data name="&gt;&gt;labelControl1.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="chk_2.Properties.Caption" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;barDockControlTop.Name" xml:space="preserve">
    <value>barDockControlTop</value>
  </data>
  <data name="txt_MarginRight.Properties.Mask.EditMask" xml:space="preserve">
    <value>N00</value>
  </data>
  <data name="labelControl20.Size" type="System.Drawing.Size, System.Drawing">
    <value>25, 13</value>
  </data>
  <data name="txt_Paperwidth.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="labelControl25.Size" type="System.Drawing.Size, System.Drawing">
    <value>16, 13</value>
  </data>
  <data name="barDockControlRight.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 398</value>
  </data>
  <data name="&gt;&gt;groupBox1.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="txt_Paperheight.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 20</value>
  </data>
  <data name="txt_BatchPrefix.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="labelControl12.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;btnNext.Name" xml:space="preserve">
    <value>btnNext</value>
  </data>
  <data name="labelControl10.Appearance.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8pt, style=Bold</value>
  </data>
  <data name="&gt;&gt;barAndDockingController1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarAndDockingController, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_TemplateName.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_Paperwidth.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="chklst_Line3.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="labelControl14.Size" type="System.Drawing.Size, System.Drawing">
    <value>33, 13</value>
  </data>
  <data name="&gt;&gt;labelControl4.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cb_Papers.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="barDockControlTop.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Paperwidth.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Line_4.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;groupBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="btnPrev.TabIndex" type="System.Int32, mscorlib">
    <value>91</value>
  </data>
  <data name="&gt;&gt;txt_Line_4.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;txtRowsCount.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;labelControl21.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="txt_TemplateName.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="chklst_Line1.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl8.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="&gt;&gt;labelControl36.Name" xml:space="preserve">
    <value>labelControl36</value>
  </data>
  <data name="txt_Paperheight.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barDockControlBottom.Size" type="System.Drawing.Size, System.Drawing">
    <value>616, 0</value>
  </data>
  <data name="labelControl14.Location" type="System.Drawing.Point, System.Drawing">
    <value>239, 49</value>
  </data>
  <data name="spn_4.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="labelControl11.Appearance.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8pt, style=Bold</value>
  </data>
  <data name="barDockControlRight.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <assembly alias="DevExpress.Utils.v15.1" name="DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="chklst_Line3.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="labelControl11.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_MarginBottom.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="btnNext.ToolTip" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_MarginBottom.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl1.TabIndex" type="System.Int32, mscorlib">
    <value>53</value>
  </data>
  <data name="Lbl_Line_3.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="cb_Fonts.EditValue" xml:space="preserve">
    <value>Times New Roman</value>
  </data>
  <data name="labelControl2.Text" xml:space="preserve">
    <value>First</value>
  </data>
  <data name="labelControl28.Location" type="System.Drawing.Point, System.Drawing">
    <value>226, 85</value>
  </data>
  <data name="txt_Paperheight.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_QtyPrefix.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl15.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="txt_MarginLeft.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="lbl_line_2.Appearance.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 12pt</value>
  </data>
  <data name="labelControl17.Text" xml:space="preserve">
    <value>mm</value>
  </data>
  <data name="chklst_Line3.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_MarginLeft.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl19.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barDockControlRight.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;groupBox2.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="labelControl26.Text" xml:space="preserve">
    <value>Font</value>
  </data>
  <data name="&gt;&gt;labelControl11.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="txt_Paperheight.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="labelControl4.Size" type="System.Drawing.Size, System.Drawing">
    <value>35, 13</value>
  </data>
  <data name="&gt;&gt;chklst_Line1.Name" xml:space="preserve">
    <value>chklst_Line1</value>
  </data>
  <data name="chklst_Line3.Size" type="System.Drawing.Size, System.Drawing">
    <value>158, 20</value>
  </data>
  <data name="spn_4.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl24.Text" xml:space="preserve">
    <value>mm</value>
  </data>
  <data name="spn_3.EditValue" type="System.Decimal, mscorlib">
    <value>12</value>
  </data>
  <data name="&gt;&gt;labelControl2.ZOrder" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="&gt;&gt;labelControl12.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_MarginLeft.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_MarginBottom.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="chklst_Line3.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txtCurrency.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl10.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="txtColumnsCount.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_MarginLeft.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl5.Text" xml:space="preserve">
    <value>Column Count</value>
  </data>
  <data name="labelControl11.Text" xml:space="preserve">
    <value>Show
Hide</value>
  </data>
  <data name="chk_2.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="spn_2.Location" type="System.Drawing.Point, System.Drawing">
    <value>346, 69</value>
  </data>
  <data name="chklst_Line2.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;txt_MarginTop.Name" xml:space="preserve">
    <value>txt_MarginTop</value>
  </data>
  <data name="labelControl13.Location" type="System.Drawing.Point, System.Drawing">
    <value>92, 50</value>
  </data>
  <data name="labelControl6.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_PrintBatchNo.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl23.Text" xml:space="preserve">
    <value>mm</value>
  </data>
  <data name="txt_BatchPrefix.Size" type="System.Drawing.Size, System.Drawing">
    <value>79, 20</value>
  </data>
  <data name="labelControl3.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 13</value>
  </data>
  <data name="txtCurrency.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="labelControl10.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtRowsCount.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chklst_Line2.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="spn_3.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="chklst_Line2.Location" type="System.Drawing.Point, System.Drawing">
    <value>387, 70</value>
  </data>
  <data name="txt_BatchPrefix.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="chk_2.EditValue" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Paperheight.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chklst_Line3.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl36.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl6.Location" type="System.Drawing.Point, System.Drawing">
    <value>545, 19</value>
  </data>
  <data name="spn_3.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl13.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cb_Papers.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="labelControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>21, 13</value>
  </data>
  <data name="txt_Line_4.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cb_Papers.Location" type="System.Drawing.Point, System.Drawing">
    <value>29, 19</value>
  </data>
  <data name="labelControl21.TabIndex" type="System.Int32, mscorlib">
    <value>30</value>
  </data>
  <data name="txtCurrency.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="labelControl8.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl19.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 13</value>
  </data>
  <data name="&gt;&gt;groupBox4.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lbl_line_2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_MarginTop.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;barBtnNew.Name" xml:space="preserve">
    <value>barBtnNew</value>
  </data>
  <data name="&gt;&gt;txt_MarginRight.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lbl_line_2.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="spn_2.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="groupBox2.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="&gt;&gt;chk_3.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAA
        AAAfbf8AH2z/AB9r/x8fa/+rH2v/9h9r/9AfbP9HH2//AB9u/wAfcP8AAAAAAB9p/wAfZv8AH2D/Ah9j
        /2cfY//iH2P/7R9j/4YfY/8KH2P/AB9j/wAAAAAADw4QAA4NDwAODQ4ADQwORwwMDdAMCw32CwsMqwsL
        DB8MCw0ADAsNAB9u/wAfbf8WH23/qx9t//8fbv//H27//x9u/9gfb/89H3D/AB9y/wAfcf8AH23/AB9n
        /wAfZ/9cH2X/6x9j//8fY///H2P/9x9j/38fY/8GH2P/AB5e8QAPDhAADw4QAA4NDz0NDQ/YDQ0O/w0M
        Dv8MDA3/DAsNqwwLDRYMCw0AH2//FR9v/6Ifb///H3D//x9x//8fcf//H3H//x9x/9Mfcv82IHD/AiBw
        /wQfcP8DH2z/VB9q/+gfZ///H2X//x9j//8fY///H2P/9x9j/3cfY/8FH2P+ABAPEQAQDxE1Dw4Q0w8O
        EP8ODg//Dg0P/w0NDv8MDA3/DAsNogwLDRUfcP+MH3H//R9y//8ec///HnP//x50//8edP//HnP//x5z
        /9cec/+pH3L/qR9x/6wfbv/mH23//x9q//8faP//H2X//x9j//8fY///H2P/8h9j/04bTb8AERASERAQ
        ErsQEBL/EA8R/w8PEf8PDhD/Dg0P/w0ND/8MDA79DAwNjB5z/9QedP//HnX//x52//8edv//Hnb//x52
        //8edv//Hnb//x51//8edP//HnP//x9x//8fb///H23//x9r//8faP//H2X//x9j//8fY///H2P/gxg4
        hAASERM1ERET7hERE/8REBL/EBAS/xAPEf8PDhD/Dg4Q/w0ND/8NDA7THnX/wx52//8ed///Hnj//x54
        //8eef//Hnn//x54//8eeP/9Hnf/8h52//Iedf/zHnP//h9x//8fb///H23//x9q//8fZ///H2T//x9j
        //8fY/90GkGeABISFCYTEhThEhIU/xIRE/8RERP/EBAS/xAPEf8PDhD/Dg4Q/w4ND78ed/9UHnj/5x55
        //8eev//Hnr//x57//8ee///Hnr/+x56/5AeeP88Hnf/PB52/0Eedf+wHnP//x9x//8fb///H2z//x9p
        //8fZv//H2T/zB9j/yQdV9wAExIVAxMTFYMTEhX8ExIU/xISFP8RERP/ERAS/xAPEf8PDhDkDg4QTx50
        /wAeev9dHnv/7R58//8efP//Hn3//x59//0efP+UHnv/DB55/wAed/8AHnb/AB52/x8edf+5HnP//x9x
        //8fbv//H2v//x9o/9YfZf85H2f/AAAAAAAUExYAFBMWDRQTFpQUExX9ExIV/xISFP8SERP/ERAS6hAP
        EVYTDhQAHnr/AB58/wEefP9nHn3/7x5+//8efv/9Hn7/nR59/xAefP8AHnv/AB52/wAedf8AHnX/AB51
        /yUedP2+H3P//x9x//8fbPvbH2r/Px9o/wAfZv8AH2P/ABQTFgAUExYAFBQWEBQUFp0UExb9ExIV/xIS
        FOwRERNfIxkMABAQEQAee/8AHn3/AB5+/wcefv+xHn///x5//+oef/8tHn//AB59/wAefP8AAAAAAB52
        /wAeV/8AGkKGABk+eUYaQoPzG0KG/xo0ZZIbRZkAH2v/AB9o/wAAAAAAFBQWABQUFgAVFBcAFRQXLRUU
        F+oUExb/ExMVqxMTFAUSERQAEBASAAAAAAAegP8AHn//Ax6A/6kegP//HoD/5x6A/ycegP8AHoD/AAAA
        AAAAAAAAAAAAABpCgQAXFRcAFhMSOhcVFvAXFRb/GBUWjhgYHgAcO3gAAAAAAAAAAAAAAAAAFxYZABYV
        GAAWFRgnFRUX5xUUF/8UExajExIVAhQTFgAAAAAAHoD/AB6A/wAegP8IHoD/sx6B//8egf/rHoH/Lx6B
        /wAegv8AHoL/AAAAAAAXFhkAFBQXABcXGgAXFxpIGBca9BgYG/8ZGBuTGRkcABoaHQAbGx4AAAAAABkZ
        HAAYGBsAFxcZABcWGS8WFhjrFRUX/xUUF6wUFBYGFBMWABQTFQAef/8AHoD/Ah6A/20egf/xHoH//x6B
        //4egv+kHoL/Ex6C/wAegv8AFhYYABsZIAAXFxoAFxcaKRgXGsMYGBv/GRkc/xoZHN4bGh1FHBoeABwb
        HwAdHCAAGhkdABkZHAAYGBsTGBcapBcWGf4WFRj/FRQX7xQUFmYTEBMBExMVAB59/wIegP9kHoD/8B6B
        //8egv//HoL//x6C//8egv+cHoL/EB6B/wAaRX4AFxYZABcWGSQXFxq/GBgb/xkZHP8aGR3/Gxoe/xwb
        HtkcGx89HBwfABoYIQAbGh0AGxodEBkZHJwYGBv/Fxca/xYWGf8VFRf/FBQW7RQTFVwBABAAHn//Vx5/
        /+kegP//HoH//x6C//8egv//HoL//x6C//4egf+JHoD/BBgxUQAWFhgSFxYZsRgXGv8ZGBv/Ghkc/xsa
        Hf8cGx7/HBwf/x0cIM8eHSAmHRwgAB0cHwQbGh6JGhkc/hkYG/8YFxr/FxYZ/xYVGP8VFBf/ExMV5hMS
        FFIefv/EHn///x6A//8egf//HoL//x6C//8egv//HoL//x6B/+Megf8nGUB0ABYWGEwXFhn4GBca/xkY
        G/8aGh3/Gxse/xwcH/8dHCD/Hh0h/x4dIXUdHCAAHBsfJxwbHuMaGh3/GRkc/xgXGv8XFhn/FhUY/xUU
        F/8UExX/ExIUwR5+/9Uef///HoD//x6B//8egf//HoL//x6C//8egf//HoH/7h6B/zQaRX4AFhYZWhcW
        GfwYGBv/GRkc/xsaHf8cGx7/HRwg/x4dIf8fHiL/Hx4igx4dIQAdHB80HBsf7hsaHf8aGRz/GBgb/xcX
        Gf8WFRj/FRQX/xQTFf8TEhXSHn7/jR5///0egP//HoD//x6B//8egf//HoH//x6B//8egf+4HoD/EBk3
        YAAWFhkqFxYZ2xgYG/8ZGRz/Gxod/xwbHv8dHCD/Hh0h/x8eIu8gHyNKHx4iABwcHxAcGx64Gxod/xoZ
        HP8YGBv/Fxca/xYVGP8VFBf/FBMV/BMSFYgefv8VHn7/oh5///8egP//HoD//x6B//8egf//HoD/0B6A
        /zIegf8AFxIRABgaHQAXFxpQGBca5hkYHP8aGh3/Gxse/x0cH/8dHSD1Hh0hch8eIwQfHiIAGxseABwb
        HjIaGh3QGRkc/xgXG/8XFhn/FhUY/xUUF/8UExafExIVEx5+/wAefv8XHn7/qx5///8ef///Hn///x6A
        /9UegP85HoD/AB5//wAYIzUAFxcaABgYGwAYFxpXGRgb6hoZHP8bGh7/HBse+B0cH3weHSEFHh0hAB8e
        IgAbGh0AGxodABoZHDkZGBvVGBca/xcWGf8WFRj/FRQXqBQUFhUUExYAHn7/AB5+/wAefv8dHn7/yR5+
        //8efv/yHn//Sh5//wAef/8AHoD/AAAAAAAYFxoAGBcaABUXGgAZGBtUGRkcwxoaHc0bGh5yHBseCB0c
        HwAeHSEAAAAAABsaHQAaGRwAGRgbABgYG0oYFxryFxYZ/xYVGMUVFBcbFRQXABQUFgAefv8AHnz/AB56
        /wIefP+oHn3//x59/+ceff8lHn3/AB5//wAAAAAAAAAAAAAAAAAYGBoAGBgbABgXGgAaGRwNGhkdERkZ
        HAAbGh4AGxoeAAAAAAAAAAAAAAAAABkZHAAXFxoAFxcaJRcWGecWFRj/FRUXohMSFQEVFBcAFRQXAB50
        /wAed/8AHnn/CR56/7Qee///Hnv/6x57/y4eev8AHnj/AB53/wAAAAAAFRQXABYVGAAWFRgAFxYZJBgX
        GnEYFxp6GRgbNBkOEQAYGBsAGBcaAAAAAAAWFhgAFhYYABYWGQAXFhkuFhYY6xUVF/8VFBeuFBQWBxMT
        FQASERQAHnL/AB11/wMedv9zHnf/8x54//8eef/+Hnn/oR55/xEed/8AHnb/ABMSFgAbFx0AFRQXABUV
        Fy4WFhjJFxYZ/xgXGv8YFxreGBcaShcXGgAYFxoAFxYZABYVGAAWFRgAFhYYERYWGKEWFRj+FRQX/xQT
        FvETEhVuEhETAxIREwAgb/8CH3L/aR50//Iedf//Hnb//x53//8ed//+Hnf/lx52/w0edf8AGEOMABQT
        FgAUExYnFRQXxBUVF/8WFhj/FxYZ/xcXGv8XFxrdFxcaQRYWGQAWFRgAFhUYABYVGA0WFRiXFhUY/hUU
        F/8UExb/ExMV/xISFPARERNlEQ4RAh9t/1sfb//sH3H//x9y//8ec///HnT//x50//8edP/9HnT/hx9y
        /wUVKUwAEhIUFBMSFbYUExb/FBQW/xERE/8QEBL/FhUY/xYWGf8XFhnXFhYYYBYVGE4WFRhOFhUYmBUV
        F/wVFBf/FBMW/xMTFf8SEhT/ERET/xEQEuoQDxFXH2v/xh9s//8fbv//H2///x9w//8fcf//H3H//x9x
        //8fcf/kH3D/KBc3cQASERNOEhIU+BMSFf8SERT/DAwN/woJC/8QDxH/FRUX/xYVGP8WFRj7FRUX+BUU
        F/gVFBf/FBQW/xQTFv8TEhX/EhIU/xIRE/8REBL/EA8R/w8PEMMfZ//UH2n//x9r//8fbP//H23//x9u
        //8fbv//H27//x9u/+4fbv80Fzl6ABEQE1oRERP8EhIU/xERE/8ODg//DAwN/xAPEf8UExb/FBQW/xQU
        Fv8UFBb/FBMW/xQTFv8TExX/ExIU/xISFP8SERP/ERAS/xAQEv8PDxH/Dg4Q0R9l/4kfZf/8H2f//x9o
        //8faf//H2r//x9q//8fav//H2v/sh9r/w4VKlcAEBASKRAQEtoRERP/EhET/xEQEv8QEBL/EhIU/xMT
        Ff8TExXyExMVsxMTFagTEhWoExIU1BISFP8SERP/ERET/xEQEv8QDxH/Dw8R/w4OEPwODQ+EH2P/Ex9j
        /6AfZP//H2T//x9l//8fZv//H2b//x9n/8gfaP8qH2j/AA0CAAAREBIAEA8RThAQEuUREBL/ERET/xIR
        E/8SERP/EhEU9hISFHYTExUIEhEUAxMRFQISERMxERETzREQEv8QEBL/EA8R/w8OEP8ODhD/Dg0Pnw0N
        DxIfY/8AH2P/Fh9j/6kfY///H2P//x9j//8fY//MH2T/MB9m/wAfVP8AERgnABAPEQAQERMAEA8RVRAP
        EegQEBL/EBAS/xEQEvcRERN9EhETBhISFAATEhUAExIVABEREwAQEBI2EA8R0g8PEf8PDhD/Dg4P/w4N
        D6gNDQ8VDg0PAB9j/wAfY/8AH2P/HR9j/6QfY//nH2P/wh9j/zofZP8AH2P/AB9m/wAAAAAAEA8RAA8P
        EQAKDhIBDw8QYA8PEdcQDxHiEA8RgRAPEQkREBIAEhETAAAAAAARERMAEA8RABAPEQAPDhBADg4Qxg4N
        D+cODQ+iDQ0OHA0NDwAODQ8AACAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAGAIBw
        DgEAIAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAQAAHAOAAAgBAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgBAA=
</value>
  </data>
  <data name="txt_Line_4.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;panel2.Name" xml:space="preserve">
    <value>panel2</value>
  </data>
  <data name="txtRowsCount.EditValue" type="System.Decimal, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;btnPrev.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="cb_Fonts.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl8.Location" type="System.Drawing.Point, System.Drawing">
    <value>555, 131</value>
  </data>
  <data name="chk_2.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl10.Size" type="System.Drawing.Size, System.Drawing">
    <value>25, 26</value>
  </data>
  <data name="lbl_Line_1.Size" type="System.Drawing.Size, System.Drawing">
    <value>290, 19</value>
  </data>
  <data name="lbl_line_2.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="chklst_Line3.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="spn_3.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;labelControl18.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="txt_Paperwidth.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_QtyPrefix.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="chk_1.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="barDockControlRight.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;cb_Fonts.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="Lbl_Line_3.TabIndex" type="System.Int32, mscorlib">
    <value>100</value>
  </data>
  <data name="cb_Fonts.Size" type="System.Drawing.Size, System.Drawing">
    <value>187, 20</value>
  </data>
  <data name="chklst_Line1.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="txt_MarginTop.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl28.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;labelControl22.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_MarginTop.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="labelControl10.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl11.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_TemplateName.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl16.TabIndex" type="System.Int32, mscorlib">
    <value>32</value>
  </data>
  <data name="cb_Fonts.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="spn_2.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Paperheight.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="labelControl7.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 13</value>
  </data>
  <data name="labelControl27.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_BatchPrefix.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;chk_2.Name" xml:space="preserve">
    <value>chk_2</value>
  </data>
  <data name="labelControl26.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 13</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_MarginLeft.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="txt_MarginTop.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl16.Size" type="System.Drawing.Size, System.Drawing">
    <value>16, 13</value>
  </data>
  <data name="txt_Paperheight.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lbl_line_2.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlLeft.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 398</value>
  </data>
  <data name="spn_2.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl1.Text" xml:space="preserve">
    <value>Template Name</value>
  </data>
  <data name="&gt;&gt;labelControl26.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="btnPrev.Location" type="System.Drawing.Point, System.Drawing">
    <value>11, 37</value>
  </data>
  <data name="chk_4.Location" type="System.Drawing.Point, System.Drawing">
    <value>315, 128</value>
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="cb_Papers.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Near</value>
  </data>
  <data name="&gt;&gt;labelControl23.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="&gt;&gt;txt_MarginLeft.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl6.Size" type="System.Drawing.Size, System.Drawing">
    <value>23, 13</value>
  </data>
  <data name="&gt;&gt;labelControl5.Name" xml:space="preserve">
    <value>labelControl5</value>
  </data>
  <data name="txt_MarginRight.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtCurrency.Location" type="System.Drawing.Point, System.Drawing">
    <value>455, 82</value>
  </data>
  <data name="&gt;&gt;labelControl6.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl18.Location" type="System.Drawing.Point, System.Drawing">
    <value>240, 22</value>
  </data>
  <data name="&gt;&gt;groupBox1.Name" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;txt_MarginTop.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="spn_1.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl1.Name" xml:space="preserve">
    <value>labelControl1</value>
  </data>
  <data name="labelControl27.Text" xml:space="preserve">
    <value>Batch Prefix</value>
  </data>
  <data name="txt_QtyPrefix.Location" type="System.Drawing.Point, System.Drawing">
    <value>147, 82</value>
  </data>
  <data name="&gt;&gt;txtCurrency.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="txt_BatchPrefix.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="chkIsDefaultTemplate.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="chk_3.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chkIsDefaultTemplate.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>0, 0, 0, 0</value>
  </data>
  <data name="labelControl6.Text" xml:space="preserve">
    <value>Line</value>
  </data>
  <data name="&gt;&gt;spn_2.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;barManager1.Name" xml:space="preserve">
    <value>barManager1</value>
  </data>
  <data name="spn_3.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="spn_2.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txt_Paperheight.Name" xml:space="preserve">
    <value>txt_Paperheight</value>
  </data>
  <data name="txt_Paperwidth.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;repositoryItemTextEdit1.Name" xml:space="preserve">
    <value>repositoryItemTextEdit1</value>
  </data>
  <data name="txt_MarginRight.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;lbl_line_2.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;labelControl27.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="chklst_Line3.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.XtraForm, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="groupBox5.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="txt_MarginBottom.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="chk_ShowBacrodeText.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="txt_MarginTop.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtCurrency.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;barBtn_Preview.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="spn_4.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl21.Name" xml:space="preserve">
    <value>labelControl21</value>
  </data>
  <data name="labelControl9.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_BatchPrefix.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_MarginBottom.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl16.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtCurrency.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="panel2.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 37</value>
  </data>
  <data name="labelControl9.Text" xml:space="preserve">
    <value>Line Contents</value>
  </data>
  <data name="barBtn_Cancel.Caption" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="chk_1.EditValue" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="Lbl_Line_3.Appearance.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 12pt</value>
  </data>
  <data name="&gt;&gt;chkIsDefaultTemplate.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl7.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="chkIsDefaultTemplate.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="barDockControlTop.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl28.Text" xml:space="preserve">
    <value>Qty Prefix</value>
  </data>
  <data name="lbl_Line_1.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtCurrency.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_MarginTop.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chkIsDefaultTemplate.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="chklst_Line3.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl20.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl19.Location" type="System.Drawing.Point, System.Drawing">
    <value>220, 22</value>
  </data>
  <data name="txt_TemplateName.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl3.Location" type="System.Drawing.Point, System.Drawing">
    <value>527, 85</value>
  </data>
  <data name="chklst_Line3.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl5.Location" type="System.Drawing.Point, System.Drawing">
    <value>90, 17</value>
  </data>
  <data name="&gt;&gt;labelControl15.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;barButtonItem8.Name" xml:space="preserve">
    <value>barButtonItem8</value>
  </data>
  <data name="labelControl8.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Barcode Settings</value>
  </data>
  <data name="txt_MarginRight.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="chklst_Line1.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_Line_4.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_BatchPrefix.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="groupBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>301, 39</value>
  </data>
  <data name="txt_QtyPrefix.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl5.Size" type="System.Drawing.Size, System.Drawing">
    <value>67, 13</value>
  </data>
  <data name="labelControl13.Size" type="System.Drawing.Size, System.Drawing">
    <value>28, 13</value>
  </data>
  <data name="chklst_Line2.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="chklst_Line1.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="barBtnSave.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="spn_3.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Paperwidth.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 20</value>
  </data>
  <data name="&gt;&gt;labelControl36.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="chklst_Line2.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl2.Name" xml:space="preserve">
    <value>labelControl2</value>
  </data>
  <data name="&gt;&gt;barBtnHelp.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl10.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="bar1.Text" xml:space="preserve">
    <value>Tools</value>
  </data>
  <data name="labelControl12.Size" type="System.Drawing.Size, System.Drawing">
    <value>34, 13</value>
  </data>
  <data name="chkIsDefaultTemplate.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;chk_1.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="txt_TemplateName.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="chklst_Line2.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Paperheight.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barButtonItem8.Caption" xml:space="preserve">
    <value>Help</value>
  </data>
  <data name="chk_2.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="txtRowsCount.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="barBtnHelp.Caption" xml:space="preserve">
    <value>Help</value>
  </data>
  <data name="txt_MarginBottom.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;btnNext.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;chklst_Line2.Name" xml:space="preserve">
    <value>chklst_Line2</value>
  </data>
  <data name="lbl_Line_4.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="txt_MarginLeft.Location" type="System.Drawing.Point, System.Drawing">
    <value>28, 46</value>
  </data>
  <data name="chk_ShowBacrodeText.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="chk_PrintBatchNo.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;groupBox3.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="&gt;&gt;labelControl5.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_Paperwidth.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="spn_4.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="chkIsDefaultTemplate.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;barManager1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarManager, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;groupBox5.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="txt_TemplateName.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;btnNext.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="txt_BatchPrefix.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lbl_Line_1.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="chk_4.EditValue" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Line_4.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;chklst_Line3.Name" xml:space="preserve">
    <value>chklst_Line3</value>
  </data>
  <data name="txt_MarginLeft.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txt_TemplateName.Name" xml:space="preserve">
    <value>txt_TemplateName</value>
  </data>
  <data name="labelControl11.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;labelControl7.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="txt_QtyPrefix.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl15.Location" type="System.Drawing.Point, System.Drawing">
    <value>227, 17</value>
  </data>
  <data name="&gt;&gt;spn_4.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="barBtn_Cancel.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="txt_MarginLeft.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="cb_Fonts.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="chk_2.Location" type="System.Drawing.Point, System.Drawing">
    <value>315, 68</value>
  </data>
  <data name="labelControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="spn_3.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_TemplateName.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="labelControl11.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="labelControl3.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="chk_3.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl10.Location" type="System.Drawing.Point, System.Drawing">
    <value>351, 11</value>
  </data>
  <data name="bar2.Text" xml:space="preserve">
    <value>Custom 3</value>
  </data>
  <data name="txt_Line_4.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;lbl_Line_1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txtRowsCount.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl5.TabIndex" type="System.Int32, mscorlib">
    <value>30</value>
  </data>
  <data name="txt_MarginRight.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 20</value>
  </data>
  <data name="groupBox5.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="txt_MarginTop.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtColumnsCount.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lbl_Line_1.Text" xml:space="preserve">
    <value> </value>
  </data>
  <data name="chk_ShowBacrodeText.Properties.Caption" xml:space="preserve">
    <value>Show Barcode Number</value>
  </data>
  <data name="txt_Line_4.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_MarginLeft.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_BatchPrefix.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl2.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;txtCurrency.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Line_4.Size" type="System.Drawing.Size, System.Drawing">
    <value>158, 20</value>
  </data>
  <data name="&gt;&gt;labelControl7.Name" xml:space="preserve">
    <value>labelControl7</value>
  </data>
  <data name="groupBox3.Location" type="System.Drawing.Point, System.Drawing">
    <value>295, 116</value>
  </data>
  <data name="labelControl6.Appearance.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8pt, style=Bold</value>
  </data>
  <data name="&gt;&gt;chkIsDefaultTemplate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Name" xml:space="preserve">
    <value>barDockControlBottom</value>
  </data>
  <data name="&gt;&gt;labelControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="groupBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>295, 196</value>
  </data>
  <data name="chk_4.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lbl_Line_4.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl17.Size" type="System.Drawing.Size, System.Drawing">
    <value>16, 13</value>
  </data>
  <data name="lbl_Line_4.Size" type="System.Drawing.Size, System.Drawing">
    <value>290, 19</value>
  </data>
  <data name="txt_Line_4.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;chklst_Line2.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;spn_1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_MarginBottom.Name" xml:space="preserve">
    <value>txt_MarginBottom</value>
  </data>
  <data name="chk_3.Properties.Caption" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl6.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="cb_Papers.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;btnPrev.Name" xml:space="preserve">
    <value>btnPrev</value>
  </data>
  <data name="labelControl24.Size" type="System.Drawing.Size, System.Drawing">
    <value>16, 13</value>
  </data>
  <data name="spn_2.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;spn_3.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="&gt;&gt;labelControl27.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="chk_PrintBatchNo.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cb_Papers.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="groupBox1.Text" xml:space="preserve">
    <value>Paper Type</value>
  </data>
  <data name="txtRowsCount.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="chklst_Line1.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="chklst_Line3.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="labelControl11.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barBtnNew.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="txt_TemplateName.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;spn_3.Name" xml:space="preserve">
    <value>spn_3</value>
  </data>
  <data name="txt_MarginTop.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="spn_1.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl23.Size" type="System.Drawing.Size, System.Drawing">
    <value>16, 13</value>
  </data>
  <data name="&gt;&gt;lbl_Line_4.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;labelControl22.Name" xml:space="preserve">
    <value>labelControl22</value>
  </data>
  <data name="&gt;&gt;chkIsDefaultTemplate.Name" xml:space="preserve">
    <value>chkIsDefaultTemplate</value>
  </data>
  <data name="labelControl15.Size" type="System.Drawing.Size, System.Drawing">
    <value>53, 13</value>
  </data>
  <data name="&gt;&gt;lbl_Line_4.Name" xml:space="preserve">
    <value>lbl_Line_4</value>
  </data>
  <data name="&gt;&gt;barBtn_Cancel.Name" xml:space="preserve">
    <value>barBtn_Cancel</value>
  </data>
  <data name="chk_4.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="groupBox2.Text" xml:space="preserve">
    <value>Sticker Settings</value>
  </data>
  <data name="&gt;&gt;labelControl17.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;chk_2.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="labelControl19.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="cb_Papers.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="cb_Papers.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;spn_1.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="&gt;&gt;labelControl25.Name" xml:space="preserve">
    <value>labelControl25</value>
  </data>
  <data name="labelControl22.Size" type="System.Drawing.Size, System.Drawing">
    <value>16, 13</value>
  </data>
  <data name="txtCurrency.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_TemplateName.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl6.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="chklst_Line1.Location" type="System.Drawing.Point, System.Drawing">
    <value>387, 41</value>
  </data>
  <data name="&gt;&gt;barButtonItem8.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl13.TabIndex" type="System.Int32, mscorlib">
    <value>30</value>
  </data>
  <data name="chk_4.Properties.Caption" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl4.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="barDockControlRight.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="spn_1.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_MarginTop.Properties.Mask.EditMask" xml:space="preserve">
    <value>N00</value>
  </data>
  <data name="spn_1.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="txt_MarginTop.Location" type="System.Drawing.Point, System.Drawing">
    <value>160, 18</value>
  </data>
  <data name="&gt;&gt;labelControl14.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="spn_4.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="chklst_Line1.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtCurrency.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="groupBox3.Text" xml:space="preserve">
    <value>Print Paper Settings</value>
  </data>
  <data name="&gt;&gt;repositoryItemTextEdit1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemTextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="spn_4.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl21.Size" type="System.Drawing.Size, System.Drawing">
    <value>19, 13</value>
  </data>
  <data name="spn_3.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="spn_2.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl3.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;spn_2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Paperwidth.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="labelControl17.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 49</value>
  </data>
  <data name="&gt;&gt;labelControl5.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;labelControl20.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="txt_MarginTop.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="lbl_Line_1.TabIndex" type="System.Int32, mscorlib">
    <value>100</value>
  </data>
  <data name="txt_Line_4.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="spn_1.EditValue" type="System.Decimal, mscorlib">
    <value>12</value>
  </data>
  <data name="&gt;&gt;txtCurrency.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="groupBox4.Text" xml:space="preserve">
    <value>Margins</value>
  </data>
  <data name="chklst_Line1.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="btnNext.TabIndex" type="System.Int32, mscorlib">
    <value>92</value>
  </data>
  <data name="txt_MarginRight.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_QtyPrefix.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_MarginRight.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_TemplateName.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtRowsCount.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_TemplateName.Location" type="System.Drawing.Point, System.Drawing">
    <value>295, 44</value>
  </data>
  <data name="chklst_Line1.Size" type="System.Drawing.Size, System.Drawing">
    <value>158, 20</value>
  </data>
  <data name="lbl_line_2.Text" xml:space="preserve">
    <value> </value>
  </data>
  <data name="txt_Paperwidth.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txtRowsCount.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;labelControl11.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_MarginRight.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="&gt;&gt;cb_Fonts.Name" xml:space="preserve">
    <value>cb_Fonts</value>
  </data>
  <data name="&gt;&gt;cb_Fonts.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.FontEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lbl_Line_1.Parent" xml:space="preserve">
    <value>panel2</value>
  </data>
  <data name="groupBox5.Text" xml:space="preserve">
    <value>Font</value>
  </data>
  <data name="&gt;&gt;labelControl9.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;txt_MarginRight.Name" xml:space="preserve">
    <value>txt_MarginRight</value>
  </data>
  <data name="txtColumnsCount.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;spn_4.Name" xml:space="preserve">
    <value>spn_4</value>
  </data>
  <data name="&gt;&gt;barBtnHelp.Name" xml:space="preserve">
    <value>barBtnHelp</value>
  </data>
  <data name="&gt;&gt;chk_ShowBacrodeText.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="chkIsDefaultTemplate.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="labelControl36.Size" type="System.Drawing.Size, System.Drawing">
    <value>49, 13</value>
  </data>
  <data name="labelControl21.Location" type="System.Drawing.Point, System.Drawing">
    <value>88, 51</value>
  </data>
  <data name="&gt;&gt;lbl_Line_1.Name" xml:space="preserve">
    <value>lbl_Line_1</value>
  </data>
  <data name="&gt;&gt;labelControl12.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="lbl_Line_4.TabIndex" type="System.Int32, mscorlib">
    <value>100</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl15.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_MarginRight.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="spn_2.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;chk_PrintBatchNo.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txt_BatchPrefix.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="chklst_Line2.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="chk_4.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="spn_3.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Paperwidth.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_BatchPrefix.Location" type="System.Drawing.Point, System.Drawing">
    <value>295, 82</value>
  </data>
  <data name="&gt;&gt;chk_ShowBacrodeText.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="txt_MarginBottom.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl17.Name" xml:space="preserve">
    <value>labelControl17</value>
  </data>
  <data name="&gt;&gt;groupBox1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="chklst_Line2.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="labelControl3.Text" xml:space="preserve">
    <value>Currency Prefix</value>
  </data>
  <data name="&gt;&gt;labelControl13.Name" xml:space="preserve">
    <value>labelControl13</value>
  </data>
  <data name="&gt;&gt;labelControl22.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="labelControl26.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;txtColumnsCount.Name" xml:space="preserve">
    <value>txtColumnsCount</value>
  </data>
  <data name="labelControl27.Size" type="System.Drawing.Size, System.Drawing">
    <value>58, 13</value>
  </data>
  <data name="chk_PrintBatchNo.TabIndex" type="System.Int32, mscorlib">
    <value>102</value>
  </data>
  <data name="barDockControlLeft.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="spn_2.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;Lbl_Line_3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="spn_3.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="spn_1.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;chklst_Line3.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="&gt;&gt;labelControl6.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="labelControl12.TabIndex" type="System.Int32, mscorlib">
    <value>26</value>
  </data>
  <data name="chkIsDefaultTemplate.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl4.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="labelControl7.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>528, 47</value>
  </data>
  <data name="txt_MarginLeft.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 20</value>
  </data>
  <data name="&gt;&gt;labelControl8.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="labelControl36.TabIndex" type="System.Int32, mscorlib">
    <value>93</value>
  </data>
  <data name="spn_2.EditValue" type="System.Decimal, mscorlib">
    <value>12</value>
  </data>
  <data name="chk_2.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="txt_Paperheight.EditValue" type="System.Decimal, mscorlib">
    <value>29.7</value>
  </data>
  <data name="groupBox2.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="labelControl6.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_MarginTop.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_MarginRight.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_MarginLeft.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl21.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="chk_4.Size" type="System.Drawing.Size, System.Drawing">
    <value>20, 19</value>
  </data>
  <data name="labelControl6.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="chkIsDefaultTemplate.Size" type="System.Drawing.Size, System.Drawing">
    <value>131, 19</value>
  </data>
  <data name="barBtn_Preview.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="spn_1.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="chklst_Line1.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txt_MarginTop.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cb_Fonts.Parent" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="chkIsDefaultTemplate.Properties.Caption" xml:space="preserve">
    <value>Default Template</value>
  </data>
  <data name="spn_2.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl28.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barDockControlBottom.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="&gt;&gt;labelControl4.Name" xml:space="preserve">
    <value>labelControl4</value>
  </data>
  <data name="&gt;&gt;labelControl9.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;groupBox2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl9.Name" xml:space="preserve">
    <value>labelControl9</value>
  </data>
  <data name="&gt;&gt;groupBox2.Name" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="Lbl_Line_3.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="txt_Paperheight.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_TemplateName.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl11.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;labelControl25.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl11.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="chk_3.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl25.Location" type="System.Drawing.Point, System.Drawing">
    <value>11, 50</value>
  </data>
  <data name="labelControl7.Text" xml:space="preserve">
    <value>Third</value>
  </data>
  <data name="chk_2.Size" type="System.Drawing.Size, System.Drawing">
    <value>20, 19</value>
  </data>
  <data name="chklst_Line2.Size" type="System.Drawing.Size, System.Drawing">
    <value>158, 20</value>
  </data>
  <data name="&gt;&gt;txt_MarginTop.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="chklst_Line1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lbl_Line_4.Text" xml:space="preserve">
    <value>||||||||||||| ||| ||| ||||||||| |||</value>
  </data>
  <data name="&gt;&gt;labelControl18.Name" xml:space="preserve">
    <value>labelControl18</value>
  </data>
  <data name="chk_1.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl9.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl23.Name" xml:space="preserve">
    <value>labelControl23</value>
  </data>
  <data name="txt_MarginBottom.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_QtyPrefix.Size" type="System.Drawing.Size, System.Drawing">
    <value>73, 20</value>
  </data>
  <data name="lbl_Line_4.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="spn_2.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_MarginBottom.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="spn_4.EditValue" type="System.Decimal, mscorlib">
    <value>12</value>
  </data>
  <data name="Lbl_Line_3.Size" type="System.Drawing.Size, System.Drawing">
    <value>290, 19</value>
  </data>
  <data name="chk_2.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="spn_4.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_MarginRight.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="btnNext.Text" xml:space="preserve">
    <value>=&gt;</value>
  </data>
  <data name="chk_3.Size" type="System.Drawing.Size, System.Drawing">
    <value>20, 19</value>
  </data>
  <data name="txt_MarginBottom.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="txtRowsCount.Location" type="System.Drawing.Point, System.Drawing">
    <value>179, 14</value>
  </data>
  <data name="&gt;&gt;labelControl24.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="txt_MarginLeft.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlTop.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;Lbl_Line_3.Parent" xml:space="preserve">
    <value>panel2</value>
  </data>
  <data name="chk_1.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;groupBox3.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="txt_Paperheight.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_MarginRight.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtColumnsCount.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lbl_Line_1.Appearance.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 12pt</value>
  </data>
  <data name="txt_QtyPrefix.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_Paperheight.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtCurrency.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chklst_Line1.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_TemplateName.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_QtyPrefix.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Name" xml:space="preserve">
    <value>barDockControlLeft</value>
  </data>
  <data name="barDockControlLeft.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtCurrency.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtRowsCount.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;chklst_Line1.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="labelControl11.Location" type="System.Drawing.Point, System.Drawing">
    <value>308, 11</value>
  </data>
  <data name="&gt;&gt;labelControl27.Name" xml:space="preserve">
    <value>labelControl27</value>
  </data>
  <data name="chk_ShowBacrodeText.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;barDockControlTop.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl6.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="panel2.Size" type="System.Drawing.Size, System.Drawing">
    <value>295, 107</value>
  </data>
  <data name="labelControl9.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;labelControl16.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="txt_MarginLeft.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;spn_1.Name" xml:space="preserve">
    <value>spn_1</value>
  </data>
  <data name="txt_Line_4.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="labelControl7.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="chk_1.Size" type="System.Drawing.Size, System.Drawing">
    <value>20, 19</value>
  </data>
  <data name="barDockControlBottom.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl14.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="txtRowsCount.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Paperwidth.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;labelControl26.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;spn_2.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="&gt;&gt;labelControl18.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="txt_Line_4.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_2.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Paperwidth.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_BatchPrefix.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Paperwidth.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;txt_BatchPrefix.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Paperheight.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;chk_ShowBacrodeText.Name" xml:space="preserve">
    <value>chk_ShowBacrodeText</value>
  </data>
  <data name="&gt;&gt;chk_1.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="&gt;&gt;labelControl23.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="groupBox4.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="chk_PrintBatchNo.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="txtColumnsCount.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl14.Text" xml:space="preserve">
    <value>Lenght</value>
  </data>
  <data name="txtRowsCount.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl28.Name" xml:space="preserve">
    <value>labelControl28</value>
  </data>
  <data name="txt_MarginBottom.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_MarginLeft.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="txt_QtyPrefix.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_TemplateName.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="spn_1.Location" type="System.Drawing.Point, System.Drawing">
    <value>346, 40</value>
  </data>
  <data name="labelControl9.Appearance.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8pt, style=Bold</value>
  </data>
  <data name="&gt;&gt;labelControl5.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="cb_Papers.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_1.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="chk_PrintBatchNo.Size" type="System.Drawing.Size, System.Drawing">
    <value>220, 19</value>
  </data>
  <data name="chklst_Line1.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_TemplateName.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl13.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_MarginBottom.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lbl_line_2.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="chk_3.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="labelControl16.Location" type="System.Drawing.Point, System.Drawing">
    <value>159, 50</value>
  </data>
  <data name="chk_1.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtColumnsCount.Size" type="System.Drawing.Size, System.Drawing">
    <value>42, 20</value>
  </data>
  <data name="&gt;&gt;chk_PrintBatchNo.Name" xml:space="preserve">
    <value>chk_PrintBatchNo</value>
  </data>
  <data name="groupBox5.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 196</value>
  </data>
  <data name="txt_MarginBottom.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl14.Name" xml:space="preserve">
    <value>labelControl14</value>
  </data>
  <data name="chklst_Line2.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_MarginRight.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="spn_1.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl10.Name" xml:space="preserve">
    <value>labelControl10</value>
  </data>
  <data name="spn_4.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="labelControl24.TabIndex" type="System.Int32, mscorlib">
    <value>36</value>
  </data>
  <data name="txt_Paperheight.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl15.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="chk_3.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="groupBox4.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="chklst_Line2.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl19.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="spn_1.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="txtColumnsCount.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlBottom.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtRowsCount.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;txt_Line_4.Name" xml:space="preserve">
    <value>txt_Line_4</value>
  </data>
  <data name="&gt;&gt;labelControl23.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="labelControl19.Text" xml:space="preserve">
    <value>Upper</value>
  </data>
  <data name="chkIsDefaultTemplate.Location" type="System.Drawing.Point, System.Drawing">
    <value>145, 42</value>
  </data>
  <data name="txtColumnsCount.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="chk_PrintBatchNo.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;barDockControlTop.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="chklst_Line3.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Paperwidth.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtRowsCount.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="spn_2.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_MarginLeft.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>frm_ST_Barcode</value>
  </data>
  <data name="chkIsDefaultTemplate.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_TemplateName.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl20.Location" type="System.Drawing.Point, System.Drawing">
    <value>87, 22</value>
  </data>
  <data name="chk_3.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="lbl_Line_4.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 80</value>
  </data>
  <data name="btnPrev.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 19</value>
  </data>
  <data name="&gt;&gt;barBtn_Preview.Name" xml:space="preserve">
    <value>barBtn_Preview</value>
  </data>
  <data name="Lbl_Line_3.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl14.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;txtRowsCount.Name" xml:space="preserve">
    <value>txtRowsCount</value>
  </data>
  <data name="txt_Paperheight.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;spn_4.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtColumnsCount.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_QtyPrefix.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtCurrency.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;chk_1.Name" xml:space="preserve">
    <value>chk_1</value>
  </data>
  <data name="&gt;&gt;txt_MarginLeft.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="cb_Fonts.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;spn_3.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="txt_Line_4.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txtRowsCount.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;chk_ShowBacrodeText.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;labelControl6.Name" xml:space="preserve">
    <value>labelControl6</value>
  </data>
  <data name="chk_PrintBatchNo.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl26.Parent" xml:space="preserve">
    <value>groupBox5</value>
  </data>
  <data name="chkIsDefaultTemplate.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Paperwidth.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_line_2.TabIndex" type="System.Int32, mscorlib">
    <value>100</value>
  </data>
  <data name="labelControl23.Location" type="System.Drawing.Point, System.Drawing">
    <value>141, 51</value>
  </data>
  <data name="lbl_line_2.Size" type="System.Drawing.Size, System.Drawing">
    <value>290, 19</value>
  </data>
  <data name="&gt;&gt;chk_PrintBatchNo.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;spn_2.Name" xml:space="preserve">
    <value>spn_2</value>
  </data>
  <data name="txt_Paperwidth.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barDockControlBottom.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 429</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Name" xml:space="preserve">
    <value>barDockControlRight</value>
  </data>
  <data name="&gt;&gt;txt_TemplateName.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtRowsCount.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txtCurrency.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;lbl_Line_1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl19.Name" xml:space="preserve">
    <value>labelControl19</value>
  </data>
  <data name="&gt;&gt;chk_3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtCurrency.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_BatchPrefix.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="chklst_Line1.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl25.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="chk_1.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;groupBox2.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="spn_2.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;panel2.Type" xml:space="preserve">
    <value>System.Windows.Forms.Panel, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;txt_QtyPrefix.Name" xml:space="preserve">
    <value>txt_QtyPrefix</value>
  </data>
  <data name="&gt;&gt;labelControl22.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="&gt;&gt;labelControl24.Name" xml:space="preserve">
    <value>labelControl24</value>
  </data>
  <data name="&gt;&gt;chk_4.Name" xml:space="preserve">
    <value>chk_4</value>
  </data>
  <data name="txt_QtyPrefix.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;spn_4.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="barDockControlLeft.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lbl_Line_4.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl20.Name" xml:space="preserve">
    <value>labelControl20</value>
  </data>
  <data name="txt_Line_4.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="chk_3.EditValue" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl28.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="barDockControlTop.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="chklst_Line1.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>616, 429</value>
  </data>
  <data name="Lbl_Line_3.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txt_MarginBottom.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="btnNext.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 19</value>
  </data>
  <data name="spn_4.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Line_4.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txt_Paperwidth.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="txt_Line_4.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Paperheight.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_QtyPrefix.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;barBtnSave.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_MarginBottom.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="txtCurrency.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="chk_4.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_1.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="lbl_Line_1.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlRight.Location" type="System.Drawing.Point, System.Drawing">
    <value>616, 31</value>
  </data>
  <data name="lbl_Line_1.AutoSizeMode" type="DevExpress.XtraEditors.LabelAutoSizeMode, DevExpress.XtraEditors.v15.1">
    <value>Vertical</value>
  </data>
  <data name="barButtonItem1.Caption" xml:space="preserve">
    <value>Help</value>
  </data>
  <data name="&gt;&gt;chk_1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Paperwidth.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_MarginRight.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_Line_1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="Lbl_Line_3.AutoSizeMode" type="DevExpress.XtraEditors.LabelAutoSizeMode, DevExpress.XtraEditors.v15.1">
    <value>Vertical</value>
  </data>
  <data name="labelControl28.Size" type="System.Drawing.Size, System.Drawing">
    <value>49, 13</value>
  </data>
  <data name="&gt;&gt;chk_4.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="lbl_line_2.AutoSizeMode" type="DevExpress.XtraEditors.LabelAutoSizeMode, DevExpress.XtraEditors.v15.1">
    <value>Vertical</value>
  </data>
  <data name="btnPrev.ToolTip" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="chk_1.Properties.Caption" xml:space="preserve">
    <value />
  </data>
  <data name="groupBox3.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="Lbl_Line_3.Text" xml:space="preserve">
    <value> </value>
  </data>
  <data name="txt_QtyPrefix.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl8.Text" xml:space="preserve">
    <value>Fourth</value>
  </data>
  <data name="txt_MarginBottom.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_MarginLeft.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;groupBox4.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="&gt;&gt;bar1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Bar, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="chk_PrintBatchNo.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="chklst_Line2.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl9.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="txt_MarginRight.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="Lbl_Line_3.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="txtRowsCount.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="chklst_Line2.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_BatchPrefix.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl28.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txt_MarginBottom.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 20</value>
  </data>
  <data name="txtCurrency.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="cb_Papers.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txt_MarginBottom.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl17.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="chkIsDefaultTemplate.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txt_BatchPrefix.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtRowsCount.Properties.Mask.EditMask" xml:space="preserve">
    <value>N00</value>
  </data>
  <data name="txt_BatchPrefix.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="txtColumnsCount.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_MarginTop.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl4.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="chklst_Line2.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="chk_ShowBacrodeText.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="chklst_Line3.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;btnPrev.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="btnNext.Location" type="System.Drawing.Point, System.Drawing">
    <value>34, 37</value>
  </data>
  <data name="lbl_Line_1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 3</value>
  </data>
  <data name="chklst_Line1.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_TemplateName.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="spn_4.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="Lbl_Line_3.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 54</value>
  </data>
  <data name="&gt;&gt;txt_MarginRight.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="txt_TemplateName.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_QtyPrefix.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;labelControl7.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl9.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl17.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Paperheight.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Line_4.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lbl_Line_4.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;chk_4.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="spn_4.Location" type="System.Drawing.Point, System.Drawing">
    <value>346, 127</value>
  </data>
  <data name="chk_ShowBacrodeText.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txt_QtyPrefix.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl15.Name" xml:space="preserve">
    <value>labelControl15</value>
  </data>
  <data name="txt_MarginLeft.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="chk_ShowBacrodeText.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl11.Name" xml:space="preserve">
    <value>labelControl11</value>
  </data>
  <data name="&gt;&gt;txt_Paperwidth.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="&gt;&gt;chk_2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txtColumnsCount.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl3.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="txtColumnsCount.Location" type="System.Drawing.Point, System.Drawing">
    <value>42, 14</value>
  </data>
  <data name="spn_3.Location" type="System.Drawing.Point, System.Drawing">
    <value>346, 98</value>
  </data>
  <data name="txt_MarginTop.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cb_Fonts.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;barDockControlTop.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="txt_MarginBottom.Location" type="System.Drawing.Point, System.Drawing">
    <value>160, 47</value>
  </data>
  <data name="labelControl11.Size" type="System.Drawing.Size, System.Drawing">
    <value>30, 26</value>
  </data>
  <data name="labelControl10.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="chk_ShowBacrodeText.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="spn_1.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtRowsCount.Size" type="System.Drawing.Size, System.Drawing">
    <value>42, 20</value>
  </data>
  <data name="labelControl20.Text" xml:space="preserve">
    <value>Right</value>
  </data>
  <data name="txtRowsCount.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtCurrency.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtRowsCount.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chklst_Line3.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl10.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barDockControlLeft.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="txt_MarginBottom.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_MarginRight.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtRowsCount.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl12.Text" xml:space="preserve">
    <value>Bottom</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;txt_BatchPrefix.Name" xml:space="preserve">
    <value>txt_BatchPrefix</value>
  </data>
  <data name="&gt;&gt;barAndDockingController1.Name" xml:space="preserve">
    <value>barAndDockingController1</value>
  </data>
  <data name="txt_Paperheight.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="groupBox1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="&gt;&gt;chk_PrintBatchNo.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barDockControlTop.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="Lbl_Line_3.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtCurrency.Size" type="System.Drawing.Size, System.Drawing">
    <value>61, 20</value>
  </data>
  <data name="txt_MarginBottom.Properties.Mask.EditMask" xml:space="preserve">
    <value>N00</value>
  </data>
  <data name="spn_4.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="lbl_Line_1.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="labelControl9.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_BatchPrefix.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Line_4.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_MarginLeft.Properties.Mask.EditMask" xml:space="preserve">
    <value>N00</value>
  </data>
  <data name="txt_MarginRight.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Paperheight.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="chklst_Line3.Location" type="System.Drawing.Point, System.Drawing">
    <value>387, 99</value>
  </data>
  <data name="labelControl14.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="groupBox3.Size" type="System.Drawing.Size, System.Drawing">
    <value>302, 74</value>
  </data>
  <data name="txt_QtyPrefix.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;panel2.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="barDockControlTop.Size" type="System.Drawing.Size, System.Drawing">
    <value>616, 31</value>
  </data>
  <data name="&gt;&gt;txtColumnsCount.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="labelControl27.TabIndex" type="System.Int32, mscorlib">
    <value>109</value>
  </data>
  <data name="chklst_Line2.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chklst_Line3.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_1.Location" type="System.Drawing.Point, System.Drawing">
    <value>315, 42</value>
  </data>
  <data name="chklst_Line1.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_MarginLeft.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl13.Parent" xml:space="preserve">
    <value>groupBox3</value>
  </data>
  <data name="chk_3.Location" type="System.Drawing.Point, System.Drawing">
    <value>315, 99</value>
  </data>
  <data name="spn_2.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="txt_MarginRight.Location" type="System.Drawing.Point, System.Drawing">
    <value>28, 18</value>
  </data>
  <data name="groupBox4.Size" type="System.Drawing.Size, System.Drawing">
    <value>270, 74</value>
  </data>
  <data name="spn_4.Size" type="System.Drawing.Size, System.Drawing">
    <value>37, 20</value>
  </data>
  <data name="&gt;&gt;barButtonItem1.Name" xml:space="preserve">
    <value>barButtonItem1</value>
  </data>
  <data name="cb_Fonts.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;labelControl36.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txt_Line_4.EditValue" xml:space="preserve">
    <value>Barcode</value>
  </data>
  <data name="&gt;&gt;groupBox3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl8.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="&gt;&gt;labelControl25.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="txt_MarginTop.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barDockControlLeft.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barDockControlTop.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl8.Name" xml:space="preserve">
    <value>labelControl8</value>
  </data>
  <data name="&gt;&gt;labelControl15.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;bar2.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Bar, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="chklst_Line2.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_TemplateName.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="spn_3.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chk_ShowBacrodeText.Size" type="System.Drawing.Size, System.Drawing">
    <value>158, 19</value>
  </data>
  <data name="&gt;&gt;labelControl19.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="&gt;&gt;chk_3.Name" xml:space="preserve">
    <value>chk_3</value>
  </data>
  <data name="lbl_line_2.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtCurrency.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_BatchPrefix.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txtRowsCount.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="barBtn_Preview.Caption" xml:space="preserve">
    <value>Preview</value>
  </data>
  <data name="&gt;&gt;txt_Line_4.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Paperwidth.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;chk_2.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="labelControl18.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="groupBox2.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 241</value>
  </data>
  <data name="groupBox5.Size" type="System.Drawing.Size, System.Drawing">
    <value>270, 39</value>
  </data>
  <data name="txt_Line_4.Location" type="System.Drawing.Point, System.Drawing">
    <value>387, 128</value>
  </data>
  <data name="spn_2.Size" type="System.Drawing.Size, System.Drawing">
    <value>37, 20</value>
  </data>
  <data name="lbl_Line_4.Appearance.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 12pt</value>
  </data>
  <data name="&gt;&gt;labelControl19.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="&gt;&gt;labelControl10.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;txt_MarginLeft.Name" xml:space="preserve">
    <value>txt_MarginLeft</value>
  </data>
  <data name="labelControl27.Location" type="System.Drawing.Point, System.Drawing">
    <value>380, 85</value>
  </data>
  <data name="lbl_Line_4.AutoSizeMode" type="DevExpress.XtraEditors.LabelAutoSizeMode, DevExpress.XtraEditors.v15.1">
    <value>Vertical</value>
  </data>
  <data name="labelControl7.Location" type="System.Drawing.Point, System.Drawing">
    <value>555, 102</value>
  </data>
  <data name="chk_PrintBatchNo.Properties.Caption" xml:space="preserve">
    <value>Add Operation Number in Barcode</value>
  </data>
  <data name="txtCurrency.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlBottom.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_BatchPrefix.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_Line_4.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="labelControl18.Size" type="System.Drawing.Size, System.Drawing">
    <value>55, 13</value>
  </data>
  <data name="&gt;&gt;Lbl_Line_3.Name" xml:space="preserve">
    <value>Lbl_Line_3</value>
  </data>
  <data name="&gt;&gt;labelControl20.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="labelControl36.Location" type="System.Drawing.Point, System.Drawing">
    <value>62, 40</value>
  </data>
  <data name="&gt;&gt;chklst_Line2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckedComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_Paperheight.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="&gt;&gt;spn_3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_QtyPrefix.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="chklst_Line2.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="spn_3.Size" type="System.Drawing.Size, System.Drawing">
    <value>37, 20</value>
  </data>
  <data name="txt_BatchPrefix.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_TemplateName.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_QtyPrefix.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;groupBox4.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;chklst_Line3.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="&gt;&gt;txt_Line_4.ZOrder" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="&gt;&gt;labelControl24.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl18.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_TemplateName.Size" type="System.Drawing.Size, System.Drawing">
    <value>218, 20</value>
  </data>
  <data name="txt_TemplateName.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Paperwidth.Location" type="System.Drawing.Point, System.Drawing">
    <value>29, 46</value>
  </data>
  <data name="&gt;&gt;labelControl12.Parent" xml:space="preserve">
    <value>groupBox4</value>
  </data>
  <data name="&gt;&gt;txtColumnsCount.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl15.Text" xml:space="preserve">
    <value>Row Count</value>
  </data>
  <data name="txtRowsCount.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barDockControlBottom.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;cb_Papers.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl16.Text" xml:space="preserve">
    <value>mm</value>
  </data>
  <data name="labelControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>555, 44</value>
  </data>
  <data name="txt_Paperheight.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl28.TabIndex" type="System.Int32, mscorlib">
    <value>111</value>
  </data>
  <data name="labelControl4.Location" type="System.Drawing.Point, System.Drawing">
    <value>555, 73</value>
  </data>
  <data name="txt_Paperwidth.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="barBtnSave.Caption" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="&gt;&gt;txt_Paperheight.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="spn_2.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="spn_3.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="cb_Papers.Size" type="System.Drawing.Size, System.Drawing">
    <value>207, 20</value>
  </data>
  <data name="&gt;&gt;groupBox5.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="chklst_Line3.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="labelControl22.Text" xml:space="preserve">
    <value>mm</value>
  </data>
  <data name="chk_4.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="txtColumnsCount.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl18.Text" xml:space="preserve">
    <value>Paper Type</value>
  </data>
  <data name="labelControl9.Size" type="System.Drawing.Size, System.Drawing">
    <value>77, 13</value>
  </data>
  <data name="spn_1.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Line_4.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl10.Text" xml:space="preserve">
    <value>Font
Size</value>
  </data>
  <data name="chk_PrintBatchNo.Location" type="System.Drawing.Point, System.Drawing">
    <value>82, 151</value>
  </data>
  <data name="&gt;&gt;labelControl3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="labelControl22.Location" type="System.Drawing.Point, System.Drawing">
    <value>141, 22</value>
  </data>
  <data name="&gt;&gt;txt_TemplateName.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="&gt;&gt;labelControl21.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="chklst_Line1.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="spn_4.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;cb_Papers.Name" xml:space="preserve">
    <value>cb_Papers</value>
  </data>
  <data name="&gt;&gt;groupBox5.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="labelControl25.TabIndex" type="System.Int32, mscorlib">
    <value>38</value>
  </data>
  <data name="spn_1.Size" type="System.Drawing.Size, System.Drawing">
    <value>37, 20</value>
  </data>
  <data name="chk_ShowBacrodeText.TabIndex" type="System.Int32, mscorlib">
    <value>101</value>
  </data>
  <data name="txt_Paperheight.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="chklst_Line1.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;btnPrev.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl21.Text" xml:space="preserve">
    <value>Left</value>
  </data>
  <data name="labelControl8.Size" type="System.Drawing.Size, System.Drawing">
    <value>32, 13</value>
  </data>
  <data name="chk_3.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="spn_1.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_MarginTop.Size" type="System.Drawing.Size, System.Drawing">
    <value>56, 20</value>
  </data>
  <data name="txtRowsCount.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="spn_4.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="spn_4.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl16.Name" xml:space="preserve">
    <value>labelControl16</value>
  </data>
  <data name="labelControl12.Location" type="System.Drawing.Point, System.Drawing">
    <value>220, 51</value>
  </data>
  <data name="spn_1.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;lbl_line_2.Name" xml:space="preserve">
    <value>lbl_line_2</value>
  </data>
  <data name="txt_MarginRight.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="chklst_Line3.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl12.Name" xml:space="preserve">
    <value>labelControl12</value>
  </data>
  <data name="spn_3.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_4.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="repositoryItemTextEdit1.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cb_Fonts.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl9.Location" type="System.Drawing.Point, System.Drawing">
    <value>410, 19</value>
  </data>
  <data name="labelControl20.TabIndex" type="System.Int32, mscorlib">
    <value>28</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="labelControl26.Location" type="System.Drawing.Point, System.Drawing">
    <value>221, 17</value>
  </data>
  <data name="&gt;&gt;labelControl27.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barButtonItem1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnSave.Name" xml:space="preserve">
    <value>barBtnSave</value>
  </data>
  <data name="spn_2.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Paperwidth.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="btnPrev.Text" xml:space="preserve">
    <value>&lt;=</value>
  </data>
  <data name="&gt;&gt;barBtn_Cancel.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Paperheight.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl21.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;panel2.Parent" xml:space="preserve">
    <value>groupBox2</value>
  </data>
  <data name="spn_3.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="labelControl20.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;chklst_Line3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckedComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtColumnsCount.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;labelControl26.Name" xml:space="preserve">
    <value>labelControl26</value>
  </data>
  <data name="labelControl22.TabIndex" type="System.Int32, mscorlib">
    <value>32</value>
  </data>
  <data name="labelControl36.Text" xml:space="preserve">
    <value>Templates</value>
  </data>
  <data name="spn_3.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="spn_4.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl5.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;txtCurrency.Name" xml:space="preserve">
    <value>txtCurrency</value>
  </data>
  <data name="spn_1.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="labelControl24.Location" type="System.Drawing.Point, System.Drawing">
    <value>11, 22</value>
  </data>
  <data name="txtColumnsCount.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barBtnNew.Caption" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="spn_1.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Paperheight.Location" type="System.Drawing.Point, System.Drawing">
    <value>179, 46</value>
  </data>
  <data name="chklst_Line2.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lbl_line_2.Parent" xml:space="preserve">
    <value>panel2</value>
  </data>
  <data name="txt_MarginLeft.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtColumnsCount.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;barBtnNew.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barDockControlLeft.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 31</value>
  </data>
  <data name="labelControl14.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="cb_Papers.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;txt_TemplateName.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="chk_4.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl9.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="txtColumnsCount.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="repositoryItemTextEdit1.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl23.TabIndex" type="System.Int32, mscorlib">
    <value>34</value>
  </data>
  <data name="txtCurrency.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_line_2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>61</value>
  </metadata>
  <metadata name="barAndDockingController1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>134, 17</value>
  </metadata>
  <metadata name="barManager1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
</root>