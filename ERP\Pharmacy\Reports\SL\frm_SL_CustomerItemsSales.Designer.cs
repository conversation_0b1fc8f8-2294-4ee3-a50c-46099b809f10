﻿namespace Reports
{
    partial class frm_SL_CustomerItemsSales
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_SL_CustomerItemsSales));
            this.barManager1 = new DevExpress.XtraBars.BarManager();
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barSubItem2 = new DevExpress.XtraBars.BarSubItem();
            this.barBtnPrint = new DevExpress.XtraBars.BarButtonItem();
            this.barbtnPrintP = new DevExpress.XtraBars.BarButtonItem();
            this.barSubItem1 = new DevExpress.XtraBars.BarSubItem();
            this.barBtnPreview = new DevExpress.XtraBars.BarButtonItem();
            this.barbtnPrint_P = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnRefresh = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.grdCategory = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip();
            this.mi_CopyRows = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_kg_Weight_libra = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_TotalSellValue = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_TotalSellPrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colSalesTax = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDiscountValue = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDiscountRatio3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDiscountRatio2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDiscountRatio = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_SellPrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_PiecesCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_UOM = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Qty = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ItemNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CusNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_InvoiceDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_InvoiceCode = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colItemDescription = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemMemoEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit();
            this.colItemDescriptionEn = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDriverName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colVehicleNumber = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDestination = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colSalesEmp = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_DicVal1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_DicVal2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_DicVal3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ItemCode1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ItemCode2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Serial = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Serial2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Batch = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CompanyNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Store = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_Stores1 = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView6 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_storeId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_NameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_NameEn = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_Process = new DevExpress.XtraEditors.Repository.RepositoryItemImageComboBox();
            this.col_CrncId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_Currency = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.col_CrncRate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_TotalSellPrice_Local = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CustomerCategory = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CustomerGroup = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colNotes = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colPaymentMethod = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repPaymentMethod = new DevExpress.XtraEditors.Repository.RepositoryItemImageComboBox();
            this.Col_ItemCategory = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_ItemCategory1 = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.repositoryItemGridLookUpEdit2View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_CategoryId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CategoryNameEn = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CategoryNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_UserName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_LibraQty = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_MtrxParent = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.repositoryItemGridLookUpEdit1View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_cat = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_comp = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_mtrx = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView4 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_Vendor = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView5 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.picLogo = new DevExpress.XtraEditors.PictureEdit();
            this.lblReportName = new DevExpress.XtraEditors.TextEdit();
            this.lblDateFilter = new DevExpress.XtraEditors.TextEdit();
            this.lblFilter = new DevExpress.XtraEditors.TextEdit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdCategory)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Stores1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Process)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Currency)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repPaymentMethod)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_ItemCategory1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit2View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_MtrxParent)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_cat)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_comp)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_mtrx)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Vendor)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblReportName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDateFilter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFilter.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnPreview,
            this.barBtnClose,
            this.barBtnPrint,
            this.barBtnRefresh,
            this.barSubItem1,
            this.barbtnPrint_P,
            this.barSubItem2,
            this.barbtnPrintP});
            this.barManager1.MaxItemId = 33;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(158, 122);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barSubItem2, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barSubItem1, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnRefresh),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barSubItem2
            // 
            resources.ApplyResources(this.barSubItem2, "barSubItem2");
            this.barSubItem2.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barSubItem2.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barSubItem2.Id = 31;
            this.barSubItem2.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barBtnPrint, DevExpress.XtraBars.BarItemPaintStyle.Caption),
            new DevExpress.XtraBars.LinkPersistInfo(this.barbtnPrintP)});
            this.barSubItem2.MenuAppearance.HeaderItemAppearance.FontSizeDelta = ((int)(resources.GetObject("barSubItem2.MenuAppearance.HeaderItemAppearance.FontSizeDelta")));
            this.barSubItem2.MenuAppearance.HeaderItemAppearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barSubItem2.MenuAppearance.HeaderItemAppearance.FontStyleDelta")));
            this.barSubItem2.MenuAppearance.HeaderItemAppearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barSubItem2.MenuAppearance.HeaderItemAppearance.GradientMode")));
            this.barSubItem2.MenuAppearance.HeaderItemAppearance.Image = ((System.Drawing.Image)(resources.GetObject("barSubItem2.MenuAppearance.HeaderItemAppearance.Image")));
            this.barSubItem2.Name = "barSubItem2";
            // 
            // barBtnPrint
            // 
            resources.ApplyResources(this.barBtnPrint, "barBtnPrint");
            this.barBtnPrint.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnPrint.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barBtnPrint.Id = 27;
            this.barBtnPrint.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.P));
            this.barBtnPrint.Name = "barBtnPrint";
            this.barBtnPrint.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnPrint.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnPrint_ItemClick);
            // 
            // barbtnPrintP
            // 
            resources.ApplyResources(this.barbtnPrintP, "barbtnPrintP");
            this.barbtnPrintP.Id = 32;
            this.barbtnPrintP.Name = "barbtnPrintP";
            this.barbtnPrintP.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barbtnPrintP_ItemClick);
            // 
            // barSubItem1
            // 
            resources.ApplyResources(this.barSubItem1, "barSubItem1");
            this.barSubItem1.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barSubItem1.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barSubItem1.Id = 29;
            this.barSubItem1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barBtnPreview, DevExpress.XtraBars.BarItemPaintStyle.Caption),
            new DevExpress.XtraBars.LinkPersistInfo(this.barbtnPrint_P)});
            this.barSubItem1.MenuAppearance.HeaderItemAppearance.FontSizeDelta = ((int)(resources.GetObject("barSubItem1.MenuAppearance.HeaderItemAppearance.FontSizeDelta")));
            this.barSubItem1.MenuAppearance.HeaderItemAppearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barSubItem1.MenuAppearance.HeaderItemAppearance.FontStyleDelta")));
            this.barSubItem1.MenuAppearance.HeaderItemAppearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barSubItem1.MenuAppearance.HeaderItemAppearance.GradientMode")));
            this.barSubItem1.MenuAppearance.HeaderItemAppearance.Image = ((System.Drawing.Image)(resources.GetObject("barSubItem1.MenuAppearance.HeaderItemAppearance.Image")));
            this.barSubItem1.Name = "barSubItem1";
            // 
            // barBtnPreview
            // 
            resources.ApplyResources(this.barBtnPreview, "barBtnPreview");
            this.barBtnPreview.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnPreview.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barBtnPreview.Id = 1;
            this.barBtnPreview.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.O));
            this.barBtnPreview.Name = "barBtnPreview";
            this.barBtnPreview.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnPreview.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Preview_ItemClick);
            // 
            // barbtnPrint_P
            // 
            resources.ApplyResources(this.barbtnPrint_P, "barbtnPrint_P");
            this.barbtnPrint_P.Id = 30;
            this.barbtnPrint_P.Name = "barbtnPrint_P";
            this.barbtnPrint_P.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barbtnPrint_P_ItemClick);
            // 
            // barBtnRefresh
            // 
            resources.ApplyResources(this.barBtnRefresh, "barBtnRefresh");
            this.barBtnRefresh.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnRefresh.Glyph = global::Pharmacy.Properties.Resources.refresh;
            this.barBtnRefresh.Id = 28;
            this.barBtnRefresh.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.R));
            this.barBtnRefresh.Name = "barBtnRefresh";
            this.barBtnRefresh.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnRefresh.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnRefresh_ItemClick);
            // 
            // barBtnClose
            // 
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 25;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Close_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlTop.Appearance.FontSizeDelta")));
            this.barDockControlTop.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlTop.Appearance.FontStyleDelta")));
            this.barDockControlTop.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlTop.Appearance.GradientMode")));
            this.barDockControlTop.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlTop.Appearance.Image")));
            this.barDockControlTop.CausesValidation = false;
            // 
            // barDockControlBottom
            // 
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlBottom.Appearance.FontSizeDelta")));
            this.barDockControlBottom.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlBottom.Appearance.FontStyleDelta")));
            this.barDockControlBottom.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlBottom.Appearance.GradientMode")));
            this.barDockControlBottom.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlBottom.Appearance.Image")));
            this.barDockControlBottom.CausesValidation = false;
            // 
            // barDockControlLeft
            // 
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlLeft.Appearance.FontSizeDelta")));
            this.barDockControlLeft.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlLeft.Appearance.FontStyleDelta")));
            this.barDockControlLeft.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlLeft.Appearance.GradientMode")));
            this.barDockControlLeft.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlLeft.Appearance.Image")));
            this.barDockControlLeft.CausesValidation = false;
            // 
            // barDockControlRight
            // 
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlRight.Appearance.FontSizeDelta")));
            this.barDockControlRight.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlRight.Appearance.FontStyleDelta")));
            this.barDockControlRight.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlRight.Appearance.GradientMode")));
            this.barDockControlRight.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlRight.Appearance.Image")));
            this.barDockControlRight.CausesValidation = false;
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemTextEdit1.Mask.AutoComplete")));
            this.repositoryItemTextEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.BeepOnError")));
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemTextEdit1.Mask.MaskType")));
            this.repositoryItemTextEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemTextEdit1.Mask.PlaceHolder")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // grdCategory
            // 
            resources.ApplyResources(this.grdCategory, "grdCategory");
            this.grdCategory.ContextMenuStrip = this.contextMenuStrip1;
            this.grdCategory.Cursor = System.Windows.Forms.Cursors.Default;
            this.grdCategory.EmbeddedNavigator.AccessibleDescription = resources.GetString("grdCategory.EmbeddedNavigator.AccessibleDescription");
            this.grdCategory.EmbeddedNavigator.AccessibleName = resources.GetString("grdCategory.EmbeddedNavigator.AccessibleName");
            this.grdCategory.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grdCategory.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grdCategory.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grdCategory.EmbeddedNavigator.Anchor")));
            this.grdCategory.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grdCategory.EmbeddedNavigator.BackgroundImage")));
            this.grdCategory.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grdCategory.EmbeddedNavigator.BackgroundImageLayout")));
            this.grdCategory.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grdCategory.EmbeddedNavigator.ImeMode")));
            this.grdCategory.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grdCategory.EmbeddedNavigator.MaximumSize")));
            this.grdCategory.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grdCategory.EmbeddedNavigator.TextLocation")));
            this.grdCategory.EmbeddedNavigator.ToolTip = resources.GetString("grdCategory.EmbeddedNavigator.ToolTip");
            this.grdCategory.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grdCategory.EmbeddedNavigator.ToolTipIconType")));
            this.grdCategory.EmbeddedNavigator.ToolTipTitle = resources.GetString("grdCategory.EmbeddedNavigator.ToolTipTitle");
            this.grdCategory.MainView = this.gridView1;
            this.grdCategory.Name = "grdCategory";
            this.grdCategory.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.rep_MtrxParent,
            this.rep_cat,
            this.rep_comp,
            this.rep_mtrx,
            this.rep_Vendor,
            this.repositoryItemMemoEdit1,
            this.rep_Process,
            this.rep_Currency,
            this.repPaymentMethod,
            this.rep_Stores1,
            this.rep_ItemCategory1});
            this.grdCategory.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // contextMenuStrip1
            // 
            resources.ApplyResources(this.contextMenuStrip1, "contextMenuStrip1");
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.mi_CopyRows});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            // 
            // mi_CopyRows
            // 
            resources.ApplyResources(this.mi_CopyRows, "mi_CopyRows");
            this.mi_CopyRows.Name = "mi_CopyRows";
            this.mi_CopyRows.Click += new System.EventHandler(this.mi_CopyRows_Click);
            // 
            // gridView1
            // 
            this.gridView1.Appearance.FooterPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.FooterPanel.FontSizeDelta")));
            this.gridView1.Appearance.FooterPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.FooterPanel.FontStyleDelta")));
            this.gridView1.Appearance.FooterPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.FooterPanel.GradientMode")));
            this.gridView1.Appearance.FooterPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.FooterPanel.Image")));
            this.gridView1.Appearance.FooterPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.GroupFooter.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.GroupFooter.FontSizeDelta")));
            this.gridView1.Appearance.GroupFooter.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.GroupFooter.FontStyleDelta")));
            this.gridView1.Appearance.GroupFooter.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.GroupFooter.GradientMode")));
            this.gridView1.Appearance.GroupFooter.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.GroupFooter.Image")));
            this.gridView1.Appearance.GroupFooter.Options.UseTextOptions = true;
            this.gridView1.Appearance.GroupFooter.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.GroupPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.GroupPanel.FontSizeDelta")));
            this.gridView1.Appearance.GroupPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.GroupPanel.FontStyleDelta")));
            this.gridView1.Appearance.GroupPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.GroupPanel.GradientMode")));
            this.gridView1.Appearance.GroupPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.GroupPanel.Image")));
            this.gridView1.Appearance.GroupPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.GroupPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.GroupRow.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.GroupRow.FontSizeDelta")));
            this.gridView1.Appearance.GroupRow.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.GroupRow.FontStyleDelta")));
            this.gridView1.Appearance.GroupRow.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.GroupRow.GradientMode")));
            this.gridView1.Appearance.GroupRow.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.GroupRow.Image")));
            this.gridView1.Appearance.GroupRow.Options.UseTextOptions = true;
            this.gridView1.Appearance.GroupRow.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.HeaderPanel.FontSizeDelta")));
            this.gridView1.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.HeaderPanel.FontStyleDelta")));
            this.gridView1.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.HeaderPanel.GradientMode")));
            this.gridView1.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.HeaderPanel.Image")));
            this.gridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.HeaderPanel.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridView1.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView1.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.Row.FontSizeDelta")));
            this.gridView1.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.Row.FontStyleDelta")));
            this.gridView1.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.Row.GradientMode")));
            this.gridView1.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.Row.Image")));
            this.gridView1.Appearance.Row.Options.UseTextOptions = true;
            this.gridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.Row.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridView1.Appearance.Row.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView1.AppearancePrint.FooterPanel.BackColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.BackColor")));
            this.gridView1.AppearancePrint.FooterPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.BorderColor")));
            this.gridView1.AppearancePrint.FooterPanel.Font = ((System.Drawing.Font)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.Font")));
            this.gridView1.AppearancePrint.FooterPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.FontSizeDelta")));
            this.gridView1.AppearancePrint.FooterPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.FontStyleDelta")));
            this.gridView1.AppearancePrint.FooterPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.ForeColor")));
            this.gridView1.AppearancePrint.FooterPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.GradientMode")));
            this.gridView1.AppearancePrint.FooterPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.Image")));
            this.gridView1.AppearancePrint.FooterPanel.Options.UseBackColor = true;
            this.gridView1.AppearancePrint.FooterPanel.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.FooterPanel.Options.UseFont = true;
            this.gridView1.AppearancePrint.FooterPanel.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.GroupFooter.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.BorderColor")));
            this.gridView1.AppearancePrint.GroupFooter.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.FontSizeDelta")));
            this.gridView1.AppearancePrint.GroupFooter.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.FontStyleDelta")));
            this.gridView1.AppearancePrint.GroupFooter.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.ForeColor")));
            this.gridView1.AppearancePrint.GroupFooter.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.GradientMode")));
            this.gridView1.AppearancePrint.GroupFooter.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.Image")));
            this.gridView1.AppearancePrint.GroupFooter.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.GroupFooter.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.GroupRow.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupRow.BorderColor")));
            this.gridView1.AppearancePrint.GroupRow.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.GroupRow.FontSizeDelta")));
            this.gridView1.AppearancePrint.GroupRow.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.GroupRow.FontStyleDelta")));
            this.gridView1.AppearancePrint.GroupRow.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupRow.ForeColor")));
            this.gridView1.AppearancePrint.GroupRow.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.GroupRow.GradientMode")));
            this.gridView1.AppearancePrint.GroupRow.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.GroupRow.Image")));
            this.gridView1.AppearancePrint.GroupRow.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.GroupRow.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.BackColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.BackColor")));
            this.gridView1.AppearancePrint.HeaderPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.BorderColor")));
            this.gridView1.AppearancePrint.HeaderPanel.Font = ((System.Drawing.Font)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.Font")));
            this.gridView1.AppearancePrint.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.FontSizeDelta")));
            this.gridView1.AppearancePrint.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.FontStyleDelta")));
            this.gridView1.AppearancePrint.HeaderPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.ForeColor")));
            this.gridView1.AppearancePrint.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.GradientMode")));
            this.gridView1.AppearancePrint.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.Image")));
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseBackColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseFont = true;
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.AppearancePrint.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.AppearancePrint.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView1.AppearancePrint.Lines.BackColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Lines.BackColor")));
            this.gridView1.AppearancePrint.Lines.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.Lines.FontSizeDelta")));
            this.gridView1.AppearancePrint.Lines.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.Lines.FontStyleDelta")));
            this.gridView1.AppearancePrint.Lines.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Lines.ForeColor")));
            this.gridView1.AppearancePrint.Lines.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.Lines.GradientMode")));
            this.gridView1.AppearancePrint.Lines.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.Lines.Image")));
            this.gridView1.AppearancePrint.Lines.Options.UseBackColor = true;
            this.gridView1.AppearancePrint.Lines.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.Row.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Row.BorderColor")));
            this.gridView1.AppearancePrint.Row.Font = ((System.Drawing.Font)(resources.GetObject("gridView1.AppearancePrint.Row.Font")));
            this.gridView1.AppearancePrint.Row.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.Row.FontSizeDelta")));
            this.gridView1.AppearancePrint.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.Row.FontStyleDelta")));
            this.gridView1.AppearancePrint.Row.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Row.ForeColor")));
            this.gridView1.AppearancePrint.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.Row.GradientMode")));
            this.gridView1.AppearancePrint.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.Row.Image")));
            this.gridView1.AppearancePrint.Row.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.Row.Options.UseFont = true;
            this.gridView1.AppearancePrint.Row.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.Row.Options.UseTextOptions = true;
            this.gridView1.AppearancePrint.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.gridView1, "gridView1");
            this.gridView1.ColumnPanelRowHeight = 50;
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_kg_Weight_libra,
            this.col_TotalSellValue,
            this.col_TotalSellPrice,
            this.colSalesTax,
            this.colDiscountValue,
            this.colDiscountRatio3,
            this.colDiscountRatio2,
            this.colDiscountRatio,
            this.col_SellPrice,
            this.col_PiecesCount,
            this.col_UOM,
            this.col_Qty,
            this.col_ItemNameAr,
            this.col_CusNameAr,
            this.col_InvoiceDate,
            this.col_InvoiceCode,
            this.colItemDescription,
            this.colItemDescriptionEn,
            this.colDriverName,
            this.colVehicleNumber,
            this.colDestination,
            this.colSalesEmp,
            this.col_DicVal1,
            this.col_DicVal2,
            this.col_DicVal3,
            this.col_ItemCode1,
            this.col_ItemCode2,
            this.col_Serial,
            this.col_Serial2,
            this.col_Batch,
            this.col_CompanyNameAr,
            this.col_Store,
            this.gridColumn11,
            this.col_CrncId,
            this.col_CrncRate,
            this.col_TotalSellPrice_Local,
            this.col_CustomerCategory,
            this.col_CustomerGroup,
            this.colNotes,
            this.colPaymentMethod,
            this.Col_ItemCategory,
            this.col_UserName,
            this.col_LibraQty,
            this.gridColumn12,
            this.gridColumn13});
            this.gridView1.CustomizationFormBounds = new System.Drawing.Rectangle(730, 326, 208, 308);
            this.gridView1.GridControl = this.grdCategory;
            this.gridView1.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary"))), resources.GetString("gridView1.GroupSummary1"), this.col_TotalSellValue, resources.GetString("gridView1.GroupSummary2")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary3"))), resources.GetString("gridView1.GroupSummary4"), this.col_TotalSellPrice, resources.GetString("gridView1.GroupSummary5")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary6"))), resources.GetString("gridView1.GroupSummary7"), this.colSalesTax, resources.GetString("gridView1.GroupSummary8")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary9"))), resources.GetString("gridView1.GroupSummary10"), this.colDiscountValue, resources.GetString("gridView1.GroupSummary11")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary12"))), resources.GetString("gridView1.GroupSummary13"), this.col_PiecesCount, resources.GetString("gridView1.GroupSummary14")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary15"))), resources.GetString("gridView1.GroupSummary16"), this.col_Qty, resources.GetString("gridView1.GroupSummary17")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary18"))), resources.GetString("gridView1.GroupSummary19"), this.col_DicVal1, resources.GetString("gridView1.GroupSummary20")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary21"))), resources.GetString("gridView1.GroupSummary22"), this.col_DicVal2, resources.GetString("gridView1.GroupSummary23")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary24"))), resources.GetString("gridView1.GroupSummary25"), this.col_DicVal3, resources.GetString("gridView1.GroupSummary26")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary27"))), resources.GetString("gridView1.GroupSummary28"), this.col_TotalSellPrice_Local, resources.GetString("gridView1.GroupSummary29"))});
            this.gridView1.HorzScrollStep = 2;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView1.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView1.OptionsBehavior.AutoExpandAllGroups = true;
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsPrint.ExpandAllGroups = false;
            this.gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView1.OptionsSelection.MultiSelect = true;
            this.gridView1.OptionsView.GroupFooterShowMode = DevExpress.XtraGrid.Views.Grid.GroupFooterShowMode.VisibleAlways;
            this.gridView1.OptionsView.RowAutoHeight = true;
            this.gridView1.OptionsView.ShowAutoFilterRow = true;
            this.gridView1.OptionsView.ShowFooter = true;
            this.gridView1.OptionsView.ShowIndicator = false;
            this.gridView1.CustomUnboundColumnData += new DevExpress.XtraGrid.Views.Base.CustomColumnDataEventHandler(this.gridView1_CustomUnboundColumnData);
            // 
            // col_kg_Weight_libra
            // 
            resources.ApplyResources(this.col_kg_Weight_libra, "col_kg_Weight_libra");
            this.col_kg_Weight_libra.FieldName = "kg_Weight_libra";
            this.col_kg_Weight_libra.Name = "col_kg_Weight_libra";
            this.col_kg_Weight_libra.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // col_TotalSellValue
            // 
            resources.ApplyResources(this.col_TotalSellValue, "col_TotalSellValue");
            this.col_TotalSellValue.DisplayFormat.FormatString = "n2";
            this.col_TotalSellValue.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_TotalSellValue.FieldName = "TotalSellValue";
            this.col_TotalSellValue.Name = "col_TotalSellValue";
            this.col_TotalSellValue.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col_TotalSellValue.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_TotalSellValue.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_TotalSellValue.Summary"))), resources.GetString("col_TotalSellValue.Summary1"), resources.GetString("col_TotalSellValue.Summary2"))});
            // 
            // col_TotalSellPrice
            // 
            resources.ApplyResources(this.col_TotalSellPrice, "col_TotalSellPrice");
            this.col_TotalSellPrice.DisplayFormat.FormatString = "n2";
            this.col_TotalSellPrice.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_TotalSellPrice.FieldName = "TotalSellPrice";
            this.col_TotalSellPrice.Name = "col_TotalSellPrice";
            this.col_TotalSellPrice.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col_TotalSellPrice.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_TotalSellPrice.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_TotalSellPrice.Summary"))), resources.GetString("col_TotalSellPrice.Summary1"), resources.GetString("col_TotalSellPrice.Summary2"))});
            // 
            // colSalesTax
            // 
            resources.ApplyResources(this.colSalesTax, "colSalesTax");
            this.colSalesTax.DisplayFormat.FormatString = "n2";
            this.colSalesTax.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colSalesTax.FieldName = "SalesTax";
            this.colSalesTax.Name = "colSalesTax";
            this.colSalesTax.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colSalesTax.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.colSalesTax.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("colSalesTax.Summary"))), resources.GetString("colSalesTax.Summary1"), resources.GetString("colSalesTax.Summary2"))});
            // 
            // colDiscountValue
            // 
            resources.ApplyResources(this.colDiscountValue, "colDiscountValue");
            this.colDiscountValue.DisplayFormat.FormatString = "n2";
            this.colDiscountValue.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colDiscountValue.FieldName = "DiscountValue";
            this.colDiscountValue.Name = "colDiscountValue";
            this.colDiscountValue.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colDiscountValue.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.colDiscountValue.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("colDiscountValue.Summary"))), resources.GetString("colDiscountValue.Summary1"), resources.GetString("colDiscountValue.Summary2"))});
            // 
            // colDiscountRatio3
            // 
            resources.ApplyResources(this.colDiscountRatio3, "colDiscountRatio3");
            this.colDiscountRatio3.DisplayFormat.FormatString = "p2";
            this.colDiscountRatio3.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colDiscountRatio3.FieldName = "DiscountRatio3";
            this.colDiscountRatio3.Name = "colDiscountRatio3";
            this.colDiscountRatio3.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colDiscountRatio3.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // colDiscountRatio2
            // 
            resources.ApplyResources(this.colDiscountRatio2, "colDiscountRatio2");
            this.colDiscountRatio2.DisplayFormat.FormatString = "p2";
            this.colDiscountRatio2.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colDiscountRatio2.FieldName = "DiscountRatio2";
            this.colDiscountRatio2.Name = "colDiscountRatio2";
            this.colDiscountRatio2.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colDiscountRatio2.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // colDiscountRatio
            // 
            resources.ApplyResources(this.colDiscountRatio, "colDiscountRatio");
            this.colDiscountRatio.DisplayFormat.FormatString = "p2";
            this.colDiscountRatio.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colDiscountRatio.FieldName = "DiscountRatio";
            this.colDiscountRatio.Name = "colDiscountRatio";
            this.colDiscountRatio.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colDiscountRatio.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // col_SellPrice
            // 
            resources.ApplyResources(this.col_SellPrice, "col_SellPrice");
            this.col_SellPrice.DisplayFormat.FormatString = "n2";
            this.col_SellPrice.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_SellPrice.FieldName = "SellPrice";
            this.col_SellPrice.Name = "col_SellPrice";
            this.col_SellPrice.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col_SellPrice.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // col_PiecesCount
            // 
            resources.ApplyResources(this.col_PiecesCount, "col_PiecesCount");
            this.col_PiecesCount.DisplayFormat.FormatString = "n2";
            this.col_PiecesCount.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_PiecesCount.FieldName = "PiecesCount";
            this.col_PiecesCount.Name = "col_PiecesCount";
            this.col_PiecesCount.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col_PiecesCount.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_PiecesCount.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_PiecesCount.Summary"))), resources.GetString("col_PiecesCount.Summary1"), resources.GetString("col_PiecesCount.Summary2"))});
            // 
            // col_UOM
            // 
            this.col_UOM.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_UOM.AppearanceCell.FontSizeDelta")));
            this.col_UOM.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_UOM.AppearanceCell.FontStyleDelta")));
            this.col_UOM.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_UOM.AppearanceCell.GradientMode")));
            this.col_UOM.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_UOM.AppearanceCell.Image")));
            this.col_UOM.AppearanceCell.Options.UseTextOptions = true;
            this.col_UOM.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.col_UOM, "col_UOM");
            this.col_UOM.FieldName = "UOM";
            this.col_UOM.Name = "col_UOM";
            this.col_UOM.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col_UOM.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // col_Qty
            // 
            resources.ApplyResources(this.col_Qty, "col_Qty");
            this.col_Qty.DisplayFormat.FormatString = "n3";
            this.col_Qty.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_Qty.FieldName = "Qty";
            this.col_Qty.Name = "col_Qty";
            this.col_Qty.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col_Qty.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_Qty.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_Qty.Summary"))), resources.GetString("col_Qty.Summary1"), resources.GetString("col_Qty.Summary2"))});
            // 
            // col_ItemNameAr
            // 
            resources.ApplyResources(this.col_ItemNameAr, "col_ItemNameAr");
            this.col_ItemNameAr.FieldName = "ItemNameAr";
            this.col_ItemNameAr.Name = "col_ItemNameAr";
            this.col_ItemNameAr.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col_ItemNameAr.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            this.col_ItemNameAr.OptionsFilter.FilterPopupMode = DevExpress.XtraGrid.Columns.FilterPopupMode.CheckedList;
            // 
            // col_CusNameAr
            // 
            resources.ApplyResources(this.col_CusNameAr, "col_CusNameAr");
            this.col_CusNameAr.FieldName = "CusNameAr";
            this.col_CusNameAr.Name = "col_CusNameAr";
            this.col_CusNameAr.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col_CusNameAr.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // col_InvoiceDate
            // 
            resources.ApplyResources(this.col_InvoiceDate, "col_InvoiceDate");
            this.col_InvoiceDate.FieldName = "InvoiceDate";
            this.col_InvoiceDate.Name = "col_InvoiceDate";
            this.col_InvoiceDate.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.col_InvoiceDate.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // col_InvoiceCode
            // 
            resources.ApplyResources(this.col_InvoiceCode, "col_InvoiceCode");
            this.col_InvoiceCode.FieldName = "InvoiceCode";
            this.col_InvoiceCode.Name = "col_InvoiceCode";
            this.col_InvoiceCode.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.True;
            this.col_InvoiceCode.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // colItemDescription
            // 
            resources.ApplyResources(this.colItemDescription, "colItemDescription");
            this.colItemDescription.ColumnEdit = this.repositoryItemMemoEdit1;
            this.colItemDescription.FieldName = "ItemDescription";
            this.colItemDescription.Name = "colItemDescription";
            this.colItemDescription.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colItemDescription.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // repositoryItemMemoEdit1
            // 
            resources.ApplyResources(this.repositoryItemMemoEdit1, "repositoryItemMemoEdit1");
            this.repositoryItemMemoEdit1.Name = "repositoryItemMemoEdit1";
            // 
            // colItemDescriptionEn
            // 
            resources.ApplyResources(this.colItemDescriptionEn, "colItemDescriptionEn");
            this.colItemDescriptionEn.ColumnEdit = this.repositoryItemMemoEdit1;
            this.colItemDescriptionEn.FieldName = "ItemDescriptionEn";
            this.colItemDescriptionEn.Name = "colItemDescriptionEn";
            this.colItemDescriptionEn.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colItemDescriptionEn.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // colDriverName
            // 
            resources.ApplyResources(this.colDriverName, "colDriverName");
            this.colDriverName.FieldName = "DriverName";
            this.colDriverName.Name = "colDriverName";
            this.colDriverName.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colDriverName.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // colVehicleNumber
            // 
            resources.ApplyResources(this.colVehicleNumber, "colVehicleNumber");
            this.colVehicleNumber.FieldName = "VehicleNumber";
            this.colVehicleNumber.Name = "colVehicleNumber";
            this.colVehicleNumber.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colVehicleNumber.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // colDestination
            // 
            resources.ApplyResources(this.colDestination, "colDestination");
            this.colDestination.FieldName = "Destination";
            this.colDestination.Name = "colDestination";
            this.colDestination.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            this.colDestination.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // colSalesEmp
            // 
            resources.ApplyResources(this.colSalesEmp, "colSalesEmp");
            this.colSalesEmp.FieldName = "SalesEmp";
            this.colSalesEmp.Name = "colSalesEmp";
            this.colSalesEmp.OptionsColumn.AllowMerge = DevExpress.Utils.DefaultBoolean.False;
            // 
            // col_DicVal1
            // 
            resources.ApplyResources(this.col_DicVal1, "col_DicVal1");
            this.col_DicVal1.DisplayFormat.FormatString = "n3";
            this.col_DicVal1.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_DicVal1.FieldName = "DicVal1";
            this.col_DicVal1.Name = "col_DicVal1";
            this.col_DicVal1.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_DicVal1.Summary"))), resources.GetString("col_DicVal1.Summary1"), resources.GetString("col_DicVal1.Summary2"))});
            // 
            // col_DicVal2
            // 
            resources.ApplyResources(this.col_DicVal2, "col_DicVal2");
            this.col_DicVal2.DisplayFormat.FormatString = "n3";
            this.col_DicVal2.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_DicVal2.FieldName = "DicVal2";
            this.col_DicVal2.Name = "col_DicVal2";
            this.col_DicVal2.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_DicVal2.Summary"))), resources.GetString("col_DicVal2.Summary1"), resources.GetString("col_DicVal2.Summary2"))});
            // 
            // col_DicVal3
            // 
            resources.ApplyResources(this.col_DicVal3, "col_DicVal3");
            this.col_DicVal3.DisplayFormat.FormatString = "n3";
            this.col_DicVal3.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_DicVal3.FieldName = "DicVal3";
            this.col_DicVal3.Name = "col_DicVal3";
            this.col_DicVal3.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_DicVal3.Summary"))), resources.GetString("col_DicVal3.Summary1"), resources.GetString("col_DicVal3.Summary2"))});
            // 
            // col_ItemCode1
            // 
            resources.ApplyResources(this.col_ItemCode1, "col_ItemCode1");
            this.col_ItemCode1.FieldName = "ItemCode1";
            this.col_ItemCode1.Name = "col_ItemCode1";
            // 
            // col_ItemCode2
            // 
            resources.ApplyResources(this.col_ItemCode2, "col_ItemCode2");
            this.col_ItemCode2.FieldName = "ItemCode2";
            this.col_ItemCode2.Name = "col_ItemCode2";
            // 
            // col_Serial
            // 
            resources.ApplyResources(this.col_Serial, "col_Serial");
            this.col_Serial.FieldName = "Serial";
            this.col_Serial.Name = "col_Serial";
            // 
            // col_Serial2
            // 
            this.col_Serial2.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_Serial2.AppearanceCell.FontSizeDelta")));
            this.col_Serial2.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_Serial2.AppearanceCell.FontStyleDelta")));
            this.col_Serial2.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_Serial2.AppearanceCell.GradientMode")));
            this.col_Serial2.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_Serial2.AppearanceCell.Image")));
            this.col_Serial2.AppearanceCell.Options.UseTextOptions = true;
            this.col_Serial2.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Serial2.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_Serial2.AppearanceHeader.FontSizeDelta")));
            this.col_Serial2.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_Serial2.AppearanceHeader.FontStyleDelta")));
            this.col_Serial2.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_Serial2.AppearanceHeader.GradientMode")));
            this.col_Serial2.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_Serial2.AppearanceHeader.Image")));
            this.col_Serial2.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Serial2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.col_Serial2, "col_Serial2");
            this.col_Serial2.FieldName = "Serial2";
            this.col_Serial2.Name = "col_Serial2";
            // 
            // col_Batch
            // 
            resources.ApplyResources(this.col_Batch, "col_Batch");
            this.col_Batch.FieldName = "Batch";
            this.col_Batch.Name = "col_Batch";
            // 
            // col_CompanyNameAr
            // 
            resources.ApplyResources(this.col_CompanyNameAr, "col_CompanyNameAr");
            this.col_CompanyNameAr.FieldName = "CompanyNameAr";
            this.col_CompanyNameAr.Name = "col_CompanyNameAr";
            this.col_CompanyNameAr.OptionsFilter.FilterPopupMode = DevExpress.XtraGrid.Columns.FilterPopupMode.CheckedList;
            // 
            // col_Store
            // 
            resources.ApplyResources(this.col_Store, "col_Store");
            this.col_Store.ColumnEdit = this.rep_Stores1;
            this.col_Store.FieldName = "Store";
            this.col_Store.Name = "col_Store";
            this.col_Store.OptionsFilter.FilterPopupMode = DevExpress.XtraGrid.Columns.FilterPopupMode.CheckedList;
            // 
            // rep_Stores1
            // 
            resources.ApplyResources(this.rep_Stores1, "rep_Stores1");
            this.rep_Stores1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Stores1.Buttons"))))});
            this.rep_Stores1.Name = "rep_Stores1";
            this.rep_Stores1.View = this.gridView6;
            // 
            // gridView6
            // 
            resources.ApplyResources(this.gridView6, "gridView6");
            this.gridView6.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_storeId,
            this.col_NameAr,
            this.col_NameEn});
            this.gridView6.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView6.Name = "gridView6";
            this.gridView6.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView6.OptionsView.ShowGroupPanel = false;
            // 
            // col_storeId
            // 
            resources.ApplyResources(this.col_storeId, "col_storeId");
            this.col_storeId.FieldName = "StoreId";
            this.col_storeId.Name = "col_storeId";
            // 
            // col_NameAr
            // 
            resources.ApplyResources(this.col_NameAr, "col_NameAr");
            this.col_NameAr.FieldName = "StoreNameAr";
            this.col_NameAr.Name = "col_NameAr";
            // 
            // col_NameEn
            // 
            resources.ApplyResources(this.col_NameEn, "col_NameEn");
            this.col_NameEn.FieldName = "StoreNameEn";
            this.col_NameEn.Name = "col_NameEn";
            // 
            // gridColumn11
            // 
            resources.ApplyResources(this.gridColumn11, "gridColumn11");
            this.gridColumn11.ColumnEdit = this.rep_Process;
            this.gridColumn11.FieldName = "ProcessId";
            this.gridColumn11.Name = "gridColumn11";
            // 
            // rep_Process
            // 
            resources.ApplyResources(this.rep_Process, "rep_Process");
            this.rep_Process.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Process.Buttons"))))});
            this.rep_Process.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("rep_Process.Items"), ((object)(resources.GetObject("rep_Process.Items1"))), ((int)(resources.GetObject("rep_Process.Items2")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("rep_Process.Items3"), ((object)(resources.GetObject("rep_Process.Items4"))), ((int)(resources.GetObject("rep_Process.Items5")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("rep_Process.Items6"), ((object)(resources.GetObject("rep_Process.Items7"))), ((int)(resources.GetObject("rep_Process.Items8")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("rep_Process.Items9"), ((object)(resources.GetObject("rep_Process.Items10"))), ((int)(resources.GetObject("rep_Process.Items11")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("rep_Process.Items12"), ((object)(resources.GetObject("rep_Process.Items13"))), ((int)(resources.GetObject("rep_Process.Items14")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("rep_Process.Items15"), ((object)(resources.GetObject("rep_Process.Items16"))), ((int)(resources.GetObject("rep_Process.Items17"))))});
            this.rep_Process.Name = "rep_Process";
            // 
            // col_CrncId
            // 
            resources.ApplyResources(this.col_CrncId, "col_CrncId");
            this.col_CrncId.ColumnEdit = this.rep_Currency;
            this.col_CrncId.FieldName = "CrncId";
            this.col_CrncId.Name = "col_CrncId";
            // 
            // rep_Currency
            // 
            resources.ApplyResources(this.rep_Currency, "rep_Currency");
            this.rep_Currency.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Currency.Buttons"))))});
            this.rep_Currency.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_Currency.Columns"), resources.GetString("rep_Currency.Columns1")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_Currency.Columns2"), resources.GetString("rep_Currency.Columns3"), ((int)(resources.GetObject("rep_Currency.Columns4"))), ((DevExpress.Utils.FormatType)(resources.GetObject("rep_Currency.Columns5"))), resources.GetString("rep_Currency.Columns6"), ((bool)(resources.GetObject("rep_Currency.Columns7"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("rep_Currency.Columns8"))))});
            this.rep_Currency.Name = "rep_Currency";
            // 
            // col_CrncRate
            // 
            resources.ApplyResources(this.col_CrncRate, "col_CrncRate");
            this.col_CrncRate.DisplayFormat.FormatString = "n3";
            this.col_CrncRate.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_CrncRate.FieldName = "CrncRate";
            this.col_CrncRate.Name = "col_CrncRate";
            // 
            // col_TotalSellPrice_Local
            // 
            resources.ApplyResources(this.col_TotalSellPrice_Local, "col_TotalSellPrice_Local");
            this.col_TotalSellPrice_Local.DisplayFormat.FormatString = "n3";
            this.col_TotalSellPrice_Local.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_TotalSellPrice_Local.FieldName = "TotalSellPrice_Local";
            this.col_TotalSellPrice_Local.Name = "col_TotalSellPrice_Local";
            this.col_TotalSellPrice_Local.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_TotalSellPrice_Local.Summary"))), resources.GetString("col_TotalSellPrice_Local.Summary1"), resources.GetString("col_TotalSellPrice_Local.Summary2"))});
            // 
            // col_CustomerCategory
            // 
            resources.ApplyResources(this.col_CustomerCategory, "col_CustomerCategory");
            this.col_CustomerCategory.FieldName = "CustCat";
            this.col_CustomerCategory.Name = "col_CustomerCategory";
            this.col_CustomerCategory.OptionsFilter.FilterPopupMode = DevExpress.XtraGrid.Columns.FilterPopupMode.CheckedList;
            // 
            // col_CustomerGroup
            // 
            resources.ApplyResources(this.col_CustomerGroup, "col_CustomerGroup");
            this.col_CustomerGroup.FieldName = "CustGroup";
            this.col_CustomerGroup.Name = "col_CustomerGroup";
            this.col_CustomerGroup.OptionsFilter.FilterPopupMode = DevExpress.XtraGrid.Columns.FilterPopupMode.CheckedList;
            // 
            // colNotes
            // 
            resources.ApplyResources(this.colNotes, "colNotes");
            this.colNotes.FieldName = "Notes";
            this.colNotes.Name = "colNotes";
            // 
            // colPaymentMethod
            // 
            resources.ApplyResources(this.colPaymentMethod, "colPaymentMethod");
            this.colPaymentMethod.ColumnEdit = this.repPaymentMethod;
            this.colPaymentMethod.FieldName = "PaymentMethod";
            this.colPaymentMethod.Name = "colPaymentMethod";
            // 
            // repPaymentMethod
            // 
            resources.ApplyResources(this.repPaymentMethod, "repPaymentMethod");
            this.repPaymentMethod.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repPaymentMethod.Buttons"))))});
            this.repPaymentMethod.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("repPaymentMethod.Items"), ((object)(resources.GetObject("repPaymentMethod.Items1"))), ((int)(resources.GetObject("repPaymentMethod.Items2")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("repPaymentMethod.Items3"), ((object)(resources.GetObject("repPaymentMethod.Items4"))), ((int)(resources.GetObject("repPaymentMethod.Items5")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("repPaymentMethod.Items6"), ((object)(resources.GetObject("repPaymentMethod.Items7"))), ((int)(resources.GetObject("repPaymentMethod.Items8"))))});
            this.repPaymentMethod.Name = "repPaymentMethod";
            // 
            // Col_ItemCategory
            // 
            resources.ApplyResources(this.Col_ItemCategory, "Col_ItemCategory");
            this.Col_ItemCategory.ColumnEdit = this.rep_ItemCategory1;
            this.Col_ItemCategory.FieldName = "Category";
            this.Col_ItemCategory.Name = "Col_ItemCategory";
            this.Col_ItemCategory.OptionsFilter.FilterPopupMode = DevExpress.XtraGrid.Columns.FilterPopupMode.CheckedList;
            // 
            // rep_ItemCategory1
            // 
            resources.ApplyResources(this.rep_ItemCategory1, "rep_ItemCategory1");
            this.rep_ItemCategory1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_ItemCategory1.Buttons"))))});
            this.rep_ItemCategory1.Name = "rep_ItemCategory1";
            this.rep_ItemCategory1.View = this.repositoryItemGridLookUpEdit2View;
            // 
            // repositoryItemGridLookUpEdit2View
            // 
            resources.ApplyResources(this.repositoryItemGridLookUpEdit2View, "repositoryItemGridLookUpEdit2View");
            this.repositoryItemGridLookUpEdit2View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_CategoryId,
            this.col_CategoryNameEn,
            this.col_CategoryNameAr});
            this.repositoryItemGridLookUpEdit2View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.repositoryItemGridLookUpEdit2View.Name = "repositoryItemGridLookUpEdit2View";
            this.repositoryItemGridLookUpEdit2View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.repositoryItemGridLookUpEdit2View.OptionsView.ShowGroupPanel = false;
            // 
            // col_CategoryId
            // 
            resources.ApplyResources(this.col_CategoryId, "col_CategoryId");
            this.col_CategoryId.FieldName = "CategoryId";
            this.col_CategoryId.Name = "col_CategoryId";
            // 
            // col_CategoryNameEn
            // 
            resources.ApplyResources(this.col_CategoryNameEn, "col_CategoryNameEn");
            this.col_CategoryNameEn.FieldName = "CategoryNameEn";
            this.col_CategoryNameEn.Name = "col_CategoryNameEn";
            // 
            // col_CategoryNameAr
            // 
            resources.ApplyResources(this.col_CategoryNameAr, "col_CategoryNameAr");
            this.col_CategoryNameAr.FieldName = "CategoryNameAr";
            this.col_CategoryNameAr.Name = "col_CategoryNameAr";
            // 
            // col_UserName
            // 
            resources.ApplyResources(this.col_UserName, "col_UserName");
            this.col_UserName.FieldName = "UserName";
            this.col_UserName.Name = "col_UserName";
            // 
            // col_LibraQty
            // 
            resources.ApplyResources(this.col_LibraQty, "col_LibraQty");
            this.col_LibraQty.FieldName = "LibraQty";
            this.col_LibraQty.Name = "col_LibraQty";
            this.col_LibraQty.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // gridColumn12
            // 
            resources.ApplyResources(this.gridColumn12, "gridColumn12");
            this.gridColumn12.FieldName = "City";
            this.gridColumn12.Name = "gridColumn12";
            this.gridColumn12.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // gridColumn13
            // 
            resources.ApplyResources(this.gridColumn13, "gridColumn13");
            this.gridColumn13.FieldName = "Area";
            this.gridColumn13.Name = "gridColumn13";
            this.gridColumn13.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // rep_MtrxParent
            // 
            resources.ApplyResources(this.rep_MtrxParent, "rep_MtrxParent");
            this.rep_MtrxParent.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_MtrxParent.Buttons"))))});
            this.rep_MtrxParent.Name = "rep_MtrxParent";
            this.rep_MtrxParent.View = this.repositoryItemGridLookUpEdit1View;
            // 
            // repositoryItemGridLookUpEdit1View
            // 
            resources.ApplyResources(this.repositoryItemGridLookUpEdit1View, "repositoryItemGridLookUpEdit1View");
            this.repositoryItemGridLookUpEdit1View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2});
            this.repositoryItemGridLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.repositoryItemGridLookUpEdit1View.Name = "repositoryItemGridLookUpEdit1View";
            this.repositoryItemGridLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            resources.ApplyResources(this.gridColumn1, "gridColumn1");
            this.gridColumn1.FieldName = "ItemNameAr";
            this.gridColumn1.Name = "gridColumn1";
            // 
            // gridColumn2
            // 
            resources.ApplyResources(this.gridColumn2, "gridColumn2");
            this.gridColumn2.FieldName = "ItemId";
            this.gridColumn2.Name = "gridColumn2";
            // 
            // rep_cat
            // 
            resources.ApplyResources(this.rep_cat, "rep_cat");
            this.rep_cat.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_cat.Buttons"))))});
            this.rep_cat.Name = "rep_cat";
            this.rep_cat.View = this.gridView2;
            // 
            // gridView2
            // 
            resources.ApplyResources(this.gridView2, "gridView2");
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn3,
            this.gridColumn4});
            this.gridView2.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn3
            // 
            resources.ApplyResources(this.gridColumn3, "gridColumn3");
            this.gridColumn3.FieldName = "CategoryNameAr";
            this.gridColumn3.Name = "gridColumn3";
            // 
            // gridColumn4
            // 
            resources.ApplyResources(this.gridColumn4, "gridColumn4");
            this.gridColumn4.FieldName = "CategoryId";
            this.gridColumn4.Name = "gridColumn4";
            // 
            // rep_comp
            // 
            resources.ApplyResources(this.rep_comp, "rep_comp");
            this.rep_comp.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_comp.Buttons"))))});
            this.rep_comp.Name = "rep_comp";
            this.rep_comp.View = this.gridView3;
            // 
            // gridView3
            // 
            resources.ApplyResources(this.gridView3, "gridView3");
            this.gridView3.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn5,
            this.gridColumn6});
            this.gridView3.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn5
            // 
            resources.ApplyResources(this.gridColumn5, "gridColumn5");
            this.gridColumn5.FieldName = "CompanyNameAr";
            this.gridColumn5.Name = "gridColumn5";
            // 
            // gridColumn6
            // 
            resources.ApplyResources(this.gridColumn6, "gridColumn6");
            this.gridColumn6.FieldName = "CompanyId";
            this.gridColumn6.Name = "gridColumn6";
            // 
            // rep_mtrx
            // 
            resources.ApplyResources(this.rep_mtrx, "rep_mtrx");
            this.rep_mtrx.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_mtrx.Buttons"))))});
            this.rep_mtrx.Name = "rep_mtrx";
            this.rep_mtrx.ShowDropDown = DevExpress.XtraEditors.Controls.ShowDropDown.Never;
            this.rep_mtrx.View = this.gridView4;
            // 
            // gridView4
            // 
            resources.ApplyResources(this.gridView4, "gridView4");
            this.gridView4.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn7,
            this.gridColumn8});
            this.gridView4.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView4.Name = "gridView4";
            this.gridView4.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView4.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn7
            // 
            resources.ApplyResources(this.gridColumn7, "gridColumn7");
            this.gridColumn7.FieldName = "MDName";
            this.gridColumn7.Name = "gridColumn7";
            // 
            // gridColumn8
            // 
            resources.ApplyResources(this.gridColumn8, "gridColumn8");
            this.gridColumn8.FieldName = "MatrixDetailId";
            this.gridColumn8.Name = "gridColumn8";
            // 
            // rep_Vendor
            // 
            resources.ApplyResources(this.rep_Vendor, "rep_Vendor");
            this.rep_Vendor.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Vendor.Buttons"))))});
            this.rep_Vendor.Name = "rep_Vendor";
            this.rep_Vendor.View = this.gridView5;
            // 
            // gridView5
            // 
            resources.ApplyResources(this.gridView5, "gridView5");
            this.gridView5.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn9,
            this.gridColumn10});
            this.gridView5.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView5.Name = "gridView5";
            this.gridView5.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView5.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn9
            // 
            resources.ApplyResources(this.gridColumn9, "gridColumn9");
            this.gridColumn9.FieldName = "VenNameAr";
            this.gridColumn9.Name = "gridColumn9";
            // 
            // gridColumn10
            // 
            resources.ApplyResources(this.gridColumn10, "gridColumn10");
            this.gridColumn10.FieldName = "VendorId";
            this.gridColumn10.Name = "gridColumn10";
            // 
            // picLogo
            // 
            resources.ApplyResources(this.picLogo, "picLogo");
            this.picLogo.MenuManager = this.barManager1;
            this.picLogo.Name = "picLogo";
            this.picLogo.Properties.AccessibleDescription = resources.GetString("picLogo.Properties.AccessibleDescription");
            this.picLogo.Properties.AccessibleName = resources.GetString("picLogo.Properties.AccessibleName");
            this.picLogo.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Stretch;
            // 
            // lblReportName
            // 
            resources.ApplyResources(this.lblReportName, "lblReportName");
            this.lblReportName.MenuManager = this.barManager1;
            this.lblReportName.Name = "lblReportName";
            this.lblReportName.Properties.AccessibleDescription = resources.GetString("lblReportName.Properties.AccessibleDescription");
            this.lblReportName.Properties.AccessibleName = resources.GetString("lblReportName.Properties.AccessibleName");
            this.lblReportName.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("lblReportName.Properties.Appearance.Font")));
            this.lblReportName.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lblReportName.Properties.Appearance.FontSizeDelta")));
            this.lblReportName.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lblReportName.Properties.Appearance.FontStyleDelta")));
            this.lblReportName.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lblReportName.Properties.Appearance.GradientMode")));
            this.lblReportName.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lblReportName.Properties.Appearance.Image")));
            this.lblReportName.Properties.Appearance.Options.UseFont = true;
            this.lblReportName.Properties.Appearance.Options.UseTextOptions = true;
            this.lblReportName.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblReportName.Properties.AutoHeight = ((bool)(resources.GetObject("lblReportName.Properties.AutoHeight")));
            this.lblReportName.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("lblReportName.Properties.Mask.AutoComplete")));
            this.lblReportName.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("lblReportName.Properties.Mask.BeepOnError")));
            this.lblReportName.Properties.Mask.EditMask = resources.GetString("lblReportName.Properties.Mask.EditMask");
            this.lblReportName.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("lblReportName.Properties.Mask.IgnoreMaskBlank")));
            this.lblReportName.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("lblReportName.Properties.Mask.MaskType")));
            this.lblReportName.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("lblReportName.Properties.Mask.PlaceHolder")));
            this.lblReportName.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("lblReportName.Properties.Mask.SaveLiteral")));
            this.lblReportName.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("lblReportName.Properties.Mask.ShowPlaceHolders")));
            this.lblReportName.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("lblReportName.Properties.Mask.UseMaskAsDisplayFormat")));
            this.lblReportName.Properties.NullValuePrompt = resources.GetString("lblReportName.Properties.NullValuePrompt");
            this.lblReportName.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lblReportName.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // lblDateFilter
            // 
            resources.ApplyResources(this.lblDateFilter, "lblDateFilter");
            this.lblDateFilter.MenuManager = this.barManager1;
            this.lblDateFilter.Name = "lblDateFilter";
            this.lblDateFilter.Properties.AccessibleDescription = resources.GetString("lblDateFilter.Properties.AccessibleDescription");
            this.lblDateFilter.Properties.AccessibleName = resources.GetString("lblDateFilter.Properties.AccessibleName");
            this.lblDateFilter.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lblDateFilter.Properties.Appearance.FontSizeDelta")));
            this.lblDateFilter.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lblDateFilter.Properties.Appearance.FontStyleDelta")));
            this.lblDateFilter.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lblDateFilter.Properties.Appearance.GradientMode")));
            this.lblDateFilter.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lblDateFilter.Properties.Appearance.Image")));
            this.lblDateFilter.Properties.Appearance.Options.UseTextOptions = true;
            this.lblDateFilter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblDateFilter.Properties.AutoHeight = ((bool)(resources.GetObject("lblDateFilter.Properties.AutoHeight")));
            this.lblDateFilter.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("lblDateFilter.Properties.Mask.AutoComplete")));
            this.lblDateFilter.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.BeepOnError")));
            this.lblDateFilter.Properties.Mask.EditMask = resources.GetString("lblDateFilter.Properties.Mask.EditMask");
            this.lblDateFilter.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.IgnoreMaskBlank")));
            this.lblDateFilter.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("lblDateFilter.Properties.Mask.MaskType")));
            this.lblDateFilter.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("lblDateFilter.Properties.Mask.PlaceHolder")));
            this.lblDateFilter.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.SaveLiteral")));
            this.lblDateFilter.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.ShowPlaceHolders")));
            this.lblDateFilter.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.UseMaskAsDisplayFormat")));
            this.lblDateFilter.Properties.NullValuePrompt = resources.GetString("lblDateFilter.Properties.NullValuePrompt");
            this.lblDateFilter.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lblDateFilter.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // lblFilter
            // 
            resources.ApplyResources(this.lblFilter, "lblFilter");
            this.lblFilter.MenuManager = this.barManager1;
            this.lblFilter.Name = "lblFilter";
            this.lblFilter.Properties.AccessibleDescription = resources.GetString("lblFilter.Properties.AccessibleDescription");
            this.lblFilter.Properties.AccessibleName = resources.GetString("lblFilter.Properties.AccessibleName");
            this.lblFilter.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lblFilter.Properties.Appearance.FontSizeDelta")));
            this.lblFilter.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lblFilter.Properties.Appearance.FontStyleDelta")));
            this.lblFilter.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lblFilter.Properties.Appearance.GradientMode")));
            this.lblFilter.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lblFilter.Properties.Appearance.Image")));
            this.lblFilter.Properties.Appearance.Options.UseTextOptions = true;
            this.lblFilter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblFilter.Properties.AutoHeight = ((bool)(resources.GetObject("lblFilter.Properties.AutoHeight")));
            this.lblFilter.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("lblFilter.Properties.Mask.AutoComplete")));
            this.lblFilter.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("lblFilter.Properties.Mask.BeepOnError")));
            this.lblFilter.Properties.Mask.EditMask = resources.GetString("lblFilter.Properties.Mask.EditMask");
            this.lblFilter.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("lblFilter.Properties.Mask.IgnoreMaskBlank")));
            this.lblFilter.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("lblFilter.Properties.Mask.MaskType")));
            this.lblFilter.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("lblFilter.Properties.Mask.PlaceHolder")));
            this.lblFilter.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("lblFilter.Properties.Mask.SaveLiteral")));
            this.lblFilter.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("lblFilter.Properties.Mask.ShowPlaceHolders")));
            this.lblFilter.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("lblFilter.Properties.Mask.UseMaskAsDisplayFormat")));
            this.lblFilter.Properties.NullValuePrompt = resources.GetString("lblFilter.Properties.NullValuePrompt");
            this.lblFilter.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lblFilter.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // frm_SL_CustomerItemsSales
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.picLogo);
            this.Controls.Add(this.grdCategory);
            this.Controls.Add(this.lblFilter);
            this.Controls.Add(this.lblDateFilter);
            this.Controls.Add(this.lblReportName);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.KeyPreview = true;
            this.Name = "frm_SL_CustomerItemsSales";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_Rep_FormClosing);
            this.Load += new System.EventHandler(this.frm_SL_InvoiceList_Load);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdCategory)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Stores1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Process)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Currency)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repPaymentMethod)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_ItemCategory1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit2View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_MtrxParent)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_cat)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_comp)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_mtrx)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Vendor)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblReportName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDateFilter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFilter.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnPreview;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraGrid.GridControl grdCategory;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraBars.BarButtonItem barBtnPrint;
        private DevExpress.XtraEditors.TextEdit lblDateFilter;
        private DevExpress.XtraEditors.TextEdit lblReportName;
        private DevExpress.XtraEditors.PictureEdit picLogo;
        private DevExpress.XtraEditors.TextEdit lblFilter;
        private DevExpress.XtraGrid.Columns.GridColumn col_Qty;
        private DevExpress.XtraGrid.Columns.GridColumn col_ItemNameAr;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_MtrxParent;
        private DevExpress.XtraGrid.Views.Grid.GridView repositoryItemGridLookUpEdit1View;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_comp;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_cat;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_mtrx;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraBars.BarButtonItem barBtnRefresh;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_Vendor;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn col_TotalSellValue;
        private DevExpress.XtraGrid.Columns.GridColumn col_TotalSellPrice;
        private DevExpress.XtraGrid.Columns.GridColumn colSalesTax;
        private DevExpress.XtraGrid.Columns.GridColumn colDiscountValue;
        private DevExpress.XtraGrid.Columns.GridColumn colDiscountRatio3;
        private DevExpress.XtraGrid.Columns.GridColumn colDiscountRatio2;
        private DevExpress.XtraGrid.Columns.GridColumn colDiscountRatio;
        private DevExpress.XtraGrid.Columns.GridColumn col_SellPrice;
        private DevExpress.XtraGrid.Columns.GridColumn col_PiecesCount;
        private DevExpress.XtraGrid.Columns.GridColumn col_UOM;
        private DevExpress.XtraGrid.Columns.GridColumn col_InvoiceDate;
        private DevExpress.XtraGrid.Columns.GridColumn col_InvoiceCode;
        private DevExpress.XtraGrid.Columns.GridColumn col_CusNameAr;
        private DevExpress.XtraGrid.Columns.GridColumn colItemDescription;
        private DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit repositoryItemMemoEdit1;
        private DevExpress.XtraGrid.Columns.GridColumn colItemDescriptionEn;
        private DevExpress.XtraGrid.Columns.GridColumn colDriverName;
        private DevExpress.XtraGrid.Columns.GridColumn colVehicleNumber;
        private DevExpress.XtraGrid.Columns.GridColumn colDestination;
        private DevExpress.XtraGrid.Columns.GridColumn colSalesEmp;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem mi_CopyRows;
        private DevExpress.XtraGrid.Columns.GridColumn col_DicVal1;
        private DevExpress.XtraGrid.Columns.GridColumn col_DicVal2;
        private DevExpress.XtraGrid.Columns.GridColumn col_DicVal3;
        private DevExpress.XtraGrid.Columns.GridColumn col_ItemCode1;
        private DevExpress.XtraGrid.Columns.GridColumn col_ItemCode2;
        private DevExpress.XtraGrid.Columns.GridColumn col_Serial;
        private DevExpress.XtraGrid.Columns.GridColumn col_Batch;
        private DevExpress.XtraGrid.Columns.GridColumn col_CompanyNameAr;
        private DevExpress.XtraGrid.Columns.GridColumn col_Store;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraEditors.Repository.RepositoryItemImageComboBox rep_Process;
        private DevExpress.XtraGrid.Columns.GridColumn col_CrncId;
        private DevExpress.XtraGrid.Columns.GridColumn col_CrncRate;
        private DevExpress.XtraGrid.Columns.GridColumn col_TotalSellPrice_Local;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit rep_Currency;
        private DevExpress.XtraGrid.Columns.GridColumn col_CustomerCategory;
        private DevExpress.XtraGrid.Columns.GridColumn col_CustomerGroup;
        private DevExpress.XtraGrid.Columns.GridColumn colNotes;
        private DevExpress.XtraGrid.Columns.GridColumn colPaymentMethod;
        private DevExpress.XtraEditors.Repository.RepositoryItemImageComboBox repPaymentMethod;
        private DevExpress.XtraGrid.Columns.GridColumn Col_ItemCategory;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_Stores1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView6;
        private DevExpress.XtraGrid.Columns.GridColumn col_storeId;
        private DevExpress.XtraGrid.Columns.GridColumn col_NameAr;
        private DevExpress.XtraGrid.Columns.GridColumn col_NameEn;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_ItemCategory1;
        private DevExpress.XtraGrid.Views.Grid.GridView repositoryItemGridLookUpEdit2View;
        private DevExpress.XtraGrid.Columns.GridColumn col_CategoryId;
        private DevExpress.XtraGrid.Columns.GridColumn col_CategoryNameEn;
        private DevExpress.XtraGrid.Columns.GridColumn col_CategoryNameAr;
        private DevExpress.XtraGrid.Columns.GridColumn col_Serial2;
        private DevExpress.XtraGrid.Columns.GridColumn col_UserName;
        private DevExpress.XtraBars.BarSubItem barSubItem1;
        private DevExpress.XtraBars.BarButtonItem barbtnPrint_P;
        private DevExpress.XtraBars.BarSubItem barSubItem2;
        private DevExpress.XtraBars.BarButtonItem barbtnPrintP;
        private DevExpress.XtraGrid.Columns.GridColumn col_kg_Weight_libra;
        private DevExpress.XtraGrid.Columns.GridColumn col_LibraQty;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
    }
}