﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;
using DAL.Res;

using System.Data.SqlClient;

namespace Pharmacy.Forms
{
    /// <summary>
    /// 1. get items from 'IC_ItemStore' which has Qtys.
    /// 2. clear all store tables.
    /// 3. add items again to 'IC_ItemStore', and also to 'IC_OpenBalance'.
    /// 4. get Inventoy Cost, and keep it in memory, to be added as a journal.
    /// 5. get balances of all 'vendors' and 'clients' (Debit/Credit).
    /// 6. get balances of all 'banks' and 'drawers' (Debit/Credit).
    /// 7. clear all 'ACC_Journal'&'ACC_JournalDetails'.
    /// 8. add balances for all previous accounts, as open balance.
    /// 9. Add Capital balance
    /// 
    /// </summary>
    public partial class frm_ST_NewYear : DevExpress.XtraEditors.XtraForm
    {
        DataTable dtStores = new DataTable();
        DataTable OpenJournals = new DataTable();
        List<ACC_Account> lstAccounts = new List<ACC_Account>();

        List<ACC_NotesPayable> notesPayable = new List<ACC_NotesPayable>();
        List<ACC_NotesReceivable> notesReceivable = new List<ACC_NotesReceivable>();

        List<HR_Loan> loans = new List<HR_Loan>();
        List<HR_LoanDetail> loanDetail = new List<HR_LoanDetail>();

        public frm_ST_NewYear()
        {
            InitializeComponent();
        }

        private void frmNewYear_Load(object sender, EventArgs e)
        {
            StringBuilder msg = new StringBuilder();
            msg.AppendLine("برجاء الحذر, وقراءة هذه الرسالة قبل عمل سنة مالية جديدة");
            msg.AppendLine("1. سيتم عمل نسخة احتياطية لقاعدة البيانات الحالية, فهي تمثل السنه المالية السابقة.");
            msg.AppendLine("2. سيتم عمل القيد الافتتاحي للسنة الجديدة.");
            msg.AppendLine("3. سيتم نقل البضاعة الموجودة في المخازن الان, كأرصدة افتتاحية للسنة الجديدة.");
            msg.AppendLine("4. سيتم مسح جميع الفواتير والقيود والسندات الخاصة بالسنة السابقة.");
            msg.AppendLine("5. سيتم مسح جميع التشغيلات .");
            msg.AppendLine("6. سيتم مسح جميع الأوراق التجارية الغير مستحقة.");
            msg.AppendLine("");
            memoEdit1.Text = msg.ToString();

            BindDataSources();
        }

        private void BindDataSources()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            lstAccounts = DB.ACC_Accounts.OrderBy(x => x.AcNumber).Select(x => x).ToList();

            OpenJournals.Columns.Add("AccountId", typeof(int));
            OpenJournals.Columns.Add("CurrencyId", typeof(int));
            OpenJournals.Columns.Add("Debit", typeof(decimal));
            OpenJournals.Columns.Add("Credit", typeof(decimal));

            //get stores and its OpenInventoryAccounts
            dtStores.Columns.Add("StoreId");
            dtStores.Columns.Add("OpenInventoryAccountId");
            dtStores.Columns.Add("CostMethod");
            dtStores.PrimaryKey = new DataColumn[] { dtStores.Columns["StoreId"] };
            var stores = DB.IC_Stores.ToList();
            foreach (var store in stores)
                dtStores.Rows.Add(store.StoreId, store.OpenInventoryAccount, store.CostMethod);

        }

        private void btnNewYear_Click(object sender, EventArgs e)
        {
            if (dateEdit1.EditValue == null)
            {
                XtraMessageBox.Show("يجب تسجيل تاريخ بداية السنة الجديدة", "", MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
                dateEdit1.Focus();
                return;
            }

            if (XtraMessageBox.Show("هذه العملية لابد ان تتم من خلال الخادم فقط، هل تريد الاستمرار", "", MessageBoxButtons.YesNo,
            MessageBoxIcon.Question) == DialogResult.No)
                return;

            if (XtraMessageBox.Show("هل قرأت الرسالة المكتوبة بشكل كامل", "تحذير", MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                if (XtraMessageBox.Show("ستسغرق العملية بعض الوقت, هل تريد الاستمرار", "تحذير", MessageBoxButtons.YesNo,
               MessageBoxIcon.Question) == DialogResult.No)
                    return;

                XtraMessageBox.Show("قم بتسجيل اسم قاعدة البيانات للسنه السابقة", "", MessageBoxButtons.OK,
               MessageBoxIcon.Information);

                //current connection string & database name
                SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(DAL.Config.ConnectionString);
                string connectionString = builder.ConnectionString;
                string databaseName = builder.InitialCatalog;
                DateTime date_start = dateEdit1.DateTime.Date;

                SaveFileDialog SFD = new SaveFileDialog();
                if (SFD.ShowDialog() == DialogResult.OK && SFD.FileName.Length > 0)
                {
                    // take a backup for the last year.
                    string conn_string_master = connectionString.Replace(databaseName, "Master");
                    if (ErpUtils.backup_Database(databaseName, SFD.FileName, conn_string_master) == true)
                        XtraMessageBox.Show("تم حفظ السنه السابقه بنجاح", "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    else
                    {
                        XtraMessageBox.Show("حدث خطأ عند حفظ السنة السابقة, عملية انشاء سنة مالية جديدة لم تتم", "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }
                    progressBarControl1.Increment(1);
                    XtraMessageBox.Show("برجاء الانتظار حتي ظهور رسالة تفيد بانتهاء العملية", "", MessageBoxButtons.OK, MessageBoxIcon.Information);


                    DAL.ERPDataContext DB = new DAL.ERPDataContext();

                    //get still notes, to be added again to the new year
                    notesPayable = DB.ACC_NotesPayables.Where(p => p.RegDate < date_start && p.ResponseType == (byte)PayNoteResponseType.Still).ToList();
                    notesReceivable = DB.ACC_NotesReceivables.Where(p => p.RegDate < date_start && p.ResponseType == (byte)ReceiveNoteResponseType.Still).ToList();

                    loans = DB.HR_Loans.Where(p => p.RegDate < date_start || p.Payed == false).ToList();
                    loanDetail = DB.HR_LoanDetails.Where(p => loans.Select(x => x.LoanId).Contains(p.LoanId)).ToList();

                    #region GetNewYearDocuments to be reinserted
                    //List<ACC_Journal> lst_ACC_Journal = DB.ACC_Journals.Where(x => x.InsertDate > date_start).ToList();
                    //List<ACC_JournalDetail> lst_ACC_JournalDetail = (from jd in DB.ACC_JournalDetails
                    //                                                 join j in DB.ACC_Journals
                    //                                                 on jd.JournalId equals j.JournalId
                    //                                                 where j.InsertDate > date_start
                    //                                                 select jd).ToList();
                    //List<IC_ItemStore> lst_IC_ItemStore = DB.IC_ItemStores.Where(x => x.InsertTime > date_start).ToList();

                    //#region Acc
                    //List<ACC_CashNote> lst_ACC_CashNote = DB.ACC_CashNotes.Where(x => x.NoteDate > date_start).ToList();
                    //List<ACC_CashTransfer> lst_ACC_CashTransfer = DB.ACC_CashTransfers.Where(x => x.TransferDate > date_start).ToList();
                    //List<ACC_NotesReceivable> lst_ACC_NotesReceivable = DB.ACC_NotesReceivables.Where(x => x.RegDate > date_start).ToList();
                    //List<ACC_NotesPayable> lst_ACC_NotesPayable = DB.ACC_NotesPayables.Where(x => x.RegDate > date_start).ToList();
                    //List<ACC_RevExpEntry> lst_ACC_RevExpEntry = DB.ACC_RevExpEntries.Where(x => x.EntryDate > date_start).ToList();
                    //List<ACC_DebitCreditNote> lst_ACC_DebitCreditNote = DB.ACC_DebitCreditNotes.Where(x => x.NoteDate > date_start).ToList();
                    //#endregion

                    //#region IC
                    //List<IC_InTrn> lst_IC_InTrn = DB.IC_InTrns.Where(x => x.InTrnsDate > date_start).ToList();
                    //List<IC_InTrnsDetail> lst_IC_InTrnsDetail = (from jd in DB.IC_InTrnsDetails
                    //                                             join j in DB.IC_InTrns
                    //                                                 on jd.InTrnsId equals j.InTrnsId
                    //                                                 where j.InTrnsDate > date_start
                    //                                                 select jd).ToList();

                    //List<IC_OutTrn> lst_IC_OutTrn = DB.IC_OutTrns.Where(x => x.OutTrnsDate > date_start).ToList();
                    //List<IC_OutTrnsDetail> lst_IC_OutTrnsDetail = (from jd in DB.IC_OutTrnsDetails
                    //                                             join j in DB.IC_OutTrns
                    //                                                 on jd.OutTrnsId equals j.OutTrnsId
                    //                                             where j.OutTrnsDate > date_start
                    //                                             select jd).ToList();

                    //List<IC_Damaged> lst_IC_Damaged = DB.IC_Damageds.Where(x => x.DamagedDate > date_start).ToList();
                    //List<IC_DamagedDetail> lst_IC_DamagedDetail = (from jd in DB.IC_DamagedDetails
                    //                                               join j in DB.IC_Damageds
                    //                                                   on jd.DamagedId equals j.DamagedId
                    //                                               where j.DamagedDate > date_start
                    //                                               select jd).ToList();

                    //List<IC_StoreMove> lst_IC_StoreMove = DB.IC_StoreMoves.Where(x => x.StoreMoveDate > date_start).ToList();
                    //List<IC_StoreMoveDetail> lst_IC_StoreMoveDetail = (from jd in DB.IC_StoreMoveDetails
                    //                                               join j in DB.IC_StoreMoves
                    //                                                   on jd.StoreMoveId equals j.StoreMoveId
                    //                                               where j.StoreMoveDate > date_start
                    //                                               select jd).ToList();
                    //#endregion

                    //#region Sales
                    //List<SL_Invoice> lst_SL_Invoice = DB.SL_Invoices.Where(x => x.InvoiceDate > date_start).ToList();
                    //List<SL_InvoiceDetail> lst_SL_InvoiceDetail = (from jd in DB.SL_InvoiceDetails
                    //                                             join j in DB.SL_Invoices
                    //                                                 on jd.SL_InvoiceId equals j.SL_InvoiceId
                    //                                             where j.InvoiceDate > date_start
                    //                                             select jd).ToList();

                    //List<SL_Return> lst_SL_Return = DB.SL_Returns.Where(x => x.ReturnDate > date_start).ToList();
                    //List<SL_ReturnDetail> lst_SL_ReturnDetail = (from jd in DB.SL_ReturnDetails
                    //                                               join j in DB.SL_Returns
                    //                                                   on jd.SL_ReturnId equals j.SL_ReturnId
                    //                                               where j.ReturnDate > date_start
                    //                                               select jd).ToList();
                    
                    //#endregion

                    //#region Purchase
                    //List<PR_Invoice> lst_PR_Invoice = DB.PR_Invoices.Where(x => x.InvoiceDate > date_start).ToList();
                    //List<PR_InvoiceDetail> lst_PR_InvoiceDetail = (from jd in DB.PR_InvoiceDetails
                    //                                               join j in DB.PR_Invoices
                    //                                                   on jd.PR_InvoiceId equals j.PR_InvoiceId
                    //                                               where j.InvoiceDate > date_start
                    //                                               select jd).ToList();

                    //List<PR_Return> lst_PR_Return = DB.PR_Returns.Where(x => x.ReturnDate > date_start).ToList();
                    //List<PR_ReturnDetail> lst_PR_ReturnDetail = (from jd in DB.PR_ReturnDetails
                    //                                             join j in DB.PR_Returns
                    //                                                 on jd.PR_ReturnId equals j.PR_ReturnId
                    //                                             where j.ReturnDate > date_start
                    //                                             select jd).ToList();

                    //#endregion

                    //#region HR
                    //List<HR_Loan> lst_HR_Loan = DB.HR_Loans.Where(x => x.RegDate > date_start).ToList();
                    //List<HR_LoanDetail> lst_HR_LoanDetail = (from jd in DB.HR_LoanDetails
                    //                                         join j in DB.HR_Loans
                    //                                             on jd.LoanId equals j.LoanId
                    //                                         where j.RegDate > date_start
                    //                                         select jd).ToList();

                    //List<HR_Pay> lst_HR_Pay = DB.HR_Pays.Where(x => x.DueDate > date_start).ToList();
                    //List<HR_PayDetail> lst_HR_PayDetail = (from jd in DB.HR_PayDetails
                    //                                             join j in DB.HR_Pays
                    //                                                 on jd.PayId equals j.PayId
                    //                                             where j.DueDate > date_start
                    //                                             select jd).ToList();

                    //#endregion

                    //#region Manf
                    //List<Manf_QC> lst_Manf_QC = DB.Manf_QCs.Where(x => x.Manf_QCDate > date_start).ToList();
                    //List<Manf_QCDetail> lst_Manf_QCDetail = (from jd in DB.Manf_QCDetails
                    //                                         join j in DB.Manf_QCs
                    //                                             on jd.Manf_QCId equals j.Manf_QCId
                    //                                         where j.Manf_QCDate > date_start
                    //                                         select jd).ToList();

                    //List<Manufacturing> lst_Manufacturing = DB.Manufacturings.Where(x => x.StartDate > date_start).ToList();
                    //List<ManfProduct> lst_ManfProduct = (from jd in DB.ManfProducts
                    //                                     join j in DB.Manufacturings
                    //                                         on jd.Manf_Id equals j.Manf_Id
                    //                                     where j.StartDate > date_start
                    //                                     select jd).ToList();
                    //List<ManfDamage> lst_ManfDamage = (from jd in DB.ManfDamages
                    //                                     join j in DB.Manufacturings
                    //                                         on jd.Manf_Id equals j.Manf_Id
                    //                                     where j.StartDate > date_start
                    //                                     select jd).ToList();
                    //List<Manf_Wage> lst_Manf_Wage = (from jd in DB.Manf_Wages
                    //                                   join j in DB.Manufacturings
                    //                                       on jd.Manf_Id equals j.Manf_Id
                    //                                   where j.StartDate > date_start
                    //                                   select jd).ToList();
                    //List<Manf_Expense> lst_Manf_Expense = (from jd in DB.Manf_Expenses
                    //                                 join j in DB.Manufacturings
                    //                                     on jd.Manf_Id equals j.Manf_Id
                    //                                 where j.StartDate > date_start
                    //                                 select jd).ToList();
                    //List<ManfDetail> lst_ManfDetail = (from jd in DB.ManfDetails
                    //                                       join j in DB.Manufacturings
                    //                                           on jd.Manf_Id equals j.Manf_Id
                    //                                       where j.StartDate > date_start
                    //                                       select jd).ToList();
                    //#endregion
                    #endregion

                    //قيود الشيكات لازم تتشال الاول، لأنها بتتحط تاني
                    ClearAllStillNotesJournals(date_start);
                    progressBarControl1.Increment(1);
                    memoEdit1.Text += "تم الانتهاء من قيود الشيكات" + "\r\n";

                    LoadAssetLiabTree(date_start);
                    progressBarControl1.Increment(1);
                    memoEdit1.Text += "تم الانتهاء من تحميل الاصول والخصوم" + "\r\n";

                    //1. get Open Inventory.
                    List<IC_ItemStore> lstNewStore = MyHelper.GetAvailableItemsNewYear(date_start);
                    progressBarControl1.Increment(1);
                    memoEdit1.Text += "تم الانتهاء من ترصيد المخزون" + "\r\n";

                    //1.1 get transactions in new year

                    //2. clear all tables.
                    DeleteFromTables(date_start);
                    progressBarControl1.Increment(1);
                    memoEdit1.Text += "تم الانتهاء من مسح العمليات" + "\r\n";

                    System.Threading.Thread.Sleep(3000);
                    //ResetIdentity();

                    progressBarControl1.Increment(1);
                    memoEdit1.Text += "تم الانتهاء من اعادة تهيئة الجداول" + "\r\n";

                    //3. add items again to 'IC_ItemStore'.
                    DB.IC_ItemStores.InsertAllOnSubmit(lstNewStore);
                    DB.SubmitChanges();

                    progressBarControl1.Increment(1);
                    memoEdit1.Text += "تم الانتهاء من اضافة رصيد اول المدة للمخازن" + "\r\n";

                    // 8. add balances for all previous accounts, as open balance.
                    ACC_Journal jr = new ACC_Journal();
                    jr.JCode = 1;
                    jr.InsertDate = date_start;
                    jr.JNotes = "القيد الافتتاحي";
                    jr.ProcessId = (int)Process.DailyJournal;
                    jr.SourceId = 0;
                    jr.InsertUser = Shared.UserId;
                    jr.IsPosted = true;
                    jr.StoreId = 1;
                    jr.CrncId = 0;
                    jr.CrncRate = 1;
                    DB.ACC_Journals.InsertOnSubmit(jr);
                    DB.SubmitChanges();

                    decimal CapitalBalance = 0;
                    foreach (DataRow r in OpenJournals.AsEnumerable())
                    {
                        DAL.ACC_JournalDetail d1 = new DAL.ACC_JournalDetail();
                        d1.JournalId = jr.JournalId;
                        d1.AccountId = Convert.ToInt32(r["AccountId"]);
                        d1.Debit = Convert.ToDecimal(r["Debit"]);
                        d1.Credit = Convert.ToDecimal(r["Credit"]);
                        d1.Notes = "مرحل من السنه السابقه";
                        d1.CrncId = Convert.ToInt32(r["CurrencyId"]);
                        d1.CrncRate = Shared.lstCurrency.Where(x => x.CrncId == Convert.ToInt32(r["CurrencyId"])).Select(x => x.LastRate).FirstOrDefault();
                        DB.ACC_JournalDetails.InsertOnSubmit(d1);
                        CapitalBalance += (d1.Debit - d1.Credit);
                    }

                    progressBarControl1.Increment(1);
                    memoEdit1.Text += "تم الانتهاء من تسجيل القيد الافتتاحي" + "\r\n";

                    // 9. Add Capital balance                    
                    //DAL.ACC_JournalDetail d2 = new DAL.ACC_JournalDetail();
                    //d2.JournalId = jr.JCode;
                    //d2.AccountId = (int)AccountsTree.Capital;
                    //d2.Debit = CapitalBalance == 0? 0 : (CapitalBalance < 0 ? CapitalBalance * -1 : 0);
                    //d2.Credit = CapitalBalance == 0? 0 : (CapitalBalance > 0 ? CapitalBalance : 0);
                    //d2.Notes = "مرحل من السنه السابقه";
                    //DB.ACC_JournalDetails.InsertOnSubmit(d2);

                    DB.SubmitChanges();
                    progressBarControl1.Increment(1);

                    //^^^^ add notes again and its journals
                    AddStillNotesAgain();
                    progressBarControl1.Increment(1);
                    memoEdit1.Text += "تم الانتهاء من اضافة الشيكات المستحقة" + "\r\n";

                    //update new fiscal year date range
                    DB.ST_CompanyInfos.First().FiscalYearStartDate = dateEdit1.DateTime.Date;
                    DB.ST_CompanyInfos.First().FiscalYearEndDate = dateEdit1.DateTime.Date.AddYears(1).AddDays(-1);
                    
                    DB.ST_Stores.First().ClosePeriodDate = dateEdit1.DateTime.Date.AddDays(-1);
                    
                    var hr_users = DB.HR_Users.ToList();
                    foreach (var u in hr_users)
                        u.EditInClosedPeriod = false;

                    DB.SubmitChanges();

                    progressBarControl1.Increment(1);

                    DB = new ERPDataContext();

                    #region Add Old Still Loans
                    foreach (HR_Loan n in loans)
                    {
                        HR_Loan l = new HR_Loan();
                        l.RegDate = n.RegDate;
                        l.StoreId = n.StoreId;
                        l.Payed = n.Payed;
                        l.Notes = n.Notes;
                        l.JournalId = 0;
                        l.InstallmentsCount = n.InstallmentsCount;
                        l.InsertUser = n.InsertUser;
                        l.EmpId = n.EmpId;
                        l.EmpAccountId = n.EmpAccountId;
                        l.DrawerAccountId = n.DrawerAccountId;
                        l.Amount = n.Amount;
                        DB.HR_Loans.InsertOnSubmit(l);
                        DB.SubmitChanges();

                        var details = loanDetail.Where(x => x.LoanId == n.LoanId).ToList();
                        foreach (HR_LoanDetail d in details)
                        {
                            if (DB.HR_LoanDetails.Where(x => x.LoanDetailId == d.LoanDetailId).Count() > 0)
                                continue;

                            HR_LoanDetail t = new HR_LoanDetail();
                            t.LoanId = l.LoanId;
                            t.Amount = d.Amount;
                            t.PayDate = d.PayDate;
                            t.Payed = d.Payed;
                            DB.HR_LoanDetails.InsertOnSubmit(t);
                        }
                        DB.SubmitChanges();
                    }                     

                    progressBarControl1.Increment(1);
                    memoEdit1.Text += "تم الانتهاء من اضافة سلف الموظفين المستحقة" + "\r\n";
                    #endregion

                    #region reinsert new documents
                    #region SL
                    //foreach (SL_Invoice s in lst_SL_Invoice)
                    //{
                    //    //insert journal, get old id, and new id, 
                    //    //insert journal_details with new journal_id
                    //    int JournalId = s.JornalId;
                    //    int JournalId_new = 0;
                    //    if (JournalId > 0)
                    //    {
                    //        ACC_Journal j = lst_ACC_Journal.Where(x => x.JournalId == JournalId).First();
                    //        j.JCode = HelperAcc.Get_Jornal_Code();
                    //        List<ACC_JournalDetail> lst_jd = lst_ACC_JournalDetail.Where(x => x.JournalId == JournalId).ToList();
                    //        DB.ACC_Journals.InsertOnSubmit(j);
                    //        DB.SubmitChanges();
                    //        JournalId_new = j.JournalId;

                    //        foreach (ACC_JournalDetail d in lst_jd)
                    //        {
                    //            d.JournalId = JournalId_new;
                    //            DB.ACC_JournalDetails.InsertOnSubmit(d);
                    //        }
                    //    }

                    //    //insert sl_inv, after updating journal id, then get old and new id.                        
                    //    int SL_InvoiceId = s.SL_InvoiceId;
                    //    int SL_InvoiceId_new = 0;
                    //    s.JornalId = JournalId_new;

                    //    DB.SL_Invoices.InsertOnSubmit(s);
                    //    DB.SubmitChanges();
                    //    SL_InvoiceId_new = s.SL_InvoiceId;

                    //    //insert sl_detail, using new id, then add itemstore using new detail id
                    //    List<SL_InvoiceDetail> lst_d = lst_SL_InvoiceDetail.Where(x => x.SL_InvoiceId == SL_InvoiceId).ToList();
                    //    int SL_InvoiceDetailId = 0;
                    //    int SL_InvoiceDetailId_new = 0;
                    //    foreach (SL_InvoiceDetail d in lst_d)
                    //    {
                    //        SL_InvoiceDetailId = d.SL_InvoiceDetailId;
                    //        DB.SL_InvoiceDetails.InsertOnSubmit(d);
                    //        DB.SubmitChanges();
                    //        SL_InvoiceDetailId_new = d.SL_InvoiceDetailId;

                    //        //get itemstore
                    //        List<IC_ItemStore> lst_store = lst_IC_ItemStore.Where(x => x.ProcessId == (int)Process.SellInvoice && x.SourceId == SL_InvoiceDetailId).ToList();
                    //        foreach (IC_ItemStore itmstr in lst_store)
                    //        {
                    //            itmstr.SourceId = SL_InvoiceDetailId_new;
                    //            DB.IC_ItemStores.InsertOnSubmit(itmstr);
                    //        }
                    //    }
                    //}
                    #endregion
                    #endregion

                    //reindex journal code
                    List<ACC_Journal> lst_ACC_Journal = DB.ACC_Journals.OrderBy(x => x.InsertDate).ToList();
                    int index = 1;
                    foreach(var j in lst_ACC_Journal)
                    {
                        j.JCode = index;
                        index++;
                    }
                    DB.SubmitChanges();
                    progressBarControl1.Increment(1);

                    XtraMessageBox.Show("تمت عملية التحول لسنة جديدة بنجاح", "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    btnNewYear.Enabled = false;
                }
                else
                    XtraMessageBox.Show("يجب حفظ النسخة الاحتياطية قبل الانتقال لسنة جديدة, حاول مرة أخري", "", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        #region Still Pay and Receive Notes

        private void ClearAllStillNotesJournals(DateTime insertTime)
        {
            ERPDataContext DB = new ERPDataContext();

            var stillPayNotes = from p in DB.ACC_NotesPayables
                                where p.RegDate < insertTime
                                where p.ResponseType == (byte)PayNoteResponseType.Still
                                select p;
            //get journals to delete
            foreach (ACC_NotesPayable p in stillPayNotes)
            {
                var jrnl = DB.ACC_Journals.Where(j => j.JournalId == p.RegJournalId).FirstOrDefault();
                var jrnlDetails = DB.ACC_JournalDetails.Where(j => j.JournalId == p.RegJournalId).Select(j => j);
                DB.ACC_JournalDetails.DeleteAllOnSubmit(jrnlDetails);
                DB.SubmitChanges();
                DB.ACC_Journals.DeleteOnSubmit(jrnl);
                DB.SubmitChanges();
            }


            var stillReceiveNotes = from r in DB.ACC_NotesReceivables
                                    where r.RegDate < insertTime
                                    where r.ResponseType == (byte)ReceiveNoteResponseType.Still
                                    select r;
            //get journals to delete
            foreach (ACC_NotesReceivable r in stillReceiveNotes)
            {
                var jrnl = DB.ACC_Journals.Where(j => j.JournalId == r.RegJournalId).FirstOrDefault();
                var jrnlDetails = DB.ACC_JournalDetails.Where(j => j.JournalId == r.RegJournalId).Select(j => j);

                DB.ACC_JournalDetails.DeleteAllOnSubmit(jrnlDetails);
                DB.SubmitChanges();
                DB.ACC_Journals.DeleteOnSubmit(jrnl);
                DB.SubmitChanges();

                if (r.UndercollectJournalId.HasValue)
                {
                    var jrnlUndr = DB.ACC_Journals.Where(j => j.JournalId == r.UndercollectJournalId).FirstOrDefault();
                    var jrnlDetailsUndr = DB.ACC_JournalDetails.Where(j => j.JournalId == r.UndercollectJournalId).Select(j => j);

                    DB.ACC_JournalDetails.DeleteAllOnSubmit(jrnlDetailsUndr);
                    DB.SubmitChanges();
                    DB.ACC_Journals.DeleteOnSubmit(jrnlUndr);
                    DB.SubmitChanges();
                }
            }
        }


        private void AddStillNotesAgain()
        {
            ERPDataContext DB = new ERPDataContext();

            foreach (ACC_NotesPayable p in notesPayable)
            {
                ACC_NotesPayable nt = new ACC_NotesPayable();
                nt.NoteType = p.NoteType;
                nt.NoteSerial = DB.ACC_NotesPayables.Where(a => a.NoteType == p.NoteType).Select(a => a.NoteSerial).ToList().DefaultIfEmpty(0).Max() + 1;
                nt.NoteNumber = p.NoteNumber;
                nt.RegDate = dateEdit1.DateTime.Date;
                nt.DueDate = p.DueDate;
                nt.Amount = p.Amount;
                nt.Notes = p.Notes;
                nt.Provision = p.Provision;
                nt.BankAccountId = p.BankAccountId;
                nt.IsVendor = p.IsVendor;
                nt.DealerId = p.DealerId;
                nt.DealerAccountId = p.DealerAccountId;
                nt.ProcessId = p.ProcessId;
                nt.SourceId = p.SourceId;
                nt.ResponseType = (byte)PayNoteResponseType.Still;
                nt.DrawerAccountId = p.DrawerAccountId;
                nt.RegJournalId = 0;
                nt.StoreId = p.StoreId;
                nt.CostCenter = p.CostCenter;
                nt.CostCenter = p.CostCenter;
                nt.CrncId = p.CrncId;
                nt.CrncRate = p.CrncRate;

                DB.ACC_NotesPayables.InsertOnSubmit(nt);
                DB.SubmitChanges();

                if (Convert.ToInt32(nt.NoteType) == (int)NoteType.Installment)
                {
                    //do nothing
                }
                else
                {
                    string isVendor = p.IsVendor == null ? (Shared.IsEnglish ? ResEn.Expenses : ResAr.Expenses) :
                    p.IsVendor.Value == true ? (Shared.IsEnglish ? ResEn.Vendor : ResAr.Vendor) :
                   (Shared.IsEnglish ? ResEn.Customer : ResAr.Customer);
                    string dealerName = DB.ACC_Accounts.Where(a => a.AccountId == nt.DealerAccountId).Select(a => a.AcNameAr).FirstOrDefault();

                    HelperAcc.CreatePayNoteJournal(nt, isVendor, dealerName);
                }

                DB.SubmitChanges();
            }

            foreach (ACC_NotesReceivable r in notesReceivable)
            {
                ACC_NotesReceivable nt = new ACC_NotesReceivable();
                nt.NoteType = r.NoteType;
                nt.NoteSerial = DB.ACC_NotesReceivables.Where(a => a.NoteType == r.NoteType).Select(a => a.NoteSerial).ToList().DefaultIfEmpty(0).Max() + 1;
                nt.NoteNumber = r.NoteNumber;
                nt.RegDate = dateEdit1.DateTime.Date;
                nt.DueDate = r.DueDate;
                nt.Amount = r.Amount;
                nt.Notes = r.Notes;
                nt.Provision = r.Provision;
                nt.IsVendor = r.IsVendor;
                nt.DealerId = r.DealerId;
                nt.DealerAccountId = r.DealerAccountId;
                nt.ProcessId = r.ProcessId;
                nt.SourceId = r.SourceId;
                nt.ResponseType = (byte)ReceiveNoteResponseType.Still;
                nt.DrawerAccountId = r.DrawerAccountId;
                nt.BankAccountId = r.BankAccountId;
                nt.RegJournalId = 0;
                nt.StoreId = r.StoreId;
                nt.CostCenter = r.CostCenter;
                nt.RecipientEmpId = r.RecipientEmpId;
                nt.Is_Undercollect = r.Is_Undercollect;
                nt.collectBankAccId = r.collectBankAccId;
                nt.UndercollectJournalId = null;
                nt.UndercollectDate = r.UndercollectDate;
                nt.CrncId = r.CrncId;
                nt.CrncRate = r.CrncRate;

                DB.ACC_NotesReceivables.InsertOnSubmit(nt);
                DB.SubmitChanges();

                if (Convert.ToInt32(nt.NoteType) == (int)NoteType.Installment)
                {
                    //do nothing
                }
                else
                {
                    string isVendor = r.IsVendor == null ? (Shared.IsEnglish ? ResEn.Revenue : ResAr.Revenue) :
                    r.IsVendor.Value == true ? (Shared.IsEnglish ? ResEn.Vendor : ResAr.Vendor) :
                    (Shared.IsEnglish ? ResEn.Customer : ResAr.Customer);
                    string dealerName = DB.ACC_Accounts.Where(a => a.AccountId == nt.DealerAccountId).Select(a => a.AcNameAr).FirstOrDefault();

                    HelperAcc.CreateReceiveNoteJournal(nt, isVendor, dealerName,null);
                }
                DB.SubmitChanges();
            }
        }
        #endregion

        private static List<IC_ItemStore> GetAvailableItemsNewYear(DateTime insertTime)
        {
            List<IC_ItemStore> lstNewStore = new List<IC_ItemStore>();
            ERPDataContext DB = new ERPDataContext();
            var data = (from i in DB.IC_ItemStores
                        where i.InsertTime < insertTime.Date
                        where i.ProcessId != (int)Process.SalesOrder
                        group i by new
                        {
                            i.StoreId,
                            i.ItemId,
                            i.Expire,
                            i.Batch,
                            i.Height,
                            i.Length,
                            i.Width,
                            i.ParentItemId,
                            i.M1,
                            i.M2,
                            i.M3,
                            i.QC,
                        } into grp
                        select new
                        {
                            StoreId = grp.Key.StoreId,
                            ItemId = grp.Key.ItemId,
                            CurrentQty = grp.Sum(x => x.IsInTrns ? x.Qty : x.Qty * -1),
                            PiecesCount = grp.Sum(x => x.IsInTrns ? x.PiecesCount : x.PiecesCount * -1),
                            totalPrice = grp.Sum(x => x.IsInTrns ? x.PurchasePrice : x.PurchasePrice * -1),
                            grp.Key.Expire,
                            grp.Key.Batch,
                            grp.Key.Height,
                            grp.Key.Length,
                            grp.Key.Width,
                            grp.Key.ParentItemId,
                            grp.Key.M1,
                            grp.Key.M2,
                            grp.Key.M3,
                            grp.Key.QC
                        }).Distinct().ToList();

            foreach (var row in data)
            {
                if (row.CurrentQty == 0)
                    continue;

                IC_ItemStore it = new IC_ItemStore();
                it.ItemId = row.ItemId;
                it.StoreId = row.StoreId;
                it.Qty = Math.Abs(row.CurrentQty);
                it.PurchasePrice = Math.Abs(row.totalPrice);
                it.ProcessId = (int)Process.OpenBalance;
                it.SourceId = -1;
                it.IsInTrns = row.CurrentQty > 0 ? true : false;
                it.InsertTime = insertTime.Date;
                it.Expire = row.Expire;
                it.Batch = row.Batch;
                it.Height = row.Height;
                it.Length = row.Length;
                it.Width = row.Width;
                it.PiecesCount = Math.Abs(row.PiecesCount);
                it.ParentItemId = row.ParentItemId;
                it.M1 = row.M1;
                it.M2 = row.M2;
                it.M3 = row.M3;
                it.QC = row.QC;
                lstNewStore.Add(it);
            }
            return lstNewStore;
        }

        private static void DeleteFromTables(DateTime insertTime)
        {
            string date_format = insertTime.ToString("yyyy-MM-dd HH:mm:ss");

            SqlConnection connect = new SqlConnection(DAL.Config.ConnectionString);
            connect.Open();
            SqlCommand cmd;

            #region Account
            string query = "delete jd from ACC_JournalDetail jd join ACC_journal j on jd.JournalId = j.JournalId where j.InsertDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete j from ACC_Journal j where j.InsertDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete cn from ACC_CashNote cn where cn.NoteDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete ct from ACC_CashTransfer ct where ct.TransferDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete np from ACC_NotesPayable np where np.RegDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete nr from ACC_NotesReceivable nr where nr.RegDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete rv from ACC_RevExpEntry rv where rv.EntryDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete dc from ACC_DebitCreditNote dc where dc.NoteDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();
            #endregion

            #region Invoices
            #region PR_Invoices
            query = "delete d from PR_InvoiceDetail d join PR_Invoice p on d.PR_InvoiceId = p.PR_InvoiceId where p.InvoiceDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete d from PR_InvoiceExpense d join PR_Invoice p on d.PR_InvoiceId = p.PR_InvoiceId where p.InvoiceDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete d from PR_InvoiceOtherExpense d join PR_Invoice p on d.PR_InvoiceId = p.PR_InvoiceId where p.InvoiceDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete p from PR_Invoice p where p.InvoiceDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();
            #endregion

            #region PR_Return
            query = "delete d from PR_ReturnDetail d join PR_Return p on d.PR_ReturnId = p.PR_ReturnId where p.ReturnDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete d from PR_ReturnExpense d join PR_Return p on d.PR_ReturnId = p.PR_ReturnId where p.ReturnDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete p from PR_Return p where p.ReturnDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();
            #endregion

            #region PR_Order
            query = "delete d from PR_PurchaseOrderDetail d join PR_PurchaseOrder p on d.PR_POId = p.PR_POId where p.InvoiceDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete p from PR_PurchaseOrder p where p.InvoiceDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();
            #endregion

            #region SLPosting
            query = "delete d from SL_InvoicePost d where d.PostDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();
            #endregion

            #region SL_Invoices
            query = "delete d from SL_InvoiceDetail d join SL_Invoice p on d.SL_InvoiceId = p.SL_InvoiceId where p.InvoiceDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete p from SL_Invoice p where p.InvoiceDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();
            #endregion

            #region SL_Return
            query = query = "delete d from SL_ReturnDetail d join SL_Return p on d.SL_ReturnId = p.SL_ReturnId where p.ReturnDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete p from SL_Return p where p.ReturnDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();
            #endregion

            #region SL_Quote
            query = "delete d from SL_QuoteDetail d join SL_Quote p on d.SL_QuoteId = p.SL_QuoteId where p.QuoteDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete p from SL_Quote p where p.QuoteDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();
            #endregion

            #region SL_SalesOrder
            query = "delete d from SL_SalesOrderDetail d join SL_SalesOrder p on d.SL_SalesOrderId = p.SL_SalesOrderId where p.SalesOrderDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete p from SL_SalesOrder p where p.SalesOrderDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();
            #endregion

            #region JO
            query = "delete d from JO_JobOrderDetail d join JO_JobOrder p on d.JoId = p.JobOrderId where p.RegDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete d from JO_JobOrderItem d join JO_JobOrder p on d.JoId = p.JobOrderId where p.RegDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete p from JO_JobOrder p where p.RegDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            #endregion

            #endregion

            #region IC
            query = "delete p from IC_ItemPriceChange p  where p.ChangeDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete p from IC_ItemQtyChange p  where p.ChangeDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete p from IC_ItemStore p  where p.InsertTime < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            #region Obsolete
            query = "delete from IC_OpenBalance";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from IC_StockTaking_Detail";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from IC_StockTaking";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();
            #endregion

            query = "delete d from IC_DamagedDetail d join IC_Damaged p on d.DamagedId = p.DamagedId where p.DamagedDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete p from IC_Damaged p where p.DamagedDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete d from IC_InTrnsDetail d join IC_InTrns p on d.InTrnsId = p.InTrnsId where p.InTrnsDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete p from IC_InTrns p where p.InTrnsDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete d from IC_OutTrnsDetail d join IC_OutTrns p on d.OutTrnsId = p.OutTrnsId where p.OutTrnsDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete p from IC_OutTrns p where p.OutTrnsDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete d from IC_StoreMoveDetail d join IC_StoreMove p on d.StoreMoveId = p.StoreMoveId where p.StoreMoveDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete p from IC_StoreMove p where p.StoreMoveDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();
            #endregion

            #region Manufacturing
            query = "delete d from Manf_QCDetail d join Manf_QC p on d.Manf_QCId = p.Manf_QCId where p.Manf_QCDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete p from Manf_QC p where p.Manf_QCDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete d from ManfDetail d join Manufacturing p on d.Manf_Id = p.Manf_Id where p.StartDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete d from Manf_Expense d join Manufacturing p on d.Manf_Id = p.Manf_Id where p.StartDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete d from Manf_Wage d join Manufacturing p on d.Manf_Id = p.Manf_Id where p.StartDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete d from ManfDamage d join Manufacturing p on d.Manf_Id = p.Manf_Id where p.StartDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete d from ManfProduct d join Manufacturing p on d.Manf_Id = p.Manf_Id where p.StartDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete p from Manufacturing p where p.StartDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();
            #endregion

            #region HR
            query = "delete d from HR_Absence d where d.StartDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete d from HR_Delay d where d.DelayDay < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete d from HR_OverTime d where d.OverTimeDay < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete d from HR_Penalty d where d.PenaltyDay < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete d from HR_Reward d where d.RewardDay < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete d from HR_Vacation d where d.StartDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete d from HR_Attendance d where d.Day < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete d from HR_AttendLog d where d.Time < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete d from HR_AttendLogAdv d where d.Time < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete d from HR_PayDetail d join HR_Pay p on d.PayId = p.PayId where p.DueDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete p from HR_Pay p where p.DueDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete d from HR_LoanDetail d join HR_Loan p on d.LoanId = p.LoanId where p.RegDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete p from HR_Loan p where p.RegDate < CONVERT(datetime,'" + date_format + "',120)";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();
            #endregion

            connect.Close();
        }

        private static void DeleteFromTables()
        {
            SqlConnection connect = new SqlConnection(DAL.Config.ConnectionString);
            connect.Open();
            SqlCommand cmd;

            #region Account
            string query = "delete from ACC_JournalDetail";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from ACC_Journal";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from ACC_CashNote";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from ACC_CashTransfer";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from ACC_NotesPayable";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from ACC_NotesReceivable";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from ACC_RevExpEntry";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from ACC_DebitCreditNote";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();
            #endregion

            #region Invoices
            //PR
            query = "delete from PR_InvoiceDetail";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from PR_InvoiceExpense";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from PR_InvoiceOtherExpense";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from PR_Invoice";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from PR_InvoiceDetail";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from PR_ReturnDetail";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from PR_ReturnExpense";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from PR_Return";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            //query = "delete from PR_PurchaseOrderDetail";
            //cmd = new SqlCommand(query, connect);
            //cmd.ExecuteScalar();

            //query = "delete from PR_PurchaseOrder";
            //cmd = new SqlCommand(query, connect);
            //cmd.ExecuteScalar();

            //query = "delete from PR_QuoteDetail";
            //cmd = new SqlCommand(query, connect);
            //cmd.ExecuteScalar();

            //query = "delete from PR_Quote";
            //cmd = new SqlCommand(query, connect);
            //cmd.ExecuteScalar();

            //query = "delete from PR_PurchaseRequestDetail";
            //cmd = new SqlCommand(query, connect);
            //cmd.ExecuteScalar();

            //query = "delete from PR_PurchaseRequest";
            //cmd = new SqlCommand(query, connect);
            //cmd.ExecuteScalar();


            //SL
            query = "delete from SL_InvoicePost";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from SL_InvoiceDetail";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from SL_Invoice";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from SL_ReturnDetail";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from SL_Return";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            //query = "delete from SL_QuoteDetail";
            //cmd = new SqlCommand(query, connect);
            //cmd.ExecuteScalar();

            //query = "delete from SL_Quote";
            //cmd = new SqlCommand(query, connect);
            //cmd.ExecuteScalar();

            //query = "delete from SL_SalesOrderDetail";
            //cmd = new SqlCommand(query, connect);
            //cmd.ExecuteScalar();

            //query = "delete from SL_SalesOrder";
            //cmd = new SqlCommand(query, connect);
            //cmd.ExecuteScalar();

            //query = "delete from JO_JobOrderDetail";
            //cmd = new SqlCommand(query, connect);
            //cmd.ExecuteScalar();

            //query = "delete from JO_JobOrder";
            //cmd = new SqlCommand(query, connect);
            //cmd.ExecuteScalar();
            #endregion

            #region IC
            query = "delete from IC_ItemPriceChange";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from IC_ItemQtyChange";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from IC_ItemStore";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from IC_OpenBalance";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from IC_StockTaking_Detail";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from IC_StockTaking";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from IC_DamagedDetail";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from IC_Damaged";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from IC_InTrnsDetail";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from IC_InTrns";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from IC_OutTrnsDetail";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from IC_OutTrns";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from IC_StoreMoveDetail";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from IC_StoreMove";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();
            #endregion

            #region Manufacturing
            query = "delete from Manf_QCDetail";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from Manf_QC";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from ManfDetail";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from Manf_Expense";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from Manf_Wage";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from ManfDamage";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from ManfProduct";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from Manufacturing";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();
            #endregion

            #region HR
            query = "delete from HR_Absence";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from HR_Delay";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from HR_Mission";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from HR_OverTime";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from HR_Penalty";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from HR_Reward";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from HR_Vacation";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from HR_Attendance";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from HR_Attendance";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from HR_AttendLogAdv";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from HR_PayDetail";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from HR_Pay";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from HR_LoanDetail";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();

            query = "delete from HR_Loan";
            cmd = new SqlCommand(query, connect);
            cmd.ExecuteScalar();
            #endregion

            connect.Close();
        }
        
        private static void ResetIdentity()
        {
            SqlConnection connect = new SqlConnection(DAL.Config.ConnectionString);
            connect.Open();

            #region Account
            SqlCommand cmd;
            string query = "select IDENT_CURRENT ('ACC_JournalDetail')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.ACC_JournalDetail', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('ACC_Journal')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.ACC_Journal', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('ACC_CashNote')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.ACC_CashNote', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('ACC_CashTransfer')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.ACC_CashTransfer', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('ACC_NotesPayable')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.ACC_NotesPayable', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('ACC_NotesReceivable')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.ACC_NotesReceivable', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('ACC_RevExpEntry')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.ACC_RevExpEntry', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('ACC_DebitCreditNote')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.ACC_DebitCreditNote', reseed,0)";
                cmd.ExecuteNonQuery();
            }
            #endregion

            #region Invoices
            query = "select IDENT_CURRENT ('PR_InvoiceDetail')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.PR_InvoiceDetail', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('PR_InvoiceExpense')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.PR_InvoiceExpense', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('PR_Invoice')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.PR_Invoice', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('PR_ReturnDetail')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.PR_ReturnDetail', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('PR_ReturnExpense')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.PR_ReturnExpense', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('PR_Return')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.PR_Return', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('PR_PurchaseOrderDetail')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.PR_PurchaseOrderDetail', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('PR_PurchaseOrder')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.PR_PurchaseOrder', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('SL_InvoicePost')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.SL_InvoicePost', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('SL_InvoiceDetail')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.SL_InvoiceDetail', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('SL_Invoice')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.SL_Invoice', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('SL_ReturnDetail')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.SL_ReturnDetail', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('SL_Return')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.SL_Return', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('SL_QuoteDetail')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.SL_QuoteDetail', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('SL_Quote')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.SL_Quote', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('SL_SalesOrderDetail')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.SL_SalesOrderDetail', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('SL_SalesOrder')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.SL_SalesOrder', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('JO_JobOrderDetail')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.JO_JobOrderDetail', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('JO_JobOrder')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.JO_JobOrder', reseed,0)";
                cmd.ExecuteNonQuery();
            }
            #endregion

            #region IC
            query = "select IDENT_CURRENT ('IC_ItemPriceChange')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.IC_ItemPriceChange', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('IC_ItemQtyChange')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.IC_ItemQtyChange', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('IC_ItemStore')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.IC_ItemStore', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('IC_OpenBalance')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.IC_OpenBalance', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('IC_StockTaking_Detail')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.IC_StockTaking_Detail', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('IC_StockTaking')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.IC_StockTaking', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('IC_DamagedDetail')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.IC_DamagedDetail', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('IC_Damaged')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.IC_Damaged', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('IC_InTrnsDetail')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.IC_InTrnsDetail', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('IC_InTrns')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.IC_InTrns', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('IC_OutTrnsDetail')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.IC_OutTrnsDetail', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('IC_OutTrns')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.IC_OutTrns', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('IC_StoreMoveDetail')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.IC_StoreMoveDetail', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('IC_StoreMove')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.IC_StoreMove', reseed,0)";
                cmd.ExecuteNonQuery();
            }
            #endregion

            #region Manufacturing

            query = "select IDENT_CURRENT ('Manf_QCDetail')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.Manf_QCDetail', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('Manf_QC')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.Manf_QC', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('ManfDetail')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.ManfDetail', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('Manf_Expense')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.Manf_Expense', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('Manf_Wage')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.Manf_Wage', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('ManfDamage')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.ManfDamage', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('ManfProduct')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.ManfProduct', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('Manufacturing')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.Manufacturing', reseed,0)";
                cmd.ExecuteNonQuery();
            }
            #endregion

            #region HR
            query = "select IDENT_CURRENT ('HR_AttendLog')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.HR_AttendLog', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('HR_PayDetail')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.HR_PayDetail', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('HR_LoanDetail')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.HR_LoanDetail', reseed,0)";
                cmd.ExecuteNonQuery();
            }

            query = "select IDENT_CURRENT ('HR_Loan')";
            cmd = new SqlCommand(query, connect);
            if (cmd.ExecuteScalar().ToString() != "1")
            {
                cmd.CommandText = "DBCC checkident('dbo.HR_Loan', reseed,0)";
                cmd.ExecuteNonQuery();
            }
            #endregion

            connect.Close();
        }

        private void LoadAssetLiabTree(DateTime insertTime)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var balances = (from d in DB.ACC_JournalDetails
                            join ac in DB.ACC_Accounts on d.AccountId equals ac.AccountId
                            where ac.AcNumber.StartsWith("1") || ac.AcNumber.StartsWith("2")
                            join j in DB.ACC_Journals
                            on d.JournalId equals j.JournalId
                            where j.InsertDate < insertTime
                            group d by new { d.AccountId, d.CrncId } into grp
                            select new
                            {
                                CurrencyId = grp.Key.CrncId,
                                AccountId = grp.Key.AccountId,
                                Balance = grp.Sum(d => d.Credit - d.Debit),
                            }).ToList();


            foreach (var d in balances)
            {
                if (d.Balance > 0)
                    OpenJournals.Rows.Add(d.AccountId, d.CurrencyId, 0, d.Balance);
                else
                    OpenJournals.Rows.Add(d.AccountId, d.CurrencyId, Math.Abs(d.Balance), 0);

            }

            progressBarControl1.Increment(1);


        }


    }
}
