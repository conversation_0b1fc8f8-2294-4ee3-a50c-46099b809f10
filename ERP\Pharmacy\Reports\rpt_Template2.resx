<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="xrSubreport1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 0</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="xrSubreport1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>100, 23</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="Detail.HeightF" type="System.Single, mscorlib">
    <value>23</value>
  </data>
  <data name="Detail.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="xrLabel13.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14.25pt</value>
  </data>
  <data name="xrLabel13.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>415.1249, 103.208351</value>
  </data>
  <data name="xrLabel13.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>312.499969, 19.0000153</value>
  </data>
  <data name="xrLabel13.Text" xml:space="preserve">
    <value>lblReportName</value>
  </data>
  <data name="xrLabel13.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel12.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14.25pt</value>
  </data>
  <data name="xrLabel12.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>671.3751, 81.54167</value>
  </data>
  <data name="xrLabel12.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>115.624878, 21.6666718</value>
  </data>
  <data name="xrLabel12.Text" xml:space="preserve">
    <value>/عنوان الجهة </value>
  </data>
  <data name="xrLabel12.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel11.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14.25pt</value>
  </data>
  <data name="xrLabel11.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>671.375, 61.5417175</value>
  </data>
  <data name="xrLabel11.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>115.624878, 21.6666718</value>
  </data>
  <data name="xrLabel11.Text" xml:space="preserve">
    <value>/اســــم الجهـــة </value>
  </data>
  <data name="xrLabel11.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel10.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14.25pt</value>
  </data>
  <data name="xrLabel10.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 122.208374</value>
  </data>
  <data name="xrLabel10.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>786.999939, 16.7500153</value>
  </data>
  <data name="xrLabel10.Text" xml:space="preserve">
    <value>lblFilters
</value>
  </data>
  <data name="xrLabel10.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel9.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 7pt</value>
  </data>
  <data name="xrLabel9.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 102.208405</value>
  </data>
  <data name="xrLabel9.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>415.124939, 19.0000153</value>
  </data>
  <data name="xrLabel9.Text" xml:space="preserve">
    <value>قيمــــــــــة المبــــالــــــغ المحـــــصلــــة من المــموليــــن طبقـــــــا للنمــــاذج الــــمـــــرفقــــة وعـــددهــــا   (   3  ) نــــمــــــوذج </value>
  </data>
  <data name="xrLabel9.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel7.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14.25pt</value>
  </data>
  <data name="xrLabel7.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>727.6249, 103.208351</value>
  </data>
  <data name="xrLabel7.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>59.3751221, 19.0000153</value>
  </data>
  <data name="xrLabel7.Text" xml:space="preserve">
    <value> /تليفون </value>
  </data>
  <data name="xrLabel7.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel8.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14.25pt</value>
  </data>
  <data name="xrLabel8.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>415.1249, 84.20833</value>
  </data>
  <data name="xrLabel8.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>256.2501, 19.0000153</value>
  </data>
  <data name="xrLabel8.Text" xml:space="preserve">
    <value>lblReportName</value>
  </data>
  <data name="xrLabel8.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel5.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt</value>
  </data>
  <data name="xrLabel5.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 83.20839</value>
  </data>
  <data name="xrLabel5.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>415.124939, 19.0000153</value>
  </data>
  <data name="xrLabel5.Text" xml:space="preserve">
    <value>مرفق مع الحوالة رقم (                     ) بمبلغ  (                   ) (فقط    لاغير )</value>
  </data>
  <data name="xrLabel5.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel6.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14.25pt</value>
  </data>
  <data name="xrLabel6.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 64.20837</value>
  </data>
  <data name="xrLabel6.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>415.124939, 19.0000153</value>
  </data>
  <data name="xrLabel6.Text" xml:space="preserve">
    <value>تــحـــــــــيـــة طــيــــبـــــــة وبــــعـد </value>
  </data>
  <data name="xrLabel6.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel4.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 7pt, style=Bold</value>
  </data>
  <data name="xrLabel4.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 42.5416946</value>
  </data>
  <data name="xrLabel4.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>415.124939, 19.0000153</value>
  </data>
  <data name="xrLabel4.Text" xml:space="preserve">
    <value>الســــيد / مديــر عــــام الادارة العامــــــة لتــجـمـيـــــــــع نمــــــــازج الخصـــــم والتــحصـيل  تحـــــت حســــــاب الضريبـــــة </value>
  </data>
  <data name="xrLabel4.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel3.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14.25pt</value>
  </data>
  <data name="xrLabel3.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 23.5416737</value>
  </data>
  <data name="xrLabel3.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>415.124939, 19.0000134</value>
  </data>
  <data name="xrLabel3.Text" xml:space="preserve">
    <value>نموذج 41 معــدل ( خ.أ.ت) ضرائب</value>
  </data>
  <data name="xrLabel3.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel2.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14.25pt</value>
  </data>
  <data name="xrLabel2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>415.1249, 42.5416946</value>
  </data>
  <data name="xrLabel2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>371.8751, 19.0000153</value>
  </data>
  <data name="xrLabel2.Text" xml:space="preserve">
    <value>مصلحة الضرائب العامة</value>
  </data>
  <data name="xrLabel2.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14.25pt</value>
  </data>
  <data name="xrLabel1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>415.1249, 23.5416737</value>
  </data>
  <data name="xrLabel1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>371.8751, 19.0000134</value>
  </data>
  <data name="xrLabel1.Text" xml:space="preserve">
    <value>وزارة الــمــالــيــــــــــة </value>
  </data>
  <data name="xrLabel1.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lblDateFilter.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt</value>
  </data>
  <data name="lblDateFilter.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>97.5, 139.916641</value>
  </data>
  <data name="lblDateFilter.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>600.0001, 16.75</value>
  </data>
  <data name="lblDateFilter.Text" xml:space="preserve">
    <value>lblDateFilter</value>
  </data>
  <data name="lblDateFilter.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopCenter</value>
  </data>
  <data name="lblDateFilter.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblReportName.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lblReportName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>432.916656, 141.916656</value>
  </data>
  <data name="lblReportName.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>264.583435, 19.0000153</value>
  </data>
  <data name="lblReportName.Text" xml:space="preserve">
    <value>lblReportName</value>
  </data>
  <data name="lblReportName.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopCenter</value>
  </data>
  <data name="lblReportName.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="picLogo.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>22.4999981, 139.916641</value>
  </data>
  <data name="picLogo.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>70, 18.50003</value>
  </data>
  <data name="picLogo.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblCompName.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14.25pt</value>
  </data>
  <data name="lblCompName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>415.1249, 61.5417175</value>
  </data>
  <data name="lblCompName.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>256.2501, 21.6666718</value>
  </data>
  <data name="lblCompName.Text" xml:space="preserve">
    <value>lblCompName</value>
  </data>
  <data name="lblCompName.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lblFilter.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt</value>
  </data>
  <data name="lblFilter.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>22.4999981, 160.916672</value>
  </data>
  <data name="lblFilter.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>764.5, 16.75</value>
  </data>
  <data name="lblFilter.Text" xml:space="preserve">
    <value>lblFilters
</value>
  </data>
  <data name="lblFilter.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopCenter</value>
  </data>
  <data name="lblFilter.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="TopMargin.HeightF" type="System.Single, mscorlib">
    <value>182</value>
  </data>
  <data name="TopMargin.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleJustify</value>
  </data>
  <data name="xrPageInfo2.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="xrPageInfo2.Format" xml:space="preserve">
    <value>Printed at {0:dd MM yyyy  h:mm tt}</value>
  </data>
  <data name="xrPageInfo2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>11.45833, 7.000001</value>
  </data>
  <data name="xrPageInfo2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>189.5833, 23</value>
  </data>
  <data name="xrPageInfo2.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrPageInfo1.Format" xml:space="preserve">
    <value>page {0} of {1}</value>
  </data>
  <data name="xrPageInfo1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>337.5, 7</value>
  </data>
  <data name="xrPageInfo1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>100, 23</value>
  </data>
  <data name="xrPageInfo1.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="BottomMargin.HeightF" type="System.Single, mscorlib">
    <value>41</value>
  </data>
  <data name="BottomMargin.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="lbl_FooterText.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 0</value>
  </data>
  <data name="lbl_FooterText.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>787, 46</value>
  </data>
  <data name="lbl_FooterText.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="ReportFooter.HeightF" type="System.Single, mscorlib">
    <value>46</value>
  </data>
  <data name="Detail1.HeightF" type="System.Single, mscorlib">
    <value>100</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.Language" type="System.Globalization.CultureInfo, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>ar-EG</value>
  </metadata>
  <data name="$this.Margins" type="System.Drawing.Printing.Margins, System.Drawing">
    <value>20, 20, 182, 41</value>
  </data>
  <data name="$this.PageHeight" type="System.Int32, mscorlib">
    <value>1169</value>
  </data>
  <data name="$this.PageWidth" type="System.Int32, mscorlib">
    <value>827</value>
  </data>
  <data name="$this.PaperKind" type="System.Drawing.Printing.PaperKind, System.Drawing">
    <value>A4</value>
  </data>
  <data name="$this.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
</root>