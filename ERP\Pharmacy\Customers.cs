﻿using DAL;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Pharmacy
{
    public partial class Customers : Form
    {
        public Customers()
        {
            InitializeComponent();

            //HelperAcc.SettingsAccounts = HelperAcc.Get_Accounts(out revAcc, out expAcc);
            //HelperAcc.ExpensesAcc = expAcc;
            //HelperAcc.RevenuesAcc = revAcc;

            //load user settings
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            fix(2);//Fix Level

        }

        private static void fix(int lvl)
        {
            ERPDataContext db = new ERPDataContext();
            foreach (SL_Customer c in db.SL_Customers)
            {
                Forms.frm_SL_Customer frm = new Forms.frm_SL_Customer(c.CustomerId);
                frm.Show();
                frm.SaveData();
                frm.Close();
            }
            db.SubmitChanges();
        }
    }
}
