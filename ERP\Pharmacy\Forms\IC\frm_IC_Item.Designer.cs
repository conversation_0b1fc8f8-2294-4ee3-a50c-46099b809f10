﻿namespace Pharmacy.Forms
{
    partial class frm_IC_Item
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_IC_Item));
            this.txtMediumUOMFactor = new DevExpress.XtraEditors.TextEdit();
            this.labelControl15 = new DevExpress.XtraEditors.LabelControl();
            this.txtItemCode1 = new DevExpress.XtraEditors.TextEdit();
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtnHelp = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnNew = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnDelete = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnSave = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnList = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.txtItemCode2 = new DevExpress.XtraEditors.TextEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.txtItemNameAr = new DevExpress.XtraEditors.TextEdit();
            this.labelControl18 = new DevExpress.XtraEditors.LabelControl();
            this.btnAddCat = new DevExpress.XtraEditors.SimpleButton();
            this.lkpCategory = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.btn_AddNewInter_Code = new DevExpress.XtraEditors.SimpleButton();
            this.btnAddComp = new DevExpress.XtraEditors.SimpleButton();
            this.lkpComp = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.lstInternationalCodes = new DevExpress.XtraEditors.ImageListBoxControl();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.grdQty = new DevExpress.XtraGrid.GridControl();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.txtMinQty = new DevExpress.XtraEditors.TextEdit();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.txtMaxQty = new DevExpress.XtraEditors.TextEdit();
            this.txtReorder = new DevExpress.XtraEditors.TextEdit();
            this.labelControl27 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl28 = new DevExpress.XtraEditors.LabelControl();
            this.grp_UOM = new System.Windows.Forms.GroupBox();
            this.chk_LargeIsStopped = new DevExpress.XtraEditors.CheckEdit();
            this.chk_MediumIsStopped = new DevExpress.XtraEditors.CheckEdit();
            this.chk_SmallIsStopped = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl37 = new DevExpress.XtraEditors.LabelControl();
            this.lbl_WeightUnit = new DevExpress.XtraEditors.LabelControl();
            this.cmb_WeightUnit = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.txt_LargeUOMCode = new DevExpress.XtraEditors.TextEdit();
            this.labelControl30 = new DevExpress.XtraEditors.LabelControl();
            this.txt_MediumUOMCode = new DevExpress.XtraEditors.TextEdit();
            this.labelControl29 = new DevExpress.XtraEditors.LabelControl();
            this.rdoPrchsUOM2 = new DevExpress.XtraEditors.CheckEdit();
            this.rdoPrchsUOM1 = new DevExpress.XtraEditors.CheckEdit();
            this.rdoPrchsUOM0 = new DevExpress.XtraEditors.CheckEdit();
            this.rdoSellUOM2 = new DevExpress.XtraEditors.CheckEdit();
            this.rdoSellUOM1 = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl24 = new DevExpress.XtraEditors.LabelControl();
            this.rdoSellUOM0 = new DevExpress.XtraEditors.CheckEdit();
            this.textEdit5 = new DevExpress.XtraEditors.TextEdit();
            this.txtLargeUOMFactor = new DevExpress.XtraEditors.TextEdit();
            this.lkpSmallUOM = new DevExpress.XtraEditors.LookUpEdit();
            this.lkpLargeUOM = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl13 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl19 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl23 = new DevExpress.XtraEditors.LabelControl();
            this.btnAddUOM = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl21 = new DevExpress.XtraEditors.LabelControl();
            this.lkpMediumUOM = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl20 = new DevExpress.XtraEditors.LabelControl();
            this.txtSmallUOMPrice = new DevExpress.XtraEditors.SpinEdit();
            this.txtMediumUOMPrice = new DevExpress.XtraEditors.SpinEdit();
            this.txtLargeUOMPrice = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.txtItemNameEn = new DevExpress.XtraEditors.TextEdit();
            this.btnNext = new DevExpress.XtraEditors.SimpleButton();
            this.btnPrev = new DevExpress.XtraEditors.SimpleButton();
            this.btnDelete = new DevExpress.XtraEditors.SimpleButton();
            this.txtInternationalCode = new DevExpress.XtraEditors.TextEdit();
            this.labelControl36 = new DevExpress.XtraEditors.LabelControl();
            this.grp_international = new System.Windows.Forms.GroupBox();
            this.grp_ItemType = new System.Windows.Forms.GroupBox();
            this.chk_VariableWeight = new DevExpress.XtraEditors.CheckEdit();
            this.chk_PricingWithSmall = new DevExpress.XtraEditors.CheckEdit();
            this.chk_IsLibra = new DevExpress.XtraEditors.CheckEdit();
            this.chk_IsPos = new DevExpress.XtraEditors.CheckEdit();
            this.chkIsExpire = new DevExpress.XtraEditors.CheckEdit();
            this.rdoItemType = new DevExpress.XtraEditors.RadioGroup();
            this.chk_IsDeleted = new DevExpress.XtraEditors.CheckEdit();
            this.tab_Control1 = new DevExpress.XtraTab.XtraTabControl();
            this.tab_MainInfo = new DevExpress.XtraTab.XtraTabPage();
            this.labelControl35 = new DevExpress.XtraEditors.LabelControl();
            this.txtAudiancePrice = new DevExpress.XtraEditors.SpinEdit();
            this.grpExtra = new System.Windows.Forms.GroupBox();
            this.tabExtraData = new DevExpress.XtraTab.XtraTabControl();
            this.tab_InventoryLevels = new DevExpress.XtraTab.XtraTabPage();
            this.tabWarranty = new DevExpress.XtraTab.XtraTabPage();
            this.labelControl26 = new DevExpress.XtraEditors.LabelControl();
            this.txt_WarrantyMonths = new DevExpress.XtraEditors.SpinEdit();
            this.tabPriceChange = new DevExpress.XtraTab.XtraTabPage();
            this.cmbChangeSellPrice = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.cmbChangePriceMethod = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.tabDimension = new DevExpress.XtraTab.XtraTabPage();
            this.labelControl22 = new DevExpress.XtraEditors.LabelControl();
            this.txtLength = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl17 = new DevExpress.XtraEditors.LabelControl();
            this.txtWidth = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl12 = new DevExpress.XtraEditors.LabelControl();
            this.txtHeight = new DevExpress.XtraEditors.SpinEdit();
            this.tabDiscount = new DevExpress.XtraTab.XtraTabPage();
            this.lblSalesDiscountRatio = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.txtSalesDiscRatio = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl25 = new DevExpress.XtraEditors.LabelControl();
            this.txtPurchaseDiscRatio = new DevExpress.XtraEditors.SpinEdit();
            this.tabTax = new DevExpress.XtraTab.XtraTabPage();
            this.txtCustomSalesTaxRatio = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl31 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl32 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl33 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl34 = new DevExpress.XtraEditors.LabelControl();
            this.txtCustomPurchasesTaxRatio = new DevExpress.XtraEditors.SpinEdit();
            this.chk_calcTaxBeforeDisc = new DevExpress.XtraEditors.CheckEdit();
            this.txtSalesTaxRatio = new DevExpress.XtraEditors.SpinEdit();
            this.lblPrTaxRatio = new DevExpress.XtraEditors.LabelControl();
            this.labelControl16 = new DevExpress.XtraEditors.LabelControl();
            this.lblPrTaxVal = new DevExpress.XtraEditors.LabelControl();
            this.txtSalesTaxValue = new DevExpress.XtraEditors.SpinEdit();
            this.txtPurchaseTaxValue = new DevExpress.XtraEditors.SpinEdit();
            this.lblSalesTaxRatio = new DevExpress.XtraEditors.LabelControl();
            this.labelControl14 = new DevExpress.XtraEditors.LabelControl();
            this.lblSalesTaxValue = new DevExpress.XtraEditors.LabelControl();
            this.txtPurchaseTaxRatio = new DevExpress.XtraEditors.SpinEdit();
            this.tab_SubTaxes = new DevExpress.XtraTab.XtraTabPage();
            this.grd_SubTaxes = new DevExpress.XtraGrid.GridControl();
            this.gv_SubTaxes = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.IC_ItemSubTaxesId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.SubTaxId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.lkp_SubTaxes = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.col_Rate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.txtPurchasePrice = new DevExpress.XtraEditors.SpinEdit();
            this.txtDesc = new DevExpress.XtraEditors.MemoEdit();
            this.txtDescEn = new DevExpress.XtraEditors.MemoEdit();
            this.tab_Other = new DevExpress.XtraTab.XtraTabPage();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.gridLocation = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_Location = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Store = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_stores = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.grd_Vendors = new DevExpress.XtraGrid.GridControl();
            this.gv_Vendors = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_Vnd_PurchasePrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Vnd_VendorId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_Vendors = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.col_Vnd_ItemId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.tab_image = new DevExpress.XtraTab.XtraTabPage();
            this.btnRemovePic = new DevExpress.XtraEditors.SimpleButton();
            this.btnAddPicture = new DevExpress.XtraEditors.SimpleButton();
            this.itemPhoto = new DevExpress.XtraEditors.PictureEdit();
            this.tab_PricesPerQty = new DevExpress.XtraTab.XtraTabPage();
            this.grd_SalesPerQty = new DevExpress.XtraGrid.GridControl();
            this.gv_SalesPerQty = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_SellPrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_SLQtyNums = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.col_QtyTo = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_QtyFrom = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ItemId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ItemPriceId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.tab_PriceLevels = new DevExpress.XtraTab.XtraTabPage();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.grdSlPLevel = new DevExpress.XtraGrid.GridControl();
            this.gridView7 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colPlLargeUOMPrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repPriceLevels = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.colPlMediumUOMPrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colPlSmallUOMPrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colPLName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colPriceLevelId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.grdPrPLevel = new DevExpress.XtraGrid.GridControl();
            this.gridView6 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colPrsmallUOMPrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colPrPLName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colPrPriceLevelId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.tab_Matrix = new DevExpress.XtraTab.XtraTabPage();
            this.btn_MatrixPrint = new DevExpress.XtraEditors.SimpleButton();
            this.btn_GenMtrx = new DevExpress.XtraEditors.SimpleButton();
            this.grd_Mtrx = new DevExpress.XtraGrid.GridControl();
            this.gv_Matrix = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colLength = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_M_Spin = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.colWidth = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colHeight = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_m_Attribute3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_m_attribute = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.col_m_Attribute2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_m_Attribute1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_m_MinQty = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_m_MaxQty = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_m_ReorderLevel = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_m_LargeUOMPrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_m_MediumUOMPrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_m_SmallUOMPrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_m_PurchasePrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_m_name = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_m_ItemCode2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_m_ItemId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_m_Code1 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.txtMediumUOMFactor.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtItemCode1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtItemCode2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtItemNameAr.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpCategory.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpComp.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lstInternationalCodes)).BeginInit();
            this.groupBox3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grdQty)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMinQty.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMaxQty.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtReorder.Properties)).BeginInit();
            this.grp_UOM.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chk_LargeIsStopped.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_MediumIsStopped.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_SmallIsStopped.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmb_WeightUnit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_LargeUOMCode.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_MediumUOMCode.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rdoPrchsUOM2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rdoPrchsUOM1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rdoPrchsUOM0.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rdoSellUOM2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rdoSellUOM1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rdoSellUOM0.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit5.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtLargeUOMFactor.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpSmallUOM.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpLargeUOM.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpMediumUOM.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSmallUOMPrice.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMediumUOMPrice.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtLargeUOMPrice.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtItemNameEn.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtInternationalCode.Properties)).BeginInit();
            this.grp_international.SuspendLayout();
            this.grp_ItemType.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chk_VariableWeight.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_PricingWithSmall.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_IsLibra.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_IsPos.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkIsExpire.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rdoItemType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_IsDeleted.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tab_Control1)).BeginInit();
            this.tab_Control1.SuspendLayout();
            this.tab_MainInfo.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtAudiancePrice.Properties)).BeginInit();
            this.grpExtra.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.tabExtraData)).BeginInit();
            this.tabExtraData.SuspendLayout();
            this.tab_InventoryLevels.SuspendLayout();
            this.tabWarranty.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txt_WarrantyMonths.Properties)).BeginInit();
            this.tabPriceChange.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbChangeSellPrice.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbChangePriceMethod.Properties)).BeginInit();
            this.tabDimension.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtLength.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtWidth.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtHeight.Properties)).BeginInit();
            this.tabDiscount.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtSalesDiscRatio.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPurchaseDiscRatio.Properties)).BeginInit();
            this.tabTax.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtCustomSalesTaxRatio.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCustomPurchasesTaxRatio.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_calcTaxBeforeDisc.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSalesTaxRatio.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSalesTaxValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPurchaseTaxValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPurchaseTaxRatio.Properties)).BeginInit();
            this.tab_SubTaxes.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grd_SubTaxes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv_SubTaxes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_SubTaxes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPurchasePrice.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDesc.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDescEn.Properties)).BeginInit();
            this.tab_Other.SuspendLayout();
            this.groupBox5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridLocation)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_stores)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grd_Vendors)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv_Vendors)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Vendors)).BeginInit();
            this.tab_image.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.itemPhoto.Properties)).BeginInit();
            this.tab_PricesPerQty.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grd_SalesPerQty)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv_SalesPerQty)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_SLQtyNums)).BeginInit();
            this.tab_PriceLevels.SuspendLayout();
            this.groupBox4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grdSlPLevel)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repPriceLevels)).BeginInit();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grdPrPLevel)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).BeginInit();
            this.tab_Matrix.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grd_Mtrx)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv_Matrix)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_M_Spin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_m_attribute)).BeginInit();
            this.SuspendLayout();
            // 
            // txtMediumUOMFactor
            // 
            resources.ApplyResources(this.txtMediumUOMFactor, "txtMediumUOMFactor");
            this.txtMediumUOMFactor.EnterMoveNextControl = true;
            this.txtMediumUOMFactor.Name = "txtMediumUOMFactor";
            this.txtMediumUOMFactor.Properties.AccessibleDescription = resources.GetString("txtMediumUOMFactor.Properties.AccessibleDescription");
            this.txtMediumUOMFactor.Properties.AccessibleName = resources.GetString("txtMediumUOMFactor.Properties.AccessibleName");
            this.txtMediumUOMFactor.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtMediumUOMFactor.Properties.Appearance.FontSizeDelta")));
            this.txtMediumUOMFactor.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtMediumUOMFactor.Properties.Appearance.FontStyleDelta")));
            this.txtMediumUOMFactor.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtMediumUOMFactor.Properties.Appearance.GradientMode")));
            this.txtMediumUOMFactor.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtMediumUOMFactor.Properties.Appearance.Image")));
            this.txtMediumUOMFactor.Properties.Appearance.Options.UseTextOptions = true;
            this.txtMediumUOMFactor.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.txtMediumUOMFactor.Properties.AutoHeight = ((bool)(resources.GetObject("txtMediumUOMFactor.Properties.AutoHeight")));
            this.txtMediumUOMFactor.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtMediumUOMFactor.Properties.Mask.AutoComplete")));
            this.txtMediumUOMFactor.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtMediumUOMFactor.Properties.Mask.BeepOnError")));
            this.txtMediumUOMFactor.Properties.Mask.EditMask = resources.GetString("txtMediumUOMFactor.Properties.Mask.EditMask");
            this.txtMediumUOMFactor.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtMediumUOMFactor.Properties.Mask.IgnoreMaskBlank")));
            this.txtMediumUOMFactor.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtMediumUOMFactor.Properties.Mask.MaskType")));
            this.txtMediumUOMFactor.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtMediumUOMFactor.Properties.Mask.PlaceHolder")));
            this.txtMediumUOMFactor.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtMediumUOMFactor.Properties.Mask.SaveLiteral")));
            this.txtMediumUOMFactor.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtMediumUOMFactor.Properties.Mask.ShowPlaceHolders")));
            this.txtMediumUOMFactor.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtMediumUOMFactor.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtMediumUOMFactor.Properties.MaxLength = 11;
            this.txtMediumUOMFactor.Properties.NullValuePrompt = resources.GetString("txtMediumUOMFactor.Properties.NullValuePrompt");
            this.txtMediumUOMFactor.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtMediumUOMFactor.Properties.NullValuePromptShowForEmptyValue")));
            this.txtMediumUOMFactor.Modified += new System.EventHandler(this.Editors_Modified);
            this.txtMediumUOMFactor.EditValueChanging += new DevExpress.XtraEditors.Controls.ChangingEventHandler(this.txtPurchasePrice_EditValueChanging);
            this.txtMediumUOMFactor.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtUOM_factor_KeyPress);
            // 
            // labelControl15
            // 
            resources.ApplyResources(this.labelControl15, "labelControl15");
            this.labelControl15.Name = "labelControl15";
            // 
            // txtItemCode1
            // 
            resources.ApplyResources(this.txtItemCode1, "txtItemCode1");
            this.txtItemCode1.EnterMoveNextControl = true;
            this.txtItemCode1.Name = "txtItemCode1";
            this.txtItemCode1.Properties.AccessibleDescription = resources.GetString("txtItemCode1.Properties.AccessibleDescription");
            this.txtItemCode1.Properties.AccessibleName = resources.GetString("txtItemCode1.Properties.AccessibleName");
            this.txtItemCode1.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txtItemCode1.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtItemCode1.Properties.Appearance.FontSizeDelta")));
            this.txtItemCode1.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtItemCode1.Properties.Appearance.FontStyleDelta")));
            this.txtItemCode1.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtItemCode1.Properties.Appearance.GradientMode")));
            this.txtItemCode1.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtItemCode1.Properties.Appearance.Image")));
            this.txtItemCode1.Properties.Appearance.Options.UseTextOptions = true;
            this.txtItemCode1.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtItemCode1.Properties.AutoHeight = ((bool)(resources.GetObject("txtItemCode1.Properties.AutoHeight")));
            this.txtItemCode1.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtItemCode1.Properties.Mask.AutoComplete")));
            this.txtItemCode1.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtItemCode1.Properties.Mask.BeepOnError")));
            this.txtItemCode1.Properties.Mask.EditMask = resources.GetString("txtItemCode1.Properties.Mask.EditMask");
            this.txtItemCode1.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtItemCode1.Properties.Mask.IgnoreMaskBlank")));
            this.txtItemCode1.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtItemCode1.Properties.Mask.MaskType")));
            this.txtItemCode1.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtItemCode1.Properties.Mask.PlaceHolder")));
            this.txtItemCode1.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtItemCode1.Properties.Mask.SaveLiteral")));
            this.txtItemCode1.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtItemCode1.Properties.Mask.ShowPlaceHolders")));
            this.txtItemCode1.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtItemCode1.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtItemCode1.Properties.MaxLength = 10;
            this.txtItemCode1.Properties.NullValuePrompt = resources.GetString("txtItemCode1.Properties.NullValuePrompt");
            this.txtItemCode1.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtItemCode1.Properties.NullValuePromptShowForEmptyValue")));
            this.txtItemCode1.Modified += new System.EventHandler(this.Editors_Modified);
            this.txtItemCode1.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtNumberOnly_KeyPress);
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnSave,
            this.barBtnClose,
            this.barBtnHelp,
            this.barBtnDelete,
            this.barBtnNew,
            this.barBtnList});
            this.barManager1.MaxItemId = 31;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(377, 152);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnHelp),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnNew),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnDelete),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnSave),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnList),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtnHelp
            // 
            resources.ApplyResources(this.barBtnHelp, "barBtnHelp");
            this.barBtnHelp.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnHelp.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtnHelp.Id = 2;
            this.barBtnHelp.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtnHelp.Name = "barBtnHelp";
            this.barBtnHelp.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnHelp.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnHelp_ItemClick);
            // 
            // barBtnNew
            // 
            resources.ApplyResources(this.barBtnNew, "barBtnNew");
            this.barBtnNew.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnNew.Glyph = global::Pharmacy.Properties.Resources._new;
            this.barBtnNew.Id = 26;
            this.barBtnNew.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.N));
            this.barBtnNew.Name = "barBtnNew";
            this.barBtnNew.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnNew.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnNew_ItemClick);
            // 
            // barBtnDelete
            // 
            resources.ApplyResources(this.barBtnDelete, "barBtnDelete");
            this.barBtnDelete.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnDelete.Glyph = global::Pharmacy.Properties.Resources.del;
            this.barBtnDelete.Id = 25;
            this.barBtnDelete.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.D));
            this.barBtnDelete.Name = "barBtnDelete";
            this.barBtnDelete.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnDelete.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnDelete_ItemClick);
            // 
            // barBtnSave
            // 
            resources.ApplyResources(this.barBtnSave, "barBtnSave");
            this.barBtnSave.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnSave.Glyph = global::Pharmacy.Properties.Resources.sve;
            this.barBtnSave.Id = 0;
            this.barBtnSave.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.S));
            this.barBtnSave.Name = "barBtnSave";
            this.barBtnSave.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnSave.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Save_ItemClick);
            // 
            // barBtnList
            // 
            resources.ApplyResources(this.barBtnList, "barBtnList");
            this.barBtnList.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnList.Glyph = global::Pharmacy.Properties.Resources.list32;
            this.barBtnList.Id = 30;
            this.barBtnList.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.T));
            this.barBtnList.Name = "barBtnList";
            this.barBtnList.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnList.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnList_ItemClick);
            // 
            // barBtnClose
            // 
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 1;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Close_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.BarAppearance.Normal.GradientMode")));
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.BarAppearance.Normal.Image")));
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.Dock.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.Dock.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.Dock.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.Dock.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.Dock.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.Dock.GradientMode")));
            this.barAndDockingController1.AppearancesBar.Dock.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.Dock.Image")));
            this.barAndDockingController1.AppearancesBar.Dock.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.Dock.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.GradientMode")));
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Image")));
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontStyleDelta" +
        "")));
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.GradientMode")));
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.Image")));
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontSizeDel" +
        "ta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontStyleDe" +
        "lta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.GradientMod" +
        "e")));
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontSizeDelt" +
        "a")));
            this.barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontStyleDel" +
        "ta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.GradientMode" +
        "")));
            this.barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuBar.GradientMode")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuBar.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.GradientMode")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStrip.GradientMode")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStrip.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontStyleDelta" +
        "")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.GradientMode")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.Panel.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesDocking.Panel.FontSizeDelta")));
            this.barAndDockingController1.AppearancesDocking.Panel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesDocking.Panel.FontStyleDelta")));
            this.barAndDockingController1.AppearancesDocking.Panel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesDocking.Panel.GradientMode")));
            this.barAndDockingController1.AppearancesDocking.Panel.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesDocking.Panel.Image")));
            this.barAndDockingController1.AppearancesDocking.Panel.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.Panel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.PanelCaption.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaption.FontSizeDelta")));
            this.barAndDockingController1.AppearancesDocking.PanelCaption.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaption.FontStyleDelta")));
            this.barAndDockingController1.AppearancesDocking.PanelCaption.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaption.GradientMode")));
            this.barAndDockingController1.AppearancesDocking.PanelCaption.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaption.Image")));
            this.barAndDockingController1.AppearancesDocking.PanelCaption.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.PanelCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesRibbon.Item.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesRibbon.Item.FontSizeDelta")));
            this.barAndDockingController1.AppearancesRibbon.Item.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesRibbon.Item.FontStyleDelta")));
            this.barAndDockingController1.AppearancesRibbon.Item.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesRibbon.Item.GradientMode")));
            this.barAndDockingController1.AppearancesRibbon.Item.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesRibbon.Item.Image")));
            this.barAndDockingController1.AppearancesRibbon.Item.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesRibbon.Item.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesRibbon.PageHeader.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesRibbon.PageHeader.FontSizeDelta")));
            this.barAndDockingController1.AppearancesRibbon.PageHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesRibbon.PageHeader.FontStyleDelta")));
            this.barAndDockingController1.AppearancesRibbon.PageHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesRibbon.PageHeader.GradientMode")));
            this.barAndDockingController1.AppearancesRibbon.PageHeader.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesRibbon.PageHeader.Image")));
            this.barAndDockingController1.AppearancesRibbon.PageHeader.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesRibbon.PageHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlTop.Appearance.FontSizeDelta")));
            this.barDockControlTop.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlTop.Appearance.FontStyleDelta")));
            this.barDockControlTop.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlTop.Appearance.GradientMode")));
            this.barDockControlTop.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlTop.Appearance.Image")));
            this.barDockControlTop.Appearance.Options.UseTextOptions = true;
            this.barDockControlTop.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barDockControlTop.CausesValidation = false;
            // 
            // barDockControlBottom
            // 
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlBottom.Appearance.FontSizeDelta")));
            this.barDockControlBottom.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlBottom.Appearance.FontStyleDelta")));
            this.barDockControlBottom.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlBottom.Appearance.GradientMode")));
            this.barDockControlBottom.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlBottom.Appearance.Image")));
            this.barDockControlBottom.CausesValidation = false;
            // 
            // barDockControlLeft
            // 
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlLeft.Appearance.FontSizeDelta")));
            this.barDockControlLeft.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlLeft.Appearance.FontStyleDelta")));
            this.barDockControlLeft.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlLeft.Appearance.GradientMode")));
            this.barDockControlLeft.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlLeft.Appearance.Image")));
            this.barDockControlLeft.CausesValidation = false;
            // 
            // barDockControlRight
            // 
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlRight.Appearance.FontSizeDelta")));
            this.barDockControlRight.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlRight.Appearance.FontStyleDelta")));
            this.barDockControlRight.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlRight.Appearance.GradientMode")));
            this.barDockControlRight.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlRight.Appearance.Image")));
            this.barDockControlRight.CausesValidation = false;
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemTextEdit1.Mask.AutoComplete")));
            this.repositoryItemTextEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.BeepOnError")));
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemTextEdit1.Mask.MaskType")));
            this.repositoryItemTextEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemTextEdit1.Mask.PlaceHolder")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // labelControl2
            // 
            resources.ApplyResources(this.labelControl2, "labelControl2");
            this.labelControl2.Name = "labelControl2";
            // 
            // txtItemCode2
            // 
            resources.ApplyResources(this.txtItemCode2, "txtItemCode2");
            this.txtItemCode2.EnterMoveNextControl = true;
            this.txtItemCode2.Name = "txtItemCode2";
            this.txtItemCode2.Properties.AccessibleDescription = resources.GetString("txtItemCode2.Properties.AccessibleDescription");
            this.txtItemCode2.Properties.AccessibleName = resources.GetString("txtItemCode2.Properties.AccessibleName");
            this.txtItemCode2.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtItemCode2.Properties.Appearance.FontSizeDelta")));
            this.txtItemCode2.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtItemCode2.Properties.Appearance.FontStyleDelta")));
            this.txtItemCode2.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtItemCode2.Properties.Appearance.GradientMode")));
            this.txtItemCode2.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtItemCode2.Properties.Appearance.Image")));
            this.txtItemCode2.Properties.Appearance.Options.UseTextOptions = true;
            this.txtItemCode2.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtItemCode2.Properties.AutoHeight = ((bool)(resources.GetObject("txtItemCode2.Properties.AutoHeight")));
            this.txtItemCode2.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtItemCode2.Properties.Mask.AutoComplete")));
            this.txtItemCode2.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtItemCode2.Properties.Mask.BeepOnError")));
            this.txtItemCode2.Properties.Mask.EditMask = resources.GetString("txtItemCode2.Properties.Mask.EditMask");
            this.txtItemCode2.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtItemCode2.Properties.Mask.IgnoreMaskBlank")));
            this.txtItemCode2.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtItemCode2.Properties.Mask.MaskType")));
            this.txtItemCode2.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtItemCode2.Properties.Mask.PlaceHolder")));
            this.txtItemCode2.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtItemCode2.Properties.Mask.SaveLiteral")));
            this.txtItemCode2.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtItemCode2.Properties.Mask.ShowPlaceHolders")));
            this.txtItemCode2.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtItemCode2.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtItemCode2.Properties.MaxLength = 50;
            this.txtItemCode2.Properties.NullValuePrompt = resources.GetString("txtItemCode2.Properties.NullValuePrompt");
            this.txtItemCode2.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtItemCode2.Properties.NullValuePromptShowForEmptyValue")));
            this.txtItemCode2.EditValueChanged += new System.EventHandler(this.txtItemCode2_EditValueChanged);
            this.txtItemCode2.Modified += new System.EventHandler(this.Editors_Modified);
            // 
            // labelControl4
            // 
            resources.ApplyResources(this.labelControl4, "labelControl4");
            this.labelControl4.Name = "labelControl4";
            // 
            // txtItemNameAr
            // 
            resources.ApplyResources(this.txtItemNameAr, "txtItemNameAr");
            this.txtItemNameAr.EnterMoveNextControl = true;
            this.txtItemNameAr.Name = "txtItemNameAr";
            this.txtItemNameAr.Properties.AccessibleDescription = resources.GetString("txtItemNameAr.Properties.AccessibleDescription");
            this.txtItemNameAr.Properties.AccessibleName = resources.GetString("txtItemNameAr.Properties.AccessibleName");
            this.txtItemNameAr.Properties.AutoHeight = ((bool)(resources.GetObject("txtItemNameAr.Properties.AutoHeight")));
            this.txtItemNameAr.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtItemNameAr.Properties.Mask.AutoComplete")));
            this.txtItemNameAr.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtItemNameAr.Properties.Mask.BeepOnError")));
            this.txtItemNameAr.Properties.Mask.EditMask = resources.GetString("txtItemNameAr.Properties.Mask.EditMask");
            this.txtItemNameAr.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtItemNameAr.Properties.Mask.IgnoreMaskBlank")));
            this.txtItemNameAr.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtItemNameAr.Properties.Mask.MaskType")));
            this.txtItemNameAr.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtItemNameAr.Properties.Mask.PlaceHolder")));
            this.txtItemNameAr.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtItemNameAr.Properties.Mask.SaveLiteral")));
            this.txtItemNameAr.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtItemNameAr.Properties.Mask.ShowPlaceHolders")));
            this.txtItemNameAr.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtItemNameAr.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtItemNameAr.Properties.MaxLength = 100;
            this.txtItemNameAr.Properties.NullValuePrompt = resources.GetString("txtItemNameAr.Properties.NullValuePrompt");
            this.txtItemNameAr.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtItemNameAr.Properties.NullValuePromptShowForEmptyValue")));
            this.txtItemNameAr.Modified += new System.EventHandler(this.Editors_Modified);
            this.txtItemNameAr.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtItemNameAr_KeyPress);
            // 
            // labelControl18
            // 
            resources.ApplyResources(this.labelControl18, "labelControl18");
            this.labelControl18.Name = "labelControl18";
            // 
            // btnAddCat
            // 
            resources.ApplyResources(this.btnAddCat, "btnAddCat");
            this.btnAddCat.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnAddCat.Image = global::Pharmacy.Properties.Resources.add32;
            this.btnAddCat.Name = "btnAddCat";
            this.btnAddCat.TabStop = false;
            this.btnAddCat.Click += new System.EventHandler(this.btn_AddNewCategory_Click);
            // 
            // lkpCategory
            // 
            resources.ApplyResources(this.lkpCategory, "lkpCategory");
            this.lkpCategory.EnterMoveNextControl = true;
            this.lkpCategory.MenuManager = this.barManager1;
            this.lkpCategory.Name = "lkpCategory";
            this.lkpCategory.Properties.AccessibleDescription = resources.GetString("lkpCategory.Properties.AccessibleDescription");
            this.lkpCategory.Properties.AccessibleName = resources.GetString("lkpCategory.Properties.AccessibleName");
            this.lkpCategory.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkpCategory.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkpCategory.Properties.Appearance.FontSizeDelta")));
            this.lkpCategory.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpCategory.Properties.Appearance.FontStyleDelta")));
            this.lkpCategory.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpCategory.Properties.Appearance.GradientMode")));
            this.lkpCategory.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkpCategory.Properties.Appearance.Image")));
            this.lkpCategory.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpCategory.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpCategory.Properties.AppearanceDropDown.FontSizeDelta = ((int)(resources.GetObject("lkpCategory.Properties.AppearanceDropDown.FontSizeDelta")));
            this.lkpCategory.Properties.AppearanceDropDown.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpCategory.Properties.AppearanceDropDown.FontStyleDelta")));
            this.lkpCategory.Properties.AppearanceDropDown.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpCategory.Properties.AppearanceDropDown.GradientMode")));
            this.lkpCategory.Properties.AppearanceDropDown.Image = ((System.Drawing.Image)(resources.GetObject("lkpCategory.Properties.AppearanceDropDown.Image")));
            this.lkpCategory.Properties.AppearanceDropDown.Options.UseTextOptions = true;
            this.lkpCategory.Properties.AppearanceDropDown.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpCategory.Properties.AppearanceDropDownHeader.FontSizeDelta = ((int)(resources.GetObject("lkpCategory.Properties.AppearanceDropDownHeader.FontSizeDelta")));
            this.lkpCategory.Properties.AppearanceDropDownHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpCategory.Properties.AppearanceDropDownHeader.FontStyleDelta")));
            this.lkpCategory.Properties.AppearanceDropDownHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpCategory.Properties.AppearanceDropDownHeader.GradientMode")));
            this.lkpCategory.Properties.AppearanceDropDownHeader.Image = ((System.Drawing.Image)(resources.GetObject("lkpCategory.Properties.AppearanceDropDownHeader.Image")));
            this.lkpCategory.Properties.AppearanceDropDownHeader.Options.UseTextOptions = true;
            this.lkpCategory.Properties.AppearanceDropDownHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkpCategory.Properties.AppearanceFocused.FontSizeDelta = ((int)(resources.GetObject("lkpCategory.Properties.AppearanceFocused.FontSizeDelta")));
            this.lkpCategory.Properties.AppearanceFocused.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpCategory.Properties.AppearanceFocused.FontStyleDelta")));
            this.lkpCategory.Properties.AppearanceFocused.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpCategory.Properties.AppearanceFocused.GradientMode")));
            this.lkpCategory.Properties.AppearanceFocused.Image = ((System.Drawing.Image)(resources.GetObject("lkpCategory.Properties.AppearanceFocused.Image")));
            this.lkpCategory.Properties.AppearanceFocused.Options.UseTextOptions = true;
            this.lkpCategory.Properties.AppearanceFocused.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkpCategory.Properties.AutoHeight = ((bool)(resources.GetObject("lkpCategory.Properties.AutoHeight")));
            this.lkpCategory.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkpCategory.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpCategory.Properties.Buttons"))))});
            this.lkpCategory.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpCategory.Properties.Columns"), resources.GetString("lkpCategory.Properties.Columns1"), ((int)(resources.GetObject("lkpCategory.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpCategory.Properties.Columns3"))), resources.GetString("lkpCategory.Properties.Columns4"), ((bool)(resources.GetObject("lkpCategory.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpCategory.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpCategory.Properties.Columns7"), resources.GetString("lkpCategory.Properties.Columns8"), ((int)(resources.GetObject("lkpCategory.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpCategory.Properties.Columns10"))), resources.GetString("lkpCategory.Properties.Columns11"), ((bool)(resources.GetObject("lkpCategory.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpCategory.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpCategory.Properties.Columns14"), resources.GetString("lkpCategory.Properties.Columns15"), ((int)(resources.GetObject("lkpCategory.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpCategory.Properties.Columns17"))), resources.GetString("lkpCategory.Properties.Columns18"), ((bool)(resources.GetObject("lkpCategory.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpCategory.Properties.Columns20")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpCategory.Properties.Columns21"), resources.GetString("lkpCategory.Properties.Columns22"), ((int)(resources.GetObject("lkpCategory.Properties.Columns23"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpCategory.Properties.Columns24"))), resources.GetString("lkpCategory.Properties.Columns25"), ((bool)(resources.GetObject("lkpCategory.Properties.Columns26"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpCategory.Properties.Columns27"))))});
            this.lkpCategory.Properties.NullText = resources.GetString("lkpCategory.Properties.NullText");
            this.lkpCategory.Properties.NullValuePrompt = resources.GetString("lkpCategory.Properties.NullValuePrompt");
            this.lkpCategory.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkpCategory.Properties.NullValuePromptShowForEmptyValue")));
            this.lkpCategory.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.lkpCategory.EditValueChanged += new System.EventHandler(this.lkpCategory_EditValueChanged);
            this.lkpCategory.Modified += new System.EventHandler(this.Editors_Modified);
            // 
            // labelControl11
            // 
            resources.ApplyResources(this.labelControl11, "labelControl11");
            this.labelControl11.Name = "labelControl11";
            // 
            // btn_AddNewInter_Code
            // 
            resources.ApplyResources(this.btn_AddNewInter_Code, "btn_AddNewInter_Code");
            this.btn_AddNewInter_Code.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btn_AddNewInter_Code.Image = global::Pharmacy.Properties.Resources.add32;
            this.btn_AddNewInter_Code.Name = "btn_AddNewInter_Code";
            this.btn_AddNewInter_Code.Click += new System.EventHandler(this.btn_AddNewInter_Code_Click);
            // 
            // btnAddComp
            // 
            resources.ApplyResources(this.btnAddComp, "btnAddComp");
            this.btnAddComp.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnAddComp.Image = global::Pharmacy.Properties.Resources.add32;
            this.btnAddComp.Name = "btnAddComp";
            this.btnAddComp.TabStop = false;
            this.btnAddComp.Click += new System.EventHandler(this.btn_AddNewCompany_Click);
            // 
            // lkpComp
            // 
            resources.ApplyResources(this.lkpComp, "lkpComp");
            this.lkpComp.EnterMoveNextControl = true;
            this.lkpComp.MenuManager = this.barManager1;
            this.lkpComp.Name = "lkpComp";
            this.lkpComp.Properties.AccessibleDescription = resources.GetString("lkpComp.Properties.AccessibleDescription");
            this.lkpComp.Properties.AccessibleName = resources.GetString("lkpComp.Properties.AccessibleName");
            this.lkpComp.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkpComp.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkpComp.Properties.Appearance.FontSizeDelta")));
            this.lkpComp.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpComp.Properties.Appearance.FontStyleDelta")));
            this.lkpComp.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpComp.Properties.Appearance.GradientMode")));
            this.lkpComp.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkpComp.Properties.Appearance.Image")));
            this.lkpComp.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpComp.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpComp.Properties.AppearanceDropDown.FontSizeDelta = ((int)(resources.GetObject("lkpComp.Properties.AppearanceDropDown.FontSizeDelta")));
            this.lkpComp.Properties.AppearanceDropDown.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpComp.Properties.AppearanceDropDown.FontStyleDelta")));
            this.lkpComp.Properties.AppearanceDropDown.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpComp.Properties.AppearanceDropDown.GradientMode")));
            this.lkpComp.Properties.AppearanceDropDown.Image = ((System.Drawing.Image)(resources.GetObject("lkpComp.Properties.AppearanceDropDown.Image")));
            this.lkpComp.Properties.AppearanceDropDown.Options.UseTextOptions = true;
            this.lkpComp.Properties.AppearanceDropDown.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpComp.Properties.AppearanceDropDownHeader.FontSizeDelta = ((int)(resources.GetObject("lkpComp.Properties.AppearanceDropDownHeader.FontSizeDelta")));
            this.lkpComp.Properties.AppearanceDropDownHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpComp.Properties.AppearanceDropDownHeader.FontStyleDelta")));
            this.lkpComp.Properties.AppearanceDropDownHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpComp.Properties.AppearanceDropDownHeader.GradientMode")));
            this.lkpComp.Properties.AppearanceDropDownHeader.Image = ((System.Drawing.Image)(resources.GetObject("lkpComp.Properties.AppearanceDropDownHeader.Image")));
            this.lkpComp.Properties.AppearanceDropDownHeader.Options.UseTextOptions = true;
            this.lkpComp.Properties.AppearanceDropDownHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkpComp.Properties.AppearanceFocused.FontSizeDelta = ((int)(resources.GetObject("lkpComp.Properties.AppearanceFocused.FontSizeDelta")));
            this.lkpComp.Properties.AppearanceFocused.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpComp.Properties.AppearanceFocused.FontStyleDelta")));
            this.lkpComp.Properties.AppearanceFocused.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpComp.Properties.AppearanceFocused.GradientMode")));
            this.lkpComp.Properties.AppearanceFocused.Image = ((System.Drawing.Image)(resources.GetObject("lkpComp.Properties.AppearanceFocused.Image")));
            this.lkpComp.Properties.AppearanceFocused.Options.UseTextOptions = true;
            this.lkpComp.Properties.AppearanceFocused.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkpComp.Properties.AutoHeight = ((bool)(resources.GetObject("lkpComp.Properties.AutoHeight")));
            this.lkpComp.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkpComp.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpComp.Properties.Buttons"))))});
            this.lkpComp.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpComp.Properties.Columns"), resources.GetString("lkpComp.Properties.Columns1"), ((int)(resources.GetObject("lkpComp.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpComp.Properties.Columns3"))), resources.GetString("lkpComp.Properties.Columns4"), ((bool)(resources.GetObject("lkpComp.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpComp.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpComp.Properties.Columns7"), resources.GetString("lkpComp.Properties.Columns8"), ((int)(resources.GetObject("lkpComp.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpComp.Properties.Columns10"))), resources.GetString("lkpComp.Properties.Columns11"), ((bool)(resources.GetObject("lkpComp.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpComp.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpComp.Properties.Columns14"), resources.GetString("lkpComp.Properties.Columns15"), ((int)(resources.GetObject("lkpComp.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpComp.Properties.Columns17"))), resources.GetString("lkpComp.Properties.Columns18"), ((bool)(resources.GetObject("lkpComp.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpComp.Properties.Columns20"))))});
            this.lkpComp.Properties.NullText = resources.GetString("lkpComp.Properties.NullText");
            this.lkpComp.Properties.NullValuePrompt = resources.GetString("lkpComp.Properties.NullValuePrompt");
            this.lkpComp.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkpComp.Properties.NullValuePromptShowForEmptyValue")));
            this.lkpComp.Modified += new System.EventHandler(this.Editors_Modified);
            // 
            // labelControl6
            // 
            resources.ApplyResources(this.labelControl6, "labelControl6");
            this.labelControl6.Name = "labelControl6";
            // 
            // lstInternationalCodes
            // 
            resources.ApplyResources(this.lstInternationalCodes, "lstInternationalCodes");
            this.lstInternationalCodes.Name = "lstInternationalCodes";
            this.lstInternationalCodes.TabStop = false;
            // 
            // groupBox3
            // 
            resources.ApplyResources(this.groupBox3, "groupBox3");
            this.groupBox3.Controls.Add(this.grdQty);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.TabStop = false;
            // 
            // grdQty
            // 
            resources.ApplyResources(this.grdQty, "grdQty");
            this.grdQty.Cursor = System.Windows.Forms.Cursors.Default;
            this.grdQty.EmbeddedNavigator.AccessibleDescription = resources.GetString("grdQty.EmbeddedNavigator.AccessibleDescription");
            this.grdQty.EmbeddedNavigator.AccessibleName = resources.GetString("grdQty.EmbeddedNavigator.AccessibleName");
            this.grdQty.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grdQty.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grdQty.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grdQty.EmbeddedNavigator.Anchor")));
            this.grdQty.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grdQty.EmbeddedNavigator.BackgroundImage")));
            this.grdQty.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grdQty.EmbeddedNavigator.BackgroundImageLayout")));
            this.grdQty.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grdQty.EmbeddedNavigator.ImeMode")));
            this.grdQty.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grdQty.EmbeddedNavigator.MaximumSize")));
            this.grdQty.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grdQty.EmbeddedNavigator.TextLocation")));
            this.grdQty.EmbeddedNavigator.ToolTip = resources.GetString("grdQty.EmbeddedNavigator.ToolTip");
            this.grdQty.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grdQty.EmbeddedNavigator.ToolTipIconType")));
            this.grdQty.EmbeddedNavigator.ToolTipTitle = resources.GetString("grdQty.EmbeddedNavigator.ToolTipTitle");
            this.grdQty.MainView = this.gridView2;
            this.grdQty.MenuManager = this.barManager1;
            this.grdQty.Name = "grdQty";
            this.grdQty.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView2});
            // 
            // gridView2
            // 
            this.gridView2.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView2.Appearance.HeaderPanel.FontSizeDelta")));
            this.gridView2.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView2.Appearance.HeaderPanel.FontStyleDelta")));
            this.gridView2.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView2.Appearance.HeaderPanel.GradientMode")));
            this.gridView2.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView2.Appearance.HeaderPanel.Image")));
            this.gridView2.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView2.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView2.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gridView2.Appearance.Row.FontSizeDelta")));
            this.gridView2.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView2.Appearance.Row.FontStyleDelta")));
            this.gridView2.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView2.Appearance.Row.GradientMode")));
            this.gridView2.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView2.Appearance.Row.Image")));
            this.gridView2.Appearance.Row.Options.UseTextOptions = true;
            this.gridView2.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.gridView2, "gridView2");
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn2,
            this.gridColumn4,
            this.gridColumn15});
            this.gridView2.GridControl = this.grdQty;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsBehavior.Editable = false;
            this.gridView2.OptionsBehavior.FocusLeaveOnTab = true;
            this.gridView2.OptionsBehavior.ReadOnly = true;
            this.gridView2.OptionsNavigation.EnterMoveNextColumn = true;
            this.gridView2.OptionsView.AnimationType = DevExpress.XtraGrid.Views.Base.GridAnimationType.NeverAnimate;
            this.gridView2.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            this.gridView2.OptionsView.ShowIndicator = false;
            this.gridView2.PaintStyleName = "UltraFlat";
            // 
            // gridColumn2
            // 
            resources.ApplyResources(this.gridColumn2, "gridColumn2");
            this.gridColumn2.DisplayFormat.FormatString = "n3";
            this.gridColumn2.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn2.FieldName = "Qnt";
            this.gridColumn2.Name = "gridColumn2";
            // 
            // gridColumn4
            // 
            resources.ApplyResources(this.gridColumn4, "gridColumn4");
            this.gridColumn4.FieldName = "StoreNameAr";
            this.gridColumn4.Name = "gridColumn4";
            // 
            // gridColumn15
            // 
            resources.ApplyResources(this.gridColumn15, "gridColumn15");
            this.gridColumn15.FieldName = "ItemId";
            this.gridColumn15.Name = "gridColumn15";
            // 
            // txtMinQty
            // 
            resources.ApplyResources(this.txtMinQty, "txtMinQty");
            this.txtMinQty.EnterMoveNextControl = true;
            this.txtMinQty.Name = "txtMinQty";
            this.txtMinQty.Properties.AccessibleDescription = resources.GetString("txtMinQty.Properties.AccessibleDescription");
            this.txtMinQty.Properties.AccessibleName = resources.GetString("txtMinQty.Properties.AccessibleName");
            this.txtMinQty.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtMinQty.Properties.Appearance.FontSizeDelta")));
            this.txtMinQty.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtMinQty.Properties.Appearance.FontStyleDelta")));
            this.txtMinQty.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtMinQty.Properties.Appearance.GradientMode")));
            this.txtMinQty.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtMinQty.Properties.Appearance.Image")));
            this.txtMinQty.Properties.Appearance.Options.UseTextOptions = true;
            this.txtMinQty.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.txtMinQty.Properties.AutoHeight = ((bool)(resources.GetObject("txtMinQty.Properties.AutoHeight")));
            this.txtMinQty.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtMinQty.Properties.Mask.AutoComplete")));
            this.txtMinQty.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtMinQty.Properties.Mask.BeepOnError")));
            this.txtMinQty.Properties.Mask.EditMask = resources.GetString("txtMinQty.Properties.Mask.EditMask");
            this.txtMinQty.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtMinQty.Properties.Mask.IgnoreMaskBlank")));
            this.txtMinQty.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtMinQty.Properties.Mask.MaskType")));
            this.txtMinQty.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtMinQty.Properties.Mask.PlaceHolder")));
            this.txtMinQty.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtMinQty.Properties.Mask.SaveLiteral")));
            this.txtMinQty.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtMinQty.Properties.Mask.ShowPlaceHolders")));
            this.txtMinQty.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtMinQty.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtMinQty.Properties.NullText = resources.GetString("txtMinQty.Properties.NullText");
            this.txtMinQty.Properties.NullValuePrompt = resources.GetString("txtMinQty.Properties.NullValuePrompt");
            this.txtMinQty.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtMinQty.Properties.NullValuePromptShowForEmptyValue")));
            this.txtMinQty.Modified += new System.EventHandler(this.Editors_Modified);
            this.txtMinQty.EditValueChanging += new DevExpress.XtraEditors.Controls.ChangingEventHandler(this.txtPurchasePrice_EditValueChanging);
            this.txtMinQty.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtNumberOnly_KeyPress);
            // 
            // labelControl8
            // 
            resources.ApplyResources(this.labelControl8, "labelControl8");
            this.labelControl8.Name = "labelControl8";
            // 
            // txtMaxQty
            // 
            resources.ApplyResources(this.txtMaxQty, "txtMaxQty");
            this.txtMaxQty.EnterMoveNextControl = true;
            this.txtMaxQty.Name = "txtMaxQty";
            this.txtMaxQty.Properties.AccessibleDescription = resources.GetString("txtMaxQty.Properties.AccessibleDescription");
            this.txtMaxQty.Properties.AccessibleName = resources.GetString("txtMaxQty.Properties.AccessibleName");
            this.txtMaxQty.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtMaxQty.Properties.Appearance.FontSizeDelta")));
            this.txtMaxQty.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtMaxQty.Properties.Appearance.FontStyleDelta")));
            this.txtMaxQty.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtMaxQty.Properties.Appearance.GradientMode")));
            this.txtMaxQty.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtMaxQty.Properties.Appearance.Image")));
            this.txtMaxQty.Properties.Appearance.Options.UseTextOptions = true;
            this.txtMaxQty.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.txtMaxQty.Properties.AutoHeight = ((bool)(resources.GetObject("txtMaxQty.Properties.AutoHeight")));
            this.txtMaxQty.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtMaxQty.Properties.Mask.AutoComplete")));
            this.txtMaxQty.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtMaxQty.Properties.Mask.BeepOnError")));
            this.txtMaxQty.Properties.Mask.EditMask = resources.GetString("txtMaxQty.Properties.Mask.EditMask");
            this.txtMaxQty.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtMaxQty.Properties.Mask.IgnoreMaskBlank")));
            this.txtMaxQty.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtMaxQty.Properties.Mask.MaskType")));
            this.txtMaxQty.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtMaxQty.Properties.Mask.PlaceHolder")));
            this.txtMaxQty.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtMaxQty.Properties.Mask.SaveLiteral")));
            this.txtMaxQty.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtMaxQty.Properties.Mask.ShowPlaceHolders")));
            this.txtMaxQty.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtMaxQty.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtMaxQty.Properties.NullText = resources.GetString("txtMaxQty.Properties.NullText");
            this.txtMaxQty.Properties.NullValuePrompt = resources.GetString("txtMaxQty.Properties.NullValuePrompt");
            this.txtMaxQty.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtMaxQty.Properties.NullValuePromptShowForEmptyValue")));
            this.txtMaxQty.Modified += new System.EventHandler(this.Editors_Modified);
            this.txtMaxQty.EditValueChanging += new DevExpress.XtraEditors.Controls.ChangingEventHandler(this.txtPurchasePrice_EditValueChanging);
            this.txtMaxQty.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtNumberOnly_KeyPress);
            // 
            // txtReorder
            // 
            resources.ApplyResources(this.txtReorder, "txtReorder");
            this.txtReorder.EnterMoveNextControl = true;
            this.txtReorder.Name = "txtReorder";
            this.txtReorder.Properties.AccessibleDescription = resources.GetString("txtReorder.Properties.AccessibleDescription");
            this.txtReorder.Properties.AccessibleName = resources.GetString("txtReorder.Properties.AccessibleName");
            this.txtReorder.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txtReorder.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtReorder.Properties.Appearance.FontSizeDelta")));
            this.txtReorder.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtReorder.Properties.Appearance.FontStyleDelta")));
            this.txtReorder.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtReorder.Properties.Appearance.GradientMode")));
            this.txtReorder.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtReorder.Properties.Appearance.Image")));
            this.txtReorder.Properties.Appearance.Options.UseTextOptions = true;
            this.txtReorder.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.txtReorder.Properties.AutoHeight = ((bool)(resources.GetObject("txtReorder.Properties.AutoHeight")));
            this.txtReorder.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtReorder.Properties.Mask.AutoComplete")));
            this.txtReorder.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtReorder.Properties.Mask.BeepOnError")));
            this.txtReorder.Properties.Mask.EditMask = resources.GetString("txtReorder.Properties.Mask.EditMask");
            this.txtReorder.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtReorder.Properties.Mask.IgnoreMaskBlank")));
            this.txtReorder.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtReorder.Properties.Mask.MaskType")));
            this.txtReorder.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtReorder.Properties.Mask.PlaceHolder")));
            this.txtReorder.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtReorder.Properties.Mask.SaveLiteral")));
            this.txtReorder.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtReorder.Properties.Mask.ShowPlaceHolders")));
            this.txtReorder.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtReorder.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtReorder.Properties.NullText = resources.GetString("txtReorder.Properties.NullText");
            this.txtReorder.Properties.NullValuePrompt = resources.GetString("txtReorder.Properties.NullValuePrompt");
            this.txtReorder.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtReorder.Properties.NullValuePromptShowForEmptyValue")));
            this.txtReorder.Modified += new System.EventHandler(this.Editors_Modified);
            this.txtReorder.EditValueChanging += new DevExpress.XtraEditors.Controls.ChangingEventHandler(this.txtPurchasePrice_EditValueChanging);
            this.txtReorder.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtNumberOnly_KeyPress);
            // 
            // labelControl27
            // 
            resources.ApplyResources(this.labelControl27, "labelControl27");
            this.labelControl27.Name = "labelControl27";
            // 
            // labelControl28
            // 
            resources.ApplyResources(this.labelControl28, "labelControl28");
            this.labelControl28.Name = "labelControl28";
            // 
            // grp_UOM
            // 
            resources.ApplyResources(this.grp_UOM, "grp_UOM");
            this.grp_UOM.Controls.Add(this.chk_LargeIsStopped);
            this.grp_UOM.Controls.Add(this.chk_MediumIsStopped);
            this.grp_UOM.Controls.Add(this.chk_SmallIsStopped);
            this.grp_UOM.Controls.Add(this.labelControl37);
            this.grp_UOM.Controls.Add(this.lbl_WeightUnit);
            this.grp_UOM.Controls.Add(this.cmb_WeightUnit);
            this.grp_UOM.Controls.Add(this.txt_LargeUOMCode);
            this.grp_UOM.Controls.Add(this.labelControl30);
            this.grp_UOM.Controls.Add(this.txt_MediumUOMCode);
            this.grp_UOM.Controls.Add(this.labelControl29);
            this.grp_UOM.Controls.Add(this.rdoPrchsUOM2);
            this.grp_UOM.Controls.Add(this.rdoPrchsUOM1);
            this.grp_UOM.Controls.Add(this.rdoPrchsUOM0);
            this.grp_UOM.Controls.Add(this.rdoSellUOM2);
            this.grp_UOM.Controls.Add(this.rdoSellUOM1);
            this.grp_UOM.Controls.Add(this.labelControl24);
            this.grp_UOM.Controls.Add(this.rdoSellUOM0);
            this.grp_UOM.Controls.Add(this.textEdit5);
            this.grp_UOM.Controls.Add(this.txtLargeUOMFactor);
            this.grp_UOM.Controls.Add(this.lkpSmallUOM);
            this.grp_UOM.Controls.Add(this.lkpLargeUOM);
            this.grp_UOM.Controls.Add(this.labelControl13);
            this.grp_UOM.Controls.Add(this.labelControl19);
            this.grp_UOM.Controls.Add(this.labelControl23);
            this.grp_UOM.Controls.Add(this.btnAddUOM);
            this.grp_UOM.Controls.Add(this.txtMediumUOMFactor);
            this.grp_UOM.Controls.Add(this.labelControl21);
            this.grp_UOM.Controls.Add(this.lkpMediumUOM);
            this.grp_UOM.Controls.Add(this.labelControl20);
            this.grp_UOM.Controls.Add(this.txtSmallUOMPrice);
            this.grp_UOM.Controls.Add(this.txtMediumUOMPrice);
            this.grp_UOM.Controls.Add(this.txtLargeUOMPrice);
            this.grp_UOM.Name = "grp_UOM";
            this.grp_UOM.TabStop = false;
            // 
            // chk_LargeIsStopped
            // 
            resources.ApplyResources(this.chk_LargeIsStopped, "chk_LargeIsStopped");
            this.chk_LargeIsStopped.MenuManager = this.barManager1;
            this.chk_LargeIsStopped.Name = "chk_LargeIsStopped";
            this.chk_LargeIsStopped.Properties.AccessibleDescription = resources.GetString("chk_LargeIsStopped.Properties.AccessibleDescription");
            this.chk_LargeIsStopped.Properties.AccessibleName = resources.GetString("chk_LargeIsStopped.Properties.AccessibleName");
            this.chk_LargeIsStopped.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("chk_LargeIsStopped.Properties.Appearance.FontSizeDelta")));
            this.chk_LargeIsStopped.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("chk_LargeIsStopped.Properties.Appearance.FontStyleDelta")));
            this.chk_LargeIsStopped.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("chk_LargeIsStopped.Properties.Appearance.GradientMode")));
            this.chk_LargeIsStopped.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("chk_LargeIsStopped.Properties.Appearance.Image")));
            this.chk_LargeIsStopped.Properties.Appearance.Options.UseTextOptions = true;
            this.chk_LargeIsStopped.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chk_LargeIsStopped.Properties.AutoHeight = ((bool)(resources.GetObject("chk_LargeIsStopped.Properties.AutoHeight")));
            this.chk_LargeIsStopped.Properties.Caption = resources.GetString("chk_LargeIsStopped.Properties.Caption");
            this.chk_LargeIsStopped.Properties.DisplayValueChecked = resources.GetString("chk_LargeIsStopped.Properties.DisplayValueChecked");
            this.chk_LargeIsStopped.Properties.DisplayValueGrayed = resources.GetString("chk_LargeIsStopped.Properties.DisplayValueGrayed");
            this.chk_LargeIsStopped.Properties.DisplayValueUnchecked = resources.GetString("chk_LargeIsStopped.Properties.DisplayValueUnchecked");
            this.chk_LargeIsStopped.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("chk_LargeIsStopped.Properties.GlyphAlignment")));
            this.chk_LargeIsStopped.CheckedChanged += new System.EventHandler(this.chk_LargeIsStopped_CheckedChanged);
            // 
            // chk_MediumIsStopped
            // 
            resources.ApplyResources(this.chk_MediumIsStopped, "chk_MediumIsStopped");
            this.chk_MediumIsStopped.MenuManager = this.barManager1;
            this.chk_MediumIsStopped.Name = "chk_MediumIsStopped";
            this.chk_MediumIsStopped.Properties.AccessibleDescription = resources.GetString("chk_MediumIsStopped.Properties.AccessibleDescription");
            this.chk_MediumIsStopped.Properties.AccessibleName = resources.GetString("chk_MediumIsStopped.Properties.AccessibleName");
            this.chk_MediumIsStopped.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("chk_MediumIsStopped.Properties.Appearance.FontSizeDelta")));
            this.chk_MediumIsStopped.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("chk_MediumIsStopped.Properties.Appearance.FontStyleDelta")));
            this.chk_MediumIsStopped.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("chk_MediumIsStopped.Properties.Appearance.GradientMode")));
            this.chk_MediumIsStopped.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("chk_MediumIsStopped.Properties.Appearance.Image")));
            this.chk_MediumIsStopped.Properties.Appearance.Options.UseTextOptions = true;
            this.chk_MediumIsStopped.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chk_MediumIsStopped.Properties.AutoHeight = ((bool)(resources.GetObject("chk_MediumIsStopped.Properties.AutoHeight")));
            this.chk_MediumIsStopped.Properties.Caption = resources.GetString("chk_MediumIsStopped.Properties.Caption");
            this.chk_MediumIsStopped.Properties.DisplayValueChecked = resources.GetString("chk_MediumIsStopped.Properties.DisplayValueChecked");
            this.chk_MediumIsStopped.Properties.DisplayValueGrayed = resources.GetString("chk_MediumIsStopped.Properties.DisplayValueGrayed");
            this.chk_MediumIsStopped.Properties.DisplayValueUnchecked = resources.GetString("chk_MediumIsStopped.Properties.DisplayValueUnchecked");
            this.chk_MediumIsStopped.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("chk_MediumIsStopped.Properties.GlyphAlignment")));
            // 
            // chk_SmallIsStopped
            // 
            resources.ApplyResources(this.chk_SmallIsStopped, "chk_SmallIsStopped");
            this.chk_SmallIsStopped.MenuManager = this.barManager1;
            this.chk_SmallIsStopped.Name = "chk_SmallIsStopped";
            this.chk_SmallIsStopped.Properties.AccessibleDescription = resources.GetString("chk_SmallIsStopped.Properties.AccessibleDescription");
            this.chk_SmallIsStopped.Properties.AccessibleName = resources.GetString("chk_SmallIsStopped.Properties.AccessibleName");
            this.chk_SmallIsStopped.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("chk_SmallIsStopped.Properties.Appearance.FontSizeDelta")));
            this.chk_SmallIsStopped.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("chk_SmallIsStopped.Properties.Appearance.FontStyleDelta")));
            this.chk_SmallIsStopped.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("chk_SmallIsStopped.Properties.Appearance.GradientMode")));
            this.chk_SmallIsStopped.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("chk_SmallIsStopped.Properties.Appearance.Image")));
            this.chk_SmallIsStopped.Properties.Appearance.Options.UseTextOptions = true;
            this.chk_SmallIsStopped.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chk_SmallIsStopped.Properties.AutoHeight = ((bool)(resources.GetObject("chk_SmallIsStopped.Properties.AutoHeight")));
            this.chk_SmallIsStopped.Properties.Caption = resources.GetString("chk_SmallIsStopped.Properties.Caption");
            this.chk_SmallIsStopped.Properties.DisplayValueChecked = resources.GetString("chk_SmallIsStopped.Properties.DisplayValueChecked");
            this.chk_SmallIsStopped.Properties.DisplayValueGrayed = resources.GetString("chk_SmallIsStopped.Properties.DisplayValueGrayed");
            this.chk_SmallIsStopped.Properties.DisplayValueUnchecked = resources.GetString("chk_SmallIsStopped.Properties.DisplayValueUnchecked");
            this.chk_SmallIsStopped.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("chk_SmallIsStopped.Properties.GlyphAlignment")));
            // 
            // labelControl37
            // 
            resources.ApplyResources(this.labelControl37, "labelControl37");
            this.labelControl37.Name = "labelControl37";
            // 
            // lbl_WeightUnit
            // 
            resources.ApplyResources(this.lbl_WeightUnit, "lbl_WeightUnit");
            this.lbl_WeightUnit.Name = "lbl_WeightUnit";
            // 
            // cmb_WeightUnit
            // 
            resources.ApplyResources(this.cmb_WeightUnit, "cmb_WeightUnit");
            this.cmb_WeightUnit.EnterMoveNextControl = true;
            this.cmb_WeightUnit.MenuManager = this.barManager1;
            this.cmb_WeightUnit.Name = "cmb_WeightUnit";
            this.cmb_WeightUnit.Properties.AccessibleDescription = resources.GetString("cmb_WeightUnit.Properties.AccessibleDescription");
            this.cmb_WeightUnit.Properties.AccessibleName = resources.GetString("cmb_WeightUnit.Properties.AccessibleName");
            this.cmb_WeightUnit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.cmb_WeightUnit.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("cmb_WeightUnit.Properties.Appearance.FontSizeDelta")));
            this.cmb_WeightUnit.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("cmb_WeightUnit.Properties.Appearance.FontStyleDelta")));
            this.cmb_WeightUnit.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("cmb_WeightUnit.Properties.Appearance.GradientMode")));
            this.cmb_WeightUnit.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("cmb_WeightUnit.Properties.Appearance.Image")));
            this.cmb_WeightUnit.Properties.Appearance.Options.UseTextOptions = true;
            this.cmb_WeightUnit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.cmb_WeightUnit.Properties.AutoHeight = ((bool)(resources.GetObject("cmb_WeightUnit.Properties.AutoHeight")));
            this.cmb_WeightUnit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("cmb_WeightUnit.Properties.Buttons"))))});
            this.cmb_WeightUnit.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("cmb_WeightUnit.Properties.GlyphAlignment")));
            this.cmb_WeightUnit.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmb_WeightUnit.Properties.Items"), ((object)(resources.GetObject("cmb_WeightUnit.Properties.Items1"))), ((int)(resources.GetObject("cmb_WeightUnit.Properties.Items2")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmb_WeightUnit.Properties.Items3"), ((object)(resources.GetObject("cmb_WeightUnit.Properties.Items4"))), ((int)(resources.GetObject("cmb_WeightUnit.Properties.Items5")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmb_WeightUnit.Properties.Items6"), ((object)(resources.GetObject("cmb_WeightUnit.Properties.Items7"))), ((int)(resources.GetObject("cmb_WeightUnit.Properties.Items8")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmb_WeightUnit.Properties.Items9"), ((object)(resources.GetObject("cmb_WeightUnit.Properties.Items10"))), ((int)(resources.GetObject("cmb_WeightUnit.Properties.Items11"))))});
            this.cmb_WeightUnit.Properties.NullValuePrompt = resources.GetString("cmb_WeightUnit.Properties.NullValuePrompt");
            this.cmb_WeightUnit.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("cmb_WeightUnit.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // txt_LargeUOMCode
            // 
            resources.ApplyResources(this.txt_LargeUOMCode, "txt_LargeUOMCode");
            this.txt_LargeUOMCode.EnterMoveNextControl = true;
            this.txt_LargeUOMCode.Name = "txt_LargeUOMCode";
            this.txt_LargeUOMCode.Properties.AccessibleDescription = resources.GetString("txt_LargeUOMCode.Properties.AccessibleDescription");
            this.txt_LargeUOMCode.Properties.AccessibleName = resources.GetString("txt_LargeUOMCode.Properties.AccessibleName");
            this.txt_LargeUOMCode.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txt_LargeUOMCode.Properties.Appearance.FontSizeDelta")));
            this.txt_LargeUOMCode.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txt_LargeUOMCode.Properties.Appearance.FontStyleDelta")));
            this.txt_LargeUOMCode.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txt_LargeUOMCode.Properties.Appearance.GradientMode")));
            this.txt_LargeUOMCode.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txt_LargeUOMCode.Properties.Appearance.Image")));
            this.txt_LargeUOMCode.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_LargeUOMCode.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_LargeUOMCode.Properties.AutoHeight = ((bool)(resources.GetObject("txt_LargeUOMCode.Properties.AutoHeight")));
            this.txt_LargeUOMCode.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_LargeUOMCode.Properties.Mask.AutoComplete")));
            this.txt_LargeUOMCode.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_LargeUOMCode.Properties.Mask.BeepOnError")));
            this.txt_LargeUOMCode.Properties.Mask.EditMask = resources.GetString("txt_LargeUOMCode.Properties.Mask.EditMask");
            this.txt_LargeUOMCode.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_LargeUOMCode.Properties.Mask.IgnoreMaskBlank")));
            this.txt_LargeUOMCode.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_LargeUOMCode.Properties.Mask.MaskType")));
            this.txt_LargeUOMCode.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_LargeUOMCode.Properties.Mask.PlaceHolder")));
            this.txt_LargeUOMCode.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_LargeUOMCode.Properties.Mask.SaveLiteral")));
            this.txt_LargeUOMCode.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_LargeUOMCode.Properties.Mask.ShowPlaceHolders")));
            this.txt_LargeUOMCode.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_LargeUOMCode.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_LargeUOMCode.Properties.MaxLength = 50;
            this.txt_LargeUOMCode.Properties.NullValuePrompt = resources.GetString("txt_LargeUOMCode.Properties.NullValuePrompt");
            this.txt_LargeUOMCode.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_LargeUOMCode.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_LargeUOMCode.Modified += new System.EventHandler(this.Editors_Modified);
            // 
            // labelControl30
            // 
            resources.ApplyResources(this.labelControl30, "labelControl30");
            this.labelControl30.Name = "labelControl30";
            // 
            // txt_MediumUOMCode
            // 
            resources.ApplyResources(this.txt_MediumUOMCode, "txt_MediumUOMCode");
            this.txt_MediumUOMCode.EnterMoveNextControl = true;
            this.txt_MediumUOMCode.Name = "txt_MediumUOMCode";
            this.txt_MediumUOMCode.Properties.AccessibleDescription = resources.GetString("txt_MediumUOMCode.Properties.AccessibleDescription");
            this.txt_MediumUOMCode.Properties.AccessibleName = resources.GetString("txt_MediumUOMCode.Properties.AccessibleName");
            this.txt_MediumUOMCode.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txt_MediumUOMCode.Properties.Appearance.FontSizeDelta")));
            this.txt_MediumUOMCode.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txt_MediumUOMCode.Properties.Appearance.FontStyleDelta")));
            this.txt_MediumUOMCode.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txt_MediumUOMCode.Properties.Appearance.GradientMode")));
            this.txt_MediumUOMCode.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txt_MediumUOMCode.Properties.Appearance.Image")));
            this.txt_MediumUOMCode.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_MediumUOMCode.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_MediumUOMCode.Properties.AutoHeight = ((bool)(resources.GetObject("txt_MediumUOMCode.Properties.AutoHeight")));
            this.txt_MediumUOMCode.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_MediumUOMCode.Properties.Mask.AutoComplete")));
            this.txt_MediumUOMCode.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_MediumUOMCode.Properties.Mask.BeepOnError")));
            this.txt_MediumUOMCode.Properties.Mask.EditMask = resources.GetString("txt_MediumUOMCode.Properties.Mask.EditMask");
            this.txt_MediumUOMCode.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_MediumUOMCode.Properties.Mask.IgnoreMaskBlank")));
            this.txt_MediumUOMCode.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_MediumUOMCode.Properties.Mask.MaskType")));
            this.txt_MediumUOMCode.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_MediumUOMCode.Properties.Mask.PlaceHolder")));
            this.txt_MediumUOMCode.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_MediumUOMCode.Properties.Mask.SaveLiteral")));
            this.txt_MediumUOMCode.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_MediumUOMCode.Properties.Mask.ShowPlaceHolders")));
            this.txt_MediumUOMCode.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_MediumUOMCode.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_MediumUOMCode.Properties.MaxLength = 50;
            this.txt_MediumUOMCode.Properties.NullValuePrompt = resources.GetString("txt_MediumUOMCode.Properties.NullValuePrompt");
            this.txt_MediumUOMCode.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_MediumUOMCode.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_MediumUOMCode.Modified += new System.EventHandler(this.Editors_Modified);
            // 
            // labelControl29
            // 
            resources.ApplyResources(this.labelControl29, "labelControl29");
            this.labelControl29.Name = "labelControl29";
            // 
            // rdoPrchsUOM2
            // 
            resources.ApplyResources(this.rdoPrchsUOM2, "rdoPrchsUOM2");
            this.rdoPrchsUOM2.MenuManager = this.barManager1;
            this.rdoPrchsUOM2.Name = "rdoPrchsUOM2";
            this.rdoPrchsUOM2.Properties.AccessibleDescription = resources.GetString("rdoPrchsUOM2.Properties.AccessibleDescription");
            this.rdoPrchsUOM2.Properties.AccessibleName = resources.GetString("rdoPrchsUOM2.Properties.AccessibleName");
            this.rdoPrchsUOM2.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("rdoPrchsUOM2.Properties.Appearance.FontSizeDelta")));
            this.rdoPrchsUOM2.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("rdoPrchsUOM2.Properties.Appearance.FontStyleDelta")));
            this.rdoPrchsUOM2.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("rdoPrchsUOM2.Properties.Appearance.GradientMode")));
            this.rdoPrchsUOM2.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("rdoPrchsUOM2.Properties.Appearance.Image")));
            this.rdoPrchsUOM2.Properties.Appearance.Options.UseTextOptions = true;
            this.rdoPrchsUOM2.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.rdoPrchsUOM2.Properties.AutoHeight = ((bool)(resources.GetObject("rdoPrchsUOM2.Properties.AutoHeight")));
            this.rdoPrchsUOM2.Properties.Caption = resources.GetString("rdoPrchsUOM2.Properties.Caption");
            this.rdoPrchsUOM2.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Radio;
            this.rdoPrchsUOM2.Properties.DisplayValueChecked = resources.GetString("rdoPrchsUOM2.Properties.DisplayValueChecked");
            this.rdoPrchsUOM2.Properties.DisplayValueGrayed = resources.GetString("rdoPrchsUOM2.Properties.DisplayValueGrayed");
            this.rdoPrchsUOM2.Properties.DisplayValueUnchecked = resources.GetString("rdoPrchsUOM2.Properties.DisplayValueUnchecked");
            this.rdoPrchsUOM2.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("rdoPrchsUOM2.Properties.GlyphAlignment")));
            this.rdoPrchsUOM2.Properties.RadioGroupIndex = 1;
            this.rdoPrchsUOM2.TabStop = false;
            this.rdoPrchsUOM2.Modified += new System.EventHandler(this.Editors_Modified);
            // 
            // rdoPrchsUOM1
            // 
            resources.ApplyResources(this.rdoPrchsUOM1, "rdoPrchsUOM1");
            this.rdoPrchsUOM1.MenuManager = this.barManager1;
            this.rdoPrchsUOM1.Name = "rdoPrchsUOM1";
            this.rdoPrchsUOM1.Properties.AccessibleDescription = resources.GetString("rdoPrchsUOM1.Properties.AccessibleDescription");
            this.rdoPrchsUOM1.Properties.AccessibleName = resources.GetString("rdoPrchsUOM1.Properties.AccessibleName");
            this.rdoPrchsUOM1.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("rdoPrchsUOM1.Properties.Appearance.FontSizeDelta")));
            this.rdoPrchsUOM1.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("rdoPrchsUOM1.Properties.Appearance.FontStyleDelta")));
            this.rdoPrchsUOM1.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("rdoPrchsUOM1.Properties.Appearance.GradientMode")));
            this.rdoPrchsUOM1.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("rdoPrchsUOM1.Properties.Appearance.Image")));
            this.rdoPrchsUOM1.Properties.Appearance.Options.UseTextOptions = true;
            this.rdoPrchsUOM1.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.rdoPrchsUOM1.Properties.AutoHeight = ((bool)(resources.GetObject("rdoPrchsUOM1.Properties.AutoHeight")));
            this.rdoPrchsUOM1.Properties.Caption = resources.GetString("rdoPrchsUOM1.Properties.Caption");
            this.rdoPrchsUOM1.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Radio;
            this.rdoPrchsUOM1.Properties.DisplayValueChecked = resources.GetString("rdoPrchsUOM1.Properties.DisplayValueChecked");
            this.rdoPrchsUOM1.Properties.DisplayValueGrayed = resources.GetString("rdoPrchsUOM1.Properties.DisplayValueGrayed");
            this.rdoPrchsUOM1.Properties.DisplayValueUnchecked = resources.GetString("rdoPrchsUOM1.Properties.DisplayValueUnchecked");
            this.rdoPrchsUOM1.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("rdoPrchsUOM1.Properties.GlyphAlignment")));
            this.rdoPrchsUOM1.Properties.RadioGroupIndex = 1;
            this.rdoPrchsUOM1.TabStop = false;
            this.rdoPrchsUOM1.Modified += new System.EventHandler(this.Editors_Modified);
            // 
            // rdoPrchsUOM0
            // 
            resources.ApplyResources(this.rdoPrchsUOM0, "rdoPrchsUOM0");
            this.rdoPrchsUOM0.MenuManager = this.barManager1;
            this.rdoPrchsUOM0.Name = "rdoPrchsUOM0";
            this.rdoPrchsUOM0.Properties.AccessibleDescription = resources.GetString("rdoPrchsUOM0.Properties.AccessibleDescription");
            this.rdoPrchsUOM0.Properties.AccessibleName = resources.GetString("rdoPrchsUOM0.Properties.AccessibleName");
            this.rdoPrchsUOM0.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("rdoPrchsUOM0.Properties.Appearance.FontSizeDelta")));
            this.rdoPrchsUOM0.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("rdoPrchsUOM0.Properties.Appearance.FontStyleDelta")));
            this.rdoPrchsUOM0.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("rdoPrchsUOM0.Properties.Appearance.GradientMode")));
            this.rdoPrchsUOM0.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("rdoPrchsUOM0.Properties.Appearance.Image")));
            this.rdoPrchsUOM0.Properties.Appearance.Options.UseTextOptions = true;
            this.rdoPrchsUOM0.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.rdoPrchsUOM0.Properties.AutoHeight = ((bool)(resources.GetObject("rdoPrchsUOM0.Properties.AutoHeight")));
            this.rdoPrchsUOM0.Properties.Caption = resources.GetString("rdoPrchsUOM0.Properties.Caption");
            this.rdoPrchsUOM0.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Radio;
            this.rdoPrchsUOM0.Properties.DisplayValueChecked = resources.GetString("rdoPrchsUOM0.Properties.DisplayValueChecked");
            this.rdoPrchsUOM0.Properties.DisplayValueGrayed = resources.GetString("rdoPrchsUOM0.Properties.DisplayValueGrayed");
            this.rdoPrchsUOM0.Properties.DisplayValueUnchecked = resources.GetString("rdoPrchsUOM0.Properties.DisplayValueUnchecked");
            this.rdoPrchsUOM0.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("rdoPrchsUOM0.Properties.GlyphAlignment")));
            this.rdoPrchsUOM0.Properties.RadioGroupIndex = 1;
            this.rdoPrchsUOM0.TabStop = false;
            this.rdoPrchsUOM0.Modified += new System.EventHandler(this.Editors_Modified);
            // 
            // rdoSellUOM2
            // 
            resources.ApplyResources(this.rdoSellUOM2, "rdoSellUOM2");
            this.rdoSellUOM2.MenuManager = this.barManager1;
            this.rdoSellUOM2.Name = "rdoSellUOM2";
            this.rdoSellUOM2.Properties.AccessibleDescription = resources.GetString("rdoSellUOM2.Properties.AccessibleDescription");
            this.rdoSellUOM2.Properties.AccessibleName = resources.GetString("rdoSellUOM2.Properties.AccessibleName");
            this.rdoSellUOM2.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("rdoSellUOM2.Properties.Appearance.FontSizeDelta")));
            this.rdoSellUOM2.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("rdoSellUOM2.Properties.Appearance.FontStyleDelta")));
            this.rdoSellUOM2.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("rdoSellUOM2.Properties.Appearance.GradientMode")));
            this.rdoSellUOM2.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("rdoSellUOM2.Properties.Appearance.Image")));
            this.rdoSellUOM2.Properties.Appearance.Options.UseTextOptions = true;
            this.rdoSellUOM2.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.rdoSellUOM2.Properties.AutoHeight = ((bool)(resources.GetObject("rdoSellUOM2.Properties.AutoHeight")));
            this.rdoSellUOM2.Properties.Caption = resources.GetString("rdoSellUOM2.Properties.Caption");
            this.rdoSellUOM2.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Radio;
            this.rdoSellUOM2.Properties.DisplayValueChecked = resources.GetString("rdoSellUOM2.Properties.DisplayValueChecked");
            this.rdoSellUOM2.Properties.DisplayValueGrayed = resources.GetString("rdoSellUOM2.Properties.DisplayValueGrayed");
            this.rdoSellUOM2.Properties.DisplayValueUnchecked = resources.GetString("rdoSellUOM2.Properties.DisplayValueUnchecked");
            this.rdoSellUOM2.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("rdoSellUOM2.Properties.GlyphAlignment")));
            this.rdoSellUOM2.Properties.RadioGroupIndex = 0;
            this.rdoSellUOM2.TabStop = false;
            this.rdoSellUOM2.Modified += new System.EventHandler(this.Editors_Modified);
            // 
            // rdoSellUOM1
            // 
            resources.ApplyResources(this.rdoSellUOM1, "rdoSellUOM1");
            this.rdoSellUOM1.MenuManager = this.barManager1;
            this.rdoSellUOM1.Name = "rdoSellUOM1";
            this.rdoSellUOM1.Properties.AccessibleDescription = resources.GetString("rdoSellUOM1.Properties.AccessibleDescription");
            this.rdoSellUOM1.Properties.AccessibleName = resources.GetString("rdoSellUOM1.Properties.AccessibleName");
            this.rdoSellUOM1.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("rdoSellUOM1.Properties.Appearance.FontSizeDelta")));
            this.rdoSellUOM1.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("rdoSellUOM1.Properties.Appearance.FontStyleDelta")));
            this.rdoSellUOM1.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("rdoSellUOM1.Properties.Appearance.GradientMode")));
            this.rdoSellUOM1.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("rdoSellUOM1.Properties.Appearance.Image")));
            this.rdoSellUOM1.Properties.Appearance.Options.UseTextOptions = true;
            this.rdoSellUOM1.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.rdoSellUOM1.Properties.AutoHeight = ((bool)(resources.GetObject("rdoSellUOM1.Properties.AutoHeight")));
            this.rdoSellUOM1.Properties.Caption = resources.GetString("rdoSellUOM1.Properties.Caption");
            this.rdoSellUOM1.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Radio;
            this.rdoSellUOM1.Properties.DisplayValueChecked = resources.GetString("rdoSellUOM1.Properties.DisplayValueChecked");
            this.rdoSellUOM1.Properties.DisplayValueGrayed = resources.GetString("rdoSellUOM1.Properties.DisplayValueGrayed");
            this.rdoSellUOM1.Properties.DisplayValueUnchecked = resources.GetString("rdoSellUOM1.Properties.DisplayValueUnchecked");
            this.rdoSellUOM1.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("rdoSellUOM1.Properties.GlyphAlignment")));
            this.rdoSellUOM1.Properties.RadioGroupIndex = 0;
            this.rdoSellUOM1.TabStop = false;
            this.rdoSellUOM1.Modified += new System.EventHandler(this.Editors_Modified);
            // 
            // labelControl24
            // 
            resources.ApplyResources(this.labelControl24, "labelControl24");
            this.labelControl24.Name = "labelControl24";
            // 
            // rdoSellUOM0
            // 
            resources.ApplyResources(this.rdoSellUOM0, "rdoSellUOM0");
            this.rdoSellUOM0.MenuManager = this.barManager1;
            this.rdoSellUOM0.Name = "rdoSellUOM0";
            this.rdoSellUOM0.Properties.AccessibleDescription = resources.GetString("rdoSellUOM0.Properties.AccessibleDescription");
            this.rdoSellUOM0.Properties.AccessibleName = resources.GetString("rdoSellUOM0.Properties.AccessibleName");
            this.rdoSellUOM0.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("rdoSellUOM0.Properties.Appearance.FontSizeDelta")));
            this.rdoSellUOM0.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("rdoSellUOM0.Properties.Appearance.FontStyleDelta")));
            this.rdoSellUOM0.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("rdoSellUOM0.Properties.Appearance.GradientMode")));
            this.rdoSellUOM0.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("rdoSellUOM0.Properties.Appearance.Image")));
            this.rdoSellUOM0.Properties.Appearance.Options.UseTextOptions = true;
            this.rdoSellUOM0.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.rdoSellUOM0.Properties.AutoHeight = ((bool)(resources.GetObject("rdoSellUOM0.Properties.AutoHeight")));
            this.rdoSellUOM0.Properties.Caption = resources.GetString("rdoSellUOM0.Properties.Caption");
            this.rdoSellUOM0.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Radio;
            this.rdoSellUOM0.Properties.DisplayValueChecked = resources.GetString("rdoSellUOM0.Properties.DisplayValueChecked");
            this.rdoSellUOM0.Properties.DisplayValueGrayed = resources.GetString("rdoSellUOM0.Properties.DisplayValueGrayed");
            this.rdoSellUOM0.Properties.DisplayValueUnchecked = resources.GetString("rdoSellUOM0.Properties.DisplayValueUnchecked");
            this.rdoSellUOM0.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("rdoSellUOM0.Properties.GlyphAlignment")));
            this.rdoSellUOM0.Properties.RadioGroupIndex = 0;
            this.rdoSellUOM0.TabStop = false;
            this.rdoSellUOM0.Modified += new System.EventHandler(this.Editors_Modified);
            // 
            // textEdit5
            // 
            resources.ApplyResources(this.textEdit5, "textEdit5");
            this.textEdit5.EnterMoveNextControl = true;
            this.textEdit5.Name = "textEdit5";
            this.textEdit5.Properties.AccessibleDescription = resources.GetString("textEdit5.Properties.AccessibleDescription");
            this.textEdit5.Properties.AccessibleName = resources.GetString("textEdit5.Properties.AccessibleName");
            this.textEdit5.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("textEdit5.Properties.Appearance.FontSizeDelta")));
            this.textEdit5.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("textEdit5.Properties.Appearance.FontStyleDelta")));
            this.textEdit5.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("textEdit5.Properties.Appearance.GradientMode")));
            this.textEdit5.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("textEdit5.Properties.Appearance.Image")));
            this.textEdit5.Properties.Appearance.Options.UseTextOptions = true;
            this.textEdit5.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.textEdit5.Properties.AutoHeight = ((bool)(resources.GetObject("textEdit5.Properties.AutoHeight")));
            this.textEdit5.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("textEdit5.Properties.Mask.AutoComplete")));
            this.textEdit5.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("textEdit5.Properties.Mask.BeepOnError")));
            this.textEdit5.Properties.Mask.EditMask = resources.GetString("textEdit5.Properties.Mask.EditMask");
            this.textEdit5.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("textEdit5.Properties.Mask.IgnoreMaskBlank")));
            this.textEdit5.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("textEdit5.Properties.Mask.MaskType")));
            this.textEdit5.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("textEdit5.Properties.Mask.PlaceHolder")));
            this.textEdit5.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("textEdit5.Properties.Mask.SaveLiteral")));
            this.textEdit5.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("textEdit5.Properties.Mask.ShowPlaceHolders")));
            this.textEdit5.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("textEdit5.Properties.Mask.UseMaskAsDisplayFormat")));
            this.textEdit5.Properties.NullValuePrompt = resources.GetString("textEdit5.Properties.NullValuePrompt");
            this.textEdit5.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("textEdit5.Properties.NullValuePromptShowForEmptyValue")));
            this.textEdit5.TabStop = false;
            this.textEdit5.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtNumberOnly_KeyPress);
            // 
            // txtLargeUOMFactor
            // 
            resources.ApplyResources(this.txtLargeUOMFactor, "txtLargeUOMFactor");
            this.txtLargeUOMFactor.EnterMoveNextControl = true;
            this.txtLargeUOMFactor.Name = "txtLargeUOMFactor";
            this.txtLargeUOMFactor.Properties.AccessibleDescription = resources.GetString("txtLargeUOMFactor.Properties.AccessibleDescription");
            this.txtLargeUOMFactor.Properties.AccessibleName = resources.GetString("txtLargeUOMFactor.Properties.AccessibleName");
            this.txtLargeUOMFactor.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtLargeUOMFactor.Properties.Appearance.FontSizeDelta")));
            this.txtLargeUOMFactor.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtLargeUOMFactor.Properties.Appearance.FontStyleDelta")));
            this.txtLargeUOMFactor.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtLargeUOMFactor.Properties.Appearance.GradientMode")));
            this.txtLargeUOMFactor.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtLargeUOMFactor.Properties.Appearance.Image")));
            this.txtLargeUOMFactor.Properties.Appearance.Options.UseTextOptions = true;
            this.txtLargeUOMFactor.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.txtLargeUOMFactor.Properties.AutoHeight = ((bool)(resources.GetObject("txtLargeUOMFactor.Properties.AutoHeight")));
            this.txtLargeUOMFactor.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtLargeUOMFactor.Properties.Mask.AutoComplete")));
            this.txtLargeUOMFactor.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtLargeUOMFactor.Properties.Mask.BeepOnError")));
            this.txtLargeUOMFactor.Properties.Mask.EditMask = resources.GetString("txtLargeUOMFactor.Properties.Mask.EditMask");
            this.txtLargeUOMFactor.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtLargeUOMFactor.Properties.Mask.IgnoreMaskBlank")));
            this.txtLargeUOMFactor.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtLargeUOMFactor.Properties.Mask.MaskType")));
            this.txtLargeUOMFactor.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtLargeUOMFactor.Properties.Mask.PlaceHolder")));
            this.txtLargeUOMFactor.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtLargeUOMFactor.Properties.Mask.SaveLiteral")));
            this.txtLargeUOMFactor.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtLargeUOMFactor.Properties.Mask.ShowPlaceHolders")));
            this.txtLargeUOMFactor.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtLargeUOMFactor.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtLargeUOMFactor.Properties.MaxLength = 11;
            this.txtLargeUOMFactor.Properties.NullValuePrompt = resources.GetString("txtLargeUOMFactor.Properties.NullValuePrompt");
            this.txtLargeUOMFactor.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtLargeUOMFactor.Properties.NullValuePromptShowForEmptyValue")));
            this.txtLargeUOMFactor.Modified += new System.EventHandler(this.Editors_Modified);
            this.txtLargeUOMFactor.EditValueChanging += new DevExpress.XtraEditors.Controls.ChangingEventHandler(this.txtPurchasePrice_EditValueChanging);
            this.txtLargeUOMFactor.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtUOM_factor_KeyPress);
            // 
            // lkpSmallUOM
            // 
            resources.ApplyResources(this.lkpSmallUOM, "lkpSmallUOM");
            this.lkpSmallUOM.EnterMoveNextControl = true;
            this.lkpSmallUOM.MenuManager = this.barManager1;
            this.lkpSmallUOM.Name = "lkpSmallUOM";
            this.lkpSmallUOM.Properties.AccessibleDescription = resources.GetString("lkpSmallUOM.Properties.AccessibleDescription");
            this.lkpSmallUOM.Properties.AccessibleName = resources.GetString("lkpSmallUOM.Properties.AccessibleName");
            this.lkpSmallUOM.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkpSmallUOM.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkpSmallUOM.Properties.Appearance.FontSizeDelta")));
            this.lkpSmallUOM.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpSmallUOM.Properties.Appearance.FontStyleDelta")));
            this.lkpSmallUOM.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpSmallUOM.Properties.Appearance.GradientMode")));
            this.lkpSmallUOM.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkpSmallUOM.Properties.Appearance.Image")));
            this.lkpSmallUOM.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpSmallUOM.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpSmallUOM.Properties.AppearanceDropDown.FontSizeDelta = ((int)(resources.GetObject("lkpSmallUOM.Properties.AppearanceDropDown.FontSizeDelta")));
            this.lkpSmallUOM.Properties.AppearanceDropDown.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpSmallUOM.Properties.AppearanceDropDown.FontStyleDelta")));
            this.lkpSmallUOM.Properties.AppearanceDropDown.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpSmallUOM.Properties.AppearanceDropDown.GradientMode")));
            this.lkpSmallUOM.Properties.AppearanceDropDown.Image = ((System.Drawing.Image)(resources.GetObject("lkpSmallUOM.Properties.AppearanceDropDown.Image")));
            this.lkpSmallUOM.Properties.AppearanceDropDown.Options.UseTextOptions = true;
            this.lkpSmallUOM.Properties.AppearanceDropDown.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpSmallUOM.Properties.AppearanceDropDownHeader.FontSizeDelta = ((int)(resources.GetObject("lkpSmallUOM.Properties.AppearanceDropDownHeader.FontSizeDelta")));
            this.lkpSmallUOM.Properties.AppearanceDropDownHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpSmallUOM.Properties.AppearanceDropDownHeader.FontStyleDelta")));
            this.lkpSmallUOM.Properties.AppearanceDropDownHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpSmallUOM.Properties.AppearanceDropDownHeader.GradientMode")));
            this.lkpSmallUOM.Properties.AppearanceDropDownHeader.Image = ((System.Drawing.Image)(resources.GetObject("lkpSmallUOM.Properties.AppearanceDropDownHeader.Image")));
            this.lkpSmallUOM.Properties.AppearanceDropDownHeader.Options.UseTextOptions = true;
            this.lkpSmallUOM.Properties.AppearanceDropDownHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpSmallUOM.Properties.AutoHeight = ((bool)(resources.GetObject("lkpSmallUOM.Properties.AutoHeight")));
            this.lkpSmallUOM.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkpSmallUOM.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpSmallUOM.Properties.Buttons"))))});
            this.lkpSmallUOM.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpSmallUOM.Properties.Columns"), resources.GetString("lkpSmallUOM.Properties.Columns1"), ((int)(resources.GetObject("lkpSmallUOM.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpSmallUOM.Properties.Columns3"))), resources.GetString("lkpSmallUOM.Properties.Columns4"), ((bool)(resources.GetObject("lkpSmallUOM.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpSmallUOM.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpSmallUOM.Properties.Columns7"), resources.GetString("lkpSmallUOM.Properties.Columns8"), ((int)(resources.GetObject("lkpSmallUOM.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpSmallUOM.Properties.Columns10"))), resources.GetString("lkpSmallUOM.Properties.Columns11"), ((bool)(resources.GetObject("lkpSmallUOM.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpSmallUOM.Properties.Columns13"))))});
            this.lkpSmallUOM.Properties.NullText = resources.GetString("lkpSmallUOM.Properties.NullText");
            this.lkpSmallUOM.Properties.NullValuePrompt = resources.GetString("lkpSmallUOM.Properties.NullValuePrompt");
            this.lkpSmallUOM.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkpSmallUOM.Properties.NullValuePromptShowForEmptyValue")));
            this.lkpSmallUOM.Modified += new System.EventHandler(this.Editors_Modified);
            this.lkpSmallUOM.EditValueChanging += new DevExpress.XtraEditors.Controls.ChangingEventHandler(this.lkpUOMs_EditValueChanging);
            // 
            // lkpLargeUOM
            // 
            resources.ApplyResources(this.lkpLargeUOM, "lkpLargeUOM");
            this.lkpLargeUOM.EnterMoveNextControl = true;
            this.lkpLargeUOM.MenuManager = this.barManager1;
            this.lkpLargeUOM.Name = "lkpLargeUOM";
            this.lkpLargeUOM.Properties.AccessibleDescription = resources.GetString("lkpLargeUOM.Properties.AccessibleDescription");
            this.lkpLargeUOM.Properties.AccessibleName = resources.GetString("lkpLargeUOM.Properties.AccessibleName");
            this.lkpLargeUOM.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkpLargeUOM.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkpLargeUOM.Properties.Appearance.FontSizeDelta")));
            this.lkpLargeUOM.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpLargeUOM.Properties.Appearance.FontStyleDelta")));
            this.lkpLargeUOM.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpLargeUOM.Properties.Appearance.GradientMode")));
            this.lkpLargeUOM.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkpLargeUOM.Properties.Appearance.Image")));
            this.lkpLargeUOM.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpLargeUOM.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpLargeUOM.Properties.AppearanceDisabled.FontSizeDelta = ((int)(resources.GetObject("lkpLargeUOM.Properties.AppearanceDisabled.FontSizeDelta")));
            this.lkpLargeUOM.Properties.AppearanceDisabled.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpLargeUOM.Properties.AppearanceDisabled.FontStyleDelta")));
            this.lkpLargeUOM.Properties.AppearanceDisabled.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpLargeUOM.Properties.AppearanceDisabled.GradientMode")));
            this.lkpLargeUOM.Properties.AppearanceDisabled.Image = ((System.Drawing.Image)(resources.GetObject("lkpLargeUOM.Properties.AppearanceDisabled.Image")));
            this.lkpLargeUOM.Properties.AppearanceDisabled.Options.UseTextOptions = true;
            this.lkpLargeUOM.Properties.AppearanceDisabled.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpLargeUOM.Properties.AppearanceDropDown.FontSizeDelta = ((int)(resources.GetObject("lkpLargeUOM.Properties.AppearanceDropDown.FontSizeDelta")));
            this.lkpLargeUOM.Properties.AppearanceDropDown.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpLargeUOM.Properties.AppearanceDropDown.FontStyleDelta")));
            this.lkpLargeUOM.Properties.AppearanceDropDown.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpLargeUOM.Properties.AppearanceDropDown.GradientMode")));
            this.lkpLargeUOM.Properties.AppearanceDropDown.Image = ((System.Drawing.Image)(resources.GetObject("lkpLargeUOM.Properties.AppearanceDropDown.Image")));
            this.lkpLargeUOM.Properties.AppearanceDropDown.Options.UseTextOptions = true;
            this.lkpLargeUOM.Properties.AppearanceDropDown.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpLargeUOM.Properties.AppearanceDropDownHeader.FontSizeDelta = ((int)(resources.GetObject("lkpLargeUOM.Properties.AppearanceDropDownHeader.FontSizeDelta")));
            this.lkpLargeUOM.Properties.AppearanceDropDownHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpLargeUOM.Properties.AppearanceDropDownHeader.FontStyleDelta")));
            this.lkpLargeUOM.Properties.AppearanceDropDownHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpLargeUOM.Properties.AppearanceDropDownHeader.GradientMode")));
            this.lkpLargeUOM.Properties.AppearanceDropDownHeader.Image = ((System.Drawing.Image)(resources.GetObject("lkpLargeUOM.Properties.AppearanceDropDownHeader.Image")));
            this.lkpLargeUOM.Properties.AppearanceDropDownHeader.Options.UseTextOptions = true;
            this.lkpLargeUOM.Properties.AppearanceDropDownHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpLargeUOM.Properties.AppearanceFocused.FontSizeDelta = ((int)(resources.GetObject("lkpLargeUOM.Properties.AppearanceFocused.FontSizeDelta")));
            this.lkpLargeUOM.Properties.AppearanceFocused.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpLargeUOM.Properties.AppearanceFocused.FontStyleDelta")));
            this.lkpLargeUOM.Properties.AppearanceFocused.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpLargeUOM.Properties.AppearanceFocused.GradientMode")));
            this.lkpLargeUOM.Properties.AppearanceFocused.Image = ((System.Drawing.Image)(resources.GetObject("lkpLargeUOM.Properties.AppearanceFocused.Image")));
            this.lkpLargeUOM.Properties.AppearanceFocused.Options.UseTextOptions = true;
            this.lkpLargeUOM.Properties.AppearanceFocused.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpLargeUOM.Properties.AppearanceReadOnly.FontSizeDelta = ((int)(resources.GetObject("lkpLargeUOM.Properties.AppearanceReadOnly.FontSizeDelta")));
            this.lkpLargeUOM.Properties.AppearanceReadOnly.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpLargeUOM.Properties.AppearanceReadOnly.FontStyleDelta")));
            this.lkpLargeUOM.Properties.AppearanceReadOnly.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpLargeUOM.Properties.AppearanceReadOnly.GradientMode")));
            this.lkpLargeUOM.Properties.AppearanceReadOnly.Image = ((System.Drawing.Image)(resources.GetObject("lkpLargeUOM.Properties.AppearanceReadOnly.Image")));
            this.lkpLargeUOM.Properties.AppearanceReadOnly.Options.UseTextOptions = true;
            this.lkpLargeUOM.Properties.AppearanceReadOnly.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpLargeUOM.Properties.AutoHeight = ((bool)(resources.GetObject("lkpLargeUOM.Properties.AutoHeight")));
            this.lkpLargeUOM.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkpLargeUOM.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpLargeUOM.Properties.Buttons"))))});
            this.lkpLargeUOM.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpLargeUOM.Properties.Columns"), resources.GetString("lkpLargeUOM.Properties.Columns1"), ((int)(resources.GetObject("lkpLargeUOM.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpLargeUOM.Properties.Columns3"))), resources.GetString("lkpLargeUOM.Properties.Columns4"), ((bool)(resources.GetObject("lkpLargeUOM.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpLargeUOM.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpLargeUOM.Properties.Columns7"), resources.GetString("lkpLargeUOM.Properties.Columns8"), ((int)(resources.GetObject("lkpLargeUOM.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpLargeUOM.Properties.Columns10"))), resources.GetString("lkpLargeUOM.Properties.Columns11"), ((bool)(resources.GetObject("lkpLargeUOM.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpLargeUOM.Properties.Columns13"))))});
            this.lkpLargeUOM.Properties.NullText = resources.GetString("lkpLargeUOM.Properties.NullText");
            this.lkpLargeUOM.Properties.NullValuePrompt = resources.GetString("lkpLargeUOM.Properties.NullValuePrompt");
            this.lkpLargeUOM.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkpLargeUOM.Properties.NullValuePromptShowForEmptyValue")));
            this.lkpLargeUOM.EditValueChanged += new System.EventHandler(this.lkpLargeUOM_EditValueChanged);
            this.lkpLargeUOM.Modified += new System.EventHandler(this.Editors_Modified);
            this.lkpLargeUOM.EditValueChanging += new DevExpress.XtraEditors.Controls.ChangingEventHandler(this.lkpUOMs_EditValueChanging);
            // 
            // labelControl13
            // 
            resources.ApplyResources(this.labelControl13, "labelControl13");
            this.labelControl13.Name = "labelControl13";
            // 
            // labelControl19
            // 
            resources.ApplyResources(this.labelControl19, "labelControl19");
            this.labelControl19.Name = "labelControl19";
            // 
            // labelControl23
            // 
            resources.ApplyResources(this.labelControl23, "labelControl23");
            this.labelControl23.Name = "labelControl23";
            // 
            // btnAddUOM
            // 
            resources.ApplyResources(this.btnAddUOM, "btnAddUOM");
            this.btnAddUOM.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnAddUOM.Image = global::Pharmacy.Properties.Resources.add32;
            this.btnAddUOM.Name = "btnAddUOM";
            this.btnAddUOM.TabStop = false;
            this.btnAddUOM.Click += new System.EventHandler(this.btnAddUOM_Click);
            // 
            // labelControl21
            // 
            resources.ApplyResources(this.labelControl21, "labelControl21");
            this.labelControl21.Name = "labelControl21";
            // 
            // lkpMediumUOM
            // 
            resources.ApplyResources(this.lkpMediumUOM, "lkpMediumUOM");
            this.lkpMediumUOM.EnterMoveNextControl = true;
            this.lkpMediumUOM.MenuManager = this.barManager1;
            this.lkpMediumUOM.Name = "lkpMediumUOM";
            this.lkpMediumUOM.Properties.AccessibleDescription = resources.GetString("lkpMediumUOM.Properties.AccessibleDescription");
            this.lkpMediumUOM.Properties.AccessibleName = resources.GetString("lkpMediumUOM.Properties.AccessibleName");
            this.lkpMediumUOM.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkpMediumUOM.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkpMediumUOM.Properties.Appearance.FontSizeDelta")));
            this.lkpMediumUOM.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpMediumUOM.Properties.Appearance.FontStyleDelta")));
            this.lkpMediumUOM.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpMediumUOM.Properties.Appearance.GradientMode")));
            this.lkpMediumUOM.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkpMediumUOM.Properties.Appearance.Image")));
            this.lkpMediumUOM.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpMediumUOM.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpMediumUOM.Properties.AppearanceDropDown.FontSizeDelta = ((int)(resources.GetObject("lkpMediumUOM.Properties.AppearanceDropDown.FontSizeDelta")));
            this.lkpMediumUOM.Properties.AppearanceDropDown.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpMediumUOM.Properties.AppearanceDropDown.FontStyleDelta")));
            this.lkpMediumUOM.Properties.AppearanceDropDown.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpMediumUOM.Properties.AppearanceDropDown.GradientMode")));
            this.lkpMediumUOM.Properties.AppearanceDropDown.Image = ((System.Drawing.Image)(resources.GetObject("lkpMediumUOM.Properties.AppearanceDropDown.Image")));
            this.lkpMediumUOM.Properties.AppearanceDropDown.Options.UseTextOptions = true;
            this.lkpMediumUOM.Properties.AppearanceDropDown.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpMediumUOM.Properties.AppearanceDropDownHeader.FontSizeDelta = ((int)(resources.GetObject("lkpMediumUOM.Properties.AppearanceDropDownHeader.FontSizeDelta")));
            this.lkpMediumUOM.Properties.AppearanceDropDownHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpMediumUOM.Properties.AppearanceDropDownHeader.FontStyleDelta")));
            this.lkpMediumUOM.Properties.AppearanceDropDownHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpMediumUOM.Properties.AppearanceDropDownHeader.GradientMode")));
            this.lkpMediumUOM.Properties.AppearanceDropDownHeader.Image = ((System.Drawing.Image)(resources.GetObject("lkpMediumUOM.Properties.AppearanceDropDownHeader.Image")));
            this.lkpMediumUOM.Properties.AppearanceDropDownHeader.Options.UseTextOptions = true;
            this.lkpMediumUOM.Properties.AppearanceDropDownHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpMediumUOM.Properties.AppearanceFocused.FontSizeDelta = ((int)(resources.GetObject("lkpMediumUOM.Properties.AppearanceFocused.FontSizeDelta")));
            this.lkpMediumUOM.Properties.AppearanceFocused.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpMediumUOM.Properties.AppearanceFocused.FontStyleDelta")));
            this.lkpMediumUOM.Properties.AppearanceFocused.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpMediumUOM.Properties.AppearanceFocused.GradientMode")));
            this.lkpMediumUOM.Properties.AppearanceFocused.Image = ((System.Drawing.Image)(resources.GetObject("lkpMediumUOM.Properties.AppearanceFocused.Image")));
            this.lkpMediumUOM.Properties.AppearanceFocused.Options.UseTextOptions = true;
            this.lkpMediumUOM.Properties.AppearanceFocused.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpMediumUOM.Properties.AppearanceReadOnly.FontSizeDelta = ((int)(resources.GetObject("lkpMediumUOM.Properties.AppearanceReadOnly.FontSizeDelta")));
            this.lkpMediumUOM.Properties.AppearanceReadOnly.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpMediumUOM.Properties.AppearanceReadOnly.FontStyleDelta")));
            this.lkpMediumUOM.Properties.AppearanceReadOnly.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpMediumUOM.Properties.AppearanceReadOnly.GradientMode")));
            this.lkpMediumUOM.Properties.AppearanceReadOnly.Image = ((System.Drawing.Image)(resources.GetObject("lkpMediumUOM.Properties.AppearanceReadOnly.Image")));
            this.lkpMediumUOM.Properties.AppearanceReadOnly.Options.UseTextOptions = true;
            this.lkpMediumUOM.Properties.AppearanceReadOnly.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpMediumUOM.Properties.AutoHeight = ((bool)(resources.GetObject("lkpMediumUOM.Properties.AutoHeight")));
            this.lkpMediumUOM.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkpMediumUOM.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpMediumUOM.Properties.Buttons"))))});
            this.lkpMediumUOM.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpMediumUOM.Properties.Columns"), resources.GetString("lkpMediumUOM.Properties.Columns1"), ((int)(resources.GetObject("lkpMediumUOM.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpMediumUOM.Properties.Columns3"))), resources.GetString("lkpMediumUOM.Properties.Columns4"), ((bool)(resources.GetObject("lkpMediumUOM.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpMediumUOM.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpMediumUOM.Properties.Columns7"), resources.GetString("lkpMediumUOM.Properties.Columns8"), ((int)(resources.GetObject("lkpMediumUOM.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpMediumUOM.Properties.Columns10"))), resources.GetString("lkpMediumUOM.Properties.Columns11"), ((bool)(resources.GetObject("lkpMediumUOM.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpMediumUOM.Properties.Columns13"))))});
            this.lkpMediumUOM.Properties.NullText = resources.GetString("lkpMediumUOM.Properties.NullText");
            this.lkpMediumUOM.Properties.NullValuePrompt = resources.GetString("lkpMediumUOM.Properties.NullValuePrompt");
            this.lkpMediumUOM.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkpMediumUOM.Properties.NullValuePromptShowForEmptyValue")));
            this.lkpMediumUOM.EditValueChanged += new System.EventHandler(this.lkpMediumUOM_EditValueChanged);
            this.lkpMediumUOM.Modified += new System.EventHandler(this.Editors_Modified);
            this.lkpMediumUOM.EditValueChanging += new DevExpress.XtraEditors.Controls.ChangingEventHandler(this.lkpUOMs_EditValueChanging);
            // 
            // labelControl20
            // 
            resources.ApplyResources(this.labelControl20, "labelControl20");
            this.labelControl20.Name = "labelControl20";
            // 
            // txtSmallUOMPrice
            // 
            resources.ApplyResources(this.txtSmallUOMPrice, "txtSmallUOMPrice");
            this.txtSmallUOMPrice.EnterMoveNextControl = true;
            this.txtSmallUOMPrice.MenuManager = this.barManager1;
            this.txtSmallUOMPrice.Name = "txtSmallUOMPrice";
            this.txtSmallUOMPrice.Properties.AccessibleDescription = resources.GetString("txtSmallUOMPrice.Properties.AccessibleDescription");
            this.txtSmallUOMPrice.Properties.AccessibleName = resources.GetString("txtSmallUOMPrice.Properties.AccessibleName");
            this.txtSmallUOMPrice.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtSmallUOMPrice.Properties.Appearance.FontSizeDelta")));
            this.txtSmallUOMPrice.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtSmallUOMPrice.Properties.Appearance.FontStyleDelta")));
            this.txtSmallUOMPrice.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtSmallUOMPrice.Properties.Appearance.GradientMode")));
            this.txtSmallUOMPrice.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtSmallUOMPrice.Properties.Appearance.Image")));
            this.txtSmallUOMPrice.Properties.Appearance.Options.UseTextOptions = true;
            this.txtSmallUOMPrice.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.txtSmallUOMPrice.Properties.AutoHeight = ((bool)(resources.GetObject("txtSmallUOMPrice.Properties.AutoHeight")));
            this.txtSmallUOMPrice.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtSmallUOMPrice.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtSmallUOMPrice.Properties.Mask.AutoComplete")));
            this.txtSmallUOMPrice.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtSmallUOMPrice.Properties.Mask.BeepOnError")));
            this.txtSmallUOMPrice.Properties.Mask.EditMask = resources.GetString("txtSmallUOMPrice.Properties.Mask.EditMask");
            this.txtSmallUOMPrice.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtSmallUOMPrice.Properties.Mask.IgnoreMaskBlank")));
            this.txtSmallUOMPrice.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtSmallUOMPrice.Properties.Mask.MaskType")));
            this.txtSmallUOMPrice.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtSmallUOMPrice.Properties.Mask.PlaceHolder")));
            this.txtSmallUOMPrice.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtSmallUOMPrice.Properties.Mask.SaveLiteral")));
            this.txtSmallUOMPrice.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtSmallUOMPrice.Properties.Mask.ShowPlaceHolders")));
            this.txtSmallUOMPrice.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtSmallUOMPrice.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtSmallUOMPrice.Properties.NullValuePrompt = resources.GetString("txtSmallUOMPrice.Properties.NullValuePrompt");
            this.txtSmallUOMPrice.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtSmallUOMPrice.Properties.NullValuePromptShowForEmptyValue")));
            this.txtSmallUOMPrice.Modified += new System.EventHandler(this.Editors_Modified);
            this.txtSmallUOMPrice.EditValueChanging += new DevExpress.XtraEditors.Controls.ChangingEventHandler(this.txtPurchasePrice_EditValueChanging);
            this.txtSmallUOMPrice.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtNumberOnly_KeyPress);
            this.txtSmallUOMPrice.Leave += new System.EventHandler(this.txtPurchaseTaxRatio_Leave);
            // 
            // txtMediumUOMPrice
            // 
            resources.ApplyResources(this.txtMediumUOMPrice, "txtMediumUOMPrice");
            this.txtMediumUOMPrice.EnterMoveNextControl = true;
            this.txtMediumUOMPrice.MenuManager = this.barManager1;
            this.txtMediumUOMPrice.Name = "txtMediumUOMPrice";
            this.txtMediumUOMPrice.Properties.AccessibleDescription = resources.GetString("txtMediumUOMPrice.Properties.AccessibleDescription");
            this.txtMediumUOMPrice.Properties.AccessibleName = resources.GetString("txtMediumUOMPrice.Properties.AccessibleName");
            this.txtMediumUOMPrice.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtMediumUOMPrice.Properties.Appearance.FontSizeDelta")));
            this.txtMediumUOMPrice.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtMediumUOMPrice.Properties.Appearance.FontStyleDelta")));
            this.txtMediumUOMPrice.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtMediumUOMPrice.Properties.Appearance.GradientMode")));
            this.txtMediumUOMPrice.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtMediumUOMPrice.Properties.Appearance.Image")));
            this.txtMediumUOMPrice.Properties.Appearance.Options.UseTextOptions = true;
            this.txtMediumUOMPrice.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.txtMediumUOMPrice.Properties.AutoHeight = ((bool)(resources.GetObject("txtMediumUOMPrice.Properties.AutoHeight")));
            this.txtMediumUOMPrice.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtMediumUOMPrice.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtMediumUOMPrice.Properties.Mask.AutoComplete")));
            this.txtMediumUOMPrice.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtMediumUOMPrice.Properties.Mask.BeepOnError")));
            this.txtMediumUOMPrice.Properties.Mask.EditMask = resources.GetString("txtMediumUOMPrice.Properties.Mask.EditMask");
            this.txtMediumUOMPrice.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtMediumUOMPrice.Properties.Mask.IgnoreMaskBlank")));
            this.txtMediumUOMPrice.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtMediumUOMPrice.Properties.Mask.MaskType")));
            this.txtMediumUOMPrice.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtMediumUOMPrice.Properties.Mask.PlaceHolder")));
            this.txtMediumUOMPrice.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtMediumUOMPrice.Properties.Mask.SaveLiteral")));
            this.txtMediumUOMPrice.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtMediumUOMPrice.Properties.Mask.ShowPlaceHolders")));
            this.txtMediumUOMPrice.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtMediumUOMPrice.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtMediumUOMPrice.Properties.NullValuePrompt = resources.GetString("txtMediumUOMPrice.Properties.NullValuePrompt");
            this.txtMediumUOMPrice.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtMediumUOMPrice.Properties.NullValuePromptShowForEmptyValue")));
            this.txtMediumUOMPrice.Modified += new System.EventHandler(this.Editors_Modified);
            this.txtMediumUOMPrice.EditValueChanging += new DevExpress.XtraEditors.Controls.ChangingEventHandler(this.txtPurchasePrice_EditValueChanging);
            this.txtMediumUOMPrice.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtNumberOnly_KeyPress);
            // 
            // txtLargeUOMPrice
            // 
            resources.ApplyResources(this.txtLargeUOMPrice, "txtLargeUOMPrice");
            this.txtLargeUOMPrice.EnterMoveNextControl = true;
            this.txtLargeUOMPrice.MenuManager = this.barManager1;
            this.txtLargeUOMPrice.Name = "txtLargeUOMPrice";
            this.txtLargeUOMPrice.Properties.AccessibleDescription = resources.GetString("txtLargeUOMPrice.Properties.AccessibleDescription");
            this.txtLargeUOMPrice.Properties.AccessibleName = resources.GetString("txtLargeUOMPrice.Properties.AccessibleName");
            this.txtLargeUOMPrice.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtLargeUOMPrice.Properties.Appearance.FontSizeDelta")));
            this.txtLargeUOMPrice.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtLargeUOMPrice.Properties.Appearance.FontStyleDelta")));
            this.txtLargeUOMPrice.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtLargeUOMPrice.Properties.Appearance.GradientMode")));
            this.txtLargeUOMPrice.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtLargeUOMPrice.Properties.Appearance.Image")));
            this.txtLargeUOMPrice.Properties.Appearance.Options.UseTextOptions = true;
            this.txtLargeUOMPrice.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.txtLargeUOMPrice.Properties.AutoHeight = ((bool)(resources.GetObject("txtLargeUOMPrice.Properties.AutoHeight")));
            this.txtLargeUOMPrice.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtLargeUOMPrice.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtLargeUOMPrice.Properties.Mask.AutoComplete")));
            this.txtLargeUOMPrice.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtLargeUOMPrice.Properties.Mask.BeepOnError")));
            this.txtLargeUOMPrice.Properties.Mask.EditMask = resources.GetString("txtLargeUOMPrice.Properties.Mask.EditMask");
            this.txtLargeUOMPrice.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtLargeUOMPrice.Properties.Mask.IgnoreMaskBlank")));
            this.txtLargeUOMPrice.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtLargeUOMPrice.Properties.Mask.MaskType")));
            this.txtLargeUOMPrice.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtLargeUOMPrice.Properties.Mask.PlaceHolder")));
            this.txtLargeUOMPrice.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtLargeUOMPrice.Properties.Mask.SaveLiteral")));
            this.txtLargeUOMPrice.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtLargeUOMPrice.Properties.Mask.ShowPlaceHolders")));
            this.txtLargeUOMPrice.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtLargeUOMPrice.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtLargeUOMPrice.Properties.NullValuePrompt = resources.GetString("txtLargeUOMPrice.Properties.NullValuePrompt");
            this.txtLargeUOMPrice.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtLargeUOMPrice.Properties.NullValuePromptShowForEmptyValue")));
            this.txtLargeUOMPrice.Modified += new System.EventHandler(this.Editors_Modified);
            this.txtLargeUOMPrice.EditValueChanging += new DevExpress.XtraEditors.Controls.ChangingEventHandler(this.txtPurchasePrice_EditValueChanging);
            this.txtLargeUOMPrice.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtNumberOnly_KeyPress);
            // 
            // labelControl3
            // 
            resources.ApplyResources(this.labelControl3, "labelControl3");
            this.labelControl3.Name = "labelControl3";
            // 
            // txtItemNameEn
            // 
            resources.ApplyResources(this.txtItemNameEn, "txtItemNameEn");
            this.txtItemNameEn.EnterMoveNextControl = true;
            this.txtItemNameEn.Name = "txtItemNameEn";
            this.txtItemNameEn.Properties.AccessibleDescription = resources.GetString("txtItemNameEn.Properties.AccessibleDescription");
            this.txtItemNameEn.Properties.AccessibleName = resources.GetString("txtItemNameEn.Properties.AccessibleName");
            this.txtItemNameEn.Properties.AutoHeight = ((bool)(resources.GetObject("txtItemNameEn.Properties.AutoHeight")));
            this.txtItemNameEn.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtItemNameEn.Properties.Mask.AutoComplete")));
            this.txtItemNameEn.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtItemNameEn.Properties.Mask.BeepOnError")));
            this.txtItemNameEn.Properties.Mask.EditMask = resources.GetString("txtItemNameEn.Properties.Mask.EditMask");
            this.txtItemNameEn.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtItemNameEn.Properties.Mask.IgnoreMaskBlank")));
            this.txtItemNameEn.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtItemNameEn.Properties.Mask.MaskType")));
            this.txtItemNameEn.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtItemNameEn.Properties.Mask.PlaceHolder")));
            this.txtItemNameEn.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtItemNameEn.Properties.Mask.SaveLiteral")));
            this.txtItemNameEn.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtItemNameEn.Properties.Mask.ShowPlaceHolders")));
            this.txtItemNameEn.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtItemNameEn.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtItemNameEn.Properties.MaxLength = 50;
            this.txtItemNameEn.Properties.NullValuePrompt = resources.GetString("txtItemNameEn.Properties.NullValuePrompt");
            this.txtItemNameEn.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtItemNameEn.Properties.NullValuePromptShowForEmptyValue")));
            this.txtItemNameEn.Modified += new System.EventHandler(this.Editors_Modified);
            // 
            // btnNext
            // 
            resources.ApplyResources(this.btnNext, "btnNext");
            this.btnNext.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnNext.Image = global::Pharmacy.Properties.Resources.nxt;
            this.btnNext.Name = "btnNext";
            this.btnNext.TabStop = false;
            this.btnNext.Click += new System.EventHandler(this.btnNext_Click);
            // 
            // btnPrev
            // 
            resources.ApplyResources(this.btnPrev, "btnPrev");
            this.btnPrev.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnPrev.Image = global::Pharmacy.Properties.Resources.prev32;
            this.btnPrev.Name = "btnPrev";
            this.btnPrev.TabStop = false;
            this.btnPrev.Click += new System.EventHandler(this.btnPrev_Click);
            // 
            // btnDelete
            // 
            resources.ApplyResources(this.btnDelete, "btnDelete");
            this.btnDelete.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("btnDelete.Appearance.Font")));
            this.btnDelete.Appearance.FontSizeDelta = ((int)(resources.GetObject("btnDelete.Appearance.FontSizeDelta")));
            this.btnDelete.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("btnDelete.Appearance.FontStyleDelta")));
            this.btnDelete.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("btnDelete.Appearance.ForeColor")));
            this.btnDelete.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("btnDelete.Appearance.GradientMode")));
            this.btnDelete.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("btnDelete.Appearance.Image")));
            this.btnDelete.Appearance.Options.UseFont = true;
            this.btnDelete.Appearance.Options.UseForeColor = true;
            this.btnDelete.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnDelete.Image = global::Pharmacy.Properties.Resources.del;
            this.btnDelete.Name = "btnDelete";
            this.btnDelete.TabStop = false;
            this.btnDelete.Click += new System.EventHandler(this.btnDeleteInternationalCode_Click);
            // 
            // txtInternationalCode
            // 
            resources.ApplyResources(this.txtInternationalCode, "txtInternationalCode");
            this.txtInternationalCode.Name = "txtInternationalCode";
            this.txtInternationalCode.Properties.AccessibleDescription = resources.GetString("txtInternationalCode.Properties.AccessibleDescription");
            this.txtInternationalCode.Properties.AccessibleName = resources.GetString("txtInternationalCode.Properties.AccessibleName");
            this.txtInternationalCode.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtInternationalCode.Properties.Appearance.FontSizeDelta")));
            this.txtInternationalCode.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtInternationalCode.Properties.Appearance.FontStyleDelta")));
            this.txtInternationalCode.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtInternationalCode.Properties.Appearance.GradientMode")));
            this.txtInternationalCode.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtInternationalCode.Properties.Appearance.Image")));
            this.txtInternationalCode.Properties.Appearance.Options.UseTextOptions = true;
            this.txtInternationalCode.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.txtInternationalCode.Properties.AutoHeight = ((bool)(resources.GetObject("txtInternationalCode.Properties.AutoHeight")));
            this.txtInternationalCode.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtInternationalCode.Properties.Mask.AutoComplete")));
            this.txtInternationalCode.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtInternationalCode.Properties.Mask.BeepOnError")));
            this.txtInternationalCode.Properties.Mask.EditMask = resources.GetString("txtInternationalCode.Properties.Mask.EditMask");
            this.txtInternationalCode.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtInternationalCode.Properties.Mask.IgnoreMaskBlank")));
            this.txtInternationalCode.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtInternationalCode.Properties.Mask.MaskType")));
            this.txtInternationalCode.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtInternationalCode.Properties.Mask.PlaceHolder")));
            this.txtInternationalCode.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtInternationalCode.Properties.Mask.SaveLiteral")));
            this.txtInternationalCode.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtInternationalCode.Properties.Mask.ShowPlaceHolders")));
            this.txtInternationalCode.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtInternationalCode.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtInternationalCode.Properties.MaxLength = 100;
            this.txtInternationalCode.Properties.NullValuePrompt = resources.GetString("txtInternationalCode.Properties.NullValuePrompt");
            this.txtInternationalCode.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtInternationalCode.Properties.NullValuePromptShowForEmptyValue")));
            this.txtInternationalCode.Properties.ReadOnly = true;
            this.txtInternationalCode.Modified += new System.EventHandler(this.Editors_Modified);
            this.txtInternationalCode.KeyDown += new System.Windows.Forms.KeyEventHandler(this.txtInternationalCode_KeyDown);
            // 
            // labelControl36
            // 
            resources.ApplyResources(this.labelControl36, "labelControl36");
            this.labelControl36.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl36.Appearance.FontSizeDelta")));
            this.labelControl36.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl36.Appearance.FontStyleDelta")));
            this.labelControl36.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl36.Appearance.GradientMode")));
            this.labelControl36.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl36.Appearance.Image")));
            this.labelControl36.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.labelControl36.Name = "labelControl36";
            // 
            // grp_international
            // 
            resources.ApplyResources(this.grp_international, "grp_international");
            this.grp_international.Controls.Add(this.lstInternationalCodes);
            this.grp_international.Controls.Add(this.btn_AddNewInter_Code);
            this.grp_international.Controls.Add(this.txtInternationalCode);
            this.grp_international.Controls.Add(this.btnDelete);
            this.grp_international.Name = "grp_international";
            this.grp_international.TabStop = false;
            // 
            // grp_ItemType
            // 
            resources.ApplyResources(this.grp_ItemType, "grp_ItemType");
            this.grp_ItemType.Controls.Add(this.chk_VariableWeight);
            this.grp_ItemType.Controls.Add(this.chk_PricingWithSmall);
            this.grp_ItemType.Controls.Add(this.chk_IsLibra);
            this.grp_ItemType.Controls.Add(this.chk_IsPos);
            this.grp_ItemType.Controls.Add(this.chkIsExpire);
            this.grp_ItemType.Controls.Add(this.rdoItemType);
            this.grp_ItemType.Name = "grp_ItemType";
            this.grp_ItemType.TabStop = false;
            // 
            // chk_VariableWeight
            // 
            resources.ApplyResources(this.chk_VariableWeight, "chk_VariableWeight");
            this.chk_VariableWeight.MenuManager = this.barManager1;
            this.chk_VariableWeight.Name = "chk_VariableWeight";
            this.chk_VariableWeight.Properties.AccessibleDescription = resources.GetString("chk_VariableWeight.Properties.AccessibleDescription");
            this.chk_VariableWeight.Properties.AccessibleName = resources.GetString("chk_VariableWeight.Properties.AccessibleName");
            this.chk_VariableWeight.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("chk_VariableWeight.Properties.Appearance.FontSizeDelta")));
            this.chk_VariableWeight.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("chk_VariableWeight.Properties.Appearance.FontStyleDelta")));
            this.chk_VariableWeight.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("chk_VariableWeight.Properties.Appearance.GradientMode")));
            this.chk_VariableWeight.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("chk_VariableWeight.Properties.Appearance.Image")));
            this.chk_VariableWeight.Properties.Appearance.Options.UseTextOptions = true;
            this.chk_VariableWeight.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chk_VariableWeight.Properties.AutoHeight = ((bool)(resources.GetObject("chk_VariableWeight.Properties.AutoHeight")));
            this.chk_VariableWeight.Properties.Caption = resources.GetString("chk_VariableWeight.Properties.Caption");
            this.chk_VariableWeight.Properties.DisplayValueChecked = resources.GetString("chk_VariableWeight.Properties.DisplayValueChecked");
            this.chk_VariableWeight.Properties.DisplayValueGrayed = resources.GetString("chk_VariableWeight.Properties.DisplayValueGrayed");
            this.chk_VariableWeight.Properties.DisplayValueUnchecked = resources.GetString("chk_VariableWeight.Properties.DisplayValueUnchecked");
            this.chk_VariableWeight.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("chk_VariableWeight.Properties.GlyphAlignment")));
            this.chk_VariableWeight.CheckedChanged += new System.EventHandler(this.chk_VariableWeight_CheckedChanged);
            // 
            // chk_PricingWithSmall
            // 
            resources.ApplyResources(this.chk_PricingWithSmall, "chk_PricingWithSmall");
            this.chk_PricingWithSmall.MenuManager = this.barManager1;
            this.chk_PricingWithSmall.Name = "chk_PricingWithSmall";
            this.chk_PricingWithSmall.Properties.AccessibleDescription = resources.GetString("chk_PricingWithSmall.Properties.AccessibleDescription");
            this.chk_PricingWithSmall.Properties.AccessibleName = resources.GetString("chk_PricingWithSmall.Properties.AccessibleName");
            this.chk_PricingWithSmall.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("chk_PricingWithSmall.Properties.Appearance.FontSizeDelta")));
            this.chk_PricingWithSmall.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("chk_PricingWithSmall.Properties.Appearance.FontStyleDelta")));
            this.chk_PricingWithSmall.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("chk_PricingWithSmall.Properties.Appearance.GradientMode")));
            this.chk_PricingWithSmall.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("chk_PricingWithSmall.Properties.Appearance.Image")));
            this.chk_PricingWithSmall.Properties.Appearance.Options.UseTextOptions = true;
            this.chk_PricingWithSmall.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chk_PricingWithSmall.Properties.AutoHeight = ((bool)(resources.GetObject("chk_PricingWithSmall.Properties.AutoHeight")));
            this.chk_PricingWithSmall.Properties.Caption = resources.GetString("chk_PricingWithSmall.Properties.Caption");
            this.chk_PricingWithSmall.Properties.DisplayValueChecked = resources.GetString("chk_PricingWithSmall.Properties.DisplayValueChecked");
            this.chk_PricingWithSmall.Properties.DisplayValueGrayed = resources.GetString("chk_PricingWithSmall.Properties.DisplayValueGrayed");
            this.chk_PricingWithSmall.Properties.DisplayValueUnchecked = resources.GetString("chk_PricingWithSmall.Properties.DisplayValueUnchecked");
            this.chk_PricingWithSmall.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("chk_PricingWithSmall.Properties.GlyphAlignment")));
            this.chk_PricingWithSmall.CheckedChanged += new System.EventHandler(this.chk_PricingWithSmall_CheckedChanged);
            // 
            // chk_IsLibra
            // 
            resources.ApplyResources(this.chk_IsLibra, "chk_IsLibra");
            this.chk_IsLibra.MenuManager = this.barManager1;
            this.chk_IsLibra.Name = "chk_IsLibra";
            this.chk_IsLibra.Properties.AccessibleDescription = resources.GetString("chk_IsLibra.Properties.AccessibleDescription");
            this.chk_IsLibra.Properties.AccessibleName = resources.GetString("chk_IsLibra.Properties.AccessibleName");
            this.chk_IsLibra.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("chk_IsLibra.Properties.Appearance.FontSizeDelta")));
            this.chk_IsLibra.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("chk_IsLibra.Properties.Appearance.FontStyleDelta")));
            this.chk_IsLibra.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("chk_IsLibra.Properties.Appearance.GradientMode")));
            this.chk_IsLibra.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("chk_IsLibra.Properties.Appearance.Image")));
            this.chk_IsLibra.Properties.Appearance.Options.UseTextOptions = true;
            this.chk_IsLibra.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chk_IsLibra.Properties.AutoHeight = ((bool)(resources.GetObject("chk_IsLibra.Properties.AutoHeight")));
            this.chk_IsLibra.Properties.Caption = resources.GetString("chk_IsLibra.Properties.Caption");
            this.chk_IsLibra.Properties.DisplayValueChecked = resources.GetString("chk_IsLibra.Properties.DisplayValueChecked");
            this.chk_IsLibra.Properties.DisplayValueGrayed = resources.GetString("chk_IsLibra.Properties.DisplayValueGrayed");
            this.chk_IsLibra.Properties.DisplayValueUnchecked = resources.GetString("chk_IsLibra.Properties.DisplayValueUnchecked");
            this.chk_IsLibra.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("chk_IsLibra.Properties.GlyphAlignment")));
            this.chk_IsLibra.CheckedChanged += new System.EventHandler(this.chk_IsLibra_CheckedChanged);
            // 
            // chk_IsPos
            // 
            resources.ApplyResources(this.chk_IsPos, "chk_IsPos");
            this.chk_IsPos.MenuManager = this.barManager1;
            this.chk_IsPos.Name = "chk_IsPos";
            this.chk_IsPos.Properties.AccessibleDescription = resources.GetString("chk_IsPos.Properties.AccessibleDescription");
            this.chk_IsPos.Properties.AccessibleName = resources.GetString("chk_IsPos.Properties.AccessibleName");
            this.chk_IsPos.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("chk_IsPos.Properties.Appearance.FontSizeDelta")));
            this.chk_IsPos.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("chk_IsPos.Properties.Appearance.FontStyleDelta")));
            this.chk_IsPos.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("chk_IsPos.Properties.Appearance.GradientMode")));
            this.chk_IsPos.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("chk_IsPos.Properties.Appearance.Image")));
            this.chk_IsPos.Properties.Appearance.Options.UseTextOptions = true;
            this.chk_IsPos.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chk_IsPos.Properties.AutoHeight = ((bool)(resources.GetObject("chk_IsPos.Properties.AutoHeight")));
            this.chk_IsPos.Properties.Caption = resources.GetString("chk_IsPos.Properties.Caption");
            this.chk_IsPos.Properties.DisplayValueChecked = resources.GetString("chk_IsPos.Properties.DisplayValueChecked");
            this.chk_IsPos.Properties.DisplayValueGrayed = resources.GetString("chk_IsPos.Properties.DisplayValueGrayed");
            this.chk_IsPos.Properties.DisplayValueUnchecked = resources.GetString("chk_IsPos.Properties.DisplayValueUnchecked");
            this.chk_IsPos.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("chk_IsPos.Properties.GlyphAlignment")));
            this.chk_IsPos.Modified += new System.EventHandler(this.Editors_Modified);
            // 
            // chkIsExpire
            // 
            resources.ApplyResources(this.chkIsExpire, "chkIsExpire");
            this.chkIsExpire.MenuManager = this.barManager1;
            this.chkIsExpire.Name = "chkIsExpire";
            this.chkIsExpire.Properties.AccessibleDescription = resources.GetString("chkIsExpire.Properties.AccessibleDescription");
            this.chkIsExpire.Properties.AccessibleName = resources.GetString("chkIsExpire.Properties.AccessibleName");
            this.chkIsExpire.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("chkIsExpire.Properties.Appearance.FontSizeDelta")));
            this.chkIsExpire.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("chkIsExpire.Properties.Appearance.FontStyleDelta")));
            this.chkIsExpire.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("chkIsExpire.Properties.Appearance.GradientMode")));
            this.chkIsExpire.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("chkIsExpire.Properties.Appearance.Image")));
            this.chkIsExpire.Properties.Appearance.Options.UseTextOptions = true;
            this.chkIsExpire.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chkIsExpire.Properties.AutoHeight = ((bool)(resources.GetObject("chkIsExpire.Properties.AutoHeight")));
            this.chkIsExpire.Properties.Caption = resources.GetString("chkIsExpire.Properties.Caption");
            this.chkIsExpire.Properties.DisplayValueChecked = resources.GetString("chkIsExpire.Properties.DisplayValueChecked");
            this.chkIsExpire.Properties.DisplayValueGrayed = resources.GetString("chkIsExpire.Properties.DisplayValueGrayed");
            this.chkIsExpire.Properties.DisplayValueUnchecked = resources.GetString("chkIsExpire.Properties.DisplayValueUnchecked");
            this.chkIsExpire.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("chkIsExpire.Properties.GlyphAlignment")));
            // 
            // rdoItemType
            // 
            resources.ApplyResources(this.rdoItemType, "rdoItemType");
            this.rdoItemType.MenuManager = this.barManager1;
            this.rdoItemType.Name = "rdoItemType";
            this.rdoItemType.Properties.AccessibleDescription = resources.GetString("rdoItemType.Properties.AccessibleDescription");
            this.rdoItemType.Properties.AccessibleName = resources.GetString("rdoItemType.Properties.AccessibleName");
            this.rdoItemType.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("rdoItemType.Properties.Appearance.BackColor")));
            this.rdoItemType.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("rdoItemType.Properties.Appearance.FontSizeDelta")));
            this.rdoItemType.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("rdoItemType.Properties.Appearance.FontStyleDelta")));
            this.rdoItemType.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("rdoItemType.Properties.Appearance.GradientMode")));
            this.rdoItemType.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("rdoItemType.Properties.Appearance.Image")));
            this.rdoItemType.Properties.Appearance.Options.UseBackColor = true;
            this.rdoItemType.Properties.Appearance.Options.UseTextOptions = true;
            this.rdoItemType.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.rdoItemType.Properties.Columns = 1;
            this.rdoItemType.Properties.GlyphAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.rdoItemType.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(((object)(resources.GetObject("rdoItemType.Properties.Items"))), resources.GetString("rdoItemType.Properties.Items1")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(((object)(resources.GetObject("rdoItemType.Properties.Items2"))), resources.GetString("rdoItemType.Properties.Items3")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(((object)(resources.GetObject("rdoItemType.Properties.Items4"))), resources.GetString("rdoItemType.Properties.Items5")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(((object)(resources.GetObject("rdoItemType.Properties.Items6"))), resources.GetString("rdoItemType.Properties.Items7")),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(((object)(resources.GetObject("rdoItemType.Properties.Items8"))), resources.GetString("rdoItemType.Properties.Items9"))});
            this.rdoItemType.EditValueChanged += new System.EventHandler(this.rdoItemType_EditValueChanged);
            this.rdoItemType.Modified += new System.EventHandler(this.Editors_Modified);
            this.rdoItemType.EditValueChanging += new DevExpress.XtraEditors.Controls.ChangingEventHandler(this.rdoItemType_EditValueChanging);
            this.rdoItemType.KeyDown += new System.Windows.Forms.KeyEventHandler(this.rdoItemType_KeyDown);
            // 
            // chk_IsDeleted
            // 
            resources.ApplyResources(this.chk_IsDeleted, "chk_IsDeleted");
            this.chk_IsDeleted.MenuManager = this.barManager1;
            this.chk_IsDeleted.Name = "chk_IsDeleted";
            this.chk_IsDeleted.Properties.AccessibleDescription = resources.GetString("chk_IsDeleted.Properties.AccessibleDescription");
            this.chk_IsDeleted.Properties.AccessibleName = resources.GetString("chk_IsDeleted.Properties.AccessibleName");
            this.chk_IsDeleted.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("chk_IsDeleted.Properties.Appearance.FontSizeDelta")));
            this.chk_IsDeleted.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("chk_IsDeleted.Properties.Appearance.FontStyleDelta")));
            this.chk_IsDeleted.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("chk_IsDeleted.Properties.Appearance.GradientMode")));
            this.chk_IsDeleted.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("chk_IsDeleted.Properties.Appearance.Image")));
            this.chk_IsDeleted.Properties.Appearance.Options.UseTextOptions = true;
            this.chk_IsDeleted.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chk_IsDeleted.Properties.AutoHeight = ((bool)(resources.GetObject("chk_IsDeleted.Properties.AutoHeight")));
            this.chk_IsDeleted.Properties.Caption = resources.GetString("chk_IsDeleted.Properties.Caption");
            this.chk_IsDeleted.Properties.DisplayValueChecked = resources.GetString("chk_IsDeleted.Properties.DisplayValueChecked");
            this.chk_IsDeleted.Properties.DisplayValueGrayed = resources.GetString("chk_IsDeleted.Properties.DisplayValueGrayed");
            this.chk_IsDeleted.Properties.DisplayValueUnchecked = resources.GetString("chk_IsDeleted.Properties.DisplayValueUnchecked");
            this.chk_IsDeleted.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("chk_IsDeleted.Properties.GlyphAlignment")));
            this.chk_IsDeleted.TabStop = false;
            this.chk_IsDeleted.Modified += new System.EventHandler(this.Editors_Modified);
            // 
            // tab_Control1
            // 
            resources.ApplyResources(this.tab_Control1, "tab_Control1");
            this.tab_Control1.AppearancePage.Header.FontSizeDelta = ((int)(resources.GetObject("tab_Control1.AppearancePage.Header.FontSizeDelta")));
            this.tab_Control1.AppearancePage.Header.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("tab_Control1.AppearancePage.Header.FontStyleDelta")));
            this.tab_Control1.AppearancePage.Header.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("tab_Control1.AppearancePage.Header.GradientMode")));
            this.tab_Control1.AppearancePage.Header.Image = ((System.Drawing.Image)(resources.GetObject("tab_Control1.AppearancePage.Header.Image")));
            this.tab_Control1.AppearancePage.Header.Options.UseTextOptions = true;
            this.tab_Control1.AppearancePage.Header.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.tab_Control1.Name = "tab_Control1";
            this.tab_Control1.SelectedTabPage = this.tab_MainInfo;
            this.tab_Control1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.tab_Other,
            this.tab_image,
            this.tab_PricesPerQty,
            this.tab_PriceLevels,
            this.tab_Matrix,
            this.tab_MainInfo});
            // 
            // tab_MainInfo
            // 
            resources.ApplyResources(this.tab_MainInfo, "tab_MainInfo");
            this.tab_MainInfo.Controls.Add(this.labelControl35);
            this.tab_MainInfo.Controls.Add(this.txtAudiancePrice);
            this.tab_MainInfo.Controls.Add(this.grpExtra);
            this.tab_MainInfo.Controls.Add(this.labelControl9);
            this.tab_MainInfo.Controls.Add(this.lkpCategory);
            this.tab_MainInfo.Controls.Add(this.labelControl11);
            this.tab_MainInfo.Controls.Add(this.labelControl18);
            this.tab_MainInfo.Controls.Add(this.grp_ItemType);
            this.tab_MainInfo.Controls.Add(this.btnAddComp);
            this.tab_MainInfo.Controls.Add(this.grp_international);
            this.tab_MainInfo.Controls.Add(this.btnAddCat);
            this.tab_MainInfo.Controls.Add(this.labelControl3);
            this.tab_MainInfo.Controls.Add(this.lkpComp);
            this.tab_MainInfo.Controls.Add(this.txtItemNameEn);
            this.tab_MainInfo.Controls.Add(this.labelControl36);
            this.tab_MainInfo.Controls.Add(this.labelControl6);
            this.tab_MainInfo.Controls.Add(this.grp_UOM);
            this.tab_MainInfo.Controls.Add(this.txtPurchasePrice);
            this.tab_MainInfo.Controls.Add(this.txtDesc);
            this.tab_MainInfo.Controls.Add(this.txtDescEn);
            this.tab_MainInfo.Name = "tab_MainInfo";
            // 
            // labelControl35
            // 
            resources.ApplyResources(this.labelControl35, "labelControl35");
            this.labelControl35.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl35.Appearance.FontSizeDelta")));
            this.labelControl35.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl35.Appearance.FontStyleDelta")));
            this.labelControl35.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl35.Appearance.GradientMode")));
            this.labelControl35.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl35.Appearance.Image")));
            this.labelControl35.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.labelControl35.Name = "labelControl35";
            // 
            // txtAudiancePrice
            // 
            resources.ApplyResources(this.txtAudiancePrice, "txtAudiancePrice");
            this.txtAudiancePrice.EnterMoveNextControl = true;
            this.txtAudiancePrice.Name = "txtAudiancePrice";
            this.txtAudiancePrice.Properties.AccessibleDescription = resources.GetString("txtAudiancePrice.Properties.AccessibleDescription");
            this.txtAudiancePrice.Properties.AccessibleName = resources.GetString("txtAudiancePrice.Properties.AccessibleName");
            this.txtAudiancePrice.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtAudiancePrice.Properties.Appearance.FontSizeDelta")));
            this.txtAudiancePrice.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtAudiancePrice.Properties.Appearance.FontStyleDelta")));
            this.txtAudiancePrice.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtAudiancePrice.Properties.Appearance.GradientMode")));
            this.txtAudiancePrice.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtAudiancePrice.Properties.Appearance.Image")));
            this.txtAudiancePrice.Properties.Appearance.Options.UseTextOptions = true;
            this.txtAudiancePrice.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.txtAudiancePrice.Properties.AutoHeight = ((bool)(resources.GetObject("txtAudiancePrice.Properties.AutoHeight")));
            this.txtAudiancePrice.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtAudiancePrice.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtAudiancePrice.Properties.Mask.AutoComplete")));
            this.txtAudiancePrice.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtAudiancePrice.Properties.Mask.BeepOnError")));
            this.txtAudiancePrice.Properties.Mask.EditMask = resources.GetString("txtAudiancePrice.Properties.Mask.EditMask");
            this.txtAudiancePrice.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtAudiancePrice.Properties.Mask.IgnoreMaskBlank")));
            this.txtAudiancePrice.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtAudiancePrice.Properties.Mask.MaskType")));
            this.txtAudiancePrice.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtAudiancePrice.Properties.Mask.PlaceHolder")));
            this.txtAudiancePrice.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtAudiancePrice.Properties.Mask.SaveLiteral")));
            this.txtAudiancePrice.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtAudiancePrice.Properties.Mask.ShowPlaceHolders")));
            this.txtAudiancePrice.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtAudiancePrice.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtAudiancePrice.Properties.NullValuePrompt = resources.GetString("txtAudiancePrice.Properties.NullValuePrompt");
            this.txtAudiancePrice.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtAudiancePrice.Properties.NullValuePromptShowForEmptyValue")));
            this.txtAudiancePrice.EditValueChanging += new DevExpress.XtraEditors.Controls.ChangingEventHandler(this.txtPurchasePrice_EditValueChanging);
            // 
            // grpExtra
            // 
            resources.ApplyResources(this.grpExtra, "grpExtra");
            this.grpExtra.Controls.Add(this.tabExtraData);
            this.grpExtra.Name = "grpExtra";
            this.grpExtra.TabStop = false;
            // 
            // tabExtraData
            // 
            resources.ApplyResources(this.tabExtraData, "tabExtraData");
            this.tabExtraData.AppearancePage.Header.FontSizeDelta = ((int)(resources.GetObject("tabExtraData.AppearancePage.Header.FontSizeDelta")));
            this.tabExtraData.AppearancePage.Header.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("tabExtraData.AppearancePage.Header.FontStyleDelta")));
            this.tabExtraData.AppearancePage.Header.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("tabExtraData.AppearancePage.Header.GradientMode")));
            this.tabExtraData.AppearancePage.Header.Image = ((System.Drawing.Image)(resources.GetObject("tabExtraData.AppearancePage.Header.Image")));
            this.tabExtraData.AppearancePage.Header.Options.UseTextOptions = true;
            this.tabExtraData.AppearancePage.Header.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.tabExtraData.Name = "tabExtraData";
            this.tabExtraData.SelectedTabPage = this.tab_InventoryLevels;
            this.tabExtraData.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.tabWarranty,
            this.tabPriceChange,
            this.tabDimension,
            this.tabDiscount,
            this.tabTax,
            this.tab_SubTaxes,
            this.tab_InventoryLevels});
            // 
            // tab_InventoryLevels
            // 
            resources.ApplyResources(this.tab_InventoryLevels, "tab_InventoryLevels");
            this.tab_InventoryLevels.Controls.Add(this.labelControl27);
            this.tab_InventoryLevels.Controls.Add(this.txtMinQty);
            this.tab_InventoryLevels.Controls.Add(this.txtMaxQty);
            this.tab_InventoryLevels.Controls.Add(this.txtReorder);
            this.tab_InventoryLevels.Controls.Add(this.labelControl8);
            this.tab_InventoryLevels.Controls.Add(this.labelControl28);
            this.tab_InventoryLevels.Name = "tab_InventoryLevels";
            // 
            // tabWarranty
            // 
            resources.ApplyResources(this.tabWarranty, "tabWarranty");
            this.tabWarranty.Controls.Add(this.labelControl26);
            this.tabWarranty.Controls.Add(this.txt_WarrantyMonths);
            this.tabWarranty.Name = "tabWarranty";
            // 
            // labelControl26
            // 
            resources.ApplyResources(this.labelControl26, "labelControl26");
            this.labelControl26.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl26.Appearance.FontSizeDelta")));
            this.labelControl26.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl26.Appearance.FontStyleDelta")));
            this.labelControl26.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl26.Appearance.GradientMode")));
            this.labelControl26.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl26.Appearance.Image")));
            this.labelControl26.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.labelControl26.Name = "labelControl26";
            // 
            // txt_WarrantyMonths
            // 
            resources.ApplyResources(this.txt_WarrantyMonths, "txt_WarrantyMonths");
            this.txt_WarrantyMonths.EnterMoveNextControl = true;
            this.txt_WarrantyMonths.Name = "txt_WarrantyMonths";
            this.txt_WarrantyMonths.Properties.AccessibleDescription = resources.GetString("txt_WarrantyMonths.Properties.AccessibleDescription");
            this.txt_WarrantyMonths.Properties.AccessibleName = resources.GetString("txt_WarrantyMonths.Properties.AccessibleName");
            this.txt_WarrantyMonths.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txt_WarrantyMonths.Properties.Appearance.FontSizeDelta")));
            this.txt_WarrantyMonths.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txt_WarrantyMonths.Properties.Appearance.FontStyleDelta")));
            this.txt_WarrantyMonths.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txt_WarrantyMonths.Properties.Appearance.GradientMode")));
            this.txt_WarrantyMonths.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txt_WarrantyMonths.Properties.Appearance.Image")));
            this.txt_WarrantyMonths.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_WarrantyMonths.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.txt_WarrantyMonths.Properties.AutoHeight = ((bool)(resources.GetObject("txt_WarrantyMonths.Properties.AutoHeight")));
            this.txt_WarrantyMonths.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_WarrantyMonths.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_WarrantyMonths.Properties.Mask.AutoComplete")));
            this.txt_WarrantyMonths.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_WarrantyMonths.Properties.Mask.BeepOnError")));
            this.txt_WarrantyMonths.Properties.Mask.EditMask = resources.GetString("txt_WarrantyMonths.Properties.Mask.EditMask");
            this.txt_WarrantyMonths.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_WarrantyMonths.Properties.Mask.IgnoreMaskBlank")));
            this.txt_WarrantyMonths.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_WarrantyMonths.Properties.Mask.MaskType")));
            this.txt_WarrantyMonths.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_WarrantyMonths.Properties.Mask.PlaceHolder")));
            this.txt_WarrantyMonths.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_WarrantyMonths.Properties.Mask.SaveLiteral")));
            this.txt_WarrantyMonths.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_WarrantyMonths.Properties.Mask.ShowPlaceHolders")));
            this.txt_WarrantyMonths.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_WarrantyMonths.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_WarrantyMonths.Properties.NullValuePrompt = resources.GetString("txt_WarrantyMonths.Properties.NullValuePrompt");
            this.txt_WarrantyMonths.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_WarrantyMonths.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_WarrantyMonths.Modified += new System.EventHandler(this.Editors_Modified);
            this.txt_WarrantyMonths.EditValueChanging += new DevExpress.XtraEditors.Controls.ChangingEventHandler(this.txtPurchasePrice_EditValueChanging);
            // 
            // tabPriceChange
            // 
            resources.ApplyResources(this.tabPriceChange, "tabPriceChange");
            this.tabPriceChange.Controls.Add(this.cmbChangeSellPrice);
            this.tabPriceChange.Controls.Add(this.labelControl5);
            this.tabPriceChange.Controls.Add(this.cmbChangePriceMethod);
            this.tabPriceChange.Controls.Add(this.labelControl10);
            this.tabPriceChange.Name = "tabPriceChange";
            // 
            // cmbChangeSellPrice
            // 
            resources.ApplyResources(this.cmbChangeSellPrice, "cmbChangeSellPrice");
            this.cmbChangeSellPrice.EnterMoveNextControl = true;
            this.cmbChangeSellPrice.MenuManager = this.barManager1;
            this.cmbChangeSellPrice.Name = "cmbChangeSellPrice";
            this.cmbChangeSellPrice.Properties.AccessibleDescription = resources.GetString("cmbChangeSellPrice.Properties.AccessibleDescription");
            this.cmbChangeSellPrice.Properties.AccessibleName = resources.GetString("cmbChangeSellPrice.Properties.AccessibleName");
            this.cmbChangeSellPrice.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.cmbChangeSellPrice.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("cmbChangeSellPrice.Properties.Appearance.FontSizeDelta")));
            this.cmbChangeSellPrice.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("cmbChangeSellPrice.Properties.Appearance.FontStyleDelta")));
            this.cmbChangeSellPrice.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("cmbChangeSellPrice.Properties.Appearance.GradientMode")));
            this.cmbChangeSellPrice.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("cmbChangeSellPrice.Properties.Appearance.Image")));
            this.cmbChangeSellPrice.Properties.Appearance.Options.UseTextOptions = true;
            this.cmbChangeSellPrice.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.cmbChangeSellPrice.Properties.AutoHeight = ((bool)(resources.GetObject("cmbChangeSellPrice.Properties.AutoHeight")));
            this.cmbChangeSellPrice.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("cmbChangeSellPrice.Properties.Buttons"))))});
            this.cmbChangeSellPrice.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("cmbChangeSellPrice.Properties.GlyphAlignment")));
            this.cmbChangeSellPrice.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbChangeSellPrice.Properties.Items"), ((object)(resources.GetObject("cmbChangeSellPrice.Properties.Items1"))), ((int)(resources.GetObject("cmbChangeSellPrice.Properties.Items2")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbChangeSellPrice.Properties.Items3"), ((object)(resources.GetObject("cmbChangeSellPrice.Properties.Items4"))), ((int)(resources.GetObject("cmbChangeSellPrice.Properties.Items5"))))});
            this.cmbChangeSellPrice.Properties.NullValuePrompt = resources.GetString("cmbChangeSellPrice.Properties.NullValuePrompt");
            this.cmbChangeSellPrice.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("cmbChangeSellPrice.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl5
            // 
            resources.ApplyResources(this.labelControl5, "labelControl5");
            this.labelControl5.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl5.Appearance.FontSizeDelta")));
            this.labelControl5.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl5.Appearance.FontStyleDelta")));
            this.labelControl5.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl5.Appearance.GradientMode")));
            this.labelControl5.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl5.Appearance.Image")));
            this.labelControl5.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.labelControl5.Name = "labelControl5";
            // 
            // cmbChangePriceMethod
            // 
            resources.ApplyResources(this.cmbChangePriceMethod, "cmbChangePriceMethod");
            this.cmbChangePriceMethod.EnterMoveNextControl = true;
            this.cmbChangePriceMethod.MenuManager = this.barManager1;
            this.cmbChangePriceMethod.Name = "cmbChangePriceMethod";
            this.cmbChangePriceMethod.Properties.AccessibleDescription = resources.GetString("cmbChangePriceMethod.Properties.AccessibleDescription");
            this.cmbChangePriceMethod.Properties.AccessibleName = resources.GetString("cmbChangePriceMethod.Properties.AccessibleName");
            this.cmbChangePriceMethod.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.cmbChangePriceMethod.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("cmbChangePriceMethod.Properties.Appearance.FontSizeDelta")));
            this.cmbChangePriceMethod.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("cmbChangePriceMethod.Properties.Appearance.FontStyleDelta")));
            this.cmbChangePriceMethod.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("cmbChangePriceMethod.Properties.Appearance.GradientMode")));
            this.cmbChangePriceMethod.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("cmbChangePriceMethod.Properties.Appearance.Image")));
            this.cmbChangePriceMethod.Properties.Appearance.Options.UseTextOptions = true;
            this.cmbChangePriceMethod.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.cmbChangePriceMethod.Properties.AutoHeight = ((bool)(resources.GetObject("cmbChangePriceMethod.Properties.AutoHeight")));
            this.cmbChangePriceMethod.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("cmbChangePriceMethod.Properties.Buttons"))))});
            this.cmbChangePriceMethod.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("cmbChangePriceMethod.Properties.GlyphAlignment")));
            this.cmbChangePriceMethod.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbChangePriceMethod.Properties.Items"), ((object)(resources.GetObject("cmbChangePriceMethod.Properties.Items1"))), ((int)(resources.GetObject("cmbChangePriceMethod.Properties.Items2")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbChangePriceMethod.Properties.Items3"), ((object)(resources.GetObject("cmbChangePriceMethod.Properties.Items4"))), ((int)(resources.GetObject("cmbChangePriceMethod.Properties.Items5")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbChangePriceMethod.Properties.Items6"), ((object)(resources.GetObject("cmbChangePriceMethod.Properties.Items7"))), ((int)(resources.GetObject("cmbChangePriceMethod.Properties.Items8"))))});
            this.cmbChangePriceMethod.Properties.NullValuePrompt = resources.GetString("cmbChangePriceMethod.Properties.NullValuePrompt");
            this.cmbChangePriceMethod.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("cmbChangePriceMethod.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl10
            // 
            resources.ApplyResources(this.labelControl10, "labelControl10");
            this.labelControl10.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl10.Appearance.FontSizeDelta")));
            this.labelControl10.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl10.Appearance.FontStyleDelta")));
            this.labelControl10.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl10.Appearance.GradientMode")));
            this.labelControl10.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl10.Appearance.Image")));
            this.labelControl10.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.labelControl10.Name = "labelControl10";
            // 
            // tabDimension
            // 
            resources.ApplyResources(this.tabDimension, "tabDimension");
            this.tabDimension.Controls.Add(this.labelControl22);
            this.tabDimension.Controls.Add(this.txtLength);
            this.tabDimension.Controls.Add(this.labelControl17);
            this.tabDimension.Controls.Add(this.txtWidth);
            this.tabDimension.Controls.Add(this.labelControl12);
            this.tabDimension.Controls.Add(this.txtHeight);
            this.tabDimension.Name = "tabDimension";
            // 
            // labelControl22
            // 
            resources.ApplyResources(this.labelControl22, "labelControl22");
            this.labelControl22.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl22.Appearance.FontSizeDelta")));
            this.labelControl22.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl22.Appearance.FontStyleDelta")));
            this.labelControl22.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl22.Appearance.GradientMode")));
            this.labelControl22.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl22.Appearance.Image")));
            this.labelControl22.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.labelControl22.Name = "labelControl22";
            // 
            // txtLength
            // 
            resources.ApplyResources(this.txtLength, "txtLength");
            this.txtLength.EnterMoveNextControl = true;
            this.txtLength.Name = "txtLength";
            this.txtLength.Properties.AccessibleDescription = resources.GetString("txtLength.Properties.AccessibleDescription");
            this.txtLength.Properties.AccessibleName = resources.GetString("txtLength.Properties.AccessibleName");
            this.txtLength.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txtLength.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtLength.Properties.Appearance.FontSizeDelta")));
            this.txtLength.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtLength.Properties.Appearance.FontStyleDelta")));
            this.txtLength.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtLength.Properties.Appearance.GradientMode")));
            this.txtLength.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtLength.Properties.Appearance.Image")));
            this.txtLength.Properties.Appearance.Options.UseTextOptions = true;
            this.txtLength.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.txtLength.Properties.AutoHeight = ((bool)(resources.GetObject("txtLength.Properties.AutoHeight")));
            this.txtLength.Properties.DisplayFormat.FormatString = "n0";
            this.txtLength.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.txtLength.Properties.EditFormat.FormatString = "n0";
            this.txtLength.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.txtLength.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtLength.Properties.Increment = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.txtLength.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtLength.Properties.Mask.AutoComplete")));
            this.txtLength.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtLength.Properties.Mask.BeepOnError")));
            this.txtLength.Properties.Mask.EditMask = resources.GetString("txtLength.Properties.Mask.EditMask");
            this.txtLength.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtLength.Properties.Mask.IgnoreMaskBlank")));
            this.txtLength.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtLength.Properties.Mask.MaskType")));
            this.txtLength.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtLength.Properties.Mask.PlaceHolder")));
            this.txtLength.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtLength.Properties.Mask.SaveLiteral")));
            this.txtLength.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtLength.Properties.Mask.ShowPlaceHolders")));
            this.txtLength.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtLength.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtLength.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.txtLength.Properties.NullText = resources.GetString("txtLength.Properties.NullText");
            this.txtLength.Properties.NullValuePrompt = resources.GetString("txtLength.Properties.NullValuePrompt");
            this.txtLength.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtLength.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl17
            // 
            resources.ApplyResources(this.labelControl17, "labelControl17");
            this.labelControl17.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl17.Appearance.FontSizeDelta")));
            this.labelControl17.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl17.Appearance.FontStyleDelta")));
            this.labelControl17.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl17.Appearance.GradientMode")));
            this.labelControl17.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl17.Appearance.Image")));
            this.labelControl17.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.labelControl17.Name = "labelControl17";
            // 
            // txtWidth
            // 
            resources.ApplyResources(this.txtWidth, "txtWidth");
            this.txtWidth.EnterMoveNextControl = true;
            this.txtWidth.Name = "txtWidth";
            this.txtWidth.Properties.AccessibleDescription = resources.GetString("txtWidth.Properties.AccessibleDescription");
            this.txtWidth.Properties.AccessibleName = resources.GetString("txtWidth.Properties.AccessibleName");
            this.txtWidth.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txtWidth.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtWidth.Properties.Appearance.FontSizeDelta")));
            this.txtWidth.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtWidth.Properties.Appearance.FontStyleDelta")));
            this.txtWidth.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtWidth.Properties.Appearance.GradientMode")));
            this.txtWidth.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtWidth.Properties.Appearance.Image")));
            this.txtWidth.Properties.Appearance.Options.UseTextOptions = true;
            this.txtWidth.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.txtWidth.Properties.AutoHeight = ((bool)(resources.GetObject("txtWidth.Properties.AutoHeight")));
            this.txtWidth.Properties.DisplayFormat.FormatString = "n0";
            this.txtWidth.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.txtWidth.Properties.EditFormat.FormatString = "n0";
            this.txtWidth.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.txtWidth.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtWidth.Properties.Increment = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.txtWidth.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtWidth.Properties.Mask.AutoComplete")));
            this.txtWidth.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtWidth.Properties.Mask.BeepOnError")));
            this.txtWidth.Properties.Mask.EditMask = resources.GetString("txtWidth.Properties.Mask.EditMask");
            this.txtWidth.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtWidth.Properties.Mask.IgnoreMaskBlank")));
            this.txtWidth.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtWidth.Properties.Mask.MaskType")));
            this.txtWidth.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtWidth.Properties.Mask.PlaceHolder")));
            this.txtWidth.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtWidth.Properties.Mask.SaveLiteral")));
            this.txtWidth.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtWidth.Properties.Mask.ShowPlaceHolders")));
            this.txtWidth.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtWidth.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtWidth.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.txtWidth.Properties.NullText = resources.GetString("txtWidth.Properties.NullText");
            this.txtWidth.Properties.NullValuePrompt = resources.GetString("txtWidth.Properties.NullValuePrompt");
            this.txtWidth.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtWidth.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl12
            // 
            resources.ApplyResources(this.labelControl12, "labelControl12");
            this.labelControl12.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl12.Appearance.FontSizeDelta")));
            this.labelControl12.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl12.Appearance.FontStyleDelta")));
            this.labelControl12.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl12.Appearance.GradientMode")));
            this.labelControl12.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl12.Appearance.Image")));
            this.labelControl12.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.labelControl12.Name = "labelControl12";
            // 
            // txtHeight
            // 
            resources.ApplyResources(this.txtHeight, "txtHeight");
            this.txtHeight.EnterMoveNextControl = true;
            this.txtHeight.Name = "txtHeight";
            this.txtHeight.Properties.AccessibleDescription = resources.GetString("txtHeight.Properties.AccessibleDescription");
            this.txtHeight.Properties.AccessibleName = resources.GetString("txtHeight.Properties.AccessibleName");
            this.txtHeight.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txtHeight.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtHeight.Properties.Appearance.FontSizeDelta")));
            this.txtHeight.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtHeight.Properties.Appearance.FontStyleDelta")));
            this.txtHeight.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtHeight.Properties.Appearance.GradientMode")));
            this.txtHeight.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtHeight.Properties.Appearance.Image")));
            this.txtHeight.Properties.Appearance.Options.UseTextOptions = true;
            this.txtHeight.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.txtHeight.Properties.AutoHeight = ((bool)(resources.GetObject("txtHeight.Properties.AutoHeight")));
            this.txtHeight.Properties.DisplayFormat.FormatString = "n0";
            this.txtHeight.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.txtHeight.Properties.EditFormat.FormatString = "n0";
            this.txtHeight.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.txtHeight.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtHeight.Properties.Increment = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.txtHeight.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtHeight.Properties.Mask.AutoComplete")));
            this.txtHeight.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtHeight.Properties.Mask.BeepOnError")));
            this.txtHeight.Properties.Mask.EditMask = resources.GetString("txtHeight.Properties.Mask.EditMask");
            this.txtHeight.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtHeight.Properties.Mask.IgnoreMaskBlank")));
            this.txtHeight.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtHeight.Properties.Mask.MaskType")));
            this.txtHeight.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtHeight.Properties.Mask.PlaceHolder")));
            this.txtHeight.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtHeight.Properties.Mask.SaveLiteral")));
            this.txtHeight.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtHeight.Properties.Mask.ShowPlaceHolders")));
            this.txtHeight.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtHeight.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtHeight.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.txtHeight.Properties.NullText = resources.GetString("txtHeight.Properties.NullText");
            this.txtHeight.Properties.NullValuePrompt = resources.GetString("txtHeight.Properties.NullValuePrompt");
            this.txtHeight.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtHeight.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // tabDiscount
            // 
            resources.ApplyResources(this.tabDiscount, "tabDiscount");
            this.tabDiscount.Controls.Add(this.lblSalesDiscountRatio);
            this.tabDiscount.Controls.Add(this.labelControl1);
            this.tabDiscount.Controls.Add(this.txtSalesDiscRatio);
            this.tabDiscount.Controls.Add(this.labelControl7);
            this.tabDiscount.Controls.Add(this.labelControl25);
            this.tabDiscount.Controls.Add(this.txtPurchaseDiscRatio);
            this.tabDiscount.Name = "tabDiscount";
            // 
            // lblSalesDiscountRatio
            // 
            resources.ApplyResources(this.lblSalesDiscountRatio, "lblSalesDiscountRatio");
            this.lblSalesDiscountRatio.Appearance.FontSizeDelta = ((int)(resources.GetObject("lblSalesDiscountRatio.Appearance.FontSizeDelta")));
            this.lblSalesDiscountRatio.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lblSalesDiscountRatio.Appearance.FontStyleDelta")));
            this.lblSalesDiscountRatio.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lblSalesDiscountRatio.Appearance.GradientMode")));
            this.lblSalesDiscountRatio.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lblSalesDiscountRatio.Appearance.Image")));
            this.lblSalesDiscountRatio.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lblSalesDiscountRatio.Name = "lblSalesDiscountRatio";
            // 
            // labelControl1
            // 
            resources.ApplyResources(this.labelControl1, "labelControl1");
            this.labelControl1.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl1.Appearance.FontSizeDelta")));
            this.labelControl1.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl1.Appearance.FontStyleDelta")));
            this.labelControl1.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl1.Appearance.GradientMode")));
            this.labelControl1.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl1.Appearance.Image")));
            this.labelControl1.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.labelControl1.Name = "labelControl1";
            // 
            // txtSalesDiscRatio
            // 
            resources.ApplyResources(this.txtSalesDiscRatio, "txtSalesDiscRatio");
            this.txtSalesDiscRatio.EnterMoveNextControl = true;
            this.txtSalesDiscRatio.Name = "txtSalesDiscRatio";
            this.txtSalesDiscRatio.Properties.AccessibleDescription = resources.GetString("txtSalesDiscRatio.Properties.AccessibleDescription");
            this.txtSalesDiscRatio.Properties.AccessibleName = resources.GetString("txtSalesDiscRatio.Properties.AccessibleName");
            this.txtSalesDiscRatio.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txtSalesDiscRatio.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtSalesDiscRatio.Properties.Appearance.FontSizeDelta")));
            this.txtSalesDiscRatio.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtSalesDiscRatio.Properties.Appearance.FontStyleDelta")));
            this.txtSalesDiscRatio.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtSalesDiscRatio.Properties.Appearance.GradientMode")));
            this.txtSalesDiscRatio.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtSalesDiscRatio.Properties.Appearance.Image")));
            this.txtSalesDiscRatio.Properties.Appearance.Options.UseTextOptions = true;
            this.txtSalesDiscRatio.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.txtSalesDiscRatio.Properties.AutoHeight = ((bool)(resources.GetObject("txtSalesDiscRatio.Properties.AutoHeight")));
            this.txtSalesDiscRatio.Properties.DisplayFormat.FormatString = "n0";
            this.txtSalesDiscRatio.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.txtSalesDiscRatio.Properties.EditFormat.FormatString = "n0";
            this.txtSalesDiscRatio.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.txtSalesDiscRatio.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtSalesDiscRatio.Properties.Increment = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.txtSalesDiscRatio.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtSalesDiscRatio.Properties.Mask.AutoComplete")));
            this.txtSalesDiscRatio.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtSalesDiscRatio.Properties.Mask.BeepOnError")));
            this.txtSalesDiscRatio.Properties.Mask.EditMask = resources.GetString("txtSalesDiscRatio.Properties.Mask.EditMask");
            this.txtSalesDiscRatio.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtSalesDiscRatio.Properties.Mask.IgnoreMaskBlank")));
            this.txtSalesDiscRatio.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtSalesDiscRatio.Properties.Mask.MaskType")));
            this.txtSalesDiscRatio.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtSalesDiscRatio.Properties.Mask.PlaceHolder")));
            this.txtSalesDiscRatio.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtSalesDiscRatio.Properties.Mask.SaveLiteral")));
            this.txtSalesDiscRatio.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtSalesDiscRatio.Properties.Mask.ShowPlaceHolders")));
            this.txtSalesDiscRatio.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtSalesDiscRatio.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtSalesDiscRatio.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.txtSalesDiscRatio.Properties.NullText = resources.GetString("txtSalesDiscRatio.Properties.NullText");
            this.txtSalesDiscRatio.Properties.NullValuePrompt = resources.GetString("txtSalesDiscRatio.Properties.NullValuePrompt");
            this.txtSalesDiscRatio.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtSalesDiscRatio.Properties.NullValuePromptShowForEmptyValue")));
            this.txtSalesDiscRatio.Modified += new System.EventHandler(this.Editors_Modified);
            this.txtSalesDiscRatio.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtNumberOnly_KeyPress);
            // 
            // labelControl7
            // 
            resources.ApplyResources(this.labelControl7, "labelControl7");
            this.labelControl7.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl7.Appearance.FontSizeDelta")));
            this.labelControl7.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl7.Appearance.FontStyleDelta")));
            this.labelControl7.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl7.Appearance.GradientMode")));
            this.labelControl7.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl7.Appearance.Image")));
            this.labelControl7.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.labelControl7.Name = "labelControl7";
            // 
            // labelControl25
            // 
            resources.ApplyResources(this.labelControl25, "labelControl25");
            this.labelControl25.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl25.Appearance.FontSizeDelta")));
            this.labelControl25.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl25.Appearance.FontStyleDelta")));
            this.labelControl25.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl25.Appearance.GradientMode")));
            this.labelControl25.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl25.Appearance.Image")));
            this.labelControl25.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.labelControl25.Name = "labelControl25";
            // 
            // txtPurchaseDiscRatio
            // 
            resources.ApplyResources(this.txtPurchaseDiscRatio, "txtPurchaseDiscRatio");
            this.txtPurchaseDiscRatio.EnterMoveNextControl = true;
            this.txtPurchaseDiscRatio.Name = "txtPurchaseDiscRatio";
            this.txtPurchaseDiscRatio.Properties.AccessibleDescription = resources.GetString("txtPurchaseDiscRatio.Properties.AccessibleDescription");
            this.txtPurchaseDiscRatio.Properties.AccessibleName = resources.GetString("txtPurchaseDiscRatio.Properties.AccessibleName");
            this.txtPurchaseDiscRatio.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txtPurchaseDiscRatio.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtPurchaseDiscRatio.Properties.Appearance.FontSizeDelta")));
            this.txtPurchaseDiscRatio.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtPurchaseDiscRatio.Properties.Appearance.FontStyleDelta")));
            this.txtPurchaseDiscRatio.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtPurchaseDiscRatio.Properties.Appearance.GradientMode")));
            this.txtPurchaseDiscRatio.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtPurchaseDiscRatio.Properties.Appearance.Image")));
            this.txtPurchaseDiscRatio.Properties.Appearance.Options.UseTextOptions = true;
            this.txtPurchaseDiscRatio.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.txtPurchaseDiscRatio.Properties.AutoHeight = ((bool)(resources.GetObject("txtPurchaseDiscRatio.Properties.AutoHeight")));
            this.txtPurchaseDiscRatio.Properties.DisplayFormat.FormatString = "n0";
            this.txtPurchaseDiscRatio.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.txtPurchaseDiscRatio.Properties.EditFormat.FormatString = "n0";
            this.txtPurchaseDiscRatio.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.txtPurchaseDiscRatio.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtPurchaseDiscRatio.Properties.Increment = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.txtPurchaseDiscRatio.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtPurchaseDiscRatio.Properties.Mask.AutoComplete")));
            this.txtPurchaseDiscRatio.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtPurchaseDiscRatio.Properties.Mask.BeepOnError")));
            this.txtPurchaseDiscRatio.Properties.Mask.EditMask = resources.GetString("txtPurchaseDiscRatio.Properties.Mask.EditMask");
            this.txtPurchaseDiscRatio.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtPurchaseDiscRatio.Properties.Mask.IgnoreMaskBlank")));
            this.txtPurchaseDiscRatio.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtPurchaseDiscRatio.Properties.Mask.MaskType")));
            this.txtPurchaseDiscRatio.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtPurchaseDiscRatio.Properties.Mask.PlaceHolder")));
            this.txtPurchaseDiscRatio.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtPurchaseDiscRatio.Properties.Mask.SaveLiteral")));
            this.txtPurchaseDiscRatio.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtPurchaseDiscRatio.Properties.Mask.ShowPlaceHolders")));
            this.txtPurchaseDiscRatio.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtPurchaseDiscRatio.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtPurchaseDiscRatio.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.txtPurchaseDiscRatio.Properties.NullText = resources.GetString("txtPurchaseDiscRatio.Properties.NullText");
            this.txtPurchaseDiscRatio.Properties.NullValuePrompt = resources.GetString("txtPurchaseDiscRatio.Properties.NullValuePrompt");
            this.txtPurchaseDiscRatio.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtPurchaseDiscRatio.Properties.NullValuePromptShowForEmptyValue")));
            this.txtPurchaseDiscRatio.Modified += new System.EventHandler(this.Editors_Modified);
            this.txtPurchaseDiscRatio.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtNumberOnly_KeyPress);
            // 
            // tabTax
            // 
            resources.ApplyResources(this.tabTax, "tabTax");
            this.tabTax.Controls.Add(this.txtCustomSalesTaxRatio);
            this.tabTax.Controls.Add(this.labelControl31);
            this.tabTax.Controls.Add(this.labelControl32);
            this.tabTax.Controls.Add(this.labelControl33);
            this.tabTax.Controls.Add(this.labelControl34);
            this.tabTax.Controls.Add(this.txtCustomPurchasesTaxRatio);
            this.tabTax.Controls.Add(this.chk_calcTaxBeforeDisc);
            this.tabTax.Controls.Add(this.txtSalesTaxRatio);
            this.tabTax.Controls.Add(this.lblPrTaxRatio);
            this.tabTax.Controls.Add(this.labelControl16);
            this.tabTax.Controls.Add(this.lblPrTaxVal);
            this.tabTax.Controls.Add(this.txtSalesTaxValue);
            this.tabTax.Controls.Add(this.txtPurchaseTaxValue);
            this.tabTax.Controls.Add(this.lblSalesTaxRatio);
            this.tabTax.Controls.Add(this.labelControl14);
            this.tabTax.Controls.Add(this.lblSalesTaxValue);
            this.tabTax.Controls.Add(this.txtPurchaseTaxRatio);
            this.tabTax.Name = "tabTax";
            this.tabTax.PageVisible = false;
            // 
            // txtCustomSalesTaxRatio
            // 
            resources.ApplyResources(this.txtCustomSalesTaxRatio, "txtCustomSalesTaxRatio");
            this.txtCustomSalesTaxRatio.EnterMoveNextControl = true;
            this.txtCustomSalesTaxRatio.Name = "txtCustomSalesTaxRatio";
            this.txtCustomSalesTaxRatio.Properties.AccessibleDescription = resources.GetString("txtCustomSalesTaxRatio.Properties.AccessibleDescription");
            this.txtCustomSalesTaxRatio.Properties.AccessibleName = resources.GetString("txtCustomSalesTaxRatio.Properties.AccessibleName");
            this.txtCustomSalesTaxRatio.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txtCustomSalesTaxRatio.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtCustomSalesTaxRatio.Properties.Appearance.FontSizeDelta")));
            this.txtCustomSalesTaxRatio.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtCustomSalesTaxRatio.Properties.Appearance.FontStyleDelta")));
            this.txtCustomSalesTaxRatio.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtCustomSalesTaxRatio.Properties.Appearance.GradientMode")));
            this.txtCustomSalesTaxRatio.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtCustomSalesTaxRatio.Properties.Appearance.Image")));
            this.txtCustomSalesTaxRatio.Properties.Appearance.Options.UseTextOptions = true;
            this.txtCustomSalesTaxRatio.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.txtCustomSalesTaxRatio.Properties.AutoHeight = ((bool)(resources.GetObject("txtCustomSalesTaxRatio.Properties.AutoHeight")));
            this.txtCustomSalesTaxRatio.Properties.DisplayFormat.FormatString = "n0";
            this.txtCustomSalesTaxRatio.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.txtCustomSalesTaxRatio.Properties.EditFormat.FormatString = "n0";
            this.txtCustomSalesTaxRatio.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.txtCustomSalesTaxRatio.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtCustomSalesTaxRatio.Properties.Increment = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.txtCustomSalesTaxRatio.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtCustomSalesTaxRatio.Properties.Mask.AutoComplete")));
            this.txtCustomSalesTaxRatio.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtCustomSalesTaxRatio.Properties.Mask.BeepOnError")));
            this.txtCustomSalesTaxRatio.Properties.Mask.EditMask = resources.GetString("txtCustomSalesTaxRatio.Properties.Mask.EditMask");
            this.txtCustomSalesTaxRatio.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtCustomSalesTaxRatio.Properties.Mask.IgnoreMaskBlank")));
            this.txtCustomSalesTaxRatio.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtCustomSalesTaxRatio.Properties.Mask.MaskType")));
            this.txtCustomSalesTaxRatio.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtCustomSalesTaxRatio.Properties.Mask.PlaceHolder")));
            this.txtCustomSalesTaxRatio.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtCustomSalesTaxRatio.Properties.Mask.SaveLiteral")));
            this.txtCustomSalesTaxRatio.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtCustomSalesTaxRatio.Properties.Mask.ShowPlaceHolders")));
            this.txtCustomSalesTaxRatio.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtCustomSalesTaxRatio.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtCustomSalesTaxRatio.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.txtCustomSalesTaxRatio.Properties.NullValuePrompt = resources.GetString("txtCustomSalesTaxRatio.Properties.NullValuePrompt");
            this.txtCustomSalesTaxRatio.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtCustomSalesTaxRatio.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl31
            // 
            resources.ApplyResources(this.labelControl31, "labelControl31");
            this.labelControl31.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl31.Appearance.FontSizeDelta")));
            this.labelControl31.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl31.Appearance.FontStyleDelta")));
            this.labelControl31.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl31.Appearance.GradientMode")));
            this.labelControl31.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl31.Appearance.Image")));
            this.labelControl31.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.labelControl31.Name = "labelControl31";
            // 
            // labelControl32
            // 
            resources.ApplyResources(this.labelControl32, "labelControl32");
            this.labelControl32.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl32.Appearance.FontSizeDelta")));
            this.labelControl32.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl32.Appearance.FontStyleDelta")));
            this.labelControl32.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl32.Appearance.GradientMode")));
            this.labelControl32.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl32.Appearance.Image")));
            this.labelControl32.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.labelControl32.Name = "labelControl32";
            // 
            // labelControl33
            // 
            resources.ApplyResources(this.labelControl33, "labelControl33");
            this.labelControl33.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl33.Appearance.FontSizeDelta")));
            this.labelControl33.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl33.Appearance.FontStyleDelta")));
            this.labelControl33.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl33.Appearance.GradientMode")));
            this.labelControl33.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl33.Appearance.Image")));
            this.labelControl33.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.labelControl33.Name = "labelControl33";
            // 
            // labelControl34
            // 
            resources.ApplyResources(this.labelControl34, "labelControl34");
            this.labelControl34.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl34.Appearance.FontSizeDelta")));
            this.labelControl34.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl34.Appearance.FontStyleDelta")));
            this.labelControl34.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl34.Appearance.GradientMode")));
            this.labelControl34.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl34.Appearance.Image")));
            this.labelControl34.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.labelControl34.Name = "labelControl34";
            // 
            // txtCustomPurchasesTaxRatio
            // 
            resources.ApplyResources(this.txtCustomPurchasesTaxRatio, "txtCustomPurchasesTaxRatio");
            this.txtCustomPurchasesTaxRatio.EnterMoveNextControl = true;
            this.txtCustomPurchasesTaxRatio.Name = "txtCustomPurchasesTaxRatio";
            this.txtCustomPurchasesTaxRatio.Properties.AccessibleDescription = resources.GetString("txtCustomPurchasesTaxRatio.Properties.AccessibleDescription");
            this.txtCustomPurchasesTaxRatio.Properties.AccessibleName = resources.GetString("txtCustomPurchasesTaxRatio.Properties.AccessibleName");
            this.txtCustomPurchasesTaxRatio.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txtCustomPurchasesTaxRatio.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtCustomPurchasesTaxRatio.Properties.Appearance.FontSizeDelta")));
            this.txtCustomPurchasesTaxRatio.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtCustomPurchasesTaxRatio.Properties.Appearance.FontStyleDelta")));
            this.txtCustomPurchasesTaxRatio.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtCustomPurchasesTaxRatio.Properties.Appearance.GradientMode")));
            this.txtCustomPurchasesTaxRatio.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtCustomPurchasesTaxRatio.Properties.Appearance.Image")));
            this.txtCustomPurchasesTaxRatio.Properties.Appearance.Options.UseTextOptions = true;
            this.txtCustomPurchasesTaxRatio.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.txtCustomPurchasesTaxRatio.Properties.AutoHeight = ((bool)(resources.GetObject("txtCustomPurchasesTaxRatio.Properties.AutoHeight")));
            this.txtCustomPurchasesTaxRatio.Properties.DisplayFormat.FormatString = "n0";
            this.txtCustomPurchasesTaxRatio.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.txtCustomPurchasesTaxRatio.Properties.EditFormat.FormatString = "n0";
            this.txtCustomPurchasesTaxRatio.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.txtCustomPurchasesTaxRatio.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtCustomPurchasesTaxRatio.Properties.Increment = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.txtCustomPurchasesTaxRatio.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtCustomPurchasesTaxRatio.Properties.Mask.AutoComplete")));
            this.txtCustomPurchasesTaxRatio.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtCustomPurchasesTaxRatio.Properties.Mask.BeepOnError")));
            this.txtCustomPurchasesTaxRatio.Properties.Mask.EditMask = resources.GetString("txtCustomPurchasesTaxRatio.Properties.Mask.EditMask");
            this.txtCustomPurchasesTaxRatio.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtCustomPurchasesTaxRatio.Properties.Mask.IgnoreMaskBlank")));
            this.txtCustomPurchasesTaxRatio.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtCustomPurchasesTaxRatio.Properties.Mask.MaskType")));
            this.txtCustomPurchasesTaxRatio.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtCustomPurchasesTaxRatio.Properties.Mask.PlaceHolder")));
            this.txtCustomPurchasesTaxRatio.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtCustomPurchasesTaxRatio.Properties.Mask.SaveLiteral")));
            this.txtCustomPurchasesTaxRatio.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtCustomPurchasesTaxRatio.Properties.Mask.ShowPlaceHolders")));
            this.txtCustomPurchasesTaxRatio.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtCustomPurchasesTaxRatio.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtCustomPurchasesTaxRatio.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.txtCustomPurchasesTaxRatio.Properties.NullValuePrompt = resources.GetString("txtCustomPurchasesTaxRatio.Properties.NullValuePrompt");
            this.txtCustomPurchasesTaxRatio.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtCustomPurchasesTaxRatio.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // chk_calcTaxBeforeDisc
            // 
            resources.ApplyResources(this.chk_calcTaxBeforeDisc, "chk_calcTaxBeforeDisc");
            this.chk_calcTaxBeforeDisc.MenuManager = this.barManager1;
            this.chk_calcTaxBeforeDisc.Name = "chk_calcTaxBeforeDisc";
            this.chk_calcTaxBeforeDisc.Properties.AccessibleDescription = resources.GetString("chk_calcTaxBeforeDisc.Properties.AccessibleDescription");
            this.chk_calcTaxBeforeDisc.Properties.AccessibleName = resources.GetString("chk_calcTaxBeforeDisc.Properties.AccessibleName");
            this.chk_calcTaxBeforeDisc.Properties.AutoHeight = ((bool)(resources.GetObject("chk_calcTaxBeforeDisc.Properties.AutoHeight")));
            this.chk_calcTaxBeforeDisc.Properties.Caption = resources.GetString("chk_calcTaxBeforeDisc.Properties.Caption");
            this.chk_calcTaxBeforeDisc.Properties.DisplayValueChecked = resources.GetString("chk_calcTaxBeforeDisc.Properties.DisplayValueChecked");
            this.chk_calcTaxBeforeDisc.Properties.DisplayValueGrayed = resources.GetString("chk_calcTaxBeforeDisc.Properties.DisplayValueGrayed");
            this.chk_calcTaxBeforeDisc.Properties.DisplayValueUnchecked = resources.GetString("chk_calcTaxBeforeDisc.Properties.DisplayValueUnchecked");
            this.chk_calcTaxBeforeDisc.Modified += new System.EventHandler(this.Editors_Modified);
            this.chk_calcTaxBeforeDisc.EditValueChanging += new DevExpress.XtraEditors.Controls.ChangingEventHandler(this.chk_calcTaxBeforeDisc_EditValueChanging);
            // 
            // txtSalesTaxRatio
            // 
            resources.ApplyResources(this.txtSalesTaxRatio, "txtSalesTaxRatio");
            this.txtSalesTaxRatio.EnterMoveNextControl = true;
            this.txtSalesTaxRatio.Name = "txtSalesTaxRatio";
            this.txtSalesTaxRatio.Properties.AccessibleDescription = resources.GetString("txtSalesTaxRatio.Properties.AccessibleDescription");
            this.txtSalesTaxRatio.Properties.AccessibleName = resources.GetString("txtSalesTaxRatio.Properties.AccessibleName");
            this.txtSalesTaxRatio.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txtSalesTaxRatio.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtSalesTaxRatio.Properties.Appearance.FontSizeDelta")));
            this.txtSalesTaxRatio.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtSalesTaxRatio.Properties.Appearance.FontStyleDelta")));
            this.txtSalesTaxRatio.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtSalesTaxRatio.Properties.Appearance.GradientMode")));
            this.txtSalesTaxRatio.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtSalesTaxRatio.Properties.Appearance.Image")));
            this.txtSalesTaxRatio.Properties.Appearance.Options.UseTextOptions = true;
            this.txtSalesTaxRatio.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.txtSalesTaxRatio.Properties.AutoHeight = ((bool)(resources.GetObject("txtSalesTaxRatio.Properties.AutoHeight")));
            this.txtSalesTaxRatio.Properties.DisplayFormat.FormatString = "n0";
            this.txtSalesTaxRatio.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.txtSalesTaxRatio.Properties.EditFormat.FormatString = "n0";
            this.txtSalesTaxRatio.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.txtSalesTaxRatio.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtSalesTaxRatio.Properties.Increment = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.txtSalesTaxRatio.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtSalesTaxRatio.Properties.Mask.AutoComplete")));
            this.txtSalesTaxRatio.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtSalesTaxRatio.Properties.Mask.BeepOnError")));
            this.txtSalesTaxRatio.Properties.Mask.EditMask = resources.GetString("txtSalesTaxRatio.Properties.Mask.EditMask");
            this.txtSalesTaxRatio.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtSalesTaxRatio.Properties.Mask.IgnoreMaskBlank")));
            this.txtSalesTaxRatio.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtSalesTaxRatio.Properties.Mask.MaskType")));
            this.txtSalesTaxRatio.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtSalesTaxRatio.Properties.Mask.PlaceHolder")));
            this.txtSalesTaxRatio.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtSalesTaxRatio.Properties.Mask.SaveLiteral")));
            this.txtSalesTaxRatio.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtSalesTaxRatio.Properties.Mask.ShowPlaceHolders")));
            this.txtSalesTaxRatio.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtSalesTaxRatio.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtSalesTaxRatio.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.txtSalesTaxRatio.Properties.NullValuePrompt = resources.GetString("txtSalesTaxRatio.Properties.NullValuePrompt");
            this.txtSalesTaxRatio.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtSalesTaxRatio.Properties.NullValuePromptShowForEmptyValue")));
            this.txtSalesTaxRatio.Modified += new System.EventHandler(this.Editors_Modified);
            this.txtSalesTaxRatio.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtNumberOnly_KeyPress);
            this.txtSalesTaxRatio.Leave += new System.EventHandler(this.txtPurchaseTaxRatio_Leave);
            // 
            // lblPrTaxRatio
            // 
            resources.ApplyResources(this.lblPrTaxRatio, "lblPrTaxRatio");
            this.lblPrTaxRatio.Appearance.FontSizeDelta = ((int)(resources.GetObject("lblPrTaxRatio.Appearance.FontSizeDelta")));
            this.lblPrTaxRatio.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lblPrTaxRatio.Appearance.FontStyleDelta")));
            this.lblPrTaxRatio.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lblPrTaxRatio.Appearance.GradientMode")));
            this.lblPrTaxRatio.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lblPrTaxRatio.Appearance.Image")));
            this.lblPrTaxRatio.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lblPrTaxRatio.Name = "lblPrTaxRatio";
            // 
            // labelControl16
            // 
            resources.ApplyResources(this.labelControl16, "labelControl16");
            this.labelControl16.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl16.Appearance.FontSizeDelta")));
            this.labelControl16.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl16.Appearance.FontStyleDelta")));
            this.labelControl16.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl16.Appearance.GradientMode")));
            this.labelControl16.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl16.Appearance.Image")));
            this.labelControl16.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.labelControl16.Name = "labelControl16";
            // 
            // lblPrTaxVal
            // 
            resources.ApplyResources(this.lblPrTaxVal, "lblPrTaxVal");
            this.lblPrTaxVal.Appearance.FontSizeDelta = ((int)(resources.GetObject("lblPrTaxVal.Appearance.FontSizeDelta")));
            this.lblPrTaxVal.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lblPrTaxVal.Appearance.FontStyleDelta")));
            this.lblPrTaxVal.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lblPrTaxVal.Appearance.GradientMode")));
            this.lblPrTaxVal.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lblPrTaxVal.Appearance.Image")));
            this.lblPrTaxVal.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lblPrTaxVal.Name = "lblPrTaxVal";
            // 
            // txtSalesTaxValue
            // 
            resources.ApplyResources(this.txtSalesTaxValue, "txtSalesTaxValue");
            this.txtSalesTaxValue.EnterMoveNextControl = true;
            this.txtSalesTaxValue.Name = "txtSalesTaxValue";
            this.txtSalesTaxValue.Properties.AccessibleDescription = resources.GetString("txtSalesTaxValue.Properties.AccessibleDescription");
            this.txtSalesTaxValue.Properties.AccessibleName = resources.GetString("txtSalesTaxValue.Properties.AccessibleName");
            this.txtSalesTaxValue.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txtSalesTaxValue.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtSalesTaxValue.Properties.Appearance.FontSizeDelta")));
            this.txtSalesTaxValue.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtSalesTaxValue.Properties.Appearance.FontStyleDelta")));
            this.txtSalesTaxValue.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtSalesTaxValue.Properties.Appearance.GradientMode")));
            this.txtSalesTaxValue.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtSalesTaxValue.Properties.Appearance.Image")));
            this.txtSalesTaxValue.Properties.Appearance.Options.UseTextOptions = true;
            this.txtSalesTaxValue.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.txtSalesTaxValue.Properties.AutoHeight = ((bool)(resources.GetObject("txtSalesTaxValue.Properties.AutoHeight")));
            this.txtSalesTaxValue.Properties.DisplayFormat.FormatString = "n3";
            this.txtSalesTaxValue.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.txtSalesTaxValue.Properties.EditFormat.FormatString = "n3";
            this.txtSalesTaxValue.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.txtSalesTaxValue.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtSalesTaxValue.Properties.Increment = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.txtSalesTaxValue.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtSalesTaxValue.Properties.Mask.AutoComplete")));
            this.txtSalesTaxValue.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtSalesTaxValue.Properties.Mask.BeepOnError")));
            this.txtSalesTaxValue.Properties.Mask.EditMask = resources.GetString("txtSalesTaxValue.Properties.Mask.EditMask");
            this.txtSalesTaxValue.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtSalesTaxValue.Properties.Mask.IgnoreMaskBlank")));
            this.txtSalesTaxValue.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtSalesTaxValue.Properties.Mask.MaskType")));
            this.txtSalesTaxValue.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtSalesTaxValue.Properties.Mask.PlaceHolder")));
            this.txtSalesTaxValue.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtSalesTaxValue.Properties.Mask.SaveLiteral")));
            this.txtSalesTaxValue.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtSalesTaxValue.Properties.Mask.ShowPlaceHolders")));
            this.txtSalesTaxValue.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtSalesTaxValue.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtSalesTaxValue.Properties.MaxValue = new decimal(new int[] {
            99999999,
            0,
            0,
            0});
            this.txtSalesTaxValue.Properties.NullValuePrompt = resources.GetString("txtSalesTaxValue.Properties.NullValuePrompt");
            this.txtSalesTaxValue.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtSalesTaxValue.Properties.NullValuePromptShowForEmptyValue")));
            this.txtSalesTaxValue.Modified += new System.EventHandler(this.Editors_Modified);
            this.txtSalesTaxValue.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtNumberOnly_KeyPress);
            this.txtSalesTaxValue.Leave += new System.EventHandler(this.txtPurchaseTaxRatio_Leave);
            // 
            // txtPurchaseTaxValue
            // 
            resources.ApplyResources(this.txtPurchaseTaxValue, "txtPurchaseTaxValue");
            this.txtPurchaseTaxValue.EnterMoveNextControl = true;
            this.txtPurchaseTaxValue.Name = "txtPurchaseTaxValue";
            this.txtPurchaseTaxValue.Properties.AccessibleDescription = resources.GetString("txtPurchaseTaxValue.Properties.AccessibleDescription");
            this.txtPurchaseTaxValue.Properties.AccessibleName = resources.GetString("txtPurchaseTaxValue.Properties.AccessibleName");
            this.txtPurchaseTaxValue.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txtPurchaseTaxValue.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtPurchaseTaxValue.Properties.Appearance.FontSizeDelta")));
            this.txtPurchaseTaxValue.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtPurchaseTaxValue.Properties.Appearance.FontStyleDelta")));
            this.txtPurchaseTaxValue.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtPurchaseTaxValue.Properties.Appearance.GradientMode")));
            this.txtPurchaseTaxValue.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtPurchaseTaxValue.Properties.Appearance.Image")));
            this.txtPurchaseTaxValue.Properties.Appearance.Options.UseTextOptions = true;
            this.txtPurchaseTaxValue.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.txtPurchaseTaxValue.Properties.AutoHeight = ((bool)(resources.GetObject("txtPurchaseTaxValue.Properties.AutoHeight")));
            this.txtPurchaseTaxValue.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtPurchaseTaxValue.Properties.Increment = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.txtPurchaseTaxValue.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtPurchaseTaxValue.Properties.Mask.AutoComplete")));
            this.txtPurchaseTaxValue.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtPurchaseTaxValue.Properties.Mask.BeepOnError")));
            this.txtPurchaseTaxValue.Properties.Mask.EditMask = resources.GetString("txtPurchaseTaxValue.Properties.Mask.EditMask");
            this.txtPurchaseTaxValue.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtPurchaseTaxValue.Properties.Mask.IgnoreMaskBlank")));
            this.txtPurchaseTaxValue.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtPurchaseTaxValue.Properties.Mask.MaskType")));
            this.txtPurchaseTaxValue.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtPurchaseTaxValue.Properties.Mask.PlaceHolder")));
            this.txtPurchaseTaxValue.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtPurchaseTaxValue.Properties.Mask.SaveLiteral")));
            this.txtPurchaseTaxValue.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtPurchaseTaxValue.Properties.Mask.ShowPlaceHolders")));
            this.txtPurchaseTaxValue.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtPurchaseTaxValue.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtPurchaseTaxValue.Properties.MaxValue = new decimal(new int[] {
            99999999,
            0,
            0,
            0});
            this.txtPurchaseTaxValue.Properties.NullValuePrompt = resources.GetString("txtPurchaseTaxValue.Properties.NullValuePrompt");
            this.txtPurchaseTaxValue.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtPurchaseTaxValue.Properties.NullValuePromptShowForEmptyValue")));
            this.txtPurchaseTaxValue.Modified += new System.EventHandler(this.Editors_Modified);
            this.txtPurchaseTaxValue.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtNumberOnly_KeyPress);
            this.txtPurchaseTaxValue.Leave += new System.EventHandler(this.txtPurchaseTaxRatio_Leave);
            // 
            // lblSalesTaxRatio
            // 
            resources.ApplyResources(this.lblSalesTaxRatio, "lblSalesTaxRatio");
            this.lblSalesTaxRatio.Appearance.FontSizeDelta = ((int)(resources.GetObject("lblSalesTaxRatio.Appearance.FontSizeDelta")));
            this.lblSalesTaxRatio.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lblSalesTaxRatio.Appearance.FontStyleDelta")));
            this.lblSalesTaxRatio.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lblSalesTaxRatio.Appearance.GradientMode")));
            this.lblSalesTaxRatio.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lblSalesTaxRatio.Appearance.Image")));
            this.lblSalesTaxRatio.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lblSalesTaxRatio.Name = "lblSalesTaxRatio";
            // 
            // labelControl14
            // 
            resources.ApplyResources(this.labelControl14, "labelControl14");
            this.labelControl14.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl14.Appearance.FontSizeDelta")));
            this.labelControl14.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl14.Appearance.FontStyleDelta")));
            this.labelControl14.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl14.Appearance.GradientMode")));
            this.labelControl14.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl14.Appearance.Image")));
            this.labelControl14.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.labelControl14.Name = "labelControl14";
            // 
            // lblSalesTaxValue
            // 
            resources.ApplyResources(this.lblSalesTaxValue, "lblSalesTaxValue");
            this.lblSalesTaxValue.Appearance.FontSizeDelta = ((int)(resources.GetObject("lblSalesTaxValue.Appearance.FontSizeDelta")));
            this.lblSalesTaxValue.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lblSalesTaxValue.Appearance.FontStyleDelta")));
            this.lblSalesTaxValue.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lblSalesTaxValue.Appearance.GradientMode")));
            this.lblSalesTaxValue.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lblSalesTaxValue.Appearance.Image")));
            this.lblSalesTaxValue.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lblSalesTaxValue.Name = "lblSalesTaxValue";
            // 
            // txtPurchaseTaxRatio
            // 
            resources.ApplyResources(this.txtPurchaseTaxRatio, "txtPurchaseTaxRatio");
            this.txtPurchaseTaxRatio.EnterMoveNextControl = true;
            this.txtPurchaseTaxRatio.Name = "txtPurchaseTaxRatio";
            this.txtPurchaseTaxRatio.Properties.AccessibleDescription = resources.GetString("txtPurchaseTaxRatio.Properties.AccessibleDescription");
            this.txtPurchaseTaxRatio.Properties.AccessibleName = resources.GetString("txtPurchaseTaxRatio.Properties.AccessibleName");
            this.txtPurchaseTaxRatio.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txtPurchaseTaxRatio.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtPurchaseTaxRatio.Properties.Appearance.FontSizeDelta")));
            this.txtPurchaseTaxRatio.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtPurchaseTaxRatio.Properties.Appearance.FontStyleDelta")));
            this.txtPurchaseTaxRatio.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtPurchaseTaxRatio.Properties.Appearance.GradientMode")));
            this.txtPurchaseTaxRatio.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtPurchaseTaxRatio.Properties.Appearance.Image")));
            this.txtPurchaseTaxRatio.Properties.Appearance.Options.UseTextOptions = true;
            this.txtPurchaseTaxRatio.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.txtPurchaseTaxRatio.Properties.AutoHeight = ((bool)(resources.GetObject("txtPurchaseTaxRatio.Properties.AutoHeight")));
            this.txtPurchaseTaxRatio.Properties.DisplayFormat.FormatString = "n0";
            this.txtPurchaseTaxRatio.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.txtPurchaseTaxRatio.Properties.EditFormat.FormatString = "n0";
            this.txtPurchaseTaxRatio.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.txtPurchaseTaxRatio.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtPurchaseTaxRatio.Properties.Increment = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.txtPurchaseTaxRatio.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtPurchaseTaxRatio.Properties.Mask.AutoComplete")));
            this.txtPurchaseTaxRatio.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtPurchaseTaxRatio.Properties.Mask.BeepOnError")));
            this.txtPurchaseTaxRatio.Properties.Mask.EditMask = resources.GetString("txtPurchaseTaxRatio.Properties.Mask.EditMask");
            this.txtPurchaseTaxRatio.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtPurchaseTaxRatio.Properties.Mask.IgnoreMaskBlank")));
            this.txtPurchaseTaxRatio.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtPurchaseTaxRatio.Properties.Mask.MaskType")));
            this.txtPurchaseTaxRatio.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtPurchaseTaxRatio.Properties.Mask.PlaceHolder")));
            this.txtPurchaseTaxRatio.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtPurchaseTaxRatio.Properties.Mask.SaveLiteral")));
            this.txtPurchaseTaxRatio.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtPurchaseTaxRatio.Properties.Mask.ShowPlaceHolders")));
            this.txtPurchaseTaxRatio.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtPurchaseTaxRatio.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtPurchaseTaxRatio.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.txtPurchaseTaxRatio.Properties.NullValuePrompt = resources.GetString("txtPurchaseTaxRatio.Properties.NullValuePrompt");
            this.txtPurchaseTaxRatio.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtPurchaseTaxRatio.Properties.NullValuePromptShowForEmptyValue")));
            this.txtPurchaseTaxRatio.Modified += new System.EventHandler(this.Editors_Modified);
            this.txtPurchaseTaxRatio.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtNumberOnly_KeyPress);
            this.txtPurchaseTaxRatio.Leave += new System.EventHandler(this.txtPurchaseTaxRatio_Leave);
            // 
            // tab_SubTaxes
            // 
            resources.ApplyResources(this.tab_SubTaxes, "tab_SubTaxes");
            this.tab_SubTaxes.Controls.Add(this.grd_SubTaxes);
            this.tab_SubTaxes.Name = "tab_SubTaxes";
            // 
            // grd_SubTaxes
            // 
            resources.ApplyResources(this.grd_SubTaxes, "grd_SubTaxes");
            this.grd_SubTaxes.EmbeddedNavigator.AccessibleDescription = resources.GetString("grd_SubTaxes.EmbeddedNavigator.AccessibleDescription");
            this.grd_SubTaxes.EmbeddedNavigator.AccessibleName = resources.GetString("grd_SubTaxes.EmbeddedNavigator.AccessibleName");
            this.grd_SubTaxes.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grd_SubTaxes.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grd_SubTaxes.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grd_SubTaxes.EmbeddedNavigator.Anchor")));
            this.grd_SubTaxes.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grd_SubTaxes.EmbeddedNavigator.BackgroundImage")));
            this.grd_SubTaxes.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grd_SubTaxes.EmbeddedNavigator.BackgroundImageLayout")));
            this.grd_SubTaxes.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grd_SubTaxes.EmbeddedNavigator.ImeMode")));
            this.grd_SubTaxes.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grd_SubTaxes.EmbeddedNavigator.MaximumSize")));
            this.grd_SubTaxes.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grd_SubTaxes.EmbeddedNavigator.TextLocation")));
            this.grd_SubTaxes.EmbeddedNavigator.ToolTip = resources.GetString("grd_SubTaxes.EmbeddedNavigator.ToolTip");
            this.grd_SubTaxes.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grd_SubTaxes.EmbeddedNavigator.ToolTipIconType")));
            this.grd_SubTaxes.EmbeddedNavigator.ToolTipTitle = resources.GetString("grd_SubTaxes.EmbeddedNavigator.ToolTipTitle");
            this.grd_SubTaxes.MainView = this.gv_SubTaxes;
            this.grd_SubTaxes.Name = "grd_SubTaxes";
            this.grd_SubTaxes.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.lkp_SubTaxes});
            this.grd_SubTaxes.TabStop = false;
            this.grd_SubTaxes.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gv_SubTaxes});
            this.grd_SubTaxes.Click += new System.EventHandler(this.grd_SubTaxes_Click);
            // 
            // gv_SubTaxes
            // 
            this.gv_SubTaxes.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gv_SubTaxes.Appearance.HeaderPanel.FontSizeDelta")));
            this.gv_SubTaxes.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gv_SubTaxes.Appearance.HeaderPanel.FontStyleDelta")));
            this.gv_SubTaxes.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gv_SubTaxes.Appearance.HeaderPanel.GradientMode")));
            this.gv_SubTaxes.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gv_SubTaxes.Appearance.HeaderPanel.Image")));
            this.gv_SubTaxes.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gv_SubTaxes.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gv_SubTaxes.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gv_SubTaxes.Appearance.Row.FontSizeDelta")));
            this.gv_SubTaxes.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gv_SubTaxes.Appearance.Row.FontStyleDelta")));
            this.gv_SubTaxes.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gv_SubTaxes.Appearance.Row.GradientMode")));
            this.gv_SubTaxes.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gv_SubTaxes.Appearance.Row.Image")));
            this.gv_SubTaxes.Appearance.Row.Options.UseTextOptions = true;
            this.gv_SubTaxes.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.gv_SubTaxes, "gv_SubTaxes");
            this.gv_SubTaxes.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.IC_ItemSubTaxesId,
            this.SubTaxId,
            this.col_Rate});
            this.gv_SubTaxes.GridControl = this.grd_SubTaxes;
            this.gv_SubTaxes.HorzScrollStep = 2;
            this.gv_SubTaxes.Name = "gv_SubTaxes";
            this.gv_SubTaxes.OptionsCustomization.AllowFilter = false;
            this.gv_SubTaxes.OptionsCustomization.AllowGroup = false;
            this.gv_SubTaxes.OptionsCustomization.AllowQuickHideColumns = false;
            this.gv_SubTaxes.OptionsMenu.EnableColumnMenu = false;
            this.gv_SubTaxes.OptionsMenu.EnableFooterMenu = false;
            this.gv_SubTaxes.OptionsMenu.EnableGroupPanelMenu = false;
            this.gv_SubTaxes.OptionsMenu.ShowDateTimeGroupIntervalItems = false;
            this.gv_SubTaxes.OptionsMenu.ShowGroupSortSummaryItems = false;
            this.gv_SubTaxes.OptionsNavigation.EnterMoveNextColumn = true;
            this.gv_SubTaxes.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gv_SubTaxes.OptionsView.AnimationType = DevExpress.XtraGrid.Views.Base.GridAnimationType.NeverAnimate;
            this.gv_SubTaxes.OptionsView.EnableAppearanceEvenRow = true;
            this.gv_SubTaxes.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Bottom;
            this.gv_SubTaxes.OptionsView.ShowDetailButtons = false;
            this.gv_SubTaxes.OptionsView.ShowGroupPanel = false;
            this.gv_SubTaxes.InitNewRow += new DevExpress.XtraGrid.Views.Grid.InitNewRowEventHandler(this.gv_SubTaxes_InitNewRow);
            this.gv_SubTaxes.InvalidRowException += new DevExpress.XtraGrid.Views.Base.InvalidRowExceptionEventHandler(this.gv_SubTaxes_InvalidRowException);
            this.gv_SubTaxes.ValidateRow += new DevExpress.XtraGrid.Views.Base.ValidateRowEventHandler(this.gv_SubTaxes_ValidateRow);
            this.gv_SubTaxes.KeyDown += new System.Windows.Forms.KeyEventHandler(this.gv_SubTaxes_KeyDown);
            // 
            // IC_ItemSubTaxesId
            // 
            resources.ApplyResources(this.IC_ItemSubTaxesId, "IC_ItemSubTaxesId");
            this.IC_ItemSubTaxesId.FieldName = "IC_ItemSubTaxesId";
            this.IC_ItemSubTaxesId.Name = "IC_ItemSubTaxesId";
            this.IC_ItemSubTaxesId.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // SubTaxId
            // 
            this.SubTaxId.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("SubTaxId.AppearanceHeader.FontSizeDelta")));
            this.SubTaxId.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("SubTaxId.AppearanceHeader.FontStyleDelta")));
            this.SubTaxId.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("SubTaxId.AppearanceHeader.GradientMode")));
            this.SubTaxId.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("SubTaxId.AppearanceHeader.Image")));
            this.SubTaxId.AppearanceHeader.Options.UseTextOptions = true;
            this.SubTaxId.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.SubTaxId, "SubTaxId");
            this.SubTaxId.ColumnEdit = this.lkp_SubTaxes;
            this.SubTaxId.FieldName = "SubTaxId";
            this.SubTaxId.Name = "SubTaxId";
            this.SubTaxId.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // lkp_SubTaxes
            // 
            resources.ApplyResources(this.lkp_SubTaxes, "lkp_SubTaxes");
            this.lkp_SubTaxes.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_SubTaxes.Appearance.FontSizeDelta")));
            this.lkp_SubTaxes.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_SubTaxes.Appearance.FontStyleDelta")));
            this.lkp_SubTaxes.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_SubTaxes.Appearance.GradientMode")));
            this.lkp_SubTaxes.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_SubTaxes.Appearance.Image")));
            this.lkp_SubTaxes.Appearance.Options.UseTextOptions = true;
            this.lkp_SubTaxes.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkp_SubTaxes.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.lkp_SubTaxes.AppearanceDisabled.FontSizeDelta = ((int)(resources.GetObject("lkp_SubTaxes.AppearanceDisabled.FontSizeDelta")));
            this.lkp_SubTaxes.AppearanceDisabled.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_SubTaxes.AppearanceDisabled.FontStyleDelta")));
            this.lkp_SubTaxes.AppearanceDisabled.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_SubTaxes.AppearanceDisabled.GradientMode")));
            this.lkp_SubTaxes.AppearanceDisabled.Image = ((System.Drawing.Image)(resources.GetObject("lkp_SubTaxes.AppearanceDisabled.Image")));
            this.lkp_SubTaxes.AppearanceDisabled.Options.UseTextOptions = true;
            this.lkp_SubTaxes.AppearanceDisabled.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkp_SubTaxes.AppearanceDisabled.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.lkp_SubTaxes.AppearanceDisabled.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.lkp_SubTaxes.AppearanceDropDownHeader.FontSizeDelta = ((int)(resources.GetObject("lkp_SubTaxes.AppearanceDropDownHeader.FontSizeDelta")));
            this.lkp_SubTaxes.AppearanceDropDownHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_SubTaxes.AppearanceDropDownHeader.FontStyleDelta")));
            this.lkp_SubTaxes.AppearanceDropDownHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_SubTaxes.AppearanceDropDownHeader.GradientMode")));
            this.lkp_SubTaxes.AppearanceDropDownHeader.Image = ((System.Drawing.Image)(resources.GetObject("lkp_SubTaxes.AppearanceDropDownHeader.Image")));
            this.lkp_SubTaxes.AppearanceDropDownHeader.Options.UseTextOptions = true;
            this.lkp_SubTaxes.AppearanceDropDownHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkp_SubTaxes.AppearanceDropDownHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.lkp_SubTaxes.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_SubTaxes.Buttons"))))});
            this.lkp_SubTaxes.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_SubTaxes.Columns"), resources.GetString("lkp_SubTaxes.Columns1")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_SubTaxes.Columns2"), resources.GetString("lkp_SubTaxes.Columns3")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_SubTaxes.Columns4"), resources.GetString("lkp_SubTaxes.Columns5"), ((int)(resources.GetObject("lkp_SubTaxes.Columns6"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_SubTaxes.Columns7"))), resources.GetString("lkp_SubTaxes.Columns8"), ((bool)(resources.GetObject("lkp_SubTaxes.Columns9"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_SubTaxes.Columns10"))))});
            this.lkp_SubTaxes.Name = "lkp_SubTaxes";
            // 
            // col_Rate
            // 
            this.col_Rate.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_Rate.AppearanceHeader.FontSizeDelta")));
            this.col_Rate.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_Rate.AppearanceHeader.FontStyleDelta")));
            this.col_Rate.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_Rate.AppearanceHeader.GradientMode")));
            this.col_Rate.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_Rate.AppearanceHeader.Image")));
            this.col_Rate.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Rate.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.col_Rate, "col_Rate");
            this.col_Rate.FieldName = "Rate";
            this.col_Rate.Name = "col_Rate";
            this.col_Rate.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // labelControl9
            // 
            resources.ApplyResources(this.labelControl9, "labelControl9");
            this.labelControl9.Name = "labelControl9";
            // 
            // txtPurchasePrice
            // 
            resources.ApplyResources(this.txtPurchasePrice, "txtPurchasePrice");
            this.txtPurchasePrice.EnterMoveNextControl = true;
            this.txtPurchasePrice.Name = "txtPurchasePrice";
            this.txtPurchasePrice.Properties.AccessibleDescription = resources.GetString("txtPurchasePrice.Properties.AccessibleDescription");
            this.txtPurchasePrice.Properties.AccessibleName = resources.GetString("txtPurchasePrice.Properties.AccessibleName");
            this.txtPurchasePrice.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtPurchasePrice.Properties.Appearance.FontSizeDelta")));
            this.txtPurchasePrice.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtPurchasePrice.Properties.Appearance.FontStyleDelta")));
            this.txtPurchasePrice.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtPurchasePrice.Properties.Appearance.GradientMode")));
            this.txtPurchasePrice.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtPurchasePrice.Properties.Appearance.Image")));
            this.txtPurchasePrice.Properties.Appearance.Options.UseTextOptions = true;
            this.txtPurchasePrice.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.txtPurchasePrice.Properties.AutoHeight = ((bool)(resources.GetObject("txtPurchasePrice.Properties.AutoHeight")));
            this.txtPurchasePrice.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtPurchasePrice.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtPurchasePrice.Properties.Mask.AutoComplete")));
            this.txtPurchasePrice.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtPurchasePrice.Properties.Mask.BeepOnError")));
            this.txtPurchasePrice.Properties.Mask.EditMask = resources.GetString("txtPurchasePrice.Properties.Mask.EditMask");
            this.txtPurchasePrice.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtPurchasePrice.Properties.Mask.IgnoreMaskBlank")));
            this.txtPurchasePrice.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtPurchasePrice.Properties.Mask.MaskType")));
            this.txtPurchasePrice.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtPurchasePrice.Properties.Mask.PlaceHolder")));
            this.txtPurchasePrice.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtPurchasePrice.Properties.Mask.SaveLiteral")));
            this.txtPurchasePrice.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtPurchasePrice.Properties.Mask.ShowPlaceHolders")));
            this.txtPurchasePrice.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtPurchasePrice.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtPurchasePrice.Properties.NullValuePrompt = resources.GetString("txtPurchasePrice.Properties.NullValuePrompt");
            this.txtPurchasePrice.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtPurchasePrice.Properties.NullValuePromptShowForEmptyValue")));
            this.txtPurchasePrice.Modified += new System.EventHandler(this.Editors_Modified);
            this.txtPurchasePrice.EditValueChanging += new DevExpress.XtraEditors.Controls.ChangingEventHandler(this.txtPurchasePrice_EditValueChanging);
            this.txtPurchasePrice.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtNumberOnly_KeyPress);
            this.txtPurchasePrice.Leave += new System.EventHandler(this.txtPurchaseTaxRatio_Leave);
            // 
            // txtDesc
            // 
            resources.ApplyResources(this.txtDesc, "txtDesc");
            this.txtDesc.Name = "txtDesc";
            this.txtDesc.Properties.AccessibleDescription = resources.GetString("txtDesc.Properties.AccessibleDescription");
            this.txtDesc.Properties.AccessibleName = resources.GetString("txtDesc.Properties.AccessibleName");
            this.txtDesc.Properties.NullValuePrompt = resources.GetString("txtDesc.Properties.NullValuePrompt");
            this.txtDesc.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtDesc.Properties.NullValuePromptShowForEmptyValue")));
            this.txtDesc.Modified += new System.EventHandler(this.Editors_Modified);
            // 
            // txtDescEn
            // 
            resources.ApplyResources(this.txtDescEn, "txtDescEn");
            this.txtDescEn.Name = "txtDescEn";
            this.txtDescEn.Properties.AccessibleDescription = resources.GetString("txtDescEn.Properties.AccessibleDescription");
            this.txtDescEn.Properties.AccessibleName = resources.GetString("txtDescEn.Properties.AccessibleName");
            this.txtDescEn.Properties.NullValuePrompt = resources.GetString("txtDescEn.Properties.NullValuePrompt");
            this.txtDescEn.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtDescEn.Properties.NullValuePromptShowForEmptyValue")));
            this.txtDescEn.Modified += new System.EventHandler(this.Editors_Modified);
            // 
            // tab_Other
            // 
            resources.ApplyResources(this.tab_Other, "tab_Other");
            this.tab_Other.Controls.Add(this.groupBox5);
            this.tab_Other.Controls.Add(this.groupBox1);
            this.tab_Other.Controls.Add(this.groupBox3);
            this.tab_Other.Name = "tab_Other";
            this.tab_Other.PageVisible = false;
            // 
            // groupBox5
            // 
            resources.ApplyResources(this.groupBox5, "groupBox5");
            this.groupBox5.Controls.Add(this.gridLocation);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.TabStop = false;
            // 
            // gridLocation
            // 
            resources.ApplyResources(this.gridLocation, "gridLocation");
            this.gridLocation.Cursor = System.Windows.Forms.Cursors.Default;
            this.gridLocation.EmbeddedNavigator.AccessibleDescription = resources.GetString("gridLocation.EmbeddedNavigator.AccessibleDescription");
            this.gridLocation.EmbeddedNavigator.AccessibleName = resources.GetString("gridLocation.EmbeddedNavigator.AccessibleName");
            this.gridLocation.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("gridLocation.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.gridLocation.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("gridLocation.EmbeddedNavigator.Anchor")));
            this.gridLocation.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("gridLocation.EmbeddedNavigator.BackgroundImage")));
            this.gridLocation.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("gridLocation.EmbeddedNavigator.BackgroundImageLayout")));
            this.gridLocation.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("gridLocation.EmbeddedNavigator.ImeMode")));
            this.gridLocation.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("gridLocation.EmbeddedNavigator.MaximumSize")));
            this.gridLocation.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("gridLocation.EmbeddedNavigator.TextLocation")));
            this.gridLocation.EmbeddedNavigator.ToolTip = resources.GetString("gridLocation.EmbeddedNavigator.ToolTip");
            this.gridLocation.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("gridLocation.EmbeddedNavigator.ToolTipIconType")));
            this.gridLocation.EmbeddedNavigator.ToolTipTitle = resources.GetString("gridLocation.EmbeddedNavigator.ToolTipTitle");
            this.gridLocation.MainView = this.gridView1;
            this.gridLocation.MenuManager = this.barManager1;
            this.gridLocation.Name = "gridLocation";
            this.gridLocation.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.rep_stores});
            this.gridLocation.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // gridView1
            // 
            this.gridView1.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.HeaderPanel.FontSizeDelta")));
            this.gridView1.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.HeaderPanel.FontStyleDelta")));
            this.gridView1.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.HeaderPanel.GradientMode")));
            this.gridView1.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.HeaderPanel.Image")));
            this.gridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.Row.FontSizeDelta")));
            this.gridView1.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.Row.FontStyleDelta")));
            this.gridView1.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.Row.GradientMode")));
            this.gridView1.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.Row.Image")));
            this.gridView1.Appearance.Row.Options.UseTextOptions = true;
            this.gridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.gridView1, "gridView1");
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_Location,
            this.col_Store,
            this.gridColumn7});
            this.gridView1.GridControl = this.gridLocation;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsNavigation.EnterMoveNextColumn = true;
            this.gridView1.OptionsView.AnimationType = DevExpress.XtraGrid.Views.Base.GridAnimationType.NeverAnimate;
            this.gridView1.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView1.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Bottom;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.OptionsView.ShowIndicator = false;
            this.gridView1.PaintStyleName = "UltraFlat";
            this.gridView1.InitNewRow += new DevExpress.XtraGrid.Views.Grid.InitNewRowEventHandler(this.gridView1_InitNewRow);
            this.gridView1.ValidateRow += new DevExpress.XtraGrid.Views.Base.ValidateRowEventHandler(this.gridView1_ValidateRow);
            // 
            // col_Location
            // 
            resources.ApplyResources(this.col_Location, "col_Location");
            this.col_Location.FieldName = "Location";
            this.col_Location.Name = "col_Location";
            // 
            // col_Store
            // 
            resources.ApplyResources(this.col_Store, "col_Store");
            this.col_Store.ColumnEdit = this.rep_stores;
            this.col_Store.FieldName = "StoreId";
            this.col_Store.Name = "col_Store";
            // 
            // rep_stores
            // 
            resources.ApplyResources(this.rep_stores, "rep_stores");
            this.rep_stores.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_stores.Buttons"))))});
            this.rep_stores.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_stores.Columns"), resources.GetString("rep_stores.Columns1")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_stores.Columns2"), resources.GetString("rep_stores.Columns3")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_stores.Columns4"), resources.GetString("rep_stores.Columns5"), ((int)(resources.GetObject("rep_stores.Columns6"))), ((DevExpress.Utils.FormatType)(resources.GetObject("rep_stores.Columns7"))), resources.GetString("rep_stores.Columns8"), ((bool)(resources.GetObject("rep_stores.Columns9"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("rep_stores.Columns10"))))});
            this.rep_stores.Name = "rep_stores";
            // 
            // gridColumn7
            // 
            resources.ApplyResources(this.gridColumn7, "gridColumn7");
            this.gridColumn7.FieldName = "ItemId";
            this.gridColumn7.Name = "gridColumn7";
            // 
            // groupBox1
            // 
            resources.ApplyResources(this.groupBox1, "groupBox1");
            this.groupBox1.Controls.Add(this.grd_Vendors);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.TabStop = false;
            // 
            // grd_Vendors
            // 
            resources.ApplyResources(this.grd_Vendors, "grd_Vendors");
            this.grd_Vendors.EmbeddedNavigator.AccessibleDescription = resources.GetString("grd_Vendors.EmbeddedNavigator.AccessibleDescription");
            this.grd_Vendors.EmbeddedNavigator.AccessibleName = resources.GetString("grd_Vendors.EmbeddedNavigator.AccessibleName");
            this.grd_Vendors.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grd_Vendors.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grd_Vendors.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grd_Vendors.EmbeddedNavigator.Anchor")));
            this.grd_Vendors.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grd_Vendors.EmbeddedNavigator.BackgroundImage")));
            this.grd_Vendors.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grd_Vendors.EmbeddedNavigator.BackgroundImageLayout")));
            this.grd_Vendors.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grd_Vendors.EmbeddedNavigator.ImeMode")));
            this.grd_Vendors.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grd_Vendors.EmbeddedNavigator.MaximumSize")));
            this.grd_Vendors.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grd_Vendors.EmbeddedNavigator.TextLocation")));
            this.grd_Vendors.EmbeddedNavigator.ToolTip = resources.GetString("grd_Vendors.EmbeddedNavigator.ToolTip");
            this.grd_Vendors.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grd_Vendors.EmbeddedNavigator.ToolTipIconType")));
            this.grd_Vendors.EmbeddedNavigator.ToolTipTitle = resources.GetString("grd_Vendors.EmbeddedNavigator.ToolTipTitle");
            this.grd_Vendors.MainView = this.gv_Vendors;
            this.grd_Vendors.Name = "grd_Vendors";
            this.grd_Vendors.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.rep_Vendors});
            this.grd_Vendors.TabStop = false;
            this.grd_Vendors.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gv_Vendors});
            this.grd_Vendors.KeyDown += new System.Windows.Forms.KeyEventHandler(this.grd_Vendors_KeyDown);
            // 
            // gv_Vendors
            // 
            this.gv_Vendors.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gv_Vendors.Appearance.HeaderPanel.FontSizeDelta")));
            this.gv_Vendors.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gv_Vendors.Appearance.HeaderPanel.FontStyleDelta")));
            this.gv_Vendors.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gv_Vendors.Appearance.HeaderPanel.GradientMode")));
            this.gv_Vendors.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gv_Vendors.Appearance.HeaderPanel.Image")));
            this.gv_Vendors.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gv_Vendors.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gv_Vendors.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gv_Vendors.Appearance.Row.FontSizeDelta")));
            this.gv_Vendors.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gv_Vendors.Appearance.Row.FontStyleDelta")));
            this.gv_Vendors.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gv_Vendors.Appearance.Row.GradientMode")));
            this.gv_Vendors.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gv_Vendors.Appearance.Row.Image")));
            this.gv_Vendors.Appearance.Row.Options.UseTextOptions = true;
            this.gv_Vendors.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.gv_Vendors, "gv_Vendors");
            this.gv_Vendors.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_Vnd_PurchasePrice,
            this.col_Vnd_VendorId,
            this.col_Vnd_ItemId});
            this.gv_Vendors.GridControl = this.grd_Vendors;
            this.gv_Vendors.HorzScrollStep = 2;
            this.gv_Vendors.Name = "gv_Vendors";
            this.gv_Vendors.OptionsCustomization.AllowFilter = false;
            this.gv_Vendors.OptionsCustomization.AllowGroup = false;
            this.gv_Vendors.OptionsCustomization.AllowQuickHideColumns = false;
            this.gv_Vendors.OptionsMenu.EnableColumnMenu = false;
            this.gv_Vendors.OptionsMenu.EnableFooterMenu = false;
            this.gv_Vendors.OptionsMenu.EnableGroupPanelMenu = false;
            this.gv_Vendors.OptionsMenu.ShowDateTimeGroupIntervalItems = false;
            this.gv_Vendors.OptionsMenu.ShowGroupSortSummaryItems = false;
            this.gv_Vendors.OptionsNavigation.EnterMoveNextColumn = true;
            this.gv_Vendors.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gv_Vendors.OptionsView.AnimationType = DevExpress.XtraGrid.Views.Base.GridAnimationType.NeverAnimate;
            this.gv_Vendors.OptionsView.EnableAppearanceEvenRow = true;
            this.gv_Vendors.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Bottom;
            this.gv_Vendors.OptionsView.ShowDetailButtons = false;
            this.gv_Vendors.OptionsView.ShowGroupPanel = false;
            this.gv_Vendors.InvalidRowException += new DevExpress.XtraGrid.Views.Base.InvalidRowExceptionEventHandler(this.gv_Vendors_InvalidRowException);
            this.gv_Vendors.ValidateRow += new DevExpress.XtraGrid.Views.Base.ValidateRowEventHandler(this.gv_Vendors_ValidateRow);
            // 
            // col_Vnd_PurchasePrice
            // 
            resources.ApplyResources(this.col_Vnd_PurchasePrice, "col_Vnd_PurchasePrice");
            this.col_Vnd_PurchasePrice.FieldName = "PurchasePrice";
            this.col_Vnd_PurchasePrice.Name = "col_Vnd_PurchasePrice";
            // 
            // col_Vnd_VendorId
            // 
            resources.ApplyResources(this.col_Vnd_VendorId, "col_Vnd_VendorId");
            this.col_Vnd_VendorId.ColumnEdit = this.rep_Vendors;
            this.col_Vnd_VendorId.FieldName = "VendorId";
            this.col_Vnd_VendorId.Name = "col_Vnd_VendorId";
            // 
            // rep_Vendors
            // 
            resources.ApplyResources(this.rep_Vendors, "rep_Vendors");
            this.rep_Vendors.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Vendors.Buttons"))))});
            this.rep_Vendors.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_Vendors.Columns"), resources.GetString("rep_Vendors.Columns1"), ((int)(resources.GetObject("rep_Vendors.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("rep_Vendors.Columns3"))), resources.GetString("rep_Vendors.Columns4"), ((bool)(resources.GetObject("rep_Vendors.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("rep_Vendors.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_Vendors.Columns7"), resources.GetString("rep_Vendors.Columns8"), ((int)(resources.GetObject("rep_Vendors.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("rep_Vendors.Columns10"))), resources.GetString("rep_Vendors.Columns11"), ((bool)(resources.GetObject("rep_Vendors.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("rep_Vendors.Columns13"))))});
            this.rep_Vendors.Name = "rep_Vendors";
            // 
            // col_Vnd_ItemId
            // 
            resources.ApplyResources(this.col_Vnd_ItemId, "col_Vnd_ItemId");
            this.col_Vnd_ItemId.FieldName = "ItemId";
            this.col_Vnd_ItemId.Name = "col_Vnd_ItemId";
            // 
            // tab_image
            // 
            resources.ApplyResources(this.tab_image, "tab_image");
            this.tab_image.Controls.Add(this.btnRemovePic);
            this.tab_image.Controls.Add(this.btnAddPicture);
            this.tab_image.Controls.Add(this.itemPhoto);
            this.tab_image.Name = "tab_image";
            // 
            // btnRemovePic
            // 
            resources.ApplyResources(this.btnRemovePic, "btnRemovePic");
            this.btnRemovePic.Image = global::Pharmacy.Properties.Resources.del;
            this.btnRemovePic.Name = "btnRemovePic";
            this.btnRemovePic.TabStop = false;
            this.btnRemovePic.Click += new System.EventHandler(this.btnRemovePic_Click);
            // 
            // btnAddPicture
            // 
            resources.ApplyResources(this.btnAddPicture, "btnAddPicture");
            this.btnAddPicture.Image = global::Pharmacy.Properties.Resources.add32;
            this.btnAddPicture.Name = "btnAddPicture";
            this.btnAddPicture.TabStop = false;
            this.btnAddPicture.Click += new System.EventHandler(this.btnAddPicture_Click);
            // 
            // itemPhoto
            // 
            resources.ApplyResources(this.itemPhoto, "itemPhoto");
            this.itemPhoto.MenuManager = this.barManager1;
            this.itemPhoto.Name = "itemPhoto";
            this.itemPhoto.Properties.AccessibleDescription = resources.GetString("itemPhoto.Properties.AccessibleDescription");
            this.itemPhoto.Properties.AccessibleName = resources.GetString("itemPhoto.Properties.AccessibleName");
            this.itemPhoto.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("itemPhoto.Properties.Appearance.FontSizeDelta")));
            this.itemPhoto.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("itemPhoto.Properties.Appearance.FontStyleDelta")));
            this.itemPhoto.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("itemPhoto.Properties.Appearance.GradientMode")));
            this.itemPhoto.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("itemPhoto.Properties.Appearance.Image")));
            this.itemPhoto.Properties.Appearance.Options.UseTextOptions = true;
            this.itemPhoto.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.itemPhoto.Properties.NullText = resources.GetString("itemPhoto.Properties.NullText");
            this.itemPhoto.Properties.ShowMenu = false;
            this.itemPhoto.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Zoom;
            this.itemPhoto.Modified += new System.EventHandler(this.Editors_Modified);
            // 
            // tab_PricesPerQty
            // 
            resources.ApplyResources(this.tab_PricesPerQty, "tab_PricesPerQty");
            this.tab_PricesPerQty.Controls.Add(this.grd_SalesPerQty);
            this.tab_PricesPerQty.Name = "tab_PricesPerQty";
            this.tab_PricesPerQty.PageVisible = false;
            // 
            // grd_SalesPerQty
            // 
            resources.ApplyResources(this.grd_SalesPerQty, "grd_SalesPerQty");
            this.grd_SalesPerQty.EmbeddedNavigator.AccessibleDescription = resources.GetString("grd_SalesPerQty.EmbeddedNavigator.AccessibleDescription");
            this.grd_SalesPerQty.EmbeddedNavigator.AccessibleName = resources.GetString("grd_SalesPerQty.EmbeddedNavigator.AccessibleName");
            this.grd_SalesPerQty.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grd_SalesPerQty.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grd_SalesPerQty.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grd_SalesPerQty.EmbeddedNavigator.Anchor")));
            this.grd_SalesPerQty.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grd_SalesPerQty.EmbeddedNavigator.BackgroundImage")));
            this.grd_SalesPerQty.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grd_SalesPerQty.EmbeddedNavigator.BackgroundImageLayout")));
            this.grd_SalesPerQty.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grd_SalesPerQty.EmbeddedNavigator.ImeMode")));
            this.grd_SalesPerQty.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grd_SalesPerQty.EmbeddedNavigator.MaximumSize")));
            this.grd_SalesPerQty.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grd_SalesPerQty.EmbeddedNavigator.TextLocation")));
            this.grd_SalesPerQty.EmbeddedNavigator.ToolTip = resources.GetString("grd_SalesPerQty.EmbeddedNavigator.ToolTip");
            this.grd_SalesPerQty.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grd_SalesPerQty.EmbeddedNavigator.ToolTipIconType")));
            this.grd_SalesPerQty.EmbeddedNavigator.ToolTipTitle = resources.GetString("grd_SalesPerQty.EmbeddedNavigator.ToolTipTitle");
            this.grd_SalesPerQty.MainView = this.gv_SalesPerQty;
            this.grd_SalesPerQty.Name = "grd_SalesPerQty";
            this.grd_SalesPerQty.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.rep_SLQtyNums});
            this.grd_SalesPerQty.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gv_SalesPerQty});
            this.grd_SalesPerQty.ProcessGridKey += new System.Windows.Forms.KeyEventHandler(this.grd_SalesPerQty_ProcessGridKey);
            this.grd_SalesPerQty.KeyDown += new System.Windows.Forms.KeyEventHandler(this.grd_SalesPerQty_KeyDown);
            // 
            // gv_SalesPerQty
            // 
            this.gv_SalesPerQty.Appearance.FooterPanel.FontSizeDelta = ((int)(resources.GetObject("gv_SalesPerQty.Appearance.FooterPanel.FontSizeDelta")));
            this.gv_SalesPerQty.Appearance.FooterPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gv_SalesPerQty.Appearance.FooterPanel.FontStyleDelta")));
            this.gv_SalesPerQty.Appearance.FooterPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gv_SalesPerQty.Appearance.FooterPanel.GradientMode")));
            this.gv_SalesPerQty.Appearance.FooterPanel.Image = ((System.Drawing.Image)(resources.GetObject("gv_SalesPerQty.Appearance.FooterPanel.Image")));
            this.gv_SalesPerQty.Appearance.FooterPanel.Options.UseTextOptions = true;
            this.gv_SalesPerQty.Appearance.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.gv_SalesPerQty, "gv_SalesPerQty");
            this.gv_SalesPerQty.ColumnPanelRowHeight = 30;
            this.gv_SalesPerQty.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_SellPrice,
            this.col_QtyTo,
            this.col_QtyFrom,
            this.col_ItemId,
            this.col_ItemPriceId});
            this.gv_SalesPerQty.GridControl = this.grd_SalesPerQty;
            this.gv_SalesPerQty.Name = "gv_SalesPerQty";
            this.gv_SalesPerQty.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.True;
            this.gv_SalesPerQty.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gv_SalesPerQty.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.gv_SalesPerQty.OptionsSelection.EnableAppearanceHideSelection = false;
            this.gv_SalesPerQty.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Bottom;
            this.gv_SalesPerQty.OptionsView.RowAutoHeight = true;
            this.gv_SalesPerQty.OptionsView.ShowGroupPanel = false;
            // 
            // col_SellPrice
            // 
            this.col_SellPrice.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_SellPrice.AppearanceCell.FontSizeDelta")));
            this.col_SellPrice.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_SellPrice.AppearanceCell.FontStyleDelta")));
            this.col_SellPrice.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_SellPrice.AppearanceCell.GradientMode")));
            this.col_SellPrice.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_SellPrice.AppearanceCell.Image")));
            this.col_SellPrice.AppearanceCell.Options.UseTextOptions = true;
            this.col_SellPrice.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_SellPrice.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_SellPrice.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_SellPrice.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_SellPrice.AppearanceHeader.FontSizeDelta")));
            this.col_SellPrice.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_SellPrice.AppearanceHeader.FontStyleDelta")));
            this.col_SellPrice.AppearanceHeader.ForeColor = ((System.Drawing.Color)(resources.GetObject("col_SellPrice.AppearanceHeader.ForeColor")));
            this.col_SellPrice.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_SellPrice.AppearanceHeader.GradientMode")));
            this.col_SellPrice.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_SellPrice.AppearanceHeader.Image")));
            this.col_SellPrice.AppearanceHeader.Options.UseFont = true;
            this.col_SellPrice.AppearanceHeader.Options.UseForeColor = true;
            this.col_SellPrice.AppearanceHeader.Options.UseTextOptions = true;
            this.col_SellPrice.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_SellPrice.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_SellPrice.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_SellPrice, "col_SellPrice");
            this.col_SellPrice.ColumnEdit = this.rep_SLQtyNums;
            this.col_SellPrice.FieldName = "SellPrice";
            this.col_SellPrice.Name = "col_SellPrice";
            // 
            // rep_SLQtyNums
            // 
            resources.ApplyResources(this.rep_SLQtyNums, "rep_SLQtyNums");
            this.rep_SLQtyNums.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.rep_SLQtyNums.DisplayFormat.FormatString = "f3";
            this.rep_SLQtyNums.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.rep_SLQtyNums.EditFormat.FormatString = "f3";
            this.rep_SLQtyNums.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.rep_SLQtyNums.Increment = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.rep_SLQtyNums.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("rep_SLQtyNums.Mask.AutoComplete")));
            this.rep_SLQtyNums.Mask.BeepOnError = ((bool)(resources.GetObject("rep_SLQtyNums.Mask.BeepOnError")));
            this.rep_SLQtyNums.Mask.EditMask = resources.GetString("rep_SLQtyNums.Mask.EditMask");
            this.rep_SLQtyNums.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("rep_SLQtyNums.Mask.IgnoreMaskBlank")));
            this.rep_SLQtyNums.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("rep_SLQtyNums.Mask.MaskType")));
            this.rep_SLQtyNums.Mask.PlaceHolder = ((char)(resources.GetObject("rep_SLQtyNums.Mask.PlaceHolder")));
            this.rep_SLQtyNums.Mask.SaveLiteral = ((bool)(resources.GetObject("rep_SLQtyNums.Mask.SaveLiteral")));
            this.rep_SLQtyNums.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("rep_SLQtyNums.Mask.ShowPlaceHolders")));
            this.rep_SLQtyNums.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("rep_SLQtyNums.Mask.UseMaskAsDisplayFormat")));
            this.rep_SLQtyNums.MaxValue = new decimal(new int[] {
            10000000,
            0,
            0,
            0});
            this.rep_SLQtyNums.Name = "rep_SLQtyNums";
            this.rep_SLQtyNums.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.rep_SLQtyNums_Spin);
            // 
            // col_QtyTo
            // 
            this.col_QtyTo.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_QtyTo.AppearanceCell.FontSizeDelta")));
            this.col_QtyTo.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_QtyTo.AppearanceCell.FontStyleDelta")));
            this.col_QtyTo.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_QtyTo.AppearanceCell.GradientMode")));
            this.col_QtyTo.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_QtyTo.AppearanceCell.Image")));
            this.col_QtyTo.AppearanceCell.Options.UseTextOptions = true;
            this.col_QtyTo.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_QtyTo.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_QtyTo.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_QtyTo.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_QtyTo.AppearanceHeader.FontSizeDelta")));
            this.col_QtyTo.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_QtyTo.AppearanceHeader.FontStyleDelta")));
            this.col_QtyTo.AppearanceHeader.ForeColor = ((System.Drawing.Color)(resources.GetObject("col_QtyTo.AppearanceHeader.ForeColor")));
            this.col_QtyTo.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_QtyTo.AppearanceHeader.GradientMode")));
            this.col_QtyTo.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_QtyTo.AppearanceHeader.Image")));
            this.col_QtyTo.AppearanceHeader.Options.UseFont = true;
            this.col_QtyTo.AppearanceHeader.Options.UseForeColor = true;
            this.col_QtyTo.AppearanceHeader.Options.UseTextOptions = true;
            this.col_QtyTo.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_QtyTo.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_QtyTo.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_QtyTo, "col_QtyTo");
            this.col_QtyTo.ColumnEdit = this.rep_SLQtyNums;
            this.col_QtyTo.FieldName = "QtyTo";
            this.col_QtyTo.Name = "col_QtyTo";
            // 
            // col_QtyFrom
            // 
            this.col_QtyFrom.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_QtyFrom.AppearanceCell.FontSizeDelta")));
            this.col_QtyFrom.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_QtyFrom.AppearanceCell.FontStyleDelta")));
            this.col_QtyFrom.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_QtyFrom.AppearanceCell.GradientMode")));
            this.col_QtyFrom.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_QtyFrom.AppearanceCell.Image")));
            this.col_QtyFrom.AppearanceCell.Options.UseTextOptions = true;
            this.col_QtyFrom.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_QtyFrom.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_QtyFrom.AppearanceHeader.FontSizeDelta")));
            this.col_QtyFrom.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_QtyFrom.AppearanceHeader.FontStyleDelta")));
            this.col_QtyFrom.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_QtyFrom.AppearanceHeader.GradientMode")));
            this.col_QtyFrom.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_QtyFrom.AppearanceHeader.Image")));
            this.col_QtyFrom.AppearanceHeader.Options.UseTextOptions = true;
            this.col_QtyFrom.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.col_QtyFrom, "col_QtyFrom");
            this.col_QtyFrom.ColumnEdit = this.rep_SLQtyNums;
            this.col_QtyFrom.FieldName = "QtyFrom";
            this.col_QtyFrom.Name = "col_QtyFrom";
            // 
            // col_ItemId
            // 
            resources.ApplyResources(this.col_ItemId, "col_ItemId");
            this.col_ItemId.FieldName = "ItemId";
            this.col_ItemId.Name = "col_ItemId";
            // 
            // col_ItemPriceId
            // 
            resources.ApplyResources(this.col_ItemPriceId, "col_ItemPriceId");
            this.col_ItemPriceId.FieldName = "ItemPriceId";
            this.col_ItemPriceId.Name = "col_ItemPriceId";
            // 
            // tab_PriceLevels
            // 
            resources.ApplyResources(this.tab_PriceLevels, "tab_PriceLevels");
            this.tab_PriceLevels.Controls.Add(this.groupBox4);
            this.tab_PriceLevels.Controls.Add(this.groupBox2);
            this.tab_PriceLevels.Name = "tab_PriceLevels";
            this.tab_PriceLevels.PageVisible = false;
            // 
            // groupBox4
            // 
            resources.ApplyResources(this.groupBox4, "groupBox4");
            this.groupBox4.Controls.Add(this.grdSlPLevel);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.TabStop = false;
            // 
            // grdSlPLevel
            // 
            resources.ApplyResources(this.grdSlPLevel, "grdSlPLevel");
            this.grdSlPLevel.EmbeddedNavigator.AccessibleDescription = resources.GetString("grdSlPLevel.EmbeddedNavigator.AccessibleDescription");
            this.grdSlPLevel.EmbeddedNavigator.AccessibleName = resources.GetString("grdSlPLevel.EmbeddedNavigator.AccessibleName");
            this.grdSlPLevel.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grdSlPLevel.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grdSlPLevel.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grdSlPLevel.EmbeddedNavigator.Anchor")));
            this.grdSlPLevel.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grdSlPLevel.EmbeddedNavigator.BackgroundImage")));
            this.grdSlPLevel.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grdSlPLevel.EmbeddedNavigator.BackgroundImageLayout")));
            this.grdSlPLevel.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grdSlPLevel.EmbeddedNavigator.ImeMode")));
            this.grdSlPLevel.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grdSlPLevel.EmbeddedNavigator.MaximumSize")));
            this.grdSlPLevel.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grdSlPLevel.EmbeddedNavigator.TextLocation")));
            this.grdSlPLevel.EmbeddedNavigator.ToolTip = resources.GetString("grdSlPLevel.EmbeddedNavigator.ToolTip");
            this.grdSlPLevel.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grdSlPLevel.EmbeddedNavigator.ToolTipIconType")));
            this.grdSlPLevel.EmbeddedNavigator.ToolTipTitle = resources.GetString("grdSlPLevel.EmbeddedNavigator.ToolTipTitle");
            this.grdSlPLevel.MainView = this.gridView7;
            this.grdSlPLevel.Name = "grdSlPLevel";
            this.grdSlPLevel.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repPriceLevels});
            this.grdSlPLevel.TabStop = false;
            this.grdSlPLevel.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView7});
            this.grdSlPLevel.DoubleClick += new System.EventHandler(this.grdSlPLevel_DoubleClick);
            // 
            // gridView7
            // 
            this.gridView7.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView7.Appearance.HeaderPanel.FontSizeDelta")));
            this.gridView7.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView7.Appearance.HeaderPanel.FontStyleDelta")));
            this.gridView7.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView7.Appearance.HeaderPanel.GradientMode")));
            this.gridView7.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView7.Appearance.HeaderPanel.Image")));
            this.gridView7.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView7.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView7.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView7.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gridView7.Appearance.Row.FontSizeDelta")));
            this.gridView7.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView7.Appearance.Row.FontStyleDelta")));
            this.gridView7.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView7.Appearance.Row.GradientMode")));
            this.gridView7.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView7.Appearance.Row.Image")));
            this.gridView7.Appearance.Row.Options.UseTextOptions = true;
            this.gridView7.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.gridView7, "gridView7");
            this.gridView7.ColumnPanelRowHeight = 35;
            this.gridView7.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colPlLargeUOMPrice,
            this.colPlMediumUOMPrice,
            this.colPlSmallUOMPrice,
            this.colPLName,
            this.colPriceLevelId,
            this.gridColumn1});
            this.gridView7.GridControl = this.grdSlPLevel;
            this.gridView7.HorzScrollStep = 2;
            this.gridView7.Name = "gridView7";
            this.gridView7.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView7.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView7.OptionsCustomization.AllowColumnMoving = false;
            this.gridView7.OptionsCustomization.AllowFilter = false;
            this.gridView7.OptionsCustomization.AllowGroup = false;
            this.gridView7.OptionsCustomization.AllowQuickHideColumns = false;
            this.gridView7.OptionsMenu.EnableColumnMenu = false;
            this.gridView7.OptionsMenu.EnableFooterMenu = false;
            this.gridView7.OptionsMenu.EnableGroupPanelMenu = false;
            this.gridView7.OptionsMenu.ShowDateTimeGroupIntervalItems = false;
            this.gridView7.OptionsMenu.ShowGroupSortSummaryItems = false;
            this.gridView7.OptionsNavigation.EnterMoveNextColumn = true;
            this.gridView7.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView7.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.gridView7.OptionsView.AnimationType = DevExpress.XtraGrid.Views.Base.GridAnimationType.NeverAnimate;
            this.gridView7.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView7.OptionsView.ShowDetailButtons = false;
            this.gridView7.OptionsView.ShowGroupPanel = false;
            this.gridView7.OptionsView.ShowIndicator = false;
            this.gridView7.CellValueChanged += new DevExpress.XtraGrid.Views.Base.CellValueChangedEventHandler(this.gridView7_CellValueChanged);
            this.gridView7.ValidatingEditor += new DevExpress.XtraEditors.Controls.BaseContainerValidateEditorEventHandler(this.gridView7_ValidatingEditor);
            this.gridView7.InvalidValueException += new DevExpress.XtraEditors.Controls.InvalidValueExceptionEventHandler(this.gridView7_InvalidValueException);
            // 
            // colPlLargeUOMPrice
            // 
            resources.ApplyResources(this.colPlLargeUOMPrice, "colPlLargeUOMPrice");
            this.colPlLargeUOMPrice.ColumnEdit = this.repPriceLevels;
            this.colPlLargeUOMPrice.DisplayFormat.FormatString = "n2";
            this.colPlLargeUOMPrice.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colPlLargeUOMPrice.FieldName = "LargeUOMPrice";
            this.colPlLargeUOMPrice.Name = "colPlLargeUOMPrice";
            // 
            // repPriceLevels
            // 
            resources.ApplyResources(this.repPriceLevels, "repPriceLevels");
            this.repPriceLevels.AllowMouseWheel = false;
            this.repPriceLevels.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repPriceLevels.Buttons"))))});
            this.repPriceLevels.EditFormat.FormatString = "n2";
            this.repPriceLevels.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.repPriceLevels.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repPriceLevels.Mask.AutoComplete")));
            this.repPriceLevels.Mask.BeepOnError = ((bool)(resources.GetObject("repPriceLevels.Mask.BeepOnError")));
            this.repPriceLevels.Mask.EditMask = resources.GetString("repPriceLevels.Mask.EditMask");
            this.repPriceLevels.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repPriceLevels.Mask.IgnoreMaskBlank")));
            this.repPriceLevels.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repPriceLevels.Mask.MaskType")));
            this.repPriceLevels.Mask.PlaceHolder = ((char)(resources.GetObject("repPriceLevels.Mask.PlaceHolder")));
            this.repPriceLevels.Mask.SaveLiteral = ((bool)(resources.GetObject("repPriceLevels.Mask.SaveLiteral")));
            this.repPriceLevels.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repPriceLevels.Mask.ShowPlaceHolders")));
            this.repPriceLevels.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repPriceLevels.Mask.UseMaskAsDisplayFormat")));
            this.repPriceLevels.MaxValue = new decimal(new int[] {
            2147483647,
            0,
            0,
            0});
            this.repPriceLevels.Name = "repPriceLevels";
            // 
            // colPlMediumUOMPrice
            // 
            resources.ApplyResources(this.colPlMediumUOMPrice, "colPlMediumUOMPrice");
            this.colPlMediumUOMPrice.ColumnEdit = this.repPriceLevels;
            this.colPlMediumUOMPrice.FieldName = "MediumUOMPrice";
            this.colPlMediumUOMPrice.Name = "colPlMediumUOMPrice";
            // 
            // colPlSmallUOMPrice
            // 
            resources.ApplyResources(this.colPlSmallUOMPrice, "colPlSmallUOMPrice");
            this.colPlSmallUOMPrice.ColumnEdit = this.repPriceLevels;
            this.colPlSmallUOMPrice.FieldName = "smallUOMPrice";
            this.colPlSmallUOMPrice.Name = "colPlSmallUOMPrice";
            // 
            // colPLName
            // 
            resources.ApplyResources(this.colPLName, "colPLName");
            this.colPLName.FieldName = "PLName";
            this.colPLName.Name = "colPLName";
            this.colPLName.OptionsColumn.AllowEdit = false;
            // 
            // colPriceLevelId
            // 
            resources.ApplyResources(this.colPriceLevelId, "colPriceLevelId");
            this.colPriceLevelId.FieldName = "PriceLevelId";
            this.colPriceLevelId.Name = "colPriceLevelId";
            this.colPriceLevelId.OptionsColumn.AllowEdit = false;
            // 
            // gridColumn1
            // 
            resources.ApplyResources(this.gridColumn1, "gridColumn1");
            this.gridColumn1.FieldName = "ItemId";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.OptionsColumn.AllowEdit = false;
            // 
            // groupBox2
            // 
            resources.ApplyResources(this.groupBox2, "groupBox2");
            this.groupBox2.Controls.Add(this.grdPrPLevel);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.TabStop = false;
            // 
            // grdPrPLevel
            // 
            resources.ApplyResources(this.grdPrPLevel, "grdPrPLevel");
            this.grdPrPLevel.EmbeddedNavigator.AccessibleDescription = resources.GetString("grdPrPLevel.EmbeddedNavigator.AccessibleDescription");
            this.grdPrPLevel.EmbeddedNavigator.AccessibleName = resources.GetString("grdPrPLevel.EmbeddedNavigator.AccessibleName");
            this.grdPrPLevel.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grdPrPLevel.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grdPrPLevel.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grdPrPLevel.EmbeddedNavigator.Anchor")));
            this.grdPrPLevel.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grdPrPLevel.EmbeddedNavigator.BackgroundImage")));
            this.grdPrPLevel.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grdPrPLevel.EmbeddedNavigator.BackgroundImageLayout")));
            this.grdPrPLevel.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grdPrPLevel.EmbeddedNavigator.ImeMode")));
            this.grdPrPLevel.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grdPrPLevel.EmbeddedNavigator.MaximumSize")));
            this.grdPrPLevel.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grdPrPLevel.EmbeddedNavigator.TextLocation")));
            this.grdPrPLevel.EmbeddedNavigator.ToolTip = resources.GetString("grdPrPLevel.EmbeddedNavigator.ToolTip");
            this.grdPrPLevel.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grdPrPLevel.EmbeddedNavigator.ToolTipIconType")));
            this.grdPrPLevel.EmbeddedNavigator.ToolTipTitle = resources.GetString("grdPrPLevel.EmbeddedNavigator.ToolTipTitle");
            this.grdPrPLevel.MainView = this.gridView6;
            this.grdPrPLevel.Name = "grdPrPLevel";
            this.grdPrPLevel.TabStop = false;
            this.grdPrPLevel.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView6});
            this.grdPrPLevel.DoubleClick += new System.EventHandler(this.grdSlPRLevel_DoubleClick);
            // 
            // gridView6
            // 
            this.gridView6.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView6.Appearance.HeaderPanel.FontSizeDelta")));
            this.gridView6.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView6.Appearance.HeaderPanel.FontStyleDelta")));
            this.gridView6.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView6.Appearance.HeaderPanel.GradientMode")));
            this.gridView6.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView6.Appearance.HeaderPanel.Image")));
            this.gridView6.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView6.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView6.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView6.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gridView6.Appearance.Row.FontSizeDelta")));
            this.gridView6.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView6.Appearance.Row.FontStyleDelta")));
            this.gridView6.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView6.Appearance.Row.GradientMode")));
            this.gridView6.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView6.Appearance.Row.Image")));
            this.gridView6.Appearance.Row.Options.UseTextOptions = true;
            this.gridView6.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.gridView6, "gridView6");
            this.gridView6.ColumnPanelRowHeight = 35;
            this.gridView6.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colPrsmallUOMPrice,
            this.colPrPLName,
            this.colPrPriceLevelId,
            this.gridColumn3});
            this.gridView6.GridControl = this.grdPrPLevel;
            this.gridView6.HorzScrollStep = 2;
            this.gridView6.Name = "gridView6";
            this.gridView6.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView6.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView6.OptionsBehavior.Editable = false;
            this.gridView6.OptionsCustomization.AllowColumnMoving = false;
            this.gridView6.OptionsCustomization.AllowFilter = false;
            this.gridView6.OptionsCustomization.AllowGroup = false;
            this.gridView6.OptionsCustomization.AllowQuickHideColumns = false;
            this.gridView6.OptionsMenu.EnableColumnMenu = false;
            this.gridView6.OptionsMenu.EnableFooterMenu = false;
            this.gridView6.OptionsMenu.EnableGroupPanelMenu = false;
            this.gridView6.OptionsMenu.ShowDateTimeGroupIntervalItems = false;
            this.gridView6.OptionsMenu.ShowGroupSortSummaryItems = false;
            this.gridView6.OptionsNavigation.EnterMoveNextColumn = true;
            this.gridView6.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView6.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.gridView6.OptionsView.AnimationType = DevExpress.XtraGrid.Views.Base.GridAnimationType.NeverAnimate;
            this.gridView6.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView6.OptionsView.ShowDetailButtons = false;
            this.gridView6.OptionsView.ShowGroupPanel = false;
            this.gridView6.OptionsView.ShowIndicator = false;
            // 
            // colPrsmallUOMPrice
            // 
            resources.ApplyResources(this.colPrsmallUOMPrice, "colPrsmallUOMPrice");
            this.colPrsmallUOMPrice.FieldName = "smallUOMPrice";
            this.colPrsmallUOMPrice.Name = "colPrsmallUOMPrice";
            // 
            // colPrPLName
            // 
            resources.ApplyResources(this.colPrPLName, "colPrPLName");
            this.colPrPLName.FieldName = "PrPLName";
            this.colPrPLName.Name = "colPrPLName";
            // 
            // colPrPriceLevelId
            // 
            resources.ApplyResources(this.colPrPriceLevelId, "colPrPriceLevelId");
            this.colPrPriceLevelId.FieldName = "PrPriceLevelId";
            this.colPrPriceLevelId.Name = "colPrPriceLevelId";
            // 
            // gridColumn3
            // 
            resources.ApplyResources(this.gridColumn3, "gridColumn3");
            this.gridColumn3.FieldName = "ItemId";
            this.gridColumn3.Name = "gridColumn3";
            // 
            // tab_Matrix
            // 
            resources.ApplyResources(this.tab_Matrix, "tab_Matrix");
            this.tab_Matrix.Controls.Add(this.btn_MatrixPrint);
            this.tab_Matrix.Controls.Add(this.btn_GenMtrx);
            this.tab_Matrix.Controls.Add(this.grd_Mtrx);
            this.tab_Matrix.Name = "tab_Matrix";
            this.tab_Matrix.PageVisible = false;
            this.tab_Matrix.Enter += new System.EventHandler(this.tab_Matrix_Enter);
            // 
            // btn_MatrixPrint
            // 
            resources.ApplyResources(this.btn_MatrixPrint, "btn_MatrixPrint");
            this.btn_MatrixPrint.Name = "btn_MatrixPrint";
            this.btn_MatrixPrint.Click += new System.EventHandler(this.btn_MatrixPrint_Click);
            // 
            // btn_GenMtrx
            // 
            resources.ApplyResources(this.btn_GenMtrx, "btn_GenMtrx");
            this.btn_GenMtrx.Name = "btn_GenMtrx";
            this.btn_GenMtrx.Click += new System.EventHandler(this.btn_GenMtrx_Click);
            // 
            // grd_Mtrx
            // 
            resources.ApplyResources(this.grd_Mtrx, "grd_Mtrx");
            this.grd_Mtrx.EmbeddedNavigator.AccessibleDescription = resources.GetString("grd_Mtrx.EmbeddedNavigator.AccessibleDescription");
            this.grd_Mtrx.EmbeddedNavigator.AccessibleName = resources.GetString("grd_Mtrx.EmbeddedNavigator.AccessibleName");
            this.grd_Mtrx.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grd_Mtrx.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grd_Mtrx.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grd_Mtrx.EmbeddedNavigator.Anchor")));
            this.grd_Mtrx.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grd_Mtrx.EmbeddedNavigator.BackgroundImage")));
            this.grd_Mtrx.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grd_Mtrx.EmbeddedNavigator.BackgroundImageLayout")));
            this.grd_Mtrx.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grd_Mtrx.EmbeddedNavigator.ImeMode")));
            this.grd_Mtrx.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grd_Mtrx.EmbeddedNavigator.MaximumSize")));
            this.grd_Mtrx.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grd_Mtrx.EmbeddedNavigator.TextLocation")));
            this.grd_Mtrx.EmbeddedNavigator.ToolTip = resources.GetString("grd_Mtrx.EmbeddedNavigator.ToolTip");
            this.grd_Mtrx.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grd_Mtrx.EmbeddedNavigator.ToolTipIconType")));
            this.grd_Mtrx.EmbeddedNavigator.ToolTipTitle = resources.GetString("grd_Mtrx.EmbeddedNavigator.ToolTipTitle");
            this.grd_Mtrx.MainView = this.gv_Matrix;
            this.grd_Mtrx.Name = "grd_Mtrx";
            this.grd_Mtrx.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.rep_M_Spin,
            this.rep_m_attribute});
            this.grd_Mtrx.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gv_Matrix});
            // 
            // gv_Matrix
            // 
            this.gv_Matrix.Appearance.FooterPanel.FontSizeDelta = ((int)(resources.GetObject("gv_Matrix.Appearance.FooterPanel.FontSizeDelta")));
            this.gv_Matrix.Appearance.FooterPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gv_Matrix.Appearance.FooterPanel.FontStyleDelta")));
            this.gv_Matrix.Appearance.FooterPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gv_Matrix.Appearance.FooterPanel.GradientMode")));
            this.gv_Matrix.Appearance.FooterPanel.Image = ((System.Drawing.Image)(resources.GetObject("gv_Matrix.Appearance.FooterPanel.Image")));
            this.gv_Matrix.Appearance.FooterPanel.Options.UseTextOptions = true;
            this.gv_Matrix.Appearance.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gv_Matrix.Appearance.GroupPanel.FontSizeDelta = ((int)(resources.GetObject("gv_Matrix.Appearance.GroupPanel.FontSizeDelta")));
            this.gv_Matrix.Appearance.GroupPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gv_Matrix.Appearance.GroupPanel.FontStyleDelta")));
            this.gv_Matrix.Appearance.GroupPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gv_Matrix.Appearance.GroupPanel.GradientMode")));
            this.gv_Matrix.Appearance.GroupPanel.Image = ((System.Drawing.Image)(resources.GetObject("gv_Matrix.Appearance.GroupPanel.Image")));
            this.gv_Matrix.Appearance.GroupPanel.Options.UseTextOptions = true;
            this.gv_Matrix.Appearance.GroupPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gv_Matrix.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gv_Matrix.Appearance.HeaderPanel.FontSizeDelta")));
            this.gv_Matrix.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gv_Matrix.Appearance.HeaderPanel.FontStyleDelta")));
            this.gv_Matrix.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gv_Matrix.Appearance.HeaderPanel.GradientMode")));
            this.gv_Matrix.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gv_Matrix.Appearance.HeaderPanel.Image")));
            this.gv_Matrix.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gv_Matrix.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gv_Matrix, "gv_Matrix");
            this.gv_Matrix.ColumnPanelRowHeight = 45;
            this.gv_Matrix.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colLength,
            this.colWidth,
            this.colHeight,
            this.col_m_Attribute3,
            this.col_m_Attribute2,
            this.col_m_Attribute1,
            this.col_m_MinQty,
            this.col_m_MaxQty,
            this.col_m_ReorderLevel,
            this.col_m_LargeUOMPrice,
            this.col_m_MediumUOMPrice,
            this.col_m_SmallUOMPrice,
            this.col_m_PurchasePrice,
            this.col_m_name,
            this.col_m_ItemCode2,
            this.col_m_ItemId,
            this.col_m_Code1});
            this.gv_Matrix.GridControl = this.grd_Mtrx;
            this.gv_Matrix.Name = "gv_Matrix";
            this.gv_Matrix.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gv_Matrix.OptionsDetail.AllowZoomDetail = false;
            this.gv_Matrix.OptionsDetail.EnableMasterViewMode = false;
            this.gv_Matrix.OptionsDetail.ShowDetailTabs = false;
            this.gv_Matrix.OptionsDetail.SmartDetailExpand = false;
            this.gv_Matrix.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gv_Matrix.OptionsSelection.EnableAppearanceHideSelection = false;
            this.gv_Matrix.OptionsView.RowAutoHeight = true;
            this.gv_Matrix.OptionsView.ShowGroupedColumns = true;
            // 
            // colLength
            // 
            this.colLength.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("colLength.AppearanceCell.FontSizeDelta")));
            this.colLength.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("colLength.AppearanceCell.FontStyleDelta")));
            this.colLength.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("colLength.AppearanceCell.GradientMode")));
            this.colLength.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("colLength.AppearanceCell.Image")));
            this.colLength.AppearanceCell.Options.UseTextOptions = true;
            this.colLength.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.colLength.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("colLength.AppearanceHeader.FontSizeDelta")));
            this.colLength.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("colLength.AppearanceHeader.FontStyleDelta")));
            this.colLength.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("colLength.AppearanceHeader.GradientMode")));
            this.colLength.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("colLength.AppearanceHeader.Image")));
            this.colLength.AppearanceHeader.Options.UseTextOptions = true;
            this.colLength.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.colLength, "colLength");
            this.colLength.ColumnEdit = this.rep_M_Spin;
            this.colLength.FieldName = "Length";
            this.colLength.Name = "colLength";
            // 
            // rep_M_Spin
            // 
            resources.ApplyResources(this.rep_M_Spin, "rep_M_Spin");
            this.rep_M_Spin.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.rep_M_Spin.DisplayFormat.FormatString = "f3";
            this.rep_M_Spin.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.rep_M_Spin.EditFormat.FormatString = "f3";
            this.rep_M_Spin.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.rep_M_Spin.Increment = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.rep_M_Spin.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("rep_M_Spin.Mask.AutoComplete")));
            this.rep_M_Spin.Mask.BeepOnError = ((bool)(resources.GetObject("rep_M_Spin.Mask.BeepOnError")));
            this.rep_M_Spin.Mask.EditMask = resources.GetString("rep_M_Spin.Mask.EditMask");
            this.rep_M_Spin.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("rep_M_Spin.Mask.IgnoreMaskBlank")));
            this.rep_M_Spin.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("rep_M_Spin.Mask.MaskType")));
            this.rep_M_Spin.Mask.PlaceHolder = ((char)(resources.GetObject("rep_M_Spin.Mask.PlaceHolder")));
            this.rep_M_Spin.Mask.SaveLiteral = ((bool)(resources.GetObject("rep_M_Spin.Mask.SaveLiteral")));
            this.rep_M_Spin.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("rep_M_Spin.Mask.ShowPlaceHolders")));
            this.rep_M_Spin.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("rep_M_Spin.Mask.UseMaskAsDisplayFormat")));
            this.rep_M_Spin.MaxValue = new decimal(new int[] {
            10000000,
            0,
            0,
            0});
            this.rep_M_Spin.Name = "rep_M_Spin";
            // 
            // colWidth
            // 
            this.colWidth.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("colWidth.AppearanceCell.FontSizeDelta")));
            this.colWidth.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("colWidth.AppearanceCell.FontStyleDelta")));
            this.colWidth.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("colWidth.AppearanceCell.GradientMode")));
            this.colWidth.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("colWidth.AppearanceCell.Image")));
            this.colWidth.AppearanceCell.Options.UseTextOptions = true;
            this.colWidth.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.colWidth.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("colWidth.AppearanceHeader.FontSizeDelta")));
            this.colWidth.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("colWidth.AppearanceHeader.FontStyleDelta")));
            this.colWidth.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("colWidth.AppearanceHeader.GradientMode")));
            this.colWidth.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("colWidth.AppearanceHeader.Image")));
            this.colWidth.AppearanceHeader.Options.UseTextOptions = true;
            this.colWidth.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.colWidth, "colWidth");
            this.colWidth.ColumnEdit = this.rep_M_Spin;
            this.colWidth.FieldName = "Width";
            this.colWidth.Name = "colWidth";
            // 
            // colHeight
            // 
            this.colHeight.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("colHeight.AppearanceCell.FontSizeDelta")));
            this.colHeight.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("colHeight.AppearanceCell.FontStyleDelta")));
            this.colHeight.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("colHeight.AppearanceCell.GradientMode")));
            this.colHeight.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("colHeight.AppearanceCell.Image")));
            this.colHeight.AppearanceCell.Options.UseTextOptions = true;
            this.colHeight.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.colHeight.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("colHeight.AppearanceHeader.FontSizeDelta")));
            this.colHeight.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("colHeight.AppearanceHeader.FontStyleDelta")));
            this.colHeight.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("colHeight.AppearanceHeader.GradientMode")));
            this.colHeight.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("colHeight.AppearanceHeader.Image")));
            this.colHeight.AppearanceHeader.Options.UseTextOptions = true;
            this.colHeight.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.colHeight, "colHeight");
            this.colHeight.ColumnEdit = this.rep_M_Spin;
            this.colHeight.FieldName = "Height";
            this.colHeight.Name = "colHeight";
            // 
            // col_m_Attribute3
            // 
            this.col_m_Attribute3.AppearanceCell.BackColor = ((System.Drawing.Color)(resources.GetObject("col_m_Attribute3.AppearanceCell.BackColor")));
            this.col_m_Attribute3.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_m_Attribute3.AppearanceCell.FontSizeDelta")));
            this.col_m_Attribute3.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_m_Attribute3.AppearanceCell.FontStyleDelta")));
            this.col_m_Attribute3.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_m_Attribute3.AppearanceCell.GradientMode")));
            this.col_m_Attribute3.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_m_Attribute3.AppearanceCell.Image")));
            this.col_m_Attribute3.AppearanceCell.Options.UseBackColor = true;
            this.col_m_Attribute3.AppearanceCell.Options.UseTextOptions = true;
            this.col_m_Attribute3.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_m_Attribute3.AppearanceHeader.BackColor = ((System.Drawing.Color)(resources.GetObject("col_m_Attribute3.AppearanceHeader.BackColor")));
            this.col_m_Attribute3.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_m_Attribute3.AppearanceHeader.FontSizeDelta")));
            this.col_m_Attribute3.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_m_Attribute3.AppearanceHeader.FontStyleDelta")));
            this.col_m_Attribute3.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_m_Attribute3.AppearanceHeader.GradientMode")));
            this.col_m_Attribute3.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_m_Attribute3.AppearanceHeader.Image")));
            this.col_m_Attribute3.AppearanceHeader.Options.UseBackColor = true;
            this.col_m_Attribute3.AppearanceHeader.Options.UseTextOptions = true;
            this.col_m_Attribute3.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.col_m_Attribute3, "col_m_Attribute3");
            this.col_m_Attribute3.ColumnEdit = this.rep_m_attribute;
            this.col_m_Attribute3.FieldName = "mtrxAttribute3";
            this.col_m_Attribute3.MinWidth = 10;
            this.col_m_Attribute3.Name = "col_m_Attribute3";
            this.col_m_Attribute3.OptionsColumn.AllowEdit = false;
            this.col_m_Attribute3.OptionsColumn.AllowFocus = false;
            // 
            // rep_m_attribute
            // 
            resources.ApplyResources(this.rep_m_attribute, "rep_m_attribute");
            this.rep_m_attribute.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_m_attribute.Buttons"))))});
            this.rep_m_attribute.Name = "rep_m_attribute";
            // 
            // col_m_Attribute2
            // 
            this.col_m_Attribute2.AppearanceCell.BackColor = ((System.Drawing.Color)(resources.GetObject("col_m_Attribute2.AppearanceCell.BackColor")));
            this.col_m_Attribute2.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_m_Attribute2.AppearanceCell.FontSizeDelta")));
            this.col_m_Attribute2.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_m_Attribute2.AppearanceCell.FontStyleDelta")));
            this.col_m_Attribute2.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_m_Attribute2.AppearanceCell.GradientMode")));
            this.col_m_Attribute2.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_m_Attribute2.AppearanceCell.Image")));
            this.col_m_Attribute2.AppearanceCell.Options.UseBackColor = true;
            this.col_m_Attribute2.AppearanceCell.Options.UseTextOptions = true;
            this.col_m_Attribute2.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_m_Attribute2.AppearanceHeader.BackColor = ((System.Drawing.Color)(resources.GetObject("col_m_Attribute2.AppearanceHeader.BackColor")));
            this.col_m_Attribute2.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_m_Attribute2.AppearanceHeader.FontSizeDelta")));
            this.col_m_Attribute2.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_m_Attribute2.AppearanceHeader.FontStyleDelta")));
            this.col_m_Attribute2.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_m_Attribute2.AppearanceHeader.GradientMode")));
            this.col_m_Attribute2.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_m_Attribute2.AppearanceHeader.Image")));
            this.col_m_Attribute2.AppearanceHeader.Options.UseBackColor = true;
            this.col_m_Attribute2.AppearanceHeader.Options.UseTextOptions = true;
            this.col_m_Attribute2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.col_m_Attribute2, "col_m_Attribute2");
            this.col_m_Attribute2.ColumnEdit = this.rep_m_attribute;
            this.col_m_Attribute2.FieldName = "mtrxAttribute2";
            this.col_m_Attribute2.MinWidth = 10;
            this.col_m_Attribute2.Name = "col_m_Attribute2";
            this.col_m_Attribute2.OptionsColumn.AllowEdit = false;
            this.col_m_Attribute2.OptionsColumn.AllowFocus = false;
            // 
            // col_m_Attribute1
            // 
            this.col_m_Attribute1.AppearanceCell.BackColor = ((System.Drawing.Color)(resources.GetObject("col_m_Attribute1.AppearanceCell.BackColor")));
            this.col_m_Attribute1.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_m_Attribute1.AppearanceCell.FontSizeDelta")));
            this.col_m_Attribute1.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_m_Attribute1.AppearanceCell.FontStyleDelta")));
            this.col_m_Attribute1.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_m_Attribute1.AppearanceCell.GradientMode")));
            this.col_m_Attribute1.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_m_Attribute1.AppearanceCell.Image")));
            this.col_m_Attribute1.AppearanceCell.Options.UseBackColor = true;
            this.col_m_Attribute1.AppearanceCell.Options.UseTextOptions = true;
            this.col_m_Attribute1.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_m_Attribute1.AppearanceHeader.BackColor = ((System.Drawing.Color)(resources.GetObject("col_m_Attribute1.AppearanceHeader.BackColor")));
            this.col_m_Attribute1.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_m_Attribute1.AppearanceHeader.FontSizeDelta")));
            this.col_m_Attribute1.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_m_Attribute1.AppearanceHeader.FontStyleDelta")));
            this.col_m_Attribute1.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_m_Attribute1.AppearanceHeader.GradientMode")));
            this.col_m_Attribute1.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_m_Attribute1.AppearanceHeader.Image")));
            this.col_m_Attribute1.AppearanceHeader.Options.UseBackColor = true;
            this.col_m_Attribute1.AppearanceHeader.Options.UseTextOptions = true;
            this.col_m_Attribute1.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.col_m_Attribute1, "col_m_Attribute1");
            this.col_m_Attribute1.ColumnEdit = this.rep_m_attribute;
            this.col_m_Attribute1.FieldName = "mtrxAttribute1";
            this.col_m_Attribute1.MinWidth = 10;
            this.col_m_Attribute1.Name = "col_m_Attribute1";
            this.col_m_Attribute1.OptionsColumn.AllowEdit = false;
            this.col_m_Attribute1.OptionsColumn.AllowFocus = false;
            // 
            // col_m_MinQty
            // 
            this.col_m_MinQty.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_m_MinQty.AppearanceCell.FontSizeDelta")));
            this.col_m_MinQty.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_m_MinQty.AppearanceCell.FontStyleDelta")));
            this.col_m_MinQty.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_m_MinQty.AppearanceCell.GradientMode")));
            this.col_m_MinQty.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_m_MinQty.AppearanceCell.Image")));
            this.col_m_MinQty.AppearanceCell.Options.UseTextOptions = true;
            this.col_m_MinQty.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_m_MinQty.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_m_MinQty.AppearanceHeader.FontSizeDelta")));
            this.col_m_MinQty.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_m_MinQty.AppearanceHeader.FontStyleDelta")));
            this.col_m_MinQty.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_m_MinQty.AppearanceHeader.GradientMode")));
            this.col_m_MinQty.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_m_MinQty.AppearanceHeader.Image")));
            this.col_m_MinQty.AppearanceHeader.Options.UseTextOptions = true;
            this.col_m_MinQty.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_m_MinQty.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_m_MinQty, "col_m_MinQty");
            this.col_m_MinQty.ColumnEdit = this.rep_M_Spin;
            this.col_m_MinQty.FieldName = "MinQty";
            this.col_m_MinQty.MinWidth = 10;
            this.col_m_MinQty.Name = "col_m_MinQty";
            this.col_m_MinQty.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            // 
            // col_m_MaxQty
            // 
            this.col_m_MaxQty.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_m_MaxQty.AppearanceCell.FontSizeDelta")));
            this.col_m_MaxQty.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_m_MaxQty.AppearanceCell.FontStyleDelta")));
            this.col_m_MaxQty.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_m_MaxQty.AppearanceCell.GradientMode")));
            this.col_m_MaxQty.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_m_MaxQty.AppearanceCell.Image")));
            this.col_m_MaxQty.AppearanceCell.Options.UseTextOptions = true;
            this.col_m_MaxQty.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_m_MaxQty.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_m_MaxQty.AppearanceHeader.FontSizeDelta")));
            this.col_m_MaxQty.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_m_MaxQty.AppearanceHeader.FontStyleDelta")));
            this.col_m_MaxQty.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_m_MaxQty.AppearanceHeader.GradientMode")));
            this.col_m_MaxQty.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_m_MaxQty.AppearanceHeader.Image")));
            this.col_m_MaxQty.AppearanceHeader.Options.UseTextOptions = true;
            this.col_m_MaxQty.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_m_MaxQty.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_m_MaxQty, "col_m_MaxQty");
            this.col_m_MaxQty.ColumnEdit = this.rep_M_Spin;
            this.col_m_MaxQty.FieldName = "MaxQty";
            this.col_m_MaxQty.MinWidth = 10;
            this.col_m_MaxQty.Name = "col_m_MaxQty";
            this.col_m_MaxQty.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            // 
            // col_m_ReorderLevel
            // 
            this.col_m_ReorderLevel.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_m_ReorderLevel.AppearanceCell.FontSizeDelta")));
            this.col_m_ReorderLevel.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_m_ReorderLevel.AppearanceCell.FontStyleDelta")));
            this.col_m_ReorderLevel.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_m_ReorderLevel.AppearanceCell.GradientMode")));
            this.col_m_ReorderLevel.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_m_ReorderLevel.AppearanceCell.Image")));
            this.col_m_ReorderLevel.AppearanceCell.Options.UseTextOptions = true;
            this.col_m_ReorderLevel.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_m_ReorderLevel.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_m_ReorderLevel.AppearanceHeader.FontSizeDelta")));
            this.col_m_ReorderLevel.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_m_ReorderLevel.AppearanceHeader.FontStyleDelta")));
            this.col_m_ReorderLevel.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_m_ReorderLevel.AppearanceHeader.GradientMode")));
            this.col_m_ReorderLevel.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_m_ReorderLevel.AppearanceHeader.Image")));
            this.col_m_ReorderLevel.AppearanceHeader.Options.UseTextOptions = true;
            this.col_m_ReorderLevel.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_m_ReorderLevel.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_m_ReorderLevel, "col_m_ReorderLevel");
            this.col_m_ReorderLevel.ColumnEdit = this.rep_M_Spin;
            this.col_m_ReorderLevel.FieldName = "ReorderLevel";
            this.col_m_ReorderLevel.MinWidth = 10;
            this.col_m_ReorderLevel.Name = "col_m_ReorderLevel";
            this.col_m_ReorderLevel.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            // 
            // col_m_LargeUOMPrice
            // 
            this.col_m_LargeUOMPrice.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_m_LargeUOMPrice.AppearanceCell.FontSizeDelta")));
            this.col_m_LargeUOMPrice.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_m_LargeUOMPrice.AppearanceCell.FontStyleDelta")));
            this.col_m_LargeUOMPrice.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_m_LargeUOMPrice.AppearanceCell.GradientMode")));
            this.col_m_LargeUOMPrice.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_m_LargeUOMPrice.AppearanceCell.Image")));
            this.col_m_LargeUOMPrice.AppearanceCell.Options.UseTextOptions = true;
            this.col_m_LargeUOMPrice.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_m_LargeUOMPrice.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_m_LargeUOMPrice.AppearanceHeader.FontSizeDelta")));
            this.col_m_LargeUOMPrice.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_m_LargeUOMPrice.AppearanceHeader.FontStyleDelta")));
            this.col_m_LargeUOMPrice.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_m_LargeUOMPrice.AppearanceHeader.GradientMode")));
            this.col_m_LargeUOMPrice.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_m_LargeUOMPrice.AppearanceHeader.Image")));
            this.col_m_LargeUOMPrice.AppearanceHeader.Options.UseTextOptions = true;
            this.col_m_LargeUOMPrice.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_m_LargeUOMPrice.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_m_LargeUOMPrice, "col_m_LargeUOMPrice");
            this.col_m_LargeUOMPrice.ColumnEdit = this.rep_M_Spin;
            this.col_m_LargeUOMPrice.FieldName = "LargeUOMPrice";
            this.col_m_LargeUOMPrice.MinWidth = 65;
            this.col_m_LargeUOMPrice.Name = "col_m_LargeUOMPrice";
            this.col_m_LargeUOMPrice.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            // 
            // col_m_MediumUOMPrice
            // 
            this.col_m_MediumUOMPrice.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_m_MediumUOMPrice.AppearanceCell.FontSizeDelta")));
            this.col_m_MediumUOMPrice.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_m_MediumUOMPrice.AppearanceCell.FontStyleDelta")));
            this.col_m_MediumUOMPrice.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_m_MediumUOMPrice.AppearanceCell.GradientMode")));
            this.col_m_MediumUOMPrice.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_m_MediumUOMPrice.AppearanceCell.Image")));
            this.col_m_MediumUOMPrice.AppearanceCell.Options.UseTextOptions = true;
            this.col_m_MediumUOMPrice.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_m_MediumUOMPrice.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_m_MediumUOMPrice.AppearanceHeader.FontSizeDelta")));
            this.col_m_MediumUOMPrice.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_m_MediumUOMPrice.AppearanceHeader.FontStyleDelta")));
            this.col_m_MediumUOMPrice.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_m_MediumUOMPrice.AppearanceHeader.GradientMode")));
            this.col_m_MediumUOMPrice.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_m_MediumUOMPrice.AppearanceHeader.Image")));
            this.col_m_MediumUOMPrice.AppearanceHeader.Options.UseTextOptions = true;
            this.col_m_MediumUOMPrice.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_m_MediumUOMPrice.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_m_MediumUOMPrice, "col_m_MediumUOMPrice");
            this.col_m_MediumUOMPrice.ColumnEdit = this.rep_M_Spin;
            this.col_m_MediumUOMPrice.FieldName = "MediumUOMPrice";
            this.col_m_MediumUOMPrice.MinWidth = 68;
            this.col_m_MediumUOMPrice.Name = "col_m_MediumUOMPrice";
            this.col_m_MediumUOMPrice.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            // 
            // col_m_SmallUOMPrice
            // 
            this.col_m_SmallUOMPrice.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_m_SmallUOMPrice.AppearanceCell.FontSizeDelta")));
            this.col_m_SmallUOMPrice.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_m_SmallUOMPrice.AppearanceCell.FontStyleDelta")));
            this.col_m_SmallUOMPrice.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_m_SmallUOMPrice.AppearanceCell.GradientMode")));
            this.col_m_SmallUOMPrice.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_m_SmallUOMPrice.AppearanceCell.Image")));
            this.col_m_SmallUOMPrice.AppearanceCell.Options.UseTextOptions = true;
            this.col_m_SmallUOMPrice.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_m_SmallUOMPrice.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_m_SmallUOMPrice.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_m_SmallUOMPrice.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_m_SmallUOMPrice.AppearanceHeader.FontSizeDelta")));
            this.col_m_SmallUOMPrice.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_m_SmallUOMPrice.AppearanceHeader.FontStyleDelta")));
            this.col_m_SmallUOMPrice.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_m_SmallUOMPrice.AppearanceHeader.GradientMode")));
            this.col_m_SmallUOMPrice.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_m_SmallUOMPrice.AppearanceHeader.Image")));
            this.col_m_SmallUOMPrice.AppearanceHeader.Options.UseFont = true;
            this.col_m_SmallUOMPrice.AppearanceHeader.Options.UseTextOptions = true;
            this.col_m_SmallUOMPrice.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_m_SmallUOMPrice.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_m_SmallUOMPrice.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_m_SmallUOMPrice, "col_m_SmallUOMPrice");
            this.col_m_SmallUOMPrice.ColumnEdit = this.rep_M_Spin;
            this.col_m_SmallUOMPrice.FieldName = "SmallUOMPrice";
            this.col_m_SmallUOMPrice.MinWidth = 68;
            this.col_m_SmallUOMPrice.Name = "col_m_SmallUOMPrice";
            this.col_m_SmallUOMPrice.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            // 
            // col_m_PurchasePrice
            // 
            this.col_m_PurchasePrice.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_m_PurchasePrice.AppearanceCell.FontSizeDelta")));
            this.col_m_PurchasePrice.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_m_PurchasePrice.AppearanceCell.FontStyleDelta")));
            this.col_m_PurchasePrice.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_m_PurchasePrice.AppearanceCell.GradientMode")));
            this.col_m_PurchasePrice.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_m_PurchasePrice.AppearanceCell.Image")));
            this.col_m_PurchasePrice.AppearanceCell.Options.UseTextOptions = true;
            this.col_m_PurchasePrice.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_m_PurchasePrice.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_m_PurchasePrice.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_m_PurchasePrice.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_m_PurchasePrice.AppearanceHeader.FontSizeDelta")));
            this.col_m_PurchasePrice.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_m_PurchasePrice.AppearanceHeader.FontStyleDelta")));
            this.col_m_PurchasePrice.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_m_PurchasePrice.AppearanceHeader.GradientMode")));
            this.col_m_PurchasePrice.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_m_PurchasePrice.AppearanceHeader.Image")));
            this.col_m_PurchasePrice.AppearanceHeader.Options.UseFont = true;
            this.col_m_PurchasePrice.AppearanceHeader.Options.UseTextOptions = true;
            this.col_m_PurchasePrice.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_m_PurchasePrice.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_m_PurchasePrice.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_m_PurchasePrice, "col_m_PurchasePrice");
            this.col_m_PurchasePrice.ColumnEdit = this.rep_M_Spin;
            this.col_m_PurchasePrice.FieldName = "PurchasePrice";
            this.col_m_PurchasePrice.MinWidth = 10;
            this.col_m_PurchasePrice.Name = "col_m_PurchasePrice";
            this.col_m_PurchasePrice.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            // 
            // col_m_name
            // 
            this.col_m_name.AppearanceCell.BackColor = ((System.Drawing.Color)(resources.GetObject("col_m_name.AppearanceCell.BackColor")));
            this.col_m_name.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_m_name.AppearanceCell.FontSizeDelta")));
            this.col_m_name.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_m_name.AppearanceCell.FontStyleDelta")));
            this.col_m_name.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_m_name.AppearanceCell.GradientMode")));
            this.col_m_name.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_m_name.AppearanceCell.Image")));
            this.col_m_name.AppearanceCell.Options.UseBackColor = true;
            this.col_m_name.AppearanceCell.Options.UseTextOptions = true;
            this.col_m_name.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_m_name.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_m_name.AppearanceHeader.FontSizeDelta")));
            this.col_m_name.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_m_name.AppearanceHeader.FontStyleDelta")));
            this.col_m_name.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_m_name.AppearanceHeader.GradientMode")));
            this.col_m_name.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_m_name.AppearanceHeader.Image")));
            this.col_m_name.AppearanceHeader.Options.UseTextOptions = true;
            this.col_m_name.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.col_m_name, "col_m_name");
            this.col_m_name.FieldName = "ItemNameAr";
            this.col_m_name.Name = "col_m_name";
            this.col_m_name.OptionsColumn.AllowEdit = false;
            this.col_m_name.OptionsColumn.AllowFocus = false;
            // 
            // col_m_ItemCode2
            // 
            this.col_m_ItemCode2.AppearanceCell.BackColor = ((System.Drawing.Color)(resources.GetObject("col_m_ItemCode2.AppearanceCell.BackColor")));
            this.col_m_ItemCode2.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_m_ItemCode2.AppearanceCell.FontSizeDelta")));
            this.col_m_ItemCode2.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_m_ItemCode2.AppearanceCell.FontStyleDelta")));
            this.col_m_ItemCode2.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_m_ItemCode2.AppearanceCell.GradientMode")));
            this.col_m_ItemCode2.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_m_ItemCode2.AppearanceCell.Image")));
            this.col_m_ItemCode2.AppearanceCell.Options.UseBackColor = true;
            this.col_m_ItemCode2.AppearanceCell.Options.UseTextOptions = true;
            this.col_m_ItemCode2.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_m_ItemCode2.AppearanceHeader.BackColor = ((System.Drawing.Color)(resources.GetObject("col_m_ItemCode2.AppearanceHeader.BackColor")));
            this.col_m_ItemCode2.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_m_ItemCode2.AppearanceHeader.FontSizeDelta")));
            this.col_m_ItemCode2.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_m_ItemCode2.AppearanceHeader.FontStyleDelta")));
            this.col_m_ItemCode2.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_m_ItemCode2.AppearanceHeader.GradientMode")));
            this.col_m_ItemCode2.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_m_ItemCode2.AppearanceHeader.Image")));
            this.col_m_ItemCode2.AppearanceHeader.Options.UseBackColor = true;
            this.col_m_ItemCode2.AppearanceHeader.Options.UseTextOptions = true;
            this.col_m_ItemCode2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.col_m_ItemCode2, "col_m_ItemCode2");
            this.col_m_ItemCode2.FieldName = "ItemCode2";
            this.col_m_ItemCode2.MinWidth = 10;
            this.col_m_ItemCode2.Name = "col_m_ItemCode2";
            this.col_m_ItemCode2.OptionsColumn.AllowEdit = false;
            this.col_m_ItemCode2.OptionsColumn.AllowFocus = false;
            // 
            // col_m_ItemId
            // 
            resources.ApplyResources(this.col_m_ItemId, "col_m_ItemId");
            this.col_m_ItemId.FieldName = "ItemId";
            this.col_m_ItemId.Name = "col_m_ItemId";
            this.col_m_ItemId.OptionsColumn.AllowShowHide = false;
            // 
            // col_m_Code1
            // 
            resources.ApplyResources(this.col_m_Code1, "col_m_Code1");
            this.col_m_Code1.FieldName = "ItemCode1";
            this.col_m_Code1.Name = "col_m_Code1";
            // 
            // frm_IC_Item
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.tab_Control1);
            this.Controls.Add(this.chk_IsDeleted);
            this.Controls.Add(this.btnPrev);
            this.Controls.Add(this.btnNext);
            this.Controls.Add(this.txtItemCode2);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.txtItemNameAr);
            this.Controls.Add(this.txtItemCode1);
            this.Controls.Add(this.labelControl15);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.KeyPreview = true;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "frm_IC_Item";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_IC_Item_FormClosing);
            this.Load += new System.EventHandler(this.frm_IC_Item_Load);
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.frm_IC_Item_KeyUp);
            ((System.ComponentModel.ISupportInitialize)(this.txtMediumUOMFactor.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtItemCode1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtItemCode2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtItemNameAr.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpCategory.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpComp.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lstInternationalCodes)).EndInit();
            this.groupBox3.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grdQty)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMinQty.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMaxQty.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtReorder.Properties)).EndInit();
            this.grp_UOM.ResumeLayout(false);
            this.grp_UOM.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chk_LargeIsStopped.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_MediumIsStopped.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_SmallIsStopped.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmb_WeightUnit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_LargeUOMCode.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_MediumUOMCode.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rdoPrchsUOM2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rdoPrchsUOM1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rdoPrchsUOM0.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rdoSellUOM2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rdoSellUOM1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rdoSellUOM0.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit5.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtLargeUOMFactor.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpSmallUOM.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpLargeUOM.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpMediumUOM.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSmallUOMPrice.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMediumUOMPrice.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtLargeUOMPrice.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtItemNameEn.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtInternationalCode.Properties)).EndInit();
            this.grp_international.ResumeLayout(false);
            this.grp_ItemType.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.chk_VariableWeight.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_PricingWithSmall.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_IsLibra.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_IsPos.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkIsExpire.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rdoItemType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_IsDeleted.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tab_Control1)).EndInit();
            this.tab_Control1.ResumeLayout(false);
            this.tab_MainInfo.ResumeLayout(false);
            this.tab_MainInfo.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtAudiancePrice.Properties)).EndInit();
            this.grpExtra.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.tabExtraData)).EndInit();
            this.tabExtraData.ResumeLayout(false);
            this.tab_InventoryLevels.ResumeLayout(false);
            this.tab_InventoryLevels.PerformLayout();
            this.tabWarranty.ResumeLayout(false);
            this.tabWarranty.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txt_WarrantyMonths.Properties)).EndInit();
            this.tabPriceChange.ResumeLayout(false);
            this.tabPriceChange.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.cmbChangeSellPrice.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbChangePriceMethod.Properties)).EndInit();
            this.tabDimension.ResumeLayout(false);
            this.tabDimension.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtLength.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtWidth.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtHeight.Properties)).EndInit();
            this.tabDiscount.ResumeLayout(false);
            this.tabDiscount.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtSalesDiscRatio.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPurchaseDiscRatio.Properties)).EndInit();
            this.tabTax.ResumeLayout(false);
            this.tabTax.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtCustomSalesTaxRatio.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCustomPurchasesTaxRatio.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_calcTaxBeforeDisc.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSalesTaxRatio.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSalesTaxValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPurchaseTaxValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPurchaseTaxRatio.Properties)).EndInit();
            this.tab_SubTaxes.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grd_SubTaxes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv_SubTaxes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_SubTaxes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPurchasePrice.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDesc.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDescEn.Properties)).EndInit();
            this.tab_Other.ResumeLayout(false);
            this.groupBox5.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridLocation)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_stores)).EndInit();
            this.groupBox1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grd_Vendors)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv_Vendors)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Vendors)).EndInit();
            this.tab_image.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.itemPhoto.Properties)).EndInit();
            this.tab_PricesPerQty.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grd_SalesPerQty)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv_SalesPerQty)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_SLQtyNums)).EndInit();
            this.tab_PriceLevels.ResumeLayout(false);
            this.groupBox4.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grdSlPLevel)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repPriceLevels)).EndInit();
            this.groupBox2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grdPrPLevel)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).EndInit();
            this.tab_Matrix.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grd_Mtrx)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv_Matrix)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_M_Spin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_m_attribute)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControl15;
        private DevExpress.XtraEditors.TextEdit txtItemCode1;
        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnSave;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtnHelp;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.TextEdit txtItemCode2;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.TextEdit txtItemNameAr;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.TextEdit txtItemNameEn;
        private DevExpress.XtraEditors.SimpleButton btnPrev;
        private DevExpress.XtraEditors.SimpleButton btnNext;
        private DevExpress.XtraEditors.SimpleButton btn_AddNewInter_Code;
        private DevExpress.XtraEditors.SimpleButton btnAddComp;
        private DevExpress.XtraEditors.LookUpEdit lkpComp;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.ImageListBoxControl lstInternationalCodes;
        private DevExpress.XtraEditors.SimpleButton btnAddCat;
        private DevExpress.XtraEditors.LookUpEdit lkpCategory;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private DevExpress.XtraEditors.LabelControl labelControl18;
        private System.Windows.Forms.GroupBox grp_UOM;
        private DevExpress.XtraEditors.SimpleButton btnAddUOM;
        private DevExpress.XtraEditors.LookUpEdit lkpLargeUOM;
        private DevExpress.XtraEditors.LabelControl labelControl19;
        private DevExpress.XtraEditors.LabelControl labelControl20;
        private DevExpress.XtraEditors.TextEdit txtMaxQty;
        private DevExpress.XtraEditors.TextEdit txtReorder;
        private DevExpress.XtraEditors.LabelControl labelControl27;
        private DevExpress.XtraEditors.LabelControl labelControl28;
        private DevExpress.XtraEditors.TextEdit txtLargeUOMFactor;
        private DevExpress.XtraEditors.LookUpEdit lkpSmallUOM;
        private DevExpress.XtraEditors.LabelControl labelControl23;
        private DevExpress.XtraEditors.TextEdit txtMediumUOMFactor;
        private DevExpress.XtraEditors.LabelControl labelControl21;
        private DevExpress.XtraEditors.LookUpEdit lkpMediumUOM;
        private DevExpress.XtraBars.BarButtonItem barBtnDelete;
        private DevExpress.XtraBars.BarButtonItem barBtnNew;
        private System.Windows.Forms.GroupBox groupBox3;
        private DevExpress.XtraGrid.GridControl grdQty;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraEditors.TextEdit txtInternationalCode;
        //private Pharmacy.DAL.PharmacyDataSetTableAdapters.IC_EffectiveMaterialTableAdapter iC_EffectiveMaterialTableAdapter;
        private DevExpress.XtraEditors.SimpleButton btnDelete;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraEditors.LabelControl labelControl36;
        private DevExpress.XtraBars.BarButtonItem barBtnList;
        private DevExpress.XtraEditors.TextEdit txtMinQty;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private System.Windows.Forms.GroupBox grp_international;
        private DevExpress.XtraEditors.TextEdit textEdit5;
        private DevExpress.XtraEditors.LabelControl labelControl13;
        private System.Windows.Forms.GroupBox grp_ItemType;
        
        private DevExpress.XtraEditors.CheckEdit chkIsExpire;
        private DevExpress.XtraEditors.CheckEdit chk_IsDeleted;
        private DevExpress.XtraTab.XtraTabControl tab_Control1;
        private DevExpress.XtraTab.XtraTabPage tab_MainInfo;
        private DevExpress.XtraTab.XtraTabPage tab_image;
        private DevExpress.XtraTab.XtraTabPage tab_Other;
        private DevExpress.XtraEditors.SimpleButton btnRemovePic;
        private DevExpress.XtraEditors.SimpleButton btnAddPicture;
        private DevExpress.XtraEditors.PictureEdit itemPhoto;
        private DevExpress.XtraTab.XtraTabPage tab_PricesPerQty;
        private DevExpress.XtraGrid.GridControl grd_SalesPerQty;
        private DevExpress.XtraGrid.Views.Grid.GridView gv_SalesPerQty;
        private DevExpress.XtraGrid.Columns.GridColumn col_QtyTo;
        private DevExpress.XtraGrid.Columns.GridColumn col_QtyFrom;
        private DevExpress.XtraGrid.Columns.GridColumn col_SellPrice;
        private DevExpress.XtraGrid.Columns.GridColumn col_ItemId;
        private DevExpress.XtraGrid.Columns.GridColumn col_ItemPriceId;
        private DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit rep_SLQtyNums;
        private DevExpress.XtraEditors.RadioGroup rdoItemType;
        private DevExpress.XtraTab.XtraTabPage tab_Matrix;
        private DevExpress.XtraGrid.GridControl grd_Mtrx;
        private DevExpress.XtraGrid.Views.Grid.GridView gv_Matrix;
        private DevExpress.XtraGrid.Columns.GridColumn col_m_MediumUOMPrice;
        private DevExpress.XtraGrid.Columns.GridColumn col_m_SmallUOMPrice;
        private DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit rep_M_Spin;
        private DevExpress.XtraGrid.Columns.GridColumn col_m_PurchasePrice;
        private DevExpress.XtraGrid.Columns.GridColumn col_m_ItemCode2;
        private DevExpress.XtraGrid.Columns.GridColumn col_m_ItemId;
        private DevExpress.XtraGrid.Columns.GridColumn col_m_MinQty;
        private DevExpress.XtraGrid.Columns.GridColumn col_m_MaxQty;
        private DevExpress.XtraGrid.Columns.GridColumn col_m_ReorderLevel;
        private DevExpress.XtraGrid.Columns.GridColumn col_m_LargeUOMPrice;
        private DevExpress.XtraGrid.Columns.GridColumn col_m_Attribute3;
        private DevExpress.XtraGrid.Columns.GridColumn col_m_Attribute2;
        private DevExpress.XtraGrid.Columns.GridColumn col_m_Attribute1;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit rep_m_attribute;
        private DevExpress.XtraEditors.SimpleButton btn_GenMtrx;
        private System.Windows.Forms.GroupBox groupBox1;
        private DevExpress.XtraGrid.GridControl grd_Vendors;
        private DevExpress.XtraGrid.Views.Grid.GridView gv_Vendors;
        private DevExpress.XtraGrid.Columns.GridColumn col_Vnd_PurchasePrice;
        private DevExpress.XtraGrid.Columns.GridColumn col_Vnd_VendorId;
        private DevExpress.XtraGrid.Columns.GridColumn col_Vnd_ItemId;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit rep_Vendors;
        private DevExpress.XtraTab.XtraTabPage tab_PriceLevels;
        private System.Windows.Forms.GroupBox groupBox4;
        private DevExpress.XtraGrid.GridControl grdSlPLevel;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView7;
        private DevExpress.XtraGrid.Columns.GridColumn colPlLargeUOMPrice;
        private DevExpress.XtraGrid.Columns.GridColumn colPlMediumUOMPrice;
        private DevExpress.XtraGrid.Columns.GridColumn colPlSmallUOMPrice;
        private DevExpress.XtraGrid.Columns.GridColumn colPLName;
        private System.Windows.Forms.GroupBox groupBox2;
        private DevExpress.XtraGrid.GridControl grdPrPLevel;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView6;
        private DevExpress.XtraGrid.Columns.GridColumn colPrsmallUOMPrice;
        private DevExpress.XtraGrid.Columns.GridColumn colPrPLName;
        private DevExpress.XtraEditors.SpinEdit txtSmallUOMPrice;
        private DevExpress.XtraEditors.SpinEdit txtMediumUOMPrice;
        private DevExpress.XtraEditors.SpinEdit txtLargeUOMPrice;
        private DevExpress.XtraEditors.SpinEdit txtPurchasePrice;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.SpinEdit txtPurchaseDiscRatio;
        private DevExpress.XtraEditors.LabelControl lblPrTaxVal;
        private DevExpress.XtraEditors.SpinEdit txtPurchaseTaxValue;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.MemoEdit txtDesc;
        private DevExpress.XtraEditors.MemoEdit txtDescEn;
        private DevExpress.XtraEditors.SpinEdit txtSalesTaxRatio;
        private DevExpress.XtraEditors.LabelControl labelControl16;
        private DevExpress.XtraEditors.SpinEdit txtSalesTaxValue;
        private DevExpress.XtraEditors.LabelControl lblSalesTaxRatio;
        private DevExpress.XtraEditors.LabelControl lblSalesTaxValue;
        private DevExpress.XtraEditors.SpinEdit txtPurchaseTaxRatio;
        private DevExpress.XtraEditors.LabelControl labelControl14;
        private DevExpress.XtraEditors.LabelControl lblPrTaxRatio;
        private DevExpress.XtraEditors.LabelControl lblSalesDiscountRatio;
        private DevExpress.XtraEditors.SpinEdit txtSalesDiscRatio;
        private DevExpress.XtraEditors.LabelControl labelControl25;
        private DevExpress.XtraGrid.Columns.GridColumn col_m_name;
        private DevExpress.XtraTab.XtraTabControl tabExtraData;
        private DevExpress.XtraTab.XtraTabPage tabTax;
        private DevExpress.XtraTab.XtraTabPage tabDiscount;
        private DevExpress.XtraTab.XtraTabPage tabPriceChange;
        private DevExpress.XtraTab.XtraTabPage tabDimension;
        private DevExpress.XtraEditors.ImageComboBoxEdit cmbChangeSellPrice;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.ImageComboBoxEdit cmbChangePriceMethod;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraEditors.LabelControl labelControl22;
        private DevExpress.XtraEditors.SpinEdit txtLength;
        private DevExpress.XtraEditors.LabelControl labelControl17;
        private DevExpress.XtraEditors.SpinEdit txtWidth;
        private DevExpress.XtraEditors.LabelControl labelControl12;
        private DevExpress.XtraEditors.SpinEdit txtHeight;
        private System.Windows.Forms.GroupBox grpExtra;
        private DevExpress.XtraGrid.Columns.GridColumn colLength;
        private DevExpress.XtraGrid.Columns.GridColumn colWidth;
        private DevExpress.XtraGrid.Columns.GridColumn colHeight;
        private DevExpress.XtraEditors.LabelControl labelControl24;
        private DevExpress.XtraEditors.CheckEdit rdoSellUOM0;
        private DevExpress.XtraEditors.CheckEdit rdoPrchsUOM2;
        private DevExpress.XtraEditors.CheckEdit rdoPrchsUOM1;
        private DevExpress.XtraEditors.CheckEdit rdoPrchsUOM0;
        private DevExpress.XtraEditors.CheckEdit rdoSellUOM2;
        private DevExpress.XtraEditors.CheckEdit rdoSellUOM1;
        private DevExpress.XtraTab.XtraTabPage tabWarranty;
        private DevExpress.XtraEditors.LabelControl labelControl26;
        private DevExpress.XtraEditors.SpinEdit txt_WarrantyMonths;
        private DevExpress.XtraTab.XtraTabPage tab_InventoryLevels;
        private DevExpress.XtraEditors.TextEdit txt_LargeUOMCode;
        private DevExpress.XtraEditors.LabelControl labelControl30;
        private DevExpress.XtraEditors.TextEdit txt_MediumUOMCode;
        private DevExpress.XtraEditors.LabelControl labelControl29;
        private DevExpress.XtraEditors.CheckEdit chk_calcTaxBeforeDisc;
        private DevExpress.XtraEditors.SimpleButton btn_MatrixPrint;
        private DevExpress.XtraGrid.Columns.GridColumn col_m_Code1;
        private DevExpress.XtraEditors.CheckEdit chk_IsPos;
        private DevExpress.XtraEditors.SpinEdit txtCustomSalesTaxRatio;
        private DevExpress.XtraEditors.LabelControl labelControl31;
        private DevExpress.XtraEditors.LabelControl labelControl32;
        private DevExpress.XtraEditors.LabelControl labelControl33;
        private DevExpress.XtraEditors.LabelControl labelControl34;
        private DevExpress.XtraEditors.SpinEdit txtCustomPurchasesTaxRatio;
        private DevExpress.XtraEditors.LabelControl labelControl35;
        private DevExpress.XtraEditors.SpinEdit txtAudiancePrice;
        private DevExpress.XtraGrid.Columns.GridColumn colPriceLevelId;
        private DevExpress.XtraEditors.CheckEdit chk_IsLibra;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit repPriceLevels;
        private DevExpress.XtraGrid.Columns.GridColumn colPrPriceLevelId;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private System.Windows.Forms.GroupBox groupBox5;
        private DevExpress.XtraGrid.GridControl gridLocation;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn col_Location;
        private DevExpress.XtraGrid.Columns.GridColumn col_Store;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit rep_stores;
        private DevExpress.XtraEditors.CheckEdit chk_VariableWeight;
        private DevExpress.XtraEditors.CheckEdit chk_PricingWithSmall;
        private DevExpress.XtraEditors.LabelControl lbl_WeightUnit;
        private DevExpress.XtraEditors.ImageComboBoxEdit cmb_WeightUnit;
        private DevExpress.XtraEditors.CheckEdit chk_LargeIsStopped;
        private DevExpress.XtraEditors.CheckEdit chk_MediumIsStopped;
        private DevExpress.XtraEditors.CheckEdit chk_SmallIsStopped;
        private DevExpress.XtraEditors.LabelControl labelControl37;
        private DevExpress.XtraTab.XtraTabPage tab_SubTaxes;
        private DevExpress.XtraGrid.GridControl grd_SubTaxes;
        private DevExpress.XtraGrid.Views.Grid.GridView gv_SubTaxes;
        private DevExpress.XtraGrid.Columns.GridColumn IC_ItemSubTaxesId;
        private DevExpress.XtraGrid.Columns.GridColumn SubTaxId;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit lkp_SubTaxes;
        private DevExpress.XtraGrid.Columns.GridColumn col_Rate;
    }
}