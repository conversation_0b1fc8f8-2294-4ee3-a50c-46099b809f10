using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using DAL;
using System.Linq;
using System.Data;
using DevExpress.XtraEditors;

using System.Windows.Forms;

namespace Reports
{
    public partial class rpt_manufacture : DevExpress.XtraReports.UI.XtraReport
    {
        string _Manf_Number, _Startdate, _EndDate, _ProductStore, _SupposedCost, _ActualCost, _Notes, userName;

        DataTable dt_Supposed_Raws;
        DataTable dt_Actual_Raws;
        DataTable dt_Supposed_expenses;
        DataTable dt_Actual_expenses;
        DataTable dt_emps;
        DataTable dt_damages;
        DataTable dt_products;

        public rpt_manufacture()
        {
            InitializeComponent();
        }

        public rpt_manufacture(string Manf_Number, string Startdate, string EndDate, string ProductStore, string SupposedCost, string ActualCost, string Notes,
            DataTable dt_Supposed_Raws,
            DataTable dt_Actual_Raws,
            DataTable dt_Supposed_expenses,
            DataTable dt_Actual_expenses,
            DataTable dt_emps,
            DataTable dt_damages,
            DataTable dt_products,
            string userName)
        {
            InitializeComponent();
            _Manf_Number = Manf_Number;
            _Startdate = Startdate;
            _EndDate = EndDate;
            _ProductStore = ProductStore;
            _SupposedCost = SupposedCost;
            _ActualCost = ActualCost;
            _Notes = Notes;
            this.userName = userName;

            this.dt_Supposed_Raws = dt_Supposed_Raws;
            this.dt_Actual_Raws = dt_Actual_Raws;
            this.dt_Supposed_expenses = dt_Supposed_expenses;
            this.dt_Actual_expenses = dt_Actual_expenses;
            this.dt_emps = dt_emps;
            this.dt_damages = dt_damages;
            this.dt_products = dt_products;

            getReportHeader();
        }

        public void LoadData()
        {
            lbl_Manf_Number.Text = _Manf_Number;
            lbl_Startdate.Text = _Startdate;
            lbl_EndDate.Text = _EndDate;
            lbl_ProductStore.Text = _ProductStore;
            lbl_SupposedCost.Text = _SupposedCost;
            lbl_ActualCost.Text = _ActualCost;
            lbl_Notes.Text = _Notes;
            lbl_User.Text = userName;

            DetailReport_Products.DataSource = dt_products;
            cell_Prd_ItemName.DataBindings.Add("Text", DetailReport_Products.DataSource, "Prd_ItemName");
            cell_Prd_UOM.DataBindings.Add("Text", DetailReport_Products.DataSource, "Prd_UOM");
            cell_Prd_Qty.DataBindings.Add("Text", DetailReport_Products.DataSource, "Prd_Qty");
            cell_Prd_Length.DataBindings.Add("Text", DetailReport_Products.DataSource, "Prd_Length");
            cell_Prd_Width.DataBindings.Add("Text", DetailReport_Products.DataSource, "Prd_Width");
            cell_Prd_Height.DataBindings.Add("Text", DetailReport_Products.DataSource, "Prd_Height");
            cell_Prd_TotalQty.DataBindings.Add("Text", DetailReport_Products.DataSource, "Prd_TotalQty");
            cell_Prd_PiecesCount.DataBindings.Add("Text", DetailReport_Products.DataSource, "Prd_PiecesCount");
            cell_Prd_SupposedCost.DataBindings.Add("Text", DetailReport_Products.DataSource, "Prd_SupposedCost");
            cell_Prd_ActualCost.DataBindings.Add("Text", DetailReport_Products.DataSource, "Prd_ActualCost");
            cell_Prd_BOM.DataBindings.Add("Text", DetailReport_Products.DataSource, "Prd_BOM");

            DetailReport_SupposedRaws.DataSource = dt_Supposed_Raws;
            cell_S_ItemName.DataBindings.Add("Text", DetailReport_SupposedRaws.DataSource, "S_ItemName");
            cell_S_UOM.DataBindings.Add("Text", DetailReport_SupposedRaws.DataSource, "S_UOM");
            cell_S_Qty.DataBindings.Add("Text", DetailReport_SupposedRaws.DataSource, "S_Qty");
            cell_S_Total.DataBindings.Add("Text", DetailReport_SupposedRaws.DataSource, "S_Total");

            DetailReport_ActualRaws.DataSource = dt_Actual_Raws;
            cell_A_StoreName.DataBindings.Add("Text", DetailReport_ActualRaws.DataSource, "A_StoreName");
            cell_A_ItemName.DataBindings.Add("Text", DetailReport_ActualRaws.DataSource, "A_ItemName");
            cell_A_UOM.DataBindings.Add("Text", DetailReport_ActualRaws.DataSource, "A_UOM");
            cell_A_Qty.DataBindings.Add("Text", DetailReport_ActualRaws.DataSource, "A_Qty");
            cell_A_Total.DataBindings.Add("Text", DetailReport_ActualRaws.DataSource, "A_Total");
            cell_A_Length.DataBindings.Add("Text", DetailReport_ActualRaws.DataSource, "A_Length");
            cell_A_Width.DataBindings.Add("Text", DetailReport_ActualRaws.DataSource, "A_Width");
            cell_A_Height.DataBindings.Add("Text", DetailReport_ActualRaws.DataSource, "A_Height");
            cell_A_TotalQty.DataBindings.Add("Text", DetailReport_ActualRaws.DataSource, "A_TotalQty");
            cell_A_PiecesCount.DataBindings.Add("Text", DetailReport_ActualRaws.DataSource, "A_PiecesCount");
            cell_A_Code1.DataBindings.Add("Text", DetailReport_ActualRaws.DataSource, "ItemCode1");
            cell_A_Code2.DataBindings.Add("Text", DetailReport_ActualRaws.DataSource, "ItemCode2");
            cell_A_TotalCost.DataBindings.Add("Text", DetailReport_ActualRaws.DataSource, "TotalCost");

            var itms_totalQty = (from DataRow a in dt_Actual_Raws.Rows
                                 select a["A_Qty"]).ToList().DefaultIfEmpty().Sum(x => Convert.ToDouble(x));


            lbl_itms_totalQty.Text = itms_totalQty.ToString();

            DetailReport_SupposedExpenses.DataSource = dt_Supposed_expenses;
            cell_S_ExpenseName.DataBindings.Add("Text", DetailReport_SupposedExpenses.DataSource, "S_ExpenseName");
            cell_S_ExpenseValue.DataBindings.Add("Text", DetailReport_SupposedExpenses.DataSource, "S_ExpenseValue");

            DetailReport_ActualExpenes.DataSource = dt_Actual_expenses;
            cell_A_ExpenseDrawer.DataBindings.Add("Text", DetailReport_ActualExpenes.DataSource, "A_ExpenseDrawer");
            cell_A_ExpenseName.DataBindings.Add("Text", DetailReport_ActualExpenes.DataSource, "A_ExpenseName");
            cell_A_ExpenseValue.DataBindings.Add("Text", DetailReport_ActualExpenes.DataSource, "A_ExpenseValue");

            DetailReport_wages.DataSource = dt_emps;
            cell_EmpName.DataBindings.Add("Text", DetailReport_wages.DataSource, "EmpName");
            cell_EmpDate.DataBindings.Add("Text", DetailReport_wages.DataSource, "EmpDate");
            cell_EmpWorkHours.DataBindings.Add("Text", DetailReport_wages.DataSource, "EmpWorkHours");
            cell_EmpNotes.DataBindings.Add("Text", DetailReport_wages.DataSource, "EmpNotes");
            cell_EmpTotal.DataBindings.Add("Text", DetailReport_wages.DataSource, "EmpTotal");
            cell_EmpItemName.DataBindings.Add("Text", DetailReport_wages.DataSource, "EmpItemName");
            cell_EmpItemUOM.DataBindings.Add("Text", DetailReport_wages.DataSource, "EmpItemUOM");

            DetailReport_Damage.DataSource = dt_damages;
            cell_DamageItemName.DataBindings.Add("Text", DetailReport_Damage.DataSource, "DamageItemName");
            cell_DamageUOM.DataBindings.Add("Text", DetailReport_Damage.DataSource, "DamageUOM");
            cell_DamageQty.DataBindings.Add("Text", DetailReport_Damage.DataSource, "DamageQty");

        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                lblCompName.Text = comp.CmpNameAr;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

    }
}
