﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;
using DAL.Res;
using System.Linq;
using DevExpress.XtraPrinting;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Base;

namespace Pharmacy.Forms
{
    public partial class frm_E_UOM : DevExpress.XtraEditors.XtraForm
    {        
        ERPDataContext DB = new ERPDataContext();

        public frm_E_UOM()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
        }

        private void frm_E_UOM_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            var uoms = DB.IC_UOMs.Select(x => x);
            grdUom.DataSource = uoms;

          //  grdUom.ProcessGridKey += new KeyEventHandler(grdDept_ProcessGridKey);
        }        

        

        private void barBtnSave_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                //  gvUom.UpdateCurrentRow();
                grdUom.FocusedView.PostEditor();
                grdUom.FocusedView.UpdateCurrentRow();
                DB.SubmitChanges();
                XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.MsgSave : ResSLAr.MsgSave, "", MessageBoxButtons.OK, MessageBoxIcon.Information);

            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);                
            }
        }


        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void gridView1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Delete && e.Modifiers == Keys.Control)
            {
                if (MessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgDelRow : ResICAr.MsgDelRow, //"حذف صف ؟"
                    Shared.IsEnglish == true ? ResICEn.MsgTQues : ResICAr.MsgTQues,
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) !=
                  DialogResult.Yes)
                    return;

                GridView view = sender as GridView;

                if (view.GetFocusedRowCellValue(colUOMId) == null)
                    return;

                int pId = Convert.ToInt32(view.GetFocusedRowCellValue(colUOMId));
                if (DB.IC_Items.Where(x => x.SmallUOM == pId || x.MediumUOM == pId || x.LargeUOM == pId).Count() > 0)
                {
                    MessageBox.Show(
                         Shared.IsEnglish == true ? ResEn.DelEntryDenied : ResAr.DelEntryDenied,
                    Shared.IsEnglish == true ? ResEn.MsgDelRow : ResAr.MsgDelRow,
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }
                else
                    view.DeleteRow(view.FocusedRowHandle);
            }
        }

        private void gvPriority_ValidateRow(object sender, DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs e)
        {
            try
            {
                ColumnView view = sender as ColumnView;

                if (view.GetRowCellValue(e.RowHandle, view.Columns["UOM"]).ToString() == string.Empty)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["UOM"],"*");
                }
            }
            catch
            {
                e.Valid = false;
            }
        }

        private void gvPriority_InvalidRowException(object sender, InvalidRowExceptionEventArgs e)
        {
            e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.NoAction;
        }

        private void barBtnHelp_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "تسجيل عميل جديد");
        }

        //void grdDept_ProcessGridKey(object sender, KeyEventArgs e)
        //{
        //    try
        //    {
        //        DevExpress.XtraGrid.GridControl grid = sender as DevExpress.XtraGrid.GridControl;
        //        var view = (grid.FocusedView as DevExpress.XtraGrid.Views.Grid.GridView);
        //        if (e.KeyCode == Keys.Enter)
        //        {
        //            var focused_column = (grid.FocusedView as DevExpress.XtraGrid.Views.Base.ColumnView).FocusedColumn;
        //            int focused_row_handle = (grid.FocusedView as DevExpress.XtraGrid.Views.Base.ColumnView).FocusedRowHandle;

        //            if (view.FocusedColumn.VisibleIndex != 0)
        //            {
        //                var temp = view.FocusedColumn;
        //                grdDept_ProcessGridKey(sender, new KeyEventArgs(Keys.Tab));
        //                if (view.GetFocusedRowCellValue(temp) == null || view.GetFocusedRowCellValue(temp).ToString() == string.Empty
        //                    || view.GetFocusedRowCellValue(temp).ToString() == "0")
        //                    view.FocusedColumn = temp;

        //                return;
        //            }
        //            if (view.FocusedColumn.VisibleIndex == 0)
        //            {
        //                var temp = view.FocusedColumn;
        //                grdDept_ProcessGridKey(sender, new KeyEventArgs(Keys.Tab));
        //                if (view.GetFocusedRowCellValue(temp) == null || view.GetFocusedRowCellValue(temp).ToString() == string.Empty
        //                    || view.GetFocusedRowCellValue(temp).ToString() == "0")
        //                {
        //                    view.FocusedColumn = temp;
        //                    return;
        //                }
        //            }
        //            if (view.FocusedRowHandle < 0)
        //            {
        //                view.AddNewRow();
        //                view.FocusedColumn = view.VisibleColumns[view.VisibleColumns.Count - 1];
        //            }
        //            else
        //            {
        //                view.FocusedRowHandle = focused_row_handle + 1;
        //                view.FocusedColumn = view.VisibleColumns[view.VisibleColumns.Count - 1];
        //            }
        //            e.Handled = true;
        //            return;
        //        }
        //        if (e.KeyCode == Keys.Tab && e.Modifiers != Keys.Shift)
        //        {
        //            if (view.FocusedColumn.VisibleIndex == 0)
        //                view.FocusedColumn = view.VisibleColumns[view.VisibleColumns.Count - 1];
        //            else
        //                view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex - 1];
        //            e.Handled = true;
        //            return;
        //        }
        //        if (e.KeyCode == Keys.Tab && e.Modifiers == Keys.Shift)
        //        {
        //            if (view.FocusedColumn.VisibleIndex == view.VisibleColumns.Count)
        //                view.FocusedColumn = view.VisibleColumns[0];
        //            else
        //                view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex + 1];
        //            e.Handled = true;
        //            return;
        //        }
        //    }
        //    catch
        //    { }
        //}
    }
}