﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;
using DAL.Res;

namespace Pharmacy.Forms
{
    public partial class uc_CurrencyValue : DevExpress.XtraEditors.XtraUserControl
    {
        int crncyId;
        public int CrncyId
        {
            get { return crncyId; }
            set { crncyId = value; }
        }

        public uc_CurrencyValue()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();            
        }

        private void uc_LinkAccount_Load(object sender, EventArgs e)
        {
            if (Shared.CurrencyAvailable == false)
            {
                lkp_Crnc.Properties.ReadOnly = true;
            }
            else
            {
                List<DAL.ST_Currency> lstCrnc = new List<DAL.ST_Currency>();
                lstCrnc.AddRange(Shared.lstCurrency);
                lstCrnc.Insert(lstCrnc.Count, new DAL.ST_Currency
                {
                    CrncId = -1,
                    crncName = Shared.IsEnglish ? ResEn.LastRate : ResAr.LastRate,
                    CurrencyDigitsCount = 0,
                    CurrencyPiaster1 = string.Empty,
                    CurrencyPiaster2 = string.Empty,
                    CurrencyPiaster3 = string.Empty,
                    CurrencyPound1 = string.Empty,
                    CurrencyPound2 = string.Empty,
                    CurrencyPound3 = string.Empty,
                    LastRate = 0
                });
                lstCrnc.Insert(lstCrnc.Count, new DAL.ST_Currency
                {
                    CrncId = -2,
                    crncName = Shared.IsEnglish ? ResEn.TrnsRate : ResAr.TrnsRate,
                    CurrencyDigitsCount = 0,
                    CurrencyPiaster1 = string.Empty,
                    CurrencyPiaster2 = string.Empty,
                    CurrencyPiaster3 = string.Empty,
                    CurrencyPound1 = string.Empty,
                    CurrencyPound2 = string.Empty,
                    CurrencyPound3 = string.Empty,
                    LastRate = 0
                });

                lkp_Crnc.Properties.DataSource = lstCrnc;
                lkp_Crnc.Properties.ValueMember = "CrncId";
                lkp_Crnc.Properties.DisplayMember = "crncName";                
            }

            lkp_Crnc.EditValue = 0;//default one            
        }

        private void lkp_Crnc_EditValueChanged(object sender, EventArgs e)
        {
            CrncyId = Convert.ToInt32(lkp_Crnc.EditValue);
        }
    }
}
