﻿namespace Pharmacy.Forms
{
    partial class frm_SL_CustomerGroup
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_SL_CustomerGroup));
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtnHelp = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnPrint = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnRefresh = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnNew = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnDelete = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnSave = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.txtName = new DevExpress.XtraEditors.TextEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.txtCode = new DevExpress.XtraEditors.TextEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.txtFName = new DevExpress.XtraEditors.TextEdit();
            this.lkp_ParentGrpId = new DevExpress.XtraEditors.GridLookUpEdit();
            this.gridLookUpEdit1View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_AccId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_AccName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.treeView1 = new System.Windows.Forms.TreeView();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.tab_MainData = new DevExpress.XtraTab.XtraTabPage();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCode.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_ParentGrpId.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridLookUpEdit1View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.tab_MainData.SuspendLayout();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnSave,
            this.barBtnHelp,
            this.barBtnNew,
            this.barBtnDelete,
            this.barBtnClose,
            this.barBtnRefresh,
            this.barBtnPrint});
            this.barManager1.MaxItemId = 34;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(377, 152);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnHelp),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnPrint),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnRefresh),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnNew),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnDelete),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnSave),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtnHelp
            // 
            resources.ApplyResources(this.barBtnHelp, "barBtnHelp");
            this.barBtnHelp.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnHelp.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtnHelp.Id = 2;
            this.barBtnHelp.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtnHelp.Name = "barBtnHelp";
            this.barBtnHelp.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnHelp.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnHelp_ItemClick);
            // 
            // barBtnPrint
            // 
            resources.ApplyResources(this.barBtnPrint, "barBtnPrint");
            this.barBtnPrint.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnPrint.Glyph = global::Pharmacy.Properties.Resources.prnt;
            this.barBtnPrint.Id = 33;
            this.barBtnPrint.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.P));
            this.barBtnPrint.Name = "barBtnPrint";
            this.barBtnPrint.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnPrint.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnPrint_ItemClick);
            // 
            // barBtnRefresh
            // 
            resources.ApplyResources(this.barBtnRefresh, "barBtnRefresh");
            this.barBtnRefresh.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnRefresh.Glyph = global::Pharmacy.Properties.Resources.refresh;
            this.barBtnRefresh.Id = 32;
            this.barBtnRefresh.Name = "barBtnRefresh";
            this.barBtnRefresh.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnRefresh.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnRefresh_ItemClick);
            // 
            // barBtnNew
            // 
            resources.ApplyResources(this.barBtnNew, "barBtnNew");
            this.barBtnNew.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnNew.Glyph = global::Pharmacy.Properties.Resources._new;
            this.barBtnNew.Id = 27;
            this.barBtnNew.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.N));
            this.barBtnNew.Name = "barBtnNew";
            this.barBtnNew.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnNew.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnNew_ItemClick);
            // 
            // barBtnDelete
            // 
            resources.ApplyResources(this.barBtnDelete, "barBtnDelete");
            this.barBtnDelete.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnDelete.Glyph = global::Pharmacy.Properties.Resources.del;
            this.barBtnDelete.Id = 28;
            this.barBtnDelete.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.D));
            this.barBtnDelete.Name = "barBtnDelete";
            this.barBtnDelete.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnDelete.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Delete_ItemClick);
            // 
            // barBtnSave
            // 
            resources.ApplyResources(this.barBtnSave, "barBtnSave");
            this.barBtnSave.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnSave.Glyph = global::Pharmacy.Properties.Resources.sve;
            this.barBtnSave.Id = 0;
            this.barBtnSave.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.S));
            this.barBtnSave.Name = "barBtnSave";
            this.barBtnSave.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnSave.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Save_ItemClick);
            // 
            // barBtnClose
            // 
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 31;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnClose_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlTop.Appearance.FontSizeDelta")));
            this.barDockControlTop.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlTop.Appearance.FontStyleDelta")));
            this.barDockControlTop.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlTop.Appearance.GradientMode")));
            this.barDockControlTop.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlTop.Appearance.Image")));
            this.barDockControlTop.CausesValidation = false;
            // 
            // barDockControlBottom
            // 
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlBottom.Appearance.FontSizeDelta")));
            this.barDockControlBottom.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlBottom.Appearance.FontStyleDelta")));
            this.barDockControlBottom.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlBottom.Appearance.GradientMode")));
            this.barDockControlBottom.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlBottom.Appearance.Image")));
            this.barDockControlBottom.CausesValidation = false;
            // 
            // barDockControlLeft
            // 
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlLeft.Appearance.FontSizeDelta")));
            this.barDockControlLeft.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlLeft.Appearance.FontStyleDelta")));
            this.barDockControlLeft.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlLeft.Appearance.GradientMode")));
            this.barDockControlLeft.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlLeft.Appearance.Image")));
            this.barDockControlLeft.CausesValidation = false;
            // 
            // barDockControlRight
            // 
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlRight.Appearance.FontSizeDelta")));
            this.barDockControlRight.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlRight.Appearance.FontStyleDelta")));
            this.barDockControlRight.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlRight.Appearance.GradientMode")));
            this.barDockControlRight.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlRight.Appearance.Image")));
            this.barDockControlRight.CausesValidation = false;
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemTextEdit1.Mask.AutoComplete")));
            this.repositoryItemTextEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.BeepOnError")));
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemTextEdit1.Mask.MaskType")));
            this.repositoryItemTextEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemTextEdit1.Mask.PlaceHolder")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // labelControl4
            // 
            resources.ApplyResources(this.labelControl4, "labelControl4");
            this.labelControl4.Name = "labelControl4";
            // 
            // txtName
            // 
            resources.ApplyResources(this.txtName, "txtName");
            this.txtName.EnterMoveNextControl = true;
            this.txtName.Name = "txtName";
            this.txtName.Properties.AccessibleDescription = resources.GetString("txtName.Properties.AccessibleDescription");
            this.txtName.Properties.AccessibleName = resources.GetString("txtName.Properties.AccessibleName");
            this.txtName.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txtName.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtName.Properties.Appearance.FontSizeDelta")));
            this.txtName.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtName.Properties.Appearance.FontStyleDelta")));
            this.txtName.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtName.Properties.Appearance.GradientMode")));
            this.txtName.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtName.Properties.Appearance.Image")));
            this.txtName.Properties.Appearance.Options.UseTextOptions = true;
            this.txtName.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtName.Properties.AutoHeight = ((bool)(resources.GetObject("txtName.Properties.AutoHeight")));
            this.txtName.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtName.Properties.Mask.AutoComplete")));
            this.txtName.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtName.Properties.Mask.BeepOnError")));
            this.txtName.Properties.Mask.EditMask = resources.GetString("txtName.Properties.Mask.EditMask");
            this.txtName.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtName.Properties.Mask.IgnoreMaskBlank")));
            this.txtName.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtName.Properties.Mask.MaskType")));
            this.txtName.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtName.Properties.Mask.PlaceHolder")));
            this.txtName.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtName.Properties.Mask.SaveLiteral")));
            this.txtName.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtName.Properties.Mask.ShowPlaceHolders")));
            this.txtName.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtName.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtName.Properties.MaxLength = 100;
            this.txtName.Properties.NullValuePrompt = resources.GetString("txtName.Properties.NullValuePrompt");
            this.txtName.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtName.Properties.NullValuePromptShowForEmptyValue")));
            this.txtName.Modified += new System.EventHandler(this.txtDrawerNameAr_Modified);
            // 
            // labelControl1
            // 
            resources.ApplyResources(this.labelControl1, "labelControl1");
            this.labelControl1.Name = "labelControl1";
            // 
            // txtCode
            // 
            resources.ApplyResources(this.txtCode, "txtCode");
            this.txtCode.EnterMoveNextControl = true;
            this.txtCode.Name = "txtCode";
            this.txtCode.Properties.AccessibleDescription = resources.GetString("txtCode.Properties.AccessibleDescription");
            this.txtCode.Properties.AccessibleName = resources.GetString("txtCode.Properties.AccessibleName");
            this.txtCode.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txtCode.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtCode.Properties.Appearance.FontSizeDelta")));
            this.txtCode.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtCode.Properties.Appearance.FontStyleDelta")));
            this.txtCode.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtCode.Properties.Appearance.GradientMode")));
            this.txtCode.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtCode.Properties.Appearance.Image")));
            this.txtCode.Properties.Appearance.Options.UseTextOptions = true;
            this.txtCode.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtCode.Properties.AutoHeight = ((bool)(resources.GetObject("txtCode.Properties.AutoHeight")));
            this.txtCode.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtCode.Properties.Mask.AutoComplete")));
            this.txtCode.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtCode.Properties.Mask.BeepOnError")));
            this.txtCode.Properties.Mask.EditMask = resources.GetString("txtCode.Properties.Mask.EditMask");
            this.txtCode.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtCode.Properties.Mask.IgnoreMaskBlank")));
            this.txtCode.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtCode.Properties.Mask.MaskType")));
            this.txtCode.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtCode.Properties.Mask.PlaceHolder")));
            this.txtCode.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtCode.Properties.Mask.SaveLiteral")));
            this.txtCode.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtCode.Properties.Mask.ShowPlaceHolders")));
            this.txtCode.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtCode.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtCode.Properties.NullValuePrompt = resources.GetString("txtCode.Properties.NullValuePrompt");
            this.txtCode.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtCode.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl3
            // 
            resources.ApplyResources(this.labelControl3, "labelControl3");
            this.labelControl3.Name = "labelControl3";
            // 
            // txtFName
            // 
            resources.ApplyResources(this.txtFName, "txtFName");
            this.txtFName.EnterMoveNextControl = true;
            this.txtFName.Name = "txtFName";
            this.txtFName.Properties.AccessibleDescription = resources.GetString("txtFName.Properties.AccessibleDescription");
            this.txtFName.Properties.AccessibleName = resources.GetString("txtFName.Properties.AccessibleName");
            this.txtFName.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txtFName.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtFName.Properties.Appearance.FontSizeDelta")));
            this.txtFName.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtFName.Properties.Appearance.FontStyleDelta")));
            this.txtFName.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtFName.Properties.Appearance.GradientMode")));
            this.txtFName.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtFName.Properties.Appearance.Image")));
            this.txtFName.Properties.Appearance.Options.UseTextOptions = true;
            this.txtFName.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtFName.Properties.AutoHeight = ((bool)(resources.GetObject("txtFName.Properties.AutoHeight")));
            this.txtFName.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtFName.Properties.Mask.AutoComplete")));
            this.txtFName.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtFName.Properties.Mask.BeepOnError")));
            this.txtFName.Properties.Mask.EditMask = resources.GetString("txtFName.Properties.Mask.EditMask");
            this.txtFName.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtFName.Properties.Mask.IgnoreMaskBlank")));
            this.txtFName.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtFName.Properties.Mask.MaskType")));
            this.txtFName.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtFName.Properties.Mask.PlaceHolder")));
            this.txtFName.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtFName.Properties.Mask.SaveLiteral")));
            this.txtFName.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtFName.Properties.Mask.ShowPlaceHolders")));
            this.txtFName.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtFName.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtFName.Properties.MaxLength = 100;
            this.txtFName.Properties.NullValuePrompt = resources.GetString("txtFName.Properties.NullValuePrompt");
            this.txtFName.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtFName.Properties.NullValuePromptShowForEmptyValue")));
            this.txtFName.Modified += new System.EventHandler(this.txtDrawerNameAr_Modified);
            // 
            // lkp_ParentGrpId
            // 
            resources.ApplyResources(this.lkp_ParentGrpId, "lkp_ParentGrpId");
            this.lkp_ParentGrpId.EnterMoveNextControl = true;
            this.lkp_ParentGrpId.MenuManager = this.barManager1;
            this.lkp_ParentGrpId.Name = "lkp_ParentGrpId";
            this.lkp_ParentGrpId.Properties.AccessibleDescription = resources.GetString("lkp_ParentGrpId.Properties.AccessibleDescription");
            this.lkp_ParentGrpId.Properties.AccessibleName = resources.GetString("lkp_ParentGrpId.Properties.AccessibleName");
            this.lkp_ParentGrpId.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkp_ParentGrpId.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_ParentGrpId.Properties.Appearance.FontSizeDelta")));
            this.lkp_ParentGrpId.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_ParentGrpId.Properties.Appearance.FontStyleDelta")));
            this.lkp_ParentGrpId.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_ParentGrpId.Properties.Appearance.GradientMode")));
            this.lkp_ParentGrpId.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_ParentGrpId.Properties.Appearance.Image")));
            this.lkp_ParentGrpId.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_ParentGrpId.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_ParentGrpId.Properties.AppearanceDisabled.BackColor = ((System.Drawing.Color)(resources.GetObject("lkp_ParentGrpId.Properties.AppearanceDisabled.BackColor")));
            this.lkp_ParentGrpId.Properties.AppearanceDisabled.FontSizeDelta = ((int)(resources.GetObject("lkp_ParentGrpId.Properties.AppearanceDisabled.FontSizeDelta")));
            this.lkp_ParentGrpId.Properties.AppearanceDisabled.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_ParentGrpId.Properties.AppearanceDisabled.FontStyleDelta")));
            this.lkp_ParentGrpId.Properties.AppearanceDisabled.ForeColor = ((System.Drawing.Color)(resources.GetObject("lkp_ParentGrpId.Properties.AppearanceDisabled.ForeColor")));
            this.lkp_ParentGrpId.Properties.AppearanceDisabled.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_ParentGrpId.Properties.AppearanceDisabled.GradientMode")));
            this.lkp_ParentGrpId.Properties.AppearanceDisabled.Image = ((System.Drawing.Image)(resources.GetObject("lkp_ParentGrpId.Properties.AppearanceDisabled.Image")));
            this.lkp_ParentGrpId.Properties.AppearanceDisabled.Options.UseBackColor = true;
            this.lkp_ParentGrpId.Properties.AppearanceDisabled.Options.UseForeColor = true;
            this.lkp_ParentGrpId.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_ParentGrpId.Properties.AutoHeight")));
            this.lkp_ParentGrpId.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_ParentGrpId.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_ParentGrpId.Properties.Buttons"))))});
            this.lkp_ParentGrpId.Properties.DisplayMember = "AccName";
            this.lkp_ParentGrpId.Properties.ImmediatePopup = true;
            this.lkp_ParentGrpId.Properties.NullText = resources.GetString("lkp_ParentGrpId.Properties.NullText");
            this.lkp_ParentGrpId.Properties.NullValuePrompt = resources.GetString("lkp_ParentGrpId.Properties.NullValuePrompt");
            this.lkp_ParentGrpId.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_ParentGrpId.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_ParentGrpId.Properties.PopupFilterMode = DevExpress.XtraEditors.PopupFilterMode.Contains;
            this.lkp_ParentGrpId.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.lkp_ParentGrpId.Properties.ValueMember = "AccId";
            this.lkp_ParentGrpId.Properties.View = this.gridLookUpEdit1View;
            this.lkp_ParentGrpId.TabStop = false;
            this.lkp_ParentGrpId.Modified += new System.EventHandler(this.txtDrawerNameAr_Modified);
            // 
            // gridLookUpEdit1View
            // 
            resources.ApplyResources(this.gridLookUpEdit1View, "gridLookUpEdit1View");
            this.gridLookUpEdit1View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_AccId,
            this.col_AccName});
            this.gridLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridLookUpEdit1View.Name = "gridLookUpEdit1View";
            this.gridLookUpEdit1View.OptionsFilter.ShowAllTableValuesInFilterPopup = true;
            this.gridLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridLookUpEdit1View.OptionsView.ShowAutoFilterRow = true;
            this.gridLookUpEdit1View.OptionsView.ShowDetailButtons = false;
            this.gridLookUpEdit1View.OptionsView.ShowGroupExpandCollapseButtons = false;
            this.gridLookUpEdit1View.OptionsView.ShowGroupPanel = false;
            this.gridLookUpEdit1View.OptionsView.ShowIndicator = false;
            // 
            // col_AccId
            // 
            this.col_AccId.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_AccId.AppearanceCell.FontSizeDelta")));
            this.col_AccId.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_AccId.AppearanceCell.FontStyleDelta")));
            this.col_AccId.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_AccId.AppearanceCell.GradientMode")));
            this.col_AccId.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_AccId.AppearanceCell.Image")));
            this.col_AccId.AppearanceCell.Options.UseTextOptions = true;
            this.col_AccId.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_AccId.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_AccId.AppearanceHeader.FontSizeDelta")));
            this.col_AccId.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_AccId.AppearanceHeader.FontStyleDelta")));
            this.col_AccId.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_AccId.AppearanceHeader.GradientMode")));
            this.col_AccId.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_AccId.AppearanceHeader.Image")));
            this.col_AccId.AppearanceHeader.Options.UseTextOptions = true;
            this.col_AccId.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.col_AccId, "col_AccId");
            this.col_AccId.FieldName = "AccId";
            this.col_AccId.Name = "col_AccId";
            this.col_AccId.OptionsFilter.AllowAutoFilter = false;
            this.col_AccId.OptionsFilter.AllowFilter = false;
            // 
            // col_AccName
            // 
            this.col_AccName.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_AccName.AppearanceCell.FontSizeDelta")));
            this.col_AccName.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_AccName.AppearanceCell.FontStyleDelta")));
            this.col_AccName.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_AccName.AppearanceCell.GradientMode")));
            this.col_AccName.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_AccName.AppearanceCell.Image")));
            this.col_AccName.AppearanceCell.Options.UseTextOptions = true;
            this.col_AccName.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_AccName.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_AccName.AppearanceHeader.FontSizeDelta")));
            this.col_AccName.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_AccName.AppearanceHeader.FontStyleDelta")));
            this.col_AccName.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_AccName.AppearanceHeader.GradientMode")));
            this.col_AccName.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_AccName.AppearanceHeader.Image")));
            this.col_AccName.AppearanceHeader.Options.UseTextOptions = true;
            this.col_AccName.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.col_AccName, "col_AccName");
            this.col_AccName.FieldName = "AccName";
            this.col_AccName.Name = "col_AccName";
            this.col_AccName.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // labelControl6
            // 
            resources.ApplyResources(this.labelControl6, "labelControl6");
            this.labelControl6.Name = "labelControl6";
            // 
            // treeView1
            // 
            resources.ApplyResources(this.treeView1, "treeView1");
            this.treeView1.Name = "treeView1";
            this.treeView1.AfterSelect += new System.Windows.Forms.TreeViewEventHandler(this.treeView1_AfterSelect);
            // 
            // xtraTabControl1
            // 
            resources.ApplyResources(this.xtraTabControl1, "xtraTabControl1");
            this.xtraTabControl1.AppearancePage.Header.FontSizeDelta = ((int)(resources.GetObject("xtraTabControl1.AppearancePage.Header.FontSizeDelta")));
            this.xtraTabControl1.AppearancePage.Header.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("xtraTabControl1.AppearancePage.Header.FontStyleDelta")));
            this.xtraTabControl1.AppearancePage.Header.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("xtraTabControl1.AppearancePage.Header.GradientMode")));
            this.xtraTabControl1.AppearancePage.Header.Image = ((System.Drawing.Image)(resources.GetObject("xtraTabControl1.AppearancePage.Header.Image")));
            this.xtraTabControl1.AppearancePage.Header.Options.UseTextOptions = true;
            this.xtraTabControl1.AppearancePage.Header.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.tab_MainData;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.tab_MainData});
            // 
            // tab_MainData
            // 
            resources.ApplyResources(this.tab_MainData, "tab_MainData");
            this.tab_MainData.Controls.Add(this.lkp_ParentGrpId);
            this.tab_MainData.Controls.Add(this.txtCode);
            this.tab_MainData.Controls.Add(this.labelControl6);
            this.tab_MainData.Controls.Add(this.labelControl1);
            this.tab_MainData.Controls.Add(this.txtFName);
            this.tab_MainData.Controls.Add(this.labelControl4);
            this.tab_MainData.Controls.Add(this.txtName);
            this.tab_MainData.Controls.Add(this.labelControl3);
            this.tab_MainData.Name = "tab_MainData";
            // 
            // frm_SL_CustomerGroup
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.xtraTabControl1);
            this.Controls.Add(this.treeView1);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.KeyPreview = true;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "frm_SL_CustomerGroup";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_SL_CustomerGroup_FormClosing);
            this.Load += new System.EventHandler(this.frm_SL_CustomerGroup_Load);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCode.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_ParentGrpId.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridLookUpEdit1View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.tab_MainData.ResumeLayout(false);
            this.tab_MainData.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnSave;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtnHelp;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.TextEdit txtName;
        private DevExpress.XtraBars.BarButtonItem barBtnNew;
        private DevExpress.XtraBars.BarButtonItem barBtnDelete;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.TextEdit txtCode;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.TextEdit txtFName;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraEditors.GridLookUpEdit lkp_ParentGrpId;
        private DevExpress.XtraGrid.Views.Grid.GridView gridLookUpEdit1View;
        private DevExpress.XtraGrid.Columns.GridColumn col_AccId;
        private DevExpress.XtraGrid.Columns.GridColumn col_AccName;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private System.Windows.Forms.TreeView treeView1;
        private DevExpress.XtraBars.BarButtonItem barBtnRefresh;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage tab_MainData;
        private DevExpress.XtraBars.BarButtonItem barBtnPrint;
    }
}