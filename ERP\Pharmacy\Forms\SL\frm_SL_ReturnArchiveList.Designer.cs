﻿namespace Pharmacy.Forms
{
    partial class frm_SL_ReturnArchiveList
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_SL_ReturnArchiveList));
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtn_Help = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnPrint = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnRefresh = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnOpen = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnNew = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.grdCategory = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.mi_OpenDealer = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_SL_ReturnId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ReturnCode = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_InvoiceBookId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_InvoiceBook = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.col_StoreId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CustomerId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ReturnDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Notes = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_PayMethod = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_paymethod = new DevExpress.XtraEditors.Repository.RepositoryItemImageComboBox();
            this.col_DiscountRatio = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_DiscountValue = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Expenses = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colPaid = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colRemains = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Net = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_UserId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colStore = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CustId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Is_InTrans = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_SalesEmpId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_salesEmp = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.col_TaxValue = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_DeductTaxValue = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_AddTaxValue = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CrncId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repCrncy = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.col_CrncRate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDriverName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colVehicleNumber = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDestination = new DevExpress.XtraGrid.Columns.GridColumn();
            this.navBarControl1 = new DevExpress.XtraNavBar.NavBarControl();
            this.NBG_Tasks = new DevExpress.XtraNavBar.NavBarGroup();
            this.NBI_Customers = new DevExpress.XtraNavBar.NavBarItem();
            this.NBG_Reports = new DevExpress.XtraNavBar.NavBarGroup();
            this.NBI_ItemsNotSold = new DevExpress.XtraNavBar.NavBarItem();
            this.NBI_ItemsMaxSold = new DevExpress.XtraNavBar.NavBarItem();
            this.NBI_ItemsMinSold = new DevExpress.XtraNavBar.NavBarItem();
            this.btnClearSearch = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.dt2 = new DevExpress.XtraEditors.DateEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.dt1 = new DevExpress.XtraEditors.DateEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdCategory)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_InvoiceBook)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_paymethod)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_salesEmp)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repCrncy)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.navBarControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dt2.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dt2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dt1.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dt1.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnNew,
            this.barBtnOpen,
            this.barBtn_Help,
            this.barBtnClose,
            this.barBtnRefresh,
            this.barBtnPrint});
            this.barManager1.MaxItemId = 28;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(567, 147);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_Help),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnPrint),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnRefresh),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnOpen),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnNew),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtn_Help
            // 
            this.barBtn_Help.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtn_Help, "barBtn_Help");
            this.barBtn_Help.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtn_Help.Id = 2;
            this.barBtn_Help.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtn_Help.Name = "barBtn_Help";
            this.barBtn_Help.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtn_Help.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Help_ItemClick);
            // 
            // barBtnPrint
            // 
            this.barBtnPrint.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnPrint, "barBtnPrint");
            this.barBtnPrint.Glyph = global::Pharmacy.Properties.Resources.prnt;
            this.barBtnPrint.Id = 27;
            this.barBtnPrint.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.P));
            this.barBtnPrint.Name = "barBtnPrint";
            this.barBtnPrint.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnPrint.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnPrint_ItemClick);
            // 
            // barBtnRefresh
            // 
            this.barBtnRefresh.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnRefresh, "barBtnRefresh");
            this.barBtnRefresh.Glyph = global::Pharmacy.Properties.Resources.refresh;
            this.barBtnRefresh.Id = 26;
            this.barBtnRefresh.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.R));
            this.barBtnRefresh.Name = "barBtnRefresh";
            this.barBtnRefresh.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnRefresh.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Refresh_ItemClick);
            // 
            // barBtnOpen
            // 
            this.barBtnOpen.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnOpen, "barBtnOpen");
            this.barBtnOpen.Glyph = global::Pharmacy.Properties.Resources.open;
            this.barBtnOpen.Id = 1;
            this.barBtnOpen.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.O));
            this.barBtnOpen.Name = "barBtnOpen";
            this.barBtnOpen.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnOpen.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Open_ItemClick);
            // 
            // barBtnNew
            // 
            this.barBtnNew.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnNew, "barBtnNew");
            this.barBtnNew.Enabled = false;
            this.barBtnNew.Glyph = global::Pharmacy.Properties.Resources._new;
            this.barBtnNew.Id = 0;
            this.barBtnNew.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.N));
            this.barBtnNew.Name = "barBtnNew";
            this.barBtnNew.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnNew.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_New_ItemClick);
            // 
            // barBtnClose
            // 
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 25;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Close_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // grdCategory
            // 
            resources.ApplyResources(this.grdCategory, "grdCategory");
            this.grdCategory.ContextMenuStrip = this.contextMenuStrip1;
            this.grdCategory.MainView = this.gridView1;
            this.grdCategory.Name = "grdCategory";
            this.grdCategory.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.rep_paymethod,
            this.rep_salesEmp,
            this.rep_InvoiceBook,
            this.repCrncy});
            this.grdCategory.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            this.grdCategory.DoubleClick += new System.EventHandler(this.grdCategory_DoubleClick);
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.mi_OpenDealer});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            resources.ApplyResources(this.contextMenuStrip1, "contextMenuStrip1");
            // 
            // mi_OpenDealer
            // 
            this.mi_OpenDealer.Name = "mi_OpenDealer";
            resources.ApplyResources(this.mi_OpenDealer, "mi_OpenDealer");
            this.mi_OpenDealer.Click += new System.EventHandler(this.mi_OpenDealer_Click);
            // 
            // gridView1
            // 
            this.gridView1.Appearance.FooterPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.GroupPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.GroupPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.GroupRow.Options.UseTextOptions = true;
            this.gridView1.Appearance.GroupRow.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.Row.Options.UseTextOptions = true;
            this.gridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.AppearancePrint.FooterPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.BorderColor")));
            this.gridView1.AppearancePrint.FooterPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.ForeColor")));
            this.gridView1.AppearancePrint.FooterPanel.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.FooterPanel.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.GroupFooter.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.BorderColor")));
            this.gridView1.AppearancePrint.GroupFooter.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.ForeColor")));
            this.gridView1.AppearancePrint.GroupFooter.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.GroupFooter.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.GroupRow.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupRow.BorderColor")));
            this.gridView1.AppearancePrint.GroupRow.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupRow.ForeColor")));
            this.gridView1.AppearancePrint.GroupRow.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.GroupRow.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.BorderColor")));
            this.gridView1.AppearancePrint.HeaderPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.ForeColor")));
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.Lines.BackColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Lines.BackColor")));
            this.gridView1.AppearancePrint.Lines.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Lines.ForeColor")));
            this.gridView1.AppearancePrint.Lines.Options.UseBackColor = true;
            this.gridView1.AppearancePrint.Lines.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.Row.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Row.BorderColor")));
            this.gridView1.AppearancePrint.Row.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Row.ForeColor")));
            this.gridView1.AppearancePrint.Row.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.Row.Options.UseForeColor = true;
            this.gridView1.ColumnPanelRowHeight = 35;
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_SL_ReturnId,
            this.col_ReturnCode,
            this.col_InvoiceBookId,
            this.col_StoreId,
            this.col_CustomerId,
            this.col_ReturnDate,
            this.col_Notes,
            this.col_PayMethod,
            this.col_DiscountRatio,
            this.col_DiscountValue,
            this.col_Expenses,
            this.colPaid,
            this.colRemains,
            this.col_Net,
            this.col_UserId,
            this.colStore,
            this.col_CustId,
            this.col_Is_InTrans,
            this.col_SalesEmpId,
            this.col_TaxValue,
            this.col_DeductTaxValue,
            this.col_AddTaxValue,
            this.col_CrncId,
            this.col_CrncRate,
            this.colDriverName,
            this.colVehicleNumber,
            this.colDestination});
            this.gridView1.GridControl = this.grdCategory;
            resources.ApplyResources(this.gridView1, "gridView1");
            this.gridView1.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary"))), resources.GetString("gridView1.GroupSummary1"), this.col_DiscountValue, resources.GetString("gridView1.GroupSummary2")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary3"))), resources.GetString("gridView1.GroupSummary4"), this.col_Expenses, resources.GetString("gridView1.GroupSummary5")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary6"))), resources.GetString("gridView1.GroupSummary7"), this.col_Net, resources.GetString("gridView1.GroupSummary8")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary9"))), resources.GetString("gridView1.GroupSummary10"), this.col_TaxValue, resources.GetString("gridView1.GroupSummary11")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary12"))), resources.GetString("gridView1.GroupSummary13"), this.col_DeductTaxValue, resources.GetString("gridView1.GroupSummary14")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary15"))), resources.GetString("gridView1.GroupSummary16"), this.col_AddTaxValue, resources.GetString("gridView1.GroupSummary17"))});
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView1.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView1.OptionsView.GroupFooterShowMode = DevExpress.XtraGrid.Views.Grid.GroupFooterShowMode.VisibleAlways;
            this.gridView1.OptionsView.ShowAutoFilterRow = true;
            this.gridView1.OptionsView.ShowFooter = true;
            this.gridView1.OptionsView.ShowIndicator = false;
            // 
            // col_SL_ReturnId
            // 
            this.col_SL_ReturnId.AppearanceCell.Options.UseTextOptions = true;
            this.col_SL_ReturnId.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_SL_ReturnId.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_SL_ReturnId.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_SL_ReturnId.AppearanceHeader.Options.UseTextOptions = true;
            this.col_SL_ReturnId.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_SL_ReturnId.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_SL_ReturnId.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_SL_ReturnId, "col_SL_ReturnId");
            this.col_SL_ReturnId.FieldName = "SL_ReturnId";
            this.col_SL_ReturnId.Name = "col_SL_ReturnId";
            this.col_SL_ReturnId.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // col_ReturnCode
            // 
            this.col_ReturnCode.AppearanceCell.Options.UseTextOptions = true;
            this.col_ReturnCode.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_ReturnCode.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_ReturnCode.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_ReturnCode.AppearanceHeader.Options.UseTextOptions = true;
            this.col_ReturnCode.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_ReturnCode.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_ReturnCode.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_ReturnCode, "col_ReturnCode");
            this.col_ReturnCode.FieldName = "ReturnCode";
            this.col_ReturnCode.Name = "col_ReturnCode";
            this.col_ReturnCode.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // col_InvoiceBookId
            // 
            resources.ApplyResources(this.col_InvoiceBookId, "col_InvoiceBookId");
            this.col_InvoiceBookId.ColumnEdit = this.rep_InvoiceBook;
            this.col_InvoiceBookId.FieldName = "InvoiceBookId";
            this.col_InvoiceBookId.Name = "col_InvoiceBookId";
            // 
            // rep_InvoiceBook
            // 
            resources.ApplyResources(this.rep_InvoiceBook, "rep_InvoiceBook");
            this.rep_InvoiceBook.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_InvoiceBook.Buttons"))))});
            this.rep_InvoiceBook.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_InvoiceBook.Columns"), resources.GetString("rep_InvoiceBook.Columns1"), ((int)(resources.GetObject("rep_InvoiceBook.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("rep_InvoiceBook.Columns3"))), resources.GetString("rep_InvoiceBook.Columns4"), ((bool)(resources.GetObject("rep_InvoiceBook.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("rep_InvoiceBook.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_InvoiceBook.Columns7"), resources.GetString("rep_InvoiceBook.Columns8"))});
            this.rep_InvoiceBook.Name = "rep_InvoiceBook";
            // 
            // col_StoreId
            // 
            this.col_StoreId.AppearanceCell.Options.UseTextOptions = true;
            this.col_StoreId.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_StoreId.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_StoreId.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_StoreId.AppearanceHeader.Options.UseTextOptions = true;
            this.col_StoreId.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_StoreId.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_StoreId.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_StoreId, "col_StoreId");
            this.col_StoreId.FieldName = "StoreId";
            this.col_StoreId.Name = "col_StoreId";
            // 
            // col_CustomerId
            // 
            this.col_CustomerId.AppearanceCell.Options.UseTextOptions = true;
            this.col_CustomerId.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_CustomerId.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_CustomerId.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_CustomerId.AppearanceHeader.Options.UseTextOptions = true;
            this.col_CustomerId.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_CustomerId.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_CustomerId.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_CustomerId, "col_CustomerId");
            this.col_CustomerId.FieldName = "CustomerId";
            this.col_CustomerId.Name = "col_CustomerId";
            // 
            // col_ReturnDate
            // 
            this.col_ReturnDate.AppearanceCell.Options.UseTextOptions = true;
            this.col_ReturnDate.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_ReturnDate.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_ReturnDate.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_ReturnDate.AppearanceHeader.Options.UseTextOptions = true;
            this.col_ReturnDate.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_ReturnDate.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_ReturnDate.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_ReturnDate, "col_ReturnDate");
            this.col_ReturnDate.FieldName = "ReturnDate";
            this.col_ReturnDate.Name = "col_ReturnDate";
            this.col_ReturnDate.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // col_Notes
            // 
            this.col_Notes.AppearanceCell.Options.UseTextOptions = true;
            this.col_Notes.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_Notes.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Notes.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_Notes.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Notes.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_Notes.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Notes.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_Notes, "col_Notes");
            this.col_Notes.FieldName = "Notes";
            this.col_Notes.Name = "col_Notes";
            // 
            // col_PayMethod
            // 
            this.col_PayMethod.AppearanceCell.Options.UseTextOptions = true;
            this.col_PayMethod.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_PayMethod.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_PayMethod.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_PayMethod.AppearanceHeader.Options.UseTextOptions = true;
            this.col_PayMethod.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_PayMethod.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_PayMethod.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_PayMethod, "col_PayMethod");
            this.col_PayMethod.ColumnEdit = this.rep_paymethod;
            this.col_PayMethod.FieldName = "PayMethod";
            this.col_PayMethod.Name = "col_PayMethod";
            // 
            // rep_paymethod
            // 
            resources.ApplyResources(this.rep_paymethod, "rep_paymethod");
            this.rep_paymethod.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_paymethod.Buttons"))))});
            this.rep_paymethod.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("rep_paymethod.Items"), ((object)(resources.GetObject("rep_paymethod.Items1"))), ((int)(resources.GetObject("rep_paymethod.Items2")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("rep_paymethod.Items3"), ((object)(resources.GetObject("rep_paymethod.Items4"))), ((int)(resources.GetObject("rep_paymethod.Items5")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("rep_paymethod.Items6"), ((object)(resources.GetObject("rep_paymethod.Items7"))), ((int)(resources.GetObject("rep_paymethod.Items8"))))});
            this.rep_paymethod.Name = "rep_paymethod";
            // 
            // col_DiscountRatio
            // 
            this.col_DiscountRatio.AppearanceCell.Options.UseTextOptions = true;
            this.col_DiscountRatio.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_DiscountRatio.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_DiscountRatio.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_DiscountRatio.AppearanceHeader.Options.UseTextOptions = true;
            this.col_DiscountRatio.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_DiscountRatio.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_DiscountRatio.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_DiscountRatio, "col_DiscountRatio");
            this.col_DiscountRatio.FieldName = "DiscountRatio";
            this.col_DiscountRatio.Name = "col_DiscountRatio";
            // 
            // col_DiscountValue
            // 
            this.col_DiscountValue.AppearanceCell.Options.UseTextOptions = true;
            this.col_DiscountValue.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_DiscountValue.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_DiscountValue.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_DiscountValue.AppearanceHeader.Options.UseTextOptions = true;
            this.col_DiscountValue.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_DiscountValue.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_DiscountValue.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_DiscountValue, "col_DiscountValue");
            this.col_DiscountValue.DisplayFormat.FormatString = "n2";
            this.col_DiscountValue.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_DiscountValue.FieldName = "DiscountValue";
            this.col_DiscountValue.GroupFormat.FormatString = "n2";
            this.col_DiscountValue.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_DiscountValue.Name = "col_DiscountValue";
            this.col_DiscountValue.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_DiscountValue.Summary"))))});
            // 
            // col_Expenses
            // 
            this.col_Expenses.AppearanceCell.Options.UseTextOptions = true;
            this.col_Expenses.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_Expenses.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Expenses.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_Expenses.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Expenses.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_Expenses.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Expenses.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_Expenses, "col_Expenses");
            this.col_Expenses.DisplayFormat.FormatString = "n2";
            this.col_Expenses.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_Expenses.FieldName = "Expenses";
            this.col_Expenses.GroupFormat.FormatString = "n2";
            this.col_Expenses.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_Expenses.Name = "col_Expenses";
            this.col_Expenses.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_Expenses.Summary"))))});
            // 
            // colPaid
            // 
            this.colPaid.AppearanceCell.Options.UseTextOptions = true;
            this.colPaid.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.colPaid.AppearanceHeader.Options.UseTextOptions = true;
            this.colPaid.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.colPaid, "colPaid");
            this.colPaid.DisplayFormat.FormatString = "n2";
            this.colPaid.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colPaid.FieldName = "Paid";
            this.colPaid.GroupFormat.FormatString = "n2";
            this.colPaid.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colPaid.Name = "colPaid";
            this.colPaid.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("colPaid.Summary"))))});
            // 
            // colRemains
            // 
            this.colRemains.AppearanceCell.Options.UseTextOptions = true;
            this.colRemains.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.colRemains.AppearanceHeader.Options.UseTextOptions = true;
            this.colRemains.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.colRemains, "colRemains");
            this.colRemains.DisplayFormat.FormatString = "n2";
            this.colRemains.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colRemains.FieldName = "Remains";
            this.colRemains.GroupFormat.FormatString = "n2";
            this.colRemains.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colRemains.Name = "colRemains";
            this.colRemains.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("colRemains.Summary"))))});
            // 
            // col_Net
            // 
            this.col_Net.AppearanceCell.Options.UseTextOptions = true;
            this.col_Net.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_Net.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Net.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_Net.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Net.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_Net.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Net.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_Net, "col_Net");
            this.col_Net.DisplayFormat.FormatString = "n2";
            this.col_Net.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_Net.FieldName = "Net";
            this.col_Net.GroupFormat.FormatString = "n2";
            this.col_Net.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_Net.Name = "col_Net";
            this.col_Net.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_Net.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_Net.Summary"))))});
            // 
            // col_UserId
            // 
            this.col_UserId.AppearanceCell.Options.UseTextOptions = true;
            this.col_UserId.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_UserId.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_UserId.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_UserId.AppearanceHeader.Options.UseTextOptions = true;
            this.col_UserId.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_UserId.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_UserId.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_UserId, "col_UserId");
            this.col_UserId.FieldName = "UserId";
            this.col_UserId.Name = "col_UserId";
            // 
            // colStore
            // 
            this.colStore.AppearanceCell.Options.UseTextOptions = true;
            this.colStore.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.colStore.AppearanceHeader.Options.UseTextOptions = true;
            this.colStore.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.colStore, "colStore");
            this.colStore.FieldName = "Store";
            this.colStore.Name = "colStore";
            // 
            // col_CustId
            // 
            resources.ApplyResources(this.col_CustId, "col_CustId");
            this.col_CustId.FieldName = "CustId";
            this.col_CustId.Name = "col_CustId";
            // 
            // col_Is_InTrans
            // 
            this.col_Is_InTrans.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Is_InTrans.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Is_InTrans.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Is_InTrans.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_Is_InTrans, "col_Is_InTrans");
            this.col_Is_InTrans.FieldName = "Is_InTrans";
            this.col_Is_InTrans.Name = "col_Is_InTrans";
            // 
            // col_SalesEmpId
            // 
            this.col_SalesEmpId.AppearanceCell.Options.UseTextOptions = true;
            this.col_SalesEmpId.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_SalesEmpId.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_SalesEmpId.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_SalesEmpId.AppearanceHeader.Options.UseTextOptions = true;
            this.col_SalesEmpId.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_SalesEmpId.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_SalesEmpId.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_SalesEmpId, "col_SalesEmpId");
            this.col_SalesEmpId.ColumnEdit = this.rep_salesEmp;
            this.col_SalesEmpId.FieldName = "SalesEmpId";
            this.col_SalesEmpId.Name = "col_SalesEmpId";
            // 
            // rep_salesEmp
            // 
            resources.ApplyResources(this.rep_salesEmp, "rep_salesEmp");
            this.rep_salesEmp.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_salesEmp.Buttons"))))});
            this.rep_salesEmp.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_salesEmp.Columns"), resources.GetString("rep_salesEmp.Columns1")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_salesEmp.Columns2"), resources.GetString("rep_salesEmp.Columns3"), ((int)(resources.GetObject("rep_salesEmp.Columns4"))), ((DevExpress.Utils.FormatType)(resources.GetObject("rep_salesEmp.Columns5"))), resources.GetString("rep_salesEmp.Columns6"), ((bool)(resources.GetObject("rep_salesEmp.Columns7"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("rep_salesEmp.Columns8"))))});
            this.rep_salesEmp.DisplayMember = "EmpName";
            this.rep_salesEmp.Name = "rep_salesEmp";
            this.rep_salesEmp.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.rep_salesEmp.ValueMember = "EmpId";
            // 
            // col_TaxValue
            // 
            this.col_TaxValue.AppearanceCell.Options.UseTextOptions = true;
            this.col_TaxValue.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_TaxValue.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_TaxValue.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_TaxValue.AppearanceHeader.Options.UseTextOptions = true;
            this.col_TaxValue.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_TaxValue.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_TaxValue.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_TaxValue, "col_TaxValue");
            this.col_TaxValue.DisplayFormat.FormatString = "n2";
            this.col_TaxValue.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_TaxValue.FieldName = "TaxValue";
            this.col_TaxValue.GroupFormat.FormatString = "n2";
            this.col_TaxValue.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_TaxValue.Name = "col_TaxValue";
            this.col_TaxValue.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_TaxValue.Summary"))), resources.GetString("col_TaxValue.Summary1"), resources.GetString("col_TaxValue.Summary2"))});
            // 
            // col_DeductTaxValue
            // 
            this.col_DeductTaxValue.AppearanceCell.Options.UseTextOptions = true;
            this.col_DeductTaxValue.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_DeductTaxValue.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_DeductTaxValue.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_DeductTaxValue.AppearanceHeader.Options.UseTextOptions = true;
            this.col_DeductTaxValue.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_DeductTaxValue.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_DeductTaxValue.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_DeductTaxValue, "col_DeductTaxValue");
            this.col_DeductTaxValue.DisplayFormat.FormatString = "n2";
            this.col_DeductTaxValue.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_DeductTaxValue.FieldName = "DeductTaxValue";
            this.col_DeductTaxValue.GroupFormat.FormatString = "n2";
            this.col_DeductTaxValue.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_DeductTaxValue.Name = "col_DeductTaxValue";
            this.col_DeductTaxValue.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_DeductTaxValue.Summary"))), resources.GetString("col_DeductTaxValue.Summary1"), resources.GetString("col_DeductTaxValue.Summary2"))});
            // 
            // col_AddTaxValue
            // 
            this.col_AddTaxValue.AppearanceCell.Options.UseTextOptions = true;
            this.col_AddTaxValue.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_AddTaxValue.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_AddTaxValue.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_AddTaxValue.AppearanceHeader.Options.UseTextOptions = true;
            this.col_AddTaxValue.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_AddTaxValue.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_AddTaxValue.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_AddTaxValue, "col_AddTaxValue");
            this.col_AddTaxValue.DisplayFormat.FormatString = "n2";
            this.col_AddTaxValue.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_AddTaxValue.FieldName = "AddTaxValue";
            this.col_AddTaxValue.GroupFormat.FormatString = "n2";
            this.col_AddTaxValue.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_AddTaxValue.Name = "col_AddTaxValue";
            this.col_AddTaxValue.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_AddTaxValue.Summary"))), resources.GetString("col_AddTaxValue.Summary1"), resources.GetString("col_AddTaxValue.Summary2"))});
            // 
            // col_CrncId
            // 
            resources.ApplyResources(this.col_CrncId, "col_CrncId");
            this.col_CrncId.ColumnEdit = this.repCrncy;
            this.col_CrncId.FieldName = "CrncId";
            this.col_CrncId.Name = "col_CrncId";
            // 
            // repCrncy
            // 
            resources.ApplyResources(this.repCrncy, "repCrncy");
            this.repCrncy.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repCrncy.Buttons"))))});
            this.repCrncy.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("repCrncy.Columns"), resources.GetString("repCrncy.Columns1"), ((int)(resources.GetObject("repCrncy.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("repCrncy.Columns3"))), resources.GetString("repCrncy.Columns4"), ((bool)(resources.GetObject("repCrncy.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("repCrncy.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("repCrncy.Columns7"), resources.GetString("repCrncy.Columns8"))});
            this.repCrncy.Name = "repCrncy";
            // 
            // col_CrncRate
            // 
            resources.ApplyResources(this.col_CrncRate, "col_CrncRate");
            this.col_CrncRate.FieldName = "CrncRate";
            this.col_CrncRate.Name = "col_CrncRate";
            // 
            // colDriverName
            // 
            resources.ApplyResources(this.colDriverName, "colDriverName");
            this.colDriverName.FieldName = "DriverName";
            this.colDriverName.Name = "colDriverName";
            this.colDriverName.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // colVehicleNumber
            // 
            resources.ApplyResources(this.colVehicleNumber, "colVehicleNumber");
            this.colVehicleNumber.FieldName = "VehicleNumber";
            this.colVehicleNumber.Name = "colVehicleNumber";
            this.colVehicleNumber.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // colDestination
            // 
            resources.ApplyResources(this.colDestination, "colDestination");
            this.colDestination.FieldName = "Destination";
            this.colDestination.Name = "colDestination";
            this.colDestination.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // navBarControl1
            // 
            this.navBarControl1.ActiveGroup = this.NBG_Tasks;
            resources.ApplyResources(this.navBarControl1, "navBarControl1");
            this.navBarControl1.Appearance.Background.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.Background.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.Button.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.Button.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.ButtonDisabled.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.ButtonDisabled.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.ButtonHotTracked.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.ButtonHotTracked.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.ButtonPressed.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.ButtonPressed.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.GroupBackground.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.GroupBackground.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.GroupHeader.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.GroupHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.GroupHeaderActive.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.GroupHeaderActive.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.GroupHeaderHotTracked.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.GroupHeaderHotTracked.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.GroupHeaderPressed.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.GroupHeaderPressed.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.Hint.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.Hint.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.Item.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.Item.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.ItemActive.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.ItemActive.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.ItemDisabled.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.ItemDisabled.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.ItemHotTracked.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.ItemHotTracked.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.ItemPressed.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.ItemPressed.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.LinkDropTarget.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.LinkDropTarget.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.Appearance.NavigationPaneHeader.Options.UseTextOptions = true;
            this.navBarControl1.Appearance.NavigationPaneHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.navBarControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.navBarControl1.Groups.AddRange(new DevExpress.XtraNavBar.NavBarGroup[] {
            this.NBG_Tasks,
            this.NBG_Reports});
            this.navBarControl1.Items.AddRange(new DevExpress.XtraNavBar.NavBarItem[] {
            this.NBI_Customers,
            this.NBI_ItemsNotSold,
            this.NBI_ItemsMaxSold,
            this.NBI_ItemsMinSold});
            this.navBarControl1.Name = "navBarControl1";
            this.navBarControl1.OptionsNavPane.ExpandedWidth = ((int)(resources.GetObject("resource.ExpandedWidth")));
            // 
            // NBG_Tasks
            // 
            resources.ApplyResources(this.NBG_Tasks, "NBG_Tasks");
            this.NBG_Tasks.Expanded = true;
            this.NBG_Tasks.GroupStyle = DevExpress.XtraNavBar.NavBarGroupStyle.LargeIconsText;
            this.NBG_Tasks.ItemLinks.AddRange(new DevExpress.XtraNavBar.NavBarItemLink[] {
            new DevExpress.XtraNavBar.NavBarItemLink(this.NBI_Customers)});
            this.NBG_Tasks.Name = "NBG_Tasks";
            // 
            // NBI_Customers
            // 
            resources.ApplyResources(this.NBI_Customers, "NBI_Customers");
            this.NBI_Customers.Name = "NBI_Customers";
            this.NBI_Customers.LinkClicked += new DevExpress.XtraNavBar.NavBarLinkEventHandler(this.NBI_LinkClicked);
            // 
            // NBG_Reports
            // 
            resources.ApplyResources(this.NBG_Reports, "NBG_Reports");
            this.NBG_Reports.Expanded = true;
            this.NBG_Reports.GroupStyle = DevExpress.XtraNavBar.NavBarGroupStyle.LargeIconsText;
            this.NBG_Reports.ItemLinks.AddRange(new DevExpress.XtraNavBar.NavBarItemLink[] {
            new DevExpress.XtraNavBar.NavBarItemLink(this.NBI_ItemsNotSold),
            new DevExpress.XtraNavBar.NavBarItemLink(this.NBI_ItemsMaxSold),
            new DevExpress.XtraNavBar.NavBarItemLink(this.NBI_ItemsMinSold)});
            this.NBG_Reports.Name = "NBG_Reports";
            // 
            // NBI_ItemsNotSold
            // 
            resources.ApplyResources(this.NBI_ItemsNotSold, "NBI_ItemsNotSold");
            this.NBI_ItemsNotSold.Name = "NBI_ItemsNotSold";
            this.NBI_ItemsNotSold.LinkClicked += new DevExpress.XtraNavBar.NavBarLinkEventHandler(this.NBI_LinkClicked);
            // 
            // NBI_ItemsMaxSold
            // 
            resources.ApplyResources(this.NBI_ItemsMaxSold, "NBI_ItemsMaxSold");
            this.NBI_ItemsMaxSold.Name = "NBI_ItemsMaxSold";
            this.NBI_ItemsMaxSold.LinkClicked += new DevExpress.XtraNavBar.NavBarLinkEventHandler(this.NBI_LinkClicked);
            // 
            // NBI_ItemsMinSold
            // 
            resources.ApplyResources(this.NBI_ItemsMinSold, "NBI_ItemsMinSold");
            this.NBI_ItemsMinSold.Name = "NBI_ItemsMinSold";
            this.NBI_ItemsMinSold.LinkClicked += new DevExpress.XtraNavBar.NavBarLinkEventHandler(this.NBI_LinkClicked);
            // 
            // btnClearSearch
            // 
            resources.ApplyResources(this.btnClearSearch, "btnClearSearch");
            this.btnClearSearch.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Flat;
            this.btnClearSearch.Name = "btnClearSearch";
            this.btnClearSearch.TabStop = false;
            this.btnClearSearch.Click += new System.EventHandler(this.btnClearSearch_Click);
            // 
            // labelControl3
            // 
            this.labelControl3.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl3.Appearance.Font")));
            this.labelControl3.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("labelControl3.Appearance.ForeColor")));
            resources.ApplyResources(this.labelControl3, "labelControl3");
            this.labelControl3.Name = "labelControl3";
            // 
            // dt2
            // 
            resources.ApplyResources(this.dt2, "dt2");
            this.dt2.EnterMoveNextControl = true;
            this.dt2.Name = "dt2";
            this.dt2.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.dt2.Properties.Appearance.Options.UseTextOptions = true;
            this.dt2.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.dt2.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("dt2.Properties.Buttons"))))});
            this.dt2.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dt2.EditValueChanged += new System.EventHandler(this.dt1_EditValueChanged);
            // 
            // labelControl2
            // 
            resources.ApplyResources(this.labelControl2, "labelControl2");
            this.labelControl2.Name = "labelControl2";
            // 
            // dt1
            // 
            resources.ApplyResources(this.dt1, "dt1");
            this.dt1.EnterMoveNextControl = true;
            this.dt1.Name = "dt1";
            this.dt1.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.dt1.Properties.Appearance.Options.UseTextOptions = true;
            this.dt1.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.dt1.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("dt1.Properties.Buttons"))))});
            this.dt1.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dt1.EditValueChanged += new System.EventHandler(this.dt1_EditValueChanged);
            // 
            // labelControl1
            // 
            resources.ApplyResources(this.labelControl1, "labelControl1");
            this.labelControl1.Name = "labelControl1";
            // 
            // gridColumn1
            // 
            this.gridColumn1.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn1.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridColumn1.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn1.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn1.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn1.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridColumn1.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn1.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn1, "gridColumn1");
            this.gridColumn1.ColumnEdit = this.rep_salesEmp;
            this.gridColumn1.FieldName = "SalesEmpId";
            this.gridColumn1.Name = "gridColumn1";
            // 
            // frm_SL_ReturnArchiveList
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.btnClearSearch);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.dt2);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.dt1);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.navBarControl1);
            this.Controls.Add(this.grdCategory);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.KeyPreview = true;
            this.Name = "frm_SL_ReturnArchiveList";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_SL_ReturnList_FormClosing);
            this.Load += new System.EventHandler(this.frm_SL_ReturnList_Load);
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.frm_SL_ReturnList_KeyUp);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdCategory)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_InvoiceBook)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_paymethod)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_salesEmp)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repCrncy)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.navBarControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dt2.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dt2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dt1.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dt1.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnNew;
        private DevExpress.XtraBars.BarButtonItem barBtnOpen;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtn_Help;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraGrid.GridControl grdCategory;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraBars.BarButtonItem barBtnRefresh;
        private DevExpress.XtraGrid.Columns.GridColumn col_SL_ReturnId;
        private DevExpress.XtraGrid.Columns.GridColumn col_ReturnCode;
        private DevExpress.XtraGrid.Columns.GridColumn col_StoreId;
        private DevExpress.XtraGrid.Columns.GridColumn col_CustomerId;
        private DevExpress.XtraGrid.Columns.GridColumn col_ReturnDate;
        private DevExpress.XtraGrid.Columns.GridColumn col_Notes;
        private DevExpress.XtraGrid.Columns.GridColumn col_Expenses;
        private DevExpress.XtraGrid.Columns.GridColumn col_DiscountRatio;
        private DevExpress.XtraGrid.Columns.GridColumn col_PayMethod;
        private DevExpress.XtraGrid.Columns.GridColumn col_DiscountValue;
        private DevExpress.XtraGrid.Columns.GridColumn col_Net;
        private DevExpress.XtraGrid.Columns.GridColumn col_UserId;
        private DevExpress.XtraEditors.Repository.RepositoryItemImageComboBox rep_paymethod;
        private DevExpress.XtraBars.BarButtonItem barBtnPrint;
        private DevExpress.XtraNavBar.NavBarControl navBarControl1;
        private DevExpress.XtraNavBar.NavBarGroup NBG_Tasks;
        private DevExpress.XtraNavBar.NavBarItem NBI_Customers;
        private DevExpress.XtraNavBar.NavBarGroup NBG_Reports;
        private DevExpress.XtraNavBar.NavBarItem NBI_ItemsNotSold;
        private DevExpress.XtraNavBar.NavBarItem NBI_ItemsMaxSold;
        private DevExpress.XtraNavBar.NavBarItem NBI_ItemsMinSold;
        private DevExpress.XtraGrid.Columns.GridColumn colStore;
        private DevExpress.XtraEditors.SimpleButton btnClearSearch;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.DateEdit dt2;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.DateEdit dt1;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraGrid.Columns.GridColumn colPaid;
        private DevExpress.XtraGrid.Columns.GridColumn colRemains;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem mi_OpenDealer;
        private DevExpress.XtraGrid.Columns.GridColumn col_CustId;
        private DevExpress.XtraGrid.Columns.GridColumn col_Is_InTrans;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit rep_salesEmp;
        private DevExpress.XtraGrid.Columns.GridColumn col_SalesEmpId;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn col_AddTaxValue;
        private DevExpress.XtraGrid.Columns.GridColumn col_DeductTaxValue;
        private DevExpress.XtraGrid.Columns.GridColumn col_TaxValue;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit rep_InvoiceBook;
        private DevExpress.XtraGrid.Columns.GridColumn col_InvoiceBookId;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repCrncy;
        private DevExpress.XtraGrid.Columns.GridColumn col_CrncId;
        private DevExpress.XtraGrid.Columns.GridColumn col_CrncRate;
        private DevExpress.XtraGrid.Columns.GridColumn colDriverName;
        private DevExpress.XtraGrid.Columns.GridColumn colVehicleNumber;
        private DevExpress.XtraGrid.Columns.GridColumn colDestination;
    }
}