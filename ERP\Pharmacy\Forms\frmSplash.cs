﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Reflection;
using Microsoft.Win32;
using System.Xml;

namespace Pharmacy.Forms
{
    public partial class frmSplash : Form
    {
        public frmSplash()
        {
            InitializeComponent();

            #region Write file to set installation path
            XmlWriterSettings settings = new XmlWriterSettings();
            settings.Indent = true;

            XmlWriter writer = null;
            try
            {
                string Folder_path = Environment.GetFolderPath(Environment.SpecialFolder.Personal) + "\\GlobalERP";
                if (!System.IO.Directory.Exists(Folder_path))
                    System.IO.Directory.CreateDirectory(Folder_path);

                writer = XmlWriter.Create(
                    Environment.GetFolderPath(Environment.SpecialFolder.Personal) + "\\" + "GlobalERP" + "\\" + "ERP_Path.xml", settings);

                writer.WriteStartDocument();
                writer.WriteComment("This file is generated by Global Grid ERP System");

                writer.WriteStartElement("Settings");

                writer.WriteElementString("ERP_Path", @Application.ExecutablePath);

                writer.WriteEndElement();
                writer.WriteEndDocument();
                writer.Flush();

            }
            catch
            {

            }
            finally
            {
                writer.Close();
            }

            #endregion

            if (Program.ModelIsERP == false)
                this.BackgroundImage = Pharmacy.Properties.Resources.Splash2;

            this.lblVersion.Text = "Version " + AssemblyVersion;
            FadingSplash();
        }

        public string AssemblyVersion
        {
            get
            {
                return Assembly.GetExecutingAssembly().GetName().Version.Major.ToString() + "." +
                    Assembly.GetExecutingAssembly().GetName().Version.Minor.ToString() + "." +
                 Assembly.GetExecutingAssembly().GetName().Version.Build.ToString() + "." +
                 Assembly.GetExecutingAssembly().GetName().Version.Revision.ToString();

            }
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            //this.Opacity += 0.045;
            this.Opacity += 0.005;
            this.Show();
            this.Update();
            if (this.Opacity >= 0.95)
            {
                this.Opacity = 1;
                timer1.Enabled = false;
                Thread.Sleep(700);
                this.Hide();
                frmLogin frm = new frmLogin();
                frm.ShowDialog();
                this.Close();
            }
        }

        public void FadingSplash()
        {
            this.Opacity = 0.15;
            timer1.Enabled = true;
            timer1.Tick += new EventHandler(timer1_Tick);
        }

        private void frmSplash_Click(object sender, EventArgs e)
        {
            EscapeForm();
        }

        private void frmSplash_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyData == Keys.Escape)
            {
                EscapeForm();
            }
        }

        private void EscapeForm()
        {
            ////write exe to registry to be used by our update application
            //RegistryKey key = Registry.LocalMachine.OpenSubKey("Software", true);
            //key.CreateSubKey(Application.ProductName);
            //key = key.OpenSubKey(Application.ProductName, true);
            //key.SetValue("InstallPath", Application.ExecutablePath);

            timer1.Stop();
            this.Hide();
            frmLogin frm = new frmLogin();
            frm.ShowDialog();
            this.Close();
        }
    }
}
