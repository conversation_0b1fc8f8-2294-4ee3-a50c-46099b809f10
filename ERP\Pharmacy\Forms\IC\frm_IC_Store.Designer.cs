﻿namespace Pharmacy.Forms
{
    partial class frm_IC_Store
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_IC_Store));
            this.labelControl15 = new DevExpress.XtraEditors.LabelControl();
            this.txtTel = new DevExpress.XtraEditors.TextEdit();
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtnHelp = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnNew = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnDelete = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnSave = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnList = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.txtAddress = new DevExpress.XtraEditors.TextEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.txtStoreNameAr = new DevExpress.XtraEditors.TextEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.txtStoreNameEn = new DevExpress.XtraEditors.TextEdit();
            this.btnNext = new DevExpress.XtraEditors.SimpleButton();
            this.btnPrev = new DevExpress.XtraEditors.SimpleButton();
            this.txt_StoreCode = new DevExpress.XtraEditors.TextEdit();
            this.cb_CostMethod = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.txt_Manager = new DevExpress.XtraEditors.TextEdit();
            this.lkpStore = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.lkp_PerPetualCostOfSoldGoods = new DevExpress.XtraEditors.LookUpEdit();
            this.lblPurchaseDiscount = new DevExpress.XtraEditors.LabelControl();
            this.lkp_PerPetualPurchaseDiscountAcc = new DevExpress.XtraEditors.LookUpEdit();
            this.lkp_PerPetualSalesDiscount = new DevExpress.XtraEditors.LookUpEdit();
            this.lblSalesDiscount = new DevExpress.XtraEditors.LabelControl();
            this.lblInventory = new DevExpress.XtraEditors.LabelControl();
            this.lkp_PerPetualInventoryAcc = new DevExpress.XtraEditors.LookUpEdit();
            this.lblSalesReturn = new DevExpress.XtraEditors.LabelControl();
            this.lkp_PerPetualSalesReturnAcc = new DevExpress.XtraEditors.LookUpEdit();
            this.lblSales = new DevExpress.XtraEditors.LabelControl();
            this.lkp_PerPetualSalesAcc = new DevExpress.XtraEditors.LookUpEdit();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.tab_PeriodicAcc = new DevExpress.XtraTab.XtraTabPage();
            this.chk_autoCreateAccs = new DevExpress.XtraEditors.CheckEdit();
            this.lkp_PeriodicCloseInvAcc = new DevExpress.XtraEditors.LookUpEdit();
            this.lblPurchasesReturnAccount = new DevExpress.XtraEditors.LabelControl();
            this.lkp_PeriodicPrReturnAcc = new DevExpress.XtraEditors.LookUpEdit();
            this.lblCloseInventoryAccount = new DevExpress.XtraEditors.LabelControl();
            this.lblPurchasesAccount = new DevExpress.XtraEditors.LabelControl();
            this.lkp_PeriodicSalesAcc = new DevExpress.XtraEditors.LookUpEdit();
            this.lkp_PeriodicPurchaseAcc = new DevExpress.XtraEditors.LookUpEdit();
            this.lblSalesAccount = new DevExpress.XtraEditors.LabelControl();
            this.labelControl12 = new DevExpress.XtraEditors.LabelControl();
            this.lkp_PeriodicSalesReturnAcc = new DevExpress.XtraEditors.LookUpEdit();
            this.lkp_PeriodicPrDiscAcc = new DevExpress.XtraEditors.LookUpEdit();
            this.lblSalesReturnAccount = new DevExpress.XtraEditors.LabelControl();
            this.lkp_PeriodicSalesDiscAcc = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl14 = new DevExpress.XtraEditors.LabelControl();
            this.lkp_PeriodicOpenInvAcc = new DevExpress.XtraEditors.LookUpEdit();
            this.lblOpenInventoryAccount = new DevExpress.XtraEditors.LabelControl();
            this.tab_PerpetualAcc = new DevExpress.XtraTab.XtraTabPage();
            this.lbl_PriceList = new DevExpress.XtraEditors.LabelControl();
            this.lkpPriceLevel = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.Mobile = new DevExpress.XtraEditors.TextEdit();
            this.IsStopped = new DevExpress.XtraEditors.CheckEdit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTel.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAddress.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtStoreNameAr.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtStoreNameEn.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_StoreCode.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cb_CostMethod.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Manager.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpStore.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_PerPetualCostOfSoldGoods.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_PerPetualPurchaseDiscountAcc.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_PerPetualSalesDiscount.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_PerPetualInventoryAcc.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_PerPetualSalesReturnAcc.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_PerPetualSalesAcc.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.tab_PeriodicAcc.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chk_autoCreateAccs.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_PeriodicCloseInvAcc.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_PeriodicPrReturnAcc.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_PeriodicSalesAcc.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_PeriodicPurchaseAcc.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_PeriodicSalesReturnAcc.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_PeriodicPrDiscAcc.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_PeriodicSalesDiscAcc.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_PeriodicOpenInvAcc.Properties)).BeginInit();
            this.tab_PerpetualAcc.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lkpPriceLevel.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.Mobile.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.IsStopped.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // labelControl15
            // 
            resources.ApplyResources(this.labelControl15, "labelControl15");
            this.labelControl15.Name = "labelControl15";
            // 
            // txtTel
            // 
            resources.ApplyResources(this.txtTel, "txtTel");
            this.txtTel.EnterMoveNextControl = true;
            this.txtTel.Name = "txtTel";
            this.txtTel.Properties.AccessibleDescription = resources.GetString("txtTel.Properties.AccessibleDescription");
            this.txtTel.Properties.AccessibleName = resources.GetString("txtTel.Properties.AccessibleName");
            this.txtTel.Properties.AutoHeight = ((bool)(resources.GetObject("txtTel.Properties.AutoHeight")));
            this.txtTel.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtTel.Properties.Mask.AutoComplete")));
            this.txtTel.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtTel.Properties.Mask.BeepOnError")));
            this.txtTel.Properties.Mask.EditMask = resources.GetString("txtTel.Properties.Mask.EditMask");
            this.txtTel.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtTel.Properties.Mask.IgnoreMaskBlank")));
            this.txtTel.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtTel.Properties.Mask.MaskType")));
            this.txtTel.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtTel.Properties.Mask.PlaceHolder")));
            this.txtTel.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtTel.Properties.Mask.SaveLiteral")));
            this.txtTel.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtTel.Properties.Mask.ShowPlaceHolders")));
            this.txtTel.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtTel.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtTel.Properties.MaxLength = 50;
            this.txtTel.Properties.NullValuePrompt = resources.GetString("txtTel.Properties.NullValuePrompt");
            this.txtTel.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtTel.Properties.NullValuePromptShowForEmptyValue")));
            this.txtTel.Modified += new System.EventHandler(this.txt_StoreCode_Modified);
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnSave,
            this.barBtnHelp,
            this.barBtnList,
            this.barBtnClose,
            this.barBtnNew,
            this.barBtnDelete});
            this.barManager1.MaxItemId = 30;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(377, 152);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnHelp),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnNew),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnDelete),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnSave),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnList),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtnHelp
            // 
            resources.ApplyResources(this.barBtnHelp, "barBtnHelp");
            this.barBtnHelp.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnHelp.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtnHelp.Id = 2;
            this.barBtnHelp.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtnHelp.Name = "barBtnHelp";
            this.barBtnHelp.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnHelp.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnHelp_ItemClick);
            // 
            // barBtnNew
            // 
            resources.ApplyResources(this.barBtnNew, "barBtnNew");
            this.barBtnNew.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnNew.Glyph = global::Pharmacy.Properties.Resources._new;
            this.barBtnNew.Id = 27;
            this.barBtnNew.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.N));
            this.barBtnNew.Name = "barBtnNew";
            this.barBtnNew.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnNew.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnNew_ItemClick);
            // 
            // barBtnDelete
            // 
            resources.ApplyResources(this.barBtnDelete, "barBtnDelete");
            this.barBtnDelete.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnDelete.Glyph = global::Pharmacy.Properties.Resources.del;
            this.barBtnDelete.Id = 28;
            this.barBtnDelete.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.D));
            this.barBtnDelete.Name = "barBtnDelete";
            this.barBtnDelete.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnDelete.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Delete_ItemClick);
            // 
            // barBtnSave
            // 
            resources.ApplyResources(this.barBtnSave, "barBtnSave");
            this.barBtnSave.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnSave.Glyph = global::Pharmacy.Properties.Resources.sve;
            this.barBtnSave.Id = 0;
            this.barBtnSave.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.S));
            this.barBtnSave.Name = "barBtnSave";
            this.barBtnSave.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnSave.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Save_ItemClick);
            // 
            // barBtnList
            // 
            resources.ApplyResources(this.barBtnList, "barBtnList");
            this.barBtnList.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnList.Glyph = global::Pharmacy.Properties.Resources.list32;
            this.barBtnList.Id = 25;
            this.barBtnList.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.T));
            this.barBtnList.Name = "barBtnList";
            this.barBtnList.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnList.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_List_ItemClick);
            // 
            // barBtnClose
            // 
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 26;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnClose_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlTop.Appearance.FontSizeDelta")));
            this.barDockControlTop.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlTop.Appearance.FontStyleDelta")));
            this.barDockControlTop.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlTop.Appearance.GradientMode")));
            this.barDockControlTop.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlTop.Appearance.Image")));
            this.barDockControlTop.CausesValidation = false;
            // 
            // barDockControlBottom
            // 
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlBottom.Appearance.FontSizeDelta")));
            this.barDockControlBottom.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlBottom.Appearance.FontStyleDelta")));
            this.barDockControlBottom.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlBottom.Appearance.GradientMode")));
            this.barDockControlBottom.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlBottom.Appearance.Image")));
            this.barDockControlBottom.CausesValidation = false;
            // 
            // barDockControlLeft
            // 
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlLeft.Appearance.FontSizeDelta")));
            this.barDockControlLeft.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlLeft.Appearance.FontStyleDelta")));
            this.barDockControlLeft.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlLeft.Appearance.GradientMode")));
            this.barDockControlLeft.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlLeft.Appearance.Image")));
            this.barDockControlLeft.CausesValidation = false;
            // 
            // barDockControlRight
            // 
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlRight.Appearance.FontSizeDelta")));
            this.barDockControlRight.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlRight.Appearance.FontStyleDelta")));
            this.barDockControlRight.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlRight.Appearance.GradientMode")));
            this.barDockControlRight.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlRight.Appearance.Image")));
            this.barDockControlRight.CausesValidation = false;
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemTextEdit1.Mask.AutoComplete")));
            this.repositoryItemTextEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.BeepOnError")));
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemTextEdit1.Mask.MaskType")));
            this.repositoryItemTextEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemTextEdit1.Mask.PlaceHolder")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // labelControl2
            // 
            resources.ApplyResources(this.labelControl2, "labelControl2");
            this.labelControl2.Name = "labelControl2";
            // 
            // txtAddress
            // 
            resources.ApplyResources(this.txtAddress, "txtAddress");
            this.txtAddress.EnterMoveNextControl = true;
            this.txtAddress.Name = "txtAddress";
            this.txtAddress.Properties.AccessibleDescription = resources.GetString("txtAddress.Properties.AccessibleDescription");
            this.txtAddress.Properties.AccessibleName = resources.GetString("txtAddress.Properties.AccessibleName");
            this.txtAddress.Properties.AutoHeight = ((bool)(resources.GetObject("txtAddress.Properties.AutoHeight")));
            this.txtAddress.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtAddress.Properties.Mask.AutoComplete")));
            this.txtAddress.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtAddress.Properties.Mask.BeepOnError")));
            this.txtAddress.Properties.Mask.EditMask = resources.GetString("txtAddress.Properties.Mask.EditMask");
            this.txtAddress.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtAddress.Properties.Mask.IgnoreMaskBlank")));
            this.txtAddress.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtAddress.Properties.Mask.MaskType")));
            this.txtAddress.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtAddress.Properties.Mask.PlaceHolder")));
            this.txtAddress.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtAddress.Properties.Mask.SaveLiteral")));
            this.txtAddress.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtAddress.Properties.Mask.ShowPlaceHolders")));
            this.txtAddress.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtAddress.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtAddress.Properties.MaxLength = 50;
            this.txtAddress.Properties.NullValuePrompt = resources.GetString("txtAddress.Properties.NullValuePrompt");
            this.txtAddress.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtAddress.Properties.NullValuePromptShowForEmptyValue")));
            this.txtAddress.Modified += new System.EventHandler(this.txt_StoreCode_Modified);
            // 
            // labelControl4
            // 
            resources.ApplyResources(this.labelControl4, "labelControl4");
            this.labelControl4.Name = "labelControl4";
            // 
            // txtStoreNameAr
            // 
            resources.ApplyResources(this.txtStoreNameAr, "txtStoreNameAr");
            this.txtStoreNameAr.EnterMoveNextControl = true;
            this.txtStoreNameAr.Name = "txtStoreNameAr";
            this.txtStoreNameAr.Properties.AccessibleDescription = resources.GetString("txtStoreNameAr.Properties.AccessibleDescription");
            this.txtStoreNameAr.Properties.AccessibleName = resources.GetString("txtStoreNameAr.Properties.AccessibleName");
            this.txtStoreNameAr.Properties.AutoHeight = ((bool)(resources.GetObject("txtStoreNameAr.Properties.AutoHeight")));
            this.txtStoreNameAr.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtStoreNameAr.Properties.Mask.AutoComplete")));
            this.txtStoreNameAr.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtStoreNameAr.Properties.Mask.BeepOnError")));
            this.txtStoreNameAr.Properties.Mask.EditMask = resources.GetString("txtStoreNameAr.Properties.Mask.EditMask");
            this.txtStoreNameAr.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtStoreNameAr.Properties.Mask.IgnoreMaskBlank")));
            this.txtStoreNameAr.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtStoreNameAr.Properties.Mask.MaskType")));
            this.txtStoreNameAr.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtStoreNameAr.Properties.Mask.PlaceHolder")));
            this.txtStoreNameAr.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtStoreNameAr.Properties.Mask.SaveLiteral")));
            this.txtStoreNameAr.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtStoreNameAr.Properties.Mask.ShowPlaceHolders")));
            this.txtStoreNameAr.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtStoreNameAr.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtStoreNameAr.Properties.MaxLength = 36;
            this.txtStoreNameAr.Properties.NullValuePrompt = resources.GetString("txtStoreNameAr.Properties.NullValuePrompt");
            this.txtStoreNameAr.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtStoreNameAr.Properties.NullValuePromptShowForEmptyValue")));
            this.txtStoreNameAr.Modified += new System.EventHandler(this.txt_StoreCode_Modified);
            // 
            // labelControl3
            // 
            resources.ApplyResources(this.labelControl3, "labelControl3");
            this.labelControl3.Name = "labelControl3";
            // 
            // txtStoreNameEn
            // 
            resources.ApplyResources(this.txtStoreNameEn, "txtStoreNameEn");
            this.txtStoreNameEn.EnterMoveNextControl = true;
            this.txtStoreNameEn.Name = "txtStoreNameEn";
            this.txtStoreNameEn.Properties.AccessibleDescription = resources.GetString("txtStoreNameEn.Properties.AccessibleDescription");
            this.txtStoreNameEn.Properties.AccessibleName = resources.GetString("txtStoreNameEn.Properties.AccessibleName");
            this.txtStoreNameEn.Properties.AutoHeight = ((bool)(resources.GetObject("txtStoreNameEn.Properties.AutoHeight")));
            this.txtStoreNameEn.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtStoreNameEn.Properties.Mask.AutoComplete")));
            this.txtStoreNameEn.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtStoreNameEn.Properties.Mask.BeepOnError")));
            this.txtStoreNameEn.Properties.Mask.EditMask = resources.GetString("txtStoreNameEn.Properties.Mask.EditMask");
            this.txtStoreNameEn.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtStoreNameEn.Properties.Mask.IgnoreMaskBlank")));
            this.txtStoreNameEn.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtStoreNameEn.Properties.Mask.MaskType")));
            this.txtStoreNameEn.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtStoreNameEn.Properties.Mask.PlaceHolder")));
            this.txtStoreNameEn.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtStoreNameEn.Properties.Mask.SaveLiteral")));
            this.txtStoreNameEn.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtStoreNameEn.Properties.Mask.ShowPlaceHolders")));
            this.txtStoreNameEn.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtStoreNameEn.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtStoreNameEn.Properties.MaxLength = 36;
            this.txtStoreNameEn.Properties.NullValuePrompt = resources.GetString("txtStoreNameEn.Properties.NullValuePrompt");
            this.txtStoreNameEn.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtStoreNameEn.Properties.NullValuePromptShowForEmptyValue")));
            this.txtStoreNameEn.Modified += new System.EventHandler(this.txt_StoreCode_Modified);
            // 
            // btnNext
            // 
            resources.ApplyResources(this.btnNext, "btnNext");
            this.btnNext.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnNext.Image = global::Pharmacy.Properties.Resources.nxt;
            this.btnNext.Name = "btnNext";
            this.btnNext.TabStop = false;
            this.btnNext.Click += new System.EventHandler(this.btnNext_Click);
            // 
            // btnPrev
            // 
            resources.ApplyResources(this.btnPrev, "btnPrev");
            this.btnPrev.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnPrev.Image = global::Pharmacy.Properties.Resources.prev32;
            this.btnPrev.Name = "btnPrev";
            this.btnPrev.TabStop = false;
            this.btnPrev.Click += new System.EventHandler(this.btnPrev_Click);
            // 
            // txt_StoreCode
            // 
            resources.ApplyResources(this.txt_StoreCode, "txt_StoreCode");
            this.txt_StoreCode.EnterMoveNextControl = true;
            this.txt_StoreCode.Name = "txt_StoreCode";
            this.txt_StoreCode.Properties.AccessibleDescription = resources.GetString("txt_StoreCode.Properties.AccessibleDescription");
            this.txt_StoreCode.Properties.AccessibleName = resources.GetString("txt_StoreCode.Properties.AccessibleName");
            this.txt_StoreCode.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txt_StoreCode.Properties.Appearance.FontSizeDelta")));
            this.txt_StoreCode.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txt_StoreCode.Properties.Appearance.FontStyleDelta")));
            this.txt_StoreCode.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txt_StoreCode.Properties.Appearance.GradientMode")));
            this.txt_StoreCode.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txt_StoreCode.Properties.Appearance.Image")));
            this.txt_StoreCode.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_StoreCode.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_StoreCode.Properties.AutoHeight = ((bool)(resources.GetObject("txt_StoreCode.Properties.AutoHeight")));
            this.txt_StoreCode.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_StoreCode.Properties.Mask.AutoComplete")));
            this.txt_StoreCode.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_StoreCode.Properties.Mask.BeepOnError")));
            this.txt_StoreCode.Properties.Mask.EditMask = resources.GetString("txt_StoreCode.Properties.Mask.EditMask");
            this.txt_StoreCode.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_StoreCode.Properties.Mask.IgnoreMaskBlank")));
            this.txt_StoreCode.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_StoreCode.Properties.Mask.MaskType")));
            this.txt_StoreCode.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_StoreCode.Properties.Mask.PlaceHolder")));
            this.txt_StoreCode.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_StoreCode.Properties.Mask.SaveLiteral")));
            this.txt_StoreCode.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_StoreCode.Properties.Mask.ShowPlaceHolders")));
            this.txt_StoreCode.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_StoreCode.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_StoreCode.Properties.NullValuePrompt = resources.GetString("txt_StoreCode.Properties.NullValuePrompt");
            this.txt_StoreCode.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_StoreCode.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_StoreCode.Modified += new System.EventHandler(this.txt_StoreCode_Modified);
            // 
            // cb_CostMethod
            // 
            resources.ApplyResources(this.cb_CostMethod, "cb_CostMethod");
            this.cb_CostMethod.EnterMoveNextControl = true;
            this.cb_CostMethod.MenuManager = this.barManager1;
            this.cb_CostMethod.Name = "cb_CostMethod";
            this.cb_CostMethod.Properties.AccessibleDescription = resources.GetString("cb_CostMethod.Properties.AccessibleDescription");
            this.cb_CostMethod.Properties.AccessibleName = resources.GetString("cb_CostMethod.Properties.AccessibleName");
            this.cb_CostMethod.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("cb_CostMethod.Properties.Appearance.FontSizeDelta")));
            this.cb_CostMethod.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("cb_CostMethod.Properties.Appearance.FontStyleDelta")));
            this.cb_CostMethod.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("cb_CostMethod.Properties.Appearance.GradientMode")));
            this.cb_CostMethod.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("cb_CostMethod.Properties.Appearance.Image")));
            this.cb_CostMethod.Properties.Appearance.Options.UseTextOptions = true;
            this.cb_CostMethod.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.cb_CostMethod.Properties.AutoHeight = ((bool)(resources.GetObject("cb_CostMethod.Properties.AutoHeight")));
            this.cb_CostMethod.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("cb_CostMethod.Properties.Buttons"))))});
            this.cb_CostMethod.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("cb_CostMethod.Properties.GlyphAlignment")));
            this.cb_CostMethod.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cb_CostMethod.Properties.Items"), ((object)(resources.GetObject("cb_CostMethod.Properties.Items1"))), ((int)(resources.GetObject("cb_CostMethod.Properties.Items2")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cb_CostMethod.Properties.Items3"), ((object)(resources.GetObject("cb_CostMethod.Properties.Items4"))), ((int)(resources.GetObject("cb_CostMethod.Properties.Items5")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cb_CostMethod.Properties.Items6"), ((object)(resources.GetObject("cb_CostMethod.Properties.Items7"))), ((int)(resources.GetObject("cb_CostMethod.Properties.Items8"))))});
            this.cb_CostMethod.Properties.NullValuePrompt = resources.GetString("cb_CostMethod.Properties.NullValuePrompt");
            this.cb_CostMethod.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("cb_CostMethod.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl9
            // 
            resources.ApplyResources(this.labelControl9, "labelControl9");
            this.labelControl9.Name = "labelControl9";
            // 
            // labelControl1
            // 
            resources.ApplyResources(this.labelControl1, "labelControl1");
            this.labelControl1.Name = "labelControl1";
            // 
            // labelControl5
            // 
            resources.ApplyResources(this.labelControl5, "labelControl5");
            this.labelControl5.Name = "labelControl5";
            // 
            // txt_Manager
            // 
            resources.ApplyResources(this.txt_Manager, "txt_Manager");
            this.txt_Manager.EnterMoveNextControl = true;
            this.txt_Manager.Name = "txt_Manager";
            this.txt_Manager.Properties.AccessibleDescription = resources.GetString("txt_Manager.Properties.AccessibleDescription");
            this.txt_Manager.Properties.AccessibleName = resources.GetString("txt_Manager.Properties.AccessibleName");
            this.txt_Manager.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Manager.Properties.AutoHeight")));
            this.txt_Manager.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_Manager.Properties.Mask.AutoComplete")));
            this.txt_Manager.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_Manager.Properties.Mask.BeepOnError")));
            this.txt_Manager.Properties.Mask.EditMask = resources.GetString("txt_Manager.Properties.Mask.EditMask");
            this.txt_Manager.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Manager.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Manager.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Manager.Properties.Mask.MaskType")));
            this.txt_Manager.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_Manager.Properties.Mask.PlaceHolder")));
            this.txt_Manager.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Manager.Properties.Mask.SaveLiteral")));
            this.txt_Manager.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Manager.Properties.Mask.ShowPlaceHolders")));
            this.txt_Manager.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_Manager.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_Manager.Properties.MaxLength = 50;
            this.txt_Manager.Properties.NullValuePrompt = resources.GetString("txt_Manager.Properties.NullValuePrompt");
            this.txt_Manager.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_Manager.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_Manager.Modified += new System.EventHandler(this.txt_StoreCode_Modified);
            // 
            // lkpStore
            // 
            resources.ApplyResources(this.lkpStore, "lkpStore");
            this.lkpStore.EnterMoveNextControl = true;
            this.lkpStore.MenuManager = this.barManager1;
            this.lkpStore.Name = "lkpStore";
            this.lkpStore.Properties.AccessibleDescription = resources.GetString("lkpStore.Properties.AccessibleDescription");
            this.lkpStore.Properties.AccessibleName = resources.GetString("lkpStore.Properties.AccessibleName");
            this.lkpStore.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkpStore.Properties.Appearance.FontSizeDelta")));
            this.lkpStore.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpStore.Properties.Appearance.FontStyleDelta")));
            this.lkpStore.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpStore.Properties.Appearance.GradientMode")));
            this.lkpStore.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkpStore.Properties.Appearance.Image")));
            this.lkpStore.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpStore.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpStore.Properties.AutoHeight = ((bool)(resources.GetObject("lkpStore.Properties.AutoHeight")));
            this.lkpStore.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpStore.Properties.Buttons"))))});
            this.lkpStore.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns"), resources.GetString("lkpStore.Properties.Columns1"), ((int)(resources.GetObject("lkpStore.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns3"))), resources.GetString("lkpStore.Properties.Columns4"), ((bool)(resources.GetObject("lkpStore.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns7"), resources.GetString("lkpStore.Properties.Columns8"), ((int)(resources.GetObject("lkpStore.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns10"))), resources.GetString("lkpStore.Properties.Columns11"), ((bool)(resources.GetObject("lkpStore.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns14"), resources.GetString("lkpStore.Properties.Columns15"), ((int)(resources.GetObject("lkpStore.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns17"))), resources.GetString("lkpStore.Properties.Columns18"), ((bool)(resources.GetObject("lkpStore.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns20")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns21"), resources.GetString("lkpStore.Properties.Columns22"), ((int)(resources.GetObject("lkpStore.Properties.Columns23"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns24"))), resources.GetString("lkpStore.Properties.Columns25"), ((bool)(resources.GetObject("lkpStore.Properties.Columns26"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns27")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns28"), resources.GetString("lkpStore.Properties.Columns29"), ((int)(resources.GetObject("lkpStore.Properties.Columns30"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns31"))), resources.GetString("lkpStore.Properties.Columns32"), ((bool)(resources.GetObject("lkpStore.Properties.Columns33"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns34")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns35"), resources.GetString("lkpStore.Properties.Columns36"), ((int)(resources.GetObject("lkpStore.Properties.Columns37"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns38"))), resources.GetString("lkpStore.Properties.Columns39"), ((bool)(resources.GetObject("lkpStore.Properties.Columns40"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns41")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns42"), resources.GetString("lkpStore.Properties.Columns43"), ((int)(resources.GetObject("lkpStore.Properties.Columns44"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns45"))), resources.GetString("lkpStore.Properties.Columns46"), ((bool)(resources.GetObject("lkpStore.Properties.Columns47"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns48"))))});
            this.lkpStore.Properties.NullText = resources.GetString("lkpStore.Properties.NullText");
            this.lkpStore.Properties.NullValuePrompt = resources.GetString("lkpStore.Properties.NullValuePrompt");
            this.lkpStore.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkpStore.Properties.NullValuePromptShowForEmptyValue")));
            this.lkpStore.EditValueChanged += new System.EventHandler(this.lkpStore_EditValueChanged);
            this.lkpStore.Modified += new System.EventHandler(this.txt_StoreCode_Modified);
            // 
            // labelControl7
            // 
            resources.ApplyResources(this.labelControl7, "labelControl7");
            this.labelControl7.Name = "labelControl7";
            // 
            // labelControl8
            // 
            resources.ApplyResources(this.labelControl8, "labelControl8");
            this.labelControl8.Name = "labelControl8";
            // 
            // lkp_PerPetualCostOfSoldGoods
            // 
            resources.ApplyResources(this.lkp_PerPetualCostOfSoldGoods, "lkp_PerPetualCostOfSoldGoods");
            this.lkp_PerPetualCostOfSoldGoods.EnterMoveNextControl = true;
            this.lkp_PerPetualCostOfSoldGoods.MenuManager = this.barManager1;
            this.lkp_PerPetualCostOfSoldGoods.Name = "lkp_PerPetualCostOfSoldGoods";
            this.lkp_PerPetualCostOfSoldGoods.Properties.AccessibleDescription = resources.GetString("lkp_PerPetualCostOfSoldGoods.Properties.AccessibleDescription");
            this.lkp_PerPetualCostOfSoldGoods.Properties.AccessibleName = resources.GetString("lkp_PerPetualCostOfSoldGoods.Properties.AccessibleName");
            this.lkp_PerPetualCostOfSoldGoods.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_PerPetualCostOfSoldGoods.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_PerPetualCostOfSoldGoods.Properties.Appearance.FontSizeDelta")));
            this.lkp_PerPetualCostOfSoldGoods.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_PerPetualCostOfSoldGoods.Properties.Appearance.FontStyleDelta")));
            this.lkp_PerPetualCostOfSoldGoods.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_PerPetualCostOfSoldGoods.Properties.Appearance.GradientMode")));
            this.lkp_PerPetualCostOfSoldGoods.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_PerPetualCostOfSoldGoods.Properties.Appearance.Image")));
            this.lkp_PerPetualCostOfSoldGoods.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_PerPetualCostOfSoldGoods.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_PerPetualCostOfSoldGoods.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_PerPetualCostOfSoldGoods.Properties.AutoHeight")));
            this.lkp_PerPetualCostOfSoldGoods.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_PerPetualCostOfSoldGoods.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_PerPetualCostOfSoldGoods.Properties.Buttons"))))});
            this.lkp_PerPetualCostOfSoldGoods.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PerPetualCostOfSoldGoods.Properties.Columns"), resources.GetString("lkp_PerPetualCostOfSoldGoods.Properties.Columns1"), ((int)(resources.GetObject("lkp_PerPetualCostOfSoldGoods.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PerPetualCostOfSoldGoods.Properties.Columns3"))), resources.GetString("lkp_PerPetualCostOfSoldGoods.Properties.Columns4"), ((bool)(resources.GetObject("lkp_PerPetualCostOfSoldGoods.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PerPetualCostOfSoldGoods.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PerPetualCostOfSoldGoods.Properties.Columns7"), resources.GetString("lkp_PerPetualCostOfSoldGoods.Properties.Columns8"), ((int)(resources.GetObject("lkp_PerPetualCostOfSoldGoods.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PerPetualCostOfSoldGoods.Properties.Columns10"))), resources.GetString("lkp_PerPetualCostOfSoldGoods.Properties.Columns11"), ((bool)(resources.GetObject("lkp_PerPetualCostOfSoldGoods.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PerPetualCostOfSoldGoods.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PerPetualCostOfSoldGoods.Properties.Columns14"), resources.GetString("lkp_PerPetualCostOfSoldGoods.Properties.Columns15"), ((int)(resources.GetObject("lkp_PerPetualCostOfSoldGoods.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PerPetualCostOfSoldGoods.Properties.Columns17"))), resources.GetString("lkp_PerPetualCostOfSoldGoods.Properties.Columns18"), ((bool)(resources.GetObject("lkp_PerPetualCostOfSoldGoods.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PerPetualCostOfSoldGoods.Properties.Columns20"))))});
            this.lkp_PerPetualCostOfSoldGoods.Properties.NullText = resources.GetString("lkp_PerPetualCostOfSoldGoods.Properties.NullText");
            this.lkp_PerPetualCostOfSoldGoods.Properties.NullValuePrompt = resources.GetString("lkp_PerPetualCostOfSoldGoods.Properties.NullValuePrompt");
            this.lkp_PerPetualCostOfSoldGoods.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_PerPetualCostOfSoldGoods.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_PerPetualCostOfSoldGoods.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            // 
            // lblPurchaseDiscount
            // 
            resources.ApplyResources(this.lblPurchaseDiscount, "lblPurchaseDiscount");
            this.lblPurchaseDiscount.Name = "lblPurchaseDiscount";
            // 
            // lkp_PerPetualPurchaseDiscountAcc
            // 
            resources.ApplyResources(this.lkp_PerPetualPurchaseDiscountAcc, "lkp_PerPetualPurchaseDiscountAcc");
            this.lkp_PerPetualPurchaseDiscountAcc.EnterMoveNextControl = true;
            this.lkp_PerPetualPurchaseDiscountAcc.MenuManager = this.barManager1;
            this.lkp_PerPetualPurchaseDiscountAcc.Name = "lkp_PerPetualPurchaseDiscountAcc";
            this.lkp_PerPetualPurchaseDiscountAcc.Properties.AccessibleDescription = resources.GetString("lkp_PerPetualPurchaseDiscountAcc.Properties.AccessibleDescription");
            this.lkp_PerPetualPurchaseDiscountAcc.Properties.AccessibleName = resources.GetString("lkp_PerPetualPurchaseDiscountAcc.Properties.AccessibleName");
            this.lkp_PerPetualPurchaseDiscountAcc.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_PerPetualPurchaseDiscountAcc.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_PerPetualPurchaseDiscountAcc.Properties.Appearance.FontSizeDelta")));
            this.lkp_PerPetualPurchaseDiscountAcc.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_PerPetualPurchaseDiscountAcc.Properties.Appearance.FontStyleDelta")));
            this.lkp_PerPetualPurchaseDiscountAcc.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_PerPetualPurchaseDiscountAcc.Properties.Appearance.GradientMode")));
            this.lkp_PerPetualPurchaseDiscountAcc.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_PerPetualPurchaseDiscountAcc.Properties.Appearance.Image")));
            this.lkp_PerPetualPurchaseDiscountAcc.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_PerPetualPurchaseDiscountAcc.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_PerPetualPurchaseDiscountAcc.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_PerPetualPurchaseDiscountAcc.Properties.AutoHeight")));
            this.lkp_PerPetualPurchaseDiscountAcc.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_PerPetualPurchaseDiscountAcc.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_PerPetualPurchaseDiscountAcc.Properties.Buttons"))))});
            this.lkp_PerPetualPurchaseDiscountAcc.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PerPetualPurchaseDiscountAcc.Properties.Columns"), resources.GetString("lkp_PerPetualPurchaseDiscountAcc.Properties.Columns1"), ((int)(resources.GetObject("lkp_PerPetualPurchaseDiscountAcc.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PerPetualPurchaseDiscountAcc.Properties.Columns3"))), resources.GetString("lkp_PerPetualPurchaseDiscountAcc.Properties.Columns4"), ((bool)(resources.GetObject("lkp_PerPetualPurchaseDiscountAcc.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PerPetualPurchaseDiscountAcc.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PerPetualPurchaseDiscountAcc.Properties.Columns7"), resources.GetString("lkp_PerPetualPurchaseDiscountAcc.Properties.Columns8"), ((int)(resources.GetObject("lkp_PerPetualPurchaseDiscountAcc.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PerPetualPurchaseDiscountAcc.Properties.Columns10"))), resources.GetString("lkp_PerPetualPurchaseDiscountAcc.Properties.Columns11"), ((bool)(resources.GetObject("lkp_PerPetualPurchaseDiscountAcc.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PerPetualPurchaseDiscountAcc.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PerPetualPurchaseDiscountAcc.Properties.Columns14"), resources.GetString("lkp_PerPetualPurchaseDiscountAcc.Properties.Columns15"), ((int)(resources.GetObject("lkp_PerPetualPurchaseDiscountAcc.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PerPetualPurchaseDiscountAcc.Properties.Columns17"))), resources.GetString("lkp_PerPetualPurchaseDiscountAcc.Properties.Columns18"), ((bool)(resources.GetObject("lkp_PerPetualPurchaseDiscountAcc.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PerPetualPurchaseDiscountAcc.Properties.Columns20"))))});
            this.lkp_PerPetualPurchaseDiscountAcc.Properties.NullText = resources.GetString("lkp_PerPetualPurchaseDiscountAcc.Properties.NullText");
            this.lkp_PerPetualPurchaseDiscountAcc.Properties.NullValuePrompt = resources.GetString("lkp_PerPetualPurchaseDiscountAcc.Properties.NullValuePrompt");
            this.lkp_PerPetualPurchaseDiscountAcc.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_PerPetualPurchaseDiscountAcc.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_PerPetualPurchaseDiscountAcc.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            // 
            // lkp_PerPetualSalesDiscount
            // 
            resources.ApplyResources(this.lkp_PerPetualSalesDiscount, "lkp_PerPetualSalesDiscount");
            this.lkp_PerPetualSalesDiscount.EnterMoveNextControl = true;
            this.lkp_PerPetualSalesDiscount.MenuManager = this.barManager1;
            this.lkp_PerPetualSalesDiscount.Name = "lkp_PerPetualSalesDiscount";
            this.lkp_PerPetualSalesDiscount.Properties.AccessibleDescription = resources.GetString("lkp_PerPetualSalesDiscount.Properties.AccessibleDescription");
            this.lkp_PerPetualSalesDiscount.Properties.AccessibleName = resources.GetString("lkp_PerPetualSalesDiscount.Properties.AccessibleName");
            this.lkp_PerPetualSalesDiscount.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_PerPetualSalesDiscount.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_PerPetualSalesDiscount.Properties.Appearance.FontSizeDelta")));
            this.lkp_PerPetualSalesDiscount.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_PerPetualSalesDiscount.Properties.Appearance.FontStyleDelta")));
            this.lkp_PerPetualSalesDiscount.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_PerPetualSalesDiscount.Properties.Appearance.GradientMode")));
            this.lkp_PerPetualSalesDiscount.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_PerPetualSalesDiscount.Properties.Appearance.Image")));
            this.lkp_PerPetualSalesDiscount.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_PerPetualSalesDiscount.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_PerPetualSalesDiscount.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_PerPetualSalesDiscount.Properties.AutoHeight")));
            this.lkp_PerPetualSalesDiscount.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_PerPetualSalesDiscount.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_PerPetualSalesDiscount.Properties.Buttons"))))});
            this.lkp_PerPetualSalesDiscount.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PerPetualSalesDiscount.Properties.Columns"), resources.GetString("lkp_PerPetualSalesDiscount.Properties.Columns1"), ((int)(resources.GetObject("lkp_PerPetualSalesDiscount.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PerPetualSalesDiscount.Properties.Columns3"))), resources.GetString("lkp_PerPetualSalesDiscount.Properties.Columns4"), ((bool)(resources.GetObject("lkp_PerPetualSalesDiscount.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PerPetualSalesDiscount.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PerPetualSalesDiscount.Properties.Columns7"), resources.GetString("lkp_PerPetualSalesDiscount.Properties.Columns8"), ((int)(resources.GetObject("lkp_PerPetualSalesDiscount.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PerPetualSalesDiscount.Properties.Columns10"))), resources.GetString("lkp_PerPetualSalesDiscount.Properties.Columns11"), ((bool)(resources.GetObject("lkp_PerPetualSalesDiscount.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PerPetualSalesDiscount.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PerPetualSalesDiscount.Properties.Columns14"), resources.GetString("lkp_PerPetualSalesDiscount.Properties.Columns15"), ((int)(resources.GetObject("lkp_PerPetualSalesDiscount.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PerPetualSalesDiscount.Properties.Columns17"))), resources.GetString("lkp_PerPetualSalesDiscount.Properties.Columns18"), ((bool)(resources.GetObject("lkp_PerPetualSalesDiscount.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PerPetualSalesDiscount.Properties.Columns20"))))});
            this.lkp_PerPetualSalesDiscount.Properties.NullText = resources.GetString("lkp_PerPetualSalesDiscount.Properties.NullText");
            this.lkp_PerPetualSalesDiscount.Properties.NullValuePrompt = resources.GetString("lkp_PerPetualSalesDiscount.Properties.NullValuePrompt");
            this.lkp_PerPetualSalesDiscount.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_PerPetualSalesDiscount.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_PerPetualSalesDiscount.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            // 
            // lblSalesDiscount
            // 
            resources.ApplyResources(this.lblSalesDiscount, "lblSalesDiscount");
            this.lblSalesDiscount.Name = "lblSalesDiscount";
            // 
            // lblInventory
            // 
            resources.ApplyResources(this.lblInventory, "lblInventory");
            this.lblInventory.Name = "lblInventory";
            // 
            // lkp_PerPetualInventoryAcc
            // 
            resources.ApplyResources(this.lkp_PerPetualInventoryAcc, "lkp_PerPetualInventoryAcc");
            this.lkp_PerPetualInventoryAcc.EnterMoveNextControl = true;
            this.lkp_PerPetualInventoryAcc.MenuManager = this.barManager1;
            this.lkp_PerPetualInventoryAcc.Name = "lkp_PerPetualInventoryAcc";
            this.lkp_PerPetualInventoryAcc.Properties.AccessibleDescription = resources.GetString("lkp_PerPetualInventoryAcc.Properties.AccessibleDescription");
            this.lkp_PerPetualInventoryAcc.Properties.AccessibleName = resources.GetString("lkp_PerPetualInventoryAcc.Properties.AccessibleName");
            this.lkp_PerPetualInventoryAcc.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_PerPetualInventoryAcc.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_PerPetualInventoryAcc.Properties.Appearance.FontSizeDelta")));
            this.lkp_PerPetualInventoryAcc.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_PerPetualInventoryAcc.Properties.Appearance.FontStyleDelta")));
            this.lkp_PerPetualInventoryAcc.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_PerPetualInventoryAcc.Properties.Appearance.GradientMode")));
            this.lkp_PerPetualInventoryAcc.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_PerPetualInventoryAcc.Properties.Appearance.Image")));
            this.lkp_PerPetualInventoryAcc.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_PerPetualInventoryAcc.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_PerPetualInventoryAcc.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_PerPetualInventoryAcc.Properties.AutoHeight")));
            this.lkp_PerPetualInventoryAcc.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_PerPetualInventoryAcc.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_PerPetualInventoryAcc.Properties.Buttons"))))});
            this.lkp_PerPetualInventoryAcc.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PerPetualInventoryAcc.Properties.Columns"), resources.GetString("lkp_PerPetualInventoryAcc.Properties.Columns1"), ((int)(resources.GetObject("lkp_PerPetualInventoryAcc.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PerPetualInventoryAcc.Properties.Columns3"))), resources.GetString("lkp_PerPetualInventoryAcc.Properties.Columns4"), ((bool)(resources.GetObject("lkp_PerPetualInventoryAcc.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PerPetualInventoryAcc.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PerPetualInventoryAcc.Properties.Columns7"), resources.GetString("lkp_PerPetualInventoryAcc.Properties.Columns8"), ((int)(resources.GetObject("lkp_PerPetualInventoryAcc.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PerPetualInventoryAcc.Properties.Columns10"))), resources.GetString("lkp_PerPetualInventoryAcc.Properties.Columns11"), ((bool)(resources.GetObject("lkp_PerPetualInventoryAcc.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PerPetualInventoryAcc.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PerPetualInventoryAcc.Properties.Columns14"), resources.GetString("lkp_PerPetualInventoryAcc.Properties.Columns15"), ((int)(resources.GetObject("lkp_PerPetualInventoryAcc.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PerPetualInventoryAcc.Properties.Columns17"))), resources.GetString("lkp_PerPetualInventoryAcc.Properties.Columns18"), ((bool)(resources.GetObject("lkp_PerPetualInventoryAcc.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PerPetualInventoryAcc.Properties.Columns20"))))});
            this.lkp_PerPetualInventoryAcc.Properties.NullText = resources.GetString("lkp_PerPetualInventoryAcc.Properties.NullText");
            this.lkp_PerPetualInventoryAcc.Properties.NullValuePrompt = resources.GetString("lkp_PerPetualInventoryAcc.Properties.NullValuePrompt");
            this.lkp_PerPetualInventoryAcc.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_PerPetualInventoryAcc.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_PerPetualInventoryAcc.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            // 
            // lblSalesReturn
            // 
            resources.ApplyResources(this.lblSalesReturn, "lblSalesReturn");
            this.lblSalesReturn.Name = "lblSalesReturn";
            // 
            // lkp_PerPetualSalesReturnAcc
            // 
            resources.ApplyResources(this.lkp_PerPetualSalesReturnAcc, "lkp_PerPetualSalesReturnAcc");
            this.lkp_PerPetualSalesReturnAcc.EnterMoveNextControl = true;
            this.lkp_PerPetualSalesReturnAcc.MenuManager = this.barManager1;
            this.lkp_PerPetualSalesReturnAcc.Name = "lkp_PerPetualSalesReturnAcc";
            this.lkp_PerPetualSalesReturnAcc.Properties.AccessibleDescription = resources.GetString("lkp_PerPetualSalesReturnAcc.Properties.AccessibleDescription");
            this.lkp_PerPetualSalesReturnAcc.Properties.AccessibleName = resources.GetString("lkp_PerPetualSalesReturnAcc.Properties.AccessibleName");
            this.lkp_PerPetualSalesReturnAcc.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_PerPetualSalesReturnAcc.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_PerPetualSalesReturnAcc.Properties.Appearance.FontSizeDelta")));
            this.lkp_PerPetualSalesReturnAcc.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_PerPetualSalesReturnAcc.Properties.Appearance.FontStyleDelta")));
            this.lkp_PerPetualSalesReturnAcc.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_PerPetualSalesReturnAcc.Properties.Appearance.GradientMode")));
            this.lkp_PerPetualSalesReturnAcc.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_PerPetualSalesReturnAcc.Properties.Appearance.Image")));
            this.lkp_PerPetualSalesReturnAcc.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_PerPetualSalesReturnAcc.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_PerPetualSalesReturnAcc.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_PerPetualSalesReturnAcc.Properties.AutoHeight")));
            this.lkp_PerPetualSalesReturnAcc.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_PerPetualSalesReturnAcc.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_PerPetualSalesReturnAcc.Properties.Buttons"))))});
            this.lkp_PerPetualSalesReturnAcc.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PerPetualSalesReturnAcc.Properties.Columns"), resources.GetString("lkp_PerPetualSalesReturnAcc.Properties.Columns1"), ((int)(resources.GetObject("lkp_PerPetualSalesReturnAcc.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PerPetualSalesReturnAcc.Properties.Columns3"))), resources.GetString("lkp_PerPetualSalesReturnAcc.Properties.Columns4"), ((bool)(resources.GetObject("lkp_PerPetualSalesReturnAcc.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PerPetualSalesReturnAcc.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PerPetualSalesReturnAcc.Properties.Columns7"), resources.GetString("lkp_PerPetualSalesReturnAcc.Properties.Columns8"), ((int)(resources.GetObject("lkp_PerPetualSalesReturnAcc.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PerPetualSalesReturnAcc.Properties.Columns10"))), resources.GetString("lkp_PerPetualSalesReturnAcc.Properties.Columns11"), ((bool)(resources.GetObject("lkp_PerPetualSalesReturnAcc.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PerPetualSalesReturnAcc.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PerPetualSalesReturnAcc.Properties.Columns14"), resources.GetString("lkp_PerPetualSalesReturnAcc.Properties.Columns15"), ((int)(resources.GetObject("lkp_PerPetualSalesReturnAcc.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PerPetualSalesReturnAcc.Properties.Columns17"))), resources.GetString("lkp_PerPetualSalesReturnAcc.Properties.Columns18"), ((bool)(resources.GetObject("lkp_PerPetualSalesReturnAcc.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PerPetualSalesReturnAcc.Properties.Columns20"))))});
            this.lkp_PerPetualSalesReturnAcc.Properties.NullText = resources.GetString("lkp_PerPetualSalesReturnAcc.Properties.NullText");
            this.lkp_PerPetualSalesReturnAcc.Properties.NullValuePrompt = resources.GetString("lkp_PerPetualSalesReturnAcc.Properties.NullValuePrompt");
            this.lkp_PerPetualSalesReturnAcc.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_PerPetualSalesReturnAcc.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_PerPetualSalesReturnAcc.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            // 
            // lblSales
            // 
            resources.ApplyResources(this.lblSales, "lblSales");
            this.lblSales.Name = "lblSales";
            // 
            // lkp_PerPetualSalesAcc
            // 
            resources.ApplyResources(this.lkp_PerPetualSalesAcc, "lkp_PerPetualSalesAcc");
            this.lkp_PerPetualSalesAcc.EnterMoveNextControl = true;
            this.lkp_PerPetualSalesAcc.MenuManager = this.barManager1;
            this.lkp_PerPetualSalesAcc.Name = "lkp_PerPetualSalesAcc";
            this.lkp_PerPetualSalesAcc.Properties.AccessibleDescription = resources.GetString("lkp_PerPetualSalesAcc.Properties.AccessibleDescription");
            this.lkp_PerPetualSalesAcc.Properties.AccessibleName = resources.GetString("lkp_PerPetualSalesAcc.Properties.AccessibleName");
            this.lkp_PerPetualSalesAcc.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_PerPetualSalesAcc.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_PerPetualSalesAcc.Properties.Appearance.FontSizeDelta")));
            this.lkp_PerPetualSalesAcc.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_PerPetualSalesAcc.Properties.Appearance.FontStyleDelta")));
            this.lkp_PerPetualSalesAcc.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_PerPetualSalesAcc.Properties.Appearance.GradientMode")));
            this.lkp_PerPetualSalesAcc.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_PerPetualSalesAcc.Properties.Appearance.Image")));
            this.lkp_PerPetualSalesAcc.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_PerPetualSalesAcc.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_PerPetualSalesAcc.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_PerPetualSalesAcc.Properties.AutoHeight")));
            this.lkp_PerPetualSalesAcc.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_PerPetualSalesAcc.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_PerPetualSalesAcc.Properties.Buttons"))))});
            this.lkp_PerPetualSalesAcc.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PerPetualSalesAcc.Properties.Columns"), resources.GetString("lkp_PerPetualSalesAcc.Properties.Columns1"), ((int)(resources.GetObject("lkp_PerPetualSalesAcc.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PerPetualSalesAcc.Properties.Columns3"))), resources.GetString("lkp_PerPetualSalesAcc.Properties.Columns4"), ((bool)(resources.GetObject("lkp_PerPetualSalesAcc.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PerPetualSalesAcc.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PerPetualSalesAcc.Properties.Columns7"), resources.GetString("lkp_PerPetualSalesAcc.Properties.Columns8"), ((int)(resources.GetObject("lkp_PerPetualSalesAcc.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PerPetualSalesAcc.Properties.Columns10"))), resources.GetString("lkp_PerPetualSalesAcc.Properties.Columns11"), ((bool)(resources.GetObject("lkp_PerPetualSalesAcc.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PerPetualSalesAcc.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PerPetualSalesAcc.Properties.Columns14"), resources.GetString("lkp_PerPetualSalesAcc.Properties.Columns15"), ((int)(resources.GetObject("lkp_PerPetualSalesAcc.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PerPetualSalesAcc.Properties.Columns17"))), resources.GetString("lkp_PerPetualSalesAcc.Properties.Columns18"), ((bool)(resources.GetObject("lkp_PerPetualSalesAcc.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PerPetualSalesAcc.Properties.Columns20"))))});
            this.lkp_PerPetualSalesAcc.Properties.NullText = resources.GetString("lkp_PerPetualSalesAcc.Properties.NullText");
            this.lkp_PerPetualSalesAcc.Properties.NullValuePrompt = resources.GetString("lkp_PerPetualSalesAcc.Properties.NullValuePrompt");
            this.lkp_PerPetualSalesAcc.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_PerPetualSalesAcc.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_PerPetualSalesAcc.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            // 
            // xtraTabControl1
            // 
            resources.ApplyResources(this.xtraTabControl1, "xtraTabControl1");
            this.xtraTabControl1.AppearancePage.Header.FontSizeDelta = ((int)(resources.GetObject("xtraTabControl1.AppearancePage.Header.FontSizeDelta")));
            this.xtraTabControl1.AppearancePage.Header.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("xtraTabControl1.AppearancePage.Header.FontStyleDelta")));
            this.xtraTabControl1.AppearancePage.Header.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("xtraTabControl1.AppearancePage.Header.GradientMode")));
            this.xtraTabControl1.AppearancePage.Header.Image = ((System.Drawing.Image)(resources.GetObject("xtraTabControl1.AppearancePage.Header.Image")));
            this.xtraTabControl1.AppearancePage.Header.Options.UseTextOptions = true;
            this.xtraTabControl1.AppearancePage.Header.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.tab_PeriodicAcc;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.tab_PeriodicAcc,
            this.tab_PerpetualAcc});
            // 
            // tab_PeriodicAcc
            // 
            resources.ApplyResources(this.tab_PeriodicAcc, "tab_PeriodicAcc");
            this.tab_PeriodicAcc.Controls.Add(this.chk_autoCreateAccs);
            this.tab_PeriodicAcc.Controls.Add(this.lkp_PeriodicCloseInvAcc);
            this.tab_PeriodicAcc.Controls.Add(this.lblPurchasesReturnAccount);
            this.tab_PeriodicAcc.Controls.Add(this.lkp_PeriodicPrReturnAcc);
            this.tab_PeriodicAcc.Controls.Add(this.lblCloseInventoryAccount);
            this.tab_PeriodicAcc.Controls.Add(this.lblPurchasesAccount);
            this.tab_PeriodicAcc.Controls.Add(this.lkp_PeriodicSalesAcc);
            this.tab_PeriodicAcc.Controls.Add(this.lkp_PeriodicPurchaseAcc);
            this.tab_PeriodicAcc.Controls.Add(this.lblSalesAccount);
            this.tab_PeriodicAcc.Controls.Add(this.labelControl12);
            this.tab_PeriodicAcc.Controls.Add(this.lkp_PeriodicSalesReturnAcc);
            this.tab_PeriodicAcc.Controls.Add(this.lkp_PeriodicPrDiscAcc);
            this.tab_PeriodicAcc.Controls.Add(this.lblSalesReturnAccount);
            this.tab_PeriodicAcc.Controls.Add(this.lkp_PeriodicSalesDiscAcc);
            this.tab_PeriodicAcc.Controls.Add(this.labelControl14);
            this.tab_PeriodicAcc.Controls.Add(this.lkp_PeriodicOpenInvAcc);
            this.tab_PeriodicAcc.Controls.Add(this.lblOpenInventoryAccount);
            this.tab_PeriodicAcc.Name = "tab_PeriodicAcc";
            // 
            // chk_autoCreateAccs
            // 
            resources.ApplyResources(this.chk_autoCreateAccs, "chk_autoCreateAccs");
            this.chk_autoCreateAccs.MenuManager = this.barManager1;
            this.chk_autoCreateAccs.Name = "chk_autoCreateAccs";
            this.chk_autoCreateAccs.Properties.AccessibleDescription = resources.GetString("chk_autoCreateAccs.Properties.AccessibleDescription");
            this.chk_autoCreateAccs.Properties.AccessibleName = resources.GetString("chk_autoCreateAccs.Properties.AccessibleName");
            this.chk_autoCreateAccs.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("chk_autoCreateAccs.Properties.Appearance.FontSizeDelta")));
            this.chk_autoCreateAccs.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("chk_autoCreateAccs.Properties.Appearance.FontStyleDelta")));
            this.chk_autoCreateAccs.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("chk_autoCreateAccs.Properties.Appearance.GradientMode")));
            this.chk_autoCreateAccs.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("chk_autoCreateAccs.Properties.Appearance.Image")));
            this.chk_autoCreateAccs.Properties.Appearance.Options.UseTextOptions = true;
            this.chk_autoCreateAccs.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chk_autoCreateAccs.Properties.AutoHeight = ((bool)(resources.GetObject("chk_autoCreateAccs.Properties.AutoHeight")));
            this.chk_autoCreateAccs.Properties.Caption = resources.GetString("chk_autoCreateAccs.Properties.Caption");
            this.chk_autoCreateAccs.Properties.DisplayValueChecked = resources.GetString("chk_autoCreateAccs.Properties.DisplayValueChecked");
            this.chk_autoCreateAccs.Properties.DisplayValueGrayed = resources.GetString("chk_autoCreateAccs.Properties.DisplayValueGrayed");
            this.chk_autoCreateAccs.Properties.DisplayValueUnchecked = resources.GetString("chk_autoCreateAccs.Properties.DisplayValueUnchecked");
            this.chk_autoCreateAccs.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("chk_autoCreateAccs.Properties.GlyphAlignment")));
            this.chk_autoCreateAccs.CheckedChanged += new System.EventHandler(this.chk_autoCreateAccs_CheckedChanged);
            // 
            // lkp_PeriodicCloseInvAcc
            // 
            resources.ApplyResources(this.lkp_PeriodicCloseInvAcc, "lkp_PeriodicCloseInvAcc");
            this.lkp_PeriodicCloseInvAcc.EnterMoveNextControl = true;
            this.lkp_PeriodicCloseInvAcc.MenuManager = this.barManager1;
            this.lkp_PeriodicCloseInvAcc.Name = "lkp_PeriodicCloseInvAcc";
            this.lkp_PeriodicCloseInvAcc.Properties.AccessibleDescription = resources.GetString("lkp_PeriodicCloseInvAcc.Properties.AccessibleDescription");
            this.lkp_PeriodicCloseInvAcc.Properties.AccessibleName = resources.GetString("lkp_PeriodicCloseInvAcc.Properties.AccessibleName");
            this.lkp_PeriodicCloseInvAcc.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_PeriodicCloseInvAcc.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_PeriodicCloseInvAcc.Properties.Appearance.FontSizeDelta")));
            this.lkp_PeriodicCloseInvAcc.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_PeriodicCloseInvAcc.Properties.Appearance.FontStyleDelta")));
            this.lkp_PeriodicCloseInvAcc.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_PeriodicCloseInvAcc.Properties.Appearance.GradientMode")));
            this.lkp_PeriodicCloseInvAcc.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_PeriodicCloseInvAcc.Properties.Appearance.Image")));
            this.lkp_PeriodicCloseInvAcc.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_PeriodicCloseInvAcc.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_PeriodicCloseInvAcc.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_PeriodicCloseInvAcc.Properties.AutoHeight")));
            this.lkp_PeriodicCloseInvAcc.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_PeriodicCloseInvAcc.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_PeriodicCloseInvAcc.Properties.Buttons"))))});
            this.lkp_PeriodicCloseInvAcc.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PeriodicCloseInvAcc.Properties.Columns"), resources.GetString("lkp_PeriodicCloseInvAcc.Properties.Columns1"), ((int)(resources.GetObject("lkp_PeriodicCloseInvAcc.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PeriodicCloseInvAcc.Properties.Columns3"))), resources.GetString("lkp_PeriodicCloseInvAcc.Properties.Columns4"), ((bool)(resources.GetObject("lkp_PeriodicCloseInvAcc.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PeriodicCloseInvAcc.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PeriodicCloseInvAcc.Properties.Columns7"), resources.GetString("lkp_PeriodicCloseInvAcc.Properties.Columns8"), ((int)(resources.GetObject("lkp_PeriodicCloseInvAcc.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PeriodicCloseInvAcc.Properties.Columns10"))), resources.GetString("lkp_PeriodicCloseInvAcc.Properties.Columns11"), ((bool)(resources.GetObject("lkp_PeriodicCloseInvAcc.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PeriodicCloseInvAcc.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PeriodicCloseInvAcc.Properties.Columns14"), resources.GetString("lkp_PeriodicCloseInvAcc.Properties.Columns15"), ((int)(resources.GetObject("lkp_PeriodicCloseInvAcc.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PeriodicCloseInvAcc.Properties.Columns17"))), resources.GetString("lkp_PeriodicCloseInvAcc.Properties.Columns18"), ((bool)(resources.GetObject("lkp_PeriodicCloseInvAcc.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PeriodicCloseInvAcc.Properties.Columns20"))))});
            this.lkp_PeriodicCloseInvAcc.Properties.NullText = resources.GetString("lkp_PeriodicCloseInvAcc.Properties.NullText");
            this.lkp_PeriodicCloseInvAcc.Properties.NullValuePrompt = resources.GetString("lkp_PeriodicCloseInvAcc.Properties.NullValuePrompt");
            this.lkp_PeriodicCloseInvAcc.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_PeriodicCloseInvAcc.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_PeriodicCloseInvAcc.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            // 
            // lblPurchasesReturnAccount
            // 
            resources.ApplyResources(this.lblPurchasesReturnAccount, "lblPurchasesReturnAccount");
            this.lblPurchasesReturnAccount.Name = "lblPurchasesReturnAccount";
            // 
            // lkp_PeriodicPrReturnAcc
            // 
            resources.ApplyResources(this.lkp_PeriodicPrReturnAcc, "lkp_PeriodicPrReturnAcc");
            this.lkp_PeriodicPrReturnAcc.EnterMoveNextControl = true;
            this.lkp_PeriodicPrReturnAcc.MenuManager = this.barManager1;
            this.lkp_PeriodicPrReturnAcc.Name = "lkp_PeriodicPrReturnAcc";
            this.lkp_PeriodicPrReturnAcc.Properties.AccessibleDescription = resources.GetString("lkp_PeriodicPrReturnAcc.Properties.AccessibleDescription");
            this.lkp_PeriodicPrReturnAcc.Properties.AccessibleName = resources.GetString("lkp_PeriodicPrReturnAcc.Properties.AccessibleName");
            this.lkp_PeriodicPrReturnAcc.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_PeriodicPrReturnAcc.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_PeriodicPrReturnAcc.Properties.Appearance.FontSizeDelta")));
            this.lkp_PeriodicPrReturnAcc.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_PeriodicPrReturnAcc.Properties.Appearance.FontStyleDelta")));
            this.lkp_PeriodicPrReturnAcc.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_PeriodicPrReturnAcc.Properties.Appearance.GradientMode")));
            this.lkp_PeriodicPrReturnAcc.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_PeriodicPrReturnAcc.Properties.Appearance.Image")));
            this.lkp_PeriodicPrReturnAcc.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_PeriodicPrReturnAcc.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_PeriodicPrReturnAcc.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_PeriodicPrReturnAcc.Properties.AutoHeight")));
            this.lkp_PeriodicPrReturnAcc.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_PeriodicPrReturnAcc.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_PeriodicPrReturnAcc.Properties.Buttons"))))});
            this.lkp_PeriodicPrReturnAcc.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PeriodicPrReturnAcc.Properties.Columns"), resources.GetString("lkp_PeriodicPrReturnAcc.Properties.Columns1"), ((int)(resources.GetObject("lkp_PeriodicPrReturnAcc.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PeriodicPrReturnAcc.Properties.Columns3"))), resources.GetString("lkp_PeriodicPrReturnAcc.Properties.Columns4"), ((bool)(resources.GetObject("lkp_PeriodicPrReturnAcc.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PeriodicPrReturnAcc.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PeriodicPrReturnAcc.Properties.Columns7"), resources.GetString("lkp_PeriodicPrReturnAcc.Properties.Columns8"), ((int)(resources.GetObject("lkp_PeriodicPrReturnAcc.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PeriodicPrReturnAcc.Properties.Columns10"))), resources.GetString("lkp_PeriodicPrReturnAcc.Properties.Columns11"), ((bool)(resources.GetObject("lkp_PeriodicPrReturnAcc.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PeriodicPrReturnAcc.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PeriodicPrReturnAcc.Properties.Columns14"), resources.GetString("lkp_PeriodicPrReturnAcc.Properties.Columns15"), ((int)(resources.GetObject("lkp_PeriodicPrReturnAcc.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PeriodicPrReturnAcc.Properties.Columns17"))), resources.GetString("lkp_PeriodicPrReturnAcc.Properties.Columns18"), ((bool)(resources.GetObject("lkp_PeriodicPrReturnAcc.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PeriodicPrReturnAcc.Properties.Columns20"))))});
            this.lkp_PeriodicPrReturnAcc.Properties.NullText = resources.GetString("lkp_PeriodicPrReturnAcc.Properties.NullText");
            this.lkp_PeriodicPrReturnAcc.Properties.NullValuePrompt = resources.GetString("lkp_PeriodicPrReturnAcc.Properties.NullValuePrompt");
            this.lkp_PeriodicPrReturnAcc.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_PeriodicPrReturnAcc.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_PeriodicPrReturnAcc.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            // 
            // lblCloseInventoryAccount
            // 
            resources.ApplyResources(this.lblCloseInventoryAccount, "lblCloseInventoryAccount");
            this.lblCloseInventoryAccount.Name = "lblCloseInventoryAccount";
            // 
            // lblPurchasesAccount
            // 
            resources.ApplyResources(this.lblPurchasesAccount, "lblPurchasesAccount");
            this.lblPurchasesAccount.Name = "lblPurchasesAccount";
            this.lblPurchasesAccount.Click += new System.EventHandler(this.labelControl10_Click);
            // 
            // lkp_PeriodicSalesAcc
            // 
            resources.ApplyResources(this.lkp_PeriodicSalesAcc, "lkp_PeriodicSalesAcc");
            this.lkp_PeriodicSalesAcc.EnterMoveNextControl = true;
            this.lkp_PeriodicSalesAcc.MenuManager = this.barManager1;
            this.lkp_PeriodicSalesAcc.Name = "lkp_PeriodicSalesAcc";
            this.lkp_PeriodicSalesAcc.Properties.AccessibleDescription = resources.GetString("lkp_PeriodicSalesAcc.Properties.AccessibleDescription");
            this.lkp_PeriodicSalesAcc.Properties.AccessibleName = resources.GetString("lkp_PeriodicSalesAcc.Properties.AccessibleName");
            this.lkp_PeriodicSalesAcc.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_PeriodicSalesAcc.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_PeriodicSalesAcc.Properties.Appearance.FontSizeDelta")));
            this.lkp_PeriodicSalesAcc.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_PeriodicSalesAcc.Properties.Appearance.FontStyleDelta")));
            this.lkp_PeriodicSalesAcc.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_PeriodicSalesAcc.Properties.Appearance.GradientMode")));
            this.lkp_PeriodicSalesAcc.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_PeriodicSalesAcc.Properties.Appearance.Image")));
            this.lkp_PeriodicSalesAcc.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_PeriodicSalesAcc.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_PeriodicSalesAcc.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_PeriodicSalesAcc.Properties.AutoHeight")));
            this.lkp_PeriodicSalesAcc.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_PeriodicSalesAcc.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_PeriodicSalesAcc.Properties.Buttons"))))});
            this.lkp_PeriodicSalesAcc.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PeriodicSalesAcc.Properties.Columns"), resources.GetString("lkp_PeriodicSalesAcc.Properties.Columns1"), ((int)(resources.GetObject("lkp_PeriodicSalesAcc.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PeriodicSalesAcc.Properties.Columns3"))), resources.GetString("lkp_PeriodicSalesAcc.Properties.Columns4"), ((bool)(resources.GetObject("lkp_PeriodicSalesAcc.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PeriodicSalesAcc.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PeriodicSalesAcc.Properties.Columns7"), resources.GetString("lkp_PeriodicSalesAcc.Properties.Columns8"), ((int)(resources.GetObject("lkp_PeriodicSalesAcc.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PeriodicSalesAcc.Properties.Columns10"))), resources.GetString("lkp_PeriodicSalesAcc.Properties.Columns11"), ((bool)(resources.GetObject("lkp_PeriodicSalesAcc.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PeriodicSalesAcc.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PeriodicSalesAcc.Properties.Columns14"), resources.GetString("lkp_PeriodicSalesAcc.Properties.Columns15"), ((int)(resources.GetObject("lkp_PeriodicSalesAcc.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PeriodicSalesAcc.Properties.Columns17"))), resources.GetString("lkp_PeriodicSalesAcc.Properties.Columns18"), ((bool)(resources.GetObject("lkp_PeriodicSalesAcc.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PeriodicSalesAcc.Properties.Columns20"))))});
            this.lkp_PeriodicSalesAcc.Properties.NullText = resources.GetString("lkp_PeriodicSalesAcc.Properties.NullText");
            this.lkp_PeriodicSalesAcc.Properties.NullValuePrompt = resources.GetString("lkp_PeriodicSalesAcc.Properties.NullValuePrompt");
            this.lkp_PeriodicSalesAcc.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_PeriodicSalesAcc.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_PeriodicSalesAcc.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            // 
            // lkp_PeriodicPurchaseAcc
            // 
            resources.ApplyResources(this.lkp_PeriodicPurchaseAcc, "lkp_PeriodicPurchaseAcc");
            this.lkp_PeriodicPurchaseAcc.EnterMoveNextControl = true;
            this.lkp_PeriodicPurchaseAcc.MenuManager = this.barManager1;
            this.lkp_PeriodicPurchaseAcc.Name = "lkp_PeriodicPurchaseAcc";
            this.lkp_PeriodicPurchaseAcc.Properties.AccessibleDescription = resources.GetString("lkp_PeriodicPurchaseAcc.Properties.AccessibleDescription");
            this.lkp_PeriodicPurchaseAcc.Properties.AccessibleName = resources.GetString("lkp_PeriodicPurchaseAcc.Properties.AccessibleName");
            this.lkp_PeriodicPurchaseAcc.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_PeriodicPurchaseAcc.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_PeriodicPurchaseAcc.Properties.Appearance.FontSizeDelta")));
            this.lkp_PeriodicPurchaseAcc.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_PeriodicPurchaseAcc.Properties.Appearance.FontStyleDelta")));
            this.lkp_PeriodicPurchaseAcc.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_PeriodicPurchaseAcc.Properties.Appearance.GradientMode")));
            this.lkp_PeriodicPurchaseAcc.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_PeriodicPurchaseAcc.Properties.Appearance.Image")));
            this.lkp_PeriodicPurchaseAcc.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_PeriodicPurchaseAcc.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_PeriodicPurchaseAcc.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_PeriodicPurchaseAcc.Properties.AutoHeight")));
            this.lkp_PeriodicPurchaseAcc.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_PeriodicPurchaseAcc.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_PeriodicPurchaseAcc.Properties.Buttons"))))});
            this.lkp_PeriodicPurchaseAcc.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PeriodicPurchaseAcc.Properties.Columns"), resources.GetString("lkp_PeriodicPurchaseAcc.Properties.Columns1"), ((int)(resources.GetObject("lkp_PeriodicPurchaseAcc.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PeriodicPurchaseAcc.Properties.Columns3"))), resources.GetString("lkp_PeriodicPurchaseAcc.Properties.Columns4"), ((bool)(resources.GetObject("lkp_PeriodicPurchaseAcc.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PeriodicPurchaseAcc.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PeriodicPurchaseAcc.Properties.Columns7"), resources.GetString("lkp_PeriodicPurchaseAcc.Properties.Columns8"), ((int)(resources.GetObject("lkp_PeriodicPurchaseAcc.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PeriodicPurchaseAcc.Properties.Columns10"))), resources.GetString("lkp_PeriodicPurchaseAcc.Properties.Columns11"), ((bool)(resources.GetObject("lkp_PeriodicPurchaseAcc.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PeriodicPurchaseAcc.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PeriodicPurchaseAcc.Properties.Columns14"), resources.GetString("lkp_PeriodicPurchaseAcc.Properties.Columns15"), ((int)(resources.GetObject("lkp_PeriodicPurchaseAcc.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PeriodicPurchaseAcc.Properties.Columns17"))), resources.GetString("lkp_PeriodicPurchaseAcc.Properties.Columns18"), ((bool)(resources.GetObject("lkp_PeriodicPurchaseAcc.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PeriodicPurchaseAcc.Properties.Columns20"))))});
            this.lkp_PeriodicPurchaseAcc.Properties.NullText = resources.GetString("lkp_PeriodicPurchaseAcc.Properties.NullText");
            this.lkp_PeriodicPurchaseAcc.Properties.NullValuePrompt = resources.GetString("lkp_PeriodicPurchaseAcc.Properties.NullValuePrompt");
            this.lkp_PeriodicPurchaseAcc.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_PeriodicPurchaseAcc.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_PeriodicPurchaseAcc.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.lkp_PeriodicPurchaseAcc.EditValueChanged += new System.EventHandler(this.lookUpEdit2_EditValueChanged);
            // 
            // lblSalesAccount
            // 
            resources.ApplyResources(this.lblSalesAccount, "lblSalesAccount");
            this.lblSalesAccount.Name = "lblSalesAccount";
            // 
            // labelControl12
            // 
            resources.ApplyResources(this.labelControl12, "labelControl12");
            this.labelControl12.Name = "labelControl12";
            // 
            // lkp_PeriodicSalesReturnAcc
            // 
            resources.ApplyResources(this.lkp_PeriodicSalesReturnAcc, "lkp_PeriodicSalesReturnAcc");
            this.lkp_PeriodicSalesReturnAcc.EnterMoveNextControl = true;
            this.lkp_PeriodicSalesReturnAcc.MenuManager = this.barManager1;
            this.lkp_PeriodicSalesReturnAcc.Name = "lkp_PeriodicSalesReturnAcc";
            this.lkp_PeriodicSalesReturnAcc.Properties.AccessibleDescription = resources.GetString("lkp_PeriodicSalesReturnAcc.Properties.AccessibleDescription");
            this.lkp_PeriodicSalesReturnAcc.Properties.AccessibleName = resources.GetString("lkp_PeriodicSalesReturnAcc.Properties.AccessibleName");
            this.lkp_PeriodicSalesReturnAcc.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_PeriodicSalesReturnAcc.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_PeriodicSalesReturnAcc.Properties.Appearance.FontSizeDelta")));
            this.lkp_PeriodicSalesReturnAcc.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_PeriodicSalesReturnAcc.Properties.Appearance.FontStyleDelta")));
            this.lkp_PeriodicSalesReturnAcc.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_PeriodicSalesReturnAcc.Properties.Appearance.GradientMode")));
            this.lkp_PeriodicSalesReturnAcc.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_PeriodicSalesReturnAcc.Properties.Appearance.Image")));
            this.lkp_PeriodicSalesReturnAcc.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_PeriodicSalesReturnAcc.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_PeriodicSalesReturnAcc.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_PeriodicSalesReturnAcc.Properties.AutoHeight")));
            this.lkp_PeriodicSalesReturnAcc.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_PeriodicSalesReturnAcc.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_PeriodicSalesReturnAcc.Properties.Buttons"))))});
            this.lkp_PeriodicSalesReturnAcc.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PeriodicSalesReturnAcc.Properties.Columns"), resources.GetString("lkp_PeriodicSalesReturnAcc.Properties.Columns1"), ((int)(resources.GetObject("lkp_PeriodicSalesReturnAcc.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PeriodicSalesReturnAcc.Properties.Columns3"))), resources.GetString("lkp_PeriodicSalesReturnAcc.Properties.Columns4"), ((bool)(resources.GetObject("lkp_PeriodicSalesReturnAcc.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PeriodicSalesReturnAcc.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PeriodicSalesReturnAcc.Properties.Columns7"), resources.GetString("lkp_PeriodicSalesReturnAcc.Properties.Columns8"), ((int)(resources.GetObject("lkp_PeriodicSalesReturnAcc.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PeriodicSalesReturnAcc.Properties.Columns10"))), resources.GetString("lkp_PeriodicSalesReturnAcc.Properties.Columns11"), ((bool)(resources.GetObject("lkp_PeriodicSalesReturnAcc.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PeriodicSalesReturnAcc.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PeriodicSalesReturnAcc.Properties.Columns14"), resources.GetString("lkp_PeriodicSalesReturnAcc.Properties.Columns15"), ((int)(resources.GetObject("lkp_PeriodicSalesReturnAcc.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PeriodicSalesReturnAcc.Properties.Columns17"))), resources.GetString("lkp_PeriodicSalesReturnAcc.Properties.Columns18"), ((bool)(resources.GetObject("lkp_PeriodicSalesReturnAcc.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PeriodicSalesReturnAcc.Properties.Columns20"))))});
            this.lkp_PeriodicSalesReturnAcc.Properties.NullText = resources.GetString("lkp_PeriodicSalesReturnAcc.Properties.NullText");
            this.lkp_PeriodicSalesReturnAcc.Properties.NullValuePrompt = resources.GetString("lkp_PeriodicSalesReturnAcc.Properties.NullValuePrompt");
            this.lkp_PeriodicSalesReturnAcc.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_PeriodicSalesReturnAcc.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_PeriodicSalesReturnAcc.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            // 
            // lkp_PeriodicPrDiscAcc
            // 
            resources.ApplyResources(this.lkp_PeriodicPrDiscAcc, "lkp_PeriodicPrDiscAcc");
            this.lkp_PeriodicPrDiscAcc.EnterMoveNextControl = true;
            this.lkp_PeriodicPrDiscAcc.MenuManager = this.barManager1;
            this.lkp_PeriodicPrDiscAcc.Name = "lkp_PeriodicPrDiscAcc";
            this.lkp_PeriodicPrDiscAcc.Properties.AccessibleDescription = resources.GetString("lkp_PeriodicPrDiscAcc.Properties.AccessibleDescription");
            this.lkp_PeriodicPrDiscAcc.Properties.AccessibleName = resources.GetString("lkp_PeriodicPrDiscAcc.Properties.AccessibleName");
            this.lkp_PeriodicPrDiscAcc.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_PeriodicPrDiscAcc.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_PeriodicPrDiscAcc.Properties.Appearance.FontSizeDelta")));
            this.lkp_PeriodicPrDiscAcc.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_PeriodicPrDiscAcc.Properties.Appearance.FontStyleDelta")));
            this.lkp_PeriodicPrDiscAcc.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_PeriodicPrDiscAcc.Properties.Appearance.GradientMode")));
            this.lkp_PeriodicPrDiscAcc.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_PeriodicPrDiscAcc.Properties.Appearance.Image")));
            this.lkp_PeriodicPrDiscAcc.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_PeriodicPrDiscAcc.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_PeriodicPrDiscAcc.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_PeriodicPrDiscAcc.Properties.AutoHeight")));
            this.lkp_PeriodicPrDiscAcc.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_PeriodicPrDiscAcc.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_PeriodicPrDiscAcc.Properties.Buttons"))))});
            this.lkp_PeriodicPrDiscAcc.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PeriodicPrDiscAcc.Properties.Columns"), resources.GetString("lkp_PeriodicPrDiscAcc.Properties.Columns1"), ((int)(resources.GetObject("lkp_PeriodicPrDiscAcc.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PeriodicPrDiscAcc.Properties.Columns3"))), resources.GetString("lkp_PeriodicPrDiscAcc.Properties.Columns4"), ((bool)(resources.GetObject("lkp_PeriodicPrDiscAcc.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PeriodicPrDiscAcc.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PeriodicPrDiscAcc.Properties.Columns7"), resources.GetString("lkp_PeriodicPrDiscAcc.Properties.Columns8"), ((int)(resources.GetObject("lkp_PeriodicPrDiscAcc.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PeriodicPrDiscAcc.Properties.Columns10"))), resources.GetString("lkp_PeriodicPrDiscAcc.Properties.Columns11"), ((bool)(resources.GetObject("lkp_PeriodicPrDiscAcc.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PeriodicPrDiscAcc.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PeriodicPrDiscAcc.Properties.Columns14"), resources.GetString("lkp_PeriodicPrDiscAcc.Properties.Columns15"), ((int)(resources.GetObject("lkp_PeriodicPrDiscAcc.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PeriodicPrDiscAcc.Properties.Columns17"))), resources.GetString("lkp_PeriodicPrDiscAcc.Properties.Columns18"), ((bool)(resources.GetObject("lkp_PeriodicPrDiscAcc.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PeriodicPrDiscAcc.Properties.Columns20"))))});
            this.lkp_PeriodicPrDiscAcc.Properties.NullText = resources.GetString("lkp_PeriodicPrDiscAcc.Properties.NullText");
            this.lkp_PeriodicPrDiscAcc.Properties.NullValuePrompt = resources.GetString("lkp_PeriodicPrDiscAcc.Properties.NullValuePrompt");
            this.lkp_PeriodicPrDiscAcc.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_PeriodicPrDiscAcc.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_PeriodicPrDiscAcc.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            // 
            // lblSalesReturnAccount
            // 
            resources.ApplyResources(this.lblSalesReturnAccount, "lblSalesReturnAccount");
            this.lblSalesReturnAccount.Name = "lblSalesReturnAccount";
            // 
            // lkp_PeriodicSalesDiscAcc
            // 
            resources.ApplyResources(this.lkp_PeriodicSalesDiscAcc, "lkp_PeriodicSalesDiscAcc");
            this.lkp_PeriodicSalesDiscAcc.EnterMoveNextControl = true;
            this.lkp_PeriodicSalesDiscAcc.MenuManager = this.barManager1;
            this.lkp_PeriodicSalesDiscAcc.Name = "lkp_PeriodicSalesDiscAcc";
            this.lkp_PeriodicSalesDiscAcc.Properties.AccessibleDescription = resources.GetString("lkp_PeriodicSalesDiscAcc.Properties.AccessibleDescription");
            this.lkp_PeriodicSalesDiscAcc.Properties.AccessibleName = resources.GetString("lkp_PeriodicSalesDiscAcc.Properties.AccessibleName");
            this.lkp_PeriodicSalesDiscAcc.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_PeriodicSalesDiscAcc.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_PeriodicSalesDiscAcc.Properties.Appearance.FontSizeDelta")));
            this.lkp_PeriodicSalesDiscAcc.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_PeriodicSalesDiscAcc.Properties.Appearance.FontStyleDelta")));
            this.lkp_PeriodicSalesDiscAcc.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_PeriodicSalesDiscAcc.Properties.Appearance.GradientMode")));
            this.lkp_PeriodicSalesDiscAcc.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_PeriodicSalesDiscAcc.Properties.Appearance.Image")));
            this.lkp_PeriodicSalesDiscAcc.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_PeriodicSalesDiscAcc.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_PeriodicSalesDiscAcc.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_PeriodicSalesDiscAcc.Properties.AutoHeight")));
            this.lkp_PeriodicSalesDiscAcc.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_PeriodicSalesDiscAcc.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_PeriodicSalesDiscAcc.Properties.Buttons"))))});
            this.lkp_PeriodicSalesDiscAcc.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PeriodicSalesDiscAcc.Properties.Columns"), resources.GetString("lkp_PeriodicSalesDiscAcc.Properties.Columns1"), ((int)(resources.GetObject("lkp_PeriodicSalesDiscAcc.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PeriodicSalesDiscAcc.Properties.Columns3"))), resources.GetString("lkp_PeriodicSalesDiscAcc.Properties.Columns4"), ((bool)(resources.GetObject("lkp_PeriodicSalesDiscAcc.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PeriodicSalesDiscAcc.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PeriodicSalesDiscAcc.Properties.Columns7"), resources.GetString("lkp_PeriodicSalesDiscAcc.Properties.Columns8"), ((int)(resources.GetObject("lkp_PeriodicSalesDiscAcc.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PeriodicSalesDiscAcc.Properties.Columns10"))), resources.GetString("lkp_PeriodicSalesDiscAcc.Properties.Columns11"), ((bool)(resources.GetObject("lkp_PeriodicSalesDiscAcc.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PeriodicSalesDiscAcc.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PeriodicSalesDiscAcc.Properties.Columns14"), resources.GetString("lkp_PeriodicSalesDiscAcc.Properties.Columns15"), ((int)(resources.GetObject("lkp_PeriodicSalesDiscAcc.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PeriodicSalesDiscAcc.Properties.Columns17"))), resources.GetString("lkp_PeriodicSalesDiscAcc.Properties.Columns18"), ((bool)(resources.GetObject("lkp_PeriodicSalesDiscAcc.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PeriodicSalesDiscAcc.Properties.Columns20"))))});
            this.lkp_PeriodicSalesDiscAcc.Properties.NullText = resources.GetString("lkp_PeriodicSalesDiscAcc.Properties.NullText");
            this.lkp_PeriodicSalesDiscAcc.Properties.NullValuePrompt = resources.GetString("lkp_PeriodicSalesDiscAcc.Properties.NullValuePrompt");
            this.lkp_PeriodicSalesDiscAcc.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_PeriodicSalesDiscAcc.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_PeriodicSalesDiscAcc.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            // 
            // labelControl14
            // 
            resources.ApplyResources(this.labelControl14, "labelControl14");
            this.labelControl14.Name = "labelControl14";
            // 
            // lkp_PeriodicOpenInvAcc
            // 
            resources.ApplyResources(this.lkp_PeriodicOpenInvAcc, "lkp_PeriodicOpenInvAcc");
            this.lkp_PeriodicOpenInvAcc.EnterMoveNextControl = true;
            this.lkp_PeriodicOpenInvAcc.MenuManager = this.barManager1;
            this.lkp_PeriodicOpenInvAcc.Name = "lkp_PeriodicOpenInvAcc";
            this.lkp_PeriodicOpenInvAcc.Properties.AccessibleDescription = resources.GetString("lkp_PeriodicOpenInvAcc.Properties.AccessibleDescription");
            this.lkp_PeriodicOpenInvAcc.Properties.AccessibleName = resources.GetString("lkp_PeriodicOpenInvAcc.Properties.AccessibleName");
            this.lkp_PeriodicOpenInvAcc.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_PeriodicOpenInvAcc.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_PeriodicOpenInvAcc.Properties.Appearance.FontSizeDelta")));
            this.lkp_PeriodicOpenInvAcc.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_PeriodicOpenInvAcc.Properties.Appearance.FontStyleDelta")));
            this.lkp_PeriodicOpenInvAcc.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_PeriodicOpenInvAcc.Properties.Appearance.GradientMode")));
            this.lkp_PeriodicOpenInvAcc.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_PeriodicOpenInvAcc.Properties.Appearance.Image")));
            this.lkp_PeriodicOpenInvAcc.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_PeriodicOpenInvAcc.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_PeriodicOpenInvAcc.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_PeriodicOpenInvAcc.Properties.AutoHeight")));
            this.lkp_PeriodicOpenInvAcc.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_PeriodicOpenInvAcc.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_PeriodicOpenInvAcc.Properties.Buttons"))))});
            this.lkp_PeriodicOpenInvAcc.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PeriodicOpenInvAcc.Properties.Columns"), resources.GetString("lkp_PeriodicOpenInvAcc.Properties.Columns1"), ((int)(resources.GetObject("lkp_PeriodicOpenInvAcc.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PeriodicOpenInvAcc.Properties.Columns3"))), resources.GetString("lkp_PeriodicOpenInvAcc.Properties.Columns4"), ((bool)(resources.GetObject("lkp_PeriodicOpenInvAcc.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PeriodicOpenInvAcc.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PeriodicOpenInvAcc.Properties.Columns7"), resources.GetString("lkp_PeriodicOpenInvAcc.Properties.Columns8"), ((int)(resources.GetObject("lkp_PeriodicOpenInvAcc.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PeriodicOpenInvAcc.Properties.Columns10"))), resources.GetString("lkp_PeriodicOpenInvAcc.Properties.Columns11"), ((bool)(resources.GetObject("lkp_PeriodicOpenInvAcc.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PeriodicOpenInvAcc.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_PeriodicOpenInvAcc.Properties.Columns14"), resources.GetString("lkp_PeriodicOpenInvAcc.Properties.Columns15"), ((int)(resources.GetObject("lkp_PeriodicOpenInvAcc.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_PeriodicOpenInvAcc.Properties.Columns17"))), resources.GetString("lkp_PeriodicOpenInvAcc.Properties.Columns18"), ((bool)(resources.GetObject("lkp_PeriodicOpenInvAcc.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_PeriodicOpenInvAcc.Properties.Columns20"))))});
            this.lkp_PeriodicOpenInvAcc.Properties.NullText = resources.GetString("lkp_PeriodicOpenInvAcc.Properties.NullText");
            this.lkp_PeriodicOpenInvAcc.Properties.NullValuePrompt = resources.GetString("lkp_PeriodicOpenInvAcc.Properties.NullValuePrompt");
            this.lkp_PeriodicOpenInvAcc.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_PeriodicOpenInvAcc.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_PeriodicOpenInvAcc.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            // 
            // lblOpenInventoryAccount
            // 
            resources.ApplyResources(this.lblOpenInventoryAccount, "lblOpenInventoryAccount");
            this.lblOpenInventoryAccount.Name = "lblOpenInventoryAccount";
            // 
            // tab_PerpetualAcc
            // 
            resources.ApplyResources(this.tab_PerpetualAcc, "tab_PerpetualAcc");
            this.tab_PerpetualAcc.Controls.Add(this.labelControl8);
            this.tab_PerpetualAcc.Controls.Add(this.lkp_PerPetualSalesAcc);
            this.tab_PerpetualAcc.Controls.Add(this.lkp_PerPetualCostOfSoldGoods);
            this.tab_PerpetualAcc.Controls.Add(this.lblSales);
            this.tab_PerpetualAcc.Controls.Add(this.lblPurchaseDiscount);
            this.tab_PerpetualAcc.Controls.Add(this.lkp_PerPetualSalesReturnAcc);
            this.tab_PerpetualAcc.Controls.Add(this.lkp_PerPetualPurchaseDiscountAcc);
            this.tab_PerpetualAcc.Controls.Add(this.lblSalesReturn);
            this.tab_PerpetualAcc.Controls.Add(this.lkp_PerPetualSalesDiscount);
            this.tab_PerpetualAcc.Controls.Add(this.lkp_PerPetualInventoryAcc);
            this.tab_PerpetualAcc.Controls.Add(this.lblSalesDiscount);
            this.tab_PerpetualAcc.Controls.Add(this.lblInventory);
            this.tab_PerpetualAcc.Name = "tab_PerpetualAcc";
            // 
            // lbl_PriceList
            // 
            resources.ApplyResources(this.lbl_PriceList, "lbl_PriceList");
            this.lbl_PriceList.Name = "lbl_PriceList";
            // 
            // lkpPriceLevel
            // 
            resources.ApplyResources(this.lkpPriceLevel, "lkpPriceLevel");
            this.lkpPriceLevel.EnterMoveNextControl = true;
            this.lkpPriceLevel.Name = "lkpPriceLevel";
            this.lkpPriceLevel.Properties.AccessibleDescription = resources.GetString("lkpPriceLevel.Properties.AccessibleDescription");
            this.lkpPriceLevel.Properties.AccessibleName = resources.GetString("lkpPriceLevel.Properties.AccessibleName");
            this.lkpPriceLevel.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkpPriceLevel.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkpPriceLevel.Properties.Appearance.FontSizeDelta")));
            this.lkpPriceLevel.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpPriceLevel.Properties.Appearance.FontStyleDelta")));
            this.lkpPriceLevel.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpPriceLevel.Properties.Appearance.GradientMode")));
            this.lkpPriceLevel.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkpPriceLevel.Properties.Appearance.Image")));
            this.lkpPriceLevel.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpPriceLevel.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpPriceLevel.Properties.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.lkpPriceLevel.Properties.AppearanceDropDown.FontSizeDelta = ((int)(resources.GetObject("lkpPriceLevel.Properties.AppearanceDropDown.FontSizeDelta")));
            this.lkpPriceLevel.Properties.AppearanceDropDown.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpPriceLevel.Properties.AppearanceDropDown.FontStyleDelta")));
            this.lkpPriceLevel.Properties.AppearanceDropDown.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpPriceLevel.Properties.AppearanceDropDown.GradientMode")));
            this.lkpPriceLevel.Properties.AppearanceDropDown.Image = ((System.Drawing.Image)(resources.GetObject("lkpPriceLevel.Properties.AppearanceDropDown.Image")));
            this.lkpPriceLevel.Properties.AppearanceDropDown.Options.UseTextOptions = true;
            this.lkpPriceLevel.Properties.AppearanceDropDown.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkpPriceLevel.Properties.AppearanceDropDownHeader.FontSizeDelta = ((int)(resources.GetObject("lkpPriceLevel.Properties.AppearanceDropDownHeader.FontSizeDelta")));
            this.lkpPriceLevel.Properties.AppearanceDropDownHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpPriceLevel.Properties.AppearanceDropDownHeader.FontStyleDelta")));
            this.lkpPriceLevel.Properties.AppearanceDropDownHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpPriceLevel.Properties.AppearanceDropDownHeader.GradientMode")));
            this.lkpPriceLevel.Properties.AppearanceDropDownHeader.Image = ((System.Drawing.Image)(resources.GetObject("lkpPriceLevel.Properties.AppearanceDropDownHeader.Image")));
            this.lkpPriceLevel.Properties.AppearanceDropDownHeader.Options.UseTextOptions = true;
            this.lkpPriceLevel.Properties.AppearanceDropDownHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkpPriceLevel.Properties.AutoHeight = ((bool)(resources.GetObject("lkpPriceLevel.Properties.AutoHeight")));
            this.lkpPriceLevel.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkpPriceLevel.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpPriceLevel.Properties.Buttons"))))});
            this.lkpPriceLevel.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpPriceLevel.Properties.Columns"), resources.GetString("lkpPriceLevel.Properties.Columns1"), ((int)(resources.GetObject("lkpPriceLevel.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpPriceLevel.Properties.Columns3"))), resources.GetString("lkpPriceLevel.Properties.Columns4"), ((bool)(resources.GetObject("lkpPriceLevel.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpPriceLevel.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpPriceLevel.Properties.Columns7"), resources.GetString("lkpPriceLevel.Properties.Columns8"), ((int)(resources.GetObject("lkpPriceLevel.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpPriceLevel.Properties.Columns10"))), resources.GetString("lkpPriceLevel.Properties.Columns11"), ((bool)(resources.GetObject("lkpPriceLevel.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpPriceLevel.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpPriceLevel.Properties.Columns14"), resources.GetString("lkpPriceLevel.Properties.Columns15"), ((int)(resources.GetObject("lkpPriceLevel.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpPriceLevel.Properties.Columns17"))), resources.GetString("lkpPriceLevel.Properties.Columns18"), ((bool)(resources.GetObject("lkpPriceLevel.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpPriceLevel.Properties.Columns20")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpPriceLevel.Properties.Columns21"), resources.GetString("lkpPriceLevel.Properties.Columns22"), ((int)(resources.GetObject("lkpPriceLevel.Properties.Columns23"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpPriceLevel.Properties.Columns24"))), resources.GetString("lkpPriceLevel.Properties.Columns25"), ((bool)(resources.GetObject("lkpPriceLevel.Properties.Columns26"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpPriceLevel.Properties.Columns27"))))});
            this.lkpPriceLevel.Properties.DisplayMember = "PLNAme";
            this.lkpPriceLevel.Properties.NullText = resources.GetString("lkpPriceLevel.Properties.NullText");
            this.lkpPriceLevel.Properties.NullValuePrompt = resources.GetString("lkpPriceLevel.Properties.NullValuePrompt");
            this.lkpPriceLevel.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkpPriceLevel.Properties.NullValuePromptShowForEmptyValue")));
            this.lkpPriceLevel.Properties.PopupSizeable = false;
            this.lkpPriceLevel.Properties.ValueMember = "PriceLevelId";
            // 
            // labelControl10
            // 
            resources.ApplyResources(this.labelControl10, "labelControl10");
            this.labelControl10.Name = "labelControl10";
            // 
            // Mobile
            // 
            resources.ApplyResources(this.Mobile, "Mobile");
            this.Mobile.EnterMoveNextControl = true;
            this.Mobile.Name = "Mobile";
            this.Mobile.Properties.AccessibleDescription = resources.GetString("Mobile.Properties.AccessibleDescription");
            this.Mobile.Properties.AccessibleName = resources.GetString("Mobile.Properties.AccessibleName");
            this.Mobile.Properties.AutoHeight = ((bool)(resources.GetObject("Mobile.Properties.AutoHeight")));
            this.Mobile.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("Mobile.Properties.Mask.AutoComplete")));
            this.Mobile.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("Mobile.Properties.Mask.BeepOnError")));
            this.Mobile.Properties.Mask.EditMask = resources.GetString("Mobile.Properties.Mask.EditMask");
            this.Mobile.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("Mobile.Properties.Mask.IgnoreMaskBlank")));
            this.Mobile.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("Mobile.Properties.Mask.MaskType")));
            this.Mobile.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("Mobile.Properties.Mask.PlaceHolder")));
            this.Mobile.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("Mobile.Properties.Mask.SaveLiteral")));
            this.Mobile.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("Mobile.Properties.Mask.ShowPlaceHolders")));
            this.Mobile.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("Mobile.Properties.Mask.UseMaskAsDisplayFormat")));
            this.Mobile.Properties.MaxLength = 50;
            this.Mobile.Properties.NullValuePrompt = resources.GetString("Mobile.Properties.NullValuePrompt");
            this.Mobile.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("Mobile.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // IsStopped
            // 
            resources.ApplyResources(this.IsStopped, "IsStopped");
            this.IsStopped.MenuManager = this.barManager1;
            this.IsStopped.Name = "IsStopped";
            this.IsStopped.Properties.AccessibleDescription = resources.GetString("IsStopped.Properties.AccessibleDescription");
            this.IsStopped.Properties.AccessibleName = resources.GetString("IsStopped.Properties.AccessibleName");
            this.IsStopped.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("IsStopped.Properties.Appearance.FontSizeDelta")));
            this.IsStopped.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("IsStopped.Properties.Appearance.FontStyleDelta")));
            this.IsStopped.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("IsStopped.Properties.Appearance.GradientMode")));
            this.IsStopped.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("IsStopped.Properties.Appearance.Image")));
            this.IsStopped.Properties.Appearance.Options.UseTextOptions = true;
            this.IsStopped.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.IsStopped.Properties.AutoHeight = ((bool)(resources.GetObject("IsStopped.Properties.AutoHeight")));
            this.IsStopped.Properties.Caption = resources.GetString("IsStopped.Properties.Caption");
            this.IsStopped.Properties.DisplayValueChecked = resources.GetString("IsStopped.Properties.DisplayValueChecked");
            this.IsStopped.Properties.DisplayValueGrayed = resources.GetString("IsStopped.Properties.DisplayValueGrayed");
            this.IsStopped.Properties.DisplayValueUnchecked = resources.GetString("IsStopped.Properties.DisplayValueUnchecked");
            this.IsStopped.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("IsStopped.Properties.GlyphAlignment")));
            // 
            // frm_IC_Store
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.IsStopped);
            this.Controls.Add(this.Mobile);
            this.Controls.Add(this.labelControl10);
            this.Controls.Add(this.lkpPriceLevel);
            this.Controls.Add(this.lbl_PriceList);
            this.Controls.Add(this.xtraTabControl1);
            this.Controls.Add(this.lkpStore);
            this.Controls.Add(this.labelControl7);
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.txt_Manager);
            this.Controls.Add(this.cb_CostMethod);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.txt_StoreCode);
            this.Controls.Add(this.btnNext);
            this.Controls.Add(this.btnPrev);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.txtStoreNameEn);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.txtStoreNameAr);
            this.Controls.Add(this.txtTel);
            this.Controls.Add(this.labelControl9);
            this.Controls.Add(this.labelControl15);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.txtAddress);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.KeyPreview = true;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "frm_IC_Store";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_IC_Store_FormClosing);
            this.Load += new System.EventHandler(this.frm_IC_Store_Load);
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.frm_IC_Store_KeyUp);
            ((System.ComponentModel.ISupportInitialize)(this.txtTel.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAddress.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtStoreNameAr.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtStoreNameEn.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_StoreCode.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cb_CostMethod.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Manager.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpStore.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_PerPetualCostOfSoldGoods.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_PerPetualPurchaseDiscountAcc.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_PerPetualSalesDiscount.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_PerPetualInventoryAcc.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_PerPetualSalesReturnAcc.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_PerPetualSalesAcc.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.tab_PeriodicAcc.ResumeLayout(false);
            this.tab_PeriodicAcc.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chk_autoCreateAccs.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_PeriodicCloseInvAcc.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_PeriodicPrReturnAcc.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_PeriodicSalesAcc.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_PeriodicPurchaseAcc.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_PeriodicSalesReturnAcc.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_PeriodicPrDiscAcc.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_PeriodicSalesDiscAcc.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_PeriodicOpenInvAcc.Properties)).EndInit();
            this.tab_PerpetualAcc.ResumeLayout(false);
            this.tab_PerpetualAcc.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lkpPriceLevel.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.Mobile.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.IsStopped.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControl15;
        private DevExpress.XtraEditors.TextEdit txtTel;
        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnSave;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtnHelp;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.TextEdit txtAddress;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.TextEdit txtStoreNameAr;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.TextEdit txtStoreNameEn;
        private DevExpress.XtraBars.BarButtonItem barBtnList;
        private DevExpress.XtraEditors.TextEdit txt_StoreCode;
        private DevExpress.XtraEditors.SimpleButton btnNext;
        private DevExpress.XtraEditors.SimpleButton btnPrev;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraBars.BarButtonItem barBtnNew;
        private DevExpress.XtraBars.BarButtonItem barBtnDelete;
        private DevExpress.XtraEditors.ImageComboBoxEdit cb_CostMethod;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.TextEdit txt_Manager;
        private DevExpress.XtraEditors.LookUpEdit lkpStore;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.LabelControl lblSales;
        private DevExpress.XtraEditors.LookUpEdit lkp_PerPetualSalesAcc;
        private DevExpress.XtraEditors.LabelControl lblSalesReturn;
        private DevExpress.XtraEditors.LookUpEdit lkp_PerPetualSalesReturnAcc;
        private DevExpress.XtraEditors.LabelControl lblInventory;
        private DevExpress.XtraEditors.LookUpEdit lkp_PerPetualInventoryAcc;
        private DevExpress.XtraEditors.LabelControl lblPurchaseDiscount;
        private DevExpress.XtraEditors.LookUpEdit lkp_PerPetualPurchaseDiscountAcc;
        private DevExpress.XtraEditors.LookUpEdit lkp_PerPetualSalesDiscount;
        private DevExpress.XtraEditors.LabelControl lblSalesDiscount;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.LookUpEdit lkp_PerPetualCostOfSoldGoods;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage tab_PeriodicAcc;
        private DevExpress.XtraTab.XtraTabPage tab_PerpetualAcc;
        private DevExpress.XtraEditors.LabelControl lblPurchasesAccount;
        private DevExpress.XtraEditors.LookUpEdit lkp_PeriodicSalesAcc;
        private DevExpress.XtraEditors.LookUpEdit lkp_PeriodicPurchaseAcc;
        private DevExpress.XtraEditors.LabelControl lblSalesAccount;
        private DevExpress.XtraEditors.LabelControl labelControl12;
        private DevExpress.XtraEditors.LookUpEdit lkp_PeriodicSalesReturnAcc;
        private DevExpress.XtraEditors.LookUpEdit lkp_PeriodicPrDiscAcc;
        private DevExpress.XtraEditors.LabelControl lblSalesReturnAccount;
        private DevExpress.XtraEditors.LookUpEdit lkp_PeriodicSalesDiscAcc;
        private DevExpress.XtraEditors.LookUpEdit lkp_PeriodicOpenInvAcc;
        private DevExpress.XtraEditors.LabelControl labelControl14;
        private DevExpress.XtraEditors.LabelControl lblOpenInventoryAccount;
        private DevExpress.XtraEditors.LabelControl lblPurchasesReturnAccount;
        private DevExpress.XtraEditors.LookUpEdit lkp_PeriodicPrReturnAcc;
        private DevExpress.XtraEditors.LookUpEdit lkp_PeriodicCloseInvAcc;
        private DevExpress.XtraEditors.LabelControl lblCloseInventoryAccount;
        private DevExpress.XtraEditors.CheckEdit chk_autoCreateAccs;
        private DevExpress.XtraEditors.LabelControl lbl_PriceList;
        private DevExpress.XtraEditors.LookUpEdit lkpPriceLevel;
        private DevExpress.XtraEditors.TextEdit Mobile;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraEditors.CheckEdit IsStopped;
    }
}