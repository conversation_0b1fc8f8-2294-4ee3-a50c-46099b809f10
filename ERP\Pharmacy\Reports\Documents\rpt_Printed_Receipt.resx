<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="xrTable2.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="xrTable2.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="xrTable2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 0</value>
  </data>
  <data name="cell_Total.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="cell_Total.Text" xml:space="preserve">
    <value>اجمالي</value>
  </data>
  <data name="cell_Total.Weight" type="System.Double, mscorlib">
    <value>0.75237988604066264</value>
  </data>
  <data name="cell_Qty.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="cell_Qty.Text" xml:space="preserve">
    <value>كمية</value>
  </data>
  <data name="cell_Qty.Weight" type="System.Double, mscorlib">
    <value>0.64531715854687377</value>
  </data>
  <data name="cell_Price.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="cell_Price.Text" xml:space="preserve">
    <value>السعر</value>
  </data>
  <data name="cell_Price.Weight" type="System.Double, mscorlib">
    <value>0.70668642698304573</value>
  </data>
  <data name="cell_Item.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="cell_Item.Text" xml:space="preserve">
    <value>الصنف</value>
  </data>
  <data name="cell_Item.Weight" type="System.Double, mscorlib">
    <value>2.1875132206102306</value>
  </data>
  <data name="cell_Serial.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="cell_Serial.Text" xml:space="preserve">
    <value>م</value>
  </data>
  <data name="cell_Serial.Weight" type="System.Double, mscorlib">
    <value>0.2794572634564092</value>
  </data>
  <data name="xrTableRow2.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="xrTableRow2.Weight" type="System.Double, mscorlib">
    <value>0.520833353357991</value>
  </data>
  <data name="xrTable2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>739.4167, 66.14584</value>
  </data>
  <data name="xrTable2.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="Detail.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="Detail.HeightF" type="System.Single, mscorlib">
    <value>66.14584</value>
  </data>
  <data name="Detail.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="qrCode.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="qrCode.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>25.0000076, 626.337463</value>
  </data>
  <data name="qrCode.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>457.200043, 457.2</value>
  </data>
  <data name="qrCode.Text" xml:space="preserve">
    <value>LinkIT Information Technology</value>
  </data>
  <data name="xrLabel2.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="xrLabel2.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrLabel2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>512.5867, 61.07062</value>
  </data>
  <data name="xrLabel2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>211.9531, 62.22989</value>
  </data>
  <data name="xrLabel2.Text" xml:space="preserve">
    <value>فاتورة مبيعات</value>
  </data>
  <data name="xrLabel2.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_User.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="lbl_User.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lbl_User.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>28.46877, 496.6803</value>
  </data>
  <data name="lbl_User.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>484.118, 62.23001</value>
  </data>
  <data name="lbl_User.Text" xml:space="preserve">
    <value>..</value>
  </data>
  <data name="lbl_User.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_store.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="lbl_store.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_store.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>28.46877, 185.5306</value>
  </data>
  <data name="lbl_store.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>484.1181, 62.22989</value>
  </data>
  <data name="lbl_store.Text" xml:space="preserve">
    <value>الرئيسي</value>
  </data>
  <data name="lbl_store.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_date.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="lbl_date.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_date.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>28.46877, 123.3007</value>
  </data>
  <data name="lbl_date.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>484.1181, 62.22988</value>
  </data>
  <data name="lbl_date.Text" xml:space="preserve">
    <value>1/2/2013</value>
  </data>
  <data name="lbl_date.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel3.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="xrLabel3.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrLabel3.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>512.5867, 247.7606</value>
  </data>
  <data name="xrLabel3.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>211.953, 62.22995</value>
  </data>
  <data name="xrLabel3.Text" xml:space="preserve">
    <value>المستخدم</value>
  </data>
  <data name="xrLabel3.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel7.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="xrLabel7.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrLabel7.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>512.5867, 123.3007</value>
  </data>
  <data name="xrLabel7.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>211.953, 62.22996</value>
  </data>
  <data name="xrLabel7.Text" xml:space="preserve">
    <value>التاريخ</value>
  </data>
  <data name="xrLabel7.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel6.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="xrLabel6.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel6.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>512.5867, 496.6803</value>
  </data>
  <data name="xrLabel6.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>211.953, 62.22995</value>
  </data>
  <data name="xrLabel6.Text" xml:space="preserve">
    <value>البائع</value>
  </data>
  <data name="xrLabel6.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_Number.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="lbl_Number.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="lbl_Number.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>28.46877, 61.07064</value>
  </data>
  <data name="lbl_Number.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>484.1181, 62.22988</value>
  </data>
  <data name="lbl_Number.Text" xml:space="preserve">
    <value>123</value>
  </data>
  <data name="lbl_Number.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel8.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="xrLabel8.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrLabel8.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>512.5867, 185.5306</value>
  </data>
  <data name="xrLabel8.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>211.953, 62.22995</value>
  </data>
  <data name="xrLabel8.Text" xml:space="preserve">
    <value>الفرع</value>
  </data>
  <data name="xrLabel8.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_notes.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="lbl_notes.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_notes.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>28.46877, 372.2204</value>
  </data>
  <data name="lbl_notes.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>484.1181, 62.22995</value>
  </data>
  <data name="lbl_notes.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="xrLabel12.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="xrLabel12.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel12.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>512.5867, 434.4504</value>
  </data>
  <data name="xrLabel12.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>211.9531, 62.22992</value>
  </data>
  <data name="xrLabel12.Text" xml:space="preserve">
    <value>الخزينة</value>
  </data>
  <data name="xrLabel12.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel12.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_Drawer.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="lbl_Drawer.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lbl_Drawer.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>28.46877, 434.4503</value>
  </data>
  <data name="lbl_Drawer.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>484.1181, 62.22995</value>
  </data>
  <data name="lbl_Drawer.Text" xml:space="preserve">
    <value>خزينة 1</value>
  </data>
  <data name="lbl_Drawer.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_Drawer.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="xrLabel9.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="xrLabel9.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrLabel9.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>512.5867, 372.2204</value>
  </data>
  <data name="xrLabel9.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>211.9531, 62.22998</value>
  </data>
  <data name="xrLabel9.Text" xml:space="preserve">
    <value>ملاحظات</value>
  </data>
  <data name="xrLabel9.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel4.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="xrLabel4.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="xrLabel4.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>28.46877, 247.7604</value>
  </data>
  <data name="xrLabel4.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>484.1181, 62.2299</value>
  </data>
  <data name="xrLabel4.Text" xml:space="preserve">
    <value>..</value>
  </data>
  <data name="xrLabel4.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel5.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="xrLabel5.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrLabel5.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>512.5867, 309.9905</value>
  </data>
  <data name="xrLabel5.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>211.953, 62.22995</value>
  </data>
  <data name="xrLabel5.Text" xml:space="preserve">
    <value>العميل</value>
  </data>
  <data name="xrLabel5.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_Customer.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="lbl_Customer.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_Customer.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>28.46877, 309.9905</value>
  </data>
  <data name="lbl_Customer.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>484.1181, 62.22995</value>
  </data>
  <data name="lbl_Customer.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="TopMargin.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="TopMargin.HeightF" type="System.Single, mscorlib">
    <value>1098.35413</value>
  </data>
  <data name="TopMargin.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="xrLabel11.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="xrLabel11.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt</value>
  </data>
  <data name="xrLabel11.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>302.009979, 25.3024616</value>
  </data>
  <data name="xrLabel11.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>419.5212, 48.26006</value>
  </data>
  <data name="xrLabel11.Text" xml:space="preserve">
    <value>الإجمالي</value>
  </data>
  <data name="xrLabel11.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lbl_Total.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="lbl_Total.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt</value>
  </data>
  <data name="lbl_Total.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>4.037221E-05, 24.99999</value>
  </data>
  <data name="lbl_Total.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>127.6958, 48.26006</value>
  </data>
  <data name="lbl_Total.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lbl_Remain.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="lbl_Remain.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt</value>
  </data>
  <data name="lbl_Remain.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>4.037221E-05, 218.3428</value>
  </data>
  <data name="lbl_Remain.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>127.6958, 48.26006</value>
  </data>
  <data name="lbl_Remain.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrLabel16.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="xrLabel16.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt</value>
  </data>
  <data name="xrLabel16.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>302.0101, 218.3428</value>
  </data>
  <data name="xrLabel16.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>419.5212, 48.26006</value>
  </data>
  <data name="xrLabel16.Text" xml:space="preserve">
    <value>المتبقي</value>
  </data>
  <data name="xrLabel16.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrLabel13.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="xrLabel13.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt</value>
  </data>
  <data name="xrLabel13.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>302.0101, 73.56257</value>
  </data>
  <data name="xrLabel13.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>419.5212, 48.26006</value>
  </data>
  <data name="xrLabel13.Text" xml:space="preserve">
    <value>الخصم</value>
  </data>
  <data name="xrLabel13.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lbl_Discount.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="lbl_Discount.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt</value>
  </data>
  <data name="lbl_Discount.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>4.037221E-05, 73.56256</value>
  </data>
  <data name="lbl_Discount.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>127.6958, 48.26006</value>
  </data>
  <data name="lbl_Discount.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrLabel10.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="xrLabel10.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt</value>
  </data>
  <data name="xrLabel10.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>302.0101, 170.0828</value>
  </data>
  <data name="xrLabel10.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>419.5212, 48.26006</value>
  </data>
  <data name="xrLabel10.Text" xml:space="preserve">
    <value>المدفوع</value>
  </data>
  <data name="xrLabel10.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lbl_Paid.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="lbl_Paid.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt</value>
  </data>
  <data name="lbl_Paid.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 170.0827</value>
  </data>
  <data name="lbl_Paid.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>127.6958, 48.26006</value>
  </data>
  <data name="lbl_Paid.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrLine2.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="xrLine2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>4.037221E-05, 0</value>
  </data>
  <data name="xrLine2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>750, 15.875</value>
  </data>
  <data name="xrLine1.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="xrLine1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>28.46875, 266.6028</value>
  </data>
  <data name="xrLine1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>693.0625, 15.875</value>
  </data>
  <data name="xrLabel1.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="xrLabel1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt</value>
  </data>
  <data name="xrLabel1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>302.01, 121.8226</value>
  </data>
  <data name="xrLabel1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>419.5212, 48.26006</value>
  </data>
  <data name="xrLabel1.Text" xml:space="preserve">
    <value>الصافي</value>
  </data>
  <data name="xrLabel1.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lbl_Net.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="lbl_Net.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt</value>
  </data>
  <data name="lbl_Net.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 121.8226</value>
  </data>
  <data name="lbl_Net.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>127.6958, 48.26006</value>
  </data>
  <data name="lbl_Net.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="BottomMargin.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="BottomMargin.HeightF" type="System.Single, mscorlib">
    <value>342.802856</value>
  </data>
  <data name="BottomMargin.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="xrTable1.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="xrTable1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrTable1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 0</value>
  </data>
  <data name="xrTableCell1.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="xrTableCell1.Text" xml:space="preserve">
    <value>اجمالي</value>
  </data>
  <data name="xrTableCell1.Weight" type="System.Double, mscorlib">
    <value>0.5107834027251229</value>
  </data>
  <data name="xrTableCell2.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="xrTableCell2.Text" xml:space="preserve">
    <value>كمية</value>
  </data>
  <data name="xrTableCell2.Weight" type="System.Double, mscorlib">
    <value>0.4380996685750711</value>
  </data>
  <data name="xrTableCell4.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="xrTableCell4.Text" xml:space="preserve">
    <value>السعر</value>
  </data>
  <data name="xrTableCell4.Weight" type="System.Double, mscorlib">
    <value>0.47976297306258453</value>
  </data>
  <data name="xrTableCell3.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="xrTableCell3.Text" xml:space="preserve">
    <value>الصنف</value>
  </data>
  <data name="xrTableCell3.Weight" type="System.Double, mscorlib">
    <value>1.4850814797266707</value>
  </data>
  <data name="xrTableCell6.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="xrTableCell6.Text" xml:space="preserve">
    <value>م</value>
  </data>
  <data name="xrTableCell6.Weight" type="System.Double, mscorlib">
    <value>0.1897199270334447</value>
  </data>
  <data name="xrTableRow1.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="xrTableRow1.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="xrTable1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>739.4165, 63.5</value>
  </data>
  <data name="xrTable1.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="PageHeader.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="PageHeader.HeightF" type="System.Single, mscorlib">
    <value>64</value>
  </data>
  <metadata name="$this.Language" type="System.Globalization.CultureInfo, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>ar-EG</value>
  </metadata>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.Dpi" type="System.Single, mscorlib">
    <value>254</value>
  </data>
  <data name="$this.Margins" type="System.Drawing.Printing.Margins, System.Drawing">
    <value>0, 0, 1098, 343</value>
  </data>
  <data name="$this.PageHeight" type="System.Int32, mscorlib">
    <value>5001</value>
  </data>
  <data name="$this.PageWidth" type="System.Int32, mscorlib">
    <value>750</value>
  </data>
  <data name="$this.PaperKind" type="System.Drawing.Printing.PaperKind, System.Drawing">
    <value>Custom</value>
  </data>
  <assembly alias="DevExpress.XtraReports.v15.1" name="DevExpress.XtraReports.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="$this.ReportUnit" type="DevExpress.XtraReports.UI.ReportUnit, DevExpress.XtraReports.v15.1">
    <value>TenthsOfAMillimeter</value>
  </data>
  <data name="$this.SnapGridSize" type="System.Single, mscorlib">
    <value>31.75</value>
  </data>
</root>