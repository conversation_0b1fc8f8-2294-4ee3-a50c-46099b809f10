﻿using DAL;
using DAL.Res;
using DevExpress.XtraPrinting;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Pharmacy.Forms
{
    public partial class frm_MaxItemsSalesCategory : DevExpress.XtraEditors.XtraForm
    {
        ERPDataContext DB = new ERPDataContext();
        UserPriv prvlg;
        public frm_MaxItemsSalesCategory()
        {
            InitializeComponent();
        }

        private void barBtnOk_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            DateTime From = Convert.ToDateTime(dtFromDate.EditValue);
            DateTime To = Convert.ToDateTime(dtToDate.EditValue);
            int Noofcategory =Convert.ToInt32(txt_No.EditValue);
            var soldQty = (from d in DB.SL_InvoiceDetails
                           join s in DB.SL_Invoices
                           on d.SL_InvoiceId equals s.SL_InvoiceId
                           
                           where dtFromDate.EditValue == null ? true : (s.InvoiceDate.Date >= From.Date)
                           where dtToDate.EditValue == null ? true : (s.InvoiceDate.Date <= To.Date)


                           join t in DB.IC_Items
                           on d.ItemId equals t.ItemId
                           join c in DB.IC_Categories on t.Category equals c.CategoryId

                          
                           select new
                           {
                               ItemId = d.ItemId,
                               SoldQty = d.Qty,
                               SoldPiecesCount = d.PiecesCount,
                               d.UOMIndex,
                               d.UOMId,
                               t.MediumUOMFactor,
                               t.LargeUOMFactor
                           }).ToList();

            var storeQty = (from d in DB.IC_ItemStores
                            where dtFromDate.EditValue == null ? true : (d.InsertTime.Date >= From.Date)
                            where dtToDate.EditValue == null ? true : (d.InsertTime.Date <= To.Date)
                            
                            group d by d.ItemId into grp

                            join t in DB.IC_Items
                            on grp.Key equals t.ItemId
                            join g in DB.IC_Categories on t.Category equals g.CategoryId

                   

                            select new
                            {
                                ItemId = grp.Key,
                                CurrentQty = grp.Select(c => c.IsInTrns ? c.Qty : c.Qty * -1).Sum(),
                                CurrentPiecesCount = grp.Select(c => c.IsInTrns ? c.PiecesCount : c.PiecesCount * -1).Sum(),
                            }).ToList();

            var data1 = from d in soldQty
                        select new
                        {
                            ItemId = d.ItemId,
                            SoldQty = MyHelper.CalculateUomQty(d.SoldQty, d.UOMIndex, MyHelper.FractionToDouble(d.MediumUOMFactor),
                            MyHelper.FractionToDouble(d.LargeUOMFactor)),
                            d.SoldPiecesCount,
                        };

            var data2 = (from d in data1
                         group d by d.ItemId into grp
                         select new
                         {
                             ItemId = grp.Key,
                             SoldQty = grp.Sum(x => x.SoldQty),
                             SoldPiecesCount = grp.Sum(x => x.SoldPiecesCount),
                         }).ToList();

            var data3 = (from d in data2
                         join t in DB.IC_Items
                         on d.ItemId equals t.ItemId
                         join c in DB.IC_Categories on t.Category equals c.CategoryId
                         join s in storeQty
                         on d.ItemId equals s.ItemId into store
                         from st in store.DefaultIfEmpty()
                         select new
                         {
                             ItemId = t.ItemId,
                             t.ItemCode1,
                             t.ItemCode2,
                             t.ItemNameAr,
                             t.Category,
                             c.CategoryNameAr,
                             c.CategoryNameEn,
                             SoldQty = d.SoldQty,
                            // CurrentQty = (st == null ? 0 : st.CurrentQty),
                             //t.MediumUOMFactor,
                             //t.LargeUOMFactor,
                             //SUom = DB.IC_UOMs.Where(x => x.UOMId == t.SmallUOM).Select(x => x.UOM).FirstOrDefault(),
                             //MUom = Shared.st_Store.UseMediumUom ?
                             //DB.IC_UOMs.Where(x => x.UOMId == t.MediumUOM).Select(x => x.UOM).FirstOrDefault() : string.Empty,
                             //LUom = Shared.st_Store.UseLargeUom ?
                             //DB.IC_UOMs.Where(x => x.UOMId == t.LargeUOM).Select(x => x.UOM).FirstOrDefault() : string.Empty,

                             //CurrentPiecesCount = (st == null ? 0 : decimal.ToDouble(st.CurrentPiecesCount)),
                             //SoldPiecesCount = d.SoldPiecesCount,
                         }).OrderByDescending(x => x.SoldQty).ToList();

            var result = (from g in data3
                          group g by g.Category into grp
                          let TotalSold = data3.Select(a => a.SoldQty).Sum()
                          select new
                          {
                              CategoryId = grp.Key,
                              CategoryName = Shared.IsEnglish ? grp.Select(a => a.CategoryNameEn).FirstOrDefault() : grp.Select(a => a.CategoryNameAr).FirstOrDefault(),
                              soldQty = grp.Sum(a => a.SoldQty),
                              percent = (grp.Sum(a => a.SoldQty) / TotalSold) * 100
                          }).OrderByDescending(x => x.soldQty).ToList();
            if (Noofcategory > 0)
                result = result.OrderByDescending(x => x.soldQty).Take(Noofcategory).ToList();
           
            
                chartControl1.DataSource = result;

        }

        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void frm_MaxItemsSalesCategory_Load(object sender, EventArgs e)
        {
            LoadPrivilege();
            dtFromDate.EditValue = MyHelper.Get_Server_DateTime();
            dtToDate.EditValue = MyHelper.Get_Server_DateTime();
        }
         private void printableComponentLink1_CreateReportHeaderArea(object sender, DevExpress.XtraPrinting.CreateAreaEventArgs e)
        {
            string ReportName = this.Text;
            string dateFilters = string.Empty;
            string otherFilters = string.Empty;

            //create filters line
            if (dtFromDate.EditValue != null && dtToDate.EditValue != null)
                dateFilters = (Shared.IsEnglish == true ? ResAccEn.txtFrom : ResAccAr.txtFrom) +
                    dtFromDate.DateTime.ToShortDateString() +
                    (Shared.IsEnglish == true ? ResAccEn.txtTo : ResAccAr.txtTo) +
                    dtToDate.DateTime.ToShortDateString();

            else if (dtFromDate.EditValue != null && dtToDate.EditValue == null)
                dateFilters =
                    (Shared.IsEnglish == true ? ResAccEn.txtFromDate : ResAccAr.txtFromDate) +
                    dtFromDate.DateTime.ToShortDateString();
            else if (dtFromDate.EditValue == null && dtToDate.EditValue != null)
                dateFilters = (Shared.IsEnglish == true ? ResAccEn.txtToDate : ResAccAr.txtToDate) +
                    dtToDate.DateTime.ToShortDateString();
            else
                dateFilters = "";


            ErpUtils.CreateReportHeader(e, ReportName, dateFilters, otherFilters);
        }
        private void printableComponentLink1_CreateReportFooter(object sender, DevExpress.XtraPrinting.CreateAreaEventArgs e)
        {
            RectangleF recTotal = new RectangleF((float)10, (float)17, 740, (float)25);

            e.Graph.StringFormat = Shared.IsEnglish ? new BrickStringFormat(StringAlignment.Near) : new BrickStringFormat(StringAlignment.Far);
            e.Graph.Font = new Font("Times New Roman", 13, FontStyle.Regular);
            e.Graph.ForeColor = Color.Black;
            e.Graph.DefaultBrickStyle.BorderColor = Color.Transparent;
            e.Graph.BackColor = Color.Snow;


            //string total = txtTotal.Text;

            //e.Graph.DrawString(total, recTotal);            
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (this.Width == 1376)
                chartControl1.Width = this.Width - 300;
            //chartControl1.Height = this.Height - 130;
            PrintingSystem printSystem = new PrintingSystem(this.components);
            PrintableComponentLink printLink;
            if (this.components == null)
                printLink = new PrintableComponentLink();
            else
                printLink = new PrintableComponentLink(this.components);

            ((System.ComponentModel.ISupportInitialize)(printSystem)).BeginInit();

            printSystem.Links.AddRange(new object[] {
            printLink});

            printLink.Component = this.chartControl1;

            printLink.PaperKind = System.Drawing.Printing.PaperKind.A4;
            printLink.Landscape = true;
            printLink.Margins = new System.Drawing.Printing.Margins(5, 5, 135, 50);
            printLink.PrintingSystem = printSystem;
            printLink.PrintingSystemBase = printSystem;

            printLink.CreateMarginalHeaderArea +=
                new DevExpress.XtraPrinting.CreateAreaEventHandler(this.printableComponentLink1_CreateReportHeaderArea);
            printLink.CreateReportFooterArea +=
                new DevExpress.XtraPrinting.CreateAreaEventHandler(this.printableComponentLink1_CreateReportFooter);

            ((System.ComponentModel.ISupportInitialize)(printSystem)).EndInit();

            printLink.CreateDocument();
            printLink.ShowPreview();
            chartControl1.Width = this.Width - 51;
        }



        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                prvlg = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.frm_MaxItemsSalesCategory).FirstOrDefault();

                if (!prvlg.CanPrint)
                    barBtnPrint.Enabled = false;
                if (!prvlg.CanAdd)
                    barBtnOk.Enabled = false;
            }
        }
    }
}
