﻿namespace Pharmacy.Forms
{
    partial class frm_Accounts_statistics
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_Accounts_statistics));
            DevExpress.XtraCharts.XYDiagram xyDiagram1 = new DevExpress.XtraCharts.XYDiagram();
            DevExpress.XtraCharts.Series series1 = new DevExpress.XtraCharts.Series();
            DevExpress.XtraCharts.LineSeriesView lineSeriesView1 = new DevExpress.XtraCharts.LineSeriesView();
            DevExpress.XtraCharts.PointSeriesLabel pointSeriesLabel1 = new DevExpress.XtraCharts.PointSeriesLabel();
            DevExpress.XtraCharts.LineSeriesView lineSeriesView2 = new DevExpress.XtraCharts.LineSeriesView();
            this.lkpCustomList = new DevExpress.XtraEditors.LookUpEdit();
            this.cb_PeriodUnit = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.txt_Period = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.dtToDate = new DevExpress.XtraEditors.DateEdit();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.dtFromDate = new DevExpress.XtraEditors.DateEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtnHelp = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnPrint = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnOk = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.chartControl1 = new DevExpress.XtraCharts.ChartControl();
            this.chkSubAccounts = new DevExpress.XtraEditors.CheckEdit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpCustomList.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cb_PeriodUnit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Period.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtToDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtToDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtFromDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtFromDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(lineSeriesView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(lineSeriesView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSubAccounts.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // lkpCustomList
            // 
            resources.ApplyResources(this.lkpCustomList, "lkpCustomList");
            this.lkpCustomList.EnterMoveNextControl = true;
            this.lkpCustomList.Name = "lkpCustomList";
            this.lkpCustomList.Properties.AccessibleDescription = resources.GetString("lkpCustomList.Properties.AccessibleDescription");
            this.lkpCustomList.Properties.AccessibleName = resources.GetString("lkpCustomList.Properties.AccessibleName");
            this.lkpCustomList.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkpCustomList.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkpCustomList.Properties.Appearance.FontSizeDelta")));
            this.lkpCustomList.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpCustomList.Properties.Appearance.FontStyleDelta")));
            this.lkpCustomList.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpCustomList.Properties.Appearance.GradientMode")));
            this.lkpCustomList.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkpCustomList.Properties.Appearance.Image")));
            this.lkpCustomList.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpCustomList.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpCustomList.Properties.AutoHeight = ((bool)(resources.GetObject("lkpCustomList.Properties.AutoHeight")));
            this.lkpCustomList.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkpCustomList.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpCustomList.Properties.Buttons"))))});
            this.lkpCustomList.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpCustomList.Properties.Columns"), resources.GetString("lkpCustomList.Properties.Columns1"), ((int)(resources.GetObject("lkpCustomList.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpCustomList.Properties.Columns3"))), resources.GetString("lkpCustomList.Properties.Columns4"), ((bool)(resources.GetObject("lkpCustomList.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpCustomList.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpCustomList.Properties.Columns7"), resources.GetString("lkpCustomList.Properties.Columns8"), ((int)(resources.GetObject("lkpCustomList.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpCustomList.Properties.Columns10"))), resources.GetString("lkpCustomList.Properties.Columns11"), ((bool)(resources.GetObject("lkpCustomList.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpCustomList.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpCustomList.Properties.Columns14"), resources.GetString("lkpCustomList.Properties.Columns15"), ((int)(resources.GetObject("lkpCustomList.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpCustomList.Properties.Columns17"))), resources.GetString("lkpCustomList.Properties.Columns18"), ((bool)(resources.GetObject("lkpCustomList.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpCustomList.Properties.Columns20"))))});
            this.lkpCustomList.Properties.NullText = resources.GetString("lkpCustomList.Properties.NullText");
            this.lkpCustomList.Properties.NullValuePrompt = resources.GetString("lkpCustomList.Properties.NullValuePrompt");
            this.lkpCustomList.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkpCustomList.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // cb_PeriodUnit
            // 
            resources.ApplyResources(this.cb_PeriodUnit, "cb_PeriodUnit");
            this.cb_PeriodUnit.EnterMoveNextControl = true;
            this.cb_PeriodUnit.Name = "cb_PeriodUnit";
            this.cb_PeriodUnit.Properties.AccessibleDescription = resources.GetString("cb_PeriodUnit.Properties.AccessibleDescription");
            this.cb_PeriodUnit.Properties.AccessibleName = resources.GetString("cb_PeriodUnit.Properties.AccessibleName");
            this.cb_PeriodUnit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.cb_PeriodUnit.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("cb_PeriodUnit.Properties.Appearance.FontSizeDelta")));
            this.cb_PeriodUnit.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("cb_PeriodUnit.Properties.Appearance.FontStyleDelta")));
            this.cb_PeriodUnit.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("cb_PeriodUnit.Properties.Appearance.GradientMode")));
            this.cb_PeriodUnit.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("cb_PeriodUnit.Properties.Appearance.Image")));
            this.cb_PeriodUnit.Properties.Appearance.Options.UseTextOptions = true;
            this.cb_PeriodUnit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.cb_PeriodUnit.Properties.AutoHeight = ((bool)(resources.GetObject("cb_PeriodUnit.Properties.AutoHeight")));
            this.cb_PeriodUnit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("cb_PeriodUnit.Properties.Buttons"))))});
            this.cb_PeriodUnit.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("cb_PeriodUnit.Properties.GlyphAlignment")));
            this.cb_PeriodUnit.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cb_PeriodUnit.Properties.Items"), resources.GetString("cb_PeriodUnit.Properties.Items1"), ((int)(resources.GetObject("cb_PeriodUnit.Properties.Items2")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cb_PeriodUnit.Properties.Items3"), resources.GetString("cb_PeriodUnit.Properties.Items4"), ((int)(resources.GetObject("cb_PeriodUnit.Properties.Items5")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cb_PeriodUnit.Properties.Items6"), resources.GetString("cb_PeriodUnit.Properties.Items7"), ((int)(resources.GetObject("cb_PeriodUnit.Properties.Items8"))))});
            this.cb_PeriodUnit.Properties.NullValuePrompt = resources.GetString("cb_PeriodUnit.Properties.NullValuePrompt");
            this.cb_PeriodUnit.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("cb_PeriodUnit.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl4
            // 
            resources.ApplyResources(this.labelControl4, "labelControl4");
            this.labelControl4.Name = "labelControl4";
            // 
            // txt_Period
            // 
            resources.ApplyResources(this.txt_Period, "txt_Period");
            this.txt_Period.EnterMoveNextControl = true;
            this.txt_Period.Name = "txt_Period";
            this.txt_Period.Properties.AccessibleDescription = resources.GetString("txt_Period.Properties.AccessibleDescription");
            this.txt_Period.Properties.AccessibleName = resources.GetString("txt_Period.Properties.AccessibleName");
            this.txt_Period.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Period.Properties.AutoHeight")));
            this.txt_Period.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txt_Period.Properties.IsFloatValue = false;
            this.txt_Period.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_Period.Properties.Mask.AutoComplete")));
            this.txt_Period.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_Period.Properties.Mask.BeepOnError")));
            this.txt_Period.Properties.Mask.EditMask = resources.GetString("txt_Period.Properties.Mask.EditMask");
            this.txt_Period.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Period.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Period.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Period.Properties.Mask.MaskType")));
            this.txt_Period.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_Period.Properties.Mask.PlaceHolder")));
            this.txt_Period.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Period.Properties.Mask.SaveLiteral")));
            this.txt_Period.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Period.Properties.Mask.ShowPlaceHolders")));
            this.txt_Period.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_Period.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_Period.Properties.NullValuePrompt = resources.GetString("txt_Period.Properties.NullValuePrompt");
            this.txt_Period.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_Period.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl2
            // 
            resources.ApplyResources(this.labelControl2, "labelControl2");
            this.labelControl2.Name = "labelControl2";
            // 
            // dtToDate
            // 
            resources.ApplyResources(this.dtToDate, "dtToDate");
            this.dtToDate.EnterMoveNextControl = true;
            this.dtToDate.Name = "dtToDate";
            this.dtToDate.Properties.AccessibleDescription = resources.GetString("dtToDate.Properties.AccessibleDescription");
            this.dtToDate.Properties.AccessibleName = resources.GetString("dtToDate.Properties.AccessibleName");
            this.dtToDate.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.dtToDate.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("dtToDate.Properties.Appearance.FontSizeDelta")));
            this.dtToDate.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("dtToDate.Properties.Appearance.FontStyleDelta")));
            this.dtToDate.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("dtToDate.Properties.Appearance.GradientMode")));
            this.dtToDate.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("dtToDate.Properties.Appearance.Image")));
            this.dtToDate.Properties.Appearance.Options.UseTextOptions = true;
            this.dtToDate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.dtToDate.Properties.AutoHeight = ((bool)(resources.GetObject("dtToDate.Properties.AutoHeight")));
            this.dtToDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("dtToDate.Properties.Buttons"))))});
            this.dtToDate.Properties.CalendarTimeProperties.AccessibleDescription = resources.GetString("dtToDate.Properties.CalendarTimeProperties.AccessibleDescription");
            this.dtToDate.Properties.CalendarTimeProperties.AccessibleName = resources.GetString("dtToDate.Properties.CalendarTimeProperties.AccessibleName");
            this.dtToDate.Properties.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("dtToDate.Properties.CalendarTimeProperties.AutoHeight")));
            this.dtToDate.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dtToDate.Properties.CalendarTimeProperties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dtToDate.Properties.CalendarTimeProperties.Mask.AutoComplete")));
            this.dtToDate.Properties.CalendarTimeProperties.Mask.BeepOnError = ((bool)(resources.GetObject("dtToDate.Properties.CalendarTimeProperties.Mask.BeepOnError")));
            this.dtToDate.Properties.CalendarTimeProperties.Mask.EditMask = resources.GetString("dtToDate.Properties.CalendarTimeProperties.Mask.EditMask");
            this.dtToDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtToDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.dtToDate.Properties.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtToDate.Properties.CalendarTimeProperties.Mask.MaskType")));
            this.dtToDate.Properties.CalendarTimeProperties.Mask.PlaceHolder = ((char)(resources.GetObject("dtToDate.Properties.CalendarTimeProperties.Mask.PlaceHolder")));
            this.dtToDate.Properties.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtToDate.Properties.CalendarTimeProperties.Mask.SaveLiteral")));
            this.dtToDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtToDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.dtToDate.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtToDate.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat")));
            this.dtToDate.Properties.CalendarTimeProperties.NullValuePrompt = resources.GetString("dtToDate.Properties.CalendarTimeProperties.NullValuePrompt");
            this.dtToDate.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dtToDate.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue")));
            this.dtToDate.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dtToDate.Properties.Mask.AutoComplete")));
            this.dtToDate.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("dtToDate.Properties.Mask.BeepOnError")));
            this.dtToDate.Properties.Mask.EditMask = resources.GetString("dtToDate.Properties.Mask.EditMask");
            this.dtToDate.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtToDate.Properties.Mask.IgnoreMaskBlank")));
            this.dtToDate.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtToDate.Properties.Mask.MaskType")));
            this.dtToDate.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("dtToDate.Properties.Mask.PlaceHolder")));
            this.dtToDate.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtToDate.Properties.Mask.SaveLiteral")));
            this.dtToDate.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtToDate.Properties.Mask.ShowPlaceHolders")));
            this.dtToDate.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtToDate.Properties.Mask.UseMaskAsDisplayFormat")));
            this.dtToDate.Properties.NullValuePrompt = resources.GetString("dtToDate.Properties.NullValuePrompt");
            this.dtToDate.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dtToDate.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl6
            // 
            resources.ApplyResources(this.labelControl6, "labelControl6");
            this.labelControl6.Name = "labelControl6";
            // 
            // dtFromDate
            // 
            resources.ApplyResources(this.dtFromDate, "dtFromDate");
            this.dtFromDate.EnterMoveNextControl = true;
            this.dtFromDate.Name = "dtFromDate";
            this.dtFromDate.Properties.AccessibleDescription = resources.GetString("dtFromDate.Properties.AccessibleDescription");
            this.dtFromDate.Properties.AccessibleName = resources.GetString("dtFromDate.Properties.AccessibleName");
            this.dtFromDate.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.dtFromDate.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("dtFromDate.Properties.Appearance.FontSizeDelta")));
            this.dtFromDate.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("dtFromDate.Properties.Appearance.FontStyleDelta")));
            this.dtFromDate.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("dtFromDate.Properties.Appearance.GradientMode")));
            this.dtFromDate.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("dtFromDate.Properties.Appearance.Image")));
            this.dtFromDate.Properties.Appearance.Options.UseTextOptions = true;
            this.dtFromDate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.dtFromDate.Properties.AutoHeight = ((bool)(resources.GetObject("dtFromDate.Properties.AutoHeight")));
            this.dtFromDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("dtFromDate.Properties.Buttons"))))});
            this.dtFromDate.Properties.CalendarTimeProperties.AccessibleDescription = resources.GetString("dtFromDate.Properties.CalendarTimeProperties.AccessibleDescription");
            this.dtFromDate.Properties.CalendarTimeProperties.AccessibleName = resources.GetString("dtFromDate.Properties.CalendarTimeProperties.AccessibleName");
            this.dtFromDate.Properties.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("dtFromDate.Properties.CalendarTimeProperties.AutoHeight")));
            this.dtFromDate.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dtFromDate.Properties.CalendarTimeProperties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dtFromDate.Properties.CalendarTimeProperties.Mask.AutoComplete")));
            this.dtFromDate.Properties.CalendarTimeProperties.Mask.BeepOnError = ((bool)(resources.GetObject("dtFromDate.Properties.CalendarTimeProperties.Mask.BeepOnError")));
            this.dtFromDate.Properties.CalendarTimeProperties.Mask.EditMask = resources.GetString("dtFromDate.Properties.CalendarTimeProperties.Mask.EditMask");
            this.dtFromDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtFromDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.dtFromDate.Properties.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtFromDate.Properties.CalendarTimeProperties.Mask.MaskType")));
            this.dtFromDate.Properties.CalendarTimeProperties.Mask.PlaceHolder = ((char)(resources.GetObject("dtFromDate.Properties.CalendarTimeProperties.Mask.PlaceHolder")));
            this.dtFromDate.Properties.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtFromDate.Properties.CalendarTimeProperties.Mask.SaveLiteral")));
            this.dtFromDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtFromDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.dtFromDate.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtFromDate.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat")));
            this.dtFromDate.Properties.CalendarTimeProperties.NullValuePrompt = resources.GetString("dtFromDate.Properties.CalendarTimeProperties.NullValuePrompt");
            this.dtFromDate.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dtFromDate.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue")));
            this.dtFromDate.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dtFromDate.Properties.Mask.AutoComplete")));
            this.dtFromDate.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("dtFromDate.Properties.Mask.BeepOnError")));
            this.dtFromDate.Properties.Mask.EditMask = resources.GetString("dtFromDate.Properties.Mask.EditMask");
            this.dtFromDate.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtFromDate.Properties.Mask.IgnoreMaskBlank")));
            this.dtFromDate.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtFromDate.Properties.Mask.MaskType")));
            this.dtFromDate.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("dtFromDate.Properties.Mask.PlaceHolder")));
            this.dtFromDate.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtFromDate.Properties.Mask.SaveLiteral")));
            this.dtFromDate.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtFromDate.Properties.Mask.ShowPlaceHolders")));
            this.dtFromDate.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtFromDate.Properties.Mask.UseMaskAsDisplayFormat")));
            this.dtFromDate.Properties.NullValuePrompt = resources.GetString("dtFromDate.Properties.NullValuePrompt");
            this.dtFromDate.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dtFromDate.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl5
            // 
            resources.ApplyResources(this.labelControl5, "labelControl5");
            this.labelControl5.Name = "labelControl5";
            // 
            // labelControl1
            // 
            resources.ApplyResources(this.labelControl1, "labelControl1");
            this.labelControl1.Name = "labelControl1";
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnHelp,
            this.barBtnPrint,
            this.barBtnOk,
            this.barBtnClose});
            this.barManager1.MaxItemId = 32;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(377, 152);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnHelp),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnPrint),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnOk),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtnHelp
            // 
            resources.ApplyResources(this.barBtnHelp, "barBtnHelp");
            this.barBtnHelp.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnHelp.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtnHelp.Id = 2;
            this.barBtnHelp.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtnHelp.Name = "barBtnHelp";
            this.barBtnHelp.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnHelp.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            // 
            // barBtnPrint
            // 
            resources.ApplyResources(this.barBtnPrint, "barBtnPrint");
            this.barBtnPrint.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnPrint.Glyph = global::Pharmacy.Properties.Resources.prnt;
            this.barBtnPrint.Id = 25;
            this.barBtnPrint.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.P));
            this.barBtnPrint.Name = "barBtnPrint";
            this.barBtnPrint.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            // 
            // barBtnOk
            // 
            resources.ApplyResources(this.barBtnOk, "barBtnOk");
            this.barBtnOk.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnOk.Glyph = global::Pharmacy.Properties.Resources.open;
            this.barBtnOk.Id = 26;
            this.barBtnOk.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.O));
            this.barBtnOk.Name = "barBtnOk";
            this.barBtnOk.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnOk.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnOk_ItemClick);
            // 
            // barBtnClose
            // 
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 31;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnClose_ItemClick);
            // 
            // barDockControlTop
            // 
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlTop.Appearance.FontSizeDelta")));
            this.barDockControlTop.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlTop.Appearance.FontStyleDelta")));
            this.barDockControlTop.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlTop.Appearance.GradientMode")));
            this.barDockControlTop.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlTop.Appearance.Image")));
            this.barDockControlTop.CausesValidation = false;
            // 
            // barDockControlBottom
            // 
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlBottom.Appearance.FontSizeDelta")));
            this.barDockControlBottom.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlBottom.Appearance.FontStyleDelta")));
            this.barDockControlBottom.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlBottom.Appearance.GradientMode")));
            this.barDockControlBottom.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlBottom.Appearance.Image")));
            this.barDockControlBottom.CausesValidation = false;
            // 
            // barDockControlLeft
            // 
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlLeft.Appearance.FontSizeDelta")));
            this.barDockControlLeft.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlLeft.Appearance.FontStyleDelta")));
            this.barDockControlLeft.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlLeft.Appearance.GradientMode")));
            this.barDockControlLeft.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlLeft.Appearance.Image")));
            this.barDockControlLeft.CausesValidation = false;
            // 
            // barDockControlRight
            // 
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlRight.Appearance.FontSizeDelta")));
            this.barDockControlRight.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlRight.Appearance.FontStyleDelta")));
            this.barDockControlRight.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlRight.Appearance.GradientMode")));
            this.barDockControlRight.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlRight.Appearance.Image")));
            this.barDockControlRight.CausesValidation = false;
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemTextEdit1.Mask.AutoComplete")));
            this.repositoryItemTextEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.BeepOnError")));
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemTextEdit1.Mask.MaskType")));
            this.repositoryItemTextEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemTextEdit1.Mask.PlaceHolder")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // chartControl1
            // 
            resources.ApplyResources(this.chartControl1, "chartControl1");
            xyDiagram1.AxisX.MinorCount = 1;
            xyDiagram1.AxisX.VisibleInPanesSerializable = "-1";
            xyDiagram1.AxisY.VisibleInPanesSerializable = "-1";
            this.chartControl1.Diagram = xyDiagram1;
            this.chartControl1.Name = "chartControl1";
            series1.ArgumentDataMember = "Date";
            series1.ValueDataMembersSerializable = "Balance";
            series1.View = lineSeriesView1;
            this.chartControl1.SeriesSerializable = new DevExpress.XtraCharts.Series[] {
        series1};
            pointSeriesLabel1.LineVisibility = DevExpress.Utils.DefaultBoolean.True;
            this.chartControl1.SeriesTemplate.Label = pointSeriesLabel1;
            this.chartControl1.SeriesTemplate.View = lineSeriesView2;
            // 
            // chkSubAccounts
            // 
            resources.ApplyResources(this.chkSubAccounts, "chkSubAccounts");
            this.chkSubAccounts.MenuManager = this.barManager1;
            this.chkSubAccounts.Name = "chkSubAccounts";
            this.chkSubAccounts.Properties.AccessibleDescription = resources.GetString("chkSubAccounts.Properties.AccessibleDescription");
            this.chkSubAccounts.Properties.AccessibleName = resources.GetString("chkSubAccounts.Properties.AccessibleName");
            this.chkSubAccounts.Properties.AutoHeight = ((bool)(resources.GetObject("chkSubAccounts.Properties.AutoHeight")));
            this.chkSubAccounts.Properties.Caption = resources.GetString("chkSubAccounts.Properties.Caption");
            this.chkSubAccounts.Properties.DisplayValueChecked = resources.GetString("chkSubAccounts.Properties.DisplayValueChecked");
            this.chkSubAccounts.Properties.DisplayValueGrayed = resources.GetString("chkSubAccounts.Properties.DisplayValueGrayed");
            this.chkSubAccounts.Properties.DisplayValueUnchecked = resources.GetString("chkSubAccounts.Properties.DisplayValueUnchecked");
            // 
            // frm_Accounts_statistics
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.chkSubAccounts);
            this.Controls.Add(this.chartControl1);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.cb_PeriodUnit);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.txt_Period);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.dtToDate);
            this.Controls.Add(this.labelControl6);
            this.Controls.Add(this.dtFromDate);
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.lkpCustomList);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.Name = "frm_Accounts_statistics";
            this.Load += new System.EventHandler(this.frm_Accounts_statistics_Load);
            ((System.ComponentModel.ISupportInitialize)(this.lkpCustomList.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cb_PeriodUnit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Period.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtToDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtToDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtFromDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtFromDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(xyDiagram1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(lineSeriesView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(series1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(pointSeriesLabel1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(lineSeriesView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSubAccounts.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.LookUpEdit lkpCustomList;
        private DevExpress.XtraEditors.ImageComboBoxEdit cb_PeriodUnit;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.SpinEdit txt_Period;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.DateEdit dtToDate;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.DateEdit dtFromDate;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnHelp;
        private DevExpress.XtraBars.BarButtonItem barBtnPrint;
        private DevExpress.XtraBars.BarButtonItem barBtnOk;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraCharts.ChartControl chartControl1;
        private DevExpress.XtraEditors.CheckEdit chkSubAccounts;
    }
}