﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="txt_DueDays.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Category.Properties.Columns6" xml:space="preserve">
    <value />
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="txt_DueDays.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txtImagePath.Name" xml:space="preserve">
    <value>txtImagePath</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="lkpPriceLevel.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtShipping.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="lblOpenDate.Text" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="txtCusNameAr.Location" type="System.Drawing.Point, System.Drawing">
    <value>66, 58</value>
  </data>
  <data name="labelControl46.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Red</value>
  </data>
  <data name="labelControl44.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Regions.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl49.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;barDockControlBottom.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="&gt;&gt;labelControl48.Name" xml:space="preserve">
    <value>labelControl48</value>
  </data>
  <data name="&gt;&gt;barBtnDelete.Name" xml:space="preserve">
    <value>barBtnDelete</value>
  </data>
  <data name="txtCity.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtZip.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chk_IsTaxable.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_CollectEmp.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_BuildingNumber.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txtTradeRegistry.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl5.TabIndex" type="System.Int32, mscorlib">
    <value>93</value>
  </data>
  <data name="&gt;&gt;pnlOpenBlnce.Name" xml:space="preserve">
    <value>pnlOpenBlnce</value>
  </data>
  <data name="lkp_Category.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMaxCredit.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_DueDays.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;Governate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl10.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtTradeRegistry.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="barBtnDelete.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="lkpDelivery.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtTel.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Bank.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cmbIsCredit.Properties.AppearanceDropDown.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtCusNameEn.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;labelControl28.Name" xml:space="preserve">
    <value>labelControl28</value>
  </data>
  <data name="&gt;&gt;txtFax.ZOrder" xml:space="preserve">
    <value>78</value>
  </data>
  <data name="txtImageDesc.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="xtraTabControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>7, 54</value>
  </data>
  <data name="&gt;&gt;labelControl14.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="labelControl21.Text" xml:space="preserve">
    <value>F Job</value>
  </data>
  <data name="lkp_country.Size" type="System.Drawing.Size, System.Drawing">
    <value>115, 20</value>
  </data>
  <data name="lkpPriceLevel.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtOpenBalance.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl14.Text" xml:space="preserve">
    <value>Manger Name</value>
  </data>
  <data name="chk_IsBlocked.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;chk_IsTaxable.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="bandedGridView1.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtTaxDepartment.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;barAndDockingController1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarAndDockingController, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtAddress.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;gridBand5.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.GridBand, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_CollectEmp.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_Neighborhood.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtOpenBalance.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Representative.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_AM.Caption" xml:space="preserve">
    <value>AM</value>
  </data>
  <data name="lkp_CollectEmp.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Category.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 20</value>
  </data>
  <data name="lblTaxDepartment.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_country.Properties.AppearanceDropDownHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="btnShowPhotoes.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl26.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="lkpPriceLevel.Properties.AppearanceDropDownHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <assembly alias="DevExpress.XtraEditors.v15.1" name="DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="txt_Governate.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtAddress.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="&gt;&gt;txt_IdNumber.ZOrder" xml:space="preserve">
    <value>37</value>
  </data>
  <data name="labelControl46.Text" xml:space="preserve">
    <value>*</value>
  </data>
  <data name="txt_DueDays.Properties.Buttons3" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cmbIsCredit.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtTaxFileNumber.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;labelControl14.Name" xml:space="preserve">
    <value>labelControl14</value>
  </data>
  <data name="txt_BankAccNum.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtOpenBalance.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtCity.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl38.TabIndex" type="System.Int32, mscorlib">
    <value>229</value>
  </data>
  <data name="btnNext.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 19</value>
  </data>
  <data name="&gt;&gt;txtTel.ZOrder" xml:space="preserve">
    <value>56</value>
  </data>
  <data name="labelControl3.Text" xml:space="preserve">
    <value>Customer F Name</value>
  </data>
  <data name="labelControl36.Text" xml:space="preserve">
    <value>Region</value>
  </data>
  <data name="chk_IsBlocked.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 344</value>
  </data>
  <data name="chk_IsActive.Location" type="System.Drawing.Point, System.Drawing">
    <value>293, 344</value>
  </data>
  <data name="dtOpenBalance.Properties.CalendarTimeProperties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtCusNameEn.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;btnEditPhoto.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_country.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl18.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl7.Size" type="System.Drawing.Size, System.Drawing">
    <value>52, 13</value>
  </data>
  <data name="&gt;&gt;labelControl50.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtTel.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 20</value>
  </data>
  <data name="lkp_country.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl46.TabIndex" type="System.Int32, mscorlib">
    <value>246</value>
  </data>
  <data name="&gt;&gt;labelControl39.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_country.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_Category.Properties.AppearanceDropDownHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="bar2.Text" xml:space="preserve">
    <value>Custom 3</value>
  </data>
  <data name="txtTel.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_RepresentativeJob.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl22.Location" type="System.Drawing.Point, System.Drawing">
    <value>464, 32</value>
  </data>
  <data name="&gt;&gt;barBtnSave.Name" xml:space="preserve">
    <value>barBtnSave</value>
  </data>
  <data name="&gt;&gt;lkp_Regions.ZOrder" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="&gt;&gt;txtOpenAmount.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="txtCusCode.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lkp_CollectEmp.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="labelControl26.Text" xml:space="preserve">
    <value>ID Number</value>
  </data>
  <data name="txtEmail.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Bank.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtImageDesc.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl49.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl36.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtTaxDepartment.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_SalesEmp.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtOpenAmount.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txtCusNameAr.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="btn_AddRegion.Location" type="System.Drawing.Point, System.Drawing">
    <value>35, 399</value>
  </data>
  <data name="chk_IsActive.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl8.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="bandedGridView1.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtFax.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txt_Street.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtTaxCardNumber.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="chk_IsActive.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txt_Street.Name" xml:space="preserve">
    <value>txt_Street</value>
  </data>
  <data name="txt_Rep_Phone.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;txtTaxDepartment.Name" xml:space="preserve">
    <value>txtTaxDepartment</value>
  </data>
  <data name="labelControl20.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;labelControl11.ZOrder" xml:space="preserve">
    <value>80</value>
  </data>
  <data name="txt_RepresentativeJob.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl11.Location" type="System.Drawing.Point, System.Drawing">
    <value>416, 136</value>
  </data>
  <data name="&gt;&gt;btnAddEmpPhoho.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="txtImageDesc.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;xtraTabControl1.Name" xml:space="preserve">
    <value>xtraTabControl1</value>
  </data>
  <data name="lkp_country.Properties.Columns7" xml:space="preserve">
    <value>CountryName</value>
  </data>
  <data name="lkp_Category.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="btnEditPhoto.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="&gt;&gt;txtMobile.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="barDockControlBottom.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl44.Text" xml:space="preserve">
    <value>*</value>
  </data>
  <data name="txtTel.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtTaxFileNumber.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtMobile.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="btnCustomerItems.TabIndex" type="System.Int32, mscorlib">
    <value>81</value>
  </data>
  <data name="&gt;&gt;chk_IsActive.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="dtOpenBalance.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txt_Bank.Name" xml:space="preserve">
    <value>txt_Bank</value>
  </data>
  <data name="labelControl48.Location" type="System.Drawing.Point, System.Drawing">
    <value>47, 271</value>
  </data>
  <data name="txtMaxCredit.Size" type="System.Drawing.Size, System.Drawing">
    <value>86, 20</value>
  </data>
  <data name="lkp_Regions.Properties.Columns4" xml:space="preserve">
    <value>IdRegion</value>
  </data>
  <data name="labelControl34.Text" xml:space="preserve">
    <value>Mobile No.</value>
  </data>
  <data name="&gt;&gt;pnlOpenBlnce.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="txtMobile.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;txtAddress.ZOrder" xml:space="preserve">
    <value>48</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="labelControl14.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtManagerName.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txt_Governate.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_Group.Properties.Columns" xml:space="preserve">
    <value>NameEn</value>
  </data>
  <data name="txtTradeRegistry.Size" type="System.Drawing.Size, System.Drawing">
    <value>150, 20</value>
  </data>
  <data name="txtTradeRegistry.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_RepresentativeJob.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="cmbIsCredit.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="labelControl17.Size" type="System.Drawing.Size, System.Drawing">
    <value>104, 13</value>
  </data>
  <data name="txtCusNameAr.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtOpenBalance.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl24.Text" xml:space="preserve">
    <value>Category</value>
  </data>
  <data name="&gt;&gt;labelControl35.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Street.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl22.TabIndex" type="System.Int32, mscorlib">
    <value>26</value>
  </data>
  <data name="labelControl45.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_Group.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 20</value>
  </data>
  <data name="lkpDelivery.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTaxDepartment.Location" type="System.Drawing.Point, System.Drawing">
    <value>274, 59</value>
  </data>
  <data name="lkp_country.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;txt_BuildingNumber.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtTaxFileNumber.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="repositoryItemTextEdit1.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_BuildingNumber.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="labelControl31.TabIndex" type="System.Int32, mscorlib">
    <value>213</value>
  </data>
  <data name="&gt;&gt;labelControl20.ZOrder" xml:space="preserve">
    <value>47</value>
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="lkpPriceLevel.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpDelivery.Properties.AppearanceDropDown.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns7" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="&gt;&gt;txtDiscRatio.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="txt_Rep_ID.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Group.Properties.Columns4" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_Group.Properties.Columns5" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_Group.Properties.Columns6" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Group.Properties.Columns7" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_Group.Properties.Columns1" xml:space="preserve">
    <value>Group Name</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_Group.Properties.Columns3" xml:space="preserve">
    <value>Name4</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl35.ZOrder" xml:space="preserve">
    <value>25</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns14" xml:space="preserve">
    <value>EmpId</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;labelControl32.Name" xml:space="preserve">
    <value>labelControl32</value>
  </data>
  <data name="gridVisits.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 480</value>
  </data>
  <data name="txtMaxCredit.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="lkp_country.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;txtZip.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridVisits.EmbeddedNavigator.TextLocation" type="DevExpress.XtraEditors.NavigatorButtonsTextLocation, DevExpress.XtraEditors.v15.1">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;cmbIsCredit.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="labelControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>419, 301</value>
  </data>
  <data name="&gt;&gt;labelControl40.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;txt_BankAccNum.ZOrder" xml:space="preserve">
    <value>35</value>
  </data>
  <data name="txtZip.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblTaxDepartment.Size" type="System.Drawing.Size, System.Drawing">
    <value>78, 13</value>
  </data>
  <data name="groupControl2.AppearanceCaption.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txt_Bank.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;labelControl8.ZOrder" xml:space="preserve">
    <value>79</value>
  </data>
  <data name="lkpDelivery.Properties.AppearanceDropDownHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtCusCode.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl41.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl43.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl30.Location" type="System.Drawing.Point, System.Drawing">
    <value>167, 62</value>
  </data>
  <data name="txtOpenAmount.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="lkp_Category.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="txt_Governate.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl44.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Red</value>
  </data>
  <data name="&gt;&gt;barManager1.Name" xml:space="preserve">
    <value>barManager1</value>
  </data>
  <assembly alias="DevExpress.Utils.v15.1" name="DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="lkp_country.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;groupControl1.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="btnAddEmpPhoho.ToolTip" xml:space="preserve">
    <value>Upload Pic</value>
  </data>
  <data name="lkp_Regions.Properties.AppearanceDropDownHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;txtAddress.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="txtEmail.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txt_Rep_ID.Name" xml:space="preserve">
    <value>txt_Rep_ID</value>
  </data>
  <data name="lkpPriceLevel.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_country.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtZip.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="lkpDelivery.Properties.Columns1" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="txtCusNameAr.Size" type="System.Drawing.Size, System.Drawing">
    <value>344, 20</value>
  </data>
  <data name="txt_Street.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_Category.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;barBtnList.Name" xml:space="preserve">
    <value>barBtnList</value>
  </data>
  <data name="&gt;&gt;labelControl43.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="txtImageDesc.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="&gt;&gt;txtCusNameAr.ZOrder" xml:space="preserve">
    <value>58</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns8" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="labelControl32.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl39.Name" xml:space="preserve">
    <value>labelControl39</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns1" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="lkp_country.Properties.AppearanceDropDownHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_BuildingNumber.Location" type="System.Drawing.Point, System.Drawing">
    <value>295, 238</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkpPriceLevel.Properties.Columns7" xml:space="preserve">
    <value>IsRatio</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="txtCusCode.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;btnBrowse.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridBand4.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.GridBand, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl38.Text" xml:space="preserve">
    <value>Collect Empolyee</value>
  </data>
  <data name="txtRepFName.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="cmbCsType.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnEditPhoto.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtCusNameEn.Size" type="System.Drawing.Size, System.Drawing">
    <value>344, 20</value>
  </data>
  <data name="&gt;&gt;btnShowPhotoes.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="txtDiscRatio.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;btn_AddRegion.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="txtTradeRegistry.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;labelControl34.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Governate.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_IsTaxable.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Name" xml:space="preserve">
    <value>barDockControlTop</value>
  </data>
  <data name="&gt;&gt;col_Telephone.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="chk_IsActive.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMaxCredit.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl17.Location" type="System.Drawing.Point, System.Drawing">
    <value>418, 414</value>
  </data>
  <data name="txtTel.Location" type="System.Drawing.Point, System.Drawing">
    <value>66, 108</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns23" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;txt_Street.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="&gt;&gt;labelControl24.ZOrder" xml:space="preserve">
    <value>41</value>
  </data>
  <data name="txtMaxCredit.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_IdNumber.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl4.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtOpenAmount.Properties.NullText" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txt_Rep_Phone.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="btnDeleteEmpPhoto.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_RepFJob.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="BuildingNumber.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 13</value>
  </data>
  <data name="txtTel.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_Regions.Properties.AppearanceDropDown.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtZip.Name" xml:space="preserve">
    <value>txtZip</value>
  </data>
  <data name="txtZip.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_RepresentativeJob.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl49.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtManagerName.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="labelControl46.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl43.Location" type="System.Drawing.Point, System.Drawing">
    <value>48, 61</value>
  </data>
  <data name="&gt;&gt;labelControl10.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="btn_AddRegion.TabIndex" type="System.Int32, mscorlib">
    <value>227</value>
  </data>
  <data name="lkp_Category.Properties.Columns3" xml:space="preserve">
    <value>CustomerGroupId</value>
  </data>
  <data name="&gt;&gt;labelControl44.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="lkp_country.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_Rep_Phone.Location" type="System.Drawing.Point, System.Drawing">
    <value>262, 469</value>
  </data>
  <data name="&gt;&gt;labelControl2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;uc_LinkAccount1.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="txtImageDesc.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMaxCredit.Properties.Mask.EditMask" xml:space="preserve">
    <value>n2</value>
  </data>
  <data name="chk_IsTaxable.Properties.Caption" xml:space="preserve">
    <value>Customer is Taxable</value>
  </data>
  <data name="&gt;&gt;txtTaxCardNumber.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="&gt;&gt;lkp_SalesEmp.ZOrder" xml:space="preserve">
    <value>49</value>
  </data>
  <data name="dtOpenBalance.Properties.CalendarTimeProperties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="btnEditPhoto.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Red</value>
  </data>
  <data name="&gt;&gt;txt_Representative.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;txtFax.Name" xml:space="preserve">
    <value>txtFax</value>
  </data>
  <data name="&gt;&gt;txtCity.ZOrder" xml:space="preserve">
    <value>83</value>
  </data>
  <data name="labelControl45.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="txtTaxDepartment.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="&gt;&gt;bar2.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Bar, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_Regions.Properties.AppearanceDropDownHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl49.Name" xml:space="preserve">
    <value>labelControl49</value>
  </data>
  <data name="txtImageDesc.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtImageDesc.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl22.Parent" xml:space="preserve">
    <value>groupControl2</value>
  </data>
  <data name="&gt;&gt;labelControl46.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="BuildingNumber.TabIndex" type="System.Int32, mscorlib">
    <value>238</value>
  </data>
  <data name="&gt;&gt;btnDeleteEmpPhoto.Name" xml:space="preserve">
    <value>btnDeleteEmpPhoto</value>
  </data>
  <data name="&gt;&gt;txtImageDesc.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtFax.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_Group.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_RepFJob.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns15" xml:space="preserve">
    <value>Price List Name</value>
  </data>
  <data name="labelControl25.Location" type="System.Drawing.Point, System.Drawing">
    <value>419, 498</value>
  </data>
  <data name="labelControl10.Size" type="System.Drawing.Size, System.Drawing">
    <value>14, 13</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns25" xml:space="preserve">
    <value />
  </data>
  <data name="dtOpenBalance.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_SalesEmp.Size" type="System.Drawing.Size, System.Drawing">
    <value>346, 20</value>
  </data>
  <data name="&gt;&gt;txt_Bank.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_RepFJob.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl50.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;tab_main.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txtCity.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="txt_BankAccNum.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="labelControl35.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 13</value>
  </data>
  <data name="txt_Bank.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Rep_ID.Size" type="System.Drawing.Size, System.Drawing">
    <value>150, 20</value>
  </data>
  <data name="txtRepFName.Location" type="System.Drawing.Point, System.Drawing">
    <value>67, 412</value>
  </data>
  <data name="txtImageDesc.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txt_DueDays.Properties.Buttons10" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Bank.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_DueDays.TabIndex" type="System.Int32, mscorlib">
    <value>23</value>
  </data>
  <data name="&gt;&gt;txt_Bank.ZOrder" xml:space="preserve">
    <value>33</value>
  </data>
  <data name="lkpDelivery.Properties.AppearanceDropDown.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtImageDesc.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="btnBrowse.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl17.TabIndex" type="System.Int32, mscorlib">
    <value>180</value>
  </data>
  <data name="chk_IsBlocked.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="txtImageDesc.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txt_Neighborhood.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="lkp_country.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="txtCusNameAr.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl6.Location" type="System.Drawing.Point, System.Drawing">
    <value>417, 550</value>
  </data>
  <data name="lkp_Regions.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="dtOpenBalance.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="&gt;&gt;labelControl37.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtCusNameAr.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl17.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="lkpPriceLevel.Properties.AppearanceDropDown.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_Regions.Properties.AppearanceDropDown.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_AM.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtFax.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTradeRegistry.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="lstPhotos.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="txtOpenAmount.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="txt_Rep_ID.Location" type="System.Drawing.Point, System.Drawing">
    <value>67, 469</value>
  </data>
  <data name="&gt;&gt;txtAddress.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.MemoEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtTel.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;btnNext.Name" xml:space="preserve">
    <value>btnNext</value>
  </data>
  <data name="txt_RepFJob.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_BankAccNum.Location" type="System.Drawing.Point, System.Drawing">
    <value>66, 360</value>
  </data>
  <data name="&gt;&gt;txtEmail.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;xtraTabControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="cmbCsType.EditValue" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;btnCustomerItems.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="col_Sell.Caption" xml:space="preserve">
    <value>Sell</value>
  </data>
  <data name="txtImageDesc.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtOpenAmount.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblTaxDepartment.Text" xml:space="preserve">
    <value>Tax Department</value>
  </data>
  <data name="txtCusCode.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;txtCity.Name" xml:space="preserve">
    <value>txtCity</value>
  </data>
  <data name="&gt;&gt;txt_BuildingNumber.Name" xml:space="preserve">
    <value>txt_BuildingNumber</value>
  </data>
  <data name="txtImagePath.Size" type="System.Drawing.Size, System.Drawing">
    <value>170, 20</value>
  </data>
  <data name="&gt;&gt;btnCustomerItems.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_Group.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;col_Notes.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtTel.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="lkpDelivery.Properties.Columns5" xml:space="preserve">
    <value>طريقة التسليم  ج</value>
  </data>
  <data name="labelControl24.TabIndex" type="System.Int32, mscorlib">
    <value>201</value>
  </data>
  <data name="labelControl35.TabIndex" type="System.Int32, mscorlib">
    <value>222</value>
  </data>
  <data name="&gt;&gt;labelControl28.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="btnPrev.TabIndex" type="System.Int32, mscorlib">
    <value>81</value>
  </data>
  <data name="txtMobile.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;gridBand5.Name" xml:space="preserve">
    <value>gridBand5</value>
  </data>
  <data name="btnEditPhoto.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpPriceLevel.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="dtOpenBalance.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl40.Size" type="System.Drawing.Size, System.Drawing">
    <value>67, 13</value>
  </data>
  <data name="&gt;&gt;txt_RepresentativeJob.ZOrder" xml:space="preserve">
    <value>59</value>
  </data>
  <data name="txtFax.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_Group.Location" type="System.Drawing.Point, System.Drawing">
    <value>67, 32</value>
  </data>
  <data name="chk_IsActive.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_RepFJob.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Representative.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl12.Location" type="System.Drawing.Point, System.Drawing">
    <value>417, 161</value>
  </data>
  <data name="txtTradeRegistry.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtEmail.Size" type="System.Drawing.Size, System.Drawing">
    <value>344, 20</value>
  </data>
  <data name="&gt;&gt;labelControl7.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="lkp_country.Properties.Columns" xml:space="preserve">
    <value>CountryId</value>
  </data>
  <data name="txtTaxFileNumber.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="groupControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>512, 115</value>
  </data>
  <data name="&gt;&gt;labelControl29.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="labelControl45.TabIndex" type="System.Int32, mscorlib">
    <value>245</value>
  </data>
  <data name="&gt;&gt;txtCusNameEn.ZOrder" xml:space="preserve">
    <value>62</value>
  </data>
  <data name="chk_IsBlocked.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="groupControl2.AppearanceCaption.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_Group.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txt_Street.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtEmail.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Rep_Phone.TabIndex" type="System.Int32, mscorlib">
    <value>217</value>
  </data>
  <data name="dtOpenBalance.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Rep_Phone.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbIsCredit.Properties.AppearanceDropDown.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl48.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;chk_IsBlocked.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl31.ZOrder" xml:space="preserve">
    <value>36</value>
  </data>
  <data name="&gt;&gt;groupControl2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GroupControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl49.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="txt_RepresentativeJob.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtOpenAmount.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;barManager1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarManager, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl20.Size" type="System.Drawing.Size, System.Drawing">
    <value>36, 13</value>
  </data>
  <data name="txtCusCode.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl28.Location" type="System.Drawing.Point, System.Drawing">
    <value>420, 363</value>
  </data>
  <data name="labelControl36.Location" type="System.Drawing.Point, System.Drawing">
    <value>221, 297</value>
  </data>
  <data name="btnBrowse.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl27.TabIndex" type="System.Int32, mscorlib">
    <value>79</value>
  </data>
  <data name="txt_Neighborhood.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>558, 776</value>
  </data>
  <data name="labelControl40.TabIndex" type="System.Int32, mscorlib">
    <value>231</value>
  </data>
  <data name="txt_BankAccNum.Size" type="System.Drawing.Size, System.Drawing">
    <value>147, 20</value>
  </data>
  <data name="&gt;&gt;gridBand1.Name" xml:space="preserve">
    <value>gridBand1</value>
  </data>
  <data name="labelControl18.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 13</value>
  </data>
  <data name="lkp_CollectEmp.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;cmbIsCredit.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMaxCredit.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtImagePath.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="cmbCsType.Location" type="System.Drawing.Point, System.Drawing">
    <value>67, 6</value>
  </data>
  <data name="&gt;&gt;lkp_country.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="lkp_CollectEmp.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Bank.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="txtDiscRatio.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;labelControl5.Name" xml:space="preserve">
    <value>labelControl5</value>
  </data>
  <data name="&gt;&gt;txt_DueDays.Name" xml:space="preserve">
    <value>txt_DueDays</value>
  </data>
  <data name="&gt;&gt;colDayName.Name" xml:space="preserve">
    <value>colDayName</value>
  </data>
  <data name="labelControl43.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Red</value>
  </data>
  <data name="&gt;&gt;repositoryItemTextEdit1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemTextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl41.Name" xml:space="preserve">
    <value>labelControl41</value>
  </data>
  <data name="txtDiscRatio.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="txtAddress.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 22</value>
  </data>
  <data name="lkp_country.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtManagerName.Name" xml:space="preserve">
    <value>txtManagerName</value>
  </data>
  <data name="txtCusNameEn.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="btnCustomerItems.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="bandedGridView1.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="$this.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="txt_RepFJob.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="xtraTabControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>549, 720</value>
  </data>
  <data name="lkp_CollectEmp.Size" type="System.Drawing.Size, System.Drawing">
    <value>344, 20</value>
  </data>
  <data name="&gt;&gt;lblOpenDate.Name" xml:space="preserve">
    <value>lblOpenDate</value>
  </data>
  <data name="lkpDelivery.Properties.AppearanceDropDown.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpDelivery.Properties.Columns3" xml:space="preserve">
    <value>طريقة التسليم</value>
  </data>
  <data name="lkp_Regions.Properties.Columns1" xml:space="preserve">
    <value>Region Name F</value>
  </data>
  <data name="xtraTabControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Right</value>
  </data>
  <data name="txt_Neighborhood.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 20</value>
  </data>
  <data name="&gt;&gt;chk_IsBlocked.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="gridVisits.EmbeddedNavigator.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtTaxCardNumber.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="txtTaxCardNumber.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txtMaxCredit.ZOrder" xml:space="preserve">
    <value>74</value>
  </data>
  <data name="dtOpenBalance.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;labelControl50.Name" xml:space="preserve">
    <value>labelControl50</value>
  </data>
  <data name="barDockControlBottom.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtOpenAmount.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="labelControl30.Size" type="System.Drawing.Size, System.Drawing">
    <value>34, 13</value>
  </data>
  <data name="txtDiscRatio.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="lkp_Category.Properties.Columns7" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txtImageDesc.Name" xml:space="preserve">
    <value>txtImageDesc</value>
  </data>
  <data name="lblOpenBalance.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtZip.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="btnDeleteEmpPhoto.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;barDockControlBottom.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtDiscRatio.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtFax.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;labelControl45.Name" xml:space="preserve">
    <value>labelControl45</value>
  </data>
  <data name="txtAddress.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;btnNext.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Rep_Phone.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl43.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;txtTaxFileNumber.Name" xml:space="preserve">
    <value>txtTaxFileNumber</value>
  </data>
  <data name="&gt;&gt;lstPhotos.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ListBoxControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="btnDeleteEmpPhoto.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="chk_IsBlocked.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtZip.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 20</value>
  </data>
  <data name="txtEmail.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="btnCustomerItems.Text" xml:space="preserve">
    <value>Click here to add Items for Customer</value>
  </data>
  <data name="&gt;&gt;labelControl30.Name" xml:space="preserve">
    <value>labelControl30</value>
  </data>
  <data name="txtCity.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpDelivery.Properties.Columns4" xml:space="preserve">
    <value>DeliveryF</value>
  </data>
  <data name="labelControl45.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtCusCode.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTaxDepartment.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;labelControl49.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl48.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="txtAddress.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="cmbCsType.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="barBtnSave.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="barBtnDelete.Caption" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="labelControl47.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Red</value>
  </data>
  <data name="lkpPriceLevel.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_Representative.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="labelControl9.Size" type="System.Drawing.Size, System.Drawing">
    <value>19, 13</value>
  </data>
  <data name="txtTaxDepartment.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txt_BuildingNumber.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="barBtnClose.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="lkp_Regions.Properties.Columns3" xml:space="preserve">
    <value>Region Name</value>
  </data>
  <data name="&gt;&gt;labelControl8.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="txtCity.Location" type="System.Drawing.Point, System.Drawing">
    <value>65, 187</value>
  </data>
  <data name="&gt;&gt;labelControl33.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtImagePath.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtMobile.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="labelControl12.TabIndex" type="System.Int32, mscorlib">
    <value>172</value>
  </data>
  <data name="txtCusCode.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtManagerName.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl4.Location" type="System.Drawing.Point, System.Drawing">
    <value>417, 61</value>
  </data>
  <data name="txt_Street.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_Rep_Phone.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtImagePath.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl8.Size" type="System.Drawing.Size, System.Drawing">
    <value>69, 13</value>
  </data>
  <data name="&gt;&gt;lkp_Category.ZOrder" xml:space="preserve">
    <value>42</value>
  </data>
  <data name="txtFax.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl15.Text" xml:space="preserve">
    <value>Tel</value>
  </data>
  <data name="&gt;&gt;lkpDelivery.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="barDockControlTop.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtEmail.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDiscRatio.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="lkpPriceLevel.Properties.AppearanceDropDown.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl4.Name" xml:space="preserve">
    <value>labelControl4</value>
  </data>
  <data name="labelControl40.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl44.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl50.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtDiscRatio.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;lkp_Regions.Name" xml:space="preserve">
    <value>lkp_Regions</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="txt_IdNumber.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txtTaxFileNumber.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;labelControl18.Name" xml:space="preserve">
    <value>labelControl18</value>
  </data>
  <data name="txtShipping.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Regions.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Street.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl44.Location" type="System.Drawing.Point, System.Drawing">
    <value>48, 190</value>
  </data>
  <data name="&gt;&gt;lkp_Category.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="gridVisits.TabIndex" type="System.Int32, mscorlib">
    <value>79</value>
  </data>
  <data name="lkp_Group.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl34.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="txtRepFName.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Representative.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl28.TabIndex" type="System.Int32, mscorlib">
    <value>214</value>
  </data>
  <data name="barDockControlLeft.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 28</value>
  </data>
  <data name="txt_RepFJob.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="uc_LinkAccount1.Size" type="System.Drawing.Size, System.Drawing">
    <value>379, 73</value>
  </data>
  <data name="txt_BuildingNumber.Size" type="System.Drawing.Size, System.Drawing">
    <value>116, 20</value>
  </data>
  <data name="btnDeleteEmpPhoto.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl25.Text" xml:space="preserve">
    <value>Sales Empolyee</value>
  </data>
  <data name="lkpDelivery.Location" type="System.Drawing.Point, System.Drawing">
    <value>67, 328</value>
  </data>
  <data name="&gt;&gt;labelControl27.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="lkp_CollectEmp.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="txt_Neighborhood.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDiscRatio.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txt_Governate.Name" xml:space="preserve">
    <value>txt_Governate</value>
  </data>
  <data name="labelControl15.Location" type="System.Drawing.Point, System.Drawing">
    <value>221, 111</value>
  </data>
  <data name="&gt;&gt;labelControl44.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="&gt;&gt;txtTaxCardNumber.Name" xml:space="preserve">
    <value>txtTaxCardNumber</value>
  </data>
  <data name="&gt;&gt;txt_RepFJob.Name" xml:space="preserve">
    <value>txt_RepFJob</value>
  </data>
  <data name="gridVisits.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="&gt;&gt;labelControl45.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridBand1.Name" xml:space="preserve">
    <value>gridBand1</value>
  </data>
  <data name="col_PM.Caption" xml:space="preserve">
    <value>PM</value>
  </data>
  <data name="&gt;&gt;lkp_country.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="txt_Rep_ID.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;gridBand2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.GridBand, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpDelivery.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl22.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl13.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="chk_IsBlocked.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl47.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="lkpDelivery.Properties.Columns2" xml:space="preserve">
    <value>Delivery</value>
  </data>
  <data name="&gt;&gt;txtTradeRegistry.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="txt_IdNumber.Location" type="System.Drawing.Point, System.Drawing">
    <value>294, 183</value>
  </data>
  <data name="btnEditPhoto.Size" type="System.Drawing.Size, System.Drawing">
    <value>25, 23</value>
  </data>
  <data name="btnBrowse.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;lblOpenAmount.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl48.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl26.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barDockControlBottom.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="btnDeleteEmpPhoto.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Red</value>
  </data>
  <data name="&gt;&gt;labelControl36.Name" xml:space="preserve">
    <value>labelControl36</value>
  </data>
  <data name="txt_BuildingNumber.TabIndex" type="System.Int32, mscorlib">
    <value>237</value>
  </data>
  <data name="txtOpenAmount.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtZip.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtTaxCardNumber.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;lstPhotos.Name" xml:space="preserve">
    <value>lstPhotos</value>
  </data>
  <data name="txtTaxDepartment.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Rep_ID.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl29.Location" type="System.Drawing.Point, System.Drawing">
    <value>168, 37</value>
  </data>
  <data name="btnAddEmpPhoho.Size" type="System.Drawing.Size, System.Drawing">
    <value>25, 23</value>
  </data>
  <data name="&gt;&gt;labelControl25.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="labelControl38.Location" type="System.Drawing.Point, System.Drawing">
    <value>418, 524</value>
  </data>
  <data name="labelControl45.Text" xml:space="preserve">
    <value>*</value>
  </data>
  <data name="txtCusNameAr.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl23.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_country.Properties.Columns8" xml:space="preserve">
    <value>CountryName</value>
  </data>
  <data name="labelControl36.TabIndex" type="System.Int32, mscorlib">
    <value>226</value>
  </data>
  <data name="txtRepFName.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_IdNumber.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtOpenAmount.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barBtnList.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="txt_IdNumber.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Category.Properties.Columns4" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="labelControl23.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="labelControl42.TabIndex" type="System.Int32, mscorlib">
    <value>242</value>
  </data>
  <data name="labelControl13.Location" type="System.Drawing.Point, System.Drawing">
    <value>420, 331</value>
  </data>
  <data name="txtMobile.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txt_DueDays.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="barDockControlLeft.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_country.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>74, 13</value>
  </data>
  <data name="txtCusCode.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="labelControl5.Location" type="System.Drawing.Point, System.Drawing">
    <value>417, 111</value>
  </data>
  <data name="cmbCsType.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="labelControl15.Size" type="System.Drawing.Size, System.Drawing">
    <value>14, 13</value>
  </data>
  <data name="&gt;&gt;txtShipping.Name" xml:space="preserve">
    <value>txtShipping</value>
  </data>
  <data name="&gt;&gt;col_Id.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_IdNumber.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;chk_IsActive.Name" xml:space="preserve">
    <value>chk_IsActive</value>
  </data>
  <data name="lkp_Group.Properties.AppearanceDropDownHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;Governate.Name" xml:space="preserve">
    <value>Governate</value>
  </data>
  <data name="lkp_SalesEmp.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl21.TabIndex" type="System.Int32, mscorlib">
    <value>180</value>
  </data>
  <data name="txtOpenAmount.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;rep_Check.Name" xml:space="preserve">
    <value>rep_Check</value>
  </data>
  <data name="labelControl13.Text" xml:space="preserve">
    <value>Ship to</value>
  </data>
  <data name="txtTaxFileNumber.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Governate.TabIndex" type="System.Int32, mscorlib">
    <value>236</value>
  </data>
  <data name="&gt;&gt;lkp_Category.Name" xml:space="preserve">
    <value>lkp_Category</value>
  </data>
  <data name="labelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>417, 9</value>
  </data>
  <data name="col_Collect.Caption" xml:space="preserve">
    <value>Collect</value>
  </data>
  <data name="labelControl43.TabIndex" type="System.Int32, mscorlib">
    <value>243</value>
  </data>
  <data name="txtCusNameAr.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_RepresentativeJob.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtMaxCredit.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl17.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_Street.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dtOpenBalance.Location" type="System.Drawing.Point, System.Drawing">
    <value>138, 7</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpPriceLevel.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_RepFJob.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;txt_Rep_ID.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="chk_IsTaxable.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="txtOpenAmount.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl44.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl28.Size" type="System.Drawing.Size, System.Drawing">
    <value>23, 13</value>
  </data>
  <data name="lkpDelivery.Size" type="System.Drawing.Size, System.Drawing">
    <value>149, 20</value>
  </data>
  <data name="&gt;&gt;txt_DueDays.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl47.Name" xml:space="preserve">
    <value>labelControl47</value>
  </data>
  <data name="labelControl39.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAA
        AAAfbf8AH2z/AB9r/x8fa/+rH2v/9h9r/9AfbP9HH2//AB9u/wAfcP8AAAAAAB9p/wAfZv8AH2D/Ah9j
        /2cfY//iH2P/7R9j/4YfY/8KH2P/AB9j/wAAAAAADw4QAA4NDwAODQ4ADQwORwwMDdAMCw32CwsMqwsL
        DB8MCw0ADAsNAB9u/wAfbf8WH23/qx9t//8fbv//H27//x9u/9gfb/89H3D/AB9y/wAfcf8AH23/AB9n
        /wAfZ/9cH2X/6x9j//8fY///H2P/9x9j/38fY/8GH2P/AB5e8QAPDhAADw4QAA4NDz0NDQ/YDQ0O/w0M
        Dv8MDA3/DAsNqwwLDRYMCw0AH2//FR9v/6Ifb///H3D//x9x//8fcf//H3H//x9x/9Mfcv82IHD/AiBw
        /wQfcP8DH2z/VB9q/+gfZ///H2X//x9j//8fY///H2P/9x9j/3cfY/8FH2P+ABAPEQAQDxE1Dw4Q0w8O
        EP8ODg//Dg0P/w0NDv8MDA3/DAsNogwLDRUfcP+MH3H//R9y//8ec///HnP//x50//8edP//HnP//x5z
        /9cec/+pH3L/qR9x/6wfbv/mH23//x9q//8faP//H2X//x9j//8fY///H2P/8h9j/04bTb8AERASERAQ
        ErsQEBL/EA8R/w8PEf8PDhD/Dg0P/w0ND/8MDA79DAwNjB5z/9QedP//HnX//x52//8edv//Hnb//x52
        //8edv//Hnb//x51//8edP//HnP//x9x//8fb///H23//x9r//8faP//H2X//x9j//8fY///H2P/gxg4
        hAASERM1ERET7hERE/8REBL/EBAS/xAPEf8PDhD/Dg4Q/w0ND/8NDA7THnX/wx52//8ed///Hnj//x54
        //8eef//Hnn//x54//8eeP/9Hnf/8h52//Iedf/zHnP//h9x//8fb///H23//x9q//8fZ///H2T//x9j
        //8fY/90GkGeABISFCYTEhThEhIU/xIRE/8RERP/EBAS/xAPEf8PDhD/Dg4Q/w4ND78ed/9UHnj/5x55
        //8eev//Hnr//x57//8ee///Hnr/+x56/5AeeP88Hnf/PB52/0Eedf+wHnP//x9x//8fb///H2z//x9p
        //8fZv//H2T/zB9j/yQdV9wAExIVAxMTFYMTEhX8ExIU/xISFP8RERP/ERAS/xAPEf8PDhDkDg4QTx50
        /wAeev9dHnv/7R58//8efP//Hn3//x59//0efP+UHnv/DB55/wAed/8AHnb/AB52/x8edf+5HnP//x9x
        //8fbv//H2v//x9o/9YfZf85H2f/AAAAAAAUExYAFBMWDRQTFpQUExX9ExIV/xISFP8SERP/ERAS6hAP
        EVYTDhQAHnr/AB58/wEefP9nHn3/7x5+//8efv/9Hn7/nR59/xAefP8AHnv/AB52/wAedf8AHnX/AB51
        /yUedP2+H3P//x9x//8fbPvbH2r/Px9o/wAfZv8AH2P/ABQTFgAUExYAFBQWEBQUFp0UExb9ExIV/xIS
        FOwRERNfIxkMABAQEQAee/8AHn3/AB5+/wcefv+xHn///x5//+oef/8tHn//AB59/wAefP8AAAAAAB52
        /wAeV/8AGkKGABk+eUYaQoPzG0KG/xo0ZZIbRZkAH2v/AB9o/wAAAAAAFBQWABQUFgAVFBcAFRQXLRUU
        F+oUExb/ExMVqxMTFAUSERQAEBASAAAAAAAegP8AHn//Ax6A/6kegP//HoD/5x6A/ycegP8AHoD/AAAA
        AAAAAAAAAAAAABpCgQAXFRcAFhMSOhcVFvAXFRb/GBUWjhgYHgAcO3gAAAAAAAAAAAAAAAAAFxYZABYV
        GAAWFRgnFRUX5xUUF/8UExajExIVAhQTFgAAAAAAHoD/AB6A/wAegP8IHoD/sx6B//8egf/rHoH/Lx6B
        /wAegv8AHoL/AAAAAAAXFhkAFBQXABcXGgAXFxpIGBca9BgYG/8ZGBuTGRkcABoaHQAbGx4AAAAAABkZ
        HAAYGBsAFxcZABcWGS8WFhjrFRUX/xUUF6wUFBYGFBMWABQTFQAef/8AHoD/Ah6A/20egf/xHoH//x6B
        //4egv+kHoL/Ex6C/wAegv8AFhYYABsZIAAXFxoAFxcaKRgXGsMYGBv/GRkc/xoZHN4bGh1FHBoeABwb
        HwAdHCAAGhkdABkZHAAYGBsTGBcapBcWGf4WFRj/FRQX7xQUFmYTEBMBExMVAB59/wIegP9kHoD/8B6B
        //8egv//HoL//x6C//8egv+cHoL/EB6B/wAaRX4AFxYZABcWGSQXFxq/GBgb/xkZHP8aGR3/Gxoe/xwb
        HtkcGx89HBwfABoYIQAbGh0AGxodEBkZHJwYGBv/Fxca/xYWGf8VFRf/FBQW7RQTFVwBABAAHn//Vx5/
        /+kegP//HoH//x6C//8egv//HoL//x6C//4egf+JHoD/BBgxUQAWFhgSFxYZsRgXGv8ZGBv/Ghkc/xsa
        Hf8cGx7/HBwf/x0cIM8eHSAmHRwgAB0cHwQbGh6JGhkc/hkYG/8YFxr/FxYZ/xYVGP8VFBf/ExMV5hMS
        FFIefv/EHn///x6A//8egf//HoL//x6C//8egv//HoL//x6B/+Megf8nGUB0ABYWGEwXFhn4GBca/xkY
        G/8aGh3/Gxse/xwcH/8dHCD/Hh0h/x4dIXUdHCAAHBsfJxwbHuMaGh3/GRkc/xgXGv8XFhn/FhUY/xUU
        F/8UExX/ExIUwR5+/9Uef///HoD//x6B//8egf//HoL//x6C//8egf//HoH/7h6B/zQaRX4AFhYZWhcW
        GfwYGBv/GRkc/xsaHf8cGx7/HRwg/x4dIf8fHiL/Hx4igx4dIQAdHB80HBsf7hsaHf8aGRz/GBgb/xcX
        Gf8WFRj/FRQX/xQTFf8TEhXSHn7/jR5///0egP//HoD//x6B//8egf//HoH//x6B//8egf+4HoD/EBk3
        YAAWFhkqFxYZ2xgYG/8ZGRz/Gxod/xwbHv8dHCD/Hh0h/x8eIu8gHyNKHx4iABwcHxAcGx64Gxod/xoZ
        HP8YGBv/Fxca/xYVGP8VFBf/FBMV/BMSFYgefv8VHn7/oh5///8egP//HoD//x6B//8egf//HoD/0B6A
        /zIegf8AFxIRABgaHQAXFxpQGBca5hkYHP8aGh3/Gxse/x0cH/8dHSD1Hh0hch8eIwQfHiIAGxseABwb
        HjIaGh3QGRkc/xgXG/8XFhn/FhUY/xUUF/8UExafExIVEx5+/wAefv8XHn7/qx5///8ef///Hn///x6A
        /9UegP85HoD/AB5//wAYIzUAFxcaABgYGwAYFxpXGRgb6hoZHP8bGh7/HBse+B0cH3weHSEFHh0hAB8e
        IgAbGh0AGxodABoZHDkZGBvVGBca/xcWGf8WFRj/FRQXqBQUFhUUExYAHn7/AB5+/wAefv8dHn7/yR5+
        //8efv/yHn//Sh5//wAef/8AHoD/AAAAAAAYFxoAGBcaABUXGgAZGBtUGRkcwxoaHc0bGh5yHBseCB0c
        HwAeHSEAAAAAABsaHQAaGRwAGRgbABgYG0oYFxryFxYZ/xYVGMUVFBcbFRQXABQUFgAefv8AHnz/AB56
        /wIefP+oHn3//x59/+ceff8lHn3/AB5//wAAAAAAAAAAAAAAAAAYGBoAGBgbABgXGgAaGRwNGhkdERkZ
        HAAbGh4AGxoeAAAAAAAAAAAAAAAAABkZHAAXFxoAFxcaJRcWGecWFRj/FRUXohMSFQEVFBcAFRQXAB50
        /wAed/8AHnn/CR56/7Qee///Hnv/6x57/y4eev8AHnj/AB53/wAAAAAAFRQXABYVGAAWFRgAFxYZJBgX
        GnEYFxp6GRgbNBkOEQAYGBsAGBcaAAAAAAAWFhgAFhYYABYWGQAXFhkuFhYY6xUVF/8VFBeuFBQWBxMT
        FQASERQAHnL/AB11/wMedv9zHnf/8x54//8eef/+Hnn/oR55/xEed/8AHnb/ABMSFgAbFx0AFRQXABUV
        Fy4WFhjJFxYZ/xgXGv8YFxreGBcaShcXGgAYFxoAFxYZABYVGAAWFRgAFhYYERYWGKEWFRj+FRQX/xQT
        FvETEhVuEhETAxIREwAgb/8CH3L/aR50//Iedf//Hnb//x53//8ed//+Hnf/lx52/w0edf8AGEOMABQT
        FgAUExYnFRQXxBUVF/8WFhj/FxYZ/xcXGv8XFxrdFxcaQRYWGQAWFRgAFhUYABYVGA0WFRiXFhUY/hUU
        F/8UExb/ExMV/xISFPARERNlEQ4RAh9t/1sfb//sH3H//x9y//8ec///HnT//x50//8edP/9HnT/hx9y
        /wUVKUwAEhIUFBMSFbYUExb/FBQW/xERE/8QEBL/FhUY/xYWGf8XFhnXFhYYYBYVGE4WFRhOFhUYmBUV
        F/wVFBf/FBMW/xMTFf8SEhT/ERET/xEQEuoQDxFXH2v/xh9s//8fbv//H2///x9w//8fcf//H3H//x9x
        //8fcf/kH3D/KBc3cQASERNOEhIU+BMSFf8SERT/DAwN/woJC/8QDxH/FRUX/xYVGP8WFRj7FRUX+BUU
        F/gVFBf/FBQW/xQTFv8TEhX/EhIU/xIRE/8REBL/EA8R/w8PEMMfZ//UH2n//x9r//8fbP//H23//x9u
        //8fbv//H27//x9u/+4fbv80Fzl6ABEQE1oRERP8EhIU/xERE/8ODg//DAwN/xAPEf8UExb/FBQW/xQU
        Fv8UFBb/FBMW/xQTFv8TExX/ExIU/xISFP8SERP/ERAS/xAQEv8PDxH/Dg4Q0R9l/4kfZf/8H2f//x9o
        //8faf//H2r//x9q//8fav//H2v/sh9r/w4VKlcAEBASKRAQEtoRERP/EhET/xEQEv8QEBL/EhIU/xMT
        Ff8TExXyExMVsxMTFagTEhWoExIU1BISFP8SERP/ERET/xEQEv8QDxH/Dw8R/w4OEPwODQ+EH2P/Ex9j
        /6AfZP//H2T//x9l//8fZv//H2b//x9n/8gfaP8qH2j/AA0CAAAREBIAEA8RThAQEuUREBL/ERET/xIR
        E/8SERP/EhEU9hISFHYTExUIEhEUAxMRFQISERMxERETzREQEv8QEBL/EA8R/w8OEP8ODhD/Dg0Pnw0N
        DxIfY/8AH2P/Fh9j/6kfY///H2P//x9j//8fY//MH2T/MB9m/wAfVP8AERgnABAPEQAQERMAEA8RVRAP
        EegQEBL/EBAS/xEQEvcRERN9EhETBhISFAATEhUAExIVABEREwAQEBI2EA8R0g8PEf8PDhD/Dg4P/w4N
        D6gNDQ8VDg0PAB9j/wAfY/8AH2P/HR9j/6QfY//nH2P/wh9j/zofZP8AH2P/AB9m/wAAAAAAEA8RAA8P
        EQAKDhIBDw8QYA8PEdcQDxHiEA8RgRAPEQkREBIAEhETAAAAAAARERMAEA8RABAPEQAPDhBADg4Qxg4N
        D+cODQ+iDQ0OHA0NDwAODQ8AACAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAGAIBw
        DgEAIAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAQAAHAOAAAgBAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgBAA=
</value>
  </data>
  <data name="&gt;&gt;labelControl31.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_RepFJob.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtTaxFileNumber.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Neighborhood.Location" type="System.Drawing.Point, System.Drawing">
    <value>262, 268</value>
  </data>
  <data name="lkpPriceLevel.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="dtOpenBalance.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtTaxCardNumber.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="labelControl9.TabIndex" type="System.Int32, mscorlib">
    <value>152</value>
  </data>
  <data name="txt_DueDays.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="btnPrev.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtTradeRegistry.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 59</value>
  </data>
  <data name="txt_Rep_Phone.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl4.ZOrder" xml:space="preserve">
    <value>60</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns14" xml:space="preserve">
    <value>PLName</value>
  </data>
  <data name="txtOpenAmount.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns24" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="txtDiscRatio.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtImagePath.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;btnNext.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;lblOpenAmount.Parent" xml:space="preserve">
    <value>pnlOpenBlnce</value>
  </data>
  <data name="gridVisits.EmbeddedNavigator.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>Inherit</value>
  </data>
  <data name="labelControl44.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;btnDeleteEmpPhoto.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="labelControl5.Size" type="System.Drawing.Size, System.Drawing">
    <value>30, 13</value>
  </data>
  <data name="txtTradeRegistry.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="txt_DueDays.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl27.Name" xml:space="preserve">
    <value>labelControl27</value>
  </data>
  <data name="&gt;&gt;labelControl14.ZOrder" xml:space="preserve">
    <value>69</value>
  </data>
  <data name="labelControl26.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;labelControl43.Name" xml:space="preserve">
    <value>labelControl43</value>
  </data>
  <data name="btnEditPhoto.TabIndex" type="System.Int32, mscorlib">
    <value>55</value>
  </data>
  <data name="txt_RepFJob.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txtOpenAmount.Name" xml:space="preserve">
    <value>txtOpenAmount</value>
  </data>
  <data name="txtMaxCredit.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_country.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="txtCusNameAr.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtTel.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_Representative.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl49.Location" type="System.Drawing.Point, System.Drawing">
    <value>263, 37</value>
  </data>
  <data name="&gt;&gt;txtTel.Name" xml:space="preserve">
    <value>txtTel</value>
  </data>
  <data name="cmbCsType.Size" type="System.Drawing.Size, System.Drawing">
    <value>147, 20</value>
  </data>
  <data name="labelControl4.Size" type="System.Drawing.Size, System.Drawing">
    <value>76, 13</value>
  </data>
  <data name="lblOpenAmount.Size" type="System.Drawing.Size, System.Drawing">
    <value>37, 13</value>
  </data>
  <data name="labelControl12.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 13</value>
  </data>
  <data name="&gt;&gt;labelControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtTaxFileNumber.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;cmbCsType.ZOrder" xml:space="preserve">
    <value>24</value>
  </data>
  <data name="txt_Representative.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_BankAccNum.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chk_IsActive.Properties.Caption" xml:space="preserve">
    <value>Active Customer</value>
  </data>
  <data name="labelControl13.TabIndex" type="System.Int32, mscorlib">
    <value>174</value>
  </data>
  <data name="txtFax.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.XtraForm, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl8.Text" xml:space="preserve">
    <value>Discount Ratio</value>
  </data>
  <data name="&gt;&gt;chk_IsActive.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;groupControl1.Name" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="txtTaxDepartment.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;txt_Rep_ID.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="txt_RepresentativeJob.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Representative.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="lkpPriceLevel.Properties.AppearanceDropDownHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_Street.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 20</value>
  </data>
  <data name="cmbIsCredit.Properties.Items" xml:space="preserve">
    <value>Debit</value>
  </data>
  <data name="txtDiscRatio.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;btnDeleteEmpPhoto.Parent" xml:space="preserve">
    <value>groupControl2</value>
  </data>
  <data name="&gt;&gt;labelControl7.Name" xml:space="preserve">
    <value>labelControl7</value>
  </data>
  <data name="&gt;&gt;tab_docs.Parent" xml:space="preserve">
    <value>xtraTabControl1</value>
  </data>
  <data name="btnShowPhotoes.Location" type="System.Drawing.Point, System.Drawing">
    <value>202, 150</value>
  </data>
  <data name="labelControl3.Size" type="System.Drawing.Size, System.Drawing">
    <value>85, 13</value>
  </data>
  <data name="labelControl22.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="labelControl35.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtMobile.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txtImagePath.Parent" xml:space="preserve">
    <value>groupControl2</value>
  </data>
  <data name="labelControl43.Text" xml:space="preserve">
    <value>*</value>
  </data>
  <data name="txtZip.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="labelControl42.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl14.TabIndex" type="System.Int32, mscorlib">
    <value>180</value>
  </data>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="&gt;&gt;barAndDockingController1.Name" xml:space="preserve">
    <value>barAndDockingController1</value>
  </data>
  <data name="&gt;&gt;txtImagePath.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="dtOpenBalance.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 20</value>
  </data>
  <data name="txtImageDesc.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="lkp_CollectEmp.Properties.Columns14" xml:space="preserve">
    <value>EmpId</value>
  </data>
  <data name="txt_Street.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="labelControl33.Text" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="lkp_Group.Properties.Columns2" xml:space="preserve">
    <value>GroupId</value>
  </data>
  <data name="txtTel.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="btn_AddRegion.ToolTip" xml:space="preserve">
    <value>Add Region</value>
  </data>
  <data name="gridBand3.Width" type="System.Int32, mscorlib">
    <value>150</value>
  </data>
  <data name="labelControl49.TabIndex" type="System.Int32, mscorlib">
    <value>244</value>
  </data>
  <data name="txtImagePath.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtManagerName.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_CollectEmp.Properties.Columns" xml:space="preserve">
    <value>EmpName</value>
  </data>
  <data name="labelControl33.Location" type="System.Drawing.Point, System.Drawing">
    <value>220, 472</value>
  </data>
  <data name="lkp_country.Properties.Columns1" xml:space="preserve">
    <value>الدولة</value>
  </data>
  <data name="barBtnHelp.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="&gt;&gt;txt_Neighborhood.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl47.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="dtOpenBalance.Properties.Mask.EditMask" xml:space="preserve">
    <value>d</value>
  </data>
  <data name="txt_Bank.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Rep_Phone.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl23.Text" xml:space="preserve">
    <value>Pic Name</value>
  </data>
  <data name="&gt;&gt;labelControl30.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="tab_docs.Size" type="System.Drawing.Size, System.Drawing">
    <value>525, 716</value>
  </data>
  <data name="txt_BuildingNumber.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtDiscRatio.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;barDockControlLeft.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="cmbCsType.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="labelControl45.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl31.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_Bank.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtCity.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtRepFName.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;lkp_Group.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;lkp_CollectEmp.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="lkp_Category.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtManagerName.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl50.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridVisits.EmbeddedNavigator.ToolTipTitle" xml:space="preserve">
    <value />
  </data>
  <data name="chk_IsBlocked.Properties.Caption" xml:space="preserve">
    <value>Block Customer</value>
  </data>
  <data name="btnCustomerItems.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_CollectEmp.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="txtImagePath.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl42.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="lkp_Category.Location" type="System.Drawing.Point, System.Drawing">
    <value>262, 32</value>
  </data>
  <data name="labelControl49.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="cmbCsType.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtCusNameEn.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;bar1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Bar, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="cmbIsCredit.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtCity.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="txtDiscRatio.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_SalesEmp.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Rep_ID.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_CollectEmp.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_RepFJob.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridBand2.Caption" xml:space="preserve">
    <value>Visit Type</value>
  </data>
  <data name="txt_Governate.Location" type="System.Drawing.Point, System.Drawing">
    <value>66, 242</value>
  </data>
  <data name="txtFax.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;labelControl30.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="lblOpenBalance.Location" type="System.Drawing.Point, System.Drawing">
    <value>418, 609</value>
  </data>
  <data name="txt_IdNumber.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="barDockControlTop.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_SalesEmp.Properties.Columns1" xml:space="preserve">
    <value>Employee</value>
  </data>
  <data name="txtImagePath.Location" type="System.Drawing.Point, System.Drawing">
    <value>288, 28</value>
  </data>
  <data name="labelControl20.Location" type="System.Drawing.Point, System.Drawing">
    <value>224, 415</value>
  </data>
  <data name="lkpDelivery.Properties.AppearanceDropDownHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl6.Size" type="System.Drawing.Size, System.Drawing">
    <value>51, 13</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns8" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="&gt;&gt;txt_Rep_Phone.ZOrder" xml:space="preserve">
    <value>29</value>
  </data>
  <data name="txtImagePath.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtRepFName.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txtTradeRegistry.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_DueDays.Properties.Buttons11" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl23.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="txtTel.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtOpenBalance.Properties.CalendarTimeProperties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblTaxDepartment.Location" type="System.Drawing.Point, System.Drawing">
    <value>430, 62</value>
  </data>
  <data name="lkp_CollectEmp.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkpDelivery.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtRepFName.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl40.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="labelControl47.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="txt_BankAccNum.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Governate.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txt_Representative.Name" xml:space="preserve">
    <value>txt_Representative</value>
  </data>
  <data name="&gt;&gt;btnCustomerItems.Name" xml:space="preserve">
    <value>btnCustomerItems</value>
  </data>
  <data name="&gt;&gt;lkp_Regions.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;labelControl42.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl22.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 13</value>
  </data>
  <data name="groupControl2.AppearanceCaption.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="Governate.Location" type="System.Drawing.Point, System.Drawing">
    <value>219, 245</value>
  </data>
  <data name="xtraTabControl1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl1.Text" xml:space="preserve">
    <value>Customer Code</value>
  </data>
  <data name="btnEditPhoto.ToolTip" xml:space="preserve">
    <value>Edit Pic</value>
  </data>
  <data name="&gt;&gt;uc_LinkAccount1.Type" xml:space="preserve">
    <value>Pharmacy.Forms.uc_LinkAccount, LinkIT ERP System, Version=**********, Culture=neutral, PublicKeyToken=null</value>
  </data>
  <data name="txtCusNameEn.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txtCusNameEn.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_PM.Name" xml:space="preserve">
    <value>col_PM</value>
  </data>
  <data name="txt_DueDays.Properties.NullText" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txtFax.Location" type="System.Drawing.Point, System.Drawing">
    <value>262, 133</value>
  </data>
  <data name="txtDiscRatio.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;btnEditPhoto.Parent" xml:space="preserve">
    <value>groupControl2</value>
  </data>
  <data name="lkpPriceLevel.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_Rep_Phone.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Rep_ID.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Regions.Properties.AppearanceDropDown.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl37.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 13</value>
  </data>
  <data name="tab_main.Text" xml:space="preserve">
    <value>Main Info</value>
  </data>
  <data name="txtCity.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtCusCode.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="&gt;&gt;gridBand3.Name" xml:space="preserve">
    <value>gridBand3</value>
  </data>
  <data name="txtDiscRatio.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtShipping.Location" type="System.Drawing.Point, System.Drawing">
    <value>263, 326</value>
  </data>
  <data name="txtEmail.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_Category.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl47.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl21.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="dtOpenBalance.Properties.CalendarTimeProperties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="lkpPriceLevel.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="cmbCsType.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="btnBrowse.TabIndex" type="System.Int32, mscorlib">
    <value>50</value>
  </data>
  <data name="txtOpenAmount.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl25.Name" xml:space="preserve">
    <value>labelControl25</value>
  </data>
  <data name="&gt;&gt;lblOpenBalance.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;barBtnHelp.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="cmbCsType.Properties.Items7" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="labelControl27.Size" type="System.Drawing.Size, System.Drawing">
    <value>64, 13</value>
  </data>
  <data name="labelControl32.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 13</value>
  </data>
  <data name="chk_IsActive.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lkpPriceLevel.ZOrder" xml:space="preserve">
    <value>86</value>
  </data>
  <data name="&gt;&gt;labelControl37.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="lkpDelivery.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;txtTel.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_RepresentativeJob.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;txtOpenAmount.Parent" xml:space="preserve">
    <value>pnlOpenBlnce</value>
  </data>
  <data name="btnAddEmpPhoho.TabIndex" type="System.Int32, mscorlib">
    <value>53</value>
  </data>
  <data name="barDockControlRight.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 748</value>
  </data>
  <data name="txtMobile.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_IdNumber.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_CollectEmp.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;txtImageDesc.Parent" xml:space="preserve">
    <value>groupControl2</value>
  </data>
  <data name="txt_DueDays.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;txt_DueDays.ZOrder" xml:space="preserve">
    <value>50</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;bandedGridView1.Name" xml:space="preserve">
    <value>bandedGridView1</value>
  </data>
  <data name="&gt;&gt;tab_docs.Name" xml:space="preserve">
    <value>tab_docs</value>
  </data>
  <data name="&gt;&gt;gridVisits.Parent" xml:space="preserve">
    <value>tab_docs</value>
  </data>
  <data name="txt_DueDays.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="btnBrowse.ToolTip" xml:space="preserve">
    <value>Preview</value>
  </data>
  <data name="&gt;&gt;labelControl21.Name" xml:space="preserve">
    <value>labelControl21</value>
  </data>
  <data name="&gt;&gt;labelControl47.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="lkpDelivery.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="txt_Street.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txtTaxCardNumber.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_IdNumber.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtTaxCardNumber.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cmbIsCredit.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl47.Location" type="System.Drawing.Point, System.Drawing">
    <value>48, 249</value>
  </data>
  <data name="&gt;&gt;txtFax.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="labelControl5.Text" xml:space="preserve">
    <value>Mobile</value>
  </data>
  <data name="txtManagerName.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtCusNameEn.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Rep_ID.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="labelControl31.Location" type="System.Drawing.Point, System.Drawing">
    <value>223, 363</value>
  </data>
  <data name="lkp_Regions.Location" type="System.Drawing.Point, System.Drawing">
    <value>67, 294</value>
  </data>
  <data name="cmbCsType.Properties.Items4" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="txt_Bank.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 20</value>
  </data>
  <data name="labelControl19.Text" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="&gt;&gt;btnAddEmpPhoho.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="cmbCsType.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns" xml:space="preserve">
    <value>EmpName</value>
  </data>
  <data name="chk_IsBlocked.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtManagerName.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="chk_IsTaxable.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl26.ZOrder" xml:space="preserve">
    <value>38</value>
  </data>
  <data name="&gt;&gt;labelControl30.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="cmbCsType.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtMaxCredit.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtAddress.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;barDockControlBottom.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;colDayName.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_Group.TabIndex" type="System.Int32, mscorlib">
    <value>215</value>
  </data>
  <data name="txtDiscRatio.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridVisits.EmbeddedNavigator.BackgroundImage" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;gridVisits.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;txtTaxDepartment.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtOpenAmount.Size" type="System.Drawing.Size, System.Drawing">
    <value>76, 20</value>
  </data>
  <data name="barDockControlBottom.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="btnBrowse.Location" type="System.Drawing.Point, System.Drawing">
    <value>257, 28</value>
  </data>
  <data name="txt_Governate.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_Regions.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtManagerName.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns22" xml:space="preserve">
    <value>PriceLevelId</value>
  </data>
  <data name="groupControl1.Text" xml:space="preserve">
    <value>Tax</value>
  </data>
  <data name="lkp_Group.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="groupControl2.TabIndex" type="System.Int32, mscorlib">
    <value>76</value>
  </data>
  <data name="&gt;&gt;btnShowPhotoes.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl6.ZOrder" xml:space="preserve">
    <value>84</value>
  </data>
  <data name="txtImagePath.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;lkpDelivery.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="dtOpenBalance.Properties.CalendarTimeProperties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txt_Rep_Phone.Name" xml:space="preserve">
    <value>txt_Rep_Phone</value>
  </data>
  <data name="&gt;&gt;labelControl46.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;lblTaxDepartment.Name" xml:space="preserve">
    <value>lblTaxDepartment</value>
  </data>
  <data name="txtManagerName.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Governate.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;btnShowPhotoes.Parent" xml:space="preserve">
    <value>groupControl2</value>
  </data>
  <data name="txtCusCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 20</value>
  </data>
  <data name="cmbCsType.Properties.Items1" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_CollectEmp.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl9.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lblTaxDepartment.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Rep_ID.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtFax.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="btnCustomerItems.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 369</value>
  </data>
  <data name="lkp_Regions.Properties.Columns6" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="btnPrev.Location" type="System.Drawing.Point, System.Drawing">
    <value>31, 30</value>
  </data>
  <data name="txt_Rep_ID.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_BuildingNumber.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl28.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl2.Text" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="txt_RepFJob.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl45.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Red</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl38.Name" xml:space="preserve">
    <value>labelControl38</value>
  </data>
  <data name="txtZip.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbCsType.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="cmbIsCredit.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;btn_AddRegion.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;lkp_Group.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_CollectEmp.Properties.Columns15" xml:space="preserve">
    <value>EmpId</value>
  </data>
  <data name="txt_Neighborhood.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtImagePath.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="dtOpenBalance.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_CollectEmp.Properties.Columns1" xml:space="preserve">
    <value>Employee</value>
  </data>
  <data name="chk_IsActive.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="gridVisits.EmbeddedNavigator.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlRight.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;chk_IsTaxable.Name" xml:space="preserve">
    <value>chk_IsTaxable</value>
  </data>
  <data name="bandedGridView1.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="BuildingNumber.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl30.TabIndex" type="System.Int32, mscorlib">
    <value>78</value>
  </data>
  <data name="lkpPriceLevel.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;dtOpenBalance.Name" xml:space="preserve">
    <value>dtOpenBalance</value>
  </data>
  <data name="lkp_Category.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="Governate.Text" xml:space="preserve">
    <value>Governate</value>
  </data>
  <data name="labelControl3.TabIndex" type="System.Int32, mscorlib">
    <value>67</value>
  </data>
  <data name="lkp_CollectEmp.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtRepFName.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_Category.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Regions.Properties.Columns5" xml:space="preserve">
    <value>Id</value>
  </data>
  <data name="cmbIsCredit.Location" type="System.Drawing.Point, System.Drawing">
    <value>259, 8</value>
  </data>
  <data name="cmbCsType.Properties.Items6" xml:space="preserve">
    <value>Foreigner</value>
  </data>
  <data name="txtEmail.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="dtOpenBalance.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl29.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 13</value>
  </data>
  <data name="labelControl7.Text" xml:space="preserve">
    <value>Max Credit</value>
  </data>
  <data name="&gt;&gt;txtCusCode.ZOrder" xml:space="preserve">
    <value>44</value>
  </data>
  <data name="labelControl22.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_RepresentativeJob.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="txt_Neighborhood.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl22.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Name" xml:space="preserve">
    <value>barDockControlLeft</value>
  </data>
  <data name="&gt;&gt;txt_Representative.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMaxCredit.Properties.NullText" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="barBtnList.Caption" xml:space="preserve">
    <value>List</value>
  </data>
  <data name="labelControl48.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Name" xml:space="preserve">
    <value>barDockControlBottom</value>
  </data>
  <data name="txt_Governate.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtAddress.Location" type="System.Drawing.Point, System.Drawing">
    <value>262, 296</value>
  </data>
  <data name="txt_Neighborhood.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Representative.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;dtOpenBalance.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.DateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtTaxCardNumber.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="lstPhotos.Size" type="System.Drawing.Size, System.Drawing">
    <value>190, 160</value>
  </data>
  <data name="txt_Rep_ID.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="Governate.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl6.Text" xml:space="preserve">
    <value>Price Level</value>
  </data>
  <data name="labelControl23.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="&gt;&gt;labelControl34.ZOrder" xml:space="preserve">
    <value>30</value>
  </data>
  <data name="btnDeleteEmpPhoto.Size" type="System.Drawing.Size, System.Drawing">
    <value>25, 23</value>
  </data>
  <data name="txt_BuildingNumber.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_BuildingNumber.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl17.ZOrder" xml:space="preserve">
    <value>65</value>
  </data>
  <data name="txtCusCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>262, 6</value>
  </data>
  <data name="txtMobile.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="barBtnNew.Caption" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="txt_IdNumber.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_Regions.Properties.Columns8" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl42.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_IdNumber.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl4.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="txt_Representative.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="lkp_CollectEmp.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;txt_Representative.ZOrder" xml:space="preserve">
    <value>63</value>
  </data>
  <data name="lblOpenAmount.Text" xml:space="preserve">
    <value>Amount</value>
  </data>
  <data name="&gt;&gt;barBtnNew.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtTradeRegistry.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblOpenBalance.Text" xml:space="preserve">
    <value>Open Balance</value>
  </data>
  <data name="cmbCsType.Properties.Items3" xml:space="preserve">
    <value>Company</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="txtCity.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="labelControl12.Text" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="labelControl42.Location" type="System.Drawing.Point, System.Drawing">
    <value>48, 9</value>
  </data>
  <data name="txtShipping.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;col_Sell.Name" xml:space="preserve">
    <value>col_Sell</value>
  </data>
  <data name="&gt;&gt;labelControl15.Name" xml:space="preserve">
    <value>labelControl15</value>
  </data>
  <data name="labelControl34.Location" type="System.Drawing.Point, System.Drawing">
    <value>419, 472</value>
  </data>
  <data name="txt_RepFJob.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl8.Name" xml:space="preserve">
    <value>labelControl8</value>
  </data>
  <data name="txtOpenAmount.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl13.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_DueDays.Properties.Buttons1" xml:space="preserve">
    <value />
  </data>
  <data name="txt_DueDays.Properties.Buttons9" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtCusNameAr.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;gridBand1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.GridBand, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl26.Location" type="System.Drawing.Point, System.Drawing">
    <value>417, 186</value>
  </data>
  <data name="lkp_CollectEmp.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="txtMobile.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_country.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_Regions.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Category.Properties.Columns1" xml:space="preserve">
    <value>فئة العميل</value>
  </data>
  <data name="&gt;&gt;tab_main.Name" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;barBtnNew.Name" xml:space="preserve">
    <value>barBtnNew</value>
  </data>
  <data name="lkp_Group.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="txtShipping.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 22</value>
  </data>
  <data name="chk_IsActive.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtCity.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_Category.Properties.Columns8" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="labelControl49.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Red</value>
  </data>
  <data name="txt_Bank.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_CollectEmp.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_Representative.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblTaxDepartment.TabIndex" type="System.Int32, mscorlib">
    <value>178</value>
  </data>
  <data name="&gt;&gt;col_Notes.Name" xml:space="preserve">
    <value>col_Notes</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtOpenAmount.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl49.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lkp_Group.Properties.Columns8" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;labelControl11.Name" xml:space="preserve">
    <value>labelControl11</value>
  </data>
  <data name="txtMaxCredit.Location" type="System.Drawing.Point, System.Drawing">
    <value>65, 575</value>
  </data>
  <data name="txt_RepFJob.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Group.Properties.AppearanceDropDown.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtImagePath.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="barDockControlLeft.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="lkpDelivery.Properties.AppearanceDropDownHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtZip.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_IsActive.EditValue" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lstPhotos.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="chk_IsActive.Size" type="System.Drawing.Size, System.Drawing">
    <value>101, 19</value>
  </data>
  <data name="txt_Bank.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns27" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;uc_LinkAccount1.Name" xml:space="preserve">
    <value>uc_LinkAccount1</value>
  </data>
  <data name="btnAddEmpPhoho.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="lkp_Regions.Properties.Columns7" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="dtOpenBalance.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="col_Telephone.Caption" xml:space="preserve">
    <value>Telephone</value>
  </data>
  <data name="txt_BuildingNumber.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtCusNameAr.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;tab_main.Parent" xml:space="preserve">
    <value>xtraTabControl1</value>
  </data>
  <data name="btnBrowse.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpPriceLevel.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 20</value>
  </data>
  <data name="txt_RepresentativeJob.Location" type="System.Drawing.Point, System.Drawing">
    <value>263, 437</value>
  </data>
  <data name="labelControl43.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barDockControlRight.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Representative.Size" type="System.Drawing.Size, System.Drawing">
    <value>149, 20</value>
  </data>
  <data name="&gt;&gt;btnShowPhotoes.Name" xml:space="preserve">
    <value>btnShowPhotoes</value>
  </data>
  <data name="&gt;&gt;xtraTabControl1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="txtOpenAmount.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtCusNameAr.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="labelControl43.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtDiscRatio.Location" type="System.Drawing.Point, System.Drawing">
    <value>67, 547</value>
  </data>
  <data name="groupControl1.AppearanceCaption.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_Street.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cmbIsCredit.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="btnDeleteEmpPhoto.Location" type="System.Drawing.Point, System.Drawing">
    <value>202, 102</value>
  </data>
  <data name="txtMaxCredit.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtCusNameEn.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_DueDays.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl42.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="btn_AddRegion.Text" xml:space="preserve">
    <value>+</value>
  </data>
  <data name="lblOpenDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>23, 13</value>
  </data>
  <data name="&gt;&gt;labelControl5.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Rep_Phone.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtImageDesc.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtTaxFileNumber.Size" type="System.Drawing.Size, System.Drawing">
    <value>150, 20</value>
  </data>
  <data name="barBtnNew.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="&gt;&gt;labelControl41.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_Regions.Properties.Columns" xml:space="preserve">
    <value>RegionNameF</value>
  </data>
  <data name="txtTradeRegistry.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl48.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;txt_BuildingNumber.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="lkp_Category.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Governate.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="labelControl16.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtTaxDepartment.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 20</value>
  </data>
  <data name="btnShowPhotoes.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_Regions.Properties.Columns2" xml:space="preserve">
    <value>RegionName</value>
  </data>
  <data name="&gt;&gt;labelControl33.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="labelControl26.TabIndex" type="System.Int32, mscorlib">
    <value>210</value>
  </data>
  <data name="barDockControlTop.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="txt_BankAccNum.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Category.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl42.Text" xml:space="preserve">
    <value>*</value>
  </data>
  <data name="labelControl10.Location" type="System.Drawing.Point, System.Drawing">
    <value>221, 136</value>
  </data>
  <data name="&gt;&gt;txt_BankAccNum.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;labelControl18.ZOrder" xml:space="preserve">
    <value>61</value>
  </data>
  <data name="txt_Neighborhood.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_CollectEmp.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_Category.Properties.Columns5" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_country.Location" type="System.Drawing.Point, System.Drawing">
    <value>295, 210</value>
  </data>
  <data name="labelControl11.TabIndex" type="System.Int32, mscorlib">
    <value>168</value>
  </data>
  <data name="dtOpenBalance.Properties.CalendarTimeProperties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_BankAccNum.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl32.Text" xml:space="preserve">
    <value>Group</value>
  </data>
  <data name="lkp_country.Properties.AppearanceDropDown.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_Notes.Caption" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="btnEditPhoto.Appearance.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8pt, style=Bold</value>
  </data>
  <data name="labelControl47.TabIndex" type="System.Int32, mscorlib">
    <value>247</value>
  </data>
  <data name="txt_IdNumber.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtFax.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="txtTaxDepartment.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl46.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_Representative.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="groupControl2.Text" xml:space="preserve">
    <value>Papers Pictures</value>
  </data>
  <data name="txtManagerName.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="gridVisits.EmbeddedNavigator.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="labelControl42.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Red</value>
  </data>
  <data name="txt_DueDays.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_DueDays.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cmbIsCredit.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="txt_Bank.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="rep_Check.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtFax.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_Neighborhood.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="labelControl22.Text" xml:space="preserve">
    <value>Path</value>
  </data>
  <data name="labelControl12.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl44.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_Street.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Rep_ID.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl50.Location" type="System.Drawing.Point, System.Drawing">
    <value>282, 190</value>
  </data>
  <data name="&gt;&gt;txtTradeRegistry.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="labelControl27.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl10.Text" xml:space="preserve">
    <value>Zip</value>
  </data>
  <data name="&gt;&gt;labelControl39.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="lblOpenAmount.TabIndex" type="System.Int32, mscorlib">
    <value>206</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Representative.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cmbCsType.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="txtFax.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_BankAccNum.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="chk_IsBlocked.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="lkpPriceLevel.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl22.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns15" xml:space="preserve">
    <value>EmpId</value>
  </data>
  <data name="lkpDelivery.Properties.AppearanceDropDown.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_Regions.Properties.Columns9" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtOpenBalance.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Street.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="chk_IsTaxable.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;lblOpenBalance.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtDiscRatio.Name" xml:space="preserve">
    <value>txtDiscRatio</value>
  </data>
  <data name="btnBrowse.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="txtCusCode.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Category.Properties.AppearanceDropDown.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_DueDays.Properties.Buttons8" xml:space="preserve">
    <value />
  </data>
  <data name="groupControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 9</value>
  </data>
  <data name="barDockControlTop.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="lkp_CollectEmp.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl19.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="lkp_country.Properties.AppearanceDropDown.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="btnNext.Location" type="System.Drawing.Point, System.Drawing">
    <value>61, 30</value>
  </data>
  <data name="&gt;&gt;btnBrowse.Parent" xml:space="preserve">
    <value>groupControl2</value>
  </data>
  <data name="&gt;&gt;labelControl16.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtTaxCardNumber.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl22.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl43.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl42.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;txt_Rep_ID.ZOrder" xml:space="preserve">
    <value>27</value>
  </data>
  <data name="&gt;&gt;labelControl19.ZOrder" xml:space="preserve">
    <value>82</value>
  </data>
  <data name="txt_BuildingNumber.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl47.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkpDelivery.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;txtDiscRatio.ZOrder" xml:space="preserve">
    <value>81</value>
  </data>
  <data name="lkpPriceLevel.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMaxCredit.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;labelControl24.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="col_PM.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl2.ZOrder" xml:space="preserve">
    <value>52</value>
  </data>
  <data name="&gt;&gt;col_PM.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtManagerName.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="labelControl29.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtTaxFileNumber.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;labelControl40.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl29.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Street.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_CollectEmp.Properties.Columns7" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="cmbIsCredit.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl8.TabIndex" type="System.Int32, mscorlib">
    <value>109</value>
  </data>
  <data name="labelControl33.TabIndex" type="System.Int32, mscorlib">
    <value>219</value>
  </data>
  <data name="lkp_Regions.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_country.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txtShipping.ZOrder" xml:space="preserve">
    <value>46</value>
  </data>
  <data name="chk_IsTaxable.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl46.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpDelivery.Properties.AppearanceDropDownHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtMobile.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="chk_IsActive.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;cmbCsType.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_Group.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="colDayName.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="dtOpenBalance.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="btnEditPhoto.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_DueDays.Location" type="System.Drawing.Point, System.Drawing">
    <value>322, 575</value>
  </data>
  <data name="&gt;&gt;labelControl17.Name" xml:space="preserve">
    <value>labelControl17</value>
  </data>
  <data name="txtShipping.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="labelControl50.Text" xml:space="preserve">
    <value>*</value>
  </data>
  <data name="labelControl45.Location" type="System.Drawing.Point, System.Drawing">
    <value>283, 217</value>
  </data>
  <data name="&gt;&gt;txt_IdNumber.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;labelControl33.ZOrder" xml:space="preserve">
    <value>28</value>
  </data>
  <data name="&gt;&gt;bandedGridView1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtCusCode.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;BuildingNumber.Name" xml:space="preserve">
    <value>BuildingNumber</value>
  </data>
  <data name="lkp_Group.Properties.AppearanceDropDown.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_Group.Properties.AppearanceDropDownHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lstPhotos.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 28</value>
  </data>
  <data name="&gt;&gt;txtManagerName.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="txt_DueDays.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;txtShipping.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.MemoEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl28.ZOrder" xml:space="preserve">
    <value>34</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtCusCode.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_DueDays.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl40.Text" xml:space="preserve">
    <value>Neighborhood</value>
  </data>
  <data name="chk_IsBlocked.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl50.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="cmbCsType.Properties.Items" xml:space="preserve">
    <value>Individual</value>
  </data>
  <data name="&gt;&gt;labelControl15.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="labelControl14.Size" type="System.Drawing.Size, System.Drawing">
    <value>66, 13</value>
  </data>
  <data name="barDockControlRight.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lblOpenBalance.TabIndex" type="System.Int32, mscorlib">
    <value>208</value>
  </data>
  <data name="txtCusCode.Properties.Mask.EditMask" xml:space="preserve">
    <value>n0</value>
  </data>
  <data name="txtImagePath.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl13.Name" xml:space="preserve">
    <value>labelControl13</value>
  </data>
  <data name="labelControl30.Text" xml:space="preserve">
    <value>CR No.</value>
  </data>
  <data name="groupControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>512, 207</value>
  </data>
  <data name="txtDiscRatio.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Governate.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="bandedGridView1.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtTaxCardNumber.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkpDelivery.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_SalesEmp.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl48.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;labelControl13.ZOrder" xml:space="preserve">
    <value>71</value>
  </data>
  <data name="labelControl25.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;labelControl6.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="lkp_Regions.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtImageDesc.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;tab_main.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl3.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl20.Text" xml:space="preserve">
    <value>F Name</value>
  </data>
  <data name="lstPhotos.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lkpPriceLevel.Name" xml:space="preserve">
    <value>lkpPriceLevel</value>
  </data>
  <data name="&gt;&gt;chk_IsBlocked.Parent" xml:space="preserve">
    <value>tab_docs</value>
  </data>
  <data name="txt_Street.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;labelControl25.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl42.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl18.Text" xml:space="preserve">
    <value>Rep Job</value>
  </data>
  <data name="txtEmail.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="cmbIsCredit.Properties.AppearanceDropDown.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtTradeRegistry.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;col_Field.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_DueDays.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Ellipsis</value>
  </data>
  <data name="txtCity.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl6.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="btnPrev.Text" xml:space="preserve">
    <value>&lt;=</value>
  </data>
  <data name="labelControl22.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="btnDeleteEmpPhoto.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="lkpDelivery.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="barDockControlLeft.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl6.Name" xml:space="preserve">
    <value>labelControl6</value>
  </data>
  <data name="xtraTabControl1.HeaderLocation" type="DevExpress.XtraTab.TabHeaderLocation, DevExpress.XtraEditors.v15.1">
    <value>Left</value>
  </data>
  <data name="&gt;&gt;labelControl43.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl2.Name" xml:space="preserve">
    <value>labelControl2</value>
  </data>
  <data name="&gt;&gt;txtImageDesc.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="labelControl48.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Red</value>
  </data>
  <data name="&gt;&gt;txtDiscRatio.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl21.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnShowPhotoes.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="lkp_CollectEmp.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkp_Group.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="txt_RepFJob.Location" type="System.Drawing.Point, System.Drawing">
    <value>68, 437</value>
  </data>
  <data name="txtTaxCardNumber.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl39.TabIndex" type="System.Int32, mscorlib">
    <value>233</value>
  </data>
  <data name="&gt;&gt;labelControl32.ZOrder" xml:space="preserve">
    <value>31</value>
  </data>
  <data name="&gt;&gt;txtCusCode.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="btnPrev.Size" type="System.Drawing.Size, System.Drawing">
    <value>22, 19</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_country.Properties.AppearanceDropDownHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlTop.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtTaxDepartment.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl38.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_Rep_ID.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txtMaxCredit.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="btnCustomerItems.Size" type="System.Drawing.Size, System.Drawing">
    <value>511, 23</value>
  </data>
  <data name="txt_Neighborhood.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txtManagerName.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_DueDays.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlLeft.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl19.Location" type="System.Drawing.Point, System.Drawing">
    <value>51, 550</value>
  </data>
  <data name="txtRepFName.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="gridBand3.Caption" xml:space="preserve">
    <value>Visit Target</value>
  </data>
  <data name="labelControl44.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="txtOpenAmount.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 7</value>
  </data>
  <data name="txt_Rep_ID.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;labelControl37.ZOrder" xml:space="preserve">
    <value>23</value>
  </data>
  <data name="labelControl9.Location" type="System.Drawing.Point, System.Drawing">
    <value>221, 190</value>
  </data>
  <data name="Governate.Size" type="System.Drawing.Size, System.Drawing">
    <value>51, 13</value>
  </data>
  <data name="&gt;&gt;lkpPriceLevel.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_CollectEmp.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_Governate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_SalesEmp.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;chk_IsTaxable.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_RepresentativeJob.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtTaxDepartment.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="chk_IsBlocked.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="col_Sell.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_Group.Properties.AppearanceDropDown.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_DueDays.Properties.Buttons4" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtManagerName.Location" type="System.Drawing.Point, System.Drawing">
    <value>67, 386</value>
  </data>
  <data name="lkp_country.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="txtCity.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="btnAddEmpPhoho.Text" xml:space="preserve">
    <value>+</value>
  </data>
  <data name="chk_IsBlocked.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtOpenAmount.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtMaxCredit.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtMobile.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_country.Properties.AppearanceDropDown.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Regions.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl29.Name" xml:space="preserve">
    <value>labelControl29</value>
  </data>
  <data name="&gt;&gt;cmbIsCredit.Name" xml:space="preserve">
    <value>cmbIsCredit</value>
  </data>
  <data name="labelControl34.Size" type="System.Drawing.Size, System.Drawing">
    <value>50, 13</value>
  </data>
  <data name="txtCusCode.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl23.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="chk_IsTaxable.Size" type="System.Drawing.Size, System.Drawing">
    <value>200, 19</value>
  </data>
  <data name="labelControl11.Size" type="System.Drawing.Size, System.Drawing">
    <value>18, 13</value>
  </data>
  <data name="chk_IsBlocked.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="&gt;&gt;barBtnList.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_IdNumber.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="lkp_CollectEmp.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_Regions.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_RepFJob.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="&gt;&gt;lblOpenBalance.Name" xml:space="preserve">
    <value>lblOpenBalance</value>
  </data>
  <data name="lkp_Regions.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;col_Collect.Name" xml:space="preserve">
    <value>col_Collect</value>
  </data>
  <data name="&gt;&gt;labelControl18.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="lkp_Group.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;labelControl20.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="btnShowPhotoes.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="chk_IsActive.TabIndex" type="System.Int32, mscorlib">
    <value>78</value>
  </data>
  <data name="&gt;&gt;labelControl46.Name" xml:space="preserve">
    <value>labelControl46</value>
  </data>
  <data name="&gt;&gt;txt_Rep_Phone.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="txtTaxFileNumber.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 34</value>
  </data>
  <data name="labelControl24.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 13</value>
  </data>
  <data name="groupControl1.AppearanceCaption.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl48.Text" xml:space="preserve">
    <value>*</value>
  </data>
  <data name="&gt;&gt;lkp_SalesEmp.Name" xml:space="preserve">
    <value>lkp_SalesEmp</value>
  </data>
  <data name="&gt;&gt;labelControl12.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;txtCusNameEn.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>frm_SL_Customer</value>
  </data>
  <data name="pnlOpenBlnce.Size" type="System.Drawing.Size, System.Drawing">
    <value>348, 33</value>
  </data>
  <data name="&gt;&gt;labelControl24.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtImageDesc.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtEmail.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="lkp_Regions.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;rep_Check.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_BuildingNumber.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_CollectEmp.Properties.Columns8" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="&gt;&gt;labelControl9.ZOrder" xml:space="preserve">
    <value>85</value>
  </data>
  <data name="txt_Neighborhood.TabIndex" type="System.Int32, mscorlib">
    <value>234</value>
  </data>
  <data name="btnDeleteEmpPhoto.TabIndex" type="System.Int32, mscorlib">
    <value>29</value>
  </data>
  <data name="&gt;&gt;chk_IsBlocked.Name" xml:space="preserve">
    <value>chk_IsBlocked</value>
  </data>
  <data name="barBtnClose.Caption" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="cmbIsCredit.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;txtMobile.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridVisits.Size" type="System.Drawing.Size, System.Drawing">
    <value>525, 236</value>
  </data>
  <data name="&gt;&gt;labelControl13.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl19.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl37.Location" type="System.Drawing.Point, System.Drawing">
    <value>218, 9</value>
  </data>
  <data name="labelControl23.Location" type="System.Drawing.Point, System.Drawing">
    <value>464, 58</value>
  </data>
  <data name="txtZip.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_SalesEmp.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Group.Properties.AppearanceDropDown.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl50.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cmbCsType.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl42.Name" xml:space="preserve">
    <value>labelControl42</value>
  </data>
  <data name="btnCustomerItems.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtCusCode.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;BuildingNumber.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="&gt;&gt;labelControl3.ZOrder" xml:space="preserve">
    <value>64</value>
  </data>
  <data name="labelControl28.Text" xml:space="preserve">
    <value>Bank</value>
  </data>
  <data name="dtOpenBalance.Properties.CalendarTimeProperties.Mask.EditMask" xml:space="preserve">
    <value>T</value>
  </data>
  <data name="labelControl44.TabIndex" type="System.Int32, mscorlib">
    <value>244</value>
  </data>
  <data name="&gt;&gt;barBtnDelete.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtCusNameAr.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="BuildingNumber.Text" xml:space="preserve">
    <value>BuildingNum</value>
  </data>
  <data name="lkp_SalesEmp.Location" type="System.Drawing.Point, System.Drawing">
    <value>66, 495</value>
  </data>
  <data name="txtRepFName.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtTaxCardNumber.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="btnCustomerItems.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="dtOpenBalance.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="gridVisits.EmbeddedNavigator.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>Tile</value>
  </data>
  <data name="cmbCsType.Properties.Items8" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="&gt;&gt;txtMaxCredit.Name" xml:space="preserve">
    <value>txtMaxCredit</value>
  </data>
  <data name="txtImagePath.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;lkp_Group.Name" xml:space="preserve">
    <value>lkp_Group</value>
  </data>
  <data name="&gt;&gt;txt_IdNumber.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnHelp.Name" xml:space="preserve">
    <value>barBtnHelp</value>
  </data>
  <data name="labelControl41.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 13</value>
  </data>
  <data name="btn_AddRegion.Size" type="System.Drawing.Size, System.Drawing">
    <value>25, 23</value>
  </data>
  <data name="txtTaxFileNumber.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_Rep_Phone.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 20</value>
  </data>
  <data name="txt_DueDays.Properties.Buttons2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="&gt;&gt;pnlOpenBlnce.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.PanelControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barDockControlRight.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpDelivery.TabIndex" type="System.Int32, mscorlib">
    <value>221</value>
  </data>
  <data name="&gt;&gt;txt_Street.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;tab_docs.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="labelControl50.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Red</value>
  </data>
  <data name="txt_DueDays.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtCusNameEn.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtEmail.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_BankAccNum.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="barDockControlLeft.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_DueDays.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txt_Governate.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="&gt;&gt;btnNext.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txtCusCode.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_Group.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl31.Size" type="System.Drawing.Size, System.Drawing">
    <value>34, 13</value>
  </data>
  <data name="txtRepFName.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txtManagerName.ZOrder" xml:space="preserve">
    <value>67</value>
  </data>
  <data name="lblOpenDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>230, 9</value>
  </data>
  <data name="dtOpenBalance.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl25.TabIndex" type="System.Int32, mscorlib">
    <value>192</value>
  </data>
  <data name="barDockControlBottom.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 776</value>
  </data>
  <data name="labelControl10.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtCusNameAr.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;groupControl2.Name" xml:space="preserve">
    <value>groupControl2</value>
  </data>
  <data name="&gt;&gt;col_Sell.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtTaxDepartment.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtImagePath.TabIndex" type="System.Int32, mscorlib">
    <value>51</value>
  </data>
  <data name="txtManagerName.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Rep_Phone.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="btnEditPhoto.Location" type="System.Drawing.Point, System.Drawing">
    <value>202, 65</value>
  </data>
  <data name="labelControl47.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_Neighborhood.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="txt_Rep_Phone.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="cmbCsType.TabIndex" type="System.Int32, mscorlib">
    <value>223</value>
  </data>
  <data name="txtCity.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;lkp_Group.ZOrder" xml:space="preserve">
    <value>32</value>
  </data>
  <data name="txt_IdNumber.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="cmbCsType.Properties.NullValuePrompt" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpPriceLevel.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;gridBand3.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.GridBand, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl1.Name" xml:space="preserve">
    <value>labelControl1</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns26" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_Regions.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 20</value>
  </data>
  <data name="labelControl45.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl19.Size" type="System.Drawing.Size, System.Drawing">
    <value>11, 13</value>
  </data>
  <data name="&gt;&gt;labelControl32.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;labelControl27.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Neighborhood.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtEmail.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_Category.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkpDelivery.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl21.Size" type="System.Drawing.Size, System.Drawing">
    <value>26, 13</value>
  </data>
  <data name="dtOpenBalance.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="lblOpenAmount.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_Representative.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl20.TabIndex" type="System.Int32, mscorlib">
    <value>198</value>
  </data>
  <data name="txtCity.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="btnShowPhotoes.Size" type="System.Drawing.Size, System.Drawing">
    <value>58, 38</value>
  </data>
  <data name="txtCusCode.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_RepresentativeJob.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl18.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtRepFName.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_RepFJob.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl39.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;txt_RepFJob.ZOrder" xml:space="preserve">
    <value>55</value>
  </data>
  <data name="&gt;&gt;lkp_Category.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;btnAddEmpPhoho.Parent" xml:space="preserve">
    <value>groupControl2</value>
  </data>
  <data name="&gt;&gt;labelControl1.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="lkp_CollectEmp.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;labelControl42.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="txtMobile.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="uc_LinkAccount1.TabIndex" type="System.Int32, mscorlib">
    <value>26</value>
  </data>
  <data name="&gt;&gt;labelControl1.ZOrder" xml:space="preserve">
    <value>66</value>
  </data>
  <data name="labelControl16.TabIndex" type="System.Int32, mscorlib">
    <value>186</value>
  </data>
  <data name="txtOpenAmount.Properties.Mask.EditMask" xml:space="preserve">
    <value>n2</value>
  </data>
  <data name="txt_Representative.Location" type="System.Drawing.Point, System.Drawing">
    <value>262, 411</value>
  </data>
  <data name="txtRepFName.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtFax.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;txtTaxFileNumber.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="btnDeleteEmpPhoto.Text" xml:space="preserve">
    <value>X</value>
  </data>
  <data name="&gt;&gt;lblOpenDate.Parent" xml:space="preserve">
    <value>pnlOpenBlnce</value>
  </data>
  <data name="chk_IsTaxable.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="btnBrowse.Size" type="System.Drawing.Size, System.Drawing">
    <value>25, 23</value>
  </data>
  <data name="txtDiscRatio.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl11.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="txt_Rep_ID.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl45.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="txtCusNameEn.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="lkpPriceLevel.Properties.AppearanceDropDown.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_BankAccNum.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_Category.Properties.AppearanceDropDown.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtMobile.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;lkpDelivery.ZOrder" xml:space="preserve">
    <value>26</value>
  </data>
  <data name="txt_Bank.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="bandedGridView1.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="chk_IsActive.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="txtShipping.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtOpenAmount.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="chk_IsTaxable.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;chk_IsActive.Parent" xml:space="preserve">
    <value>tab_docs</value>
  </data>
  <data name="btnAddEmpPhoho.Location" type="System.Drawing.Point, System.Drawing">
    <value>202, 28</value>
  </data>
  <data name="&gt;&gt;txtCusNameAr.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;labelControl23.Name" xml:space="preserve">
    <value>labelControl23</value>
  </data>
  <data name="&gt;&gt;tab_docs.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Governate.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;groupControl2.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="txt_DueDays.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_SalesEmp.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="gridVisits.EmbeddedNavigator.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>0, 0, 0, 0</value>
  </data>
  <data name="txtImagePath.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl21.Location" type="System.Drawing.Point, System.Drawing">
    <value>222, 440</value>
  </data>
  <data name="labelControl32.Location" type="System.Drawing.Point, System.Drawing">
    <value>218, 35</value>
  </data>
  <data name="txt_Governate.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;labelControl19.Name" xml:space="preserve">
    <value>labelControl19</value>
  </data>
  <data name="chk_IsTaxable.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="txtTaxFileNumber.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTradeRegistry.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;lblOpenDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_BuildingNumber.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl4.TabIndex" type="System.Int32, mscorlib">
    <value>61</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Customer Info</value>
  </data>
  <data name="txtFax.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_Category.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_Regions.Properties.AppearanceDropDownHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl5.ZOrder" xml:space="preserve">
    <value>68</value>
  </data>
  <data name="gridBand5.Name" xml:space="preserve">
    <value>gridBand5</value>
  </data>
  <data name="labelControl49.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="labelControl39.Text" xml:space="preserve">
    <value>Street</value>
  </data>
  <data name="&gt;&gt;labelControl38.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="txtTradeRegistry.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtTaxDepartment.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtRepFName.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlRight.Location" type="System.Drawing.Point, System.Drawing">
    <value>558, 28</value>
  </data>
  <data name="txtTaxFileNumber.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_Category.Properties.AppearanceDropDownHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl16.Text" xml:space="preserve">
    <value>Due Days</value>
  </data>
  <data name="txt_Rep_ID.TabIndex" type="System.Int32, mscorlib">
    <value>218</value>
  </data>
  <data name="labelControl50.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="txt_DueDays.Properties.Buttons5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl14.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl18.Location" type="System.Drawing.Point, System.Drawing">
    <value>417, 440</value>
  </data>
  <data name="gridVisits.EmbeddedNavigator.ToolTipIconType" type="DevExpress.Utils.ToolTipIconType, DevExpress.Utils.v15.1">
    <value>None</value>
  </data>
  <data name="cmbCsType.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;btnCustomerItems.Parent" xml:space="preserve">
    <value>tab_docs</value>
  </data>
  <data name="dtOpenBalance.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_BuildingNumber.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="dtOpenBalance.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="txt_IdNumber.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="bandedGridView1.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Category.Properties.Columns" xml:space="preserve">
    <value>CGNameAr</value>
  </data>
  <data name="txtTradeRegistry.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_Neighborhood.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtTel.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="barDockControlTop.Size" type="System.Drawing.Size, System.Drawing">
    <value>558, 28</value>
  </data>
  <data name="txtCusCode.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtCity.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl39.Size" type="System.Drawing.Size, System.Drawing">
    <value>30, 13</value>
  </data>
  <data name="txt_BankAccNum.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="col_Notes.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtCusNameEn.Location" type="System.Drawing.Point, System.Drawing">
    <value>66, 83</value>
  </data>
  <data name="&gt;&gt;lblTaxDepartment.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="txtOpenAmount.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtTaxCardNumber.Location" type="System.Drawing.Point, System.Drawing">
    <value>275, 34</value>
  </data>
  <data name="txtManagerName.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="chk_IsTaxable.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl7.TabIndex" type="System.Int32, mscorlib">
    <value>97</value>
  </data>
  <data name="txtRepFName.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_CollectEmp.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_BuildingNumber.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;lstPhotos.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="txtImageDesc.Location" type="System.Drawing.Point, System.Drawing">
    <value>288, 54</value>
  </data>
  <data name="&gt;&gt;labelControl44.Name" xml:space="preserve">
    <value>labelControl44</value>
  </data>
  <data name="btnShowPhotoes.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="cmbIsCredit.EditValue" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl48.TabIndex" type="System.Int32, mscorlib">
    <value>248</value>
  </data>
  <data name="gridBand4.Caption" xml:space="preserve">
    <value>Visit Time</value>
  </data>
  <data name="col_Field.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl46.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;barBtnClose.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;chk_IsTaxable.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="cmbIsCredit.Size" type="System.Drawing.Size, System.Drawing">
    <value>85, 20</value>
  </data>
  <data name="labelControl43.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;gridBand4.Name" xml:space="preserve">
    <value>gridBand4</value>
  </data>
  <data name="cmbIsCredit.Properties.AppearanceDropDown.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpPriceLevel.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTaxCardNumber.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;BuildingNumber.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="labelControl47.Text" xml:space="preserve">
    <value>*</value>
  </data>
  <data name="txt_DueDays.Properties.Buttons6" type="DevExpress.XtraEditors.ImageLocation, DevExpress.XtraEditors.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lkp_country.Properties.AppearanceDropDown.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtImageDesc.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;lblOpenAmount.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_Regions.Properties.AppearanceDropDownHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl48.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnShowPhotoes.Text" xml:space="preserve">
    <value>Show
Pictures</value>
  </data>
  <data name="txtImagePath.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl6.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_CollectEmp.Location" type="System.Drawing.Point, System.Drawing">
    <value>67, 521</value>
  </data>
  <data name="labelControl17.Text" xml:space="preserve">
    <value>Representative Name</value>
  </data>
  <data name="&gt;&gt;btnDeleteEmpPhoto.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtTradeRegistry.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl29.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="dtOpenBalance.Properties.CalendarTimeProperties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_DueDays.Size" type="System.Drawing.Size, System.Drawing">
    <value>86, 20</value>
  </data>
  <data name="&gt;&gt;labelControl24.Name" xml:space="preserve">
    <value>labelControl24</value>
  </data>
  <data name="txtTaxFileNumber.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_SalesEmp.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl40.Name" xml:space="preserve">
    <value>labelControl40</value>
  </data>
  <data name="txtTaxFileNumber.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txtZip.ZOrder" xml:space="preserve">
    <value>76</value>
  </data>
  <data name="txt_Bank.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="Governate.TabIndex" type="System.Int32, mscorlib">
    <value>239</value>
  </data>
  <data name="txt_IdNumber.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;gridBand2.Name" xml:space="preserve">
    <value>gridBand2</value>
  </data>
  <data name="&gt;&gt;labelControl12.ZOrder" xml:space="preserve">
    <value>75</value>
  </data>
  <data name="lblOpenDate.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl19.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnDeleteEmpPhoto.Appearance.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8pt, style=Bold</value>
  </data>
  <data name="lkp_Group.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txt_Rep_Phone.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="btnEditPhoto.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="txtTel.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtImageDesc.Size" type="System.Drawing.Size, System.Drawing">
    <value>170, 20</value>
  </data>
  <data name="&gt;&gt;col_Telephone.Name" xml:space="preserve">
    <value>col_Telephone</value>
  </data>
  <data name="txtDiscRatio.Properties.NullText" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;dtOpenBalance.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="txt_BuildingNumber.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpDelivery.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl3.Location" type="System.Drawing.Point, System.Drawing">
    <value>417, 86</value>
  </data>
  <data name="lkp_Category.Properties.Columns2" xml:space="preserve">
    <value>CustomerGroupId</value>
  </data>
  <data name="lkp_country.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="txtTaxDepartment.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Regions.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="&gt;&gt;labelControl20.Name" xml:space="preserve">
    <value>labelControl20</value>
  </data>
  <data name="&gt;&gt;bar2.Name" xml:space="preserve">
    <value>bar2</value>
  </data>
  <data name="txtZip.Location" type="System.Drawing.Point, System.Drawing">
    <value>66, 133</value>
  </data>
  <data name="labelControl22.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;uc_LinkAccount1.ZOrder" xml:space="preserve">
    <value>43</value>
  </data>
  <data name="lblOpenDate.TabIndex" type="System.Int32, mscorlib">
    <value>205</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="groupControl2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;labelControl37.Name" xml:space="preserve">
    <value>labelControl37</value>
  </data>
  <data name="txtEmail.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridBand5.Width" type="System.Int32, mscorlib">
    <value>75</value>
  </data>
  <data name="txtMobile.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDiscRatio.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtCusNameEn.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_Category.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl17.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barBtnHelp.Caption" xml:space="preserve">
    <value>Help</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Name" xml:space="preserve">
    <value>barDockControlRight</value>
  </data>
  <data name="col_Collect.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;btnPrev.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="txtImagePath.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl19.TabIndex" type="System.Int32, mscorlib">
    <value>140</value>
  </data>
  <data name="labelControl49.Text" xml:space="preserve">
    <value>*</value>
  </data>
  <data name="gridBand4.Name" xml:space="preserve">
    <value>gridBand4</value>
  </data>
  <data name="lstPhotos.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="lkpPriceLevel.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="txtZip.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txtEmail.Name" xml:space="preserve">
    <value>txtEmail</value>
  </data>
  <data name="labelControl11.Text" xml:space="preserve">
    <value>Fax</value>
  </data>
  <data name="&gt;&gt;labelControl23.Parent" xml:space="preserve">
    <value>groupControl2</value>
  </data>
  <data name="labelControl47.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="groupControl1.AppearanceCaption.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_Group.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtFax.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="btnEditPhoto.Text" xml:space="preserve">
    <value>X</value>
  </data>
  <data name="labelControl24.Location" type="System.Drawing.Point, System.Drawing">
    <value>417, 36</value>
  </data>
  <data name="&gt;&gt;txtCity.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Governate.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="lkp_Category.Properties.AppearanceDropDownHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtMobile.Location" type="System.Drawing.Point, System.Drawing">
    <value>262, 108</value>
  </data>
  <data name="&gt;&gt;labelControl33.Name" xml:space="preserve">
    <value>labelControl33</value>
  </data>
  <data name="tab_docs.Text" xml:space="preserve">
    <value>Page 2</value>
  </data>
  <data name="txtCusNameEn.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl6.TabIndex" type="System.Int32, mscorlib">
    <value>146</value>
  </data>
  <data name="txtMobile.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_SalesEmp.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="groupControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 131</value>
  </data>
  <data name="txtTaxCardNumber.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtShipping.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl41.TabIndex" type="System.Int32, mscorlib">
    <value>241</value>
  </data>
  <data name="&gt;&gt;labelControl36.ZOrder" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="cmbCsType.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;btn_AddRegion.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl15.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_BankAccNum.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Category.Properties.AppearanceDropDownHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_Street.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Bank.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txtCusNameEn.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpDelivery.Properties.Columns" xml:space="preserve">
    <value>idDelivery</value>
  </data>
  <data name="groupControl1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtManagerName.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="gridBand2.Name" xml:space="preserve">
    <value>gridBand2</value>
  </data>
  <data name="&gt;&gt;cmbIsCredit.Parent" xml:space="preserve">
    <value>pnlOpenBlnce</value>
  </data>
  <data name="colId.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;Governate.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="lstPhotos.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtCusNameEn.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;txtTaxDepartment.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;txtMaxCredit.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl3.Name" xml:space="preserve">
    <value>labelControl3</value>
  </data>
  <data name="&gt;&gt;gridVisits.Name" xml:space="preserve">
    <value>gridVisits</value>
  </data>
  <data name="labelControl42.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;txt_Neighborhood.Name" xml:space="preserve">
    <value>txt_Neighborhood</value>
  </data>
  <data name="&gt;&gt;txtTradeRegistry.Name" xml:space="preserve">
    <value>txtTradeRegistry</value>
  </data>
  <data name="&gt;&gt;lkp_Regions.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtEmail.Location" type="System.Drawing.Point, System.Drawing">
    <value>66, 158</value>
  </data>
  <data name="labelControl37.Text" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="&gt;&gt;labelControl34.Name" xml:space="preserve">
    <value>labelControl34</value>
  </data>
  <data name="txt_BankAccNum.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridBand3.Name" xml:space="preserve">
    <value>gridBand3</value>
  </data>
  <data name="txt_Street.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_Rep_Phone.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txt_Neighborhood.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="txt_BankAccNum.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtZip.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="pnlOpenBlnce.TabIndex" type="System.Int32, mscorlib">
    <value>25</value>
  </data>
  <data name="labelControl23.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="txtTaxCardNumber.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_RepresentativeJob.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 20</value>
  </data>
  <data name="txtMaxCredit.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;lblOpenBalance.ZOrder" xml:space="preserve">
    <value>40</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl16.ZOrder" xml:space="preserve">
    <value>53</value>
  </data>
  <data name="txtTaxDepartment.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl12.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="tab_main.Size" type="System.Drawing.Size, System.Drawing">
    <value>525, 716</value>
  </data>
  <data name="gridBand1.Width" type="System.Int32, mscorlib">
    <value>150</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Representative.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl11.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl27.Text" xml:space="preserve">
    <value>Tax Card No.</value>
  </data>
  <data name="groupControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl16.Size" type="System.Drawing.Size, System.Drawing">
    <value>46, 13</value>
  </data>
  <data name="lkp_country.TabIndex" type="System.Int32, mscorlib">
    <value>240</value>
  </data>
  <data name="txtTaxFileNumber.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtImagePath.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl32.TabIndex" type="System.Int32, mscorlib">
    <value>216</value>
  </data>
  <data name="&gt;&gt;txt_BankAccNum.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_country.Name" xml:space="preserve">
    <value>lkp_country</value>
  </data>
  <data name="&gt;&gt;groupControl1.Parent" xml:space="preserve">
    <value>tab_docs</value>
  </data>
  <data name="&gt;&gt;labelControl21.ZOrder" xml:space="preserve">
    <value>57</value>
  </data>
  <data name="&gt;&gt;labelControl3.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="txtImagePath.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Bank.Location" type="System.Drawing.Point, System.Drawing">
    <value>263, 360</value>
  </data>
  <data name="txtCusNameAr.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="txt_Governate.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 20</value>
  </data>
  <data name="chk_IsTaxable.Location" type="System.Drawing.Point, System.Drawing">
    <value>223, 84</value>
  </data>
  <data name="&gt;&gt;labelControl26.Name" xml:space="preserve">
    <value>labelControl26</value>
  </data>
  <data name="labelControl9.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_Category.Properties.AppearanceDropDown.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;lkp_SalesEmp.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkp_CollectEmp.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="labelControl23.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtRepFName.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl9.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="groupControl2.AppearanceCaption.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="chk_IsTaxable.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_country.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMaxCredit.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_Street.Location" type="System.Drawing.Point, System.Drawing">
    <value>66, 268</value>
  </data>
  <data name="lkp_Group.Properties.AppearanceDropDownHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtAddress.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;labelControl41.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;txt_RepresentativeJob.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="lkp_Regions.Properties.Columns10" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="txtCusNameAr.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtRepFName.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 20</value>
  </data>
  <data name="labelControl35.Location" type="System.Drawing.Point, System.Drawing">
    <value>219, 331</value>
  </data>
  <data name="labelControl4.Text" xml:space="preserve">
    <value>Customer Name</value>
  </data>
  <data name="labelControl8.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="cmbIsCredit.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtZip.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txtCity.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl31.Text" xml:space="preserve">
    <value>Acc.No</value>
  </data>
  <data name="txtFax.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 20</value>
  </data>
  <data name="gridBand4.Width" type="System.Int32, mscorlib">
    <value>150</value>
  </data>
  <data name="lkp_country.Properties.AppearanceDropDownHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtRepFName.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtFax.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="dtOpenBalance.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl22.Name" xml:space="preserve">
    <value>labelControl22</value>
  </data>
  <data name="cmbIsCredit.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lkpPriceLevel.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="cmbIsCredit.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl40.Location" type="System.Drawing.Point, System.Drawing">
    <value>416, 271</value>
  </data>
  <data name="txt_RepFJob.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl35.Text" xml:space="preserve">
    <value>Delivery</value>
  </data>
  <data name="txtZip.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="pnlOpenBlnce.Location" type="System.Drawing.Point, System.Drawing">
    <value>64, 601</value>
  </data>
  <data name="&gt;&gt;labelControl2.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;lkpDelivery.Name" xml:space="preserve">
    <value>lkpDelivery</value>
  </data>
  <data name="&gt;&gt;txtEmail.ZOrder" xml:space="preserve">
    <value>73</value>
  </data>
  <data name="txtImageDesc.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtTaxFileNumber.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lstPhotos.Parent" xml:space="preserve">
    <value>groupControl2</value>
  </data>
  <data name="txtDiscRatio.Size" type="System.Drawing.Size, System.Drawing">
    <value>86, 20</value>
  </data>
  <data name="lkp_country.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_IdNumber.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;labelControl35.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;col_Id.Name" xml:space="preserve">
    <value>col_Id</value>
  </data>
  <data name="&gt;&gt;labelControl16.Name" xml:space="preserve">
    <value>labelControl16</value>
  </data>
  <data name="txtCusNameEn.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="txtMobile.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_Group.Properties.AppearanceDropDownHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtManagerName.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_BankAccNum.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="barDockControlBottom.Size" type="System.Drawing.Size, System.Drawing">
    <value>558, 0</value>
  </data>
  <data name="labelControl47.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="btnPrev.ToolTip" xml:space="preserve">
    <value>Previous</value>
  </data>
  <data name="&gt;&gt;txt_RepresentativeJob.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtCusNameEn.Name" xml:space="preserve">
    <value>txtCusNameEn</value>
  </data>
  <data name="&gt;&gt;btnAddEmpPhoho.Name" xml:space="preserve">
    <value>btnAddEmpPhoho</value>
  </data>
  <data name="labelControl50.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridBand2.Width" type="System.Int32, mscorlib">
    <value>150</value>
  </data>
  <data name="&gt;&gt;labelControl7.ZOrder" xml:space="preserve">
    <value>72</value>
  </data>
  <data name="labelControl24.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;pnlOpenBlnce.ZOrder" xml:space="preserve">
    <value>39</value>
  </data>
  <data name="labelControl37.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl46.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="txt_RepresentativeJob.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl35.Name" xml:space="preserve">
    <value>labelControl35</value>
  </data>
  <data name="&gt;&gt;labelControl45.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="&gt;&gt;labelControl25.ZOrder" xml:space="preserve">
    <value>51</value>
  </data>
  <data name="txt_Representative.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl29.TabIndex" type="System.Int32, mscorlib">
    <value>177</value>
  </data>
  <data name="lkp_SalesEmp.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;labelControl15.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Bank.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl28.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;labelControl10.ZOrder" xml:space="preserve">
    <value>77</value>
  </data>
  <data name="&gt;&gt;txtZip.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;txtTaxDepartment.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="txt_DueDays.Properties.Buttons7" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="btn_AddRegion.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="&gt;&gt;labelControl21.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtShipping.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="cmbIsCredit.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txt_RepresentativeJob.Name" xml:space="preserve">
    <value>txt_RepresentativeJob</value>
  </data>
  <data name="labelControl41.Location" type="System.Drawing.Point, System.Drawing">
    <value>417, 214</value>
  </data>
  <data name="txtTaxCardNumber.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="labelControl46.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl15.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="txtTaxFileNumber.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpPriceLevel.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="labelControl36.Size" type="System.Drawing.Size, System.Drawing">
    <value>33, 13</value>
  </data>
  <data name="labelControl8.Location" type="System.Drawing.Point, System.Drawing">
    <value>161, 550</value>
  </data>
  <data name="&gt;&gt;lblTaxDepartment.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="txtZip.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtDiscRatio.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl16.Location" type="System.Drawing.Point, System.Drawing">
    <value>414, 578</value>
  </data>
  <data name="&gt;&gt;lkp_SalesEmp.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="txtCusNameAr.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtCusCode.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txtTaxCardNumber.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl31.Name" xml:space="preserve">
    <value>labelControl31</value>
  </data>
  <data name="&gt;&gt;btnPrev.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="dtOpenBalance.Properties.CalendarTimeProperties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;xtraTabControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupControl2.Parent" xml:space="preserve">
    <value>tab_docs</value>
  </data>
  <data name="lkp_Category.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="txtRepFName.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lblOpenBalance.Size" type="System.Drawing.Size, System.Drawing">
    <value>66, 13</value>
  </data>
  <data name="&gt;&gt;txt_IdNumber.Name" xml:space="preserve">
    <value>txt_IdNumber</value>
  </data>
  <data name="lkp_CollectEmp.TabIndex" type="System.Int32, mscorlib">
    <value>228</value>
  </data>
  <data name="txtMaxCredit.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl26.Size" type="System.Drawing.Size, System.Drawing">
    <value>51, 13</value>
  </data>
  <data name="txt_Street.TabIndex" type="System.Int32, mscorlib">
    <value>235</value>
  </data>
  <data name="&gt;&gt;btnBrowse.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="&gt;&gt;labelControl32.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_BankAccNum.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbIsCredit.Properties.Items7" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpDelivery.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="txtEmail.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="lkpPriceLevel.Properties.Columns21" xml:space="preserve">
    <value>PriceLevelId</value>
  </data>
  <data name="cmbIsCredit.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="labelControl10.TabIndex" type="System.Int32, mscorlib">
    <value>170</value>
  </data>
  <data name="txtTaxDepartment.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>39, 13</value>
  </data>
  <data name="chk_IsBlocked.TabIndex" type="System.Int32, mscorlib">
    <value>77</value>
  </data>
  <data name="txtImagePath.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl9.Text" xml:space="preserve">
    <value>City</value>
  </data>
  <data name="txtEmail.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtTaxCardNumber.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl27.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="txtImageDesc.TabIndex" type="System.Int32, mscorlib">
    <value>52</value>
  </data>
  <data name="&gt;&gt;txtCusCode.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="btnNext.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txt_Governate.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtEmail.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txtImagePath.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cmbCsType.Name" xml:space="preserve">
    <value>cmbCsType</value>
  </data>
  <data name="lkp_Regions.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkpPriceLevel.Properties.AppearanceDropDownHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="dtOpenBalance.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl7.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="BuildingNumber.Location" type="System.Drawing.Point, System.Drawing">
    <value>416, 241</value>
  </data>
  <data name="labelControl25.Size" type="System.Drawing.Size, System.Drawing">
    <value>74, 13</value>
  </data>
  <data name="btnShowPhotoes.TabIndex" type="System.Int32, mscorlib">
    <value>54</value>
  </data>
  <data name="&gt;&gt;col_Field.Name" xml:space="preserve">
    <value>col_Field</value>
  </data>
  <data name="txt_DueDays.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="groupControl1.AppearanceCaption.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;gridVisits.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.GridControl, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl5.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="cmbIsCredit.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Near</value>
  </data>
  <data name="txt_Bank.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtTaxDepartment.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="txtOpenAmount.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl13.Size" type="System.Drawing.Size, System.Drawing">
    <value>33, 13</value>
  </data>
  <data name="txtFax.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_Regions.TabIndex" type="System.Int32, mscorlib">
    <value>225</value>
  </data>
  <data name="&gt;&gt;btnBrowse.Name" xml:space="preserve">
    <value>btnBrowse</value>
  </data>
  <data name="labelControl49.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_RepresentativeJob.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtCusNameAr.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;lblOpenAmount.Name" xml:space="preserve">
    <value>lblOpenAmount</value>
  </data>
  <data name="lkp_CollectEmp.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="btnNext.ToolTip" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="txtOpenAmount.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_RepresentativeJob.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;barBtnClose.Name" xml:space="preserve">
    <value>barBtnClose</value>
  </data>
  <data name="lkp_Category.Properties.AppearanceDropDown.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtCusNameAr.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="btnDeleteEmpPhoto.ToolTip" xml:space="preserve">
    <value>Delete Pic</value>
  </data>
  <data name="txtTaxFileNumber.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="chk_IsTaxable.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lstPhotos.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;labelControl38.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="bar1.Text" xml:space="preserve">
    <value>Tools</value>
  </data>
  <data name="txtCusCode.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="&gt;&gt;txt_RepFJob.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="txtDiscRatio.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Rep_ID.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_RepFJob.Size" type="System.Drawing.Size, System.Drawing">
    <value>147, 20</value>
  </data>
  <data name="labelControl29.Text" xml:space="preserve">
    <value>Tax File No.</value>
  </data>
  <data name="chk_IsTaxable.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;txtRepFName.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="txt_RepresentativeJob.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;labelControl20.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtCusCode.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_Rep_Phone.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_BuildingNumber.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="labelControl5.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="gridVisits.EmbeddedNavigator.AllowHtmlTextInToolTip" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="gridVisits.EmbeddedNavigator.MaximumSize" type="System.Drawing.Size, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="txtImageDesc.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl48.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="cmbIsCredit.Properties.Items8" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="txtCity.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtTaxCardNumber.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 20</value>
  </data>
  <data name="&gt;&gt;txt_BankAccNum.Name" xml:space="preserve">
    <value>txt_BankAccNum</value>
  </data>
  <data name="cmbIsCredit.Properties.Items3" xml:space="preserve">
    <value>Credit</value>
  </data>
  <data name="lkp_Group.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="cmbIsCredit.Properties.Items1" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbIsCredit.Properties.Items6" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txtTel.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="cmbIsCredit.Properties.Items4" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cmbIsCredit.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="labelControl48.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_BuildingNumber.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="labelControl30.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtManagerName.Size" type="System.Drawing.Size, System.Drawing">
    <value>344, 20</value>
  </data>
  <data name="lkp_Regions.Properties.AppearanceDropDown.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barDockControlRight.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Right</value>
  </data>
  <data name="dtOpenBalance.EditValue" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl1.TabIndex" type="System.Int32, mscorlib">
    <value>74</value>
  </data>
  <data name="labelControl4.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="chk_IsActive.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="bandedGridView1.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl15.ZOrder" xml:space="preserve">
    <value>54</value>
  </data>
  <data name="txtTel.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txtRepFName.ZOrder" xml:space="preserve">
    <value>45</value>
  </data>
  <data name="txtCusNameEn.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl10.Name" xml:space="preserve">
    <value>labelControl10</value>
  </data>
  <data name="txt_Governate.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_Field.Caption" xml:space="preserve">
    <value>Field</value>
  </data>
  <data name="txtTel.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txtCusCode.Name" xml:space="preserve">
    <value>txtCusCode</value>
  </data>
  <data name="lkp_SalesEmp.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl36.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl12.Name" xml:space="preserve">
    <value>labelControl12</value>
  </data>
  <data name="&gt;&gt;repositoryItemTextEdit1.Name" xml:space="preserve">
    <value>repositoryItemTextEdit1</value>
  </data>
  <data name="labelControl46.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;col_AM.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpPriceLevel.Location" type="System.Drawing.Point, System.Drawing">
    <value>262, 547</value>
  </data>
  <data name="labelControl34.TabIndex" type="System.Int32, mscorlib">
    <value>220</value>
  </data>
  <data name="barBtnSave.Caption" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="&gt;&gt;col_AM.Name" xml:space="preserve">
    <value>col_AM</value>
  </data>
  <data name="labelControl43.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="lkp_CollectEmp.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnNext.TabIndex" type="System.Int32, mscorlib">
    <value>82</value>
  </data>
  <data name="txt_Neighborhood.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="barDockControlTop.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtTel.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_DueDays.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtImagePath.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>None</value>
  </data>
  <data name="labelControl23.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtTradeRegistry.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpDelivery.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="txtMaxCredit.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Governate.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpPriceLevel.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridVisits.EmbeddedNavigator.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;btnPrev.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtMobile.Name" xml:space="preserve">
    <value>txtMobile</value>
  </data>
  <data name="&gt;&gt;lkp_CollectEmp.Name" xml:space="preserve">
    <value>lkp_CollectEmp</value>
  </data>
  <data name="lkp_CollectEmp.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="labelControl33.Size" type="System.Drawing.Size, System.Drawing">
    <value>11, 13</value>
  </data>
  <data name="labelControl14.Location" type="System.Drawing.Point, System.Drawing">
    <value>418, 389</value>
  </data>
  <data name="&gt;&gt;txtMobile.ZOrder" xml:space="preserve">
    <value>70</value>
  </data>
  <data name="lkp_CollectEmp.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;txtMaxCredit.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="txtOpenAmount.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;labelControl9.Name" xml:space="preserve">
    <value>labelControl9</value>
  </data>
  <data name="txtMaxCredit.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="labelControl41.Text" xml:space="preserve">
    <value>Country</value>
  </data>
  <data name="txtCity.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 20</value>
  </data>
  <data name="txtZip.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtMaxCredit.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="txt_Neighborhood.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="uc_LinkAccount1.Location" type="System.Drawing.Point, System.Drawing">
    <value>51, 640</value>
  </data>
  <data name="txtAddress.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;btnPrev.Name" xml:space="preserve">
    <value>btnPrev</value>
  </data>
  <data name="txtImageDesc.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;labelControl11.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtAddress.Name" xml:space="preserve">
    <value>txtAddress</value>
  </data>
  <data name="labelControl23.Size" type="System.Drawing.Size, System.Drawing">
    <value>43, 13</value>
  </data>
  <data name="&gt;&gt;btnEditPhoto.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="barDockControlBottom.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;btn_AddRegion.Name" xml:space="preserve">
    <value>btn_AddRegion</value>
  </data>
  <data name="labelControl38.Size" type="System.Drawing.Size, System.Drawing">
    <value>81, 13</value>
  </data>
  <data name="txtMobile.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 20</value>
  </data>
  <data name="&gt;&gt;bar1.Name" xml:space="preserve">
    <value>bar1</value>
  </data>
  <data name="&gt;&gt;labelControl23.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Collect.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colId.Name" xml:space="preserve">
    <value>colId</value>
  </data>
  <data name="labelControl7.Location" type="System.Drawing.Point, System.Drawing">
    <value>157, 578</value>
  </data>
  <data name="&gt;&gt;cmbCsType.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="txtTaxDepartment.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl37.TabIndex" type="System.Int32, mscorlib">
    <value>224</value>
  </data>
  <data name="&gt;&gt;labelControl38.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="dtOpenBalance.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl46.Location" type="System.Drawing.Point, System.Drawing">
    <value>283, 241</value>
  </data>
  <data name="labelControl7.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;btnEditPhoto.Name" xml:space="preserve">
    <value>btnEditPhoto</value>
  </data>
  <data name="txtCity.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl50.TabIndex" type="System.Int32, mscorlib">
    <value>249</value>
  </data>
  <data name="&gt;&gt;labelControl16.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="labelControl2.TabIndex" type="System.Int32, mscorlib">
    <value>53</value>
  </data>
  <data name="txtCusNameEn.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl27.Location" type="System.Drawing.Point, System.Drawing">
    <value>428, 37</value>
  </data>
  <data name="txtTaxCardNumber.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txt_DueDays.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtTel.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="labelControl39.Location" type="System.Drawing.Point, System.Drawing">
    <value>219, 270</value>
  </data>
  <data name="dtOpenBalance.Properties.CalendarTimeProperties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_Telephone.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Bank.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_RepresentativeJob.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txtManagerName.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_Governate.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;txtShipping.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;txtEmail.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="chk_IsBlocked.Size" type="System.Drawing.Size, System.Drawing">
    <value>95, 19</value>
  </data>
  <data name="&gt;&gt;barBtnSave.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl44.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;lblOpenDate.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="txt_IdNumber.Size" type="System.Drawing.Size, System.Drawing">
    <value>116, 20</value>
  </data>
  <data name="btnNext.Text" xml:space="preserve">
    <value>=&gt;</value>
  </data>
  <data name="txt_Representative.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Rep_Phone.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_DueDays.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl45.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;groupControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GroupControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtTradeRegistry.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;labelControl50.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;labelControl31.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="lkp_country.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl18.TabIndex" type="System.Int32, mscorlib">
    <value>180</value>
  </data>
  <data name="lkp_CollectEmp.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="chk_IsBlocked.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_BankAccNum.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="chk_IsActive.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txtCusNameAr.Name" xml:space="preserve">
    <value>txtCusNameAr</value>
  </data>
  <data name="txtMaxCredit.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;labelControl36.Parent" xml:space="preserve">
    <value>tab_main</value>
  </data>
  <data name="&gt;&gt;BuildingNumber.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtRepFName.Name" xml:space="preserve">
    <value>txtRepFName</value>
  </data>
  <data name="&gt;&gt;dtOpenBalance.Parent" xml:space="preserve">
    <value>pnlOpenBlnce</value>
  </data>
  <data name="barDockControlLeft.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 748</value>
  </data>
  <data name="txt_Neighborhood.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpPriceLevel.Properties.AppearanceDropDownHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblOpenAmount.Location" type="System.Drawing.Point, System.Drawing">
    <value>95, 9</value>
  </data>
  <data name="lkpPriceLevel.Properties.AppearanceDropDown.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtEmail.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtTel.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl41.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <metadata name="$this.Language" type="System.Globalization.CultureInfo, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>ar-EG</value>
  </metadata>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>25</value>
  </metadata>
  <metadata name="barAndDockingController1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>134, 17</value>
  </metadata>
  <metadata name="barManager1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
</root>