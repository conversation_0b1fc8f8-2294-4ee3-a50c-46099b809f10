﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;
using DAL.Res;

namespace Pharmacy.Forms
{
    public partial class frm_AddUOM : DevExpress.XtraEditors.XtraForm
    {
        public frm_AddUOM()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();            

            if (Shared.IsEnglish)
                RTL.LTRLayout(this);
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            if(!(string.IsNullOrEmpty( txtUOM.Text)))
            {
                ERPDataContext pharm=new ERPDataContext();
                IC_UOM uom = new IC_UOM();
                uom.UOM = txtUOM.Text;
                pharm.IC_UOMs.InsertOnSubmit(uom);
                pharm.SubmitChanges();
                MessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgSave : ResICAr.MsgSave
                    ,"",MessageBoxButtons.OK,MessageBoxIcon.Information);
                this.Close();
            }
        }
    }
}