﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="rdo_LinkedAcc.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="rdo_LinkedAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>197, 39</value>
  </data>
  <data name="rdo_LinkedAcc.Properties.Caption" xml:space="preserve">
    <value>Linked to an existing acount</value>
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="rdo_LinkedAcc.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="rdo_LinkedAcc.Size" type="System.Drawing.Size, System.Drawing">
    <value>177, 19</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="rdo_LinkedAcc.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="&gt;&gt;rdo_LinkedAcc.Name" xml:space="preserve">
    <value>rdo_LinkedAcc</value>
  </data>
  <data name="&gt;&gt;rdo_LinkedAcc.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;rdo_LinkedAcc.Parent" xml:space="preserve">
    <value>grp_Account</value>
  </data>
  <data name="&gt;&gt;rdo_LinkedAcc.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="rdo_SeparateAcc.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="rdo_SeparateAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>162, 15</value>
  </data>
  <data name="rdo_SeparateAcc.Properties.Caption" xml:space="preserve">
    <value>Has separate Account</value>
  </data>
  <data name="rdo_SeparateAcc.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="rdo_SeparateAcc.Size" type="System.Drawing.Size, System.Drawing">
    <value>212, 19</value>
  </data>
  <data name="rdo_SeparateAcc.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="&gt;&gt;rdo_SeparateAcc.Name" xml:space="preserve">
    <value>rdo_SeparateAcc</value>
  </data>
  <data name="&gt;&gt;rdo_SeparateAcc.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckEdit, DevExpress.XtraEditors.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;rdo_SeparateAcc.Parent" xml:space="preserve">
    <value>grp_Account</value>
  </data>
  <data name="&gt;&gt;rdo_SeparateAcc.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="lkp_LinkedAcc.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkp_LinkedAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 38</value>
  </data>
  <assembly alias="DevExpress.Utils.v15.1" name="DevExpress.Utils.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="lkp_LinkedAcc.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_LinkedAcc.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="colAccId.Caption" xml:space="preserve">
    <value>AccId</value>
  </data>
  <data name="colAccNumber.Caption" xml:space="preserve">
    <value>Account Number</value>
  </data>
  <data name="colAccNumber.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colAccNumber.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="colAccName.Caption" xml:space="preserve">
    <value>Account Name</value>
  </data>
  <data name="colAccName.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colAccName.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="lkp_LinkedAcc.Size" type="System.Drawing.Size, System.Drawing">
    <value>184, 19</value>
  </data>
  <data name="lkp_LinkedAcc.TabIndex" type="System.Int32, mscorlib">
    <value>110</value>
  </data>
  <data name="&gt;&gt;lkp_LinkedAcc.Name" xml:space="preserve">
    <value>lkp_LinkedAcc</value>
  </data>
  <data name="&gt;&gt;lkp_LinkedAcc.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_LinkedAcc.Parent" xml:space="preserve">
    <value>grp_Account</value>
  </data>
  <data name="&gt;&gt;lkp_LinkedAcc.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="grp_Account.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 3</value>
  </data>
  <data name="grp_Account.Size" type="System.Drawing.Size, System.Drawing">
    <value>380, 67</value>
  </data>
  <data name="grp_Account.TabIndex" type="System.Int32, mscorlib">
    <value>158</value>
  </data>
  <data name="grp_Account.Text" xml:space="preserve">
    <value>Account Info</value>
  </data>
  <data name="&gt;&gt;grp_Account.Name" xml:space="preserve">
    <value>grp_Account</value>
  </data>
  <data name="&gt;&gt;grp_Account.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;grp_Account.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;grp_Account.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Language" type="System.Globalization.CultureInfo, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Arabic (Egypt)</value>
  </metadata>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="$this.Size" type="System.Drawing.Size, System.Drawing">
    <value>387, 73</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit1View.Name" xml:space="preserve">
    <value>gridLookUpEdit1View</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit1View.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colAccId.Name" xml:space="preserve">
    <value>colAccId</value>
  </data>
  <data name="&gt;&gt;colAccId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colAccNumber.Name" xml:space="preserve">
    <value>colAccNumber</value>
  </data>
  <data name="&gt;&gt;colAccNumber.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colAccName.Name" xml:space="preserve">
    <value>colAccName</value>
  </data>
  <data name="&gt;&gt;colAccName.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>uc_LinkAccount</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.UserControl, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>