﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{A81C7343-3A75-43C4-B9D4-A2F9A3D4C53A}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Pharmacy</RootNamespace>
    <AssemblyName>LinkIT ERP System</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <ApplicationIcon>Resources\ggIco.ico</ApplicationIcon>
    <RunPostBuildEvent>OnOutputUpdated</RunPostBuildEvent>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>3.5</OldToolsVersion>
    <TargetFrameworkProfile />
    <PublishUrl>E:\appPub\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>3</ApplicationRevision>
    <ApplicationVersion>1.9.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <PublishWizardCompleted>true</PublishWizardCompleted>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
    <UseVSHostingProcess>true</UseVSHostingProcess>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x86</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup>
    <SignManifests>false</SignManifests>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>false</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>
    </AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestCertificateThumbprint>F0F0A27C2D3E8D17E142FC0F69739247E525CE90</ManifestCertificateThumbprint>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestKeyFile>
    </ManifestKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <GenerateManifests>true</GenerateManifests>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x86\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\x86\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevExpress.BonusSkins.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Charts.v15.1.Core, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Printing.v15.1.Core, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Data.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Office.v15.1.Core, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.RichEdit.v15.1.Core, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Utils.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Xpo.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraBars.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraCharts.v15.1.Wizard, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraCharts.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraCharts.v15.1.UI, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraEditors.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraLayout.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraNavBar.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraPivotGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.PivotGrid.v15.1.Core, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraPrinting.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Sparkline.v15.1.Core, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Utils.v15.1.UI, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraReports.v15.1.Extensions, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraReports.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraRichEdit.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraRichEdit.v15.1.Extensions, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraTreeList.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraVerticalGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="ExcelDataReader">
      <HintPath>.\ExcelDataReader.dll</HintPath>
    </Reference>
    <Reference Include="ExcelDataReader.DataSet">
      <HintPath>.\ExcelDataReader.DataSet.dll</HintPath>
    </Reference>
    <Reference Include="GlobalHR, Version=16.18.39.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Packages\GlobalHR.dll</HintPath>
    </Reference>
    <Reference Include="netstandard, Version=2.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51" />
    <Reference Include="Newtonsoft.Json, Version=11.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Newtonsoft.Json.11.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="StringParser, Version=1.0.2206.35490, Culture=neutral">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>.\StringParser.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data.Linq">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.DirectoryServices" />
    <Reference Include="System.Management" />
    <Reference Include="System.Management.Instrumentation" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Runtime.Remoting" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.XML" />
    <Reference Include="TableDependency, Version=7.5.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\SqlTableDependency.7.5.0\lib\TableDependency.dll</HintPath>
    </Reference>
    <Reference Include="TableDependency.SqlClient, Version=7.5.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\SqlTableDependency.7.5.0\lib\TableDependency.SqlClient.dll</HintPath>
    </Reference>
    <Reference Include="TableDependency.SqlClient.Where, Version=7.5.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\SqlTableDependency.7.5.0\lib\TableDependency.SqlClient.Where.dll</HintPath>
    </Reference>
    <Reference Include="TextRuler, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>.\TextRuler.dll</HintPath>
    </Reference>
    <Reference Include="Tulpep.NotificationWindow, Version=1.1.38.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Tulpep.NotificationWindow.1.1.38\lib\net40\Tulpep.NotificationWindow.dll</HintPath>
    </Reference>
    <Reference Include="UIAutomationClient" />
    <Reference Include="WebResourceProvider, Version=1.0.2206.29497, Culture=neutral">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>.\WebResourceProvider.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Customers.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Customers.Designer.cs">
      <DependentUpon>Customers.cs</DependentUpon>
    </Compile>
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\E-Invoice\frm_AddNotesList.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\E-Invoice\frm_AddNotesList.Designer.cs">
      <DependentUpon>frm_AddNotesList.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\E-Invoice\frm_DebitNotesList.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\E-Invoice\frm_DebitNotesList.Designer.cs">
      <DependentUpon>frm_DebitNotesList.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\E-Invoice\frm_E_RecievedInvoices.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\E-Invoice\frm_E_RecievedInvoices.Designer.cs">
      <DependentUpon>frm_E_RecievedInvoices.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\E-Invoice\frm_E_StoreCode.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\E-Invoice\frm_E_StoreCode.Designer.cs">
      <DependentUpon>frm_E_StoreCode.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\E-Invoice\frm_E_TaxableSubtypes.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\E-Invoice\frm_E_TaxableSubtypes.Designer.cs">
      <DependentUpon>frm_E_TaxableSubtypes.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\E-Invoice\frm_E_TaxableTypes.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\E-Invoice\frm_E_TaxableTypes.Designer.cs">
      <DependentUpon>frm_E_TaxableTypes.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\E-Invoice\frm_E_InvoiceList_Rejected.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\E-Invoice\frm_E_InvoiceList_Rejected.Designer.cs">
      <DependentUpon>frm_E_InvoiceList_Rejected.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\E-Invoice\frm_E_ItemCodes.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\E-Invoice\frm_E_ItemCodes.Designer.cs">
      <DependentUpon>frm_E_ItemCodes.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\E-Invoice\frm_E_Currency.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\E-Invoice\frm_E_Currency.Designer.cs">
      <DependentUpon>frm_E_Currency.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\E-Invoice\frm_E_ActivityTypes.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\E-Invoice\frm_E_ActivityTypes.Designer.cs">
      <DependentUpon>frm_E_ActivityTypes.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\E-Invoice\frm_E_UOM.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\E-Invoice\frm_E_UOM.Designer.cs">
      <DependentUpon>frm_E_UOM.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\E-Invoice\frm_E_InvoiceList.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\E-Invoice\frm_E_InvoiceList.Designer.cs">
      <DependentUpon>frm_E_InvoiceList.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frm_ReleaseNotes.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frm_ReleaseNotes.Designer.cs">
      <DependentUpon>frm_ReleaseNotes.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frm_Excel_Import.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frm_Excel_Import.designer.cs">
      <DependentUpon>frm_Excel_Import.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_Customer_Items.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_Customer_Items.Designer.cs">
      <DependentUpon>frm_IC_Customer_Items.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_ItemReplacment.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_ItemReplacment.Designer.cs">
      <DependentUpon>frm_IC_ItemReplacment.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frm_LcList.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frm_LcList.Designer.cs">
      <DependentUpon>frm_LcList.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frm_LC.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frm_LC.Designer.cs">
      <DependentUpon>frm_LC.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frm_InvoiceDimenstions.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frm_InvoiceDimenstions.Designer.cs">
      <DependentUpon>frm_InvoiceDimenstions.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frm_CloseInventory.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frm_CloseInventory.Designer.cs">
      <DependentUpon>frm_CloseInventory.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frm_ReEvaluate.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frm_ReEvaluate.Designer.cs">
      <DependentUpon>frm_ReEvaluate.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frm_SelectExpire.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frm_SelectExpire.Designer.cs">
      <DependentUpon>frm_SelectExpire.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_CatPosting_Peroidic.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_CatPosting_Peroidic.Designer.cs">
      <DependentUpon>frm_IC_CatPosting_Peroidic.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_CatPosting.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_CatPosting.Designer.cs">
      <DependentUpon>frm_IC_CatPosting.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_ImportItems.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_ImportItems.Designer.cs">
      <DependentUpon>frm_IC_ImportItems.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_UOM.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_UOM.Designer.cs">
      <DependentUpon>frm_IC_UOM.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_Cat.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_Cat.Designer.cs">
      <DependentUpon>frm_IC_Cat.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SL\frm_Sl_Add_Add_Taxes.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SL\frm_Sl_Add_Add_Taxes.Designer.cs">
      <DependentUpon>frm_Sl_Add_Add_Taxes.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SL\frm_Sl_Return_Add_Taxes .cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SL\frm_Sl_Return_Add_Taxes .Designer.cs">
      <DependentUpon>frm_Sl_Return_Add_Taxes .cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_Add_Taxes.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_Add_Taxes.Designer.cs">
      <DependentUpon>frm_SL_Add_Taxes.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_ImportCustomers.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_ImportCustomers.Designer.cs">
      <DependentUpon>frm_SL_ImportCustomers.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_Delivery.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_Delivery.Designer.cs">
      <DependentUpon>frm_SL_Delivery.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_CustomerGroup.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_CustomerGroup.Designer.cs">
      <DependentUpon>frm_SL_CustomerGroup.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_CustomerCategory.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_CustomerCategory.Designer.cs">
      <DependentUpon>frm_SL_CustomerCategory.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_InvoiceArchive.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_InvoiceArchive.Designer.cs">
      <DependentUpon>frm_SL_InvoiceArchive.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_InvoiceArchiveList.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_InvoiceArchiveList.Designer.cs">
      <DependentUpon>frm_SL_InvoiceArchiveList.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_Add.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SL\.Designer.cs">
      <DependentUpon>frm_SL_Add.cs</DependentUpon>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_ReturnArchive.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_ReturnArchive.Designer.cs">
      <DependentUpon>frm_SL_ReturnArchive.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_ReturnArchiveList.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_ReturnArchiveList.Designer.cs">
      <DependentUpon>frm_SL_ReturnArchiveList.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SL\frm_CustomerRegion.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SL\frm_CustomerRegion.Designer.cs">
      <DependentUpon>frm_CustomerRegion.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_AddList .cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_AddList .Designer.cs">
      <DependentUpon>frm_SL_AddList .cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ST\frm_ST_BarcodeMatchTable.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ST\frm_ST_BarcodeMatchTable.Designer.cs">
      <DependentUpon>frm_ST_BarcodeMatchTable.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ST\frm_InsertAssets.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ST\frm_InsertAssets.Designer.cs">
      <DependentUpon>frm_InsertAssets.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ST\frm_ST_Cars.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ST\frm_ST_Cars.Designer.cs">
      <DependentUpon>frm_ST_Cars.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ST\frm_ST_E_InvoiceInfo.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ST\frm_ST_E_InvoiceInfo.Designer.cs">
      <DependentUpon>frm_ST_E_InvoiceInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\uc_Currency.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\uc_Currency.Designer.cs">
      <DependentUpon>uc_Currency.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frm_InvoiceDiscs.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frm_InvoiceDiscs.Designer.cs">
      <DependentUpon>frm_InvoiceDiscs.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ST\frm_ST_ChangeUserPass.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ST\frm_ST_ChangeUserPass.Designer.cs">
      <DependentUpon>frm_ST_ChangeUserPass.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ST\frm_ST_InvoiceBook.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ST\frm_ST_InvoiceBook.Designer.cs">
      <DependentUpon>frm_ST_InvoiceBook.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_MatrixAddInv.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_MatrixAddInv.Designer.cs">
      <DependentUpon>frm_IC_MatrixAddInv.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ST\frmActivation.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ST\frmActivation.designer.cs">
      <DependentUpon>frmActivation.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ST\frm_ST_InvBooksList.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ST\frm_ST_InvBooksList.Designer.cs">
      <DependentUpon>frm_ST_InvBooksList.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\uc_CurrencyValue.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\uc_CurrencyValue.Designer.cs">
      <DependentUpon>uc_CurrencyValue.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\uc_InvPayments.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\uc_InvPayments.Designer.cs">
      <DependentUpon>uc_InvPayments.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frm_WaitingScreen.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frm_WaitingScreen.Designer.cs">
      <DependentUpon>frm_WaitingScreen.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_ItemMatrixCreate.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_ItemMatrixCreate.Designer.cs">
      <DependentUpon>frm_IC_ItemMatrixCreate.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_MatrixList.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_MatrixList.Designer.cs">
      <DependentUpon>frm_IC_MatrixList.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_Matrix.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_Matrix.Designer.cs">
      <DependentUpon>frm_IC_Matrix.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmAbout.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmAbout.Designer.cs">
      <DependentUpon>frmAbout.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_ReturnList.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_ReturnList.Designer.cs">
      <DependentUpon>frm_SL_ReturnList.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_InvoiceList.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_InvoiceList.Designer.cs">
      <DependentUpon>frm_SL_InvoiceList.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_Return.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_Return.Designer.cs">
      <DependentUpon>frm_SL_Return.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_Invoice.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_Invoice.Designer.cs">
      <DependentUpon>frm_SL_Invoice.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_Customer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_Customer.Designer.cs">
      <DependentUpon>frm_SL_Customer.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_CustomerList.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SL\frm_SL_CustomerList.Designer.cs">
      <DependentUpon>frm_SL_CustomerList.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ST\frm_ST_NewYear.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ST\frm_ST_NewYear.designer.cs">
      <DependentUpon>frm_ST_NewYear.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\IC\frm_AddUOM.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\IC\frm_AddUOM.Designer.cs">
      <DependentUpon>frm_AddUOM.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmLogin.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmLogin.designer.cs">
      <DependentUpon>frmLogin.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\uc_AddExpenses.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\uc_AddExpenses.Designer.cs">
      <DependentUpon>uc_AddExpenses.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\uc_Charts.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\uc_Charts.Designer.cs">
      <DependentUpon>uc_Charts.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\uc_LinkAccount.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\uc_LinkAccount.Designer.cs">
      <DependentUpon>uc_LinkAccount.cs</DependentUpon>
    </Compile>
    <Compile Include="Helper.cs" />
    <Compile Include="Forms\IC\frm_IC_Item.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_Item.Designer.cs">
      <DependentUpon>frm_IC_Item.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmSplash.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmSplash.Designer.cs">
      <DependentUpon>frmSplash.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_ItemsList.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_ItemsList.Designer.cs">
      <DependentUpon>frm_IC_ItemsList.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_Company.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_Company.Designer.cs">
      <DependentUpon>frm_IC_Company.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_CompaniesList.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_CompaniesList.Designer.cs">
      <DependentUpon>frm_IC_CompaniesList.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ST\frm_ST_Barcode.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ST\frm_ST_Barcode.Designer.cs">
      <DependentUpon>frm_ST_Barcode.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\Settings2.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_ACC_Journal.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_ACC_Journal.Designer.cs">
      <DependentUpon>rpt_ACC_Journal.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_ACC_Statment.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_ACC_Statment.Designer.cs">
      <DependentUpon>rpt_ACC_Statment.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_CashNote.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_CashNote.Designer.cs">
      <DependentUpon>rpt_CashNote.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_CashTransfer.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_CashTransfer.Designer.cs">
      <DependentUpon>rpt_CashTransfer.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_EmployeeDetails.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_EmployeeDetails.Designer.cs">
      <DependentUpon>rpt_EmployeeDetails.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_EmployeeStroy.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_EmployeeStroy.designer.cs">
      <DependentUpon>rpt_EmployeeStroy.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_HR_Penalty.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_HR_Penalty.Designer.cs">
      <DependentUpon>rpt_HR_Penalty.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_HR_Reward.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_HR_Reward.Designer.cs">
      <DependentUpon>rpt_HR_Reward.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_IC_BOM.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_IC_BOM.Designer.cs">
      <DependentUpon>rpt_IC_BOM.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_IC_Damaged.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_IC_Damaged.Designer.cs">
      <DependentUpon>rpt_IC_Damaged.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_IC_InTrns.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_IC_InTrns.Designer.cs">
      <DependentUpon>rpt_IC_InTrns.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_IC_Multiple_Weights_Sub.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_IC_Multiple_Weights_Sub.Designer.cs">
      <DependentUpon>rpt_IC_Multiple_Weights_Sub.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_IC_OutTrns.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_IC_OutTrns.Designer.cs">
      <DependentUpon>rpt_IC_OutTrns.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_IC_StoreMove.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_IC_StoreMove.Designer.cs">
      <DependentUpon>rpt_IC_StoreMove.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_JO_JobOrder.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_JO_JobOrder.Designer.cs">
      <DependentUpon>rpt_JO_JobOrder.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_JO_JobOrderListCustomer.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_JO_JobOrderListCustomer.Designer.cs">
      <DependentUpon>rpt_JO_JobOrderListCustomer.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_JO_JobOrderListDept.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_JO_JobOrderListDept.Designer.cs">
      <DependentUpon>rpt_JO_JobOrderListDept.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_Manf_QC.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_Manf_QC.Designer.cs">
      <DependentUpon>rpt_Manf_QC.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_manufacture.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_manufacture.Designer.cs">
      <DependentUpon>rpt_manufacture.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_manufacture_actualRows.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_manufacture_actualRows.Designer.cs">
      <DependentUpon>rpt_manufacture_actualRows.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_multiple_weights.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_multiple_weights.Designer.cs">
      <DependentUpon>rpt_multiple_weights.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_multiple_weights_dup.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_multiple_weights_dup.Designer.cs">
      <DependentUpon>rpt_multiple_weights_dup.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_PayNotes.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_PayNotes.Designer.cs">
      <DependentUpon>rpt_PayNotes.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_PriceLevel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_PriceLevel.Designer.cs">
      <DependentUpon>rpt_PriceLevel.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_Printed_Receipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_Printed_Receipt.designer.cs">
      <DependentUpon>rpt_Printed_Receipt.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_PR_Invoice.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_PR_Invoice.Designer.cs">
      <DependentUpon>rpt_PR_Invoice.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_PR_Invoice_Store.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_PR_Invoice_Store.Designer.cs">
      <DependentUpon>rpt_PR_Invoice_Store.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_PR_Invoice_Store_Sub.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_PR_Invoice_Store_Sub.Designer.cs">
      <DependentUpon>rpt_PR_Invoice_Store_Sub.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_PR_PriceLevel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_PR_PriceLevel.Designer.cs">
      <DependentUpon>rpt_PR_PriceLevel.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_PR_PurchaseOrder.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_PR_PurchaseOrder.Designer.cs">
      <DependentUpon>rpt_PR_PurchaseOrder.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_PR_Quote.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_PR_Quote.Designer.cs">
      <DependentUpon>rpt_PR_Quote.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_PR_Request.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_PR_Request.Designer.cs">
      <DependentUpon>rpt_PR_Request.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_PR_ReturnInvoice.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_PR_ReturnInvoice.Designer.cs">
      <DependentUpon>rpt_PR_ReturnInvoice.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_RecieveNotes.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_RecieveNotes.Designer.cs">
      <DependentUpon>rpt_RecieveNotes.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_RevExp.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_RevExp.Designer.cs">
      <DependentUpon>rpt_RevExp.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_SL_Invoice.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_SL_Invoice.Designer.cs">
      <DependentUpon>rpt_SL_Invoice.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_SL_Invoice_Store.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_SL_Invoice_Store.Designer.cs">
      <DependentUpon>rpt_SL_Invoice_Store.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_SL_Invoice_Store_Sub.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_SL_Invoice_Store_Sub.Designer.cs">
      <DependentUpon>rpt_SL_Invoice_Store_Sub.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_SL_Quote.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_SL_Quote.Designer.cs">
      <DependentUpon>rpt_SL_Quote.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_SL_Add.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_SL_Add.Designer.cs">
      <DependentUpon>rpt_SL_Add.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_SL_ReturnInvoice.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_SL_ReturnInvoice.Designer.cs">
      <DependentUpon>rpt_SL_ReturnInvoice.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_SL_SalesOrder.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_SL_SalesOrder.Designer.cs">
      <DependentUpon>rpt_SL_SalesOrder.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_SL_SalesOrder_Manufacturing.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_SL_SalesOrder_Manufacturing.Designer.cs">
      <DependentUpon>rpt_SL_SalesOrder_Manufacturing.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Documents\rpt_Weight.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Documents\rpt_Weight.Designer.cs">
      <DependentUpon>rpt_Weight.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\ExtensionsHelper.cs" />
    <Compile Include="Reports\frm_ReportViewer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\frm_ReportViewer.designer.cs">
      <DependentUpon>frm_ReportViewer.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Properties\Resources1.Designer.cs" />
    <Compile Include="Reports\ReportsRTL.cs" />
    <Compile Include="Reports\ReportsUtils.cs" />
    <Compile Include="Reports\rpt_barcodeLabels.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\rpt_barcodeLabels.designer.cs">
      <DependentUpon>rpt_barcodeLabels.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\rpt_Sub_Tree.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\rpt_Sub_Tree.designer.cs">
      <DependentUpon>rpt_Sub_Tree.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\rpt_Template.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\rpt_Template.designer.cs">
      <DependentUpon>rpt_Template.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\rpt_Template2.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\rpt_Template2.designer.cs">
      <DependentUpon>rpt_Template2.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_CapitalInDeferredInvoices.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_CapitalInDeferredInvoices.Designer.cs">
      <DependentUpon>frm_CapitalInDeferredInvoices.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_E_Invoice.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_E_Invoice.Designer.cs">
      <DependentUpon>frm_SL_E_Invoice.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_SubTaxTotal.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_SubTaxTotal.Designer.cs">
      <DependentUpon>frm_SubTaxTotal.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_SubTaxNet.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_SubTaxNet.Designer.cs">
      <DependentUpon>frm_SubTaxNet.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_CustomsCertifecationWarning.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_CustomsCertifecationWarning.Designer.cs">
      <DependentUpon>frm_CustomsCertifecationWarning.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_IC_StoreSales.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_IC_StoreSales.Designer.cs">
      <DependentUpon>frm_IC_StoreSales.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_MrAllSales.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_MrAllSales.Designer.cs">
      <DependentUpon>frm_MrAllSales.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_averagesellingpriceoftheitems.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_averagesellingpriceoftheitems.Designer.cs">
      <DependentUpon>frm_SL_averagesellingpriceoftheitems.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_Car_Weights.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_Car_Weights.Designer.cs">
      <DependentUpon>frm_SL_Car_Weights.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_CustomerGroupItemsNet.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_CustomerGroupItemsNet.Designer.cs">
      <DependentUpon>frm_SL_CustomerGroupItemsNet.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_CustomerInvoicesHeaders.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_CustomerInvoicesHeaders.Designer.cs">
      <DependentUpon>frm_SL_CustomerInvoicesHeaders.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_CustomerItemsSales.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_CustomerItemsSales.Designer.cs">
      <DependentUpon>frm_SL_CustomerItemsSales.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_CustomerItemsSalesReturns.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_CustomerItemsSalesReturns.Designer.cs">
      <DependentUpon>frm_SL_CustomerItemsSalesReturns.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_CustomerItemsSales_OutTrns.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_CustomerItemsSales_OutTrns.Designer.cs">
      <DependentUpon>frm_SL_CustomerItemsSales_OutTrns.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_CustomerTotal_Invoices.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_CustomerTotal_Invoices.Designer.cs">
      <DependentUpon>frm_SL_CustomerTotal_Invoices.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_DelegatesSales.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_DelegatesSales.Designer.cs">
      <DependentUpon>frm_SL_DelegatesSales.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_DeliveryOfficialsSales.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_DeliveryOfficialsSales.Designer.cs">
      <DependentUpon>frm_SL_DeliveryOfficialsSales.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_InvoiceDetails.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_InvoiceDetails.Designer.cs">
      <DependentUpon>frm_SL_InvoiceDetails.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_InvoicesHeaders.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_InvoicesHeaders.Designer.cs">
      <DependentUpon>frm_SL_InvoicesHeaders.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_Invoices_Due.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_Invoices_Due.Designer.cs">
      <DependentUpon>frm_SL_Invoices_Due.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_ItemsNetSalesDetails.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_ItemsNetSalesDetails.Designer.cs">
      <DependentUpon>frm_SL_ItemsNetSalesDetails.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_ItemsReturn.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_ItemsReturn.Designer.cs">
      <DependentUpon>frm_SL_ItemsReturn.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_ItemsSales.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_ItemsSales.Designer.cs">
      <DependentUpon>frm_SL_ItemsSales.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_ItemsSalesDetails.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_ItemsSalesDetails.Designer.cs">
      <DependentUpon>frm_SL_ItemsSalesDetails.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_ItemsSalesTotals.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_ItemsSalesTotals.Designer.cs">
      <DependentUpon>frm_SL_ItemsSalesTotals.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_JobOrderInv.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_JobOrderInv.Designer.cs">
      <DependentUpon>frm_SL_JobOrderInv.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_Sl_JobOrderStatus.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_Sl_JobOrderStatus.Designer.cs">
      <DependentUpon>frm_Sl_JobOrderStatus.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_Profit_Loss.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_Profit_Loss.Designer.cs">
      <DependentUpon>frm_SL_Profit_Loss.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_ReturnHeaders.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_ReturnHeaders.Designer.cs">
      <DependentUpon>frm_SL_ReturnHeaders.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_SalesOrderItems.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_SalesOrderItems.Designer.cs">
      <DependentUpon>frm_SL_SalesOrderItems.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_SalesOrderItemsAndBalance.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_SalesOrderItemsAndBalance.Designer.cs">
      <DependentUpon>frm_SL_SalesOrderItemsAndBalance.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_Warranty.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\frm_SL_Warranty.Designer.cs">
      <DependentUpon>frm_SL_Warranty.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\rpt_SL_CustomerSale.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Reports\SL\rpt_SL_CustomerSale.Designer.cs">
      <DependentUpon>rpt_SL_CustomerSale.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\rpt_SL_CustomerTrans.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\SL\rpt_SL_CustomerTrans.Designer.cs">
      <DependentUpon>rpt_SL_CustomerTrans.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\rpt_SL_CustomerTrans_Sub.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\SL\rpt_SL_CustomerTrans_Sub.Designer.cs">
      <DependentUpon>rpt_SL_CustomerTrans_Sub.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\SL\rpt_SL_ItemTrade.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\SL\rpt_SL_ItemTrade.Designer.cs">
      <DependentUpon>rpt_SL_ItemTrade.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\uc_Currency.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Reports\uc_Currency.designer.cs">
      <DependentUpon>uc_Currency.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\uc_CurrencyValue.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Reports\uc_CurrencyValue.designer.cs">
      <DependentUpon>uc_CurrencyValue.cs</DependentUpon>
    </Compile>
    <Compile Include="RTL.cs" />
    <Compile Include="Translator.cs" />
    <Compile Include="ErpUtils.cs" />
    <Compile Include="Forms\IC\frm_IC_StoresList.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_StoresList.Designer.cs">
      <DependentUpon>frm_IC_StoresList.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frmMain.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frmMain.designer.cs">
      <DependentUpon>frmMain.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_Store.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\IC\frm_IC_Store.Designer.cs">
      <DependentUpon>frm_IC_Store.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ST\frm_ST_Store.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ST\frm_ST_Store.Designer.cs">
      <DependentUpon>frm_ST_Store.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ST\frm_ST_Print.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ST\frm_ST_Print.Designer.cs">
      <DependentUpon>frm_ST_Print.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\ST\frm_ST_CompInfo.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ST\frm_ST_CompInfo.Designer.cs">
      <DependentUpon>frm_ST_CompInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <EmbeddedResource Include="Customers.resx">
      <DependentUpon>Customers.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form1.resx">
      <DependentUpon>Form1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\E-Invoice\frm_AddNotesList.ar-EG.resx">
      <DependentUpon>frm_AddNotesList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\E-Invoice\frm_AddNotesList.resx">
      <DependentUpon>frm_AddNotesList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\E-Invoice\frm_DebitNotesList.ar-EG.resx">
      <DependentUpon>frm_DebitNotesList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\E-Invoice\frm_DebitNotesList.resx">
      <DependentUpon>frm_DebitNotesList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\E-Invoice\frm_E_RecievedInvoices.ar-EG.resx">
      <DependentUpon>frm_E_RecievedInvoices.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\E-Invoice\frm_E_RecievedInvoices.resx">
      <DependentUpon>frm_E_RecievedInvoices.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\E-Invoice\frm_E_StoreCode.ar-EG.resx">
      <DependentUpon>frm_E_StoreCode.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\E-Invoice\frm_E_StoreCode.resx">
      <DependentUpon>frm_E_StoreCode.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\E-Invoice\frm_E_TaxableSubtypes.ar-EG.resx">
      <DependentUpon>frm_E_TaxableSubtypes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\E-Invoice\frm_E_TaxableSubtypes.resx">
      <DependentUpon>frm_E_TaxableSubtypes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\E-Invoice\frm_E_TaxableTypes.ar-EG.resx">
      <DependentUpon>frm_E_TaxableTypes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\E-Invoice\frm_E_TaxableTypes.resx">
      <DependentUpon>frm_E_TaxableTypes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\E-Invoice\frm_E_InvoiceList_Rejected.ar-EG.resx">
      <DependentUpon>frm_E_InvoiceList_Rejected.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\E-Invoice\frm_E_InvoiceList_Rejected.resx">
      <DependentUpon>frm_E_InvoiceList_Rejected.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\E-Invoice\frm_E_InvoiceList.ar-EG.resx">
      <DependentUpon>frm_E_InvoiceList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\E-Invoice\frm_E_InvoiceList.resx">
      <DependentUpon>frm_E_InvoiceList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\E-Invoice\frm_E_ItemCodes.ar-EG.resx">
      <DependentUpon>frm_E_ItemCodes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\E-Invoice\frm_E_ItemCodes.resx">
      <DependentUpon>frm_E_ItemCodes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\E-Invoice\frm_E_Currency.ar-EG.resx">
      <DependentUpon>frm_E_Currency.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\E-Invoice\frm_E_Currency.resx">
      <DependentUpon>frm_E_Currency.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\E-Invoice\frm_E_ActivityTypes.ar-EG.resx">
      <DependentUpon>frm_E_ActivityTypes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\E-Invoice\frm_E_ActivityTypes.resx">
      <DependentUpon>frm_E_ActivityTypes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\E-Invoice\frm_E_UOM.ar-EG.resx">
      <DependentUpon>frm_E_UOM.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\E-Invoice\frm_E_UOM.resx">
      <DependentUpon>frm_E_UOM.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frm_ReleaseNotes.resx">
      <DependentUpon>frm_ReleaseNotes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frm_Excel_Import.resx">
      <DependentUpon>frm_Excel_Import.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_Customer_Items.ar-EG.resx">
      <DependentUpon>frm_IC_Customer_Items.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_Customer_Items.resx">
      <DependentUpon>frm_IC_Customer_Items.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_ItemReplacment.ar-EG.resx">
      <DependentUpon>frm_IC_ItemReplacment.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_ItemReplacment.resx">
      <DependentUpon>frm_IC_ItemReplacment.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frm_LcList.ar-EG.resx">
      <DependentUpon>frm_LcList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frm_LcList.resx">
      <DependentUpon>frm_LcList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frm_LC.ar-EG.resx">
      <DependentUpon>frm_LC.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frm_LC.resx">
      <DependentUpon>frm_LC.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frm_InvoiceDimenstions.ar-EG.resx">
      <DependentUpon>frm_InvoiceDimenstions.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frm_InvoiceDimenstions.resx">
      <DependentUpon>frm_InvoiceDimenstions.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frm_CloseInventory.ar-EG.resx">
      <DependentUpon>frm_CloseInventory.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frm_CloseInventory.resx">
      <DependentUpon>frm_CloseInventory.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frm_ReEvaluate.ar-EG.resx">
      <DependentUpon>frm_ReEvaluate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frm_ReEvaluate.resx">
      <DependentUpon>frm_ReEvaluate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frm_SelectExpire.ar-EG.resx">
      <DependentUpon>frm_SelectExpire.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frm_SelectExpire.resx">
      <DependentUpon>frm_SelectExpire.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_CatPosting_Peroidic.ar-EG.resx">
      <DependentUpon>frm_IC_CatPosting_Peroidic.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_CatPosting_Peroidic.resx">
      <DependentUpon>frm_IC_CatPosting_Peroidic.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_CatPosting.ar-EG.resx">
      <DependentUpon>frm_IC_CatPosting.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_CatPosting.resx">
      <DependentUpon>frm_IC_CatPosting.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_ImportItems.ar-EG.resx">
      <DependentUpon>frm_IC_ImportItems.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_ImportItems.resx">
      <DependentUpon>frm_IC_ImportItems.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_UOM.ar-EG.resx">
      <DependentUpon>frm_IC_UOM.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_UOM.resx">
      <DependentUpon>frm_IC_UOM.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_Cat.ar-EG.resx">
      <DependentUpon>frm_IC_Cat.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_Cat.resx">
      <DependentUpon>frm_IC_Cat.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_Sl_Add_Add_Taxes.ar-EG.resx">
      <DependentUpon>frm_Sl_Add_Add_Taxes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_Sl_Add_Add_Taxes.resx">
      <DependentUpon>frm_Sl_Add_Add_Taxes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_Sl_Return_Add_Taxes .ar-EG.resx">
      <DependentUpon>frm_Sl_Return_Add_Taxes .cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_Sl_Return_Add_Taxes .resx">
      <DependentUpon>frm_Sl_Return_Add_Taxes .cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_Add_Taxes.ar-EG.resx">
      <DependentUpon>frm_SL_Add_Taxes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_Add_Taxes.resx">
      <DependentUpon>frm_SL_Add_Taxes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_ImportCustomers.ar-EG.resx">
      <DependentUpon>frm_SL_ImportCustomers.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_ImportCustomers.resx">
      <DependentUpon>frm_SL_ImportCustomers.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_Delivery.ar-EG.resx">
      <DependentUpon>frm_SL_Delivery.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_Delivery.resx">
      <DependentUpon>frm_SL_Delivery.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_CustomerGroup.ar-EG.resx">
      <DependentUpon>frm_SL_CustomerGroup.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_CustomerGroup.resx">
      <DependentUpon>frm_SL_CustomerGroup.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_CustomerCategory.ar-EG.resx">
      <DependentUpon>frm_SL_CustomerCategory.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_CustomerCategory.resx">
      <DependentUpon>frm_SL_CustomerCategory.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_InvoiceArchive.ar-EG.resx">
      <DependentUpon>frm_SL_InvoiceArchive.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_InvoiceArchive.resx">
      <DependentUpon>frm_SL_InvoiceArchive.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_InvoiceArchiveList.ar-EG.resx">
      <DependentUpon>frm_SL_InvoiceArchiveList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_InvoiceArchiveList.resx">
      <DependentUpon>frm_SL_InvoiceArchiveList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_Add.ar-EG.resx">
      <DependentUpon>frm_SL_Add.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_Add.resx">
      <DependentUpon>frm_SL_Add.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_ReturnArchive.ar-EG.resx">
      <DependentUpon>frm_SL_ReturnArchive.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_ReturnArchive.resx">
      <DependentUpon>frm_SL_ReturnArchive.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_ReturnArchiveList.ar-EG.resx">
      <DependentUpon>frm_SL_ReturnArchiveList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_ReturnArchiveList.resx">
      <DependentUpon>frm_SL_ReturnArchiveList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_CustomerRegion.ar-EG.resx">
      <DependentUpon>frm_CustomerRegion.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_CustomerRegion.resx">
      <DependentUpon>frm_CustomerRegion.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_AddList .ar-EG.resx">
      <DependentUpon>frm_SL_AddList .cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_AddList .resx">
      <DependentUpon>frm_SL_AddList .cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ST\frm_ST_BarcodeMatchTable.ar-EG.resx">
      <DependentUpon>frm_ST_BarcodeMatchTable.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ST\frm_ST_BarcodeMatchTable.resx">
      <DependentUpon>frm_ST_BarcodeMatchTable.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ST\frm_InsertAssets.ar-EG.resx">
      <DependentUpon>frm_InsertAssets.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ST\frm_InsertAssets.resx">
      <DependentUpon>frm_InsertAssets.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ST\frm_ST_Cars.ar-EG.resx">
      <DependentUpon>frm_ST_Cars.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ST\frm_ST_Cars.resx">
      <DependentUpon>frm_ST_Cars.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ST\frm_ST_NewYear.ar-EG.resx">
      <DependentUpon>frm_ST_NewYear.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ST\frm_ST_E_InvoiceInfo.ar-EG.resx">
      <DependentUpon>frm_ST_E_InvoiceInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ST\frm_ST_E_InvoiceInfo.resx">
      <DependentUpon>frm_ST_E_InvoiceInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\uc_Currency.ar-EG.resx">
      <DependentUpon>uc_Currency.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\uc_Currency.resx">
      <DependentUpon>uc_Currency.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frm_InvoiceDiscs.ar-EG.resx">
      <DependentUpon>frm_InvoiceDiscs.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frm_InvoiceDiscs.resx">
      <DependentUpon>frm_InvoiceDiscs.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ST\frm_ST_ChangeUserPass.ar-EG.resx">
      <DependentUpon>frm_ST_ChangeUserPass.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ST\frm_ST_ChangeUserPass.resx">
      <DependentUpon>frm_ST_ChangeUserPass.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ST\frm_ST_InvoiceBook.ar-EG.resx">
      <DependentUpon>frm_ST_InvoiceBook.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ST\frm_ST_InvoiceBook.resx">
      <DependentUpon>frm_ST_InvoiceBook.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_MatrixAddInv.ar-EG.resx">
      <DependentUpon>frm_IC_MatrixAddInv.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_MatrixAddInv.resx">
      <DependentUpon>frm_IC_MatrixAddInv.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ST\frmActivation.ar-EG.resx">
      <DependentUpon>frmActivation.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ST\frmActivation.resx">
      <DependentUpon>frmActivation.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ST\frm_ST_InvBooksList.ar-EG.resx">
      <DependentUpon>frm_ST_InvBooksList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ST\frm_ST_InvBooksList.resx">
      <DependentUpon>frm_ST_InvBooksList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\uc_CurrencyValue.ar-EG.resx">
      <DependentUpon>uc_CurrencyValue.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\uc_CurrencyValue.resx">
      <DependentUpon>uc_CurrencyValue.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\uc_InvPayments.ar-EG.resx">
      <DependentUpon>uc_InvPayments.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\uc_InvPayments.resx">
      <DependentUpon>uc_InvPayments.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmLogin.ar-EG.resx">
      <DependentUpon>frmLogin.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmMain.ar-EG.resx">
      <DependentUpon>frmMain.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frm_WaitingScreen.resx">
      <DependentUpon>frm_WaitingScreen.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_ItemMatrixCreate.ar-EG.resx">
      <DependentUpon>frm_IC_ItemMatrixCreate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_ItemMatrixCreate.resx">
      <DependentUpon>frm_IC_ItemMatrixCreate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_MatrixList.ar-EG.resx">
      <DependentUpon>frm_IC_MatrixList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_MatrixList.resx">
      <DependentUpon>frm_IC_MatrixList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_Matrix.ar-EG.resx">
      <DependentUpon>frm_IC_Matrix.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_Matrix.resx">
      <DependentUpon>frm_IC_Matrix.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_AddUOM.ar-EG.resx">
      <DependentUpon>frm_AddUOM.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_CompaniesList.ar-EG.resx">
      <DependentUpon>frm_IC_CompaniesList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_Company.ar-EG.resx">
      <DependentUpon>frm_IC_Company.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_Item.ar-EG.resx">
      <DependentUpon>frm_IC_Item.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_ItemsList.ar-EG.resx">
      <DependentUpon>frm_IC_ItemsList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_Store.ar-EG.resx">
      <DependentUpon>frm_IC_Store.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_StoresList.ar-EG.resx">
      <DependentUpon>frm_IC_StoresList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_Customer.ar-EG.resx">
      <DependentUpon>frm_SL_Customer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_CustomerList.ar-EG.resx">
      <DependentUpon>frm_SL_CustomerList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_Invoice.ar-EG.resx">
      <DependentUpon>frm_SL_Invoice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_InvoiceList.ar-EG.resx">
      <DependentUpon>frm_SL_InvoiceList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmAbout.resx">
      <DependentUpon>frmAbout.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_Return.ar-EG.resx">
      <DependentUpon>frm_SL_Return.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_ReturnList.ar-EG.resx">
      <DependentUpon>frm_SL_ReturnList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_ReturnList.resx">
      <DependentUpon>frm_SL_ReturnList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_InvoiceList.resx">
      <DependentUpon>frm_SL_InvoiceList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_Return.resx">
      <DependentUpon>frm_SL_Return.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_Invoice.resx">
      <DependentUpon>frm_SL_Invoice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_Customer.resx">
      <DependentUpon>frm_SL_Customer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SL\frm_SL_CustomerList.resx">
      <DependentUpon>frm_SL_CustomerList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ST\frm_ST_Barcode.ar-EG.resx">
      <DependentUpon>frm_ST_Barcode.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ST\frm_ST_CompInfo.ar-EG.resx">
      <DependentUpon>frm_ST_CompInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ST\frm_ST_NewYear.resx">
      <DependentUpon>frm_ST_NewYear.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_AddUOM.resx">
      <DependentUpon>frm_AddUOM.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmLogin.resx">
      <DependentUpon>frmLogin.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ST\frm_ST_Print.ar-EG.resx">
      <DependentUpon>frm_ST_Print.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ST\frm_ST_Store.ar-EG.resx">
      <DependentUpon>frm_ST_Store.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\uc_AddExpenses.ar-EG.resx">
      <DependentUpon>uc_AddExpenses.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\uc_AddExpenses.resx">
      <DependentUpon>uc_AddExpenses.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\uc_Charts.ar-EG.resx">
      <DependentUpon>uc_Charts.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\uc_Charts.resx">
      <DependentUpon>uc_Charts.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\uc_LinkAccount.ar-EG.resx">
      <DependentUpon>uc_LinkAccount.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\uc_LinkAccount.resx">
      <DependentUpon>uc_LinkAccount.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_Item.resx">
      <DependentUpon>frm_IC_Item.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmSplash.resx">
      <DependentUpon>frmSplash.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_ItemsList.resx">
      <DependentUpon>frm_IC_ItemsList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_Company.resx">
      <DependentUpon>frm_IC_Company.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_CompaniesList.resx">
      <DependentUpon>frm_IC_CompaniesList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_StoresList.resx">
      <DependentUpon>frm_IC_StoresList.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frmMain.resx">
      <DependentUpon>frmMain.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\IC\frm_IC_Store.resx">
      <DependentUpon>frm_IC_Store.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ST\frm_ST_Barcode.resx">
      <DependentUpon>frm_ST_Barcode.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ST\frm_ST_Store.resx">
      <DependentUpon>frm_ST_Store.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ST\frm_ST_Print.resx">
      <DependentUpon>frm_ST_Print.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ST\frm_ST_CompInfo.resx">
      <DependentUpon>frm_ST_CompInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <EmbeddedResource Include="Reports\Documents\rpt_ACC_Journal.resx">
      <DependentUpon>rpt_ACC_Journal.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_ACC_Statment.ar-EG.resx">
      <DependentUpon>rpt_ACC_Statment.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_ACC_Statment.resx">
      <DependentUpon>rpt_ACC_Statment.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_CashNote.resx">
      <DependentUpon>rpt_CashNote.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_CashTransfer.resx">
      <DependentUpon>rpt_CashTransfer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_EmployeeDetails.ar-EG.resx">
      <DependentUpon>rpt_EmployeeDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_EmployeeDetails.resx">
      <DependentUpon>rpt_EmployeeDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_EmployeeStroy.ar-EG.resx">
      <DependentUpon>rpt_EmployeeStroy.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_EmployeeStroy.resx">
      <DependentUpon>rpt_EmployeeStroy.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_HR_Penalty.resx">
      <DependentUpon>rpt_HR_Penalty.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_HR_Reward.resx">
      <DependentUpon>rpt_HR_Reward.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_IC_BOM.resx">
      <DependentUpon>rpt_IC_BOM.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_IC_Damaged.resx">
      <DependentUpon>rpt_IC_Damaged.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_IC_InTrns.resx">
      <DependentUpon>rpt_IC_InTrns.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_IC_Multiple_Weights_Sub.resx">
      <DependentUpon>rpt_IC_Multiple_Weights_Sub.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_IC_OutTrns.resx">
      <DependentUpon>rpt_IC_OutTrns.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_IC_StoreMove.resx">
      <DependentUpon>rpt_IC_StoreMove.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_JO_JobOrder.resx">
      <DependentUpon>rpt_JO_JobOrder.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_JO_JobOrderListCustomer.resx">
      <DependentUpon>rpt_JO_JobOrderListCustomer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_JO_JobOrderListDept.resx">
      <DependentUpon>rpt_JO_JobOrderListDept.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_Manf_QC.resx">
      <DependentUpon>rpt_Manf_QC.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_manufacture.resx">
      <DependentUpon>rpt_manufacture.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_manufacture_actualRows.resx">
      <DependentUpon>rpt_manufacture_actualRows.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_multiple_weights.ar-EG.resx">
      <DependentUpon>rpt_multiple_weights.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_multiple_weights.resx">
      <DependentUpon>rpt_multiple_weights.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_multiple_weights_dup.ar-EG.resx">
      <DependentUpon>rpt_multiple_weights_dup.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_multiple_weights_dup.resx">
      <DependentUpon>rpt_multiple_weights_dup.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_PayNotes.resx">
      <DependentUpon>rpt_PayNotes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_PriceLevel.resx">
      <DependentUpon>rpt_PriceLevel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_Printed_Receipt.resx">
      <DependentUpon>rpt_Printed_Receipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_PR_Invoice.ar-EG.resx">
      <DependentUpon>rpt_PR_Invoice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_PR_Invoice.resx">
      <DependentUpon>rpt_PR_Invoice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_PR_Invoice_Store.ar-EG.resx">
      <DependentUpon>rpt_PR_Invoice_Store.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_PR_Invoice_Store.resx">
      <DependentUpon>rpt_PR_Invoice_Store.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_PR_Invoice_Store_Sub.resx">
      <DependentUpon>rpt_PR_Invoice_Store_Sub.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_PR_PriceLevel.resx">
      <DependentUpon>rpt_PR_PriceLevel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_PR_PurchaseOrder.ar-EG.resx">
      <DependentUpon>rpt_PR_PurchaseOrder.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_PR_PurchaseOrder.resx">
      <DependentUpon>rpt_PR_PurchaseOrder.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_PR_Quote.resx">
      <DependentUpon>rpt_PR_Quote.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_PR_Request.resx">
      <DependentUpon>rpt_PR_Request.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_PR_ReturnInvoice.ar-EG.resx">
      <DependentUpon>rpt_PR_ReturnInvoice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_PR_ReturnInvoice.resx">
      <DependentUpon>rpt_PR_ReturnInvoice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_RecieveNotes.resx">
      <DependentUpon>rpt_RecieveNotes.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_RevExp.resx">
      <DependentUpon>rpt_RevExp.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_SL_Invoice.ar-EG.resx">
      <DependentUpon>rpt_SL_Invoice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_SL_Invoice.resx">
      <DependentUpon>rpt_SL_Invoice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_SL_Invoice_Store.ar-EG.resx">
      <DependentUpon>rpt_SL_Invoice_Store.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_SL_Invoice_Store.resx">
      <DependentUpon>rpt_SL_Invoice_Store.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_SL_Invoice_Store_Sub.resx">
      <DependentUpon>rpt_SL_Invoice_Store_Sub.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_SL_Quote.resx">
      <DependentUpon>rpt_SL_Quote.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_SL_Add.ar-EG.resx">
      <DependentUpon>rpt_SL_Add.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_SL_Add.resx">
      <DependentUpon>rpt_SL_Add.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_SL_ReturnInvoice.ar-EG.resx">
      <DependentUpon>rpt_SL_ReturnInvoice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_SL_ReturnInvoice.resx">
      <DependentUpon>rpt_SL_ReturnInvoice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_SL_SalesOrder.resx">
      <DependentUpon>rpt_SL_SalesOrder.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_SL_SalesOrder_Manufacturing.resx">
      <DependentUpon>rpt_SL_SalesOrder_Manufacturing.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Documents\rpt_Weight.resx">
      <DependentUpon>rpt_Weight.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\frm_ReportViewer.ar-EG.resx">
      <DependentUpon>frm_ReportViewer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\frm_ReportViewer.resx">
      <DependentUpon>frm_ReportViewer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Properties\Resources.resx" />
    <EmbeddedResource Include="Reports\rpt_barcodeLabels.resx">
      <DependentUpon>rpt_barcodeLabels.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\rpt_Sub_Tree.resx">
      <DependentUpon>rpt_Sub_Tree.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\rpt_Template.resx">
      <DependentUpon>rpt_Template.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\rpt_Template2.resx">
      <DependentUpon>rpt_Template2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_CapitalInDeferredInvoices.ar-EG.resx">
      <DependentUpon>frm_CapitalInDeferredInvoices.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_CapitalInDeferredInvoices.resx">
      <DependentUpon>frm_CapitalInDeferredInvoices.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_E_Invoice.ar-EG.resx">
      <DependentUpon>frm_SL_E_Invoice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_E_Invoice.ar-ER.resx">
      <DependentUpon>frm_SL_E_Invoice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_E_Invoice.resx">
      <DependentUpon>frm_SL_E_Invoice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SubTaxTotal.ar-EG.resx">
      <DependentUpon>frm_SubTaxTotal.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SubTaxTotal.resx">
      <DependentUpon>frm_SubTaxTotal.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SubTaxNet.ar-EG.resx">
      <DependentUpon>frm_SubTaxNet.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SubTaxNet.resx">
      <DependentUpon>frm_SubTaxNet.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_CustomsCertifecationWarning.ar-EG.resx">
      <DependentUpon>frm_CustomsCertifecationWarning.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_CustomsCertifecationWarning.resx">
      <DependentUpon>frm_CustomsCertifecationWarning.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_IC_StoreSales.ar-EG.resx">
      <DependentUpon>frm_IC_StoreSales.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_IC_StoreSales.resx">
      <DependentUpon>frm_IC_StoreSales.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_MrAllSales.ar-EG.resx">
      <DependentUpon>frm_MrAllSales.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_MrAllSales.resx">
      <DependentUpon>frm_MrAllSales.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_averagesellingpriceoftheitems.ar-EG.resx">
      <DependentUpon>frm_SL_averagesellingpriceoftheitems.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_averagesellingpriceoftheitems.resx">
      <DependentUpon>frm_SL_averagesellingpriceoftheitems.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_Car_Weights.ar-EG.resx">
      <DependentUpon>frm_SL_Car_Weights.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_Car_Weights.resx">
      <DependentUpon>frm_SL_Car_Weights.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_CustomerGroupItemsNet.ar-EG.resx">
      <DependentUpon>frm_SL_CustomerGroupItemsNet.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_CustomerGroupItemsNet.resx">
      <DependentUpon>frm_SL_CustomerGroupItemsNet.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_CustomerInvoicesHeaders.ar-EG.resx">
      <DependentUpon>frm_SL_CustomerInvoicesHeaders.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_CustomerInvoicesHeaders.resx">
      <DependentUpon>frm_SL_CustomerInvoicesHeaders.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_CustomerItemsSales.ar-EG.resx">
      <DependentUpon>frm_SL_CustomerItemsSales.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_CustomerItemsSales.resx">
      <DependentUpon>frm_SL_CustomerItemsSales.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_CustomerItemsSalesReturns.ar-EG.resx">
      <DependentUpon>frm_SL_CustomerItemsSalesReturns.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_CustomerItemsSalesReturns.resx">
      <DependentUpon>frm_SL_CustomerItemsSalesReturns.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_CustomerItemsSales_OutTrns.ar-EG.resx">
      <DependentUpon>frm_SL_CustomerItemsSales_OutTrns.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_CustomerItemsSales_OutTrns.resx">
      <DependentUpon>frm_SL_CustomerItemsSales_OutTrns.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_CustomerTotal_Invoices.ar-EG.resx">
      <DependentUpon>frm_SL_CustomerTotal_Invoices.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_CustomerTotal_Invoices.resx">
      <DependentUpon>frm_SL_CustomerTotal_Invoices.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_DelegatesSales.ar-EG.resx">
      <DependentUpon>frm_SL_DelegatesSales.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_DelegatesSales.resx">
      <DependentUpon>frm_SL_DelegatesSales.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_DeliveryOfficialsSales.ar-EG.resx">
      <DependentUpon>frm_SL_DeliveryOfficialsSales.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_DeliveryOfficialsSales.resx">
      <DependentUpon>frm_SL_DeliveryOfficialsSales.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_InvoiceDetails.ar-EG.resx">
      <DependentUpon>frm_SL_InvoiceDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_InvoiceDetails.resx">
      <DependentUpon>frm_SL_InvoiceDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_InvoicesHeaders.ar-EG.resx">
      <DependentUpon>frm_SL_InvoicesHeaders.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_InvoicesHeaders.resx">
      <DependentUpon>frm_SL_InvoicesHeaders.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_Invoices_Due.ar-EG.resx">
      <DependentUpon>frm_SL_Invoices_Due.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_Invoices_Due.resx">
      <DependentUpon>frm_SL_Invoices_Due.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_ItemsNetSalesDetails.ar-EG.resx">
      <DependentUpon>frm_SL_ItemsNetSalesDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_ItemsNetSalesDetails.resx">
      <DependentUpon>frm_SL_ItemsNetSalesDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_ItemsReturn.ar-EG.resx">
      <DependentUpon>frm_SL_ItemsReturn.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_ItemsReturn.resx">
      <DependentUpon>frm_SL_ItemsReturn.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_ItemsSales.ar-EG.resx">
      <DependentUpon>frm_SL_ItemsSales.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_ItemsSales.resx">
      <DependentUpon>frm_SL_ItemsSales.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_ItemsSalesDetails.ar-EG.resx">
      <DependentUpon>frm_SL_ItemsSalesDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_ItemsSalesDetails.resx">
      <DependentUpon>frm_SL_ItemsSalesDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_ItemsSalesTotals.ar-EG.resx">
      <DependentUpon>frm_SL_ItemsSalesTotals.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_ItemsSalesTotals.resx">
      <DependentUpon>frm_SL_ItemsSalesTotals.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_JobOrderInv.ar-EG.resx">
      <DependentUpon>frm_SL_JobOrderInv.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_JobOrderInv.resx">
      <DependentUpon>frm_SL_JobOrderInv.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_Sl_JobOrderStatus.ar-EG.resx">
      <DependentUpon>frm_Sl_JobOrderStatus.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_Sl_JobOrderStatus.resx">
      <DependentUpon>frm_Sl_JobOrderStatus.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_Profit_Loss.ar-EG.resx">
      <DependentUpon>frm_SL_Profit_Loss.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_Profit_Loss.resx">
      <DependentUpon>frm_SL_Profit_Loss.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_ReturnHeaders.ar-EG.resx">
      <DependentUpon>frm_SL_ReturnHeaders.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_ReturnHeaders.resx">
      <DependentUpon>frm_SL_ReturnHeaders.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_SalesOrderItems.ar-EG.resx">
      <DependentUpon>frm_SL_SalesOrderItems.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_SalesOrderItems.resx">
      <DependentUpon>frm_SL_SalesOrderItems.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_SalesOrderItemsAndBalance.ar-EG.resx">
      <DependentUpon>frm_SL_SalesOrderItemsAndBalance.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_SalesOrderItemsAndBalance.resx">
      <DependentUpon>frm_SL_SalesOrderItemsAndBalance.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_Warranty.ar-EG.resx">
      <DependentUpon>frm_SL_Warranty.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\frm_SL_Warranty.resx">
      <DependentUpon>frm_SL_Warranty.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\rpt_SL_CustomerSale.ar-EG.resx">
      <DependentUpon>rpt_SL_CustomerSale.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\rpt_SL_CustomerSale.resx">
      <DependentUpon>rpt_SL_CustomerSale.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\rpt_SL_CustomerTrans.ar-EG.resx">
      <DependentUpon>rpt_SL_CustomerTrans.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\rpt_SL_CustomerTrans.resx">
      <DependentUpon>rpt_SL_CustomerTrans.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\rpt_SL_CustomerTrans_Sub.resx">
      <DependentUpon>rpt_SL_CustomerTrans_Sub.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\rpt_SL_ItemTrade.ar-EG.resx">
      <DependentUpon>rpt_SL_ItemTrade.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\SL\rpt_SL_ItemTrade.resx">
      <DependentUpon>rpt_SL_ItemTrade.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\uc_Currency.ar-EG.resx">
      <DependentUpon>uc_Currency.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\uc_Currency.resx">
      <DependentUpon>uc_Currency.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\uc_CurrencyValue.ar-EG.resx">
      <DependentUpon>uc_CurrencyValue.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\uc_CurrencyValue.resx">
      <DependentUpon>uc_CurrencyValue.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{3259AA49-8AA1-44D3-9025-A0B520596A8C}" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\DalBL\DAL.csproj">
      <Project>{f862ce1a-2da2-4148-8a18-1df544113f03}</Project>
      <Name>DAL</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\McDRSigniture\McDRSigniture.csproj">
      <Project>{3e0e6265-0af6-484e-ac64-6bd4da95dc3e}</Project>
      <Name>McDRSigniture</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Models_1\Models_1.csproj">
      <Project>{4670bd8c-095d-40de-a45a-4119482c5b15}</Project>
      <Name>Models_1</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\SignuatuerGenerator\SignuatuerGenerator_.csproj">
      <Project>{0ec84433-e34f-4a40-b582-84fd80961d07}</Project>
      <Name>SignuatuerGenerator_</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Zatca\Zatca.csproj">
      <Project>{a396d132-9502-41d7-aa2f-1c2c7ffe632f}</Project>
      <Name>Zatca</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="ERP_TemporaryKey.pfx" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings2.Designer.cs</LastGenOutput>
    </None>
    <None Include="Reports\Resources\Thumbs.db" />
    <None Include="Resources\Splash.jpg" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.2.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 2.0 %28x86%29</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.0 %28x86%29</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.3.1">
      <Visible>False</Visible>
      <ProductName>Windows Installer 3.1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\list.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\open.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\add.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\prnt.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\new.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\sve.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\prev.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\nxt.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\cmt.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\edit.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\del.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\hlp.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\srch.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\cancel.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\rfrsh.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\refresh.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\clse.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Pricing.ico" />
    <None Include="Resources\packaging.ico" />
    <None Include="Resources\s-l300.ico" />
    <None Include="Resources\twitter.png" />
    <None Include="Resources\wash.ico" />
    <None Include="Resources\sort %283%29.ico" />
    <None Include="Resources\sort %281%29.ico" />
    <None Include="Resources\sort.ico" />
    <None Include="Resources\sort.jpg" />
    <None Include="Resources\starch.png" />
    <None Include="Resources\starch-spray-for-clothes.png" />
    <None Include="Resources\whitener.png" />
    <None Include="Resources\damage.png" />
    <None Include="Resources\ggIco.ico" />
    <Content Include="ggIco.ico" />
    <None Include="Resources\chart.png" />
    <None Include="Resources\16 convert.png" />
    <None Include="Resources\16 prchs.png" />
    <None Include="Resources\16 PurOrder.png" />
    <None Include="Resources\16 sales_order.png" />
    <None Include="Resources\16 sell.png" />
    <None Include="Resources\16convert.png" />
    <None Include="Resources\16_CashIn.png" />
    <None Include="Resources\16_transfer.png" />
    <None Include="Resources\16_PrOrder.png" />
    <None Include="Resources\bom2.png" />
    <None Include="Resources\52896.png" />
    <None Include="Resources\16_cv.png" />
    <None Include="Resources\16_CashOut.png" />
    <Content Include="Perpetual.dll" />
    <None Include="Resources\3.png" />
    <None Include="Resources\4-1.png" />
    <None Include="Resources\folded-1.png" />
    <None Include="Resources\3-1.png" />
    <None Include="Resources\2-1.png" />
    <None Include="Resources\4-2.png" />
    <None Include="Resources\3-2.png" />
    <None Include="Resources\2-2.png" />
    <None Include="Resources\folded-2.png" />
    <None Include="Resources\folded-21.png" />
    <None Include="Resources\folded-22.png" />
    <None Include="Resources\22.ico" />
    <None Include="Resources\delivery.ico" />
    <None Include="Resources\all.ico" />
    <None Include="Resources\facebook.png" />
    <None Include="Resources\Glue.ico" />
    <None Include="Resources\Design.ico" />
    <Content Include="Reports\Resources\16 convert.png" />
    <Content Include="Reports\Resources\16 prchs.png" />
    <Content Include="Reports\Resources\16 PurOrder.png" />
    <Content Include="Reports\Resources\16 sales_order.png" />
    <Content Include="Reports\Resources\16 sell.png" />
    <Content Include="Reports\Resources\16convert.png" />
    <Content Include="Reports\Resources\16_CashIn.png" />
    <Content Include="Reports\Resources\16_CashOut.png" />
    <Content Include="Reports\Resources\16_cv.png" />
    <Content Include="Reports\Resources\16_PrOrder.png" />
    <Content Include="Reports\Resources\16_transfer.png" />
    <Content Include="Reports\Resources\52896.png" />
    <Content Include="Reports\Resources\add.png" />
    <Content Include="Reports\Resources\bom2.png" />
    <Content Include="Reports\Resources\cancel.png" />
    <Content Include="Reports\Resources\chart.png" />
    <Content Include="Reports\Resources\clse.png" />
    <Content Include="Reports\Resources\cmt.png" />
    <Content Include="Reports\Resources\damage.png" />
    <Content Include="Reports\Resources\del.png" />
    <Content Include="Reports\Resources\edit.png" />
    <Content Include="Reports\Resources\ggIco.ico" />
    <Content Include="Reports\Resources\Header.jpg" />
    <Content Include="Reports\Resources\hlp.png" />
    <Content Include="Reports\Resources\icon.ico" />
    <Content Include="Reports\Resources\ic__.ico" />
    <Content Include="Reports\Resources\in-trns-lst.png" />
    <Content Include="Reports\Resources\in-trns.png" />
    <Content Include="Reports\Resources\in-trns16.png" />
    <Content Include="Reports\Resources\job_order.png" />
    <Content Include="Reports\Resources\list.png" />
    <Content Include="Reports\Resources\loginLogo.png" />
    <Content Include="Reports\Resources\LOGO2.png" />
    <Content Include="Reports\Resources\manf_Plan.png" />
    <Content Include="Reports\Resources\MrData.png" />
    <Content Include="Reports\Resources\mtrx.png" />
    <Content Include="Reports\Resources\new.png" />
    <Content Include="Reports\Resources\nxt.png" />
    <Content Include="Reports\Resources\N_absence2.png" />
    <Content Include="Reports\Resources\N_AccTree.png" />
    <Content Include="Reports\Resources\N_assembly.png" />
    <Content Include="Reports\Resources\N_Attend2.png" />
    <Content Include="Reports\Resources\N_balance sheet.png" />
    <Content Include="Reports\Resources\N_Bank.png" />
    <Content Include="Reports\Resources\N_barcode.png" />
    <Content Include="Reports\Resources\N_BuyList.png" />
    <Content Include="Reports\Resources\N_Cash.png" />
    <Content Include="Reports\Resources\N_cashInLst.png" />
    <Content Include="Reports\Resources\N_CashOut.png" />
    <Content Include="Reports\Resources\N_cashOutLst.png" />
    <Content Include="Reports\Resources\N_category.png" />
    <Content Include="Reports\Resources\N_Check.png" />
    <Content Include="Reports\Resources\N_CheckIn16.png" />
    <Content Include="Reports\Resources\N_checkInLst.png" />
    <Content Include="Reports\Resources\N_CheckOut.png" />
    <Content Include="Reports\Resources\N_CheckOut16.png" />
    <Content Include="Reports\Resources\N_checkOutLst.png" />
    <Content Include="Reports\Resources\N_Comp.png" />
    <Content Include="Reports\Resources\N_Company.png" />
    <Content Include="Reports\Resources\N_customers.png" />
    <Content Include="Reports\Resources\N_delay2.png" />
    <Content Include="Reports\Resources\N_Drawer.png" />
    <Content Include="Reports\Resources\N_Drawer2.png" />
    <Content Include="Reports\Resources\N_EditQty.png" />
    <Content Include="Reports\Resources\N_expenses.png" />
    <Content Include="Reports\Resources\N_Income.png" />
    <Content Include="Reports\Resources\N_items.png" />
    <Content Include="Reports\Resources\N_journal.png" />
    <Content Include="Reports\Resources\N_ManList.jpg" />
    <Content Include="Reports\Resources\N_ManList.png" />
    <Content Include="Reports\Resources\N_Manufacuring.png" />
    <Content Include="Reports\Resources\N_OpenBalance.png" />
    <Content Include="Reports\Resources\N_OverTime2.png" />
    <Content Include="Reports\Resources\N_Pay2.png" />
    <Content Include="Reports\Resources\N_prchs.png" />
    <Content Include="Reports\Resources\N_print.png" />
    <Content Include="Reports\Resources\N_Prqoute.png" />
    <Content Include="Reports\Resources\N_PrReturn.png" />
    <Content Include="Reports\Resources\N_purchase.png" />
    <Content Include="Reports\Resources\N_purchase2.png" />
    <Content Include="Reports\Resources\N_PurOrder.png" />
    <Content Include="Reports\Resources\N_PurOrderLst.png" />
    <Content Include="Reports\Resources\N_qoute.png" />
    <Content Include="Reports\Resources\N_reports.png" />
    <Content Include="Reports\Resources\N_reports2.png" />
    <Content Include="Reports\Resources\N_return1.png" />
    <Content Include="Reports\Resources\N_return2.png" />
    <Content Include="Reports\Resources\N_return3.png" />
    <Content Include="Reports\Resources\N_revenue.png" />
    <Content Include="Reports\Resources\N_sell.png" />
    <Content Include="Reports\Resources\N_SellList.png" />
    <Content Include="Reports\Resources\N_Settings.png" />
    <Content Include="Reports\Resources\N_statment.png" />
    <Content Include="Reports\Resources\N_StockTaking.png" />
    <Content Include="Reports\Resources\N_Store.png" />
    <Content Include="Reports\Resources\N_Style.png" />
    <Content Include="Reports\Resources\N_Trade.png" />
    <Content Include="Reports\Resources\N_Users.png" />
    <Content Include="Reports\Resources\N_Vac16.png" />
    <Content Include="Reports\Resources\N_Vacation2.png" />
    <Content Include="Reports\Resources\N_vendor.png" />
    <Content Include="Reports\Resources\N_vendos.png" />
    <Content Include="Reports\Resources\open.png" />
    <Content Include="Reports\Resources\out-trns-lst.png" />
    <Content Include="Reports\Resources\out-trns.png" />
    <Content Include="Reports\Resources\out-trns16.png" />
    <Content Include="Reports\Resources\pos.png" />
    <Content Include="Reports\Resources\pr price level.png" />
    <Content Include="Reports\Resources\prchs.png" />
    <Content Include="Reports\Resources\prev.png" />
    <Content Include="Reports\Resources\priceList.png" />
    <Content Include="Reports\Resources\prnt.png" />
    <Content Include="Reports\Resources\PurOrder.png" />
    <Content Include="Reports\Resources\P_return.png" />
    <Content Include="Reports\Resources\QC1.png" />
    <Content Include="Reports\Resources\QCLst1.png" />
    <Content Include="Reports\Resources\qoute.png" />
    <Content Include="Reports\Resources\refresh.png" />
    <Content Include="Reports\Resources\rfrsh.png" />
    <Content Include="Reports\Resources\salesOrderLst.png" />
    <Content Include="Reports\Resources\sales_order.png" />
    <Content Include="Reports\Resources\sell.png" />
    <Content Include="Reports\Resources\Splash-pos.jpg" />
    <Content Include="Reports\Resources\Splash-pos.png" />
    <Content Include="Reports\Resources\Splash.jpg" />
    <Content Include="Reports\Resources\Splash2.jpg" />
    <Content Include="Reports\Resources\srch.png" />
    <Content Include="Reports\Resources\sve.png" />
    <Content Include="Reports\Resources\tool16.png" />
    <Content Include="Reports\Resources\transfer-lst.png" />
    <Content Include="Reports\Resources\transfer.png" />
    <Content Include="Resources\icon.ico" />
    <Content Include="Resources\ic__.ico" />
    <None Include="Resources\N_PurOrderLst.png" />
    <None Include="Resources\N_PurOrder.png" />
    <None Include="Resources\N_ManList.png" />
    <None Include="Resources\N_cashInLst.png" />
    <None Include="Resources\N_cashOutLst.png" />
    <None Include="Resources\N_checkInLst.png" />
    <None Include="Resources\N_checkOutLst.png" />
    <None Include="Resources\in-trns16.png" />
    <None Include="Resources\job_order.png" />
    <None Include="Resources\MrData.png" />
    <None Include="Resources\N_Prqoute.png" />
    <None Include="Resources\iron.ico" />
    <None Include="Resources\New Arrival.ico" />
    <None Include="Resources\linkedin.png" />
    <Content Include="Resources\N_qoute.png" />
    <None Include="Resources\tool16.png" />
    <None Include="Resources\N_Vac16.png" />
    <None Include="Resources\QCLst1.png" />
    <None Include="Resources\QC1.png" />
    <None Include="Resources\Splash-pos.png" />
    <None Include="Resources\Splash-pos.jpg" />
    <None Include="Resources\N_return3.png" />
    <None Include="Resources\P_return.png" />
    <None Include="Resources\out-trns16.png" />
    <None Include="Resources\salesOrderLst.png" />
    <None Include="Resources\sales_order.png" />
    <None Include="Resources\pr price level.png" />
    <None Include="Resources\prchs.png" />
    <None Include="Resources\PurOrder.png" />
    <None Include="Resources\sell.png" />
    <None Include="Resources\mtrx.png" />
    <None Include="Resources\LOGO2.png" />
    <None Include="Resources\loginLogo.png" />
    <None Include="Resources\Splash2.jpg" />
    <None Include="Resources\priceList.png" />
    <None Include="Resources\N_CheckIn16.png" />
    <None Include="Resources\N_CheckOut16.png" />
    <None Include="Resources\N_absence2.png" />
    <None Include="Resources\N_Vacation2.png" />
    <None Include="Resources\N_Pay2.png" />
    <None Include="Resources\N_OverTime2.png" />
    <None Include="Resources\N_delay2.png" />
    <None Include="Resources\N_Attend2.png" />
    <None Include="Resources\N_PrReturn.png" />
    <None Include="Resources\qoute.png" />
    <None Include="Resources\pos.png" />
    <None Include="Resources\out-trns.png" />
    <None Include="Resources\transfer.png" />
    <None Include="Resources\out-trns-lst.png" />
    <None Include="Resources\in-trns-lst.png" />
    <None Include="Resources\in-trns.png" />
    <None Include="Resources\transfer-lst.png" />
    <None Include="Resources\N_ManList.jpg" />
    <None Include="Resources\N_BuyList.png" />
    <None Include="Resources\N_SellList.png" />
    <None Include="Resources\N_assembly.png" />
    <None Include="Resources\N_EditQty.png" />
    <None Include="Resources\N_OpenBalance.png" />
    <None Include="Resources\N_Trade.png" />
    <None Include="Resources\N_Style.png" />
    <None Include="Resources\N_StockTaking.png" />
    <None Include="Resources\N_statment.png" />
    <None Include="Resources\N_Income.png" />
    <None Include="Resources\N_Comp.png" />
    <None Include="Resources\N_vendos.png" />
    <None Include="Resources\N_vendor.png" />
    <None Include="Resources\N_Users.png" />
    <None Include="Resources\N_Store.png" />
    <None Include="Resources\N_Settings.png" />
    <None Include="Resources\N_sell.png" />
    <None Include="Resources\N_revenue.png" />
    <None Include="Resources\N_return2.png" />
    <None Include="Resources\N_return1.png" />
    <None Include="Resources\N_reports.png" />
    <None Include="Resources\N_reports2.png" />
    <None Include="Resources\N_purchase.png" />
    <None Include="Resources\N_purchase2.png" />
    <None Include="Resources\N_print.png" />
    <None Include="Resources\N_prchs.png" />
    <None Include="Resources\N_Manufacuring.png" />
    <None Include="Resources\N_journal.png" />
    <None Include="Resources\N_items.png" />
    <None Include="Resources\N_expenses.png" />
    <None Include="Resources\N_Drawer.png" />
    <None Include="Resources\N_Drawer2.png" />
    <None Include="Resources\N_customers.png" />
    <None Include="Resources\N_Company.png" />
    <None Include="Resources\N_CheckOut.png" />
    <None Include="Resources\N_Check.png" />
    <None Include="Resources\N_category.png" />
    <None Include="Resources\N_CashOut.png" />
    <None Include="Resources\N_Cash.png" />
    <None Include="Resources\N_barcode.png" />
    <None Include="Resources\N_Bank.png" />
    <None Include="Resources\N_balance sheet.png" />
    <None Include="Resources\N_AccTree.png" />
  </ItemGroup>
  <ItemGroup>
    <PublishFile Include="ar-EG\ERP.resources">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Prerequisite</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Satellite</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.BonusSkins.v15.1">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.Charts.v15.1.Core">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.CodeParser.v15.1">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.Data.v15.1">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.DataAccess.v15.1">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.DataAccess.v15.1.UI">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.Office.v15.1.Core">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.PivotGrid.v15.1.Core">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.Printing.v15.1.Core">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.RichEdit.v15.1.Core">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.Sparkline.v15.1.Core">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.Utils.v15.1">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.Utils.v15.1.UI">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.Xpo.v15.1">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.XtraBars.v15.1">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.XtraCharts.v15.1">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.XtraCharts.v15.1.Extensions">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.XtraCharts.v15.1.UI">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.XtraCharts.v15.1.Wizard">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.XtraEditors.v15.1">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.XtraGauges.v15.1.Core">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.XtraGrid.v15.1">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.XtraLayout.v15.1">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.XtraNavBar.v15.1">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.XtraPivotGrid.v15.1">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.XtraPrinting.v15.1">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.XtraReports.v15.1">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.XtraReports.v15.1.Extensions">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.XtraRichEdit.v15.1">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.XtraRichEdit.v15.1.Extensions">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.XtraTreeList.v15.1">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.XtraVerticalGrid.v15.1">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <PropertyGroup>
    <PostBuildEvent>
    </PostBuildEvent>
  </PropertyGroup>
</Project>