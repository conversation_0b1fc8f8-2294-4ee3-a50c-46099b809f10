using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using System.Linq;
using System.Data;
using DAL;

namespace Reports
{
    public partial class rpt_CashTransfer : DevExpress.XtraReports.UI.XtraReport
    {
        string code, RegDate, Amount, Account1, Account2, Notes, TotalWords,  userName;

        public rpt_CashTransfer()
        {
            InitializeComponent();
        }
        public rpt_CashTransfer(string _Code, string _RegDate, string _Amount,
            string _Account1, string _Account2, string _Notes, string _TotalWords, string userName)
        {
            InitializeComponent();
           // getReportHeader();

            this.code = _Code;
            this.RegDate = _RegDate;
            this.Amount = _Amount;
            this.Account1 = _Account1;
            this.Account2 = _Account2;
            this.Notes = _Notes;
            this.TotalWords = _TotalWords;
            this.userName = userName;
            getReportHeader();
        }

        public void LoadData()
        {
            lbl_Serial.Text = code;
            lbl_RegDate.Text = RegDate;
            lbl_Amount.Text = Amount;
            lbl_Drawer.Text = Account1;
            lbl_Account.Text = Account2;
            lbl_Notes.Text = Notes;
            lblTotalWords.Text = TotalWords;
            lbl_User.Text = userName;
            getReportHeader();
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                lblCompName.Text = comp.CmpNameAr;
                txt_tel.Text = comp.CmpTel;
                txt_mobile.Text = comp.CmpMobile;
                lbl_compNameEn.Text = comp.CmpNameEn;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }     
    }
}
