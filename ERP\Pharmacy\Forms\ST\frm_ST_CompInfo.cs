﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;

namespace Pharmacy.Forms
{
    public partial class frm_ST_CompInfo : DevExpress.XtraEditors.XtraForm
    {
        bool DataModified;
        public frm_ST_CompInfo()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
        }

        private void frm_ST_PharmacyInfo_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            Load_Pharmacy_Data();
        }

        private void frm_ST_PharmacyInfo_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                e.Cancel = true;
        }


        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Save_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            SaveData();
        }

        private void btnLoadLogo_Click(object sender, EventArgs e)
        {
            OpenFileDialog OFD = new OpenFileDialog();
            OFD.Filter = "Images|*.jpg;*.jpeg;*.png;*.bmp;*.gif";
            if (OFD.ShowDialog() == DialogResult.OK && OFD.FileName != string.Empty)
            {
                Image srcImage = null;
                try
                {
                    srcImage = Image.FromFile(OFD.FileName);
                }
                catch
                {

                }
                picLogo.Image = srcImage;
                DataModified = true;
            }
            else
                return;
        }

        private void btnRemovePic_Click(object sender, EventArgs e)
        {
            picLogo.Image = null;
            DataModified = true;
        }

        private void txtCompanyNameAr_Modified(object sender, EventArgs e)
        {
            DataModified = true;

        }


        private void SaveData()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            bool Is_New = false;
            var compInfo = (from d in DB.ST_CompanyInfos
                                 select d).FirstOrDefault();

            if (compInfo == null)
            {
                compInfo = new ST_CompanyInfo();
                Is_New = true;
            }
            try
            {
                compInfo.CmpNameAr = txtCompanyNameAr.Text;
                compInfo.CmpNameEn = txtCompanyNameEn.Text;
                compInfo.CmpAddress = txtAddress.Text;
                compInfo.Iban = txtIban.Text;
                compInfo.CmpCity = txtCity.Text;
                compInfo.CmpCountry = txtCountry.Text;
                compInfo.CmpTel = txtTel.Text;
                compInfo.CmpMobile = txtMobile.Text;

                if (picLogo.Image != null)
                    compInfo.Logo = ErpUtils.imageToByteArray(picLogo.Image);
                else if (picLogo.Image == null)
                    compInfo.Logo = null;

                compInfo.CommercialBook = txtCommercialBook.Text;
                compInfo.TaxCard = txtTaxCard.Text;
                compInfo.ActivityType = txtActivityType.Text;
                compInfo.MngrName = txtMngrName.Text;
                compInfo.MngrAdress = txtMngrAddress.Text;
                compInfo.MngrMobile = txtMngrMobile.Text;
                compInfo.MngrTel = txtMngrTel.Text;

                if (dtFiscalYearStartDate.EditValue != null)
                    compInfo.FiscalYearStartDate = dtFiscalYearStartDate.DateTime.Date;
                else
                    compInfo.FiscalYearStartDate = null;

                if (dtFiscalYearEndDate.EditValue != null)
                    compInfo.FiscalYearEndDate = dtFiscalYearEndDate.DateTime.Date.AddDays(1).AddSeconds(-1);
                else
                    compInfo.FiscalYearEndDate = null;


                if (Is_New == true)
                    DB.ST_CompanyInfos.InsertOnSubmit(compInfo);

                MyHelper.UpdateST_UserLog(DB, "", "",
(int)FormAction.Edit, (int)FormsNames.ST_CompInfo);

                DB.SubmitChanges();
                Shared.CompName = compInfo.CmpNameAr;

                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResAccEn.MsgSave : ResAccAr.MsgSave//"تم الحفظ بنجاح"
                    , "", MessageBoxButtons.OK, MessageBoxIcon.Information);

                DoValidate();
                DataModified = false;
            }
            catch(Exception xxx)
            {
                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResAccEn.MsgIncorrectData : ResAccAr.MsgIncorrectData//"تأكد من صحة البيانات"                    
                    , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void Load_Pharmacy_Data()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var compData = (from d in DB.ST_CompanyInfos
                                 select d).FirstOrDefault();

            if (compData == null)
                return;

            txtCompanyNameAr.Text = compData.CmpNameAr;
            txtCompanyNameEn.Text = compData.CmpNameEn;
            txtAddress.Text = compData.CmpAddress;
            txtIban.Text = compData.Iban;
            txtCity.Text = compData.CmpCity;
            txtCountry.Text = compData.CmpCountry;
            txtTel.Text = compData.CmpTel;
            txtMobile.Text = compData.CmpMobile;

            if (compData.Logo != null)
                picLogo.Image = ErpUtils.byteArrayToImage(compData.Logo.ToArray());
            txtCommercialBook.Text = compData.CommercialBook;
            txtTaxCard.Text = compData.TaxCard;
            txtActivityType.Text = compData.ActivityType;
            txtMngrName.Text = compData.MngrName;
            txtMngrAddress.Text = compData.MngrAdress;
            txtMngrMobile.Text = compData.MngrMobile;
            txtMngrTel.Text = compData.MngrTel;

            if (compData.FiscalYearStartDate != null)
                dtFiscalYearStartDate.EditValue = compData.FiscalYearStartDate.Value;
            else
                dtFiscalYearStartDate.EditValue = null;

            if (compData.FiscalYearEndDate != null)
                dtFiscalYearEndDate.EditValue = compData.FiscalYearEndDate.Value;
            else
                dtFiscalYearEndDate.EditValue = null;
        }


        DialogResult ChangesMade()
        {

            if ( DataModified)
            {
                DialogResult r = XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResAccEn.MsgDataModified : ResAccAr.MsgDataModified//"لقد قمت بعمل بعض التغييرات, هل تريد الحفظ أولا "                    
                    , "", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                if (r == DialogResult.Yes)
                {                
                    SaveData();
                    return r;
                }
                else if (r == DialogResult.No)
                {
                    // no thing made, continue closing or do next or do previous
                    return r;
                }
                else if (r == DialogResult.Cancel)
                {
                    //cancel form action
                    return r;
                }
            }
            return DialogResult.No;
        }

        void DoValidate()
        {
            txtAddress.DoValidate();
            txtCity.DoValidate();
            txtCommercialBook.DoValidate();
            txtCompanyNameAr.DoValidate();
            txtCompanyNameEn.DoValidate();
            txtCountry.DoValidate();
            txtMngrAddress.DoValidate();
            txtMngrMobile.DoValidate();
            txtMngrName.DoValidate();
            txtMngrTel.DoValidate();
            txtMobile.DoValidate();
            txtTaxCard.DoValidate();
            txtActivityType.DoValidate();
            txtTel.DoValidate();
            picLogo.DoValidate();
            dtFiscalYearEndDate.DoValidate();
            dtFiscalYearStartDate.DoValidate();
        }

        private void barBtn_Help_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "بيانات المنشأة");
        }

    }
}