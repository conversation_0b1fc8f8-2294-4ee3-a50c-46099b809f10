﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;

using DevExpress.XtraReports.UI;

namespace Reports
{
    public partial class frm_SL_ItemsSalesDetails : DevExpress.XtraEditors.XtraForm
    {
        public bool UserCanOpen;
        string reportName, dateFilter, otherFilters;

        int itemId1, itemId2, storeId1, storeId2;
        byte FltrTyp_item, fltrTyp_Date, FltrTyp_Customer, FltrTyp_Category, FltrTyp_InvBook, FltrTyp_Store;
        DateTime date1, date2;

        int customerId1, customerId2, custGroupId, salesEmpId, EmpGroupId;
        string categoryNum;
        string custGroupAccNumber;

        byte FltrTyp_Company;
        int companyId;

        bool UsingTaxRatio = false;

        List<int> lstStores = new List<int>();
        List<int> lst_invBooksId = new List<int>();

        DataTable dt = new DataTable();

        public frm_SL_ItemsSalesDetails(
            string reportName, string dateFilter, string otherFilters,
            byte fltrTyp_item, int itemId1, int itemId2,
            byte fltrTyp_Date, DateTime date1, DateTime date2,
            byte FltrTyp_Customer, int customerId1, int customerId2,
            int custGroupId, string custGroupAccNumber,
            byte FltrTyp_Category, string categoryNum, int salesEmpId,
            byte FltrTyp_InvBook, string InvBooks, int EmpGroupId, bool UsingTaxRatio,
            byte _fltrTyp_Store, int _storeId1, int _storeId2, byte FltrTyp_Company, int companyId)
        {
            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            ReportsRTL.RTL_BarManager(this.barManager1);

            this.reportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;

            this.FltrTyp_Company = FltrTyp_Company;
            this.companyId = companyId;

            this.FltrTyp_item = fltrTyp_item;
            this.fltrTyp_Date = fltrTyp_Date;
            this.FltrTyp_Customer = FltrTyp_Customer;
            this.FltrTyp_Category = FltrTyp_Category;
            this.FltrTyp_Store = _fltrTyp_Store;

            this.itemId1 = itemId1;
            this.itemId2 = itemId2;
            this.date1 = date1;
            this.date2 = date2;
            this.customerId1 = customerId1;
            this.customerId2 = customerId2;
            this.custGroupId = custGroupId;
            this.custGroupAccNumber = custGroupAccNumber;

            this.categoryNum = categoryNum;
            this.salesEmpId = salesEmpId;
            this.EmpGroupId = EmpGroupId;

            this.UsingTaxRatio = UsingTaxRatio;

            this.FltrTyp_InvBook = FltrTyp_InvBook;
            Utilities.Get_ChkLst_Items(InvBooks, lst_invBooksId);

            this.storeId1 = _storeId1;
            this.storeId2 = _storeId2;

            #region Init_DataTable
            dt.Columns.Add("ItemCode1");
            dt.Columns.Add("ItemCode2");
            dt.Columns.Add("ItemNameAr");
            dt.Columns.Add("SalesTaxRatio", typeof(decimal));
            dt.Columns.Add("SoldQty", typeof(decimal));
            dt.Columns.Add("SoldBonus", typeof(decimal));
            dt.Columns.Add("SoldAmount", typeof(decimal));
            dt.Columns.Add("SoldTotalPrice", typeof(decimal));
            dt.Columns.Add("SoldTotalSalesTaxValue", typeof(decimal));
            dt.Columns.Add("ReturnQty", typeof(decimal));
            dt.Columns.Add("ReturnBonus", typeof(decimal));
            dt.Columns.Add("ReturnAmount", typeof(decimal));
            dt.Columns.Add("ReturnTotalPrice", typeof(decimal));
            dt.Columns.Add("ReturnTotalSalesTaxValue", typeof(decimal));
            dt.Columns.Add("NetQty", typeof(decimal));
            dt.Columns.Add("NetBonus", typeof(decimal));
            dt.Columns.Add("NetAmount", typeof(decimal));
            dt.Columns.Add("NetTotalPrice", typeof(decimal));
            dt.Columns.Add("NetTotalSalesTaxValue", typeof(decimal));
            dt.Columns.Add("NetAmountPlusNetTax", typeof(decimal));

            dt.Columns.Add("ExtraBonus", typeof(decimal));
            dt.Columns.Add("TotalBonus", typeof(decimal));
            dt.Columns.Add("BonusRatio", typeof(decimal));
            dt.Columns.Add("TotalBonusRatio", typeof(decimal));
            dt.Columns.Add("NetTotalPrice_Local", typeof(decimal));
            dt.Columns.Add("SellCrncRate", typeof(decimal));
            dt.Columns.Add("ReturnCrncRate", typeof(decimal));
            dt.Columns.Add("SellCrncId", typeof(decimal));
            dt.Columns.Add("ReturnCrncId", typeof(decimal));
            dt.Columns.Add("SLR_EmpName");
            dt.Columns.Add("SL_EmpName");

            dt.Columns.Add("SLR_CustCatName");
            dt.Columns.Add("SL_CustCatName");
            dt.Columns.Add("Category");

            dt.Columns.Add("SoldLibraQty");
            dt.Columns.Add("SoldkgWeight");
            dt.Columns.Add("ReturnLibraQty");
            dt.Columns.Add("ReturnkgWeight");

            dt.Columns.Add("NetPiecesCount");
            dt.Columns.Add("ReturnPiecesCount");
            dt.Columns.Add("SoldPiecesCount");


            #endregion

            getReportHeader();
            LoadData();
            ReportsUtils.ColumnChooser(grdCategory);
        }


        private void frm_SL_InvoiceList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                ReportsRTL.LTRLayout(this);


            if (Shared.st_Store.UseLengthDimension)
                col_length.Visible = true;
            if (Shared.st_Store.UseHeightDimension)
                col_height.Visible = true;
            if (Shared.st_Store.UseWidthDimension)
                col_width.Visible = true;

            col_SoldPiecesCount.Caption = Shared.IsEnglish ? Shared.st_Store.PiecesCountNameEn : Shared.st_Store.PiecesCountNameAr;
            col_ReturnPiecesCount.Caption = Shared.IsEnglish ? Shared.st_Store.PiecesCountNameEn : Shared.st_Store.PiecesCountNameAr;
            col_NetPiecesCount.Caption = Shared.IsEnglish ? Shared.st_Store.PiecesCountNameEn : Shared.st_Store.PiecesCountNameAr;

            ReportsUtils.Load_Grid_Layout(grdCategory, this.Name.Replace("frm_", ""));
        }

        private void frm_Rep_FormClosing(object sender, FormClosingEventArgs e)
        {
            ReportsUtils.save_Grid_Layout(grdCategory, this.Name.Replace("frm_", ""), true);
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Preview_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCategory, true).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        private void barBtnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadData();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grdCategory.MinimumSize = grdCategory.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grdCategory, true, true).ShowPreview();
                grdCategory.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                //lblCompName.Text = comp.CmpNameAr;
                lblReportName.Text = this.Text = reportName;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        void LoadData()
        {
            dt.Rows.Clear();

            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;

            ERPDataContext DB = new ERPDataContext();

            var stores = DB.IC_Stores.ToList();
            foreach (var store in stores)
            {
                if (FltrTyp_Store == 2)
                {
                    if (store.StoreId <= storeId2 && store.StoreId >= storeId1)
                    {
                        lstStores.Add(store.StoreId);
                    }
                }
                else if (FltrTyp_Store == 0)
                {
                    lstStores.Add(store.StoreId);
                }
                else if (storeId1 > 0 && (store.StoreId == storeId1 || store.ParentId == storeId1))
                    lstStores.Add(store.StoreId);
                //else if (store_id2 > 0 && (store.StoreId == store_id2 || store.ParentId == store_id2))
                //    lstStores.Add(store.StoreId);
            }
            var defaultCategories = DB.IC_User_Categories.Where(a => a.UserId == Shared.UserId).Select(a => a.CategoryId).ToList();
            var sales_data = (
                from c in DB.SL_Customers
                join a in DB.ACC_Accounts
                        on c.AccountId equals a.AccountId
                where custGroupId == 0 ? true : a.AcNumber.StartsWith(custGroupAccNumber)

                where FltrTyp_Customer == 1 ? c.CustomerId == customerId1 : true
                where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 != 0) ?
                c.CustomerId >= customerId1 && c.CustomerId <= customerId2 : true
                where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 == 0) ?
                c.CustomerId >= customerId1 : true
                where (FltrTyp_Customer == 2 && customerId1 == 0 && customerId2 != 0) ?
                c.CustomerId <= customerId2 : true

                join i in DB.SL_Invoices on c.CustomerId equals i.CustomerId
                where lstStores.Count > 0 ? lstStores.Contains(i.StoreId) : true
                where salesEmpId == 0 ? true : i.SalesEmpId == salesEmpId

                where FltrTyp_InvBook == 0 ? true : (i.InvoiceBookId.HasValue && lst_invBooksId.Contains(i.InvoiceBookId.Value))

                where fltrTyp_Date == 1 ? i.InvoiceDate.Date == date1 : true
                where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                i.InvoiceDate >= date1 && i.InvoiceDate <= date2 : true
                where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                i.InvoiceDate >= date1 : true
                where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                i.InvoiceDate <= date2 : true

                join hh in DB.HR_Employees on i.SalesEmpId equals hh.EmpId into hq
                from xx in hq.DefaultIfEmpty().Where(x => EmpGroupId == 0 ? true : x.GroupId == EmpGroupId)


                join s in DB.SL_InvoiceDetails on i.SL_InvoiceId equals s.SL_InvoiceId

                where FltrTyp_item == 1 ? s.ItemId == itemId1 : true
                where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 != 0) ?
                s.ItemId >= itemId1 && s.ItemId <= itemId2 : true
                where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 == 0) ?
                s.ItemId >= itemId1 : true
                where (FltrTyp_item == 2 && itemId1 == 0 && itemId2 != 0) ?
                s.ItemId <= itemId2 : true
                let TotalInvoice = s.SL_Invoice.Net - s.SL_Invoice.TaxValue + s.SL_Invoice.DiscountValue - s.SL_Invoice.Expenses
                                 + s.SL_Invoice.DeductTaxValue - s.SL_Invoice.AddTaxValue

                join t in DB.IC_Items on s.ItemId equals t.ItemId
                where FltrTyp_Company == 1 ? t.Company == companyId : true

                join g in DB.IC_Categories on t.Category equals g.CategoryId
                where defaultCategories.Count() > 0 ? defaultCategories.Contains(g.CategoryId) : true
                where FltrTyp_Category == 1 ? g.CatNumber.StartsWith(categoryNum) : true

                select new
                {
                    t.ItemCode1,
                    t.ItemCode2,
                    t.ItemNameAr,
                    s.UOMIndex,
                    t.MediumUOMFactor,
                    t.LargeUOMFactor,
                    s.ItemId,
                    t.is_libra,
                    SoldPiecesCount = s.PiecesCount,
                    Qty = s.SellPrice > 0 ? s.Qty : 0,
                    LibraQty =  t.is_libra == true ? s.LibraQty : null,
                    kg_Weight_libra = s.kg_Weight_libra,
                    s.TotalSellPrice,
                    TotalPrice = s.SL_Invoice.Net == 0 ? 0 : (s.TotalSellPrice / TotalInvoice) * s.SL_Invoice.Net,
                    Bonus = s.SellPrice > 0 ? 0 : s.Qty,
                    SalesTaxRatio = UsingTaxRatio ? s.SalesTaxRatio : (t.SalesTaxRatio / 100),
                    s.SalesTax,

                    //mohammad 13-09-2018
                    CrncId = s.SL_Invoice.CrncId,
                    CrncRate = s.SL_Invoice.CrncRate,
                    TotalSellPrice_Local = s.TotalSellPrice * s.SL_Invoice.CrncRate,
                    Category = Shared.IsEnglish ? t.IC_Category.CategoryNameEn : t.IC_Category.CategoryNameAr,
                    t.Height,
                    t.Length,
                    t.Width
                }).ToList();

            var slReturn_data = (
                                from c in DB.SL_Customers
                                join a in DB.ACC_Accounts
                        on c.AccountId equals a.AccountId
                                where custGroupId == 0 ? true : a.AcNumber.StartsWith(custGroupAccNumber)

                                where FltrTyp_Customer == 1 ? c.CustomerId == customerId1 : true
                                where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 != 0) ?
                                c.CustomerId >= customerId1 && c.CustomerId <= customerId2 : true
                                where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 == 0) ?
                                c.CustomerId >= customerId1 : true
                                where (FltrTyp_Customer == 2 && customerId1 == 0 && customerId2 != 0) ?
                                c.CustomerId <= customerId2 : true

                                join i in DB.SL_Returns on c.CustomerId equals i.CustomerId
                                where lstStores.Count > 0 ? lstStores.Contains(i.StoreId) : true
                                where salesEmpId == 0 ? true : i.SalesEmpId == salesEmpId

                                where FltrTyp_InvBook == 0 ? true : (i.InvoiceBookId.HasValue && lst_invBooksId.Contains(i.InvoiceBookId.Value))

                                where fltrTyp_Date == 1 ? i.ReturnDate.Date == date1 : true
                                where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                                i.ReturnDate >= date1 && i.ReturnDate <= date2 : true
                                where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                                i.ReturnDate >= date1 : true
                                where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                                i.ReturnDate <= date2 : true

                                join hh in DB.HR_Employees on i.SalesEmpId equals hh.EmpId into hq
                                from xx in hq.DefaultIfEmpty().Where(x => EmpGroupId == 0 ? true : x.GroupId == EmpGroupId)


                                join s in DB.SL_ReturnDetails on i.SL_ReturnId equals s.SL_ReturnId

                                where FltrTyp_item == 1 ? s.ItemId == itemId1 : true
                                where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 != 0) ?
                                s.ItemId >= itemId1 && s.ItemId <= itemId2 : true
                                where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 == 0) ?
                                s.ItemId >= itemId1 : true
                                where (FltrTyp_item == 2 && itemId1 == 0 && itemId2 != 0) ?
                                s.ItemId <= itemId2 : true

                                let TotalInvoice = s.SL_Return.Net - s.SL_Return.TaxValue + s.SL_Return.DiscountValue + s.SL_Return.Expenses
                                           + s.SL_Return.DeductTaxValue - s.SL_Return.AddTaxValue

                                join t in DB.IC_Items on s.ItemId equals t.ItemId
                                where FltrTyp_Company == 1 ? t.Company == companyId : true

                                join g in DB.IC_Categories on t.Category equals g.CategoryId
                                where FltrTyp_Category == 1 ? g.CatNumber.StartsWith(categoryNum) : true
                                where defaultCategories.Count() > 0 ? defaultCategories.Contains(g.CategoryId) : true
                                select new
                                {
                                    t.ItemCode1,
                                    t.ItemCode2,
                                    t.ItemNameAr,
                                    s.UOMIndex,
                                    t.MediumUOMFactor,
                                    t.LargeUOMFactor,
                                    s.ItemId,
                                    t.is_libra,
                                    ReturnPiecesCount = s.PiecesCount,
                                    Qty = s.SellPrice > 0 ? s.Qty : 0,
                                    LibraQty = t.is_libra == true ? s.LibraQty : null,
                                    kg_Weight_libra = s.kg_Weight_libra,
                                    s.TotalSellPrice,
                                    TotalPrice = s.SL_Return.Net == 0 ? 0 : (s.TotalSellPrice / TotalInvoice) * s.SL_Return.Net,
                                    Bonus = s.SellPrice > 0 ? 0 : s.Qty,
                                    SalesTaxRatio = UsingTaxRatio ? s.SalesTaxRatio : (t.SalesTaxRatio / 100),
                                    s.SalesTax,

                                    //mohammad 13-09-2018
                                    CrncId = s.SL_Return.CrncId,
                                    CrncRate = s.SL_Return.CrncRate,
                                    TotalSellPrice_Local = s.TotalSellPrice * s.SL_Return.CrncRate,
                                    Category = Shared.IsEnglish ? t.IC_Category.CategoryNameEn : t.IC_Category.CategoryNameAr,
                                    t.Height,
                                    t.Length,
                                    t.Width
                                }).ToList();

            var ic_Outtrns_data = (                         //EXTRA BONUS
                                from c in DB.SL_Customers
                                join a in DB.ACC_Accounts
                on c.AccountId equals a.AccountId
                                where custGroupId == 0 ? true : a.AcNumber.StartsWith(custGroupAccNumber)

                                where FltrTyp_Customer == 1 ? c.CustomerId == customerId1 : true
                                where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 != 0) ?
                                c.CustomerId >= customerId1 && c.CustomerId <= customerId2 : true
                                where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 == 0) ?
                                c.CustomerId >= customerId1 : true
                                where (FltrTyp_Customer == 2 && customerId1 == 0 && customerId2 != 0) ?
                                c.CustomerId <= customerId2 : true

                                join i in DB.IC_OutTrns on c.CustomerId equals i.CustomerId
                                where i.IsVendor == (byte)IsVendor.Customer
                                where lstStores.Count > 0 ? lstStores.Contains(i.StoreId) : true
                                where fltrTyp_Date == 1 ? i.OutTrnsDate.Date == date1 : true
                                where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                                i.OutTrnsDate >= date1 && i.OutTrnsDate <= date2 : true
                                where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                                i.OutTrnsDate >= date1 : true
                                where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                                i.OutTrnsDate <= date2 : true

                                join s in DB.IC_OutTrnsDetails on i.OutTrnsId equals s.OutTrnsId

                                where FltrTyp_item == 1 ? s.ItemId == itemId1 : true
                                where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 != 0) ?
                                s.ItemId >= itemId1 && s.ItemId <= itemId2 : true
                                where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 == 0) ?
                                s.ItemId >= itemId1 : true
                                where (FltrTyp_item == 2 && itemId1 == 0 && itemId2 != 0) ?
                                s.ItemId <= itemId2 : true

                                join t in DB.IC_Items on s.ItemId equals t.ItemId
                                where FltrTyp_Company == 1 ? t.Company == companyId : true

                                join g in DB.IC_Categories on t.Category equals g.CategoryId
                                where defaultCategories.Count() > 0 ? defaultCategories.Contains(g.CategoryId) : true
                                where FltrTyp_Category == 1 ? g.CatNumber.StartsWith(categoryNum) : true

                                select new
                                {
                                    t.ItemCode1,
                                    t.ItemCode2,
                                    t.ItemNameAr,
                                    s.UOMIndex,
                                    t.MediumUOMFactor,
                                    t.LargeUOMFactor,
                                    s.ItemId,
                                    t.is_libra,
                                    ExtraBonus = s.Qty,
                                    SalesTaxRatio = (t.SalesTaxRatio / 100),
                                    Category = Shared.IsEnglish ? t.IC_Category.CategoryNameEn : t.IC_Category.CategoryNameAr,
                                    t.Height,
                                    t.Length,
                                    t.Width
                                }).ToList();

            var lst_Items = sales_data.Select(x => new { x.ItemId, x.ItemCode1, x.ItemCode2, x.ItemNameAr, x.MediumUOMFactor, x.LargeUOMFactor, x.SalesTaxRatio, x.Category,
                x.Height, x.Length, x.Width, x.is_libra
            })
                .Union(slReturn_data.Select(x => new { x.ItemId, x.ItemCode1, x.ItemCode2, x.ItemNameAr, x.MediumUOMFactor, x.LargeUOMFactor, x.SalesTaxRatio, x.Category,
                    x.Height, x.Length, x.Width, x.is_libra
                }))
                .Union(ic_Outtrns_data.Select(x => new { x.ItemId, x.ItemCode1, x.ItemCode2, x.ItemNameAr, x.MediumUOMFactor, x.LargeUOMFactor, x.SalesTaxRatio, x.Category,
                    x.Height, x.Length, x.Width, x.is_libra
                }))
                .Distinct().OrderBy(x => x.ItemCode1).ToList();

            rep_Currency.DataSource = Shared.lstCurrency;
            rep_Currency.ValueMember = "CrncId";
            rep_Currency.DisplayMember = "crncName";

            foreach (var t in lst_Items.Distinct())
            {
                decimal MUOM_Factor = MyHelper.FractionToDouble(t.MediumUOMFactor);
                decimal LUOM_Factor = MyHelper.FractionToDouble(t.LargeUOMFactor);

                DataRow dr = dt.NewRow();
                dr["ItemCode1"] = t.ItemCode1;
                dr["ItemCode2"] = t.ItemCode2;
                dr["ItemNameAr"] = t.ItemNameAr;
                dr["Category"] = t.Category;


                dr["SalesTaxRatio"] = t.SalesTaxRatio;

                dr["SoldPiecesCount"] = (double)sales_data.Where(x => x.ItemId == t.ItemId && x.SalesTaxRatio == t.SalesTaxRatio).Select(x => x.SoldPiecesCount).DefaultIfEmpty(0).Sum();
                dr["ReturnPiecesCount"] = (double)slReturn_data.Where(x => x.ItemId == t.ItemId && x.SalesTaxRatio == t.SalesTaxRatio).Select(x => x.ReturnPiecesCount).DefaultIfEmpty(0).Sum();
                dr["NetPiecesCount"] = Convert.ToDouble(dr["SoldPiecesCount"]) - Convert.ToDouble(dr["ReturnPiecesCount"]);

                dr["SoldLibraQty"] = t.is_libra == true ? sales_data.Where(x => x.ItemId == t.ItemId && x.SalesTaxRatio == t.SalesTaxRatio).Select(x => x.LibraQty).DefaultIfEmpty(0).Sum() : null;
                dr["SoldkgWeight"] = (double)sales_data.Where(x => x.ItemId == t.ItemId && x.SalesTaxRatio == t.SalesTaxRatio).Select(x => x.kg_Weight_libra).DefaultIfEmpty(0).Sum();
                dr["SoldQty"] = sales_data.Where(x => x.ItemId == t.ItemId && x.SalesTaxRatio == t.SalesTaxRatio).Select(x => MyHelper.CalculateUomQty(x.Qty, x.UOMIndex, MUOM_Factor, LUOM_Factor)).DefaultIfEmpty(0).Sum();
                dr["SoldBonus"] = sales_data.Where(x => x.ItemId == t.ItemId && x.SalesTaxRatio == t.SalesTaxRatio).Select(x => MyHelper.CalculateUomQty(x.Bonus, x.UOMIndex, MUOM_Factor, LUOM_Factor)).DefaultIfEmpty(0).Sum();
                dr["SoldAmount"] = sales_data.Where(x => x.ItemId == t.ItemId && x.SalesTaxRatio == t.SalesTaxRatio).Select(x => x.TotalSellPrice).DefaultIfEmpty(0).Sum();
                dr["SoldTotalPrice"] = sales_data.Where(x => x.ItemId == t.ItemId && x.SalesTaxRatio == t.SalesTaxRatio).Select(x => x.TotalPrice).DefaultIfEmpty(0).Sum();
                dr["SoldTotalSalesTaxValue"] = sales_data.Where(x => x.ItemId == t.ItemId && x.SalesTaxRatio == t.SalesTaxRatio).Select(x => x.SalesTax).DefaultIfEmpty(0).Sum();

                dr["ReturnLibraQty"] = t.is_libra == true ? slReturn_data.Where(x => x.ItemId == t.ItemId && x.SalesTaxRatio == t.SalesTaxRatio).Select(x => x.LibraQty).DefaultIfEmpty(0).Sum() : null;
                dr["ReturnkgWeight"] = (double)slReturn_data.Where(x => x.ItemId == t.ItemId && x.SalesTaxRatio == t.SalesTaxRatio).Select(x => x.kg_Weight_libra).DefaultIfEmpty(0).Sum();
                dr["ReturnQty"] = slReturn_data.Where(x => x.ItemId == t.ItemId && x.SalesTaxRatio == t.SalesTaxRatio).Select(x => MyHelper.CalculateUomQty(x.Qty, x.UOMIndex, MUOM_Factor, LUOM_Factor)).DefaultIfEmpty(0).Sum();
                dr["ReturnBonus"] = slReturn_data.Where(x => x.ItemId == t.ItemId && x.SalesTaxRatio == t.SalesTaxRatio).Select(x => MyHelper.CalculateUomQty(x.Bonus, x.UOMIndex, MUOM_Factor, LUOM_Factor)).DefaultIfEmpty(0).Sum();
                dr["ReturnAmount"] = slReturn_data.Where(x => x.ItemId == t.ItemId && x.SalesTaxRatio == t.SalesTaxRatio).Select(x => x.TotalSellPrice).DefaultIfEmpty(0).Sum();
                dr["ReturnTotalPrice"] = slReturn_data.Where(x => x.ItemId == t.ItemId && x.SalesTaxRatio == t.SalesTaxRatio).Select(x => x.TotalPrice).DefaultIfEmpty(0).Sum();
                dr["ReturnTotalSalesTaxValue"] = slReturn_data.Where(x => x.ItemId == t.ItemId && x.SalesTaxRatio == t.SalesTaxRatio).Select(x => x.SalesTax).DefaultIfEmpty(0).Sum();

                dr["NetQty"] = Convert.ToDecimal(dr["SoldQty"]) - Convert.ToDecimal(dr["ReturnQty"]);
                dr["NetBonus"] = Convert.ToDecimal(dr["SoldBonus"]) - Convert.ToDecimal(dr["ReturnBonus"]);
                dr["NetAmount"] = Convert.ToDecimal(dr["SoldAmount"]) - Convert.ToDecimal(dr["ReturnAmount"]);
                dr["NetTotalPrice"] = Convert.ToDecimal(dr["SoldTotalPrice"]) - Convert.ToDecimal(dr["ReturnTotalPrice"]);
                dr["NetTotalSalesTaxValue"] = Convert.ToDecimal(dr["SoldTotalSalesTaxValue"]) - Convert.ToDecimal(dr["ReturnTotalSalesTaxValue"]);
                dr["NetAmountPlusNetTax"] = Convert.ToDecimal(dr["NetAmount"]) + Convert.ToDecimal(dr["NetTotalSalesTaxValue"]);

                dr["ExtraBonus"] = ic_Outtrns_data.Where(x => x.ItemId == t.ItemId).Select(x => MyHelper.CalculateUomQty(x.ExtraBonus, x.UOMIndex, MUOM_Factor, LUOM_Factor)).DefaultIfEmpty(0).Sum();
                dr["TotalBonus"] = Convert.ToDecimal(dr["NetBonus"]) + Convert.ToDecimal(dr["ExtraBonus"]);

                if (Convert.ToDecimal(dr["NetQty"]) > 0)
                {
                    dr["BonusRatio"] = Convert.ToDecimal(dr["NetBonus"]) / Convert.ToDecimal(dr["NetQty"]);
                    dr["TotalBonusRatio"] = Convert.ToDecimal(dr["TotalBonus"]) / Convert.ToDecimal(dr["NetQty"]);
                }

                dr["SellCrncRate"] = sales_data.Where(x => x.ItemId == t.ItemId).Select(x => x.CrncRate).FirstOrDefault();
                dr["ReturnCrncRate"] = slReturn_data.Where(x => x.ItemId == t.ItemId).Select(x => x.CrncRate).FirstOrDefault();

                dr["SellCrncId"] = sales_data.Where(x => x.ItemId == t.ItemId).Select(x => x.CrncId).FirstOrDefault();
                dr["ReturnCrncId"] = slReturn_data.Where(x => x.ItemId == t.ItemId).Select(x => x.CrncId).FirstOrDefault();

                dr["NetTotalPrice_Local"] =Math.Round( (Convert.ToDecimal(dr["SoldTotalPrice"]) * Convert.ToDecimal(dr["SellCrncRate"])) - (Convert.ToDecimal(dr["ReturnTotalPrice"]) * Convert.ToDecimal(dr["ReturnCrncRate"])),3);
                dt.Rows.Add(dr);
            }

            grdCategory.DataSource = dt;
        }

        private void gridView1_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            if (e.ListSourceRowIndex < 0)
                return;

            if (e.Column.FieldName == "colIndex")
                e.Value = e.RowHandle() + 1;
        }

        public bool LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.frm_SL_ItemsSalesDetails).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            return true;
        }
    }
}