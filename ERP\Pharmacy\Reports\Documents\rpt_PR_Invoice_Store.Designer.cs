namespace Reports
{
    partial class rpt_PR_Invoice_Store
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(rpt_PR_Invoice_Store));
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.lbl_BranchId = new DevExpress.XtraReports.UI.XRLabel();
            this.xrTable1 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow1 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell4 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableRow2 = new DevExpress.XtraReports.UI.XRTableRow();
            this.lbl_Emp = new DevExpress.XtraReports.UI.XRTableCell();
            this.lbl_InvoiceDate = new DevExpress.XtraReports.UI.XRTableCell();
            this.lbl_Code = new DevExpress.XtraReports.UI.XRTableCell();
            this.lblCustomer = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrLine2 = new DevExpress.XtraReports.UI.XRLine();
            this.lbl_StoreName = new DevExpress.XtraReports.UI.XRLabel();
            this.xrSubreport2 = new DevExpress.XtraReports.UI.XRSubreport();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.xrLabel2 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLine1 = new DevExpress.XtraReports.UI.XRLine();
            this.lbl_Shipping = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel18 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel13 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_SalesEmp = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel10 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_User = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel6 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel4 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_store = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel7 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel8 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Customer = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel1 = new DevExpress.XtraReports.UI.XRLabel();
            this.picLogo = new DevExpress.XtraReports.UI.XRPictureBox();
            this.lbl_date = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Number = new DevExpress.XtraReports.UI.XRLabel();
            this.lblCompName = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_notes = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_DeliverDate = new DevExpress.XtraReports.UI.XRLabel();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.lbl_BranchId,
            this.xrTable1,
            this.xrLine2,
            this.lbl_StoreName,
            this.xrSubreport2});
            resources.ApplyResources(this.Detail, "Detail");
            this.Detail.Name = "Detail";
            this.Detail.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Detail.PageBreak = DevExpress.XtraReports.UI.PageBreak.AfterBand;
            // 
            // lbl_BranchId
            // 
            resources.ApplyResources(this.lbl_BranchId, "lbl_BranchId");
            this.lbl_BranchId.Name = "lbl_BranchId";
            this.lbl_BranchId.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_BranchId.StylePriority.UseFont = false;
            this.lbl_BranchId.StylePriority.UseForeColor = false;
            this.lbl_BranchId.StylePriority.UseTextAlignment = false;
            // 
            // xrTable1
            // 
            resources.ApplyResources(this.xrTable1, "xrTable1");
            this.xrTable1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable1.Name = "xrTable1";
            this.xrTable1.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow1,
            this.xrTableRow2});
            this.xrTable1.StylePriority.UseBorderColor = false;
            this.xrTable1.StylePriority.UseBorders = false;
            this.xrTable1.StylePriority.UseFont = false;
            this.xrTable1.StylePriority.UseForeColor = false;
            this.xrTable1.StylePriority.UseTextAlignment = false;
            // 
            // xrTableRow1
            // 
            resources.ApplyResources(this.xrTableRow1, "xrTableRow1");
            this.xrTableRow1.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell4,
            this.xrTableCell1,
            this.xrTableCell2,
            this.xrTableCell3});
            this.xrTableRow1.Name = "xrTableRow1";
            // 
            // xrTableCell4
            // 
            resources.ApplyResources(this.xrTableCell4, "xrTableCell4");
            this.xrTableCell4.Name = "xrTableCell4";
            this.xrTableCell4.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrTableCell4.StylePriority.UseBackColor = false;
            this.xrTableCell4.StylePriority.UseForeColor = false;
            this.xrTableCell4.StylePriority.UsePadding = false;
            // 
            // xrTableCell1
            // 
            resources.ApplyResources(this.xrTableCell1, "xrTableCell1");
            this.xrTableCell1.Name = "xrTableCell1";
            this.xrTableCell1.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrTableCell1.StylePriority.UseBackColor = false;
            this.xrTableCell1.StylePriority.UseForeColor = false;
            this.xrTableCell1.StylePriority.UsePadding = false;
            // 
            // xrTableCell2
            // 
            resources.ApplyResources(this.xrTableCell2, "xrTableCell2");
            this.xrTableCell2.Name = "xrTableCell2";
            this.xrTableCell2.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrTableCell2.StylePriority.UseBackColor = false;
            this.xrTableCell2.StylePriority.UseForeColor = false;
            this.xrTableCell2.StylePriority.UsePadding = false;
            // 
            // xrTableCell3
            // 
            resources.ApplyResources(this.xrTableCell3, "xrTableCell3");
            this.xrTableCell3.Name = "xrTableCell3";
            this.xrTableCell3.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrTableCell3.StylePriority.UseBackColor = false;
            this.xrTableCell3.StylePriority.UseForeColor = false;
            this.xrTableCell3.StylePriority.UsePadding = false;
            // 
            // xrTableRow2
            // 
            resources.ApplyResources(this.xrTableRow2, "xrTableRow2");
            this.xrTableRow2.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.lbl_Emp,
            this.lbl_InvoiceDate,
            this.lbl_Code,
            this.lblCustomer});
            this.xrTableRow2.Name = "xrTableRow2";
            // 
            // lbl_Emp
            // 
            resources.ApplyResources(this.lbl_Emp, "lbl_Emp");
            this.lbl_Emp.Name = "lbl_Emp";
            this.lbl_Emp.StylePriority.UseBackColor = false;
            this.lbl_Emp.StylePriority.UseFont = false;
            // 
            // lbl_InvoiceDate
            // 
            resources.ApplyResources(this.lbl_InvoiceDate, "lbl_InvoiceDate");
            this.lbl_InvoiceDate.Name = "lbl_InvoiceDate";
            this.lbl_InvoiceDate.StylePriority.UseBackColor = false;
            this.lbl_InvoiceDate.StylePriority.UseFont = false;
            // 
            // lbl_Code
            // 
            resources.ApplyResources(this.lbl_Code, "lbl_Code");
            this.lbl_Code.Name = "lbl_Code";
            this.lbl_Code.StylePriority.UseBackColor = false;
            this.lbl_Code.StylePriority.UseFont = false;
            // 
            // lblCustomer
            // 
            resources.ApplyResources(this.lblCustomer, "lblCustomer");
            this.lblCustomer.Name = "lblCustomer";
            this.lblCustomer.StylePriority.UseBackColor = false;
            // 
            // xrLine2
            // 
            resources.ApplyResources(this.xrLine2, "xrLine2");
            this.xrLine2.Name = "xrLine2";
            this.xrLine2.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            // 
            // lbl_StoreName
            // 
            resources.ApplyResources(this.lbl_StoreName, "lbl_StoreName");
            this.lbl_StoreName.Name = "lbl_StoreName";
            this.lbl_StoreName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_StoreName.StylePriority.UseFont = false;
            this.lbl_StoreName.StylePriority.UseForeColor = false;
            this.lbl_StoreName.StylePriority.UseTextAlignment = false;
            // 
            // xrSubreport2
            // 
            resources.ApplyResources(this.xrSubreport2, "xrSubreport2");
            this.xrSubreport2.Name = "xrSubreport2";
            this.xrSubreport2.BeforePrint += new System.Drawing.Printing.PrintEventHandler(this.rpt_PR_Invoice_Store_BeforePrint);
            // 
            // TopMargin
            // 
            this.TopMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLabel2,
            this.xrLine1,
            this.lbl_Shipping,
            this.xrLabel18,
            this.xrLabel13,
            this.lbl_SalesEmp,
            this.xrLabel10,
            this.lbl_User,
            this.xrLabel6,
            this.xrLabel4,
            this.lbl_store,
            this.xrLabel7,
            this.xrLabel8,
            this.lbl_Customer,
            this.xrLabel1,
            this.picLogo,
            this.lbl_date,
            this.lbl_Number,
            this.lblCompName,
            this.lbl_notes,
            this.lbl_DeliverDate});
            resources.ApplyResources(this.TopMargin, "TopMargin");
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            // 
            // xrLabel2
            // 
            resources.ApplyResources(this.xrLabel2, "xrLabel2");
            this.xrLabel2.Name = "xrLabel2";
            this.xrLabel2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel2.StylePriority.UseFont = false;
            this.xrLabel2.StylePriority.UseTextAlignment = false;
            // 
            // xrLine1
            // 
            resources.ApplyResources(this.xrLine1, "xrLine1");
            this.xrLine1.BorderWidth = 0F;
            this.xrLine1.LineWidth = 0;
            this.xrLine1.Name = "xrLine1";
            this.xrLine1.StylePriority.UseBackColor = false;
            this.xrLine1.StylePriority.UseBorderColor = false;
            this.xrLine1.StylePriority.UseBorderWidth = false;
            this.xrLine1.StylePriority.UseForeColor = false;
            // 
            // lbl_Shipping
            // 
            resources.ApplyResources(this.lbl_Shipping, "lbl_Shipping");
            this.lbl_Shipping.Multiline = true;
            this.lbl_Shipping.Name = "lbl_Shipping";
            this.lbl_Shipping.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Shipping.StylePriority.UseFont = false;
            this.lbl_Shipping.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel18
            // 
            resources.ApplyResources(this.xrLabel18, "xrLabel18");
            this.xrLabel18.Name = "xrLabel18";
            this.xrLabel18.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel18.StylePriority.UseFont = false;
            this.xrLabel18.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel13
            // 
            resources.ApplyResources(this.xrLabel13, "xrLabel13");
            this.xrLabel13.Name = "xrLabel13";
            this.xrLabel13.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel13.StylePriority.UseFont = false;
            this.xrLabel13.StylePriority.UseTextAlignment = false;
            // 
            // lbl_SalesEmp
            // 
            resources.ApplyResources(this.lbl_SalesEmp, "lbl_SalesEmp");
            this.lbl_SalesEmp.Name = "lbl_SalesEmp";
            this.lbl_SalesEmp.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_SalesEmp.StylePriority.UseFont = false;
            this.lbl_SalesEmp.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel10
            // 
            resources.ApplyResources(this.xrLabel10, "xrLabel10");
            this.xrLabel10.Name = "xrLabel10";
            this.xrLabel10.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel10.StylePriority.UseFont = false;
            this.xrLabel10.StylePriority.UseTextAlignment = false;
            // 
            // lbl_User
            // 
            this.lbl_User.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lbl_User, "lbl_User");
            this.lbl_User.Name = "lbl_User";
            this.lbl_User.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_User.StylePriority.UseBorders = false;
            this.lbl_User.StylePriority.UseFont = false;
            this.lbl_User.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel6
            // 
            this.xrLabel6.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.xrLabel6, "xrLabel6");
            this.xrLabel6.Multiline = true;
            this.xrLabel6.Name = "xrLabel6";
            this.xrLabel6.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel6.StylePriority.UseBorders = false;
            this.xrLabel6.StylePriority.UseFont = false;
            this.xrLabel6.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel4
            // 
            resources.ApplyResources(this.xrLabel4, "xrLabel4");
            this.xrLabel4.Name = "xrLabel4";
            this.xrLabel4.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel4.StylePriority.UseFont = false;
            this.xrLabel4.StylePriority.UseTextAlignment = false;
            // 
            // lbl_store
            // 
            resources.ApplyResources(this.lbl_store, "lbl_store");
            this.lbl_store.Name = "lbl_store";
            this.lbl_store.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_store.StylePriority.UseFont = false;
            this.lbl_store.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel7
            // 
            resources.ApplyResources(this.xrLabel7, "xrLabel7");
            this.xrLabel7.Multiline = true;
            this.xrLabel7.Name = "xrLabel7";
            this.xrLabel7.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel7.StylePriority.UseFont = false;
            this.xrLabel7.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel8
            // 
            resources.ApplyResources(this.xrLabel8, "xrLabel8");
            this.xrLabel8.Name = "xrLabel8";
            this.xrLabel8.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel8.StylePriority.UseFont = false;
            this.xrLabel8.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Customer
            // 
            resources.ApplyResources(this.lbl_Customer, "lbl_Customer");
            this.lbl_Customer.Name = "lbl_Customer";
            this.lbl_Customer.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Customer.StylePriority.UseFont = false;
            this.lbl_Customer.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel1
            // 
            resources.ApplyResources(this.xrLabel1, "xrLabel1");
            this.xrLabel1.Name = "xrLabel1";
            this.xrLabel1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel1.StylePriority.UseFont = false;
            this.xrLabel1.StylePriority.UseTextAlignment = false;
            // 
            // picLogo
            // 
            resources.ApplyResources(this.picLogo, "picLogo");
            this.picLogo.Name = "picLogo";
            this.picLogo.Sizing = DevExpress.XtraPrinting.ImageSizeMode.StretchImage;
            // 
            // lbl_date
            // 
            resources.ApplyResources(this.lbl_date, "lbl_date");
            this.lbl_date.Name = "lbl_date";
            this.lbl_date.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_date.StylePriority.UseFont = false;
            this.lbl_date.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Number
            // 
            resources.ApplyResources(this.lbl_Number, "lbl_Number");
            this.lbl_Number.Name = "lbl_Number";
            this.lbl_Number.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Number.StylePriority.UseFont = false;
            this.lbl_Number.StylePriority.UseTextAlignment = false;
            // 
            // lblCompName
            // 
            resources.ApplyResources(this.lblCompName, "lblCompName");
            this.lblCompName.Name = "lblCompName";
            this.lblCompName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblCompName.StylePriority.UseFont = false;
            this.lblCompName.StylePriority.UseTextAlignment = false;
            // 
            // lbl_notes
            // 
            resources.ApplyResources(this.lbl_notes, "lbl_notes");
            this.lbl_notes.Multiline = true;
            this.lbl_notes.Name = "lbl_notes";
            this.lbl_notes.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_notes.StylePriority.UseFont = false;
            this.lbl_notes.StylePriority.UseTextAlignment = false;
            // 
            // lbl_DeliverDate
            // 
            resources.ApplyResources(this.lbl_DeliverDate, "lbl_DeliverDate");
            this.lbl_DeliverDate.Name = "lbl_DeliverDate";
            this.lbl_DeliverDate.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_DeliverDate.StylePriority.UseFont = false;
            this.lbl_DeliverDate.StylePriority.UseTextAlignment = false;
            // 
            // BottomMargin
            // 
            resources.ApplyResources(this.BottomMargin, "BottomMargin");
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            // 
            // rpt_PR_Invoice_Store
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail,
            this.TopMargin,
            this.BottomMargin});
            resources.ApplyResources(this, "$this");
            this.ShowPrintMarginsWarning = false;
            this.Version = "15.1";
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.XRPictureBox picLogo;
        private DevExpress.XtraReports.UI.XRLabel lblCompName;
        private DevExpress.XtraReports.UI.XRLabel xrLabel1;
        private DevExpress.XtraReports.UI.XRLabel lbl_date;
        private DevExpress.XtraReports.UI.XRLabel lbl_store;
        private DevExpress.XtraReports.UI.XRLabel xrLabel7;
        private DevExpress.XtraReports.UI.XRLabel xrLabel8;
        private DevExpress.XtraReports.UI.XRLabel lbl_Number;
        private DevExpress.XtraReports.UI.XRLabel lbl_Customer;
        private DevExpress.XtraReports.UI.XRLabel lbl_notes;
        private DevExpress.XtraReports.UI.XRLabel xrLabel4;
        private DevExpress.XtraReports.UI.XRLabel lbl_User;
        private DevExpress.XtraReports.UI.XRLabel xrLabel6;
        private DevExpress.XtraReports.UI.XRLabel lbl_SalesEmp;
        private DevExpress.XtraReports.UI.XRLabel xrLabel10;
        private DevExpress.XtraReports.UI.XRLabel lbl_Shipping;
        private DevExpress.XtraReports.UI.XRLabel xrLabel18;
        private DevExpress.XtraReports.UI.XRLabel lbl_DeliverDate;
        private DevExpress.XtraReports.UI.XRLabel xrLabel13;
        private DevExpress.XtraReports.UI.XRLine xrLine1;
        private DevExpress.XtraReports.UI.XRSubreport xrSubreport2;
        private DevExpress.XtraReports.UI.XRLabel lbl_StoreName;
        private DevExpress.XtraReports.UI.XRTable xrTable1;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow1;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell4;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell1;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell2;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell3;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow2;
        private DevExpress.XtraReports.UI.XRTableCell lbl_Emp;
        private DevExpress.XtraReports.UI.XRTableCell lbl_InvoiceDate;
        private DevExpress.XtraReports.UI.XRTableCell lbl_Code;
        private DevExpress.XtraReports.UI.XRTableCell lblCustomer;
        private DevExpress.XtraReports.UI.XRLine xrLine2;
        private DevExpress.XtraReports.UI.XRLabel lbl_BranchId;
        private DevExpress.XtraReports.UI.XRLabel xrLabel2;
    }
}
