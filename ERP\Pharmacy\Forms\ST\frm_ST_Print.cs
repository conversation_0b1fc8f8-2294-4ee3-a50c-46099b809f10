﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;
using System.Management;
using System.Collections;

namespace Pharmacy.Forms
{
    public partial class frm_ST_Print : DevExpress.XtraEditors.XtraForm
    {
        bool DataModified;

        public frm_ST_Print()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
        }

        private void frm_ST_Print_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            var printerQuery = new ManagementObjectSearcher("SELECT * from Win32_Printer");
            List<c_printer> printers = new List<c_printer>();
            foreach (var printer in printerQuery.Get())
            {
                var name = printer.GetPropertyValue("Name").ToString();
                //var status = printer.GetPropertyValue("Status");
                //var isDefault = printer.GetPropertyValue("Default");
                var isNetworkPrinter = printer.GetPropertyValue("Network").ToString();

                printers.Add(new c_printer { Name = name, Network = isNetworkPrinter });
            }

            lkp_printers.Properties.DataSource = printers.ToList();
            lkp_printers.Properties.ValueMember = lkp_printers.Properties.DisplayMember = "Name";

            Load_invoice_Print_Settings();
        }

        private void frm_ST_Print_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                e.Cancel = true;
        }


        private void txtMinValue_Spin(object sender, DevExpress.XtraEditors.Controls.SpinEventArgs e)
        {
            if (!e.IsSpinUp)
            {
                if (((SpinEdit)sender).Name == "txtMinValue")
                {
                    if (Convert.ToDecimal(txtMinValue.EditValue) <= 0)
                        e.Handled = true;
                }
                else if (((SpinEdit)sender).Name == "txt_NoOfPrint")
                {
                    if (Convert.ToDecimal(txt_NoOfPrint.EditValue) <= 0)
                        e.Handled = true;
                }
            }
        }


        private void barBtn_Save_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Save_PrintSettings();
        }

        private void barButtonClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }


        private void Save_PrintSettings()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            var print_data = (from d in DB.ST_PrintInvoices
                              select d).FirstOrDefault();

            try
            {
                if (print_data == null)
                {
                    print_data = new ST_PrintInvoice();

                    print_data.PrintReceiptPOS = (bool)(cmbPrintReceiptPOS.EditValue);
                    print_data.PrintReceiptSLI = (bool)(cmbPrintReceiptSLI.EditValue);
                    print_data.MinValue = txtMinValue.Text.Trim() == string.Empty ? 0 : Convert.ToDecimal(txtMinValue.Text);
                    print_data.NoOfPrint = txt_NoOfPrint.Text.Trim() == string.Empty ? 0 : Convert.ToInt32(txt_NoOfPrint.Text);

                    print_data.PrintLogo = chkPrintLogo.Checked;
                    print_data.PrintCompName = chkPrintCompName.Checked;
                    print_data.PrintInvoiceId = chkPrintInvoiceId.Checked;
                    print_data.PrintInvoiceDate = chkPrintDate.Checked;

                    print_data.FooterLine1 = txtFooterLine1.Text;
                    print_data.FooterLine2 = txtFooterLine2.Text;

                    print_data.scndPrinter = lkp_printers.SelectedText;
                    if (lkp_AddTaxValue.EditValue != null)
                    print_data.AddTaxValue = Convert.ToDecimal(lkp_AddTaxValue.EditValue);
                    DB.ST_PrintInvoices.InsertOnSubmit(print_data);

                    MyHelper.UpdateST_UserLog(DB, "", "",
             (int)FormAction.Edit, (int)FormsNames.ST_Print);

                    DB.SubmitChanges();
                }
                else
                {
                    print_data.PrintReceiptPOS = (bool)(cmbPrintReceiptPOS.EditValue);
                    print_data.PrintReceiptSLI = (bool)(cmbPrintReceiptSLI.EditValue);
                    print_data.MinValue = txtMinValue.Text.Trim() == string.Empty ? 0 : Convert.ToDecimal(txtMinValue.Text);
                    print_data.NoOfPrint = txt_NoOfPrint.Text.Trim() == string.Empty ? 0 : Convert.ToInt32(txt_NoOfPrint.Text);

                    print_data.PrintLogo = chkPrintLogo.Checked;
                    print_data.PrintCompName = chkPrintCompName.Checked;
                    print_data.PrintInvoiceId = chkPrintInvoiceId.Checked;
                    print_data.PrintInvoiceDate = chkPrintDate.Checked;

                    print_data.FooterLine1 = txtFooterLine1.Text;
                    print_data.FooterLine2 = txtFooterLine2.Text;

                    print_data.scndPrinter = lkp_printers.SelectedText;
                    if (lkp_AddTaxValue.EditValue != null)
                        print_data.AddTaxValue = Convert.ToDecimal(lkp_AddTaxValue.EditValue);
                    MyHelper.UpdateST_UserLog(DB, "", "",
                    (int)FormAction.Add, (int)FormsNames.ST_Print);

                    DB.SubmitChanges();
                }
                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResAccEn.MsgSave : ResAccAr.MsgSave//"تم الحفظ بنجاح"
                    , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                DataModified = false;

            }
            catch
            {
                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResAccEn.MsgIncorrectData : ResAccAr.MsgIncorrectData//"تأكد من صحة البيانات"                    
                    , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void Load_invoice_Print_Settings()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            var print_data = (from d in DB.ST_PrintInvoices
                              select d).FirstOrDefault();

            if (print_data == null)
                return;
            else
            {
                cmbPrintReceiptPOS.EditValue = print_data.PrintReceiptPOS;
                cmbPrintReceiptSLI.EditValue = print_data.PrintReceiptSLI;
                txtMinValue.EditValue = print_data.MinValue;
                txt_NoOfPrint.EditValue = print_data.NoOfPrint;
                txtFooterLine1.Text = print_data.FooterLine1;
                txtFooterLine2.Text = print_data.FooterLine2;
                chkPrintLogo.Checked = print_data.PrintLogo;
                chkPrintCompName.Checked = print_data.PrintCompName;
                chkPrintInvoiceId.Checked = print_data.PrintInvoiceId;
                chkPrintDate.Checked = print_data.PrintInvoiceDate;
                lkp_AddTaxValue.EditValue = print_data.AddTaxValue;
                lkp_printers.EditValue = print_data.scndPrinter;
            }
        }

        private void controls_Modified(object sender, EventArgs e)
        {
            DataModified = true;
        }

        DialogResult ChangesMade()
        {

            if (DataModified)
            {
                DialogResult r = XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResAccEn.MsgDataModified : ResAccAr.MsgDataModified//"لقد قمت بعمل بعض التغييرات, هل تريد الحفظ أولا "                                        
                    , "", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                if (r == DialogResult.Yes)
                {
                    Save_PrintSettings();
                    return r;
                }
                else if (r == DialogResult.No)
                {
                    // no thing made, continue closing or do next or do previous
                    return r;
                }
                else if (r == DialogResult.Cancel)
                {
                    //cancel form action
                    return r;
                }
            }
            return DialogResult.No;
        }

        void DoValidate()
        {
            cmbPrintReceiptSLI.DoValidate();
            cmbPrintReceiptPOS.DoValidate();

            chkPrintInvoiceId.DoValidate();
            chkPrintCompName.DoValidate();
            chkPrintLogo.DoValidate();
            chkPrintDate.DoValidate();

            txtMinValue.DoValidate();
            txt_NoOfPrint.DoValidate();
            txtFooterLine2.DoValidate();
            txtFooterLine2.DoValidate();
        }

        private void barBtn_Help_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "إعدادات طباعة الباركود");
        }
    }

    public class c_printer { public string Name, Network; }

}