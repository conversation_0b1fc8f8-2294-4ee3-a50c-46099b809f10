﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="barManager1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="barBtn_Help.Caption" xml:space="preserve">
    <value>Help</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="barBtn_Help.Glyph" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAACH
        DwAAjA8AAP1SAACBQAAAfXkAAOmLAAA85QAAGcxzPIV3AAAKOWlDQ1BQaG90b3Nob3AgSUNDIHByb2Zp
        bGUAAEjHnZZ3VFTXFofPvXd6oc0wAlKG3rvAANJ7k15FYZgZYCgDDjM0sSGiAhFFRJoiSFDEgNFQJFZE
        sRAUVLAHJAgoMRhFVCxvRtaLrqy89/Ly++Osb+2z97n77L3PWhcAkqcvl5cGSwGQyhPwgzyc6RGRUXTs
        AIABHmCAKQBMVka6X7B7CBDJy82FniFyAl8EAfB6WLwCcNPQM4BOB/+fpFnpfIHomAARm7M5GSwRF4g4
        JUuQLrbPipgalyxmGCVmvihBEcuJOWGRDT77LLKjmNmpPLaIxTmns1PZYu4V8bZMIUfEiK+ICzO5nCwR
        3xKxRoowlSviN+LYVA4zAwAUSWwXcFiJIjYRMYkfEuQi4uUA4EgJX3HcVyzgZAvEl3JJS8/hcxMSBXQd
        li7d1NqaQffkZKVwBALDACYrmcln013SUtOZvBwAFu/8WTLi2tJFRbY0tba0NDQzMv2qUP91829K3NtF
        ehn4uWcQrf+L7a/80hoAYMyJarPziy2uCoDOLQDI3fti0zgAgKSobx3Xv7oPTTwviQJBuo2xcVZWlhGX
        wzISF/QP/U+Hv6GvvmckPu6P8tBdOfFMYYqALq4bKy0lTcinZ6QzWRy64Z+H+B8H/nUeBkGceA6fwxNF
        hImmjMtLELWbx+YKuGk8Opf3n5r4D8P+pMW5FonS+BFQY4yA1HUqQH7tBygKESDR+8Vd/6NvvvgwIH55
        4SqTi3P/7zf9Z8Gl4iWDm/A5ziUohM4S8jMX98TPEqABAUgCKpAHykAd6ABDYAasgC1wBG7AG/iDEBAJ
        VgMWSASpgA+yQB7YBApBMdgJ9oBqUAcaQTNoBcdBJzgFzoNL4Bq4AW6D+2AUTIBnYBa8BgsQBGEhMkSB
        5CEVSBPSh8wgBmQPuUG+UBAUCcVCCRAPEkJ50GaoGCqDqqF6qBn6HjoJnYeuQIPQXWgMmoZ+h97BCEyC
        qbASrAUbwwzYCfaBQ+BVcAK8Bs6FC+AdcCXcAB+FO+Dz8DX4NjwKP4PnEIAQERqiihgiDMQF8UeikHiE
        j6xHipAKpAFpRbqRPuQmMorMIG9RGBQFRUcZomxRnqhQFAu1BrUeVYKqRh1GdaB6UTdRY6hZ1Ec0Ga2I
        1kfboL3QEegEdBa6EF2BbkK3oy+ib6Mn0K8xGAwNo42xwnhiIjFJmLWYEsw+TBvmHGYQM46Zw2Kx8lh9
        rB3WH8vECrCF2CrsUexZ7BB2AvsGR8Sp4Mxw7rgoHA+Xj6vAHcGdwQ3hJnELeCm8Jt4G749n43PwpfhG
        fDf+On4Cv0CQJmgT7AghhCTCJkIloZVwkfCA8JJIJKoRrYmBRC5xI7GSeIx4mThGfEuSIemRXEjRJCFp
        B+kQ6RzpLuklmUzWIjuSo8gC8g5yM/kC+RH5jQRFwkjCS4ItsUGiRqJDYkjiuSReUlPSSXK1ZK5kheQJ
        yeuSM1J4KS0pFymm1HqpGqmTUiNSc9IUaVNpf+lU6RLpI9JXpKdksDJaMm4ybJkCmYMyF2TGKQhFneJC
        YVE2UxopFykTVAxVm+pFTaIWU7+jDlBnZWVkl8mGyWbL1sielh2lITQtmhcthVZKO04bpr1borTEaQln
        yfYlrUuGlszLLZVzlOPIFcm1yd2WeydPl3eTT5bfJd8p/1ABpaCnEKiQpbBf4aLCzFLqUtulrKVFS48v
        vacIK+opBimuVTyo2K84p6Ss5KGUrlSldEFpRpmm7KicpFyufEZ5WoWiYq/CVSlXOavylC5Ld6Kn0Cvp
        vfRZVUVVT1Whar3qgOqCmrZaqFq+WpvaQ3WCOkM9Xr1cvUd9VkNFw08jT6NF454mXpOhmai5V7NPc15L
        Wytca6tWp9aUtpy2l3audov2Ax2yjoPOGp0GnVu6GF2GbrLuPt0berCehV6iXo3edX1Y31Kfq79Pf9AA
        bWBtwDNoMBgxJBk6GWYathiOGdGMfI3yjTqNnhtrGEcZ7zLuM/5oYmGSYtJoct9UxtTbNN+02/R3Mz0z
        llmN2S1zsrm7+QbzLvMXy/SXcZbtX3bHgmLhZ7HVosfig6WVJd+y1XLaSsMq1qrWaoRBZQQwShiXrdHW
        ztYbrE9Zv7WxtBHYHLf5zdbQNtn2iO3Ucu3lnOWNy8ft1OyYdvV2o/Z0+1j7A/ajDqoOTIcGh8eO6o5s
        xybHSSddpySno07PnU2c+c7tzvMuNi7rXM65Iq4erkWuA24ybqFu1W6P3NXcE9xb3Gc9LDzWepzzRHv6
        eO7yHPFS8mJ5NXvNelt5r/Pu9SH5BPtU+zz21fPl+3b7wX7efrv9HqzQXMFb0ekP/L38d/s/DNAOWBPw
        YyAmMCCwJvBJkGlQXlBfMCU4JvhI8OsQ55DSkPuhOqHC0J4wybDosOaw+XDX8LLw0QjjiHUR1yIVIrmR
        XVHYqLCopqi5lW4r96yciLaILoweXqW9KnvVldUKq1NWn46RjGHGnIhFx4bHHol9z/RnNjDn4rziauNm
        WS6svaxnbEd2OXuaY8cp40zG28WXxU8l2CXsTphOdEisSJzhunCruS+SPJPqkuaT/ZMPJX9KCU9pS8Wl
        xqae5Mnwknm9acpp2WmD6frphemja2zW7Fkzy/fhN2VAGasyugRU0c9Uv1BHuEU4lmmfWZP5Jiss60S2
        dDYvuz9HL2d7zmSue+63a1FrWWt78lTzNuWNrXNaV78eWh+3vmeD+oaCDRMbPTYe3kTYlLzpp3yT/LL8
        V5vDN3cXKBVsLBjf4rGlpVCikF84stV2a9021DbutoHt5turtn8sYhddLTYprih+X8IqufqN6TeV33za
        Eb9joNSydP9OzE7ezuFdDrsOl0mX5ZaN7/bb3VFOLy8qf7UnZs+VimUVdXsJe4V7Ryt9K7uqNKp2Vr2v
        Tqy+XeNc01arWLu9dn4fe9/Qfsf9rXVKdcV17w5wD9yp96jvaNBqqDiIOZh58EljWGPft4xvm5sUmoqb
        PhziHRo9HHS4t9mqufmI4pHSFrhF2DJ9NProje9cv+tqNWytb6O1FR8Dx4THnn4f+/3wcZ/jPScYJ1p/
        0Pyhtp3SXtQBdeR0zHYmdo52RXYNnvQ+2dNt293+o9GPh06pnqo5LXu69AzhTMGZT2dzz86dSz83cz7h
        /HhPTM/9CxEXbvUG9g5c9Ll4+ZL7pQt9Tn1nL9tdPnXF5srJq4yrndcsr3X0W/S3/2TxU/uA5UDHdavr
        XTesb3QPLh88M+QwdP6m681Lt7xuXbu94vbgcOjwnZHokdE77DtTd1PuvriXeW/h/sYH6AdFD6UeVjxS
        fNTws+7PbaOWo6fHXMf6Hwc/vj/OGn/2S8Yv7ycKnpCfVEyqTDZPmU2dmnafvvF05dOJZ+nPFmYKf5X+
        tfa5zvMffnP8rX82YnbiBf/Fp99LXsq/PPRq2aueuYC5R69TXy/MF72Rf3P4LeNt37vwd5MLWe+x7ys/
        6H7o/ujz8cGn1E+f/gUDmPP8usTo0wAAAAlwSFlzAAALEgAACxIB0t1+/AAAAtpJREFUOE9tkklME2EU
        xxuNR08E48EQY0yM0YNXDh68G64evBiXRBQjJ5YCKYVSNtlFgbJathbKWnYLBLCIskOU3RZoujClnQ4U
        Bgh/3zcFDGqTX6bvvf/7f+97MzIA58hu81wkwvM6vP1Fn4X90oEdFPUJfK7Ra6R82N/6cwEJbpFwvHZM
        RPPMEXp+HqF34VB6srjavA+qt5Iu+B8DSoYWdvOiYeoQ2tE9lAwKyO3gkNXukige8En5holD5HfybtKH
        SAZPkoZkGU1bl7Na3R7ddxEaavxo8iHHuIW5jQPSADPrIrIp/tDHk5GA2q97eNfCzZwZpBtcKZp+H8qH
        d1HYy+M9kWPkYJr3Y+/gGL2zuzQFh/xuL/K7PCgbIl2PF9T3SDJQ6xzLldTMinmMzgBJNRYotStI0W2Q
        4TZNQbQHKKNJ1Hr7gExVbwvKMNhRQhOcFrMYbW6YF/3SFUw0QWaLm+CQwWjm6M3wSKmzeWTKmvU7ar0N
        OTTiaTGd0cRh5McOjo7oTUz5kGbYQmpjADVBO6AJrYcyhdZ6RaG10H2cZ0V1A8OFwTkfRFFE1zgPld4F
        lS5Ass6JtEYn4spXeWkHUcULXrXeAWWtXSom1wcwTXvh9/thHKN91DmhZNQ6oai2QVVnQ4xmaU4yiC5Z
        1Cd8siK+ah2JNXYSOZBI9E1sQxAEtI26KU85QlFtR1zlOmI1S4gtXUqVDORlyyFv82ZEhXYTMWUWJFRt
        SmLDtA8dCztopB2wOL5yAzGlv8jAgsiCWS6+YvWSZEBFmmLhVUTO5LG8worYciuiNWuIJTN5BZ1GzyiK
        5ZSPKllBRPbkAY1/n/WdGTBoF2Gvs8aFyIJ5MrHQlTaITTqRnbxGjVN4kztlo8Punfaw34WTP9eJu0FX
        bzx8oTINh2d+8z1VjeC52oxnKV/wMt3sfRxV10OaB0Qo00o97Hs+dTsxYoXbxE3i2n/i4D96h+w3curF
        81pcic0AAAAASUVORK5CYII=
</value>
  </data>
  <data name="barBtn_Help.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="barBtn_Open.Caption" xml:space="preserve">
    <value>Open</value>
  </data>
  <data name="barBtn_Open.Glyph" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAACH
        DwAAjA8AAP1SAACBQAAAfXkAAOmLAAA85QAAGcxzPIV3AAAKOWlDQ1BQaG90b3Nob3AgSUNDIHByb2Zp
        bGUAAEjHnZZ3VFTXFofPvXd6oc0wAlKG3rvAANJ7k15FYZgZYCgDDjM0sSGiAhFFRJoiSFDEgNFQJFZE
        sRAUVLAHJAgoMRhFVCxvRtaLrqy89/Ly++Osb+2z97n77L3PWhcAkqcvl5cGSwGQyhPwgzyc6RGRUXTs
        AIABHmCAKQBMVka6X7B7CBDJy82FniFyAl8EAfB6WLwCcNPQM4BOB/+fpFnpfIHomAARm7M5GSwRF4g4
        JUuQLrbPipgalyxmGCVmvihBEcuJOWGRDT77LLKjmNmpPLaIxTmns1PZYu4V8bZMIUfEiK+ICzO5nCwR
        3xKxRoowlSviN+LYVA4zAwAUSWwXcFiJIjYRMYkfEuQi4uUA4EgJX3HcVyzgZAvEl3JJS8/hcxMSBXQd
        li7d1NqaQffkZKVwBALDACYrmcln013SUtOZvBwAFu/8WTLi2tJFRbY0tba0NDQzMv2qUP91829K3NtF
        ehn4uWcQrf+L7a/80hoAYMyJarPziy2uCoDOLQDI3fti0zgAgKSobx3Xv7oPTTwviQJBuo2xcVZWlhGX
        wzISF/QP/U+Hv6GvvmckPu6P8tBdOfFMYYqALq4bKy0lTcinZ6QzWRy64Z+H+B8H/nUeBkGceA6fwxNF
        hImmjMtLELWbx+YKuGk8Opf3n5r4D8P+pMW5FonS+BFQY4yA1HUqQH7tBygKESDR+8Vd/6NvvvgwIH55
        4SqTi3P/7zf9Z8Gl4iWDm/A5ziUohM4S8jMX98TPEqABAUgCKpAHykAd6ABDYAasgC1wBG7AG/iDEBAJ
        VgMWSASpgA+yQB7YBApBMdgJ9oBqUAcaQTNoBcdBJzgFzoNL4Bq4AW6D+2AUTIBnYBa8BgsQBGEhMkSB
        5CEVSBPSh8wgBmQPuUG+UBAUCcVCCRAPEkJ50GaoGCqDqqF6qBn6HjoJnYeuQIPQXWgMmoZ+h97BCEyC
        qbASrAUbwwzYCfaBQ+BVcAK8Bs6FC+AdcCXcAB+FO+Dz8DX4NjwKP4PnEIAQERqiihgiDMQF8UeikHiE
        j6xHipAKpAFpRbqRPuQmMorMIG9RGBQFRUcZomxRnqhQFAu1BrUeVYKqRh1GdaB6UTdRY6hZ1Ec0Ga2I
        1kfboL3QEegEdBa6EF2BbkK3oy+ib6Mn0K8xGAwNo42xwnhiIjFJmLWYEsw+TBvmHGYQM46Zw2Kx8lh9
        rB3WH8vECrCF2CrsUexZ7BB2AvsGR8Sp4Mxw7rgoHA+Xj6vAHcGdwQ3hJnELeCm8Jt4G749n43PwpfhG
        fDf+On4Cv0CQJmgT7AghhCTCJkIloZVwkfCA8JJIJKoRrYmBRC5xI7GSeIx4mThGfEuSIemRXEjRJCFp
        B+kQ6RzpLuklmUzWIjuSo8gC8g5yM/kC+RH5jQRFwkjCS4ItsUGiRqJDYkjiuSReUlPSSXK1ZK5kheQJ
        yeuSM1J4KS0pFymm1HqpGqmTUiNSc9IUaVNpf+lU6RLpI9JXpKdksDJaMm4ybJkCmYMyF2TGKQhFneJC
        YVE2UxopFykTVAxVm+pFTaIWU7+jDlBnZWVkl8mGyWbL1sielh2lITQtmhcthVZKO04bpr1borTEaQln
        yfYlrUuGlszLLZVzlOPIFcm1yd2WeydPl3eTT5bfJd8p/1ABpaCnEKiQpbBf4aLCzFLqUtulrKVFS48v
        vacIK+opBimuVTyo2K84p6Ss5KGUrlSldEFpRpmm7KicpFyufEZ5WoWiYq/CVSlXOavylC5Ld6Kn0Cvp
        vfRZVUVVT1Whar3qgOqCmrZaqFq+WpvaQ3WCOkM9Xr1cvUd9VkNFw08jT6NF454mXpOhmai5V7NPc15L
        Wytca6tWp9aUtpy2l3audov2Ax2yjoPOGp0GnVu6GF2GbrLuPt0berCehV6iXo3edX1Y31Kfq79Pf9AA
        bWBtwDNoMBgxJBk6GWYathiOGdGMfI3yjTqNnhtrGEcZ7zLuM/5oYmGSYtJoct9UxtTbNN+02/R3Mz0z
        llmN2S1zsrm7+QbzLvMXy/SXcZbtX3bHgmLhZ7HVosfig6WVJd+y1XLaSsMq1qrWaoRBZQQwShiXrdHW
        ztYbrE9Zv7WxtBHYHLf5zdbQNtn2iO3Ucu3lnOWNy8ft1OyYdvV2o/Z0+1j7A/ajDqoOTIcGh8eO6o5s
        xybHSSddpySno07PnU2c+c7tzvMuNi7rXM65Iq4erkWuA24ybqFu1W6P3NXcE9xb3Gc9LDzWepzzRHv6
        eO7yHPFS8mJ5NXvNelt5r/Pu9SH5BPtU+zz21fPl+3b7wX7efrv9HqzQXMFb0ekP/L38d/s/DNAOWBPw
        YyAmMCCwJvBJkGlQXlBfMCU4JvhI8OsQ55DSkPuhOqHC0J4wybDosOaw+XDX8LLw0QjjiHUR1yIVIrmR
        XVHYqLCopqi5lW4r96yciLaILoweXqW9KnvVldUKq1NWn46RjGHGnIhFx4bHHol9z/RnNjDn4rziauNm
        WS6svaxnbEd2OXuaY8cp40zG28WXxU8l2CXsTphOdEisSJzhunCruS+SPJPqkuaT/ZMPJX9KCU9pS8Wl
        xqae5Mnwknm9acpp2WmD6frphemja2zW7Fkzy/fhN2VAGasyugRU0c9Uv1BHuEU4lmmfWZP5Jiss60S2
        dDYvuz9HL2d7zmSue+63a1FrWWt78lTzNuWNrXNaV78eWh+3vmeD+oaCDRMbPTYe3kTYlLzpp3yT/LL8
        V5vDN3cXKBVsLBjf4rGlpVCikF84stV2a9021DbutoHt5turtn8sYhddLTYprih+X8IqufqN6TeV33za
        Eb9joNSydP9OzE7ezuFdDrsOl0mX5ZaN7/bb3VFOLy8qf7UnZs+VimUVdXsJe4V7Ryt9K7uqNKp2Vr2v
        Tqy+XeNc01arWLu9dn4fe9/Qfsf9rXVKdcV17w5wD9yp96jvaNBqqDiIOZh58EljWGPft4xvm5sUmoqb
        PhziHRo9HHS4t9mqufmI4pHSFrhF2DJ9NProje9cv+tqNWytb6O1FR8Dx4THnn4f+/3wcZ/jPScYJ1p/
        0Pyhtp3SXtQBdeR0zHYmdo52RXYNnvQ+2dNt293+o9GPh06pnqo5LXu69AzhTMGZT2dzz86dSz83cz7h
        /HhPTM/9CxEXbvUG9g5c9Ll4+ZL7pQt9Tn1nL9tdPnXF5srJq4yrndcsr3X0W/S3/2TxU/uA5UDHdavr
        XTesb3QPLh88M+QwdP6m681Lt7xuXbu94vbgcOjwnZHokdE77DtTd1PuvriXeW/h/sYH6AdFD6UeVjxS
        fNTws+7PbaOWo6fHXMf6Hwc/vj/OGn/2S8Yv7ycKnpCfVEyqTDZPmU2dmnafvvF05dOJZ+nPFmYKf5X+
        tfa5zvMffnP8rX82YnbiBf/Fp99LXsq/PPRq2aueuYC5R69TXy/MF72Rf3P4LeNt37vwd5MLWe+x7ys/
        6H7o/ujz8cGn1E+f/gUDmPP8usTo0wAAAAlwSFlzAAALEgAACxIB0t1+/AAAAU9JREFUOE9j+P//P0UY
        qyApmKFx+eOF/Wvv/kfH9gnzGLFpQMcM5XNv/X/9/d//Hbe/wfGW658xDETG0fW72VAMmHXk4/9Je7+h
        4JXnEXjJaQhecOLb/9Z1z/8ntB6SBGkGupKVoWzOzf+9O178b17z7H/JrCsEcfn8m/9d05cmADU7A7Es
        0IAb/9s3PPpfNu8mGH/89R/FO/gw0ABfhtLZN/6Xz7sINn3hiS9A13wmCtctf/QzqeOoHtCA6//zppz4
        P2Pvq/892z7+79pKHM6beukYOBBLZ13/n9Z94P/8Yx//t298TxRuWPn8f2r3yTSwASWzrv2fsOXR/+4t
        7/83r31HFM6beuUlPBpBBiw+8eF/58Z3//u2fyCIe7e++5/Re2Yl3ACf9Emx1Qtv/s2ZfJkonNF37pei
        nrMx3AAgsCAHww2AMcjFWAWJx/8ZAEYM2PK7ULaRAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="barBtn_Open.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="barBtn_Open.ItemAppearance.Normal.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 10pt</value>
  </data>
  <data name="barBtnClose.Caption" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="barBtnClose.Glyph" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAACH
        DwAAjA8AAP1SAACBQAAAfXkAAOmLAAA85QAAGcxzPIV3AAAKOWlDQ1BQaG90b3Nob3AgSUNDIHByb2Zp
        bGUAAEjHnZZ3VFTXFofPvXd6oc0wAlKG3rvAANJ7k15FYZgZYCgDDjM0sSGiAhFFRJoiSFDEgNFQJFZE
        sRAUVLAHJAgoMRhFVCxvRtaLrqy89/Ly++Osb+2z97n77L3PWhcAkqcvl5cGSwGQyhPwgzyc6RGRUXTs
        AIABHmCAKQBMVka6X7B7CBDJy82FniFyAl8EAfB6WLwCcNPQM4BOB/+fpFnpfIHomAARm7M5GSwRF4g4
        JUuQLrbPipgalyxmGCVmvihBEcuJOWGRDT77LLKjmNmpPLaIxTmns1PZYu4V8bZMIUfEiK+ICzO5nCwR
        3xKxRoowlSviN+LYVA4zAwAUSWwXcFiJIjYRMYkfEuQi4uUA4EgJX3HcVyzgZAvEl3JJS8/hcxMSBXQd
        li7d1NqaQffkZKVwBALDACYrmcln013SUtOZvBwAFu/8WTLi2tJFRbY0tba0NDQzMv2qUP91829K3NtF
        ehn4uWcQrf+L7a/80hoAYMyJarPziy2uCoDOLQDI3fti0zgAgKSobx3Xv7oPTTwviQJBuo2xcVZWlhGX
        wzISF/QP/U+Hv6GvvmckPu6P8tBdOfFMYYqALq4bKy0lTcinZ6QzWRy64Z+H+B8H/nUeBkGceA6fwxNF
        hImmjMtLELWbx+YKuGk8Opf3n5r4D8P+pMW5FonS+BFQY4yA1HUqQH7tBygKESDR+8Vd/6NvvvgwIH55
        4SqTi3P/7zf9Z8Gl4iWDm/A5ziUohM4S8jMX98TPEqABAUgCKpAHykAd6ABDYAasgC1wBG7AG/iDEBAJ
        VgMWSASpgA+yQB7YBApBMdgJ9oBqUAcaQTNoBcdBJzgFzoNL4Bq4AW6D+2AUTIBnYBa8BgsQBGEhMkSB
        5CEVSBPSh8wgBmQPuUG+UBAUCcVCCRAPEkJ50GaoGCqDqqF6qBn6HjoJnYeuQIPQXWgMmoZ+h97BCEyC
        qbASrAUbwwzYCfaBQ+BVcAK8Bs6FC+AdcCXcAB+FO+Dz8DX4NjwKP4PnEIAQERqiihgiDMQF8UeikHiE
        j6xHipAKpAFpRbqRPuQmMorMIG9RGBQFRUcZomxRnqhQFAu1BrUeVYKqRh1GdaB6UTdRY6hZ1Ec0Ga2I
        1kfboL3QEegEdBa6EF2BbkK3oy+ib6Mn0K8xGAwNo42xwnhiIjFJmLWYEsw+TBvmHGYQM46Zw2Kx8lh9
        rB3WH8vECrCF2CrsUexZ7BB2AvsGR8Sp4Mxw7rgoHA+Xj6vAHcGdwQ3hJnELeCm8Jt4G749n43PwpfhG
        fDf+On4Cv0CQJmgT7AghhCTCJkIloZVwkfCA8JJIJKoRrYmBRC5xI7GSeIx4mThGfEuSIemRXEjRJCFp
        B+kQ6RzpLuklmUzWIjuSo8gC8g5yM/kC+RH5jQRFwkjCS4ItsUGiRqJDYkjiuSReUlPSSXK1ZK5kheQJ
        yeuSM1J4KS0pFymm1HqpGqmTUiNSc9IUaVNpf+lU6RLpI9JXpKdksDJaMm4ybJkCmYMyF2TGKQhFneJC
        YVE2UxopFykTVAxVm+pFTaIWU7+jDlBnZWVkl8mGyWbL1sielh2lITQtmhcthVZKO04bpr1borTEaQln
        yfYlrUuGlszLLZVzlOPIFcm1yd2WeydPl3eTT5bfJd8p/1ABpaCnEKiQpbBf4aLCzFLqUtulrKVFS48v
        vacIK+opBimuVTyo2K84p6Ss5KGUrlSldEFpRpmm7KicpFyufEZ5WoWiYq/CVSlXOavylC5Ld6Kn0Cvp
        vfRZVUVVT1Whar3qgOqCmrZaqFq+WpvaQ3WCOkM9Xr1cvUd9VkNFw08jT6NF454mXpOhmai5V7NPc15L
        Wytca6tWp9aUtpy2l3audov2Ax2yjoPOGp0GnVu6GF2GbrLuPt0berCehV6iXo3edX1Y31Kfq79Pf9AA
        bWBtwDNoMBgxJBk6GWYathiOGdGMfI3yjTqNnhtrGEcZ7zLuM/5oYmGSYtJoct9UxtTbNN+02/R3Mz0z
        llmN2S1zsrm7+QbzLvMXy/SXcZbtX3bHgmLhZ7HVosfig6WVJd+y1XLaSsMq1qrWaoRBZQQwShiXrdHW
        ztYbrE9Zv7WxtBHYHLf5zdbQNtn2iO3Ucu3lnOWNy8ft1OyYdvV2o/Z0+1j7A/ajDqoOTIcGh8eO6o5s
        xybHSSddpySno07PnU2c+c7tzvMuNi7rXM65Iq4erkWuA24ybqFu1W6P3NXcE9xb3Gc9LDzWepzzRHv6
        eO7yHPFS8mJ5NXvNelt5r/Pu9SH5BPtU+zz21fPl+3b7wX7efrv9HqzQXMFb0ekP/L38d/s/DNAOWBPw
        YyAmMCCwJvBJkGlQXlBfMCU4JvhI8OsQ55DSkPuhOqHC0J4wybDosOaw+XDX8LLw0QjjiHUR1yIVIrmR
        XVHYqLCopqi5lW4r96yciLaILoweXqW9KnvVldUKq1NWn46RjGHGnIhFx4bHHol9z/RnNjDn4rziauNm
        WS6svaxnbEd2OXuaY8cp40zG28WXxU8l2CXsTphOdEisSJzhunCruS+SPJPqkuaT/ZMPJX9KCU9pS8Wl
        xqae5Mnwknm9acpp2WmD6frphemja2zW7Fkzy/fhN2VAGasyugRU0c9Uv1BHuEU4lmmfWZP5Jiss60S2
        dDYvuz9HL2d7zmSue+63a1FrWWt78lTzNuWNrXNaV78eWh+3vmeD+oaCDRMbPTYe3kTYlLzpp3yT/LL8
        V5vDN3cXKBVsLBjf4rGlpVCikF84stV2a9021DbutoHt5turtn8sYhddLTYprih+X8IqufqN6TeV33za
        Eb9joNSydP9OzE7ezuFdDrsOl0mX5ZaN7/bb3VFOLy8qf7UnZs+VimUVdXsJe4V7Ryt9K7uqNKp2Vr2v
        Tqy+XeNc01arWLu9dn4fe9/Qfsf9rXVKdcV17w5wD9yp96jvaNBqqDiIOZh58EljWGPft4xvm5sUmoqb
        PhziHRo9HHS4t9mqufmI4pHSFrhF2DJ9NProje9cv+tqNWytb6O1FR8Dx4THnn4f+/3wcZ/jPScYJ1p/
        0Pyhtp3SXtQBdeR0zHYmdo52RXYNnvQ+2dNt293+o9GPh06pnqo5LXu69AzhTMGZT2dzz86dSz83cz7h
        /HhPTM/9CxEXbvUG9g5c9Ll4+ZL7pQt9Tn1nL9tdPnXF5srJq4yrndcsr3X0W/S3/2TxU/uA5UDHdavr
        XTesb3QPLh88M+QwdP6m681Lt7xuXbu94vbgcOjwnZHokdE77DtTd1PuvriXeW/h/sYH6AdFD6UeVjxS
        fNTws+7PbaOWo6fHXMf6Hwc/vj/OGn/2S8Yv7ycKnpCfVEyqTDZPmU2dmnafvvF05dOJZ+nPFmYKf5X+
        tfa5zvMffnP8rX82YnbiBf/Fp99LXsq/PPRq2aueuYC5R69TXy/MF72Rf3P4LeNt37vwd5MLWe+x7ys/
        6H7o/ujz8cGn1E+f/gUDmPP8usTo0wAAAAlwSFlzAAALEwAACxMBAJqcGAAAAzJJREFUOE9tkGtMknEY
        xd8yIjNJRE1nIOlqucL5Qde9NhNBEJEVrizLMqulI7WWecvVWrHMmpYmrszSmrrUpFVaKRq6ghdTaRkS
        dAHL7LYuX2zVTi/U6Pps57+z5znn9+FPACAqYzmEShRE1MbPItQJbEKT4DePlDFTTHJmtkXulT0g80rp
        kfmFXpPNJOokQUQVlXV0HN0/AOclXKJJHLBRmxxKmgrixp+Xbfs6Wrb926MCyfidDaE6dZz/+to4zv8B
        lUI2/ZyYqyJzpXh1oQgfW0vwqVnp1AfKO3aG3HjUxgVVUFnaH4ByAZuoFHCU9/IleFOdibHyzRg7tgYv
        GpUYbTiMlyVrqF0qXlO3vvx4qIScg46OC1C6MjCiMy3ys/1oIh4XxeDxKQVsnQ0YI2/iFdlO+UZYKzKo
        Gx/24kTc2hIxXhodGO4C1IiDSod2L4UpOxLm0q0Ytdtga1BipL0GI23VsDUewajtGYaPpzozQ7uW4KyY
        U+wCqOWzyAfpYTCmh2PkPgnrmRwMyBl4qq7Ek8vl6Ke8tToPdqMOxh1heJDBQ+tq7h0XoC3B33x3FQsD
        hVLYDbehTwqEXjYd1pZTsDSdgE7GgH4dG/Y+LfrzxHBkr0tnDLkA18RMcwd/Msj8VbB2tEAjcEeX2BN6
        xQroFMucXiOcCqumFfq9UnTw6bgqYv4CqGO9e7pF09CzORwWvRZdiVx0Cj3QIZz2Ux7oXhsMK6mFNoWH
        2yJPqIXeXS5AQwzrgEEegB7BFDysL8PgpSpoRAx082nojqZBI/aCsbkaQ3Ul6BXQ0Udl6/msQhegJmr6
        7MFNc98PJAfDsD4E5qsXYGxrgu5oJvQlWTC2X4b5ynkYkrgYTA6BfsPct6rljGAXQDCTRuREMDMsmYvx
        ULGA+ulIDJ/Mgrm5ipIKw2WZ1C4CJsVCmHYuwuko1nZlpLuz++P5MYxNYb4Xh3NjMVachCf7E2DZJ3HK
        4R07U64AyTyfE/SJBOHuNuEfAJ3SnDDfKdnHpbyu3hzJO9OhpC8O9e6RvDkWz7vB86GnURmWM03N34Df
        x82HRnDnMycJw5mTYvwnE2zaz8PvA4D4Do7rKQv5AfG9AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="barBtnClose.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="barBtnClose.ItemAppearance.Normal.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 10pt</value>
  </data>
  <data name="bar1.Text" xml:space="preserve">
    <value>Tools</value>
  </data>
  <metadata name="barAndDockingController1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>134, 17</value>
  </metadata>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="barDockControlTop.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="barDockControlTop.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="barDockControlTop.Size" type="System.Drawing.Size, System.Drawing">
    <value>1036, 29</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Name" xml:space="preserve">
    <value>barDockControlTop</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="barDockControlBottom.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="barDockControlBottom.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 665</value>
  </data>
  <data name="barDockControlBottom.Size" type="System.Drawing.Size, System.Drawing">
    <value>1036, 0</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Name" xml:space="preserve">
    <value>barDockControlBottom</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="barDockControlLeft.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="barDockControlLeft.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 29</value>
  </data>
  <data name="barDockControlLeft.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 636</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Name" xml:space="preserve">
    <value>barDockControlLeft</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="barDockControlRight.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Right</value>
  </data>
  <data name="barDockControlRight.Location" type="System.Drawing.Point, System.Drawing">
    <value>1036, 29</value>
  </data>
  <data name="barDockControlRight.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 636</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Name" xml:space="preserve">
    <value>barDockControlRight</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>25</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>1036, 665</value>
  </data>
  <data name="groupControl3.AppearanceCaption.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 10pt, style=Bold</value>
  </data>
  <data name="txtDescription.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="txtDescription.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="txtDescription.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="txtDescription.Size" type="System.Drawing.Size, System.Drawing">
    <value>9, 4</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="txtDescription.TabIndex" type="System.Int32, mscorlib">
    <value>200</value>
  </data>
  <data name="&gt;&gt;txtDescription.Name" xml:space="preserve">
    <value>txtDescription</value>
  </data>
  <data name="&gt;&gt;txtDescription.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.MemoEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtDescription.Parent" xml:space="preserve">
    <value>groupControl3</value>
  </data>
  <data name="&gt;&gt;txtDescription.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="groupControl3.Location" type="System.Drawing.Point, System.Drawing">
    <value>38, 660</value>
  </data>
  <data name="groupControl3.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="groupControl3.Size" type="System.Drawing.Size, System.Drawing">
    <value>13, 29</value>
  </data>
  <data name="groupControl3.TabIndex" type="System.Int32, mscorlib">
    <value>82</value>
  </data>
  <data name="groupControl3.Text" xml:space="preserve">
    <value>Desc</value>
  </data>
  <data name="groupControl3.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;groupControl3.Name" xml:space="preserve">
    <value>groupControl3</value>
  </data>
  <data name="&gt;&gt;groupControl3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GroupControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;groupControl3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupControl3.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="groupControl2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="groupControl2.AppearanceCaption.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 10pt, style=Bold</value>
  </data>
  <data name="layoutControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="layoutControl1.Appearance.Control.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Gray</value>
  </data>
  <data name="layoutControl1.AutoScroll" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbFltrTyp_SalesEmp.EditValue" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbFltrTyp_SalesEmp.Location" type="System.Drawing.Point, System.Drawing">
    <value>385, 557</value>
  </data>
  <assembly alias="DevExpress.Utils.v15.1" name="DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="cmbFltrTyp_SalesEmp.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="cmbFltrTyp_SalesEmp.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="cmbFltrTyp_SalesEmp.Properties.Items1" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbFltrTyp_SalesEmp.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_SalesEmp.Properties.Items3" xml:space="preserve">
    <value>Equals</value>
  </data>
  <data name="cmbFltrTyp_SalesEmp.Properties.Items4" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="cmbFltrTyp_SalesEmp.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_SalesEmp.Size" type="System.Drawing.Size, System.Drawing">
    <value>134, 20</value>
  </data>
  <data name="cmbFltrTyp_SalesEmp.TabIndex" type="System.Int32, mscorlib">
    <value>41</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_SalesEmp.Name" xml:space="preserve">
    <value>cmbFltrTyp_SalesEmp</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_SalesEmp.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_SalesEmp.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_SalesEmp.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="labelControl3.Location" type="System.Drawing.Point, System.Drawing">
    <value>104, 8</value>
  </data>
  <data name="labelControl3.Size" type="System.Drawing.Size, System.Drawing">
    <value>14, 13</value>
  </data>
  <data name="labelControl3.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="labelControl3.Text" xml:space="preserve">
    <value>M3</value>
  </data>
  <data name="&gt;&gt;labelControl3.Name" xml:space="preserve">
    <value>labelControl3</value>
  </data>
  <data name="&gt;&gt;labelControl3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl3.Parent" xml:space="preserve">
    <value>panelControl3</value>
  </data>
  <data name="&gt;&gt;labelControl3.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lkpMtrx3.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="lkpMtrx3.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 5</value>
  </data>
  <data name="lkpMtrx3.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpMtrx3.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn27.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="gridColumn27.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn27.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn26.Caption" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="gridColumn26.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn26.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn32.Caption" xml:space="preserve">
    <value>MatrixId</value>
  </data>
  <data name="lkpMtrx3.Size" type="System.Drawing.Size, System.Drawing">
    <value>91, 20</value>
  </data>
  <data name="lkpMtrx3.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="&gt;&gt;lkpMtrx3.Name" xml:space="preserve">
    <value>lkpMtrx3</value>
  </data>
  <data name="&gt;&gt;lkpMtrx3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpMtrx3.Parent" xml:space="preserve">
    <value>panelControl3</value>
  </data>
  <data name="&gt;&gt;lkpMtrx3.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="labelControl4.Location" type="System.Drawing.Point, System.Drawing">
    <value>101, 31</value>
  </data>
  <data name="labelControl4.Size" type="System.Drawing.Size, System.Drawing">
    <value>33, 13</value>
  </data>
  <data name="labelControl4.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="labelControl4.Text" xml:space="preserve">
    <value>M Item</value>
  </data>
  <data name="&gt;&gt;labelControl4.Name" xml:space="preserve">
    <value>labelControl4</value>
  </data>
  <data name="&gt;&gt;labelControl4.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl4.Parent" xml:space="preserve">
    <value>panelControl3</value>
  </data>
  <data name="&gt;&gt;labelControl4.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="lkpMtrxD3.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="lkpMtrxD3.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 28</value>
  </data>
  <data name="lkpMtrxD3.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpMtrxD3.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn51.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="gridColumn51.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn51.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn50.Caption" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="gridColumn50.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn50.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn52.Caption" xml:space="preserve">
    <value>MatrixDetailId</value>
  </data>
  <data name="lkpMtrxD3.Size" type="System.Drawing.Size, System.Drawing">
    <value>91, 20</value>
  </data>
  <data name="lkpMtrxD3.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;lkpMtrxD3.Name" xml:space="preserve">
    <value>lkpMtrxD3</value>
  </data>
  <data name="&gt;&gt;lkpMtrxD3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpMtrxD3.Parent" xml:space="preserve">
    <value>panelControl3</value>
  </data>
  <data name="&gt;&gt;lkpMtrxD3.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="panelControl3.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 88</value>
  </data>
  <data name="panelControl3.Size" type="System.Drawing.Size, System.Drawing">
    <value>193, 49</value>
  </data>
  <data name="panelControl3.TabIndex" type="System.Int32, mscorlib">
    <value>89</value>
  </data>
  <data name="&gt;&gt;panelControl3.Name" xml:space="preserve">
    <value>panelControl3</value>
  </data>
  <data name="&gt;&gt;panelControl3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.PanelControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;panelControl3.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;panelControl3.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="labelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>104, 8</value>
  </data>
  <data name="labelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>14, 13</value>
  </data>
  <data name="labelControl1.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="labelControl1.Text" xml:space="preserve">
    <value>M2</value>
  </data>
  <data name="&gt;&gt;labelControl1.Name" xml:space="preserve">
    <value>labelControl1</value>
  </data>
  <data name="&gt;&gt;labelControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl1.Parent" xml:space="preserve">
    <value>panelControl2</value>
  </data>
  <data name="&gt;&gt;labelControl1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lkpMtrx2.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="lkpMtrx2.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 5</value>
  </data>
  <data name="lkpMtrx2.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpMtrx2.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn25.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="gridColumn25.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn25.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn24.Caption" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="gridColumn24.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn24.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn29.Caption" xml:space="preserve">
    <value>MatrixId</value>
  </data>
  <data name="lkpMtrx2.Size" type="System.Drawing.Size, System.Drawing">
    <value>91, 20</value>
  </data>
  <data name="lkpMtrx2.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="&gt;&gt;lkpMtrx2.Name" xml:space="preserve">
    <value>lkpMtrx2</value>
  </data>
  <data name="&gt;&gt;lkpMtrx2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpMtrx2.Parent" xml:space="preserve">
    <value>panelControl2</value>
  </data>
  <data name="&gt;&gt;lkpMtrx2.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="labelControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>101, 31</value>
  </data>
  <data name="labelControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>33, 13</value>
  </data>
  <data name="labelControl2.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="labelControl2.Text" xml:space="preserve">
    <value>M Item</value>
  </data>
  <data name="&gt;&gt;labelControl2.Name" xml:space="preserve">
    <value>labelControl2</value>
  </data>
  <data name="&gt;&gt;labelControl2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl2.Parent" xml:space="preserve">
    <value>panelControl2</value>
  </data>
  <data name="&gt;&gt;labelControl2.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="lkpMtrxD2.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="lkpMtrxD2.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 28</value>
  </data>
  <data name="lkpMtrxD2.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpMtrxD2.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn48.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="gridColumn48.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn48.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn47.Caption" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="gridColumn47.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn47.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn49.Caption" xml:space="preserve">
    <value>MatrixDetailId</value>
  </data>
  <data name="lkpMtrxD2.Size" type="System.Drawing.Size, System.Drawing">
    <value>91, 20</value>
  </data>
  <data name="lkpMtrxD2.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;lkpMtrxD2.Name" xml:space="preserve">
    <value>lkpMtrxD2</value>
  </data>
  <data name="&gt;&gt;lkpMtrxD2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpMtrxD2.Parent" xml:space="preserve">
    <value>panelControl2</value>
  </data>
  <data name="&gt;&gt;lkpMtrxD2.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="panelControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>207, 88</value>
  </data>
  <data name="panelControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>169, 49</value>
  </data>
  <data name="panelControl2.TabIndex" type="System.Int32, mscorlib">
    <value>88</value>
  </data>
  <data name="&gt;&gt;panelControl2.Name" xml:space="preserve">
    <value>panelControl2</value>
  </data>
  <data name="&gt;&gt;panelControl2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.PanelControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;panelControl2.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;panelControl2.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="lblM1.Location" type="System.Drawing.Point, System.Drawing">
    <value>104, 8</value>
  </data>
  <data name="lblM1.Size" type="System.Drawing.Size, System.Drawing">
    <value>14, 13</value>
  </data>
  <data name="lblM1.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="lblM1.Text" xml:space="preserve">
    <value>M1</value>
  </data>
  <data name="&gt;&gt;lblM1.Name" xml:space="preserve">
    <value>lblM1</value>
  </data>
  <data name="&gt;&gt;lblM1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lblM1.Parent" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="&gt;&gt;lblM1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lkpMtrx1.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="lkpMtrx1.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 5</value>
  </data>
  <data name="lkpMtrx1.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpMtrx1.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn45.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="gridColumn45.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn45.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn44.Caption" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="gridColumn44.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn44.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn46.Caption" xml:space="preserve">
    <value>MatrixId</value>
  </data>
  <data name="lkpMtrx1.Size" type="System.Drawing.Size, System.Drawing">
    <value>91, 20</value>
  </data>
  <data name="lkpMtrx1.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="&gt;&gt;lkpMtrx1.Name" xml:space="preserve">
    <value>lkpMtrx1</value>
  </data>
  <data name="&gt;&gt;lkpMtrx1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpMtrx1.Parent" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="&gt;&gt;lkpMtrx1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="lblM1D.Location" type="System.Drawing.Point, System.Drawing">
    <value>101, 31</value>
  </data>
  <data name="lblM1D.Size" type="System.Drawing.Size, System.Drawing">
    <value>33, 13</value>
  </data>
  <data name="lblM1D.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="lblM1D.Text" xml:space="preserve">
    <value>M Item</value>
  </data>
  <data name="&gt;&gt;lblM1D.Name" xml:space="preserve">
    <value>lblM1D</value>
  </data>
  <data name="&gt;&gt;lblM1D.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lblM1D.Parent" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="&gt;&gt;lblM1D.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="lkpMtrxD1.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="lkpMtrxD1.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 28</value>
  </data>
  <data name="lkpMtrxD1.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpMtrxD1.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn23.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="gridColumn23.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn23.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn22.Caption" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="gridColumn22.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn22.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn28.Caption" xml:space="preserve">
    <value>MatrixDetailId</value>
  </data>
  <data name="lkpMtrxD1.Size" type="System.Drawing.Size, System.Drawing">
    <value>91, 20</value>
  </data>
  <data name="lkpMtrxD1.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;lkpMtrxD1.Name" xml:space="preserve">
    <value>lkpMtrxD1</value>
  </data>
  <data name="&gt;&gt;lkpMtrxD1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpMtrxD1.Parent" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="&gt;&gt;lkpMtrxD1.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="panelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>380, 88</value>
  </data>
  <data name="panelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>140, 49</value>
  </data>
  <data name="panelControl1.TabIndex" type="System.Int32, mscorlib">
    <value>87</value>
  </data>
  <data name="&gt;&gt;panelControl1.Name" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="&gt;&gt;panelControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.PanelControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;panelControl1.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;panelControl1.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="lkpVenGroup.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="lkpVenGroup.Location" type="System.Drawing.Point, System.Drawing">
    <value>211, 303</value>
  </data>
  <data name="lkpVenGroup.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpVenGroup.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn42.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="gridColumn42.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn42.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn39.Caption" xml:space="preserve">
    <value>Group</value>
  </data>
  <data name="gridColumn39.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn39.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn43.Caption" xml:space="preserve">
    <value>CustomerGroupId</value>
  </data>
  <data name="lkpVenGroup.Size" type="System.Drawing.Size, System.Drawing">
    <value>306, 20</value>
  </data>
  <data name="lkpVenGroup.TabIndex" type="System.Int32, mscorlib">
    <value>88</value>
  </data>
  <data name="&gt;&gt;lkpVenGroup.Name" xml:space="preserve">
    <value>lkpVenGroup</value>
  </data>
  <data name="&gt;&gt;lkpVenGroup.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpVenGroup.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;lkpVenGroup.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="txtQC.Location" type="System.Drawing.Point, System.Drawing">
    <value>384, 603</value>
  </data>
  <data name="txtQC.Size" type="System.Drawing.Size, System.Drawing">
    <value>133, 20</value>
  </data>
  <data name="txtQC.TabIndex" type="System.Int32, mscorlib">
    <value>44</value>
  </data>
  <data name="&gt;&gt;txtQC.Name" xml:space="preserve">
    <value>txtQC</value>
  </data>
  <data name="&gt;&gt;txtQC.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtQC.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;txtQC.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="lkpEmpGroup.Location" type="System.Drawing.Point, System.Drawing">
    <value>212, 534</value>
  </data>
  <data name="lkpEmpGroup.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpEmpGroup.Properties.Columns" xml:space="preserve">
    <value>GroupNameAr</value>
  </data>
  <data name="lkpEmpGroup.Properties.Columns1" xml:space="preserve">
    <value>Empolyee Group</value>
  </data>
  <data name="lkpEmpGroup.Properties.Columns2" xml:space="preserve">
    <value>GroupId</value>
  </data>
  <data name="lkpEmpGroup.Properties.Columns3" xml:space="preserve">
    <value>GroupId</value>
  </data>
  <data name="lkpEmpGroup.Properties.Columns4" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="lkpEmpGroup.Properties.Columns5" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpEmpGroup.Properties.Columns6" xml:space="preserve">
    <value />
  </data>
  <data name="lkpEmpGroup.Properties.Columns7" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpEmpGroup.Properties.Columns8" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpEmpGroup.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkpEmpGroup.Size" type="System.Drawing.Size, System.Drawing">
    <value>162, 20</value>
  </data>
  <data name="lkpEmpGroup.TabIndex" type="System.Int32, mscorlib">
    <value>40</value>
  </data>
  <data name="&gt;&gt;lkpEmpGroup.Name" xml:space="preserve">
    <value>lkpEmpGroup</value>
  </data>
  <data name="&gt;&gt;lkpEmpGroup.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpEmpGroup.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;lkpEmpGroup.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="cmbFltrTyp_EmpGroup.EditValue" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbFltrTyp_EmpGroup.Location" type="System.Drawing.Point, System.Drawing">
    <value>384, 534</value>
  </data>
  <data name="cmbFltrTyp_EmpGroup.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="cmbFltrTyp_EmpGroup.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="cmbFltrTyp_EmpGroup.Properties.Items1" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbFltrTyp_EmpGroup.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_EmpGroup.Properties.Items3" xml:space="preserve">
    <value>Equals</value>
  </data>
  <data name="cmbFltrTyp_EmpGroup.Properties.Items4" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="cmbFltrTyp_EmpGroup.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_EmpGroup.Size" type="System.Drawing.Size, System.Drawing">
    <value>132, 20</value>
  </data>
  <data name="cmbFltrTyp_EmpGroup.TabIndex" type="System.Int32, mscorlib">
    <value>39</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_EmpGroup.Name" xml:space="preserve">
    <value>cmbFltrTyp_EmpGroup</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_EmpGroup.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_EmpGroup.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_EmpGroup.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="chkLstInvBooks.Location" type="System.Drawing.Point, System.Drawing">
    <value>211, 257</value>
  </data>
  <data name="chkLstInvBooks.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="chkLstInvBooks.Size" type="System.Drawing.Size, System.Drawing">
    <value>162, 20</value>
  </data>
  <data name="chkLstInvBooks.TabIndex" type="System.Int32, mscorlib">
    <value>88</value>
  </data>
  <data name="&gt;&gt;chkLstInvBooks.Name" xml:space="preserve">
    <value>chkLstInvBooks</value>
  </data>
  <data name="&gt;&gt;chkLstInvBooks.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckedComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;chkLstInvBooks.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;chkLstInvBooks.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="cmbFltrTyp_InvBook.EditValue" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbFltrTyp_InvBook.Location" type="System.Drawing.Point, System.Drawing">
    <value>383, 257</value>
  </data>
  <data name="cmbFltrTyp_InvBook.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="cmbFltrTyp_InvBook.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="cmbFltrTyp_InvBook.Properties.Items1" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbFltrTyp_InvBook.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_InvBook.Properties.Items3" xml:space="preserve">
    <value>Equals</value>
  </data>
  <data name="cmbFltrTyp_InvBook.Properties.Items4" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="cmbFltrTyp_InvBook.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_InvBook.Size" type="System.Drawing.Size, System.Drawing">
    <value>134, 20</value>
  </data>
  <data name="cmbFltrTyp_InvBook.TabIndex" type="System.Int32, mscorlib">
    <value>87</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_InvBook.Name" xml:space="preserve">
    <value>cmbFltrTyp_InvBook</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_InvBook.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_InvBook.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_InvBook.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="txtSellPrice2.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txtSellPrice2.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 626</value>
  </data>
  <data name="txtSellPrice2.Properties.Mask.EditMask" xml:space="preserve">
    <value>n2</value>
  </data>
  <data name="txtSellPrice2.Size" type="System.Drawing.Size, System.Drawing">
    <value>189, 20</value>
  </data>
  <data name="txtSellPrice2.TabIndex" type="System.Int32, mscorlib">
    <value>47</value>
  </data>
  <data name="&gt;&gt;txtSellPrice2.Name" xml:space="preserve">
    <value>txtSellPrice2</value>
  </data>
  <data name="&gt;&gt;txtSellPrice2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtSellPrice2.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;txtSellPrice2.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="lkpCategory.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkpCategory.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpCategory.Location" type="System.Drawing.Point, System.Drawing">
    <value>231, 19</value>
  </data>
  <data name="lkpCategory.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpCategory.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkpCategory.Size" type="System.Drawing.Size, System.Drawing">
    <value>141, 20</value>
  </data>
  <data name="lkpCategory.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;lkpCategory.Name" xml:space="preserve">
    <value>lkpCategory</value>
  </data>
  <data name="&gt;&gt;lkpCategory.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpCategory.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;lkpCategory.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="cmbFltrTyp_Cat.EditValue" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbFltrTyp_Cat.Location" type="System.Drawing.Point, System.Drawing">
    <value>382, 19</value>
  </data>
  <data name="cmbFltrTyp_Cat.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="cmbFltrTyp_Cat.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="cmbFltrTyp_Cat.Properties.Items1" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbFltrTyp_Cat.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_Cat.Properties.Items3" xml:space="preserve">
    <value>Equal</value>
  </data>
  <data name="cmbFltrTyp_Cat.Properties.Items4" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="cmbFltrTyp_Cat.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_Cat.Size" type="System.Drawing.Size, System.Drawing">
    <value>134, 20</value>
  </data>
  <data name="cmbFltrTyp_Cat.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Cat.Name" xml:space="preserve">
    <value>cmbFltrTyp_Cat</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Cat.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Cat.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Cat.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="txtSellPrice1.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txtSellPrice1.Location" type="System.Drawing.Point, System.Drawing">
    <value>212, 626</value>
  </data>
  <data name="txtSellPrice1.Properties.Mask.EditMask" xml:space="preserve">
    <value>n2</value>
  </data>
  <data name="txtSellPrice1.Size" type="System.Drawing.Size, System.Drawing">
    <value>162, 20</value>
  </data>
  <data name="txtSellPrice1.TabIndex" type="System.Int32, mscorlib">
    <value>46</value>
  </data>
  <data name="&gt;&gt;txtSellPrice1.Name" xml:space="preserve">
    <value>txtSellPrice1</value>
  </data>
  <data name="&gt;&gt;txtSellPrice1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtSellPrice1.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;txtSellPrice1.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="cmbFltrTyp_SellPrice.EditValue" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbFltrTyp_SellPrice.Location" type="System.Drawing.Point, System.Drawing">
    <value>384, 626</value>
  </data>
  <data name="cmbFltrTyp_SellPrice.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="cmbFltrTyp_SellPrice.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="cmbFltrTyp_SellPrice.Properties.Items1" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbFltrTyp_SellPrice.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_SellPrice.Properties.Items3" xml:space="preserve">
    <value>Equals</value>
  </data>
  <data name="cmbFltrTyp_SellPrice.Properties.Items4" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="cmbFltrTyp_SellPrice.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_SellPrice.Properties.Items6" xml:space="preserve">
    <value>Between</value>
  </data>
  <data name="cmbFltrTyp_SellPrice.Properties.Items7" type="System.Byte, mscorlib">
    <value>2</value>
  </data>
  <data name="cmbFltrTyp_SellPrice.Properties.Items8" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_SellPrice.Size" type="System.Drawing.Size, System.Drawing">
    <value>133, 20</value>
  </data>
  <data name="cmbFltrTyp_SellPrice.TabIndex" type="System.Int32, mscorlib">
    <value>45</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_SellPrice.Name" xml:space="preserve">
    <value>cmbFltrTyp_SellPrice</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_SellPrice.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_SellPrice.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_SellPrice.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="lkpJoDept.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="lkpJoDept.Location" type="System.Drawing.Point, System.Drawing">
    <value>10, 511</value>
  </data>
  <data name="lkpJoDept.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpJoDept.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn40.Caption" xml:space="preserve">
    <value>Department</value>
  </data>
  <data name="gridColumn40.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn40.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn41.Caption" xml:space="preserve">
    <value>DeptId</value>
  </data>
  <data name="lkpJoDept.Size" type="System.Drawing.Size, System.Drawing">
    <value>142, 20</value>
  </data>
  <data name="lkpJoDept.TabIndex" type="System.Int32, mscorlib">
    <value>38</value>
  </data>
  <data name="&gt;&gt;lkpJoDept.Name" xml:space="preserve">
    <value>lkpJoDept</value>
  </data>
  <data name="&gt;&gt;lkpJoDept.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpJoDept.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;lkpJoDept.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="lkpJoState.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="lkpJoState.Location" type="System.Drawing.Point, System.Drawing">
    <value>208, 511</value>
  </data>
  <data name="lkpJoState.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpJoState.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn37.Caption" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="gridColumn37.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn37.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn38.Caption" xml:space="preserve">
    <value>StatusId</value>
  </data>
  <data name="lkpJoState.Size" type="System.Drawing.Size, System.Drawing">
    <value>89, 20</value>
  </data>
  <data name="lkpJoState.TabIndex" type="System.Int32, mscorlib">
    <value>37</value>
  </data>
  <data name="&gt;&gt;lkpJoState.Name" xml:space="preserve">
    <value>lkpJoState</value>
  </data>
  <data name="&gt;&gt;lkpJoState.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpJoState.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;lkpJoState.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="lkpJoPriority.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="lkpJoPriority.Location" type="System.Drawing.Point, System.Drawing">
    <value>353, 511</value>
  </data>
  <data name="lkpJoPriority.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpJoPriority.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn34.Caption" xml:space="preserve">
    <value>Priority</value>
  </data>
  <data name="gridColumn34.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn34.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn35.Caption" xml:space="preserve">
    <value>PriorityId</value>
  </data>
  <data name="lkpJoPriority.Size" type="System.Drawing.Size, System.Drawing">
    <value>114, 20</value>
  </data>
  <data name="lkpJoPriority.TabIndex" type="System.Int32, mscorlib">
    <value>36</value>
  </data>
  <data name="&gt;&gt;lkpJoPriority.Name" xml:space="preserve">
    <value>lkpJoPriority</value>
  </data>
  <data name="&gt;&gt;lkpJoPriority.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpJoPriority.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;lkpJoPriority.ZOrder" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="txtWidth.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtWidth.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txtWidth.Location" type="System.Drawing.Point, System.Drawing">
    <value>168, 141</value>
  </data>
  <data name="txtWidth.Properties.Mask.EditMask" xml:space="preserve">
    <value>n2</value>
  </data>
  <data name="txtWidth.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 20</value>
  </data>
  <data name="txtWidth.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="&gt;&gt;txtWidth.Name" xml:space="preserve">
    <value>txtWidth</value>
  </data>
  <data name="&gt;&gt;txtWidth.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtWidth.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;txtWidth.ZOrder" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="cmbItemType.EditValue" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbItemType.Location" type="System.Drawing.Point, System.Drawing">
    <value>382, 165</value>
  </data>
  <data name="cmbItemType.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="cmbItemType.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="cmbItemType.Properties.Items1" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbItemType.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbItemType.Properties.Items3" xml:space="preserve">
    <value>Inventory</value>
  </data>
  <data name="cmbItemType.Properties.Items4" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbItemType.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbItemType.Properties.Items6" xml:space="preserve">
    <value>Service</value>
  </data>
  <data name="cmbItemType.Properties.Items7" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="cmbItemType.Properties.Items8" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbItemType.Properties.Items9" xml:space="preserve">
    <value>Product</value>
  </data>
  <data name="cmbItemType.Properties.Items10" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="cmbItemType.Properties.Items11" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbItemType.Size" type="System.Drawing.Size, System.Drawing">
    <value>134, 20</value>
  </data>
  <data name="cmbItemType.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="&gt;&gt;cmbItemType.Name" xml:space="preserve">
    <value>cmbItemType</value>
  </data>
  <data name="&gt;&gt;cmbItemType.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cmbItemType.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;cmbItemType.ZOrder" xml:space="preserve">
    <value>23</value>
  </data>
  <data name="lkpAccount.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="lkpAccount.Location" type="System.Drawing.Point, System.Drawing">
    <value>211, 442</value>
  </data>
  <data name="lkpAccount.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpAccount.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="col_ParentActId.Caption" xml:space="preserve">
    <value>ParentActId</value>
  </data>
  <data name="col_CostCenter.Caption" xml:space="preserve">
    <value>CostCenter</value>
  </data>
  <data name="col_AcNumber.Caption" xml:space="preserve">
    <value>Account Number</value>
  </data>
  <data name="col_AcNumber.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_AcNumber.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_AcNumber.Width" type="System.Int32, mscorlib">
    <value>252</value>
  </data>
  <data name="col_AcNameAr.Caption" xml:space="preserve">
    <value>Account Name</value>
  </data>
  <data name="col_AcNameAr.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_AcNameAr.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="col_AcNameAr.Width" type="System.Int32, mscorlib">
    <value>810</value>
  </data>
  <data name="col_AccountId.Caption" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="lkpAccount.Size" type="System.Drawing.Size, System.Drawing">
    <value>306, 20</value>
  </data>
  <data name="lkpAccount.TabIndex" type="System.Int32, mscorlib">
    <value>32</value>
  </data>
  <data name="&gt;&gt;lkpAccount.Name" xml:space="preserve">
    <value>lkpAccount</value>
  </data>
  <data name="&gt;&gt;lkpAccount.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpAccount.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;lkpAccount.ZOrder" xml:space="preserve">
    <value>24</value>
  </data>
  <data name="txtLength.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtLength.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txtLength.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 141</value>
  </data>
  <data name="txtLength.Properties.Mask.EditMask" xml:space="preserve">
    <value>n2</value>
  </data>
  <data name="txtLength.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 20</value>
  </data>
  <data name="txtLength.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="&gt;&gt;txtLength.Name" xml:space="preserve">
    <value>txtLength</value>
  </data>
  <data name="&gt;&gt;txtLength.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtLength.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;txtLength.ZOrder" xml:space="preserve">
    <value>25</value>
  </data>
  <data name="lkpMtrxParentId.EditValue" xml:space="preserve">
    <value>`</value>
  </data>
  <data name="lkpMtrxParentId.Location" type="System.Drawing.Point, System.Drawing">
    <value>210, 65</value>
  </data>
  <data name="lkpMtrxParentId.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpMtrxParentId.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn30.Caption" xml:space="preserve">
    <value>Item Name</value>
  </data>
  <data name="gridColumn30.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn30.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn31.Caption" xml:space="preserve">
    <value>ItemId</value>
  </data>
  <data name="lkpMtrxParentId.Size" type="System.Drawing.Size, System.Drawing">
    <value>307, 20</value>
  </data>
  <data name="lkpMtrxParentId.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="&gt;&gt;lkpMtrxParentId.Name" xml:space="preserve">
    <value>lkpMtrxParentId</value>
  </data>
  <data name="&gt;&gt;lkpMtrxParentId.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpMtrxParentId.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;lkpMtrxParentId.ZOrder" xml:space="preserve">
    <value>26</value>
  </data>
  <data name="lkpCustGroup.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="lkpCustGroup.Location" type="System.Drawing.Point, System.Drawing">
    <value>211, 280</value>
  </data>
  <data name="lkpCustGroup.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpCustGroup.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn20.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="gridColumn20.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn20.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn17.Caption" xml:space="preserve">
    <value>Group</value>
  </data>
  <data name="gridColumn17.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn17.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn21.Caption" xml:space="preserve">
    <value>CustomerGroupId</value>
  </data>
  <data name="lkpCustGroup.Size" type="System.Drawing.Size, System.Drawing">
    <value>306, 20</value>
  </data>
  <data name="lkpCustGroup.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="&gt;&gt;lkpCustGroup.Name" xml:space="preserve">
    <value>lkpCustGroup</value>
  </data>
  <data name="&gt;&gt;lkpCustGroup.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpCustGroup.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;lkpCustGroup.ZOrder" xml:space="preserve">
    <value>27</value>
  </data>
  <data name="txtBatch.Location" type="System.Drawing.Point, System.Drawing">
    <value>384, 580</value>
  </data>
  <data name="txtBatch.Size" type="System.Drawing.Size, System.Drawing">
    <value>132, 20</value>
  </data>
  <data name="txtBatch.TabIndex" type="System.Int32, mscorlib">
    <value>43</value>
  </data>
  <data name="&gt;&gt;txtBatch.Name" xml:space="preserve">
    <value>txtBatch</value>
  </data>
  <data name="&gt;&gt;txtBatch.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtBatch.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;txtBatch.ZOrder" xml:space="preserve">
    <value>28</value>
  </data>
  <data name="lkpSalesEmp.Location" type="System.Drawing.Point, System.Drawing">
    <value>213, 557</value>
  </data>
  <data name="lkpSalesEmp.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpSalesEmp.Properties.Columns" xml:space="preserve">
    <value>EmpName</value>
  </data>
  <data name="lkpSalesEmp.Properties.Columns1" xml:space="preserve">
    <value>Empolyee</value>
  </data>
  <data name="lkpSalesEmp.Properties.Columns2" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="lkpSalesEmp.Properties.Columns3" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="lkpSalesEmp.Properties.Columns4" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpSalesEmp.Properties.Columns5" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpSalesEmp.Properties.Columns6" xml:space="preserve">
    <value />
  </data>
  <data name="lkpSalesEmp.Properties.Columns7" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpSalesEmp.Properties.Columns8" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpSalesEmp.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkpSalesEmp.Size" type="System.Drawing.Size, System.Drawing">
    <value>162, 20</value>
  </data>
  <data name="lkpSalesEmp.TabIndex" type="System.Int32, mscorlib">
    <value>42</value>
  </data>
  <data name="&gt;&gt;lkpSalesEmp.Name" xml:space="preserve">
    <value>lkpSalesEmp</value>
  </data>
  <data name="&gt;&gt;lkpSalesEmp.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpSalesEmp.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;lkpSalesEmp.ZOrder" xml:space="preserve">
    <value>29</value>
  </data>
  <data name="lkpUser.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="lkpUser.Location" type="System.Drawing.Point, System.Drawing">
    <value>211, 488</value>
  </data>
  <data name="lkpUser.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpUser.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn18.Caption" xml:space="preserve">
    <value>User Name</value>
  </data>
  <data name="gridColumn18.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn18.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn19.Caption" xml:space="preserve">
    <value>UserId</value>
  </data>
  <data name="lkpUser.Size" type="System.Drawing.Size, System.Drawing">
    <value>163, 20</value>
  </data>
  <data name="lkpUser.TabIndex" type="System.Int32, mscorlib">
    <value>35</value>
  </data>
  <data name="&gt;&gt;lkpUser.Name" xml:space="preserve">
    <value>lkpUser</value>
  </data>
  <data name="&gt;&gt;lkpUser.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpUser.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;lkpUser.ZOrder" xml:space="preserve">
    <value>30</value>
  </data>
  <data name="cmbFltrTyp_User.EditValue" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbFltrTyp_User.Location" type="System.Drawing.Point, System.Drawing">
    <value>384, 488</value>
  </data>
  <data name="cmbFltrTyp_User.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="cmbFltrTyp_User.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="cmbFltrTyp_User.Properties.Items1" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbFltrTyp_User.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_User.Properties.Items3" xml:space="preserve">
    <value>Equal</value>
  </data>
  <data name="cmbFltrTyp_User.Properties.Items4" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="cmbFltrTyp_User.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_User.Size" type="System.Drawing.Size, System.Drawing">
    <value>132, 20</value>
  </data>
  <data name="cmbFltrTyp_User.TabIndex" type="System.Int32, mscorlib">
    <value>34</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_User.Name" xml:space="preserve">
    <value>cmbFltrTyp_User</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_User.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_User.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_User.ZOrder" xml:space="preserve">
    <value>31</value>
  </data>
  <data name="lkpCustomList.Location" type="System.Drawing.Point, System.Drawing">
    <value>211, 465</value>
  </data>
  <data name="lkpCustomList.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpCustomList.Properties.Columns" xml:space="preserve">
    <value>CustomAccListId</value>
  </data>
  <data name="lkpCustomList.Properties.Columns1" xml:space="preserve">
    <value>CustomAccListId</value>
  </data>
  <data name="lkpCustomList.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpCustomList.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpCustomList.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkpCustomList.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpCustomList.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpCustomList.Properties.Columns7" xml:space="preserve">
    <value>CustomAccListName</value>
  </data>
  <data name="lkpCustomList.Properties.Columns8" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="lkpCustomList.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpCustomList.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpCustomList.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="lkpCustomList.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpCustomList.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkpCustomList.Properties.Columns14" xml:space="preserve">
    <value>CustomAccListCode</value>
  </data>
  <data name="lkpCustomList.Properties.Columns15" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="lkpCustomList.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpCustomList.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpCustomList.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="lkpCustomList.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpCustomList.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkpCustomList.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkpCustomList.Size" type="System.Drawing.Size, System.Drawing">
    <value>305, 20</value>
  </data>
  <data name="lkpCustomList.TabIndex" type="System.Int32, mscorlib">
    <value>33</value>
  </data>
  <data name="&gt;&gt;lkpCustomList.Name" xml:space="preserve">
    <value>lkpCustomList</value>
  </data>
  <data name="&gt;&gt;lkpCustomList.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpCustomList.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;lkpCustomList.ZOrder" xml:space="preserve">
    <value>32</value>
  </data>
  <data name="txtHeight.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtHeight.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="txtHeight.Location" type="System.Drawing.Point, System.Drawing">
    <value>341, 141</value>
  </data>
  <data name="txtHeight.Properties.Mask.EditMask" xml:space="preserve">
    <value>n2</value>
  </data>
  <data name="txtHeight.Size" type="System.Drawing.Size, System.Drawing">
    <value>119, 20</value>
  </data>
  <data name="txtHeight.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="&gt;&gt;txtHeight.Name" xml:space="preserve">
    <value>txtHeight</value>
  </data>
  <data name="&gt;&gt;txtHeight.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtHeight.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;txtHeight.ZOrder" xml:space="preserve">
    <value>33</value>
  </data>
  <data name="cmbProcess.EditValue" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbProcess.Location" type="System.Drawing.Point, System.Drawing">
    <value>211, 396</value>
  </data>
  <data name="cmbProcess.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="cmbProcess.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="cmbProcess.Properties.Items1" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbProcess.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbProcess.Properties.Items3" xml:space="preserve">
    <value>Purchase Invoice</value>
  </data>
  <data name="cmbProcess.Properties.Items4" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="cmbProcess.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbProcess.Properties.Items6" xml:space="preserve">
    <value>Sales Invoice</value>
  </data>
  <data name="cmbProcess.Properties.Items7" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="cmbProcess.Properties.Items8" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbProcess.Properties.Items9" xml:space="preserve">
    <value>Purchase Return</value>
  </data>
  <data name="cmbProcess.Properties.Items10" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="cmbProcess.Properties.Items11" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbProcess.Properties.Items12" xml:space="preserve">
    <value>Sales Return</value>
  </data>
  <data name="cmbProcess.Properties.Items13" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="cmbProcess.Properties.Items14" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbProcess.Properties.Items15" xml:space="preserve">
    <value>Add Adjustment</value>
  </data>
  <data name="cmbProcess.Properties.Items16" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="cmbProcess.Properties.Items17" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbProcess.Properties.Items18" xml:space="preserve">
    <value>Subtract Adjustment</value>
  </data>
  <data name="cmbProcess.Properties.Items19" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="cmbProcess.Properties.Items20" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbProcess.Properties.Items21" xml:space="preserve">
    <value>Damage</value>
  </data>
  <data name="cmbProcess.Properties.Items22" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="cmbProcess.Properties.Items23" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbProcess.Properties.Items24" xml:space="preserve">
    <value>Transfer From</value>
  </data>
  <data name="cmbProcess.Properties.Items25" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="cmbProcess.Properties.Items26" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbProcess.Properties.Items27" xml:space="preserve">
    <value>Transfer to</value>
  </data>
  <data name="cmbProcess.Properties.Items28" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="cmbProcess.Properties.Items29" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbProcess.Properties.Items30" xml:space="preserve">
    <value>Open Balance</value>
  </data>
  <data name="cmbProcess.Properties.Items31" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="cmbProcess.Properties.Items32" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbProcess.Properties.Items33" xml:space="preserve">
    <value>Manufacturing</value>
  </data>
  <data name="cmbProcess.Properties.Items34" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="cmbProcess.Properties.Items35" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbProcess.Properties.Items36" xml:space="preserve">
    <value>Receive Bill</value>
  </data>
  <data name="cmbProcess.Properties.Items37" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="cmbProcess.Properties.Items38" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbProcess.Properties.Items39" xml:space="preserve">
    <value>Outgoing Bill</value>
  </data>
  <data name="cmbProcess.Properties.Items40" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="cmbProcess.Properties.Items41" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbProcess.Properties.Items42" xml:space="preserve">
    <value>Sales Order</value>
  </data>
  <data name="cmbProcess.Properties.Items43" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="cmbProcess.Properties.Items44" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbProcess.Properties.Items45" xml:space="preserve">
    <value>Quality Control</value>
  </data>
  <data name="cmbProcess.Properties.Items46" type="System.Int32, mscorlib">
    <value>27</value>
  </data>
  <data name="cmbProcess.Properties.Items47" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbProcess.Size" type="System.Drawing.Size, System.Drawing">
    <value>306, 20</value>
  </data>
  <data name="cmbProcess.TabIndex" type="System.Int32, mscorlib">
    <value>30</value>
  </data>
  <data name="&gt;&gt;cmbProcess.Name" xml:space="preserve">
    <value>cmbProcess</value>
  </data>
  <data name="&gt;&gt;cmbProcess.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cmbProcess.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;cmbProcess.ZOrder" xml:space="preserve">
    <value>34</value>
  </data>
  <data name="lkpCostCenters.Location" type="System.Drawing.Point, System.Drawing">
    <value>211, 419</value>
  </data>
  <data name="lkpCostCenters.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpCostCenters.Properties.Columns" xml:space="preserve">
    <value>CostCenterId</value>
  </data>
  <data name="lkpCostCenters.Properties.Columns1" xml:space="preserve">
    <value>CostCenterId</value>
  </data>
  <data name="lkpCostCenters.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpCostCenters.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpCostCenters.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkpCostCenters.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpCostCenters.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpCostCenters.Properties.Columns7" xml:space="preserve">
    <value>CostCenterName</value>
  </data>
  <data name="lkpCostCenters.Properties.Columns8" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="lkpCostCenters.Properties.Columns9" xml:space="preserve">
    <value>CostCenterCode</value>
  </data>
  <data name="lkpCostCenters.Properties.Columns10" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="lkpCostCenters.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkpCostCenters.Size" type="System.Drawing.Size, System.Drawing">
    <value>306, 20</value>
  </data>
  <data name="lkpCostCenters.TabIndex" type="System.Int32, mscorlib">
    <value>31</value>
  </data>
  <data name="&gt;&gt;lkpCostCenters.Name" xml:space="preserve">
    <value>lkpCostCenters</value>
  </data>
  <data name="&gt;&gt;lkpCostCenters.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpCostCenters.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;lkpCostCenters.ZOrder" xml:space="preserve">
    <value>35</value>
  </data>
  <data name="lkpCustomer2.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="lkpCustomer2.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 350</value>
  </data>
  <data name="lkpCustomer2.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpCustomer2.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn14.Caption" xml:space="preserve">
    <value>Customer Code</value>
  </data>
  <data name="gridColumn14.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn14.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn15.Caption" xml:space="preserve">
    <value>Customer Name</value>
  </data>
  <data name="gridColumn15.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn15.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn16.Caption" xml:space="preserve">
    <value>Customer F Name</value>
  </data>
  <data name="gridColumn16.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn16.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpCustomer2.Size" type="System.Drawing.Size, System.Drawing">
    <value>188, 20</value>
  </data>
  <data name="lkpCustomer2.TabIndex" type="System.Int32, mscorlib">
    <value>27</value>
  </data>
  <data name="&gt;&gt;lkpCustomer2.Name" xml:space="preserve">
    <value>lkpCustomer2</value>
  </data>
  <data name="&gt;&gt;lkpCustomer2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpCustomer2.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;lkpCustomer2.ZOrder" xml:space="preserve">
    <value>36</value>
  </data>
  <data name="cmbFltrTyp_Customer.EditValue" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbFltrTyp_Customer.Location" type="System.Drawing.Point, System.Drawing">
    <value>384, 350</value>
  </data>
  <data name="cmbFltrTyp_Customer.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="cmbFltrTyp_Customer.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="cmbFltrTyp_Customer.Properties.Items1" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbFltrTyp_Customer.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_Customer.Properties.Items3" xml:space="preserve">
    <value>Equal</value>
  </data>
  <data name="cmbFltrTyp_Customer.Properties.Items4" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="cmbFltrTyp_Customer.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_Customer.Properties.Items6" xml:space="preserve">
    <value>Between</value>
  </data>
  <data name="cmbFltrTyp_Customer.Properties.Items7" type="System.Byte, mscorlib">
    <value>2</value>
  </data>
  <data name="cmbFltrTyp_Customer.Properties.Items8" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_Customer.Properties.Items9" xml:space="preserve">
    <value>كل من</value>
  </data>
  <data name="cmbFltrTyp_Customer.Properties.Items10" type="System.Byte, mscorlib">
    <value>3</value>
  </data>
  <data name="cmbFltrTyp_Customer.Properties.Items11" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_Customer.Size" type="System.Drawing.Size, System.Drawing">
    <value>133, 20</value>
  </data>
  <data name="cmbFltrTyp_Customer.TabIndex" type="System.Int32, mscorlib">
    <value>25</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Customer.Name" xml:space="preserve">
    <value>cmbFltrTyp_Customer</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Customer.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Customer.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Customer.ZOrder" xml:space="preserve">
    <value>37</value>
  </data>
  <data name="lkpCustomer1.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="lkpCustomer1.Location" type="System.Drawing.Point, System.Drawing">
    <value>211, 350</value>
  </data>
  <data name="lkpCustomer1.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpCustomer1.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn13.Caption" xml:space="preserve">
    <value>Customer Code</value>
  </data>
  <data name="gridColumn13.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn13.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn12.Caption" xml:space="preserve">
    <value>Customer Name</value>
  </data>
  <data name="gridColumn12.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn12.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn11.Caption" xml:space="preserve">
    <value>Customer F Name</value>
  </data>
  <data name="gridColumn11.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn11.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpCustomer1.Size" type="System.Drawing.Size, System.Drawing">
    <value>163, 20</value>
  </data>
  <data name="lkpCustomer1.TabIndex" type="System.Int32, mscorlib">
    <value>26</value>
  </data>
  <data name="&gt;&gt;lkpCustomer1.Name" xml:space="preserve">
    <value>lkpCustomer1</value>
  </data>
  <data name="&gt;&gt;lkpCustomer1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpCustomer1.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;lkpCustomer1.ZOrder" xml:space="preserve">
    <value>38</value>
  </data>
  <data name="cmbFltrTyp_Comp.EditValue" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbFltrTyp_Comp.Location" type="System.Drawing.Point, System.Drawing">
    <value>384, 373</value>
  </data>
  <data name="cmbFltrTyp_Comp.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="cmbFltrTyp_Comp.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="cmbFltrTyp_Comp.Properties.Items1" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbFltrTyp_Comp.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_Comp.Properties.Items3" xml:space="preserve">
    <value>Equal</value>
  </data>
  <data name="cmbFltrTyp_Comp.Properties.Items4" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="cmbFltrTyp_Comp.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_Comp.Size" type="System.Drawing.Size, System.Drawing">
    <value>133, 20</value>
  </data>
  <data name="cmbFltrTyp_Comp.TabIndex" type="System.Int32, mscorlib">
    <value>28</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Comp.Name" xml:space="preserve">
    <value>cmbFltrTyp_Comp</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Comp.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Comp.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Comp.ZOrder" xml:space="preserve">
    <value>39</value>
  </data>
  <data name="cmbFltrTyp_Vendor.EditValue" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbFltrTyp_Vendor.Location" type="System.Drawing.Point, System.Drawing">
    <value>384, 327</value>
  </data>
  <data name="cmbFltrTyp_Vendor.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="cmbFltrTyp_Vendor.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="cmbFltrTyp_Vendor.Properties.Items1" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbFltrTyp_Vendor.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_Vendor.Properties.Items3" xml:space="preserve">
    <value>Equal</value>
  </data>
  <data name="cmbFltrTyp_Vendor.Properties.Items4" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="cmbFltrTyp_Vendor.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_Vendor.Properties.Items6" xml:space="preserve">
    <value>Between</value>
  </data>
  <data name="cmbFltrTyp_Vendor.Properties.Items7" type="System.Byte, mscorlib">
    <value>2</value>
  </data>
  <data name="cmbFltrTyp_Vendor.Properties.Items8" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_Vendor.Properties.Items9" xml:space="preserve">
    <value>كل من</value>
  </data>
  <data name="cmbFltrTyp_Vendor.Properties.Items10" type="System.Byte, mscorlib">
    <value>3</value>
  </data>
  <data name="cmbFltrTyp_Vendor.Properties.Items11" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_Vendor.Size" type="System.Drawing.Size, System.Drawing">
    <value>133, 20</value>
  </data>
  <data name="cmbFltrTyp_Vendor.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Vendor.Name" xml:space="preserve">
    <value>cmbFltrTyp_Vendor</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Vendor.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Vendor.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Vendor.ZOrder" xml:space="preserve">
    <value>40</value>
  </data>
  <data name="cmbFltrTyp_Store.EditValue" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbFltrTyp_Store.Location" type="System.Drawing.Point, System.Drawing">
    <value>383, 234</value>
  </data>
  <data name="cmbFltrTyp_Store.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="cmbFltrTyp_Store.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="cmbFltrTyp_Store.Properties.Items1" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbFltrTyp_Store.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_Store.Properties.Items3" xml:space="preserve">
    <value>Equal</value>
  </data>
  <data name="cmbFltrTyp_Store.Properties.Items4" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="cmbFltrTyp_Store.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_Store.Properties.Items6" xml:space="preserve">
    <value>Between</value>
  </data>
  <data name="cmbFltrTyp_Store.Properties.Items7" type="System.Byte, mscorlib">
    <value>2</value>
  </data>
  <data name="cmbFltrTyp_Store.Properties.Items8" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_Store.Size" type="System.Drawing.Size, System.Drawing">
    <value>134, 20</value>
  </data>
  <data name="cmbFltrTyp_Store.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Store.Name" xml:space="preserve">
    <value>cmbFltrTyp_Store</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Store.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Store.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Store.ZOrder" xml:space="preserve">
    <value>41</value>
  </data>
  <data name="dtExpDate.EditValue" type="System.DateTime, mscorlib">
    <value />
  </data>
  <data name="dtExpDate.Location" type="System.Drawing.Point, System.Drawing">
    <value>382, 211</value>
  </data>
  <data name="dtExpDate.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="dtExpDate.Size" type="System.Drawing.Size, System.Drawing">
    <value>134, 20</value>
  </data>
  <data name="dtExpDate.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="&gt;&gt;dtExpDate.Name" xml:space="preserve">
    <value>dtExpDate</value>
  </data>
  <data name="&gt;&gt;dtExpDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.DateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;dtExpDate.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;dtExpDate.ZOrder" xml:space="preserve">
    <value>42</value>
  </data>
  <data name="lkpVendor2.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="lkpVendor2.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 327</value>
  </data>
  <data name="lkpVendor2.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpVendor2.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn10.Caption" xml:space="preserve">
    <value>Vendor Code</value>
  </data>
  <data name="gridColumn10.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn10.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn9.Caption" xml:space="preserve">
    <value>Vendor Name</value>
  </data>
  <data name="gridColumn9.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn9.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn8.Caption" xml:space="preserve">
    <value>Vendor F Name</value>
  </data>
  <data name="gridColumn8.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn8.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpVendor2.Size" type="System.Drawing.Size, System.Drawing">
    <value>188, 20</value>
  </data>
  <data name="lkpVendor2.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="&gt;&gt;lkpVendor2.Name" xml:space="preserve">
    <value>lkpVendor2</value>
  </data>
  <data name="&gt;&gt;lkpVendor2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpVendor2.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;lkpVendor2.ZOrder" xml:space="preserve">
    <value>43</value>
  </data>
  <data name="lkpStore2.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore2.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 234</value>
  </data>
  <data name="lkpStore2.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpStore2.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn3.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="gridColumn3.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn3.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn4.Caption" xml:space="preserve">
    <value>Branch/ Store Name</value>
  </data>
  <data name="gridColumn4.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn4.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpStore2.Size" type="System.Drawing.Size, System.Drawing">
    <value>188, 20</value>
  </data>
  <data name="lkpStore2.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;lkpStore2.Name" xml:space="preserve">
    <value>lkpStore2</value>
  </data>
  <data name="&gt;&gt;lkpStore2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpStore2.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;lkpStore2.ZOrder" xml:space="preserve">
    <value>44</value>
  </data>
  <data name="cmbFltrTyp_Date.EditValue" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbFltrTyp_Date.Location" type="System.Drawing.Point, System.Drawing">
    <value>382, 188</value>
  </data>
  <data name="cmbFltrTyp_Date.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="cmbFltrTyp_Date.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="cmbFltrTyp_Date.Properties.Items1" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbFltrTyp_Date.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_Date.Properties.Items3" xml:space="preserve">
    <value>Equal</value>
  </data>
  <data name="cmbFltrTyp_Date.Properties.Items4" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="cmbFltrTyp_Date.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_Date.Properties.Items6" xml:space="preserve">
    <value>Between</value>
  </data>
  <data name="cmbFltrTyp_Date.Properties.Items7" type="System.Byte, mscorlib">
    <value>2</value>
  </data>
  <data name="cmbFltrTyp_Date.Properties.Items8" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_Date.Size" type="System.Drawing.Size, System.Drawing">
    <value>134, 20</value>
  </data>
  <data name="cmbFltrTyp_Date.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Date.Name" xml:space="preserve">
    <value>cmbFltrTyp_Date</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Date.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Date.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Date.ZOrder" xml:space="preserve">
    <value>45</value>
  </data>
  <data name="lkpItem2.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="lkpItem2.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 42</value>
  </data>
  <data name="lkpItem2.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpItem2.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="colItemCode11.Caption" xml:space="preserve">
    <value>Code1</value>
  </data>
  <data name="colItemCode11.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colItemCode11.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="gridColumn36.Caption" xml:space="preserve">
    <value>Code2</value>
  </data>
  <data name="gridColumn36.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn36.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="colItemNameAr1.Caption" xml:space="preserve">
    <value>Item Name</value>
  </data>
  <data name="colItemNameAr1.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colItemNameAr1.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="colItemNameEn1.Caption" xml:space="preserve">
    <value>Item F Name</value>
  </data>
  <data name="colItemNameEn1.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colItemNameEn1.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpItem2.Size" type="System.Drawing.Size, System.Drawing">
    <value>187, 20</value>
  </data>
  <data name="lkpItem2.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;lkpItem2.Name" xml:space="preserve">
    <value>lkpItem2</value>
  </data>
  <data name="&gt;&gt;lkpItem2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpItem2.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;lkpItem2.ZOrder" xml:space="preserve">
    <value>46</value>
  </data>
  <data name="cmbFltrTyp_Item.EditValue" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbFltrTyp_Item.Location" type="System.Drawing.Point, System.Drawing">
    <value>383, 42</value>
  </data>
  <data name="cmbFltrTyp_Item.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="cmbFltrTyp_Item.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="cmbFltrTyp_Item.Properties.Items1" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbFltrTyp_Item.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_Item.Properties.Items3" xml:space="preserve">
    <value>Equal</value>
  </data>
  <data name="cmbFltrTyp_Item.Properties.Items4" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="cmbFltrTyp_Item.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_Item.Properties.Items6" xml:space="preserve">
    <value>Between</value>
  </data>
  <data name="cmbFltrTyp_Item.Properties.Items7" type="System.Byte, mscorlib">
    <value>2</value>
  </data>
  <data name="cmbFltrTyp_Item.Properties.Items8" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_Item.Size" type="System.Drawing.Size, System.Drawing">
    <value>134, 20</value>
  </data>
  <data name="cmbFltrTyp_Item.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Item.Name" xml:space="preserve">
    <value>cmbFltrTyp_Item</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Item.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Item.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Item.ZOrder" xml:space="preserve">
    <value>47</value>
  </data>
  <data name="lkpItem1.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="lkpItem1.Location" type="System.Drawing.Point, System.Drawing">
    <value>210, 42</value>
  </data>
  <data name="lkpItem1.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpItem1.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="colItemCode1.Caption" xml:space="preserve">
    <value>Code1</value>
  </data>
  <data name="colItemCode1.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colItemCode1.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="colItemCode2.Caption" xml:space="preserve">
    <value>Code2</value>
  </data>
  <data name="colItemCode2.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colItemCode2.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="colItemNameAr.Caption" xml:space="preserve">
    <value>Item Name</value>
  </data>
  <data name="colItemNameAr.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colItemNameAr.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="colItemNameEn.Caption" xml:space="preserve">
    <value>Item F Name</value>
  </data>
  <data name="colItemNameEn.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colItemNameEn.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpItem1.Size" type="System.Drawing.Size, System.Drawing">
    <value>163, 20</value>
  </data>
  <data name="lkpItem1.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;lkpItem1.Name" xml:space="preserve">
    <value>lkpItem1</value>
  </data>
  <data name="&gt;&gt;lkpItem1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpItem1.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;lkpItem1.ZOrder" xml:space="preserve">
    <value>48</value>
  </data>
  <data name="lkpVendor1.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="lkpVendor1.Location" type="System.Drawing.Point, System.Drawing">
    <value>211, 327</value>
  </data>
  <data name="lkpVendor1.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpVendor1.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn7.Caption" xml:space="preserve">
    <value>Vendor Code</value>
  </data>
  <data name="gridColumn7.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn7.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn6.Caption" xml:space="preserve">
    <value>Vendor Name</value>
  </data>
  <data name="gridColumn6.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn6.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn5.Caption" xml:space="preserve">
    <value>Vendor F Name</value>
  </data>
  <data name="gridColumn5.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn5.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpVendor1.Size" type="System.Drawing.Size, System.Drawing">
    <value>163, 20</value>
  </data>
  <data name="lkpVendor1.TabIndex" type="System.Int32, mscorlib">
    <value>23</value>
  </data>
  <data name="&gt;&gt;lkpVendor1.Name" xml:space="preserve">
    <value>lkpVendor1</value>
  </data>
  <data name="&gt;&gt;lkpVendor1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpVendor1.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;lkpVendor1.ZOrder" xml:space="preserve">
    <value>49</value>
  </data>
  <data name="dt1.EditValue" type="System.DateTime, mscorlib">
    <value />
  </data>
  <data name="dt1.Location" type="System.Drawing.Point, System.Drawing">
    <value>212, 188</value>
  </data>
  <data name="dt1.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="dt1.Size" type="System.Drawing.Size, System.Drawing">
    <value>160, 20</value>
  </data>
  <data name="dt1.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="&gt;&gt;dt1.Name" xml:space="preserve">
    <value>dt1</value>
  </data>
  <data name="&gt;&gt;dt1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.DateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;dt1.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;dt1.ZOrder" xml:space="preserve">
    <value>50</value>
  </data>
  <data name="lkpStore1.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore1.Location" type="System.Drawing.Point, System.Drawing">
    <value>211, 234</value>
  </data>
  <data name="lkpStore1.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpStore1.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn2.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="gridColumn2.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn2.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn1.Caption" xml:space="preserve">
    <value>Branch/ Store Name</value>
  </data>
  <data name="gridColumn1.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn1.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpStore1.Size" type="System.Drawing.Size, System.Drawing">
    <value>162, 20</value>
  </data>
  <data name="lkpStore1.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="&gt;&gt;lkpStore1.Name" xml:space="preserve">
    <value>lkpStore1</value>
  </data>
  <data name="&gt;&gt;lkpStore1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpStore1.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;lkpStore1.ZOrder" xml:space="preserve">
    <value>51</value>
  </data>
  <data name="dt2.EditValue" type="System.DateTime, mscorlib">
    <value />
  </data>
  <data name="dt2.Location" type="System.Drawing.Point, System.Drawing">
    <value>13, 188</value>
  </data>
  <data name="dt2.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="dt2.Size" type="System.Drawing.Size, System.Drawing">
    <value>189, 20</value>
  </data>
  <data name="dt2.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="&gt;&gt;dt2.Name" xml:space="preserve">
    <value>dt2</value>
  </data>
  <data name="&gt;&gt;dt2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.DateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;dt2.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;dt2.ZOrder" xml:space="preserve">
    <value>52</value>
  </data>
  <data name="lkpCompany.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkpCompany.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpCompany.Location" type="System.Drawing.Point, System.Drawing">
    <value>211, 373</value>
  </data>
  <data name="lkpCompany.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpCompany.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkpCompany.Size" type="System.Drawing.Size, System.Drawing">
    <value>163, 20</value>
  </data>
  <data name="lkpCompany.TabIndex" type="System.Int32, mscorlib">
    <value>29</value>
  </data>
  <data name="&gt;&gt;lkpCompany.Name" xml:space="preserve">
    <value>lkpCompany</value>
  </data>
  <data name="&gt;&gt;lkpCompany.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpCompany.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;lkpCompany.ZOrder" xml:space="preserve">
    <value>53</value>
  </data>
  <data name="cmbFltrTyp_Car.EditValue" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbFltrTyp_Car.Location" type="System.Drawing.Point, System.Drawing">
    <value>383, 649</value>
  </data>
  <data name="cmbFltrTyp_Car.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="cmbFltrTyp_Car.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="cmbFltrTyp_Car.Properties.Items1" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="cmbFltrTyp_Car.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_Car.Properties.Items3" xml:space="preserve">
    <value>Equal</value>
  </data>
  <data name="cmbFltrTyp_Car.Properties.Items4" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="cmbFltrTyp_Car.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="cmbFltrTyp_Car.Size" type="System.Drawing.Size, System.Drawing">
    <value>134, 20</value>
  </data>
  <data name="cmbFltrTyp_Car.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Car.Name" xml:space="preserve">
    <value>cmbFltrTyp_Car</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Car.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Car.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;cmbFltrTyp_Car.ZOrder" xml:space="preserve">
    <value>54</value>
  </data>
  <data name="lkpCars.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkpCars.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpCars.Location" type="System.Drawing.Point, System.Drawing">
    <value>216, 649</value>
  </data>
  <metadata name="barManager2.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>332, 17</value>
  </metadata>
  <metadata name="barAndDockingController2.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>457, 17</value>
  </metadata>
  <data name="barDockControl1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="barDockControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="barDockControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>1036, 0</value>
  </data>
  <data name="&gt;&gt;barDockControl1.Name" xml:space="preserve">
    <value>barDockControl1</value>
  </data>
  <data name="&gt;&gt;barDockControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControl1.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="barDockControl2.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="barDockControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 665</value>
  </data>
  <data name="barDockControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>1036, 0</value>
  </data>
  <data name="&gt;&gt;barDockControl2.Name" xml:space="preserve">
    <value>barDockControl2</value>
  </data>
  <data name="&gt;&gt;barDockControl2.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControl2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControl2.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="barDockControl3.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="barDockControl3.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="barDockControl3.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 665</value>
  </data>
  <data name="&gt;&gt;barDockControl3.Name" xml:space="preserve">
    <value>barDockControl3</value>
  </data>
  <data name="&gt;&gt;barDockControl3.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControl3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControl3.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="barDockControl4.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Right</value>
  </data>
  <data name="barDockControl4.Location" type="System.Drawing.Point, System.Drawing">
    <value>1036, 0</value>
  </data>
  <data name="barDockControl4.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 665</value>
  </data>
  <data name="&gt;&gt;barDockControl4.Name" xml:space="preserve">
    <value>barDockControl4</value>
  </data>
  <data name="&gt;&gt;barDockControl4.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControl4.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControl4.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="barButtonItem2.Caption" xml:space="preserve">
    <value>Open</value>
  </data>
  <data name="barButtonItem2.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="barButtonItem2.ItemAppearance.Normal.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 10pt</value>
  </data>
  <data name="barButtonItem1.Caption" xml:space="preserve">
    <value>Help</value>
  </data>
  <data name="barButtonItem1.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="barButtonItem3.Caption" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="barButtonItem3.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="barButtonItem3.ItemAppearance.Normal.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 10pt</value>
  </data>
  <data name="repositoryItemTextEdit2.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpCars.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkpCars.Properties.Columns" xml:space="preserve">
    <value>CarId</value>
  </data>
  <data name="lkpCars.Properties.Columns1" xml:space="preserve">
    <value>Name58</value>
  </data>
  <data name="lkpCars.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkpCars.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkpCars.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkpCars.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpCars.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpCars.Properties.Columns7" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="lkpCars.Properties.Columns8" xml:space="preserve">
    <value>Car</value>
  </data>
  <data name="lkpCars.Properties.Columns9" xml:space="preserve">
    <value>PlateNo</value>
  </data>
  <data name="lkpCars.Properties.Columns10" xml:space="preserve">
    <value>Plate No.</value>
  </data>
  <data name="lkpCars.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkpCars.Size" type="System.Drawing.Size, System.Drawing">
    <value>157, 20</value>
  </data>
  <data name="lkpCars.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;lkpCars.Name" xml:space="preserve">
    <value>lkpCars</value>
  </data>
  <data name="&gt;&gt;lkpCars.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpCars.Parent" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;lkpCars.ZOrder" xml:space="preserve">
    <value>55</value>
  </data>
  <data name="layoutControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 3</value>
  </data>
  <data name="layoutControl1.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>0, 0, 0, 0</value>
  </data>
  <data name="layoutControlGroup1.CustomizationFormText" xml:space="preserve">
    <value>Root</value>
  </data>
  <data name="comp1.CustomizationFormText" xml:space="preserve">
    <value>Company</value>
  </data>
  <data name="comp1.Text" xml:space="preserve">
    <value>Item Group</value>
  </data>
  <data name="item1.CustomizationFormText" xml:space="preserve">
    <value>LabelsimpleLabelItem1</value>
  </data>
  <data name="item1.Text" xml:space="preserve">
    <value>Item</value>
  </data>
  <data name="item2.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem2</value>
  </data>
  <data name="item3.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem1</value>
  </data>
  <data name="item4.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem3</value>
  </data>
  <data name="date2.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem5</value>
  </data>
  <data name="date3.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem6</value>
  </data>
  <data name="date4.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem7</value>
  </data>
  <data name="date1.CustomizationFormText" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="date1.Text" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="store3.CustomizationFormText" xml:space="preserve">
    <value>store3</value>
  </data>
  <data name="store4.CustomizationFormText" xml:space="preserve">
    <value>store4</value>
  </data>
  <data name="store2.CustomizationFormText" xml:space="preserve">
    <value>store2</value>
  </data>
  <data name="store1.CustomizationFormText" xml:space="preserve">
    <value>Store</value>
  </data>
  <data name="store1.Text" xml:space="preserve">
    <value>Branch/Store</value>
  </data>
  <data name="vendor4.CustomizationFormText" xml:space="preserve">
    <value>vendor4</value>
  </data>
  <data name="vendor3.CustomizationFormText" xml:space="preserve">
    <value>vendor3</value>
  </data>
  <data name="vendor2.CustomizationFormText" xml:space="preserve">
    <value>vendor2</value>
  </data>
  <data name="vendor1.CustomizationFormText" xml:space="preserve">
    <value>Vendor</value>
  </data>
  <data name="vendor1.Text" xml:space="preserve">
    <value>Vendor</value>
  </data>
  <data name="comp4.CustomizationFormText" xml:space="preserve">
    <value>comp4</value>
  </data>
  <data name="comp2.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem1</value>
  </data>
  <data name="comp3.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem2</value>
  </data>
  <data name="expDate3.CustomizationFormText" xml:space="preserve">
    <value>emptySpaceItem2</value>
  </data>
  <data name="expDate1.CustomizationFormText" xml:space="preserve">
    <value>Date Less Than</value>
  </data>
  <data name="expDate1.Text" xml:space="preserve">
    <value>Date Less Than</value>
  </data>
  <data name="expDate2.CustomizationFormText" xml:space="preserve">
    <value>expDate2</value>
  </data>
  <data name="lblFltrName.CustomizationFormText" xml:space="preserve">
    <value>lblFltrName</value>
  </data>
  <data name="lblTo.CustomizationFormText" xml:space="preserve">
    <value>To</value>
  </data>
  <data name="lblTo.Text" xml:space="preserve">
    <value>To</value>
  </data>
  <data name="lblFrom.CustomizationFormText" xml:space="preserve">
    <value>From</value>
  </data>
  <data name="lblFrom.Text" xml:space="preserve">
    <value>From</value>
  </data>
  <data name="lblFltrType.CustomizationFormText" xml:space="preserve">
    <value>Filter Type</value>
  </data>
  <data name="lblFltrType.Text" xml:space="preserve">
    <value>Filter Type</value>
  </data>
  <data name="Customer1.CustomizationFormText" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="Customer1.Text" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="Customer2.CustomizationFormText" xml:space="preserve">
    <value>Customer2</value>
  </data>
  <data name="Customer3.CustomizationFormText" xml:space="preserve">
    <value>Customer3</value>
  </data>
  <data name="Customer4.CustomizationFormText" xml:space="preserve">
    <value>Customer4</value>
  </data>
  <data name="process3.CustomizationFormText" xml:space="preserve">
    <value>process3</value>
  </data>
  <data name="process1.CustomizationFormText" xml:space="preserve">
    <value>Process Type</value>
  </data>
  <data name="process1.Text" xml:space="preserve">
    <value>Process Type</value>
  </data>
  <data name="process2.CustomizationFormText" xml:space="preserve">
    <value>process2</value>
  </data>
  <data name="cost3.CustomizationFormText" xml:space="preserve">
    <value>cost3</value>
  </data>
  <data name="cost2.CustomizationFormText" xml:space="preserve">
    <value>cost2</value>
  </data>
  <data name="acc3.CustomizationFormText" xml:space="preserve">
    <value>acc3</value>
  </data>
  <data name="cost1.CustomizationFormText" xml:space="preserve">
    <value>Cost Center</value>
  </data>
  <data name="cost1.Text" xml:space="preserve">
    <value>Cost Center</value>
  </data>
  <data name="acc1.CustomizationFormText" xml:space="preserve">
    <value>Account</value>
  </data>
  <data name="acc1.Text" xml:space="preserve">
    <value>Account</value>
  </data>
  <data name="cstmLst3.CustomizationFormText" xml:space="preserve">
    <value>cstmLst3</value>
  </data>
  <data name="cstmLst2.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem1</value>
  </data>
  <data name="cstmLst1.CustomizationFormText" xml:space="preserve">
    <value>Custom Accounts List</value>
  </data>
  <data name="cstmLst1.Text" xml:space="preserve">
    <value>Custom Accounts List</value>
  </data>
  <data name="user4.CustomizationFormText" xml:space="preserve">
    <value>user4</value>
  </data>
  <data name="user2.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem1</value>
  </data>
  <data name="user1.CustomizationFormText" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="user1.Text" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="user3.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem2</value>
  </data>
  <data name="Batch3.CustomizationFormText" xml:space="preserve">
    <value>emptySpaceItem5</value>
  </data>
  <data name="Batch4.CustomizationFormText" xml:space="preserve">
    <value>Batch4</value>
  </data>
  <data name="salesEmp3.CustomizationFormText" xml:space="preserve">
    <value>salesEmp3</value>
  </data>
  <data name="salesEmp4.CustomizationFormText" xml:space="preserve">
    <value>salesEmp4</value>
  </data>
  <data name="salesEmp1.CustomizationFormText" xml:space="preserve">
    <value>Sales Employee</value>
  </data>
  <data name="salesEmp1.Text" xml:space="preserve">
    <value>Sales Employee</value>
  </data>
  <data name="salesEmp2.CustomizationFormText" xml:space="preserve">
    <value>salesEmp2</value>
  </data>
  <data name="Batch1.CustomizationFormText" xml:space="preserve">
    <value>Batch</value>
  </data>
  <data name="Batch1.Text" xml:space="preserve">
    <value>Batch</value>
  </data>
  <data name="Batch2.CustomizationFormText" xml:space="preserve">
    <value>Batch2</value>
  </data>
  <data name="custGroup1.CustomizationFormText" xml:space="preserve">
    <value>Customer Group</value>
  </data>
  <data name="custGroup1.Text" xml:space="preserve">
    <value>Customer Group</value>
  </data>
  <data name="custGroup2.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem1</value>
  </data>
  <data name="custGroup3.CustomizationFormText" xml:space="preserve">
    <value>custGroup3</value>
  </data>
  <data name="acc2.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem1</value>
  </data>
  <data name="ParentItem2.CustomizationFormText" xml:space="preserve">
    <value>ParentItem2</value>
  </data>
  <data name="ParentItem3.CustomizationFormText" xml:space="preserve">
    <value>ParentItem3</value>
  </data>
  <data name="dim4.AppearanceItemCaption.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="dim4.CustomizationFormText" xml:space="preserve">
    <value>Length</value>
  </data>
  <data name="dim4.Text" xml:space="preserve">
    <value>Length</value>
  </data>
  <data name="dim2.AppearanceItemCaption.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="dim2.CustomizationFormText" xml:space="preserve">
    <value>Height</value>
  </data>
  <data name="dim2.Text" xml:space="preserve">
    <value>Height</value>
  </data>
  <data name="dim3.AppearanceItemCaption.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="dim3.CustomizationFormText" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="dim3.Text" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="ParentItem1.CustomizationFormText" xml:space="preserve">
    <value>Parent Item</value>
  </data>
  <data name="ParentItem1.Text" xml:space="preserve">
    <value>Parent Item</value>
  </data>
  <data name="Mtrx0.CustomizationFormText" xml:space="preserve">
    <value>Matrix</value>
  </data>
  <data name="Mtrx0.Text" xml:space="preserve">
    <value>Matrix</value>
  </data>
  <data name="dim1.CustomizationFormText" xml:space="preserve">
    <value>Dimensions</value>
  </data>
  <data name="dim1.Text" xml:space="preserve">
    <value>Dimensions</value>
  </data>
  <data name="jo1.CustomizationFormText" xml:space="preserve">
    <value>Job Order</value>
  </data>
  <data name="jo1.Text" xml:space="preserve">
    <value>Job Order</value>
  </data>
  <data name="jo2.CustomizationFormText" xml:space="preserve">
    <value>Priority</value>
  </data>
  <data name="jo2.Text" xml:space="preserve">
    <value>Priority</value>
  </data>
  <data name="jo3.CustomizationFormText" xml:space="preserve">
    <value>State</value>
  </data>
  <data name="jo3.Text" xml:space="preserve">
    <value>State</value>
  </data>
  <data name="jo4.CustomizationFormText" xml:space="preserve">
    <value>Dept</value>
  </data>
  <data name="jo4.Text" xml:space="preserve">
    <value>Dept</value>
  </data>
  <data name="itmTyp3.CustomizationFormText" xml:space="preserve">
    <value>itmTyp3</value>
  </data>
  <data name="itmTyp1.CustomizationFormText" xml:space="preserve">
    <value>Item Type</value>
  </data>
  <data name="itmTyp1.Text" xml:space="preserve">
    <value>Item Type</value>
  </data>
  <data name="itmTyp2.CustomizationFormText" xml:space="preserve">
    <value>itmTyp2</value>
  </data>
  <data name="sell1.CustomizationFormText" xml:space="preserve">
    <value>sell</value>
  </data>
  <data name="sell1.Text" xml:space="preserve">
    <value>sell Price</value>
  </data>
  <data name="sell2.CustomizationFormText" xml:space="preserve">
    <value>sell2</value>
  </data>
  <data name="sell3.CustomizationFormText" xml:space="preserve">
    <value>sell3</value>
  </data>
  <data name="sell4.CustomizationFormText" xml:space="preserve">
    <value>sell4</value>
  </data>
  <data name="cat1.CustomizationFormText" xml:space="preserve">
    <value>Item Category</value>
  </data>
  <data name="cat1.Text" xml:space="preserve">
    <value>Item Category</value>
  </data>
  <data name="cat2.CustomizationFormText" xml:space="preserve">
    <value>cat2</value>
  </data>
  <data name="cat3.CustomizationFormText" xml:space="preserve">
    <value>cat3</value>
  </data>
  <data name="cat4.CustomizationFormText" xml:space="preserve">
    <value>cat4</value>
  </data>
  <data name="InvoiceBook4.CustomizationFormText" xml:space="preserve">
    <value>InvoiceBook4</value>
  </data>
  <data name="InvoiceBook1.CustomizationFormText" xml:space="preserve">
    <value>Invoice Book</value>
  </data>
  <data name="InvoiceBook1.Text" xml:space="preserve">
    <value>Invoice Book</value>
  </data>
  <data name="InvoiceBook2.CustomizationFormText" xml:space="preserve">
    <value>InvoiceBook2</value>
  </data>
  <data name="InvoiceBook3.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem1</value>
  </data>
  <data name="EmpGroup4.CustomizationFormText" xml:space="preserve">
    <value>EmpGroup4</value>
  </data>
  <data name="EmpGroup1.CustomizationFormText" xml:space="preserve">
    <value>Employee Group</value>
  </data>
  <data name="EmpGroup1.Text" xml:space="preserve">
    <value>Employee Group</value>
  </data>
  <data name="EmpGroup2.CustomizationFormText" xml:space="preserve">
    <value>EmpGroup2</value>
  </data>
  <data name="EmpGroup3.CustomizationFormText" xml:space="preserve">
    <value>EmpGroup3</value>
  </data>
  <data name="QC1.CustomizationFormText" xml:space="preserve">
    <value>QC</value>
  </data>
  <data name="QC1.Text" xml:space="preserve">
    <value>QC</value>
  </data>
  <data name="QC2.CustomizationFormText" xml:space="preserve">
    <value>QC2</value>
  </data>
  <data name="QC3.CustomizationFormText" xml:space="preserve">
    <value>QC3</value>
  </data>
  <data name="QC4.CustomizationFormText" xml:space="preserve">
    <value>emptySpaceItem8</value>
  </data>
  <data name="VenGroup2.CustomizationFormText" xml:space="preserve">
    <value>VenGroup2</value>
  </data>
  <data name="VenGroup1.CustomizationFormText" xml:space="preserve">
    <value>Vendor Group</value>
  </data>
  <data name="VenGroup1.Text" xml:space="preserve">
    <value>Vendor Group</value>
  </data>
  <data name="VenGroup3.CustomizationFormText" xml:space="preserve">
    <value>VenGroup3</value>
  </data>
  <data name="Mtrx1.CustomizationFormText" xml:space="preserve">
    <value>Mtrx1</value>
  </data>
  <data name="Mtrx2.CustomizationFormText" xml:space="preserve">
    <value>Mtrx2</value>
  </data>
  <data name="Mtrx3.CustomizationFormText" xml:space="preserve">
    <value>Mtrx3</value>
  </data>
  <data name="cat5.CustomizationFormText" xml:space="preserve">
    <value>Item Category</value>
  </data>
  <data name="cat5.Text" xml:space="preserve">
    <value>Cars</value>
  </data>
  <data name="cat6.CustomizationFormText" xml:space="preserve">
    <value>cat2</value>
  </data>
  <data name="cat6.Text" xml:space="preserve">
    <value>cat2</value>
  </data>
  <data name="cat7.CustomizationFormText" xml:space="preserve">
    <value>cat3</value>
  </data>
  <data name="cat7.Text" xml:space="preserve">
    <value>cat3</value>
  </data>
  <data name="cat8.CustomizationFormText" xml:space="preserve">
    <value>cat4</value>
  </data>
  <data name="cat8.Text" xml:space="preserve">
    <value>cat4</value>
  </data>
  <data name="emptySpaceItem2.CustomizationFormText" xml:space="preserve">
    <value>emptySpaceItem2</value>
  </data>
  <data name="layoutControlGroup1.Text" xml:space="preserve">
    <value>Root</value>
  </data>
  <data name="layoutControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>686, 598</value>
  </data>
  <data name="layoutControl1.TabIndex" type="System.Int32, mscorlib">
    <value>107</value>
  </data>
  <data name="layoutControl1.Text" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;layoutControl1.Name" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;layoutControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControl, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControl1.Parent" xml:space="preserve">
    <value>xtraScrollableControl1</value>
  </data>
  <data name="&gt;&gt;layoutControl1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="xtraScrollableControl1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="xtraScrollableControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="xtraScrollableControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>702, 601</value>
  </data>
  <data name="xtraScrollableControl1.TabIndex" type="System.Int32, mscorlib">
    <value>108</value>
  </data>
  <data name="&gt;&gt;xtraScrollableControl1.Name" xml:space="preserve">
    <value>xtraScrollableControl1</value>
  </data>
  <data name="&gt;&gt;xtraScrollableControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.XtraScrollableControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;xtraScrollableControl1.Parent" xml:space="preserve">
    <value>groupControl2</value>
  </data>
  <data name="&gt;&gt;xtraScrollableControl1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="groupControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 30</value>
  </data>
  <data name="groupControl2.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="groupControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>706, 626</value>
  </data>
  <data name="groupControl2.TabIndex" type="System.Int32, mscorlib">
    <value>81</value>
  </data>
  <data name="groupControl2.Text" xml:space="preserve">
    <value>Filters</value>
  </data>
  <data name="&gt;&gt;groupControl2.Name" xml:space="preserve">
    <value>groupControl2</value>
  </data>
  <data name="&gt;&gt;groupControl2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GroupControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;groupControl2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupControl2.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="groupControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Right</value>
  </data>
  <data name="groupControl1.AppearanceCaption.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 10pt, style=Bold</value>
  </data>
  <data name="lstBxReports.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="lstBxReports.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="lstBxReports.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="lstBxReports.Size" type="System.Drawing.Size, System.Drawing">
    <value>196, 603</value>
  </data>
  <data name="lstBxReports.TabIndex" type="System.Int32, mscorlib">
    <value>79</value>
  </data>
  <data name="&gt;&gt;lstBxReports.Name" xml:space="preserve">
    <value>lstBxReports</value>
  </data>
  <data name="&gt;&gt;lstBxReports.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageListBoxControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lstBxReports.Parent" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;lstBxReports.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="groupControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>713, 30</value>
  </data>
  <data name="groupControl1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="groupControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>200, 628</value>
  </data>
  <data name="groupControl1.TabIndex" type="System.Int32, mscorlib">
    <value>80</value>
  </data>
  <data name="groupControl1.Text" xml:space="preserve">
    <value>Reports</value>
  </data>
  <data name="&gt;&gt;groupControl1.Name" xml:space="preserve">
    <value>groupControl1</value>
  </data>
  <data name="&gt;&gt;groupControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GroupControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;groupControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupControl1.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="NBG_Dept.Caption" xml:space="preserve">
    <value>Departments</value>
  </data>
  <data name="NBI_SL.Caption" xml:space="preserve">
    <value>Sales</value>
  </data>
  <data name="navBarControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Right</value>
  </data>
  <data name="navBarControl1.Appearance.ItemActive.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="navBarControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>919, 30</value>
  </data>
  <data name="resource.ExpandedWidth" type="System.Int32, mscorlib">
    <value>113</value>
  </data>
  <data name="navBarControl1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="navBarControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>113, 626</value>
  </data>
  <data name="navBarControl1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="navBarControl1.Text" xml:space="preserve">
    <value>navBarControl1</value>
  </data>
  <data name="&gt;&gt;navBarControl1.Name" xml:space="preserve">
    <value>navBarControl1</value>
  </data>
  <data name="&gt;&gt;navBarControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraNavBar.NavBarControl, DevExpress.XtraNavBar.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;navBarControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;navBarControl1.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAA
        AAAfbf8AH2z/AB9r/x8fa/+rH2v/9h9r/9AfbP9HH2//AB9u/wAfcP8AAAAAAB9p/wAfZv8AH2D/Ah9j
        /2cfY//iH2P/7R9j/4YfY/8KH2P/AB9j/wAAAAAADw4QAA4NDwAODQ4ADQwORwwMDdAMCw32CwsMqwsL
        DB8MCw0ADAsNAB9u/wAfbf8WH23/qx9t//8fbv//H27//x9u/9gfb/89H3D/AB9y/wAfcf8AH23/AB9n
        /wAfZ/9cH2X/6x9j//8fY///H2P/9x9j/38fY/8GH2P/AB5e8QAPDhAADw4QAA4NDz0NDQ/YDQ0O/w0M
        Dv8MDA3/DAsNqwwLDRYMCw0AH2//FR9v/6Ifb///H3D//x9x//8fcf//H3H//x9x/9Mfcv82IHD/AiBw
        /wQfcP8DH2z/VB9q/+gfZ///H2X//x9j//8fY///H2P/9x9j/3cfY/8FH2P+ABAPEQAQDxE1Dw4Q0w8O
        EP8ODg//Dg0P/w0NDv8MDA3/DAsNogwLDRUfcP+MH3H//R9y//8ec///HnP//x50//8edP//HnP//x5z
        /9cec/+pH3L/qR9x/6wfbv/mH23//x9q//8faP//H2X//x9j//8fY///H2P/8h9j/04bTb8AERASERAQ
        ErsQEBL/EA8R/w8PEf8PDhD/Dg0P/w0ND/8MDA79DAwNjB5z/9QedP//HnX//x52//8edv//Hnb//x52
        //8edv//Hnb//x51//8edP//HnP//x9x//8fb///H23//x9r//8faP//H2X//x9j//8fY///H2P/gxg4
        hAASERM1ERET7hERE/8REBL/EBAS/xAPEf8PDhD/Dg4Q/w0ND/8NDA7THnX/wx52//8ed///Hnj//x54
        //8eef//Hnn//x54//8eeP/9Hnf/8h52//Iedf/zHnP//h9x//8fb///H23//x9q//8fZ///H2T//x9j
        //8fY/90GkGeABISFCYTEhThEhIU/xIRE/8RERP/EBAS/xAPEf8PDhD/Dg4Q/w4ND78ed/9UHnj/5x55
        //8eev//Hnr//x57//8ee///Hnr/+x56/5AeeP88Hnf/PB52/0Eedf+wHnP//x9x//8fb///H2z//x9p
        //8fZv//H2T/zB9j/yQdV9wAExIVAxMTFYMTEhX8ExIU/xISFP8RERP/ERAS/xAPEf8PDhDkDg4QTx50
        /wAeev9dHnv/7R58//8efP//Hn3//x59//0efP+UHnv/DB55/wAed/8AHnb/AB52/x8edf+5HnP//x9x
        //8fbv//H2v//x9o/9YfZf85H2f/AAAAAAAUExYAFBMWDRQTFpQUExX9ExIV/xISFP8SERP/ERAS6hAP
        EVYTDhQAHnr/AB58/wEefP9nHn3/7x5+//8efv/9Hn7/nR59/xAefP8AHnv/AB52/wAedf8AHnX/AB51
        /yUedP2+H3P//x9x//8fbPvbH2r/Px9o/wAfZv8AH2P/ABQTFgAUExYAFBQWEBQUFp0UExb9ExIV/xIS
        FOwRERNfIxkMABAQEQAee/8AHn3/AB5+/wcefv+xHn///x5//+oef/8tHn//AB59/wAefP8AAAAAAB52
        /wAeV/8AGkKGABk+eUYaQoPzG0KG/xo0ZZIbRZkAH2v/AB9o/wAAAAAAFBQWABQUFgAVFBcAFRQXLRUU
        F+oUExb/ExMVqxMTFAUSERQAEBASAAAAAAAegP8AHn//Ax6A/6kegP//HoD/5x6A/ycegP8AHoD/AAAA
        AAAAAAAAAAAAABpCgQAXFRcAFhMSOhcVFvAXFRb/GBUWjhgYHgAcO3gAAAAAAAAAAAAAAAAAFxYZABYV
        GAAWFRgnFRUX5xUUF/8UExajExIVAhQTFgAAAAAAHoD/AB6A/wAegP8IHoD/sx6B//8egf/rHoH/Lx6B
        /wAegv8AHoL/AAAAAAAXFhkAFBQXABcXGgAXFxpIGBca9BgYG/8ZGBuTGRkcABoaHQAbGx4AAAAAABkZ
        HAAYGBsAFxcZABcWGS8WFhjrFRUX/xUUF6wUFBYGFBMWABQTFQAef/8AHoD/Ah6A/20egf/xHoH//x6B
        //4egv+kHoL/Ex6C/wAegv8AFhYYABsZIAAXFxoAFxcaKRgXGsMYGBv/GRkc/xoZHN4bGh1FHBoeABwb
        HwAdHCAAGhkdABkZHAAYGBsTGBcapBcWGf4WFRj/FRQX7xQUFmYTEBMBExMVAB59/wIegP9kHoD/8B6B
        //8egv//HoL//x6C//8egv+cHoL/EB6B/wAaRX4AFxYZABcWGSQXFxq/GBgb/xkZHP8aGR3/Gxoe/xwb
        HtkcGx89HBwfABoYIQAbGh0AGxodEBkZHJwYGBv/Fxca/xYWGf8VFRf/FBQW7RQTFVwBABAAHn//Vx5/
        /+kegP//HoH//x6C//8egv//HoL//x6C//4egf+JHoD/BBgxUQAWFhgSFxYZsRgXGv8ZGBv/Ghkc/xsa
        Hf8cGx7/HBwf/x0cIM8eHSAmHRwgAB0cHwQbGh6JGhkc/hkYG/8YFxr/FxYZ/xYVGP8VFBf/ExMV5hMS
        FFIefv/EHn///x6A//8egf//HoL//x6C//8egv//HoL//x6B/+Megf8nGUB0ABYWGEwXFhn4GBca/xkY
        G/8aGh3/Gxse/xwcH/8dHCD/Hh0h/x4dIXUdHCAAHBsfJxwbHuMaGh3/GRkc/xgXGv8XFhn/FhUY/xUU
        F/8UExX/ExIUwR5+/9Uef///HoD//x6B//8egf//HoL//x6C//8egf//HoH/7h6B/zQaRX4AFhYZWhcW
        GfwYGBv/GRkc/xsaHf8cGx7/HRwg/x4dIf8fHiL/Hx4igx4dIQAdHB80HBsf7hsaHf8aGRz/GBgb/xcX
        Gf8WFRj/FRQX/xQTFf8TEhXSHn7/jR5///0egP//HoD//x6B//8egf//HoH//x6B//8egf+4HoD/EBk3
        YAAWFhkqFxYZ2xgYG/8ZGRz/Gxod/xwbHv8dHCD/Hh0h/x8eIu8gHyNKHx4iABwcHxAcGx64Gxod/xoZ
        HP8YGBv/Fxca/xYVGP8VFBf/FBMV/BMSFYgefv8VHn7/oh5///8egP//HoD//x6B//8egf//HoD/0B6A
        /zIegf8AFxIRABgaHQAXFxpQGBca5hkYHP8aGh3/Gxse/x0cH/8dHSD1Hh0hch8eIwQfHiIAGxseABwb
        HjIaGh3QGRkc/xgXG/8XFhn/FhUY/xUUF/8UExafExIVEx5+/wAefv8XHn7/qx5///8ef///Hn///x6A
        /9UegP85HoD/AB5//wAYIzUAFxcaABgYGwAYFxpXGRgb6hoZHP8bGh7/HBse+B0cH3weHSEFHh0hAB8e
        IgAbGh0AGxodABoZHDkZGBvVGBca/xcWGf8WFRj/FRQXqBQUFhUUExYAHn7/AB5+/wAefv8dHn7/yR5+
        //8efv/yHn//Sh5//wAef/8AHoD/AAAAAAAYFxoAGBcaABUXGgAZGBtUGRkcwxoaHc0bGh5yHBseCB0c
        HwAeHSEAAAAAABsaHQAaGRwAGRgbABgYG0oYFxryFxYZ/xYVGMUVFBcbFRQXABQUFgAefv8AHnz/AB56
        /wIefP+oHn3//x59/+ceff8lHn3/AB5//wAAAAAAAAAAAAAAAAAYGBoAGBgbABgXGgAaGRwNGhkdERkZ
        HAAbGh4AGxoeAAAAAAAAAAAAAAAAABkZHAAXFxoAFxcaJRcWGecWFRj/FRUXohMSFQEVFBcAFRQXAB50
        /wAed/8AHnn/CR56/7Qee///Hnv/6x57/y4eev8AHnj/AB53/wAAAAAAFRQXABYVGAAWFRgAFxYZJBgX
        GnEYFxp6GRgbNBkOEQAYGBsAGBcaAAAAAAAWFhgAFhYYABYWGQAXFhkuFhYY6xUVF/8VFBeuFBQWBxMT
        FQASERQAHnL/AB11/wMedv9zHnf/8x54//8eef/+Hnn/oR55/xEed/8AHnb/ABMSFgAbFx0AFRQXABUV
        Fy4WFhjJFxYZ/xgXGv8YFxreGBcaShcXGgAYFxoAFxYZABYVGAAWFRgAFhYYERYWGKEWFRj+FRQX/xQT
        FvETEhVuEhETAxIREwAgb/8CH3L/aR50//Iedf//Hnb//x53//8ed//+Hnf/lx52/w0edf8AGEOMABQT
        FgAUExYnFRQXxBUVF/8WFhj/FxYZ/xcXGv8XFxrdFxcaQRYWGQAWFRgAFhUYABYVGA0WFRiXFhUY/hUU
        F/8UExb/ExMV/xISFPARERNlEQ4RAh9t/1sfb//sH3H//x9y//8ec///HnT//x50//8edP/9HnT/hx9y
        /wUVKUwAEhIUFBMSFbYUExb/FBQW/xERE/8QEBL/FhUY/xYWGf8XFhnXFhYYYBYVGE4WFRhOFhUYmBUV
        F/wVFBf/FBMW/xMTFf8SEhT/ERET/xEQEuoQDxFXH2v/xh9s//8fbv//H2///x9w//8fcf//H3H//x9x
        //8fcf/kH3D/KBc3cQASERNOEhIU+BMSFf8SERT/DAwN/woJC/8QDxH/FRUX/xYVGP8WFRj7FRUX+BUU
        F/gVFBf/FBQW/xQTFv8TEhX/EhIU/xIRE/8REBL/EA8R/w8PEMMfZ//UH2n//x9r//8fbP//H23//x9u
        //8fbv//H27//x9u/+4fbv80Fzl6ABEQE1oRERP8EhIU/xERE/8ODg//DAwN/xAPEf8UExb/FBQW/xQU
        Fv8UFBb/FBMW/xQTFv8TExX/ExIU/xISFP8SERP/ERAS/xAQEv8PDxH/Dg4Q0R9l/4kfZf/8H2f//x9o
        //8faf//H2r//x9q//8fav//H2v/sh9r/w4VKlcAEBASKRAQEtoRERP/EhET/xEQEv8QEBL/EhIU/xMT
        Ff8TExXyExMVsxMTFagTEhWoExIU1BISFP8SERP/ERET/xEQEv8QDxH/Dw8R/w4OEPwODQ+EH2P/Ex9j
        /6AfZP//H2T//x9l//8fZv//H2b//x9n/8gfaP8qH2j/AA0CAAAREBIAEA8RThAQEuUREBL/ERET/xIR
        E/8SERP/EhEU9hISFHYTExUIEhEUAxMRFQISERMxERETzREQEv8QEBL/EA8R/w8OEP8ODhD/Dg0Pnw0N
        DxIfY/8AH2P/Fh9j/6kfY///H2P//x9j//8fY//MH2T/MB9m/wAfVP8AERgnABAPEQAQERMAEA8RVRAP
        EegQEBL/EBAS/xEQEvcRERN9EhETBhISFAATEhUAExIVABEREwAQEBI2EA8R0g8PEf8PDhD/Dg4P/w4N
        D6gNDQ8VDg0PAB9j/wAfY/8AH2P/HR9j/6QfY//nH2P/wh9j/zofZP8AH2P/AB9m/wAAAAAAEA8RAA8P
        EQAKDhIBDw8QYA8PEdcQDxHiEA8RgRAPEQkREBIAEhETAAAAAAARERMAEA8RABAPEQAPDhBADg4Qxg4N
        D+cODQ+iDQ0OHA0NDwAODQ8AACAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAGAIBw
        DgEAIAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAQAAHAOAAAgBAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgBAA=
</value>
  </data>
  <data name="$this.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Reports Center</value>
  </data>
  <data name="&gt;&gt;barManager1.Name" xml:space="preserve">
    <value>barManager1</value>
  </data>
  <data name="&gt;&gt;barManager1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarManager, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;bar1.Name" xml:space="preserve">
    <value>bar1</value>
  </data>
  <data name="&gt;&gt;bar1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Bar, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtn_Help.Name" xml:space="preserve">
    <value>barBtn_Help</value>
  </data>
  <data name="&gt;&gt;barBtn_Help.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtn_Open.Name" xml:space="preserve">
    <value>barBtn_Open</value>
  </data>
  <data name="&gt;&gt;barBtn_Open.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnClose.Name" xml:space="preserve">
    <value>barBtnClose</value>
  </data>
  <data name="&gt;&gt;barBtnClose.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barAndDockingController1.Name" xml:space="preserve">
    <value>barAndDockingController1</value>
  </data>
  <data name="&gt;&gt;barAndDockingController1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarAndDockingController, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repositoryItemTextEdit1.Name" xml:space="preserve">
    <value>repositoryItemTextEdit1</value>
  </data>
  <data name="&gt;&gt;repositoryItemTextEdit1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemTextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;bar2.Name" xml:space="preserve">
    <value>bar2</value>
  </data>
  <data name="&gt;&gt;bar2.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Bar, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;NBG_Dept.Name" xml:space="preserve">
    <value>NBG_Dept</value>
  </data>
  <data name="&gt;&gt;NBG_Dept.Type" xml:space="preserve">
    <value>DevExpress.XtraNavBar.NavBarGroup, DevExpress.XtraNavBar.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;NBI_SL.Name" xml:space="preserve">
    <value>NBI_SL</value>
  </data>
  <data name="&gt;&gt;NBI_SL.Type" xml:space="preserve">
    <value>DevExpress.XtraNavBar.NavBarItem, DevExpress.XtraNavBar.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView12.Name" xml:space="preserve">
    <value>gridView12</value>
  </data>
  <data name="&gt;&gt;gridView12.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn27.Name" xml:space="preserve">
    <value>gridColumn27</value>
  </data>
  <data name="&gt;&gt;gridColumn27.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn26.Name" xml:space="preserve">
    <value>gridColumn26</value>
  </data>
  <data name="&gt;&gt;gridColumn26.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn32.Name" xml:space="preserve">
    <value>gridColumn32</value>
  </data>
  <data name="&gt;&gt;gridColumn32.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView20.Name" xml:space="preserve">
    <value>gridView20</value>
  </data>
  <data name="&gt;&gt;gridView20.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn51.Name" xml:space="preserve">
    <value>gridColumn51</value>
  </data>
  <data name="&gt;&gt;gridColumn51.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn50.Name" xml:space="preserve">
    <value>gridColumn50</value>
  </data>
  <data name="&gt;&gt;gridColumn50.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn52.Name" xml:space="preserve">
    <value>gridColumn52</value>
  </data>
  <data name="&gt;&gt;gridColumn52.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView11.Name" xml:space="preserve">
    <value>gridView11</value>
  </data>
  <data name="&gt;&gt;gridView11.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn25.Name" xml:space="preserve">
    <value>gridColumn25</value>
  </data>
  <data name="&gt;&gt;gridColumn25.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn24.Name" xml:space="preserve">
    <value>gridColumn24</value>
  </data>
  <data name="&gt;&gt;gridColumn24.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn29.Name" xml:space="preserve">
    <value>gridColumn29</value>
  </data>
  <data name="&gt;&gt;gridColumn29.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView19.Name" xml:space="preserve">
    <value>gridView19</value>
  </data>
  <data name="&gt;&gt;gridView19.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn48.Name" xml:space="preserve">
    <value>gridColumn48</value>
  </data>
  <data name="&gt;&gt;gridColumn48.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn47.Name" xml:space="preserve">
    <value>gridColumn47</value>
  </data>
  <data name="&gt;&gt;gridColumn47.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn49.Name" xml:space="preserve">
    <value>gridColumn49</value>
  </data>
  <data name="&gt;&gt;gridColumn49.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView18.Name" xml:space="preserve">
    <value>gridView18</value>
  </data>
  <data name="&gt;&gt;gridView18.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn45.Name" xml:space="preserve">
    <value>gridColumn45</value>
  </data>
  <data name="&gt;&gt;gridColumn45.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn44.Name" xml:space="preserve">
    <value>gridColumn44</value>
  </data>
  <data name="&gt;&gt;gridColumn44.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn46.Name" xml:space="preserve">
    <value>gridColumn46</value>
  </data>
  <data name="&gt;&gt;gridColumn46.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView10.Name" xml:space="preserve">
    <value>gridView10</value>
  </data>
  <data name="&gt;&gt;gridView10.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn23.Name" xml:space="preserve">
    <value>gridColumn23</value>
  </data>
  <data name="&gt;&gt;gridColumn23.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn22.Name" xml:space="preserve">
    <value>gridColumn22</value>
  </data>
  <data name="&gt;&gt;gridColumn22.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn28.Name" xml:space="preserve">
    <value>gridColumn28</value>
  </data>
  <data name="&gt;&gt;gridColumn28.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView17.Name" xml:space="preserve">
    <value>gridView17</value>
  </data>
  <data name="&gt;&gt;gridView17.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn42.Name" xml:space="preserve">
    <value>gridColumn42</value>
  </data>
  <data name="&gt;&gt;gridColumn42.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn39.Name" xml:space="preserve">
    <value>gridColumn39</value>
  </data>
  <data name="&gt;&gt;gridColumn39.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn43.Name" xml:space="preserve">
    <value>gridColumn43</value>
  </data>
  <data name="&gt;&gt;gridColumn43.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView16.Name" xml:space="preserve">
    <value>gridView16</value>
  </data>
  <data name="&gt;&gt;gridView16.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn40.Name" xml:space="preserve">
    <value>gridColumn40</value>
  </data>
  <data name="&gt;&gt;gridColumn40.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn41.Name" xml:space="preserve">
    <value>gridColumn41</value>
  </data>
  <data name="&gt;&gt;gridColumn41.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView15.Name" xml:space="preserve">
    <value>gridView15</value>
  </data>
  <data name="&gt;&gt;gridView15.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn37.Name" xml:space="preserve">
    <value>gridColumn37</value>
  </data>
  <data name="&gt;&gt;gridColumn37.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn38.Name" xml:space="preserve">
    <value>gridColumn38</value>
  </data>
  <data name="&gt;&gt;gridColumn38.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView14.Name" xml:space="preserve">
    <value>gridView14</value>
  </data>
  <data name="&gt;&gt;gridView14.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn34.Name" xml:space="preserve">
    <value>gridColumn34</value>
  </data>
  <data name="&gt;&gt;gridColumn34.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn35.Name" xml:space="preserve">
    <value>gridColumn35</value>
  </data>
  <data name="&gt;&gt;gridColumn35.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;grdVAccount.Name" xml:space="preserve">
    <value>grdVAccount</value>
  </data>
  <data name="&gt;&gt;grdVAccount.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_ParentActId.Name" xml:space="preserve">
    <value>col_ParentActId</value>
  </data>
  <data name="&gt;&gt;col_ParentActId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_CostCenter.Name" xml:space="preserve">
    <value>col_CostCenter</value>
  </data>
  <data name="&gt;&gt;col_CostCenter.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_AcNumber.Name" xml:space="preserve">
    <value>col_AcNumber</value>
  </data>
  <data name="&gt;&gt;col_AcNumber.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_AcNameAr.Name" xml:space="preserve">
    <value>col_AcNameAr</value>
  </data>
  <data name="&gt;&gt;col_AcNameAr.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_AccountId.Name" xml:space="preserve">
    <value>col_AccountId</value>
  </data>
  <data name="&gt;&gt;col_AccountId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView13.Name" xml:space="preserve">
    <value>gridView13</value>
  </data>
  <data name="&gt;&gt;gridView13.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn30.Name" xml:space="preserve">
    <value>gridColumn30</value>
  </data>
  <data name="&gt;&gt;gridColumn30.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn31.Name" xml:space="preserve">
    <value>gridColumn31</value>
  </data>
  <data name="&gt;&gt;gridColumn31.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView9.Name" xml:space="preserve">
    <value>gridView9</value>
  </data>
  <data name="&gt;&gt;gridView9.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn20.Name" xml:space="preserve">
    <value>gridColumn20</value>
  </data>
  <data name="&gt;&gt;gridColumn20.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn17.Name" xml:space="preserve">
    <value>gridColumn17</value>
  </data>
  <data name="&gt;&gt;gridColumn17.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn21.Name" xml:space="preserve">
    <value>gridColumn21</value>
  </data>
  <data name="&gt;&gt;gridColumn21.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView8.Name" xml:space="preserve">
    <value>gridView8</value>
  </data>
  <data name="&gt;&gt;gridView8.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn18.Name" xml:space="preserve">
    <value>gridColumn18</value>
  </data>
  <data name="&gt;&gt;gridColumn18.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn19.Name" xml:space="preserve">
    <value>gridColumn19</value>
  </data>
  <data name="&gt;&gt;gridColumn19.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView7.Name" xml:space="preserve">
    <value>gridView7</value>
  </data>
  <data name="&gt;&gt;gridView7.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn14.Name" xml:space="preserve">
    <value>gridColumn14</value>
  </data>
  <data name="&gt;&gt;gridColumn14.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn15.Name" xml:space="preserve">
    <value>gridColumn15</value>
  </data>
  <data name="&gt;&gt;gridColumn15.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn16.Name" xml:space="preserve">
    <value>gridColumn16</value>
  </data>
  <data name="&gt;&gt;gridColumn16.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView5.Name" xml:space="preserve">
    <value>gridView5</value>
  </data>
  <data name="&gt;&gt;gridView5.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn13.Name" xml:space="preserve">
    <value>gridColumn13</value>
  </data>
  <data name="&gt;&gt;gridColumn13.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn12.Name" xml:space="preserve">
    <value>gridColumn12</value>
  </data>
  <data name="&gt;&gt;gridColumn12.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn11.Name" xml:space="preserve">
    <value>gridColumn11</value>
  </data>
  <data name="&gt;&gt;gridColumn11.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn53.Name" xml:space="preserve">
    <value>gridColumn53</value>
  </data>
  <data name="&gt;&gt;gridColumn53.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView2.Name" xml:space="preserve">
    <value>gridView2</value>
  </data>
  <data name="&gt;&gt;gridView2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn10.Name" xml:space="preserve">
    <value>gridColumn10</value>
  </data>
  <data name="&gt;&gt;gridColumn10.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn9.Name" xml:space="preserve">
    <value>gridColumn9</value>
  </data>
  <data name="&gt;&gt;gridColumn9.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn8.Name" xml:space="preserve">
    <value>gridColumn8</value>
  </data>
  <data name="&gt;&gt;gridColumn8.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView6.Name" xml:space="preserve">
    <value>gridView6</value>
  </data>
  <data name="&gt;&gt;gridView6.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn3.Name" xml:space="preserve">
    <value>gridColumn3</value>
  </data>
  <data name="&gt;&gt;gridColumn3.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn4.Name" xml:space="preserve">
    <value>gridColumn4</value>
  </data>
  <data name="&gt;&gt;gridColumn4.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView4.Name" xml:space="preserve">
    <value>gridView4</value>
  </data>
  <data name="&gt;&gt;gridView4.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colItemId1.Name" xml:space="preserve">
    <value>colItemId1</value>
  </data>
  <data name="&gt;&gt;colItemId1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colItemCode11.Name" xml:space="preserve">
    <value>colItemCode11</value>
  </data>
  <data name="&gt;&gt;colItemCode11.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn36.Name" xml:space="preserve">
    <value>gridColumn36</value>
  </data>
  <data name="&gt;&gt;gridColumn36.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colItemNameAr1.Name" xml:space="preserve">
    <value>colItemNameAr1</value>
  </data>
  <data name="&gt;&gt;colItemNameAr1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colItemNameEn1.Name" xml:space="preserve">
    <value>colItemNameEn1</value>
  </data>
  <data name="&gt;&gt;colItemNameEn1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit1View.Name" xml:space="preserve">
    <value>gridLookUpEdit1View</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit1View.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colItemId.Name" xml:space="preserve">
    <value>colItemId</value>
  </data>
  <data name="&gt;&gt;colItemId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colItemCode1.Name" xml:space="preserve">
    <value>colItemCode1</value>
  </data>
  <data name="&gt;&gt;colItemCode1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colItemCode2.Name" xml:space="preserve">
    <value>colItemCode2</value>
  </data>
  <data name="&gt;&gt;colItemCode2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colItemNameAr.Name" xml:space="preserve">
    <value>colItemNameAr</value>
  </data>
  <data name="&gt;&gt;colItemNameAr.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colItemNameEn.Name" xml:space="preserve">
    <value>colItemNameEn</value>
  </data>
  <data name="&gt;&gt;colItemNameEn.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView1.Name" xml:space="preserve">
    <value>gridView1</value>
  </data>
  <data name="&gt;&gt;gridView1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn7.Name" xml:space="preserve">
    <value>gridColumn7</value>
  </data>
  <data name="&gt;&gt;gridColumn7.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn6.Name" xml:space="preserve">
    <value>gridColumn6</value>
  </data>
  <data name="&gt;&gt;gridColumn6.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn5.Name" xml:space="preserve">
    <value>gridColumn5</value>
  </data>
  <data name="&gt;&gt;gridColumn5.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn54.Name" xml:space="preserve">
    <value>gridColumn54</value>
  </data>
  <data name="&gt;&gt;gridColumn54.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView3.Name" xml:space="preserve">
    <value>gridView3</value>
  </data>
  <data name="&gt;&gt;gridView3.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn2.Name" xml:space="preserve">
    <value>gridColumn2</value>
  </data>
  <data name="&gt;&gt;gridColumn2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn1.Name" xml:space="preserve">
    <value>gridColumn1</value>
  </data>
  <data name="&gt;&gt;gridColumn1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barManager2.Name" xml:space="preserve">
    <value>barManager2</value>
  </data>
  <data name="&gt;&gt;barManager2.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarManager, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barAndDockingController2.Name" xml:space="preserve">
    <value>barAndDockingController2</value>
  </data>
  <data name="&gt;&gt;barAndDockingController2.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarAndDockingController, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barButtonItem2.Name" xml:space="preserve">
    <value>barButtonItem2</value>
  </data>
  <data name="&gt;&gt;barButtonItem2.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barButtonItem1.Name" xml:space="preserve">
    <value>barButtonItem1</value>
  </data>
  <data name="&gt;&gt;barButtonItem1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barButtonItem3.Name" xml:space="preserve">
    <value>barButtonItem3</value>
  </data>
  <data name="&gt;&gt;barButtonItem3.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repositoryItemTextEdit2.Name" xml:space="preserve">
    <value>repositoryItemTextEdit2</value>
  </data>
  <data name="&gt;&gt;repositoryItemTextEdit2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemTextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlGroup1.Name" xml:space="preserve">
    <value>layoutControlGroup1</value>
  </data>
  <data name="&gt;&gt;layoutControlGroup1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlGroup, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;comp1.Name" xml:space="preserve">
    <value>comp1</value>
  </data>
  <data name="&gt;&gt;comp1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;item1.Name" xml:space="preserve">
    <value>item1</value>
  </data>
  <data name="&gt;&gt;item1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;item2.Name" xml:space="preserve">
    <value>item2</value>
  </data>
  <data name="&gt;&gt;item2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;item3.Name" xml:space="preserve">
    <value>item3</value>
  </data>
  <data name="&gt;&gt;item3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;item4.Name" xml:space="preserve">
    <value>item4</value>
  </data>
  <data name="&gt;&gt;item4.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;date2.Name" xml:space="preserve">
    <value>date2</value>
  </data>
  <data name="&gt;&gt;date2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;date3.Name" xml:space="preserve">
    <value>date3</value>
  </data>
  <data name="&gt;&gt;date3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;date4.Name" xml:space="preserve">
    <value>date4</value>
  </data>
  <data name="&gt;&gt;date4.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;date1.Name" xml:space="preserve">
    <value>date1</value>
  </data>
  <data name="&gt;&gt;date1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;store3.Name" xml:space="preserve">
    <value>store3</value>
  </data>
  <data name="&gt;&gt;store3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;store4.Name" xml:space="preserve">
    <value>store4</value>
  </data>
  <data name="&gt;&gt;store4.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;store2.Name" xml:space="preserve">
    <value>store2</value>
  </data>
  <data name="&gt;&gt;store2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;store1.Name" xml:space="preserve">
    <value>store1</value>
  </data>
  <data name="&gt;&gt;store1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;vendor4.Name" xml:space="preserve">
    <value>vendor4</value>
  </data>
  <data name="&gt;&gt;vendor4.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;vendor3.Name" xml:space="preserve">
    <value>vendor3</value>
  </data>
  <data name="&gt;&gt;vendor3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;vendor2.Name" xml:space="preserve">
    <value>vendor2</value>
  </data>
  <data name="&gt;&gt;vendor2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;vendor1.Name" xml:space="preserve">
    <value>vendor1</value>
  </data>
  <data name="&gt;&gt;vendor1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;comp4.Name" xml:space="preserve">
    <value>comp4</value>
  </data>
  <data name="&gt;&gt;comp4.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;comp2.Name" xml:space="preserve">
    <value>comp2</value>
  </data>
  <data name="&gt;&gt;comp2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;comp3.Name" xml:space="preserve">
    <value>comp3</value>
  </data>
  <data name="&gt;&gt;comp3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;expDate3.Name" xml:space="preserve">
    <value>expDate3</value>
  </data>
  <data name="&gt;&gt;expDate3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;expDate1.Name" xml:space="preserve">
    <value>expDate1</value>
  </data>
  <data name="&gt;&gt;expDate1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;expDate2.Name" xml:space="preserve">
    <value>expDate2</value>
  </data>
  <data name="&gt;&gt;expDate2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lblFltrName.Name" xml:space="preserve">
    <value>lblFltrName</value>
  </data>
  <data name="&gt;&gt;lblFltrName.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lblTo.Name" xml:space="preserve">
    <value>lblTo</value>
  </data>
  <data name="&gt;&gt;lblTo.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lblFrom.Name" xml:space="preserve">
    <value>lblFrom</value>
  </data>
  <data name="&gt;&gt;lblFrom.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lblFltrType.Name" xml:space="preserve">
    <value>lblFltrType</value>
  </data>
  <data name="&gt;&gt;lblFltrType.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Customer1.Name" xml:space="preserve">
    <value>Customer1</value>
  </data>
  <data name="&gt;&gt;Customer1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Customer2.Name" xml:space="preserve">
    <value>Customer2</value>
  </data>
  <data name="&gt;&gt;Customer2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Customer3.Name" xml:space="preserve">
    <value>Customer3</value>
  </data>
  <data name="&gt;&gt;Customer3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Customer4.Name" xml:space="preserve">
    <value>Customer4</value>
  </data>
  <data name="&gt;&gt;Customer4.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;process3.Name" xml:space="preserve">
    <value>process3</value>
  </data>
  <data name="&gt;&gt;process3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;process1.Name" xml:space="preserve">
    <value>process1</value>
  </data>
  <data name="&gt;&gt;process1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;process2.Name" xml:space="preserve">
    <value>process2</value>
  </data>
  <data name="&gt;&gt;process2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cost3.Name" xml:space="preserve">
    <value>cost3</value>
  </data>
  <data name="&gt;&gt;cost3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cost2.Name" xml:space="preserve">
    <value>cost2</value>
  </data>
  <data name="&gt;&gt;cost2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;acc3.Name" xml:space="preserve">
    <value>acc3</value>
  </data>
  <data name="&gt;&gt;acc3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cost1.Name" xml:space="preserve">
    <value>cost1</value>
  </data>
  <data name="&gt;&gt;cost1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;acc1.Name" xml:space="preserve">
    <value>acc1</value>
  </data>
  <data name="&gt;&gt;acc1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cstmLst3.Name" xml:space="preserve">
    <value>cstmLst3</value>
  </data>
  <data name="&gt;&gt;cstmLst3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cstmLst2.Name" xml:space="preserve">
    <value>cstmLst2</value>
  </data>
  <data name="&gt;&gt;cstmLst2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cstmLst1.Name" xml:space="preserve">
    <value>cstmLst1</value>
  </data>
  <data name="&gt;&gt;cstmLst1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;user4.Name" xml:space="preserve">
    <value>user4</value>
  </data>
  <data name="&gt;&gt;user4.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;user2.Name" xml:space="preserve">
    <value>user2</value>
  </data>
  <data name="&gt;&gt;user2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;user1.Name" xml:space="preserve">
    <value>user1</value>
  </data>
  <data name="&gt;&gt;user1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;user3.Name" xml:space="preserve">
    <value>user3</value>
  </data>
  <data name="&gt;&gt;user3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Batch3.Name" xml:space="preserve">
    <value>Batch3</value>
  </data>
  <data name="&gt;&gt;Batch3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Batch4.Name" xml:space="preserve">
    <value>Batch4</value>
  </data>
  <data name="&gt;&gt;Batch4.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;salesEmp3.Name" xml:space="preserve">
    <value>salesEmp3</value>
  </data>
  <data name="&gt;&gt;salesEmp3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;salesEmp4.Name" xml:space="preserve">
    <value>salesEmp4</value>
  </data>
  <data name="&gt;&gt;salesEmp4.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;salesEmp1.Name" xml:space="preserve">
    <value>salesEmp1</value>
  </data>
  <data name="&gt;&gt;salesEmp1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;salesEmp2.Name" xml:space="preserve">
    <value>salesEmp2</value>
  </data>
  <data name="&gt;&gt;salesEmp2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Batch1.Name" xml:space="preserve">
    <value>Batch1</value>
  </data>
  <data name="&gt;&gt;Batch1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Batch2.Name" xml:space="preserve">
    <value>Batch2</value>
  </data>
  <data name="&gt;&gt;Batch2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;custGroup1.Name" xml:space="preserve">
    <value>custGroup1</value>
  </data>
  <data name="&gt;&gt;custGroup1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;custGroup2.Name" xml:space="preserve">
    <value>custGroup2</value>
  </data>
  <data name="&gt;&gt;custGroup2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;custGroup3.Name" xml:space="preserve">
    <value>custGroup3</value>
  </data>
  <data name="&gt;&gt;custGroup3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;acc2.Name" xml:space="preserve">
    <value>acc2</value>
  </data>
  <data name="&gt;&gt;acc2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;ParentItem2.Name" xml:space="preserve">
    <value>ParentItem2</value>
  </data>
  <data name="&gt;&gt;ParentItem2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;ParentItem3.Name" xml:space="preserve">
    <value>ParentItem3</value>
  </data>
  <data name="&gt;&gt;ParentItem3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;dim4.Name" xml:space="preserve">
    <value>dim4</value>
  </data>
  <data name="&gt;&gt;dim4.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;dim2.Name" xml:space="preserve">
    <value>dim2</value>
  </data>
  <data name="&gt;&gt;dim2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;dim3.Name" xml:space="preserve">
    <value>dim3</value>
  </data>
  <data name="&gt;&gt;dim3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;ParentItem1.Name" xml:space="preserve">
    <value>ParentItem1</value>
  </data>
  <data name="&gt;&gt;ParentItem1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Mtrx0.Name" xml:space="preserve">
    <value>Mtrx0</value>
  </data>
  <data name="&gt;&gt;Mtrx0.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;dim1.Name" xml:space="preserve">
    <value>dim1</value>
  </data>
  <data name="&gt;&gt;dim1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;jo1.Name" xml:space="preserve">
    <value>jo1</value>
  </data>
  <data name="&gt;&gt;jo1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;jo2.Name" xml:space="preserve">
    <value>jo2</value>
  </data>
  <data name="&gt;&gt;jo2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;jo3.Name" xml:space="preserve">
    <value>jo3</value>
  </data>
  <data name="&gt;&gt;jo3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;jo4.Name" xml:space="preserve">
    <value>jo4</value>
  </data>
  <data name="&gt;&gt;jo4.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;itmTyp3.Name" xml:space="preserve">
    <value>itmTyp3</value>
  </data>
  <data name="&gt;&gt;itmTyp3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;itmTyp1.Name" xml:space="preserve">
    <value>itmTyp1</value>
  </data>
  <data name="&gt;&gt;itmTyp1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;itmTyp2.Name" xml:space="preserve">
    <value>itmTyp2</value>
  </data>
  <data name="&gt;&gt;itmTyp2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;sell1.Name" xml:space="preserve">
    <value>sell1</value>
  </data>
  <data name="&gt;&gt;sell1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;sell2.Name" xml:space="preserve">
    <value>sell2</value>
  </data>
  <data name="&gt;&gt;sell2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;sell3.Name" xml:space="preserve">
    <value>sell3</value>
  </data>
  <data name="&gt;&gt;sell3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;sell4.Name" xml:space="preserve">
    <value>sell4</value>
  </data>
  <data name="&gt;&gt;sell4.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cat1.Name" xml:space="preserve">
    <value>cat1</value>
  </data>
  <data name="&gt;&gt;cat1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cat2.Name" xml:space="preserve">
    <value>cat2</value>
  </data>
  <data name="&gt;&gt;cat2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cat3.Name" xml:space="preserve">
    <value>cat3</value>
  </data>
  <data name="&gt;&gt;cat3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cat4.Name" xml:space="preserve">
    <value>cat4</value>
  </data>
  <data name="&gt;&gt;cat4.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;InvoiceBook4.Name" xml:space="preserve">
    <value>InvoiceBook4</value>
  </data>
  <data name="&gt;&gt;InvoiceBook4.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;InvoiceBook1.Name" xml:space="preserve">
    <value>InvoiceBook1</value>
  </data>
  <data name="&gt;&gt;InvoiceBook1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;InvoiceBook2.Name" xml:space="preserve">
    <value>InvoiceBook2</value>
  </data>
  <data name="&gt;&gt;InvoiceBook2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;InvoiceBook3.Name" xml:space="preserve">
    <value>InvoiceBook3</value>
  </data>
  <data name="&gt;&gt;InvoiceBook3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;EmpGroup4.Name" xml:space="preserve">
    <value>EmpGroup4</value>
  </data>
  <data name="&gt;&gt;EmpGroup4.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;EmpGroup1.Name" xml:space="preserve">
    <value>EmpGroup1</value>
  </data>
  <data name="&gt;&gt;EmpGroup1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;EmpGroup2.Name" xml:space="preserve">
    <value>EmpGroup2</value>
  </data>
  <data name="&gt;&gt;EmpGroup2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;EmpGroup3.Name" xml:space="preserve">
    <value>EmpGroup3</value>
  </data>
  <data name="&gt;&gt;EmpGroup3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;QC1.Name" xml:space="preserve">
    <value>QC1</value>
  </data>
  <data name="&gt;&gt;QC1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;QC2.Name" xml:space="preserve">
    <value>QC2</value>
  </data>
  <data name="&gt;&gt;QC2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;QC3.Name" xml:space="preserve">
    <value>QC3</value>
  </data>
  <data name="&gt;&gt;QC3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;QC4.Name" xml:space="preserve">
    <value>QC4</value>
  </data>
  <data name="&gt;&gt;QC4.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;VenGroup2.Name" xml:space="preserve">
    <value>VenGroup2</value>
  </data>
  <data name="&gt;&gt;VenGroup2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;VenGroup1.Name" xml:space="preserve">
    <value>VenGroup1</value>
  </data>
  <data name="&gt;&gt;VenGroup1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.SimpleLabelItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;VenGroup3.Name" xml:space="preserve">
    <value>VenGroup3</value>
  </data>
  <data name="&gt;&gt;VenGroup3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.EmptySpaceItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Mtrx1.Name" xml:space="preserve">
    <value>Mtrx1</value>
  </data>
  <data name="&gt;&gt;Mtrx1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Mtrx2.Name" xml:space="preserve">
    <value>Mtrx2</value>
  </data>
  <data name="&gt;&gt;Mtrx2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;Mtrx3.Name" xml:space="preserve">
    <value>Mtrx3</value>
  </data>
  <data name="&gt;&gt;Mtrx3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cat5.Name" xml:space="preserve">
    <value>cat5</value>
  </data>
  <data name="&gt;&gt;cat5.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cat6.Name" xml:space="preserve">
    <value>cat6</value>
  </data>
  <data name="&gt;&gt;cat6.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cat7.Name" xml:space="preserve">
    <value>cat7</value>
  </data>
  <data name="&gt;&gt;cat7.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;cat8.Name" xml:space="preserve">
    <value>cat8</value>
  </data>
  <data name="&gt;&gt;cat8.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;emptySpaceItem2.Name" xml:space="preserve">
    <value>emptySpaceItem2</value>
  </data>
  <data name="&gt;&gt;emptySpaceItem2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.EmptySpaceItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;emptySpaceItem8.Name" xml:space="preserve">
    <value>emptySpaceItem8</value>
  </data>
  <data name="&gt;&gt;emptySpaceItem8.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.EmptySpaceItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;emptySpaceItem1.Name" xml:space="preserve">
    <value>emptySpaceItem1</value>
  </data>
  <data name="&gt;&gt;emptySpaceItem1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;emptySpaceItem3.Name" xml:space="preserve">
    <value>emptySpaceItem3</value>
  </data>
  <data name="&gt;&gt;emptySpaceItem3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;emptySpaceItem4.Name" xml:space="preserve">
    <value>emptySpaceItem4</value>
  </data>
  <data name="&gt;&gt;emptySpaceItem4.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;simpleLabelItem1.Name" xml:space="preserve">
    <value>simpleLabelItem1</value>
  </data>
  <data name="&gt;&gt;simpleLabelItem1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;simpleLabelItem2.Name" xml:space="preserve">
    <value>simpleLabelItem2</value>
  </data>
  <data name="&gt;&gt;simpleLabelItem2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;simpleLabelItem3.Name" xml:space="preserve">
    <value>simpleLabelItem3</value>
  </data>
  <data name="&gt;&gt;simpleLabelItem3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;emptySpaceItem6.Name" xml:space="preserve">
    <value>emptySpaceItem6</value>
  </data>
  <data name="&gt;&gt;emptySpaceItem6.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn33.Name" xml:space="preserve">
    <value>gridColumn33</value>
  </data>
  <data name="&gt;&gt;gridColumn33.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView21.Name" xml:space="preserve">
    <value>gridView21</value>
  </data>
  <data name="&gt;&gt;gridView21.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn55.Name" xml:space="preserve">
    <value>gridColumn55</value>
  </data>
  <data name="&gt;&gt;gridColumn55.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn56.Name" xml:space="preserve">
    <value>gridColumn56</value>
  </data>
  <data name="&gt;&gt;gridColumn56.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn57.Name" xml:space="preserve">
    <value>gridColumn57</value>
  </data>
  <data name="&gt;&gt;gridColumn57.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView22.Name" xml:space="preserve">
    <value>gridView22</value>
  </data>
  <data name="&gt;&gt;gridView22.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn58.Name" xml:space="preserve">
    <value>gridColumn58</value>
  </data>
  <data name="&gt;&gt;gridColumn58.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn59.Name" xml:space="preserve">
    <value>gridColumn59</value>
  </data>
  <data name="&gt;&gt;gridColumn59.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn60.Name" xml:space="preserve">
    <value>gridColumn60</value>
  </data>
  <data name="&gt;&gt;gridColumn60.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView23.Name" xml:space="preserve">
    <value>gridView23</value>
  </data>
  <data name="&gt;&gt;gridView23.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn61.Name" xml:space="preserve">
    <value>gridColumn61</value>
  </data>
  <data name="&gt;&gt;gridColumn61.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn62.Name" xml:space="preserve">
    <value>gridColumn62</value>
  </data>
  <data name="&gt;&gt;gridColumn62.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn63.Name" xml:space="preserve">
    <value>gridColumn63</value>
  </data>
  <data name="&gt;&gt;gridColumn63.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView24.Name" xml:space="preserve">
    <value>gridView24</value>
  </data>
  <data name="&gt;&gt;gridView24.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn64.Name" xml:space="preserve">
    <value>gridColumn64</value>
  </data>
  <data name="&gt;&gt;gridColumn64.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn65.Name" xml:space="preserve">
    <value>gridColumn65</value>
  </data>
  <data name="&gt;&gt;gridColumn65.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn66.Name" xml:space="preserve">
    <value>gridColumn66</value>
  </data>
  <data name="&gt;&gt;gridColumn66.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView25.Name" xml:space="preserve">
    <value>gridView25</value>
  </data>
  <data name="&gt;&gt;gridView25.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn67.Name" xml:space="preserve">
    <value>gridColumn67</value>
  </data>
  <data name="&gt;&gt;gridColumn67.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn68.Name" xml:space="preserve">
    <value>gridColumn68</value>
  </data>
  <data name="&gt;&gt;gridColumn68.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn69.Name" xml:space="preserve">
    <value>gridColumn69</value>
  </data>
  <data name="&gt;&gt;gridColumn69.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView26.Name" xml:space="preserve">
    <value>gridView26</value>
  </data>
  <data name="&gt;&gt;gridView26.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn70.Name" xml:space="preserve">
    <value>gridColumn70</value>
  </data>
  <data name="&gt;&gt;gridColumn70.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn71.Name" xml:space="preserve">
    <value>gridColumn71</value>
  </data>
  <data name="&gt;&gt;gridColumn71.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn72.Name" xml:space="preserve">
    <value>gridColumn72</value>
  </data>
  <data name="&gt;&gt;gridColumn72.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView27.Name" xml:space="preserve">
    <value>gridView27</value>
  </data>
  <data name="&gt;&gt;gridView27.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn73.Name" xml:space="preserve">
    <value>gridColumn73</value>
  </data>
  <data name="&gt;&gt;gridColumn73.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn74.Name" xml:space="preserve">
    <value>gridColumn74</value>
  </data>
  <data name="&gt;&gt;gridColumn74.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn75.Name" xml:space="preserve">
    <value>gridColumn75</value>
  </data>
  <data name="&gt;&gt;gridColumn75.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView28.Name" xml:space="preserve">
    <value>gridView28</value>
  </data>
  <data name="&gt;&gt;gridView28.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn76.Name" xml:space="preserve">
    <value>gridColumn76</value>
  </data>
  <data name="&gt;&gt;gridColumn76.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn77.Name" xml:space="preserve">
    <value>gridColumn77</value>
  </data>
  <data name="&gt;&gt;gridColumn77.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView29.Name" xml:space="preserve">
    <value>gridView29</value>
  </data>
  <data name="&gt;&gt;gridView29.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn78.Name" xml:space="preserve">
    <value>gridColumn78</value>
  </data>
  <data name="&gt;&gt;gridColumn78.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn79.Name" xml:space="preserve">
    <value>gridColumn79</value>
  </data>
  <data name="&gt;&gt;gridColumn79.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView30.Name" xml:space="preserve">
    <value>gridView30</value>
  </data>
  <data name="&gt;&gt;gridView30.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn80.Name" xml:space="preserve">
    <value>gridColumn80</value>
  </data>
  <data name="&gt;&gt;gridColumn80.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn81.Name" xml:space="preserve">
    <value>gridColumn81</value>
  </data>
  <data name="&gt;&gt;gridColumn81.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView31.Name" xml:space="preserve">
    <value>gridView31</value>
  </data>
  <data name="&gt;&gt;gridView31.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn82.Name" xml:space="preserve">
    <value>gridColumn82</value>
  </data>
  <data name="&gt;&gt;gridColumn82.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn83.Name" xml:space="preserve">
    <value>gridColumn83</value>
  </data>
  <data name="&gt;&gt;gridColumn83.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn84.Name" xml:space="preserve">
    <value>gridColumn84</value>
  </data>
  <data name="&gt;&gt;gridColumn84.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn85.Name" xml:space="preserve">
    <value>gridColumn85</value>
  </data>
  <data name="&gt;&gt;gridColumn85.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn86.Name" xml:space="preserve">
    <value>gridColumn86</value>
  </data>
  <data name="&gt;&gt;gridColumn86.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView32.Name" xml:space="preserve">
    <value>gridView32</value>
  </data>
  <data name="&gt;&gt;gridView32.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn87.Name" xml:space="preserve">
    <value>gridColumn87</value>
  </data>
  <data name="&gt;&gt;gridColumn87.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn88.Name" xml:space="preserve">
    <value>gridColumn88</value>
  </data>
  <data name="&gt;&gt;gridColumn88.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView33.Name" xml:space="preserve">
    <value>gridView33</value>
  </data>
  <data name="&gt;&gt;gridView33.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn89.Name" xml:space="preserve">
    <value>gridColumn89</value>
  </data>
  <data name="&gt;&gt;gridColumn89.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn90.Name" xml:space="preserve">
    <value>gridColumn90</value>
  </data>
  <data name="&gt;&gt;gridColumn90.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn91.Name" xml:space="preserve">
    <value>gridColumn91</value>
  </data>
  <data name="&gt;&gt;gridColumn91.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView34.Name" xml:space="preserve">
    <value>gridView34</value>
  </data>
  <data name="&gt;&gt;gridView34.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn92.Name" xml:space="preserve">
    <value>gridColumn92</value>
  </data>
  <data name="&gt;&gt;gridColumn92.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn93.Name" xml:space="preserve">
    <value>gridColumn93</value>
  </data>
  <data name="&gt;&gt;gridColumn93.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView35.Name" xml:space="preserve">
    <value>gridView35</value>
  </data>
  <data name="&gt;&gt;gridView35.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn94.Name" xml:space="preserve">
    <value>gridColumn94</value>
  </data>
  <data name="&gt;&gt;gridColumn94.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn95.Name" xml:space="preserve">
    <value>gridColumn95</value>
  </data>
  <data name="&gt;&gt;gridColumn95.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn96.Name" xml:space="preserve">
    <value>gridColumn96</value>
  </data>
  <data name="&gt;&gt;gridColumn96.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView36.Name" xml:space="preserve">
    <value>gridView36</value>
  </data>
  <data name="&gt;&gt;gridView36.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn97.Name" xml:space="preserve">
    <value>gridColumn97</value>
  </data>
  <data name="&gt;&gt;gridColumn97.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn98.Name" xml:space="preserve">
    <value>gridColumn98</value>
  </data>
  <data name="&gt;&gt;gridColumn98.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn99.Name" xml:space="preserve">
    <value>gridColumn99</value>
  </data>
  <data name="&gt;&gt;gridColumn99.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn100.Name" xml:space="preserve">
    <value>gridColumn100</value>
  </data>
  <data name="&gt;&gt;gridColumn100.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView37.Name" xml:space="preserve">
    <value>gridView37</value>
  </data>
  <data name="&gt;&gt;gridView37.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn101.Name" xml:space="preserve">
    <value>gridColumn101</value>
  </data>
  <data name="&gt;&gt;gridColumn101.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn102.Name" xml:space="preserve">
    <value>gridColumn102</value>
  </data>
  <data name="&gt;&gt;gridColumn102.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn103.Name" xml:space="preserve">
    <value>gridColumn103</value>
  </data>
  <data name="&gt;&gt;gridColumn103.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView38.Name" xml:space="preserve">
    <value>gridView38</value>
  </data>
  <data name="&gt;&gt;gridView38.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn104.Name" xml:space="preserve">
    <value>gridColumn104</value>
  </data>
  <data name="&gt;&gt;gridColumn104.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn105.Name" xml:space="preserve">
    <value>gridColumn105</value>
  </data>
  <data name="&gt;&gt;gridColumn105.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView39.Name" xml:space="preserve">
    <value>gridView39</value>
  </data>
  <data name="&gt;&gt;gridView39.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn106.Name" xml:space="preserve">
    <value>gridColumn106</value>
  </data>
  <data name="&gt;&gt;gridColumn106.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn107.Name" xml:space="preserve">
    <value>gridColumn107</value>
  </data>
  <data name="&gt;&gt;gridColumn107.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn108.Name" xml:space="preserve">
    <value>gridColumn108</value>
  </data>
  <data name="&gt;&gt;gridColumn108.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn109.Name" xml:space="preserve">
    <value>gridColumn109</value>
  </data>
  <data name="&gt;&gt;gridColumn109.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn110.Name" xml:space="preserve">
    <value>gridColumn110</value>
  </data>
  <data name="&gt;&gt;gridColumn110.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView40.Name" xml:space="preserve">
    <value>gridView40</value>
  </data>
  <data name="&gt;&gt;gridView40.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn111.Name" xml:space="preserve">
    <value>gridColumn111</value>
  </data>
  <data name="&gt;&gt;gridColumn111.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn112.Name" xml:space="preserve">
    <value>gridColumn112</value>
  </data>
  <data name="&gt;&gt;gridColumn112.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn113.Name" xml:space="preserve">
    <value>gridColumn113</value>
  </data>
  <data name="&gt;&gt;gridColumn113.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn114.Name" xml:space="preserve">
    <value>gridColumn114</value>
  </data>
  <data name="&gt;&gt;gridColumn114.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn115.Name" xml:space="preserve">
    <value>gridColumn115</value>
  </data>
  <data name="&gt;&gt;gridColumn115.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView41.Name" xml:space="preserve">
    <value>gridView41</value>
  </data>
  <data name="&gt;&gt;gridView41.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn116.Name" xml:space="preserve">
    <value>gridColumn116</value>
  </data>
  <data name="&gt;&gt;gridColumn116.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn117.Name" xml:space="preserve">
    <value>gridColumn117</value>
  </data>
  <data name="&gt;&gt;gridColumn117.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn118.Name" xml:space="preserve">
    <value>gridColumn118</value>
  </data>
  <data name="&gt;&gt;gridColumn118.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn119.Name" xml:space="preserve">
    <value>gridColumn119</value>
  </data>
  <data name="&gt;&gt;gridColumn119.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView42.Name" xml:space="preserve">
    <value>gridView42</value>
  </data>
  <data name="&gt;&gt;gridView42.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn120.Name" xml:space="preserve">
    <value>gridColumn120</value>
  </data>
  <data name="&gt;&gt;gridColumn120.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn121.Name" xml:space="preserve">
    <value>gridColumn121</value>
  </data>
  <data name="&gt;&gt;gridColumn121.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlGroup3.Name" xml:space="preserve">
    <value>layoutControlGroup3</value>
  </data>
  <data name="&gt;&gt;layoutControlGroup3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlGroup, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem1.Name" xml:space="preserve">
    <value>layoutControlItem1</value>
  </data>
  <data name="&gt;&gt;layoutControlItem1.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem2.Name" xml:space="preserve">
    <value>layoutControlItem2</value>
  </data>
  <data name="&gt;&gt;layoutControlItem2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem3.Name" xml:space="preserve">
    <value>layoutControlItem3</value>
  </data>
  <data name="&gt;&gt;layoutControlItem3.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem4.Name" xml:space="preserve">
    <value>layoutControlItem4</value>
  </data>
  <data name="&gt;&gt;layoutControlItem4.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem5.Name" xml:space="preserve">
    <value>layoutControlItem5</value>
  </data>
  <data name="&gt;&gt;layoutControlItem5.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem6.Name" xml:space="preserve">
    <value>layoutControlItem6</value>
  </data>
  <data name="&gt;&gt;layoutControlItem6.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem7.Name" xml:space="preserve">
    <value>layoutControlItem7</value>
  </data>
  <data name="&gt;&gt;layoutControlItem7.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem8.Name" xml:space="preserve">
    <value>layoutControlItem8</value>
  </data>
  <data name="&gt;&gt;layoutControlItem8.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem9.Name" xml:space="preserve">
    <value>layoutControlItem9</value>
  </data>
  <data name="&gt;&gt;layoutControlItem9.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem10.Name" xml:space="preserve">
    <value>layoutControlItem10</value>
  </data>
  <data name="&gt;&gt;layoutControlItem10.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem11.Name" xml:space="preserve">
    <value>layoutControlItem11</value>
  </data>
  <data name="&gt;&gt;layoutControlItem11.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem12.Name" xml:space="preserve">
    <value>layoutControlItem12</value>
  </data>
  <data name="&gt;&gt;layoutControlItem12.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem13.Name" xml:space="preserve">
    <value>layoutControlItem13</value>
  </data>
  <data name="&gt;&gt;layoutControlItem13.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem14.Name" xml:space="preserve">
    <value>layoutControlItem14</value>
  </data>
  <data name="&gt;&gt;layoutControlItem14.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem15.Name" xml:space="preserve">
    <value>layoutControlItem15</value>
  </data>
  <data name="&gt;&gt;layoutControlItem15.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem16.Name" xml:space="preserve">
    <value>layoutControlItem16</value>
  </data>
  <data name="&gt;&gt;layoutControlItem16.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem17.Name" xml:space="preserve">
    <value>layoutControlItem17</value>
  </data>
  <data name="&gt;&gt;layoutControlItem17.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem18.Name" xml:space="preserve">
    <value>layoutControlItem18</value>
  </data>
  <data name="&gt;&gt;layoutControlItem18.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem19.Name" xml:space="preserve">
    <value>layoutControlItem19</value>
  </data>
  <data name="&gt;&gt;layoutControlItem19.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem20.Name" xml:space="preserve">
    <value>layoutControlItem20</value>
  </data>
  <data name="&gt;&gt;layoutControlItem20.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem21.Name" xml:space="preserve">
    <value>layoutControlItem21</value>
  </data>
  <data name="&gt;&gt;layoutControlItem21.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem22.Name" xml:space="preserve">
    <value>layoutControlItem22</value>
  </data>
  <data name="&gt;&gt;layoutControlItem22.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem23.Name" xml:space="preserve">
    <value>layoutControlItem23</value>
  </data>
  <data name="&gt;&gt;layoutControlItem23.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem24.Name" xml:space="preserve">
    <value>layoutControlItem24</value>
  </data>
  <data name="&gt;&gt;layoutControlItem24.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem25.Name" xml:space="preserve">
    <value>layoutControlItem25</value>
  </data>
  <data name="&gt;&gt;layoutControlItem25.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem26.Name" xml:space="preserve">
    <value>layoutControlItem26</value>
  </data>
  <data name="&gt;&gt;layoutControlItem26.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem27.Name" xml:space="preserve">
    <value>layoutControlItem27</value>
  </data>
  <data name="&gt;&gt;layoutControlItem27.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem28.Name" xml:space="preserve">
    <value>layoutControlItem28</value>
  </data>
  <data name="&gt;&gt;layoutControlItem28.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem29.Name" xml:space="preserve">
    <value>layoutControlItem29</value>
  </data>
  <data name="&gt;&gt;layoutControlItem29.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem30.Name" xml:space="preserve">
    <value>layoutControlItem30</value>
  </data>
  <data name="&gt;&gt;layoutControlItem30.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem31.Name" xml:space="preserve">
    <value>layoutControlItem31</value>
  </data>
  <data name="&gt;&gt;layoutControlItem31.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem32.Name" xml:space="preserve">
    <value>layoutControlItem32</value>
  </data>
  <data name="&gt;&gt;layoutControlItem32.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem33.Name" xml:space="preserve">
    <value>layoutControlItem33</value>
  </data>
  <data name="&gt;&gt;layoutControlItem33.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem34.Name" xml:space="preserve">
    <value>layoutControlItem34</value>
  </data>
  <data name="&gt;&gt;layoutControlItem34.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem35.Name" xml:space="preserve">
    <value>layoutControlItem35</value>
  </data>
  <data name="&gt;&gt;layoutControlItem35.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem36.Name" xml:space="preserve">
    <value>layoutControlItem36</value>
  </data>
  <data name="&gt;&gt;layoutControlItem36.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem37.Name" xml:space="preserve">
    <value>layoutControlItem37</value>
  </data>
  <data name="&gt;&gt;layoutControlItem37.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem38.Name" xml:space="preserve">
    <value>layoutControlItem38</value>
  </data>
  <data name="&gt;&gt;layoutControlItem38.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem39.Name" xml:space="preserve">
    <value>layoutControlItem39</value>
  </data>
  <data name="&gt;&gt;layoutControlItem39.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem40.Name" xml:space="preserve">
    <value>layoutControlItem40</value>
  </data>
  <data name="&gt;&gt;layoutControlItem40.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem41.Name" xml:space="preserve">
    <value>layoutControlItem41</value>
  </data>
  <data name="&gt;&gt;layoutControlItem41.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem42.Name" xml:space="preserve">
    <value>layoutControlItem42</value>
  </data>
  <data name="&gt;&gt;layoutControlItem42.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem43.Name" xml:space="preserve">
    <value>layoutControlItem43</value>
  </data>
  <data name="&gt;&gt;layoutControlItem43.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem44.Name" xml:space="preserve">
    <value>layoutControlItem44</value>
  </data>
  <data name="&gt;&gt;layoutControlItem44.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem45.Name" xml:space="preserve">
    <value>layoutControlItem45</value>
  </data>
  <data name="&gt;&gt;layoutControlItem45.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem46.Name" xml:space="preserve">
    <value>layoutControlItem46</value>
  </data>
  <data name="&gt;&gt;layoutControlItem46.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem47.Name" xml:space="preserve">
    <value>layoutControlItem47</value>
  </data>
  <data name="&gt;&gt;layoutControlItem47.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem48.Name" xml:space="preserve">
    <value>layoutControlItem48</value>
  </data>
  <data name="&gt;&gt;layoutControlItem48.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem49.Name" xml:space="preserve">
    <value>layoutControlItem49</value>
  </data>
  <data name="&gt;&gt;layoutControlItem49.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem50.Name" xml:space="preserve">
    <value>layoutControlItem50</value>
  </data>
  <data name="&gt;&gt;layoutControlItem50.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem51.Name" xml:space="preserve">
    <value>layoutControlItem51</value>
  </data>
  <data name="&gt;&gt;layoutControlItem51.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem52.Name" xml:space="preserve">
    <value>layoutControlItem52</value>
  </data>
  <data name="&gt;&gt;layoutControlItem52.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem53.Name" xml:space="preserve">
    <value>layoutControlItem53</value>
  </data>
  <data name="&gt;&gt;layoutControlItem53.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem54.Name" xml:space="preserve">
    <value>layoutControlItem54</value>
  </data>
  <data name="&gt;&gt;layoutControlItem54.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem55.Name" xml:space="preserve">
    <value>layoutControlItem55</value>
  </data>
  <data name="&gt;&gt;layoutControlItem55.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem56.Name" xml:space="preserve">
    <value>layoutControlItem56</value>
  </data>
  <data name="&gt;&gt;layoutControlItem56.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem57.Name" xml:space="preserve">
    <value>layoutControlItem57</value>
  </data>
  <data name="&gt;&gt;layoutControlItem57.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem58.Name" xml:space="preserve">
    <value>layoutControlItem58</value>
  </data>
  <data name="&gt;&gt;layoutControlItem58.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem59.Name" xml:space="preserve">
    <value>layoutControlItem59</value>
  </data>
  <data name="&gt;&gt;layoutControlItem59.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem60.Name" xml:space="preserve">
    <value>layoutControlItem60</value>
  </data>
  <data name="&gt;&gt;layoutControlItem60.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem61.Name" xml:space="preserve">
    <value>layoutControlItem61</value>
  </data>
  <data name="&gt;&gt;layoutControlItem61.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem62.Name" xml:space="preserve">
    <value>layoutControlItem62</value>
  </data>
  <data name="&gt;&gt;layoutControlItem62.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem63.Name" xml:space="preserve">
    <value>layoutControlItem63</value>
  </data>
  <data name="&gt;&gt;layoutControlItem63.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem64.Name" xml:space="preserve">
    <value>layoutControlItem64</value>
  </data>
  <data name="&gt;&gt;layoutControlItem64.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem65.Name" xml:space="preserve">
    <value>layoutControlItem65</value>
  </data>
  <data name="&gt;&gt;layoutControlItem65.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem66.Name" xml:space="preserve">
    <value>layoutControlItem66</value>
  </data>
  <data name="&gt;&gt;layoutControlItem66.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem67.Name" xml:space="preserve">
    <value>layoutControlItem67</value>
  </data>
  <data name="&gt;&gt;layoutControlItem67.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem68.Name" xml:space="preserve">
    <value>layoutControlItem68</value>
  </data>
  <data name="&gt;&gt;layoutControlItem68.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem69.Name" xml:space="preserve">
    <value>layoutControlItem69</value>
  </data>
  <data name="&gt;&gt;layoutControlItem69.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem70.Name" xml:space="preserve">
    <value>layoutControlItem70</value>
  </data>
  <data name="&gt;&gt;layoutControlItem70.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem71.Name" xml:space="preserve">
    <value>layoutControlItem71</value>
  </data>
  <data name="&gt;&gt;layoutControlItem71.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem72.Name" xml:space="preserve">
    <value>layoutControlItem72</value>
  </data>
  <data name="&gt;&gt;layoutControlItem72.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem73.Name" xml:space="preserve">
    <value>layoutControlItem73</value>
  </data>
  <data name="&gt;&gt;layoutControlItem73.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem74.Name" xml:space="preserve">
    <value>layoutControlItem74</value>
  </data>
  <data name="&gt;&gt;layoutControlItem74.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem75.Name" xml:space="preserve">
    <value>layoutControlItem75</value>
  </data>
  <data name="&gt;&gt;layoutControlItem75.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem76.Name" xml:space="preserve">
    <value>layoutControlItem76</value>
  </data>
  <data name="&gt;&gt;layoutControlItem76.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem77.Name" xml:space="preserve">
    <value>layoutControlItem77</value>
  </data>
  <data name="&gt;&gt;layoutControlItem77.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem78.Name" xml:space="preserve">
    <value>layoutControlItem78</value>
  </data>
  <data name="&gt;&gt;layoutControlItem78.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem81.Name" xml:space="preserve">
    <value>layoutControlItem81</value>
  </data>
  <data name="&gt;&gt;layoutControlItem81.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem82.Name" xml:space="preserve">
    <value>layoutControlItem82</value>
  </data>
  <data name="&gt;&gt;layoutControlItem82.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem83.Name" xml:space="preserve">
    <value>layoutControlItem83</value>
  </data>
  <data name="&gt;&gt;layoutControlItem83.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem84.Name" xml:space="preserve">
    <value>layoutControlItem84</value>
  </data>
  <data name="&gt;&gt;layoutControlItem84.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem85.Name" xml:space="preserve">
    <value>layoutControlItem85</value>
  </data>
  <data name="&gt;&gt;layoutControlItem85.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem86.Name" xml:space="preserve">
    <value>layoutControlItem86</value>
  </data>
  <data name="&gt;&gt;layoutControlItem86.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem87.Name" xml:space="preserve">
    <value>layoutControlItem87</value>
  </data>
  <data name="&gt;&gt;layoutControlItem87.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem88.Name" xml:space="preserve">
    <value>layoutControlItem88</value>
  </data>
  <data name="&gt;&gt;layoutControlItem88.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem89.Name" xml:space="preserve">
    <value>layoutControlItem89</value>
  </data>
  <data name="&gt;&gt;layoutControlItem89.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem90.Name" xml:space="preserve">
    <value>layoutControlItem90</value>
  </data>
  <data name="&gt;&gt;layoutControlItem90.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem91.Name" xml:space="preserve">
    <value>layoutControlItem91</value>
  </data>
  <data name="&gt;&gt;layoutControlItem91.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem92.Name" xml:space="preserve">
    <value>layoutControlItem92</value>
  </data>
  <data name="&gt;&gt;layoutControlItem92.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem93.Name" xml:space="preserve">
    <value>layoutControlItem93</value>
  </data>
  <data name="&gt;&gt;layoutControlItem93.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;emptySpaceItem5.Name" xml:space="preserve">
    <value>emptySpaceItem5</value>
  </data>
  <data name="&gt;&gt;emptySpaceItem5.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.EmptySpaceItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem94.Name" xml:space="preserve">
    <value>layoutControlItem94</value>
  </data>
  <data name="&gt;&gt;layoutControlItem94.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;simpleLabelItem4.Name" xml:space="preserve">
    <value>simpleLabelItem4</value>
  </data>
  <data name="&gt;&gt;simpleLabelItem4.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.SimpleLabelItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;emptySpaceItem7.Name" xml:space="preserve">
    <value>emptySpaceItem7</value>
  </data>
  <data name="&gt;&gt;emptySpaceItem7.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.EmptySpaceItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem95.Name" xml:space="preserve">
    <value>layoutControlItem95</value>
  </data>
  <data name="&gt;&gt;layoutControlItem95.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem96.Name" xml:space="preserve">
    <value>layoutControlItem96</value>
  </data>
  <data name="&gt;&gt;layoutControlItem96.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControlItem97.Name" xml:space="preserve">
    <value>layoutControlItem97</value>
  </data>
  <data name="&gt;&gt;layoutControlItem97.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControlItem, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;navBarGroup1.Name" xml:space="preserve">
    <value>navBarGroup1</value>
  </data>
  <data name="&gt;&gt;navBarGroup1.Type" xml:space="preserve">
    <value>DevExpress.XtraNavBar.NavBarGroup, DevExpress.XtraNavBar.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;navBarItem1.Name" xml:space="preserve">
    <value>navBarItem1</value>
  </data>
  <data name="&gt;&gt;navBarItem1.Type" xml:space="preserve">
    <value>DevExpress.XtraNavBar.NavBarItem, DevExpress.XtraNavBar.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;navBarItem2.Name" xml:space="preserve">
    <value>navBarItem2</value>
  </data>
  <data name="&gt;&gt;navBarItem2.Type" xml:space="preserve">
    <value>DevExpress.XtraNavBar.NavBarItem, DevExpress.XtraNavBar.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;navBarItem3.Name" xml:space="preserve">
    <value>navBarItem3</value>
  </data>
  <data name="&gt;&gt;navBarItem3.Type" xml:space="preserve">
    <value>DevExpress.XtraNavBar.NavBarItem, DevExpress.XtraNavBar.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;navBarItem4.Name" xml:space="preserve">
    <value>navBarItem4</value>
  </data>
  <data name="&gt;&gt;navBarItem4.Type" xml:space="preserve">
    <value>DevExpress.XtraNavBar.NavBarItem, DevExpress.XtraNavBar.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;navBarItem5.Name" xml:space="preserve">
    <value>navBarItem5</value>
  </data>
  <data name="&gt;&gt;navBarItem5.Type" xml:space="preserve">
    <value>DevExpress.XtraNavBar.NavBarItem, DevExpress.XtraNavBar.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;navBarItem6.Name" xml:space="preserve">
    <value>navBarItem6</value>
  </data>
  <data name="&gt;&gt;navBarItem6.Type" xml:space="preserve">
    <value>DevExpress.XtraNavBar.NavBarItem, DevExpress.XtraNavBar.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>frm_ReportViewer</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.XtraForm, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repositoryItemTextEdit1.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="bar2.Text" xml:space="preserve">
    <value>Custom 3</value>
  </data>
  <data name="emptySpaceItem1.CustomizationFormText" xml:space="preserve">
    <value>emptySpaceItem1</value>
  </data>
  <data name="emptySpaceItem3.CustomizationFormText" xml:space="preserve">
    <value>emptySpaceItem3</value>
  </data>
  <data name="emptySpaceItem4.CustomizationFormText" xml:space="preserve">
    <value>emptySpaceItem4</value>
  </data>
  <data name="simpleLabelItem1.CustomizationFormText" xml:space="preserve">
    <value>LabelsimpleLabelItem1</value>
  </data>
  <data name="simpleLabelItem1.Text" xml:space="preserve">
    <value>LabelsimpleLabelItem1</value>
  </data>
  <data name="simpleLabelItem2.CustomizationFormText" xml:space="preserve">
    <value>LabelsimpleLabelItem2</value>
  </data>
  <data name="simpleLabelItem2.Text" xml:space="preserve">
    <value>LabelsimpleLabelItem2</value>
  </data>
  <data name="simpleLabelItem3.CustomizationFormText" xml:space="preserve">
    <value>LabelsimpleLabelItem3</value>
  </data>
  <data name="simpleLabelItem3.Text" xml:space="preserve">
    <value>LabelsimpleLabelItem3</value>
  </data>
  <data name="emptySpaceItem6.CustomizationFormText" xml:space="preserve">
    <value>emptySpaceItem6</value>
  </data>
  <data name="groupControl4.AppearanceCaption.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 10pt, style=Bold</value>
  </data>
  <data name="memoEdit1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="memoEdit1.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="memoEdit1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="memoEdit1.Size" type="System.Drawing.Size, System.Drawing">
    <value>9, 4</value>
  </data>
  <data name="memoEdit1.TabIndex" type="System.Int32, mscorlib">
    <value>200</value>
  </data>
  <data name="&gt;&gt;memoEdit1.Name" xml:space="preserve">
    <value>memoEdit1</value>
  </data>
  <data name="&gt;&gt;memoEdit1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.MemoEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;memoEdit1.Parent" xml:space="preserve">
    <value>groupControl4</value>
  </data>
  <data name="&gt;&gt;memoEdit1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="groupControl4.Location" type="System.Drawing.Point, System.Drawing">
    <value>38, 660</value>
  </data>
  <data name="groupControl4.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="groupControl4.Size" type="System.Drawing.Size, System.Drawing">
    <value>13, 29</value>
  </data>
  <data name="groupControl4.TabIndex" type="System.Int32, mscorlib">
    <value>82</value>
  </data>
  <data name="groupControl4.Text" xml:space="preserve">
    <value>Desc</value>
  </data>
  <data name="groupControl4.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;groupControl4.Name" xml:space="preserve">
    <value>groupControl4</value>
  </data>
  <data name="&gt;&gt;groupControl4.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GroupControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="groupControl5.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="groupControl5.AppearanceCaption.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 10pt, style=Bold</value>
  </data>
  <data name="layoutControl2.Appearance.Control.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Gray</value>
  </data>
  <data name="labelControl5.Location" type="System.Drawing.Point, System.Drawing">
    <value>104, 8</value>
  </data>
  <data name="labelControl5.Size" type="System.Drawing.Size, System.Drawing">
    <value>14, 13</value>
  </data>
  <data name="labelControl5.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="labelControl5.Text" xml:space="preserve">
    <value>M3</value>
  </data>
  <data name="&gt;&gt;labelControl5.Name" xml:space="preserve">
    <value>labelControl5</value>
  </data>
  <data name="&gt;&gt;labelControl5.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl5.Parent" xml:space="preserve">
    <value>panelControl4</value>
  </data>
  <data name="&gt;&gt;labelControl5.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="gridLookUpEdit1.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="gridLookUpEdit1.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 5</value>
  </data>
  <data name="gridLookUpEdit1.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridLookUpEdit1.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn55.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="gridColumn55.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn55.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn56.Caption" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="gridColumn56.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn56.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn57.Caption" xml:space="preserve">
    <value>MatrixId</value>
  </data>
  <data name="gridLookUpEdit1.Size" type="System.Drawing.Size, System.Drawing">
    <value>91, 20</value>
  </data>
  <data name="gridLookUpEdit1.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit1.Name" xml:space="preserve">
    <value>gridLookUpEdit1</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit1.Parent" xml:space="preserve">
    <value>panelControl4</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="labelControl6.Location" type="System.Drawing.Point, System.Drawing">
    <value>101, 31</value>
  </data>
  <data name="labelControl6.Size" type="System.Drawing.Size, System.Drawing">
    <value>33, 13</value>
  </data>
  <data name="labelControl6.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="labelControl6.Text" xml:space="preserve">
    <value>M Item</value>
  </data>
  <data name="&gt;&gt;labelControl6.Name" xml:space="preserve">
    <value>labelControl6</value>
  </data>
  <data name="&gt;&gt;labelControl6.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl6.Parent" xml:space="preserve">
    <value>panelControl4</value>
  </data>
  <data name="&gt;&gt;labelControl6.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="gridLookUpEdit2.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="gridLookUpEdit2.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 28</value>
  </data>
  <data name="gridLookUpEdit2.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridLookUpEdit2.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn58.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="gridColumn58.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn58.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn59.Caption" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="gridColumn59.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn59.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn60.Caption" xml:space="preserve">
    <value>MatrixDetailId</value>
  </data>
  <data name="gridLookUpEdit2.Size" type="System.Drawing.Size, System.Drawing">
    <value>91, 20</value>
  </data>
  <data name="gridLookUpEdit2.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit2.Name" xml:space="preserve">
    <value>gridLookUpEdit2</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit2.Parent" xml:space="preserve">
    <value>panelControl4</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit2.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="panelControl4.Location" type="System.Drawing.Point, System.Drawing">
    <value>-10, 75</value>
  </data>
  <data name="panelControl4.Size" type="System.Drawing.Size, System.Drawing">
    <value>194, 49</value>
  </data>
  <data name="panelControl4.TabIndex" type="System.Int32, mscorlib">
    <value>89</value>
  </data>
  <data name="&gt;&gt;panelControl4.Name" xml:space="preserve">
    <value>panelControl4</value>
  </data>
  <data name="&gt;&gt;panelControl4.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.PanelControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;panelControl4.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;panelControl4.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="labelControl7.Location" type="System.Drawing.Point, System.Drawing">
    <value>104, 8</value>
  </data>
  <data name="labelControl7.Size" type="System.Drawing.Size, System.Drawing">
    <value>14, 13</value>
  </data>
  <data name="labelControl7.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="labelControl7.Text" xml:space="preserve">
    <value>M2</value>
  </data>
  <data name="&gt;&gt;labelControl7.Name" xml:space="preserve">
    <value>labelControl7</value>
  </data>
  <data name="&gt;&gt;labelControl7.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl7.Parent" xml:space="preserve">
    <value>panelControl5</value>
  </data>
  <data name="&gt;&gt;labelControl7.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="gridLookUpEdit3.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="gridLookUpEdit3.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 5</value>
  </data>
  <data name="gridLookUpEdit3.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridLookUpEdit3.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn61.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="gridColumn61.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn61.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn62.Caption" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="gridColumn62.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn62.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn63.Caption" xml:space="preserve">
    <value>MatrixId</value>
  </data>
  <data name="gridLookUpEdit3.Size" type="System.Drawing.Size, System.Drawing">
    <value>91, 20</value>
  </data>
  <data name="gridLookUpEdit3.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit3.Name" xml:space="preserve">
    <value>gridLookUpEdit3</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit3.Parent" xml:space="preserve">
    <value>panelControl5</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit3.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="labelControl8.Location" type="System.Drawing.Point, System.Drawing">
    <value>101, 31</value>
  </data>
  <data name="labelControl8.Size" type="System.Drawing.Size, System.Drawing">
    <value>33, 13</value>
  </data>
  <data name="labelControl8.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="labelControl8.Text" xml:space="preserve">
    <value>M Item</value>
  </data>
  <data name="&gt;&gt;labelControl8.Name" xml:space="preserve">
    <value>labelControl8</value>
  </data>
  <data name="&gt;&gt;labelControl8.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl8.Parent" xml:space="preserve">
    <value>panelControl5</value>
  </data>
  <data name="&gt;&gt;labelControl8.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="gridLookUpEdit4.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="gridLookUpEdit4.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 28</value>
  </data>
  <data name="gridLookUpEdit4.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridLookUpEdit4.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn64.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="gridColumn64.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn64.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn65.Caption" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="gridColumn65.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn65.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn66.Caption" xml:space="preserve">
    <value>MatrixDetailId</value>
  </data>
  <data name="gridLookUpEdit4.Size" type="System.Drawing.Size, System.Drawing">
    <value>91, 20</value>
  </data>
  <data name="gridLookUpEdit4.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit4.Name" xml:space="preserve">
    <value>gridLookUpEdit4</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit4.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit4.Parent" xml:space="preserve">
    <value>panelControl5</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit4.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="panelControl5.Location" type="System.Drawing.Point, System.Drawing">
    <value>188, 75</value>
  </data>
  <data name="panelControl5.Size" type="System.Drawing.Size, System.Drawing">
    <value>169, 49</value>
  </data>
  <data name="panelControl5.TabIndex" type="System.Int32, mscorlib">
    <value>88</value>
  </data>
  <data name="&gt;&gt;panelControl5.Name" xml:space="preserve">
    <value>panelControl5</value>
  </data>
  <data name="&gt;&gt;panelControl5.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.PanelControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;panelControl5.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;panelControl5.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="labelControl9.Location" type="System.Drawing.Point, System.Drawing">
    <value>104, 8</value>
  </data>
  <data name="labelControl9.Size" type="System.Drawing.Size, System.Drawing">
    <value>14, 13</value>
  </data>
  <data name="labelControl9.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="labelControl9.Text" xml:space="preserve">
    <value>M1</value>
  </data>
  <data name="&gt;&gt;labelControl9.Name" xml:space="preserve">
    <value>labelControl9</value>
  </data>
  <data name="&gt;&gt;labelControl9.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl9.Parent" xml:space="preserve">
    <value>panelControl6</value>
  </data>
  <data name="&gt;&gt;labelControl9.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="gridLookUpEdit5.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="gridLookUpEdit5.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 5</value>
  </data>
  <data name="gridLookUpEdit5.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridLookUpEdit5.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn67.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="gridColumn67.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn67.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn68.Caption" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="gridColumn68.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn68.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn69.Caption" xml:space="preserve">
    <value>MatrixId</value>
  </data>
  <data name="gridLookUpEdit5.Size" type="System.Drawing.Size, System.Drawing">
    <value>91, 20</value>
  </data>
  <data name="gridLookUpEdit5.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit5.Name" xml:space="preserve">
    <value>gridLookUpEdit5</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit5.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit5.Parent" xml:space="preserve">
    <value>panelControl6</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit5.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="labelControl10.Location" type="System.Drawing.Point, System.Drawing">
    <value>101, 31</value>
  </data>
  <data name="labelControl10.Size" type="System.Drawing.Size, System.Drawing">
    <value>33, 13</value>
  </data>
  <data name="labelControl10.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="labelControl10.Text" xml:space="preserve">
    <value>M Item</value>
  </data>
  <data name="&gt;&gt;labelControl10.Name" xml:space="preserve">
    <value>labelControl10</value>
  </data>
  <data name="&gt;&gt;labelControl10.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl10.Parent" xml:space="preserve">
    <value>panelControl6</value>
  </data>
  <data name="&gt;&gt;labelControl10.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="gridLookUpEdit6.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="gridLookUpEdit6.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 28</value>
  </data>
  <data name="gridLookUpEdit6.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridLookUpEdit6.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn70.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="gridColumn70.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn70.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn71.Caption" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="gridColumn71.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn71.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn72.Caption" xml:space="preserve">
    <value>MatrixDetailId</value>
  </data>
  <data name="gridLookUpEdit6.Size" type="System.Drawing.Size, System.Drawing">
    <value>91, 20</value>
  </data>
  <data name="gridLookUpEdit6.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit6.Name" xml:space="preserve">
    <value>gridLookUpEdit6</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit6.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit6.Parent" xml:space="preserve">
    <value>panelControl6</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit6.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="panelControl6.Location" type="System.Drawing.Point, System.Drawing">
    <value>361, 75</value>
  </data>
  <data name="panelControl6.Size" type="System.Drawing.Size, System.Drawing">
    <value>141, 49</value>
  </data>
  <data name="panelControl6.TabIndex" type="System.Int32, mscorlib">
    <value>87</value>
  </data>
  <data name="&gt;&gt;panelControl6.Name" xml:space="preserve">
    <value>panelControl6</value>
  </data>
  <data name="&gt;&gt;panelControl6.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.PanelControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;panelControl6.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;panelControl6.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="gridLookUpEdit7.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="gridLookUpEdit7.Location" type="System.Drawing.Point, System.Drawing">
    <value>192, 290</value>
  </data>
  <data name="gridLookUpEdit7.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridLookUpEdit7.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn73.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="gridColumn73.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn73.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn74.Caption" xml:space="preserve">
    <value>Group</value>
  </data>
  <data name="gridColumn74.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn74.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn75.Caption" xml:space="preserve">
    <value>CustomerGroupId</value>
  </data>
  <data name="gridLookUpEdit7.Size" type="System.Drawing.Size, System.Drawing">
    <value>307, 20</value>
  </data>
  <data name="gridLookUpEdit7.TabIndex" type="System.Int32, mscorlib">
    <value>88</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit7.Name" xml:space="preserve">
    <value>gridLookUpEdit7</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit7.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit7.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit7.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="textEdit1.Location" type="System.Drawing.Point, System.Drawing">
    <value>365, 590</value>
  </data>
  <data name="textEdit1.Size" type="System.Drawing.Size, System.Drawing">
    <value>134, 20</value>
  </data>
  <data name="textEdit1.TabIndex" type="System.Int32, mscorlib">
    <value>44</value>
  </data>
  <data name="&gt;&gt;textEdit1.Name" xml:space="preserve">
    <value>textEdit1</value>
  </data>
  <data name="&gt;&gt;textEdit1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;textEdit1.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;textEdit1.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="lookUpEdit2.Location" type="System.Drawing.Point, System.Drawing">
    <value>193, 521</value>
  </data>
  <data name="lookUpEdit2.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lookUpEdit2.Properties.Columns" xml:space="preserve">
    <value>GroupNameAr</value>
  </data>
  <data name="lookUpEdit2.Properties.Columns1" xml:space="preserve">
    <value>Empolyee Group</value>
  </data>
  <data name="lookUpEdit2.Properties.Columns2" xml:space="preserve">
    <value>GroupId</value>
  </data>
  <data name="lookUpEdit2.Properties.Columns3" xml:space="preserve">
    <value>GroupId</value>
  </data>
  <data name="lookUpEdit2.Properties.Columns4" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lookUpEdit2.Properties.Columns5" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lookUpEdit2.Properties.Columns6" xml:space="preserve">
    <value />
  </data>
  <data name="lookUpEdit2.Properties.Columns7" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lookUpEdit2.Properties.Columns8" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lookUpEdit2.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lookUpEdit2.Size" type="System.Drawing.Size, System.Drawing">
    <value>162, 20</value>
  </data>
  <data name="lookUpEdit2.TabIndex" type="System.Int32, mscorlib">
    <value>40</value>
  </data>
  <data name="&gt;&gt;lookUpEdit2.Name" xml:space="preserve">
    <value>lookUpEdit2</value>
  </data>
  <data name="&gt;&gt;lookUpEdit2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lookUpEdit2.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;lookUpEdit2.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="imageComboBoxEdit2.EditValue" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="imageComboBoxEdit2.Location" type="System.Drawing.Point, System.Drawing">
    <value>365, 521</value>
  </data>
  <data name="imageComboBoxEdit2.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="imageComboBoxEdit2.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="imageComboBoxEdit2.Properties.Items1" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="imageComboBoxEdit2.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit2.Properties.Items3" xml:space="preserve">
    <value>Equals</value>
  </data>
  <data name="imageComboBoxEdit2.Properties.Items4" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="imageComboBoxEdit2.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit2.Size" type="System.Drawing.Size, System.Drawing">
    <value>134, 20</value>
  </data>
  <data name="imageComboBoxEdit2.TabIndex" type="System.Int32, mscorlib">
    <value>39</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit2.Name" xml:space="preserve">
    <value>imageComboBoxEdit2</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit2.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit2.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="checkedComboBoxEdit1.Location" type="System.Drawing.Point, System.Drawing">
    <value>192, 244</value>
  </data>
  <data name="checkedComboBoxEdit1.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="checkedComboBoxEdit1.Size" type="System.Drawing.Size, System.Drawing">
    <value>162, 20</value>
  </data>
  <data name="checkedComboBoxEdit1.TabIndex" type="System.Int32, mscorlib">
    <value>88</value>
  </data>
  <data name="&gt;&gt;checkedComboBoxEdit1.Name" xml:space="preserve">
    <value>checkedComboBoxEdit1</value>
  </data>
  <data name="&gt;&gt;checkedComboBoxEdit1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.CheckedComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;checkedComboBoxEdit1.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;checkedComboBoxEdit1.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="imageComboBoxEdit3.EditValue" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="imageComboBoxEdit3.Location" type="System.Drawing.Point, System.Drawing">
    <value>364, 244</value>
  </data>
  <data name="imageComboBoxEdit3.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="imageComboBoxEdit3.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="imageComboBoxEdit3.Properties.Items1" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="imageComboBoxEdit3.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit3.Properties.Items3" xml:space="preserve">
    <value>Equals</value>
  </data>
  <data name="imageComboBoxEdit3.Properties.Items4" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="imageComboBoxEdit3.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit3.Size" type="System.Drawing.Size, System.Drawing">
    <value>135, 20</value>
  </data>
  <data name="imageComboBoxEdit3.TabIndex" type="System.Int32, mscorlib">
    <value>87</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit3.Name" xml:space="preserve">
    <value>imageComboBoxEdit3</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit3.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit3.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="spinEdit1.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="spinEdit1.Location" type="System.Drawing.Point, System.Drawing">
    <value>-7, 613</value>
  </data>
  <data name="spinEdit1.Properties.Mask.EditMask" xml:space="preserve">
    <value>n2</value>
  </data>
  <data name="spinEdit1.Size" type="System.Drawing.Size, System.Drawing">
    <value>190, 20</value>
  </data>
  <data name="spinEdit1.TabIndex" type="System.Int32, mscorlib">
    <value>47</value>
  </data>
  <data name="&gt;&gt;spinEdit1.Name" xml:space="preserve">
    <value>spinEdit1</value>
  </data>
  <data name="&gt;&gt;spinEdit1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;spinEdit1.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;spinEdit1.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="spinEdit2.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="spinEdit2.Location" type="System.Drawing.Point, System.Drawing">
    <value>193, 613</value>
  </data>
  <data name="spinEdit2.Properties.Mask.EditMask" xml:space="preserve">
    <value>n2</value>
  </data>
  <data name="spinEdit2.Size" type="System.Drawing.Size, System.Drawing">
    <value>162, 20</value>
  </data>
  <data name="spinEdit2.TabIndex" type="System.Int32, mscorlib">
    <value>46</value>
  </data>
  <data name="&gt;&gt;spinEdit2.Name" xml:space="preserve">
    <value>spinEdit2</value>
  </data>
  <data name="&gt;&gt;spinEdit2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;spinEdit2.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;spinEdit2.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="imageComboBoxEdit4.EditValue" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="imageComboBoxEdit4.Location" type="System.Drawing.Point, System.Drawing">
    <value>365, 613</value>
  </data>
  <data name="imageComboBoxEdit4.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="imageComboBoxEdit4.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="imageComboBoxEdit4.Properties.Items1" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="imageComboBoxEdit4.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit4.Properties.Items3" xml:space="preserve">
    <value>Equals</value>
  </data>
  <data name="imageComboBoxEdit4.Properties.Items4" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="imageComboBoxEdit4.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit4.Properties.Items6" xml:space="preserve">
    <value>Between</value>
  </data>
  <data name="imageComboBoxEdit4.Properties.Items7" type="System.Byte, mscorlib">
    <value>2</value>
  </data>
  <data name="imageComboBoxEdit4.Properties.Items8" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit4.Size" type="System.Drawing.Size, System.Drawing">
    <value>134, 20</value>
  </data>
  <data name="imageComboBoxEdit4.TabIndex" type="System.Int32, mscorlib">
    <value>45</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit4.Name" xml:space="preserve">
    <value>imageComboBoxEdit4</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit4.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit4.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit4.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="gridLookUpEdit8.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="gridLookUpEdit8.Location" type="System.Drawing.Point, System.Drawing">
    <value>-10, 498</value>
  </data>
  <data name="gridLookUpEdit8.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridLookUpEdit8.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn76.Caption" xml:space="preserve">
    <value>Department</value>
  </data>
  <data name="gridColumn76.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn76.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn77.Caption" xml:space="preserve">
    <value>DeptId</value>
  </data>
  <data name="gridLookUpEdit8.Size" type="System.Drawing.Size, System.Drawing">
    <value>143, 20</value>
  </data>
  <data name="gridLookUpEdit8.TabIndex" type="System.Int32, mscorlib">
    <value>38</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit8.Name" xml:space="preserve">
    <value>gridLookUpEdit8</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit8.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit8.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit8.ZOrder" xml:space="preserve">
    <value>16</value>
  </data>
  <data name="gridLookUpEdit9.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="gridLookUpEdit9.Location" type="System.Drawing.Point, System.Drawing">
    <value>189, 498</value>
  </data>
  <data name="gridLookUpEdit9.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridLookUpEdit9.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn78.Caption" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="gridColumn78.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn78.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn79.Caption" xml:space="preserve">
    <value>StatusId</value>
  </data>
  <data name="gridLookUpEdit9.Size" type="System.Drawing.Size, System.Drawing">
    <value>90, 20</value>
  </data>
  <data name="gridLookUpEdit9.TabIndex" type="System.Int32, mscorlib">
    <value>37</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit9.Name" xml:space="preserve">
    <value>gridLookUpEdit9</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit9.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit9.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit9.ZOrder" xml:space="preserve">
    <value>17</value>
  </data>
  <data name="gridLookUpEdit10.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="gridLookUpEdit10.Location" type="System.Drawing.Point, System.Drawing">
    <value>335, 498</value>
  </data>
  <data name="gridLookUpEdit10.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridLookUpEdit10.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn80.Caption" xml:space="preserve">
    <value>Priority</value>
  </data>
  <data name="gridColumn80.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn80.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn81.Caption" xml:space="preserve">
    <value>PriorityId</value>
  </data>
  <data name="gridLookUpEdit10.Size" type="System.Drawing.Size, System.Drawing">
    <value>115, 20</value>
  </data>
  <data name="gridLookUpEdit10.TabIndex" type="System.Int32, mscorlib">
    <value>36</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit10.Name" xml:space="preserve">
    <value>gridLookUpEdit10</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit10.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit10.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit10.ZOrder" xml:space="preserve">
    <value>18</value>
  </data>
  <data name="spinEdit3.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="spinEdit3.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="spinEdit3.Location" type="System.Drawing.Point, System.Drawing">
    <value>149, 128</value>
  </data>
  <data name="spinEdit3.Properties.Mask.EditMask" xml:space="preserve">
    <value>n2</value>
  </data>
  <data name="spinEdit3.Size" type="System.Drawing.Size, System.Drawing">
    <value>106, 20</value>
  </data>
  <data name="spinEdit3.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="&gt;&gt;spinEdit3.Name" xml:space="preserve">
    <value>spinEdit3</value>
  </data>
  <data name="&gt;&gt;spinEdit3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;spinEdit3.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;spinEdit3.ZOrder" xml:space="preserve">
    <value>19</value>
  </data>
  <data name="imageComboBoxEdit5.EditValue" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit5.Location" type="System.Drawing.Point, System.Drawing">
    <value>364, 152</value>
  </data>
  <data name="imageComboBoxEdit5.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="imageComboBoxEdit5.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="imageComboBoxEdit5.Properties.Items1" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit5.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit5.Properties.Items3" xml:space="preserve">
    <value>Inventory</value>
  </data>
  <data name="imageComboBoxEdit5.Properties.Items4" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="imageComboBoxEdit5.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit5.Properties.Items6" xml:space="preserve">
    <value>Service</value>
  </data>
  <data name="imageComboBoxEdit5.Properties.Items7" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="imageComboBoxEdit5.Properties.Items8" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit5.Properties.Items9" xml:space="preserve">
    <value>Product</value>
  </data>
  <data name="imageComboBoxEdit5.Properties.Items10" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="imageComboBoxEdit5.Properties.Items11" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit5.Size" type="System.Drawing.Size, System.Drawing">
    <value>135, 20</value>
  </data>
  <data name="imageComboBoxEdit5.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit5.Name" xml:space="preserve">
    <value>imageComboBoxEdit5</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit5.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit5.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit5.ZOrder" xml:space="preserve">
    <value>20</value>
  </data>
  <data name="gridLookUpEdit11.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="gridLookUpEdit11.Location" type="System.Drawing.Point, System.Drawing">
    <value>192, 429</value>
  </data>
  <data name="gridLookUpEdit11.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridLookUpEdit11.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn82.Caption" xml:space="preserve">
    <value>ParentActId</value>
  </data>
  <data name="gridColumn83.Caption" xml:space="preserve">
    <value>CostCenter</value>
  </data>
  <data name="gridColumn84.Caption" xml:space="preserve">
    <value>Account Number</value>
  </data>
  <data name="gridColumn84.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn84.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn84.Width" type="System.Int32, mscorlib">
    <value>252</value>
  </data>
  <data name="gridColumn85.Caption" xml:space="preserve">
    <value>Account Name</value>
  </data>
  <data name="gridColumn85.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn85.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn85.Width" type="System.Int32, mscorlib">
    <value>810</value>
  </data>
  <data name="gridColumn86.Caption" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="gridLookUpEdit11.Size" type="System.Drawing.Size, System.Drawing">
    <value>307, 20</value>
  </data>
  <data name="gridLookUpEdit11.TabIndex" type="System.Int32, mscorlib">
    <value>32</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit11.Name" xml:space="preserve">
    <value>gridLookUpEdit11</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit11.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit11.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit11.ZOrder" xml:space="preserve">
    <value>21</value>
  </data>
  <data name="spinEdit4.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="spinEdit4.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="spinEdit4.Location" type="System.Drawing.Point, System.Drawing">
    <value>-7, 128</value>
  </data>
  <data name="spinEdit4.Properties.Mask.EditMask" xml:space="preserve">
    <value>n2</value>
  </data>
  <data name="spinEdit4.Size" type="System.Drawing.Size, System.Drawing">
    <value>89, 20</value>
  </data>
  <data name="spinEdit4.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="&gt;&gt;spinEdit4.Name" xml:space="preserve">
    <value>spinEdit4</value>
  </data>
  <data name="&gt;&gt;spinEdit4.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;spinEdit4.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;spinEdit4.ZOrder" xml:space="preserve">
    <value>22</value>
  </data>
  <data name="gridLookUpEdit12.EditValue" xml:space="preserve">
    <value>`</value>
  </data>
  <data name="gridLookUpEdit12.Location" type="System.Drawing.Point, System.Drawing">
    <value>191, 52</value>
  </data>
  <data name="gridLookUpEdit12.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridLookUpEdit12.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn87.Caption" xml:space="preserve">
    <value>Item Name</value>
  </data>
  <data name="gridColumn87.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn87.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn88.Caption" xml:space="preserve">
    <value>ItemId</value>
  </data>
  <data name="gridLookUpEdit12.Size" type="System.Drawing.Size, System.Drawing">
    <value>308, 20</value>
  </data>
  <data name="gridLookUpEdit12.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit12.Name" xml:space="preserve">
    <value>gridLookUpEdit12</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit12.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit12.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit12.ZOrder" xml:space="preserve">
    <value>23</value>
  </data>
  <data name="gridLookUpEdit13.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="gridLookUpEdit13.Location" type="System.Drawing.Point, System.Drawing">
    <value>192, 267</value>
  </data>
  <data name="gridLookUpEdit13.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridLookUpEdit13.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn89.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="gridColumn89.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn89.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn90.Caption" xml:space="preserve">
    <value>Group</value>
  </data>
  <data name="gridColumn90.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn90.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn91.Caption" xml:space="preserve">
    <value>CustomerGroupId</value>
  </data>
  <data name="gridLookUpEdit13.Size" type="System.Drawing.Size, System.Drawing">
    <value>307, 20</value>
  </data>
  <data name="gridLookUpEdit13.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit13.Name" xml:space="preserve">
    <value>gridLookUpEdit13</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit13.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit13.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit13.ZOrder" xml:space="preserve">
    <value>24</value>
  </data>
  <data name="textEdit2.Location" type="System.Drawing.Point, System.Drawing">
    <value>365, 567</value>
  </data>
  <data name="textEdit2.Size" type="System.Drawing.Size, System.Drawing">
    <value>134, 20</value>
  </data>
  <data name="textEdit2.TabIndex" type="System.Int32, mscorlib">
    <value>43</value>
  </data>
  <data name="&gt;&gt;textEdit2.Name" xml:space="preserve">
    <value>textEdit2</value>
  </data>
  <data name="&gt;&gt;textEdit2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;textEdit2.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;textEdit2.ZOrder" xml:space="preserve">
    <value>25</value>
  </data>
  <data name="imageComboBoxEdit6.EditValue" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="imageComboBoxEdit6.Location" type="System.Drawing.Point, System.Drawing">
    <value>365, 544</value>
  </data>
  <data name="imageComboBoxEdit6.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="imageComboBoxEdit6.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="imageComboBoxEdit6.Properties.Items1" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="imageComboBoxEdit6.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit6.Properties.Items3" xml:space="preserve">
    <value>Equals</value>
  </data>
  <data name="imageComboBoxEdit6.Properties.Items4" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="imageComboBoxEdit6.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit6.Size" type="System.Drawing.Size, System.Drawing">
    <value>134, 20</value>
  </data>
  <data name="imageComboBoxEdit6.TabIndex" type="System.Int32, mscorlib">
    <value>41</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit6.Name" xml:space="preserve">
    <value>imageComboBoxEdit6</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit6.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit6.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit6.ZOrder" xml:space="preserve">
    <value>26</value>
  </data>
  <data name="lookUpEdit3.Location" type="System.Drawing.Point, System.Drawing">
    <value>193, 544</value>
  </data>
  <data name="lookUpEdit3.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lookUpEdit3.Properties.Columns" xml:space="preserve">
    <value>EmpName</value>
  </data>
  <data name="lookUpEdit3.Properties.Columns1" xml:space="preserve">
    <value>Empolyee</value>
  </data>
  <data name="lookUpEdit3.Properties.Columns2" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="lookUpEdit3.Properties.Columns3" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="lookUpEdit3.Properties.Columns4" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lookUpEdit3.Properties.Columns5" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lookUpEdit3.Properties.Columns6" xml:space="preserve">
    <value />
  </data>
  <data name="lookUpEdit3.Properties.Columns7" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lookUpEdit3.Properties.Columns8" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lookUpEdit3.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lookUpEdit3.Size" type="System.Drawing.Size, System.Drawing">
    <value>162, 20</value>
  </data>
  <data name="lookUpEdit3.TabIndex" type="System.Int32, mscorlib">
    <value>42</value>
  </data>
  <data name="&gt;&gt;lookUpEdit3.Name" xml:space="preserve">
    <value>lookUpEdit3</value>
  </data>
  <data name="&gt;&gt;lookUpEdit3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lookUpEdit3.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;lookUpEdit3.ZOrder" xml:space="preserve">
    <value>27</value>
  </data>
  <data name="gridLookUpEdit14.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="gridLookUpEdit14.Location" type="System.Drawing.Point, System.Drawing">
    <value>192, 475</value>
  </data>
  <data name="gridLookUpEdit14.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridLookUpEdit14.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn92.Caption" xml:space="preserve">
    <value>User Name</value>
  </data>
  <data name="gridColumn92.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn92.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn93.Caption" xml:space="preserve">
    <value>UserId</value>
  </data>
  <data name="gridLookUpEdit14.Size" type="System.Drawing.Size, System.Drawing">
    <value>163, 20</value>
  </data>
  <data name="gridLookUpEdit14.TabIndex" type="System.Int32, mscorlib">
    <value>35</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit14.Name" xml:space="preserve">
    <value>gridLookUpEdit14</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit14.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit14.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit14.ZOrder" xml:space="preserve">
    <value>28</value>
  </data>
  <data name="imageComboBoxEdit7.EditValue" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="imageComboBoxEdit7.Location" type="System.Drawing.Point, System.Drawing">
    <value>365, 475</value>
  </data>
  <data name="imageComboBoxEdit7.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="imageComboBoxEdit7.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="imageComboBoxEdit7.Properties.Items1" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="imageComboBoxEdit7.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit7.Properties.Items3" xml:space="preserve">
    <value>Equal</value>
  </data>
  <data name="imageComboBoxEdit7.Properties.Items4" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="imageComboBoxEdit7.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit7.Size" type="System.Drawing.Size, System.Drawing">
    <value>134, 20</value>
  </data>
  <data name="imageComboBoxEdit7.TabIndex" type="System.Int32, mscorlib">
    <value>34</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit7.Name" xml:space="preserve">
    <value>imageComboBoxEdit7</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit7.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit7.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit7.ZOrder" xml:space="preserve">
    <value>29</value>
  </data>
  <data name="lookUpEdit4.Location" type="System.Drawing.Point, System.Drawing">
    <value>192, 452</value>
  </data>
  <data name="lookUpEdit4.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lookUpEdit4.Properties.Columns" xml:space="preserve">
    <value>CustomAccListId</value>
  </data>
  <data name="lookUpEdit4.Properties.Columns1" xml:space="preserve">
    <value>CustomAccListId</value>
  </data>
  <data name="lookUpEdit4.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lookUpEdit4.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lookUpEdit4.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lookUpEdit4.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lookUpEdit4.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lookUpEdit4.Properties.Columns7" xml:space="preserve">
    <value>CustomAccListName</value>
  </data>
  <data name="lookUpEdit4.Properties.Columns8" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="lookUpEdit4.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lookUpEdit4.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lookUpEdit4.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="lookUpEdit4.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lookUpEdit4.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lookUpEdit4.Properties.Columns14" xml:space="preserve">
    <value>CustomAccListCode</value>
  </data>
  <data name="lookUpEdit4.Properties.Columns15" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="lookUpEdit4.Properties.Columns16" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lookUpEdit4.Properties.Columns17" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lookUpEdit4.Properties.Columns18" xml:space="preserve">
    <value />
  </data>
  <data name="lookUpEdit4.Properties.Columns19" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lookUpEdit4.Properties.Columns20" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lookUpEdit4.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lookUpEdit4.Size" type="System.Drawing.Size, System.Drawing">
    <value>307, 20</value>
  </data>
  <data name="lookUpEdit4.TabIndex" type="System.Int32, mscorlib">
    <value>33</value>
  </data>
  <data name="&gt;&gt;lookUpEdit4.Name" xml:space="preserve">
    <value>lookUpEdit4</value>
  </data>
  <data name="&gt;&gt;lookUpEdit4.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lookUpEdit4.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;lookUpEdit4.ZOrder" xml:space="preserve">
    <value>30</value>
  </data>
  <data name="spinEdit5.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="spinEdit5.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="spinEdit5.Location" type="System.Drawing.Point, System.Drawing">
    <value>322, 128</value>
  </data>
  <data name="spinEdit5.Properties.Mask.EditMask" xml:space="preserve">
    <value>n2</value>
  </data>
  <data name="spinEdit5.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 20</value>
  </data>
  <data name="spinEdit5.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="&gt;&gt;spinEdit5.Name" xml:space="preserve">
    <value>spinEdit5</value>
  </data>
  <data name="&gt;&gt;spinEdit5.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;spinEdit5.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;spinEdit5.ZOrder" xml:space="preserve">
    <value>31</value>
  </data>
  <data name="imageComboBoxEdit8.EditValue" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="imageComboBoxEdit8.Location" type="System.Drawing.Point, System.Drawing">
    <value>192, 383</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items1" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items3" xml:space="preserve">
    <value>Purchase Invoice</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items4" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items6" xml:space="preserve">
    <value>Sales Invoice</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items7" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items8" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items9" xml:space="preserve">
    <value>Purchase Return</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items10" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items11" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items12" xml:space="preserve">
    <value>Sales Return</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items13" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items14" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items15" xml:space="preserve">
    <value>Add Adjustment</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items16" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items17" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items18" xml:space="preserve">
    <value>Subtract Adjustment</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items19" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items20" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items21" xml:space="preserve">
    <value>Damage</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items22" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items23" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items24" xml:space="preserve">
    <value>Transfer From</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items25" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items26" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items27" xml:space="preserve">
    <value>Transfer to</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items28" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items29" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items30" xml:space="preserve">
    <value>Open Balance</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items31" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items32" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items33" xml:space="preserve">
    <value>Manufacturing</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items34" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items35" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items36" xml:space="preserve">
    <value>Receive Bill</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items37" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items38" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items39" xml:space="preserve">
    <value>Outgoing Bill</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items40" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items41" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items42" xml:space="preserve">
    <value>Sales Order</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items43" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items44" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items45" xml:space="preserve">
    <value>Quality Control</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items46" type="System.Int32, mscorlib">
    <value>27</value>
  </data>
  <data name="imageComboBoxEdit8.Properties.Items47" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit8.Size" type="System.Drawing.Size, System.Drawing">
    <value>307, 20</value>
  </data>
  <data name="imageComboBoxEdit8.TabIndex" type="System.Int32, mscorlib">
    <value>30</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit8.Name" xml:space="preserve">
    <value>imageComboBoxEdit8</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit8.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit8.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit8.ZOrder" xml:space="preserve">
    <value>32</value>
  </data>
  <data name="lookUpEdit5.Location" type="System.Drawing.Point, System.Drawing">
    <value>192, 406</value>
  </data>
  <data name="lookUpEdit5.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lookUpEdit5.Properties.Columns" xml:space="preserve">
    <value>CostCenterId</value>
  </data>
  <data name="lookUpEdit5.Properties.Columns1" xml:space="preserve">
    <value>CostCenterId</value>
  </data>
  <data name="lookUpEdit5.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lookUpEdit5.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lookUpEdit5.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lookUpEdit5.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lookUpEdit5.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lookUpEdit5.Properties.Columns7" xml:space="preserve">
    <value>CostCenterName</value>
  </data>
  <data name="lookUpEdit5.Properties.Columns8" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="lookUpEdit5.Properties.Columns9" xml:space="preserve">
    <value>CostCenterCode</value>
  </data>
  <data name="lookUpEdit5.Properties.Columns10" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="lookUpEdit5.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lookUpEdit5.Size" type="System.Drawing.Size, System.Drawing">
    <value>307, 20</value>
  </data>
  <data name="lookUpEdit5.TabIndex" type="System.Int32, mscorlib">
    <value>31</value>
  </data>
  <data name="&gt;&gt;lookUpEdit5.Name" xml:space="preserve">
    <value>lookUpEdit5</value>
  </data>
  <data name="&gt;&gt;lookUpEdit5.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lookUpEdit5.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;lookUpEdit5.ZOrder" xml:space="preserve">
    <value>33</value>
  </data>
  <data name="gridLookUpEdit15.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="gridLookUpEdit15.Location" type="System.Drawing.Point, System.Drawing">
    <value>-7, 337</value>
  </data>
  <data name="gridLookUpEdit15.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridLookUpEdit15.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn94.Caption" xml:space="preserve">
    <value>Customer Code</value>
  </data>
  <data name="gridColumn94.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn94.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn95.Caption" xml:space="preserve">
    <value>Customer Name</value>
  </data>
  <data name="gridColumn95.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn95.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn96.Caption" xml:space="preserve">
    <value>Customer F Name</value>
  </data>
  <data name="gridColumn96.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn96.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridLookUpEdit15.Size" type="System.Drawing.Size, System.Drawing">
    <value>189, 20</value>
  </data>
  <data name="gridLookUpEdit15.TabIndex" type="System.Int32, mscorlib">
    <value>27</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit15.Name" xml:space="preserve">
    <value>gridLookUpEdit15</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit15.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit15.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit15.ZOrder" xml:space="preserve">
    <value>34</value>
  </data>
  <data name="imageComboBoxEdit9.EditValue" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="imageComboBoxEdit9.Location" type="System.Drawing.Point, System.Drawing">
    <value>365, 337</value>
  </data>
  <data name="imageComboBoxEdit9.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="imageComboBoxEdit9.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="imageComboBoxEdit9.Properties.Items1" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="imageComboBoxEdit9.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit9.Properties.Items3" xml:space="preserve">
    <value>Equal</value>
  </data>
  <data name="imageComboBoxEdit9.Properties.Items4" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="imageComboBoxEdit9.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit9.Properties.Items6" xml:space="preserve">
    <value>Between</value>
  </data>
  <data name="imageComboBoxEdit9.Properties.Items7" type="System.Byte, mscorlib">
    <value>2</value>
  </data>
  <data name="imageComboBoxEdit9.Properties.Items8" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit9.Properties.Items9" xml:space="preserve">
    <value>كل من</value>
  </data>
  <data name="imageComboBoxEdit9.Properties.Items10" type="System.Byte, mscorlib">
    <value>3</value>
  </data>
  <data name="imageComboBoxEdit9.Properties.Items11" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit9.Size" type="System.Drawing.Size, System.Drawing">
    <value>134, 20</value>
  </data>
  <data name="imageComboBoxEdit9.TabIndex" type="System.Int32, mscorlib">
    <value>25</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit9.Name" xml:space="preserve">
    <value>imageComboBoxEdit9</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit9.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit9.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit9.ZOrder" xml:space="preserve">
    <value>35</value>
  </data>
  <data name="gridLookUpEdit16.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="gridLookUpEdit16.Location" type="System.Drawing.Point, System.Drawing">
    <value>192, 337</value>
  </data>
  <data name="gridLookUpEdit16.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridLookUpEdit16.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn97.Caption" xml:space="preserve">
    <value>Customer Code</value>
  </data>
  <data name="gridColumn97.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn97.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn98.Caption" xml:space="preserve">
    <value>Customer Name</value>
  </data>
  <data name="gridColumn98.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn98.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn99.Caption" xml:space="preserve">
    <value>Customer F Name</value>
  </data>
  <data name="gridColumn99.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn99.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridLookUpEdit16.Size" type="System.Drawing.Size, System.Drawing">
    <value>163, 20</value>
  </data>
  <data name="gridLookUpEdit16.TabIndex" type="System.Int32, mscorlib">
    <value>26</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit16.Name" xml:space="preserve">
    <value>gridLookUpEdit16</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit16.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit16.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit16.ZOrder" xml:space="preserve">
    <value>36</value>
  </data>
  <data name="imageComboBoxEdit10.EditValue" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="imageComboBoxEdit10.Location" type="System.Drawing.Point, System.Drawing">
    <value>365, 360</value>
  </data>
  <data name="imageComboBoxEdit10.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="imageComboBoxEdit10.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="imageComboBoxEdit10.Properties.Items1" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="imageComboBoxEdit10.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit10.Properties.Items3" xml:space="preserve">
    <value>Equal</value>
  </data>
  <data name="imageComboBoxEdit10.Properties.Items4" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="imageComboBoxEdit10.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit10.Size" type="System.Drawing.Size, System.Drawing">
    <value>134, 20</value>
  </data>
  <data name="imageComboBoxEdit10.TabIndex" type="System.Int32, mscorlib">
    <value>28</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit10.Name" xml:space="preserve">
    <value>imageComboBoxEdit10</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit10.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit10.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit10.ZOrder" xml:space="preserve">
    <value>37</value>
  </data>
  <data name="imageComboBoxEdit11.EditValue" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="imageComboBoxEdit11.Location" type="System.Drawing.Point, System.Drawing">
    <value>365, 314</value>
  </data>
  <data name="imageComboBoxEdit11.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="imageComboBoxEdit11.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="imageComboBoxEdit11.Properties.Items1" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="imageComboBoxEdit11.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit11.Properties.Items3" xml:space="preserve">
    <value>Equal</value>
  </data>
  <data name="imageComboBoxEdit11.Properties.Items4" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="imageComboBoxEdit11.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit11.Properties.Items6" xml:space="preserve">
    <value>Between</value>
  </data>
  <data name="imageComboBoxEdit11.Properties.Items7" type="System.Byte, mscorlib">
    <value>2</value>
  </data>
  <data name="imageComboBoxEdit11.Properties.Items8" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit11.Properties.Items9" xml:space="preserve">
    <value>كل من</value>
  </data>
  <data name="imageComboBoxEdit11.Properties.Items10" type="System.Byte, mscorlib">
    <value>3</value>
  </data>
  <data name="imageComboBoxEdit11.Properties.Items11" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit11.Size" type="System.Drawing.Size, System.Drawing">
    <value>134, 20</value>
  </data>
  <data name="imageComboBoxEdit11.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit11.Name" xml:space="preserve">
    <value>imageComboBoxEdit11</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit11.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit11.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit11.ZOrder" xml:space="preserve">
    <value>38</value>
  </data>
  <data name="imageComboBoxEdit12.EditValue" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="imageComboBoxEdit12.Location" type="System.Drawing.Point, System.Drawing">
    <value>364, 221</value>
  </data>
  <data name="imageComboBoxEdit12.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="imageComboBoxEdit12.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="imageComboBoxEdit12.Properties.Items1" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="imageComboBoxEdit12.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit12.Properties.Items3" xml:space="preserve">
    <value>Equal</value>
  </data>
  <data name="imageComboBoxEdit12.Properties.Items4" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="imageComboBoxEdit12.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit12.Properties.Items6" xml:space="preserve">
    <value>Between</value>
  </data>
  <data name="imageComboBoxEdit12.Properties.Items7" type="System.Byte, mscorlib">
    <value>2</value>
  </data>
  <data name="imageComboBoxEdit12.Properties.Items8" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit12.Size" type="System.Drawing.Size, System.Drawing">
    <value>135, 20</value>
  </data>
  <data name="imageComboBoxEdit12.TabIndex" type="System.Int32, mscorlib">
    <value>18</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit12.Name" xml:space="preserve">
    <value>imageComboBoxEdit12</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit12.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit12.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit12.ZOrder" xml:space="preserve">
    <value>39</value>
  </data>
  <data name="dateEdit1.EditValue" type="System.DateTime, mscorlib">
    <value />
  </data>
  <data name="dateEdit1.Location" type="System.Drawing.Point, System.Drawing">
    <value>364, 198</value>
  </data>
  <data name="dateEdit1.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="dateEdit1.Size" type="System.Drawing.Size, System.Drawing">
    <value>135, 20</value>
  </data>
  <data name="dateEdit1.TabIndex" type="System.Int32, mscorlib">
    <value>17</value>
  </data>
  <data name="&gt;&gt;dateEdit1.Name" xml:space="preserve">
    <value>dateEdit1</value>
  </data>
  <data name="&gt;&gt;dateEdit1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.DateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;dateEdit1.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;dateEdit1.ZOrder" xml:space="preserve">
    <value>40</value>
  </data>
  <data name="gridLookUpEdit17.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="gridLookUpEdit17.Location" type="System.Drawing.Point, System.Drawing">
    <value>-7, 314</value>
  </data>
  <data name="gridLookUpEdit17.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridLookUpEdit17.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn101.Caption" xml:space="preserve">
    <value>Vendor Code</value>
  </data>
  <data name="gridColumn101.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn101.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn102.Caption" xml:space="preserve">
    <value>Vendor Name</value>
  </data>
  <data name="gridColumn102.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn102.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn103.Caption" xml:space="preserve">
    <value>Vendor F Name</value>
  </data>
  <data name="gridColumn103.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn103.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridLookUpEdit17.Size" type="System.Drawing.Size, System.Drawing">
    <value>189, 20</value>
  </data>
  <data name="gridLookUpEdit17.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit17.Name" xml:space="preserve">
    <value>gridLookUpEdit17</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit17.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit17.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit17.ZOrder" xml:space="preserve">
    <value>41</value>
  </data>
  <data name="gridLookUpEdit18.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="gridLookUpEdit18.Location" type="System.Drawing.Point, System.Drawing">
    <value>-7, 221</value>
  </data>
  <data name="gridLookUpEdit18.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridLookUpEdit18.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn104.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="gridColumn104.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn104.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn105.Caption" xml:space="preserve">
    <value>Branch/ Store Name</value>
  </data>
  <data name="gridColumn105.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn105.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridLookUpEdit18.Size" type="System.Drawing.Size, System.Drawing">
    <value>189, 20</value>
  </data>
  <data name="gridLookUpEdit18.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit18.Name" xml:space="preserve">
    <value>gridLookUpEdit18</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit18.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit18.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit18.ZOrder" xml:space="preserve">
    <value>42</value>
  </data>
  <data name="imageComboBoxEdit13.EditValue" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="imageComboBoxEdit13.Location" type="System.Drawing.Point, System.Drawing">
    <value>364, 175</value>
  </data>
  <data name="imageComboBoxEdit13.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="imageComboBoxEdit13.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="imageComboBoxEdit13.Properties.Items1" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="imageComboBoxEdit13.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit13.Properties.Items3" xml:space="preserve">
    <value>Equal</value>
  </data>
  <data name="imageComboBoxEdit13.Properties.Items4" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="imageComboBoxEdit13.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit13.Properties.Items6" xml:space="preserve">
    <value>Between</value>
  </data>
  <data name="imageComboBoxEdit13.Properties.Items7" type="System.Byte, mscorlib">
    <value>2</value>
  </data>
  <data name="imageComboBoxEdit13.Properties.Items8" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit13.Size" type="System.Drawing.Size, System.Drawing">
    <value>135, 20</value>
  </data>
  <data name="imageComboBoxEdit13.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit13.Name" xml:space="preserve">
    <value>imageComboBoxEdit13</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit13.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit13.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit13.ZOrder" xml:space="preserve">
    <value>43</value>
  </data>
  <data name="gridLookUpEdit19.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="gridLookUpEdit19.Location" type="System.Drawing.Point, System.Drawing">
    <value>-7, 29</value>
  </data>
  <data name="gridLookUpEdit19.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridLookUpEdit19.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn107.Caption" xml:space="preserve">
    <value>Code1</value>
  </data>
  <data name="gridColumn107.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn107.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="gridColumn108.Caption" xml:space="preserve">
    <value>Code2</value>
  </data>
  <data name="gridColumn108.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn108.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn109.Caption" xml:space="preserve">
    <value>Item Name</value>
  </data>
  <data name="gridColumn109.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn109.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn110.Caption" xml:space="preserve">
    <value>Item F Name</value>
  </data>
  <data name="gridColumn110.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn110.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridLookUpEdit19.Size" type="System.Drawing.Size, System.Drawing">
    <value>188, 20</value>
  </data>
  <data name="gridLookUpEdit19.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit19.Name" xml:space="preserve">
    <value>gridLookUpEdit19</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit19.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit19.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit19.ZOrder" xml:space="preserve">
    <value>44</value>
  </data>
  <data name="imageComboBoxEdit14.EditValue" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="imageComboBoxEdit14.Location" type="System.Drawing.Point, System.Drawing">
    <value>364, 29</value>
  </data>
  <data name="imageComboBoxEdit14.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="imageComboBoxEdit14.Properties.Items" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="imageComboBoxEdit14.Properties.Items1" type="System.Byte, mscorlib">
    <value>0</value>
  </data>
  <data name="imageComboBoxEdit14.Properties.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit14.Properties.Items3" xml:space="preserve">
    <value>Equal</value>
  </data>
  <data name="imageComboBoxEdit14.Properties.Items4" type="System.Byte, mscorlib">
    <value>1</value>
  </data>
  <data name="imageComboBoxEdit14.Properties.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit14.Properties.Items6" xml:space="preserve">
    <value>Between</value>
  </data>
  <data name="imageComboBoxEdit14.Properties.Items7" type="System.Byte, mscorlib">
    <value>2</value>
  </data>
  <data name="imageComboBoxEdit14.Properties.Items8" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="imageComboBoxEdit14.Size" type="System.Drawing.Size, System.Drawing">
    <value>135, 20</value>
  </data>
  <data name="imageComboBoxEdit14.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit14.Name" xml:space="preserve">
    <value>imageComboBoxEdit14</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit14.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageComboBoxEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit14.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;imageComboBoxEdit14.ZOrder" xml:space="preserve">
    <value>45</value>
  </data>
  <data name="gridLookUpEdit20.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="gridLookUpEdit20.Location" type="System.Drawing.Point, System.Drawing">
    <value>191, 29</value>
  </data>
  <data name="gridLookUpEdit20.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridLookUpEdit20.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn112.Caption" xml:space="preserve">
    <value>Code1</value>
  </data>
  <data name="gridColumn112.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn112.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="gridColumn113.Caption" xml:space="preserve">
    <value>Code2</value>
  </data>
  <data name="gridColumn113.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn113.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn114.Caption" xml:space="preserve">
    <value>Item Name</value>
  </data>
  <data name="gridColumn114.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn114.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn115.Caption" xml:space="preserve">
    <value>Item F Name</value>
  </data>
  <data name="gridColumn115.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn115.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridLookUpEdit20.Size" type="System.Drawing.Size, System.Drawing">
    <value>163, 20</value>
  </data>
  <data name="gridLookUpEdit20.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit20.Name" xml:space="preserve">
    <value>gridLookUpEdit20</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit20.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit20.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit20.ZOrder" xml:space="preserve">
    <value>46</value>
  </data>
  <data name="gridLookUpEdit21.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="gridLookUpEdit21.Location" type="System.Drawing.Point, System.Drawing">
    <value>192, 314</value>
  </data>
  <data name="gridLookUpEdit21.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridLookUpEdit21.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn116.Caption" xml:space="preserve">
    <value>Vendor Code</value>
  </data>
  <data name="gridColumn116.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn116.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn117.Caption" xml:space="preserve">
    <value>Vendor Name</value>
  </data>
  <data name="gridColumn117.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn117.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn118.Caption" xml:space="preserve">
    <value>Vendor F Name</value>
  </data>
  <data name="gridColumn118.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn118.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridLookUpEdit21.Size" type="System.Drawing.Size, System.Drawing">
    <value>163, 20</value>
  </data>
  <data name="gridLookUpEdit21.TabIndex" type="System.Int32, mscorlib">
    <value>23</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit21.Name" xml:space="preserve">
    <value>gridLookUpEdit21</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit21.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit21.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit21.ZOrder" xml:space="preserve">
    <value>47</value>
  </data>
  <data name="dateEdit2.EditValue" type="System.DateTime, mscorlib">
    <value />
  </data>
  <data name="dateEdit2.Location" type="System.Drawing.Point, System.Drawing">
    <value>193, 175</value>
  </data>
  <data name="dateEdit2.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="dateEdit2.Size" type="System.Drawing.Size, System.Drawing">
    <value>161, 20</value>
  </data>
  <data name="dateEdit2.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="&gt;&gt;dateEdit2.Name" xml:space="preserve">
    <value>dateEdit2</value>
  </data>
  <data name="&gt;&gt;dateEdit2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.DateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;dateEdit2.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;dateEdit2.ZOrder" xml:space="preserve">
    <value>48</value>
  </data>
  <data name="gridLookUpEdit22.EditValue" xml:space="preserve">
    <value />
  </data>
  <data name="gridLookUpEdit22.Location" type="System.Drawing.Point, System.Drawing">
    <value>192, 221</value>
  </data>
  <data name="gridLookUpEdit22.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridLookUpEdit22.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn120.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="gridColumn120.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn120.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn121.Caption" xml:space="preserve">
    <value>Branch/ Store Name</value>
  </data>
  <data name="gridColumn121.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn121.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridLookUpEdit22.Size" type="System.Drawing.Size, System.Drawing">
    <value>162, 20</value>
  </data>
  <data name="gridLookUpEdit22.TabIndex" type="System.Int32, mscorlib">
    <value>19</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit22.Name" xml:space="preserve">
    <value>gridLookUpEdit22</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit22.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit22.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit22.ZOrder" xml:space="preserve">
    <value>49</value>
  </data>
  <data name="dateEdit3.EditValue" type="System.DateTime, mscorlib">
    <value />
  </data>
  <data name="dateEdit3.Location" type="System.Drawing.Point, System.Drawing">
    <value>-7, 175</value>
  </data>
  <data name="dateEdit3.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="dateEdit3.Size" type="System.Drawing.Size, System.Drawing">
    <value>190, 20</value>
  </data>
  <data name="dateEdit3.TabIndex" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="&gt;&gt;dateEdit3.Name" xml:space="preserve">
    <value>dateEdit3</value>
  </data>
  <data name="&gt;&gt;dateEdit3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.DateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;dateEdit3.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;dateEdit3.ZOrder" xml:space="preserve">
    <value>50</value>
  </data>
  <data name="lookUpEdit6.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lookUpEdit6.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lookUpEdit6.Location" type="System.Drawing.Point, System.Drawing">
    <value>192, 360</value>
  </data>
  <data name="lookUpEdit6.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lookUpEdit6.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lookUpEdit6.Size" type="System.Drawing.Size, System.Drawing">
    <value>163, 20</value>
  </data>
  <data name="lookUpEdit6.TabIndex" type="System.Int32, mscorlib">
    <value>29</value>
  </data>
  <data name="&gt;&gt;lookUpEdit6.Name" xml:space="preserve">
    <value>lookUpEdit6</value>
  </data>
  <data name="&gt;&gt;lookUpEdit6.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lookUpEdit6.Parent" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;lookUpEdit6.ZOrder" xml:space="preserve">
    <value>51</value>
  </data>
  <data name="layoutControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 3</value>
  </data>
  <data name="layoutControl2.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>0, 0, 0, 0</value>
  </data>
  <data name="layoutControlGroup3.CustomizationFormText" xml:space="preserve">
    <value>Root</value>
  </data>
  <data name="layoutControlItem1.CustomizationFormText" xml:space="preserve">
    <value>Company</value>
  </data>
  <data name="layoutControlItem1.Text" xml:space="preserve">
    <value>Item Group</value>
  </data>
  <data name="layoutControlItem2.CustomizationFormText" xml:space="preserve">
    <value>LabelsimpleLabelItem1</value>
  </data>
  <data name="layoutControlItem2.Text" xml:space="preserve">
    <value>Item</value>
  </data>
  <data name="layoutControlItem3.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem2</value>
  </data>
  <data name="layoutControlItem4.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem1</value>
  </data>
  <data name="layoutControlItem5.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem3</value>
  </data>
  <data name="layoutControlItem6.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem5</value>
  </data>
  <data name="layoutControlItem7.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem6</value>
  </data>
  <data name="layoutControlItem8.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem7</value>
  </data>
  <data name="layoutControlItem9.CustomizationFormText" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="layoutControlItem9.Text" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="layoutControlItem10.CustomizationFormText" xml:space="preserve">
    <value>store3</value>
  </data>
  <data name="layoutControlItem11.CustomizationFormText" xml:space="preserve">
    <value>store4</value>
  </data>
  <data name="layoutControlItem12.CustomizationFormText" xml:space="preserve">
    <value>store2</value>
  </data>
  <data name="layoutControlItem13.CustomizationFormText" xml:space="preserve">
    <value>Store</value>
  </data>
  <data name="layoutControlItem13.Text" xml:space="preserve">
    <value>Branch/Store</value>
  </data>
  <data name="layoutControlItem14.CustomizationFormText" xml:space="preserve">
    <value>vendor4</value>
  </data>
  <data name="layoutControlItem15.CustomizationFormText" xml:space="preserve">
    <value>vendor3</value>
  </data>
  <data name="layoutControlItem16.CustomizationFormText" xml:space="preserve">
    <value>vendor2</value>
  </data>
  <data name="layoutControlItem17.CustomizationFormText" xml:space="preserve">
    <value>Vendor</value>
  </data>
  <data name="layoutControlItem17.Text" xml:space="preserve">
    <value>Vendor</value>
  </data>
  <data name="layoutControlItem18.CustomizationFormText" xml:space="preserve">
    <value>comp4</value>
  </data>
  <data name="layoutControlItem19.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem1</value>
  </data>
  <data name="layoutControlItem20.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem2</value>
  </data>
  <data name="layoutControlItem21.CustomizationFormText" xml:space="preserve">
    <value>emptySpaceItem2</value>
  </data>
  <data name="layoutControlItem22.CustomizationFormText" xml:space="preserve">
    <value>Date Less Than</value>
  </data>
  <data name="layoutControlItem22.Text" xml:space="preserve">
    <value>Date Less Than</value>
  </data>
  <data name="layoutControlItem23.CustomizationFormText" xml:space="preserve">
    <value>expDate2</value>
  </data>
  <data name="layoutControlItem24.CustomizationFormText" xml:space="preserve">
    <value>lblFltrName</value>
  </data>
  <data name="layoutControlItem25.CustomizationFormText" xml:space="preserve">
    <value>To</value>
  </data>
  <data name="layoutControlItem25.Text" xml:space="preserve">
    <value>To</value>
  </data>
  <data name="layoutControlItem26.CustomizationFormText" xml:space="preserve">
    <value>From</value>
  </data>
  <data name="layoutControlItem26.Text" xml:space="preserve">
    <value>From</value>
  </data>
  <data name="layoutControlItem27.CustomizationFormText" xml:space="preserve">
    <value>Filter Type</value>
  </data>
  <data name="layoutControlItem27.Text" xml:space="preserve">
    <value>Filter Type</value>
  </data>
  <data name="layoutControlItem28.CustomizationFormText" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="layoutControlItem28.Text" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="layoutControlItem29.CustomizationFormText" xml:space="preserve">
    <value>Customer2</value>
  </data>
  <data name="layoutControlItem30.CustomizationFormText" xml:space="preserve">
    <value>Customer3</value>
  </data>
  <data name="layoutControlItem31.CustomizationFormText" xml:space="preserve">
    <value>Customer4</value>
  </data>
  <data name="layoutControlItem32.CustomizationFormText" xml:space="preserve">
    <value>process3</value>
  </data>
  <data name="layoutControlItem33.CustomizationFormText" xml:space="preserve">
    <value>Process Type</value>
  </data>
  <data name="layoutControlItem33.Text" xml:space="preserve">
    <value>Process Type</value>
  </data>
  <data name="layoutControlItem34.CustomizationFormText" xml:space="preserve">
    <value>process2</value>
  </data>
  <data name="layoutControlItem35.CustomizationFormText" xml:space="preserve">
    <value>cost3</value>
  </data>
  <data name="layoutControlItem36.CustomizationFormText" xml:space="preserve">
    <value>cost2</value>
  </data>
  <data name="layoutControlItem37.CustomizationFormText" xml:space="preserve">
    <value>acc3</value>
  </data>
  <data name="layoutControlItem38.CustomizationFormText" xml:space="preserve">
    <value>Cost Center</value>
  </data>
  <data name="layoutControlItem38.Text" xml:space="preserve">
    <value>Cost Center</value>
  </data>
  <data name="layoutControlItem39.CustomizationFormText" xml:space="preserve">
    <value>Account</value>
  </data>
  <data name="layoutControlItem39.Text" xml:space="preserve">
    <value>Account</value>
  </data>
  <data name="layoutControlItem40.CustomizationFormText" xml:space="preserve">
    <value>cstmLst3</value>
  </data>
  <data name="layoutControlItem41.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem1</value>
  </data>
  <data name="layoutControlItem42.CustomizationFormText" xml:space="preserve">
    <value>Custom Accounts List</value>
  </data>
  <data name="layoutControlItem42.Text" xml:space="preserve">
    <value>Custom Accounts List</value>
  </data>
  <data name="layoutControlItem43.CustomizationFormText" xml:space="preserve">
    <value>user4</value>
  </data>
  <data name="layoutControlItem44.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem1</value>
  </data>
  <data name="layoutControlItem45.CustomizationFormText" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="layoutControlItem45.Text" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="layoutControlItem46.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem2</value>
  </data>
  <data name="layoutControlItem47.CustomizationFormText" xml:space="preserve">
    <value>emptySpaceItem5</value>
  </data>
  <data name="layoutControlItem48.CustomizationFormText" xml:space="preserve">
    <value>Batch4</value>
  </data>
  <data name="layoutControlItem49.CustomizationFormText" xml:space="preserve">
    <value>salesEmp3</value>
  </data>
  <data name="layoutControlItem50.CustomizationFormText" xml:space="preserve">
    <value>salesEmp4</value>
  </data>
  <data name="layoutControlItem51.CustomizationFormText" xml:space="preserve">
    <value>Sales Employee</value>
  </data>
  <data name="layoutControlItem51.Text" xml:space="preserve">
    <value>Sales Employee</value>
  </data>
  <data name="layoutControlItem52.CustomizationFormText" xml:space="preserve">
    <value>salesEmp2</value>
  </data>
  <data name="layoutControlItem53.CustomizationFormText" xml:space="preserve">
    <value>Batch</value>
  </data>
  <data name="layoutControlItem53.Text" xml:space="preserve">
    <value>Batch</value>
  </data>
  <data name="layoutControlItem54.CustomizationFormText" xml:space="preserve">
    <value>Batch2</value>
  </data>
  <data name="layoutControlItem55.CustomizationFormText" xml:space="preserve">
    <value>Customer Group</value>
  </data>
  <data name="layoutControlItem55.Text" xml:space="preserve">
    <value>Customer Group</value>
  </data>
  <data name="layoutControlItem56.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem1</value>
  </data>
  <data name="layoutControlItem57.CustomizationFormText" xml:space="preserve">
    <value>custGroup3</value>
  </data>
  <data name="layoutControlItem58.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem1</value>
  </data>
  <data name="layoutControlItem59.CustomizationFormText" xml:space="preserve">
    <value>ParentItem2</value>
  </data>
  <data name="layoutControlItem60.CustomizationFormText" xml:space="preserve">
    <value>ParentItem3</value>
  </data>
  <data name="layoutControlItem61.AppearanceItemCaption.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="layoutControlItem61.CustomizationFormText" xml:space="preserve">
    <value>Length</value>
  </data>
  <data name="layoutControlItem61.Text" xml:space="preserve">
    <value>Length</value>
  </data>
  <data name="layoutControlItem62.AppearanceItemCaption.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="layoutControlItem62.CustomizationFormText" xml:space="preserve">
    <value>Height</value>
  </data>
  <data name="layoutControlItem62.Text" xml:space="preserve">
    <value>Height</value>
  </data>
  <data name="layoutControlItem63.AppearanceItemCaption.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="layoutControlItem63.CustomizationFormText" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="layoutControlItem63.Text" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="layoutControlItem64.CustomizationFormText" xml:space="preserve">
    <value>Parent Item</value>
  </data>
  <data name="layoutControlItem64.Text" xml:space="preserve">
    <value>Parent Item</value>
  </data>
  <data name="layoutControlItem65.CustomizationFormText" xml:space="preserve">
    <value>Matrix</value>
  </data>
  <data name="layoutControlItem65.Text" xml:space="preserve">
    <value>Matrix</value>
  </data>
  <data name="layoutControlItem66.CustomizationFormText" xml:space="preserve">
    <value>Dimensions</value>
  </data>
  <data name="layoutControlItem66.Text" xml:space="preserve">
    <value>Dimensions</value>
  </data>
  <data name="layoutControlItem67.CustomizationFormText" xml:space="preserve">
    <value>Job Order</value>
  </data>
  <data name="layoutControlItem67.Text" xml:space="preserve">
    <value>Job Order</value>
  </data>
  <data name="layoutControlItem68.CustomizationFormText" xml:space="preserve">
    <value>Priority</value>
  </data>
  <data name="layoutControlItem68.Text" xml:space="preserve">
    <value>Priority</value>
  </data>
  <data name="layoutControlItem69.CustomizationFormText" xml:space="preserve">
    <value>State</value>
  </data>
  <data name="layoutControlItem69.Text" xml:space="preserve">
    <value>State</value>
  </data>
  <data name="layoutControlItem70.CustomizationFormText" xml:space="preserve">
    <value>Dept</value>
  </data>
  <data name="layoutControlItem70.Text" xml:space="preserve">
    <value>Dept</value>
  </data>
  <data name="layoutControlItem71.CustomizationFormText" xml:space="preserve">
    <value>itmTyp3</value>
  </data>
  <data name="layoutControlItem72.CustomizationFormText" xml:space="preserve">
    <value>Item Type</value>
  </data>
  <data name="layoutControlItem72.Text" xml:space="preserve">
    <value>Item Type</value>
  </data>
  <data name="layoutControlItem73.CustomizationFormText" xml:space="preserve">
    <value>itmTyp2</value>
  </data>
  <data name="layoutControlItem74.CustomizationFormText" xml:space="preserve">
    <value>sell</value>
  </data>
  <data name="layoutControlItem74.Text" xml:space="preserve">
    <value>sell Price</value>
  </data>
  <data name="layoutControlItem75.CustomizationFormText" xml:space="preserve">
    <value>sell2</value>
  </data>
  <data name="layoutControlItem76.CustomizationFormText" xml:space="preserve">
    <value>sell3</value>
  </data>
  <data name="layoutControlItem77.CustomizationFormText" xml:space="preserve">
    <value>sell4</value>
  </data>
  <data name="layoutControlItem78.CustomizationFormText" xml:space="preserve">
    <value>Item Category</value>
  </data>
  <data name="layoutControlItem78.Text" xml:space="preserve">
    <value>Item Category</value>
  </data>
  <data name="layoutControlItem81.CustomizationFormText" xml:space="preserve">
    <value>cat4</value>
  </data>
  <data name="layoutControlItem82.CustomizationFormText" xml:space="preserve">
    <value>InvoiceBook4</value>
  </data>
  <data name="layoutControlItem83.CustomizationFormText" xml:space="preserve">
    <value>Invoice Book</value>
  </data>
  <data name="layoutControlItem83.Text" xml:space="preserve">
    <value>Invoice Book</value>
  </data>
  <data name="layoutControlItem84.CustomizationFormText" xml:space="preserve">
    <value>InvoiceBook2</value>
  </data>
  <data name="layoutControlItem85.CustomizationFormText" xml:space="preserve">
    <value>layoutControlItem1</value>
  </data>
  <data name="layoutControlItem86.CustomizationFormText" xml:space="preserve">
    <value>EmpGroup4</value>
  </data>
  <data name="layoutControlItem87.CustomizationFormText" xml:space="preserve">
    <value>Employee Group</value>
  </data>
  <data name="layoutControlItem87.Text" xml:space="preserve">
    <value>Employee Group</value>
  </data>
  <data name="layoutControlItem88.CustomizationFormText" xml:space="preserve">
    <value>EmpGroup2</value>
  </data>
  <data name="layoutControlItem89.CustomizationFormText" xml:space="preserve">
    <value>EmpGroup3</value>
  </data>
  <data name="layoutControlItem90.CustomizationFormText" xml:space="preserve">
    <value>QC</value>
  </data>
  <data name="layoutControlItem90.Text" xml:space="preserve">
    <value>QC</value>
  </data>
  <data name="layoutControlItem91.CustomizationFormText" xml:space="preserve">
    <value>QC2</value>
  </data>
  <data name="layoutControlItem92.CustomizationFormText" xml:space="preserve">
    <value>QC3</value>
  </data>
  <data name="layoutControlItem93.CustomizationFormText" xml:space="preserve">
    <value>emptySpaceItem8</value>
  </data>
  <data name="emptySpaceItem5.CustomizationFormText" xml:space="preserve">
    <value>emptySpaceItem2</value>
  </data>
  <data name="layoutControlItem94.CustomizationFormText" xml:space="preserve">
    <value>VenGroup2</value>
  </data>
  <data name="simpleLabelItem4.CustomizationFormText" xml:space="preserve">
    <value>Vendor Group</value>
  </data>
  <data name="simpleLabelItem4.Text" xml:space="preserve">
    <value>Vendor Group</value>
  </data>
  <data name="emptySpaceItem7.CustomizationFormText" xml:space="preserve">
    <value>VenGroup3</value>
  </data>
  <data name="layoutControlItem95.CustomizationFormText" xml:space="preserve">
    <value>Mtrx1</value>
  </data>
  <data name="layoutControlItem96.CustomizationFormText" xml:space="preserve">
    <value>Mtrx2</value>
  </data>
  <data name="layoutControlItem97.CustomizationFormText" xml:space="preserve">
    <value>Mtrx3</value>
  </data>
  <data name="layoutControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>669, 657</value>
  </data>
  <data name="layoutControl2.TabIndex" type="System.Int32, mscorlib">
    <value>107</value>
  </data>
  <data name="layoutControl2.Text" xml:space="preserve">
    <value>layoutControl1</value>
  </data>
  <data name="&gt;&gt;layoutControl2.Name" xml:space="preserve">
    <value>layoutControl2</value>
  </data>
  <data name="&gt;&gt;layoutControl2.Type" xml:space="preserve">
    <value>DevExpress.XtraLayout.LayoutControl, DevExpress.XtraLayout.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;layoutControl2.Parent" xml:space="preserve">
    <value>xtraScrollableControl2</value>
  </data>
  <data name="&gt;&gt;layoutControl2.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="xtraScrollableControl2.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="xtraScrollableControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="xtraScrollableControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>702, 601</value>
  </data>
  <data name="xtraScrollableControl2.TabIndex" type="System.Int32, mscorlib">
    <value>108</value>
  </data>
  <data name="&gt;&gt;xtraScrollableControl2.Name" xml:space="preserve">
    <value>xtraScrollableControl2</value>
  </data>
  <data name="&gt;&gt;xtraScrollableControl2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.XtraScrollableControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;xtraScrollableControl2.Parent" xml:space="preserve">
    <value>groupControl5</value>
  </data>
  <data name="&gt;&gt;xtraScrollableControl2.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="groupControl5.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 30</value>
  </data>
  <data name="groupControl5.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="groupControl5.Size" type="System.Drawing.Size, System.Drawing">
    <value>706, 626</value>
  </data>
  <data name="groupControl5.TabIndex" type="System.Int32, mscorlib">
    <value>81</value>
  </data>
  <data name="groupControl5.Text" xml:space="preserve">
    <value>Filters</value>
  </data>
  <data name="&gt;&gt;groupControl5.Name" xml:space="preserve">
    <value>groupControl5</value>
  </data>
  <data name="&gt;&gt;groupControl5.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GroupControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="groupControl6.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Right</value>
  </data>
  <data name="groupControl6.AppearanceCaption.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 10pt, style=Bold</value>
  </data>
  <data name="imageListBoxControl1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="imageListBoxControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 23</value>
  </data>
  <data name="imageListBoxControl1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="imageListBoxControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>196, 603</value>
  </data>
  <data name="imageListBoxControl1.TabIndex" type="System.Int32, mscorlib">
    <value>79</value>
  </data>
  <data name="&gt;&gt;imageListBoxControl1.Name" xml:space="preserve">
    <value>imageListBoxControl1</value>
  </data>
  <data name="&gt;&gt;imageListBoxControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.ImageListBoxControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;imageListBoxControl1.Parent" xml:space="preserve">
    <value>groupControl6</value>
  </data>
  <data name="&gt;&gt;imageListBoxControl1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="groupControl6.Location" type="System.Drawing.Point, System.Drawing">
    <value>713, 30</value>
  </data>
  <data name="groupControl6.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="groupControl6.Size" type="System.Drawing.Size, System.Drawing">
    <value>200, 628</value>
  </data>
  <data name="groupControl6.TabIndex" type="System.Int32, mscorlib">
    <value>80</value>
  </data>
  <data name="groupControl6.Text" xml:space="preserve">
    <value>Reports</value>
  </data>
  <data name="&gt;&gt;groupControl6.Name" xml:space="preserve">
    <value>groupControl6</value>
  </data>
  <data name="&gt;&gt;groupControl6.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GroupControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="navBarGroup1.Caption" xml:space="preserve">
    <value>Departments</value>
  </data>
  <data name="navBarItem1.Caption" xml:space="preserve">
    <value>Stores</value>
  </data>
  <data name="navBarItem2.Caption" xml:space="preserve">
    <value>Sales</value>
  </data>
  <data name="navBarItem3.Caption" xml:space="preserve">
    <value>Purchases</value>
  </data>
  <data name="navBarItem4.Caption" xml:space="preserve">
    <value>HR</value>
  </data>
  <data name="navBarItem5.Caption" xml:space="preserve">
    <value>Accounting</value>
  </data>
  <data name="navBarItem6.Caption" xml:space="preserve">
    <value>Special Reports</value>
  </data>
  <data name="navBarControl2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Right</value>
  </data>
  <data name="navBarControl2.Appearance.ItemActive.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="navBarControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>919, 30</value>
  </data>
  <data name="resource.ExpandedWidth1" type="System.Int32, mscorlib">
    <value>113</value>
  </data>
  <data name="navBarControl2.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="navBarControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>113, 626</value>
  </data>
  <data name="navBarControl2.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="navBarControl2.Text" xml:space="preserve">
    <value>navBarControl1</value>
  </data>
  <data name="&gt;&gt;navBarControl2.Name" xml:space="preserve">
    <value>navBarControl2</value>
  </data>
  <data name="&gt;&gt;navBarControl2.Type" xml:space="preserve">
    <value>DevExpress.XtraNavBar.NavBarControl, DevExpress.XtraNavBar.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
</root>