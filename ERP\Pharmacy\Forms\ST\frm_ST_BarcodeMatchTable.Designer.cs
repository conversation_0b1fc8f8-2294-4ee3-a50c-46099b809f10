﻿namespace Pharmacy.Forms
{
    partial class frm_ST_BarcodeMatchTable
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_ST_BarcodeMatchTable));
            this.btn_SaveImport = new DevExpress.XtraEditors.SimpleButton();
            this.btn_Openfile = new DevExpress.XtraEditors.SimpleButton();
            this.grd_Import = new DevExpress.XtraGrid.GridControl();
            this.gv_import = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_Serial = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Barcode = new DevExpress.XtraGrid.Columns.GridColumn();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.btn_delete = new DevExpress.XtraEditors.SimpleButton();
            this.grd_MatchTable = new DevExpress.XtraGrid.GridControl();
            this.gv_MatchTable = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.btn_SaveMatchTable = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.grd_Import)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv_import)).BeginInit();
            this.groupBox1.SuspendLayout();
            this.groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grd_MatchTable)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv_MatchTable)).BeginInit();
            this.SuspendLayout();
            // 
            // btn_SaveImport
            // 
            this.btn_SaveImport.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btn_SaveImport.Location = new System.Drawing.Point(6, 375);
            this.btn_SaveImport.Name = "btn_SaveImport";
            this.btn_SaveImport.Size = new System.Drawing.Size(60, 20);
            this.btn_SaveImport.TabIndex = 1;
            this.btn_SaveImport.Text = "Save";
            this.btn_SaveImport.Click += new System.EventHandler(this.btnSaveImport_Click);
            // 
            // btn_Openfile
            // 
            this.btn_Openfile.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_Openfile.Location = new System.Drawing.Point(305, 375);
            this.btn_Openfile.Name = "btn_Openfile";
            this.btn_Openfile.Size = new System.Drawing.Size(129, 23);
            this.btn_Openfile.TabIndex = 2;
            this.btn_Openfile.Text = "Open Items Excel File";
            this.btn_Openfile.Click += new System.EventHandler(this.btn_Openfile_Click);
            // 
            // grd_Import
            // 
            this.grd_Import.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.grd_Import.Cursor = System.Windows.Forms.Cursors.Default;
            this.grd_Import.Location = new System.Drawing.Point(6, 26);
            this.grd_Import.MainView = this.gv_import;
            this.grd_Import.Name = "grd_Import";
            this.grd_Import.Size = new System.Drawing.Size(428, 343);
            this.grd_Import.TabIndex = 6;
            this.grd_Import.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gv_import});
            // 
            // gv_import
            // 
            this.gv_import.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_Serial,
            this.col_Barcode});
            this.gv_import.GridControl = this.grd_Import;
            this.gv_import.Name = "gv_import";
            this.gv_import.OptionsCustomization.AllowColumnMoving = false;
            this.gv_import.OptionsCustomization.AllowFilter = false;
            this.gv_import.OptionsCustomization.AllowGroup = false;
            this.gv_import.OptionsCustomization.AllowQuickHideColumns = false;
            this.gv_import.OptionsCustomization.AllowSort = false;
            this.gv_import.OptionsView.ShowAutoFilterRow = true;
            this.gv_import.OptionsView.ShowGroupPanel = false;
            // 
            // col_Serial
            // 
            this.col_Serial.Caption = "Serial";
            this.col_Serial.FieldName = "Serial";
            this.col_Serial.Name = "col_Serial";
            this.col_Serial.Visible = true;
            this.col_Serial.VisibleIndex = 0;
            this.col_Serial.Width = 139;
            // 
            // col_Barcode
            // 
            this.col_Barcode.Caption = "Barcode";
            this.col_Barcode.FieldName = "Barcode";
            this.col_Barcode.Name = "col_Barcode";
            this.col_Barcode.Visible = true;
            this.col_Barcode.VisibleIndex = 1;
            this.col_Barcode.Width = 135;
            // 
            // groupBox1
            // 
            this.groupBox1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox1.Controls.Add(this.grd_Import);
            this.groupBox1.Controls.Add(this.btn_SaveImport);
            this.groupBox1.Controls.Add(this.btn_Openfile);
            this.groupBox1.Location = new System.Drawing.Point(471, 12);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(440, 406);
            this.groupBox1.TabIndex = 7;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "Import Excel Sheet";
            // 
            // groupBox2
            // 
            this.groupBox2.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox2.Controls.Add(this.btn_delete);
            this.groupBox2.Controls.Add(this.grd_MatchTable);
            this.groupBox2.Controls.Add(this.btn_SaveMatchTable);
            this.groupBox2.Location = new System.Drawing.Point(12, 12);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(440, 406);
            this.groupBox2.TabIndex = 8;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "Barcode Match Table";
            // 
            // btn_delete
            // 
            this.btn_delete.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btn_delete.Location = new System.Drawing.Point(317, 375);
            this.btn_delete.Name = "btn_delete";
            this.btn_delete.Size = new System.Drawing.Size(117, 20);
            this.btn_delete.TabIndex = 7;
            this.btn_delete.Text = "Delete All Rows";
            this.btn_delete.Click += new System.EventHandler(this.btn_delete_Click);
            // 
            // grd_MatchTable
            // 
            this.grd_MatchTable.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.grd_MatchTable.Cursor = System.Windows.Forms.Cursors.Default;
            this.grd_MatchTable.Location = new System.Drawing.Point(6, 26);
            this.grd_MatchTable.MainView = this.gv_MatchTable;
            this.grd_MatchTable.Name = "grd_MatchTable";
            this.grd_MatchTable.Size = new System.Drawing.Size(428, 343);
            this.grd_MatchTable.TabIndex = 6;
            this.grd_MatchTable.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gv_MatchTable});
            // 
            // gv_MatchTable
            // 
            this.gv_MatchTable.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2});
            this.gv_MatchTable.GridControl = this.grd_MatchTable;
            this.gv_MatchTable.Name = "gv_MatchTable";
            this.gv_MatchTable.OptionsCustomization.AllowColumnMoving = false;
            this.gv_MatchTable.OptionsCustomization.AllowFilter = false;
            this.gv_MatchTable.OptionsCustomization.AllowGroup = false;
            this.gv_MatchTable.OptionsCustomization.AllowQuickHideColumns = false;
            this.gv_MatchTable.OptionsCustomization.AllowSort = false;
            this.gv_MatchTable.OptionsView.ShowAutoFilterRow = true;
            this.gv_MatchTable.OptionsView.ShowGroupPanel = false;
            this.gv_MatchTable.KeyDown += new System.Windows.Forms.KeyEventHandler(this.gv_MatchTable_KeyDown);
            // 
            // gridColumn1
            // 
            this.gridColumn1.Caption = "Serial";
            this.gridColumn1.FieldName = "Serial";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.Visible = true;
            this.gridColumn1.VisibleIndex = 0;
            this.gridColumn1.Width = 139;
            // 
            // gridColumn2
            // 
            this.gridColumn2.Caption = "Barcode";
            this.gridColumn2.FieldName = "Barcode";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.Visible = true;
            this.gridColumn2.VisibleIndex = 1;
            this.gridColumn2.Width = 135;
            // 
            // btn_SaveMatchTable
            // 
            this.btn_SaveMatchTable.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btn_SaveMatchTable.Location = new System.Drawing.Point(6, 375);
            this.btn_SaveMatchTable.Name = "btn_SaveMatchTable";
            this.btn_SaveMatchTable.Size = new System.Drawing.Size(60, 20);
            this.btn_SaveMatchTable.TabIndex = 1;
            this.btn_SaveMatchTable.Text = "Save";
            this.btn_SaveMatchTable.Click += new System.EventHandler(this.btn_SaveMatchTable_Click);
            // 
            // frm_ST_BarcodeMatchTable
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(923, 430);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "frm_ST_BarcodeMatchTable";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Barcode Match Table";
            this.Load += new System.EventHandler(this.frm_IC_ImportItems_Load);
            ((System.ComponentModel.ISupportInitialize)(this.grd_Import)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv_import)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grd_MatchTable)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv_MatchTable)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.SimpleButton btn_SaveImport;
        private DevExpress.XtraEditors.SimpleButton btn_Openfile;
        private DevExpress.XtraGrid.GridControl grd_Import;
        private DevExpress.XtraGrid.Views.Grid.GridView gv_import;
        private DevExpress.XtraGrid.Columns.GridColumn col_Serial;
        private DevExpress.XtraGrid.Columns.GridColumn col_Barcode;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox groupBox2;
        private DevExpress.XtraGrid.GridControl grd_MatchTable;
        private DevExpress.XtraGrid.Views.Grid.GridView gv_MatchTable;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraEditors.SimpleButton btn_SaveMatchTable;
        private DevExpress.XtraEditors.SimpleButton btn_delete;
    }
}