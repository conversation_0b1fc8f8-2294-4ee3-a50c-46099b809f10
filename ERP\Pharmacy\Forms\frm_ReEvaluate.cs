﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;

namespace Pharmacy.Forms
{
    public partial class frm_ReEvaluate : DevExpress.XtraEditors.XtraForm
    {
        public frm_ReEvaluate()
        {
            InitializeComponent();
        }

        private void btn_ReEvaluate_Click(object sender, EventArgs e)
        {
            txt_Start.DoValidate();
            txt_End.DoValidate();
            if (txt_Start.DateTime == DateTime.MinValue)
            {
                txt_Start.ErrorText = "*";
                return;
            }
            if (txt_End.DateTime == DateTime.MinValue)
            {
                txt_End.ErrorText = "*";
                return;
            }
            if (MessageBox.Show(
                "يفضل تشغيل هذه العملية من الخادم" + "\r\n" +
                "ستستغرق العملية بعض الوقت، برجاء الإنتظار حتى الإنتهاء" + "\r\n" +
                "هل تريد الإستمرار؟"
                , "تنبيه", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) == DialogResult.No)
                return;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var data = ((from d in DB.IC_Damageds            //IC_Damage
                         where d.DamagedDate.Date >= txt_Start.DateTime.Date
                         && d.DamagedDate.Date <= txt_End.DateTime.Date
                         select new
                         {
                             TransDate = d.DamagedDate,
                             ProcessId = DAL.Process.Damage,
                             SourceId = d.DamagedId,
                             IsFinished = false,
                         }).Union
                        (from d in DB.IC_OutTrns            //IC_OutTrans
                         where d.OutTrnsDate.Date >= txt_Start.DateTime.Date
                         && d.OutTrnsDate.Date <= txt_End.DateTime.Date
                         select new
                         {
                             TransDate = d.OutTrnsDate,
                             ProcessId = DAL.Process.OutTrns,
                             SourceId = d.OutTrnsId,
                             IsFinished = false,
                         }).Union
                        (from d in DB.SL_Invoices           //SL_Invoice
                         where d.InvoiceDate.Date >= txt_Start.DateTime.Date
                         && d.InvoiceDate.Date <= txt_End.DateTime.Date
                         select new
                         {
                             TransDate = d.InvoiceDate,
                             ProcessId = DAL.Process.SellInvoice,
                             SourceId = d.SL_InvoiceId,
                             IsFinished = false,
                         }).Union
                         (from d in DB.SL_SalesOrders           //SL_SalesOrders
                          where d.SalesOrderDate.Date >= txt_Start.DateTime.Date
                          && d.SalesOrderDate.Date <= txt_End.DateTime.Date
                          select new
                          {
                              TransDate = d.SalesOrderDate,
                              ProcessId = DAL.Process.SalesOrder,
                              SourceId = d.SL_SalesOrderId,
                              IsFinished = false,
                          })

                         .Union
                        (from d in DB.PR_Returns            //PR_Returns
                         where d.ReturnDate.Date >= txt_Start.DateTime.Date
                         && d.ReturnDate.Date <= txt_End.DateTime.Date
                         select new
                         {
                             TransDate = d.ReturnDate,
                             ProcessId = DAL.Process.PurchaseReturn,
                             SourceId = d.PR_ReturnId,
                             IsFinished = false,
                         }).Union
                        (from d in DB.IC_StoreMoves            //StoreMove
                         where d.StoreMoveDate.Date >= txt_Start.DateTime.Date
                         && d.StoreMoveDate.Date <= txt_End.DateTime.Date
                         select new
                         {
                             TransDate = d.StoreMoveDate,
                             ProcessId = DAL.Process.MoveOut,
                             SourceId = d.StoreMoveId,
                             IsFinished = false,
                         })

                         .Union
                        (from d in DB.Manf_QCs
                         where d.Manf_QCDate.Date >= txt_Start.DateTime.Date
                         && d.Manf_QCDate.Date <= txt_End.DateTime.Date
                         select new
                         {
                             TransDate = d.Manf_QCDate,
                             ProcessId = DAL.Process.QualityControl,
                             SourceId = d.Manf_QCId,
                             IsFinished = false,
                         })

                         .Union
                        (from d in DB.Manufacturings            //ManuFacturing
                         where d.StartDate.Date >= txt_Start.DateTime.Date
                         && d.StartDate.Date <= txt_End.DateTime.Date
                         select new
                         {
                             TransDate = d.StartDate,
                             ProcessId = DAL.Process.Manufacturing,
                             SourceId = d.Manf_Id,
                             IsFinished = d.IsFinished,
                         })).ToList().OrderBy(x => x.TransDate).ToList();

            int totalcount = data.Count();
            progressBar1.Show();
            progressBar1.Maximum = totalcount;
            progressBar1.Minimum = 0;
            progressBar1.Step = 1;

            int count = 0;

            frm_SL_Invoice frmsl = new frm_SL_Invoice();
            frmsl.Show();

            try
            {
                foreach (var d in data)
                {
                    progressBar1.Increment(1);

                    if (d.ProcessId == DAL.Process.SellInvoice)
                    {
                        try
                        {
                            frmsl.invoiceId = d.SourceId;
                            frmsl.LoadInvoice();
                            frmsl.Save_Invoice();
                        }
                        catch (Exception x) { MessageBox.Show("Sell Invoice. Source: " + d.SourceId + " Message: " + x.Message + " Inner: " + x.InnerException.Message); }
                    }
                    count++;
                }
                MessageBox.Show(" تم حفظ عدد " + count + " مستند ");
            }
            catch (Exception ex) { MessageBox.Show(ex.Message); }
            frmsl.Close();

            progressBar1.Hide();
            progressBar1.Value = 0;
        }

        private void mi_ReSavePRInvoices_Click(object sender, EventArgs e)
        {
        }

        private void btn_SLReturnInv_Click(object sender, EventArgs e)
        {
            if (txt_Start.DateTime == DateTime.MinValue
                || txt_End.DateTime == DateTime.MinValue)
                return;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var data = (from d in DB.SL_Returns
                        where d.ReturnDate.Date >= txt_Start.DateTime.Date
                        && d.ReturnDate.Date <= txt_End.DateTime.Date
                        select d.SL_ReturnId).ToList();

            int count = 0;

            foreach (var d in data)
            {
                frm_SL_Return frm = new frm_SL_Return(d);
                frm.Show();
                frm.Save_Invoice();
                frm.Close();

                count++;
            }
            MessageBox.Show(" تم حفظ عدد " + count + " مستند ");
        }

        private void btn_Replacement_Click(object sender, EventArgs e)
        {
            progressBar1.Value = 0;
            if (txt_Start.DateTime == DateTime.MinValue
                || txt_End.DateTime == DateTime.MinValue)
                return;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var data = (from d in DB.IC_StockTakings
                        where d.Date.Date >= txt_Start.DateTime.Date
                        && d.Date.Date <= txt_End.DateTime.Date
                        where d.Replacement == true && d.IsCommited
                        select d).ToList();

            int count = 0;
            progressBar1.Maximum = data.Count();
            foreach (var d in data)
            {
                frm_IC_ItemReplacment frm = new frm_IC_ItemReplacment(d.StorId, d.IC_StockTaking_ID, d.Date, d.Notes, d.IsCommited);
                frm.Show();
                frm.Commit();
                frm.Close();
                count++;
                progressBar1.Value = count;
            }
            MessageBox.Show(" تم حفظ عدد " + count + " مستند ");
        }


        private void btn_RepostAllProcess_Click(object sender, EventArgs e)
        {
            #region Validate
            txt_Start.DoValidate();
            txt_End.DoValidate();
            if (txt_Start.DateTime == DateTime.MinValue)
            {
                txt_Start.ErrorText = "*";
                return;
            }
            if (txt_End.DateTime == DateTime.MinValue)
            {
                txt_End.ErrorText = "*";
                return;
            }
            if (MessageBox.Show(
                "يفضل تشغيل هذه العملية من الخادم" + "\r\n" +
                "ستستغرق العملية بعض الوقت، برجاء الإنتظار حتى الإنتهاء" + "\r\n" +
                "هل تريد الإستمرار؟"
                , "تنبيه", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) == DialogResult.No)
                return;
            #endregion

            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            //var allProcess = DB.IC_ItemStores.Where(d => d.InsertTime.Date >= txt_Start.DateTime.Date && d.InsertTime.Date <= txt_End.DateTime.Date).Select(d => new { d.ProcessId, d.SourceId, d.InsertTime }).OrderBy(d => d.InsertTime).Distinct();
            //List<IC_processes> allProc = new List<IC_processes>();
            var allProc = (from s in DB.IC_ItemStores
                           where s.InsertTime.Date >= txt_Start.DateTime.Date
                          && s.InsertTime.Date <= txt_End.DateTime.Date
                           orderby s.InsertTime
                           select new IC_processes
                           {
                               ProcessId = s.ProcessId,
                               SourceId = s.ProcessId == (int)Process.SellInvoice ? DB.SL_InvoiceDetails.Where(x => x.SL_InvoiceDetailId == s.SourceId).Select(x => x.SL_InvoiceId).FirstOrDefault() :
                                          s.ProcessId == (int)(int)Process.Damage ? DB.IC_DamagedDetails.Where(x => x.DamagedDetailId == s.SourceId).Select(x => x.DamagedId).FirstOrDefault() :
                                          s.ProcessId == (int)Process.OutTrns ? DB.IC_OutTrnsDetails.Where(x => x.OutTrnsDetailId == s.SourceId).Select(x => x.OutTrnsId).FirstOrDefault() :
                                          s.ProcessId == (int)Process.SalesOrder ? DB.SL_SalesOrderDetails.Where(x => x.SL_SalesOrderDetailId == s.SourceId).Select(x => x.SL_SalesOrderId).FirstOrDefault() :
                                          s.ProcessId == (int)Process.PurchaseReturn ? DB.PR_ReturnDetails.Where(x => x.PR_ReturnDetailId == s.SourceId).Select(x => x.PR_ReturnId).FirstOrDefault() :
                                          s.ProcessId == (int)Process.MoveOut ? DB.IC_StoreMoveDetails.Where(x => x.StoreMoveDetailId == s.SourceId).Select(x => x.StoreMoveId).FirstOrDefault() :
                                          s.ProcessId == (int)Process.QualityControl ? DB.Manf_QCDetails.Where(x => x.Manf_QCDetailId == s.SourceId).Select(x => x.Manf_QCId).FirstOrDefault() :
                                          s.ProcessId == (int)Process.PurchaseInvoice ? DB.PR_InvoiceDetails.Where(x => x.PR_InvoiceDetailId == s.SourceId).Select(x => x.PR_InvoiceId).FirstOrDefault() :
                                          s.ProcessId == (int)Process.SellReturn ? DB.SL_ReturnDetails.Where(x => x.SL_ReturnDetailId == s.SourceId).Select(x => x.SL_ReturnId).FirstOrDefault() :
                                          s.ProcessId == (int)Process.InTrns ? DB.IC_InTrnsDetails.Where(x => x.InTrnsDetailId == s.SourceId).Select(x => x.InTrnsId).FirstOrDefault() :
                                          s.SourceId,
                               InsertTime = s.InsertTime

                           }).ToList().Distinct().ToList();



            if (Shared.PrInvoicePostToStore == false)
            {
                allProc.AddRange((from p in DB.PR_Invoices

                                  where p.InvoiceDate >= txt_Start.DateTime.Date
                                 && p.InvoiceDate <= txt_End.DateTime.Date

                                  orderby p.InvoiceDate
                                  select new IC_processes
                                  {
                                      ProcessId = (int)Process.PurchaseInvoice,
                                      SourceId = p.PR_InvoiceId,
                                      InsertTime = p.InvoiceDate
                                  }).Union

                    (from p in DB.PR_Returns

                     where p.ReturnDate >= txt_Start.DateTime.Date
                    && p.ReturnDate <= txt_End.DateTime.Date

                     orderby p.ReturnDate
                     select new IC_processes
                     {
                         ProcessId = (int)Process.PurchaseReturn,
                         SourceId = p.PR_ReturnId,
                         InsertTime = p.ReturnDate
                     }
                    ));

                //allProc.ToList().AddRange(prs.ToList());
            }

            if (Shared.InvoicePostToStore == false)
            {
                allProc.AddRange((from p in DB.SL_Invoices

                                  where p.InvoiceDate >= txt_Start.DateTime.Date
                                 && p.InvoiceDate <= txt_End.DateTime.Date

                                  orderby p.InvoiceDate
                                  select new IC_processes
                                  {
                                      ProcessId = (int)Process.SellInvoice,
                                      SourceId = p.SL_InvoiceId,
                                      InsertTime = p.InvoiceDate

                                  }).Union

                    (from p in DB.SL_Returns

                     where p.ReturnDate >= txt_Start.DateTime.Date
                    && p.ReturnDate <= txt_End.DateTime.Date

                     orderby p.ReturnDate
                     select new IC_processes
                     {
                         ProcessId = (int)Process.SellReturn,
                         SourceId = p.SL_ReturnId,
                         InsertTime = p.ReturnDate

                     }
                    ));
                //allProc.ToList().AddRange(vv);
            }

            if (allProc.Count() <= 0) return;

            allProc = allProc.OrderBy(x => x.InsertTime).ToList();

            int count = 0;
            progressBar1.Maximum = allProc.Count();
            foreach (var proc in allProc)
            {
                // Sell Invoice
                if (proc.ProcessId == (int)Process.SellInvoice)
                {
                    try
                    {
                        frm_SL_Invoice frmsl = new frm_SL_Invoice();
                        frmsl.Show();


                        frmsl.invoiceId = proc.SourceId;
                        frmsl.LoadInvoice();
                        frmsl.Save_Invoice();
                        frmsl.Close();
                    }
                    catch (Exception x) { MessageBox.Show("Sell Invoice. Source: " + proc.SourceId + " Message: " + x.Message + " Inner: " + x.InnerException.Message); }


                }


                count++;
                progressBar1.Value = count;
            }

            MessageBox.Show(" تم حفظ عدد " + count + " مستند ");
        }

        private void btn_IC_Trns_ALL_Click(object sender, EventArgs e)
        {
            #region Validate
            txt_Start.DoValidate();
            txt_End.DoValidate();
            if (txt_Start.DateTime == DateTime.MinValue)
            {
                txt_Start.ErrorText = "*";
                return;
            }
            if (txt_End.DateTime == DateTime.MinValue)
            {
                txt_End.ErrorText = "*";
                return;
            }
            if (MessageBox.Show(
                "يفضل تشغيل هذه العملية من الخادم" + "\r\n" +
                "ستستغرق العملية بعض الوقت، برجاء الإنتظار حتى الإنتهاء" + "\r\n" +
                "هل تريد الإستمرار؟"
                , "تنبيه", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) == DialogResult.No)
                return;
            #endregion

            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            //var allProcess = DB.IC_ItemStores.Where(d => d.InsertTime.Date >= txt_Start.DateTime.Date && d.InsertTime.Date <= txt_End.DateTime.Date).Select(d => new { d.ProcessId, d.SourceId, d.InsertTime }).OrderBy(d => d.InsertTime).Distinct();

            var allProc = (from s in DB.IC_ItemStores
                           where s.InsertTime.Date >= txt_Start.DateTime.Date
                          && s.InsertTime.Date <= txt_End.DateTime.Date
                           where s.ProcessId == (int)Process.InTrns
                           orderby s.InsertTime
                           select new
                           {
                               s.ProcessId,
                               SourceId = s.ProcessId == (int)Process.OutTrns ? DB.IC_OutTrnsDetails.Where(x => x.OutTrnsDetailId == s.SourceId).Select(x => x.OutTrnsId).FirstOrDefault() :
                                          s.ProcessId == (int)Process.InTrns ? DB.IC_InTrnsDetails.Where(x => x.InTrnsDetailId == s.SourceId).Select(x => x.InTrnsId).FirstOrDefault() :
                                          s.SourceId

                           }).ToList().Distinct();

            if (allProc.Count() <= 0) return;

            int count = 0;
            int result;
            progressBar1.Maximum = allProc.Count();
            foreach (var proc in allProc)
            {
                count++;
                progressBar1.Value = count;
            }

            MessageBox.Show(" تم حفظ عدد " + count + " مستند ");
        }

        private void btnRe_CodingCostCenter_Click(object sender, EventArgs e)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            foreach (ACC_CostCenter cc in DB.ACC_CostCenters)
            {
                long z = DB.ACC_CostCenters.Select(x => Convert.ToInt64(x.CostCenterCode)).ToList().DefaultIfEmpty(0).Max();
                cc.CostCenterCode = (++z).ToString();
                //var parent = DB.ACC_CostCenters.Where(c => c.CostCenterId == cc.ParentCCId).First();
                //cc.ccNumber = HelperAcc.CcNumGenerated(parent); 
                int cc_count = 0;
                if (cc.ParentCCId == null)
                    cc_count = DB.ACC_CostCenters.Where(x => x.ParentCCId == null).Where(x => x.CostCenterId < cc.CostCenterId).Count();
                else
                    cc_count = DB.ACC_CostCenters.Where(x => x.ParentCCId == cc.ParentCCId).Where(x => x.CostCenterId < cc.CostCenterId).Count();
                string par = "";
                if (cc.Level > 1)
                {
                    par = DB.ACC_CostCenters.Where(c => c.CostCenterId == cc.ParentCCId).Select(x => x.ccNumber).First();
                }
                cc.ccNumber = string.Format("{0}{1}{2}", par, cc.Level == 1 ? "0" : cc.Level == 2 ? cc_count > 9 ? "0" : "00" : cc_count > 9 ? "00" : "000", ++cc_count);
                DB.SubmitChanges();
            }
        }

        private void btn_ReCodingAccounts_Click(object sender, EventArgs e)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            foreach (ACC_Account ac in DB.ACC_Accounts)
            {
                if (ac.Level > 1)
                    if (ac.AcNumber[1] != '0')
                    {
                        ac.AcNumber = ac.AcNumber.Insert(1, "0");
                        DB.SubmitChanges();
                    }
            }
        }

        private void btn_Re_SerialMontlyJournalCode_Click(object sender, EventArgs e)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            var jrnlLst = DB.ACC_Journals.OrderBy(x => x.InsertDate).ToList();
            progressBar1.Value = 0;
            progressBar1.Maximum = jrnlLst.Count();
            foreach (ACC_Journal jrnl in DB.ACC_Journals.OrderBy(x => x.InsertDate))
            {
                int jnum = jrnlLst.Where(j => j.InsertDate < jrnl.InsertDate && j.InsertDate.Month == jrnl.InsertDate.Month && j.ProcessId == jrnl.ProcessId).Count();
                jrnl.Monthly_Code = string.Format("{0}-{1}", jrnl.InsertDate.Month, ++jnum);
                DB.SubmitChanges();
                progressBar1.Value++;
            }
        }
    }
    class IC_processes
    {
        public int ProcessId, SourceId;
        public DateTime InsertTime;
    }
}