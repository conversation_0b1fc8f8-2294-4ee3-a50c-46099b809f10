﻿namespace Reports
{
    partial class rpt_PayNotes
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.xrPanel1 = new DevExpress.XtraReports.UI.XRPanel();
            this.xrLine1 = new DevExpress.XtraReports.UI.XRLine();
            this.xrLabel7 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_User = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel5 = new DevExpress.XtraReports.UI.XRLabel();
            this.lblTotalWords = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Account = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Amount = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_RegDate = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Reason = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_RespondDate = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_BankName = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Notes = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_PayCondition = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_DueDate = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Serial = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel16 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel10 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Is_Bank = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel8 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Respond = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel6 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel4 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel3 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel2 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel1 = new DevExpress.XtraReports.UI.XRLabel();
            this.picLogo = new DevExpress.XtraReports.UI.XRPictureBox();
            this.lblCompName = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_PaperDetails = new DevExpress.XtraReports.UI.XRLabel();
            this.topMarginBand1 = new DevExpress.XtraReports.UI.TopMarginBand();
            this.bottomMarginBand1 = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.lbl_CostCenter = new DevExpress.XtraReports.UI.XRLabel();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrPanel1});
            this.Detail.HeightF = 347F;
            this.Detail.MultiColumn.ColumnSpacing = 10F;
            this.Detail.MultiColumn.ColumnWidth = 700F;
            this.Detail.MultiColumn.Mode = DevExpress.XtraReports.UI.MultiColumnMode.UseColumnCount;
            this.Detail.Name = "Detail";
            this.Detail.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Detail.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrPanel1
            // 
            this.xrPanel1.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrPanel1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLine1,
            this.xrLabel7,
            this.lbl_User,
            this.xrLabel5,
            this.lblTotalWords,
            this.lbl_Account,
            this.lbl_Amount,
            this.lbl_RegDate,
            this.lbl_Reason,
            this.lbl_RespondDate,
            this.lbl_BankName,
            this.lbl_Notes,
            this.lbl_PayCondition,
            this.lbl_DueDate,
            this.lbl_Serial,
            this.xrLabel16,
            this.xrLabel10,
            this.lbl_Is_Bank,
            this.xrLabel8,
            this.lbl_Respond,
            this.xrLabel6,
            this.xrLabel4,
            this.xrLabel3,
            this.xrLabel2,
            this.xrLabel1,
            this.picLogo,
            this.lblCompName,
            this.lbl_PaperDetails});
            this.xrPanel1.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.xrPanel1.Name = "xrPanel1";
            this.xrPanel1.SizeF = new System.Drawing.SizeF(750F, 337.0417F);
            this.xrPanel1.StylePriority.UseBorders = false;
            // 
            // xrLine1
            // 
            this.xrLine1.BackColor = System.Drawing.Color.DarkGray;
            this.xrLine1.BorderColor = System.Drawing.Color.DarkGray;
            this.xrLine1.BorderWidth = 0F;
            this.xrLine1.ForeColor = System.Drawing.Color.DarkGray;
            this.xrLine1.LineWidth = 0;
            this.xrLine1.LocationFloat = new DevExpress.Utils.PointFloat(23.50019F, 131.25F);
            this.xrLine1.Name = "xrLine1";
            this.xrLine1.SizeF = new System.Drawing.SizeF(717.4998F, 3.541748F);
            this.xrLine1.StylePriority.UseBackColor = false;
            this.xrLine1.StylePriority.UseBorderColor = false;
            this.xrLine1.StylePriority.UseBorderWidth = false;
            this.xrLine1.StylePriority.UseForeColor = false;
            // 
            // xrLabel7
            // 
            this.xrLabel7.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel7.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel7.LocationFloat = new DevExpress.Utils.PointFloat(201.75F, 73.9583F);
            this.xrLabel7.Name = "xrLabel7";
            this.xrLabel7.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel7.SizeF = new System.Drawing.SizeF(98.54172F, 22.99999F);
            this.xrLabel7.StylePriority.UseBorders = false;
            this.xrLabel7.StylePriority.UseFont = false;
            this.xrLabel7.StylePriority.UseTextAlignment = false;
            this.xrLabel7.Text = "اسم المستخدم";
            this.xrLabel7.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_User
            // 
            this.lbl_User.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_User.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_User.LocationFloat = new DevExpress.Utils.PointFloat(23.50025F, 73.9583F);
            this.lbl_User.Name = "lbl_User";
            this.lbl_User.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_User.SizeF = new System.Drawing.SizeF(178.2498F, 22.99999F);
            this.lbl_User.StylePriority.UseBorders = false;
            this.lbl_User.StylePriority.UseFont = false;
            this.lbl_User.StylePriority.UseTextAlignment = false;
            this.lbl_User.Text = "..";
            this.lbl_User.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel5
            // 
            this.xrLabel5.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel5.LocationFloat = new DevExpress.Utils.PointFloat(642.4582F, 192.7025F);
            this.xrLabel5.Name = "xrLabel5";
            this.xrLabel5.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel5.SizeF = new System.Drawing.SizeF(98.54181F, 24.49995F);
            this.xrLabel5.StylePriority.UseFont = false;
            this.xrLabel5.StylePriority.UseTextAlignment = false;
            this.xrLabel5.Text = "فقط وقدره";
            this.xrLabel5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lblTotalWords
            // 
            this.lblTotalWords.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lblTotalWords.LocationFloat = new DevExpress.Utils.PointFloat(129.7498F, 192.7025F);
            this.lblTotalWords.Name = "lblTotalWords";
            this.lblTotalWords.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblTotalWords.SizeF = new System.Drawing.SizeF(512.7083F, 24.49991F);
            this.lblTotalWords.StylePriority.UseFont = false;
            this.lblTotalWords.StylePriority.UseTextAlignment = false;
            this.lblTotalWords.Text = "..";
            this.lblTotalWords.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_Account
            // 
            this.lbl_Account.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_Account.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_Account.LocationFloat = new DevExpress.Utils.PointFloat(129.7498F, 146.7025F);
            this.lbl_Account.Name = "lbl_Account";
            this.lbl_Account.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Account.SizeF = new System.Drawing.SizeF(512.7083F, 23.00002F);
            this.lbl_Account.StylePriority.UseBorders = false;
            this.lbl_Account.StylePriority.UseFont = false;
            this.lbl_Account.StylePriority.UseTextAlignment = false;
            this.lbl_Account.Text = "محمد احمد";
            this.lbl_Account.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_Amount
            // 
            this.lbl_Amount.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_Amount.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_Amount.LocationFloat = new DevExpress.Utils.PointFloat(463.2914F, 169.7025F);
            this.lbl_Amount.Name = "lbl_Amount";
            this.lbl_Amount.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Amount.SizeF = new System.Drawing.SizeF(179.1666F, 23.00002F);
            this.lbl_Amount.StylePriority.UseBorders = false;
            this.lbl_Amount.StylePriority.UseFont = false;
            this.lbl_Amount.StylePriority.UseTextAlignment = false;
            this.lbl_Amount.Text = "5000";
            this.lbl_Amount.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_RegDate
            // 
            this.lbl_RegDate.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_RegDate.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_RegDate.LocationFloat = new DevExpress.Utils.PointFloat(23.50025F, 50.95832F);
            this.lbl_RegDate.Name = "lbl_RegDate";
            this.lbl_RegDate.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_RegDate.SizeF = new System.Drawing.SizeF(178.2498F, 22.99998F);
            this.lbl_RegDate.StylePriority.UseBorders = false;
            this.lbl_RegDate.StylePriority.UseFont = false;
            this.lbl_RegDate.StylePriority.UseTextAlignment = false;
            this.lbl_RegDate.Text = "1/12/2012";
            this.lbl_RegDate.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_Reason
            // 
            this.lbl_Reason.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_Reason.CanGrow = false;
            this.lbl_Reason.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_Reason.LocationFloat = new DevExpress.Utils.PointFloat(312.1252F, 263.2025F);
            this.lbl_Reason.Name = "lbl_Reason";
            this.lbl_Reason.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Reason.SizeF = new System.Drawing.SizeF(330.3329F, 22.99994F);
            this.lbl_Reason.StylePriority.UseBorders = false;
            this.lbl_Reason.StylePriority.UseFont = false;
            this.lbl_Reason.StylePriority.UseTextAlignment = false;
            this.lbl_Reason.Text = "فاتورة مشتريات رقم 123";
            this.lbl_Reason.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_RespondDate
            // 
            this.lbl_RespondDate.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_RespondDate.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_RespondDate.LocationFloat = new DevExpress.Utils.PointFloat(23.50031F, 217.2025F);
            this.lbl_RespondDate.Name = "lbl_RespondDate";
            this.lbl_RespondDate.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_RespondDate.SizeF = new System.Drawing.SizeF(160.5416F, 22.99998F);
            this.lbl_RespondDate.StylePriority.UseBorders = false;
            this.lbl_RespondDate.StylePriority.UseFont = false;
            this.lbl_RespondDate.StylePriority.UseTextAlignment = false;
            this.lbl_RespondDate.Text = "تاريخ السداد - تاريخ الرد";
            this.lbl_RespondDate.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.lbl_RespondDate.Visible = false;
            // 
            // lbl_BankName
            // 
            this.lbl_BankName.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_BankName.CanGrow = false;
            this.lbl_BankName.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_BankName.LocationFloat = new DevExpress.Utils.PointFloat(312.1252F, 240.2025F);
            this.lbl_BankName.Name = "lbl_BankName";
            this.lbl_BankName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_BankName.SizeF = new System.Drawing.SizeF(330.3333F, 23.00002F);
            this.lbl_BankName.StylePriority.UseBorders = false;
            this.lbl_BankName.StylePriority.UseFont = false;
            this.lbl_BankName.StylePriority.UseTextAlignment = false;
            this.lbl_BankName.Text = "بنك القاهرة";
            this.lbl_BankName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_Notes
            // 
            this.lbl_Notes.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_Notes.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_Notes.LocationFloat = new DevExpress.Utils.PointFloat(128.7498F, 309.2026F);
            this.lbl_Notes.Multiline = true;
            this.lbl_Notes.Name = "lbl_Notes";
            this.lbl_Notes.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Notes.SizeF = new System.Drawing.SizeF(513.7081F, 23.00002F);
            this.lbl_Notes.StylePriority.UseBorders = false;
            this.lbl_Notes.StylePriority.UseFont = false;
            this.lbl_Notes.StylePriority.UseTextAlignment = false;
            this.lbl_Notes.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_PayCondition
            // 
            this.lbl_PayCondition.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_PayCondition.CanGrow = false;
            this.lbl_PayCondition.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_PayCondition.LocationFloat = new DevExpress.Utils.PointFloat(128.7498F, 286.2025F);
            this.lbl_PayCondition.Name = "lbl_PayCondition";
            this.lbl_PayCondition.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_PayCondition.SizeF = new System.Drawing.SizeF(513.7082F, 23.00002F);
            this.lbl_PayCondition.StylePriority.UseBorders = false;
            this.lbl_PayCondition.StylePriority.UseFont = false;
            this.lbl_PayCondition.StylePriority.UseTextAlignment = false;
            this.lbl_PayCondition.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_DueDate
            // 
            this.lbl_DueDate.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_DueDate.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_DueDate.LocationFloat = new DevExpress.Utils.PointFloat(481.9168F, 217.2025F);
            this.lbl_DueDate.Name = "lbl_DueDate";
            this.lbl_DueDate.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_DueDate.SizeF = new System.Drawing.SizeF(160.5415F, 23.00002F);
            this.lbl_DueDate.StylePriority.UseBorders = false;
            this.lbl_DueDate.StylePriority.UseFont = false;
            this.lbl_DueDate.StylePriority.UseTextAlignment = false;
            this.lbl_DueDate.Text = "1/1/2013";
            this.lbl_DueDate.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_Serial
            // 
            this.lbl_Serial.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_Serial.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.lbl_Serial.LocationFloat = new DevExpress.Utils.PointFloat(23.50019F, 96.95829F);
            this.lbl_Serial.Name = "lbl_Serial";
            this.lbl_Serial.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Serial.SizeF = new System.Drawing.SizeF(178.2498F, 23F);
            this.lbl_Serial.StylePriority.UseBorders = false;
            this.lbl_Serial.StylePriority.UseFont = false;
            this.lbl_Serial.StylePriority.UseTextAlignment = false;
            this.lbl_Serial.Text = "1";
            this.lbl_Serial.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.lbl_Serial.Visible = false;
            // 
            // xrLabel16
            // 
            this.xrLabel16.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel16.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel16.LocationFloat = new DevExpress.Utils.PointFloat(642.4581F, 263.2026F);
            this.xrLabel16.Name = "xrLabel16";
            this.xrLabel16.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel16.SizeF = new System.Drawing.SizeF(98.54175F, 22.99998F);
            this.xrLabel16.StylePriority.UseBorders = false;
            this.xrLabel16.StylePriority.UseFont = false;
            this.xrLabel16.StylePriority.UseTextAlignment = false;
            this.xrLabel16.Text = "و ذلك قيمة";
            this.xrLabel16.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel10
            // 
            this.xrLabel10.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel10.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel10.LocationFloat = new DevExpress.Utils.PointFloat(642.4581F, 169.7025F);
            this.xrLabel10.Name = "xrLabel10";
            this.xrLabel10.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel10.SizeF = new System.Drawing.SizeF(98.54187F, 22.99998F);
            this.xrLabel10.StylePriority.UseBorders = false;
            this.xrLabel10.StylePriority.UseFont = false;
            this.xrLabel10.StylePriority.UseTextAlignment = false;
            this.xrLabel10.Text = "المبلغ";
            this.xrLabel10.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_Is_Bank
            // 
            this.lbl_Is_Bank.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_Is_Bank.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.lbl_Is_Bank.LocationFloat = new DevExpress.Utils.PointFloat(642.4584F, 240.2025F);
            this.lbl_Is_Bank.Name = "lbl_Is_Bank";
            this.lbl_Is_Bank.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Is_Bank.SizeF = new System.Drawing.SizeF(98.54172F, 23.00002F);
            this.lbl_Is_Bank.StylePriority.UseBorders = false;
            this.lbl_Is_Bank.StylePriority.UseFont = false;
            this.lbl_Is_Bank.StylePriority.UseTextAlignment = false;
            this.lbl_Is_Bank.Text = "من بنك / خزينة";
            this.lbl_Is_Bank.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel8
            // 
            this.xrLabel8.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel8.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel8.LocationFloat = new DevExpress.Utils.PointFloat(642.4584F, 217.2025F);
            this.xrLabel8.Name = "xrLabel8";
            this.xrLabel8.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel8.SizeF = new System.Drawing.SizeF(98.54166F, 23.00002F);
            this.xrLabel8.StylePriority.UseBorders = false;
            this.xrLabel8.StylePriority.UseFont = false;
            this.xrLabel8.StylePriority.UseTextAlignment = false;
            this.xrLabel8.Text = "تاريخ الاستحقاق";
            this.xrLabel8.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_Respond
            // 
            this.lbl_Respond.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_Respond.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.lbl_Respond.LocationFloat = new DevExpress.Utils.PointFloat(184.0419F, 217.2025F);
            this.lbl_Respond.Name = "lbl_Respond";
            this.lbl_Respond.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Respond.SizeF = new System.Drawing.SizeF(98.54175F, 22.99995F);
            this.lbl_Respond.StylePriority.UseBorders = false;
            this.lbl_Respond.StylePriority.UseFont = false;
            this.lbl_Respond.StylePriority.UseTextAlignment = false;
            this.lbl_Respond.Text = "تاريخ سداد / رد";
            this.lbl_Respond.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.lbl_Respond.Visible = false;
            // 
            // xrLabel6
            // 
            this.xrLabel6.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel6.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel6.LocationFloat = new DevExpress.Utils.PointFloat(642.4583F, 146.7025F);
            this.xrLabel6.Name = "xrLabel6";
            this.xrLabel6.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel6.SizeF = new System.Drawing.SizeF(98.54175F, 23.00002F);
            this.xrLabel6.StylePriority.UseBorders = false;
            this.xrLabel6.StylePriority.UseFont = false;
            this.xrLabel6.StylePriority.UseTextAlignment = false;
            this.xrLabel6.Text = "لحســـاب";
            this.xrLabel6.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel4
            // 
            this.xrLabel4.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel4.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel4.LocationFloat = new DevExpress.Utils.PointFloat(642.4581F, 309.2026F);
            this.xrLabel4.Name = "xrLabel4";
            this.xrLabel4.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel4.SizeF = new System.Drawing.SizeF(97.54181F, 23F);
            this.xrLabel4.StylePriority.UseBorders = false;
            this.xrLabel4.StylePriority.UseFont = false;
            this.xrLabel4.StylePriority.UseTextAlignment = false;
            this.xrLabel4.Text = "ملاحظات";
            this.xrLabel4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel3
            // 
            this.xrLabel3.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel3.CanGrow = false;
            this.xrLabel3.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel3.LocationFloat = new DevExpress.Utils.PointFloat(642.458F, 286.2025F);
            this.xrLabel3.Name = "xrLabel3";
            this.xrLabel3.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel3.SizeF = new System.Drawing.SizeF(97.54181F, 23F);
            this.xrLabel3.StylePriority.UseBorders = false;
            this.xrLabel3.StylePriority.UseFont = false;
            this.xrLabel3.StylePriority.UseTextAlignment = false;
            this.xrLabel3.Text = "شرط الدفع";
            this.xrLabel3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel2
            // 
            this.xrLabel2.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel2.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel2.LocationFloat = new DevExpress.Utils.PointFloat(201.75F, 50.95832F);
            this.xrLabel2.Name = "xrLabel2";
            this.xrLabel2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel2.SizeF = new System.Drawing.SizeF(98.54172F, 22.99998F);
            this.xrLabel2.StylePriority.UseBorders = false;
            this.xrLabel2.StylePriority.UseFont = false;
            this.xrLabel2.StylePriority.UseTextAlignment = false;
            this.xrLabel2.Text = "تاريخ التسجيل";
            this.xrLabel2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel1
            // 
            this.xrLabel1.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel1.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel1.LocationFloat = new DevExpress.Utils.PointFloat(201.75F, 96.95829F);
            this.xrLabel1.Name = "xrLabel1";
            this.xrLabel1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel1.SizeF = new System.Drawing.SizeF(98.54172F, 23F);
            this.xrLabel1.StylePriority.UseBorders = false;
            this.xrLabel1.StylePriority.UseFont = false;
            this.xrLabel1.StylePriority.UseTextAlignment = false;
            this.xrLabel1.Text = "تسلسل";
            this.xrLabel1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.xrLabel1.Visible = false;
            // 
            // picLogo
            // 
            this.picLogo.LocationFloat = new DevExpress.Utils.PointFloat(599.125F, 50.95832F);
            this.picLogo.Name = "picLogo";
            this.picLogo.SizeF = new System.Drawing.SizeF(141.875F, 70F);
            this.picLogo.Sizing = DevExpress.XtraPrinting.ImageSizeMode.StretchImage;
            // 
            // lblCompName
            // 
            this.lblCompName.Font = new System.Drawing.Font("Times New Roman", 16F, System.Drawing.FontStyle.Bold);
            this.lblCompName.LocationFloat = new DevExpress.Utils.PointFloat(362.8748F, 10.00001F);
            this.lblCompName.Name = "lblCompName";
            this.lblCompName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblCompName.SizeF = new System.Drawing.SizeF(378.1252F, 30F);
            this.lblCompName.StylePriority.UseFont = false;
            this.lblCompName.StylePriority.UseTextAlignment = false;
            this.lblCompName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_PaperDetails
            // 
            this.lbl_PaperDetails.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_PaperDetails.Font = new System.Drawing.Font("Times New Roman", 16F, System.Drawing.FontStyle.Bold);
            this.lbl_PaperDetails.LocationFloat = new DevExpress.Utils.PointFloat(23.50025F, 10.00001F);
            this.lbl_PaperDetails.Name = "lbl_PaperDetails";
            this.lbl_PaperDetails.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_PaperDetails.SizeF = new System.Drawing.SizeF(276.7915F, 30F);
            this.lbl_PaperDetails.StylePriority.UseBorders = false;
            this.lbl_PaperDetails.StylePriority.UseFont = false;
            this.lbl_PaperDetails.StylePriority.UseTextAlignment = false;
            this.lbl_PaperDetails.Text = "شيك صادر- مستحق رقم 123";
            this.lbl_PaperDetails.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // topMarginBand1
            // 
            this.topMarginBand1.HeightF = 56F;
            this.topMarginBand1.Name = "topMarginBand1";
            // 
            // bottomMarginBand1
            // 
            this.bottomMarginBand1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.lbl_CostCenter});
            this.bottomMarginBand1.HeightF = 38F;
            this.bottomMarginBand1.Name = "bottomMarginBand1";
            // 
            // lbl_CostCenter
            // 
            this.lbl_CostCenter.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_CostCenter.CanGrow = false;
            this.lbl_CostCenter.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_CostCenter.LocationFloat = new DevExpress.Utils.PointFloat(212.5F, 12.5F);
            this.lbl_CostCenter.Name = "lbl_CostCenter";
            this.lbl_CostCenter.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_CostCenter.SizeF = new System.Drawing.SizeF(330.3333F, 23.00002F);
            this.lbl_CostCenter.StylePriority.UseBorders = false;
            this.lbl_CostCenter.StylePriority.UseFont = false;
            this.lbl_CostCenter.StylePriority.UseTextAlignment = false;
            this.lbl_CostCenter.Text = "بنك القاهرة";
            this.lbl_CostCenter.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.lbl_CostCenter.Visible = false;
            // 
            // rpt_PayNotes
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail,
            this.topMarginBand1,
            this.bottomMarginBand1});
            this.Margins = new System.Drawing.Printing.Margins(39, 37, 56, 38);
            this.PageHeight = 1169;
            this.PageWidth = 827;
            this.PaperKind = System.Drawing.Printing.PaperKind.A4;
            this.ShowPrintMarginsWarning = false;
            this.Version = "15.1";
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.XRPanel xrPanel1;
        private DevExpress.XtraReports.UI.TopMarginBand topMarginBand1;
        private DevExpress.XtraReports.UI.BottomMarginBand bottomMarginBand1;
        private DevExpress.XtraReports.UI.XRPictureBox picLogo;
        private DevExpress.XtraReports.UI.XRLabel lblCompName;
        private DevExpress.XtraReports.UI.XRLabel xrLabel10;
        private DevExpress.XtraReports.UI.XRLabel lbl_Is_Bank;
        private DevExpress.XtraReports.UI.XRLabel xrLabel8;
        private DevExpress.XtraReports.UI.XRLabel lbl_Respond;
        private DevExpress.XtraReports.UI.XRLabel xrLabel6;
        private DevExpress.XtraReports.UI.XRLabel xrLabel4;
        private DevExpress.XtraReports.UI.XRLabel xrLabel3;
        private DevExpress.XtraReports.UI.XRLabel xrLabel2;
        private DevExpress.XtraReports.UI.XRLabel xrLabel1;
        private DevExpress.XtraReports.UI.XRLabel xrLabel16;
        private DevExpress.XtraReports.UI.XRLabel lbl_Account;
        private DevExpress.XtraReports.UI.XRLabel lbl_Amount;
        private DevExpress.XtraReports.UI.XRLabel lbl_RegDate;
        private DevExpress.XtraReports.UI.XRLabel lbl_Reason;
        private DevExpress.XtraReports.UI.XRLabel lbl_RespondDate;
        private DevExpress.XtraReports.UI.XRLabel lbl_BankName;
        private DevExpress.XtraReports.UI.XRLabel lbl_Notes;
        private DevExpress.XtraReports.UI.XRLabel lbl_PayCondition;
        private DevExpress.XtraReports.UI.XRLabel lbl_DueDate;
        private DevExpress.XtraReports.UI.XRLabel lbl_PaperDetails;
        private DevExpress.XtraReports.UI.XRLabel lbl_Serial;
        private DevExpress.XtraReports.UI.XRLabel xrLabel5;
        private DevExpress.XtraReports.UI.XRLabel lblTotalWords;
        private DevExpress.XtraReports.UI.XRLabel xrLabel7;
        private DevExpress.XtraReports.UI.XRLabel lbl_User;
        private DevExpress.XtraReports.UI.XRLine xrLine1;
        private DevExpress.XtraReports.UI.XRLabel lbl_CostCenter;
    }
}
