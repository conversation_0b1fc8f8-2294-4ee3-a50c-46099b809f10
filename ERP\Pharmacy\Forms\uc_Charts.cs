﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using DAL;

namespace Pharmacy.Forms
{
    public partial class uc_Charts : UserControl
    {
        public uc_Charts()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            if (Shared.StockIsPeriodic == false)
                return;
            LoadData();

        }

        private void LoadData()
        {

            ERPDataContext DB = new ERPDataContext();

            //Accounts must be set before

            //if (Shared.st_Store.PurchasesAcc.HasValue == false ||
            //        Shared.st_Store.PurchasesReturnAcc.HasValue == false ||
            //        Shared.st_Store.SalesAcc.HasValue == false ||
            //        Shared.st_Store.SalesReturnAcc.HasValue == false ||
            //        Shared.st_Store.PurchaseDiscountAcc.HasValue == false ||
            //        Shared.st_Store.SalesDiscountAcc.HasValue == false)
            //    return;

            var sl = (from s in DB.SL_Invoices
                      group s by new { s.InvoiceDate.Date } into grp
                      select new
                      {
                          Net = grp.Sum(x => x.Net),
                          Date = grp.Key.Date.ToShortDateString(),
                          DateOrder = grp.Key.Date
                      }).OrderByDescending(s => s.DateOrder).Distinct();

            chSales.Series[0].ArgumentScaleType = DevExpress.XtraCharts.ScaleType.DateTime;
            chSales.Series[0].ValueScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            chSales.Series[0].DataSource = sl;
            chSales.Series[0].ArgumentDataMember = "DateOrder";
            chSales.Series[0].ValueDataMembers[0] = "Net";
            chSales.RefreshData();
            chSales.Refresh();

            var pr = (from s in DB.SL_Returns
                      group s by new { s.ReturnDate.Date } into grp
                      select new
                      {
                          Net = grp.Sum(x => x.Net),
                          Date = grp.Key.Date.ToShortDateString(),
                          DateOrder = grp.Key.Date
                      }).OrderByDescending(s => s.DateOrder).Distinct();

            chTaxes.Series[0].ArgumentScaleType = DevExpress.XtraCharts.ScaleType.DateTime;
            chTaxes.Series[0].ValueScaleType = DevExpress.XtraCharts.ScaleType.Numerical;
            chTaxes.Series[0].DataSource = pr;
            chTaxes.Series[0].ArgumentDataMember = "DateOrder";
            chTaxes.Series[0].ValueDataMembers[0] = "Net";
            chTaxes.RefreshData();
            chTaxes.Refresh();


            List<chartAccoutns> chartData = new List<chartAccoutns>();
            var taxtypes = (from t in DB.SL_InvoiceDetailSubTaxValues
                            join td in DB.SL_InvoiceDetails on t.InvoiceDetailId equals td.SL_InvoiceDetailId
                            join tm in DB.SL_Invoices on td.SL_InvoiceId equals tm.SL_InvoiceId
                            join lkpTaxes in DB.E_TaxableTypes on t.esubTypeId equals lkpTaxes.E_TaxableTypeId
                            group t by new { lkpTaxes.DescriptionAr, t.esubTypeId } into grp
                            select new
                            {
                                TaxTypeId = grp.Key.esubTypeId,
                                TaxTypeName = grp.Key.DescriptionAr,
                                Amount = grp.Select(x => x.value).Sum()
                            }).ToList();

            foreach (var t in taxtypes.Select(x => x.TaxTypeName).Distinct().ToList())
            {
                chTrade.Series.Add(t,
                    DevExpress.XtraCharts.ViewType.Bar);
            }

            foreach (var chartAcc in taxtypes)
            {
                chartData.Add(new chartAccoutns
                {
                    AccountId = chartAcc.TaxTypeId,
                    AccountName = chartAcc.TaxTypeName,
                    Balance = chartAcc.Amount,
                });

                chTrade.Series[chartAcc.TaxTypeName].Points.
                    Add(new DevExpress.XtraCharts.SeriesPoint(chartAcc.TaxTypeName, Math.Abs(chartAcc.Amount)));
            }

            chTrade.DataSource = taxtypes;

            chTrade.RefreshData();
            chTrade.Refresh();
            chTrade.Invalidate();
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            this.Refresh();

            LoadData();
        }

        private void uc_Charts_Load(object sender, EventArgs e)
        {
            LoadData();
        }
    }

    class chartAccoutns
    {
        int accountId;

        public int AccountId
        {
            get { return accountId; }
            set { accountId = value; }
        }
        string accountName;

        public string AccountName
        {
            get { return accountName; }
            set { accountName = value; }
        }

        decimal balance;

        public decimal Balance
        {
            get { return balance; }
            set { balance = value; }
        }

        private int month;
        public int Month { get => month; set => month = value; }
    }
}
