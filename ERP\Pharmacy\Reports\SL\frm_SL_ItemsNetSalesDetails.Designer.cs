﻿namespace Reports
{
    partial class frm_SL_ItemsNetSalesDetails
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_SL_ItemsNetSalesDetails));
            this.barManager1 = new DevExpress.XtraBars.BarManager();
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtnPrint = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnPreview = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnRefresh = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.grdCategory = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_Customer = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_SoldTotalPrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ReturnTotalPrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_NetTotalPrice_Local = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_DiffNet = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ReturnNetPrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_SoldNetPrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_Ratio = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.rep_Currency = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.picLogo = new DevExpress.XtraEditors.PictureEdit();
            this.lblReportName = new DevExpress.XtraEditors.TextEdit();
            this.lblDateFilter = new DevExpress.XtraEditors.TextEdit();
            this.lblFilter = new DevExpress.XtraEditors.TextEdit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdCategory)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Ratio)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Currency)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblReportName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDateFilter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFilter.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnPreview,
            this.barBtnClose,
            this.barBtnPrint,
            this.barBtnRefresh});
            this.barManager1.MaxItemId = 29;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(567, 147);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnPrint),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnPreview),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnRefresh),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtnPrint
            // 
            this.barBtnPrint.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnPrint, "barBtnPrint");
            this.barBtnPrint.Glyph = ((System.Drawing.Image)(resources.GetObject("barBtnPrint.Glyph")));
            this.barBtnPrint.Id = 27;
            this.barBtnPrint.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.P));
            this.barBtnPrint.Name = "barBtnPrint";
            this.barBtnPrint.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnPrint.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnPrint_ItemClick);
            // 
            // barBtnPreview
            // 
            this.barBtnPreview.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnPreview, "barBtnPreview");
            this.barBtnPreview.Glyph = ((System.Drawing.Image)(resources.GetObject("barBtnPreview.Glyph")));
            this.barBtnPreview.Id = 1;
            this.barBtnPreview.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.O));
            this.barBtnPreview.Name = "barBtnPreview";
            this.barBtnPreview.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnPreview.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Preview_ItemClick);
            // 
            // barBtnRefresh
            // 
            this.barBtnRefresh.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnRefresh, "barBtnRefresh");
            this.barBtnRefresh.Glyph = ((System.Drawing.Image)(resources.GetObject("barBtnRefresh.Glyph")));
            this.barBtnRefresh.Id = 28;
            this.barBtnRefresh.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.R));
            this.barBtnRefresh.Name = "barBtnRefresh";
            this.barBtnRefresh.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnRefresh.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnRefresh_ItemClick);
            // 
            // barBtnClose
            // 
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Glyph = ((System.Drawing.Image)(resources.GetObject("barBtnClose.Glyph")));
            this.barBtnClose.Id = 25;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Close_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // grdCategory
            // 
            resources.ApplyResources(this.grdCategory, "grdCategory");
            this.grdCategory.MainView = this.gridView1;
            this.grdCategory.Name = "grdCategory";
            this.grdCategory.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.rep_Ratio,
            this.rep_Currency});
            this.grdCategory.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // gridView1
            // 
            this.gridView1.Appearance.FooterPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.GroupPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.GroupPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.GroupRow.Options.UseTextOptions = true;
            this.gridView1.Appearance.GroupRow.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.HeaderPanel.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridView1.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView1.Appearance.Row.Options.UseTextOptions = true;
            this.gridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.Row.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridView1.Appearance.Row.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView1.AppearancePrint.FooterPanel.BackColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.BackColor")));
            this.gridView1.AppearancePrint.FooterPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.BorderColor")));
            this.gridView1.AppearancePrint.FooterPanel.Font = ((System.Drawing.Font)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.Font")));
            this.gridView1.AppearancePrint.FooterPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.ForeColor")));
            this.gridView1.AppearancePrint.FooterPanel.Options.UseBackColor = true;
            this.gridView1.AppearancePrint.FooterPanel.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.FooterPanel.Options.UseFont = true;
            this.gridView1.AppearancePrint.FooterPanel.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.FooterPanel.Options.UseTextOptions = true;
            this.gridView1.AppearancePrint.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.AppearancePrint.GroupFooter.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.BorderColor")));
            this.gridView1.AppearancePrint.GroupFooter.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.ForeColor")));
            this.gridView1.AppearancePrint.GroupFooter.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.GroupFooter.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.GroupFooter.Options.UseTextOptions = true;
            this.gridView1.AppearancePrint.GroupFooter.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.AppearancePrint.GroupRow.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupRow.BorderColor")));
            this.gridView1.AppearancePrint.GroupRow.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupRow.ForeColor")));
            this.gridView1.AppearancePrint.GroupRow.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.GroupRow.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.GroupRow.Options.UseTextOptions = true;
            this.gridView1.AppearancePrint.GroupRow.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.AppearancePrint.HeaderPanel.BackColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.BackColor")));
            this.gridView1.AppearancePrint.HeaderPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.BorderColor")));
            this.gridView1.AppearancePrint.HeaderPanel.Font = ((System.Drawing.Font)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.Font")));
            this.gridView1.AppearancePrint.HeaderPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.ForeColor")));
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseBackColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseFont = true;
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.AppearancePrint.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.AppearancePrint.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView1.AppearancePrint.Lines.BackColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Lines.BackColor")));
            this.gridView1.AppearancePrint.Lines.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Lines.ForeColor")));
            this.gridView1.AppearancePrint.Lines.Options.UseBackColor = true;
            this.gridView1.AppearancePrint.Lines.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.Row.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Row.BorderColor")));
            this.gridView1.AppearancePrint.Row.Font = ((System.Drawing.Font)(resources.GetObject("gridView1.AppearancePrint.Row.Font")));
            this.gridView1.AppearancePrint.Row.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Row.ForeColor")));
            this.gridView1.AppearancePrint.Row.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.Row.Options.UseFont = true;
            this.gridView1.AppearancePrint.Row.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.Row.Options.UseTextOptions = true;
            this.gridView1.AppearancePrint.Row.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView1.ColumnPanelRowHeight = 40;
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_Customer,
            this.col_SoldTotalPrice,
            this.col_ReturnTotalPrice,
            this.col_NetTotalPrice_Local,
            this.col_DiffNet,
            this.col_ReturnNetPrice,
            this.col_SoldNetPrice});
            this.gridView1.GridControl = this.grdCategory;
            this.gridView1.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary"))), resources.GetString("gridView1.GroupSummary1"), ((DevExpress.XtraGrid.Columns.GridColumn)(resources.GetObject("gridView1.GroupSummary2"))), resources.GetString("gridView1.GroupSummary3")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary4"))), resources.GetString("gridView1.GroupSummary5"), ((DevExpress.XtraGrid.Columns.GridColumn)(resources.GetObject("gridView1.GroupSummary6"))), resources.GetString("gridView1.GroupSummary7"))});
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView1.OptionsSelection.MultiSelect = true;
            this.gridView1.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView1.OptionsView.ShowAutoFilterRow = true;
            this.gridView1.OptionsView.ShowFooter = true;
            this.gridView1.OptionsView.ShowIndicator = false;
            // 
            // col_Customer
            // 
            resources.ApplyResources(this.col_Customer, "col_Customer");
            this.col_Customer.FieldName = "customer";
            this.col_Customer.Name = "col_Customer";
            // 
            // col_SoldTotalPrice
            // 
            resources.ApplyResources(this.col_SoldTotalPrice, "col_SoldTotalPrice");
            this.col_SoldTotalPrice.FieldName = "SoldTotalPrice";
            this.col_SoldTotalPrice.Name = "col_SoldTotalPrice";
            this.col_SoldTotalPrice.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_SoldTotalPrice.Summary"))), resources.GetString("col_SoldTotalPrice.Summary1"), resources.GetString("col_SoldTotalPrice.Summary2"))});
            // 
            // col_ReturnTotalPrice
            // 
            resources.ApplyResources(this.col_ReturnTotalPrice, "col_ReturnTotalPrice");
            this.col_ReturnTotalPrice.FieldName = "ReturnTotalPrice";
            this.col_ReturnTotalPrice.Name = "col_ReturnTotalPrice";
            this.col_ReturnTotalPrice.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_ReturnTotalPrice.Summary"))), resources.GetString("col_ReturnTotalPrice.Summary1"), resources.GetString("col_ReturnTotalPrice.Summary2"))});
            // 
            // col_NetTotalPrice_Local
            // 
            resources.ApplyResources(this.col_NetTotalPrice_Local, "col_NetTotalPrice_Local");
            this.col_NetTotalPrice_Local.FieldName = "NetTotalPrice_Local";
            this.col_NetTotalPrice_Local.Name = "col_NetTotalPrice_Local";
            this.col_NetTotalPrice_Local.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_NetTotalPrice_Local.Summary"))), resources.GetString("col_NetTotalPrice_Local.Summary1"), resources.GetString("col_NetTotalPrice_Local.Summary2"))});
            this.col_NetTotalPrice_Local.UnboundExpression = "[SoldTotalPrice] - [ReturnTotalPrice]";
            this.col_NetTotalPrice_Local.UnboundType = DevExpress.Data.UnboundColumnType.Decimal;
            // 
            // col_DiffNet
            // 
            resources.ApplyResources(this.col_DiffNet, "col_DiffNet");
            this.col_DiffNet.FieldName = "DiffNet";
            this.col_DiffNet.Name = "col_DiffNet";
            this.col_DiffNet.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_DiffNet.Summary"))), resources.GetString("col_DiffNet.Summary1"), resources.GetString("col_DiffNet.Summary2"))});
            this.col_DiffNet.UnboundExpression = "[SoldNetPrice] - [ReturnNetPrice]";
            this.col_DiffNet.UnboundType = DevExpress.Data.UnboundColumnType.Decimal;
            // 
            // col_ReturnNetPrice
            // 
            resources.ApplyResources(this.col_ReturnNetPrice, "col_ReturnNetPrice");
            this.col_ReturnNetPrice.FieldName = "ReturnNetPrice";
            this.col_ReturnNetPrice.Name = "col_ReturnNetPrice";
            this.col_ReturnNetPrice.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_ReturnNetPrice.Summary"))), resources.GetString("col_ReturnNetPrice.Summary1"), resources.GetString("col_ReturnNetPrice.Summary2"))});
            // 
            // col_SoldNetPrice
            // 
            resources.ApplyResources(this.col_SoldNetPrice, "col_SoldNetPrice");
            this.col_SoldNetPrice.FieldName = "SoldNetPrice";
            this.col_SoldNetPrice.Name = "col_SoldNetPrice";
            this.col_SoldNetPrice.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_SoldNetPrice.Summary"))), resources.GetString("col_SoldNetPrice.Summary1"), resources.GetString("col_SoldNetPrice.Summary2"))});
            // 
            // rep_Ratio
            // 
            resources.ApplyResources(this.rep_Ratio, "rep_Ratio");
            this.rep_Ratio.Mask.EditMask = resources.GetString("rep_Ratio.Mask.EditMask");
            this.rep_Ratio.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("rep_Ratio.Mask.MaskType")));
            this.rep_Ratio.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("rep_Ratio.Mask.UseMaskAsDisplayFormat")));
            this.rep_Ratio.Name = "rep_Ratio";
            // 
            // rep_Currency
            // 
            resources.ApplyResources(this.rep_Currency, "rep_Currency");
            this.rep_Currency.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Currency.Buttons"))))});
            this.rep_Currency.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_Currency.Columns"), resources.GetString("rep_Currency.Columns1")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_Currency.Columns2"), resources.GetString("rep_Currency.Columns3"), ((int)(resources.GetObject("rep_Currency.Columns4"))), ((DevExpress.Utils.FormatType)(resources.GetObject("rep_Currency.Columns5"))), resources.GetString("rep_Currency.Columns6"), ((bool)(resources.GetObject("rep_Currency.Columns7"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("rep_Currency.Columns8"))))});
            this.rep_Currency.Name = "rep_Currency";
            // 
            // picLogo
            // 
            resources.ApplyResources(this.picLogo, "picLogo");
            this.picLogo.MenuManager = this.barManager1;
            this.picLogo.Name = "picLogo";
            this.picLogo.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Stretch;
            // 
            // lblReportName
            // 
            resources.ApplyResources(this.lblReportName, "lblReportName");
            this.lblReportName.MenuManager = this.barManager1;
            this.lblReportName.Name = "lblReportName";
            this.lblReportName.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("lblReportName.Properties.Appearance.Font")));
            this.lblReportName.Properties.Appearance.Options.UseFont = true;
            this.lblReportName.Properties.Appearance.Options.UseTextOptions = true;
            this.lblReportName.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            // 
            // lblDateFilter
            // 
            resources.ApplyResources(this.lblDateFilter, "lblDateFilter");
            this.lblDateFilter.MenuManager = this.barManager1;
            this.lblDateFilter.Name = "lblDateFilter";
            this.lblDateFilter.Properties.Appearance.Options.UseTextOptions = true;
            this.lblDateFilter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            // 
            // lblFilter
            // 
            resources.ApplyResources(this.lblFilter, "lblFilter");
            this.lblFilter.MenuManager = this.barManager1;
            this.lblFilter.Name = "lblFilter";
            this.lblFilter.Properties.Appearance.Options.UseTextOptions = true;
            this.lblFilter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            // 
            // frm_SL_ItemsNetSalesDetails
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.picLogo);
            this.Controls.Add(this.grdCategory);
            this.Controls.Add(this.lblFilter);
            this.Controls.Add(this.lblDateFilter);
            this.Controls.Add(this.lblReportName);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.KeyPreview = true;
            this.Name = "frm_SL_ItemsNetSalesDetails";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_Rep_FormClosing);
            this.Load += new System.EventHandler(this.frm_SL_InvoiceList_Load);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdCategory)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Ratio)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Currency)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.picLogo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblReportName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDateFilter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFilter.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnPreview;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraGrid.GridControl grdCategory;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraBars.BarButtonItem barBtnPrint;
        private DevExpress.XtraEditors.TextEdit lblDateFilter;
        private DevExpress.XtraEditors.TextEdit lblReportName;
        private DevExpress.XtraEditors.PictureEdit picLogo;
        private DevExpress.XtraEditors.TextEdit lblFilter;
        private DevExpress.XtraBars.BarButtonItem barBtnRefresh;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit rep_Ratio;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit rep_Currency;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn col_Customer;
        private DevExpress.XtraGrid.Columns.GridColumn col_ReturnTotalPrice;
        private DevExpress.XtraGrid.Columns.GridColumn col_NetTotalPrice_Local;
        private DevExpress.XtraGrid.Columns.GridColumn col_SoldTotalPrice;
        private DevExpress.XtraGrid.Columns.GridColumn col_DiffNet;
        private DevExpress.XtraGrid.Columns.GridColumn col_ReturnNetPrice;
        private DevExpress.XtraGrid.Columns.GridColumn col_SoldNetPrice;
    }
}