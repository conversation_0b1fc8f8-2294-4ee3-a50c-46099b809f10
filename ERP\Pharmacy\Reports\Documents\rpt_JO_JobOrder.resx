<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="xrTable2.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="xrTable2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 0</value>
  </data>
  <data name="xrTableRow2.BackColor" type="System.Drawing.Color, System.Drawing">
    <value />
  </data>
  <data name="cell_EnCaption.Text" xml:space="preserve">
    <value>عنوان أجنبي</value>
  </data>
  <data name="cell_EnCaption.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleLeft</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="cell_EnCaption.Weight" type="System.Double, mscorlib">
    <value>0.49415549795136193</value>
  </data>
  <data name="cell_EnText.Text" xml:space="preserve">
    <value>التفاصيل ج</value>
  </data>
  <data name="cell_EnText.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleLeft</value>
  </data>
  <data name="cell_EnText.Weight" type="System.Double, mscorlib">
    <value>0.57192276088336047</value>
  </data>
  <data name="cell_ArText.Text" xml:space="preserve">
    <value>التفاصيل ع</value>
  </data>
  <data name="cell_ArText.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="cell_ArText.Weight" type="System.Double, mscorlib">
    <value>0.63573483474381964</value>
  </data>
  <data name="cell_ArCaption.Text" xml:space="preserve">
    <value>عنوان عربي</value>
  </data>
  <data name="cell_ArCaption.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="cell_ArCaption.Weight" type="System.Double, mscorlib">
    <value>0.54818690642145762</value>
  </data>
  <data name="xrTableRow2.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt, style=Bold</value>
  </data>
  <data name="xrTableRow2.Weight" type="System.Double, mscorlib">
    <value>0.54901959587545957</value>
  </data>
  <data name="xrTable2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>786, 70.00002</value>
  </data>
  <data name="xrTable2.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="Detail.HeightF" type="System.Single, mscorlib">
    <value>71</value>
  </data>
  <data name="Detail.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="xrLabel23.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel23.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>230.5, 50</value>
  </data>
  <data name="xrLabel23.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>117.7085, 24.49998</value>
  </data>
  <data name="xrLabel23.Text" xml:space="preserve">
    <value>Job Order No</value>
  </data>
  <data name="xrLabel23.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="xrLabel21.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel21.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>9.999998, 216</value>
  </data>
  <data name="xrLabel21.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>74.99994, 24.49999</value>
  </data>
  <data name="xrLabel21.Text" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="xrLabel21.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="xrLabel12.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel12.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>368.458, 216</value>
  </data>
  <data name="xrLabel12.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>97.41708, 24.49998</value>
  </data>
  <data name="xrLabel12.Text" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="xrLabel12.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="xrLabel18.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel18.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>368.458, 139.5</value>
  </data>
  <data name="xrLabel18.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>97.41708, 24.49998</value>
  </data>
  <data name="xrLabel18.Text" xml:space="preserve">
    <value>Sales</value>
  </data>
  <data name="xrLabel18.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="lbl_salesEmp.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lbl_salesEmp.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>467.3333, 139.5</value>
  </data>
  <data name="lbl_salesEmp.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>235.5415, 24.49998</value>
  </data>
  <data name="lbl_salesEmp.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="xrLabel5.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel5.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>703.8749, 139.5</value>
  </data>
  <data name="xrLabel5.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>78.12512, 24.49998</value>
  </data>
  <data name="xrLabel5.Text" xml:space="preserve">
    <value>المبيعات</value>
  </data>
  <data name="xrLabel5.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="xrLabel22.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel22.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>13.41708, 165</value>
  </data>
  <data name="xrLabel22.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>74.99994, 24.49999</value>
  </data>
  <data name="xrLabel22.Text" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="xrLabel22.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="lbl_status.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lbl_status.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>88.87483, 165</value>
  </data>
  <data name="lbl_status.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>134.8335, 24.49998</value>
  </data>
  <data name="lbl_status.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="xrLabel20.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel20.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>224.7083, 165</value>
  </data>
  <data name="xrLabel20.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>55.20836, 24.49998</value>
  </data>
  <data name="xrLabel20.Text" xml:space="preserve">
    <value>الحالة</value>
  </data>
  <data name="xrLabel20.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="xrLabel19.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel19.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>13.41708, 139.4999</value>
  </data>
  <data name="xrLabel19.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>74.99994, 24.49999</value>
  </data>
  <data name="xrLabel19.Text" xml:space="preserve">
    <value>Dept</value>
  </data>
  <data name="xrLabel19.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="lbl_dept.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lbl_dept.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>88.87483, 139.4999</value>
  </data>
  <data name="lbl_dept.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>134.8335, 24.49998</value>
  </data>
  <data name="lbl_dept.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="xrLabel17.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel17.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>224.7083, 139.4999</value>
  </data>
  <data name="xrLabel17.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>55.20836, 24.49998</value>
  </data>
  <data name="xrLabel17.Text" xml:space="preserve">
    <value>الجهة</value>
  </data>
  <data name="xrLabel17.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="xrLabel16.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel16.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>368.458, 165</value>
  </data>
  <data name="xrLabel16.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>97.41708, 24.49998</value>
  </data>
  <data name="xrLabel16.Text" xml:space="preserve">
    <value>Priority</value>
  </data>
  <data name="xrLabel16.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="xrLabel15.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel15.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>13.41708, 114</value>
  </data>
  <data name="xrLabel15.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>74.99994, 24.49999</value>
  </data>
  <data name="xrLabel15.Text" xml:space="preserve">
    <value>Delivery</value>
  </data>
  <data name="xrLabel15.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="xrLabel14.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel14.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>13.62533, 88.49998</value>
  </data>
  <data name="xrLabel14.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>74.79169, 24.49999</value>
  </data>
  <data name="xrLabel14.Text" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="xrLabel14.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="xrLabel13.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel13.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>368.458, 190.5</value>
  </data>
  <data name="xrLabel13.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>97.41708, 24.49998</value>
  </data>
  <data name="xrLabel13.Text" xml:space="preserve">
    <value>Job</value>
  </data>
  <data name="xrLabel13.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="xrLabel11.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel11.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>368.458, 114</value>
  </data>
  <data name="xrLabel11.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>97.41708, 24.49998</value>
  </data>
  <data name="xrLabel11.Text" xml:space="preserve">
    <value>Mrs</value>
  </data>
  <data name="xrLabel11.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="xrLabel9.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel9.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>368.458, 88.5</value>
  </data>
  <data name="xrLabel9.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>97.41708, 24.49998</value>
  </data>
  <data name="xrLabel9.Text" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="xrLabel9.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="lbl_Priority.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lbl_Priority.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>467.3332, 165</value>
  </data>
  <data name="lbl_Priority.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>235.5415, 24.49998</value>
  </data>
  <data name="lbl_Priority.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="xrLabel3.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel3.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>703.8748, 165</value>
  </data>
  <data name="xrLabel3.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>78.12512, 24.49998</value>
  </data>
  <data name="xrLabel3.Text" xml:space="preserve">
    <value>الأهمية</value>
  </data>
  <data name="xrLabel3.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="xrLabel10.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel10.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>703.8748, 190.5</value>
  </data>
  <data name="xrLabel10.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>78.12518, 24.49998</value>
  </data>
  <data name="xrLabel10.Text" xml:space="preserve">
    <value>العمل</value>
  </data>
  <data name="xrLabel10.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="lbl_Job.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lbl_Job.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>467.3332, 190.5</value>
  </data>
  <data name="lbl_Job.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>235.5415, 24.49998</value>
  </data>
  <data name="lbl_Job.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="lbl_Attn.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lbl_Attn.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>467.3333, 114</value>
  </data>
  <data name="lbl_Attn.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>235.5415, 24.49998</value>
  </data>
  <data name="lbl_Attn.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="xrLabel2.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>703.8748, 114</value>
  </data>
  <data name="xrLabel2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>78.12518, 24.49998</value>
  </data>
  <data name="xrLabel2.Text" xml:space="preserve">
    <value>عناية السيد</value>
  </data>
  <data name="xrLabel2.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="lbl_User.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="lbl_User.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>89.58334, 216</value>
  </data>
  <data name="lbl_User.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>134.125, 24.49998</value>
  </data>
  <data name="lbl_User.Text" xml:space="preserve">
    <value>..</value>
  </data>
  <data name="lbl_User.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="xrLabel6.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrLabel6.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>224.7083, 216</value>
  </data>
  <data name="xrLabel6.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>86.4585, 24.49998</value>
  </data>
  <data name="xrLabel6.Text" xml:space="preserve">
    <value>اسم المستخدم</value>
  </data>
  <data name="xrLabel6.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="lbl_notes.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lbl_notes.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>467.3333, 216</value>
  </data>
  <data name="lbl_notes.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>236.5417, 48.12495</value>
  </data>
  <data name="lbl_notes.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="xrLabel4.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel4.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>703.8749, 216</value>
  </data>
  <data name="xrLabel4.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>78.12512, 24.49998</value>
  </data>
  <data name="xrLabel4.Text" xml:space="preserve">
    <value>ملاحظات</value>
  </data>
  <data name="xrLabel4.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="lbl_date.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lbl_date.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>88.87483, 88.49998</value>
  </data>
  <data name="lbl_date.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>134.8335, 24.49999</value>
  </data>
  <data name="lbl_date.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="lbl_dueDate.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lbl_dueDate.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>88.87483, 114</value>
  </data>
  <data name="lbl_dueDate.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>134.8335, 24.49998</value>
  </data>
  <data name="lbl_dueDate.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="xrLabel7.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel7.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>224.7083, 88.49998</value>
  </data>
  <data name="xrLabel7.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>55.20836, 24.49999</value>
  </data>
  <data name="xrLabel7.Text" xml:space="preserve">
    <value>التاريخ</value>
  </data>
  <data name="xrLabel7.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="xrLabel8.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel8.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>224.7083, 114</value>
  </data>
  <data name="xrLabel8.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>55.20836, 24.49998</value>
  </data>
  <data name="xrLabel8.Text" xml:space="preserve">
    <value>التسليم</value>
  </data>
  <data name="xrLabel8.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="lbl_Number.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lbl_Number.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>350, 50</value>
  </data>
  <data name="lbl_Number.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>96.87521, 24.49998</value>
  </data>
  <data name="lbl_Number.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="lbl_cust.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lbl_cust.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>467.3333, 88.5</value>
  </data>
  <data name="lbl_cust.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>235.5415, 24.49998</value>
  </data>
  <data name="lbl_cust.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="xrLabel1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>703.8748, 88.5</value>
  </data>
  <data name="xrLabel1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>78.12518, 24.49998</value>
  </data>
  <data name="xrLabel1.Text" xml:space="preserve">
    <value>العميل</value>
  </data>
  <data name="xrLabel1.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="lblReportName.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lblReportName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>447, 50</value>
  </data>
  <data name="lblReportName.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>96.87518, 24.49998</value>
  </data>
  <data name="lblReportName.Text" xml:space="preserve">
    <value>أمر عمل رقم</value>
  </data>
  <data name="lblReportName.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="picLogo.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>14.58333, 10.00001</value>
  </data>
  <data name="picLogo.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>70, 70</value>
  </data>
  <data name="lblCompName.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 18pt</value>
  </data>
  <data name="lblCompName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>89.58334, 10.00001</value>
  </data>
  <data name="lblCompName.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>600.0001, 30</value>
  </data>
  <data name="lblCompName.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopCenter</value>
  </data>
  <data name="TopMargin.HeightF" type="System.Single, mscorlib">
    <value>274</value>
  </data>
  <data name="TopMargin.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="xrPageInfo1.Format" xml:space="preserve">
    <value>Page {0} of {1} </value>
  </data>
  <data name="xrPageInfo1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>337.5, 12.5</value>
  </data>
  <data name="xrPageInfo1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>109.375, 23</value>
  </data>
  <data name="xrPageInfo1.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="BottomMargin.HeightF" type="System.Single, mscorlib">
    <value>45</value>
  </data>
  <data name="BottomMargin.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="xrTable1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="xrTable1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 0</value>
  </data>
  <data name="xrTableRow1.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Moccasin</value>
  </data>
  <data name="xrTableCell9.Weight" type="System.Double, mscorlib">
    <value>0.49415549795136193</value>
  </data>
  <data name="xrTableCell4.Text" xml:space="preserve">
    <value>التفاصيل ج</value>
  </data>
  <data name="xrTableCell4.Weight" type="System.Double, mscorlib">
    <value>0.57192276088336047</value>
  </data>
  <data name="xrTableCell10.Text" xml:space="preserve">
    <value>التفاصيل ع</value>
  </data>
  <data name="xrTableCell10.Weight" type="System.Double, mscorlib">
    <value>0.63573483474381964</value>
  </data>
  <data name="xrTableCell8.Weight" type="System.Double, mscorlib">
    <value>0.54818690642145762</value>
  </data>
  <data name="xrTableRow1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrTableRow1.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="xrTable1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>786, 53.125</value>
  </data>
  <data name="xrTable1.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="PageHeader.HeightF" type="System.Single, mscorlib">
    <value>53.125</value>
  </data>
  <data name="lbl_salesEmp_JobEn.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt</value>
  </data>
  <data name="lbl_salesEmp_JobEn.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>359.166, 0</value>
  </data>
  <data name="lbl_salesEmp_JobEn.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>106.7085, 16.49998</value>
  </data>
  <data name="lbl_salesEmp_JobEn.Text" xml:space="preserve">
    <value>lbl_salesEmp_JobEn</value>
  </data>
  <data name="lbl_salesEmp_JobEn.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="lbl_salesEmp_JobEn.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_salesEmp_JobAr.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt</value>
  </data>
  <data name="lbl_salesEmp_JobAr.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>465.8745, 0</value>
  </data>
  <data name="lbl_salesEmp_JobAr.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>106.7085, 16.49998</value>
  </data>
  <data name="lbl_salesEmp_JobAr.Text" xml:space="preserve">
    <value>lbl_salesEmp_JobAr</value>
  </data>
  <data name="lbl_salesEmp_JobAr.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="lbl_salesEmp_JobAr.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_salesEmpEn.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt</value>
  </data>
  <data name="lbl_salesEmpEn.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>572.583, 0</value>
  </data>
  <data name="lbl_salesEmpEn.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>106.7085, 16.49998</value>
  </data>
  <data name="lbl_salesEmpEn.Text" xml:space="preserve">
    <value>lbl_salesEmpEn</value>
  </data>
  <data name="lbl_salesEmpEn.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="lbl_salesEmpEn.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_CustomerEn.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt</value>
  </data>
  <data name="lbl_CustomerEn.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>679.2915, 0</value>
  </data>
  <data name="lbl_CustomerEn.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>106.7085, 16.49998</value>
  </data>
  <data name="lbl_CustomerEn.Text" xml:space="preserve">
    <value>lbl_CustomerEn</value>
  </data>
  <data name="lbl_CustomerEn.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="lbl_CustomerEn.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="ReportFooter.HeightF" type="System.Single, mscorlib">
    <value>24.49998</value>
  </data>
  <metadata name="backgroundWorker1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="xrTable4.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="xrTable4.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 0</value>
  </data>
  <data name="xrTableRow4.BackColor" type="System.Drawing.Color, System.Drawing">
    <value />
  </data>
  <data name="cell_Total.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="cell_Total.Text" xml:space="preserve">
    <value>الاجمالي</value>
  </data>
  <data name="cell_Total.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="cell_Total.Weight" type="System.Double, mscorlib">
    <value>0.2921051470164624</value>
  </data>
  <data name="cell_Disc.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="cell_Disc.Text" xml:space="preserve">
    <value>قيمة خصم</value>
  </data>
  <data name="cell_Disc.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="cell_Disc.Weight" type="System.Double, mscorlib">
    <value>0.21827298036155546</value>
  </data>
  <data name="cell_Price.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="cell_Price.Text" xml:space="preserve">
    <value>السعر</value>
  </data>
  <data name="cell_Price.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="cell_Price.Weight" type="System.Double, mscorlib">
    <value>0.29582313270640603</value>
  </data>
  <data name="cell_Qty.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="cell_Qty.Text" xml:space="preserve">
    <value>كمية</value>
  </data>
  <data name="cell_Qty.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="cell_Qty.Weight" type="System.Double, mscorlib">
    <value>0.2941101794815652</value>
  </data>
  <data name="cell_UOM.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="cell_UOM.Text" xml:space="preserve">
    <value>وحدة قياس</value>
  </data>
  <data name="cell_UOM.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="cell_UOM.Weight" type="System.Double, mscorlib">
    <value>0.26633982490083141</value>
  </data>
  <data name="cell_ItemName.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="cell_ItemName.Text" xml:space="preserve">
    <value>اســـم الصنف</value>
  </data>
  <data name="cell_ItemName.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="cell_ItemName.Weight" type="System.Double, mscorlib">
    <value>0.62249732429428317</value>
  </data>
  <data name="cell_code.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="cell_code.Text" xml:space="preserve">
    <value>كود</value>
  </data>
  <data name="cell_code.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="cell_code.Weight" type="System.Double, mscorlib">
    <value>0.12857687506459745</value>
  </data>
  <data name="xrTableRow4.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt, style=Bold</value>
  </data>
  <data name="xrTableRow4.Weight" type="System.Double, mscorlib">
    <value>0.54901959587545957</value>
  </data>
  <data name="xrTable4.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>786.000061, 29.16667</value>
  </data>
  <data name="xrTable4.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="Detail1.HeightF" type="System.Single, mscorlib">
    <value>29.16667</value>
  </data>
  <data name="xrTable3.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="xrTable3.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 26.0416489</value>
  </data>
  <data name="tableCell1.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="tableCell1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14.25pt, style=Bold</value>
  </data>
  <data name="tableCell1.Text" xml:space="preserve">
    <value>Items الاصناف</value>
  </data>
  <data name="tableCell1.Weight" type="System.Double, mscorlib">
    <value>2.1347400296713905</value>
  </data>
  <data name="tableRow1.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="xrTableRow3.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Moccasin</value>
  </data>
  <data name="xrTableCell1.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="xrTableCell1.Text" xml:space="preserve">
    <value>الاجمالي</value>
  </data>
  <data name="xrTableCell1.Weight" type="System.Double, mscorlib">
    <value>0.294452000674701</value>
  </data>
  <data name="xrTableCell2.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="xrTableCell2.Text" xml:space="preserve">
    <value>قيمة خصم</value>
  </data>
  <data name="xrTableCell2.Weight" type="System.Double, mscorlib">
    <value>0.21974292694909528</value>
  </data>
  <data name="xrTableCell7.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="xrTableCell7.Text" xml:space="preserve">
    <value>السعر</value>
  </data>
  <data name="xrTableCell7.Weight" type="System.Double, mscorlib">
    <value>0.29852093872785157</value>
  </data>
  <data name="xrTableCell6.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="xrTableCell6.Text" xml:space="preserve">
    <value>كمية</value>
  </data>
  <data name="xrTableCell6.Weight" type="System.Double, mscorlib">
    <value>0.29643582512191319</value>
  </data>
  <data name="xrTableCell5.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="xrTableCell5.Text" xml:space="preserve">
    <value>وحدة قياس</value>
  </data>
  <data name="xrTableCell5.Weight" type="System.Double, mscorlib">
    <value>0.26847979467140126</value>
  </data>
  <data name="xrTableCell3.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="xrTableCell3.Text" xml:space="preserve">
    <value>اســـم الصنف</value>
  </data>
  <data name="xrTableCell3.Weight" type="System.Double, mscorlib">
    <value>0.62749876489076273</value>
  </data>
  <data name="xrTableCell11.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="xrTableCell11.Text" xml:space="preserve">
    <value>كود</value>
  </data>
  <data name="xrTableCell11.Weight" type="System.Double, mscorlib">
    <value>0.12960977863566553</value>
  </data>
  <data name="xrTableRow3.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrTableRow3.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="xrTable3.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>786.000061, 72.25</value>
  </data>
  <data name="xrTable3.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="ReportHeader.HeightF" type="System.Single, mscorlib">
    <value>98.29165</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.Margins" type="System.Drawing.Printing.Margins, System.Drawing">
    <value>19, 22, 274, 45</value>
  </data>
  <data name="$this.PageHeight" type="System.Int32, mscorlib">
    <value>1169</value>
  </data>
  <data name="$this.PageWidth" type="System.Int32, mscorlib">
    <value>827</value>
  </data>
  <data name="$this.PaperKind" type="System.Drawing.Printing.PaperKind, System.Drawing">
    <value>A4</value>
  </data>
</root>