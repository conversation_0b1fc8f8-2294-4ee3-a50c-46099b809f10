﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraPrinting;
using DAL;
using DAL.Res;
using DevExpress.XtraReports.UI;
using System.Net.Http;
using Models_1.ViewModels;
using System.Threading.Tasks;

namespace Pharmacy.Forms
{
    public partial class frm_SL_Return : DevExpress.XtraEditors.XtraForm
    {
        System.IO.MemoryStream rep_layout = new System.IO.MemoryStream();
        List<string> tableTax = new List<string>();
        UserPriv prvlg;
        bool DataModified;
        List<IC_Store> stores_table;
        List<DAL.IC_UOM> uom_list;
        List<SL_Customer_Info> lst_Customers = new List<SL_Customer_Info>();
        List<VendorInfo> lst_Vendors = new List<VendorInfo>();
        List<ItemLkp> lstItems = new List<ItemLkp>();
        int SelectedInvId = 0;
        DataTable dtSLReturn_Details = new DataTable();
        DataTable dtCompanies = new DataTable();
        DataTable dtUOM = new DataTable();
        DataTable dt_SalesEmps = new DataTable();
        DataTable dt_Multi_CC = new DataTable();
        public static DataTable Dt_Rows = new DataTable();
        DataTable dtPayAccounts = new DataTable();
        int rowhandle = 0;
        List<IC_Category> lst_Cat = new List<IC_Category>();
        static int discountTaxId = 0;
        decimal taxValue = 0;
        // Adel:add Multiple Datatable
        public static DataTable Multiple_Data = new DataTable();

        DAL.ERPDataContext DB;

        byte defaultRoundingPoints;
        public int invoiceId = 0;
        int userId = 0;
        int customerId = 0;
        decimal totalTaxRatio = 0;
        int SrcProcessId;
        int SourceId;
        string sourceCode;
        private string uUId;
        DAL.IC_PriceLevel CustomerPriceLevel = null;
        DataTable dt_SubTax = new DataTable();
        bool? IsTaxable = null;

        public frm_SL_Return()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
        }

        public frm_SL_Return(int invoiceId)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            this.invoiceId = invoiceId;
        }

        public frm_SL_Return(int invoiceId, int CustomerId)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            this.invoiceId = invoiceId;
            this.customerId = CustomerId;
        }

        public frm_SL_Return(int SrcProcessId, int SourceId, string sourceCode)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

        }

        private void frm_SL_Return_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.PageUp)
            {
                btnPrevious.PerformClick();
            }
            if (e.KeyCode == Keys.PageDown)
            {
                btnNext.PerformClick();
            }

            if (e.KeyCode == Keys.Home && e.Modifiers == Keys.Control)
            {
                lkp_Customers.Focus();
            }
            if (e.KeyCode == Keys.Insert)
            {
                txtNotes.Focus();
                FocusItemCode1(true);
            }
            if (e.KeyCode == Keys.End && e.Modifiers == Keys.Control)
            {
                txtDiscountRatio.Focus();
            }
        }

        private void frm_SL_Return_Load(object sender, EventArgs e)
        {
            DB = new DAL.ERPDataContext();
            discountTaxId = DB.E_TaxableTypes.Where(a => a.Code == "T4").FirstOrDefault().E_TaxableTypeId;
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            LoadPrivilege();
            defaultRoundingPoints = Shared.st_Store.DefaultRoundingPoints ?? 4;
            lkp_Customers.Properties.View.Columns["CusNameAr"].OptionsFilter.AutoFilterCondition = AutoFilterCondition.Contains;

            //ErpUtils.Allow_Incremental_Search(repItems);
            if (Shared.user.UseContainsToSearchItems)
                repItems.PopupFilterMode = PopupFilterMode.Contains;
            else
                repItems.PopupFilterMode = PopupFilterMode.Default;
            //ErpUtils.Allow_Incremental_Search(repUOM);
            //ErpUtils.Allow_Incremental_Search(lkp_Customers);
            lkp_Drawers.Properties.TextEditStyle = lkp_Drawers2.Properties.TextEditStyle =
                lkpStore.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            repItems.View.Columns["ItemNameAr"].OptionsFilter.AutoFilterCondition = AutoFilterCondition.Contains;
            repItems.View.Columns["ItemNameEn"].OptionsFilter.AutoFilterCondition = AutoFilterCondition.Contains;
            repItems.View.Columns["ItemCode1"].SortIndex = 0;

            DB = new ERPDataContext();

            Reset();

            if (Shared.user.SL_Return_PayMethod == false)
            {
                groupControl1.Visible = false;
                cmbPayMethod.Properties.Items.RemoveAt(2);
                cmbPayMethod.Enabled = true;
                cmbPayMethod.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Default;
                cmbPayMethod.Properties.Buttons[0].Visible = true;
                lbl_Paid.Visible = txt_paid.Visible = lbl_remains.Visible = txt_Remains.Visible = false;
            }

            if (Shared.user.SL_Return_PayMethod == true &&
                Shared.user.UserEditTransactionDate == false)
                dtInvoiceDate.Enabled = false;

            if (Shared.InvoicePostToStore)
                chk_IsInTrns.Visible = false;

            #region Modules Available
            if (Shared.CurrencyAvailable == false)
                pnlCrncy.Visible = false;
            if (Shared.user.UserChangeCostCenterInInv == false)
                pnlCostCenter.Visible = false;
            if (Shared.ItemsPostingAvailable)
                txtExpenses.Enabled = false;

            if (Shared.LibraAvailabe)
            {
                gridView2.Columns["Qty"].OptionsColumn.ShowInCustomizationForm = false;
            }

            #endregion

            BindDataSources();
            LoadInvoice();


            repItems.CloseUp += new DevExpress.XtraEditors.Controls.CloseUpEventHandler(this.repItems_CloseUp);

            ErpUtils.Load_Grid_Layout(grdPrInvoice, this.Name.Replace("frm_", ""));
            ErpUtils.Load_MemoryStream_Layout(rep_layout, this.Name + ".repItems");

            ErpUtils.ColumnChooser(grdPrInvoice);

            #region Hide_Show_Columns
            if (!Shared.st_Store.UseHeightDimension)
                col_Height.OptionsColumn.ShowInCustomizationForm = col_Height.Visible = false;
            if (!Shared.st_Store.UseWidthDimension)
                col_Width.OptionsColumn.ShowInCustomizationForm = col_Width.Visible = false;
            if (!Shared.st_Store.UseLengthDimension)
                col_Length.OptionsColumn.ShowInCustomizationForm = col_Length.Visible = false;
            if (!Shared.st_Store.UseHeightDimension && !Shared.st_Store.UseWidthDimension && !Shared.st_Store.UseLengthDimension)
                col_TotalQty.OptionsColumn.ShowInCustomizationForm = col_TotalQty.Visible = false;
            if (!Shared.st_Store.PiecesCount)
                col_PiecesCount.OptionsColumn.ShowInCustomizationForm = col_PiecesCount.Visible = false;
            if (!Shared.st_Store.ExpireDate)
                col_Expire.OptionsColumn.ShowInCustomizationForm = col_Expire.Visible = false;
            if (!Shared.st_Store.Batch)
                col_Batch.OptionsColumn.ShowInCustomizationForm = col_Batch.Visible = false;
            if (Shared.user.HidePurchasePrice)
                colPurchasePrice.OptionsColumn.ShowInCustomizationForm = colPurchasePrice.Visible = false;
            if (Shared.user.HideItemDiscount)
            {
                gridView2.Columns["DiscountRatio"].OptionsColumn.ShowInCustomizationForm = gridView2.Columns["DiscountRatio"].Visible = false;
                gridView2.Columns["DiscountValue"].OptionsColumn.ShowInCustomizationForm = gridView2.Columns["DiscountValue"].Visible = false;
            }
            if (!Shared.TaxAvailable)
            {
                col_SalesTax.OptionsColumn.ShowInCustomizationForm = col_SalesTax.Visible = false;
            }

            if (!Shared.st_Store.Serial)
                col_Serial.OptionsColumn.ShowInCustomizationForm = col_Serial.Visible = false;

            col_Serial.Caption = Shared.IsEnglish ? Shared.st_Store.SerialNameEn : Shared.st_Store.SerialNameAr;
            col_Serial2.Caption = Shared.IsEnglish ? Shared.st_Store.Serial2NameEn : Shared.st_Store.Serial2NameAr;
            col_Batch.Caption = Shared.IsEnglish ? Shared.st_Store.BatchNameEn : Shared.st_Store.BatchNameAr;
            col_PiecesCount.Caption = Shared.IsEnglish ? Shared.st_Store.PiecesCountNameEn : Shared.st_Store.PiecesCountNameAr;
            #endregion

            if (Shared.st_Store.SalesDeductTaxAccount == null || Shared.TaxAvailable == false)
                txt_DeductTaxR.Enabled = txt_DeductTaxV.Enabled = false;

            if (Shared.st_Store.SalesAddTaxAccount == null || Shared.TaxAvailable == false)
                txt_AddTaxR.Enabled = txt_AddTaxV.Enabled = false;

            txt_TaxValue.BackColor = Color.White;
            txt_TaxValue.ForeColor = Color.DimGray;

            txtInvoiceCode.Leave += new EventHandler(txtInvoiceCode_Leave);
            if (Shared.E_invoiceAvailable)
            {
                colEtaxValue.Visible = false;
                col_ETaxRatio.Visible = false;
                colTaxType.Visible = false;
                TotalTaxes.Visible = false;
                totalTaxesRatio.Visible = false;
                btn_AddTaxes.Visible = false;
                col_TotalSubDiscountTax.Visible =
                col_TotalSubCustomTax.Visible =
                col_TotalSubAddTax.Visible =
                col_TaxValue.Visible = col_TotalSubDiscountTax.OptionsColumn.ShowInCustomizationForm =
                col_TotalSubCustomTax.OptionsColumn.ShowInCustomizationForm =
                col_TotalSubAddTax.OptionsColumn.ShowInCustomizationForm =
                col_TaxValue.OptionsColumn.ShowInCustomizationForm = true;


                colEtaxValue.OptionsColumn.ShowInCustomizationForm = false;
                col_ETaxRatio.OptionsColumn.ShowInCustomizationForm = false;
                colTaxType.OptionsColumn.ShowInCustomizationForm = false;
                TotalTaxes.OptionsColumn.ShowInCustomizationForm = false;
                totalTaxesRatio.OptionsColumn.ShowInCustomizationForm = false;
                btn_AddTaxes.OptionsColumn.ShowInCustomizationForm = false;
            }
            else
            {
                if (Shared.st_Store.E_AllowMoreThanTax == true)
                {
                    TotalTaxes.Visible = true;
                    // totalTaxesRatio.Visible = true;
                    btn_AddTaxes.Visible = true;

                    colEtaxValue.Visible = false;
                    col_ETaxRatio.Visible = false;
                    colTaxType.Visible = false;
                    colEtaxValue.OptionsColumn.ShowInCustomizationForm = false;
                    col_ETaxRatio.OptionsColumn.ShowInCustomizationForm = false;
                    colTaxType.OptionsColumn.ShowInCustomizationForm = false;

                }
                else
                {
                    colEtaxValue.Visible = true;
                    col_ETaxRatio.Visible = true;
                    colTaxType.Visible = true;

                    TotalTaxes.Visible = false;
                    totalTaxesRatio.Visible = false;
                    btn_AddTaxes.Visible = false;
                    TotalTaxes.OptionsColumn.ShowInCustomizationForm = false;
                    totalTaxesRatio.OptionsColumn.ShowInCustomizationForm = false;
                    btn_AddTaxes.OptionsColumn.ShowInCustomizationForm = false;

                }
            }
        }

        private void frm_SL_Return_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                e.Cancel = true;
            else
                e.Cancel = false;

            ErpUtils.save_Grid_Layout(grdPrInvoice, this.Name.Replace("frm_", ""), true);
            ErpUtils.save_MemoryStream_Layout(rep_layout, this.Name + ".repItems");
        }

        public static void CalcTotalPrice(int RowHandle, GridView view, decimal TotalQty, decimal SellPrice, decimal SalesTaxRatio, decimal CustomTaxRatio, bool calcTaxBeforeDisc, decimal DiscV, decimal EtaxRatio, decimal TaxType, decimal totalTaxesRatio, byte roundingDigits)
        {
            decimal bonusDiscount = 0;
            //decimal TotalSellPrice = (TotalQty * SellPrice) - DiscV;
            //decimal EtaxValue = TotalSellPrice * EtaxRatio / 100;
            //TotalSellPrice += EtaxValue;
            //view.SetRowCellValue(RowHandle, "EtaxValue", decimal.ToDouble(EtaxValue));

            //===============Update==============================//
            var DB = new DAL.ERPDataContext();
            int? taxID = 0;
            if (TaxType != 0)
                taxID = DB.E_TaxableTypes.FirstOrDefault(a => a.E_TaxableTypeId == TaxType).ParentTaxId;

            decimal TotalSellPrice = (TotalQty * SellPrice) - DiscV;
            decimal EtaxValue = TotalSellPrice * EtaxRatio / 100;
            decimal EtotalTaxesRatio = TotalSellPrice * totalTaxesRatio / 100;
            if (taxID.Value == discountTaxId && taxID.Value != 0)
            {
                TotalSellPrice -= EtaxValue + EtotalTaxesRatio;
            }
            else
            {
                TotalSellPrice += EtaxValue + EtotalTaxesRatio;
            }
            //===========================================================//
            var bonusDiscountValue = view.GetDataRow(RowHandle)["bonusDiscount"];
            if (bonusDiscountValue != DBNull.Value || bonusDiscountValue == null)
                bonusDiscount = Convert.ToDecimal(view.GetDataRow(RowHandle)["bonusDiscount"]);

            TotalSellPrice = TotalSellPrice - bonusDiscount;

            if (Shared.st_Store.PriceIncludeSalesTax)/*السعر شامل الضريبة*/
            {
                decimal salesTaxValue = 0;

                decimal customTaxValue = calcTaxBeforeDisc
                                               ? (CustomTaxRatio * TotalQty * SellPrice) / (1 + CustomTaxRatio)
                                               : (CustomTaxRatio * TotalSellPrice) / (1 + CustomTaxRatio);

                view.SetRowCellValue(RowHandle, "CustomTax", decimal.ToDouble(customTaxValue));

                if (CustomTaxRatio > 0)
                {


                    var salesTaxMask = TotalSellPrice + customTaxValue;
                    salesTaxValue = salesTaxMask * SalesTaxRatio;

                    view.SetRowCellValue(RowHandle, "SalesTax", decimal.ToDouble(salesTaxValue));
                }


                else
                {
                    salesTaxValue = calcTaxBeforeDisc
                    ? (SalesTaxRatio * TotalQty * SellPrice) / (1 + SalesTaxRatio)
                    : (SalesTaxRatio * TotalSellPrice) / (1 + SalesTaxRatio);


                    view.SetRowCellValue(RowHandle, "SalesTax", decimal.ToDouble(salesTaxValue));
                }


                view.GetDataRow(RowHandle)["TotalSellPrice"] = decimal.ToDouble(TotalSellPrice - salesTaxValue - customTaxValue);// السعر الاجمالي شامل الضريبة 
            }

            else                                            /*السعر غير شامل الضريبة*/
            {
                if (CustomTaxRatio > 0)
                {
                    var customTaxValue = calcTaxBeforeDisc ? (CustomTaxRatio * TotalQty * SellPrice) : (CustomTaxRatio * TotalSellPrice);
                    view.SetRowCellValue(RowHandle, "CustomTax", decimal.ToDouble(customTaxValue));

                    var salesTaxMask = TotalSellPrice + customTaxValue;
                    var salesTaxValue = salesTaxMask * SalesTaxRatio;
                    view.SetRowCellValue(RowHandle, "SalesTax", decimal.ToDouble(salesTaxValue));

                }

                else
                {
                    decimal salesTaxValue = calcTaxBeforeDisc ? (SalesTaxRatio * TotalQty * SellPrice) : (SalesTaxRatio * TotalSellPrice);
                    view.SetRowCellValue(RowHandle, "SalesTax", decimal.ToDouble(salesTaxValue));
                    view.SetRowCellValue(RowHandle, "CustomTax", 0);

                }

                view.GetDataRow(RowHandle)["TotalSellPrice"] = decimal.ToDouble(TotalSellPrice);//ضيف الضريبة على السعر الاجمالي 

            }
        }

        private void gridView1_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            if (e.Column.FieldName == "CompanyNameAr" || e.Column.FieldName == "LargeUOMFactor"
                || e.Column.FieldName == "MediumUOMFactor" || e.Column.FieldName == "TotalSellPrice" || e.Column.FieldName == "CurrentQty"
                || e.Column.FieldName == "IsAssembly" || e.Column.FieldName == "UomIndex"
                || e.Column.FieldName == "VendorId"
                || e.Column.FieldName == "Expire" || e.Column.FieldName == "Batch")
                return;

            DB = new DAL.ERPDataContext();
            DAL.IC_Item item = null;

            GridView view = grdPrInvoice.FocusedView as GridView;
            DataRow row = view.GetFocusedDataRow();

            #region Barcode_Init_Detail_PR

            Detail_PR detail_PR = new Detail_PR
            {
                ItemId = (int)0,
                Batch = "",
                Expire = (DateTime?)DateTime.Now,
                Length = (decimal?)1,
                Width = (decimal?)1,
                Height = (decimal?)1,
                PiecesCount = (decimal)0,
                PurchasePrice = (decimal)0,
                TotalPurchasePrice = (decimal)0,
                SellPrice = (decimal)0,
                Qty = (decimal)0,
                UOMId = (int)0,
                UOMIndex = (byte)0,
                VendorId = (int?)0
            };
            detail_PR = null;
            #endregion

            int barcodeTemplateCode = 0;
            decimal barcodeTemplateQty = 0;
            string barcodeBatch = string.Empty;
            string code1 = string.Empty;


            #region Rounding values based on settings
            if (e.Column.FieldName == "SellPrice" && Shared.st_Store.DefaultRoundingPoints != null)
            {
                var sellPriceValue = Convert.ToDecimal(view.GetFocusedRowCellValue("SellPrice"));
                var sellPriceDisplayValue = Convert.ToDecimal(view.GetFocusedRowCellDisplayText("SellPrice"));
                if (sellPriceValue != sellPriceDisplayValue)
                {
                    view.SetFocusedRowCellValue("SellPrice", sellPriceDisplayValue);
                }
            }

            if (e.Column.FieldName == "DiscountValue" && Shared.st_Store.DefaultRoundingPoints != null)
            {
                var DiscountValueValue = Convert.ToDecimal(view.GetFocusedRowCellValue("DiscountValue"));
                var DiscountValueDisplayValue = Convert.ToDecimal(view.GetFocusedRowCellDisplayText("DiscountValue"));
                if (DiscountValueValue != DiscountValueDisplayValue)
                {
                    view.SetFocusedRowCellValue("DiscountValue", DiscountValueDisplayValue);
                }
            }

            #endregion


            #region GetItem
            if (e.Column.FieldName == "ItemCode1")
            {
                if (view.GetFocusedRowCellValue("ItemCode1") != null && view.GetFocusedRowCellValue("ItemCode1").ToString() != string.Empty)
                {
                    code1 = view.GetFocusedRowCellValue("ItemCode1").ToString();

                    item = MyHelper.SearchItem(DB, code1, detail_PR, ref barcodeTemplateCode, ref barcodeTemplateQty, ref barcodeBatch, invoiceId,
                        Shared.st_Store);
                }
                else
                {
                    view.DeleteRow(e.RowHandle);
                }
            }

            if (e.Column.FieldName == "ItemCode2")
            {
                if (view.GetFocusedRowCellValue("ItemCode2").ToString() != string.Empty)
                {
                    item = (from i in DB.IC_Items
                            where i.ItemType != (int)DAL.ItemType.MatrixParent
                            where i.ItemCode2 == view.GetFocusedRowCellValue("ItemCode2").ToString()
                            where invoiceId == 0 ? i.IsDeleted == false : true
                            select i).FirstOrDefault();
                }
                else
                {
                    view.DeleteRow(e.RowHandle);
                }
            }

            if (e.Column.FieldName == "ItemId")
            {
                if (view.GetFocusedRowCellValue("ItemId").ToString() != string.Empty)
                {
                    item = (from i in DB.IC_Items
                            where i.ItemType != (int)DAL.ItemType.MatrixParent
                            where i.ItemId == Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"))
                            where invoiceId == 0 ? i.IsDeleted == false : true
                            select i).SingleOrDefault();
                }
                else
                {
                    view.DeleteRow(e.RowHandle);
                }
            }

            if (e.Column.FieldName == "ItemCode1" || e.Column.FieldName == "ItemCode2"
                || e.Column.FieldName == "ItemId")
            {
                if (item != null && item.ItemId > 0)
                {
                    if (item.ItemType == (int)ItemType.Subtotal)
                    {
                        row["ItemId"] = item.ItemId;
                        row["ItemCode1"] = item.ItemCode1;
                        row["ItemCode2"] = item.ItemCode2;
                        row["ItemType"] = item.ItemType;
                        row["IsExpire"] = item.IsExpire;
                        row["ItemDescription"] = item.Description;
                        row["ItemDescriptionEn"] = item.DescriptionEn;
                        row["CategoryId"] = item.Category;
                        row["bonusDiscount"] = 0;
                        row["ETaxRatio"] = 0;
                        row["EtaxValue"] = 0;
                        Get_SubTotal_RowData(row, view, e.RowHandle);
                    }
                    else
                    {
                        LoadItemRow(item, row);
                        if (Shared.st_Store.PrintBarcodePerInventory && detail_PR != null)
                        {
                            row["PurchasePrice"] = Decimal.ToDouble(detail_PR.TotalPurchasePrice / (detail_PR.Qty));
                            row["VendorId"] = detail_PR.VendorId;
                            row["PiecesCount"] = detail_PR.PiecesCount;
                            row["Length"] = detail_PR.Length.HasValue ? decimal.ToDouble(detail_PR.Length.Value) : 1;
                            row["Width"] = detail_PR.Width.HasValue ? decimal.ToDouble(detail_PR.Width.Value) : 1;
                            row["Height"] = detail_PR.Height.HasValue ? decimal.ToDouble(detail_PR.Height.Value) : 1;
                            if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish)
                                row["Qty"] = decimal.ToDouble(detail_PR.Qty);
                            row["Batch"] = detail_PR.Batch;

                            if (detail_PR.Expire.HasValue)
                            {
                                row["Expire"] = detail_PR.Expire.Value;
                            }
                            else
                            {
                                row["Expire"] = DBNull.Value;
                            }
                        }

                        if (Shared.st_Store.PrintBarcodePerInventory == false && barcodeTemplateCode > 0 && barcodeTemplateQty > 0)
                        {
                            row["Qty"] = decimal.ToDouble(barcodeTemplateQty);
                            row["Batch"] = barcodeBatch;
                            row["PiecesCount"] = 1;
                        }
                        if (Shared.E_invoiceAvailable == true &&
        ((IsTaxable == null && Convert.ToBoolean(ErpUtils.GetGridLookUpValue(lkp_Customers, lkp_Customers.EditValue, "IsTaxable"))) ||
        IsTaxable == true))
                        {
                            view.SetRowCellValue(e.RowHandle, "TaxValue", 0);
                            calcSubTaxes(row, view);
                        }
                    }
                    Get_TotalAccount();
                }
                else
                {
                    view.DeleteRow(e.RowHandle);
                    return;
                }
            }
            #endregion

            if (view.GetRowCellValue(e.RowHandle, "ItemType") != null && Convert.ToInt32(view.GetRowCellValue(e.RowHandle, "ItemType")) == (int)ItemType.Subtotal)
            {
                Update_First_SubTotal(view, e.RowHandle);
                return;
            }

            #region GetUomPrice
            if (e.Column.FieldName == "UOM")
            {
                if (view.GetFocusedRowCellValue("UOM").ToString() != string.Empty &&
                    view.GetFocusedRowCellValue("ItemId").ToString() != string.Empty)
                {
                    int itmId = Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"));
                    int uomIndex = Convert.ToInt32(ErpUtils.GetGridLookUpValue(repUOM, view.GetRowCellValue(e.RowHandle, "UOM"), "Index"));

                    //get UOM and Factor, multiple by purchase and sell prices
                    item = (from i in DB.IC_Items
                            where i.ItemId == itmId
                            select i).SingleOrDefault();

                    decimal uom_price = MyHelper.GetPriceLevelSellPrice(CustomerPriceLevel, item, uomIndex);
                    view.GetDataRow(e.RowHandle)["SellPrice"] = decimal.ToDouble(uom_price);

                    if (uomIndex == 0)//small                    
                        view.GetDataRow(e.RowHandle)["PurchasePrice"] = decimal.ToDouble(item.PurchasePrice);
                    if (uomIndex == 1)//medium                                           
                        view.GetDataRow(e.RowHandle)["PurchasePrice"] = decimal.ToDouble(item.PurchasePrice * MyHelper.FractionToDouble(item.MediumUOMFactor));
                    if (uomIndex == 2)//large
                        view.GetDataRow(e.RowHandle)["PurchasePrice"] = decimal.ToDouble(item.PurchasePrice * MyHelper.FractionToDouble(item.LargeUOMFactor));

                    view.GetDataRow(e.RowHandle)["UomIndex"] = uomIndex;
                }
            }
            #endregion

            #region Calculate Prices
            if (e.Column.FieldName == "bonusDiscount"||e.Column.FieldName == "DiscountValue" || e.Column.FieldName == "SellPrice"
                || e.Column.FieldName == "Qty" || e.Column.FieldName == "ItemId"
                || e.Column.FieldName == "ItemCode1" || e.Column.FieldName == "ItemCode2"
                || e.Column.FieldName == "UOM"
                || e.Column.FieldName == "ETaxRatio"
                || e.Column.FieldName == "totalTaxesRatio"
                || e.Column == col_Height || e.Column == col_Width || e.Column == col_Length)
            {
                try
                {
                    if (!string.IsNullOrEmpty(view.GetFocusedRowCellValue("SellPrice").ToString()) &&
                        !string.IsNullOrEmpty(view.GetFocusedRowCellValue("DiscountValue").ToString()) &&
                        !string.IsNullOrEmpty(view.GetFocusedRowCellValue("Qty").ToString()) &&
                        !string.IsNullOrEmpty(view.GetFocusedRowCellValue("ItemId").ToString()))
                    {
                        decimal Height = Convert.ToDecimal(view.GetFocusedRowCellValue("Height"));
                        decimal Length = Convert.ToDecimal(view.GetFocusedRowCellValue("Length"));
                        decimal Width = Convert.ToDecimal(view.GetFocusedRowCellValue("Width"));
                        if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish)
                            Height = Length = Width = 1;

                        decimal Qty = Convert.ToDecimal(view.GetFocusedRowCellValue("Qty"));
                        decimal TotalQty = Qty * Height * Width * Length;
                        decimal SellPrice = Convert.ToDecimal(view.GetFocusedRowCellValue("SellPrice"));
                        decimal PurchasePrice = Convert.ToDecimal(view.GetFocusedRowCellValue("PurchasePrice"));
                        int itmId = Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"));
                        byte uomIndex = Convert.ToByte(view.GetFocusedRowCellValue("UomIndex"));
                        decimal SalesTaxRatio = Convert.ToDecimal(view.GetFocusedDataRow()["SalesTaxRatio"]);
                        decimal CustomTaxRatio = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["CustomTaxRatio"]);
                        bool calcTaxBeforeDisc = Convert.ToBoolean(view.GetDataRow(e.RowHandle)["calcTaxBeforeDisc"]);

                        var SubTaxes = DB.IC_ItemSubTaxes.Where(a => a.ItemId == itmId).ToList();
                        var SubTaxesIds = SubTaxes.Select(a => a.SubTaxId).ToList();
                        if ((Shared.E_invoiceAvailable == true &&
                            ((IsTaxable == null && Convert.ToBoolean(ErpUtils.GetGridLookUpValue(lkp_Customers, lkp_Customers.EditValue, "IsTaxable"))) ||
                            IsTaxable == true)) || Shared.st_Store.E_AllowMoreThanTax == true)
                        {
                           
                             var  dataRowsCountSubTax = Dt_Rows.AsEnumerable()
                                 .Where(a => a.RowState != DataRowState.Deleted)
                                 .Where(a => a["ItemId"].ToString() == itmId.ToString() && Convert.ToInt32(a["RowHandle"].ToString()) == rowhandle).Select(a => Convert.ToInt32(a["SubTax"].ToString())).ToList();
                            if (Enumerable.SequenceEqual(dataRowsCountSubTax.Intersect(SubTaxesIds).ToList().OrderBy(m => m), SubTaxesIds.OrderBy(m => m))
                                && dataRowsCountSubTax.Count== SubTaxesIds.Count)
                            {
                                var item1 = DB.IC_Items.FirstOrDefault(a => a.ItemId == itmId);
                                if (item1 != null)
                                    calcSubTaxes(view.GetDataRow(e.RowHandle), view);
                            }

                        }
                        //decimal DiscV = Convert.ToDecimal(view.GetFocusedDataRow()["DiscountValue"]);
                        //decimal TotalSellPrice = (TotalQty * SellPrice) - DiscV;
                        //decimal EtaxRatio = 0;
                        //if (view.GetDataRow(e.RowHandle)["ETaxRatio"] != DBNull.Value && view.GetDataRow(e.RowHandle)["ETaxRatio"] != null)
                        //    EtaxRatio = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["ETaxRatio"]);
                        //CalcTotalPrice(e.RowHandle, view, TotalQty, SellPrice, SalesTaxRatio, CustomTaxRatio, calcTaxBeforeDisc, DiscV, EtaxRatio);

                        decimal totalTaxesRatio = 0;
                        decimal DiscV = string.IsNullOrEmpty(view.GetFocusedRowCellValue("DiscountValue").ToString()) == false?Convert.ToDecimal(view.GetFocusedRowCellValue("DiscountValue")) :0m;
                        //if (view.GetDataRow(e.RowHandle)["TaxValue"] != DBNull.Value && view.GetDataRow(e.RowHandle)["totalTaxesRatio"] != null)
                        //    totalTaxesRatio = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["totalTaxesRatio"]);
                        //decimal EtaxRatio = 0;
                        //if (view.GetDataRow(e.RowHandle)["ETaxRatio"] != DBNull.Value && view.GetDataRow(e.RowHandle)["ETaxRatio"] != null)
                        //    EtaxRatio = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["ETaxRatio"]);
                        //decimal TaxType = 0;
                        //if (view.GetDataRow(e.RowHandle)["TaxType"] != DBNull.Value && view.GetDataRow(e.RowHandle)["TaxType"] != null)
                        //    TaxType = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["TaxType"]);
                        ////CalcTotalPrice(e.RowHandle, view, TotalQty, SellPrice, SalesTaxRatio, CustomTaxRatio, calcTaxBeforeDisc, DiscV, EtaxRatio, TaxType, totalTaxesRatio,defaultRoundingPoints);
                        //view.GetDataRow(e.RowHandle)["ETaxValue"] = (TotalQty * SellPrice - DiscV) * EtaxRatio / 100;
                        //view.GetDataRow(e.RowHandle)["TotalTaxes"] = (TotalQty * SellPrice - DiscV) * totalTaxesRatio / 100;
                        if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.DistinguishAndMultiply)
                            view.GetDataRow(e.RowHandle)["PiecesCount"] = Qty;

                        //get store qty
                        decimal medium = 1;
                        decimal large = 1;
                        medium = MyHelper.FractionToDouble(view.GetFocusedRowCellValue("MediumUOMFactor").ToString());
                        large = MyHelper.FractionToDouble(view.GetFocusedRowCellValue("LargeUOMFactor").ToString());

                        //decimal currentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, itmId, Convert.ToInt32(lkpStore.EditValue));
                        //currentQty = MyHelper.getCalculatedUomQty(currentQty, uomIndex, medium, large);

                        //  view.SetFocusedRowCellValue("CurrentQty", decimal.ToDouble(currentQty));
                        view.SetFocusedRowCellValue("CurrentQty", decimal.ToDouble(0));
                        Get_TotalAccount();
                    }
                }
                catch (Exception ex) { }
            }
            #endregion

            #region DiscountRation_changed_by_User
            if (e.Column.FieldName == "bonusDiscount" || e.Column.FieldName == "ETaxRatio" || e.Column.FieldName == "DiscountRatio" || e.Column.FieldName == "DiscountRatio2" || e.Column.FieldName == "DiscountRatio3"
                || e.Column.FieldName == "Qty" || e.Column.FieldName == "SellPrice" || e.Column.FieldName == "UOM")
            {
                try
                {
                    if (!string.IsNullOrEmpty(view.GetFocusedRowCellValue(e.Column.FieldName).ToString()))
                    {
                        decimal Height = Convert.ToDecimal(view.GetFocusedRowCellValue("Height"));
                        decimal Length = Convert.ToDecimal(view.GetFocusedRowCellValue("Length"));
                        decimal Width = Convert.ToDecimal(view.GetFocusedRowCellValue("Width"));
                        if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish)
                            Height = Length = Width = 1;

                        decimal Qty = Convert.ToDecimal(view.GetFocusedRowCellValue("Qty"));
                        decimal TotalQty = Qty * Height * Width * Length;

                        decimal SellPrice = Convert.ToDecimal(view.GetFocusedRowCellValue("SellPrice"));
                        decimal DiscR1 = Convert.ToDecimal(view.GetFocusedDataRow()["DiscountRatio"]);
                        decimal DiscR2 = Convert.ToDecimal(view.GetFocusedDataRow()["DiscountRatio2"]);
                        decimal DiscR3 = Convert.ToDecimal(view.GetFocusedDataRow()["DiscountRatio3"]);

                        bool calcTaxBeforeDisc = Convert.ToBoolean(view.GetDataRow(e.RowHandle)["calcTaxBeforeDisc"]);
                        decimal salesTax = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["SalesTax"]);
                        decimal totalSellP = calcTaxBeforeDisc ? ((SellPrice * TotalQty) - salesTax) : SellPrice * TotalQty;
                        decimal bonusDiscount = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["bonusDiscount"]);
                        decimal DiscountValue = Utilities.Calc_DiscountValue(DiscR1, DiscR2, DiscR3, totalSellP);
                        view.SetFocusedRowCellValue("DiscountValue", decimal.ToDouble(DiscountValue).ToString($"F{defaultRoundingPoints}"));
                    }
                }
                catch { }
            }
            #endregion

            #region totalTableTaxes

            //decimal Qty1 = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "Qty"));
            //decimal SellPrice1 = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "SellPrice"));
            //decimal DiscR11 = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["DiscountRatio"]);
            //decimal DiscR21 = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["DiscountRatio2"]);
            //decimal DiscR31 = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["DiscountRatio3"]);
            //bool calcTaxBeforeDisc1 = Convert.ToBoolean(view.GetDataRow(e.RowHandle)["calcTaxBeforeDisc"]);
            //var tableTaxIds = DB.E_TaxableTypes.Where(a => tableTax.Contains(a.Code)).Select(a => a.E_TaxableTypeId.ToString()).ToList();
            ////=======================//
            //var tableTaxes = Dt_Rows.AsEnumerable()
            //     .Where(a => a.RowState != DataRowState.Deleted)
            //     .Where(r => tableTaxIds.Contains(r["Tax"].ToString()) && r["Tax"].ToString() != "1" && Convert.ToInt32(r["RowHandle"]) == Convert.ToInt32(row["RowHandle"])).ToList();
            //decimal tableTaxesType = tableTaxes.Sum(a => Convert.ToInt32(a["Percentage"]));
            ////=======================//
            //var addTaxes = Dt_Rows.AsEnumerable()
            //.Where(a => a.RowState != DataRowState.Deleted)
            //.Where(r => r["Tax"].ToString() == "1" && Convert.ToInt32(r["RowHandle"]) == Convert.ToInt32(row["RowHandle"])).ToList();
            //decimal addTaxesType = addTaxes.Sum(a => Convert.ToInt32(a["Percentage"]));
            //if (view.GetDataRow(e.RowHandle)["totalTableTaxes"] != DBNull.Value && view.GetDataRow(e.RowHandle)["totalTableTaxes"] != null)
            //{

            //    decimal totalTableTaxes = Convert.ToDecimal(view.GetDataRow(e.RowHandle)["totalTableTaxes"]);
            //    CalcTableTax(e.RowHandle, view, DiscR11, DiscR21, DiscR31, SellPrice1, Qty1, calcTaxBeforeDisc1, totalTableTaxes);
            //}

            //if (view.GetDataRow(e.RowHandle)["tableTaxValue"] != DBNull.Value && view.GetDataRow(e.RowHandle)["tableTaxValue"] != null)
            //{

            //    CalcTableTaxType(e.RowHandle, view, DiscR11, DiscR21, DiscR31, SellPrice1, Qty1, calcTaxBeforeDisc1, tableTaxesType);
            //}


            //if (view.GetDataRow(e.RowHandle)["addTaxValue"] != DBNull.Value && view.GetDataRow(e.RowHandle)["addTaxValue"] != null)
            //{


            //    CalcAddTaxType(e.RowHandle, view, DiscR11, DiscR21, DiscR31, SellPrice1, Qty1, calcTaxBeforeDisc1, addTaxesType, tableTaxesType);
            //}
            #endregion


            calcSubTaxes(row, view);

            if (Shared.LibraAvailabe)
        
    {
                if (item == null & view.GetRowCellValue(e.RowHandle, "ItemId") != DBNull.Value)
                    item = (from i in DB.IC_Items
                            where i.ItemId == Convert.ToInt32(view.GetRowCellValue(e.RowHandle, "ItemId"))
                            select i).FirstOrDefault();

                //var VariableWeight = view.GetFocusedRowCellValue("VariableWeight");
                int _PiecesCount = 0;
                //if (VariableWeight != null && VariableWeight != DBNull.Value && Convert.ToBoolean(VariableWeight))
                //{
                if (view.GetFocusedRowCellValue("PiecesCount") != DBNull.Value)
                    _PiecesCount = Convert.ToInt32(view.GetFocusedRowCellValue("PiecesCount"));
                if (_PiecesCount < 1)
                {
                    if (item != null && item.PricingWithSmall != true && e.Column.FieldName != "PiecesCount")
                    {
                        _PiecesCount = 1;
                        view.SetFocusedRowCellValue("PiecesCount", 1);
                    }
                }
                //}


                //في حالة ان الصنف وزنه متغير، هنخلي الكمية بتساوي الوزن بالكيلو
                if (e.Column == col_kg_Weight_libra && item != null && item.VariableWeight == true)
                {
                    view.SetFocusedRowCellValue("Qty", view.GetFocusedRowCellValue(col_kg_Weight_libra));
                }
                //في حالة ان الوزن ثابت، هنخلي الوزن بالكيلو بيساوي عدد القطع مضروب في معامل وحدة القياس
                //ونخلي الكمية بتساوي عدد القطع
                else if ((e.Column.FieldName == "PiecesCount"
                    || e.Column.FieldName == "ItemId" || e.Column.FieldName == "ItemCode1" || e.Column.FieldName == "ItemCode2" || e.Column.FieldName == "UOM")
                    && item != null && item.PricingWithSmall == true)
                {
                    //var xxx = repUOM.GetRowByKeyValue(view.GetFocusedRowCellValue("UOM"));
                    //if (xxx != DBNull.Value)
                    {
                        try
                        {
                            decimal f = 0;
                            //if (xxx == null)
                            {
                                var _uom = view.GetFocusedRowCellValue("UOM");
                                if (_uom != DBNull.Value)
                                {
                                    byte uom = Convert.ToByte(_uom);
                                    f = item.SmallUOM == uom ? 1 : (item.MediumUOM == uom ? item.MediumUOMFactorDecimal.Value : item.LargeUOMFactorDecimal.Value);
                                }
                            }
                            //else
                            //    f = Convert.ToDecimal(((DataRowView)xxx)["Factor"]);
                            var PiecesCount = Convert.ToDecimal(view.GetFocusedRowCellValue("PiecesCount"));

                            view.SetFocusedRowCellValue(col_kg_Weight_libra, Convert.ToDouble(f * PiecesCount));
                            view.SetFocusedRowCellValue("Qty", PiecesCount);
                        }
                        catch
                        { }
                    }
                }
                else if (e.Column.FieldName == "PiecesCount" && item != null && item.VariableWeight != true &&
                   item.VariableWeight != true && item.PricingWithSmall != true && item.is_libra != true)
                {
                    var PiecesCount = Convert.ToDecimal(view.GetFocusedRowCellValue("PiecesCount"));
                    view.SetFocusedRowCellValue("Qty", PiecesCount);
                }
                else if (e.Column.FieldName == "Qty" && item != null && item.is_libra == true)
                {
                    view.SetFocusedRowCellValue(col_kg_Weight_libra, view.GetFocusedRowCellValue("Qty"));
                }
                var PricingWithSmall = view.GetFocusedRowCellValue("PricingWithSmall");
                if (PricingWithSmall != null && PricingWithSmall != DBNull.Value && Convert.ToBoolean(PricingWithSmall))
                {
                    //var xxx = repUOM.GetRowByKeyValue(view.GetFocusedRowCellValue("UOM"));
                    //if (xxx != DBNull.Value)
                    {
                        try
                        {
                            decimal f = 0;
                            //if (xxx == null)
                            {
                                var _uom = view.GetFocusedRowCellValue("UOM");
                                if (_uom != DBNull.Value)
                                {
                                    byte uom = Convert.ToByte(_uom);
                                    f = item.SmallUOM == uom ? 1 : (item.MediumUOM == uom ? item.MediumUOMFactorDecimal.Value : item.LargeUOMFactorDecimal.Value);
                                }
                            }
                            //else
                            //    f = Convert.ToDecimal(((DataRowView)xxx)["Factor"]);
                            var sp = Convert.ToDecimal(view.GetFocusedRowCellValue("SellPrice"));
                            var qty = Convert.ToDecimal(view.GetFocusedRowCellValue("Qty"));
                            var PiecesCount = Convert.ToDecimal(view.GetFocusedRowCellValue("PiecesCount"));

                            view.SetFocusedRowCellValue("TotalSellPrice", Convert.ToDouble(f * sp * qty) /** (_PiecesCount > 1 ? PiecesCount : 1)*/);
                        }
                        catch
                        { }
                    }
                }

                if (e.Column.FieldName == "ItemId" || e.Column.FieldName == "LibraQty" ||
                     (e.Column.FieldName == "PiecesCount" && item != null))
                {
                    if (item.is_libra != true) return;

                    var Large_price = view.GetRowCellValue(e.RowHandle, "LibraQty");
                    if (Large_price == DBNull.Value || Large_price == null) return;
                    view.SetRowCellValue(e.RowHandle, "Qty", Math.Round((Convert.ToDecimal(Large_price) / MyHelper.FractionToDouble(item.LargeUOMFactorDecimal.Value.ToString())), defaultRoundingPoints));

                }
            }


            Update_First_SubTotal(view, e.RowHandle);
            Get_TotalAccount();
        }
        public void CalcTableTax(int RowHandle, GridView view, decimal disc1, decimal disc2, decimal disc3, decimal SellPrice, decimal Qty, bool calcTaxBeforeDisc, decimal totalTableTaxes)
        {
            var TotalSellPrice = Convert.ToDecimal(Qty * SellPrice * (1 - disc1) * (1 - disc2) * (1 - disc3));
            totalTableTaxes = (totalTableTaxes / 100) * TotalSellPrice;
            view.GetDataRow(RowHandle)["salePriceWithTaxTable"] = decimal.ToDouble(TotalSellPrice + totalTableTaxes);
        }

        public void CalcTableTaxType(int RowHandle, GridView view, decimal disc1, decimal disc2, decimal disc3, decimal SellPrice, decimal Qty, bool calcTaxBeforeDisc, decimal totalTableTaxesType)
        {
            var TotalSellPrice = Convert.ToDecimal(Qty * SellPrice * (1 - disc1) * (1 - disc2) * (1 - disc3));
            totalTableTaxesType = (totalTableTaxesType / 100) * TotalSellPrice;
            view.GetDataRow(RowHandle)["tableTaxValue"] = decimal.ToDouble(totalTableTaxesType);
        }
        public void CalcAddTaxType(int RowHandle, GridView view, decimal disc1, decimal disc2, decimal disc3, decimal SellPrice, decimal Qty, bool calcTaxBeforeDisc, decimal totaladdTaxesTyp, decimal tableTaxesType)
        {
            var TotalSellPrice = Convert.ToDecimal(Qty * SellPrice * (1 - disc1) * (1 - disc2) * (1 - disc3));
            var tableTaxes = (tableTaxesType / 100) * TotalSellPrice;
            TotalSellPrice = tableTaxes + TotalSellPrice;
            var addTaxValue = (totaladdTaxesTyp / 100) * TotalSellPrice;
            view.GetDataRow(RowHandle)["addTaxValue"] = decimal.ToDouble(addTaxValue);
        }
        private void gridView1_FocusedColumnChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedColumnChangedEventArgs e)
        {
            if (e.FocusedColumn != null && e.FocusedColumn.FieldName == "UOM")
            {
                GridView view = grdPrInvoice.FocusedView as GridView;
                DataRow row = view.GetFocusedDataRow();

                DB = new DAL.ERPDataContext();
                DAL.IC_Item item = new DAL.IC_Item();

                if (row != null && row["ItemId"].ToString() != string.Empty)
                {
                    item = (from i in DB.IC_Items
                            where i.ItemId == Convert.ToInt32(row["ItemId"])
                            where invoiceId == 0 ? i.IsDeleted == false : true
                            select i).SingleOrDefault();

                    MyHelper.GetUOMs(item, dtUOM, uom_list);

                    if (string.IsNullOrEmpty(row["UOM"].ToString()))
                        view.SetFocusedRowCellValue("UOM", dtUOM.Rows[0]["UomId"]);
                }
            }

        }

        private void gridView1_KeyDown(object sender, KeyEventArgs e)
        {
            GridView view = sender as GridView;
            if (e.KeyCode == Keys.Delete && e.Modifiers == Keys.Control)
            {
                //if (MessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgDelRow : ResSLAr.MsgDelRow,
                //    Shared.IsEnglish ? ResSLEn.MsgTQues : ResSLAr.MsgTQues, MessageBoxButtons.YesNo, MessageBoxIcon.Question) !=
                //  DialogResult.Yes)
                //    return;

                view.DeleteRow(view.FocusedRowHandle);
                dtSLReturn_Details.AcceptChanges();
                Dt_Rows.AcceptChanges();

                Update_First_SubTotal(view, view.FocusedRowHandle);
                taxValue = 0;
                foreach (DataRow dr in dtSLReturn_Details.Rows)
                {

                    if (dr.RowState == DataRowState.Deleted)
                        continue;

                    calcSubTaxes(dr, view);

                }
                Get_TotalAccount();
            }
            if (e.KeyCode == Keys.Up && e.Control && e.Shift)
            {
                ErpUtils.Move_Row_Up(view);
                Update_First_SubTotal(view, view.FocusedRowHandle - 1);
                Update_First_SubTotal(view, view.FocusedRowHandle + 1);
            }
            if (e.KeyCode == Keys.Down && e.Control && e.Shift)
            {
                ErpUtils.Move_Row_Down(view);
                Update_First_SubTotal(view, view.FocusedRowHandle - 1);
                Update_First_SubTotal(view, view.FocusedRowHandle + 2);
            }
        }

        private void gridView1_ValidateRow(object sender, DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs e)
        {
            try
            {
                ColumnView view = sender as ColumnView;
                if (Convert.ToInt32(view.GetDataRow(e.RowHandle)["ItemType"]) == (int)ItemType.Subtotal)
                    return;

                if (view.GetRowCellValue(e.RowHandle, view.Columns["ItemId"]).ToString() == string.Empty)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["ItemId"], Shared.IsEnglish ? ResSLEn.txtValidateItem : ResSLAr.txtValidateItem);//"يجب اختيار الصنف";
                }

                if (Shared.LibraAvailabe)
                {
                    var itmm = view.GetRowCellValue(e.RowHandle, view.Columns["ItemId"]);
                    var item = DB.IC_Items.Where(x => x.ItemId == Convert.ToInt32(itmm)).FirstOrDefault();
                    if (itmm != null && itmm != DBNull.Value)
                    {
                        if (item != null && item.is_libra == true)
                        {
                            if ((view.GetRowCellValue(e.RowHandle, view.Columns["LibraQty"])).ToString() == string.Empty
                                || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["LibraQty"])) <= 0)
                            {
                                e.Valid = false;
                                view.SetColumnError(view.Columns["LibraQty"], Shared.IsEnglish ? ResPrEn.txtValidateQty : ResPrAr.txtValidateQty);//"الكمية يجب ان تكون اكبر من الصفر");
                            }
                            else
                            { e.Valid = true; }
                        }
                    }
                    try
                    {
                        decimal PiecesCount = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "PiecesCount"));
                        decimal PriceWithSmall = Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, "PriceWithSmall"));
                        if (PiecesCount < 1)
                        {
                            if (item != null && item.PricingWithSmall == true)
                            {
                                e.Valid = false;
                                view.SetColumnError(view.Columns["PiecesCount"], Shared.IsEnglish ? "Pieces count must be larger than 0" : "عدد القطع يجب أن يكون أكبر من صفر");//"يجب ادخال عدد القطع";}
                            }
                        }
                    }
                    catch (Exception x)
                    {
                        e.Valid = false;
                        view.SetColumnError(view.Columns["PiecesCount"], Shared.IsEnglish ? "Pieces count must be a number larger than 0" : "عدد القطع يجب أن يكون رقم أكبر من صفر");//"يجب ادخال عدد القطع";}
                    }


                    if ((item.VariableWeight == true || item.is_libra == true || item.PricingWithSmall == true) &&
                        view.GetRowCellValue(e.RowHandle, view.Columns["kg_Weight_libra"]).ToString() == string.Empty)
                    {
                        e.Valid = false;
                        view.SetColumnError(view.Columns["kg_Weight_libra"], Shared.IsEnglish ? "You must enter the weight (kg)" : "يجب إدخال الوزن بالكيلو");
                    }

                    if (view.GetRowCellValue(e.RowHandle, view.Columns["PiecesCount"]).ToString() == string.Empty)
                    {
                        e.Valid = false;
                        view.SetColumnError(view.Columns["PiecesCount"], Shared.IsEnglish ? "You must enter the pieces count" : "يجب إدخال عدد القطع");
                    }

                }

                if (view.GetRowCellValue(e.RowHandle, view.Columns["UOM"]).ToString() == string.Empty)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["UOM"], Shared.IsEnglish ? ResSLEn.txtValidateUom : ResSLAr.txtValidateUom);//"يجب اختيار وحدة القياس");
                }
                if ((view.GetRowCellValue(e.RowHandle, view.Columns["Qty"])).ToString() == string.Empty
                    || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Qty"])) <= 0)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["Qty"], Shared.IsEnglish ? ResSLEn.txtValidateQty : ResSLAr.txtValidateQty);//"الكمية يجب ان تكون اكبر من الصفر");
                }

                #region Validate Height Length and Width
                if ((view.GetRowCellValue(e.RowHandle, view.Columns["Height"])).ToString() == string.Empty
                    || (Shared.st_Store.MultiplyDimensions != (byte)Dimensions.Distinguish && Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Height"])) <= 0)
                    || (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish && Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Height"])) < 0))
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["Height"], Shared.IsEnglish ? ResSLEn.txtValidateQty : ResSLAr.txtValidateQty);//"الكمية يجب ان تكون اكبر من الصفر");
                }
                if ((view.GetRowCellValue(e.RowHandle, view.Columns["Length"])).ToString() == string.Empty
                    || (Shared.st_Store.MultiplyDimensions != (byte)Dimensions.Distinguish && Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Length"])) <= 0)
                    || (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish && Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Length"])) < 0))
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["Length"], Shared.IsEnglish ? ResSLEn.txtValidateQty : ResSLAr.txtValidateQty);//"الكمية يجب ان تكون اكبر من الصفر");
                }
                if ((view.GetRowCellValue(e.RowHandle, view.Columns["Width"])).ToString() == string.Empty
                    || (Shared.st_Store.MultiplyDimensions != (byte)Dimensions.Distinguish && Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Width"])) <= 0)
                    || (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish && Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["Width"])) < 0))
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["Width"], Shared.IsEnglish ? ResSLEn.txtValidateQty : ResSLAr.txtValidateQty);//"الكمية يجب ان تكون اكبر من الصفر");
                }
                if ((view.GetRowCellValue(e.RowHandle, view.Columns["PiecesCount"])).ToString() == string.Empty
                    || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["PiecesCount"])) < 0)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["PiecesCount"], Shared.IsEnglish ? ResSLEn.txtValidateQty : ResSLAr.txtValidateQty);//"الكمية يجب ان تكون اكبر من الصفر");
                }

                #endregion

                if (view.GetRowCellValue(e.RowHandle, view.Columns["SellPrice"]).ToString() == string.Empty
                    || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["SellPrice"])) < 0)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["SellPrice"], Shared.IsEnglish ? ResSLEn.txtValidateSPrice : ResSLAr.txtValidateSPrice);//"سعر البيع يجب أن يكون أكبر من الصفر");
                    return;
                }

                if (view.GetRowCellValue(e.RowHandle, view.Columns["DiscountValue"]).ToString() == string.Empty)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["DiscountValue"], Shared.IsEnglish ? ResSLEn.txtValidateDiscount : ResSLAr.txtValidateDiscount);//"يجب تحديد الخصم");
                }
                if (view.GetRowCellValue(e.RowHandle, view.Columns["DiscountRatio"]).ToString() == string.Empty
                    || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["DiscountRatio"])) > 1)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["DiscountRatio"], Shared.IsEnglish ? ResSLEn.txtValidateMaxDiscount : ResSLAr.txtValidateMaxDiscount);// "نسبة الخصم لايمكن ان تتجاوز المائة";
                }
                if (view.GetRowCellValue(e.RowHandle, view.Columns["DiscountRatio2"]).ToString() == string.Empty
                    || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["DiscountRatio2"])) > 1)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["DiscountRatio2"], Shared.IsEnglish ? ResSLEn.txtValidateMaxDiscount : ResSLAr.txtValidateMaxDiscount);// "نسبة الخصم لايمكن ان تتجاوز المائة";
                }
                if (view.GetRowCellValue(e.RowHandle, view.Columns["DiscountRatio3"]).ToString() == string.Empty
                    || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["DiscountRatio3"])) > 1)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["DiscountRatio3"], Shared.IsEnglish ? ResSLEn.txtValidateMaxDiscount : ResSLAr.txtValidateMaxDiscount);// "نسبة الخصم لايمكن ان تتجاوز المائة";
                }
                if (view.GetRowCellValue(e.RowHandle, view.Columns["TotalTaxes"]) != DBNull.Value && view.GetRowCellValue(e.RowHandle, view.Columns["TotalTaxes"]).ToString() != string.Empty)
                {

                    updateSubTaxGrid();
                }
            }
            catch
            {
                e.Valid = false;
            }
        }

        private void gridView1_InvalidRowException(object sender, DevExpress.XtraGrid.Views.Base.InvalidRowExceptionEventArgs e)
        {
            var xx = (e.Row as DataRowView).Row["ItemId"];
            if ((e.Row as DataRowView).Row["ItemId"] == DBNull.Value)
                e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.Ignore;
            else
                e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.NoAction;
        }

        private void gridView1_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            Get_TotalAccount();
            grd_FocusOnItemId(Shared.user.InvoicesUseSearchItems ? "ItemId" : "ItemCode1");//mahmoud
        }


        private void btnNext_Click(object sender, EventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            DB = new DAL.ERPDataContext();
            int lastInvId = (from inv in DB.SL_Returns
                             where inv.SL_ReturnId > invoiceId
                             where Shared.user.UserChangeStore ? true : DB.IC_Stores.Where(x => x.StoreId == Shared.user.DefaultStore ||
                                                                      x.ParentId == Shared.user.DefaultStore).Select(x => x.StoreId).Contains(inv.StoreId)// inv.StoreId == Shared.user.DefaultStore
                             where Shared.user.AccessOtherUserTrns ? true : inv.UserId == Shared.UserId

                             orderby inv.SL_ReturnId ascending
                             select inv.SL_ReturnId).FirstOrDefault();

            if (lastInvId != 0)
            {
                invoiceId = lastInvId;
                LoadInvoice();
            }
            else
            {
                lastInvId = (from inv in DB.SL_Returns
                             where Shared.user.UserChangeStore ? true : DB.IC_Stores.Where(x => x.StoreId == Shared.user.DefaultStore ||
                                                                      x.ParentId == Shared.user.DefaultStore).Select(x => x.StoreId).Contains(inv.StoreId)// inv.StoreId == Shared.user.DefaultStore
                             where Shared.user.AccessOtherUserTrns ? true : inv.UserId == Shared.UserId

                             orderby inv.SL_ReturnId ascending
                             select inv.SL_ReturnId).FirstOrDefault();

                if (lastInvId != 0)
                {
                    invoiceId = lastInvId;
                    LoadInvoice();
                }
            }
        }

        private void btnPrevious_Click(object sender, EventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;
            DB = new DAL.ERPDataContext();
            int lastInvId = (from inv in DB.SL_Returns
                             where inv.SL_ReturnId < invoiceId
                             where Shared.user.UserChangeStore ? true : DB.IC_Stores.Where(x => x.StoreId == Shared.user.DefaultStore ||
                                                                      x.ParentId == Shared.user.DefaultStore).Select(x => x.StoreId).Contains(inv.StoreId)// inv.StoreId == Shared.user.DefaultStore
                             where Shared.user.AccessOtherUserTrns ? true : inv.UserId == Shared.UserId

                             orderby inv.SL_ReturnId descending
                             select inv.SL_ReturnId).FirstOrDefault();

            if (lastInvId != 0)
            {
                invoiceId = lastInvId;
                LoadInvoice();
            }
            else
            {
                lastInvId = (from inv in DB.SL_Returns
                             where Shared.user.UserChangeStore ? true : DB.IC_Stores.Where(x => x.StoreId == Shared.user.DefaultStore ||
                                                                      x.ParentId == Shared.user.DefaultStore).Select(x => x.StoreId).Contains(inv.StoreId)// inv.StoreId == Shared.user.DefaultStore
                             where Shared.user.AccessOtherUserTrns ? true : inv.UserId == Shared.UserId

                             orderby inv.SL_ReturnId descending
                             select inv.SL_ReturnId).FirstOrDefault();

                if (lastInvId != 0)
                {
                    invoiceId = lastInvId;
                    LoadInvoice();
                }
            }
        }


        private void grid_ProcessGridKey(object sender, KeyEventArgs e)
        {
            try
            {
                DevExpress.XtraGrid.GridControl grid = sender as DevExpress.XtraGrid.GridControl;
                var view = (grid.FocusedView as DevExpress.XtraGrid.Views.Grid.GridView);

                if (e.KeyCode == Keys.Enter)
                {
                    var focused_column = (grid.FocusedView as DevExpress.XtraGrid.Views.Base.ColumnView).FocusedColumn;
                    int focused_row_handle = (grid.FocusedView as DevExpress.XtraGrid.Views.Base.ColumnView).FocusedRowHandle;

                    /*if (view.FocusedColumn == view.Columns["ItemId"]
                        || view.FocusedColumn == view.Columns["ItemCode1"]
                        || view.FocusedColumn == view.Columns["ItemCode2"])
                    {
                        if (Shared.user.UseBarcodeScanner)
                        {
                            grid_ProcessGridKey(sender, new KeyEventArgs(Keys.Tab));
                        }
                        else
                        {
                            string temp = view.FocusedColumn.FieldName;
                            view.FocusedColumn = view.VisibleColumns[view.Columns["ItemId"].VisibleIndex - 1];
                            if (view.GetFocusedRowCellValue(temp) == null || string.IsNullOrEmpty(view.GetFocusedRowCellValue(temp).ToString()))
                                view.FocusedColumn = view.Columns[temp];
                            return;
                        }
                    }*/

                    if (view.FocusedColumn == view.Columns["ItemId"]
                        || view.FocusedColumn == view.Columns["ItemCode1"]
                        || view.FocusedColumn == view.Columns["ItemCode2"]
                        || view.FocusedColumn == view.Columns["UOM"]
                        || view.FocusedColumn == view.Columns["Qty"]
                        || view.FocusedColumn == view.Columns["Length"]
                        || view.FocusedColumn == view.Columns["Width"]
                        || view.FocusedColumn == view.Columns["Height"]
                        || view.FocusedColumn == view.Columns["Expire"]
                        || view.FocusedColumn == view.Columns["Batch"]
                        || view.FocusedColumn == view.Columns["ItemDescription"]
                        || view.FocusedColumn == view.Columns["ItemDescriptionEn"]
                        || view.FocusedColumn == view.Columns["PiecesCount"]
                        || view.FocusedColumn == view.Columns["SellPrice"]
                        || view.FocusedColumn == view.Columns["PurchasePrice"]
                        || view.FocusedColumn == view.Columns["DiscountRatio"]
                        || view.FocusedColumn == view.Columns["DiscountRatio2"]
                        || view.FocusedColumn == view.Columns["DiscountRatio3"]
                        || view.FocusedColumn == view.Columns["DiscountValue"]
                        || view.FocusedColumn == view.Columns["Batch"]
                        || view.FocusedColumn == view.Columns["Serial"]
                        || view.FocusedColumn == view.Columns["Serial2"]
                        || view.FocusedColumn == view.Columns["LibraQty"]
                        || view.FocusedColumn == view.Columns["kg_Weight_libra"])
                    {
                        if (view.FocusedColumn.VisibleIndex - 1 >= 0 && view.VisibleColumns[view.FocusedColumn.VisibleIndex - 1].OptionsColumn.AllowFocus)
                        {
                            view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex - 1];
                            return;
                        }
                        else if (view.FocusedColumn.VisibleIndex - 2 >= 0 && view.VisibleColumns[view.FocusedColumn.VisibleIndex - 2].OptionsColumn.AllowFocus)
                        {
                            view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex - 2];
                            return;
                        }
                        else
                            grid_ProcessGridKey(sender, new KeyEventArgs(Keys.Tab));
                    }


                    if (view.FocusedRowHandle < 0)//|| view.FocusedRowHandle == view.RowCount)
                    {
                        view.AddNewRow();
                        view.FocusedColumn = view.Columns[Shared.user.InvoicesUseSearchItems ? "ItemId" : "ItemCode1"];//mahmoud                        
                    }
                    else
                    {
                        view.FocusedRowHandle = focused_row_handle + 1;
                        view.FocusedColumn = view.Columns[Shared.user.InvoicesUseSearchItems ? "ItemId" : "ItemCode1"];
                    }

                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == Keys.Tab && e.Modifiers != Keys.Shift)
                {
                    if (view.FocusedColumn.VisibleIndex == 0)
                        view.FocusedColumn = view.VisibleColumns[view.VisibleColumns.Count - 1];
                    else
                        view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex - 1];
                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == Keys.Tab && e.Modifiers == Keys.Shift)
                {
                    if (view.FocusedColumn.VisibleIndex == view.VisibleColumns.Count)
                        view.FocusedColumn = view.VisibleColumns[0];
                    else
                        view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex + 1];
                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == Keys.Up
                   && view.GetFocusedRow() != null
                   && (view.GetFocusedRow() as DataRowView).IsNew == true
                   && (view.GetFocusedRowCellValue("ItemId") == null || view.GetFocusedRowCellValue("ItemId").ToString() == string.Empty))
                {
                    view.DeleteRow(view.FocusedRowHandle);
                }

            }
            catch
            { }
        }

        private void gridView1_CustomColumnDisplayText(object sender, CustomColumnDisplayTextEventArgs e)
        { 

            if (e.Column.FieldName == "DiscountValue"
               || e.Column.FieldName == "TotalSellPrice"
               || e.Column.FieldName == "SellPrice"
               || e.Column.FieldName == "PurchasePrice"
               || e.Column.FieldName == "CurrentQty"
               || e.Column.FieldName == "SalesTax"
               || e.Column.FieldName == "CustomTax"
               || e.Column.FieldName == "salePriceWithTaxTable"
               || e.Column.FieldName == "TotalTaxes"
               || e.Column.FieldName == "tableTaxValue"
               || e.Column.FieldName == "EtaxValue")
            {
                if (e.Value != DBNull.Value && e.Value != null)
                    e.DisplayText = decimal.Round(Convert.ToDecimal(e.Value), defaultRoundingPoints).ToString($"F{defaultRoundingPoints}");
            }
            else if (e.Column.FieldName == "ManufactureDate")
            {
                if (e.Value != null && e.Value != DBNull.Value)
                    e.DisplayText = Convert.ToDateTime(e.Value).ToShortDateString();
            }
        }


        private void txtDiscountRatio_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (!char.IsNumber(e.KeyChar) && e.KeyChar != '.')
                e.Handled = e.KeyChar != (char)Keys.Back;
        }

        private void repUOM_CustomDisplayText(object sender, DevExpress.XtraEditors.Controls.CustomDisplayTextEventArgs e)
        {
            if (e.Value == null || e.Value == DBNull.Value)
                return;
            try
            {
                e.DisplayText = uom_list.Where(x => x.UOMId == Convert.ToInt32(e.Value)).FirstOrDefault().UOM;
            }
            catch
            {
            }
        }


        private void barBtnNew_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            invoiceId = userId = 0;
            invoice_remains = 0;
            LoadInvoice();
            lkp_Customers_EditValueChanged(null, EventArgs.Empty);
            txtNotes.Focus();
            FocusItemCode1(Shared.user.FocusGridInInvoices);
            GetNextCode();
        }

        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void batBtnList_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_SL_ReturnList)))
            {
                frm_SL_ReturnList frm = new frm_SL_ReturnList();
                frm.BringToFront();
                frm.Show();
            }
            else
                Application.OpenForms["frm_SL_ReturnList"].BringToFront();
        }

        private void barBtnDelete_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (invoiceId != 0)
            {
                var slReturn = DB.SL_Returns.FirstOrDefault(x => x.SL_ReturnId == invoiceId);
                if (slReturn.EstatusCode != 1) {
                    var slReturnDetail = DB.SL_ReturnDetails.Where(x => x.SL_ReturnId == invoiceId);
                    var slReturnTax = DB.SlReturnInvoiceDetailSubTaxValues.Where(x => slReturnDetail.Select(z => z.SL_ReturnDetailId).Contains(x.ReturnInvoiceDetailId));

                    DB.SlReturnInvoiceDetailSubTaxValues.DeleteAllOnSubmit(slReturnTax);
                    DB.SL_ReturnDetails.DeleteAllOnSubmit(slReturnDetail);
                    DB.SL_Returns.DeleteOnSubmit(slReturn);

                    DB.SubmitChanges();

                    MessageBox.Show("تم حذف الاشعار بنجاح");
                }
                else
                {
                    MessageBox.Show("لا يمكن حذف اشعار حدث له مزامنة من قبل");
                }
            }
        }

        private void barBtn_Save_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if ((grdPrInvoice.FocusedView as GridView) != null)
                ++(grdPrInvoice.FocusedView as GridView).FocusedRowHandle;
            grdPrInvoice.DefaultView.PostEditor();
            grdPrInvoice.DefaultView.UpdateCurrentRow();

            if (!ValidData())
                return;

            if (Shared.st_Store.LastEvaluationDate.HasValue && dtInvoiceDate.DateTime.Date > Shared.st_Store.LastEvaluationDate.Value.Date)
            {
                dtInvoiceDate.ErrorText = Shared.IsEnglish ? ResEn.LastEvaluationDateError : ResAr.LastEvaluationDateError;
                dtInvoiceDate.Focus();
                MessageBox.Show(Shared.IsEnglish ? ResEn.LastEvaluationDateError : ResAr.LastEvaluationDateError, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (invoiceId == 0)
            {
                Save_Invoice();
                XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResPrEn.MsgSave : ResPrAr.MsgSave, "", MessageBoxButtons.OK, MessageBoxIcon.Information);

                #region Ask_To_Create_InTrnsBill
                //if (Shared.InvoicePostToStore == false)
                //{
                //    if (XtraMessageBox.Show(Shared.IsEnglish == true ? ResSLEn.MsgAskCreateInTrns : ResSLAr.MsgAskCreateInTrns,
                //    Shared.IsEnglish == true ? ResSLEn.MsgTQues : ResSLAr.MsgTQues,
                //    MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) == DialogResult.Yes)
                //    {
                //        if (ErpUtils.IsFormOpen(typeof(frm_IC_InTrns)))
                //            Application.OpenForms["frm_IC_InTrns"].Close();

                //        if (ErpUtils.IsFormOpen(typeof(frm_IC_OutTrns)))
                //            Application.OpenForms["frm_IC_InTrns"].BringToFront();
                //        else
                //        {
                //            new frm_IC_InTrns((int)Process.SellReturn, invoiceId, txtInvoiceCode.Text).Show();
                //        }
                //    }
                //}
                #endregion
            }
            else
            {
                Save_Invoice();
                XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResPrEn.MsgSave : ResPrAr.MsgSave, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            invoice_remains = Convert.ToDouble(txt_Remains.EditValue);

            lkp_Customers_EditValueChanged(null, EventArgs.Empty);
        }


        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (ErpUtils.CheckDocumentSaved(invoiceId, DataModified, dtSLReturn_Details) == false)
                return;

            DataTable dt_PrintTable = new DataTable();
            dt_PrintTable.Columns.Add("ItemCode1");
            dt_PrintTable.Columns.Add("ItemCode2");
            dt_PrintTable.Columns.Add("DiscountValue");
            dt_PrintTable.Columns.Add("Expire");
            dt_PrintTable.Columns.Add("Batch");
            dt_PrintTable.Columns.Add("SellPrice");
            dt_PrintTable.Columns.Add("Qty");
            dt_PrintTable.Columns.Add("TotalSellPrice");
            dt_PrintTable.Columns.Add("ItemName");
            dt_PrintTable.Columns.Add("UOM");
            dt_PrintTable.Columns.Add("Height");
            dt_PrintTable.Columns.Add("Width");
            dt_PrintTable.Columns.Add("Length");
            dt_PrintTable.Columns.Add("TotalQty");
            dt_PrintTable.Columns.Add("ItemType");
            dt_PrintTable.Columns.Add("ItemDescription");
            dt_PrintTable.Columns.Add("ItemDescriptionEn");
            dt_PrintTable.Columns.Add("PiecesCount");

            dt_PrintTable.Columns.Add("SalesTax");
            dt_PrintTable.Columns.Add("DiscountRatio");
            dt_PrintTable.Columns.Add("DiscountRatio2");
            dt_PrintTable.Columns.Add("DiscountRatio3");
            dt_PrintTable.Columns.Add("SalesTaxRatio");
            dt_PrintTable.Columns.Add("Serial");
            dt_PrintTable.Columns.Add("PicPath");
            dt_PrintTable.Columns.Add("Serial2");
            dt_PrintTable.Columns.Add("ManufactureDate");

            dt_PrintTable.Columns.Add("Factor");
            dt_PrintTable.Columns.Add("MUOM");
            dt_PrintTable.Columns.Add("MUOM_Factor");

            dt_PrintTable.Columns.Add("Pack");

            dt_PrintTable.Columns.Add("addTaxValue");
            dt_PrintTable.Columns.Add("tableTaxValue");

            dt_PrintTable.Columns.Add("bonusDiscount");

            DataTable dt_PrintTableSubTaxDetails = new DataTable();
            dt_PrintTableSubTaxDetails.Columns.Add("SubTaxId");
            dt_PrintTableSubTaxDetails.Columns.Add("Rate");
            dt_PrintTableSubTaxDetails.Columns.Add("Value");
            var viewSubTaxes = grd_SubTaxes.FocusedView as GridView;
            for (int i = 0; i < viewSubTaxes.RowCount; i++)
            {
                if (viewSubTaxes.GetRowCellDisplayText(i, "SubTaxId") == string.Empty)
                    continue;

                DataRow dr = dt_PrintTableSubTaxDetails.NewRow();
                dr["SubTaxId"] = viewSubTaxes.GetRowCellDisplayText(i, "SubTaxId");
                dr["Rate"] = viewSubTaxes.GetRowCellDisplayText(i, "Rate");
                dr["Value"] = viewSubTaxes.GetRowCellDisplayText(i, "Value"); //mahmoud:18-12-2012, naser azemi issue                
                dt_PrintTableSubTaxDetails.Rows.Add(dr);

            }

            var view = grdPrInvoice.FocusedView as GridView;
            for (int i = 0; i < view.RowCount; i++)
            {
                if (view.GetRowCellDisplayText(i, "ItemCode1") == string.Empty)
                    continue;

                DataRow dr = dt_PrintTable.NewRow();
                dr["ItemCode1"] = view.GetRowCellDisplayText(i, "ItemCode1");
                dr["ItemCode2"] = view.GetRowCellDisplayText(i, "ItemCode2");
                dr["DiscountValue"] = view.GetRowCellDisplayText(i, "DiscountValue") == "0" ? "" : view.GetRowCellDisplayText(i, "DiscountValue"); //mahmoud:18-12-2012, naser azemi issue
                dr["Expire"] = view.GetRowCellDisplayText(i, "Expire");
                dr["Batch"] = view.GetRowCellDisplayText(i, "Batch");
                dr["SellPrice"] = view.GetRowCellDisplayText(i, "SellPrice");
                dr["Qty"] = view.GetRowCellDisplayText(i, "Qty");
                dr["TotalSellPrice"] = view.GetRowCellDisplayText(i, "TotalSellPrice");
                dr["ItemName"] = view.GetRowCellDisplayText(i, "ItemId");
                dr["UOM"] = view.GetRowCellDisplayText(i, "UOM");
                dr["Height"] = view.GetRowCellDisplayText(i, "Height");
                dr["Width"] = view.GetRowCellDisplayText(i, "Width");
                dr["Length"] = view.GetRowCellDisplayText(i, "Length");
                dr["TotalQty"] = view.GetRowCellDisplayText(i, "TotalQty");
                dr["ItemType"] = view.GetRowCellValue(i, "ItemType");
                dr["ItemDescription"] = view.GetRowCellDisplayText(i, "ItemDescription");
                dr["ItemDescriptionEn"] = view.GetRowCellDisplayText(i, "ItemDescriptionEn");
                dr["PiecesCount"] = view.GetRowCellDisplayText(i, "PiecesCount");

                dr["SalesTax"] = view.GetRowCellDisplayText(i, "SalesTax");
                dr["DiscountRatio"] = view.GetRowCellDisplayText(i, "DiscountRatio");
                dr["DiscountRatio2"] = view.GetRowCellDisplayText(i, "DiscountRatio2");
                dr["DiscountRatio3"] = view.GetRowCellDisplayText(i, "DiscountRatio3");
                dr["SalesTaxRatio"] = view.GetDataRow(i)["SalesTaxRatio"];
                dr["PicPath"] = ErpUtils.GetGridLookUpValue(repItems, view.GetRowCellValue(i, "ItemId"), "PicPath");
                dr["Serial"] = view.GetRowCellDisplayText(i, "Serial");
                dr["Serial2"] = view.GetRowCellDisplayText(i, "Serial2");
                dr["ManufactureDate"] = view.GetRowCellDisplayText(i, "ManufactureDate");
                dr["bonusDiscount"] = view.GetRowCellDisplayText(i, "bonusDiscount");
                if (Convert.ToInt32(view.GetRowCellValue(i, "UomIndex")) == 0)
                    dr["Factor"] = 1;
                else if (Convert.ToInt32(view.GetRowCellValue(i, "UomIndex")) == 1)
                    dr["Factor"] = view.GetRowCellDisplayText(i, "MediumUOMFactor");
                else if (Convert.ToInt32(view.GetRowCellValue(i, "UomIndex")) == 2)
                    dr["Factor"] = view.GetRowCellDisplayText(i, "LargeUOMFactor");

                if (ErpUtils.GetGridLookUpValue(repItems, view.GetRowCellValue(i, "ItemId"), "MediumUOM") != null)
                {
                    dr["MUOM"] = uom_list.Where(x => x.UOMId == Convert.ToInt32(ErpUtils.GetGridLookUpValue(repItems, view.GetRowCellValue(i, "ItemId"), "MediumUOM"))).Select(x => x.UOM).FirstOrDefault();
                    dr["MUOM_Factor"] = ErpUtils.GetGridLookUpValue(repItems, view.GetRowCellValue(i, "ItemId"), "MediumUOMFactor");
                }
                else
                {
                    dr["MUOM"] = dr["UOM"];
                    dr["MUOM_Factor"] = dr["Factor"];
                }

                dr["Pack"] = view.GetRowCellValue(i, "Pack");

                dr["addTaxValue"] = view.GetRowCellDisplayText(i, "addTaxValue");
                dr["tableTaxValue"] = view.GetRowCellDisplayText(i, "tableTaxValue");

                dt_PrintTable.Rows.Add(dr);
            }
            Reports.rpt_SL_ReturnInvoice r = new Reports.rpt_SL_ReturnInvoice
                (lkp_Customers.Text, "", txtInvoiceCode.Text, dtInvoiceDate.Text, lkpStore.Text,
                cmbPayMethod.Text, lkp_Drawers.Text, txtNotes.Text, txt_Total.Text, txt_TaxValue.Text, txtDiscountRatio.Text,
                txtDiscountValue.Text, txtExpenses.Text, txtNet.Text, txt_paid.Text, txt_Remains.Text, dt_PrintTable,
                                Shared.lst_Users.Where(x => x.UserId == userId).Select(x => x.Name).FirstOrDefault(),
                txt_DeductTaxV.Text, txt_AddTaxV.Text, Convert.ToInt32(uc_Currency1.lkp_Crnc.EditValue),
                "", "", "", "",
                txt_AddTaxV.Text, txt_CusTaxV.Text, "", " " ," ", Multiple_Data, dt_PrintTableSubTaxDetails,txt_EtaxValue.Text);

            string TemplateName = "rpt_SL_ReturnInvoice";
            
            if (System.IO.File.Exists(Shared.ReportsPath + TemplateName + ".repx"))
                r.LoadLayout(Shared.ReportsPath + TemplateName + ".repx");

            r.LoadData();

            if (Shared.user.ShowPrintPreview == false)
                r.PrintDialog();
            else
                r.ShowPreview();

        }

        private void btnAddCustomer_Click(object sender, EventArgs e)
        {
            int customers_count = lkp_Customers.Properties.View.RowCount;
            int LastCustId = 0;

            new frm_SL_Customer().ShowDialog();
            int? goldencustomer = MyHelper.GetCustomers(out lst_Customers, Shared.user.DefaultCustGrp, out LastCustId);
            if (lst_Customers.Count > customers_count)
            {
                lkp_Customers.Properties.DataSource = lst_Customers;
                //lkp_Customers.EditValue = lkp_Customers.Properties.GetKeyValue(lkp_Customers.Properties.View.RowCount - 1);
                lkp_Customers.EditValue = LastCustId;
            }
        }



        private void lkp_Customers_Modified(object sender, EventArgs e)
        {
            DataModified = true;
        }

        private void Get_Items()
        {
            #region Get Items
            DB = new ERPDataContext();
            
                lstItems = (from i in DB.IC_Items
                            where i.ItemType != (int)ItemType.MatrixParent

                            where Shared.st_Store.SellRawMaterial == false ?
                                                           i.ItemType == (int)ItemType.Assembly : true
                            where invoiceId == 0 ? i.IsDeleted == false : true
                         
                            select new ItemLkp
                            {
                                ItemCode1 = i.ItemCode1,
                                ItemCode2 = i.ItemCode2,
                                ItemId = i.ItemId,
                                ItemNameAr = i.ItemNameAr,
                                ItemNameEn = i.ItemNameEn,
                                MaxQty = i.MaxQty,
                                MinQty = i.MinQty,
                                ReorderLevel = i.ReorderLevel,
                                IsExpire = i.IsExpire,
                                PurchasePrice = i.PurchasePrice,
                                SellPrice = i.SmallUOMPrice,
                                PicPath = i.PicPath,
                                MediumUOM = i.MediumUOM,
                                LargeUOM = i.LargeUOM,
                                CategoryNameAr = i.IC_Category.CategoryNameAr,
                                //CompanyNameAr = i.IC_Company.CompanyNameAr,
                                MediumUOMFactorDecimal = i.MediumUOMFactorDecimal,
                                LargeUOMFactorDecimal = i.LargeUOMFactorDecimal
                            }).ToList();

            repItems.DataSource = lstItems;
            repItems.DisplayMember = Shared.IsEnglish ? "ItemNameEn" : "ItemNameAr";
            repItems.ValueMember = "ItemId";
            #endregion
        }

        void grd_FocusOnItemId(string columnName)
        {
            GridView view = grdPrInvoice.FocusedView as GridView;
            view.FocusedColumn = view.Columns[columnName];

        }

        private void Reset()
        {
            chk_IsInTrns.Checked = false;

            txtNotes.EditValue = null;
            txtExpenses.EditValue = txtDiscountRatio.EditValue = txtDiscountValue.EditValue = 0;

            txt_TaxValue.EditValue = 0;
            txt_DeductTaxR.EditValue = txt_DeductTaxV.EditValue = 0;
            txt_AddTaxR.EditValue = txt_AddTaxV.EditValue = 0;

            txt_Total.EditValue = 0.0;
            txt_paid.EditValue = 0.0;
            txtNet.EditValue = 0.0;
            txt_PayAcc1_Paid.EditValue = 0.0;
            txt_PayAcc2_Paid.EditValue = 0.0;
            lkp_Drawers2.EditValue = null;

            txt_Remains.EditValue = 0.0;


            dtInvoiceDate.DateTime = MyHelper.Get_Server_DateTime();

            dtSLReturn_Details.Rows.Clear();

            cmbPayMethod.EditValue = Shared.user.SL_Return_PayMethod;

            txtInvoiceCode.ResetBackColor();
            txtInvoiceCode.ToolTipIconType = DevExpress.Utils.ToolTipIconType.None;
            txtInvoiceCode.ToolTip = string.Empty;
            txtInvoiceCode.ErrorText = string.Empty;
            txt_EtaxValue.Text =string.Empty;
            Multiple_Data.Rows.Clear();
            dt_Multi_CC.Rows.Clear();
            int defaultStoreId = 0;
            rowhandle = 0;
            Dt_Rows.Rows.Clear();
            dt_SubTax.Rows.Clear();
        }

        private void BindDataSources()
        {
            DB = new DAL.ERPDataContext();
            //disable on credit invoice
            if (Shared.user.UserMakeOnCreditInv == false)
                txt_paid.Enabled = txt_Remains.Enabled = false;

            if (Shared.ItemsPostingAvailable)
            {
                if (Shared.StockIsPeriodic)
                {
                    lst_Cat = MyHelper.GetChildCategoriesList_Periodic();
                    if (lst_Cat.Where(x => x.SellAcc.HasValue == false || x.SellReturnAcc.HasValue == false
                                   || x.PurchaseAcc.HasValue == false || x.PurchaseReturnAcc.HasValue == false
                                   || x.OpenInventoryAcc.HasValue == false || x.CloseInventoryAcc.HasValue == false).Count() > 0)
                    {
                        XtraMessageBox.Show(
                            Shared.IsEnglish == true ? ResEn.ItemPosting : ResAr.ItemPosting,
                            Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        this.BeginInvoke(new MethodInvoker(this.Close));
                    }
                }
                else
                {
                    lst_Cat = MyHelper.GetChildCategoriesList();
                    if (lst_Cat.Where(x => x.SellAcc.HasValue == false || x.SellReturnAcc.HasValue == false || x.COGSAcc.HasValue == false ||
                        x.InvAcc.HasValue == false).Count() > 0)
                    {
                        XtraMessageBox.Show(
                            Shared.IsEnglish == true ? ResEn.ItemPosting : ResAr.ItemPosting,
                            Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        this.BeginInvoke(new MethodInvoker(this.Close));
                    }
                }
            }


            //================================//'

            dt_Multi_CC.Columns.Clear();
            dt_Multi_CC.Columns.Add("CostCenterId");
            dt_Multi_CC.Rows.Clear();
            //============================//
            #region Get Stores
            int defaultStoreId = 0;

            //if (Shared.InvoicePostToStore)
            //    stores_table = MyHelper.Get_Stores(true, out defaultStoreId, Shared.user.UserChangeStore, Shared.user.DefaultStore,
            //    Shared.UserId);
            //else
            //    stores_table = MyHelper.Get_Stores(false, out defaultStoreId, Shared.user.UserChangeStore, Shared.user.DefaultStore,
            //    Shared.UserId);
            if (Shared.InvoicePostToStore)
                stores_table = MyHelper.Get_StoresNotStopped(0,true, out defaultStoreId, Shared.user.UserChangeStore, Shared.user.DefaultStore,
                Shared.UserId);
            else
                stores_table = MyHelper.Get_StoresNotStopped(0,false, out defaultStoreId, Shared.user.UserChangeStore, Shared.user.DefaultStore,
                Shared.UserId);

            lkpStore.Properties.DataSource = stores_table;

            lkpStore.Properties.DisplayMember = "StoreNameAr";
            lkpStore.Properties.ValueMember = "StoreId";
            //lkpStore.EditValue = defaultStoreId;
            #endregion
/*
            #region LoadInvoicesBooks
            var empty_book = new
            {
                InvoiceBookId = (int?)null,
                InvoiceBookName = "",
                IsTaxable = (bool?)null,
                PrintFileName = (string)null,
            };

            var books = (from b in DB.ST_InvoiceBooks
                         where b.ProcessId == (int)Process.SellReturn
                         select new
                         {
                             InvoiceBookId = (int?)b.InvoiceBookId,
                             b.InvoiceBookName,
                             b.IsTaxable,
                             b.PrintFileName,
                         }).ToList();

            if (books.Count == 0)
                pnlBook.Visible = false;

            books.Insert(0, empty_book);
            lkp_InvoiceBook.Properties.DataSource = books;
            lkp_InvoiceBook.Properties.DisplayMember = "InvoiceBookName";
            lkp_InvoiceBook.Properties.ValueMember = "InvoiceBookId";
            lkp_InvoiceBook.EditValue = Shared.user.DefaultSLRet_InvBookId;
            #endregion
*/
            Get_Items();

            //#region Get Cost Centers
            //lkpCostCenter.Properties.DataSource = HelperAcc.GetCostCentersLst(true);
            //lkpCostCenter.Properties.DisplayMember = "CostCenterName";
            //lkpCostCenter.Properties.ValueMember = "CostCenterId";

            //#endregion

            //#region Get_PayAccounts
            //int defaultAcc = HelperAcc.LoadPayAccounts(dtPayAccounts, Shared.user.UserChangeDrawer, Shared.user.DefaultDrawer,
            //    Shared.IsEnglish);

            //lkp_Drawers.Properties.ValueMember = "AccountId";
            //lkp_Drawers.Properties.DisplayMember = "AccountName";
            //lkp_Drawers.EditValue = defaultAcc;
            //lkp_Drawers.Properties.DataSource = dtPayAccounts;

            //lkp_Drawers2.Properties.ValueMember = "AccountId";
            //lkp_Drawers2.Properties.DisplayMember = "AccountName";
            //lkp_Drawers2.EditValue = null;
            //lkp_Drawers2.Properties.DataSource = dtPayAccounts;
            //#endregion

            #region dtPR_Details
            dtSLReturn_Details.Columns.Clear();
            dtSLReturn_Details.Columns.Add("PR_InvoiceDetailId");
            dtSLReturn_Details.Columns.Add("PR_InvoiceId");
            dtSLReturn_Details.Columns.Add("ItemId");
            dtSLReturn_Details.Columns.Add("ItemIdF");
            dtSLReturn_Details.Columns.Add("ItemCode1");
            dtSLReturn_Details.Columns.Add("ItemCode2");
            dtSLReturn_Details.Columns.Add("ItemType").DefaultValue = 0;
            dtSLReturn_Details.Columns.Add("UOM");
            dtSLReturn_Details.Columns.Add("Qty").DefaultValue = 1;
            dtSLReturn_Details.Columns.Add("PurchasePrice").DefaultValue = 0;
            dtSLReturn_Details.Columns.Add("SellPrice").DefaultValue = 0;
            dtSLReturn_Details.Columns.Add("DiscountValue").DefaultValue = 0;
            dtSLReturn_Details.Columns.Add("DiscountRatio").DefaultValue = 0;
            dtSLReturn_Details.Columns.Add("DiscountRatio2").DefaultValue = 0;
            dtSLReturn_Details.Columns.Add("DiscountRatio3").DefaultValue = 0;
            dtSLReturn_Details.Columns.Add("TotalSellPrice").DefaultValue = 0;
            dtSLReturn_Details.Columns.Add("CurrentQty");

            dtSLReturn_Details.Columns.Add("MediumUOMFactor");
            dtSLReturn_Details.Columns.Add("LargeUOMFactor");

            dtSLReturn_Details.Columns.Add("CompanyNameAr");
            dtSLReturn_Details.Columns.Add("UomIndex");
            dtSLReturn_Details.Columns.Add("VendorId");

            dtSLReturn_Details.Columns.Add("Length").DefaultValue = 1;
            dtSLReturn_Details.Columns.Add("Width").DefaultValue = 1;
            dtSLReturn_Details.Columns.Add("Height").DefaultValue = 1;
            dtSLReturn_Details.Columns.Add("PiecesCount").DefaultValue = 0;

            dtSLReturn_Details.Columns.Add("SalesTax").DefaultValue = 0;
            dtSLReturn_Details.Columns.Add("SalesTaxRatio").DefaultValue = 0;

            //fayza custom tax
            dtSLReturn_Details.Columns.Add("CustomTaxRatio").DefaultValue = 0;
            dtSLReturn_Details.Columns.Add("CustomTax").DefaultValue = 0;

            dtSLReturn_Details.Columns.Add("ItemDescription");
            dtSLReturn_Details.Columns.Add("ItemDescriptionEn");

            dtSLReturn_Details.Columns.Add("ParentItemId");
            dtSLReturn_Details.Columns.Add("M1");
            dtSLReturn_Details.Columns.Add("M2");
            dtSLReturn_Details.Columns.Add("M3");
            dtSLReturn_Details.Columns.Add("Serial");
            dtSLReturn_Details.Columns.Add("Serial2");

            //for Elm Dawa2..Doesn't affect Qty, Mohammad 17-03-2018 
            dtSLReturn_Details.Columns.Add("ManufactureDate");

            #region Expire
            dtSLReturn_Details.Columns.Add("Expire");
            dtSLReturn_Details.Columns.Add("Batch");
            dtSLReturn_Details.Columns.Add("IsExpire");
            #endregion

            dtSLReturn_Details.Columns.Add("calcTaxBeforeDisc");

            dtSLReturn_Details.Columns.Add("CategoryId");

            dtSLReturn_Details.Columns.Add("LibraQty");
            dtSLReturn_Details.Columns.Add("IsOffer");
            dtSLReturn_Details.Columns.Add("PricingWithSmall");
            dtSLReturn_Details.Columns.Add("VariableWeight");
            dtSLReturn_Details.Columns.Add("kg_Weight_libra");
            dtSLReturn_Details.Columns.Add("Is_Libra");
            dtSLReturn_Details.Columns.Add("Pack");
            dtSLReturn_Details.Columns.Add("bonusDiscount");
            dtSLReturn_Details.Columns.Add("TaxType");
            dtSLReturn_Details.Columns.Add("ETaxRatio");
            dtSLReturn_Details.Columns.Add("EtaxValue");
            dtSLReturn_Details.Columns.Add("TotalTaxes", typeof(decimal));
            dtSLReturn_Details.Columns.Add("totalTaxesRatio", typeof(decimal));
            dtSLReturn_Details.Columns.Add("RowHandle", typeof(int));

            dtSLReturn_Details.Columns.Add("salePriceWithTaxTable");
            dtSLReturn_Details.Columns.Add("totalTableTaxes");

            dtSLReturn_Details.Columns.Add("addTaxValue");
            dtSLReturn_Details.Columns.Add("tableTaxValue");
            dtSLReturn_Details.Columns.Add("TotalSubDiscountTax");
            dtSLReturn_Details.Columns.Add("TotalSubCustomTax");
            dtSLReturn_Details.Columns.Add("TotalSubAddTax");
            dtSLReturn_Details.Columns.Add("TaxValue");

            dtSLReturn_Details.TableNewRow += new DataTableNewRowEventHandler(dt_TableNewRow);
            dtSLReturn_Details.RowDeleted += new DataRowChangeEventHandler(dt_RowChanged);
            dtSLReturn_Details.RowChanged += new DataRowChangeEventHandler(dt_RowChanged);
            grdPrInvoice.DataSource = dtSLReturn_Details;
            #endregion

            //#region Get Vendors
            //int lastVenId = 0;
            //int? goldenVendor = MyHelper.GetVendors(out lst_Vendors, out lastVenId);
            //rep_vendors.DisplayMember = "VenNameAr";
            //rep_vendors.ValueMember = "VendorId";
            //rep_vendors.DataSource = lst_Vendors;
            //#endregion


            #region GetCustomers
            int LastCustId = 0;
            int? goldencustomer = MyHelper.GetCustomers(out lst_Customers, Shared.user.DefaultCustGrp, out LastCustId);

            lkp_Customers.Properties.DisplayMember = "CusNameAr";
            lkp_Customers.Properties.ValueMember = "CustomerId";
            lkp_Customers.Properties.DataSource = lst_Customers;
            lkp_Customers.EditValue = goldencustomer;
            #endregion

            #region UOM
            uom_list = DB.IC_UOMs.ToList();
            repUOM.DisplayMember = "Uom";
            repUOM.ValueMember = "UomId";
            repUOM.DataSource = MyHelper.GetUomDataTable(dtUOM);
            #endregion

            if (Multiple_Data.Columns.Count < 1)
            {
                Multiple_Data.Columns.Add("Code1", typeof(int));
                Multiple_Data.Columns.Add("item");
                Multiple_Data.Columns.Add("Count", typeof(int));
                Multiple_Data.Columns.Add("Weight", typeof(decimal));
                Multiple_Data.Columns.Add("Libra", typeof(decimal));
            }
            #region Get SubTax Types
            var taxTypes = DB.E_TaxableTypes.Where(x => x.ParentTaxId != null);
            repTaxTypes.DataSource = taxTypes;
            repTaxTypes.ValueMember = "E_TaxableTypeId";
            repTaxTypes.DisplayMember = "Code";
            #endregion
            #region Taxes
            if (Dt_Rows.Columns.Count < 1)
            {
                Dt_Rows.Columns.Add("ItemId", typeof(int));
                Dt_Rows.Columns.Add("SellPrice", typeof(decimal));
                Dt_Rows.Columns.Add("Qty", typeof(decimal));
                Dt_Rows.Columns.Add("Tax");
                Dt_Rows.Columns.Add("SubTax");
                Dt_Rows.Columns.Add("Percentage");
                Dt_Rows.Columns.Add("TaxValue");
                Dt_Rows.Columns.Add("RowHandle", typeof(int));
            }
            #endregion

            tableTax.Add("T2");
            tableTax.Add("T3");
            tableTax.Add("T5");
            tableTax.Add("T6");
            tableTax.Add("T7");
            tableTax.Add("T8");
            tableTax.Add("T9");
            tableTax.Add("T10");
            tableTax.Add("T11");
            tableTax.Add("T12");


            #region SubTax
            lkp_SubTaxes.DataSource = DB.E_TaxableTypes.Where(x => x.ParentTaxId != null).ToList();
            lkp_SubTaxes.ValueMember = "E_TaxableTypeId";
            lkp_SubTaxes.DisplayMember = "DescriptionAr";
            dt_SubTax.Columns.Add("SubTaxId");
            dt_SubTax.Columns.Add("ItemId");
            dt_SubTax.Columns.Add("Rate");
            dt_SubTax.Columns.Add("Value");
            grd_SubTaxes.DataSource = dt_SubTax;
            #endregion
        }

        private void LoadItemRow(DAL.IC_Item item, DataRow row)
        {


            if (item != null && item.ItemId > 0)
            {
                row["ItemId"] = item.ItemId;
                row["ItemIdF"] = Shared.IsEnglish ? item.ItemNameAr : item.ItemNameEn;
                row["CategoryId"] = item.Category;
                row["ItemCode1"] = item.ItemCode1;
                row["ItemCode2"] = item.ItemCode2;
                row["ItemType"] = item.ItemType;
                row["PurchasePrice"] = Decimal.ToDouble(item.PurchasePrice);

                MyHelper.GetUOMs(item, dtUOM, uom_list);
                row["UOM"] = dtUOM.Rows[item.DfltSellUomIndx]["UomId"];
                row["UomIndex"] = item.DfltSellUomIndx;

                decimal uom_price = MyHelper.GetPriceLevelSellPrice(CustomerPriceLevel, item, item.DfltSellUomIndx);
                row["SellPrice"] = decimal.ToDouble(uom_price);

                if (frm_InvoiceDiscs.SLRet_DiscR1 > 0 ||
                    frm_InvoiceDiscs.SLRet_DiscR2 > 0 ||
                    frm_InvoiceDiscs.SLRet_DiscR3 > 0)
                {
                    if (frm_InvoiceDiscs.SLRet_DiscR1 > 0)
                        row["DiscountRatio"] = decimal.ToDouble(frm_InvoiceDiscs.SLRet_DiscR1);
                    if (frm_InvoiceDiscs.SLRet_DiscR2 > 0)
                        row["DiscountRatio2"] = decimal.ToDouble(frm_InvoiceDiscs.SLRet_DiscR2);
                    if (frm_InvoiceDiscs.SLRet_DiscR3 > 0)
                        row["DiscountRatio3"] = decimal.ToDouble(frm_InvoiceDiscs.SLRet_DiscR3);
                }
                else
                {
                    row["DiscountRatio"] = decimal.ToDouble(item.SalesDiscRatio / 100);
                    row["DiscountRatio2"] = decimal.ToDouble(0);
                    row["DiscountRatio3"] = decimal.ToDouble(0);
                }

                row["DiscountValue"] = decimal.ToDouble(Utilities.Calc_DiscountValue(Convert.ToDecimal(row["DiscountRatio"]),
                    Convert.ToDecimal(row["DiscountRatio2"]), Convert.ToDecimal(row["DiscountRatio3"]), Convert.ToDecimal(row["SellPrice"])));

                if (IsTaxable == null && Convert.ToBoolean(ErpUtils.GetGridLookUpValue(lkp_Customers, lkp_Customers.EditValue, "IsTaxable")))
                {
                    row["SalesTaxRatio"] = item.SalesTaxRatio / 100;
                    row["CustomTaxRatio"] = item.CustomSalesTaxRatio / 100;
                }
                else if (IsTaxable == true)
                {
                    row["SalesTaxRatio"] = item.SalesTaxRatio / 100;
                    row["CustomTaxRatio"] = item.CustomSalesTaxRatio / 100;
                }
                else
                {
                    row["SalesTaxRatio"] = 0;
                    row["CustomTaxRatio"] = 0;
                }

                row["calcTaxBeforeDisc"] = item.calcTaxBeforeDisc;

                row["Qty"] = "1";

                row["Length"] = Decimal.ToDouble(item.Length);
                row["Width"] = Decimal.ToDouble(item.Width);
                row["Height"] = Decimal.ToDouble(item.Height);

                if (frm_InvoiceDimenstions.SLRet_Height > 0)
                    row["Height"] = decimal.ToDouble(frm_InvoiceDimenstions.SLRet_Height);
                if (frm_InvoiceDimenstions.SLRet_Width > 0)
                    row["Width"] = decimal.ToDouble(frm_InvoiceDimenstions.SLRet_Width);
                if (frm_InvoiceDimenstions.SLRet_Length > 0)
                    row["Length"] = decimal.ToDouble(frm_InvoiceDimenstions.SLRet_Length);

                row["TotalSellPrice"] = "0";
                row["MediumUOMFactor"] = MyHelper.FractionToDouble(item.MediumUOMFactor);
                row["LargeUOMFactor"] = MyHelper.FractionToDouble(item.LargeUOMFactor);



                if (item.DfltSellUomIndx == 0)
                    row["PurchasePrice"] = Decimal.ToDouble(item.PurchasePrice);
                else if (item.DfltSellUomIndx == 1)
                    row["PurchasePrice"] = Decimal.ToDouble(item.PurchasePrice) * decimal.ToDouble(MyHelper.FractionToDouble(item.MediumUOMFactor));
                else if (item.DfltSellUomIndx == 2)
                    row["PurchasePrice"] = Decimal.ToDouble(item.PurchasePrice) * decimal.ToDouble(MyHelper.FractionToDouble(item.LargeUOMFactor));

                row["IsExpire"] = item.IsExpire;

                //get comapny name
                DB = new DAL.ERPDataContext();
                //var compName = DB.IC_Companies.Where(c => c.CompanyId == item.Company).Select(c => c.CompanyNameAr).Single();
                //row["CompanyNameAr"] = compName;

                //decimal currentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, item.ItemId, Convert.ToInt32(lkpStore.EditValue));
                //currentQty = MyHelper.getCalculatedUomQty(currentQty, Convert.ToByte(row["UomIndex"]), MyHelper.FractionToDouble(item.MediumUOMFactor), MyHelper.FractionToDouble(item.LargeUOMFactor));
                //row["CurrentQty"] = decimal.ToDouble(currentQty);

                row["ItemDescription"] = item.Description;
                row["ItemDescriptionEn"] = item.DescriptionEn;

                row["ParentItemId"] = item.mtrxParentItem;
                row["M1"] = item.mtrxAttribute1;
                row["M2"] = item.mtrxAttribute2;
                row["M3"] = item.mtrxAttribute3;

                row["IsOffer"] = item.IsOffer;
                row["PricingWithSmall"] = item.PricingWithSmall;
                row["VariableWeight"] = item.VariableWeight;
                row["Is_Libra"] = item.is_libra;
                row["bonusDiscount"] = 0;
                //row["ETaxRatio"] = 0;
                //===============Update==================//
                row["RowHandle"] = ++rowhandle;
                var SubTaxes = DB.IC_ItemSubTaxes.Where(a => a.ItemId == item.ItemId).ToList();
                if (Shared.E_invoiceAvailable == true || Shared.st_Store.E_AllowMoreThanTax == true)
                {

                    setETaxE_Invoice(SubTaxes, row, item);

                }
                else
                {
                    if (SubTaxes.Count > 0)
                    {
                        var subTaxItem = SubTaxes.FirstOrDefault();
                        row["ETaxRatio"] = subTaxItem.Rate;
                        row["TaxType"] = subTaxItem.SubTaxId;
                    }

                }
            }
        }


        double invoice_remains = 0;
        private void GetInvoice(int invId)
        {
            DB = new DAL.ERPDataContext();

            var inv = DB.SL_Returns.Where(v => v.SL_ReturnId == invId).SingleOrDefault();
            if (inv != null)
            {
                invoice_remains = decimal.ToDouble(inv.Remains);
                lkp_Drawers.EditValue = inv.DrawerAccountId.ToString();
                lkpStore.EditValue = inv.StoreId;
                lkp_Customers.EditValue = inv.CustomerId;
                txtInvoiceCode.Text = inv.ReturnCode;
                dtInvoiceDate.EditValue = inv.ReturnDate;
            
                //lkpCostCenter.EditValue = inv.CostCenterId;

                //dt_Multi_CC.Rows.Clear();
                //if (inv.CostCenterId != null)
                //{
                //    dt_Multi_CC.Rows.Add(inv.CostCenterId);
                //}
                //MyHelper.FillMultiCC(ref dt_Multi_CC, invId, (int)Process.SellReturn);


                txtNotes.Text = inv.Notes;

                chk_IsInTrns.Checked = (inv.Is_InTrans == true);

                uc_Currency1.lkp_Crnc.EditValue = inv.CrncId;
                uc_Currency1.txtRate.EditValue = inv.CrncRate;
                uUId = inv.uuid;

                GetInvoiceDetails(invId);

                txtExpenses.EditValue = decimal.ToDouble(inv.Expenses).ToString($"F{defaultRoundingPoints}");

                txtDiscountRatio.EditValue = decimal.ToDouble(decimal.Round(inv.DiscountRatio * 100,defaultRoundingPoints));
                txt_DeductTaxR.EditValue = decimal.ToDouble(decimal.Round(inv.DeductTaxRatio * 100, defaultRoundingPoints));
                txt_AddTaxR.EditValue = decimal.ToDouble(decimal.Round(inv.AddTaxRatio * 100, defaultRoundingPoints));

                txtDiscountValue.EditValue = decimal.ToDouble(inv.DiscountValue);
                txt_TaxValue.EditValue = decimal.ToDouble(inv.TaxValue);
                txt_DeductTaxV.EditValue = decimal.ToDouble(inv.DeductTaxValue);
                txt_AddTaxV.EditValue = decimal.ToDouble(inv.AddTaxValue);

                txt_Total.EditValue = decimal.ToDouble(inv.Net - inv.TaxValue - inv.Expenses + inv.DiscountValue);
                txtNet.EditValue = decimal.ToDouble(inv.Net).ToString($"F{defaultRoundingPoints}");
                txt_PayAcc1_Paid.EditValue = decimal.ToDouble(inv.Paid).ToString($"F{defaultRoundingPoints}");

                if (inv.PayAccountId2.HasValue)
                    lkp_Drawers2.EditValue = inv.PayAccountId2.ToString();
                else
                    lkp_Drawers2.EditValue = null;

                if (inv.PayAcc2_Paid.HasValue)
                    txt_PayAcc2_Paid.EditValue = decimal.ToDouble(inv.PayAcc2_Paid.Value).ToString($"F{defaultRoundingPoints}");
                else
                    txt_PayAcc2_Paid.EditValue = 0;
                txt_Remains.EditValue = decimal.ToDouble(inv.Remains).ToString($"F{defaultRoundingPoints}");

                cmbPayMethod.EditValue = inv.PayMethod;

                if (inv.Estatus == "Valid")
                    barBtnSave.Enabled = false;
                else
                    barBtnSave.Enabled = true;

                if (Shared.E_invoiceAvailable)
                    barBtnSave.Enabled = false;
            }
            else
                invoice_remains = 0;
            Get_TotalAccount();
        }

        public void getTaxColumns(List<int> details)
        {
            var subTaxesList = DB.SlReturnInvoiceDetailSubTaxValues.Where(a => details.Contains(a.ReturnInvoiceDetailId)).Select(a => a.esubTypeId).Distinct().ToList();
            var count = 1;
            foreach (var item in subTaxesList)
            {
                var view = (grdPrInvoice.FocusedView as DevExpress.XtraGrid.Views.Grid.GridView);
                string ETaxType = "ETaxType" + item.ToString();
                dtSLReturn_Details.Columns.Add(ETaxType);
                GridColumn columnETaxType = new GridColumn();
                columnETaxType.FieldName = ETaxType;
                columnETaxType.Visible = true;
                columnETaxType.OptionsColumn.ReadOnly = true;
                columnETaxType.Caption = "نوع الضريبة" + count.ToString();
                view.Columns.Add(columnETaxType);
                //==================================
                string ETaxRatio = "ETaxRatio" + item.ToString();
                dtSLReturn_Details.Columns.Add(ETaxRatio);
                GridColumn columnETaxRatio = new GridColumn();
                columnETaxRatio.FieldName = ETaxRatio;
                columnETaxRatio.Visible = true;
                columnETaxRatio.OptionsColumn.ReadOnly = true;
                columnETaxRatio.Caption = "نسبة الضريبة" + count.ToString();
                view.Columns.Add(columnETaxRatio);
                //==================================
                string ETaxValue = "ETaxValue" + item.ToString();
                dtSLReturn_Details.Columns.Add(ETaxValue);
                GridColumn columnETaxValue = new GridColumn();
                columnETaxValue.FieldName = ETaxValue;
                columnETaxValue.Visible = true;
                columnETaxValue.OptionsColumn.ReadOnly = true;
                columnETaxValue.Caption = "قيمة الضريبة" + count.ToString();
                view.Columns.Add(columnETaxValue);

                count++;
                //colETaxValue.Visible = false;
                //colETaxRatio.Visible = false;
                //Col_ETaxType.Visible = false;
                //TotalTaxes.Visible = false;
                //totalTaxesRatio.Visible = false;
                //btn_AddTaxes.Visible = false;

            }
        }
        public void getETaxDetailSl_InvoiceForErp(int detailId, DataRow row)
        {

            var etax = DB.SL_InvoiceDetailSubTaxValues.Where(x => x.InvoiceDetailId == detailId).ToList();
            if (etax.Count != 0)
            {
                foreach (var item in etax)
                {
                    row["ETaxType" + item.esubTypeId.ToString()] = DB.E_TaxableTypes.Where(a => a.E_TaxableTypeId == item.esubTypeId).Select(a => a.Code).FirstOrDefault();
                    row["ETaxRatio" + item.esubTypeId.ToString()] = item.TaxRatio != null ? decimal.ToDouble(item.TaxRatio.Value) : 0;
                    row["ETaxValue" + item.esubTypeId.ToString()] = decimal.ToDouble(item.value);

                }

            }
        }
        public void getETaxE_InvoiceForSL_Invoice(SL_InvoiceDetail detail, int detailId, DataRow row)
        {

            var etaxes = DB.SL_InvoiceDetailSubTaxValues.Where(x => x.InvoiceDetailId == detailId).ToList();

            if (Shared.st_Store.E_AllowMoreThanTax == false || Shared.st_Store.E_AllowMoreThanTax == null)
            {
                if (etaxes.Count > 0)
                {
                    row["TaxType"] = etaxes.FirstOrDefault().esubTypeId;
                    row["ETaxRatio"] = decimal.ToDouble(etaxes.FirstOrDefault().TaxRatio.GetValueOrDefault(0));
                    row["EtaxValue"] = decimal.ToDouble(etaxes.FirstOrDefault().value);
                }
            }
            else
            {
                if (etaxes.Count > 0)
                {
                    taxValue = 0;
                    decimal totalTaxValue = 0;
                    decimal totalTaxRatio = 0;
                    foreach (var tax in etaxes)
                    {
                        DataRow dTax = Dt_Rows.NewRow();
                        dTax["SubTax"] = tax.esubTypeId;
                        dTax["Tax"] = DB.E_TaxableTypes.Where(x => x.E_TaxableTypeId == DB.E_TaxableTypes.FirstOrDefault(a => a.E_TaxableTypeId == tax.esubTypeId).ParentTaxId).Select(x => x.E_TaxableTypeId).FirstOrDefault();
                        dTax["Percentage"] = decimal.ToDouble(tax.TaxRatio.GetValueOrDefault(0));

                        //decimal taxvalueRatio = tax.TaxRatio.GetValueOrDefault(0);
                        //totalTaxRatio += taxvalueRatio;
                        decimal DiscR1 = Convert.ToDecimal(row["DiscountRatio"]);
                        decimal DiscR2 = Convert.ToDecimal(row["DiscountRatio2"]);
                        decimal DiscR3 = Convert.ToDecimal(row["DiscountRatio3"]);

                        decimal TotalSellPrice = Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(row["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);

                        bool calcTaxBeforeDisc = Convert.ToBoolean(row["calcTaxBeforeDisc"]);

                        decimal taxvalue = calcTaxBeforeDisc ?
                            Math.Round(Convert.ToDecimal(row["SellPrice"]) * Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(dTax["Percentage"]) / 100, defaultRoundingPoints)
                            : Math.Round(TotalSellPrice * Convert.ToDecimal(dTax["Percentage"]) / 100, defaultRoundingPoints);
                        if (Convert.ToInt32(dTax["Tax"]) == discountTaxId)
                        {

                            decimal taxvalueRatio = tax.TaxRatio.GetValueOrDefault(0);
                            totalTaxValue -= taxvalue;
                            totalTaxRatio -= taxvalueRatio;
                            taxValue -= taxvalue;
                        }

                        else
                        {


                            decimal taxvalueRatio = tax.TaxRatio.GetValueOrDefault(0);
                            totalTaxValue += taxvalue;
                            totalTaxRatio += taxvalueRatio;
                            taxValue += taxvalue;

                        }

                        //totalTaxValue += taxvalue;
                        //taxValue += taxvalue;

                        dTax["ItemId"] = detail.ItemId;
                        dTax["SellPrice"] = detail.SellPrice;
                        dTax["Qty"] = detail.Qty;
                        dTax["TaxValue"] = decimal.ToDouble(taxvalue);
                        dTax["RowHandle"] = rowhandle;
                        Dt_Rows.Rows.Add(dTax);



                    }
                    row["TotalTaxes"] = decimal.ToDouble(totalTaxValue);
                    row["totalTaxesRatio"] = decimal.ToDouble(totalTaxRatio);
                }
            }

        }
        public void getETaxDetailForErp(int detailId, DataRow row)
        {
          
            var etax = DB.SlReturnInvoiceDetailSubTaxValues.Where(x => x.ReturnInvoiceDetailId == detailId).ToList();
            if (etax.Count != 0)
            {
                foreach (var item in etax)
                {
                    row["ETaxType" + item.esubTypeId.ToString()] = DB.E_TaxableTypes.Where(a => a.E_TaxableTypeId == item.esubTypeId).Select(a => a.Code).FirstOrDefault();
                    row["ETaxRatio" + item.esubTypeId.ToString()] = item.TaxRatio != null ? decimal.ToDouble(item.TaxRatio.Value) : 0;
                    row["ETaxValue" + item.esubTypeId.ToString()] = decimal.ToDouble(item.value);
                  
                }
                
            }
        }
        public void getETaxE_Invoice(SL_ReturnDetail detail, int detailId, DataRow row)
        {

            var etaxes = DB.SlReturnInvoiceDetailSubTaxValues.Where(x => x.ReturnInvoiceDetailId == detailId).ToList();

            if (Shared.st_Store.E_AllowMoreThanTax == false || Shared.st_Store.E_AllowMoreThanTax == null)
            {
                if (etaxes.Count > 0)
                {
                    row["TaxType"] = etaxes.FirstOrDefault().esubTypeId;
                    row["ETaxRatio"] = decimal.ToDouble(etaxes.FirstOrDefault().TaxRatio.GetValueOrDefault(0));
                    row["EtaxValue"] = decimal.ToDouble(etaxes.FirstOrDefault().value);
                }
            }
            else
            {
                //if (etaxes.Count > 0)
                //{
                //    taxValue = 0;
                //    decimal totalTaxValue = 0;
                //    decimal totalTaxRatio = 0;
                //    foreach (var tax in etaxes)
                //    {
                //        DataRow dTax = Dt_Rows.NewRow();
                //        dTax["SubTax"] = tax.esubTypeId;
                //        dTax["Tax"] = DB.E_TaxableTypes.Where(x => x.E_TaxableTypeId == DB.E_TaxableTypes.FirstOrDefault(a => a.E_TaxableTypeId == tax.esubTypeId).ParentTaxId).Select(x => x.E_TaxableTypeId).FirstOrDefault();
                //        dTax["Percentage"] = decimal.ToDouble(tax.TaxRatio.GetValueOrDefault(0));

                //        //decimal taxvalueRatio = tax.TaxRatio.GetValueOrDefault(0);
                //        //totalTaxRatio += taxvalueRatio;
                //        decimal DiscR1 = Convert.ToDecimal(row["DiscountRatio"]);
                //        decimal DiscR2 = Convert.ToDecimal(row["DiscountRatio2"]);
                //        decimal DiscR3 = Convert.ToDecimal(row["DiscountRatio3"]);

                //        decimal TotalSellPrice = Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(row["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);

                //        bool calcTaxBeforeDisc = Convert.ToBoolean(row["calcTaxBeforeDisc"]);

                //        decimal taxvalue = calcTaxBeforeDisc ?
                //            Math.Round(Convert.ToDecimal(row["SellPrice"]) * Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(dTax["Percentage"]) / 100, defaultRoundingPoints)
                //            : Math.Round(TotalSellPrice * Convert.ToDecimal(dTax["Percentage"]) / 100, defaultRoundingPoints);
                //        if (Convert.ToInt32(dTax["Tax"]) == discountTaxId)
                //        {

                //            decimal taxvalueRatio = tax.TaxRatio.GetValueOrDefault(0);
                //            totalTaxValue -= taxvalue;
                //            totalTaxRatio -= taxvalueRatio;
                //            taxValue -= taxvalue;
                //        }

                //        else
                //        {


                //            decimal taxvalueRatio = tax.TaxRatio.GetValueOrDefault(0);
                //            totalTaxValue += taxvalue;
                //            totalTaxRatio += taxvalueRatio;
                //            taxValue += taxvalue;

                //        }

                //        //totalTaxValue += taxvalue;
                //        //taxValue += taxvalue;

                //        dTax["ItemId"] = detail.ItemId;
                //        dTax["SellPrice"] = detail.SellPrice;
                //        dTax["Qty"] = detail.Qty;
                //        dTax["TaxValue"] = decimal.ToDouble(taxvalue);
                //        dTax["RowHandle"] = rowhandle;
                //        Dt_Rows.Rows.Add(dTax);

                //    }
                //    row["TotalTaxes"] = decimal.ToDouble(totalTaxValue);
                //    row["totalTaxesRatio"] = decimal.ToDouble(totalTaxRatio);
                //}
                if (etaxes.Count > 0)
                {
                    taxValue = 0;
                    decimal totalTaxValue = 0;
                    decimal totalTaxRatio = 0;
                    decimal ratio = 0;
                    decimal tableTaxValue = 0;
                    decimal TotalSellPrice = 0;
                    decimal totalTableTaxes = 0;

                    foreach (var tax in etaxes)
                    {
                        bool isT1 = false;
                        DataRow dTax = Dt_Rows.NewRow();
                        dTax["SubTax"] = tax.esubTypeId;
                        dTax["Tax"] = DB.E_TaxableTypes.Where(x => x.E_TaxableTypeId == DB.E_TaxableTypes.FirstOrDefault(a => a.E_TaxableTypeId == tax.esubTypeId).ParentTaxId).Select(x => x.E_TaxableTypeId).FirstOrDefault();
                        dTax["Percentage"] = decimal.ToDouble(tax.TaxRatio.GetValueOrDefault(0));
                        var tableTaxIds = DB.E_TaxableTypes.Where(a => tableTax.Contains(a.Code)).Select(a => a.E_TaxableTypeId).ToList();
                        //decimal taxvalueRatio = tax.TaxRatio.GetValueOrDefault(0);
                        //totalTaxRatio += taxvalueRatio;
                        decimal DiscR1 = Convert.ToDecimal(row["DiscountRatio"]);
                        decimal DiscR2 = Convert.ToDecimal(row["DiscountRatio2"]);
                        decimal DiscR3 = Convert.ToDecimal(row["DiscountRatio3"]);

                        TotalSellPrice = Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(row["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);

                        bool calcTaxBeforeDisc = Convert.ToBoolean(row["calcTaxBeforeDisc"]);

                        decimal taxvalue = calcTaxBeforeDisc ?
                            Math.Round(Convert.ToDecimal(row["SellPrice"]) * Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(dTax["Percentage"]) / 100, defaultRoundingPoints)
                            : Math.Round(TotalSellPrice * Convert.ToDecimal(dTax["Percentage"]) / 100, defaultRoundingPoints);


                        if (dTax["Tax"].ToString() == "1")
                        {
                            isT1 = true;
                            taxvalue = tax.value;
                            ratio = Math.Round((tax.TaxRatio.GetValueOrDefault(0) / TotalSellPrice) * 100, defaultRoundingPoints);

                        }
                        if (Convert.ToInt32(dTax["Tax"]) == discountTaxId)
                        {

                            decimal taxvalueRatio = Math.Round(Convert.ToDecimal(tax.TaxRatio.GetValueOrDefault(0)), defaultRoundingPoints);
                            totalTaxValue -= taxvalue;
                            totalTaxRatio -= taxvalueRatio;
                            taxValue -= taxvalue;
                        }

                        else
                        {


                            decimal taxvalueRatio = Math.Round(Convert.ToDecimal(tax.TaxRatio.GetValueOrDefault(0)), defaultRoundingPoints);
                            if (isT1 == true)
                            {
                                row["addTaxValue"] = Math.Round(Convert.ToDecimal(taxvalue), defaultRoundingPoints);
                                taxvalueRatio = ratio;
                            }
                               

                            totalTaxValue += taxvalue;
                            totalTaxRatio += taxvalueRatio;
                            taxValue += taxvalue;
                            if (tableTaxIds.Contains(Convert.ToInt32(dTax["Tax"])))
                            {
                                tableTaxValue += taxvalue;
                                totalTableTaxes += taxvalueRatio;
                                row["tableTaxValue"] = Math.Round(Convert.ToDecimal(taxvalue), defaultRoundingPoints);

                            }


                        }

                        //totalTaxValue += taxvalue;
                        //taxValue += taxvalue;

                        dTax["ItemId"] = detail.ItemId;
                        dTax["SellPrice"] = detail.SellPrice;
                        dTax["Qty"] = detail.Qty;
                        dTax["TaxValue"] = decimal.ToDouble(taxvalue);
                        dTax["RowHandle"] = rowhandle;
                        Dt_Rows.Rows.Add(dTax);
                        if (dt_SubTax.AsEnumerable()
                        .Where(a => a.RowState != DataRowState.Deleted)
                        .Any(r => Convert.ToInt32(r["SubTaxId"].ToString()) == Convert.ToInt32(tax.esubTypeId)))
                        {
                            var subTax = dt_SubTax.AsEnumerable()
                           .Where(a => a.RowState != DataRowState.Deleted)
                           .FirstOrDefault(r => Convert.ToInt32(r["SubTaxId"].ToString()) == Convert.ToInt32(tax.esubTypeId));
                            subTax["Rate"] = Convert.ToDouble(subTax["Rate"]) + Convert.ToDouble(dTax["Percentage"]);
                            subTax["Value"] = Convert.ToDouble(subTax["Value"]) + Convert.ToDouble(dTax["TaxValue"]);

                        }
                        else
                        {
                            DataRow dtTax = dt_SubTax.NewRow();
                            dtTax["SubTaxId"] = Convert.ToInt32(tax.esubTypeId);
                            dtTax["Rate"] = Convert.ToDouble(dTax["Percentage"]);
                            dtTax["Value"] = Convert.ToDouble(dTax["TaxValue"]);
                            dt_SubTax.Rows.Add(dtTax);
                        }

                    }

                    row["salePriceWithTaxTable"] = Math.Round(Convert.ToDecimal(TotalSellPrice + tableTaxValue), defaultRoundingPoints);
                    row["totalTableTaxes"] = Math.Round(Convert.ToDecimal(totalTableTaxes), defaultRoundingPoints);
                    row["TotalTaxes"] = Math.Round(Convert.ToDecimal(totalTaxValue), defaultRoundingPoints);
                    row["totalTaxesRatio"] = Math.Round(Convert.ToDecimal(totalTaxRatio), defaultRoundingPoints);
                }
            }

        }
        public void setETaxE_Invoice(List<IC_ItemSubTax> detail, DataRow row, IC_Item item)
        {
            MyHelper.TaxService.setETaxE_Invoice(detail, row,ref Dt_Rows, rowhandle, item, discountTaxId);
            if (Dt_Rows.Rows.Count > 0)
            {
                taxValue = dtSLReturn_Details.AsEnumerable()
                    .Where(r => r.RowState != DataRowState.Deleted)
                    .Sum(x => Convert.ToDecimal(x["TaxValue"] is DBNull ? 0 : Convert.ToDecimal(x["TaxValue"])));
                totalSubDiscountTax = dtSLReturn_Details.AsEnumerable()
                    .Where(r => r.RowState != DataRowState.Deleted)
                    .Sum(x => Convert.ToDecimal(x["TotalSubDiscountTax"] is DBNull ? 0 : Convert.ToDecimal(x["TotalSubDiscountTax"])));
                // Call the method to get total account
                Get_TotalAccount();
                updateSubTaxGrid();
            }
        }
        decimal totalSubDiscountTax = 0;
        private void calcSubTaxes(DataRow detail, GridView view)
        {
            MyHelper.TaxService.CalcSubTaxes(detail, ref Dt_Rows, discountTaxId);
            // Update grid view
            if (Dt_Rows.Rows.Count > 0)
            {
                view.UpdateCurrentRow();

                taxValue = dtSLReturn_Details.AsEnumerable()
                    .Where(r => r.RowState != DataRowState.Deleted)
                    .Sum(x => Convert.ToDecimal(x["TaxValue"] is DBNull ? 0 : Convert.ToDecimal(x["TaxValue"])));
                totalSubDiscountTax = dtSLReturn_Details.AsEnumerable()
                    .Where(r => r.RowState != DataRowState.Deleted)
                    .Sum(x => Convert.ToDecimal(x["TotalSubDiscountTax"] is DBNull ? 0 : Convert.ToDecimal(x["TotalSubDiscountTax"])));
                // Call the method to get total account
                Get_TotalAccount();
                updateSubTaxGrid();
            }
        }
        //public void setETaxE_Invoice(List<IC_ItemSubTax> detail, DataRow row, IC_Item item)
        //{

        //    //if (detail.Count > 0)
        //    //{
        //    //    taxValue = 0;
        //    //    decimal totalTaxValue = 0;
        //    //    decimal totalTaxRatio = 0;
        //    //    row["TotalTaxes"] = 0;
        //    //    row["totalTaxesRatio"] = 0;
        //    //    foreach (var tax in detail)
        //    //    {
        //    //        DataRow dTax = Dt_Rows.NewRow();
        //    //        dTax["SubTax"] = tax.SubTaxId;
        //    //        dTax["Tax"] = DB.E_TaxableTypes.Where(x => x.E_TaxableTypeId == DB.E_TaxableTypes.FirstOrDefault(a => a.E_TaxableTypeId == tax.SubTaxId).ParentTaxId).Select(x => x.E_TaxableTypeId).FirstOrDefault();
        //    //        dTax["Percentage"] = decimal.ToDouble(tax.Rate.GetValueOrDefault(0));

        //    //        //decimal taxvalueRatio = tax.TaxRatio.GetValueOrDefault(0);
        //    //        //totalTaxRatio += taxvalueRatio;
        //    //        decimal DiscR1 = Convert.ToDecimal(row["DiscountRatio"]);
        //    //        decimal DiscR2 = Convert.ToDecimal(row["DiscountRatio2"]);
        //    //        decimal DiscR3 = Convert.ToDecimal(row["DiscountRatio3"]);

        //    //        decimal TotalSellPrice = Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(row["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);

        //    //        bool calcTaxBeforeDisc = Convert.ToBoolean(row["calcTaxBeforeDisc"]);

        //    //        decimal taxvalue = calcTaxBeforeDisc ?
        //    //            Math.Round(Convert.ToDecimal(row["SellPrice"]) * Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(dTax["Percentage"]) / 100,defaultRoundingPoints)
        //    //            : Math.Round(TotalSellPrice * Convert.ToDecimal(dTax["Percentage"]) / 100, defaultRoundingPoints);
        //    //        if (Convert.ToInt32(dTax["Tax"]) == discountTaxId)
        //    //        {

        //    //            decimal taxvalueRatio = tax.Rate.GetValueOrDefault(0);
        //    //            totalTaxValue -= taxvalue;
        //    //            totalTaxRatio -= taxvalueRatio;
        //    //            taxValue -= taxvalue;
        //    //        }

        //    //        else
        //    //        {
        //    //            decimal taxvalueRatio = tax.Rate.GetValueOrDefault(0);
        //    //            totalTaxValue += taxvalue;
        //    //            totalTaxRatio += taxvalueRatio;
        //    //            taxValue += taxvalue;

        //    //        }
        //    //        dTax["ItemId"] = item.ItemId;
        //    //        dTax["SellPrice"] = 0;
        //    //        dTax["Qty"] = 0;
        //    //        dTax["TaxValue"] = decimal.ToDouble(taxvalue);
        //    //        dTax["RowHandle"] = rowhandle;
        //    //        Dt_Rows.Rows.Add(dTax);

        //    //    }
        //    //    row["TotalTaxes"] = decimal.ToDouble(totalTaxValue);
        //    //    row["totalTaxesRatio"] = decimal.ToDouble(totalTaxRatio);

        //    //}


        //    if (detail.Count > 0)
        //    {
        //        taxValue = 0;
        //        decimal totalTaxValue = 0;
        //        decimal totalTaxRatio = 0;
        //        decimal TotalSellPrice = 0;
        //        decimal totalTableTaxRatio = 0;
        //        decimal tableTaxValue = 0;
        //        decimal tableTaxratio = 0;
        //        row["TotalTaxes"] = 0;
        //        row["totalTaxesRatio"] = 0;

        //        var detailTaxes = DB.E_TaxableTypes.Where(a => detail.Select(c => c.SubTaxId).Contains(a.E_TaxableTypeId)).Select(a => new { ParentTaxId = (int)a.ParentTaxId, E_TaxableTypeId = a.E_TaxableTypeId }).ToList();
        //        var tableTaxIds = DB.E_TaxableTypes.Where(a => tableTax.Contains(a.Code)).Select(a => a.E_TaxableTypeId).ToList();
        //        var detailTablesTaxes = detailTaxes.Where(a => tableTaxIds.Contains(a.ParentTaxId)).Select(a => a.E_TaxableTypeId);
        //        foreach (var tax in detail.Where(a => detailTablesTaxes.Contains(a.SubTaxId)))
        //        {
        //            DataRow dTax = Dt_Rows.NewRow();
        //            dTax["SubTax"] = tax.SubTaxId;
        //            dTax["Tax"] = DB.E_TaxableTypes.Where(x => x.E_TaxableTypeId == DB.E_TaxableTypes.FirstOrDefault(a => a.E_TaxableTypeId == tax.SubTaxId).ParentTaxId).Select(x => x.E_TaxableTypeId).FirstOrDefault();
        //            dTax["Percentage"] = decimal.ToDouble(tax.Rate.GetValueOrDefault(0));

        //            decimal DiscR1 = Convert.ToDecimal(row["DiscountRatio"]);
        //            decimal DiscR2 = Convert.ToDecimal(row["DiscountRatio2"]);
        //            decimal DiscR3 = Convert.ToDecimal(row["DiscountRatio3"]);

        //            TotalSellPrice = Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(row["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);

        //            bool calcTaxBeforeDisc = Convert.ToBoolean(row["calcTaxBeforeDisc"]);

        //            decimal taxvalue = calcTaxBeforeDisc ?
        //              Math.Round(Convert.ToDecimal(row["SellPrice"]) * Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(dTax["Percentage"]) / 100, defaultRoundingPoints)
        //              : Math.Round(TotalSellPrice * Convert.ToDecimal(dTax["Percentage"]) / 100, defaultRoundingPoints);
        //            if (Convert.ToInt32(dTax["Tax"]) == discountTaxId)
        //            {

        //                decimal taxvalueRatio = Math.Round(Convert.ToDecimal(tax.Rate.GetValueOrDefault(0)), defaultRoundingPoints);
        //                totalTaxValue -= taxvalue;
        //                totalTaxRatio -= taxvalueRatio;
        //                taxValue -= taxvalue;
        //            }

        //            else
        //            {


        //                decimal taxvalueRatio = Math.Round(Convert.ToDecimal(tax.Rate.GetValueOrDefault(0)), defaultRoundingPoints);
        //                totalTaxValue += taxvalue;
        //                totalTaxRatio += taxvalueRatio;
        //                taxValue += taxvalue;
        //                totalTableTaxRatio += taxvalueRatio;
        //                tableTaxValue += taxvalue;
        //                tableTaxratio += taxvalueRatio;
        //            }
        //            dTax["ItemId"] = item.ItemId;
        //            dTax["SellPrice"] = 0;
        //            dTax["Qty"] = 0;
        //            dTax["TaxValue"] = decimal.ToDouble(taxvalue);
        //            dTax["RowHandle"] = rowhandle;
        //            Dt_Rows.Rows.Add(dTax);
        //            row["tableTaxValue"] = Math.Round(Convert.ToDecimal(taxvalue), defaultRoundingPoints);
        //        }
        //        foreach (var tax in detail.Where(a => !detailTablesTaxes.Contains(a.SubTaxId)))
        //        {
        //            bool isT1 = false;
        //            DataRow dTax = Dt_Rows.NewRow();
        //            dTax["SubTax"] = tax.SubTaxId;
        //            dTax["Tax"] = DB.E_TaxableTypes.Where(x => x.E_TaxableTypeId == DB.E_TaxableTypes.FirstOrDefault(a => a.E_TaxableTypeId == tax.SubTaxId).ParentTaxId).Select(x => x.E_TaxableTypeId).FirstOrDefault();
        //            dTax["Percentage"] = decimal.ToDouble(tax.Rate.GetValueOrDefault(0));


        //            decimal DiscR1 = Convert.ToDecimal(row["DiscountRatio"]);
        //            decimal DiscR2 = Convert.ToDecimal(row["DiscountRatio2"]);
        //            decimal DiscR3 = Convert.ToDecimal(row["DiscountRatio3"]);

        //            TotalSellPrice = Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(row["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);

        //            bool calcTaxBeforeDisc = Convert.ToBoolean(row["calcTaxBeforeDisc"]);
        //            if (dTax["Tax"].ToString() == "1")
        //            {
        //                var totalSellPriceWithTableTaxes = Convert.ToDecimal(totalTableTaxRatio / 100) * TotalSellPrice;
        //                TotalSellPrice = TotalSellPrice + totalSellPriceWithTableTaxes;
        //                isT1 = true;
        //            }

        //            decimal taxvalue = calcTaxBeforeDisc ?
        //              Math.Round(Convert.ToDecimal(row["SellPrice"]) * Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(dTax["Percentage"]) / 100, defaultRoundingPoints)
        //              : Math.Round(TotalSellPrice * Convert.ToDecimal(dTax["Percentage"]) / 100, defaultRoundingPoints);
        //            if (Convert.ToInt32(dTax["Tax"]) == discountTaxId)
        //            {

        //                decimal taxvalueRatio = Math.Round(Convert.ToDecimal(tax.Rate.GetValueOrDefault(0)), defaultRoundingPoints);
        //                totalTaxValue -= taxvalue;
        //                totalTaxRatio -= taxvalueRatio;
        //                taxValue -= taxvalue;
        //            }

        //            else
        //            {


        //                decimal taxvalueRatio = Math.Round(Convert.ToDecimal(tax.Rate.GetValueOrDefault(0)), defaultRoundingPoints);
        //                if (isT1)
        //                {
        //                    TotalSellPrice = Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(row["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);
        //                    taxvalueRatio = TotalSellPrice != 0 ? Math.Round((taxvalue / TotalSellPrice) * 100, defaultRoundingPoints) : 0;
        //                    row["addTaxValue"] = Math.Round(Convert.ToDecimal(taxvalue), defaultRoundingPoints);

        //                }
        //                totalTaxValue += taxvalue;
        //                totalTaxRatio += taxvalueRatio;
        //                taxValue += taxvalue;

        //            }
        //            dTax["ItemId"] = item.ItemId;
        //            dTax["SellPrice"] = 0;
        //            dTax["Qty"] = 0;
        //            dTax["TaxValue"] = decimal.ToDouble(taxvalue);
        //            dTax["RowHandle"] = rowhandle;
        //            Dt_Rows.Rows.Add(dTax);

        //        }

        //        row["totalTableTaxes"] = Math.Round(Convert.ToDecimal(tableTaxratio), defaultRoundingPoints);
        //        row["salePriceWithTaxTable"] = Math.Round(Convert.ToDecimal(TotalSellPrice + tableTaxValue), defaultRoundingPoints);
        //        row["TotalTaxes"] = Math.Round(Convert.ToDecimal(totalTaxValue), defaultRoundingPoints);
        //        row["totalTaxesRatio"] = Math.Round(Convert.ToDecimal(totalTaxRatio), defaultRoundingPoints);



        //    }

        //}
        public void setETaxE_InvoiceAfterSellInvoice(List<IC_ItemSubTax> detail, DataRow row, IC_Item item)
        {

            if (detail.Count > 0)
            {
                taxValue = 0;
                decimal totalTaxValue = 0;
                decimal totalTaxRatio = 0;
                decimal TotalSellPrice = 0;
                decimal totalTableTaxRatio = 0;
                decimal tableTaxValue = 0;
                decimal tableTaxratio = 0;

                row["TotalTaxes"] = 0;
                row["totalTaxesRatio"] = 0;
                var detailTaxes = DB.E_TaxableTypes.Where(a => detail.Select(c => c.SubTaxId).Contains(a.E_TaxableTypeId)).Select(a => new { ParentTaxId = (int)a.ParentTaxId, E_TaxableTypeId = a.E_TaxableTypeId }).ToList();
                var tableTaxIds = DB.E_TaxableTypes.Where(a => tableTax.Contains(a.Code)).Select(a => a.E_TaxableTypeId).ToList();
                var detailTablesTaxes = detailTaxes.Where(a => tableTaxIds.Contains(a.ParentTaxId)).Select(a => a.E_TaxableTypeId);
                var Dt_RowsTaxes = Dt_Rows.AsEnumerable()
                      .Where(a => a.RowState != DataRowState.Deleted)
                      .Where(r => Convert.ToInt32(r["RowHandle"].ToString()) == rowhandle).ToList();
                foreach (var tax in detail.Where(a => detailTablesTaxes.Contains(a.SubTaxId)))
                {
                    var rowExist = Dt_RowsTaxes.Where(r => Convert.ToInt32(r["SubTax"].ToString()) == tax.SubTaxId).FirstOrDefault();
                    var Tax = DB.E_TaxableTypes.Where(x => x.E_TaxableTypeId == DB.E_TaxableTypes.FirstOrDefault(a => a.E_TaxableTypeId == tax.SubTaxId).ParentTaxId).Select(x => x.E_TaxableTypeId).FirstOrDefault();
                    var Percentage = decimal.ToDouble(tax.Rate.GetValueOrDefault(0));

                    decimal DiscR1 = Convert.ToDecimal(row["DiscountRatio"]);
                    decimal DiscR2 = Convert.ToDecimal(row["DiscountRatio2"]);
                    decimal DiscR3 = Convert.ToDecimal(row["DiscountRatio3"]);

                    TotalSellPrice = Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(row["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);

                    bool calcTaxBeforeDisc = Convert.ToBoolean(row["calcTaxBeforeDisc"]);

                    decimal taxvalue = calcTaxBeforeDisc ?
                       Math.Round(Convert.ToDecimal(row["SellPrice"]) * Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(Percentage) / 100, defaultRoundingPoints)
                       : Math.Round(TotalSellPrice * Convert.ToDecimal(Percentage) / 100, defaultRoundingPoints);
                    if (Tax == discountTaxId)
                    {

                        decimal taxvalueRatio = Math.Round(Convert.ToDecimal(tax.Rate.GetValueOrDefault(0)), defaultRoundingPoints);
                        totalTaxValue -= taxvalue;
                        totalTaxRatio -= taxvalueRatio;
                        taxValue -= taxvalue;
                    }

                    else
                    {
                        decimal taxvalueRatio = Math.Round(Convert.ToDecimal(tax.Rate.GetValueOrDefault(0)), defaultRoundingPoints);
                        totalTaxValue += taxvalue;
                        totalTaxRatio += taxvalueRatio;
                        taxValue += taxvalue;
                        totalTableTaxRatio += taxvalueRatio;
                        tableTaxValue += taxvalue;
                        tableTaxratio += taxvalueRatio;
                    }
                    if (rowExist != null)
                    {
                        rowExist["TaxValue"] = taxvalue;
                    }
                    row["tableTaxValue"] = Math.Round(Convert.ToDecimal(taxvalue), defaultRoundingPoints);

                }
                foreach (var tax in detail.Where(a => !detailTablesTaxes.Contains(a.SubTaxId)))
                {
                    bool isT1 = false;
                    var rowExist = Dt_RowsTaxes.Where(r => Convert.ToInt32(r["SubTax"].ToString()) == tax.SubTaxId).FirstOrDefault();
                    var Tax = DB.E_TaxableTypes.Where(x => x.E_TaxableTypeId == DB.E_TaxableTypes.FirstOrDefault(a => a.E_TaxableTypeId == tax.SubTaxId).ParentTaxId).Select(x => x.E_TaxableTypeId).FirstOrDefault();
                    var Percentage = Math.Round(Convert.ToDecimal(tax.Rate.GetValueOrDefault(0)), defaultRoundingPoints);

                    decimal DiscR1 = Convert.ToDecimal(row["DiscountRatio"]);
                    decimal DiscR2 = Convert.ToDecimal(row["DiscountRatio2"]);
                    decimal DiscR3 = Convert.ToDecimal(row["DiscountRatio3"]);

                    TotalSellPrice = Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(row["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);

                    bool calcTaxBeforeDisc = Convert.ToBoolean(row["calcTaxBeforeDisc"]);
                    if (Tax == 1)
                    {
                        var totalSellPriceWithTableTaxes = Convert.ToDecimal(totalTableTaxRatio / 100) * TotalSellPrice;
                        TotalSellPrice = TotalSellPrice + totalSellPriceWithTableTaxes;
                        isT1 = true;
                    }

                    decimal taxvalue = calcTaxBeforeDisc ?
                      Math.Round(Convert.ToDecimal(row["SellPrice"]) * Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(Percentage) / 100, defaultRoundingPoints)
                      : Math.Round(TotalSellPrice * Percentage / 100, defaultRoundingPoints);
                    if (Tax == discountTaxId)
                    {

                        decimal taxvalueRatio = Math.Round(Convert.ToDecimal(tax.Rate.GetValueOrDefault(0)), defaultRoundingPoints);
                        totalTaxValue -= taxvalue;
                        totalTaxRatio -= taxvalueRatio;
                        taxValue -= taxvalue;
                    }

                    else
                    {


                        decimal taxvalueRatio = Math.Round(Convert.ToDecimal(tax.Rate.GetValueOrDefault(0)), defaultRoundingPoints);
                        if (isT1)
                        {
                            TotalSellPrice = Convert.ToDecimal(row["Qty"]) * Convert.ToDecimal(row["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);
                            taxvalueRatio = TotalSellPrice != 0 ? Math.Round(Convert.ToDecimal(taxvalue / TotalSellPrice) * 100, defaultRoundingPoints) : 0;
                            row["addTaxValue"] = Math.Round(Convert.ToDecimal(taxvalue), defaultRoundingPoints);

                        }
                        totalTaxValue += taxvalue;
                        totalTaxRatio += taxvalueRatio;
                        taxValue += taxvalue;

                    }

                    if (rowExist != null)
                    {
                        rowExist["TaxValue"] = taxvalue;
                    }
                }
                row["totalTableTaxes"] = Math.Round(Convert.ToDecimal(tableTaxratio), defaultRoundingPoints);
                row["salePriceWithTaxTable"] = Math.Round(Convert.ToDecimal(TotalSellPrice + tableTaxValue), defaultRoundingPoints);
                row["TotalTaxes"] = Math.Round(Convert.ToDecimal(totalTaxValue), defaultRoundingPoints);
                row["totalTaxesRatio"] = Math.Round(Convert.ToDecimal(totalTaxRatio), defaultRoundingPoints);

            }

        }
        private void GetInvoiceDetails(int invoiceId)
        {
            dtSLReturn_Details.Rows.Clear();
            DB = new DAL.ERPDataContext();
            var details = (from d in DB.SL_ReturnDetails
                           where d.SL_ReturnId == invoiceId
                           join i in DB.IC_Items on d.ItemId equals i.ItemId
                           orderby d.SL_ReturnDetailId

                           select new { detail = d, item = i }).ToList();
            Dt_Rows.Rows.Clear();
            if (Shared.E_invoiceAvailable)
            {
                getTaxColumns(details.Select(a => a.detail.SL_ReturnDetailId).ToList());

            }


            foreach (var d in details)
            {
                DataRow row = dtSLReturn_Details.NewRow();

                row["PR_InvoiceDetailId"] = d.detail.SL_ReturnDetailId;
                row["PR_InvoiceId"] = d.detail.SL_ReturnId;
                row["ItemId"] = d.detail.ItemId;
                row["ItemIdF"] = Shared.IsEnglish ? d.item.ItemNameAr : d.item.ItemNameEn;
                row["CategoryId"] = d.item.Category;
                row["ItemCode1"] = d.item.ItemCode1;
                row["ItemCode2"] = d.item.ItemCode2;
                row["ItemType"] = d.item.ItemType;
                row["UOM"] = d.detail.UOMId;
                row["Qty"] = decimal.ToDouble(d.detail.Qty);
                row["RowHandle"] = ++rowhandle;
                if (d.detail.Height != null)
                    row["Height"] = decimal.ToDouble(d.detail.Height.Value);
                if (d.detail.Length != null)
                    row["Length"] = decimal.ToDouble(d.detail.Length.Value);
                if (d.detail.Width != null)
                    row["Width"] = decimal.ToDouble(d.detail.Width.Value);
                row["PiecesCount"] = decimal.ToDouble(d.detail.PiecesCount);

                row["PurchasePrice"] = decimal.ToDouble(d.detail.PurchasePrice);
                row["SellPrice"] = decimal.ToDouble(d.detail.SellPrice);

                row["SalesTaxRatio"] = d.detail.SalesTaxRatio;
                row["SalesTax"] = decimal.ToDouble(d.detail.SalesTax);

                row["CustomTaxRatio"] = decimal.ToDouble(d.detail.CustomTaxRatio);
                row["CustomTax"] = decimal.ToDouble(d.detail.CustomTax);

                row["calcTaxBeforeDisc"] = d.item.calcTaxBeforeDisc;

                row["DiscountValue"] = decimal.ToDouble(d.detail.DiscountValue);
                row["DiscountRatio"] = decimal.ToDouble(d.detail.DiscountRatio);
                row["DiscountRatio2"] = decimal.ToDouble(d.detail.DiscountRatio2);
                row["DiscountRatio3"] = decimal.ToDouble(d.detail.DiscountRatio3);

                //row["CompanyNameAr"] = d.item.IC_Company.CompanyNameAr;
                row["MediumUOMFactor"] = MyHelper.FractionToDouble(d.item.MediumUOMFactor);
                row["LargeUOMFactor"] = MyHelper.FractionToDouble(d.item.LargeUOMFactor);

                if (d.detail.Expire.HasValue)
                    row["Expire"] = d.detail.Expire;
                row["Batch"] = d.detail.Batch;
                row["Serial"] = d.detail.Serial;
                row["Serial2"] = d.detail.Serial2;
                row["IsExpire"] = d.item.IsExpire;

                row["VendorId"] = d.detail.VendorId;

                //get store qty                                
                //decimal currentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, d.detail.ItemId, d.detail.SL_Return.StoreId);
                //currentQty = MyHelper.getCalculatedUomQty(currentQty, d.detail.UOMIndex, MyHelper.FractionToDouble(d.item.MediumUOMFactor), MyHelper.FractionToDouble(d.item.LargeUOMFactor));
                //row["CurrentQty"] = decimal.ToDouble(currentQty);

                row["TotalSellPrice"] = decimal.ToDouble(d.detail.TotalSellPrice);
                row["UomIndex"] = d.detail.UOMIndex;

                row["ItemDescription"] = d.detail.ItemDescription;
                row["ItemDescriptionEn"] = d.detail.ItemDescriptionEn;

                row["ParentItemId"] = d.item.mtrxParentItem;
                row["M1"] = d.item.mtrxAttribute1;
                row["M2"] = d.item.mtrxAttribute2;
                row["M3"] = d.item.mtrxAttribute3;

                row["ManufactureDate"] = d.detail.ManufactureDate;

                row["LibraQty"] = Math.Round(d.detail.LibraQty.HasValue ? d.detail.LibraQty.Value : 0, defaultRoundingPoints);
                row["PricingWithSmall"] = d.item.PricingWithSmall;
                row["kg_Weight_libra"] = decimal.Round(d.detail.kg_Weight_libra.HasValue ? d.detail.kg_Weight_libra.Value : 0, defaultRoundingPoints);

                row["Pack"] = d.detail.Pack;

                row["bonusDiscount"] = decimal.ToDouble(d.detail.bonusDiscount.GetValueOrDefault(0));
                //if (Shared.E_invoiceAvailable)
                //    getETaxDetailForErp(d.detail.SL_ReturnDetailId, row);
                //else
                //    getETaxE_Invoice(d.detail, d.detail.SL_ReturnDetailId, row);
                //getETax(d.detail.SL_ReturnDetailId, row);
                var eTaxes = (from tax in DB.SlReturnInvoiceDetailSubTaxValues
                              from detail in DB.SL_Add_Details
                              where tax.ReturnInvoiceDetailId == d.detail.SL_ReturnDetailId && detail.ItemId == d.detail.ItemId
                              select new
                              {
                                  IC_ItemSubTaxesId = 0,
                                  ItemId = detail.ItemId,
                                  SubTaxId = tax.esubTypeId,
                                  Rate = tax.TaxRatio,
                              }).Distinct();

                var taxesForItem = eTaxes.AsEnumerable()
                                        .Select(item => new IC_ItemSubTax
                                        {
                                            IC_ItemSubTaxesId = item.IC_ItemSubTaxesId,
                                            ItemId = item.ItemId,
                                            SubTaxId = item.SubTaxId,
                                            Rate = item.Rate,
                                        }).ToList();
                //var SubTaxes = DB.IC_ItemSubTaxes.Where(a => a.ItemId == d.detail.ItemId).ToList();
                if (Shared.st_Store.E_AllowMoreThanTax == true || Shared.E_invoiceAvailable == true)
                {

                    setETaxE_Invoice(taxesForItem, row, d.item);

                }
                else
                {
                    if (taxesForItem.Count > 0)
                    {
                        var subTaxItem = taxesForItem.FirstOrDefault();
                        row["ETaxRatio"] = subTaxItem.Rate;
                        row["TaxType"] = subTaxItem.SubTaxId;
                    }

                }

                dtSLReturn_Details.Rows.Add(row);
            }
            dtSLReturn_Details.AcceptChanges();
            DataModified = false;
            Get_TotalAccount();
        }
        public void getETax(int detailId, DataRow row)
        {
            var etax = DB.SlReturnInvoiceDetailSubTaxValues.Where(x => x.ReturnInvoiceDetailId == detailId).FirstOrDefault();
            if (etax != null)
            {
                row["TaxType"] = etax.esubTypeId;
                row["ETaxRatio"] = decimal.ToDouble(etax.TaxRatio.GetValueOrDefault(0));
                row["EtaxValue"] = decimal.ToDouble(etax.value);
            }
        }
        public void getETaxInvoice(int detailId, DataRow row)
        {
            var etax = DB.SL_InvoiceDetailSubTaxValues.Where(x => x.InvoiceDetailId == detailId).FirstOrDefault();
            if (etax != null)
            {
                row["TaxType"] = etax.esubTypeId;
                row["ETaxRatio"] = decimal.ToDouble(etax.TaxRatio.GetValueOrDefault(0));
                row["EtaxValue"] = decimal.ToDouble(etax.value);
            }
        }
        public void LoadInvoice()
        {
            Reset();
            int defaultStoreId = 0;
            if (invoiceId > 0)
            {
                var storeId = DB.SL_Returns.Where(a => a.SL_ReturnId == invoiceId).FirstOrDefault().StoreId;

                if (Shared.InvoicePostToStore)
                    stores_table = MyHelper.Get_StoresNotStopped(storeId, true, out defaultStoreId, Shared.user.UserChangeStore, Shared.user.DefaultStore,
                    Shared.UserId);
                else
                    stores_table = MyHelper.Get_StoresNotStopped(storeId, false, out defaultStoreId, Shared.user.UserChangeStore, Shared.user.DefaultStore,
                    Shared.UserId);
                lkpStore.Properties.DataSource = stores_table;
                GetInvoice(invoiceId);
            }
            else
            {
                if (Shared.InvoicePostToStore)
                    stores_table = MyHelper.Get_StoresNotStopped(0, true, out defaultStoreId, Shared.user.UserChangeStore, Shared.user.DefaultStore,
                    Shared.UserId);
                else
                    stores_table = MyHelper.Get_StoresNotStopped(0, false, out defaultStoreId, Shared.user.UserChangeStore, Shared.user.DefaultStore,
                    Shared.UserId);

                lkpStore.Properties.DataSource = stores_table;
                lkpStore.EditValue = defaultStoreId;

                txtNotes.Text = Shared.user.InvoicesNotes;

                lkpStore_EditValueChanged(lkpStore, EventArgs.Empty);

                FocusItemCode1(Shared.user.FocusGridInInvoices);

                if (customerId != 0)
                    lkp_Customers.EditValue = customerId;

            }
        }

        private void FocusItemCode1(bool focusGrid)
        {
            if (focusGrid)
            {
                grdPrInvoice.Focus();
                var view = (grdPrInvoice.FocusedView as DevExpress.XtraGrid.Views.Grid.GridView);
                view.FocusedColumn = view.Columns[Shared.user.InvoicesUseSearchItems ? "ItemId" : "ItemCode1"];//mahmoud                        
            }
            else
                lkp_Customers.Focus();
        }


        public void Save_Invoice()
        {
            GridView view = grdPrInvoice.FocusedView as GridView;
            grdPrInvoice.RefreshDataSource();
            view.RefreshData();
            Get_TotalAccount();

            //save invoice
            DB = new DAL.ERPDataContext();
            DAL.SL_Return pr;
            DAL.IC_MultipleWeight mul;

            if (invoiceId > 0)
            {
                pr = DB.SL_Returns.Where(x => x.SL_ReturnId == invoiceId).FirstOrDefault();
                pr.LastUpdateUserId = Shared.UserId;
                pr.LastUpdateDate = DateTime.Now;

                MyHelper.UpdateST_UserLog(DB, txtInvoiceCode.Text, lkp_Customers.Text,
                (int)FormAction.Edit, (int)FormsNames.SL_Return);
            }
            else
            {
                pr = new SL_Return();
                pr.UserId = Shared.UserId;
                pr.JornalId = 0;
                DB.SL_Returns.InsertOnSubmit(pr);

                MyHelper.UpdateST_UserLog(DB, txtInvoiceCode.Text, lkp_Customers.Text,
                (int)FormAction.Add, (int)FormsNames.SL_Return);
            }

            #region SL Return

            pr.SourceId = SelectedInvId;
            pr.CustomerId = Convert.ToInt32(lkp_Customers.EditValue);
            pr.ReturnCode = txtInvoiceCode.Text.Trim();
            pr.ReturnDate = dtInvoiceDate.DateTime;
            pr.StoreId = Convert.ToInt32(lkpStore.EditValue);
            pr.Notes = txtNotes.Text;

            pr.PayMethod = (bool?)cmbPayMethod.EditValue;

            pr.DiscountRatio = Convert.ToDecimal(txtDiscountRatio.EditValue) / 100;
            pr.DiscountValue = Convert.ToDecimal(txtDiscountValue.EditValue);
            pr.Expenses = Convert.ToDecimal(txtExpenses.EditValue);

            pr.TaxValue = Convert.ToDecimal(txt_TaxValue.EditValue);
            pr.CustomTaxValue = Convert.ToDecimal(txt_CusTaxV.EditValue);

            pr.DeductTaxValue = Convert.ToDecimal(txt_DeductTaxV.EditValue);
            pr.DeductTaxRatio = Convert.ToDecimal(txt_DeductTaxR.EditValue) / 100;

            pr.AddTaxValue = Convert.ToDecimal(txt_AddTaxV.EditValue);
            pr.AddTaxRatio = Convert.ToDecimal(txt_AddTaxR.EditValue) / 100;

            pr.Net = Convert.ToDecimal(txtNet.EditValue);
            pr.Paid = Convert.ToDecimal(txt_PayAcc1_Paid.EditValue);

            if (lkp_Drawers2.EditValue != null)
                pr.PayAccountId2 = Convert.ToInt32(lkp_Drawers2.EditValue);
            else
                pr.PayAccountId2 = null;
            pr.PayAcc2_Paid = Convert.ToDecimal(txt_PayAcc2_Paid.EditValue);
            pr.Remains = Convert.ToDecimal(txt_Remains.EditValue);

            if (lkp_Drawers.EditValue != "")
                pr.DrawerAccountId = Convert.ToInt32(lkp_Drawers.EditValue);

            pr.CrncId = Convert.ToInt32(uc_Currency1.lkp_Crnc.EditValue);
            pr.CrncRate = Convert.ToDecimal(uc_Currency1.txtRate.EditValue);

            #endregion
            userId = pr.UserId;
            DB.SubmitChanges();

            #region Delete ItemStore & Sl RetuenDetail
            var Return_details_ids = pr.SL_ReturnDetails.Select(s => s.SL_ReturnDetailId).ToList();
            //var invoice_itemstores = DB.IC_ItemStores.Where(s => s.ProcessId == (int)Process.SellReturn
            //    && Return_details_ids.Contains(s.SourceId));
            //DB.IC_ItemStores.DeleteAllOnSubmit(invoice_itemstores);
            var subTaxes = DB.SlReturnInvoiceDetailSubTaxValues.Where(sub => pr.SL_ReturnDetails.Select(d => d.SL_ReturnDetailId).Contains(sub.ReturnInvoiceDetailId));
            if (subTaxes.Count() > 0)
                DB.SlReturnInvoiceDetailSubTaxValues.DeleteAllOnSubmit(subTaxes);

            DB.SL_ReturnDetails.DeleteAllOnSubmit(pr.SL_ReturnDetails);
            #endregion

            decimal CostOfSoldGoods = 0;//used for continual inventory
            List<StoreItem> lst_outitems = new List<StoreItem>();
            //int[] store_id = new int[dtSLReturn_Details.Rows.Count];
            for (int x = 0; x < dtSLReturn_Details.Rows.Count; x++)
            {
                #region SL ReturnDetail
                if (dtSLReturn_Details.Rows[x].RowState == DataRowState.Deleted)
                    continue;

                decimal MediumUOMFactor = 1;
                decimal LargeUOMFactor = 1;

                DAL.SL_ReturnDetail detail = new DAL.SL_ReturnDetail();
                detail.SL_ReturnId = pr.SL_ReturnId;
                detail.ItemId = Convert.ToInt32(dtSLReturn_Details.Rows[x]["ItemId"]);
                detail.UOMId = Convert.ToInt32(dtSLReturn_Details.Rows[x]["UOM"]);
                detail.UOMIndex = Convert.ToByte(dtSLReturn_Details.Rows[x]["UomIndex"]);
                detail.Qty = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["Qty"]);

                detail.Height = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["Height"]);
                detail.Length = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["Length"]);
                detail.Width = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["Width"]);
                detail.PiecesCount = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["PiecesCount"]);

                #region Expire
                if (dtSLReturn_Details.Rows[x]["Expire"] == DBNull.Value)
                    detail.Expire = null;
                else
                {
                    DateTime temp = Convert.ToDateTime(dtSLReturn_Details.Rows[x]["Expire"]);
                    temp = temp.AddDays(-temp.Day + 1);
                    detail.Expire = temp;
                }

                if (dtSLReturn_Details.Rows[x]["Batch"] == DBNull.Value
                    || dtSLReturn_Details.Rows[x]["Batch"].ToString().Trim() == string.Empty)
                    detail.Batch = null;
                else
                    detail.Batch = dtSLReturn_Details.Rows[x]["Batch"].ToString();

                if (dtSLReturn_Details.Rows[x]["Serial"] == DBNull.Value
                    || dtSLReturn_Details.Rows[x]["Serial"].ToString().Trim() == string.Empty)
                    detail.Serial = null;
                else
                    detail.Serial = dtSLReturn_Details.Rows[x]["Serial"].ToString();

                if (dtSLReturn_Details.Rows[x]["Serial2"] == DBNull.Value
                    || dtSLReturn_Details.Rows[x]["Serial2"].ToString().Trim() == string.Empty)
                    detail.Serial2 = null;
                else
                    detail.Serial2 = dtSLReturn_Details.Rows[x]["Serial2"].ToString();
                #endregion

                detail.SellPrice = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["SellPrice"]);
                //detail.PurchasePrice = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["PurchasePrice"]);
                detail.SalesTax = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["SalesTax"]);
                detail.SalesTaxRatio = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["SalesTaxRatio"]);

                //CustomTaxRatio
                detail.CustomTaxRatio = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["CustomTaxRatio"]);
                detail.CustomTax = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["CustomTax"]);

                detail.DiscountRatio = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["DiscountRatio"]);
                detail.DiscountRatio2 = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["DiscountRatio2"]);
                detail.DiscountRatio3 = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["DiscountRatio3"]);
                detail.DiscountValue = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["DiscountValue"]);

                if (dtSLReturn_Details.Rows[x]["MediumUOMFactor"].ToString() != string.Empty)
                    MediumUOMFactor = MyHelper.FractionToDouble(dtSLReturn_Details.Rows[x]["MediumUOMFactor"].ToString());
                if (dtSLReturn_Details.Rows[x]["LargeUOMFactor"].ToString() != string.Empty)
                    LargeUOMFactor = MyHelper.FractionToDouble(dtSLReturn_Details.Rows[x]["LargeUOMFactor"].ToString());

                if (dtSLReturn_Details.Rows[x]["VendorId"].ToString() != string.Empty)
                    detail.VendorId = Convert.ToInt32(dtSLReturn_Details.Rows[x]["VendorId"]);
                else
                    detail.VendorId = null;
                var xx = dtSLReturn_Details.Rows[x]["bonusDiscount"];
                if (dtSLReturn_Details.Rows[x]["bonusDiscount"].ToString() != string.Empty)
                    detail.bonusDiscount = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["bonusDiscount"]);
                detail.TotalSellPrice = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["TotalSellPrice"]);
                detail.ItemDescription = dtSLReturn_Details.Rows[x]["ItemDescription"].ToString();
                detail.ItemDescriptionEn = dtSLReturn_Details.Rows[x]["ItemDescriptionEn"].ToString();
                //store_id[x] = pr.StoreId;
                if (dtSLReturn_Details.Rows[x]["ManufactureDate"] != null && dtSLReturn_Details.Rows[x]["ManufactureDate"] != DBNull.Value)
                    detail.ManufactureDate = Convert.ToDateTime(dtSLReturn_Details.Rows[x]["ManufactureDate"]);

                #endregion

                if (Shared.LibraAvailabe)
                {
                    if (dtSLReturn_Details.Rows[x]["LibraQty"] != null && dtSLReturn_Details.Rows[x]["LibraQty"] != DBNull.Value && dtSLReturn_Details.Rows[x]["LibraQty"].ToString() != string.Empty)
                        detail.LibraQty = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["LibraQty"]);

                    if (dtSLReturn_Details.Rows[x]["kg_Weight_libra"] != null && dtSLReturn_Details.Rows[x]["kg_Weight_libra"] != DBNull.Value & dtSLReturn_Details.Rows[x]["kg_Weight_libra"].ToString() != string.Empty)
                        detail.kg_Weight_libra = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["kg_Weight_libra"]);
                }

                detail.Pack = dtSLReturn_Details.Rows[x]["Pack"] == null || dtSLReturn_Details.Rows[x]["Pack"] == DBNull.Value ? 0 : Convert.ToInt32(dtSLReturn_Details.Rows[x]["Pack"]);


                DB.SL_ReturnDetails.InsertOnSubmit(detail);

                #region Add Item To Store
                /*
                if ((Shared.InvoicePostToStore == true && Shared.StockIsPeriodic) ||
                    (Shared.InvoicePostToStore == true && Shared.StockIsPeriodic == false))
                {
                    //check if item is not service subtract from store
                    //decimal item_PurchasePrice = MyHelper.GetAveragePriceForSalesReturn(detail.UOMIndex, MediumUOMFactor, LargeUOMFactor, detail.ItemId,
                    //                                    pr.StoreId, pr.ReturnDate, detail.PurchasePrice);                    

                    decimal total_Qty = (detail.Height.HasValue ? detail.Height.Value : 1)
                                    * (detail.Length.HasValue ? detail.Length.Value : 1)
                                    * (detail.Width.HasValue ? detail.Width.Value : 1)
                                    * detail.Qty;

                    if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish)
                        total_Qty = detail.Qty;

                    decimal item_PurchasePrice = detail.TotalSellPrice / total_Qty;

                    if (Convert.ToInt32(dtSLReturn_Details.Rows[x]["ItemType"]) != (int)ItemType.Service
                        && Convert.ToInt32(dtSLReturn_Details.Rows[x]["ItemType"]) != (int)ItemType.Subtotal)
                    {
                        if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Multiply)
                        {
                            MyHelper.AddItemToStore(detail.ItemId, pr.StoreId, total_Qty, detail.UOMIndex,
                                MediumUOMFactor, LargeUOMFactor, detail.VendorId, detail.SL_ReturnDetailId, (int)Process.SellReturn,
                                true, total_Qty * item_PurchasePrice * pr.CrncRate, pr.ReturnDate, detail.Expire, detail.Batch, 0, 0, 0, detail.PiecesCount,
                                ParentItemId, M1, M2, M3, 0, null);
                            CostOfSoldGoods += total_Qty * item_PurchasePrice * pr.CrncRate;
                        }
                        else
                        {
                            MyHelper.AddItemToStore(detail.ItemId, pr.StoreId, total_Qty, detail.UOMIndex,
                                MediumUOMFactor, LargeUOMFactor, detail.VendorId, detail.SL_ReturnDetailId, (int)Process.SellReturn,
                                true, total_Qty * item_PurchasePrice * pr.CrncRate, pr.ReturnDate, detail.Expire, detail.Batch,
                                detail.Length.Value, detail.Width.Value, detail.Height.Value, detail.PiecesCount,
                                ParentItemId, M1, M2, M3, 0, null);
                            CostOfSoldGoods += total_Qty * item_PurchasePrice * pr.CrncRate;
                        }
                    }
                }
                else if (Shared.InvoicePostToStore == false && Shared.StockIsPeriodic == false)//just calc cost of sold goods
                {
                    //check if item is not service subtract from store
                    decimal item_PurchasePrice = MyHelper.GetAveragePriceForSalesReturn(detail.UOMIndex, MediumUOMFactor, LargeUOMFactor, detail.ItemId,
                                                        pr.StoreId, pr.ReturnDate, detail.PurchasePrice);

                    decimal total_Qty = (detail.Height.HasValue ? detail.Height.Value : 1)
                                    * (detail.Length.HasValue ? detail.Length.Value : 1)
                                    * (detail.Width.HasValue ? detail.Width.Value : 1)
                                    * detail.Qty;

                    if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish)
                        total_Qty = detail.Qty;

                    if (Convert.ToInt32(dtSLReturn_Details.Rows[x]["ItemType"]) != (int)ItemType.Service
                        && Convert.ToInt32(dtSLReturn_Details.Rows[x]["ItemType"]) != (int)ItemType.Subtotal)
                    {
                        CostOfSoldGoods += total_Qty * item_PurchasePrice * pr.CrncRate;
                    }
                }
                */
                #endregion

                int itemType = Convert.ToInt32(dtSLReturn_Details.Rows[x]["ItemType"]);
                /*Add to store*/
                if (itemType != (int)ItemType.Subtotal)
                {
                    if (dtSLReturn_Details.Rows[x]["MediumUOMFactor"].ToString() != string.Empty)
                        MediumUOMFactor = MyHelper.FractionToDouble(dtSLReturn_Details.Rows[x]["MediumUOMFactor"].ToString());
                    if (dtSLReturn_Details.Rows[x]["LargeUOMFactor"].ToString() != string.Empty)
                        LargeUOMFactor = MyHelper.FractionToDouble(dtSLReturn_Details.Rows[x]["LargeUOMFactor"].ToString());

                    int? ParentItemId = ErpUtils.GetMtrxVal(dtSLReturn_Details.Rows[x]["ParentItemId"]);
                    int? M1 = ErpUtils.GetMtrxVal(dtSLReturn_Details.Rows[x]["M1"]);
                    int? M2 = ErpUtils.GetMtrxVal(dtSLReturn_Details.Rows[x]["M2"]);
                    int? M3 = ErpUtils.GetMtrxVal(dtSLReturn_Details.Rows[x]["M3"]);

                    decimal CostPrice = 0;// MyHelper.GetLastCostPrice(lstItems.Where(z => z.ItemId == detail.ItemId).First(), detail.UOMIndex, DB, pr.ReturnDate, pr.StoreId);
                    dtSLReturn_Details.Rows[x]["PurchasePrice"] = detail.PurchasePrice = CostPrice;
                    lst_outitems.Add(new StoreItem(Shared.st_Store.MultiplyDimensions, detail.ItemId, itemType, detail.SL_ReturnDetailId, detail.UOMIndex, detail.Qty,
                        MediumUOMFactor, LargeUOMFactor, detail.Expire, detail.Batch, null, detail.VendorId, detail.Length.Value, detail.Width.Value, detail.Height.Value,
                        detail.PiecesCount, ParentItemId, M1, M2, M3, detail.TotalSellPrice * pr.CrncRate, CostPrice * detail.Qty, detail, detail.Serial, detail.Serial2,
                        Convert.ToInt32(dtSLReturn_Details.Rows[x]["CategoryId"]), detail.Pack));
                }
                //if (dtSLReturn_Details.Rows[x]["TaxType"] != null && dtSLReturn_Details.Rows[x]["TaxType"] != DBNull.Value)
                //{
                //    DB.SubmitChanges();
                //    SlReturnInvoiceDetailSubTaxValue dTax = new SlReturnInvoiceDetailSubTaxValue();
                //    dTax.ReturnInvoiceDetailId = detail.SL_ReturnDetailId;
                //    dTax.esubTypeId = Convert.ToInt32(dtSLReturn_Details.Rows[x]["TaxType"]);
                //    dTax.TaxRatio = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["ETaxRatio"]);
                //    dTax.value = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["EtaxValue"]);
                //    DB.SlReturnInvoiceDetailSubTaxValues.InsertOnSubmit(dTax);
                //}
                DB.SubmitChanges();
                //if (Shared.st_Store.E_AllowMoreThanTax == false || Shared.st_Store.E_AllowMoreThanTax == null)
                //{
                //    if (dtSLReturn_Details.Rows[x]["TaxType"] != null && dtSLReturn_Details.Rows[x]["TaxType"] != DBNull.Value)
                //    {
                //        DB.SubmitChanges();
                //        SlReturnInvoiceDetailSubTaxValue dTax = new SlReturnInvoiceDetailSubTaxValue();
                //        dTax.ReturnInvoiceDetailId = detail.SL_ReturnDetailId;
                //        dTax.esubTypeId = Convert.ToInt32(dtSLReturn_Details.Rows[x]["TaxType"]);
                //        dTax.TaxRatio = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["ETaxRatio"]);
                //        dTax.value = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["EtaxValue"]);
                //        DB.SlReturnInvoiceDetailSubTaxValues.InsertOnSubmit(dTax);
                //    }
                //}

                //else
                //{
                //    //if (Dt_Rows.Rows.Count > 0)
                //    //{

                //    //    foreach (DataRow dd in Dt_Rows.Rows)
                //    //    {
                //    //        DB.SubmitChanges();
                //    //        if (Convert.ToInt32(dd["RowHandle"]) == Convert.ToInt32(dtSLReturn_Details.Rows[x]["RowHandle"]))
                //    //        {
                //    //            SlReturnInvoiceDetailSubTaxValue dTax = new SlReturnInvoiceDetailSubTaxValue();
                //    //            decimal DiscR1 = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["DiscountRatio"]);
                //    //            decimal DiscR2 = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["DiscountRatio2"]);
                //    //            decimal DiscR3 = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["DiscountRatio3"]);

                //    //            decimal TotalSellPrice = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["Qty"]) * Convert.ToDecimal(dtSLReturn_Details.Rows[x]["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);

                //    //            bool calcTaxBeforeDisc = Convert.ToBoolean(dtSLReturn_Details.Rows[x]["calcTaxBeforeDisc"]);

                //    //            decimal taxvalue = calcTaxBeforeDisc ?
                //    //                Math.Round(Convert.ToDecimal(dtSLReturn_Details.Rows[x]["SellPrice"]) * Convert.ToDecimal(dtSLReturn_Details.Rows[x]["Qty"]) * Convert.ToDecimal(dd["Percentage"]) / 100, defaultRoundingPoints)
                //    //                : Math.Round(TotalSellPrice * Convert.ToDecimal(dd["Percentage"]) / 100, defaultRoundingPoints);
                //    //            dTax.esubTypeId = Convert.ToInt32(dd["SubTax"]);
                //    //            dTax.TaxRatio = Convert.ToDecimal(dd["Percentage"]);
                //    //            dTax.ReturnInvoiceDetailId = detail.SL_ReturnDetailId;
                //    //            dTax.value = taxvalue;
                //    //            DB.SlReturnInvoiceDetailSubTaxValues.InsertOnSubmit(dTax);
                //    //        }
                //    //    }
                //    //}

                //    if (Dt_Rows.Rows.Count > 0)
                //    {

                //        decimal totalTableTaxRatio = 0;
                //        var tableTaxIds = DB.E_TaxableTypes.Where(a => tableTax.Contains(a.Code)).Select(a => a.E_TaxableTypeId.ToString()).ToList();

                //        var tableTaxes = Dt_Rows.AsEnumerable()
                //            .Where(a => a.RowState != DataRowState.Deleted)
                //            .Where(r => tableTaxIds.Contains(r["Tax"].ToString())).ToList();

                //        var otherTaxes = Dt_Rows.AsEnumerable()
                //            .Where(a => a.RowState != DataRowState.Deleted)
                //            .Where(r => !tableTaxIds.Contains(r["Tax"].ToString()) && r["Tax"].ToString() != "1").ToList();
                //        //=================Case 1===================//
                //        foreach (DataRow row in tableTaxes)
                //        {
                //            if (Convert.ToInt32(row["RowHandle"]) == Convert.ToInt32(dtSLReturn_Details.Rows[x]["RowHandle"]))
                //            {
                //                SlReturnInvoiceDetailSubTaxValue dTax = new SlReturnInvoiceDetailSubTaxValue();
                //                decimal DiscR1 = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["DiscountRatio"]);
                //                decimal DiscR2 = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["DiscountRatio2"]);
                //                decimal DiscR3 = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["DiscountRatio3"]);

                //                decimal TotalSellPrice = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["Qty"]) * Convert.ToDecimal(dtSLReturn_Details.Rows[x]["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);

                //                bool calcTaxBeforeDisc = Convert.ToBoolean(dtSLReturn_Details.Rows[x]["calcTaxBeforeDisc"]);

                //                decimal taxvalue = calcTaxBeforeDisc ?
                //                    Math.Round(Convert.ToDecimal(dtSLReturn_Details.Rows[x]["SellPrice"]) * Convert.ToDecimal(dtSLReturn_Details.Rows[x]["Qty"]) * Convert.ToDecimal(row["Percentage"]) / 100, defaultRoundingPoints)
                //                    : Math.Round(TotalSellPrice * Convert.ToDecimal(row["Percentage"]) / 100, defaultRoundingPoints);
                //                dTax.esubTypeId = Convert.ToInt32(row["SubTax"]);
                //                dTax.TaxRatio = Math.Round(Convert.ToDecimal(row["Percentage"]), defaultRoundingPoints);
                //                dTax.ReturnInvoiceDetailId = detail.SL_ReturnDetailId;
                //                dTax.value = taxvalue;
                //                totalTableTaxRatio += Convert.ToDecimal(row["Percentage"]);
                //                DB.SlReturnInvoiceDetailSubTaxValues.InsertOnSubmit(dTax);
                //            }
                //        }
                //        //=================Case 2===================//
                //        foreach (DataRow mm in Dt_Rows.AsEnumerable()
                //            .Where(a => a.RowState != DataRowState.Deleted)
                //            .Where(r => r["Tax"].ToString() == "1").ToList())
                //        {

                //            if (Convert.ToInt32(mm["RowHandle"]) == Convert.ToInt32(dtSLReturn_Details.Rows[x]["RowHandle"]))
                //            {
                //                SlReturnInvoiceDetailSubTaxValue dTax = new SlReturnInvoiceDetailSubTaxValue();
                //                decimal DiscR1 = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["DiscountRatio"]);
                //                decimal DiscR2 = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["DiscountRatio2"]);
                //                decimal DiscR3 = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["DiscountRatio3"]);

                //                decimal TotalSellPrice = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["Qty"]) * Convert.ToDecimal(dtSLReturn_Details.Rows[x]["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);
                //                var totalSellPriceWithTableTaxes = Convert.ToDecimal((totalTableTaxRatio / 100)) * TotalSellPrice;
                //                TotalSellPrice = TotalSellPrice + totalSellPriceWithTableTaxes;
                //                bool calcTaxBeforeDisc = Convert.ToBoolean(dtSLReturn_Details.Rows[x]["calcTaxBeforeDisc"]);

                //                decimal taxvalue = calcTaxBeforeDisc ?
                //                    Math.Round(Convert.ToDecimal(dtSLReturn_Details.Rows[x]["SellPrice"]) * Convert.ToDecimal(dtSLReturn_Details.Rows[x]["Qty"]) * Convert.ToDecimal(mm["Percentage"]) / 100, defaultRoundingPoints)
                //                    : Math.Round(TotalSellPrice * Convert.ToDecimal(mm["Percentage"]) / 100, defaultRoundingPoints);
                //                dTax.esubTypeId = Convert.ToInt32(mm["SubTax"]);
                //                dTax.TaxRatio = Math.Round(Convert.ToDecimal(mm["Percentage"]), defaultRoundingPoints);
                //                dTax.ReturnInvoiceDetailId = detail.SL_ReturnDetailId; 
                //                dTax.value = taxvalue;
                //                DB.SlReturnInvoiceDetailSubTaxValues.InsertOnSubmit(dTax);
                //            }
                //        }
                //        //==============Case 3=================//
                //        foreach (DataRow m in otherTaxes)
                //        {
                //            if (Convert.ToInt32(m["RowHandle"]) == Convert.ToInt32(dtSLReturn_Details.Rows[x]["RowHandle"]))
                //            {
                //                SlReturnInvoiceDetailSubTaxValue dTax = new SlReturnInvoiceDetailSubTaxValue();
                //                decimal DiscR1 = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["DiscountRatio"]);
                //                decimal DiscR2 = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["DiscountRatio2"]);
                //                decimal DiscR3 = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["DiscountRatio3"]);

                //                decimal TotalSellPrice = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["Qty"]) * Convert.ToDecimal(dtSLReturn_Details.Rows[x]["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);

                //                bool calcTaxBeforeDisc = Convert.ToBoolean(dtSLReturn_Details.Rows[x]["calcTaxBeforeDisc"]);

                //                decimal taxvalue = calcTaxBeforeDisc ?
                //                    Math.Round(Convert.ToDecimal(dtSLReturn_Details.Rows[x]["SellPrice"]) * Convert.ToDecimal(dtSLReturn_Details.Rows[x]["Qty"]) * Convert.ToDecimal(m["Percentage"]) / 100, defaultRoundingPoints)
                //                    : Math.Round(TotalSellPrice * Convert.ToDecimal(m["Percentage"]) / 100, defaultRoundingPoints);
                //                dTax.esubTypeId = Convert.ToInt32(m["SubTax"]);
                //                dTax.TaxRatio = Math.Round(Convert.ToDecimal(m["Percentage"]), defaultRoundingPoints);
                //                dTax.ReturnInvoiceDetailId = detail.SL_ReturnDetailId;
                //                dTax.value = taxvalue;
                //                DB.SlReturnInvoiceDetailSubTaxValues.InsertOnSubmit(dTax);
                //            }
                //        }


                //    }
                //}
                #region Save Taxes
                if (Dt_Rows.Rows.Count > 0)
                {

                    foreach (DataRow dd in Dt_Rows.Rows)
                    {
                        if (dd.RowState == DataRowState.Deleted)
                            continue;
                        if (Convert.ToInt32(dd["RowHandle"]) == Convert.ToInt32(dtSLReturn_Details.Rows[x]["RowHandle"]))
                        {
                            SlReturnInvoiceDetailSubTaxValue dTax = new SlReturnInvoiceDetailSubTaxValue();
                            dTax.esubTypeId = Convert.ToInt32(dd["SubTax"]);
                            dTax.TaxRatio = Convert.ToDecimal(dd["Percentage"]);
                            dTax.ReturnInvoiceDetailId = detail.SL_ReturnDetailId;

                            dTax.value = Convert.ToDecimal(dtSLReturn_Details.Rows[x]["TaxValue"]);
                            DB.SlReturnInvoiceDetailSubTaxValues.InsertOnSubmit(dTax);
                        }
                    }
                }
                #endregion
            }

            DB.SubmitChanges();
            lst_outitems.ForEach(x => x.SourceId = ((SL_ReturnDetail)x.Source).SL_ReturnDetailId);
            var lst = MyHelper.Add_to_store((int)Process.SellReturn, pr.ReturnDate, lst_outitems, pr.StoreId);
            CostOfSoldGoods = lst.Select(x => x.PurchasePrice).ToList().DefaultIfEmpty(0).Sum();
            //if (Shared.InvoicePostToStore)
            //    DB.IC_ItemStores.InsertAllOnSubmit(lst);
            DB.SubmitChanges();


            invoiceId = pr.SL_ReturnId;

            DB.SubmitChanges();

            //CreateJournal(DB, pr, CostOfSoldGoods,
            //    lst_Cat, lst_outitems, lst);

            DoValidate();
            DataModified = false;
            dtSLReturn_Details.AcceptChanges();

            //XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.MsgSave : ResSLAr.MsgSave, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void CreateJournal(ERPDataContext DB, DAL.SL_Return pr, decimal CostOfSoldGoods,
            List<IC_Category> lstCats, List<StoreItem> lstSoldItems, List<IC_ItemStore> lstInvItems)
        {
            string note =
                (Shared.IsEnglish == true ? ResSLEn.txtSLReturnInvNumber : ResSLAr.txtSLReturnInvNumber) + " " + pr.ReturnCode + "  - " +
                    (Shared.IsEnglish == true ? "from " : "من ") + " " + lkp_Customers.Text;

            int BranchId = Convert.ToInt32(lkpStore.EditValue);
            if (lkpStore.GetColumnValue("ParentId") != null)
                BranchId = Convert.ToInt32(lkpStore.GetColumnValue("ParentId"));

            int CustomerAccountId = lst_Customers.Where(x => x.CustomerId == pr.CustomerId).Select(x => x.AccountId.Value).FirstOrDefault();

            #region Save_Jornal
            DAL.ACC_Journal jornal;
            if (pr.JornalId > 0)
                jornal = DB.ACC_Journals.Where(x => x.JournalId == pr.JornalId).FirstOrDefault();
            else
            {
                jornal = new ACC_Journal();
                jornal.InsertDate = dtInvoiceDate.DateTime;
                jornal.InsertUser = Shared.UserId;
                jornal.JCode = HelperAcc.Get_Jornal_Code();
                jornal.IsPosted = !Shared.OfflinePostToGL;
                jornal.Monthly_Code = HelperAcc.Get_Jornal_Monthly_Code(jornal.InsertDate, jornal.ProcessId);
                DB.ACC_Journals.InsertOnSubmit(jornal);
            }

            jornal.JNumber = pr.ReturnCode;
            jornal.LastUpdateDate = MyHelper.Get_Server_DateTime();
            jornal.LastUpdateUser = Shared.UserId;
            jornal.JNotes = note;
            jornal.ProcessId = (int)Process.SellReturn;
            jornal.SourceId = pr.SL_ReturnId;
            jornal.StoreId = BranchId;
            jornal.InsertDate = dtInvoiceDate.DateTime;
            jornal.CrncId = pr.CrncId;
            jornal.CrncRate = pr.CrncRate;
            #endregion

            DB.SubmitChanges();
            pr.JornalId = jornal.JournalId;

            var jd = DB.ACC_JournalDetails.Where(x => x.JournalId == jornal.JournalId);
            #region Delete Multiple CC
            var multCC = DB.Acc_Journal_CostCenters.Where(j => jd.Select(x => x.JDetailId).Contains(j.Journal_Detail_Id));
            DB.Acc_Journal_CostCenters.DeleteAllOnSubmit(multCC);
            #endregion
            DB.ACC_JournalDetails.DeleteAllOnSubmit(jd);
            //DB.ACC_JournalDetails.DeleteAllOnSubmit(DB.ACC_JournalDetails.Where(x => x.JournalId == pr.JornalId));

            decimal total_Returns = pr.Net - pr.TaxValue - pr.CustomTaxValue + pr.DiscountValue + pr.Expenses + pr.DeductTaxValue - pr.AddTaxValue;

            //int? costCenter = pr.CostCenterId;
            //int? costCenter = Convert.ToInt32(lkpStore.GetColumnValue("CostCenterId"));      // تحميل مركز تكلفة المخزن
            //if (costCenter == 0)
            //    costCenter = null;

            #region Sales Return
            /*قيد ارتجاع النقدية */
            /*من حساب مردودات و مسموحات المبيعات*/
            if (!Shared.ItemsPostingAvailable)
            {
                DAL.ACC_JournalDetail jornal_Detail_1 = new DAL.ACC_JournalDetail();
                jornal_Detail_1.JournalId = jornal.JournalId;
                jornal_Detail_1.AccountId = Convert.ToInt32(lkpStore.GetColumnValue("SellReturnAccount"));      // حساب مردودات و مسموحات مبيعات المخزن
                //jornal_Detail_1.CostCenter = costCenter;
                jornal_Detail_1.Credit = 0;
                jornal_Detail_1.Debit = total_Returns - pr.Expenses;
                jornal_Detail_1.Notes = note;
                jornal_Detail_1.CrncId = pr.CrncId;
                jornal_Detail_1.CrncRate = pr.CrncRate;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_1);

                DB.SubmitChanges();
                foreach (DataRow d in dt_Multi_CC.Rows)
                {
                    if (dt_Multi_CC.Rows[0][0] == d[0])
                    {
                        jornal_Detail_1.CostCenter = Convert.ToInt32(d[0]);
                        pr.CostCenterId = Convert.ToInt32(d[0]);
                    }
                    else
                    {
                        Acc_Journal_CostCenter Jcc = new Acc_Journal_CostCenter();
                        Jcc.CostCenter_Id = Convert.ToInt32(d[0]);
                        Jcc.Journal_Detail_Id = jornal_Detail_1.JDetailId;
                        Jcc.Amount = jornal_Detail_1.Debit;
                        Jcc.Percentage = 100;
                        Jcc.SourceId = pr.SL_ReturnId;
                        Jcc.ProcessId = (int)Process.SellReturn;
                        DB.Acc_Journal_CostCenters.InsertOnSubmit(Jcc);
                    }
                }

            }
            else
            {
                //get sold and store data
                var soldItems1 = (from x in lstSoldItems
                                  group x by new { x.CategoryId, x.ItemId } into grp
                                  select new
                                  {
                                      CatId = grp.Key.CategoryId,
                                      ItemId = grp.Key.ItemId,
                                      TotalCost = 0,
                                      TotalSell = grp.Sum(z => z.TotalLocalSellPrice)
                                  }).ToList();

                var storeItems1 = (from x in lstInvItems
                                   group x by x.ItemId into grp
                                   select new
                                   {
                                       ItemId = grp.Key,
                                       catId = soldItems1.Where(x => x.ItemId == grp.Key).Select(x => x.CatId).First(),
                                       TotalCost = grp.Sum(z => z.PurchasePrice),
                                       TotalSell = 0
                                   }).ToList();

                //group by category
                var soldItems2 = (from x in soldItems1
                                  group x by x.CatId into grp
                                  select new ItemPosting
                                  {
                                      catId = grp.Key,
                                      Cost = 0,
                                      Price = grp.Sum(z => z.TotalSell)
                                  }).ToList();

                var storeItems2 = (from x in storeItems1
                                   group x by x.catId into grp
                                   select new ItemPosting
                                   {
                                       catId = grp.Key,
                                       Cost = grp.Sum(z => z.TotalCost),
                                       Price = 0
                                   }).ToList();

                //summary rows per category
                List<ItemPosting> lstRows;
                if (!Shared.StockIsPeriodic)
                    if (Shared.InvoicePostToStore == true || chk_IsInTrns.Checked == true)
                    {
                        lstRows = (from x in soldItems2.Union(storeItems2)
                                   group x by x.catId into grp
                                   select new ItemPosting
                                   {
                                       catId = grp.Key,
                                       Cost = grp.Sum(x => x.Cost),
                                       Price = grp.Sum(x => x.Price),
                                       SellReturnAcc = lstCats.Where(x => x.CategoryId == grp.Key).Select(x => x.SellReturnAcc.Value).First(),
                                       COGSAcc = lstCats.Where(x => x.CategoryId == grp.Key).Select(x => x.COGSAcc.Value).First(),
                                       InvAcc = lstCats.Where(x => x.CategoryId == grp.Key).Select(x => x.InvAcc.Value).First(),
                                   }).ToList();
                    }
                    else
                    {
                        lstRows = (from x in soldItems2.Union(storeItems2)
                                   group x by x.catId into grp
                                   select new ItemPosting
                                   {
                                       catId = grp.Key,
                                       Cost = grp.Sum(x => x.Cost),
                                       Price = grp.Sum(x => x.Price),
                                       SellReturnAcc = lstCats.Where(x => x.CategoryId == grp.Key).Select(x => x.SellReturnAcc.Value).First(),
                                       COGSAcc = lstCats.Where(x => x.CategoryId == grp.Key).Select(x => x.COGSAcc.Value).First(),
                                       InvAcc = Shared.st_Store.intermediateInventoryAcc ?? Shared.st_Store.intermediateInventoryAcc.Value
                                   }).ToList();
                    }
                else
                    lstRows = (from x in soldItems2.Union(storeItems2)
                               group x by x.catId into grp
                               select new ItemPosting
                               {
                                   catId = grp.Key,
                                   Cost = grp.Sum(x => x.Cost),
                                   Price = grp.Sum(x => x.Price),
                                   SellReturnAcc = lstCats.Where(x => x.CategoryId == grp.Key).Select(x => x.SellReturnAcc.Value).First(),
                               }).ToList();
                //post sales
                var salesRows = from x in lstRows
                                    //where x.Price > 0
                                group x by x.SellReturnAcc into grp
                                select new
                                {
                                    SellReturnAcc = grp.Key,
                                    Price = grp.Sum(x => x.Price)
                                };
                foreach (var r in salesRows)
                {

                    DAL.ACC_JournalDetail jornal_Detail_1 = new DAL.ACC_JournalDetail();
                    jornal_Detail_1.JournalId = jornal.JournalId;
                    jornal_Detail_1.AccountId = r.SellReturnAcc;
                    //jornal_Detail_1.CostCenter = costCenter;
                    jornal_Detail_1.Credit = 0;
                    jornal_Detail_1.Debit = r.Price / pr.CrncRate;
                    jornal_Detail_1.Notes = note;
                    jornal_Detail_1.CrncId = pr.CrncId;
                    jornal_Detail_1.CrncRate = pr.CrncRate;
                    DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_1);

                    DB.SubmitChanges();
                    foreach (DataRow d in dt_Multi_CC.Rows)
                    {
                        if (dt_Multi_CC.Rows[0][0] == d[0])
                        {
                            jornal_Detail_1.CostCenter = Convert.ToInt32(d[0]);
                            pr.CostCenterId = Convert.ToInt32(d[0]);
                        }
                        else
                        {
                            Acc_Journal_CostCenter Jcc = new Acc_Journal_CostCenter();
                            Jcc.CostCenter_Id = Convert.ToInt32(d[0]);
                            Jcc.Journal_Detail_Id = jornal_Detail_1.JDetailId;
                            Jcc.Amount = jornal_Detail_1.Debit;
                            Jcc.Percentage = 100;
                            Jcc.SourceId = pr.SL_ReturnId;
                            Jcc.ProcessId = (int)Process.SellReturn;
                            DB.Acc_Journal_CostCenters.InsertOnSubmit(Jcc);
                        }
                    }
                }

                //post inv
                var invRows = from x in lstRows
                                  //where x.Cost > 0
                              group x by x.InvAcc into grp
                              select new
                              {
                                  InvAcc = grp.Key,
                                  Cost = grp.Sum(x => x.Cost)
                              };
                foreach (var r in invRows)
                {
                    DAL.ACC_JournalDetail jdCost2 = new DAL.ACC_JournalDetail();
                    jdCost2.JournalId = jornal.JournalId;
                    jdCost2.AccountId = r.InvAcc;
                    jdCost2.Credit = 0;
                    jdCost2.Debit = r.Cost / pr.CrncRate;
                    //jdCost2.CostCenter = costCenter;
                    jdCost2.Notes = note;
                    jdCost2.CrncId = pr.CrncId;
                    jdCost2.CrncRate = pr.CrncRate;
                    DB.ACC_JournalDetails.InsertOnSubmit(jdCost2);

                    DB.SubmitChanges();
                    foreach (DataRow d in dt_Multi_CC.Rows)
                    {
                        if (dt_Multi_CC.Rows[0][0] == d[0])
                        {
                            jdCost2.CostCenter = Convert.ToInt32(d[0]);
                            pr.CostCenterId = Convert.ToInt32(d[0]);
                        }
                        else
                        {
                            Acc_Journal_CostCenter Jcc = new Acc_Journal_CostCenter();
                            Jcc.CostCenter_Id = Convert.ToInt32(d[0]);
                            Jcc.Journal_Detail_Id = jdCost2.JDetailId;
                            Jcc.Amount = jdCost2.Debit;
                            Jcc.Percentage = 100;
                            Jcc.SourceId = pr.SL_ReturnId;
                            Jcc.ProcessId = (int)Process.SellReturn;
                            DB.Acc_Journal_CostCenters.InsertOnSubmit(Jcc);
                        }
                    }
                }

                //post cogs
                var cogsRows = from x in lstRows
                                   //where x.Cost > 0
                               group x by x.COGSAcc into grp
                               select new
                               {
                                   InvAcc = grp.Key,
                                   Cost = grp.Sum(x => x.Cost)
                               };
                foreach (var r in cogsRows)
                {
                    DAL.ACC_JournalDetail jdCost1 = new DAL.ACC_JournalDetail();
                    jdCost1.JournalId = jornal.JournalId;
                    jdCost1.AccountId = r.InvAcc;
                    jdCost1.Credit = r.Cost / pr.CrncRate;
                    jdCost1.Debit = 0;
                    jdCost1.Notes = note;
                    jdCost1.CrncId = pr.CrncId;
                    jdCost1.CrncRate = pr.CrncRate;
                    //jdCost1.CostCenter = costCenter;
                    DB.ACC_JournalDetails.InsertOnSubmit(jdCost1);

                    DB.SubmitChanges();
                    foreach (DataRow d in dt_Multi_CC.Rows)
                    {
                        if (dt_Multi_CC.Rows[0][0] == d[0])
                        {
                            jdCost1.CostCenter = Convert.ToInt32(d[0]);
                            pr.CostCenterId = Convert.ToInt32(d[0]);
                        }
                        else
                        {
                            Acc_Journal_CostCenter Jcc = new Acc_Journal_CostCenter();
                            Jcc.CostCenter_Id = Convert.ToInt32(d[0]);
                            Jcc.Journal_Detail_Id = jdCost1.JDetailId;
                            Jcc.Amount = jdCost1.Debit;
                            Jcc.Percentage = 100;
                            Jcc.SourceId = pr.SL_ReturnId;
                            Jcc.ProcessId = (int)Process.SellReturn;
                            DB.Acc_Journal_CostCenters.InsertOnSubmit(Jcc);
                        }
                    }
                }
            }
            #endregion

            /*من حساب ض ع */
            if (pr.TaxValue > 0)
            {
                DAL.ACC_JournalDetail jornal_Detail_Tax = new DAL.ACC_JournalDetail();
                jornal_Detail_Tax.JournalId = jornal.JournalId;
                jornal_Detail_Tax.AccountId = Shared.st_Store.TaxAcc.Value;//حساب ضريبة المبيعات
                //jornal_Detail_Tax.CostCenter = costCenter;
                jornal_Detail_Tax.Credit = 0;
                jornal_Detail_Tax.Debit = pr.TaxValue;
                jornal_Detail_Tax.Notes = note + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Tax : ResSLAr.txt_Tax);
                jornal_Detail_Tax.CrncId = pr.CrncId;
                jornal_Detail_Tax.CrncRate = pr.CrncRate;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_Tax);

                DB.SubmitChanges();
                foreach (DataRow d in dt_Multi_CC.Rows)
                {
                    if (dt_Multi_CC.Rows[0][0] == d[0])
                    {
                        jornal_Detail_Tax.CostCenter = Convert.ToInt32(d[0]);
                        pr.CostCenterId = Convert.ToInt32(d[0]);
                    }
                    else
                    {
                        Acc_Journal_CostCenter Jcc = new Acc_Journal_CostCenter();
                        Jcc.CostCenter_Id = Convert.ToInt32(d[0]);
                        Jcc.Journal_Detail_Id = jornal_Detail_Tax.JDetailId;
                        Jcc.Amount = jornal_Detail_Tax.Debit;
                        Jcc.Percentage = 100;
                        Jcc.SourceId = pr.SL_ReturnId;
                        Jcc.ProcessId = (int)Process.SellReturn;
                        DB.Acc_Journal_CostCenters.InsertOnSubmit(Jcc);
                    }
                }
            }

            /*من حساب ض ج */
            if (pr.CustomTaxValue > 0)
            {
                DAL.ACC_JournalDetail jornal_Detail_Tax2 = new DAL.ACC_JournalDetail();
                jornal_Detail_Tax2.JournalId = jornal.JournalId;
                jornal_Detail_Tax2.AccountId = Shared.st_Store.CustomTaxAcc.Value;//حساب ضريبة الجدول
                //jornal_Detail_Tax2.CostCenter = costCenter;
                jornal_Detail_Tax2.Credit = 0;
                jornal_Detail_Tax2.Debit = pr.CustomTaxValue;
                jornal_Detail_Tax2.Notes = note + "\r\n" + (Shared.IsEnglish ? "Custom Tax" : "ضريبة الجدول");
                jornal_Detail_Tax2.CrncId = pr.CrncId;
                jornal_Detail_Tax2.CrncRate = pr.CrncRate;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_Tax2);

                DB.SubmitChanges();
                foreach (DataRow d in dt_Multi_CC.Rows)
                {
                    if (dt_Multi_CC.Rows[0][0] == d[0])
                    {
                        jornal_Detail_Tax2.CostCenter = Convert.ToInt32(d[0]);
                        pr.CostCenterId = Convert.ToInt32(d[0]);
                    }
                    else
                    {
                        Acc_Journal_CostCenter Jcc = new Acc_Journal_CostCenter();
                        Jcc.CostCenter_Id = Convert.ToInt32(d[0]);
                        Jcc.Journal_Detail_Id = jornal_Detail_Tax2.JDetailId;
                        Jcc.Amount = jornal_Detail_Tax2.Debit;
                        Jcc.Percentage = 100;
                        Jcc.SourceId = pr.SL_ReturnId;
                        Jcc.ProcessId = (int)Process.SellReturn;
                        DB.Acc_Journal_CostCenters.InsertOnSubmit(Jcc);
                    }
                }
            }

            /*من حساب ض الاضافة */
            if (pr.AddTaxValue > 0)
            {
                DAL.ACC_JournalDetail jornal_Detail_Tax = new DAL.ACC_JournalDetail();
                jornal_Detail_Tax.JournalId = jornal.JournalId;
                jornal_Detail_Tax.AccountId = Shared.st_Store.SalesAddTaxAccount.Value;//حساب ضريبة الاضافة
                //jornal_Detail_Tax.CostCenter = costCenter;
                jornal_Detail_Tax.Credit = 0;
                jornal_Detail_Tax.Debit = pr.AddTaxValue;
                jornal_Detail_Tax.Notes = note + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Tax : ResSLAr.txt_Tax);
                jornal_Detail_Tax.CrncId = pr.CrncId;
                jornal_Detail_Tax.CrncRate = pr.CrncRate;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_Tax);

                DB.SubmitChanges();
                foreach (DataRow d in dt_Multi_CC.Rows)
                {
                    if (dt_Multi_CC.Rows[0][0] == d[0])
                    {
                        jornal_Detail_Tax.CostCenter = Convert.ToInt32(d[0]);
                        pr.CostCenterId = Convert.ToInt32(d[0]);
                    }
                    else
                    {
                        Acc_Journal_CostCenter Jcc = new Acc_Journal_CostCenter();
                        Jcc.CostCenter_Id = Convert.ToInt32(d[0]);
                        Jcc.Journal_Detail_Id = jornal_Detail_Tax.JDetailId;
                        Jcc.Amount = jornal_Detail_Tax.Debit;
                        Jcc.Percentage = 100;
                        Jcc.SourceId = pr.SL_ReturnId;
                        Jcc.ProcessId = (int)Process.SellReturn;
                        DB.Acc_Journal_CostCenters.InsertOnSubmit(Jcc);
                    }
                }
            }

            /*الى حســاب كل من*/
            /* حساب العميل*/
            DAL.ACC_JournalDetail jornal_Detail_2 = new DAL.ACC_JournalDetail();
            jornal_Detail_2.JournalId = jornal.JournalId;
            jornal_Detail_2.AccountId = CustomerAccountId;          //حساب عميل  
            //jornal_Detail_2.CostCenter = costCenter;
            jornal_Detail_2.Credit = total_Returns - pr.Expenses + pr.TaxValue + pr.CustomTaxValue - pr.DeductTaxValue + pr.AddTaxValue;
            jornal_Detail_2.Debit = 0;
            jornal_Detail_2.Notes = note;
            jornal_Detail_2.CrncId = pr.CrncId;
            jornal_Detail_2.CrncRate = pr.CrncRate;
            DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_2);
            DB.SubmitChanges();
            foreach (DataRow d in dt_Multi_CC.Rows)
            {
                if (dt_Multi_CC.Rows[0][0] == d[0])
                {
                    jornal_Detail_2.CostCenter = Convert.ToInt32(d[0]);
                    pr.CostCenterId = Convert.ToInt32(d[0]);
                }
                else
                {
                    Acc_Journal_CostCenter Jcc = new Acc_Journal_CostCenter();
                    Jcc.CostCenter_Id = Convert.ToInt32(d[0]);
                    Jcc.Journal_Detail_Id = jornal_Detail_2.JDetailId;
                    Jcc.Amount = jornal_Detail_2.Credit;
                    Jcc.Percentage = 100;
                    Jcc.SourceId = pr.SL_ReturnId;
                    Jcc.ProcessId = (int)Process.SellReturn;
                    DB.Acc_Journal_CostCenters.InsertOnSubmit(Jcc);
                }
            }

            /*الى حساب ضريبة الخصم*/
            #region Deduct_Tax_Value
            if (pr.DeductTaxValue > 0)
            {
                DAL.ACC_JournalDetail jornal_Detail_deduct_Tax = new DAL.ACC_JournalDetail();
                jornal_Detail_deduct_Tax.JournalId = jornal.JournalId;
                jornal_Detail_deduct_Tax.AccountId = Shared.st_Store.SalesDeductTaxAccount.Value;
                //jornal_Detail_deduct_Tax.CostCenter = costCenter;
                jornal_Detail_deduct_Tax.Credit = pr.DeductTaxValue;
                jornal_Detail_deduct_Tax.Debit = 0;
                jornal_Detail_deduct_Tax.Notes = note;
                jornal_Detail_deduct_Tax.CrncId = pr.CrncId;
                jornal_Detail_deduct_Tax.CrncRate = pr.CrncRate;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_deduct_Tax);

                DB.SubmitChanges();
                foreach (DataRow d in dt_Multi_CC.Rows)
                {
                    if (dt_Multi_CC.Rows[0][0] == d[0])
                    {
                        jornal_Detail_deduct_Tax.CostCenter = Convert.ToInt32(d[0]);
                        pr.CostCenterId = Convert.ToInt32(d[0]);
                    }
                    else
                    {
                        Acc_Journal_CostCenter Jcc = new Acc_Journal_CostCenter();
                        Jcc.CostCenter_Id = Convert.ToInt32(d[0]);
                        Jcc.Journal_Detail_Id = jornal_Detail_deduct_Tax.JDetailId;
                        Jcc.Amount = jornal_Detail_deduct_Tax.Credit;
                        Jcc.Percentage = 100;
                        Jcc.SourceId = pr.SL_ReturnId;
                        Jcc.ProcessId = (int)Process.SellReturn;
                        DB.Acc_Journal_CostCenters.InsertOnSubmit(Jcc);
                    }
                }
            }
            #endregion

            #region Discount
            /*قيد الخصم*/
            if (pr.DiscountValue > 0)
            {
                DAL.ACC_JournalDetail jornal_Detail_5 = new DAL.ACC_JournalDetail();
                jornal_Detail_5.JournalId = jornal.JournalId;//العميل                
                jornal_Detail_5.AccountId = CustomerAccountId;//حساب عميل  
                //jornal_Detail_5.CostCenter = costCenter;
                jornal_Detail_5.Credit = 0;
                jornal_Detail_5.Debit = pr.DiscountValue;
                jornal_Detail_5.Notes = note + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Discount : ResSLAr.txt_Discount);
                jornal_Detail_5.CrncId = pr.CrncId;
                jornal_Detail_5.CrncRate = pr.CrncRate;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_5);

                DB.SubmitChanges();
                foreach (DataRow d in dt_Multi_CC.Rows)
                {
                    if (dt_Multi_CC.Rows[0][0] == d[0])
                    {
                        jornal_Detail_5.CostCenter = Convert.ToInt32(d[0]);
                        pr.CostCenterId = Convert.ToInt32(d[0]);
                    }
                    else
                    {
                        Acc_Journal_CostCenter Jcc = new Acc_Journal_CostCenter();
                        Jcc.CostCenter_Id = Convert.ToInt32(d[0]);
                        Jcc.Journal_Detail_Id = jornal_Detail_5.JDetailId;
                        Jcc.Amount = jornal_Detail_5.Debit;
                        Jcc.Percentage = 100;
                        Jcc.SourceId = pr.SL_ReturnId;
                        Jcc.ProcessId = (int)Process.SellReturn;
                        DB.Acc_Journal_CostCenters.InsertOnSubmit(Jcc);
                    }
                }
                /* الى حساب الخصم المكتسب*/
                DAL.ACC_JournalDetail jornal_Detail_6 = new DAL.ACC_JournalDetail();
                jornal_Detail_6.JournalId = jornal.JournalId;
                jornal_Detail_6.AccountId = Shared.ItemsPostingAvailable ? Shared.st_Store.SalesDiscountAcc.Value : stores_table.Where(x => x.StoreId == Convert.ToInt32(lkpStore.EditValue)).Select(x => x.PurchaseDiscountAcc.Value).First();//  28 حساب الخصم النقدي المكتسب
                //jornal_Detail_6.CostCenter = costCenter;
                jornal_Detail_6.Credit = pr.DiscountValue;
                jornal_Detail_6.Debit = 0;
                jornal_Detail_6.Notes = note + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Discount : ResSLAr.txt_Discount);
                jornal_Detail_6.CrncId = pr.CrncId;
                jornal_Detail_6.CrncRate = pr.CrncRate;
                //jornal_Detail_6.CostCenter = costCenter;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_6);

                DB.SubmitChanges();
                foreach (DataRow d in dt_Multi_CC.Rows)
                {
                    if (dt_Multi_CC.Rows[0][0] == d[0])
                    {
                        jornal_Detail_6.CostCenter = Convert.ToInt32(d[0]);
                        pr.CostCenterId = Convert.ToInt32(d[0]);
                    }
                    else
                    {
                        Acc_Journal_CostCenter Jcc = new Acc_Journal_CostCenter();
                        Jcc.CostCenter_Id = Convert.ToInt32(d[0]);
                        Jcc.Journal_Detail_Id = jornal_Detail_6.JDetailId;
                        Jcc.Amount = jornal_Detail_6.Credit;
                        Jcc.Percentage = 100;
                        Jcc.SourceId = pr.SL_ReturnId;
                        Jcc.ProcessId = (int)Process.SellReturn;
                        DB.Acc_Journal_CostCenters.InsertOnSubmit(Jcc);
                    }
                }
            }
            #endregion            

            #region CostOfSoldGoods
            if (Shared.ItemsPostingAvailable == false && Shared.StockIsPeriodic == false)
            {
                CostOfSoldGoods = CostOfSoldGoods / pr.CrncRate;            //Convert to Journal Currency
                DAL.ACC_JournalDetail jdCost1 = new DAL.ACC_JournalDetail();
                jdCost1.JournalId = jornal.JournalId;
                if (Shared.InvoicePostToStore)
                    jdCost1.AccountId = stores_table.Where(x => x.StoreId == Convert.ToInt32(lkpStore.EditValue)).Select(x => x.PurchaseAccount).First();//حساب تكلفة البضاعة المباعة
                else
                    jdCost1.AccountId = Shared.st_Store.intermediateInventoryAcc.Value;
                //jdCost1.CostCenter = costCenter;
                jdCost1.Credit = 0;
                jdCost1.Debit = CostOfSoldGoods;
                jdCost1.Notes = note;// +"\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Discount : ResSLAr.txt_Discount);
                jdCost1.CrncId = pr.CrncId;
                jdCost1.CrncRate = pr.CrncRate;
                DB.ACC_JournalDetails.InsertOnSubmit(jdCost1);

                DB.SubmitChanges();
                foreach (DataRow d in dt_Multi_CC.Rows)
                {
                    if (dt_Multi_CC.Rows[0][0] == d[0])
                    {
                        jdCost1.CostCenter = Convert.ToInt32(d[0]);
                        pr.CostCenterId = Convert.ToInt32(d[0]);
                    }
                    else
                    {
                        Acc_Journal_CostCenter Jcc = new Acc_Journal_CostCenter();
                        Jcc.CostCenter_Id = Convert.ToInt32(d[0]);
                        Jcc.Journal_Detail_Id = jdCost1.JDetailId;
                        Jcc.Amount = jdCost1.Debit;
                        Jcc.Percentage = 100;
                        Jcc.SourceId = pr.SL_ReturnId;
                        Jcc.ProcessId = (int)Process.SellReturn;
                        DB.Acc_Journal_CostCenters.InsertOnSubmit(Jcc);
                    }
                }

                DAL.ACC_JournalDetail jdCost2 = new DAL.ACC_JournalDetail();
                jdCost2.JournalId = jornal.JournalId;
                jdCost2.AccountId = stores_table.Where(x => x.StoreId == Convert.ToInt32(lkpStore.EditValue)).Select(x => x.CostOfSoldGoodsAcc.Value).First();//المخزون
                //jdCost2.CostCenter = costCenter;
                jdCost2.Credit = CostOfSoldGoods;
                jdCost2.Debit = 0;
                jdCost2.Notes = note;// +"\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Discount : ResSLAr.txt_Discount);
                jdCost2.CrncId = pr.CrncId;
                jdCost2.CrncRate = pr.CrncRate;
                //jdCost2.CostCenter = costCenter;
                DB.ACC_JournalDetails.InsertOnSubmit(jdCost2);

                DB.SubmitChanges();
                foreach (DataRow d in dt_Multi_CC.Rows)
                {
                    if (dt_Multi_CC.Rows[0][0] == d[0])
                    {
                        jdCost2.CostCenter = Convert.ToInt32(d[0]);
                        pr.CostCenterId = Convert.ToInt32(d[0]);
                    }
                    else
                    {
                        Acc_Journal_CostCenter Jcc = new Acc_Journal_CostCenter();
                        Jcc.CostCenter_Id = Convert.ToInt32(d[0]);
                        Jcc.Journal_Detail_Id = jdCost2.JDetailId;
                        Jcc.Amount = jdCost2.Credit;
                        Jcc.Percentage = 100;
                        Jcc.SourceId = pr.SL_ReturnId;
                        Jcc.ProcessId = (int)Process.SellReturn;
                        DB.Acc_Journal_CostCenters.InsertOnSubmit(Jcc);
                    }
                }
            }
            #endregion

            #region Paid
            if (pr.Paid > 0 || (pr.PayAccountId2.HasValue && pr.PayAcc2_Paid.HasValue && pr.PayAcc2_Paid.Value > 0))
            {
                /*قيد السداد*/
                /* من حساب العميل*/
                DAL.ACC_JournalDetail jornal_Detail_6 = new DAL.ACC_JournalDetail();
                jornal_Detail_6.JournalId = jornal.JournalId;
                jornal_Detail_6.AccountId = CustomerAccountId;//حساب عميل  
                //jornal_Detail_6.CostCenter = costCenter;
                jornal_Detail_6.Credit = 0;
                jornal_Detail_6.Debit = pr.Paid + (pr.PayAcc2_Paid.HasValue ? pr.PayAcc2_Paid.Value : 0);
                jornal_Detail_6.Notes = note + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Paid : ResSLAr.txt_Paid);
                jornal_Detail_6.CrncId = pr.CrncId;
                jornal_Detail_6.CrncRate = pr.CrncRate;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_6);

                DB.SubmitChanges();
                foreach (DataRow d in dt_Multi_CC.Rows)
                {
                    if (dt_Multi_CC.Rows[0][0] == d[0])
                    {
                        jornal_Detail_6.CostCenter = Convert.ToInt32(d[0]);
                        pr.CostCenterId = Convert.ToInt32(d[0]);
                    }
                    else
                    {
                        Acc_Journal_CostCenter Jcc = new Acc_Journal_CostCenter();
                        Jcc.CostCenter_Id = Convert.ToInt32(d[0]);
                        Jcc.Journal_Detail_Id = jornal_Detail_6.JDetailId;
                        Jcc.Amount = jornal_Detail_6.Debit;
                        Jcc.Percentage = 100;
                        Jcc.SourceId = pr.SL_ReturnId;
                        Jcc.ProcessId = (int)Process.SellReturn;
                        DB.Acc_Journal_CostCenters.InsertOnSubmit(Jcc);
                    }
                }

                /* الى حساب الخزينة*/
                if (pr.Paid > 0)
                {
                    DAL.ACC_JournalDetail jornal_Detail_4 = new DAL.ACC_JournalDetail();
                    jornal_Detail_4.JournalId = jornal.JournalId;
                    jornal_Detail_4.AccountId = Convert.ToInt32(lkp_Drawers.EditValue);          // حساب الخزينة
                    //jornal_Detail_4.CostCenter = costCenter;
                    jornal_Detail_4.Credit = pr.Paid;
                    jornal_Detail_4.Debit = 0;
                    jornal_Detail_4.Notes = note + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Paid : ResSLAr.txt_Paid);
                    jornal_Detail_4.CrncId = pr.CrncId;
                    jornal_Detail_4.CrncRate = pr.CrncRate;
                    DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_4);

                    DB.SubmitChanges();
                    foreach (DataRow d in dt_Multi_CC.Rows)
                    {
                        if (dt_Multi_CC.Rows[0][0] == d[0])
                        {
                            jornal_Detail_4.CostCenter = Convert.ToInt32(d[0]);
                            pr.CostCenterId = Convert.ToInt32(d[0]);
                        }
                        else
                        {
                            Acc_Journal_CostCenter Jcc = new Acc_Journal_CostCenter();
                            Jcc.CostCenter_Id = Convert.ToInt32(d[0]);
                            Jcc.Journal_Detail_Id = jornal_Detail_4.JDetailId;
                            Jcc.Amount = jornal_Detail_4.Credit;
                            Jcc.Percentage = 100;
                            Jcc.SourceId = pr.SL_ReturnId;
                            Jcc.ProcessId = (int)Process.SellReturn;
                            DB.Acc_Journal_CostCenters.InsertOnSubmit(Jcc);
                        }
                    }
                }
                /* الى حساب الخزينة2*/
                if (pr.PayAccountId2 != null && pr.PayAcc2_Paid.HasValue && pr.PayAcc2_Paid.Value > 0)
                {
                    DAL.ACC_JournalDetail jornal_Detail_5 = new DAL.ACC_JournalDetail();
                    jornal_Detail_5.JournalId = jornal.JournalId;
                    //jornal_Detail_5.CostCenter = costCenter;
                    jornal_Detail_5.AccountId = pr.PayAccountId2.Value;          // حساب الخزينة
                    jornal_Detail_5.Credit = pr.PayAcc2_Paid.Value;
                    jornal_Detail_5.Debit = 0;
                    jornal_Detail_5.Notes = note + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Paid : ResSLAr.txt_Paid);
                    jornal_Detail_5.CrncId = pr.CrncId;
                    jornal_Detail_5.CrncRate = pr.CrncRate;
                    DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_5);

                    DB.SubmitChanges();
                    foreach (DataRow d in dt_Multi_CC.Rows)
                    {
                        if (dt_Multi_CC.Rows[0][0] == d[0])
                        {
                            jornal_Detail_5.CostCenter = Convert.ToInt32(d[0]);
                            pr.CostCenterId = Convert.ToInt32(d[0]);
                        }
                        else
                        {
                            Acc_Journal_CostCenter Jcc = new Acc_Journal_CostCenter();
                            Jcc.CostCenter_Id = Convert.ToInt32(d[0]);
                            Jcc.Journal_Detail_Id = jornal_Detail_5.JDetailId;
                            Jcc.Amount = jornal_Detail_5.Debit;
                            Jcc.Percentage = 100;
                            Jcc.SourceId = pr.SL_ReturnId;
                            Jcc.ProcessId = (int)Process.SellReturn;
                            DB.Acc_Journal_CostCenters.InsertOnSubmit(Jcc);
                        }
                    }
                }
            }
            #endregion

            DB.SubmitChanges();
        }


        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.SL_Customer).Count() < 1)
                {
                    btnAddCustomer.Enabled = false;
                }
                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.Item).Count() < 1)
                    mi_frm_IC_Item.Enabled = false;

                prvlg = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.SL_Return).FirstOrDefault();

                if (!prvlg.CanDel)
                    barBtnCancel.Enabled = false;
                if (!prvlg.CanAdd)
                    barBtnNew.Enabled = false;
                if (!prvlg.CanPrint)
                    barBtnPrint.Enabled = false;

                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.SL_Invoice).Count() < 1)
                    barbtnLoadSellInvoice.Enabled = false;

                //UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.AccStatement).FirstOrDefault();
                //if (p == null)
                //    page_AccInfo.PageVisible = false;
            }
        }

        void DoValidate()
        {
            txt_paid.DoValidate();
            txt_Remains.DoValidate();
            txtDiscountRatio.DoValidate();
            txtDiscountValue.DoValidate();
            txtExpenses.DoValidate();
            txtInvoiceCode.DoValidate();

            txtNotes.DoValidate();
            lkp_Drawers.DoValidate();
            lkpStore.DoValidate();
            lkp_Customers.DoValidate();

            dtInvoiceDate.DoValidate();
            dtSLReturn_Details.AcceptChanges();
        }

        DialogResult ChangesMade()
        {
            if (
                DataModified ||
                dtSLReturn_Details.GetChanges(DataRowState.Added) != null ||
                dtSLReturn_Details.GetChanges(DataRowState.Modified) != null ||
                dtSLReturn_Details.GetChanges(DataRowState.Deleted) != null
                )
            {
                DialogResult r = XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgDataModified : ResSLAr.MsgDataModified, "", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                if (r == DialogResult.Yes)
                {
                    if (!ValidData())
                        return DialogResult.Cancel;

                    barBtnSave.PerformClick();
                    return r;
                }
                else if (r == DialogResult.No)
                {
                    return r;
                }
                else if (r == DialogResult.Cancel)
                {
                    //cancel form action
                    return r;
                }
            }
            return DialogResult.No;
        }

        private bool ValidData()
        {
            DB = new ERPDataContext();

            //can't post in closed period
            if (ErpHelper.CanSaveInClsedPeriod(dtInvoiceDate.DateTime.Date, Shared.st_Store.ClosePeriodDate, Shared.user.EditInClosedPeriod) == false)
                return false;

            ((GridView)grdPrInvoice.FocusedView).FocusedRowHandle += 1;
            if (invoiceId == 0)
            {
                if (prvlg != null && !prvlg.CanAdd)
                {
                    // "عفوا, انت لا تمتلك صلاحية انشاء بيان جديد"
                    XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgPrvNew : ResSLAr.MsgPrvNew, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            if (invoiceId > 0)
            {
                if (prvlg != null && !prvlg.CanEdit)
                {
                    //"عفوا, انت لا تمتلك صلاحية تعديل هذا البيان"
                    XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgPrvEdit : ResSLAr.MsgPrvEdit, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
                if (ErpHelper.CanEditPostedBill(invoiceId, (int)Process.SellReturn,
                    Shared.OfflinePostToGL, Shared.user.UserEditPostedBills) == false)
                    return false;
            }

            if (lkp_Customers.EditValue == null)
            {
                XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.MsgSelectCustomer : ResAr.MsgSelectCustomer,
                        Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                lkp_Customers.Focus();

                return false;
            }

            if (Convert.ToDecimal(txt_TaxValue.EditValue) > 0 && Shared.st_Store.TaxAcc.HasValue == false)
            {
                //يجب تحديد حساب الضرائب
                //check sales tax Account

                XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.MsgSalesTaxAcc : ResAr.MsgSalesTaxAcc,
                        Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);

                return false;
            }
            if (Convert.ToDecimal(txt_CusTaxV.EditValue) > 0 && Shared.st_Store.CustomTaxAcc.HasValue == false)
            {
                //يجب تحديد حساب الجدول
                //check Custom tax Account

                XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.MsgCusTaxAcc : ResAr.MsgCusTaxAcc,
                        Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);

                return false;
            }
            if (Shared.StockIsPeriodic && Convert.ToDecimal(txtDiscountValue.EditValue) > 0 && Shared.st_Store.PurchaseDiscountAcc.HasValue == false)
            {
                //check sales discount Account

                XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.MsgDiscountAcc : ResAr.MsgDiscountAcc,
                        Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);

                return false;
            }

            if (string.IsNullOrEmpty(txtInvoiceCode.Text.Trim()))
            {
                XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.txtValidateInvNumber : ResSLAr.txtValidateInvNumber, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                txtInvoiceCode.Focus();
                return false;
            }

            if (Shared.st_Store.InvoicesCodeRedundancy == false)
            {
                bool code_exist = InvCodeExist();

                if (invoiceId == 0 && code_exist && Shared.st_Store.GenerateNewInvCodeOnSave == true)
                {
                    txtInvoiceCode.Focus();
                    txtInvoiceCode.ErrorText = Shared.IsEnglish ? "Invoice code Exists" : "الكود موجود مسبقاً";
                    lkpStore_EditValueChanged(lkpStore, EventArgs.Empty);
                    return false;
                }
                if (ErpHelper.ValidateInvCodeExist(code_exist, txtInvoiceCode) == false)
                    return false;
            }

            if (dtSLReturn_Details.Rows.Count <= 0)
            {
                //يجب تسجيل صنف علي الاقل في الفاتوره
                XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.txtValidateNoRows : ResSLAr.txtValidateNoRows, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                grdPrInvoice.Focus();
                return false;
            }

            if (dtSLReturn_Details.Rows[dtSLReturn_Details.Rows.Count - 1].RowState != DataRowState.Deleted
                && dtSLReturn_Details.Rows[dtSLReturn_Details.Rows.Count - 1]["ItemId"] == DBNull.Value)
            {
                dtSLReturn_Details.Rows[dtSLReturn_Details.Rows.Count - 1].Delete();
                grdPrInvoice.RefreshDataSource();
            }

            if (Shared.LibraAvailabe)
            {
                var comply = from d in dtSLReturn_Details.Select()
                             where (d["VariableWeight"].ToString() == true.ToString() ||
                             d["PricingWithSmall"].ToString() == true.ToString() ||
                             d["IsOffer"].ToString() == true.ToString() ||
                             d["Is_Libra"].ToString() == true.ToString())
                            && d["kg_Weight_libra"].ToString() == ""
                             select d;
                if (comply.FirstOrDefault() != null)
                {
                    MessageBox.Show(Shared.IsEnglish ? "Make sure to enter the Weight (kg) for required items" :
                        "تأكد من إدخال الوزن بالكيلو لكل الأصناف اللازمة", Shared.IsEnglish ? "Warning" : "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return false;
                }
            }

            //if (Shared.InvoicePostToStore == false && Shared.st_Store.intermediateInventoryAcc.HasValue == false)
            //{
            //    MessageBox.Show(Shared.IsEnglish ? ResAccEn.valIntrmdtStorActId : ResAccAr.valIntrmdtStorActId);
            //    return false;
            //}

            return true;
        }

        private void lkpStore_EditValueChanged(object sender, EventArgs e)
        {
                #region GetNextInvNumber
                var lastNumber = (from x in DB.SL_Returns
                                  join s in DB.IC_Stores on x.StoreId equals s.StoreId
                                  where Shared.st_Store.AutoInvSerialForStore == true ? x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true    //مستوى المخزن
                                  where Shared.st_Store.AutoInvSerialForStore == null ? (int?)lkpStore.GetColumnValue("ParentId") != null ?
                                  s.ParentId.HasValue && s.ParentId.Value == (int?)lkpStore.GetColumnValue("ParentId") : x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true//مستوى الفرع
                                  where x.InvoiceBookId == null
                                  orderby x.ReturnDate descending
                                  orderby x.SL_ReturnId descending
                                  select x.ReturnCode).FirstOrDefault();
                txtInvoiceCode.Text = MyHelper.GetNextNumberInString(lastNumber);
                #endregion
            
            if (lkpStore.EditValue != null)
            {
                //lkpCostCenter.EditValue = stores_table.Where(x => x.StoreId == Convert.ToInt32(lkpStore.EditValue)).Select(x => x.CostCenter).First();
                var StoreCC = stores_table.Where(x => x.StoreId == Convert.ToInt32(lkpStore.EditValue)).Select(x => x.CostCenter).FirstOrDefault();
                if (StoreCC != null)
                {
                    if (invoiceId <= 0)
                    {
                        dt_Multi_CC.Rows.Clear();
                        dt_Multi_CC.Rows.Add(StoreCC);
                    }
                }
                else if (invoiceId <= 0)
                {
                    dt_Multi_CC.Rows.Clear();
                }
            }
        }
        private void GetNextCode()
        {
                #region GetNextInvNumber
                var lastNumber = (from x in DB.SL_Returns
                                  join s in DB.IC_Stores on x.StoreId equals s.StoreId
                                  where Shared.st_Store.AutoInvSerialForStore == true ? x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true    //مستوى المخزن
                                  where Shared.st_Store.AutoInvSerialForStore == null ? (int?)lkpStore.GetColumnValue("ParentId") != null ?
                                  s.ParentId.HasValue && s.ParentId.Value == (int?)lkpStore.GetColumnValue("ParentId") : x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true//مستوى الفرع
                                  where x.InvoiceBookId == null
                                  orderby x.ReturnDate descending
                                  orderby x.SL_ReturnId descending
                                  select x.ReturnCode).FirstOrDefault();
                txtInvoiceCode.Text = MyHelper.GetNextNumberInString(lastNumber);
                #endregion

        }
        private void contextMenuStrip1_Opened(object sender, EventArgs e)
        {
            var view = grdPrInvoice.FocusedView as GridView;
            var item_id = view.GetFocusedRowCellValue("ItemId");
            if (item_id == null || item_id == DBNull.Value || Convert.ToInt32(item_id) <= 0)
                mi_frm_IC_Item.Enabled = false;
            else
            {
                if (Shared.LstUserPrvlg == null || Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.Item).Count() > 0)
                    mi_frm_IC_Item.Enabled = true;
            }
        }

        private void mi_frm_IC_Item_Click(object sender, EventArgs e)
        {
            var view = grdPrInvoice.FocusedView as GridView;
            var item_id = view.GetFocusedRowCellValue("ItemId");
            if (item_id == null || item_id == DBNull.Value || Convert.ToInt32(item_id) <= 0)
                return;


            new frm_IC_Item(Convert.ToInt32(item_id), FormAction.Edit).ShowDialog();
        }

        private void frm_SL_Return_Shown(object sender, EventArgs e)
        {
            txtNotes.Focus();
            FocusItemCode1(Shared.user.FocusGridInInvoices);
        }

        private void barBtnHelp_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "فاتورة مردود مبيعات جديدة");
        }

        bool cust_IsDebit = false;
        private void lkp_Customers_EditValueChanged(object sender, EventArgs e)
        { 
        }

        private void gridView2_ShowingEditor(object sender, CancelEventArgs e)
        {
            try
            {
                #region Expire
                if (gridView2.FocusedColumn == col_Expire
                && gridView2.GetFocusedRowCellValue("IsExpire") != null
                && gridView2.GetFocusedRowCellValue("IsExpire") != DBNull.Value)
                {
                    bool IsExpire = Convert.ToBoolean(gridView2.GetFocusedRowCellValue("IsExpire"));
                    e.Cancel = !IsExpire;
                }
                #endregion
            }
            catch { }
        }

        private void rep_expireDate_CustomDisplayText(object sender, DevExpress.XtraEditors.Controls.CustomDisplayTextEventArgs e)
        {
            #region Expire
            if (e.Value == null || e.Value == DBNull.Value)
                return;
            try
            {
                DateTime date = Convert.ToDateTime(e.Value);
                e.DisplayText = date.Month + "-" + date.Year;
            }
            catch
            { }

            #endregion
        }

        private void Load_SellInvoiceData(int sellInvoiceId)
        {

            dtSLReturn_Details.Rows.Clear();

            var details = (from d in DB.SL_InvoiceDetails
                           where d.SL_InvoiceId == sellInvoiceId
                           join i in DB.IC_Items on d.ItemId equals i.ItemId
                           select new { detail = d, item = i }).ToList();

            lkp_Customers.EditValue = details.FirstOrDefault().detail.SL_Invoice.CustomerId;
            lkpStore.EditValue = details.FirstOrDefault().detail.SL_Invoice.StoreId;


            //if (details.FirstOrDefault().detail.SL_Invoice.CostCenterId != null)
            //{
            //    //lkpCostCenter.EditValue = inv.CostCenterId;
            //    int rowHandle = gv_CostCenter.LocateByValue("CostCenterId", details.FirstOrDefault().detail.SL_Invoice.CostCenterId);
            //    gv_CostCenter.SelectRow(rowHandle);
            //}
            //var multiCC = DB.Acc_Journal_CostCenters.Where(x => x.ProcessId == (int)Process.SellInvoice && x.SourceId == details.FirstOrDefault().detail.SL_InvoiceId).Select(x => x.CostCenter_Id).Distinct();
            //if (multiCC.Count() > 0)
            //{
            //    foreach (var c in multiCC)
            //    {
            //        //lkpCostCenter.EditValue = c;
            //        int rowHandle = gv_CostCenter.LocateByValue("CostCenterId", Convert.ToInt32(c));
            //        gv_CostCenter.SelectRow(rowHandle);
            //    }
            //}

            dt_Multi_CC.Rows.Clear();
            if (details.FirstOrDefault().detail.SL_Invoice.CostCenterId != null)
            {
                dt_Multi_CC.Rows.Add(details.FirstOrDefault().detail.SL_Invoice.CostCenterId);
            }
            //MyHelper.FillMultiCC(ref dt_Multi_CC, sellInvoiceId, (int)Process.SellInvoice);


            foreach (var d in details)
            {
                DataRow row = dtSLReturn_Details.NewRow();
                row["ItemId"] = d.detail.ItemId;
                row["ItemIdF"] = Shared.IsEnglish ? d.item.ItemNameAr : d.item.ItemNameEn;
                row["CategoryId"] = d.item.Category;
                row["ItemCode1"] = d.item.ItemCode1;
                row["ItemCode2"] = d.item.ItemCode2;
                row["UOM"] = d.detail.UOMId;
                row["Qty"] = decimal.ToDouble(d.detail.Qty);

                if (d.detail.Height != null)
                    row["Height"] = decimal.ToDouble(d.detail.Height.Value);
                if (d.detail.Length != null)
                    row["Length"] = decimal.ToDouble(d.detail.Length.Value);
                if (d.detail.Width != null)
                    row["Width"] = decimal.ToDouble(d.detail.Width.Value);
                row["PiecesCount"] = decimal.ToDouble(d.detail.PiecesCount);

                if (d.detail.UOMIndex == 0)
                    row["PurchasePrice"] = decimal.ToDouble(d.item.PurchasePrice);
                else if (d.detail.UOMIndex == 1)
                    row["PurchasePrice"] = decimal.ToDouble(d.item.PurchasePrice / MyHelper.FractionToDouble(d.item.MediumUOMFactor));
                else if (d.detail.UOMIndex == 2)
                    row["PurchasePrice"] = decimal.ToDouble(d.item.PurchasePrice / MyHelper.FractionToDouble(d.item.LargeUOMFactor));

                row["SellPrice"] = decimal.ToDouble(d.detail.SellPrice);
                row["SalesTaxRatio"] = d.detail.SalesTaxRatio;
                row["SalesTax"] = decimal.ToDouble(d.detail.SalesTax);
                row["CustomTax"] = decimal.ToDouble(d.detail.CustomTax);
                row["CustomTaxRatio"] = decimal.ToDouble(d.detail.CustomTaxRatio);
                row["calcTaxBeforeDisc"] = d.item.calcTaxBeforeDisc;


                row["DiscountValue"] = decimal.ToDouble(d.detail.DiscountValue);
                row["DiscountRatio"] = decimal.ToDouble(d.detail.DiscountRatio);
                row["DiscountRatio2"] = decimal.ToDouble(d.detail.DiscountRatio2);
                row["DiscountRatio3"] = decimal.ToDouble(d.detail.DiscountRatio3);

                //row["CompanyNameAr"] = d.item.IC_Company.CompanyNameAr;
                row["MediumUOMFactor"] = MyHelper.FractionToDouble(d.item.MediumUOMFactor);
                row["LargeUOMFactor"] = MyHelper.FractionToDouble(d.item.LargeUOMFactor);

                #region Expire
                if (d.detail.Expire.HasValue)
                    row["Expire"] = d.detail.Expire;
                row["Batch"] = d.detail.Batch;
                row["Serial"] = d.detail.Serial;
                row["Serial2"] = d.detail.Serial2;

                row["IsExpire"] = d.item.IsExpire;
                #endregion

                //get store qty                                
                //decimal currentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, d.detail.ItemId, d.detail.SL_Invoice.StoreId);
                //currentQty = MyHelper.getCalculatedUomQty(currentQty, d.detail.UOMIndex, MyHelper.FractionToDouble(d.item.MediumUOMFactor), MyHelper.FractionToDouble(d.item.LargeUOMFactor));
                //row["CurrentQty"] = decimal.ToDouble(currentQty);

                row["TotalSellPrice"] = decimal.ToDouble(d.detail.TotalSellPrice);
                row["UomIndex"] = d.detail.UOMIndex;
                row["ItemType"] = d.item.ItemType;

                row["ItemDescription"] = d.detail.ItemDescription;
                row["ItemDescriptionEn"] = d.detail.ItemDescriptionEn;

                row["ParentItemId"] = d.item.mtrxParentItem;
                row["M1"] = d.item.mtrxAttribute1;
                row["M2"] = d.item.mtrxAttribute2;
                row["M3"] = d.item.mtrxAttribute3;
                row["RowHandle"] = ++rowhandle;
                row["Pack"] = d.detail.Pack;
                row["bonusDiscount"] = d.detail.bonusDiscount !=null? Convert.ToDouble(d.detail.bonusDiscount):0;
                //if (Shared.E_invoiceAvailable)
                //    getETaxDetailSl_InvoiceForErp(d.detail.SL_InvoiceDetailId, row);
                //else
                //    getETaxE_InvoiceForSL_Invoice(d.detail, d.detail.SL_InvoiceDetailId, row);
                //   getETaxInvoice(d.detail.SL_InvoiceDetailId, row);
                if (Shared.E_invoiceAvailable || Shared.st_Store.E_AllowMoreThanTax == true)
                {
                    var SLSubTaxes = DB.SL_InvoiceDetailSubTaxValues.Where(a => a.InvoiceDetailId == d.detail.SL_InvoiceDetailId).ToList();
                    List<IC_ItemSubTax> SubTaxes = SLSubTaxes.Select(x => new IC_ItemSubTax
                    {
                        ItemId = d.item.ItemId,
                        Rate = x.TaxRatio,
                        SubTaxId = x.esubTypeId
                    }).ToList();
                    setETaxE_Invoice(SubTaxes, row, d.item);
                }
                dtSLReturn_Details.Rows.Add(row);
                dtSLReturn_Details.AcceptChanges();
                GridView view = grdPrInvoice.FocusedView as GridView;
                view.UpdateCurrentRow();
                calcSubTaxes(row, view);
            }
            txtDiscountRatio.EditValue = Convert.ToDouble(details.FirstOrDefault().detail.SL_Invoice.DiscountRatio * 100);
            txtDiscountValue.EditValue = Convert.ToDouble(details.FirstOrDefault().detail.SL_Invoice.DiscountValue);

            txt_TaxValue.EditValue = Convert.ToDouble(details.FirstOrDefault().detail.SL_Invoice.TaxValue);
            Get_TotalAccount();
        }

        private void barbtnLoadSellInvoice_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            new frm_SL_InvoiceList(true).ShowDialog();
             SelectedInvId = frm_SL_InvoiceList.SelectedInvId;
            if (SelectedInvId > 0)
            {
                Reset();
                Load_SellInvoiceData(SelectedInvId);
                frm_SL_InvoiceList.SelectedInvId = 0;
            }
        }

        private void lkp_Drawers2_EditValueChanged(object sender, EventArgs e)
        {
            if (lkp_Drawers2.EditValue != null && Convert.ToInt32(lkp_Drawers2.EditValue) == Convert.ToInt32(lkp_Drawers.EditValue))
                lkp_Drawers2.EditValue = null;

            if (lkp_Drawers2.EditValue == null)
            {
                txt_PayAcc2_Paid.EditValue = 0;
                txt_PayAcc2_Paid.Enabled = false;
            }
            else
            {
                txt_PayAcc2_Paid.Enabled = true;
            }
        }

        private void btn_AddMatrixItems_Click(object sender, EventArgs e)
        {

            new frm_IC_MatrixAddInv().ShowDialog();

            foreach (var d in frm_IC_MatrixAddInv.lst_InvMatrixItems)
            {
                var item = DB.IC_Items.Where(x => x.ItemId == d.ItemId).FirstOrDefault();
                DataRow row = dtSLReturn_Details.NewRow();
                row["ItemId"] = item.ItemId;
                row["ItemCode1"] = item.ItemCode1;
                row["ItemCode2"] = item.ItemCode2;
                row["ItemType"] = item.ItemType;
                row["PurchasePrice"] = Decimal.ToDouble(item.PurchasePrice);
                row["SellPrice"] = Decimal.ToDouble(item.SmallUOMPrice);
                row["DiscountRatio"] = "0";
                row["DiscountValue"] = "0";

                if (IsTaxable == null && Convert.ToBoolean(ErpUtils.GetGridLookUpValue(lkp_Customers, lkp_Customers.EditValue, "IsTaxable")))
                { row["SalesTaxRatio"] = item.SalesTaxRatio / 100; row["CustomTaxRatio"] = item.CustomSalesTaxRatio / 100; }
                else if (IsTaxable == true)
                { row["SalesTaxRatio"] = item.SalesTaxRatio / 100; row["CustomTaxRatio"] = item.CustomSalesTaxRatio / 100; }
                else
                { row["SalesTaxRatio"] = 0; row["CustomTaxRatio"] = 0; }

                row["calcTaxBeforeDisc"] = item.calcTaxBeforeDisc;

                row["Qty"] = decimal.ToDouble(d.Qty);

                decimal salestaxratio = Convert.ToDecimal(row["SalesTaxRatio"]);
                decimal CustomTaxRatio = Convert.ToDecimal(row["CustomTaxRatio"]);
                decimal DiscR1 = Convert.ToDecimal(row["DiscountRatio"]);
                decimal DiscR2 = Convert.ToDecimal(row["DiscountRatio2"]);
                decimal DiscR3 = Convert.ToDecimal(row["DiscountRatio3"]);
                decimal TotalSellPrice = d.Qty * Convert.ToDecimal(row["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);

                if (Shared.st_Store.PriceIncludeSalesTax)    /*السعر شامل الضريبة*/
                {
                    decimal temp = item.calcTaxBeforeDisc ? (salestaxratio * d.Qty * Convert.ToDecimal(row["SellPrice"])) / (1 + salestaxratio) :
                        (salestaxratio * TotalSellPrice) / (1 + salestaxratio);
                    row["SalesTax"] = decimal.ToDouble(temp);
                    row["TotalSellPrice"] = decimal.ToDouble(TotalSellPrice - temp);// السعر الاجمالي شامل الضريبة                            

                    var totalsellp = (TotalSellPrice - temp);
                    //custom
                    decimal temp2 = item.calcTaxBeforeDisc ? (CustomTaxRatio * d.Qty * Convert.ToDecimal(row["SellPrice"])) / (1 + CustomTaxRatio) :
                       (CustomTaxRatio * totalsellp) / (1 + CustomTaxRatio);
                    row["CustomTax"] = decimal.ToDouble(temp2);
                    row["TotalSellPrice"] = decimal.ToDouble(totalsellp - temp2);

                }
                else
                {
                    decimal temp = item.calcTaxBeforeDisc ? (salestaxratio * d.Qty * Convert.ToDecimal(row["SellPrice"])) : (salestaxratio * TotalSellPrice);
                    row["SalesTax"] = decimal.ToDouble(temp);
                    row["TotalSellPrice"] = decimal.ToDouble(TotalSellPrice);//ضيف الضريبة على السعر الاجمالي                            


                    var totalsellp = (TotalSellPrice);
                    //custom tax
                    decimal temp2 = item.calcTaxBeforeDisc ? (CustomTaxRatio * d.Qty * Convert.ToDecimal(row["SellPrice"])) : (CustomTaxRatio * totalsellp);
                    row["CustomTax"] = decimal.ToDouble(temp2);
                    row["TotalSellPrice"] = decimal.ToDouble(totalsellp);
                }

                row["MediumUOMFactor"] = MyHelper.FractionToDouble(item.MediumUOMFactor);
                row["LargeUOMFactor"] = MyHelper.FractionToDouble(item.LargeUOMFactor);

                MyHelper.GetUOMs(item, dtUOM, uom_list);
                row["UOM"] = dtUOM.Rows[0]["UomId"];
                row["UomIndex"] = "0";
                row["IsExpire"] = item.IsExpire;
                //var compName = DB.IC_Companies.Where(c => c.CompanyId == item.Company).Select(c => c.CompanyNameAr).Single();
                //row["CompanyNameAr"] = compName;

                //if (Shared.user.Sell_ShowCrntQty == true)
                //{
                //    decimal currentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, d.ItemId, Convert.ToInt32(lkpStore.EditValue));
                //    row["CurrentQty"] = decimal.ToDouble(currentQty);
                //}
                //else
                {
                    row["CurrentQty"] = decimal.ToDouble(0);
                }

                row["ItemDescription"] = item.Description;
                row["ItemDescriptionEn"] = item.DescriptionEn;

                row["ParentItemId"] = item.mtrxParentItem;
                row["M1"] = item.mtrxAttribute1;
                row["M2"] = item.mtrxAttribute2;
                row["M3"] = item.mtrxAttribute3;

                row["Length"] = Decimal.ToDouble(item.Length);
                row["Width"] = Decimal.ToDouble(item.Width);
                row["Height"] = Decimal.ToDouble(item.Height);
                row["RowHandle"] = ++rowhandle;
                dtSLReturn_Details.Rows.Add(row);
            }
        }

        private void gridView2_CustomUnboundColumnData(object sender, CustomColumnDataEventArgs e)
        {
            var view = ((sender) as GridView);
            decimal totalqty = 0;
            try
            {
                if (e.Column == col_TotalQty
    && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "ItemType") != null && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "ItemType") != DBNull.Value
    && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Qty") != null && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Qty") != DBNull.Value
    && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Length") != null && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Length") != DBNull.Value
    && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Width") != null && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Width") != DBNull.Value
    && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Height") != null && view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Height") != DBNull.Value)
                {
                    if (Convert.ToInt32(view.GetListSourceRowCellValue(e.ListSourceRowIndex, "ItemType")) != (int)ItemType.Subtotal)
                    {
                        totalqty = Convert.ToDecimal(view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Qty"))
                            * Convert.ToDecimal(view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Length"))
                            * Convert.ToDecimal(view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Width"))
                            * Convert.ToDecimal(view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Height"));
                    }
                    else
                    {
                        int i = e.ListSourceRowIndex < 0 ? view.RowCount - 1 : e.ListSourceRowIndex - 1;
                        while (Convert.ToInt32(view.GetListSourceRowCellValue(i, "ItemType")) != (int)ItemType.Subtotal && i >= 0)
                        {
                            if (Shared.st_Store.MultiplyDimensions != (byte)Dimensions.Distinguish)
                                totalqty += Convert.ToDecimal(view.GetListSourceRowCellValue(i, "Length")) *
                                Convert.ToDecimal(view.GetListSourceRowCellValue(i, "Height")) *
                                Convert.ToDecimal(view.GetListSourceRowCellValue(i, "Width")) *
                                Convert.ToDecimal(view.GetListSourceRowCellValue(i, "Qty"));
                            i--;
                        }
                    }
                    e.Value = decimal.ToDouble(totalqty);
                }
            }
            catch { }
        }

        private void Get_SubTotal_RowData(DataRow row, GridView view, int CurrentRowHandle)
        {
            row["Qty"] = 0;
            row["Length"] = 0;
            row["Width"] = 0;
            row["Height"] = 0;
            row["DiscountRatio"] = 0;
            row["UOM"] = 0;
            row["UomIndex"] = 0;
            row["CurrentQty"] = 0;
            row["ItemDescription"] = "";
            row["ItemDescriptionEn"] = "";

            int i = CurrentRowHandle < 0 ? view.RowCount - 1 : CurrentRowHandle - 1;
            decimal TotalSellPrice = 0, TotalDiscountValue = 0, TotalSalesTax = 0, TotalCusTax = 0,
                TotalPiecesCount = 0, TotalQty = 0, SellPrice = 0;

            while (Convert.ToInt32(view.GetRowCellValue(i, "ItemType")) != (int)ItemType.Subtotal && i >= 0)
            {
                TotalQty += Convert.ToDecimal(view.GetRowCellValue(i, "Qty"));
                TotalSellPrice += Convert.ToDecimal(view.GetRowCellValue(i, "TotalSellPrice"));
                TotalDiscountValue += Convert.ToDecimal(view.GetRowCellValue(i, "DiscountValue"));

                decimal totalrowqty = Convert.ToDecimal(view.GetRowCellValue(i, "Qty"));
                if (Shared.st_Store.MultiplyDimensions != (byte)Dimensions.Distinguish)
                    totalrowqty = Convert.ToDecimal(view.GetRowCellValue(i, "Length")) *
                    Convert.ToDecimal(view.GetRowCellValue(i, "Height")) *
                    Convert.ToDecimal(view.GetRowCellValue(i, "Width")) *
                    Convert.ToDecimal(view.GetRowCellValue(i, "Qty"));

                TotalSalesTax += (Convert.ToDecimal(view.GetRowCellValue(i, "SalesTax")));
                TotalCusTax += Convert.ToDecimal(view.GetRowCellValue(i, "CustomTax"));
                SellPrice += (Convert.ToDecimal(view.GetRowCellValue(i, "SellPrice")) * totalrowqty);
                TotalPiecesCount += Convert.ToDecimal(view.GetRowCellValue(i, "PiecesCount"));
                i--;
            }

            row["Qty"] = decimal.ToDouble(TotalQty);
            row["TotalSellPrice"] = decimal.ToDouble(TotalSellPrice);
            row["DiscountValue"] = decimal.ToDouble(TotalDiscountValue);
            row["SalesTax"] = decimal.ToDouble(TotalSalesTax);
            row["CustomTax"] = decimal.ToDouble(TotalCusTax);
            row["SellPrice"] = decimal.ToDouble(SellPrice);
            row["PiecesCount"] = decimal.ToDouble(TotalPiecesCount);

        }
        private void Update_First_SubTotal(GridView view, int CurrentRowHandle)
        {
            if (CurrentRowHandle >= 0)
            {
                for (int i = CurrentRowHandle; i < view.RowCount; i++)
                {
                    if (Convert.ToInt32(view.GetRowCellValue(i, "ItemType")) == (int)ItemType.Subtotal)
                    {
                        Get_SubTotal_RowData(view.GetDataRow(i), view, i);
                        return;
                    }
                }
            }
            else if (Convert.ToInt32(view.GetFocusedRowCellValue("ItemType")) == (int)ItemType.Subtotal)
                Get_SubTotal_RowData(view.GetFocusedDataRow(), view, CurrentRowHandle);
        }

        private void gridView2_RowStyle(object sender, RowStyleEventArgs e)
        {
            if (e.RowHandle >= 0 && Convert.ToInt32(gridView2.GetRowCellValue(e.RowHandle, "ItemType")) == (int)ItemType.Subtotal)
            {
                e.HighPriority = true;
                e.Appearance.BackColor = Shared.user.SubtotalBackcolor == null ? Color.Yellow : Color.FromName(Shared.user.SubtotalBackcolor);
            }
        }

        private void txtDiscountValue_Leave(object sender, EventArgs e)
        {
            Get_TotalAccount();
        }


        private void txtDiscountRatio_EditValueChanged(object sender, EventArgs e)
        {
            Get_TotalAccount();
        }

        private void Get_TotalAccount()
        {
            gridView1.RefreshData();
            try
            {
                decimal total_sell = 0;
                decimal total_salestax = 0, total_Custax = 0;
                decimal net = 0;
                decimal discount_ratio = 0;
                decimal Discount_value = 0;
                decimal total_ETaxValu = 0;
                foreach (DataRow dr in dtSLReturn_Details.Rows)
                {
                    if (dr.RowState == DataRowState.Deleted)
                        continue;
                    if (Convert.ToInt32(dr["ItemType"]) != (int)ItemType.Subtotal)
                    {
                        total_sell += Convert.ToDecimal(dr["TotalSellPrice"]);
                        total_salestax += (Convert.ToDecimal(dr["SalesTax"]));
                        total_Custax += (Convert.ToDecimal(dr["CustomTax"]));
                        if (Shared.st_Store.E_AllowMoreThanTax == false || Shared.st_Store.E_AllowMoreThanTax == null)
                        {

                            if (dr["EtaxValue"] != DBNull.Value && dr["EtaxValue"].ToString() != "0" && dr["TaxType"] != DBNull.Value)
                            {
                                var taxType = Convert.ToInt32(dr["TaxType"]);
                                var taxId = DB.E_TaxableTypes.FirstOrDefault(a => a.E_TaxableTypeId == taxType).ParentTaxId;
                                if (taxId == discountTaxId)
                                    total_ETaxValu -= Convert.ToDecimal(dr["EtaxValue"]);
                                else
                                    total_ETaxValu += Convert.ToDecimal(dr["EtaxValue"]);
                            }
                        }

                        else
                        {
                            //if (dr["PR_InvoiceDetailId"] != DBNull.Value)
                            //{
                            //    var detailId = Convert.ToInt32(dr["PR_InvoiceDetailId"]);
                            //    var taxes = DB.SlReturnInvoiceDetailSubTaxValues.Where(a => a.ReturnInvoiceDetailId == detailId);
                            //    foreach (var item in taxes)
                            //    {
                            //        var taxId = DB.E_TaxableTypes.FirstOrDefault(a => a.E_TaxableTypeId == item.esubTypeId).ParentTaxId;
                            //        if (taxId == discountTaxId)
                            //            total_ETaxValu -= item.value;
                            //        else
                            //            total_ETaxValu += item.value;
                            //    }
                            //}
                            //else
                                total_ETaxValu += dr["TotalTaxes"] != DBNull.Value ? Convert.ToDecimal(dr["TotalTaxes"]) : 0;

                        }
                    }
                }

                #region Discount
                discount_ratio = Convert.ToDecimal(txtDiscountRatio.EditValue) / 100;
                Discount_value = Convert.ToDecimal(txtDiscountValue.EditValue);

                if (discount_ratio > 0)
                {
                    if (Utilities.ValuesNotEqual(total_sell * discount_ratio, Discount_value))
                        Discount_value = total_sell * discount_ratio;
                }
                //else
                //    Discount_value = 0;
                #endregion

                #region Deduct_Tax
                decimal deductTax_ratio = Convert.ToDecimal(txt_DeductTaxR.EditValue) / 100;
                decimal deductTax_value = Convert.ToDecimal(txt_DeductTaxV.EditValue);

                if (deductTax_ratio > 0)
                {
                    if (Utilities.ValuesNotEqual(total_sell * deductTax_ratio, deductTax_value))
                        deductTax_value = total_sell * deductTax_ratio;
                }
                //else
                //    deductTax_value = 0;
                #endregion

                #region Add_Tax
                decimal addTax_ratio = Convert.ToDecimal(txt_AddTaxR.EditValue) / 100;
                decimal addTax_value = Convert.ToDecimal(txt_AddTaxV.EditValue);

                if (addTax_ratio > 0)
                {
                    if (Utilities.ValuesNotEqual(total_sell * addTax_ratio, addTax_value))
                        addTax_value = total_sell * addTax_ratio;
                }
                //else
                //    addTax_value = 0;
                #endregion

                total_sell = decimal.Round(total_sell, defaultRoundingPoints);
                total_salestax = decimal.Round(total_salestax, defaultRoundingPoints);
                total_Custax = decimal.Round(total_Custax, defaultRoundingPoints);
                deductTax_value = decimal.Round(deductTax_value, defaultRoundingPoints);
                addTax_value = decimal.Round(addTax_value, defaultRoundingPoints);
                Discount_value = decimal.Round(Discount_value, defaultRoundingPoints);
                total_ETaxValu = decimal.Round(total_ETaxValu, defaultRoundingPoints);
                decimal totaltaxValue = 0;
                if (Shared.E_invoiceAvailable || Shared.st_Store.E_AllowMoreThanTax == true)
                {
                    txtDiscountValue.Enabled = false;
                    txt_DeductTaxV.Enabled = false;
                    txt_AddTaxV.Enabled = false;
                    txtDiscountRatio.Enabled = false;
                    txt_DeductTaxR.Enabled = false;
                    txt_AddTaxR.Enabled = false;
                    //txt_AddTaxV.EditValue = taxValue + DiscountTotaltax;
                    if (gridColumn2.SummaryItem.SummaryValue != null)
                        Discount_value = Convert.ToDecimal(gridColumn2.SummaryItem.SummaryValue);

                    //Sales Tax
                    addTax_value = dtSLReturn_Details.AsEnumerable().Where(x => x.RowState != DataRowState.Deleted).Sum(X => Convert.ToDecimal(X["TotalSubAddTax"]));
                    //Table Taxes
                    total_Custax = dtSLReturn_Details.AsEnumerable().Where(x => x.RowState != DataRowState.Deleted).Sum(X => Convert.ToDecimal(X["TotalSubCustomTax"]));
                    //DeductTax
                    deductTax_value = dtSLReturn_Details.AsEnumerable().Where(x => x.RowState != DataRowState.Deleted && Convert.ToDecimal(x["TotalSubDiscountTax"]) > 0).Sum(X => Convert.ToDecimal(X["TotalSubDiscountTax"]));

                    totaltaxValue = dtSLReturn_Details.AsEnumerable()
                     .Where(r => r.RowState != DataRowState.Deleted)
                     .Sum(x => Convert.ToDecimal(x["TaxValue"] is DBNull ? 0 : Convert.ToDecimal(x["TaxValue"])));
                    txt_DeductTaxV.EditValue = decimal.Round(deductTax_value, 4);
                    txt_EtaxValue.EditValue = totaltaxValue;

                    total_sell = decimal.Round(total_sell, 4) /*- taxValue - Discount_value*/;

                    //net = decimal.Round(total_sell, 4) + taxValue - deductTax_value;
                    net = decimal.Round(total_sell, 4) + totaltaxValue/*addTax_value+total_Custax - deductTax_value*/;

                }

                var bounsDiscount = dtSLReturn_Details.AsEnumerable()
     .Where(r => r.RowState != DataRowState.Deleted)
     .Sum(x => Convert.ToDecimal(x["bonusDiscount"] is DBNull ? 0 : Convert.ToDecimal(x["bonusDiscount"])));

                Discount_value = dtSLReturn_Details.AsEnumerable()
     .Where(r => r.RowState != DataRowState.Deleted)
     .Sum(x => Convert.ToDecimal(x["DiscountValue"] is DBNull ? 0 : Convert.ToDecimal(x["DiscountValue"])));
                total_sell = total_sell + Discount_value;
                net = total_sell + total_salestax + totaltaxValue/*total_Custax + addTax_value - deductTax_value */- Discount_value - Convert.ToDecimal(txtExpenses.EditValue)- bounsDiscount;

                txtNet.EditValue = decimal.ToDouble(decimal.Round(net, defaultRoundingPoints)).ToString($"F{defaultRoundingPoints}");
                txtDiscountValue.EditValue = decimal.ToDouble(Discount_value).ToString($"F{defaultRoundingPoints}");
                txt_DeductTaxV.EditValue = decimal.ToDouble(deductTax_value).ToString($"F{defaultRoundingPoints}");
                txt_AddTaxV.EditValue = decimal.ToDouble(addTax_value).ToString($"F{defaultRoundingPoints}");
                txt_TaxValue.EditValue = decimal.ToDouble(total_salestax).ToString($"F{defaultRoundingPoints}");
                txt_CusTaxV.EditValue = decimal.ToDouble(total_Custax).ToString($"F{defaultRoundingPoints}");
                txt_Total.EditValue = decimal.ToDouble(total_sell).ToString($"F{defaultRoundingPoints}");
                txt_EtaxValue.EditValue = decimal.ToDouble(total_ETaxValu).ToString($"F{defaultRoundingPoints}");
                if (Shared.user.SL_Return_PayMethod == false)          //اجل
                {
                    if (Convert.ToDecimal(txt_paid.EditValue) == 0)
                        txt_Remains.EditValue = decimal.ToDouble(net).ToString($"F{defaultRoundingPoints}");
                    txt_Remains.EditValue = decimal.ToDouble(net - Convert.ToDecimal(txt_paid.EditValue)).ToString($"F{defaultRoundingPoints}");
                }
                else
                {
                    if (Convert.ToDecimal(txt_Remains.EditValue) == 0 && Convert.ToDecimal(txt_PayAcc2_Paid.EditValue) == 0)
                        txt_PayAcc1_Paid.EditValue = decimal.ToDouble(net).ToString($"F{defaultRoundingPoints}");
                    txt_Remains.EditValue = decimal.ToDouble(net - Convert.ToDecimal(txt_paid.EditValue)).ToString($"F{defaultRoundingPoints}");
                }
            }
            catch { }
        }

        private void txt_paid_EditValueChanged(object sender, EventArgs e)
        {
            try
            {
                if (Shared.user.SL_Return_PayMethod == true)
                {
                    if (Convert.ToDecimal(txt_Remains.EditValue) <= 0)
                    {
                        cmbPayMethod.EditValue = true;
                    }
                    else if (Convert.ToDecimal(txt_paid.EditValue) == 0)
                    {
                        cmbPayMethod.EditValue = false;
                    }
                    else
                        cmbPayMethod.EditValue = null;
                }

                if ((sender as TextEdit).Name == "txt_paid")
                    txt_Remains.EditValue = decimal.ToDouble(Convert.ToDecimal(txtNet.EditValue) - Convert.ToDecimal(txt_paid.EditValue));
                else if ((sender as TextEdit).Name == "txt_Remains")
                {
                    txt_paid.EditValue = decimal.ToDouble(Convert.ToDecimal(txtNet.EditValue) - Convert.ToDecimal(txt_Remains.EditValue));
                }
            }
            catch
            { }

        }

        private void txt_PayAcc1_Paid_EditValueChanged(object sender, EventArgs e)
        {
            if (Convert.ToDecimal(txt_PayAcc1_Paid.EditValue) < 0)
                txt_PayAcc1_Paid.EditValue = 0;
            if (Convert.ToDecimal(txt_PayAcc2_Paid.EditValue) < 0)
                txt_PayAcc2_Paid.EditValue = 0;

            txt_paid.EditValue = Decimal.ToDouble(Convert.ToDecimal(txt_PayAcc1_Paid.EditValue) + Convert.ToDecimal(txt_PayAcc2_Paid.EditValue));
        }

        void dt_TableNewRow(object sender, DataTableNewRowEventArgs e)
        {
            Get_TotalAccount();
        }
        void dt_RowChanged(object sender, DataRowChangeEventArgs e)
        {
            Get_TotalAccount();
        }

        private void mi_InvoiceStaticDisc_Click(object sender, EventArgs e)
        {
            if (ErpUtils.IsFormOpen(typeof(frm_InvoiceDiscs)))
                Application.OpenForms["frm_InvoiceDiscs"].Close();
            else
                new frm_InvoiceDiscs(Process.SellReturn).Show();
        }

        private void mi_InvoiceStaticDimensions_Click(object sender, EventArgs e)
        {
            if (ErpUtils.IsFormOpen(typeof(frm_InvoiceDimenstions)))
                Application.OpenForms["frm_InvoiceDimenstions"].Close();
            else
                new frm_InvoiceDimenstions(Process.SellReturn).Show();
        }

        void txtInvoiceCode_Leave(object sender, EventArgs e)
        {
            if (Shared.st_Store.InvoicesCodeRedundancy == null)
                return;

            bool code_exist = InvCodeExist();
            ErpHelper.ValidateInvCodeExist(code_exist, txtInvoiceCode);
        }

        public bool InvCodeExist()
        {
            return (from x in DB.SL_Returns
                    join s in DB.IC_Stores on x.StoreId equals s.StoreId
                    where Shared.st_Store.AutoInvSerialForStore == true ? x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true    //مستوى المخزن
                    where Shared.st_Store.AutoInvSerialForStore == null ? (int?)lkpStore.GetColumnValue("ParentId") != null ?
                       s.ParentId.HasValue && s.ParentId.Value == (int?)lkpStore.GetColumnValue("ParentId") : x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true//مستوى الفرع
                    where x.ReturnCode == txtInvoiceCode.Text
                    where x.SL_ReturnId != invoiceId

                    select x.ReturnCode).Count() > 0;
        }

        private void mi_PasteRows_Click(object sender, EventArgs e)
        {
            foreach (DataRow dr in ErpUtils.dt_Copied_Rows.Rows)
            {
                decimal totalqty = Convert.ToDecimal(dr["Qty"]);
                if (Shared.st_Store.MultiplyDimensions != (byte)Dimensions.Distinguish)
                    totalqty = Convert.ToDecimal(dr["Qty"]) * Convert.ToDecimal(dr["Length"]) * Convert.ToDecimal(dr["Width"])
                        * Convert.ToDecimal(dr["Height"]);

                DataRow row = dtSLReturn_Details.NewRow();
                int itemId = Convert.ToInt32(dr["ItemId"]);
                var item = DB.IC_Items.Where(x => x.ItemId == itemId).FirstOrDefault();
                LoadItemRow(item, row);
                row["Qty"] = Convert.ToDouble(dr["Qty"]);
                row["Height"] = dr["Height"];
                row["Length"] = dr["Length"];
                row["Width"] = dr["Width"];
                row["PiecesCount"] = dr["PiecesCount"];
                row["Expire"] = dr["Expire"];
                row["Batch"] = dr["Batch"];
                row["Serial"] = dr["Serial"];
                row["Serial2"] = dr["Serial2"];
                if (dr["SellPrice"] != DBNull.Value)
                    row["SellPrice"] = Convert.ToDouble(dr["SellPrice"]);

                row["TotalSellPrice"] = decimal.ToDouble(Convert.ToDecimal(row["SellPrice"]) * totalqty);
                dtSLReturn_Details.Rows.Add(row);
            }
        }

        private void repItems_Popup(object sender, EventArgs e)
        {
            if (rep_layout.Length > 0)
            {
                rep_layout.Seek(0, System.IO.SeekOrigin.Begin);
                (sender as GridLookUpEdit).Properties.View.RestoreLayoutFromStream(rep_layout);
            }

            (sender as GridLookUpEdit).Properties.View.ClearColumnsFilter();

        }

        private void repItems_CloseUp(object sender, DevExpress.XtraEditors.Controls.CloseUpEventArgs e)
        {
            rep_layout = new System.IO.MemoryStream();
            (sender as GridLookUpEdit).Properties.View.SaveLayoutToStream(rep_layout);
        }

        private void lkpStore_EditValueChanging(object sender, DevExpress.XtraEditors.Controls.ChangingEventArgs e)
        {
            if (Shared.InvoicePostToStore)
                if (dtSLReturn_Details.Rows.Count > 0)
                {
                    MessageBox.Show(Shared.IsEnglish ? "You have added some items, you can not change branch/store at the moment" : "لقد قمت بإدراج بعض الأصناف، لا يمكن تغيير الفرع/المحزن حاليا");
                    e.Cancel = true;
                    return;
                }
        }

        private void gridView2_InitNewRow(object sender, InitNewRowEventArgs e)
        {
            if (Shared.st_Store.PackCount == true)
                (grdPrInvoice.FocusedView as GridView).SetRowCellValue(e.RowHandle, "Pack", 1);
        }

        private void lkpCostCenter_Popup(object sender, EventArgs e)
        {
            gv_CostCenter.ClearSelection();

            if (gv_CostCenter.RowCount > 0)
            {

                foreach (DataRow d in dt_Multi_CC.Rows)
                {
                    int rowHandle = gv_CostCenter.LocateByValue("CostCenterId", Convert.ToInt32(d[0]));
                    gv_CostCenter.SelectRow(rowHandle);
                }
            }
        }

        private void lkpCostCenter_CloseUp(object sender, DevExpress.XtraEditors.Controls.CloseUpEventArgs e)
        {
            dt_Multi_CC.Rows.Clear();
            foreach (var c in gv_CostCenter.GetSelectedRows())
            {
                dt_Multi_CC.Rows.Add(gv_CostCenter.GetRowCellValue(c, "CostCenterId"));
            }
        }

        private void barBtnCancel_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            CancelInvoice(uUId).ConfigureAwait(false);
        }
        static async Task CancelInvoice(string UUId)
        {
            try
            {
                HttpClient client = new HttpClient();
                UriBuilder uriBuilder = new UriBuilder(Properties.Settings.Default.BackEndPoint);
                uriBuilder.Port = Properties.Settings.Default.BackEndPort;

                client.BaseAddress = uriBuilder.Uri;// new Uri(Properties.Settings.Default.BackEndPoint);
                client.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
                HttpResponseMessage response = await client.PutAsync(string.Format("api/Einvoice/ChangeDocumentStatus?UUID=" + UUId + "&status=4&reason=test&documentType=" + (int)DocumentType.C), null);

                if (response.IsSuccessStatusCode)
                    MessageBox.Show("تم إلغاء  مردود المبيعات بنجاح");
                else
                    MessageBox.Show("Status Code: " + response.StatusCode + " Message: " + response.ReasonPhrase);
            }
            catch (Exception exc)
            { MessageBox.Show(exc.Message); }
        }

        private void rep_btnAddTaxes_Click(object sender, EventArgs e)
        {
            if (ErpUtils.IsFormOpen(typeof(frm_Sl_Return_Add_Taxes)))
                Application.OpenForms["frm_Sl_Return_Add_Taxes"].Close();

            if (ErpUtils.IsFormOpen(typeof(frm_Sl_Return_Add_Taxes)))
                Application.OpenForms["frm_Sl_Return_Add_Taxes"].BringToFront();
            else
            {
              
                GridView view = grdPrInvoice.FocusedView as GridView;
                view.UpdateCurrentRow();
                dtSLReturn_Details.AcceptChanges();
                if (view.GetFocusedRowCellValue("ItemId") == DBNull.Value || view.GetFocusedRowCellValue("ItemId") == null) return;
                int ItemId = Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"));
                decimal SellPrice = Convert.ToDecimal(view.GetFocusedRowCellValue("SellPrice"));
                decimal Qty = Convert.ToInt32(view.GetFocusedRowCellValue("Qty"));
                int rowhandle = Convert.ToInt32(view.GetFocusedRowCellValue("RowHandle"));
                frm_Sl_Return_Add_Taxes form = new frm_Sl_Return_Add_Taxes(invoiceId,Dt_Rows, ItemId, SellPrice, Qty, rowhandle);

                if (form.ShowDialog() == DialogResult.OK)
                {
                    foreach (DataRow detail in dtSLReturn_Details.Rows)
                    {
                        if (Convert.ToInt32(detail["RowHandle"]) == rowhandle)
                        {
                            detail["TaxValue"] = 0;
                            detail["totalTaxesRatio"] = detail["TotalTaxes"] = 0;
                            calcSubTaxes(detail, view);
                        }
                    }

                }
            }
        }

        //private void calcSubTaxes(DataRow detail, GridView view)
        //{
        //    //decimal totalTaxValue = totalTaxRatio = 0;
        //    //var discountTaxId = DB.E_TaxableTypes.Where(a => a.Code == "T4").FirstOrDefault().E_TaxableTypeId;
        //    //foreach (DataRow m in Dt_Rows.Rows)
        //    //{
        //    //    if (Convert.ToInt32(m["RowHandle"]) == Convert.ToInt32(detail["RowHandle"]))
        //    //    {
        //    //        decimal DiscR1 = Convert.ToDecimal(detail["DiscountRatio"]);
        //    //        decimal DiscR2 = Convert.ToDecimal(detail["DiscountRatio2"]);
        //    //        decimal DiscR3 = Convert.ToDecimal(detail["DiscountRatio3"]);
        //    //        var xx = detail["totalTaxesRatio"];
        //    //        if (detail["totalTaxesRatio"] == DBNull.Value) totalTaxRatio = 0;
        //    //        else totalTaxRatio = Convert.ToDecimal(detail["totalTaxesRatio"]);

        //    //        if (detail["TotalTaxes"] == DBNull.Value) totalTaxValue = 0;
        //    //        else totalTaxValue = Convert.ToDecimal(detail["TotalTaxes"]);

        //    //        decimal TotalSellPrice = Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(detail["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);

        //    //        bool calcTaxBeforeDisc = Convert.ToBoolean(detail["calcTaxBeforeDisc"]);

        //    //        decimal taxValue = calcTaxBeforeDisc ?
        //    //            Math.Round(Convert.ToDecimal(detail["SellPrice"]) * Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(m["Percentage"]) / 100, defaultRoundingPoints)
        //    //            : Math.Round(TotalSellPrice * Convert.ToDecimal(m["Percentage"]) / 100, defaultRoundingPoints);

        //    //        m["TaxValue"] = taxValue;
        //    //        //=================Check DiscountTax or Not================//
        //    //        if (Convert.ToInt32(m["Tax"]) == discountTaxId)
        //    //        {

        //    //            decimal TaxRatio = Convert.ToDecimal(m["Percentage"]);
        //    //            totalTaxValue -= taxValue;
        //    //            totalTaxRatio -= TaxRatio;
        //    //        }

        //    //        else
        //    //        {


        //    //            decimal TaxRatio = Convert.ToDecimal(m["Percentage"]);
        //    //            totalTaxValue += taxValue;
        //    //            totalTaxRatio += TaxRatio;

        //    //        }
        //    //        m["TaxValue"] = taxValue;
        //    //        view.SetFocusedRowCellValue(totalTaxesRatio, totalTaxRatio);
        //    //        //========================================================//

        //    //    }
        //    //}
        //    //view.UpdateCurrentRow();
        //    //Get_TotalAccount();

        //    decimal totalTaxValue = totalTaxRatio = 0;
        //    decimal totalTableTaxRatio = 0;
        //    decimal tableTaxValue = 0;
        //    decimal tableTaxratio = 0;
        //    var discountTaxId = DB.E_TaxableTypes.Where(a => a.Code == "T4").FirstOrDefault().E_TaxableTypeId;
        //    //==================Updata==================//
        //    var tableTaxIds = DB.E_TaxableTypes.Where(a => tableTax.Contains(a.Code)).Select(a => a.E_TaxableTypeId.ToString()).ToList();

        //    var tableTaxes = Dt_Rows.AsEnumerable()
        //     .Where(a => a.RowState != DataRowState.Deleted)
        //     .Where(r => tableTaxIds.Contains(r["Tax"].ToString()) && r["Tax"].ToString() != "1" && Convert.ToInt32(r["RowHandle"]) == Convert.ToInt32(detail["RowHandle"])).ToList();

        //    var otherTaxes = Dt_Rows.AsEnumerable()
        //    .Where(a => a.RowState != DataRowState.Deleted)
        //    .Where(r => !tableTaxIds.Contains(r["Tax"].ToString()) && r["Tax"].ToString() != "1" && Convert.ToInt32(r["RowHandle"]) == Convert.ToInt32(detail["RowHandle"])).ToList();

        //    var addTaxType = Dt_Rows.AsEnumerable()
        //       .Where(a => a.RowState != DataRowState.Deleted)
        //    .Where(r => r["Tax"].ToString() == "1"
        //    && Convert.ToInt32(r["RowHandle"]) == Convert.ToInt32(detail["RowHandle"])).ToList();
        //    //=================Case 1===================//
        //    foreach (DataRow row in tableTaxes)
        //    {

        //        if (Convert.ToInt32(row["RowHandle"]) == Convert.ToInt32(detail["RowHandle"]))
        //        {
        //            decimal TotalSellPrice = 0;
        //            decimal DiscR1 = Convert.ToDecimal(detail["DiscountRatio"]);
        //            decimal DiscR2 = Convert.ToDecimal(detail["DiscountRatio2"]);
        //            decimal DiscR3 = Convert.ToDecimal(detail["DiscountRatio3"]);
        //            if (detail["totalTaxesRatio"] == DBNull.Value) totalTaxRatio = 0;
        //            else totalTaxRatio = Convert.ToDecimal(detail["totalTaxesRatio"]);

        //            if (detail["TotalTaxes"] == DBNull.Value) totalTaxValue = 0;
        //            else totalTaxValue = Convert.ToDecimal(detail["TotalTaxes"]);

        //            bool calcTaxBeforeDisc = Convert.ToBoolean(detail["calcTaxBeforeDisc"]);
        //            TotalSellPrice = Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(detail["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);
        //            decimal taxValue = calcTaxBeforeDisc ?
        //                Math.Round(Convert.ToDecimal(detail["SellPrice"]) * Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(row["Percentage"]) / 100, defaultRoundingPoints)
        //                : Math.Round(TotalSellPrice * Convert.ToDecimal(row["Percentage"]) / 100, defaultRoundingPoints);
        //            row["TaxValue"] = taxValue;
        //            //=================Check DiscountTax or Not================//
        //            if (Convert.ToInt32(row["Tax"]) == discountTaxId)
        //            {
        //                decimal TaxRatio = Math.Round(Convert.ToDecimal(row["Percentage"]), defaultRoundingPoints);
        //                totalTaxValue -= taxValue;
        //                totalTaxRatio -= TaxRatio;
        //                totalTableTaxRatio -= TaxRatio;
        //            }

        //            else
        //            {
        //                decimal TaxRatio = Math.Round(Convert.ToDecimal(row["Percentage"]), defaultRoundingPoints);
        //                totalTaxValue += taxValue;
        //                totalTaxRatio += TaxRatio;
        //                totalTableTaxRatio += TaxRatio;
        //                tableTaxValue += taxValue;
        //                tableTaxratio += TaxRatio;
        //            }
        //            row["TaxValue"] = taxValue;
        //            view.SetFocusedRowCellValue(totalTaxesRatio, totalTaxRatio);
        //            //detail["salePriceWithTaxTable"] = Math.Round(Convert.ToDecimal(TotalSellPrice + tableTaxValue), defaultRoundingPoints);
        //            detail["totalTableTaxes"] = Math.Round(Convert.ToDecimal(tableTaxratio), defaultRoundingPoints);
        //            detail["tableTaxValue"] = Math.Round(Convert.ToDecimal(taxValue), defaultRoundingPoints);
        //            //========================================================//

        //        }
        //    }
        //    if (tableTaxes.Count == 0)
        //    {
        //        detail["totalTableTaxes"] = Math.Round(Convert.ToDecimal(0), defaultRoundingPoints);
        //        detail["tableTaxValue"] = Math.Round(Convert.ToDecimal(0), defaultRoundingPoints);
        //    }
        //    //=================Case 2===================//
        //    foreach (DataRow mm in addTaxType)
        //    {

        //        if (Convert.ToInt32(mm["RowHandle"]) == Convert.ToInt32(detail["RowHandle"]))
        //        {
        //            decimal TotalSellPrice = 0;
        //            decimal taxRatio = 0;
        //            decimal DiscR1 = Convert.ToDecimal(detail["DiscountRatio"]);
        //            decimal DiscR2 = Convert.ToDecimal(detail["DiscountRatio2"]);
        //            decimal DiscR3 = Convert.ToDecimal(detail["DiscountRatio3"]);
        //            var xx = detail["totalTaxesRatio"];
        //            if (detail["totalTaxesRatio"] == DBNull.Value) totalTaxRatio = 0;
        //            else totalTaxRatio = Convert.ToDecimal(detail["totalTaxesRatio"]);

        //            if (detail["TotalTaxes"] == DBNull.Value) totalTaxValue = 0;
        //            else totalTaxValue = Convert.ToDecimal(detail["TotalTaxes"]);
        //            TotalSellPrice = Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(detail["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);
        //            var totalSellPriceWithTableTaxes = Convert.ToDecimal(totalTableTaxRatio / 100) * TotalSellPrice;
        //            TotalSellPrice = TotalSellPrice + totalSellPriceWithTableTaxes;


        //            //=================================//
        //            bool calcTaxBeforeDisc = Convert.ToBoolean(detail["calcTaxBeforeDisc"]);

        //            decimal taxValue = calcTaxBeforeDisc ?
        //                Math.Round(Convert.ToDecimal(detail["SellPrice"]) * Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(mm["Percentage"]) / 100, defaultRoundingPoints)
        //                : Math.Round(TotalSellPrice * Convert.ToDecimal(mm["Percentage"]) / 100, defaultRoundingPoints);

        //            mm["TaxValue"] = taxValue;


        //            TotalSellPrice = Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(detail["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);
        //            taxRatio = Math.Round(Convert.ToDecimal(taxValue / TotalSellPrice) * 100, defaultRoundingPoints);


        //            //=================Check DiscountTax or Not================//
        //            if (Convert.ToInt32(mm["Tax"]) == discountTaxId)
        //            {

        //                decimal TaxRatio = taxRatio;
        //                totalTaxValue -= taxValue;
        //                totalTaxRatio -= TaxRatio;
        //            }

        //            else
        //            {
        //                decimal TaxRatio = taxRatio;
        //                totalTaxValue += taxValue;
        //                totalTaxRatio += TaxRatio;

        //            }
        //            mm["TaxValue"] = taxValue;
        //            view.SetFocusedRowCellValue(totalTaxesRatio, totalTaxRatio);
        //            detail["addTaxValue"] = Math.Round(Convert.ToDecimal(taxValue), defaultRoundingPoints);
        //            //========================================================//

        //        }
        //    }
        //    if (addTaxType.Count == 0)
        //    {
        //        detail["addTaxValue"] = Math.Round(Convert.ToDecimal(0), defaultRoundingPoints);
        //    }
        //    //==============Case 3=================//
        //    foreach (DataRow m in otherTaxes)
        //    {
        //        if (Convert.ToInt32(m["RowHandle"]) == Convert.ToInt32(detail["RowHandle"]))
        //        {
        //            decimal TotalSellPrice = 0;
        //            decimal DiscR1 = Convert.ToDecimal(detail["DiscountRatio"]);
        //            decimal DiscR2 = Convert.ToDecimal(detail["DiscountRatio2"]);
        //            decimal DiscR3 = Convert.ToDecimal(detail["DiscountRatio3"]);
        //            var xx = detail["totalTaxesRatio"];
        //            if (detail["totalTaxesRatio"] == DBNull.Value) totalTaxRatio = 0;
        //            else totalTaxRatio = Convert.ToDecimal(detail["totalTaxesRatio"]);

        //            if (detail["TotalTaxes"] == DBNull.Value) totalTaxValue = 0;
        //            else totalTaxValue = Convert.ToDecimal(detail["TotalTaxes"]);
        //            TotalSellPrice = Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(detail["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);
        //            bool calcTaxBeforeDisc = Convert.ToBoolean(detail["calcTaxBeforeDisc"]);

        //            decimal taxValue = calcTaxBeforeDisc ?
        //                Math.Round(Convert.ToDecimal(detail["SellPrice"]) * Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(m["Percentage"]) / 100, defaultRoundingPoints)
        //                : Math.Round(TotalSellPrice * Convert.ToDecimal(m["Percentage"]) / 100, defaultRoundingPoints);
        //            m["TaxValue"] = taxValue;
        //            //=================Check DiscountTax or Not================//
        //            if (Convert.ToInt32(m["Tax"]) == discountTaxId)
        //            {

        //                decimal TaxRatio = Math.Round(Convert.ToDecimal(m["Percentage"]), defaultRoundingPoints);
        //                totalTaxValue -= taxValue;
        //                totalTaxRatio -= TaxRatio;
        //            }

        //            else
        //            {
        //                decimal TaxRatio = Math.Round(Convert.ToDecimal(m["Percentage"]), defaultRoundingPoints);
        //                totalTaxValue += taxValue;
        //                totalTaxRatio += TaxRatio;

        //            }
        //            m["TaxValue"] = taxValue;
        //            view.SetFocusedRowCellValue(totalTaxesRatio, totalTaxRatio);

        //            //========================================================//

        //        }
        //    }
        //    view.UpdateCurrentRow();
        //    Get_TotalAccount();
        //}
        private void calcSubTaxesAgin(DataRow detail, GridView view)
        {

            decimal totalTaxValue = totalTaxRatio = 0;
            decimal totalTableTaxRatio = 0;
            decimal tableTaxValue = 0;
            decimal tableTaxratio = 0;
            var discountTaxId = DB.E_TaxableTypes.Where(a => a.Code == "T4").FirstOrDefault().E_TaxableTypeId;
            //==================Updata==================//
            var tableTaxIds = DB.E_TaxableTypes.Where(a => tableTax.Contains(a.Code)).Select(a => a.E_TaxableTypeId.ToString()).ToList();

            var tableTaxes = Dt_Rows.AsEnumerable()
             .Where(a => a.RowState != DataRowState.Deleted)
             .Where(r => tableTaxIds.Contains(r["Tax"].ToString()) && r["Tax"].ToString() != "1" && Convert.ToInt32(r["RowHandle"]) == Convert.ToInt32(detail["RowHandle"])).ToList();

            var otherTaxes = Dt_Rows.AsEnumerable()
            .Where(a => a.RowState != DataRowState.Deleted)
            .Where(r => !tableTaxIds.Contains(r["Tax"].ToString()) && r["Tax"].ToString() != "1" && Convert.ToInt32(r["RowHandle"]) == Convert.ToInt32(detail["RowHandle"])).ToList();

            var addTaxType = Dt_Rows.AsEnumerable()
                 .Where(a => a.RowState != DataRowState.Deleted)
            .Where(r => r["Tax"].ToString() == "1"
            && Convert.ToInt32(r["RowHandle"]) == Convert.ToInt32(detail["RowHandle"])).ToList();
            //=================Case 1===================//
            foreach (DataRow row in tableTaxes)
            {

                if (Convert.ToInt32(row["RowHandle"]) == Convert.ToInt32(detail["RowHandle"]))
                {
                    decimal TotalSellPrice = 0;
                    decimal DiscR1 = Convert.ToDecimal(detail["DiscountRatio"]);
                    decimal DiscR2 = Convert.ToDecimal(detail["DiscountRatio2"]);
                    decimal DiscR3 = Convert.ToDecimal(detail["DiscountRatio3"]);
                    if (detail["totalTaxesRatio"] == DBNull.Value) totalTaxRatio = 0;
                    else totalTaxRatio = Convert.ToDecimal(detail["totalTaxesRatio"]);

                    if (detail["TotalTaxes"] == DBNull.Value) totalTaxValue = 0;
                    else totalTaxValue = Convert.ToDecimal(detail["TotalTaxes"]);

                    bool calcTaxBeforeDisc = Convert.ToBoolean(detail["calcTaxBeforeDisc"]);
                    TotalSellPrice = Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(detail["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);
                    decimal taxValue = calcTaxBeforeDisc ?
                        Math.Round(Convert.ToDecimal(detail["SellPrice"]) * Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(row["Percentage"]) / 100, defaultRoundingPoints)
                        : Math.Round(TotalSellPrice * Convert.ToDecimal(row["Percentage"]) / 100, defaultRoundingPoints);
                    row["TaxValue"] = taxValue;
                    //=================Check DiscountTax or Not================//
                    if (Convert.ToInt32(row["Tax"]) == discountTaxId)
                    {
                        decimal TaxRatio = Math.Round(Convert.ToDecimal(row["Percentage"]), defaultRoundingPoints);
                        totalTaxValue -= taxValue;
                        totalTaxRatio -= TaxRatio;
                        totalTableTaxRatio -= TaxRatio;
                    }

                    else
                    {
                        decimal TaxRatio = Math.Round(Convert.ToDecimal(row["Percentage"]), defaultRoundingPoints);
                        totalTaxValue += taxValue;
                        totalTaxRatio += TaxRatio;
                        totalTableTaxRatio += TaxRatio;
                        tableTaxValue += taxValue;
                        tableTaxratio += TaxRatio;
                    }
                    row["TaxValue"] = taxValue;

                    //========================================================//

                }
            }

            //=================Case 2===================//
            foreach (DataRow mm in addTaxType)
            {

                if (Convert.ToInt32(mm["RowHandle"]) == Convert.ToInt32(detail["RowHandle"]))
                {
                    decimal TotalSellPrice = 0;
                    decimal taxRatio = 0;
                    decimal DiscR1 = Convert.ToDecimal(detail["DiscountRatio"]);
                    decimal DiscR2 = Convert.ToDecimal(detail["DiscountRatio2"]);
                    decimal DiscR3 = Convert.ToDecimal(detail["DiscountRatio3"]);
                    var xx = detail["totalTaxesRatio"];
                    if (detail["totalTaxesRatio"] == DBNull.Value) totalTaxRatio = 0;
                    else totalTaxRatio = Convert.ToDecimal(detail["totalTaxesRatio"]);

                    if (detail["TotalTaxes"] == DBNull.Value) totalTaxValue = 0;
                    else totalTaxValue = Convert.ToDecimal(detail["TotalTaxes"]);
                    TotalSellPrice = Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(detail["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);
                    var totalSellPriceWithTableTaxes = Convert.ToDecimal(totalTableTaxRatio / 100) * TotalSellPrice;
                    TotalSellPrice = TotalSellPrice + totalSellPriceWithTableTaxes;


                    //=================================//
                    bool calcTaxBeforeDisc = Convert.ToBoolean(detail["calcTaxBeforeDisc"]);

                    decimal taxValue = calcTaxBeforeDisc ?
                        Math.Round(Convert.ToDecimal(detail["SellPrice"]) * Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(mm["Percentage"]) / 100, defaultRoundingPoints)
                        : Math.Round(TotalSellPrice * Convert.ToDecimal(mm["Percentage"]) / 100, defaultRoundingPoints);

                    mm["TaxValue"] = taxValue;


                    TotalSellPrice = Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(detail["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);
                    taxRatio = Math.Round(Convert.ToDecimal(taxValue / (TotalSellPrice == 0 ? 1 : TotalSellPrice)) * 100, defaultRoundingPoints);


                    //=================Check DiscountTax or Not================//
                    if (Convert.ToInt32(mm["Tax"]) == discountTaxId)
                    {

                        decimal TaxRatio = taxRatio;
                        totalTaxValue -= taxValue;
                        totalTaxRatio -= TaxRatio;
                    }

                    else
                    {
                        decimal TaxRatio = taxRatio;
                        totalTaxValue += taxValue;
                        totalTaxRatio += TaxRatio;

                    }

                    mm["TaxValue"] = taxValue;
                    //========================================================//

                }
            }
            //==============Case 3=================//
            foreach (DataRow m in otherTaxes)
            {
                if (Convert.ToInt32(m["RowHandle"]) == Convert.ToInt32(detail["RowHandle"]))
                {
                    decimal TotalSellPrice = 0;
                    decimal DiscR1 = Convert.ToDecimal(detail["DiscountRatio"]);
                    decimal DiscR2 = Convert.ToDecimal(detail["DiscountRatio2"]);
                    decimal DiscR3 = Convert.ToDecimal(detail["DiscountRatio3"]);
                    var xx = detail["totalTaxesRatio"];
                    if (detail["totalTaxesRatio"] == DBNull.Value) totalTaxRatio = 0;
                    else totalTaxRatio = Convert.ToDecimal(detail["totalTaxesRatio"]);

                    if (detail["TotalTaxes"] == DBNull.Value) totalTaxValue = 0;
                    else totalTaxValue = Convert.ToDecimal(detail["TotalTaxes"]);
                    TotalSellPrice = Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(detail["SellPrice"]) * (1 - DiscR1) * (1 - DiscR2) * (1 - DiscR3);
                    bool calcTaxBeforeDisc = Convert.ToBoolean(detail["calcTaxBeforeDisc"]);

                    decimal taxValue = calcTaxBeforeDisc ?
                        Math.Round(Convert.ToDecimal(detail["SellPrice"]) * Convert.ToDecimal(detail["Qty"]) * Convert.ToDecimal(m["Percentage"]) / 100, defaultRoundingPoints)
                        : Math.Round(TotalSellPrice * Convert.ToDecimal(m["Percentage"]) / 100, defaultRoundingPoints);
                    m["TaxValue"] = taxValue;
                    //=================Check DiscountTax or Not================//
                    if (Convert.ToInt32(m["Tax"]) == discountTaxId)
                    {

                        decimal TaxRatio = Math.Round(Convert.ToDecimal(m["Percentage"]), defaultRoundingPoints);
                        totalTaxValue -= taxValue;
                        totalTaxRatio -= TaxRatio;
                    }

                    else
                    {
                        decimal TaxRatio = Math.Round(Convert.ToDecimal(m["Percentage"]), defaultRoundingPoints);
                        totalTaxValue += taxValue;
                        totalTaxRatio += TaxRatio;

                    }
                    m["TaxValue"] = taxValue;



                }
            }
        }
        private void updateSubTaxGrid()
        {
            foreach (DataRow dTax in Dt_Rows.Rows)
            {
                if (dt_SubTax.AsEnumerable()
                  .Where(a => a.RowState != DataRowState.Deleted)
                  .Any(r => Convert.ToInt32(r["SubTaxId"].ToString()) == Convert.ToInt32(dTax["SubTax"])))
                {
                    var subTax = dt_SubTax.AsEnumerable()
                   .Where(a => a.RowState != DataRowState.Deleted)
                   .FirstOrDefault(r => Convert.ToInt32(r["SubTaxId"].ToString()) == Convert.ToInt32(dTax["SubTax"]));
                    subTax["Rate"] = Convert.ToDecimal(dTax["Percentage"]);
                    subTax["Value"] = Convert.ToDecimal(dTax["TaxValue"]);

                }
                else
                {
                    DataRow dtTax = dt_SubTax.NewRow();
                    dtTax["SubTaxId"] = Convert.ToInt32(dTax["SubTax"]);
                    dtTax["Rate"] = Convert.ToDecimal(dTax["Percentage"]);
                    dtTax["Value"] = Convert.ToDecimal(dTax["TaxValue"]);
                    dt_SubTax.Rows.Add(dtTax);
                }
            }
        }
    }
}