﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="col_GenMatrix3.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="col_GenMatrix1.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl3.Name" xml:space="preserve">
    <value>labelControl3</value>
  </data>
  <data name="txtMtrx3.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_MDName.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="groupBox1.TabIndex" type="System.Int32, mscorlib">
    <value>23</value>
  </data>
  <data name="&gt;&gt;labelControl4.Name" xml:space="preserve">
    <value>labelControl4</value>
  </data>
  <data name="repositoryItemCheckEdit3.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="lkpMatrix3.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Center</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="txtMtrx1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtMtrx1.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;gridColumn9.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMtrx1.Properties.AppearanceDisabled.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>64, 64, 64</value>
  </data>
  <data name="repositoryItemSpinEdit2.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpMatrix3.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtMtrxSprtr2.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpMatrix1.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="txtParentItemName.Location" type="System.Drawing.Point, System.Drawing">
    <value>559, 20</value>
  </data>
  <data name="lkpMatrix2.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_MDCode.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <assembly alias="DevExpress.XtraEditors.v15.1" name="DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="txtMtrx3.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;labelControl4.Parent" xml:space="preserve">
    <value>tab_DefineMatrix</value>
  </data>
  <data name="txtMtrxSprtr0.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMtrxSprtr3.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_GenItemName.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_MatrixId.Caption" xml:space="preserve">
    <value>MatrixId</value>
  </data>
  <data name="&gt;&gt;gridColumn6.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMtrxSprtr2.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txtMtrxSprtr3.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtMtrx3.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="xtraTabControl1.AppearancePage.Header.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn2.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtMtrx1.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;btn_Save.Name" xml:space="preserve">
    <value>btn_Save</value>
  </data>
  <data name="col_GenItemCode.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;txtMtrx3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMtrxSprtr2.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gridColumn8.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMtrx2.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;col_GenItemCode.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn9.Width" type="System.Int32, mscorlib">
    <value>59</value>
  </data>
  <data name="lkpMatrix3.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Center</value>
  </data>
  <data name="labelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>742, 23</value>
  </data>
  <data name="col_MDCode.AppearanceHeader.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Navy</value>
  </data>
  <data name="&gt;&gt;grd_Mtrx3.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="groupBox1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkpMatrix1.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;grd_Mtrx2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.GridControl, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn2.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;col_GenMatrix3.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;grd_genItems.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.GridControl, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_MatrixId.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpMatrix1.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtMtrxCode0.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtMtrx3.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtMtrxSprtr0.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_GenMatrix1.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMtrxSprtr0.Location" type="System.Drawing.Point, System.Drawing">
    <value>607, 28</value>
  </data>
  <data name="txtMtrxSprtr1.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;repositoryItemCheckEdit3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMtrx2.Location" type="System.Drawing.Point, System.Drawing">
    <value>255, 28</value>
  </data>
  <data name="rep_SLQtyNums.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="col_MDCode.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_GenMatrix2.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="labelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>57, 13</value>
  </data>
  <data name="col_GenMatrix3.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView2.Appearance.FooterPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn7.Width" type="System.Int32, mscorlib">
    <value>41</value>
  </data>
  <data name="txtMtrxSprtr0.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="lkpMatrix3.Properties.Columns7" xml:space="preserve">
    <value>MatrixId</value>
  </data>
  <data name="btn_GenerateItems.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="&gt;&gt;colDisabled2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_ItmMtrxDetailId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_GenItemName.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtMtrx3.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="gridColumn3.Width" type="System.Int32, mscorlib">
    <value>59</value>
  </data>
  <assembly alias="DevExpress.Utils.v15.1" name="DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="grd_Mtrx1.EmbeddedNavigator.ToolTipIconType" type="DevExpress.Utils.ToolTipIconType, DevExpress.Utils.v15.1">
    <value>None</value>
  </data>
  <data name="gv_SalesPerQty.Appearance.FooterPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;col_available.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_MDCode.Width" type="System.Int32, mscorlib">
    <value>59</value>
  </data>
  <data name="&gt;&gt;labelControl1.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="&gt;&gt;col_GenMatrix2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpMatrix2.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txtMtrx3.Name" xml:space="preserve">
    <value>txtMtrx3</value>
  </data>
  <data name="grd_Mtrx1.EmbeddedNavigator.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>Inherit</value>
  </data>
  <data name="lkpMatrix1.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;groupBox1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;gridColumn10.Name" xml:space="preserve">
    <value>gridColumn10</value>
  </data>
  <data name="&gt;&gt;gv_SalesPerQty.Name" xml:space="preserve">
    <value>gv_SalesPerQty</value>
  </data>
  <data name="txtMtrx2.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_GenItemName.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_GenMatrix1.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="xtraTabControl1.AppearancePage.Header.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl4.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;grd_Mtrx2.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;groupBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="txtMtrx2.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="&gt;&gt;txtMtrxCode0.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="txtMtrx1.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtMtrxCode0.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;grd_Mtrx2.Name" xml:space="preserve">
    <value>grd_Mtrx2</value>
  </data>
  <data name="lkpMatrix2.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpMatrix3.Properties.AppearanceDropDownHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grd_genItems.EmbeddedNavigator.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="col_GenItemCode.AppearanceHeader.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt</value>
  </data>
  <data name="lkpMatrix1.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpMatrix1.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;rep_SLQtyNums.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn8.Caption" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="txtMtrx3.Properties.AppearanceDisabled.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="labelControl1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtMtrx2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="xtraTabControl1.HeaderAutoFill" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>True</value>
  </data>
  <data name="txtMtrx3.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_GenMatrix2.Caption" xml:space="preserve">
    <value>Matrix2</value>
  </data>
  <data name="grd_Mtrx2.EmbeddedNavigator.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>Tile</value>
  </data>
  <data name="&gt;&gt;tab_GeneratedItems.Parent" xml:space="preserve">
    <value>xtraTabControl1</value>
  </data>
  <data name="gridColumn9.AppearanceHeader.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Navy</value>
  </data>
  <data name="txtMtrxSprtr2.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_GenMatrix2.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_available.Caption" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="lkpMatrix2.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="repositoryItemSpinEdit2.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtMtrxSprtr1.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gridColumn11.Name" xml:space="preserve">
    <value>gridColumn11</value>
  </data>
  <data name="col_GenMatrix2.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="grd_Mtrx3.EmbeddedNavigator.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="grd_Mtrx2.Location" type="System.Drawing.Point, System.Drawing">
    <value>306, 98</value>
  </data>
  <data name="txtMtrxSprtr0.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="col_GenMatrix1.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="labelControl4.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 13</value>
  </data>
  <data name="txtMtrx2.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;txtMtrxSprtr2.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="txtMtrxSprtr3.Location" type="System.Drawing.Point, System.Drawing">
    <value>19, 28</value>
  </data>
  <data name="grd_Mtrx1.EmbeddedNavigator.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>frm_IC_ItemMatrixCreate</value>
  </data>
  <data name="gridColumn2.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="lkpMatrix2.Properties.Columns7" xml:space="preserve">
    <value>MatrixId</value>
  </data>
  <data name="&gt;&gt;labelControl2.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="lkpMatrix2.Properties.AppearanceDropDown.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtMtrx3.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lkpMatrix3.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="txtMtrx1.Properties.AppearanceDisabled.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpMatrix3.Properties.Columns" xml:space="preserve">
    <value>MatrixName</value>
  </data>
  <data name="&gt;&gt;gridView3.Name" xml:space="preserve">
    <value>gridView3</value>
  </data>
  <data name="txtMtrxSprtr3.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="col_GenItemCode.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtMtrx1.Location" type="System.Drawing.Point, System.Drawing">
    <value>451, 28</value>
  </data>
  <data name="labelControl3.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 13</value>
  </data>
  <data name="&gt;&gt;gridView2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="grd_Mtrx1.EmbeddedNavigator.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>Tile</value>
  </data>
  <data name="txtParentItemName.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_MDCode.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;col_Serial.Name" xml:space="preserve">
    <value>col_Serial</value>
  </data>
  <data name="col_GenItemName.Caption" xml:space="preserve">
    <value>Item Name</value>
  </data>
  <data name="txtMtrxSprtr1.Size" type="System.Drawing.Size, System.Drawing">
    <value>34, 20</value>
  </data>
  <data name="txtMtrxCode0.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtMtrxCode0.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;colDisabled1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 13</value>
  </data>
  <data name="gridColumn11.Caption" xml:space="preserve">
    <value>MatrixDetailId</value>
  </data>
  <data name="txtMtrxSprtr3.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn9.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="lkpMatrix3.Properties.AppearanceFocused.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repositoryItemSpinEdit3.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="col_GenMatrix3.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtMtrx1.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="grd_genItems.EmbeddedNavigator.BackgroundImage" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMtrxSprtr0.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="gridView2.Appearance.FooterPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="grd_Mtrx1.EmbeddedNavigator.TextLocation" type="DevExpress.XtraEditors.NavigatorButtonsTextLocation, DevExpress.XtraEditors.v15.1">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;txtMtrxSprtr1.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="lkpMatrix3.Properties.Columns1" xml:space="preserve">
    <value>Matrix Name</value>
  </data>
  <data name="&gt;&gt;btn_Save.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="grd_genItems.EmbeddedNavigator.ToolTipTitle" xml:space="preserve">
    <value />
  </data>
  <data name="gridView1.Appearance.FooterPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn4.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txtMtrxSprtr3.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="txtMtrxSprtr0.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtMtrxSprtr1.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtParentItemName.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtParentItemName.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_MDName.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpMatrix1.Properties.Columns" xml:space="preserve">
    <value>MatrixName</value>
  </data>
  <data name="col_Serial.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView3.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpMatrix2.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Center</value>
  </data>
  <data name="txtMtrx1.Properties.AppearanceDisabled.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtMtrxSprtr3.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpMatrix1.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repositoryItemSpinEdit3.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;tab_DefineMatrix.Name" xml:space="preserve">
    <value>tab_DefineMatrix</value>
  </data>
  <data name="&gt;&gt;txtMtrxCode0.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="grd_Mtrx1.EmbeddedNavigator.AllowHtmlTextInToolTip" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="col_GenMatrix3.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtMtrxSprtr1.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="col_GenMatrix3.Caption" xml:space="preserve">
    <value>Matrix3</value>
  </data>
  <data name="lkpMatrix3.Properties.AppearanceDropDown.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpMatrix3.Size" type="System.Drawing.Size, System.Drawing">
    <value>236, 20</value>
  </data>
  <data name="txtMtrx3.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="&gt;&gt;txtMtrx1.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="txtMtrxCode0.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;col_GenMatrix3.Name" xml:space="preserve">
    <value>col_GenMatrix3</value>
  </data>
  <data name="txtParentItemName.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkpMatrix2.Size" type="System.Drawing.Size, System.Drawing">
    <value>236, 20</value>
  </data>
  <data name="&gt;&gt;btn_GenerateItems.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMtrx2.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_GenMatrix3.Width" type="System.Int32, mscorlib">
    <value>100</value>
  </data>
  <data name="col_available.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="repositoryItemSpinEdit1.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpMatrix3.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn2.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn2.Caption" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="txtMtrxSprtr1.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_Serial.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_Serial.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtMtrx3.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="lkpMatrix1.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpMatrix2.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Center</value>
  </data>
  <data name="col_GenItemName.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="&gt;&gt;gridView1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMtrx3.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtParentItemName.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtMtrxSprtr2.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpMatrix3.Location" type="System.Drawing.Point, System.Drawing">
    <value>49, 73</value>
  </data>
  <data name="col_MatrixDetailId.Caption" xml:space="preserve">
    <value>MatrixDetailId</value>
  </data>
  <data name="&gt;&gt;groupBox1.Name" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="txtMtrx1.Properties.AppearanceDisabled.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtMtrx1.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtParentItemName.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtParentItemName.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;labelControl1.Name" xml:space="preserve">
    <value>labelControl1</value>
  </data>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="&gt;&gt;txtMtrx1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="grd_genItems.EmbeddedNavigator.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpMatrix3.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="gridColumn2.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_GenMatrix2.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_ItmMtrxDetailId.Caption" xml:space="preserve">
    <value>ItmMtrxDetailId</value>
  </data>
  <data name="rep_SLQtyNums.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtMtrx1.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn3.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_GenItemCode.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpMatrix3.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repositoryItemSpinEdit1.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.XtraForm, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpMatrix1.Size" type="System.Drawing.Size, System.Drawing">
    <value>236, 20</value>
  </data>
  <data name="gridColumn4.Width" type="System.Int32, mscorlib">
    <value>246</value>
  </data>
  <data name="&gt;&gt;tab_DefineMatrix.Parent" xml:space="preserve">
    <value>xtraTabControl1</value>
  </data>
  <data name="grd_Mtrx3.Location" type="System.Drawing.Point, System.Drawing">
    <value>49, 98</value>
  </data>
  <data name="repositoryItemSpinEdit3.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpMatrix1.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="txtMtrx1.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;lkpMatrix2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;rep_SLQtyNums.Name" xml:space="preserve">
    <value>rep_SLQtyNums</value>
  </data>
  <data name="col_MatrixId.Width" type="System.Int32, mscorlib">
    <value>246</value>
  </data>
  <data name="grd_genItems.TabIndex" type="System.Int32, mscorlib">
    <value>23</value>
  </data>
  <data name="lkpMatrix1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;grd_Mtrx1.Name" xml:space="preserve">
    <value>grd_Mtrx1</value>
  </data>
  <data name="&gt;&gt;col_MatrixDetailId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtMtrxSprtr0.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="&gt;&gt;lkpMatrix1.Name" xml:space="preserve">
    <value>lkpMatrix1</value>
  </data>
  <data name="grd_Mtrx2.EmbeddedNavigator.BackgroundImage" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn8.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView3.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMtrxSprtr1.Location" type="System.Drawing.Point, System.Drawing">
    <value>411, 28</value>
  </data>
  <data name="col_GenMatrix1.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="tab_GeneratedItems.Text" xml:space="preserve">
    <value>Generated Items</value>
  </data>
  <data name="lkpMatrix2.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="grd_Mtrx3.EmbeddedNavigator.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>Inherit</value>
  </data>
  <data name="labelControl3.Location" type="System.Drawing.Point, System.Drawing">
    <value>455, 54</value>
  </data>
  <data name="txtMtrxCode0.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="tab_DefineMatrix.Text" xml:space="preserve">
    <value>Define Matrix</value>
  </data>
  <data name="col_MatrixId.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpMatrix3.Properties.AppearanceDropDownHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;gridColumn12.Name" xml:space="preserve">
    <value>gridColumn12</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Item Matrix Generation</value>
  </data>
  <data name="&gt;&gt;colDisabled2.Name" xml:space="preserve">
    <value>colDisabled2</value>
  </data>
  <data name="txtMtrx1.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="txtMtrxSprtr2.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtMtrx3.Size" type="System.Drawing.Size, System.Drawing">
    <value>150, 20</value>
  </data>
  <data name="gridColumn10.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpMatrix2.Properties.AppearanceDropDownHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtMtrxSprtr3.Size" type="System.Drawing.Size, System.Drawing">
    <value>34, 20</value>
  </data>
  <data name="rep_SLQtyNums.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="txtMtrx3.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gridView3.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMtrxCode0.Location" type="System.Drawing.Point, System.Drawing">
    <value>647, 28</value>
  </data>
  <data name="&gt;&gt;repositoryItemCheckEdit1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_MDCode.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_GenMatrix1.Width" type="System.Int32, mscorlib">
    <value>117</value>
  </data>
  <data name="txtMtrx2.Properties.AppearanceDisabled.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpMatrix3.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="txtMtrxSprtr3.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn10.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl2.Name" xml:space="preserve">
    <value>labelControl2</value>
  </data>
  <data name="&gt;&gt;labelControl1.Parent" xml:space="preserve">
    <value>tab_DefineMatrix</value>
  </data>
  <data name="&gt;&gt;gridColumn7.Name" xml:space="preserve">
    <value>gridColumn7</value>
  </data>
  <data name="xtraTabControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 10</value>
  </data>
  <data name="grd_genItems.EmbeddedNavigator.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>Tile</value>
  </data>
  <data name="repositoryItemSpinEdit3.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtMtrxSprtr2.Size" type="System.Drawing.Size, System.Drawing">
    <value>34, 20</value>
  </data>
  <data name="gridView1.Appearance.FooterPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtMtrxSprtr2.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtParentItemName.Size" type="System.Drawing.Size, System.Drawing">
    <value>177, 20</value>
  </data>
  <data name="gridColumn3.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpMatrix1.Properties.Columns7" xml:space="preserve">
    <value>MatrixId</value>
  </data>
  <data name="&gt;&gt;repositoryItemSpinEdit1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="btn_GenerateItems.Text" xml:space="preserve">
    <value>Generate Items</value>
  </data>
  <data name="col_GenItemName.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMtrxSprtr3.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn9.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtMtrxSprtr3.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtParentItemName.Parent" xml:space="preserve">
    <value>tab_DefineMatrix</value>
  </data>
  <data name="txtMtrxSprtr3.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="gridView3.Appearance.Row.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>64, 64, 64</value>
  </data>
  <data name="&gt;&gt;txtMtrxSprtr3.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;col_MatrixDetailId.Name" xml:space="preserve">
    <value>col_MatrixDetailId</value>
  </data>
  <data name="&gt;&gt;repositoryItemSpinEdit1.Name" xml:space="preserve">
    <value>repositoryItemSpinEdit1</value>
  </data>
  <data name="lkpMatrix3.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn8.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpMatrix2.Properties.AppearanceFocused.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_MatrixId.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtMtrx1.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="grd_Mtrx2.EmbeddedNavigator.AllowHtmlTextInToolTip" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="rep_SLQtyNums.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_MDName.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtMtrx1.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpMatrix2.Properties.Columns8" xml:space="preserve">
    <value>Name47</value>
  </data>
  <data name="col_GenMatrix2.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_available.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;btn_Save.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMtrxSprtr0.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMtrxSprtr0.Size" type="System.Drawing.Size, System.Drawing">
    <value>34, 20</value>
  </data>
  <data name="&gt;&gt;txtMtrxCode0.Name" xml:space="preserve">
    <value>txtMtrxCode0</value>
  </data>
  <data name="col_Serial.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="btn_Save.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 4</value>
  </data>
  <data name="txtMtrx3.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="grd_Mtrx1.EmbeddedNavigator.MaximumSize" type="System.Drawing.Size, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="labelControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtMtrxCode0.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn8.Width" type="System.Int32, mscorlib">
    <value>115</value>
  </data>
  <data name="labelControl3.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="&gt;&gt;col_MDName.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_Serial.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView3.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn1.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpMatrix1.Properties.AppearanceFocused.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpMatrix2.Properties.AppearanceFocused.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn8.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="grd_Mtrx2.EmbeddedNavigator.TextLocation" type="DevExpress.XtraEditors.NavigatorButtonsTextLocation, DevExpress.XtraEditors.v15.1">
    <value>Center</value>
  </data>
  <data name="repositoryItemSpinEdit1.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtMtrx3.Properties.AppearanceDisabled.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>64, 64, 64</value>
  </data>
  <data name="lkpMatrix1.Properties.AppearanceFocused.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn6.Caption" xml:space="preserve">
    <value>ItmMtrxDetailId</value>
  </data>
  <data name="grd_genItems.Location" type="System.Drawing.Point, System.Drawing">
    <value>3, 33</value>
  </data>
  <data name="txtMtrx3.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_GenItemCode.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="rep_SLQtyNums.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="repositoryItemSpinEdit3.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="&gt;&gt;labelControl2.Parent" xml:space="preserve">
    <value>tab_DefineMatrix</value>
  </data>
  <data name="txtMtrxSprtr1.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grd_genItems.EmbeddedNavigator.ToolTipIconType" type="DevExpress.Utils.ToolTipIconType, DevExpress.Utils.v15.1">
    <value>None</value>
  </data>
  <data name="grd_genItems.EmbeddedNavigator.AllowHtmlTextInToolTip" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;btn_GenerateItems.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="txtMtrx3.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txtMtrxSprtr3.Name" xml:space="preserve">
    <value>txtMtrxSprtr3</value>
  </data>
  <data name="col_GenMatrix1.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;labelControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="groupBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>49, 343</value>
  </data>
  <data name="lkpMatrix1.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpMatrix1.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn3.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn10.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpMatrix2.Properties.AppearanceDropDownHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;txtMtrxSprtr2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="col_MDName.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_Serial.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="grd_Mtrx2.EmbeddedNavigator.MaximumSize" type="System.Drawing.Size, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="col_GenMatrix1.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpMatrix3.Properties.AppearanceDropDown.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="rep_SLQtyNums.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;gridColumn8.Name" xml:space="preserve">
    <value>gridColumn8</value>
  </data>
  <data name="labelControl1.Text" xml:space="preserve">
    <value>parent Item</value>
  </data>
  <data name="col_GenItemName.Width" type="System.Int32, mscorlib">
    <value>253</value>
  </data>
  <data name="repositoryItemSpinEdit1.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_MDName.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="groupBox1.Text" xml:space="preserve">
    <value>Item Code &amp; Name Method</value>
  </data>
  <data name="repositoryItemCheckEdit2.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn9.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpMatrix2.Properties.AppearanceFocused.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtMtrxSprtr0.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="repositoryItemSpinEdit3.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpMatrix1.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Center</value>
  </data>
  <data name="repositoryItemSpinEdit2.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtMtrx2.Properties.AppearanceDisabled.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>64, 64, 64</value>
  </data>
  <data name="gridColumn1.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;repositoryItemSpinEdit2.Name" xml:space="preserve">
    <value>repositoryItemSpinEdit2</value>
  </data>
  <data name="&gt;&gt;colDisabled1.Name" xml:space="preserve">
    <value>colDisabled1</value>
  </data>
  <data name="txtMtrxSprtr1.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="repositoryItemSpinEdit1.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpMatrix3.Properties.AppearanceDropDown.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtMtrxSprtr3.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txtMtrx2.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_MatrixId.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtMtrx1.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;col_available.Name" xml:space="preserve">
    <value>col_available</value>
  </data>
  <data name="col_GenMatrix1.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn9.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMtrxSprtr1.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;repositoryItemSpinEdit3.Name" xml:space="preserve">
    <value>repositoryItemSpinEdit3</value>
  </data>
  <data name="&gt;&gt;tab_GeneratedItems.Name" xml:space="preserve">
    <value>tab_GeneratedItems</value>
  </data>
  <data name="grd_Mtrx3.EmbeddedNavigator.BackgroundImage" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpMatrix1.Properties.AppearanceDropDownHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl4.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="gridColumn9.AppearanceHeader.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt</value>
  </data>
  <data name="repositoryItemSpinEdit2.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn10.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_GenMatrix3.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMtrx3.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkpMatrix1.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="gridColumn9.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtMtrx2.Properties.AppearanceDisabled.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpMatrix1.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txtMtrx2.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl3.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="repositoryItemSpinEdit3.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;col_MatrixId.Name" xml:space="preserve">
    <value>col_MatrixId</value>
  </data>
  <data name="&gt;&gt;col_GenMatrix2.Name" xml:space="preserve">
    <value>col_GenMatrix2</value>
  </data>
  <data name="grd_Mtrx3.EmbeddedNavigator.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>Tile</value>
  </data>
  <data name="gv_SalesPerQty.Appearance.FooterPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtMtrxSprtr1.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;col_MDCode.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMtrxSprtr1.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpMatrix1.Properties.AppearanceFocused.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_Serial.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn4.Caption" xml:space="preserve">
    <value>MatrixId</value>
  </data>
  <data name="col_GenItemName.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtMtrxSprtr2.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;repositoryItemCheckEdit1.Name" xml:space="preserve">
    <value>repositoryItemCheckEdit1</value>
  </data>
  <data name="grd_Mtrx1.EmbeddedNavigator.BackgroundImage" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="gridColumn2.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl3.Parent" xml:space="preserve">
    <value>tab_DefineMatrix</value>
  </data>
  <data name="lkpMatrix3.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;gridColumn1.Name" xml:space="preserve">
    <value>gridColumn1</value>
  </data>
  <data name="txtMtrxSprtr3.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="gridColumn9.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="labelControl4.Text" xml:space="preserve">
    <value>Matrix Item Type3</value>
  </data>
  <data name="lkpMatrix1.Properties.AppearanceDropDown.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_MDName.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMtrxSprtr3.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;xtraTabControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtMtrxSprtr1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn2.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtMtrx2.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtMtrx3.Location" type="System.Drawing.Point, System.Drawing">
    <value>59, 28</value>
  </data>
  <data name="gridColumn8.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpMatrix2.Properties.Columns1" xml:space="preserve">
    <value>Matrix Name</value>
  </data>
  <data name="lkpMatrix3.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtMtrxSprtr2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkpMatrix2.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="txtMtrxSprtr0.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpMatrix2.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtMtrxSprtr0.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="txtMtrxCode0.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpMatrix3.Properties.AppearanceDropDownHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="btn_Save.Text" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="&gt;&gt;groupBox1.Parent" xml:space="preserve">
    <value>tab_DefineMatrix</value>
  </data>
  <data name="gridView3.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="labelControl3.Text" xml:space="preserve">
    <value>Matrix Item Type2</value>
  </data>
  <data name="gridColumn4.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_GenMatrix3.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;btn_GenerateItems.Name" xml:space="preserve">
    <value>btn_GenerateItems</value>
  </data>
  <data name="col_MatrixId.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;xtraTabControl1.Name" xml:space="preserve">
    <value>xtraTabControl1</value>
  </data>
  <data name="col_GenItemName.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpMatrix1.Properties.Columns8" xml:space="preserve">
    <value>Name47</value>
  </data>
  <data name="lkpMatrix2.Properties.AppearanceDropDownHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpMatrix3.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtMtrxSprtr1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;tab_GeneratedItems.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="gridColumn4.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpMatrix2.Properties.AppearanceDropDown.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtMtrx2.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="btn_Save.Size" type="System.Drawing.Size, System.Drawing">
    <value>111, 23</value>
  </data>
  <data name="grd_genItems.EmbeddedNavigator.MaximumSize" type="System.Drawing.Size, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="col_GenItemCode.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn10.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn9.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn7.Caption" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="&gt;&gt;txtMtrxSprtr2.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="gridView3.Appearance.FooterPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="grd_genItems.Size" type="System.Drawing.Size, System.Drawing">
    <value>808, 384</value>
  </data>
  <data name="rep_SLQtyNums.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtMtrxSprtr1.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_MDName.Width" type="System.Int32, mscorlib">
    <value>115</value>
  </data>
  <data name="col_MDName.AppearanceHeader.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Navy</value>
  </data>
  <data name="txtMtrxSprtr0.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;lkpMatrix2.Name" xml:space="preserve">
    <value>lkpMatrix2</value>
  </data>
  <data name="labelControl4.Location" type="System.Drawing.Point, System.Drawing">
    <value>197, 54</value>
  </data>
  <data name="txtMtrx3.Properties.AppearanceDisabled.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;grd_genItems.Parent" xml:space="preserve">
    <value>tab_GeneratedItems</value>
  </data>
  <data name="lkpMatrix1.Properties.AppearanceDropDown.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="repositoryItemSpinEdit3.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_GenMatrix2.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtMtrxSprtr2.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtMtrx1.Properties.AppearanceDisabled.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="txtMtrxSprtr3.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;labelControl3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMtrxSprtr2.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn8.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="col_Serial.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtMtrxSprtr1.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtMtrx3.Properties.AppearanceDisabled.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_MDCode.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn8.AppearanceHeader.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt</value>
  </data>
  <data name="grd_Mtrx3.EmbeddedNavigator.TextLocation" type="DevExpress.XtraEditors.NavigatorButtonsTextLocation, DevExpress.XtraEditors.v15.1">
    <value>Center</value>
  </data>
  <data name="lkpMatrix3.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="xtraTabControl1.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="repositoryItemSpinEdit2.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="col_GenItemCode.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMtrxSprtr3.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtMtrxCode0.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="gridColumn7.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="btn_GenerateItems.Size" type="System.Drawing.Size, System.Drawing">
    <value>111, 23</value>
  </data>
  <data name="gridColumn10.Caption" xml:space="preserve">
    <value>MatrixId</value>
  </data>
  <data name="repositoryItemSpinEdit2.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpMatrix2.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_MDCode.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtMtrxSprtr1.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn1.Width" type="System.Int32, mscorlib">
    <value>41</value>
  </data>
  <data name="gridColumn4.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView2.Appearance.FooterPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMtrx3.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpMatrix2.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="col_GenMatrix3.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn8.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView2.Appearance.FooterPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txtMtrxSprtr2.Name" xml:space="preserve">
    <value>txtMtrxSprtr2</value>
  </data>
  <data name="col_MDName.Caption" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="grd_Mtrx1.TabIndex" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="col_GenMatrix2.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;gridColumn3.Name" xml:space="preserve">
    <value>gridColumn3</value>
  </data>
  <data name="gridColumn3.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn10.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_MDName.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;txtMtrxSprtr3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridView2.Name" xml:space="preserve">
    <value>gridView2</value>
  </data>
  <data name="grd_Mtrx3.TabIndex" type="System.Int32, mscorlib">
    <value>22</value>
  </data>
  <data name="grd_genItems.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="lkpMatrix2.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpMatrix2.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="grd_Mtrx3.EmbeddedNavigator.ToolTipIconType" type="DevExpress.Utils.ToolTipIconType, DevExpress.Utils.v15.1">
    <value>None</value>
  </data>
  <data name="&gt;&gt;labelControl2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="tab_DefineMatrix.Size" type="System.Drawing.Size, System.Drawing">
    <value>815, 421</value>
  </data>
  <data name="&gt;&gt;gridView1.Name" xml:space="preserve">
    <value>gridView1</value>
  </data>
  <data name="txtParentItemName.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn3.AppearanceHeader.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt</value>
  </data>
  <data name="col_GenItemName.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="grd_Mtrx2.EmbeddedNavigator.ToolTipTitle" xml:space="preserve">
    <value />
  </data>
  <data name="txtParentItemName.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtMtrxSprtr0.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpMatrix2.Properties.AppearanceDropDownHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMtrx1.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repositoryItemSpinEdit2.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtMtrxSprtr2.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtParentItemName.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtMtrxSprtr1.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtMtrx2.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_MDCode.AppearanceHeader.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt</value>
  </data>
  <data name="lkpMatrix1.Properties.AppearanceDropDown.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;tab_DefineMatrix.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="txtMtrx2.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_MDCode.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;tab_GeneratedItems.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gv_SalesPerQty.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_GenMatrix1.Name" xml:space="preserve">
    <value>col_GenMatrix1</value>
  </data>
  <data name="gridColumn2.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtMtrx1.Size" type="System.Drawing.Size, System.Drawing">
    <value>150, 20</value>
  </data>
  <data name="lkpMatrix3.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="col_MDCode.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtMtrx2.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="gridColumn2.AppearanceHeader.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt</value>
  </data>
  <data name="&gt;&gt;repositoryItemCheckEdit2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="grd_Mtrx1.Location" type="System.Drawing.Point, System.Drawing">
    <value>563, 98</value>
  </data>
  <data name="btn_GenerateItems.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkpMatrix2.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;xtraTabControl1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="grd_Mtrx2.EmbeddedNavigator.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="col_GenMatrix1.Caption" xml:space="preserve">
    <value>Matrix1</value>
  </data>
  <data name="lkpMatrix3.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="gridColumn2.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpMatrix3.Properties.Columns8" xml:space="preserve">
    <value>Name47</value>
  </data>
  <data name="lkpMatrix1.Properties.AppearanceFocused.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpMatrix2.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="grd_Mtrx1.EmbeddedNavigator.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;grd_Mtrx1.Parent" xml:space="preserve">
    <value>tab_DefineMatrix</value>
  </data>
  <data name="lkpMatrix1.Properties.Columns1" xml:space="preserve">
    <value>Matrix Name</value>
  </data>
  <data name="repositoryItemSpinEdit1.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="col_MDCode.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.Appearance.FooterPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;gridColumn4.Name" xml:space="preserve">
    <value>gridColumn4</value>
  </data>
  <data name="txtMtrx2.Size" type="System.Drawing.Size, System.Drawing">
    <value>150, 20</value>
  </data>
  <data name="txtMtrxSprtr2.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtMtrxCode0.Size" type="System.Drawing.Size, System.Drawing">
    <value>98, 20</value>
  </data>
  <data name="tab_GeneratedItems.Size" type="System.Drawing.Size, System.Drawing">
    <value>815, 421</value>
  </data>
  <data name="&gt;&gt;txtMtrx2.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="col_GenItemCode.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtMtrxSprtr0.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;grd_genItems.Name" xml:space="preserve">
    <value>grd_genItems</value>
  </data>
  <data name="txtParentItemName.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txtMtrxSprtr2.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="col_MDName.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;col_Serial.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repositoryItemSpinEdit3.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gridColumn10.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn8.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;gridColumn5.Name" xml:space="preserve">
    <value>gridColumn5</value>
  </data>
  <data name="grd_Mtrx3.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;lkpMatrix3.Name" xml:space="preserve">
    <value>lkpMatrix3</value>
  </data>
  <data name="col_GenMatrix2.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="grd_Mtrx3.EmbeddedNavigator.AllowHtmlTextInToolTip" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="lkpMatrix3.Properties.AppearanceFocused.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn3.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="txtMtrxSprtr3.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;repositoryItemCheckEdit3.Name" xml:space="preserve">
    <value>repositoryItemCheckEdit3</value>
  </data>
  <data name="lkpMatrix1.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="txtMtrxCode0.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txtMtrx3.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="lkpMatrix1.Properties.AppearanceDropDownHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;gridColumn2.Name" xml:space="preserve">
    <value>gridColumn2</value>
  </data>
  <data name="grd_Mtrx2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;repositoryItemSpinEdit2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMtrxSprtr2.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="col_Serial.Width" type="System.Int32, mscorlib">
    <value>57</value>
  </data>
  <data name="&gt;&gt;grd_Mtrx3.Name" xml:space="preserve">
    <value>grd_Mtrx3</value>
  </data>
  <data name="col_MDName.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_MDCode.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtMtrx2.Properties.AppearanceDisabled.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridColumn12.Caption" xml:space="preserve">
    <value>ItmMtrxDetailId</value>
  </data>
  <data name="grd_Mtrx3.Size" type="System.Drawing.Size, System.Drawing">
    <value>236, 239</value>
  </data>
  <data name="&gt;&gt;labelControl3.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="txtParentItemName.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_GenItemName.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;colDisabled3.Name" xml:space="preserve">
    <value>colDisabled3</value>
  </data>
  <data name="col_GenMatrix3.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;repositoryItemSpinEdit3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn9.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn3.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtMtrx1.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn3.AppearanceHeader.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Navy</value>
  </data>
  <data name="txtParentItemName.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="grd_Mtrx2.Size" type="System.Drawing.Size, System.Drawing">
    <value>236, 239</value>
  </data>
  <data name="&gt;&gt;grd_Mtrx3.Parent" xml:space="preserve">
    <value>tab_DefineMatrix</value>
  </data>
  <data name="col_MDName.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtMtrx2.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="grd_genItems.EmbeddedNavigator.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn9.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl2.Text" xml:space="preserve">
    <value>Matrix Item Type1</value>
  </data>
  <data name="col_Serial.Caption" xml:space="preserve">
    <value>#</value>
  </data>
  <data name="&gt;&gt;txtMtrx1.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="&gt;&gt;repositoryItemCheckEdit2.Name" xml:space="preserve">
    <value>repositoryItemCheckEdit2</value>
  </data>
  <data name="&gt;&gt;gridColumn1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn7.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txtMtrxSprtr0.Name" xml:space="preserve">
    <value>txtMtrxSprtr0</value>
  </data>
  <data name="lkpMatrix2.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="gridColumn3.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;xtraTabControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtParentItemName.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtMtrx2.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;col_GenItemCode.Name" xml:space="preserve">
    <value>col_GenItemCode</value>
  </data>
  <data name="&gt;&gt;gridColumn9.Name" xml:space="preserve">
    <value>gridColumn9</value>
  </data>
  <data name="txtMtrxSprtr0.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn4.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtMtrxSprtr3.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txtParentItemName.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>845, 462</value>
  </data>
  <data name="col_GenMatrix1.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpMatrix1.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn3.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="repositoryItemSpinEdit2.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="txtMtrx2.Properties.AppearanceDisabled.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="&gt;&gt;btn_Save.Parent" xml:space="preserve">
    <value>tab_GeneratedItems</value>
  </data>
  <data name="grd_Mtrx2.EmbeddedNavigator.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="txtMtrxCode0.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txtMtrxSprtr0.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repositoryItemSpinEdit3.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="&gt;&gt;lkpMatrix2.Parent" xml:space="preserve">
    <value>tab_DefineMatrix</value>
  </data>
  <data name="grd_Mtrx3.EmbeddedNavigator.ToolTipTitle" xml:space="preserve">
    <value />
  </data>
  <data name="grd_Mtrx3.EmbeddedNavigator.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="grd_Mtrx3.EmbeddedNavigator.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_GenMatrix2.Width" type="System.Int32, mscorlib">
    <value>110</value>
  </data>
  <data name="&gt;&gt;gridColumn6.Name" xml:space="preserve">
    <value>gridColumn6</value>
  </data>
  <data name="&gt;&gt;grd_Mtrx1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.GridControl, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="grd_Mtrx2.TabIndex" type="System.Int32, mscorlib">
    <value>21</value>
  </data>
  <data name="gridView1.Appearance.FooterPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn1.Caption" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="gridColumn2.AppearanceHeader.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Navy</value>
  </data>
  <data name="gridView3.Appearance.FooterPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMtrx3.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;col_MDName.Name" xml:space="preserve">
    <value>col_MDName</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAA
        AAAfbf8AH2z/AB9r/x8fa/+rH2v/9h9r/9AfbP9HH2//AB9u/wAfcP8AAAAAAB9p/wAfZv8AH2D/Ah9j
        /2cfY//iH2P/7R9j/4YfY/8KH2P/AB9j/wAAAAAADw4QAA4NDwAODQ4ADQwORwwMDdAMCw32CwsMqwsL
        DB8MCw0ADAsNAB9u/wAfbf8WH23/qx9t//8fbv//H27//x9u/9gfb/89H3D/AB9y/wAfcf8AH23/AB9n
        /wAfZ/9cH2X/6x9j//8fY///H2P/9x9j/38fY/8GH2P/AB5e8QAPDhAADw4QAA4NDz0NDQ/YDQ0O/w0M
        Dv8MDA3/DAsNqwwLDRYMCw0AH2//FR9v/6Ifb///H3D//x9x//8fcf//H3H//x9x/9Mfcv82IHD/AiBw
        /wQfcP8DH2z/VB9q/+gfZ///H2X//x9j//8fY///H2P/9x9j/3cfY/8FH2P+ABAPEQAQDxE1Dw4Q0w8O
        EP8ODg//Dg0P/w0NDv8MDA3/DAsNogwLDRUfcP+MH3H//R9y//8ec///HnP//x50//8edP//HnP//x5z
        /9cec/+pH3L/qR9x/6wfbv/mH23//x9q//8faP//H2X//x9j//8fY///H2P/8h9j/04bTb8AERASERAQ
        ErsQEBL/EA8R/w8PEf8PDhD/Dg0P/w0ND/8MDA79DAwNjB5z/9QedP//HnX//x52//8edv//Hnb//x52
        //8edv//Hnb//x51//8edP//HnP//x9x//8fb///H23//x9r//8faP//H2X//x9j//8fY///H2P/gxg4
        hAASERM1ERET7hERE/8REBL/EBAS/xAPEf8PDhD/Dg4Q/w0ND/8NDA7THnX/wx52//8ed///Hnj//x54
        //8eef//Hnn//x54//8eeP/9Hnf/8h52//Iedf/zHnP//h9x//8fb///H23//x9q//8fZ///H2T//x9j
        //8fY/90GkGeABISFCYTEhThEhIU/xIRE/8RERP/EBAS/xAPEf8PDhD/Dg4Q/w4ND78ed/9UHnj/5x55
        //8eev//Hnr//x57//8ee///Hnr/+x56/5AeeP88Hnf/PB52/0Eedf+wHnP//x9x//8fb///H2z//x9p
        //8fZv//H2T/zB9j/yQdV9wAExIVAxMTFYMTEhX8ExIU/xISFP8RERP/ERAS/xAPEf8PDhDkDg4QTx50
        /wAeev9dHnv/7R58//8efP//Hn3//x59//0efP+UHnv/DB55/wAed/8AHnb/AB52/x8edf+5HnP//x9x
        //8fbv//H2v//x9o/9YfZf85H2f/AAAAAAAUExYAFBMWDRQTFpQUExX9ExIV/xISFP8SERP/ERAS6hAP
        EVYTDhQAHnr/AB58/wEefP9nHn3/7x5+//8efv/9Hn7/nR59/xAefP8AHnv/AB52/wAedf8AHnX/AB51
        /yUedP2+H3P//x9x//8fbPvbH2r/Px9o/wAfZv8AH2P/ABQTFgAUExYAFBQWEBQUFp0UExb9ExIV/xIS
        FOwRERNfIxkMABAQEQAee/8AHn3/AB5+/wcefv+xHn///x5//+oef/8tHn//AB59/wAefP8AAAAAAB52
        /wAeV/8AGkKGABk+eUYaQoPzG0KG/xo0ZZIbRZkAH2v/AB9o/wAAAAAAFBQWABQUFgAVFBcAFRQXLRUU
        F+oUExb/ExMVqxMTFAUSERQAEBASAAAAAAAegP8AHn//Ax6A/6kegP//HoD/5x6A/ycegP8AHoD/AAAA
        AAAAAAAAAAAAABpCgQAXFRcAFhMSOhcVFvAXFRb/GBUWjhgYHgAcO3gAAAAAAAAAAAAAAAAAFxYZABYV
        GAAWFRgnFRUX5xUUF/8UExajExIVAhQTFgAAAAAAHoD/AB6A/wAegP8IHoD/sx6B//8egf/rHoH/Lx6B
        /wAegv8AHoL/AAAAAAAXFhkAFBQXABcXGgAXFxpIGBca9BgYG/8ZGBuTGRkcABoaHQAbGx4AAAAAABkZ
        HAAYGBsAFxcZABcWGS8WFhjrFRUX/xUUF6wUFBYGFBMWABQTFQAef/8AHoD/Ah6A/20egf/xHoH//x6B
        //4egv+kHoL/Ex6C/wAegv8AFhYYABsZIAAXFxoAFxcaKRgXGsMYGBv/GRkc/xoZHN4bGh1FHBoeABwb
        HwAdHCAAGhkdABkZHAAYGBsTGBcapBcWGf4WFRj/FRQX7xQUFmYTEBMBExMVAB59/wIegP9kHoD/8B6B
        //8egv//HoL//x6C//8egv+cHoL/EB6B/wAaRX4AFxYZABcWGSQXFxq/GBgb/xkZHP8aGR3/Gxoe/xwb
        HtkcGx89HBwfABoYIQAbGh0AGxodEBkZHJwYGBv/Fxca/xYWGf8VFRf/FBQW7RQTFVwBABAAHn//Vx5/
        /+kegP//HoH//x6C//8egv//HoL//x6C//4egf+JHoD/BBgxUQAWFhgSFxYZsRgXGv8ZGBv/Ghkc/xsa
        Hf8cGx7/HBwf/x0cIM8eHSAmHRwgAB0cHwQbGh6JGhkc/hkYG/8YFxr/FxYZ/xYVGP8VFBf/ExMV5hMS
        FFIefv/EHn///x6A//8egf//HoL//x6C//8egv//HoL//x6B/+Megf8nGUB0ABYWGEwXFhn4GBca/xkY
        G/8aGh3/Gxse/xwcH/8dHCD/Hh0h/x4dIXUdHCAAHBsfJxwbHuMaGh3/GRkc/xgXGv8XFhn/FhUY/xUU
        F/8UExX/ExIUwR5+/9Uef///HoD//x6B//8egf//HoL//x6C//8egf//HoH/7h6B/zQaRX4AFhYZWhcW
        GfwYGBv/GRkc/xsaHf8cGx7/HRwg/x4dIf8fHiL/Hx4igx4dIQAdHB80HBsf7hsaHf8aGRz/GBgb/xcX
        Gf8WFRj/FRQX/xQTFf8TEhXSHn7/jR5///0egP//HoD//x6B//8egf//HoH//x6B//8egf+4HoD/EBk3
        YAAWFhkqFxYZ2xgYG/8ZGRz/Gxod/xwbHv8dHCD/Hh0h/x8eIu8gHyNKHx4iABwcHxAcGx64Gxod/xoZ
        HP8YGBv/Fxca/xYVGP8VFBf/FBMV/BMSFYgefv8VHn7/oh5///8egP//HoD//x6B//8egf//HoD/0B6A
        /zIegf8AFxIRABgaHQAXFxpQGBca5hkYHP8aGh3/Gxse/x0cH/8dHSD1Hh0hch8eIwQfHiIAGxseABwb
        HjIaGh3QGRkc/xgXG/8XFhn/FhUY/xUUF/8UExafExIVEx5+/wAefv8XHn7/qx5///8ef///Hn///x6A
        /9UegP85HoD/AB5//wAYIzUAFxcaABgYGwAYFxpXGRgb6hoZHP8bGh7/HBse+B0cH3weHSEFHh0hAB8e
        IgAbGh0AGxodABoZHDkZGBvVGBca/xcWGf8WFRj/FRQXqBQUFhUUExYAHn7/AB5+/wAefv8dHn7/yR5+
        //8efv/yHn//Sh5//wAef/8AHoD/AAAAAAAYFxoAGBcaABUXGgAZGBtUGRkcwxoaHc0bGh5yHBseCB0c
        HwAeHSEAAAAAABsaHQAaGRwAGRgbABgYG0oYFxryFxYZ/xYVGMUVFBcbFRQXABQUFgAefv8AHnz/AB56
        /wIefP+oHn3//x59/+ceff8lHn3/AB5//wAAAAAAAAAAAAAAAAAYGBoAGBgbABgXGgAaGRwNGhkdERkZ
        HAAbGh4AGxoeAAAAAAAAAAAAAAAAABkZHAAXFxoAFxcaJRcWGecWFRj/FRUXohMSFQEVFBcAFRQXAB50
        /wAed/8AHnn/CR56/7Qee///Hnv/6x57/y4eev8AHnj/AB53/wAAAAAAFRQXABYVGAAWFRgAFxYZJBgX
        GnEYFxp6GRgbNBkOEQAYGBsAGBcaAAAAAAAWFhgAFhYYABYWGQAXFhkuFhYY6xUVF/8VFBeuFBQWBxMT
        FQASERQAHnL/AB11/wMedv9zHnf/8x54//8eef/+Hnn/oR55/xEed/8AHnb/ABMSFgAbFx0AFRQXABUV
        Fy4WFhjJFxYZ/xgXGv8YFxreGBcaShcXGgAYFxoAFxYZABYVGAAWFRgAFhYYERYWGKEWFRj+FRQX/xQT
        FvETEhVuEhETAxIREwAgb/8CH3L/aR50//Iedf//Hnb//x53//8ed//+Hnf/lx52/w0edf8AGEOMABQT
        FgAUExYnFRQXxBUVF/8WFhj/FxYZ/xcXGv8XFxrdFxcaQRYWGQAWFRgAFhUYABYVGA0WFRiXFhUY/hUU
        F/8UExb/ExMV/xISFPARERNlEQ4RAh9t/1sfb//sH3H//x9y//8ec///HnT//x50//8edP/9HnT/hx9y
        /wUVKUwAEhIUFBMSFbYUExb/FBQW/xERE/8QEBL/FhUY/xYWGf8XFhnXFhYYYBYVGE4WFRhOFhUYmBUV
        F/wVFBf/FBMW/xMTFf8SEhT/ERET/xEQEuoQDxFXH2v/xh9s//8fbv//H2///x9w//8fcf//H3H//x9x
        //8fcf/kH3D/KBc3cQASERNOEhIU+BMSFf8SERT/DAwN/woJC/8QDxH/FRUX/xYVGP8WFRj7FRUX+BUU
        F/gVFBf/FBQW/xQTFv8TEhX/EhIU/xIRE/8REBL/EA8R/w8PEMMfZ//UH2n//x9r//8fbP//H23//x9u
        //8fbv//H27//x9u/+4fbv80Fzl6ABEQE1oRERP8EhIU/xERE/8ODg//DAwN/xAPEf8UExb/FBQW/xQU
        Fv8UFBb/FBMW/xQTFv8TExX/ExIU/xISFP8SERP/ERAS/xAQEv8PDxH/Dg4Q0R9l/4kfZf/8H2f//x9o
        //8faf//H2r//x9q//8fav//H2v/sh9r/w4VKlcAEBASKRAQEtoRERP/EhET/xEQEv8QEBL/EhIU/xMT
        Ff8TExXyExMVsxMTFagTEhWoExIU1BISFP8SERP/ERET/xEQEv8QDxH/Dw8R/w4OEPwODQ+EH2P/Ex9j
        /6AfZP//H2T//x9l//8fZv//H2b//x9n/8gfaP8qH2j/AA0CAAAREBIAEA8RThAQEuUREBL/ERET/xIR
        E/8SERP/EhEU9hISFHYTExUIEhEUAxMRFQISERMxERETzREQEv8QEBL/EA8R/w8OEP8ODhD/Dg0Pnw0N
        DxIfY/8AH2P/Fh9j/6kfY///H2P//x9j//8fY//MH2T/MB9m/wAfVP8AERgnABAPEQAQERMAEA8RVRAP
        EegQEBL/EBAS/xEQEvcRERN9EhETBhISFAATEhUAExIVABEREwAQEBI2EA8R0g8PEf8PDhD/Dg4P/w4N
        D6gNDQ8VDg0PAB9j/wAfY/8AH2P/HR9j/6QfY//nH2P/wh9j/zofZP8AH2P/AB9m/wAAAAAAEA8RAA8P
        EQAKDhIBDw8QYA8PEdcQDxHiEA8RgRAPEQkREBIAEhETAAAAAAARERMAEA8RABAPEQAPDhBADg4Qxg4N
        D+cODQ+iDQ0OHA0NDwAODQ8AACAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAGAIBw
        DgEAIAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAQAAHAOAAAgBAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgBAA=
</value>
  </data>
  <data name="lkpMatrix3.Properties.AppearanceDropDownHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;grd_Mtrx2.Parent" xml:space="preserve">
    <value>tab_DefineMatrix</value>
  </data>
  <data name="txtMtrxCode0.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtMtrxCode0.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="txtMtrxSprtr2.Location" type="System.Drawing.Point, System.Drawing">
    <value>215, 28</value>
  </data>
  <data name="&gt;&gt;gridColumn11.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repositoryItemSpinEdit1.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtMtrx2.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="grd_Mtrx1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;txtParentItemName.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="txtMtrx1.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lkpMatrix3.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="&gt;&gt;lkpMatrix1.Parent" xml:space="preserve">
    <value>tab_DefineMatrix</value>
  </data>
  <data name="gridColumn2.Width" type="System.Int32, mscorlib">
    <value>115</value>
  </data>
  <data name="txtMtrxSprtr0.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_SLQtyNums.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_GenMatrix3.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_GenMatrix2.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;labelControl4.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="col_GenItemCode.Caption" xml:space="preserve">
    <value>Item Code</value>
  </data>
  <data name="lkpMatrix3.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_MatrixId.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtMtrx3.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpMatrix3.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkpMatrix3.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="txtMtrxCode0.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="grd_Mtrx1.EmbeddedNavigator.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grd_Mtrx1.Size" type="System.Drawing.Size, System.Drawing">
    <value>236, 239</value>
  </data>
  <data name="col_available.Width" type="System.Int32, mscorlib">
    <value>41</value>
  </data>
  <data name="lkpMatrix1.Properties.AppearanceDropDownHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtMtrx3.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txtMtrx1.Name" xml:space="preserve">
    <value>txtMtrx1</value>
  </data>
  <data name="&gt;&gt;col_ItmMtrxDetailId.Name" xml:space="preserve">
    <value>col_ItmMtrxDetailId</value>
  </data>
  <data name="&gt;&gt;tab_DefineMatrix.Type" xml:space="preserve">
    <value>DevExpress.XtraTab.XtraTabPage, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repositoryItemSpinEdit2.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="groupBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>751, 67</value>
  </data>
  <data name="repositoryItemCheckEdit1.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtMtrxSprtr1.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="gridColumn8.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtMtrxSprtr1.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_SLQtyNums.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn4.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_GenItemName.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpMatrix2.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpMatrix3.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="gv_SalesPerQty.Appearance.FooterPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grd_Mtrx1.EmbeddedNavigator.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="col_MDName.AppearanceHeader.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt</value>
  </data>
  <data name="txtMtrx1.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn4.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMtrx2.Enabled" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtMtrx2.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="col_GenMatrix3.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtMtrxSprtr0.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpMatrix3.Properties.AppearanceFocused.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="grd_genItems.EmbeddedNavigator.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>Inherit</value>
  </data>
  <data name="&gt;&gt;gridColumn3.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMtrx1.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpMatrix1.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Center</value>
  </data>
  <data name="col_GenItemCode.AppearanceHeader.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Navy</value>
  </data>
  <data name="lkpMatrix3.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMtrxCode0.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridColumn10.Width" type="System.Int32, mscorlib">
    <value>246</value>
  </data>
  <data name="txtMtrx3.Properties.AppearanceDisabled.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;txtMtrx2.Name" xml:space="preserve">
    <value>txtMtrx2</value>
  </data>
  <data name="gridColumn4.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtMtrx1.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkpMatrix1.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="col_MatrixId.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtParentItemName.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;col_GenMatrix1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpMatrix2.Properties.Columns" xml:space="preserve">
    <value>MatrixName</value>
  </data>
  <data name="txtMtrx2.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtMtrxCode0.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn3.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpMatrix2.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtParentItemName.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMtrxSprtr3.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpMatrix1.Properties.AppearanceDropDown.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpMatrix2.Properties.AppearanceDropDown.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtMtrx2.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="grd_genItems.EmbeddedNavigator.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="btn_Save.TabIndex" type="System.Int32, mscorlib">
    <value>25</value>
  </data>
  <data name="gridColumn9.AppearanceHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;txtMtrxSprtr1.Name" xml:space="preserve">
    <value>txtMtrxSprtr1</value>
  </data>
  <data name="txtMtrx1.Properties.AppearanceDisabled.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="xtraTabControl1.AppearancePage.Header.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="repositoryItemSpinEdit1.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="gridColumn2.AppearanceHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;gridColumn12.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMtrx1.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_GenItemCode.VisibleIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="txtParentItemName.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;col_GenItemName.Name" xml:space="preserve">
    <value>col_GenItemName</value>
  </data>
  <data name="lkpMatrix3.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn3.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpMatrix1.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="btn_GenerateItems.Location" type="System.Drawing.Point, System.Drawing">
    <value>700, 4</value>
  </data>
  <data name="rep_SLQtyNums.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridView3.Appearance.FooterPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl2.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="lkpMatrix2.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="grd_Mtrx3.EmbeddedNavigator.MaximumSize" type="System.Drawing.Size, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="lkpMatrix3.Properties.AppearanceDropDown.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;lkpMatrix1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpMatrix2.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="grd_Mtrx2.EmbeddedNavigator.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_GenItemCode.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;lkpMatrix2.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="lkpMatrix2.Properties.AppearanceFocused.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl4.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="&gt;&gt;repositoryItemCheckEdit4.Name" xml:space="preserve">
    <value>repositoryItemCheckEdit4</value>
  </data>
  <data name="lkpMatrix1.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtMtrxSprtr1.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn8.AppearanceHeader.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Navy</value>
  </data>
  <data name="&gt;&gt;repositoryItemCheckEdit4.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpMatrix1.Properties.AppearanceDropDownHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridColumn10.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;col_MatrixId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMtrxSprtr0.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="grd_Mtrx2.EmbeddedNavigator.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>Inherit</value>
  </data>
  <data name="txtMtrxCode0.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;gridColumn7.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtParentItemName.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtMtrxSprtr2.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtMtrxSprtr3.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtMtrxCode0.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="col_GenItemName.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="grd_Mtrx1.EmbeddedNavigator.ToolTipTitle" xml:space="preserve">
    <value />
  </data>
  <data name="txtMtrx2.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn5.Caption" xml:space="preserve">
    <value>MatrixDetailId</value>
  </data>
  <data name="&gt;&gt;lkpMatrix3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMtrxSprtr2.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtMtrx2.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;btn_GenerateItems.Parent" xml:space="preserve">
    <value>tab_GeneratedItems</value>
  </data>
  <data name="lkpMatrix1.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="labelControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>712, 54</value>
  </data>
  <data name="gridColumn8.AppearanceCell.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="groupBox1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="repositoryItemSpinEdit2.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="gridColumn8.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;gridColumn4.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repositoryItemSpinEdit1.Mask.EditMask" xml:space="preserve">
    <value>f3</value>
  </data>
  <data name="repositoryItemCheckEdit4.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;lkpMatrix1.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="txtParentItemName.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lkpMatrix1.Location" type="System.Drawing.Point, System.Drawing">
    <value>563, 73</value>
  </data>
  <data name="&gt;&gt;txtMtrx2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="grd_Mtrx3.EmbeddedNavigator.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMtrxSprtr0.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;grd_Mtrx1.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="col_GenItemName.AppearanceHeader.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8.25pt</value>
  </data>
  <data name="grd_Mtrx2.EmbeddedNavigator.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;txtMtrxSprtr0.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMtrxSprtr0.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn3.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridColumn9.AppearanceCell.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtMtrxSprtr2.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="col_GenItemCode.Width" type="System.Int32, mscorlib">
    <value>150</value>
  </data>
  <data name="&gt;&gt;col_MDCode.Name" xml:space="preserve">
    <value>col_MDCode</value>
  </data>
  <data name="col_Serial.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gv_SalesPerQty.Appearance.FooterPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpMatrix2.Properties.AppearanceDropDown.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMtrx3.Properties.AppearanceDisabled.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_GenItemName.AppearanceHeader.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Navy</value>
  </data>
  <data name="col_GenItemCode.AppearanceCell.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpMatrix3.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="xtraTabControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>821, 449</value>
  </data>
  <data name="xtraTabControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="lkpMatrix2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="col_MatrixId.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMtrxSprtr2.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txtMtrxSprtr1.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="gridView3.Appearance.FooterPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpMatrix2.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtMtrx2.Properties.AppearanceDisabled.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txtMtrxCode0.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtMtrxSprtr0.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="grd_genItems.EmbeddedNavigator.TextLocation" type="DevExpress.XtraEditors.NavigatorButtonsTextLocation, DevExpress.XtraEditors.v15.1">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;gridColumn2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpMatrix3.Properties.AppearanceFocused.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpMatrix2.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="txtMtrxSprtr3.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtMtrx3.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMtrx3.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txtParentItemName.Name" xml:space="preserve">
    <value>txtParentItemName</value>
  </data>
  <data name="&gt;&gt;colDisabled3.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMtrxCode0.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txtMtrx1.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txtMtrxSprtr1.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="txtMtrxCode0.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="txtMtrxSprtr2.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtMtrxSprtr1.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="col_GenMatrix1.AppearanceHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grd_Mtrx2.EmbeddedNavigator.ToolTipIconType" type="DevExpress.Utils.ToolTipIconType, DevExpress.Utils.v15.1">
    <value>None</value>
  </data>
  <data name="gridColumn10.AppearanceHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txtParentItemName.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="col_GenMatrix2.AppearanceCell.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="repositoryItemSpinEdit1.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>Numeric</value>
  </data>
  <data name="&gt;&gt;gridColumn5.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txtMtrxSprtr3.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="xtraTabControl1.AppearancePage.Header.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpMatrix1.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtMtrx1.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtMtrx3.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_Serial.VisibleIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="&gt;&gt;lkpMatrix3.Parent" xml:space="preserve">
    <value>tab_DefineMatrix</value>
  </data>
  <data name="lkpMatrix2.Location" type="System.Drawing.Point, System.Drawing">
    <value>307, 73</value>
  </data>
  <data name="col_MDCode.Caption" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="&gt;&gt;grd_genItems.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="lkpMatrix3.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;grd_Mtrx3.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.GridControl, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.Language" type="System.Globalization.CultureInfo, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>ar-EG</value>
  </metadata>
</root>