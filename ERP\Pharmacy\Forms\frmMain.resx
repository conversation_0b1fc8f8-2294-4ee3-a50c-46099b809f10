﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="mi_ST_CompInfo.Size" type="System.Drawing.Size, System.Drawing">
    <value>171, 22</value>
  </data>
  <data name="rdoStyles.Properties.Items42" xml:space="preserve">
    <value>McSkin</value>
  </data>
  <data name="btnClosePopup.Text" xml:space="preserve">
    <value>X</value>
  </data>
  <data name="&gt;&gt;miExit.Name" xml:space="preserve">
    <value>miExit</value>
  </data>
  <data name="pnlLarge.Size" type="System.Drawing.Size, System.Drawing">
    <value>879, 539</value>
  </data>
  <data name="mi_UOM.Text" xml:space="preserve">
    <value>Units Of Measure</value>
  </data>
  <data name="&gt;&gt;mi_SL_Customer.Name" xml:space="preserve">
    <value>mi_SL_Customer</value>
  </data>
  <data name="profitabilityOfItemsToolStripMenuItem.Text" xml:space="preserve">
    <value>Profitability of Items</value>
  </data>
  <data name="&gt;&gt;NBG_Sales.Name" xml:space="preserve">
    <value>NBG_Sales</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator9.Name" xml:space="preserve">
    <value>toolStripSeparator9</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="picLogo.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;mi_DT_Sep3.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mi_SL_InvoiceList.Name" xml:space="preserve">
    <value>mi_SL_InvoiceList</value>
  </data>
  <data name="&gt;&gt;navBarControl1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="barDockControlBottom.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator8.Name" xml:space="preserve">
    <value>toolStripSeparator8</value>
  </data>
  <data name="&gt;&gt;CTA_Twitter.Name" xml:space="preserve">
    <value>CTA_Twitter</value>
  </data>
  <data name="&gt;&gt;bar3.Name" xml:space="preserve">
    <value>bar3</value>
  </data>
  <data name="mi_PreInvoice.Text" xml:space="preserve">
    <value>Pre Invoice</value>
  </data>
  <data name="barDockControlRight.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="mi_SL_Customer.Size" type="System.Drawing.Size, System.Drawing">
    <value>168, 22</value>
  </data>
  <data name="&gt;&gt;topCategoriesOfItemsSoldToolStripMenuItem.Name" xml:space="preserve">
    <value>topCategoriesOfItemsSoldToolStripMenuItem</value>
  </data>
  <data name="toolStripSeparator7.Size" type="System.Drawing.Size, System.Drawing">
    <value>199, 6</value>
  </data>
  <data name="&gt;&gt;mi_ShowHideChart.Name" xml:space="preserve">
    <value>mi_ShowHideChart</value>
  </data>
  <data name="barDockControlTop.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="&gt;&gt;profitabilityOfItemsToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="releaseNotesToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>195, 22</value>
  </data>
  <data name="&gt;&gt;mi_DT_Sep1.Name" xml:space="preserve">
    <value>mi_DT_Sep1</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="rdoStyles.Properties.Items61" xml:space="preserve">
    <value>Xmas Blue</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="barDockControlRight.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;mi_Item.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="E_taxableTypes.Size" type="System.Drawing.Size, System.Drawing">
    <value>202, 22</value>
  </data>
  <data name="&gt;&gt;mi_Sales.Name" xml:space="preserve">
    <value>mi_Sales</value>
  </data>
  <data name="&gt;&gt;itemsSalesToEachDelegateToolStripMenuItem.Name" xml:space="preserve">
    <value>itemsSalesToEachDelegateToolStripMenuItem</value>
  </data>
  <data name="mi_GlobalGrid.Text" xml:space="preserve">
    <value>LinkIT on the Web</value>
  </data>
  <data name="scr_Currency.Text" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="&gt;&gt;mi_reportCenter.Name" xml:space="preserve">
    <value>mi_reportCenter</value>
  </data>
  <data name="salesRateToolStripMenuItem.Text" xml:space="preserve">
    <value>Sales Rate</value>
  </data>
  <data name="mi_globalERPRegistration.Size" type="System.Drawing.Size, System.Drawing">
    <value>195, 22</value>
  </data>
  <data name="&gt;&gt;salesDelegatesForItemsToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="profitabilityOfItemsToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>244, 22</value>
  </data>
  <data name="taxSubTypesToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>202, 22</value>
  </data>
  <data name="salesRateToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>244, 22</value>
  </data>
  <data name="pnlLarge.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="&gt;&gt;customeAccountsListStatisticsToolStripMenuItem.Name" xml:space="preserve">
    <value>customeAccountsListStatisticsToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;salesRateToolStripMenuItem.Name" xml:space="preserve">
    <value>salesRateToolStripMenuItem</value>
  </data>
  <data name="rdoStyles.Properties.Items13" xml:space="preserve">
    <value>Blue</value>
  </data>
  <data name="rdoStyles.Properties.Items23" xml:space="preserve">
    <value>The Asphalt World</value>
  </data>
  <data name="rdoStyles.Properties.Items33" xml:space="preserve">
    <value>Foggy</value>
  </data>
  <data name="rdoStyles.Properties.Items43" xml:space="preserve">
    <value>McSkin</value>
  </data>
  <data name="&gt;&gt;mi_settings.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator1.Name" xml:space="preserve">
    <value>toolStripSeparator1</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="customeAccountsListStatisticsToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>244, 22</value>
  </data>
  <data name="panelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>1174, 541</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Name" xml:space="preserve">
    <value>barDockControlBottom</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator8.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;salesRateToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;salesDelegatesForItemsToolStripMenuItem.Name" xml:space="preserve">
    <value>salesDelegatesForItemsToolStripMenuItem</value>
  </data>
  <data name="pnlImage.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;mi_Styles.Name" xml:space="preserve">
    <value>mi_Styles</value>
  </data>
  <data name="&gt;&gt;picLogo.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;CTA_Linkitsys.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarLinkContainerItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl1.TabIndex" type="System.Int32, mscorlib">
    <value>61</value>
  </data>
  <data name="toolStripSeparator31.Size" type="System.Drawing.Size, System.Drawing">
    <value>187, 6</value>
  </data>
  <data name="&gt;&gt;rdoStyles.Name" xml:space="preserve">
    <value>rdoStyles</value>
  </data>
  <data name="&gt;&gt;popupStyle.Parent" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="src_Countries.Size" type="System.Drawing.Size, System.Drawing">
    <value>202, 22</value>
  </data>
  <data name="mi_data.Text" xml:space="preserve">
    <value>Data</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator32.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="labelControl1.Text" xml:space="preserve">
    <value>Styles</value>
  </data>
  <data name="&gt;&gt;topSellingCustomerCategoriesToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="picLogo.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="miExit.Text" xml:space="preserve">
    <value>Exit</value>
  </data>
  <data name="&gt;&gt;CTA_LinkedIn.Name" xml:space="preserve">
    <value>CTA_LinkedIn</value>
  </data>
  <data name="&gt;&gt;btnClosePopup.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barDockControlBottom.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 565</value>
  </data>
  <data name="mi_Windows.Text" xml:space="preserve">
    <value>Windo&amp;ws</value>
  </data>
  <data name="rdoStyles.Properties.Items" xml:space="preserve">
    <value>Flat</value>
  </data>
  <data name="mi_ImportExcelFiles.Text" xml:space="preserve">
    <value>Import Excel File</value>
  </data>
  <data name="mi_ST_CompInfo.Text" xml:space="preserve">
    <value>Company Info</value>
  </data>
  <data name="customeAccountsListStatisticsToolStripMenuItem.Text" xml:space="preserve">
    <value>Custome Accounts List statistics</value>
  </data>
  <data name="barDockControlRight.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;navBarControl1.Name" xml:space="preserve">
    <value>navBarControl1</value>
  </data>
  <data name="mi_SL_Customer.Text" xml:space="preserve">
    <value>Customers</value>
  </data>
  <data name="pnlSmall.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="الاعداداتToolStripMenuItem.Text" xml:space="preserve">
    <value>Settings</value>
  </data>
  <data name="pnlLarge.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 0</value>
  </data>
  <data name="E_storesCodes.Size" type="System.Drawing.Size, System.Drawing">
    <value>202, 22</value>
  </data>
  <data name="&gt;&gt;pnlSmall.Name" xml:space="preserve">
    <value>pnlSmall</value>
  </data>
  <data name="&gt;&gt;pnlSmall.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="bar3.Text" xml:space="preserve">
    <value>Status bar</value>
  </data>
  <data name="&gt;&gt;mi_envoice.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="barDockControlRight.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 565</value>
  </data>
  <data name="&gt;&gt;NBI_Store.Type" xml:space="preserve">
    <value>DevExpress.XtraNavBar.NavBarItem, DevExpress.XtraNavBar.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;scr_Currency.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="rdoStyles.Properties.Items53" xml:space="preserve">
    <value>Sharp Plus</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="pnlSmall.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;NBI_Styles.Type" xml:space="preserve">
    <value>DevExpress.XtraNavBar.NavBarItem, DevExpress.XtraNavBar.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="mi_SL_Return.Text" xml:space="preserve">
    <value>New Credit Note</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>1174, 609</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator7.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;eInvoicesUnsyncToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlLarge.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="CTA_Twitter.Caption" xml:space="preserve">
    <value>Twitter</value>
  </data>
  <data name="&gt;&gt;NBI_SL_Invoice.Name" xml:space="preserve">
    <value>NBI_SL_Invoice</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator6.Name" xml:space="preserve">
    <value>toolStripSeparator6</value>
  </data>
  <data name="&gt;&gt;Scr_UnitOfMeaure.Name" xml:space="preserve">
    <value>Scr_UnitOfMeaure</value>
  </data>
  <data name="toolStripSeparator3.AutoSize" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="pnlImage.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="frmMain.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;mi_SL_Sep1Ret.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="barDockControlTop.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="toolStripSeparator6.Size" type="System.Drawing.Size, System.Drawing">
    <value>168, 1</value>
  </data>
  <data name="&gt;&gt;labelControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="rdoStyles.Properties.Items20" xml:space="preserve">
    <value>Money Twins</value>
  </data>
  <data name="&gt;&gt;pnlLarge.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.PanelControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barDockControlBottom.Size" type="System.Drawing.Size, System.Drawing">
    <value>1174, 44</value>
  </data>
  <data name="rdoStyles.Properties.Items50" xml:space="preserve">
    <value>Sharp</value>
  </data>
  <data name="rdoStyles.Properties.Items14" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="toolStripSeparator1.Size" type="System.Drawing.Size, System.Drawing">
    <value>162, 1</value>
  </data>
  <data name="&gt;&gt;mi_data.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;E_activityTypes.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;NBI_Store.Name" xml:space="preserve">
    <value>NBI_Store</value>
  </data>
  <data name="mi_data.Size" type="System.Drawing.Size, System.Drawing">
    <value>43, 20</value>
  </data>
  <data name="&gt;&gt;labelControl1.Parent" xml:space="preserve">
    <value>popupStyle</value>
  </data>
  <data name="&gt;&gt;mi_SL_Invoice.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="popupStyle.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="rdoStyles.Properties.Items22" xml:space="preserve">
    <value>The Asphalt World</value>
  </data>
  <data name="mi_Help.Size" type="System.Drawing.Size, System.Drawing">
    <value>44, 20</value>
  </data>
  <data name="navBarControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>220, 537</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator7.Name" xml:space="preserve">
    <value>toolStripSeparator7</value>
  </data>
  <data name="mi_GlobalGrid.Size" type="System.Drawing.Size, System.Drawing">
    <value>195, 22</value>
  </data>
  <data name="&gt;&gt;btnClosePopup.Parent" xml:space="preserve">
    <value>popupStyle</value>
  </data>
  <data name="mi_SL_AddList.Text" xml:space="preserve">
    <value>Debit Notes List</value>
  </data>
  <data name="&gt;&gt;barManager1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarManager, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;E_activityTypes.Name" xml:space="preserve">
    <value>E_activityTypes</value>
  </data>
  <data name="NBG_Sales.Caption" xml:space="preserve">
    <value>Sales</value>
  </data>
  <data name="&gt;&gt;barStaticItem1.Name" xml:space="preserve">
    <value>barStaticItem1</value>
  </data>
  <data name="CTA_Linkitsys.Caption" xml:space="preserve">
    <value>www.linkitsys.com</value>
  </data>
  <data name="eInvoicesUnsyncToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>202, 22</value>
  </data>
  <data name="barDockControlBottom.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;salesProfitabilityToolStripMenuItem.Name" xml:space="preserve">
    <value>salesProfitabilityToolStripMenuItem</value>
  </data>
  <data name="barDockControlTop.Size" type="System.Drawing.Size, System.Drawing">
    <value>1174, 0</value>
  </data>
  <data name="mi_UOM.Size" type="System.Drawing.Size, System.Drawing">
    <value>165, 22</value>
  </data>
  <data name="mi_SL_Sep1Ret.Size" type="System.Drawing.Size, System.Drawing">
    <value>165, 6</value>
  </data>
  <data name="rdoStyles.Properties.Items60" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;rdoStyles.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.RadioGroup, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="btnClosePopup.TabIndex" type="System.Int32, mscorlib">
    <value>62</value>
  </data>
  <data name="NBI_Styles.Caption" xml:space="preserve">
    <value>Styles</value>
  </data>
  <data name="الاعداداتToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>171, 22</value>
  </data>
  <data name="&gt;&gt;CTA_Facebook.Name" xml:space="preserve">
    <value>CTA_Facebook</value>
  </data>
  <data name="&gt;&gt;NBI_SL_Return.Type" xml:space="preserve">
    <value>DevExpress.XtraNavBar.NavBarItem, DevExpress.XtraNavBar.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="mi_About.Size" type="System.Drawing.Size, System.Drawing">
    <value>195, 22</value>
  </data>
  <data name="navBarControl1.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="barUser.Caption" xml:space="preserve">
    <value>User Name</value>
  </data>
  <data name="&gt;&gt;mi_Item.Name" xml:space="preserve">
    <value>mi_Item</value>
  </data>
  <data name="&gt;&gt;mi_DT_Sep3.Name" xml:space="preserve">
    <value>mi_DT_Sep3</value>
  </data>
  <data name="&gt;&gt;mi_Styles.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mi_Help.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="CTA_LinkedIn.Caption" xml:space="preserve">
    <value>LinkedIn</value>
  </data>
  <data name="&gt;&gt;mi_UnSyncAddNotes.Name" xml:space="preserve">
    <value>mi_UnSyncAddNotes</value>
  </data>
  <data name="picLogo.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="frmMain.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>224, 224, 224</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator4.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="barDockControlBottom.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;navBarControl1.Parent" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barDockControlLeft.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="&gt;&gt;mi_Store.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mi_SL_Sep5IndSl.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="popupStyle.Size" type="System.Drawing.Size, System.Drawing">
    <value>292, 419</value>
  </data>
  <data name="&gt;&gt;mi_Help.Name" xml:space="preserve">
    <value>mi_Help</value>
  </data>
  <data name="Scr_UnitOfMeaure.Text" xml:space="preserve">
    <value>Unit Of Measure</value>
  </data>
  <data name="&gt;&gt;profitabilityOfItemsToolStripMenuItem.Name" xml:space="preserve">
    <value>profitabilityOfItemsToolStripMenuItem</value>
  </data>
  <data name="Scr_UnitOfMeaure.Size" type="System.Drawing.Size, System.Drawing">
    <value>202, 22</value>
  </data>
  <data name="&gt;&gt;NBI_SL_Return.Name" xml:space="preserve">
    <value>NBI_SL_Return</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator3.Name" xml:space="preserve">
    <value>toolStripSeparator3</value>
  </data>
  <data name="&gt;&gt;mi_ST_SP_1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="rdoStyles.Properties.Items37" xml:space="preserve">
    <value>High Contrast</value>
  </data>
  <data name="&gt;&gt;popupStyle.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.PopupContainerControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barDockControlLeft.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 565</value>
  </data>
  <data name="pnlSmall.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 138</value>
  </data>
  <data name="barStaticItem1.Caption" xml:space="preserve">
    <value>Follow us on:</value>
  </data>
  <data name="pnlImage.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;mi_reportCenter.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="mi_Windows.Size" type="System.Drawing.Size, System.Drawing">
    <value>68, 20</value>
  </data>
  <data name="CTA_Linkitsys.MenuAppearance.HeaderItemAppearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;mi_Store.Name" xml:space="preserve">
    <value>mi_Store</value>
  </data>
  <data name="&gt;&gt;mi_SL_Add.Name" xml:space="preserve">
    <value>mi_SL_Add</value>
  </data>
  <data name="pnlLarge.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;mi_ChangePass.Name" xml:space="preserve">
    <value>mi_ChangePass</value>
  </data>
  <data name="NBI_Item.Caption" xml:space="preserve">
    <value>Items</value>
  </data>
  <data name="&gt;&gt;E_taxableTypes.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="picLogo.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;pnlSmall.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.PanelControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;mi_ImportExcelFiles.Name" xml:space="preserve">
    <value>mi_ImportExcelFiles</value>
  </data>
  <data name="pnlImage.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="mi_DT_Sep3.Size" type="System.Drawing.Size, System.Drawing">
    <value>162, 6</value>
  </data>
  <data name="NBI_SL_Customer.Caption" xml:space="preserve">
    <value>Customers</value>
  </data>
  <data name="mi_reportCenter.Text" xml:space="preserve">
    <value>Reports Center</value>
  </data>
  <data name="&gt;&gt;الاعداداتToolStripMenuItem.Name" xml:space="preserve">
    <value>الاعداداتToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;mi_PreInvoice.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="recievedDocuments.Size" type="System.Drawing.Size, System.Drawing">
    <value>202, 22</value>
  </data>
  <data name="rdoStyles.Properties.Items24" xml:space="preserve">
    <value>Coffee</value>
  </data>
  <data name="rdoStyles.Properties.Items34" xml:space="preserve">
    <value>Glass Oceans</value>
  </data>
  <data name="mi_settings.Size" type="System.Drawing.Size, System.Drawing">
    <value>61, 20</value>
  </data>
  <data name="rdoStyles.Properties.Items54" xml:space="preserve">
    <value>Springtime</value>
  </data>
  <data name="picLogo.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="&gt;&gt;mi_SL_Return.Name" xml:space="preserve">
    <value>mi_SL_Return</value>
  </data>
  <data name="pnlLarge.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="$this.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="mi_SL_Add.Text" xml:space="preserve">
    <value>New Debit Note</value>
  </data>
  <data name="pnlSmall.Size" type="System.Drawing.Size, System.Drawing">
    <value>875, 488</value>
  </data>
  <data name="&gt;&gt;mi_SL_ReturnList.Name" xml:space="preserve">
    <value>mi_SL_ReturnList</value>
  </data>
  <data name="picLogo.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barDockControlTop.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="mi_DT_Sep1.Size" type="System.Drawing.Size, System.Drawing">
    <value>162, 6</value>
  </data>
  <data name="&gt;&gt;barUserName.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarStaticItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="rdoStyles.Size" type="System.Drawing.Size, System.Drawing">
    <value>276, 387</value>
  </data>
  <data name="&gt;&gt;mi_ImportExcelFiles.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mi_Windows.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="mi_Styles.Text" xml:space="preserve">
    <value>Styles</value>
  </data>
  <data name="&gt;&gt;ribbonPage1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Ribbon.RibbonPage, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;mi_ShowHideChart.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;recievedDocuments.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator31.Name" xml:space="preserve">
    <value>toolStripSeparator31</value>
  </data>
  <data name="&gt;&gt;NBG_Settings.Name" xml:space="preserve">
    <value>NBG_Settings</value>
  </data>
  <data name="mi_SL_ReturnList.Text" xml:space="preserve">
    <value>Credit Notes List</value>
  </data>
  <data name="&gt;&gt;ribbonPageGroup1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Ribbon.RibbonPageGroup, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="navBarControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>2, 2</value>
  </data>
  <data name="rdoStyles.Properties.Items39" xml:space="preserve">
    <value>Liquid Sky</value>
  </data>
  <data name="pnlSmall.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="mi_globalERPRegistration.Text" xml:space="preserve">
    <value>LinkIT ERP Registration</value>
  </data>
  <data name="rdoStyles.Properties.Items18" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="rdoStyles.Properties.Items28" xml:space="preserve">
    <value>Darkroom</value>
  </data>
  <data name="rdoStyles.Properties.Items38" xml:space="preserve">
    <value>Liquid Sky</value>
  </data>
  <data name="rdoStyles.Properties.Items48" xml:space="preserve">
    <value>Seven Classic</value>
  </data>
  <data name="rdoStyles.Properties.Items58" xml:space="preserve">
    <value>Summer 2008</value>
  </data>
  <data name="pnlSmall.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="&gt;&gt;menuStrip1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="frmMain.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="toolStripSeparator2.Size" type="System.Drawing.Size, System.Drawing">
    <value>199, 6</value>
  </data>
  <data name="pnlSmall.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="mi_Sales.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 20</value>
  </data>
  <data name="labelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>207, 10</value>
  </data>
  <data name="&gt;&gt;mi_E_InvoiceSettings.Name" xml:space="preserve">
    <value>mi_E_InvoiceSettings</value>
  </data>
  <data name="pnlLarge.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="pnlLarge.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;menuStrip1.Name" xml:space="preserve">
    <value>menuStrip1</value>
  </data>
  <data name="&gt;&gt;pnlLarge.Name" xml:space="preserve">
    <value>pnlLarge</value>
  </data>
  <data name="&gt;&gt;NBG_Data.Type" xml:space="preserve">
    <value>DevExpress.XtraNavBar.NavBarGroup, DevExpress.XtraNavBar.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="salesDelegatesForItemsToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>244, 22</value>
  </data>
  <data name="&gt;&gt;toolStripMenuItem4.Name" xml:space="preserve">
    <value>toolStripMenuItem4</value>
  </data>
  <data name="scr_Currency.Size" type="System.Drawing.Size, System.Drawing">
    <value>202, 22</value>
  </data>
  <data name="picLogo.Properties.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="&gt;&gt;mi_SL_InvoiceList.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelControl1.Name" xml:space="preserve">
    <value>labelControl1</value>
  </data>
  <data name="toolStripSeparator1.AutoSize" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;bar3.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Bar, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;CTA_Linkitsys.Name" xml:space="preserve">
    <value>CTA_Linkitsys</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;toolStripMenuItem4.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="menuStrip1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="&gt;&gt;mi_Windows.Name" xml:space="preserve">
    <value>mi_Windows</value>
  </data>
  <data name="pnlSmall.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="&gt;&gt;defaultLookAndFeel1.Name" xml:space="preserve">
    <value>defaultLookAndFeel1</value>
  </data>
  <data name="&gt;&gt;picLogo.Name" xml:space="preserve">
    <value>picLogo</value>
  </data>
  <data name="&gt;&gt;mi_reports.Name" xml:space="preserve">
    <value>mi_reports</value>
  </data>
  <data name="rdoStyles.Location" type="System.Drawing.Point, System.Drawing">
    <value>-1, 29</value>
  </data>
  <data name="itemsSalesToEachDelegateToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>244, 22</value>
  </data>
  <data name="&gt;&gt;E_taxableTypes.Name" xml:space="preserve">
    <value>E_taxableTypes</value>
  </data>
  <data name="&gt;&gt;rdoStyles.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;mi_reports.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mi_ChangePass.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mi_UnSyncAddNotes.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;pnlImage.Parent" xml:space="preserve">
    <value>pnlLarge</value>
  </data>
  <data name="&gt;&gt;pnlImage.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.PanelControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="rdoStyles.Properties.Items15" xml:space="preserve">
    <value>Caramel</value>
  </data>
  <data name="rdoStyles.Properties.Items25" xml:space="preserve">
    <value>Coffee</value>
  </data>
  <data name="topCategoriesOfItemsSoldToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>244, 22</value>
  </data>
  <data name="rdoStyles.Properties.Items45" xml:space="preserve">
    <value>Pumpkin</value>
  </data>
  <data name="rdoStyles.Properties.Items55" xml:space="preserve">
    <value>Springtime</value>
  </data>
  <data name="mi_SL_ReturnList.Size" type="System.Drawing.Size, System.Drawing">
    <value>168, 22</value>
  </data>
  <data name="rdoStyles.Properties.Items44" xml:space="preserve">
    <value>Pumpkin</value>
  </data>
  <data name="barDockControlTop.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;barDockControlRight.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;defaultLookAndFeel1.Type" xml:space="preserve">
    <value>DevExpress.LookAndFeel.DefaultLookAndFeel, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator31.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="toolStripMenuItem4.Size" type="System.Drawing.Size, System.Drawing">
    <value>32, 19</value>
  </data>
  <data name="E_activityTypes.Text" xml:space="preserve">
    <value>Activity Type</value>
  </data>
  <data name="&gt;&gt;mi_EinvoiceSync.Name" xml:space="preserve">
    <value>mi_EinvoiceSync</value>
  </data>
  <data name="&gt;&gt;mi_envoice.Name" xml:space="preserve">
    <value>mi_envoice</value>
  </data>
  <data name="E_activityTypes.Size" type="System.Drawing.Size, System.Drawing">
    <value>202, 22</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator9.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator4.Name" xml:space="preserve">
    <value>toolStripSeparator4</value>
  </data>
  <data name="&gt;&gt;CTA_Facebook.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;src_Countries.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="barDockControlRight.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Right</value>
  </data>
  <data name="toolStripSeparator6.AutoSize" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="mi_envoice.Text" xml:space="preserve">
    <value>E-Invoice</value>
  </data>
  <data name="&gt;&gt;menuStrip1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="pnlSmall.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="toolStripSeparator3.Size" type="System.Drawing.Size, System.Drawing">
    <value>199, 1</value>
  </data>
  <data name="&gt;&gt;E_storesCodes.Name" xml:space="preserve">
    <value>E_storesCodes</value>
  </data>
  <data name="&gt;&gt;E_storesCodes.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="mi_Item.Size" type="System.Drawing.Size, System.Drawing">
    <value>165, 22</value>
  </data>
  <data name="&gt;&gt;src_Countries.Name" xml:space="preserve">
    <value>src_Countries</value>
  </data>
  <data name="&gt;&gt;picLogo.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.PictureEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="picLogo.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="mi_UnSyncAddNotes.Size" type="System.Drawing.Size, System.Drawing">
    <value>202, 22</value>
  </data>
  <data name="rdoStyles.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlLeft.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="picLogo.Size" type="System.Drawing.Size, System.Drawing">
    <value>869, 129</value>
  </data>
  <data name="&gt;&gt;ribbonPageGroup1.Name" xml:space="preserve">
    <value>ribbonPageGroup1</value>
  </data>
  <data name="itemsSalesToEachDelegateToolStripMenuItem.Text" xml:space="preserve">
    <value>Items sales to each delegate</value>
  </data>
  <data name="mi_SL_Sep5IndSl.Size" type="System.Drawing.Size, System.Drawing">
    <value>165, 6</value>
  </data>
  <data name="&gt;&gt;NBI_SL_Customer.Type" xml:space="preserve">
    <value>DevExpress.XtraNavBar.NavBarItem, DevExpress.XtraNavBar.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="NBG_Settings.Caption" xml:space="preserve">
    <value>Settings</value>
  </data>
  <data name="mi_Sales.Text" xml:space="preserve">
    <value>Sa&amp;les</value>
  </data>
  <data name="mi_EinvoiceSync.Size" type="System.Drawing.Size, System.Drawing">
    <value>202, 22</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator5.Name" xml:space="preserve">
    <value>toolStripSeparator5</value>
  </data>
  <data name="miExit.Size" type="System.Drawing.Size, System.Drawing">
    <value>171, 22</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Name" xml:space="preserve">
    <value>barDockControlTop</value>
  </data>
  <data name="salesDelegatesForItemsToolStripMenuItem.Text" xml:space="preserve">
    <value>Sales Delegates For Items</value>
  </data>
  <data name="&gt;&gt;NBI_Item.Type" xml:space="preserve">
    <value>DevExpress.XtraNavBar.NavBarItem, DevExpress.XtraNavBar.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="mi_Item.Text" xml:space="preserve">
    <value>Items</value>
  </data>
  <data name="barDockControlTop.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="mi_ChangePass.Size" type="System.Drawing.Size, System.Drawing">
    <value>195, 22</value>
  </data>
  <data name="btnClosePopup.ToolTip" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="&gt;&gt;NBI_SL_Invoice.Type" xml:space="preserve">
    <value>DevExpress.XtraNavBar.NavBarItem, DevExpress.XtraNavBar.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;topSellingCustomerCategoriesToolStripMenuItem.Name" xml:space="preserve">
    <value>topSellingCustomerCategoriesToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>frmMain</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator2.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator5.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelControl1.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="mi_unSyncedRtrnInces.Size" type="System.Drawing.Size, System.Drawing">
    <value>202, 22</value>
  </data>
  <data name="&gt;&gt;pnlImage.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="&gt;&gt;pnlLarge.Parent" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="toolStripSeparator8.Size" type="System.Drawing.Size, System.Drawing">
    <value>165, 6</value>
  </data>
  <data name="rdoStyles.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="barDockControlLeft.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="mi_ChangePass.Text" xml:space="preserve">
    <value>Change  Pass</value>
  </data>
  <data name="btnClosePopup.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 23</value>
  </data>
  <data name="rdoStyles.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;CTA_Twitter.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.XtraForm, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="resource.ExpandedWidth" type="System.Int32, mscorlib">
    <value>220</value>
  </data>
  <data name="E_storesCodes.Text" xml:space="preserve">
    <value>Stores Code</value>
  </data>
  <data name="pnlImage.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="NBI_SL_Invoice.Caption" xml:space="preserve">
    <value>New Sales Invoice</value>
  </data>
  <data name="panelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 24</value>
  </data>
  <data name="navBarControl1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="&gt;&gt;mi_SL_Add.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="frmMain.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="rdoStyles.Properties.Items11" xml:space="preserve">
    <value>Black</value>
  </data>
  <data name="rdoStyles.Properties.Items21" xml:space="preserve">
    <value>Money Twins</value>
  </data>
  <data name="&gt;&gt;barStaticItem1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarStaticItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="rdoStyles.Properties.Items41" xml:space="preserve">
    <value>London Liquid Sky</value>
  </data>
  <data name="mi_Styles.Size" type="System.Drawing.Size, System.Drawing">
    <value>171, 22</value>
  </data>
  <data name="mi_EinvoiceSync.Text" xml:space="preserve">
    <value>Sales Invoice</value>
  </data>
  <data name="menuStrip1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="pnlImage.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="mi_SL_AddList.Size" type="System.Drawing.Size, System.Drawing">
    <value>168, 22</value>
  </data>
  <data name="&gt;&gt;btnClosePopup.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;taxSubTypesToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAA
        AAAfbf8AH2z/AB9r/x8fa/+rH2v/9h9r/9AfbP9HH2//AB9u/wAfcP8AAAAAAB9p/wAfZv8AH2D/Ah9j
        /2cfY//iH2P/7R9j/4YfY/8KH2P/AB9j/wAAAAAADw4QAA4NDwAODQ4ADQwORwwMDdAMCw32CwsMqwsL
        DB8MCw0ADAsNAB9u/wAfbf8WH23/qx9t//8fbv//H27//x9u/9gfb/89H3D/AB9y/wAfcf8AH23/AB9n
        /wAfZ/9cH2X/6x9j//8fY///H2P/9x9j/38fY/8GH2P/AB5e8QAPDhAADw4QAA4NDz0NDQ/YDQ0O/w0M
        Dv8MDA3/DAsNqwwLDRYMCw0AH2//FR9v/6Ifb///H3D//x9x//8fcf//H3H//x9x/9Mfcv82IHD/AiBw
        /wQfcP8DH2z/VB9q/+gfZ///H2X//x9j//8fY///H2P/9x9j/3cfY/8FH2P+ABAPEQAQDxE1Dw4Q0w8O
        EP8ODg//Dg0P/w0NDv8MDA3/DAsNogwLDRUfcP+MH3H//R9y//8ec///HnP//x50//8edP//HnP//x5z
        /9cec/+pH3L/qR9x/6wfbv/mH23//x9q//8faP//H2X//x9j//8fY///H2P/8h9j/04bTb8AERASERAQ
        ErsQEBL/EA8R/w8PEf8PDhD/Dg0P/w0ND/8MDA79DAwNjB5z/9QedP//HnX//x52//8edv//Hnb//x52
        //8edv//Hnb//x51//8edP//HnP//x9x//8fb///H23//x9r//8faP//H2X//x9j//8fY///H2P/gxg4
        hAASERM1ERET7hERE/8REBL/EBAS/xAPEf8PDhD/Dg4Q/w0ND/8NDA7THnX/wx52//8ed///Hnj//x54
        //8eef//Hnn//x54//8eeP/9Hnf/8h52//Iedf/zHnP//h9x//8fb///H23//x9q//8fZ///H2T//x9j
        //8fY/90GkGeABISFCYTEhThEhIU/xIRE/8RERP/EBAS/xAPEf8PDhD/Dg4Q/w4ND78ed/9UHnj/5x55
        //8eev//Hnr//x57//8ee///Hnr/+x56/5AeeP88Hnf/PB52/0Eedf+wHnP//x9x//8fb///H2z//x9p
        //8fZv//H2T/zB9j/yQdV9wAExIVAxMTFYMTEhX8ExIU/xISFP8RERP/ERAS/xAPEf8PDhDkDg4QTx50
        /wAeev9dHnv/7R58//8efP//Hn3//x59//0efP+UHnv/DB55/wAed/8AHnb/AB52/x8edf+5HnP//x9x
        //8fbv//H2v//x9o/9YfZf85H2f/AAAAAAAUExYAFBMWDRQTFpQUExX9ExIV/xISFP8SERP/ERAS6hAP
        EVYTDhQAHnr/AB58/wEefP9nHn3/7x5+//8efv/9Hn7/nR59/xAefP8AHnv/AB52/wAedf8AHnX/AB51
        /yUedP2+H3P//x9x//8fbPvbH2r/Px9o/wAfZv8AH2P/ABQTFgAUExYAFBQWEBQUFp0UExb9ExIV/xIS
        FOwRERNfIxkMABAQEQAee/8AHn3/AB5+/wcefv+xHn///x5//+oef/8tHn//AB59/wAefP8AAAAAAB52
        /wAeV/8AGkKGABk+eUYaQoPzG0KG/xo0ZZIbRZkAH2v/AB9o/wAAAAAAFBQWABQUFgAVFBcAFRQXLRUU
        F+oUExb/ExMVqxMTFAUSERQAEBASAAAAAAAegP8AHn//Ax6A/6kegP//HoD/5x6A/ycegP8AHoD/AAAA
        AAAAAAAAAAAAABpCgQAXFRcAFhMSOhcVFvAXFRb/GBUWjhgYHgAcO3gAAAAAAAAAAAAAAAAAFxYZABYV
        GAAWFRgnFRUX5xUUF/8UExajExIVAhQTFgAAAAAAHoD/AB6A/wAegP8IHoD/sx6B//8egf/rHoH/Lx6B
        /wAegv8AHoL/AAAAAAAXFhkAFBQXABcXGgAXFxpIGBca9BgYG/8ZGBuTGRkcABoaHQAbGx4AAAAAABkZ
        HAAYGBsAFxcZABcWGS8WFhjrFRUX/xUUF6wUFBYGFBMWABQTFQAef/8AHoD/Ah6A/20egf/xHoH//x6B
        //4egv+kHoL/Ex6C/wAegv8AFhYYABsZIAAXFxoAFxcaKRgXGsMYGBv/GRkc/xoZHN4bGh1FHBoeABwb
        HwAdHCAAGhkdABkZHAAYGBsTGBcapBcWGf4WFRj/FRQX7xQUFmYTEBMBExMVAB59/wIegP9kHoD/8B6B
        //8egv//HoL//x6C//8egv+cHoL/EB6B/wAaRX4AFxYZABcWGSQXFxq/GBgb/xkZHP8aGR3/Gxoe/xwb
        HtkcGx89HBwfABoYIQAbGh0AGxodEBkZHJwYGBv/Fxca/xYWGf8VFRf/FBQW7RQTFVwBABAAHn//Vx5/
        /+kegP//HoH//x6C//8egv//HoL//x6C//4egf+JHoD/BBgxUQAWFhgSFxYZsRgXGv8ZGBv/Ghkc/xsa
        Hf8cGx7/HBwf/x0cIM8eHSAmHRwgAB0cHwQbGh6JGhkc/hkYG/8YFxr/FxYZ/xYVGP8VFBf/ExMV5hMS
        FFIefv/EHn///x6A//8egf//HoL//x6C//8egv//HoL//x6B/+Megf8nGUB0ABYWGEwXFhn4GBca/xkY
        G/8aGh3/Gxse/xwcH/8dHCD/Hh0h/x4dIXUdHCAAHBsfJxwbHuMaGh3/GRkc/xgXGv8XFhn/FhUY/xUU
        F/8UExX/ExIUwR5+/9Uef///HoD//x6B//8egf//HoL//x6C//8egf//HoH/7h6B/zQaRX4AFhYZWhcW
        GfwYGBv/GRkc/xsaHf8cGx7/HRwg/x4dIf8fHiL/Hx4igx4dIQAdHB80HBsf7hsaHf8aGRz/GBgb/xcX
        Gf8WFRj/FRQX/xQTFf8TEhXSHn7/jR5///0egP//HoD//x6B//8egf//HoH//x6B//8egf+4HoD/EBk3
        YAAWFhkqFxYZ2xgYG/8ZGRz/Gxod/xwbHv8dHCD/Hh0h/x8eIu8gHyNKHx4iABwcHxAcGx64Gxod/xoZ
        HP8YGBv/Fxca/xYVGP8VFBf/FBMV/BMSFYgefv8VHn7/oh5///8egP//HoD//x6B//8egf//HoD/0B6A
        /zIegf8AFxIRABgaHQAXFxpQGBca5hkYHP8aGh3/Gxse/x0cH/8dHSD1Hh0hch8eIwQfHiIAGxseABwb
        HjIaGh3QGRkc/xgXG/8XFhn/FhUY/xUUF/8UExafExIVEx5+/wAefv8XHn7/qx5///8ef///Hn///x6A
        /9UegP85HoD/AB5//wAYIzUAFxcaABgYGwAYFxpXGRgb6hoZHP8bGh7/HBse+B0cH3weHSEFHh0hAB8e
        IgAbGh0AGxodABoZHDkZGBvVGBca/xcWGf8WFRj/FRQXqBQUFhUUExYAHn7/AB5+/wAefv8dHn7/yR5+
        //8efv/yHn//Sh5//wAef/8AHoD/AAAAAAAYFxoAGBcaABUXGgAZGBtUGRkcwxoaHc0bGh5yHBseCB0c
        HwAeHSEAAAAAABsaHQAaGRwAGRgbABgYG0oYFxryFxYZ/xYVGMUVFBcbFRQXABQUFgAefv8AHnz/AB56
        /wIefP+oHn3//x59/+ceff8lHn3/AB5//wAAAAAAAAAAAAAAAAAYGBoAGBgbABgXGgAaGRwNGhkdERkZ
        HAAbGh4AGxoeAAAAAAAAAAAAAAAAABkZHAAXFxoAFxcaJRcWGecWFRj/FRUXohMSFQEVFBcAFRQXAB50
        /wAed/8AHnn/CR56/7Qee///Hnv/6x57/y4eev8AHnj/AB53/wAAAAAAFRQXABYVGAAWFRgAFxYZJBgX
        GnEYFxp6GRgbNBkOEQAYGBsAGBcaAAAAAAAWFhgAFhYYABYWGQAXFhkuFhYY6xUVF/8VFBeuFBQWBxMT
        FQASERQAHnL/AB11/wMedv9zHnf/8x54//8eef/+Hnn/oR55/xEed/8AHnb/ABMSFgAbFx0AFRQXABUV
        Fy4WFhjJFxYZ/xgXGv8YFxreGBcaShcXGgAYFxoAFxYZABYVGAAWFRgAFhYYERYWGKEWFRj+FRQX/xQT
        FvETEhVuEhETAxIREwAgb/8CH3L/aR50//Iedf//Hnb//x53//8ed//+Hnf/lx52/w0edf8AGEOMABQT
        FgAUExYnFRQXxBUVF/8WFhj/FxYZ/xcXGv8XFxrdFxcaQRYWGQAWFRgAFhUYABYVGA0WFRiXFhUY/hUU
        F/8UExb/ExMV/xISFPARERNlEQ4RAh9t/1sfb//sH3H//x9y//8ec///HnT//x50//8edP/9HnT/hx9y
        /wUVKUwAEhIUFBMSFbYUExb/FBQW/xERE/8QEBL/FhUY/xYWGf8XFhnXFhYYYBYVGE4WFRhOFhUYmBUV
        F/wVFBf/FBMW/xMTFf8SEhT/ERET/xEQEuoQDxFXH2v/xh9s//8fbv//H2///x9w//8fcf//H3H//x9x
        //8fcf/kH3D/KBc3cQASERNOEhIU+BMSFf8SERT/DAwN/woJC/8QDxH/FRUX/xYVGP8WFRj7FRUX+BUU
        F/gVFBf/FBQW/xQTFv8TEhX/EhIU/xIRE/8REBL/EA8R/w8PEMMfZ//UH2n//x9r//8fbP//H23//x9u
        //8fbv//H27//x9u/+4fbv80Fzl6ABEQE1oRERP8EhIU/xERE/8ODg//DAwN/xAPEf8UExb/FBQW/xQU
        Fv8UFBb/FBMW/xQTFv8TExX/ExIU/xISFP8SERP/ERAS/xAQEv8PDxH/Dg4Q0R9l/4kfZf/8H2f//x9o
        //8faf//H2r//x9q//8fav//H2v/sh9r/w4VKlcAEBASKRAQEtoRERP/EhET/xEQEv8QEBL/EhIU/xMT
        Ff8TExXyExMVsxMTFagTEhWoExIU1BISFP8SERP/ERET/xEQEv8QDxH/Dw8R/w4OEPwODQ+EH2P/Ex9j
        /6AfZP//H2T//x9l//8fZv//H2b//x9n/8gfaP8qH2j/AA0CAAAREBIAEA8RThAQEuUREBL/ERET/xIR
        E/8SERP/EhEU9hISFHYTExUIEhEUAxMRFQISERMxERETzREQEv8QEBL/EA8R/w8OEP8ODhD/Dg0Pnw0N
        DxIfY/8AH2P/Fh9j/6kfY///H2P//x9j//8fY//MH2T/MB9m/wAfVP8AERgnABAPEQAQERMAEA8RVRAP
        EegQEBL/EBAS/xEQEvcRERN9EhETBhISFAATEhUAExIVABEREwAQEBI2EA8R0g8PEf8PDhD/Dg4P/w4N
        D6gNDQ8VDg0PAB9j/wAfY/8AH2P/HR9j/6QfY//nH2P/wh9j/zofZP8AH2P/AB9m/wAAAAAAEA8RAA8P
        EQAKDhIBDw8QYA8PEdcQDxHiEA8RgRAPEQkREBIAEhETAAAAAAARERMAEA8RABAPEQAPDhBADg4Qxg4N
        D+cODQ+iDQ0OHA0NDwAODQ8AACAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAGAIBw
        DgEAIAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAQAAHAOAAAgBAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgBAA=
</value>
  </data>
  <data name="mi_Store.Text" xml:space="preserve">
    <value>Branches/ Stores</value>
  </data>
  <data name="menuStrip1.Size" type="System.Drawing.Size, System.Drawing">
    <value>1174, 24</value>
  </data>
  <data name="scr_Item_Codes.Size" type="System.Drawing.Size, System.Drawing">
    <value>202, 22</value>
  </data>
  <data name="mi_SL_InvoiceList.Size" type="System.Drawing.Size, System.Drawing">
    <value>168, 22</value>
  </data>
  <data name="rdoStyles.Properties.Items10" xml:space="preserve">
    <value>Black</value>
  </data>
  <data name="&gt;&gt;panelControl1.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="picLogo.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="topSellingCustomerCategoriesToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>244, 22</value>
  </data>
  <data name="picLogo.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="NBG_Data.Caption" xml:space="preserve">
    <value>Data</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator2.Name" xml:space="preserve">
    <value>toolStripSeparator2</value>
  </data>
  <data name="panelControl1.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="&gt;&gt;mi_ST_SP_1.Name" xml:space="preserve">
    <value>mi_ST_SP_1</value>
  </data>
  <data name="panelControl1.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="mi_E_InvoiceSettings.Size" type="System.Drawing.Size, System.Drawing">
    <value>203, 22</value>
  </data>
  <data name="ribbonPageGroup1.Text" xml:space="preserve">
    <value>ribbonPageGroup1</value>
  </data>
  <data name="&gt;&gt;barUser.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarStaticItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;mi_UOM.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mi_E_InvoiceSettings.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mi_globalERPRegistration.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mi_GlobalGrid.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="rdoStyles.Properties.Items12" xml:space="preserve">
    <value>Blue</value>
  </data>
  <data name="&gt;&gt;mi_About.Name" xml:space="preserve">
    <value>mi_About</value>
  </data>
  <data name="rdoStyles.Properties.Items35" xml:space="preserve">
    <value>Glass Oceans</value>
  </data>
  <data name="mi_ImportExcelFiles.Size" type="System.Drawing.Size, System.Drawing">
    <value>195, 22</value>
  </data>
  <data name="&gt;&gt;scr_Currency.Name" xml:space="preserve">
    <value>scr_Currency</value>
  </data>
  <data name="&gt;&gt;picLogo.Parent" xml:space="preserve">
    <value>pnlImage</value>
  </data>
  <data name="salesProfitabilityToolStripMenuItem.Text" xml:space="preserve">
    <value>Sales Profitability</value>
  </data>
  <data name="&gt;&gt;popupStyle.Name" xml:space="preserve">
    <value>popupStyle</value>
  </data>
  <data name="salesProfitabilityToolStripMenuItem.Size" type="System.Drawing.Size, System.Drawing">
    <value>244, 22</value>
  </data>
  <data name="&gt;&gt;pnlSmall.Parent" xml:space="preserve">
    <value>pnlImage</value>
  </data>
  <data name="pnlImage.Location" type="System.Drawing.Point, System.Drawing">
    <value>1, 2</value>
  </data>
  <data name="&gt;&gt;releaseNotesToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;ribbonPage1.Name" xml:space="preserve">
    <value>ribbonPage1</value>
  </data>
  <data name="mi_ST_SP_1.Size" type="System.Drawing.Size, System.Drawing">
    <value>168, 6</value>
  </data>
  <data name="mi_SL_Return.Size" type="System.Drawing.Size, System.Drawing">
    <value>168, 22</value>
  </data>
  <data name="src_Countries.Text" xml:space="preserve">
    <value>Countries</value>
  </data>
  <data name="labelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 13</value>
  </data>
  <data name="&gt;&gt;mi_EinvoiceSync.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mi_SL_Sep1Ret.Name" xml:space="preserve">
    <value>mi_SL_Sep1Ret</value>
  </data>
  <data name="mi_PreInvoice.Size" type="System.Drawing.Size, System.Drawing">
    <value>190, 22</value>
  </data>
  <data name="mi_ShowHideChart.Size" type="System.Drawing.Size, System.Drawing">
    <value>171, 22</value>
  </data>
  <data name="&gt;&gt;mi_settings.Name" xml:space="preserve">
    <value>mi_settings</value>
  </data>
  <data name="toolStripSeparator9.Size" type="System.Drawing.Size, System.Drawing">
    <value>199, 6</value>
  </data>
  <data name="&gt;&gt;NBG_Data.Name" xml:space="preserve">
    <value>NBG_Data</value>
  </data>
  <data name="toolStripSeparator4.Size" type="System.Drawing.Size, System.Drawing">
    <value>199, 6</value>
  </data>
  <data name="rdoStyles.Properties.Items47" xml:space="preserve">
    <value>Seven</value>
  </data>
  <data name="frmMain.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator32.Name" xml:space="preserve">
    <value>toolStripSeparator32</value>
  </data>
  <data name="NBI_SL_Return.Caption" xml:space="preserve">
    <value>New Sales Return Invoice</value>
  </data>
  <data name="rdoStyles.Properties.Items31" xml:space="preserve">
    <value>Main Style</value>
  </data>
  <data name="&gt;&gt;scr_Item_Codes.Name" xml:space="preserve">
    <value>scr_Item_Codes</value>
  </data>
  <data name="&gt;&gt;mi_unSyncedRtrnInces.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="picLogo.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 4</value>
  </data>
  <data name="&gt;&gt;menuStrip1.Type" xml:space="preserve">
    <value>System.Windows.Forms.MenuStrip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mi_DT_Sep1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="mi_reports.Size" type="System.Drawing.Size, System.Drawing">
    <value>59, 20</value>
  </data>
  <data name="rdoStyles.Properties.Items1" xml:space="preserve">
    <value>Flat</value>
  </data>
  <data name="mi_settings.Text" xml:space="preserve">
    <value>Settings</value>
  </data>
  <data name="popupStyle.Location" type="System.Drawing.Point, System.Drawing">
    <value>882, 6</value>
  </data>
  <data name="&gt;&gt;topCategoriesOfItemsSoldToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;popupStyle.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="pnlLarge.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value> </value>
  </data>
  <data name="&gt;&gt;CTA_LinkedIn.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="topSellingCustomerCategoriesToolStripMenuItem.Text" xml:space="preserve">
    <value>Top selling customer categories</value>
  </data>
  <data name="&gt;&gt;mi_GlobalGrid.Name" xml:space="preserve">
    <value>mi_GlobalGrid</value>
  </data>
  <data name="&gt;&gt;mi_SL_Sep5IndSl.Name" xml:space="preserve">
    <value>mi_SL_Sep5IndSl</value>
  </data>
  <data name="mi_Store.Size" type="System.Drawing.Size, System.Drawing">
    <value>165, 22</value>
  </data>
  <data name="&gt;&gt;NBI_Styles.Name" xml:space="preserve">
    <value>NBI_Styles</value>
  </data>
  <data name="&gt;&gt;الاعداداتToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="rdoStyles.Properties.Items3" xml:space="preserve">
    <value>Ultra Flat</value>
  </data>
  <data name="rdoStyles.Properties.Items4" xml:space="preserve">
    <value>Style 3D</value>
  </data>
  <data name="rdoStyles.Properties.Items5" xml:space="preserve">
    <value>Style 3D</value>
  </data>
  <data name="rdoStyles.Properties.Items6" xml:space="preserve">
    <value>Office 2003</value>
  </data>
  <data name="rdoStyles.Properties.Items7" xml:space="preserve">
    <value>Office 2003</value>
  </data>
  <data name="rdoStyles.Properties.Items8" xml:space="preserve">
    <value>Windows XP</value>
  </data>
  <data name="rdoStyles.Properties.Items9" xml:space="preserve">
    <value>Windows XP</value>
  </data>
  <data name="rdoStyles.Properties.Items16" xml:space="preserve">
    <value>iMaginary</value>
  </data>
  <data name="&gt;&gt;navBarControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraNavBar.NavBarControl, DevExpress.XtraNavBar.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="rdoStyles.Properties.Items36" xml:space="preserve">
    <value>High Contrast</value>
  </data>
  <data name="rdoStyles.Properties.Items46" xml:space="preserve">
    <value>Seven</value>
  </data>
  <data name="rdoStyles.Properties.Items56" xml:space="preserve">
    <value>Stardust</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Name" xml:space="preserve">
    <value>barDockControlLeft</value>
  </data>
  <data name="&gt;&gt;eInvoicesUnsyncToolStripMenuItem.Name" xml:space="preserve">
    <value>eInvoicesUnsyncToolStripMenuItem</value>
  </data>
  <data name="mi_unSyncedRtrnInces.Text" xml:space="preserve">
    <value>UnSynced Credit Notes</value>
  </data>
  <data name="NBI_Store.Caption" xml:space="preserve">
    <value>Branches/ Stores</value>
  </data>
  <data name="&gt;&gt;NBG_Settings.Type" xml:space="preserve">
    <value>DevExpress.XtraNavBar.NavBarGroup, DevExpress.XtraNavBar.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barDockControlRight.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;mi_SL_ReturnList.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="scr_Item_Codes.Text" xml:space="preserve">
    <value>Item Codes</value>
  </data>
  <data name="&gt;&gt;mi_data.Name" xml:space="preserve">
    <value>mi_data</value>
  </data>
  <data name="rdoStyles.Properties.Items40" xml:space="preserve">
    <value>London Liquid Sky</value>
  </data>
  <data name="pnlLarge.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="&gt;&gt;taxSubTypesToolStripMenuItem.Name" xml:space="preserve">
    <value>taxSubTypesToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;recievedDocuments.Name" xml:space="preserve">
    <value>recievedDocuments</value>
  </data>
  <data name="&gt;&gt;mi_ST_CompInfo.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="recievedDocuments.Text" xml:space="preserve">
    <value>Recieved Documents</value>
  </data>
  <data name="mi_SL_Invoice.Text" xml:space="preserve">
    <value>New Sales Invoice</value>
  </data>
  <data name="&gt;&gt;rdoStyles.Parent" xml:space="preserve">
    <value>popupStyle</value>
  </data>
  <data name="mi_ShowHideChart.Text" xml:space="preserve">
    <value>Show / Hide Chart</value>
  </data>
  <data name="rdoStyles.Properties.Items63" xml:space="preserve">
    <value>Valentine</value>
  </data>
  <data name="mi_SL_InvoiceList.Text" xml:space="preserve">
    <value>Sales Invoices</value>
  </data>
  <data name="barDockControlBottom.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;mi_SL_Return.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="popupStyle.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="&gt;&gt;releaseNotesToolStripMenuItem.Name" xml:space="preserve">
    <value>releaseNotesToolStripMenuItem</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator6.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator3.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mi_SL_Customer.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;NBI_SL_Customer.Name" xml:space="preserve">
    <value>NBI_SL_Customer</value>
  </data>
  <data name="CTA_Linkitsys.MenuAppearance.HeaderItemAppearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barDockControlBottom.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;barManager1.Name" xml:space="preserve">
    <value>barManager1</value>
  </data>
  <data name="&gt;&gt;pnlImage.Name" xml:space="preserve">
    <value>pnlImage</value>
  </data>
  <data name="&gt;&gt;panelControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.PanelControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="eInvoicesUnsyncToolStripMenuItem.Text" xml:space="preserve">
    <value>UnSynced Sales Invoices</value>
  </data>
  <data name="&gt;&gt;mi_Sales.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;scr_Item_Codes.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="&gt;&gt;salesProfitabilityToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="barDockControlRight.Location" type="System.Drawing.Point, System.Drawing">
    <value>1174, 0</value>
  </data>
  <data name="mi_SL_Add.Size" type="System.Drawing.Size, System.Drawing">
    <value>168, 22</value>
  </data>
  <data name="navBarControl1.Text" xml:space="preserve">
    <value>navBarMain</value>
  </data>
  <data name="rdoStyles.Properties.Items2" xml:space="preserve">
    <value>Ultra Flat</value>
  </data>
  <data name="pnlImage.Appearance.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="releaseNotesToolStripMenuItem.Text" xml:space="preserve">
    <value>Release Notes</value>
  </data>
  <data name="mi_UnSyncAddNotes.Text" xml:space="preserve">
    <value>UnSynced Debit Notes</value>
  </data>
  <data name="&gt;&gt;customeAccountsListStatisticsToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="mi_reportCenter.Size" type="System.Drawing.Size, System.Drawing">
    <value>180, 22</value>
  </data>
  <data name="&gt;&gt;mi_UOM.Name" xml:space="preserve">
    <value>mi_UOM</value>
  </data>
  <data name="barDockControlLeft.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;mi_PreInvoice.Name" xml:space="preserve">
    <value>mi_PreInvoice</value>
  </data>
  <data name="rdoStyles.Properties.Items30" xml:space="preserve">
    <value>DevExpress Style</value>
  </data>
  <data name="pnlSmall.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="mi_SL_Invoice.Size" type="System.Drawing.Size, System.Drawing">
    <value>168, 22</value>
  </data>
  <data name="rdoStyles.Properties.Items19" xml:space="preserve">
    <value>Lilian</value>
  </data>
  <data name="&gt;&gt;Scr_UnitOfMeaure.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;mi_SL_AddList.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="rdoStyles.Properties.Items49" xml:space="preserve">
    <value>Seven Classic</value>
  </data>
  <data name="rdoStyles.Properties.Items59" xml:space="preserve">
    <value>Summer</value>
  </data>
  <data name="&gt;&gt;mi_About.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="rdoStyles.Properties.Items51" xml:space="preserve">
    <value>Sharp</value>
  </data>
  <data name="barDockControlTop.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="CTA_Linkitsys.MenuAppearance.HeaderItemAppearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="mi_About.Text" xml:space="preserve">
    <value>About Program</value>
  </data>
  <data name="mi_reports.Text" xml:space="preserve">
    <value>&amp;Reports</value>
  </data>
  <data name="rdoStyles.Properties.Items32" xml:space="preserve">
    <value>Foggy</value>
  </data>
  <data name="rdoStyles.Properties.Items26" xml:space="preserve">
    <value>Dark Side</value>
  </data>
  <data name="rdoStyles.Properties.Items52" xml:space="preserve">
    <value>Sharp Plus</value>
  </data>
  <data name="rdoStyles.Properties.Items62" xml:space="preserve">
    <value>Valentine</value>
  </data>
  <data name="&gt;&gt;mi_SL_Invoice.Name" xml:space="preserve">
    <value>mi_SL_Invoice</value>
  </data>
  <data name="taxSubTypesToolStripMenuItem.Text" xml:space="preserve">
    <value>Tax SubTypes</value>
  </data>
  <data name="barUserName.Caption" xml:space="preserve">
    <value>....................</value>
  </data>
  <data name="resource.CollapsedWidth" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;itemsSalesToEachDelegateToolStripMenuItem.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;btnClosePopup.Name" xml:space="preserve">
    <value>btnClosePopup</value>
  </data>
  <data name="rdoStyles.Properties.Items17" xml:space="preserve">
    <value>iMaginary</value>
  </data>
  <data name="rdoStyles.Properties.Items27" xml:space="preserve">
    <value>Dark Side</value>
  </data>
  <data name="&gt;&gt;mi_unSyncedRtrnInces.Name" xml:space="preserve">
    <value>mi_unSyncedRtrnInces</value>
  </data>
  <data name="&gt;&gt;panelControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="rdoStyles.Properties.Items57" xml:space="preserve">
    <value>Stardust</value>
  </data>
  <data name="&gt;&gt;barUser.Name" xml:space="preserve">
    <value>barUser</value>
  </data>
  <data name="rdoStyles.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="E_taxableTypes.Text" xml:space="preserve">
    <value>Tax Types</value>
  </data>
  <data name="toolStripSeparator5.Size" type="System.Drawing.Size, System.Drawing">
    <value>199, 6</value>
  </data>
  <data name="barDockControlLeft.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Name" xml:space="preserve">
    <value>barDockControlRight</value>
  </data>
  <data name="toolStripSeparator32.Size" type="System.Drawing.Size, System.Drawing">
    <value>192, 6</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;panelControl1.Name" xml:space="preserve">
    <value>panelControl1</value>
  </data>
  <data name="btnClosePopup.Location" type="System.Drawing.Point, System.Drawing">
    <value>4, 5</value>
  </data>
  <data name="menuStrip1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="menuStrip1.Text" xml:space="preserve">
    <value>menuStrip1</value>
  </data>
  <data name="&gt;&gt;mi_ST_CompInfo.Name" xml:space="preserve">
    <value>mi_ST_CompInfo</value>
  </data>
  <data name="mi_Help.Text" xml:space="preserve">
    <value>He&amp;lp</value>
  </data>
  <data name="&gt;&gt;barUserName.Name" xml:space="preserve">
    <value>barUserName</value>
  </data>
  <data name="&gt;&gt;mi_SL_AddList.Name" xml:space="preserve">
    <value>mi_SL_AddList</value>
  </data>
  <data name="&gt;&gt;NBI_Item.Name" xml:space="preserve">
    <value>NBI_Item</value>
  </data>
  <data name="&gt;&gt;miExit.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;NBG_Sales.Type" xml:space="preserve">
    <value>DevExpress.XtraNavBar.NavBarGroup, DevExpress.XtraNavBar.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barDockControlLeft.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;mi_globalERPRegistration.Name" xml:space="preserve">
    <value>mi_globalERPRegistration</value>
  </data>
  <data name="mi_envoice.Size" type="System.Drawing.Size, System.Drawing">
    <value>68, 20</value>
  </data>
  <data name="CTA_Linkitsys.MenuAppearance.HeaderItemAppearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="pnlImage.Size" type="System.Drawing.Size, System.Drawing">
    <value>876, 136</value>
  </data>
  <data name="rdoStyles.Properties.Items29" xml:space="preserve">
    <value>Darkroom</value>
  </data>
  <metadata name="$this.Language" type="System.Globalization.CultureInfo, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>ar-EG</value>
  </metadata>
  <metadata name="barManager1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>281, 17</value>
  </metadata>
  <metadata name="menuStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>0, 0</value>
  </metadata>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="defaultLookAndFeel1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>126, 17</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>29</value>
  </metadata>
</root>