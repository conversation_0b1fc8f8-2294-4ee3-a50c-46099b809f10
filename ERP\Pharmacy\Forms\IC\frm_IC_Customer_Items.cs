﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;
using DAL.Res;
using Pharmacy.Forms;
using System.Linq;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraReports.UI;
using DevExpress.XtraGrid.Columns;

namespace Pharmacy.Forms
{
    public partial class frm_IC_Customer_Items : DevExpress.XtraEditors.XtraForm
    {
        int priceLevelId, itemsHeight, ratioHeight;
        UserPriv prvlg;
        FormAction action = FormAction.None;
        ERPDataContext DB = new ERPDataContext();
        List<IC_Item> items = new List<IC_Item>();
        List<SL_Customer_Info> lst_Customers = new List<SL_Customer_Info>();
        List<ItemLkp> lstItems = new List<ItemLkp>();
        bool DataModified;
        int ItemId_filter = 0;
        DataTable dt_CustomerItem = new DataTable();
        int customerId = 0;

        public frm_IC_Customer_Items()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(barManager1);
            action = FormAction.Add;
        }
        
        public frm_IC_Customer_Items(int _CustomerId)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(barManager1);
            action = FormAction.Add;
            customerId = _CustomerId;
        }

        private void frm_IC_Customer_Items_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            grdPrice.ProcessGridKey += new KeyEventHandler(grid_ProcessGridKey);

            lkp_Customers.Properties.View.Columns["CusNameAr"].OptionsFilter.AutoFilterCondition = AutoFilterCondition.Contains;

            LoadPrivilege();
            if (Shared.user.UseContainsToSearchItems)
                repItems.PopupFilterMode = PopupFilterMode.Contains;
            else
                repItems.PopupFilterMode = PopupFilterMode.StartsWith;

            //ErpUtils.Allow_Incremental_Search(repUOM);
            //ErpUtils.Allow_Incremental_Search(lkp_Customers);
            Get_Items();
            repItems.View.Columns["ItemNameAr"].OptionsFilter.AutoFilterCondition = AutoFilterCondition.Contains;
            repItems.View.Columns["ItemNameEn"].OptionsFilter.AutoFilterCondition = AutoFilterCondition.Contains;
            repItems.View.Columns["ItemCode1"].SortIndex = 0;
            
            #region dt_CustomerItem
            dt_CustomerItem.Columns.Clear();

            dt_CustomerItem.Columns.Add("ItemCode1");
            dt_CustomerItem.Columns.Add("ItemCode2");
            dt_CustomerItem.Columns.Add("Company");
            dt_CustomerItem.Columns.Add("Category");
            dt_CustomerItem.Columns.Add("ItemId");
            dt_CustomerItem.Columns.Add("ItemIdF");


            grdPrice.DataSource = dt_CustomerItem;
            #endregion

            #region GetCustomers
            int LastCustId = 0;
            int? goldencustomer = MyHelper.GetCustomers(out lst_Customers, Shared.user.DefaultCustGrp, out LastCustId, true);

            lkp_Customers.Properties.DisplayMember = Shared.IsEnglish ? "CusNameEn" : "CusNameAr";
            lkp_Customers.Properties.ValueMember = "CustomerId";
            lkp_Customers.Properties.DataSource = lst_Customers;
            lkp_Customers.EditValue = goldencustomer;
            lkp_Customers_EditValueChanged(lkp_Customers, EventArgs.Empty);
            #endregion

            lkp_Customers.EditValue = customerId;

            ErpUtils.Load_Grid_Layout(grdPrice, this.Name.Replace("frm_", ""));
            ErpUtils.ColumnChooser(grdPrice);

            FillItemsData();
        }

        private void frm_IC_Customer_Items_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                e.Cancel = true;

            ErpUtils.save_Grid_Layout(grdPrice, this.Name.Replace("frm_", ""), true);            
        }

        private void frm_IC_Customer_Items_KeyUp(object sender, KeyEventArgs e)
        {
                       
        }

        private void barBtn_Save_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            gridView1.PostEditor();
            gridView1.UpdateCurrentRow();

            this.Focus();

            if (!ValidData())
                return;

            SaveData();
        }

        private void barBtn_Delete_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (action == FormAction.Add)
                return;

            if (priceLevelId <= 0)
                return;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            //get all customers asigned to this price list
            if (DB.SL_Customers.Where(s => s.PriceLevel == priceLevelId).Count() > 0)
            {
                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgDelPriceListDenied : ResICAr.MsgDelPriceListDenied
                    , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            //ask for confirmation
            DialogResult DR = XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgDelPriceList : ResICAr.MsgDelPriceList//"هل أنت متأكد أنك تريد حذف هذه القائمة"
                    ,
                    Shared.IsEnglish == true ? ResICEn.MsgTWarn : ResICAr.MsgTWarn//"تنبيه"
                    , MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2);
            if (DR == DialogResult.Yes)
            {
                //delete price list
                var lstDetails = from c in DB.IC_PriceLevelDetails
                                 where c.PriceLevelId == priceLevelId
                                 select c;
                DB.IC_PriceLevelDetails.DeleteAllOnSubmit(lstDetails);
                DB.SubmitChanges();
                var lst = (from c in DB.IC_PriceLevels
                           where c.PriceLevelId == priceLevelId
                           select c).FirstOrDefault();
                DB.IC_PriceLevels.DeleteOnSubmit(lst);

                MyHelper.UpdateST_UserLog(DB, string.Empty, lkp_Customers.Text,
(int)FormAction.Delete, (int)FormsNames.IC_Customer_Items);

                DB.SubmitChanges();

                XtraMessageBox.Show(
                            Shared.IsEnglish == true ? ResICEn.MsgDel : ResICAr.MsgDel//"تم الحذف بنجاح"
                            , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                priceLevelId = 0;
                barBtnNew.PerformClick();
            }
        }

        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtnNew_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {

            lkp_Customers_EditValueChanged(null, EventArgs.Empty);
            NewPriceLevel();
        }

        private void gridView1_CellValueChanged(object sender, CellValueChangedEventArgs e)
        {

            ERPDataContext DB = new DAL.ERPDataContext();
            DAL.IC_Item item = null;

            GridView view = grdPrice.FocusedView as GridView;
            DataRow row = view.GetDataRow(e.RowHandle);

            string code1 = string.Empty;

            #region GetItem
            if (e.Column.FieldName == "ItemCode1")
            {
                if (view.GetFocusedRowCellValue("ItemCode1") != null && view.GetFocusedRowCellValue("ItemCode1").ToString() != string.Empty)
                {
                    code1 = view.GetFocusedRowCellValue("ItemCode1").ToString();

                    item = (from i in DB.IC_Items
                            where i.ItemType != (int)DAL.ItemType.MatrixParent
                            where Shared.st_Store.SellRawMaterial == false ?
                                  i.ItemType == (int)DAL.ItemType.Assembly : true
                            where i.ItemCode1 == Convert.ToInt32(view.GetFocusedRowCellValue("ItemCode1"))

                            select i).FirstOrDefault();
                }
                else
                {
                    view.DeleteRow(e.RowHandle);
                }
            }

            if (e.Column.FieldName == "ItemCode2")
            {
                if (view.GetFocusedRowCellValue("ItemCode2").ToString() != string.Empty)
                {
                    item = (from i in DB.IC_Items
                            where i.ItemType != (int)DAL.ItemType.MatrixParent
                            where Shared.st_Store.SellRawMaterial == false ?
                                  i.ItemType == (int)DAL.ItemType.Assembly : true
                            where i.ItemCode2 == view.GetFocusedRowCellValue("ItemCode2").ToString()

                            select i).FirstOrDefault();
                }
                else
                {
                    view.DeleteRow(e.RowHandle);
                }
            }

            if (e.Column.FieldName == "ItemId")
            {
                if (view.GetFocusedRowCellValue("ItemId").ToString() != string.Empty)
                {
                    item = (from i in DB.IC_Items
                            where i.ItemType != (int)ItemType.MatrixParent
                            where Shared.st_Store.SellRawMaterial == false ?
                                      i.ItemType == (int)ItemType.Assembly : true
                            where i.ItemId == Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"))
                            select i).SingleOrDefault();
                }
                else
                {
                    view.DeleteRow(e.RowHandle);
                }
            }

            if (e.Column.FieldName == "ItemCode1" || e.Column.FieldName == "ItemCode2"
                || e.Column.FieldName == "ItemId")
            {
                if (item != null && item.ItemId > 0)
                {
                    LoadItemRow(item, row);
                }
                else
                {
                    view.DeleteRow(e.RowHandle);
                    return;
                }
            }
            #endregion

            DataModified = true;
        }

        private void grid_ProcessGridKey(object sender, KeyEventArgs e)
        {
            try
            {
                DevExpress.XtraGrid.GridControl grid = sender as DevExpress.XtraGrid.GridControl;
                var view = (grid.FocusedView as DevExpress.XtraGrid.Views.Grid.GridView);
                if (e.KeyCode == Keys.Enter)
                {
                    var focused_column = (grid.FocusedView as DevExpress.XtraGrid.Views.Base.ColumnView).FocusedColumn;
                    int focused_row_handle = (grid.FocusedView as DevExpress.XtraGrid.Views.Base.ColumnView).FocusedRowHandle;

                    

                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == Keys.Tab && e.Modifiers != Keys.Shift)
                {
                    if (view.FocusedColumn.VisibleIndex == 0)
                        view.FocusedColumn = view.VisibleColumns[view.VisibleColumns.Count - 1];
                    else
                        view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex - 1];
                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == Keys.Tab && e.Modifiers == Keys.Shift)
                {
                    if (view.FocusedColumn.VisibleIndex == view.VisibleColumns.Count)
                        view.FocusedColumn = view.VisibleColumns[0];
                    else
                        view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex + 1];
                    e.Handled = true;
                    return;
                }
            }
            catch
            { }
        }

        private void data_Modified(object sender, EventArgs e)
        {
            DataModified = true;
        }

        private void Get_Items()
        {
            #region Get Items
            lstItems = (from i in DB.IC_Items
                        where i.ItemType != (int)ItemType.MatrixParent
                        where Shared.st_Store.SellRawMaterial == false ? i.ItemType == (int)ItemType.Assembly : true

                        select new ItemLkp
                        {
                            ItemCode1 = i.ItemCode1,
                            ItemCode2 = i.ItemCode2,
                            ItemId = i.ItemId,
                            ItemNameAr = i.ItemNameAr,
                            ItemNameEn = i.ItemNameEn,
                            LargeUOM = i.LargeUOM,
                            CategoryNameAr = i.IC_Category.CategoryNameAr,
                            CompanyNameAr = i.IC_Company.CompanyNameAr,
                            SellDiscountRatio = i.SalesDiscRatio,
                            CategoryId = i.Category
                        }).ToList();

            repItems.DataSource = lstItems;
            repItems.DisplayMember = Shared.IsEnglish ? "ItemNameEn" : "ItemNameAr";
            repItems.ValueMember = "ItemId";
            #endregion
        }

        private void LoadItemRow(DAL.IC_Item item, DataRow row)
        {


            if (item != null && item.ItemId > 0)
            {
                row["ItemId"] = item.ItemId;
                row["ItemIdF"] = Shared.IsEnglish ? item.ItemNameAr : item.ItemNameEn;
                row["Category"] = item.IC_Category.CategoryNameAr;
                row["Company"] = item.IC_Company.CompanyNameAr;
                row["ItemCode1"] = item.ItemCode1;
                row["ItemCode2"] = item.ItemCode2;
                //row["ItemType"] = item.ItemType;
                

                //row["ItemNameAr"] = item.ItemNameAr;
                //row["ItemNameEn"] = item.ItemNameEn;
                /**Commented
                row["SmallUOM"] = item.SmallUOM;

                if (item.MediumUOM.HasValue)
                {
                    row["MediumUOM"] = item.MediumUOM;
                    row["MediumUOMFactor"] = MyHelper.FractionToDouble(item.MediumUOMFactor);
                    row["MediumUOMPrice"] = item.MediumUOMPrice;
                }
                if (item.LargeUOM.HasValue)
                {
                    row["LargeUOM"] = item.LargeUOM;
                    row["LargeUOMFactor"] = MyHelper.FractionToDouble(item.LargeUOMFactor);
                    row["LargeUOMPrice"] = item.LargeUOMPrice;
                }
                */
            }
        }

        void DoValidate()
        {
        }

        private void NewPriceLevel()
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            Reset();
            action = FormAction.Add;

            DoValidate();
        }

        private void SaveData()
        {
            if (action == FormAction.None)
                return;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            var csItems = DB.IC_CustomerItems.Where(x => x.CustomerId == Convert.ToInt32(lkp_Customers.EditValue));
            DB.IC_CustomerItems.DeleteAllOnSubmit(csItems);
            DB.SubmitChanges();


            foreach (DataRow dr in dt_CustomerItem.Rows)
            {
                IC_CustomerItem pricLevel = new IC_CustomerItem();
            
                pricLevel.CustomerId = Convert.ToInt32(lkp_Customers.EditValue);
                pricLevel.ItemId = Convert.ToInt32(dr["ItemId"]);
                DB.IC_CustomerItems.InsertOnSubmit(pricLevel);
            }

            if (action == FormAction.Add)
            {
                MyHelper.UpdateST_UserLog(DB, string.Empty, lkp_Customers.Text,
(int)FormAction.Add, (int)FormsNames.IC_Customer_Items);
            }
            else
                MyHelper.UpdateST_UserLog(DB, string.Empty, lkp_Customers.Text,
(int)FormAction.Edit, (int)FormsNames.IC_Customer_Items);

            DB.SubmitChanges();
            

            XtraMessageBox.Show(
                Shared.IsEnglish == true ? ResICEn.MsgSave : ResICAr.MsgSave//"تم الحفظ بنجاح"
                , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
            
            DoValidate();
            DataModified = false;
        }

        private bool ValidData()
        {
            if (action == FormAction.Add)
            {
                if (prvlg != null && !prvlg.CanAdd)
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgPrvNew : ResICAr.MsgPrvNew//"عفوا, انت لا تمتلك صلاحية انشاء بيان جديد"                        
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            if (action == FormAction.Edit)
            {
                if (prvlg != null && !prvlg.CanEdit)
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgPrvEdit : ResICAr.MsgPrvEdit//"عفوا, انت لا تمتلك صلاحية تعديل هذا البيان"                        
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }

            if(lkp_Customers.EditValue == null)
            {
                return false;
            }
            return true;
        }

        void Reset()
        {

            dt_CustomerItem.Rows.Clear();
            DataModified = false;
        }

        void FillItemsData()
        {
            dt_CustomerItem.Rows.Clear();
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            
                var Csitems = (from m in DB.IC_Items.Where(m =>
                              m.IsDeleted == false )
                         join a in DB.IC_CustomerItems.Where(a => a.CustomerId == Convert.ToInt32(lkp_Customers.EditValue))
                         on m.ItemId equals a.ItemId
                         select new 
                         {
                             ItemId = m.ItemId,
                             ItemCode1 = m.ItemCode1,
                             ItemCode2 = m.ItemCode2,
                             ItemNameAr = m.ItemNameAr,
                             ItemNameEn = m.ItemNameEn,
                             Company = Shared.IsEnglish ? m.IC_Company.CompanyNameEn : m.IC_Company.CompanyNameAr,
                             Category = Shared.IsEnglish ? m.IC_Category.CategoryNameEn : m.IC_Category.CategoryNameAr
                         }).ToList();
                foreach(var i in Csitems)
                {
                gridView1.AddNewRow();
                gridView1.SetRowCellValue(gridView1.FocusedRowHandle, "ItemId", i.ItemId);
                gridView1.PostEditor();
                }
                int focusedIndex = (grdPrice.FocusedView as GridView).FocusedRowHandle;
            
        }

        DialogResult ChangesMade()
        {
            if (priceLevelId > 0 && DataModified)
            {
                DialogResult r = XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgDataModified : ResICAr.MsgDataModified//"لقد قمت بعمل بعض التغييرات, هل تريد الحفظ أولا "                    
                    , "", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                if (r == DialogResult.Yes)
                {
                    if (!ValidData())
                        return DialogResult.Cancel;

                    SaveData();
                    return r;
                }
                else if (r == DialogResult.No)
                {
                    // no thing made, continue closing or do next or do previous
                    return r;
                }
                else if (r == DialogResult.Cancel)
                {
                    //cancel form action
                    return r;
                }
            }
            return DialogResult.No;
        }

        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                prvlg = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.IC_Customer_Items).FirstOrDefault();

                if (!prvlg.CanDel)
                    barBtnDelete.Enabled = false;
                if (!prvlg.CanAdd)
                    barBtnNew.Enabled = false;            }
        }

        private void grdPrice_DoubleClick(object sender, EventArgs e)
        {
            openItem();
        }

        private void lkp_Customers_EditValueChanged(object sender, EventArgs e)
        {

            var selected_customer = lst_Customers.Where(x => x.CustomerId == Convert.ToInt32(lkp_Customers.EditValue)).FirstOrDefault();

            if (selected_customer != null)
            {
                Reset();
                FillItemsData();
            }
        }

        private void gridView1_KeyDown(object sender, KeyEventArgs e)
        {
            GridView view = sender as GridView;
                if (e.KeyCode == Keys.Delete && e.Modifiers == Keys.Control)
                {
                    //if (MessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgDelRow : ResSLAr.MsgDelRow,
                    //    Shared.IsEnglish ? ResSLEn.MsgTQues : ResSLAr.MsgTQues, MessageBoxButtons.YesNo, MessageBoxIcon.Question) !=
                    //  DialogResult.Yes)
                    //    return;

                    view.DeleteRow(view.FocusedRowHandle);

                    dt_CustomerItem.AcceptChanges();
                }
            }

        private void grdPrice_Click(object sender, EventArgs e)
        {

        }

        private void openItem()
        {
            var view = grdPrice.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            int itemId = Convert.ToInt32(view.GetRowCellValue(focused_row_index, colItemName));

            if (ErpUtils.IsFormOpen(typeof(frm_IC_Item)))
                Application.OpenForms["frm_IC_Item"].Close();

            new frm_IC_Item(itemId, FormAction.Edit).ShowDialog();
        }

        private void barBtnHelp_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "أصناف العملاء");
        }

    }
}