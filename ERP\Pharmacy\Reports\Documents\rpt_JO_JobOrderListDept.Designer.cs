﻿namespace Reports
{
    partial class rpt_JO_JobOrderListDept
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.xrTable2 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow2 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_Index = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_JOCode = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_CustomerAr = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Job = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Priority = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_SalesEmpAr = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_DeliverDate = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_RegDate = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Status = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Notes = new DevExpress.XtraReports.UI.XRTableCell();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.lbl_Department = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel8 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel5 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_DuedateTo = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel3 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_DuedateFrom = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_RegdateTo = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel1 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_RegdateFrom = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel7 = new DevExpress.XtraReports.UI.XRLabel();
            this.lblReportName = new DevExpress.XtraReports.UI.XRLabel();
            this.picLogo = new DevExpress.XtraReports.UI.XRPictureBox();
            this.lblCompName = new DevExpress.XtraReports.UI.XRLabel();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.xrPageInfo1 = new DevExpress.XtraReports.UI.XRPageInfo();
            this.PageHeader = new DevExpress.XtraReports.UI.PageHeaderBand();
            this.xrTable1 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow1 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell16 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell6 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell9 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell13 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell4 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell5 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell14 = new DevExpress.XtraReports.UI.XRTableCell();
            this.ReportFooter = new DevExpress.XtraReports.UI.ReportFooterBand();
            this.xrTable4 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow4 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_SalesEmpEn = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_CustomerEn = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell15 = new DevExpress.XtraReports.UI.XRTableCell();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable2});
            this.Detail.HeightF = 53.125F;
            this.Detail.Name = "Detail";
            this.Detail.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Detail.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrTable2
            // 
            this.xrTable2.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top)
                        | DevExpress.XtraPrinting.BorderSide.Right)
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable2.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable2.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.xrTable2.Name = "xrTable2";
            this.xrTable2.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow2});
            this.xrTable2.SizeF = new System.Drawing.SizeF(1128F, 53.125F);
            this.xrTable2.StylePriority.UseBorders = false;
            this.xrTable2.StylePriority.UseFont = false;
            this.xrTable2.StylePriority.UseTextAlignment = false;
            this.xrTable2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow2
            // 
            this.xrTableRow2.BackColor = System.Drawing.Color.Empty;
            this.xrTableRow2.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_Index,
            this.cell_JOCode,
            this.cell_CustomerAr,
            this.cell_Job,
            this.cell_Priority,
            this.cell_SalesEmpAr,
            this.cell_DeliverDate,
            this.cell_RegDate,
            this.cell_Status,
            this.cell_Notes});
            this.xrTableRow2.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrTableRow2.Name = "xrTableRow2";
            this.xrTableRow2.StylePriority.UseBackColor = false;
            this.xrTableRow2.StylePriority.UseFont = false;
            this.xrTableRow2.Weight = 1;
            // 
            // cell_Index
            // 
            this.cell_Index.Name = "cell_Index";
            this.cell_Index.Text = " ";
            this.cell_Index.Weight = 0.069214267546390873;
            // 
            // cell_JOCode
            // 
            this.cell_JOCode.Name = "cell_JOCode";
            this.cell_JOCode.Text = " ";
            this.cell_JOCode.Weight = 0.099502464635662036;
            // 
            // cell_CustomerAr
            // 
            this.cell_CustomerAr.Name = "cell_CustomerAr";
            this.cell_CustomerAr.Weight = 0.35666482883632722;
            // 
            // cell_Job
            // 
            this.cell_Job.Name = "cell_Job";
            this.cell_Job.Text = " ";
            this.cell_Job.Weight = 0.28174093813405932;
            // 
            // cell_Priority
            // 
            this.cell_Priority.Name = "cell_Priority";
            this.cell_Priority.Text = " ";
            this.cell_Priority.Weight = 0.16076964766285687;
            // 
            // cell_SalesEmpAr
            // 
            this.cell_SalesEmpAr.Name = "cell_SalesEmpAr";
            this.cell_SalesEmpAr.Text = " ";
            this.cell_SalesEmpAr.Weight = 0.25518151092049135;
            // 
            // cell_DeliverDate
            // 
            this.cell_DeliverDate.Name = "cell_DeliverDate";
            this.cell_DeliverDate.Text = " ";
            this.cell_DeliverDate.Weight = 0.19284780280667893;
            // 
            // cell_RegDate
            // 
            this.cell_RegDate.Name = "cell_RegDate";
            this.cell_RegDate.Text = " ";
            this.cell_RegDate.Weight = 0.17830321435245275;
            // 
            // cell_Status
            // 
            this.cell_Status.Name = "cell_Status";
            this.cell_Status.Text = " ";
            this.cell_Status.Weight = 0.20323660324509307;
            // 
            // cell_Notes
            // 
            this.cell_Notes.Name = "cell_Notes";
            this.cell_Notes.Text = " ";
            this.cell_Notes.Weight = 0.45253872185998734;
            // 
            // TopMargin
            // 
            this.TopMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.lbl_Department,
            this.xrLabel8,
            this.xrLabel5,
            this.lbl_DuedateTo,
            this.xrLabel3,
            this.lbl_DuedateFrom,
            this.lbl_RegdateTo,
            this.xrLabel1,
            this.lbl_RegdateFrom,
            this.xrLabel7,
            this.lblReportName,
            this.picLogo,
            this.lblCompName});
            this.TopMargin.HeightF = 164F;
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.TopMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // lbl_Department
            // 
            this.lbl_Department.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_Department.LocationFloat = new DevExpress.Utils.PointFloat(404.528F, 130.25F);
            this.lbl_Department.Name = "lbl_Department";
            this.lbl_Department.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Department.SizeF = new System.Drawing.SizeF(285.1233F, 24.49999F);
            this.lbl_Department.StylePriority.UseFont = false;
            this.lbl_Department.StylePriority.UseTextAlignment = false;
            this.lbl_Department.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // xrLabel8
            // 
            this.xrLabel8.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.xrLabel8.LocationFloat = new DevExpress.Utils.PointFloat(689.6513F, 130.25F);
            this.xrLabel8.Name = "xrLabel8";
            this.xrLabel8.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel8.SizeF = new System.Drawing.SizeF(163.0854F, 24.49999F);
            this.xrLabel8.StylePriority.UseFont = false;
            this.xrLabel8.StylePriority.UseTextAlignment = false;
            this.xrLabel8.Text = "الجهة";
            this.xrLabel8.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // xrLabel5
            // 
            this.xrLabel5.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.xrLabel5.LocationFloat = new DevExpress.Utils.PointFloat(539.3615F, 105.75F);
            this.xrLabel5.Name = "xrLabel5";
            this.xrLabel5.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel5.SizeF = new System.Drawing.SizeF(32.599F, 24.49999F);
            this.xrLabel5.StylePriority.UseFont = false;
            this.xrLabel5.StylePriority.UseTextAlignment = false;
            this.xrLabel5.Text = "الى";
            this.xrLabel5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // lbl_DuedateTo
            // 
            this.lbl_DuedateTo.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_DuedateTo.LocationFloat = new DevExpress.Utils.PointFloat(404.528F, 105.75F);
            this.lbl_DuedateTo.Name = "lbl_DuedateTo";
            this.lbl_DuedateTo.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_DuedateTo.SizeF = new System.Drawing.SizeF(134.8335F, 24.49999F);
            this.lbl_DuedateTo.StylePriority.UseFont = false;
            this.lbl_DuedateTo.StylePriority.UseTextAlignment = false;
            this.lbl_DuedateTo.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // xrLabel3
            // 
            this.xrLabel3.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.xrLabel3.LocationFloat = new DevExpress.Utils.PointFloat(689.6513F, 105.75F);
            this.xrLabel3.Name = "xrLabel3";
            this.xrLabel3.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel3.SizeF = new System.Drawing.SizeF(163.0854F, 24.49999F);
            this.xrLabel3.StylePriority.UseFont = false;
            this.xrLabel3.StylePriority.UseTextAlignment = false;
            this.xrLabel3.Text = "الاستحقاق في الفترة من";
            this.xrLabel3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // lbl_DuedateFrom
            // 
            this.lbl_DuedateFrom.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_DuedateFrom.LocationFloat = new DevExpress.Utils.PointFloat(571.9605F, 105.75F);
            this.lbl_DuedateFrom.Name = "lbl_DuedateFrom";
            this.lbl_DuedateFrom.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_DuedateFrom.SizeF = new System.Drawing.SizeF(117.6908F, 24.49999F);
            this.lbl_DuedateFrom.StylePriority.UseFont = false;
            this.lbl_DuedateFrom.StylePriority.UseTextAlignment = false;
            this.lbl_DuedateFrom.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // lbl_RegdateTo
            // 
            this.lbl_RegdateTo.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_RegdateTo.LocationFloat = new DevExpress.Utils.PointFloat(404.6374F, 81.25F);
            this.lbl_RegdateTo.Name = "lbl_RegdateTo";
            this.lbl_RegdateTo.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_RegdateTo.SizeF = new System.Drawing.SizeF(134.8335F, 24.49999F);
            this.lbl_RegdateTo.StylePriority.UseFont = false;
            this.lbl_RegdateTo.StylePriority.UseTextAlignment = false;
            this.lbl_RegdateTo.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // xrLabel1
            // 
            this.xrLabel1.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.xrLabel1.LocationFloat = new DevExpress.Utils.PointFloat(539.4709F, 81.25F);
            this.xrLabel1.Name = "xrLabel1";
            this.xrLabel1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel1.SizeF = new System.Drawing.SizeF(32.599F, 24.49999F);
            this.xrLabel1.StylePriority.UseFont = false;
            this.xrLabel1.StylePriority.UseTextAlignment = false;
            this.xrLabel1.Text = "الى";
            this.xrLabel1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // lbl_RegdateFrom
            // 
            this.lbl_RegdateFrom.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_RegdateFrom.LocationFloat = new DevExpress.Utils.PointFloat(572.0699F, 81.25F);
            this.lbl_RegdateFrom.Name = "lbl_RegdateFrom";
            this.lbl_RegdateFrom.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_RegdateFrom.SizeF = new System.Drawing.SizeF(117.5814F, 24.49999F);
            this.lbl_RegdateFrom.StylePriority.UseFont = false;
            this.lbl_RegdateFrom.StylePriority.UseTextAlignment = false;
            this.lbl_RegdateFrom.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // xrLabel7
            // 
            this.xrLabel7.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.xrLabel7.LocationFloat = new DevExpress.Utils.PointFloat(689.6513F, 81.25F);
            this.xrLabel7.Name = "xrLabel7";
            this.xrLabel7.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel7.SizeF = new System.Drawing.SizeF(163.0854F, 24.49999F);
            this.xrLabel7.StylePriority.UseFont = false;
            this.xrLabel7.StylePriority.UseTextAlignment = false;
            this.xrLabel7.Text = "التسجيل في الفترة من";
            this.xrLabel7.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // lblReportName
            // 
            this.lblReportName.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lblReportName.LocationFloat = new DevExpress.Utils.PointFloat(425.8899F, 40.00001F);
            this.lblReportName.Name = "lblReportName";
            this.lblReportName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblReportName.SizeF = new System.Drawing.SizeF(253.235F, 24.49998F);
            this.lblReportName.StylePriority.UseFont = false;
            this.lblReportName.StylePriority.UseTextAlignment = false;
            this.lblReportName.Text = "أوامر العمل";
            this.lblReportName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // picLogo
            // 
            this.picLogo.LocationFloat = new DevExpress.Utils.PointFloat(14.58333F, 10.00001F);
            this.picLogo.Name = "picLogo";
            this.picLogo.SizeF = new System.Drawing.SizeF(70F, 70F);
            this.picLogo.Sizing = DevExpress.XtraPrinting.ImageSizeMode.StretchImage;
            // 
            // lblCompName
            // 
            this.lblCompName.Font = new System.Drawing.Font("Times New Roman", 18F);
            this.lblCompName.LocationFloat = new DevExpress.Utils.PointFloat(252.7366F, 10.00001F);
            this.lblCompName.Name = "lblCompName";
            this.lblCompName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblCompName.SizeF = new System.Drawing.SizeF(600.0001F, 30F);
            this.lblCompName.StylePriority.UseFont = false;
            this.lblCompName.StylePriority.UseTextAlignment = false;
            this.lblCompName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // BottomMargin
            // 
            this.BottomMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrPageInfo1});
            this.BottomMargin.HeightF = 45F;
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.BottomMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrPageInfo1
            // 
            this.xrPageInfo1.Format = "Page {0} of {1} ";
            this.xrPageInfo1.LocationFloat = new DevExpress.Utils.PointFloat(510.0425F, 12.5F);
            this.xrPageInfo1.Name = "xrPageInfo1";
            this.xrPageInfo1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrPageInfo1.SizeF = new System.Drawing.SizeF(109.375F, 23F);
            this.xrPageInfo1.StylePriority.UseTextAlignment = false;
            this.xrPageInfo1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // PageHeader
            // 
            this.PageHeader.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable1});
            this.PageHeader.HeightF = 53.125F;
            this.PageHeader.Name = "PageHeader";
            // 
            // xrTable1
            // 
            this.xrTable1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top)
                        | DevExpress.XtraPrinting.BorderSide.Right)
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable1.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable1.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.xrTable1.Name = "xrTable1";
            this.xrTable1.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow1});
            this.xrTable1.SizeF = new System.Drawing.SizeF(1128F, 53.125F);
            this.xrTable1.StylePriority.UseBorders = false;
            this.xrTable1.StylePriority.UseFont = false;
            this.xrTable1.StylePriority.UseTextAlignment = false;
            this.xrTable1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow1
            // 
            this.xrTableRow1.BackColor = System.Drawing.Color.Moccasin;
            this.xrTableRow1.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell1,
            this.xrTableCell16,
            this.xrTableCell6,
            this.xrTableCell9,
            this.xrTableCell2,
            this.xrTableCell13,
            this.xrTableCell3,
            this.xrTableCell4,
            this.xrTableCell5,
            this.xrTableCell14});
            this.xrTableRow1.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrTableRow1.Name = "xrTableRow1";
            this.xrTableRow1.StylePriority.UseBackColor = false;
            this.xrTableRow1.StylePriority.UseFont = false;
            this.xrTableRow1.Weight = 1;
            // 
            // xrTableCell1
            // 
            this.xrTableCell1.Name = "xrTableCell1";
            this.xrTableCell1.Text = "م";
            this.xrTableCell1.Weight = 0.069214267546390873;
            // 
            // xrTableCell16
            // 
            this.xrTableCell16.Name = "xrTableCell16";
            this.xrTableCell16.Text = "رقم";
            this.xrTableCell16.Weight = 0.099502464635662036;
            // 
            // xrTableCell6
            // 
            this.xrTableCell6.Name = "xrTableCell6";
            this.xrTableCell6.Text = "العميل";
            this.xrTableCell6.Weight = 0.35666482883632722;
            // 
            // xrTableCell9
            // 
            this.xrTableCell9.Name = "xrTableCell9";
            this.xrTableCell9.Text = "العمل";
            this.xrTableCell9.Weight = 0.28174093813405932;
            // 
            // xrTableCell2
            // 
            this.xrTableCell2.Name = "xrTableCell2";
            this.xrTableCell2.Text = "الأهمية";
            this.xrTableCell2.Weight = 0.16076964766285687;
            // 
            // xrTableCell13
            // 
            this.xrTableCell13.Name = "xrTableCell13";
            this.xrTableCell13.Text = "المبيعات";
            this.xrTableCell13.Weight = 0.25518151092049135;
            // 
            // xrTableCell3
            // 
            this.xrTableCell3.Name = "xrTableCell3";
            this.xrTableCell3.Text = "تاريخ التسليم";
            this.xrTableCell3.Weight = 0.19284780280667893;
            // 
            // xrTableCell4
            // 
            this.xrTableCell4.Name = "xrTableCell4";
            this.xrTableCell4.Text = "تاريخ الاستحقاق";
            this.xrTableCell4.Weight = 0.17830321435245275;
            // 
            // xrTableCell5
            // 
            this.xrTableCell5.Name = "xrTableCell5";
            this.xrTableCell5.Text = "الحالة";
            this.xrTableCell5.Weight = 0.20323660324509307;
            // 
            // xrTableCell14
            // 
            this.xrTableCell14.Name = "xrTableCell14";
            this.xrTableCell14.Text = "ملاحظات";
            this.xrTableCell14.Weight = 0.45253872185998734;
            // 
            // ReportFooter
            // 
            this.ReportFooter.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable4});
            this.ReportFooter.HeightF = 25.41669F;
            this.ReportFooter.Name = "ReportFooter";
            // 
            // xrTable4
            // 
            this.xrTable4.LocationFloat = new DevExpress.Utils.PointFloat(828F, 0F);
            this.xrTable4.Name = "xrTable4";
            this.xrTable4.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow4});
            this.xrTable4.SizeF = new System.Drawing.SizeF(300F, 25.41669F);
            this.xrTable4.Visible = false;
            // 
            // xrTableRow4
            // 
            this.xrTableRow4.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_SalesEmpEn,
            this.cell_CustomerEn,
            this.xrTableCell15});
            this.xrTableRow4.Name = "xrTableRow4";
            this.xrTableRow4.Weight = 1;
            // 
            // cell_SalesEmpEn
            // 
            this.cell_SalesEmpEn.Name = "cell_SalesEmpEn";
            this.cell_SalesEmpEn.Text = "cell_SalesEmpEn";
            this.cell_SalesEmpEn.Weight = 1;
            // 
            // cell_CustomerEn
            // 
            this.cell_CustomerEn.Name = "cell_CustomerEn";
            this.cell_CustomerEn.Text = "cell_CustomerEn";
            this.cell_CustomerEn.Weight = 1;
            // 
            // xrTableCell15
            // 
            this.xrTableCell15.Name = "xrTableCell15";
            this.xrTableCell15.Text = "xrTableCell15";
            this.xrTableCell15.Weight = 1;
            // 
            // rpt_JO_JobOrderListDept
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail,
            this.TopMargin,
            this.BottomMargin,
            this.PageHeader,
            this.ReportFooter});
            this.Landscape = true;
            this.Margins = new System.Drawing.Printing.Margins(19, 22, 164, 45);
            this.PageHeight = 827;
            this.PageWidth = 1169;
            this.PaperKind = System.Drawing.Printing.PaperKind.A4;
            this.Version = "10.1";
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.XRLabel lblReportName;
        private DevExpress.XtraReports.UI.XRPictureBox picLogo;
        private DevExpress.XtraReports.UI.XRLabel lblCompName;
        private DevExpress.XtraReports.UI.XRPageInfo xrPageInfo1;
        private DevExpress.XtraReports.UI.PageHeaderBand PageHeader;
        private DevExpress.XtraReports.UI.XRTable xrTable1;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow1;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell4;
        private DevExpress.XtraReports.UI.ReportFooterBand ReportFooter;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell9;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell1;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell2;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell3;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell5;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell16;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell13;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell14;
        private DevExpress.XtraReports.UI.XRLabel lbl_RegdateTo;
        private DevExpress.XtraReports.UI.XRLabel xrLabel1;
        private DevExpress.XtraReports.UI.XRLabel lbl_RegdateFrom;
        private DevExpress.XtraReports.UI.XRLabel xrLabel7;
        private DevExpress.XtraReports.UI.XRLabel xrLabel5;
        private DevExpress.XtraReports.UI.XRLabel lbl_DuedateTo;
        private DevExpress.XtraReports.UI.XRLabel xrLabel3;
        private DevExpress.XtraReports.UI.XRLabel lbl_DuedateFrom;
        private DevExpress.XtraReports.UI.XRLabel lbl_Department;
        private DevExpress.XtraReports.UI.XRLabel xrLabel8;
        private DevExpress.XtraReports.UI.XRTable xrTable2;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow2;
        private DevExpress.XtraReports.UI.XRTableCell cell_Index;
        private DevExpress.XtraReports.UI.XRTableCell cell_JOCode;
        private DevExpress.XtraReports.UI.XRTableCell cell_Job;
        private DevExpress.XtraReports.UI.XRTableCell cell_Priority;
        private DevExpress.XtraReports.UI.XRTableCell cell_SalesEmpAr;
        private DevExpress.XtraReports.UI.XRTableCell cell_DeliverDate;
        private DevExpress.XtraReports.UI.XRTableCell cell_RegDate;
        private DevExpress.XtraReports.UI.XRTableCell cell_Status;
        private DevExpress.XtraReports.UI.XRTableCell cell_Notes;
        private DevExpress.XtraReports.UI.XRTableCell cell_CustomerAr;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell6;
        private DevExpress.XtraReports.UI.XRTable xrTable4;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow4;
        private DevExpress.XtraReports.UI.XRTableCell cell_SalesEmpEn;
        private DevExpress.XtraReports.UI.XRTableCell cell_CustomerEn;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell15;
    }
}
