﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraPrinting;
using DAL;
using DAL.Res;
using DevExpress.XtraReports.UI;
using Reports;

namespace Pharmacy.Forms
{
    public partial class frm_SL_POS_Resturant_ : DevExpress.XtraEditors.XtraForm
    {
        List<DAL.IC_UOM> uom_list;
        List<IC_Store> stores_table;

        DataTable dtSL_Details = new DataTable();

        List<SL_Customer_Info> lst_Customers = new List<SL_Customer_Info>();
        DataTable dtPayAccounts = new DataTable();

        DAL.ERPDataContext DB;

        int invoiceId = 0;
        int customerId = 0;

        DAL.ST_PrintInvoice printOptions;
        DAL.ST_CompanyInfo comp;

        DAL.IC_PriceLevel CustomerPriceLevel = null;

        bool Saved_Successfully = false;
        private DataTable dt;

        public frm_SL_POS_Resturant_()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
        }


        private void frm_SL_POS_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            #region Tax_Available
            if (Shared.TaxAvailable == false)
            {
                txt_TaxRatio.Enabled = txt_TaxValue.Enabled = false;
            }
            #endregion

            //LoadPrivilege();
            Reset();

            BindDataSources();
            //ErpUtils.Allow_Incremental_Search(lkp_Customers);
            ErpUtils.ColumnChooser(grdPrInvoice);

            NewInvoice();

            DB = new ERPDataContext();
            printOptions = DB.ST_PrintInvoices.FirstOrDefault();
            comp = DB.ST_CompanyInfos.FirstOrDefault();
        }

        private void frm_SL_POS_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Home && e.Modifiers == Keys.Control)
            {
                txtItemCode.Focus();
            }
            if (e.KeyCode == Keys.Insert)
            {
                FocusOnGrid();
            }
            if (e.KeyCode == Keys.F5)
            {
                txtDiscountRatio.Focus();
            }
            if (e.KeyCode == Keys.F6)
            {
                txt_TaxRatio.Focus();
            }

            if (e.KeyCode == Keys.F2)
            {
                txtPaid.Focus();
            }
            if (e.KeyCode == Keys.F4)
            {
                txtQty.Focus();
            }

            if (e.KeyCode == Keys.End && e.Modifiers == Keys.Control)
            {
                lkp_Customers.Focus();
            }
            if (e.KeyCode == Keys.F7)
            {
                btnQuote_Click(null, EventArgs.Empty);
            }
        }

        private void frm_SL_POS_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgAskExit : ResSLAr.MsgAskExit,
                Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.YesNo, MessageBoxIcon.Question)
                != DialogResult.Yes)
                e.Cancel = true;
        }


        private void txtItemCode_KeyDown(object sender, KeyEventArgs e)
        {
            //nothing
            if (string.IsNullOrEmpty(txtItemCode.Text.Trim()) && dtSL_Details.Rows.Count < 1)
                return;

            //decrease or delete 
            if (string.IsNullOrEmpty(txtItemCode.Text.Trim()) && e != null && e.KeyCode == Keys.Delete)
            {
                int index = dtSL_Details.Rows.Count - 1;
                if (index < 0)
                    return;

                decimal qty = Convert.ToDecimal(dtSL_Details.Rows[index]["Qty"]);
                if (qty > 1)
                {
                    dtSL_Details.Rows[index]["Qty"] = qty - 1;
                    dtSL_Details.Rows[index]["TotalSellPrice"] = Convert.ToDecimal(dtSL_Details.Rows[index]["Qty"]) *
                                                              Convert.ToDecimal(dtSL_Details.Rows[index]["SellPrice"]);
                }
                else
                    dtSL_Details.Rows[index].Delete();
                CalcPrices();
                return;
            }

            #region Search

            if (e != null && e.KeyCode == Keys.Enter)
            {
                DB = new DAL.ERPDataContext();
                DAL.IC_Item item = null;

                int code1 = 0;
                string itemCode = txtItemCode.Text.Trim();

                if (string.IsNullOrEmpty(itemCode))
                {
                    int index = dtSL_Details.Rows.Count - 1;
                    if (index >= 0)
                    {
                        item = DB.IC_Items.Where(x => x.ItemId == Convert.ToInt32(dtSL_Details.Rows[index]["ItemId"])).FirstOrDefault();
                        if (Validate_Sell_Without_Balance(item, Convert.ToDecimal(txtQty.EditValue)) == true)
                        {
                            dtSL_Details.Rows[index]["Qty"] = Convert.ToDecimal(dtSL_Details.Rows[index]["Qty"]) +
                                Convert.ToDecimal(txtQty.EditValue);
                            dtSL_Details.Rows[index]["TotalSellPrice"] = Convert.ToDecimal(dtSL_Details.Rows[index]["Qty"]) *
                                                              Convert.ToDecimal(dtSL_Details.Rows[index]["SellPrice"]);
                        }
                        CalcPrices();
                        return;
                    }
                }

                Int32.TryParse(itemCode, out code1);
                if (code1 > 0)
                {
                    item = (from i in DB.IC_Items
                            where i.ItemType != (int)ItemType.MatrixParent
                            //&& i.ItemType != (int)ItemType.Service
                            && i.ItemType != (int)ItemType.Subtotal
                            && i.ItemCode1 == code1
                            select i).FirstOrDefault();
                }

                if (item == null && !string.IsNullOrEmpty(itemCode))
                {
                    item = (from i in DB.IC_Items
                            where i.ItemType != (int)ItemType.MatrixParent
                            //&& i.ItemType != (int)ItemType.Service
                            && i.ItemType != (int)ItemType.Subtotal
                            && i.IC_InternationalCodes.Select(x => x.InternationalCode)
                        .Contains(itemCode)
                            select i).FirstOrDefault();

                    if (item == null)
                    {
                        item = (from i in DB.IC_Items
                                where i.ItemType != (int)ItemType.MatrixParent
                            //&& i.ItemType != (int)ItemType.Service
                            && i.ItemType != (int)ItemType.Subtotal
                            && i.ItemCode2 == itemCode
                                select i).FirstOrDefault();
                    }
                }

                #region Mizan
                if (item == null && !string.IsNullOrEmpty(itemCode))
                {
                    if (itemCode.Length == 13)
                    {
                        if (itemCode.StartsWith("99") == true)
                        {
                            item = (from i in DB.IC_Items
                                    where i.ItemType != (int)ItemType.MatrixParent
                            //&& i.ItemType != (int)ItemType.Service
                            && i.ItemType != (int)ItemType.Subtotal
                            && i.ItemCode1 == Convert.ToInt32(itemCode.Substring(0, 7))
                                    select i).FirstOrDefault();

                            if (item == null)
                            {
                                item = (from i in DB.IC_Items
                                        where i.ItemType != (int)ItemType.MatrixParent
                            //&& i.ItemType != (int)ItemType.Service
                            && i.ItemType != (int)ItemType.Subtotal
                            && i.ItemCode2 == itemCode.Substring(0, 7)
                                        select i).FirstOrDefault();
                            }
                            if (item == null)
                            {
                                item = (from i in DB.IC_Items
                                        where i.ItemType != (int)ItemType.MatrixParent
                            //&& i.ItemType != (int)ItemType.Service
                            && i.ItemType != (int)ItemType.Subtotal
                            && i.IC_InternationalCodes.Select(x => x.InternationalCode)
                                        .Contains(itemCode.Substring(0, 7))
                                        select i).FirstOrDefault();
                            }
                            try
                            {
                                string st_price = itemCode.Substring(7, 5);
                                double price = Convert.ToDouble(st_price.Substring(0, 3) + "." + st_price.Substring(3, 2));
                                double qty = price / decimal.ToDouble(item.SmallUOMPrice);
                                txtQty.EditValue = qty;
                            }
                            catch { }
                        }
                    }
                }
                #endregion

                #endregion

                #region insert or increase
                if (txtQty.EditValue == null || txtQty.Text.Trim() == string.Empty)
                {
                    txtQty.EditValue = 1;
                    txtQty.Text = "1";
                }

                if (item != null && item.ItemId > 0)
                {
                    if (Validate_Sell_Without_Balance(item, Convert.ToDecimal(txtQty.EditValue)) == false)
                        return;

                    var xx = (from DataRow dr in dtSL_Details.Rows
                              where dr.RowState != DataRowState.Deleted
                              where Convert.ToInt32(dr["ItemId"]) == item.ItemId
                              select dr).Count();

                    decimal uom_price = MyHelper.GetPriceLevelSellPrice(CustomerPriceLevel, item, 0);

                    if (xx < 1)
                    {
                        decimal sellprice = MyHelper.Get_ItemPricesPerQty(item.ItemId, Convert.ToDecimal(txtQty.EditValue), 0, 1, 1);
                        if (sellprice == 0)
                            sellprice = uom_price;
                        dtSL_Details.Rows.Add(
                            item.ItemId,
                            item.ItemCode1,
                            item.ItemNameAr,
                            item.SmallUOM,
                            uom_list.Where(u => u.UOMId == item.SmallUOM).Select(u => u.UOM).First(),
                            Convert.ToDecimal(txtQty.EditValue),
                            sellprice,
                            sellprice * Convert.ToDecimal(txtQty.EditValue),
                            (item.ItemType == (int)ItemType.Service),
                            item.IsExpire
                            );
                    }
                    else
                    {
                        DataRow row = dtSL_Details.Rows.Find(item.ItemId);
                        int index = dtSL_Details.Rows.IndexOf(row);
                        dtSL_Details.Rows[index]["Qty"] = Convert.ToDecimal(dtSL_Details.Rows[index]["Qty"]) +
                            Convert.ToDecimal(txtQty.EditValue);
                        decimal sellprice = MyHelper.Get_ItemPricesPerQty(item.ItemId, Convert.ToDecimal(dtSL_Details.Rows[index]["Qty"]), 0, 1, 1);
                        if (sellprice == 0)
                            sellprice = Convert.ToDecimal(dtSL_Details.Rows[index]["SellPrice"]);

                        dtSL_Details.Rows[index]["SellPrice"] = sellprice;
                    }

                    txtItemCode.Text = string.Empty;
                }
                #endregion

                txtQty.EditValue = 1;
                txtQty.Text = "1";
            }

            CalcPrices();
            //Get_TotalAccount();
        }

        private void txtQty_Leave(object sender, EventArgs e)
        {
            if (txtQty.EditValue == null || txtQty.Text.Trim() == string.Empty)
            {
                txtQty.EditValue = 1;
                txtQty.Text = "1";
            }
        }

        private void lkpItem1_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                txtQty.Focus();
            }
        }
        private void lkpItem1_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                txtQty.Focus();
            }
        }

        private void txtQty_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                txtItemCode.Focus();
            }
        }

        private void txtDiscountRatio_Leave(object sender, EventArgs e)
        {
            decimal discount_ratio = Convert.ToDecimal(txtDiscountRatio.EditValue) / 100;
            decimal Discount_value = Convert.ToDecimal(col_TotalSellPrice.SummaryItem.SummaryValue) * discount_ratio;
            txtDiscountValue.EditValue = decimal.ToDouble(Discount_value);
        }

        private void txtDiscountExpenses_EditValueChanged(object sender, EventArgs e)
        {
            if (Convert.ToDecimal(txtNet.EditValue) == Convert.ToDecimal(txtPaid.EditValue))
            {
                txtPaid.EditValue = txtNet.EditValue =
                    decimal.ToDouble(Convert.ToDecimal(col_TotalSellPrice.SummaryItem.SummaryValue) -
                    Convert.ToDecimal(txtDiscountValue.EditValue) +
                    Convert.ToDecimal(txtExpenses.EditValue) +
                    Convert.ToDecimal(txt_TaxValue.EditValue));
            }
            else if (Convert.ToDecimal(txtNet.EditValue) != Convert.ToDecimal(txtPaid.EditValue))
            {
                txtNet.EditValue = decimal.ToDouble(Convert.ToDecimal(col_TotalSellPrice.SummaryItem.SummaryValue) -
                    Convert.ToDecimal(txtDiscountValue.EditValue) +
                    Convert.ToDecimal(txtExpenses.EditValue) +
                    Convert.ToDecimal(txt_TaxValue.EditValue));

                txtRemains.EditValue = decimal.ToDouble(Convert.ToDecimal(txtPaid.EditValue) -
                    Convert.ToDecimal(txtNet.EditValue));
            }
        }

        private void txt_paid_EditValueChanged(object sender, EventArgs e)
        {
            txtRemains.EditValue = decimal.ToDouble(Convert.ToDecimal(txtPaid.EditValue) -
                    Convert.ToDecimal(txtNet.EditValue));
        }

        private void txtDiscountRatio_Spin(object sender, DevExpress.XtraEditors.Controls.SpinEventArgs e)
        {
            if (e.IsSpinUp == false)
            {
                if (Convert.ToDecimal(((TextEdit)sender).Text) == 0)
                    e.Handled = true;
            }
        }

        private void txtRemains_EditValueChanged(object sender, EventArgs e)
        {
            if (Convert.ToDecimal(txtRemains.EditValue) < 0)
                lblRemains.ForeColor = txtRemains.BackColor = Color.Red;
            else
            {
                lblRemains.ForeColor = Color.Black;
                txtRemains.BackColor = Color.White;
            }
        }

        private void txtNumberOnly_KeyPress(object sender, KeyPressEventArgs e)
        {
            //accepts only integer values           
            if (char.IsNumber(e.KeyChar) || e.KeyChar == '.')
            {
            }
            else
            {
                e.Handled = e.KeyChar != (char)Keys.Back;
            }
        }


        private void barBtn_Save_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (!ValidData())
                return;

            Save_Invoice();

            if (Saved_Successfully)
            {
                if (printOptions != null && comp != null && printOptions.PrintReceiptPOS == true)
                {
                    if (Convert.ToDecimal(txtNet.EditValue) >= printOptions.MinValue)
                    {
                        try
                        {
                            //new Reports.rpt_Printed_Receipt(dtSL_Details, Convert.ToDecimal(txtNet.EditValue), " رقم الفاتورة " + txtInvoiceCode.Text, printOptions, comp).ShowPreview();
                            Reports.rpt_Printed_Receipt r = new Reports.rpt_Printed_Receipt();

                            if (System.IO.File.Exists(Shared.ReportsPath + "rpt_Printed_Receipt.repx"))
                                r.LoadLayout(Shared.ReportsPath + "rpt_Printed_Receipt.repx");

                            r.Load_Receipt(dtSL_Details, lkp_Customers.Text, txtInvoiceCode.Text, dtInvoiceDate.Text, lkpStore.Text, lkp_Drawers.Text,
                                txtNotes.Text, txtNet.Text, Shared.UserName);
                            r.Print(Shared.ReceiptPrinterName);
                        }
                        catch
                        { }
                    }
                }
                barBtnCancel.PerformClick();
            }

            barBtnCancel.PerformClick();
            //GetNonArchivedInvoices();
        }

        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtnCancel_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            invoiceId = 0;
            NewInvoice();
        }

        private void btnQuote_Click(object sender, EventArgs e)
        {
            frm_SL_QuoteOpen f = new frm_SL_QuoteOpen();
            f.ShowDialog();
            int invId = f.inv_id;
            f.Close();

            if (invId > 0)
            {
                DAL.ERPDataContext DB = new DAL.ERPDataContext();
                var qt = (from q in DB.SL_Quotes
                          where q.SL_QuoteId == invId
                          select q).Single();

                var qtDtl = (from q in DB.SL_QuoteDetails
                             where q.SL_QuoteId == invId
                             select q).ToList();

                //txtTotal.EditValue = decimal.ToDouble(qt.Net - qt.Expenses + qt.DiscountValue);
                txtDiscountRatio.EditValue = decimal.ToDouble(qt.DiscountRatio);
                txtDiscountValue.EditValue = decimal.ToDouble(qt.DiscountValue);
                txtExpenses.EditValue = decimal.ToDouble(qt.Expenses);
                txtNet.EditValue = decimal.ToDouble(qt.Net);

                dtSL_Details.Clear();
                decimal totalQty = 0;
                foreach (var Dt in qtDtl)
                {
                    var item = DB.IC_Items.Where(i => i.ItemType != (int)ItemType.MatrixParent
                            //&& i.ItemType != (int)ItemType.Service
                            && i.ItemType != (int)ItemType.Subtotal
                            && i.ItemId == Dt.ItemId).SingleOrDefault();

                    decimal smallUomQty = MyHelper.CalculateUomQty(Dt.Qty, Dt.UOMIndex, MyHelper.FractionToDouble(item.MediumUOMFactor), MyHelper.FractionToDouble(item.LargeUOMFactor));

                    var xx = (from DataRow dr in dtSL_Details.Rows
                              where dr.RowState != DataRowState.Deleted
                              where Convert.ToInt32(dr["ItemId"]) == item.ItemId
                              select dr).Count();
                    if (xx == 0)
                    {
                        dtSL_Details.Rows.Add(Dt.ItemId,
                            item.ItemCode1,
                            item.ItemNameAr,
                            item.SmallUOM,
                            uom_list.Where(u => u.UOMId == item.SmallUOM).Select(u => u.UOM).First(),
                            smallUomQty,
                            Dt.SellPrice,
                            Dt.TotalSellPrice,
                            (item.ItemType == (int)ItemType.Service),
                            item.IsExpire);

                        totalQty += smallUomQty;
                    }
                    else
                    {
                        DataRow row = dtSL_Details.Rows.Find(item.ItemId);
                        int index = dtSL_Details.Rows.IndexOf(row);
                        dtSL_Details.Rows[index]["Qty"] = Convert.ToDecimal(dtSL_Details.Rows[index]["Qty"]) +
                            smallUomQty;

                        dtSL_Details.Rows[index]["SellPrice"] = Dt.SellPrice;
                        dtSL_Details.Rows[index]["TotalSellPrice"] = Convert.ToDecimal(dtSL_Details.Rows[index]["Qty"]) * Dt.SellPrice;
                    }
                }
                //txtTotalQty.EditValue = decimal.ToDouble(totalQty);
            }

            txtItemCode.Focus();
        }

        private void btnAddCustomer_Click(object sender, EventArgs e)
        {
            int customers_count = lkp_Customers.Properties.View.RowCount;
            int LastCustId = 0;

            new frm_SL_Customer().ShowDialog();
            int? goldencustomer = MyHelper.GetCustomers(out lst_Customers, Shared.user.DefaultCustGrp, out LastCustId);
            if (lst_Customers.Count > customers_count)
            {
                lkp_Customers.Properties.DataSource = lst_Customers;
                //lkp_Customers.EditValue = lkp_Customers.Properties.GetKeyValue(lkp_Customers.Properties.View.RowCount - 1);
                lkp_Customers.EditValue = LastCustId;
            }
        }

        private void mi_frm_IC_Item_Click(object sender, EventArgs e)
        {
            var view = grdPrInvoice.FocusedView as GridView;
            var item_id = view.GetFocusedRowCellValue("ItemId");
            if (item_id == null || item_id == DBNull.Value || Convert.ToInt32(item_id) <= 0)
                return;


            new frm_IC_Item(Convert.ToInt32(item_id), FormAction.Edit).ShowDialog();
        }

        private void lkpStore_EditValueChanging(object sender, DevExpress.XtraEditors.Controls.ChangingEventArgs e)
        {
            if (dtSL_Details.Rows.Count > 0)
                e.Cancel = true;
        }


        private void gridView2_CustomUnboundColumnData(object sender, CustomColumnDataEventArgs e)
        {
            if (e.ListSourceRowIndex < 0)
                return;
            e.Value = e.RowHandle() + 1;
        }

        private void gridView2_CustomRowFilter(object sender, RowFilterEventArgs e)
        {
            //try
            //{
            //    GridView gridView = grdPrInvoice.FocusedView as GridView;
            //    int gridItem = Convert.ToInt32(gridView.GetFocusedRowCellValue("ItemId"));
            //    //int gridUomIndex = Convert.ToByte((from DataRow dr in dtUOM.Rows
            //    //                                   where dr["UomId"] == gridView.GetFocusedRowCellValue("UOM")
            //    //                                   select dr["Index"]).FirstOrDefault());
            //    int gridUomIndex = Convert.ToInt32(gridView.GetFocusedRowCellValue("UomIndex"));

            //    var view = (sender as GridView);
            //    int itemId = Convert.ToInt32(view.GetRowCellValue(e.ListSourceRow, "ItemId"));
            //    int uomIndex = Convert.ToInt32(view.GetRowCellValue(e.ListSourceRow, "uomIndex"));

            //    if (itemId == gridItem && uomIndex == gridUomIndex)
            //    {
            //        e.Visible = true;
            //        e.Handled = true;
            //    }
            //    else
            //    {
            //        e.Visible = false;
            //        e.Handled = true;
            //    }
            //}
            //catch
            //{
            //}

        }

        private void gridView2_KeyDown(object sender, KeyEventArgs e)
        {
            GridView view = grdPrInvoice.FocusedView as GridView;
            if (view.FocusedRowHandle < 0)
                return;

            if (e.KeyCode == Keys.Delete)
            {
                dtSL_Details.Rows[view.FocusedRowHandle].Delete();
                CalcPrices();
            }
            else if (e.KeyCode == Keys.Back)
            {
                decimal qty = Convert.ToDecimal(dtSL_Details.Rows[view.FocusedRowHandle]["Qty"]);
                if (qty > 1)
                {
                    dtSL_Details.Rows[view.FocusedRowHandle]["Qty"] = qty - 1;
                    dtSL_Details.Rows[view.FocusedRowHandle]["TotalSellPrice"] =
                        Convert.ToDecimal(dtSL_Details.Rows[view.FocusedRowHandle]["Qty"]) *
                                                              Convert.ToDecimal(dtSL_Details.Rows[view.FocusedRowHandle]["SellPrice"]);
                }
                else
                    dtSL_Details.Rows[view.FocusedRowHandle].Delete();
                CalcPrices();
            }
        }

        private void gridView2_CustomColumnDisplayText(object sender, CustomColumnDisplayTextEventArgs e)
        {
            if (e.Column.FieldName == "Qty" || e.Column.FieldName == "SellPrice" || e.Column.FieldName == "TotalSellPrice")
                e.DisplayText = Convert.ToDouble(e.Value).ToString();
        }


        private void BindDataSources()
        {
            DB = new DAL.ERPDataContext();

            #region Get Stores
            int defaultStoreId = 0;
            stores_table = MyHelper.Get_Stores(false, out defaultStoreId, Shared.user.UserChangeStore, Shared.user.DefaultStore,
                Shared.UserId);
            lkpStore.Properties.DataSource = stores_table;

            lkpStore.Properties.DisplayMember = "StoreNameAr";
            lkpStore.Properties.ValueMember = "StoreId";
            lkpStore.EditValue = defaultStoreId;
            #endregion

            //disable dicount if user has no privilege
            if (Shared.user.UserCanWriteDiscount == false)
                txtDiscountRatio.Enabled = txtDiscountValue.Enabled = false;

            //disable on credit invoice
            if (Shared.user.UserMakeOnCreditInv == false)
                txtPaid.Enabled = txtRemains.Enabled = false;

            #region Get_PayAccounts
            int defaultAcc = HelperAcc.LoadPayAccounts(dtPayAccounts, Shared.user.UserChangeDrawer, Shared.user.DefaultDrawer,
                Shared.IsEnglish);

            lkp_Drawers.Properties.ValueMember = "AccountId";
            lkp_Drawers.Properties.DisplayMember = "AccountName";
            lkp_Drawers.EditValue = defaultAcc;
            lkp_Drawers.Properties.DataSource = dtPayAccounts;
            #endregion


            #region dtPR_Details
            dtSL_Details.Columns.Clear();
            dtSL_Details.Columns.Add("ItemId");
            dtSL_Details.Columns.Add("ItemCode1");
            dtSL_Details.Columns.Add("ItemName");
            dtSL_Details.Columns.Add("UOMId");
            dtSL_Details.Columns.Add("UOMName");
            dtSL_Details.Columns.Add("Qty", typeof(decimal)).AllowDBNull = false;
            dtSL_Details.Columns.Add("SellPrice", typeof(decimal)).AllowDBNull = false;
            dtSL_Details.Columns.Add("TotalSellPrice", typeof(decimal)).AllowDBNull = false;
            dtSL_Details.Columns.Add("IsService");
            dtSL_Details.Columns.Add("IsExpire");

            DataColumn[] keys = new DataColumn[1];
            keys[0] = dtSL_Details.Columns[0];
            dtSL_Details.PrimaryKey = keys;

            grdPrInvoice.DataSource = dtSL_Details;
            #endregion

            #region GetCustomers
            int LastCustId = 0;
            int? goldencustomer = MyHelper.GetCustomers(out lst_Customers, Shared.user.DefaultCustGrp, out LastCustId);

            lkp_Customers.Properties.DisplayMember = "CusNameAr";
            lkp_Customers.Properties.ValueMember = "CustomerId";
            lkp_Customers.Properties.DataSource = lst_Customers;
            lkp_Customers.EditValue = goldencustomer;
            #endregion

            uom_list = (from u in DB.IC_UOMs
                        select u).ToList();


            #region ListViewItems

            var itms = (from d in DB.IC_Items
                        where d.IsPos == true
                        where d.ItemType != (int)ItemType.MatrixParent
                        where d.ItemType != (int)ItemType.Subtotal
                        group d by new { d.IC_Category.CategoryNameAr ,d.Category} into grp
                        orderby grp.Key.Category
                        select new
                        {
                            key = grp.Key,
                            list = grp,// d.ItemId,
                            //d.ItemCode1,
                            //d.ItemNameAr,
                            //d.IC_Category.CategoryNameAr,
                            //d.SmallUOMPrice,
                        }).ToList();

            dt = new DataTable();
            dt.Columns.Add("key");
            dt.Columns.Add("list").DataType = typeof(List<IC_Item>);


            foreach (var item in itms)
            {
                DataRow dr = dt.NewRow();
                dr["key"] = item.key.CategoryNameAr;
                dr["list"] = item.list.ToList();
                dt.Rows.Add(dr);

                TileGroup newGroup = new TileGroup
                {
                    Name = item.list.First().IC_Category.CategoryNameAr,
                    Text = item.list.First().IC_Category.CategoryNameAr
                };

                tileControl1.Groups.Add(newGroup);
                //tileControl1.Orientation = Orientation.Vertical;
                //tileControl1.ShowGroupText = tileControl1.ShowText = false;
                //tileControl1.IndentBetweenGroups = 15;

                TileItem newTileItem = new TileItem();
                newTileItem.ItemSize = TileItemSize.Medium;
                newTileItem.Text = item.key.CategoryNameAr;
                newTileItem.TextAlignment = TileItemContentAlignment.MiddleCenter;

                newGroup.Items.Add(newTileItem);
            }

            if (tileControl1.Groups.Count > 0 && tileControl1.Groups[0].Items.Count > 0)
                tileControl1.Groups[0].Items[0].PerformItemClick();
            #endregion
        }

        //private void ClickItem(object sender, TileItemEventArgs e)
        //{
        //    try
        //    {
        //        var item = (TileItem)sender;
        //        var x = (from DataRow r in dt.Rows
        //                 where r["key"].ToString() == item.Text
        //                 select r["list"]).ToList();
        //        if (x == null || x.Count == 0)
        //            return;

        //        foreach (var i in ((List<IC_Item>)x[0]))
        //        {
        //            TileItem newTileItem = new TileItem();
        //            newTileItem.ItemSize = TileItemSize.Medium;
        //            newTileItem.ItemClick += SubItemClick; ;

        //            TileItemElement elementDescription = new TileItemElement();
        //            elementDescription.Text = i.ItemNameAr;
        //            elementDescription.TextAlignment = TileItemContentAlignment.MiddleCenter;

        //            TileItemElement elementValue = new TileItemElement();
        //            elementValue.Text = Math.Round(i.SmallUOMPrice,3).ToString();
        //            elementValue.TextAlignment = TileItemContentAlignment.BottomCenter;

        //            newTileItem.Elements.Add(elementDescription);
        //            newTileItem.Elements.Add(elementValue);

        //            newTileItem.ItemClick += ClickItem;
        //        }
        //    }
        //    catch (Exception xx) { MessageBox.Show(xx.Message); }
        //}

        private void SubItemClick(object sender, TileItemEventArgs e)
        {
            txtItemCode.EditValue = e.Item.Elements[1].Text;
            txtItemCode_KeyDown(sender, null);
            MessageBox.Show("Test");
        }

        private void Reset()
        {
            txtNotes.EditValue = null;

            txtExpenses.EditValue = txtDiscountRatio.EditValue = txtDiscountValue.EditValue = 0;

            txt_TaxRatio.EditValue = txt_TaxValue.EditValue = 0;

            //txtTotal.EditValue = 0;
            //txtTotalQty.EditValue = 0;
            txtNet.EditValue = 0;
            txtPaid.EditValue = 0;
            txtRemains.EditValue = 0;
            txtQty.EditValue = 1;
            dtInvoiceDate.DateTime = MyHelper.Get_Server_DateTime();

            dtSL_Details.Rows.Clear();
        }

        private void CalcPrices()
        {
            //calculate prices
            decimal Tsl = 0;
            decimal TQty = 0;
            for (int x = 0; x < dtSL_Details.Rows.Count; x++)
            {
                decimal qty = Convert.ToDecimal(dtSL_Details.Rows[x]["Qty"]);
                decimal Sl = Convert.ToDecimal(dtSL_Details.Rows[x]["SellPrice"]);
                dtSL_Details.Rows[x]["TotalSellPrice"] = decimal.ToDouble(qty * Sl);
                Tsl += (qty * Sl);
                TQty += qty;
            }

            //txtTotalQty.EditValue = decimal.ToDouble(TQty);

            if (Convert.ToDecimal(txtNet.EditValue) == Convert.ToDecimal(txtPaid.EditValue))
            {
                //txtTotal.EditValue = decimal.ToDouble(Tsl);

                txtNet.EditValue = decimal.ToDouble(Convert.ToDecimal(col_TotalSellPrice.SummaryItem.SummaryValue) -
                    Convert.ToDecimal(txtDiscountValue.EditValue) +
                    Convert.ToDecimal(txtExpenses.EditValue) +
                    Convert.ToDecimal(txt_TaxValue.EditValue));
                txtPaid.EditValue = txtNet.EditValue;
                txtRemains.EditValue = 0;
            }
            else if (Convert.ToDecimal(txtNet.EditValue) != Convert.ToDecimal(txtPaid.EditValue))
            {
                //txtTotal.EditValue = decimal.ToDouble(Tsl);

                txtNet.EditValue = decimal.ToDouble(Convert.ToDecimal(col_TotalSellPrice.SummaryItem.SummaryValue) -
                    Convert.ToDecimal(txtDiscountValue.EditValue) +
                    Convert.ToDecimal(txtExpenses.EditValue) +
                    Convert.ToDecimal(txt_TaxValue.EditValue));

                txtRemains.EditValue = decimal.ToDouble(Convert.ToDecimal(txtPaid.EditValue) -
                    Convert.ToDecimal(txtNet.EditValue));
            }
        }

        private void FocusOnGrid()
        {
            grdPrInvoice.Focus();
            var view = (grdPrInvoice.FocusedView as DevExpress.XtraGrid.Views.Grid.GridView);
            view.FocusedRowHandle = view.RowCount - 1;
        }

        private bool ValidData()
        {
            txtDiscountValue.Focus();
            gv_Details.UpdateCurrentRow();
            gv_Details.RefreshRow(gv_Details.FocusedRowHandle);

            //can't post in closed period
            if (ErpHelper.CanSaveInClsedPeriod(dtInvoiceDate.DateTime.Date,
                Shared.st_Store.ClosePeriodDate, Shared.user.EditInClosedPeriod) == false)
                return false;

            if (Shared.StockIsPeriodic && Convert.ToDecimal(txtDiscountValue.EditValue) > 0 && Shared.st_Store.SalesDiscountAcc.HasValue == false)
            {
                //check sales discount Account

                XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.MsgDiscountAcc : ResAr.MsgDiscountAcc,
                        Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);

                return false;
            }

            if (lkp_Customers.EditValue == null)
            {
                XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.MsgSelectCustomer : ResAr.MsgSelectCustomer,
                        Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                lkp_Customers.Focus();

                return false;
            }

            if (Convert.ToDecimal(txt_TaxValue.EditValue) > 0 && Shared.st_Store.TaxAcc.HasValue == false)
            {
                //يجب تحديد حساب الضرائب
                //check sales tax Account

                XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.MsgSalesTaxAcc : ResAr.MsgSalesTaxAcc,
                        Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);

                return false;
            }

            if (string.IsNullOrEmpty(txtInvoiceCode.Text.Trim()))
            {
                XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.txtValidateInvNumber : ResSLAr.txtValidateInvNumber
                    , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                txtInvoiceCode.Focus();
                return false;
            }

            int ZeroQty = (from DataRow dr in dtSL_Details.Rows
                           where Convert.ToDecimal(dr["Qty"]) <= 0
                           select dr).Count();
            if (ZeroQty > 0)
            {
                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResAccEn.MsgIncorrectData : ResAccAr.MsgIncorrectData,
                    Shared.IsEnglish == true ? ResAccEn.MsgTError : ResAccAr.MsgTError,
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            if (Convert.ToDecimal(txtDiscountValue.EditValue) ==
                Convert.ToDecimal(col_TotalSellPrice.SummaryItem.SummaryValue))
            {
                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResAccEn.MsgIncorrectData : ResAccAr.MsgIncorrectData,
                    Shared.IsEnglish == true ? ResAccEn.MsgTError : ResAccAr.MsgTError,
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);

                txtDiscountValue.Focus();
                return false;
            }

            int deleted_rows = 0;
            if (dtSL_Details.GetChanges(DataRowState.Deleted) != null)
                deleted_rows = dtSL_Details.GetChanges(DataRowState.Deleted).Rows.Count;

            if (dtSL_Details.Rows.Count - deleted_rows <= 0)
            {
                XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.txtValidateNoRows : ResSLAr.txtValidateNoRows,
                    "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                grdPrInvoice.Focus();
                return false;
            }

            if (dtSL_Details.Rows[dtSL_Details.Rows.Count - 1].RowState != DataRowState.Deleted
                && dtSL_Details.Rows[dtSL_Details.Rows.Count - 1]["ItemId"] == DBNull.Value)
            {
                dtSL_Details.Rows[dtSL_Details.Rows.Count - 1].Delete();
                grdPrInvoice.RefreshDataSource();
            }

            if (!grdPrInvoice.DefaultView.UpdateCurrentRow())
                return false;

            return true;
        }


        private void Save_Invoice()
        {
            Saved_Successfully = false;

            GridView view = grdPrInvoice.FocusedView as GridView;

            //save invoice
            DB = new DAL.ERPDataContext();
            DAL.SL_Invoice sl;

            if (invoiceId == 0)
                sl = new DAL.SL_Invoice();
            else
                sl = DB.SL_Invoices.Where(x => x.SL_InvoiceId == invoiceId).FirstOrDefault();

            sl.CustomerId = Convert.ToInt32(lkp_Customers.EditValue);
            sl.InvoiceCode = txtInvoiceCode.Text.Trim();
            sl.InvoiceDate = dtInvoiceDate.DateTime;
            sl.DueDate = dtInvoiceDate.DateTime;
            sl.StoreId = Convert.ToInt32(lkpStore.EditValue);
            sl.Notes = txtNotes.Text;
            sl.DiscountRatio = Convert.ToDecimal(txtDiscountRatio.EditValue) / 100;
            sl.DiscountValue = Convert.ToDecimal(txtDiscountValue.EditValue);
            sl.TaxValue = Convert.ToDecimal(txt_TaxValue.EditValue);
            sl.DeductTaxRatio = 0;
            sl.DeductTaxValue = 0;
            sl.Expenses = Convert.ToDecimal(txtExpenses.EditValue);
            sl.Net = Convert.ToDecimal(txtNet.EditValue);
            sl.SalesEmpId = Shared.user.DefaultSalesRep;
            if (Convert.ToDecimal(txtPaid.EditValue) >= Convert.ToDecimal(txtNet.EditValue))
            {
                sl.Paid = Convert.ToDecimal(txtNet.EditValue);
                sl.Remains = 0;
            }
            else
            {
                sl.Paid = Convert.ToDecimal(txtPaid.EditValue);
                sl.Remains = Math.Abs(Convert.ToDecimal(txtRemains.EditValue));
            }

            if (sl.Paid == 0)
                sl.PayMethod = false;
            else if (sl.Paid >= sl.Net)
                sl.PayMethod = true;
            else
                sl.PayMethod = null;

            sl.UserId = Shared.UserId;
            sl.DrawerAccountId = Convert.ToInt32(lkp_Drawers.EditValue);

            if (invoiceId == 0)
            {
                sl.JornalId = 0;
                DB.SL_Invoices.InsertOnSubmit(sl);
                MyHelper.UpdateST_UserLog(DB, sl.InvoiceCode, lkp_Customers.Text,
                    (int)FormAction.Add, (int)FormsNames.SL_POS);
            }
            else
                MyHelper.UpdateST_UserLog(DB, sl.InvoiceCode, lkp_Customers.Text,
                    (int)FormAction.Edit, (int)FormsNames.SL_POS);

            #region Delete ItemStore & SL Detail & journal detail

            var invoice_itemstores = (from i in DB.IC_ItemStores
                                      join s in DB.SL_InvoiceDetails
                                      on i.SourceId equals s.SL_InvoiceDetailId
                                      where i.ProcessId == (int)Process.SellInvoice &&
                                      s.SL_InvoiceId == invoiceId
                                      select i).ToList();

            DB.IC_ItemStores.DeleteAllOnSubmit(invoice_itemstores);
            DB.SL_InvoiceDetails.DeleteAllOnSubmit(sl.SL_InvoiceDetails);

            /*Delete All Jornal details First To Store Them Again */
            DB.ACC_JournalDetails.DeleteAllOnSubmit(DB.ACC_JournalDetails.Where(x => x.JournalId == sl.JornalId));

            DB.SubmitChanges();
            #endregion

            decimal CostOfSoldGoods = 0;//used for continual inventory
            for (int x = 0; x < dtSL_Details.Rows.Count; x++)
            {
                if (dtSL_Details.Rows[x].RowState == DataRowState.Deleted)
                    continue;

                DAL.SL_InvoiceDetail detail = new DAL.SL_InvoiceDetail();
                detail.SL_InvoiceId = sl.SL_InvoiceId;
                detail.ItemId = Convert.ToInt32(dtSL_Details.Rows[x]["ItemId"]);
                detail.UOMId = Convert.ToInt32(dtSL_Details.Rows[x]["UOMId"]);
                detail.UOMIndex = 0;
                detail.Qty = Convert.ToDecimal(dtSL_Details.Rows[x]["Qty"]);
                detail.SellPrice = Convert.ToDecimal(dtSL_Details.Rows[x]["SellPrice"]);
                detail.DiscountValue = 0;
                detail.DiscountRatio = 0;
                detail.TotalSellPrice = Convert.ToDecimal(dtSL_Details.Rows[x]["TotalSellPrice"]);

                DB.SL_InvoiceDetails.InsertOnSubmit(detail);
                DB.SubmitChanges();

                //Add Item To Store                
                /*check if item is not service subtract from store*/
                if (Shared.st_Store.AutoPostSales == true)
                {
                    if ((Shared.InvoicePostToStore == true && Shared.StockIsPeriodic) ||
                         (Shared.InvoicePostToStore == true && Shared.StockIsPeriodic == false))
                    {
                        byte costmethod = Convert.ToByte(lkpStore.Properties.GetDataSourceValue("CostMethod", lkpStore.ItemIndex));
                        if (dtSL_Details.Rows[x]["IsService"].ToString() == "False")
                        {
                            var itm = DB.IC_Items.Where(z => z.ItemId == detail.ItemId).FirstOrDefault();
                            CostOfSoldGoods += MyHelper.Subtract_from_store(detail.ItemId, detail.SL_Invoice.StoreId, detail.SL_InvoiceDetailId,
                                (int)Process.SellInvoice, detail.UOMIndex, detail.Qty, 1, 1, costmethod, sl.InvoiceDate, null, null, 0, 0, 0, 0,
                                itm.mtrxParentItem, itm.mtrxAttribute1, itm.mtrxAttribute2, itm.mtrxAttribute3, detail.TotalSellPrice, null, null, false, string.Empty);
                        }
                    }
                    else if (Shared.InvoicePostToStore == false && Shared.StockIsPeriodic == false)//just calc cost of sold goods
                    {
                        byte costmethod = Convert.ToByte(lkpStore.Properties.GetDataSourceValue("CostMethod", lkpStore.ItemIndex));
                        if (dtSL_Details.Rows[x]["IsService"].ToString() == "False")
                        {
                            var itm = DB.IC_Items.Where(z => z.ItemId == detail.ItemId).FirstOrDefault();
                            CostOfSoldGoods += MyHelper.Subtract_from_store(detail.ItemId, detail.SL_Invoice.StoreId, detail.SL_InvoiceDetailId,
                                (int)Process.SellInvoice, detail.UOMIndex, detail.Qty, 1, 1, costmethod, sl.InvoiceDate, null, null, 0, 0, 0, 0,
                                itm.mtrxParentItem, itm.mtrxAttribute1, itm.mtrxAttribute2, itm.mtrxAttribute3, detail.TotalSellPrice, null, null, true, string.Empty);
                        }
                    }
                }
            }

            int jornal_Id = Save_Sell_Jornal_Data(sl, CostOfSoldGoods);
            sl.JornalId = jornal_Id;

            if (Shared.st_Store.AutoPostSales == true)
            {
                sl.IsArchived = null;
                sl.Is_OutTrans = true;

                #region Print
                if (printOptions != null && comp != null && printOptions.PrintReceiptPOS == true)
                {
                    if (Convert.ToDecimal(txtNet.EditValue) >= printOptions.MinValue)
                    {
                        try
                        {

                            Reports.rpt_Printed_Receipt r = new Reports.rpt_Printed_Receipt();

                            if (System.IO.File.Exists(Shared.ReportsPath + "rpt_Printed_Receipt.repx"))
                                r.LoadLayout(Shared.ReportsPath + "rpt_Printed_Receipt.repx");

                            #region Print_Invoice
                            DataTable dt_temp = dtSL_Details.Copy();
                            dt_temp.Rows.Clear();

                            DB = new DAL.ERPDataContext();
                            var details = (from d in DB.SL_InvoiceDetails
                                           where d.SL_InvoiceId == sl.SL_InvoiceId
                                           join i in DB.IC_Items on d.ItemId equals i.ItemId
                                           select new { detail = d, item = i }).ToList();

                            foreach (var d in details)
                            {
                                DataRow row = dt_temp.NewRow();

                                row["ItemId"] = d.detail.ItemId;
                                row["ItemCode1"] = d.item.ItemCode1;
                                row["ItemName"] = d.item.ItemNameAr;
                                row["UOMId"] = d.detail.UOMId;
                                row["UOMName"] = uom_list.Where(u => u.UOMId == d.detail.UOMId).Select(u => u.UOM).First();
                                row["Qty"] = d.detail.Qty;
                                row["SellPrice"] = d.detail.SellPrice;
                                row["TotalSellPrice"] = d.detail.TotalSellPrice;
                                row["IsService"] = (d.item.ItemType == (int)ItemType.Service);
                                row["IsExpire"] = d.item.IsExpire;
                                dt_temp.Rows.Add(row);
                            }
                            dt_temp.AcceptChanges();


                            r.Load_Receipt(dt_temp, lkp_Customers.Text, txtInvoiceCode.Text, dtInvoiceDate.Text, lkpStore.Text, lkp_Drawers.Text,
                                    txtNotes.Text, txtNet.Text, Shared.UserName);

                            for (int i = 0; i < printOptions.NoOfPrint; i++)
                                r.Print(Shared.ReceiptPrinterName);
                            #endregion
                        }
                        catch
                        { }
                    }
                }
                #endregion
            }
            else
            {
                sl.IsArchived = false;
                sl.Is_OutTrans = false;
            }
            DB.SubmitChanges();

            invoiceId = sl.SL_InvoiceId;
            dtSL_Details.AcceptChanges();

            Saved_Successfully = true;
        }

        private void NewInvoice()
        {
            Reset();

            #region GetNextInvNumber
            var lastNumber = (from x in DB.SL_Invoices
                              join s in DB.IC_Stores on x.StoreId equals s.StoreId
                              where Shared.st_Store.AutoInvSerialForStore == true ? x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true    //مستوى المخزن
                              where Shared.st_Store.AutoInvSerialForStore == null ? (int?)lkpStore.GetColumnValue("ParentId") != null ?
                                  s.ParentId.HasValue && s.ParentId.Value == (int?)lkpStore.GetColumnValue("ParentId") : x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true//مستوى الفرع
                              orderby x.InvoiceDate descending
                              orderby x.SL_InvoiceId descending
                              select x.InvoiceCode).FirstOrDefault();
            txtInvoiceCode.EditValue = MyHelper.GetNextNumberInString(lastNumber);
            #endregion

            if (customerId != 0)
                lkp_Customers.EditValue = customerId;

            //GetNonArchivedInvoices();
        }


        private void GetInvoice(int invId)
        {
            DB = new DAL.ERPDataContext();
            var inv = DB.SL_Invoices.Where(v => v.SL_InvoiceId == invId).SingleOrDefault();
            if (inv != null)
            {
                invoiceId = invId;
                lkp_Drawers.EditValue = inv.DrawerAccountId.ToString();
                lkp_Customers.EditValue = inv.CustomerId;
                lkpStore.EditValue = inv.StoreId;

                txtInvoiceCode.Text = inv.InvoiceCode;
                dtInvoiceDate.EditValue = inv.InvoiceDate;
                txtNotes.Text = inv.Notes;

                GetInvoiceDetails(invId);

                txtDiscountRatio.EditValue = inv.DiscountRatio * 100;
                txtDiscountValue.EditValue = inv.DiscountValue;
                txt_TaxValue.EditValue = inv.TaxValue;
                txtExpenses.EditValue = inv.Expenses;

                //txtTotal.EditValue = inv.Net - inv.Expenses + inv.TaxValue + inv.DiscountValue;
                txt_TaxRatio.EditValue = 100 * inv.TaxValue / (Convert.ToDecimal(col_TotalSellPrice.SummaryItem.SummaryValue) - inv.DiscountValue);

                txtNet.EditValue = inv.Net;
                txtPaid.EditValue = inv.Paid;
                txtRemains.EditValue = inv.Remains;
                CalcPrices();
            }
        }

        private void GetInvoiceDetails(int invoiceId)
        {
            dtSL_Details.Rows.Clear();
            DB = new DAL.ERPDataContext();
            var details = (from d in DB.SL_InvoiceDetails
                           where d.SL_InvoiceId == invoiceId
                           join i in DB.IC_Items on d.ItemId equals i.ItemId
                           select new { detail = d, item = i }).ToList();

            foreach (var d in details)
            {
                DataRow row = dtSL_Details.NewRow();

                row["ItemId"] = d.detail.ItemId;
                row["ItemCode1"] = d.item.ItemCode1;
                row["ItemName"] = d.item.ItemNameAr;
                row["UOMId"] = d.detail.UOMId;
                row["UOMName"] = uom_list.Where(u => u.UOMId == d.detail.UOMId).Select(u => u.UOM).First();
                row["Qty"] = d.detail.Qty;
                row["SellPrice"] = d.detail.SellPrice;
                row["TotalSellPrice"] = d.detail.TotalSellPrice;
                row["IsService"] = (d.item.ItemType == (int)ItemType.Service);
                row["IsExpire"] = d.item.IsExpire;
                dtSL_Details.Rows.Add(row);
            }
            dtSL_Details.AcceptChanges();
        }

        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.Item).Count() < 1)
                {
                    mi_frm_IC_Item.Enabled = false;
                }

                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.SL_Customer).Count() < 1)
                {
                    btnAddCustomer.Enabled = false;
                }
                if (Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.ACC_RecieveNote).Count() < 1)
                    barBtn_RNote.Enabled = false;
                else
                {
                    if (Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.ACC_RecieveNote).FirstOrDefault().CanAdd == false)
                        barBtn_RNote.Enabled = false;
                }
                if (Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.CashIn).Count() < 1)
                    barBtn_RCashNote.Enabled = false;
                else
                {
                    if (Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.CashIn).FirstOrDefault().CanAdd == false)
                        barBtn_RCashNote.Enabled = false;
                }
                if (Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.SL_Return).Count() < 1)
                    barBtnSalesReturn.Enabled = false;
                else
                {
                    if (Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.SL_Return).FirstOrDefault().CanAdd == false)
                        barBtnSalesReturn.Enabled = false;
                }
            }
        }

        private void barBtnHelp_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "نقطة البيع");
        }

        private void lkp_Customers_EditValueChanged(object sender, EventArgs e)
        {
            txtDiscountRatio.EditValue = Convert.ToDouble(ErpUtils.GetGridLookUpValue(lkp_Customers, lkp_Customers.EditValue, "DiscountRatio")) * 100;

            var selected_customer = lst_Customers.Where(x => x.CustomerId == Convert.ToInt32(lkp_Customers.EditValue)).FirstOrDefault();
            if (selected_customer != null && selected_customer.PriceLevelId.HasValue)
                CustomerPriceLevel = DB.IC_PriceLevels.Where(x => x.PriceLevelId == selected_customer.PriceLevelId).FirstOrDefault();
            else
                CustomerPriceLevel = null;
        }

        private void txt_TaxRatio_Leave(object sender, EventArgs e)
        {
            decimal tax_ratio = Convert.ToDecimal(txt_TaxRatio.EditValue) / 100;
            decimal disc_value = Convert.ToDecimal(txtDiscountValue.EditValue);
            decimal tax_value = (Convert.ToDecimal(col_TotalSellPrice.SummaryItem.SummaryValue) - disc_value) * tax_ratio;
            txt_TaxValue.EditValue = decimal.ToDouble(tax_value);
        }

        private void txt_TaxValue_Leave(object sender, EventArgs e)
        {
            decimal total = Convert.ToDecimal(col_TotalSellPrice.SummaryItem.SummaryValue);
            if (total == 0)
                return;

            decimal tax_value = Convert.ToDecimal(txt_TaxValue.EditValue);
            decimal disc_value = Convert.ToDecimal(txtDiscountValue.EditValue);

            txt_TaxRatio.EditValue = decimal.ToDouble((tax_value / (total - disc_value)) * 100);
        }

        private void txtDiscountValue_Leave(object sender, EventArgs e)
        {
            if (Convert.ToDecimal(col_TotalSellPrice.SummaryItem.SummaryValue) == 0)
                return;

            decimal discount_value = Convert.ToDecimal(txtDiscountValue.EditValue);
            decimal discount_ratio = (discount_value / Convert.ToDecimal(col_TotalSellPrice.SummaryItem.SummaryValue)) * 100;
            txtDiscountRatio.EditValue = decimal.ToDouble(discount_ratio);

            txt_TaxValue_Leave(sender, EventArgs.Empty);
        }


        private void lkpStore_EditValueChanged(object sender, EventArgs e)
        {
            #region GetNextInvNumber
            var lastNumber = (from x in DB.SL_Invoices
                              join s in DB.IC_Stores on x.StoreId equals s.StoreId
                              where Shared.st_Store.AutoInvSerialForStore == true ? x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true    //مستوى المخزن
                              where Shared.st_Store.AutoInvSerialForStore == null ? (int?)lkpStore.GetColumnValue("ParentId") != null ?
                                  s.ParentId.HasValue && s.ParentId.Value == (int?)lkpStore.GetColumnValue("ParentId") : x.StoreId == Convert.ToInt32(lkpStore.EditValue) : true//مستوى الفرع
                              orderby x.InvoiceDate descending
                              orderby x.SL_InvoiceId descending
                              select x.InvoiceCode).FirstOrDefault();
            txtInvoiceCode.EditValue = MyHelper.GetNextNumberInString(lastNumber);
            #endregion
        }

        private void barBtn_RCashNote_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            frmMain.OpenACC_CashNote(false, IsVendor.Customer, Convert.ToInt32(lkp_Customers.EditValue), null, null);
        }

        private void barBtn_RNote_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            frmMain.Open_ACCInBankNote();
        }

        private void barBtnSalesReturn_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_SL_Return)))
            {
                frm_SL_Return f = new frm_SL_Return();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_SL_Return"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_SL_Return"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_SL_Return"].BringToFront();
            }
        }

        private bool Validate_Sell_Without_Balance(IC_Item itm, decimal Qty)
        {
            if (Shared.user.SellWithNoBalance == null      //عدم التدخل
                || itm.ItemType == (int)ItemType.Service
                || itm.ItemType == (int)ItemType.MatrixParent
                || itm.ItemType == (int)ItemType.Subtotal)
                return true;

            decimal mediumFactor = MyHelper.FractionToDouble(itm.MediumUOMFactor);
            decimal largeFactor = MyHelper.FractionToDouble(itm.LargeUOMFactor);

            decimal SoldQty = (from d in DB.SL_InvoiceDetails
                               where d.SL_Invoice.IsArchived == false
                               && d.ItemId == itm.ItemId
                               && d.SL_Invoice.StoreId == Convert.ToInt32(lkpStore.EditValue)
                               let UomFactor = d.UOMIndex == 0 ? 1 : d.UOMIndex == 1 ? mediumFactor : largeFactor
                               select new { Qty = d.Qty * UomFactor }).Select(x => x.Qty).ToList().DefaultIfEmpty(0).Sum();

            DataRow row = dtSL_Details.Rows.Find(itm.ItemId);
            int index = dtSL_Details.Rows.IndexOf(row);
            if (index >= 0)
                SoldQty += Convert.ToDecimal(dtSL_Details.Rows[index]["Qty"]);

            decimal CurrentQty = MyHelper.GetItemQty(dtInvoiceDate.DateTime, itm.ItemId, Convert.ToInt32(lkpStore.EditValue)) - SoldQty;

            //CurrentQty-=
            if (Qty > CurrentQty)//validate when new invoice only
            {
                if (Shared.user.SellWithNoBalance == true)//warning
                {
                    if (XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.MsgNoEnoughQty_continue : ResSLAr.MsgNoEnoughQty_continue,
                           Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                        return true;
                    else
                        return false;
                }
                else if (Shared.user.SellWithNoBalance == false)//Prevent
                {
                    XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.txtValidateStoreQty : ResSLAr.txtValidateStoreQty,
                           Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Exclamation);

                    return false;
                }
            }
            return true;

        }

        private void lkpItem1_Popup(object sender, EventArgs e)
        {
            (sender as GridLookUpEdit).Properties.View.ClearColumnsFilter();
        }

        private void tileView1_ItemClick(object sender, DevExpress.XtraGrid.Views.Tile.TileViewItemClickEventArgs e)
        {
            try
            {
                txtItemCode.EditValue = e.Item.Elements[1].Text;
                txtItemCode_KeyDown(txtItemCode, new KeyEventArgs(Keys.Enter));
            }
            catch
            { }
        }

        private void listView_Items_MouseClick(object sender, MouseEventArgs e)
        {
            //try
            //{
            //    txtItemCode.EditValue = listView_Items.SelectedItems[0].SubItems[2].Text;
            //    txtItemCode_KeyDown(txtItemCode, new KeyEventArgs(Keys.Enter));
            //}
            //catch
            //{ }
        }

        #region Pending Invoices

        private int Save_Sell_Jornal_Data(DAL.SL_Invoice pr, decimal CostOfSoldGoods)
        {
            string note =
                (Shared.IsEnglish == true ? ResSLEn.txtSLInvNumber : ResSLAr.txtSLInvNumber) + " " + pr.InvoiceCode + "  - " +
                    (Shared.IsEnglish == true ? "to " : "الى ") + " " + lkp_Customers.Text;

            int BranchId = Convert.ToInt32(lkpStore.EditValue);
            if (lkpStore.GetColumnValue("ParentId") != null)
                BranchId = Convert.ToInt32(lkpStore.GetColumnValue("ParentId"));

            int CustomerAccountId = lst_Customers.Where(x => x.CustomerId == pr.CustomerId).Select(x => x.AccountId.Value).FirstOrDefault();

            #region Save_Jornal
            DAL.ACC_Journal jornal;
            if (pr.JornalId == 0)
            {
                jornal = new DAL.ACC_Journal();
                jornal.InsertDate = dtInvoiceDate.DateTime;     //MyHelper.Get_Server_DateTime();
                jornal.InsertUser = Shared.UserId;
                jornal.JCode = HelperAcc.Get_Jornal_Code();
                jornal.JNotes = note;
                jornal.ProcessId = (int)Process.SellInvoice;
                jornal.SourceId = pr.SL_InvoiceId;
                jornal.IsPosted = !Shared.OfflinePostToGL;
                jornal.StoreId = BranchId;
                jornal.CrncId = 0;
                jornal.CrncRate = 1;
                DB.ACC_Journals.InsertOnSubmit(jornal);
            }
            else
            {
                jornal = DB.ACC_Journals.Where(x => x.JournalId == pr.JornalId).FirstOrDefault();
                jornal.LastUpdateDate = MyHelper.Get_Server_DateTime();
                jornal.LastUpdateUser = Shared.UserId;
                jornal.JNotes = note;
                jornal.ProcessId = (int)Process.SellInvoice;
                jornal.SourceId = pr.SL_InvoiceId;
                jornal.IsPosted = !Shared.OfflinePostToGL;
                jornal.StoreId = BranchId;
                jornal.CrncId = 0;
                jornal.CrncRate = 1;
            }

            DB.SubmitChanges();
            #endregion

            decimal total_Sells = pr.Net - pr.TaxValue + pr.DiscountValue - pr.Expenses + pr.DeductTaxValue - pr.AddTaxValue;

            int? costCenter = Convert.ToInt32(lkpStore.GetColumnValue("CostCenterId"));      // تحميل مركز تكلفة المخزن
            if (costCenter == 0)
                costCenter = null;

            #region Sell
            /*قيد البيع*/
            /*من حســاب كل من*/
            /* حساب العميل*/
            DAL.ACC_JournalDetail jornal_Detail_2 = new DAL.ACC_JournalDetail();
            jornal_Detail_2.JournalId = jornal.JournalId;
            jornal_Detail_2.AccountId = CustomerAccountId;              //حساب عميل  
            jornal_Detail_2.Credit = 0;
            jornal_Detail_2.Debit = total_Sells + pr.Expenses + pr.TaxValue - pr.DeductTaxValue + pr.AddTaxValue;
            jornal_Detail_2.Notes = note;
            jornal_Detail_2.DueDate = pr.DueDate;
            jornal_Detail_2.CrncId = 0;
            jornal_Detail_2.CrncRate = 1;
            DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_2);

            /*من حساب ضريبة الخصم و الاضافة*/
            #region Deduct_Tax_Value
            if (pr.DeductTaxValue > 0)
            {
                DAL.ACC_JournalDetail jornal_Detail_deduct_Tax = new DAL.ACC_JournalDetail();
                jornal_Detail_deduct_Tax.JournalId = jornal.JournalId;
                jornal_Detail_deduct_Tax.AccountId = Shared.st_Store.SalesDeductTaxAccount.Value;
                jornal_Detail_deduct_Tax.Credit = 0;
                jornal_Detail_deduct_Tax.Debit = pr.DeductTaxValue;
                jornal_Detail_deduct_Tax.Notes = note;
                jornal.CrncId = 0;
                jornal.CrncRate = 1;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_deduct_Tax);
            }
            #endregion

            /*الى حساب المبيعات*/
            DAL.ACC_JournalDetail jornal_Detail_1 = new DAL.ACC_JournalDetail();
            jornal_Detail_1.JournalId = jornal.JournalId;
            jornal_Detail_1.AccountId = Convert.ToInt32(lkpStore.GetColumnValue("SellAccount"));      // حساب مبيعات المخزن
            jornal_Detail_1.CostCenter = costCenter;
            jornal_Detail_1.Credit = total_Sells + pr.Expenses;           //اجمالي مبيعات + مصاريف النقل 
            jornal_Detail_1.Debit = 0;
            jornal_Detail_1.Notes = note;
            jornal_Detail_1.CrncId = 0;
            jornal_Detail_1.CrncRate = 1;
            DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_1);

            /*الى حساب ضريبة المبيعات*/
            if (pr.TaxValue > 0)
            {
                DAL.ACC_JournalDetail jornal_Detail_tax = new DAL.ACC_JournalDetail();
                jornal_Detail_tax.JournalId = jornal.JournalId;
                jornal_Detail_tax.AccountId = Shared.st_Store.TaxAcc.Value;//حساب ضريبة المبيعات 
                jornal_Detail_tax.Credit = pr.TaxValue;
                jornal_Detail_tax.Debit = 0;
                jornal_Detail_tax.Notes = note + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Tax : ResSLAr.txt_Tax);
                jornal_Detail_tax.CrncId = 0;
                jornal_Detail_tax.CrncRate = 1;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_tax);
            }

            /*الى حساب ضريبة الاضافة*/
            if (pr.AddTaxValue > 0)
            {
                DAL.ACC_JournalDetail jornal_Detail_addtax = new DAL.ACC_JournalDetail();
                jornal_Detail_addtax.JournalId = jornal.JournalId;
                jornal_Detail_addtax.AccountId = Shared.st_Store.SalesAddTaxAccount.Value;//حساب ضريبة الاضافة 
                jornal_Detail_addtax.Credit = pr.AddTaxValue;
                jornal_Detail_addtax.Debit = 0;
                jornal_Detail_addtax.Notes = note + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Tax : ResSLAr.txt_Tax);
                jornal_Detail_addtax.CrncId = 0;
                jornal_Detail_addtax.CrncRate = 1;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_addtax);
            }



            #endregion

            #region Paid
            /*قيد السداد*/
            if (pr.Paid > 0 || (pr.PayAccountId2.HasValue && pr.PayAcc2_Paid.HasValue && pr.PayAcc2_Paid.Value > 0))
            {
                /* من حساب الخزينة*/
                if (pr.Paid > 0)
                {
                    DAL.ACC_JournalDetail jornal_Detail_4 = new DAL.ACC_JournalDetail();
                    jornal_Detail_4.JournalId = jornal.JournalId;
                    jornal_Detail_4.AccountId = Convert.ToInt32(lkp_Drawers.EditValue);                 // حساب الخزينة
                    jornal_Detail_4.Credit = 0;
                    jornal_Detail_4.Debit = pr.Paid;
                    jornal_Detail_4.Notes = note + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Paid : ResSLAr.txt_Paid);
                    jornal_Detail_4.CrncId = 0;
                    jornal_Detail_4.CrncRate = 1;
                    DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_4);
                }
                /*من حساب الخزينة 2*/
                if (pr.PayAccountId2 != null && pr.PayAcc2_Paid.HasValue && pr.PayAcc2_Paid.Value > 0)
                {
                    DAL.ACC_JournalDetail jornal_Detail_5 = new DAL.ACC_JournalDetail();
                    jornal_Detail_5.JournalId = jornal.JournalId;
                    jornal_Detail_5.AccountId = pr.PayAccountId2.Value;          // حساب الخزينة
                    jornal_Detail_5.Credit = 0;
                    jornal_Detail_5.Debit = pr.PayAcc2_Paid.Value;
                    jornal_Detail_5.Notes = note + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Paid : ResSLAr.txt_Paid);
                    jornal_Detail_5.CrncId = 0;
                    jornal_Detail_5.CrncRate = 1;
                    DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_5);
                }
                /*1 الى حساب العميل*/
                DAL.ACC_JournalDetail jornal_Detail_3 = new DAL.ACC_JournalDetail();
                jornal_Detail_3.JournalId = jornal.JournalId;
                jornal_Detail_3.AccountId = CustomerAccountId;                  // حساب عميل 
                jornal_Detail_3.Credit = pr.Paid + (pr.PayAcc2_Paid.HasValue ? pr.PayAcc2_Paid.Value : 0);
                jornal_Detail_3.Debit = 0;
                jornal_Detail_3.Notes = note + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Paid : ResSLAr.txt_Paid);
                jornal_Detail_3.CrncId = 0;
                jornal_Detail_3.CrncRate = 1;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_3);
            }
            #endregion

            #region Discount
            /*قيد الخصم*/
            /*1 الى حساب العميل*/
            if (pr.DiscountValue > 0)
            {
                /* من حساب الخصم المسموح به*/
                DAL.ACC_JournalDetail jornal_Detail_6 = new DAL.ACC_JournalDetail();
                jornal_Detail_6.JournalId = jornal.JournalId;
                jornal_Detail_6.AccountId = stores_table.Where(x => x.StoreId == Convert.ToInt32(lkpStore.EditValue)).Select(x => x.SalesDiscountAcc.Value).First();//  حساب الخصم النقدي المسموح به
                jornal_Detail_6.Credit = 0;
                jornal_Detail_6.Debit = pr.DiscountValue;
                jornal_Detail_6.Notes = note + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Discount : ResSLAr.txt_Discount);
                jornal_Detail_6.CrncId = 0;
                jornal_Detail_6.CrncRate = 1;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_6);

                DAL.ACC_JournalDetail jornal_Detail_5 = new DAL.ACC_JournalDetail();
                jornal_Detail_5.JournalId = jornal.JournalId;
                jornal_Detail_5.AccountId = CustomerAccountId;                      //حساب عميل
                jornal_Detail_5.Credit = Convert.ToDecimal(pr.DiscountValue);
                jornal_Detail_5.Debit = 0;
                jornal_Detail_5.Notes = note + "\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Discount : ResSLAr.txt_Discount);
                jornal_Detail_5.CrncId = 0;
                jornal_Detail_5.CrncRate = 1;
                DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_5);
            }
            #endregion

            #region CostOfSoldGoods
            if (Shared.StockIsPeriodic == false)
            {
                DAL.ACC_JournalDetail jdCost1 = new DAL.ACC_JournalDetail();
                jdCost1.JournalId = jornal.JournalId;
                jdCost1.AccountId = stores_table.Where(x => x.StoreId == Convert.ToInt32(lkpStore.EditValue)).Select(x => x.CostOfSoldGoodsAcc.Value).First();//حساب تكلفة البضاعة المباعة
                jdCost1.Credit = 0;
                jdCost1.Debit = CostOfSoldGoods;
                jdCost1.Notes = jornal.JNotes;// +"\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Discount : ResSLAr.txt_Discount);
                jdCost1.CrncId = pr.CrncId;
                jdCost1.CrncRate = pr.CrncRate;
                jdCost1.CostCenter = costCenter;
                DB.ACC_JournalDetails.InsertOnSubmit(jdCost1);

                DAL.ACC_JournalDetail jdCost2 = new DAL.ACC_JournalDetail();
                jdCost2.JournalId = jornal.JournalId;
                jdCost2.AccountId = stores_table.Where(x => x.StoreId == Convert.ToInt32(lkpStore.EditValue)).Select(x => x.PurchaseAccount).First();//المخزون
                jdCost2.Credit = CostOfSoldGoods;
                jdCost2.Debit = 0;
                jdCost2.Notes = jornal.JNotes;// +"\r\n" + (Shared.IsEnglish ? ResSLEn.txt_Discount : ResSLAr.txt_Discount);
                jdCost2.CrncId = pr.CrncId;
                jdCost2.CrncRate = pr.CrncRate;
                DB.ACC_JournalDetails.InsertOnSubmit(jdCost2);
            }
            #endregion

            DB.SubmitChanges();

            return jornal.JournalId;

        }

        private void mi_FinishInvoice_Click(object sender, EventArgs e)
        {
            //if (bandedGridView1.FocusedRowHandle < 0)
            //    return;

            //int invid = Convert.ToInt32(bandedGridView1.GetFocusedRowCellValue("SL_InvoiceId"));
            //if (invid == 0)
            //    return;

            //var invoice = DB.SL_Invoices.Where(x => x.SL_InvoiceId == invid).First();
            //invoice.IsArchived = null;
            //DB.SubmitChanges();

            //if (Shared.st_Store.AutoPostSales == false)
            //{
            //    if (MyHelper.PostNonArchivedSalesInvoices(null, invid, Shared.InvoicePostToStore, Shared.OfflinePostToGL,
            //        Shared.st_Store, Shared.StockIsPeriodic) == true)
            //        MessageBox.Show(Shared.IsEnglish ? ResEn.MsgPostedSuccessfully : ResAr.MsgPostedSuccessfully,
            //            Shared.IsEnglish ? ResEn.MsgTInfo : ResAr.MsgTInfo, MessageBoxButtons.OK, MessageBoxIcon.Information);
            //    else
            //        MessageBox.Show(Shared.IsEnglish ? ResEn.MsgPostedFailed : ResAr.MsgPostedFailed,
            //            Shared.IsEnglish ? ResEn.MsgTError : ResAr.MsgTError, MessageBoxButtons.OK, MessageBoxIcon.Error);
            //}
            //else
            //{
            //    var invoice = DB.SL_Invoices.Where(x => x.SL_InvoiceId == invid).First();
            //    invoice.IsArchived = null;
            //    DB.SubmitChanges();
            //}

            //barBtnCancel.PerformClick();
            //GetNonArchivedInvoices();
        }

        private void mi_FinishAndPrint_Click(object sender, EventArgs e)
        {
            //if (printOptions != null && comp != null && printOptions.PrintReceiptPOS == true)
            //{
            //    if (Convert.ToDecimal(txtNet.EditValue) >= printOptions.MinValue)
            //    {
            //try
            //{

            //    Reports.rpt_Printed_Receipt r = new Reports.rpt_Printed_Receipt();

            //    if (System.IO.File.Exists(Shared.ReportsPath + "rpt_Printed_Receipt.repx"))
            //        r.LoadLayout(Shared.ReportsPath + "rpt_Printed_Receipt.repx");

            //    #region Print_Invoice
            //    DataTable dt_temp = dtSL_Details.Copy();
            //    dt_temp.Rows.Clear();

            //    DB = new DAL.ERPDataContext();
            //    var details = (from d in DB.SL_InvoiceDetails
            //                   where d.SL_InvoiceId == invid
            //                   join i in DB.IC_Items on d.ItemId equals i.ItemId
            //                   select new { detail = d, item = i }).ToList();

            //    foreach (var d in details)
            //    {
            //        DataRow row = dt_temp.NewRow();

            //        row["ItemId"] = d.detail.ItemId;
            //        row["ItemCode1"] = d.item.ItemCode1;
            //        row["ItemName"] = d.item.ItemNameAr;
            //        row["UOMId"] = d.detail.UOMId;
            //        row["UOMName"] = uom_list.Where(u => u.UOMId == d.detail.UOMId).Select(u => u.UOM).First();
            //        row["Qty"] = d.detail.Qty;
            //        row["SellPrice"] = d.detail.SellPrice;
            //        row["TotalSellPrice"] = d.detail.TotalSellPrice;
            //        row["IsService"] = (d.item.ItemType == (int)ItemType.Service);
            //        row["IsExpire"] = d.item.IsExpire;
            //        dt_temp.Rows.Add(row);
            //    }
            //    dt_temp.AcceptChanges();


            //    r.Load_Receipt(dt_temp, lkp_Customers.Text, txtInvoiceCode.Text, dtInvoiceDate.Text, lkpStore.Text, lkp_Drawers.Text,
            //            txtNotes.Text, txtNet.Text, Shared.UserName);

            //    for (int i = 0; i < printOptions.NoOfPrint; i++)
            //        r.Print(Shared.ReceiptPrinterName);
            //    #endregion
            //}
            //catch
            //{ }
            //}
            //}

            //barBtnCancel.PerformClick();
        }


        #endregion

        private void txtDiscountRatio_Enter(object sender, EventArgs e)
        {
            ((TextEdit)sender).SelectAll();
        }

        private void gridView2_CellValueChanged(object sender, CellValueChangedEventArgs e)
        {
            if (e.Column.FieldName == "Qty")
            {
                decimal Qty = Convert.ToDecimal(gv_Details.GetFocusedRowCellValue("Qty"));
                decimal price = Convert.ToDecimal(gv_Details.GetFocusedRowCellValue("SellPrice"));

                gv_Details.SetRowCellValue(e.RowHandle, "TotalSellPrice", Qty * price);
            }
        }

        private void mi_Print_Click(object sender, EventArgs e)
        {
            //int invid = Convert.ToInt32(bandedGridView1.GetFocusedRowCellValue("SL_InvoiceId"));
            //if (invid == 0)
            //    return;

            //try
            //{
            //    Reports.rpt_Printed_Receipt r = new Reports.rpt_Printed_Receipt();

            //    if (System.IO.File.Exists(Shared.ReportsPath + "rpt_Printed_Receipt.repx"))
            //        r.LoadLayout(Shared.ReportsPath + "rpt_Printed_Receipt.repx");

            //    #region Print_Invoice
            //    DataTable dt_temp = dtSL_Details.Copy();
            //    dt_temp.Rows.Clear();

            //    DB = new DAL.ERPDataContext();
            //    var details = (from d in DB.SL_InvoiceDetails
            //                   where d.SL_InvoiceId == invid
            //                   join i in DB.IC_Items on d.ItemId equals i.ItemId
            //                   select new { detail = d, item = i }).ToList();

            //    foreach (var d in details)
            //    {
            //        DataRow row = dt_temp.NewRow();

            //        row["ItemId"] = d.detail.ItemId;
            //        row["ItemCode1"] = d.item.ItemCode1;
            //        row["ItemName"] = d.item.ItemNameAr;
            //        row["UOMId"] = d.detail.UOMId;
            //        row["UOMName"] = uom_list.Where(u => u.UOMId == d.detail.UOMId).Select(u => u.UOM).First();
            //        row["Qty"] = d.detail.Qty;
            //        row["SellPrice"] = d.detail.SellPrice;
            //        row["TotalSellPrice"] = d.detail.TotalSellPrice;
            //        row["IsService"] = (d.item.ItemType == (int)ItemType.Service);
            //        row["IsExpire"] = d.item.IsExpire;
            //        dt_temp.Rows.Add(row);
            //    }
            //    dt_temp.AcceptChanges();


            //    r.Load_Receipt(dt_temp, lkp_Customers.Text, txtInvoiceCode.Text, dtInvoiceDate.Text, lkpStore.Text, lkp_Drawers.Text,
            //            txtNotes.Text, txtNet.Text, Shared.UserName);
            //    r.Print(Shared.ReceiptPrinterName);
            //    #endregion
            //}
            //catch
            //{ }
        }

        private void tileControl1_ItemClick(object sender, TileItemEventArgs e)
        {
            #region ListViewItems
            var x = (from DataRow r in dt.Rows
                     where r["key"].ToString() == e.Item.Text
                     select r["list"]).ToList();

            if (x == null || x.Count == 0) return;


            var itms = (from d in x[0] as List<IC_Item>
                        select new
                        {
                            d.ItemId,
                            d.ItemCode1,
                            d.ItemNameAr,
                            d.IC_Category.CategoryNameAr,
                            d.SmallUOMPrice,
                        }).ToList();

            listView_Items.DataSource = itms;
            #endregion
        }
    }
}
