﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btn_Replacement.Size" type="System.Drawing.Size, System.Drawing">
    <value>320, 23</value>
  </data>
  <data name="btn_ReEvaluate.Location" type="System.Drawing.Point, System.Drawing">
    <value>15, 75</value>
  </data>
  <data name="&gt;&gt;txt_Start.Name" xml:space="preserve">
    <value>txt_Start</value>
  </data>
  <data name="&gt;&gt;btn_IC_Trns_ALL.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txt_Start.Properties.Mask.EditMask" xml:space="preserve">
    <value>d</value>
  </data>
  <data name="&gt;&gt;btnRe_CodingCostCenter.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="&gt;&gt;btn_RepostAllProcess.Name" xml:space="preserve">
    <value>btn_RepostAllProcess</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="btn_IC_Trns_ALL.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="txt_Start.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Start.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;btn_StockTaking.Name" xml:space="preserve">
    <value>btn_StockTaking</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="txt_Start.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="progressBar1.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <assembly alias="DevExpress.XtraEditors.v15.1" name="DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="txt_Start.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="btn_StockTaking.Location" type="System.Drawing.Point, System.Drawing">
    <value>15, 191</value>
  </data>
  <data name="btn_RepostAllProcess.Size" type="System.Drawing.Size, System.Drawing">
    <value>320, 23</value>
  </data>
  <data name="btnRe_CodingCostCenter.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="btn_SLReturnInv.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="&gt;&gt;btn_Replacement.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="btn_Replacement.Location" type="System.Drawing.Point, System.Drawing">
    <value>15, 162</value>
  </data>
  <data name="btn_Replacement.Text" xml:space="preserve">
    <value>Repost Replacement</value>
  </data>
  <data name="txt_End.Properties.CalendarTimeProperties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_End.Properties.CalendarTimeProperties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;progressBar1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ProgressBar, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="btn_RepostAllProcess.Location" type="System.Drawing.Point, System.Drawing">
    <value>17, 250</value>
  </data>
  <data name="txt_Start.Properties.CalendarTimeProperties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Reealuate Inventory</value>
  </data>
  <data name="btn_ReEvaluate.Size" type="System.Drawing.Size, System.Drawing">
    <value>320, 23</value>
  </data>
  <data name="btn_ReCodingAccounts.Size" type="System.Drawing.Size, System.Drawing">
    <value>320, 23</value>
  </data>
  <data name="txt_End.Properties.Mask.EditMask" xml:space="preserve">
    <value>d</value>
  </data>
  <data name="txt_End.Properties.CalendarTimeProperties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Start.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="btn_ReCodingAccounts.Text" xml:space="preserve">
    <value>Re-Coding Accounts</value>
  </data>
  <data name="labelControl2.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAA
        AAAfbf8AH2z/AB9r/x8fa/+rH2v/9h9r/9AfbP9HH2//AB9u/wAfcP8AAAAAAB9p/wAfZv8AH2D/Ah9j
        /2cfY//iH2P/7R9j/4YfY/8KH2P/AB9j/wAAAAAADw4QAA4NDwAODQ4ADQwORwwMDdAMCw32CwsMqwsL
        DB8MCw0ADAsNAB9u/wAfbf8WH23/qx9t//8fbv//H27//x9u/9gfb/89H3D/AB9y/wAfcf8AH23/AB9n
        /wAfZ/9cH2X/6x9j//8fY///H2P/9x9j/38fY/8GH2P/AB5e8QAPDhAADw4QAA4NDz0NDQ/YDQ0O/w0M
        Dv8MDA3/DAsNqwwLDRYMCw0AH2//FR9v/6Ifb///H3D//x9x//8fcf//H3H//x9x/9Mfcv82IHD/AiBw
        /wQfcP8DH2z/VB9q/+gfZ///H2X//x9j//8fY///H2P/9x9j/3cfY/8FH2P+ABAPEQAQDxE1Dw4Q0w8O
        EP8ODg//Dg0P/w0NDv8MDA3/DAsNogwLDRUfcP+MH3H//R9y//8ec///HnP//x50//8edP//HnP//x5z
        /9cec/+pH3L/qR9x/6wfbv/mH23//x9q//8faP//H2X//x9j//8fY///H2P/8h9j/04bTb8AERASERAQ
        ErsQEBL/EA8R/w8PEf8PDhD/Dg0P/w0ND/8MDA79DAwNjB5z/9QedP//HnX//x52//8edv//Hnb//x52
        //8edv//Hnb//x51//8edP//HnP//x9x//8fb///H23//x9r//8faP//H2X//x9j//8fY///H2P/gxg4
        hAASERM1ERET7hERE/8REBL/EBAS/xAPEf8PDhD/Dg4Q/w0ND/8NDA7THnX/wx52//8ed///Hnj//x54
        //8eef//Hnn//x54//8eeP/9Hnf/8h52//Iedf/zHnP//h9x//8fb///H23//x9q//8fZ///H2T//x9j
        //8fY/90GkGeABISFCYTEhThEhIU/xIRE/8RERP/EBAS/xAPEf8PDhD/Dg4Q/w4ND78ed/9UHnj/5x55
        //8eev//Hnr//x57//8ee///Hnr/+x56/5AeeP88Hnf/PB52/0Eedf+wHnP//x9x//8fb///H2z//x9p
        //8fZv//H2T/zB9j/yQdV9wAExIVAxMTFYMTEhX8ExIU/xISFP8RERP/ERAS/xAPEf8PDhDkDg4QTx50
        /wAeev9dHnv/7R58//8efP//Hn3//x59//0efP+UHnv/DB55/wAed/8AHnb/AB52/x8edf+5HnP//x9x
        //8fbv//H2v//x9o/9YfZf85H2f/AAAAAAAUExYAFBMWDRQTFpQUExX9ExIV/xISFP8SERP/ERAS6hAP
        EVYTDhQAHnr/AB58/wEefP9nHn3/7x5+//8efv/9Hn7/nR59/xAefP8AHnv/AB52/wAedf8AHnX/AB51
        /yUedP2+H3P//x9x//8fbPvbH2r/Px9o/wAfZv8AH2P/ABQTFgAUExYAFBQWEBQUFp0UExb9ExIV/xIS
        FOwRERNfIxkMABAQEQAee/8AHn3/AB5+/wcefv+xHn///x5//+oef/8tHn//AB59/wAefP8AAAAAAB52
        /wAeV/8AGkKGABk+eUYaQoPzG0KG/xo0ZZIbRZkAH2v/AB9o/wAAAAAAFBQWABQUFgAVFBcAFRQXLRUU
        F+oUExb/ExMVqxMTFAUSERQAEBASAAAAAAAegP8AHn//Ax6A/6kegP//HoD/5x6A/ycegP8AHoD/AAAA
        AAAAAAAAAAAAABpCgQAXFRcAFhMSOhcVFvAXFRb/GBUWjhgYHgAcO3gAAAAAAAAAAAAAAAAAFxYZABYV
        GAAWFRgnFRUX5xUUF/8UExajExIVAhQTFgAAAAAAHoD/AB6A/wAegP8IHoD/sx6B//8egf/rHoH/Lx6B
        /wAegv8AHoL/AAAAAAAXFhkAFBQXABcXGgAXFxpIGBca9BgYG/8ZGBuTGRkcABoaHQAbGx4AAAAAABkZ
        HAAYGBsAFxcZABcWGS8WFhjrFRUX/xUUF6wUFBYGFBMWABQTFQAef/8AHoD/Ah6A/20egf/xHoH//x6B
        //4egv+kHoL/Ex6C/wAegv8AFhYYABsZIAAXFxoAFxcaKRgXGsMYGBv/GRkc/xoZHN4bGh1FHBoeABwb
        HwAdHCAAGhkdABkZHAAYGBsTGBcapBcWGf4WFRj/FRQX7xQUFmYTEBMBExMVAB59/wIegP9kHoD/8B6B
        //8egv//HoL//x6C//8egv+cHoL/EB6B/wAaRX4AFxYZABcWGSQXFxq/GBgb/xkZHP8aGR3/Gxoe/xwb
        HtkcGx89HBwfABoYIQAbGh0AGxodEBkZHJwYGBv/Fxca/xYWGf8VFRf/FBQW7RQTFVwBABAAHn//Vx5/
        /+kegP//HoH//x6C//8egv//HoL//x6C//4egf+JHoD/BBgxUQAWFhgSFxYZsRgXGv8ZGBv/Ghkc/xsa
        Hf8cGx7/HBwf/x0cIM8eHSAmHRwgAB0cHwQbGh6JGhkc/hkYG/8YFxr/FxYZ/xYVGP8VFBf/ExMV5hMS
        FFIefv/EHn///x6A//8egf//HoL//x6C//8egv//HoL//x6B/+Megf8nGUB0ABYWGEwXFhn4GBca/xkY
        G/8aGh3/Gxse/xwcH/8dHCD/Hh0h/x4dIXUdHCAAHBsfJxwbHuMaGh3/GRkc/xgXGv8XFhn/FhUY/xUU
        F/8UExX/ExIUwR5+/9Uef///HoD//x6B//8egf//HoL//x6C//8egf//HoH/7h6B/zQaRX4AFhYZWhcW
        GfwYGBv/GRkc/xsaHf8cGx7/HRwg/x4dIf8fHiL/Hx4igx4dIQAdHB80HBsf7hsaHf8aGRz/GBgb/xcX
        Gf8WFRj/FRQX/xQTFf8TEhXSHn7/jR5///0egP//HoD//x6B//8egf//HoH//x6B//8egf+4HoD/EBk3
        YAAWFhkqFxYZ2xgYG/8ZGRz/Gxod/xwbHv8dHCD/Hh0h/x8eIu8gHyNKHx4iABwcHxAcGx64Gxod/xoZ
        HP8YGBv/Fxca/xYVGP8VFBf/FBMV/BMSFYgefv8VHn7/oh5///8egP//HoD//x6B//8egf//HoD/0B6A
        /zIegf8AFxIRABgaHQAXFxpQGBca5hkYHP8aGh3/Gxse/x0cH/8dHSD1Hh0hch8eIwQfHiIAGxseABwb
        HjIaGh3QGRkc/xgXG/8XFhn/FhUY/xUUF/8UExafExIVEx5+/wAefv8XHn7/qx5///8ef///Hn///x6A
        /9UegP85HoD/AB5//wAYIzUAFxcaABgYGwAYFxpXGRgb6hoZHP8bGh7/HBse+B0cH3weHSEFHh0hAB8e
        IgAbGh0AGxodABoZHDkZGBvVGBca/xcWGf8WFRj/FRQXqBQUFhUUExYAHn7/AB5+/wAefv8dHn7/yR5+
        //8efv/yHn//Sh5//wAef/8AHoD/AAAAAAAYFxoAGBcaABUXGgAZGBtUGRkcwxoaHc0bGh5yHBseCB0c
        HwAeHSEAAAAAABsaHQAaGRwAGRgbABgYG0oYFxryFxYZ/xYVGMUVFBcbFRQXABQUFgAefv8AHnz/AB56
        /wIefP+oHn3//x59/+ceff8lHn3/AB5//wAAAAAAAAAAAAAAAAAYGBoAGBgbABgXGgAaGRwNGhkdERkZ
        HAAbGh4AGxoeAAAAAAAAAAAAAAAAABkZHAAXFxoAFxcaJRcWGecWFRj/FRUXohMSFQEVFBcAFRQXAB50
        /wAed/8AHnn/CR56/7Qee///Hnv/6x57/y4eev8AHnj/AB53/wAAAAAAFRQXABYVGAAWFRgAFxYZJBgX
        GnEYFxp6GRgbNBkOEQAYGBsAGBcaAAAAAAAWFhgAFhYYABYWGQAXFhkuFhYY6xUVF/8VFBeuFBQWBxMT
        FQASERQAHnL/AB11/wMedv9zHnf/8x54//8eef/+Hnn/oR55/xEed/8AHnb/ABMSFgAbFx0AFRQXABUV
        Fy4WFhjJFxYZ/xgXGv8YFxreGBcaShcXGgAYFxoAFxYZABYVGAAWFRgAFhYYERYWGKEWFRj+FRQX/xQT
        FvETEhVuEhETAxIREwAgb/8CH3L/aR50//Iedf//Hnb//x53//8ed//+Hnf/lx52/w0edf8AGEOMABQT
        FgAUExYnFRQXxBUVF/8WFhj/FxYZ/xcXGv8XFxrdFxcaQRYWGQAWFRgAFhUYABYVGA0WFRiXFhUY/hUU
        F/8UExb/ExMV/xISFPARERNlEQ4RAh9t/1sfb//sH3H//x9y//8ec///HnT//x50//8edP/9HnT/hx9y
        /wUVKUwAEhIUFBMSFbYUExb/FBQW/xERE/8QEBL/FhUY/xYWGf8XFhnXFhYYYBYVGE4WFRhOFhUYmBUV
        F/wVFBf/FBMW/xMTFf8SEhT/ERET/xEQEuoQDxFXH2v/xh9s//8fbv//H2///x9w//8fcf//H3H//x9x
        //8fcf/kH3D/KBc3cQASERNOEhIU+BMSFf8SERT/DAwN/woJC/8QDxH/FRUX/xYVGP8WFRj7FRUX+BUU
        F/gVFBf/FBQW/xQTFv8TEhX/EhIU/xIRE/8REBL/EA8R/w8PEMMfZ//UH2n//x9r//8fbP//H23//x9u
        //8fbv//H27//x9u/+4fbv80Fzl6ABEQE1oRERP8EhIU/xERE/8ODg//DAwN/xAPEf8UExb/FBQW/xQU
        Fv8UFBb/FBMW/xQTFv8TExX/ExIU/xISFP8SERP/ERAS/xAQEv8PDxH/Dg4Q0R9l/4kfZf/8H2f//x9o
        //8faf//H2r//x9q//8fav//H2v/sh9r/w4VKlcAEBASKRAQEtoRERP/EhET/xEQEv8QEBL/EhIU/xMT
        Ff8TExXyExMVsxMTFagTEhWoExIU1BISFP8SERP/ERET/xEQEv8QDxH/Dw8R/w4OEPwODQ+EH2P/Ex9j
        /6AfZP//H2T//x9l//8fZv//H2b//x9n/8gfaP8qH2j/AA0CAAAREBIAEA8RThAQEuUREBL/ERET/xIR
        E/8SERP/EhEU9hISFHYTExUIEhEUAxMRFQISERMxERETzREQEv8QEBL/EA8R/w8OEP8ODhD/Dg0Pnw0N
        DxIfY/8AH2P/Fh9j/6kfY///H2P//x9j//8fY//MH2T/MB9m/wAfVP8AERgnABAPEQAQERMAEA8RVRAP
        EegQEBL/EBAS/xEQEvcRERN9EhETBhISFAATEhUAExIVABEREwAQEBI2EA8R0g8PEf8PDhD/Dg4P/w4N
        D6gNDQ8VDg0PAB9j/wAfY/8AH2P/HR9j/6QfY//nH2P/wh9j/zofZP8AH2P/AB9m/wAAAAAAEA8RAA8P
        EQAKDhIBDw8QYA8PEdcQDxHiEA8RgRAPEQkREBIAEhETAAAAAAARERMAEA8RABAPEQAPDhBADg4Qxg4N
        D+cODQ+iDQ0OHA0NDwAODQ8AACAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAGAIBw
        DgEAIAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAQAAHAOAAAgBAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgBAA=
</value>
  </data>
  <data name="&gt;&gt;btn_StockTaking.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txt_End.Properties.CalendarTimeProperties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_End.EditValue" type="System.DateTime, mscorlib">
    <value>2016-07-11</value>
  </data>
  <data name="&gt;&gt;btnRe_CodingCostCenter.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="btnRe_CodingCostCenter.Size" type="System.Drawing.Size, System.Drawing">
    <value>320, 23</value>
  </data>
  <data name="txt_Start.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl2.Text" xml:space="preserve">
    <value>To Date</value>
  </data>
  <data name="&gt;&gt;btn_IC_Trns_ALL.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;btn_PrInv.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txt_Start.Properties.CalendarTimeProperties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Start.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Start.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>38, 13</value>
  </data>
  <data name="btn_SLReturnInv.Text" xml:space="preserve">
    <value>Repost Sales Return Invoices</value>
  </data>
  <data name="labelControl1.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="btn_StockTaking.Size" type="System.Drawing.Size, System.Drawing">
    <value>320, 23</value>
  </data>
  <data name="&gt;&gt;labelControl2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Start.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;btn_Re_SerialMontlyJournalCode.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Start.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;btn_StockTaking.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="&gt;&gt;btn_IC_Trns_ALL.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;btn_ReEvaluate.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="&gt;&gt;txt_Start.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="btn_PrInv.Text" xml:space="preserve">
    <value>RePost Purchase Invoices</value>
  </data>
  <data name="txt_Start.Properties.CalendarTimeProperties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="btn_Re_SerialMontlyJournalCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>15, 337</value>
  </data>
  <data name="&gt;&gt;btnRe_CodingCostCenter.Name" xml:space="preserve">
    <value>btnRe_CodingCostCenter</value>
  </data>
  <data name="&gt;&gt;btn_StockTaking.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_End.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>278, 44</value>
  </data>
  <data name="&gt;&gt;btn_SLReturnInv.Name" xml:space="preserve">
    <value>btn_SLReturnInv</value>
  </data>
  <data name="labelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>50, 13</value>
  </data>
  <data name="progressBar1.Size" type="System.Drawing.Size, System.Drawing">
    <value>323, 23</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>frm_ReEvaluate</value>
  </data>
  <data name="btn_Re_SerialMontlyJournalCode.Text" xml:space="preserve">
    <value>Re_Insert All Journal Monthly Serial</value>
  </data>
  <data name="btn_Re_SerialMontlyJournalCode.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="&gt;&gt;labelControl2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txt_End.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="btn_PrInv.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="&gt;&gt;btn_Re_SerialMontlyJournalCode.Name" xml:space="preserve">
    <value>btn_Re_SerialMontlyJournalCode</value>
  </data>
  <data name="txt_End.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Start.Properties.CalendarTimeProperties.Mask.EditMask" xml:space="preserve">
    <value>T</value>
  </data>
  <data name="btn_IC_Trns_ALL.Location" type="System.Drawing.Point, System.Drawing">
    <value>17, 221</value>
  </data>
  <data name="txt_End.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="txt_End.Properties.CalendarTimeProperties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_End.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txt_End.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.DateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_End.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;btn_PrInv.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;btn_RepostAllProcess.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="btn_RepostAllProcess.Text" xml:space="preserve">
    <value>Repost All Processes</value>
  </data>
  <data name="&gt;&gt;btn_RepostAllProcess.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="txt_Start.Properties.CalendarTimeProperties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Start.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="btn_StockTaking.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="btn_IC_Trns_ALL.Text" xml:space="preserve">
    <value>Repost All In/Out Trans.</value>
  </data>
  <data name="&gt;&gt;txt_End.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_End.Name" xml:space="preserve">
    <value>txt_End</value>
  </data>
  <data name="btn_IC_Trns_ALL.Size" type="System.Drawing.Size, System.Drawing">
    <value>318, 23</value>
  </data>
  <assembly alias="DevExpress.Utils.v15.1" name="DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="txt_End.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="txt_End.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="txt_End.Properties.CalendarTimeProperties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_End.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_End.Properties.CalendarTimeProperties.Mask.EditMask" xml:space="preserve">
    <value>T</value>
  </data>
  <data name="txt_Start.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;btn_ReCodingAccounts.Name" xml:space="preserve">
    <value>btn_ReCodingAccounts</value>
  </data>
  <data name="&gt;&gt;labelControl2.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="txt_Start.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;progressBar1.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="btnRe_CodingCostCenter.Text" xml:space="preserve">
    <value>Re-Coding CostCenter</value>
  </data>
  <data name="&gt;&gt;labelControl1.Name" xml:space="preserve">
    <value>labelControl1</value>
  </data>
  <data name="btn_StockTaking.Text" xml:space="preserve">
    <value>Repost StockTaking</value>
  </data>
  <data name="txt_Start.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_End.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_End.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;btn_ReEvaluate.Name" xml:space="preserve">
    <value>btn_ReEvaluate</value>
  </data>
  <data name="&gt;&gt;labelControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txt_Start.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;btn_Re_SerialMontlyJournalCode.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="btnRe_CodingCostCenter.Location" type="System.Drawing.Point, System.Drawing">
    <value>17, 279</value>
  </data>
  <data name="&gt;&gt;btn_SLReturnInv.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="txt_End.Properties.CalendarTimeProperties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;btnRe_CodingCostCenter.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txt_Start.Properties.CalendarTimeProperties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="btn_SLReturnInv.Location" type="System.Drawing.Point, System.Drawing">
    <value>15, 133</value>
  </data>
  <data name="&gt;&gt;progressBar1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txt_Start.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;txt_End.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="&gt;&gt;btn_ReEvaluate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.XtraForm, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_End.Properties.CalendarTimeProperties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="btn_ReCodingAccounts.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="&gt;&gt;btn_ReCodingAccounts.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txt_Start.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.DateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl1.Text" xml:space="preserve">
    <value>From Date</value>
  </data>
  <data name="&gt;&gt;btn_ReCodingAccounts.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="txt_Start.EditValue" type="System.DateTime, mscorlib">
    <value>2016-07-11</value>
  </data>
  <data name="&gt;&gt;btn_Re_SerialMontlyJournalCode.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btn_Replacement.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="&gt;&gt;btn_PrInv.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="btn_ReCodingAccounts.Location" type="System.Drawing.Point, System.Drawing">
    <value>17, 308</value>
  </data>
  <data name="txt_End.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;btn_IC_Trns_ALL.Name" xml:space="preserve">
    <value>btn_IC_Trns_ALL</value>
  </data>
  <data name="progressBar1.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 412</value>
  </data>
  <data name="&gt;&gt;btn_SLReturnInv.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl1.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="txt_End.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_End.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;labelControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="btn_PrInv.Location" type="System.Drawing.Point, System.Drawing">
    <value>15, 104</value>
  </data>
  <data name="txt_Start.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Start.Properties.CalendarTimeProperties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_End.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;btn_PrInv.Name" xml:space="preserve">
    <value>btn_PrInv</value>
  </data>
  <data name="txt_End.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_End.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="btn_Replacement.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="txt_Start.Location" type="System.Drawing.Point, System.Drawing">
    <value>172, 41</value>
  </data>
  <data name="btn_ReEvaluate.Text" xml:space="preserve">
    <value>Reevaluate Out Transactions</value>
  </data>
  <data name="txt_End.Properties.CalendarTimeProperties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;btn_RepostAllProcess.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txt_Start.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="txt_Start.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;btn_SLReturnInv.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>349, 447</value>
  </data>
  <data name="&gt;&gt;btn_ReEvaluate.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txt_End.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_End.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="txt_Start.Properties.CalendarTimeProperties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="btn_Re_SerialMontlyJournalCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>320, 23</value>
  </data>
  <data name="txt_End.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;btn_Replacement.Name" xml:space="preserve">
    <value>btn_Replacement</value>
  </data>
  <data name="&gt;&gt;txt_Start.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="txt_End.Location" type="System.Drawing.Point, System.Drawing">
    <value>15, 41</value>
  </data>
  <data name="txt_Start.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;btn_ReCodingAccounts.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="btn_SLReturnInv.Size" type="System.Drawing.Size, System.Drawing">
    <value>320, 23</value>
  </data>
  <data name="txt_Start.Properties.CalendarTimeProperties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>DateTime</value>
  </data>
  <data name="btn_PrInv.Size" type="System.Drawing.Size, System.Drawing">
    <value>320, 23</value>
  </data>
  <data name="txt_Start.Properties.CalendarTimeProperties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;btn_Replacement.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="btn_ReEvaluate.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="btn_RepostAllProcess.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="&gt;&gt;labelControl2.Name" xml:space="preserve">
    <value>labelControl2</value>
  </data>
  <data name="&gt;&gt;progressBar1.Name" xml:space="preserve">
    <value>progressBar1</value>
  </data>
  <data name="labelControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>121, 44</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.Language" type="System.Globalization.CultureInfo, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>ar-EG</value>
  </metadata>
</root>