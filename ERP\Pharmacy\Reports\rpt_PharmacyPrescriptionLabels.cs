using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using System.Linq;
using DAL;
using System.Data;
using System.Drawing.Printing;

namespace Pharmacy.Reports
{
    public partial class rpt_PharmacyPrescriptionLabels : DevExpress.XtraReports.UI.XtraReport
    {
        ST_CompanyInfo cmp;
        ST_BarcodeTemplate template;
        DataTable dt_Items_Prescriptions = new DataTable();
        int StartFrom = 0;
        string customer_Name, Sell_Date;

        public rpt_PharmacyPrescriptionLabels(DataTable items_Prescriptions, int start_from, ST_BarcodeTemplate Template,string _CustomerName,string _SellDate)
        {
            InitializeComponent();
            lbl_Line_1.BeforePrint += new PrintEventHandler(lbl_Line_1_BeforePrint);
            lbl_Line_2.BeforePrint += new PrintEventHandler(lbl_Line_2_BeforePrint);
            lbl_Line_3.BeforePrint += new PrintEventHandler(lbl_Line_3_BeforePrint);
            lbl_Line_4.BeforePrint += new PrintEventHandler(lbl_Line_4_BeforePrint);
            
            StartFrom = start_from;
            customer_Name = _CustomerName;
            Sell_Date = _SellDate;

            dt_Items_Prescriptions = items_Prescriptions;

            cmp = new ERPDataContext().ST_CompanyInfos.FirstOrDefault();

            #region Template_Data
            if (Template == null)
                template = (from t in new ERPDataContext().ST_BarcodeTemplates
                            where t.IsDefault == true
                            select t).FirstOrDefault();
            else
                template = Template;

            PaperKind paper_kind = (PaperKind)Enum.Parse(typeof(PaperKind), template.PageName);

            this.PaperKind = paper_kind;

            if (paper_kind == PaperKind.Custom)
            {
                this.PageHeight = template.PaperHeight;
                this.PageWidth = template.PaperWidth;                
            }

            xrPanel1.Borders = DevExpress.XtraPrinting.BorderSide.None;

            topMarginBand1.HeightF = template.Margins_Top;
            bottomMarginBand1.HeightF = template.Margins_Bottom;
            Margins.Left = template.Margins_Left;
            Margins.Right = template.Margins_Right;
            Margins.Top = template.Margins_Top;
            Margins.Bottom = template.Margins_Bottom;

            Detail.MultiColumn.ColumnCount = template.ColumnsCount;
            Detail.MultiColumn.ColumnWidth = (this.PageSize.Width - Margins.Left - Margins.Right) / template.ColumnsCount;
            Detail.MultiColumn.ColumnWidth = Detail.MultiColumn.ColumnWidth - 1;
            Detail.RepeatCountOnEmptyDataSource = template.RowsCount * template.ColumnsCount;
            Detail.HeightF = ((float)this.PageSize.Height - Margins.Top - Margins.Bottom) / (float)template.RowsCount;
            Detail.HeightF = (int)Detail.HeightF;
            Detail.HeightF = Detail.HeightF - 1;
            
            xrPanel1.HeightF = Detail.HeightF;
            xrPanel1.WidthF = Detail.MultiColumn.ColumnWidth;
            
            #endregion            
            
            Print_BarCode_Labels();
        }


        private void Print_BarCode_Labels()
        {
            
            lbl_Line_1.Font = ChangeFontSize(lbl_Line_1.Font, (float)template.Line_1_FontSize);
            lbl_Line_2.Font = ChangeFontSize(lbl_Line_2.Font, (float)template.Line_2_FontSize);            
            lbl_Line_3.Font = ChangeFontSize(lbl_Line_3.Font, (float)template.Line_3_FontSize);            
            lbl_Line_4.Font = ChangeFontSize(lbl_Line_4.Font, (float)template.Line_4_FontSize);                        
            lbl_Line_1.HeightF = lbl_Line_1.Font.Size * 5;
            lbl_Line_2.HeightF = lbl_Line_2.Font.Size * 5;
            lbl_Line_3.HeightF = lbl_Line_3.Font.Size * 5;
            lbl_Line_4.HeightF = lbl_Line_4.Font.Size * 5 * 2;
            lbl_Line_4.CanGrow = true;
            lbl_Line_4.WordWrap = true;            

            // Set Lines Widths by panel width
            lbl_Line_1.Width = lbl_Line_4.Width = lbl_Line_2.Width = lbl_Line_3.Width = (int)xrPanel1.WidthF * 8 / 10;

            // How many label you want in Page            
            DataTable datasource = FormatDataSourceUsingTemplate();

            this.DataSource = datasource;

            lbl_Line_1.DataBindings.Add("Text", datasource, "Line1");
            lbl_Line_2.DataBindings.Add("Text", datasource, "Line2");
            lbl_Line_3.DataBindings.Add("Text", datasource, "Line3");
            lbl_Line_4.DataBindings.Add("Text", datasource, "Line4");
        }

        private DataTable FormatDataSourceUsingTemplate()
        {
            ERPDataContext DB = new ERPDataContext();
            ST_CompanyInfo comp = DB.ST_CompanyInfos.FirstOrDefault();

            DataTable dt = new DataTable();
            dt.Columns.Add("Line1");
            dt.Columns.Add("Line2");
            dt.Columns.Add("Line3");
            dt.Columns.Add("Line4");

            for (int i = 1; i < StartFrom; i++)
                dt.Rows.Add("", "", "", "");

            foreach (DataRow dr in dt_Items_Prescriptions.Rows)
            {
                for (int i = 1; i <= decimal.ToInt32(Convert.ToDecimal(dr["Qty"])); i++)
                {
                    DataRow barcode_Row = dt.NewRow();

                    barcode_Row["Line1"] = comp.CmpNameAr + " " + comp.CmpTel;
                    barcode_Row["Line2"] = Sell_Date;
                    barcode_Row["Line3"] = customer_Name;
                    barcode_Row["Line4"] = dr["ItemName"] + "\r\n" + dr["ItemDescription"];
                    dt.Rows.Add(barcode_Row);
                }
            }
            return dt;
        }

        private Font ChangeFontSize(Font font, float fontSize)
        {
            if (font != null && fontSize > 0)
            {
                float currentSize = font.Size;
                if (currentSize != fontSize)
                {
                    font = new Font(font.Name, fontSize,
                        font.Style, font.Unit,
                        font.GdiCharSet, font.GdiVerticalFont);
                }
            }
            return font;
        }

        private void lbl_Line_1_BeforePrint(object sender, System.Drawing.Printing.PrintEventArgs e)
        {
            lbl_Line_1.LocationF = new PointF(30, 5);
        }

        private void lbl_Line_2_BeforePrint(object sender, System.Drawing.Printing.PrintEventArgs e)
        {
            lbl_Line_2.LocationF = new PointF(30, lbl_Line_1.LocationF.Y + lbl_Line_1.HeightF);
        }

        private void lbl_Line_3_BeforePrint(object sender, System.Drawing.Printing.PrintEventArgs e)
        {

            lbl_Line_3.LocationF = new PointF(30, lbl_Line_2.LocationF.Y + lbl_Line_2.HeightF);
        }

        private void lbl_Line_4_BeforePrint(object sender, System.Drawing.Printing.PrintEventArgs e)
        {
            lbl_Line_4.LocationF = new PointF(30, lbl_Line_3.LocationF.Y + lbl_Line_3.HeightF);
        }

        int count = 0;

        private void xrPanel1_PrintOnPage(object sender, PrintOnPageEventArgs e)
        {
            count++;
            if (count < StartFrom)
                e.Cancel = true;
        }
       
    }
}
