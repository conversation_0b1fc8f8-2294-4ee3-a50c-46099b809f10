﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;

namespace Pharmacy.Forms
{
    public partial class frm_ST_ChangeUserPass : DevExpress.XtraEditors.XtraForm
    {
        public frm_ST_ChangeUserPass()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
        }

        private void frm_ST_Print_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            txtCrntUserName.Text = Shared.UserName;
        }

        private void btnSubmit_Click(object sender, EventArgs e)
        {
            if (txtCrntPass.Text == string.Empty)
            {
                txtCrntPass.ErrorText = "*";
                return;
            }

            ERPDataContext DB = new ERPDataContext();

            var user = (from u in DB.HR_Users
                        where u.UserName == txtCrntUserName.Text.Trim() &&
                        u.Password == Crypto.EncryptStringAES(txtCrntPass.Text.Trim(), Crypto.Key) &&
                        u.IsDeleted == false
                        select u).SingleOrDefault();            

            if (user == null)
            {
                MessageBox.Show("عفوا, الاسم أو الرقم السري غير صحيح", "خطأ في الدخول", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
            }
            else
            {
                grpCrnt.Enabled = false;
                grpNew.Visible = true;
                txtNewUserName.Text = Shared.UserName;

                if (user.UserId == 1)
                {
                    txtNewUserName.Enabled = false;//user not allowed
                    txtNewPass1.Focus();
                }
                else
                    txtCrntUserName.Focus();
            }
        }

        private void btnSubmit2_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(txtNewUserName.Text.Trim()))
            {
                txtNewUserName.ErrorText = Shared.IsEnglish == true ? ResEn.valUserName : ResAr.valUserName;
                txtNewUserName.Focus();
                return;
            }
            if (string.IsNullOrEmpty(txtNewPass1.Text.Trim()) || string.IsNullOrEmpty(txtNewPass2.Text.Trim())
                || txtNewPass1.Text.Trim() != txtNewPass2.Text.Trim())
            {
                txtNewPass2.ErrorText = Shared.IsEnglish == true ? ResEn.ValPass : ResAr.ValPass;
                txtNewPass2.Focus();
                return;
            }

            ERPDataContext DB = new ERPDataContext();

            var user = (from u in DB.HR_Users
                        where u.UserId == Shared.UserId
                        select u).SingleOrDefault();
            user.UserName = txtNewUserName.Text.Trim();
            user.Password = Crypto.EncryptStringAES(txtNewPass1.Text.Trim(), Crypto.Key);
            DB.SubmitChanges();
            XtraMessageBox.Show(Shared.IsEnglish == true ? ResAccEn.MsgSave : ResAccAr.MsgSave,
    "", MessageBoxButtons.OK, MessageBoxIcon.Information);
            
        }
    }
}