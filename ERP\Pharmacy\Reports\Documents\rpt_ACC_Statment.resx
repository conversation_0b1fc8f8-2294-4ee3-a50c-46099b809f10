<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="cell_process.Text" xml:space="preserve">
    <value>نوع العملية</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="lbl_accountType.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="xrTableCell8.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>BottomCenter</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="xrTableRow5.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="xrTableCell2.Weight" type="System.Double, mscorlib">
    <value>0.*****************</value>
  </data>
  <data name="xrTable1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>725.5001, 30.12499</value>
  </data>
  <data name="cell_fDebit.Weight" type="System.Double, mscorlib">
    <value>0.****************</value>
  </data>
  <data name="xrPageInfo1.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrLabel4.Text" xml:space="preserve">
    <value>To</value>
  </data>
  <data name="xrLabel3.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>62.0213623, 24.4999847</value>
  </data>
  <data name="xrLabel2.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cell_debit.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="PageHeader.HeightF" type="System.Single, mscorlib">
    <value>30.12499</value>
  </data>
  <data name="cell_credit.Text" xml:space="preserve">
    <value>دائن</value>
  </data>
  <data name="lblTotal.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="xrTableCell7.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="lbl_fromDate.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="cell_balance.Text" xml:space="preserve">
    <value>الرصيد</value>
  </data>
  <data name="lbl_accountType.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>105.2085, 24.5</value>
  </data>
  <data name="xrTable2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>12.50007, 0</value>
  </data>
  <data name="xrTableCell7.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="xrLabel4.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrTable4.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>188.4789, 25</value>
  </data>
  <data name="TopMargin.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="cell_credit.Weight" type="System.Double, mscorlib">
    <value>0.*****************</value>
  </data>
  <data name="cell_P2.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt, style=Bold</value>
  </data>
  <data name="cell_credit.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="cell_header_P4.Text" xml:space="preserve">
    <value>P4</value>
  </data>
  <data name="xrTableCell10.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>BottomCenter</value>
  </data>
  <data name="lbl_accountType.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblReportName.Text" xml:space="preserve">
    <value>Account Statement</value>
  </data>
  <data name="cell_CrncRate.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cell_debit.Text" xml:space="preserve">
    <value>مدين</value>
  </data>
  <data name="xrLabel8.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleLeft</value>
  </data>
  <data name="lbl_CurrencyName.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="xrPageInfo1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>109.375, 23</value>
  </data>
  <data name="xrLine1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>12.5001, 165.9583</value>
  </data>
  <data name="lbl_fromDate.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>125.0418, 24.49998</value>
  </data>
  <data name="lbl_TotalCredit.Text" xml:space="preserve">
    <value> </value>
  </data>
  <data name="xrCrossBandLine3.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>384.5, 1.041667</value>
  </data>
  <data name="cell_header_P1.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="lbl_TotalCredit.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrTableRow1.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Moccasin</value>
  </data>
  <data name="xrTable3.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>12.04166, 51.79164</value>
  </data>
  <data name="xrLine1.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>DarkGray</value>
  </data>
  <data name="xrCrossBandBox1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>12.04166, 0</value>
  </data>
  <data name="lbl_CurrencyName.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="$this.Margins" type="System.Drawing.Printing.Margins, System.Drawing">
    <value>34, 43, 206, 45</value>
  </data>
  <data name="xrTable1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>12.5001, 0</value>
  </data>
  <data name="xrTableRow1.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="rpt_ACC_Statment.ExportOptions.Csv.Separator" xml:space="preserve">
    <value>,</value>
  </data>
  <data name="xrTableCell5.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="cell_header_P5.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="$this.PageWidth" type="System.Int32, mscorlib">
    <value>827</value>
  </data>
  <data name="xrTableRow3.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="cell_header_P0.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="cell_process.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrTableCell6.Text" xml:space="preserve">
    <value>Debit</value>
  </data>
  <data name="cell_P3.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt, style=Bold</value>
  </data>
  <data name="cell_insertDate.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_TotalDebit.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>188.5, 1.041667</value>
  </data>
  <data name="lblTotal.Text" xml:space="preserve">
    <value>..</value>
  </data>
  <data name="cell_P2.Weight" type="System.Double, mscorlib">
    <value>0.96337591187803551</value>
  </data>
  <data name="xrLabel3.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt, style=Bold</value>
  </data>
  <data name="xrTableCell8.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="xrCrossBandBox2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>115.8391, 0</value>
  </data>
  <data name="xrLabel8.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>64.5835, 24.49998</value>
  </data>
  <data name="lblTotal.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>BottomLeft</value>
  </data>
  <data name="xrCrossBandBox1.StartPointFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>12.04166, 0</value>
  </data>
  <data name="cell_header_P3.Text" xml:space="preserve">
    <value>P3</value>
  </data>
  <data name="cell_P0.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrTableCell8.Text" xml:space="preserve">
    <value>#</value>
  </data>
  <data name="lbl_User.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>125.0418, 24.49998</value>
  </data>
  <data name="cell_header_P3.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrLabel6.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleLeft</value>
  </data>
  <data name="xrLabel4.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleLeft</value>
  </data>
  <data name="cell_P0.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt, style=Bold</value>
  </data>
  <data name="xrLabel1.Text" xml:space="preserve">
    <value>أعمارالديون</value>
  </data>
  <data name="lbl_User.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrTableCell3.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>BottomCenter</value>
  </data>
  <data name="picLogo.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>13.0833311, 86</value>
  </data>
  <data name="xrLabel7.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrTableCell10.Weight" type="System.Double, mscorlib">
    <value>0.167730765517889</value>
  </data>
  <data name="lbl_fromDate.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>611.9168, 86</value>
  </data>
  <data name="cell_header_P1.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>BottomCenter</value>
  </data>
  <data name="Detail.HeightF" type="System.Single, mscorlib">
    <value>29.16667</value>
  </data>
  <data name="TopMargin.HeightF" type="System.Single, mscorlib">
    <value>206</value>
  </data>
  <data name="xrLabel3.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>584.2917, 175.3751</value>
  </data>
  <data name="xrTableCell10.Text" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="xrTableRow2.BackColor" type="System.Drawing.Color, System.Drawing">
    <value />
  </data>
  <data name="lbl_toDate.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_TotalCredit.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="lbl_CurrencyName.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblCompName.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="lblAcNumber.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>94.24944, 24.5</value>
  </data>
  <data name="xrTableCell7.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>BottomCenter</value>
  </data>
  <data name="cell_CrncRate.Weight" type="System.Double, mscorlib">
    <value>0.*****************</value>
  </data>
  <data name="cell_header_P5.Text" xml:space="preserve">
    <value>P5</value>
  </data>
  <data name="cell_header_P0.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>BottomCenter</value>
  </data>
  <data name="xrTableCell8.Weight" type="System.Double, mscorlib">
    <value>0.091125337222150216</value>
  </data>
  <data name="xrTableCell2.Text" xml:space="preserve">
    <value>Credit</value>
  </data>
  <data name="xrCrossBandLine5.EndPointFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>624.4375, 0</value>
  </data>
  <data name="$this.PaperKind" type="System.Drawing.Printing.PaperKind, System.Drawing">
    <value>A4</value>
  </data>
  <data name="xrTableCell5.Weight" type="System.Double, mscorlib">
    <value>0.26106811143270975</value>
  </data>
  <data name="lbl_TotalDebit.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="xrPageInfo1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>14.62526, 167.7083</value>
  </data>
  <data name="cell_header_P4.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrLabel3.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel3.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="xrLabel7.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt, style=Bold</value>
  </data>
  <data name="cell_P0.Weight" type="System.Double, mscorlib">
    <value>0.****************</value>
  </data>
  <data name="lbl_accountName.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleLeft</value>
  </data>
  <data name="cell_header_P3.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="cell_header_P5.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>BottomCenter</value>
  </data>
  <data name="lbl_User.Text" xml:space="preserve">
    <value>..</value>
  </data>
  <data name="xrCrossBandLine6.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>707.7708, 0</value>
  </data>
  <data name="cell_JNumber.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="xrLabel1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>659.8751, 25.29165</value>
  </data>
  <data name="xrCrossBandLine6.StartPointFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>707.7708, 0</value>
  </data>
  <data name="cell_P5.Weight" type="System.Double, mscorlib">
    <value>0.96337593539038435</value>
  </data>
  <data name="cell_process.Weight" type="System.Double, mscorlib">
    <value>0.22762079669633811</value>
  </data>
  <data name="xrLine1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>725.5002, 3.541748</value>
  </data>
  <data name="cell_notes.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="cell_index.Text" xml:space="preserve">
    <value>#</value>
  </data>
  <data name="lbl_toDate.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel6.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>64.58347, 24.49998</value>
  </data>
  <data name="xrTableCell8.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrLabel8.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrCrossBandLine1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>187.5, 1.041683</value>
  </data>
  <data name="xrTable1.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="BottomMargin.HeightF" type="System.Single, mscorlib">
    <value>45</value>
  </data>
  <data name="cell_index.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lblReportName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>546.3333, 50</value>
  </data>
  <data name="picLogo.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>139.8336, 73.49994</value>
  </data>
  <data name="xrLabel2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>385.249237, 175.3751</value>
  </data>
  <data name="cell_header_P3.Weight" type="System.Double, mscorlib">
    <value>0.*****************</value>
  </data>
  <data name="cell_header_P5.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="lbl_TotalDebit.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>68.22855, 20.91669</value>
  </data>
  <data name="cell_insertDate.Text" xml:space="preserve">
    <value>التاريخ</value>
  </data>
  <data name="lbl_accountName.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="cell_header_P2.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="cell_header_P4.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="xrTableCell9.Text" xml:space="preserve">
    <value>Balance</value>
  </data>
  <data name="cell_P5.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="cell_fCredit.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cell_header_P0.Text" xml:space="preserve">
    <value>P0</value>
  </data>
  <data name="xrCrossBandLine1.EndPointFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>187.5, 23</value>
  </data>
  <data name="xrLabel6.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>546.3333, 135</value>
  </data>
  <data name="lblCompName.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 18pt, style=Bold</value>
  </data>
  <data name="xrCrossBandLine2.StartPointFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>312.5, 0.06771088</value>
  </data>
  <data name="xrLine1.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>DarkGray</value>
  </data>
  <data name="cell_header_P3.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>BottomCenter</value>
  </data>
  <data name="cell_header_P1.Weight" type="System.Double, mscorlib">
    <value>0.63782661069134849</value>
  </data>
  <data name="xrTableCell7.Text" xml:space="preserve">
    <value>Note</value>
  </data>
  <data name="xrTableCell6.Weight" type="System.Double, mscorlib">
    <value>0.21948044773739456</value>
  </data>
  <data name="xrTableCell9.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>BottomCenter</value>
  </data>
  <data name="cell_JCode.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrTableCell6.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>BottomCenter</value>
  </data>
  <data name="xrTableCell10.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="xrTableCell10.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="cell_JCode.Weight" type="System.Double, mscorlib">
    <value>0.16773094014407997</value>
  </data>
  <data name="cell_debit.Weight" type="System.Double, mscorlib">
    <value>0.21948042749677868</value>
  </data>
  <data name="cell_fCredit.Weight" type="System.Double, mscorlib">
    <value>0.3743743896484375</value>
  </data>
  <data name="xrCrossBandLine5.StartPointFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>624.4375, 1.041667</value>
  </data>
  <data name="xrTableCell2.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="lblTotal.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>13.08333, 123.6666</value>
  </data>
  <data name="lblCompName.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>442.1461, 29</value>
  </data>
  <data name="xrCrossBandBox2.EndPointFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>115.8391, 22.95837</value>
  </data>
  <data name="lbl_User.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>611.9168, 135</value>
  </data>
  <data name="xrTableCell3.Text" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="lblReportName.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>190.6253, 29</value>
  </data>
  <data name="lblReportName.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 16pt, style=Bold</value>
  </data>
  <data name="lbl_TotalDebit.Text" xml:space="preserve">
    <value> </value>
  </data>
  <data name="cell_P4.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt, style=Bold</value>
  </data>
  <data name="xrLabel4.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>64.5835, 24.49998</value>
  </data>
  <data name="xrTableCell2.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>BottomCenter</value>
  </data>
  <data name="xrTableCell5.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>BottomCenter</value>
  </data>
  <data name="xrLabel7.Text" xml:space="preserve">
    <value>Account Name</value>
  </data>
  <data name="xrLabel8.Text" xml:space="preserve">
    <value>From</value>
  </data>
  <data name="xrCrossBandLine3.EndPointFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>384.5, 0</value>
  </data>
  <data name="xrLabel1.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="cell_index.Weight" type="System.Double, mscorlib">
    <value>0.091125162503191581</value>
  </data>
  <data name="$this.PageHeight" type="System.Int32, mscorlib">
    <value>1169</value>
  </data>
  <data name="xrLabel7.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>101.213837, 24.4999847</value>
  </data>
  <data name="xrTable3.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopCenter</value>
  </data>
  <data name="xrTableCell5.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="cell_CrncId.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="xrTableRow2.Weight" type="System.Double, mscorlib">
    <value>0.54901959587545957</value>
  </data>
  <data name="cell_JCode.Text" xml:space="preserve">
    <value>رقم القيد</value>
  </data>
  <data name="xrTable3.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>725.9586, 54.41667</value>
  </data>
  <data name="xrTableCell9.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="cell_P2.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="cell_balance.Weight" type="System.Double, mscorlib">
    <value>0.32169763494223585</value>
  </data>
  <data name="xrTable2.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="xrLabel3.Text" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="xrCrossBandLine6.EndPointFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>707.7708, 0</value>
  </data>
  <data name="cell_notes.Text" xml:space="preserve">
    <value>البيان</value>
  </data>
  <data name="cell_CrncId.Weight" type="System.Double, mscorlib">
    <value>0.35416717529296882</value>
  </data>
  <data name="lbl_User.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel5.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>272.7655, 175.375092</value>
  </data>
  <data name="xrCrossBandLine3.StartPointFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>384.5, 1.041667</value>
  </data>
  <data name="cell_P3.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="cell_insertDate.Weight" type="System.Double, mscorlib">
    <value>0.26106815858643057</value>
  </data>
  <data name="cell_balance.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lblCompName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>13.0833311, 50</value>
  </data>
  <data name="xrLabel8.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>546.3333, 86</value>
  </data>
  <data name="xrLabel7.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>13.0833311, 175.3751</value>
  </data>
  <data name="lbl_accountType.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>472.375366, 175.3751</value>
  </data>
  <data name="xrTableRow1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="cell_P1.Weight" type="System.Double, mscorlib">
    <value>0.**************</value>
  </data>
  <data name="lblTotal.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>360.896, 27.62496</value>
  </data>
  <data name="lblTotal.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="xrLabel1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrTableCell6.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrLine1.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>DarkGray</value>
  </data>
  <data name="xrCrossBandBox2.StartPointFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>115.8391, 0</value>
  </data>
  <data name="lbl_toDate.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>611.9168, 110.500015</value>
  </data>
  <data name="cell_P4.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel6.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrCrossBandLine2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>312.5, 0.06771088</value>
  </data>
  <data name="xrTableCell5.Text" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="cell_header_P5.Weight" type="System.Double, mscorlib">
    <value>0.*****************</value>
  </data>
  <data name="xrLabel5.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>101.213837, 24.4999847</value>
  </data>
  <data name="cell_header_P1.Text" xml:space="preserve">
    <value>P1</value>
  </data>
  <data name="xrTable2.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="cell_header_P4.Weight" type="System.Double, mscorlib">
    <value>0.*****************</value>
  </data>
  <data name="lbl_TotalCredit.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>116.2297, 1.041667</value>
  </data>
  <data name="lbl_accountType.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrTable1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="xrTableCell9.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="Detail.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="cell_header_P4.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>BottomCenter</value>
  </data>
  <data name="lbl_TotalCredit.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>70.77026, 20.91669</value>
  </data>
  <data name="xrLabel2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>87.12613, 24.4999847</value>
  </data>
  <assembly alias="DevExpress.Printing.v15.1.Core" name="DevExpress.Printing.v15.1.Core, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="rpt_ACC_Statment.ExportOptions.Csv.EncodingType" type="DevExpress.XtraPrinting.EncodingType, DevExpress.Printing.v15.1.Core">
    <value>Default</value>
  </data>
  <data name="cell_header_P0.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="cell_fDebit.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="xrPageInfo1.Format" xml:space="preserve">
    <value>Page {0} of {1} </value>
  </data>
  <data name="xrLabel2.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt, style=Bold</value>
  </data>
  <data name="xrTableCell3.Weight" type="System.Double, mscorlib">
    <value>0.*****************</value>
  </data>
  <data name="lbl_accountName.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>248.786469, 24.5</value>
  </data>
  <data name="xrCrossBandLine2.EndPointFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>312.5, 1.000023</value>
  </data>
  <data name="xrTableCell7.Weight" type="System.Double, mscorlib">
    <value>0.****************</value>
  </data>
  <data name="xrTableCell3.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="lbl_accountName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>115.8391, 175.3751</value>
  </data>
  <data name="lbl_toDate.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>125.0418, 24.49998</value>
  </data>
  <data name="xrLabel4.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>546.3333, 110.500015</value>
  </data>
  <data name="cell_header_P2.Text" xml:space="preserve">
    <value>P2</value>
  </data>
  <data name="cell_P1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt, style=Bold</value>
  </data>
  <data name="xrCrossBandLine1.StartPointFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>187.5, 1.041683</value>
  </data>
  <data name="xrLabel1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>78.12518, 24.49998</value>
  </data>
  <data name="lbl_fromDate.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="xrTableRow2.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt, style=Bold</value>
  </data>
  <data name="cell_JNumber.Weight" type="System.Double, mscorlib">
    <value>0.35416656494140625</value>
  </data>
  <data name="cell_header_P1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrTableCell9.Weight" type="System.Double, mscorlib">
    <value>0.32169756944885119</value>
  </data>
  <data name="xrCrossBandBox1.EndPointFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>12.04166, 1.041676</value>
  </data>
  <data name="cell_notes.Weight" type="System.Double, mscorlib">
    <value>0.74179644414177193</value>
  </data>
  <data name="xrLabel6.Text" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="cell_P4.Weight" type="System.Double, mscorlib">
    <value>0.96337587604971153</value>
  </data>
  <data name="lblReportName.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleLeft</value>
  </data>
  <data name="cell_P1.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lblAcNumber.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>562.5, 9.999974</value>
  </data>
  <data name="cell_header_P2.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="cell_P5.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt, style=Bold</value>
  </data>
  <data name="lbl_CurrencyName.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>94.24944, 24.5</value>
  </data>
  <data name="lbl_CurrencyName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>646.313049, 175.3751</value>
  </data>
  <data name="xrTableRow4.Weight" type="System.Double, mscorlib">
    <value>1</value>
  </data>
  <data name="cell_header_P0.Weight" type="System.Double, mscorlib">
    <value>0.63782653344959139</value>
  </data>
  <data name="xrTable4.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>436.9587, 126.2915</value>
  </data>
  <data name="cell_header_P2.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>BottomCenter</value>
  </data>
  <data name="ReportFooter.HeightF" type="System.Single, mscorlib">
    <value>211</value>
  </data>
  <data name="xrTable2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>725.5002, 29.16667</value>
  </data>
  <data name="BottomMargin.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="lbl_TotalDebit.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrCrossBandLine5.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>624.4375, 1.041667</value>
  </data>
  <data name="xrLabel2.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrTableCell6.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="xrTableCell2.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="xrLabel2.Text" xml:space="preserve">
    <value>Account Type</value>
  </data>
  <data name="cell_P3.Weight" type="System.Double, mscorlib">
    <value>0.*****************</value>
  </data>
  <data name="cell_header_P2.Weight" type="System.Double, mscorlib">
    <value>0.*****************</value>
  </data>
  <data name="xrTableCell3.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.Language" type="System.Globalization.CultureInfo, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>ar-EG</value>
  </metadata>
</root>