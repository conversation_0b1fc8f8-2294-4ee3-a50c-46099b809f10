﻿namespace Pharmacy.Forms
{
    partial class frm_E_InvoiceList_Rejected
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_E_InvoiceList_Rejected));
            DevExpress.XtraGrid.GridLevelNode gridLevelNode1 = new DevExpress.XtraGrid.GridLevelNode();
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtn_Help = new DevExpress.XtraBars.BarButtonItem();
            this.barMnu_Print = new DevExpress.XtraBars.BarSubItem();
            this.barBtn_Print1 = new DevExpress.XtraBars.BarButtonItem();
            this.barBtn_PrintData = new DevExpress.XtraBars.BarButtonItem();
            this.btn_UpdateInvoices = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnRefresh = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnOpen = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnNew = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.grdInvoices = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.mi_OpenDealer = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_SL_InvoiceId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_InvoiceCode = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_InvoiceBookId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_InvoiceBook = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.col_StoreId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CustomerId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_InvoiceDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Notes = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_PayMethod = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_paymethod = new DevExpress.XtraEditors.Repository.RepositoryItemImageComboBox();
            this.col_DiscountRatio = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_DiscountValue = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Expenses = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colPaid = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colRemains = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_UserId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_User = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.colStore = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_SalesEmpId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_salesEmp = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.col_CustId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Net = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_TotalCostPrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Profit = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ProfitRatio = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ProfitCostRatio = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CategoryId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_CategoryId = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.col_Is_OutTrans = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_TaxValue = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_DeductTaxValue = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_AddTaxValue = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_DueDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CrncId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repCrncy = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.col_CrncRate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDriverName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colVehicleNumber = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDestination = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Process = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_SourceCode = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_IsOffer = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Paid_To = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_Accounts = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.col_CustomerGroup = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Total = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_IdRegion = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_City = new DevExpress.XtraGrid.Columns.GridColumn();
            this.chk_Sync = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
            this.btnClearSearch = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.dt2 = new DevExpress.XtraEditors.DateEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.dt1 = new DevExpress.XtraEditors.DateEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.btn_Advanced = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdInvoices)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_InvoiceBook)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_paymethod)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_User)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_salesEmp)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_CategoryId)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repCrncy)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Accounts)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_Sync)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dt2.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dt2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dt1.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dt1.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnNew,
            this.barBtnOpen,
            this.barBtn_Help,
            this.barBtnClose,
            this.barBtnRefresh,
            this.barMnu_Print,
            this.barBtn_Print1,
            this.barBtn_PrintData,
            this.btn_UpdateInvoices});
            this.barManager1.MaxItemId = 34;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(567, 147);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.None, false, this.barBtn_Help, false),
            new DevExpress.XtraBars.LinkPersistInfo(this.barMnu_Print),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.btn_UpdateInvoices, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnRefresh),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnOpen),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnNew),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtn_Help
            // 
            resources.ApplyResources(this.barBtn_Help, "barBtn_Help");
            this.barBtn_Help.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtn_Help.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtn_Help.Id = 2;
            this.barBtn_Help.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtn_Help.Name = "barBtn_Help";
            this.barBtn_Help.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtn_Help.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Help_ItemClick);
            // 
            // barMnu_Print
            // 
            resources.ApplyResources(this.barMnu_Print, "barMnu_Print");
            this.barMnu_Print.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barMnu_Print.Glyph = global::Pharmacy.Properties.Resources.prnt;
            this.barMnu_Print.Id = 29;
            this.barMnu_Print.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_Print1),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_PrintData)});
            this.barMnu_Print.MenuAppearance.HeaderItemAppearance.FontSizeDelta = ((int)(resources.GetObject("barMnu_Print.MenuAppearance.HeaderItemAppearance.FontSizeDelta")));
            this.barMnu_Print.MenuAppearance.HeaderItemAppearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barMnu_Print.MenuAppearance.HeaderItemAppearance.FontStyleDelta")));
            this.barMnu_Print.MenuAppearance.HeaderItemAppearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barMnu_Print.MenuAppearance.HeaderItemAppearance.GradientMode")));
            this.barMnu_Print.MenuAppearance.HeaderItemAppearance.Image = ((System.Drawing.Image)(resources.GetObject("barMnu_Print.MenuAppearance.HeaderItemAppearance.Image")));
            this.barMnu_Print.Name = "barMnu_Print";
            this.barMnu_Print.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            // 
            // barBtn_Print1
            // 
            resources.ApplyResources(this.barBtn_Print1, "barBtn_Print1");
            this.barBtn_Print1.Id = 30;
            this.barBtn_Print1.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.P));
            this.barBtn_Print1.Name = "barBtn_Print1";
            this.barBtn_Print1.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Print1_ItemClick);
            // 
            // barBtn_PrintData
            // 
            resources.ApplyResources(this.barBtn_PrintData, "barBtn_PrintData");
            this.barBtn_PrintData.Id = 31;
            this.barBtn_PrintData.Name = "barBtn_PrintData";
            this.barBtn_PrintData.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_PrintData_ItemClick);
            // 
            // btn_UpdateInvoices
            // 
            resources.ApplyResources(this.btn_UpdateInvoices, "btn_UpdateInvoices");
            this.btn_UpdateInvoices.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.btn_UpdateInvoices.Glyph = global::Pharmacy.Properties.Resources._16_transfer;
            this.btn_UpdateInvoices.Id = 33;
            this.btn_UpdateInvoices.Name = "btn_UpdateInvoices";
            this.btn_UpdateInvoices.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btn_UpdateInvoices_ItemClick);
            // 
            // barBtnRefresh
            // 
            resources.ApplyResources(this.barBtnRefresh, "barBtnRefresh");
            this.barBtnRefresh.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnRefresh.Glyph = global::Pharmacy.Properties.Resources.refresh;
            this.barBtnRefresh.Id = 26;
            this.barBtnRefresh.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.R));
            this.barBtnRefresh.Name = "barBtnRefresh";
            this.barBtnRefresh.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnRefresh.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Refresh_ItemClick);
            // 
            // barBtnOpen
            // 
            resources.ApplyResources(this.barBtnOpen, "barBtnOpen");
            this.barBtnOpen.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnOpen.Glyph = global::Pharmacy.Properties.Resources.open;
            this.barBtnOpen.Id = 1;
            this.barBtnOpen.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.O));
            this.barBtnOpen.Name = "barBtnOpen";
            this.barBtnOpen.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnOpen.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Open_ItemClick);
            // 
            // barBtnNew
            // 
            resources.ApplyResources(this.barBtnNew, "barBtnNew");
            this.barBtnNew.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnNew.Glyph = global::Pharmacy.Properties.Resources._new;
            this.barBtnNew.Id = 0;
            this.barBtnNew.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.N));
            this.barBtnNew.Name = "barBtnNew";
            this.barBtnNew.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnNew.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_New_ItemClick);
            // 
            // barBtnClose
            // 
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 25;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Close_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlTop.Appearance.FontSizeDelta")));
            this.barDockControlTop.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlTop.Appearance.FontStyleDelta")));
            this.barDockControlTop.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlTop.Appearance.GradientMode")));
            this.barDockControlTop.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlTop.Appearance.Image")));
            this.barDockControlTop.CausesValidation = false;
            // 
            // barDockControlBottom
            // 
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlBottom.Appearance.FontSizeDelta")));
            this.barDockControlBottom.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlBottom.Appearance.FontStyleDelta")));
            this.barDockControlBottom.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlBottom.Appearance.GradientMode")));
            this.barDockControlBottom.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlBottom.Appearance.Image")));
            this.barDockControlBottom.CausesValidation = false;
            // 
            // barDockControlLeft
            // 
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlLeft.Appearance.FontSizeDelta")));
            this.barDockControlLeft.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlLeft.Appearance.FontStyleDelta")));
            this.barDockControlLeft.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlLeft.Appearance.GradientMode")));
            this.barDockControlLeft.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlLeft.Appearance.Image")));
            this.barDockControlLeft.CausesValidation = false;
            // 
            // barDockControlRight
            // 
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlRight.Appearance.FontSizeDelta")));
            this.barDockControlRight.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlRight.Appearance.FontStyleDelta")));
            this.barDockControlRight.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlRight.Appearance.GradientMode")));
            this.barDockControlRight.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlRight.Appearance.Image")));
            this.barDockControlRight.CausesValidation = false;
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemTextEdit1.Mask.AutoComplete")));
            this.repositoryItemTextEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.BeepOnError")));
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemTextEdit1.Mask.MaskType")));
            this.repositoryItemTextEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemTextEdit1.Mask.PlaceHolder")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // grdInvoices
            // 
            resources.ApplyResources(this.grdInvoices, "grdInvoices");
            this.grdInvoices.ContextMenuStrip = this.contextMenuStrip1;
            this.grdInvoices.Cursor = System.Windows.Forms.Cursors.Default;
            this.grdInvoices.EmbeddedNavigator.AccessibleDescription = resources.GetString("grdInvoices.EmbeddedNavigator.AccessibleDescription");
            this.grdInvoices.EmbeddedNavigator.AccessibleName = resources.GetString("grdInvoices.EmbeddedNavigator.AccessibleName");
            this.grdInvoices.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grdInvoices.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grdInvoices.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grdInvoices.EmbeddedNavigator.Anchor")));
            this.grdInvoices.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grdInvoices.EmbeddedNavigator.BackgroundImage")));
            this.grdInvoices.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grdInvoices.EmbeddedNavigator.BackgroundImageLayout")));
            this.grdInvoices.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grdInvoices.EmbeddedNavigator.ImeMode")));
            this.grdInvoices.EmbeddedNavigator.Margin = ((System.Windows.Forms.Padding)(resources.GetObject("grdInvoices.EmbeddedNavigator.Margin")));
            this.grdInvoices.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grdInvoices.EmbeddedNavigator.MaximumSize")));
            this.grdInvoices.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grdInvoices.EmbeddedNavigator.TextLocation")));
            this.grdInvoices.EmbeddedNavigator.ToolTip = resources.GetString("grdInvoices.EmbeddedNavigator.ToolTip");
            this.grdInvoices.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grdInvoices.EmbeddedNavigator.ToolTipIconType")));
            this.grdInvoices.EmbeddedNavigator.ToolTipTitle = resources.GetString("grdInvoices.EmbeddedNavigator.ToolTipTitle");
            gridLevelNode1.RelationName = "Level1";
            this.grdInvoices.LevelTree.Nodes.AddRange(new DevExpress.XtraGrid.GridLevelNode[] {
            gridLevelNode1});
            this.grdInvoices.MainView = this.gridView1;
            this.grdInvoices.Name = "grdInvoices";
            this.grdInvoices.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.rep_paymethod,
            this.rep_salesEmp,
            this.rep_CategoryId,
            this.rep_InvoiceBook,
            this.repCrncy,
            this.rep_User,
            this.rep_Accounts,
            this.chk_Sync});
            this.grdInvoices.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            this.grdInvoices.DoubleClick += new System.EventHandler(this.grdCategory_DoubleClick);
            // 
            // contextMenuStrip1
            // 
            resources.ApplyResources(this.contextMenuStrip1, "contextMenuStrip1");
            this.contextMenuStrip1.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.mi_OpenDealer});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            // 
            // mi_OpenDealer
            // 
            resources.ApplyResources(this.mi_OpenDealer, "mi_OpenDealer");
            this.mi_OpenDealer.Name = "mi_OpenDealer";
            this.mi_OpenDealer.Click += new System.EventHandler(this.mi_OpenDealer_Click);
            // 
            // gridView1
            // 
            this.gridView1.Appearance.FooterPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.FooterPanel.FontSizeDelta")));
            this.gridView1.Appearance.FooterPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.FooterPanel.FontStyleDelta")));
            this.gridView1.Appearance.FooterPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.FooterPanel.GradientMode")));
            this.gridView1.Appearance.FooterPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.FooterPanel.Image")));
            this.gridView1.Appearance.FooterPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.GroupPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.GroupPanel.FontSizeDelta")));
            this.gridView1.Appearance.GroupPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.GroupPanel.FontStyleDelta")));
            this.gridView1.Appearance.GroupPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.GroupPanel.GradientMode")));
            this.gridView1.Appearance.GroupPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.GroupPanel.Image")));
            this.gridView1.Appearance.GroupPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.GroupPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.GroupRow.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.GroupRow.FontSizeDelta")));
            this.gridView1.Appearance.GroupRow.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.GroupRow.FontStyleDelta")));
            this.gridView1.Appearance.GroupRow.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.GroupRow.GradientMode")));
            this.gridView1.Appearance.GroupRow.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.GroupRow.Image")));
            this.gridView1.Appearance.GroupRow.Options.UseTextOptions = true;
            this.gridView1.Appearance.GroupRow.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.HeaderPanel.FontSizeDelta")));
            this.gridView1.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.HeaderPanel.FontStyleDelta")));
            this.gridView1.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.HeaderPanel.GradientMode")));
            this.gridView1.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.HeaderPanel.Image")));
            this.gridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.Row.FontSizeDelta")));
            this.gridView1.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.Row.FontStyleDelta")));
            this.gridView1.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.Row.GradientMode")));
            this.gridView1.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.Row.Image")));
            this.gridView1.Appearance.Row.Options.UseTextOptions = true;
            this.gridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.AppearancePrint.FooterPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.BorderColor")));
            this.gridView1.AppearancePrint.FooterPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.FontSizeDelta")));
            this.gridView1.AppearancePrint.FooterPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.FontStyleDelta")));
            this.gridView1.AppearancePrint.FooterPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.ForeColor")));
            this.gridView1.AppearancePrint.FooterPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.GradientMode")));
            this.gridView1.AppearancePrint.FooterPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.Image")));
            this.gridView1.AppearancePrint.FooterPanel.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.FooterPanel.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.FooterPanel.Options.UseTextOptions = true;
            this.gridView1.AppearancePrint.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.AppearancePrint.GroupFooter.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.BorderColor")));
            this.gridView1.AppearancePrint.GroupFooter.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.FontSizeDelta")));
            this.gridView1.AppearancePrint.GroupFooter.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.FontStyleDelta")));
            this.gridView1.AppearancePrint.GroupFooter.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.ForeColor")));
            this.gridView1.AppearancePrint.GroupFooter.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.GradientMode")));
            this.gridView1.AppearancePrint.GroupFooter.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.Image")));
            this.gridView1.AppearancePrint.GroupFooter.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.GroupFooter.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.GroupFooter.Options.UseTextOptions = true;
            this.gridView1.AppearancePrint.GroupFooter.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.AppearancePrint.GroupRow.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupRow.BorderColor")));
            this.gridView1.AppearancePrint.GroupRow.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.GroupRow.FontSizeDelta")));
            this.gridView1.AppearancePrint.GroupRow.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.GroupRow.FontStyleDelta")));
            this.gridView1.AppearancePrint.GroupRow.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupRow.ForeColor")));
            this.gridView1.AppearancePrint.GroupRow.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.GroupRow.GradientMode")));
            this.gridView1.AppearancePrint.GroupRow.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.GroupRow.Image")));
            this.gridView1.AppearancePrint.GroupRow.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.GroupRow.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.GroupRow.Options.UseTextOptions = true;
            this.gridView1.AppearancePrint.GroupRow.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.AppearancePrint.HeaderPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.BorderColor")));
            this.gridView1.AppearancePrint.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.FontSizeDelta")));
            this.gridView1.AppearancePrint.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.FontStyleDelta")));
            this.gridView1.AppearancePrint.HeaderPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.ForeColor")));
            this.gridView1.AppearancePrint.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.GradientMode")));
            this.gridView1.AppearancePrint.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.Image")));
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.AppearancePrint.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.AppearancePrint.Lines.BackColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Lines.BackColor")));
            this.gridView1.AppearancePrint.Lines.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.Lines.FontSizeDelta")));
            this.gridView1.AppearancePrint.Lines.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.Lines.FontStyleDelta")));
            this.gridView1.AppearancePrint.Lines.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Lines.ForeColor")));
            this.gridView1.AppearancePrint.Lines.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.Lines.GradientMode")));
            this.gridView1.AppearancePrint.Lines.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.Lines.Image")));
            this.gridView1.AppearancePrint.Lines.Options.UseBackColor = true;
            this.gridView1.AppearancePrint.Lines.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.Row.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Row.BorderColor")));
            this.gridView1.AppearancePrint.Row.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.Row.FontSizeDelta")));
            this.gridView1.AppearancePrint.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.Row.FontStyleDelta")));
            this.gridView1.AppearancePrint.Row.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Row.ForeColor")));
            this.gridView1.AppearancePrint.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.Row.GradientMode")));
            this.gridView1.AppearancePrint.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.Row.Image")));
            this.gridView1.AppearancePrint.Row.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.Row.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.Row.Options.UseTextOptions = true;
            this.gridView1.AppearancePrint.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.gridView1, "gridView1");
            this.gridView1.ColumnPanelRowHeight = 35;
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_SL_InvoiceId,
            this.col_InvoiceCode,
            this.col_InvoiceBookId,
            this.col_StoreId,
            this.col_CustomerId,
            this.col_InvoiceDate,
            this.col_Notes,
            this.col_PayMethod,
            this.col_DiscountRatio,
            this.col_DiscountValue,
            this.col_Expenses,
            this.colPaid,
            this.colRemains,
            this.col_UserId,
            this.colStore,
            this.col_SalesEmpId,
            this.col_CustId,
            this.col_Net,
            this.col_TotalCostPrice,
            this.col_Profit,
            this.col_ProfitRatio,
            this.col_ProfitCostRatio,
            this.col_CategoryId,
            this.col_Is_OutTrans,
            this.col_TaxValue,
            this.col_DeductTaxValue,
            this.col_AddTaxValue,
            this.col_DueDate,
            this.col_CrncId,
            this.col_CrncRate,
            this.colDriverName,
            this.colVehicleNumber,
            this.colDestination,
            this.col_Process,
            this.col_SourceCode,
            this.col_IsOffer,
            this.col_Paid_To,
            this.col_CustomerGroup,
            this.col_Total,
            this.col_IdRegion,
            this.col_City});
            this.gridView1.CustomizationFormBounds = new System.Drawing.Rectangle(959, 364, 210, 277);
            this.gridView1.GridControl = this.grdInvoices;
            this.gridView1.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary"))), resources.GetString("gridView1.GroupSummary1"), this.col_DiscountValue, resources.GetString("gridView1.GroupSummary2")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary3"))), resources.GetString("gridView1.GroupSummary4"), this.col_Expenses, resources.GetString("gridView1.GroupSummary5")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary6"))), resources.GetString("gridView1.GroupSummary7"), this.col_Net, resources.GetString("gridView1.GroupSummary8")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary9"))), resources.GetString("gridView1.GroupSummary10"), this.col_TaxValue, resources.GetString("gridView1.GroupSummary11")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary12"))), resources.GetString("gridView1.GroupSummary13"), this.col_DeductTaxValue, resources.GetString("gridView1.GroupSummary14")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary15"))), resources.GetString("gridView1.GroupSummary16"), this.col_AddTaxValue, resources.GetString("gridView1.GroupSummary17")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary18"))), resources.GetString("gridView1.GroupSummary19"), this.col_TotalCostPrice, resources.GetString("gridView1.GroupSummary20")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary21"))), resources.GetString("gridView1.GroupSummary22"), this.col_ProfitRatio, resources.GetString("gridView1.GroupSummary23")),
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary24"))), resources.GetString("gridView1.GroupSummary25"), this.col_ProfitCostRatio, resources.GetString("gridView1.GroupSummary26"))});
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView1.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsSelection.CheckBoxSelectorColumnWidth = 50;
            this.gridView1.OptionsSelection.MultiSelect = true;
            this.gridView1.OptionsSelection.MultiSelectMode = DevExpress.XtraGrid.Views.Grid.GridMultiSelectMode.CheckBoxRowSelect;
            this.gridView1.OptionsSelection.ShowCheckBoxSelectorInColumnHeader = DevExpress.Utils.DefaultBoolean.True;
            this.gridView1.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView1.OptionsView.GroupFooterShowMode = DevExpress.XtraGrid.Views.Grid.GroupFooterShowMode.VisibleAlways;
            this.gridView1.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Bottom;
            this.gridView1.OptionsView.ShowAutoFilterRow = true;
            this.gridView1.OptionsView.ShowFooter = true;
            this.gridView1.OptionsView.ShowIndicator = false;
            // 
            // col_SL_InvoiceId
            // 
            resources.ApplyResources(this.col_SL_InvoiceId, "col_SL_InvoiceId");
            this.col_SL_InvoiceId.FieldName = "SL_InvoiceId";
            this.col_SL_InvoiceId.Name = "col_SL_InvoiceId";
            // 
            // col_InvoiceCode
            // 
            resources.ApplyResources(this.col_InvoiceCode, "col_InvoiceCode");
            this.col_InvoiceCode.FieldName = "InvoiceCode";
            this.col_InvoiceCode.Name = "col_InvoiceCode";
            this.col_InvoiceCode.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // col_InvoiceBookId
            // 
            resources.ApplyResources(this.col_InvoiceBookId, "col_InvoiceBookId");
            this.col_InvoiceBookId.ColumnEdit = this.rep_InvoiceBook;
            this.col_InvoiceBookId.FieldName = "InvoiceBookId";
            this.col_InvoiceBookId.Name = "col_InvoiceBookId";
            // 
            // rep_InvoiceBook
            // 
            resources.ApplyResources(this.rep_InvoiceBook, "rep_InvoiceBook");
            this.rep_InvoiceBook.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_InvoiceBook.Buttons"))))});
            this.rep_InvoiceBook.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_InvoiceBook.Columns"), resources.GetString("rep_InvoiceBook.Columns1"), ((int)(resources.GetObject("rep_InvoiceBook.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("rep_InvoiceBook.Columns3"))), resources.GetString("rep_InvoiceBook.Columns4"), ((bool)(resources.GetObject("rep_InvoiceBook.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("rep_InvoiceBook.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_InvoiceBook.Columns7"), resources.GetString("rep_InvoiceBook.Columns8"))});
            this.rep_InvoiceBook.Name = "rep_InvoiceBook";
            // 
            // col_StoreId
            // 
            resources.ApplyResources(this.col_StoreId, "col_StoreId");
            this.col_StoreId.FieldName = "StoreId";
            this.col_StoreId.Name = "col_StoreId";
            // 
            // col_CustomerId
            // 
            resources.ApplyResources(this.col_CustomerId, "col_CustomerId");
            this.col_CustomerId.FieldName = "CustomerId";
            this.col_CustomerId.Name = "col_CustomerId";
            this.col_CustomerId.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // col_InvoiceDate
            // 
            resources.ApplyResources(this.col_InvoiceDate, "col_InvoiceDate");
            this.col_InvoiceDate.FieldName = "InvoiceDate";
            this.col_InvoiceDate.Name = "col_InvoiceDate";
            this.col_InvoiceDate.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // col_Notes
            // 
            resources.ApplyResources(this.col_Notes, "col_Notes");
            this.col_Notes.FieldName = "Notes";
            this.col_Notes.Name = "col_Notes";
            // 
            // col_PayMethod
            // 
            resources.ApplyResources(this.col_PayMethod, "col_PayMethod");
            this.col_PayMethod.ColumnEdit = this.rep_paymethod;
            this.col_PayMethod.FieldName = "PayMethod";
            this.col_PayMethod.Name = "col_PayMethod";
            // 
            // rep_paymethod
            // 
            resources.ApplyResources(this.rep_paymethod, "rep_paymethod");
            this.rep_paymethod.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_paymethod.Buttons"))))});
            this.rep_paymethod.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("rep_paymethod.Items"), ((object)(resources.GetObject("rep_paymethod.Items1"))), ((int)(resources.GetObject("rep_paymethod.Items2")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("rep_paymethod.Items3"), ((object)(resources.GetObject("rep_paymethod.Items4"))), ((int)(resources.GetObject("rep_paymethod.Items5")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("rep_paymethod.Items6"), ((object)(resources.GetObject("rep_paymethod.Items7"))), ((int)(resources.GetObject("rep_paymethod.Items8"))))});
            this.rep_paymethod.Name = "rep_paymethod";
            // 
            // col_DiscountRatio
            // 
            resources.ApplyResources(this.col_DiscountRatio, "col_DiscountRatio");
            this.col_DiscountRatio.DisplayFormat.FormatString = "n2";
            this.col_DiscountRatio.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_DiscountRatio.FieldName = "DiscountRatio";
            this.col_DiscountRatio.GroupFormat.FormatString = "n2";
            this.col_DiscountRatio.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_DiscountRatio.Name = "col_DiscountRatio";
            // 
            // col_DiscountValue
            // 
            resources.ApplyResources(this.col_DiscountValue, "col_DiscountValue");
            this.col_DiscountValue.DisplayFormat.FormatString = "n2";
            this.col_DiscountValue.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_DiscountValue.FieldName = "DiscountValue";
            this.col_DiscountValue.GroupFormat.FormatString = "n2";
            this.col_DiscountValue.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_DiscountValue.Name = "col_DiscountValue";
            this.col_DiscountValue.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_DiscountValue.Summary"))), resources.GetString("col_DiscountValue.Summary1"), resources.GetString("col_DiscountValue.Summary2"))});
            // 
            // col_Expenses
            // 
            resources.ApplyResources(this.col_Expenses, "col_Expenses");
            this.col_Expenses.DisplayFormat.FormatString = "n2";
            this.col_Expenses.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_Expenses.FieldName = "Expenses";
            this.col_Expenses.GroupFormat.FormatString = "n2";
            this.col_Expenses.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_Expenses.Name = "col_Expenses";
            this.col_Expenses.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_Expenses.Summary"))), resources.GetString("col_Expenses.Summary1"), resources.GetString("col_Expenses.Summary2"))});
            // 
            // colPaid
            // 
            resources.ApplyResources(this.colPaid, "colPaid");
            this.colPaid.DisplayFormat.FormatString = "n2";
            this.colPaid.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colPaid.FieldName = "Paid";
            this.colPaid.GroupFormat.FormatString = "n2";
            this.colPaid.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colPaid.Name = "colPaid";
            this.colPaid.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("colPaid.Summary"))), resources.GetString("colPaid.Summary1"), resources.GetString("colPaid.Summary2"))});
            // 
            // colRemains
            // 
            resources.ApplyResources(this.colRemains, "colRemains");
            this.colRemains.DisplayFormat.FormatString = "n2";
            this.colRemains.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colRemains.FieldName = "Remains";
            this.colRemains.GroupFormat.FormatString = "n2";
            this.colRemains.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.colRemains.Name = "colRemains";
            this.colRemains.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("colRemains.Summary"))), resources.GetString("colRemains.Summary1"), resources.GetString("colRemains.Summary2"))});
            // 
            // col_UserId
            // 
            resources.ApplyResources(this.col_UserId, "col_UserId");
            this.col_UserId.ColumnEdit = this.rep_User;
            this.col_UserId.FieldName = "UserId";
            this.col_UserId.Name = "col_UserId";
            // 
            // rep_User
            // 
            resources.ApplyResources(this.rep_User, "rep_User");
            this.rep_User.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_User.Buttons"))))});
            this.rep_User.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_User.Columns"), resources.GetString("rep_User.Columns1"), ((int)(resources.GetObject("rep_User.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("rep_User.Columns3"))), resources.GetString("rep_User.Columns4"), ((bool)(resources.GetObject("rep_User.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("rep_User.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_User.Columns7"), resources.GetString("rep_User.Columns8"))});
            this.rep_User.DisplayMember = "UserName";
            this.rep_User.Name = "rep_User";
            this.rep_User.ValueMember = "UserId";
            // 
            // colStore
            // 
            resources.ApplyResources(this.colStore, "colStore");
            this.colStore.FieldName = "store";
            this.colStore.Name = "colStore";
            // 
            // col_SalesEmpId
            // 
            resources.ApplyResources(this.col_SalesEmpId, "col_SalesEmpId");
            this.col_SalesEmpId.ColumnEdit = this.rep_salesEmp;
            this.col_SalesEmpId.FieldName = "SalesEmpId";
            this.col_SalesEmpId.Name = "col_SalesEmpId";
            // 
            // rep_salesEmp
            // 
            resources.ApplyResources(this.rep_salesEmp, "rep_salesEmp");
            this.rep_salesEmp.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_salesEmp.Buttons"))))});
            this.rep_salesEmp.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_salesEmp.Columns"), resources.GetString("rep_salesEmp.Columns1")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_salesEmp.Columns2"), resources.GetString("rep_salesEmp.Columns3"), ((int)(resources.GetObject("rep_salesEmp.Columns4"))), ((DevExpress.Utils.FormatType)(resources.GetObject("rep_salesEmp.Columns5"))), resources.GetString("rep_salesEmp.Columns6"), ((bool)(resources.GetObject("rep_salesEmp.Columns7"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("rep_salesEmp.Columns8"))))});
            this.rep_salesEmp.DisplayMember = "EmpName";
            this.rep_salesEmp.Name = "rep_salesEmp";
            this.rep_salesEmp.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.rep_salesEmp.ValueMember = "EmpId";
            // 
            // col_CustId
            // 
            resources.ApplyResources(this.col_CustId, "col_CustId");
            this.col_CustId.FieldName = "CustId";
            this.col_CustId.Name = "col_CustId";
            this.col_CustId.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // col_Net
            // 
            resources.ApplyResources(this.col_Net, "col_Net");
            this.col_Net.DisplayFormat.FormatString = "n2";
            this.col_Net.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_Net.FieldName = "Net";
            this.col_Net.GroupFormat.FormatString = "n2";
            this.col_Net.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_Net.Name = "col_Net";
            this.col_Net.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_Net.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_Net.Summary"))), resources.GetString("col_Net.Summary1"), resources.GetString("col_Net.Summary2"))});
            // 
            // col_TotalCostPrice
            // 
            resources.ApplyResources(this.col_TotalCostPrice, "col_TotalCostPrice");
            this.col_TotalCostPrice.DisplayFormat.FormatString = "n2";
            this.col_TotalCostPrice.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_TotalCostPrice.FieldName = "TotalCostPrice";
            this.col_TotalCostPrice.GroupFormat.FormatString = "n2";
            this.col_TotalCostPrice.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_TotalCostPrice.Name = "col_TotalCostPrice";
            this.col_TotalCostPrice.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_TotalCostPrice.Summary"))), resources.GetString("col_TotalCostPrice.Summary1"), resources.GetString("col_TotalCostPrice.Summary2"))});
            // 
            // col_Profit
            // 
            resources.ApplyResources(this.col_Profit, "col_Profit");
            this.col_Profit.DisplayFormat.FormatString = "n2";
            this.col_Profit.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_Profit.FieldName = "Profit";
            this.col_Profit.GroupFormat.FormatString = "n2";
            this.col_Profit.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_Profit.Name = "col_Profit";
            this.col_Profit.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_Profit.Summary"))), resources.GetString("col_Profit.Summary1"), resources.GetString("col_Profit.Summary2"))});
            // 
            // col_ProfitRatio
            // 
            resources.ApplyResources(this.col_ProfitRatio, "col_ProfitRatio");
            this.col_ProfitRatio.DisplayFormat.FormatString = "p2";
            this.col_ProfitRatio.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_ProfitRatio.FieldName = "profitRatio";
            this.col_ProfitRatio.Name = "col_ProfitRatio";
            this.col_ProfitRatio.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_ProfitRatio.Summary"))), resources.GetString("col_ProfitRatio.Summary1"), resources.GetString("col_ProfitRatio.Summary2"))});
            // 
            // col_ProfitCostRatio
            // 
            resources.ApplyResources(this.col_ProfitCostRatio, "col_ProfitCostRatio");
            this.col_ProfitCostRatio.DisplayFormat.FormatString = "p2";
            this.col_ProfitCostRatio.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_ProfitCostRatio.FieldName = "ProfitCostRatio";
            this.col_ProfitCostRatio.Name = "col_ProfitCostRatio";
            this.col_ProfitCostRatio.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_ProfitCostRatio.Summary"))), resources.GetString("col_ProfitCostRatio.Summary1"), resources.GetString("col_ProfitCostRatio.Summary2"))});
            // 
            // col_CategoryId
            // 
            resources.ApplyResources(this.col_CategoryId, "col_CategoryId");
            this.col_CategoryId.ColumnEdit = this.rep_CategoryId;
            this.col_CategoryId.FieldName = "CategoryId";
            this.col_CategoryId.Name = "col_CategoryId";
            // 
            // rep_CategoryId
            // 
            resources.ApplyResources(this.rep_CategoryId, "rep_CategoryId");
            this.rep_CategoryId.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_CategoryId.Buttons"))))});
            this.rep_CategoryId.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_CategoryId.Columns"), resources.GetString("rep_CategoryId.Columns1"), ((int)(resources.GetObject("rep_CategoryId.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("rep_CategoryId.Columns3"))), resources.GetString("rep_CategoryId.Columns4"), ((bool)(resources.GetObject("rep_CategoryId.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("rep_CategoryId.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_CategoryId.Columns7"), resources.GetString("rep_CategoryId.Columns8"))});
            this.rep_CategoryId.Name = "rep_CategoryId";
            this.rep_CategoryId.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            // 
            // col_Is_OutTrans
            // 
            resources.ApplyResources(this.col_Is_OutTrans, "col_Is_OutTrans");
            this.col_Is_OutTrans.FieldName = "Is_OutTrans";
            this.col_Is_OutTrans.Name = "col_Is_OutTrans";
            // 
            // col_TaxValue
            // 
            resources.ApplyResources(this.col_TaxValue, "col_TaxValue");
            this.col_TaxValue.DisplayFormat.FormatString = "n2";
            this.col_TaxValue.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_TaxValue.FieldName = "TaxValue";
            this.col_TaxValue.GroupFormat.FormatString = "n2";
            this.col_TaxValue.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_TaxValue.Name = "col_TaxValue";
            this.col_TaxValue.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_TaxValue.Summary"))), resources.GetString("col_TaxValue.Summary1"), resources.GetString("col_TaxValue.Summary2"))});
            // 
            // col_DeductTaxValue
            // 
            resources.ApplyResources(this.col_DeductTaxValue, "col_DeductTaxValue");
            this.col_DeductTaxValue.DisplayFormat.FormatString = "n2";
            this.col_DeductTaxValue.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_DeductTaxValue.FieldName = "DeductTaxValue";
            this.col_DeductTaxValue.GroupFormat.FormatString = "n2";
            this.col_DeductTaxValue.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_DeductTaxValue.Name = "col_DeductTaxValue";
            this.col_DeductTaxValue.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_DeductTaxValue.Summary"))), resources.GetString("col_DeductTaxValue.Summary1"), resources.GetString("col_DeductTaxValue.Summary2"))});
            // 
            // col_AddTaxValue
            // 
            resources.ApplyResources(this.col_AddTaxValue, "col_AddTaxValue");
            this.col_AddTaxValue.DisplayFormat.FormatString = "n2";
            this.col_AddTaxValue.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_AddTaxValue.FieldName = "AddTaxValue";
            this.col_AddTaxValue.GroupFormat.FormatString = "n2";
            this.col_AddTaxValue.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_AddTaxValue.Name = "col_AddTaxValue";
            this.col_AddTaxValue.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_AddTaxValue.Summary"))), resources.GetString("col_AddTaxValue.Summary1"), resources.GetString("col_AddTaxValue.Summary2"))});
            // 
            // col_DueDate
            // 
            resources.ApplyResources(this.col_DueDate, "col_DueDate");
            this.col_DueDate.DisplayFormat.FormatString = "d";
            this.col_DueDate.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.col_DueDate.FieldName = "DueDate";
            this.col_DueDate.Name = "col_DueDate";
            // 
            // col_CrncId
            // 
            resources.ApplyResources(this.col_CrncId, "col_CrncId");
            this.col_CrncId.ColumnEdit = this.repCrncy;
            this.col_CrncId.FieldName = "CrncId";
            this.col_CrncId.Name = "col_CrncId";
            // 
            // repCrncy
            // 
            resources.ApplyResources(this.repCrncy, "repCrncy");
            this.repCrncy.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repCrncy.Buttons"))))});
            this.repCrncy.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("repCrncy.Columns"), resources.GetString("repCrncy.Columns1"), ((int)(resources.GetObject("repCrncy.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("repCrncy.Columns3"))), resources.GetString("repCrncy.Columns4"), ((bool)(resources.GetObject("repCrncy.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("repCrncy.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("repCrncy.Columns7"), resources.GetString("repCrncy.Columns8"))});
            this.repCrncy.Name = "repCrncy";
            // 
            // col_CrncRate
            // 
            resources.ApplyResources(this.col_CrncRate, "col_CrncRate");
            this.col_CrncRate.FieldName = "CrncRate";
            this.col_CrncRate.Name = "col_CrncRate";
            // 
            // colDriverName
            // 
            resources.ApplyResources(this.colDriverName, "colDriverName");
            this.colDriverName.FieldName = "DriverName";
            this.colDriverName.Name = "colDriverName";
            this.colDriverName.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // colVehicleNumber
            // 
            resources.ApplyResources(this.colVehicleNumber, "colVehicleNumber");
            this.colVehicleNumber.FieldName = "VehicleNumber";
            this.colVehicleNumber.Name = "colVehicleNumber";
            this.colVehicleNumber.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // colDestination
            // 
            resources.ApplyResources(this.colDestination, "colDestination");
            this.colDestination.FieldName = "Destination";
            this.colDestination.Name = "colDestination";
            this.colDestination.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // col_Process
            // 
            resources.ApplyResources(this.col_Process, "col_Process");
            this.col_Process.FieldName = "Process";
            this.col_Process.Name = "col_Process";
            this.col_Process.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // col_SourceCode
            // 
            resources.ApplyResources(this.col_SourceCode, "col_SourceCode");
            this.col_SourceCode.FieldName = "SourceCode";
            this.col_SourceCode.Name = "col_SourceCode";
            this.col_SourceCode.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // col_IsOffer
            // 
            resources.ApplyResources(this.col_IsOffer, "col_IsOffer");
            this.col_IsOffer.FieldName = "IsOffer";
            this.col_IsOffer.Name = "col_IsOffer";
            // 
            // col_Paid_To
            // 
            resources.ApplyResources(this.col_Paid_To, "col_Paid_To");
            this.col_Paid_To.ColumnEdit = this.rep_Accounts;
            this.col_Paid_To.FieldName = "DrawerAccountId";
            this.col_Paid_To.Name = "col_Paid_To";
            // 
            // rep_Accounts
            // 
            resources.ApplyResources(this.rep_Accounts, "rep_Accounts");
            this.rep_Accounts.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Accounts.Buttons"))))});
            this.rep_Accounts.Name = "rep_Accounts";
            // 
            // col_CustomerGroup
            // 
            resources.ApplyResources(this.col_CustomerGroup, "col_CustomerGroup");
            this.col_CustomerGroup.FieldName = "CustomerGroup";
            this.col_CustomerGroup.Name = "col_CustomerGroup";
            // 
            // col_Total
            // 
            resources.ApplyResources(this.col_Total, "col_Total");
            this.col_Total.FieldName = "Total";
            this.col_Total.Name = "col_Total";
            // 
            // col_IdRegion
            // 
            resources.ApplyResources(this.col_IdRegion, "col_IdRegion");
            this.col_IdRegion.FieldName = "Region";
            this.col_IdRegion.Name = "col_IdRegion";
            // 
            // col_City
            // 
            resources.ApplyResources(this.col_City, "col_City");
            this.col_City.FieldName = "City";
            this.col_City.Name = "col_City";
            // 
            // chk_Sync
            // 
            resources.ApplyResources(this.chk_Sync, "chk_Sync");
            this.chk_Sync.Name = "chk_Sync";
            // 
            // btnClearSearch
            // 
            resources.ApplyResources(this.btnClearSearch, "btnClearSearch");
            this.btnClearSearch.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Flat;
            this.btnClearSearch.Name = "btnClearSearch";
            this.btnClearSearch.TabStop = false;
            this.btnClearSearch.Click += new System.EventHandler(this.btnClearSearch_Click);
            // 
            // labelControl3
            // 
            resources.ApplyResources(this.labelControl3, "labelControl3");
            this.labelControl3.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl3.Appearance.Font")));
            this.labelControl3.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl3.Appearance.FontSizeDelta")));
            this.labelControl3.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl3.Appearance.FontStyleDelta")));
            this.labelControl3.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("labelControl3.Appearance.ForeColor")));
            this.labelControl3.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl3.Appearance.GradientMode")));
            this.labelControl3.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl3.Appearance.Image")));
            this.labelControl3.Name = "labelControl3";
            // 
            // dt2
            // 
            resources.ApplyResources(this.dt2, "dt2");
            this.dt2.EnterMoveNextControl = true;
            this.dt2.Name = "dt2";
            this.dt2.Properties.AccessibleDescription = resources.GetString("dt2.Properties.AccessibleDescription");
            this.dt2.Properties.AccessibleName = resources.GetString("dt2.Properties.AccessibleName");
            this.dt2.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.dt2.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("dt2.Properties.Appearance.FontSizeDelta")));
            this.dt2.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("dt2.Properties.Appearance.FontStyleDelta")));
            this.dt2.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("dt2.Properties.Appearance.GradientMode")));
            this.dt2.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("dt2.Properties.Appearance.Image")));
            this.dt2.Properties.Appearance.Options.UseTextOptions = true;
            this.dt2.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.dt2.Properties.AutoHeight = ((bool)(resources.GetObject("dt2.Properties.AutoHeight")));
            this.dt2.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("dt2.Properties.Buttons"))))});
            this.dt2.Properties.CalendarTimeProperties.AccessibleDescription = resources.GetString("dt2.Properties.CalendarTimeProperties.AccessibleDescription");
            this.dt2.Properties.CalendarTimeProperties.AccessibleName = resources.GetString("dt2.Properties.CalendarTimeProperties.AccessibleName");
            this.dt2.Properties.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("dt2.Properties.CalendarTimeProperties.AutoHeight")));
            this.dt2.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dt2.Properties.CalendarTimeProperties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dt2.Properties.CalendarTimeProperties.Mask.AutoComplete")));
            this.dt2.Properties.CalendarTimeProperties.Mask.BeepOnError = ((bool)(resources.GetObject("dt2.Properties.CalendarTimeProperties.Mask.BeepOnError")));
            this.dt2.Properties.CalendarTimeProperties.Mask.EditMask = resources.GetString("dt2.Properties.CalendarTimeProperties.Mask.EditMask");
            this.dt2.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dt2.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.dt2.Properties.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dt2.Properties.CalendarTimeProperties.Mask.MaskType")));
            this.dt2.Properties.CalendarTimeProperties.Mask.PlaceHolder = ((char)(resources.GetObject("dt2.Properties.CalendarTimeProperties.Mask.PlaceHolder")));
            this.dt2.Properties.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("dt2.Properties.CalendarTimeProperties.Mask.SaveLiteral")));
            this.dt2.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dt2.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.dt2.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dt2.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat")));
            this.dt2.Properties.CalendarTimeProperties.NullValuePrompt = resources.GetString("dt2.Properties.CalendarTimeProperties.NullValuePrompt");
            this.dt2.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dt2.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue")));
            this.dt2.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dt2.Properties.Mask.AutoComplete")));
            this.dt2.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("dt2.Properties.Mask.BeepOnError")));
            this.dt2.Properties.Mask.EditMask = resources.GetString("dt2.Properties.Mask.EditMask");
            this.dt2.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dt2.Properties.Mask.IgnoreMaskBlank")));
            this.dt2.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dt2.Properties.Mask.MaskType")));
            this.dt2.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("dt2.Properties.Mask.PlaceHolder")));
            this.dt2.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("dt2.Properties.Mask.SaveLiteral")));
            this.dt2.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dt2.Properties.Mask.ShowPlaceHolders")));
            this.dt2.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dt2.Properties.Mask.UseMaskAsDisplayFormat")));
            this.dt2.Properties.NullValuePrompt = resources.GetString("dt2.Properties.NullValuePrompt");
            this.dt2.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dt2.Properties.NullValuePromptShowForEmptyValue")));
            this.dt2.EditValueChanged += new System.EventHandler(this.dt1_EditValueChanged);
            // 
            // labelControl2
            // 
            resources.ApplyResources(this.labelControl2, "labelControl2");
            this.labelControl2.Name = "labelControl2";
            // 
            // dt1
            // 
            resources.ApplyResources(this.dt1, "dt1");
            this.dt1.EnterMoveNextControl = true;
            this.dt1.Name = "dt1";
            this.dt1.Properties.AccessibleDescription = resources.GetString("dt1.Properties.AccessibleDescription");
            this.dt1.Properties.AccessibleName = resources.GetString("dt1.Properties.AccessibleName");
            this.dt1.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.dt1.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("dt1.Properties.Appearance.FontSizeDelta")));
            this.dt1.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("dt1.Properties.Appearance.FontStyleDelta")));
            this.dt1.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("dt1.Properties.Appearance.GradientMode")));
            this.dt1.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("dt1.Properties.Appearance.Image")));
            this.dt1.Properties.Appearance.Options.UseTextOptions = true;
            this.dt1.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.dt1.Properties.AutoHeight = ((bool)(resources.GetObject("dt1.Properties.AutoHeight")));
            this.dt1.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("dt1.Properties.Buttons"))))});
            this.dt1.Properties.CalendarTimeProperties.AccessibleDescription = resources.GetString("dt1.Properties.CalendarTimeProperties.AccessibleDescription");
            this.dt1.Properties.CalendarTimeProperties.AccessibleName = resources.GetString("dt1.Properties.CalendarTimeProperties.AccessibleName");
            this.dt1.Properties.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("dt1.Properties.CalendarTimeProperties.AutoHeight")));
            this.dt1.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dt1.Properties.CalendarTimeProperties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dt1.Properties.CalendarTimeProperties.Mask.AutoComplete")));
            this.dt1.Properties.CalendarTimeProperties.Mask.BeepOnError = ((bool)(resources.GetObject("dt1.Properties.CalendarTimeProperties.Mask.BeepOnError")));
            this.dt1.Properties.CalendarTimeProperties.Mask.EditMask = resources.GetString("dt1.Properties.CalendarTimeProperties.Mask.EditMask");
            this.dt1.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dt1.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.dt1.Properties.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dt1.Properties.CalendarTimeProperties.Mask.MaskType")));
            this.dt1.Properties.CalendarTimeProperties.Mask.PlaceHolder = ((char)(resources.GetObject("dt1.Properties.CalendarTimeProperties.Mask.PlaceHolder")));
            this.dt1.Properties.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("dt1.Properties.CalendarTimeProperties.Mask.SaveLiteral")));
            this.dt1.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dt1.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.dt1.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dt1.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat")));
            this.dt1.Properties.CalendarTimeProperties.NullValuePrompt = resources.GetString("dt1.Properties.CalendarTimeProperties.NullValuePrompt");
            this.dt1.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dt1.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue")));
            this.dt1.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dt1.Properties.Mask.AutoComplete")));
            this.dt1.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("dt1.Properties.Mask.BeepOnError")));
            this.dt1.Properties.Mask.EditMask = resources.GetString("dt1.Properties.Mask.EditMask");
            this.dt1.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dt1.Properties.Mask.IgnoreMaskBlank")));
            this.dt1.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dt1.Properties.Mask.MaskType")));
            this.dt1.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("dt1.Properties.Mask.PlaceHolder")));
            this.dt1.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("dt1.Properties.Mask.SaveLiteral")));
            this.dt1.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dt1.Properties.Mask.ShowPlaceHolders")));
            this.dt1.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dt1.Properties.Mask.UseMaskAsDisplayFormat")));
            this.dt1.Properties.NullValuePrompt = resources.GetString("dt1.Properties.NullValuePrompt");
            this.dt1.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dt1.Properties.NullValuePromptShowForEmptyValue")));
            this.dt1.EditValueChanged += new System.EventHandler(this.dt1_EditValueChanged);
            // 
            // labelControl1
            // 
            resources.ApplyResources(this.labelControl1, "labelControl1");
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Click += new System.EventHandler(this.labelControl1_Click);
            // 
            // btn_Advanced
            // 
            resources.ApplyResources(this.btn_Advanced, "btn_Advanced");
            this.btn_Advanced.Name = "btn_Advanced";
            this.btn_Advanced.Click += new System.EventHandler(this.btn_Advanced_Click);
            // 
            // frm_E_InvoiceList_Rejected
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.btn_Advanced);
            this.Controls.Add(this.btnClearSearch);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.dt2);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.dt1);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.grdInvoices);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.KeyPreview = true;
            this.Name = "frm_E_InvoiceList_Rejected";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_E_InvoiceList_FormClosing);
            this.Load += new System.EventHandler(this.frm_E_InvoiceList_Load);
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.frm_E_InvoiceList_KeyUp);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdInvoices)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_InvoiceBook)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_paymethod)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_User)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_salesEmp)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_CategoryId)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repCrncy)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Accounts)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_Sync)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dt2.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dt2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dt1.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dt1.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnNew;
        private DevExpress.XtraBars.BarButtonItem barBtnOpen;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtn_Help;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraGrid.GridControl grdInvoices;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraBars.BarButtonItem barBtnRefresh;
        private DevExpress.XtraGrid.Columns.GridColumn col_SL_InvoiceId;
        private DevExpress.XtraGrid.Columns.GridColumn col_InvoiceCode;
        private DevExpress.XtraGrid.Columns.GridColumn col_StoreId;
        private DevExpress.XtraGrid.Columns.GridColumn col_CustomerId;
        private DevExpress.XtraGrid.Columns.GridColumn col_InvoiceDate;
        private DevExpress.XtraGrid.Columns.GridColumn col_Notes;
        private DevExpress.XtraGrid.Columns.GridColumn col_Expenses;
        private DevExpress.XtraGrid.Columns.GridColumn col_DiscountRatio;
        private DevExpress.XtraGrid.Columns.GridColumn col_PayMethod;
        private DevExpress.XtraGrid.Columns.GridColumn col_DiscountValue;
        private DevExpress.XtraGrid.Columns.GridColumn col_Net;
        private DevExpress.XtraGrid.Columns.GridColumn col_UserId;
        private DevExpress.XtraEditors.Repository.RepositoryItemImageComboBox rep_paymethod;
        private DevExpress.XtraGrid.Columns.GridColumn colStore;
        private DevExpress.XtraEditors.SimpleButton btnClearSearch;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.DateEdit dt2;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.DateEdit dt1;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraGrid.Columns.GridColumn colPaid;
        private DevExpress.XtraGrid.Columns.GridColumn colRemains;
        private DevExpress.XtraGrid.Columns.GridColumn col_SalesEmpId;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit rep_salesEmp;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem mi_OpenDealer;
        private DevExpress.XtraGrid.Columns.GridColumn col_CustId;
        private DevExpress.XtraGrid.Columns.GridColumn col_TotalCostPrice;
        private DevExpress.XtraGrid.Columns.GridColumn col_Profit;
        private DevExpress.XtraGrid.Columns.GridColumn col_CategoryId;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit rep_CategoryId;
        private DevExpress.XtraGrid.Columns.GridColumn col_Is_OutTrans;
        private DevExpress.XtraGrid.Columns.GridColumn col_InvoiceBookId;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit rep_InvoiceBook;
        private DevExpress.XtraGrid.Columns.GridColumn col_AddTaxValue;
        private DevExpress.XtraGrid.Columns.GridColumn col_DeductTaxValue;
        private DevExpress.XtraGrid.Columns.GridColumn col_TaxValue;
        private DevExpress.XtraGrid.Columns.GridColumn col_DueDate;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit repCrncy;
        private DevExpress.XtraGrid.Columns.GridColumn col_CrncId;
        private DevExpress.XtraGrid.Columns.GridColumn col_CrncRate;
        private DevExpress.XtraGrid.Columns.GridColumn colDriverName;
        private DevExpress.XtraGrid.Columns.GridColumn colVehicleNumber;
        private DevExpress.XtraGrid.Columns.GridColumn colDestination;
        private DevExpress.XtraGrid.Columns.GridColumn col_ProfitRatio;
        private DevExpress.XtraGrid.Columns.GridColumn col_Process;
        private DevExpress.XtraGrid.Columns.GridColumn col_SourceCode;
        private DevExpress.XtraGrid.Columns.GridColumn col_ProfitCostRatio;
        private DevExpress.XtraBars.BarSubItem barMnu_Print;
        private DevExpress.XtraBars.BarButtonItem barBtn_Print1;
        private DevExpress.XtraBars.BarButtonItem barBtn_PrintData;
        private DevExpress.XtraGrid.Columns.GridColumn col_IsOffer;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit rep_User;
        private DevExpress.XtraGrid.Columns.GridColumn col_Paid_To;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit rep_Accounts;
        private DevExpress.XtraGrid.Columns.GridColumn col_CustomerGroup;
        private DevExpress.XtraGrid.Columns.GridColumn col_Total;
        private DevExpress.XtraGrid.Columns.GridColumn col_IdRegion;
        private DevExpress.XtraGrid.Columns.GridColumn col_City;
        private DevExpress.XtraEditors.SimpleButton btn_Advanced;
        public DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit chk_Sync;
        private DevExpress.XtraBars.BarButtonItem btn_UpdateInvoices;
    }
}