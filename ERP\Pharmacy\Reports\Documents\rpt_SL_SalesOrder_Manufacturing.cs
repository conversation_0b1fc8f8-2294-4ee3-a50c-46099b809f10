using System;
using System.Data;
using System.Data.Linq;
using System.Linq;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using DAL;

namespace Reports
{
    public partial class rpt_SL_SalesOrder_Manufacturing : DevExpress.XtraReports.UI.XtraReport
    {
        public rpt_SL_SalesOrder_Manufacturing(int SalesOrder)
        {
            InitializeComponent();

            ERPDataContext DB = new ERPDataContext();

            var emps = DB.HR_Employees.ToList();

            IList data = null;

            data = (from d in DB.ManfDetails
                    join m in DB.Manufacturings on d.Manf_Id equals m.Manf_Id
                    join i in DB.IC_Items on d.ItemId equals i.ItemId
                    join u in DB.IC_UOMs on d.UOMId equals u.UOMId
                    where m.ProcessType == (int)Process.SalesOrder
                    where m.ProcessId == SalesOrder
                    select new
                    {
                        ItemName = Shared.IsEnglish ? i.ItemNameEn : i.ItemNameAr,
                        ItemDesc = Shared.IsEnglish ? i.DescriptionEn : i.Description,
                        d.Qty,
                        u.UOM,
                        d.Length,
                        d.Width,
                        d.Height,
                        LinearMeter = (d.Length * d.Qty),
                        SQ_Meter = (d.Length * d.Width)
                    }).ToList();

            lbl_Item.DataBindings.Add("Text", data, "ItemName");
            lbl_Qty.DataBindings.Add("Text", data, "Qty");
            lbl_Uom.DataBindings.Add("Text", data, "UOM");
            lbl_LinearMeter.DataBindings.Add("Text", data, "LinearMeter");
            lbl_Length.DataBindings.Add("Text", data, "Length");
            lbl_Height.DataBindings.Add("Text", data, "Height");
            lbl_Width.DataBindings.Add("Text", data, "Width");
            lbl_SQ_Meter.DataBindings.Add("Text", data, "SQ_Meter");
        }

    }
}
