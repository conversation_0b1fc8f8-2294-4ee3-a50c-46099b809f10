﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.Utils;
using DevExpress.XtraLayout.Utils;
using Pharmacy.Forms.IC;
using System.Linq;
using Pharmacy.Forms.PR;
using Pharmacy.Forms.SL;
using DAL;
using Pharmacy.Forms;
using BL;

namespace Pharmacy.Reports
{
    public partial class frm_ReportViewer1 : DevExpress.XtraEditors.XtraForm
    {
        List<reportListItem> lstReportListItem = new List<reportListItem>();
        //DataTable dtStores = new DataTable();
        List<SL_Customer_Info> lst_Customers = new List<SL_Customer_Info>();
        List<PR_Vendor> lst_Vendors = new List<PR_Vendor>();
        
        DataTable dtCompanies = new DataTable();
        DataTable dtCatogries = new DataTable();
        DataTable dtCostCenters = new DataTable();
        DataTable dtUsers = new DataTable();
        DataTable dtSalesEmp = new DataTable();

        DataTable dt_filters = new DataTable();

        DateTime dateFrom, dateTo;

        int itemId1, itemId2,
            storeId1, storeId2,
            vendorId1, vendorId2,
            customerId1, customerId2,
            companyId,
            categoryId,
            processType,
            costCenter,
            costCenterAccount,
            customAccListId,
            userId,
            salesEmpId;        

        int? goldenVendor, goldenCustomer;        

        byte FltrTyp_Item, FltrTyp_Date, FltrTyp_Store, FltrTyp_Vendor, FltrTyp_Customer,
            FltrTyp_Company, FltrTyp_Category, FltrTyp_User, FltrTyp_SalesEmp;
        string dateFilter, otherFilters;

        public frm_ReportViewer1()
        {
            RTL.EnCulture(RTL.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            dateFrom = dateTo = Utilities.minDate;
        }

        private void frm_ReportViewer_Load(object sender, EventArgs e)
        {
            if (RTL.IsEnglish)
                RTL.LTRLayout(this);

            #region InitDataTable
            dt_filters.Columns.Add("FilterName");
            dt_filters.Columns.Add("Filter");
            dt_filters.Columns.Add("FilterType", typeof(Int32));
            dt_filters.Columns.Add("From");
            dt_filters.Columns.Add("To");

            dt_filters.Rows.Add("Item", "الصنف");
            dt_filters.Rows.Add("Date", "التاريخ");
            dt_filters.Rows.Add("ExpireDate", "التاريخ أقل من");
            dt_filters.Rows.Add("Store", "الفرع / المخزن");
            dt_filters.Rows.Add("Vendor", "المورد");
            dt_filters.Rows.Add("Customer", "العميل");
            dt_filters.Rows.Add("ItemCompany", "الشركة");
            dt_filters.Rows.Add("ItemCateogry", "فئة الصنف");
            dt_filters.Rows.Add("Process", "نوع العملية");
            dt_filters.Rows.Add("CostCenter", "مركز التكلفة");
            dt_filters.Rows.Add("Account", "الحساب");
            dt_filters.Rows.Add("CustomAccList", "قائمة حسابات مخصصة");
            dt_filters.Rows.Add("User", "المستخدم");
            dt_filters.Rows.Add("SalesEmp", "مندوب المبيعات");
            dt_filters.Rows.Add("Batch", "التشغيلة");
            #endregion

            #region BindDataSources

            #region Vendors
            BL.MyHelper.GetVendors(out lst_Vendors);
            rep_Vendor2.DisplayMember = rep_Vendor1.DisplayMember = "VenNameAr";
            rep_Vendor2.ValueMember = rep_Vendor1.ValueMember = "VendorId";
            rep_Vendor2.DataSource = rep_Vendor1.DataSource = lst_Vendors;
            #endregion

            #region Get Customers
            rep_Customer2.DisplayMember = rep_Customer1.DisplayMember = "CusNameAr";
            rep_Customer2.ValueMember = rep_Customer1.ValueMember = "CustomerId";
            BL.MyHelper.GetCustomers(out lst_Customers);
            rep_Customer2.DataSource = rep_Customer1.DataSource = lst_Customers;
            #endregion

            #region Get users
            rep_User.DisplayMember = "UserName";
            rep_User.ValueMember = "UserId";
            rep_User.DataSource = dtUsers;
            #endregion

            #region Get sales emps
            BL.MyHelper.GetSalesEmps(dtSalesEmp, true, true);
            rep_Emp.DisplayMember = "EmpName";
            rep_Emp.ValueMember = "EmpId";
            rep_Emp.DataSource = dtSalesEmp;
            #endregion

            #region Get Companies
            rep_ItemCompany2.DisplayMember = rep_ItemCompany1.DisplayMember = "CompanyNameAr";
            rep_ItemCompany2.ValueMember = rep_ItemCompany1.ValueMember = "CompanyId";
            rep_ItemCompany2.DataSource = rep_ItemCompany1.DataSource = dtCompanies;
            #endregion

            #region Get Categories
            rep_ItemCateogry2.DisplayMember = rep_ItemCateogry1.DisplayMember = "CategoryNameAr";
            rep_ItemCateogry2.ValueMember = rep_ItemCateogry1.ValueMember = "CategoryId";
            MyHelper.GetCategories(dtCatogries);
            rep_ItemCateogry2.DataSource = rep_ItemCateogry1.DataSource = dtCatogries;
            #endregion

            #region Get Stores
            rep_Store2.DisplayMember = rep_Store1.DisplayMember = "StoreNameAr";
            rep_Store2.ValueMember = rep_Store1.ValueMember = "StoreId";
            rep_Store2.DataSource = rep_Store1.DataSource = MyHelper.Get_Stores4Reports();
            #endregion

            #region LoadItems
            linqServerModeSource1.ElementType = typeof(DAL.IC_Item);
            linqServerModeSource1.KeyExpression = "ItemId";
            linqServerModeSource1.QueryableSource = new DAL.ERPDataContext().IC_Items.Where(i => i.ItemType != (int)BL.ItemType.MatrixParent && i.ItemType != (int)BL.ItemType.Subtotal);
            rep_Item1.ServerMode = true;
            rep_Item1.DataSource = linqServerModeSource1;
            rep_Item2.ServerMode = true;
            rep_Item2.DataSource = linqServerModeSource1;
            #endregion

            #region Customersgroups
            //lst_CustGroup = BL.MyHelper.GetCustomersGroups();
            //lkpCustGroup.Properties.DisplayMember = "CGNameAr";
            //lkpCustGroup.Properties.ValueMember = "CustomerGroupId";
            //lkpCustGroup.Properties.DataSource = lst_CustGroup;
            #endregion

            #region Process
            rep_process.DataSource = new ERPDataContext().LKP_Processes.ToList();
            rep_process.ValueMember = "ProcessId";
            rep_process.DisplayMember = "ProcessName";
            #endregion


            #endregion

            gridControl1.DataSource = dt_filters;

        }

        private void NBI_IC_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            //Fill Store Reports
            lstReportListItem.Clear();

            //lstReportListItem.Add(new reportListItem
            //{
            //    ReportName = "rprt_IC_Items",
            //    ReportCaption = "الأصناف",
            //    Description = "عرض كود1, اسم الصنف-ن, الفئة, المجموعة العلمية, الشركة, الكمية الحالية",
            //    TabIndex = 0,
            //    ItemFltr = false,
            //    DateFltr = false,
            //    ExpDateFltr = false,
            //    StoreFltr = false,
            //    VendorFltr = false,
            //    CustomerFltr = false,
            //    ComapnyFltr = false,
            //    CategoryFltr = false
            //});

            //lstReportListItem.Add(new reportListItem
            //{
            //    ReportName = "frm_rpt_IC_EditItemQty",
            //    ReportCaption = "أصناف تم تسوية كمياتها",
            //    Description = "عرض كود1, اسم الصنف-ن, الكميه القديمه والكميه الجديده",
            //    TabIndex = 0,
            //    ItemFltr = false,
            //    DateFltr = true,
            //    ExpDateFltr = false,
            //    StoreFltr = true,
            //    VendorFltr = false,
            //    CustomerFltr = false,
            //    ComapnyFltr = false,
            //    CategoryFltr = false
            //});

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_IC_ItemsOpenBalance",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_IC_ItemsOpenBalance : ResRptAr.rpt_IC_ItemsOpenBalance, //الارصدة الافتتاحية
                Description = "عرض كود1, اسم الصنف-ن, الكميه,سعر التكلفه",
                TabIndex = 0,
                ItemFltr = true,
                DateFltr = false,
                ExpDateFltr = false,
                StoreFltr = true,
                VendorFltr = true,
                CustomerFltr = false,
                ComapnyFltr = false,
                CategoryFltr = false,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_IC_ItemsQty",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_IC_ItemsQty : ResRptAr.rpt_IC_ItemsQty, //"أرصدة الأصناف",
                Description = "عرض الأصناف وأرصدتها الموجوده في المخازن",
                TabIndex = 0,
                ItemFltr = true,
                DateFltr = false,
                ExpDateFltr = false,
                StoreFltr = true,
                VendorFltr = false,
                CustomerFltr = false,
                ComapnyFltr = true,
                CategoryFltr = true,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_IC_ItemsQtyWithPrices",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_IC_ItemsQtyWithPrices : ResRptAr.rpt_IC_ItemsQtyWithPrices, //"أرصدة الأصناف بالأسعار",
                Description = "عرض الأصناف وأرصدتها و تقييمها بأسعار الشراء ، البيع و التكلفة",
                TabIndex = 0,
                ItemFltr = true,
                DateFltr = false,
                ExpDateFltr = false,
                StoreFltr = true,
                VendorFltr = false,
                CustomerFltr = false,
                ComapnyFltr = true,
                CategoryFltr = true,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });


            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_IC_ItemsReorder",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_IC_ItemsReorder : ResRptAr.rpt_IC_ItemsReorder, //"أصناف وصلت لحد الطلب",
                Description = "عرض الأصناف التي وصلت لحد الطلب المحدد في كارت الصنف وأرصدتها الموجوده في المخازن",
                TabIndex = 0,
                ItemFltr = true,
                DateFltr = false,
                ExpDateFltr = false,
                StoreFltr = true,
                VendorFltr = false,
                CustomerFltr = false,
                ComapnyFltr = true,
                CategoryFltr = true,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_IC_ItemsNotSold",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_IC_ItemsNotSold : ResRptAr.rpt_IC_ItemsNotSold, //"أصناف لم تباع مطلقا",
                Description = "عرض الأصناف التي تم شرائها ولكن لم يباع منها شئ حتي الان",
                TabIndex = 0,
                ItemFltr = true,
                DateFltr = false,
                ExpDateFltr = false,
                StoreFltr = true,
                VendorFltr = false,
                CustomerFltr = false,
                ComapnyFltr = true,
                CategoryFltr = true,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_IC_ItemsMinSell",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_IC_ItemsMinSell : ResRptAr.rpt_IC_ItemsMinSell, //"الاصناف الاقل مبيعا",
                Description = "عرض الأصناف الاقل مبيعا حتي الان",
                TabIndex = 0,
                ItemFltr = true,
                DateFltr = false,
                ExpDateFltr = false,
                StoreFltr = true,
                VendorFltr = false,
                CustomerFltr = false,
                ComapnyFltr = true,
                CategoryFltr = true,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_IC_ItemsMaxSell",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_IC_ItemsMaxSell : ResRptAr.rpt_IC_ItemsMaxSell, //"الاصناف الاكثر مبيعا",
                Description = "عرض الأصناف الاكثر مبيعا حتي الان",
                TabIndex = 0,
                ItemFltr = true,
                DateFltr = false,
                ExpDateFltr = false,
                StoreFltr = true,
                VendorFltr = false,
                CustomerFltr = false,
                ComapnyFltr = true,
                CategoryFltr = true,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_IC_ItemsTotals",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_IC_ItemsTotals : ResRptAr.rpt_IC_ItemsTotals, //"حركة الاصناف",
                Description = "عرض اجمالي حركات التي تمت علي الصنف مثل البيع والشراء والمرتجعات",
                TabIndex = 0,
                ItemFltr = true,
                DateFltr = false,
                ExpDateFltr = false,
                StoreFltr = true,
                VendorFltr = false,
                CustomerFltr = false,
                ComapnyFltr = true,
                CategoryFltr = true,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_IC_ItemTransactions",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_IC_ItemTransactions : ResRptAr.rpt_IC_ItemTransactions, //"حركة صنف تفصيلي",
                Description = "عرض تفصيلي لجميع الحركات التي تمت علي الصنف",
                TabIndex = 0,
                ItemFltr = true,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = true,
                VendorFltr = false,
                CustomerFltr = false,
                ComapnyFltr = false,
                CategoryFltr = false,
                ProcessFltr = true,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = true
            });
            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_IC_ItemOpenInOutClose",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_IC_ItemOpenInOutClose : ResRptAr.rpt_IC_ItemOpenInOutClose, //"تقرير وارد و صادر أصناف",
                Description = "عرض تفصيلي لرصيد اول المدة والوارد والصادر ورصيد نهاية المدة للأصناف",
                TabIndex = 0,
                ItemFltr = true,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = true,
                VendorFltr = false,
                CustomerFltr = false,
                ComapnyFltr = false,
                CategoryFltr = true,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });
            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_IC_SoldItemsCost",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_IC_SoldItemsCost : ResRptAr.rpt_IC_SoldItemsCost, //"تكلفة البضاعـــة المبـــاعة",
                Description = "تكلفة البضاعـــة المبـــاعة",
                TabIndex = 0,
                ItemFltr = true,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = true,
                VendorFltr = false,
                CustomerFltr = false,
                ComapnyFltr = false,
                CategoryFltr = true,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });
            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_IC_ItemTransactionsDetails",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_IC_ItemTransactionsDetails : ResRptAr.rpt_IC_ItemTransactionsDetails, //"جرد محتويات مخزن",
                Description = "جرد محتويات مخزن",
                TabIndex = 0,
                ItemFltr = false,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = true,
                VendorFltr = false,
                CustomerFltr = false,
                ComapnyFltr = false,
                CategoryFltr = false,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            if (frmMain.st_Store.ExpireDate == true)//mahmoud:expire, if expire available, show report
            {
                lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_IC_ItemsExpired",
                    ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_IC_ItemsExpired : ResRptAr.rpt_IC_ItemsExpired, //"أصناف تنتهي صلاحيتها",
                    Description = "أصناف تنتهي صلاحيتها",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = false,
                    ExpDateFltr = true,
                    StoreFltr = true,
                    VendorFltr = false,
                    CustomerFltr = false,
                    ComapnyFltr = true,
                    CategoryFltr = true,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    CostCenterAccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false
                });
            }

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_IC_ItemsQtyDetails",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_IC_ItemsQtyDetails : ResRptAr.rpt_IC_ItemsQtyDetails,
                Description = "تقرير أرصدة الأصناف تفصيلي",
                TabIndex = 0,
                ItemFltr = true,
                DateFltr = false,
                ExpDateFltr = false,
                StoreFltr = true,
                VendorFltr = false,
                CustomerFltr = false,
                ComapnyFltr = false,
                CategoryFltr = false,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = true
            });
            

            lstBxReports.DataSource = lstReportListItem;
            lstBxReports.ValueMember = "ReportName";
            lstBxReports.DisplayMember = "ReportCaption";
            txtDescription.Text = string.Empty;
            lstBxReports.SelectedIndex = -1;            
            lstBxReports.Focus();

        }

        private void NBI_SL_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            lstReportListItem.Clear();

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_SL_InvoicesHeaders",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_SL_InvoicesHeaders : ResRptAr.rpt_SL_InvoicesHeaders, //"اجمالي فواتير المبيعات",
                Description = "عرض اجماليات البيانات الرئيسيه لفواتير المبيعات",
                TabIndex = 0,
                ItemFltr = false,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = true,
                VendorFltr = false,
                CustomerFltr = true,
                ComapnyFltr = false,
                CategoryFltr = false,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_SL_ReturnHeaders",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_SL_ReturnHeaders : ResRptAr.rpt_SL_ReturnHeaders, //"اجمالي فواتير مردود المبيعات",
                Description = "عرض اجماليات البيانات الرئيسيه لفواتير مردود المبيعات",
                TabIndex = 0,
                ItemFltr = false,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = true,
                VendorFltr = false,
                CustomerFltr = true,
                ComapnyFltr = false,
                CategoryFltr = false,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_SL_ItemsSales",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_SL_ItemsSales : ResRptAr.rpt_SL_ItemsSales, //"اجمالي مبيعات صنف/أصناف",
                Description = "عرض اجمالي الكميه المباعه من صنف او من عدة أصناف",
                TabIndex = 0,
                ItemFltr = true,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = true,
                VendorFltr = false,
                CustomerFltr = false,
                ComapnyFltr = true,
                CategoryFltr = true,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_SL_ItemsReturn",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_SL_ItemsReturn : ResRptAr.rpt_SL_ItemsReturn, //"اجمالي مردود مبيعات صنف/أصناف",
                Description = "عرض اجمالي كمية مردود مبيعات من صنف او من عدة أصناف",
                TabIndex = 0,
                ItemFltr = true,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = true,
                VendorFltr = false,
                CustomerFltr = false,
                ComapnyFltr = true,
                CategoryFltr = true,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_SL_ItemTrade",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_SL_ItemTrade : ResRptAr.rpt_SL_ItemTrade, // "ربح او خسارة صنف",
                Description = "عرض تقرير متاجرة الصنف، وحساب الربح او الخسارة",
                TabIndex = 0,
                ItemFltr = true,
                DateFltr = false,
                ExpDateFltr = false,
                StoreFltr = true,
                VendorFltr = false,
                CustomerFltr = false,
                ComapnyFltr = false,
                CategoryFltr = false,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_Acc_SL_AccountDetails",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_Acc_SL_AccountDetails : ResRptAr.rpt_Acc_SL_AccountDetails, // "كشف حساب تفصيلي",
                Description = "كشف حساب تفصيلي، يعرض تفاصيل الفواتير أو أصناف الفواتير من ضمن التقرير",
                TabIndex = 0,
                ItemFltr = false,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = false,
                VendorFltr = false,
                CustomerFltr = true,
                ComapnyFltr = false,
                CategoryFltr = false,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_Acc_SL_AccountsBalances",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_Acc_SL_AccountsBalances : ResRptAr.rpt_Acc_SL_AccountsBalances, // "كشف اجمالى حسابات العملاء",
                Description = "يعرض اجمالي كشف الحساب لكل العملاء",
                TabIndex = 0,
                ItemFltr = false,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = false,
                VendorFltr = false,
                CustomerFltr = true,
                ComapnyFltr = false,
                CategoryFltr = false,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_SL_CustomerItemsSales",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_SL_CustomerItemsSales : ResRptAr.rpt_SL_CustomerItemsSales, //"اجمالي مبيعات صنف لعميل",
                Description = "يعرض اجمالي مبيعات صنف او مجموعة اصناف الى عميل",
                TabIndex = 0,
                ItemFltr = true,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = false,
                VendorFltr = false,
                CustomerFltr = true,
                ComapnyFltr = false,
                CategoryFltr = false,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_SL_SalesEmpCommission",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_SL_SalesEmpCommission : ResRptAr.rpt_SL_SalesEmpCommission,
                Description = "يعرض عمولة المندوب للمبيعات والتحصيل حسب الهدف المحدد",
                TabIndex = 0,
                ItemFltr = false,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = false,
                VendorFltr = false,
                CustomerFltr = false,
                ComapnyFltr = false,
                CategoryFltr = false,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = true,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_SL_SalesCommision",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_SL_SalesCommision : ResRptAr.rpt_SL_SalesCommision,
                Description = "يعرض مبيعات المندوب والنسب المستحقه حسب العمولة المحددة",
                TabIndex = 0,
                ItemFltr = false,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = false,
                VendorFltr = false,
                CustomerFltr = true,
                ComapnyFltr = false,
                CategoryFltr = false,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = true,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_SL_DeliveryCommision",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_IC_DeliveryCommision : ResRptAr.rpt_IC_DeliveryCommision,
                Description = "يعرض عمولات مسئولي التسليم عن البضاعة المنصرفة من المخازن",
                TabIndex = 0,
                ItemFltr = true,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = false,
                VendorFltr = false,
                CustomerFltr = false,
                ComapnyFltr = false,
                CategoryFltr = false,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = true,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_SL_CustomerTrans",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_SL_CustomerTrans : ResRptAr.rpt_SL_CustomerTrans,
                Description = "يعرض بيان بجميع العمليات التي تمت مع عميل",
                TabIndex = 0,
                ItemFltr = false,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = false,
                VendorFltr = false,
                CustomerFltr = true,
                ComapnyFltr = false,
                CategoryFltr = false,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstBxReports.DataSource = lstReportListItem;
            lstBxReports.ValueMember = "ReportName";
            lstBxReports.DisplayMember = "ReportCaption";
            txtDescription.Text = string.Empty;
            lstBxReports.SelectedIndex = -1;
            
            lstBxReports.Focus();
        }

        private void NBI_PR_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            lstReportListItem.Clear();

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_PR_InvoicesHeaders",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_PR_InvoicesHeaders : ResRptAr.rpt_PR_InvoicesHeaders, //"اجمالي فواتير المشتريات",
                Description = "عرض اجماليات البيانات الرئيسيه لفواتير المشتريات",
                TabIndex = 0,
                ItemFltr = false,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = true,
                VendorFltr = true,
                CustomerFltr = false,
                ComapnyFltr = false,
                CategoryFltr = false,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_PR_ReturnHeaders",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_PR_ReturnHeaders : ResRptAr.rpt_PR_ReturnHeaders, //"اجمالي فواتير مردود المشتريات",
                Description = "عرض اجماليات البيانات الرئيسيه لفواتير مردود المشتريات",
                TabIndex = 0,
                ItemFltr = false,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = true,
                VendorFltr = true,
                CustomerFltr = false,
                ComapnyFltr = false,
                CategoryFltr = false,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_PR_ItemsPurchases",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_PR_ItemsPurchases : ResRptAr.rpt_PR_ItemsPurchases, //"اجمالي مشتريات صنف/أصناف",
                Description = "عرض اجمالي الكميه المشتراه من صنف او من عدة أصناف",
                TabIndex = 0,
                ItemFltr = true,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = true,
                VendorFltr = true,
                CustomerFltr = false,
                ComapnyFltr = true,
                CategoryFltr = true,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_PR_ItemsReturns",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_PR_ItemsReturns : ResRptAr.rpt_PR_ItemsReturns, //"اجمالي مردود مشتريات صنف/أصناف",
                Description = "عرض اجمالي كمية مردود شراء من صنف او من عدة أصناف",
                TabIndex = 0,
                ItemFltr = true,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = true,
                VendorFltr = false,
                CustomerFltr = false,
                ComapnyFltr = true,
                CategoryFltr = true,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
                {
                    ReportName = "rpt_ItemPriceChangings",
                    ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_ItemPriceChangings : ResRptAr.rpt_ItemPriceChangings, //"تغييرات أسعار الاصناف",
                    Description = "عرض تغيرات الاسعار التي حدثت علي صنف او مجموعة اصناف",
                    TabIndex = 0,
                    ItemFltr = true,
                    DateFltr = true,
                    ExpDateFltr = false,
                    StoreFltr = false,
                    VendorFltr = false,
                    CustomerFltr = false,
                    ComapnyFltr = false,
                    CategoryFltr = false,
                    ProcessFltr = false,
                    CostCenterFltr = false,
                    CostCenterAccountFltr = false,
                    CustmAccListFltr = false,
                    UserFltr = false,
                    SalesEmpFltr = false,
                    BatchFltr = false
                });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_Acc_PR_AccountDetails",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_Acc_PR_AccountDetails : ResRptAr.rpt_Acc_PR_AccountDetails, //"كشف حساب تفصيلي",
                Description = "كشف حساب تفصيلي، يعرض تفاصيل الفواتير أو أصناف الفواتير من ضمن التقرير",
                TabIndex = 0,
                ItemFltr = false,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = false,
                VendorFltr = true,
                CustomerFltr = false,
                ComapnyFltr = false,
                CategoryFltr = false,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_Acc_PR_AccountsBalances",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_Acc_PR_AccountsBalances : ResRptAr.rpt_Acc_PR_AccountsBalances, //"كشف اجمالى حسابات الموردين",
                Description = "يعرض اجمالي كشف الحساب لكل الموردين",
                TabIndex = 0,
                ItemFltr = false,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = false,
                VendorFltr = true,
                CustomerFltr = false,
                ComapnyFltr = false,
                CategoryFltr = false,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_PR_VendorItemsPurchases",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_PR_VendorItemsPurchases : ResRptAr.rpt_PR_VendorItemsPurchases, //"اجمالي مشتربات صنف من مورد",
                Description = "يعرض اجمالي مشتريات صنف او مجموعة اصناف من مورد",
                TabIndex = 0,
                ItemFltr = true,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = false,
                VendorFltr = true,
                CustomerFltr = false,
                ComapnyFltr = false,
                CategoryFltr = false,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstBxReports.DataSource = lstReportListItem;
            lstBxReports.ValueMember = "ReportName";
            lstBxReports.DisplayMember = "ReportCaption";
            txtDescription.Text = string.Empty;
            lstBxReports.SelectedIndex = -1;
            
            lstBxReports.Focus();

        }

        private void NBI_Acc_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            lstReportListItem.Clear();

            //lstReportListItem.Add(new reportListItem
            //{
            //    ReportName = "rpt_Acc_Income",
            //    ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_Acc_Income : ResRptAr.rpt_Acc_Income, //"قائمة الدخل",
            //    Description = "قائمة الدخل",
            //    TabIndex = 0,
            //    ItemFltr = false,
            //    DateFltr = true,
            //    ExpDateFltr = false,
            //    StoreFltr = false,
            //    VendorFltr = false,
            //    CustomerFltr = false,
            //    ComapnyFltr = false,
            //    CategoryFltr = false,
            //    ProcessFltr = false,
            //    CostCenterFltr = true,
            //    CostCenterAccountFltr = false,
            //    CustmAccListFltr = false,
            //    UserFltr = false,
            //    SalesEmpFltr = false,
            //    BatchFltr = false
            //});                  

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_Acc_IncomeT",
                ReportCaption = (RTL.IsEnglish ? ResRptEn.rpt_Acc_Income : ResRptAr.rpt_Acc_Income) + " T ", //"قائمة الدخل",
                Description = "قائمة الدخل T",
                TabIndex = 0,
                ItemFltr = false,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = false,
                VendorFltr = false,
                CustomerFltr = false,
                ComapnyFltr = false,
                CategoryFltr = false,
                ProcessFltr = false,
                CostCenterFltr = true,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });
            
            //lstReportListItem.Add(new reportListItem
            //{
            //    ReportName = "rpt_Acc_Balance",
            //    ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_Acc_Balance : ResRptAr.rpt_Acc_Balance, //"المركز المالي",
            //    Description = "المركز المالي",
            //    TabIndex = 0,
            //    ItemFltr = false,
            //    DateFltr = true,
            //    ExpDateFltr = false,
            //    StoreFltr = false,
            //    VendorFltr = false,
            //    CustomerFltr = false,
            //    ComapnyFltr = false,
            //    CategoryFltr = false,
            //    ProcessFltr = false,
            //    CostCenterFltr = false,
            //    CostCenterAccountFltr = false,
            //    CustmAccListFltr = false,
            //    UserFltr = false,
            //    SalesEmpFltr = false,
            //    BatchFltr = false
            //});
           
            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_Acc_BalanceT",
                ReportCaption = (RTL.IsEnglish ? ResRptEn.rpt_Acc_Balance : ResRptAr.rpt_Acc_Balance) + " T ", //"المركز المالي T",
                Description = "المركز المالي T",
                TabIndex = 0,
                ItemFltr = false,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = false,
                VendorFltr = false,
                CustomerFltr = false,
                ComapnyFltr = false,
                CategoryFltr = false,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });
            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_Acc_CostCenter_AccDetails",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_Acc_CostCenter_AccDetails : ResRptAr.rpt_Acc_CostCenter_AccDetails, //"كشف حساب مجمع لمركز التكلفة",
                Description = "كشف حساب مجمع لمركز التكلفة",
                TabIndex = 0,
                ItemFltr = false,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = false,
                VendorFltr = false,
                CustomerFltr = false,
                ComapnyFltr = false,
                CategoryFltr = false,
                ProcessFltr = false,
                CostCenterFltr = true,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_Acc_Account_CostCenters",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_Acc_Account_CostCenters : ResRptAr.rpt_Acc_Account_CostCenters, //"كشف مجمع لمراكز تكلفة حساب",
                Description = "كشف مجمع لمراكز تكلفة حساب",
                TabIndex = 0,
                ItemFltr = false,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = false,
                VendorFltr = false,
                CustomerFltr = false,
                ComapnyFltr = false,
                CategoryFltr = false,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = true,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_Acc_CostCenterTotalBalances",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_Acc_CostCenterTotalBalances : ResRptAr.rpt_Acc_CostCenterTotalBalances, //"إجمالي أرصدة مراكز التكلفة",
                Description = "إجمالي أرصدة مراكز التكلفة",
                TabIndex = 0,
                ItemFltr = false,
                DateFltr = false,
                ExpDateFltr = false,
                StoreFltr = false,
                VendorFltr = false,
                CustomerFltr = false,
                ComapnyFltr = false,
                CategoryFltr = false,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_Acc_CustomAccListDetails",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_Acc_CustomAccListDetails : ResRptAr.rpt_Acc_CustomAccListDetails, //"كشف تفصيلي لقائمة مخصصة",
                Description = "كشف تفصيلي لقائمة مخصصة",
                TabIndex = 0,
                ItemFltr = false,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = false,
                VendorFltr = false,
                CustomerFltr = false,
                ComapnyFltr = false,
                CategoryFltr = false,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = true,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_Acc_CustomAccListBalances",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_Acc_CustomAccListBalances : ResRptAr.rpt_Acc_CustomAccListBalances, //"كشف اجمالى القوائم المخصصة",
                Description = "كشف اجمالى القوائم المخصصة",
                TabIndex = 0,
                ItemFltr = false,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = false,
                VendorFltr = false,
                CustomerFltr = false,
                ComapnyFltr = false,
                CategoryFltr = false,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_Acc_DailyIncome",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_Acc_DailyIncome : ResRptAr.rpt_Acc_DailyIncome,
                Description = "كشف ايرادات يومية لحسابات البنوك والخزانات",
                TabIndex = 0,
                ItemFltr = false,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = false,
                VendorFltr = false,
                CustomerFltr = false,
                ComapnyFltr = false,
                CategoryFltr = false,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });

            lstReportListItem.Add(new reportListItem
            {
                ReportName = "rpt_Acc_DailyPayments",
                ReportCaption = RTL.IsEnglish ? ResRptEn.rpt_Acc_DailyPayments : ResRptAr.rpt_Acc_DailyPayments,
                Description = "كشف مدفوعات يومية",
                TabIndex = 0,
                ItemFltr = false,
                DateFltr = true,
                ExpDateFltr = false,
                StoreFltr = false,
                VendorFltr = false,
                CustomerFltr = false,
                ComapnyFltr = false,
                CategoryFltr = false,
                ProcessFltr = false,
                CostCenterFltr = false,
                CostCenterAccountFltr = false,
                CustmAccListFltr = false,
                UserFltr = false,
                SalesEmpFltr = false,
                BatchFltr = false
            });
            

            lstBxReports.DataSource = lstReportListItem;
            lstBxReports.ValueMember = "ReportName";
            lstBxReports.DisplayMember = "ReportCaption";
            txtDescription.Text = string.Empty;
            lstBxReports.SelectedIndex = -1;
            
            lstBxReports.Focus();
        }

        private void lstBxReports_SelectedValueChanged(object sender, EventArgs e)
        {
            gridView1.RefreshData();
        }

        private void barBtn_Open_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            /*
            if (lstBxReports.SelectedIndex >= 0)
            {
                CreateFilterStrings(out dateFilter, out otherFilters);
                txtDescription.Focus();
                this.Focus();
                string reportCaption = ((reportListItem)lstBxReports.SelectedItem).ReportCaption;

                #region IC

                if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_IC_ItemsOpenBalance")
                {
                    rpt_IC_ItemsOpenBalance rprt = new rpt_IC_ItemsOpenBalance(reportCaption, dateFilter, otherFilters, FltrTyp_Store, storeId1, storeId2,
                        FltrTyp_Vendor, vendorId1, FltrTyp_Item, itemId1, itemId2);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    ////rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_IC_ItemsQty")
                {
                    rpt_IC_ItemsQty rprt = new rpt_IC_ItemsQty(reportCaption, dateFilter, otherFilters, FltrTyp_Store, storeId1, storeId2,
                        FltrTyp_Company, companyId, FltrTyp_Category, categoryId, FltrTyp_Item, itemId1, itemId2);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    ////rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_IC_ItemsQtyWithPrices")
                {
                    rpt_IC_ItemsQtyWithPrices rprt = new rpt_IC_ItemsQtyWithPrices(reportCaption, dateFilter, otherFilters, FltrTyp_Store, storeId1, storeId2,
                        FltrTyp_Company, companyId, FltrTyp_Category, categoryId, FltrTyp_Item, itemId1, itemId2);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    ////rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_IC_ItemsReorder")
                {
                    rpt_IC_ItemsReorder rprt = new rpt_IC_ItemsReorder(reportCaption, dateFilter, otherFilters, FltrTyp_Store, storeId1, storeId2,
                        FltrTyp_Company, companyId, FltrTyp_Category, categoryId, FltrTyp_Item, itemId1, itemId2);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    ////rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_IC_ItemsNotSold")
                {
                    rpt_IC_ItemsNotSold rprt = new rpt_IC_ItemsNotSold(reportCaption, dateFilter, otherFilters, FltrTyp_Store, storeId1, storeId2,
                        FltrTyp_Company, companyId, FltrTyp_Category, categoryId, FltrTyp_Item, itemId1, itemId2);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    ////rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_IC_ItemsMinSell")
                {
                    rpt_IC_ItemsMinSell rprt = new rpt_IC_ItemsMinSell(reportCaption, dateFilter, otherFilters, FltrTyp_Store, storeId1, storeId2,
                        FltrTyp_Company, companyId, FltrTyp_Category, categoryId, FltrTyp_Item, itemId1, itemId2);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    ////rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_IC_ItemsMaxSell")
                {
                    rpt_IC_ItemsMaxSell rprt = new rpt_IC_ItemsMaxSell(reportCaption, dateFilter, otherFilters, FltrTyp_Store, storeId1, storeId2,
                        FltrTyp_Company, companyId, FltrTyp_Category, categoryId, FltrTyp_Item, itemId1, itemId2);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }

                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_IC_ItemsTotals")
                {
                    rpt_IC_ItemsTotals rprt = new rpt_IC_ItemsTotals(reportCaption, dateFilter, otherFilters,
                        FltrTyp_Store, storeId1, storeId2,
                        FltrTyp_Company, companyId,
                        FltrTyp_Category, categoryId,
                        FltrTyp_Item, itemId1, itemId2);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }

                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_IC_ItemTransactions")
                {
                    if (lkpItem1.EditValue == null || string.IsNullOrEmpty(lkpItem1.EditValue.ToString().Trim()))
                    {
                        XtraMessageBox.Show(RTL.IsEnglish ? ResRptEn.ValItem : ResRptAr.ValItem,
                            "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        lkpItem1.Focus();
                        return;
                    }
                    if (lkpStore1.EditValue == null || string.IsNullOrEmpty(lkpStore1.EditValue.ToString().Trim())
                        || lkpStore1.EditValue.ToString() == "0")
                    {
                        XtraMessageBox.Show(RTL.IsEnglish ? ResRptEn.ValStore : ResRptAr.ValStore,
                             "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        lkpStore1.Focus();
                        return;
                    }
                    rpt_IC_ItemTransactions rprt = new rpt_IC_ItemTransactions(reportCaption, dateFilter, otherFilters,
                        FltrTyp_Date, dateFrom, dateTo, storeId1, itemId1, processType, txtBatch.Text.Trim());
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_IC_ItemOpenInOutClose")
                {
                    if (dt1.EditValue == null || string.IsNullOrEmpty(dt1.EditValue.ToString().Trim()) ||
                           dt2.EditValue == null || string.IsNullOrEmpty(dt2.EditValue.ToString().Trim()))
                    {
                        XtraMessageBox.Show(RTL.IsEnglish ? ResRptEn.ValDateFromTo : ResRptAr.ValDateFromTo,
                            "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        if (dt1.EditValue == null || string.IsNullOrEmpty(dt1.EditValue.ToString().Trim()))
                            dt1.Focus();
                        else if (dt2.EditValue == null || string.IsNullOrEmpty(dt2.EditValue.ToString().Trim()))
                            dt2.Focus();
                        return;
                    }

                    rpt_IC_ItemOpenInOutClose rprt = new rpt_IC_ItemOpenInOutClose(reportCaption, dateFilter, otherFilters,
                        FltrTyp_Store, storeId1, storeId2,
                        FltrTyp_Category, categoryId,
                        FltrTyp_Item, itemId1, itemId2,
                        FltrTyp_Date, dateFrom, dateTo);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_IC_SoldItemsCost")
                {
                    rpt_IC_SoldItemsCost rprt = new rpt_IC_SoldItemsCost(reportCaption, dateFilter, otherFilters,
                        FltrTyp_Store, storeId1, storeId2,
                        FltrTyp_Category, categoryId,
                        FltrTyp_Item, itemId1, itemId2,
                        FltrTyp_Date, dateFrom, dateTo);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_IC_ItemTransactionsDetails")
                {
                    if (lkpStore1.EditValue == null || string.IsNullOrEmpty(lkpStore1.EditValue.ToString().Trim())
                       || lkpStore1.EditValue.ToString() == "0")
                    {
                        XtraMessageBox.Show(RTL.IsEnglish ? ResRptEn.ValStore : ResRptAr.ValStore,
                            "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        lkpStore1.Focus();
                        return;
                    }

                    if (dt1.EditValue == null || string.IsNullOrEmpty(dt1.EditValue.ToString().Trim()) ||
                        dt2.EditValue == null || string.IsNullOrEmpty(dt2.EditValue.ToString().Trim()))
                    {
                        XtraMessageBox.Show(RTL.IsEnglish ? ResRptEn.ValDateFromTo : ResRptAr.ValDateFromTo,
                            "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        if (dt1.EditValue == null || string.IsNullOrEmpty(dt1.EditValue.ToString().Trim()))
                            dt1.Focus();
                        else if (dt2.EditValue == null || string.IsNullOrEmpty(dt2.EditValue.ToString().Trim()))
                            dt2.Focus();
                        return;
                    }

                    rpt_IC_ItemTransactionsDetails rprt = new rpt_IC_ItemTransactionsDetails(reportCaption, dateFilter, otherFilters,
                        dateFrom, dateTo, storeId1);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_IC_ItemsExpired")
                {
                    rpt_IC_ItemsExpired rprt = new rpt_IC_ItemsExpired(reportCaption, dateFilter, otherFilters,
                        FltrTyp_Store, storeId1, storeId2,
                        FltrTyp_Company, companyId,
                        FltrTyp_Category, categoryId,
                        FltrTyp_Item, itemId1, itemId2, dtExpDate.DateTime);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_IC_ItemsQtyDetails")
                {
                    rpt_IC_ItemsQtyDetails rprt = new rpt_IC_ItemsQtyDetails(reportCaption, dateFilter, otherFilters,
                        FltrTyp_Store, storeId1, storeId2,
                        FltrTyp_Company, companyId,
                        FltrTyp_Category, categoryId,
                        FltrTyp_Item, itemId1, itemId2, txtBatch.Text.Trim());
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                #endregion

                #region PR
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_PR_InvoicesHeaders")
                {
                    rpt_PR_InvoicesHeaders rprt = new rpt_PR_InvoicesHeaders(reportCaption, dateFilter, otherFilters,
                        FltrTyp_Store, storeId1, storeId2,
                        FltrTyp_Date, dateFrom, dateTo,
                        FltrTyp_Vendor, vendorId1, vendorId2);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_PR_ReturnHeaders")
                {
                    rpt_PR_ReturnHeaders rprt = new rpt_PR_ReturnHeaders(reportCaption, dateFilter, otherFilters,
                        FltrTyp_Store, storeId1, storeId2,
                        FltrTyp_Date, dateFrom, dateTo,
                        FltrTyp_Vendor, vendorId1, vendorId2);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_PR_ItemsPurchases")
                {
                    rpt_PR_ItemsPurchases rprt = new rpt_PR_ItemsPurchases(reportCaption, dateFilter, otherFilters,
                        FltrTyp_Store, storeId1, storeId2,
                        FltrTyp_Company, companyId,
                        FltrTyp_Category, categoryId,
                        FltrTyp_Item, itemId1, itemId2,
                        FltrTyp_Date, dateFrom, dateTo,
                        FltrTyp_Vendor, vendorId1, vendorId2);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_PR_ItemsReturns")
                {
                    rpt_PR_ItemsReturns rprt = new rpt_PR_ItemsReturns(reportCaption, dateFilter, otherFilters,
                        FltrTyp_Store, storeId1, storeId2,
                        FltrTyp_Company, companyId,
                        FltrTyp_Category, categoryId,
                        FltrTyp_Item, itemId1, itemId2,
                        FltrTyp_Date, dateFrom, dateTo);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_ItemPriceChangings")
                {
                    rpt_ItemPriceChangings rprt = new rpt_ItemPriceChangings(reportCaption, dateFilter, otherFilters,
                        FltrTyp_Item, itemId1, itemId2,
                        FltrTyp_Date, dateFrom, dateTo);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_Acc_PR_AccountDetails")
                {
                    if (lkpVendor1.EditValue == null || string.IsNullOrEmpty(lkpVendor1.EditValue.ToString().Trim())
                        || lkpVendor1.EditValue.ToString() == "0")
                    {
                        XtraMessageBox.Show(RTL.IsEnglish ? ResRptEn.ValVendor : ResRptAr.ValVendor
                            , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        lkpVendor1.Focus();
                        return;
                    }

                    DAL.ERPDataContext DB = new DAL.ERPDataContext();
                    var vnd = DB.PR_Vendors.Where(v => v.VendorId == vendorId1).Select(v => v.AccountId).FirstOrDefault();
                    if (vnd == null)
                    {
                        XtraMessageBox.Show(
                            RTL.IsEnglish == true ? ResPrEn.MsgNoAccount : ResPrAr.MsgNoAccount
                            , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }
                    dateTo = dateTo == Utilities.minDate ? Utilities.maxDate : dateTo;
                    rpt_Acc_AccountDetails rprt = new rpt_Acc_AccountDetails(RTL.IsEnglish == true ? ResPrEn.accDetail : ResPrAr.accDetail,
                    dateFilter, otherFilters,
                        dateFrom, dateTo, vnd.Value, false);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_Acc_PR_AccountsBalances")
                {
                    rpt_Acc_AccountsBalances rprt = new rpt_Acc_AccountsBalances(reportCaption, dateFilter, otherFilters,
                        dateFrom, dateTo, false, FltrTyp_Vendor, vendorId1, vendorId2);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_PR_VendorItemsPurchases")
                {
                    rpt_PR_VendorItemsPurchases rprt = new rpt_PR_VendorItemsPurchases(reportCaption, dateFilter, otherFilters,
                        FltrTyp_Item, itemId1, itemId2, FltrTyp_Date, dateFrom, dateTo, FltrTyp_Vendor, vendorId1, vendorId2);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }

                #endregion

                #region SL
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_SL_InvoicesHeaders")
                {
                    rpt_SL_InvoicesHeaders rprt = new rpt_SL_InvoicesHeaders(reportCaption, dateFilter, otherFilters,
                        FltrTyp_Store, storeId1, storeId2,
                        FltrTyp_Date, dateFrom, dateTo,
                        FltrTyp_Customer, customerId1, customerId2);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_SL_ReturnHeaders")
                {
                    rpt_SL_ReturnHeaders rprt = new rpt_SL_ReturnHeaders(reportCaption, dateFilter, otherFilters,
                        FltrTyp_Store, storeId1, storeId2,
                        FltrTyp_Date, dateFrom, dateTo,
                        FltrTyp_Customer, customerId1, customerId2);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_SL_ItemsSales")
                {
                    rpt_SL_ItemsSales rprt = new rpt_SL_ItemsSales(reportCaption, dateFilter, otherFilters,
                        FltrTyp_Store, storeId1, storeId2,
                        FltrTyp_Company, companyId,
                        FltrTyp_Category, categoryId,
                        FltrTyp_Item, itemId1, itemId2,
                        FltrTyp_Date, dateFrom, dateTo);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_SL_ItemsReturn")
                {
                    rpt_SL_ItemsReturn rprt = new rpt_SL_ItemsReturn(reportCaption, dateFilter, otherFilters,
                        FltrTyp_Store, storeId1, storeId2,
                        FltrTyp_Company, companyId,
                        FltrTyp_Category, categoryId,
                        FltrTyp_Item, itemId1, itemId2,
                        FltrTyp_Date, dateFrom, dateTo);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_SL_ItemTrade")
                {
                    if (lkpItem1.EditValue == null || string.IsNullOrEmpty(lkpItem1.EditValue.ToString().Trim()))
                    {
                        XtraMessageBox.Show(RTL.IsEnglish ? ResRptEn.ValItem : ResRptAr.ValItem
                            , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        lkpItem1.Focus();
                        return;
                    }
                    if (lkpStore1.EditValue == null || string.IsNullOrEmpty(lkpStore1.EditValue.ToString().Trim())
                        || lkpStore1.EditValue.ToString() == "0")
                    {
                        XtraMessageBox.Show(RTL.IsEnglish ? ResRptEn.ValStore : ResRptAr.ValStore
                            , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        lkpStore1.Focus();
                        return;
                    }
                    rpt_SL_ItemTrade rprt = new rpt_SL_ItemTrade(reportCaption, dateFilter, otherFilters,
                         storeId1, itemId1);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_Acc_SL_AccountDetails")
                {
                    if (lkpCustomer1.EditValue == null || string.IsNullOrEmpty(lkpCustomer1.EditValue.ToString().Trim())
                         || lkpCustomer1.EditValue.ToString() == "0")
                    {
                        XtraMessageBox.Show(RTL.IsEnglish ? ResRptEn.ValCustomer : ResRptAr.ValCustomer,
                            "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        lkpStore1.Focus();
                        return;
                    }

                    DAL.ERPDataContext DB = new DAL.ERPDataContext();
                    var cst = DB.SL_Customers.Where(v => v.CustomerId == customerId1).Select(v => v.AccountId).FirstOrDefault();
                    if (cst == null)
                    {
                        XtraMessageBox.Show(
                            RTL.IsEnglish == true ? ResSLEn.MsgNoAccount : ResSLAr.MsgNoAccount
                            , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    dateTo = dateTo == Utilities.minDate ? Utilities.maxDate : dateTo;

                    rpt_Acc_AccountDetails rprt = new rpt_Acc_AccountDetails(RTL.IsEnglish == true ? ResSLEn.accDetail : ResSLAr.accDetail,
                    dateFilter, otherFilters,
                        dateFrom, dateTo, cst.Value, true);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_Acc_SL_AccountsBalances")
                {
                    rpt_Acc_AccountsBalances rprt = new rpt_Acc_AccountsBalances(reportCaption, dateFilter, otherFilters,
                        dateFrom, dateTo, true, FltrTyp_Customer, customerId1, customerId2);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_SL_CustomerItemsSales")
                {
                    rpt_SL_CustomerItemsSales rprt = new rpt_SL_CustomerItemsSales(reportCaption, dateFilter, otherFilters,
                        FltrTyp_Item, itemId1, itemId2, FltrTyp_Date, dateFrom, dateTo, FltrTyp_Customer, customerId1, customerId2);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_SL_SalesEmpCommission")
                {
                    rpt_SL_SalesEmpCommission rprt = new rpt_SL_SalesEmpCommission(reportCaption, dateFilter, otherFilters,
                        FltrTyp_Date, dateFrom, dateTo, salesEmpId);
                    if (rprt.UserCanOpen)
                        rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_SL_SalesCommision")
                {
                    if (lkpSalesEmp.EditValue == null || string.IsNullOrEmpty(lkpSalesEmp.EditValue.ToString().Trim()))
                    {
                        XtraMessageBox.Show(RTL.IsEnglish ? ResRptEn.valSalesEmp : ResRptAr.valSalesEmp,
                            "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        lkpSalesEmp.Focus();
                        return;
                    }

                    rpt_SL_SalesCommision rprt = new rpt_SL_SalesCommision(reportCaption, dateFilter, otherFilters,                        
                        FltrTyp_Date, dateFrom, dateTo,
                        salesEmpId, FltrTyp_Customer, customerId1, customerId2);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_SL_CustomerTrans")
                {
                    if (lkpCustomer1.EditValue == null || string.IsNullOrEmpty(lkpCustomer1.EditValue.ToString().Trim())
                         || lkpCustomer1.EditValue.ToString() == "0")
                    {
                        XtraMessageBox.Show(RTL.IsEnglish ? ResRptEn.ValCustomer : ResRptAr.ValCustomer,
                            "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        lkpStore1.Focus();
                        return;
                    }

                    rpt_SL_CustomerTrans rprt = new rpt_SL_CustomerTrans(reportCaption, dateFilter, otherFilters,
                        FltrTyp_Date, dateFrom, dateTo, customerId1);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_SL_DeliveryCommision")
                {
                    if (lkpSalesEmp.EditValue == null || string.IsNullOrEmpty(lkpSalesEmp.EditValue.ToString().Trim()))
                    {
                        XtraMessageBox.Show(RTL.IsEnglish ? ResRptEn.valSalesEmp : ResRptAr.valSalesEmp,
                            "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        lkpSalesEmp.Focus();
                        return;
                    }

                    rpt_SL_DeliveryCommision rprt = new rpt_SL_DeliveryCommision(reportCaption, dateFilter, otherFilters,
                        FltrTyp_Item, itemId1, itemId2,
                        FltrTyp_Date, dateFrom, dateTo, salesEmpId);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }

                #endregion

                #region ACC
                //else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_Acc_Income")
                //{
                //    if (dateTo == Utilities.minDate)
                //        dateTo = Utilities.maxDate;

                //    rpt_Acc_Income rprt = new rpt_Acc_Income(reportCaption, dateFilter, otherFilters,
                //        FltrTyp_Date, dateFrom, dateTo);
                //    if (rprt.UserCanOpen)
                //        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                //    //rprt.ShowPreview();
                //}
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_Acc_IncomeT")
                {
                    if (dateTo == Utilities.minDate)
                        dateTo = Utilities.maxDate;

                    rpt_Acc_IncomeT rprt = new rpt_Acc_IncomeT(reportCaption, dateFilter, otherFilters,
                        FltrTyp_Date, dateFrom, dateTo, costCenter);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_Acc_BalanceT")
                {
                    if (dateTo == Utilities.minDate)
                        dateTo = Utilities.maxDate;

                    rpt_Acc_BalanceT rprt = new rpt_Acc_BalanceT(reportCaption, dateFilter, otherFilters,
                        FltrTyp_Date, dateFrom, dateTo);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                //else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_Acc_Balance")
                //{
                //    if (dateTo == Utilities.minDate)
                //        dateTo = Utilities.maxDate;

                //    rpt_Acc_Balance rprt = new rpt_Acc_Balance(reportCaption, dateFilter, otherFilters,
                //        FltrTyp_Date, dateFrom, dateTo);
                //    if (rprt.UserCanOpen)
                //        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                //    //rprt.ShowPreview();
                //}
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_Acc_CostCenter_AccDetails")
                {
                    if (lkpCostCenters.EditValue == null || string.IsNullOrEmpty(lkpCostCenters.EditValue.ToString().Trim()))
                    {
                        XtraMessageBox.Show(RTL.IsEnglish ? ResRptEn.ValCostCenter : ResRptAr.ValCostCenter,
                            "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        lkpCostCenters.Focus();
                        return;
                    }

                    rpt_Acc_CostCenter_AccDetails rprt = new rpt_Acc_CostCenter_AccDetails
                        (reportCaption, dateFilter, otherFilters, dateFrom, dateTo, costCenter);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_Acc_Account_CostCenters")
                {
                    if (lkpCostCentersAccounts.EditValue == null || string.IsNullOrEmpty(lkpCostCentersAccounts.EditValue.ToString().Trim()))
                    {
                        XtraMessageBox.Show(RTL.IsEnglish ? ResRptEn.ValAccount : ResRptAr.ValAccount
                            , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        lkpCostCentersAccounts.Focus();
                        return;
                    }

                    rpt_Acc_Account_CostCenters rprt = new rpt_Acc_Account_CostCenters
                        (reportCaption, dateFilter, otherFilters, dateFrom, dateTo, costCenterAccount);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_Acc_CostCenterTotalBalances")
                {
                    rpt_Acc_CostCenterTotalBalances rprt = new rpt_Acc_CostCenterTotalBalances
                        (reportCaption, dateFilter, otherFilters, dateFrom, dateTo);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_Acc_CustomAccListDetails")
                {
                    if (lkpCustomList.EditValue == null || string.IsNullOrEmpty(lkpCustomList.EditValue.ToString().Trim()))
                    {
                        XtraMessageBox.Show(RTL.IsEnglish ? ResRptEn.ValCustomList : ResRptAr.ValCustomList
                            , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        lkpCustomList.Focus();
                        return;
                    }
                    rpt_Acc_CustomAccListDetails rprt = new rpt_Acc_CustomAccListDetails
                        (reportCaption, dateFilter, otherFilters, dateFrom, dateTo, customAccListId);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_Acc_CustomAccListBalances")
                {
                    rpt_Acc_CustomAccListBalances rprt = new rpt_Acc_CustomAccListBalances
                        (reportCaption, dateFilter, otherFilters, dateFrom, dateTo);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_Acc_DailyIncome")
                {
                    rpt_Acc_DailyIncome rprt = new rpt_Acc_DailyIncome
                        (reportCaption, dateFilter, otherFilters, FltrTyp_Date, dateFrom, dateTo);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt).ShowPreview();
                    //rprt.ShowPreview();
                }
                else if (((reportListItem)lstBxReports.SelectedItem).ReportName == "rpt_Acc_DailyPayments")
                {
                    rpt_Acc_DailyPayments rprt = new rpt_Acc_DailyPayments
                        (reportCaption, dateFilter, otherFilters, FltrTyp_Date, dateFrom, dateTo);
                    if (rprt.UserCanOpen)
                        new rpt_Template(reportCaption, dateFilter, otherFilters, rprt, true).ShowPreview();
                    //rprt.ShowPreview();
                }
                #endregion
            }
            */
        }

        void CreateFilterStrings(out string dateFilter, out string otherFilters)
        {
            dateFilter = otherFilters = "";
            /*
            reportListItem r =
            lstReportListItem.Find(x => x.ReportName == ((reportListItem)lstBxReports.SelectedItem).ReportName);

            otherFilters = "";
            dateFilter = "";

            if (r.DateFltr)
            {
                if (FltrTyp_Date == 1)
                {
                    dateFilter = (RTL.IsEnglish ? ResRptEn.FDate : ResRptAr.FDate) + dateFrom.ToShortDateString();
                }
                else if (FltrTyp_Date == 2)
                {
                    if (dateFrom != Utilities.minDate && dateTo != Utilities.minDate)
                        dateFilter = (RTL.IsEnglish ? ResRptEn.FFrom : ResRptAr.FFrom) + dateFrom.ToShortDateString()
                            + (RTL.IsEnglish ? ResRptEn.FTo : ResRptAr.FTo) + dateTo.ToShortDateString();
                    else if (dateFrom != Utilities.minDate && dateTo == Utilities.minDate)
                        dateFilter = (RTL.IsEnglish ? ResRptEn.FfromDate : ResRptAr.FfromDate) + dateFrom.ToShortDateString();
                    else if (dateFrom == Utilities.minDate && dateTo != Utilities.minDate)
                        dateFilter = (RTL.IsEnglish ? ResRptEn.FtoDate : ResRptAr.FtoDate) + dateTo.ToShortDateString();
                    else
                        dateFilter = "";
                }
                else
                {
                    dateFilter = string.Empty;
                }
            }

            if (r.ItemFltr)
            {
                if (FltrTyp_Item == 0)
                    otherFilters += (RTL.IsEnglish ? ResRptEn.FitemsAll : ResRptAr.FitemsAll) + " ; ";//"اسم الصنف: الكل"
                else if (FltrTyp_Item == 1)
                    otherFilters += (RTL.IsEnglish ? ResRptEn.FitemName : ResRptAr.FitemName) + lkpItem1.Text + " ; ";
            }
            if (r.StoreFltr)
            {
                if (FltrTyp_Store == 0)
                    otherFilters += (RTL.IsEnglish ? ResRptEn.FstoreAll : ResRptAr.FstoreAll) + " ; ";//"المخزن: الكل "
                else if (FltrTyp_Store == 1)
                    otherFilters += (RTL.IsEnglish ? ResRptEn.Fstore : ResRptAr.Fstore) + lkpStore1.Text + " ; ";
            }

            if (r.VendorFltr)
            {
                if (FltrTyp_Vendor == 0)
                    otherFilters += (RTL.IsEnglish ? ResRptEn.FvendorAll : ResRptAr.FvendorAll) + " ; ";
                else if (FltrTyp_Vendor == 1)
                    otherFilters += (RTL.IsEnglish ? ResRptEn.Fvendor : ResRptAr.Fvendor) + lkpVendor1.Text + " ; ";
            }

            if (r.CustomerFltr)
            {
                if (FltrTyp_Customer == 0)
                    otherFilters += (RTL.IsEnglish ? ResRptEn.FcustomerAll : ResRptAr.FcustomerAll) + " ; ";
                else if (FltrTyp_Customer == 1)
                    otherFilters += (RTL.IsEnglish ? ResRptEn.Fcustomer : ResRptAr.Fcustomer) + lkpCustomer1.Text + " ; ";
            }

            if (r.ComapnyFltr)
            {
                if (FltrTyp_Company == 0)
                    otherFilters += (RTL.IsEnglish ? ResRptEn.FcompAll : ResRptAr.FcompAll) + " ; ";
                else if (FltrTyp_Company == 1)
                    otherFilters += (RTL.IsEnglish ? ResRptEn.Fcomp : ResRptAr.Fcomp) + lkpCompany.Text + " ; ";
            }

            if (r.CategoryFltr)
            {
                if (FltrTyp_Category == 0)
                    otherFilters += (RTL.IsEnglish ? ResRptEn.FcatAll : ResRptAr.FcatAll) + " ; ";
                else if (FltrTyp_Category == 1)
                    otherFilters += (RTL.IsEnglish ? ResRptEn.Fcat : ResRptAr.Fcat) + lkpCategory.Text + " ; ";
            }

            if (r.ExpDateFltr)
            {
                if (dtExpDate.Visible == true)
                    otherFilters += (RTL.IsEnglish ? ResRptEn.FdateBefore : ResRptAr.FdateBefore)
                        + dtExpDate.DateTime.Month.ToString() + "/" + dtExpDate.DateTime.Year.ToString() + " ; ";
            }

            if (r.CostCenterFltr)
            {
                otherFilters += (RTL.IsEnglish ? ResRptEn.FcostCenter : ResRptAr.FcostCenter) + lkpCostCenters.Text + " ; ";
            }
            if (r.CostCenterAccountFltr)
            {
                otherFilters += (RTL.IsEnglish ? ResRptEn.Faccount : ResRptAr.Faccount) + lkpCostCentersAccounts.Text + " ; ";
            }
            if (r.CustmAccListFltr)
            {
                otherFilters += (RTL.IsEnglish ? ResRptEn.FcustomList : ResRptAr.FcustomList) + lkpCustomList.Text + " ; ";
            }

            if (r.UserFltr)
            {
                if (FltrTyp_User == 0)
                    otherFilters += (RTL.IsEnglish ? ResRptEn.FuserAll : ResRptAr.FuserAll) + " ; ";
                else if (FltrTyp_User == 1)
                    otherFilters += (RTL.IsEnglish ? ResRptEn.Fuser : ResRptAr.Fuser) + lkpUser.Text + " ; ";
            }

            if (r.SalesEmpFltr)
            {
                if (FltrTyp_SalesEmp == 0)
                    otherFilters += (RTL.IsEnglish ? ResRptEn.FsalesEmpAll : ResRptAr.FsalesEmpAll) + " ; ";
                else if (FltrTyp_SalesEmp == 1)
                    otherFilters += (RTL.IsEnglish ? ResRptEn.FsalesEmp : ResRptAr.FsalesEmp) + lkpSalesEmp.Text + " ; ";
            }

            if (r.BatchFltr)
            {
                if (!string.IsNullOrEmpty(txtBatch.Text))
                    otherFilters += (RTL.IsEnglish ? ResRptEn.Batch : ResRptAr.Batch) + txtBatch.Text + " ; ";
            }
            */
        }

        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }
        
        private void gridView1_CustomRowFilter(object sender, DevExpress.XtraGrid.Views.Base.RowFilterEventArgs e)
        {
            if (lstBxReports.SelectedItem == null)
            {
                e.Visible = false;
                e.Handled = true;
            }
            else
            {
                reportListItem rpt = ((reportListItem)lstBxReports.SelectedItem);
                if (dt_filters.Rows[e.ListSourceRow]["FilterName"].ToString() == "Item" && !rpt.ItemFltr)
                {
                    e.Visible = false;
                    e.Handled = true;
                }
                if (dt_filters.Rows[e.ListSourceRow]["FilterName"].ToString() == "Date" && !rpt.DateFltr)
                {
                    e.Visible = false;
                    e.Handled = true;
                }
                if (dt_filters.Rows[e.ListSourceRow]["FilterName"].ToString() == "ExpireDate" && !rpt.ExpDateFltr)
                {
                    e.Visible = false;
                    e.Handled = true;
                }
                if (dt_filters.Rows[e.ListSourceRow]["FilterName"].ToString() == "Store" && !rpt.StoreFltr)
                {
                    e.Visible = false;
                    e.Handled = true;
                }
                if (dt_filters.Rows[e.ListSourceRow]["FilterName"].ToString() == "Vendor" && !rpt.VendorFltr)
                {
                    e.Visible = false;
                    e.Handled = true;
                }
                if (dt_filters.Rows[e.ListSourceRow]["FilterName"].ToString() == "Customer" && !rpt.CustomerFltr)
                {
                    e.Visible = false;
                    e.Handled = true;
                }
                if (dt_filters.Rows[e.ListSourceRow]["FilterName"].ToString() == "ItemCompany" && !rpt.ComapnyFltr)
                {
                    e.Visible = false;
                    e.Handled = true;
                }
                if (dt_filters.Rows[e.ListSourceRow]["FilterName"].ToString() == "ItemCateogry" && !rpt.CategoryFltr)
                {
                    e.Visible = false;
                    e.Handled = true;
                }
                if (dt_filters.Rows[e.ListSourceRow]["FilterName"].ToString() == "Process" && !rpt.ProcessFltr)
                {
                    e.Visible = false;
                    e.Handled = true;
                }
                if (dt_filters.Rows[e.ListSourceRow]["FilterName"].ToString() == "CostCenter" && !rpt.CostCenterFltr)
                {
                    e.Visible = false;
                    e.Handled = true;
                }
                if (dt_filters.Rows[e.ListSourceRow]["FilterName"].ToString() == "Account" && !rpt.CostCenterAccountFltr)
                {
                    e.Visible = false;
                    e.Handled = true;
                }
                if (dt_filters.Rows[e.ListSourceRow]["FilterName"].ToString() == "CustomAccList" && !rpt.CustmAccListFltr)
                {
                    e.Visible = false;
                    e.Handled = true;
                }
                if (dt_filters.Rows[e.ListSourceRow]["FilterName"].ToString() == "User" && !rpt.UserFltr)
                {
                    e.Visible = false;
                    e.Handled = true;
                }
                if (dt_filters.Rows[e.ListSourceRow]["FilterName"].ToString() == "SalesEmp" && !rpt.SalesEmpFltr)
                {
                    e.Visible = false;
                    e.Handled = true;
                }
                if (dt_filters.Rows[e.ListSourceRow]["FilterName"].ToString() == "Batch" && !rpt.BatchFltr)
                {
                    e.Visible = false;
                    e.Handled = true;
                }
            }
        }

        private void gridView1_ShowingEditor(object sender, CancelEventArgs e)
        {
            if (gridView1.FocusedRowHandle < 0 || lstBxReports.SelectedItem == null)
                return;

            string reportName = ((reportListItem)lstBxReports.SelectedItem).ReportName;
            
            if (reportName == "rpt_IC_ItemTransactions" || reportName == "rpt_SL_ItemTrade")
            {                
                gridView1.SetRowCellValue(gridView1.GetRowHandle((int)FilterIndex.Item), col_FilterType, 1);
                gridView1.SetRowCellValue(gridView1.GetRowHandle((int)FilterIndex.Store), col_FilterType, 1);

                if (gridView1.FocusedColumn == col_FilterType && (gridView1.GetFocusedRowCellValue("FilterName").ToString() == "Item"
                    || gridView1.GetFocusedRowCellValue("FilterName").ToString() == "Store"))                
                    e.Cancel = true;                
            }
            else if (reportName == "rpt_Acc_PR_AccountDetails")// 
            {
                gridView1.SetRowCellValue(gridView1.GetRowHandle((int)FilterIndex.Vendor), col_FilterType, 1);
                gridView1.SetRowCellValue(gridView1.GetRowHandle((int)FilterIndex.Date), col_FilterType, 2);

                if (gridView1.FocusedColumn == col_FilterType && (gridView1.GetFocusedRowCellValue("FilterName").ToString() == "Vendor"
                    || gridView1.GetFocusedRowCellValue("FilterName").ToString() == "Date"))
                    e.Cancel = true;                                
            }
            else if (reportName == "rpt_Acc_SL_AccountDetails")
            {
                gridView1.SetRowCellValue(gridView1.GetRowHandle((int)FilterIndex.Customer), col_FilterType, 1);
                gridView1.SetRowCellValue(gridView1.GetRowHandle((int)FilterIndex.Date), col_FilterType, 2);

                if (gridView1.FocusedColumn == col_FilterType && (gridView1.GetFocusedRowCellValue("FilterName").ToString() == "Customer"
                    || gridView1.GetFocusedRowCellValue("FilterName").ToString() == "Date"))
                    e.Cancel = true;                                
            }
            else if (reportName == "rpt_IC_ItemTransactionsDetails")
            {
                gridView1.SetRowCellValue(gridView1.GetRowHandle((int)FilterIndex.Store), col_FilterType, 1);
                gridView1.SetRowCellValue(gridView1.GetRowHandle((int)FilterIndex.Date), col_FilterType, 2);

                if (gridView1.FocusedColumn == col_FilterType && (gridView1.GetFocusedRowCellValue("FilterName").ToString() == "Store"
                    || gridView1.GetFocusedRowCellValue("FilterName").ToString() == "FilterIndex"))
                    e.Cancel = true;                
                
            }
            else if (reportName == "rpt_SL_SalesEmpCommission")
            {
                gridView1.SetRowCellValue(gridView1.GetRowHandle((int)FilterIndex.SalesEmp), col_FilterType, 1);

                if (gridView1.FocusedColumn == col_FilterType && gridView1.GetFocusedRowCellValue("FilterName").ToString() == "SalesEmp")
                    e.Cancel = true;                
            }
            
            if (frmMain.st_Store.UserId > 0 && frmMain.st_Store.UserChangeStore == false)// user can't see other stores' data
            {
                gridView1.SetRowCellValue(gridView1.GetRowHandle((int)FilterIndex.Store), col_FilterType, 1);

                if (gridView1.FocusedColumn == col_FilterType && gridView1.GetFocusedRowCellValue("FilterName").ToString() == "Store")
                    e.Cancel = true;
            }

            if (((reportListItem)lstBxReports.SelectedItem).ProcessFltr == true)
            {
                //gridView1.SetRowCellValue(gridView1.GetRowHandle((int)FilterIndex.Process), col_FilterType, 1);

                //if (gridView1.FocusedColumn == col_FilterType && gridView1.GetFocusedRowCellValue("FilterName").ToString() == "Process")
                    //e.Cancel = true;
            }
            if (((reportListItem)lstBxReports.SelectedItem).CostCenterAccountFltr == true)
            {
                gridView1.SetRowCellValue(gridView1.GetRowHandle((int)FilterIndex.Account), col_FilterType, 1);

                if (gridView1.FocusedColumn == col_FilterType && gridView1.GetFocusedRowCellValue("FilterName").ToString() == "Account")
                    e.Cancel = true;
            }
            if (((reportListItem)lstBxReports.SelectedItem).CostCenterFltr == true)
            {
                gridView1.SetRowCellValue(gridView1.GetRowHandle((int)FilterIndex.CostCenter), col_FilterType, 1);

                if (gridView1.FocusedColumn == col_FilterType && gridView1.GetFocusedRowCellValue("FilterName").ToString() == "CostCenter")
                    e.Cancel = true;
            }
            if (((reportListItem)lstBxReports.SelectedItem).CustmAccListFltr == true)
            {
                gridView1.SetRowCellValue(gridView1.GetRowHandle((int)FilterIndex.CustomAccList), col_FilterType, 1);

                if (gridView1.FocusedColumn == col_FilterType && gridView1.GetFocusedRowCellValue("FilterName").ToString() == "CustomAccList")
                    e.Cancel = true;
            }
            if (((reportListItem)lstBxReports.SelectedItem).BatchFltr == true)
            {
                gridView1.SetRowCellValue(gridView1.GetRowHandle((int)FilterIndex.Batch), col_FilterType, 1);

                if (gridView1.FocusedColumn == col_FilterType && gridView1.GetFocusedRowCellValue("FilterName").ToString() == "Batch")
                    e.Cancel = true;
            }
        }

        private void gridView1_CustomRowCellEdit(object sender, DevExpress.XtraGrid.Views.Grid.CustomRowCellEditEventArgs e)
        {
            if (gridView1.GetRowHandle((int)FilterIndex.Account) == e.RowHandle)
            {
                if (e.Column == col_From)
                    e.RepositoryItem = rep_Account;                
            }
            if (gridView1.GetRowHandle((int)FilterIndex.Batch) == e.RowHandle)
            {
                if (e.Column == col_From)
                    e.RepositoryItem = rep_Batch;
            }
            if (gridView1.GetRowHandle((int)FilterIndex.CostCenter) == e.RowHandle)
            {
                if (e.Column == col_From)
                    e.RepositoryItem = rep_CostCenter;
            }
            if (gridView1.GetRowHandle((int)FilterIndex.CustomAccList) == e.RowHandle)
            {
                if (e.Column == col_From)
                    e.RepositoryItem = rep_CustomAccList;
            }
            if (gridView1.GetRowHandle((int)FilterIndex.Customer) == e.RowHandle)
            {
                if (e.Column == col_From)
                    e.RepositoryItem = rep_Customer1;
                else if (e.Column == col_To)
                    e.RepositoryItem = rep_Customer2;
            }
            if (gridView1.GetRowHandle((int)FilterIndex.ExpireDate) == e.RowHandle)
            {
                if (e.Column == col_From)
                    e.RepositoryItem = rep_Date;                
            }            

            if(gridView1.GetRowHandle((int)FilterIndex.Item)==e.RowHandle)
            {
                if (e.Column == col_From)
                    e.RepositoryItem = rep_Item1;
                else if (e.Column == col_To)
                    e.RepositoryItem = rep_Item2;
            }
            if (gridView1.GetRowHandle((int)FilterIndex.ItemCateogry) == e.RowHandle)
            {
                if (e.Column == col_From)
                    e.RepositoryItem = rep_ItemCateogry1;
                else if (e.Column == col_To)
                    e.RepositoryItem = rep_ItemCateogry2;
            }
            if (gridView1.GetRowHandle((int)FilterIndex.ItemCompany) == e.RowHandle)
            {
                if (e.Column == col_From)
                    e.RepositoryItem = rep_ItemCompany1;
                else if (e.Column == col_To)
                    e.RepositoryItem = rep_ItemCompany2;
            }

            if (gridView1.GetRowHandle((int)FilterIndex.Vendor) == e.RowHandle)
            {
                if (e.Column == col_From)
                    e.RepositoryItem = rep_Vendor1;
                else if (e.Column == col_To)
                    e.RepositoryItem = rep_Vendor2;
            }
            if (gridView1.GetRowHandle((int)FilterIndex.Store) == e.RowHandle)
            {
                if (e.Column == col_From)
                    e.RepositoryItem = rep_Store1;
                else if (e.Column == col_To)
                    e.RepositoryItem = rep_Store2;
            }
            if (gridView1.GetRowHandle((int)FilterIndex.Date) == e.RowHandle)
            {
                if (e.Column == col_From || e.Column==col_To)
                    e.RepositoryItem = rep_Date;                
            }
            if (gridView1.GetRowHandle((int)FilterIndex.User) == e.RowHandle)
            {
                if (e.Column == col_From )
                    e.RepositoryItem = rep_User;
            }
            if (gridView1.GetRowHandle((int)FilterIndex.Process) == e.RowHandle)
            {
                if (e.Column == col_From)
                    e.RepositoryItem = rep_process;
            }
            if (gridView1.GetRowHandle((int)FilterIndex.SalesEmp) == e.RowHandle)
            {
                if (e.Column == col_From)
                    e.RepositoryItem = rep_Emp;
            }

        }

        private void gridView1_CustomColumnDisplayText(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDisplayTextEventArgs e)
        {
            if (e.RowHandle >= 0 && (e.Column == col_From || e.Column == col_To))
            {
                if (gridView1.GetRowCellValue(e.RowHandle, "FilterName").ToString() == "Date"
                    && gridView1.GetRowCellValue(e.RowHandle, e.Column) != null
                    && gridView1.GetRowCellValue(e.RowHandle, e.Column) != DBNull.Value)
                    e.DisplayText = Convert.ToDateTime(e.Value).GetDateTimeFormats()[0];
            }
        }

        enum FilterIndex
        {
            Item = 0,
            Date = 1,
            ExpireDate = 2,
            Store = 3,
            Vendor = 4,
            Customer = 5,
            ItemCompany = 6,
            ItemCateogry = 7,
            Process = 8,
            CostCenter = 9,
            Account = 10,
            CustomAccList = 11,
            User = 12,
            SalesEmp = 13,
            Batch = 14,
        }
    }
    
}