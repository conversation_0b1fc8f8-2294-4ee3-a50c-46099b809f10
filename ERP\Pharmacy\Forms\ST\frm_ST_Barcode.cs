﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;
using DAL.Res;
using System.Linq;
using System.Drawing.Printing;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraReports.UI;

namespace Pharmacy.Forms
{
    public partial class frm_ST_Barcode : DevExpress.XtraEditors.XtraForm
    {
        int TmpltId;
        bool DataModified;

        public frm_ST_Barcode()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
        }

        private void frm_ST_Barcode_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            fill_cb_Papers();

            fill_chklst_Line(chklst_Line1);
            fill_chklst_Line(chklst_Line2);
            fill_chklst_Line(chklst_Line3);

        }

        private void frm_ST_Barcode_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                e.Cancel = true;
            else
                e.Cancel = false;
        }

        private void chklst_Line1_EditValueChanged(object sender, EventArgs e)
        {
            if (chklst_Line1.EditValue == null)
                return;

            preview_Line(1, chklst_Line1.EditValue.ToString());
        }

        private void chklst_Line2_EditValueChanged(object sender, EventArgs e)
        {
            if (chklst_Line2.EditValue == null)
                return;

            preview_Line(2, chklst_Line2.EditValue.ToString());
        }

        private void chklst_Line3_EditValueChanged(object sender, EventArgs e)
        {
            if (chklst_Line3.EditValue == null)
                return;

            preview_Line(3, chklst_Line3.EditValue.ToString());
        }

        private void barBtn_Cancel_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_New_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            clear_controls();
        }

        private void barBtn_Save_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (TmpltId == 0)
                Save_New_Template();
            else
                Update_Template();
        }

        private void barBtn_Preview_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (TmpltId == 0)
                return;

            ERPDataContext DB = new ERPDataContext();
            ST_BarcodeTemplate template = DB.ST_BarcodeTemplates.Where(x => x.Template_Id == TmpltId).FirstOrDefault();

            if (template != null)
            {
                DataTable dt_Items_Qty = new DataTable();
                dt_Items_Qty.Columns.Add("ItemId");
                dt_Items_Qty.Columns.Add("ItemCode1");
                dt_Items_Qty.Columns.Add("ItemCode2");
                dt_Items_Qty.Columns.Add("SellPrice");
                dt_Items_Qty.Columns.Add("VenCode");
                dt_Items_Qty.Columns.Add("Qty");
                dt_Items_Qty.Columns.Add("PurchaseInvNo");
                dt_Items_Qty.Columns.Add("ExpireDate");
                dt_Items_Qty.Columns.Add("PR_InvoiceDetailId");
                dt_Items_Qty.Columns.Add("Batch");

                dt_Items_Qty.Rows.Add("Item1", 1, "Code2", "Price", "Vendor", template.RowsCount * template.ColumnsCount, "PInv001", "7-2020", "1", "Batch");
                new Reports.rpt_barcodeLabels(dt_Items_Qty, 1, template, Shared.st_Store.PrintBarcodePerInventory).ShowPreview();
            }
        }


        private void Update_Template()
        {
            ERPDataContext DB = new ERPDataContext();
            try
            {
                var template_Data = (from t in DB.ST_BarcodeTemplates
                                     where t.Template_Id == TmpltId
                                     select t).FirstOrDefault();

                template_Data.Template_Name = txt_TemplateName.Text;
                template_Data.RowsCount = Convert.ToByte(txtRowsCount.Text);
                template_Data.ColumnsCount = Convert.ToByte(txtColumnsCount.Text);
                template_Data.Currency = txtCurrency.Text;
                template_Data.QtyPrefix = txt_QtyPrefix.Text;
                template_Data.BatchPrefix = txt_BatchPrefix.Text;

                template_Data.PageName = cb_Papers.Text;
                template_Data.PaperHeight = Convert.ToInt32(txt_Paperheight.EditValue);
                template_Data.PaperWidth = Convert.ToInt32(txt_Paperwidth.EditValue);

                template_Data.Line_1 = chklst_Line1.EditValue.ToString();
                template_Data.Line_2 = chklst_Line2.EditValue.ToString();
                template_Data.Line_3 = chklst_Line3.EditValue.ToString();

                template_Data.Line_1_FontSize = Convert.ToDouble(spn_1.EditValue);
                template_Data.Line_1_Visible = chk_1.Checked;
                template_Data.Line_2_FontSize = Convert.ToDouble(spn_2.EditValue);
                template_Data.Line_2_Visible = chk_2.Checked;
                template_Data.Line_3_FontSize = Convert.ToDouble(spn_3.EditValue);
                template_Data.Line_3_Visible = chk_3.Checked;
                template_Data.Line_4_FontSize = Convert.ToDouble(spn_4.EditValue);
                template_Data.Line_4_Visible = chk_4.Checked;

                template_Data.ShowBacrodeText = chk_ShowBacrodeText.Checked;
                template_Data.PrintBatchinBarcode = chk_PrintBatchNo.Checked;

                template_Data.Margins_Top = Convert.ToInt32(txt_MarginTop.EditValue);
                template_Data.Margins_Bottom = Convert.ToInt32(txt_MarginBottom.EditValue);
                template_Data.Margins_Left = Convert.ToInt32(txt_MarginLeft.EditValue);
                template_Data.Margins_Right = Convert.ToInt32(txt_MarginRight.EditValue);

                template_Data.Font = cb_Fonts.EditValue.ToString();

                if (chkIsDefaultTemplate.Checked == true)
                {
                    template_Data.IsDefault = true;
                    var templts = from t in DB.ST_BarcodeTemplates
                                  where t.IsDefault == true
                                  && t.Template_Id != TmpltId
                                  select t;
                    foreach (var t in templts)
                    {
                        t.IsDefault = false;
                    }
                }
                else
                    template_Data.IsDefault = false;

                MyHelper.UpdateST_UserLog(DB, "", txt_TemplateName.Text,
(int)FormAction.Edit, (int)FormsNames.ST_Barcode);

                DB.SubmitChanges();

                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResAccEn.MsgSave : ResAccAr.MsgSave//"تم الحفظ بنجاح"
                    , "", MessageBoxButtons.OK, MessageBoxIcon.Information);

                DoValidate();
                DataModified = false;
            }
            catch
            {
                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResAccEn.MsgIncorrectData : ResAccAr.MsgIncorrectData//"تأكد من صحة البيانات"
                    , "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void Save_New_Template()
        {
            ERPDataContext DB = new ERPDataContext();
            try
            {
                var template_Data = new ST_BarcodeTemplate();

                template_Data.Template_Name = txt_TemplateName.Text;
                template_Data.RowsCount = Convert.ToByte(txtRowsCount.Text);
                template_Data.ColumnsCount = Convert.ToByte(txtColumnsCount.Text);
                template_Data.Currency = txtCurrency.Text;
                template_Data.QtyPrefix = txt_QtyPrefix.Text;
                template_Data.BatchPrefix = txt_BatchPrefix.Text;

                template_Data.PageName = cb_Papers.Text;
                template_Data.PaperHeight = Convert.ToInt32(txt_Paperheight.EditValue);
                template_Data.PaperWidth = Convert.ToInt32(txt_Paperwidth.EditValue);

                template_Data.Line_1 = chklst_Line1.EditValue.ToString();
                template_Data.Line_2 = chklst_Line2.EditValue.ToString();
                template_Data.Line_3 = chklst_Line3.EditValue.ToString();

                template_Data.Line_1_FontSize = Convert.ToDouble(spn_1.EditValue);
                template_Data.Line_1_Visible = chk_1.Checked;
                template_Data.Line_2_FontSize = Convert.ToDouble(spn_2.EditValue);
                template_Data.Line_2_Visible = chk_2.Checked;
                template_Data.Line_3_FontSize = Convert.ToDouble(spn_3.EditValue);
                template_Data.Line_3_Visible = chk_3.Checked;
                template_Data.Line_4_FontSize = Convert.ToDouble(spn_4.EditValue);
                template_Data.Line_4_Visible = chk_4.Checked;

                template_Data.ShowBacrodeText = chk_ShowBacrodeText.Checked;
                template_Data.PrintBatchinBarcode = chk_PrintBatchNo.Checked;
 
                template_Data.Margins_Top = Convert.ToInt32(txt_MarginTop.EditValue);
                template_Data.Margins_Bottom = Convert.ToInt32(txt_MarginBottom.EditValue);
                template_Data.Margins_Left = Convert.ToInt32(txt_MarginLeft.EditValue);
                template_Data.Margins_Right = Convert.ToInt32(txt_MarginRight.EditValue);

                template_Data.Font = cb_Fonts.EditValue.ToString();

                if (chkIsDefaultTemplate.Checked == true)
                {
                    template_Data.IsDefault = true;
                    var templts = from t in DB.ST_BarcodeTemplates
                                  where t.IsDefault == true
                                  && t.Template_Id != TmpltId
                                  select t;
                    foreach (var t in templts)
                    {
                        t.IsDefault = false;
                    }
                }

                DB.ST_BarcodeTemplates.InsertOnSubmit(template_Data);

                MyHelper.UpdateST_UserLog(DB, "", txt_TemplateName.Text,
(int)FormAction.Add, (int)FormsNames.ST_Barcode);

                DB.SubmitChanges();

                XtraMessageBox.Show(Shared.IsEnglish == true ? ResAccEn.MsgSave : ResAccAr.MsgSave//"تم الحفظ بنجاح"
                    , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                TmpltId = template_Data.Template_Id;

                DoValidate();
                DataModified = false;
            }
            catch
            {
                XtraMessageBox.Show(Shared.IsEnglish == true ? ResAccEn.MsgIncorrectData : ResAccAr.MsgIncorrectData//"تأكد من صحة البيانات"
                    , Shared.IsEnglish == true ? ResAccEn.MsgTError : ResAccAr.MsgTError//"خطأ"
                    , MessageBoxButtons.OK, MessageBoxIcon.Information);
                TmpltId = 0;
            }
        }


        private void spn_1_EditValueChanged(object sender, EventArgs e)
        {
            float fsize = Convert.ToInt32(spn_1.EditValue);
            lbl_Line_1.Font = ChangeFontSize(lbl_Line_1.Font, fsize);
        }

        private void spn_2_EditValueChanged(object sender, EventArgs e)
        {
            float fsize = Convert.ToInt32(spn_2.EditValue);
            lbl_line_2.Font = ChangeFontSize(lbl_line_2.Font, fsize);
        }

        private void spn_3_EditValueChanged(object sender, EventArgs e)
        {
            float fsize = Convert.ToInt32(spn_3.EditValue);
            Lbl_Line_3.Font = ChangeFontSize(Lbl_Line_3.Font, fsize);
        }

        private void spn_4_EditValueChanged(object sender, EventArgs e)
        {
            float fsize = Convert.ToInt32(spn_4.EditValue);
            lbl_Line_4.Font = ChangeFontSize(lbl_Line_4.Font, fsize);
        }

        private Font ChangeFontSize(Font font, float fontSize)
        {
            if (font != null && fontSize > 0)
            {
                float currentSize = font.Size;
                if (currentSize != fontSize)
                {
                    font = new Font(font.Name, fontSize,
                        font.Style, font.Unit,
                        font.GdiCharSet, font.GdiVerticalFont);
                }
            }
            return font;
        }

        private void chk_1_CheckedChanged(object sender, EventArgs e)
        {
            if (chk_1.Checked == true)
                lbl_Line_1.Visible = true;
            else
                lbl_Line_1.Visible = false;
            Refresh_Labels_Locations();
        }

        private void chk_2_CheckedChanged(object sender, EventArgs e)
        {
            if (chk_2.Checked == true)
                lbl_line_2.Visible = true;
            else
                lbl_line_2.Visible = false;
            Refresh_Labels_Locations();
        }

        private void chk_3_CheckedChanged(object sender, EventArgs e)
        {
            if (chk_3.Checked == true)
                Lbl_Line_3.Visible = true;
            else
                Lbl_Line_3.Visible = false;
            Refresh_Labels_Locations();

        }

        private void chk_4_CheckedChanged(object sender, EventArgs e)
        {
            if (chk_4.Checked == true)
                lbl_Line_4.Visible = true;
            else
                lbl_Line_4.Visible = false;
            Refresh_Labels_Locations();
        }

        private void lbl_Line_1_Resize(object sender, EventArgs e)
        {
            Refresh_Labels_Locations();
        }


        private void cb_Papers_EditValueChanged(object sender, EventArgs e)
        {
            txt_Paperwidth.EditValue = 0;
            txt_Paperheight.EditValue = 0;

            if (cb_Papers.Text != "Custom")
            {
                txt_Paperwidth.Enabled = false;
                txt_Paperheight.Enabled = false;
            }
            else
            {
                txt_Paperwidth.Enabled = true;
                txt_Paperheight.Enabled = true;
            }
        }

        private void txt_TemplateName_Modified(object sender, EventArgs e)
        {
            DataModified = true;
        }


        private void fill_cb_Papers()
        {
            Array arr_paperKinds = Enum.GetValues(typeof(PaperKind));

            foreach (int id in arr_paperKinds)
            {
                cb_Papers.Properties.Items.Add(new DevExpress.XtraEditors.Controls.ImageComboBoxItem
                (Enum.GetName(typeof(PaperKind), id), Enum.GetName(typeof(PaperKind), id), -1));
            }

            cb_Papers.EditValue = "A4";
        }

        private void fill_chklst_Line(CheckedComboBoxEdit chklst)
        {
            chklst.Properties.Items.Add(1, Shared.IsEnglish == true ? ResAccEn.st_CompName : ResAccAr.st_CompName);//"اسم المنشأه"
            chklst.Properties.Items.Add(2, Shared.IsEnglish == true ? ResAccEn.st_CompTel : ResAccAr.st_CompTel);//"تليفون المنشأه"
            chklst.Properties.Items.Add(3, Shared.IsEnglish == true ? ResAccEn.st_ItemName : ResAccAr.st_ItemName);//"اسم الصنف"
            chklst.Properties.Items.Add(4, Shared.IsEnglish == true ? ResAccEn.st_ItemCode : ResAccAr.st_ItemCode);//"كود الصنف"
            chklst.Properties.Items.Add(5, Shared.IsEnglish == true ? ResAccEn.st_ItemPrice : ResAccAr.st_ItemPrice);//"سعر الصنف"
            chklst.Properties.Items.Add(6, Shared.IsEnglish == true ? ResAccEn.st_vendorCode : ResAccAr.st_vendorCode);//"كود المورد"
            chklst.Properties.Items.Add(7, Shared.IsEnglish == true ? ResAccEn.st_PurchaseInvoice : ResAccAr.st_PurchaseInvoice);//"رقم فاتورة المشتريات"
            chklst.Properties.Items.Add(8, Shared.IsEnglish == true ? ResAccEn.ExpireDate : ResAccAr.ExpireDate);//"رقم فاتورة المشتريات"
            chklst.Properties.Items.Add(9, Shared.IsEnglish == true ? ResAccEn.st_ItemCode2 : ResAccAr.st_ItemCode2);//"رقم فاتورة المشتريات"
            chklst.Properties.Items.Add(10, Shared.IsEnglish == true ? ResAccEn.st_Batch : ResAccAr.st_Batch);
            chklst.Properties.Items.Add(11, Shared.IsEnglish == true ? ResAccEn.st_Qty : ResAccAr.st_Qty);
        }

        private void preview_Line(int lineNumber, string editvalue)
        {
            List<int> ids = new List<int>();
            if (string.IsNullOrEmpty(editvalue.Trim()) == true)
                ids.Clear();
            else
                foreach (var s in editvalue.Split(','))
                    ids.Add(Convert.ToInt32(s));

            switch (lineNumber)
            {
                case 1:
                    lbl_Line_1.Text = "";
                    foreach (int x in ids)
                        lbl_Line_1.Text += Get_previewed_Text(x) + "    ";
                    break;
                case 2:
                    lbl_line_2.Text = "";
                    foreach (int x in ids)
                        lbl_line_2.Text += Get_previewed_Text(x) + "    ";
                    break;
                case 3:
                    Lbl_Line_3.Text = "";
                    foreach (int x in ids)
                        Lbl_Line_3.Text += Get_previewed_Text(x) + "    ";
                    break;
            }
        }

        private string Get_previewed_Text(int id)
        {
            switch (id)
            {
                case 1:
                    return Shared.CompName;
                case 2:
                    return "\t" + "ت: " + new ERPDataContext().ST_CompanyInfos.FirstOrDefault().CmpTel;
                case 3:
                    return "صنف تجريبي ";
                case 4:
                    return "كود1";
                case 5:
                    return "6.5" + txtCurrency.Text;
                case 6:
                    return "كود المورد";
                case 7:
                    return "رقم فاتورة";
                case 8:
                    return "50";
                case 9:
                    return "A-2020";
                case 10:
                    return txt_BatchPrefix.Text+ "112";
                case 11:
                    return txt_QtyPrefix.Text+ "120.5";
                default:
                    return "";
            }
        }

        private void Get_Template_Data(int temp_id)
        {
            ERPDataContext DB = new ERPDataContext();
            var template_Data = (from t in DB.ST_BarcodeTemplates
                                 where t.Template_Id == temp_id
                                 select t).FirstOrDefault();

            /*empty_or_new_template*/
            if (temp_id == 0 || template_Data == null)
                clear_controls();
            else
            {
                TmpltId = temp_id;
                txt_TemplateName.Text = template_Data.Template_Name;
                txtRowsCount.Text = template_Data.RowsCount.ToString();
                txtColumnsCount.Text = template_Data.ColumnsCount.ToString();
                txtCurrency.Text = template_Data.Currency;
                txt_QtyPrefix.Text = template_Data.QtyPrefix;
                txt_BatchPrefix.Text = template_Data.BatchPrefix;
                cb_Papers.EditValue = template_Data.PageName;
                txt_Paperheight.EditValue = template_Data.PaperHeight;
                txt_Paperwidth.EditValue = template_Data.PaperWidth;

                chklst_Line1.EditValue = template_Data.Line_1;
                chklst_Line1.RefreshEditValue();
                chklst_Line2.EditValue = template_Data.Line_2;
                chklst_Line2.RefreshEditValue();
                chklst_Line3.EditValue = template_Data.Line_3;
                chklst_Line3.RefreshEditValue();

                cb_Fonts.EditValue = template_Data.Font;

                spn_1.EditValue = template_Data.Line_1_FontSize;
                spn_2.EditValue = template_Data.Line_2_FontSize;
                spn_3.EditValue = template_Data.Line_3_FontSize;
                spn_4.EditValue = template_Data.Line_4_FontSize;

                chk_1.Checked = template_Data.Line_1_Visible;
                chk_2.Checked = template_Data.Line_2_Visible;
                chk_3.Checked = template_Data.Line_3_Visible;
                chk_4.Checked = template_Data.Line_4_Visible;

                chk_ShowBacrodeText.Checked = template_Data.ShowBacrodeText;
                chk_PrintBatchNo.Checked = template_Data.PrintBatchinBarcode.GetValueOrDefault(false);

                txt_MarginTop.EditValue = template_Data.Margins_Top;
                txt_MarginBottom.EditValue = template_Data.Margins_Bottom;
                txt_MarginLeft.EditValue = template_Data.Margins_Left;
                txt_MarginRight.EditValue = template_Data.Margins_Right;

                chkIsDefaultTemplate.Checked = template_Data.IsDefault;
                DoValidate();
                DataModified = false;
            }
        }

        private void clear_controls()
        {
            TmpltId = 0;
            txt_TemplateName.Text = "";
            txtRowsCount.Text = "";
            txtColumnsCount.Text = "";
            txtCurrency.Text = txt_BatchPrefix.Text = txt_QtyPrefix.Text = string.Empty;

            chklst_Line1.EditValue = null;
            chklst_Line1.RefreshEditValue();
            chklst_Line2.EditValue = null;
            chklst_Line2.RefreshEditValue();
            chklst_Line3.EditValue = null;
            chklst_Line3.RefreshEditValue();

            spn_1.EditValue = 12;
            spn_4.EditValue = 12;
            spn_2.EditValue = 12;
            spn_3.EditValue = 12;

            chk_1.Checked = true;
            chk_4.Checked = true;
            chk_2.Checked = true;
            chk_3.Checked = true;

            txt_MarginBottom.EditValue = 0;
            txt_MarginTop.EditValue = 0;
            txt_MarginLeft.EditValue = 0;
            txt_MarginRight.EditValue = 0;

            TmpltId = 0;
        }

        private void Refresh_Labels_Locations()
        {
            int temp = 0;
            lbl_Line_1.Location = new Point(0, 0);

            if (lbl_Line_1.Visible == true)
                temp = lbl_Line_1.Location.Y + lbl_Line_1.Size.Height + 2;
            lbl_line_2.Location = new Point(0, temp);

            if (lbl_line_2.Visible == true)
                temp = lbl_line_2.Location.Y + lbl_line_2.Size.Height + 2;
            Lbl_Line_3.Location = new Point(0, temp);

            if (Lbl_Line_3.Visible == true)
                temp = Lbl_Line_3.Location.Y + Lbl_Line_3.Size.Height + 2;
            lbl_Line_4.Location = new Point(0, temp);
        }

        DialogResult ChangesMade()
        {
            if (DataModified)
            {
                DialogResult r = XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResAccEn.MsgDataModified : ResAccAr.MsgDataModified//"لقد قمت بعمل بعض التغييرات, هل تريد الحفظ أولا "
                    , "", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                if (r == DialogResult.Yes)
                {
                    barBtnSave.PerformClick();
                    return r;
                }
                else if (r == DialogResult.No)
                {
                    // no thing made, continue closing or do next or do previous
                    return r;
                }
                else if (r == DialogResult.Cancel)
                {
                    //cancel form action
                    return r;
                }
            }
            return DialogResult.No;
        }

        void DoValidate()
        {
            txt_Line_4.DoValidate();
            txt_Paperheight.DoValidate();
            txt_Paperwidth.DoValidate();
            txt_TemplateName.DoValidate();
            txtColumnsCount.DoValidate();
            txtCurrency.DoValidate();
            txt_QtyPrefix.DoValidate();
            txt_BatchPrefix.DoValidate();
            txtRowsCount.DoValidate();
            cb_Papers.DoValidate();

            spn_1.DoValidate();
            spn_2.DoValidate();
            spn_3.DoValidate();
            spn_4.DoValidate();
            chk_1.DoValidate();
            chk_2.DoValidate();
            chk_3.DoValidate();
            chk_4.DoValidate();

            chkIsDefaultTemplate.DoValidate();

            chklst_Line1.DoValidate();
            chklst_Line2.DoValidate();
            chklst_Line3.DoValidate();

            txt_MarginBottom.DoValidate();
            txt_MarginTop.DoValidate();
            txt_MarginLeft.DoValidate();
            txt_MarginRight.DoValidate();
        }

        private void barBtnHelp_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "إعدادات الباركود");
        }

        private void frm_ST_Barcode_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.PageUp)
            {
                btnPrev.PerformClick();
            }
            if (e.KeyCode == Keys.PageDown)
            {
                btnNext.PerformClick();
            }
        }

        private void btnNext_Click(object sender, EventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            ERPDataContext DB = new ERPDataContext();
            int lasttempId = (from t in DB.ST_BarcodeTemplates
                              where t.Template_Id > TmpltId
                              orderby t.Template_Id ascending
                              select t.Template_Id).FirstOrDefault();

            if (lasttempId != 0)
            {
                TmpltId = lasttempId;
                Get_Template_Data(TmpltId);
            }
            else
            {
                lasttempId = (from t in DB.ST_BarcodeTemplates
                              orderby t.Template_Id ascending
                              select t.Template_Id).FirstOrDefault();

                if (lasttempId != 0)
                {
                    TmpltId = lasttempId;
                    Get_Template_Data(TmpltId);
                }
            }
        }

        private void btnPrev_Click(object sender, EventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            ERPDataContext DB = new ERPDataContext();
            int lasttempId = (from t in DB.ST_BarcodeTemplates
                              where t.Template_Id < TmpltId
                              orderby t.Template_Id descending
                              select t.Template_Id).FirstOrDefault();

            if (lasttempId != 0)
            {
                TmpltId = lasttempId;
                Get_Template_Data(TmpltId);
            }
            else
            {
                lasttempId = (from t in DB.ST_BarcodeTemplates
                              orderby t.Template_Id descending
                              select t.Template_Id).FirstOrDefault();

                if (lasttempId != 0)
                {
                    TmpltId = lasttempId;
                    Get_Template_Data(TmpltId);
                }
            }
        }

        private void cb_Fonts_SelectedIndexChanged(object sender, EventArgs e)
        {
            lbl_Line_1.Font = new Font(cb_Fonts.EditValue.ToString(), Convert.ToInt32(spn_1.EditValue));
            lbl_line_2.Font = new Font(cb_Fonts.EditValue.ToString(), Convert.ToInt32(spn_2.EditValue));
            Lbl_Line_3.Font = new Font(cb_Fonts.EditValue.ToString(), Convert.ToInt32(spn_3.EditValue));
        }

        private void txt_QtyPrefix_EditValueChanged(object sender, EventArgs e)
        {

        }
    }
}
