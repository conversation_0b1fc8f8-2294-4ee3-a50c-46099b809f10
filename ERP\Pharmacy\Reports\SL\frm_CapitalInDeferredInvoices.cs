﻿using DAL;
using DAL.Res;
using DevExpress.XtraEditors;
using DevExpress.XtraReports.UI;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Reports.SL
{
    public partial class frm_CapitalInDeferredInvoices : DevExpress.XtraEditors.XtraForm
    {
        public bool UserCanOpen;

        string reportName, dateFilter, otherFilters;

        int customerId1, customerId2;
        //, itemId1, itemId2,  custGroupId,
        //storeId1, storeId2, salesEmpId;
        byte fltrTyp_Date, FltrTyp_Customer;
            //, FltrTyp_item,  FltrTyp_Category,
            //FltrTyp_Store, FltrTyp_InvBook;

        private void gridView1_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            if (e.Column.FieldName == "Index")
                e.Value = e.RowHandle() + 1;
        }

     //   string categoryNum;
        DateTime date1, date2;

        private void barButtonItem5_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grd_data.MinimumSize = grd_data.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grd_data, true).ShowPreview();
            grd_data.MinimumSize = new Size(0, 0);
        }

        private void barBtn_PreviewData_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grd_data.MinimumSize = grd_data.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grd_data, true, true).ShowPreview();
                grd_data.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        //    string custGroupAccNumber;
        //List<int> lstStores = new List<int>();
        //List<int> lst_invBooksId = new List<int>();
        // public int count;

        // byte FltrTyp_Company;

        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

      //  int companyId;

        public frm_CapitalInDeferredInvoices(string reportName, string dateFilter, string otherFilters,
            //byte fltrTyp_item, int itemId1, int itemId2,
            byte fltrTyp_Date, DateTime date1, DateTime date2,
            byte FltrTyp_Customer, int customerId1, int customerId2
            //,byte FltrTyp_Category, string categoryNum,
            //int custGroupId, string custGroupAccNumber,
            //byte FltrTyp_Store, int storeId1, int storeId2,
            //int salesEmpId,
            //byte FltrTyp_InvBook, string InvBooks, byte FltrTyp_Company, int companyId
            )
        {
            UserCanOpen = LoadPrivilege();
            if (UserCanOpen == false)
                return;

            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();

            this.reportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;

            //this.FltrTyp_Company = FltrTyp_Company;
            //this.companyId = companyId;

            //this.FltrTyp_item = fltrTyp_item;
            this.fltrTyp_Date = fltrTyp_Date;
            this.FltrTyp_Customer = FltrTyp_Customer;
            //this.FltrTyp_Category = FltrTyp_Category;
            //this.FltrTyp_Store = FltrTyp_Store;

            //this.itemId1 = itemId1;
            //this.itemId2 = itemId2;

            this.date1 = date1;
            this.date2 = date2;
            if (date2 == Shared.minDate)
                this.date2 = Shared.maxDate;

            this.customerId1 = customerId1;
            this.customerId2 = customerId2;
            
            
            //this.categoryNum = categoryNum;

            //this.custGroupId = custGroupId;
            //this.custGroupAccNumber = custGroupAccNumber;
            //this.storeId1 = storeId1;
            //this.storeId2 = storeId2;
            //this.salesEmpId = salesEmpId;

            //this.FltrTyp_InvBook = FltrTyp_InvBook;
            //Utilities.Get_ChkLst_Items(InvBooks, lst_invBooksId);
            getReportHeader();
        }

        private void frm_CapitalInDeferredInvoices_Load(object sender, EventArgs e)
        {
            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;
            ERPDataContext DB = new DAL.ERPDataContext();
            #region one
            //var data = (from i in DB.SL_Invoices
            //                join d in DB.SL_InvoiceDetails on i.SL_InvoiceId equals d.SL_InvoiceId
            //                join m in DB.IC_ItemStores on d.ItemId equals m.ItemId
            //               // join m in DB.IC_ItemStores on d.SL_InvoiceDetailId equals m.SourceId
            //              //  where m.ProcessId==(int)Process.SellInvoice
            //                where i.PayMethod == false || i.PayMethod == null

            //                #region
            //                //where fltrTyp_Date == 1 ? i.InvoiceDate.Date == date1.Date : true
            //                //where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
            //                //i.InvoiceDate.Date >= date1.Date && i.InvoiceDate.Date <= date2.Date : true
            //                //where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
            //                //i.InvoiceDate.Date >= date1.Date : true
            //                //where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
            //                //i.InvoiceDate.Date <= date2.Date : true



            //                //where FltrTyp_Customer == 1 ? i.CustomerId == customerId1 : true
            //                //where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 != 0) ?
            //                //i.CustomerId >= customerId1 && i.CustomerId <= customerId2 : true
            //                //where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 == 0) ?
            //                //i.CustomerId >= customerId1 : true
            //                //where (FltrTyp_Customer == 2 && customerId1 == 0 && customerId2 != 0) ?
            //                //i.CustomerId <= customerId2 : true
            //                #endregion
            //                //update

            //                group new { i, d, m } by i.SL_InvoiceId into grp
            //                //  join p in DB.IC_ItemPriceChanges.DefaultIfEmpty() on grp.Select(g => g.d.ItemId).FirstOrDefault() equals p.ItemId into xxx

            //                let CashPaid = from c in DB.ACC_CashNotes.Where(x => x.SourceId == grp.Select(g => g.i.SL_InvoiceId).FirstOrDefault()).DefaultIfEmpty()
            //                               where c.IsPay == false && c.ProcessId == (int)Process.SellInvoice
            //                               select new { c.SourceId, c.Amount }
            //                let NotePaid = from i3 in DB.ACC_NotesReceivables.Where(x => x.SourceId == grp.Select(g => g.i.SL_InvoiceId).FirstOrDefault()).DefaultIfEmpty()
            //                               where i3.ResponseType == 3 || i3.ResponseType == 4
            //                               select new { i3.SourceId, i3.Amount }

            //                let Net = grp.Select(g => g.i.Net).FirstOrDefault()
            //                let cash = CashPaid.Count() > 0 ? CashPaid.Sum(x => x.Amount) : 0
            //                let note = NotePaid.Count() > 0 ? NotePaid.Sum(x => x.Amount) : 0
            //                let IsPaid = grp.Select(g => g.i.PayMethod).FirstOrDefault() == true ? true : cash + note + grp.Select(g => g.i.Paid).FirstOrDefault() >= Net
            //                #region
            //           

            //                //let BuyPrice = grp.Sum(g => DB.IC_ItemPriceChanges.SingleOrDefault(p => p.ItemId == g.d.ItemId) == null
            //                // ? g.t.PurchasePrice * g.d.Qty :
            //                // ((DB.IC_ItemPriceChanges.Where(p => p.ItemId == g.d.ItemId).Select(p => p.OldPPrice).Sum() + DB.IC_ItemPriceChanges.Where(p => p.ItemId == g.d.ItemId).Select(p => p.NewPPrice).Sum()) / (DB.IC_ItemPriceChanges.Where(p => p.ItemId == g.d.ItemId).Count() * 2)) * g.d.Qty)
            //                #endregion
            //                let BuyPrice = grp.Sum(g => g.m.SourceId == g.d.SL_InvoiceDetailId && g.m.ProcessId == (int)Process.SellInvoice ? g.m.PurchasePrice :0)
            //                // from xx in xxx.DefaultIfEmpty()
            //                where IsPaid == false && /*where*/ BuyPrice > 0
            //                select new
            //                {


            //                    #region test
            //                    //SellPrice = igroup.Select(g => g.d.SellPrice).FirstOrDefault(),

            //                    //Paid = /*cash + note + igroup.Select(g => g.i.Paid).FirstOrDefault() / DB.SL_InvoiceDetails.Where(n => n.SL_InvoiceId == igroup.Select(g => g.i.SL_InvoiceId).FirstOrDefault()).Count(),*/ igroup.Select(g => g.i.Paid).FirstOrDefault() / DB.SL_InvoiceDetails.Where(n => n.SL_InvoiceId == igroup.Select(g => g.i.SL_InvoiceId).FirstOrDefault()).Count(),

            //                    //x= DB.SL_InvoiceDetails.Where(n => n.SL_InvoiceId == igroup.Select(g => g.i.SL_InvoiceId).FirstOrDefault()).Count(),
            //                    //Item = DB.IC_Items.SingleOrDefault(t => t.ItemId == igroup.Key.ItemId).ItemNameAr,
            //                    //BuyPrice = DB.IC_ItemPriceChanges.SingleOrDefault(p => p.ItemId == igroup.Key.ItemId) == null ? DB.IC_Items.SingleOrDefault(t => t.ItemId == igroup.Key.ItemId).PurchasePrice :
            //                    //(DB.IC_ItemPriceChanges.Where(p => p.ItemId == igroup.Key.ItemId).Select(p => p.OldPPrice).Sum() + DB.IC_ItemPriceChanges.Where(p => p.ItemId == igroup.Key.ItemId).Select(p => p.NewPPrice).Sum()) / (DB.IC_ItemPriceChanges.Where(p => p.ItemId == igroup.Key.ItemId).Count() * 2)

            //                    // InvoiceCode=i.InvoiceCode,
            //                    // InvoiceDate= i.InvoiceDate,
            //                    // Customer= DB.SL_Customers.SingleOrDefault(c=>c.CustomerId==i.CustomerId) .CusNameAr,
            //                    // Net=i.Net,
            //                    //PaidInAdvance=i.Paid,
            //                    //Paid= cash+note,
            //                    //x = DB.IC_ItemPriceChanges.SingleOrDefault(p => p.ItemId == grp.Select(g => g.d.ItemId).FirstOrDefault()) == null ? DB.IC_Items.SingleOrDefault(t => t.ItemId == grp.Select(g => g.d.ItemId).FirstOrDefault()).PurchasePrice :
            //                    //              (SumOldPrice + SumNewPrice) / Count,
            //                  
            //                    #endregion
            //                    InvoiceCode = grp.Select(g => g.i.InvoiceCode).FirstOrDefault(),
            //                    InvoiceDate = grp.Select(g => g.i.InvoiceDate).FirstOrDefault(),
            //                    Customer = DB.SL_Customers.SingleOrDefault(c => c.CustomerId == grp.Select(g => g.i.CustomerId).FirstOrDefault()).CusNameAr,
            //                    TotalInvoice =/*Math.Round(*/ grp.Select(g => g.i.Net).FirstOrDefault()/*,1,MidpointRounding.AwayFromZero)*/,
            //                    PaidInAdvance =  grp.Select(g => g.i.Paid).FirstOrDefault(),
            //                    Paid =/*Math.Round(*/ (cash + note)/*,MidpointRounding.AwayFromZero)*/,
            //                    TotalCapital = BuyPrice,
            //                    PercentageOfTheRest =/* BuyPrice == 0 ? 0 :*/ "%" + ( (BuyPrice - (cash + note+ grp.Select(g => g.i.Paid).FirstOrDefault())) / BuyPrice)*100

            //                }).Distinct();
            #endregion

            


            var data1=from i in DB.SL_Invoices
                      
                     
                     where i.PayMethod == false || i.PayMethod == null
                      where fltrTyp_Date == 1 ? i.InvoiceDate.Date == date1.Date : true
                      where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                      i.InvoiceDate.Date >= date1.Date && i.InvoiceDate.Date <= date2.Date : true
                      where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                      i.InvoiceDate.Date >= date1.Date : true
                      where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                      i.InvoiceDate.Date <= date2.Date : true



                      where FltrTyp_Customer == 1 ? i.CustomerId == customerId1 : true
                      where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 != 0) ?
                      i.CustomerId >= customerId1 && i.CustomerId <= customerId2 : true
                      where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 == 0) ?
                      i.CustomerId >= customerId1 : true
                      where (FltrTyp_Customer == 2 && customerId1 == 0 && customerId2 != 0) ?
                      i.CustomerId <= customerId2 : true

                      let CashPaid = from c in DB.ACC_CashNotes.Where(x => x.SourceId ==i.SL_InvoiceId)
                                    where c.IsPay == false && c.ProcessId == (int)Process.SellInvoice
                                    select new { c.SourceId, c.Amount }
                     let NotePaid = from i3 in DB.ACC_NotesReceivables.Where(x => x.SourceId == i.SL_InvoiceId)
                                    where i3.ResponseType == 3 || i3.ResponseType == 4
                                    select new { i3.SourceId, i3.Amount }

                     let Net = i.Net
                     let cash = CashPaid.Count() > 0 ? CashPaid.Sum(x => x.Amount) : 0
                     let note = NotePaid.Count() > 0 ? NotePaid.Sum(x => x.Amount) : 0
                    // let IsPaid = i.PayMethod == true ? true : (cash + note +i.Paid) >= Net

                  where (cash + note + i.Paid) <Net
                      select new
                     {
                       //  x=IsPaid,
                         y=cash,
                         z=note,
                         SL_InvoiceId = i.SL_InvoiceId,
                         InvoiceCode= i.InvoiceCode,
                         InvoiceDate=  i.InvoiceDate,
                         TotalInvoice = i.Net,
                         PaidInAdvance=  i.Paid,
                         Paid=cash+note,
                         Customer= DB.SL_Customers.SingleOrDefault(c => c.CustomerId==i.CustomerId).CusNameAr
                     };

            var data2 =  from d in DB.SL_InvoiceDetails
                        join s in DB.IC_ItemStores on d.SL_InvoiceDetailId equals s.SourceId  
                          group new { d, s } by d.SL_InvoiceId into grp
                        let BuyPrice =grp.Sum(g => g.s.ProcessId == (int)Process.SellInvoice ? g.s.PurchasePrice : 0)

                        select new
                        {
                            SL_InvoiceId= grp.Select(g => g.d.SL_InvoiceId).FirstOrDefault(),
                           
                            BuyPrice = BuyPrice
                        };

            var data = (from i in DB.SL_Invoices
                          join o in DB.IC_OutTrns  on i.SL_InvoiceId equals o.SourceId
                        //where o.Is_SellInv == true && o.SourceId != null
                        join od in DB.IC_OutTrnsDetails on o.OutTrnsId equals od.OutTrnsId
                        join s in DB.IC_ItemStores on od.OutTrnsDetailId equals s.SourceId
                        //group new { o, s, i,od } by new { i.SL_InvoiceId } into grp
                        group new { o, s, i, od } by new { o.OutTrnsId } into grp

                        let BuyPrice = grp.Sum(g => g.s.ProcessId==(int)Process.OutTrns? g.s.PurchasePrice:0 )
                        //let outId= grp.Select(g => g.o.OutTrnsId).FirstOrDefault()//DB.SL_Invoices.SingleOrDefault(l=>l.SL_InvoiceId== grp.Select(g => g.i.SL_InvoiceId).FirstOrDefault())
                        let source=DB.IC_OutTrns.SingleOrDefault(r=>r.OutTrnsId==grp.Key.OutTrnsId).SourceId

                        select new
                        {
                            // SL_InvoiceId = grp.Select(g => g.o.SourceId).FirstOrDefault(),
                            SL_InvoiceId =source,
                            BuyPrice = BuyPrice,
                            //s=i.SL_InvoiceId
                           //SL_InvoiceId=source

                        }).Distinct().ToList();

            var data3 = (from d2 in data2
                         join d1 in data1 on d2.SL_InvoiceId equals d1.SL_InvoiceId
                         where d2.BuyPrice > 0
                         let PercentageOfTheRest = ((d2.BuyPrice - (d1.Paid + d1.PaidInAdvance)) / d2.BuyPrice) * 100
                         select new
                         {
                             PercentageOfTheRest = "%" + (PercentageOfTheRest < 0 ? 0 : PercentageOfTheRest),
                             InvoiceCode = d1.InvoiceCode,
                             InvoiceDate = d1.InvoiceDate,
                             TotalInvoice = d1.TotalInvoice,
                             PaidInAdvance = d1.PaidInAdvance,
                             Paid = d1.Paid,
                             Customer = d1.Customer,
                             TotalCapital = d2.BuyPrice

                         }).Distinct().ToList();
           
                        var data4= (from d2 in data
                        join d1 in data1 on d2.SL_InvoiceId equals d1.SL_InvoiceId
                        where d2.BuyPrice > 0
                        let PercentageOfTheRest = ((d2.BuyPrice - (d1.Paid + d1.PaidInAdvance)) / d2.BuyPrice) * 100
                        select new
                        {
                            PercentageOfTheRest = "%" + (PercentageOfTheRest < 0 ? 0 : PercentageOfTheRest),
                            InvoiceCode = d1.InvoiceCode,
                            InvoiceDate = d1.InvoiceDate,
                            TotalInvoice = d1.TotalInvoice,
                            PaidInAdvance = d1.PaidInAdvance,
                            Paid = d1.Paid,
                            Customer = d1.Customer,
                            TotalCapital = d2.BuyPrice

                        }).Distinct().ToList();

            grd_data.DataSource = data3.Union(data4);




                      }

        bool LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.rpt_SL_InvoicesHeaders).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            return true;
        }


        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                //lblCompName.Text = comp.CmpNameAr;
                lblReportName.Text = this.Text = reportName;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }
    }
}
