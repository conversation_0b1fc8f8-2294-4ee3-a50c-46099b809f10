﻿namespace Pharmacy.Forms
{
    partial class frm_SL_Add_Taxes
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_SL_Add_Taxes));
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtnHelp = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnSave = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barClose = new DevExpress.XtraBars.BarButtonItem();
            this.barbtn_Print = new DevExpress.XtraBars.BarButtonItem();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.panelControl2 = new DevExpress.XtraEditors.PanelControl();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.lkpSubTax = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.lkpItem = new DevExpress.XtraEditors.LookUpEdit();
            this.txt_Percenage = new DevExpress.XtraEditors.SpinEdit();
            this.grdPrInvoice = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_Tax = new DevExpress.XtraGrid.Columns.GridColumn();
            this.lkp_Tax = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colTax = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colTaxId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_SubTax = new DevExpress.XtraGrid.Columns.GridColumn();
            this.lkp_SubTax = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colSubTax = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colSubTaxId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Percentage = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repSpin = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.repDistributeMethod = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.repositoryItemGridLookUpEdit1View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).BeginInit();
            this.panelControl2.SuspendLayout();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lkpSubTax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpItem.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Percenage.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdPrInvoice)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Tax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_SubTax)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repSpin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repDistributeMethod)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnSave,
            this.barBtnClose,
            this.barBtnHelp,
            this.barClose,
            this.barbtn_Print});
            this.barManager1.MaxItemId = 36;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(554, 147);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnHelp),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnSave)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtnHelp
            // 
            resources.ApplyResources(this.barBtnHelp, "barBtnHelp");
            this.barBtnHelp.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnHelp.Glyph = ((System.Drawing.Image)(resources.GetObject("barBtnHelp.Glyph")));
            this.barBtnHelp.Id = 2;
            this.barBtnHelp.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtnHelp.Name = "barBtnHelp";
            this.barBtnHelp.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnHelp.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnHelp_ItemClick);
            // 
            // barBtnSave
            // 
            resources.ApplyResources(this.barBtnSave, "barBtnSave");
            this.barBtnSave.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnSave.Glyph = ((System.Drawing.Image)(resources.GetObject("barBtnSave.Glyph")));
            this.barBtnSave.Id = 0;
            this.barBtnSave.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.S));
            this.barBtnSave.Name = "barBtnSave";
            this.barBtnSave.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnSave.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnSave_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.BarAppearance.Normal.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.BarAppearance.Normal.GradientMode")));
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.BarAppearance.Normal.Image")));
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.Dock.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.Dock.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.Dock.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.Dock.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.Dock.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.Dock.GradientMode")));
            this.barAndDockingController1.AppearancesBar.Dock.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.Dock.Image")));
            this.barAndDockingController1.AppearancesBar.Dock.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.Dock.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.GradientMode")));
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Image")));
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.FontStyleDelta" +
        "")));
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.GradientMode")));
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.Image")));
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontSizeDel" +
        "ta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.FontStyleDe" +
        "lta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.GradientMod" +
        "e")));
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontSizeDelt" +
        "a")));
            this.barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.FontStyleDel" +
        "ta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.GradientMode" +
        "")));
            this.barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.HeaderItemAppearance.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuBar.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuBar.GradientMode")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuBar.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.GradientMode")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStrip.FontStyleDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStrip.GradientMode")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStrip.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontSizeDelta")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.FontStyleDelta" +
        "")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.GradientMode")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.Image")));
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.Panel.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesDocking.Panel.FontSizeDelta")));
            this.barAndDockingController1.AppearancesDocking.Panel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesDocking.Panel.FontStyleDelta")));
            this.barAndDockingController1.AppearancesDocking.Panel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesDocking.Panel.GradientMode")));
            this.barAndDockingController1.AppearancesDocking.Panel.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesDocking.Panel.Image")));
            this.barAndDockingController1.AppearancesDocking.Panel.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.Panel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.PanelCaption.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaption.FontSizeDelta")));
            this.barAndDockingController1.AppearancesDocking.PanelCaption.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaption.FontStyleDelta")));
            this.barAndDockingController1.AppearancesDocking.PanelCaption.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaption.GradientMode")));
            this.barAndDockingController1.AppearancesDocking.PanelCaption.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesDocking.PanelCaption.Image")));
            this.barAndDockingController1.AppearancesDocking.PanelCaption.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.PanelCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesRibbon.Item.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesRibbon.Item.FontSizeDelta")));
            this.barAndDockingController1.AppearancesRibbon.Item.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesRibbon.Item.FontStyleDelta")));
            this.barAndDockingController1.AppearancesRibbon.Item.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesRibbon.Item.GradientMode")));
            this.barAndDockingController1.AppearancesRibbon.Item.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesRibbon.Item.Image")));
            this.barAndDockingController1.AppearancesRibbon.Item.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesRibbon.Item.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesRibbon.PageHeader.FontSizeDelta = ((int)(resources.GetObject("barAndDockingController1.AppearancesRibbon.PageHeader.FontSizeDelta")));
            this.barAndDockingController1.AppearancesRibbon.PageHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barAndDockingController1.AppearancesRibbon.PageHeader.FontStyleDelta")));
            this.barAndDockingController1.AppearancesRibbon.PageHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barAndDockingController1.AppearancesRibbon.PageHeader.GradientMode")));
            this.barAndDockingController1.AppearancesRibbon.PageHeader.Image = ((System.Drawing.Image)(resources.GetObject("barAndDockingController1.AppearancesRibbon.PageHeader.Image")));
            this.barAndDockingController1.AppearancesRibbon.PageHeader.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesRibbon.PageHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlTop.Appearance.FontSizeDelta")));
            this.barDockControlTop.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlTop.Appearance.FontStyleDelta")));
            this.barDockControlTop.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlTop.Appearance.GradientMode")));
            this.barDockControlTop.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlTop.Appearance.Image")));
            this.barDockControlTop.Appearance.Options.UseTextOptions = true;
            this.barDockControlTop.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barDockControlTop.CausesValidation = false;
            // 
            // barDockControlBottom
            // 
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlBottom.Appearance.FontSizeDelta")));
            this.barDockControlBottom.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlBottom.Appearance.FontStyleDelta")));
            this.barDockControlBottom.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlBottom.Appearance.GradientMode")));
            this.barDockControlBottom.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlBottom.Appearance.Image")));
            this.barDockControlBottom.CausesValidation = false;
            // 
            // barDockControlLeft
            // 
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlLeft.Appearance.FontSizeDelta")));
            this.barDockControlLeft.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlLeft.Appearance.FontStyleDelta")));
            this.barDockControlLeft.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlLeft.Appearance.GradientMode")));
            this.barDockControlLeft.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlLeft.Appearance.Image")));
            this.barDockControlLeft.CausesValidation = false;
            // 
            // barDockControlRight
            // 
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlRight.Appearance.FontSizeDelta")));
            this.barDockControlRight.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlRight.Appearance.FontStyleDelta")));
            this.barDockControlRight.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlRight.Appearance.GradientMode")));
            this.barDockControlRight.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlRight.Appearance.Image")));
            this.barDockControlRight.CausesValidation = false;
            // 
            // barBtnClose
            // 
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnClose.Glyph = ((System.Drawing.Image)(resources.GetObject("barBtnClose.Glyph")));
            this.barBtnClose.Id = 1;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            // 
            // barClose
            // 
            resources.ApplyResources(this.barClose, "barClose");
            this.barClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barClose.Id = 32;
            this.barClose.Name = "barClose";
            this.barClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem1_ItemClick);
            // 
            // barbtn_Print
            // 
            resources.ApplyResources(this.barbtn_Print, "barbtn_Print");
            this.barbtn_Print.Id = 35;
            this.barbtn_Print.Name = "barbtn_Print";
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemTextEdit1.Mask.AutoComplete")));
            this.repositoryItemTextEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.BeepOnError")));
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemTextEdit1.Mask.MaskType")));
            this.repositoryItemTextEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemTextEdit1.Mask.PlaceHolder")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // panelControl2
            // 
            resources.ApplyResources(this.panelControl2, "panelControl2");
            this.panelControl2.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.panelControl2.Controls.Add(this.groupBox1);
            this.panelControl2.Controls.Add(this.grdPrInvoice);
            this.panelControl2.Name = "panelControl2";
            // 
            // groupBox1
            // 
            resources.ApplyResources(this.groupBox1, "groupBox1");
            this.groupBox1.Controls.Add(this.labelControl3);
            this.groupBox1.Controls.Add(this.labelControl2);
            this.groupBox1.Controls.Add(this.labelControl1);
            this.groupBox1.Controls.Add(this.lkpSubTax);
            this.groupBox1.Controls.Add(this.labelControl10);
            this.groupBox1.Controls.Add(this.lkpItem);
            this.groupBox1.Controls.Add(this.txt_Percenage);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.TabStop = false;
            // 
            // labelControl3
            // 
            resources.ApplyResources(this.labelControl3, "labelControl3");
            this.labelControl3.Name = "labelControl3";
            // 
            // labelControl2
            // 
            resources.ApplyResources(this.labelControl2, "labelControl2");
            this.labelControl2.Name = "labelControl2";
            // 
            // labelControl1
            // 
            resources.ApplyResources(this.labelControl1, "labelControl1");
            this.labelControl1.Name = "labelControl1";
            // 
            // lkpSubTax
            // 
            resources.ApplyResources(this.lkpSubTax, "lkpSubTax");
            this.lkpSubTax.EnterMoveNextControl = true;
            this.lkpSubTax.MenuManager = this.barManager1;
            this.lkpSubTax.Name = "lkpSubTax";
            this.lkpSubTax.Properties.AccessibleDescription = resources.GetString("lkpSubTax.Properties.AccessibleDescription");
            this.lkpSubTax.Properties.AccessibleName = resources.GetString("lkpSubTax.Properties.AccessibleName");
            this.lkpSubTax.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkpSubTax.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkpSubTax.Properties.Appearance.FontSizeDelta")));
            this.lkpSubTax.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpSubTax.Properties.Appearance.FontStyleDelta")));
            this.lkpSubTax.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpSubTax.Properties.Appearance.GradientMode")));
            this.lkpSubTax.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkpSubTax.Properties.Appearance.Image")));
            this.lkpSubTax.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpSubTax.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpSubTax.Properties.AutoHeight = ((bool)(resources.GetObject("lkpSubTax.Properties.AutoHeight")));
            this.lkpSubTax.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpSubTax.Properties.Buttons"))))});
            this.lkpSubTax.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpSubTax.Properties.Columns"), resources.GetString("lkpSubTax.Properties.Columns1")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpSubTax.Properties.Columns2"), resources.GetString("lkpSubTax.Properties.Columns3"), ((int)(resources.GetObject("lkpSubTax.Properties.Columns4"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpSubTax.Properties.Columns5"))), resources.GetString("lkpSubTax.Properties.Columns6"), ((bool)(resources.GetObject("lkpSubTax.Properties.Columns7"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpSubTax.Properties.Columns8"))))});
            this.lkpSubTax.Properties.NullText = resources.GetString("lkpSubTax.Properties.NullText");
            this.lkpSubTax.Properties.NullValuePrompt = resources.GetString("lkpSubTax.Properties.NullValuePrompt");
            this.lkpSubTax.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkpSubTax.Properties.NullValuePromptShowForEmptyValue")));
            this.lkpSubTax.TabStop = false;
            // 
            // labelControl10
            // 
            resources.ApplyResources(this.labelControl10, "labelControl10");
            this.labelControl10.Name = "labelControl10";
            // 
            // lkpItem
            // 
            resources.ApplyResources(this.lkpItem, "lkpItem");
            this.lkpItem.EnterMoveNextControl = true;
            this.lkpItem.MenuManager = this.barManager1;
            this.lkpItem.Name = "lkpItem";
            this.lkpItem.Properties.AccessibleDescription = resources.GetString("lkpItem.Properties.AccessibleDescription");
            this.lkpItem.Properties.AccessibleName = resources.GetString("lkpItem.Properties.AccessibleName");
            this.lkpItem.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkpItem.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkpItem.Properties.Appearance.FontSizeDelta")));
            this.lkpItem.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpItem.Properties.Appearance.FontStyleDelta")));
            this.lkpItem.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpItem.Properties.Appearance.GradientMode")));
            this.lkpItem.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkpItem.Properties.Appearance.Image")));
            this.lkpItem.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpItem.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpItem.Properties.AutoHeight = ((bool)(resources.GetObject("lkpItem.Properties.AutoHeight")));
            this.lkpItem.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpItem.Properties.Buttons"))))});
            this.lkpItem.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpItem.Properties.Columns"), resources.GetString("lkpItem.Properties.Columns1")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpItem.Properties.Columns2"), resources.GetString("lkpItem.Properties.Columns3"), ((int)(resources.GetObject("lkpItem.Properties.Columns4"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpItem.Properties.Columns5"))), resources.GetString("lkpItem.Properties.Columns6"), ((bool)(resources.GetObject("lkpItem.Properties.Columns7"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpItem.Properties.Columns8"))))});
            this.lkpItem.Properties.NullText = resources.GetString("lkpItem.Properties.NullText");
            this.lkpItem.Properties.NullValuePrompt = resources.GetString("lkpItem.Properties.NullValuePrompt");
            this.lkpItem.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkpItem.Properties.NullValuePromptShowForEmptyValue")));
            this.lkpItem.TabStop = false;
            this.lkpItem.EditValueChanged += new System.EventHandler(this.lkpItem_EditValueChanged);
            // 
            // txt_Percenage
            // 
            resources.ApplyResources(this.txt_Percenage, "txt_Percenage");
            this.txt_Percenage.EnterMoveNextControl = true;
            this.txt_Percenage.MenuManager = this.barManager1;
            this.txt_Percenage.Name = "txt_Percenage";
            this.txt_Percenage.Properties.AccessibleDescription = resources.GetString("txt_Percenage.Properties.AccessibleDescription");
            this.txt_Percenage.Properties.AccessibleName = resources.GetString("txt_Percenage.Properties.AccessibleName");
            this.txt_Percenage.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Percenage.Properties.AutoHeight")));
            this.txt_Percenage.Properties.Increment = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.txt_Percenage.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_Percenage.Properties.Mask.AutoComplete")));
            this.txt_Percenage.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_Percenage.Properties.Mask.BeepOnError")));
            this.txt_Percenage.Properties.Mask.EditMask = resources.GetString("txt_Percenage.Properties.Mask.EditMask");
            this.txt_Percenage.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Percenage.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Percenage.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Percenage.Properties.Mask.MaskType")));
            this.txt_Percenage.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_Percenage.Properties.Mask.PlaceHolder")));
            this.txt_Percenage.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Percenage.Properties.Mask.SaveLiteral")));
            this.txt_Percenage.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Percenage.Properties.Mask.ShowPlaceHolders")));
            this.txt_Percenage.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_Percenage.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_Percenage.Properties.MaxValue = new decimal(new int[] {
            -1530494977,
            232830,
            0,
            0});
            this.txt_Percenage.Properties.NullValuePrompt = resources.GetString("txt_Percenage.Properties.NullValuePrompt");
            this.txt_Percenage.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_Percenage.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_Percenage.TabStop = false;
            this.txt_Percenage.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtNumberOnly_KeyPress);
            // 
            // grdPrInvoice
            // 
            resources.ApplyResources(this.grdPrInvoice, "grdPrInvoice");
            this.grdPrInvoice.Cursor = System.Windows.Forms.Cursors.Default;
            this.grdPrInvoice.EmbeddedNavigator.AccessibleDescription = resources.GetString("grdPrInvoice.EmbeddedNavigator.AccessibleDescription");
            this.grdPrInvoice.EmbeddedNavigator.AccessibleName = resources.GetString("grdPrInvoice.EmbeddedNavigator.AccessibleName");
            this.grdPrInvoice.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grdPrInvoice.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.Anchor")));
            this.grdPrInvoice.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.BackgroundImage")));
            this.grdPrInvoice.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.BackgroundImageLayout")));
            this.grdPrInvoice.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.ImeMode")));
            this.grdPrInvoice.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.MaximumSize")));
            this.grdPrInvoice.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.TextLocation")));
            this.grdPrInvoice.EmbeddedNavigator.ToolTip = resources.GetString("grdPrInvoice.EmbeddedNavigator.ToolTip");
            this.grdPrInvoice.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.ToolTipIconType")));
            this.grdPrInvoice.EmbeddedNavigator.ToolTipTitle = resources.GetString("grdPrInvoice.EmbeddedNavigator.ToolTipTitle");
            this.grdPrInvoice.MainView = this.gridView1;
            this.grdPrInvoice.MenuManager = this.barManager1;
            this.grdPrInvoice.Name = "grdPrInvoice";
            this.grdPrInvoice.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repSpin,
            this.repDistributeMethod,
            this.lkp_Tax,
            this.lkp_SubTax});
            this.grdPrInvoice.TabStop = false;
            this.grdPrInvoice.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            this.grdPrInvoice.ProcessGridKey += new System.Windows.Forms.KeyEventHandler(this.grdPrInvoice_ProcessGridKey);
            // 
            // gridView1
            // 
            this.gridView1.Appearance.FooterPanel.BackColor = ((System.Drawing.Color)(resources.GetObject("gridView1.Appearance.FooterPanel.BackColor")));
            this.gridView1.Appearance.FooterPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.FooterPanel.FontSizeDelta")));
            this.gridView1.Appearance.FooterPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.FooterPanel.FontStyleDelta")));
            this.gridView1.Appearance.FooterPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.Appearance.FooterPanel.ForeColor")));
            this.gridView1.Appearance.FooterPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.FooterPanel.GradientMode")));
            this.gridView1.Appearance.FooterPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.FooterPanel.Image")));
            this.gridView1.Appearance.FooterPanel.Options.UseBackColor = true;
            this.gridView1.Appearance.FooterPanel.Options.UseForeColor = true;
            this.gridView1.Appearance.FooterPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.GroupFooter.BackColor = ((System.Drawing.Color)(resources.GetObject("gridView1.Appearance.GroupFooter.BackColor")));
            this.gridView1.Appearance.GroupFooter.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.GroupFooter.FontSizeDelta")));
            this.gridView1.Appearance.GroupFooter.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.GroupFooter.FontStyleDelta")));
            this.gridView1.Appearance.GroupFooter.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.Appearance.GroupFooter.ForeColor")));
            this.gridView1.Appearance.GroupFooter.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.GroupFooter.GradientMode")));
            this.gridView1.Appearance.GroupFooter.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.GroupFooter.Image")));
            this.gridView1.Appearance.GroupFooter.Options.UseBackColor = true;
            this.gridView1.Appearance.GroupFooter.Options.UseForeColor = true;
            this.gridView1.Appearance.GroupFooter.Options.UseTextOptions = true;
            this.gridView1.Appearance.GroupFooter.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.HeaderPanel.FontSizeDelta")));
            this.gridView1.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.HeaderPanel.FontStyleDelta")));
            this.gridView1.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.HeaderPanel.GradientMode")));
            this.gridView1.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.HeaderPanel.Image")));
            this.gridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.HeaderPanel.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridView1.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.Row.FontSizeDelta")));
            this.gridView1.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.Row.FontStyleDelta")));
            this.gridView1.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.Row.GradientMode")));
            this.gridView1.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.Row.Image")));
            this.gridView1.Appearance.Row.Options.UseTextOptions = true;
            this.gridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.Row.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            resources.ApplyResources(this.gridView1, "gridView1");
            this.gridView1.ColumnPanelRowHeight = 60;
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_Tax,
            this.col_SubTax,
            this.col_Percentage});
            this.gridView1.GridControl = this.grdPrInvoice;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.True;
            this.gridView1.OptionsBehavior.AutoExpandAllGroups = true;
            this.gridView1.OptionsCustomization.AllowSort = false;
            this.gridView1.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.gridView1.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView1.OptionsView.GroupFooterShowMode = DevExpress.XtraGrid.Views.Grid.GroupFooterShowMode.VisibleAlways;
            this.gridView1.OptionsView.RowAutoHeight = true;
            this.gridView1.OptionsView.ShowFooter = true;
            this.gridView1.OptionsView.ShowGroupedColumns = true;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.CustomUnboundColumnData += new DevExpress.XtraGrid.Views.Base.CustomColumnDataEventHandler(this.gridView1_CustomUnboundColumnData);
            // 
            // col_Tax
            // 
            resources.ApplyResources(this.col_Tax, "col_Tax");
            this.col_Tax.ColumnEdit = this.lkp_Tax;
            this.col_Tax.FieldName = "Tax";
            this.col_Tax.Name = "col_Tax";
            this.col_Tax.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // lkp_Tax
            // 
            resources.ApplyResources(this.lkp_Tax, "lkp_Tax");
            this.lkp_Tax.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkp_Tax.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_Tax.Buttons"))))});
            this.lkp_Tax.Name = "lkp_Tax";
            this.lkp_Tax.View = this.gridView2;
            // 
            // gridView2
            // 
            resources.ApplyResources(this.gridView2, "gridView2");
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colTax,
            this.colTaxId});
            this.gridView2.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            // 
            // colTax
            // 
            resources.ApplyResources(this.colTax, "colTax");
            this.colTax.FieldName = "Tax";
            this.colTax.Name = "colTax";
            // 
            // colTaxId
            // 
            resources.ApplyResources(this.colTaxId, "colTaxId");
            this.colTaxId.FieldName = "TaxId";
            this.colTaxId.Name = "colTaxId";
            // 
            // col_SubTax
            // 
            resources.ApplyResources(this.col_SubTax, "col_SubTax");
            this.col_SubTax.ColumnEdit = this.lkp_SubTax;
            this.col_SubTax.FieldName = "SubTax";
            this.col_SubTax.Name = "col_SubTax";
            this.col_SubTax.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // lkp_SubTax
            // 
            resources.ApplyResources(this.lkp_SubTax, "lkp_SubTax");
            this.lkp_SubTax.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_SubTax.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_SubTax.Buttons"))))});
            this.lkp_SubTax.Name = "lkp_SubTax";
            this.lkp_SubTax.View = this.gridView3;
            // 
            // gridView3
            // 
            resources.ApplyResources(this.gridView3, "gridView3");
            this.gridView3.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colSubTax,
            this.colSubTaxId});
            this.gridView3.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            // 
            // colSubTax
            // 
            resources.ApplyResources(this.colSubTax, "colSubTax");
            this.colSubTax.FieldName = "SubTax";
            this.colSubTax.Name = "colSubTax";
            // 
            // colSubTaxId
            // 
            resources.ApplyResources(this.colSubTaxId, "colSubTaxId");
            this.colSubTaxId.FieldName = "SubTaxId";
            this.colSubTaxId.Name = "colSubTaxId";
            // 
            // col_Percentage
            // 
            resources.ApplyResources(this.col_Percentage, "col_Percentage");
            this.col_Percentage.ColumnEdit = this.repSpin;
            this.col_Percentage.FieldName = "Percentage";
            this.col_Percentage.Name = "col_Percentage";
            this.col_Percentage.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // repSpin
            // 
            resources.ApplyResources(this.repSpin, "repSpin");
            this.repSpin.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.repSpin.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repSpin.Mask.AutoComplete")));
            this.repSpin.Mask.BeepOnError = ((bool)(resources.GetObject("repSpin.Mask.BeepOnError")));
            this.repSpin.Mask.EditMask = resources.GetString("repSpin.Mask.EditMask");
            this.repSpin.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repSpin.Mask.IgnoreMaskBlank")));
            this.repSpin.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repSpin.Mask.MaskType")));
            this.repSpin.Mask.PlaceHolder = ((char)(resources.GetObject("repSpin.Mask.PlaceHolder")));
            this.repSpin.Mask.SaveLiteral = ((bool)(resources.GetObject("repSpin.Mask.SaveLiteral")));
            this.repSpin.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repSpin.Mask.ShowPlaceHolders")));
            this.repSpin.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repSpin.Mask.UseMaskAsDisplayFormat")));
            this.repSpin.Name = "repSpin";
            // 
            // repDistributeMethod
            // 
            resources.ApplyResources(this.repDistributeMethod, "repDistributeMethod");
            this.repDistributeMethod.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.repDistributeMethod.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.repDistributeMethod.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repDistributeMethod.Buttons"))))});
            this.repDistributeMethod.Name = "repDistributeMethod";
            this.repDistributeMethod.ShowFooter = false;
            this.repDistributeMethod.View = this.repositoryItemGridLookUpEdit1View;
            // 
            // repositoryItemGridLookUpEdit1View
            // 
            this.repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.FontSizeDelta")));
            this.repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.FontStyleDelta")));
            this.repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.GradientMode")));
            this.repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.Image")));
            this.repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.repositoryItemGridLookUpEdit1View.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("repositoryItemGridLookUpEdit1View.Appearance.Row.FontSizeDelta")));
            this.repositoryItemGridLookUpEdit1View.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("repositoryItemGridLookUpEdit1View.Appearance.Row.FontStyleDelta")));
            this.repositoryItemGridLookUpEdit1View.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("repositoryItemGridLookUpEdit1View.Appearance.Row.GradientMode")));
            this.repositoryItemGridLookUpEdit1View.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("repositoryItemGridLookUpEdit1View.Appearance.Row.Image")));
            this.repositoryItemGridLookUpEdit1View.Appearance.Row.Options.UseTextOptions = true;
            this.repositoryItemGridLookUpEdit1View.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.repositoryItemGridLookUpEdit1View, "repositoryItemGridLookUpEdit1View");
            this.repositoryItemGridLookUpEdit1View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn14,
            this.gridColumn15});
            this.repositoryItemGridLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.repositoryItemGridLookUpEdit1View.Name = "repositoryItemGridLookUpEdit1View";
            this.repositoryItemGridLookUpEdit1View.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.repositoryItemGridLookUpEdit1View.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.repositoryItemGridLookUpEdit1View.OptionsBehavior.AutoSelectAllInEditor = false;
            this.repositoryItemGridLookUpEdit1View.OptionsBehavior.AutoUpdateTotalSummary = false;
            this.repositoryItemGridLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.repositoryItemGridLookUpEdit1View.OptionsSelection.UseIndicatorForSelection = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.BestFitMaxRowCount = 10;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowAutoFilterRow = true;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowDetailButtons = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowGroupPanel = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowIndicator = false;
            // 
            // gridColumn14
            // 
            resources.ApplyResources(this.gridColumn14, "gridColumn14");
            this.gridColumn14.FieldName = "dName";
            this.gridColumn14.Name = "gridColumn14";
            // 
            // gridColumn15
            // 
            resources.ApplyResources(this.gridColumn15, "gridColumn15");
            this.gridColumn15.FieldName = "dId";
            this.gridColumn15.Name = "gridColumn15";
            // 
            // frm_SL_Add_Taxes
            // 
            resources.ApplyResources(this, "$this");
            this.Appearance.FontSizeDelta = ((int)(resources.GetObject("frm_SL_Add_Taxes.Appearance.FontSizeDelta")));
            this.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("frm_SL_Add_Taxes.Appearance.FontStyleDelta")));
            this.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("frm_SL_Add_Taxes.Appearance.GradientMode")));
            this.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("frm_SL_Add_Taxes.Appearance.Image")));
            this.Appearance.Options.UseTextOptions = true;
            this.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.panelControl2);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.KeyPreview = true;
            this.Name = "frm_SL_Add_Taxes";
            this.Load += new System.EventHandler(this.frm_SL_Add_Taxes_Load);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).EndInit();
            this.panelControl2.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lkpSubTax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpItem.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Percenage.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdPrInvoice)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Tax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_SubTax)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repSpin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repDistributeMethod)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnSave;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtnHelp;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        
        private DevExpress.XtraEditors.PanelControl panelControl2;
        private DevExpress.XtraGrid.GridControl grdPrInvoice;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit repSpin;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit repDistributeMethod;
        private DevExpress.XtraGrid.Views.Grid.GridView repositoryItemGridLookUpEdit1View;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraEditors.LookUpEdit lkpItem;
        private System.Windows.Forms.GroupBox groupBox1;
        private DevExpress.XtraBars.BarButtonItem barClose;
        private DevExpress.XtraBars.BarButtonItem barbtn_Print;
        private DevExpress.XtraEditors.LookUpEdit lkpSubTax;
        private DevExpress.XtraEditors.SpinEdit txt_Percenage;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit lkp_Tax;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn colTax;
        private DevExpress.XtraGrid.Columns.GridColumn colTaxId;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit lkp_SubTax;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn colSubTax;
        private DevExpress.XtraGrid.Columns.GridColumn colSubTaxId;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraGrid.Columns.GridColumn col_Tax;
        private DevExpress.XtraGrid.Columns.GridColumn col_SubTax;
        private DevExpress.XtraGrid.Columns.GridColumn col_Percentage;
    }
}