﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="Pharmacy.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <connectionStrings>
        <add name="Pharmacy.Properties.Settings.ERPConnectionString"
            connectionString="Data Source=.;Initial Catalog=Nice_Food;Integrated Security=True;Pooling=False;TrustServerCertificate=True"
            providerName="System.Data.SqlClient" />
    </connectionStrings>
    <userSettings>
        <Pharmacy.Properties.Settings>
            <setting name="ReceiptPrinterName" serializeAs="String">
                <value />
            </setting>
            <setting name="SWPortName" serializeAs="String">
                <value>COM1</value>
            </setting>
            <setting name="SWBaudRate" serializeAs="String">
                <value>2400</value>
            </setting>
            <setting name="SWParity" serializeAs="String">
                <value>2</value>
            </setting>
            <setting name="SWDataBits" serializeAs="String">
                <value>7</value>
            </setting>
            <setting name="SWStopBits" serializeAs="String">
                <value>1</value>
            </setting>
            <setting name="SWStartIndex" serializeAs="String">
                <value>4</value>
            </setting>
            <setting name="SWLength" serializeAs="String">
                <value>8</value>
            </setting>
            <setting name="SWPacketLength" serializeAs="String">
                <value>22</value>
            </setting>
            <setting name="SWTimerInterval" serializeAs="String">
                <value>270</value>
            </setting>
            <setting name="fb" serializeAs="String">
                <value />
            </setting>
            <setting name="Tw" serializeAs="String">
                <value />
            </setting>
            <setting name="Lnkd" serializeAs="String">
                <value />
            </setting>
            <setting name="BackEndPoint" serializeAs="String">
                <value>http://localhost/</value>
            </setting>
            <setting name="signuatuer_dll" serializeAs="String">
                <value>C:\Users\<USER>\source\repos\centeromeer\LinkIT-E-Invoice\ERP\Pharmacy\bin\McDRSigniture.dll</value>
            </setting>
            <setting name="RecievedDocumentsMinutesInterval" serializeAs="String">
                <value>10</value>
            </setting>
            <setting name="RecievedDocumentsLastSyncDate" serializeAs="String">
                <value />
            </setting>
            <setting name="BackEndPort" serializeAs="String">
                <value>57978</value>
            </setting>
        </Pharmacy.Properties.Settings>
    </userSettings>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="OC.Core" publicKeyToken="21d5b409dafb75d2" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.0.0.17347" newVersion="2.0.0.17347" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>