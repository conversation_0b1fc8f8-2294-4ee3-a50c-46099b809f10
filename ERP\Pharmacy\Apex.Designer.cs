﻿#pragma warning disable 1591
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Pharmacy
{
	using System.Data.Linq;
	using System.Data.Linq.Mapping;
	using System.Data;
	using System.Collections.Generic;
	using System.Reflection;
	using System.Linq;
	using System.Linq.Expressions;
	using System.ComponentModel;
	using System;
	
	
	[global::System.Data.Linq.Mapping.DatabaseAttribute(Name="apexdata")]
	public partial class ApexDataContext : System.Data.Linq.DataContext
	{
		
		private static System.Data.Linq.Mapping.MappingSource mappingSource = new AttributeMappingSource();
		
    #region Extensibility Method Definitions
    partial void OnCreated();
    #endregion
		
		public ApexDataContext() : 
				base(global::Pharmacy.Properties.Settings.Default.dataConnectionString, mappingSource)
		{
			OnCreated();
		}
		
		public ApexDataContext(string connection) : 
				base(connection, mappingSource)
		{
			OnCreated();
		}
		
		public ApexDataContext(System.Data.IDbConnection connection) : 
				base(connection, mappingSource)
		{
			OnCreated();
		}
		
		public ApexDataContext(string connection, System.Data.Linq.Mapping.MappingSource mappingSource) : 
				base(connection, mappingSource)
		{
			OnCreated();
		}
		
		public ApexDataContext(System.Data.IDbConnection connection, System.Data.Linq.Mapping.MappingSource mappingSource) : 
				base(connection, mappingSource)
		{
			OnCreated();
		}
		
		public System.Data.Linq.Table<cust> custs
		{
			get
			{
				return this.GetTable<cust>();
			}
		}
		
		public System.Data.Linq.Table<custb> custbs
		{
			get
			{
				return this.GetTable<custb>();
			}
		}
		
		public System.Data.Linq.Table<store> stores
		{
			get
			{
				return this.GetTable<store>();
			}
		}
		
		public System.Data.Linq.Table<supp> supps
		{
			get
			{
				return this.GetTable<supp>();
			}
		}
		
		public System.Data.Linq.Table<item> items
		{
			get
			{
				return this.GetTable<item>();
			}
		}
		
		public System.Data.Linq.Table<asset> assets
		{
			get
			{
				return this.GetTable<asset>();
			}
		}
		
		public System.Data.Linq.Table<assetd> assetds
		{
			get
			{
				return this.GetTable<assetd>();
			}
		}
		
		public System.Data.Linq.Table<enterprise> enterprises
		{
			get
			{
				return this.GetTable<enterprise>();
			}
		}
		
		public System.Data.Linq.Table<clientitem> clientitems
		{
			get
			{
				return this.GetTable<clientitem>();
			}
		}
	}
	
	[global::System.Data.Linq.Mapping.TableAttribute(Name="dbo.cust")]
	public partial class cust
	{
		
		private System.Nullable<int> _serial;
		
		private string _code;
		
		private string _descx;
		
		private System.Nullable<short> _price;
		
		private string _ref;
		
		private System.Nullable<short> _status;
		
		private System.Nullable<short> _type;
		
		private System.Nullable<short> _area;
		
		private string _tax;
		
		private System.Nullable<double> _limit;
		
		private string _typed;
		
		private System.Nullable<int> _credit;
		
		private System.Nullable<int> _sales;
		
		public cust()
		{
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_serial", DbType="Int")]
		public System.Nullable<int> serial
		{
			get
			{
				return this._serial;
			}
			set
			{
				if ((this._serial != value))
				{
					this._serial = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_code", DbType="NVarChar(50)")]
		public string code
		{
			get
			{
				return this._code;
			}
			set
			{
				if ((this._code != value))
				{
					this._code = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_descx", DbType="NVarChar(250)")]
		public string descx
		{
			get
			{
				return this._descx;
			}
			set
			{
				if ((this._descx != value))
				{
					this._descx = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_price", DbType="SmallInt")]
		public System.Nullable<short> price
		{
			get
			{
				return this._price;
			}
			set
			{
				if ((this._price != value))
				{
					this._price = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Name="ref", Storage="_ref", DbType="NVarChar(50)")]
		public string @ref
		{
			get
			{
				return this._ref;
			}
			set
			{
				if ((this._ref != value))
				{
					this._ref = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_status", DbType="SmallInt")]
		public System.Nullable<short> status
		{
			get
			{
				return this._status;
			}
			set
			{
				if ((this._status != value))
				{
					this._status = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_type", DbType="SmallInt")]
		public System.Nullable<short> type
		{
			get
			{
				return this._type;
			}
			set
			{
				if ((this._type != value))
				{
					this._type = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_area", DbType="SmallInt")]
		public System.Nullable<short> area
		{
			get
			{
				return this._area;
			}
			set
			{
				if ((this._area != value))
				{
					this._area = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_tax", DbType="NVarChar(50)")]
		public string tax
		{
			get
			{
				return this._tax;
			}
			set
			{
				if ((this._tax != value))
				{
					this._tax = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_limit", DbType="Float")]
		public System.Nullable<double> limit
		{
			get
			{
				return this._limit;
			}
			set
			{
				if ((this._limit != value))
				{
					this._limit = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_typed", DbType="NVarChar(100)")]
		public string typed
		{
			get
			{
				return this._typed;
			}
			set
			{
				if ((this._typed != value))
				{
					this._typed = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_credit", DbType="Int")]
		public System.Nullable<int> credit
		{
			get
			{
				return this._credit;
			}
			set
			{
				if ((this._credit != value))
				{
					this._credit = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_sales", DbType="Int")]
		public System.Nullable<int> sales
		{
			get
			{
				return this._sales;
			}
			set
			{
				if ((this._sales != value))
				{
					this._sales = value;
				}
			}
		}
	}
	
	[global::System.Data.Linq.Mapping.TableAttribute(Name="dbo.custb")]
	public partial class custb
	{
		
		private System.Nullable<int> _serial;
		
		private string _descx;
		
		private string _address;
		
		private string _phone1;
		
		private string _phone2;
		
		private string _phone3;
		
		private string _fax1;
		
		private string _fax2;
		
		private string _fax3;
		
		private string _zip;
		
		private string _country;
		
		private string _city;
		
		private string _ctc1;
		
		private string _ctc2;
		
		private string _ctc3;
		
		private string _ctm1;
		
		private string _ctm2;
		
		private string _ctm3;
		
		private System.Nullable<short> _serx;
		
		private System.Nullable<int> _branch;
		
		private System.Nullable<int> _area;
		
		private string _serv;
		
		private string _manager;
		
		private string _email;
		
		private System.Nullable<int> _type;
		
		private string _typed;
		
		private System.Nullable<System.DateTime> _edate;
		
		private System.Nullable<System.DateTime> _ddate;
		
		private System.Nullable<int> _cancel;
		
		private System.Nullable<System.DateTime> _cdate;
		
		private string _bank;
		
		private string _swift;
		
		private string _iban;
		
		private string _rout;
		
		private string _rights;
		
		public custb()
		{
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_serial", DbType="Int")]
		public System.Nullable<int> serial
		{
			get
			{
				return this._serial;
			}
			set
			{
				if ((this._serial != value))
				{
					this._serial = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_descx", DbType="NVarChar(80)")]
		public string descx
		{
			get
			{
				return this._descx;
			}
			set
			{
				if ((this._descx != value))
				{
					this._descx = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_address", DbType="NVarChar(250)")]
		public string address
		{
			get
			{
				return this._address;
			}
			set
			{
				if ((this._address != value))
				{
					this._address = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_phone1", DbType="NVarChar(20)")]
		public string phone1
		{
			get
			{
				return this._phone1;
			}
			set
			{
				if ((this._phone1 != value))
				{
					this._phone1 = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_phone2", DbType="NVarChar(20)")]
		public string phone2
		{
			get
			{
				return this._phone2;
			}
			set
			{
				if ((this._phone2 != value))
				{
					this._phone2 = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_phone3", DbType="NVarChar(20)")]
		public string phone3
		{
			get
			{
				return this._phone3;
			}
			set
			{
				if ((this._phone3 != value))
				{
					this._phone3 = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_fax1", DbType="NVarChar(20)")]
		public string fax1
		{
			get
			{
				return this._fax1;
			}
			set
			{
				if ((this._fax1 != value))
				{
					this._fax1 = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_fax2", DbType="NVarChar(20)")]
		public string fax2
		{
			get
			{
				return this._fax2;
			}
			set
			{
				if ((this._fax2 != value))
				{
					this._fax2 = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_fax3", DbType="NVarChar(20)")]
		public string fax3
		{
			get
			{
				return this._fax3;
			}
			set
			{
				if ((this._fax3 != value))
				{
					this._fax3 = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_zip", DbType="NVarChar(50)")]
		public string zip
		{
			get
			{
				return this._zip;
			}
			set
			{
				if ((this._zip != value))
				{
					this._zip = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_country", DbType="NVarChar(20)")]
		public string country
		{
			get
			{
				return this._country;
			}
			set
			{
				if ((this._country != value))
				{
					this._country = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_city", DbType="NVarChar(20)")]
		public string city
		{
			get
			{
				return this._city;
			}
			set
			{
				if ((this._city != value))
				{
					this._city = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_ctc1", DbType="NVarChar(50)")]
		public string ctc1
		{
			get
			{
				return this._ctc1;
			}
			set
			{
				if ((this._ctc1 != value))
				{
					this._ctc1 = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_ctc2", DbType="NVarChar(50)")]
		public string ctc2
		{
			get
			{
				return this._ctc2;
			}
			set
			{
				if ((this._ctc2 != value))
				{
					this._ctc2 = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_ctc3", DbType="NVarChar(50)")]
		public string ctc3
		{
			get
			{
				return this._ctc3;
			}
			set
			{
				if ((this._ctc3 != value))
				{
					this._ctc3 = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_ctm1", DbType="NVarChar(50)")]
		public string ctm1
		{
			get
			{
				return this._ctm1;
			}
			set
			{
				if ((this._ctm1 != value))
				{
					this._ctm1 = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_ctm2", DbType="NVarChar(50)")]
		public string ctm2
		{
			get
			{
				return this._ctm2;
			}
			set
			{
				if ((this._ctm2 != value))
				{
					this._ctm2 = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_ctm3", DbType="NVarChar(50)")]
		public string ctm3
		{
			get
			{
				return this._ctm3;
			}
			set
			{
				if ((this._ctm3 != value))
				{
					this._ctm3 = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_serx", DbType="SmallInt")]
		public System.Nullable<short> serx
		{
			get
			{
				return this._serx;
			}
			set
			{
				if ((this._serx != value))
				{
					this._serx = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_branch", DbType="Int")]
		public System.Nullable<int> branch
		{
			get
			{
				return this._branch;
			}
			set
			{
				if ((this._branch != value))
				{
					this._branch = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_area", DbType="Int")]
		public System.Nullable<int> area
		{
			get
			{
				return this._area;
			}
			set
			{
				if ((this._area != value))
				{
					this._area = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_serv", DbType="NVarChar(250)")]
		public string serv
		{
			get
			{
				return this._serv;
			}
			set
			{
				if ((this._serv != value))
				{
					this._serv = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_manager", DbType="NVarChar(100)")]
		public string manager
		{
			get
			{
				return this._manager;
			}
			set
			{
				if ((this._manager != value))
				{
					this._manager = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_email", DbType="NVarChar(50)")]
		public string email
		{
			get
			{
				return this._email;
			}
			set
			{
				if ((this._email != value))
				{
					this._email = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_type", DbType="Int")]
		public System.Nullable<int> type
		{
			get
			{
				return this._type;
			}
			set
			{
				if ((this._type != value))
				{
					this._type = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_typed", DbType="NVarChar(100)")]
		public string typed
		{
			get
			{
				return this._typed;
			}
			set
			{
				if ((this._typed != value))
				{
					this._typed = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_edate", DbType="DateTime")]
		public System.Nullable<System.DateTime> edate
		{
			get
			{
				return this._edate;
			}
			set
			{
				if ((this._edate != value))
				{
					this._edate = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_ddate", DbType="DateTime")]
		public System.Nullable<System.DateTime> ddate
		{
			get
			{
				return this._ddate;
			}
			set
			{
				if ((this._ddate != value))
				{
					this._ddate = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_cancel", DbType="Int")]
		public System.Nullable<int> cancel
		{
			get
			{
				return this._cancel;
			}
			set
			{
				if ((this._cancel != value))
				{
					this._cancel = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_cdate", DbType="DateTime")]
		public System.Nullable<System.DateTime> cdate
		{
			get
			{
				return this._cdate;
			}
			set
			{
				if ((this._cdate != value))
				{
					this._cdate = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_bank", DbType="NVarChar(100)")]
		public string bank
		{
			get
			{
				return this._bank;
			}
			set
			{
				if ((this._bank != value))
				{
					this._bank = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_swift", DbType="NVarChar(50)")]
		public string swift
		{
			get
			{
				return this._swift;
			}
			set
			{
				if ((this._swift != value))
				{
					this._swift = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_iban", DbType="NVarChar(50)")]
		public string iban
		{
			get
			{
				return this._iban;
			}
			set
			{
				if ((this._iban != value))
				{
					this._iban = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_rout", DbType="NVarChar(50)")]
		public string rout
		{
			get
			{
				return this._rout;
			}
			set
			{
				if ((this._rout != value))
				{
					this._rout = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_rights", DbType="NVarChar(250)")]
		public string rights
		{
			get
			{
				return this._rights;
			}
			set
			{
				if ((this._rights != value))
				{
					this._rights = value;
				}
			}
		}
	}
	
	[global::System.Data.Linq.Mapping.TableAttribute(Name="dbo.store")]
	public partial class store
	{
		
		private string _code;
		
		private string _descx;
		
		private string _address;
		
		private System.Nullable<short> _type;
		
		private System.Nullable<int> _serial;
		
		private string _ref;
		
		private System.Nullable<int> _area;
		
		public store()
		{
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_code", DbType="NVarChar(10)")]
		public string code
		{
			get
			{
				return this._code;
			}
			set
			{
				if ((this._code != value))
				{
					this._code = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_descx", DbType="NVarChar(150)")]
		public string descx
		{
			get
			{
				return this._descx;
			}
			set
			{
				if ((this._descx != value))
				{
					this._descx = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_address", DbType="NVarChar(250)")]
		public string address
		{
			get
			{
				return this._address;
			}
			set
			{
				if ((this._address != value))
				{
					this._address = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_type", DbType="SmallInt")]
		public System.Nullable<short> type
		{
			get
			{
				return this._type;
			}
			set
			{
				if ((this._type != value))
				{
					this._type = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_serial", DbType="Int")]
		public System.Nullable<int> serial
		{
			get
			{
				return this._serial;
			}
			set
			{
				if ((this._serial != value))
				{
					this._serial = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Name="ref", Storage="_ref", DbType="NVarChar(50)")]
		public string @ref
		{
			get
			{
				return this._ref;
			}
			set
			{
				if ((this._ref != value))
				{
					this._ref = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_area", DbType="Int")]
		public System.Nullable<int> area
		{
			get
			{
				return this._area;
			}
			set
			{
				if ((this._area != value))
				{
					this._area = value;
				}
			}
		}
	}
	
	[global::System.Data.Linq.Mapping.TableAttribute(Name="dbo.supp")]
	public partial class supp
	{
		
		private System.Nullable<int> _serial;
		
		private string _code;
		
		private string _descx;
		
		private System.Nullable<short> _price;
		
		private string _ref;
		
		private System.Nullable<short> _status;
		
		private System.Nullable<int> _area;
		
		private System.Nullable<short> _type;
		
		private string _typed;
		
		public supp()
		{
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_serial", DbType="Int")]
		public System.Nullable<int> serial
		{
			get
			{
				return this._serial;
			}
			set
			{
				if ((this._serial != value))
				{
					this._serial = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_code", DbType="NVarChar(50)")]
		public string code
		{
			get
			{
				return this._code;
			}
			set
			{
				if ((this._code != value))
				{
					this._code = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_descx", DbType="NVarChar(250)")]
		public string descx
		{
			get
			{
				return this._descx;
			}
			set
			{
				if ((this._descx != value))
				{
					this._descx = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_price", DbType="SmallInt")]
		public System.Nullable<short> price
		{
			get
			{
				return this._price;
			}
			set
			{
				if ((this._price != value))
				{
					this._price = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Name="ref", Storage="_ref", DbType="NVarChar(50)")]
		public string @ref
		{
			get
			{
				return this._ref;
			}
			set
			{
				if ((this._ref != value))
				{
					this._ref = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_status", DbType="SmallInt")]
		public System.Nullable<short> status
		{
			get
			{
				return this._status;
			}
			set
			{
				if ((this._status != value))
				{
					this._status = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_area", DbType="Int")]
		public System.Nullable<int> area
		{
			get
			{
				return this._area;
			}
			set
			{
				if ((this._area != value))
				{
					this._area = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_type", DbType="SmallInt")]
		public System.Nullable<short> type
		{
			get
			{
				return this._type;
			}
			set
			{
				if ((this._type != value))
				{
					this._type = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_typed", DbType="NVarChar(100)")]
		public string typed
		{
			get
			{
				return this._typed;
			}
			set
			{
				if ((this._typed != value))
				{
					this._typed = value;
				}
			}
		}
	}
	
	[global::System.Data.Linq.Mapping.TableAttribute(Name="dbo.item")]
	public partial class item
	{
		
		private System.Nullable<int> _serial;
		
		private string _code;
		
		private string _descx;
		
		private System.Nullable<int> _store;
		
		private string _logistic;
		
		private System.Nullable<int> _properties;
		
		private bool _prod;
		
		private System.Nullable<short> _useserial;
		
		private System.Nullable<int> _unit;
		
		private System.Nullable<int> _specs;
		
		private System.Nullable<short> _type;
		
		private System.Nullable<int> _tax;
		
		private System.Nullable<double> _discp;
		
		private System.Nullable<double> _discv;
		
		private System.Nullable<double> _addp;
		
		private System.Nullable<double> _addv;
		
		private System.Nullable<double> _taxp;
		
		private System.Nullable<double> _taxv;
		
		private System.Nullable<double> _sprice;
		
		private System.Nullable<double> _pprice;
		
		private System.Nullable<double> _limit;
		
		private string _ref;
		
		private System.Nullable<double> _rorder;
		
		private System.Nullable<double> _weight;
		
		private System.Nullable<int> _publisher;
		
		private System.Nullable<int> _author;
		
		private System.Nullable<int> _orgin;
		
		private System.Nullable<short> _way;
		
		private System.Nullable<short> _manuf;
		
		private System.Nullable<double> _qp;
		
		private System.Nullable<double> _qb;
		
		private System.Nullable<short> _itype;
		
		private System.Nullable<double> _nweight;
		
		private System.Nullable<double> _uprice;
		
		private System.Nullable<int> _ser;
		
		private System.Nullable<double> _basic;
		
		private string _barcode;
		
		private System.Nullable<int> _status;
		
		private System.Nullable<int> _classd;
		
		private string _sdesc;
		
		private string _compos;
		
		private string _used;
		
		private System.Nullable<double> _length;
		
		private System.Nullable<double> _width;
		
		private System.Nullable<double> _height;
		
		private System.Nullable<int> _supp;
		
		private System.Nullable<int> _points;
		
		private System.Nullable<int> _maxbal;
		
		private System.Nullable<double> _cdisc;
		
		private System.Nullable<double> _ptax;
		
		private System.Nullable<double> _mqty;
		
		private System.Nullable<int> _curr;
		
		private System.Nullable<double> _box;
		
		private System.Nullable<double> _packet;
		
		private System.Nullable<double> _maxd;
		
		private System.Nullable<double> _mind;
		
		private System.Nullable<double> _cprice;
		
		public item()
		{
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_serial", DbType="Int")]
		public System.Nullable<int> serial
		{
			get
			{
				return this._serial;
			}
			set
			{
				if ((this._serial != value))
				{
					this._serial = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_code", DbType="NVarChar(50)")]
		public string code
		{
			get
			{
				return this._code;
			}
			set
			{
				if ((this._code != value))
				{
					this._code = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_descx", DbType="NVarChar(250)")]
		public string descx
		{
			get
			{
				return this._descx;
			}
			set
			{
				if ((this._descx != value))
				{
					this._descx = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_store", DbType="Int")]
		public System.Nullable<int> store
		{
			get
			{
				return this._store;
			}
			set
			{
				if ((this._store != value))
				{
					this._store = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_logistic", DbType="NVarChar(50)")]
		public string logistic
		{
			get
			{
				return this._logistic;
			}
			set
			{
				if ((this._logistic != value))
				{
					this._logistic = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_properties", DbType="Int")]
		public System.Nullable<int> properties
		{
			get
			{
				return this._properties;
			}
			set
			{
				if ((this._properties != value))
				{
					this._properties = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_prod", DbType="Bit NOT NULL")]
		public bool prod
		{
			get
			{
				return this._prod;
			}
			set
			{
				if ((this._prod != value))
				{
					this._prod = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_useserial", DbType="SmallInt")]
		public System.Nullable<short> useserial
		{
			get
			{
				return this._useserial;
			}
			set
			{
				if ((this._useserial != value))
				{
					this._useserial = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_unit", DbType="Int")]
		public System.Nullable<int> unit
		{
			get
			{
				return this._unit;
			}
			set
			{
				if ((this._unit != value))
				{
					this._unit = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_specs", DbType="Int")]
		public System.Nullable<int> specs
		{
			get
			{
				return this._specs;
			}
			set
			{
				if ((this._specs != value))
				{
					this._specs = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_type", DbType="SmallInt")]
		public System.Nullable<short> type
		{
			get
			{
				return this._type;
			}
			set
			{
				if ((this._type != value))
				{
					this._type = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_tax", DbType="Int")]
		public System.Nullable<int> tax
		{
			get
			{
				return this._tax;
			}
			set
			{
				if ((this._tax != value))
				{
					this._tax = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_discp", DbType="Float")]
		public System.Nullable<double> discp
		{
			get
			{
				return this._discp;
			}
			set
			{
				if ((this._discp != value))
				{
					this._discp = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_discv", DbType="Float")]
		public System.Nullable<double> discv
		{
			get
			{
				return this._discv;
			}
			set
			{
				if ((this._discv != value))
				{
					this._discv = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_addp", DbType="Float")]
		public System.Nullable<double> addp
		{
			get
			{
				return this._addp;
			}
			set
			{
				if ((this._addp != value))
				{
					this._addp = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_addv", DbType="Float")]
		public System.Nullable<double> addv
		{
			get
			{
				return this._addv;
			}
			set
			{
				if ((this._addv != value))
				{
					this._addv = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_taxp", DbType="Float")]
		public System.Nullable<double> taxp
		{
			get
			{
				return this._taxp;
			}
			set
			{
				if ((this._taxp != value))
				{
					this._taxp = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_taxv", DbType="Float")]
		public System.Nullable<double> taxv
		{
			get
			{
				return this._taxv;
			}
			set
			{
				if ((this._taxv != value))
				{
					this._taxv = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_sprice", DbType="Float")]
		public System.Nullable<double> sprice
		{
			get
			{
				return this._sprice;
			}
			set
			{
				if ((this._sprice != value))
				{
					this._sprice = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_pprice", DbType="Float")]
		public System.Nullable<double> pprice
		{
			get
			{
				return this._pprice;
			}
			set
			{
				if ((this._pprice != value))
				{
					this._pprice = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_limit", DbType="Float")]
		public System.Nullable<double> limit
		{
			get
			{
				return this._limit;
			}
			set
			{
				if ((this._limit != value))
				{
					this._limit = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Name="ref", Storage="_ref", DbType="NVarChar(50)")]
		public string @ref
		{
			get
			{
				return this._ref;
			}
			set
			{
				if ((this._ref != value))
				{
					this._ref = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_rorder", DbType="Float")]
		public System.Nullable<double> rorder
		{
			get
			{
				return this._rorder;
			}
			set
			{
				if ((this._rorder != value))
				{
					this._rorder = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_weight", DbType="Float")]
		public System.Nullable<double> weight
		{
			get
			{
				return this._weight;
			}
			set
			{
				if ((this._weight != value))
				{
					this._weight = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_publisher", DbType="Int")]
		public System.Nullable<int> publisher
		{
			get
			{
				return this._publisher;
			}
			set
			{
				if ((this._publisher != value))
				{
					this._publisher = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_author", DbType="Int")]
		public System.Nullable<int> author
		{
			get
			{
				return this._author;
			}
			set
			{
				if ((this._author != value))
				{
					this._author = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_orgin", DbType="Int")]
		public System.Nullable<int> orgin
		{
			get
			{
				return this._orgin;
			}
			set
			{
				if ((this._orgin != value))
				{
					this._orgin = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_way", DbType="SmallInt")]
		public System.Nullable<short> way
		{
			get
			{
				return this._way;
			}
			set
			{
				if ((this._way != value))
				{
					this._way = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_manuf", DbType="SmallInt")]
		public System.Nullable<short> manuf
		{
			get
			{
				return this._manuf;
			}
			set
			{
				if ((this._manuf != value))
				{
					this._manuf = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_qp", DbType="Float")]
		public System.Nullable<double> qp
		{
			get
			{
				return this._qp;
			}
			set
			{
				if ((this._qp != value))
				{
					this._qp = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_qb", DbType="Float")]
		public System.Nullable<double> qb
		{
			get
			{
				return this._qb;
			}
			set
			{
				if ((this._qb != value))
				{
					this._qb = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_itype", DbType="SmallInt")]
		public System.Nullable<short> itype
		{
			get
			{
				return this._itype;
			}
			set
			{
				if ((this._itype != value))
				{
					this._itype = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_nweight", DbType="Float")]
		public System.Nullable<double> nweight
		{
			get
			{
				return this._nweight;
			}
			set
			{
				if ((this._nweight != value))
				{
					this._nweight = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_uprice", DbType="Float")]
		public System.Nullable<double> uprice
		{
			get
			{
				return this._uprice;
			}
			set
			{
				if ((this._uprice != value))
				{
					this._uprice = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_ser", DbType="Int")]
		public System.Nullable<int> ser
		{
			get
			{
				return this._ser;
			}
			set
			{
				if ((this._ser != value))
				{
					this._ser = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_basic", DbType="Float")]
		public System.Nullable<double> basic
		{
			get
			{
				return this._basic;
			}
			set
			{
				if ((this._basic != value))
				{
					this._basic = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_barcode", DbType="NVarChar(50)")]
		public string barcode
		{
			get
			{
				return this._barcode;
			}
			set
			{
				if ((this._barcode != value))
				{
					this._barcode = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_status", DbType="Int")]
		public System.Nullable<int> status
		{
			get
			{
				return this._status;
			}
			set
			{
				if ((this._status != value))
				{
					this._status = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_classd", DbType="Int")]
		public System.Nullable<int> classd
		{
			get
			{
				return this._classd;
			}
			set
			{
				if ((this._classd != value))
				{
					this._classd = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_sdesc", DbType="NVarChar(150)")]
		public string sdesc
		{
			get
			{
				return this._sdesc;
			}
			set
			{
				if ((this._sdesc != value))
				{
					this._sdesc = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_compos", DbType="NVarChar(100)")]
		public string compos
		{
			get
			{
				return this._compos;
			}
			set
			{
				if ((this._compos != value))
				{
					this._compos = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_used", DbType="NVarChar(100)")]
		public string used
		{
			get
			{
				return this._used;
			}
			set
			{
				if ((this._used != value))
				{
					this._used = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_length", DbType="Float")]
		public System.Nullable<double> length
		{
			get
			{
				return this._length;
			}
			set
			{
				if ((this._length != value))
				{
					this._length = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_width", DbType="Float")]
		public System.Nullable<double> width
		{
			get
			{
				return this._width;
			}
			set
			{
				if ((this._width != value))
				{
					this._width = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_height", DbType="Float")]
		public System.Nullable<double> height
		{
			get
			{
				return this._height;
			}
			set
			{
				if ((this._height != value))
				{
					this._height = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_supp", DbType="Int")]
		public System.Nullable<int> supp
		{
			get
			{
				return this._supp;
			}
			set
			{
				if ((this._supp != value))
				{
					this._supp = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_points", DbType="Int")]
		public System.Nullable<int> points
		{
			get
			{
				return this._points;
			}
			set
			{
				if ((this._points != value))
				{
					this._points = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_maxbal", DbType="Int")]
		public System.Nullable<int> maxbal
		{
			get
			{
				return this._maxbal;
			}
			set
			{
				if ((this._maxbal != value))
				{
					this._maxbal = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_cdisc", DbType="Float")]
		public System.Nullable<double> cdisc
		{
			get
			{
				return this._cdisc;
			}
			set
			{
				if ((this._cdisc != value))
				{
					this._cdisc = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_ptax", DbType="Float")]
		public System.Nullable<double> ptax
		{
			get
			{
				return this._ptax;
			}
			set
			{
				if ((this._ptax != value))
				{
					this._ptax = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_mqty", DbType="Float")]
		public System.Nullable<double> mqty
		{
			get
			{
				return this._mqty;
			}
			set
			{
				if ((this._mqty != value))
				{
					this._mqty = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_curr", DbType="Int")]
		public System.Nullable<int> curr
		{
			get
			{
				return this._curr;
			}
			set
			{
				if ((this._curr != value))
				{
					this._curr = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_box", DbType="Float")]
		public System.Nullable<double> box
		{
			get
			{
				return this._box;
			}
			set
			{
				if ((this._box != value))
				{
					this._box = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_packet", DbType="Float")]
		public System.Nullable<double> packet
		{
			get
			{
				return this._packet;
			}
			set
			{
				if ((this._packet != value))
				{
					this._packet = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_maxd", DbType="Float")]
		public System.Nullable<double> maxd
		{
			get
			{
				return this._maxd;
			}
			set
			{
				if ((this._maxd != value))
				{
					this._maxd = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_mind", DbType="Float")]
		public System.Nullable<double> mind
		{
			get
			{
				return this._mind;
			}
			set
			{
				if ((this._mind != value))
				{
					this._mind = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_cprice", DbType="Float")]
		public System.Nullable<double> cprice
		{
			get
			{
				return this._cprice;
			}
			set
			{
				if ((this._cprice != value))
				{
					this._cprice = value;
				}
			}
		}
	}
	
	[global::System.Data.Linq.Mapping.TableAttribute(Name="dbo.asset")]
	public partial class asset
	{
		
		private System.Nullable<int> _serial;
		
		private string _ref;
		
		private string _code;
		
		private string _descx;
		
		private System.Nullable<System.DateTime> _date;
		
		private System.Nullable<double> _price;
		
		private System.Nullable<double> _dep;
		
		private System.Nullable<int> _acc;
		
		private System.Nullable<int> _typed;
		
		private System.Nullable<int> _supp;
		
		private string _comment;
		
		private System.Nullable<double> _dtax;
		
		public asset()
		{
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_serial", DbType="Int")]
		public System.Nullable<int> serial
		{
			get
			{
				return this._serial;
			}
			set
			{
				if ((this._serial != value))
				{
					this._serial = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Name="ref", Storage="_ref", DbType="NVarChar(50)")]
		public string @ref
		{
			get
			{
				return this._ref;
			}
			set
			{
				if ((this._ref != value))
				{
					this._ref = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_code", DbType="NVarChar(50)")]
		public string code
		{
			get
			{
				return this._code;
			}
			set
			{
				if ((this._code != value))
				{
					this._code = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_descx", DbType="NVarChar(150)")]
		public string descx
		{
			get
			{
				return this._descx;
			}
			set
			{
				if ((this._descx != value))
				{
					this._descx = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_date", DbType="DateTime")]
		public System.Nullable<System.DateTime> date
		{
			get
			{
				return this._date;
			}
			set
			{
				if ((this._date != value))
				{
					this._date = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_price", DbType="Float")]
		public System.Nullable<double> price
		{
			get
			{
				return this._price;
			}
			set
			{
				if ((this._price != value))
				{
					this._price = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_dep", DbType="Float")]
		public System.Nullable<double> dep
		{
			get
			{
				return this._dep;
			}
			set
			{
				if ((this._dep != value))
				{
					this._dep = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_acc", DbType="Int")]
		public System.Nullable<int> acc
		{
			get
			{
				return this._acc;
			}
			set
			{
				if ((this._acc != value))
				{
					this._acc = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_typed", DbType="Int")]
		public System.Nullable<int> typed
		{
			get
			{
				return this._typed;
			}
			set
			{
				if ((this._typed != value))
				{
					this._typed = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_supp", DbType="Int")]
		public System.Nullable<int> supp
		{
			get
			{
				return this._supp;
			}
			set
			{
				if ((this._supp != value))
				{
					this._supp = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_comment", DbType="NVarChar(250)")]
		public string comment
		{
			get
			{
				return this._comment;
			}
			set
			{
				if ((this._comment != value))
				{
					this._comment = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_dtax", DbType="Float")]
		public System.Nullable<double> dtax
		{
			get
			{
				return this._dtax;
			}
			set
			{
				if ((this._dtax != value))
				{
					this._dtax = value;
				}
			}
		}
	}
	
	[global::System.Data.Linq.Mapping.TableAttribute(Name="dbo.assetd")]
	public partial class assetd
	{
		
		private System.Nullable<int> _serial;
		
		private System.Nullable<System.DateTime> _date;
		
		private System.Nullable<double> _amount;
		
		private string _desc;
		
		private System.Nullable<int> _acc;
		
		private System.Nullable<int> _typed;
		
		private System.Nullable<int> _supp;
		
		private string _comment;
		
		private int _serx;
		
		private System.Nullable<double> _dtax;
		
		public assetd()
		{
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_serial", DbType="Int")]
		public System.Nullable<int> serial
		{
			get
			{
				return this._serial;
			}
			set
			{
				if ((this._serial != value))
				{
					this._serial = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_date", DbType="DateTime")]
		public System.Nullable<System.DateTime> date
		{
			get
			{
				return this._date;
			}
			set
			{
				if ((this._date != value))
				{
					this._date = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_amount", DbType="Float")]
		public System.Nullable<double> amount
		{
			get
			{
				return this._amount;
			}
			set
			{
				if ((this._amount != value))
				{
					this._amount = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Name="[desc]", Storage="_desc", DbType="NVarChar(150)")]
		public string desc
		{
			get
			{
				return this._desc;
			}
			set
			{
				if ((this._desc != value))
				{
					this._desc = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_acc", DbType="Int")]
		public System.Nullable<int> acc
		{
			get
			{
				return this._acc;
			}
			set
			{
				if ((this._acc != value))
				{
					this._acc = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_typed", DbType="Int")]
		public System.Nullable<int> typed
		{
			get
			{
				return this._typed;
			}
			set
			{
				if ((this._typed != value))
				{
					this._typed = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_supp", DbType="Int")]
		public System.Nullable<int> supp
		{
			get
			{
				return this._supp;
			}
			set
			{
				if ((this._supp != value))
				{
					this._supp = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_comment", DbType="NVarChar(250)")]
		public string comment
		{
			get
			{
				return this._comment;
			}
			set
			{
				if ((this._comment != value))
				{
					this._comment = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_serx", AutoSync=AutoSync.Always, DbType="Int NOT NULL IDENTITY", IsDbGenerated=true)]
		public int serx
		{
			get
			{
				return this._serx;
			}
			set
			{
				if ((this._serx != value))
				{
					this._serx = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_dtax", DbType="Float")]
		public System.Nullable<double> dtax
		{
			get
			{
				return this._dtax;
			}
			set
			{
				if ((this._dtax != value))
				{
					this._dtax = value;
				}
			}
		}
	}
	
	[global::System.Data.Linq.Mapping.TableAttribute(Name="dbo.enterprise")]
	public partial class enterprise
	{
		
		private string _code;
		
		private string _descx;
		
		private System.Nullable<short> _type;
		
		private string _ref;
		
		private System.Nullable<double> _pamount;
		
		private System.Nullable<double> _pqty;
		
		private System.Nullable<double> _samount;
		
		private System.Nullable<double> _sqty;
		
		private System.Nullable<double> _value;
		
		private System.Nullable<double> _profit;
		
		private System.Nullable<System.DateTime> _edate;
		
		private string _pid;
		
		private int _serial;
		
		private System.Nullable<int> _user;
		
		private System.Nullable<double> _limit;
		
		private System.Nullable<short> _limitd;
		
		private System.Nullable<short> _status;
		
		private System.Nullable<short> _ser;
		
		private string _profile;
		
		private System.Nullable<double> _sprice;
		
		private System.Nullable<double> _pprice;
		
		private System.Nullable<double> _pprice1;
		
		private System.Nullable<double> _sprice1;
		
		private System.Nullable<int> _acc;
		
		private string _keyd;
		
		private string _bind;
		
		private System.Nullable<int> _ruled;
		
		public enterprise()
		{
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_code", DbType="NVarChar(50)")]
		public string code
		{
			get
			{
				return this._code;
			}
			set
			{
				if ((this._code != value))
				{
					this._code = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_descx", DbType="NVarChar(150)")]
		public string descx
		{
			get
			{
				return this._descx;
			}
			set
			{
				if ((this._descx != value))
				{
					this._descx = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_type", DbType="SmallInt")]
		public System.Nullable<short> type
		{
			get
			{
				return this._type;
			}
			set
			{
				if ((this._type != value))
				{
					this._type = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Name="ref", Storage="_ref", DbType="NVarChar(50)")]
		public string @ref
		{
			get
			{
				return this._ref;
			}
			set
			{
				if ((this._ref != value))
				{
					this._ref = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_pamount", DbType="Float")]
		public System.Nullable<double> pamount
		{
			get
			{
				return this._pamount;
			}
			set
			{
				if ((this._pamount != value))
				{
					this._pamount = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_pqty", DbType="Float")]
		public System.Nullable<double> pqty
		{
			get
			{
				return this._pqty;
			}
			set
			{
				if ((this._pqty != value))
				{
					this._pqty = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_samount", DbType="Float")]
		public System.Nullable<double> samount
		{
			get
			{
				return this._samount;
			}
			set
			{
				if ((this._samount != value))
				{
					this._samount = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_sqty", DbType="Float")]
		public System.Nullable<double> sqty
		{
			get
			{
				return this._sqty;
			}
			set
			{
				if ((this._sqty != value))
				{
					this._sqty = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_value", DbType="Float")]
		public System.Nullable<double> value
		{
			get
			{
				return this._value;
			}
			set
			{
				if ((this._value != value))
				{
					this._value = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_profit", DbType="Float")]
		public System.Nullable<double> profit
		{
			get
			{
				return this._profit;
			}
			set
			{
				if ((this._profit != value))
				{
					this._profit = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_edate", DbType="DateTime")]
		public System.Nullable<System.DateTime> edate
		{
			get
			{
				return this._edate;
			}
			set
			{
				if ((this._edate != value))
				{
					this._edate = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_pid", DbType="NVarChar(50)")]
		public string pid
		{
			get
			{
				return this._pid;
			}
			set
			{
				if ((this._pid != value))
				{
					this._pid = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_serial", AutoSync=AutoSync.Always, DbType="Int NOT NULL IDENTITY", IsDbGenerated=true)]
		public int serial
		{
			get
			{
				return this._serial;
			}
			set
			{
				if ((this._serial != value))
				{
					this._serial = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Name="[user]", Storage="_user", DbType="Int")]
		public System.Nullable<int> user
		{
			get
			{
				return this._user;
			}
			set
			{
				if ((this._user != value))
				{
					this._user = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_limit", DbType="Float")]
		public System.Nullable<double> limit
		{
			get
			{
				return this._limit;
			}
			set
			{
				if ((this._limit != value))
				{
					this._limit = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_limitd", DbType="SmallInt")]
		public System.Nullable<short> limitd
		{
			get
			{
				return this._limitd;
			}
			set
			{
				if ((this._limitd != value))
				{
					this._limitd = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_status", DbType="SmallInt")]
		public System.Nullable<short> status
		{
			get
			{
				return this._status;
			}
			set
			{
				if ((this._status != value))
				{
					this._status = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_ser", DbType="SmallInt")]
		public System.Nullable<short> ser
		{
			get
			{
				return this._ser;
			}
			set
			{
				if ((this._ser != value))
				{
					this._ser = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_profile", DbType="NVarChar(500)")]
		public string profile
		{
			get
			{
				return this._profile;
			}
			set
			{
				if ((this._profile != value))
				{
					this._profile = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_sprice", DbType="Float")]
		public System.Nullable<double> sprice
		{
			get
			{
				return this._sprice;
			}
			set
			{
				if ((this._sprice != value))
				{
					this._sprice = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_pprice", DbType="Float")]
		public System.Nullable<double> pprice
		{
			get
			{
				return this._pprice;
			}
			set
			{
				if ((this._pprice != value))
				{
					this._pprice = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_pprice1", DbType="Float")]
		public System.Nullable<double> pprice1
		{
			get
			{
				return this._pprice1;
			}
			set
			{
				if ((this._pprice1 != value))
				{
					this._pprice1 = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_sprice1", DbType="Float")]
		public System.Nullable<double> sprice1
		{
			get
			{
				return this._sprice1;
			}
			set
			{
				if ((this._sprice1 != value))
				{
					this._sprice1 = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_acc", DbType="Int")]
		public System.Nullable<int> acc
		{
			get
			{
				return this._acc;
			}
			set
			{
				if ((this._acc != value))
				{
					this._acc = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_keyd", DbType="NVarChar(250)")]
		public string keyd
		{
			get
			{
				return this._keyd;
			}
			set
			{
				if ((this._keyd != value))
				{
					this._keyd = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_bind", DbType="NVarChar(50)")]
		public string bind
		{
			get
			{
				return this._bind;
			}
			set
			{
				if ((this._bind != value))
				{
					this._bind = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_ruled", DbType="Int")]
		public System.Nullable<int> ruled
		{
			get
			{
				return this._ruled;
			}
			set
			{
				if ((this._ruled != value))
				{
					this._ruled = value;
				}
			}
		}
	}
	
	[global::System.Data.Linq.Mapping.TableAttribute(Name="dbo.clientitem")]
	public partial class clientitem
	{
		
		private System.Nullable<int> _serial;
		
		private string _ref;
		
		private string _descx;
		
		private System.Nullable<int> _client;
		
		public clientitem()
		{
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_serial", DbType="Int")]
		public System.Nullable<int> serial
		{
			get
			{
				return this._serial;
			}
			set
			{
				if ((this._serial != value))
				{
					this._serial = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Name="ref", Storage="_ref", DbType="NVarChar(50)")]
		public string @ref
		{
			get
			{
				return this._ref;
			}
			set
			{
				if ((this._ref != value))
				{
					this._ref = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_descx", DbType="NVarChar(100)")]
		public string descx
		{
			get
			{
				return this._descx;
			}
			set
			{
				if ((this._descx != value))
				{
					this._descx = value;
				}
			}
		}
		
		[global::System.Data.Linq.Mapping.ColumnAttribute(Storage="_client", DbType="Int")]
		public System.Nullable<int> client
		{
			get
			{
				return this._client;
			}
			set
			{
				if ((this._client != value))
				{
					this._client = value;
				}
			}
		}
	}
}
#pragma warning restore 1591
