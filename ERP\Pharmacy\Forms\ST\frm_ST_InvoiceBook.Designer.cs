﻿namespace Pharmacy.Forms
{
    partial class frm_ST_InvoiceBook
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_ST_InvoiceBook));
            this.barManager1 = new DevExpress.XtraBars.BarManager();
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtnHelp = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnDelete = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnNew = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnSave = new DevExpress.XtraBars.BarButtonItem();
            this.barBtn_Cancel = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.barButtonItem1 = new DevExpress.XtraBars.BarButtonItem();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.barButtonItem8 = new DevExpress.XtraBars.BarButtonItem();
            this.labelControl36 = new DevExpress.XtraEditors.LabelControl();
            this.btnNext = new DevExpress.XtraEditors.SimpleButton();
            this.btnPrev = new DevExpress.XtraEditors.SimpleButton();
            this.lkpProcess = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.txtPrintFileName = new DevExpress.XtraEditors.TextEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.cmbTaxable = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.txtBookName = new DevExpress.XtraEditors.TextEdit();
            this.labelControl22 = new DevExpress.XtraEditors.LabelControl();
            this.txtNotes = new DevExpress.XtraEditors.MemoEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpProcess.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPrintFileName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbTaxable.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtBookName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNotes.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnSave,
            this.barBtn_Cancel,
            this.barBtnNew,
            this.barButtonItem1,
            this.barBtnHelp,
            this.barBtnDelete});
            this.barManager1.MaxItemId = 30;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(377, 152);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnHelp),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnDelete),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnNew),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnSave),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_Cancel)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtnHelp
            // 
            this.barBtnHelp.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnHelp, "barBtnHelp");
            this.barBtnHelp.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtnHelp.Id = 28;
            this.barBtnHelp.Name = "barBtnHelp";
            this.barBtnHelp.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnHelp.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnHelp_ItemClick);
            // 
            // barBtnDelete
            // 
            this.barBtnDelete.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnDelete, "barBtnDelete");
            this.barBtnDelete.Glyph = global::Pharmacy.Properties.Resources.del;
            this.barBtnDelete.Id = 29;
            this.barBtnDelete.Name = "barBtnDelete";
            this.barBtnDelete.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnDelete.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnDelete_ItemClick);
            // 
            // barBtnNew
            // 
            this.barBtnNew.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnNew, "barBtnNew");
            this.barBtnNew.Glyph = global::Pharmacy.Properties.Resources._new;
            this.barBtnNew.Id = 24;
            this.barBtnNew.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.N));
            this.barBtnNew.Name = "barBtnNew";
            this.barBtnNew.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnNew.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_New_ItemClick);
            // 
            // barBtnSave
            // 
            this.barBtnSave.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnSave, "barBtnSave");
            this.barBtnSave.Glyph = global::Pharmacy.Properties.Resources.sve;
            this.barBtnSave.Id = 0;
            this.barBtnSave.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.S));
            this.barBtnSave.Name = "barBtnSave";
            this.barBtnSave.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnSave.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Save_ItemClick);
            // 
            // barBtn_Cancel
            // 
            this.barBtn_Cancel.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtn_Cancel, "barBtn_Cancel");
            this.barBtn_Cancel.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtn_Cancel.Id = 1;
            this.barBtn_Cancel.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtn_Cancel.Name = "barBtn_Cancel";
            this.barBtn_Cancel.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtn_Cancel.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Cancel_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            // 
            // barButtonItem1
            // 
            resources.ApplyResources(this.barButtonItem1, "barButtonItem1");
            this.barButtonItem1.Id = 27;
            this.barButtonItem1.Name = "barButtonItem1";
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // barButtonItem8
            // 
            this.barButtonItem8.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barButtonItem8, "barButtonItem8");
            this.barButtonItem8.Id = 2;
            this.barButtonItem8.Name = "barButtonItem8";
            // 
            // labelControl36
            // 
            resources.ApplyResources(this.labelControl36, "labelControl36");
            this.labelControl36.Name = "labelControl36";
            // 
            // btnNext
            // 
            this.btnNext.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnNext.Image = global::Pharmacy.Properties.Resources.nxt;
            resources.ApplyResources(this.btnNext, "btnNext");
            this.btnNext.Name = "btnNext";
            this.btnNext.TabStop = false;
            this.btnNext.Click += new System.EventHandler(this.btnNext_Click);
            // 
            // btnPrev
            // 
            this.btnPrev.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnPrev.Image = global::Pharmacy.Properties.Resources.prev32;
            resources.ApplyResources(this.btnPrev, "btnPrev");
            this.btnPrev.Name = "btnPrev";
            this.btnPrev.TabStop = false;
            this.btnPrev.Click += new System.EventHandler(this.btnPrev_Click);
            // 
            // lkpProcess
            // 
            resources.ApplyResources(this.lkpProcess, "lkpProcess");
            this.lkpProcess.EnterMoveNextControl = true;
            this.lkpProcess.MenuManager = this.barManager1;
            this.lkpProcess.Name = "lkpProcess";
            this.lkpProcess.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkpProcess.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpProcess.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpProcess.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpProcess.Properties.Buttons"))))});
            this.lkpProcess.Properties.NullText = resources.GetString("lkpProcess.Properties.NullText");
            this.lkpProcess.EditValueChanged += new System.EventHandler(this.lkpProcess_EditValueChanged);
            this.lkpProcess.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // labelControl1
            // 
            resources.ApplyResources(this.labelControl1, "labelControl1");
            this.labelControl1.Name = "labelControl1";
            // 
            // txtPrintFileName
            // 
            resources.ApplyResources(this.txtPrintFileName, "txtPrintFileName");
            this.txtPrintFileName.EnterMoveNextControl = true;
            this.txtPrintFileName.Name = "txtPrintFileName";
            this.txtPrintFileName.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txtPrintFileName.Properties.Appearance.Options.UseTextOptions = true;
            this.txtPrintFileName.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtPrintFileName.Properties.Mask.EditMask = resources.GetString("txtPrintFileName.Properties.Mask.EditMask");
            this.txtPrintFileName.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // labelControl4
            // 
            resources.ApplyResources(this.labelControl4, "labelControl4");
            this.labelControl4.Name = "labelControl4";
            // 
            // labelControl3
            // 
            resources.ApplyResources(this.labelControl3, "labelControl3");
            this.labelControl3.Name = "labelControl3";
            // 
            // cmbTaxable
            // 
            resources.ApplyResources(this.cmbTaxable, "cmbTaxable");
            this.cmbTaxable.EnterMoveNextControl = true;
            this.cmbTaxable.MenuManager = this.barManager1;
            this.cmbTaxable.Name = "cmbTaxable";
            this.cmbTaxable.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.cmbTaxable.Properties.Appearance.Options.UseTextOptions = true;
            this.cmbTaxable.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.cmbTaxable.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("cmbTaxable.Properties.Buttons"))))});
            this.cmbTaxable.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbTaxable.Properties.Items"), ((object)(resources.GetObject("cmbTaxable.Properties.Items1"))), ((int)(resources.GetObject("cmbTaxable.Properties.Items2")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbTaxable.Properties.Items3"), ((object)(resources.GetObject("cmbTaxable.Properties.Items4"))), ((int)(resources.GetObject("cmbTaxable.Properties.Items5")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbTaxable.Properties.Items6"), ((object)(resources.GetObject("cmbTaxable.Properties.Items7"))), ((int)(resources.GetObject("cmbTaxable.Properties.Items8"))))});
            this.cmbTaxable.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // txtBookName
            // 
            resources.ApplyResources(this.txtBookName, "txtBookName");
            this.txtBookName.EnterMoveNextControl = true;
            this.txtBookName.Name = "txtBookName";
            this.txtBookName.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txtBookName.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("txtBookName.Properties.Appearance.Font")));
            this.txtBookName.Properties.Appearance.Options.UseFont = true;
            this.txtBookName.Properties.Appearance.Options.UseTextOptions = true;
            this.txtBookName.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtBookName.Properties.Mask.EditMask = resources.GetString("txtBookName.Properties.Mask.EditMask");
            this.txtBookName.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // labelControl22
            // 
            resources.ApplyResources(this.labelControl22, "labelControl22");
            this.labelControl22.Name = "labelControl22";
            // 
            // txtNotes
            // 
            resources.ApplyResources(this.txtNotes, "txtNotes");
            this.txtNotes.MenuManager = this.barManager1;
            this.txtNotes.Name = "txtNotes";
            this.txtNotes.Properties.ScrollBars = System.Windows.Forms.ScrollBars.Both;
            this.txtNotes.Modified += new System.EventHandler(this.txt_TemplateName_Modified);
            // 
            // labelControl5
            // 
            resources.ApplyResources(this.labelControl5, "labelControl5");
            this.labelControl5.Name = "labelControl5";
            // 
            // frm_ST_InvoiceBook
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.lkpProcess);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.txtPrintFileName);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.cmbTaxable);
            this.Controls.Add(this.txtBookName);
            this.Controls.Add(this.labelControl22);
            this.Controls.Add(this.txtNotes);
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.labelControl36);
            this.Controls.Add(this.btnNext);
            this.Controls.Add(this.btnPrev);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.KeyPreview = true;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "frm_ST_InvoiceBook";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_ST_Barcode_FormClosing);
            this.Load += new System.EventHandler(this.frm_ST_Barcode_Load);
            this.KeyDown += new System.Windows.Forms.KeyEventHandler(this.frm_ST_Barcode_KeyDown);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpProcess.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPrintFileName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbTaxable.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtBookName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNotes.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnSave;
        private DevExpress.XtraBars.BarButtonItem barBtn_Cancel;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraBars.BarButtonItem barBtnNew;
        private DevExpress.XtraBars.BarButtonItem barButtonItem8;
        private DevExpress.XtraBars.BarButtonItem barButtonItem1;
        private DevExpress.XtraBars.BarButtonItem barBtnHelp;
        private DevExpress.XtraEditors.LabelControl labelControl36;
        private DevExpress.XtraEditors.SimpleButton btnNext;
        private DevExpress.XtraEditors.SimpleButton btnPrev;
        private DevExpress.XtraBars.BarButtonItem barBtnDelete;
        private DevExpress.XtraEditors.LookUpEdit lkpProcess;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.TextEdit txtPrintFileName;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.ImageComboBoxEdit cmbTaxable;
        private DevExpress.XtraEditors.TextEdit txtBookName;
        private DevExpress.XtraEditors.LabelControl labelControl22;
        private DevExpress.XtraEditors.MemoEdit txtNotes;
        private DevExpress.XtraEditors.LabelControl labelControl5;
    }
}