﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;

using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;

using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraReports.UI;

namespace Reports
{
    public partial class frm_IC_StoreSales : DevExpress.XtraEditors.XtraForm
    {
        public bool UserCanOpen;
        string reportName, dateFilter, otherFilters;

        int store_id1, store_id2, custGroupId;

        string custGroupAccNumber;

        byte FltrTyp_Store, fltrTyp_Date;
        DateTime date1, date2;

        List<int> lst_invBooksId = new List<int>();

        List<int> lstStores = new List<int>();

        private void btn_Landscape_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCategory, true).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        private void btn_Portrait_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCategory, false).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        public frm_IC_StoreSales(string reportName, string dateFilter, string otherFilters,
            byte fltrTyp_Store, int store_id1, int store_id2,
            byte fltrTyp_Date, DateTime date1, DateTime date2,
            int custGroupId, string custGroupAccNumber)
        {
            UserCanOpen = LoadPrivilege();
            if (UserCanOpen == false)
                return;

            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();

            this.reportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;

            this.FltrTyp_Store = fltrTyp_Store;
            this.fltrTyp_Date = fltrTyp_Date;

            this.store_id1 = store_id1;
            this.store_id2 = store_id2;

            this.date1 = date1;
            this.date2 = date2;

            this.custGroupId = custGroupId;
            this.custGroupAccNumber = custGroupAccNumber;

            getReportHeader();

            LoadData();

            ReportsUtils.ColumnChooser(grdCategory);
        }

        private void frm_SL_InvoiceList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                ReportsRTL.LTRLayout(this);

            ReportsUtils.Load_Grid_Layout(grdCategory, this.Name.Replace("frm_", "Rpt_"));

            ERPDataContext DB = new DAL.ERPDataContext();


            rep_CategoryId.DataSource = DB.SL_CustomerGroups;
            rep_CategoryId.ValueMember = "CustomerGroupId";
            rep_CategoryId.DisplayMember = Shared.IsEnglish ? "CGNameEn" : "CGNameAr";
            //LoadPrivilege();
        }

        private void frm_Rep_FormClosing(object sender, FormClosingEventArgs e)
        {
            ReportsUtils.save_Grid_Layout(grdCategory, this.Name.Replace("frm_", "Rpt_"), true);
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Preview_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //grdCategory.MinimumSize = grdCategory.Size;
            //new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
            //        lblFilter.Text.Trim(), "", grdCategory, true).ShowPreview();
            //grdCategory.MinimumSize = new Size(0, 0);
        }

        private void barBtnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadData();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grdCategory.MinimumSize = grdCategory.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grdCategory, true, true).ShowPreview();
                grdCategory.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                //lblCompName.Text = comp.CmpNameAr;
                lblReportName.Text = this.Text = reportName;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        void LoadData()
        {
            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            try
            {
                var stores = DB.IC_Stores.ToList();
                foreach (var store in stores)
                {
                    if (FltrTyp_Store == 2)
                    {
                        if (store.StoreId <= store_id2 && store.StoreId >= store_id1)
                        {
                            lstStores.Add(store.StoreId);
                        }
                    }
                    else if (FltrTyp_Store == 0)
                    {
                        lstStores.Add(store.StoreId);
                    }
                    else if (store_id1 > 0 && (store.StoreId == store_id1 || store.ParentId == store_id1))
                        lstStores.Add(store.StoreId);
                    //else if (store_id2 > 0 && (store.StoreId == store_id2 || store.ParentId == store_id2))
                    //    lstStores.Add(store.StoreId);
                }

                var data1 = (from c in DB.SL_Invoices
                             join v in DB.SL_Customers on c.CustomerId equals v.CustomerId
                             join a in DB.ACC_Accounts on v.AccountId equals a.AccountId
                             where custGroupId == 0 ? true : a.AcNumber.StartsWith(custGroupAccNumber)

                             where fltrTyp_Date == 1 ? c.InvoiceDate.Date == date1.Date : true
                             where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                             c.InvoiceDate.Date >= date1.Date && c.InvoiceDate.Date <= date2.Date : true
                             where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                             c.InvoiceDate.Date >= date1.Date : true
                             where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                             c.InvoiceDate.Date <= date2.Date : true

                             where lstStores.Count > 0 ? lstStores.Contains(c.StoreId) : true

                             group c by new { c.StoreId } into grp
                             //let custGroup = DB.SL_Group_Customers
                             select new
                             {
                                 /* الصـــــافي*/
                                 Net = grp.Select(x => x.Net).Sum() - grp.Select(x => x.TaxValue).Sum() + grp.Select(x => x.DiscountValue).Sum()
                                 - grp.Select(x => x.Expenses).Sum() + grp.Select(x => x.DeductTaxValue).Sum() - grp.Select(x => x.AddTaxValue).Sum()
                                 - grp.Select(x => x.CustomTaxValue).Sum(),
                                 grp.Key.StoreId,
                                 /* الإجمـــــــــالى*/
                                 Total = Math.Round((double)grp.ToList().DefaultIfEmpty().Sum(x => x.Net - x.DiscountValue 
                                        - x.CustomTaxValue - x.AddTaxValue + x.DeductTaxValue + x.DiscountValue + x.RetentionValue 
                                        + x.AdvancePaymentValue - x.Expenses - x.HandingValue 
                                        - x.TransportationValue - x.ShiftAdd), 3, MidpointRounding.AwayFromZero)

                             }).ToList();
                
                var data2 = (from c in DB.SL_Returns
                            join v in DB.SL_Customers on c.CustomerId equals v.CustomerId
                            join a in DB.ACC_Accounts on v.AccountId equals a.AccountId
                            where custGroupId == 0 ? true : a.AcNumber.StartsWith(custGroupAccNumber)

                            where fltrTyp_Date == 1 ? c.ReturnDate.Date == date1.Date : true
                            where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                            c.ReturnDate.Date >= date1.Date && c.ReturnDate.Date <= date2.Date : true
                            where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                            c.ReturnDate.Date >= date1.Date : true
                            where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                            c.ReturnDate.Date <= date2.Date : true

                            where lstStores.Count > 0 ? lstStores.Contains(c.StoreId) : true

                             //orderby c.InvoiceDate


                             group c by new { c.StoreId } into grp
                             //let custGroup = DB.SL_Group_Customers
                             select new
                             {
                                 /* الصـــــافي*/
                                 Net = grp.Select(x => x.Net).Sum() - grp.Select(x => x.TaxValue).Sum() + grp.Select(x => x.DiscountValue).Sum()
                                 - grp.Select(x => x.Expenses).Sum() + grp.Select(x => x.DeductTaxValue).Sum() - grp.Select(x => x.AddTaxValue).Sum()
                                 - grp.Select(x => x.CustomTaxValue).Sum(),
                                 grp.Key.StoreId,
                                 /* الإجمـــــــــالى*/
                                 Total = Math.Round((double)grp.ToList().DefaultIfEmpty().Sum(x => x.Net - x.DiscountValue - x.CustomTaxValue
                                        - x.AddTaxValue + x.DeductTaxValue + x.DiscountValue 
                                        - x.Expenses - x.HandingValue), 3, MidpointRounding.AwayFromZero),
                             }).ToList();


                var data = (from d1 in data1

                            select new
                            {
                                d1.StoreId,
                                NetSales = (double)d1.Net,
                                NetReturns = data2.Where(x => x.StoreId == d1.StoreId).Count() > 0 ? (double)data2.Where(x => x.StoreId == d1.StoreId).Select(x => x.Net).Sum() : 0,
                                Net = (double)(d1.Net - (data2.Where(x => x.StoreId == d1.StoreId).Count() > 0 ? data2.Where(x => x.StoreId == d1.StoreId).Select(x => x.Net).Sum() : 0)),
                                StoreName = Shared.IsEnglish ? DB.IC_Stores.FirstOrDefault(x => x.StoreId == d1.StoreId).StoreNameEn : DB.IC_Stores.FirstOrDefault(x => x.StoreId == d1.StoreId).StoreNameAr,
                                TotalSales = (double)d1.Total,
                                TotalReturns = data2.Where(x => x.StoreId == d1.StoreId).Count() > 0 ? (double)data2.Where(x => x.StoreId == d1.StoreId).Select(x => x.Total).Sum() : 0,
                                Total = (double)(d1.Total - (data2.Where(x => x.StoreId == d1.StoreId).Count() > 0 ? data2.Where(x => x.StoreId == d1.StoreId).Select(x => x.Total).Sum() : 0)),
                            }).Distinct().ToList().OrderByDescending(x => x.Net);

                grdCategory.DataSource = data;

            }
            catch (Exception ex) { MessageBox.Show(ex.Message); }
        }

        bool LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.frm_IC_StoreSales).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            return true;
        }



    }
}