﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Base;
using DAL;
using DAL.Res;

namespace Pharmacy.Forms
{
    public partial class frm_IC_EditItemQty : DevExpress.XtraEditors.XtraForm
    {
        DAL.ERPDataContext DB;
        DataTable dtItemQtyChange = new DataTable();
        DataTable dtStores = new DataTable();
        DataTable dtCompanies = new DataTable();
        List<VendorInfo> lst_Vendors = new List<VendorInfo>();
        DataTable dtUOM = new DataTable();
        List<DAL.IC_UOM> uom_list;


        public frm_IC_EditItemQty()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

        }

        private void frm_IC_EditQty_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            ErpUtils.Tab_Enter_Process(grdEditItemQty);
            BindDataSources();

            ERPDataContext DB = new ERPDataContext();

            //ErpUtils.Allow_Incremental_Search(lkpItems);
            //ErpUtils.Allow_Incremental_Search(lkpVendor);
            //ErpUtils.Allow_Incremental_Search(lkpCompany);

            colExpire.OptionsColumn.ShowInCustomizationForm = colExpire.Visible = frmMain.st_Store.ExpireDate;
            colBatch.OptionsColumn.ShowInCustomizationForm = colBatch.Visible = frmMain.st_Store.Batch;
            col_OldPiecesCount.OptionsColumn.ShowInCustomizationForm = col_OldPiecesCount.Visible = frmMain.st_Store.PiecesCount;
            col_NewPiecesCount.OptionsColumn.ShowInCustomizationForm = col_NewPiecesCount.Visible = frmMain.st_Store.PiecesCount;
            lbl_Height.Visible = txt_Height.Visible = col_Height.OptionsColumn.ShowInCustomizationForm = col_Height.Visible =
            lbl_Length.Visible = txt_Length.Visible = col_Length.OptionsColumn.ShowInCustomizationForm = col_Length.Visible =
            lbl_Width.Visible = txt_Width.Visible = col_Width.OptionsColumn.ShowInCustomizationForm = col_Width.Visible =
                (frmMain.st_Store.MultiplyDimensions != (byte)Dimensions.Multiply);

            ErpUtils.Load_Grid_Layout(grdEditItemQty, this.Name.Replace("frm_", ""));
            ErpUtils.ColumnChooser(grdEditItemQty);
        }

        private void frm_IC_EditQty_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Home && e.Modifiers == Keys.Control)
            {
                lkpItems.Focus();
            }
            if (e.KeyCode == Keys.Insert)
            {
                grdEditItemQty.Focus();
            }
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            int itemId = lkpItems.EditValue == null ? 0 : Convert.ToInt32(lkpItems.EditValue);
            int storeId = lkpStore.EditValue == null ? 0 : Convert.ToInt32(lkpStore.EditValue);
            int vendorId = lkpVendor.EditValue == null ? 0 : Convert.ToInt32(lkpVendor.EditValue);
            int companyId = lkpCompany.EditValue == null ? 0 : Convert.ToInt32(lkpCompany.EditValue);

            decimal height = Convert.ToDecimal(txt_Height.EditValue);
            decimal width = Convert.ToDecimal(txt_Width.EditValue);
            decimal length = Convert.ToDecimal(txt_Length.EditValue);

            ERPDataContext DB = new ERPDataContext();

            var itemsInStore = (from i in DB.IC_ItemStores
                                join t in DB.IC_Items
                                on i.ItemId equals t.ItemId
                                where (itemId == 0 ? true : (i.ItemId == itemId))
                                where (storeId == 0 ? true : (i.StoreId == storeId))
                                where (vendorId == 0 ? true : (i.VendorId == vendorId))
                                where (companyId == 0 ? true : (t.Company == companyId))

                                where (frmMain.st_Store.MultiplyDimensions == (byte)Dimensions.Multiply) || height <= 0 ? true : i.Height == height
                                where (frmMain.st_Store.MultiplyDimensions == (byte)Dimensions.Multiply) || width <= 0 ? true : i.Width == width
                                where (frmMain.st_Store.MultiplyDimensions == (byte)Dimensions.Multiply) || length <= 0 ? true : i.Length == length

                                group i by new
                                {
                                    i.ItemId,
                                    i.Expire,
                                    i.Batch,
                                    Height = (frmMain.st_Store.MultiplyDimensions == (byte)Dimensions.Multiply) ? 0 : i.Height,
                                    Length = (frmMain.st_Store.MultiplyDimensions == (byte)Dimensions.Multiply) ? 0 : i.Length,
                                    Width = (frmMain.st_Store.MultiplyDimensions == (byte)Dimensions.Multiply) ? 0 : i.Width,
                                } into grp

                                join t in DB.IC_Items
                                on grp.Key.ItemId equals t.ItemId
                                select new
                                {
                                    ItemId = t.ItemId,
                                    ItemCode1 = t.ItemCode1,
                                    ItemNameAr = t.ItemNameAr,
                                    Qty = grp.Select(c => c.IsInTrns ? c.Qty : c.Qty * -1).Sum(),
                                    Batch = grp.Key.Batch,
                                    Expire = grp.Key.Expire,

                                    SellPrice = Convert.ToDouble(t.SmallUOMPrice),
                                    PurchasePrice = Convert.ToDouble(t.PurchasePrice),

                                    MediumUOMFactor = t.MediumUOMFactor,
                                    LargeUOMFactor = t.LargeUOMFactor,
                                    PiecesCount = grp.Select(c => c.IsInTrns ? c.PiecesCount : c.PiecesCount * -1).Sum(),

                                    ParentItemId = t.mtrxParentItem,
                                    M1 = t.mtrxAttribute1,
                                    M2 = t.mtrxAttribute2,
                                    M3 = t.mtrxAttribute3,

                                    grp.Key.Height,
                                    grp.Key.Length,
                                    grp.Key.Width
                                }).ToList();

            dtItemQtyChange.Rows.Clear();
            foreach (var row in itemsInStore)
            {
                //get default sell uom
                var item = DB.IC_Items.Where(i => i.ItemId == row.ItemId).Single();

                DAL.MyHelper.GetUOMs(item, dtUOM, uom_list);
                object uom = "0";

                decimal uomQty = MyHelper.getCalculatedUomQty(row.Qty, Convert.ToByte(uom), MyHelper.FractionToDouble(row.MediumUOMFactor), MyHelper.FractionToDouble(row.LargeUOMFactor));

                DataRow dr = dtItemQtyChange.NewRow();
                dr["ItemId"] = row.ItemId;
                dr["ItemCode1"] = row.ItemCode1;
                dr["ItemNameAr"] = row.ItemNameAr;
                dr["StoreQty"] = decimal.ToDouble(row.Qty);
                dr["Qty"] = decimal.ToDouble(uomQty);
                dr["NewQty"] = decimal.ToDouble(uomQty);
                dr["UOM"] = uom;
                dr["Expire"] = row.Expire;
                dr["Batch"] = row.Batch;
                dr["SellPrice"] = row.SellPrice;
                dr["PurchasePrice"] = row.PurchasePrice;
                dr["InsertTime"] = "";
                dr["MediumUOMFactor"] = MyHelper.FractionToDouble(row.MediumUOMFactor);
                dr["LargeUOMFactor"] = MyHelper.FractionToDouble(row.LargeUOMFactor);
                dr["Notes"] = "";
                dr["OldPiecesCount"] = decimal.ToDouble(row.PiecesCount);
                dr["NewPiecesCount"] = decimal.ToDouble(row.PiecesCount);
                dr["ParentItemId"] = row.ParentItemId;
                dr["M1"] = row.M1;
                dr["M2"] = row.M2;
                dr["M3"] = row.M3;
                dr["Height"] = row.Height;
                dr["Width"] = row.Width;
                dr["Length"] = row.Length;
                dtItemQtyChange.Rows.Add(dr);
            }

            grdEditItemQty.DataSource = dtItemQtyChange;
            grdEditItemQty.Focus();
        }

        private void gridView1_FocusedColumnChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedColumnChangedEventArgs e)
        {
            if (e.FocusedColumn != null)
            {
                GridView view = grdEditItemQty.FocusedView as GridView;

                if (e.FocusedColumn.FieldName == "UOM")
                {
                    DB = new DAL.ERPDataContext();
                    DataRow row = view.GetFocusedDataRow();

                    DAL.IC_Item item = new DAL.IC_Item();

                    if (row != null && row["ItemId"].ToString() != string.Empty)
                    {
                        item = (from i in DB.IC_Items
                                where i.ItemId == Convert.ToInt32(row["ItemId"])
                                select i).SingleOrDefault();

                        MyHelper.GetUOMs(item, dtUOM, uom_list);


                        //auto select default purchase UOM, on first time focus only
                        if (string.IsNullOrEmpty(row["UOM"].ToString()))
                            view.SetFocusedRowCellValue("UOM", 0);
                    }
                }
            }
        }

        private void gridView1_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            GridView view = grdEditItemQty.FocusedView as GridView;
            view.FocusedColumn = view.Columns["NewQty"];
        }

        private void gridView1_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            GridView view = grdEditItemQty.FocusedView as GridView;
            DataRow row = view.GetFocusedDataRow();

            #region GetUomPrice
            if (e.Column.FieldName == "UOM")
            {
                ERPDataContext DB = new ERPDataContext();
                var item = (from i in DB.IC_Items
                            where i.ItemId == Convert.ToInt32(row["ItemId"])
                            select i).SingleOrDefault();

                decimal qty = Convert.ToDecimal(row["StoreQty"]);
                byte uom = Convert.ToByte(row["UOM"]);

                //decimal Purchase = Convert.ToDecimal(row["DefaultBuy"]);               

                decimal LargeUOMFactor = 1;
                if (row["LargeUOMFactor"].ToString() != string.Empty)
                {
                    LargeUOMFactor = MyHelper.FractionToDouble(row["LargeUOMFactor"].ToString());
                }
                decimal MediumUOMFactor = 1;
                if (row["MediumUOMFactor"].ToString() != string.Empty)
                {
                    MediumUOMFactor = MyHelper.FractionToDouble(row["MediumUOMFactor"].ToString());
                }

                //Set UOM Prices
                if (uom == 0)//small
                {
                    view.SetFocusedRowCellValue("SellPrice", item.SmallUOMPrice);
                    view.SetFocusedRowCellValue("PurchasePrice", (item.PurchasePrice));
                }
                if (uom == 1)//medium
                {
                    view.SetFocusedRowCellValue("PurchasePrice", (item.PurchasePrice * MyHelper.FractionToDouble(item.MediumUOMFactor)));
                    view.SetFocusedRowCellValue("SellPrice", item.MediumUOMPrice);
                }
                if (uom == 2)//large
                {
                    view.SetFocusedRowCellValue("PurchasePrice", (item.PurchasePrice * MyHelper.FractionToDouble(item.LargeUOMFactor)));
                    view.SetFocusedRowCellValue("SellPrice", item.LargeUOMPrice);
                }

                decimal uomQty = MyHelper.getCalculatedUomQty(qty, uom, MediumUOMFactor, LargeUOMFactor);
                view.SetFocusedRowCellValue("Qty", uomQty);
            }
            #endregion
        }

        private void gridView1_ValidateRow(object sender, DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs e)
        {
            ColumnView view = sender as ColumnView;
            object newQty = view.GetFocusedRowCellValue("NewQty");
            if ((newQty != null && !string.IsNullOrEmpty(newQty.ToString()) && Convert.ToDecimal(newQty) < 0))
            {
                e.Valid = false;
                view.SetColumnError(view.Columns["NewQty"],
                    Shared.IsEnglish == true ? ResICEn.txtEditItemQty : ResICAr.txtEditItemQty);//"الكمية الجديدة يجب أن تكون أكبر من أو تساوي 0"
            }
            if ((view.GetRowCellValue(e.RowHandle, view.Columns["NewPiecesCount"])).ToString() == string.Empty
                    || Convert.ToDecimal(view.GetRowCellValue(e.RowHandle, view.Columns["NewPiecesCount"])) < 0)
            {
                e.Valid = false;
                view.SetColumnError(view.Columns["NewPiecesCount"], Shared.IsEnglish ? ResICEn.txtValidateQty : ResICAr.txtValidateQty);//"الكمية يجب ان تكون اكبر من الصفر");
            }
        }

        private void gridView1_InvalidRowException(object sender, InvalidRowExceptionEventArgs e)
        {
            e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.NoAction;
        }

        private void rep_ExpireDate_CustomDisplayText(object sender, DevExpress.XtraEditors.Controls.CustomDisplayTextEventArgs e)
        {
            #region Expire
            if (e.Value == null || e.Value == DBNull.Value)
                return;
            try
            {
                DateTime date = Convert.ToDateTime(e.Value);
                e.DisplayText = date.Month + "-" + date.Year;
            }
            catch
            { }
            #endregion
        }

        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Save_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            GridView view = grdEditItemQty.FocusedView as GridView;
            view.FocusedRowHandle = view.FocusedRowHandle + 1;
            view.RefreshEditor(false);

            grdEditItemQty.RefreshDataSource();

            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            DataRow[] changedItems = dtItemQtyChange.Select("NewQty <> Qty OR NewPiecesCount <> OldPiecesCount");

            if (changedItems.Count() > 0)
            {
                DateTime insertTime = MyHelper.Get_Server_DateTime();
                byte store_costmethod = DB.IC_Stores.Where(x => x.StoreId == Convert.ToInt32(lkpStore.EditValue)).Select(x => x.CostMethod).FirstOrDefault();

                foreach (DataRow row in changedItems)
                {
                    decimal LargeUOMFactor = 1, MediumUOMFactor = 1;
                    DateTime? expire = null;
                    string batch = null;

                    decimal height = Convert.ToDecimal(row["Height"]);
                    decimal width = Convert.ToDecimal(row["Width"]);
                    decimal length = Convert.ToDecimal(row["Length"]);

                    if (!string.IsNullOrEmpty(row["LargeUOMFactor"].ToString()))
                        LargeUOMFactor = MyHelper.FractionToDouble(row["LargeUOMFactor"].ToString());
                    if (!string.IsNullOrEmpty(row["MediumUOMFactor"].ToString()))
                        MediumUOMFactor = MyHelper.FractionToDouble(row["MediumUOMFactor"].ToString());

                    if (!string.IsNullOrEmpty(row["Expire"].ToString()))
                        expire = Convert.ToDateTime(row["Expire"]);
                    if (!string.IsNullOrEmpty(row["batch"].ToString()))
                        batch = Convert.ToString(row["batch"]);

                    int ItmID = Convert.ToInt32(row["ItemId"]);
                    decimal PurchasePrice = Convert.ToDecimal(row["PurchasePrice"]);
                    decimal currentQty = Convert.ToDecimal(row["Qty"]);

                    #region Matrix_Data
                    int? ParentItemId = null, M1 = null, M2 = null, M3 = null;
                    if (!string.IsNullOrEmpty(row["ParentItemId"].ToString()) && Convert.ToInt32(row["ParentItemId"]) != 0)
                        ParentItemId = Convert.ToInt32(row["ParentItemId"]);
                    if (!string.IsNullOrEmpty(row["M1"].ToString()) && Convert.ToInt32(row["M1"]) != 0)
                        M1 = Convert.ToInt32(row["M1"]);
                    if (!string.IsNullOrEmpty(row["M2"].ToString()) && Convert.ToInt32(row["M2"]) != 0)
                        M2 = Convert.ToInt32(row["M2"]);
                    if (!string.IsNullOrEmpty(row["M3"].ToString()) && Convert.ToInt32(row["M3"]) != 0)
                        M3 = Convert.ToInt32(row["M3"]);
                    #endregion

                    var Newquantity = Convert.ToDecimal(row["NewQty"]);
                    decimal newPiecescount = Convert.ToDecimal(row["NewPiecesCount"]) - Convert.ToDecimal(row["OldPiecesCount"]);

                    decimal qt = 0;
                    if (currentQty < Newquantity)//InTrns
                    {
                        qt = Newquantity - currentQty;
                        if (frmMain.st_Store.MultiplyDimensions == (byte)Dimensions.Multiply)
                            MyHelper.AddItemToStore(ItmID, Convert.ToInt32(lkpStore.EditValue),
                                qt, Convert.ToByte(row["UOM"]), MediumUOMFactor, LargeUOMFactor, null, 0, (int)Process.AdujstIn,
                                true, PurchasePrice * qt, insertTime, expire, batch, 0, 0, 0, newPiecescount, ParentItemId, M1, M2, M3, 0, null, string.Empty);
                        else
                            MyHelper.AddItemToStore(ItmID, Convert.ToInt32(lkpStore.EditValue),
                                qt, Convert.ToByte(row["UOM"]), MediumUOMFactor, LargeUOMFactor, null, 0, (int)Process.AdujstIn,
                                true, PurchasePrice * qt, insertTime, expire, batch, length, width, height, newPiecescount,
                                ParentItemId, M1, M2, M3, 0, null, string.Empty);

                    }
                    else if (currentQty > Newquantity)//OutTrns
                    {
                        qt = currentQty - Newquantity;
                        if (frmMain.st_Store.MultiplyDimensions == (byte)Dimensions.Multiply)
                            MyHelper.AddItemToStore(ItmID, Convert.ToInt32(lkpStore.EditValue),
                                qt, Convert.ToByte(row["UOM"]), MediumUOMFactor, LargeUOMFactor, null, 0, (int)Process.AdujstOut,
                                false, PurchasePrice * qt, insertTime, expire, batch, 0, 0, 0, newPiecescount * -1,
                                ParentItemId, M1, M2, M3, 0, null, string.Empty);
                        else
                            MyHelper.AddItemToStore(ItmID, Convert.ToInt32(lkpStore.EditValue),
                                qt, Convert.ToByte(row["UOM"]), MediumUOMFactor, LargeUOMFactor, null, 0, (int)Process.AdujstOut,
                                false, PurchasePrice * qt, insertTime, expire, batch, length, width, height,
                                newPiecescount * -1, ParentItemId, M1, M2, M3, 0, null, string.Empty);

                    }
                    else if (currentQty == Newquantity)
                    {
                        if (newPiecescount < 0)
                        {
                            if (frmMain.st_Store.MultiplyDimensions == (byte)Dimensions.Multiply)
                                MyHelper.AddItemToStore(ItmID, Convert.ToInt32(lkpStore.EditValue),
                                    0, Convert.ToByte(row["UOM"]), MediumUOMFactor, LargeUOMFactor, null, 0, (int)Process.AdujstOut,
                                    false, 0, insertTime, expire, batch, 0, 0, 0, newPiecescount * -1, ParentItemId, M1, M2, M3, 0, null, string.Empty);
                            else
                                MyHelper.AddItemToStore(ItmID, Convert.ToInt32(lkpStore.EditValue),
                                    0, Convert.ToByte(row["UOM"]), MediumUOMFactor, LargeUOMFactor, null, 0, (int)Process.AdujstOut,
                                    false, 0, insertTime, expire, batch, length, width, height, newPiecescount * -1,
                                    ParentItemId, M1, M2, M3, 0, null, string.Empty);
                        }
                        else if (newPiecescount > 0)
                        {
                            if (frmMain.st_Store.MultiplyDimensions == (byte)Dimensions.Multiply)
                                MyHelper.AddItemToStore(ItmID, Convert.ToInt32(lkpStore.EditValue),
                                    0, Convert.ToByte(row["UOM"]), MediumUOMFactor, LargeUOMFactor, null, 0, (int)Process.AdujstIn,
                                    true, 0, insertTime, expire, batch, 0, 0, 0, newPiecescount, ParentItemId, M1, M2, M3, 0, null, string.Empty);
                            else
                                MyHelper.AddItemToStore(ItmID, Convert.ToInt32(lkpStore.EditValue),
                                    0, Convert.ToByte(row["UOM"]), MediumUOMFactor, LargeUOMFactor, null, 0, (int)Process.AdujstIn,
                                    true, 0, insertTime, expire, batch, length, width, height, newPiecescount,
                                    ParentItemId, M1, M2, M3, 0, null, string.Empty);
                        }
                    }

                    IC_ItemQtyChange iqc = new IC_ItemQtyChange();
                    iqc.ChangeDate = MyHelper.Get_Server_DateTime().Date;
                    iqc.ItemId = ItmID;
                    iqc.NewQty = Newquantity;
                    iqc.Expire = expire;
                    iqc.Batch = batch;
                    iqc.Notes = row["Notes"].ToString();
                    iqc.OldQty = Convert.ToDecimal(row["Qty"]);
                    iqc.StoreId = Convert.ToInt32(lkpStore.EditValue);
                    iqc.UOM = Convert.ToByte(row["UOM"]);
                    iqc.UserId = Shared.UserId;
                    iqc.OldPiecesCount = Convert.ToDecimal(row["OldPiecesCount"]);
                    iqc.NewPiecesCount = Convert.ToDecimal(row["NewPiecesCount"]);
                    DB.IC_ItemQtyChanges.InsertOnSubmit(iqc);
                }
                //}
                DB.SubmitChanges();
                btnSearch.PerformClick();
                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgSave : ResICAr.MsgSave//تم الحفظ بنجاح
                    , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.txtEditItemQty2 : ResICAr.txtEditItemQty2//"يجب تسجيل الكمية الجديدة لصنف واحد علي الأقل"
                    , "", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
        }

        private void BindDataSources()
        {
            ERPDataContext DB = new ERPDataContext();

            #region Get Items
            lkpItems.Properties.DataSource = (from i in DB.IC_Items
                                              where i.IsDeleted != true
                                              && i.ItemType != (int)ItemType.MatrixParent
                                              && i.ItemType != (int)ItemType.Service
                                              && i.ItemType != (int)ItemType.Subtotal
                                              select new
                                                  {
                                                      ItemCode1 = i.ItemCode1,
                                                      ItemCode2 = i.ItemCode2,
                                                      ItemId = i.ItemId,
                                                      ItemNameAr = i.ItemNameAr,
                                                      ItemNameEn = i.ItemNameEn,
                                                      i.MaxQty,
                                                      i.MinQty,
                                                      i.ReorderLevel,
                                                      i.IsExpire,
                                                      i.PicPath,
                                                      i.MediumUOM,
                                                      i.LargeUOM,
                                                  }).ToList();

            #endregion

            #region ItemQtyChange
            dtItemQtyChange.Columns.Add("ItemId");
            dtItemQtyChange.Columns.Add("ItemCode1");
            dtItemQtyChange.Columns.Add("ItemNameAr");
            dtItemQtyChange.Columns.Add("StoreQty"); //never change
            dtItemQtyChange.Columns.Add("Qty");//recalculated by changing uom            
            dtItemQtyChange.Columns.Add("NewQty");
            dtItemQtyChange.Columns.Add("UOM");
            dtItemQtyChange.Columns.Add("Expire");
            dtItemQtyChange.Columns.Add("Batch");
            dtItemQtyChange.Columns.Add("SellPrice");
            dtItemQtyChange.Columns.Add("PurchasePrice");
            dtItemQtyChange.Columns.Add("InsertTime");
            dtItemQtyChange.Columns.Add("MediumUOMFactor");
            dtItemQtyChange.Columns.Add("LargeUOMFactor");
            dtItemQtyChange.Columns.Add("Notes");
            dtItemQtyChange.Columns.Add("OldPiecesCount");
            dtItemQtyChange.Columns.Add("NewPiecesCount");
            dtItemQtyChange.Columns.Add("ParentItemId");
            dtItemQtyChange.Columns.Add("M1");
            dtItemQtyChange.Columns.Add("M2");
            dtItemQtyChange.Columns.Add("M3");
            dtItemQtyChange.Columns.Add("Height");
            dtItemQtyChange.Columns.Add("Length");
            dtItemQtyChange.Columns.Add("Width");

            grdEditItemQty.DataSource = dtItemQtyChange;
            #endregion

            #region Get Stores
            //int defaultStoreId = MyHelper.GetStores(dtStores,true);

            //lkpStore.Properties.DisplayMember = "StoreNameAr";
            //lkpStore.Properties.ValueMember = "StoreId";

            //lkpStore.EditValue = defaultStoreId;
            //lkpStore.Properties.DataSource = dtStores;
            #endregion

            #region Get Stores
            int defaultStoreId = 0;
            var stores_table = MyHelper.Get_Stores(true, out defaultStoreId, Shared.user.UserChangeStore, Shared.user.DefaultStore,
                Shared.UserId);
            lkpStore.Properties.DataSource = stores_table;

            lkpStore.Properties.DisplayMember = "StoreNameAr";
            lkpStore.Properties.ValueMember = "StoreId";
            lkpStore.EditValue = defaultStoreId;
            #endregion

            #region Get Companies
            MyHelper.GetCompanies(dtCompanies);

            lkpCompany.Properties.DisplayMember = "CompanyNameAr";
            lkpCompany.Properties.ValueMember = "CompanyId";
            lkpCompany.Properties.DataSource = dtCompanies;
            #endregion

            #region Get Vendors
            int lastVenId = 0;
            MyHelper.GetVendors(out lst_Vendors, out lastVenId);

            lkpVendor.Properties.DisplayMember = "VenNameAr";
            lkpVendor.Properties.ValueMember = "VendorId";
            lkpVendor.Properties.DataSource = lst_Vendors;
            #endregion

            #region UOM
            uom_list = DB.IC_UOMs.ToList();
            repUOM.DisplayMember = "Uom";
            repUOM.ValueMember = "Index";
            repUOM.DataSource = MyHelper.GetUomDataTable(dtUOM);
            #endregion
        }

        private void barBtn_Help_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "تسوية كميات أصناف");
        }

        private void frm_IC_EditItemQty_FormClosing(object sender, FormClosingEventArgs e)
        {
            ErpUtils.save_Grid_Layout(grdEditItemQty, this.Name.Replace("frm_", ""), true);
        }
    }
}