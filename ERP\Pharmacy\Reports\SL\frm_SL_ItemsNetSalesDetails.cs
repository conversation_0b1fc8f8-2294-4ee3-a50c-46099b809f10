﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;

using DevExpress.XtraReports.UI;

namespace Reports
{
    public partial class frm_SL_ItemsNetSalesDetails : DevExpress.XtraEditors.XtraForm
    {
        public bool UserCanOpen;
        string reportName, dateFilter, otherFilters;

        int  storeId1, storeId2;
        byte  fltrTyp_Date, FltrTyp_Customer, FltrTyp_InvBook, FltrTyp_Store;
        DateTime date1, date2;

        int customerId1, customerId2, custGroupId, salesEmpId, EmpGroupId;
        string custGroupAccNumber;


        bool UsingTaxRatio = false;

        List<int> lstStores = new List<int>();
        List<int> lst_invBooksId = new List<int>();

        DataTable dt = new DataTable();

        public frm_SL_ItemsNetSalesDetails(
            string reportName, string dateFilter, string otherFilters,
            byte fltrTyp_Date, DateTime date1, DateTime date2,
            byte FltrTyp_Customer, int customerId1, int customerId2,
            int custGroupId, string custGroupAccNumber,
             int salesEmpId,
            byte FltrTyp_InvBook, string InvBooks, int EmpGroupId, bool UsingTaxRatio,
            byte _fltrTyp_Store, int _storeId1, int _storeId2)
        {
            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            ReportsRTL.RTL_BarManager(this.barManager1);

            this.reportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;

            this.fltrTyp_Date = fltrTyp_Date;
            this.FltrTyp_Customer = FltrTyp_Customer;
            this.FltrTyp_Store = _fltrTyp_Store;

            this.date1 = date1;
            this.date2 = date2;
            this.customerId1 = customerId1;
            this.customerId2 = customerId2;
            this.custGroupId = custGroupId;
            this.custGroupAccNumber = custGroupAccNumber;

            this.salesEmpId = salesEmpId;
            this.EmpGroupId = EmpGroupId;

            this.UsingTaxRatio = UsingTaxRatio;

            this.FltrTyp_InvBook = FltrTyp_InvBook;
            Utilities.Get_ChkLst_Items(InvBooks, lst_invBooksId);

            this.storeId1 = _storeId1;
            this.storeId2 = _storeId2;

            #region Init_DataTable
            dt.Columns.Add("customer");
            dt.Columns.Add("SoldTotalPrice", typeof(decimal));
            dt.Columns.Add("ReturnTotalPrice", typeof(decimal));
            dt.Columns.Add("SoldNetPrice", typeof(decimal));
            dt.Columns.Add("ReturnNetPrice", typeof(decimal));

            #endregion

            getReportHeader();
            LoadData();
            ReportsUtils.ColumnChooser(grdCategory);
        }


        private void frm_SL_InvoiceList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                ReportsRTL.LTRLayout(this);

            ReportsUtils.Load_Grid_Layout(grdCategory, this.Name.Replace("frm_", ""));
        }

        private void frm_Rep_FormClosing(object sender, FormClosingEventArgs e)
        {
            ReportsUtils.save_Grid_Layout(grdCategory, this.Name.Replace("frm_", ""), true);
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Preview_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCategory, true).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        private void barBtnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadData();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grdCategory.MinimumSize = grdCategory.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grdCategory, true, true).ShowPreview();
                grdCategory.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                //lblCompName.Text = comp.CmpNameAr;
                lblReportName.Text = this.Text = reportName;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        void LoadData()
        {
            dt.Rows.Clear();

            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;

            ERPDataContext DB = new ERPDataContext();

            var stores = DB.IC_Stores.ToList();
            foreach (var store in stores)
            {
                if (FltrTyp_Store == 2)
                {
                    if (store.StoreId <= storeId2 && store.StoreId >= storeId1)
                    {
                        lstStores.Add(store.StoreId);
                    }
                }
                else if (FltrTyp_Store == 0)
                {
                    lstStores.Add(store.StoreId);
                }
                else if (storeId1 > 0 && (store.StoreId == storeId1 || store.ParentId == storeId1))
                    lstStores.Add(store.StoreId);
                //else if (store_id2 > 0 && (store.StoreId == store_id2 || store.ParentId == store_id2))
                //    lstStores.Add(store.StoreId);
            }

            var sales_data = (
                from c in DB.SL_Customers
                join a in DB.ACC_Accounts
                        on c.AccountId equals a.AccountId
                where custGroupId == 0 ? true : a.AcNumber.StartsWith(custGroupAccNumber)

                where FltrTyp_Customer == 1 ? c.CustomerId == customerId1 : true
                where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 != 0) ?
                c.CustomerId >= customerId1 && c.CustomerId <= customerId2 : true
                where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 == 0) ?
                c.CustomerId >= customerId1 : true
                where (FltrTyp_Customer == 2 && customerId1 == 0 && customerId2 != 0) ?
                c.CustomerId <= customerId2 : true

                join i in DB.SL_Invoices on c.CustomerId equals i.CustomerId
                where lstStores.Count > 0 ? lstStores.Contains(i.StoreId) : true
                where salesEmpId == 0 ? true : i.SalesEmpId == salesEmpId

                where FltrTyp_InvBook == 0 ? true : (i.InvoiceBookId.HasValue && lst_invBooksId.Contains(i.InvoiceBookId.Value))

                where fltrTyp_Date == 1 ? i.InvoiceDate.Date == date1 : true
                where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                i.InvoiceDate >= date1 && i.InvoiceDate <= date2 : true
                where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                i.InvoiceDate >= date1 : true
                where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                i.InvoiceDate <= date2 : true

                join hh in DB.HR_Employees on i.SalesEmpId equals hh.EmpId into hq
                from xx in hq.DefaultIfEmpty().Where(x => EmpGroupId == 0 ? true : x.GroupId == EmpGroupId)


                //join s in DB.SL_InvoiceDetails on i.SL_InvoiceId equals s.SL_InvoiceId

               
                let TotalInvoice = i.Net + i.DiscountValue + i.CustomTaxValue + i.AddTaxValue + i.DeductTaxValue + i.RetentionValue + i.AdvancePaymentValue + i.Expenses - i.HandingValue.GetValueOrDefault(0) - i.TransportationValue.GetValueOrDefault(0) - i.ShiftAdd.GetValueOrDefault(0)



                //join t in DB.IC_Items on s.ItemId equals t.ItemId


                select new
                {
                    c.CustomerId,
                    customer = !Shared.IsEnglish ? c.CusNameAr : c.CusNameEn,
                    Net = i.Net * i.CrncRate,
                    TotalInvoice = TotalInvoice * i.CrncRate
                }).ToList();

            var slReturn_data = (
                                from c in DB.SL_Customers
                                join a in DB.ACC_Accounts
                        on c.AccountId equals a.AccountId
                                where custGroupId == 0 ? true : a.AcNumber.StartsWith(custGroupAccNumber)

                                where FltrTyp_Customer == 1 ? c.CustomerId == customerId1 : true
                                where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 != 0) ?
                                c.CustomerId >= customerId1 && c.CustomerId <= customerId2 : true
                                where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 == 0) ?
                                c.CustomerId >= customerId1 : true
                                where (FltrTyp_Customer == 2 && customerId1 == 0 && customerId2 != 0) ?
                                c.CustomerId <= customerId2 : true

                                join i in DB.SL_Returns on c.CustomerId equals i.CustomerId
                                where lstStores.Count > 0 ? lstStores.Contains(i.StoreId) : true
                                where salesEmpId == 0 ? true : i.SalesEmpId == salesEmpId

                                where FltrTyp_InvBook == 0 ? true : (i.InvoiceBookId.HasValue && lst_invBooksId.Contains(i.InvoiceBookId.Value))

                                where fltrTyp_Date == 1 ? i.ReturnDate.Date == date1 : true
                                where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                                i.ReturnDate >= date1 && i.ReturnDate <= date2 : true
                                where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                                i.ReturnDate >= date1 : true
                                where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                                i.ReturnDate <= date2 : true

                                join hh in DB.HR_Employees on i.SalesEmpId equals hh.EmpId into hq
                                from xx in hq.DefaultIfEmpty().Where(x => EmpGroupId == 0 ? true : x.GroupId == EmpGroupId)


                                //join s in DB.SL_ReturnDetails on i.SL_ReturnId equals s.SL_ReturnId

                               

                                let TotalInvoice = i.Net + i.TaxValue + i.DiscountValue + i.Expenses
                                           + i.DeductTaxValue + i.AddTaxValue

                                //join t in DB.IC_Items on s.ItemId equals t.ItemId
                                
                                select new
                                {
                                    c.CustomerId,
                                    customer = !Shared.IsEnglish ? c.CusNameAr : c.CusNameEn,
                                    Net = i.Net * i.CrncRate,
                                    TotalInvoice = TotalInvoice * i.CrncRate
                                }).ToList();

            rep_Currency.DataSource = Shared.lstCurrency;
            rep_Currency.ValueMember = "CrncId";
            rep_Currency.DisplayMember = "crncName";

            var lst = sales_data.Union(slReturn_data).Select(x=>x.CustomerId).Distinct().ToList();

            foreach (var t in lst.Distinct())
            {

                DataRow dr = dt.NewRow();

                dr["customer"] = sales_data.Where(x => x.CustomerId == t).Select(x => x.customer).FirstOrDefault();
                dr["SoldTotalPrice"] = (double)Math.Round(sales_data.Where(x => x.CustomerId == t).Select(x => x.TotalInvoice).DefaultIfEmpty(0).Sum(), 2, MidpointRounding.AwayFromZero);

                dr["ReturnTotalPrice"] = (double)Math.Round(slReturn_data.Where(x => x.CustomerId == t).Select(x => x.TotalInvoice).DefaultIfEmpty(0).Sum(), 2, MidpointRounding.AwayFromZero);
                
                dr["SoldNetPrice"] = (double)Math.Round(sales_data.Where(x => x.CustomerId == t).Select(x => x.Net).DefaultIfEmpty(0).Sum(), 2, MidpointRounding.AwayFromZero);

                dr["ReturnNetPrice"] = (double)Math.Round(slReturn_data.Where(x => x.CustomerId == t).Select(x => x.Net).DefaultIfEmpty(0).Sum(), 2, MidpointRounding.AwayFromZero);

                //dr["NetTotalPrice_Local"] = (double)Math.Round((Convert.ToDecimal(dr["SoldTotalPrice"])) - (Convert.ToDecimal(dr["ReturnTotalPrice"])), 2, MidpointRounding.AwayFromZero);
                if (Convert.ToDouble(dr["SoldNetPrice"]) == 0 && Convert.ToDouble(dr["ReturnNetPrice"]) == 0) continue;
                dt.Rows.Add(dr);
            }

            grdCategory.DataSource = dt;
        }

        private void gridView1_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            if (e.ListSourceRowIndex < 0)
                return;

            if (e.Column.FieldName == "colIndex")
                e.Value = e.RowHandle() + 1;
        }

        public bool LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.frm_SL_ItemsNetSalesDetails).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            return true;
        }
    }
}