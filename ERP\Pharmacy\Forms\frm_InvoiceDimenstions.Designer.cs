﻿namespace Pharmacy.Forms
{
    partial class frm_InvoiceDimenstions
    {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_InvoiceDimenstions));
            this.textEdit7 = new DevExpress.XtraEditors.TextEdit();
            this.textEdit1 = new DevExpress.XtraEditors.TextEdit();
            this.textEdit2 = new DevExpress.XtraEditors.TextEdit();
            this.txt_Height = new DevExpress.XtraEditors.TextEdit();
            this.txt_Width = new DevExpress.XtraEditors.TextEdit();
            this.txt_Length = new DevExpress.XtraEditors.TextEdit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit7.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Height.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Width.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Length.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // textEdit7
            // 
            resources.ApplyResources(this.textEdit7, "textEdit7");
            this.textEdit7.EnterMoveNextControl = true;
            this.textEdit7.Name = "textEdit7";
            this.textEdit7.Properties.AccessibleDescription = resources.GetString("textEdit7.Properties.AccessibleDescription");
            this.textEdit7.Properties.AccessibleName = resources.GetString("textEdit7.Properties.AccessibleName");
            this.textEdit7.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("textEdit7.Properties.Appearance.BackColor")));
            this.textEdit7.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("textEdit7.Properties.Appearance.FontSizeDelta")));
            this.textEdit7.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("textEdit7.Properties.Appearance.FontStyleDelta")));
            this.textEdit7.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("textEdit7.Properties.Appearance.ForeColor")));
            this.textEdit7.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("textEdit7.Properties.Appearance.GradientMode")));
            this.textEdit7.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("textEdit7.Properties.Appearance.Image")));
            this.textEdit7.Properties.Appearance.Options.UseBackColor = true;
            this.textEdit7.Properties.Appearance.Options.UseForeColor = true;
            this.textEdit7.Properties.Appearance.Options.UseTextOptions = true;
            this.textEdit7.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.textEdit7.Properties.AutoHeight = ((bool)(resources.GetObject("textEdit7.Properties.AutoHeight")));
            this.textEdit7.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.textEdit7.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("textEdit7.Properties.Mask.AutoComplete")));
            this.textEdit7.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("textEdit7.Properties.Mask.BeepOnError")));
            this.textEdit7.Properties.Mask.EditMask = resources.GetString("textEdit7.Properties.Mask.EditMask");
            this.textEdit7.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("textEdit7.Properties.Mask.IgnoreMaskBlank")));
            this.textEdit7.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("textEdit7.Properties.Mask.MaskType")));
            this.textEdit7.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("textEdit7.Properties.Mask.PlaceHolder")));
            this.textEdit7.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("textEdit7.Properties.Mask.SaveLiteral")));
            this.textEdit7.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("textEdit7.Properties.Mask.ShowPlaceHolders")));
            this.textEdit7.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("textEdit7.Properties.Mask.UseMaskAsDisplayFormat")));
            this.textEdit7.Properties.MaxLength = 190;
            this.textEdit7.Properties.NullValuePrompt = resources.GetString("textEdit7.Properties.NullValuePrompt");
            this.textEdit7.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("textEdit7.Properties.NullValuePromptShowForEmptyValue")));
            this.textEdit7.TabStop = false;
            // 
            // textEdit1
            // 
            resources.ApplyResources(this.textEdit1, "textEdit1");
            this.textEdit1.EnterMoveNextControl = true;
            this.textEdit1.Name = "textEdit1";
            this.textEdit1.Properties.AccessibleDescription = resources.GetString("textEdit1.Properties.AccessibleDescription");
            this.textEdit1.Properties.AccessibleName = resources.GetString("textEdit1.Properties.AccessibleName");
            this.textEdit1.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("textEdit1.Properties.Appearance.BackColor")));
            this.textEdit1.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("textEdit1.Properties.Appearance.FontSizeDelta")));
            this.textEdit1.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("textEdit1.Properties.Appearance.FontStyleDelta")));
            this.textEdit1.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("textEdit1.Properties.Appearance.ForeColor")));
            this.textEdit1.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("textEdit1.Properties.Appearance.GradientMode")));
            this.textEdit1.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("textEdit1.Properties.Appearance.Image")));
            this.textEdit1.Properties.Appearance.Options.UseBackColor = true;
            this.textEdit1.Properties.Appearance.Options.UseForeColor = true;
            this.textEdit1.Properties.Appearance.Options.UseTextOptions = true;
            this.textEdit1.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.textEdit1.Properties.AutoHeight = ((bool)(resources.GetObject("textEdit1.Properties.AutoHeight")));
            this.textEdit1.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.textEdit1.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("textEdit1.Properties.Mask.AutoComplete")));
            this.textEdit1.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("textEdit1.Properties.Mask.BeepOnError")));
            this.textEdit1.Properties.Mask.EditMask = resources.GetString("textEdit1.Properties.Mask.EditMask");
            this.textEdit1.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("textEdit1.Properties.Mask.IgnoreMaskBlank")));
            this.textEdit1.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("textEdit1.Properties.Mask.MaskType")));
            this.textEdit1.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("textEdit1.Properties.Mask.PlaceHolder")));
            this.textEdit1.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("textEdit1.Properties.Mask.SaveLiteral")));
            this.textEdit1.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("textEdit1.Properties.Mask.ShowPlaceHolders")));
            this.textEdit1.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("textEdit1.Properties.Mask.UseMaskAsDisplayFormat")));
            this.textEdit1.Properties.MaxLength = 190;
            this.textEdit1.Properties.NullValuePrompt = resources.GetString("textEdit1.Properties.NullValuePrompt");
            this.textEdit1.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("textEdit1.Properties.NullValuePromptShowForEmptyValue")));
            this.textEdit1.TabStop = false;
            // 
            // textEdit2
            // 
            resources.ApplyResources(this.textEdit2, "textEdit2");
            this.textEdit2.EnterMoveNextControl = true;
            this.textEdit2.Name = "textEdit2";
            this.textEdit2.Properties.AccessibleDescription = resources.GetString("textEdit2.Properties.AccessibleDescription");
            this.textEdit2.Properties.AccessibleName = resources.GetString("textEdit2.Properties.AccessibleName");
            this.textEdit2.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("textEdit2.Properties.Appearance.BackColor")));
            this.textEdit2.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("textEdit2.Properties.Appearance.FontSizeDelta")));
            this.textEdit2.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("textEdit2.Properties.Appearance.FontStyleDelta")));
            this.textEdit2.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("textEdit2.Properties.Appearance.ForeColor")));
            this.textEdit2.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("textEdit2.Properties.Appearance.GradientMode")));
            this.textEdit2.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("textEdit2.Properties.Appearance.Image")));
            this.textEdit2.Properties.Appearance.Options.UseBackColor = true;
            this.textEdit2.Properties.Appearance.Options.UseForeColor = true;
            this.textEdit2.Properties.Appearance.Options.UseTextOptions = true;
            this.textEdit2.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.textEdit2.Properties.AutoHeight = ((bool)(resources.GetObject("textEdit2.Properties.AutoHeight")));
            this.textEdit2.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.textEdit2.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("textEdit2.Properties.Mask.AutoComplete")));
            this.textEdit2.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("textEdit2.Properties.Mask.BeepOnError")));
            this.textEdit2.Properties.Mask.EditMask = resources.GetString("textEdit2.Properties.Mask.EditMask");
            this.textEdit2.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("textEdit2.Properties.Mask.IgnoreMaskBlank")));
            this.textEdit2.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("textEdit2.Properties.Mask.MaskType")));
            this.textEdit2.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("textEdit2.Properties.Mask.PlaceHolder")));
            this.textEdit2.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("textEdit2.Properties.Mask.SaveLiteral")));
            this.textEdit2.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("textEdit2.Properties.Mask.ShowPlaceHolders")));
            this.textEdit2.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("textEdit2.Properties.Mask.UseMaskAsDisplayFormat")));
            this.textEdit2.Properties.MaxLength = 190;
            this.textEdit2.Properties.NullValuePrompt = resources.GetString("textEdit2.Properties.NullValuePrompt");
            this.textEdit2.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("textEdit2.Properties.NullValuePromptShowForEmptyValue")));
            this.textEdit2.TabStop = false;
            // 
            // txt_Height
            // 
            resources.ApplyResources(this.txt_Height, "txt_Height");
            this.txt_Height.EnterMoveNextControl = true;
            this.txt_Height.Name = "txt_Height";
            this.txt_Height.Properties.AccessibleDescription = resources.GetString("txt_Height.Properties.AccessibleDescription");
            this.txt_Height.Properties.AccessibleName = resources.GetString("txt_Height.Properties.AccessibleName");
            this.txt_Height.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Height.Properties.AutoHeight")));
            this.txt_Height.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_Height.Properties.Mask.AutoComplete")));
            this.txt_Height.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_Height.Properties.Mask.BeepOnError")));
            this.txt_Height.Properties.Mask.EditMask = resources.GetString("txt_Height.Properties.Mask.EditMask");
            this.txt_Height.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Height.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Height.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Height.Properties.Mask.MaskType")));
            this.txt_Height.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_Height.Properties.Mask.PlaceHolder")));
            this.txt_Height.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Height.Properties.Mask.SaveLiteral")));
            this.txt_Height.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Height.Properties.Mask.ShowPlaceHolders")));
            this.txt_Height.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_Height.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_Height.Properties.NullValuePrompt = resources.GetString("txt_Height.Properties.NullValuePrompt");
            this.txt_Height.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_Height.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_Height.EditValueChanged += new System.EventHandler(this.txt_DiscR1_EditValueChanged);
            // 
            // txt_Width
            // 
            resources.ApplyResources(this.txt_Width, "txt_Width");
            this.txt_Width.EnterMoveNextControl = true;
            this.txt_Width.Name = "txt_Width";
            this.txt_Width.Properties.AccessibleDescription = resources.GetString("txt_Width.Properties.AccessibleDescription");
            this.txt_Width.Properties.AccessibleName = resources.GetString("txt_Width.Properties.AccessibleName");
            this.txt_Width.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Width.Properties.AutoHeight")));
            this.txt_Width.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_Width.Properties.Mask.AutoComplete")));
            this.txt_Width.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_Width.Properties.Mask.BeepOnError")));
            this.txt_Width.Properties.Mask.EditMask = resources.GetString("txt_Width.Properties.Mask.EditMask");
            this.txt_Width.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Width.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Width.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Width.Properties.Mask.MaskType")));
            this.txt_Width.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_Width.Properties.Mask.PlaceHolder")));
            this.txt_Width.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Width.Properties.Mask.SaveLiteral")));
            this.txt_Width.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Width.Properties.Mask.ShowPlaceHolders")));
            this.txt_Width.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_Width.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_Width.Properties.NullValuePrompt = resources.GetString("txt_Width.Properties.NullValuePrompt");
            this.txt_Width.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_Width.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_Width.EditValueChanged += new System.EventHandler(this.txt_DiscR2_EditValueChanged);
            // 
            // txt_Length
            // 
            resources.ApplyResources(this.txt_Length, "txt_Length");
            this.txt_Length.EnterMoveNextControl = true;
            this.txt_Length.Name = "txt_Length";
            this.txt_Length.Properties.AccessibleDescription = resources.GetString("txt_Length.Properties.AccessibleDescription");
            this.txt_Length.Properties.AccessibleName = resources.GetString("txt_Length.Properties.AccessibleName");
            this.txt_Length.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Length.Properties.AutoHeight")));
            this.txt_Length.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_Length.Properties.Mask.AutoComplete")));
            this.txt_Length.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_Length.Properties.Mask.BeepOnError")));
            this.txt_Length.Properties.Mask.EditMask = resources.GetString("txt_Length.Properties.Mask.EditMask");
            this.txt_Length.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Length.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Length.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Length.Properties.Mask.MaskType")));
            this.txt_Length.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_Length.Properties.Mask.PlaceHolder")));
            this.txt_Length.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Length.Properties.Mask.SaveLiteral")));
            this.txt_Length.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Length.Properties.Mask.ShowPlaceHolders")));
            this.txt_Length.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_Length.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_Length.Properties.NullValuePrompt = resources.GetString("txt_Length.Properties.NullValuePrompt");
            this.txt_Length.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_Length.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_Length.EditValueChanged += new System.EventHandler(this.txt_DiscR3_EditValueChanged);
            this.txt_Length.KeyDown += new System.Windows.Forms.KeyEventHandler(this.txt_DiscR3_KeyDown);
            // 
            // frm_InvoiceDimenstions
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.txt_Length);
            this.Controls.Add(this.txt_Width);
            this.Controls.Add(this.txt_Height);
            this.Controls.Add(this.textEdit2);
            this.Controls.Add(this.textEdit1);
            this.Controls.Add(this.textEdit7);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.MaximizeBox = false;
            this.Name = "frm_InvoiceDimenstions";
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.TopMost = true;
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_InvoiceDiscs_FormClosing);
            ((System.ComponentModel.ISupportInitialize)(this.textEdit7.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Height.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Width.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Length.Properties)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.TextEdit textEdit7;
        private DevExpress.XtraEditors.TextEdit textEdit1;
        private DevExpress.XtraEditors.TextEdit textEdit2;
        private DevExpress.XtraEditors.TextEdit txt_Height;
        private DevExpress.XtraEditors.TextEdit txt_Width;
        private DevExpress.XtraEditors.TextEdit txt_Length;


    }
}
