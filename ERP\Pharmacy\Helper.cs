﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using DevExpress.XtraEditors;
using DAL;
using DAL.Res;
using System.Windows.Forms;
using Pharmacy.HR;
using System.Data;
using System.Drawing;
using System.Threading.Tasks;
using System.Net.Http;
using Newtonsoft.Json;

namespace Pharmacy.Forms
{


    public class RecievedDocument
    {
        public string uuid { get; set; }
        public string publicUrl { get; set; }
        public string typeName { get; set; }
        public string issuerName { get; set; }
        public string issuerId { get; set; }
        public string receiverType { get; set; }
        public DateTime dateTimeReceived { get; set; }
        public decimal totalSales { get; set; }
        public decimal totalDiscount { get; set; }
        public decimal netAmount { get; set; }
        public decimal total { get; set; }
        public string status { get; set; }
        public string documentStatusReason { get; set; }
        public string documentTypeNamePrimaryLang { get; set; }
        public string documentTypeNameSecondaryLang { get; set; }
        public DateTime dateTimeIssued { get; set; }


    }


    public class ErpHelper
    {

        //test if user able to save data in aclosed period
        public static bool CanSaveInClsedPeriod(DateTime trnsDate, DateTime? ClosePeriodDate, bool EditInClosedPeriod)
        {
            if (ClosePeriodDate != null &&
                   trnsDate <= ClosePeriodDate.Value.Date)
            {
                if (EditInClosedPeriod == true)
                {
                    if (XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResEn.EditInClosedPeriodWarning : ResAr.EditInClosedPeriodWarning,
                    Shared.IsEnglish == true ? ResAccEn.MsgTWarn : ResAccAr.MsgTWarn,
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question)
                    == DialogResult.No)
                        return false;
                }
                else
                {
                    XtraMessageBox.Show(Shared.IsEnglish == true ? ResEn.EditInClosedPeriodDenie : ResAr.EditInClosedPeriodDenie,
                         "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Load DataTable with Drawers, 
        /// and disable lkpDrawer if user has no rights to change it
        /// </summary>
        /// <param name="DrawerDataTable"> Drawer DataTable</param>
        /// <returns>return Default Drawer or first Drawer</returns>
        /// <summary>
        public static int? GetDrawers(DataTable DrawerDataTable, ref DevExpress.XtraEditors.LookUpEdit lkpDrawer)
        {
            int? DefaultDrawerAccountId = 0;
            int counter = 0;
            DrawerDataTable.Columns.Clear();
            DrawerDataTable.Columns.Add("DrawerId");
            DrawerDataTable.Columns.Add("DrawerNameAr");
            DrawerDataTable.Columns.Add("DrawerNameEn");
            DrawerDataTable.Columns.Add("DrawerNotes");
            DrawerDataTable.Columns.Add("AccountId");
            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var DrawerQuery = (from d in DB.ACC_Drawers
                               join c in DB.ACC_Accounts on d.AccountId equals c.AccountId
                               where c.AccSecurityLevel <= Shared.user.AccSecurityLevel
                               select d).ToList();

            if (DrawerQuery.Count > 0)
            {
                foreach (var d in DrawerQuery)
                {
                    if (counter == 0)// get first drawer Id
                        DefaultDrawerAccountId = d.AccountId;

                    DrawerDataTable.Rows.Add(d.DrawerId,
                        d.DrawerNameAr,
                        d.DrawerNameEn,
                        d.DrawerNotes,
                        d.AccountId);

                    counter += 1;
                }
            }

            if (DrawerQuery.Count > 0)
            {
                if (Shared.user.DefaultDrawer != null) //user has defualt acount
                    DefaultDrawerAccountId = Shared.user.DefaultDrawer;
            }

            if (lkpDrawer != null && (!Shared.user.UserChangeDrawer))
                lkpDrawer.Enabled = false;

            return DefaultDrawerAccountId;
        }

        public static async Task<List<RecievedDocument>> GetRecievedDocumentsAsync(string dateFrom, string dateTo)
        {

            HttpClient client = new HttpClient();
            UriBuilder uriBuilder = new UriBuilder(Properties.Settings.Default.BackEndPoint);
            uriBuilder.Port = Properties.Settings.Default.BackEndPort;
            client.BaseAddress = uriBuilder.Uri;
            client.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));
            try
            {
                var response = await client.GetStringAsync($"api/Einvoice/ReceievedDocuments?dateFrom={dateFrom}&dateTo={dateTo}");
                var data = JsonConvert.DeserializeObject<List<RecievedDocument>>(response);
                return data;
            }
            catch { return new List<RecievedDocument> { }; }
        }

        public static void PopulateRecievedDocumentsTable(List<RecievedDocument> documents, out bool isNewAdded)
        {
            isNewAdded = false;
            ERPDataContext DB = new ERPDataContext();  
            var recievedDocuments = new List<E_RecievedDocument>();

            foreach (var document in documents)
            {
                var recievedDocument = new E_RecievedDocument()
                {
                    uuid = document.uuid,
                    dateTimeReceived = document.dateTimeReceived.ToString("dd/MM/yyyy HH:mm:ss"),
                    dateTimeIssued = document.dateTimeIssued.ToString("dd/MM/yyyy HH:mm:ss"),
                    issuerName = document.issuerName,
                    net = document.netAmount,
                    publicUrl = document.publicUrl,
                    typeName = document.typeName,
                    total = document.total,
                    totalDiscount = document.totalDiscount,
                    status = document.status,
                    totalSales = document.totalSales,
                    typeNameAr = document.documentTypeNameSecondaryLang,
                    typeNameEn = document.documentTypeNamePrimaryLang,

                };


                if(DB.E_RecievedDocuments.Any(x => x.uuid == recievedDocument.uuid) == false)
                {
                    recievedDocuments.Add(recievedDocument);
                    isNewAdded = true;

                }

            }
            DB.E_RecievedDocuments.InsertAllOnSubmit(recievedDocuments);
            DB.SubmitChanges();
        }

        public static bool CanEditPostedBill(int sourceId, int processId, bool OfflinePostToGL, bool UserEditPostedBills)
        {
            ERPDataContext DB = new ERPDataContext();

            if (OfflinePostToGL == true
                    && UserEditPostedBills == false
                    && DB.ACC_Journals.Where(x => x.SourceId == sourceId && x.ProcessId == processId)
                    .Select(x => x.IsPosted).FirstOrDefault() == true)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show(Shared.IsEnglish ? ResEn.MsgPostedBill : ResAr.MsgPostedBill, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return false;
            }
            return true;
        }

        public static bool CanEditPostedBill(int JournalId, bool OfflinePostToGL, bool UserEditPostedBills)
        {
            ERPDataContext DB = new ERPDataContext();

            if (OfflinePostToGL == true
                    && UserEditPostedBills == false
                    && DB.ACC_Journals.Where(x => x.JournalId == JournalId)
                    .Select(x => x.IsPosted).FirstOrDefault() == true)
            {
                DevExpress.XtraEditors.XtraMessageBox.Show(Shared.IsEnglish ? ResEn.MsgPostedBill : ResAr.MsgPostedBill, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return false;
            }
            return true;
        }

        public static bool ValidateInvCodeExist(bool code_exist, TextEdit txt)
        {
            if (code_exist == false)
            {
                txt.ResetBackColor();
                txt.ToolTipIconType = DevExpress.Utils.ToolTipIconType.None;
                txt.ToolTip = "";
                txt.ErrorText = "";
                return true;
            }

            if (Shared.st_Store.InvoicesCodeRedundancy == true)//warnning
            {
                txt.BackColor = Color.Red;
                txt.ToolTipIconType = DevExpress.Utils.ToolTipIconType.Warning;
                txt.ToolTip = Shared.IsEnglish ? ResICEn.MsgCodeExist : ResICAr.MsgCodeExist;
                return true;
            }
            else if (Shared.st_Store.InvoicesCodeRedundancy == false)//Prevent
            {
                txt.ErrorText = Shared.IsEnglish ? ResICEn.MsgCodeExist : ResICAr.MsgCodeExist;
                txt.Focus();
                return false;
            }
            return true;
        }

        public static int ReEvaluate_OutTrans(int storeId)
        {
            ERPDataContext DB = new ERPDataContext();
            var lst_stores = DB.IC_Stores.Where(x => storeId == 0 ? true : x.StoreId == storeId).ToList();

            int count = 0;
            foreach (var s in lst_stores)
            {
                #region Out_Trasnactions
                var data = (from d in DB.IC_ItemStores
                            where d.IsInTrns == false
                            && d.StoreId == s.StoreId
                            select d);
                #endregion

                foreach (var d in data)
                {
                    var avg_price = (from x in DB.IC_ItemStores
                                     where x.ItemStoreId != d.ItemStoreId && x.ItemId == d.ItemId && x.StoreId == s.StoreId
                                     && x.InsertTime <= d.InsertTime
                                     && x.Qty > 0
                                     select x.IsInTrns ? (x.PurchasePrice / x.Qty) : (-1 * x.PurchasePrice / x.Qty)).ToList().DefaultIfEmpty(0).Sum();

                    if (avg_price <= 0)
                        avg_price = 0;

                    d.PurchasePrice = avg_price;
                    DB.SubmitChanges();

                    count++;
                }
            }

            MessageBox.Show(" تم تعديل تكلفة عدد " + count + " حركة ");
            return count;
        }

        public static int ReEvaluate_Manfs()
        {
            ERPDataContext DB = new ERPDataContext();
            var manfs_Id = DB.Manufacturings.Where(x => x.IsFinished).ToList();

            foreach (var mId in manfs_Id)
            {
                Update_Manufacture(mId);
            }
            MessageBox.Show(manfs_Id.Count + "");
            return manfs_Id.Count();
        }

        public static void Update_Manufacture(Manufacturing manf)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            decimal total_expenses = DB.Manf_Expenses.Where(x => x.Manf_Id == manf.Manf_Id).Select(x => x.Expenses_Value).ToList().DefaultIfEmpty(0).Sum();
            decimal total_wages = DB.Manf_Wages.Where(x => x.Manf_Id == manf.Manf_Id).Select(x => x.Total_Wage).ToList().DefaultIfEmpty(0).Sum();
            decimal total_purchasePrice = DB.IC_ItemStores.Where(x => x.IsInTrns == false && x.ProcessId == (int)Process.Manufacturing
                && x.SourceId == manf.Manf_Id).Select(x => x.PurchasePrice).ToList().DefaultIfEmpty(0).Sum();

            var manf_products = DB.ManfProducts.Where(x => x.Manf_Id == manf.Manf_Id);
            decimal total_qty = manf_products.Select(x => x.Qty).ToList().DefaultIfEmpty(0).Sum();
            foreach (var prd in manf_products)
            {
                decimal prod_ratio = 0;
                if (manf_products.Count() == 1)
                    prod_ratio = 1;
                else if (manf.ProductSupposedTotalCost > 0)
                    prod_ratio = prd.SupposedTotalCost / manf.ProductSupposedTotalCost;

                if (manf.ProductSupposedTotalCost <= 0)
                    prd.ActualTotalCost = prd.Qty * (total_purchasePrice / total_qty);
                else
                    prd.ActualTotalCost = prod_ratio * total_purchasePrice;

                var prd_ItemStore = DB.IC_ItemStores.Where(x => x.IsInTrns && x.ProcessId == (int)Process.Manufacturing && x.SourceId == manf.Manf_Id).FirstOrDefault();
                prd_ItemStore.PurchasePrice = prd.ActualTotalCost;
                DB.SubmitChanges();
            }

        }


        public static void ReEvaluate_UsingBatch()
        {
            ERPDataContext DB = new ERPDataContext();
            var batchs = (from d in DB.IC_ItemStores
                          where d.Batch != string.Empty
                          && (d.ProcessId == (int)Process.PurchaseInvoice || d.ProcessId == (int)Process.InTrns)
                          group d by new { d.ItemId, d.Batch } into grp
                          let total_cost = grp.Select(x => x.PurchasePrice).Sum()
                          let total_qty = grp.Select(x => x.Qty).Sum()
                          select new
                          {
                              grp.Key.ItemId,
                              grp.Key.Batch,
                              UnitCost = total_cost <= 0 || total_qty <= 0 ? 0 : total_cost / total_qty
                          }).ToList();

            foreach (var d in batchs)
            {
                var data = from x in DB.IC_ItemStores
                           where x.ItemId == d.ItemId && x.Batch == d.Batch
                           select x;

                foreach (var x in data)
                {
                    x.PurchasePrice = x.Qty * d.UnitCost;
                }
            }

            DB.SubmitChanges();
            MessageBox.Show("تم اعادة التقييم بنجاح");
        }

        /// <summary>
        /// open source process screen from statement reports
        /// </summary>
        /// <param name="processId"></param>
        /// <param name="sourceId"></param>
        /// <param name="journalId"></param>
        /// <param name="OpenFromJournal"> true if opened from journal, false if opened from item_store</param>
        public static void OpenSourceProcess(int processId, int sourceId, int journalId, bool OpenFromJournal)
        {
            ERPDataContext DB = new ERPDataContext();

            if (processId < 1 || sourceId < 1)
                return;

            #region Invoices
            
            if (processId == (int)Process.SellInvoice &&
                (Shared.LstUserPrvlg == null ||
                (Shared.LstUserPrvlg != null &&
                Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.SL_Invoice).Count() > 0)))
            {
                int PinvId = sourceId;
                if (OpenFromJournal == false)
                    PinvId = DB.SL_InvoiceDetails.Where(p => p.SL_InvoiceDetailId == sourceId).Select(p => p.SL_InvoiceId).FirstOrDefault();

                if (PinvId == 0)
                    return;

                if (ErpUtils.IsFormOpen(typeof(frm_SL_Invoice)))
                    Application.OpenForms["frm_SL_Invoice"].Close();

                frm_SL_Invoice f = new frm_SL_Invoice(PinvId);
                f.BringToFront();
                f.Show();
            }
            if (processId == (int)Process.SellReturn &&
                (Shared.LstUserPrvlg == null ||
                (Shared.LstUserPrvlg != null &&
                Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.SL_Return).Count() > 0)))
            {
                int PinvId = sourceId;
                if (OpenFromJournal == false)
                    PinvId = DB.SL_ReturnDetails.Where(p => p.SL_ReturnDetailId == sourceId).Select(p => p.SL_ReturnId).FirstOrDefault();

                if (PinvId == 0)
                    return;

                if (ErpUtils.IsFormOpen(typeof(frm_SL_Return)))
                    Application.OpenForms["frm_SL_Return"].Close();

                frm_SL_Return f = new frm_SL_Return(PinvId);
                f.BringToFront();
                f.Show();
            }
            #endregion


        }

        public static void OpenJobOrder(int jobOrderId,int customerId)
        {
            ERPDataContext DB = new ERPDataContext();

           

        }
        // to make Manufacturing
        public static void Save_Manufacture(int InvoiceId, List<IC_Store> lst_stores, int defaultStoreId)
        {
            ERPDataContext DB = new ERPDataContext();
            DataTable dt_manfProducts = new DataTable();
            DataTable dt_actualExpenses = new DataTable();
            DataTable dt_actualRaws = new DataTable();
            #region Products
            dt_manfProducts.Columns.Add("ItemId", typeof(Int32)).AllowDBNull = false;
            dt_manfProducts.Columns.Add("Qty", typeof(Double)).AllowDBNull = false;
            dt_manfProducts.Columns.Add("UomFactor");
            dt_manfProducts.Columns.Add("UOMId");
            dt_manfProducts.Columns.Add("Height").DefaultValue = 1;
            dt_manfProducts.Columns.Add("Width").DefaultValue = 1;
            dt_manfProducts.Columns.Add("Length").DefaultValue = 1;
            dt_manfProducts.Columns.Add("PiecesCount").DefaultValue = 0;
            dt_manfProducts.Columns.Add("SupposedTotalCost", typeof(decimal));
            dt_manfProducts.Columns.Add("ActualTotalCost", typeof(decimal));
            dt_manfProducts.Columns.Add("IsExpire", typeof(bool));
            dt_manfProducts.Columns.Add("Expire", typeof(DateTime));
            dt_manfProducts.Columns.Add("Batch");
            dt_manfProducts.Columns.Add("ManfProductsId");
            dt_manfProducts.Columns.Add("ActualQty", typeof(decimal)).AllowDBNull = false;
            dt_manfProducts.Columns.Add("OperationalWaste", typeof(decimal));
            dt_manfProducts.Columns.Add("BOMId", typeof(int));
            dt_manfProducts.Columns.Add("CategoryId");


            // grid_Products.DataSource = dt_manfProducts;
            #endregion

            #region Actual_Expenses
            dt_actualExpenses.Columns.Add("Expenses_Name");
            dt_actualExpenses.Columns.Add("Expenses_Value").DataType = typeof(double);
            dt_actualExpenses.Columns["Expenses_Value"].AllowDBNull = false;
            dt_actualExpenses.Columns.Add("PayAccountId", typeof(Int32));
            dt_actualExpenses.Columns["PayAccountId"].AllowDBNull = false;
            //gridActualExpenses.DataSource = dt_actualExpenses;
            #endregion

            #region Actual_Raws
            dt_actualRaws.Columns.Add("StoreId", typeof(Int32)).AllowDBNull = false;
            dt_actualRaws.Columns["StoreId"].DefaultValue = defaultStoreId;
            dt_actualRaws.Columns.Add("ItemId").DataType = typeof(Int32);
            dt_actualRaws.Columns["ItemId"].AllowDBNull = false;
            dt_actualRaws.Columns.Add("ItemCode1");
            dt_actualRaws.Columns.Add("ItemCode2");
            dt_actualRaws.Columns.Add("CurrentQty");
            dt_actualRaws.Columns.Add("Qty").DataType = typeof(Double);
            dt_actualRaws.Columns["Qty"].AllowDBNull = false;
            dt_actualRaws.Columns["Qty"].DefaultValue = 1;
            dt_actualRaws.Columns.Add("PurchasePrice");
            dt_actualRaws.Columns.Add("UomFactor");
            dt_actualRaws.Columns.Add("UOMId");
            dt_actualRaws.Columns.Add("Height").DefaultValue = 1;
            dt_actualRaws.Columns.Add("Width").DefaultValue = 1;
            dt_actualRaws.Columns.Add("Length").DefaultValue = 1;
            dt_actualRaws.Columns.Add("PiecesCount").DefaultValue = 0;
            dt_actualRaws.Columns.Add("QC");
            dt_actualRaws.Columns.Add("IsExpire", typeof(bool));
            dt_actualRaws.Columns.Add("Expire", typeof(DateTime));
            dt_actualRaws.Columns.Add("Batch");
            dt_actualRaws.Columns.Add("TotalCost", typeof(decimal));
            dt_actualRaws.Columns.Add("CategoryId");


            #endregion

            var invoice = DB.SL_Invoices.FirstOrDefault(a => a.SL_InvoiceId == InvoiceId);
            if (invoice != null)
            {
                var InvoiceDetail = (from id in DB.SL_InvoiceDetails
                                     where id.SL_InvoiceId == InvoiceId
                                     join item in DB.IC_Items on id.ItemId equals item.ItemId
                                     select new { id, item }).ToList();
                dt_manfProducts.Rows.Clear();
                dt_actualRaws.Rows.Clear();
                dt_actualExpenses.Rows.Clear();
                foreach (var detail in InvoiceDetail)
                {
                    DataRow dr = dt_manfProducts.NewRow();
                    dr["ItemId"] = detail.id.ItemId;
                    dr["CategoryId"] = detail.item.Category;
                    dr["Qty"] = decimal.ToDouble(detail.id.Qty);
                    if (detail.id.UOMId == detail.item.SmallUOM)
                        dr["UomFactor"] = 1;
                    else if (detail.id.UOMId == detail.item.MediumUOM)
                        dr["UomFactor"] = detail.item.MediumUOMFactor;
                    else
                        dr["UomFactor"] = detail.item.LargeUOMFactor;

                    dr["UOMId"] = detail.id.UOMId;
                    dr["Height"] = decimal.ToDouble(detail.item.Height);
                    dr["Width"] = decimal.ToDouble(detail.item.Width);
                    dr["Length"] = decimal.ToDouble(detail.item.Length);
                    dr["PiecesCount"] = decimal.ToDouble(detail.id.PiecesCount);
                    dr["SupposedTotalCost"] = 0;
                    dr["ActualTotalCost"] = 0;

                    dr["IsExpire"] = detail.item.IsExpire;
                    if (detail.id.Expire != null)
                        dr["Expire"] = detail.id.Expire;
                    dr["Batch"] = detail.id.Batch;
                    dr["ActualQty"] = detail.id.Qty;
                    //dr["OperationalWaste"] = detail.OperationalWaste;

                    //if (detail.BOMId == null)
                    //    dr["BOMId"] = DBNull.Value;
                    //else
                    //    dr["BOMId"] = detail.BOMId;

                    dt_manfProducts.Rows.Add(dr);

                    int Product_Item_Id = detail.id.ItemId;
                    decimal ProductQty = detail.id.Qty;
                    decimal product_factor = Convert.ToDecimal(dr["UomFactor"]);

                    int? BOMId = 0;
                    var BOM = DB.IC_BOMs.FirstOrDefault(a => a.ProductItemId == detail.id.ItemId);
                    if (BOM != null)
                        BOMId = BOM.BOMId;
                    dr["BOMId"] = BOMId;


                    //BOMId = Convert.ToInt32(dr_prd["BOMId"]);

                    var bom_data = (from d in DB.IC_BOMs
                                    where d.BOMId == BOMId
                                    join t in DB.IC_Items on d.ProductItemId equals t.ItemId
                                    select new
                                    {
                                        bom_factor = d.UomId == t.SmallUOM ? "1" : d.UomId == t.MediumUOM ?
                                    t.MediumUOMFactor : t.LargeUOMFactor,
                                        d.Qty,
                                    }).FirstOrDefault();

                    decimal BOM_factor = MyHelper.FractionToDouble(bom_data.bom_factor) * bom_data.Qty;
                    product_factor = product_factor / BOM_factor;

                    if (Shared.st_Store.MultiplyDimensions != (byte)Dimensions.Distinguish)
                        ProductQty = Convert.ToDecimal(dr["Qty"]) * Convert.ToDecimal(dr["Height"]) *
                                     Convert.ToDecimal(dr["Width"]) * Convert.ToDecimal(dr["Length"]);

                    var raws = (from d in DB.IC_BOMDetails
                                where d.BOMId == BOMId
                                join t in DB.IC_Items on d.RawItemId equals t.ItemId
                                select new
                                {
                                    Product_Item_Id = Product_Item_Id,
                                    d.Qty,
                                    UOMId = d.UomId,
                                    t.SmallUOM,
                                    t.MediumUOM,
                                    t.LargeUOM,
                                    t.MediumUOMFactor,
                                    t.LargeUOMFactor,
                                    Raw_Item_Id = d.RawItemId,
                                    t.ItemNameAr,
                                    t.PurchasePrice,
                                    t.IsExpire,
                                    t.ItemType,
                                    t.Category
                                }).ToList();

                    var supposed_Raws = (from d in raws
                                         let factor = d.UOMId == d.SmallUOM ? 1 :
                                                     (d.UOMId == d.MediumUOM.Value ? MyHelper.FractionToDouble(d.MediumUOMFactor) :
                                                                                     MyHelper.FractionToDouble(d.LargeUOMFactor))
                                         select new supposed_raw_Obj()
                                         {
                                             Raw_Item_ID = d.ItemNameAr,
                                             Qty = decimal.ToDouble(d.Qty * ProductQty * product_factor),
                                             UomId = d.UOMId,
                                             ItemId = d.Raw_Item_Id,
                                             Factor = factor,
                                             PurchasePrice = d.PurchasePrice,
                                             Total_Price = decimal.ToDouble(factor * product_factor * (decimal)d.Qty * (decimal)ProductQty * d.PurchasePrice),
                                             IsExpire = d.IsExpire,
                                             ItemType = d.ItemType,
                                             CurrentQty = d.ItemType == (int)ItemType.Service ? 0 : DB.IC_ItemStores.Where(x => x.ItemId == d.Raw_Item_Id).Select(x => x.IsInTrns ? x.Qty : x.Qty * -1).ToList().DefaultIfEmpty(0).Sum() / factor,
                                             CategoryId = d.Category
                                         }).ToList();

                    foreach (var r in supposed_Raws)
                    { //lst_supposedRaws.Add(r); 
                        DataRow dract = dt_actualRaws.NewRow();
                        //mahmoud:27-1-2013
                        dract["StoreId"] = defaultStoreId;
                        dract["ItemId"] = r.ItemId;
                        dract["CategoryId"] = r.CategoryId;
                        dract["Qty"] = r.Qty;
                        dract["UOMId"] = r.UomId;
                        dract["PurchasePrice"] = r.PurchasePrice * r.Factor;
                        dract["UomFactor"] = r.Factor;
                        dract["CurrentQty"] = decimal.ToDouble(MyHelper.GetItemQty(DateTime.Now, r.ItemId, defaultStoreId)
                            / r.Factor);
                        dract["IsExpire"] = r.IsExpire;

                        dt_actualRaws.Rows.Add(dract);
                    }
                    try
                    {
                        // DB.Manf_Expenses
                        var supposed_Expenses = (from d in DB.IC_BOMExpenses
                                                 where d.BOMId == BOMId
                                                 select new
                                                 {
                                                     Expenses_Name = "",
                                                     PayAccountId = d.AccountId,
                                                     Expenses_Value = Decimal.ToDouble(d.Value * Math.Round(product_factor, 5) * ProductQty)
                                                 });
                        foreach (var expense in supposed_Expenses)
                        { /*lst_supposedExpenses.Add(r);*/
                            DataRow drexp = dt_actualExpenses.NewRow();
                            drexp["PayAccountId"] = expense.PayAccountId;
                            drexp["Expenses_Name"] = expense.Expenses_Name;
                            drexp["Expenses_Value"] = expense.Expenses_Value;
                            dt_actualExpenses.Rows.Add(drexp);

                        }


                        double product_total_Raws = supposed_Raws.Select(x => x.Total_Price).ToList().DefaultIfEmpty(0).Sum();
                        double product_total_expenses = supposed_Expenses.Select(x => x.Expenses_Value).ToList().DefaultIfEmpty(0).Sum();

                        dr["SupposedTotalCost"] = product_total_Raws + product_total_expenses;
                    }
                    catch { }



                }



                DAL.Manufacturing manf = new DAL.Manufacturing();
                manf.Manf_NO = DB.Manufacturings.Select(x => x.Manf_NO).ToList().DefaultIfEmpty(0).Max() + 1;
                manf.StartDate = invoice.InvoiceDate;

                manf.EndDate = DateTime.Now;
                manf.Notes = "";
                manf.ProductActualTotalCost = dt_manfProducts.AsEnumerable().Sum(r => r.Field<decimal>("ActualTotalCost"));
                manf.ProductSupposedTotalCost = dt_manfProducts.AsEnumerable().Sum(r => r.Field<decimal>("SupposedTotalCost"));
                manf.OperationalWaste = 0;
                manf.ProductStoreId = defaultStoreId;

                if (invoice.CostCenterId == null || invoice.CostCenterId.ToString() == string.Empty || invoice.CostCenterId == 0)
                    manf.CostCenterId = null;
                else
                    manf.CostCenterId = invoice.CostCenterId;

                manf.IsFinished = false;

                //if (Manf_ID == 0)
                DB.Manufacturings.InsertOnSubmit(manf);
                DB.SubmitChanges();

                List<StoreItem> lst_outitems = new List<StoreItem>();
                foreach (DataRow dr in dt_actualRaws.Rows)
                {
                    if (dr.RowState == DataRowState.Deleted)
                        continue;

                    #region Save_Manf_Details_Consumings
                    DAL.ManfDetail manf_detail = new DAL.ManfDetail();
                    manf_detail.ItemId = Convert.ToInt32(dr["ItemId"]);
                    manf_detail.Manf_Id = manf.Manf_Id;
                    manf_detail.PurchasePrice = Convert.ToDecimal(dr["PurchasePrice"]);
                    manf_detail.Qty = Convert.ToDecimal(dr["Qty"]);
                    manf_detail.StoreId = Convert.ToInt32(dr["StoreId"]);
                    manf_detail.UOMId = Convert.ToInt32(dr["UOMId"]);

                    manf_detail.Height = Convert.ToDecimal(dr["Height"]);
                    manf_detail.Length = Convert.ToDecimal(dr["Length"]);
                    manf_detail.Width = Convert.ToDecimal(dr["Width"]);
                    manf_detail.PiecesCount = Convert.ToDecimal(dr["PiecesCount"]);

                    if (dr["QC"] == DBNull.Value || dr["QC"] == null || dr["QC"].ToString().Trim() == string.Empty)
                        manf_detail.QC = null;
                    else
                        manf_detail.QC = dr["QC"].ToString();

                    if (Convert.ToBoolean(dr["IsExpire"]) == false || dr["Expire"] == DBNull.Value || dr["Expire"] == null)
                        manf_detail.Expire = null;
                    else
                    {
                        DateTime temp = Convert.ToDateTime(dr["Expire"]);
                        temp = temp.AddDays(-temp.Day + 1);
                        manf_detail.Expire = temp;
                    }
                    if (dr["Batch"] == DBNull.Value || dr["Batch"] == null || dr["Batch"].ToString().Trim() == string.Empty)
                        manf_detail.Batch = null;
                    else
                        manf_detail.Batch = dr["Batch"].ToString();

                    DB.ManfDetails.InsertOnSubmit(manf_detail);
                    #endregion


                    var itm = DB.IC_Items.Where(x => x.ItemId == manf_detail.ItemId).FirstOrDefault();

                    decimal factor = Convert.ToDecimal(dr["UomFactor"]);
                    lst_outitems.Add(new StoreItem(Shared.st_Store.MultiplyDimensions, manf_detail.ItemId, itm.ItemType, manf.Manf_Id, 0, manf_detail.Qty * factor,
                    1, 1, manf_detail.Expire, manf_detail.Batch, manf_detail.QC, null, manf_detail.Length.Value, manf_detail.Width.Value, manf_detail.Height.Value,
                    manf_detail.PiecesCount, itm.mtrxParentItem, itm.mtrxAttribute1, itm.mtrxAttribute2, itm.mtrxAttribute3, 0, 0, manf_detail, string.Empty, string.Empty,
                            Convert.ToInt32(dr["CategoryId"]), 0));

                }

                var groupbystore = (from d in lst_outitems
                                    let detail = d.Source as ManfDetail
                                    group d by detail.StoreId into grp
                                    join s in lst_stores on grp.Key equals s.StoreId
                                    select new
                                    {
                                        StoreId = grp.Key,
                                        CostMethod = s.CostMethod,
                                        lst_outs = grp.Select(x => x).ToList(),
                                    }).ToList();

                foreach (var d in groupbystore)
                {
                    var lst = MyHelper.Subtract_from_store(d.StoreId, d.CostMethod, (int)Process.Manufacturing, manf.StartDate, d.lst_outs);
                    DB.IC_ItemStores.InsertAllOnSubmit(lst);
                }

                lst_outitems.ForEach(x => ((ManfDetail)x.Source).TotalCost = x.TotalCost);

                DB.SubmitChanges();

                #region Sava_ActualExpenses
                var manf_expenses = from d in DB.Manf_Expenses
                                    where d.Manf_Id == manf.Manf_Id
                                    select d;

                DB.Manf_Expenses.DeleteAllOnSubmit(manf_expenses);
                DB.SubmitChanges();

                foreach (DataRow dr in dt_actualExpenses.Rows)
                {
                    if (dr.RowState == DataRowState.Deleted)
                        continue;

                    DAL.Manf_Expense expense = new DAL.Manf_Expense();
                    expense.PayAccountId = Convert.ToInt32(dr["PayAccountId"]);
                    expense.Expenses_Name = dr["Expenses_Name"].ToString();
                    expense.Expenses_Value = Convert.ToDecimal(dr["Expenses_Value"]);
                    expense.Manf_Id = manf.Manf_Id;
                    DB.Manf_Expenses.InsertOnSubmit(expense);
                }
                DB.SubmitChanges();
                #endregion


                #region Delete_manf_Jornal
                var manf_jornal = from j in DB.ACC_Journals
                                  where j.ProcessId == (int)Process.Manufacturing && j.SourceId == manf.Manf_Id
                                  select j;

                if (manf_jornal.Count() > 0)
                {
                    int manf_jornal_id = manf_jornal.FirstOrDefault().JournalId;
                    DB.ACC_JournalDetails.DeleteAllOnSubmit(DB.ACC_JournalDetails.Where(x => x.JournalId == manf_jornal_id));
                    DB.SubmitChanges();
                    DB.ACC_Journals.DeleteAllOnSubmit(manf_jornal);
                    DB.SubmitChanges();
                }
                #endregion

                int Manf_ID = manf.Manf_Id;
                // decimal waste = 0;
                // manf.ProductActualTotalCost = GetProductActualTotalCost(out waste);
                DB.SubmitChanges();

                #region Save_Products
                foreach (DataRow dr_prd in dt_manfProducts.Rows)
                {
                    if (dr_prd.RowState == DataRowState.Deleted)
                        continue;

                    decimal Total_Qty = Convert.ToDecimal(dr_prd["Height"]) *
                                        Convert.ToDecimal(dr_prd["Length"]) *
                                        Convert.ToDecimal(dr_prd["Width"]) *
                                        Convert.ToDecimal(dr_prd["Qty"]) * Convert.ToDecimal(dr_prd["UomFactor"]);

                    if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish)
                        Total_Qty = Convert.ToDecimal(dr_prd["Qty"]) * Convert.ToDecimal(dr_prd["UomFactor"]);


                    DAL.ManfProduct manf_prd = new DAL.ManfProduct();
                    manf_prd.ItemId = Convert.ToInt32(dr_prd["ItemId"]);
                    manf_prd.Manf_Id = manf.Manf_Id;
                    manf_prd.Qty = Convert.ToDecimal(dr_prd["Qty"]);
                    manf_prd.UOMId = Convert.ToInt32(dr_prd["UOMId"]);
                    manf_prd.Height = Convert.ToDecimal(dr_prd["Height"]);
                    manf_prd.Length = Convert.ToDecimal(dr_prd["Length"]);
                    manf_prd.Width = Convert.ToDecimal(dr_prd["Width"]);
                    manf_prd.PiecesCount = Convert.ToDecimal(dr_prd["PiecesCount"]);
                    manf_prd.ActualTotalCost = Convert.ToDecimal(dr_prd["ActualTotalCost"]);
                    manf_prd.SupposedTotalCost = Convert.ToDecimal(dr_prd["SupposedTotalCost"]);
                    manf_prd.ActualQty = Convert.ToDecimal(dr_prd["ActualQty"]);
                    //manf_prd.OperationalWaste = Convert.ToDecimal(dr_prd["OperationalWaste"]);

                    if (Convert.ToBoolean(dr_prd["IsExpire"]) == false || dr_prd["Expire"] == DBNull.Value || dr_prd["Expire"] == null)
                        manf_prd.Expire = null;
                    else
                    {
                        DateTime temp = Convert.ToDateTime(dr_prd["Expire"]);
                        temp = temp.AddDays(-temp.Day + 1);
                        manf_prd.Expire = temp;
                    }
                    if (dr_prd["Batch"] == DBNull.Value || dr_prd["Batch"] == null || dr_prd["Batch"].ToString().Trim() == string.Empty)
                        manf_prd.Batch = null;
                    else
                        manf_prd.Batch = dr_prd["Batch"].ToString();

                    if (dr_prd["BOMId"] == DBNull.Value || dr_prd["BOMId"].ToString() == string.Empty || Convert.ToInt32(dr_prd["BOMId"]) == 0)
                        manf_prd.BOMId = null;
                    else
                        manf_prd.BOMId = Convert.ToInt32(dr_prd["BOMId"]);

                    DB.ManfProducts.InsertOnSubmit(manf_prd);
                    DB.SubmitChanges();
                    dr_prd["ManfProductsId"] = manf_prd.ManfProductsId;
                }

                #endregion




                int? CostCenterId = null;
                if (invoice.CostCenterId == null || invoice.CostCenterId.ToString() == string.Empty || invoice.CostCenterId == 0)
                    CostCenterId = null;
                else
                    CostCenterId = Convert.ToInt32(invoice.CostCenterId);

                try
                {
                    DB.Connection.Open();
                    DB.Transaction = DB.Connection.BeginTransaction();

                    #region Record_Expense_and_JornalData
                    var manf_expensess = from d in DB.Manf_Expenses
                                         where d.Manf_Id == Manf_ID
                                         select d;

                    DateTime InsertTime = DateTime.Now.Date;

                    decimal total_expenses = manf_expensess.Select(x => x.Expenses_Value).ToList().DefaultIfEmpty(0).Sum();
                    /*في حالة جرد مستمر يقفل المنتجات اجمالي التكلفة في حساب مراقبة الانتاج*/
                    if (total_expenses > 0 || Shared.StockIsPeriodic == false)
                    {
                        #region Jornal
                        DAL.ACC_Journal j = new DAL.ACC_Journal();
                        j.JCode = HelperAcc.Get_Jornal_Code();
                        j.JNumber = manf.Manf_NO.ToString().Trim();

                        j.InsertDate = InsertTime;
                        j.JNotes = (Shared.IsEnglish ? ResEn.ManfNo : ResAr.ManfNo) + " " + manf.Manf_NO;
                        j.ProcessId = (int)Process.Manufacturing;
                        j.SourceId = Manf_ID;
                        j.InsertUser = Shared.UserId;
                        j.IsPosted = !Shared.OfflinePostToGL;
                        j.StoreId = defaultStoreId;
                        j.CrncId = invoice.CrncId;
                        j.CrncRate = invoice.CrncRate;
                        j.Monthly_Code = HelperAcc.Get_Jornal_Monthly_Code(j.InsertDate, j.ProcessId);
                        DB.ACC_Journals.InsertOnSubmit(j);
                        DB.SubmitChanges();
                        #endregion

                        #region expenses
                        if (total_expenses > 0)
                        {
                            DAL.ACC_JournalDetail d1 = new DAL.ACC_JournalDetail();
                            d1.JournalId = j.JournalId;
                            d1.AccountId = Shared.st_Store.ManufacturingExpAcc.Value;                     //حساب مصاريف التشغيل
                            d1.Debit = total_expenses;
                            d1.Credit = 0;
                            d1.Notes = j.JNotes;
                            d1.CrncId = 0;
                            d1.CrncRate = 1;
                            d1.CostCenter = CostCenterId;
                            DB.ACC_JournalDetails.InsertOnSubmit(d1);
                            DB.SubmitChanges();

                            foreach (var expens in manf_expensess)
                            {
                                #region Expense_Jornal_Detail
                                DAL.ACC_JournalDetail d2 = new DAL.ACC_JournalDetail();
                                d2.JournalId = j.JournalId;
                                d2.AccountId = expens.PayAccountId;
                                d2.Debit = 0;
                                d2.Credit = expens.Expenses_Value;
                                d2.Notes = (Shared.IsEnglish ? ResEn.txtmanfExpense : ResAr.txtmanfExpense) + " " + manf.Manf_NO + "\r\n" +
                                    expens.Expenses_Name;
                                d2.CrncId = 0;
                                d2.CrncRate = 1;
                                DB.ACC_JournalDetails.InsertOnSubmit(d2);
                                DB.SubmitChanges();
                                #endregion
                            }
                        }
                        #endregion

                        #region Raws
                        /*جرد مستمر من حساب مراقبة الانتاج الى مخزون الخامات*/
                        if (Shared.StockIsPeriodic == false)
                        {
                            if (!Shared.ItemsPostingAvailable)
                            {
                                var raws = (from d in DB.IC_ItemStores
                                            where d.ProcessId == (int)Process.Manufacturing && d.SourceId == Manf_ID && d.IsInTrns == false
                                            group d by d.StoreId into grp
                                            let TotalCost = grp.Select(x => x.PurchasePrice).Sum()
                                            where TotalCost > 0
                                            select new
                                            {
                                                StoreId = grp.Key,
                                                TotalCost,
                                            }).ToList();

                                decimal total_raws = raws.Select(x => x.TotalCost).ToList().DefaultIfEmpty(0).Sum();

                                if (total_raws > 0)
                                {
                                    DAL.ACC_JournalDetail d1 = new DAL.ACC_JournalDetail();
                                    d1.JournalId = j.JournalId;
                                    d1.AccountId = Shared.st_Store.ManufacturingExpAcc.Value;                     //حساب مصاريف التشغيل
                                    d1.Debit = total_raws;
                                    d1.Credit = 0;
                                    d1.Notes = j.JNotes;
                                    d1.CrncId = 0;
                                    d1.CrncRate = 1;
                                    d1.CostCenter = CostCenterId;
                                    DB.ACC_JournalDetails.InsertOnSubmit(d1);
                                    DB.SubmitChanges();
                                }

                                foreach (var raw in raws)
                                {
                                    #region raws_Jornal_Detail
                                    DAL.ACC_JournalDetail d2 = new DAL.ACC_JournalDetail();
                                    d2.JournalId = j.JournalId;
                                    d2.AccountId = lst_stores.Where(x => x.StoreId == raw.StoreId).Select(x => x.PurchaseAccount).FirstOrDefault();
                                    d2.Debit = 0;
                                    d2.Credit = raw.TotalCost;
                                    d2.Notes = j.JNotes;
                                    d2.CrncId = 0;
                                    d2.CrncRate = 1;
                                    DB.ACC_JournalDetails.InsertOnSubmit(d2);
                                    DB.SubmitChanges();
                                    #endregion
                                }
                            }
                            else
                            {
                                #region Raws
                                var raws = (from d in DB.IC_ItemStores
                                            where d.ProcessId == (int)Process.Manufacturing && d.SourceId == Manf_ID && d.IsInTrns == false
                                            group d by d.ItemId into grp
                                            join i in DB.IC_Items
                                            on grp.Key equals i.ItemId
                                            join c in DB.IC_Categories
                                            on i.Category equals c.CategoryId
                                            let TotalCost = grp.Select(x => x.PurchasePrice).Sum()
                                            where TotalCost > 0
                                            select new
                                            {
                                                ItemId = grp.Key,
                                                TotalCost,
                                                catid = i.Category,
                                                InvAcc = c.InvAcc.Value
                                            }).ToList();

                                decimal total_raws = raws.Select(x => x.TotalCost).ToList().DefaultIfEmpty(0).Sum();

                                if (total_raws > 0)
                                {
                                    DAL.ACC_JournalDetail d1 = new DAL.ACC_JournalDetail();
                                    d1.JournalId = j.JournalId;
                                    d1.AccountId = Shared.st_Store.ManufacturingExpAcc.Value;                     //حساب مصاريف التشغيل
                                    d1.Debit = total_raws;
                                    d1.Credit = 0;
                                    d1.Notes = j.JNotes;
                                    d1.CrncId = 0;
                                    d1.CrncRate = 1;
                                    d1.CostCenter = CostCenterId;
                                    DB.ACC_JournalDetails.InsertOnSubmit(d1);
                                    DB.SubmitChanges();
                                }

                                //post inv
                                var invRows = from x in raws
                                              where x.TotalCost > 0
                                              group x by x.InvAcc into grp
                                              select new
                                              {
                                                  InvAcc = grp.Key,
                                                  Cost = grp.Sum(x => x.TotalCost)
                                              };

                                foreach (var raw in invRows)
                                {
                                    #region raws_Jornal_Detail
                                    DAL.ACC_JournalDetail d2 = new DAL.ACC_JournalDetail();
                                    d2.JournalId = j.JournalId;
                                    d2.AccountId = raw.InvAcc;
                                    d2.Debit = 0;
                                    d2.Credit = raw.Cost;
                                    d2.Notes = j.JNotes;
                                    d2.CrncId = 0;
                                    d2.CrncRate = 1;
                                    DB.ACC_JournalDetails.InsertOnSubmit(d2);
                                    DB.SubmitChanges();
                                    #endregion
                                }
                                #endregion
                                var lst_Cat = MyHelper.GetChildCategoriesList();
                                #region Products
                                List<ItemPosting> lstPost = new List<ItemPosting>();

                                for (int w = 0; w < dt_manfProducts.Rows.Count; w++)
                                {
                                    lstPost.Add(new ItemPosting
                                    {
                                        catId = Convert.ToInt32(dt_manfProducts.Rows[w]["CategoryId"]),
                                        Cost = Convert.ToDecimal(dt_manfProducts.Rows[w]["ActualTotalCost"]),
                                        InvAcc = lst_Cat.Where(x => x.CategoryId == Convert.ToInt32(dt_manfProducts.Rows[w]["CategoryId"])).Select(x => x.InvAcc).First().Value
                                    });
                                }

                                var lstPost2 = from w in lstPost
                                               group w by w.InvAcc into grp
                                               select new
                                               {
                                                   InvAcc = grp.Key,
                                                   Cost = grp.Sum(x => x.Cost)
                                               };

                                foreach (var r in lstPost2)
                                {
                                    DAL.ACC_JournalDetail dprod = new DAL.ACC_JournalDetail();
                                    dprod.JournalId = j.JournalId;
                                    dprod.AccountId = r.InvAcc;                             //حساب مخزون المنتج النهائي
                                    dprod.Debit = r.Cost;
                                    dprod.Credit = 0;
                                    dprod.Notes = j.JNotes;
                                    dprod.CrncId = 0;
                                    dprod.CrncRate = 1;
                                    dprod.CostCenter = null;
                                    DB.ACC_JournalDetails.InsertOnSubmit(dprod);
                                }

                                DAL.ACC_JournalDetail dprodexpense = new DAL.ACC_JournalDetail();
                                dprodexpense.JournalId = j.JournalId;
                                dprodexpense.AccountId = Shared.st_Store.ManufacturingExpAcc.Value;      //حساب مصروفات التشغيل او حساب مراقبة الانتاج
                                dprodexpense.Debit = 0;
                                dprodexpense.Credit = lstPost2.Sum(x => x.Cost);
                                dprodexpense.Notes = j.JNotes;
                                dprodexpense.CrncId = 0;
                                dprodexpense.CrncRate = 1;
                                dprodexpense.CostCenter = CostCenterId;
                                DB.ACC_JournalDetails.InsertOnSubmit(dprodexpense);
                                DB.SubmitChanges();
                                #endregion
                            }
                        }

                        /*جرد مستمر اقفال حساب مراقبة الانتاج*/
                        if (Shared.StockIsPeriodic == false)
                        {
                            if (!Shared.ItemsPostingAvailable)
                            {
                                var Total_ProductsCost = (from DataRow dr_prd in dt_manfProducts.Rows
                                                          where dr_prd.RowState != DataRowState.Deleted
                                                          select Convert.ToDecimal(dr_prd["ActualTotalCost"])).ToList().Sum();

                                if (Total_ProductsCost > 0)
                                {
                                    int ProductPurchaseAccountId = DB.IC_Stores.Where(x => x.StoreId == defaultStoreId).Select(x => x.PurchaseAccount).FirstOrDefault();//المخزون
                                                                                                                                                                        //Convert.ToInt32(ErpUtils.GetGridLookUpValue(lkp_ProductStoreId, lkp_ProductStoreId.EditValue, "PurchaseAccount"));

                                    DAL.ACC_JournalDetail dprod = new DAL.ACC_JournalDetail();
                                    dprod.JournalId = j.JournalId;
                                    dprod.AccountId = ProductPurchaseAccountId;                             //حساب مخزون المنتج النهائي
                                    dprod.Debit = Total_ProductsCost;
                                    dprod.Credit = 0;
                                    dprod.Notes = j.JNotes;
                                    dprod.CrncId = 0;
                                    dprod.CrncRate = 1;
                                    dprod.CostCenter = null;
                                    DB.ACC_JournalDetails.InsertOnSubmit(dprod);
                                    DB.SubmitChanges();

                                    DAL.ACC_JournalDetail dprodexpense = new DAL.ACC_JournalDetail();
                                    dprodexpense.JournalId = j.JournalId;
                                    dprodexpense.AccountId = Shared.st_Store.ManufacturingExpAcc.Value;      //حساب مصروفات التشغيل او حساب مراقبة الانتاج
                                    dprodexpense.Debit = 0;
                                    dprodexpense.Credit = Total_ProductsCost;
                                    dprodexpense.Notes = j.JNotes;
                                    dprodexpense.CrncId = 0;
                                    dprodexpense.CrncRate = 1;
                                    dprodexpense.CostCenter = CostCenterId;
                                    DB.ACC_JournalDetails.InsertOnSubmit(dprodexpense);
                                    DB.SubmitChanges();

                                }
                            }

                        }
                        #endregion
                        DB.SubmitChanges();
                    }
                    #endregion

                    #region Add_Products_To_ItemStore
                    foreach (DataRow dr_prd in dt_manfProducts.Rows)
                    {
                        if (dr_prd.RowState == DataRowState.Deleted)
                            continue;

                        decimal Total_Qty = Convert.ToDecimal(dr_prd["Height"]) *
                                            Convert.ToDecimal(dr_prd["Length"]) *
                                            Convert.ToDecimal(dr_prd["Width"]) *
                                            Convert.ToDecimal(dr_prd["ActualQty"]) * Convert.ToInt32(dr_prd["UomFactor"]);

                        if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish)
                            Total_Qty = Convert.ToDecimal(dr_prd["ActualQty"]) * Convert.ToInt32(dr_prd["UomFactor"]);

                        #region add_product_qty
                        DAL.IC_ItemStore products = new DAL.IC_ItemStore();
                        products.InsertTime = DateTime.Now;
                        products.IsInTrns = true;
                        products.ItemId = Convert.ToInt32(dr_prd["ItemId"]);
                        products.ProcessId = (int)Process.Manufacturing;             // تشغيل
                        products.PurchasePrice = Convert.ToDecimal(dr_prd["ActualTotalCost"]);
                        products.Qty = Total_Qty;
                        products.SourceId = Manf_ID;
                        products.StoreId = defaultStoreId;
                        products.VendorId = null;
                        products.PiecesCount = Convert.ToDecimal(dr_prd["PiecesCount"]);
                        if (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Distinguish)
                        {
                            products.Length = Convert.ToDecimal(dr_prd["Length"]);
                            products.Width = Convert.ToDecimal(dr_prd["Width"]);
                            products.Height = Convert.ToDecimal(dr_prd["Height"]);
                        }

                        if (Convert.ToBoolean(dr_prd["IsExpire"]) == false || dr_prd["Expire"] == DBNull.Value || dr_prd["Expire"] == null)
                            products.Expire = null;
                        else
                        {
                            DateTime temp = Convert.ToDateTime(dr_prd["Expire"]);
                            temp = temp.AddDays(-temp.Day + 1);
                            products.Expire = temp;
                        }
                        string batch;
                        if (dr_prd["Batch"] == DBNull.Value || dr_prd["Batch"] == null || dr_prd["Batch"].ToString().Trim() == string.Empty)
                            batch = null;
                        else
                            batch = dr_prd["Batch"].ToString();

                        if (Shared.st_Store.Batch && Shared.st_Store.PurchaseAutoSerialBatch && Shared.st_Store.SerialForAssembly == false)
                        {
                            batch = MyHelper.GetNextItemBatch(DB, Convert.ToInt32(dr_prd["ItemId"]));
                            dr_prd["Batch"] = batch;
                            DB.ManfProducts.Where(x => x.ManfProductsId == Convert.ToInt32(dr_prd["ManfProductsId"])).FirstOrDefault().Batch = batch;
                        }

                        products.Batch = batch;
                        DB.IC_ItemStores.InsertOnSubmit(products);
                        DB.SubmitChanges();
                        #endregion
                    }
                    DB.SubmitChanges();


                    #endregion




                    DB.Manufacturings.Where(x => x.Manf_Id == Manf_ID).FirstOrDefault().IsFinished = true;
                    DB.SubmitChanges();
                    DB.Transaction.Commit();


                    dt_manfProducts.AcceptChanges();

                }
                catch
                {
                    MessageBox.Show(Shared.IsEnglish ? ResEn.MsgIncorrectData : ResAr.MsgIncorrectData
                        , Shared.IsEnglish ? ResEn.MsgTError : ResAr.MsgTError, MessageBoxButtons.OK, MessageBoxIcon.Error);
                    DB.Transaction.Rollback();
                }
                finally
                {
                    DB.Connection.Close();
                }



            }

        }
        public struct supposed_raw_Obj
        {
            public string Raw_Item_ID;
            public double Qty;
            public int UomId;
            public int ItemId;
            public decimal Factor;
            public decimal PurchasePrice;
            public double Total_Price;
            public bool IsExpire;
            public decimal CurrentQty;
            public int ItemType;
            public int CategoryId;
        }

        /// <summary>
        /// Invoices Status in DataBase
        /// </summary>
        public enum InvoiceStatus
        {
            Invalid = 0,
            Valid = 1,
            Rejected = 2
        }
    }
}
