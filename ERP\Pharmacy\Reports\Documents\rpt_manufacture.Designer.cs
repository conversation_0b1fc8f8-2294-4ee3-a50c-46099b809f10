namespace Reports
{
    partial class rpt_manufacture
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.xrTable2 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow2 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_S_Total = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_S_Qty = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_S_UOM = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_S_ItemName = new DevExpress.XtraReports.UI.XRTableCell();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.lbl_User = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel10 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel14 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Notes = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_SupposedCost = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel4 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_ActualCost = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel12 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Startdate = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_EndDate = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel7 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel8 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_ProductStore = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Manf_Number = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel2 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel1 = new DevExpress.XtraReports.UI.XRLabel();
            this.lblReportName = new DevExpress.XtraReports.UI.XRLabel();
            this.picLogo = new DevExpress.XtraReports.UI.XRPictureBox();
            this.lblCompName = new DevExpress.XtraReports.UI.XRLabel();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.xrTable15 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow15 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_A_PiecesCount = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Prd_PiecesCount = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrPageInfo1 = new DevExpress.XtraReports.UI.XRPageInfo();
            this.xrTable1 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow1 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell6 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell5 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrLabel21 = new DevExpress.XtraReports.UI.XRLabel();
            this.DetailReport_SupposedRaws = new DevExpress.XtraReports.UI.DetailReportBand();
            this.Detail1 = new DevExpress.XtraReports.UI.DetailBand();
            this.ReportHeader = new DevExpress.XtraReports.UI.ReportHeaderBand();
            this.DetailReport_ActualRaws = new DevExpress.XtraReports.UI.DetailReportBand();
            this.Detail2 = new DevExpress.XtraReports.UI.DetailBand();
            this.xrTable4 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow4 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_A_Total = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_A_TotalQty = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_A_Qty = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_A_Height = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_A_Width = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_A_Length = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_A_UOM = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_A_Code1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_A_ItemName = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_A_StoreName = new DevExpress.XtraReports.UI.XRTableCell();
            this.SubBand1 = new DevExpress.XtraReports.UI.SubBand();
            this.xrLabel3 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_itms_totalQty = new DevExpress.XtraReports.UI.XRLabel();
            this.ReportHeader1 = new DevExpress.XtraReports.UI.ReportHeaderBand();
            this.xrLabel5 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrTable3 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow3 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell32 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell4 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell24 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell31 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell30 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell7 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell39 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell8 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell13 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTable16 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow16 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_A_TotalCost = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_A_Code2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.DetailReport_SupposedExpenses = new DevExpress.XtraReports.UI.DetailReportBand();
            this.Detail3 = new DevExpress.XtraReports.UI.DetailBand();
            this.xrTable6 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow6 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_S_ExpenseValue = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_S_ExpenseName = new DevExpress.XtraReports.UI.XRTableCell();
            this.ReportHeader2 = new DevExpress.XtraReports.UI.ReportHeaderBand();
            this.xrLabel15 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrTable5 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow5 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell17 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell18 = new DevExpress.XtraReports.UI.XRTableCell();
            this.DetailReport_ActualExpenes = new DevExpress.XtraReports.UI.DetailReportBand();
            this.Detail4 = new DevExpress.XtraReports.UI.DetailBand();
            this.xrTable8 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow8 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_A_ExpenseValue = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_A_ExpenseName = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_A_ExpenseDrawer = new DevExpress.XtraReports.UI.XRTableCell();
            this.ReportHeader3 = new DevExpress.XtraReports.UI.ReportHeaderBand();
            this.xrTable7 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow7 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell15 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell16 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell19 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrLabel16 = new DevExpress.XtraReports.UI.XRLabel();
            this.DetailReport_wages = new DevExpress.XtraReports.UI.DetailReportBand();
            this.Detail5 = new DevExpress.XtraReports.UI.DetailBand();
            this.xrTable10 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow10 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_EmpTotal = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_EmpNotes = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_EmpWorkHours = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_EmpDate = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_EmpItemUOM = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_EmpItemName = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_EmpName = new DevExpress.XtraReports.UI.XRTableCell();
            this.ReportHeader4 = new DevExpress.XtraReports.UI.ReportHeaderBand();
            this.xrLabel17 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrTable9 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow9 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell29 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell28 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell25 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell26 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell34 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell33 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell27 = new DevExpress.XtraReports.UI.XRTableCell();
            this.DetailReport_Damage = new DevExpress.XtraReports.UI.DetailReportBand();
            this.Detail6 = new DevExpress.XtraReports.UI.DetailBand();
            this.xrTable12 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow12 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_DamageQty = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_DamageUOM = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_DamageItemName = new DevExpress.XtraReports.UI.XRTableCell();
            this.ReportHeader5 = new DevExpress.XtraReports.UI.ReportHeaderBand();
            this.xrLabel19 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrTable11 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow11 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell36 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell37 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell38 = new DevExpress.XtraReports.UI.XRTableCell();
            this.DetailReport_Products = new DevExpress.XtraReports.UI.DetailReportBand();
            this.Detail7 = new DevExpress.XtraReports.UI.DetailBand();
            this.xrTable14 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow14 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_Prd_ActualCost = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Prd_SupposedCost = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Prd_TotalQty = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Prd_Qty = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Prd_Height = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Prd_Width = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Prd_Length = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Prd_UOM = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Prd_ItemName = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Prd_BOM = new DevExpress.XtraReports.UI.XRTableCell();
            this.ReportHeader6 = new DevExpress.XtraReports.UI.ReportHeaderBand();
            this.xrLabel13 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrTable13 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow13 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell23 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell9 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell22 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell10 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell11 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell20 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell14 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell21 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell12 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell35 = new DevExpress.XtraReports.UI.XRTableCell();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable15)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable16)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable10)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable12)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable11)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable14)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable13)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // Detail
            // 
            this.Detail.Expanded = false;
            this.Detail.HeightF = 2F;
            this.Detail.Name = "Detail";
            this.Detail.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Detail.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrTable2
            // 
            this.xrTable2.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable2.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable2.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.xrTable2.Name = "xrTable2";
            this.xrTable2.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow2});
            this.xrTable2.SizeF = new System.Drawing.SizeF(786F, 29.16667F);
            this.xrTable2.StylePriority.UseBorders = false;
            this.xrTable2.StylePriority.UseFont = false;
            this.xrTable2.StylePriority.UseTextAlignment = false;
            this.xrTable2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow2
            // 
            this.xrTableRow2.BackColor = System.Drawing.Color.Empty;
            this.xrTableRow2.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_S_Total,
            this.cell_S_Qty,
            this.cell_S_UOM,
            this.cell_S_ItemName});
            this.xrTableRow2.Font = new System.Drawing.Font("Times New Roman", 10F, System.Drawing.FontStyle.Bold);
            this.xrTableRow2.Name = "xrTableRow2";
            this.xrTableRow2.StylePriority.UseBackColor = false;
            this.xrTableRow2.StylePriority.UseFont = false;
            this.xrTableRow2.Weight = 0.54901959587545957D;
            // 
            // cell_S_Total
            // 
            this.cell_S_Total.Name = "cell_S_Total";
            this.cell_S_Total.Text = "الاجمالي";
            this.cell_S_Total.Weight = 0.38382618299877369D;
            // 
            // cell_S_Qty
            // 
            this.cell_S_Qty.Name = "cell_S_Qty";
            this.cell_S_Qty.Text = "الكمية";
            this.cell_S_Qty.Weight = 0.46409833340244439D;
            // 
            // cell_S_UOM
            // 
            this.cell_S_UOM.Name = "cell_S_UOM";
            this.cell_S_UOM.Text = "وحدة القياس";
            this.cell_S_UOM.Weight = 0.43129776088336036D;
            // 
            // cell_S_ItemName
            // 
            this.cell_S_ItemName.Name = "cell_S_ItemName";
            this.cell_S_ItemName.Text = "اســـم الصنف";
            this.cell_S_ItemName.Weight = 0.97077772271542151D;
            // 
            // TopMargin
            // 
            this.TopMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.lbl_User,
            this.xrLabel10,
            this.xrLabel14,
            this.lbl_Notes,
            this.lbl_SupposedCost,
            this.xrLabel4,
            this.lbl_ActualCost,
            this.xrLabel12,
            this.lbl_Startdate,
            this.lbl_EndDate,
            this.xrLabel7,
            this.xrLabel8,
            this.lbl_ProductStore,
            this.lbl_Manf_Number,
            this.xrLabel2,
            this.xrLabel1,
            this.lblReportName,
            this.picLogo,
            this.lblCompName});
            this.TopMargin.HeightF = 228F;
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.TopMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // lbl_User
            // 
            this.lbl_User.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_User.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.lbl_User.LocationFloat = new DevExpress.Utils.PointFloat(14.58333F, 83.00002F);
            this.lbl_User.Name = "lbl_User";
            this.lbl_User.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_User.SizeF = new System.Drawing.SizeF(184.375F, 23F);
            this.lbl_User.StylePriority.UseBorders = false;
            this.lbl_User.StylePriority.UseFont = false;
            this.lbl_User.StylePriority.UseTextAlignment = false;
            this.lbl_User.Text = "..";
            this.lbl_User.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel10
            // 
            this.xrLabel10.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel10.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel10.LocationFloat = new DevExpress.Utils.PointFloat(214.5833F, 83.00002F);
            this.xrLabel10.Name = "xrLabel10";
            this.xrLabel10.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel10.SizeF = new System.Drawing.SizeF(88.54166F, 23F);
            this.xrLabel10.StylePriority.UseBorders = false;
            this.xrLabel10.StylePriority.UseFont = false;
            this.xrLabel10.StylePriority.UseTextAlignment = false;
            this.xrLabel10.Text = "اسم المستخدم";
            this.xrLabel10.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel14
            // 
            this.xrLabel14.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.xrLabel14.LocationFloat = new DevExpress.Utils.PointFloat(680.5835F, 170.25F);
            this.xrLabel14.Name = "xrLabel14";
            this.xrLabel14.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel14.SizeF = new System.Drawing.SizeF(104.4166F, 24.49998F);
            this.xrLabel14.StylePriority.UseFont = false;
            this.xrLabel14.StylePriority.UseTextAlignment = false;
            this.xrLabel14.Text = "ملاحظات";
            this.xrLabel14.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // lbl_Notes
            // 
            this.lbl_Notes.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_Notes.LocationFloat = new DevExpress.Utils.PointFloat(271.8745F, 155.5417F);
            this.lbl_Notes.Name = "lbl_Notes";
            this.lbl_Notes.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Notes.SizeF = new System.Drawing.SizeF(402.4168F, 54.95831F);
            this.lbl_Notes.StylePriority.UseFont = false;
            this.lbl_Notes.StylePriority.UseTextAlignment = false;
            this.lbl_Notes.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // lbl_SupposedCost
            // 
            this.lbl_SupposedCost.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_SupposedCost.LocationFloat = new DevExpress.Utils.PointFloat(1.999998F, 155.5417F);
            this.lbl_SupposedCost.Multiline = true;
            this.lbl_SupposedCost.Name = "lbl_SupposedCost";
            this.lbl_SupposedCost.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_SupposedCost.SizeF = new System.Drawing.SizeF(142.9165F, 24.49998F);
            this.lbl_SupposedCost.StylePriority.UseFont = false;
            this.lbl_SupposedCost.StylePriority.UseTextAlignment = false;
            this.lbl_SupposedCost.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight;
            // 
            // xrLabel4
            // 
            this.xrLabel4.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.xrLabel4.LocationFloat = new DevExpress.Utils.PointFloat(152.9164F, 155.5417F);
            this.xrLabel4.Name = "xrLabel4";
            this.xrLabel4.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel4.SizeF = new System.Drawing.SizeF(113.7086F, 24.49998F);
            this.xrLabel4.StylePriority.UseFont = false;
            this.xrLabel4.StylePriority.UseTextAlignment = false;
            this.xrLabel4.Text = "التكلفة الافتراضية";
            this.xrLabel4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // lbl_ActualCost
            // 
            this.lbl_ActualCost.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_ActualCost.LocationFloat = new DevExpress.Utils.PointFloat(1.589457E-05F, 186F);
            this.lbl_ActualCost.Name = "lbl_ActualCost";
            this.lbl_ActualCost.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_ActualCost.SizeF = new System.Drawing.SizeF(144.9165F, 24.49998F);
            this.lbl_ActualCost.StylePriority.UseFont = false;
            this.lbl_ActualCost.StylePriority.UseTextAlignment = false;
            this.lbl_ActualCost.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel12
            // 
            this.xrLabel12.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.xrLabel12.LocationFloat = new DevExpress.Utils.PointFloat(151.3325F, 186F);
            this.xrLabel12.Name = "xrLabel12";
            this.xrLabel12.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel12.SizeF = new System.Drawing.SizeF(100.7086F, 24.49998F);
            this.xrLabel12.StylePriority.UseFont = false;
            this.xrLabel12.StylePriority.UseTextAlignment = false;
            this.xrLabel12.Text = "التكلفة الفعلية";
            this.xrLabel12.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // lbl_Startdate
            // 
            this.lbl_Startdate.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_Startdate.LocationFloat = new DevExpress.Utils.PointFloat(545.2499F, 120.5417F);
            this.lbl_Startdate.Name = "lbl_Startdate";
            this.lbl_Startdate.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Startdate.SizeF = new System.Drawing.SizeF(129.0414F, 24.49998F);
            this.lbl_Startdate.StylePriority.UseFont = false;
            this.lbl_Startdate.StylePriority.UseTextAlignment = false;
            this.lbl_Startdate.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_EndDate
            // 
            this.lbl_EndDate.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_EndDate.LocationFloat = new DevExpress.Utils.PointFloat(286.1247F, 120.5417F);
            this.lbl_EndDate.Name = "lbl_EndDate";
            this.lbl_EndDate.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_EndDate.SizeF = new System.Drawing.SizeF(122.2499F, 24.49998F);
            this.lbl_EndDate.StylePriority.UseFont = false;
            this.lbl_EndDate.StylePriority.UseTextAlignment = false;
            this.lbl_EndDate.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel7
            // 
            this.xrLabel7.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.xrLabel7.LocationFloat = new DevExpress.Utils.PointFloat(680.5835F, 120.5417F);
            this.xrLabel7.Name = "xrLabel7";
            this.xrLabel7.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel7.SizeF = new System.Drawing.SizeF(105.4165F, 24.49998F);
            this.xrLabel7.StylePriority.UseFont = false;
            this.xrLabel7.StylePriority.UseTextAlignment = false;
            this.xrLabel7.Text = "بدء التشغيل";
            this.xrLabel7.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel8
            // 
            this.xrLabel8.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.xrLabel8.LocationFloat = new DevExpress.Utils.PointFloat(415.3746F, 120.5418F);
            this.xrLabel8.Name = "xrLabel8";
            this.xrLabel8.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel8.SizeF = new System.Drawing.SizeF(100.7086F, 24.49998F);
            this.xrLabel8.StylePriority.UseFont = false;
            this.xrLabel8.StylePriority.UseTextAlignment = false;
            this.xrLabel8.Text = "انتهاء التشغيل";
            this.xrLabel8.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // lbl_ProductStore
            // 
            this.lbl_ProductStore.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_ProductStore.LocationFloat = new DevExpress.Utils.PointFloat(2.000024F, 120.5417F);
            this.lbl_ProductStore.Name = "lbl_ProductStore";
            this.lbl_ProductStore.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_ProductStore.SizeF = new System.Drawing.SizeF(142.9165F, 24.49998F);
            this.lbl_ProductStore.StylePriority.UseFont = false;
            this.lbl_ProductStore.StylePriority.UseTextAlignment = false;
            this.lbl_ProductStore.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_Manf_Number
            // 
            this.lbl_Manf_Number.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_Manf_Number.LocationFloat = new DevExpress.Utils.PointFloat(322.9167F, 50F);
            this.lbl_Manf_Number.Name = "lbl_Manf_Number";
            this.lbl_Manf_Number.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Manf_Number.SizeF = new System.Drawing.SizeF(142.9165F, 24.49998F);
            this.lbl_Manf_Number.StylePriority.UseFont = false;
            this.lbl_Manf_Number.StylePriority.UseTextAlignment = false;
            this.lbl_Manf_Number.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel2
            // 
            this.xrLabel2.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.xrLabel2.LocationFloat = new DevExpress.Utils.PointFloat(152.9164F, 120.5418F);
            this.xrLabel2.Name = "xrLabel2";
            this.xrLabel2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel2.SizeF = new System.Drawing.SizeF(116.2499F, 24.49999F);
            this.xrLabel2.StylePriority.UseFont = false;
            this.xrLabel2.StylePriority.UseTextAlignment = false;
            this.xrLabel2.Text = "مخزن التشغيل";
            this.xrLabel2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            // 
            // xrLabel1
            // 
            this.xrLabel1.Font = new System.Drawing.Font("Times New Roman", 14F, System.Drawing.FontStyle.Bold);
            this.xrLabel1.LocationFloat = new DevExpress.Utils.PointFloat(465.8331F, 50F);
            this.xrLabel1.Name = "xrLabel1";
            this.xrLabel1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel1.SizeF = new System.Drawing.SizeF(29.83331F, 24.49998F);
            this.xrLabel1.StylePriority.UseFont = false;
            this.xrLabel1.StylePriority.UseTextAlignment = false;
            this.xrLabel1.Text = "رقم";
            this.xrLabel1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // lblReportName
            // 
            this.lblReportName.Font = new System.Drawing.Font("Times New Roman", 14F, System.Drawing.FontStyle.Bold);
            this.lblReportName.LocationFloat = new DevExpress.Utils.PointFloat(495.6664F, 50F);
            this.lblReportName.Name = "lblReportName";
            this.lblReportName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblReportName.SizeF = new System.Drawing.SizeF(58.79193F, 24.49998F);
            this.lblReportName.StylePriority.UseFont = false;
            this.lblReportName.StylePriority.UseTextAlignment = false;
            this.lblReportName.Text = "تشغيلة";
            this.lblReportName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // picLogo
            // 
            this.picLogo.LocationFloat = new DevExpress.Utils.PointFloat(14.58333F, 10.00001F);
            this.picLogo.Name = "picLogo";
            this.picLogo.SizeF = new System.Drawing.SizeF(70F, 70F);
            this.picLogo.Sizing = DevExpress.XtraPrinting.ImageSizeMode.StretchImage;
            // 
            // lblCompName
            // 
            this.lblCompName.Font = new System.Drawing.Font("Times New Roman", 18F);
            this.lblCompName.LocationFloat = new DevExpress.Utils.PointFloat(89.58334F, 10.00001F);
            this.lblCompName.Name = "lblCompName";
            this.lblCompName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblCompName.SizeF = new System.Drawing.SizeF(600.0001F, 30F);
            this.lblCompName.StylePriority.UseFont = false;
            this.lblCompName.StylePriority.UseTextAlignment = false;
            this.lblCompName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopCenter;
            // 
            // BottomMargin
            // 
            this.BottomMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable15,
            this.xrPageInfo1});
            this.BottomMargin.HeightF = 47.91666F;
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.BottomMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrTable15
            // 
            this.xrTable15.LocationFloat = new DevExpress.Utils.PointFloat(476F, 12.5F);
            this.xrTable15.Name = "xrTable15";
            this.xrTable15.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow15});
            this.xrTable15.SizeF = new System.Drawing.SizeF(300F, 25F);
            this.xrTable15.Visible = false;
            // 
            // xrTableRow15
            // 
            this.xrTableRow15.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_A_PiecesCount,
            this.cell_Prd_PiecesCount});
            this.xrTableRow15.Name = "xrTableRow15";
            this.xrTableRow15.Weight = 1D;
            // 
            // cell_A_PiecesCount
            // 
            this.cell_A_PiecesCount.Name = "cell_A_PiecesCount";
            this.cell_A_PiecesCount.Text = "cell_A_PiecesCount";
            this.cell_A_PiecesCount.Weight = 1D;
            // 
            // cell_Prd_PiecesCount
            // 
            this.cell_Prd_PiecesCount.Name = "cell_Prd_PiecesCount";
            this.cell_Prd_PiecesCount.Text = "cell_Prd_PiecesCount";
            this.cell_Prd_PiecesCount.Weight = 2D;
            // 
            // xrPageInfo1
            // 
            this.xrPageInfo1.Format = "Page {0} of {1} ";
            this.xrPageInfo1.LocationFloat = new DevExpress.Utils.PointFloat(337.5F, 12.5F);
            this.xrPageInfo1.Name = "xrPageInfo1";
            this.xrPageInfo1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrPageInfo1.SizeF = new System.Drawing.SizeF(109.375F, 23F);
            this.xrPageInfo1.StylePriority.UseTextAlignment = false;
            this.xrPageInfo1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTable1
            // 
            this.xrTable1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable1.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable1.LocationFloat = new DevExpress.Utils.PointFloat(0F, 27.87501F);
            this.xrTable1.Name = "xrTable1";
            this.xrTable1.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow1});
            this.xrTable1.SizeF = new System.Drawing.SizeF(786F, 53.125F);
            this.xrTable1.StylePriority.UseBorders = false;
            this.xrTable1.StylePriority.UseFont = false;
            this.xrTable1.StylePriority.UseTextAlignment = false;
            this.xrTable1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow1
            // 
            this.xrTableRow1.BackColor = System.Drawing.Color.Moccasin;
            this.xrTableRow1.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell1,
            this.xrTableCell6,
            this.xrTableCell5,
            this.xrTableCell3});
            this.xrTableRow1.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrTableRow1.Name = "xrTableRow1";
            this.xrTableRow1.StylePriority.UseBackColor = false;
            this.xrTableRow1.StylePriority.UseFont = false;
            this.xrTableRow1.Weight = 1D;
            // 
            // xrTableCell1
            // 
            this.xrTableCell1.Name = "xrTableCell1";
            this.xrTableCell1.Text = "الاجمالي";
            this.xrTableCell1.Weight = 0.3838262266785134D;
            // 
            // xrTableCell6
            // 
            this.xrTableCell6.Name = "xrTableCell6";
            this.xrTableCell6.Text = "الكمية";
            this.xrTableCell6.Weight = 0.46409833340244433D;
            // 
            // xrTableCell5
            // 
            this.xrTableCell5.Name = "xrTableCell5";
            this.xrTableCell5.Text = "وحدة القياس";
            this.xrTableCell5.Weight = 0.43129784824283979D;
            // 
            // xrTableCell3
            // 
            this.xrTableCell3.Name = "xrTableCell3";
            this.xrTableCell3.Text = "اســـم الصنف";
            this.xrTableCell3.Weight = 0.97603736787117457D;
            // 
            // xrLabel21
            // 
            this.xrLabel21.Font = new System.Drawing.Font("Times New Roman", 14F, System.Drawing.FontStyle.Bold);
            this.xrLabel21.ForeColor = System.Drawing.Color.Navy;
            this.xrLabel21.LocationFloat = new DevExpress.Utils.PointFloat(575F, 1F);
            this.xrLabel21.Name = "xrLabel21";
            this.xrLabel21.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel21.SizeF = new System.Drawing.SizeF(202.8332F, 24.49999F);
            this.xrLabel21.StylePriority.UseFont = false;
            this.xrLabel21.StylePriority.UseForeColor = false;
            this.xrLabel21.StylePriority.UseTextAlignment = false;
            this.xrLabel21.Text = "الاستهلاكات الافتراضية";
            this.xrLabel21.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // DetailReport_SupposedRaws
            // 
            this.DetailReport_SupposedRaws.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail1,
            this.ReportHeader});
            this.DetailReport_SupposedRaws.Expanded = false;
            this.DetailReport_SupposedRaws.Level = 1;
            this.DetailReport_SupposedRaws.Name = "DetailReport_SupposedRaws";
            this.DetailReport_SupposedRaws.PageBreak = DevExpress.XtraReports.UI.PageBreak.AfterBand;
            // 
            // Detail1
            // 
            this.Detail1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable2});
            this.Detail1.HeightF = 29.16667F;
            this.Detail1.Name = "Detail1";
            // 
            // ReportHeader
            // 
            this.ReportHeader.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLabel21,
            this.xrTable1});
            this.ReportHeader.HeightF = 81.00001F;
            this.ReportHeader.Name = "ReportHeader";
            // 
            // DetailReport_ActualRaws
            // 
            this.DetailReport_ActualRaws.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail2,
            this.ReportHeader1});
            this.DetailReport_ActualRaws.Expanded = false;
            this.DetailReport_ActualRaws.Level = 2;
            this.DetailReport_ActualRaws.Name = "DetailReport_ActualRaws";
            this.DetailReport_ActualRaws.PageBreak = DevExpress.XtraReports.UI.PageBreak.AfterBand;
            // 
            // Detail2
            // 
            this.Detail2.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable4});
            this.Detail2.HeightF = 29.16667F;
            this.Detail2.Name = "Detail2";
            this.Detail2.SubBands.AddRange(new DevExpress.XtraReports.UI.SubBand[] {
            this.SubBand1});
            // 
            // xrTable4
            // 
            this.xrTable4.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable4.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable4.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.xrTable4.Name = "xrTable4";
            this.xrTable4.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow4});
            this.xrTable4.SizeF = new System.Drawing.SizeF(786F, 29.16667F);
            this.xrTable4.StylePriority.UseBorders = false;
            this.xrTable4.StylePriority.UseFont = false;
            this.xrTable4.StylePriority.UseTextAlignment = false;
            this.xrTable4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow4
            // 
            this.xrTableRow4.BackColor = System.Drawing.Color.Empty;
            this.xrTableRow4.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_A_Total,
            this.cell_A_TotalQty,
            this.cell_A_Qty,
            this.cell_A_Height,
            this.cell_A_Width,
            this.cell_A_Length,
            this.cell_A_UOM,
            this.cell_A_Code1,
            this.cell_A_ItemName,
            this.cell_A_StoreName});
            this.xrTableRow4.Font = new System.Drawing.Font("Times New Roman", 10F, System.Drawing.FontStyle.Bold);
            this.xrTableRow4.Name = "xrTableRow4";
            this.xrTableRow4.StylePriority.UseBackColor = false;
            this.xrTableRow4.StylePriority.UseFont = false;
            this.xrTableRow4.Weight = 0.54901959587545957D;
            // 
            // cell_A_Total
            // 
            this.cell_A_Total.Name = "cell_A_Total";
            this.cell_A_Total.Text = "الاجمالي";
            this.cell_A_Total.Weight = 0.37041466351380481D;
            // 
            // cell_A_TotalQty
            // 
            this.cell_A_TotalQty.Name = "cell_A_TotalQty";
            this.cell_A_TotalQty.Text = "اجمالي كمية";
            this.cell_A_TotalQty.Weight = 0.29383402865654945D;
            // 
            // cell_A_Qty
            // 
            this.cell_A_Qty.Name = "cell_A_Qty";
            this.cell_A_Qty.Text = "الكمية";
            this.cell_A_Qty.Weight = 0.26077529916933173D;
            // 
            // cell_A_Height
            // 
            this.cell_A_Height.Name = "cell_A_Height";
            this.cell_A_Height.Text = "ارتفاع";
            this.cell_A_Height.Weight = 0.19913891253580573D;
            // 
            // cell_A_Width
            // 
            this.cell_A_Width.Name = "cell_A_Width";
            this.cell_A_Width.Text = "عرض";
            this.cell_A_Width.Weight = 0.26721826400465643D;
            // 
            // cell_A_Length
            // 
            this.cell_A_Length.Name = "cell_A_Length";
            this.cell_A_Length.Text = "طول";
            this.cell_A_Length.Weight = 0.23901468500229545D;
            // 
            // cell_A_UOM
            // 
            this.cell_A_UOM.Name = "cell_A_UOM";
            this.cell_A_UOM.Text = "وحدة القياس";
            this.cell_A_UOM.Weight = 0.25702860883174034D;
            // 
            // cell_A_Code1
            // 
            this.cell_A_Code1.Name = "cell_A_Code1";
            this.cell_A_Code1.Text = "كود الصنف";
            this.cell_A_Code1.Weight = 0.25702875443087286D;
            // 
            // cell_A_ItemName
            // 
            this.cell_A_ItemName.Name = "cell_A_ItemName";
            this.cell_A_ItemName.Text = "اســـم الصنف";
            this.cell_A_ItemName.Weight = 0.66217016385105132D;
            // 
            // cell_A_StoreName
            // 
            this.cell_A_StoreName.Name = "cell_A_StoreName";
            this.cell_A_StoreName.Text = "المخزن";
            this.cell_A_StoreName.Weight = 0.44337662000389194D;
            // 
            // SubBand1
            // 
            this.SubBand1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLabel3,
            this.lbl_itms_totalQty});
            this.SubBand1.HeightF = 24.49995F;
            this.SubBand1.Name = "SubBand1";
            // 
            // xrLabel3
            // 
            this.xrLabel3.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel3.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.xrLabel3.LocationFloat = new DevExpress.Utils.PointFloat(160.646F, 0F);
            this.xrLabel3.Name = "xrLabel3";
            this.xrLabel3.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel3.SizeF = new System.Drawing.SizeF(111.2285F, 24.49995F);
            this.xrLabel3.StylePriority.UseBorders = false;
            this.xrLabel3.StylePriority.UseFont = false;
            this.xrLabel3.StylePriority.UseTextAlignment = false;
            this.xrLabel3.Text = "إجمالي الكمية";
            this.xrLabel3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // lbl_itms_totalQty
            // 
            this.lbl_itms_totalQty.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_itms_totalQty.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_itms_totalQty.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.lbl_itms_totalQty.Name = "lbl_itms_totalQty";
            this.lbl_itms_totalQty.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_itms_totalQty.SizeF = new System.Drawing.SizeF(160.646F, 24.49995F);
            this.lbl_itms_totalQty.StylePriority.UseBorders = false;
            this.lbl_itms_totalQty.StylePriority.UseFont = false;
            this.lbl_itms_totalQty.StylePriority.UseTextAlignment = false;
            this.lbl_itms_totalQty.Text = " ";
            this.lbl_itms_totalQty.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // ReportHeader1
            // 
            this.ReportHeader1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLabel5,
            this.xrTable3,
            this.xrTable16});
            this.ReportHeader1.HeightF = 78F;
            this.ReportHeader1.Name = "ReportHeader1";
            // 
            // xrLabel5
            // 
            this.xrLabel5.Font = new System.Drawing.Font("Times New Roman", 14F, System.Drawing.FontStyle.Bold);
            this.xrLabel5.ForeColor = System.Drawing.Color.Navy;
            this.xrLabel5.LocationFloat = new DevExpress.Utils.PointFloat(573.1669F, 0F);
            this.xrLabel5.Name = "xrLabel5";
            this.xrLabel5.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel5.SizeF = new System.Drawing.SizeF(202.8332F, 24.49999F);
            this.xrLabel5.StylePriority.UseFont = false;
            this.xrLabel5.StylePriority.UseForeColor = false;
            this.xrLabel5.StylePriority.UseTextAlignment = false;
            this.xrLabel5.Text = "الاستهلاكات الفعلية";
            this.xrLabel5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTable3
            // 
            this.xrTable3.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable3.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable3.LocationFloat = new DevExpress.Utils.PointFloat(0F, 24.50002F);
            this.xrTable3.Name = "xrTable3";
            this.xrTable3.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow3});
            this.xrTable3.SizeF = new System.Drawing.SizeF(786F, 53.12499F);
            this.xrTable3.StylePriority.UseBorders = false;
            this.xrTable3.StylePriority.UseFont = false;
            this.xrTable3.StylePriority.UseTextAlignment = false;
            this.xrTable3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow3
            // 
            this.xrTableRow3.BackColor = System.Drawing.Color.Moccasin;
            this.xrTableRow3.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell2,
            this.xrTableCell32,
            this.xrTableCell4,
            this.xrTableCell24,
            this.xrTableCell31,
            this.xrTableCell30,
            this.xrTableCell7,
            this.xrTableCell39,
            this.xrTableCell8,
            this.xrTableCell13});
            this.xrTableRow3.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrTableRow3.Name = "xrTableRow3";
            this.xrTableRow3.StylePriority.UseBackColor = false;
            this.xrTableRow3.StylePriority.UseFont = false;
            this.xrTableRow3.Weight = 1D;
            // 
            // xrTableCell2
            // 
            this.xrTableCell2.Name = "xrTableCell2";
            this.xrTableCell2.Text = "الاجمالي";
            this.xrTableCell2.Weight = 0.28476859231031582D;
            // 
            // xrTableCell32
            // 
            this.xrTableCell32.Name = "xrTableCell32";
            this.xrTableCell32.Text = "اجمالي كمية";
            this.xrTableCell32.Weight = 0.21458785042507955D;
            // 
            // xrTableCell4
            // 
            this.xrTableCell4.Name = "xrTableCell4";
            this.xrTableCell4.Text = "الكمية";
            this.xrTableCell4.Weight = 0.19604071224008807D;
            // 
            // xrTableCell24
            // 
            this.xrTableCell24.Name = "xrTableCell24";
            this.xrTableCell24.Text = "ارتفاع";
            this.xrTableCell24.Weight = 0.1505499059007368D;
            // 
            // xrTableCell31
            // 
            this.xrTableCell31.Name = "xrTableCell31";
            this.xrTableCell31.Text = "عرض";
            this.xrTableCell31.Weight = 0.20367137075380512D;
            // 
            // xrTableCell30
            // 
            this.xrTableCell30.Name = "xrTableCell30";
            this.xrTableCell30.Text = "طول";
            this.xrTableCell30.Weight = 0.17605004601806171D;
            // 
            // xrTableCell7
            // 
            this.xrTableCell7.Name = "xrTableCell7";
            this.xrTableCell7.Text = "وحدة القياس";
            this.xrTableCell7.Weight = 0.19322404788650621D;
            // 
            // xrTableCell39
            // 
            this.xrTableCell39.Name = "xrTableCell39";
            this.xrTableCell39.Text = "كود الصنف";
            this.xrTableCell39.Weight = 0.19322404788650621D;
            // 
            // xrTableCell8
            // 
            this.xrTableCell8.Name = "xrTableCell8";
            this.xrTableCell8.Text = "اســـم الصنف";
            this.xrTableCell8.Weight = 0.49779406030669476D;
            // 
            // xrTableCell13
            // 
            this.xrTableCell13.Name = "xrTableCell13";
            this.xrTableCell13.Text = "المخزن";
            this.xrTableCell13.Weight = 0.33331341415871191D;
            // 
            // xrTable16
            // 
            this.xrTable16.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.xrTable16.Name = "xrTable16";
            this.xrTable16.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow16});
            this.xrTable16.SizeF = new System.Drawing.SizeF(600F, 25F);
            this.xrTable16.Visible = false;
            // 
            // xrTableRow16
            // 
            this.xrTableRow16.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_A_TotalCost,
            this.cell_A_Code2});
            this.xrTableRow16.Name = "xrTableRow16";
            this.xrTableRow16.Weight = 1D;
            // 
            // cell_A_TotalCost
            // 
            this.cell_A_TotalCost.Name = "cell_A_TotalCost";
            this.cell_A_TotalCost.Text = "cell_A_TotalCost";
            this.cell_A_TotalCost.Visible = false;
            this.cell_A_TotalCost.Weight = 2D;
            // 
            // cell_A_Code2
            // 
            this.cell_A_Code2.Name = "cell_A_Code2";
            this.cell_A_Code2.Text = "code2";
            this.cell_A_Code2.Visible = false;
            this.cell_A_Code2.Weight = 2D;
            // 
            // DetailReport_SupposedExpenses
            // 
            this.DetailReport_SupposedExpenses.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail3,
            this.ReportHeader2});
            this.DetailReport_SupposedExpenses.Expanded = false;
            this.DetailReport_SupposedExpenses.Level = 3;
            this.DetailReport_SupposedExpenses.Name = "DetailReport_SupposedExpenses";
            this.DetailReport_SupposedExpenses.PageBreak = DevExpress.XtraReports.UI.PageBreak.AfterBand;
            // 
            // Detail3
            // 
            this.Detail3.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable6});
            this.Detail3.HeightF = 29.16667F;
            this.Detail3.Name = "Detail3";
            // 
            // xrTable6
            // 
            this.xrTable6.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable6.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable6.LocationFloat = new DevExpress.Utils.PointFloat(2.384186E-05F, 0F);
            this.xrTable6.Name = "xrTable6";
            this.xrTable6.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow6});
            this.xrTable6.SizeF = new System.Drawing.SizeF(786F, 29.16667F);
            this.xrTable6.StylePriority.UseBorders = false;
            this.xrTable6.StylePriority.UseFont = false;
            this.xrTable6.StylePriority.UseTextAlignment = false;
            this.xrTable6.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow6
            // 
            this.xrTableRow6.BackColor = System.Drawing.Color.Empty;
            this.xrTableRow6.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_S_ExpenseValue,
            this.cell_S_ExpenseName});
            this.xrTableRow6.Font = new System.Drawing.Font("Times New Roman", 10F, System.Drawing.FontStyle.Bold);
            this.xrTableRow6.Name = "xrTableRow6";
            this.xrTableRow6.StylePriority.UseBackColor = false;
            this.xrTableRow6.StylePriority.UseFont = false;
            this.xrTableRow6.Weight = 0.54901959587545957D;
            // 
            // cell_S_ExpenseValue
            // 
            this.cell_S_ExpenseValue.Name = "cell_S_ExpenseValue";
            this.cell_S_ExpenseValue.Text = "القيمة";
            this.cell_S_ExpenseValue.Weight = 0.9243798656318023D;
            // 
            // cell_S_ExpenseName
            // 
            this.cell_S_ExpenseName.Name = "cell_S_ExpenseName";
            this.cell_S_ExpenseName.Text = "بيان المصروفات";
            this.cell_S_ExpenseName.Weight = 1.3256201343681977D;
            // 
            // ReportHeader2
            // 
            this.ReportHeader2.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLabel15,
            this.xrTable5});
            this.ReportHeader2.HeightF = 77.62498F;
            this.ReportHeader2.Name = "ReportHeader2";
            // 
            // xrLabel15
            // 
            this.xrLabel15.Font = new System.Drawing.Font("Times New Roman", 14F, System.Drawing.FontStyle.Bold);
            this.xrLabel15.ForeColor = System.Drawing.Color.Navy;
            this.xrLabel15.LocationFloat = new DevExpress.Utils.PointFloat(583.1667F, 0F);
            this.xrLabel15.Name = "xrLabel15";
            this.xrLabel15.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel15.SizeF = new System.Drawing.SizeF(202.8332F, 24.49999F);
            this.xrLabel15.StylePriority.UseFont = false;
            this.xrLabel15.StylePriority.UseForeColor = false;
            this.xrLabel15.StylePriority.UseTextAlignment = false;
            this.xrLabel15.Text = "المصاريف الافتراضية";
            this.xrLabel15.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTable5
            // 
            this.xrTable5.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable5.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable5.LocationFloat = new DevExpress.Utils.PointFloat(2.384186E-05F, 24.49999F);
            this.xrTable5.Name = "xrTable5";
            this.xrTable5.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow5});
            this.xrTable5.SizeF = new System.Drawing.SizeF(786F, 53.125F);
            this.xrTable5.StylePriority.UseBorders = false;
            this.xrTable5.StylePriority.UseFont = false;
            this.xrTable5.StylePriority.UseTextAlignment = false;
            this.xrTable5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow5
            // 
            this.xrTableRow5.BackColor = System.Drawing.Color.Moccasin;
            this.xrTableRow5.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell17,
            this.xrTableCell18});
            this.xrTableRow5.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrTableRow5.Name = "xrTableRow5";
            this.xrTableRow5.StylePriority.UseBackColor = false;
            this.xrTableRow5.StylePriority.UseFont = false;
            this.xrTableRow5.Weight = 1D;
            // 
            // xrTableCell17
            // 
            this.xrTableCell17.Name = "xrTableCell17";
            this.xrTableCell17.Text = "القيمة";
            this.xrTableCell17.Weight = 0.924379778272323D;
            // 
            // xrTableCell18
            // 
            this.xrTableCell18.Name = "xrTableCell18";
            this.xrTableCell18.Text = "بيان المصروفات";
            this.xrTableCell18.Weight = 1.3256202217276769D;
            // 
            // DetailReport_ActualExpenes
            // 
            this.DetailReport_ActualExpenes.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail4,
            this.ReportHeader3});
            this.DetailReport_ActualExpenes.Expanded = false;
            this.DetailReport_ActualExpenes.Level = 4;
            this.DetailReport_ActualExpenes.Name = "DetailReport_ActualExpenes";
            this.DetailReport_ActualExpenes.PageBreak = DevExpress.XtraReports.UI.PageBreak.AfterBand;
            // 
            // Detail4
            // 
            this.Detail4.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable8});
            this.Detail4.HeightF = 29.16667F;
            this.Detail4.Name = "Detail4";
            // 
            // xrTable8
            // 
            this.xrTable8.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable8.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable8.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.xrTable8.Name = "xrTable8";
            this.xrTable8.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow8});
            this.xrTable8.SizeF = new System.Drawing.SizeF(786F, 29.16667F);
            this.xrTable8.StylePriority.UseBorders = false;
            this.xrTable8.StylePriority.UseFont = false;
            this.xrTable8.StylePriority.UseTextAlignment = false;
            this.xrTable8.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow8
            // 
            this.xrTableRow8.BackColor = System.Drawing.Color.Empty;
            this.xrTableRow8.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_A_ExpenseValue,
            this.cell_A_ExpenseName,
            this.cell_A_ExpenseDrawer});
            this.xrTableRow8.Font = new System.Drawing.Font("Times New Roman", 10F, System.Drawing.FontStyle.Bold);
            this.xrTableRow8.Name = "xrTableRow8";
            this.xrTableRow8.StylePriority.UseBackColor = false;
            this.xrTableRow8.StylePriority.UseFont = false;
            this.xrTableRow8.Weight = 0.54901959587545957D;
            // 
            // cell_A_ExpenseValue
            // 
            this.cell_A_ExpenseValue.Name = "cell_A_ExpenseValue";
            this.cell_A_ExpenseValue.Text = "القيمة";
            this.cell_A_ExpenseValue.Weight = 0.43833504742338447D;
            // 
            // cell_A_ExpenseName
            // 
            this.cell_A_ExpenseName.Name = "cell_A_ExpenseName";
            this.cell_A_ExpenseName.Text = "بيان المصروفات";
            this.cell_A_ExpenseName.Weight = 1.3262760475391651D;
            // 
            // cell_A_ExpenseDrawer
            // 
            this.cell_A_ExpenseDrawer.Name = "cell_A_ExpenseDrawer";
            this.cell_A_ExpenseDrawer.Text = "الخزينة";
            this.cell_A_ExpenseDrawer.Weight = 0.48538890503745047D;
            // 
            // ReportHeader3
            // 
            this.ReportHeader3.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable7,
            this.xrLabel16});
            this.ReportHeader3.HeightF = 77.62499F;
            this.ReportHeader3.Name = "ReportHeader3";
            // 
            // xrTable7
            // 
            this.xrTable7.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable7.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable7.LocationFloat = new DevExpress.Utils.PointFloat(0F, 24.49999F);
            this.xrTable7.Name = "xrTable7";
            this.xrTable7.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow7});
            this.xrTable7.SizeF = new System.Drawing.SizeF(786F, 53.125F);
            this.xrTable7.StylePriority.UseBorders = false;
            this.xrTable7.StylePriority.UseFont = false;
            this.xrTable7.StylePriority.UseTextAlignment = false;
            this.xrTable7.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow7
            // 
            this.xrTableRow7.BackColor = System.Drawing.Color.Moccasin;
            this.xrTableRow7.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell15,
            this.xrTableCell16,
            this.xrTableCell19});
            this.xrTableRow7.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrTableRow7.Name = "xrTableRow7";
            this.xrTableRow7.StylePriority.UseBackColor = false;
            this.xrTableRow7.StylePriority.UseFont = false;
            this.xrTableRow7.Weight = 1D;
            // 
            // xrTableCell15
            // 
            this.xrTableCell15.Name = "xrTableCell15";
            this.xrTableCell15.Text = "القيمة";
            this.xrTableCell15.Weight = 0.43833496006390515D;
            // 
            // xrTableCell16
            // 
            this.xrTableCell16.Name = "xrTableCell16";
            this.xrTableCell16.Text = "بيان المصروفات";
            this.xrTableCell16.Weight = 1.3262760912189047D;
            // 
            // xrTableCell19
            // 
            this.xrTableCell19.Name = "xrTableCell19";
            this.xrTableCell19.Text = "الخزينة";
            this.xrTableCell19.Weight = 0.48538894871719007D;
            // 
            // xrLabel16
            // 
            this.xrLabel16.Font = new System.Drawing.Font("Times New Roman", 14F, System.Drawing.FontStyle.Bold);
            this.xrLabel16.ForeColor = System.Drawing.Color.Navy;
            this.xrLabel16.LocationFloat = new DevExpress.Utils.PointFloat(573.1668F, 0F);
            this.xrLabel16.Name = "xrLabel16";
            this.xrLabel16.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel16.SizeF = new System.Drawing.SizeF(202.8332F, 24.49999F);
            this.xrLabel16.StylePriority.UseFont = false;
            this.xrLabel16.StylePriority.UseForeColor = false;
            this.xrLabel16.StylePriority.UseTextAlignment = false;
            this.xrLabel16.Text = "المصاريف الفعلية";
            this.xrLabel16.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // DetailReport_wages
            // 
            this.DetailReport_wages.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail5,
            this.ReportHeader4});
            this.DetailReport_wages.Expanded = false;
            this.DetailReport_wages.Level = 5;
            this.DetailReport_wages.Name = "DetailReport_wages";
            this.DetailReport_wages.PageBreak = DevExpress.XtraReports.UI.PageBreak.AfterBand;
            // 
            // Detail5
            // 
            this.Detail5.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable10});
            this.Detail5.HeightF = 29.17F;
            this.Detail5.Name = "Detail5";
            // 
            // xrTable10
            // 
            this.xrTable10.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable10.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable10.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.xrTable10.Name = "xrTable10";
            this.xrTable10.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow10});
            this.xrTable10.SizeF = new System.Drawing.SizeF(786F, 29.17F);
            this.xrTable10.StylePriority.UseBorders = false;
            this.xrTable10.StylePriority.UseFont = false;
            this.xrTable10.StylePriority.UseTextAlignment = false;
            this.xrTable10.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow10
            // 
            this.xrTableRow10.BackColor = System.Drawing.Color.Empty;
            this.xrTableRow10.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_EmpTotal,
            this.cell_EmpNotes,
            this.cell_EmpWorkHours,
            this.cell_EmpDate,
            this.cell_EmpItemUOM,
            this.cell_EmpItemName,
            this.cell_EmpName});
            this.xrTableRow10.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrTableRow10.Name = "xrTableRow10";
            this.xrTableRow10.StylePriority.UseBackColor = false;
            this.xrTableRow10.StylePriority.UseFont = false;
            this.xrTableRow10.Weight = 0.5490823543772978D;
            // 
            // cell_EmpTotal
            // 
            this.cell_EmpTotal.Name = "cell_EmpTotal";
            this.cell_EmpTotal.Text = "اجمالي";
            this.cell_EmpTotal.Weight = 0.18249041251553835D;
            // 
            // cell_EmpNotes
            // 
            this.cell_EmpNotes.Name = "cell_EmpNotes";
            this.cell_EmpNotes.Text = "ملاحظات";
            this.cell_EmpNotes.Weight = 0.45264805182245837D;
            // 
            // cell_EmpWorkHours
            // 
            this.cell_EmpWorkHours.Name = "cell_EmpWorkHours";
            this.cell_EmpWorkHours.Text = "ساعات العمل";
            this.cell_EmpWorkHours.Weight = 0.17592879652067D;
            // 
            // cell_EmpDate
            // 
            this.cell_EmpDate.Name = "cell_EmpDate";
            this.cell_EmpDate.Text = "التاريخ";
            this.cell_EmpDate.Weight = 0.19465802098048546D;
            // 
            // cell_EmpItemUOM
            // 
            this.cell_EmpItemUOM.Name = "cell_EmpItemUOM";
            this.cell_EmpItemUOM.Text = "وحدة";
            this.cell_EmpItemUOM.Weight = 0.16328612720693347D;
            // 
            // cell_EmpItemName
            // 
            this.cell_EmpItemName.Name = "cell_EmpItemName";
            this.cell_EmpItemName.Text = "المنتج";
            this.cell_EmpItemName.Weight = 0.47173357282886075D;
            // 
            // cell_EmpName
            // 
            this.cell_EmpName.Name = "cell_EmpName";
            this.cell_EmpName.Text = "الموظف";
            this.cell_EmpName.Weight = 0.60925501812505345D;
            // 
            // ReportHeader4
            // 
            this.ReportHeader4.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLabel17,
            this.xrTable9});
            this.ReportHeader4.HeightF = 77.62499F;
            this.ReportHeader4.Name = "ReportHeader4";
            // 
            // xrLabel17
            // 
            this.xrLabel17.Font = new System.Drawing.Font("Times New Roman", 14F, System.Drawing.FontStyle.Bold);
            this.xrLabel17.ForeColor = System.Drawing.Color.Navy;
            this.xrLabel17.LocationFloat = new DevExpress.Utils.PointFloat(573.1668F, 0F);
            this.xrLabel17.Name = "xrLabel17";
            this.xrLabel17.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel17.SizeF = new System.Drawing.SizeF(202.8332F, 24.49999F);
            this.xrLabel17.StylePriority.UseFont = false;
            this.xrLabel17.StylePriority.UseForeColor = false;
            this.xrLabel17.StylePriority.UseTextAlignment = false;
            this.xrLabel17.Text = "الأجور المباشرة";
            this.xrLabel17.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTable9
            // 
            this.xrTable9.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable9.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable9.LocationFloat = new DevExpress.Utils.PointFloat(2.384186E-05F, 24.49999F);
            this.xrTable9.Name = "xrTable9";
            this.xrTable9.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow9});
            this.xrTable9.SizeF = new System.Drawing.SizeF(786F, 53.125F);
            this.xrTable9.StylePriority.UseBorders = false;
            this.xrTable9.StylePriority.UseFont = false;
            this.xrTable9.StylePriority.UseTextAlignment = false;
            this.xrTable9.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow9
            // 
            this.xrTableRow9.BackColor = System.Drawing.Color.Moccasin;
            this.xrTableRow9.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell29,
            this.xrTableCell28,
            this.xrTableCell25,
            this.xrTableCell26,
            this.xrTableCell34,
            this.xrTableCell33,
            this.xrTableCell27});
            this.xrTableRow9.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrTableRow9.Name = "xrTableRow9";
            this.xrTableRow9.StylePriority.UseBackColor = false;
            this.xrTableRow9.StylePriority.UseFont = false;
            this.xrTableRow9.Weight = 1D;
            // 
            // xrTableCell29
            // 
            this.xrTableCell29.Name = "xrTableCell29";
            this.xrTableCell29.Text = "اجمالي";
            this.xrTableCell29.Weight = 0.18249041251553835D;
            // 
            // xrTableCell28
            // 
            this.xrTableCell28.Name = "xrTableCell28";
            this.xrTableCell28.Text = "ملاحظات";
            this.xrTableCell28.Weight = 0.452647964462979D;
            // 
            // xrTableCell25
            // 
            this.xrTableCell25.Name = "xrTableCell25";
            this.xrTableCell25.Text = "ساعات العمل";
            this.xrTableCell25.Weight = 0.1759289712396287D;
            // 
            // xrTableCell26
            // 
            this.xrTableCell26.Name = "xrTableCell26";
            this.xrTableCell26.Text = "التاريخ";
            this.xrTableCell26.Weight = 0.19465784626152671D;
            // 
            // xrTableCell34
            // 
            this.xrTableCell34.Name = "xrTableCell34";
            this.xrTableCell34.Text = "وحدة";
            this.xrTableCell34.Weight = 0.16328605531736179D;
            // 
            // xrTableCell33
            // 
            this.xrTableCell33.Name = "xrTableCell33";
            this.xrTableCell33.Text = "المنتج";
            this.xrTableCell33.Weight = 0.47173355098899084D;
            // 
            // xrTableCell27
            // 
            this.xrTableCell27.Name = "xrTableCell27";
            this.xrTableCell27.Text = "الموظف";
            this.xrTableCell27.Weight = 0.60925519921397431D;
            // 
            // DetailReport_Damage
            // 
            this.DetailReport_Damage.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail6,
            this.ReportHeader5});
            this.DetailReport_Damage.Expanded = false;
            this.DetailReport_Damage.Level = 6;
            this.DetailReport_Damage.Name = "DetailReport_Damage";
            this.DetailReport_Damage.PageBreak = DevExpress.XtraReports.UI.PageBreak.AfterBand;
            // 
            // Detail6
            // 
            this.Detail6.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable12});
            this.Detail6.HeightF = 29.16667F;
            this.Detail6.Name = "Detail6";
            // 
            // xrTable12
            // 
            this.xrTable12.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable12.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable12.LocationFloat = new DevExpress.Utils.PointFloat(2.384186E-05F, 0F);
            this.xrTable12.Name = "xrTable12";
            this.xrTable12.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow12});
            this.xrTable12.SizeF = new System.Drawing.SizeF(786F, 29.16667F);
            this.xrTable12.StylePriority.UseBorders = false;
            this.xrTable12.StylePriority.UseFont = false;
            this.xrTable12.StylePriority.UseTextAlignment = false;
            this.xrTable12.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow12
            // 
            this.xrTableRow12.BackColor = System.Drawing.Color.Empty;
            this.xrTableRow12.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_DamageQty,
            this.cell_DamageUOM,
            this.cell_DamageItemName});
            this.xrTableRow12.Font = new System.Drawing.Font("Times New Roman", 10F, System.Drawing.FontStyle.Bold);
            this.xrTableRow12.Name = "xrTableRow12";
            this.xrTableRow12.StylePriority.UseBackColor = false;
            this.xrTableRow12.StylePriority.UseFont = false;
            this.xrTableRow12.Weight = 0.54901959587545957D;
            // 
            // cell_DamageQty
            // 
            this.cell_DamageQty.Name = "cell_DamageQty";
            this.cell_DamageQty.Text = "الكمية";
            this.cell_DamageQty.Weight = 0.38382610291925096D;
            // 
            // cell_DamageUOM
            // 
            this.cell_DamageUOM.Name = "cell_DamageUOM";
            this.cell_DamageUOM.Text = "وحدة القياس";
            this.cell_DamageUOM.Weight = 0.39742381700122631D;
            // 
            // cell_DamageItemName
            // 
            this.cell_DamageItemName.Name = "cell_DamageItemName";
            this.cell_DamageItemName.Text = "اســـم الصنف";
            this.cell_DamageItemName.Weight = 1.4687500800795228D;
            // 
            // ReportHeader5
            // 
            this.ReportHeader5.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLabel19,
            this.xrTable11});
            this.ReportHeader5.HeightF = 78F;
            this.ReportHeader5.Name = "ReportHeader5";
            // 
            // xrLabel19
            // 
            this.xrLabel19.Font = new System.Drawing.Font("Times New Roman", 14F, System.Drawing.FontStyle.Bold);
            this.xrLabel19.ForeColor = System.Drawing.Color.Navy;
            this.xrLabel19.LocationFloat = new DevExpress.Utils.PointFloat(575F, 0F);
            this.xrLabel19.Name = "xrLabel19";
            this.xrLabel19.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel19.SizeF = new System.Drawing.SizeF(202.8332F, 24.49999F);
            this.xrLabel19.StylePriority.UseFont = false;
            this.xrLabel19.StylePriority.UseForeColor = false;
            this.xrLabel19.StylePriority.UseTextAlignment = false;
            this.xrLabel19.Text = "هالك التشغيل";
            this.xrLabel19.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTable11
            // 
            this.xrTable11.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable11.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable11.LocationFloat = new DevExpress.Utils.PointFloat(0F, 24.49999F);
            this.xrTable11.Name = "xrTable11";
            this.xrTable11.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow11});
            this.xrTable11.SizeF = new System.Drawing.SizeF(786F, 53.125F);
            this.xrTable11.StylePriority.UseBorders = false;
            this.xrTable11.StylePriority.UseFont = false;
            this.xrTable11.StylePriority.UseTextAlignment = false;
            this.xrTable11.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow11
            // 
            this.xrTableRow11.BackColor = System.Drawing.Color.Moccasin;
            this.xrTableRow11.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell36,
            this.xrTableCell37,
            this.xrTableCell38});
            this.xrTableRow11.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrTableRow11.Name = "xrTableRow11";
            this.xrTableRow11.StylePriority.UseBackColor = false;
            this.xrTableRow11.StylePriority.UseFont = false;
            this.xrTableRow11.Weight = 1D;
            // 
            // xrTableCell36
            // 
            this.xrTableCell36.Name = "xrTableCell36";
            this.xrTableCell36.Text = "الكمية";
            this.xrTableCell36.Weight = 0.38382623558312584D;
            // 
            // xrTableCell37
            // 
            this.xrTableCell37.Name = "xrTableCell37";
            this.xrTableCell37.Text = "وحدة القياس";
            this.xrTableCell37.Weight = 0.39625975998018909D;
            // 
            // xrTableCell38
            // 
            this.xrTableCell38.Name = "xrTableCell38";
            this.xrTableCell38.Text = "اســـم الصنف";
            this.xrTableCell38.Weight = 1.4751737806316572D;
            // 
            // DetailReport_Products
            // 
            this.DetailReport_Products.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail7,
            this.ReportHeader6});
            this.DetailReport_Products.Expanded = false;
            this.DetailReport_Products.Level = 0;
            this.DetailReport_Products.Name = "DetailReport_Products";
            this.DetailReport_Products.PageBreak = DevExpress.XtraReports.UI.PageBreak.AfterBand;
            // 
            // Detail7
            // 
            this.Detail7.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable14});
            this.Detail7.HeightF = 53.125F;
            this.Detail7.Name = "Detail7";
            // 
            // xrTable14
            // 
            this.xrTable14.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable14.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable14.LocationFloat = new DevExpress.Utils.PointFloat(0F, 0F);
            this.xrTable14.Name = "xrTable14";
            this.xrTable14.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow14});
            this.xrTable14.SizeF = new System.Drawing.SizeF(786F, 53.125F);
            this.xrTable14.StylePriority.UseBorders = false;
            this.xrTable14.StylePriority.UseFont = false;
            this.xrTable14.StylePriority.UseTextAlignment = false;
            this.xrTable14.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow14
            // 
            this.xrTableRow14.BackColor = System.Drawing.Color.Empty;
            this.xrTableRow14.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_Prd_ActualCost,
            this.cell_Prd_SupposedCost,
            this.cell_Prd_TotalQty,
            this.cell_Prd_Qty,
            this.cell_Prd_Height,
            this.cell_Prd_Width,
            this.cell_Prd_Length,
            this.cell_Prd_UOM,
            this.cell_Prd_ItemName,
            this.cell_Prd_BOM});
            this.xrTableRow14.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrTableRow14.Name = "xrTableRow14";
            this.xrTableRow14.StylePriority.UseBackColor = false;
            this.xrTableRow14.StylePriority.UseFont = false;
            this.xrTableRow14.Weight = 1D;
            // 
            // cell_Prd_ActualCost
            // 
            this.cell_Prd_ActualCost.Name = "cell_Prd_ActualCost";
            this.cell_Prd_ActualCost.Text = "التكلفة الفعلية";
            this.cell_Prd_ActualCost.Weight = 0.28476859777028324D;
            // 
            // cell_Prd_SupposedCost
            // 
            this.cell_Prd_SupposedCost.Name = "cell_Prd_SupposedCost";
            this.cell_Prd_SupposedCost.Text = "التكلفة الافتراضية";
            this.cell_Prd_SupposedCost.Weight = 0.28476859777028324D;
            // 
            // cell_Prd_TotalQty
            // 
            this.cell_Prd_TotalQty.Name = "cell_Prd_TotalQty";
            this.cell_Prd_TotalQty.Text = "اجمالي الكمية";
            this.cell_Prd_TotalQty.Weight = 0.20872944183932005D;
            // 
            // cell_Prd_Qty
            // 
            this.cell_Prd_Qty.Name = "cell_Prd_Qty";
            this.cell_Prd_Qty.Text = "الكمية";
            this.cell_Prd_Qty.Weight = 0.18499724555561559D;
            // 
            // cell_Prd_Height
            // 
            this.cell_Prd_Height.Name = "cell_Prd_Height";
            this.cell_Prd_Height.Text = "ارتفاع";
            this.cell_Prd_Height.Weight = 0.13120056472661845D;
            // 
            // cell_Prd_Width
            // 
            this.cell_Prd_Width.Name = "cell_Prd_Width";
            this.cell_Prd_Width.Text = "عرض";
            this.cell_Prd_Width.Weight = 0.13120405728580389D;
            // 
            // cell_Prd_Length
            // 
            this.cell_Prd_Length.Name = "cell_Prd_Length";
            this.cell_Prd_Length.Text = "طول";
            this.cell_Prd_Length.Weight = 0.10782351566635012D;
            // 
            // cell_Prd_UOM
            // 
            this.cell_Prd_UOM.Name = "cell_Prd_UOM";
            this.cell_Prd_UOM.Text = "وحدة القياس";
            this.cell_Prd_UOM.Weight = 0.18064203153129749D;
            // 
            // cell_Prd_ItemName
            // 
            this.cell_Prd_ItemName.Name = "cell_Prd_ItemName";
            this.cell_Prd_ItemName.Text = "اســـم الصنف";
            this.cell_Prd_ItemName.Weight = 0.49317150170566465D;
            // 
            // cell_Prd_BOM
            // 
            this.cell_Prd_BOM.Name = "cell_Prd_BOM";
            this.cell_Prd_BOM.Text = "قائمة الخامات";
            this.cell_Prd_BOM.Weight = 0.24269444614876318D;
            // 
            // ReportHeader6
            // 
            this.ReportHeader6.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLabel13,
            this.xrTable13});
            this.ReportHeader6.HeightF = 78.125F;
            this.ReportHeader6.Name = "ReportHeader6";
            // 
            // xrLabel13
            // 
            this.xrLabel13.Font = new System.Drawing.Font("Times New Roman", 14F, System.Drawing.FontStyle.Bold);
            this.xrLabel13.ForeColor = System.Drawing.Color.Navy;
            this.xrLabel13.LocationFloat = new DevExpress.Utils.PointFloat(582.1669F, 0F);
            this.xrLabel13.Name = "xrLabel13";
            this.xrLabel13.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel13.SizeF = new System.Drawing.SizeF(202.8332F, 24.49999F);
            this.xrLabel13.StylePriority.UseFont = false;
            this.xrLabel13.StylePriority.UseForeColor = false;
            this.xrLabel13.StylePriority.UseTextAlignment = false;
            this.xrLabel13.Text = "المنتجات";
            this.xrLabel13.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTable13
            // 
            this.xrTable13.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable13.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable13.LocationFloat = new DevExpress.Utils.PointFloat(0F, 25F);
            this.xrTable13.Name = "xrTable13";
            this.xrTable13.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow13});
            this.xrTable13.SizeF = new System.Drawing.SizeF(786F, 53.125F);
            this.xrTable13.StylePriority.UseBorders = false;
            this.xrTable13.StylePriority.UseFont = false;
            this.xrTable13.StylePriority.UseTextAlignment = false;
            this.xrTable13.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow13
            // 
            this.xrTableRow13.BackColor = System.Drawing.Color.Moccasin;
            this.xrTableRow13.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell23,
            this.xrTableCell9,
            this.xrTableCell22,
            this.xrTableCell10,
            this.xrTableCell11,
            this.xrTableCell20,
            this.xrTableCell14,
            this.xrTableCell21,
            this.xrTableCell12,
            this.xrTableCell35});
            this.xrTableRow13.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrTableRow13.Name = "xrTableRow13";
            this.xrTableRow13.StylePriority.UseBackColor = false;
            this.xrTableRow13.StylePriority.UseFont = false;
            this.xrTableRow13.Weight = 1D;
            // 
            // xrTableCell23
            // 
            this.xrTableCell23.Name = "xrTableCell23";
            this.xrTableCell23.Text = "التكلفة الفعلية";
            this.xrTableCell23.Weight = 0.28476859777028324D;
            // 
            // xrTableCell9
            // 
            this.xrTableCell9.Name = "xrTableCell9";
            this.xrTableCell9.Text = "التكلفة الافتراضية";
            this.xrTableCell9.Weight = 0.28476859777028324D;
            // 
            // xrTableCell22
            // 
            this.xrTableCell22.Name = "xrTableCell22";
            this.xrTableCell22.Text = "اجمالي الكمية";
            this.xrTableCell22.Weight = 0.20872944183932005D;
            // 
            // xrTableCell10
            // 
            this.xrTableCell10.Name = "xrTableCell10";
            this.xrTableCell10.Text = "الكمية";
            this.xrTableCell10.Weight = 0.18499724555561559D;
            // 
            // xrTableCell11
            // 
            this.xrTableCell11.Name = "xrTableCell11";
            this.xrTableCell11.Text = "ارتفاع";
            this.xrTableCell11.Weight = 0.13120065208609782D;
            // 
            // xrTableCell20
            // 
            this.xrTableCell20.Name = "xrTableCell20";
            this.xrTableCell20.Text = "عرض";
            this.xrTableCell20.Weight = 0.13120396992632452D;
            // 
            // xrTableCell14
            // 
            this.xrTableCell14.Name = "xrTableCell14";
            this.xrTableCell14.Text = "طول";
            this.xrTableCell14.Weight = 0.10782351566635012D;
            // 
            // xrTableCell21
            // 
            this.xrTableCell21.Name = "xrTableCell21";
            this.xrTableCell21.Text = "وحدة القياس";
            this.xrTableCell21.Weight = 0.18064203153129749D;
            // 
            // xrTableCell12
            // 
            this.xrTableCell12.Name = "xrTableCell12";
            this.xrTableCell12.Text = "اســـم الصنف";
            this.xrTableCell12.Weight = 0.49317150170566465D;
            // 
            // xrTableCell35
            // 
            this.xrTableCell35.Name = "xrTableCell35";
            this.xrTableCell35.Text = "قائمة الخامات";
            this.xrTableCell35.Weight = 0.24269444614876318D;
            // 
            // rpt_manufacture
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail,
            this.TopMargin,
            this.BottomMargin,
            this.DetailReport_SupposedRaws,
            this.DetailReport_ActualRaws,
            this.DetailReport_SupposedExpenses,
            this.DetailReport_ActualExpenes,
            this.DetailReport_wages,
            this.DetailReport_Damage,
            this.DetailReport_Products});
            this.Margins = new System.Drawing.Printing.Margins(19, 22, 228, 48);
            this.PageHeight = 1169;
            this.PageWidth = 827;
            this.PaperKind = System.Drawing.Printing.PaperKind.A4;
            this.Version = "15.1";
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable15)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable16)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable10)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable12)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable11)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable14)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable13)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.XRLabel lblReportName;
        private DevExpress.XtraReports.UI.XRPictureBox picLogo;
        private DevExpress.XtraReports.UI.XRLabel lblCompName;
        private DevExpress.XtraReports.UI.XRPageInfo xrPageInfo1;
        private DevExpress.XtraReports.UI.XRLabel xrLabel1;
        private DevExpress.XtraReports.UI.XRLabel xrLabel2;
        private DevExpress.XtraReports.UI.XRLabel lbl_Startdate;
        private DevExpress.XtraReports.UI.XRLabel lbl_EndDate;
        private DevExpress.XtraReports.UI.XRLabel xrLabel7;
        private DevExpress.XtraReports.UI.XRLabel xrLabel8;
        private DevExpress.XtraReports.UI.XRLabel lbl_ProductStore;
        private DevExpress.XtraReports.UI.XRLabel lbl_Manf_Number;
        private DevExpress.XtraReports.UI.XRLabel lbl_ActualCost;
        private DevExpress.XtraReports.UI.XRLabel xrLabel12;
        private DevExpress.XtraReports.UI.XRTable xrTable1;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow1;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell1;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell5;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell3;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell6;
        private DevExpress.XtraReports.UI.XRTable xrTable2;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow2;
        private DevExpress.XtraReports.UI.XRTableCell cell_S_Total;
        private DevExpress.XtraReports.UI.XRTableCell cell_S_Qty;
        private DevExpress.XtraReports.UI.XRTableCell cell_S_ItemName;
        private DevExpress.XtraReports.UI.XRLabel lbl_SupposedCost;
        private DevExpress.XtraReports.UI.XRLabel xrLabel4;
        private DevExpress.XtraReports.UI.XRLabel xrLabel14;
        private DevExpress.XtraReports.UI.XRLabel lbl_Notes;
        private DevExpress.XtraReports.UI.XRLabel xrLabel21;
        private DevExpress.XtraReports.UI.XRTableCell cell_S_UOM;
        private DevExpress.XtraReports.UI.DetailReportBand DetailReport_SupposedRaws;
        private DevExpress.XtraReports.UI.DetailBand Detail1;
        private DevExpress.XtraReports.UI.DetailReportBand DetailReport_ActualRaws;
        private DevExpress.XtraReports.UI.DetailBand Detail2;
        private DevExpress.XtraReports.UI.XRTable xrTable4;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow4;
        private DevExpress.XtraReports.UI.XRTableCell cell_A_Total;
        private DevExpress.XtraReports.UI.XRTableCell cell_A_Qty;
        private DevExpress.XtraReports.UI.XRTableCell cell_A_UOM;
        private DevExpress.XtraReports.UI.XRTableCell cell_A_ItemName;
        private DevExpress.XtraReports.UI.XRTable xrTable3;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow3;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell2;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell4;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell7;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell8;
        private DevExpress.XtraReports.UI.XRLabel xrLabel5;
        private DevExpress.XtraReports.UI.ReportHeaderBand ReportHeader;
        private DevExpress.XtraReports.UI.XRTableCell cell_A_StoreName;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell13;
        private DevExpress.XtraReports.UI.ReportHeaderBand ReportHeader1;
        private DevExpress.XtraReports.UI.DetailReportBand DetailReport_SupposedExpenses;
        private DevExpress.XtraReports.UI.DetailBand Detail3;
        private DevExpress.XtraReports.UI.ReportHeaderBand ReportHeader2;
        private DevExpress.XtraReports.UI.XRTable xrTable6;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow6;
        private DevExpress.XtraReports.UI.XRTableCell cell_S_ExpenseValue;
        private DevExpress.XtraReports.UI.XRTableCell cell_S_ExpenseName;
        private DevExpress.XtraReports.UI.XRLabel xrLabel15;
        private DevExpress.XtraReports.UI.XRTable xrTable5;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow5;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell17;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell18;
        private DevExpress.XtraReports.UI.DetailReportBand DetailReport_ActualExpenes;
        private DevExpress.XtraReports.UI.DetailBand Detail4;
        private DevExpress.XtraReports.UI.XRTable xrTable8;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow8;
        private DevExpress.XtraReports.UI.XRTableCell cell_A_ExpenseValue;
        private DevExpress.XtraReports.UI.XRTableCell cell_A_ExpenseName;
        private DevExpress.XtraReports.UI.ReportHeaderBand ReportHeader3;
        private DevExpress.XtraReports.UI.XRTable xrTable7;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow7;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell15;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell16;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell19;
        private DevExpress.XtraReports.UI.XRLabel xrLabel16;
        private DevExpress.XtraReports.UI.XRTableCell cell_A_ExpenseDrawer;
        private DevExpress.XtraReports.UI.DetailReportBand DetailReport_wages;
        private DevExpress.XtraReports.UI.DetailBand Detail5;
        private DevExpress.XtraReports.UI.XRLabel xrLabel17;
        private DevExpress.XtraReports.UI.XRTable xrTable9;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow9;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell25;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell26;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell27;
        private DevExpress.XtraReports.UI.ReportHeaderBand ReportHeader4;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell29;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell28;
        private DevExpress.XtraReports.UI.XRTable xrTable10;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow10;
        private DevExpress.XtraReports.UI.XRTableCell cell_EmpTotal;
        private DevExpress.XtraReports.UI.XRTableCell cell_EmpNotes;
        private DevExpress.XtraReports.UI.XRTableCell cell_EmpWorkHours;
        private DevExpress.XtraReports.UI.XRTableCell cell_EmpDate;
        private DevExpress.XtraReports.UI.XRTableCell cell_EmpName;
        private DevExpress.XtraReports.UI.DetailReportBand DetailReport_Damage;
        private DevExpress.XtraReports.UI.DetailBand Detail6;
        private DevExpress.XtraReports.UI.ReportHeaderBand ReportHeader5;
        private DevExpress.XtraReports.UI.XRTable xrTable12;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow12;
        private DevExpress.XtraReports.UI.XRTableCell cell_DamageQty;
        private DevExpress.XtraReports.UI.XRTableCell cell_DamageUOM;
        private DevExpress.XtraReports.UI.XRTableCell cell_DamageItemName;
        private DevExpress.XtraReports.UI.XRLabel xrLabel19;
        private DevExpress.XtraReports.UI.XRTable xrTable11;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow11;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell36;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell37;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell38;
        private DevExpress.XtraReports.UI.XRLabel lbl_User;
        private DevExpress.XtraReports.UI.XRLabel xrLabel10;
        private DevExpress.XtraReports.UI.DetailReportBand DetailReport_Products;
        private DevExpress.XtraReports.UI.DetailBand Detail7;
        private DevExpress.XtraReports.UI.ReportHeaderBand ReportHeader6;
        private DevExpress.XtraReports.UI.XRLabel xrLabel13;
        private DevExpress.XtraReports.UI.XRTable xrTable13;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow13;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell9;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell10;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell11;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell12;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell22;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell20;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell14;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell21;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell23;
        private DevExpress.XtraReports.UI.XRTableCell cell_A_TotalQty;
        private DevExpress.XtraReports.UI.XRTableCell cell_A_Height;
        private DevExpress.XtraReports.UI.XRTableCell cell_A_Width;
        private DevExpress.XtraReports.UI.XRTableCell cell_A_Length;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell32;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell24;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell31;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell30;
        private DevExpress.XtraReports.UI.XRTable xrTable14;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow14;
        private DevExpress.XtraReports.UI.XRTableCell cell_Prd_ActualCost;
        private DevExpress.XtraReports.UI.XRTableCell cell_Prd_SupposedCost;
        private DevExpress.XtraReports.UI.XRTableCell cell_Prd_TotalQty;
        private DevExpress.XtraReports.UI.XRTableCell cell_Prd_Qty;
        private DevExpress.XtraReports.UI.XRTableCell cell_Prd_Height;
        private DevExpress.XtraReports.UI.XRTableCell cell_Prd_Width;
        private DevExpress.XtraReports.UI.XRTableCell cell_Prd_Length;
        private DevExpress.XtraReports.UI.XRTableCell cell_Prd_UOM;
        private DevExpress.XtraReports.UI.XRTableCell cell_Prd_ItemName;
        private DevExpress.XtraReports.UI.XRTable xrTable15;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow15;
        private DevExpress.XtraReports.UI.XRTableCell cell_A_PiecesCount;
        private DevExpress.XtraReports.UI.XRTableCell cell_Prd_PiecesCount;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell34;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell33;
        private DevExpress.XtraReports.UI.XRTableCell cell_EmpItemUOM;
        private DevExpress.XtraReports.UI.XRTableCell cell_EmpItemName;
        private DevExpress.XtraReports.UI.XRTableCell cell_Prd_BOM;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell35;
        private DevExpress.XtraReports.UI.XRTable xrTable16;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow16;
        private DevExpress.XtraReports.UI.XRTableCell cell_A_Code1;
        private DevExpress.XtraReports.UI.XRTableCell cell_A_Code2;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell39;
        private DevExpress.XtraReports.UI.SubBand SubBand1;
        private DevExpress.XtraReports.UI.XRLabel xrLabel3;
        private DevExpress.XtraReports.UI.XRLabel lbl_itms_totalQty;
        private DevExpress.XtraReports.UI.XRTableCell cell_A_TotalCost;
    }
}
