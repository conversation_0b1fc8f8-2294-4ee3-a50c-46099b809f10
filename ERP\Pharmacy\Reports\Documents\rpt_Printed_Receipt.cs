using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using System.Linq;
using System.Data;
using DAL;

namespace Reports
{
    public partial class rpt_Printed_Receipt : DevExpress.XtraReports.UI.XtraReport
    {
        public rpt_Printed_Receipt()
        {
            InitializeComponent();
        }


        public void Load_Receipt(DataTable dtSL_Details,
            string _customer, string _number, string _date, string _store, string _drawer, string _notes,
            string _net, string userName, string remains, string discount, string net, string paid,string total)
        {

            ERPDataContext DB = new ERPDataContext();
            var lst_inv_items = (from DataRow dr in dtSL_Details.Rows
                                 where dr.RowState != DataRowState.Deleted
                                 select new
                                 {
                                     ItemId = DB.IC_Items.Where(x => x.ItemId == Convert.ToInt32(dr["ItemId"]))
                                            .Select(x => Shared.IsEnglish ? x.ItemNameEn : x.ItemNameAr).FirstOrDefault(),
                                     Qty = Convert.ToDouble(dr["Qty"]),
                                     SellPrice = Convert.ToDouble(dr["SellPrice"]),
                                     TotalSellPrice = Convert.ToDouble(dr["TotalSellPrice"]),
                                     Serial = dr["Index"].ToString()
                                 }).ToList();

            if (dtSL_Details.Rows.Count == 0)
            {
                //Detail.HeightF = xrTableRow2.HeightF * lst_inv_items.Count();//(float)(((dtSL_Details.Rows.Count * 20) + 50) * 3.1);
                PageHeight = (int)(xrTableRow2.HeightF * lst_inv_items.Count() + PageHeader.HeightF + TopMargin.HeightF + BottomMargin.HeightF) + 10;
                return;
            }
            double addTaxValue =0;
            var invoice  = (from d in DB.SL_Invoices
                            where d.InvoiceCode==_number
                            select d).FirstOrDefault();
            if(invoice != null)
            {
                addTaxValue =Convert.ToDouble(invoice.TaxValue);
            }
          
            lbl_date.Text = _date;
            lbl_Drawer.Text = _drawer;
            lbl_notes.Text = _notes;
            lbl_Number.Text = _number;
            lbl_store.Text = _store;
            lbl_Customer.Text = _customer;
            lbl_User.Text = userName;

            lbl_Net.Text = _net;
            lbl_Remain.Text = remains;
            lbl_Discount.Text = discount;
            lbl_Paid.Text = paid;
            lbl_Total.Text = total;
            var companyName = "";
            var CompanyTaxNumber = "";
            var companyAdress = "";
   
            var company = DB.ST_CompanyInfos.Where(a => a.Company_Id == Shared.st_comp.Company_Id).FirstOrDefault();
            if (company != null)
            {
                companyName = Shared.IsEnglish ? company.CmpNameEn : company.CmpNameAr;
                CompanyTaxNumber = company.TaxCard;
                companyAdress = company.CmpAddress;
          

            }
            qrCode.Text =
                "Seller Name    : " + companyName + System.Environment.NewLine +
                "Seller Address : " + companyAdress + System.Environment.NewLine +
                "Tax Reg. Number: " + CompanyTaxNumber + System.Environment.NewLine +
                "Invoice Code   : " + _number + System.Environment.NewLine +
                "Invoice Date   : " + Convert.ToDateTime(_date).ToShortDateString() + System.Environment.NewLine +
                "Total Amount   : " + total + System.Environment.NewLine +
                "VAT            : " + addTaxValue.ToString() + System.Environment.NewLine +
                "Net            : " + net;
            qrCode.Alignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;

            //Detail.HeightF = xrTableRow2.HeightF * lst_inv_items.Count() ;//(float)(((dtSL_Details.Rows.Count * 20) + 50) * 3.1);
            PageHeight = (int)(xrTableRow2.HeightF * lst_inv_items.Count() + +PageHeader.HeightF + TopMargin.HeightF + BottomMargin.HeightF) + 10;
            this.DataSource = lst_inv_items;
            cell_Item.DataBindings.Add("Text", this.DataSource, "ItemId");
            cell_Qty.DataBindings.Add("Text", this.DataSource, "Qty");
            cell_Price.DataBindings.Add("Text", this.DataSource, "SellPrice");
            cell_Total.DataBindings.Add("Text", this.DataSource, "TotalSellPrice");
            cell_Serial.DataBindings.Add("Text", this.DataSource, "Serial");
        }

    }
}
