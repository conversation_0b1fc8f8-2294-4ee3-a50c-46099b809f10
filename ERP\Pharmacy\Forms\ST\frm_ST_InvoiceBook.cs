﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;
using DAL.Res;
using System.Linq;
using System.Drawing.Printing;
using DevExpress.XtraEditors.Controls;

namespace Pharmacy.Forms
{
    public partial class frm_ST_InvoiceBook : DevExpress.XtraEditors.XtraForm
    {
        int InvoiceBookId;
        bool DataModified;

        public frm_ST_InvoiceBook(int invoiceBookId)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            this.InvoiceBookId = invoiceBookId;
        }

        private void frm_ST_Barcode_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            ERPDataContext DB = new ERPDataContext();
            var InvTypes = (from p in DB.LKP_Processes
                            where p.ProcessId == (int)Process.SellInvoice
                 || p.ProcessId == (int)Process.SellReturn
                 || p.ProcessId == (int)Process.PurchaseInvoice
                 || p.ProcessId == (int)Process.PurchaseReturn
                 || p.ProcessId == (int)Process.SalesOrder
                 || p.ProcessId == (int)Process.sl_Qoute
                 || p.ProcessId == (int)Process.DailyJournal
                 //mohammad 21-09-2020
                  || p.ProcessId == (int)Process.CashIn
                 || p.ProcessId == (int)Process.CashOut
                 || p.ProcessId == (int)Process.NotesReceivable
                 || p.ProcessId == (int)Process.NotesPayable
                 || p.ProcessId == (int)Process.InTrns
                 || p.ProcessId == (int)Process.OutTrns
                            //////////////////////////////////////////////
                            select new { 
                            p.ProcessId,
                            ProcessName = Shared.IsEnglish? p.ProcessEnglishName : p.ProcessName
                            }).ToList();

            lkpProcess.Properties.DataSource = InvTypes;
            lkpProcess.Properties.DisplayMember = "ProcessName";
            lkpProcess.Properties.ValueMember = "ProcessId";
            lkpProcess.ItemIndex = 0;

            Get_Template_Data(InvoiceBookId);
        }


        private void frm_ST_Barcode_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                e.Cancel = true;
            else
                e.Cancel = false;
        }

        private void barBtn_Cancel_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtnDelete_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (InvoiceBookId == 0)
                return;

            if (XtraMessageBox.Show(Shared.IsEnglish == true ? ResEn.MsgAskDelInvBook : ResAr.MsgAskDelInvBook,
                    Shared.IsEnglish == true ? ResAccEn.MsgTWarn : ResAccAr.MsgTWarn,
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2)
                    == DialogResult.No)
                return;

            ERPDataContext db = new ERPDataContext();
            if (

                (Convert.ToInt32(lkpProcess.EditValue) == (int)Process.SellInvoice
                && db.SL_Invoices.Where(s => s.InvoiceBookId == InvoiceBookId).Count() > 0)
                ||
                (Convert.ToInt32(lkpProcess.EditValue) == (int)Process.SellReturn
                && db.SL_Returns.Where(s => s.InvoiceBookId == InvoiceBookId).Count() > 0)
                ||
                (Convert.ToInt32(lkpProcess.EditValue) == (int)Process.PurchaseInvoice
                && db.PR_Invoices.Where(s => s.InvoiceBookId == InvoiceBookId).Count() > 0)
                ||
                (Convert.ToInt32(lkpProcess.EditValue) == (int)Process.PurchaseReturn
                && db.PR_Returns.Where(s => s.InvoiceBookId == InvoiceBookId).Count() > 0)
                ||
                (Convert.ToInt32(lkpProcess.EditValue) == (int)Process.SalesOrder
                && db.SL_SalesOrders.Where(s => s.InvoiceBookId == InvoiceBookId).Count() > 0)
                ||
                (Convert.ToInt32(lkpProcess.EditValue) == (int)Process.sl_Qoute
                && db.SL_Quotes.Where(s => s.InvoiceBookId == InvoiceBookId).Count() > 0)
                ||
                (Convert.ToInt32(lkpProcess.EditValue) == (int)Process.DailyJournal
                && db.ACC_Journals.Where(s => s.BookId == InvoiceBookId).Count() > 0)
                )
                
            {
                XtraMessageBox.Show(Shared.IsEnglish == true ? ResEn.InvBookUsedDel : ResAr.InvBookUsedDel
                     , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            db.ST_InvoiceBooks.DeleteOnSubmit(db.ST_InvoiceBooks.Where(b => b.InvoiceBookId == InvoiceBookId).First());
            db.SubmitChanges();

            XtraMessageBox.Show(Shared.IsEnglish == true ? ResEn.MsgDel : ResAr.MsgDel,
                 "", MessageBoxButtons.OK, MessageBoxIcon.Information);

            NewClearData();
        }


        private void barBtn_New_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            NewClearData();
        }

        private void barBtn_Save_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (txtBookName.Text.Trim() == string.Empty)
            {
                XtraMessageBox.Show(Shared.IsEnglish == true ? ResAccEn.ValTxtName : ResAccAr.ValTxtName
                  , "", MessageBoxButtons.OK, MessageBoxIcon.Information);

                txtBookName.Focus();

                return;
            }

            SaveTemplate();
        }

        private void SaveTemplate()
        {
            ERPDataContext DB = new ERPDataContext();

            ST_InvoiceBook book;
            if (InvoiceBookId == 0)
                book = new ST_InvoiceBook();
            else
                book = DB.ST_InvoiceBooks.Where(x => x.InvoiceBookId == InvoiceBookId).FirstOrDefault();

            book.ProcessId = Convert.ToInt32(lkpProcess.EditValue);
            book.InvoiceBookName = txtBookName.Text;

            if (cmbTaxable.EditValue == null)
                book.IsTaxable = null;
            else if (Convert.ToBoolean(cmbTaxable.EditValue) == false)
                book.IsTaxable = false;
            else if (Convert.ToBoolean(cmbTaxable.EditValue) == true)
                book.IsTaxable = true;

            book.PrintFileName = txtPrintFileName.Text.Trim();

            //update commented
            #region test
           // var text = txtPrintFileName.Text.Trim();
            //var index = txtPrintFileName.Text.Trim().LastIndexOf("\\");

            //if (index > 0)
            //{
            //    if (cmbTaxable.EditValue == null)
            //        book.PrintFileName = "UnDefined\\" + text.Substring(index+1);

            //    if (Convert.ToBoolean(cmbTaxable.EditValue) == true)
            //        book.PrintFileName = "Tax\\" + text.Substring(index+1);


            //    if (Convert.ToBoolean(cmbTaxable.EditValue) == false && cmbTaxable.EditValue != null)
            //        book.PrintFileName = "NoTax\\" + text.Substring(index+1);

            //}
            //else
            //{
            //    if (cmbTaxable.EditValue == null)
            //    book.PrintFileName = "UnDefined\\" + txtPrintFileName.Text.Trim();

            //    if (Convert.ToBoolean(cmbTaxable.EditValue) == true)
            //        book.PrintFileName = "Tax\\" + txtPrintFileName.Text.Trim();

            //    if (Convert.ToBoolean(cmbTaxable.EditValue) == false && cmbTaxable.EditValue != null)
            //        book.PrintFileName = "NoTax\\" + txtPrintFileName.Text.Trim();
            //}

                        #endregion
                        book.Notes = txtNotes.Text.Trim();

            if (InvoiceBookId == 0)
                DB.ST_InvoiceBooks.InsertOnSubmit(book);

            DB.SubmitChanges();
            XtraMessageBox.Show(Shared.IsEnglish == true ? ResAccEn.MsgSave : ResAccAr.MsgSave//"تم الحفظ بنجاح"
                , "", MessageBoxButtons.OK, MessageBoxIcon.Information);

            InvoiceBookId = book.InvoiceBookId;
            DoValidate();
            DataModified = false;

        }

        private void txt_TemplateName_Modified(object sender, EventArgs e)
        {
            DataModified = true;
        }

        private void Get_Template_Data(int _InvoiceBookId)
        {
            ERPDataContext DB = new ERPDataContext();
            var book = (from t in DB.ST_InvoiceBooks
                        where t.InvoiceBookId == _InvoiceBookId
                        select t).FirstOrDefault();

            /*empty_or_new_template*/
            if (InvoiceBookId == 0 || book == null)
                NewClearData();
            else
            {
                InvoiceBookId = _InvoiceBookId;
                lkpProcess.EditValue = book.ProcessId;
                txtBookName.Text = book.InvoiceBookName;
                cmbTaxable.EditValue = book.IsTaxable;

                if (book.PrintFileName != null)
                    txtPrintFileName.Text = book.PrintFileName.Trim();
                else
                    txtPrintFileName.Text = string.Empty;

                txtNotes.Text = book.Notes;

                DoValidate();
                DataModified = false;
            }
        }

        private void frm_ST_Barcode_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.PageUp)
            {
                btnPrev.PerformClick();
            }
            if (e.KeyCode == Keys.PageDown)
            {
                btnNext.PerformClick();
            }
        }

        private void btnNext_Click(object sender, EventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            ERPDataContext DB = new ERPDataContext();
            int lastInvoiceBookId = (from t in DB.ST_InvoiceBooks
                                     where t.InvoiceBookId > InvoiceBookId
                                     orderby t.InvoiceBookId ascending
                                     select t.InvoiceBookId).FirstOrDefault();

            if (lastInvoiceBookId != 0)
            {
                InvoiceBookId = lastInvoiceBookId;
                Get_Template_Data(InvoiceBookId);
            }
            else
            {
                lastInvoiceBookId = (from t in DB.ST_InvoiceBooks
                                     orderby t.InvoiceBookId ascending
                                     select t.InvoiceBookId).FirstOrDefault();

                if (lastInvoiceBookId != 0)
                {
                    InvoiceBookId = lastInvoiceBookId;
                    Get_Template_Data(InvoiceBookId);
                }
            }
        }

        private void btnPrev_Click(object sender, EventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            ERPDataContext DB = new ERPDataContext();
            int lastInvoiceBookId = (from t in DB.ST_InvoiceBooks
                                     where t.InvoiceBookId < InvoiceBookId
                                     orderby t.InvoiceBookId descending
                                     select t.InvoiceBookId).FirstOrDefault();

            if (lastInvoiceBookId != 0)
            {
                InvoiceBookId = lastInvoiceBookId;
                Get_Template_Data(InvoiceBookId);
            }
            else
            {
                lastInvoiceBookId = (from t in DB.ST_InvoiceBooks
                                     orderby t.InvoiceBookId descending
                                     select t.InvoiceBookId).FirstOrDefault();

                if (lastInvoiceBookId != 0)
                {
                    InvoiceBookId = lastInvoiceBookId;
                    Get_Template_Data(InvoiceBookId);
                }
            }
        }

        private void NewClearData()
        {
            InvoiceBookId = 0;
            txtBookName.Text = txtPrintFileName.Text = txtNotes.Text = string.Empty;

        }

        DialogResult ChangesMade()
        {
            if (DataModified)
            {
                DialogResult r = XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResAccEn.MsgDataModified : ResAccAr.MsgDataModified//"لقد قمت بعمل بعض التغييرات, هل تريد الحفظ أولا "
                    , "", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                if (r == DialogResult.Yes)
                {
                    barBtnSave.PerformClick();
                    return r;
                }
                else if (r == DialogResult.No)
                {
                    // no thing made, continue closing or do next or do previous
                    return r;
                }
                else if (r == DialogResult.Cancel)
                {
                    //cancel form action
                    return r;
                }
            }
            return DialogResult.No;
        }

        void DoValidate()
        {
            lkpProcess.DoValidate();
            txtBookName.DoValidate();
            cmbTaxable.DoValidate();
            txtPrintFileName.DoValidate();
            txtNotes.DoValidate();
        }

        private void barBtnHelp_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
        }

        private void lkpProcess_EditValueChanged(object sender, EventArgs e)
        {
            if(lkpProcess.EditValue == null)
                return;
            
            if(Convert.ToInt32(lkpProcess.EditValue) == (int)Process.DailyJournal)
            {
                cmbTaxable.EditValue = null;
                cmbTaxable.Enabled = false;
            }
            else
                cmbTaxable.Enabled = true;
        }
    }
}