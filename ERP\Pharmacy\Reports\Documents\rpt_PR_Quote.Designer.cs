﻿namespace Reports
{
    partial class rpt_PR_Quote
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.xrTable2 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow2 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_Total = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Disc = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_DiscountRatio = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Price = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Qty = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_UOM = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_ItemName = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_code = new DevExpress.XtraReports.UI.XRTableCell();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.lbl_User = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel4 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_notes = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel6 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLine1 = new DevExpress.XtraReports.UI.XRLine();
            this.xrLabel1 = new DevExpress.XtraReports.UI.XRLabel();
            this.lblReportName = new DevExpress.XtraReports.UI.XRLabel();
            this.picLogo = new DevExpress.XtraReports.UI.XRPictureBox();
            this.lblCompName = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Number = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_date = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel8 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel7 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_store = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Vendor = new DevExpress.XtraReports.UI.XRLabel();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.PageHeader = new DevExpress.XtraReports.UI.PageHeaderBand();
            this.xrTable1 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow1 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell9 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell4 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell7 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell6 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell5 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell8 = new DevExpress.XtraReports.UI.XRTableCell();
            this.ReportFooter = new DevExpress.XtraReports.UI.ReportFooterBand();
            this.xrPageInfo1 = new DevExpress.XtraReports.UI.XRPageInfo();
            this.xrLabel5 = new DevExpress.XtraReports.UI.XRLabel();
            this.lblTotalWords = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_DiscountR = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Total = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel16 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Tax = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Net = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel19 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_DiscountV = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel15 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel23 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_DeductTaxV = new DevExpress.XtraReports.UI.XRLabel();
            this.xrTable4 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow4 = new DevExpress.XtraReports.UI.XRTableRow();
            this.Cell_MUOM = new DevExpress.XtraReports.UI.XRTableCell();
            this.Cell_MUOM_Factor = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Factor = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTable5 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow5 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_SalesTax = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_code2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Batch = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Expire = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_DiscountRatio2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_DiscountRatio3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_SalesTaxRatio = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTable3 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow3 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_ItemDescription = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Height = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Width = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Length = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_TotalQty = new DevExpress.XtraReports.UI.XRTableCell();
            this.lbl_ExpensesR = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_salesEmp_Job = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_AddTaxV = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_TaxR = new DevExpress.XtraReports.UI.XRLabel();
            this.xrCrossBandBox3 = new DevExpress.XtraReports.UI.XRCrossBandBox();
            this.xrCrossBandLine6 = new DevExpress.XtraReports.UI.XRCrossBandLine();
            this.xrCrossBandLine5 = new DevExpress.XtraReports.UI.XRCrossBandLine();
            this.xrCrossBandLine4 = new DevExpress.XtraReports.UI.XRCrossBandLine();
            this.xrCrossBandLine3 = new DevExpress.XtraReports.UI.XRCrossBandLine();
            this.xrCrossBandLine2 = new DevExpress.XtraReports.UI.XRCrossBandLine();
            this.xrCrossBandLine1 = new DevExpress.XtraReports.UI.XRCrossBandLine();
            this.xrCrossBandBox2 = new DevExpress.XtraReports.UI.XRCrossBandBox();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable2});
            this.Detail.HeightF = 29.16667F;
            this.Detail.Name = "Detail";
            this.Detail.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Detail.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // xrTable2
            // 
            this.xrTable2.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top)
                        | DevExpress.XtraPrinting.BorderSide.Right)
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable2.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable2.LocationFloat = new DevExpress.Utils.PointFloat(8.58332F, 0F);
            this.xrTable2.Name = "xrTable2";
            this.xrTable2.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow2});
            this.xrTable2.SizeF = new System.Drawing.SizeF(742.3751F, 29.16667F);
            this.xrTable2.StylePriority.UseBorders = false;
            this.xrTable2.StylePriority.UseFont = false;
            this.xrTable2.StylePriority.UseTextAlignment = false;
            this.xrTable2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow2
            // 
            this.xrTableRow2.BackColor = System.Drawing.Color.Empty;
            this.xrTableRow2.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_Total,
            this.cell_Disc,
            this.cell_DiscountRatio,
            this.cell_Price,
            this.cell_Qty,
            this.cell_UOM,
            this.cell_ItemName,
            this.cell_code});
            this.xrTableRow2.Font = new System.Drawing.Font("Times New Roman", 10F, System.Drawing.FontStyle.Bold);
            this.xrTableRow2.Name = "xrTableRow2";
            this.xrTableRow2.StylePriority.UseBackColor = false;
            this.xrTableRow2.StylePriority.UseFont = false;
            this.xrTableRow2.Weight = 0.54901959587545957;
            // 
            // cell_Total
            // 
            this.cell_Total.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.cell_Total.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_Total.Name = "cell_Total";
            this.cell_Total.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_Total.StylePriority.UseBorders = false;
            this.cell_Total.StylePriority.UseFont = false;
            this.cell_Total.StylePriority.UsePadding = false;
            this.cell_Total.StylePriority.UseTextAlignment = false;
            this.cell_Total.Text = "الاجمالي";
            this.cell_Total.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_Total.Weight = 0.29149227655176024;
            // 
            // cell_Disc
            // 
            this.cell_Disc.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.cell_Disc.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_Disc.Name = "cell_Disc";
            this.cell_Disc.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_Disc.StylePriority.UseBorders = false;
            this.cell_Disc.StylePriority.UseFont = false;
            this.cell_Disc.StylePriority.UsePadding = false;
            this.cell_Disc.StylePriority.UseTextAlignment = false;
            this.cell_Disc.Text = "قيمة خصم";
            this.cell_Disc.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_Disc.Weight = 0.22580264662770816;
            // 
            // cell_DiscountRatio
            // 
            this.cell_DiscountRatio.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.cell_DiscountRatio.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_DiscountRatio.Name = "cell_DiscountRatio";
            this.cell_DiscountRatio.StylePriority.UseBorders = false;
            this.cell_DiscountRatio.StylePriority.UseFont = false;
            this.cell_DiscountRatio.StylePriority.UsePadding = false;
            this.cell_DiscountRatio.StylePriority.UseTextAlignment = false;
            this.cell_DiscountRatio.Text = "نسبة خصم";
            this.cell_DiscountRatio.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.cell_DiscountRatio.Weight = 0.18030366903481102;
            // 
            // cell_Price
            // 
            this.cell_Price.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.cell_Price.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_Price.Name = "cell_Price";
            this.cell_Price.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_Price.StylePriority.UseBorders = false;
            this.cell_Price.StylePriority.UseFont = false;
            this.cell_Price.StylePriority.UsePadding = false;
            this.cell_Price.StylePriority.UseTextAlignment = false;
            this.cell_Price.Text = "السعر";
            this.cell_Price.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_Price.Weight = 0.25540705873346431;
            // 
            // cell_Qty
            // 
            this.cell_Qty.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.cell_Qty.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_Qty.Name = "cell_Qty";
            this.cell_Qty.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_Qty.StylePriority.UseBorders = false;
            this.cell_Qty.StylePriority.UseFont = false;
            this.cell_Qty.StylePriority.UsePadding = false;
            this.cell_Qty.StylePriority.UseTextAlignment = false;
            this.cell_Qty.Text = "كمية";
            this.cell_Qty.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_Qty.Weight = 0.22639094735611565;
            // 
            // cell_UOM
            // 
            this.cell_UOM.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.cell_UOM.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_UOM.Name = "cell_UOM";
            this.cell_UOM.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_UOM.StylePriority.UseBorders = false;
            this.cell_UOM.StylePriority.UseFont = false;
            this.cell_UOM.StylePriority.UsePadding = false;
            this.cell_UOM.StylePriority.UseTextAlignment = false;
            this.cell_UOM.Text = "وحدة قياس";
            this.cell_UOM.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_UOM.Weight = 0.23286262494926174;
            // 
            // cell_ItemName
            // 
            this.cell_ItemName.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.cell_ItemName.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_ItemName.Name = "cell_ItemName";
            this.cell_ItemName.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_ItemName.StylePriority.UseBorders = false;
            this.cell_ItemName.StylePriority.UseFont = false;
            this.cell_ItemName.StylePriority.UsePadding = false;
            this.cell_ItemName.StylePriority.UseTextAlignment = false;
            this.cell_ItemName.Text = "اســـم الصنف";
            this.cell_ItemName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_ItemName.Weight = 0.56996911900718517;
            // 
            // cell_code
            // 
            this.cell_code.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.cell_code.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_code.Name = "cell_code";
            this.cell_code.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_code.StylePriority.UseBorders = false;
            this.cell_code.StylePriority.UseFont = false;
            this.cell_code.StylePriority.UsePadding = false;
            this.cell_code.StylePriority.UseTextAlignment = false;
            this.cell_code.Text = "كود";
            this.cell_code.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_code.Weight = 0.14289107483095259;
            // 
            // TopMargin
            // 
            this.TopMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.lbl_User,
            this.xrLabel4,
            this.lbl_notes,
            this.xrLabel6,
            this.xrLine1,
            this.xrLabel1,
            this.lblReportName,
            this.picLogo,
            this.lblCompName,
            this.lbl_Number,
            this.lbl_date,
            this.xrLabel8,
            this.xrLabel7,
            this.lbl_store,
            this.lbl_Vendor});
            this.TopMargin.HeightF = 257F;
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.TopMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // lbl_User
            // 
            this.lbl_User.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_User.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_User.LocationFloat = new DevExpress.Utils.PointFloat(6.5F, 134F);
            this.lbl_User.Name = "lbl_User";
            this.lbl_User.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_User.SizeF = new System.Drawing.SizeF(173.6254F, 24.49998F);
            this.lbl_User.StylePriority.UseBorders = false;
            this.lbl_User.StylePriority.UseFont = false;
            this.lbl_User.StylePriority.UseTextAlignment = false;
            this.lbl_User.Text = "..";
            this.lbl_User.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel4
            // 
            this.xrLabel4.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel4.LocationFloat = new DevExpress.Utils.PointFloat(681.5F, 196F);
            this.xrLabel4.Name = "xrLabel4";
            this.xrLabel4.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel4.SizeF = new System.Drawing.SizeF(68.45856F, 24.49998F);
            this.xrLabel4.StylePriority.UseFont = false;
            this.xrLabel4.StylePriority.UseTextAlignment = false;
            this.xrLabel4.Text = "ملاحظات";
            this.xrLabel4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_notes
            // 
            this.lbl_notes.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_notes.LocationFloat = new DevExpress.Utils.PointFloat(294F, 196F);
            this.lbl_notes.Multiline = true;
            this.lbl_notes.Name = "lbl_notes";
            this.lbl_notes.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_notes.SizeF = new System.Drawing.SizeF(387.5F, 55.95827F);
            this.lbl_notes.StylePriority.UseFont = false;
            this.lbl_notes.StylePriority.UseTextAlignment = false;
            this.lbl_notes.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight;
            // 
            // xrLabel6
            // 
            this.xrLabel6.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel6.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel6.LocationFloat = new DevExpress.Utils.PointFloat(181.5F, 134F);
            this.xrLabel6.Name = "xrLabel6";
            this.xrLabel6.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel6.SizeF = new System.Drawing.SizeF(80.83298F, 24.49998F);
            this.xrLabel6.StylePriority.UseBorders = false;
            this.xrLabel6.StylePriority.UseFont = false;
            this.xrLabel6.StylePriority.UseTextAlignment = false;
            this.xrLabel6.Text = "المستخدم";
            this.xrLabel6.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLine1
            // 
            this.xrLine1.BackColor = System.Drawing.Color.DarkGray;
            this.xrLine1.BorderColor = System.Drawing.Color.DarkGray;
            this.xrLine1.BorderWidth = 0;
            this.xrLine1.ForeColor = System.Drawing.Color.DarkGray;
            this.xrLine1.LineWidth = 0;
            this.xrLine1.LocationFloat = new DevExpress.Utils.PointFloat(6.49999F, 159F);
            this.xrLine1.Name = "xrLine1";
            this.xrLine1.SizeF = new System.Drawing.SizeF(743.7915F, 3.541748F);
            this.xrLine1.StylePriority.UseBackColor = false;
            this.xrLine1.StylePriority.UseBorderColor = false;
            this.xrLine1.StylePriority.UseBorderWidth = false;
            this.xrLine1.StylePriority.UseForeColor = false;
            // 
            // xrLabel1
            // 
            this.xrLabel1.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel1.LocationFloat = new DevExpress.Utils.PointFloat(681.5F, 171.5F);
            this.xrLabel1.Name = "xrLabel1";
            this.xrLabel1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel1.SizeF = new System.Drawing.SizeF(68.45856F, 24.49998F);
            this.xrLabel1.StylePriority.UseFont = false;
            this.xrLabel1.StylePriority.UseTextAlignment = false;
            this.xrLabel1.Text = "المورد";
            this.xrLabel1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lblReportName
            // 
            this.lblReportName.Font = new System.Drawing.Font("Times New Roman", 14F, System.Drawing.FontStyle.Bold);
            this.lblReportName.LocationFloat = new DevExpress.Utils.PointFloat(141.5F, 46.5F);
            this.lblReportName.Name = "lblReportName";
            this.lblReportName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblReportName.SizeF = new System.Drawing.SizeF(120.6249F, 30F);
            this.lblReportName.StylePriority.UseFont = false;
            this.lblReportName.StylePriority.UseTextAlignment = false;
            this.lblReportName.Text = "عرض سعر مورد";
            this.lblReportName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // picLogo
            // 
            this.picLogo.LocationFloat = new DevExpress.Utils.PointFloat(608.5833F, 83.99999F);
            this.picLogo.Name = "picLogo";
            this.picLogo.SizeF = new System.Drawing.SizeF(141.7083F, 70.00001F);
            this.picLogo.Sizing = DevExpress.XtraPrinting.ImageSizeMode.StretchImage;
            // 
            // lblCompName
            // 
            this.lblCompName.Font = new System.Drawing.Font("Times New Roman", 16F, System.Drawing.FontStyle.Bold);
            this.lblCompName.LocationFloat = new DevExpress.Utils.PointFloat(294F, 46.5F);
            this.lblCompName.Name = "lblCompName";
            this.lblCompName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblCompName.SizeF = new System.Drawing.SizeF(456.2916F, 30F);
            this.lblCompName.StylePriority.UseFont = false;
            this.lblCompName.StylePriority.UseTextAlignment = false;
            this.lblCompName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight;
            // 
            // lbl_Number
            // 
            this.lbl_Number.Font = new System.Drawing.Font("Times New Roman", 16F, System.Drawing.FontStyle.Bold);
            this.lbl_Number.LocationFloat = new DevExpress.Utils.PointFloat(6.5F, 46.5F);
            this.lbl_Number.Name = "lbl_Number";
            this.lbl_Number.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Number.SizeF = new System.Drawing.SizeF(131.8333F, 30F);
            this.lbl_Number.StylePriority.UseFont = false;
            this.lbl_Number.StylePriority.UseTextAlignment = false;
            this.lbl_Number.Text = "123";
            this.lbl_Number.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_date
            // 
            this.lbl_date.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_date.LocationFloat = new DevExpress.Utils.PointFloat(6.5F, 84F);
            this.lbl_date.Name = "lbl_date";
            this.lbl_date.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_date.SizeF = new System.Drawing.SizeF(173.6254F, 24.49998F);
            this.lbl_date.StylePriority.UseFont = false;
            this.lbl_date.StylePriority.UseTextAlignment = false;
            this.lbl_date.Text = "1/2/2013";
            this.lbl_date.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel8
            // 
            this.xrLabel8.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel8.LocationFloat = new DevExpress.Utils.PointFloat(181.5F, 109F);
            this.xrLabel8.Name = "xrLabel8";
            this.xrLabel8.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel8.SizeF = new System.Drawing.SizeF(80.83298F, 24.49998F);
            this.xrLabel8.StylePriority.UseFont = false;
            this.xrLabel8.StylePriority.UseTextAlignment = false;
            this.xrLabel8.Text = "الفرع";
            this.xrLabel8.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel7
            // 
            this.xrLabel7.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel7.LocationFloat = new DevExpress.Utils.PointFloat(181.5F, 84F);
            this.xrLabel7.Name = "xrLabel7";
            this.xrLabel7.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel7.SizeF = new System.Drawing.SizeF(80.83298F, 24.49998F);
            this.xrLabel7.StylePriority.UseFont = false;
            this.xrLabel7.StylePriority.UseTextAlignment = false;
            this.xrLabel7.Text = "التاريخ";
            this.xrLabel7.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_store
            // 
            this.lbl_store.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_store.LocationFloat = new DevExpress.Utils.PointFloat(6.5F, 109F);
            this.lbl_store.Name = "lbl_store";
            this.lbl_store.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_store.SizeF = new System.Drawing.SizeF(173.6254F, 24.49998F);
            this.lbl_store.StylePriority.UseFont = false;
            this.lbl_store.StylePriority.UseTextAlignment = false;
            this.lbl_store.Text = "الرئيسي";
            this.lbl_store.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_Vendor
            // 
            this.lbl_Vendor.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_Vendor.LocationFloat = new DevExpress.Utils.PointFloat(294F, 171.5F);
            this.lbl_Vendor.Name = "lbl_Vendor";
            this.lbl_Vendor.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Vendor.SizeF = new System.Drawing.SizeF(387.5F, 24.49998F);
            this.lbl_Vendor.StylePriority.UseFont = false;
            this.lbl_Vendor.StylePriority.UseTextAlignment = false;
            this.lbl_Vendor.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // BottomMargin
            // 
            this.BottomMargin.HeightF = 47F;
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.BottomMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // PageHeader
            // 
            this.PageHeader.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable1});
            this.PageHeader.HeightF = 36.125F;
            this.PageHeader.Name = "PageHeader";
            // 
            // xrTable1
            // 
            this.xrTable1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top)
                        | DevExpress.XtraPrinting.BorderSide.Right)
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable1.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable1.LocationFloat = new DevExpress.Utils.PointFloat(8.583322F, 0F);
            this.xrTable1.Name = "xrTable1";
            this.xrTable1.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow1});
            this.xrTable1.SizeF = new System.Drawing.SizeF(743.4167F, 36.125F);
            this.xrTable1.StylePriority.UseBorders = false;
            this.xrTable1.StylePriority.UseFont = false;
            this.xrTable1.StylePriority.UseTextAlignment = false;
            this.xrTable1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow1
            // 
            this.xrTableRow1.BackColor = System.Drawing.Color.Moccasin;
            this.xrTableRow1.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell1,
            this.xrTableCell9,
            this.xrTableCell4,
            this.xrTableCell7,
            this.xrTableCell6,
            this.xrTableCell5,
            this.xrTableCell3,
            this.xrTableCell8});
            this.xrTableRow1.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrTableRow1.Name = "xrTableRow1";
            this.xrTableRow1.StylePriority.UseBackColor = false;
            this.xrTableRow1.StylePriority.UseFont = false;
            this.xrTableRow1.Weight = 1;
            // 
            // xrTableCell1
            // 
            this.xrTableCell1.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell1.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell1.Name = "xrTableCell1";
            this.xrTableCell1.StylePriority.UseBackColor = false;
            this.xrTableCell1.StylePriority.UseBorders = false;
            this.xrTableCell1.Text = "الاجمالي";
            this.xrTableCell1.Weight = 0.29383425027286358;
            // 
            // xrTableCell9
            // 
            this.xrTableCell9.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell9.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell9.Name = "xrTableCell9";
            this.xrTableCell9.StylePriority.UseBackColor = false;
            this.xrTableCell9.StylePriority.UseBorders = false;
            this.xrTableCell9.Text = "قيمة خصم";
            this.xrTableCell9.Weight = 0.22793417399156304;
            // 
            // xrTableCell4
            // 
            this.xrTableCell4.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell4.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell4.Name = "xrTableCell4";
            this.xrTableCell4.StylePriority.UseBackColor = false;
            this.xrTableCell4.StylePriority.UseBorders = false;
            this.xrTableCell4.Text = "نسبة خصم";
            this.xrTableCell4.Weight = 0.183564296139979;
            // 
            // xrTableCell7
            // 
            this.xrTableCell7.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell7.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell7.Name = "xrTableCell7";
            this.xrTableCell7.StylePriority.UseBackColor = false;
            this.xrTableCell7.StylePriority.UseBorders = false;
            this.xrTableCell7.Text = "السعر";
            this.xrTableCell7.Weight = 0.2553670819804999;
            // 
            // xrTableCell6
            // 
            this.xrTableCell6.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell6.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell6.Name = "xrTableCell6";
            this.xrTableCell6.StylePriority.UseBackColor = false;
            this.xrTableCell6.StylePriority.UseBorders = false;
            this.xrTableCell6.Text = "كمية";
            this.xrTableCell6.Weight = 0.22817255643043483;
            // 
            // xrTableCell5
            // 
            this.xrTableCell5.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell5.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell5.Name = "xrTableCell5";
            this.xrTableCell5.StylePriority.UseBackColor = false;
            this.xrTableCell5.StylePriority.UseBorders = false;
            this.xrTableCell5.Text = "وحدة قياس";
            this.xrTableCell5.Weight = 0.23473334069025742;
            // 
            // xrTableCell3
            // 
            this.xrTableCell3.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell3.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell3.Name = "xrTableCell3";
            this.xrTableCell3.StylePriority.UseBackColor = false;
            this.xrTableCell3.StylePriority.UseBorders = false;
            this.xrTableCell3.Text = "اســـم الصنف";
            this.xrTableCell3.Weight = 0.57575208571178893;
            // 
            // xrTableCell8
            // 
            this.xrTableCell8.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell8.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell8.Name = "xrTableCell8";
            this.xrTableCell8.StylePriority.UseBackColor = false;
            this.xrTableCell8.StylePriority.UseBorders = false;
            this.xrTableCell8.Text = "كود";
            this.xrTableCell8.Weight = 0.14584151168169221;
            // 
            // ReportFooter
            // 
            this.ReportFooter.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrPageInfo1,
            this.xrLabel5,
            this.lblTotalWords,
            this.lbl_DiscountR,
            this.lbl_Total,
            this.xrLabel16,
            this.lbl_Tax,
            this.lbl_Net,
            this.xrLabel19,
            this.lbl_DiscountV,
            this.xrLabel15,
            this.xrLabel23,
            this.lbl_DeductTaxV,
            this.xrTable4,
            this.xrTable5,
            this.xrTable3,
            this.lbl_ExpensesR,
            this.lbl_salesEmp_Job,
            this.lbl_AddTaxV,
            this.lbl_TaxR});
            this.ReportFooter.HeightF = 241F;
            this.ReportFooter.Name = "ReportFooter";
            this.ReportFooter.PrintAtBottom = true;
            // 
            // xrPageInfo1
            // 
            this.xrPageInfo1.Format = "Page {0} of {1} ";
            this.xrPageInfo1.LocationFloat = new DevExpress.Utils.PointFloat(646.0833F, 197.9584F);
            this.xrPageInfo1.Name = "xrPageInfo1";
            this.xrPageInfo1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrPageInfo1.SizeF = new System.Drawing.SizeF(104.8751F, 23.00002F);
            this.xrPageInfo1.StylePriority.UseTextAlignment = false;
            this.xrPageInfo1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel5
            // 
            this.xrLabel5.BackColor = System.Drawing.Color.Silver;
            this.xrLabel5.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel5.LocationFloat = new DevExpress.Utils.PointFloat(521.0833F, 197.9584F);
            this.xrLabel5.Name = "xrLabel5";
            this.xrLabel5.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel5.SizeF = new System.Drawing.SizeF(66.29163F, 24.49995F);
            this.xrLabel5.StylePriority.UseBackColor = false;
            this.xrLabel5.StylePriority.UseFont = false;
            this.xrLabel5.StylePriority.UseTextAlignment = false;
            this.xrLabel5.Text = "فقط وقدره";
            this.xrLabel5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lblTotalWords
            // 
            this.lblTotalWords.BackColor = System.Drawing.Color.Silver;
            this.lblTotalWords.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.lblTotalWords.LocationFloat = new DevExpress.Utils.PointFloat(8.583323F, 197.9584F);
            this.lblTotalWords.Name = "lblTotalWords";
            this.lblTotalWords.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblTotalWords.SizeF = new System.Drawing.SizeF(510.6673F, 24.49995F);
            this.lblTotalWords.StylePriority.UseBackColor = false;
            this.lblTotalWords.StylePriority.UseFont = false;
            this.lblTotalWords.StylePriority.UseTextAlignment = false;
            this.lblTotalWords.Text = "..";
            this.lblTotalWords.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_DiscountR
            // 
            this.lbl_DiscountR.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_DiscountR.CanGrow = false;
            this.lbl_DiscountR.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_DiscountR.LocationFloat = new DevExpress.Utils.PointFloat(111F, 52.9585F);
            this.lbl_DiscountR.Name = "lbl_DiscountR";
            this.lbl_DiscountR.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_DiscountR.SizeF = new System.Drawing.SizeF(43.16687F, 25.45843F);
            this.lbl_DiscountR.StylePriority.UseBorders = false;
            this.lbl_DiscountR.StylePriority.UseFont = false;
            this.lbl_DiscountR.StylePriority.UseTextAlignment = false;
            this.lbl_DiscountR.Text = " ";
            this.lbl_DiscountR.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // lbl_Total
            // 
            this.lbl_Total.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.lbl_Total.CanGrow = false;
            this.lbl_Total.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_Total.LocationFloat = new DevExpress.Utils.PointFloat(8.583331F, 2.041667F);
            this.lbl_Total.Name = "lbl_Total";
            this.lbl_Total.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_Total.SizeF = new System.Drawing.SizeF(100.9171F, 25.45843F);
            this.lbl_Total.StylePriority.UseBorders = false;
            this.lbl_Total.StylePriority.UseFont = false;
            this.lbl_Total.StylePriority.UsePadding = false;
            this.lbl_Total.StylePriority.UseTextAlignment = false;
            this.lbl_Total.Text = " ";
            this.lbl_Total.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel16
            // 
            this.xrLabel16.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel16.CanGrow = false;
            this.xrLabel16.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrLabel16.LocationFloat = new DevExpress.Utils.PointFloat(111F, 27.5001F);
            this.xrLabel16.Name = "xrLabel16";
            this.xrLabel16.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel16.SizeF = new System.Drawing.SizeF(77.19334F, 25.45843F);
            this.xrLabel16.StylePriority.UseBorders = false;
            this.xrLabel16.StylePriority.UseFont = false;
            this.xrLabel16.StylePriority.UsePadding = false;
            this.xrLabel16.StylePriority.UseTextAlignment = false;
            this.xrLabel16.Text = "ض.ع";
            this.xrLabel16.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_Tax
            // 
            this.lbl_Tax.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.lbl_Tax.CanGrow = false;
            this.lbl_Tax.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_Tax.LocationFloat = new DevExpress.Utils.PointFloat(8.583331F, 27.5001F);
            this.lbl_Tax.Name = "lbl_Tax";
            this.lbl_Tax.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_Tax.SizeF = new System.Drawing.SizeF(100.9171F, 25.45843F);
            this.lbl_Tax.StylePriority.UseBorders = false;
            this.lbl_Tax.StylePriority.UseFont = false;
            this.lbl_Tax.StylePriority.UsePadding = false;
            this.lbl_Tax.StylePriority.UseTextAlignment = false;
            this.lbl_Tax.Text = " ";
            this.lbl_Tax.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_Net
            // 
            this.lbl_Net.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top)
                        | DevExpress.XtraPrinting.BorderSide.Right)
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_Net.CanGrow = false;
            this.lbl_Net.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.lbl_Net.LocationFloat = new DevExpress.Utils.PointFloat(7.541665F, 112.2083F);
            this.lbl_Net.Name = "lbl_Net";
            this.lbl_Net.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_Net.SizeF = new System.Drawing.SizeF(102.8696F, 25.45846F);
            this.lbl_Net.StylePriority.UseBorders = false;
            this.lbl_Net.StylePriority.UseFont = false;
            this.lbl_Net.StylePriority.UsePadding = false;
            this.lbl_Net.StylePriority.UseTextAlignment = false;
            this.lbl_Net.Text = " ";
            this.lbl_Net.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel19
            // 
            this.xrLabel19.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel19.CanGrow = false;
            this.xrLabel19.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrLabel19.LocationFloat = new DevExpress.Utils.PointFloat(154.1669F, 52.9585F);
            this.xrLabel19.Name = "xrLabel19";
            this.xrLabel19.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel19.SizeF = new System.Drawing.SizeF(34.02647F, 25.45843F);
            this.xrLabel19.StylePriority.UseBorders = false;
            this.xrLabel19.StylePriority.UseFont = false;
            this.xrLabel19.StylePriority.UseTextAlignment = false;
            this.xrLabel19.Text = "خصم";
            this.xrLabel19.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_DiscountV
            // 
            this.lbl_DiscountV.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.lbl_DiscountV.CanGrow = false;
            this.lbl_DiscountV.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_DiscountV.LocationFloat = new DevExpress.Utils.PointFloat(8.999999F, 52.95849F);
            this.lbl_DiscountV.Name = "lbl_DiscountV";
            this.lbl_DiscountV.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_DiscountV.SizeF = new System.Drawing.SizeF(100.5004F, 25.45843F);
            this.lbl_DiscountV.StylePriority.UseBorders = false;
            this.lbl_DiscountV.StylePriority.UseFont = false;
            this.lbl_DiscountV.StylePriority.UsePadding = false;
            this.lbl_DiscountV.StylePriority.UseTextAlignment = false;
            this.lbl_DiscountV.Text = " ";
            this.lbl_DiscountV.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel15
            // 
            this.xrLabel15.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel15.CanGrow = false;
            this.xrLabel15.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrLabel15.LocationFloat = new DevExpress.Utils.PointFloat(111F, 2.041667F);
            this.xrLabel15.Name = "xrLabel15";
            this.xrLabel15.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel15.SizeF = new System.Drawing.SizeF(77.19334F, 25.45843F);
            this.xrLabel15.StylePriority.UseBorders = false;
            this.xrLabel15.StylePriority.UseFont = false;
            this.xrLabel15.StylePriority.UsePadding = false;
            this.xrLabel15.StylePriority.UseTextAlignment = false;
            this.xrLabel15.Text = "الاجمالي";
            this.xrLabel15.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel23
            // 
            this.xrLabel23.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel23.CanGrow = false;
            this.xrLabel23.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel23.LocationFloat = new DevExpress.Utils.PointFloat(111F, 112.2084F);
            this.xrLabel23.Name = "xrLabel23";
            this.xrLabel23.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel23.SizeF = new System.Drawing.SizeF(77.19334F, 25.4584F);
            this.xrLabel23.StylePriority.UseBorders = false;
            this.xrLabel23.StylePriority.UseFont = false;
            this.xrLabel23.StylePriority.UsePadding = false;
            this.xrLabel23.StylePriority.UseTextAlignment = false;
            this.xrLabel23.Text = "الصافي ";
            this.xrLabel23.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_DeductTaxV
            // 
            this.lbl_DeductTaxV.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top)
                        | DevExpress.XtraPrinting.BorderSide.Right)
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_DeductTaxV.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_DeductTaxV.LocationFloat = new DevExpress.Utils.PointFloat(604.4166F, 102.9585F);
            this.lbl_DeductTaxV.Name = "lbl_DeductTaxV";
            this.lbl_DeductTaxV.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_DeductTaxV.SizeF = new System.Drawing.SizeF(141.7083F, 24.49998F);
            this.lbl_DeductTaxV.StylePriority.UseBorders = false;
            this.lbl_DeductTaxV.StylePriority.UseFont = false;
            this.lbl_DeductTaxV.StylePriority.UseTextAlignment = false;
            this.lbl_DeductTaxV.Text = "DeductTaxV";
            this.lbl_DeductTaxV.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.lbl_DeductTaxV.Visible = false;
            // 
            // xrTable4
            // 
            this.xrTable4.LocationFloat = new DevExpress.Utils.PointFloat(316.9167F, 52.95849F);
            this.xrTable4.Name = "xrTable4";
            this.xrTable4.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow4});
            this.xrTable4.SizeF = new System.Drawing.SizeF(126.7083F, 25F);
            // 
            // xrTableRow4
            // 
            this.xrTableRow4.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.Cell_MUOM,
            this.Cell_MUOM_Factor,
            this.cell_Factor});
            this.xrTableRow4.Name = "xrTableRow4";
            this.xrTableRow4.Visible = false;
            this.xrTableRow4.Weight = 1;
            // 
            // Cell_MUOM
            // 
            this.Cell_MUOM.Name = "Cell_MUOM";
            this.Cell_MUOM.Weight = 0.41126075938999929;
            // 
            // Cell_MUOM_Factor
            // 
            this.Cell_MUOM_Factor.Name = "Cell_MUOM_Factor";
            this.Cell_MUOM_Factor.Weight = 0.36499161053801621;
            // 
            // cell_Factor
            // 
            this.cell_Factor.Name = "cell_Factor";
            this.cell_Factor.Weight = 0.37855708265249177;
            // 
            // xrTable5
            // 
            this.xrTable5.LocationFloat = new DevExpress.Utils.PointFloat(291.9167F, 77.95849F);
            this.xrTable5.Name = "xrTable5";
            this.xrTable5.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow5});
            this.xrTable5.SizeF = new System.Drawing.SizeF(457.7917F, 25F);
            this.xrTable5.StylePriority.UseTextAlignment = false;
            this.xrTable5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrTable5.Visible = false;
            // 
            // xrTableRow5
            // 
            this.xrTableRow5.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_SalesTax,
            this.cell_code2,
            this.cell_Batch,
            this.cell_Expire,
            this.cell_DiscountRatio2,
            this.cell_DiscountRatio3,
            this.cell_SalesTaxRatio});
            this.xrTableRow5.Name = "xrTableRow5";
            this.xrTableRow5.Weight = 1;
            // 
            // cell_SalesTax
            // 
            this.cell_SalesTax.Name = "cell_SalesTax";
            this.cell_SalesTax.Text = "SalesTax";
            this.cell_SalesTax.Weight = 0.3359375;
            // 
            // cell_code2
            // 
            this.cell_code2.Name = "cell_code2";
            this.cell_code2.Text = "code2";
            this.cell_code2.Weight = 0.3359375;
            // 
            // cell_Batch
            // 
            this.cell_Batch.Name = "cell_Batch";
            this.cell_Batch.Text = "Batch";
            this.cell_Batch.Weight = 0.671875;
            // 
            // cell_Expire
            // 
            this.cell_Expire.Name = "cell_Expire";
            this.cell_Expire.Text = "Expire";
            this.cell_Expire.Weight = 0.5704141235351563;
            // 
            // cell_DiscountRatio2
            // 
            this.cell_DiscountRatio2.Name = "cell_DiscountRatio2";
            this.cell_DiscountRatio2.Text = "DiscountRatio2";
            this.cell_DiscountRatio2.Weight = 0.5545858764648437;
            // 
            // cell_DiscountRatio3
            // 
            this.cell_DiscountRatio3.Name = "cell_DiscountRatio3";
            this.cell_DiscountRatio3.Text = "DiscountRatio3";
            this.cell_DiscountRatio3.Weight = 0.615416259765625;
            // 
            // cell_SalesTaxRatio
            // 
            this.cell_SalesTaxRatio.Name = "cell_SalesTaxRatio";
            this.cell_SalesTaxRatio.Text = "SalesTaxRatio";
            this.cell_SalesTaxRatio.Weight = 0.884583740234375;
            // 
            // xrTable3
            // 
            this.xrTable3.LocationFloat = new DevExpress.Utils.PointFloat(366.9167F, 27.95849F);
            this.xrTable3.Name = "xrTable3";
            this.xrTable3.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow3});
            this.xrTable3.SizeF = new System.Drawing.SizeF(373.9583F, 25F);
            this.xrTable3.StylePriority.UseTextAlignment = false;
            this.xrTable3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrTable3.Visible = false;
            // 
            // xrTableRow3
            // 
            this.xrTableRow3.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_ItemDescription,
            this.cell_Height,
            this.cell_Width,
            this.cell_Length,
            this.cell_TotalQty});
            this.xrTableRow3.Name = "xrTableRow3";
            this.xrTableRow3.Weight = 1;
            // 
            // cell_ItemDescription
            // 
            this.cell_ItemDescription.Name = "cell_ItemDescription";
            this.cell_ItemDescription.Text = "ItemDescription";
            this.cell_ItemDescription.Weight = 0.74479171752929685;
            // 
            // cell_Height
            // 
            this.cell_Height.Name = "cell_Height";
            this.cell_Height.Text = "Height";
            this.cell_Height.Weight = 0.74479171752929685;
            // 
            // cell_Width
            // 
            this.cell_Width.Name = "cell_Width";
            this.cell_Width.Text = "Width";
            this.cell_Width.Weight = 0.74999999999999989;
            // 
            // cell_Length
            // 
            this.cell_Length.Name = "cell_Length";
            this.cell_Length.Text = "Length";
            this.cell_Length.Weight = 0.615416259765625;
            // 
            // cell_TotalQty
            // 
            this.cell_TotalQty.Name = "cell_TotalQty";
            this.cell_TotalQty.Text = "TotalQty";
            this.cell_TotalQty.Weight = 0.884583740234375;
            // 
            // lbl_ExpensesR
            // 
            this.lbl_ExpensesR.LocationFloat = new DevExpress.Utils.PointFloat(541.9166F, 52.95849F);
            this.lbl_ExpensesR.Name = "lbl_ExpensesR";
            this.lbl_ExpensesR.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_ExpensesR.SizeF = new System.Drawing.SizeF(100F, 23F);
            this.lbl_ExpensesR.Text = "lbl_ExpensesR";
            this.lbl_ExpensesR.Visible = false;
            // 
            // lbl_salesEmp_Job
            // 
            this.lbl_salesEmp_Job.LocationFloat = new DevExpress.Utils.PointFloat(641.9166F, 52.95849F);
            this.lbl_salesEmp_Job.Name = "lbl_salesEmp_Job";
            this.lbl_salesEmp_Job.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_salesEmp_Job.SizeF = new System.Drawing.SizeF(100F, 23F);
            this.lbl_salesEmp_Job.Text = "lbl_salesEmp_Job";
            this.lbl_salesEmp_Job.Visible = false;
            // 
            // lbl_AddTaxV
            // 
            this.lbl_AddTaxV.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top)
                        | DevExpress.XtraPrinting.BorderSide.Right)
                        | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_AddTaxV.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_AddTaxV.LocationFloat = new DevExpress.Utils.PointFloat(454.4167F, 102.9585F);
            this.lbl_AddTaxV.Name = "lbl_AddTaxV";
            this.lbl_AddTaxV.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_AddTaxV.SizeF = new System.Drawing.SizeF(141.7083F, 24.49998F);
            this.lbl_AddTaxV.StylePriority.UseBorders = false;
            this.lbl_AddTaxV.StylePriority.UseFont = false;
            this.lbl_AddTaxV.StylePriority.UseTextAlignment = false;
            this.lbl_AddTaxV.Text = "AddTaxV";
            this.lbl_AddTaxV.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.lbl_AddTaxV.Visible = false;
            // 
            // lbl_TaxR
            // 
            this.lbl_TaxR.LocationFloat = new DevExpress.Utils.PointFloat(441.9167F, 52.95849F);
            this.lbl_TaxR.Name = "lbl_TaxR";
            this.lbl_TaxR.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_TaxR.SizeF = new System.Drawing.SizeF(100F, 23F);
            this.lbl_TaxR.Text = "lbl_TaxR";
            this.lbl_TaxR.Visible = false;
            // 
            // xrCrossBandBox3
            // 
            this.xrCrossBandBox3.BorderWidth = 1;
            this.xrCrossBandBox3.EndBand = this.ReportFooter;
            this.xrCrossBandBox3.EndPointFloat = new DevExpress.Utils.PointFloat(7.541665F, 81.5F);
            this.xrCrossBandBox3.LocationFloat = new DevExpress.Utils.PointFloat(7.541665F, 0F);
            this.xrCrossBandBox3.Name = "xrCrossBandBox3";
            this.xrCrossBandBox3.StartBand = this.PageHeader;
            this.xrCrossBandBox3.StartPointFloat = new DevExpress.Utils.PointFloat(7.541665F, 0F);
            this.xrCrossBandBox3.WidthF = 102.8696F;
            // 
            // xrCrossBandLine6
            // 
            this.xrCrossBandLine6.EndBand = this.ReportFooter;
            this.xrCrossBandLine6.EndPointFloat = new DevExpress.Utils.PointFloat(188.2917F, 2F);
            this.xrCrossBandLine6.LocationFloat = new DevExpress.Utils.PointFloat(188.2917F, 0F);
            this.xrCrossBandLine6.Name = "xrCrossBandLine6";
            this.xrCrossBandLine6.StartBand = this.PageHeader;
            this.xrCrossBandLine6.StartPointFloat = new DevExpress.Utils.PointFloat(188.2917F, 0F);
            this.xrCrossBandLine6.WidthF = 1F;
            // 
            // xrCrossBandLine5
            // 
            this.xrCrossBandLine5.EndBand = this.ReportFooter;
            this.xrCrossBandLine5.EndPointFloat = new DevExpress.Utils.PointFloat(340.5F, 2F);
            this.xrCrossBandLine5.LocationFloat = new DevExpress.Utils.PointFloat(340.5F, 0F);
            this.xrCrossBandLine5.Name = "xrCrossBandLine5";
            this.xrCrossBandLine5.StartBand = this.PageHeader;
            this.xrCrossBandLine5.StartPointFloat = new DevExpress.Utils.PointFloat(340.5F, 0F);
            this.xrCrossBandLine5.WidthF = 1F;
            // 
            // xrCrossBandLine4
            // 
            this.xrCrossBandLine4.EndBand = this.ReportFooter;
            this.xrCrossBandLine4.EndPointFloat = new DevExpress.Utils.PointFloat(252F, 2F);
            this.xrCrossBandLine4.LocationFloat = new DevExpress.Utils.PointFloat(252F, 0F);
            this.xrCrossBandLine4.Name = "xrCrossBandLine4";
            this.xrCrossBandLine4.StartBand = this.PageHeader;
            this.xrCrossBandLine4.StartPointFloat = new DevExpress.Utils.PointFloat(252F, 0F);
            this.xrCrossBandLine4.WidthF = 1F;
            // 
            // xrCrossBandLine3
            // 
            this.xrCrossBandLine3.EndBand = this.ReportFooter;
            this.xrCrossBandLine3.EndPointFloat = new DevExpress.Utils.PointFloat(419.875F, 2F);
            this.xrCrossBandLine3.LocationFloat = new DevExpress.Utils.PointFloat(419.875F, 0F);
            this.xrCrossBandLine3.Name = "xrCrossBandLine3";
            this.xrCrossBandLine3.StartBand = this.PageHeader;
            this.xrCrossBandLine3.StartPointFloat = new DevExpress.Utils.PointFloat(419.875F, 0F);
            this.xrCrossBandLine3.WidthF = 1F;
            // 
            // xrCrossBandLine2
            // 
            this.xrCrossBandLine2.EndBand = this.ReportFooter;
            this.xrCrossBandLine2.EndPointFloat = new DevExpress.Utils.PointFloat(500.8646F, 2F);
            this.xrCrossBandLine2.LocationFloat = new DevExpress.Utils.PointFloat(500.8646F, 0F);
            this.xrCrossBandLine2.Name = "xrCrossBandLine2";
            this.xrCrossBandLine2.StartBand = this.PageHeader;
            this.xrCrossBandLine2.StartPointFloat = new DevExpress.Utils.PointFloat(500.8646F, 0F);
            this.xrCrossBandLine2.WidthF = 1F;
            // 
            // xrCrossBandLine1
            // 
            this.xrCrossBandLine1.EndBand = this.ReportFooter;
            this.xrCrossBandLine1.EndPointFloat = new DevExpress.Utils.PointFloat(701.2605F, 2F);
            this.xrCrossBandLine1.LocationFloat = new DevExpress.Utils.PointFloat(701.2605F, 1.125F);
            this.xrCrossBandLine1.Name = "xrCrossBandLine1";
            this.xrCrossBandLine1.StartBand = this.PageHeader;
            this.xrCrossBandLine1.StartPointFloat = new DevExpress.Utils.PointFloat(701.2605F, 1.125F);
            this.xrCrossBandLine1.WidthF = 1F;
            // 
            // xrCrossBandBox2
            // 
            this.xrCrossBandBox2.BorderWidth = 1;
            this.xrCrossBandBox2.EndBand = this.ReportFooter;
            this.xrCrossBandBox2.EndPointFloat = new DevExpress.Utils.PointFloat(7.541656F, 2F);
            this.xrCrossBandBox2.LocationFloat = new DevExpress.Utils.PointFloat(7.541656F, 0F);
            this.xrCrossBandBox2.Name = "xrCrossBandBox2";
            this.xrCrossBandBox2.StartBand = this.PageHeader;
            this.xrCrossBandBox2.StartPointFloat = new DevExpress.Utils.PointFloat(7.541656F, 0F);
            this.xrCrossBandBox2.WidthF = 744.4584F;
            // 
            // rpt_PR_Quote
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail,
            this.TopMargin,
            this.BottomMargin,
            this.PageHeader,
            this.ReportFooter});
            this.CrossBandControls.AddRange(new DevExpress.XtraReports.UI.XRCrossBandControl[] {
            this.xrCrossBandLine1,
            this.xrCrossBandLine2,
            this.xrCrossBandBox2,
            this.xrCrossBandLine3,
            this.xrCrossBandLine6,
            this.xrCrossBandLine4,
            this.xrCrossBandLine5,
            this.xrCrossBandBox3});
            this.Margins = new System.Drawing.Printing.Margins(29, 36, 257, 47);
            this.PageHeight = 1169;
            this.PageWidth = 827;
            this.PaperKind = System.Drawing.Printing.PaperKind.A4;
            this.ShowPrintMarginsWarning = false;
            this.Version = "10.1";
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.XRLabel lbl_Vendor;
        private DevExpress.XtraReports.UI.PageHeaderBand PageHeader;
        private DevExpress.XtraReports.UI.ReportFooterBand ReportFooter;
        private DevExpress.XtraReports.UI.XRLabel lbl_User;
        private DevExpress.XtraReports.UI.XRLabel xrLabel4;
        private DevExpress.XtraReports.UI.XRLabel lbl_notes;
        private DevExpress.XtraReports.UI.XRLabel xrLabel6;
        private DevExpress.XtraReports.UI.XRLine xrLine1;
        private DevExpress.XtraReports.UI.XRLabel xrLabel1;
        private DevExpress.XtraReports.UI.XRLabel lblReportName;
        private DevExpress.XtraReports.UI.XRPictureBox picLogo;
        private DevExpress.XtraReports.UI.XRLabel lblCompName;
        private DevExpress.XtraReports.UI.XRLabel lbl_Number;
        private DevExpress.XtraReports.UI.XRLabel lbl_date;
        private DevExpress.XtraReports.UI.XRLabel xrLabel8;
        private DevExpress.XtraReports.UI.XRLabel xrLabel7;
        private DevExpress.XtraReports.UI.XRLabel lbl_store;
        private DevExpress.XtraReports.UI.XRLabel lbl_DeductTaxV;
        private DevExpress.XtraReports.UI.XRTable xrTable4;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow4;
        private DevExpress.XtraReports.UI.XRTableCell Cell_MUOM;
        private DevExpress.XtraReports.UI.XRTableCell Cell_MUOM_Factor;
        private DevExpress.XtraReports.UI.XRTableCell cell_Factor;
        private DevExpress.XtraReports.UI.XRTable xrTable5;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow5;
        private DevExpress.XtraReports.UI.XRTableCell cell_SalesTax;
        private DevExpress.XtraReports.UI.XRTableCell cell_code2;
        private DevExpress.XtraReports.UI.XRTableCell cell_Batch;
        private DevExpress.XtraReports.UI.XRTableCell cell_Expire;
        private DevExpress.XtraReports.UI.XRTableCell cell_DiscountRatio2;
        private DevExpress.XtraReports.UI.XRTableCell cell_DiscountRatio3;
        private DevExpress.XtraReports.UI.XRTableCell cell_SalesTaxRatio;
        private DevExpress.XtraReports.UI.XRTable xrTable3;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow3;
        private DevExpress.XtraReports.UI.XRTableCell cell_ItemDescription;
        private DevExpress.XtraReports.UI.XRTableCell cell_Height;
        private DevExpress.XtraReports.UI.XRTableCell cell_Width;
        private DevExpress.XtraReports.UI.XRTableCell cell_Length;
        private DevExpress.XtraReports.UI.XRTableCell cell_TotalQty;
        private DevExpress.XtraReports.UI.XRLabel lbl_ExpensesR;
        private DevExpress.XtraReports.UI.XRLabel lbl_salesEmp_Job;
        private DevExpress.XtraReports.UI.XRLabel lbl_AddTaxV;
        private DevExpress.XtraReports.UI.XRLabel lbl_TaxR;
        private DevExpress.XtraReports.UI.XRLabel lbl_DiscountR;
        private DevExpress.XtraReports.UI.XRLabel lbl_Total;
        private DevExpress.XtraReports.UI.XRLabel xrLabel16;
        private DevExpress.XtraReports.UI.XRLabel lbl_Tax;
        private DevExpress.XtraReports.UI.XRLabel lbl_Net;
        private DevExpress.XtraReports.UI.XRLabel xrLabel19;
        private DevExpress.XtraReports.UI.XRLabel lbl_DiscountV;
        private DevExpress.XtraReports.UI.XRLabel xrLabel15;
        private DevExpress.XtraReports.UI.XRLabel xrLabel23;
        private DevExpress.XtraReports.UI.XRCrossBandBox xrCrossBandBox3;
        private DevExpress.XtraReports.UI.XRTable xrTable1;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow1;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell1;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell9;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell4;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell7;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell6;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell5;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell3;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell8;
        private DevExpress.XtraReports.UI.XRCrossBandLine xrCrossBandLine6;
        private DevExpress.XtraReports.UI.XRCrossBandLine xrCrossBandLine5;
        private DevExpress.XtraReports.UI.XRCrossBandLine xrCrossBandLine4;
        private DevExpress.XtraReports.UI.XRCrossBandLine xrCrossBandLine3;
        private DevExpress.XtraReports.UI.XRCrossBandLine xrCrossBandLine2;
        private DevExpress.XtraReports.UI.XRCrossBandLine xrCrossBandLine1;
        private DevExpress.XtraReports.UI.XRCrossBandBox xrCrossBandBox2;
        private DevExpress.XtraReports.UI.XRTable xrTable2;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow2;
        private DevExpress.XtraReports.UI.XRTableCell cell_Total;
        private DevExpress.XtraReports.UI.XRTableCell cell_Disc;
        private DevExpress.XtraReports.UI.XRTableCell cell_DiscountRatio;
        private DevExpress.XtraReports.UI.XRTableCell cell_Price;
        private DevExpress.XtraReports.UI.XRTableCell cell_Qty;
        private DevExpress.XtraReports.UI.XRTableCell cell_UOM;
        private DevExpress.XtraReports.UI.XRTableCell cell_ItemName;
        private DevExpress.XtraReports.UI.XRTableCell cell_code;
        private DevExpress.XtraReports.UI.XRPageInfo xrPageInfo1;
        private DevExpress.XtraReports.UI.XRLabel xrLabel5;
        private DevExpress.XtraReports.UI.XRLabel lblTotalWords;
    }
}
