﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="barManager1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="bar1.Text" xml:space="preserve">
    <value>Tools</value>
  </data>
  <metadata name="barAndDockingController1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>134, 17</value>
  </metadata>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="barDockControlTop.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Top</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="barDockControlTop.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="barDockControlTop.Size" type="System.Drawing.Size, System.Drawing">
    <value>1114, 28</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Name" xml:space="preserve">
    <value>barDockControlTop</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="barDockControlBottom.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="barDockControlBottom.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 476</value>
  </data>
  <data name="barDockControlBottom.Size" type="System.Drawing.Size, System.Drawing">
    <value>1114, 0</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Name" xml:space="preserve">
    <value>barDockControlBottom</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="barDockControlLeft.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="barDockControlLeft.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 28</value>
  </data>
  <data name="barDockControlLeft.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 448</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Name" xml:space="preserve">
    <value>barDockControlLeft</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="barDockControlRight.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Right</value>
  </data>
  <data name="barDockControlRight.Location" type="System.Drawing.Point, System.Drawing">
    <value>1114, 28</value>
  </data>
  <data name="barDockControlRight.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 448</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Name" xml:space="preserve">
    <value>barDockControlRight</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <metadata name="$this.Language" type="System.Globalization.CultureInfo, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>ar-EG</value>
  </metadata>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>52</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>1114, 476</value>
  </data>
  <data name="picLogo.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 31</value>
  </data>
  <data name="picLogo.Size" type="System.Drawing.Size, System.Drawing">
    <value>77, 64</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="picLogo.TabIndex" type="System.Int32, mscorlib">
    <value>52</value>
  </data>
  <data name="&gt;&gt;picLogo.Name" xml:space="preserve">
    <value>picLogo</value>
  </data>
  <data name="&gt;&gt;picLogo.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.PictureEdit, DevExpress.XtraEditors.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;picLogo.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;picLogo.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="grdCategory.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="grdCategory.Location" type="System.Drawing.Point, System.Drawing">
    <value>5, 101</value>
  </data>
  <data name="bandedGridView1.Appearance.BandPanel.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8pt, style=Bold</value>
  </data>
  <data name="bandedGridView1.Appearance.HeaderPanel.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 8pt, style=Bold</value>
  </data>
  <data name="bandedGridView1.AppearancePrint.BandPanel.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>224, 224, 224</value>
  </data>
  <data name="bandedGridView1.AppearancePrint.BandPanel.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="bandedGridView1.AppearancePrint.BandPanel.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="bandedGridView1.AppearancePrint.BandPanel.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="bandedGridView1.AppearancePrint.FooterPanel.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>224, 224, 224</value>
  </data>
  <data name="bandedGridView1.AppearancePrint.FooterPanel.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="bandedGridView1.AppearancePrint.FooterPanel.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="bandedGridView1.AppearancePrint.FooterPanel.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="bandedGridView1.AppearancePrint.GroupFooter.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="bandedGridView1.AppearancePrint.GroupFooter.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="bandedGridView1.AppearancePrint.GroupRow.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="bandedGridView1.AppearancePrint.GroupRow.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="bandedGridView1.AppearancePrint.HeaderPanel.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>224, 224, 224</value>
  </data>
  <data name="bandedGridView1.AppearancePrint.HeaderPanel.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="bandedGridView1.AppearancePrint.HeaderPanel.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt, style=Bold</value>
  </data>
  <data name="bandedGridView1.AppearancePrint.HeaderPanel.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="bandedGridView1.AppearancePrint.Lines.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="bandedGridView1.AppearancePrint.Lines.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="bandedGridView1.AppearancePrint.Row.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="bandedGridView1.AppearancePrint.Row.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 9.75pt</value>
  </data>
  <data name="bandedGridView1.AppearancePrint.Row.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="gridBand2.Caption" xml:space="preserve">
    <value>Sold Qty</value>
  </data>
  <data name="gridBand2.MinWidth" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="gridBand2.Name" xml:space="preserve">
    <value>gridBand2</value>
  </data>
  <data name="gridBand2.Width" type="System.Int32, mscorlib">
    <value>465</value>
  </data>
  <data name="gridBand3.Caption" xml:space="preserve">
    <value>Current Qty</value>
  </data>
  <data name="gridBand3.MinWidth" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="gridBand3.Name" xml:space="preserve">
    <value>gridBand3</value>
  </data>
  <data name="gridBand3.Width" type="System.Int32, mscorlib">
    <value>303</value>
  </data>
  <data name="gridBand1.MinWidth" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="gridBand1.Name" xml:space="preserve">
    <value>gridBand1</value>
  </data>
  <data name="gridBand1.Width" type="System.Int32, mscorlib">
    <value>408</value>
  </data>
  <data name="col_Category.Caption" xml:space="preserve">
    <value>Category</value>
  </data>
  <data name="col_Category.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_CurrentLibra.Caption" xml:space="preserve">
    <value>CurrentLibra</value>
  </data>
  <data name="col_CurrentLibra.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_currentKG.Caption" xml:space="preserve">
    <value>currentKG</value>
  </data>
  <data name="col_currentKG.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_width.Caption" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="col_height.Caption" xml:space="preserve">
    <value>Height</value>
  </data>
  <data name="col_length.Caption" xml:space="preserve">
    <value>Length</value>
  </data>
  <data name="bandedGridColumn4.Caption" xml:space="preserve">
    <value>LargeUOMFactor</value>
  </data>
  <data name="bandedGridColumn5.Caption" xml:space="preserve">
    <value>MediumUOMFactor</value>
  </data>
  <data name="col_SoldLQty.Caption" xml:space="preserve">
    <value>Sold Qty in Large UOM</value>
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="col_SoldLQty.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="col_SoldLQty.Summary1" xml:space="preserve">
    <value>SoldLQty</value>
  </data>
  <data name="col_SoldLQty.Summary2" xml:space="preserve">
    <value>{0:n3}</value>
  </data>
  <data name="col_SoldLQty.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_SoldLQty.Width" type="System.Int32, mscorlib">
    <value>93</value>
  </data>
  <data name="col_SoldMQty.Caption" xml:space="preserve">
    <value>Sold Qty in Medium UOM</value>
  </data>
  <data name="col_SoldMQty.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="col_SoldMQty.Summary1" xml:space="preserve">
    <value>SoldMQty</value>
  </data>
  <data name="col_SoldMQty.Summary2" xml:space="preserve">
    <value>{0:n3}</value>
  </data>
  <data name="col_SoldMQty.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_SoldMQty.Width" type="System.Int32, mscorlib">
    <value>93</value>
  </data>
  <data name="col_SoldSQty.Caption" xml:space="preserve">
    <value>Sold Qty in Small UOM</value>
  </data>
  <data name="col_SoldSQty.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="col_SoldSQty.Summary1" xml:space="preserve">
    <value>SoldSQty</value>
  </data>
  <data name="col_SoldSQty.Summary2" xml:space="preserve">
    <value>{0:n3}</value>
  </data>
  <data name="col_SoldSQty.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_SoldSQty.Width" type="System.Int32, mscorlib">
    <value>93</value>
  </data>
  <data name="col_CurrentLQty.Caption" xml:space="preserve">
    <value>Current Qty in Large UOM</value>
  </data>
  <data name="col_CurrentLQty.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="col_CurrentLQty.Summary1" xml:space="preserve">
    <value>CurrentLQty</value>
  </data>
  <data name="col_CurrentLQty.Summary2" xml:space="preserve">
    <value>{0:n3}</value>
  </data>
  <data name="col_CurrentLQty.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_CurrentLQty.Width" type="System.Int32, mscorlib">
    <value>101</value>
  </data>
  <data name="col_CurrentMQty.Caption" xml:space="preserve">
    <value>Current Qty in Medium UOM</value>
  </data>
  <data name="col_CurrentMQty.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="col_CurrentMQty.Summary1" xml:space="preserve">
    <value>CurrentMQty</value>
  </data>
  <data name="col_CurrentMQty.Summary2" xml:space="preserve">
    <value>{0:n3}</value>
  </data>
  <data name="col_CurrentMQty.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_CurrentMQty.Width" type="System.Int32, mscorlib">
    <value>101</value>
  </data>
  <data name="col_CurrentSQty.Caption" xml:space="preserve">
    <value>Current Qty in Small UOM</value>
  </data>
  <data name="col_CurrentSQty.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="col_CurrentSQty.Summary1" xml:space="preserve">
    <value>CurrentSQty</value>
  </data>
  <data name="col_CurrentSQty.Summary2" xml:space="preserve">
    <value>{0:n3}</value>
  </data>
  <data name="col_CurrentSQty.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_CurrentSQty.Width" type="System.Int32, mscorlib">
    <value>101</value>
  </data>
  <data name="col_ItemId.Caption" xml:space="preserve">
    <value>Item Name</value>
  </data>
  <data name="col_ItemId.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_ItemId.Width" type="System.Int32, mscorlib">
    <value>260</value>
  </data>
  <data name="colIndex.Caption" xml:space="preserve">
    <value>#</value>
  </data>
  <data name="colIndex.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colIndex.Width" type="System.Int32, mscorlib">
    <value>73</value>
  </data>
  <data name="bandedGridColumn1.Caption" xml:space="preserve">
    <value>bandedGridColumn1</value>
  </data>
  <data name="bandedGridColumn2.Caption" xml:space="preserve">
    <value>bandedGridColumn2</value>
  </data>
  <data name="bandedGridColumn3.Caption" xml:space="preserve">
    <value>bandedGridColumn3</value>
  </data>
  <data name="col_CurrentPiecesCount.Caption" xml:space="preserve">
    <value>Current Pieces Count</value>
  </data>
  <data name="col_CurrentPiecesCount.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="col_SoldPiecesCount.Caption" xml:space="preserve">
    <value>Sold Pieces Count</value>
  </data>
  <data name="col_SoldPiecesCount.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="col_LibraQty.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="col_LibraQty.Summary1" xml:space="preserve">
    <value>LibraQty</value>
  </data>
  <data name="col_LibraQty.Summary2" xml:space="preserve">
    <value>{0:0.##}</value>
  </data>
  <data name="col_LibraQty.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_LibraQty.Width" type="System.Int32, mscorlib">
    <value>93</value>
  </data>
  <data name="col_kg_Weight_libra.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="col_kg_Weight_libra.Summary1" xml:space="preserve">
    <value>kg_Weight_libra</value>
  </data>
  <data name="col_kg_Weight_libra.Summary2" xml:space="preserve">
    <value>{0:0.##}</value>
  </data>
  <data name="col_kg_Weight_libra.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_kg_Weight_libra.Width" type="System.Int32, mscorlib">
    <value>93</value>
  </data>
  <data name="bandedGridView1.GroupPanelText" xml:space="preserve">
    <value>اسحب أحد الأعمدة لتجميع البيانات على أساسه</value>
  </data>
  <data name="grdCategory.Size" type="System.Drawing.Size, System.Drawing">
    <value>1105, 372</value>
  </data>
  <data name="grdCategory.TabIndex" type="System.Int32, mscorlib">
    <value>47</value>
  </data>
  <data name="&gt;&gt;grdCategory.Name" xml:space="preserve">
    <value>grdCategory</value>
  </data>
  <data name="&gt;&gt;grdCategory.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.GridControl, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;grdCategory.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;grdCategory.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="lblFilter.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="lblFilter.Location" type="System.Drawing.Point, System.Drawing">
    <value>303, 76</value>
  </data>
  <data name="lblFilter.Size" type="System.Drawing.Size, System.Drawing">
    <value>475, 20</value>
  </data>
  <data name="lblFilter.TabIndex" type="System.Int32, mscorlib">
    <value>56</value>
  </data>
  <data name="&gt;&gt;lblFilter.Name" xml:space="preserve">
    <value>lblFilter</value>
  </data>
  <data name="&gt;&gt;lblFilter.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lblFilter.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblFilter.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="lblDateFilter.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="lblDateFilter.Location" type="System.Drawing.Point, System.Drawing">
    <value>303, 56</value>
  </data>
  <data name="lblDateFilter.Size" type="System.Drawing.Size, System.Drawing">
    <value>475, 20</value>
  </data>
  <data name="lblDateFilter.TabIndex" type="System.Int32, mscorlib">
    <value>55</value>
  </data>
  <data name="&gt;&gt;lblDateFilter.Name" xml:space="preserve">
    <value>lblDateFilter</value>
  </data>
  <data name="&gt;&gt;lblDateFilter.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lblDateFilter.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblDateFilter.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="lblReportName.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="lblReportName.Location" type="System.Drawing.Point, System.Drawing">
    <value>303, 31</value>
  </data>
  <data name="lblReportName.Properties.Appearance.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 11.25pt, style=Bold</value>
  </data>
  <data name="lblReportName.Size" type="System.Drawing.Size, System.Drawing">
    <value>475, 24</value>
  </data>
  <data name="lblReportName.TabIndex" type="System.Int32, mscorlib">
    <value>54</value>
  </data>
  <data name="&gt;&gt;lblReportName.Name" xml:space="preserve">
    <value>lblReportName</value>
  </data>
  <data name="&gt;&gt;lblReportName.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.TextEdit, DevExpress.XtraEditors.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lblReportName.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lblReportName.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAA
        AAAfbf8AH2z/AB9r/x8fa/+rH2v/9h9r/9AfbP9HH2//AB9u/wAfcP8AAAAAAB9p/wAfZv8AH2D/Ah9j
        /2cfY//iH2P/7R9j/4YfY/8KH2P/AB9j/wAAAAAADw4QAA4NDwAODQ4ADQwORwwMDdAMCw32CwsMqwsL
        DB8MCw0ADAsNAB9u/wAfbf8WH23/qx9t//8fbv//H27//x9u/9gfb/89H3D/AB9y/wAfcf8AH23/AB9n
        /wAfZ/9cH2X/6x9j//8fY///H2P/9x9j/38fY/8GH2P/AB5e8QAPDhAADw4QAA4NDz0NDQ/YDQ0O/w0M
        Dv8MDA3/DAsNqwwLDRYMCw0AH2//FR9v/6Ifb///H3D//x9x//8fcf//H3H//x9x/9Mfcv82IHD/AiBw
        /wQfcP8DH2z/VB9q/+gfZ///H2X//x9j//8fY///H2P/9x9j/3cfY/8FH2P+ABAPEQAQDxE1Dw4Q0w8O
        EP8ODg//Dg0P/w0NDv8MDA3/DAsNogwLDRUfcP+MH3H//R9y//8ec///HnP//x50//8edP//HnP//x5z
        /9cec/+pH3L/qR9x/6wfbv/mH23//x9q//8faP//H2X//x9j//8fY///H2P/8h9j/04bTb8AERASERAQ
        ErsQEBL/EA8R/w8PEf8PDhD/Dg0P/w0ND/8MDA79DAwNjB5z/9QedP//HnX//x52//8edv//Hnb//x52
        //8edv//Hnb//x51//8edP//HnP//x9x//8fb///H23//x9r//8faP//H2X//x9j//8fY///H2P/gxg4
        hAASERM1ERET7hERE/8REBL/EBAS/xAPEf8PDhD/Dg4Q/w0ND/8NDA7THnX/wx52//8ed///Hnj//x54
        //8eef//Hnn//x54//8eeP/9Hnf/8h52//Iedf/zHnP//h9x//8fb///H23//x9q//8fZ///H2T//x9j
        //8fY/90GkGeABISFCYTEhThEhIU/xIRE/8RERP/EBAS/xAPEf8PDhD/Dg4Q/w4ND78ed/9UHnj/5x55
        //8eev//Hnr//x57//8ee///Hnr/+x56/5AeeP88Hnf/PB52/0Eedf+wHnP//x9x//8fb///H2z//x9p
        //8fZv//H2T/zB9j/yQdV9wAExIVAxMTFYMTEhX8ExIU/xISFP8RERP/ERAS/xAPEf8PDhDkDg4QTx50
        /wAeev9dHnv/7R58//8efP//Hn3//x59//0efP+UHnv/DB55/wAed/8AHnb/AB52/x8edf+5HnP//x9x
        //8fbv//H2v//x9o/9YfZf85H2f/AAAAAAAUExYAFBMWDRQTFpQUExX9ExIV/xISFP8SERP/ERAS6hAP
        EVYTDhQAHnr/AB58/wEefP9nHn3/7x5+//8efv/9Hn7/nR59/xAefP8AHnv/AB52/wAedf8AHnX/AB51
        /yUedP2+H3P//x9x//8fbPvbH2r/Px9o/wAfZv8AH2P/ABQTFgAUExYAFBQWEBQUFp0UExb9ExIV/xIS
        FOwRERNfIxkMABAQEQAee/8AHn3/AB5+/wcefv+xHn///x5//+oef/8tHn//AB59/wAefP8AAAAAAB52
        /wAeV/8AGkKGABk+eUYaQoPzG0KG/xo0ZZIbRZkAH2v/AB9o/wAAAAAAFBQWABQUFgAVFBcAFRQXLRUU
        F+oUExb/ExMVqxMTFAUSERQAEBASAAAAAAAegP8AHn//Ax6A/6kegP//HoD/5x6A/ycegP8AHoD/AAAA
        AAAAAAAAAAAAABpCgQAXFRcAFhMSOhcVFvAXFRb/GBUWjhgYHgAcO3gAAAAAAAAAAAAAAAAAFxYZABYV
        GAAWFRgnFRUX5xUUF/8UExajExIVAhQTFgAAAAAAHoD/AB6A/wAegP8IHoD/sx6B//8egf/rHoH/Lx6B
        /wAegv8AHoL/AAAAAAAXFhkAFBQXABcXGgAXFxpIGBca9BgYG/8ZGBuTGRkcABoaHQAbGx4AAAAAABkZ
        HAAYGBsAFxcZABcWGS8WFhjrFRUX/xUUF6wUFBYGFBMWABQTFQAef/8AHoD/Ah6A/20egf/xHoH//x6B
        //4egv+kHoL/Ex6C/wAegv8AFhYYABsZIAAXFxoAFxcaKRgXGsMYGBv/GRkc/xoZHN4bGh1FHBoeABwb
        HwAdHCAAGhkdABkZHAAYGBsTGBcapBcWGf4WFRj/FRQX7xQUFmYTEBMBExMVAB59/wIegP9kHoD/8B6B
        //8egv//HoL//x6C//8egv+cHoL/EB6B/wAaRX4AFxYZABcWGSQXFxq/GBgb/xkZHP8aGR3/Gxoe/xwb
        HtkcGx89HBwfABoYIQAbGh0AGxodEBkZHJwYGBv/Fxca/xYWGf8VFRf/FBQW7RQTFVwBABAAHn//Vx5/
        /+kegP//HoH//x6C//8egv//HoL//x6C//4egf+JHoD/BBgxUQAWFhgSFxYZsRgXGv8ZGBv/Ghkc/xsa
        Hf8cGx7/HBwf/x0cIM8eHSAmHRwgAB0cHwQbGh6JGhkc/hkYG/8YFxr/FxYZ/xYVGP8VFBf/ExMV5hMS
        FFIefv/EHn///x6A//8egf//HoL//x6C//8egv//HoL//x6B/+Megf8nGUB0ABYWGEwXFhn4GBca/xkY
        G/8aGh3/Gxse/xwcH/8dHCD/Hh0h/x4dIXUdHCAAHBsfJxwbHuMaGh3/GRkc/xgXGv8XFhn/FhUY/xUU
        F/8UExX/ExIUwR5+/9Uef///HoD//x6B//8egf//HoL//x6C//8egf//HoH/7h6B/zQaRX4AFhYZWhcW
        GfwYGBv/GRkc/xsaHf8cGx7/HRwg/x4dIf8fHiL/Hx4igx4dIQAdHB80HBsf7hsaHf8aGRz/GBgb/xcX
        Gf8WFRj/FRQX/xQTFf8TEhXSHn7/jR5///0egP//HoD//x6B//8egf//HoH//x6B//8egf+4HoD/EBk3
        YAAWFhkqFxYZ2xgYG/8ZGRz/Gxod/xwbHv8dHCD/Hh0h/x8eIu8gHyNKHx4iABwcHxAcGx64Gxod/xoZ
        HP8YGBv/Fxca/xYVGP8VFBf/FBMV/BMSFYgefv8VHn7/oh5///8egP//HoD//x6B//8egf//HoD/0B6A
        /zIegf8AFxIRABgaHQAXFxpQGBca5hkYHP8aGh3/Gxse/x0cH/8dHSD1Hh0hch8eIwQfHiIAGxseABwb
        HjIaGh3QGRkc/xgXG/8XFhn/FhUY/xUUF/8UExafExIVEx5+/wAefv8XHn7/qx5///8ef///Hn///x6A
        /9UegP85HoD/AB5//wAYIzUAFxcaABgYGwAYFxpXGRgb6hoZHP8bGh7/HBse+B0cH3weHSEFHh0hAB8e
        IgAbGh0AGxodABoZHDkZGBvVGBca/xcWGf8WFRj/FRQXqBQUFhUUExYAHn7/AB5+/wAefv8dHn7/yR5+
        //8efv/yHn//Sh5//wAef/8AHoD/AAAAAAAYFxoAGBcaABUXGgAZGBtUGRkcwxoaHc0bGh5yHBseCB0c
        HwAeHSEAAAAAABsaHQAaGRwAGRgbABgYG0oYFxryFxYZ/xYVGMUVFBcbFRQXABQUFgAefv8AHnz/AB56
        /wIefP+oHn3//x59/+ceff8lHn3/AB5//wAAAAAAAAAAAAAAAAAYGBoAGBgbABgXGgAaGRwNGhkdERkZ
        HAAbGh4AGxoeAAAAAAAAAAAAAAAAABkZHAAXFxoAFxcaJRcWGecWFRj/FRUXohMSFQEVFBcAFRQXAB50
        /wAed/8AHnn/CR56/7Qee///Hnv/6x57/y4eev8AHnj/AB53/wAAAAAAFRQXABYVGAAWFRgAFxYZJBgX
        GnEYFxp6GRgbNBkOEQAYGBsAGBcaAAAAAAAWFhgAFhYYABYWGQAXFhkuFhYY6xUVF/8VFBeuFBQWBxMT
        FQASERQAHnL/AB11/wMedv9zHnf/8x54//8eef/+Hnn/oR55/xEed/8AHnb/ABMSFgAbFx0AFRQXABUV
        Fy4WFhjJFxYZ/xgXGv8YFxreGBcaShcXGgAYFxoAFxYZABYVGAAWFRgAFhYYERYWGKEWFRj+FRQX/xQT
        FvETEhVuEhETAxIREwAgb/8CH3L/aR50//Iedf//Hnb//x53//8ed//+Hnf/lx52/w0edf8AGEOMABQT
        FgAUExYnFRQXxBUVF/8WFhj/FxYZ/xcXGv8XFxrdFxcaQRYWGQAWFRgAFhUYABYVGA0WFRiXFhUY/hUU
        F/8UExb/ExMV/xISFPARERNlEQ4RAh9t/1sfb//sH3H//x9y//8ec///HnT//x50//8edP/9HnT/hx9y
        /wUVKUwAEhIUFBMSFbYUExb/FBQW/xERE/8QEBL/FhUY/xYWGf8XFhnXFhYYYBYVGE4WFRhOFhUYmBUV
        F/wVFBf/FBMW/xMTFf8SEhT/ERET/xEQEuoQDxFXH2v/xh9s//8fbv//H2///x9w//8fcf//H3H//x9x
        //8fcf/kH3D/KBc3cQASERNOEhIU+BMSFf8SERT/DAwN/woJC/8QDxH/FRUX/xYVGP8WFRj7FRUX+BUU
        F/gVFBf/FBQW/xQTFv8TEhX/EhIU/xIRE/8REBL/EA8R/w8PEMMfZ//UH2n//x9r//8fbP//H23//x9u
        //8fbv//H27//x9u/+4fbv80Fzl6ABEQE1oRERP8EhIU/xERE/8ODg//DAwN/xAPEf8UExb/FBQW/xQU
        Fv8UFBb/FBMW/xQTFv8TExX/ExIU/xISFP8SERP/ERAS/xAQEv8PDxH/Dg4Q0R9l/4kfZf/8H2f//x9o
        //8faf//H2r//x9q//8fav//H2v/sh9r/w4VKlcAEBASKRAQEtoRERP/EhET/xEQEv8QEBL/EhIU/xMT
        Ff8TExXyExMVsxMTFagTEhWoExIU1BISFP8SERP/ERET/xEQEv8QDxH/Dw8R/w4OEPwODQ+EH2P/Ex9j
        /6AfZP//H2T//x9l//8fZv//H2b//x9n/8gfaP8qH2j/AA0CAAAREBIAEA8RThAQEuUREBL/ERET/xIR
        E/8SERP/EhEU9hISFHYTExUIEhEUAxMRFQISERMxERETzREQEv8QEBL/EA8R/w8OEP8ODhD/Dg0Pnw0N
        DxIfY/8AH2P/Fh9j/6kfY///H2P//x9j//8fY//MH2T/MB9m/wAfVP8AERgnABAPEQAQERMAEA8RVRAP
        EegQEBL/EBAS/xEQEvcRERN9EhETBhISFAATEhUAExIVABEREwAQEBI2EA8R0g8PEf8PDhD/Dg4P/w4N
        D6gNDQ8VDg0PAB9j/wAfY/8AH2P/HR9j/6QfY//nH2P/wh9j/zofZP8AH2P/AB9m/wAAAAAAEA8RAA8P
        EQAKDhIBDw8QYA8PEdcQDxHiEA8RgRAPEQkREBIAEhETAAAAAAARERMAEA8RABAPEQAPDhBADg4Qxg4N
        D+cODQ+iDQ0OHA0NDwAODQ8AACAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAGAIBw
        DgEAIAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAQAAHAOAAAgBAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgBAA=
</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Item/Items Total Sales</value>
  </data>
  <data name="&gt;&gt;barManager1.Name" xml:space="preserve">
    <value>barManager1</value>
  </data>
  <data name="&gt;&gt;barManager1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarManager, DevExpress.XtraBars.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;bar1.Name" xml:space="preserve">
    <value>bar1</value>
  </data>
  <data name="&gt;&gt;bar1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Bar, DevExpress.XtraBars.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barSubItem2.Name" xml:space="preserve">
    <value>barSubItem2</value>
  </data>
  <data name="&gt;&gt;barSubItem2.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarSubItem, DevExpress.XtraBars.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnPrint.Name" xml:space="preserve">
    <value>barBtnPrint</value>
  </data>
  <data name="&gt;&gt;barBtnPrint.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;babtnPrintP.Name" xml:space="preserve">
    <value>babtnPrintP</value>
  </data>
  <data name="&gt;&gt;babtnPrintP.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barSubItem1.Name" xml:space="preserve">
    <value>barSubItem1</value>
  </data>
  <data name="&gt;&gt;barSubItem1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarSubItem, DevExpress.XtraBars.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnPreview.Name" xml:space="preserve">
    <value>barBtnPreview</value>
  </data>
  <data name="&gt;&gt;barBtnPreview.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barbtnPrint_P.Name" xml:space="preserve">
    <value>barbtnPrint_P</value>
  </data>
  <data name="&gt;&gt;barbtnPrint_P.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnRefresh.Name" xml:space="preserve">
    <value>barBtnRefresh</value>
  </data>
  <data name="&gt;&gt;barBtnRefresh.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnClose.Name" xml:space="preserve">
    <value>barBtnClose</value>
  </data>
  <data name="&gt;&gt;barBtnClose.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barAndDockingController1.Name" xml:space="preserve">
    <value>barAndDockingController1</value>
  </data>
  <data name="&gt;&gt;barAndDockingController1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarAndDockingController, DevExpress.XtraBars.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repositoryItemTextEdit1.Name" xml:space="preserve">
    <value>repositoryItemTextEdit1</value>
  </data>
  <data name="&gt;&gt;repositoryItemTextEdit1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemTextEdit, DevExpress.XtraEditors.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;bar2.Name" xml:space="preserve">
    <value>bar2</value>
  </data>
  <data name="&gt;&gt;bar2.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Bar, DevExpress.XtraBars.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;bandedGridView1.Name" xml:space="preserve">
    <value>bandedGridView1</value>
  </data>
  <data name="&gt;&gt;bandedGridView1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridView, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_SoldPiecesCount.Name" xml:space="preserve">
    <value>col_SoldPiecesCount</value>
  </data>
  <data name="&gt;&gt;col_SoldPiecesCount.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_SoldSQty.Name" xml:space="preserve">
    <value>col_SoldSQty</value>
  </data>
  <data name="&gt;&gt;col_SoldSQty.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repositoryItemMemoEdit1.Name" xml:space="preserve">
    <value>repositoryItemMemoEdit1</value>
  </data>
  <data name="&gt;&gt;repositoryItemMemoEdit1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit, DevExpress.XtraEditors.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_kg_Weight_libra.Name" xml:space="preserve">
    <value>col_kg_Weight_libra</value>
  </data>
  <data name="&gt;&gt;col_kg_Weight_libra.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_LibraQty.Name" xml:space="preserve">
    <value>col_LibraQty</value>
  </data>
  <data name="&gt;&gt;col_LibraQty.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_SoldMQty.Name" xml:space="preserve">
    <value>col_SoldMQty</value>
  </data>
  <data name="&gt;&gt;col_SoldMQty.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_SoldLQty.Name" xml:space="preserve">
    <value>col_SoldLQty</value>
  </data>
  <data name="&gt;&gt;col_SoldLQty.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_CurrentPiecesCount.Name" xml:space="preserve">
    <value>col_CurrentPiecesCount</value>
  </data>
  <data name="&gt;&gt;col_CurrentPiecesCount.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_CurrentSQty.Name" xml:space="preserve">
    <value>col_CurrentSQty</value>
  </data>
  <data name="&gt;&gt;col_CurrentSQty.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_CurrentMQty.Name" xml:space="preserve">
    <value>col_CurrentMQty</value>
  </data>
  <data name="&gt;&gt;col_CurrentMQty.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_CurrentLQty.Name" xml:space="preserve">
    <value>col_CurrentLQty</value>
  </data>
  <data name="&gt;&gt;col_CurrentLQty.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;bandedGridColumn4.Name" xml:space="preserve">
    <value>bandedGridColumn4</value>
  </data>
  <data name="&gt;&gt;bandedGridColumn4.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;bandedGridColumn5.Name" xml:space="preserve">
    <value>bandedGridColumn5</value>
  </data>
  <data name="&gt;&gt;bandedGridColumn5.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_width.Name" xml:space="preserve">
    <value>col_width</value>
  </data>
  <data name="&gt;&gt;col_width.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_length.Name" xml:space="preserve">
    <value>col_length</value>
  </data>
  <data name="&gt;&gt;col_length.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_height.Name" xml:space="preserve">
    <value>col_height</value>
  </data>
  <data name="&gt;&gt;col_height.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_ItemId.Name" xml:space="preserve">
    <value>col_ItemId</value>
  </data>
  <data name="&gt;&gt;col_ItemId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colIndex.Name" xml:space="preserve">
    <value>colIndex</value>
  </data>
  <data name="&gt;&gt;colIndex.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Category.Name" xml:space="preserve">
    <value>col_Category</value>
  </data>
  <data name="&gt;&gt;col_Category.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_CurrentLibra.Name" xml:space="preserve">
    <value>col_CurrentLibra</value>
  </data>
  <data name="&gt;&gt;col_CurrentLibra.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_currentKG.Name" xml:space="preserve">
    <value>col_currentKG</value>
  </data>
  <data name="&gt;&gt;col_currentKG.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_CurrentQty.Name" xml:space="preserve">
    <value>col_CurrentQty</value>
  </data>
  <data name="&gt;&gt;col_CurrentQty.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_SoldQty.Name" xml:space="preserve">
    <value>col_SoldQty</value>
  </data>
  <data name="&gt;&gt;col_SoldQty.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;bandedGridColumn1.Name" xml:space="preserve">
    <value>bandedGridColumn1</value>
  </data>
  <data name="&gt;&gt;bandedGridColumn1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;bandedGridColumn2.Name" xml:space="preserve">
    <value>bandedGridColumn2</value>
  </data>
  <data name="&gt;&gt;bandedGridColumn2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;bandedGridColumn3.Name" xml:space="preserve">
    <value>bandedGridColumn3</value>
  </data>
  <data name="&gt;&gt;bandedGridColumn3.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridBand2.Name" xml:space="preserve">
    <value>gridBand2</value>
  </data>
  <data name="&gt;&gt;gridBand2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.GridBand, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridBand3.Name" xml:space="preserve">
    <value>gridBand3</value>
  </data>
  <data name="&gt;&gt;gridBand3.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.GridBand, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridBand1.Name" xml:space="preserve">
    <value>gridBand1</value>
  </data>
  <data name="&gt;&gt;gridBand1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.BandedGrid.GridBand, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>frm_SL_ItemsSales</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.XtraForm, DevExpress.Utils.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barBtnPreview.Caption" xml:space="preserve">
    <value>Landscape</value>
  </data>
  <data name="barBtnPreview.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="barBtnClose.Caption" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="barBtnClose.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="barBtnPrint.Caption" xml:space="preserve">
    <value>Landscape</value>
  </data>
  <data name="barBtnPrint.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="barBtnRefresh.Caption" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="barSubItem1.Caption" xml:space="preserve">
    <value>Preview</value>
  </data>
  <data name="barbtnPrint_P.Caption" xml:space="preserve">
    <value>Portrait</value>
  </data>
  <data name="barSubItem2.Caption" xml:space="preserve">
    <value>Preview Data</value>
  </data>
  <data name="babtnPrintP.Caption" xml:space="preserve">
    <value>Portrait</value>
  </data>
  <data name="repositoryItemTextEdit1.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="bar2.Text" xml:space="preserve">
    <value>Custom 3</value>
  </data>
</root>