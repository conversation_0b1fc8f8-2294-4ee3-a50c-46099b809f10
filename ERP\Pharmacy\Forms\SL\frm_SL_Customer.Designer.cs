﻿namespace Pharmacy.Forms
{
    partial class frm_SL_Customer
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_SL_Customer));
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject1 = new DevExpress.Utils.SerializableAppearanceObject();
            this.rep_Check = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
            this.labelControl15 = new DevExpress.XtraEditors.LabelControl();
            this.txtTel = new DevExpress.XtraEditors.TextEdit();
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtnHelp = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnNew = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnDelete = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnSave = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnList = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.txtCusNameAr = new DevExpress.XtraEditors.TextEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.txtCusNameEn = new DevExpress.XtraEditors.TextEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.txtCusCode = new DevExpress.XtraEditors.TextEdit();
            this.btnNext = new DevExpress.XtraEditors.SimpleButton();
            this.btnPrev = new DevExpress.XtraEditors.SimpleButton();
            this.txtMobile = new DevExpress.XtraEditors.TextEdit();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.txtMaxCredit = new DevExpress.XtraEditors.TextEdit();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.txtDiscRatio = new DevExpress.XtraEditors.TextEdit();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl19 = new DevExpress.XtraEditors.LabelControl();
            this.lkpPriceLevel = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.txtCity = new DevExpress.XtraEditors.TextEdit();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.txtZip = new DevExpress.XtraEditors.TextEdit();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.txtFax = new DevExpress.XtraEditors.TextEdit();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.txtEmail = new DevExpress.XtraEditors.TextEdit();
            this.labelControl12 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl13 = new DevExpress.XtraEditors.LabelControl();
            this.txtManagerName = new DevExpress.XtraEditors.TextEdit();
            this.labelControl14 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl16 = new DevExpress.XtraEditors.LabelControl();
            this.lkp_SalesEmp = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl25 = new DevExpress.XtraEditors.LabelControl();
            this.txt_DueDays = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl17 = new DevExpress.XtraEditors.LabelControl();
            this.txt_Representative = new DevExpress.XtraEditors.TextEdit();
            this.labelControl18 = new DevExpress.XtraEditors.LabelControl();
            this.txt_RepresentativeJob = new DevExpress.XtraEditors.TextEdit();
            this.txtRepFName = new DevExpress.XtraEditors.TextEdit();
            this.labelControl20 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl21 = new DevExpress.XtraEditors.LabelControl();
            this.txt_RepFJob = new DevExpress.XtraEditors.TextEdit();
            this.txtAddress = new DevExpress.XtraEditors.MemoEdit();
            this.txtShipping = new DevExpress.XtraEditors.MemoEdit();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.tab_main = new DevExpress.XtraTab.XtraTabPage();
            this.labelControl50 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl48 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl47 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl46 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl45 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl44 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl43 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl42 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl41 = new DevExpress.XtraEditors.LabelControl();
            this.lkp_country = new DevExpress.XtraEditors.LookUpEdit();
            this.txt_Governate = new DevExpress.XtraEditors.TextEdit();
            this.Governate = new DevExpress.XtraEditors.LabelControl();
            this.txt_BuildingNumber = new DevExpress.XtraEditors.TextEdit();
            this.BuildingNumber = new DevExpress.XtraEditors.LabelControl();
            this.txt_Street = new DevExpress.XtraEditors.TextEdit();
            this.txt_Neighborhood = new DevExpress.XtraEditors.TextEdit();
            this.labelControl39 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl40 = new DevExpress.XtraEditors.LabelControl();
            this.lkp_CollectEmp = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl38 = new DevExpress.XtraEditors.LabelControl();
            this.btn_AddRegion = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl36 = new DevExpress.XtraEditors.LabelControl();
            this.lkp_Regions = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl37 = new DevExpress.XtraEditors.LabelControl();
            this.cmbCsType = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.labelControl35 = new DevExpress.XtraEditors.LabelControl();
            this.lkpDelivery = new DevExpress.XtraEditors.LookUpEdit();
            this.txt_Rep_ID = new DevExpress.XtraEditors.TextEdit();
            this.labelControl33 = new DevExpress.XtraEditors.LabelControl();
            this.txt_Rep_Phone = new DevExpress.XtraEditors.TextEdit();
            this.labelControl34 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl32 = new DevExpress.XtraEditors.LabelControl();
            this.lkp_Group = new DevExpress.XtraEditors.LookUpEdit();
            this.txt_Bank = new DevExpress.XtraEditors.TextEdit();
            this.labelControl28 = new DevExpress.XtraEditors.LabelControl();
            this.txt_BankAccNum = new DevExpress.XtraEditors.TextEdit();
            this.labelControl31 = new DevExpress.XtraEditors.LabelControl();
            this.txt_IdNumber = new DevExpress.XtraEditors.TextEdit();
            this.labelControl26 = new DevExpress.XtraEditors.LabelControl();
            this.pnlOpenBlnce = new DevExpress.XtraEditors.PanelControl();
            this.lblOpenAmount = new DevExpress.XtraEditors.LabelControl();
            this.lblOpenDate = new DevExpress.XtraEditors.LabelControl();
            this.txtOpenAmount = new DevExpress.XtraEditors.TextEdit();
            this.dtOpenBalance = new DevExpress.XtraEditors.DateEdit();
            this.cmbIsCredit = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.lblOpenBalance = new DevExpress.XtraEditors.LabelControl();
            this.labelControl24 = new DevExpress.XtraEditors.LabelControl();
            this.lkp_Category = new DevExpress.XtraEditors.LookUpEdit();
            this.uc_LinkAccount1 = new Pharmacy.Forms.uc_LinkAccount();
            this.tab_docs = new DevExpress.XtraTab.XtraTabPage();
            this.btnCustomerItems = new DevExpress.XtraEditors.SimpleButton();
            this.gridVisits = new DevExpress.XtraGrid.GridControl();
            this.bandedGridView1 = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridView();
            this.gridBand1 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.colDayName = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.colId = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_Id = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand2 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.col_Field = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_Telephone = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand3 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.col_Collect = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_Sell = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand4 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.col_AM = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.col_PM = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.gridBand5 = new DevExpress.XtraGrid.Views.BandedGrid.GridBand();
            this.col_Notes = new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn();
            this.chk_IsActive = new DevExpress.XtraEditors.CheckEdit();
            this.chk_IsBlocked = new DevExpress.XtraEditors.CheckEdit();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.labelControl49 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl30 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl27 = new DevExpress.XtraEditors.LabelControl();
            this.chk_IsTaxable = new DevExpress.XtraEditors.CheckEdit();
            this.txtTradeRegistry = new DevExpress.XtraEditors.TextEdit();
            this.txtTaxCardNumber = new DevExpress.XtraEditors.TextEdit();
            this.txtTaxDepartment = new DevExpress.XtraEditors.TextEdit();
            this.lblTaxDepartment = new DevExpress.XtraEditors.LabelControl();
            this.txtTaxFileNumber = new DevExpress.XtraEditors.TextEdit();
            this.labelControl29 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.btnEditPhoto = new DevExpress.XtraEditors.SimpleButton();
            this.btnAddEmpPhoho = new DevExpress.XtraEditors.SimpleButton();
            this.btnDeleteEmpPhoto = new DevExpress.XtraEditors.SimpleButton();
            this.btnShowPhotoes = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl22 = new DevExpress.XtraEditors.LabelControl();
            this.txtImagePath = new DevExpress.XtraEditors.TextEdit();
            this.lstPhotos = new DevExpress.XtraEditors.ListBoxControl();
            this.btnBrowse = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl23 = new DevExpress.XtraEditors.LabelControl();
            this.txtImageDesc = new DevExpress.XtraEditors.TextEdit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Check)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTel.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCusNameAr.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCusNameEn.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCusCode.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMobile.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMaxCredit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscRatio.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpPriceLevel.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCity.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtZip.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtEmail.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtManagerName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_SalesEmp.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DueDays.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Representative.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_RepresentativeJob.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtRepFName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_RepFJob.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAddress.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtShipping.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.tab_main.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_country.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Governate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_BuildingNumber.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Street.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Neighborhood.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_CollectEmp.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Regions.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbCsType.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpDelivery.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Rep_ID.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Rep_Phone.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Group.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Bank.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_BankAccNum.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_IdNumber.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pnlOpenBlnce)).BeginInit();
            this.pnlOpenBlnce.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtOpenAmount.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtOpenBalance.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtOpenBalance.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbIsCredit.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Category.Properties)).BeginInit();
            this.tab_docs.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridVisits)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_IsActive.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_IsBlocked.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chk_IsTaxable.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTradeRegistry.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTaxCardNumber.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTaxDepartment.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTaxFileNumber.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtImagePath.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lstPhotos)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtImageDesc.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // rep_Check
            // 
            resources.ApplyResources(this.rep_Check, "rep_Check");
            this.rep_Check.LookAndFeel.SkinName = "Coffee";
            this.rep_Check.Name = "rep_Check";
            // 
            // labelControl15
            // 
            resources.ApplyResources(this.labelControl15, "labelControl15");
            this.labelControl15.Name = "labelControl15";
            // 
            // txtTel
            // 
            resources.ApplyResources(this.txtTel, "txtTel");
            this.txtTel.EnterMoveNextControl = true;
            this.txtTel.Name = "txtTel";
            this.txtTel.Properties.AccessibleDescription = resources.GetString("txtTel.Properties.AccessibleDescription");
            this.txtTel.Properties.AccessibleName = resources.GetString("txtTel.Properties.AccessibleName");
            this.txtTel.Properties.AutoHeight = ((bool)(resources.GetObject("txtTel.Properties.AutoHeight")));
            this.txtTel.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtTel.Properties.Mask.AutoComplete")));
            this.txtTel.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtTel.Properties.Mask.BeepOnError")));
            this.txtTel.Properties.Mask.EditMask = resources.GetString("txtTel.Properties.Mask.EditMask");
            this.txtTel.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtTel.Properties.Mask.IgnoreMaskBlank")));
            this.txtTel.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtTel.Properties.Mask.MaskType")));
            this.txtTel.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtTel.Properties.Mask.PlaceHolder")));
            this.txtTel.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtTel.Properties.Mask.SaveLiteral")));
            this.txtTel.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtTel.Properties.Mask.ShowPlaceHolders")));
            this.txtTel.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtTel.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtTel.Properties.MaxLength = 50;
            this.txtTel.Properties.NullValuePrompt = resources.GetString("txtTel.Properties.NullValuePrompt");
            this.txtTel.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtTel.Properties.NullValuePromptShowForEmptyValue")));
            this.txtTel.Modified += new System.EventHandler(this.txtVenCode_Modified);
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnSave,
            this.barBtnDelete,
            this.barBtnHelp,
            this.barBtnList,
            this.barBtnClose,
            this.barBtnNew});
            this.barManager1.MaxItemId = 31;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(377, 152);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnHelp),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.KeyTip, this.barBtnNew, "", false, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.Standard, "", ""),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.KeyTip, this.barBtnDelete, "", false, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.Standard, "", ""),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.KeyTip, this.barBtnSave, "", false, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.Standard, "", ""),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.KeyTip, this.barBtnList, "", false, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.Standard, "", ""),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.KeyTip, this.barBtnClose, "", false, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.Standard, "", "")});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtnHelp
            // 
            resources.ApplyResources(this.barBtnHelp, "barBtnHelp");
            this.barBtnHelp.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnHelp.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtnHelp.Id = 2;
            this.barBtnHelp.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtnHelp.Name = "barBtnHelp";
            this.barBtnHelp.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnHelp.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnHelp_ItemClick);
            // 
            // barBtnNew
            // 
            resources.ApplyResources(this.barBtnNew, "barBtnNew");
            this.barBtnNew.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnNew.Glyph = global::Pharmacy.Properties.Resources._new;
            this.barBtnNew.Id = 27;
            this.barBtnNew.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.N));
            this.barBtnNew.Name = "barBtnNew";
            this.barBtnNew.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnNew.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnNew_ItemClick);
            // 
            // barBtnDelete
            // 
            resources.ApplyResources(this.barBtnDelete, "barBtnDelete");
            this.barBtnDelete.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnDelete.Glyph = global::Pharmacy.Properties.Resources.del;
            this.barBtnDelete.Id = 1;
            this.barBtnDelete.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.D));
            this.barBtnDelete.Name = "barBtnDelete";
            this.barBtnDelete.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnDelete.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Delete_ItemClick);
            // 
            // barBtnSave
            // 
            resources.ApplyResources(this.barBtnSave, "barBtnSave");
            this.barBtnSave.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnSave.Glyph = global::Pharmacy.Properties.Resources.sve;
            this.barBtnSave.Id = 0;
            this.barBtnSave.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.S));
            this.barBtnSave.Name = "barBtnSave";
            this.barBtnSave.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnSave.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Save_ItemClick);
            // 
            // barBtnList
            // 
            resources.ApplyResources(this.barBtnList, "barBtnList");
            this.barBtnList.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnList.Glyph = global::Pharmacy.Properties.Resources.list32;
            this.barBtnList.Id = 25;
            this.barBtnList.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.T));
            this.barBtnList.Name = "barBtnList";
            this.barBtnList.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnList.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_List_ItemClick);
            // 
            // barBtnClose
            // 
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 26;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnClose_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlTop.Appearance.FontSizeDelta")));
            this.barDockControlTop.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlTop.Appearance.FontStyleDelta")));
            this.barDockControlTop.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlTop.Appearance.GradientMode")));
            this.barDockControlTop.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlTop.Appearance.Image")));
            this.barDockControlTop.CausesValidation = false;
            // 
            // barDockControlBottom
            // 
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlBottom.Appearance.FontSizeDelta")));
            this.barDockControlBottom.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlBottom.Appearance.FontStyleDelta")));
            this.barDockControlBottom.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlBottom.Appearance.GradientMode")));
            this.barDockControlBottom.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlBottom.Appearance.Image")));
            this.barDockControlBottom.CausesValidation = false;
            // 
            // barDockControlLeft
            // 
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlLeft.Appearance.FontSizeDelta")));
            this.barDockControlLeft.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlLeft.Appearance.FontStyleDelta")));
            this.barDockControlLeft.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlLeft.Appearance.GradientMode")));
            this.barDockControlLeft.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlLeft.Appearance.Image")));
            this.barDockControlLeft.CausesValidation = false;
            // 
            // barDockControlRight
            // 
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlRight.Appearance.FontSizeDelta")));
            this.barDockControlRight.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlRight.Appearance.FontStyleDelta")));
            this.barDockControlRight.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlRight.Appearance.GradientMode")));
            this.barDockControlRight.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlRight.Appearance.Image")));
            this.barDockControlRight.CausesValidation = false;
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemTextEdit1.Mask.AutoComplete")));
            this.repositoryItemTextEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.BeepOnError")));
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemTextEdit1.Mask.MaskType")));
            this.repositoryItemTextEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemTextEdit1.Mask.PlaceHolder")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // labelControl2
            // 
            resources.ApplyResources(this.labelControl2, "labelControl2");
            this.labelControl2.Name = "labelControl2";
            // 
            // labelControl4
            // 
            resources.ApplyResources(this.labelControl4, "labelControl4");
            this.labelControl4.Name = "labelControl4";
            // 
            // txtCusNameAr
            // 
            resources.ApplyResources(this.txtCusNameAr, "txtCusNameAr");
            this.txtCusNameAr.EnterMoveNextControl = true;
            this.txtCusNameAr.Name = "txtCusNameAr";
            this.txtCusNameAr.Properties.AccessibleDescription = resources.GetString("txtCusNameAr.Properties.AccessibleDescription");
            this.txtCusNameAr.Properties.AccessibleName = resources.GetString("txtCusNameAr.Properties.AccessibleName");
            this.txtCusNameAr.Properties.AutoHeight = ((bool)(resources.GetObject("txtCusNameAr.Properties.AutoHeight")));
            this.txtCusNameAr.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtCusNameAr.Properties.Mask.AutoComplete")));
            this.txtCusNameAr.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtCusNameAr.Properties.Mask.BeepOnError")));
            this.txtCusNameAr.Properties.Mask.EditMask = resources.GetString("txtCusNameAr.Properties.Mask.EditMask");
            this.txtCusNameAr.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtCusNameAr.Properties.Mask.IgnoreMaskBlank")));
            this.txtCusNameAr.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtCusNameAr.Properties.Mask.MaskType")));
            this.txtCusNameAr.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtCusNameAr.Properties.Mask.PlaceHolder")));
            this.txtCusNameAr.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtCusNameAr.Properties.Mask.SaveLiteral")));
            this.txtCusNameAr.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtCusNameAr.Properties.Mask.ShowPlaceHolders")));
            this.txtCusNameAr.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtCusNameAr.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtCusNameAr.Properties.NullValuePrompt = resources.GetString("txtCusNameAr.Properties.NullValuePrompt");
            this.txtCusNameAr.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtCusNameAr.Properties.NullValuePromptShowForEmptyValue")));
            this.txtCusNameAr.Modified += new System.EventHandler(this.txtVenCode_Modified);
            this.txtCusNameAr.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtCusNameAr_KeyPress);
            // 
            // labelControl3
            // 
            resources.ApplyResources(this.labelControl3, "labelControl3");
            this.labelControl3.Name = "labelControl3";
            // 
            // txtCusNameEn
            // 
            resources.ApplyResources(this.txtCusNameEn, "txtCusNameEn");
            this.txtCusNameEn.EnterMoveNextControl = true;
            this.txtCusNameEn.Name = "txtCusNameEn";
            this.txtCusNameEn.Properties.AccessibleDescription = resources.GetString("txtCusNameEn.Properties.AccessibleDescription");
            this.txtCusNameEn.Properties.AccessibleName = resources.GetString("txtCusNameEn.Properties.AccessibleName");
            this.txtCusNameEn.Properties.AutoHeight = ((bool)(resources.GetObject("txtCusNameEn.Properties.AutoHeight")));
            this.txtCusNameEn.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtCusNameEn.Properties.Mask.AutoComplete")));
            this.txtCusNameEn.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtCusNameEn.Properties.Mask.BeepOnError")));
            this.txtCusNameEn.Properties.Mask.EditMask = resources.GetString("txtCusNameEn.Properties.Mask.EditMask");
            this.txtCusNameEn.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtCusNameEn.Properties.Mask.IgnoreMaskBlank")));
            this.txtCusNameEn.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtCusNameEn.Properties.Mask.MaskType")));
            this.txtCusNameEn.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtCusNameEn.Properties.Mask.PlaceHolder")));
            this.txtCusNameEn.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtCusNameEn.Properties.Mask.SaveLiteral")));
            this.txtCusNameEn.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtCusNameEn.Properties.Mask.ShowPlaceHolders")));
            this.txtCusNameEn.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtCusNameEn.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtCusNameEn.Properties.NullValuePrompt = resources.GetString("txtCusNameEn.Properties.NullValuePrompt");
            this.txtCusNameEn.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtCusNameEn.Properties.NullValuePromptShowForEmptyValue")));
            this.txtCusNameEn.Modified += new System.EventHandler(this.txtVenCode_Modified);
            this.txtCusNameEn.Leave += new System.EventHandler(this.txtCusNameEn_Leave);
            // 
            // labelControl1
            // 
            resources.ApplyResources(this.labelControl1, "labelControl1");
            this.labelControl1.Name = "labelControl1";
            // 
            // txtCusCode
            // 
            resources.ApplyResources(this.txtCusCode, "txtCusCode");
            this.txtCusCode.EnterMoveNextControl = true;
            this.txtCusCode.Name = "txtCusCode";
            this.txtCusCode.Properties.AccessibleDescription = resources.GetString("txtCusCode.Properties.AccessibleDescription");
            this.txtCusCode.Properties.AccessibleName = resources.GetString("txtCusCode.Properties.AccessibleName");
            this.txtCusCode.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txtCusCode.Properties.Appearance.BackColor")));
            this.txtCusCode.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtCusCode.Properties.Appearance.FontSizeDelta")));
            this.txtCusCode.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtCusCode.Properties.Appearance.FontStyleDelta")));
            this.txtCusCode.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtCusCode.Properties.Appearance.GradientMode")));
            this.txtCusCode.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtCusCode.Properties.Appearance.Image")));
            this.txtCusCode.Properties.Appearance.Options.UseBackColor = true;
            this.txtCusCode.Properties.Appearance.Options.UseTextOptions = true;
            this.txtCusCode.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtCusCode.Properties.AutoHeight = ((bool)(resources.GetObject("txtCusCode.Properties.AutoHeight")));
            this.txtCusCode.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtCusCode.Properties.Mask.AutoComplete")));
            this.txtCusCode.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtCusCode.Properties.Mask.BeepOnError")));
            this.txtCusCode.Properties.Mask.EditMask = resources.GetString("txtCusCode.Properties.Mask.EditMask");
            this.txtCusCode.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtCusCode.Properties.Mask.IgnoreMaskBlank")));
            this.txtCusCode.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtCusCode.Properties.Mask.MaskType")));
            this.txtCusCode.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtCusCode.Properties.Mask.PlaceHolder")));
            this.txtCusCode.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtCusCode.Properties.Mask.SaveLiteral")));
            this.txtCusCode.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtCusCode.Properties.Mask.ShowPlaceHolders")));
            this.txtCusCode.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtCusCode.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtCusCode.Properties.NullValuePrompt = resources.GetString("txtCusCode.Properties.NullValuePrompt");
            this.txtCusCode.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtCusCode.Properties.NullValuePromptShowForEmptyValue")));
            this.txtCusCode.Modified += new System.EventHandler(this.txtVenCode_Modified);
            // 
            // btnNext
            // 
            resources.ApplyResources(this.btnNext, "btnNext");
            this.btnNext.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnNext.Image = global::Pharmacy.Properties.Resources.nxt;
            this.btnNext.Name = "btnNext";
            this.btnNext.Click += new System.EventHandler(this.btnNext_Click);
            // 
            // btnPrev
            // 
            resources.ApplyResources(this.btnPrev, "btnPrev");
            this.btnPrev.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnPrev.Image = global::Pharmacy.Properties.Resources.prev32;
            this.btnPrev.Name = "btnPrev";
            this.btnPrev.Click += new System.EventHandler(this.btnPrev_Click);
            // 
            // txtMobile
            // 
            resources.ApplyResources(this.txtMobile, "txtMobile");
            this.txtMobile.EnterMoveNextControl = true;
            this.txtMobile.Name = "txtMobile";
            this.txtMobile.Properties.AccessibleDescription = resources.GetString("txtMobile.Properties.AccessibleDescription");
            this.txtMobile.Properties.AccessibleName = resources.GetString("txtMobile.Properties.AccessibleName");
            this.txtMobile.Properties.AutoHeight = ((bool)(resources.GetObject("txtMobile.Properties.AutoHeight")));
            this.txtMobile.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtMobile.Properties.Mask.AutoComplete")));
            this.txtMobile.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtMobile.Properties.Mask.BeepOnError")));
            this.txtMobile.Properties.Mask.EditMask = resources.GetString("txtMobile.Properties.Mask.EditMask");
            this.txtMobile.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtMobile.Properties.Mask.IgnoreMaskBlank")));
            this.txtMobile.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtMobile.Properties.Mask.MaskType")));
            this.txtMobile.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtMobile.Properties.Mask.PlaceHolder")));
            this.txtMobile.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtMobile.Properties.Mask.SaveLiteral")));
            this.txtMobile.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtMobile.Properties.Mask.ShowPlaceHolders")));
            this.txtMobile.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtMobile.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtMobile.Properties.MaxLength = 50;
            this.txtMobile.Properties.NullValuePrompt = resources.GetString("txtMobile.Properties.NullValuePrompt");
            this.txtMobile.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtMobile.Properties.NullValuePromptShowForEmptyValue")));
            this.txtMobile.Modified += new System.EventHandler(this.txtVenCode_Modified);
            // 
            // labelControl5
            // 
            resources.ApplyResources(this.labelControl5, "labelControl5");
            this.labelControl5.Name = "labelControl5";
            // 
            // txtMaxCredit
            // 
            resources.ApplyResources(this.txtMaxCredit, "txtMaxCredit");
            this.txtMaxCredit.EnterMoveNextControl = true;
            this.txtMaxCredit.Name = "txtMaxCredit";
            this.txtMaxCredit.Properties.AccessibleDescription = resources.GetString("txtMaxCredit.Properties.AccessibleDescription");
            this.txtMaxCredit.Properties.AccessibleName = resources.GetString("txtMaxCredit.Properties.AccessibleName");
            this.txtMaxCredit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txtMaxCredit.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtMaxCredit.Properties.Appearance.FontSizeDelta")));
            this.txtMaxCredit.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtMaxCredit.Properties.Appearance.FontStyleDelta")));
            this.txtMaxCredit.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtMaxCredit.Properties.Appearance.GradientMode")));
            this.txtMaxCredit.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtMaxCredit.Properties.Appearance.Image")));
            this.txtMaxCredit.Properties.Appearance.Options.UseTextOptions = true;
            this.txtMaxCredit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtMaxCredit.Properties.AutoHeight = ((bool)(resources.GetObject("txtMaxCredit.Properties.AutoHeight")));
            this.txtMaxCredit.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtMaxCredit.Properties.Mask.AutoComplete")));
            this.txtMaxCredit.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtMaxCredit.Properties.Mask.BeepOnError")));
            this.txtMaxCredit.Properties.Mask.EditMask = resources.GetString("txtMaxCredit.Properties.Mask.EditMask");
            this.txtMaxCredit.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtMaxCredit.Properties.Mask.IgnoreMaskBlank")));
            this.txtMaxCredit.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtMaxCredit.Properties.Mask.MaskType")));
            this.txtMaxCredit.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtMaxCredit.Properties.Mask.PlaceHolder")));
            this.txtMaxCredit.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtMaxCredit.Properties.Mask.SaveLiteral")));
            this.txtMaxCredit.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtMaxCredit.Properties.Mask.ShowPlaceHolders")));
            this.txtMaxCredit.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtMaxCredit.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtMaxCredit.Properties.NullText = resources.GetString("txtMaxCredit.Properties.NullText");
            this.txtMaxCredit.Properties.NullValuePrompt = resources.GetString("txtMaxCredit.Properties.NullValuePrompt");
            this.txtMaxCredit.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtMaxCredit.Properties.NullValuePromptShowForEmptyValue")));
            this.txtMaxCredit.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtOpenDebit_Spin);
            this.txtMaxCredit.Modified += new System.EventHandler(this.txtVenCode_Modified);
            this.txtMaxCredit.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtMaxCredit_KeyPress);
            // 
            // labelControl7
            // 
            resources.ApplyResources(this.labelControl7, "labelControl7");
            this.labelControl7.Name = "labelControl7";
            // 
            // txtDiscRatio
            // 
            resources.ApplyResources(this.txtDiscRatio, "txtDiscRatio");
            this.txtDiscRatio.EnterMoveNextControl = true;
            this.txtDiscRatio.Name = "txtDiscRatio";
            this.txtDiscRatio.Properties.AccessibleDescription = resources.GetString("txtDiscRatio.Properties.AccessibleDescription");
            this.txtDiscRatio.Properties.AccessibleName = resources.GetString("txtDiscRatio.Properties.AccessibleName");
            this.txtDiscRatio.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txtDiscRatio.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtDiscRatio.Properties.Appearance.FontSizeDelta")));
            this.txtDiscRatio.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtDiscRatio.Properties.Appearance.FontStyleDelta")));
            this.txtDiscRatio.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtDiscRatio.Properties.Appearance.GradientMode")));
            this.txtDiscRatio.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtDiscRatio.Properties.Appearance.Image")));
            this.txtDiscRatio.Properties.Appearance.Options.UseTextOptions = true;
            this.txtDiscRatio.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtDiscRatio.Properties.AutoHeight = ((bool)(resources.GetObject("txtDiscRatio.Properties.AutoHeight")));
            this.txtDiscRatio.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtDiscRatio.Properties.Mask.AutoComplete")));
            this.txtDiscRatio.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtDiscRatio.Properties.Mask.BeepOnError")));
            this.txtDiscRatio.Properties.Mask.EditMask = resources.GetString("txtDiscRatio.Properties.Mask.EditMask");
            this.txtDiscRatio.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtDiscRatio.Properties.Mask.IgnoreMaskBlank")));
            this.txtDiscRatio.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtDiscRatio.Properties.Mask.MaskType")));
            this.txtDiscRatio.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtDiscRatio.Properties.Mask.PlaceHolder")));
            this.txtDiscRatio.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtDiscRatio.Properties.Mask.SaveLiteral")));
            this.txtDiscRatio.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtDiscRatio.Properties.Mask.ShowPlaceHolders")));
            this.txtDiscRatio.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtDiscRatio.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtDiscRatio.Properties.NullText = resources.GetString("txtDiscRatio.Properties.NullText");
            this.txtDiscRatio.Properties.NullValuePrompt = resources.GetString("txtDiscRatio.Properties.NullValuePrompt");
            this.txtDiscRatio.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtDiscRatio.Properties.NullValuePromptShowForEmptyValue")));
            this.txtDiscRatio.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtOpenDebit_Spin);
            this.txtDiscRatio.Modified += new System.EventHandler(this.txtVenCode_Modified);
            this.txtDiscRatio.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtMaxCredit_KeyPress);
            // 
            // labelControl8
            // 
            resources.ApplyResources(this.labelControl8, "labelControl8");
            this.labelControl8.Name = "labelControl8";
            // 
            // labelControl19
            // 
            resources.ApplyResources(this.labelControl19, "labelControl19");
            this.labelControl19.Name = "labelControl19";
            // 
            // lkpPriceLevel
            // 
            resources.ApplyResources(this.lkpPriceLevel, "lkpPriceLevel");
            this.lkpPriceLevel.EnterMoveNextControl = true;
            this.lkpPriceLevel.Name = "lkpPriceLevel";
            this.lkpPriceLevel.Properties.AccessibleDescription = resources.GetString("lkpPriceLevel.Properties.AccessibleDescription");
            this.lkpPriceLevel.Properties.AccessibleName = resources.GetString("lkpPriceLevel.Properties.AccessibleName");
            this.lkpPriceLevel.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkpPriceLevel.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkpPriceLevel.Properties.Appearance.FontSizeDelta")));
            this.lkpPriceLevel.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpPriceLevel.Properties.Appearance.FontStyleDelta")));
            this.lkpPriceLevel.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpPriceLevel.Properties.Appearance.GradientMode")));
            this.lkpPriceLevel.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkpPriceLevel.Properties.Appearance.Image")));
            this.lkpPriceLevel.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpPriceLevel.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpPriceLevel.Properties.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.lkpPriceLevel.Properties.AppearanceDropDown.FontSizeDelta = ((int)(resources.GetObject("lkpPriceLevel.Properties.AppearanceDropDown.FontSizeDelta")));
            this.lkpPriceLevel.Properties.AppearanceDropDown.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpPriceLevel.Properties.AppearanceDropDown.FontStyleDelta")));
            this.lkpPriceLevel.Properties.AppearanceDropDown.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpPriceLevel.Properties.AppearanceDropDown.GradientMode")));
            this.lkpPriceLevel.Properties.AppearanceDropDown.Image = ((System.Drawing.Image)(resources.GetObject("lkpPriceLevel.Properties.AppearanceDropDown.Image")));
            this.lkpPriceLevel.Properties.AppearanceDropDown.Options.UseTextOptions = true;
            this.lkpPriceLevel.Properties.AppearanceDropDown.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkpPriceLevel.Properties.AppearanceDropDownHeader.FontSizeDelta = ((int)(resources.GetObject("lkpPriceLevel.Properties.AppearanceDropDownHeader.FontSizeDelta")));
            this.lkpPriceLevel.Properties.AppearanceDropDownHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpPriceLevel.Properties.AppearanceDropDownHeader.FontStyleDelta")));
            this.lkpPriceLevel.Properties.AppearanceDropDownHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpPriceLevel.Properties.AppearanceDropDownHeader.GradientMode")));
            this.lkpPriceLevel.Properties.AppearanceDropDownHeader.Image = ((System.Drawing.Image)(resources.GetObject("lkpPriceLevel.Properties.AppearanceDropDownHeader.Image")));
            this.lkpPriceLevel.Properties.AppearanceDropDownHeader.Options.UseTextOptions = true;
            this.lkpPriceLevel.Properties.AppearanceDropDownHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkpPriceLevel.Properties.AutoHeight = ((bool)(resources.GetObject("lkpPriceLevel.Properties.AutoHeight")));
            this.lkpPriceLevel.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkpPriceLevel.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpPriceLevel.Properties.Buttons"))))});
            this.lkpPriceLevel.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpPriceLevel.Properties.Columns"), resources.GetString("lkpPriceLevel.Properties.Columns1"), ((int)(resources.GetObject("lkpPriceLevel.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpPriceLevel.Properties.Columns3"))), resources.GetString("lkpPriceLevel.Properties.Columns4"), ((bool)(resources.GetObject("lkpPriceLevel.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpPriceLevel.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpPriceLevel.Properties.Columns7"), resources.GetString("lkpPriceLevel.Properties.Columns8"), ((int)(resources.GetObject("lkpPriceLevel.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpPriceLevel.Properties.Columns10"))), resources.GetString("lkpPriceLevel.Properties.Columns11"), ((bool)(resources.GetObject("lkpPriceLevel.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpPriceLevel.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpPriceLevel.Properties.Columns14"), resources.GetString("lkpPriceLevel.Properties.Columns15"), ((int)(resources.GetObject("lkpPriceLevel.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpPriceLevel.Properties.Columns17"))), resources.GetString("lkpPriceLevel.Properties.Columns18"), ((bool)(resources.GetObject("lkpPriceLevel.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpPriceLevel.Properties.Columns20")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpPriceLevel.Properties.Columns21"), resources.GetString("lkpPriceLevel.Properties.Columns22"), ((int)(resources.GetObject("lkpPriceLevel.Properties.Columns23"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpPriceLevel.Properties.Columns24"))), resources.GetString("lkpPriceLevel.Properties.Columns25"), ((bool)(resources.GetObject("lkpPriceLevel.Properties.Columns26"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpPriceLevel.Properties.Columns27"))))});
            this.lkpPriceLevel.Properties.DisplayMember = "PLNAme";
            this.lkpPriceLevel.Properties.NullText = resources.GetString("lkpPriceLevel.Properties.NullText");
            this.lkpPriceLevel.Properties.NullValuePrompt = resources.GetString("lkpPriceLevel.Properties.NullValuePrompt");
            this.lkpPriceLevel.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkpPriceLevel.Properties.NullValuePromptShowForEmptyValue")));
            this.lkpPriceLevel.Properties.PopupSizeable = false;
            this.lkpPriceLevel.Properties.ValueMember = "PriceLevelId";
            this.lkpPriceLevel.Modified += new System.EventHandler(this.txtVenCode_Modified);
            // 
            // labelControl6
            // 
            resources.ApplyResources(this.labelControl6, "labelControl6");
            this.labelControl6.Name = "labelControl6";
            // 
            // txtCity
            // 
            resources.ApplyResources(this.txtCity, "txtCity");
            this.txtCity.EnterMoveNextControl = true;
            this.txtCity.Name = "txtCity";
            this.txtCity.Properties.AccessibleDescription = resources.GetString("txtCity.Properties.AccessibleDescription");
            this.txtCity.Properties.AccessibleName = resources.GetString("txtCity.Properties.AccessibleName");
            this.txtCity.Properties.AutoHeight = ((bool)(resources.GetObject("txtCity.Properties.AutoHeight")));
            this.txtCity.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtCity.Properties.Mask.AutoComplete")));
            this.txtCity.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtCity.Properties.Mask.BeepOnError")));
            this.txtCity.Properties.Mask.EditMask = resources.GetString("txtCity.Properties.Mask.EditMask");
            this.txtCity.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtCity.Properties.Mask.IgnoreMaskBlank")));
            this.txtCity.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtCity.Properties.Mask.MaskType")));
            this.txtCity.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtCity.Properties.Mask.PlaceHolder")));
            this.txtCity.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtCity.Properties.Mask.SaveLiteral")));
            this.txtCity.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtCity.Properties.Mask.ShowPlaceHolders")));
            this.txtCity.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtCity.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtCity.Properties.MaxLength = 50;
            this.txtCity.Properties.NullValuePrompt = resources.GetString("txtCity.Properties.NullValuePrompt");
            this.txtCity.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtCity.Properties.NullValuePromptShowForEmptyValue")));
            this.txtCity.Modified += new System.EventHandler(this.txtVenCode_Modified);
            // 
            // labelControl9
            // 
            resources.ApplyResources(this.labelControl9, "labelControl9");
            this.labelControl9.Name = "labelControl9";
            // 
            // txtZip
            // 
            resources.ApplyResources(this.txtZip, "txtZip");
            this.txtZip.EnterMoveNextControl = true;
            this.txtZip.Name = "txtZip";
            this.txtZip.Properties.AccessibleDescription = resources.GetString("txtZip.Properties.AccessibleDescription");
            this.txtZip.Properties.AccessibleName = resources.GetString("txtZip.Properties.AccessibleName");
            this.txtZip.Properties.AutoHeight = ((bool)(resources.GetObject("txtZip.Properties.AutoHeight")));
            this.txtZip.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtZip.Properties.Mask.AutoComplete")));
            this.txtZip.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtZip.Properties.Mask.BeepOnError")));
            this.txtZip.Properties.Mask.EditMask = resources.GetString("txtZip.Properties.Mask.EditMask");
            this.txtZip.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtZip.Properties.Mask.IgnoreMaskBlank")));
            this.txtZip.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtZip.Properties.Mask.MaskType")));
            this.txtZip.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtZip.Properties.Mask.PlaceHolder")));
            this.txtZip.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtZip.Properties.Mask.SaveLiteral")));
            this.txtZip.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtZip.Properties.Mask.ShowPlaceHolders")));
            this.txtZip.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtZip.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtZip.Properties.MaxLength = 50;
            this.txtZip.Properties.NullValuePrompt = resources.GetString("txtZip.Properties.NullValuePrompt");
            this.txtZip.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtZip.Properties.NullValuePromptShowForEmptyValue")));
            this.txtZip.Modified += new System.EventHandler(this.txtVenCode_Modified);
            // 
            // labelControl10
            // 
            resources.ApplyResources(this.labelControl10, "labelControl10");
            this.labelControl10.Name = "labelControl10";
            // 
            // txtFax
            // 
            resources.ApplyResources(this.txtFax, "txtFax");
            this.txtFax.EnterMoveNextControl = true;
            this.txtFax.Name = "txtFax";
            this.txtFax.Properties.AccessibleDescription = resources.GetString("txtFax.Properties.AccessibleDescription");
            this.txtFax.Properties.AccessibleName = resources.GetString("txtFax.Properties.AccessibleName");
            this.txtFax.Properties.AutoHeight = ((bool)(resources.GetObject("txtFax.Properties.AutoHeight")));
            this.txtFax.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtFax.Properties.Mask.AutoComplete")));
            this.txtFax.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtFax.Properties.Mask.BeepOnError")));
            this.txtFax.Properties.Mask.EditMask = resources.GetString("txtFax.Properties.Mask.EditMask");
            this.txtFax.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtFax.Properties.Mask.IgnoreMaskBlank")));
            this.txtFax.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtFax.Properties.Mask.MaskType")));
            this.txtFax.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtFax.Properties.Mask.PlaceHolder")));
            this.txtFax.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtFax.Properties.Mask.SaveLiteral")));
            this.txtFax.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtFax.Properties.Mask.ShowPlaceHolders")));
            this.txtFax.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtFax.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtFax.Properties.MaxLength = 50;
            this.txtFax.Properties.NullValuePrompt = resources.GetString("txtFax.Properties.NullValuePrompt");
            this.txtFax.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtFax.Properties.NullValuePromptShowForEmptyValue")));
            this.txtFax.Modified += new System.EventHandler(this.txtVenCode_Modified);
            // 
            // labelControl11
            // 
            resources.ApplyResources(this.labelControl11, "labelControl11");
            this.labelControl11.Name = "labelControl11";
            // 
            // txtEmail
            // 
            resources.ApplyResources(this.txtEmail, "txtEmail");
            this.txtEmail.EnterMoveNextControl = true;
            this.txtEmail.Name = "txtEmail";
            this.txtEmail.Properties.AccessibleDescription = resources.GetString("txtEmail.Properties.AccessibleDescription");
            this.txtEmail.Properties.AccessibleName = resources.GetString("txtEmail.Properties.AccessibleName");
            this.txtEmail.Properties.AutoHeight = ((bool)(resources.GetObject("txtEmail.Properties.AutoHeight")));
            this.txtEmail.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtEmail.Properties.Mask.AutoComplete")));
            this.txtEmail.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtEmail.Properties.Mask.BeepOnError")));
            this.txtEmail.Properties.Mask.EditMask = resources.GetString("txtEmail.Properties.Mask.EditMask");
            this.txtEmail.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtEmail.Properties.Mask.IgnoreMaskBlank")));
            this.txtEmail.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtEmail.Properties.Mask.MaskType")));
            this.txtEmail.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtEmail.Properties.Mask.PlaceHolder")));
            this.txtEmail.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtEmail.Properties.Mask.SaveLiteral")));
            this.txtEmail.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtEmail.Properties.Mask.ShowPlaceHolders")));
            this.txtEmail.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtEmail.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtEmail.Properties.MaxLength = 175;
            this.txtEmail.Properties.NullValuePrompt = resources.GetString("txtEmail.Properties.NullValuePrompt");
            this.txtEmail.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtEmail.Properties.NullValuePromptShowForEmptyValue")));
            this.txtEmail.Modified += new System.EventHandler(this.txtVenCode_Modified);
            // 
            // labelControl12
            // 
            resources.ApplyResources(this.labelControl12, "labelControl12");
            this.labelControl12.Name = "labelControl12";
            // 
            // labelControl13
            // 
            resources.ApplyResources(this.labelControl13, "labelControl13");
            this.labelControl13.Name = "labelControl13";
            // 
            // txtManagerName
            // 
            resources.ApplyResources(this.txtManagerName, "txtManagerName");
            this.txtManagerName.EnterMoveNextControl = true;
            this.txtManagerName.Name = "txtManagerName";
            this.txtManagerName.Properties.AccessibleDescription = resources.GetString("txtManagerName.Properties.AccessibleDescription");
            this.txtManagerName.Properties.AccessibleName = resources.GetString("txtManagerName.Properties.AccessibleName");
            this.txtManagerName.Properties.AutoHeight = ((bool)(resources.GetObject("txtManagerName.Properties.AutoHeight")));
            this.txtManagerName.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtManagerName.Properties.Mask.AutoComplete")));
            this.txtManagerName.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtManagerName.Properties.Mask.BeepOnError")));
            this.txtManagerName.Properties.Mask.EditMask = resources.GetString("txtManagerName.Properties.Mask.EditMask");
            this.txtManagerName.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtManagerName.Properties.Mask.IgnoreMaskBlank")));
            this.txtManagerName.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtManagerName.Properties.Mask.MaskType")));
            this.txtManagerName.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtManagerName.Properties.Mask.PlaceHolder")));
            this.txtManagerName.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtManagerName.Properties.Mask.SaveLiteral")));
            this.txtManagerName.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtManagerName.Properties.Mask.ShowPlaceHolders")));
            this.txtManagerName.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtManagerName.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtManagerName.Properties.MaxLength = 50;
            this.txtManagerName.Properties.NullValuePrompt = resources.GetString("txtManagerName.Properties.NullValuePrompt");
            this.txtManagerName.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtManagerName.Properties.NullValuePromptShowForEmptyValue")));
            this.txtManagerName.Modified += new System.EventHandler(this.txtVenCode_Modified);
            // 
            // labelControl14
            // 
            resources.ApplyResources(this.labelControl14, "labelControl14");
            this.labelControl14.Name = "labelControl14";
            // 
            // labelControl16
            // 
            resources.ApplyResources(this.labelControl16, "labelControl16");
            this.labelControl16.Name = "labelControl16";
            // 
            // lkp_SalesEmp
            // 
            resources.ApplyResources(this.lkp_SalesEmp, "lkp_SalesEmp");
            this.lkp_SalesEmp.EnterMoveNextControl = true;
            this.lkp_SalesEmp.MenuManager = this.barManager1;
            this.lkp_SalesEmp.Name = "lkp_SalesEmp";
            this.lkp_SalesEmp.Properties.AccessibleDescription = resources.GetString("lkp_SalesEmp.Properties.AccessibleDescription");
            this.lkp_SalesEmp.Properties.AccessibleName = resources.GetString("lkp_SalesEmp.Properties.AccessibleName");
            this.lkp_SalesEmp.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_SalesEmp.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_SalesEmp.Properties.Appearance.FontSizeDelta")));
            this.lkp_SalesEmp.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_SalesEmp.Properties.Appearance.FontStyleDelta")));
            this.lkp_SalesEmp.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_SalesEmp.Properties.Appearance.GradientMode")));
            this.lkp_SalesEmp.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_SalesEmp.Properties.Appearance.Image")));
            this.lkp_SalesEmp.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_SalesEmp.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_SalesEmp.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_SalesEmp.Properties.AutoHeight")));
            this.lkp_SalesEmp.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_SalesEmp.Properties.Buttons"))))});
            this.lkp_SalesEmp.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_SalesEmp.Properties.Columns"), resources.GetString("lkp_SalesEmp.Properties.Columns1"), ((int)(resources.GetObject("lkp_SalesEmp.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_SalesEmp.Properties.Columns3"))), resources.GetString("lkp_SalesEmp.Properties.Columns4"), ((bool)(resources.GetObject("lkp_SalesEmp.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_SalesEmp.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_SalesEmp.Properties.Columns7"), resources.GetString("lkp_SalesEmp.Properties.Columns8"), ((int)(resources.GetObject("lkp_SalesEmp.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_SalesEmp.Properties.Columns10"))), resources.GetString("lkp_SalesEmp.Properties.Columns11"), ((bool)(resources.GetObject("lkp_SalesEmp.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_SalesEmp.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_SalesEmp.Properties.Columns14"), resources.GetString("lkp_SalesEmp.Properties.Columns15"), ((int)(resources.GetObject("lkp_SalesEmp.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_SalesEmp.Properties.Columns17"))), resources.GetString("lkp_SalesEmp.Properties.Columns18"), ((bool)(resources.GetObject("lkp_SalesEmp.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_SalesEmp.Properties.Columns20"))))});
            this.lkp_SalesEmp.Properties.DisplayMember = "EmpName";
            this.lkp_SalesEmp.Properties.NullText = resources.GetString("lkp_SalesEmp.Properties.NullText");
            this.lkp_SalesEmp.Properties.NullValuePrompt = resources.GetString("lkp_SalesEmp.Properties.NullValuePrompt");
            this.lkp_SalesEmp.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_SalesEmp.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_SalesEmp.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.lkp_SalesEmp.Properties.ValueMember = "AccountId";
            this.lkp_SalesEmp.Modified += new System.EventHandler(this.txtVenCode_Modified);
            // 
            // labelControl25
            // 
            resources.ApplyResources(this.labelControl25, "labelControl25");
            this.labelControl25.Name = "labelControl25";
            // 
            // txt_DueDays
            // 
            resources.ApplyResources(this.txt_DueDays, "txt_DueDays");
            this.txt_DueDays.EnterMoveNextControl = true;
            this.txt_DueDays.Name = "txt_DueDays";
            this.txt_DueDays.Properties.AccessibleDescription = resources.GetString("txt_DueDays.Properties.AccessibleDescription");
            this.txt_DueDays.Properties.AccessibleName = resources.GetString("txt_DueDays.Properties.AccessibleName");
            this.txt_DueDays.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txt_DueDays.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txt_DueDays.Properties.Appearance.FontSizeDelta")));
            this.txt_DueDays.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txt_DueDays.Properties.Appearance.FontStyleDelta")));
            this.txt_DueDays.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txt_DueDays.Properties.Appearance.GradientMode")));
            this.txt_DueDays.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txt_DueDays.Properties.Appearance.Image")));
            this.txt_DueDays.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_DueDays.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_DueDays.Properties.AutoHeight = ((bool)(resources.GetObject("txt_DueDays.Properties.AutoHeight")));
            resources.ApplyResources(serializableAppearanceObject1, "serializableAppearanceObject1");
            this.txt_DueDays.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("txt_DueDays.Properties.Buttons"))), resources.GetString("txt_DueDays.Properties.Buttons1"), ((int)(resources.GetObject("txt_DueDays.Properties.Buttons2"))), ((bool)(resources.GetObject("txt_DueDays.Properties.Buttons3"))), ((bool)(resources.GetObject("txt_DueDays.Properties.Buttons4"))), ((bool)(resources.GetObject("txt_DueDays.Properties.Buttons5"))), ((DevExpress.XtraEditors.ImageLocation)(resources.GetObject("txt_DueDays.Properties.Buttons6"))), ((System.Drawing.Image)(resources.GetObject("txt_DueDays.Properties.Buttons7"))), new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject1, resources.GetString("txt_DueDays.Properties.Buttons8"), ((object)(resources.GetObject("txt_DueDays.Properties.Buttons9"))), ((DevExpress.Utils.SuperToolTip)(resources.GetObject("txt_DueDays.Properties.Buttons10"))), ((bool)(resources.GetObject("txt_DueDays.Properties.Buttons11"))))});
            this.txt_DueDays.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_DueDays.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_DueDays.Properties.Mask.AutoComplete")));
            this.txt_DueDays.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_DueDays.Properties.Mask.BeepOnError")));
            this.txt_DueDays.Properties.Mask.EditMask = resources.GetString("txt_DueDays.Properties.Mask.EditMask");
            this.txt_DueDays.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_DueDays.Properties.Mask.IgnoreMaskBlank")));
            this.txt_DueDays.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_DueDays.Properties.Mask.MaskType")));
            this.txt_DueDays.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_DueDays.Properties.Mask.PlaceHolder")));
            this.txt_DueDays.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_DueDays.Properties.Mask.SaveLiteral")));
            this.txt_DueDays.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_DueDays.Properties.Mask.ShowPlaceHolders")));
            this.txt_DueDays.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_DueDays.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_DueDays.Properties.NullText = resources.GetString("txt_DueDays.Properties.NullText");
            this.txt_DueDays.Properties.NullValuePrompt = resources.GetString("txt_DueDays.Properties.NullValuePrompt");
            this.txt_DueDays.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_DueDays.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_DueDays.Modified += new System.EventHandler(this.txtVenCode_Modified);
            this.txt_DueDays.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDays_KeyPress);
            // 
            // labelControl17
            // 
            resources.ApplyResources(this.labelControl17, "labelControl17");
            this.labelControl17.Name = "labelControl17";
            // 
            // txt_Representative
            // 
            resources.ApplyResources(this.txt_Representative, "txt_Representative");
            this.txt_Representative.EnterMoveNextControl = true;
            this.txt_Representative.Name = "txt_Representative";
            this.txt_Representative.Properties.AccessibleDescription = resources.GetString("txt_Representative.Properties.AccessibleDescription");
            this.txt_Representative.Properties.AccessibleName = resources.GetString("txt_Representative.Properties.AccessibleName");
            this.txt_Representative.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Representative.Properties.AutoHeight")));
            this.txt_Representative.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_Representative.Properties.Mask.AutoComplete")));
            this.txt_Representative.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_Representative.Properties.Mask.BeepOnError")));
            this.txt_Representative.Properties.Mask.EditMask = resources.GetString("txt_Representative.Properties.Mask.EditMask");
            this.txt_Representative.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Representative.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Representative.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Representative.Properties.Mask.MaskType")));
            this.txt_Representative.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_Representative.Properties.Mask.PlaceHolder")));
            this.txt_Representative.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Representative.Properties.Mask.SaveLiteral")));
            this.txt_Representative.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Representative.Properties.Mask.ShowPlaceHolders")));
            this.txt_Representative.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_Representative.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_Representative.Properties.MaxLength = 50;
            this.txt_Representative.Properties.NullValuePrompt = resources.GetString("txt_Representative.Properties.NullValuePrompt");
            this.txt_Representative.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_Representative.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_Representative.Modified += new System.EventHandler(this.txtVenCode_Modified);
            // 
            // labelControl18
            // 
            resources.ApplyResources(this.labelControl18, "labelControl18");
            this.labelControl18.Name = "labelControl18";
            // 
            // txt_RepresentativeJob
            // 
            resources.ApplyResources(this.txt_RepresentativeJob, "txt_RepresentativeJob");
            this.txt_RepresentativeJob.EnterMoveNextControl = true;
            this.txt_RepresentativeJob.Name = "txt_RepresentativeJob";
            this.txt_RepresentativeJob.Properties.AccessibleDescription = resources.GetString("txt_RepresentativeJob.Properties.AccessibleDescription");
            this.txt_RepresentativeJob.Properties.AccessibleName = resources.GetString("txt_RepresentativeJob.Properties.AccessibleName");
            this.txt_RepresentativeJob.Properties.AutoHeight = ((bool)(resources.GetObject("txt_RepresentativeJob.Properties.AutoHeight")));
            this.txt_RepresentativeJob.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_RepresentativeJob.Properties.Mask.AutoComplete")));
            this.txt_RepresentativeJob.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_RepresentativeJob.Properties.Mask.BeepOnError")));
            this.txt_RepresentativeJob.Properties.Mask.EditMask = resources.GetString("txt_RepresentativeJob.Properties.Mask.EditMask");
            this.txt_RepresentativeJob.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_RepresentativeJob.Properties.Mask.IgnoreMaskBlank")));
            this.txt_RepresentativeJob.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_RepresentativeJob.Properties.Mask.MaskType")));
            this.txt_RepresentativeJob.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_RepresentativeJob.Properties.Mask.PlaceHolder")));
            this.txt_RepresentativeJob.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_RepresentativeJob.Properties.Mask.SaveLiteral")));
            this.txt_RepresentativeJob.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_RepresentativeJob.Properties.Mask.ShowPlaceHolders")));
            this.txt_RepresentativeJob.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_RepresentativeJob.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_RepresentativeJob.Properties.MaxLength = 50;
            this.txt_RepresentativeJob.Properties.NullValuePrompt = resources.GetString("txt_RepresentativeJob.Properties.NullValuePrompt");
            this.txt_RepresentativeJob.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_RepresentativeJob.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_RepresentativeJob.Modified += new System.EventHandler(this.txtVenCode_Modified);
            // 
            // txtRepFName
            // 
            resources.ApplyResources(this.txtRepFName, "txtRepFName");
            this.txtRepFName.EnterMoveNextControl = true;
            this.txtRepFName.Name = "txtRepFName";
            this.txtRepFName.Properties.AccessibleDescription = resources.GetString("txtRepFName.Properties.AccessibleDescription");
            this.txtRepFName.Properties.AccessibleName = resources.GetString("txtRepFName.Properties.AccessibleName");
            this.txtRepFName.Properties.AutoHeight = ((bool)(resources.GetObject("txtRepFName.Properties.AutoHeight")));
            this.txtRepFName.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtRepFName.Properties.Mask.AutoComplete")));
            this.txtRepFName.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtRepFName.Properties.Mask.BeepOnError")));
            this.txtRepFName.Properties.Mask.EditMask = resources.GetString("txtRepFName.Properties.Mask.EditMask");
            this.txtRepFName.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtRepFName.Properties.Mask.IgnoreMaskBlank")));
            this.txtRepFName.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtRepFName.Properties.Mask.MaskType")));
            this.txtRepFName.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtRepFName.Properties.Mask.PlaceHolder")));
            this.txtRepFName.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtRepFName.Properties.Mask.SaveLiteral")));
            this.txtRepFName.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtRepFName.Properties.Mask.ShowPlaceHolders")));
            this.txtRepFName.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtRepFName.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtRepFName.Properties.MaxLength = 50;
            this.txtRepFName.Properties.NullValuePrompt = resources.GetString("txtRepFName.Properties.NullValuePrompt");
            this.txtRepFName.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtRepFName.Properties.NullValuePromptShowForEmptyValue")));
            this.txtRepFName.Modified += new System.EventHandler(this.txtVenCode_Modified);
            // 
            // labelControl20
            // 
            resources.ApplyResources(this.labelControl20, "labelControl20");
            this.labelControl20.Name = "labelControl20";
            // 
            // labelControl21
            // 
            resources.ApplyResources(this.labelControl21, "labelControl21");
            this.labelControl21.Name = "labelControl21";
            // 
            // txt_RepFJob
            // 
            resources.ApplyResources(this.txt_RepFJob, "txt_RepFJob");
            this.txt_RepFJob.EnterMoveNextControl = true;
            this.txt_RepFJob.Name = "txt_RepFJob";
            this.txt_RepFJob.Properties.AccessibleDescription = resources.GetString("txt_RepFJob.Properties.AccessibleDescription");
            this.txt_RepFJob.Properties.AccessibleName = resources.GetString("txt_RepFJob.Properties.AccessibleName");
            this.txt_RepFJob.Properties.AutoHeight = ((bool)(resources.GetObject("txt_RepFJob.Properties.AutoHeight")));
            this.txt_RepFJob.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_RepFJob.Properties.Mask.AutoComplete")));
            this.txt_RepFJob.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_RepFJob.Properties.Mask.BeepOnError")));
            this.txt_RepFJob.Properties.Mask.EditMask = resources.GetString("txt_RepFJob.Properties.Mask.EditMask");
            this.txt_RepFJob.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_RepFJob.Properties.Mask.IgnoreMaskBlank")));
            this.txt_RepFJob.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_RepFJob.Properties.Mask.MaskType")));
            this.txt_RepFJob.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_RepFJob.Properties.Mask.PlaceHolder")));
            this.txt_RepFJob.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_RepFJob.Properties.Mask.SaveLiteral")));
            this.txt_RepFJob.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_RepFJob.Properties.Mask.ShowPlaceHolders")));
            this.txt_RepFJob.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_RepFJob.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_RepFJob.Properties.MaxLength = 50;
            this.txt_RepFJob.Properties.NullValuePrompt = resources.GetString("txt_RepFJob.Properties.NullValuePrompt");
            this.txt_RepFJob.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_RepFJob.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_RepFJob.Modified += new System.EventHandler(this.txtVenCode_Modified);
            // 
            // txtAddress
            // 
            resources.ApplyResources(this.txtAddress, "txtAddress");
            this.txtAddress.Name = "txtAddress";
            this.txtAddress.Properties.AccessibleDescription = resources.GetString("txtAddress.Properties.AccessibleDescription");
            this.txtAddress.Properties.AccessibleName = resources.GetString("txtAddress.Properties.AccessibleName");
            this.txtAddress.Properties.MaxLength = 175;
            this.txtAddress.Properties.NullValuePrompt = resources.GetString("txtAddress.Properties.NullValuePrompt");
            this.txtAddress.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtAddress.Properties.NullValuePromptShowForEmptyValue")));
            this.txtAddress.Modified += new System.EventHandler(this.txtVenCode_Modified);
            // 
            // txtShipping
            // 
            resources.ApplyResources(this.txtShipping, "txtShipping");
            this.txtShipping.Name = "txtShipping";
            this.txtShipping.Properties.AccessibleDescription = resources.GetString("txtShipping.Properties.AccessibleDescription");
            this.txtShipping.Properties.AccessibleName = resources.GetString("txtShipping.Properties.AccessibleName");
            this.txtShipping.Properties.MaxLength = 175;
            this.txtShipping.Properties.NullValuePrompt = resources.GetString("txtShipping.Properties.NullValuePrompt");
            this.txtShipping.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtShipping.Properties.NullValuePromptShowForEmptyValue")));
            this.txtShipping.Modified += new System.EventHandler(this.txtVenCode_Modified);
            // 
            // xtraTabControl1
            // 
            resources.ApplyResources(this.xtraTabControl1, "xtraTabControl1");
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.tab_main;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.tab_main,
            this.tab_docs});
            // 
            // tab_main
            // 
            resources.ApplyResources(this.tab_main, "tab_main");
            this.tab_main.Controls.Add(this.labelControl50);
            this.tab_main.Controls.Add(this.labelControl48);
            this.tab_main.Controls.Add(this.labelControl47);
            this.tab_main.Controls.Add(this.labelControl46);
            this.tab_main.Controls.Add(this.labelControl45);
            this.tab_main.Controls.Add(this.labelControl44);
            this.tab_main.Controls.Add(this.labelControl43);
            this.tab_main.Controls.Add(this.labelControl42);
            this.tab_main.Controls.Add(this.labelControl41);
            this.tab_main.Controls.Add(this.lkp_country);
            this.tab_main.Controls.Add(this.txt_Governate);
            this.tab_main.Controls.Add(this.Governate);
            this.tab_main.Controls.Add(this.txt_BuildingNumber);
            this.tab_main.Controls.Add(this.BuildingNumber);
            this.tab_main.Controls.Add(this.txt_Street);
            this.tab_main.Controls.Add(this.txt_Neighborhood);
            this.tab_main.Controls.Add(this.labelControl39);
            this.tab_main.Controls.Add(this.labelControl40);
            this.tab_main.Controls.Add(this.lkp_CollectEmp);
            this.tab_main.Controls.Add(this.labelControl38);
            this.tab_main.Controls.Add(this.btn_AddRegion);
            this.tab_main.Controls.Add(this.labelControl36);
            this.tab_main.Controls.Add(this.lkp_Regions);
            this.tab_main.Controls.Add(this.labelControl37);
            this.tab_main.Controls.Add(this.cmbCsType);
            this.tab_main.Controls.Add(this.labelControl35);
            this.tab_main.Controls.Add(this.lkpDelivery);
            this.tab_main.Controls.Add(this.txt_Rep_ID);
            this.tab_main.Controls.Add(this.labelControl33);
            this.tab_main.Controls.Add(this.txt_Rep_Phone);
            this.tab_main.Controls.Add(this.labelControl34);
            this.tab_main.Controls.Add(this.labelControl32);
            this.tab_main.Controls.Add(this.lkp_Group);
            this.tab_main.Controls.Add(this.txt_Bank);
            this.tab_main.Controls.Add(this.labelControl28);
            this.tab_main.Controls.Add(this.txt_BankAccNum);
            this.tab_main.Controls.Add(this.labelControl31);
            this.tab_main.Controls.Add(this.txt_IdNumber);
            this.tab_main.Controls.Add(this.labelControl26);
            this.tab_main.Controls.Add(this.pnlOpenBlnce);
            this.tab_main.Controls.Add(this.lblOpenBalance);
            this.tab_main.Controls.Add(this.labelControl24);
            this.tab_main.Controls.Add(this.lkp_Category);
            this.tab_main.Controls.Add(this.uc_LinkAccount1);
            this.tab_main.Controls.Add(this.txtCusCode);
            this.tab_main.Controls.Add(this.txtRepFName);
            this.tab_main.Controls.Add(this.txtShipping);
            this.tab_main.Controls.Add(this.labelControl20);
            this.tab_main.Controls.Add(this.txtAddress);
            this.tab_main.Controls.Add(this.lkp_SalesEmp);
            this.tab_main.Controls.Add(this.txt_DueDays);
            this.tab_main.Controls.Add(this.labelControl25);
            this.tab_main.Controls.Add(this.labelControl2);
            this.tab_main.Controls.Add(this.labelControl16);
            this.tab_main.Controls.Add(this.labelControl15);
            this.tab_main.Controls.Add(this.txt_RepFJob);
            this.tab_main.Controls.Add(this.txtTel);
            this.tab_main.Controls.Add(this.labelControl21);
            this.tab_main.Controls.Add(this.txtCusNameAr);
            this.tab_main.Controls.Add(this.txt_RepresentativeJob);
            this.tab_main.Controls.Add(this.labelControl4);
            this.tab_main.Controls.Add(this.labelControl18);
            this.tab_main.Controls.Add(this.txtCusNameEn);
            this.tab_main.Controls.Add(this.txt_Representative);
            this.tab_main.Controls.Add(this.labelControl3);
            this.tab_main.Controls.Add(this.labelControl17);
            this.tab_main.Controls.Add(this.labelControl1);
            this.tab_main.Controls.Add(this.txtManagerName);
            this.tab_main.Controls.Add(this.labelControl5);
            this.tab_main.Controls.Add(this.labelControl14);
            this.tab_main.Controls.Add(this.txtMobile);
            this.tab_main.Controls.Add(this.labelControl13);
            this.tab_main.Controls.Add(this.labelControl7);
            this.tab_main.Controls.Add(this.txtEmail);
            this.tab_main.Controls.Add(this.txtMaxCredit);
            this.tab_main.Controls.Add(this.labelControl12);
            this.tab_main.Controls.Add(this.txtZip);
            this.tab_main.Controls.Add(this.labelControl10);
            this.tab_main.Controls.Add(this.txtFax);
            this.tab_main.Controls.Add(this.labelControl8);
            this.tab_main.Controls.Add(this.labelControl11);
            this.tab_main.Controls.Add(this.txtDiscRatio);
            this.tab_main.Controls.Add(this.labelControl19);
            this.tab_main.Controls.Add(this.txtCity);
            this.tab_main.Controls.Add(this.labelControl6);
            this.tab_main.Controls.Add(this.labelControl9);
            this.tab_main.Controls.Add(this.lkpPriceLevel);
            this.tab_main.Name = "tab_main";
            // 
            // labelControl50
            // 
            resources.ApplyResources(this.labelControl50, "labelControl50");
            this.labelControl50.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl50.Appearance.FontSizeDelta")));
            this.labelControl50.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl50.Appearance.FontStyleDelta")));
            this.labelControl50.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("labelControl50.Appearance.ForeColor")));
            this.labelControl50.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl50.Appearance.GradientMode")));
            this.labelControl50.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl50.Appearance.Image")));
            this.labelControl50.Name = "labelControl50";
            // 
            // labelControl48
            // 
            resources.ApplyResources(this.labelControl48, "labelControl48");
            this.labelControl48.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl48.Appearance.FontSizeDelta")));
            this.labelControl48.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl48.Appearance.FontStyleDelta")));
            this.labelControl48.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("labelControl48.Appearance.ForeColor")));
            this.labelControl48.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl48.Appearance.GradientMode")));
            this.labelControl48.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl48.Appearance.Image")));
            this.labelControl48.Name = "labelControl48";
            // 
            // labelControl47
            // 
            resources.ApplyResources(this.labelControl47, "labelControl47");
            this.labelControl47.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl47.Appearance.FontSizeDelta")));
            this.labelControl47.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl47.Appearance.FontStyleDelta")));
            this.labelControl47.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("labelControl47.Appearance.ForeColor")));
            this.labelControl47.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl47.Appearance.GradientMode")));
            this.labelControl47.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl47.Appearance.Image")));
            this.labelControl47.Name = "labelControl47";
            // 
            // labelControl46
            // 
            resources.ApplyResources(this.labelControl46, "labelControl46");
            this.labelControl46.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl46.Appearance.FontSizeDelta")));
            this.labelControl46.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl46.Appearance.FontStyleDelta")));
            this.labelControl46.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("labelControl46.Appearance.ForeColor")));
            this.labelControl46.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl46.Appearance.GradientMode")));
            this.labelControl46.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl46.Appearance.Image")));
            this.labelControl46.Name = "labelControl46";
            // 
            // labelControl45
            // 
            resources.ApplyResources(this.labelControl45, "labelControl45");
            this.labelControl45.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl45.Appearance.FontSizeDelta")));
            this.labelControl45.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl45.Appearance.FontStyleDelta")));
            this.labelControl45.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("labelControl45.Appearance.ForeColor")));
            this.labelControl45.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl45.Appearance.GradientMode")));
            this.labelControl45.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl45.Appearance.Image")));
            this.labelControl45.Name = "labelControl45";
            // 
            // labelControl44
            // 
            resources.ApplyResources(this.labelControl44, "labelControl44");
            this.labelControl44.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl44.Appearance.FontSizeDelta")));
            this.labelControl44.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl44.Appearance.FontStyleDelta")));
            this.labelControl44.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("labelControl44.Appearance.ForeColor")));
            this.labelControl44.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl44.Appearance.GradientMode")));
            this.labelControl44.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl44.Appearance.Image")));
            this.labelControl44.Name = "labelControl44";
            // 
            // labelControl43
            // 
            resources.ApplyResources(this.labelControl43, "labelControl43");
            this.labelControl43.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl43.Appearance.FontSizeDelta")));
            this.labelControl43.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl43.Appearance.FontStyleDelta")));
            this.labelControl43.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("labelControl43.Appearance.ForeColor")));
            this.labelControl43.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl43.Appearance.GradientMode")));
            this.labelControl43.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl43.Appearance.Image")));
            this.labelControl43.Name = "labelControl43";
            // 
            // labelControl42
            // 
            resources.ApplyResources(this.labelControl42, "labelControl42");
            this.labelControl42.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl42.Appearance.FontSizeDelta")));
            this.labelControl42.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl42.Appearance.FontStyleDelta")));
            this.labelControl42.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("labelControl42.Appearance.ForeColor")));
            this.labelControl42.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl42.Appearance.GradientMode")));
            this.labelControl42.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl42.Appearance.Image")));
            this.labelControl42.Name = "labelControl42";
            // 
            // labelControl41
            // 
            resources.ApplyResources(this.labelControl41, "labelControl41");
            this.labelControl41.Name = "labelControl41";
            // 
            // lkp_country
            // 
            resources.ApplyResources(this.lkp_country, "lkp_country");
            this.lkp_country.EnterMoveNextControl = true;
            this.lkp_country.Name = "lkp_country";
            this.lkp_country.Properties.AccessibleDescription = resources.GetString("lkp_country.Properties.AccessibleDescription");
            this.lkp_country.Properties.AccessibleName = resources.GetString("lkp_country.Properties.AccessibleName");
            this.lkp_country.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkp_country.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_country.Properties.Appearance.FontSizeDelta")));
            this.lkp_country.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_country.Properties.Appearance.FontStyleDelta")));
            this.lkp_country.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_country.Properties.Appearance.GradientMode")));
            this.lkp_country.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_country.Properties.Appearance.Image")));
            this.lkp_country.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_country.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_country.Properties.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.lkp_country.Properties.AppearanceDropDown.FontSizeDelta = ((int)(resources.GetObject("lkp_country.Properties.AppearanceDropDown.FontSizeDelta")));
            this.lkp_country.Properties.AppearanceDropDown.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_country.Properties.AppearanceDropDown.FontStyleDelta")));
            this.lkp_country.Properties.AppearanceDropDown.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_country.Properties.AppearanceDropDown.GradientMode")));
            this.lkp_country.Properties.AppearanceDropDown.Image = ((System.Drawing.Image)(resources.GetObject("lkp_country.Properties.AppearanceDropDown.Image")));
            this.lkp_country.Properties.AppearanceDropDown.Options.UseTextOptions = true;
            this.lkp_country.Properties.AppearanceDropDown.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkp_country.Properties.AppearanceDropDownHeader.FontSizeDelta = ((int)(resources.GetObject("lkp_country.Properties.AppearanceDropDownHeader.FontSizeDelta")));
            this.lkp_country.Properties.AppearanceDropDownHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_country.Properties.AppearanceDropDownHeader.FontStyleDelta")));
            this.lkp_country.Properties.AppearanceDropDownHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_country.Properties.AppearanceDropDownHeader.GradientMode")));
            this.lkp_country.Properties.AppearanceDropDownHeader.Image = ((System.Drawing.Image)(resources.GetObject("lkp_country.Properties.AppearanceDropDownHeader.Image")));
            this.lkp_country.Properties.AppearanceDropDownHeader.Options.UseTextOptions = true;
            this.lkp_country.Properties.AppearanceDropDownHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkp_country.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_country.Properties.AutoHeight")));
            this.lkp_country.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_country.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_country.Properties.Buttons"))))});
            this.lkp_country.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_country.Properties.Columns"), resources.GetString("lkp_country.Properties.Columns1"), ((int)(resources.GetObject("lkp_country.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_country.Properties.Columns3"))), resources.GetString("lkp_country.Properties.Columns4"), ((bool)(resources.GetObject("lkp_country.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_country.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_country.Properties.Columns7"), resources.GetString("lkp_country.Properties.Columns8"))});
            this.lkp_country.Properties.DisplayMember = "CGNameAr";
            this.lkp_country.Properties.NullText = resources.GetString("lkp_country.Properties.NullText");
            this.lkp_country.Properties.NullValuePrompt = resources.GetString("lkp_country.Properties.NullValuePrompt");
            this.lkp_country.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_country.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_country.Properties.PopupSizeable = false;
            this.lkp_country.Properties.ValueMember = "CustomerGroupId";
            // 
            // txt_Governate
            // 
            resources.ApplyResources(this.txt_Governate, "txt_Governate");
            this.txt_Governate.EnterMoveNextControl = true;
            this.txt_Governate.Name = "txt_Governate";
            this.txt_Governate.Properties.AccessibleDescription = resources.GetString("txt_Governate.Properties.AccessibleDescription");
            this.txt_Governate.Properties.AccessibleName = resources.GetString("txt_Governate.Properties.AccessibleName");
            this.txt_Governate.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Governate.Properties.AutoHeight")));
            this.txt_Governate.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_Governate.Properties.Mask.AutoComplete")));
            this.txt_Governate.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_Governate.Properties.Mask.BeepOnError")));
            this.txt_Governate.Properties.Mask.EditMask = resources.GetString("txt_Governate.Properties.Mask.EditMask");
            this.txt_Governate.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Governate.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Governate.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Governate.Properties.Mask.MaskType")));
            this.txt_Governate.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_Governate.Properties.Mask.PlaceHolder")));
            this.txt_Governate.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Governate.Properties.Mask.SaveLiteral")));
            this.txt_Governate.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Governate.Properties.Mask.ShowPlaceHolders")));
            this.txt_Governate.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_Governate.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_Governate.Properties.MaxLength = 50;
            this.txt_Governate.Properties.NullValuePrompt = resources.GetString("txt_Governate.Properties.NullValuePrompt");
            this.txt_Governate.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_Governate.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // Governate
            // 
            resources.ApplyResources(this.Governate, "Governate");
            this.Governate.Name = "Governate";
            // 
            // txt_BuildingNumber
            // 
            resources.ApplyResources(this.txt_BuildingNumber, "txt_BuildingNumber");
            this.txt_BuildingNumber.EnterMoveNextControl = true;
            this.txt_BuildingNumber.Name = "txt_BuildingNumber";
            this.txt_BuildingNumber.Properties.AccessibleDescription = resources.GetString("txt_BuildingNumber.Properties.AccessibleDescription");
            this.txt_BuildingNumber.Properties.AccessibleName = resources.GetString("txt_BuildingNumber.Properties.AccessibleName");
            this.txt_BuildingNumber.Properties.AutoHeight = ((bool)(resources.GetObject("txt_BuildingNumber.Properties.AutoHeight")));
            this.txt_BuildingNumber.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_BuildingNumber.Properties.Mask.AutoComplete")));
            this.txt_BuildingNumber.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_BuildingNumber.Properties.Mask.BeepOnError")));
            this.txt_BuildingNumber.Properties.Mask.EditMask = resources.GetString("txt_BuildingNumber.Properties.Mask.EditMask");
            this.txt_BuildingNumber.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_BuildingNumber.Properties.Mask.IgnoreMaskBlank")));
            this.txt_BuildingNumber.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_BuildingNumber.Properties.Mask.MaskType")));
            this.txt_BuildingNumber.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_BuildingNumber.Properties.Mask.PlaceHolder")));
            this.txt_BuildingNumber.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_BuildingNumber.Properties.Mask.SaveLiteral")));
            this.txt_BuildingNumber.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_BuildingNumber.Properties.Mask.ShowPlaceHolders")));
            this.txt_BuildingNumber.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_BuildingNumber.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_BuildingNumber.Properties.MaxLength = 50;
            this.txt_BuildingNumber.Properties.NullValuePrompt = resources.GetString("txt_BuildingNumber.Properties.NullValuePrompt");
            this.txt_BuildingNumber.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_BuildingNumber.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // BuildingNumber
            // 
            resources.ApplyResources(this.BuildingNumber, "BuildingNumber");
            this.BuildingNumber.Name = "BuildingNumber";
            // 
            // txt_Street
            // 
            resources.ApplyResources(this.txt_Street, "txt_Street");
            this.txt_Street.EnterMoveNextControl = true;
            this.txt_Street.Name = "txt_Street";
            this.txt_Street.Properties.AccessibleDescription = resources.GetString("txt_Street.Properties.AccessibleDescription");
            this.txt_Street.Properties.AccessibleName = resources.GetString("txt_Street.Properties.AccessibleName");
            this.txt_Street.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Street.Properties.AutoHeight")));
            this.txt_Street.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_Street.Properties.Mask.AutoComplete")));
            this.txt_Street.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_Street.Properties.Mask.BeepOnError")));
            this.txt_Street.Properties.Mask.EditMask = resources.GetString("txt_Street.Properties.Mask.EditMask");
            this.txt_Street.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Street.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Street.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Street.Properties.Mask.MaskType")));
            this.txt_Street.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_Street.Properties.Mask.PlaceHolder")));
            this.txt_Street.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Street.Properties.Mask.SaveLiteral")));
            this.txt_Street.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Street.Properties.Mask.ShowPlaceHolders")));
            this.txt_Street.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_Street.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_Street.Properties.MaxLength = 50;
            this.txt_Street.Properties.NullValuePrompt = resources.GetString("txt_Street.Properties.NullValuePrompt");
            this.txt_Street.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_Street.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // txt_Neighborhood
            // 
            resources.ApplyResources(this.txt_Neighborhood, "txt_Neighborhood");
            this.txt_Neighborhood.EnterMoveNextControl = true;
            this.txt_Neighborhood.Name = "txt_Neighborhood";
            this.txt_Neighborhood.Properties.AccessibleDescription = resources.GetString("txt_Neighborhood.Properties.AccessibleDescription");
            this.txt_Neighborhood.Properties.AccessibleName = resources.GetString("txt_Neighborhood.Properties.AccessibleName");
            this.txt_Neighborhood.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Neighborhood.Properties.AutoHeight")));
            this.txt_Neighborhood.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_Neighborhood.Properties.Mask.AutoComplete")));
            this.txt_Neighborhood.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_Neighborhood.Properties.Mask.BeepOnError")));
            this.txt_Neighborhood.Properties.Mask.EditMask = resources.GetString("txt_Neighborhood.Properties.Mask.EditMask");
            this.txt_Neighborhood.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Neighborhood.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Neighborhood.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Neighborhood.Properties.Mask.MaskType")));
            this.txt_Neighborhood.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_Neighborhood.Properties.Mask.PlaceHolder")));
            this.txt_Neighborhood.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Neighborhood.Properties.Mask.SaveLiteral")));
            this.txt_Neighborhood.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Neighborhood.Properties.Mask.ShowPlaceHolders")));
            this.txt_Neighborhood.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_Neighborhood.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_Neighborhood.Properties.MaxLength = 50;
            this.txt_Neighborhood.Properties.NullValuePrompt = resources.GetString("txt_Neighborhood.Properties.NullValuePrompt");
            this.txt_Neighborhood.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_Neighborhood.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl39
            // 
            resources.ApplyResources(this.labelControl39, "labelControl39");
            this.labelControl39.Name = "labelControl39";
            this.labelControl39.Click += new System.EventHandler(this.labelControl39_Click);
            // 
            // labelControl40
            // 
            resources.ApplyResources(this.labelControl40, "labelControl40");
            this.labelControl40.Name = "labelControl40";
            this.labelControl40.Click += new System.EventHandler(this.labelControl40_Click);
            // 
            // lkp_CollectEmp
            // 
            resources.ApplyResources(this.lkp_CollectEmp, "lkp_CollectEmp");
            this.lkp_CollectEmp.EnterMoveNextControl = true;
            this.lkp_CollectEmp.MenuManager = this.barManager1;
            this.lkp_CollectEmp.Name = "lkp_CollectEmp";
            this.lkp_CollectEmp.Properties.AccessibleDescription = resources.GetString("lkp_CollectEmp.Properties.AccessibleDescription");
            this.lkp_CollectEmp.Properties.AccessibleName = resources.GetString("lkp_CollectEmp.Properties.AccessibleName");
            this.lkp_CollectEmp.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_CollectEmp.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_CollectEmp.Properties.Appearance.FontSizeDelta")));
            this.lkp_CollectEmp.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_CollectEmp.Properties.Appearance.FontStyleDelta")));
            this.lkp_CollectEmp.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_CollectEmp.Properties.Appearance.GradientMode")));
            this.lkp_CollectEmp.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_CollectEmp.Properties.Appearance.Image")));
            this.lkp_CollectEmp.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_CollectEmp.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_CollectEmp.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_CollectEmp.Properties.AutoHeight")));
            this.lkp_CollectEmp.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_CollectEmp.Properties.Buttons"))))});
            this.lkp_CollectEmp.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_CollectEmp.Properties.Columns"), resources.GetString("lkp_CollectEmp.Properties.Columns1"), ((int)(resources.GetObject("lkp_CollectEmp.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_CollectEmp.Properties.Columns3"))), resources.GetString("lkp_CollectEmp.Properties.Columns4"), ((bool)(resources.GetObject("lkp_CollectEmp.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_CollectEmp.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_CollectEmp.Properties.Columns7"), resources.GetString("lkp_CollectEmp.Properties.Columns8"), ((int)(resources.GetObject("lkp_CollectEmp.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_CollectEmp.Properties.Columns10"))), resources.GetString("lkp_CollectEmp.Properties.Columns11"), ((bool)(resources.GetObject("lkp_CollectEmp.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_CollectEmp.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_CollectEmp.Properties.Columns14"), resources.GetString("lkp_CollectEmp.Properties.Columns15"), ((int)(resources.GetObject("lkp_CollectEmp.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_CollectEmp.Properties.Columns17"))), resources.GetString("lkp_CollectEmp.Properties.Columns18"), ((bool)(resources.GetObject("lkp_CollectEmp.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_CollectEmp.Properties.Columns20"))))});
            this.lkp_CollectEmp.Properties.DisplayMember = "EmpName";
            this.lkp_CollectEmp.Properties.NullText = resources.GetString("lkp_CollectEmp.Properties.NullText");
            this.lkp_CollectEmp.Properties.NullValuePrompt = resources.GetString("lkp_CollectEmp.Properties.NullValuePrompt");
            this.lkp_CollectEmp.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_CollectEmp.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_CollectEmp.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.lkp_CollectEmp.Properties.ValueMember = "AccountId";
            // 
            // labelControl38
            // 
            resources.ApplyResources(this.labelControl38, "labelControl38");
            this.labelControl38.Name = "labelControl38";
            // 
            // btn_AddRegion
            // 
            resources.ApplyResources(this.btn_AddRegion, "btn_AddRegion");
            this.btn_AddRegion.Image = global::Pharmacy.Properties.Resources.add32;
            this.btn_AddRegion.Name = "btn_AddRegion";
            this.btn_AddRegion.Click += new System.EventHandler(this.btn_AddRegion_Click);
            // 
            // labelControl36
            // 
            resources.ApplyResources(this.labelControl36, "labelControl36");
            this.labelControl36.Name = "labelControl36";
            // 
            // lkp_Regions
            // 
            resources.ApplyResources(this.lkp_Regions, "lkp_Regions");
            this.lkp_Regions.EnterMoveNextControl = true;
            this.lkp_Regions.Name = "lkp_Regions";
            this.lkp_Regions.Properties.AccessibleDescription = resources.GetString("lkp_Regions.Properties.AccessibleDescription");
            this.lkp_Regions.Properties.AccessibleName = resources.GetString("lkp_Regions.Properties.AccessibleName");
            this.lkp_Regions.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkp_Regions.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_Regions.Properties.Appearance.FontSizeDelta")));
            this.lkp_Regions.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_Regions.Properties.Appearance.FontStyleDelta")));
            this.lkp_Regions.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_Regions.Properties.Appearance.GradientMode")));
            this.lkp_Regions.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_Regions.Properties.Appearance.Image")));
            this.lkp_Regions.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_Regions.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_Regions.Properties.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.lkp_Regions.Properties.AppearanceDropDown.FontSizeDelta = ((int)(resources.GetObject("lkp_Regions.Properties.AppearanceDropDown.FontSizeDelta")));
            this.lkp_Regions.Properties.AppearanceDropDown.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_Regions.Properties.AppearanceDropDown.FontStyleDelta")));
            this.lkp_Regions.Properties.AppearanceDropDown.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_Regions.Properties.AppearanceDropDown.GradientMode")));
            this.lkp_Regions.Properties.AppearanceDropDown.Image = ((System.Drawing.Image)(resources.GetObject("lkp_Regions.Properties.AppearanceDropDown.Image")));
            this.lkp_Regions.Properties.AppearanceDropDown.Options.UseTextOptions = true;
            this.lkp_Regions.Properties.AppearanceDropDown.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkp_Regions.Properties.AppearanceDropDownHeader.FontSizeDelta = ((int)(resources.GetObject("lkp_Regions.Properties.AppearanceDropDownHeader.FontSizeDelta")));
            this.lkp_Regions.Properties.AppearanceDropDownHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_Regions.Properties.AppearanceDropDownHeader.FontStyleDelta")));
            this.lkp_Regions.Properties.AppearanceDropDownHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_Regions.Properties.AppearanceDropDownHeader.GradientMode")));
            this.lkp_Regions.Properties.AppearanceDropDownHeader.Image = ((System.Drawing.Image)(resources.GetObject("lkp_Regions.Properties.AppearanceDropDownHeader.Image")));
            this.lkp_Regions.Properties.AppearanceDropDownHeader.Options.UseTextOptions = true;
            this.lkp_Regions.Properties.AppearanceDropDownHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkp_Regions.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_Regions.Properties.AutoHeight")));
            this.lkp_Regions.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_Regions.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_Regions.Properties.Buttons"))))});
            this.lkp_Regions.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Regions.Properties.Columns"), resources.GetString("lkp_Regions.Properties.Columns1")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Regions.Properties.Columns2"), resources.GetString("lkp_Regions.Properties.Columns3")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Regions.Properties.Columns4"), resources.GetString("lkp_Regions.Properties.Columns5"), ((int)(resources.GetObject("lkp_Regions.Properties.Columns6"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_Regions.Properties.Columns7"))), resources.GetString("lkp_Regions.Properties.Columns8"), ((bool)(resources.GetObject("lkp_Regions.Properties.Columns9"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_Regions.Properties.Columns10"))))});
            this.lkp_Regions.Properties.DisplayMember = "CGNameAr";
            this.lkp_Regions.Properties.NullText = resources.GetString("lkp_Regions.Properties.NullText");
            this.lkp_Regions.Properties.NullValuePrompt = resources.GetString("lkp_Regions.Properties.NullValuePrompt");
            this.lkp_Regions.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_Regions.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_Regions.Properties.PopupSizeable = false;
            this.lkp_Regions.Properties.ValueMember = "CustomerGroupId";
            // 
            // labelControl37
            // 
            resources.ApplyResources(this.labelControl37, "labelControl37");
            this.labelControl37.Name = "labelControl37";
            // 
            // cmbCsType
            // 
            resources.ApplyResources(this.cmbCsType, "cmbCsType");
            this.cmbCsType.EnterMoveNextControl = true;
            this.cmbCsType.MenuManager = this.barManager1;
            this.cmbCsType.Name = "cmbCsType";
            this.cmbCsType.Properties.AccessibleDescription = resources.GetString("cmbCsType.Properties.AccessibleDescription");
            this.cmbCsType.Properties.AccessibleName = resources.GetString("cmbCsType.Properties.AccessibleName");
            this.cmbCsType.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.cmbCsType.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("cmbCsType.Properties.Appearance.FontSizeDelta")));
            this.cmbCsType.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("cmbCsType.Properties.Appearance.FontStyleDelta")));
            this.cmbCsType.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("cmbCsType.Properties.Appearance.GradientMode")));
            this.cmbCsType.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("cmbCsType.Properties.Appearance.Image")));
            this.cmbCsType.Properties.Appearance.Options.UseTextOptions = true;
            this.cmbCsType.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.cmbCsType.Properties.AutoHeight = ((bool)(resources.GetObject("cmbCsType.Properties.AutoHeight")));
            this.cmbCsType.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("cmbCsType.Properties.Buttons"))))});
            this.cmbCsType.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("cmbCsType.Properties.GlyphAlignment")));
            this.cmbCsType.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbCsType.Properties.Items"), ((object)(resources.GetObject("cmbCsType.Properties.Items1"))), ((int)(resources.GetObject("cmbCsType.Properties.Items2")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbCsType.Properties.Items3"), ((object)(resources.GetObject("cmbCsType.Properties.Items4"))), ((int)(resources.GetObject("cmbCsType.Properties.Items5")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbCsType.Properties.Items6"), ((object)(resources.GetObject("cmbCsType.Properties.Items7"))), ((int)(resources.GetObject("cmbCsType.Properties.Items8"))))});
            this.cmbCsType.Properties.NullValuePrompt = resources.GetString("cmbCsType.Properties.NullValuePrompt");
            this.cmbCsType.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("cmbCsType.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl35
            // 
            resources.ApplyResources(this.labelControl35, "labelControl35");
            this.labelControl35.Name = "labelControl35";
            // 
            // lkpDelivery
            // 
            resources.ApplyResources(this.lkpDelivery, "lkpDelivery");
            this.lkpDelivery.EnterMoveNextControl = true;
            this.lkpDelivery.Name = "lkpDelivery";
            this.lkpDelivery.Properties.AccessibleDescription = resources.GetString("lkpDelivery.Properties.AccessibleDescription");
            this.lkpDelivery.Properties.AccessibleName = resources.GetString("lkpDelivery.Properties.AccessibleName");
            this.lkpDelivery.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkpDelivery.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkpDelivery.Properties.Appearance.FontSizeDelta")));
            this.lkpDelivery.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpDelivery.Properties.Appearance.FontStyleDelta")));
            this.lkpDelivery.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpDelivery.Properties.Appearance.GradientMode")));
            this.lkpDelivery.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkpDelivery.Properties.Appearance.Image")));
            this.lkpDelivery.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpDelivery.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpDelivery.Properties.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.lkpDelivery.Properties.AppearanceDropDown.FontSizeDelta = ((int)(resources.GetObject("lkpDelivery.Properties.AppearanceDropDown.FontSizeDelta")));
            this.lkpDelivery.Properties.AppearanceDropDown.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpDelivery.Properties.AppearanceDropDown.FontStyleDelta")));
            this.lkpDelivery.Properties.AppearanceDropDown.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpDelivery.Properties.AppearanceDropDown.GradientMode")));
            this.lkpDelivery.Properties.AppearanceDropDown.Image = ((System.Drawing.Image)(resources.GetObject("lkpDelivery.Properties.AppearanceDropDown.Image")));
            this.lkpDelivery.Properties.AppearanceDropDown.Options.UseTextOptions = true;
            this.lkpDelivery.Properties.AppearanceDropDown.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkpDelivery.Properties.AppearanceDropDownHeader.FontSizeDelta = ((int)(resources.GetObject("lkpDelivery.Properties.AppearanceDropDownHeader.FontSizeDelta")));
            this.lkpDelivery.Properties.AppearanceDropDownHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpDelivery.Properties.AppearanceDropDownHeader.FontStyleDelta")));
            this.lkpDelivery.Properties.AppearanceDropDownHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpDelivery.Properties.AppearanceDropDownHeader.GradientMode")));
            this.lkpDelivery.Properties.AppearanceDropDownHeader.Image = ((System.Drawing.Image)(resources.GetObject("lkpDelivery.Properties.AppearanceDropDownHeader.Image")));
            this.lkpDelivery.Properties.AppearanceDropDownHeader.Options.UseTextOptions = true;
            this.lkpDelivery.Properties.AppearanceDropDownHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkpDelivery.Properties.AutoHeight = ((bool)(resources.GetObject("lkpDelivery.Properties.AutoHeight")));
            this.lkpDelivery.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkpDelivery.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpDelivery.Properties.Buttons"))))});
            this.lkpDelivery.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpDelivery.Properties.Columns"), resources.GetString("lkpDelivery.Properties.Columns1")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpDelivery.Properties.Columns2"), resources.GetString("lkpDelivery.Properties.Columns3")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpDelivery.Properties.Columns4"), resources.GetString("lkpDelivery.Properties.Columns5"))});
            this.lkpDelivery.Properties.DisplayMember = "CGNameAr";
            this.lkpDelivery.Properties.NullText = resources.GetString("lkpDelivery.Properties.NullText");
            this.lkpDelivery.Properties.NullValuePrompt = resources.GetString("lkpDelivery.Properties.NullValuePrompt");
            this.lkpDelivery.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkpDelivery.Properties.NullValuePromptShowForEmptyValue")));
            this.lkpDelivery.Properties.PopupSizeable = false;
            this.lkpDelivery.Properties.ValueMember = "CustomerGroupId";
            // 
            // txt_Rep_ID
            // 
            resources.ApplyResources(this.txt_Rep_ID, "txt_Rep_ID");
            this.txt_Rep_ID.EnterMoveNextControl = true;
            this.txt_Rep_ID.Name = "txt_Rep_ID";
            this.txt_Rep_ID.Properties.AccessibleDescription = resources.GetString("txt_Rep_ID.Properties.AccessibleDescription");
            this.txt_Rep_ID.Properties.AccessibleName = resources.GetString("txt_Rep_ID.Properties.AccessibleName");
            this.txt_Rep_ID.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Rep_ID.Properties.AutoHeight")));
            this.txt_Rep_ID.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_Rep_ID.Properties.Mask.AutoComplete")));
            this.txt_Rep_ID.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_Rep_ID.Properties.Mask.BeepOnError")));
            this.txt_Rep_ID.Properties.Mask.EditMask = resources.GetString("txt_Rep_ID.Properties.Mask.EditMask");
            this.txt_Rep_ID.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Rep_ID.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Rep_ID.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Rep_ID.Properties.Mask.MaskType")));
            this.txt_Rep_ID.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_Rep_ID.Properties.Mask.PlaceHolder")));
            this.txt_Rep_ID.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Rep_ID.Properties.Mask.SaveLiteral")));
            this.txt_Rep_ID.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Rep_ID.Properties.Mask.ShowPlaceHolders")));
            this.txt_Rep_ID.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_Rep_ID.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_Rep_ID.Properties.MaxLength = 50;
            this.txt_Rep_ID.Properties.NullValuePrompt = resources.GetString("txt_Rep_ID.Properties.NullValuePrompt");
            this.txt_Rep_ID.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_Rep_ID.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl33
            // 
            resources.ApplyResources(this.labelControl33, "labelControl33");
            this.labelControl33.Name = "labelControl33";
            // 
            // txt_Rep_Phone
            // 
            resources.ApplyResources(this.txt_Rep_Phone, "txt_Rep_Phone");
            this.txt_Rep_Phone.EnterMoveNextControl = true;
            this.txt_Rep_Phone.Name = "txt_Rep_Phone";
            this.txt_Rep_Phone.Properties.AccessibleDescription = resources.GetString("txt_Rep_Phone.Properties.AccessibleDescription");
            this.txt_Rep_Phone.Properties.AccessibleName = resources.GetString("txt_Rep_Phone.Properties.AccessibleName");
            this.txt_Rep_Phone.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Rep_Phone.Properties.AutoHeight")));
            this.txt_Rep_Phone.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_Rep_Phone.Properties.Mask.AutoComplete")));
            this.txt_Rep_Phone.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_Rep_Phone.Properties.Mask.BeepOnError")));
            this.txt_Rep_Phone.Properties.Mask.EditMask = resources.GetString("txt_Rep_Phone.Properties.Mask.EditMask");
            this.txt_Rep_Phone.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Rep_Phone.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Rep_Phone.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Rep_Phone.Properties.Mask.MaskType")));
            this.txt_Rep_Phone.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_Rep_Phone.Properties.Mask.PlaceHolder")));
            this.txt_Rep_Phone.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Rep_Phone.Properties.Mask.SaveLiteral")));
            this.txt_Rep_Phone.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Rep_Phone.Properties.Mask.ShowPlaceHolders")));
            this.txt_Rep_Phone.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_Rep_Phone.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_Rep_Phone.Properties.MaxLength = 50;
            this.txt_Rep_Phone.Properties.NullValuePrompt = resources.GetString("txt_Rep_Phone.Properties.NullValuePrompt");
            this.txt_Rep_Phone.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_Rep_Phone.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl34
            // 
            resources.ApplyResources(this.labelControl34, "labelControl34");
            this.labelControl34.Name = "labelControl34";
            // 
            // labelControl32
            // 
            resources.ApplyResources(this.labelControl32, "labelControl32");
            this.labelControl32.Name = "labelControl32";
            // 
            // lkp_Group
            // 
            resources.ApplyResources(this.lkp_Group, "lkp_Group");
            this.lkp_Group.EnterMoveNextControl = true;
            this.lkp_Group.Name = "lkp_Group";
            this.lkp_Group.Properties.AccessibleDescription = resources.GetString("lkp_Group.Properties.AccessibleDescription");
            this.lkp_Group.Properties.AccessibleName = resources.GetString("lkp_Group.Properties.AccessibleName");
            this.lkp_Group.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkp_Group.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_Group.Properties.Appearance.FontSizeDelta")));
            this.lkp_Group.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_Group.Properties.Appearance.FontStyleDelta")));
            this.lkp_Group.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_Group.Properties.Appearance.GradientMode")));
            this.lkp_Group.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_Group.Properties.Appearance.Image")));
            this.lkp_Group.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_Group.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_Group.Properties.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.lkp_Group.Properties.AppearanceDropDown.FontSizeDelta = ((int)(resources.GetObject("lkp_Group.Properties.AppearanceDropDown.FontSizeDelta")));
            this.lkp_Group.Properties.AppearanceDropDown.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_Group.Properties.AppearanceDropDown.FontStyleDelta")));
            this.lkp_Group.Properties.AppearanceDropDown.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_Group.Properties.AppearanceDropDown.GradientMode")));
            this.lkp_Group.Properties.AppearanceDropDown.Image = ((System.Drawing.Image)(resources.GetObject("lkp_Group.Properties.AppearanceDropDown.Image")));
            this.lkp_Group.Properties.AppearanceDropDown.Options.UseTextOptions = true;
            this.lkp_Group.Properties.AppearanceDropDown.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkp_Group.Properties.AppearanceDropDownHeader.FontSizeDelta = ((int)(resources.GetObject("lkp_Group.Properties.AppearanceDropDownHeader.FontSizeDelta")));
            this.lkp_Group.Properties.AppearanceDropDownHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_Group.Properties.AppearanceDropDownHeader.FontStyleDelta")));
            this.lkp_Group.Properties.AppearanceDropDownHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_Group.Properties.AppearanceDropDownHeader.GradientMode")));
            this.lkp_Group.Properties.AppearanceDropDownHeader.Image = ((System.Drawing.Image)(resources.GetObject("lkp_Group.Properties.AppearanceDropDownHeader.Image")));
            this.lkp_Group.Properties.AppearanceDropDownHeader.Options.UseTextOptions = true;
            this.lkp_Group.Properties.AppearanceDropDownHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkp_Group.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_Group.Properties.AutoHeight")));
            this.lkp_Group.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_Group.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_Group.Properties.Buttons"))))});
            this.lkp_Group.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Group.Properties.Columns"), resources.GetString("lkp_Group.Properties.Columns1")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Group.Properties.Columns2"), resources.GetString("lkp_Group.Properties.Columns3"), ((int)(resources.GetObject("lkp_Group.Properties.Columns4"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_Group.Properties.Columns5"))), resources.GetString("lkp_Group.Properties.Columns6"), ((bool)(resources.GetObject("lkp_Group.Properties.Columns7"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_Group.Properties.Columns8"))))});
            this.lkp_Group.Properties.DisplayMember = "CGNameAr";
            this.lkp_Group.Properties.NullText = resources.GetString("lkp_Group.Properties.NullText");
            this.lkp_Group.Properties.NullValuePrompt = resources.GetString("lkp_Group.Properties.NullValuePrompt");
            this.lkp_Group.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_Group.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_Group.Properties.PopupSizeable = false;
            this.lkp_Group.Properties.ValueMember = "CustomerGroupId";
            // 
            // txt_Bank
            // 
            resources.ApplyResources(this.txt_Bank, "txt_Bank");
            this.txt_Bank.EnterMoveNextControl = true;
            this.txt_Bank.Name = "txt_Bank";
            this.txt_Bank.Properties.AccessibleDescription = resources.GetString("txt_Bank.Properties.AccessibleDescription");
            this.txt_Bank.Properties.AccessibleName = resources.GetString("txt_Bank.Properties.AccessibleName");
            this.txt_Bank.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Bank.Properties.AutoHeight")));
            this.txt_Bank.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_Bank.Properties.Mask.AutoComplete")));
            this.txt_Bank.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_Bank.Properties.Mask.BeepOnError")));
            this.txt_Bank.Properties.Mask.EditMask = resources.GetString("txt_Bank.Properties.Mask.EditMask");
            this.txt_Bank.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Bank.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Bank.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Bank.Properties.Mask.MaskType")));
            this.txt_Bank.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_Bank.Properties.Mask.PlaceHolder")));
            this.txt_Bank.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Bank.Properties.Mask.SaveLiteral")));
            this.txt_Bank.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Bank.Properties.Mask.ShowPlaceHolders")));
            this.txt_Bank.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_Bank.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_Bank.Properties.MaxLength = 50;
            this.txt_Bank.Properties.NullValuePrompt = resources.GetString("txt_Bank.Properties.NullValuePrompt");
            this.txt_Bank.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_Bank.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_Bank.Modified += new System.EventHandler(this.txtVenCode_Modified);
            // 
            // labelControl28
            // 
            resources.ApplyResources(this.labelControl28, "labelControl28");
            this.labelControl28.Name = "labelControl28";
            // 
            // txt_BankAccNum
            // 
            resources.ApplyResources(this.txt_BankAccNum, "txt_BankAccNum");
            this.txt_BankAccNum.EnterMoveNextControl = true;
            this.txt_BankAccNum.Name = "txt_BankAccNum";
            this.txt_BankAccNum.Properties.AccessibleDescription = resources.GetString("txt_BankAccNum.Properties.AccessibleDescription");
            this.txt_BankAccNum.Properties.AccessibleName = resources.GetString("txt_BankAccNum.Properties.AccessibleName");
            this.txt_BankAccNum.Properties.AutoHeight = ((bool)(resources.GetObject("txt_BankAccNum.Properties.AutoHeight")));
            this.txt_BankAccNum.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_BankAccNum.Properties.Mask.AutoComplete")));
            this.txt_BankAccNum.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_BankAccNum.Properties.Mask.BeepOnError")));
            this.txt_BankAccNum.Properties.Mask.EditMask = resources.GetString("txt_BankAccNum.Properties.Mask.EditMask");
            this.txt_BankAccNum.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_BankAccNum.Properties.Mask.IgnoreMaskBlank")));
            this.txt_BankAccNum.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_BankAccNum.Properties.Mask.MaskType")));
            this.txt_BankAccNum.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_BankAccNum.Properties.Mask.PlaceHolder")));
            this.txt_BankAccNum.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_BankAccNum.Properties.Mask.SaveLiteral")));
            this.txt_BankAccNum.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_BankAccNum.Properties.Mask.ShowPlaceHolders")));
            this.txt_BankAccNum.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_BankAccNum.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_BankAccNum.Properties.MaxLength = 50;
            this.txt_BankAccNum.Properties.NullValuePrompt = resources.GetString("txt_BankAccNum.Properties.NullValuePrompt");
            this.txt_BankAccNum.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_BankAccNum.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_BankAccNum.Modified += new System.EventHandler(this.txtVenCode_Modified);
            // 
            // labelControl31
            // 
            resources.ApplyResources(this.labelControl31, "labelControl31");
            this.labelControl31.Name = "labelControl31";
            // 
            // txt_IdNumber
            // 
            resources.ApplyResources(this.txt_IdNumber, "txt_IdNumber");
            this.txt_IdNumber.EnterMoveNextControl = true;
            this.txt_IdNumber.Name = "txt_IdNumber";
            this.txt_IdNumber.Properties.AccessibleDescription = resources.GetString("txt_IdNumber.Properties.AccessibleDescription");
            this.txt_IdNumber.Properties.AccessibleName = resources.GetString("txt_IdNumber.Properties.AccessibleName");
            this.txt_IdNumber.Properties.AutoHeight = ((bool)(resources.GetObject("txt_IdNumber.Properties.AutoHeight")));
            this.txt_IdNumber.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_IdNumber.Properties.Mask.AutoComplete")));
            this.txt_IdNumber.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_IdNumber.Properties.Mask.BeepOnError")));
            this.txt_IdNumber.Properties.Mask.EditMask = resources.GetString("txt_IdNumber.Properties.Mask.EditMask");
            this.txt_IdNumber.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_IdNumber.Properties.Mask.IgnoreMaskBlank")));
            this.txt_IdNumber.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_IdNumber.Properties.Mask.MaskType")));
            this.txt_IdNumber.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_IdNumber.Properties.Mask.PlaceHolder")));
            this.txt_IdNumber.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_IdNumber.Properties.Mask.SaveLiteral")));
            this.txt_IdNumber.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_IdNumber.Properties.Mask.ShowPlaceHolders")));
            this.txt_IdNumber.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_IdNumber.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_IdNumber.Properties.MaxLength = 50;
            this.txt_IdNumber.Properties.NullValuePrompt = resources.GetString("txt_IdNumber.Properties.NullValuePrompt");
            this.txt_IdNumber.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_IdNumber.Properties.NullValuePromptShowForEmptyValue")));
            this.txt_IdNumber.Modified += new System.EventHandler(this.txtVenCode_Modified);
            // 
            // labelControl26
            // 
            resources.ApplyResources(this.labelControl26, "labelControl26");
            this.labelControl26.Name = "labelControl26";
            // 
            // pnlOpenBlnce
            // 
            resources.ApplyResources(this.pnlOpenBlnce, "pnlOpenBlnce");
            this.pnlOpenBlnce.Controls.Add(this.lblOpenAmount);
            this.pnlOpenBlnce.Controls.Add(this.lblOpenDate);
            this.pnlOpenBlnce.Controls.Add(this.txtOpenAmount);
            this.pnlOpenBlnce.Controls.Add(this.dtOpenBalance);
            this.pnlOpenBlnce.Controls.Add(this.cmbIsCredit);
            this.pnlOpenBlnce.Name = "pnlOpenBlnce";
            // 
            // lblOpenAmount
            // 
            resources.ApplyResources(this.lblOpenAmount, "lblOpenAmount");
            this.lblOpenAmount.Name = "lblOpenAmount";
            // 
            // lblOpenDate
            // 
            resources.ApplyResources(this.lblOpenDate, "lblOpenDate");
            this.lblOpenDate.Name = "lblOpenDate";
            // 
            // txtOpenAmount
            // 
            resources.ApplyResources(this.txtOpenAmount, "txtOpenAmount");
            this.txtOpenAmount.EnterMoveNextControl = true;
            this.txtOpenAmount.Name = "txtOpenAmount";
            this.txtOpenAmount.Properties.AccessibleDescription = resources.GetString("txtOpenAmount.Properties.AccessibleDescription");
            this.txtOpenAmount.Properties.AccessibleName = resources.GetString("txtOpenAmount.Properties.AccessibleName");
            this.txtOpenAmount.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txtOpenAmount.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtOpenAmount.Properties.Appearance.FontSizeDelta")));
            this.txtOpenAmount.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtOpenAmount.Properties.Appearance.FontStyleDelta")));
            this.txtOpenAmount.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtOpenAmount.Properties.Appearance.GradientMode")));
            this.txtOpenAmount.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtOpenAmount.Properties.Appearance.Image")));
            this.txtOpenAmount.Properties.Appearance.Options.UseTextOptions = true;
            this.txtOpenAmount.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtOpenAmount.Properties.AutoHeight = ((bool)(resources.GetObject("txtOpenAmount.Properties.AutoHeight")));
            this.txtOpenAmount.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtOpenAmount.Properties.Mask.AutoComplete")));
            this.txtOpenAmount.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtOpenAmount.Properties.Mask.BeepOnError")));
            this.txtOpenAmount.Properties.Mask.EditMask = resources.GetString("txtOpenAmount.Properties.Mask.EditMask");
            this.txtOpenAmount.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtOpenAmount.Properties.Mask.IgnoreMaskBlank")));
            this.txtOpenAmount.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtOpenAmount.Properties.Mask.MaskType")));
            this.txtOpenAmount.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtOpenAmount.Properties.Mask.PlaceHolder")));
            this.txtOpenAmount.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtOpenAmount.Properties.Mask.SaveLiteral")));
            this.txtOpenAmount.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtOpenAmount.Properties.Mask.ShowPlaceHolders")));
            this.txtOpenAmount.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtOpenAmount.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtOpenAmount.Properties.NullText = resources.GetString("txtOpenAmount.Properties.NullText");
            this.txtOpenAmount.Properties.NullValuePrompt = resources.GetString("txtOpenAmount.Properties.NullValuePrompt");
            this.txtOpenAmount.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtOpenAmount.Properties.NullValuePromptShowForEmptyValue")));
            this.txtOpenAmount.Modified += new System.EventHandler(this.txtVenCode_Modified);
            // 
            // dtOpenBalance
            // 
            resources.ApplyResources(this.dtOpenBalance, "dtOpenBalance");
            this.dtOpenBalance.MenuManager = this.barManager1;
            this.dtOpenBalance.Name = "dtOpenBalance";
            this.dtOpenBalance.Properties.AccessibleDescription = resources.GetString("dtOpenBalance.Properties.AccessibleDescription");
            this.dtOpenBalance.Properties.AccessibleName = resources.GetString("dtOpenBalance.Properties.AccessibleName");
            this.dtOpenBalance.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("dtOpenBalance.Properties.Appearance.FontSizeDelta")));
            this.dtOpenBalance.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("dtOpenBalance.Properties.Appearance.FontStyleDelta")));
            this.dtOpenBalance.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("dtOpenBalance.Properties.Appearance.GradientMode")));
            this.dtOpenBalance.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("dtOpenBalance.Properties.Appearance.Image")));
            this.dtOpenBalance.Properties.Appearance.Options.UseTextOptions = true;
            this.dtOpenBalance.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.dtOpenBalance.Properties.AutoHeight = ((bool)(resources.GetObject("dtOpenBalance.Properties.AutoHeight")));
            this.dtOpenBalance.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("dtOpenBalance.Properties.Buttons"))))});
            this.dtOpenBalance.Properties.CalendarTimeProperties.AccessibleDescription = resources.GetString("dtOpenBalance.Properties.CalendarTimeProperties.AccessibleDescription");
            this.dtOpenBalance.Properties.CalendarTimeProperties.AccessibleName = resources.GetString("dtOpenBalance.Properties.CalendarTimeProperties.AccessibleName");
            this.dtOpenBalance.Properties.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("dtOpenBalance.Properties.CalendarTimeProperties.AutoHeight")));
            this.dtOpenBalance.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dtOpenBalance.Properties.CalendarTimeProperties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dtOpenBalance.Properties.CalendarTimeProperties.Mask.AutoComplete")));
            this.dtOpenBalance.Properties.CalendarTimeProperties.Mask.BeepOnError = ((bool)(resources.GetObject("dtOpenBalance.Properties.CalendarTimeProperties.Mask.BeepOnError")));
            this.dtOpenBalance.Properties.CalendarTimeProperties.Mask.EditMask = resources.GetString("dtOpenBalance.Properties.CalendarTimeProperties.Mask.EditMask");
            this.dtOpenBalance.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtOpenBalance.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.dtOpenBalance.Properties.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtOpenBalance.Properties.CalendarTimeProperties.Mask.MaskType")));
            this.dtOpenBalance.Properties.CalendarTimeProperties.Mask.PlaceHolder = ((char)(resources.GetObject("dtOpenBalance.Properties.CalendarTimeProperties.Mask.PlaceHolder")));
            this.dtOpenBalance.Properties.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtOpenBalance.Properties.CalendarTimeProperties.Mask.SaveLiteral")));
            this.dtOpenBalance.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtOpenBalance.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.dtOpenBalance.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtOpenBalance.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat")));
            this.dtOpenBalance.Properties.CalendarTimeProperties.NullValuePrompt = resources.GetString("dtOpenBalance.Properties.CalendarTimeProperties.NullValuePrompt");
            this.dtOpenBalance.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dtOpenBalance.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue")));
            this.dtOpenBalance.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dtOpenBalance.Properties.Mask.AutoComplete")));
            this.dtOpenBalance.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("dtOpenBalance.Properties.Mask.BeepOnError")));
            this.dtOpenBalance.Properties.Mask.EditMask = resources.GetString("dtOpenBalance.Properties.Mask.EditMask");
            this.dtOpenBalance.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtOpenBalance.Properties.Mask.IgnoreMaskBlank")));
            this.dtOpenBalance.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtOpenBalance.Properties.Mask.MaskType")));
            this.dtOpenBalance.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("dtOpenBalance.Properties.Mask.PlaceHolder")));
            this.dtOpenBalance.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtOpenBalance.Properties.Mask.SaveLiteral")));
            this.dtOpenBalance.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtOpenBalance.Properties.Mask.ShowPlaceHolders")));
            this.dtOpenBalance.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtOpenBalance.Properties.Mask.UseMaskAsDisplayFormat")));
            this.dtOpenBalance.Properties.NullValuePrompt = resources.GetString("dtOpenBalance.Properties.NullValuePrompt");
            this.dtOpenBalance.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dtOpenBalance.Properties.NullValuePromptShowForEmptyValue")));
            this.dtOpenBalance.Modified += new System.EventHandler(this.txtVenCode_Modified);
            // 
            // cmbIsCredit
            // 
            resources.ApplyResources(this.cmbIsCredit, "cmbIsCredit");
            this.cmbIsCredit.EnterMoveNextControl = true;
            this.cmbIsCredit.Name = "cmbIsCredit";
            this.cmbIsCredit.Properties.AccessibleDescription = resources.GetString("cmbIsCredit.Properties.AccessibleDescription");
            this.cmbIsCredit.Properties.AccessibleName = resources.GetString("cmbIsCredit.Properties.AccessibleName");
            this.cmbIsCredit.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.cmbIsCredit.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("cmbIsCredit.Properties.Appearance.FontSizeDelta")));
            this.cmbIsCredit.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("cmbIsCredit.Properties.Appearance.FontStyleDelta")));
            this.cmbIsCredit.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("cmbIsCredit.Properties.Appearance.GradientMode")));
            this.cmbIsCredit.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("cmbIsCredit.Properties.Appearance.Image")));
            this.cmbIsCredit.Properties.Appearance.Options.UseTextOptions = true;
            this.cmbIsCredit.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.cmbIsCredit.Properties.AppearanceDropDown.FontSizeDelta = ((int)(resources.GetObject("cmbIsCredit.Properties.AppearanceDropDown.FontSizeDelta")));
            this.cmbIsCredit.Properties.AppearanceDropDown.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("cmbIsCredit.Properties.AppearanceDropDown.FontStyleDelta")));
            this.cmbIsCredit.Properties.AppearanceDropDown.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("cmbIsCredit.Properties.AppearanceDropDown.GradientMode")));
            this.cmbIsCredit.Properties.AppearanceDropDown.Image = ((System.Drawing.Image)(resources.GetObject("cmbIsCredit.Properties.AppearanceDropDown.Image")));
            this.cmbIsCredit.Properties.AppearanceDropDown.Options.UseTextOptions = true;
            this.cmbIsCredit.Properties.AppearanceDropDown.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.cmbIsCredit.Properties.AutoHeight = ((bool)(resources.GetObject("cmbIsCredit.Properties.AutoHeight")));
            this.cmbIsCredit.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("cmbIsCredit.Properties.Buttons"))))});
            this.cmbIsCredit.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("cmbIsCredit.Properties.GlyphAlignment")));
            this.cmbIsCredit.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbIsCredit.Properties.Items"), ((object)(resources.GetObject("cmbIsCredit.Properties.Items1"))), ((int)(resources.GetObject("cmbIsCredit.Properties.Items2")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbIsCredit.Properties.Items3"), ((object)(resources.GetObject("cmbIsCredit.Properties.Items4"))), ((int)(resources.GetObject("cmbIsCredit.Properties.Items5")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbIsCredit.Properties.Items6"), ((object)(resources.GetObject("cmbIsCredit.Properties.Items7"))), ((int)(resources.GetObject("cmbIsCredit.Properties.Items8"))))});
            this.cmbIsCredit.Properties.NullValuePrompt = resources.GetString("cmbIsCredit.Properties.NullValuePrompt");
            this.cmbIsCredit.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("cmbIsCredit.Properties.NullValuePromptShowForEmptyValue")));
            this.cmbIsCredit.EditValueChanged += new System.EventHandler(this.cmbIsCredit_EditValueChanged);
            this.cmbIsCredit.Modified += new System.EventHandler(this.txtVenCode_Modified);
            // 
            // lblOpenBalance
            // 
            resources.ApplyResources(this.lblOpenBalance, "lblOpenBalance");
            this.lblOpenBalance.Name = "lblOpenBalance";
            // 
            // labelControl24
            // 
            resources.ApplyResources(this.labelControl24, "labelControl24");
            this.labelControl24.Name = "labelControl24";
            // 
            // lkp_Category
            // 
            resources.ApplyResources(this.lkp_Category, "lkp_Category");
            this.lkp_Category.EnterMoveNextControl = true;
            this.lkp_Category.Name = "lkp_Category";
            this.lkp_Category.Properties.AccessibleDescription = resources.GetString("lkp_Category.Properties.AccessibleDescription");
            this.lkp_Category.Properties.AccessibleName = resources.GetString("lkp_Category.Properties.AccessibleName");
            this.lkp_Category.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkp_Category.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkp_Category.Properties.Appearance.FontSizeDelta")));
            this.lkp_Category.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_Category.Properties.Appearance.FontStyleDelta")));
            this.lkp_Category.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_Category.Properties.Appearance.GradientMode")));
            this.lkp_Category.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkp_Category.Properties.Appearance.Image")));
            this.lkp_Category.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_Category.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_Category.Properties.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.lkp_Category.Properties.AppearanceDropDown.FontSizeDelta = ((int)(resources.GetObject("lkp_Category.Properties.AppearanceDropDown.FontSizeDelta")));
            this.lkp_Category.Properties.AppearanceDropDown.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_Category.Properties.AppearanceDropDown.FontStyleDelta")));
            this.lkp_Category.Properties.AppearanceDropDown.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_Category.Properties.AppearanceDropDown.GradientMode")));
            this.lkp_Category.Properties.AppearanceDropDown.Image = ((System.Drawing.Image)(resources.GetObject("lkp_Category.Properties.AppearanceDropDown.Image")));
            this.lkp_Category.Properties.AppearanceDropDown.Options.UseTextOptions = true;
            this.lkp_Category.Properties.AppearanceDropDown.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkp_Category.Properties.AppearanceDropDownHeader.FontSizeDelta = ((int)(resources.GetObject("lkp_Category.Properties.AppearanceDropDownHeader.FontSizeDelta")));
            this.lkp_Category.Properties.AppearanceDropDownHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkp_Category.Properties.AppearanceDropDownHeader.FontStyleDelta")));
            this.lkp_Category.Properties.AppearanceDropDownHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkp_Category.Properties.AppearanceDropDownHeader.GradientMode")));
            this.lkp_Category.Properties.AppearanceDropDownHeader.Image = ((System.Drawing.Image)(resources.GetObject("lkp_Category.Properties.AppearanceDropDownHeader.Image")));
            this.lkp_Category.Properties.AppearanceDropDownHeader.Options.UseTextOptions = true;
            this.lkp_Category.Properties.AppearanceDropDownHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkp_Category.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_Category.Properties.AutoHeight")));
            this.lkp_Category.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_Category.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_Category.Properties.Buttons"))))});
            this.lkp_Category.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Category.Properties.Columns"), resources.GetString("lkp_Category.Properties.Columns1")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Category.Properties.Columns2"), resources.GetString("lkp_Category.Properties.Columns3"), ((int)(resources.GetObject("lkp_Category.Properties.Columns4"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_Category.Properties.Columns5"))), resources.GetString("lkp_Category.Properties.Columns6"), ((bool)(resources.GetObject("lkp_Category.Properties.Columns7"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_Category.Properties.Columns8"))))});
            this.lkp_Category.Properties.DisplayMember = "CGNameAr";
            this.lkp_Category.Properties.NullText = resources.GetString("lkp_Category.Properties.NullText");
            this.lkp_Category.Properties.NullValuePrompt = resources.GetString("lkp_Category.Properties.NullValuePrompt");
            this.lkp_Category.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkp_Category.Properties.NullValuePromptShowForEmptyValue")));
            this.lkp_Category.Properties.PopupSizeable = false;
            this.lkp_Category.Properties.ValueMember = "CustomerGroupId";
            this.lkp_Category.EditValueChanged += new System.EventHandler(this.lkp_Group_EditValueChanged);
            // 
            // uc_LinkAccount1
            // 
            resources.ApplyResources(this.uc_LinkAccount1, "uc_LinkAccount1");
            this.uc_LinkAccount1.Name = "uc_LinkAccount1";
            // 
            // tab_docs
            // 
            resources.ApplyResources(this.tab_docs, "tab_docs");
            this.tab_docs.Controls.Add(this.btnCustomerItems);
            this.tab_docs.Controls.Add(this.gridVisits);
            this.tab_docs.Controls.Add(this.chk_IsActive);
            this.tab_docs.Controls.Add(this.chk_IsBlocked);
            this.tab_docs.Controls.Add(this.groupControl1);
            this.tab_docs.Controls.Add(this.groupControl2);
            this.tab_docs.Name = "tab_docs";
            // 
            // btnCustomerItems
            // 
            resources.ApplyResources(this.btnCustomerItems, "btnCustomerItems");
            this.btnCustomerItems.Appearance.FontSizeDelta = ((int)(resources.GetObject("btnCustomerItems.Appearance.FontSizeDelta")));
            this.btnCustomerItems.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("btnCustomerItems.Appearance.FontStyleDelta")));
            this.btnCustomerItems.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("btnCustomerItems.Appearance.GradientMode")));
            this.btnCustomerItems.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("btnCustomerItems.Appearance.Image")));
            this.btnCustomerItems.Appearance.Options.UseFont = true;
            this.btnCustomerItems.Name = "btnCustomerItems";
            this.btnCustomerItems.Click += new System.EventHandler(this.btnCustomerItems_Click);
            // 
            // gridVisits
            // 
            resources.ApplyResources(this.gridVisits, "gridVisits");
            this.gridVisits.Cursor = System.Windows.Forms.Cursors.Default;
            this.gridVisits.EmbeddedNavigator.AccessibleDescription = resources.GetString("gridVisits.EmbeddedNavigator.AccessibleDescription");
            this.gridVisits.EmbeddedNavigator.AccessibleName = resources.GetString("gridVisits.EmbeddedNavigator.AccessibleName");
            this.gridVisits.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("gridVisits.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.gridVisits.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("gridVisits.EmbeddedNavigator.Anchor")));
            this.gridVisits.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("gridVisits.EmbeddedNavigator.BackgroundImage")));
            this.gridVisits.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("gridVisits.EmbeddedNavigator.BackgroundImageLayout")));
            this.gridVisits.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("gridVisits.EmbeddedNavigator.ImeMode")));
            this.gridVisits.EmbeddedNavigator.Margin = ((System.Windows.Forms.Padding)(resources.GetObject("gridVisits.EmbeddedNavigator.Margin")));
            this.gridVisits.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("gridVisits.EmbeddedNavigator.MaximumSize")));
            this.gridVisits.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("gridVisits.EmbeddedNavigator.TextLocation")));
            this.gridVisits.EmbeddedNavigator.ToolTip = resources.GetString("gridVisits.EmbeddedNavigator.ToolTip");
            this.gridVisits.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("gridVisits.EmbeddedNavigator.ToolTipIconType")));
            this.gridVisits.EmbeddedNavigator.ToolTipTitle = resources.GetString("gridVisits.EmbeddedNavigator.ToolTipTitle");
            this.gridVisits.MainView = this.bandedGridView1;
            this.gridVisits.Name = "gridVisits";
            this.gridVisits.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.bandedGridView1});
            // 
            // bandedGridView1
            // 
            this.bandedGridView1.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("bandedGridView1.Appearance.HeaderPanel.FontSizeDelta")));
            this.bandedGridView1.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("bandedGridView1.Appearance.HeaderPanel.FontStyleDelta")));
            this.bandedGridView1.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("bandedGridView1.Appearance.HeaderPanel.GradientMode")));
            this.bandedGridView1.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("bandedGridView1.Appearance.HeaderPanel.Image")));
            this.bandedGridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.bandedGridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bandedGridView1.Appearance.HeaderPanel.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.bandedGridView1.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.bandedGridView1.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("bandedGridView1.Appearance.Row.FontSizeDelta")));
            this.bandedGridView1.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("bandedGridView1.Appearance.Row.FontStyleDelta")));
            this.bandedGridView1.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("bandedGridView1.Appearance.Row.GradientMode")));
            this.bandedGridView1.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("bandedGridView1.Appearance.Row.Image")));
            this.bandedGridView1.Appearance.Row.Options.UseTextOptions = true;
            this.bandedGridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.bandedGridView1.Appearance.Row.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.bandedGridView1.Appearance.Row.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.bandedGridView1.Bands.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.GridBand[] {
            this.gridBand1,
            this.gridBand2,
            this.gridBand3,
            this.gridBand4,
            this.gridBand5});
            resources.ApplyResources(this.bandedGridView1, "bandedGridView1");
            this.bandedGridView1.Columns.AddRange(new DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn[] {
            this.col_Notes,
            this.col_PM,
            this.col_AM,
            this.col_Collect,
            this.col_Sell,
            this.col_Telephone,
            this.col_Field,
            this.colDayName,
            this.col_Id,
            this.colId});
            this.bandedGridView1.GridControl = this.gridVisits;
            this.bandedGridView1.Name = "bandedGridView1";
            this.bandedGridView1.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.bandedGridView1.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.bandedGridView1.OptionsCustomization.AllowGroup = false;
            this.bandedGridView1.OptionsSelection.MultiSelect = true;
            this.bandedGridView1.OptionsSelection.MultiSelectMode = DevExpress.XtraGrid.Views.Grid.GridMultiSelectMode.CheckBoxRowSelect;
            this.bandedGridView1.OptionsView.ShowGroupPanel = false;
            // 
            // gridBand1
            // 
            resources.ApplyResources(this.gridBand1, "gridBand1");
            this.gridBand1.Columns.Add(this.colDayName);
            this.gridBand1.Columns.Add(this.colId);
            this.gridBand1.Columns.Add(this.col_Id);
            this.gridBand1.VisibleIndex = 0;
            // 
            // colDayName
            // 
            resources.ApplyResources(this.colDayName, "colDayName");
            this.colDayName.FieldName = "day";
            this.colDayName.Name = "colDayName";
            this.colDayName.OptionsColumn.AllowEdit = false;
            // 
            // colId
            // 
            resources.ApplyResources(this.colId, "colId");
            this.colId.ColumnEdit = this.rep_Check;
            this.colId.FieldName = "dayId";
            this.colId.Name = "colId";
            // 
            // col_Id
            // 
            resources.ApplyResources(this.col_Id, "col_Id");
            this.col_Id.FieldName = "ID";
            this.col_Id.Name = "col_Id";
            // 
            // gridBand2
            // 
            resources.ApplyResources(this.gridBand2, "gridBand2");
            this.gridBand2.Columns.Add(this.col_Field);
            this.gridBand2.Columns.Add(this.col_Telephone);
            this.gridBand2.VisibleIndex = 1;
            // 
            // col_Field
            // 
            resources.ApplyResources(this.col_Field, "col_Field");
            this.col_Field.ColumnEdit = this.rep_Check;
            this.col_Field.FieldName = "Field";
            this.col_Field.Name = "col_Field";
            // 
            // col_Telephone
            // 
            resources.ApplyResources(this.col_Telephone, "col_Telephone");
            this.col_Telephone.ColumnEdit = this.rep_Check;
            this.col_Telephone.FieldName = "Telephone";
            this.col_Telephone.Name = "col_Telephone";
            // 
            // gridBand3
            // 
            resources.ApplyResources(this.gridBand3, "gridBand3");
            this.gridBand3.Columns.Add(this.col_Collect);
            this.gridBand3.Columns.Add(this.col_Sell);
            this.gridBand3.VisibleIndex = 2;
            // 
            // col_Collect
            // 
            resources.ApplyResources(this.col_Collect, "col_Collect");
            this.col_Collect.ColumnEdit = this.rep_Check;
            this.col_Collect.FieldName = "Collect";
            this.col_Collect.Name = "col_Collect";
            // 
            // col_Sell
            // 
            resources.ApplyResources(this.col_Sell, "col_Sell");
            this.col_Sell.ColumnEdit = this.rep_Check;
            this.col_Sell.FieldName = "Sell";
            this.col_Sell.Name = "col_Sell";
            // 
            // gridBand4
            // 
            resources.ApplyResources(this.gridBand4, "gridBand4");
            this.gridBand4.Columns.Add(this.col_AM);
            this.gridBand4.Columns.Add(this.col_PM);
            this.gridBand4.VisibleIndex = 3;
            // 
            // col_AM
            // 
            resources.ApplyResources(this.col_AM, "col_AM");
            this.col_AM.ColumnEdit = this.rep_Check;
            this.col_AM.FieldName = "AM";
            this.col_AM.Name = "col_AM";
            // 
            // col_PM
            // 
            resources.ApplyResources(this.col_PM, "col_PM");
            this.col_PM.ColumnEdit = this.rep_Check;
            this.col_PM.FieldName = "PM";
            this.col_PM.Name = "col_PM";
            // 
            // gridBand5
            // 
            resources.ApplyResources(this.gridBand5, "gridBand5");
            this.gridBand5.Columns.Add(this.col_Notes);
            this.gridBand5.VisibleIndex = 4;
            // 
            // col_Notes
            // 
            resources.ApplyResources(this.col_Notes, "col_Notes");
            this.col_Notes.FieldName = "Notes";
            this.col_Notes.Name = "col_Notes";
            // 
            // chk_IsActive
            // 
            resources.ApplyResources(this.chk_IsActive, "chk_IsActive");
            this.chk_IsActive.MenuManager = this.barManager1;
            this.chk_IsActive.Name = "chk_IsActive";
            this.chk_IsActive.Properties.AccessibleDescription = resources.GetString("chk_IsActive.Properties.AccessibleDescription");
            this.chk_IsActive.Properties.AccessibleName = resources.GetString("chk_IsActive.Properties.AccessibleName");
            this.chk_IsActive.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("chk_IsActive.Properties.Appearance.FontSizeDelta")));
            this.chk_IsActive.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("chk_IsActive.Properties.Appearance.FontStyleDelta")));
            this.chk_IsActive.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("chk_IsActive.Properties.Appearance.GradientMode")));
            this.chk_IsActive.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("chk_IsActive.Properties.Appearance.Image")));
            this.chk_IsActive.Properties.Appearance.Options.UseTextOptions = true;
            this.chk_IsActive.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chk_IsActive.Properties.AutoHeight = ((bool)(resources.GetObject("chk_IsActive.Properties.AutoHeight")));
            this.chk_IsActive.Properties.AutoWidth = true;
            this.chk_IsActive.Properties.Caption = resources.GetString("chk_IsActive.Properties.Caption");
            this.chk_IsActive.Properties.DisplayValueChecked = resources.GetString("chk_IsActive.Properties.DisplayValueChecked");
            this.chk_IsActive.Properties.DisplayValueGrayed = resources.GetString("chk_IsActive.Properties.DisplayValueGrayed");
            this.chk_IsActive.Properties.DisplayValueUnchecked = resources.GetString("chk_IsActive.Properties.DisplayValueUnchecked");
            this.chk_IsActive.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("chk_IsActive.Properties.GlyphAlignment")));
            // 
            // chk_IsBlocked
            // 
            resources.ApplyResources(this.chk_IsBlocked, "chk_IsBlocked");
            this.chk_IsBlocked.MenuManager = this.barManager1;
            this.chk_IsBlocked.Name = "chk_IsBlocked";
            this.chk_IsBlocked.Properties.AccessibleDescription = resources.GetString("chk_IsBlocked.Properties.AccessibleDescription");
            this.chk_IsBlocked.Properties.AccessibleName = resources.GetString("chk_IsBlocked.Properties.AccessibleName");
            this.chk_IsBlocked.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("chk_IsBlocked.Properties.Appearance.FontSizeDelta")));
            this.chk_IsBlocked.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("chk_IsBlocked.Properties.Appearance.FontStyleDelta")));
            this.chk_IsBlocked.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("chk_IsBlocked.Properties.Appearance.GradientMode")));
            this.chk_IsBlocked.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("chk_IsBlocked.Properties.Appearance.Image")));
            this.chk_IsBlocked.Properties.Appearance.Options.UseTextOptions = true;
            this.chk_IsBlocked.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chk_IsBlocked.Properties.AutoHeight = ((bool)(resources.GetObject("chk_IsBlocked.Properties.AutoHeight")));
            this.chk_IsBlocked.Properties.AutoWidth = true;
            this.chk_IsBlocked.Properties.Caption = resources.GetString("chk_IsBlocked.Properties.Caption");
            this.chk_IsBlocked.Properties.DisplayValueChecked = resources.GetString("chk_IsBlocked.Properties.DisplayValueChecked");
            this.chk_IsBlocked.Properties.DisplayValueGrayed = resources.GetString("chk_IsBlocked.Properties.DisplayValueGrayed");
            this.chk_IsBlocked.Properties.DisplayValueUnchecked = resources.GetString("chk_IsBlocked.Properties.DisplayValueUnchecked");
            this.chk_IsBlocked.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("chk_IsBlocked.Properties.GlyphAlignment")));
            // 
            // groupControl1
            // 
            resources.ApplyResources(this.groupControl1, "groupControl1");
            this.groupControl1.AppearanceCaption.FontSizeDelta = ((int)(resources.GetObject("groupControl1.AppearanceCaption.FontSizeDelta")));
            this.groupControl1.AppearanceCaption.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("groupControl1.AppearanceCaption.FontStyleDelta")));
            this.groupControl1.AppearanceCaption.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("groupControl1.AppearanceCaption.GradientMode")));
            this.groupControl1.AppearanceCaption.Image = ((System.Drawing.Image)(resources.GetObject("groupControl1.AppearanceCaption.Image")));
            this.groupControl1.AppearanceCaption.Options.UseTextOptions = true;
            this.groupControl1.AppearanceCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.groupControl1.Controls.Add(this.labelControl49);
            this.groupControl1.Controls.Add(this.labelControl30);
            this.groupControl1.Controls.Add(this.labelControl27);
            this.groupControl1.Controls.Add(this.chk_IsTaxable);
            this.groupControl1.Controls.Add(this.txtTradeRegistry);
            this.groupControl1.Controls.Add(this.txtTaxCardNumber);
            this.groupControl1.Controls.Add(this.txtTaxDepartment);
            this.groupControl1.Controls.Add(this.lblTaxDepartment);
            this.groupControl1.Controls.Add(this.txtTaxFileNumber);
            this.groupControl1.Controls.Add(this.labelControl29);
            this.groupControl1.Name = "groupControl1";
            // 
            // labelControl49
            // 
            resources.ApplyResources(this.labelControl49, "labelControl49");
            this.labelControl49.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl49.Appearance.FontSizeDelta")));
            this.labelControl49.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl49.Appearance.FontStyleDelta")));
            this.labelControl49.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("labelControl49.Appearance.ForeColor")));
            this.labelControl49.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl49.Appearance.GradientMode")));
            this.labelControl49.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl49.Appearance.Image")));
            this.labelControl49.Name = "labelControl49";
            // 
            // labelControl30
            // 
            resources.ApplyResources(this.labelControl30, "labelControl30");
            this.labelControl30.Name = "labelControl30";
            // 
            // labelControl27
            // 
            resources.ApplyResources(this.labelControl27, "labelControl27");
            this.labelControl27.Name = "labelControl27";
            // 
            // chk_IsTaxable
            // 
            resources.ApplyResources(this.chk_IsTaxable, "chk_IsTaxable");
            this.chk_IsTaxable.MenuManager = this.barManager1;
            this.chk_IsTaxable.Name = "chk_IsTaxable";
            this.chk_IsTaxable.Properties.AccessibleDescription = resources.GetString("chk_IsTaxable.Properties.AccessibleDescription");
            this.chk_IsTaxable.Properties.AccessibleName = resources.GetString("chk_IsTaxable.Properties.AccessibleName");
            this.chk_IsTaxable.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("chk_IsTaxable.Properties.Appearance.FontSizeDelta")));
            this.chk_IsTaxable.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("chk_IsTaxable.Properties.Appearance.FontStyleDelta")));
            this.chk_IsTaxable.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("chk_IsTaxable.Properties.Appearance.GradientMode")));
            this.chk_IsTaxable.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("chk_IsTaxable.Properties.Appearance.Image")));
            this.chk_IsTaxable.Properties.Appearance.Options.UseTextOptions = true;
            this.chk_IsTaxable.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chk_IsTaxable.Properties.AutoHeight = ((bool)(resources.GetObject("chk_IsTaxable.Properties.AutoHeight")));
            this.chk_IsTaxable.Properties.Caption = resources.GetString("chk_IsTaxable.Properties.Caption");
            this.chk_IsTaxable.Properties.DisplayValueChecked = resources.GetString("chk_IsTaxable.Properties.DisplayValueChecked");
            this.chk_IsTaxable.Properties.DisplayValueGrayed = resources.GetString("chk_IsTaxable.Properties.DisplayValueGrayed");
            this.chk_IsTaxable.Properties.DisplayValueUnchecked = resources.GetString("chk_IsTaxable.Properties.DisplayValueUnchecked");
            this.chk_IsTaxable.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("chk_IsTaxable.Properties.GlyphAlignment")));
            this.chk_IsTaxable.Modified += new System.EventHandler(this.txtVenCode_Modified);
            // 
            // txtTradeRegistry
            // 
            resources.ApplyResources(this.txtTradeRegistry, "txtTradeRegistry");
            this.txtTradeRegistry.EnterMoveNextControl = true;
            this.txtTradeRegistry.Name = "txtTradeRegistry";
            this.txtTradeRegistry.Properties.AccessibleDescription = resources.GetString("txtTradeRegistry.Properties.AccessibleDescription");
            this.txtTradeRegistry.Properties.AccessibleName = resources.GetString("txtTradeRegistry.Properties.AccessibleName");
            this.txtTradeRegistry.Properties.AutoHeight = ((bool)(resources.GetObject("txtTradeRegistry.Properties.AutoHeight")));
            this.txtTradeRegistry.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtTradeRegistry.Properties.Mask.AutoComplete")));
            this.txtTradeRegistry.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtTradeRegistry.Properties.Mask.BeepOnError")));
            this.txtTradeRegistry.Properties.Mask.EditMask = resources.GetString("txtTradeRegistry.Properties.Mask.EditMask");
            this.txtTradeRegistry.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtTradeRegistry.Properties.Mask.IgnoreMaskBlank")));
            this.txtTradeRegistry.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtTradeRegistry.Properties.Mask.MaskType")));
            this.txtTradeRegistry.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtTradeRegistry.Properties.Mask.PlaceHolder")));
            this.txtTradeRegistry.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtTradeRegistry.Properties.Mask.SaveLiteral")));
            this.txtTradeRegistry.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtTradeRegistry.Properties.Mask.ShowPlaceHolders")));
            this.txtTradeRegistry.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtTradeRegistry.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtTradeRegistry.Properties.MaxLength = 50;
            this.txtTradeRegistry.Properties.NullValuePrompt = resources.GetString("txtTradeRegistry.Properties.NullValuePrompt");
            this.txtTradeRegistry.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtTradeRegistry.Properties.NullValuePromptShowForEmptyValue")));
            this.txtTradeRegistry.Modified += new System.EventHandler(this.txtVenCode_Modified);
            // 
            // txtTaxCardNumber
            // 
            resources.ApplyResources(this.txtTaxCardNumber, "txtTaxCardNumber");
            this.txtTaxCardNumber.EnterMoveNextControl = true;
            this.txtTaxCardNumber.Name = "txtTaxCardNumber";
            this.txtTaxCardNumber.Properties.AccessibleDescription = resources.GetString("txtTaxCardNumber.Properties.AccessibleDescription");
            this.txtTaxCardNumber.Properties.AccessibleName = resources.GetString("txtTaxCardNumber.Properties.AccessibleName");
            this.txtTaxCardNumber.Properties.AutoHeight = ((bool)(resources.GetObject("txtTaxCardNumber.Properties.AutoHeight")));
            this.txtTaxCardNumber.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtTaxCardNumber.Properties.Mask.AutoComplete")));
            this.txtTaxCardNumber.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtTaxCardNumber.Properties.Mask.BeepOnError")));
            this.txtTaxCardNumber.Properties.Mask.EditMask = resources.GetString("txtTaxCardNumber.Properties.Mask.EditMask");
            this.txtTaxCardNumber.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtTaxCardNumber.Properties.Mask.IgnoreMaskBlank")));
            this.txtTaxCardNumber.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtTaxCardNumber.Properties.Mask.MaskType")));
            this.txtTaxCardNumber.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtTaxCardNumber.Properties.Mask.PlaceHolder")));
            this.txtTaxCardNumber.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtTaxCardNumber.Properties.Mask.SaveLiteral")));
            this.txtTaxCardNumber.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtTaxCardNumber.Properties.Mask.ShowPlaceHolders")));
            this.txtTaxCardNumber.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtTaxCardNumber.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtTaxCardNumber.Properties.MaxLength = 50;
            this.txtTaxCardNumber.Properties.NullValuePrompt = resources.GetString("txtTaxCardNumber.Properties.NullValuePrompt");
            this.txtTaxCardNumber.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtTaxCardNumber.Properties.NullValuePromptShowForEmptyValue")));
            this.txtTaxCardNumber.Modified += new System.EventHandler(this.txtVenCode_Modified);
            // 
            // txtTaxDepartment
            // 
            resources.ApplyResources(this.txtTaxDepartment, "txtTaxDepartment");
            this.txtTaxDepartment.EnterMoveNextControl = true;
            this.txtTaxDepartment.Name = "txtTaxDepartment";
            this.txtTaxDepartment.Properties.AccessibleDescription = resources.GetString("txtTaxDepartment.Properties.AccessibleDescription");
            this.txtTaxDepartment.Properties.AccessibleName = resources.GetString("txtTaxDepartment.Properties.AccessibleName");
            this.txtTaxDepartment.Properties.AutoHeight = ((bool)(resources.GetObject("txtTaxDepartment.Properties.AutoHeight")));
            this.txtTaxDepartment.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtTaxDepartment.Properties.Mask.AutoComplete")));
            this.txtTaxDepartment.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtTaxDepartment.Properties.Mask.BeepOnError")));
            this.txtTaxDepartment.Properties.Mask.EditMask = resources.GetString("txtTaxDepartment.Properties.Mask.EditMask");
            this.txtTaxDepartment.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtTaxDepartment.Properties.Mask.IgnoreMaskBlank")));
            this.txtTaxDepartment.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtTaxDepartment.Properties.Mask.MaskType")));
            this.txtTaxDepartment.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtTaxDepartment.Properties.Mask.PlaceHolder")));
            this.txtTaxDepartment.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtTaxDepartment.Properties.Mask.SaveLiteral")));
            this.txtTaxDepartment.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtTaxDepartment.Properties.Mask.ShowPlaceHolders")));
            this.txtTaxDepartment.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtTaxDepartment.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtTaxDepartment.Properties.MaxLength = 50;
            this.txtTaxDepartment.Properties.NullValuePrompt = resources.GetString("txtTaxDepartment.Properties.NullValuePrompt");
            this.txtTaxDepartment.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtTaxDepartment.Properties.NullValuePromptShowForEmptyValue")));
            this.txtTaxDepartment.Modified += new System.EventHandler(this.txtVenCode_Modified);
            // 
            // lblTaxDepartment
            // 
            resources.ApplyResources(this.lblTaxDepartment, "lblTaxDepartment");
            this.lblTaxDepartment.Name = "lblTaxDepartment";
            // 
            // txtTaxFileNumber
            // 
            resources.ApplyResources(this.txtTaxFileNumber, "txtTaxFileNumber");
            this.txtTaxFileNumber.EnterMoveNextControl = true;
            this.txtTaxFileNumber.Name = "txtTaxFileNumber";
            this.txtTaxFileNumber.Properties.AccessibleDescription = resources.GetString("txtTaxFileNumber.Properties.AccessibleDescription");
            this.txtTaxFileNumber.Properties.AccessibleName = resources.GetString("txtTaxFileNumber.Properties.AccessibleName");
            this.txtTaxFileNumber.Properties.AutoHeight = ((bool)(resources.GetObject("txtTaxFileNumber.Properties.AutoHeight")));
            this.txtTaxFileNumber.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtTaxFileNumber.Properties.Mask.AutoComplete")));
            this.txtTaxFileNumber.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtTaxFileNumber.Properties.Mask.BeepOnError")));
            this.txtTaxFileNumber.Properties.Mask.EditMask = resources.GetString("txtTaxFileNumber.Properties.Mask.EditMask");
            this.txtTaxFileNumber.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtTaxFileNumber.Properties.Mask.IgnoreMaskBlank")));
            this.txtTaxFileNumber.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtTaxFileNumber.Properties.Mask.MaskType")));
            this.txtTaxFileNumber.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtTaxFileNumber.Properties.Mask.PlaceHolder")));
            this.txtTaxFileNumber.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtTaxFileNumber.Properties.Mask.SaveLiteral")));
            this.txtTaxFileNumber.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtTaxFileNumber.Properties.Mask.ShowPlaceHolders")));
            this.txtTaxFileNumber.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtTaxFileNumber.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtTaxFileNumber.Properties.MaxLength = 50;
            this.txtTaxFileNumber.Properties.NullValuePrompt = resources.GetString("txtTaxFileNumber.Properties.NullValuePrompt");
            this.txtTaxFileNumber.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtTaxFileNumber.Properties.NullValuePromptShowForEmptyValue")));
            this.txtTaxFileNumber.Modified += new System.EventHandler(this.txtVenCode_Modified);
            // 
            // labelControl29
            // 
            resources.ApplyResources(this.labelControl29, "labelControl29");
            this.labelControl29.Name = "labelControl29";
            // 
            // groupControl2
            // 
            resources.ApplyResources(this.groupControl2, "groupControl2");
            this.groupControl2.AppearanceCaption.FontSizeDelta = ((int)(resources.GetObject("groupControl2.AppearanceCaption.FontSizeDelta")));
            this.groupControl2.AppearanceCaption.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("groupControl2.AppearanceCaption.FontStyleDelta")));
            this.groupControl2.AppearanceCaption.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("groupControl2.AppearanceCaption.GradientMode")));
            this.groupControl2.AppearanceCaption.Image = ((System.Drawing.Image)(resources.GetObject("groupControl2.AppearanceCaption.Image")));
            this.groupControl2.AppearanceCaption.Options.UseTextOptions = true;
            this.groupControl2.AppearanceCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.groupControl2.Controls.Add(this.btnEditPhoto);
            this.groupControl2.Controls.Add(this.btnAddEmpPhoho);
            this.groupControl2.Controls.Add(this.btnDeleteEmpPhoto);
            this.groupControl2.Controls.Add(this.btnShowPhotoes);
            this.groupControl2.Controls.Add(this.labelControl22);
            this.groupControl2.Controls.Add(this.txtImagePath);
            this.groupControl2.Controls.Add(this.lstPhotos);
            this.groupControl2.Controls.Add(this.btnBrowse);
            this.groupControl2.Controls.Add(this.labelControl23);
            this.groupControl2.Controls.Add(this.txtImageDesc);
            this.groupControl2.Name = "groupControl2";
            // 
            // btnEditPhoto
            // 
            resources.ApplyResources(this.btnEditPhoto, "btnEditPhoto");
            this.btnEditPhoto.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("btnEditPhoto.Appearance.Font")));
            this.btnEditPhoto.Appearance.FontSizeDelta = ((int)(resources.GetObject("btnEditPhoto.Appearance.FontSizeDelta")));
            this.btnEditPhoto.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("btnEditPhoto.Appearance.FontStyleDelta")));
            this.btnEditPhoto.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("btnEditPhoto.Appearance.ForeColor")));
            this.btnEditPhoto.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("btnEditPhoto.Appearance.GradientMode")));
            this.btnEditPhoto.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("btnEditPhoto.Appearance.Image")));
            this.btnEditPhoto.Appearance.Options.UseFont = true;
            this.btnEditPhoto.Appearance.Options.UseForeColor = true;
            this.btnEditPhoto.Image = global::Pharmacy.Properties.Resources.edit32;
            this.btnEditPhoto.Name = "btnEditPhoto";
            this.btnEditPhoto.TabStop = false;
            this.btnEditPhoto.Click += new System.EventHandler(this.btnEditPhoto_Click);
            // 
            // btnAddEmpPhoho
            // 
            resources.ApplyResources(this.btnAddEmpPhoho, "btnAddEmpPhoho");
            this.btnAddEmpPhoho.Image = global::Pharmacy.Properties.Resources.add32;
            this.btnAddEmpPhoho.Name = "btnAddEmpPhoho";
            this.btnAddEmpPhoho.Click += new System.EventHandler(this.btnAddEmpPhoho_Click);
            // 
            // btnDeleteEmpPhoto
            // 
            resources.ApplyResources(this.btnDeleteEmpPhoto, "btnDeleteEmpPhoto");
            this.btnDeleteEmpPhoto.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("btnDeleteEmpPhoto.Appearance.Font")));
            this.btnDeleteEmpPhoto.Appearance.FontSizeDelta = ((int)(resources.GetObject("btnDeleteEmpPhoto.Appearance.FontSizeDelta")));
            this.btnDeleteEmpPhoto.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("btnDeleteEmpPhoto.Appearance.FontStyleDelta")));
            this.btnDeleteEmpPhoto.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("btnDeleteEmpPhoto.Appearance.ForeColor")));
            this.btnDeleteEmpPhoto.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("btnDeleteEmpPhoto.Appearance.GradientMode")));
            this.btnDeleteEmpPhoto.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("btnDeleteEmpPhoto.Appearance.Image")));
            this.btnDeleteEmpPhoto.Appearance.Options.UseFont = true;
            this.btnDeleteEmpPhoto.Appearance.Options.UseForeColor = true;
            this.btnDeleteEmpPhoto.Image = global::Pharmacy.Properties.Resources.del;
            this.btnDeleteEmpPhoto.Name = "btnDeleteEmpPhoto";
            this.btnDeleteEmpPhoto.TabStop = false;
            this.btnDeleteEmpPhoto.Click += new System.EventHandler(this.btnDeleteEmpPhoto_Click);
            // 
            // btnShowPhotoes
            // 
            resources.ApplyResources(this.btnShowPhotoes, "btnShowPhotoes");
            this.btnShowPhotoes.Appearance.FontSizeDelta = ((int)(resources.GetObject("btnShowPhotoes.Appearance.FontSizeDelta")));
            this.btnShowPhotoes.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("btnShowPhotoes.Appearance.FontStyleDelta")));
            this.btnShowPhotoes.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("btnShowPhotoes.Appearance.GradientMode")));
            this.btnShowPhotoes.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("btnShowPhotoes.Appearance.Image")));
            this.btnShowPhotoes.Appearance.Options.UseFont = true;
            this.btnShowPhotoes.Name = "btnShowPhotoes";
            this.btnShowPhotoes.Click += new System.EventHandler(this.btnShowPhotoes_Click);
            // 
            // labelControl22
            // 
            resources.ApplyResources(this.labelControl22, "labelControl22");
            this.labelControl22.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl22.Appearance.FontSizeDelta")));
            this.labelControl22.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl22.Appearance.FontStyleDelta")));
            this.labelControl22.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("labelControl22.Appearance.ForeColor")));
            this.labelControl22.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl22.Appearance.GradientMode")));
            this.labelControl22.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl22.Appearance.Image")));
            this.labelControl22.Name = "labelControl22";
            // 
            // txtImagePath
            // 
            resources.ApplyResources(this.txtImagePath, "txtImagePath");
            this.txtImagePath.EnterMoveNextControl = true;
            this.txtImagePath.Name = "txtImagePath";
            this.txtImagePath.Properties.AccessibleDescription = resources.GetString("txtImagePath.Properties.AccessibleDescription");
            this.txtImagePath.Properties.AccessibleName = resources.GetString("txtImagePath.Properties.AccessibleName");
            this.txtImagePath.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtImagePath.Properties.Appearance.FontSizeDelta")));
            this.txtImagePath.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtImagePath.Properties.Appearance.FontStyleDelta")));
            this.txtImagePath.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtImagePath.Properties.Appearance.GradientMode")));
            this.txtImagePath.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtImagePath.Properties.Appearance.Image")));
            this.txtImagePath.Properties.Appearance.Options.UseFont = true;
            this.txtImagePath.Properties.AutoHeight = ((bool)(resources.GetObject("txtImagePath.Properties.AutoHeight")));
            this.txtImagePath.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtImagePath.Properties.Mask.AutoComplete")));
            this.txtImagePath.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtImagePath.Properties.Mask.BeepOnError")));
            this.txtImagePath.Properties.Mask.EditMask = resources.GetString("txtImagePath.Properties.Mask.EditMask");
            this.txtImagePath.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtImagePath.Properties.Mask.IgnoreMaskBlank")));
            this.txtImagePath.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtImagePath.Properties.Mask.MaskType")));
            this.txtImagePath.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtImagePath.Properties.Mask.PlaceHolder")));
            this.txtImagePath.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtImagePath.Properties.Mask.SaveLiteral")));
            this.txtImagePath.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtImagePath.Properties.Mask.ShowPlaceHolders")));
            this.txtImagePath.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtImagePath.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtImagePath.Properties.NullValuePrompt = resources.GetString("txtImagePath.Properties.NullValuePrompt");
            this.txtImagePath.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtImagePath.Properties.NullValuePromptShowForEmptyValue")));
            this.txtImagePath.Properties.ReadOnly = true;
            // 
            // lstPhotos
            // 
            resources.ApplyResources(this.lstPhotos, "lstPhotos");
            this.lstPhotos.Appearance.FontSizeDelta = ((int)(resources.GetObject("lstPhotos.Appearance.FontSizeDelta")));
            this.lstPhotos.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lstPhotos.Appearance.FontStyleDelta")));
            this.lstPhotos.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lstPhotos.Appearance.GradientMode")));
            this.lstPhotos.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lstPhotos.Appearance.Image")));
            this.lstPhotos.Appearance.Options.UseFont = true;
            this.lstPhotos.Appearance.Options.UseTextOptions = true;
            this.lstPhotos.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lstPhotos.Name = "lstPhotos";
            this.lstPhotos.TabStop = false;
            // 
            // btnBrowse
            // 
            resources.ApplyResources(this.btnBrowse, "btnBrowse");
            this.btnBrowse.Appearance.FontSizeDelta = ((int)(resources.GetObject("btnBrowse.Appearance.FontSizeDelta")));
            this.btnBrowse.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("btnBrowse.Appearance.FontStyleDelta")));
            this.btnBrowse.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("btnBrowse.Appearance.GradientMode")));
            this.btnBrowse.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("btnBrowse.Appearance.Image")));
            this.btnBrowse.Appearance.Options.UseFont = true;
            this.btnBrowse.Image = global::Pharmacy.Properties.Resources.open;
            this.btnBrowse.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleRight;
            this.btnBrowse.Name = "btnBrowse";
            this.btnBrowse.Click += new System.EventHandler(this.btnBrowse_Click);
            // 
            // labelControl23
            // 
            resources.ApplyResources(this.labelControl23, "labelControl23");
            this.labelControl23.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl23.Appearance.FontSizeDelta")));
            this.labelControl23.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl23.Appearance.FontStyleDelta")));
            this.labelControl23.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("labelControl23.Appearance.ForeColor")));
            this.labelControl23.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl23.Appearance.GradientMode")));
            this.labelControl23.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl23.Appearance.Image")));
            this.labelControl23.Name = "labelControl23";
            // 
            // txtImageDesc
            // 
            resources.ApplyResources(this.txtImageDesc, "txtImageDesc");
            this.txtImageDesc.EnterMoveNextControl = true;
            this.txtImageDesc.Name = "txtImageDesc";
            this.txtImageDesc.Properties.AccessibleDescription = resources.GetString("txtImageDesc.Properties.AccessibleDescription");
            this.txtImageDesc.Properties.AccessibleName = resources.GetString("txtImageDesc.Properties.AccessibleName");
            this.txtImageDesc.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtImageDesc.Properties.Appearance.FontSizeDelta")));
            this.txtImageDesc.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtImageDesc.Properties.Appearance.FontStyleDelta")));
            this.txtImageDesc.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtImageDesc.Properties.Appearance.GradientMode")));
            this.txtImageDesc.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtImageDesc.Properties.Appearance.Image")));
            this.txtImageDesc.Properties.Appearance.Options.UseFont = true;
            this.txtImageDesc.Properties.Appearance.Options.UseTextOptions = true;
            this.txtImageDesc.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtImageDesc.Properties.AutoHeight = ((bool)(resources.GetObject("txtImageDesc.Properties.AutoHeight")));
            this.txtImageDesc.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtImageDesc.Properties.Mask.AutoComplete")));
            this.txtImageDesc.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtImageDesc.Properties.Mask.BeepOnError")));
            this.txtImageDesc.Properties.Mask.EditMask = resources.GetString("txtImageDesc.Properties.Mask.EditMask");
            this.txtImageDesc.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtImageDesc.Properties.Mask.IgnoreMaskBlank")));
            this.txtImageDesc.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtImageDesc.Properties.Mask.MaskType")));
            this.txtImageDesc.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtImageDesc.Properties.Mask.PlaceHolder")));
            this.txtImageDesc.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtImageDesc.Properties.Mask.SaveLiteral")));
            this.txtImageDesc.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtImageDesc.Properties.Mask.ShowPlaceHolders")));
            this.txtImageDesc.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtImageDesc.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtImageDesc.Properties.NullValuePrompt = resources.GetString("txtImageDesc.Properties.NullValuePrompt");
            this.txtImageDesc.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtImageDesc.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // frm_SL_Customer
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.xtraTabControl1);
            this.Controls.Add(this.btnNext);
            this.Controls.Add(this.btnPrev);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.KeyPreview = true;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "frm_SL_Customer";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_SL_Customer_FormClosing);
            this.Load += new System.EventHandler(this.frm_SL_Customer_Load);
            this.Shown += new System.EventHandler(this.frm_SL_Customer_Shown);
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.frm_SL_Customer_KeyUp);
            ((System.ComponentModel.ISupportInitialize)(this.rep_Check)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTel.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCusNameAr.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCusNameEn.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCusCode.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMobile.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMaxCredit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscRatio.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpPriceLevel.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCity.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtZip.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtEmail.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtManagerName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_SalesEmp.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DueDays.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Representative.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_RepresentativeJob.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtRepFName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_RepFJob.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtAddress.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtShipping.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.tab_main.ResumeLayout(false);
            this.tab_main.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_country.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Governate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_BuildingNumber.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Street.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Neighborhood.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_CollectEmp.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Regions.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbCsType.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpDelivery.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Rep_ID.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Rep_Phone.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Group.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Bank.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_BankAccNum.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_IdNumber.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pnlOpenBlnce)).EndInit();
            this.pnlOpenBlnce.ResumeLayout(false);
            this.pnlOpenBlnce.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtOpenAmount.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtOpenBalance.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtOpenBalance.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbIsCredit.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Category.Properties)).EndInit();
            this.tab_docs.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridVisits)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bandedGridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_IsActive.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_IsBlocked.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chk_IsTaxable.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTradeRegistry.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTaxCardNumber.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTaxDepartment.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTaxFileNumber.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.groupControl2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtImagePath.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lstPhotos)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtImageDesc.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControl15;
        private DevExpress.XtraEditors.TextEdit txtTel;
        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnDelete;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtnHelp;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.TextEdit txtCusNameAr;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.TextEdit txtCusNameEn;
        private DevExpress.XtraBars.BarButtonItem barBtnList;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.TextEdit txtCusCode;
        private DevExpress.XtraEditors.SimpleButton btnNext;
        private DevExpress.XtraEditors.SimpleButton btnPrev;
        private DevExpress.XtraBars.BarButtonItem barBtnNew;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraEditors.TextEdit txtMaxCredit;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.TextEdit txtMobile;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.TextEdit txtDiscRatio;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.LabelControl labelControl19;
        private DevExpress.XtraEditors.LookUpEdit lkpPriceLevel;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.TextEdit txtCity;
        private DevExpress.XtraEditors.LabelControl labelControl9;        
        private DevExpress.XtraEditors.TextEdit txtZip;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraEditors.TextEdit txtFax;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private DevExpress.XtraEditors.TextEdit txtEmail;
        private DevExpress.XtraEditors.LabelControl labelControl12;
        private DevExpress.XtraEditors.LabelControl labelControl13;
        private DevExpress.XtraEditors.TextEdit txtManagerName;
        private DevExpress.XtraEditors.LabelControl labelControl14;
        private DevExpress.XtraEditors.LabelControl labelControl16;
        private DevExpress.XtraEditors.LookUpEdit lkp_SalesEmp;
        private DevExpress.XtraEditors.LabelControl labelControl25;
        private DevExpress.XtraEditors.SpinEdit txt_DueDays;
        private DevExpress.XtraEditors.TextEdit txt_Representative;
        private DevExpress.XtraEditors.LabelControl labelControl17;
        private DevExpress.XtraEditors.TextEdit txt_RepresentativeJob;
        private DevExpress.XtraEditors.LabelControl labelControl18;
        private DevExpress.XtraEditors.TextEdit txtRepFName;
        private DevExpress.XtraEditors.LabelControl labelControl20;
        private DevExpress.XtraEditors.TextEdit txt_RepFJob;
        private DevExpress.XtraEditors.LabelControl labelControl21;
        private DevExpress.XtraEditors.MemoEdit txtAddress;
        private DevExpress.XtraEditors.MemoEdit txtShipping;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage tab_docs;
        private DevExpress.XtraTab.XtraTabPage tab_main;
        private DevExpress.XtraEditors.GroupControl groupControl2;
        private DevExpress.XtraEditors.SimpleButton btnAddEmpPhoho;
        private DevExpress.XtraEditors.SimpleButton btnDeleteEmpPhoto;
        private DevExpress.XtraEditors.SimpleButton btnShowPhotoes;
        private DevExpress.XtraEditors.LabelControl labelControl22;
        private DevExpress.XtraEditors.TextEdit txtImagePath;
        private DevExpress.XtraEditors.ListBoxControl lstPhotos;
        private DevExpress.XtraEditors.SimpleButton btnBrowse;
        private DevExpress.XtraEditors.LabelControl labelControl23;
        private DevExpress.XtraEditors.TextEdit txtImageDesc;
        private DevExpress.XtraEditors.SimpleButton btnEditPhoto;
        private uc_LinkAccount uc_LinkAccount1;
        private DevExpress.XtraEditors.LabelControl labelControl24;
        private DevExpress.XtraEditors.LookUpEdit lkp_Category;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.TextEdit txtTradeRegistry;
        private DevExpress.XtraEditors.TextEdit txtTaxCardNumber;
        private DevExpress.XtraEditors.TextEdit txtTaxDepartment;
        private DevExpress.XtraEditors.LabelControl lblTaxDepartment;
        private DevExpress.XtraEditors.TextEdit txtTaxFileNumber;
        private DevExpress.XtraEditors.LabelControl labelControl29;
        private DevExpress.XtraEditors.CheckEdit chk_IsTaxable;
        private DevExpress.XtraEditors.LabelControl labelControl30;
        private DevExpress.XtraEditors.LabelControl labelControl27;
        private DevExpress.XtraEditors.PanelControl pnlOpenBlnce;
        private DevExpress.XtraEditors.LabelControl lblOpenAmount;
        private DevExpress.XtraEditors.LabelControl lblOpenDate;
        private DevExpress.XtraEditors.TextEdit txtOpenAmount;
        private DevExpress.XtraEditors.DateEdit dtOpenBalance;
        private DevExpress.XtraEditors.ImageComboBoxEdit cmbIsCredit;
        private DevExpress.XtraEditors.LabelControl lblOpenBalance;
        private DevExpress.XtraEditors.TextEdit txt_IdNumber;
        private DevExpress.XtraEditors.LabelControl labelControl26;
        private DevExpress.XtraEditors.TextEdit txt_Bank;
        private DevExpress.XtraEditors.LabelControl labelControl28;
        private DevExpress.XtraEditors.TextEdit txt_BankAccNum;
        private DevExpress.XtraEditors.LabelControl labelControl31;
        private DevExpress.XtraEditors.CheckEdit chk_IsBlocked;
        private DevExpress.XtraEditors.CheckEdit chk_IsActive;
        private DevExpress.XtraEditors.LabelControl labelControl32;
        private DevExpress.XtraEditors.LookUpEdit lkp_Group;
        private DevExpress.XtraEditors.TextEdit txt_Rep_ID;
        private DevExpress.XtraEditors.LabelControl labelControl33;
        private DevExpress.XtraEditors.TextEdit txt_Rep_Phone;
        private DevExpress.XtraEditors.LabelControl labelControl34;
        private DevExpress.XtraGrid.GridControl gridVisits;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridView bandedGridView1;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn colDayName;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn colId;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_Field;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_Telephone;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_Collect;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_Sell;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_AM;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_PM;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_Notes;
        private DevExpress.XtraGrid.Views.BandedGrid.BandedGridColumn col_Id;
        public DevExpress.XtraBars.BarButtonItem barBtnSave;
        private DevExpress.XtraEditors.LabelControl labelControl35;
        private DevExpress.XtraEditors.LookUpEdit lkpDelivery;
        private DevExpress.XtraEditors.LabelControl labelControl37;
        private DevExpress.XtraEditors.ImageComboBoxEdit cmbCsType;
        private DevExpress.XtraEditors.SimpleButton btn_AddRegion;
        private DevExpress.XtraEditors.LabelControl labelControl36;
        private DevExpress.XtraEditors.LookUpEdit lkp_Regions;
        private DevExpress.XtraEditors.LookUpEdit lkp_CollectEmp;
        private DevExpress.XtraEditors.LabelControl labelControl38;
        private DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit rep_Check;
        private DevExpress.XtraEditors.SimpleButton btnCustomerItems;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand1;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand2;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand3;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand4;
        private DevExpress.XtraGrid.Views.BandedGrid.GridBand gridBand5;
        private DevExpress.XtraEditors.LabelControl labelControl39;
        private DevExpress.XtraEditors.LabelControl labelControl40;
        private DevExpress.XtraEditors.TextEdit txt_Street;
        private DevExpress.XtraEditors.TextEdit txt_Neighborhood;
        private DevExpress.XtraEditors.LabelControl labelControl41;
        private DevExpress.XtraEditors.LookUpEdit lkp_country;
        private DevExpress.XtraEditors.TextEdit txt_Governate;
        private DevExpress.XtraEditors.LabelControl Governate;
        private DevExpress.XtraEditors.TextEdit txt_BuildingNumber;
        private DevExpress.XtraEditors.LabelControl BuildingNumber;
        private DevExpress.XtraEditors.LabelControl labelControl48;
        private DevExpress.XtraEditors.LabelControl labelControl47;
        private DevExpress.XtraEditors.LabelControl labelControl46;
        private DevExpress.XtraEditors.LabelControl labelControl45;
        private DevExpress.XtraEditors.LabelControl labelControl44;
        private DevExpress.XtraEditors.LabelControl labelControl43;
        private DevExpress.XtraEditors.LabelControl labelControl42;
        private DevExpress.XtraEditors.LabelControl labelControl49;
        private DevExpress.XtraEditors.LabelControl labelControl50;
    }
}