﻿namespace Pharmacy.Forms
{
    partial class frm_SL_CustomerList
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_SL_CustomerList));
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtn_Help = new DevExpress.XtraBars.BarButtonItem();
            this.barbtnImportCustomers = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnPrint = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnRefresh = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnOpen = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnNew = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.grd_Customer = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.mi_UpdateCust = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_PriceLevel = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDisc = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_MaxCredit = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colCity = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colAddress = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Mobile = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colTel = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CusNameEn = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CategoryId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Representative = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CusNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CusCode = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CustomerId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colAccountId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_SalesEmpId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_salesEmp = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.col_IdNumber = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CustomerGroup = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Rep_Mobile = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Rep_ID = new DevExpress.XtraGrid.Columns.GridColumn();
            this.Region = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_TaxCardNumber = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Country = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Street = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Governate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_csType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_BuildingNumber = new DevExpress.XtraGrid.Columns.GridColumn();
            this.popupData = new DevExpress.XtraBars.PopupControlContainer(this.components);
            this.panel_data = new DevExpress.XtraEditors.PanelControl();
            this.chk_IsActive = new DevExpress.XtraEditors.CheckEdit();
            this.chk_IsBlocked = new DevExpress.XtraEditors.CheckEdit();
            this.lkp_SalesEmp = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl25 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.lkpPriceLevel = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl24 = new DevExpress.XtraEditors.LabelControl();
            this.lkp_Category = new DevExpress.XtraEditors.LookUpEdit();
            this.btnClosePopup = new DevExpress.XtraEditors.SimpleButton();
            this.btn_Continue = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grd_Customer)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_salesEmp)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupData)).BeginInit();
            this.popupData.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.panel_data)).BeginInit();
            this.panel_data.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chk_IsActive.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_IsBlocked.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_SalesEmp.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpPriceLevel.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Category.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnNew,
            this.barBtnOpen,
            this.barBtn_Help,
            this.barBtnClose,
            this.barBtnRefresh,
            this.barBtnPrint,
            this.barbtnImportCustomers});
            this.barManager1.MaxItemId = 29;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(567, 147);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_Help),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barbtnImportCustomers, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnPrint),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnRefresh),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnOpen),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnNew),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.KeyTip, this.barBtnClose, "", false, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.Standard, "", "")});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtn_Help
            // 
            this.barBtn_Help.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtn_Help, "barBtn_Help");
            this.barBtn_Help.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtn_Help.Id = 2;
            this.barBtn_Help.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtn_Help.Name = "barBtn_Help";
            this.barBtn_Help.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtn_Help.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Help_ItemClick);
            // 
            // barbtnImportCustomers
            // 
            this.barbtnImportCustomers.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barbtnImportCustomers.Glyph = ((System.Drawing.Image)(resources.GetObject("barbtnImportCustomers.Glyph")));
            this.barbtnImportCustomers.Id = 28;
            this.barbtnImportCustomers.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("barbtnImportCustomers.LargeGlyph")));
            this.barbtnImportCustomers.Name = "barbtnImportCustomers";
            this.barbtnImportCustomers.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            this.barbtnImportCustomers.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barbtnImportCustomers_ItemClick);
            // 
            // barBtnPrint
            // 
            this.barBtnPrint.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnPrint, "barBtnPrint");
            this.barBtnPrint.Glyph = global::Pharmacy.Properties.Resources.prnt;
            this.barBtnPrint.Id = 27;
            this.barBtnPrint.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.P));
            this.barBtnPrint.Name = "barBtnPrint";
            this.barBtnPrint.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnPrint.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnPrint_ItemClick);
            // 
            // barBtnRefresh
            // 
            this.barBtnRefresh.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnRefresh, "barBtnRefresh");
            this.barBtnRefresh.Glyph = global::Pharmacy.Properties.Resources.refresh;
            this.barBtnRefresh.Id = 26;
            this.barBtnRefresh.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.R));
            this.barBtnRefresh.Name = "barBtnRefresh";
            this.barBtnRefresh.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnRefresh.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Refresh_ItemClick);
            // 
            // barBtnOpen
            // 
            this.barBtnOpen.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnOpen, "barBtnOpen");
            this.barBtnOpen.Glyph = global::Pharmacy.Properties.Resources.open;
            this.barBtnOpen.Id = 1;
            this.barBtnOpen.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.O));
            this.barBtnOpen.Name = "barBtnOpen";
            this.barBtnOpen.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnOpen.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Open_ItemClick);
            // 
            // barBtnNew
            // 
            this.barBtnNew.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnNew, "barBtnNew");
            this.barBtnNew.Glyph = global::Pharmacy.Properties.Resources._new;
            this.barBtnNew.Id = 0;
            this.barBtnNew.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.N));
            this.barBtnNew.Name = "barBtnNew";
            this.barBtnNew.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnNew.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_New_ItemClick);
            // 
            // barBtnClose
            // 
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 25;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Close_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.CausesValidation = false;
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // grd_Customer
            // 
            resources.ApplyResources(this.grd_Customer, "grd_Customer");
            this.grd_Customer.ContextMenuStrip = this.contextMenuStrip1;
            this.grd_Customer.Cursor = System.Windows.Forms.Cursors.Default;
            this.grd_Customer.MainView = this.gridView1;
            this.grd_Customer.MenuManager = this.barManager1;
            this.grd_Customer.Name = "grd_Customer";
            this.grd_Customer.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.rep_salesEmp});
            this.grd_Customer.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            this.grd_Customer.Click += new System.EventHandler(this.grd_Customer_Click);
            this.grd_Customer.DoubleClick += new System.EventHandler(this.grd_Customer_DoubleClick);
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.mi_UpdateCust});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            this.contextMenuStrip1.ShowImageMargin = false;
            resources.ApplyResources(this.contextMenuStrip1, "contextMenuStrip1");
            this.contextMenuStrip1.Opening += new System.ComponentModel.CancelEventHandler(this.contextMenuStrip1_Opening);
            // 
            // mi_UpdateCust
            // 
            this.mi_UpdateCust.Name = "mi_UpdateCust";
            resources.ApplyResources(this.mi_UpdateCust, "mi_UpdateCust");
            this.mi_UpdateCust.Click += new System.EventHandler(this.mi_UpdateCust_Click);
            // 
            // gridView1
            // 
            this.gridView1.Appearance.GroupPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.GroupPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView1.Appearance.Row.Options.UseTextOptions = true;
            this.gridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.AppearancePrint.FooterPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.BorderColor")));
            this.gridView1.AppearancePrint.FooterPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.ForeColor")));
            this.gridView1.AppearancePrint.FooterPanel.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.FooterPanel.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.GroupFooter.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.BorderColor")));
            this.gridView1.AppearancePrint.GroupFooter.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.ForeColor")));
            this.gridView1.AppearancePrint.GroupFooter.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.GroupFooter.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.GroupRow.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupRow.BorderColor")));
            this.gridView1.AppearancePrint.GroupRow.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupRow.ForeColor")));
            this.gridView1.AppearancePrint.GroupRow.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.GroupRow.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.BorderColor")));
            this.gridView1.AppearancePrint.HeaderPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.ForeColor")));
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.Lines.BackColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Lines.BackColor")));
            this.gridView1.AppearancePrint.Lines.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Lines.ForeColor")));
            this.gridView1.AppearancePrint.Lines.Options.UseBackColor = true;
            this.gridView1.AppearancePrint.Lines.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.Row.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Row.BorderColor")));
            this.gridView1.AppearancePrint.Row.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Row.ForeColor")));
            this.gridView1.AppearancePrint.Row.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.Row.Options.UseForeColor = true;
            this.gridView1.ColumnPanelRowHeight = 35;
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_PriceLevel,
            this.colDisc,
            this.col_MaxCredit,
            this.colCity,
            this.colAddress,
            this.col_Mobile,
            this.colTel,
            this.col_CusNameEn,
            this.col_CategoryId,
            this.col_Representative,
            this.col_CusNameAr,
            this.col_CusCode,
            this.col_CustomerId,
            this.colAccountId,
            this.col_SalesEmpId,
            this.col_IdNumber,
            this.col_CustomerGroup,
            this.col_Rep_Mobile,
            this.col_Rep_ID,
            this.Region,
            this.col_TaxCardNumber,
            this.col_Country,
            this.col_Street,
            this.col_Governate,
            this.col_csType,
            this.col_BuildingNumber});
            this.gridView1.CustomizationFormBounds = new System.Drawing.Rectangle(914, 451, 210, 277);
            this.gridView1.GridControl = this.grd_Customer;
            resources.ApplyResources(this.gridView1, "gridView1");
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsBehavior.ReadOnly = true;
            this.gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView1.OptionsSelection.MultiSelect = true;
            this.gridView1.OptionsView.RowAutoHeight = true;
            this.gridView1.OptionsView.ShowAutoFilterRow = true;
            this.gridView1.OptionsView.ShowFooter = true;
            // 
            // col_PriceLevel
            // 
            resources.ApplyResources(this.col_PriceLevel, "col_PriceLevel");
            this.col_PriceLevel.FieldName = "PriceLevel";
            this.col_PriceLevel.Name = "col_PriceLevel";
            // 
            // colDisc
            // 
            resources.ApplyResources(this.colDisc, "colDisc");
            this.colDisc.FieldName = "DiscountRatio";
            this.colDisc.Name = "colDisc";
            // 
            // col_MaxCredit
            // 
            resources.ApplyResources(this.col_MaxCredit, "col_MaxCredit");
            this.col_MaxCredit.FieldName = "MaxCredit";
            this.col_MaxCredit.Name = "col_MaxCredit";
            // 
            // colCity
            // 
            resources.ApplyResources(this.colCity, "colCity");
            this.colCity.FieldName = "City";
            this.colCity.Name = "colCity";
            // 
            // colAddress
            // 
            resources.ApplyResources(this.colAddress, "colAddress");
            this.colAddress.FieldName = "Address";
            this.colAddress.Name = "colAddress";
            this.colAddress.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // col_Mobile
            // 
            resources.ApplyResources(this.col_Mobile, "col_Mobile");
            this.col_Mobile.FieldName = "Mobile";
            this.col_Mobile.Name = "col_Mobile";
            // 
            // colTel
            // 
            resources.ApplyResources(this.colTel, "colTel");
            this.colTel.FieldName = "Tel";
            this.colTel.Name = "colTel";
            // 
            // col_CusNameEn
            // 
            resources.ApplyResources(this.col_CusNameEn, "col_CusNameEn");
            this.col_CusNameEn.FieldName = "CusNameEn";
            this.col_CusNameEn.Name = "col_CusNameEn";
            this.col_CusNameEn.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // col_CategoryId
            // 
            resources.ApplyResources(this.col_CategoryId, "col_CategoryId");
            this.col_CategoryId.FieldName = "CGNameAr";
            this.col_CategoryId.Name = "col_CategoryId";
            // 
            // col_Representative
            // 
            resources.ApplyResources(this.col_Representative, "col_Representative");
            this.col_Representative.FieldName = "Representative";
            this.col_Representative.Name = "col_Representative";
            // 
            // col_CusNameAr
            // 
            resources.ApplyResources(this.col_CusNameAr, "col_CusNameAr");
            this.col_CusNameAr.FieldName = "CusNameAr";
            this.col_CusNameAr.Name = "col_CusNameAr";
            this.col_CusNameAr.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // col_CusCode
            // 
            resources.ApplyResources(this.col_CusCode, "col_CusCode");
            this.col_CusCode.FieldName = "CusCode";
            this.col_CusCode.Name = "col_CusCode";
            this.col_CusCode.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // col_CustomerId
            // 
            resources.ApplyResources(this.col_CustomerId, "col_CustomerId");
            this.col_CustomerId.FieldName = "CustomerId";
            this.col_CustomerId.Name = "col_CustomerId";
            this.col_CustomerId.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // colAccountId
            // 
            resources.ApplyResources(this.colAccountId, "colAccountId");
            this.colAccountId.FieldName = "AccountId";
            this.colAccountId.Name = "colAccountId";
            this.colAccountId.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // col_SalesEmpId
            // 
            this.col_SalesEmpId.AppearanceCell.Options.UseTextOptions = true;
            this.col_SalesEmpId.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_SalesEmpId.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_SalesEmpId.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_SalesEmpId.AppearanceHeader.Options.UseTextOptions = true;
            this.col_SalesEmpId.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_SalesEmpId.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_SalesEmpId.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_SalesEmpId, "col_SalesEmpId");
            this.col_SalesEmpId.ColumnEdit = this.rep_salesEmp;
            this.col_SalesEmpId.FieldName = "SalesEmpId";
            this.col_SalesEmpId.Name = "col_SalesEmpId";
            // 
            // rep_salesEmp
            // 
            resources.ApplyResources(this.rep_salesEmp, "rep_salesEmp");
            this.rep_salesEmp.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_salesEmp.Buttons"))))});
            this.rep_salesEmp.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_salesEmp.Columns"), resources.GetString("rep_salesEmp.Columns1")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("rep_salesEmp.Columns2"), resources.GetString("rep_salesEmp.Columns3"), ((int)(resources.GetObject("rep_salesEmp.Columns4"))), ((DevExpress.Utils.FormatType)(resources.GetObject("rep_salesEmp.Columns5"))), resources.GetString("rep_salesEmp.Columns6"), ((bool)(resources.GetObject("rep_salesEmp.Columns7"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("rep_salesEmp.Columns8"))))});
            this.rep_salesEmp.DisplayMember = "EmpName";
            this.rep_salesEmp.Name = "rep_salesEmp";
            this.rep_salesEmp.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.rep_salesEmp.ValueMember = "EmpId";
            // 
            // col_IdNumber
            // 
            resources.ApplyResources(this.col_IdNumber, "col_IdNumber");
            this.col_IdNumber.FieldName = "IdNumber";
            this.col_IdNumber.Name = "col_IdNumber";
            this.col_IdNumber.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // col_CustomerGroup
            // 
            resources.ApplyResources(this.col_CustomerGroup, "col_CustomerGroup");
            this.col_CustomerGroup.FieldName = "CustGroup";
            this.col_CustomerGroup.Name = "col_CustomerGroup";
            // 
            // col_Rep_Mobile
            // 
            resources.ApplyResources(this.col_Rep_Mobile, "col_Rep_Mobile");
            this.col_Rep_Mobile.FieldName = "Rep_Mobile";
            this.col_Rep_Mobile.Name = "col_Rep_Mobile";
            // 
            // col_Rep_ID
            // 
            resources.ApplyResources(this.col_Rep_ID, "col_Rep_ID");
            this.col_Rep_ID.FieldName = "Rep_ID";
            this.col_Rep_ID.Name = "col_Rep_ID";
            // 
            // Region
            // 
            this.Region.FieldName = "Region";
            this.Region.Name = "Region";
            // 
            // col_TaxCardNumber
            // 
            resources.ApplyResources(this.col_TaxCardNumber, "col_TaxCardNumber");
            this.col_TaxCardNumber.FieldName = "TaxCardNumber";
            this.col_TaxCardNumber.Name = "col_TaxCardNumber";
            // 
            // col_Country
            // 
            this.col_Country.FieldName = "Country";
            this.col_Country.Name = "col_Country";
            // 
            // col_Street
            // 
            resources.ApplyResources(this.col_Street, "col_Street");
            this.col_Street.FieldName = "Street";
            this.col_Street.Name = "col_Street";
            // 
            // col_Governate
            // 
            resources.ApplyResources(this.col_Governate, "col_Governate");
            this.col_Governate.FieldName = "Governate";
            this.col_Governate.Name = "col_Governate";
            // 
            // col_csType
            // 
            resources.ApplyResources(this.col_csType, "col_csType");
            this.col_csType.FieldName = "csType";
            this.col_csType.Name = "col_csType";
            // 
            // col_BuildingNumber
            // 
            resources.ApplyResources(this.col_BuildingNumber, "col_BuildingNumber");
            this.col_BuildingNumber.FieldName = "BuildingNumber";
            this.col_BuildingNumber.Name = "col_BuildingNumber";
            // 
            // popupData
            // 
            this.popupData.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.popupData.Controls.Add(this.panel_data);
            resources.ApplyResources(this.popupData, "popupData");
            this.popupData.Manager = this.barManager1;
            this.popupData.Name = "popupData";
            // 
            // panel_data
            // 
            this.panel_data.Controls.Add(this.chk_IsActive);
            this.panel_data.Controls.Add(this.chk_IsBlocked);
            this.panel_data.Controls.Add(this.lkp_SalesEmp);
            this.panel_data.Controls.Add(this.labelControl25);
            this.panel_data.Controls.Add(this.labelControl6);
            this.panel_data.Controls.Add(this.lkpPriceLevel);
            this.panel_data.Controls.Add(this.labelControl24);
            this.panel_data.Controls.Add(this.lkp_Category);
            this.panel_data.Controls.Add(this.btnClosePopup);
            this.panel_data.Controls.Add(this.btn_Continue);
            resources.ApplyResources(this.panel_data, "panel_data");
            this.panel_data.Name = "panel_data";
            // 
            // chk_IsActive
            // 
            resources.ApplyResources(this.chk_IsActive, "chk_IsActive");
            this.chk_IsActive.MenuManager = this.barManager1;
            this.chk_IsActive.Name = "chk_IsActive";
            this.chk_IsActive.Properties.Appearance.Options.UseTextOptions = true;
            this.chk_IsActive.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chk_IsActive.Properties.AutoWidth = true;
            this.chk_IsActive.Properties.Caption = resources.GetString("chk_IsActive.Properties.Caption");
            this.chk_IsActive.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("chk_IsActive.Properties.GlyphAlignment")));
            // 
            // chk_IsBlocked
            // 
            resources.ApplyResources(this.chk_IsBlocked, "chk_IsBlocked");
            this.chk_IsBlocked.MenuManager = this.barManager1;
            this.chk_IsBlocked.Name = "chk_IsBlocked";
            this.chk_IsBlocked.Properties.Appearance.Options.UseTextOptions = true;
            this.chk_IsBlocked.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chk_IsBlocked.Properties.AutoWidth = true;
            this.chk_IsBlocked.Properties.Caption = resources.GetString("chk_IsBlocked.Properties.Caption");
            this.chk_IsBlocked.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("chk_IsBlocked.Properties.GlyphAlignment")));
            // 
            // lkp_SalesEmp
            // 
            resources.ApplyResources(this.lkp_SalesEmp, "lkp_SalesEmp");
            this.lkp_SalesEmp.EnterMoveNextControl = true;
            this.lkp_SalesEmp.MenuManager = this.barManager1;
            this.lkp_SalesEmp.Name = "lkp_SalesEmp";
            this.lkp_SalesEmp.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkp_SalesEmp.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_SalesEmp.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_SalesEmp.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_SalesEmp.Properties.Buttons"))))});
            this.lkp_SalesEmp.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_SalesEmp.Properties.Columns"), resources.GetString("lkp_SalesEmp.Properties.Columns1"), ((int)(resources.GetObject("lkp_SalesEmp.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_SalesEmp.Properties.Columns3"))), resources.GetString("lkp_SalesEmp.Properties.Columns4"), ((bool)(resources.GetObject("lkp_SalesEmp.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_SalesEmp.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_SalesEmp.Properties.Columns7"), resources.GetString("lkp_SalesEmp.Properties.Columns8"), ((int)(resources.GetObject("lkp_SalesEmp.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_SalesEmp.Properties.Columns10"))), resources.GetString("lkp_SalesEmp.Properties.Columns11"), ((bool)(resources.GetObject("lkp_SalesEmp.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_SalesEmp.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_SalesEmp.Properties.Columns14"), resources.GetString("lkp_SalesEmp.Properties.Columns15"), ((int)(resources.GetObject("lkp_SalesEmp.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_SalesEmp.Properties.Columns17"))), resources.GetString("lkp_SalesEmp.Properties.Columns18"), ((bool)(resources.GetObject("lkp_SalesEmp.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_SalesEmp.Properties.Columns20"))))});
            this.lkp_SalesEmp.Properties.DisplayMember = "EmpName";
            this.lkp_SalesEmp.Properties.NullText = resources.GetString("lkp_SalesEmp.Properties.NullText");
            this.lkp_SalesEmp.Properties.ValueMember = "AccountId";
            // 
            // labelControl25
            // 
            resources.ApplyResources(this.labelControl25, "labelControl25");
            this.labelControl25.Name = "labelControl25";
            // 
            // labelControl6
            // 
            resources.ApplyResources(this.labelControl6, "labelControl6");
            this.labelControl6.Name = "labelControl6";
            // 
            // lkpPriceLevel
            // 
            resources.ApplyResources(this.lkpPriceLevel, "lkpPriceLevel");
            this.lkpPriceLevel.EnterMoveNextControl = true;
            this.lkpPriceLevel.Name = "lkpPriceLevel";
            this.lkpPriceLevel.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkpPriceLevel.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpPriceLevel.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpPriceLevel.Properties.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.lkpPriceLevel.Properties.AppearanceDropDown.Options.UseTextOptions = true;
            this.lkpPriceLevel.Properties.AppearanceDropDown.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkpPriceLevel.Properties.AppearanceDropDownHeader.Options.UseTextOptions = true;
            this.lkpPriceLevel.Properties.AppearanceDropDownHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkpPriceLevel.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkpPriceLevel.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpPriceLevel.Properties.Buttons"))))});
            this.lkpPriceLevel.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpPriceLevel.Properties.Columns"), resources.GetString("lkpPriceLevel.Properties.Columns1"), ((int)(resources.GetObject("lkpPriceLevel.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpPriceLevel.Properties.Columns3"))), resources.GetString("lkpPriceLevel.Properties.Columns4"), ((bool)(resources.GetObject("lkpPriceLevel.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpPriceLevel.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpPriceLevel.Properties.Columns7"), resources.GetString("lkpPriceLevel.Properties.Columns8"), ((int)(resources.GetObject("lkpPriceLevel.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpPriceLevel.Properties.Columns10"))), resources.GetString("lkpPriceLevel.Properties.Columns11"), ((bool)(resources.GetObject("lkpPriceLevel.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpPriceLevel.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpPriceLevel.Properties.Columns14"), resources.GetString("lkpPriceLevel.Properties.Columns15"), ((int)(resources.GetObject("lkpPriceLevel.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpPriceLevel.Properties.Columns17"))), resources.GetString("lkpPriceLevel.Properties.Columns18"), ((bool)(resources.GetObject("lkpPriceLevel.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpPriceLevel.Properties.Columns20")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpPriceLevel.Properties.Columns21"), resources.GetString("lkpPriceLevel.Properties.Columns22"), ((int)(resources.GetObject("lkpPriceLevel.Properties.Columns23"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpPriceLevel.Properties.Columns24"))), resources.GetString("lkpPriceLevel.Properties.Columns25"), ((bool)(resources.GetObject("lkpPriceLevel.Properties.Columns26"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpPriceLevel.Properties.Columns27"))))});
            this.lkpPriceLevel.Properties.DisplayMember = "PLNAme";
            this.lkpPriceLevel.Properties.NullText = resources.GetString("lkpPriceLevel.Properties.NullText");
            this.lkpPriceLevel.Properties.PopupSizeable = false;
            this.lkpPriceLevel.Properties.ValueMember = "PriceLevelId";
            // 
            // labelControl24
            // 
            resources.ApplyResources(this.labelControl24, "labelControl24");
            this.labelControl24.Name = "labelControl24";
            // 
            // lkp_Category
            // 
            resources.ApplyResources(this.lkp_Category, "lkp_Category");
            this.lkp_Category.EnterMoveNextControl = true;
            this.lkp_Category.Name = "lkp_Category";
            this.lkp_Category.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkp_Category.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_Category.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_Category.Properties.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.lkp_Category.Properties.AppearanceDropDown.Options.UseTextOptions = true;
            this.lkp_Category.Properties.AppearanceDropDown.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkp_Category.Properties.AppearanceDropDownHeader.Options.UseTextOptions = true;
            this.lkp_Category.Properties.AppearanceDropDownHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkp_Category.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_Category.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_Category.Properties.Buttons"))))});
            this.lkp_Category.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Category.Properties.Columns"), resources.GetString("lkp_Category.Properties.Columns1")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Category.Properties.Columns2"), resources.GetString("lkp_Category.Properties.Columns3"), ((int)(resources.GetObject("lkp_Category.Properties.Columns4"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_Category.Properties.Columns5"))), resources.GetString("lkp_Category.Properties.Columns6"), ((bool)(resources.GetObject("lkp_Category.Properties.Columns7"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_Category.Properties.Columns8")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Category.Properties.Columns9"), resources.GetString("lkp_Category.Properties.Columns10"), ((int)(resources.GetObject("lkp_Category.Properties.Columns11"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_Category.Properties.Columns12"))), resources.GetString("lkp_Category.Properties.Columns13"), ((bool)(resources.GetObject("lkp_Category.Properties.Columns14"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_Category.Properties.Columns15")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Category.Properties.Columns16"), resources.GetString("lkp_Category.Properties.Columns17"), ((int)(resources.GetObject("lkp_Category.Properties.Columns18"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_Category.Properties.Columns19"))), resources.GetString("lkp_Category.Properties.Columns20"), ((bool)(resources.GetObject("lkp_Category.Properties.Columns21"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_Category.Properties.Columns22"))))});
            this.lkp_Category.Properties.DisplayMember = "CGNameAr";
            this.lkp_Category.Properties.NullText = resources.GetString("lkp_Category.Properties.NullText");
            this.lkp_Category.Properties.PopupSizeable = false;
            this.lkp_Category.Properties.ValueMember = "CustomerGroupId";
            // 
            // btnClosePopup
            // 
            resources.ApplyResources(this.btnClosePopup, "btnClosePopup");
            this.btnClosePopup.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnClosePopup.Image = global::Pharmacy.Properties.Resources.clse;
            this.btnClosePopup.Name = "btnClosePopup";
            this.btnClosePopup.Click += new System.EventHandler(this.btnClosePopup_Click);
            // 
            // btn_Continue
            // 
            resources.ApplyResources(this.btn_Continue, "btn_Continue");
            this.btn_Continue.Name = "btn_Continue";
            this.btn_Continue.Click += new System.EventHandler(this.btn_Continue_Click);
            // 
            // frm_SL_CustomerList
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.popupData);
            this.Controls.Add(this.grd_Customer);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.Name = "frm_SL_CustomerList";
            this.Activated += new System.EventHandler(this.frm_SL_CustomerList_Activated);
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_SL_CustomerList_FormClosing);
            this.Load += new System.EventHandler(this.frm_IC_CustomerList_Load);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grd_Customer)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_salesEmp)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupData)).EndInit();
            this.popupData.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.panel_data)).EndInit();
            this.panel_data.ResumeLayout(false);
            this.panel_data.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chk_IsActive.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_IsBlocked.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_SalesEmp.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpPriceLevel.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Category.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnNew;
        private DevExpress.XtraBars.BarButtonItem barBtnOpen;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtn_Help;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraGrid.GridControl grd_Customer;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn colAddress;
        private DevExpress.XtraGrid.Columns.GridColumn colTel;
        private DevExpress.XtraGrid.Columns.GridColumn col_CusNameEn;
        private DevExpress.XtraGrid.Columns.GridColumn col_CusNameAr;
        private DevExpress.XtraGrid.Columns.GridColumn col_CusCode;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraBars.BarButtonItem barBtnRefresh;
        private DevExpress.XtraGrid.Columns.GridColumn col_CustomerId;
        private DevExpress.XtraGrid.Columns.GridColumn col_MaxCredit;
        private DevExpress.XtraGrid.Columns.GridColumn col_Mobile;
        private DevExpress.XtraBars.BarButtonItem barBtnPrint;
        private DevExpress.XtraGrid.Columns.GridColumn colAccountId;
        private DevExpress.XtraGrid.Columns.GridColumn colDisc;
        private DevExpress.XtraGrid.Columns.GridColumn col_PriceLevel;
        private DevExpress.XtraGrid.Columns.GridColumn colCity;
        private DevExpress.XtraGrid.Columns.GridColumn col_CategoryId;
        private DevExpress.XtraGrid.Columns.GridColumn col_SalesEmpId;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit rep_salesEmp;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem mi_UpdateCust;
        private DevExpress.XtraBars.PopupControlContainer popupData;
        private DevExpress.XtraEditors.PanelControl panel_data;
        private DevExpress.XtraEditors.SimpleButton btnClosePopup;
        private DevExpress.XtraEditors.SimpleButton btn_Continue;
        private DevExpress.XtraEditors.LabelControl labelControl24;
        private DevExpress.XtraEditors.LookUpEdit lkp_Category;
        private DevExpress.XtraEditors.LookUpEdit lkp_SalesEmp;
        private DevExpress.XtraEditors.LabelControl labelControl25;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LookUpEdit lkpPriceLevel;
        private DevExpress.XtraGrid.Columns.GridColumn col_IdNumber;
        private DevExpress.XtraEditors.CheckEdit chk_IsActive;
        private DevExpress.XtraEditors.CheckEdit chk_IsBlocked;
        private DevExpress.XtraGrid.Columns.GridColumn col_CustomerGroup;
        private DevExpress.XtraGrid.Columns.GridColumn col_Representative;
        private DevExpress.XtraGrid.Columns.GridColumn col_Rep_Mobile;
        private DevExpress.XtraGrid.Columns.GridColumn col_Rep_ID;
        private DevExpress.XtraGrid.Columns.GridColumn Region;
        private DevExpress.XtraBars.BarButtonItem barbtnImportCustomers;
        private DevExpress.XtraGrid.Columns.GridColumn col_TaxCardNumber;
        private DevExpress.XtraGrid.Columns.GridColumn col_Country;
        private DevExpress.XtraGrid.Columns.GridColumn col_Street;
        private DevExpress.XtraGrid.Columns.GridColumn col_Governate;
        private DevExpress.XtraGrid.Columns.GridColumn col_csType;
        private DevExpress.XtraGrid.Columns.GridColumn col_BuildingNumber;
    }
}