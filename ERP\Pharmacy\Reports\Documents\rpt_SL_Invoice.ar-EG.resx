﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="Detail.HeightF" type="System.Single, mscorlib">
    <value>0</value>
  </data>
  <data name="Detail.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="lbl_Paied.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>11.2078981, 227.441147</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="lbl_Paied.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>99.52701, 25.45842</value>
  </data>
  <data name="lbl_Net.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>11.2078981, 201.982376</value>
  </data>
  <data name="lbl_Net.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>99.52701, 25.45842</value>
  </data>
  <data name="lbl_Remains.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>11.2078981, 252.899429</value>
  </data>
  <data name="lbl_Remains.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>99.52701, 25.45842</value>
  </data>
  <data name="lbl_DiscountR.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>112.4942, 26.5000668</value>
  </data>
  <data name="lbl_DiscountR.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>26.6774445, 24.9999962</value>
  </data>
  <data name="lbl_TaxV.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>11.2078981, 76.95034</value>
  </data>
  <data name="lbl_TaxV.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>101.2863, 24.7758255</value>
  </data>
  <data name="lbl_Total.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>11.207901, 1.88426971</value>
  </data>
  <data name="lbl_Total.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>101.2863, 25</value>
  </data>
  <data name="lbl_ExpensesV.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>11.2078829, 151.501968</value>
  </data>
  <data name="lbl_ExpensesV.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>99.5270538, 25</value>
  </data>
  <data name="xrLabel17.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>139.171661, 252.899673</value>
  </data>
  <data name="xrLabel17.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>124.735382, 25.45842</value>
  </data>
  <data name="xrLabel17.Text" xml:space="preserve">
    <value>المتبقي</value>
  </data>
  <data name="xrLabel23.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>138.885727, 201.982376</value>
  </data>
  <data name="xrLabel23.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>125.021225, 25.4584045</value>
  </data>
  <data name="xrLabel23.Text" xml:space="preserve">
    <value>الصافي</value>
  </data>
  <data name="xrLabel24.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>139.171661, 227.441147</value>
  </data>
  <data name="xrLabel24.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>124.735382, 25.4584656</value>
  </data>
  <data name="xrLabel24.Text" xml:space="preserve">
    <value>المدفوع</value>
  </data>
  <data name="xrLabel19.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>139.171646, 27.0356236</value>
  </data>
  <data name="xrLabel19.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>126.7805, 25</value>
  </data>
  <data name="xrLabel19.Text" xml:space="preserve">
    <value>خصم</value>
  </data>
  <data name="xrLabel20.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>139.171661, 152.3946</value>
  </data>
  <data name="xrLabel20.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>125.02121, 25</value>
  </data>
  <data name="xrLabel20.Text" xml:space="preserve">
    <value>تكاليف أخرى</value>
  </data>
  <data name="xrLabel15.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>139.171646, 1.88430154</value>
  </data>
  <data name="xrLabel15.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>126.7805, 25</value>
  </data>
  <data name="xrLabel15.Text" xml:space="preserve">
    <value>الإجمالي</value>
  </data>
  <data name="xrLabel16.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>139.171646, 77.33827</value>
  </data>
  <data name="xrLabel16.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>126.7805, 25</value>
  </data>
  <data name="xrLabel16.Text" xml:space="preserve">
    <value>الضريبة</value>
  </data>
  <data name="lbl_CommercialBook.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>224.0146, 309.833344</value>
  </data>
  <data name="lbl_CompanyTaxNumber.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>242.817627, 253.875</value>
  </data>
  <data name="txtregisterId.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>632.7416, 16.708437</value>
  </data>
  <data name="txtregisterId.Text" xml:space="preserve">
    <value>الرقم المدني</value>
  </data>
  <data name="txtFileNum.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>629.924255, 65.70835</value>
  </data>
  <data name="txtFileNum.Text" xml:space="preserve">
    <value>رقم الملف الضريبي</value>
  </data>
  <data name="txtTaxCardNumber.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>426.924225, 41.2083321</value>
  </data>
  <data name="txtTaxFileNumber.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>426.924225, 68.07295</value>
  </data>
  <data name="Regs_ID.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>426.9243, 16.708437</value>
  </data>
  <data name="txtCardNum.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>629.924255, 41.2083321</value>
  </data>
  <data name="txtCardNum.Text" xml:space="preserve">
    <value>البطاقة الضريبية</value>
  </data>
  <data name="xrLabel28.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 42.7083321</value>
  </data>
  <data name="txt_AttnMr.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>0, 42.7083321</value>
  </data>
  <data name="txt_AttnMr.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="qrCode.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>42.07096, 358.833252</value>
  </data>
  <data name="qrCode.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>192.241669, 167.947937</value>
  </data>
  <data name="qrCode.Text" xml:space="preserve">
    <value>LinkIT Information Technology</value>
  </data>
  <data name="xrLabel27.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>631.583069, 358.833252</value>
  </data>
  <data name="xrLabel27.Text" xml:space="preserve">
    <value>تليفون</value>
  </data>
  <data name="Address.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>412.7885, 334.333282</value>
  </data>
  <data name="xrLabel30.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>631.583069, 334.333282</value>
  </data>
  <data name="xrLabel30.Text" xml:space="preserve">
    <value>عنوان الفرع</value>
  </data>
  <data name="Tel.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>412.7885, 358.833252</value>
  </data>
  <data name="xrLabel32.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>631.583069, 383.333282</value>
  </data>
  <data name="xrLabel32.Text" xml:space="preserve">
    <value>اسم المدير</value>
  </data>
  <data name="ManagerName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>412.7885, 383.333344</value>
  </data>
  <data name="txtDelivery.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="txtDelivery.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>11.0943794, 253.874908</value>
  </data>
  <data name="txtDelivery.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="txtDelivery.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="xrBarCode_Voucher.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>309.4591, 241.374908</value>
  </data>
  <data name="lblDue.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="lblDue.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>421.063538, 418.531158</value>
  </data>
  <data name="lblDue.Text" xml:space="preserve">
    <value>تاريخ الاستحقاق</value>
  </data>
  <data name="lblDue.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_DueDate.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_DueDate.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>300.7162, 115.166763</value>
  </data>
  <data name="lbl_DueDate.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>120.347382, 24.4999847</value>
  </data>
  <data name="lbl_DueDate.Text" xml:space="preserve">
    <value>1/2/2013</value>
  </data>
  <data name="lbl_DueDate.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_ProcessName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>421.063568, 115.166725</value>
  </data>
  <data name="lbl_SourceCode.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>429.8485, 298.2394</value>
  </data>
  <data name="xrLine1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>10.9944658, 190.999985</value>
  </data>
  <data name="lbl_PurchaseOrderNo.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>510.952423, 229.375015</value>
  </data>
  <data name="lbl_PurchaseOrderNo.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel22.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrLabel22.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>684.5779, 229.374954</value>
  </data>
  <data name="xrLabel22.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>68.45856, 24.4999847</value>
  </data>
  <data name="xrLabel22.Text" xml:space="preserve">
    <value>أمر شراء</value>
  </data>
  <data name="xrLabel22.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_Shipping.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>10.99453, 278.375</value>
  </data>
  <data name="lbl_Shipping.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>265.24527, 55.9582977</value>
  </data>
  <data name="lbl_Shipping.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="xrLabel18.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrLabel18.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>220.531372, 253.874908</value>
  </data>
  <data name="xrLabel18.Text" xml:space="preserve">
    <value>شحن إلى</value>
  </data>
  <data name="xrLabel18.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel13.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrLabel13.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>184.620026, 204.875</value>
  </data>
  <data name="xrLabel13.Text" xml:space="preserve">
    <value>تاريخ التسليم</value>
  </data>
  <data name="xrLabel13.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_SalesEmp.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>10.99453, 229.374954</value>
  </data>
  <data name="lbl_SalesEmp.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel10.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrLabel10.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>184.620026, 229.375015</value>
  </data>
  <data name="xrLabel10.Text" xml:space="preserve">
    <value>مندوب البيع</value>
  </data>
  <data name="xrLabel10.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_User.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>10.9944658, 164.1667</value>
  </data>
  <data name="lbl_User.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel6.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>184.62, 164.1667</value>
  </data>
  <data name="xrLabel6.Text" xml:space="preserve">
    <value>المستخدم</value>
  </data>
  <data name="xrLabel6.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel4.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrLabel4.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>684.577942, 253.874908</value>
  </data>
  <data name="xrLabel4.Text" xml:space="preserve">
    <value>ملاحظات</value>
  </data>
  <data name="xrLabel4.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_Paymethod.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>465.683716, 160.666656</value>
  </data>
  <data name="lbl_Paymethod.Text" xml:space="preserve">
    <value>آجل/كاش</value>
  </data>
  <data name="lbl_Drawer.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>330.016846, 160.666656</value>
  </data>
  <data name="xrLabel12.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>394.432953, 160.666656</value>
  </data>
  <data name="lbl_store.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>10.9944658, 139.666687</value>
  </data>
  <data name="lbl_store.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel7.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>184.62, 115.166725</value>
  </data>
  <data name="xrLabel7.Text" xml:space="preserve">
    <value>التاريخ</value>
  </data>
  <data name="xrLabel7.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel8.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>184.62, 139.666748</value>
  </data>
  <data name="xrLabel8.Text" xml:space="preserve">
    <value>الفرع</value>
  </data>
  <data name="xrLabel8.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_Serial.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>532.8501, 160.666656</value>
  </data>
  <data name="lbl_Customer.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>291.244751, 204.875</value>
  </data>
  <data name="lbl_Customer.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>384.953, 24.4999847</value>
  </data>
  <data name="lbl_Customer.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel1.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrLabel1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>684.5779, 204.875</value>
  </data>
  <data name="xrLabel1.Text" xml:space="preserve">
    <value>العميل</value>
  </data>
  <data name="xrLabel1.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lblReportName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>184.620026, 79.1667</value>
  </data>
  <data name="lblReportName.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>91.61978, 30</value>
  </data>
  <data name="lblReportName.Text" xml:space="preserve">
    <value>فاتورة بيع</value>
  </data>
  <data name="lblReportName.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="picLogo.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>631.0363, 115.166664</value>
  </data>
  <data name="lbl_date.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>10.9944658, 115.166664</value>
  </data>
  <data name="lbl_date.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_Number.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>10.9944983, 79.1667</value>
  </data>
  <data name="lbl_Number.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>173.625534, 30</value>
  </data>
  <data name="lbl_Number.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lblCompName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>291.244751, 79.1667</value>
  </data>
  <data name="lblCompName.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="lbl_notes.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>394.4331, 278.375031</value>
  </data>
  <data name="lbl_notes.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>358.6034, 55.9582825</value>
  </data>
  <data name="lbl_notes.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="lbl_DeliverDate.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>10.99453, 204.875</value>
  </data>
  <data name="lbl_DeliverDate.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_Updated.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_Updated.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>551.2139, 418.531158</value>
  </data>
  <data name="lbl_Updated.Text" xml:space="preserve">
    <value>..</value>
  </data>
  <data name="lbl_Updated.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_Neighborhood.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>513.0951, 479.182678</value>
  </data>
  <data name="lbl_Neighborhood.Text" xml:space="preserve">
    <value>lbl_Neighborhood</value>
  </data>
  <data name="lbl_Neighborhood.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_Street.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>593.4948, 479.182556</value>
  </data>
  <data name="lbl_Street.Text" xml:space="preserve">
    <value>lbl_Street</value>
  </data>
  <data name="lbl_Street.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_total_after_tax.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>611.568665, 402.041748</value>
  </data>
  <data name="lbl_total_after_tax.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_BalanceBefore.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>450.5225, 252.750336</value>
  </data>
  <data name="lbl_BalanceBefore.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>173.625519, 25.4584656</value>
  </data>
  <data name="lbl_BalanceBefore.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="BottomMargin.HeightF" type="System.Single, mscorlib">
    <value>47.76554</value>
  </data>
  <data name="lbl_TotalQty.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_TotalQty.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>715.4223, 437.416718</value>
  </data>
  <data name="lbl_TotalQty.Text" xml:space="preserve">
    <value>totalQty</value>
  </data>
  <data name="lbl_TotalQty.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_TotalQty.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_TaxR.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>348.755432, 378.5417</value>
  </data>
  <data name="xrTable3.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>23.92219, 351.041656</value>
  </data>
  <data name="cell_ItemDescriptionEn.Text" xml:space="preserve">
    <value>ItemDescriptionEn</value>
  </data>
  <data name="cell_ItemDescription.Weight" type="System.Double, mscorlib">
    <value>0.43406391259207444</value>
  </data>
  <data name="cell_AudiencePrice.Text" xml:space="preserve">
    <value>Audience Price</value>
  </data>
  <data name="cell_Location.Text" xml:space="preserve">
    <value>cell_Location</value>
  </data>
  <data name="xrTable3.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>609.5735, 25</value>
  </data>
  <data name="lbl_salesEmp_Job.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>551.2139, 378.541565</value>
  </data>
  <data name="lbl_ExpensesR.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>448.755463, 378.541565</value>
  </data>
  <data name="xrTable4.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>630.2501, 461.9166</value>
  </data>
  <data name="cell_Pack.Text" xml:space="preserve">
    <value>Pack</value>
  </data>
  <data name="Cell_MUOM.Weight" type="System.Double, mscorlib">
    <value>0.20563037969499964</value>
  </data>
  <data name="xrTable5.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>129.844345, 403.041718</value>
  </data>
  <data name="cell_Batch.Weight" type="System.Double, mscorlib">
    <value>0.187999929476682</value>
  </data>
  <data name="cell_Weight_KG.Text" xml:space="preserve">
    <value>cell_Weight_KG</value>
  </data>
  <data name="cell_SerialNo2.Text" xml:space="preserve">
    <value>cell_SerialNo2</value>
  </data>
  <data name="cell_SerialNo.Text" xml:space="preserve">
    <value>cell_SerialNo</value>
  </data>
  <data name="cell_PiecesCount.Text" xml:space="preserve">
    <value>cell_PiecesCount</value>
  </data>
  <data name="xrTable5.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>637.327637, 25</value>
  </data>
  <data name="lbl_DriverName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>65.61047, 403.541565</value>
  </data>
  <data name="lbl_VehicleNumber.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>115.610535, 403.541565</value>
  </data>
  <data name="lbl_Destination.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>178.110565, 403.541565</value>
  </data>
  <data name="lbl_ScaleWeightSerial.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>235.235657, 403.541565</value>
  </data>
  <data name="pic_ItemPic.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>675.0612, 438.916656</value>
  </data>
  <data name="lbl_BalanceAfter.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>450.5225, 278.208923</value>
  </data>
  <data name="lbl_BalanceAfter.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>173.625519, 25.4584045</value>
  </data>
  <data name="lbl_BalanceAfter.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="xrPageInfo1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>9.999997, 322.8178</value>
  </data>
  <data name="xrPageInfo1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>101.186371, 23.0000153</value>
  </data>
  <data name="xrTable1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>11.2078829, 0</value>
  </data>
  <data name="xrTableCell1.Text" xml:space="preserve">
    <value>الإجمالي</value>
  </data>
  <data name="xrTableCell1.Weight" type="System.Double, mscorlib">
    <value>0.17189329428543596</value>
  </data>
  <data name="xrTableCell13.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="xrTableCell13.Text" xml:space="preserve">
    <value>خصم البونص
</value>
  </data>
  <data name="xrTableCell13.Weight" type="System.Double, mscorlib">
    <value>0.14813627300533871</value>
  </data>
  <data name="xrTableCell9.Text" xml:space="preserve">
    <value>قيمة الخصم</value>
  </data>
  <data name="xrTableCell9.Weight" type="System.Double, mscorlib">
    <value>0.13486517604053994</value>
  </data>
  <data name="xrTableCell4.Text" xml:space="preserve">
    <value>نسبة الخصم</value>
  </data>
  <data name="xrTableCell4.Weight" type="System.Double, mscorlib">
    <value>0.13678694841164632</value>
  </data>
  <data name="xrTableCell7.Text" xml:space="preserve">
    <value>السعر</value>
  </data>
  <data name="xrTableCell7.Weight" type="System.Double, mscorlib">
    <value>0.11325172126285879</value>
  </data>
  <data name="xrTableCell12.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="xrTableCell12.Text" xml:space="preserve">
    <value>ضريبة الجدول
</value>
  </data>
  <data name="xrTableCell11.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="xrTableCell11.Text" xml:space="preserve">
    <value>ضريبة القيمة المضافة</value>
  </data>
  <data name="xrTableCell6.Text" xml:space="preserve">
    <value>الكمية</value>
  </data>
  <data name="xrTableCell6.Weight" type="System.Double, mscorlib">
    <value>0.12046135803767402</value>
  </data>
  <data name="xrTableCell2.Text" xml:space="preserve">
    <value>التشغيلة</value>
  </data>
  <data name="xrTableCell2.Weight" type="System.Double, mscorlib">
    <value>0.18843472059090463</value>
  </data>
  <data name="xrTableCell5.Text" xml:space="preserve">
    <value>وحدة القياس</value>
  </data>
  <data name="xrTableCell5.Weight" type="System.Double, mscorlib">
    <value>0.22730540747084296</value>
  </data>
  <data name="xrTableCell3.Text" xml:space="preserve">
    <value>اسم الصنف</value>
  </data>
  <data name="xrTableCell3.Weight" type="System.Double, mscorlib">
    <value>0.40699194661330151</value>
  </data>
  <data name="xrTableCell8.Text" xml:space="preserve">
    <value>كود</value>
  </data>
  <data name="xrLabel29.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="xrLabel29.Text" xml:space="preserve">
    <value> </value>
  </data>
  <data name="xrLabel29.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrLabel31.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="xrLabel31.Text" xml:space="preserve">
    <value>الضريبة</value>
  </data>
  <data name="xrLabel31.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_Customer_ZIP.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_Customer_ZIP.Text" xml:space="preserve">
    <value>Customer_ZIP</value>
  </data>
  <data name="lbl_Customer_ZIP.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleLeft</value>
  </data>
  <data name="lbl_Customer_City.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_Customer_City.Text" xml:space="preserve">
    <value>Customer_City</value>
  </data>
  <data name="lbl_Customer_City.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleLeft</value>
  </data>
  <data name="lbl_Customer_Address.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_Customer_Address.Text" xml:space="preserve">
    <value>Customer_Address</value>
  </data>
  <data name="lbl_Customer_Address.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleLeft</value>
  </data>
  <data name="lbl_Customer_Country.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_Customer_Country.Text" xml:space="preserve">
    <value>Customer_Country</value>
  </data>
  <data name="lbl_Customer_Country.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleLeft</value>
  </data>
  <data name="lbl_TotalPacks.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_TotalPacks.Text" xml:space="preserve">
    <value>lbl_totalPieces</value>
  </data>
  <data name="lbl_TotalPacks.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lblShift.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lblShift.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>775.896362, 149.000168</value>
  </data>
  <data name="lblShift.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>12.9780884, 24.50003</value>
  </data>
  <data name="lblShift.Text" xml:space="preserve">
    <value>lbl_totalPieces</value>
  </data>
  <data name="lblShift.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_totalPieces.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_totalPieces.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>775.8965, 100.00013</value>
  </data>
  <data name="lbl_totalPieces.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>12.9780884, 24.5000153</value>
  </data>
  <data name="lbl_totalPieces.Text" xml:space="preserve">
    <value>lbl_totalPieces</value>
  </data>
  <data name="lbl_totalPieces.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_TaxFileNumber.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_TaxFileNumber.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>775.896362, 26.5000668</value>
  </data>
  <data name="lbl_TaxFileNumber.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>12.9780884, 24.5000343</value>
  </data>
  <data name="lbl_TaxFileNumber.Text" xml:space="preserve">
    <value>..</value>
  </data>
  <data name="lbl_TaxFileNumber.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_TaxCardNumber.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_TaxCardNumber.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>775.896362, 51.0000877</value>
  </data>
  <data name="lbl_TaxCardNumber.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>12.9780884, 24.50003</value>
  </data>
  <data name="lbl_TaxCardNumber.Text" xml:space="preserve">
    <value>..</value>
  </data>
  <data name="lbl_TaxCardNumber.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_Handing.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_Handing.Text" xml:space="preserve">
    <value> </value>
  </data>
  <data name="lbl_Handing.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lbl_trans.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_trans.Text" xml:space="preserve">
    <value> </value>
  </data>
  <data name="lbl_trans.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrLabel14.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrLabel14.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>624.148132, 252.7505</value>
  </data>
  <data name="xrLabel14.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>126.7805, 25.4584</value>
  </data>
  <data name="xrLabel14.Text" xml:space="preserve">
    <value>الرصيد قبل الفاتورة</value>
  </data>
  <data name="xrLabel14.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel21.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrLabel21.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>624.148132, 278.208862</value>
  </data>
  <data name="xrLabel21.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>126.494659, 25.4584656</value>
  </data>
  <data name="xrLabel21.Text" xml:space="preserve">
    <value>الرصيد بعد الفاتورة</value>
  </data>
  <data name="xrLabel21.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_Cust_Address.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_Cust_Address.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>775.896362, 75.50011</value>
  </data>
  <data name="lbl_Cust_Address.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>12.9780884, 24.5000229</value>
  </data>
  <data name="lbl_Cust_Address.Text" xml:space="preserve">
    <value>..</value>
  </data>
  <data name="lbl_Cust_Address.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_Cust_Tel.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_Cust_Tel.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>775.896362, 173.500244</value>
  </data>
  <data name="lbl_Cust_Tel.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>12.9780884, 24.50003</value>
  </data>
  <data name="lbl_Cust_Tel.Text" xml:space="preserve">
    <value>..</value>
  </data>
  <data name="lbl_Cust_Tel.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_Cust_Mobile.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_Cust_Mobile.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>775.896362, 124.500145</value>
  </data>
  <data name="lbl_Cust_Mobile.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>12.9780884, 24.50003</value>
  </data>
  <data name="lbl_Cust_Mobile.Text" xml:space="preserve">
    <value>..</value>
  </data>
  <data name="lbl_Cust_Mobile.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel11.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>372.0008, 252.750687</value>
  </data>
  <data name="xrLabel11.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>55.53064, 25.4583969</value>
  </data>
  <data name="xrLabel11.Text" xml:space="preserve">
    <value>الاحتفاظ</value>
  </data>
  <data name="xrLabel11.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_retention.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>342.064178, 252.750687</value>
  </data>
  <data name="txt_retention.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>29.9364929, 23.5827789</value>
  </data>
  <data name="txt_retention.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_advancepayment.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>341.9643, 276.333527</value>
  </data>
  <data name="txt_advancepayment.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>30.036438, 24.4584427</value>
  </data>
  <data name="txt_advancepayment.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="xrLabel25.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>372.000732, 278.209076</value>
  </data>
  <data name="xrLabel25.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>55.5307, 21.0521851</value>
  </data>
  <data name="xrLabel25.Text" xml:space="preserve">
    <value>دفعة مقدمة</value>
  </data>
  <data name="xrLabel25.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="xrLabel9.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>138.885788, 177.545929</value>
  </data>
  <data name="xrLabel9.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>125.021255, 24.4584351</value>
  </data>
  <data name="xrLabel9.Text" xml:space="preserve">
    <value>ضريبة الجدول</value>
  </data>
  <data name="lbl_CusTaxV.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>11.2078829, 176.5241</value>
  </data>
  <data name="lbl_CusTaxV.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>99.5270538, 25.45842</value>
  </data>
  <data name="lbl_AddTaxR.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>112.494186, 126.413979</value>
  </data>
  <data name="lbl_AddTaxR.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>24.9181976, 25.0000076</value>
  </data>
  <data name="xrLabel3.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>139.171646, 127.243294</value>
  </data>
  <data name="xrLabel3.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>125.021286, 24.9999924</value>
  </data>
  <data name="xrLabel3.Text" xml:space="preserve">
    <value>اجمالي خصم ابونص
</value>
  </data>
  <data name="xrLabel2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>139.171646, 52.0356178</value>
  </data>
  <data name="xrLabel2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>126.7805, 25.1513176</value>
  </data>
  <data name="xrLabel2.Text" xml:space="preserve">
    <value>الإجمالي بعد الخصم</value>
  </data>
  <data name="lblSubTotal.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>11.207901, 51.928318</value>
  </data>
  <data name="lblSubTotal.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>101.2863, 25</value>
  </data>
  <data name="lbl_BounsDiscount.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="lbl_BounsDiscount.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lblTotalWords.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>134.3011, 322.81778</value>
  </data>
  <data name="xrLabel5.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>651.2139, 322.8178</value>
  </data>
  <data name="xrLabel5.Text" xml:space="preserve">
    <value>مبلغ وقدره</value>
  </data>
  <data name="lbl_DiscountV.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>11.207901, 26.9062939</value>
  </data>
  <data name="lbl_DiscountV.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>101.2863, 25</value>
  </data>
  <data name="ReportFooter.HeightF" type="System.Single, mscorlib">
    <value>526.6511</value>
  </data>
  <data name="xrTable2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>11.2078829, 0</value>
  </data>
  <data name="cell_Total.Weight" type="System.Double, mscorlib">
    <value>0.16708855429150421</value>
  </data>
  <data name="cell_bonusDiscount.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="cell_bonusDiscount.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="cell_DiscountRatio.Weight" type="System.Double, mscorlib">
    <value>0.12173171584237799</value>
  </data>
  <data name="cell_Disc.Weight" type="System.Double, mscorlib">
    <value>0.14232736055709969</value>
  </data>
  <data name="cell_Price.Weight" type="System.Double, mscorlib">
    <value>0.11008611427509404</value>
  </data>
  <data name="cell_tableTaxValue.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="cell_tableTaxValue.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="cell_addTaxValue.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="cell_addTaxValue.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="cell_Qty.Weight" type="System.Double, mscorlib">
    <value>0.1170942490434593</value>
  </data>
  <data name="cell_EtaxValue.Weight" type="System.Double, mscorlib">
    <value>0.18316778058783284</value>
  </data>
  <data name="lbl_itemNameF.Weight" type="System.Double, mscorlib">
    <value>0.1950334847420751</value>
  </data>
  <data name="cell_UOM.Weight" type="System.Double, mscorlib">
    <value>0.20938060756014953</value>
  </data>
  <data name="cell_ItemName.Weight" type="System.Double, mscorlib">
    <value>0.40535291297875392</value>
  </data>
  <data name="xrLabel26.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 18pt</value>
  </data>
  <data name="xrLabel26.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="ReportHeader2.HeightF" type="System.Single, mscorlib">
    <value>536.7812</value>
  </data>
  <data name="$this.Margins" type="System.Drawing.Printing.Margins, System.Drawing">
    <value>26, 38, 0, 48</value>
  </data>
</root>