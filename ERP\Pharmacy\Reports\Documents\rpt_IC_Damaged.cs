using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using DAL;
using System.Linq;
using System.Data;
using DevExpress.XtraEditors;

using System.Windows.Forms;

namespace Reports
{
    public partial class rpt_IC_Damaged : DevExpress.XtraReports.UI.XtraReport
    {
        string serial, number, date,store,  notes,userName, totalP, totalS;
        DataTable dt_inv_details;

        public rpt_IC_Damaged()
        {
            InitializeComponent();
        }

        //    Reports.rpt_IC_StoreMove r = new Reports.rpt_IC_StoreMove(txtInvoiceId.Text, txtInvoiceCode.Text, dtInvoiceDate.Text,
        //lkpStoreFrom.Text, lkpStoreTo.Text, storeFromManager, storeToManager, txtNotes.Text,
        //dt_PrintTable, Shared.UserName);

        public rpt_IC_Damaged(string _serial, string _number, string _date,string _store, string _notes,
            DataTable dt, string userName, string _totalP, string _totalS)
        {
            InitializeComponent();

            serial = _serial;
            number = _number;
            date = _date;            
            store= _store;
            notes = _notes;

            this.userName = userName;
            totalP = _totalP;
            totalS = _totalS;

            dt_inv_details = dt;
            this.DataSource = dt_inv_details;
            getReportHeader();
            LoadData();
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                lblCompName.Text = comp.CmpNameAr;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        public void LoadData()
        {
            lbl_Serial.Text = serial;
            lbl_Number.Text = number;
            lbl_date.Text = date;
            lbl_storeFrom.Text = store;            
            lbl_notes.Text = notes;            
            lbl_User.Text = userName;

            lbl_Total_P_Price.Text = totalP;
            lbl_Total_S_Price.Text = totalS;
            lblTotal_P_Words.Text = Shared.IsEnglish ? HelperAcc.ConvertMoneyToText(totalP, 0, Shared.lstCurrency) :
                       HelperAcc.ConvertMoneyToArabicText(totalP, 0, Shared.lstCurrency);
            lblTotal_S_Words.Text = Shared.IsEnglish ? HelperAcc.ConvertMoneyToText(totalS, 0, Shared.lstCurrency) :
                       HelperAcc.ConvertMoneyToArabicText(totalS, 0, Shared.lstCurrency);

            this.DataSource = dt_inv_details;

            cell_code.DataBindings.Add("Text", this.DataSource, "ItemCode1");
            cell_code2.DataBindings.Add("Text", this.DataSource, "ItemCode2");
            cell_ItemName.DataBindings.Add("Text", this.DataSource, "ItemName");
            cell_UOM.DataBindings.Add("Text", this.DataSource, "UOM");
            cell_Qty.DataBindings.Add("Text", this.DataSource, "Qty");
            cell_Expire.DataBindings.Add("Text", this.DataSource, "Expire");
            cell_Batch.DataBindings.Add("Text", this.DataSource, "Batch");
            cell_P_Price.DataBindings.Add("Text", this.DataSource, "PurchasePrice");
            cell_S_Price.DataBindings.Add("Text", this.DataSource, "SellPrice");
            cell_Total_P_Price.DataBindings.Add("Text", this.DataSource, "TotalPurchasePrice");
            cell_Total_S_Price.DataBindings.Add("Text", this.DataSource, "TotalSellPrice");
            cell_Serial.DataBindings.Add("Text", this.DataSource, "Serial");
        }
    }
}
