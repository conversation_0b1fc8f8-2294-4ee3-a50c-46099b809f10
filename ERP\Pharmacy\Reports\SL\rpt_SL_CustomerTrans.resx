﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="col_DrawerAmount.Caption" xml:space="preserve">
    <value>Drawers</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="xrTable4.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>300, 150</value>
  </data>
  <data name="pivotGridControl1.OptionsDataField.Caption" xml:space="preserve">
    <value />
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="lblCompName.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopCenter</value>
  </data>
  <data name="xrPageInfo1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>109.375, 23</value>
  </data>
  <data name="xrLabel3.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>100, 23</value>
  </data>
  <assembly alias="DevExpress.Utils.v15.1" name="DevExpress.Utils.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="winControlContainer1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>0, 0</value>
  </data>
  <data name="xrTable2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>237.5, 25</value>
  </data>
  <data name="&gt;&gt;gridControl1.Name" xml:space="preserve">
    <value>gridControl1</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="col_OtherAmount.Width" type="System.Int32, mscorlib">
    <value>133</value>
  </data>
  <data name="xrTable3.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>300, 150</value>
  </data>
  <data name="xrTable6.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>300, 25</value>
  </data>
  <data name="gridControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>741, 198</value>
  </data>
  <data name="col_Date.Width" type="System.Int32, mscorlib">
    <value>103</value>
  </data>
  <data name="xrSubreport2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>154.166672, 178.083359</value>
  </data>
  <data name="xrSubreport2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>578.125, 93.91664</value>
  </data>
  <data name="xrPageInfo2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>9.999998, 12.00002</value>
  </data>
  <data name="col_OtherAmount.SummaryItem.DisplayFormat" xml:space="preserve">
    <value />
  </data>
  <data name="col_BankAmount.SummaryItem.DisplayFormat" xml:space="preserve">
    <value />
  </data>
  <data name="gridBand2.Width" type="System.Int32, mscorlib">
    <value>1094</value>
  </data>
  <data name="xrPageInfo1.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrLabel2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>100, 23</value>
  </data>
  <data name="col_Date.Caption" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="xrLabel2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>512.5, 10.0000067</value>
  </data>
  <data name="&gt;&gt;pivotGridControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraPivotGrid.PivotGridControl, DevExpress.XtraPivotGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lblFilter.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>14.58333, 98.62501</value>
  </data>
  <data name="gridControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="xrPageInfo2.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleLeft</value>
  </data>
  <data name="xrTable2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>300, 50</value>
  </data>
  <data name="col_BankAmount.Width" type="System.Int32, mscorlib">
    <value>121</value>
  </data>
  <data name="&gt;&gt;gridControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.GridControl, DevExpress.XtraGrid.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lbl_ProcessName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>512.5, 10.0000067</value>
  </data>
  <data name="xrLine3.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>14.5833254, 273.958344</value>
  </data>
  <data name="pivotGridControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="xrTable6.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>237.5, 369</value>
  </data>
  <data name="lblReportName.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>600.0001, 24.49999</value>
  </data>
  <data name="col_ProcessId.Caption" xml:space="preserve">
    <value>ProcessId</value>
  </data>
  <data name="xrTable7.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>14.583333, 189.875031</value>
  </data>
  <data name="xrLine4.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>761.416565, 9.********</value>
  </data>
  <data name="col_BankAmount.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="TopMargin.HeightF" type="System.Single, mscorlib">
    <value>126</value>
  </data>
  <data name="lblFilter.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopCenter</value>
  </data>
  <data name="xrPageInfo1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>337.5, 12.5</value>
  </data>
  <data name="xrPageInfo2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>217.6667, 23</value>
  </data>
  <data name="lbl_CustName.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>100, 23</value>
  </data>
  <data name="xrLine1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>148.958328, 193.708359</value>
  </data>
  <data name="lblReportName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>89.58334, 44.00002</value>
  </data>
  <data name="lblCompName.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>600.0001, 30</value>
  </data>
  <data name="col_BankAmount.Caption" xml:space="preserve">
    <value>Notes Receivables</value>
  </data>
  <data name="xrLabel3.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>287.5, 125</value>
  </data>
  <data name="TopMargin.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="xrTable5.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>227.083435, 375</value>
  </data>
  <data name="gridControl1.EmbeddedNavigator.ToolTipTitle" xml:space="preserve">
    <value />
  </data>
  <data name="lblReportName.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 14pt</value>
  </data>
  <data name="picLogo.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>70, 70</value>
  </data>
  <data name="xrTable7.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>246.073837, 75.00001</value>
  </data>
  <data name="Detail.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="BottomMargin.HeightF" type="System.Single, mscorlib">
    <value>45</value>
  </data>
  <data name="col_DrawerAmount.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;pivotGridControl1.Name" xml:space="preserve">
    <value>pivotGridControl1</value>
  </data>
  <data name="lblCompName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>89.58334, 10.00001</value>
  </data>
  <data name="Detail.HeightF" type="System.Single, mscorlib">
    <value>216</value>
  </data>
  <data name="col_Notes.Caption" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="winControlContainer1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>772, 206</value>
  </data>
  <data name="lblCompName.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 18pt</value>
  </data>
  <data name="bandedGridColumn1.SummaryItem.DisplayFormat" xml:space="preserve">
    <value />
  </data>
  <data name="$this.Margins" type="System.Drawing.Printing.Margins, System.Drawing">
    <value>19, 22, 126, 45</value>
  </data>
  <data name="gridControl1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lblDateFilter.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt</value>
  </data>
  <data name="col_ProcessId.Width" type="System.Int32, mscorlib">
    <value>58</value>
  </data>
  <data name="xrLine4.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>14.583333, 425.916656</value>
  </data>
  <data name="col_OtherAmount.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_DrawerAmount.SummaryItem.DisplayFormat" xml:space="preserve">
    <value />
  </data>
  <data name="lblDateFilter.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>89.58334, 72.87501</value>
  </data>
  <data name="xrLabel1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>100, 23</value>
  </data>
  <data name="xrLabel1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>615.625, 10.0000067</value>
  </data>
  <data name="col_Notes.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="xrLine2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>125.999992, 36.58336</value>
  </data>
  <data name="xrPanel1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>300, 75</value>
  </data>
  <data name="gridControl1.EmbeddedNavigator.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="col_DrawerAmount.Width" type="System.Int32, mscorlib">
    <value>132</value>
  </data>
  <data name="lbl_ProcessName.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>183.208313, 26.5833511</value>
  </data>
  <data name="col_Date.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="xrTable1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>300, 50</value>
  </data>
  <data name="picLogo.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>14.58333, 10.00001</value>
  </data>
  <data name="lblReportName.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopCenter</value>
  </data>
  <data name="xrPageInfo2.Format" xml:space="preserve">
    <value>Printed at {0:dd MM yyyy  h:mm tt}</value>
  </data>
  <data name="lblFilter.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 10pt</value>
  </data>
  <data name="GroupFooter1.HeightF" type="System.Single, mscorlib">
    <value>46</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="gridControl1.EmbeddedNavigator.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>Tile</value>
  </data>
  <data name="xrLine2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>650, 9</value>
  </data>
  <data name="pivotGridControl1.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <assembly alias="DevExpress.XtraEditors.v15.1" name="DevExpress.XtraEditors.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="gridControl1.EmbeddedNavigator.TextLocation" type="DevExpress.XtraEditors.NavigatorButtonsTextLocation, DevExpress.XtraEditors.v15.1">
    <value>Center</value>
  </data>
  <data name="col_AccountId.Width" type="System.Int32, mscorlib">
    <value>47</value>
  </data>
  <data name="lblFilter.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>764.5, 22.62501</value>
  </data>
  <data name="gridControl1.EmbeddedNavigator.AllowHtmlTextInToolTip" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="xrTable4.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>14.58333, 10.0000067</value>
  </data>
  <data name="xrPageInfo1.Format" xml:space="preserve">
    <value>Page {0} of {1} </value>
  </data>
  <data name="xrTable5.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>300, 25</value>
  </data>
  <data name="gridControl1.EmbeddedNavigator.ToolTipIconType" type="DevExpress.Utils.ToolTipIconType, DevExpress.Utils.v15.1">
    <value>None</value>
  </data>
  <data name="lbl_CustName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>512.5, 10.0000067</value>
  </data>
  <data name="gridControl1.EmbeddedNavigator.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="lblDateFilter.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>600.0001, 23.75</value>
  </data>
  <data name="gridControl1.EmbeddedNavigator.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>Inherit</value>
  </data>
  <data name="xrTable1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>158.333328, 10.0000067</value>
  </data>
  <data name="col_AccountId.Caption" xml:space="preserve">
    <value>AccountId</value>
  </data>
  <data name="col_OtherAmount.Caption" xml:space="preserve">
    <value>Other Amounts</value>
  </data>
  <data name="gridBand2.Name" xml:space="preserve">
    <value>gridBand2</value>
  </data>
  <data name="xrSubreport1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>383.2916, 65.9999847</value>
  </data>
  <data name="xrPanel1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>70.79167, 187.000015</value>
  </data>
  <data name="xrLine1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>566.6667, 23.0000153</value>
  </data>
  <data name="BottomMargin.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopLeft</value>
  </data>
  <data name="xrTable3.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>475.999847, 10.0000067</value>
  </data>
  <data name="col_Amount.SummaryItem.DisplayFormat" xml:space="preserve">
    <value />
  </data>
  <data name="xrSubreport1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Utils.v15.1">
    <value>392.708344, 62.00002</value>
  </data>
  <data name="lblDateFilter.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopCenter</value>
  </data>
  <data name="xrLine3.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>765.708252, 9</value>
  </data>
  <data name="col_Notes.Width" type="System.Int32, mscorlib">
    <value>605</value>
  </data>
  <data name="gridBand2.MinWidth" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.Language" type="System.Globalization.CultureInfo, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Arabic (Egypt)</value>
  </metadata>
</root>