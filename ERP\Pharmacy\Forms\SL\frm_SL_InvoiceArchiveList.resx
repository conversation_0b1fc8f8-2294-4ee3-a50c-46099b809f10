﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="barManager1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="barBtn_Help.Caption" xml:space="preserve">
    <value>Help</value>
  </data>
  <data name="barBtn_Help.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="barMnu_Print.Caption" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="barBtn_Print1.Caption" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="barBtn_PrintData.Caption" xml:space="preserve">
    <value>Print Data</value>
  </data>
  <data name="barBtnRefresh.Caption" xml:space="preserve">
    <value>Refresh</value>
  </data>
  <data name="barBtnRefresh.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="barBtnOpen.Caption" xml:space="preserve">
    <value>Open</value>
  </data>
  <data name="barBtnOpen.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="barBtnNew.Caption" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="barBtnNew.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="barBtnClose.Caption" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="barBtnClose.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="bar1.Text" xml:space="preserve">
    <value>Tools</value>
  </data>
  <metadata name="barAndDockingController1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>134, 17</value>
  </metadata>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="barDockControlTop.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Top</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="barDockControlTop.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="barDockControlTop.Size" type="System.Drawing.Size, System.Drawing">
    <value>1114, 28</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Name" xml:space="preserve">
    <value>barDockControlTop</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="barDockControlBottom.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="barDockControlBottom.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 476</value>
  </data>
  <data name="barDockControlBottom.Size" type="System.Drawing.Size, System.Drawing">
    <value>1114, 0</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Name" xml:space="preserve">
    <value>barDockControlBottom</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="barDockControlLeft.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="barDockControlLeft.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 28</value>
  </data>
  <data name="barDockControlLeft.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 448</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Name" xml:space="preserve">
    <value>barDockControlLeft</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="barDockControlRight.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Right</value>
  </data>
  <data name="barDockControlRight.Location" type="System.Drawing.Point, System.Drawing">
    <value>1114, 28</value>
  </data>
  <data name="barDockControlRight.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 448</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Name" xml:space="preserve">
    <value>barDockControlRight</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>52</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>1114, 476</value>
  </data>
  <data name="btnClearSearch.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="btnClearSearch.Location" type="System.Drawing.Point, System.Drawing">
    <value>731, 54</value>
  </data>
  <data name="btnClearSearch.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 19</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="btnClearSearch.TabIndex" type="System.Int32, mscorlib">
    <value>106</value>
  </data>
  <data name="btnClearSearch.Text" xml:space="preserve">
    <value>Clear Search</value>
  </data>
  <data name="&gt;&gt;btnClearSearch.Name" xml:space="preserve">
    <value>btnClearSearch</value>
  </data>
  <data name="&gt;&gt;btnClearSearch.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;btnClearSearch.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;btnClearSearch.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="labelControl3.Appearance.Font" type="System.Drawing.Font, System.Drawing">
    <value>Tahoma, 14.25pt</value>
  </data>
  <data name="labelControl3.Appearance.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Navy</value>
  </data>
  <data name="labelControl3.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 49</value>
  </data>
  <data name="labelControl3.Size" type="System.Drawing.Size, System.Drawing">
    <value>184, 23</value>
  </data>
  <data name="labelControl3.TabIndex" type="System.Int32, mscorlib">
    <value>105</value>
  </data>
  <data name="labelControl3.Text" xml:space="preserve">
    <value>Sales Invoices Archive</value>
  </data>
  <data name="&gt;&gt;labelControl3.Name" xml:space="preserve">
    <value>labelControl3</value>
  </data>
  <data name="&gt;&gt;labelControl3.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl3.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl3.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="dt2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="dt2.EditValue" type="System.DateTime, mscorlib">
    <value>2015-09-20</value>
  </data>
  <data name="dt2.Location" type="System.Drawing.Point, System.Drawing">
    <value>832, 54</value>
  </data>
  <assembly alias="DevExpress.Utils.v15.1" name="DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="dt2.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="dt2.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="dt2.TabIndex" type="System.Int32, mscorlib">
    <value>104</value>
  </data>
  <data name="&gt;&gt;dt2.Name" xml:space="preserve">
    <value>dt2</value>
  </data>
  <data name="&gt;&gt;dt2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.DateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;dt2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;dt2.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="labelControl2.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>938, 57</value>
  </data>
  <data name="labelControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>12, 13</value>
  </data>
  <data name="labelControl2.TabIndex" type="System.Int32, mscorlib">
    <value>103</value>
  </data>
  <data name="labelControl2.Text" xml:space="preserve">
    <value>To</value>
  </data>
  <data name="&gt;&gt;labelControl2.Name" xml:space="preserve">
    <value>labelControl2</value>
  </data>
  <data name="&gt;&gt;labelControl2.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl2.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl2.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="dt1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="dt1.EditValue" type="System.DateTime, mscorlib">
    <value>2015-09-20</value>
  </data>
  <data name="dt1.Location" type="System.Drawing.Point, System.Drawing">
    <value>964, 54</value>
  </data>
  <data name="dt1.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="dt1.Size" type="System.Drawing.Size, System.Drawing">
    <value>100, 20</value>
  </data>
  <data name="dt1.TabIndex" type="System.Int32, mscorlib">
    <value>102</value>
  </data>
  <data name="&gt;&gt;dt1.Name" xml:space="preserve">
    <value>dt1</value>
  </data>
  <data name="&gt;&gt;dt1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.DateEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;dt1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;dt1.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="labelControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>1070, 57</value>
  </data>
  <data name="labelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>24, 13</value>
  </data>
  <data name="labelControl1.TabIndex" type="System.Int32, mscorlib">
    <value>101</value>
  </data>
  <data name="labelControl1.Text" xml:space="preserve">
    <value>From</value>
  </data>
  <data name="&gt;&gt;labelControl1.Name" xml:space="preserve">
    <value>labelControl1</value>
  </data>
  <data name="&gt;&gt;labelControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelControl1.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <metadata name="contextMenuStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>460, 17</value>
  </metadata>
  <data name="mi_OpenDealer.Size" type="System.Drawing.Size, System.Drawing">
    <value>180, 22</value>
  </data>
  <data name="mi_OpenDealer.Text" xml:space="preserve">
    <value>Open Dealer Info</value>
  </data>
  <data name="contextMenuStrip1.Size" type="System.Drawing.Size, System.Drawing">
    <value>181, 48</value>
  </data>
  <data name="&gt;&gt;contextMenuStrip1.Name" xml:space="preserve">
    <value>contextMenuStrip1</value>
  </data>
  <data name="&gt;&gt;contextMenuStrip1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ContextMenuStrip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="grdCategory.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="grdCategory.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 88</value>
  </data>
  <data name="gridView1.AppearancePrint.FooterPanel.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="gridView1.AppearancePrint.FooterPanel.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="gridView1.AppearancePrint.GroupFooter.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="gridView1.AppearancePrint.GroupFooter.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="gridView1.AppearancePrint.GroupRow.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="gridView1.AppearancePrint.GroupRow.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="gridView1.AppearancePrint.HeaderPanel.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="gridView1.AppearancePrint.HeaderPanel.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="gridView1.AppearancePrint.Lines.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="gridView1.AppearancePrint.Lines.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="gridView1.AppearancePrint.Row.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="gridView1.AppearancePrint.Row.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Black</value>
  </data>
  <data name="col_SL_InvoiceId.Caption" xml:space="preserve">
    <value>Invoice Code</value>
  </data>
  <data name="col_SL_InvoiceId.Width" type="System.Int32, mscorlib">
    <value>71</value>
  </data>
  <data name="col_InvoiceCode.Caption" xml:space="preserve">
    <value>Invoice Number</value>
  </data>
  <data name="col_InvoiceCode.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_InvoiceCode.VisibleIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="col_InvoiceCode.Width" type="System.Int32, mscorlib">
    <value>79</value>
  </data>
  <data name="col_InvoiceBookId.Caption" xml:space="preserve">
    <value>Invoice Book</value>
  </data>
  <data name="rep_InvoiceBook.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_InvoiceBook.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="rep_InvoiceBook.Columns" xml:space="preserve">
    <value>InvoiceBookId</value>
  </data>
  <data name="rep_InvoiceBook.Columns1" xml:space="preserve">
    <value>InvoiceBookId</value>
  </data>
  <data name="rep_InvoiceBook.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="rep_InvoiceBook.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="rep_InvoiceBook.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="rep_InvoiceBook.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_InvoiceBook.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="rep_InvoiceBook.Columns7" xml:space="preserve">
    <value>InvoiceBookName</value>
  </data>
  <data name="rep_InvoiceBook.Columns8" xml:space="preserve">
    <value>Invoice Book</value>
  </data>
  <data name="rep_InvoiceBook.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="col_InvoiceBookId.Width" type="System.Int32, mscorlib">
    <value>46</value>
  </data>
  <data name="col_StoreId.Caption" xml:space="preserve">
    <value>Branch</value>
  </data>
  <data name="col_StoreId.Width" type="System.Int32, mscorlib">
    <value>78</value>
  </data>
  <data name="col_CustomerId.Caption" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="col_CustomerId.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_CustomerId.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="col_CustomerId.Width" type="System.Int32, mscorlib">
    <value>99</value>
  </data>
  <data name="col_InvoiceDate.Caption" xml:space="preserve">
    <value>Invoice Date</value>
  </data>
  <data name="col_InvoiceDate.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_InvoiceDate.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="col_InvoiceDate.Width" type="System.Int32, mscorlib">
    <value>64</value>
  </data>
  <data name="col_Notes.Caption" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="col_Notes.Width" type="System.Int32, mscorlib">
    <value>55</value>
  </data>
  <data name="col_PayMethod.Caption" xml:space="preserve">
    <value>Payment method</value>
  </data>
  <data name="rep_paymethod.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_paymethod.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="rep_paymethod.Items" xml:space="preserve">
    <value>Cash</value>
  </data>
  <data name="rep_paymethod.Items1" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="rep_paymethod.Items2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="rep_paymethod.Items3" xml:space="preserve">
    <value>On Credit</value>
  </data>
  <data name="rep_paymethod.Items4" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_paymethod.Items5" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="rep_paymethod.Items6" xml:space="preserve">
    <value>Cash/ On Credit</value>
  </data>
  <data name="rep_paymethod.Items7" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="rep_paymethod.Items8" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="col_PayMethod.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_PayMethod.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="col_PayMethod.Width" type="System.Int32, mscorlib">
    <value>51</value>
  </data>
  <data name="col_DiscountRatio.Caption" xml:space="preserve">
    <value>Disc Ratio</value>
  </data>
  <data name="col_DiscountRatio.Width" type="System.Int32, mscorlib">
    <value>86</value>
  </data>
  <data name="col_DiscountValue.Caption" xml:space="preserve">
    <value>Disc Value</value>
  </data>
  <data name="col_DiscountValue.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="col_DiscountValue.Summary1" xml:space="preserve">
    <value>DiscountValue</value>
  </data>
  <data name="col_DiscountValue.Summary2" xml:space="preserve">
    <value>{0:n2}</value>
  </data>
  <data name="col_DiscountValue.Width" type="System.Int32, mscorlib">
    <value>64</value>
  </data>
  <data name="col_Expenses.Caption" xml:space="preserve">
    <value>Expenses</value>
  </data>
  <data name="col_Expenses.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="col_Expenses.Summary1" xml:space="preserve">
    <value>Expenses</value>
  </data>
  <data name="col_Expenses.Summary2" xml:space="preserve">
    <value>{0:n2}</value>
  </data>
  <data name="col_Expenses.Width" type="System.Int32, mscorlib">
    <value>64</value>
  </data>
  <data name="colPaid.Caption" xml:space="preserve">
    <value>paid</value>
  </data>
  <data name="colPaid.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="colPaid.Summary1" xml:space="preserve">
    <value>Paid</value>
  </data>
  <data name="colPaid.Summary2" xml:space="preserve">
    <value>{0:n2}</value>
  </data>
  <data name="colRemains.Caption" xml:space="preserve">
    <value>Remains</value>
  </data>
  <data name="colRemains.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="colRemains.Summary1" xml:space="preserve">
    <value>Remains</value>
  </data>
  <data name="colRemains.Summary2" xml:space="preserve">
    <value>{0:n2}</value>
  </data>
  <data name="col_UserId.Caption" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="col_UserId.Width" type="System.Int32, mscorlib">
    <value>70</value>
  </data>
  <data name="colStore.Caption" xml:space="preserve">
    <value>Store Code</value>
  </data>
  <data name="col_SalesEmpId.Caption" xml:space="preserve">
    <value>Sales Empolyee</value>
  </data>
  <data name="rep_salesEmp.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_salesEmp.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="rep_salesEmp.Columns" xml:space="preserve">
    <value>EmpName</value>
  </data>
  <data name="rep_salesEmp.Columns1" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="rep_salesEmp.Columns2" xml:space="preserve">
    <value>EmpId</value>
  </data>
  <data name="rep_salesEmp.Columns3" xml:space="preserve">
    <value>EmpId</value>
  </data>
  <data name="rep_salesEmp.Columns4" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="rep_salesEmp.Columns5" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="rep_salesEmp.Columns6" xml:space="preserve">
    <value />
  </data>
  <data name="rep_salesEmp.Columns7" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_salesEmp.Columns8" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="rep_salesEmp.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="col_SalesEmpId.Width" type="System.Int32, mscorlib">
    <value>65</value>
  </data>
  <data name="col_CustId.Caption" xml:space="preserve">
    <value>CustId</value>
  </data>
  <data name="col_Net.Caption" xml:space="preserve">
    <value>Net</value>
  </data>
  <data name="col_Net.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="col_Net.Summary1" xml:space="preserve">
    <value>Net</value>
  </data>
  <data name="col_Net.Summary2" xml:space="preserve">
    <value>{0:n2}</value>
  </data>
  <data name="col_Net.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_Net.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="col_Net.Width" type="System.Int32, mscorlib">
    <value>50</value>
  </data>
  <data name="col_TotalCostPrice.Caption" xml:space="preserve">
    <value>Cost</value>
  </data>
  <data name="col_TotalCostPrice.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="col_TotalCostPrice.Summary1" xml:space="preserve">
    <value>TotalCostPrice</value>
  </data>
  <data name="col_TotalCostPrice.Summary2" xml:space="preserve">
    <value>{0:n2}</value>
  </data>
  <data name="col_Profit.Caption" xml:space="preserve">
    <value>Profit/Loss</value>
  </data>
  <data name="col_Profit.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="col_Profit.Summary1" xml:space="preserve">
    <value>Profit</value>
  </data>
  <data name="col_Profit.Summary2" xml:space="preserve">
    <value>{0:n2}</value>
  </data>
  <data name="col_ProfitRatio.Caption" xml:space="preserve">
    <value>Profit Ratio</value>
  </data>
  <data name="col_ProfitRatio.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Custom</value>
  </data>
  <data name="col_ProfitRatio.Summary1" xml:space="preserve">
    <value>profitRatio</value>
  </data>
  <data name="col_ProfitRatio.Summary2" xml:space="preserve">
    <value>{0:p2}</value>
  </data>
  <data name="col_ProfitCostRatio.Caption" xml:space="preserve">
    <value>Profit Ratio For cost</value>
  </data>
  <data name="col_ProfitCostRatio.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Custom</value>
  </data>
  <data name="col_ProfitCostRatio.Summary1" xml:space="preserve">
    <value>ProfitCostRatio</value>
  </data>
  <data name="col_ProfitCostRatio.Summary2" xml:space="preserve">
    <value>{0:p2}</value>
  </data>
  <data name="col_GroupId.Caption" xml:space="preserve">
    <value>Group</value>
  </data>
  <data name="rep_groupId.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_groupId.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="rep_groupId.Columns" xml:space="preserve">
    <value>CustomerGroupId</value>
  </data>
  <data name="rep_groupId.Columns1" xml:space="preserve">
    <value>CustomerGroupId</value>
  </data>
  <data name="rep_groupId.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="rep_groupId.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="rep_groupId.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="rep_groupId.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_groupId.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="rep_groupId.Columns7" xml:space="preserve">
    <value>CGNameAr</value>
  </data>
  <data name="rep_groupId.Columns8" xml:space="preserve">
    <value>Group</value>
  </data>
  <data name="rep_groupId.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="col_GroupId.Width" type="System.Int32, mscorlib">
    <value>46</value>
  </data>
  <data name="col_Is_OutTrans.Caption" xml:space="preserve">
    <value>Outgoing From Store</value>
  </data>
  <data name="col_Is_OutTrans.Width" type="System.Int32, mscorlib">
    <value>53</value>
  </data>
  <data name="col_TaxValue.Caption" xml:space="preserve">
    <value>Sales Tax</value>
  </data>
  <data name="col_TaxValue.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="col_TaxValue.Summary1" xml:space="preserve">
    <value>TaxValue</value>
  </data>
  <data name="col_TaxValue.Summary2" xml:space="preserve">
    <value>{0:n2}</value>
  </data>
  <data name="col_TaxValue.Width" type="System.Int32, mscorlib">
    <value>47</value>
  </data>
  <data name="col_DeductTaxValue.Caption" xml:space="preserve">
    <value>Deduct Tax</value>
  </data>
  <data name="col_DeductTaxValue.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="col_DeductTaxValue.Summary1" xml:space="preserve">
    <value>DeductTaxValue</value>
  </data>
  <data name="col_DeductTaxValue.Summary2" xml:space="preserve">
    <value>{0:n2}</value>
  </data>
  <data name="col_DeductTaxValue.Width" type="System.Int32, mscorlib">
    <value>58</value>
  </data>
  <data name="col_AddTaxValue.Caption" xml:space="preserve">
    <value>Add Tax</value>
  </data>
  <data name="col_AddTaxValue.Summary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="col_AddTaxValue.Summary1" xml:space="preserve">
    <value>AddTaxValue</value>
  </data>
  <data name="col_AddTaxValue.Summary2" xml:space="preserve">
    <value>{0:n2}</value>
  </data>
  <data name="col_AddTaxValue.Width" type="System.Int32, mscorlib">
    <value>63</value>
  </data>
  <data name="col_DueDate.Caption" xml:space="preserve">
    <value>Due Date</value>
  </data>
  <data name="col_CrncId.Caption" xml:space="preserve">
    <value>Curr.</value>
  </data>
  <data name="repCrncy.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repCrncy.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="repCrncy.Columns" xml:space="preserve">
    <value>CrncId</value>
  </data>
  <data name="repCrncy.Columns1" xml:space="preserve">
    <value>CrncId</value>
  </data>
  <data name="repCrncy.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="repCrncy.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="repCrncy.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="repCrncy.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repCrncy.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="repCrncy.Columns7" xml:space="preserve">
    <value>crncName</value>
  </data>
  <data name="repCrncy.Columns8" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="repCrncy.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="col_CrncId.Width" type="System.Int32, mscorlib">
    <value>98</value>
  </data>
  <data name="col_CrncRate.Caption" xml:space="preserve">
    <value>Curr. Rate</value>
  </data>
  <data name="col_CrncRate.Width" type="System.Int32, mscorlib">
    <value>74</value>
  </data>
  <data name="colDriverName.Caption" xml:space="preserve">
    <value>Driver Name</value>
  </data>
  <data name="colVehicleNumber.Caption" xml:space="preserve">
    <value>Vehicle Number</value>
  </data>
  <data name="colDestination.Caption" xml:space="preserve">
    <value>Destination</value>
  </data>
  <data name="col_Process.Caption" xml:space="preserve">
    <value>Source Doc</value>
  </data>
  <data name="col_SourceCode.Caption" xml:space="preserve">
    <value>Source Doc Code</value>
  </data>
  <data name="gridView1.GroupPanelText" xml:space="preserve">
    <value>Drag any column here to group by that column</value>
  </data>
  <data name="gridView1.GroupSummary" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="gridView1.GroupSummary1" xml:space="preserve">
    <value />
  </data>
  <data name="gridView1.GroupSummary2" xml:space="preserve">
    <value>n2</value>
  </data>
  <data name="gridView1.GroupSummary3" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="gridView1.GroupSummary4" xml:space="preserve">
    <value />
  </data>
  <data name="gridView1.GroupSummary5" xml:space="preserve">
    <value>n2</value>
  </data>
  <data name="gridView1.GroupSummary6" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="gridView1.GroupSummary7" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView1.GroupSummary8" xml:space="preserve">
    <value>n2</value>
  </data>
  <data name="gridView1.GroupSummary9" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="gridView1.GroupSummary10" xml:space="preserve">
    <value />
  </data>
  <data name="gridView1.GroupSummary11" xml:space="preserve">
    <value>n2</value>
  </data>
  <data name="gridView1.GroupSummary12" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="gridView1.GroupSummary13" xml:space="preserve">
    <value />
  </data>
  <data name="gridView1.GroupSummary14" xml:space="preserve">
    <value>n2</value>
  </data>
  <data name="gridView1.GroupSummary15" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="gridView1.GroupSummary16" xml:space="preserve">
    <value />
  </data>
  <data name="gridView1.GroupSummary17" xml:space="preserve">
    <value>n2</value>
  </data>
  <data name="gridView1.GroupSummary18" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Sum</value>
  </data>
  <data name="gridView1.GroupSummary19" xml:space="preserve">
    <value />
  </data>
  <data name="gridView1.GroupSummary20" xml:space="preserve">
    <value>n2</value>
  </data>
  <data name="gridView1.GroupSummary21" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Average</value>
  </data>
  <data name="gridView1.GroupSummary22" xml:space="preserve">
    <value>profitRatio</value>
  </data>
  <data name="gridView1.GroupSummary23" xml:space="preserve">
    <value>{0:p2}</value>
  </data>
  <data name="gridView1.GroupSummary24" type="DevExpress.Data.SummaryItemType, DevExpress.Data.v15.1">
    <value>Average</value>
  </data>
  <data name="gridView1.GroupSummary25" xml:space="preserve">
    <value>ProfitCostRatio</value>
  </data>
  <data name="gridView1.GroupSummary26" xml:space="preserve">
    <value>{0:p2}</value>
  </data>
  <data name="grdCategory.Size" type="System.Drawing.Size, System.Drawing">
    <value>1114, 388</value>
  </data>
  <data name="grdCategory.TabIndex" type="System.Int32, mscorlib">
    <value>47</value>
  </data>
  <data name="&gt;&gt;grdCategory.Name" xml:space="preserve">
    <value>grdCategory</value>
  </data>
  <data name="&gt;&gt;grdCategory.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.GridControl, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;grdCategory.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;grdCategory.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAA
        AAAfbf8AH2z/AB9r/x8fa/+rH2v/9h9r/9AfbP9HH2//AB9u/wAfcP8AAAAAAB9p/wAfZv8AH2D/Ah9j
        /2cfY//iH2P/7R9j/4YfY/8KH2P/AB9j/wAAAAAADw4QAA4NDwAODQ4ADQwORwwMDdAMCw32CwsMqwsL
        DB8MCw0ADAsNAB9u/wAfbf8WH23/qx9t//8fbv//H27//x9u/9gfb/89H3D/AB9y/wAfcf8AH23/AB9n
        /wAfZ/9cH2X/6x9j//8fY///H2P/9x9j/38fY/8GH2P/AB5e8QAPDhAADw4QAA4NDz0NDQ/YDQ0O/w0M
        Dv8MDA3/DAsNqwwLDRYMCw0AH2//FR9v/6Ifb///H3D//x9x//8fcf//H3H//x9x/9Mfcv82IHD/AiBw
        /wQfcP8DH2z/VB9q/+gfZ///H2X//x9j//8fY///H2P/9x9j/3cfY/8FH2P+ABAPEQAQDxE1Dw4Q0w8O
        EP8ODg//Dg0P/w0NDv8MDA3/DAsNogwLDRUfcP+MH3H//R9y//8ec///HnP//x50//8edP//HnP//x5z
        /9cec/+pH3L/qR9x/6wfbv/mH23//x9q//8faP//H2X//x9j//8fY///H2P/8h9j/04bTb8AERASERAQ
        ErsQEBL/EA8R/w8PEf8PDhD/Dg0P/w0ND/8MDA79DAwNjB5z/9QedP//HnX//x52//8edv//Hnb//x52
        //8edv//Hnb//x51//8edP//HnP//x9x//8fb///H23//x9r//8faP//H2X//x9j//8fY///H2P/gxg4
        hAASERM1ERET7hERE/8REBL/EBAS/xAPEf8PDhD/Dg4Q/w0ND/8NDA7THnX/wx52//8ed///Hnj//x54
        //8eef//Hnn//x54//8eeP/9Hnf/8h52//Iedf/zHnP//h9x//8fb///H23//x9q//8fZ///H2T//x9j
        //8fY/90GkGeABISFCYTEhThEhIU/xIRE/8RERP/EBAS/xAPEf8PDhD/Dg4Q/w4ND78ed/9UHnj/5x55
        //8eev//Hnr//x57//8ee///Hnr/+x56/5AeeP88Hnf/PB52/0Eedf+wHnP//x9x//8fb///H2z//x9p
        //8fZv//H2T/zB9j/yQdV9wAExIVAxMTFYMTEhX8ExIU/xISFP8RERP/ERAS/xAPEf8PDhDkDg4QTx50
        /wAeev9dHnv/7R58//8efP//Hn3//x59//0efP+UHnv/DB55/wAed/8AHnb/AB52/x8edf+5HnP//x9x
        //8fbv//H2v//x9o/9YfZf85H2f/AAAAAAAUExYAFBMWDRQTFpQUExX9ExIV/xISFP8SERP/ERAS6hAP
        EVYTDhQAHnr/AB58/wEefP9nHn3/7x5+//8efv/9Hn7/nR59/xAefP8AHnv/AB52/wAedf8AHnX/AB51
        /yUedP2+H3P//x9x//8fbPvbH2r/Px9o/wAfZv8AH2P/ABQTFgAUExYAFBQWEBQUFp0UExb9ExIV/xIS
        FOwRERNfIxkMABAQEQAee/8AHn3/AB5+/wcefv+xHn///x5//+oef/8tHn//AB59/wAefP8AAAAAAB52
        /wAeV/8AGkKGABk+eUYaQoPzG0KG/xo0ZZIbRZkAH2v/AB9o/wAAAAAAFBQWABQUFgAVFBcAFRQXLRUU
        F+oUExb/ExMVqxMTFAUSERQAEBASAAAAAAAegP8AHn//Ax6A/6kegP//HoD/5x6A/ycegP8AHoD/AAAA
        AAAAAAAAAAAAABpCgQAXFRcAFhMSOhcVFvAXFRb/GBUWjhgYHgAcO3gAAAAAAAAAAAAAAAAAFxYZABYV
        GAAWFRgnFRUX5xUUF/8UExajExIVAhQTFgAAAAAAHoD/AB6A/wAegP8IHoD/sx6B//8egf/rHoH/Lx6B
        /wAegv8AHoL/AAAAAAAXFhkAFBQXABcXGgAXFxpIGBca9BgYG/8ZGBuTGRkcABoaHQAbGx4AAAAAABkZ
        HAAYGBsAFxcZABcWGS8WFhjrFRUX/xUUF6wUFBYGFBMWABQTFQAef/8AHoD/Ah6A/20egf/xHoH//x6B
        //4egv+kHoL/Ex6C/wAegv8AFhYYABsZIAAXFxoAFxcaKRgXGsMYGBv/GRkc/xoZHN4bGh1FHBoeABwb
        HwAdHCAAGhkdABkZHAAYGBsTGBcapBcWGf4WFRj/FRQX7xQUFmYTEBMBExMVAB59/wIegP9kHoD/8B6B
        //8egv//HoL//x6C//8egv+cHoL/EB6B/wAaRX4AFxYZABcWGSQXFxq/GBgb/xkZHP8aGR3/Gxoe/xwb
        HtkcGx89HBwfABoYIQAbGh0AGxodEBkZHJwYGBv/Fxca/xYWGf8VFRf/FBQW7RQTFVwBABAAHn//Vx5/
        /+kegP//HoH//x6C//8egv//HoL//x6C//4egf+JHoD/BBgxUQAWFhgSFxYZsRgXGv8ZGBv/Ghkc/xsa
        Hf8cGx7/HBwf/x0cIM8eHSAmHRwgAB0cHwQbGh6JGhkc/hkYG/8YFxr/FxYZ/xYVGP8VFBf/ExMV5hMS
        FFIefv/EHn///x6A//8egf//HoL//x6C//8egv//HoL//x6B/+Megf8nGUB0ABYWGEwXFhn4GBca/xkY
        G/8aGh3/Gxse/xwcH/8dHCD/Hh0h/x4dIXUdHCAAHBsfJxwbHuMaGh3/GRkc/xgXGv8XFhn/FhUY/xUU
        F/8UExX/ExIUwR5+/9Uef///HoD//x6B//8egf//HoL//x6C//8egf//HoH/7h6B/zQaRX4AFhYZWhcW
        GfwYGBv/GRkc/xsaHf8cGx7/HRwg/x4dIf8fHiL/Hx4igx4dIQAdHB80HBsf7hsaHf8aGRz/GBgb/xcX
        Gf8WFRj/FRQX/xQTFf8TEhXSHn7/jR5///0egP//HoD//x6B//8egf//HoH//x6B//8egf+4HoD/EBk3
        YAAWFhkqFxYZ2xgYG/8ZGRz/Gxod/xwbHv8dHCD/Hh0h/x8eIu8gHyNKHx4iABwcHxAcGx64Gxod/xoZ
        HP8YGBv/Fxca/xYVGP8VFBf/FBMV/BMSFYgefv8VHn7/oh5///8egP//HoD//x6B//8egf//HoD/0B6A
        /zIegf8AFxIRABgaHQAXFxpQGBca5hkYHP8aGh3/Gxse/x0cH/8dHSD1Hh0hch8eIwQfHiIAGxseABwb
        HjIaGh3QGRkc/xgXG/8XFhn/FhUY/xUUF/8UExafExIVEx5+/wAefv8XHn7/qx5///8ef///Hn///x6A
        /9UegP85HoD/AB5//wAYIzUAFxcaABgYGwAYFxpXGRgb6hoZHP8bGh7/HBse+B0cH3weHSEFHh0hAB8e
        IgAbGh0AGxodABoZHDkZGBvVGBca/xcWGf8WFRj/FRQXqBQUFhUUExYAHn7/AB5+/wAefv8dHn7/yR5+
        //8efv/yHn//Sh5//wAef/8AHoD/AAAAAAAYFxoAGBcaABUXGgAZGBtUGRkcwxoaHc0bGh5yHBseCB0c
        HwAeHSEAAAAAABsaHQAaGRwAGRgbABgYG0oYFxryFxYZ/xYVGMUVFBcbFRQXABQUFgAefv8AHnz/AB56
        /wIefP+oHn3//x59/+ceff8lHn3/AB5//wAAAAAAAAAAAAAAAAAYGBoAGBgbABgXGgAaGRwNGhkdERkZ
        HAAbGh4AGxoeAAAAAAAAAAAAAAAAABkZHAAXFxoAFxcaJRcWGecWFRj/FRUXohMSFQEVFBcAFRQXAB50
        /wAed/8AHnn/CR56/7Qee///Hnv/6x57/y4eev8AHnj/AB53/wAAAAAAFRQXABYVGAAWFRgAFxYZJBgX
        GnEYFxp6GRgbNBkOEQAYGBsAGBcaAAAAAAAWFhgAFhYYABYWGQAXFhkuFhYY6xUVF/8VFBeuFBQWBxMT
        FQASERQAHnL/AB11/wMedv9zHnf/8x54//8eef/+Hnn/oR55/xEed/8AHnb/ABMSFgAbFx0AFRQXABUV
        Fy4WFhjJFxYZ/xgXGv8YFxreGBcaShcXGgAYFxoAFxYZABYVGAAWFRgAFhYYERYWGKEWFRj+FRQX/xQT
        FvETEhVuEhETAxIREwAgb/8CH3L/aR50//Iedf//Hnb//x53//8ed//+Hnf/lx52/w0edf8AGEOMABQT
        FgAUExYnFRQXxBUVF/8WFhj/FxYZ/xcXGv8XFxrdFxcaQRYWGQAWFRgAFhUYABYVGA0WFRiXFhUY/hUU
        F/8UExb/ExMV/xISFPARERNlEQ4RAh9t/1sfb//sH3H//x9y//8ec///HnT//x50//8edP/9HnT/hx9y
        /wUVKUwAEhIUFBMSFbYUExb/FBQW/xERE/8QEBL/FhUY/xYWGf8XFhnXFhYYYBYVGE4WFRhOFhUYmBUV
        F/wVFBf/FBMW/xMTFf8SEhT/ERET/xEQEuoQDxFXH2v/xh9s//8fbv//H2///x9w//8fcf//H3H//x9x
        //8fcf/kH3D/KBc3cQASERNOEhIU+BMSFf8SERT/DAwN/woJC/8QDxH/FRUX/xYVGP8WFRj7FRUX+BUU
        F/gVFBf/FBQW/xQTFv8TEhX/EhIU/xIRE/8REBL/EA8R/w8PEMMfZ//UH2n//x9r//8fbP//H23//x9u
        //8fbv//H27//x9u/+4fbv80Fzl6ABEQE1oRERP8EhIU/xERE/8ODg//DAwN/xAPEf8UExb/FBQW/xQU
        Fv8UFBb/FBMW/xQTFv8TExX/ExIU/xISFP8SERP/ERAS/xAQEv8PDxH/Dg4Q0R9l/4kfZf/8H2f//x9o
        //8faf//H2r//x9q//8fav//H2v/sh9r/w4VKlcAEBASKRAQEtoRERP/EhET/xEQEv8QEBL/EhIU/xMT
        Ff8TExXyExMVsxMTFagTEhWoExIU1BISFP8SERP/ERET/xEQEv8QDxH/Dw8R/w4OEPwODQ+EH2P/Ex9j
        /6AfZP//H2T//x9l//8fZv//H2b//x9n/8gfaP8qH2j/AA0CAAAREBIAEA8RThAQEuUREBL/ERET/xIR
        E/8SERP/EhEU9hISFHYTExUIEhEUAxMRFQISERMxERETzREQEv8QEBL/EA8R/w8OEP8ODhD/Dg0Pnw0N
        DxIfY/8AH2P/Fh9j/6kfY///H2P//x9j//8fY//MH2T/MB9m/wAfVP8AERgnABAPEQAQERMAEA8RVRAP
        EegQEBL/EBAS/xEQEvcRERN9EhETBhISFAATEhUAExIVABEREwAQEBI2EA8R0g8PEf8PDhD/Dg4P/w4N
        D6gNDQ8VDg0PAB9j/wAfY/8AH2P/HR9j/6QfY//nH2P/wh9j/zofZP8AH2P/AB9m/wAAAAAAEA8RAA8P
        EQAKDhIBDw8QYA8PEdcQDxHiEA8RgRAPEQkREBIAEhETAAAAAAARERMAEA8RABAPEQAPDhBADg4Qxg4N
        D+cODQ+iDQ0OHA0NDwAODQ8AACAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAGAIBw
        DgEAIAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAQAAHAOAAAgBAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgBAA=
</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Sales Invoices Archive</value>
  </data>
  <data name="&gt;&gt;barManager1.Name" xml:space="preserve">
    <value>barManager1</value>
  </data>
  <data name="&gt;&gt;barManager1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarManager, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;bar1.Name" xml:space="preserve">
    <value>bar1</value>
  </data>
  <data name="&gt;&gt;bar1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Bar, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtn_Help.Name" xml:space="preserve">
    <value>barBtn_Help</value>
  </data>
  <data name="&gt;&gt;barBtn_Help.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barMnu_Print.Name" xml:space="preserve">
    <value>barMnu_Print</value>
  </data>
  <data name="&gt;&gt;barMnu_Print.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarSubItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtn_Print1.Name" xml:space="preserve">
    <value>barBtn_Print1</value>
  </data>
  <data name="&gt;&gt;barBtn_Print1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtn_PrintData.Name" xml:space="preserve">
    <value>barBtn_PrintData</value>
  </data>
  <data name="&gt;&gt;barBtn_PrintData.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnRefresh.Name" xml:space="preserve">
    <value>barBtnRefresh</value>
  </data>
  <data name="&gt;&gt;barBtnRefresh.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnOpen.Name" xml:space="preserve">
    <value>barBtnOpen</value>
  </data>
  <data name="&gt;&gt;barBtnOpen.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnNew.Name" xml:space="preserve">
    <value>barBtnNew</value>
  </data>
  <data name="&gt;&gt;barBtnNew.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barBtnClose.Name" xml:space="preserve">
    <value>barBtnClose</value>
  </data>
  <data name="&gt;&gt;barBtnClose.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barAndDockingController1.Name" xml:space="preserve">
    <value>barAndDockingController1</value>
  </data>
  <data name="&gt;&gt;barAndDockingController1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarAndDockingController, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repositoryItemTextEdit1.Name" xml:space="preserve">
    <value>repositoryItemTextEdit1</value>
  </data>
  <data name="&gt;&gt;repositoryItemTextEdit1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemTextEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;bar2.Name" xml:space="preserve">
    <value>bar2</value>
  </data>
  <data name="&gt;&gt;bar2.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Bar, DevExpress.XtraBars.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;mi_OpenDealer.Name" xml:space="preserve">
    <value>mi_OpenDealer</value>
  </data>
  <data name="&gt;&gt;mi_OpenDealer.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;gridView1.Name" xml:space="preserve">
    <value>gridView1</value>
  </data>
  <data name="&gt;&gt;gridView1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_SL_InvoiceId.Name" xml:space="preserve">
    <value>col_SL_InvoiceId</value>
  </data>
  <data name="&gt;&gt;col_SL_InvoiceId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_InvoiceCode.Name" xml:space="preserve">
    <value>col_InvoiceCode</value>
  </data>
  <data name="&gt;&gt;col_InvoiceCode.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_InvoiceBookId.Name" xml:space="preserve">
    <value>col_InvoiceBookId</value>
  </data>
  <data name="&gt;&gt;col_InvoiceBookId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;rep_InvoiceBook.Name" xml:space="preserve">
    <value>rep_InvoiceBook</value>
  </data>
  <data name="&gt;&gt;rep_InvoiceBook.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_StoreId.Name" xml:space="preserve">
    <value>col_StoreId</value>
  </data>
  <data name="&gt;&gt;col_StoreId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_CustomerId.Name" xml:space="preserve">
    <value>col_CustomerId</value>
  </data>
  <data name="&gt;&gt;col_CustomerId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_InvoiceDate.Name" xml:space="preserve">
    <value>col_InvoiceDate</value>
  </data>
  <data name="&gt;&gt;col_InvoiceDate.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Notes.Name" xml:space="preserve">
    <value>col_Notes</value>
  </data>
  <data name="&gt;&gt;col_Notes.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_PayMethod.Name" xml:space="preserve">
    <value>col_PayMethod</value>
  </data>
  <data name="&gt;&gt;col_PayMethod.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;rep_paymethod.Name" xml:space="preserve">
    <value>rep_paymethod</value>
  </data>
  <data name="&gt;&gt;rep_paymethod.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemImageComboBox, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_DiscountRatio.Name" xml:space="preserve">
    <value>col_DiscountRatio</value>
  </data>
  <data name="&gt;&gt;col_DiscountRatio.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_DiscountValue.Name" xml:space="preserve">
    <value>col_DiscountValue</value>
  </data>
  <data name="&gt;&gt;col_DiscountValue.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Expenses.Name" xml:space="preserve">
    <value>col_Expenses</value>
  </data>
  <data name="&gt;&gt;col_Expenses.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colPaid.Name" xml:space="preserve">
    <value>colPaid</value>
  </data>
  <data name="&gt;&gt;colPaid.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colRemains.Name" xml:space="preserve">
    <value>colRemains</value>
  </data>
  <data name="&gt;&gt;colRemains.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_UserId.Name" xml:space="preserve">
    <value>col_UserId</value>
  </data>
  <data name="&gt;&gt;col_UserId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colStore.Name" xml:space="preserve">
    <value>colStore</value>
  </data>
  <data name="&gt;&gt;colStore.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_SalesEmpId.Name" xml:space="preserve">
    <value>col_SalesEmpId</value>
  </data>
  <data name="&gt;&gt;col_SalesEmpId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;rep_salesEmp.Name" xml:space="preserve">
    <value>rep_salesEmp</value>
  </data>
  <data name="&gt;&gt;rep_salesEmp.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_CustId.Name" xml:space="preserve">
    <value>col_CustId</value>
  </data>
  <data name="&gt;&gt;col_CustId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Net.Name" xml:space="preserve">
    <value>col_Net</value>
  </data>
  <data name="&gt;&gt;col_Net.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_TotalCostPrice.Name" xml:space="preserve">
    <value>col_TotalCostPrice</value>
  </data>
  <data name="&gt;&gt;col_TotalCostPrice.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Profit.Name" xml:space="preserve">
    <value>col_Profit</value>
  </data>
  <data name="&gt;&gt;col_Profit.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_ProfitRatio.Name" xml:space="preserve">
    <value>col_ProfitRatio</value>
  </data>
  <data name="&gt;&gt;col_ProfitRatio.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_ProfitCostRatio.Name" xml:space="preserve">
    <value>col_ProfitCostRatio</value>
  </data>
  <data name="&gt;&gt;col_ProfitCostRatio.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_GroupId.Name" xml:space="preserve">
    <value>col_GroupId</value>
  </data>
  <data name="&gt;&gt;col_GroupId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;rep_groupId.Name" xml:space="preserve">
    <value>rep_groupId</value>
  </data>
  <data name="&gt;&gt;rep_groupId.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Is_OutTrans.Name" xml:space="preserve">
    <value>col_Is_OutTrans</value>
  </data>
  <data name="&gt;&gt;col_Is_OutTrans.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_TaxValue.Name" xml:space="preserve">
    <value>col_TaxValue</value>
  </data>
  <data name="&gt;&gt;col_TaxValue.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_DeductTaxValue.Name" xml:space="preserve">
    <value>col_DeductTaxValue</value>
  </data>
  <data name="&gt;&gt;col_DeductTaxValue.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_AddTaxValue.Name" xml:space="preserve">
    <value>col_AddTaxValue</value>
  </data>
  <data name="&gt;&gt;col_AddTaxValue.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_DueDate.Name" xml:space="preserve">
    <value>col_DueDate</value>
  </data>
  <data name="&gt;&gt;col_DueDate.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_CrncId.Name" xml:space="preserve">
    <value>col_CrncId</value>
  </data>
  <data name="&gt;&gt;col_CrncId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repCrncy.Name" xml:space="preserve">
    <value>repCrncy</value>
  </data>
  <data name="&gt;&gt;repCrncy.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_CrncRate.Name" xml:space="preserve">
    <value>col_CrncRate</value>
  </data>
  <data name="&gt;&gt;col_CrncRate.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colDriverName.Name" xml:space="preserve">
    <value>colDriverName</value>
  </data>
  <data name="&gt;&gt;colDriverName.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colVehicleNumber.Name" xml:space="preserve">
    <value>colVehicleNumber</value>
  </data>
  <data name="&gt;&gt;colVehicleNumber.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colDestination.Name" xml:space="preserve">
    <value>colDestination</value>
  </data>
  <data name="&gt;&gt;colDestination.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Process.Name" xml:space="preserve">
    <value>col_Process</value>
  </data>
  <data name="&gt;&gt;col_Process.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_SourceCode.Name" xml:space="preserve">
    <value>col_SourceCode</value>
  </data>
  <data name="&gt;&gt;col_SourceCode.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>frm_SL_InvoiceArchiveList</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.XtraForm, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repositoryItemTextEdit1.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="bar2.Text" xml:space="preserve">
    <value>Custom 3</value>
  </data>
</root>