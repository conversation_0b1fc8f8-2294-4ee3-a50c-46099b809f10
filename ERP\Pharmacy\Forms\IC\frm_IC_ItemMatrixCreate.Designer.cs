﻿namespace Pharmacy.Forms
{
    partial class frm_IC_ItemMatrixCreate
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_IC_ItemMatrixCreate));
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.tab_DefineMatrix = new DevExpress.XtraTab.XtraTabPage();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.txtMtrxSprtr3 = new DevExpress.XtraEditors.TextEdit();
            this.txtMtrx3 = new DevExpress.XtraEditors.TextEdit();
            this.txtMtrxSprtr2 = new DevExpress.XtraEditors.TextEdit();
            this.txtMtrx2 = new DevExpress.XtraEditors.TextEdit();
            this.txtMtrxSprtr1 = new DevExpress.XtraEditors.TextEdit();
            this.txtMtrx1 = new DevExpress.XtraEditors.TextEdit();
            this.txtMtrxSprtr0 = new DevExpress.XtraEditors.TextEdit();
            this.txtMtrxCode0 = new DevExpress.XtraEditors.TextEdit();
            this.grd_Mtrx3 = new DevExpress.XtraGrid.GridControl();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemCheckEdit3 = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemSpinEdit2 = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDisabled3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.grd_Mtrx2 = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemCheckEdit2 = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemSpinEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDisabled2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.grd_Mtrx1 = new DevExpress.XtraGrid.GridControl();
            this.gv_SalesPerQty = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_available = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemCheckEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
            this.col_MDName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_SLQtyNums = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.col_MDCode = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_MatrixId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_MatrixDetailId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ItmMtrxDetailId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colDisabled1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.lkpMatrix3 = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.lkpMatrix2 = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.lkpMatrix1 = new DevExpress.XtraEditors.LookUpEdit();
            this.txtParentItemName = new DevExpress.XtraEditors.TextEdit();
            this.tab_GeneratedItems = new DevExpress.XtraTab.XtraTabPage();
            this.btn_Save = new DevExpress.XtraEditors.SimpleButton();
            this.btn_GenerateItems = new DevExpress.XtraEditors.SimpleButton();
            this.grd_genItems = new DevExpress.XtraGrid.GridControl();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_GenMatrix3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_GenMatrix2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_GenMatrix1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_GenItemName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_GenItemCode = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Serial = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemSpinEdit3 = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.repositoryItemCheckEdit4 = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.tab_DefineMatrix.SuspendLayout();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtMtrxSprtr3.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMtrx3.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMtrxSprtr2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMtrx2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMtrxSprtr1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMtrx1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMtrxSprtr0.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMtrxCode0.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grd_Mtrx3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSpinEdit2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grd_Mtrx2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSpinEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grd_Mtrx1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv_SalesPerQty)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_SLQtyNums)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpMatrix3.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpMatrix2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpMatrix1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtParentItemName.Properties)).BeginInit();
            this.tab_GeneratedItems.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grd_genItems)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSpinEdit3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit4)).BeginInit();
            this.SuspendLayout();
            // 
            // labelControl1
            // 
            resources.ApplyResources(this.labelControl1, "labelControl1");
            this.labelControl1.Name = "labelControl1";
            // 
            // xtraTabControl1
            // 
            resources.ApplyResources(this.xtraTabControl1, "xtraTabControl1");
            this.xtraTabControl1.AppearancePage.Header.FontSizeDelta = ((int)(resources.GetObject("xtraTabControl1.AppearancePage.Header.FontSizeDelta")));
            this.xtraTabControl1.AppearancePage.Header.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("xtraTabControl1.AppearancePage.Header.FontStyleDelta")));
            this.xtraTabControl1.AppearancePage.Header.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("xtraTabControl1.AppearancePage.Header.GradientMode")));
            this.xtraTabControl1.AppearancePage.Header.Image = ((System.Drawing.Image)(resources.GetObject("xtraTabControl1.AppearancePage.Header.Image")));
            this.xtraTabControl1.AppearancePage.Header.Options.UseTextOptions = true;
            this.xtraTabControl1.AppearancePage.Header.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.tab_DefineMatrix;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.tab_GeneratedItems,
            this.tab_DefineMatrix});
            // 
            // tab_DefineMatrix
            // 
            resources.ApplyResources(this.tab_DefineMatrix, "tab_DefineMatrix");
            this.tab_DefineMatrix.Controls.Add(this.groupBox1);
            this.tab_DefineMatrix.Controls.Add(this.grd_Mtrx3);
            this.tab_DefineMatrix.Controls.Add(this.grd_Mtrx2);
            this.tab_DefineMatrix.Controls.Add(this.grd_Mtrx1);
            this.tab_DefineMatrix.Controls.Add(this.labelControl4);
            this.tab_DefineMatrix.Controls.Add(this.lkpMatrix3);
            this.tab_DefineMatrix.Controls.Add(this.labelControl3);
            this.tab_DefineMatrix.Controls.Add(this.lkpMatrix2);
            this.tab_DefineMatrix.Controls.Add(this.labelControl2);
            this.tab_DefineMatrix.Controls.Add(this.lkpMatrix1);
            this.tab_DefineMatrix.Controls.Add(this.txtParentItemName);
            this.tab_DefineMatrix.Controls.Add(this.labelControl1);
            this.tab_DefineMatrix.Name = "tab_DefineMatrix";
            // 
            // groupBox1
            // 
            resources.ApplyResources(this.groupBox1, "groupBox1");
            this.groupBox1.Controls.Add(this.txtMtrxSprtr3);
            this.groupBox1.Controls.Add(this.txtMtrx3);
            this.groupBox1.Controls.Add(this.txtMtrxSprtr2);
            this.groupBox1.Controls.Add(this.txtMtrx2);
            this.groupBox1.Controls.Add(this.txtMtrxSprtr1);
            this.groupBox1.Controls.Add(this.txtMtrx1);
            this.groupBox1.Controls.Add(this.txtMtrxSprtr0);
            this.groupBox1.Controls.Add(this.txtMtrxCode0);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.TabStop = false;
            // 
            // txtMtrxSprtr3
            // 
            resources.ApplyResources(this.txtMtrxSprtr3, "txtMtrxSprtr3");
            this.txtMtrxSprtr3.EnterMoveNextControl = true;
            this.txtMtrxSprtr3.Name = "txtMtrxSprtr3";
            this.txtMtrxSprtr3.Properties.AccessibleDescription = resources.GetString("txtMtrxSprtr3.Properties.AccessibleDescription");
            this.txtMtrxSprtr3.Properties.AccessibleName = resources.GetString("txtMtrxSprtr3.Properties.AccessibleName");
            this.txtMtrxSprtr3.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtMtrxSprtr3.Properties.Appearance.FontSizeDelta")));
            this.txtMtrxSprtr3.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtMtrxSprtr3.Properties.Appearance.FontStyleDelta")));
            this.txtMtrxSprtr3.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtMtrxSprtr3.Properties.Appearance.GradientMode")));
            this.txtMtrxSprtr3.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtMtrxSprtr3.Properties.Appearance.Image")));
            this.txtMtrxSprtr3.Properties.Appearance.Options.UseTextOptions = true;
            this.txtMtrxSprtr3.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.txtMtrxSprtr3.Properties.AutoHeight = ((bool)(resources.GetObject("txtMtrxSprtr3.Properties.AutoHeight")));
            this.txtMtrxSprtr3.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtMtrxSprtr3.Properties.Mask.AutoComplete")));
            this.txtMtrxSprtr3.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtMtrxSprtr3.Properties.Mask.BeepOnError")));
            this.txtMtrxSprtr3.Properties.Mask.EditMask = resources.GetString("txtMtrxSprtr3.Properties.Mask.EditMask");
            this.txtMtrxSprtr3.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtMtrxSprtr3.Properties.Mask.IgnoreMaskBlank")));
            this.txtMtrxSprtr3.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtMtrxSprtr3.Properties.Mask.MaskType")));
            this.txtMtrxSprtr3.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtMtrxSprtr3.Properties.Mask.PlaceHolder")));
            this.txtMtrxSprtr3.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtMtrxSprtr3.Properties.Mask.SaveLiteral")));
            this.txtMtrxSprtr3.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtMtrxSprtr3.Properties.Mask.ShowPlaceHolders")));
            this.txtMtrxSprtr3.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtMtrxSprtr3.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtMtrxSprtr3.Properties.MaxLength = 1;
            this.txtMtrxSprtr3.Properties.NullValuePrompt = resources.GetString("txtMtrxSprtr3.Properties.NullValuePrompt");
            this.txtMtrxSprtr3.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtMtrxSprtr3.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // txtMtrx3
            // 
            resources.ApplyResources(this.txtMtrx3, "txtMtrx3");
            this.txtMtrx3.EnterMoveNextControl = true;
            this.txtMtrx3.Name = "txtMtrx3";
            this.txtMtrx3.Properties.AccessibleDescription = resources.GetString("txtMtrx3.Properties.AccessibleDescription");
            this.txtMtrx3.Properties.AccessibleName = resources.GetString("txtMtrx3.Properties.AccessibleName");
            this.txtMtrx3.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtMtrx3.Properties.Appearance.FontSizeDelta")));
            this.txtMtrx3.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtMtrx3.Properties.Appearance.FontStyleDelta")));
            this.txtMtrx3.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtMtrx3.Properties.Appearance.GradientMode")));
            this.txtMtrx3.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtMtrx3.Properties.Appearance.Image")));
            this.txtMtrx3.Properties.Appearance.Options.UseTextOptions = true;
            this.txtMtrx3.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtMtrx3.Properties.AppearanceDisabled.BackColor = ((System.Drawing.Color)(resources.GetObject("txtMtrx3.Properties.AppearanceDisabled.BackColor")));
            this.txtMtrx3.Properties.AppearanceDisabled.FontSizeDelta = ((int)(resources.GetObject("txtMtrx3.Properties.AppearanceDisabled.FontSizeDelta")));
            this.txtMtrx3.Properties.AppearanceDisabled.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtMtrx3.Properties.AppearanceDisabled.FontStyleDelta")));
            this.txtMtrx3.Properties.AppearanceDisabled.ForeColor = ((System.Drawing.Color)(resources.GetObject("txtMtrx3.Properties.AppearanceDisabled.ForeColor")));
            this.txtMtrx3.Properties.AppearanceDisabled.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtMtrx3.Properties.AppearanceDisabled.GradientMode")));
            this.txtMtrx3.Properties.AppearanceDisabled.Image = ((System.Drawing.Image)(resources.GetObject("txtMtrx3.Properties.AppearanceDisabled.Image")));
            this.txtMtrx3.Properties.AppearanceDisabled.Options.UseBackColor = true;
            this.txtMtrx3.Properties.AppearanceDisabled.Options.UseForeColor = true;
            this.txtMtrx3.Properties.AutoHeight = ((bool)(resources.GetObject("txtMtrx3.Properties.AutoHeight")));
            this.txtMtrx3.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtMtrx3.Properties.Mask.AutoComplete")));
            this.txtMtrx3.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtMtrx3.Properties.Mask.BeepOnError")));
            this.txtMtrx3.Properties.Mask.EditMask = resources.GetString("txtMtrx3.Properties.Mask.EditMask");
            this.txtMtrx3.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtMtrx3.Properties.Mask.IgnoreMaskBlank")));
            this.txtMtrx3.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtMtrx3.Properties.Mask.MaskType")));
            this.txtMtrx3.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtMtrx3.Properties.Mask.PlaceHolder")));
            this.txtMtrx3.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtMtrx3.Properties.Mask.SaveLiteral")));
            this.txtMtrx3.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtMtrx3.Properties.Mask.ShowPlaceHolders")));
            this.txtMtrx3.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtMtrx3.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtMtrx3.Properties.NullValuePrompt = resources.GetString("txtMtrx3.Properties.NullValuePrompt");
            this.txtMtrx3.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtMtrx3.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // txtMtrxSprtr2
            // 
            resources.ApplyResources(this.txtMtrxSprtr2, "txtMtrxSprtr2");
            this.txtMtrxSprtr2.EnterMoveNextControl = true;
            this.txtMtrxSprtr2.Name = "txtMtrxSprtr2";
            this.txtMtrxSprtr2.Properties.AccessibleDescription = resources.GetString("txtMtrxSprtr2.Properties.AccessibleDescription");
            this.txtMtrxSprtr2.Properties.AccessibleName = resources.GetString("txtMtrxSprtr2.Properties.AccessibleName");
            this.txtMtrxSprtr2.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtMtrxSprtr2.Properties.Appearance.FontSizeDelta")));
            this.txtMtrxSprtr2.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtMtrxSprtr2.Properties.Appearance.FontStyleDelta")));
            this.txtMtrxSprtr2.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtMtrxSprtr2.Properties.Appearance.GradientMode")));
            this.txtMtrxSprtr2.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtMtrxSprtr2.Properties.Appearance.Image")));
            this.txtMtrxSprtr2.Properties.Appearance.Options.UseTextOptions = true;
            this.txtMtrxSprtr2.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.txtMtrxSprtr2.Properties.AutoHeight = ((bool)(resources.GetObject("txtMtrxSprtr2.Properties.AutoHeight")));
            this.txtMtrxSprtr2.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtMtrxSprtr2.Properties.Mask.AutoComplete")));
            this.txtMtrxSprtr2.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtMtrxSprtr2.Properties.Mask.BeepOnError")));
            this.txtMtrxSprtr2.Properties.Mask.EditMask = resources.GetString("txtMtrxSprtr2.Properties.Mask.EditMask");
            this.txtMtrxSprtr2.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtMtrxSprtr2.Properties.Mask.IgnoreMaskBlank")));
            this.txtMtrxSprtr2.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtMtrxSprtr2.Properties.Mask.MaskType")));
            this.txtMtrxSprtr2.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtMtrxSprtr2.Properties.Mask.PlaceHolder")));
            this.txtMtrxSprtr2.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtMtrxSprtr2.Properties.Mask.SaveLiteral")));
            this.txtMtrxSprtr2.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtMtrxSprtr2.Properties.Mask.ShowPlaceHolders")));
            this.txtMtrxSprtr2.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtMtrxSprtr2.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtMtrxSprtr2.Properties.MaxLength = 1;
            this.txtMtrxSprtr2.Properties.NullValuePrompt = resources.GetString("txtMtrxSprtr2.Properties.NullValuePrompt");
            this.txtMtrxSprtr2.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtMtrxSprtr2.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // txtMtrx2
            // 
            resources.ApplyResources(this.txtMtrx2, "txtMtrx2");
            this.txtMtrx2.EnterMoveNextControl = true;
            this.txtMtrx2.Name = "txtMtrx2";
            this.txtMtrx2.Properties.AccessibleDescription = resources.GetString("txtMtrx2.Properties.AccessibleDescription");
            this.txtMtrx2.Properties.AccessibleName = resources.GetString("txtMtrx2.Properties.AccessibleName");
            this.txtMtrx2.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtMtrx2.Properties.Appearance.FontSizeDelta")));
            this.txtMtrx2.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtMtrx2.Properties.Appearance.FontStyleDelta")));
            this.txtMtrx2.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtMtrx2.Properties.Appearance.GradientMode")));
            this.txtMtrx2.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtMtrx2.Properties.Appearance.Image")));
            this.txtMtrx2.Properties.Appearance.Options.UseTextOptions = true;
            this.txtMtrx2.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtMtrx2.Properties.AppearanceDisabled.BackColor = ((System.Drawing.Color)(resources.GetObject("txtMtrx2.Properties.AppearanceDisabled.BackColor")));
            this.txtMtrx2.Properties.AppearanceDisabled.FontSizeDelta = ((int)(resources.GetObject("txtMtrx2.Properties.AppearanceDisabled.FontSizeDelta")));
            this.txtMtrx2.Properties.AppearanceDisabled.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtMtrx2.Properties.AppearanceDisabled.FontStyleDelta")));
            this.txtMtrx2.Properties.AppearanceDisabled.ForeColor = ((System.Drawing.Color)(resources.GetObject("txtMtrx2.Properties.AppearanceDisabled.ForeColor")));
            this.txtMtrx2.Properties.AppearanceDisabled.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtMtrx2.Properties.AppearanceDisabled.GradientMode")));
            this.txtMtrx2.Properties.AppearanceDisabled.Image = ((System.Drawing.Image)(resources.GetObject("txtMtrx2.Properties.AppearanceDisabled.Image")));
            this.txtMtrx2.Properties.AppearanceDisabled.Options.UseBackColor = true;
            this.txtMtrx2.Properties.AppearanceDisabled.Options.UseForeColor = true;
            this.txtMtrx2.Properties.AutoHeight = ((bool)(resources.GetObject("txtMtrx2.Properties.AutoHeight")));
            this.txtMtrx2.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtMtrx2.Properties.Mask.AutoComplete")));
            this.txtMtrx2.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtMtrx2.Properties.Mask.BeepOnError")));
            this.txtMtrx2.Properties.Mask.EditMask = resources.GetString("txtMtrx2.Properties.Mask.EditMask");
            this.txtMtrx2.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtMtrx2.Properties.Mask.IgnoreMaskBlank")));
            this.txtMtrx2.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtMtrx2.Properties.Mask.MaskType")));
            this.txtMtrx2.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtMtrx2.Properties.Mask.PlaceHolder")));
            this.txtMtrx2.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtMtrx2.Properties.Mask.SaveLiteral")));
            this.txtMtrx2.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtMtrx2.Properties.Mask.ShowPlaceHolders")));
            this.txtMtrx2.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtMtrx2.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtMtrx2.Properties.NullValuePrompt = resources.GetString("txtMtrx2.Properties.NullValuePrompt");
            this.txtMtrx2.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtMtrx2.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // txtMtrxSprtr1
            // 
            resources.ApplyResources(this.txtMtrxSprtr1, "txtMtrxSprtr1");
            this.txtMtrxSprtr1.EnterMoveNextControl = true;
            this.txtMtrxSprtr1.Name = "txtMtrxSprtr1";
            this.txtMtrxSprtr1.Properties.AccessibleDescription = resources.GetString("txtMtrxSprtr1.Properties.AccessibleDescription");
            this.txtMtrxSprtr1.Properties.AccessibleName = resources.GetString("txtMtrxSprtr1.Properties.AccessibleName");
            this.txtMtrxSprtr1.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtMtrxSprtr1.Properties.Appearance.FontSizeDelta")));
            this.txtMtrxSprtr1.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtMtrxSprtr1.Properties.Appearance.FontStyleDelta")));
            this.txtMtrxSprtr1.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtMtrxSprtr1.Properties.Appearance.GradientMode")));
            this.txtMtrxSprtr1.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtMtrxSprtr1.Properties.Appearance.Image")));
            this.txtMtrxSprtr1.Properties.Appearance.Options.UseTextOptions = true;
            this.txtMtrxSprtr1.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.txtMtrxSprtr1.Properties.AutoHeight = ((bool)(resources.GetObject("txtMtrxSprtr1.Properties.AutoHeight")));
            this.txtMtrxSprtr1.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtMtrxSprtr1.Properties.Mask.AutoComplete")));
            this.txtMtrxSprtr1.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtMtrxSprtr1.Properties.Mask.BeepOnError")));
            this.txtMtrxSprtr1.Properties.Mask.EditMask = resources.GetString("txtMtrxSprtr1.Properties.Mask.EditMask");
            this.txtMtrxSprtr1.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtMtrxSprtr1.Properties.Mask.IgnoreMaskBlank")));
            this.txtMtrxSprtr1.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtMtrxSprtr1.Properties.Mask.MaskType")));
            this.txtMtrxSprtr1.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtMtrxSprtr1.Properties.Mask.PlaceHolder")));
            this.txtMtrxSprtr1.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtMtrxSprtr1.Properties.Mask.SaveLiteral")));
            this.txtMtrxSprtr1.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtMtrxSprtr1.Properties.Mask.ShowPlaceHolders")));
            this.txtMtrxSprtr1.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtMtrxSprtr1.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtMtrxSprtr1.Properties.MaxLength = 1;
            this.txtMtrxSprtr1.Properties.NullValuePrompt = resources.GetString("txtMtrxSprtr1.Properties.NullValuePrompt");
            this.txtMtrxSprtr1.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtMtrxSprtr1.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // txtMtrx1
            // 
            resources.ApplyResources(this.txtMtrx1, "txtMtrx1");
            this.txtMtrx1.EnterMoveNextControl = true;
            this.txtMtrx1.Name = "txtMtrx1";
            this.txtMtrx1.Properties.AccessibleDescription = resources.GetString("txtMtrx1.Properties.AccessibleDescription");
            this.txtMtrx1.Properties.AccessibleName = resources.GetString("txtMtrx1.Properties.AccessibleName");
            this.txtMtrx1.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtMtrx1.Properties.Appearance.FontSizeDelta")));
            this.txtMtrx1.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtMtrx1.Properties.Appearance.FontStyleDelta")));
            this.txtMtrx1.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtMtrx1.Properties.Appearance.GradientMode")));
            this.txtMtrx1.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtMtrx1.Properties.Appearance.Image")));
            this.txtMtrx1.Properties.Appearance.Options.UseTextOptions = true;
            this.txtMtrx1.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtMtrx1.Properties.AppearanceDisabled.BackColor = ((System.Drawing.Color)(resources.GetObject("txtMtrx1.Properties.AppearanceDisabled.BackColor")));
            this.txtMtrx1.Properties.AppearanceDisabled.FontSizeDelta = ((int)(resources.GetObject("txtMtrx1.Properties.AppearanceDisabled.FontSizeDelta")));
            this.txtMtrx1.Properties.AppearanceDisabled.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtMtrx1.Properties.AppearanceDisabled.FontStyleDelta")));
            this.txtMtrx1.Properties.AppearanceDisabled.ForeColor = ((System.Drawing.Color)(resources.GetObject("txtMtrx1.Properties.AppearanceDisabled.ForeColor")));
            this.txtMtrx1.Properties.AppearanceDisabled.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtMtrx1.Properties.AppearanceDisabled.GradientMode")));
            this.txtMtrx1.Properties.AppearanceDisabled.Image = ((System.Drawing.Image)(resources.GetObject("txtMtrx1.Properties.AppearanceDisabled.Image")));
            this.txtMtrx1.Properties.AppearanceDisabled.Options.UseBackColor = true;
            this.txtMtrx1.Properties.AppearanceDisabled.Options.UseForeColor = true;
            this.txtMtrx1.Properties.AutoHeight = ((bool)(resources.GetObject("txtMtrx1.Properties.AutoHeight")));
            this.txtMtrx1.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtMtrx1.Properties.Mask.AutoComplete")));
            this.txtMtrx1.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtMtrx1.Properties.Mask.BeepOnError")));
            this.txtMtrx1.Properties.Mask.EditMask = resources.GetString("txtMtrx1.Properties.Mask.EditMask");
            this.txtMtrx1.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtMtrx1.Properties.Mask.IgnoreMaskBlank")));
            this.txtMtrx1.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtMtrx1.Properties.Mask.MaskType")));
            this.txtMtrx1.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtMtrx1.Properties.Mask.PlaceHolder")));
            this.txtMtrx1.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtMtrx1.Properties.Mask.SaveLiteral")));
            this.txtMtrx1.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtMtrx1.Properties.Mask.ShowPlaceHolders")));
            this.txtMtrx1.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtMtrx1.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtMtrx1.Properties.NullValuePrompt = resources.GetString("txtMtrx1.Properties.NullValuePrompt");
            this.txtMtrx1.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtMtrx1.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // txtMtrxSprtr0
            // 
            resources.ApplyResources(this.txtMtrxSprtr0, "txtMtrxSprtr0");
            this.txtMtrxSprtr0.EnterMoveNextControl = true;
            this.txtMtrxSprtr0.Name = "txtMtrxSprtr0";
            this.txtMtrxSprtr0.Properties.AccessibleDescription = resources.GetString("txtMtrxSprtr0.Properties.AccessibleDescription");
            this.txtMtrxSprtr0.Properties.AccessibleName = resources.GetString("txtMtrxSprtr0.Properties.AccessibleName");
            this.txtMtrxSprtr0.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtMtrxSprtr0.Properties.Appearance.FontSizeDelta")));
            this.txtMtrxSprtr0.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtMtrxSprtr0.Properties.Appearance.FontStyleDelta")));
            this.txtMtrxSprtr0.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtMtrxSprtr0.Properties.Appearance.GradientMode")));
            this.txtMtrxSprtr0.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtMtrxSprtr0.Properties.Appearance.Image")));
            this.txtMtrxSprtr0.Properties.Appearance.Options.UseTextOptions = true;
            this.txtMtrxSprtr0.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.txtMtrxSprtr0.Properties.AutoHeight = ((bool)(resources.GetObject("txtMtrxSprtr0.Properties.AutoHeight")));
            this.txtMtrxSprtr0.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtMtrxSprtr0.Properties.Mask.AutoComplete")));
            this.txtMtrxSprtr0.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtMtrxSprtr0.Properties.Mask.BeepOnError")));
            this.txtMtrxSprtr0.Properties.Mask.EditMask = resources.GetString("txtMtrxSprtr0.Properties.Mask.EditMask");
            this.txtMtrxSprtr0.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtMtrxSprtr0.Properties.Mask.IgnoreMaskBlank")));
            this.txtMtrxSprtr0.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtMtrxSprtr0.Properties.Mask.MaskType")));
            this.txtMtrxSprtr0.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtMtrxSprtr0.Properties.Mask.PlaceHolder")));
            this.txtMtrxSprtr0.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtMtrxSprtr0.Properties.Mask.SaveLiteral")));
            this.txtMtrxSprtr0.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtMtrxSprtr0.Properties.Mask.ShowPlaceHolders")));
            this.txtMtrxSprtr0.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtMtrxSprtr0.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtMtrxSprtr0.Properties.MaxLength = 1;
            this.txtMtrxSprtr0.Properties.NullValuePrompt = resources.GetString("txtMtrxSprtr0.Properties.NullValuePrompt");
            this.txtMtrxSprtr0.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtMtrxSprtr0.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // txtMtrxCode0
            // 
            resources.ApplyResources(this.txtMtrxCode0, "txtMtrxCode0");
            this.txtMtrxCode0.EnterMoveNextControl = true;
            this.txtMtrxCode0.Name = "txtMtrxCode0";
            this.txtMtrxCode0.Properties.AccessibleDescription = resources.GetString("txtMtrxCode0.Properties.AccessibleDescription");
            this.txtMtrxCode0.Properties.AccessibleName = resources.GetString("txtMtrxCode0.Properties.AccessibleName");
            this.txtMtrxCode0.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtMtrxCode0.Properties.Appearance.FontSizeDelta")));
            this.txtMtrxCode0.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtMtrxCode0.Properties.Appearance.FontStyleDelta")));
            this.txtMtrxCode0.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtMtrxCode0.Properties.Appearance.GradientMode")));
            this.txtMtrxCode0.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtMtrxCode0.Properties.Appearance.Image")));
            this.txtMtrxCode0.Properties.Appearance.Options.UseTextOptions = true;
            this.txtMtrxCode0.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtMtrxCode0.Properties.AutoHeight = ((bool)(resources.GetObject("txtMtrxCode0.Properties.AutoHeight")));
            this.txtMtrxCode0.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtMtrxCode0.Properties.Mask.AutoComplete")));
            this.txtMtrxCode0.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtMtrxCode0.Properties.Mask.BeepOnError")));
            this.txtMtrxCode0.Properties.Mask.EditMask = resources.GetString("txtMtrxCode0.Properties.Mask.EditMask");
            this.txtMtrxCode0.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtMtrxCode0.Properties.Mask.IgnoreMaskBlank")));
            this.txtMtrxCode0.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtMtrxCode0.Properties.Mask.MaskType")));
            this.txtMtrxCode0.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtMtrxCode0.Properties.Mask.PlaceHolder")));
            this.txtMtrxCode0.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtMtrxCode0.Properties.Mask.SaveLiteral")));
            this.txtMtrxCode0.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtMtrxCode0.Properties.Mask.ShowPlaceHolders")));
            this.txtMtrxCode0.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtMtrxCode0.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtMtrxCode0.Properties.MaxLength = 5;
            this.txtMtrxCode0.Properties.NullValuePrompt = resources.GetString("txtMtrxCode0.Properties.NullValuePrompt");
            this.txtMtrxCode0.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtMtrxCode0.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // grd_Mtrx3
            // 
            resources.ApplyResources(this.grd_Mtrx3, "grd_Mtrx3");
            this.grd_Mtrx3.EmbeddedNavigator.AccessibleDescription = resources.GetString("grd_Mtrx3.EmbeddedNavigator.AccessibleDescription");
            this.grd_Mtrx3.EmbeddedNavigator.AccessibleName = resources.GetString("grd_Mtrx3.EmbeddedNavigator.AccessibleName");
            this.grd_Mtrx3.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grd_Mtrx3.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grd_Mtrx3.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grd_Mtrx3.EmbeddedNavigator.Anchor")));
            this.grd_Mtrx3.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grd_Mtrx3.EmbeddedNavigator.BackgroundImage")));
            this.grd_Mtrx3.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grd_Mtrx3.EmbeddedNavigator.BackgroundImageLayout")));
            this.grd_Mtrx3.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grd_Mtrx3.EmbeddedNavigator.ImeMode")));
            this.grd_Mtrx3.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grd_Mtrx3.EmbeddedNavigator.MaximumSize")));
            this.grd_Mtrx3.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grd_Mtrx3.EmbeddedNavigator.TextLocation")));
            this.grd_Mtrx3.EmbeddedNavigator.ToolTip = resources.GetString("grd_Mtrx3.EmbeddedNavigator.ToolTip");
            this.grd_Mtrx3.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grd_Mtrx3.EmbeddedNavigator.ToolTipIconType")));
            this.grd_Mtrx3.EmbeddedNavigator.ToolTipTitle = resources.GetString("grd_Mtrx3.EmbeddedNavigator.ToolTipTitle");
            this.grd_Mtrx3.MainView = this.gridView2;
            this.grd_Mtrx3.Name = "grd_Mtrx3";
            this.grd_Mtrx3.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemSpinEdit2,
            this.repositoryItemCheckEdit3});
            this.grd_Mtrx3.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView2});
            // 
            // gridView2
            // 
            this.gridView2.Appearance.FooterPanel.FontSizeDelta = ((int)(resources.GetObject("gridView2.Appearance.FooterPanel.FontSizeDelta")));
            this.gridView2.Appearance.FooterPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView2.Appearance.FooterPanel.FontStyleDelta")));
            this.gridView2.Appearance.FooterPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView2.Appearance.FooterPanel.GradientMode")));
            this.gridView2.Appearance.FooterPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView2.Appearance.FooterPanel.Image")));
            this.gridView2.Appearance.FooterPanel.Options.UseTextOptions = true;
            this.gridView2.Appearance.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.gridView2, "gridView2");
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn7,
            this.gridColumn8,
            this.gridColumn9,
            this.gridColumn10,
            this.gridColumn11,
            this.gridColumn12,
            this.colDisabled3});
            this.gridView2.GridControl = this.grd_Mtrx3;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.True;
            this.gridView2.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView2.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.gridView2.OptionsSelection.EnableAppearanceHideSelection = false;
            this.gridView2.OptionsView.RowAutoHeight = true;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            this.gridView2.ShowingEditor += new System.ComponentModel.CancelEventHandler(this.gridview_ShowingEditor);
            // 
            // gridColumn7
            // 
            resources.ApplyResources(this.gridColumn7, "gridColumn7");
            this.gridColumn7.ColumnEdit = this.repositoryItemCheckEdit3;
            this.gridColumn7.FieldName = "Available";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.OptionsColumn.AllowMove = false;
            this.gridColumn7.OptionsColumn.AllowShowHide = false;
            this.gridColumn7.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn7.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // repositoryItemCheckEdit3
            // 
            resources.ApplyResources(this.repositoryItemCheckEdit3, "repositoryItemCheckEdit3");
            this.repositoryItemCheckEdit3.Name = "repositoryItemCheckEdit3";
            // 
            // gridColumn8
            // 
            this.gridColumn8.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn8.AppearanceCell.FontSizeDelta")));
            this.gridColumn8.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn8.AppearanceCell.FontStyleDelta")));
            this.gridColumn8.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn8.AppearanceCell.GradientMode")));
            this.gridColumn8.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn8.AppearanceCell.Image")));
            this.gridColumn8.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn8.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridColumn8.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn8.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn8.AppearanceHeader.Font = ((System.Drawing.Font)(resources.GetObject("gridColumn8.AppearanceHeader.Font")));
            this.gridColumn8.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn8.AppearanceHeader.FontSizeDelta")));
            this.gridColumn8.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn8.AppearanceHeader.FontStyleDelta")));
            this.gridColumn8.AppearanceHeader.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridColumn8.AppearanceHeader.ForeColor")));
            this.gridColumn8.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn8.AppearanceHeader.GradientMode")));
            this.gridColumn8.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn8.AppearanceHeader.Image")));
            this.gridColumn8.AppearanceHeader.Options.UseFont = true;
            this.gridColumn8.AppearanceHeader.Options.UseForeColor = true;
            this.gridColumn8.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn8.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridColumn8.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn8.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn8, "gridColumn8");
            this.gridColumn8.ColumnEdit = this.repositoryItemSpinEdit2;
            this.gridColumn8.FieldName = "MDName";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.OptionsColumn.AllowEdit = false;
            this.gridColumn8.OptionsColumn.AllowFocus = false;
            this.gridColumn8.OptionsColumn.AllowMove = false;
            this.gridColumn8.OptionsColumn.AllowShowHide = false;
            this.gridColumn8.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn8.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // repositoryItemSpinEdit2
            // 
            resources.ApplyResources(this.repositoryItemSpinEdit2, "repositoryItemSpinEdit2");
            this.repositoryItemSpinEdit2.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.repositoryItemSpinEdit2.DisplayFormat.FormatString = "f3";
            this.repositoryItemSpinEdit2.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.repositoryItemSpinEdit2.EditFormat.FormatString = "f3";
            this.repositoryItemSpinEdit2.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.repositoryItemSpinEdit2.Increment = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.repositoryItemSpinEdit2.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemSpinEdit2.Mask.AutoComplete")));
            this.repositoryItemSpinEdit2.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemSpinEdit2.Mask.BeepOnError")));
            this.repositoryItemSpinEdit2.Mask.EditMask = resources.GetString("repositoryItemSpinEdit2.Mask.EditMask");
            this.repositoryItemSpinEdit2.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemSpinEdit2.Mask.IgnoreMaskBlank")));
            this.repositoryItemSpinEdit2.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemSpinEdit2.Mask.MaskType")));
            this.repositoryItemSpinEdit2.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemSpinEdit2.Mask.PlaceHolder")));
            this.repositoryItemSpinEdit2.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemSpinEdit2.Mask.SaveLiteral")));
            this.repositoryItemSpinEdit2.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemSpinEdit2.Mask.ShowPlaceHolders")));
            this.repositoryItemSpinEdit2.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemSpinEdit2.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemSpinEdit2.MaxValue = new decimal(new int[] {
            10000000,
            0,
            0,
            0});
            this.repositoryItemSpinEdit2.Name = "repositoryItemSpinEdit2";
            // 
            // gridColumn9
            // 
            this.gridColumn9.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn9.AppearanceCell.FontSizeDelta")));
            this.gridColumn9.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn9.AppearanceCell.FontStyleDelta")));
            this.gridColumn9.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn9.AppearanceCell.GradientMode")));
            this.gridColumn9.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn9.AppearanceCell.Image")));
            this.gridColumn9.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn9.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridColumn9.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn9.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn9.AppearanceHeader.Font = ((System.Drawing.Font)(resources.GetObject("gridColumn9.AppearanceHeader.Font")));
            this.gridColumn9.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn9.AppearanceHeader.FontSizeDelta")));
            this.gridColumn9.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn9.AppearanceHeader.FontStyleDelta")));
            this.gridColumn9.AppearanceHeader.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridColumn9.AppearanceHeader.ForeColor")));
            this.gridColumn9.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn9.AppearanceHeader.GradientMode")));
            this.gridColumn9.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn9.AppearanceHeader.Image")));
            this.gridColumn9.AppearanceHeader.Options.UseFont = true;
            this.gridColumn9.AppearanceHeader.Options.UseForeColor = true;
            this.gridColumn9.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn9.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridColumn9.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn9.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn9, "gridColumn9");
            this.gridColumn9.FieldName = "MDCode";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.OptionsColumn.AllowEdit = false;
            this.gridColumn9.OptionsColumn.AllowFocus = false;
            this.gridColumn9.OptionsColumn.AllowMove = false;
            this.gridColumn9.OptionsColumn.AllowShowHide = false;
            this.gridColumn9.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn9.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // gridColumn10
            // 
            this.gridColumn10.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn10.AppearanceCell.FontSizeDelta")));
            this.gridColumn10.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn10.AppearanceCell.FontStyleDelta")));
            this.gridColumn10.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn10.AppearanceCell.GradientMode")));
            this.gridColumn10.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn10.AppearanceCell.Image")));
            this.gridColumn10.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn10.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn10.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn10.AppearanceHeader.FontSizeDelta")));
            this.gridColumn10.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn10.AppearanceHeader.FontStyleDelta")));
            this.gridColumn10.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn10.AppearanceHeader.GradientMode")));
            this.gridColumn10.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn10.AppearanceHeader.Image")));
            this.gridColumn10.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn10.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.gridColumn10, "gridColumn10");
            this.gridColumn10.FieldName = "MatrixId";
            this.gridColumn10.Name = "gridColumn10";
            // 
            // gridColumn11
            // 
            resources.ApplyResources(this.gridColumn11, "gridColumn11");
            this.gridColumn11.FieldName = "MatrixDetailId";
            this.gridColumn11.Name = "gridColumn11";
            // 
            // gridColumn12
            // 
            resources.ApplyResources(this.gridColumn12, "gridColumn12");
            this.gridColumn12.FieldName = "ItmMtrxDetailId";
            this.gridColumn12.Name = "gridColumn12";
            // 
            // colDisabled3
            // 
            resources.ApplyResources(this.colDisabled3, "colDisabled3");
            this.colDisabled3.FieldName = "Disabled";
            this.colDisabled3.Name = "colDisabled3";
            // 
            // grd_Mtrx2
            // 
            resources.ApplyResources(this.grd_Mtrx2, "grd_Mtrx2");
            this.grd_Mtrx2.EmbeddedNavigator.AccessibleDescription = resources.GetString("grd_Mtrx2.EmbeddedNavigator.AccessibleDescription");
            this.grd_Mtrx2.EmbeddedNavigator.AccessibleName = resources.GetString("grd_Mtrx2.EmbeddedNavigator.AccessibleName");
            this.grd_Mtrx2.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grd_Mtrx2.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grd_Mtrx2.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grd_Mtrx2.EmbeddedNavigator.Anchor")));
            this.grd_Mtrx2.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grd_Mtrx2.EmbeddedNavigator.BackgroundImage")));
            this.grd_Mtrx2.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grd_Mtrx2.EmbeddedNavigator.BackgroundImageLayout")));
            this.grd_Mtrx2.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grd_Mtrx2.EmbeddedNavigator.ImeMode")));
            this.grd_Mtrx2.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grd_Mtrx2.EmbeddedNavigator.MaximumSize")));
            this.grd_Mtrx2.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grd_Mtrx2.EmbeddedNavigator.TextLocation")));
            this.grd_Mtrx2.EmbeddedNavigator.ToolTip = resources.GetString("grd_Mtrx2.EmbeddedNavigator.ToolTip");
            this.grd_Mtrx2.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grd_Mtrx2.EmbeddedNavigator.ToolTipIconType")));
            this.grd_Mtrx2.EmbeddedNavigator.ToolTipTitle = resources.GetString("grd_Mtrx2.EmbeddedNavigator.ToolTipTitle");
            this.grd_Mtrx2.MainView = this.gridView1;
            this.grd_Mtrx2.Name = "grd_Mtrx2";
            this.grd_Mtrx2.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemSpinEdit1,
            this.repositoryItemCheckEdit2});
            this.grd_Mtrx2.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // gridView1
            // 
            this.gridView1.Appearance.FooterPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.FooterPanel.FontSizeDelta")));
            this.gridView1.Appearance.FooterPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.FooterPanel.FontStyleDelta")));
            this.gridView1.Appearance.FooterPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.FooterPanel.GradientMode")));
            this.gridView1.Appearance.FooterPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.FooterPanel.Image")));
            this.gridView1.Appearance.FooterPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.gridView1, "gridView1");
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2,
            this.gridColumn3,
            this.gridColumn4,
            this.gridColumn5,
            this.gridColumn6,
            this.colDisabled2});
            this.gridView1.GridControl = this.grd_Mtrx2;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.True;
            this.gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView1.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.gridView1.OptionsSelection.EnableAppearanceHideSelection = false;
            this.gridView1.OptionsView.RowAutoHeight = true;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.ShowingEditor += new System.ComponentModel.CancelEventHandler(this.gridview_ShowingEditor);
            // 
            // gridColumn1
            // 
            resources.ApplyResources(this.gridColumn1, "gridColumn1");
            this.gridColumn1.ColumnEdit = this.repositoryItemCheckEdit2;
            this.gridColumn1.FieldName = "Available";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.OptionsColumn.AllowMove = false;
            this.gridColumn1.OptionsColumn.AllowShowHide = false;
            this.gridColumn1.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn1.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // repositoryItemCheckEdit2
            // 
            resources.ApplyResources(this.repositoryItemCheckEdit2, "repositoryItemCheckEdit2");
            this.repositoryItemCheckEdit2.Name = "repositoryItemCheckEdit2";
            // 
            // gridColumn2
            // 
            this.gridColumn2.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn2.AppearanceCell.FontSizeDelta")));
            this.gridColumn2.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn2.AppearanceCell.FontStyleDelta")));
            this.gridColumn2.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn2.AppearanceCell.GradientMode")));
            this.gridColumn2.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn2.AppearanceCell.Image")));
            this.gridColumn2.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn2.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridColumn2.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn2.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn2.AppearanceHeader.Font = ((System.Drawing.Font)(resources.GetObject("gridColumn2.AppearanceHeader.Font")));
            this.gridColumn2.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn2.AppearanceHeader.FontSizeDelta")));
            this.gridColumn2.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn2.AppearanceHeader.FontStyleDelta")));
            this.gridColumn2.AppearanceHeader.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridColumn2.AppearanceHeader.ForeColor")));
            this.gridColumn2.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn2.AppearanceHeader.GradientMode")));
            this.gridColumn2.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn2.AppearanceHeader.Image")));
            this.gridColumn2.AppearanceHeader.Options.UseFont = true;
            this.gridColumn2.AppearanceHeader.Options.UseForeColor = true;
            this.gridColumn2.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridColumn2.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn2.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn2, "gridColumn2");
            this.gridColumn2.ColumnEdit = this.repositoryItemSpinEdit1;
            this.gridColumn2.FieldName = "MDName";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.OptionsColumn.AllowEdit = false;
            this.gridColumn2.OptionsColumn.AllowFocus = false;
            this.gridColumn2.OptionsColumn.AllowMove = false;
            this.gridColumn2.OptionsColumn.AllowShowHide = false;
            this.gridColumn2.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn2.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // repositoryItemSpinEdit1
            // 
            resources.ApplyResources(this.repositoryItemSpinEdit1, "repositoryItemSpinEdit1");
            this.repositoryItemSpinEdit1.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.repositoryItemSpinEdit1.DisplayFormat.FormatString = "f3";
            this.repositoryItemSpinEdit1.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.repositoryItemSpinEdit1.EditFormat.FormatString = "f3";
            this.repositoryItemSpinEdit1.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.repositoryItemSpinEdit1.Increment = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.repositoryItemSpinEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemSpinEdit1.Mask.AutoComplete")));
            this.repositoryItemSpinEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemSpinEdit1.Mask.BeepOnError")));
            this.repositoryItemSpinEdit1.Mask.EditMask = resources.GetString("repositoryItemSpinEdit1.Mask.EditMask");
            this.repositoryItemSpinEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemSpinEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemSpinEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemSpinEdit1.Mask.MaskType")));
            this.repositoryItemSpinEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemSpinEdit1.Mask.PlaceHolder")));
            this.repositoryItemSpinEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemSpinEdit1.Mask.SaveLiteral")));
            this.repositoryItemSpinEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemSpinEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemSpinEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemSpinEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemSpinEdit1.MaxValue = new decimal(new int[] {
            10000000,
            0,
            0,
            0});
            this.repositoryItemSpinEdit1.Name = "repositoryItemSpinEdit1";
            // 
            // gridColumn3
            // 
            this.gridColumn3.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn3.AppearanceCell.FontSizeDelta")));
            this.gridColumn3.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn3.AppearanceCell.FontStyleDelta")));
            this.gridColumn3.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn3.AppearanceCell.GradientMode")));
            this.gridColumn3.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn3.AppearanceCell.Image")));
            this.gridColumn3.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn3.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridColumn3.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn3.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn3.AppearanceHeader.Font = ((System.Drawing.Font)(resources.GetObject("gridColumn3.AppearanceHeader.Font")));
            this.gridColumn3.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn3.AppearanceHeader.FontSizeDelta")));
            this.gridColumn3.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn3.AppearanceHeader.FontStyleDelta")));
            this.gridColumn3.AppearanceHeader.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridColumn3.AppearanceHeader.ForeColor")));
            this.gridColumn3.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn3.AppearanceHeader.GradientMode")));
            this.gridColumn3.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn3.AppearanceHeader.Image")));
            this.gridColumn3.AppearanceHeader.Options.UseFont = true;
            this.gridColumn3.AppearanceHeader.Options.UseForeColor = true;
            this.gridColumn3.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn3.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridColumn3.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn3.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn3, "gridColumn3");
            this.gridColumn3.FieldName = "MDCode";
            this.gridColumn3.Name = "gridColumn3";
            this.gridColumn3.OptionsColumn.AllowEdit = false;
            this.gridColumn3.OptionsColumn.AllowFocus = false;
            this.gridColumn3.OptionsColumn.AllowMove = false;
            this.gridColumn3.OptionsColumn.AllowShowHide = false;
            this.gridColumn3.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn3.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // gridColumn4
            // 
            this.gridColumn4.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("gridColumn4.AppearanceCell.FontSizeDelta")));
            this.gridColumn4.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn4.AppearanceCell.FontStyleDelta")));
            this.gridColumn4.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn4.AppearanceCell.GradientMode")));
            this.gridColumn4.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn4.AppearanceCell.Image")));
            this.gridColumn4.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn4.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn4.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("gridColumn4.AppearanceHeader.FontSizeDelta")));
            this.gridColumn4.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridColumn4.AppearanceHeader.FontStyleDelta")));
            this.gridColumn4.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridColumn4.AppearanceHeader.GradientMode")));
            this.gridColumn4.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("gridColumn4.AppearanceHeader.Image")));
            this.gridColumn4.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn4.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.gridColumn4, "gridColumn4");
            this.gridColumn4.FieldName = "MatrixId";
            this.gridColumn4.Name = "gridColumn4";
            // 
            // gridColumn5
            // 
            resources.ApplyResources(this.gridColumn5, "gridColumn5");
            this.gridColumn5.FieldName = "MatrixDetailId";
            this.gridColumn5.Name = "gridColumn5";
            // 
            // gridColumn6
            // 
            resources.ApplyResources(this.gridColumn6, "gridColumn6");
            this.gridColumn6.FieldName = "ItmMtrxDetailId";
            this.gridColumn6.Name = "gridColumn6";
            // 
            // colDisabled2
            // 
            resources.ApplyResources(this.colDisabled2, "colDisabled2");
            this.colDisabled2.FieldName = "Disabled";
            this.colDisabled2.Name = "colDisabled2";
            // 
            // grd_Mtrx1
            // 
            resources.ApplyResources(this.grd_Mtrx1, "grd_Mtrx1");
            this.grd_Mtrx1.EmbeddedNavigator.AccessibleDescription = resources.GetString("grd_Mtrx1.EmbeddedNavigator.AccessibleDescription");
            this.grd_Mtrx1.EmbeddedNavigator.AccessibleName = resources.GetString("grd_Mtrx1.EmbeddedNavigator.AccessibleName");
            this.grd_Mtrx1.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grd_Mtrx1.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grd_Mtrx1.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grd_Mtrx1.EmbeddedNavigator.Anchor")));
            this.grd_Mtrx1.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grd_Mtrx1.EmbeddedNavigator.BackgroundImage")));
            this.grd_Mtrx1.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grd_Mtrx1.EmbeddedNavigator.BackgroundImageLayout")));
            this.grd_Mtrx1.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grd_Mtrx1.EmbeddedNavigator.ImeMode")));
            this.grd_Mtrx1.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grd_Mtrx1.EmbeddedNavigator.MaximumSize")));
            this.grd_Mtrx1.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grd_Mtrx1.EmbeddedNavigator.TextLocation")));
            this.grd_Mtrx1.EmbeddedNavigator.ToolTip = resources.GetString("grd_Mtrx1.EmbeddedNavigator.ToolTip");
            this.grd_Mtrx1.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grd_Mtrx1.EmbeddedNavigator.ToolTipIconType")));
            this.grd_Mtrx1.EmbeddedNavigator.ToolTipTitle = resources.GetString("grd_Mtrx1.EmbeddedNavigator.ToolTipTitle");
            this.grd_Mtrx1.MainView = this.gv_SalesPerQty;
            this.grd_Mtrx1.Name = "grd_Mtrx1";
            this.grd_Mtrx1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.rep_SLQtyNums,
            this.repositoryItemCheckEdit1});
            this.grd_Mtrx1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gv_SalesPerQty});
            // 
            // gv_SalesPerQty
            // 
            this.gv_SalesPerQty.Appearance.FooterPanel.FontSizeDelta = ((int)(resources.GetObject("gv_SalesPerQty.Appearance.FooterPanel.FontSizeDelta")));
            this.gv_SalesPerQty.Appearance.FooterPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gv_SalesPerQty.Appearance.FooterPanel.FontStyleDelta")));
            this.gv_SalesPerQty.Appearance.FooterPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gv_SalesPerQty.Appearance.FooterPanel.GradientMode")));
            this.gv_SalesPerQty.Appearance.FooterPanel.Image = ((System.Drawing.Image)(resources.GetObject("gv_SalesPerQty.Appearance.FooterPanel.Image")));
            this.gv_SalesPerQty.Appearance.FooterPanel.Options.UseTextOptions = true;
            this.gv_SalesPerQty.Appearance.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.gv_SalesPerQty, "gv_SalesPerQty");
            this.gv_SalesPerQty.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_available,
            this.col_MDName,
            this.col_MDCode,
            this.col_MatrixId,
            this.col_MatrixDetailId,
            this.col_ItmMtrxDetailId,
            this.colDisabled1});
            this.gv_SalesPerQty.GridControl = this.grd_Mtrx1;
            this.gv_SalesPerQty.Name = "gv_SalesPerQty";
            this.gv_SalesPerQty.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.True;
            this.gv_SalesPerQty.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gv_SalesPerQty.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.gv_SalesPerQty.OptionsSelection.EnableAppearanceHideSelection = false;
            this.gv_SalesPerQty.OptionsView.RowAutoHeight = true;
            this.gv_SalesPerQty.OptionsView.ShowGroupPanel = false;
            this.gv_SalesPerQty.ShowingEditor += new System.ComponentModel.CancelEventHandler(this.gridview_ShowingEditor);
            // 
            // col_available
            // 
            resources.ApplyResources(this.col_available, "col_available");
            this.col_available.ColumnEdit = this.repositoryItemCheckEdit1;
            this.col_available.FieldName = "Available";
            this.col_available.Name = "col_available";
            this.col_available.OptionsColumn.AllowMove = false;
            this.col_available.OptionsColumn.AllowShowHide = false;
            this.col_available.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_available.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // repositoryItemCheckEdit1
            // 
            resources.ApplyResources(this.repositoryItemCheckEdit1, "repositoryItemCheckEdit1");
            this.repositoryItemCheckEdit1.Name = "repositoryItemCheckEdit1";
            // 
            // col_MDName
            // 
            this.col_MDName.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_MDName.AppearanceCell.FontSizeDelta")));
            this.col_MDName.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_MDName.AppearanceCell.FontStyleDelta")));
            this.col_MDName.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_MDName.AppearanceCell.GradientMode")));
            this.col_MDName.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_MDName.AppearanceCell.Image")));
            this.col_MDName.AppearanceCell.Options.UseTextOptions = true;
            this.col_MDName.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_MDName.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_MDName.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_MDName.AppearanceHeader.Font = ((System.Drawing.Font)(resources.GetObject("col_MDName.AppearanceHeader.Font")));
            this.col_MDName.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_MDName.AppearanceHeader.FontSizeDelta")));
            this.col_MDName.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_MDName.AppearanceHeader.FontStyleDelta")));
            this.col_MDName.AppearanceHeader.ForeColor = ((System.Drawing.Color)(resources.GetObject("col_MDName.AppearanceHeader.ForeColor")));
            this.col_MDName.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_MDName.AppearanceHeader.GradientMode")));
            this.col_MDName.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_MDName.AppearanceHeader.Image")));
            this.col_MDName.AppearanceHeader.Options.UseFont = true;
            this.col_MDName.AppearanceHeader.Options.UseForeColor = true;
            this.col_MDName.AppearanceHeader.Options.UseTextOptions = true;
            this.col_MDName.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_MDName.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_MDName.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_MDName, "col_MDName");
            this.col_MDName.ColumnEdit = this.rep_SLQtyNums;
            this.col_MDName.FieldName = "MDName";
            this.col_MDName.Name = "col_MDName";
            this.col_MDName.OptionsColumn.AllowEdit = false;
            this.col_MDName.OptionsColumn.AllowFocus = false;
            this.col_MDName.OptionsColumn.AllowMove = false;
            this.col_MDName.OptionsColumn.AllowShowHide = false;
            this.col_MDName.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_MDName.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // rep_SLQtyNums
            // 
            resources.ApplyResources(this.rep_SLQtyNums, "rep_SLQtyNums");
            this.rep_SLQtyNums.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.rep_SLQtyNums.DisplayFormat.FormatString = "f3";
            this.rep_SLQtyNums.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.rep_SLQtyNums.EditFormat.FormatString = "f3";
            this.rep_SLQtyNums.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.rep_SLQtyNums.Increment = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.rep_SLQtyNums.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("rep_SLQtyNums.Mask.AutoComplete")));
            this.rep_SLQtyNums.Mask.BeepOnError = ((bool)(resources.GetObject("rep_SLQtyNums.Mask.BeepOnError")));
            this.rep_SLQtyNums.Mask.EditMask = resources.GetString("rep_SLQtyNums.Mask.EditMask");
            this.rep_SLQtyNums.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("rep_SLQtyNums.Mask.IgnoreMaskBlank")));
            this.rep_SLQtyNums.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("rep_SLQtyNums.Mask.MaskType")));
            this.rep_SLQtyNums.Mask.PlaceHolder = ((char)(resources.GetObject("rep_SLQtyNums.Mask.PlaceHolder")));
            this.rep_SLQtyNums.Mask.SaveLiteral = ((bool)(resources.GetObject("rep_SLQtyNums.Mask.SaveLiteral")));
            this.rep_SLQtyNums.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("rep_SLQtyNums.Mask.ShowPlaceHolders")));
            this.rep_SLQtyNums.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("rep_SLQtyNums.Mask.UseMaskAsDisplayFormat")));
            this.rep_SLQtyNums.MaxValue = new decimal(new int[] {
            10000000,
            0,
            0,
            0});
            this.rep_SLQtyNums.Name = "rep_SLQtyNums";
            // 
            // col_MDCode
            // 
            this.col_MDCode.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_MDCode.AppearanceCell.FontSizeDelta")));
            this.col_MDCode.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_MDCode.AppearanceCell.FontStyleDelta")));
            this.col_MDCode.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_MDCode.AppearanceCell.GradientMode")));
            this.col_MDCode.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_MDCode.AppearanceCell.Image")));
            this.col_MDCode.AppearanceCell.Options.UseTextOptions = true;
            this.col_MDCode.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_MDCode.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_MDCode.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_MDCode.AppearanceHeader.Font = ((System.Drawing.Font)(resources.GetObject("col_MDCode.AppearanceHeader.Font")));
            this.col_MDCode.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_MDCode.AppearanceHeader.FontSizeDelta")));
            this.col_MDCode.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_MDCode.AppearanceHeader.FontStyleDelta")));
            this.col_MDCode.AppearanceHeader.ForeColor = ((System.Drawing.Color)(resources.GetObject("col_MDCode.AppearanceHeader.ForeColor")));
            this.col_MDCode.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_MDCode.AppearanceHeader.GradientMode")));
            this.col_MDCode.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_MDCode.AppearanceHeader.Image")));
            this.col_MDCode.AppearanceHeader.Options.UseFont = true;
            this.col_MDCode.AppearanceHeader.Options.UseForeColor = true;
            this.col_MDCode.AppearanceHeader.Options.UseTextOptions = true;
            this.col_MDCode.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_MDCode.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_MDCode.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_MDCode, "col_MDCode");
            this.col_MDCode.FieldName = "MDCode";
            this.col_MDCode.Name = "col_MDCode";
            this.col_MDCode.OptionsColumn.AllowEdit = false;
            this.col_MDCode.OptionsColumn.AllowFocus = false;
            this.col_MDCode.OptionsColumn.AllowMove = false;
            this.col_MDCode.OptionsColumn.AllowShowHide = false;
            this.col_MDCode.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_MDCode.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // col_MatrixId
            // 
            this.col_MatrixId.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_MatrixId.AppearanceCell.FontSizeDelta")));
            this.col_MatrixId.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_MatrixId.AppearanceCell.FontStyleDelta")));
            this.col_MatrixId.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_MatrixId.AppearanceCell.GradientMode")));
            this.col_MatrixId.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_MatrixId.AppearanceCell.Image")));
            this.col_MatrixId.AppearanceCell.Options.UseTextOptions = true;
            this.col_MatrixId.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_MatrixId.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_MatrixId.AppearanceHeader.FontSizeDelta")));
            this.col_MatrixId.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_MatrixId.AppearanceHeader.FontStyleDelta")));
            this.col_MatrixId.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_MatrixId.AppearanceHeader.GradientMode")));
            this.col_MatrixId.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_MatrixId.AppearanceHeader.Image")));
            this.col_MatrixId.AppearanceHeader.Options.UseTextOptions = true;
            this.col_MatrixId.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.col_MatrixId, "col_MatrixId");
            this.col_MatrixId.FieldName = "MatrixId";
            this.col_MatrixId.Name = "col_MatrixId";
            // 
            // col_MatrixDetailId
            // 
            resources.ApplyResources(this.col_MatrixDetailId, "col_MatrixDetailId");
            this.col_MatrixDetailId.FieldName = "MatrixDetailId";
            this.col_MatrixDetailId.Name = "col_MatrixDetailId";
            // 
            // col_ItmMtrxDetailId
            // 
            resources.ApplyResources(this.col_ItmMtrxDetailId, "col_ItmMtrxDetailId");
            this.col_ItmMtrxDetailId.FieldName = "ItmMtrxDetailId";
            this.col_ItmMtrxDetailId.Name = "col_ItmMtrxDetailId";
            // 
            // colDisabled1
            // 
            resources.ApplyResources(this.colDisabled1, "colDisabled1");
            this.colDisabled1.FieldName = "Disabled";
            this.colDisabled1.Name = "colDisabled1";
            // 
            // labelControl4
            // 
            resources.ApplyResources(this.labelControl4, "labelControl4");
            this.labelControl4.Name = "labelControl4";
            // 
            // lkpMatrix3
            // 
            resources.ApplyResources(this.lkpMatrix3, "lkpMatrix3");
            this.lkpMatrix3.EnterMoveNextControl = true;
            this.lkpMatrix3.Name = "lkpMatrix3";
            this.lkpMatrix3.Properties.AccessibleDescription = resources.GetString("lkpMatrix3.Properties.AccessibleDescription");
            this.lkpMatrix3.Properties.AccessibleName = resources.GetString("lkpMatrix3.Properties.AccessibleName");
            this.lkpMatrix3.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkpMatrix3.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkpMatrix3.Properties.Appearance.FontSizeDelta")));
            this.lkpMatrix3.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpMatrix3.Properties.Appearance.FontStyleDelta")));
            this.lkpMatrix3.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpMatrix3.Properties.Appearance.GradientMode")));
            this.lkpMatrix3.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkpMatrix3.Properties.Appearance.Image")));
            this.lkpMatrix3.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpMatrix3.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpMatrix3.Properties.AppearanceDropDown.FontSizeDelta = ((int)(resources.GetObject("lkpMatrix3.Properties.AppearanceDropDown.FontSizeDelta")));
            this.lkpMatrix3.Properties.AppearanceDropDown.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpMatrix3.Properties.AppearanceDropDown.FontStyleDelta")));
            this.lkpMatrix3.Properties.AppearanceDropDown.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpMatrix3.Properties.AppearanceDropDown.GradientMode")));
            this.lkpMatrix3.Properties.AppearanceDropDown.Image = ((System.Drawing.Image)(resources.GetObject("lkpMatrix3.Properties.AppearanceDropDown.Image")));
            this.lkpMatrix3.Properties.AppearanceDropDown.Options.UseTextOptions = true;
            this.lkpMatrix3.Properties.AppearanceDropDown.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpMatrix3.Properties.AppearanceDropDownHeader.FontSizeDelta = ((int)(resources.GetObject("lkpMatrix3.Properties.AppearanceDropDownHeader.FontSizeDelta")));
            this.lkpMatrix3.Properties.AppearanceDropDownHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpMatrix3.Properties.AppearanceDropDownHeader.FontStyleDelta")));
            this.lkpMatrix3.Properties.AppearanceDropDownHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpMatrix3.Properties.AppearanceDropDownHeader.GradientMode")));
            this.lkpMatrix3.Properties.AppearanceDropDownHeader.Image = ((System.Drawing.Image)(resources.GetObject("lkpMatrix3.Properties.AppearanceDropDownHeader.Image")));
            this.lkpMatrix3.Properties.AppearanceDropDownHeader.Options.UseTextOptions = true;
            this.lkpMatrix3.Properties.AppearanceDropDownHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkpMatrix3.Properties.AppearanceFocused.FontSizeDelta = ((int)(resources.GetObject("lkpMatrix3.Properties.AppearanceFocused.FontSizeDelta")));
            this.lkpMatrix3.Properties.AppearanceFocused.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpMatrix3.Properties.AppearanceFocused.FontStyleDelta")));
            this.lkpMatrix3.Properties.AppearanceFocused.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpMatrix3.Properties.AppearanceFocused.GradientMode")));
            this.lkpMatrix3.Properties.AppearanceFocused.Image = ((System.Drawing.Image)(resources.GetObject("lkpMatrix3.Properties.AppearanceFocused.Image")));
            this.lkpMatrix3.Properties.AppearanceFocused.Options.UseTextOptions = true;
            this.lkpMatrix3.Properties.AppearanceFocused.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkpMatrix3.Properties.AutoHeight = ((bool)(resources.GetObject("lkpMatrix3.Properties.AutoHeight")));
            this.lkpMatrix3.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkpMatrix3.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpMatrix3.Properties.Buttons"))))});
            this.lkpMatrix3.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpMatrix3.Properties.Columns"), resources.GetString("lkpMatrix3.Properties.Columns1"), ((int)(resources.GetObject("lkpMatrix3.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpMatrix3.Properties.Columns3"))), resources.GetString("lkpMatrix3.Properties.Columns4"), ((bool)(resources.GetObject("lkpMatrix3.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpMatrix3.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpMatrix3.Properties.Columns7"), resources.GetString("lkpMatrix3.Properties.Columns8"), ((int)(resources.GetObject("lkpMatrix3.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpMatrix3.Properties.Columns10"))), resources.GetString("lkpMatrix3.Properties.Columns11"), ((bool)(resources.GetObject("lkpMatrix3.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpMatrix3.Properties.Columns13"))))});
            this.lkpMatrix3.Properties.NullText = resources.GetString("lkpMatrix3.Properties.NullText");
            this.lkpMatrix3.Properties.NullValuePrompt = resources.GetString("lkpMatrix3.Properties.NullValuePrompt");
            this.lkpMatrix3.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkpMatrix3.Properties.NullValuePromptShowForEmptyValue")));
            this.lkpMatrix3.EditValueChanged += new System.EventHandler(this.lkpMatrix1_EditValueChanged);
            // 
            // labelControl3
            // 
            resources.ApplyResources(this.labelControl3, "labelControl3");
            this.labelControl3.Name = "labelControl3";
            // 
            // lkpMatrix2
            // 
            resources.ApplyResources(this.lkpMatrix2, "lkpMatrix2");
            this.lkpMatrix2.EnterMoveNextControl = true;
            this.lkpMatrix2.Name = "lkpMatrix2";
            this.lkpMatrix2.Properties.AccessibleDescription = resources.GetString("lkpMatrix2.Properties.AccessibleDescription");
            this.lkpMatrix2.Properties.AccessibleName = resources.GetString("lkpMatrix2.Properties.AccessibleName");
            this.lkpMatrix2.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkpMatrix2.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkpMatrix2.Properties.Appearance.FontSizeDelta")));
            this.lkpMatrix2.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpMatrix2.Properties.Appearance.FontStyleDelta")));
            this.lkpMatrix2.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpMatrix2.Properties.Appearance.GradientMode")));
            this.lkpMatrix2.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkpMatrix2.Properties.Appearance.Image")));
            this.lkpMatrix2.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpMatrix2.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpMatrix2.Properties.AppearanceDropDown.FontSizeDelta = ((int)(resources.GetObject("lkpMatrix2.Properties.AppearanceDropDown.FontSizeDelta")));
            this.lkpMatrix2.Properties.AppearanceDropDown.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpMatrix2.Properties.AppearanceDropDown.FontStyleDelta")));
            this.lkpMatrix2.Properties.AppearanceDropDown.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpMatrix2.Properties.AppearanceDropDown.GradientMode")));
            this.lkpMatrix2.Properties.AppearanceDropDown.Image = ((System.Drawing.Image)(resources.GetObject("lkpMatrix2.Properties.AppearanceDropDown.Image")));
            this.lkpMatrix2.Properties.AppearanceDropDown.Options.UseTextOptions = true;
            this.lkpMatrix2.Properties.AppearanceDropDown.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpMatrix2.Properties.AppearanceDropDownHeader.FontSizeDelta = ((int)(resources.GetObject("lkpMatrix2.Properties.AppearanceDropDownHeader.FontSizeDelta")));
            this.lkpMatrix2.Properties.AppearanceDropDownHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpMatrix2.Properties.AppearanceDropDownHeader.FontStyleDelta")));
            this.lkpMatrix2.Properties.AppearanceDropDownHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpMatrix2.Properties.AppearanceDropDownHeader.GradientMode")));
            this.lkpMatrix2.Properties.AppearanceDropDownHeader.Image = ((System.Drawing.Image)(resources.GetObject("lkpMatrix2.Properties.AppearanceDropDownHeader.Image")));
            this.lkpMatrix2.Properties.AppearanceDropDownHeader.Options.UseTextOptions = true;
            this.lkpMatrix2.Properties.AppearanceDropDownHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkpMatrix2.Properties.AppearanceFocused.FontSizeDelta = ((int)(resources.GetObject("lkpMatrix2.Properties.AppearanceFocused.FontSizeDelta")));
            this.lkpMatrix2.Properties.AppearanceFocused.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpMatrix2.Properties.AppearanceFocused.FontStyleDelta")));
            this.lkpMatrix2.Properties.AppearanceFocused.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpMatrix2.Properties.AppearanceFocused.GradientMode")));
            this.lkpMatrix2.Properties.AppearanceFocused.Image = ((System.Drawing.Image)(resources.GetObject("lkpMatrix2.Properties.AppearanceFocused.Image")));
            this.lkpMatrix2.Properties.AppearanceFocused.Options.UseTextOptions = true;
            this.lkpMatrix2.Properties.AppearanceFocused.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkpMatrix2.Properties.AutoHeight = ((bool)(resources.GetObject("lkpMatrix2.Properties.AutoHeight")));
            this.lkpMatrix2.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkpMatrix2.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpMatrix2.Properties.Buttons"))))});
            this.lkpMatrix2.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpMatrix2.Properties.Columns"), resources.GetString("lkpMatrix2.Properties.Columns1"), ((int)(resources.GetObject("lkpMatrix2.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpMatrix2.Properties.Columns3"))), resources.GetString("lkpMatrix2.Properties.Columns4"), ((bool)(resources.GetObject("lkpMatrix2.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpMatrix2.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpMatrix2.Properties.Columns7"), resources.GetString("lkpMatrix2.Properties.Columns8"), ((int)(resources.GetObject("lkpMatrix2.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpMatrix2.Properties.Columns10"))), resources.GetString("lkpMatrix2.Properties.Columns11"), ((bool)(resources.GetObject("lkpMatrix2.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpMatrix2.Properties.Columns13"))))});
            this.lkpMatrix2.Properties.NullText = resources.GetString("lkpMatrix2.Properties.NullText");
            this.lkpMatrix2.Properties.NullValuePrompt = resources.GetString("lkpMatrix2.Properties.NullValuePrompt");
            this.lkpMatrix2.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkpMatrix2.Properties.NullValuePromptShowForEmptyValue")));
            this.lkpMatrix2.EditValueChanged += new System.EventHandler(this.lkpMatrix1_EditValueChanged);
            // 
            // labelControl2
            // 
            resources.ApplyResources(this.labelControl2, "labelControl2");
            this.labelControl2.Name = "labelControl2";
            // 
            // lkpMatrix1
            // 
            resources.ApplyResources(this.lkpMatrix1, "lkpMatrix1");
            this.lkpMatrix1.EnterMoveNextControl = true;
            this.lkpMatrix1.Name = "lkpMatrix1";
            this.lkpMatrix1.Properties.AccessibleDescription = resources.GetString("lkpMatrix1.Properties.AccessibleDescription");
            this.lkpMatrix1.Properties.AccessibleName = resources.GetString("lkpMatrix1.Properties.AccessibleName");
            this.lkpMatrix1.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkpMatrix1.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkpMatrix1.Properties.Appearance.FontSizeDelta")));
            this.lkpMatrix1.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpMatrix1.Properties.Appearance.FontStyleDelta")));
            this.lkpMatrix1.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpMatrix1.Properties.Appearance.GradientMode")));
            this.lkpMatrix1.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkpMatrix1.Properties.Appearance.Image")));
            this.lkpMatrix1.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpMatrix1.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpMatrix1.Properties.AppearanceDropDown.FontSizeDelta = ((int)(resources.GetObject("lkpMatrix1.Properties.AppearanceDropDown.FontSizeDelta")));
            this.lkpMatrix1.Properties.AppearanceDropDown.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpMatrix1.Properties.AppearanceDropDown.FontStyleDelta")));
            this.lkpMatrix1.Properties.AppearanceDropDown.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpMatrix1.Properties.AppearanceDropDown.GradientMode")));
            this.lkpMatrix1.Properties.AppearanceDropDown.Image = ((System.Drawing.Image)(resources.GetObject("lkpMatrix1.Properties.AppearanceDropDown.Image")));
            this.lkpMatrix1.Properties.AppearanceDropDown.Options.UseTextOptions = true;
            this.lkpMatrix1.Properties.AppearanceDropDown.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpMatrix1.Properties.AppearanceDropDownHeader.FontSizeDelta = ((int)(resources.GetObject("lkpMatrix1.Properties.AppearanceDropDownHeader.FontSizeDelta")));
            this.lkpMatrix1.Properties.AppearanceDropDownHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpMatrix1.Properties.AppearanceDropDownHeader.FontStyleDelta")));
            this.lkpMatrix1.Properties.AppearanceDropDownHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpMatrix1.Properties.AppearanceDropDownHeader.GradientMode")));
            this.lkpMatrix1.Properties.AppearanceDropDownHeader.Image = ((System.Drawing.Image)(resources.GetObject("lkpMatrix1.Properties.AppearanceDropDownHeader.Image")));
            this.lkpMatrix1.Properties.AppearanceDropDownHeader.Options.UseTextOptions = true;
            this.lkpMatrix1.Properties.AppearanceDropDownHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkpMatrix1.Properties.AppearanceFocused.FontSizeDelta = ((int)(resources.GetObject("lkpMatrix1.Properties.AppearanceFocused.FontSizeDelta")));
            this.lkpMatrix1.Properties.AppearanceFocused.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpMatrix1.Properties.AppearanceFocused.FontStyleDelta")));
            this.lkpMatrix1.Properties.AppearanceFocused.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpMatrix1.Properties.AppearanceFocused.GradientMode")));
            this.lkpMatrix1.Properties.AppearanceFocused.Image = ((System.Drawing.Image)(resources.GetObject("lkpMatrix1.Properties.AppearanceFocused.Image")));
            this.lkpMatrix1.Properties.AppearanceFocused.Options.UseTextOptions = true;
            this.lkpMatrix1.Properties.AppearanceFocused.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkpMatrix1.Properties.AutoHeight = ((bool)(resources.GetObject("lkpMatrix1.Properties.AutoHeight")));
            this.lkpMatrix1.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkpMatrix1.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpMatrix1.Properties.Buttons"))))});
            this.lkpMatrix1.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpMatrix1.Properties.Columns"), resources.GetString("lkpMatrix1.Properties.Columns1"), ((int)(resources.GetObject("lkpMatrix1.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpMatrix1.Properties.Columns3"))), resources.GetString("lkpMatrix1.Properties.Columns4"), ((bool)(resources.GetObject("lkpMatrix1.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpMatrix1.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpMatrix1.Properties.Columns7"), resources.GetString("lkpMatrix1.Properties.Columns8"), ((int)(resources.GetObject("lkpMatrix1.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpMatrix1.Properties.Columns10"))), resources.GetString("lkpMatrix1.Properties.Columns11"), ((bool)(resources.GetObject("lkpMatrix1.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpMatrix1.Properties.Columns13"))))});
            this.lkpMatrix1.Properties.NullText = resources.GetString("lkpMatrix1.Properties.NullText");
            this.lkpMatrix1.Properties.NullValuePrompt = resources.GetString("lkpMatrix1.Properties.NullValuePrompt");
            this.lkpMatrix1.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkpMatrix1.Properties.NullValuePromptShowForEmptyValue")));
            this.lkpMatrix1.EditValueChanged += new System.EventHandler(this.lkpMatrix1_EditValueChanged);
            // 
            // txtParentItemName
            // 
            resources.ApplyResources(this.txtParentItemName, "txtParentItemName");
            this.txtParentItemName.EnterMoveNextControl = true;
            this.txtParentItemName.Name = "txtParentItemName";
            this.txtParentItemName.Properties.AccessibleDescription = resources.GetString("txtParentItemName.Properties.AccessibleDescription");
            this.txtParentItemName.Properties.AccessibleName = resources.GetString("txtParentItemName.Properties.AccessibleName");
            this.txtParentItemName.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("txtParentItemName.Properties.Appearance.FontSizeDelta")));
            this.txtParentItemName.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("txtParentItemName.Properties.Appearance.FontStyleDelta")));
            this.txtParentItemName.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("txtParentItemName.Properties.Appearance.GradientMode")));
            this.txtParentItemName.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("txtParentItemName.Properties.Appearance.Image")));
            this.txtParentItemName.Properties.Appearance.Options.UseTextOptions = true;
            this.txtParentItemName.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtParentItemName.Properties.AutoHeight = ((bool)(resources.GetObject("txtParentItemName.Properties.AutoHeight")));
            this.txtParentItemName.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txtParentItemName.Properties.Mask.AutoComplete")));
            this.txtParentItemName.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txtParentItemName.Properties.Mask.BeepOnError")));
            this.txtParentItemName.Properties.Mask.EditMask = resources.GetString("txtParentItemName.Properties.Mask.EditMask");
            this.txtParentItemName.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtParentItemName.Properties.Mask.IgnoreMaskBlank")));
            this.txtParentItemName.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtParentItemName.Properties.Mask.MaskType")));
            this.txtParentItemName.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txtParentItemName.Properties.Mask.PlaceHolder")));
            this.txtParentItemName.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtParentItemName.Properties.Mask.SaveLiteral")));
            this.txtParentItemName.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtParentItemName.Properties.Mask.ShowPlaceHolders")));
            this.txtParentItemName.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txtParentItemName.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txtParentItemName.Properties.NullValuePrompt = resources.GetString("txtParentItemName.Properties.NullValuePrompt");
            this.txtParentItemName.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtParentItemName.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // tab_GeneratedItems
            // 
            resources.ApplyResources(this.tab_GeneratedItems, "tab_GeneratedItems");
            this.tab_GeneratedItems.Controls.Add(this.btn_Save);
            this.tab_GeneratedItems.Controls.Add(this.btn_GenerateItems);
            this.tab_GeneratedItems.Controls.Add(this.grd_genItems);
            this.tab_GeneratedItems.Name = "tab_GeneratedItems";
            // 
            // btn_Save
            // 
            resources.ApplyResources(this.btn_Save, "btn_Save");
            this.btn_Save.Name = "btn_Save";
            this.btn_Save.Click += new System.EventHandler(this.btn_Save_Click);
            // 
            // btn_GenerateItems
            // 
            resources.ApplyResources(this.btn_GenerateItems, "btn_GenerateItems");
            this.btn_GenerateItems.Name = "btn_GenerateItems";
            this.btn_GenerateItems.Click += new System.EventHandler(this.btn_GenerateItems_Click);
            // 
            // grd_genItems
            // 
            resources.ApplyResources(this.grd_genItems, "grd_genItems");
            this.grd_genItems.EmbeddedNavigator.AccessibleDescription = resources.GetString("grd_genItems.EmbeddedNavigator.AccessibleDescription");
            this.grd_genItems.EmbeddedNavigator.AccessibleName = resources.GetString("grd_genItems.EmbeddedNavigator.AccessibleName");
            this.grd_genItems.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grd_genItems.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grd_genItems.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grd_genItems.EmbeddedNavigator.Anchor")));
            this.grd_genItems.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grd_genItems.EmbeddedNavigator.BackgroundImage")));
            this.grd_genItems.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grd_genItems.EmbeddedNavigator.BackgroundImageLayout")));
            this.grd_genItems.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grd_genItems.EmbeddedNavigator.ImeMode")));
            this.grd_genItems.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grd_genItems.EmbeddedNavigator.MaximumSize")));
            this.grd_genItems.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grd_genItems.EmbeddedNavigator.TextLocation")));
            this.grd_genItems.EmbeddedNavigator.ToolTip = resources.GetString("grd_genItems.EmbeddedNavigator.ToolTip");
            this.grd_genItems.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grd_genItems.EmbeddedNavigator.ToolTipIconType")));
            this.grd_genItems.EmbeddedNavigator.ToolTipTitle = resources.GetString("grd_genItems.EmbeddedNavigator.ToolTipTitle");
            this.grd_genItems.MainView = this.gridView3;
            this.grd_genItems.Name = "grd_genItems";
            this.grd_genItems.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemSpinEdit3,
            this.repositoryItemCheckEdit4});
            this.grd_genItems.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView3});
            // 
            // gridView3
            // 
            this.gridView3.Appearance.FooterPanel.FontSizeDelta = ((int)(resources.GetObject("gridView3.Appearance.FooterPanel.FontSizeDelta")));
            this.gridView3.Appearance.FooterPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView3.Appearance.FooterPanel.FontStyleDelta")));
            this.gridView3.Appearance.FooterPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView3.Appearance.FooterPanel.GradientMode")));
            this.gridView3.Appearance.FooterPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView3.Appearance.FooterPanel.Image")));
            this.gridView3.Appearance.FooterPanel.Options.UseTextOptions = true;
            this.gridView3.Appearance.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView3.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gridView3.Appearance.Row.FontSizeDelta")));
            this.gridView3.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView3.Appearance.Row.FontStyleDelta")));
            this.gridView3.Appearance.Row.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView3.Appearance.Row.ForeColor")));
            this.gridView3.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView3.Appearance.Row.GradientMode")));
            this.gridView3.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView3.Appearance.Row.Image")));
            this.gridView3.Appearance.Row.Options.UseForeColor = true;
            resources.ApplyResources(this.gridView3, "gridView3");
            this.gridView3.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_GenMatrix3,
            this.col_GenMatrix2,
            this.col_GenMatrix1,
            this.col_GenItemName,
            this.col_GenItemCode,
            this.col_Serial});
            this.gridView3.GridControl = this.grd_genItems;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.True;
            this.gridView3.OptionsBehavior.Editable = false;
            this.gridView3.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView3.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.gridView3.OptionsSelection.EnableAppearanceHideSelection = false;
            this.gridView3.OptionsView.RowAutoHeight = true;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            this.gridView3.CustomUnboundColumnData += new DevExpress.XtraGrid.Views.Base.CustomColumnDataEventHandler(this.gridView3_CustomUnboundColumnData);
            // 
            // col_GenMatrix3
            // 
            this.col_GenMatrix3.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_GenMatrix3.AppearanceCell.FontSizeDelta")));
            this.col_GenMatrix3.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_GenMatrix3.AppearanceCell.FontStyleDelta")));
            this.col_GenMatrix3.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_GenMatrix3.AppearanceCell.GradientMode")));
            this.col_GenMatrix3.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_GenMatrix3.AppearanceCell.Image")));
            this.col_GenMatrix3.AppearanceCell.Options.UseTextOptions = true;
            this.col_GenMatrix3.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_GenMatrix3.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_GenMatrix3.AppearanceHeader.FontSizeDelta")));
            this.col_GenMatrix3.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_GenMatrix3.AppearanceHeader.FontStyleDelta")));
            this.col_GenMatrix3.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_GenMatrix3.AppearanceHeader.GradientMode")));
            this.col_GenMatrix3.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_GenMatrix3.AppearanceHeader.Image")));
            this.col_GenMatrix3.AppearanceHeader.Options.UseTextOptions = true;
            this.col_GenMatrix3.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.col_GenMatrix3, "col_GenMatrix3");
            this.col_GenMatrix3.FieldName = "Mtrix3";
            this.col_GenMatrix3.Name = "col_GenMatrix3";
            // 
            // col_GenMatrix2
            // 
            this.col_GenMatrix2.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_GenMatrix2.AppearanceCell.FontSizeDelta")));
            this.col_GenMatrix2.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_GenMatrix2.AppearanceCell.FontStyleDelta")));
            this.col_GenMatrix2.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_GenMatrix2.AppearanceCell.GradientMode")));
            this.col_GenMatrix2.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_GenMatrix2.AppearanceCell.Image")));
            this.col_GenMatrix2.AppearanceCell.Options.UseTextOptions = true;
            this.col_GenMatrix2.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_GenMatrix2.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_GenMatrix2.AppearanceHeader.FontSizeDelta")));
            this.col_GenMatrix2.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_GenMatrix2.AppearanceHeader.FontStyleDelta")));
            this.col_GenMatrix2.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_GenMatrix2.AppearanceHeader.GradientMode")));
            this.col_GenMatrix2.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_GenMatrix2.AppearanceHeader.Image")));
            this.col_GenMatrix2.AppearanceHeader.Options.UseTextOptions = true;
            this.col_GenMatrix2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.col_GenMatrix2, "col_GenMatrix2");
            this.col_GenMatrix2.FieldName = "Mtrix2";
            this.col_GenMatrix2.Name = "col_GenMatrix2";
            // 
            // col_GenMatrix1
            // 
            this.col_GenMatrix1.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_GenMatrix1.AppearanceCell.FontSizeDelta")));
            this.col_GenMatrix1.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_GenMatrix1.AppearanceCell.FontStyleDelta")));
            this.col_GenMatrix1.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_GenMatrix1.AppearanceCell.GradientMode")));
            this.col_GenMatrix1.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_GenMatrix1.AppearanceCell.Image")));
            this.col_GenMatrix1.AppearanceCell.Options.UseTextOptions = true;
            this.col_GenMatrix1.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_GenMatrix1.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_GenMatrix1.AppearanceHeader.FontSizeDelta")));
            this.col_GenMatrix1.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_GenMatrix1.AppearanceHeader.FontStyleDelta")));
            this.col_GenMatrix1.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_GenMatrix1.AppearanceHeader.GradientMode")));
            this.col_GenMatrix1.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_GenMatrix1.AppearanceHeader.Image")));
            this.col_GenMatrix1.AppearanceHeader.Options.UseTextOptions = true;
            this.col_GenMatrix1.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.col_GenMatrix1, "col_GenMatrix1");
            this.col_GenMatrix1.FieldName = "Mtrix1";
            this.col_GenMatrix1.Name = "col_GenMatrix1";
            // 
            // col_GenItemName
            // 
            this.col_GenItemName.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_GenItemName.AppearanceCell.FontSizeDelta")));
            this.col_GenItemName.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_GenItemName.AppearanceCell.FontStyleDelta")));
            this.col_GenItemName.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_GenItemName.AppearanceCell.GradientMode")));
            this.col_GenItemName.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_GenItemName.AppearanceCell.Image")));
            this.col_GenItemName.AppearanceCell.Options.UseTextOptions = true;
            this.col_GenItemName.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_GenItemName.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_GenItemName.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_GenItemName.AppearanceHeader.Font = ((System.Drawing.Font)(resources.GetObject("col_GenItemName.AppearanceHeader.Font")));
            this.col_GenItemName.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_GenItemName.AppearanceHeader.FontSizeDelta")));
            this.col_GenItemName.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_GenItemName.AppearanceHeader.FontStyleDelta")));
            this.col_GenItemName.AppearanceHeader.ForeColor = ((System.Drawing.Color)(resources.GetObject("col_GenItemName.AppearanceHeader.ForeColor")));
            this.col_GenItemName.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_GenItemName.AppearanceHeader.GradientMode")));
            this.col_GenItemName.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_GenItemName.AppearanceHeader.Image")));
            this.col_GenItemName.AppearanceHeader.Options.UseFont = true;
            this.col_GenItemName.AppearanceHeader.Options.UseForeColor = true;
            this.col_GenItemName.AppearanceHeader.Options.UseTextOptions = true;
            this.col_GenItemName.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_GenItemName.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_GenItemName.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_GenItemName, "col_GenItemName");
            this.col_GenItemName.FieldName = "ItemName";
            this.col_GenItemName.Name = "col_GenItemName";
            // 
            // col_GenItemCode
            // 
            this.col_GenItemCode.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_GenItemCode.AppearanceCell.FontSizeDelta")));
            this.col_GenItemCode.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_GenItemCode.AppearanceCell.FontStyleDelta")));
            this.col_GenItemCode.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_GenItemCode.AppearanceCell.GradientMode")));
            this.col_GenItemCode.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_GenItemCode.AppearanceCell.Image")));
            this.col_GenItemCode.AppearanceCell.Options.UseTextOptions = true;
            this.col_GenItemCode.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_GenItemCode.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_GenItemCode.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_GenItemCode.AppearanceHeader.Font = ((System.Drawing.Font)(resources.GetObject("col_GenItemCode.AppearanceHeader.Font")));
            this.col_GenItemCode.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_GenItemCode.AppearanceHeader.FontSizeDelta")));
            this.col_GenItemCode.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_GenItemCode.AppearanceHeader.FontStyleDelta")));
            this.col_GenItemCode.AppearanceHeader.ForeColor = ((System.Drawing.Color)(resources.GetObject("col_GenItemCode.AppearanceHeader.ForeColor")));
            this.col_GenItemCode.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_GenItemCode.AppearanceHeader.GradientMode")));
            this.col_GenItemCode.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_GenItemCode.AppearanceHeader.Image")));
            this.col_GenItemCode.AppearanceHeader.Options.UseFont = true;
            this.col_GenItemCode.AppearanceHeader.Options.UseForeColor = true;
            this.col_GenItemCode.AppearanceHeader.Options.UseTextOptions = true;
            this.col_GenItemCode.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.col_GenItemCode.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_GenItemCode.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_GenItemCode, "col_GenItemCode");
            this.col_GenItemCode.FieldName = "ItemCode";
            this.col_GenItemCode.Name = "col_GenItemCode";
            // 
            // col_Serial
            // 
            this.col_Serial.AppearanceCell.FontSizeDelta = ((int)(resources.GetObject("col_Serial.AppearanceCell.FontSizeDelta")));
            this.col_Serial.AppearanceCell.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_Serial.AppearanceCell.FontStyleDelta")));
            this.col_Serial.AppearanceCell.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_Serial.AppearanceCell.GradientMode")));
            this.col_Serial.AppearanceCell.Image = ((System.Drawing.Image)(resources.GetObject("col_Serial.AppearanceCell.Image")));
            this.col_Serial.AppearanceCell.Options.UseTextOptions = true;
            this.col_Serial.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Serial.AppearanceHeader.FontSizeDelta = ((int)(resources.GetObject("col_Serial.AppearanceHeader.FontSizeDelta")));
            this.col_Serial.AppearanceHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("col_Serial.AppearanceHeader.FontStyleDelta")));
            this.col_Serial.AppearanceHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("col_Serial.AppearanceHeader.GradientMode")));
            this.col_Serial.AppearanceHeader.Image = ((System.Drawing.Image)(resources.GetObject("col_Serial.AppearanceHeader.Image")));
            this.col_Serial.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Serial.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.col_Serial, "col_Serial");
            this.col_Serial.FieldName = "Serial";
            this.col_Serial.Name = "col_Serial";
            this.col_Serial.UnboundType = DevExpress.Data.UnboundColumnType.Integer;
            // 
            // repositoryItemSpinEdit3
            // 
            resources.ApplyResources(this.repositoryItemSpinEdit3, "repositoryItemSpinEdit3");
            this.repositoryItemSpinEdit3.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.repositoryItemSpinEdit3.DisplayFormat.FormatString = "f3";
            this.repositoryItemSpinEdit3.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.repositoryItemSpinEdit3.EditFormat.FormatString = "f3";
            this.repositoryItemSpinEdit3.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.repositoryItemSpinEdit3.Increment = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.repositoryItemSpinEdit3.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemSpinEdit3.Mask.AutoComplete")));
            this.repositoryItemSpinEdit3.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemSpinEdit3.Mask.BeepOnError")));
            this.repositoryItemSpinEdit3.Mask.EditMask = resources.GetString("repositoryItemSpinEdit3.Mask.EditMask");
            this.repositoryItemSpinEdit3.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemSpinEdit3.Mask.IgnoreMaskBlank")));
            this.repositoryItemSpinEdit3.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemSpinEdit3.Mask.MaskType")));
            this.repositoryItemSpinEdit3.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemSpinEdit3.Mask.PlaceHolder")));
            this.repositoryItemSpinEdit3.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemSpinEdit3.Mask.SaveLiteral")));
            this.repositoryItemSpinEdit3.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemSpinEdit3.Mask.ShowPlaceHolders")));
            this.repositoryItemSpinEdit3.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemSpinEdit3.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemSpinEdit3.MaxValue = new decimal(new int[] {
            10000000,
            0,
            0,
            0});
            this.repositoryItemSpinEdit3.Name = "repositoryItemSpinEdit3";
            // 
            // repositoryItemCheckEdit4
            // 
            resources.ApplyResources(this.repositoryItemCheckEdit4, "repositoryItemCheckEdit4");
            this.repositoryItemCheckEdit4.Name = "repositoryItemCheckEdit4";
            // 
            // frm_IC_ItemMatrixCreate
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.xtraTabControl1);
            this.Name = "frm_IC_ItemMatrixCreate";
            this.Load += new System.EventHandler(this.frm_IC_ItemMatrixCreate_Load);
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.tab_DefineMatrix.ResumeLayout(false);
            this.tab_DefineMatrix.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtMtrxSprtr3.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMtrx3.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMtrxSprtr2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMtrx2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMtrxSprtr1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMtrx1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMtrxSprtr0.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtMtrxCode0.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grd_Mtrx3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSpinEdit2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grd_Mtrx2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSpinEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grd_Mtrx1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv_SalesPerQty)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_SLQtyNums)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpMatrix3.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpMatrix2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpMatrix1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtParentItemName.Properties)).EndInit();
            this.tab_GeneratedItems.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grd_genItems)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSpinEdit3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemCheckEdit4)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage tab_DefineMatrix;
        private DevExpress.XtraTab.XtraTabPage tab_GeneratedItems;
        private DevExpress.XtraEditors.TextEdit txtParentItemName;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LookUpEdit lkpMatrix3;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.LookUpEdit lkpMatrix2;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.LookUpEdit lkpMatrix1;
        private DevExpress.XtraGrid.GridControl grd_Mtrx1;
        private DevExpress.XtraGrid.Views.Grid.GridView gv_SalesPerQty;
        private DevExpress.XtraGrid.Columns.GridColumn col_MDName;
        private DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit rep_SLQtyNums;
        private DevExpress.XtraGrid.Columns.GridColumn col_MDCode;
        private DevExpress.XtraGrid.Columns.GridColumn col_MatrixId;
        private DevExpress.XtraGrid.Columns.GridColumn col_MatrixDetailId;
        private DevExpress.XtraGrid.Columns.GridColumn col_ItmMtrxDetailId;
        private DevExpress.XtraGrid.Columns.GridColumn col_available;
        private DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit repositoryItemCheckEdit1;
        private DevExpress.XtraGrid.GridControl grd_Mtrx3;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit repositoryItemCheckEdit3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit repositoryItemSpinEdit2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.GridControl grd_Mtrx2;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit repositoryItemCheckEdit2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit repositoryItemSpinEdit1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private System.Windows.Forms.GroupBox groupBox1;
        private DevExpress.XtraEditors.TextEdit txtMtrxSprtr3;
        private DevExpress.XtraEditors.TextEdit txtMtrx3;
        private DevExpress.XtraEditors.TextEdit txtMtrxSprtr2;
        private DevExpress.XtraEditors.TextEdit txtMtrx2;
        private DevExpress.XtraEditors.TextEdit txtMtrxSprtr1;
        private DevExpress.XtraEditors.TextEdit txtMtrx1;
        private DevExpress.XtraEditors.TextEdit txtMtrxSprtr0;
        private DevExpress.XtraEditors.TextEdit txtMtrxCode0;
        private DevExpress.XtraGrid.GridControl grd_genItems;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn col_GenMatrix3;
        private DevExpress.XtraGrid.Columns.GridColumn col_GenMatrix2;
        private DevExpress.XtraGrid.Columns.GridColumn col_GenMatrix1;
        private DevExpress.XtraGrid.Columns.GridColumn col_GenItemName;
        private DevExpress.XtraGrid.Columns.GridColumn col_GenItemCode;
        private DevExpress.XtraGrid.Columns.GridColumn col_Serial;
        private DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit repositoryItemSpinEdit3;
        private DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit repositoryItemCheckEdit4;
        private DevExpress.XtraEditors.SimpleButton btn_Save;
        private DevExpress.XtraEditors.SimpleButton btn_GenerateItems;
        private DevExpress.XtraGrid.Columns.GridColumn colDisabled3;
        private DevExpress.XtraGrid.Columns.GridColumn colDisabled2;
        private DevExpress.XtraGrid.Columns.GridColumn colDisabled1;
    }
}