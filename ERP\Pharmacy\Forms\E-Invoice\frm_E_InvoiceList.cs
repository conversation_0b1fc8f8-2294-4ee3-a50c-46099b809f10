using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;
using DevExpress.XtraReports.UI;

using System.IO;
using DevExpress.XtraWaitForm;

namespace Pharmacy.Forms
{
    public partial class frm_E_InvoiceList : DevExpress.XtraEditors.XtraForm
    {
        int customerId;
        DateTime dateFrom, dateTo;

        bool Is_OpenForSelect = false;
        public static int SelectedInvId = 0;
        public static string SelectedInvCode;
        bool cars = false;

        public frm_E_InvoiceList()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
        }
        public frm_E_InvoiceList(bool _cars, bool IsCars)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
            cars = _cars;

        }

        public frm_E_InvoiceList(bool _is_OpenForSelect)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            Is_OpenForSelect = _is_OpenForSelect;
        }
        public frm_E_InvoiceList(bool _is_OpenForSelect, int customerId)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            Is_OpenForSelect = _is_OpenForSelect;
            this.customerId = customerId;
        }

        public frm_E_InvoiceList(int customerId)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            this.customerId = customerId;
        }


        private void frm_E_InvoiceList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            #region Get Date Range
            if (Shared.SL_I_FromDate != null)
                dt1.DateTime = dateFrom = Shared.SL_I_FromDate.Value;
            else
            {
                dateFrom = Shared.minDate;
                dt1.EditValue = null;
            }

            if (Shared.SL_I_ToDate != null)
                dt2.DateTime = dateTo = Shared.SL_I_ToDate.Value;
            else
            {
                dateTo = Shared.maxDate;
                dt2.EditValue = null;
            }
            #endregion


            #region CustomersGroups
            ERPDataContext DB = new ERPDataContext();
            rep_CategoryId.DataSource = DB.SL_CustomerGroups.Select(x => new { x.CustomerGroupId, x.CGNameAr }).ToList();
            rep_CategoryId.DisplayMember = "CGNameAr";
            rep_CategoryId.ValueMember = "CustomerGroupId";
            #endregion


            GetInvoices();
            /*
            #region SalesEmp
            DataTable dt_SalesEmps = new DataTable();
            MyHelper.GetSalesEmps(dt_SalesEmps, false, true, Shared.user.DefaultSalesRep);
            rep_salesEmp.DataSource = dt_SalesEmps;
            rep_salesEmp.DisplayMember = "EmpName";
            rep_salesEmp.ValueMember = "EmpId";
            #endregion
            
            

            #region invoice bbok            
            rep_InvoiceBook.DataSource = DB.ST_InvoiceBooks.Where(x => x.ProcessId == (int)Process.SellInvoice)
                .Select(x => new { x.InvoiceBookId, x.InvoiceBookName }).ToList();
            rep_InvoiceBook.DisplayMember = "InvoiceBookName";
            rep_InvoiceBook.ValueMember = "InvoiceBookId";
            #endregion
            */
            #region Currencies
            repCrncy.DataSource = Shared.lstCurrency;
            repCrncy.ValueMember = "CrncId";
            repCrncy.DisplayMember = "crncName";
            #endregion

            rep_User.DataSource = DB.HR_Users;
            //rep_Accounts.DataSource = DB.ACC_Accounts;
            rep_Accounts.ValueMember = "AccountId";
            rep_Accounts.DisplayMember = Shared.IsEnglish ? "AcNameEn" : "AcNameAr";

            if (Shared.InvoicePostToStore)
                col_Is_OutTrans.Visible = false;

            ErpUtils.Load_Grid_Layout(grdCategory, this.Name.Replace("frm_", ""));
            ErpUtils.ColumnChooser(grdCategory);

            if (Shared.user.HidePurchasePrice)
            {
                col_Profit.Visible = col_Profit.OptionsColumn.ShowInCustomizationForm =
                    col_TotalCostPrice.Visible = col_TotalCostPrice.OptionsColumn.ShowInCustomizationForm =
                    col_ProfitRatio.Visible = col_ProfitRatio.OptionsColumn.ShowInCustomizationForm = false;
            }
           
            ErpUtils.ColumnChooser(grdCategory);
            LoadPrivilege();
        }

        private void frm_E_InvoiceList_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Home && e.Modifiers == Keys.Control)
            {
                dt1.Focus();
            }
            if (e.KeyCode == Keys.Insert)
            {
                grdCategory.Focus();
            }
        }

        private void frm_E_InvoiceList_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (dt1.EditValue != null)
                Shared.SL_I_FromDate = dateFrom;
            else
                Shared.SL_I_FromDate = null;

            if (dt2.EditValue != null)
                Shared.SL_I_ToDate = dateTo;
            else
                Shared.SL_I_ToDate = null;

            ErpUtils.save_Grid_Layout(grdCategory, this.Name.Replace("frm_", ""), true);
        }

        private void dt1_EditValueChanged(object sender, EventArgs e)
        {
            if (dateFrom != DateTime.MinValue && dateTo != DateTime.MinValue)
            {
                if (dt1.DateTime != DateTime.MinValue)
                    dateFrom = dt1.DateTime;
                else
                    dateFrom = Shared.minDate;

                if (dt2.DateTime != DateTime.MinValue)
                    dateTo = dt2.DateTime;
                else
                {
                    dateTo = Shared.maxDate;
                }
            }
        }

        private void btnClearSearch_Click(object sender, EventArgs e)
        {
            dateFrom = Shared.minDate;
            dateTo = Shared.maxDate;
            dt1.EditValue = null;
            dt2.EditValue = null;
            barBtnRefresh.PerformClick();
        }
        private void barBtn_New_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (Shared.CarsAvailable == false)
            {
                if (ErpUtils.IsFormOpen(typeof(frm_SL_Invoice)))
                    Application.OpenForms["frm_SL_Invoice"].Close();

                if (ErpUtils.IsFormOpen(typeof(frm_SL_Invoice)))
                    Application.OpenForms["frm_SL_Invoice"].BringToFront();
                else
                    new frm_SL_Invoice().Show();
            }
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Refresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            GetInvoices();
        }

        private void barBtn_Open_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Open_Selected_Invoice();
        }


        private void grdCategory_DoubleClick(object sender, EventArgs e)
        {
            Open_Selected_Invoice();
        }

        private void NBI_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            if (((NavBarItem)sender).Name == "NBI_Customers")
            {
                frmMain.OpenSL_Customer();
            }

            var view = grdCategory.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            int strId = Convert.ToInt32(view.GetFocusedRowCellValue(colStore));
            string strName = view.GetFocusedRowCellDisplayText(col_StoreId).ToString();
            string strFltr = (Shared.IsEnglish == true ? ResSLEn.txtStore : ResSLAr.txtStore)//"المخزن: " 
                + strName;


        }

        private void Open_Selected_Invoice()
        {
            var view = grdCategory.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            int inv_id = Convert.ToInt32(view.GetRowCellValue(focused_row_index, col_SL_InvoiceId));

            if (Is_OpenForSelect == true)
            {
                SelectedInvId = inv_id;
                SelectedInvCode = view.GetRowCellValue(focused_row_index, col_InvoiceCode).ToString();

                this.Close();
                return;
            }
            if (cars == false)
            {
                if (ErpUtils.IsFormOpen(typeof(frm_SL_Invoice)))
                    Application.OpenForms["frm_SL_Invoice"].Close();

                if (ErpUtils.IsFormOpen(typeof(frm_SL_Invoice)))
                    Application.OpenForms["frm_SL_Invoice"].BringToFront();
                else
                    new frm_SL_Invoice(inv_id).Show();
            }
        }

        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                if (Shared.LstUserPrvlg.Where(pr => pr.PId == (int)FormsNames.SL_Customer).Count() < 1)
                {
                    mi_OpenDealer.Enabled = false;
                }

                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.SL_Invoice).FirstOrDefault();
                if (!p.CanAdd)
                    barBtnNew.Enabled = false;
                if (!p.CanPrint)
                    barMnu_Print.Enabled = barBtn_Print1.Enabled = barBtn_PrintData.Enabled = false;
            }
        }

        private void GetInvoices()
        {
            int focusedIndex = (grdCategory.FocusedView as GridView).FocusedRowHandle;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            try
            {
                //var stCompany = DB.ST_CompanyInfos.FirstOrDefault();
                //var EnvoiceValue = stCompany.E_invoiceAvailable;
                if (Shared.E_invoiceAvailable== true)
                {
                    var invoices = (from c in DB.SL_Invoices
                                        //update
                                   // join d in DB.SL_InvoiceDetails on c.SL_InvoiceId equals d.SL_InvoiceId

                                    join v in DB.SL_Customers on c.CustomerId equals v.CustomerId
                                    // join s in DB.IC_Stores on c.StoreId equals s.StoreId
                                    where customerId == 0 ? true : c.CustomerId == customerId
                                    join b in DB.ST_InvoiceBooks
                                    on c.InvoiceBookId equals b.InvoiceBookId
                                    where b.IsTaxable == true && b.ProcessId == (int)Process.SellInvoice
                                    where c.InvoiceDate.Date >= dateFrom && c.InvoiceDate.Date <= dateTo
                                    where Shared.user.UserChangeStore ? true : DB.IC_Stores.Where(x => x.StoreId == Shared.user.DefaultStore ||
                                                                         x.ParentId == Shared.user.DefaultStore).Select(x => x.StoreId).Contains(c.StoreId)// c.StoreId == Shared.user.DefaultStore
                                    where Shared.user.AccessOtherUserTrns ? true : c.UserId == Shared.UserId

                                    from lkp in DB.LKP_Processes.Where(lkp => lkp.ProcessId == c.ProcessId).DefaultIfEmpty()
                                        //from outTrns in DB.IC_OutTrns.Where(outTrns => c.ProcessId == (int)Process.OutTrns && outTrns.OutTrnsId == c.SourceId).DefaultIfEmpty()
                                        //from so in DB.SL_SalesOrders.Where(so => c.ProcessId == (int)Process.SalesOrder && so.SL_SalesOrderId == c.SourceId).DefaultIfEmpty()
                                        //let custGroup = DB.SL_Group_Customers
                                        //let Region=DB.SL_CustomerRegions.Where(a=>a.IdRegion==v.IdRegion).FirstOrDefault().RegionName
                                    orderby c.InvoiceDate
                                    let invoiceDetailIds = DB.SL_InvoiceDetails.Where(a => a.SL_InvoiceId == c.SL_InvoiceId).Select(a => a.SL_InvoiceDetailId).ToList()
                                    // let Total = DB.SL_InvoiceDetails.Where(a => a.SL_InvoiceId == c.SL_InvoiceId).ToList()
                                    let Total = DB.SL_InvoiceDetails.Where(a => a.SL_InvoiceId == c.SL_InvoiceId).ToList()
                                    let TaxIdDiscount = DB.E_TaxableTypes.Where(a => a.Code == "T4").FirstOrDefault()

                                    let TotalTaxesAddedList = (from r in DB.SL_InvoiceDetailSubTaxValues
                                                               join rd in DB.E_TaxableTypes on r.esubTypeId equals rd.E_TaxableTypeId
                                                               where invoiceDetailIds.Contains(r.InvoiceDetailId)
                                                               where rd.ParentTaxId != TaxIdDiscount.E_TaxableTypeId
                                                               select r).ToList()
                                    let TotalTaxesRemovedList = (from r in DB.SL_InvoiceDetailSubTaxValues
                                                                 join rd in DB.E_TaxableTypes on r.esubTypeId equals rd.E_TaxableTypeId
                                                                 where invoiceDetailIds.Contains(r.InvoiceDetailId)
                                                                 where rd.ParentTaxId == TaxIdDiscount.E_TaxableTypeId
                                                                 select r).ToList()
                                    let TotaltaxesAddValue = TotalTaxesAddedList.Count != 0 ? Convert.ToDouble(TotalTaxesAddedList.Sum(z => z.value)) : 0
                                    let TotaltaxesRemovedValue = TotalTaxesRemovedList.Count != 0 ? Convert.ToDouble(TotalTaxesRemovedList.Sum(z => z.value)) : 0
                                    //let Totaltaxes = DB.SL_InvoiceDetailSubTaxValues
                                    //.Where(a => DB.SL_InvoiceDetails.Where(v => v.SL_InvoiceId == c.SL_InvoiceId).ToList().Select(v => v.SL_InvoiceDetailId).Contains(a.InvoiceDetailId)).ToList()

                                    join st in DB.IC_Stores on c.StoreId equals st.StoreId
                                    select new
                                    {
                                        Process = lkp == null ? null : (Shared.IsEnglish ? lkp.ProcessEnglishName : lkp.ProcessName),
                                        //SourceCode = (lkp == null ? null : (lkp.ProcessId == (int)Process.OutTrns ? outTrns.OutTrnsCode : so.SalesOrderCode)),
                                        DiscountRatio = c.DiscountRatio,
                                        DiscountValue = c.DiscountValue,
                                        Expenses = c.Expenses,
                                        Net = c.Net,
                                        Paid = c.Paid,
                                        Remains = c.Remains,
                                        Total= Total.Count != 0 ? Convert.ToDouble(Total.Sum(z => z.TotalSellPrice)) : 0,
                                        c.InvoiceCode,
                                        c.InvoiceDate,
                                        c.JornalId,
                                        c.Notes,
                                        c.PayMethod,
                                        c.SL_InvoiceId,
                                        StoreId = st.StoreNameAr,
                                        //StoreId = c.StoreId == 0 ? DB.IC_Stores.FirstOrDefault(s => s.StoreId == d.StoreId).StoreNameAr : DB.IC_Stores.FirstOrDefault(s => s.StoreId == c.StoreId).StoreNameAr,
                                        store = c.StoreId,
                                       // store = c.StoreId == 0 ? d.StoreId : c.StoreId,

                                        c.UserId,
                                        CustomerId = v.CusNameAr,
                                        CustId = v.CustomerId,
                                        c.SalesEmpId,
                                        TotalCostPrice = c.TotalCostPrice,
                                        Profit = c.Net - c.TotalCostPrice,
                                        GroupId = v.CategoryId,
                                        c.Is_OutTrans,
                                        c.InvoiceBookId,
                                        c.AddTaxValue,
                                        c.DeductTaxValue,
                                        c.TaxValue,
                                        c.DueDate,
                                        c.CrncId,
                                        c.CrncRate,
                                        c.DriverName,
                                        c.VehicleNumber,
                                        c.Destination,
                                        c.IsOffer,
                                        profitRatio = c.TotalCostPrice == 0 ? 1 : (c.Net > 0 ? (c.Net - c.TotalCostPrice) / c.Net : 0),
                                        ProfitCostRatio = c.TotalCostPrice == 0 ? 1 : (c.TotalCostPrice > 0 ? (c.Net - c.TotalCostPrice) / c.TotalCostPrice : 0),
                                        c.DrawerAccountId,
                                        //mohammad 10/11/2019
                                        CategoryId = v.CategoryId,// custGroup.Where(x => x.GroupId == v.GroupId).Select(x => Shared.IsEnglish ? x.NameEn : x.NameAr).FirstOrDefault()
                                        v.City,
                                        Region,
                                        c.uuid,
                                        c.Estatus,
                                        c.EstatusCode,
                                        c.issuerId,
                                        c.syncDate,
                                        c.lastSyncDate,
                                        //Totaltaxes = Totaltaxes.Count != 0 ? Convert.ToDouble(Totaltaxes.Sum(z => z.value)) : 0
                                        Totaltaxes = TotaltaxesAddValue - TotaltaxesRemovedValue

                                    }).Distinct().ToList();

                    grdCategory.DataSource = invoices;

                    (grdCategory.FocusedView as GridView).FocusedRowHandle = focusedIndex;
                }
                else
                {
                    var invoices = (from c in DB.SL_Invoices
                                        //update
                                   // join d in DB.SL_InvoiceDetails on c.SL_InvoiceId equals d.SL_InvoiceId

                                    join v in DB.SL_Customers on c.CustomerId equals v.CustomerId
                                    // join s in DB.IC_Stores on c.StoreId equals s.StoreId
                                    where customerId == 0 ? true : c.CustomerId == customerId
                                    where c.InvoiceDate.Date >= dateFrom && c.InvoiceDate.Date <= dateTo
                                    where Shared.user.UserChangeStore ? true : DB.IC_Stores.Where(x => x.StoreId == Shared.user.DefaultStore ||
                                                                         x.ParentId == Shared.user.DefaultStore).Select(x => x.StoreId).Contains(c.StoreId)// c.StoreId == Shared.user.DefaultStore
                                    where Shared.user.AccessOtherUserTrns ? true : c.UserId == Shared.UserId

                                    from lkp in DB.LKP_Processes.Where(lkp => lkp.ProcessId == c.ProcessId).DefaultIfEmpty()
                                        //from outTrns in DB.IC_OutTrns.Where(outTrns => c.ProcessId == (int)Process.OutTrns && outTrns.OutTrnsId == c.SourceId).DefaultIfEmpty()
                                        //from so in DB.SL_SalesOrders.Where(so => c.ProcessId == (int)Process.SalesOrder && so.SL_SalesOrderId == c.SourceId).DefaultIfEmpty()
                                        //let custGroup = DB.SL_Group_Customers
                                        //let Region=DB.SL_CustomerRegions.Where(a=>a.IdRegion==v.IdRegion).FirstOrDefault().RegionName
                                    orderby c.InvoiceDate
                                    let invoiceDetailIds = DB.SL_InvoiceDetails.Where(a => a.SL_InvoiceId == c.SL_InvoiceId).Select(a => a.SL_InvoiceDetailId).ToList()
                                    //let Total = DB.SL_InvoiceDetails.Where(a => a.SL_InvoiceId == c.SL_InvoiceId).ToList()
                                    let Total = DB.SL_InvoiceDetails.Where(a => a.SL_InvoiceId == c.SL_InvoiceId).ToList()
                                    let TaxIdDiscount = DB.E_TaxableTypes.Where(a => a.Code == "T4").FirstOrDefault()

                                    let TotalTaxesAddedList = (from r in DB.SL_InvoiceDetailSubTaxValues
                                                               join rd in DB.E_TaxableTypes on r.esubTypeId equals rd.E_TaxableTypeId
                                                               where invoiceDetailIds.Contains(r.InvoiceDetailId)
                                                               where rd.ParentTaxId != TaxIdDiscount.E_TaxableTypeId
                                                               select r).ToList()
                                    let TotalTaxesRemovedList = (from r in DB.SL_InvoiceDetailSubTaxValues
                                                                 join rd in DB.E_TaxableTypes on r.esubTypeId equals rd.E_TaxableTypeId
                                                                 where invoiceDetailIds.Contains(r.InvoiceDetailId)
                                                                 where rd.ParentTaxId == TaxIdDiscount.E_TaxableTypeId
                                                                 select r).ToList()
                                    let TotaltaxesAddValue = TotalTaxesAddedList.Count != 0 ? Convert.ToDouble(TotalTaxesAddedList.Sum(z => z.value)) : 0
                                    let TotaltaxesRemovedValue = TotalTaxesRemovedList.Count != 0 ? Convert.ToDouble(TotalTaxesRemovedList.Sum(z => z.value)) : 0
                                    join st in DB.IC_Stores on c.StoreId equals st.StoreId
                                    select new
                                    {
                                        Process = lkp == null ? null : (Shared.IsEnglish ? lkp.ProcessEnglishName : lkp.ProcessName),
                                        //SourceCode = (lkp == null ? null : (lkp.ProcessId == (int)Process.OutTrns ? outTrns.OutTrnsCode : so.SalesOrderCode)),
                                        DiscountRatio = c.DiscountRatio,
                                        DiscountValue = c.DiscountValue,
                                        Expenses = c.Expenses,
                                        Net = c.Net,
                                        Paid = c.Paid,
                                        Remains = c.Remains,
                                        Total = Total.Count != 0 ? Convert.ToDouble(Total.Sum(z => z.TotalSellPrice)) : 0,
                                        c.InvoiceCode,
                                        c.InvoiceDate,
                                        c.JornalId,
                                        c.Notes,
                                        c.PayMethod,
                                        c.SL_InvoiceId,
                                        StoreId = st.StoreNameAr,
                                        //StoreId = c.StoreId == 0 ? DB.IC_Stores.FirstOrDefault(s => s.StoreId == d.StoreId).StoreNameAr : DB.IC_Stores.FirstOrDefault(s => s.StoreId == c.StoreId).StoreNameAr,
                                        store = c.StoreId,
                                      //  store = c.StoreId == 0 ? d.StoreId : c.StoreId,

                                        c.UserId,
                                        CustomerId = v.CusNameAr,
                                        CustId = v.CustomerId,
                                        c.SalesEmpId,
                                        TotalCostPrice = c.TotalCostPrice,
                                        Profit = c.Net - c.TotalCostPrice,
                                        GroupId = v.CategoryId,
                                        c.Is_OutTrans,
                                        c.InvoiceBookId,
                                        c.AddTaxValue,
                                        c.DeductTaxValue,
                                        c.TaxValue,
                                        c.DueDate,
                                        c.CrncId,
                                        c.CrncRate,
                                        c.DriverName,
                                        c.VehicleNumber,
                                        c.Destination,
                                        c.IsOffer,
                                        profitRatio = c.TotalCostPrice == 0 ? 1 : (c.Net > 0 ? (c.Net - c.TotalCostPrice) / c.Net : 0),
                                        ProfitCostRatio = c.TotalCostPrice == 0 ? 1 : (c.TotalCostPrice > 0 ? (c.Net - c.TotalCostPrice) / c.TotalCostPrice : 0),
                                        c.DrawerAccountId,
                                        //mohammad 10/11/2019
                                        CategoryId = v.CategoryId,// custGroup.Where(x => x.GroupId == v.GroupId).Select(x => Shared.IsEnglish ? x.NameEn : x.NameAr).FirstOrDefault()
                                        v.City,
                                        Region,
                                        c.uuid,
                                        c.Estatus,
                                        c.EstatusCode,
                                        c.issuerId,
                                        c.syncDate,
                                        c.lastSyncDate,
                                        // Totaltaxes = Totaltaxes.Count != 0 ? Convert.ToDouble(Totaltaxes.Sum(z => z.value)) : 0
                                        Totaltaxes = TotaltaxesAddValue - TotaltaxesRemovedValue
                                    }).Distinct().ToList();

                    grdCategory.DataSource = invoices;

                    (grdCategory.FocusedView as GridView).FocusedRowHandle = focusedIndex;
                }
            }
            catch (Exception ex) { MessageBox.Show(ex.Message); }
            //try
            //{
            //    decimal profit = Convert.ToDecimal(col_Profit.SummaryItem.SummaryValue);
            //    decimal total = Convert.ToDecimal(col_Net.SummaryItem.SummaryValue);
            //    decimal Cost = Convert.ToDecimal(col_TotalCostPrice.SummaryItem.SummaryValue);

            //    decimal profitRatio = 0;
            //    if (Cost == 0)
            //        profitRatio = 1;
            //    else
            //    {
            //        if (total == 0)
            //            profitRatio = 0;
            //        else
            //            profitRatio = profit / total;
            //    }

            //    col_ProfitRatio.SummaryItem.SetSummary(DevExpress.Data.SummaryItemType.Custom, profitRatio.ToString("p2"));

            //    decimal profitCostRatio = 0;
            //    if (Cost == 0)
            //        profitCostRatio = 1;
            //    else
            //    {
            //        if (Cost == 0)
            //            profitCostRatio = 0;
            //        else
            //            profitCostRatio = profit / Cost;
            //    }

            //    col_ProfitCostRatio.SummaryItem.SetSummary(DevExpress.Data.SummaryItemType.Custom, profitCostRatio.ToString("p2"));
            //}
            //catch
            //{ }
        }

        private void barBtn_Help_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "فاتورة مبيعات جديدة");
        }

        private void mi_OpenDealer_Click(object sender, EventArgs e)
        {
            var view = grdCategory.FocusedView as GridView;
            int focused_row_index = view.FocusedRowHandle;
            if (focused_row_index < 0)
                return;

            int DealerId = Convert.ToInt32(view.GetRowCellValue(focused_row_index, col_CustId));

            if (ErpUtils.IsFormOpen(typeof(frm_SL_Customer)))
                Application.OpenForms["frm_SL_Customer"].Close();

            new frm_SL_Customer(DealerId).Show();
        }

        private void barBtn_Print1_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(this.Text, "", "", "", grdCategory, false).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        private void barBtn_PrintData_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(this.Text, "", "", "", grdCategory, false, true).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }


        private void barbtn_ImportInvoice_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            importInvoices();
        }

        public void importInvoices()
        {
            ERPDataContext DB = new ERPDataContext();
            DataTable dtUOM = new DataTable();
            List<DAL.IC_UOM> uom_list;
            MyHelper.GetUomDataTable(dtUOM);
            uom_list = DB.IC_UOMs.ToList();

            OpenFileDialog ofd = new OpenFileDialog();
            ofd.Filter = "Excel File(*.xls)|*.xls|Excel File(*.xlsx)|*.xlsx";
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    DataSet result = ExcelHelper.ReadExcelFileAsDataSet(ofd.FileName, true);


                    int count = 1;
                    string invCode = "";
                    int invId = 0;
                    decimal totalInvoice = 0;
                    bool newInvoice = true;
                    int index = 1;

                    List < SL_Invoice> invoices_lst = DB.SL_Invoices.ToList();
                    List<SL_Customer> cstmrs_lst = DB.SL_Customers.ToList();
                    List<IC_Store> stores_lst= DB.IC_Stores.ToList();
                    //List<ACC_CostCenter> costCenters_lst = DB.ACC_CostCenters.ToList();
                    List<ST_Currency> currencies_lst = DB.ST_Currencies.ToList();
                    List<IC_UOM> UOM_lst = DB.IC_UOMs.ToList();
                    List<E_TaxableType> TaxableTypes_lst = DB.E_TaxableTypes.ToList();
                    List<IC_Item> itms_lst = DB.IC_Items.ToList();

                    var taxesFromSheet = result.Tables[0].Rows[0].ItemArray
                        .Select((x, i) => new { Tax = x.ToString().ToLowerInvariant(), Index = i })
                        .Where(x => x.Tax.Contains("tax"))
                        .OrderBy(x => x.Tax)
                        .GroupBy(x => x.Tax.Substring(0, x.Tax.IndexOf('.')))
                        .ToList();

                    var taxesFromSheetCount = taxesFromSheet.Count();

                    ProgressPanel progress = new ProgressPanel();
                    progress.Caption = "جاري تحميل الفواتير";
                    progress.Description = "الرجاء الانتظار";
                    progress.Show();

                    foreach (DataRow d in result.Tables[0].Rows)
                    {
                        try
                        {
                            count++;

                            if (count % 5 == 0)
                                progress.Description = "تم الانتهاء من : " + (count / result.Tables[0].Rows.Count * 100) + "%";


                            if (result.Tables[0].Rows.IndexOf(d) == 0) { continue; }

                            if (!newInvoice && invCode != "" && invCode != Convert.ToString(d[0]))
                            {
                                frm_SL_Invoice frmsl = new frm_SL_Invoice();
                                //frmsl.Show();
                                frmsl.Loadform();

                                frmsl.invoiceId = invId;
                                frmsl.LoadInvoice();
                                frmsl.Save_Invoice();
                                //frmsl.Close();
                                newInvoice = true;
                                totalInvoice = 0;
                            }

                            if (newInvoice || invCode != Convert.ToString(d[0]))
                            {
                                SL_Invoice inv = new SL_Invoice();
                                // invoice Code
                                inv.InvoiceCode = invCode = Convert.ToString(d[0]);
                                //Check Invoice Code
                                var invoiceIds = invoices_lst.Where(x => x.InvoiceCode == inv.InvoiceCode).Select(x => x.SL_InvoiceId).ToList();

                                if (invoiceIds.Count > 0)
                                {
                                    XtraMessageBox.Show(
                                         string.Format(string.Format(" كود الفاتورة {0}  موجود", Convert.ToString(d[0])))
                                         , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                    continue;
                                }
                                // Customer
                                int csId = cstmrs_lst.Where(x => x.CusNameAr == Convert.ToString(d[1]).Trim()).Select(x => x.CustomerId).FirstOrDefault();
                                if (csId > 0)
                                {
                                    inv.CustomerId = csId;
                                }
                                else
                                {

                                    XtraMessageBox.Show(
                                        string.Format(string.Format("العميل {0} غير موجود", Convert.ToString(d[1])))
                                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                    continue;
                                }
                                // Invoice Date
                                if (string.IsNullOrEmpty(Convert.ToString(d[2])))
                                    inv.InvoiceDate = MyHelper.Get_Server_DateTime();
                                else inv.InvoiceDate = Convert.ToDateTime(Convert.ToString(d[2]));
                                // Store
                                int storeId = stores_lst.Where(x => x.StoreNameAr == Convert.ToString(d[3])).Select(x => x.StoreId).FirstOrDefault();
                                if (storeId > 0)
                                {
                                    inv.StoreId = storeId;
                                }
                                else
                                {

                                    XtraMessageBox.Show(
                                        string.Format(string.Format("المخزن/الفرع {0} غير موجود", Convert.ToString(d[3])))
                                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                    continue;
                                }
                                // CostCenter
                                //if (!string.IsNullOrEmpty(Convert.ToString(d[4])))
                                //{
                                    //int ccId = costCenters_lst.Where(x => x.CostCenterName == Convert.ToString(d[4])).Select(x => x.CostCenterId).FirstOrDefault();
                                    //if (ccId > 0)
                                    //{
                                    //    inv.CostCenterId = ccId;
                                    //}
                                //}
                                // Currency
                                if (!string.IsNullOrEmpty(Convert.ToString(d[5])))
                                {
                                    inv.CrncId = currencies_lst.Where(c => c.crncName == Convert.ToString(d[5])).Select(c => c.CrncId).FirstOrDefault();

                                }
                                else { inv.CrncId = 0; }
                                // Currency rate
                                if (!string.IsNullOrEmpty(Convert.ToString(d[6])))
                                {
                                    inv.CrncRate = Convert.ToDecimal(d[6]);
                                }
                                else { inv.CrncId = 1; }

                                // Paid
                                if (!string.IsNullOrEmpty(Convert.ToString(d[7])))
                                {
                                    inv.Paid = Convert.ToDecimal(d[7]);
                                }
                                else
                                {
                                    inv.Paid = 0;
                                }
                                // Discount per invoice
                                if (!string.IsNullOrEmpty(Convert.ToString(d[8])))
                                {
                                    inv.DiscountValue = Convert.ToDecimal(d[8]);
                                }
                                else
                                {
                                    inv.DiscountValue = 0;
                                }

                                // Discount per invoice
                                if (!string.IsNullOrEmpty(Convert.ToString(d[8])))
                                {
                                    inv.DiscountValue = Convert.ToDecimal(d[8]);
                                }
                                else
                                {
                                    inv.DiscountValue = 0;
                                }
                                // Net
                                //if (!string.IsNullOrEmpty(Convert.ToString(d[9])))
                                //{
                                //    inv.Net = Convert.ToDecimal(d[9]);
                                //}
                                //else
                                //{
                                inv.Net = (decimal)(from DataRow r in result.Tables[0].Rows
                                                    where r[0] == d[0]
                                                    select Convert.ToDouble(r[9])).Sum();

                                //}

                                inv.JornalId = 0;
                                newInvoice = false;

                                DB.SL_Invoices.InsertOnSubmit(inv);
                                DB.SubmitChanges();
                                invId = inv.SL_InvoiceId;
                            }

                            {
                                if (string.IsNullOrEmpty(Convert.ToString(d[10]))) continue;

                                IC_Item Item = itms_lst.Where(x => x.ItemNameAr == Convert.ToString(d[10])).FirstOrDefault();
                                if (Item != null)
                                {
                                    SL_InvoiceDetail detail = new SL_InvoiceDetail();
                                    detail.SL_InvoiceId = invId;
                                    detail.ItemId = Item.ItemId;
                                    detail.UOMIndex = Item.DfltSellUomIndx;
                                    if (string.IsNullOrEmpty(Convert.ToString(d[11])))
                                    {
                                        MyHelper.GetUOMs(Item, dtUOM, uom_list);
                                        detail.UOMId = Convert.ToInt32(dtUOM.Rows[Item.DfltSellUomIndx]["UomId"]);
                                    }
                                    else
                                    {
                                        detail.UOMId = UOM_lst.Where(i => i.UOM == Convert.ToString(d[11])).Select(x => x.UOMId).FirstOrDefault();
                                    }
                                    if (string.IsNullOrEmpty(Convert.ToString(d[12])) || Convert.ToString(d[12]) == "0") continue;
                                    detail.Qty = Convert.ToDecimal(d[12]);

                                    detail.Length = string.IsNullOrEmpty(Convert.ToString(d[13])) ? Item.Length : decimal.Parse(Convert.ToString(d[13]));
                                    detail.Width = string.IsNullOrEmpty(Convert.ToString(d[14])) ? Item.Width : decimal.Parse(Convert.ToString(d[14]));
                                    detail.Height = string.IsNullOrEmpty(Convert.ToString(d[15])) ? Item.Height : decimal.Parse(Convert.ToString(d[15]));

                                    detail.SellPrice = string.IsNullOrEmpty(Convert.ToString(d[16])) ? 0 : decimal.Parse(Convert.ToString(d[16]));

                                    #region Discount on Item
                                    if (!string.IsNullOrEmpty(Convert.ToString(d[17])))
                                    {
                                        detail.DiscountRatio = string.IsNullOrEmpty(Convert.ToString(d[17])) ? 0 : decimal.Parse(Convert.ToString(d[17]));
                                        detail.DiscountValue = (detail.SellPrice * detail.Qty * detail.DiscountRatio) / 100;

                                    }

                                    if (d[18] != DBNull.Value && d[18] != null &&
                                        (!string.IsNullOrEmpty(Convert.ToString(d[18])) || Convert.ToString(d[18]) != "0"))
                                    {
                                        detail.DiscountValue = decimal.Parse(Convert.ToString(d[18]));
                                        detail.DiscountRatio = ((detail.DiscountValue * 100) / (detail.SellPrice * detail.Qty)) / 100;

                                    }
                                    if (Convert.ToString(d[18]) == "0" && Convert.ToString(d[17]) == "0")
                                    {
                                        detail.DiscountRatio = 0;
                                        detail.DiscountValue = 0;
                                    }
                                    #endregion
                                    if (!string.IsNullOrEmpty(Convert.ToString(d[19])))
                                    {
                                        detail.bonusDiscount = decimal.Parse(Convert.ToString(d[19]));
                                    }
                                    var hasDescriptions = false;
                                    if (!string.IsNullOrEmpty(result.Tables[0].Rows[0][20].ToString()))
                                    {
                                        hasDescriptions = (result.Tables[0].Rows[0][20].ToString()== "الوصف");
                                    }
                                    if (!string.IsNullOrEmpty(Convert.ToString(d[20])) && hasDescriptions)
                                    {
                                        detail.ItemDescription = Convert.ToString(d[20]);
                                    }
                                    if (!string.IsNullOrEmpty(Convert.ToString(d[21])) && hasDescriptions)
                                    {
                                        detail.ItemDescriptionEn = Convert.ToString(d[21]);
                                    }
                                    //public static void CalcTotalPrice(int RowHandle, GridView view, decimal TotalQty, decimal SellPrice,
                                    //decimal SalesTaxRatio, decimal CustomTaxRatio, bool calcTaxBeforeDisc, decimal DiscV, decimal EtaxRatio)
                                    detail.TotalSellPrice = string.IsNullOrEmpty(Convert.ToString(d[9])) ? 0 : decimal.Parse(Convert.ToString(d[9]));

                                    totalInvoice += detail.TotalSellPrice;// (detail.SellPrice * detail.Qty) - detail.DiscountValue;
                                    //var invoice = DB.SL_Invoices.FirstOrDefault(a => a.SL_InvoiceId == invId);
                                    //invoice.Net = totalInvoice;
                                    DB.SL_InvoiceDetails.InsertOnSubmit(detail);
                                    DB.SubmitChanges();

                                    #region Tax on Item

                                    var itemTaxCodeIndex = hasDescriptions?22:20;
                                    var itemTaxRatioIndex = hasDescriptions?23:21;
                                    var itemTaxValueIndex = hasDescriptions?24:22;
                                    if (!string.IsNullOrEmpty(Convert.ToString(d[itemTaxCodeIndex])))
                                    {
                                        var subTaxInDb = TaxableTypes_lst
                                                    .Where(s => s.Code == d[itemTaxCodeIndex].ToString())
                                                    .Select(s => new { Id = s.E_TaxableTypeId, ParentId = s.ParentTaxId })
                                                    .FirstOrDefault();


                                        if (subTaxInDb == null)
                                        {
                                            XtraMessageBox.Show(
                                            string.Format(string.Format("{0} لا يوجد ضريبة بهذا الاسم", Convert.ToString(d[itemTaxCodeIndex])))
                                            , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                        }

                                        else if (subTaxInDb.ParentId == null)
                                        {
                                            XtraMessageBox.Show(
                                            string.Format(string.Format(" {ليست  ضريبة فرعية {0", Convert.ToString(d[itemTaxCodeIndex])))
                                            , "", MessageBoxButtons.OK, MessageBoxIcon.Information);

                                        }

                                        else
                                        {
                                            var subTax = new SL_InvoiceDetailSubTaxValue();
                                            subTax.InvoiceDetailId = detail.SL_InvoiceDetailId;
                                            subTax.esubTypeId = subTaxInDb.Id;
                                            subTax.TaxRatio = string.IsNullOrEmpty(Convert.ToString(d[itemTaxRatioIndex])) ? 0 : decimal.Parse(Convert.ToString(d[itemTaxRatioIndex]));
                                            subTax.value = string.IsNullOrEmpty(Convert.ToString(d[itemTaxValueIndex])) ? 0 : decimal.Parse(Convert.ToString(d[itemTaxValueIndex]));
                                            DB.SL_InvoiceDetailSubTaxValues.InsertOnSubmit(subTax);
                                            DB.SubmitChanges();
                                        }

                                    }

                                    #endregion

                                    #region Old Tax on Item Code
                                    //while (result.Tables[0].Columns.Count > ccount)
                                    //{
                                    //    if (string.IsNullOrEmpty(Convert.ToString(d[ccount])))
                                    //    {
                                    //        ccount++;
                                    //        continue;
                                    //    }
                                    //    else
                                    //    {
                                    //        string subt = Convert.ToString(d[ccount]);
                                    //        var subtaxId = DB.E_TaxableTypes.Where(s => s.Code == subt).Select(x => x.E_TaxableTypeId).FirstOrDefault();
                                    //        if (subtaxId > 0)
                                    //        {
                                    //            SL_InvoiceDetailSubTaxValue subtax = new SL_InvoiceDetailSubTaxValue();
                                    //            subtax.InvoiceDetailId = detail.SL_InvoiceDetailId;
                                    //            subtax.esubTypeId = subtaxId;
                                    //            ccount++;

                                    //            subtax.TaxRatio = string.IsNullOrEmpty(Convert.ToString(d[ccount])) ? 0 : decimal.Parse(Convert.ToString(d[ccount]));
                                    //            ccount++;
                                    //            subtax.value = string.IsNullOrEmpty(Convert.ToString(d[ccount])) ? 0 : decimal.Parse(Convert.ToString(d[ccount]));
                                    //            DB.SL_InvoiceDetailSubTaxValues.InsertOnSubmit(subtax);
                                    //            DB.SubmitChanges();
                                    //            ccount++;
                                    //        }
                                    //        else break;

                                    //    }
                                    //}
                                    #endregion

                                    #region Dynamic Taxes From Excel Sheet
                                    if (taxesFromSheetCount > 0)
                                    {
                                        foreach (var taxGroup in taxesFromSheet)
                                        {
                                            var taxCodeIndex = taxGroup.ElementAtOrDefault(0).Index;
                                            var taxRatioIndex = taxGroup.ElementAtOrDefault(1).Index;
                                            var taxValueIndex = taxGroup.ElementAtOrDefault(2).Index;

                                            if (!string.IsNullOrEmpty(Convert.ToString(d[taxCodeIndex])))
                                            {


                                                var taxInDB = TaxableTypes_lst
                                                               .Where(s => s.Code == d[taxCodeIndex].ToString())
                                                               .Select(s => new { Id = s.E_TaxableTypeId, ParentId = s.ParentTaxId })
                                                               .FirstOrDefault();


                                                if (taxInDB == null)
                                                {
                                                    XtraMessageBox.Show(
                                                    string.Format(string.Format("{0} لا يوجد ضريبة بهذا الاسم", Convert.ToString(d[taxCodeIndex])))
                                                    , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                                }

                                                else if (taxInDB.ParentId == null)
                                                {
                                                    XtraMessageBox.Show(
                                                     string.Format(string.Format(" {ليست  ضريبة فرعية {0", Convert.ToString(d[taxCodeIndex])))
                                                     , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                                }

                                                else
                                                {
                                                    var subTax = new SL_InvoiceDetailSubTaxValue();
                                                    subTax.InvoiceDetailId = detail.SL_InvoiceDetailId;
                                                    subTax.esubTypeId = taxInDB.Id;
                                                    subTax.TaxRatio = string.IsNullOrEmpty(Convert.ToString(d[taxRatioIndex])) ? 0 : decimal.Parse(Convert.ToString(d[taxRatioIndex]));
                                                    subTax.value = string.IsNullOrEmpty(Convert.ToString(d[taxValueIndex])) ? 0 : decimal.Parse(Convert.ToString(d[taxValueIndex]));
                                                    DB.SL_InvoiceDetailSubTaxValues.InsertOnSubmit(subTax);
                                                    DB.SubmitChanges();

                                                }
                                            }
                                        }
                                    }
                                    #endregion

                                    if (index == result.Tables[0].Rows.Count - 1)
                                    {
                                        frm_SL_Invoice frmsl = new frm_SL_Invoice();
                                        frmsl.Show();
                                        frmsl.invoiceId = invId;
                                        frmsl.LoadInvoice();
                                        frmsl.Save_Invoice();
                                        frmsl.Close();
                                        newInvoice = true;
                                        totalInvoice = 0;
                                    }
                                    index++;

                                    GC.Collect();
                                }
                                else
                                {
                                    XtraMessageBox.Show(
                                  string.Format(string.Format("الصنف {0} غير موجود", Convert.ToString(d[10])))
                                  , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                    continue;
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            progress.Hide();
                            XtraMessageBox.Show(
                                string.Format("حدث خطأ أثناء في تحميل الفواتير")
                                , "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            break;
                        }

                    }


                    progress.Hide();

                    DB.SubmitChanges();

                    XtraMessageBox.Show(
                        string.Format("تم تحميل الفواتير بشكل سليم")
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {

                    XtraMessageBox.Show(
                        string.Format("حدث خطأ أثناء في تحميل الملف")
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            else
                return;
        }
    }
}