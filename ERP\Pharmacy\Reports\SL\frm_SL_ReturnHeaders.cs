﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;

using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraReports.UI;

namespace Reports
{
    public partial class frm_SL_ReturnHeaders : DevExpress.XtraEditors.XtraForm
    {
        public bool UserCanOpen;
        string reportName, dateFilter, otherFilters, custGroupAccNumber;

        int store_id1, store_id2, CustomerId1, CustomerId2, custGroupId, salesEmpId;
        byte FltrTyp_Store, fltrTyp_Date, FltrTyp_Customer, FltrTyp_InvBook;
        DateTime date1, date2;

        List<int> lst_invBooksId = new List<int>();

        public frm_SL_ReturnHeaders(string reportName, string dateFilter, string otherFilters,
            byte fltrTyp_Store, int store_id1, int store_id2,
            byte fltrTyp_Date, DateTime date1, DateTime date2,
            byte FltrTyp_Customer, int customerId1, int customerId2,
            int custGroupId, string custGroupAccNumber, int salesEmpId,
            byte FltrTyp_InvBook, string InvBooks)
        {
            UserCanOpen = LoadPrivilege();
            if (UserCanOpen == false)
                return;

            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();

            this.reportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;

            this.FltrTyp_Store = fltrTyp_Store;            
            this.fltrTyp_Date = fltrTyp_Date;
            this.FltrTyp_Customer = FltrTyp_Customer;
            
            this.store_id1 = store_id1;
            this.store_id2 = store_id2;

            this.date1 = date1;
            this.date2 = date2;

            this.CustomerId1 = customerId1;
            this.CustomerId2 = customerId2;

            this.salesEmpId = salesEmpId;
            this.custGroupId = custGroupId;
            this.custGroupAccNumber = custGroupAccNumber;

            this.FltrTyp_InvBook = FltrTyp_InvBook;
            Utilities.Get_ChkLst_Items(InvBooks, lst_invBooksId);

            getReportHeader();

            LoadData();

            rep_Currency.DataSource = Shared.lstCurrency;
            rep_Currency.ValueMember = "CrncId";
            rep_Currency.DisplayMember = "crncName";


            if (Shared.user.PR_Return_PayMethod.HasValue)
                col_Paid.OptionsColumn.ShowInCustomizationForm =
                col_Paid.OptionsColumn.ShowInCustomizationForm =
                col_Remains.OptionsColumn.ShowInCustomizationForm =
                col_Remains.OptionsColumn.ShowInCustomizationForm = 
                Shared.user.SL_Return_PayMethod.Value;


            ReportsUtils.ColumnChooser(grdCategory);     
        }

                void LoadData()
        {
            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            var data = from c in DB.SL_Customers
                       //join a in DB.ACC_Accounts
                       // on c.AccountId equals a.AccountId
                       //where custGroupId == 0 ? true : a.AcNumber.StartsWith(custGroupAccNumber)

                       where FltrTyp_Customer == 1 ? c.CustomerId == CustomerId1 : true
                       where (FltrTyp_Customer == 2 && CustomerId1 != 0 && CustomerId2 != 0) ?
                       c.CustomerId >= CustomerId1 && c.CustomerId <= CustomerId2 : true
                       where (FltrTyp_Customer == 2 && CustomerId1 != 0 && CustomerId2 == 0) ?
                       c.CustomerId >= CustomerId1 : true
                       where (FltrTyp_Customer == 2 && CustomerId1 == 0 && CustomerId2 != 0) ?
                       c.CustomerId <= CustomerId2 : true

                       join d in DB.SL_Returns on c.CustomerId equals d.CustomerId
                       join s in DB.SL_ReturnDetails on d.SL_ReturnId equals s.SL_ReturnId
                       where FltrTyp_Store == 1 ? d.StoreId == store_id1 : true
                       where (FltrTyp_Store == 2 && store_id1 != 0 && store_id2 != 0) ?
                       d.StoreId >= store_id1 && d.StoreId <= store_id2 : true
                       where (FltrTyp_Store == 2 && store_id1 != 0 && store_id2 == 0) ?
                       d.StoreId >= store_id1 : true
                       where (FltrTyp_Store == 2 && store_id1 == 0 && store_id2 != 0) ?
                       d.StoreId <= store_id2 : true

                       where fltrTyp_Date == 1 ? d.ReturnDate.Date == date1.Date : true
                       where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                       d.ReturnDate.Date >= date1.Date && d.ReturnDate.Date <= date2.Date : true
                       where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                       d.ReturnDate.Date >= date1.Date : true
                       where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                       d.ReturnDate.Date <= date2.Date : true

                       where salesEmpId == 0 ? true : d.SalesEmpId == salesEmpId
                       where FltrTyp_InvBook == 0 ? true : (d.InvoiceBookId.HasValue && lst_invBooksId.Contains(d.InvoiceBookId.Value))

                       let TradeDiscount = (d == null || d.SL_ReturnDetails.Count() == 0) ? 0 : d.SL_ReturnDetails.Sum(x => x == null ? 0 : x.DiscountValue)
                       let TotalInvoice = d.Net - d.TaxValue + d.DiscountValue + d.Expenses + d.DeductTaxValue - d.AddTaxValue

                       let TaxIdDiscount = DB.E_TaxableTypes.Where(a => a.Code == "T4").FirstOrDefault()
                       let TotalTaxesAddedList = (from r in DB.SlReturnInvoiceDetailSubTaxValues
                                                  join rd in DB.E_TaxableTypes on r.esubTypeId equals rd.E_TaxableTypeId
                                                  join dd in DB.SL_ReturnDetails on r.ReturnInvoiceDetailId equals dd.SL_ReturnDetailId
                                                  where dd.SL_ReturnDetailId == s.SL_ReturnDetailId
                                                  where rd.ParentTaxId != TaxIdDiscount.E_TaxableTypeId
                                                  select r).ToList()
                       let TotalTaxesRemovedList = (from r in DB.SlReturnInvoiceDetailSubTaxValues
                                                    join rd in DB.E_TaxableTypes on r.esubTypeId equals rd.E_TaxableTypeId
                                                    join dd in DB.SL_ReturnDetails on r.ReturnInvoiceDetailId equals dd.SL_ReturnDetailId
                                                    where dd.SL_ReturnDetailId == s.SL_ReturnDetailId
                                                    where rd.ParentTaxId == TaxIdDiscount.E_TaxableTypeId
                                                    select r).ToList()
                       let TotaltaxesAddValue = TotalTaxesAddedList.Count != 0 ? Convert.ToDouble(TotalTaxesAddedList.Sum(z => z.value)) : 0
                       let TotaltaxesRemovedValue = TotalTaxesRemovedList.Count != 0 ? Convert.ToDouble(TotalTaxesRemovedList.Sum(z => z.value)) : 0
                       //let Totaltaxes = DB.SlReturnInvoiceDetailSubTaxValues
                       //   .Where(a => DB.SL_ReturnDetails.Where(v => v.SL_ReturnId == d.SL_ReturnId).ToList().Select(v => v.SL_ReturnDetailId).Contains(a.ReturnInvoiceDetailId)).ToList()
                       select new
                       {
                           InvoiceCode = d.ReturnCode,
                           InvoiceDate = d.ReturnDate,
                           d.DiscountRatio,
                           d.DiscountValue,
                           d.Expenses,
                           PayMethod = d.PayMethod.HasValue ? (d.PayMethod.Value == true ? "كاش" : "اجل") : "نقدي/اجل",
                           d.Net,
                           d.Paid,
                           d.Remains,
                           Customer = c.CusNameAr,
                           d.AddTaxValue,
                           d.DeductTaxValue,
                           d.TaxValue,
                           Total = TotalInvoice,
                           TotalTradeDiscount = TradeDiscount,
                           TotalBeforeTradeDisc = TotalInvoice + TradeDiscount,
                           Store=DB.IC_Stores.SingleOrDefault(s=>s.StoreId==d.StoreId).StoreNameAr,
                           d.CrncId,
                           d.CrncRate,
                           Net_Local = d.CrncRate * d.Net,
                           c.City,
                           Totaltaxes = TotaltaxesAddValue - TotaltaxesRemovedValue
                           //Totaltaxes = Totaltaxes.Count != 0 ? Convert.ToDouble(Totaltaxes.Sum(z => z.value)) : 0
                       };
            grdCategory.DataSource = data;
        }

        private void gridView1_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            if (e.ListSourceRowIndex < 0)
                return;

            if(e.Column.FieldName == "colIndex")
                e.Value = e.RowHandle() + 1;
        }

        bool LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.rpt_SL_ReturnHeaders).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            return true;
        }

        private void frm_SL_InvoiceList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                ReportsRTL.LTRLayout(this);

            ReportsUtils.Load_Grid_Layout(grdCategory, this.Name.Replace("frm_", "Rpt_"));
            //LoadPrivilege();
        }

        private void frm_Rep_FormClosing(object sender, FormClosingEventArgs e)
        {
            ReportsUtils.save_Grid_Layout(grdCategory, this.Name.Replace("frm_", "Rpt_"), true);
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Preview_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCategory, true).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        private void barBtnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadData();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grdCategory.MinimumSize = grdCategory.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grdCategory, true, true).ShowPreview();
                grdCategory.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                //lblCompName.Text = comp.CmpNameAr;
                lblReportName.Text = this.Text = reportName;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }
    }
}