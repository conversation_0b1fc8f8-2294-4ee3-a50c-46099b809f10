﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;

using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;

using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraReports.UI;

namespace Reports
{
    public partial class frm_SL_ItemsSales : DevExpress.XtraEditors.XtraForm
    {
        public bool UserCanOpen;
        string reportName, dateFilter, otherFilters;

        int itemId1, itemId2, store_id1, store_id2, companyId, salesEmpId;
        byte FltrTyp_item, FltrTyp_Store, FltrTyp_Company, FltrTyp_Category, fltrTyp_Date, FltrTyp_InvBook;
        DateTime date1, date2;
        string categoryNum;

        List<int> lstStores = new List<int>();

        List<int> lst_invBooksId = new List<int>();


        public frm_SL_ItemsSales(string reportName, string dateFilter, string otherFilters,
            byte fltrTyp_Store, int store_id1, int store_id2,
            byte fltrTyp_company, int companyId,
            byte fltrTyp_category, string categoryNum,
            byte fltrTyp_item, int itemId1, int itemId2,
            byte fltrTyp_Date, DateTime date1, DateTime date2,
            int salesEmpId, byte FltrTyp_InvBook, string InvBooks)
        {
            UserCanOpen = LoadPrivilege();
            if (UserCanOpen == false)
                return;

            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();

            this.reportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;

            this.FltrTyp_Store = fltrTyp_Store;
            this.FltrTyp_Company = fltrTyp_company;
            this.FltrTyp_Category = fltrTyp_category;
            this.FltrTyp_item = fltrTyp_item;
            this.fltrTyp_Date = fltrTyp_Date;

            this.itemId1 = itemId1;
            this.itemId2 = itemId2;
            this.store_id1 = store_id1;
            this.store_id2 = store_id2;
            this.companyId = companyId;
            this.categoryNum = categoryNum;
            this.salesEmpId = salesEmpId;

            this.date1 = date1.Date;
            this.date2 = date2.Date;

            this.FltrTyp_InvBook = FltrTyp_InvBook;
            Utilities.Get_ChkLst_Items(InvBooks, lst_invBooksId);

            getReportHeader();

            LoadData();

            col_CurrentPiecesCount.Visible = col_SoldPiecesCount.Visible = Shared.st_Store.PiecesCount;
            col_CurrentMQty.Visible = col_SoldMQty.Visible = Shared.st_Store.UseMediumUom;
            col_CurrentLQty.Visible = col_SoldLQty.Visible = Shared.st_Store.UseLargeUom;

            ReportsUtils.ColumnChooser(grdCategory);
        }

        void LoadData()
        {
            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var stores = DB.IC_Stores.ToList();
            foreach (var store in stores)
            {
                if (FltrTyp_Store == 2)
                {
                    if (store.StoreId <= store_id2 && store.StoreId >= store_id1)
                    {
                        lstStores.Add(store.StoreId);
                    }
                }
                else if (FltrTyp_Store == 0)
                {
                    lstStores.Add(store.StoreId);
                }
                else if (store_id1 > 0 && (store.StoreId == store_id1 || store.ParentId == store_id1))
                    lstStores.Add(store.StoreId);
                //else if (store_id2 > 0 && (store.StoreId == store_id2 || store.ParentId == store_id2))
                //    lstStores.Add(store.StoreId);
            }

            #region old
            //var data = (from d in DB.IC_ItemStores
            //            where FltrTyp_Store == 1 ? d.StoreId == store_id1 : true
            //            where (FltrTyp_Store == 2 && store_id1 != 0 && store_id2 != 0) ?
            //            d.StoreId >= store_id1 && d.StoreId <= store_id2 : true
            //            where (FltrTyp_Store == 2 && store_id1 != 0 && store_id2 == 0) ?
            //            d.StoreId >= store_id1 : true
            //            where (FltrTyp_Store == 2 && store_id1 == 0 && store_id2 != 0) ?
            //            d.StoreId <= store_id2 : true

            //            where fltrTyp_Date == 1 ? d.InsertTime.Date == date1 : true
            //            where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
            //            d.InsertTime >= date1 && d.InsertTime <= date2 : true
            //            where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
            //            d.InsertTime >= date1 : true
            //            where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
            //            d.InsertTime <= date2 : true

            //            where FltrTyp_item == 1 ? d.ItemId == itemId1 : true
            //            where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 != 0) ?
            //            d.ItemId >= itemId1 && d.ItemId <= itemId2 : true
            //            where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 == 0) ?
            //            d.ItemId >= itemId1 : true
            //            where (FltrTyp_item == 2 && itemId1 == 0 && itemId2 != 0) ?
            //            d.ItemId <= itemId2 : true

            //            group d by new { d.ItemId } into grp
            //            
            //            let Qty = (grp.Where(c => c.IsInTrns == true).Count() > 0 ? grp.Where(c => c.IsInTrns == true).Sum(x => x.Qty) : 0)
            //                 -
            //                (grp.Where(c => c.IsInTrns == false).Count() > 0 ? grp.Where(c => c.IsInTrns == false).Sum(x => x.Qty) : 0)
            //            let SellQty = grp.Where(c => c.ProcessId == (int)Process.SellInvoice || c.ProcessId == (int)Process.SL_Invoices_Posting).Count() > 0 ?
            //                         grp.Where(c => c.ProcessId == (int)Process.SellInvoice || c.ProcessId == (int)Process.SL_Invoices_Posting).Sum(x => x.Qty) : 0

            //            join t in DB.IC_Items
            //            on g.ItemId equals t.ItemId
            //            where FltrTyp_Company == 1 ? t.Company == companyId : true
            //            where FltrTyp_Category == 1 ? t.Category == categoryId : true
            //            where SellQty > 0
            //            select new { grp, t, SellQty, Qty }).AsEnumerable().
            //            Select(d => new
            //            {
            //                ItemId = d.grp.Key.ItemId,
            //                ItemNameAr = d.t.ItemNameAr,
            //                CurrentQty = d.Qty,
            //                SoldQty = d.SellQty,
            //                MediumUOMFactor = MyHelper.FractionToDouble(d.t.MediumUOMFactor),
            //                LargeUOMFactor = MyHelper.FractionToDouble(d.t.LargeUOMFactor),

            //                SUom = DB.IC_UOMs.Where(x => x.UOMId == d.t.SmallUOM).Select(x => x.UOM).FirstOrDefault(),
            //                MUom = DB.IC_UOMs.Where(x => x.UOMId == d.t.MediumUOM).Select(x => x.UOM).FirstOrDefault(),
            //                LUom = DB.IC_UOMs.Where(x => x.UOMId == d.t.LargeUOM).Select(x => x.UOM).FirstOrDefault(),
            //            }).Distinct().OrderByDescending(x => x.ItemId);

            //var non_stored_Items = (from d in DB.SL_InvoiceDetails
            //                        where FltrTyp_Store == 1 ? d.SL_Invoice.StoreId == store_id1 : true
            //                        where (FltrTyp_Store == 2 && store_id1 != 0 && store_id2 != 0) ?
            //                        d.SL_Invoice.StoreId >= store_id1 && d.SL_Invoice.StoreId <= store_id2 : true
            //                        where (FltrTyp_Store == 2 && store_id1 != 0 && store_id2 == 0) ?
            //                        d.SL_Invoice.StoreId >= store_id1 : true
            //                        where (FltrTyp_Store == 2 && store_id1 == 0 && store_id2 != 0) ?
            //                        d.SL_Invoice.StoreId <= store_id2 : true

            //                        where fltrTyp_Date == 1 ? d.SL_Invoice.InvoiceDate.Date == date1 : true
            //                        where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
            //                        d.SL_Invoice.InvoiceDate >= date1 && d.SL_Invoice.InvoiceDate <= date2 : true
            //                        where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
            //                        d.SL_Invoice.InvoiceDate >= date1 : true
            //                        where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
            //                        d.SL_Invoice.InvoiceDate <= date2 : true

            //                        where FltrTyp_item == 1 ? d.ItemId == itemId1 : true
            //                        where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 != 0) ?
            //                        d.ItemId >= itemId1 && d.ItemId <= itemId2 : true
            //                        where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 == 0) ?
            //                        d.ItemId >= itemId1 : true
            //                        where (FltrTyp_item == 2 && itemId1 == 0 && itemId2 != 0) ?
            //                        d.ItemId <= itemId2 : true

            //                        group d by d.ItemId into grp
            //                        
            //                        join t in DB.IC_Items
            //                        on g.ItemId equals t.ItemId
            //                        where t.ItemType == (int)ItemType.Service
            //                        where FltrTyp_Company == 1 ? t.Company == companyId : true
            //                        where FltrTyp_Category == 1 ? t.Category == categoryId : true                                    
            //                        select new { grp, t}).AsEnumerable().                                          
            //                        Select(d => new
            //                        {
            //                            ItemId = d.grp.Key,
            //                            ItemNameAr = d.t.ItemNameAr,
            //                            CurrentQty = (decimal)0,
            //                            SoldQty =  (from x in d.grp
            //                                       let uom_factor = x.UOMIndex == 0 ? 1 : x.UOMIndex == 1 ? MyHelper.FractionToDouble(d.t.MediumUOMFactor) : MyHelper.FractionToDouble(d.t.LargeUOMFactor)
            //                                       select new
            //                                       {
            //                                           qty = x.Qty * uom_factor
            //                                       }).Select(x => x.qty).Sum(),

            //                            MediumUOMFactor = MyHelper.FractionToDouble(d.t.MediumUOMFactor),
            //                            LargeUOMFactor = MyHelper.FractionToDouble(d.t.LargeUOMFactor),

            //                            SUom = DB.IC_UOMs.Where(x => x.UOMId == d.t.SmallUOM).Select(x => x.UOM).FirstOrDefault(),
            //                            MUom = DB.IC_UOMs.Where(x => x.UOMId == d.t.MediumUOM).Select(x => x.UOM).FirstOrDefault(),
            //                            LUom = DB.IC_UOMs.Where(x => x.UOMId == d.t.LargeUOM).Select(x => x.UOM).FirstOrDefault(),
            //                        }).Distinct().OrderByDescending(x => x.ItemId); 
            #endregion
            //var defaultCategories = DB.IC_User_Categories.Where(a => a.UserId == Shared.UserId).Select(a => a.CategoryId).ToList();
            var soldQty = (from d in DB.SL_InvoiceDetails
                           join s in DB.SL_Invoices
                           on d.SL_InvoiceId equals s.SL_InvoiceId

                           where salesEmpId == 0 ? true : s.SalesEmpId == salesEmpId
                           // where lstStores.Count > 0 ? lstStores.Contains(s.StoreId)  : true
                           //update
                           where lstStores.Count > 0 ? lstStores.Contains(s.StoreId) || lstStores.Contains(d.StoreId.Value) : true
                           where FltrTyp_InvBook == 0 ? true : (s.InvoiceBookId.HasValue && lst_invBooksId.Contains(s.InvoiceBookId.Value))

                           //where FltrTyp_Store == 1 ? s.StoreId == store_id1 : true
                           //where (FltrTyp_Store == 2 && store_id1 != 0 && store_id2 != 0) ?
                           //s.StoreId >= store_id1 && s.StoreId <= store_id2 : true
                           //where (FltrTyp_Store == 2 && store_id1 != 0 && store_id2 == 0) ?
                           //s.StoreId >= store_id1 : true
                           //where (FltrTyp_Store == 2 && store_id1 == 0 && store_id2 != 0) ?
                           //s.StoreId <= store_id2 : true

                           where FltrTyp_item == 1 ? d.ItemId == itemId1 : true
                           where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 != 0) ?
                           d.ItemId >= itemId1 && d.ItemId <= itemId2 : true
                           where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 == 0) ?
                           d.ItemId >= itemId1 : true
                           where (FltrTyp_item == 2 && itemId1 == 0 && itemId2 != 0) ?
                           d.ItemId <= itemId2 : true

                           where fltrTyp_Date == 1 ? s.InvoiceDate.Date == date1 : true
                           where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                           s.InvoiceDate.Date >= date1 && s.InvoiceDate.Date <= date2 : true
                           where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                           s.InvoiceDate.Date >= date1 : true
                           where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                           s.InvoiceDate.Date <= date2 : true

                           join t in DB.IC_Items
                           on d.ItemId equals t.ItemId
                           join c in DB.IC_Categories on t.Category equals c.CategoryId
                           where FltrTyp_Category == 1 ? c.CatNumber.StartsWith(categoryNum) : true
                           where FltrTyp_Company == 1 ? t.Company == companyId : true
                           //where defaultCategories.Count() > 0 ? defaultCategories.Contains(c.CategoryId) : true
                           select new
                           {
                               ItemId = d.ItemId,
                               SoldQty = (Shared.st_Store.MultiplyDimensions == (byte)Dimensions.DistinguishAndMultiply ||
                               Shared.st_Store.MultiplyDimensions == (byte)Dimensions.Multiply) ? (d.Width.Value * d.Length.Value * d.Height.Value * d.Qty) : d.Qty,
                               d.UOMIndex,
                               d.UOMId,
                               t.MediumUOMFactor,
                               t.LargeUOMFactor,
                               SoldPiecesCount = d.PiecesCount,
                               LibraQty = t.is_libra == true ? d.LibraQty : null,
                               kg_Weight_libra = d.kg_Weight_libra,
                               //Store = s.StoreId == 0 ? DB.IC_Stores.SingleOrDefault(c => c.StoreId == d.StoreId).StoreNameAr : DB.IC_Stores.SingleOrDefault(c => c.StoreId == s.StoreId).StoreNameAr

                           }).ToList();

            var storeQty = (from d in DB.IC_ItemStores

                            where lstStores.Count > 0 ? lstStores.Contains(d.StoreId) : true

                            //where FltrTyp_Store == 1 ? d.StoreId == store_id1 : true
                            //where (FltrTyp_Store == 2 && store_id1 != 0 && store_id2 != 0) ?
                            //d.StoreId >= store_id1 && d.StoreId <= store_id2 : true
                            //where (FltrTyp_Store == 2 && store_id1 != 0 && store_id2 == 0) ?
                            //d.StoreId >= store_id1 : true
                            //where (FltrTyp_Store == 2 && store_id1 == 0 && store_id2 != 0) ?
                            //d.StoreId <= store_id2 : true

                            where FltrTyp_item == 1 ? d.ItemId == itemId1 : true
                            where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 != 0) ?
                            d.ItemId >= itemId1 && d.ItemId <= itemId2 : true
                            where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 == 0) ?
                            d.ItemId >= itemId1 : true
                            where (FltrTyp_item == 2 && itemId1 == 0 && itemId2 != 0) ?
                            d.ItemId <= itemId2 : true

                            where fltrTyp_Date == 1 ? d.InsertTime.Date <= date1 : true
                            where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                            d.InsertTime.Date <= date2 : true
                            where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                            d.InsertTime.Date <= date2 : true

                            group d by d.ItemId into grp
                           // group d by new { d.ItemId, d.StoreId } into grp

                            join t in DB.IC_Items
                            on grp.Key equals t.ItemId
                          // on grp.Key.ItemId equals t.ItemId

                            join c in DB.IC_Categories on t.Category equals c.CategoryId
                            //where defaultCategories.Count() > 0 ? defaultCategories.Contains(c.CategoryId) : true
                            where FltrTyp_Category == 1 ? c.CatNumber.StartsWith(categoryNum) : true
                         
                            where FltrTyp_Company == 1 ? t.Company == companyId : true

                            select new
                            {
                                ItemId = grp.Key,
                              //  ItemId = grp.Key.ItemId,

                                CurrentQty = grp.Select(x => x.IsInTrns ? x.Qty : x.Qty * -1).Sum(),
                                // CurrentQty = grp.Where(x=>x.StoreId==grp.Key.StoreId).Select(x => x.IsInTrns ? x.Qty : x.Qty * -1).Sum(),

                                CurrentPiecesCount = grp.Select(x => x.IsInTrns ? x.PiecesCount : x.PiecesCount * -1).Sum(),

                               // Store = DB.IC_Stores.SingleOrDefault(c => c.StoreId == grp.Key.StoreId).StoreNameAr
                            }).ToList();

            var data1 = from d in soldQty
                        select new
                        {
                            ItemId = d.ItemId,
                            SoldQty = MyHelper.CalculateUomQty(d.SoldQty, d.UOMIndex, MyHelper.FractionToDouble(d.MediumUOMFactor),
                            MyHelper.FractionToDouble(d.LargeUOMFactor)),
                            d.SoldPiecesCount,
                           d.LibraQty,
                            d.kg_Weight_libra,
                            //Store=d.Store
                        };

            var data2 = (from d in data1
                             group d by  d.ItemId into grp
                         //group d by new { d.ItemId, d.Store } into grp

                         select new
                         {
                             ItemId = grp.Key,
                             //ItemId = grp.Key.ItemId,

                             SoldQty = grp.Sum(x => x.SoldQty),
                             SoldPiecesCount = grp.Sum(x => x.SoldPiecesCount),
                             LibraQty = grp.Sum(x => x.LibraQty),
                             kg_Weight_libra = grp.Sum(x => x.kg_Weight_libra),
                             //Store=grp.Key.Store
                         }).ToList();

            var data3 = (from d in data2
                         join t in DB.IC_Items
                         on d.ItemId equals t.ItemId
                         join c in DB.IC_Categories on t.Category equals c.CategoryId
                         //where defaultCategories.Count() > 0 ? defaultCategories.Contains(c.CategoryId) : true
                         join s in storeQty
                         // on d.ItemId equals s.ItemId into store
                         on d.ItemId equals s.ItemId into store

                         from st in store.DefaultIfEmpty()
                         let Category=Shared.IsEnglish? DB.IC_Categories.Where(a=>a.CategoryId==t.Category).FirstOrDefault().CategoryNameEn: DB.IC_Categories.Where(a => a.CategoryId == t.Category).FirstOrDefault().CategoryNameAr
                         select new
                         {
                             ItemId = t.ItemId,
                             t.ItemCode1,
                             t.ItemNameAr,
                          
                             SoldQty = d.SoldQty,
                             CurrentQty = (st == null ? 0 : st.CurrentQty),
                             t.MediumUOMFactor,
                             t.LargeUOMFactor,
                             SUom = DB.IC_UOMs.Where(x => x.UOMId == t.SmallUOM).Select(x => x.UOM).FirstOrDefault(),
                             MUom = Shared.st_Store.UseMediumUom ?
                               DB.IC_UOMs.Where(x => x.UOMId == t.MediumUOM).Select(x => x.UOM).FirstOrDefault() : string.Empty,
                             LUom = Shared.st_Store.UseLargeUom ?
                               DB.IC_UOMs.Where(x => x.UOMId == t.LargeUOM).Select(x => x.UOM).FirstOrDefault() : string.Empty,

                             CurrentLibra = (t.is_libra == true && st != null) ? ((double)Math.Round(st.CurrentQty, 3, MidpointRounding.AwayFromZero)).ToString() : string.Empty,
                             currentKG = st == null ? 0 : (t.is_libra == true ? (double)Math.Round(st.CurrentQty / MyHelper.FractionToDouble(t.LargeUOMFactor), 3, MidpointRounding.AwayFromZero) 
                                                                                : (double)st.CurrentQty),

                      
                             SoldPiecesCount = decimal.ToDouble(d.SoldPiecesCount),
                             LibraQty = decimal.ToDouble(d.LibraQty.Value),
                             kg_Weight_libra = decimal.ToDouble(d.kg_Weight_libra.Value),
                             CurrentPiecesCount = (st == null ? 0 : decimal.ToDouble(st.CurrentPiecesCount)),
                             t.Height,
                             t.Length,
                             t.Width,
                             Category
                             //Store= d.Store
                             // Store = st.Store

                         }).OrderBy(x => x.ItemCode1).ToList();

            grdCategory.DataSource = data3;
        }

        private void gridView1_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            if (e.ListSourceRowIndex < 0)
                return;
            if (e.Column.FieldName == "colIndex")
                e.Value = e.RowHandle() + 1;



            decimal Luomfactor = MyHelper.FractionToDouble(bandedGridView1.GetListSourceRowCellValue(e.ListSourceRowIndex, "LargeUOMFactor") + "");
            decimal Muomfactor = MyHelper.FractionToDouble(bandedGridView1.GetListSourceRowCellValue(e.ListSourceRowIndex, "MediumUOMFactor") + "");

            decimal SoldQty = Convert.ToDecimal(bandedGridView1.GetListSourceRowCellValue(e.ListSourceRowIndex, col_SoldQty));
            decimal CurrentQty = Convert.ToDecimal(bandedGridView1.GetListSourceRowCellValue(e.ListSourceRowIndex, col_CurrentQty));

            decimal sold_L_qty = SoldQty / Luomfactor,
                sold_M_qty = SoldQty / Muomfactor,
                sold_S_qty = SoldQty;

            decimal current_L_qty = CurrentQty / Luomfactor,
                current_M_qty = CurrentQty / Muomfactor,
                current_S_qty = CurrentQty;

            #region Old
            /*            
            if (Shared.st_Store.UseLargeUom == true)
            {
                if (Luomfactor > 1)
                {
                    sold_L_qty = Math.Truncate(SoldQty / Luomfactor);
                    current_L_qty = Math.Truncate(CurrentQty / Luomfactor);
                }
            }
            else
                sold_L_qty = current_L_qty = 0;

            SoldQty = SoldQty - sold_L_qty * Luomfactor;
            CurrentQty = CurrentQty - current_L_qty * Luomfactor;

            if (Shared.st_Store.UseMediumUom == true)
            {
                if (Muomfactor > 1)
                {
                    sold_M_qty = Math.Truncate(SoldQty / Muomfactor);
                    current_M_qty = Math.Truncate(CurrentQty / Muomfactor);
                }
            }
            else
                sold_M_qty = current_M_qty = 0;

            SoldQty = SoldQty - sold_M_qty * Muomfactor;
            CurrentQty = CurrentQty - current_M_qty * Muomfactor;

            sold_S_qty = SoldQty;
            current_S_qty = CurrentQty;

            */
            #endregion


            var LUnit = bandedGridView1.GetListSourceRowCellValue(e.ListSourceRowIndex, "LUom");
            var MUnit = bandedGridView1.GetListSourceRowCellValue(e.ListSourceRowIndex, "MUom");
            var SUnit = bandedGridView1.GetListSourceRowCellValue(e.ListSourceRowIndex, "SUom");

            if (e.Column == col_SoldSQty)
                e.Value = decimal.ToDouble(sold_S_qty);// +"\r\n" + SUnit;

            if (e.Column == col_SoldMQty && Muomfactor != 1)
                e.Value = decimal.ToDouble(sold_M_qty);//+ "\r\n" + MUnit;

            if (e.Column == col_SoldLQty && Luomfactor != 1)
                e.Value = decimal.ToDouble(sold_L_qty);//+ "\r\n" + LUnit;

            if (e.Column == col_CurrentSQty)
                e.Value = decimal.ToDouble(current_S_qty);// + "\r\n" + SUnit;

            if (e.Column == col_CurrentMQty && Muomfactor != 1)
                e.Value = decimal.ToDouble(current_M_qty);//+ "\r\n" + MUnit;

            if (e.Column == col_CurrentLQty && Luomfactor != 1)
                e.Value = decimal.ToDouble(current_L_qty);//+ "\r\n" + LUnit;
        }


        bool LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.rpt_SL_ItemsSales).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            return true;
        }


        private void frm_SL_InvoiceList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                ReportsRTL.LTRLayout(this);

            if (Shared.st_Store.UseLengthDimension)
                col_length.Visible = true;
            if (Shared.st_Store.UseHeightDimension)
                col_height.Visible = true;
            if (Shared.st_Store.UseWidthDimension)
                col_width.Visible = true;

            col_LibraQty.OptionsColumn.ShowInCustomizationForm = col_kg_Weight_libra.OptionsColumn.ShowInCustomizationForm = Shared.LibraAvailabe;
            col_SoldPiecesCount.Caption = Shared.IsEnglish ? Shared.st_Store.PiecesCountNameEn : Shared.st_Store.PiecesCountNameAr;
            col_CurrentPiecesCount.Caption = Shared.IsEnglish ? Shared.st_Store.PiecesCountNameEn : Shared.st_Store.PiecesCountNameAr;

            ReportsUtils.Load_Grid_Layout(grdCategory, this.Name.Replace("frm_", "Rpt_"));
            //LoadPrivilege();
        }

        private void frm_Rep_FormClosing(object sender, FormClosingEventArgs e)
        {
            ReportsUtils.save_Grid_Layout(grdCategory, this.Name.Replace("frm_", "Rpt_"), true);
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Preview_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCategory, false).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        private void barbtnPrint_P_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCategory, true).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        private void barBtnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadData();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grdCategory.MinimumSize = grdCategory.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grdCategory, false, true).ShowPreview();
                grdCategory.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void babtnPrintP_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grdCategory.MinimumSize = grdCategory.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grdCategory, true, true).ShowPreview();
                grdCategory.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                //lblCompName.Text = comp.CmpNameAr;
                lblReportName.Text = this.Text = reportName;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }
    }
}