﻿namespace Pharmacy.Forms
{
    partial class frm_SL_Invoice
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_SL_Invoice));
            DevExpress.Utils.SerializableAppearanceObject serializableAppearanceObject1 = new DevExpress.Utils.SerializableAppearanceObject();
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtnHelp = new DevExpress.XtraBars.BarButtonItem();
            this.btnAttachments = new DevExpress.XtraBars.BarButtonItem();
            this.barSubItemPrint = new DevExpress.XtraBars.BarSubItem();
            this.barbtnPrint = new DevExpress.XtraBars.BarButtonItem();
            this.barbtnPrintF = new DevExpress.XtraBars.BarButtonItem();
            this.barbtnPrintStore = new DevExpress.XtraBars.BarButtonItem();
            this.barbtnSamplesPr = new DevExpress.XtraBars.BarSubItem();
            this.barBtnDelete = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnNew = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnCancel = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnSave = new DevExpress.XtraBars.BarButtonItem();
            this.batBtnList = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.barBtnNotesReceivable = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnLoad_Sl_Qoute = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnLoad_SalesOrder = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnLoad_IC_OutTrns = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnLoad_JO = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnLoad_PR_Invoice = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnLoad_IC_Transfer = new DevExpress.XtraBars.BarButtonItem();
            this.barBtn_OutTrns = new DevExpress.XtraBars.BarButtonItem();
            this.barBtn_CashNote = new DevExpress.XtraBars.BarButtonItem();
            this.barbtnvisanote = new DevExpress.XtraBars.BarButtonItem();
            this.barbtn_EditPerm = new DevExpress.XtraBars.BarButtonItem();
            this.barbtn_DelPerm = new DevExpress.XtraBars.BarButtonItem();
            this.barbtn_PrintPerm = new DevExpress.XtraBars.BarButtonItem();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.txtInvoiceCode = new DevExpress.XtraEditors.TextEdit();
            this.btnPrevious = new DevExpress.XtraEditors.SimpleButton();
            this.btnNext = new DevExpress.XtraEditors.SimpleButton();
            this.dtInvoiceDate = new DevExpress.XtraEditors.DateEdit();
            this.labelControl35 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl36 = new DevExpress.XtraEditors.LabelControl();
            this.panelControl1 = new DevExpress.XtraEditors.PanelControl();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.lblShipTo = new DevExpress.XtraEditors.LabelControl();
            this.flowLayoutPanel1 = new System.Windows.Forms.FlowLayoutPanel();
            this.pnlBook = new System.Windows.Forms.Panel();
            this.txtTserial = new DevExpress.XtraEditors.TextEdit();
            this.lkp_InvoiceBook = new DevExpress.XtraEditors.LookUpEdit();
            this.pnlInvCode = new System.Windows.Forms.Panel();
            this.txtTinvCode = new DevExpress.XtraEditors.TextEdit();
            this.pnlDate = new System.Windows.Forms.Panel();
            this.txtTdate = new DevExpress.XtraEditors.TextEdit();
            this.pnlBranch = new System.Windows.Forms.Panel();
            this.txtTstore = new DevExpress.XtraEditors.TextEdit();
            this.lkpStore = new DevExpress.XtraEditors.LookUpEdit();
            this.pnlAgeDate = new System.Windows.Forms.Panel();
            this.txtTdueDate = new DevExpress.XtraEditors.TextEdit();
            this.txt_DueDays = new DevExpress.XtraEditors.SpinEdit();
            this.txt_DueDate = new DevExpress.XtraEditors.DateEdit();
            this.pnlDeliveryDate = new System.Windows.Forms.Panel();
            this.txtTdeliverDate = new DevExpress.XtraEditors.TextEdit();
            this.dtDeliverDate = new DevExpress.XtraEditors.DateEdit();
            this.pnlCurrency = new System.Windows.Forms.Panel();
            this.uc_Currency1 = new Pharmacy.Forms.uc_Currency();
            this.txtCurrency = new DevExpress.XtraEditors.TextEdit();
            this.pnlPostStore = new System.Windows.Forms.Panel();
            this.txt_Post_Date = new DevExpress.XtraEditors.DateEdit();
            this.chk_IsPosted = new DevExpress.XtraEditors.CheckEdit();
            this.pnlPO = new System.Windows.Forms.Panel();
            this.txtTpo = new DevExpress.XtraEditors.TextEdit();
            this.txt_PO_No = new DevExpress.XtraEditors.TextEdit();
            this.pnlSalesEmp = new System.Windows.Forms.Panel();
            this.txtTSalesEmp = new DevExpress.XtraEditors.TextEdit();
            this.lkp_SalesEmp = new DevExpress.XtraEditors.LookUpEdit();
            this.pnlCostCenter = new System.Windows.Forms.Panel();
            this.textEdit9 = new DevExpress.XtraEditors.TextEdit();
            this.lkpCostCenter = new DevExpress.XtraEditors.GridLookUpEdit();
            this.gv_CostCenter = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn43 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn42 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn40 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.txtNotes = new DevExpress.XtraEditors.MemoEdit();
            this.txt_Shipping = new DevExpress.XtraEditors.MemoEdit();
            this.chk_IsOutTrns = new DevExpress.XtraEditors.CheckEdit();
            this.cmbPayMethod = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.lkp_Drawers = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl17 = new DevExpress.XtraEditors.LabelControl();
            this.lbl_remains = new DevExpress.XtraEditors.LabelControl();
            this.txt_Remains = new DevExpress.XtraEditors.TextEdit();
            this.lbl_Paid = new DevExpress.XtraEditors.LabelControl();
            this.txt_paid = new DevExpress.XtraEditors.TextEdit();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.lkp_Customers = new DevExpress.XtraEditors.GridLookUpEdit();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn26 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn27 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn30 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn35 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.panelControl2 = new DevExpress.XtraEditors.PanelControl();
            this.grdPrInvoice = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.mi_frm_IC_Item = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_CustLastPrices = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_LastPrices = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_PasteRows = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_ExportData = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_InvoiceStaticDisc = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_InvoiceStaticDimensions = new System.Windows.Forms.ToolStripMenuItem();
            this.mi_ImportExcel = new System.Windows.Forms.ToolStripMenuItem();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.DiscountTaxRatio = new DevExpress.XtraGrid.Columns.GridColumn();
            this.DiscountTax = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colETaxValue = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repSpin = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.colETaxRatio = new DevExpress.XtraGrid.Columns.GridColumn();
            this.Col_ETaxType = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repTaxTypes = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView9 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.ColDescriptionAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.ColCode = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colE_TaxableTypeId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colBonusDiscount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.Col_Company = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repCompany = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView8 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.ColCompanyNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.Col_CompanyCode = new DevExpress.XtraGrid.Columns.GridColumn();
            this.CompanyId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.Col_Category = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repCategory = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.repositoryItemGridLookUpEdit2View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.ColCatNumber = new DevExpress.XtraGrid.Columns.GridColumn();
            this.ColCategoryNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.ColCategoryId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_QC = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ActualPiecesCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn29 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn28 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_TotalSellPrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CurrentQty = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CommercialDiscountValue = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repDiscountRatio = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.col_SellPrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_AudiencePrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colPurchasePrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repUOM = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView4 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ItemNameF = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repItems = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.repositoryItemGridLookUpEdit1View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn25 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_SellDiscountRatio = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CompanyNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CategoryNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.ColLength = new DevExpress.XtraGrid.Columns.GridColumn();
            this.ColWidth = new DevExpress.XtraGrid.Columns.GridColumn();
            this.ColHeight = new DevExpress.XtraGrid.Columns.GridColumn();
            this.grdcol_branch = new DevExpress.XtraGrid.Columns.GridColumn();
            this.lkp_storee = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView7 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn31 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn41 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Expire = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_expireDate = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView5 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn24 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Batch = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_Batch = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView6 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn33 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn34 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Length = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Width = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Height = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_TotalQty = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_PiecesCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repspin_PiecesCount = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.col_ItemDescription = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ItemDescriptionEn = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_SalesTax = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_DiscountRatio2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_DiscountRatio3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Serial = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Serial2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn32 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repManufactureDate = new DevExpress.XtraEditors.Repository.RepositoryItemDateEdit();
            this.col_CusTax = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Location = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Libra = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_Libra = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.gridColumn36 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn37 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn38 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_kg_Weight_libra = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn39 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.ol_Index = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_itemperoffer = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Pack = new DevExpress.XtraGrid.Columns.GridColumn();
            this.btn_AddTaxes = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_btnAddTaxes = new DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit();
            this.TotalTaxes = new DevExpress.XtraGrid.Columns.GridColumn();
            this.totalTaxesRatio = new DevExpress.XtraGrid.Columns.GridColumn();
            this.salePriceWithTaxTable = new DevExpress.XtraGrid.Columns.GridColumn();
            this.totalTableTaxes = new DevExpress.XtraGrid.Columns.GridColumn();
            this.addTaxValue = new DevExpress.XtraGrid.Columns.GridColumn();
            this.tableTaxValue = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_TaxValue = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_TotalSubCustomTax = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_TotalSubAddTax = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_TotalSubDiscountTax = new DevExpress.XtraGrid.Columns.GridColumn();
            this.lkp_store = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.repositoryItemSpinEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.rep_Location = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.txtNet = new DevExpress.XtraEditors.TextEdit();
            this.labelControl13 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.txt_Total = new DevExpress.XtraEditors.TextEdit();
            this.labelControl18 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl19 = new DevExpress.XtraEditors.LabelControl();
            this.btnAddCustomer = new DevExpress.XtraEditors.SimpleButton();
            this.grdLastPrices = new DevExpress.XtraGrid.GridControl();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colTotalPurchasePrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colUOM = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colQty = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colCustNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colInvoiceDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colInvoiceCode = new DevExpress.XtraGrid.Columns.GridColumn();
            this.txtExpenses = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl20 = new DevExpress.XtraEditors.LabelControl();
            this.txt_Balance_After = new DevExpress.XtraEditors.LabelControl();
            this.txt_Balance_Before = new DevExpress.XtraEditors.LabelControl();
            this.txt_MaxCredit = new DevExpress.XtraEditors.LabelControl();
            this.lbl_IsCredit_After = new DevExpress.XtraEditors.LabelControl();
            this.lblBlncAftr = new DevExpress.XtraEditors.LabelControl();
            this.lbl_IsCredit_Before = new DevExpress.XtraEditors.LabelControl();
            this.labelControl24 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl10 = new DevExpress.XtraEditors.LabelControl();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.lkp_Drawers2 = new DevExpress.XtraEditors.LookUpEdit();
            this.txt_PayAcc1_Paid = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl30 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl31 = new DevExpress.XtraEditors.LabelControl();
            this.txt_PayAcc2_Paid = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl33 = new DevExpress.XtraEditors.LabelControl();
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.page_AccInfo = new DevExpress.XtraTab.XtraTabPage();
            this.btn_ShowAccStatement = new DevExpress.XtraEditors.SimpleButton();
            this.lbl_Validate_MaxLimit = new DevExpress.XtraEditors.LabelControl();
            this.page_JobOrder = new DevExpress.XtraTab.XtraTabPage();
            this.Page_LastPrices = new DevExpress.XtraTab.XtraTabPage();
            this.tabExtraData = new DevExpress.XtraTab.XtraTabPage();
            this.lkp_Cars = new DevExpress.XtraEditors.LookUpEdit();
            this.txtScaleSerial = new DevExpress.XtraEditors.TextEdit();
            this.labelControl26 = new DevExpress.XtraEditors.LabelControl();
            this.txtDestination = new DevExpress.XtraEditors.TextEdit();
            this.lblDestination = new DevExpress.XtraEditors.LabelControl();
            this.txtVehicleNumber = new DevExpress.XtraEditors.TextEdit();
            this.lblVehicleNumber = new DevExpress.XtraEditors.LabelControl();
            this.txtDriverName = new DevExpress.XtraEditors.TextEdit();
            this.lblDriverName = new DevExpress.XtraEditors.LabelControl();
            this.tabpg_CustomerData = new DevExpress.XtraTab.XtraTabPage();
            this.labelControl54 = new DevExpress.XtraEditors.LabelControl();
            this.lkpDelivery = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl52 = new DevExpress.XtraEditors.LabelControl();
            this.txt_Sales = new DevExpress.XtraEditors.LookUpEdit();
            this.txt_Address = new DevExpress.XtraEditors.TextEdit();
            this.labelControl51 = new DevExpress.XtraEditors.LabelControl();
            this.txt_Phone = new DevExpress.XtraEditors.TextEdit();
            this.labelControl48 = new DevExpress.XtraEditors.LabelControl();
            this.txt_Mobile = new DevExpress.XtraEditors.TextEdit();
            this.labelControl47 = new DevExpress.XtraEditors.LabelControl();
            this.textEdit3 = new DevExpress.XtraEditors.TextEdit();
            this.lkp_JOStatus = new DevExpress.XtraEditors.LookUpEdit();
            this.textEdit1 = new DevExpress.XtraEditors.TextEdit();
            this.lkp_JOPriority = new DevExpress.XtraEditors.LookUpEdit();
            this.textEdit5 = new DevExpress.XtraEditors.TextEdit();
            this.lkp_JOSalesEmp = new DevExpress.XtraEditors.LookUpEdit();
            this.textEdit8 = new DevExpress.XtraEditors.TextEdit();
            this.lkp_JODept = new DevExpress.XtraEditors.LookUpEdit();
            this.textEdit4 = new DevExpress.XtraEditors.TextEdit();
            this.txt_JOJob = new DevExpress.XtraEditors.TextEdit();
            this.textEdit2 = new DevExpress.XtraEditors.TextEdit();
            this.txt_JODeliveryDate = new DevExpress.XtraEditors.DateEdit();
            this.textEdit7 = new DevExpress.XtraEditors.TextEdit();
            this.textEdit6 = new DevExpress.XtraEditors.TextEdit();
            this.txt_JORegDate = new DevExpress.XtraEditors.DateEdit();
            this.txt_JOCode = new DevExpress.XtraEditors.TextEdit();
            this.txt_AttnMr = new DevExpress.XtraEditors.TextEdit();
            this.labelControl41 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl12 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl14 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl15 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl16 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl21 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl22 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl23 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl25 = new DevExpress.XtraEditors.LabelControl();
            this.txtDiscountRatio = new DevExpress.XtraEditors.SpinEdit();
            this.txtDiscountValue = new DevExpress.XtraEditors.SpinEdit();
            this.txt_TaxValue = new DevExpress.XtraEditors.SpinEdit();
            this.txt_DeductTaxR = new DevExpress.XtraEditors.SpinEdit();
            this.txt_DeductTaxV = new DevExpress.XtraEditors.SpinEdit();
            this.txt_AddTaxR = new DevExpress.XtraEditors.SpinEdit();
            this.txt_AddTaxV = new DevExpress.XtraEditors.SpinEdit();
            this.txtExpensesR = new DevExpress.XtraEditors.SpinEdit();
            this.chk_Offer = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl27 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl28 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl32 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl34 = new DevExpress.XtraEditors.LabelControl();
            this.txt_CusTaxR = new DevExpress.XtraEditors.SpinEdit();
            this.txt_CusTaxV = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl37 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl38 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl39 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl42 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl43 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl44 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl45 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl46 = new DevExpress.XtraEditors.LabelControl();
            this.txt_retentionR = new DevExpress.XtraEditors.SpinEdit();
            this.txt_RetentionV = new DevExpress.XtraEditors.SpinEdit();
            this.txt_AdvancePayR = new DevExpress.XtraEditors.SpinEdit();
            this.txt_AdvancePayV = new DevExpress.XtraEditors.SpinEdit();
            this.grp_ExtraRevenues = new DevExpress.XtraEditors.GroupControl();
            this.btnCeil = new DevExpress.XtraEditors.SimpleButton();
            this.txt_ShiftAdd = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl53 = new DevExpress.XtraEditors.LabelControl();
            this.txt_transportation = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl49 = new DevExpress.XtraEditors.LabelControl();
            this.txt_Handing = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl50 = new DevExpress.XtraEditors.LabelControl();
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            this.chk_Approved = new DevExpress.XtraEditors.CheckEdit();
            this.bookId = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.labelControl55 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl29 = new DevExpress.XtraEditors.LabelControl();
            this.txt_EtaxValue = new DevExpress.XtraEditors.SpinEdit();
            this.txt_Subtotal = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl40 = new DevExpress.XtraEditors.LabelControl();
            this.txt_SubAfterTax = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl56 = new DevExpress.XtraEditors.LabelControl();
            this.txt_total_b4_Discounts = new DevExpress.XtraEditors.TextEdit();
            this.lbl_total_b4_Discounts = new DevExpress.XtraEditors.LabelControl();
            this.txt_CommercialDiscounts = new DevExpress.XtraEditors.TextEdit();
            this.lbl_CommercialDiscounts = new DevExpress.XtraEditors.LabelControl();
            this.lbl_totalAfterCommercial_Disc = new DevExpress.XtraEditors.LabelControl();
            this.txt_totalAfterCommercial_Disc = new DevExpress.XtraEditors.TextEdit();
            this.grd_SubTaxes = new DevExpress.XtraGrid.GridControl();
            this.gv_SubTaxes = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.Value = new DevExpress.XtraGrid.Columns.GridColumn();
            this.SubTaxId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.lkp_SubTaxes = new DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit();
            this.col_Rate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.labelControl57 = new DevExpress.XtraEditors.LabelControl();
            this.txt_bounsDiscount = new DevExpress.XtraEditors.SpinEdit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtInvoiceCode.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtInvoiceDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtInvoiceDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).BeginInit();
            this.panelControl1.SuspendLayout();
            this.flowLayoutPanel1.SuspendLayout();
            this.pnlBook.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtTserial.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_InvoiceBook.Properties)).BeginInit();
            this.pnlInvCode.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtTinvCode.Properties)).BeginInit();
            this.pnlDate.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtTdate.Properties)).BeginInit();
            this.pnlBranch.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtTstore.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpStore.Properties)).BeginInit();
            this.pnlAgeDate.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtTdueDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DueDays.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DueDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DueDate.Properties)).BeginInit();
            this.pnlDeliveryDate.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtTdeliverDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtDeliverDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtDeliverDate.Properties)).BeginInit();
            this.pnlCurrency.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtCurrency.Properties)).BeginInit();
            this.pnlPostStore.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Post_Date.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Post_Date.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_IsPosted.Properties)).BeginInit();
            this.pnlPO.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtTpo.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_PO_No.Properties)).BeginInit();
            this.pnlSalesEmp.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtTSalesEmp.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_SalesEmp.Properties)).BeginInit();
            this.pnlCostCenter.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit9.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpCostCenter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv_CostCenter)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNotes.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Shipping.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_IsOutTrns.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbPayMethod.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Drawers.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Remains.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_paid.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Customers.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).BeginInit();
            this.panelControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.grdPrInvoice)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repSpin)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repTaxTypes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView9)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repCompany)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repCategory)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit2View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repDiscountRatio)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repUOM)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repItems)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_storee)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_expireDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Batch)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repspin_PiecesCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repManufactureDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repManufactureDate.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Libra)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_btnAddTaxes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_store)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSpinEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Location)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNet.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Total.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdLastPrices)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtExpenses.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Drawers2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_PayAcc1_Paid.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_PayAcc2_Paid.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.page_AccInfo.SuspendLayout();
            this.Page_LastPrices.SuspendLayout();
            this.tabExtraData.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Cars.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtScaleSerial.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDestination.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtVehicleNumber.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDriverName.Properties)).BeginInit();
            this.tabpg_CustomerData.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lkpDelivery.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Sales.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Address.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Phone.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Mobile.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit3.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_JOStatus.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_JOPriority.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit5.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_JOSalesEmp.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit8.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_JODept.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit4.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_JOJob.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_JODeliveryDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_JODeliveryDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit7.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit6.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_JORegDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_JORegDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_JOCode.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_AttnMr.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscountRatio.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscountValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_TaxValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DeductTaxR.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DeductTaxV.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_AddTaxR.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_AddTaxV.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtExpensesR.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_Offer.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_CusTaxR.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_CusTaxV.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_retentionR.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_RetentionV.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_AdvancePayR.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_AdvancePayV.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grp_ExtraRevenues)).BeginInit();
            this.grp_ExtraRevenues.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txt_ShiftAdd.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_transportation.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Handing.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_Approved.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.bookId.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_EtaxValue.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Subtotal.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_SubAfterTax.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_total_b4_Discounts.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_CommercialDiscounts.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_totalAfterCommercial_Disc.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grd_SubTaxes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv_SubTaxes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_SubTaxes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_bounsDiscount.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnSave,
            this.barBtnClose,
            this.barBtnHelp,
            this.batBtnList,
            this.barBtnNew,
            this.barBtnCancel,
            this.barBtnNotesReceivable,
            this.barSubItemPrint,
            this.barbtnPrint,
            this.barbtnPrintF,
            this.barBtnLoad_Sl_Qoute,
            this.barBtnLoad_SalesOrder,
            this.barBtnLoad_IC_OutTrns,
            this.barBtnLoad_JO,
            this.barBtnLoad_PR_Invoice,
            this.barBtnLoad_IC_Transfer,
            this.barBtn_OutTrns,
            this.barBtn_CashNote,
            this.btnAttachments,
            this.barbtnvisanote,
            this.barbtnPrintStore,
            this.barbtnSamplesPr,
            this.barbtn_EditPerm,
            this.barbtn_DelPerm,
            this.barbtn_PrintPerm,
            this.barBtnDelete});
            this.barManager1.MaxItemId = 79;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(405, 138);
            this.bar1.FloatSize = new System.Drawing.Size(702, 31);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnHelp),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.btnAttachments, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(this.barSubItemPrint),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barBtnDelete, "", true, true, true, 0, null, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnNew),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnCancel),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnSave),
            new DevExpress.XtraBars.LinkPersistInfo(this.batBtnList),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtnHelp
            // 
            this.barBtnHelp.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnHelp, "barBtnHelp");
            this.barBtnHelp.Id = 2;
            this.barBtnHelp.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtnHelp.Name = "barBtnHelp";
            this.barBtnHelp.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnHelp.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.btnCopy_ItemClick);
            // 
            // btnAttachments
            // 
            this.btnAttachments.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.btnAttachments, "btnAttachments");
            this.btnAttachments.Glyph = ((System.Drawing.Image)(resources.GetObject("btnAttachments.Glyph")));
            this.btnAttachments.Id = 73;
            this.btnAttachments.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("btnAttachments.LargeGlyph")));
            this.btnAttachments.Name = "btnAttachments";
            // 
            // barSubItemPrint
            // 
            this.barSubItemPrint.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barSubItemPrint, "barSubItemPrint");
            this.barSubItemPrint.Glyph = global::Pharmacy.Properties.Resources.prnt;
            this.barSubItemPrint.Id = 32;
            this.barSubItemPrint.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barbtnPrint),
            new DevExpress.XtraBars.LinkPersistInfo(this.barbtnPrintF),
            new DevExpress.XtraBars.LinkPersistInfo(this.barbtnPrintStore),
            new DevExpress.XtraBars.LinkPersistInfo(this.barbtnSamplesPr)});
            this.barSubItemPrint.Name = "barSubItemPrint";
            this.barSubItemPrint.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            // 
            // barbtnPrint
            // 
            resources.ApplyResources(this.barbtnPrint, "barbtnPrint");
            this.barbtnPrint.Id = 33;
            this.barbtnPrint.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.P));
            this.barbtnPrint.Name = "barbtnPrint";
            this.barbtnPrint.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnPrint_ItemClick);
            // 
            // barbtnPrintF
            // 
            resources.ApplyResources(this.barbtnPrintF, "barbtnPrintF");
            this.barbtnPrintF.Id = 34;
            this.barbtnPrintF.Name = "barbtnPrintF";
            this.barbtnPrintF.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            this.barbtnPrintF.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnPrint_F_ItemClick);
            // 
            // barbtnPrintStore
            // 
            resources.ApplyResources(this.barbtnPrintStore, "barbtnPrintStore");
            this.barbtnPrintStore.Id = 48;
            this.barbtnPrintStore.Name = "barbtnPrintStore";
            this.barbtnPrintStore.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barbtnPrintStore_ItemClick);
            // 
            // barbtnSamplesPr
            // 
            resources.ApplyResources(this.barbtnSamplesPr, "barbtnSamplesPr");
            this.barbtnSamplesPr.Id = 50;
            this.barbtnSamplesPr.Name = "barbtnSamplesPr";
            // 
            // barBtnDelete
            // 
            this.barBtnDelete.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnDelete, "barBtnDelete");
            this.barBtnDelete.Glyph = ((System.Drawing.Image)(resources.GetObject("barBtnDelete.Glyph")));
            this.barBtnDelete.Id = 78;
            this.barBtnDelete.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("barBtnDelete.LargeGlyph")));
            this.barBtnDelete.Name = "barBtnDelete";
            this.barBtnDelete.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnDelete_ItemClick);
            // 
            // barBtnNew
            // 
            this.barBtnNew.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnNew, "barBtnNew");
            this.barBtnNew.Glyph = global::Pharmacy.Properties.Resources._new;
            this.barBtnNew.Id = 26;
            this.barBtnNew.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.N));
            this.barBtnNew.Name = "barBtnNew";
            this.barBtnNew.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnNew.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnNew_ItemClick);
            // 
            // barBtnCancel
            // 
            this.barBtnCancel.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnCancel, "barBtnCancel");
            this.barBtnCancel.Glyph = ((System.Drawing.Image)(resources.GetObject("barBtnCancel.Glyph")));
            this.barBtnCancel.Id = 28;
            this.barBtnCancel.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.D));
            this.barBtnCancel.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("barBtnCancel.LargeGlyph")));
            this.barBtnCancel.Name = "barBtnCancel";
            this.barBtnCancel.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnCancel.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnCancel_ItemClick);
            // 
            // barBtnSave
            // 
            this.barBtnSave.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnSave, "barBtnSave");
            this.barBtnSave.Glyph = global::Pharmacy.Properties.Resources.sve;
            this.barBtnSave.Id = 0;
            this.barBtnSave.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.S));
            this.barBtnSave.Name = "barBtnSave";
            this.barBtnSave.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnSave.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Save_ItemClick);
            // 
            // batBtnList
            // 
            this.batBtnList.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.batBtnList, "batBtnList");
            this.batBtnList.Glyph = global::Pharmacy.Properties.Resources.list32;
            this.batBtnList.Id = 25;
            this.batBtnList.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.T));
            this.batBtnList.Name = "batBtnList";
            this.batBtnList.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.batBtnList.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.batBtnList_ItemClick);
            // 
            // barBtnClose
            // 
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 1;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnClose_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.BarAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.Dock.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.Dock.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.MainMenuAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.StatusBarAppearance.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.AppearanceMenu.Normal.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuBar.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.MenuCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStrip.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesBar.SubMenu.SideStripNonRecent.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.Panel.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.Panel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesDocking.PanelCaption.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesDocking.PanelCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesRibbon.Item.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesRibbon.Item.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.AppearancesRibbon.PageHeader.Options.UseTextOptions = true;
            this.barAndDockingController1.AppearancesRibbon.PageHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.Appearance.Options.UseTextOptions = true;
            this.barDockControlTop.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.barDockControlTop.CausesValidation = false;
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.CausesValidation = false;
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.CausesValidation = false;
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.CausesValidation = false;
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            // 
            // barBtnNotesReceivable
            // 
            resources.ApplyResources(this.barBtnNotesReceivable, "barBtnNotesReceivable");
            this.barBtnNotesReceivable.Id = 64;
            this.barBtnNotesReceivable.Name = "barBtnNotesReceivable";
            // 
            // barBtnLoad_Sl_Qoute
            // 
            resources.ApplyResources(this.barBtnLoad_Sl_Qoute, "barBtnLoad_Sl_Qoute");
            this.barBtnLoad_Sl_Qoute.Id = 65;
            this.barBtnLoad_Sl_Qoute.Name = "barBtnLoad_Sl_Qoute";
            // 
            // barBtnLoad_SalesOrder
            // 
            resources.ApplyResources(this.barBtnLoad_SalesOrder, "barBtnLoad_SalesOrder");
            this.barBtnLoad_SalesOrder.Id = 66;
            this.barBtnLoad_SalesOrder.Name = "barBtnLoad_SalesOrder";
            // 
            // barBtnLoad_IC_OutTrns
            // 
            resources.ApplyResources(this.barBtnLoad_IC_OutTrns, "barBtnLoad_IC_OutTrns");
            this.barBtnLoad_IC_OutTrns.Id = 67;
            this.barBtnLoad_IC_OutTrns.Name = "barBtnLoad_IC_OutTrns";
            // 
            // barBtnLoad_JO
            // 
            resources.ApplyResources(this.barBtnLoad_JO, "barBtnLoad_JO");
            this.barBtnLoad_JO.Id = 68;
            this.barBtnLoad_JO.Name = "barBtnLoad_JO";
            // 
            // barBtnLoad_PR_Invoice
            // 
            resources.ApplyResources(this.barBtnLoad_PR_Invoice, "barBtnLoad_PR_Invoice");
            this.barBtnLoad_PR_Invoice.Id = 69;
            this.barBtnLoad_PR_Invoice.Name = "barBtnLoad_PR_Invoice";
            // 
            // barBtnLoad_IC_Transfer
            // 
            resources.ApplyResources(this.barBtnLoad_IC_Transfer, "barBtnLoad_IC_Transfer");
            this.barBtnLoad_IC_Transfer.Id = 70;
            this.barBtnLoad_IC_Transfer.Name = "barBtnLoad_IC_Transfer";
            // 
            // barBtn_OutTrns
            // 
            resources.ApplyResources(this.barBtn_OutTrns, "barBtn_OutTrns");
            this.barBtn_OutTrns.Id = 71;
            this.barBtn_OutTrns.Name = "barBtn_OutTrns";
            // 
            // barBtn_CashNote
            // 
            resources.ApplyResources(this.barBtn_CashNote, "barBtn_CashNote");
            this.barBtn_CashNote.Id = 72;
            this.barBtn_CashNote.Name = "barBtn_CashNote";
            // 
            // barbtnvisanote
            // 
            resources.ApplyResources(this.barbtnvisanote, "barbtnvisanote");
            this.barbtnvisanote.Id = 74;
            this.barbtnvisanote.Name = "barbtnvisanote";
            // 
            // barbtn_EditPerm
            // 
            resources.ApplyResources(this.barbtn_EditPerm, "barbtn_EditPerm");
            this.barbtn_EditPerm.Id = 75;
            this.barbtn_EditPerm.Name = "barbtn_EditPerm";
            // 
            // barbtn_DelPerm
            // 
            resources.ApplyResources(this.barbtn_DelPerm, "barbtn_DelPerm");
            this.barbtn_DelPerm.Id = 76;
            this.barbtn_DelPerm.Name = "barbtn_DelPerm";
            // 
            // barbtn_PrintPerm
            // 
            resources.ApplyResources(this.barbtn_PrintPerm, "barbtn_PrintPerm");
            this.barbtn_PrintPerm.Id = 77;
            this.barbtn_PrintPerm.Name = "barbtn_PrintPerm";
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // txtInvoiceCode
            // 
            resources.ApplyResources(this.txtInvoiceCode, "txtInvoiceCode");
            this.txtInvoiceCode.EnterMoveNextControl = true;
            this.txtInvoiceCode.Name = "txtInvoiceCode";
            this.txtInvoiceCode.Properties.Appearance.Options.UseTextOptions = true;
            this.txtInvoiceCode.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtInvoiceCode.Properties.AutoHeight = ((bool)(resources.GetObject("txtInvoiceCode.Properties.AutoHeight")));
            this.txtInvoiceCode.Properties.Mask.EditMask = resources.GetString("txtInvoiceCode.Properties.Mask.EditMask");
            this.txtInvoiceCode.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtInvoiceCode.Properties.Mask.IgnoreMaskBlank")));
            this.txtInvoiceCode.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtInvoiceCode.Properties.Mask.SaveLiteral")));
            this.txtInvoiceCode.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtInvoiceCode.Properties.Mask.ShowPlaceHolders")));
            this.txtInvoiceCode.Properties.NullValuePrompt = resources.GetString("txtInvoiceCode.Properties.NullValuePrompt");
            this.txtInvoiceCode.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // btnPrevious
            // 
            this.btnPrevious.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnPrevious.Image = global::Pharmacy.Properties.Resources.prev32;
            resources.ApplyResources(this.btnPrevious, "btnPrevious");
            this.btnPrevious.Name = "btnPrevious";
            this.btnPrevious.TabStop = false;
            this.btnPrevious.Click += new System.EventHandler(this.btnPrevious_Click);
            // 
            // btnNext
            // 
            this.btnNext.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnNext.Image = global::Pharmacy.Properties.Resources.nxt;
            resources.ApplyResources(this.btnNext, "btnNext");
            this.btnNext.Name = "btnNext";
            this.btnNext.TabStop = false;
            this.btnNext.Click += new System.EventHandler(this.btnNext_Click);
            // 
            // dtInvoiceDate
            // 
            resources.ApplyResources(this.dtInvoiceDate, "dtInvoiceDate");
            this.dtInvoiceDate.EnterMoveNextControl = true;
            this.dtInvoiceDate.MenuManager = this.barManager1;
            this.dtInvoiceDate.Name = "dtInvoiceDate";
            this.dtInvoiceDate.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.dtInvoiceDate.Properties.Appearance.Options.UseTextOptions = true;
            this.dtInvoiceDate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.dtInvoiceDate.Properties.AutoHeight = ((bool)(resources.GetObject("dtInvoiceDate.Properties.AutoHeight")));
            this.dtInvoiceDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("dtInvoiceDate.Properties.Buttons"))))});
            this.dtInvoiceDate.Properties.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.AutoHeight")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.EditMask = resources.GetString("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.EditMask");
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.MaskType")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.SaveLiteral")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.NullValuePrompt = resources.GetString("dtInvoiceDate.Properties.CalendarTimeProperties.NullValuePrompt");
            this.dtInvoiceDate.Properties.DisplayFormat.FormatString = "g";
            this.dtInvoiceDate.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.dtInvoiceDate.Properties.EditFormat.FormatString = "g";
            this.dtInvoiceDate.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.dtInvoiceDate.Properties.Mask.EditMask = resources.GetString("dtInvoiceDate.Properties.Mask.EditMask");
            this.dtInvoiceDate.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtInvoiceDate.Properties.Mask.IgnoreMaskBlank")));
            this.dtInvoiceDate.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtInvoiceDate.Properties.Mask.MaskType")));
            this.dtInvoiceDate.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtInvoiceDate.Properties.Mask.SaveLiteral")));
            this.dtInvoiceDate.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtInvoiceDate.Properties.Mask.ShowPlaceHolders")));
            this.dtInvoiceDate.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtInvoiceDate.Properties.Mask.UseMaskAsDisplayFormat")));
            this.dtInvoiceDate.Properties.NullValuePrompt = resources.GetString("dtInvoiceDate.Properties.NullValuePrompt");
            this.dtInvoiceDate.EditValueChanged += new System.EventHandler(this.dtInvoiceDate_EditValueChanged);
            this.dtInvoiceDate.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // labelControl35
            // 
            resources.ApplyResources(this.labelControl35, "labelControl35");
            this.labelControl35.Name = "labelControl35";
            // 
            // labelControl36
            // 
            resources.ApplyResources(this.labelControl36, "labelControl36");
            this.labelControl36.Name = "labelControl36";
            // 
            // panelControl1
            // 
            resources.ApplyResources(this.panelControl1, "panelControl1");
            this.panelControl1.Controls.Add(this.labelControl4);
            this.panelControl1.Controls.Add(this.lblShipTo);
            this.panelControl1.Controls.Add(this.flowLayoutPanel1);
            this.panelControl1.Controls.Add(this.txtNotes);
            this.panelControl1.Controls.Add(this.txt_Shipping);
            this.panelControl1.Name = "panelControl1";
            // 
            // labelControl4
            // 
            resources.ApplyResources(this.labelControl4, "labelControl4");
            this.labelControl4.Name = "labelControl4";
            // 
            // lblShipTo
            // 
            resources.ApplyResources(this.lblShipTo, "lblShipTo");
            this.lblShipTo.Name = "lblShipTo";
            // 
            // flowLayoutPanel1
            // 
            resources.ApplyResources(this.flowLayoutPanel1, "flowLayoutPanel1");
            this.flowLayoutPanel1.Controls.Add(this.pnlBook);
            this.flowLayoutPanel1.Controls.Add(this.pnlInvCode);
            this.flowLayoutPanel1.Controls.Add(this.pnlDate);
            this.flowLayoutPanel1.Controls.Add(this.pnlBranch);
            this.flowLayoutPanel1.Controls.Add(this.pnlAgeDate);
            this.flowLayoutPanel1.Controls.Add(this.pnlDeliveryDate);
            this.flowLayoutPanel1.Controls.Add(this.pnlCurrency);
            this.flowLayoutPanel1.Controls.Add(this.pnlPostStore);
            this.flowLayoutPanel1.Controls.Add(this.pnlPO);
            this.flowLayoutPanel1.Controls.Add(this.pnlSalesEmp);
            this.flowLayoutPanel1.Controls.Add(this.pnlCostCenter);
            this.flowLayoutPanel1.Name = "flowLayoutPanel1";
            // 
            // pnlBook
            // 
            this.pnlBook.Controls.Add(this.txtTserial);
            this.pnlBook.Controls.Add(this.lkp_InvoiceBook);
            resources.ApplyResources(this.pnlBook, "pnlBook");
            this.pnlBook.Name = "pnlBook";
            // 
            // txtTserial
            // 
            resources.ApplyResources(this.txtTserial, "txtTserial");
            this.txtTserial.EnterMoveNextControl = true;
            this.txtTserial.MenuManager = this.barManager1;
            this.txtTserial.Name = "txtTserial";
            this.txtTserial.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txtTserial.Properties.Appearance.BackColor")));
            this.txtTserial.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("txtTserial.Properties.Appearance.ForeColor")));
            this.txtTserial.Properties.Appearance.Options.UseBackColor = true;
            this.txtTserial.Properties.Appearance.Options.UseForeColor = true;
            this.txtTserial.Properties.Appearance.Options.UseTextOptions = true;
            this.txtTserial.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtTserial.Properties.AutoHeight = ((bool)(resources.GetObject("txtTserial.Properties.AutoHeight")));
            this.txtTserial.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.txtTserial.Properties.Mask.EditMask = resources.GetString("txtTserial.Properties.Mask.EditMask");
            this.txtTserial.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtTserial.Properties.Mask.IgnoreMaskBlank")));
            this.txtTserial.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtTserial.Properties.Mask.SaveLiteral")));
            this.txtTserial.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtTserial.Properties.Mask.ShowPlaceHolders")));
            this.txtTserial.Properties.MaxLength = 190;
            this.txtTserial.Properties.NullValuePrompt = resources.GetString("txtTserial.Properties.NullValuePrompt");
            this.txtTserial.TabStop = false;
            this.txtTserial.EditValueChanged += new System.EventHandler(this.txtTserial_EditValueChanged);
            // 
            // lkp_InvoiceBook
            // 
            resources.ApplyResources(this.lkp_InvoiceBook, "lkp_InvoiceBook");
            this.lkp_InvoiceBook.EnterMoveNextControl = true;
            this.lkp_InvoiceBook.Name = "lkp_InvoiceBook";
            this.lkp_InvoiceBook.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_InvoiceBook.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_InvoiceBook.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_InvoiceBook.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_InvoiceBook.Properties.AutoHeight")));
            this.lkp_InvoiceBook.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_InvoiceBook.Properties.Buttons"))))});
            this.lkp_InvoiceBook.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_InvoiceBook.Properties.Columns"), resources.GetString("lkp_InvoiceBook.Properties.Columns1"), ((int)(resources.GetObject("lkp_InvoiceBook.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_InvoiceBook.Properties.Columns3"))), resources.GetString("lkp_InvoiceBook.Properties.Columns4"), ((bool)(resources.GetObject("lkp_InvoiceBook.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_InvoiceBook.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_InvoiceBook.Properties.Columns7"), resources.GetString("lkp_InvoiceBook.Properties.Columns8"), ((int)(resources.GetObject("lkp_InvoiceBook.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_InvoiceBook.Properties.Columns10"))), resources.GetString("lkp_InvoiceBook.Properties.Columns11"), ((bool)(resources.GetObject("lkp_InvoiceBook.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_InvoiceBook.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_InvoiceBook.Properties.Columns14"), resources.GetString("lkp_InvoiceBook.Properties.Columns15"), ((int)(resources.GetObject("lkp_InvoiceBook.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_InvoiceBook.Properties.Columns17"))), resources.GetString("lkp_InvoiceBook.Properties.Columns18"), ((bool)(resources.GetObject("lkp_InvoiceBook.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_InvoiceBook.Properties.Columns20"))))});
            this.lkp_InvoiceBook.Properties.NullText = resources.GetString("lkp_InvoiceBook.Properties.NullText");
            this.lkp_InvoiceBook.Properties.NullValuePrompt = resources.GetString("lkp_InvoiceBook.Properties.NullValuePrompt");
            this.lkp_InvoiceBook.EditValueChanged += new System.EventHandler(this.lkpStore_EditValueChanged);
            this.lkp_InvoiceBook.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // pnlInvCode
            // 
            this.pnlInvCode.Controls.Add(this.txtTinvCode);
            this.pnlInvCode.Controls.Add(this.txtInvoiceCode);
            resources.ApplyResources(this.pnlInvCode, "pnlInvCode");
            this.pnlInvCode.Name = "pnlInvCode";
            // 
            // txtTinvCode
            // 
            resources.ApplyResources(this.txtTinvCode, "txtTinvCode");
            this.txtTinvCode.EnterMoveNextControl = true;
            this.txtTinvCode.MenuManager = this.barManager1;
            this.txtTinvCode.Name = "txtTinvCode";
            this.txtTinvCode.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txtTinvCode.Properties.Appearance.BackColor")));
            this.txtTinvCode.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("txtTinvCode.Properties.Appearance.ForeColor")));
            this.txtTinvCode.Properties.Appearance.Options.UseBackColor = true;
            this.txtTinvCode.Properties.Appearance.Options.UseForeColor = true;
            this.txtTinvCode.Properties.Appearance.Options.UseTextOptions = true;
            this.txtTinvCode.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtTinvCode.Properties.AutoHeight = ((bool)(resources.GetObject("txtTinvCode.Properties.AutoHeight")));
            this.txtTinvCode.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.txtTinvCode.Properties.Mask.EditMask = resources.GetString("txtTinvCode.Properties.Mask.EditMask");
            this.txtTinvCode.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtTinvCode.Properties.Mask.IgnoreMaskBlank")));
            this.txtTinvCode.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtTinvCode.Properties.Mask.SaveLiteral")));
            this.txtTinvCode.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtTinvCode.Properties.Mask.ShowPlaceHolders")));
            this.txtTinvCode.Properties.MaxLength = 190;
            this.txtTinvCode.Properties.NullValuePrompt = resources.GetString("txtTinvCode.Properties.NullValuePrompt");
            this.txtTinvCode.TabStop = false;
            // 
            // pnlDate
            // 
            this.pnlDate.Controls.Add(this.txtTdate);
            this.pnlDate.Controls.Add(this.dtInvoiceDate);
            resources.ApplyResources(this.pnlDate, "pnlDate");
            this.pnlDate.Name = "pnlDate";
            // 
            // txtTdate
            // 
            resources.ApplyResources(this.txtTdate, "txtTdate");
            this.txtTdate.EnterMoveNextControl = true;
            this.txtTdate.MenuManager = this.barManager1;
            this.txtTdate.Name = "txtTdate";
            this.txtTdate.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txtTdate.Properties.Appearance.BackColor")));
            this.txtTdate.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("txtTdate.Properties.Appearance.ForeColor")));
            this.txtTdate.Properties.Appearance.Options.UseBackColor = true;
            this.txtTdate.Properties.Appearance.Options.UseForeColor = true;
            this.txtTdate.Properties.Appearance.Options.UseTextOptions = true;
            this.txtTdate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtTdate.Properties.AutoHeight = ((bool)(resources.GetObject("txtTdate.Properties.AutoHeight")));
            this.txtTdate.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.txtTdate.Properties.Mask.EditMask = resources.GetString("txtTdate.Properties.Mask.EditMask");
            this.txtTdate.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtTdate.Properties.Mask.IgnoreMaskBlank")));
            this.txtTdate.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtTdate.Properties.Mask.SaveLiteral")));
            this.txtTdate.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtTdate.Properties.Mask.ShowPlaceHolders")));
            this.txtTdate.Properties.MaxLength = 190;
            this.txtTdate.Properties.NullValuePrompt = resources.GetString("txtTdate.Properties.NullValuePrompt");
            this.txtTdate.TabStop = false;
            // 
            // pnlBranch
            // 
            this.pnlBranch.Controls.Add(this.txtTstore);
            this.pnlBranch.Controls.Add(this.lkpStore);
            resources.ApplyResources(this.pnlBranch, "pnlBranch");
            this.pnlBranch.Name = "pnlBranch";
            // 
            // txtTstore
            // 
            resources.ApplyResources(this.txtTstore, "txtTstore");
            this.txtTstore.EnterMoveNextControl = true;
            this.txtTstore.MenuManager = this.barManager1;
            this.txtTstore.Name = "txtTstore";
            this.txtTstore.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txtTstore.Properties.Appearance.BackColor")));
            this.txtTstore.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("txtTstore.Properties.Appearance.ForeColor")));
            this.txtTstore.Properties.Appearance.Options.UseBackColor = true;
            this.txtTstore.Properties.Appearance.Options.UseForeColor = true;
            this.txtTstore.Properties.Appearance.Options.UseTextOptions = true;
            this.txtTstore.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtTstore.Properties.AutoHeight = ((bool)(resources.GetObject("txtTstore.Properties.AutoHeight")));
            this.txtTstore.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.txtTstore.Properties.Mask.EditMask = resources.GetString("txtTstore.Properties.Mask.EditMask");
            this.txtTstore.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtTstore.Properties.Mask.IgnoreMaskBlank")));
            this.txtTstore.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtTstore.Properties.Mask.SaveLiteral")));
            this.txtTstore.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtTstore.Properties.Mask.ShowPlaceHolders")));
            this.txtTstore.Properties.MaxLength = 190;
            this.txtTstore.Properties.NullValuePrompt = resources.GetString("txtTstore.Properties.NullValuePrompt");
            this.txtTstore.TabStop = false;
            // 
            // lkpStore
            // 
            resources.ApplyResources(this.lkpStore, "lkpStore");
            this.lkpStore.EnterMoveNextControl = true;
            this.lkpStore.MenuManager = this.barManager1;
            this.lkpStore.Name = "lkpStore";
            this.lkpStore.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkpStore.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpStore.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpStore.Properties.AutoHeight = ((bool)(resources.GetObject("lkpStore.Properties.AutoHeight")));
            this.lkpStore.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpStore.Properties.Buttons"))))});
            this.lkpStore.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns"), resources.GetString("lkpStore.Properties.Columns1"), ((int)(resources.GetObject("lkpStore.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns3"))), resources.GetString("lkpStore.Properties.Columns4"), ((bool)(resources.GetObject("lkpStore.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns7"), resources.GetString("lkpStore.Properties.Columns8"), ((int)(resources.GetObject("lkpStore.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns10"))), resources.GetString("lkpStore.Properties.Columns11"), ((bool)(resources.GetObject("lkpStore.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns14"), resources.GetString("lkpStore.Properties.Columns15"), ((int)(resources.GetObject("lkpStore.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns17"))), resources.GetString("lkpStore.Properties.Columns18"), ((bool)(resources.GetObject("lkpStore.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns20")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns21"), resources.GetString("lkpStore.Properties.Columns22"), ((int)(resources.GetObject("lkpStore.Properties.Columns23"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns24"))), resources.GetString("lkpStore.Properties.Columns25"), ((bool)(resources.GetObject("lkpStore.Properties.Columns26"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns27")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns28"), resources.GetString("lkpStore.Properties.Columns29"), ((int)(resources.GetObject("lkpStore.Properties.Columns30"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns31"))), resources.GetString("lkpStore.Properties.Columns32"), ((bool)(resources.GetObject("lkpStore.Properties.Columns33"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns34")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns35"), resources.GetString("lkpStore.Properties.Columns36"), ((int)(resources.GetObject("lkpStore.Properties.Columns37"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns38"))), resources.GetString("lkpStore.Properties.Columns39"), ((bool)(resources.GetObject("lkpStore.Properties.Columns40"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns41")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns42"), resources.GetString("lkpStore.Properties.Columns43"), ((int)(resources.GetObject("lkpStore.Properties.Columns44"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns45"))), resources.GetString("lkpStore.Properties.Columns46"), ((bool)(resources.GetObject("lkpStore.Properties.Columns47"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns48")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns49"), resources.GetString("lkpStore.Properties.Columns50"))});
            this.lkpStore.Properties.NullText = resources.GetString("lkpStore.Properties.NullText");
            this.lkpStore.Properties.NullValuePrompt = resources.GetString("lkpStore.Properties.NullValuePrompt");
            this.lkpStore.EditValueChanged += new System.EventHandler(this.lkpStore_EditValueChanged);
            this.lkpStore.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.lkpStore.EditValueChanging += new DevExpress.XtraEditors.Controls.ChangingEventHandler(this.lkpStore_EditValueChanging);
            // 
            // pnlAgeDate
            // 
            this.pnlAgeDate.Controls.Add(this.txtTdueDate);
            this.pnlAgeDate.Controls.Add(this.txt_DueDays);
            this.pnlAgeDate.Controls.Add(this.txt_DueDate);
            resources.ApplyResources(this.pnlAgeDate, "pnlAgeDate");
            this.pnlAgeDate.Name = "pnlAgeDate";
            // 
            // txtTdueDate
            // 
            resources.ApplyResources(this.txtTdueDate, "txtTdueDate");
            this.txtTdueDate.EnterMoveNextControl = true;
            this.txtTdueDate.MenuManager = this.barManager1;
            this.txtTdueDate.Name = "txtTdueDate";
            this.txtTdueDate.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txtTdueDate.Properties.Appearance.BackColor")));
            this.txtTdueDate.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("txtTdueDate.Properties.Appearance.ForeColor")));
            this.txtTdueDate.Properties.Appearance.Options.UseBackColor = true;
            this.txtTdueDate.Properties.Appearance.Options.UseForeColor = true;
            this.txtTdueDate.Properties.Appearance.Options.UseTextOptions = true;
            this.txtTdueDate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtTdueDate.Properties.AutoHeight = ((bool)(resources.GetObject("txtTdueDate.Properties.AutoHeight")));
            this.txtTdueDate.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.txtTdueDate.Properties.Mask.EditMask = resources.GetString("txtTdueDate.Properties.Mask.EditMask");
            this.txtTdueDate.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtTdueDate.Properties.Mask.IgnoreMaskBlank")));
            this.txtTdueDate.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtTdueDate.Properties.Mask.SaveLiteral")));
            this.txtTdueDate.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtTdueDate.Properties.Mask.ShowPlaceHolders")));
            this.txtTdueDate.Properties.MaxLength = 190;
            this.txtTdueDate.Properties.NullValuePrompt = resources.GetString("txtTdueDate.Properties.NullValuePrompt");
            this.txtTdueDate.TabStop = false;
            // 
            // txt_DueDays
            // 
            resources.ApplyResources(this.txt_DueDays, "txt_DueDays");
            this.txt_DueDays.EnterMoveNextControl = true;
            this.txt_DueDays.MenuManager = this.barManager1;
            this.txt_DueDays.Name = "txt_DueDays";
            this.txt_DueDays.Properties.AutoHeight = ((bool)(resources.GetObject("txt_DueDays.Properties.AutoHeight")));
            this.txt_DueDays.Properties.IsFloatValue = false;
            this.txt_DueDays.Properties.Mask.EditMask = resources.GetString("txt_DueDays.Properties.Mask.EditMask");
            this.txt_DueDays.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_DueDays.Properties.Mask.IgnoreMaskBlank")));
            this.txt_DueDays.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_DueDays.Properties.Mask.MaskType")));
            this.txt_DueDays.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_DueDays.Properties.Mask.SaveLiteral")));
            this.txt_DueDays.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_DueDays.Properties.Mask.ShowPlaceHolders")));
            this.txt_DueDays.Properties.NullValuePrompt = resources.GetString("txt_DueDays.Properties.NullValuePrompt");
            this.txt_DueDays.Leave += new System.EventHandler(this.txt_DueDays_Leave);
            // 
            // txt_DueDate
            // 
            resources.ApplyResources(this.txt_DueDate, "txt_DueDate");
            this.txt_DueDate.EnterMoveNextControl = true;
            this.txt_DueDate.MenuManager = this.barManager1;
            this.txt_DueDate.Name = "txt_DueDate";
            this.txt_DueDate.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_DueDate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_DueDate.Properties.AutoHeight = ((bool)(resources.GetObject("txt_DueDate.Properties.AutoHeight")));
            this.txt_DueDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("txt_DueDate.Properties.Buttons"))))});
            this.txt_DueDate.Properties.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("txt_DueDate.Properties.CalendarTimeProperties.AutoHeight")));
            this.txt_DueDate.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txt_DueDate.Properties.CalendarTimeProperties.Mask.EditMask = resources.GetString("txt_DueDate.Properties.CalendarTimeProperties.Mask.EditMask");
            this.txt_DueDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_DueDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.txt_DueDate.Properties.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_DueDate.Properties.CalendarTimeProperties.Mask.MaskType")));
            this.txt_DueDate.Properties.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_DueDate.Properties.CalendarTimeProperties.Mask.SaveLiteral")));
            this.txt_DueDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_DueDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.txt_DueDate.Properties.CalendarTimeProperties.NullValuePrompt = resources.GetString("txt_DueDate.Properties.CalendarTimeProperties.NullValuePrompt");
            this.txt_DueDate.Properties.Mask.EditMask = resources.GetString("txt_DueDate.Properties.Mask.EditMask");
            this.txt_DueDate.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_DueDate.Properties.Mask.IgnoreMaskBlank")));
            this.txt_DueDate.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_DueDate.Properties.Mask.MaskType")));
            this.txt_DueDate.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_DueDate.Properties.Mask.SaveLiteral")));
            this.txt_DueDate.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_DueDate.Properties.Mask.ShowPlaceHolders")));
            this.txt_DueDate.Properties.NullValuePrompt = resources.GetString("txt_DueDate.Properties.NullValuePrompt");
            this.txt_DueDate.EditValueChanged += new System.EventHandler(this.txt_DueDate_EditValueChanged);
            // 
            // pnlDeliveryDate
            // 
            this.pnlDeliveryDate.Controls.Add(this.txtTdeliverDate);
            this.pnlDeliveryDate.Controls.Add(this.dtDeliverDate);
            resources.ApplyResources(this.pnlDeliveryDate, "pnlDeliveryDate");
            this.pnlDeliveryDate.Name = "pnlDeliveryDate";
            // 
            // txtTdeliverDate
            // 
            resources.ApplyResources(this.txtTdeliverDate, "txtTdeliverDate");
            this.txtTdeliverDate.EnterMoveNextControl = true;
            this.txtTdeliverDate.MenuManager = this.barManager1;
            this.txtTdeliverDate.Name = "txtTdeliverDate";
            this.txtTdeliverDate.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txtTdeliverDate.Properties.Appearance.BackColor")));
            this.txtTdeliverDate.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("txtTdeliverDate.Properties.Appearance.ForeColor")));
            this.txtTdeliverDate.Properties.Appearance.Options.UseBackColor = true;
            this.txtTdeliverDate.Properties.Appearance.Options.UseForeColor = true;
            this.txtTdeliverDate.Properties.Appearance.Options.UseTextOptions = true;
            this.txtTdeliverDate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtTdeliverDate.Properties.AutoHeight = ((bool)(resources.GetObject("txtTdeliverDate.Properties.AutoHeight")));
            this.txtTdeliverDate.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.txtTdeliverDate.Properties.Mask.EditMask = resources.GetString("txtTdeliverDate.Properties.Mask.EditMask");
            this.txtTdeliverDate.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtTdeliverDate.Properties.Mask.IgnoreMaskBlank")));
            this.txtTdeliverDate.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtTdeliverDate.Properties.Mask.SaveLiteral")));
            this.txtTdeliverDate.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtTdeliverDate.Properties.Mask.ShowPlaceHolders")));
            this.txtTdeliverDate.Properties.MaxLength = 190;
            this.txtTdeliverDate.Properties.NullValuePrompt = resources.GetString("txtTdeliverDate.Properties.NullValuePrompt");
            this.txtTdeliverDate.TabStop = false;
            // 
            // dtDeliverDate
            // 
            resources.ApplyResources(this.dtDeliverDate, "dtDeliverDate");
            this.dtDeliverDate.EnterMoveNextControl = true;
            this.dtDeliverDate.MenuManager = this.barManager1;
            this.dtDeliverDate.Name = "dtDeliverDate";
            this.dtDeliverDate.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.dtDeliverDate.Properties.Appearance.Options.UseTextOptions = true;
            this.dtDeliverDate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.dtDeliverDate.Properties.AutoHeight = ((bool)(resources.GetObject("dtDeliverDate.Properties.AutoHeight")));
            this.dtDeliverDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("dtDeliverDate.Properties.Buttons"))))});
            this.dtDeliverDate.Properties.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("dtDeliverDate.Properties.CalendarTimeProperties.AutoHeight")));
            this.dtDeliverDate.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dtDeliverDate.Properties.CalendarTimeProperties.Mask.EditMask = resources.GetString("dtDeliverDate.Properties.CalendarTimeProperties.Mask.EditMask");
            this.dtDeliverDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtDeliverDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.dtDeliverDate.Properties.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtDeliverDate.Properties.CalendarTimeProperties.Mask.MaskType")));
            this.dtDeliverDate.Properties.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtDeliverDate.Properties.CalendarTimeProperties.Mask.SaveLiteral")));
            this.dtDeliverDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtDeliverDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.dtDeliverDate.Properties.CalendarTimeProperties.NullValuePrompt = resources.GetString("dtDeliverDate.Properties.CalendarTimeProperties.NullValuePrompt");
            this.dtDeliverDate.Properties.Mask.EditMask = resources.GetString("dtDeliverDate.Properties.Mask.EditMask");
            this.dtDeliverDate.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtDeliverDate.Properties.Mask.IgnoreMaskBlank")));
            this.dtDeliverDate.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtDeliverDate.Properties.Mask.MaskType")));
            this.dtDeliverDate.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtDeliverDate.Properties.Mask.SaveLiteral")));
            this.dtDeliverDate.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtDeliverDate.Properties.Mask.ShowPlaceHolders")));
            this.dtDeliverDate.Properties.NullValuePrompt = resources.GetString("dtDeliverDate.Properties.NullValuePrompt");
            this.dtDeliverDate.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // pnlCurrency
            // 
            this.pnlCurrency.Controls.Add(this.uc_Currency1);
            this.pnlCurrency.Controls.Add(this.txtCurrency);
            resources.ApplyResources(this.pnlCurrency, "pnlCurrency");
            this.pnlCurrency.Name = "pnlCurrency";
            // 
            // uc_Currency1
            // 
            resources.ApplyResources(this.uc_Currency1, "uc_Currency1");
            this.uc_Currency1.Name = "uc_Currency1";
            this.uc_Currency1.TabStop = false;
            // 
            // txtCurrency
            // 
            resources.ApplyResources(this.txtCurrency, "txtCurrency");
            this.txtCurrency.EnterMoveNextControl = true;
            this.txtCurrency.MenuManager = this.barManager1;
            this.txtCurrency.Name = "txtCurrency";
            this.txtCurrency.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txtCurrency.Properties.Appearance.BackColor")));
            this.txtCurrency.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("txtCurrency.Properties.Appearance.ForeColor")));
            this.txtCurrency.Properties.Appearance.Options.UseBackColor = true;
            this.txtCurrency.Properties.Appearance.Options.UseForeColor = true;
            this.txtCurrency.Properties.Appearance.Options.UseTextOptions = true;
            this.txtCurrency.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtCurrency.Properties.AutoHeight = ((bool)(resources.GetObject("txtCurrency.Properties.AutoHeight")));
            this.txtCurrency.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.txtCurrency.Properties.Mask.EditMask = resources.GetString("txtCurrency.Properties.Mask.EditMask");
            this.txtCurrency.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtCurrency.Properties.Mask.IgnoreMaskBlank")));
            this.txtCurrency.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtCurrency.Properties.Mask.SaveLiteral")));
            this.txtCurrency.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtCurrency.Properties.Mask.ShowPlaceHolders")));
            this.txtCurrency.Properties.MaxLength = 190;
            this.txtCurrency.Properties.NullValuePrompt = resources.GetString("txtCurrency.Properties.NullValuePrompt");
            this.txtCurrency.TabStop = false;
            // 
            // pnlPostStore
            // 
            this.pnlPostStore.Controls.Add(this.txt_Post_Date);
            this.pnlPostStore.Controls.Add(this.chk_IsPosted);
            resources.ApplyResources(this.pnlPostStore, "pnlPostStore");
            this.pnlPostStore.Name = "pnlPostStore";
            // 
            // txt_Post_Date
            // 
            resources.ApplyResources(this.txt_Post_Date, "txt_Post_Date");
            this.txt_Post_Date.EnterMoveNextControl = true;
            this.txt_Post_Date.MenuManager = this.barManager1;
            this.txt_Post_Date.Name = "txt_Post_Date";
            this.txt_Post_Date.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.txt_Post_Date.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_Post_Date.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_Post_Date.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Post_Date.Properties.AutoHeight")));
            this.txt_Post_Date.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("txt_Post_Date.Properties.Buttons"))))});
            this.txt_Post_Date.Properties.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("txt_Post_Date.Properties.CalendarTimeProperties.AutoHeight")));
            this.txt_Post_Date.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txt_Post_Date.Properties.CalendarTimeProperties.Mask.EditMask = resources.GetString("txt_Post_Date.Properties.CalendarTimeProperties.Mask.EditMask");
            this.txt_Post_Date.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Post_Date.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.txt_Post_Date.Properties.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Post_Date.Properties.CalendarTimeProperties.Mask.MaskType")));
            this.txt_Post_Date.Properties.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Post_Date.Properties.CalendarTimeProperties.Mask.SaveLiteral")));
            this.txt_Post_Date.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Post_Date.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.txt_Post_Date.Properties.CalendarTimeProperties.NullValuePrompt = resources.GetString("txt_Post_Date.Properties.CalendarTimeProperties.NullValuePrompt");
            this.txt_Post_Date.Properties.DisplayFormat.FormatString = "g";
            this.txt_Post_Date.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.txt_Post_Date.Properties.EditFormat.FormatString = "g";
            this.txt_Post_Date.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.txt_Post_Date.Properties.Mask.EditMask = resources.GetString("txt_Post_Date.Properties.Mask.EditMask");
            this.txt_Post_Date.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Post_Date.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Post_Date.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Post_Date.Properties.Mask.MaskType")));
            this.txt_Post_Date.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Post_Date.Properties.Mask.SaveLiteral")));
            this.txt_Post_Date.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Post_Date.Properties.Mask.ShowPlaceHolders")));
            this.txt_Post_Date.Properties.NullValuePrompt = resources.GetString("txt_Post_Date.Properties.NullValuePrompt");
            this.txt_Post_Date.TabStop = false;
            // 
            // chk_IsPosted
            // 
            resources.ApplyResources(this.chk_IsPosted, "chk_IsPosted");
            this.chk_IsPosted.MenuManager = this.barManager1;
            this.chk_IsPosted.Name = "chk_IsPosted";
            this.chk_IsPosted.Properties.Appearance.Options.UseTextOptions = true;
            this.chk_IsPosted.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chk_IsPosted.Properties.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.chk_IsPosted.Properties.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.chk_IsPosted.Properties.AutoHeight = ((bool)(resources.GetObject("chk_IsPosted.Properties.AutoHeight")));
            this.chk_IsPosted.Properties.Caption = resources.GetString("chk_IsPosted.Properties.Caption");
            this.chk_IsPosted.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("chk_IsPosted.Properties.GlyphAlignment")));
            this.chk_IsPosted.TabStop = false;
            this.chk_IsPosted.EditValueChanged += new System.EventHandler(this.chk_IsPosted_EditValueChanged);
            // 
            // pnlPO
            // 
            this.pnlPO.Controls.Add(this.txtTpo);
            this.pnlPO.Controls.Add(this.txt_PO_No);
            resources.ApplyResources(this.pnlPO, "pnlPO");
            this.pnlPO.Name = "pnlPO";
            // 
            // txtTpo
            // 
            resources.ApplyResources(this.txtTpo, "txtTpo");
            this.txtTpo.EnterMoveNextControl = true;
            this.txtTpo.MenuManager = this.barManager1;
            this.txtTpo.Name = "txtTpo";
            this.txtTpo.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txtTpo.Properties.Appearance.BackColor")));
            this.txtTpo.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("txtTpo.Properties.Appearance.ForeColor")));
            this.txtTpo.Properties.Appearance.Options.UseBackColor = true;
            this.txtTpo.Properties.Appearance.Options.UseForeColor = true;
            this.txtTpo.Properties.Appearance.Options.UseTextOptions = true;
            this.txtTpo.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtTpo.Properties.AutoHeight = ((bool)(resources.GetObject("txtTpo.Properties.AutoHeight")));
            this.txtTpo.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.txtTpo.Properties.Mask.EditMask = resources.GetString("txtTpo.Properties.Mask.EditMask");
            this.txtTpo.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtTpo.Properties.Mask.IgnoreMaskBlank")));
            this.txtTpo.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtTpo.Properties.Mask.SaveLiteral")));
            this.txtTpo.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtTpo.Properties.Mask.ShowPlaceHolders")));
            this.txtTpo.Properties.MaxLength = 190;
            this.txtTpo.Properties.NullValuePrompt = resources.GetString("txtTpo.Properties.NullValuePrompt");
            this.txtTpo.TabStop = false;
            // 
            // txt_PO_No
            // 
            resources.ApplyResources(this.txt_PO_No, "txt_PO_No");
            this.txt_PO_No.EnterMoveNextControl = true;
            this.txt_PO_No.Name = "txt_PO_No";
            this.txt_PO_No.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txt_PO_No.Properties.Appearance.BackColor")));
            this.txt_PO_No.Properties.Appearance.Options.UseBackColor = true;
            this.txt_PO_No.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_PO_No.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_PO_No.Properties.AutoHeight = ((bool)(resources.GetObject("txt_PO_No.Properties.AutoHeight")));
            this.txt_PO_No.Properties.Mask.EditMask = resources.GetString("txt_PO_No.Properties.Mask.EditMask");
            this.txt_PO_No.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_PO_No.Properties.Mask.IgnoreMaskBlank")));
            this.txt_PO_No.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_PO_No.Properties.Mask.SaveLiteral")));
            this.txt_PO_No.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_PO_No.Properties.Mask.ShowPlaceHolders")));
            this.txt_PO_No.Properties.NullValuePrompt = resources.GetString("txt_PO_No.Properties.NullValuePrompt");
            this.txt_PO_No.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // pnlSalesEmp
            // 
            this.pnlSalesEmp.Controls.Add(this.txtTSalesEmp);
            this.pnlSalesEmp.Controls.Add(this.lkp_SalesEmp);
            resources.ApplyResources(this.pnlSalesEmp, "pnlSalesEmp");
            this.pnlSalesEmp.Name = "pnlSalesEmp";
            // 
            // txtTSalesEmp
            // 
            resources.ApplyResources(this.txtTSalesEmp, "txtTSalesEmp");
            this.txtTSalesEmp.EnterMoveNextControl = true;
            this.txtTSalesEmp.MenuManager = this.barManager1;
            this.txtTSalesEmp.Name = "txtTSalesEmp";
            this.txtTSalesEmp.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txtTSalesEmp.Properties.Appearance.BackColor")));
            this.txtTSalesEmp.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("txtTSalesEmp.Properties.Appearance.ForeColor")));
            this.txtTSalesEmp.Properties.Appearance.Options.UseBackColor = true;
            this.txtTSalesEmp.Properties.Appearance.Options.UseForeColor = true;
            this.txtTSalesEmp.Properties.Appearance.Options.UseTextOptions = true;
            this.txtTSalesEmp.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtTSalesEmp.Properties.AutoHeight = ((bool)(resources.GetObject("txtTSalesEmp.Properties.AutoHeight")));
            this.txtTSalesEmp.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.txtTSalesEmp.Properties.Mask.EditMask = resources.GetString("txtTSalesEmp.Properties.Mask.EditMask");
            this.txtTSalesEmp.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtTSalesEmp.Properties.Mask.IgnoreMaskBlank")));
            this.txtTSalesEmp.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtTSalesEmp.Properties.Mask.SaveLiteral")));
            this.txtTSalesEmp.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtTSalesEmp.Properties.Mask.ShowPlaceHolders")));
            this.txtTSalesEmp.Properties.MaxLength = 190;
            this.txtTSalesEmp.Properties.NullValuePrompt = resources.GetString("txtTSalesEmp.Properties.NullValuePrompt");
            this.txtTSalesEmp.TabStop = false;
            // 
            // lkp_SalesEmp
            // 
            resources.ApplyResources(this.lkp_SalesEmp, "lkp_SalesEmp");
            this.lkp_SalesEmp.EnterMoveNextControl = true;
            this.lkp_SalesEmp.MenuManager = this.barManager1;
            this.lkp_SalesEmp.Name = "lkp_SalesEmp";
            this.lkp_SalesEmp.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_SalesEmp.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_SalesEmp.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_SalesEmp.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_SalesEmp.Properties.AutoHeight")));
            this.lkp_SalesEmp.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_SalesEmp.Properties.Buttons"))))});
            this.lkp_SalesEmp.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_SalesEmp.Properties.Columns"), resources.GetString("lkp_SalesEmp.Properties.Columns1")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_SalesEmp.Properties.Columns2"), resources.GetString("lkp_SalesEmp.Properties.Columns3")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_SalesEmp.Properties.Columns4"), resources.GetString("lkp_SalesEmp.Properties.Columns5"), ((int)(resources.GetObject("lkp_SalesEmp.Properties.Columns6"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_SalesEmp.Properties.Columns7"))), resources.GetString("lkp_SalesEmp.Properties.Columns8"), ((bool)(resources.GetObject("lkp_SalesEmp.Properties.Columns9"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_SalesEmp.Properties.Columns10"))))});
            this.lkp_SalesEmp.Properties.DisplayMember = "EmpName";
            this.lkp_SalesEmp.Properties.NullText = resources.GetString("lkp_SalesEmp.Properties.NullText");
            this.lkp_SalesEmp.Properties.NullValuePrompt = resources.GetString("lkp_SalesEmp.Properties.NullValuePrompt");
            this.lkp_SalesEmp.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.lkp_SalesEmp.Properties.ValueMember = "EmpId";
            this.lkp_SalesEmp.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // pnlCostCenter
            // 
            this.pnlCostCenter.Controls.Add(this.textEdit9);
            this.pnlCostCenter.Controls.Add(this.lkpCostCenter);
            resources.ApplyResources(this.pnlCostCenter, "pnlCostCenter");
            this.pnlCostCenter.Name = "pnlCostCenter";
            // 
            // textEdit9
            // 
            resources.ApplyResources(this.textEdit9, "textEdit9");
            this.textEdit9.EnterMoveNextControl = true;
            this.textEdit9.MenuManager = this.barManager1;
            this.textEdit9.Name = "textEdit9";
            this.textEdit9.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("textEdit9.Properties.Appearance.BackColor")));
            this.textEdit9.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("textEdit9.Properties.Appearance.ForeColor")));
            this.textEdit9.Properties.Appearance.Options.UseBackColor = true;
            this.textEdit9.Properties.Appearance.Options.UseForeColor = true;
            this.textEdit9.Properties.Appearance.Options.UseTextOptions = true;
            this.textEdit9.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.textEdit9.Properties.AutoHeight = ((bool)(resources.GetObject("textEdit9.Properties.AutoHeight")));
            this.textEdit9.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.HotFlat;
            this.textEdit9.Properties.Mask.EditMask = resources.GetString("textEdit9.Properties.Mask.EditMask");
            this.textEdit9.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("textEdit9.Properties.Mask.IgnoreMaskBlank")));
            this.textEdit9.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("textEdit9.Properties.Mask.SaveLiteral")));
            this.textEdit9.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("textEdit9.Properties.Mask.ShowPlaceHolders")));
            this.textEdit9.Properties.MaxLength = 190;
            this.textEdit9.Properties.NullValuePrompt = resources.GetString("textEdit9.Properties.NullValuePrompt");
            this.textEdit9.TabStop = false;
            // 
            // lkpCostCenter
            // 
            resources.ApplyResources(this.lkpCostCenter, "lkpCostCenter");
            this.lkpCostCenter.MenuManager = this.barManager1;
            this.lkpCostCenter.Name = "lkpCostCenter";
            this.lkpCostCenter.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpCostCenter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpCostCenter.Properties.AutoHeight = ((bool)(resources.GetObject("lkpCostCenter.Properties.AutoHeight")));
            this.lkpCostCenter.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkpCostCenter.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpCostCenter.Properties.Buttons"))))});
            this.lkpCostCenter.Properties.NullText = resources.GetString("lkpCostCenter.Properties.NullText");
            this.lkpCostCenter.Properties.NullValuePrompt = resources.GetString("lkpCostCenter.Properties.NullValuePrompt");
            this.lkpCostCenter.Properties.View = this.gv_CostCenter;
            this.lkpCostCenter.Popup += new System.EventHandler(this.lkpCostCenter_Popup);
            this.lkpCostCenter.CloseUp += new DevExpress.XtraEditors.Controls.CloseUpEventHandler(this.lkpCostCenter_CloseUp);
            // 
            // gv_CostCenter
            // 
            this.gv_CostCenter.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn43,
            this.gridColumn42,
            this.gridColumn40});
            this.gv_CostCenter.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gv_CostCenter.Name = "gv_CostCenter";
            this.gv_CostCenter.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gv_CostCenter.OptionsSelection.MultiSelect = true;
            this.gv_CostCenter.OptionsSelection.MultiSelectMode = DevExpress.XtraGrid.Views.Grid.GridMultiSelectMode.CheckBoxRowSelect;
            this.gv_CostCenter.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn43
            // 
            resources.ApplyResources(this.gridColumn43, "gridColumn43");
            this.gridColumn43.FieldName = "CostCenterCode";
            this.gridColumn43.Name = "gridColumn43";
            // 
            // gridColumn42
            // 
            resources.ApplyResources(this.gridColumn42, "gridColumn42");
            this.gridColumn42.FieldName = "CostCenterName";
            this.gridColumn42.Name = "gridColumn42";
            // 
            // gridColumn40
            // 
            resources.ApplyResources(this.gridColumn40, "gridColumn40");
            this.gridColumn40.FieldName = "CostCenterId";
            this.gridColumn40.Name = "gridColumn40";
            // 
            // txtNotes
            // 
            resources.ApplyResources(this.txtNotes, "txtNotes");
            this.txtNotes.MenuManager = this.barManager1;
            this.txtNotes.Name = "txtNotes";
            this.txtNotes.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txtNotes.Properties.Appearance.BackColor")));
            this.txtNotes.Properties.Appearance.Options.UseBackColor = true;
            this.txtNotes.Properties.NullValuePrompt = resources.GetString("txtNotes.Properties.NullValuePrompt");
            this.txtNotes.Properties.ScrollBars = System.Windows.Forms.ScrollBars.Both;
            this.txtNotes.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // txt_Shipping
            // 
            resources.ApplyResources(this.txt_Shipping, "txt_Shipping");
            this.txt_Shipping.Name = "txt_Shipping";
            this.txt_Shipping.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txt_Shipping.Properties.Appearance.BackColor")));
            this.txt_Shipping.Properties.Appearance.Options.UseBackColor = true;
            this.txt_Shipping.Properties.NullValuePrompt = resources.GetString("txt_Shipping.Properties.NullValuePrompt");
            this.txt_Shipping.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // chk_IsOutTrns
            // 
            resources.ApplyResources(this.chk_IsOutTrns, "chk_IsOutTrns");
            this.chk_IsOutTrns.Name = "chk_IsOutTrns";
            this.chk_IsOutTrns.Properties.Appearance.Options.UseTextOptions = true;
            this.chk_IsOutTrns.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chk_IsOutTrns.Properties.AppearanceReadOnly.ForeColor = ((System.Drawing.Color)(resources.GetObject("chk_IsOutTrns.Properties.AppearanceReadOnly.ForeColor")));
            this.chk_IsOutTrns.Properties.AppearanceReadOnly.Options.UseForeColor = true;
            this.chk_IsOutTrns.Properties.AutoHeight = ((bool)(resources.GetObject("chk_IsOutTrns.Properties.AutoHeight")));
            this.chk_IsOutTrns.Properties.Caption = resources.GetString("chk_IsOutTrns.Properties.Caption");
            this.chk_IsOutTrns.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("chk_IsOutTrns.Properties.GlyphAlignment")));
            this.chk_IsOutTrns.Properties.ReadOnly = true;
            this.chk_IsOutTrns.TabStop = false;
            // 
            // cmbPayMethod
            // 
            resources.ApplyResources(this.cmbPayMethod, "cmbPayMethod");
            this.cmbPayMethod.EnterMoveNextControl = true;
            this.cmbPayMethod.MenuManager = this.barManager1;
            this.cmbPayMethod.Name = "cmbPayMethod";
            this.cmbPayMethod.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.cmbPayMethod.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("cmbPayMethod.Properties.Appearance.BackColor")));
            this.cmbPayMethod.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("cmbPayMethod.Properties.Appearance.Font")));
            this.cmbPayMethod.Properties.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("cmbPayMethod.Properties.Appearance.ForeColor")));
            this.cmbPayMethod.Properties.Appearance.Options.UseBackColor = true;
            this.cmbPayMethod.Properties.Appearance.Options.UseFont = true;
            this.cmbPayMethod.Properties.Appearance.Options.UseForeColor = true;
            this.cmbPayMethod.Properties.Appearance.Options.UseTextOptions = true;
            this.cmbPayMethod.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.cmbPayMethod.Properties.AppearanceDisabled.BackColor = ((System.Drawing.Color)(resources.GetObject("cmbPayMethod.Properties.AppearanceDisabled.BackColor")));
            this.cmbPayMethod.Properties.AppearanceDisabled.Font = ((System.Drawing.Font)(resources.GetObject("cmbPayMethod.Properties.AppearanceDisabled.Font")));
            this.cmbPayMethod.Properties.AppearanceDisabled.ForeColor = ((System.Drawing.Color)(resources.GetObject("cmbPayMethod.Properties.AppearanceDisabled.ForeColor")));
            this.cmbPayMethod.Properties.AppearanceDisabled.Options.UseBackColor = true;
            this.cmbPayMethod.Properties.AppearanceDisabled.Options.UseFont = true;
            this.cmbPayMethod.Properties.AppearanceDisabled.Options.UseForeColor = true;
            this.cmbPayMethod.Properties.AutoHeight = ((bool)(resources.GetObject("cmbPayMethod.Properties.AutoHeight")));
            this.cmbPayMethod.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.cmbPayMethod.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("cmbPayMethod.Properties.Buttons"))), resources.GetString("cmbPayMethod.Properties.Buttons1"), ((int)(resources.GetObject("cmbPayMethod.Properties.Buttons2"))), ((bool)(resources.GetObject("cmbPayMethod.Properties.Buttons3"))), ((bool)(resources.GetObject("cmbPayMethod.Properties.Buttons4"))), ((bool)(resources.GetObject("cmbPayMethod.Properties.Buttons5"))), ((DevExpress.XtraEditors.ImageLocation)(resources.GetObject("cmbPayMethod.Properties.Buttons6"))), ((System.Drawing.Image)(resources.GetObject("cmbPayMethod.Properties.Buttons7"))), new DevExpress.Utils.KeyShortcut(System.Windows.Forms.Keys.None), serializableAppearanceObject1, resources.GetString("cmbPayMethod.Properties.Buttons8"), ((object)(resources.GetObject("cmbPayMethod.Properties.Buttons9"))), ((DevExpress.Utils.SuperToolTip)(resources.GetObject("cmbPayMethod.Properties.Buttons10"))), ((bool)(resources.GetObject("cmbPayMethod.Properties.Buttons11"))))});
            this.cmbPayMethod.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("cmbPayMethod.Properties.GlyphAlignment")));
            this.cmbPayMethod.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbPayMethod.Properties.Items"), ((object)(resources.GetObject("cmbPayMethod.Properties.Items1"))), ((int)(resources.GetObject("cmbPayMethod.Properties.Items2")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbPayMethod.Properties.Items3"), ((object)(resources.GetObject("cmbPayMethod.Properties.Items4"))), ((int)(resources.GetObject("cmbPayMethod.Properties.Items5")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("cmbPayMethod.Properties.Items6"), ((object)(resources.GetObject("cmbPayMethod.Properties.Items7"))), ((int)(resources.GetObject("cmbPayMethod.Properties.Items8"))))});
            this.cmbPayMethod.Properties.NullValuePrompt = resources.GetString("cmbPayMethod.Properties.NullValuePrompt");
            // 
            // lkp_Drawers
            // 
            this.lkp_Drawers.EnterMoveNextControl = true;
            resources.ApplyResources(this.lkp_Drawers, "lkp_Drawers");
            this.lkp_Drawers.MenuManager = this.barManager1;
            this.lkp_Drawers.Name = "lkp_Drawers";
            this.lkp_Drawers.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkp_Drawers.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_Drawers.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_Drawers.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_Drawers.Properties.AutoHeight")));
            this.lkp_Drawers.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_Drawers.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_Drawers.Properties.Buttons"))))});
            this.lkp_Drawers.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Drawers.Properties.Columns"), resources.GetString("lkp_Drawers.Properties.Columns1"), ((int)(resources.GetObject("lkp_Drawers.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_Drawers.Properties.Columns3"))), resources.GetString("lkp_Drawers.Properties.Columns4"), ((bool)(resources.GetObject("lkp_Drawers.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_Drawers.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Drawers.Properties.Columns7"), resources.GetString("lkp_Drawers.Properties.Columns8"))});
            this.lkp_Drawers.Properties.DisplayMember = "AccountNameEn";
            this.lkp_Drawers.Properties.NullText = resources.GetString("lkp_Drawers.Properties.NullText");
            this.lkp_Drawers.Properties.NullValuePrompt = resources.GetString("lkp_Drawers.Properties.NullValuePrompt");
            this.lkp_Drawers.Properties.ValueMember = "AccountId";
            this.lkp_Drawers.EditValueChanged += new System.EventHandler(this.lkp_Drawers_EditValueChanged);
            this.lkp_Drawers.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // labelControl17
            // 
            resources.ApplyResources(this.labelControl17, "labelControl17");
            this.labelControl17.Name = "labelControl17";
            // 
            // lbl_remains
            // 
            resources.ApplyResources(this.lbl_remains, "lbl_remains");
            this.lbl_remains.Name = "lbl_remains";
            // 
            // txt_Remains
            // 
            resources.ApplyResources(this.txt_Remains, "txt_Remains");
            this.txt_Remains.EnterMoveNextControl = true;
            this.txt_Remains.Name = "txt_Remains";
            this.txt_Remains.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txt_Remains.Properties.Appearance.BackColor")));
            this.txt_Remains.Properties.Appearance.Options.UseBackColor = true;
            this.txt_Remains.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_Remains.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_Remains.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Remains.Properties.AutoHeight")));
            this.txt_Remains.Properties.Mask.EditMask = resources.GetString("txt_Remains.Properties.Mask.EditMask");
            this.txt_Remains.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Remains.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Remains.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Remains.Properties.Mask.MaskType")));
            this.txt_Remains.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Remains.Properties.Mask.SaveLiteral")));
            this.txt_Remains.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Remains.Properties.Mask.ShowPlaceHolders")));
            this.txt_Remains.Properties.NullValuePrompt = resources.GetString("txt_Remains.Properties.NullValuePrompt");
            this.txt_Remains.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txt_Remains.EditValueChanged += new System.EventHandler(this.txt_paid_EditValueChanged);
            this.txt_Remains.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txt_Remains.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress_1);
            // 
            // lbl_Paid
            // 
            resources.ApplyResources(this.lbl_Paid, "lbl_Paid");
            this.lbl_Paid.Name = "lbl_Paid";
            // 
            // txt_paid
            // 
            resources.ApplyResources(this.txt_paid, "txt_paid");
            this.txt_paid.EnterMoveNextControl = true;
            this.txt_paid.Name = "txt_paid";
            this.txt_paid.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txt_paid.Properties.Appearance.BackColor")));
            this.txt_paid.Properties.Appearance.Options.UseBackColor = true;
            this.txt_paid.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_paid.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_paid.Properties.AutoHeight = ((bool)(resources.GetObject("txt_paid.Properties.AutoHeight")));
            this.txt_paid.Properties.Mask.EditMask = resources.GetString("txt_paid.Properties.Mask.EditMask");
            this.txt_paid.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_paid.Properties.Mask.IgnoreMaskBlank")));
            this.txt_paid.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_paid.Properties.Mask.MaskType")));
            this.txt_paid.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_paid.Properties.Mask.SaveLiteral")));
            this.txt_paid.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_paid.Properties.Mask.ShowPlaceHolders")));
            this.txt_paid.Properties.NullValuePrompt = resources.GetString("txt_paid.Properties.NullValuePrompt");
            this.txt_paid.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txt_paid.EditValueChanged += new System.EventHandler(this.txt_paid_EditValueChanged);
            this.txt_paid.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txt_paid.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress_1);
            // 
            // labelControl9
            // 
            resources.ApplyResources(this.labelControl9, "labelControl9");
            this.labelControl9.Name = "labelControl9";
            // 
            // labelControl6
            // 
            resources.ApplyResources(this.labelControl6, "labelControl6");
            this.labelControl6.Name = "labelControl6";
            // 
            // labelControl7
            // 
            resources.ApplyResources(this.labelControl7, "labelControl7");
            this.labelControl7.Name = "labelControl7";
            // 
            // lkp_Customers
            // 
            resources.ApplyResources(this.lkp_Customers, "lkp_Customers");
            this.lkp_Customers.EnterMoveNextControl = true;
            this.lkp_Customers.MenuManager = this.barManager1;
            this.lkp_Customers.Name = "lkp_Customers";
            this.lkp_Customers.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkp_Customers.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_Customers.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_Customers.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_Customers.Properties.AutoHeight")));
            this.lkp_Customers.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_Customers.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_Customers.Properties.Buttons"))))});
            this.lkp_Customers.Properties.ImmediatePopup = true;
            this.lkp_Customers.Properties.NullText = resources.GetString("lkp_Customers.Properties.NullText");
            this.lkp_Customers.Properties.NullValuePrompt = resources.GetString("lkp_Customers.Properties.NullValuePrompt");
            this.lkp_Customers.Properties.PopupFilterMode = DevExpress.XtraEditors.PopupFilterMode.Contains;
            this.lkp_Customers.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.lkp_Customers.Properties.View = this.gridView1;
            this.lkp_Customers.EditValueChanged += new System.EventHandler(this.lkp_Customers_EditValueChanged);
            this.lkp_Customers.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // gridView1
            // 
            this.gridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.Row.Options.UseTextOptions = true;
            this.gridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn26,
            this.gridColumn27,
            this.gridColumn30,
            this.gridColumn35});
            this.gridView1.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView1.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView1.OptionsView.EnableAppearanceOddRow = true;
            this.gridView1.OptionsView.ShowAutoFilterRow = true;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.OptionsView.ShowIndicator = false;
            // 
            // gridColumn19
            // 
            resources.ApplyResources(this.gridColumn19, "gridColumn19");
            this.gridColumn19.FieldName = "CustomerId";
            this.gridColumn19.Name = "gridColumn19";
            // 
            // gridColumn20
            // 
            resources.ApplyResources(this.gridColumn20, "gridColumn20");
            this.gridColumn20.FieldName = "CusCode";
            this.gridColumn20.Name = "gridColumn20";
            this.gridColumn20.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // gridColumn21
            // 
            resources.ApplyResources(this.gridColumn21, "gridColumn21");
            this.gridColumn21.FieldName = "CusNameAr";
            this.gridColumn21.Name = "gridColumn21";
            this.gridColumn21.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // gridColumn22
            // 
            resources.ApplyResources(this.gridColumn22, "gridColumn22");
            this.gridColumn22.FieldName = "CusNameEn";
            this.gridColumn22.Name = "gridColumn22";
            this.gridColumn22.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // gridColumn26
            // 
            resources.ApplyResources(this.gridColumn26, "gridColumn26");
            this.gridColumn26.FieldName = "GroupId";
            this.gridColumn26.Name = "gridColumn26";
            // 
            // gridColumn27
            // 
            resources.ApplyResources(this.gridColumn27, "gridColumn27");
            this.gridColumn27.FieldName = "City";
            this.gridColumn27.Name = "gridColumn27";
            // 
            // gridColumn30
            // 
            resources.ApplyResources(this.gridColumn30, "gridColumn30");
            this.gridColumn30.FieldName = "Mobile";
            this.gridColumn30.Name = "gridColumn30";
            // 
            // gridColumn35
            // 
            resources.ApplyResources(this.gridColumn35, "gridColumn35");
            this.gridColumn35.FieldName = "Is_Blocked";
            this.gridColumn35.Name = "gridColumn35";
            // 
            // panelControl2
            // 
            resources.ApplyResources(this.panelControl2, "panelControl2");
            this.panelControl2.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.panelControl2.Controls.Add(this.grdPrInvoice);
            this.panelControl2.Name = "panelControl2";
            // 
            // grdPrInvoice
            // 
            this.grdPrInvoice.ContextMenuStrip = this.contextMenuStrip1;
            this.grdPrInvoice.Cursor = System.Windows.Forms.Cursors.Default;
            resources.ApplyResources(this.grdPrInvoice, "grdPrInvoice");
            this.grdPrInvoice.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grdPrInvoice.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.Anchor")));
            this.grdPrInvoice.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.BackgroundImageLayout")));
            this.grdPrInvoice.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.ImeMode")));
            this.grdPrInvoice.EmbeddedNavigator.Margin = ((System.Windows.Forms.Padding)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.Margin")));
            this.grdPrInvoice.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.TextLocation")));
            this.grdPrInvoice.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grdPrInvoice.EmbeddedNavigator.ToolTipIconType")));
            this.grdPrInvoice.MainView = this.gridView2;
            this.grdPrInvoice.Name = "grdPrInvoice";
            this.grdPrInvoice.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repSpin,
            this.repItems,
            this.repUOM,
            this.repDiscountRatio,
            this.rep_expireDate,
            this.rep_Batch,
            this.lkp_store,
            this.lkp_storee,
            this.repManufactureDate,
            this.repositoryItemSpinEdit1,
            this.rep_Location,
            this.rep_Libra,
            this.repspin_PiecesCount,
            this.repCompany,
            this.repCategory,
            this.repTaxTypes,
            this.rep_btnAddTaxes});
            this.grdPrInvoice.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView2});
            // 
            // contextMenuStrip1
            // 
            this.contextMenuStrip1.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.mi_frm_IC_Item,
            this.mi_CustLastPrices,
            this.mi_LastPrices,
            this.mi_PasteRows,
            this.mi_ExportData,
            this.mi_InvoiceStaticDisc,
            this.mi_InvoiceStaticDimensions,
            this.mi_ImportExcel});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            resources.ApplyResources(this.contextMenuStrip1, "contextMenuStrip1");
            this.contextMenuStrip1.Opened += new System.EventHandler(this.contextMenuStrip1_Opened);
            // 
            // mi_frm_IC_Item
            // 
            this.mi_frm_IC_Item.Name = "mi_frm_IC_Item";
            resources.ApplyResources(this.mi_frm_IC_Item, "mi_frm_IC_Item");
            this.mi_frm_IC_Item.Click += new System.EventHandler(this.mi_frm_IC_Item_Click);
            // 
            // mi_CustLastPrices
            // 
            this.mi_CustLastPrices.Name = "mi_CustLastPrices";
            resources.ApplyResources(this.mi_CustLastPrices, "mi_CustLastPrices");
            this.mi_CustLastPrices.Click += new System.EventHandler(this.mi_CustLastPrices_Click);
            // 
            // mi_LastPrices
            // 
            this.mi_LastPrices.Name = "mi_LastPrices";
            resources.ApplyResources(this.mi_LastPrices, "mi_LastPrices");
            this.mi_LastPrices.Click += new System.EventHandler(this.mi_LastPrices_Click);
            // 
            // mi_PasteRows
            // 
            this.mi_PasteRows.Name = "mi_PasteRows";
            resources.ApplyResources(this.mi_PasteRows, "mi_PasteRows");
            this.mi_PasteRows.Click += new System.EventHandler(this.mi_PasteRows_Click);
            // 
            // mi_ExportData
            // 
            this.mi_ExportData.Name = "mi_ExportData";
            resources.ApplyResources(this.mi_ExportData, "mi_ExportData");
            this.mi_ExportData.Click += new System.EventHandler(this.mi_ExportData_Click);
            // 
            // mi_InvoiceStaticDisc
            // 
            this.mi_InvoiceStaticDisc.Name = "mi_InvoiceStaticDisc";
            resources.ApplyResources(this.mi_InvoiceStaticDisc, "mi_InvoiceStaticDisc");
            this.mi_InvoiceStaticDisc.Click += new System.EventHandler(this.mi_InvoiceStaticDisc_Click);
            // 
            // mi_InvoiceStaticDimensions
            // 
            this.mi_InvoiceStaticDimensions.Name = "mi_InvoiceStaticDimensions";
            resources.ApplyResources(this.mi_InvoiceStaticDimensions, "mi_InvoiceStaticDimensions");
            this.mi_InvoiceStaticDimensions.Click += new System.EventHandler(this.mi_InvoiceStaticDimensions_Click);
            // 
            // mi_ImportExcel
            // 
            this.mi_ImportExcel.Name = "mi_ImportExcel";
            resources.ApplyResources(this.mi_ImportExcel, "mi_ImportExcel");
            this.mi_ImportExcel.Click += new System.EventHandler(this.mi_ImportExcel_Click);
            // 
            // gridView2
            // 
            this.gridView2.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView2.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView2.Appearance.HeaderPanel.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridView2.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView2.Appearance.Row.Options.UseTextOptions = true;
            this.gridView2.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView2.Appearance.Row.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridView2.Appearance.Row.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView2.ColumnPanelRowHeight = 33;
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.DiscountTaxRatio,
            this.DiscountTax,
            this.colETaxValue,
            this.colETaxRatio,
            this.Col_ETaxType,
            this.colBonusDiscount,
            this.Col_Company,
            this.Col_Category,
            this.col_QC,
            this.col_ActualPiecesCount,
            this.gridColumn29,
            this.gridColumn28,
            this.col_TotalSellPrice,
            this.col_CurrentQty,
            this.col_CommercialDiscountValue,
            this.gridColumn1,
            this.col_SellPrice,
            this.col_AudiencePrice,
            this.colPurchasePrice,
            this.gridColumn7,
            this.gridColumn8,
            this.col_ItemNameF,
            this.gridColumn10,
            this.grdcol_branch,
            this.gridColumn11,
            this.gridColumn31,
            this.gridColumn41,
            this.col_Expire,
            this.col_Batch,
            this.col_Length,
            this.col_Width,
            this.col_Height,
            this.col_TotalQty,
            this.col_PiecesCount,
            this.col_ItemDescription,
            this.col_ItemDescriptionEn,
            this.col_SalesTax,
            this.col_DiscountRatio2,
            this.col_DiscountRatio3,
            this.col_Serial,
            this.col_Serial2,
            this.gridColumn32,
            this.col_CusTax,
            this.col_Location,
            this.col_Libra,
            this.gridColumn36,
            this.gridColumn37,
            this.gridColumn38,
            this.col_kg_Weight_libra,
            this.gridColumn39,
            this.ol_Index,
            this.col_itemperoffer,
            this.col_Pack,
            this.btn_AddTaxes,
            this.TotalTaxes,
            this.totalTaxesRatio,
            this.salePriceWithTaxTable,
            this.totalTableTaxes,
            this.addTaxValue,
            this.tableTaxValue,
            this.col_TaxValue,
            this.col_TotalSubCustomTax,
            this.col_TotalSubAddTax,
            this.col_TotalSubDiscountTax});
            this.gridView2.CustomizationFormBounds = new System.Drawing.Rectangle(982, 107, 216, 388);
            this.gridView2.GridControl = this.grdPrInvoice;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView2.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.gridView2.OptionsView.EnableAppearanceOddRow = true;
            this.gridView2.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Bottom;
            this.gridView2.OptionsView.RowAutoHeight = true;
            this.gridView2.OptionsView.ShowFooter = true;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            this.gridView2.RowStyle += new DevExpress.XtraGrid.Views.Grid.RowStyleEventHandler(this.gridView2_RowStyle);
            this.gridView2.HideCustomizationForm += new System.EventHandler(this.gridView2_HideCustomizationForm);
            this.gridView2.ShowingEditor += new System.ComponentModel.CancelEventHandler(this.gridView2_ShowingEditor);
            this.gridView2.InitNewRow += new DevExpress.XtraGrid.Views.Grid.InitNewRowEventHandler(this.gridView2_InitNewRow);
            this.gridView2.FocusedRowChanged += new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventHandler(this.gridView1_FocusedRowChanged);
            this.gridView2.FocusedColumnChanged += new DevExpress.XtraGrid.Views.Base.FocusedColumnChangedEventHandler(this.gridView1_FocusedColumnChanged);
            this.gridView2.CellValueChanged += new DevExpress.XtraGrid.Views.Base.CellValueChangedEventHandler(this.gridView1_CellValueChanged);
            this.gridView2.InvalidRowException += new DevExpress.XtraGrid.Views.Base.InvalidRowExceptionEventHandler(this.gridView1_InvalidRowException);
            this.gridView2.ValidateRow += new DevExpress.XtraGrid.Views.Base.ValidateRowEventHandler(this.gridView1_ValidateRow);
            this.gridView2.CustomUnboundColumnData += new DevExpress.XtraGrid.Views.Base.CustomColumnDataEventHandler(this.gridView2_CustomUnboundColumnData);
            this.gridView2.CustomColumnDisplayText += new DevExpress.XtraGrid.Views.Base.CustomColumnDisplayTextEventHandler(this.gridView1_CustomColumnDisplayText);
            this.gridView2.KeyDown += new System.Windows.Forms.KeyEventHandler(this.gridView1_KeyDown);
            // 
            // DiscountTaxRatio
            // 
            this.DiscountTaxRatio.FieldName = "DiscountTaxRatio";
            this.DiscountTaxRatio.Name = "DiscountTaxRatio";
            this.DiscountTaxRatio.OptionsColumn.AllowEdit = false;
            // 
            // DiscountTax
            // 
            this.DiscountTax.FieldName = "DiscountTax";
            this.DiscountTax.Name = "DiscountTax";
            this.DiscountTax.OptionsColumn.AllowEdit = false;
            // 
            // colETaxValue
            // 
            resources.ApplyResources(this.colETaxValue, "colETaxValue");
            this.colETaxValue.ColumnEdit = this.repSpin;
            this.colETaxValue.FieldName = "EtaxValue";
            this.colETaxValue.Name = "colETaxValue";
            this.colETaxValue.OptionsColumn.AllowFocus = false;
            this.colETaxValue.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colETaxValue.OptionsColumn.ReadOnly = true;
            this.colETaxValue.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("colETaxValue.Summary"))), resources.GetString("colETaxValue.Summary1"), resources.GetString("colETaxValue.Summary2"))});
            // 
            // repSpin
            // 
            resources.ApplyResources(this.repSpin, "repSpin");
            this.repSpin.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.repSpin.Mask.EditMask = resources.GetString("repSpin.Mask.EditMask");
            this.repSpin.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repSpin.Mask.IgnoreMaskBlank")));
            this.repSpin.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repSpin.Mask.MaskType")));
            this.repSpin.Mask.SaveLiteral = ((bool)(resources.GetObject("repSpin.Mask.SaveLiteral")));
            this.repSpin.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repSpin.Mask.ShowPlaceHolders")));
            this.repSpin.MaxValue = new decimal(new int[] {
            1410065407,
            2,
            0,
            0});
            this.repSpin.Name = "repSpin";
            // 
            // colETaxRatio
            // 
            resources.ApplyResources(this.colETaxRatio, "colETaxRatio");
            this.colETaxRatio.ColumnEdit = this.repSpin;
            this.colETaxRatio.FieldName = "ETaxRatio";
            this.colETaxRatio.Name = "colETaxRatio";
            // 
            // Col_ETaxType
            // 
            resources.ApplyResources(this.Col_ETaxType, "Col_ETaxType");
            this.Col_ETaxType.ColumnEdit = this.repTaxTypes;
            this.Col_ETaxType.FieldName = "TaxType";
            this.Col_ETaxType.Name = "Col_ETaxType";
            // 
            // repTaxTypes
            // 
            this.repTaxTypes.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            resources.ApplyResources(this.repTaxTypes, "repTaxTypes");
            this.repTaxTypes.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repTaxTypes.Buttons"))))});
            this.repTaxTypes.Name = "repTaxTypes";
            this.repTaxTypes.View = this.gridView9;
            // 
            // gridView9
            // 
            this.gridView9.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.ColDescriptionAr,
            this.ColCode,
            this.colE_TaxableTypeId});
            this.gridView9.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView9.Name = "gridView9";
            this.gridView9.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView9.OptionsView.ShowGroupPanel = false;
            // 
            // ColDescriptionAr
            // 
            resources.ApplyResources(this.ColDescriptionAr, "ColDescriptionAr");
            this.ColDescriptionAr.FieldName = "DescriptionAr";
            this.ColDescriptionAr.Name = "ColDescriptionAr";
            // 
            // ColCode
            // 
            resources.ApplyResources(this.ColCode, "ColCode");
            this.ColCode.FieldName = "Code";
            this.ColCode.Name = "ColCode";
            // 
            // colE_TaxableTypeId
            // 
            resources.ApplyResources(this.colE_TaxableTypeId, "colE_TaxableTypeId");
            this.colE_TaxableTypeId.FieldName = "E_TaxableTypeId";
            this.colE_TaxableTypeId.Name = "colE_TaxableTypeId";
            // 
            // colBonusDiscount
            // 
            resources.ApplyResources(this.colBonusDiscount, "colBonusDiscount");
            this.colBonusDiscount.ColumnEdit = this.repSpin;
            this.colBonusDiscount.FieldName = "bonusDiscount";
            this.colBonusDiscount.Name = "colBonusDiscount";
            this.colBonusDiscount.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("colBonusDiscount.Summary"))), resources.GetString("colBonusDiscount.Summary1"), resources.GetString("colBonusDiscount.Summary2"))});
            // 
            // Col_Company
            // 
            this.Col_Company.ColumnEdit = this.repCompany;
            this.Col_Company.FieldName = "Company";
            this.Col_Company.Name = "Col_Company";
            // 
            // repCompany
            // 
            this.repCompany.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repCompany.Buttons"))))});
            this.repCompany.DisplayMember = "CompanyNameAr";
            this.repCompany.Name = "repCompany";
            resources.ApplyResources(this.repCompany, "repCompany");
            this.repCompany.ValueMember = "CompanyId";
            this.repCompany.View = this.gridView8;
            // 
            // gridView8
            // 
            this.gridView8.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.ColCompanyNameAr,
            this.Col_CompanyCode,
            this.CompanyId});
            this.gridView8.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView8.Name = "gridView8";
            this.gridView8.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView8.OptionsView.ShowGroupPanel = false;
            // 
            // ColCompanyNameAr
            // 
            this.ColCompanyNameAr.FieldName = "CompanyNameAr";
            this.ColCompanyNameAr.Name = "ColCompanyNameAr";
            resources.ApplyResources(this.ColCompanyNameAr, "ColCompanyNameAr");
            // 
            // Col_CompanyCode
            // 
            this.Col_CompanyCode.FieldName = "CompanyCode";
            this.Col_CompanyCode.Name = "Col_CompanyCode";
            resources.ApplyResources(this.Col_CompanyCode, "Col_CompanyCode");
            // 
            // CompanyId
            // 
            this.CompanyId.FieldName = "CompanyId";
            this.CompanyId.Name = "CompanyId";
            // 
            // Col_Category
            // 
            this.Col_Category.ColumnEdit = this.repCategory;
            this.Col_Category.FieldName = "Category";
            this.Col_Category.Name = "Col_Category";
            // 
            // repCategory
            // 
            this.repCategory.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repCategory.Buttons"))))});
            this.repCategory.DisplayMember = "CategoryNameAr";
            this.repCategory.Name = "repCategory";
            resources.ApplyResources(this.repCategory, "repCategory");
            this.repCategory.ValueMember = "CategoryId";
            this.repCategory.View = this.repositoryItemGridLookUpEdit2View;
            // 
            // repositoryItemGridLookUpEdit2View
            // 
            this.repositoryItemGridLookUpEdit2View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.ColCatNumber,
            this.ColCategoryNameAr,
            this.ColCategoryId});
            this.repositoryItemGridLookUpEdit2View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.repositoryItemGridLookUpEdit2View.Name = "repositoryItemGridLookUpEdit2View";
            this.repositoryItemGridLookUpEdit2View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.repositoryItemGridLookUpEdit2View.OptionsView.ShowGroupPanel = false;
            // 
            // ColCatNumber
            // 
            resources.ApplyResources(this.ColCatNumber, "ColCatNumber");
            this.ColCatNumber.FieldName = "CatNumber";
            this.ColCatNumber.Name = "ColCatNumber";
            // 
            // ColCategoryNameAr
            // 
            resources.ApplyResources(this.ColCategoryNameAr, "ColCategoryNameAr");
            this.ColCategoryNameAr.FieldName = "CategoryNameAr";
            this.ColCategoryNameAr.Name = "ColCategoryNameAr";
            // 
            // ColCategoryId
            // 
            this.ColCategoryId.FieldName = "CategoryId";
            this.ColCategoryId.Name = "ColCategoryId";
            // 
            // col_QC
            // 
            resources.ApplyResources(this.col_QC, "col_QC");
            this.col_QC.FieldName = "QC";
            this.col_QC.Name = "col_QC";
            // 
            // col_ActualPiecesCount
            // 
            resources.ApplyResources(this.col_ActualPiecesCount, "col_ActualPiecesCount");
            this.col_ActualPiecesCount.FieldName = "ActualPiecesCount";
            this.col_ActualPiecesCount.Name = "col_ActualPiecesCount";
            // 
            // gridColumn29
            // 
            this.gridColumn29.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn29.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn29.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn29.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn29.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn29.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn29.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn29.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn29, "gridColumn29");
            this.gridColumn29.FieldName = "LargeUOMFactor";
            this.gridColumn29.Name = "gridColumn29";
            this.gridColumn29.OptionsColumn.AllowEdit = false;
            this.gridColumn29.OptionsColumn.AllowFocus = false;
            this.gridColumn29.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn29.OptionsColumn.ShowInCustomizationForm = false;
            this.gridColumn29.OptionsFilter.AllowFilter = false;
            // 
            // gridColumn28
            // 
            this.gridColumn28.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn28.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn28.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn28.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn28.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn28.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn28.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn28.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn28, "gridColumn28");
            this.gridColumn28.FieldName = "MediumUOMFactor";
            this.gridColumn28.Name = "gridColumn28";
            this.gridColumn28.OptionsColumn.AllowEdit = false;
            this.gridColumn28.OptionsColumn.AllowFocus = false;
            this.gridColumn28.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn28.OptionsColumn.ShowInCustomizationForm = false;
            this.gridColumn28.OptionsFilter.AllowFilter = false;
            // 
            // col_TotalSellPrice
            // 
            this.col_TotalSellPrice.AppearanceCell.Options.UseTextOptions = true;
            this.col_TotalSellPrice.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_TotalSellPrice.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_TotalSellPrice.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_TotalSellPrice.AppearanceHeader.Options.UseTextOptions = true;
            this.col_TotalSellPrice.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_TotalSellPrice.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_TotalSellPrice.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_TotalSellPrice, "col_TotalSellPrice");
            this.col_TotalSellPrice.FieldName = "TotalSellPrice";
            this.col_TotalSellPrice.Name = "col_TotalSellPrice";
            this.col_TotalSellPrice.OptionsColumn.AllowEdit = false;
            this.col_TotalSellPrice.OptionsColumn.AllowFocus = false;
            this.col_TotalSellPrice.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_TotalSellPrice.OptionsColumn.ReadOnly = true;
            this.col_TotalSellPrice.OptionsFilter.AllowFilter = false;
            this.col_TotalSellPrice.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_TotalSellPrice.Summary"))), resources.GetString("col_TotalSellPrice.Summary1"), resources.GetString("col_TotalSellPrice.Summary2"))});
            // 
            // col_CurrentQty
            // 
            this.col_CurrentQty.AppearanceCell.Options.UseTextOptions = true;
            this.col_CurrentQty.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_CurrentQty.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_CurrentQty.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_CurrentQty.AppearanceHeader.Options.UseTextOptions = true;
            this.col_CurrentQty.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_CurrentQty.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_CurrentQty.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_CurrentQty, "col_CurrentQty");
            this.col_CurrentQty.FieldName = "CurrentQty";
            this.col_CurrentQty.Name = "col_CurrentQty";
            this.col_CurrentQty.OptionsColumn.AllowEdit = false;
            this.col_CurrentQty.OptionsColumn.AllowFocus = false;
            this.col_CurrentQty.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_CurrentQty.OptionsFilter.AllowFilter = false;
            // 
            // col_CommercialDiscountValue
            // 
            this.col_CommercialDiscountValue.AppearanceCell.Options.UseTextOptions = true;
            this.col_CommercialDiscountValue.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_CommercialDiscountValue.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_CommercialDiscountValue.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_CommercialDiscountValue.AppearanceHeader.Options.UseTextOptions = true;
            this.col_CommercialDiscountValue.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_CommercialDiscountValue.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_CommercialDiscountValue.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_CommercialDiscountValue, "col_CommercialDiscountValue");
            this.col_CommercialDiscountValue.ColumnEdit = this.repSpin;
            this.col_CommercialDiscountValue.FieldName = "DiscountValue";
            this.col_CommercialDiscountValue.Name = "col_CommercialDiscountValue";
            this.col_CommercialDiscountValue.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_CommercialDiscountValue.OptionsFilter.AllowFilter = false;
            this.col_CommercialDiscountValue.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_CommercialDiscountValue.Summary"))), resources.GetString("col_CommercialDiscountValue.Summary1"), resources.GetString("col_CommercialDiscountValue.Summary2"))});
            // 
            // gridColumn1
            // 
            this.gridColumn1.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn1.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn1.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn1.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn1.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn1.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn1.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn1.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn1, "gridColumn1");
            this.gridColumn1.ColumnEdit = this.repDiscountRatio;
            this.gridColumn1.FieldName = "DiscountRatio";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn1.OptionsFilter.AllowFilter = false;
            // 
            // repDiscountRatio
            // 
            resources.ApplyResources(this.repDiscountRatio, "repDiscountRatio");
            this.repDiscountRatio.Mask.EditMask = resources.GetString("repDiscountRatio.Mask.EditMask");
            this.repDiscountRatio.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repDiscountRatio.Mask.IgnoreMaskBlank")));
            this.repDiscountRatio.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repDiscountRatio.Mask.MaskType")));
            this.repDiscountRatio.Mask.SaveLiteral = ((bool)(resources.GetObject("repDiscountRatio.Mask.SaveLiteral")));
            this.repDiscountRatio.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repDiscountRatio.Mask.ShowPlaceHolders")));
            this.repDiscountRatio.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repDiscountRatio.Mask.UseMaskAsDisplayFormat")));
            this.repDiscountRatio.Name = "repDiscountRatio";
            // 
            // col_SellPrice
            // 
            this.col_SellPrice.AppearanceCell.Options.UseTextOptions = true;
            this.col_SellPrice.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_SellPrice.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_SellPrice.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_SellPrice.AppearanceHeader.Options.UseTextOptions = true;
            this.col_SellPrice.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_SellPrice.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_SellPrice.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_SellPrice.ColumnEdit = this.repSpin;
            this.col_SellPrice.FieldName = "SellPrice";
            this.col_SellPrice.Name = "col_SellPrice";
            this.col_SellPrice.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_SellPrice.OptionsFilter.AllowFilter = false;
            resources.ApplyResources(this.col_SellPrice, "col_SellPrice");
            // 
            // col_AudiencePrice
            // 
            resources.ApplyResources(this.col_AudiencePrice, "col_AudiencePrice");
            this.col_AudiencePrice.ColumnEdit = this.repSpin;
            this.col_AudiencePrice.DisplayFormat.FormatString = "###.##";
            this.col_AudiencePrice.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_AudiencePrice.FieldName = "AudiencePrice";
            this.col_AudiencePrice.Name = "col_AudiencePrice";
            // 
            // colPurchasePrice
            // 
            this.colPurchasePrice.AppearanceCell.Options.UseTextOptions = true;
            this.colPurchasePrice.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.colPurchasePrice.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colPurchasePrice.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.colPurchasePrice.AppearanceHeader.Options.UseTextOptions = true;
            this.colPurchasePrice.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.colPurchasePrice.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colPurchasePrice.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.colPurchasePrice, "colPurchasePrice");
            this.colPurchasePrice.ColumnEdit = this.repSpin;
            this.colPurchasePrice.FieldName = "PurchasePrice";
            this.colPurchasePrice.Name = "colPurchasePrice";
            this.colPurchasePrice.OptionsColumn.AllowEdit = false;
            this.colPurchasePrice.OptionsColumn.AllowFocus = false;
            this.colPurchasePrice.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.colPurchasePrice.OptionsColumn.ReadOnly = true;
            this.colPurchasePrice.OptionsFilter.AllowFilter = false;
            // 
            // gridColumn7
            // 
            this.gridColumn7.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn7.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn7.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn7.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn7.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn7.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn7.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn7.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn7, "gridColumn7");
            this.gridColumn7.ColumnEdit = this.repSpin;
            this.gridColumn7.FieldName = "Qty";
            this.gridColumn7.Name = "gridColumn7";
            this.gridColumn7.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn7.OptionsFilter.AllowFilter = false;
            this.gridColumn7.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridColumn7.Summary"))), resources.GetString("gridColumn7.Summary1"), resources.GetString("gridColumn7.Summary2"))});
            // 
            // gridColumn8
            // 
            this.gridColumn8.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn8.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn8.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn8.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn8.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn8.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn8.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn8.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn8, "gridColumn8");
            this.gridColumn8.ColumnEdit = this.repUOM;
            this.gridColumn8.FieldName = "UOM";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn8.OptionsFilter.AllowFilter = false;
            // 
            // repUOM
            // 
            this.repUOM.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repUOM.Buttons"))))});
            this.repUOM.Name = "repUOM";
            resources.ApplyResources(this.repUOM, "repUOM");
            this.repUOM.View = this.gridView4;
            this.repUOM.CustomDisplayText += new DevExpress.XtraEditors.Controls.CustomDisplayTextEventHandler(this.repUOM_CustomDisplayText);
            // 
            // gridView4
            // 
            this.gridView4.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView4.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView4.Appearance.Row.Options.UseTextOptions = true;
            this.gridView4.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView4.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn9,
            this.gridColumn16,
            this.gridColumn17,
            this.gridColumn3});
            this.gridView4.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView4.Name = "gridView4";
            this.gridView4.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView4.OptionsView.ShowDetailButtons = false;
            this.gridView4.OptionsView.ShowGroupExpandCollapseButtons = false;
            this.gridView4.OptionsView.ShowGroupPanel = false;
            this.gridView4.OptionsView.ShowIndicator = false;
            // 
            // gridColumn9
            // 
            resources.ApplyResources(this.gridColumn9, "gridColumn9");
            this.gridColumn9.FieldName = "Index";
            this.gridColumn9.Name = "gridColumn9";
            // 
            // gridColumn16
            // 
            resources.ApplyResources(this.gridColumn16, "gridColumn16");
            this.gridColumn16.FieldName = "Factor";
            this.gridColumn16.Name = "gridColumn16";
            // 
            // gridColumn17
            // 
            resources.ApplyResources(this.gridColumn17, "gridColumn17");
            this.gridColumn17.FieldName = "Uom";
            this.gridColumn17.Name = "gridColumn17";
            // 
            // gridColumn3
            // 
            resources.ApplyResources(this.gridColumn3, "gridColumn3");
            this.gridColumn3.FieldName = "UomId";
            this.gridColumn3.Name = "gridColumn3";
            // 
            // col_ItemNameF
            // 
            resources.ApplyResources(this.col_ItemNameF, "col_ItemNameF");
            this.col_ItemNameF.FieldName = "ItemIdF";
            this.col_ItemNameF.Name = "col_ItemNameF";
            this.col_ItemNameF.OptionsColumn.ReadOnly = true;
            // 
            // gridColumn10
            // 
            this.gridColumn10.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn10.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn10.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn10.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn10.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn10.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn10.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn10.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn10, "gridColumn10");
            this.gridColumn10.ColumnEdit = this.repItems;
            this.gridColumn10.FieldName = "ItemId";
            this.gridColumn10.Name = "gridColumn10";
            this.gridColumn10.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn10.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.True;
            this.gridColumn10.OptionsFilter.AllowFilter = false;
            // 
            // repItems
            // 
            this.repItems.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.repItems.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.repItems.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repItems.Buttons"))))});
            this.repItems.DisplayMember = "ItemNameAr";
            this.repItems.ImmediatePopup = true;
            this.repItems.Name = "repItems";
            resources.ApplyResources(this.repItems, "repItems");
            this.repItems.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.repItems.ValueMember = "ItemId";
            this.repItems.View = this.repositoryItemGridLookUpEdit1View;
            this.repItems.Popup += new System.EventHandler(this.repItems_Popup);
            // 
            // repositoryItemGridLookUpEdit1View
            // 
            this.repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.repositoryItemGridLookUpEdit1View.Appearance.Row.Options.UseTextOptions = true;
            this.repositoryItemGridLookUpEdit1View.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.repositoryItemGridLookUpEdit1View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14,
            this.gridColumn15,
            this.gridColumn5,
            this.gridColumn4,
            this.gridColumn25,
            this.col_SellDiscountRatio,
            this.col_CompanyNameAr,
            this.col_CategoryNameAr,
            this.ColLength,
            this.ColWidth,
            this.ColHeight});
            this.repositoryItemGridLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.repositoryItemGridLookUpEdit1View.Name = "repositoryItemGridLookUpEdit1View";
            this.repositoryItemGridLookUpEdit1View.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.repositoryItemGridLookUpEdit1View.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.repositoryItemGridLookUpEdit1View.OptionsBehavior.AutoSelectAllInEditor = false;
            this.repositoryItemGridLookUpEdit1View.OptionsBehavior.AutoUpdateTotalSummary = false;
            this.repositoryItemGridLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.repositoryItemGridLookUpEdit1View.OptionsSelection.UseIndicatorForSelection = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.BestFitMaxRowCount = 10;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowAutoFilterRow = true;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowDetailButtons = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowGroupPanel = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowIndicator = false;
            // 
            // gridColumn12
            // 
            resources.ApplyResources(this.gridColumn12, "gridColumn12");
            this.gridColumn12.FieldName = "ItemNameEn";
            this.gridColumn12.Name = "gridColumn12";
            // 
            // gridColumn13
            // 
            resources.ApplyResources(this.gridColumn13, "gridColumn13");
            this.gridColumn13.FieldName = "ItemNameAr";
            this.gridColumn13.Name = "gridColumn13";
            // 
            // gridColumn14
            // 
            resources.ApplyResources(this.gridColumn14, "gridColumn14");
            this.gridColumn14.FieldName = "ItemCode1";
            this.gridColumn14.Name = "gridColumn14";
            this.gridColumn14.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // gridColumn15
            // 
            resources.ApplyResources(this.gridColumn15, "gridColumn15");
            this.gridColumn15.FieldName = "ItemId";
            this.gridColumn15.Name = "gridColumn15";
            // 
            // gridColumn5
            // 
            resources.ApplyResources(this.gridColumn5, "gridColumn5");
            this.gridColumn5.FieldName = "ItemCode2";
            this.gridColumn5.Name = "gridColumn5";
            this.gridColumn5.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // gridColumn4
            // 
            resources.ApplyResources(this.gridColumn4, "gridColumn4");
            this.gridColumn4.FieldName = "Description";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // gridColumn25
            // 
            resources.ApplyResources(this.gridColumn25, "gridColumn25");
            this.gridColumn25.DisplayFormat.FormatString = "n2";
            this.gridColumn25.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.gridColumn25.FieldName = "SellPrice";
            this.gridColumn25.Name = "gridColumn25";
            this.gridColumn25.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // col_SellDiscountRatio
            // 
            resources.ApplyResources(this.col_SellDiscountRatio, "col_SellDiscountRatio");
            this.col_SellDiscountRatio.DisplayFormat.FormatString = "n2";
            this.col_SellDiscountRatio.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_SellDiscountRatio.FieldName = "SellDiscountRatio";
            this.col_SellDiscountRatio.Name = "col_SellDiscountRatio";
            // 
            // col_CompanyNameAr
            // 
            resources.ApplyResources(this.col_CompanyNameAr, "col_CompanyNameAr");
            this.col_CompanyNameAr.FieldName = "CompanyNameAr";
            this.col_CompanyNameAr.Name = "col_CompanyNameAr";
            // 
            // col_CategoryNameAr
            // 
            resources.ApplyResources(this.col_CategoryNameAr, "col_CategoryNameAr");
            this.col_CategoryNameAr.FieldName = "CategoryNameAr";
            this.col_CategoryNameAr.Name = "col_CategoryNameAr";
            // 
            // ColLength
            // 
            this.ColLength.FieldName = "Length";
            this.ColLength.Name = "ColLength";
            // 
            // ColWidth
            // 
            this.ColWidth.FieldName = "Width";
            this.ColWidth.Name = "ColWidth";
            // 
            // ColHeight
            // 
            this.ColHeight.FieldName = "Height";
            this.ColHeight.Name = "ColHeight";
            // 
            // grdcol_branch
            // 
            resources.ApplyResources(this.grdcol_branch, "grdcol_branch");
            this.grdcol_branch.ColumnEdit = this.lkp_storee;
            this.grdcol_branch.FieldName = "StoreId";
            this.grdcol_branch.Name = "grdcol_branch";
            // 
            // lkp_storee
            // 
            this.lkp_storee.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_storee.Buttons"))))});
            this.lkp_storee.DisplayMember = "StoreNameAr";
            this.lkp_storee.Name = "lkp_storee";
            resources.ApplyResources(this.lkp_storee, "lkp_storee");
            this.lkp_storee.ValueMember = "StoreId";
            this.lkp_storee.View = this.gridView7;
            // 
            // gridView7
            // 
            this.gridView7.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView7.Name = "gridView7";
            this.gridView7.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView7.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView7.OptionsBehavior.AutoSelectAllInEditor = false;
            this.gridView7.OptionsBehavior.AutoUpdateTotalSummary = false;
            this.gridView7.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView7.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn11
            // 
            this.gridColumn11.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn11.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn11.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn11.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn11.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn11.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn11.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn11.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn11, "gridColumn11");
            this.gridColumn11.FieldName = "ItemCode2";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn11.OptionsFilter.AllowFilter = false;
            // 
            // gridColumn31
            // 
            this.gridColumn31.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn31.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn31.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn31.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn31.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn31.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn31.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn31.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn31, "gridColumn31");
            this.gridColumn31.FieldName = "ItemCode1";
            this.gridColumn31.Name = "gridColumn31";
            this.gridColumn31.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn31.OptionsFilter.AllowFilter = false;
            // 
            // gridColumn41
            // 
            resources.ApplyResources(this.gridColumn41, "gridColumn41");
            this.gridColumn41.FieldName = "UomIndex";
            this.gridColumn41.Name = "gridColumn41";
            this.gridColumn41.OptionsColumn.AllowEdit = false;
            this.gridColumn41.OptionsColumn.AllowFocus = false;
            this.gridColumn41.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.gridColumn41.OptionsColumn.ShowInCustomizationForm = false;
            this.gridColumn41.OptionsFilter.AllowFilter = false;
            // 
            // col_Expire
            // 
            this.col_Expire.AppearanceCell.Options.UseTextOptions = true;
            this.col_Expire.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Expire.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Expire.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_Expire.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Expire.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Expire.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Expire.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_Expire, "col_Expire");
            this.col_Expire.ColumnEdit = this.rep_expireDate;
            this.col_Expire.FieldName = "Expire";
            this.col_Expire.Name = "col_Expire";
            this.col_Expire.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_Expire.OptionsFilter.AllowFilter = false;
            // 
            // rep_expireDate
            // 
            resources.ApplyResources(this.rep_expireDate, "rep_expireDate");
            this.rep_expireDate.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.rep_expireDate.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_expireDate.Buttons"))))});
            this.rep_expireDate.Name = "rep_expireDate";
            this.rep_expireDate.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.rep_expireDate.View = this.gridView5;
            this.rep_expireDate.CustomDisplayText += new DevExpress.XtraEditors.Controls.CustomDisplayTextEventHandler(this.rep_expireDate_CustomDisplayText);
            // 
            // gridView5
            // 
            this.gridView5.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn18,
            this.gridColumn23,
            this.gridColumn24});
            this.gridView5.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView5.Name = "gridView5";
            this.gridView5.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView5.OptionsView.ShowGroupPanel = false;
            this.gridView5.CustomColumnDisplayText += new DevExpress.XtraGrid.Views.Base.CustomColumnDisplayTextEventHandler(this.gridView5_CustomColumnDisplayText);
            // 
            // gridColumn18
            // 
            resources.ApplyResources(this.gridColumn18, "gridColumn18");
            this.gridColumn18.FieldName = "Expire";
            this.gridColumn18.Name = "gridColumn18";
            // 
            // gridColumn23
            // 
            resources.ApplyResources(this.gridColumn23, "gridColumn23");
            this.gridColumn23.FieldName = "Batch";
            this.gridColumn23.Name = "gridColumn23";
            // 
            // gridColumn24
            // 
            resources.ApplyResources(this.gridColumn24, "gridColumn24");
            this.gridColumn24.FieldName = "Qty";
            this.gridColumn24.Name = "gridColumn24";
            // 
            // col_Batch
            // 
            this.col_Batch.AppearanceCell.Options.UseTextOptions = true;
            this.col_Batch.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Batch.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Batch.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_Batch.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Batch.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Batch.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Batch.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_Batch, "col_Batch");
            this.col_Batch.ColumnEdit = this.rep_Batch;
            this.col_Batch.FieldName = "Batch";
            this.col_Batch.Name = "col_Batch";
            this.col_Batch.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_Batch.OptionsFilter.AllowFilter = false;
            // 
            // rep_Batch
            // 
            resources.ApplyResources(this.rep_Batch, "rep_Batch");
            this.rep_Batch.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Batch.Buttons"))))});
            this.rep_Batch.Name = "rep_Batch";
            this.rep_Batch.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.rep_Batch.View = this.gridView6;
            // 
            // gridView6
            // 
            this.gridView6.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn33,
            this.gridColumn34});
            this.gridView6.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView6.Name = "gridView6";
            this.gridView6.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView6.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn33
            // 
            resources.ApplyResources(this.gridColumn33, "gridColumn33");
            this.gridColumn33.FieldName = "Batch";
            this.gridColumn33.Name = "gridColumn33";
            // 
            // gridColumn34
            // 
            resources.ApplyResources(this.gridColumn34, "gridColumn34");
            this.gridColumn34.FieldName = "Qty";
            this.gridColumn34.Name = "gridColumn34";
            // 
            // col_Length
            // 
            resources.ApplyResources(this.col_Length, "col_Length");
            this.col_Length.ColumnEdit = this.repSpin;
            this.col_Length.FieldName = "Length";
            this.col_Length.Name = "col_Length";
            this.col_Length.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_Length.OptionsFilter.AllowFilter = false;
            // 
            // col_Width
            // 
            resources.ApplyResources(this.col_Width, "col_Width");
            this.col_Width.ColumnEdit = this.repSpin;
            this.col_Width.FieldName = "Width";
            this.col_Width.Name = "col_Width";
            this.col_Width.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_Width.OptionsFilter.AllowFilter = false;
            // 
            // col_Height
            // 
            resources.ApplyResources(this.col_Height, "col_Height");
            this.col_Height.ColumnEdit = this.repSpin;
            this.col_Height.FieldName = "Height";
            this.col_Height.Name = "col_Height";
            this.col_Height.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_Height.OptionsFilter.AllowFilter = false;
            // 
            // col_TotalQty
            // 
            resources.ApplyResources(this.col_TotalQty, "col_TotalQty");
            this.col_TotalQty.FieldName = "TotalQty";
            this.col_TotalQty.Name = "col_TotalQty";
            this.col_TotalQty.OptionsColumn.AllowEdit = false;
            this.col_TotalQty.OptionsColumn.AllowFocus = false;
            this.col_TotalQty.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_TotalQty.OptionsFilter.AllowFilter = false;
            this.col_TotalQty.UnboundType = DevExpress.Data.UnboundColumnType.Object;
            // 
            // col_PiecesCount
            // 
            this.col_PiecesCount.AppearanceCell.Options.UseTextOptions = true;
            this.col_PiecesCount.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_PiecesCount.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_PiecesCount.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_PiecesCount.AppearanceHeader.Options.UseTextOptions = true;
            this.col_PiecesCount.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_PiecesCount.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_PiecesCount.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_PiecesCount, "col_PiecesCount");
            this.col_PiecesCount.ColumnEdit = this.repspin_PiecesCount;
            this.col_PiecesCount.FieldName = "PiecesCount";
            this.col_PiecesCount.Name = "col_PiecesCount";
            this.col_PiecesCount.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_PiecesCount.OptionsFilter.AllowFilter = false;
            this.col_PiecesCount.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_PiecesCount.Summary"))), resources.GetString("col_PiecesCount.Summary1"), resources.GetString("col_PiecesCount.Summary2"))});
            // 
            // repspin_PiecesCount
            // 
            this.repspin_PiecesCount.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.repspin_PiecesCount.Mask.EditMask = resources.GetString("repspin_PiecesCount.Mask.EditMask");
            this.repspin_PiecesCount.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repspin_PiecesCount.Mask.IgnoreMaskBlank")));
            this.repspin_PiecesCount.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repspin_PiecesCount.Mask.MaskType")));
            this.repspin_PiecesCount.Mask.SaveLiteral = ((bool)(resources.GetObject("repspin_PiecesCount.Mask.SaveLiteral")));
            this.repspin_PiecesCount.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repspin_PiecesCount.Mask.ShowPlaceHolders")));
            this.repspin_PiecesCount.Name = "repspin_PiecesCount";
            // 
            // col_ItemDescription
            // 
            this.col_ItemDescription.AppearanceCell.Options.UseTextOptions = true;
            this.col_ItemDescription.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_ItemDescription.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_ItemDescription.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_ItemDescription.AppearanceHeader.Options.UseTextOptions = true;
            this.col_ItemDescription.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_ItemDescription.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_ItemDescription.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_ItemDescription, "col_ItemDescription");
            this.col_ItemDescription.FieldName = "ItemDescription";
            this.col_ItemDescription.Name = "col_ItemDescription";
            this.col_ItemDescription.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.True;
            // 
            // col_ItemDescriptionEn
            // 
            this.col_ItemDescriptionEn.AppearanceCell.Options.UseTextOptions = true;
            this.col_ItemDescriptionEn.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_ItemDescriptionEn.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_ItemDescriptionEn.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_ItemDescriptionEn.AppearanceHeader.Options.UseTextOptions = true;
            this.col_ItemDescriptionEn.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_ItemDescriptionEn.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_ItemDescriptionEn.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_ItemDescriptionEn, "col_ItemDescriptionEn");
            this.col_ItemDescriptionEn.FieldName = "ItemDescriptionEn";
            this.col_ItemDescriptionEn.Name = "col_ItemDescriptionEn";
            this.col_ItemDescriptionEn.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.True;
            this.col_ItemDescriptionEn.OptionsFilter.AllowFilter = false;
            // 
            // col_SalesTax
            // 
            resources.ApplyResources(this.col_SalesTax, "col_SalesTax");
            this.col_SalesTax.ColumnEdit = this.repSpin;
            this.col_SalesTax.FieldName = "SalesTax";
            this.col_SalesTax.Name = "col_SalesTax";
            this.col_SalesTax.OptionsColumn.AllowEdit = false;
            this.col_SalesTax.OptionsColumn.AllowFocus = false;
            this.col_SalesTax.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_SalesTax.OptionsFilter.AllowFilter = false;
            // 
            // col_DiscountRatio2
            // 
            resources.ApplyResources(this.col_DiscountRatio2, "col_DiscountRatio2");
            this.col_DiscountRatio2.ColumnEdit = this.repDiscountRatio;
            this.col_DiscountRatio2.FieldName = "DiscountRatio2";
            this.col_DiscountRatio2.Name = "col_DiscountRatio2";
            this.col_DiscountRatio2.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_DiscountRatio2.OptionsFilter.AllowFilter = false;
            // 
            // col_DiscountRatio3
            // 
            resources.ApplyResources(this.col_DiscountRatio3, "col_DiscountRatio3");
            this.col_DiscountRatio3.ColumnEdit = this.repDiscountRatio;
            this.col_DiscountRatio3.FieldName = "DiscountRatio3";
            this.col_DiscountRatio3.Name = "col_DiscountRatio3";
            this.col_DiscountRatio3.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            this.col_DiscountRatio3.OptionsFilter.AllowFilter = false;
            // 
            // col_Serial
            // 
            this.col_Serial.AppearanceCell.Options.UseTextOptions = true;
            this.col_Serial.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Serial.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Serial.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.col_Serial, "col_Serial");
            this.col_Serial.FieldName = "Serial";
            this.col_Serial.Name = "col_Serial";
            this.col_Serial.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            // 
            // col_Serial2
            // 
            this.col_Serial2.AppearanceCell.Options.UseTextOptions = true;
            this.col_Serial2.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Serial2.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Serial2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.col_Serial2, "col_Serial2");
            this.col_Serial2.FieldName = "Serial2";
            this.col_Serial2.Name = "col_Serial2";
            // 
            // gridColumn32
            // 
            this.gridColumn32.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn32.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn32.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn32.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn32.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn32.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn32.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn32.ColumnEdit = this.repManufactureDate;
            this.gridColumn32.FieldName = "ManufactureDate";
            this.gridColumn32.Name = "gridColumn32";
            // 
            // repManufactureDate
            // 
            this.repManufactureDate.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repManufactureDate.Buttons"))))});
            this.repManufactureDate.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("repManufactureDate.CalendarTimeProperties.AutoHeight")));
            this.repManufactureDate.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repManufactureDate.CalendarTimeProperties.Buttons"))))});
            this.repManufactureDate.CalendarTimeProperties.Mask.EditMask = resources.GetString("repManufactureDate.CalendarTimeProperties.Mask.EditMask");
            this.repManufactureDate.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repManufactureDate.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.repManufactureDate.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repManufactureDate.CalendarTimeProperties.Mask.MaskType")));
            this.repManufactureDate.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("repManufactureDate.CalendarTimeProperties.Mask.SaveLiteral")));
            this.repManufactureDate.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repManufactureDate.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.repManufactureDate.CalendarTimeProperties.NullValuePrompt = resources.GetString("repManufactureDate.CalendarTimeProperties.NullValuePrompt");
            this.repManufactureDate.Mask.EditMask = resources.GetString("repManufactureDate.Mask.EditMask");
            this.repManufactureDate.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repManufactureDate.Mask.IgnoreMaskBlank")));
            this.repManufactureDate.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repManufactureDate.Mask.MaskType")));
            this.repManufactureDate.Mask.SaveLiteral = ((bool)(resources.GetObject("repManufactureDate.Mask.SaveLiteral")));
            this.repManufactureDate.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repManufactureDate.Mask.ShowPlaceHolders")));
            this.repManufactureDate.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repManufactureDate.Mask.UseMaskAsDisplayFormat")));
            this.repManufactureDate.Name = "repManufactureDate";
            // 
            // col_CusTax
            // 
            resources.ApplyResources(this.col_CusTax, "col_CusTax");
            this.col_CusTax.FieldName = "CustomTax";
            this.col_CusTax.Name = "col_CusTax";
            this.col_CusTax.OptionsColumn.AllowEdit = false;
            this.col_CusTax.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.False;
            // 
            // col_Location
            // 
            resources.ApplyResources(this.col_Location, "col_Location");
            this.col_Location.FieldName = "Location";
            this.col_Location.Name = "col_Location";
            this.col_Location.OptionsColumn.AllowEdit = false;
            this.col_Location.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.True;
            this.col_Location.OptionsColumn.ReadOnly = true;
            // 
            // col_Libra
            // 
            resources.ApplyResources(this.col_Libra, "col_Libra");
            this.col_Libra.ColumnEdit = this.rep_Libra;
            this.col_Libra.FieldName = "LibraQty";
            this.col_Libra.Name = "col_Libra";
            this.col_Libra.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_Libra.Summary"))), resources.GetString("col_Libra.Summary1"), resources.GetString("col_Libra.Summary2"))});
            // 
            // rep_Libra
            // 
            resources.ApplyResources(this.rep_Libra, "rep_Libra");
            this.rep_Libra.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Libra.Buttons"))))});
            this.rep_Libra.Mask.EditMask = resources.GetString("rep_Libra.Mask.EditMask");
            this.rep_Libra.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("rep_Libra.Mask.IgnoreMaskBlank")));
            this.rep_Libra.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("rep_Libra.Mask.MaskType")));
            this.rep_Libra.Mask.SaveLiteral = ((bool)(resources.GetObject("rep_Libra.Mask.SaveLiteral")));
            this.rep_Libra.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("rep_Libra.Mask.ShowPlaceHolders")));
            this.rep_Libra.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("rep_Libra.Mask.UseMaskAsDisplayFormat")));
            this.rep_Libra.Name = "rep_Libra";
            // 
            // gridColumn36
            // 
            resources.ApplyResources(this.gridColumn36, "gridColumn36");
            this.gridColumn36.FieldName = "IsOffer";
            this.gridColumn36.Name = "gridColumn36";
            this.gridColumn36.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // gridColumn37
            // 
            resources.ApplyResources(this.gridColumn37, "gridColumn37");
            this.gridColumn37.FieldName = "VariableWeight";
            this.gridColumn37.Name = "gridColumn37";
            this.gridColumn37.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // gridColumn38
            // 
            resources.ApplyResources(this.gridColumn38, "gridColumn38");
            this.gridColumn38.FieldName = "PricingWithSmall";
            this.gridColumn38.Name = "gridColumn38";
            this.gridColumn38.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // col_kg_Weight_libra
            // 
            resources.ApplyResources(this.col_kg_Weight_libra, "col_kg_Weight_libra");
            this.col_kg_Weight_libra.ColumnEdit = this.repspin_PiecesCount;
            this.col_kg_Weight_libra.FieldName = "kg_Weight_libra";
            this.col_kg_Weight_libra.Name = "col_kg_Weight_libra";
            this.col_kg_Weight_libra.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_kg_Weight_libra.Summary"))), resources.GetString("col_kg_Weight_libra.Summary1"), resources.GetString("col_kg_Weight_libra.Summary2"))});
            // 
            // gridColumn39
            // 
            this.gridColumn39.FieldName = "Is_Libra";
            this.gridColumn39.Name = "gridColumn39";
            this.gridColumn39.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // ol_Index
            // 
            resources.ApplyResources(this.ol_Index, "ol_Index");
            this.ol_Index.FieldName = "index";
            this.ol_Index.Name = "ol_Index";
            this.ol_Index.UnboundType = DevExpress.Data.UnboundColumnType.Integer;
            // 
            // col_itemperoffer
            // 
            this.col_itemperoffer.FieldName = "itemperoffer";
            this.col_itemperoffer.Name = "col_itemperoffer";
            this.col_itemperoffer.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // col_Pack
            // 
            resources.ApplyResources(this.col_Pack, "col_Pack");
            this.col_Pack.ColumnEdit = this.repSpin;
            this.col_Pack.FieldName = "Pack";
            this.col_Pack.Name = "col_Pack";
            this.col_Pack.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_Pack.Summary"))))});
            // 
            // btn_AddTaxes
            // 
            resources.ApplyResources(this.btn_AddTaxes, "btn_AddTaxes");
            this.btn_AddTaxes.ColumnEdit = this.rep_btnAddTaxes;
            this.btn_AddTaxes.FieldName = "btn_AddTaxes";
            this.btn_AddTaxes.Name = "btn_AddTaxes";
            this.btn_AddTaxes.ShowButtonMode = DevExpress.XtraGrid.Views.Base.ShowButtonModeEnum.ShowAlways;
            // 
            // rep_btnAddTaxes
            // 
            resources.ApplyResources(this.rep_btnAddTaxes, "rep_btnAddTaxes");
            this.rep_btnAddTaxes.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_btnAddTaxes.Buttons"))))});
            this.rep_btnAddTaxes.Name = "rep_btnAddTaxes";
            this.rep_btnAddTaxes.Click += new System.EventHandler(this.rep_btnAddTaxes_Click);
            // 
            // TotalTaxes
            // 
            this.TotalTaxes.AppearanceHeader.Options.UseTextOptions = true;
            this.TotalTaxes.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.TotalTaxes.AppearanceHeader.TextOptions.HotkeyPrefix = DevExpress.Utils.HKeyPrefix.Hide;
            this.TotalTaxes.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.TotalTaxes.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.TotalTaxes, "TotalTaxes");
            this.TotalTaxes.FieldName = "TotalTaxes";
            this.TotalTaxes.Name = "TotalTaxes";
            this.TotalTaxes.OptionsColumn.AllowEdit = false;
            this.TotalTaxes.OptionsColumn.ReadOnly = true;
            // 
            // totalTaxesRatio
            // 
            this.totalTaxesRatio.FieldName = "totalTaxesRatio";
            this.totalTaxesRatio.Name = "totalTaxesRatio";
            this.totalTaxesRatio.OptionsColumn.AllowEdit = false;
            this.totalTaxesRatio.OptionsColumn.ReadOnly = true;
            this.totalTaxesRatio.OptionsColumn.ShowInCustomizationForm = false;
            this.totalTaxesRatio.OptionsColumn.ShowInExpressionEditor = false;
            // 
            // salePriceWithTaxTable
            // 
            resources.ApplyResources(this.salePriceWithTaxTable, "salePriceWithTaxTable");
            this.salePriceWithTaxTable.FieldName = "salePriceWithTaxTable";
            this.salePriceWithTaxTable.Name = "salePriceWithTaxTable";
            // 
            // totalTableTaxes
            // 
            this.totalTableTaxes.FieldName = "totalTableTaxes";
            this.totalTableTaxes.Name = "totalTableTaxes";
            this.totalTableTaxes.OptionsColumn.ShowInCustomizationForm = false;
            this.totalTableTaxes.OptionsColumn.ShowInExpressionEditor = false;
            // 
            // addTaxValue
            // 
            resources.ApplyResources(this.addTaxValue, "addTaxValue");
            this.addTaxValue.FieldName = "addTaxValue";
            this.addTaxValue.Name = "addTaxValue";
            // 
            // tableTaxValue
            // 
            resources.ApplyResources(this.tableTaxValue, "tableTaxValue");
            this.tableTaxValue.FieldName = "tableTaxValue";
            this.tableTaxValue.Name = "tableTaxValue";
            // 
            // col_TaxValue
            // 
            this.col_TaxValue.FieldName = "TaxValue";
            this.col_TaxValue.Name = "col_TaxValue";
            // 
            // col_TotalSubCustomTax
            // 
            this.col_TotalSubCustomTax.FieldName = "TotalSubCustomTax";
            this.col_TotalSubCustomTax.Name = "col_TotalSubCustomTax";
            // 
            // col_TotalSubAddTax
            // 
            this.col_TotalSubAddTax.FieldName = "TotalSubAddTax";
            this.col_TotalSubAddTax.Name = "col_TotalSubAddTax";
            // 
            // col_TotalSubDiscountTax
            // 
            this.col_TotalSubDiscountTax.FieldName = "TotalSubDiscountTax";
            this.col_TotalSubDiscountTax.Name = "col_TotalSubDiscountTax";
            // 
            // lkp_store
            // 
            this.lkp_store.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_store.Buttons"))))});
            this.lkp_store.DisplayMember = "StoreNameAr";
            this.lkp_store.Name = "lkp_store";
            resources.ApplyResources(this.lkp_store, "lkp_store");
            this.lkp_store.ValueMember = "StoreId";
            // 
            // repositoryItemSpinEdit1
            // 
            resources.ApplyResources(this.repositoryItemSpinEdit1, "repositoryItemSpinEdit1");
            this.repositoryItemSpinEdit1.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repositoryItemSpinEdit1.Buttons"))))});
            this.repositoryItemSpinEdit1.Mask.EditMask = resources.GetString("repositoryItemSpinEdit1.Mask.EditMask");
            this.repositoryItemSpinEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemSpinEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemSpinEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemSpinEdit1.Mask.MaskType")));
            this.repositoryItemSpinEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemSpinEdit1.Mask.SaveLiteral")));
            this.repositoryItemSpinEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemSpinEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemSpinEdit1.Name = "repositoryItemSpinEdit1";
            // 
            // rep_Location
            // 
            resources.ApplyResources(this.rep_Location, "rep_Location");
            this.rep_Location.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Location.Buttons"))))});
            this.rep_Location.DisplayMember = "StoreNameAr";
            this.rep_Location.Name = "rep_Location";
            this.rep_Location.ValueMember = "StoreId";
            // 
            // txtNet
            // 
            resources.ApplyResources(this.txtNet, "txtNet");
            this.txtNet.EnterMoveNextControl = true;
            this.txtNet.Name = "txtNet";
            this.txtNet.Properties.Appearance.Options.UseTextOptions = true;
            this.txtNet.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtNet.Properties.AutoHeight = ((bool)(resources.GetObject("txtNet.Properties.AutoHeight")));
            this.txtNet.Properties.Mask.EditMask = resources.GetString("txtNet.Properties.Mask.EditMask");
            this.txtNet.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtNet.Properties.Mask.IgnoreMaskBlank")));
            this.txtNet.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtNet.Properties.Mask.SaveLiteral")));
            this.txtNet.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtNet.Properties.Mask.ShowPlaceHolders")));
            this.txtNet.Properties.NullValuePrompt = resources.GetString("txtNet.Properties.NullValuePrompt");
            this.txtNet.Properties.ReadOnly = true;
            this.txtNet.TabStop = false;
            this.txtNet.EditValueChanged += new System.EventHandler(this.txtNet_EditValueChanged);
            // 
            // labelControl13
            // 
            resources.ApplyResources(this.labelControl13, "labelControl13");
            this.labelControl13.Name = "labelControl13";
            // 
            // labelControl11
            // 
            resources.ApplyResources(this.labelControl11, "labelControl11");
            this.labelControl11.Name = "labelControl11";
            // 
            // txt_Total
            // 
            resources.ApplyResources(this.txt_Total, "txt_Total");
            this.txt_Total.EnterMoveNextControl = true;
            this.txt_Total.Name = "txt_Total";
            this.txt_Total.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_Total.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_Total.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Total.Properties.AutoHeight")));
            this.txt_Total.Properties.Mask.EditMask = resources.GetString("txt_Total.Properties.Mask.EditMask");
            this.txt_Total.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Total.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Total.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Total.Properties.Mask.SaveLiteral")));
            this.txt_Total.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Total.Properties.Mask.ShowPlaceHolders")));
            this.txt_Total.Properties.NullValuePrompt = resources.GetString("txt_Total.Properties.NullValuePrompt");
            this.txt_Total.TabStop = false;
            // 
            // labelControl18
            // 
            resources.ApplyResources(this.labelControl18, "labelControl18");
            this.labelControl18.Name = "labelControl18";
            // 
            // labelControl19
            // 
            resources.ApplyResources(this.labelControl19, "labelControl19");
            this.labelControl19.Name = "labelControl19";
            // 
            // btnAddCustomer
            // 
            resources.ApplyResources(this.btnAddCustomer, "btnAddCustomer");
            this.btnAddCustomer.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.btnAddCustomer.Image = global::Pharmacy.Properties.Resources.add32;
            this.btnAddCustomer.Name = "btnAddCustomer";
            this.btnAddCustomer.TabStop = false;
            this.btnAddCustomer.Click += new System.EventHandler(this.btnAddCustomer_Click);
            // 
            // grdLastPrices
            // 
            this.grdLastPrices.ContextMenuStrip = this.contextMenuStrip1;
            resources.ApplyResources(this.grdLastPrices, "grdLastPrices");
            this.grdLastPrices.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grdLastPrices.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grdLastPrices.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grdLastPrices.EmbeddedNavigator.Anchor")));
            this.grdLastPrices.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grdLastPrices.EmbeddedNavigator.BackgroundImageLayout")));
            this.grdLastPrices.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grdLastPrices.EmbeddedNavigator.ImeMode")));
            this.grdLastPrices.EmbeddedNavigator.Margin = ((System.Windows.Forms.Padding)(resources.GetObject("grdLastPrices.EmbeddedNavigator.Margin")));
            this.grdLastPrices.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grdLastPrices.EmbeddedNavigator.TextLocation")));
            this.grdLastPrices.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grdLastPrices.EmbeddedNavigator.ToolTipIconType")));
            this.grdLastPrices.MainView = this.gridView3;
            this.grdLastPrices.Name = "grdLastPrices";
            this.grdLastPrices.TabStop = false;
            this.grdLastPrices.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView3});
            // 
            // gridView3
            // 
            this.gridView3.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView3.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView3.Appearance.Row.Options.UseTextOptions = true;
            this.gridView3.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView3.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colTotalPurchasePrice,
            this.gridColumn6,
            this.colUOM,
            this.colQty,
            this.colCustNameAr,
            this.colInvoiceDate,
            this.colInvoiceCode});
            this.gridView3.GridControl = this.grdLastPrices;
            this.gridView3.HorzScrollStep = 2;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView3.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView3.OptionsBehavior.Editable = false;
            this.gridView3.OptionsCustomization.AllowColumnMoving = false;
            this.gridView3.OptionsCustomization.AllowFilter = false;
            this.gridView3.OptionsMenu.EnableColumnMenu = false;
            this.gridView3.OptionsMenu.EnableFooterMenu = false;
            this.gridView3.OptionsMenu.EnableGroupPanelMenu = false;
            this.gridView3.OptionsMenu.ShowDateTimeGroupIntervalItems = false;
            this.gridView3.OptionsMenu.ShowGroupSortSummaryItems = false;
            this.gridView3.OptionsNavigation.EnterMoveNextColumn = true;
            this.gridView3.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView3.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.gridView3.OptionsView.AnimationType = DevExpress.XtraGrid.Views.Base.GridAnimationType.NeverAnimate;
            this.gridView3.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView3.OptionsView.ShowDetailButtons = false;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            this.gridView3.OptionsView.ShowIndicator = false;
            this.gridView3.PaintStyleName = "UltraFlat";
            // 
            // colTotalPurchasePrice
            // 
            resources.ApplyResources(this.colTotalPurchasePrice, "colTotalPurchasePrice");
            this.colTotalPurchasePrice.FieldName = "TotalSellPrice";
            this.colTotalPurchasePrice.Name = "colTotalPurchasePrice";
            // 
            // gridColumn6
            // 
            resources.ApplyResources(this.gridColumn6, "gridColumn6");
            this.gridColumn6.FieldName = "SellPrice";
            this.gridColumn6.Name = "gridColumn6";
            // 
            // colUOM
            // 
            resources.ApplyResources(this.colUOM, "colUOM");
            this.colUOM.FieldName = "UOM";
            this.colUOM.Name = "colUOM";
            // 
            // colQty
            // 
            resources.ApplyResources(this.colQty, "colQty");
            this.colQty.FieldName = "Qty";
            this.colQty.Name = "colQty";
            // 
            // colCustNameAr
            // 
            resources.ApplyResources(this.colCustNameAr, "colCustNameAr");
            this.colCustNameAr.FieldName = "CusNameAr";
            this.colCustNameAr.Name = "colCustNameAr";
            // 
            // colInvoiceDate
            // 
            resources.ApplyResources(this.colInvoiceDate, "colInvoiceDate");
            this.colInvoiceDate.FieldName = "InvoiceDate";
            this.colInvoiceDate.Name = "colInvoiceDate";
            // 
            // colInvoiceCode
            // 
            resources.ApplyResources(this.colInvoiceCode, "colInvoiceCode");
            this.colInvoiceCode.FieldName = "InvoiceCode";
            this.colInvoiceCode.Name = "colInvoiceCode";
            // 
            // txtExpenses
            // 
            resources.ApplyResources(this.txtExpenses, "txtExpenses");
            this.txtExpenses.EnterMoveNextControl = true;
            this.txtExpenses.Name = "txtExpenses";
            this.txtExpenses.Properties.Appearance.Options.UseTextOptions = true;
            this.txtExpenses.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtExpenses.Properties.AutoHeight = ((bool)(resources.GetObject("txtExpenses.Properties.AutoHeight")));
            this.txtExpenses.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtExpenses.Properties.Mask.EditMask = resources.GetString("txtExpenses.Properties.Mask.EditMask");
            this.txtExpenses.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtExpenses.Properties.Mask.IgnoreMaskBlank")));
            this.txtExpenses.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtExpenses.Properties.Mask.MaskType")));
            this.txtExpenses.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtExpenses.Properties.Mask.SaveLiteral")));
            this.txtExpenses.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtExpenses.Properties.Mask.ShowPlaceHolders")));
            this.txtExpenses.Properties.NullValuePrompt = resources.GetString("txtExpenses.Properties.NullValuePrompt");
            this.txtExpenses.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txtExpenses.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txtExpenses.Leave += new System.EventHandler(this.txtDiscountValue_Leave);
            // 
            // labelControl1
            // 
            resources.ApplyResources(this.labelControl1, "labelControl1");
            this.labelControl1.Name = "labelControl1";
            // 
            // labelControl20
            // 
            resources.ApplyResources(this.labelControl20, "labelControl20");
            this.labelControl20.Name = "labelControl20";
            // 
            // txt_Balance_After
            // 
            resources.ApplyResources(this.txt_Balance_After, "txt_Balance_After");
            this.txt_Balance_After.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_Balance_After.Name = "txt_Balance_After";
            // 
            // txt_Balance_Before
            // 
            resources.ApplyResources(this.txt_Balance_Before, "txt_Balance_Before");
            this.txt_Balance_Before.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_Balance_Before.Name = "txt_Balance_Before";
            // 
            // txt_MaxCredit
            // 
            resources.ApplyResources(this.txt_MaxCredit, "txt_MaxCredit");
            this.txt_MaxCredit.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_MaxCredit.Name = "txt_MaxCredit";
            // 
            // lbl_IsCredit_After
            // 
            resources.ApplyResources(this.lbl_IsCredit_After, "lbl_IsCredit_After");
            this.lbl_IsCredit_After.Name = "lbl_IsCredit_After";
            // 
            // lblBlncAftr
            // 
            resources.ApplyResources(this.lblBlncAftr, "lblBlncAftr");
            this.lblBlncAftr.Name = "lblBlncAftr";
            // 
            // lbl_IsCredit_Before
            // 
            resources.ApplyResources(this.lbl_IsCredit_Before, "lbl_IsCredit_Before");
            this.lbl_IsCredit_Before.Name = "lbl_IsCredit_Before";
            // 
            // labelControl24
            // 
            resources.ApplyResources(this.labelControl24, "labelControl24");
            this.labelControl24.Name = "labelControl24";
            // 
            // labelControl10
            // 
            resources.ApplyResources(this.labelControl10, "labelControl10");
            this.labelControl10.Name = "labelControl10";
            // 
            // groupControl1
            // 
            resources.ApplyResources(this.groupControl1, "groupControl1");
            this.groupControl1.AppearanceCaption.Options.UseTextOptions = true;
            this.groupControl1.AppearanceCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.groupControl1.CaptionLocation = DevExpress.Utils.Locations.Right;
            this.groupControl1.Controls.Add(this.lkp_Drawers2);
            this.groupControl1.Controls.Add(this.txt_PayAcc1_Paid);
            this.groupControl1.Controls.Add(this.labelControl30);
            this.groupControl1.Controls.Add(this.labelControl31);
            this.groupControl1.Controls.Add(this.lkp_Drawers);
            this.groupControl1.Controls.Add(this.labelControl17);
            this.groupControl1.Controls.Add(this.txt_PayAcc2_Paid);
            this.groupControl1.Controls.Add(this.labelControl33);
            this.groupControl1.Controls.Add(this.txt_paid);
            this.groupControl1.Controls.Add(this.lbl_Paid);
            this.groupControl1.Controls.Add(this.lbl_remains);
            this.groupControl1.Controls.Add(this.txt_Remains);
            this.groupControl1.Name = "groupControl1";
            // 
            // lkp_Drawers2
            // 
            this.lkp_Drawers2.EnterMoveNextControl = true;
            resources.ApplyResources(this.lkp_Drawers2, "lkp_Drawers2");
            this.lkp_Drawers2.MenuManager = this.barManager1;
            this.lkp_Drawers2.Name = "lkp_Drawers2";
            this.lkp_Drawers2.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_Drawers2.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_Drawers2.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_Drawers2.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_Drawers2.Properties.AutoHeight")));
            this.lkp_Drawers2.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_Drawers2.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_Drawers2.Properties.Buttons"))))});
            this.lkp_Drawers2.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Drawers2.Properties.Columns"), resources.GetString("lkp_Drawers2.Properties.Columns1"), ((int)(resources.GetObject("lkp_Drawers2.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_Drawers2.Properties.Columns3"))), resources.GetString("lkp_Drawers2.Properties.Columns4"), ((bool)(resources.GetObject("lkp_Drawers2.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_Drawers2.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Drawers2.Properties.Columns7"), resources.GetString("lkp_Drawers2.Properties.Columns8"))});
            this.lkp_Drawers2.Properties.DisplayMember = "AccountNameEn";
            this.lkp_Drawers2.Properties.NullText = resources.GetString("lkp_Drawers2.Properties.NullText");
            this.lkp_Drawers2.Properties.NullValuePrompt = resources.GetString("lkp_Drawers2.Properties.NullValuePrompt");
            this.lkp_Drawers2.Properties.ValueMember = "AccountId";
            this.lkp_Drawers2.EditValueChanged += new System.EventHandler(this.lkp_Drawers2_EditValueChanged);
            this.lkp_Drawers2.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // txt_PayAcc1_Paid
            // 
            resources.ApplyResources(this.txt_PayAcc1_Paid, "txt_PayAcc1_Paid");
            this.txt_PayAcc1_Paid.EnterMoveNextControl = true;
            this.txt_PayAcc1_Paid.MenuManager = this.barManager1;
            this.txt_PayAcc1_Paid.Name = "txt_PayAcc1_Paid";
            this.txt_PayAcc1_Paid.Properties.AutoHeight = ((bool)(resources.GetObject("txt_PayAcc1_Paid.Properties.AutoHeight")));
            this.txt_PayAcc1_Paid.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txt_PayAcc1_Paid.Properties.Mask.EditMask = resources.GetString("txt_PayAcc1_Paid.Properties.Mask.EditMask");
            this.txt_PayAcc1_Paid.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_PayAcc1_Paid.Properties.Mask.IgnoreMaskBlank")));
            this.txt_PayAcc1_Paid.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_PayAcc1_Paid.Properties.Mask.MaskType")));
            this.txt_PayAcc1_Paid.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_PayAcc1_Paid.Properties.Mask.SaveLiteral")));
            this.txt_PayAcc1_Paid.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_PayAcc1_Paid.Properties.Mask.ShowPlaceHolders")));
            this.txt_PayAcc1_Paid.Properties.NullValuePrompt = resources.GetString("txt_PayAcc1_Paid.Properties.NullValuePrompt");
            this.txt_PayAcc1_Paid.EditValueChanged += new System.EventHandler(this.txt_PayAcc1_Paid_EditValueChanged);
            this.txt_PayAcc1_Paid.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // labelControl30
            // 
            resources.ApplyResources(this.labelControl30, "labelControl30");
            this.labelControl30.Name = "labelControl30";
            // 
            // labelControl31
            // 
            resources.ApplyResources(this.labelControl31, "labelControl31");
            this.labelControl31.Name = "labelControl31";
            // 
            // txt_PayAcc2_Paid
            // 
            resources.ApplyResources(this.txt_PayAcc2_Paid, "txt_PayAcc2_Paid");
            this.txt_PayAcc2_Paid.EnterMoveNextControl = true;
            this.txt_PayAcc2_Paid.Name = "txt_PayAcc2_Paid";
            this.txt_PayAcc2_Paid.Properties.AutoHeight = ((bool)(resources.GetObject("txt_PayAcc2_Paid.Properties.AutoHeight")));
            this.txt_PayAcc2_Paid.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txt_PayAcc2_Paid.Properties.Mask.EditMask = resources.GetString("txt_PayAcc2_Paid.Properties.Mask.EditMask");
            this.txt_PayAcc2_Paid.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_PayAcc2_Paid.Properties.Mask.IgnoreMaskBlank")));
            this.txt_PayAcc2_Paid.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_PayAcc2_Paid.Properties.Mask.MaskType")));
            this.txt_PayAcc2_Paid.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_PayAcc2_Paid.Properties.Mask.SaveLiteral")));
            this.txt_PayAcc2_Paid.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_PayAcc2_Paid.Properties.Mask.ShowPlaceHolders")));
            this.txt_PayAcc2_Paid.Properties.NullValuePrompt = resources.GetString("txt_PayAcc2_Paid.Properties.NullValuePrompt");
            this.txt_PayAcc2_Paid.EditValueChanged += new System.EventHandler(this.txt_PayAcc1_Paid_EditValueChanged);
            this.txt_PayAcc2_Paid.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // labelControl33
            // 
            resources.ApplyResources(this.labelControl33, "labelControl33");
            this.labelControl33.Name = "labelControl33";
            // 
            // xtraTabControl1
            // 
            resources.ApplyResources(this.xtraTabControl1, "xtraTabControl1");
            this.xtraTabControl1.AppearancePage.Header.Options.UseTextOptions = true;
            this.xtraTabControl1.AppearancePage.Header.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.page_AccInfo;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.page_JobOrder,
            this.Page_LastPrices,
            this.tabExtraData,
            this.tabpg_CustomerData,
            this.page_AccInfo});
            // 
            // page_AccInfo
            // 
            this.page_AccInfo.Controls.Add(this.btn_ShowAccStatement);
            this.page_AccInfo.Controls.Add(this.txt_Balance_After);
            this.page_AccInfo.Controls.Add(this.txt_Balance_Before);
            this.page_AccInfo.Controls.Add(this.lbl_Validate_MaxLimit);
            this.page_AccInfo.Controls.Add(this.txt_MaxCredit);
            this.page_AccInfo.Controls.Add(this.lbl_IsCredit_After);
            this.page_AccInfo.Controls.Add(this.lbl_IsCredit_Before);
            this.page_AccInfo.Controls.Add(this.lblBlncAftr);
            this.page_AccInfo.Controls.Add(this.labelControl10);
            this.page_AccInfo.Controls.Add(this.labelControl24);
            this.page_AccInfo.Controls.Add(this.groupControl1);
            this.page_AccInfo.Name = "page_AccInfo";
            resources.ApplyResources(this.page_AccInfo, "page_AccInfo");
            // 
            // btn_ShowAccStatement
            // 
            resources.ApplyResources(this.btn_ShowAccStatement, "btn_ShowAccStatement");
            this.btn_ShowAccStatement.Name = "btn_ShowAccStatement";
            this.btn_ShowAccStatement.TabStop = false;
            this.btn_ShowAccStatement.Click += new System.EventHandler(this.btn_ShowAccStatement_Click);
            // 
            // lbl_Validate_MaxLimit
            // 
            resources.ApplyResources(this.lbl_Validate_MaxLimit, "lbl_Validate_MaxLimit");
            this.lbl_Validate_MaxLimit.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("lbl_Validate_MaxLimit.Appearance.Font")));
            this.lbl_Validate_MaxLimit.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lbl_Validate_MaxLimit.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.lbl_Validate_MaxLimit.Name = "lbl_Validate_MaxLimit";
            // 
            // page_JobOrder
            // 
            this.page_JobOrder.Name = "page_JobOrder";
            this.page_JobOrder.PageVisible = false;
            resources.ApplyResources(this.page_JobOrder, "page_JobOrder");
            // 
            // Page_LastPrices
            // 
            this.Page_LastPrices.Controls.Add(this.grdLastPrices);
            this.Page_LastPrices.Name = "Page_LastPrices";
            resources.ApplyResources(this.Page_LastPrices, "Page_LastPrices");
            // 
            // tabExtraData
            // 
            this.tabExtraData.Controls.Add(this.lkp_Cars);
            this.tabExtraData.Controls.Add(this.txtScaleSerial);
            this.tabExtraData.Controls.Add(this.labelControl26);
            this.tabExtraData.Controls.Add(this.txtDestination);
            this.tabExtraData.Controls.Add(this.lblDestination);
            this.tabExtraData.Controls.Add(this.txtVehicleNumber);
            this.tabExtraData.Controls.Add(this.lblVehicleNumber);
            this.tabExtraData.Controls.Add(this.txtDriverName);
            this.tabExtraData.Controls.Add(this.lblDriverName);
            this.tabExtraData.Name = "tabExtraData";
            resources.ApplyResources(this.tabExtraData, "tabExtraData");
            // 
            // lkp_Cars
            // 
            this.lkp_Cars.EnterMoveNextControl = true;
            resources.ApplyResources(this.lkp_Cars, "lkp_Cars");
            this.lkp_Cars.MenuManager = this.barManager1;
            this.lkp_Cars.Name = "lkp_Cars";
            this.lkp_Cars.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkp_Cars.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_Cars.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_Cars.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_Cars.Properties.AutoHeight")));
            this.lkp_Cars.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_Cars.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_Cars.Properties.Buttons"))))});
            this.lkp_Cars.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Cars.Properties.Columns"), resources.GetString("lkp_Cars.Properties.Columns1"), ((int)(resources.GetObject("lkp_Cars.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_Cars.Properties.Columns3"))), resources.GetString("lkp_Cars.Properties.Columns4"), ((bool)(resources.GetObject("lkp_Cars.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_Cars.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Cars.Properties.Columns7"), resources.GetString("lkp_Cars.Properties.Columns8")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_Cars.Properties.Columns9"), resources.GetString("lkp_Cars.Properties.Columns10"))});
            this.lkp_Cars.Properties.DisplayMember = "AccountNameEn";
            this.lkp_Cars.Properties.NullText = resources.GetString("lkp_Cars.Properties.NullText");
            this.lkp_Cars.Properties.NullValuePrompt = resources.GetString("lkp_Cars.Properties.NullValuePrompt");
            this.lkp_Cars.Properties.ValueMember = "AccountId";
            this.lkp_Cars.EditValueChanged += new System.EventHandler(this.lkp_Cars_EditValueChanged);
            // 
            // txtScaleSerial
            // 
            resources.ApplyResources(this.txtScaleSerial, "txtScaleSerial");
            this.txtScaleSerial.EnterMoveNextControl = true;
            this.txtScaleSerial.MenuManager = this.barManager1;
            this.txtScaleSerial.Name = "txtScaleSerial";
            this.txtScaleSerial.Properties.AutoHeight = ((bool)(resources.GetObject("txtScaleSerial.Properties.AutoHeight")));
            this.txtScaleSerial.Properties.Mask.EditMask = resources.GetString("txtScaleSerial.Properties.Mask.EditMask");
            this.txtScaleSerial.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtScaleSerial.Properties.Mask.IgnoreMaskBlank")));
            this.txtScaleSerial.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtScaleSerial.Properties.Mask.SaveLiteral")));
            this.txtScaleSerial.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtScaleSerial.Properties.Mask.ShowPlaceHolders")));
            this.txtScaleSerial.Properties.MaxLength = 190;
            this.txtScaleSerial.Properties.NullValuePrompt = resources.GetString("txtScaleSerial.Properties.NullValuePrompt");
            // 
            // labelControl26
            // 
            resources.ApplyResources(this.labelControl26, "labelControl26");
            this.labelControl26.Name = "labelControl26";
            // 
            // txtDestination
            // 
            resources.ApplyResources(this.txtDestination, "txtDestination");
            this.txtDestination.EnterMoveNextControl = true;
            this.txtDestination.MenuManager = this.barManager1;
            this.txtDestination.Name = "txtDestination";
            this.txtDestination.Properties.AutoHeight = ((bool)(resources.GetObject("txtDestination.Properties.AutoHeight")));
            this.txtDestination.Properties.Mask.EditMask = resources.GetString("txtDestination.Properties.Mask.EditMask");
            this.txtDestination.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtDestination.Properties.Mask.IgnoreMaskBlank")));
            this.txtDestination.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtDestination.Properties.Mask.SaveLiteral")));
            this.txtDestination.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtDestination.Properties.Mask.ShowPlaceHolders")));
            this.txtDestination.Properties.MaxLength = 190;
            this.txtDestination.Properties.NullValuePrompt = resources.GetString("txtDestination.Properties.NullValuePrompt");
            // 
            // lblDestination
            // 
            resources.ApplyResources(this.lblDestination, "lblDestination");
            this.lblDestination.Name = "lblDestination";
            // 
            // txtVehicleNumber
            // 
            resources.ApplyResources(this.txtVehicleNumber, "txtVehicleNumber");
            this.txtVehicleNumber.EnterMoveNextControl = true;
            this.txtVehicleNumber.MenuManager = this.barManager1;
            this.txtVehicleNumber.Name = "txtVehicleNumber";
            this.txtVehicleNumber.Properties.AutoHeight = ((bool)(resources.GetObject("txtVehicleNumber.Properties.AutoHeight")));
            this.txtVehicleNumber.Properties.Mask.EditMask = resources.GetString("txtVehicleNumber.Properties.Mask.EditMask");
            this.txtVehicleNumber.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtVehicleNumber.Properties.Mask.IgnoreMaskBlank")));
            this.txtVehicleNumber.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtVehicleNumber.Properties.Mask.SaveLiteral")));
            this.txtVehicleNumber.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtVehicleNumber.Properties.Mask.ShowPlaceHolders")));
            this.txtVehicleNumber.Properties.MaxLength = 190;
            this.txtVehicleNumber.Properties.NullValuePrompt = resources.GetString("txtVehicleNumber.Properties.NullValuePrompt");
            // 
            // lblVehicleNumber
            // 
            resources.ApplyResources(this.lblVehicleNumber, "lblVehicleNumber");
            this.lblVehicleNumber.Name = "lblVehicleNumber";
            // 
            // txtDriverName
            // 
            resources.ApplyResources(this.txtDriverName, "txtDriverName");
            this.txtDriverName.EnterMoveNextControl = true;
            this.txtDriverName.MenuManager = this.barManager1;
            this.txtDriverName.Name = "txtDriverName";
            this.txtDriverName.Properties.AutoHeight = ((bool)(resources.GetObject("txtDriverName.Properties.AutoHeight")));
            this.txtDriverName.Properties.Mask.EditMask = resources.GetString("txtDriverName.Properties.Mask.EditMask");
            this.txtDriverName.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtDriverName.Properties.Mask.IgnoreMaskBlank")));
            this.txtDriverName.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtDriverName.Properties.Mask.SaveLiteral")));
            this.txtDriverName.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtDriverName.Properties.Mask.ShowPlaceHolders")));
            this.txtDriverName.Properties.MaxLength = 190;
            this.txtDriverName.Properties.NullValuePrompt = resources.GetString("txtDriverName.Properties.NullValuePrompt");
            // 
            // lblDriverName
            // 
            resources.ApplyResources(this.lblDriverName, "lblDriverName");
            this.lblDriverName.Name = "lblDriverName";
            // 
            // tabpg_CustomerData
            // 
            this.tabpg_CustomerData.Controls.Add(this.labelControl54);
            this.tabpg_CustomerData.Controls.Add(this.lkpDelivery);
            this.tabpg_CustomerData.Controls.Add(this.labelControl52);
            this.tabpg_CustomerData.Controls.Add(this.txt_Sales);
            this.tabpg_CustomerData.Controls.Add(this.txt_Address);
            this.tabpg_CustomerData.Controls.Add(this.labelControl51);
            this.tabpg_CustomerData.Controls.Add(this.txt_Phone);
            this.tabpg_CustomerData.Controls.Add(this.labelControl48);
            this.tabpg_CustomerData.Controls.Add(this.txt_Mobile);
            this.tabpg_CustomerData.Controls.Add(this.labelControl47);
            this.tabpg_CustomerData.Name = "tabpg_CustomerData";
            resources.ApplyResources(this.tabpg_CustomerData, "tabpg_CustomerData");
            // 
            // labelControl54
            // 
            resources.ApplyResources(this.labelControl54, "labelControl54");
            this.labelControl54.Name = "labelControl54";
            // 
            // lkpDelivery
            // 
            resources.ApplyResources(this.lkpDelivery, "lkpDelivery");
            this.lkpDelivery.EnterMoveNextControl = true;
            this.lkpDelivery.Name = "lkpDelivery";
            this.lkpDelivery.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkpDelivery.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("lkpDelivery.Properties.Appearance.BackColor")));
            this.lkpDelivery.Properties.Appearance.Options.UseBackColor = true;
            this.lkpDelivery.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpDelivery.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpDelivery.Properties.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.lkpDelivery.Properties.AppearanceDropDown.Options.UseTextOptions = true;
            this.lkpDelivery.Properties.AppearanceDropDown.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkpDelivery.Properties.AppearanceDropDownHeader.Options.UseTextOptions = true;
            this.lkpDelivery.Properties.AppearanceDropDownHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkpDelivery.Properties.AutoHeight = ((bool)(resources.GetObject("lkpDelivery.Properties.AutoHeight")));
            this.lkpDelivery.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkpDelivery.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpDelivery.Properties.Buttons"))))});
            this.lkpDelivery.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpDelivery.Properties.Columns"), resources.GetString("lkpDelivery.Properties.Columns1")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpDelivery.Properties.Columns2"), resources.GetString("lkpDelivery.Properties.Columns3")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpDelivery.Properties.Columns4"), resources.GetString("lkpDelivery.Properties.Columns5"))});
            this.lkpDelivery.Properties.DisplayMember = "CGNameAr";
            this.lkpDelivery.Properties.NullText = resources.GetString("lkpDelivery.Properties.NullText");
            this.lkpDelivery.Properties.NullValuePrompt = resources.GetString("lkpDelivery.Properties.NullValuePrompt");
            this.lkpDelivery.Properties.PopupSizeable = false;
            this.lkpDelivery.Properties.ValueMember = "CustomerGroupId";
            // 
            // labelControl52
            // 
            resources.ApplyResources(this.labelControl52, "labelControl52");
            this.labelControl52.Name = "labelControl52";
            // 
            // txt_Sales
            // 
            resources.ApplyResources(this.txt_Sales, "txt_Sales");
            this.txt_Sales.EnterMoveNextControl = true;
            this.txt_Sales.Name = "txt_Sales";
            this.txt_Sales.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.txt_Sales.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_Sales.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_Sales.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Sales.Properties.AutoHeight")));
            this.txt_Sales.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("txt_Sales.Properties.Buttons"))))});
            this.txt_Sales.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("txt_Sales.Properties.Columns"), resources.GetString("txt_Sales.Properties.Columns1")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("txt_Sales.Properties.Columns2"), resources.GetString("txt_Sales.Properties.Columns3")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("txt_Sales.Properties.Columns4"), resources.GetString("txt_Sales.Properties.Columns5"), ((int)(resources.GetObject("txt_Sales.Properties.Columns6"))), ((DevExpress.Utils.FormatType)(resources.GetObject("txt_Sales.Properties.Columns7"))), resources.GetString("txt_Sales.Properties.Columns8"), ((bool)(resources.GetObject("txt_Sales.Properties.Columns9"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("txt_Sales.Properties.Columns10"))))});
            this.txt_Sales.Properties.DisplayMember = "EmpName";
            this.txt_Sales.Properties.NullText = resources.GetString("txt_Sales.Properties.NullText");
            this.txt_Sales.Properties.NullValuePrompt = resources.GetString("txt_Sales.Properties.NullValuePrompt");
            this.txt_Sales.Properties.ReadOnly = true;
            this.txt_Sales.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.txt_Sales.Properties.ValueMember = "EmpId";
            this.txt_Sales.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // txt_Address
            // 
            resources.ApplyResources(this.txt_Address, "txt_Address");
            this.txt_Address.EnterMoveNextControl = true;
            this.txt_Address.Name = "txt_Address";
            this.txt_Address.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_Address.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_Address.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Address.Properties.AutoHeight")));
            this.txt_Address.Properties.Mask.EditMask = resources.GetString("txt_Address.Properties.Mask.EditMask");
            this.txt_Address.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Address.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Address.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Address.Properties.Mask.SaveLiteral")));
            this.txt_Address.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Address.Properties.Mask.ShowPlaceHolders")));
            this.txt_Address.Properties.NullValuePrompt = resources.GetString("txt_Address.Properties.NullValuePrompt");
            this.txt_Address.Properties.ReadOnly = true;
            this.txt_Address.TabStop = false;
            // 
            // labelControl51
            // 
            resources.ApplyResources(this.labelControl51, "labelControl51");
            this.labelControl51.Name = "labelControl51";
            // 
            // txt_Phone
            // 
            resources.ApplyResources(this.txt_Phone, "txt_Phone");
            this.txt_Phone.EnterMoveNextControl = true;
            this.txt_Phone.Name = "txt_Phone";
            this.txt_Phone.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_Phone.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_Phone.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Phone.Properties.AutoHeight")));
            this.txt_Phone.Properties.Mask.EditMask = resources.GetString("txt_Phone.Properties.Mask.EditMask");
            this.txt_Phone.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Phone.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Phone.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Phone.Properties.Mask.SaveLiteral")));
            this.txt_Phone.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Phone.Properties.Mask.ShowPlaceHolders")));
            this.txt_Phone.Properties.NullValuePrompt = resources.GetString("txt_Phone.Properties.NullValuePrompt");
            this.txt_Phone.Properties.ReadOnly = true;
            this.txt_Phone.TabStop = false;
            // 
            // labelControl48
            // 
            resources.ApplyResources(this.labelControl48, "labelControl48");
            this.labelControl48.Name = "labelControl48";
            // 
            // txt_Mobile
            // 
            resources.ApplyResources(this.txt_Mobile, "txt_Mobile");
            this.txt_Mobile.EnterMoveNextControl = true;
            this.txt_Mobile.Name = "txt_Mobile";
            this.txt_Mobile.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_Mobile.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_Mobile.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Mobile.Properties.AutoHeight")));
            this.txt_Mobile.Properties.Mask.EditMask = resources.GetString("txt_Mobile.Properties.Mask.EditMask");
            this.txt_Mobile.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Mobile.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Mobile.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Mobile.Properties.Mask.SaveLiteral")));
            this.txt_Mobile.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Mobile.Properties.Mask.ShowPlaceHolders")));
            this.txt_Mobile.Properties.NullValuePrompt = resources.GetString("txt_Mobile.Properties.NullValuePrompt");
            this.txt_Mobile.Properties.ReadOnly = true;
            this.txt_Mobile.TabStop = false;
            // 
            // labelControl47
            // 
            resources.ApplyResources(this.labelControl47, "labelControl47");
            this.labelControl47.Name = "labelControl47";
            // 
            // textEdit3
            // 
            resources.ApplyResources(this.textEdit3, "textEdit3");
            this.textEdit3.Name = "textEdit3";
            this.textEdit3.Properties.AutoHeight = ((bool)(resources.GetObject("textEdit3.Properties.AutoHeight")));
            this.textEdit3.Properties.Mask.EditMask = resources.GetString("textEdit3.Properties.Mask.EditMask");
            this.textEdit3.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("textEdit3.Properties.Mask.IgnoreMaskBlank")));
            this.textEdit3.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("textEdit3.Properties.Mask.SaveLiteral")));
            this.textEdit3.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("textEdit3.Properties.Mask.ShowPlaceHolders")));
            this.textEdit3.Properties.NullValuePrompt = resources.GetString("textEdit3.Properties.NullValuePrompt");
            // 
            // lkp_JOStatus
            // 
            resources.ApplyResources(this.lkp_JOStatus, "lkp_JOStatus");
            this.lkp_JOStatus.Name = "lkp_JOStatus";
            this.lkp_JOStatus.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_JOStatus.Properties.AutoHeight")));
            this.lkp_JOStatus.Properties.NullValuePrompt = resources.GetString("lkp_JOStatus.Properties.NullValuePrompt");
            // 
            // textEdit1
            // 
            resources.ApplyResources(this.textEdit1, "textEdit1");
            this.textEdit1.Name = "textEdit1";
            this.textEdit1.Properties.AutoHeight = ((bool)(resources.GetObject("textEdit1.Properties.AutoHeight")));
            this.textEdit1.Properties.Mask.EditMask = resources.GetString("textEdit1.Properties.Mask.EditMask");
            this.textEdit1.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("textEdit1.Properties.Mask.IgnoreMaskBlank")));
            this.textEdit1.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("textEdit1.Properties.Mask.SaveLiteral")));
            this.textEdit1.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("textEdit1.Properties.Mask.ShowPlaceHolders")));
            this.textEdit1.Properties.NullValuePrompt = resources.GetString("textEdit1.Properties.NullValuePrompt");
            // 
            // lkp_JOPriority
            // 
            resources.ApplyResources(this.lkp_JOPriority, "lkp_JOPriority");
            this.lkp_JOPriority.Name = "lkp_JOPriority";
            this.lkp_JOPriority.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_JOPriority.Properties.AutoHeight")));
            this.lkp_JOPriority.Properties.NullValuePrompt = resources.GetString("lkp_JOPriority.Properties.NullValuePrompt");
            // 
            // textEdit5
            // 
            resources.ApplyResources(this.textEdit5, "textEdit5");
            this.textEdit5.Name = "textEdit5";
            this.textEdit5.Properties.AutoHeight = ((bool)(resources.GetObject("textEdit5.Properties.AutoHeight")));
            this.textEdit5.Properties.Mask.EditMask = resources.GetString("textEdit5.Properties.Mask.EditMask");
            this.textEdit5.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("textEdit5.Properties.Mask.IgnoreMaskBlank")));
            this.textEdit5.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("textEdit5.Properties.Mask.SaveLiteral")));
            this.textEdit5.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("textEdit5.Properties.Mask.ShowPlaceHolders")));
            this.textEdit5.Properties.NullValuePrompt = resources.GetString("textEdit5.Properties.NullValuePrompt");
            // 
            // lkp_JOSalesEmp
            // 
            resources.ApplyResources(this.lkp_JOSalesEmp, "lkp_JOSalesEmp");
            this.lkp_JOSalesEmp.Name = "lkp_JOSalesEmp";
            this.lkp_JOSalesEmp.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_JOSalesEmp.Properties.AutoHeight")));
            this.lkp_JOSalesEmp.Properties.NullValuePrompt = resources.GetString("lkp_JOSalesEmp.Properties.NullValuePrompt");
            // 
            // textEdit8
            // 
            resources.ApplyResources(this.textEdit8, "textEdit8");
            this.textEdit8.Name = "textEdit8";
            this.textEdit8.Properties.AutoHeight = ((bool)(resources.GetObject("textEdit8.Properties.AutoHeight")));
            this.textEdit8.Properties.Mask.EditMask = resources.GetString("textEdit8.Properties.Mask.EditMask");
            this.textEdit8.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("textEdit8.Properties.Mask.IgnoreMaskBlank")));
            this.textEdit8.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("textEdit8.Properties.Mask.SaveLiteral")));
            this.textEdit8.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("textEdit8.Properties.Mask.ShowPlaceHolders")));
            this.textEdit8.Properties.NullValuePrompt = resources.GetString("textEdit8.Properties.NullValuePrompt");
            // 
            // lkp_JODept
            // 
            resources.ApplyResources(this.lkp_JODept, "lkp_JODept");
            this.lkp_JODept.Name = "lkp_JODept";
            this.lkp_JODept.Properties.AutoHeight = ((bool)(resources.GetObject("lkp_JODept.Properties.AutoHeight")));
            this.lkp_JODept.Properties.NullValuePrompt = resources.GetString("lkp_JODept.Properties.NullValuePrompt");
            // 
            // textEdit4
            // 
            resources.ApplyResources(this.textEdit4, "textEdit4");
            this.textEdit4.Name = "textEdit4";
            this.textEdit4.Properties.AutoHeight = ((bool)(resources.GetObject("textEdit4.Properties.AutoHeight")));
            this.textEdit4.Properties.Mask.EditMask = resources.GetString("textEdit4.Properties.Mask.EditMask");
            this.textEdit4.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("textEdit4.Properties.Mask.IgnoreMaskBlank")));
            this.textEdit4.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("textEdit4.Properties.Mask.SaveLiteral")));
            this.textEdit4.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("textEdit4.Properties.Mask.ShowPlaceHolders")));
            this.textEdit4.Properties.NullValuePrompt = resources.GetString("textEdit4.Properties.NullValuePrompt");
            // 
            // txt_JOJob
            // 
            resources.ApplyResources(this.txt_JOJob, "txt_JOJob");
            this.txt_JOJob.Name = "txt_JOJob";
            this.txt_JOJob.Properties.AutoHeight = ((bool)(resources.GetObject("txt_JOJob.Properties.AutoHeight")));
            this.txt_JOJob.Properties.Mask.EditMask = resources.GetString("txt_JOJob.Properties.Mask.EditMask");
            this.txt_JOJob.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_JOJob.Properties.Mask.IgnoreMaskBlank")));
            this.txt_JOJob.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_JOJob.Properties.Mask.SaveLiteral")));
            this.txt_JOJob.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_JOJob.Properties.Mask.ShowPlaceHolders")));
            this.txt_JOJob.Properties.NullValuePrompt = resources.GetString("txt_JOJob.Properties.NullValuePrompt");
            // 
            // textEdit2
            // 
            resources.ApplyResources(this.textEdit2, "textEdit2");
            this.textEdit2.Name = "textEdit2";
            this.textEdit2.Properties.AutoHeight = ((bool)(resources.GetObject("textEdit2.Properties.AutoHeight")));
            this.textEdit2.Properties.Mask.EditMask = resources.GetString("textEdit2.Properties.Mask.EditMask");
            this.textEdit2.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("textEdit2.Properties.Mask.IgnoreMaskBlank")));
            this.textEdit2.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("textEdit2.Properties.Mask.SaveLiteral")));
            this.textEdit2.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("textEdit2.Properties.Mask.ShowPlaceHolders")));
            this.textEdit2.Properties.NullValuePrompt = resources.GetString("textEdit2.Properties.NullValuePrompt");
            // 
            // txt_JODeliveryDate
            // 
            resources.ApplyResources(this.txt_JODeliveryDate, "txt_JODeliveryDate");
            this.txt_JODeliveryDate.Name = "txt_JODeliveryDate";
            this.txt_JODeliveryDate.Properties.AutoHeight = ((bool)(resources.GetObject("txt_JODeliveryDate.Properties.AutoHeight")));
            this.txt_JODeliveryDate.Properties.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("txt_JODeliveryDate.Properties.CalendarTimeProperties.AutoHeight")));
            this.txt_JODeliveryDate.Properties.CalendarTimeProperties.Mask.EditMask = resources.GetString("txt_JODeliveryDate.Properties.CalendarTimeProperties.Mask.EditMask");
            this.txt_JODeliveryDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_JODeliveryDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.txt_JODeliveryDate.Properties.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_JODeliveryDate.Properties.CalendarTimeProperties.Mask.MaskType")));
            this.txt_JODeliveryDate.Properties.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_JODeliveryDate.Properties.CalendarTimeProperties.Mask.SaveLiteral")));
            this.txt_JODeliveryDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_JODeliveryDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.txt_JODeliveryDate.Properties.CalendarTimeProperties.NullValuePrompt = resources.GetString("txt_JODeliveryDate.Properties.CalendarTimeProperties.NullValuePrompt");
            this.txt_JODeliveryDate.Properties.Mask.EditMask = resources.GetString("txt_JODeliveryDate.Properties.Mask.EditMask");
            this.txt_JODeliveryDate.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_JODeliveryDate.Properties.Mask.IgnoreMaskBlank")));
            this.txt_JODeliveryDate.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_JODeliveryDate.Properties.Mask.MaskType")));
            this.txt_JODeliveryDate.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_JODeliveryDate.Properties.Mask.SaveLiteral")));
            this.txt_JODeliveryDate.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_JODeliveryDate.Properties.Mask.ShowPlaceHolders")));
            this.txt_JODeliveryDate.Properties.NullValuePrompt = resources.GetString("txt_JODeliveryDate.Properties.NullValuePrompt");
            // 
            // textEdit7
            // 
            resources.ApplyResources(this.textEdit7, "textEdit7");
            this.textEdit7.Name = "textEdit7";
            this.textEdit7.Properties.AutoHeight = ((bool)(resources.GetObject("textEdit7.Properties.AutoHeight")));
            this.textEdit7.Properties.Mask.EditMask = resources.GetString("textEdit7.Properties.Mask.EditMask");
            this.textEdit7.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("textEdit7.Properties.Mask.IgnoreMaskBlank")));
            this.textEdit7.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("textEdit7.Properties.Mask.SaveLiteral")));
            this.textEdit7.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("textEdit7.Properties.Mask.ShowPlaceHolders")));
            this.textEdit7.Properties.NullValuePrompt = resources.GetString("textEdit7.Properties.NullValuePrompt");
            // 
            // textEdit6
            // 
            resources.ApplyResources(this.textEdit6, "textEdit6");
            this.textEdit6.Name = "textEdit6";
            this.textEdit6.Properties.AutoHeight = ((bool)(resources.GetObject("textEdit6.Properties.AutoHeight")));
            this.textEdit6.Properties.Mask.EditMask = resources.GetString("textEdit6.Properties.Mask.EditMask");
            this.textEdit6.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("textEdit6.Properties.Mask.IgnoreMaskBlank")));
            this.textEdit6.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("textEdit6.Properties.Mask.SaveLiteral")));
            this.textEdit6.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("textEdit6.Properties.Mask.ShowPlaceHolders")));
            this.textEdit6.Properties.NullValuePrompt = resources.GetString("textEdit6.Properties.NullValuePrompt");
            // 
            // txt_JORegDate
            // 
            resources.ApplyResources(this.txt_JORegDate, "txt_JORegDate");
            this.txt_JORegDate.Name = "txt_JORegDate";
            this.txt_JORegDate.Properties.AutoHeight = ((bool)(resources.GetObject("txt_JORegDate.Properties.AutoHeight")));
            this.txt_JORegDate.Properties.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("txt_JORegDate.Properties.CalendarTimeProperties.AutoHeight")));
            this.txt_JORegDate.Properties.CalendarTimeProperties.Mask.EditMask = resources.GetString("txt_JORegDate.Properties.CalendarTimeProperties.Mask.EditMask");
            this.txt_JORegDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_JORegDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.txt_JORegDate.Properties.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_JORegDate.Properties.CalendarTimeProperties.Mask.MaskType")));
            this.txt_JORegDate.Properties.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_JORegDate.Properties.CalendarTimeProperties.Mask.SaveLiteral")));
            this.txt_JORegDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_JORegDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.txt_JORegDate.Properties.CalendarTimeProperties.NullValuePrompt = resources.GetString("txt_JORegDate.Properties.CalendarTimeProperties.NullValuePrompt");
            this.txt_JORegDate.Properties.Mask.EditMask = resources.GetString("txt_JORegDate.Properties.Mask.EditMask");
            this.txt_JORegDate.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_JORegDate.Properties.Mask.IgnoreMaskBlank")));
            this.txt_JORegDate.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_JORegDate.Properties.Mask.MaskType")));
            this.txt_JORegDate.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_JORegDate.Properties.Mask.SaveLiteral")));
            this.txt_JORegDate.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_JORegDate.Properties.Mask.ShowPlaceHolders")));
            this.txt_JORegDate.Properties.NullValuePrompt = resources.GetString("txt_JORegDate.Properties.NullValuePrompt");
            // 
            // txt_JOCode
            // 
            resources.ApplyResources(this.txt_JOCode, "txt_JOCode");
            this.txt_JOCode.Name = "txt_JOCode";
            this.txt_JOCode.Properties.AutoHeight = ((bool)(resources.GetObject("txt_JOCode.Properties.AutoHeight")));
            this.txt_JOCode.Properties.Mask.EditMask = resources.GetString("txt_JOCode.Properties.Mask.EditMask");
            this.txt_JOCode.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_JOCode.Properties.Mask.IgnoreMaskBlank")));
            this.txt_JOCode.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_JOCode.Properties.Mask.SaveLiteral")));
            this.txt_JOCode.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_JOCode.Properties.Mask.ShowPlaceHolders")));
            this.txt_JOCode.Properties.NullValuePrompt = resources.GetString("txt_JOCode.Properties.NullValuePrompt");
            // 
            // txt_AttnMr
            // 
            resources.ApplyResources(this.txt_AttnMr, "txt_AttnMr");
            this.txt_AttnMr.EnterMoveNextControl = true;
            this.txt_AttnMr.MenuManager = this.barManager1;
            this.txt_AttnMr.Name = "txt_AttnMr";
            this.txt_AttnMr.Properties.Appearance.BackColor = ((System.Drawing.Color)(resources.GetObject("txt_AttnMr.Properties.Appearance.BackColor")));
            this.txt_AttnMr.Properties.Appearance.BackColor2 = ((System.Drawing.Color)(resources.GetObject("txt_AttnMr.Properties.Appearance.BackColor2")));
            this.txt_AttnMr.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("txt_AttnMr.Properties.Appearance.Font")));
            this.txt_AttnMr.Properties.Appearance.Options.UseBackColor = true;
            this.txt_AttnMr.Properties.Appearance.Options.UseFont = true;
            this.txt_AttnMr.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_AttnMr.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
            this.txt_AttnMr.Properties.AppearanceDisabled.BackColor = ((System.Drawing.Color)(resources.GetObject("txt_AttnMr.Properties.AppearanceDisabled.BackColor")));
            this.txt_AttnMr.Properties.AppearanceDisabled.Options.UseBackColor = true;
            this.txt_AttnMr.Properties.AppearanceFocused.BackColor = ((System.Drawing.Color)(resources.GetObject("txt_AttnMr.Properties.AppearanceFocused.BackColor")));
            this.txt_AttnMr.Properties.AppearanceFocused.Options.UseBackColor = true;
            this.txt_AttnMr.Properties.AppearanceReadOnly.Options.UseTextOptions = true;
            this.txt_AttnMr.Properties.AppearanceReadOnly.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_AttnMr.Properties.AutoHeight = ((bool)(resources.GetObject("txt_AttnMr.Properties.AutoHeight")));
            this.txt_AttnMr.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.UltraFlat;
            this.txt_AttnMr.Properties.Mask.EditMask = resources.GetString("txt_AttnMr.Properties.Mask.EditMask");
            this.txt_AttnMr.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_AttnMr.Properties.Mask.IgnoreMaskBlank")));
            this.txt_AttnMr.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_AttnMr.Properties.Mask.SaveLiteral")));
            this.txt_AttnMr.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_AttnMr.Properties.Mask.ShowPlaceHolders")));
            this.txt_AttnMr.Properties.MaxLength = 190;
            this.txt_AttnMr.Properties.NullValuePrompt = resources.GetString("txt_AttnMr.Properties.NullValuePrompt");
            // 
            // labelControl41
            // 
            resources.ApplyResources(this.labelControl41, "labelControl41");
            this.labelControl41.Name = "labelControl41";
            // 
            // labelControl3
            // 
            resources.ApplyResources(this.labelControl3, "labelControl3");
            this.labelControl3.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl3.Appearance.Font")));
            this.labelControl3.Name = "labelControl3";
            // 
            // labelControl5
            // 
            resources.ApplyResources(this.labelControl5, "labelControl5");
            this.labelControl5.Name = "labelControl5";
            // 
            // labelControl8
            // 
            resources.ApplyResources(this.labelControl8, "labelControl8");
            this.labelControl8.Name = "labelControl8";
            // 
            // labelControl12
            // 
            resources.ApplyResources(this.labelControl12, "labelControl12");
            this.labelControl12.Name = "labelControl12";
            // 
            // labelControl2
            // 
            resources.ApplyResources(this.labelControl2, "labelControl2");
            this.labelControl2.Name = "labelControl2";
            // 
            // labelControl14
            // 
            resources.ApplyResources(this.labelControl14, "labelControl14");
            this.labelControl14.Name = "labelControl14";
            // 
            // labelControl15
            // 
            resources.ApplyResources(this.labelControl15, "labelControl15");
            this.labelControl15.Name = "labelControl15";
            // 
            // labelControl16
            // 
            resources.ApplyResources(this.labelControl16, "labelControl16");
            this.labelControl16.Name = "labelControl16";
            // 
            // labelControl21
            // 
            resources.ApplyResources(this.labelControl21, "labelControl21");
            this.labelControl21.Name = "labelControl21";
            // 
            // labelControl22
            // 
            resources.ApplyResources(this.labelControl22, "labelControl22");
            this.labelControl22.Name = "labelControl22";
            // 
            // labelControl23
            // 
            resources.ApplyResources(this.labelControl23, "labelControl23");
            this.labelControl23.Name = "labelControl23";
            // 
            // labelControl25
            // 
            resources.ApplyResources(this.labelControl25, "labelControl25");
            this.labelControl25.Name = "labelControl25";
            // 
            // txtDiscountRatio
            // 
            resources.ApplyResources(this.txtDiscountRatio, "txtDiscountRatio");
            this.txtDiscountRatio.EnterMoveNextControl = true;
            this.txtDiscountRatio.Name = "txtDiscountRatio";
            this.txtDiscountRatio.Properties.Appearance.Options.UseTextOptions = true;
            this.txtDiscountRatio.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtDiscountRatio.Properties.AutoHeight = ((bool)(resources.GetObject("txtDiscountRatio.Properties.AutoHeight")));
            this.txtDiscountRatio.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtDiscountRatio.Properties.Mask.EditMask = resources.GetString("txtDiscountRatio.Properties.Mask.EditMask");
            this.txtDiscountRatio.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtDiscountRatio.Properties.Mask.IgnoreMaskBlank")));
            this.txtDiscountRatio.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtDiscountRatio.Properties.Mask.MaskType")));
            this.txtDiscountRatio.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtDiscountRatio.Properties.Mask.SaveLiteral")));
            this.txtDiscountRatio.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtDiscountRatio.Properties.Mask.ShowPlaceHolders")));
            this.txtDiscountRatio.Properties.NullValuePrompt = resources.GetString("txtDiscountRatio.Properties.NullValuePrompt");
            this.txtDiscountRatio.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txtDiscountRatio.EditValueChanged += new System.EventHandler(this.txtDiscountRatio_EditValueChanged);
            this.txtDiscountRatio.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txtDiscountRatio.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress_1);
            // 
            // txtDiscountValue
            // 
            resources.ApplyResources(this.txtDiscountValue, "txtDiscountValue");
            this.txtDiscountValue.EnterMoveNextControl = true;
            this.txtDiscountValue.Name = "txtDiscountValue";
            this.txtDiscountValue.Properties.Appearance.Options.UseTextOptions = true;
            this.txtDiscountValue.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtDiscountValue.Properties.AutoHeight = ((bool)(resources.GetObject("txtDiscountValue.Properties.AutoHeight")));
            this.txtDiscountValue.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtDiscountValue.Properties.Mask.EditMask = resources.GetString("txtDiscountValue.Properties.Mask.EditMask");
            this.txtDiscountValue.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtDiscountValue.Properties.Mask.IgnoreMaskBlank")));
            this.txtDiscountValue.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtDiscountValue.Properties.Mask.MaskType")));
            this.txtDiscountValue.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtDiscountValue.Properties.Mask.SaveLiteral")));
            this.txtDiscountValue.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtDiscountValue.Properties.Mask.ShowPlaceHolders")));
            this.txtDiscountValue.Properties.NullValuePrompt = resources.GetString("txtDiscountValue.Properties.NullValuePrompt");
            this.txtDiscountValue.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txtDiscountValue.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txtDiscountValue.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress_1);
            this.txtDiscountValue.Leave += new System.EventHandler(this.txtDiscountValue_Leave);
            // 
            // txt_TaxValue
            // 
            resources.ApplyResources(this.txt_TaxValue, "txt_TaxValue");
            this.txt_TaxValue.EnterMoveNextControl = true;
            this.txt_TaxValue.Name = "txt_TaxValue";
            this.txt_TaxValue.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_TaxValue.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_TaxValue.Properties.AutoHeight = ((bool)(resources.GetObject("txt_TaxValue.Properties.AutoHeight")));
            this.txt_TaxValue.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_TaxValue.Properties.Mask.EditMask = resources.GetString("txt_TaxValue.Properties.Mask.EditMask");
            this.txt_TaxValue.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_TaxValue.Properties.Mask.IgnoreMaskBlank")));
            this.txt_TaxValue.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_TaxValue.Properties.Mask.MaskType")));
            this.txt_TaxValue.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_TaxValue.Properties.Mask.SaveLiteral")));
            this.txt_TaxValue.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_TaxValue.Properties.Mask.ShowPlaceHolders")));
            this.txt_TaxValue.Properties.NullValuePrompt = resources.GetString("txt_TaxValue.Properties.NullValuePrompt");
            this.txt_TaxValue.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txt_TaxValue.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            // 
            // txt_DeductTaxR
            // 
            resources.ApplyResources(this.txt_DeductTaxR, "txt_DeductTaxR");
            this.txt_DeductTaxR.EnterMoveNextControl = true;
            this.txt_DeductTaxR.Name = "txt_DeductTaxR";
            this.txt_DeductTaxR.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_DeductTaxR.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_DeductTaxR.Properties.AutoHeight = ((bool)(resources.GetObject("txt_DeductTaxR.Properties.AutoHeight")));
            this.txt_DeductTaxR.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_DeductTaxR.Properties.Mask.EditMask = resources.GetString("txt_DeductTaxR.Properties.Mask.EditMask");
            this.txt_DeductTaxR.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_DeductTaxR.Properties.Mask.IgnoreMaskBlank")));
            this.txt_DeductTaxR.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_DeductTaxR.Properties.Mask.MaskType")));
            this.txt_DeductTaxR.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_DeductTaxR.Properties.Mask.SaveLiteral")));
            this.txt_DeductTaxR.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_DeductTaxR.Properties.Mask.ShowPlaceHolders")));
            this.txt_DeductTaxR.Properties.NullValuePrompt = resources.GetString("txt_DeductTaxR.Properties.NullValuePrompt");
            this.txt_DeductTaxR.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txt_DeductTaxR.EditValueChanged += new System.EventHandler(this.txtDiscountRatio_EditValueChanged);
            this.txt_DeductTaxR.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txt_DeductTaxR.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress_1);
            // 
            // txt_DeductTaxV
            // 
            resources.ApplyResources(this.txt_DeductTaxV, "txt_DeductTaxV");
            this.txt_DeductTaxV.EnterMoveNextControl = true;
            this.txt_DeductTaxV.Name = "txt_DeductTaxV";
            this.txt_DeductTaxV.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_DeductTaxV.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_DeductTaxV.Properties.AutoHeight = ((bool)(resources.GetObject("txt_DeductTaxV.Properties.AutoHeight")));
            this.txt_DeductTaxV.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_DeductTaxV.Properties.Mask.EditMask = resources.GetString("txt_DeductTaxV.Properties.Mask.EditMask");
            this.txt_DeductTaxV.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_DeductTaxV.Properties.Mask.IgnoreMaskBlank")));
            this.txt_DeductTaxV.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_DeductTaxV.Properties.Mask.MaskType")));
            this.txt_DeductTaxV.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_DeductTaxV.Properties.Mask.SaveLiteral")));
            this.txt_DeductTaxV.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_DeductTaxV.Properties.Mask.ShowPlaceHolders")));
            this.txt_DeductTaxV.Properties.NullValuePrompt = resources.GetString("txt_DeductTaxV.Properties.NullValuePrompt");
            this.txt_DeductTaxV.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txt_DeductTaxV.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txt_DeductTaxV.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress_1);
            this.txt_DeductTaxV.Leave += new System.EventHandler(this.txtDiscountValue_Leave);
            // 
            // txt_AddTaxR
            // 
            resources.ApplyResources(this.txt_AddTaxR, "txt_AddTaxR");
            this.txt_AddTaxR.EnterMoveNextControl = true;
            this.txt_AddTaxR.Name = "txt_AddTaxR";
            this.txt_AddTaxR.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_AddTaxR.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_AddTaxR.Properties.AutoHeight = ((bool)(resources.GetObject("txt_AddTaxR.Properties.AutoHeight")));
            this.txt_AddTaxR.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_AddTaxR.Properties.Mask.EditMask = resources.GetString("txt_AddTaxR.Properties.Mask.EditMask");
            this.txt_AddTaxR.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_AddTaxR.Properties.Mask.IgnoreMaskBlank")));
            this.txt_AddTaxR.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_AddTaxR.Properties.Mask.MaskType")));
            this.txt_AddTaxR.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_AddTaxR.Properties.Mask.SaveLiteral")));
            this.txt_AddTaxR.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_AddTaxR.Properties.Mask.ShowPlaceHolders")));
            this.txt_AddTaxR.Properties.NullValuePrompt = resources.GetString("txt_AddTaxR.Properties.NullValuePrompt");
            this.txt_AddTaxR.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txt_AddTaxR.EditValueChanged += new System.EventHandler(this.txtDiscountRatio_EditValueChanged);
            this.txt_AddTaxR.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txt_AddTaxR.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress_1);
            // 
            // txt_AddTaxV
            // 
            resources.ApplyResources(this.txt_AddTaxV, "txt_AddTaxV");
            this.txt_AddTaxV.EnterMoveNextControl = true;
            this.txt_AddTaxV.Name = "txt_AddTaxV";
            this.txt_AddTaxV.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_AddTaxV.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_AddTaxV.Properties.AutoHeight = ((bool)(resources.GetObject("txt_AddTaxV.Properties.AutoHeight")));
            this.txt_AddTaxV.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_AddTaxV.Properties.Mask.EditMask = resources.GetString("txt_AddTaxV.Properties.Mask.EditMask");
            this.txt_AddTaxV.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_AddTaxV.Properties.Mask.IgnoreMaskBlank")));
            this.txt_AddTaxV.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_AddTaxV.Properties.Mask.MaskType")));
            this.txt_AddTaxV.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_AddTaxV.Properties.Mask.SaveLiteral")));
            this.txt_AddTaxV.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_AddTaxV.Properties.Mask.ShowPlaceHolders")));
            this.txt_AddTaxV.Properties.NullValuePrompt = resources.GetString("txt_AddTaxV.Properties.NullValuePrompt");
            this.txt_AddTaxV.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txt_AddTaxV.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txt_AddTaxV.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress_1);
            this.txt_AddTaxV.Leave += new System.EventHandler(this.txtDiscountValue_Leave);
            // 
            // txtExpensesR
            // 
            resources.ApplyResources(this.txtExpensesR, "txtExpensesR");
            this.txtExpensesR.EnterMoveNextControl = true;
            this.txtExpensesR.Name = "txtExpensesR";
            this.txtExpensesR.Properties.Appearance.Options.UseTextOptions = true;
            this.txtExpensesR.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txtExpensesR.Properties.AutoHeight = ((bool)(resources.GetObject("txtExpensesR.Properties.AutoHeight")));
            this.txtExpensesR.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txtExpensesR.Properties.Mask.EditMask = resources.GetString("txtExpensesR.Properties.Mask.EditMask");
            this.txtExpensesR.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txtExpensesR.Properties.Mask.IgnoreMaskBlank")));
            this.txtExpensesR.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txtExpensesR.Properties.Mask.MaskType")));
            this.txtExpensesR.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txtExpensesR.Properties.Mask.SaveLiteral")));
            this.txtExpensesR.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txtExpensesR.Properties.Mask.ShowPlaceHolders")));
            this.txtExpensesR.Properties.NullValuePrompt = resources.GetString("txtExpensesR.Properties.NullValuePrompt");
            this.txtExpensesR.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txtDiscountRatio_Spin);
            this.txtExpensesR.EditValueChanged += new System.EventHandler(this.txtDiscountRatio_EditValueChanged);
            this.txtExpensesR.Modified += new System.EventHandler(this.lkp_Customers_Modified);
            this.txtExpensesR.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txtDiscountRatio_KeyPress_1);
            // 
            // chk_Offer
            // 
            resources.ApplyResources(this.chk_Offer, "chk_Offer");
            this.chk_Offer.Name = "chk_Offer";
            this.chk_Offer.Properties.Appearance.Options.UseTextOptions = true;
            this.chk_Offer.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chk_Offer.Properties.AppearanceReadOnly.ForeColor = ((System.Drawing.Color)(resources.GetObject("chk_Offer.Properties.AppearanceReadOnly.ForeColor")));
            this.chk_Offer.Properties.AppearanceReadOnly.Options.UseForeColor = true;
            this.chk_Offer.Properties.AutoHeight = ((bool)(resources.GetObject("chk_Offer.Properties.AutoHeight")));
            this.chk_Offer.Properties.AutoWidth = true;
            this.chk_Offer.Properties.Caption = resources.GetString("chk_Offer.Properties.Caption");
            this.chk_Offer.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("chk_Offer.Properties.GlyphAlignment")));
            this.chk_Offer.TabStop = false;
            // 
            // labelControl27
            // 
            resources.ApplyResources(this.labelControl27, "labelControl27");
            this.labelControl27.Name = "labelControl27";
            // 
            // labelControl28
            // 
            resources.ApplyResources(this.labelControl28, "labelControl28");
            this.labelControl28.Name = "labelControl28";
            // 
            // labelControl32
            // 
            resources.ApplyResources(this.labelControl32, "labelControl32");
            this.labelControl32.Name = "labelControl32";
            // 
            // labelControl34
            // 
            resources.ApplyResources(this.labelControl34, "labelControl34");
            this.labelControl34.Name = "labelControl34";
            // 
            // txt_CusTaxR
            // 
            resources.ApplyResources(this.txt_CusTaxR, "txt_CusTaxR");
            this.txt_CusTaxR.EnterMoveNextControl = true;
            this.txt_CusTaxR.Name = "txt_CusTaxR";
            this.txt_CusTaxR.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_CusTaxR.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_CusTaxR.Properties.AutoHeight = ((bool)(resources.GetObject("txt_CusTaxR.Properties.AutoHeight")));
            this.txt_CusTaxR.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_CusTaxR.Properties.Mask.EditMask = resources.GetString("txt_CusTaxR.Properties.Mask.EditMask");
            this.txt_CusTaxR.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_CusTaxR.Properties.Mask.IgnoreMaskBlank")));
            this.txt_CusTaxR.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_CusTaxR.Properties.Mask.MaskType")));
            this.txt_CusTaxR.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_CusTaxR.Properties.Mask.SaveLiteral")));
            this.txt_CusTaxR.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_CusTaxR.Properties.Mask.ShowPlaceHolders")));
            this.txt_CusTaxR.Properties.NullValuePrompt = resources.GetString("txt_CusTaxR.Properties.NullValuePrompt");
            this.txt_CusTaxR.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txt_CusTaxR_Spin);
            this.txt_CusTaxR.EditValueChanged += new System.EventHandler(this.txt_CusTaxR_EditValueChanged);
            this.txt_CusTaxR.Modified += new System.EventHandler(this.txt_CusTaxR_Modified);
            // 
            // txt_CusTaxV
            // 
            resources.ApplyResources(this.txt_CusTaxV, "txt_CusTaxV");
            this.txt_CusTaxV.EnterMoveNextControl = true;
            this.txt_CusTaxV.Name = "txt_CusTaxV";
            this.txt_CusTaxV.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_CusTaxV.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_CusTaxV.Properties.AutoHeight = ((bool)(resources.GetObject("txt_CusTaxV.Properties.AutoHeight")));
            this.txt_CusTaxV.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_CusTaxV.Properties.Mask.EditMask = resources.GetString("txt_CusTaxV.Properties.Mask.EditMask");
            this.txt_CusTaxV.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_CusTaxV.Properties.Mask.IgnoreMaskBlank")));
            this.txt_CusTaxV.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_CusTaxV.Properties.Mask.MaskType")));
            this.txt_CusTaxV.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_CusTaxV.Properties.Mask.SaveLiteral")));
            this.txt_CusTaxV.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_CusTaxV.Properties.Mask.ShowPlaceHolders")));
            this.txt_CusTaxV.Properties.NullValuePrompt = resources.GetString("txt_CusTaxV.Properties.NullValuePrompt");
            this.txt_CusTaxV.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txt_CusTaxV_Spin);
            this.txt_CusTaxV.Modified += new System.EventHandler(this.txt_CusTaxV_Modified);
            this.txt_CusTaxV.Leave += new System.EventHandler(this.txt_CusTaxV_Leave);
            // 
            // labelControl37
            // 
            resources.ApplyResources(this.labelControl37, "labelControl37");
            this.labelControl37.Name = "labelControl37";
            // 
            // labelControl38
            // 
            resources.ApplyResources(this.labelControl38, "labelControl38");
            this.labelControl38.Name = "labelControl38";
            // 
            // labelControl39
            // 
            resources.ApplyResources(this.labelControl39, "labelControl39");
            this.labelControl39.Name = "labelControl39";
            // 
            // labelControl42
            // 
            resources.ApplyResources(this.labelControl42, "labelControl42");
            this.labelControl42.Name = "labelControl42";
            // 
            // labelControl43
            // 
            resources.ApplyResources(this.labelControl43, "labelControl43");
            this.labelControl43.Name = "labelControl43";
            // 
            // labelControl44
            // 
            resources.ApplyResources(this.labelControl44, "labelControl44");
            this.labelControl44.Name = "labelControl44";
            // 
            // labelControl45
            // 
            resources.ApplyResources(this.labelControl45, "labelControl45");
            this.labelControl45.Name = "labelControl45";
            // 
            // labelControl46
            // 
            resources.ApplyResources(this.labelControl46, "labelControl46");
            this.labelControl46.Name = "labelControl46";
            // 
            // txt_retentionR
            // 
            resources.ApplyResources(this.txt_retentionR, "txt_retentionR");
            this.txt_retentionR.EnterMoveNextControl = true;
            this.txt_retentionR.Name = "txt_retentionR";
            this.txt_retentionR.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_retentionR.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_retentionR.Properties.AutoHeight = ((bool)(resources.GetObject("txt_retentionR.Properties.AutoHeight")));
            this.txt_retentionR.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_retentionR.Properties.Mask.EditMask = resources.GetString("txt_retentionR.Properties.Mask.EditMask");
            this.txt_retentionR.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_retentionR.Properties.Mask.IgnoreMaskBlank")));
            this.txt_retentionR.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_retentionR.Properties.Mask.MaskType")));
            this.txt_retentionR.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_retentionR.Properties.Mask.SaveLiteral")));
            this.txt_retentionR.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_retentionR.Properties.Mask.ShowPlaceHolders")));
            this.txt_retentionR.Properties.NullValuePrompt = resources.GetString("txt_retentionR.Properties.NullValuePrompt");
            this.txt_retentionR.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txt_retentionR_Spin);
            this.txt_retentionR.EditValueChanged += new System.EventHandler(this.txt_retentionR_EditValueChanged);
            // 
            // txt_RetentionV
            // 
            resources.ApplyResources(this.txt_RetentionV, "txt_RetentionV");
            this.txt_RetentionV.EnterMoveNextControl = true;
            this.txt_RetentionV.Name = "txt_RetentionV";
            this.txt_RetentionV.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_RetentionV.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_RetentionV.Properties.AutoHeight = ((bool)(resources.GetObject("txt_RetentionV.Properties.AutoHeight")));
            this.txt_RetentionV.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_RetentionV.Properties.Mask.EditMask = resources.GetString("txt_RetentionV.Properties.Mask.EditMask");
            this.txt_RetentionV.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_RetentionV.Properties.Mask.IgnoreMaskBlank")));
            this.txt_RetentionV.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_RetentionV.Properties.Mask.MaskType")));
            this.txt_RetentionV.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_RetentionV.Properties.Mask.SaveLiteral")));
            this.txt_RetentionV.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_RetentionV.Properties.Mask.ShowPlaceHolders")));
            this.txt_RetentionV.Properties.NullValuePrompt = resources.GetString("txt_RetentionV.Properties.NullValuePrompt");
            this.txt_RetentionV.EditValueChanged += new System.EventHandler(this.txt_RetentionV_EditValueChanged);
            this.txt_RetentionV.Modified += new System.EventHandler(this.txt_RetentionV_Modified);
            this.txt_RetentionV.Leave += new System.EventHandler(this.txt_RetentionV_Leave);
            // 
            // txt_AdvancePayR
            // 
            resources.ApplyResources(this.txt_AdvancePayR, "txt_AdvancePayR");
            this.txt_AdvancePayR.EnterMoveNextControl = true;
            this.txt_AdvancePayR.Name = "txt_AdvancePayR";
            this.txt_AdvancePayR.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_AdvancePayR.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_AdvancePayR.Properties.AutoHeight = ((bool)(resources.GetObject("txt_AdvancePayR.Properties.AutoHeight")));
            this.txt_AdvancePayR.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_AdvancePayR.Properties.Mask.EditMask = resources.GetString("txt_AdvancePayR.Properties.Mask.EditMask");
            this.txt_AdvancePayR.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_AdvancePayR.Properties.Mask.IgnoreMaskBlank")));
            this.txt_AdvancePayR.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_AdvancePayR.Properties.Mask.MaskType")));
            this.txt_AdvancePayR.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_AdvancePayR.Properties.Mask.SaveLiteral")));
            this.txt_AdvancePayR.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_AdvancePayR.Properties.Mask.ShowPlaceHolders")));
            this.txt_AdvancePayR.Properties.NullValuePrompt = resources.GetString("txt_AdvancePayR.Properties.NullValuePrompt");
            this.txt_AdvancePayR.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txt_AdvancePayR_Spin);
            this.txt_AdvancePayR.EditValueChanged += new System.EventHandler(this.txt_AdvancePayR_EditValueChanged);
            // 
            // txt_AdvancePayV
            // 
            resources.ApplyResources(this.txt_AdvancePayV, "txt_AdvancePayV");
            this.txt_AdvancePayV.EnterMoveNextControl = true;
            this.txt_AdvancePayV.Name = "txt_AdvancePayV";
            this.txt_AdvancePayV.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_AdvancePayV.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_AdvancePayV.Properties.AutoHeight = ((bool)(resources.GetObject("txt_AdvancePayV.Properties.AutoHeight")));
            this.txt_AdvancePayV.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_AdvancePayV.Properties.Mask.EditMask = resources.GetString("txt_AdvancePayV.Properties.Mask.EditMask");
            this.txt_AdvancePayV.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_AdvancePayV.Properties.Mask.IgnoreMaskBlank")));
            this.txt_AdvancePayV.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_AdvancePayV.Properties.Mask.MaskType")));
            this.txt_AdvancePayV.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_AdvancePayV.Properties.Mask.SaveLiteral")));
            this.txt_AdvancePayV.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_AdvancePayV.Properties.Mask.ShowPlaceHolders")));
            this.txt_AdvancePayV.Properties.NullValuePrompt = resources.GetString("txt_AdvancePayV.Properties.NullValuePrompt");
            this.txt_AdvancePayV.EditValueChanged += new System.EventHandler(this.txt_AdvancePayV_EditValueChanged);
            this.txt_AdvancePayV.Modified += new System.EventHandler(this.txt_AdvancePayV_Modified);
            this.txt_AdvancePayV.Leave += new System.EventHandler(this.txt_AdvancePayV_Leave);
            // 
            // grp_ExtraRevenues
            // 
            resources.ApplyResources(this.grp_ExtraRevenues, "grp_ExtraRevenues");
            this.grp_ExtraRevenues.AppearanceCaption.Options.UseTextOptions = true;
            this.grp_ExtraRevenues.AppearanceCaption.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.grp_ExtraRevenues.CaptionLocation = DevExpress.Utils.Locations.Right;
            this.grp_ExtraRevenues.Controls.Add(this.btnCeil);
            this.grp_ExtraRevenues.Controls.Add(this.txt_ShiftAdd);
            this.grp_ExtraRevenues.Controls.Add(this.labelControl53);
            this.grp_ExtraRevenues.Controls.Add(this.txt_transportation);
            this.grp_ExtraRevenues.Controls.Add(this.labelControl49);
            this.grp_ExtraRevenues.Controls.Add(this.txt_Handing);
            this.grp_ExtraRevenues.Controls.Add(this.labelControl50);
            this.grp_ExtraRevenues.Name = "grp_ExtraRevenues";
            // 
            // btnCeil
            // 
            resources.ApplyResources(this.btnCeil, "btnCeil");
            this.btnCeil.Name = "btnCeil";
            this.btnCeil.TabStop = false;
            this.btnCeil.Click += new System.EventHandler(this.btnCeil_Click);
            // 
            // txt_ShiftAdd
            // 
            resources.ApplyResources(this.txt_ShiftAdd, "txt_ShiftAdd");
            this.txt_ShiftAdd.EnterMoveNextControl = true;
            this.txt_ShiftAdd.Name = "txt_ShiftAdd";
            this.txt_ShiftAdd.Properties.AutoHeight = ((bool)(resources.GetObject("txt_ShiftAdd.Properties.AutoHeight")));
            this.txt_ShiftAdd.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txt_ShiftAdd.Properties.Mask.EditMask = resources.GetString("txt_ShiftAdd.Properties.Mask.EditMask");
            this.txt_ShiftAdd.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_ShiftAdd.Properties.Mask.IgnoreMaskBlank")));
            this.txt_ShiftAdd.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_ShiftAdd.Properties.Mask.MaskType")));
            this.txt_ShiftAdd.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_ShiftAdd.Properties.Mask.SaveLiteral")));
            this.txt_ShiftAdd.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_ShiftAdd.Properties.Mask.ShowPlaceHolders")));
            this.txt_ShiftAdd.Properties.NullValuePrompt = resources.GetString("txt_ShiftAdd.Properties.NullValuePrompt");
            this.txt_ShiftAdd.EditValueChanged += new System.EventHandler(this.txt_Handing_Leave);
            // 
            // labelControl53
            // 
            resources.ApplyResources(this.labelControl53, "labelControl53");
            this.labelControl53.Name = "labelControl53";
            // 
            // txt_transportation
            // 
            resources.ApplyResources(this.txt_transportation, "txt_transportation");
            this.txt_transportation.EnterMoveNextControl = true;
            this.txt_transportation.MenuManager = this.barManager1;
            this.txt_transportation.Name = "txt_transportation";
            this.txt_transportation.Properties.AutoHeight = ((bool)(resources.GetObject("txt_transportation.Properties.AutoHeight")));
            this.txt_transportation.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txt_transportation.Properties.Mask.EditMask = resources.GetString("txt_transportation.Properties.Mask.EditMask");
            this.txt_transportation.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_transportation.Properties.Mask.IgnoreMaskBlank")));
            this.txt_transportation.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_transportation.Properties.Mask.MaskType")));
            this.txt_transportation.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_transportation.Properties.Mask.SaveLiteral")));
            this.txt_transportation.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_transportation.Properties.Mask.ShowPlaceHolders")));
            this.txt_transportation.Properties.NullValuePrompt = resources.GetString("txt_transportation.Properties.NullValuePrompt");
            this.txt_transportation.Leave += new System.EventHandler(this.txt_transportation_Leave);
            // 
            // labelControl49
            // 
            resources.ApplyResources(this.labelControl49, "labelControl49");
            this.labelControl49.Name = "labelControl49";
            // 
            // txt_Handing
            // 
            resources.ApplyResources(this.txt_Handing, "txt_Handing");
            this.txt_Handing.EnterMoveNextControl = true;
            this.txt_Handing.Name = "txt_Handing";
            this.txt_Handing.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Handing.Properties.AutoHeight")));
            this.txt_Handing.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txt_Handing.Properties.Mask.EditMask = resources.GetString("txt_Handing.Properties.Mask.EditMask");
            this.txt_Handing.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Handing.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Handing.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Handing.Properties.Mask.MaskType")));
            this.txt_Handing.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Handing.Properties.Mask.SaveLiteral")));
            this.txt_Handing.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Handing.Properties.Mask.ShowPlaceHolders")));
            this.txt_Handing.Properties.NullValuePrompt = resources.GetString("txt_Handing.Properties.NullValuePrompt");
            this.txt_Handing.Leave += new System.EventHandler(this.txt_Handing_Leave);
            // 
            // labelControl50
            // 
            resources.ApplyResources(this.labelControl50, "labelControl50");
            this.labelControl50.Name = "labelControl50";
            // 
            // timer1
            // 
            this.timer1.Interval = 500;
            this.timer1.Tick += new System.EventHandler(this.timer1_Tick);
            // 
            // chk_Approved
            // 
            resources.ApplyResources(this.chk_Approved, "chk_Approved");
            this.chk_Approved.Name = "chk_Approved";
            this.chk_Approved.Properties.Appearance.Options.UseTextOptions = true;
            this.chk_Approved.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chk_Approved.Properties.AppearanceReadOnly.ForeColor = ((System.Drawing.Color)(resources.GetObject("chk_Approved.Properties.AppearanceReadOnly.ForeColor")));
            this.chk_Approved.Properties.AppearanceReadOnly.Options.UseForeColor = true;
            this.chk_Approved.Properties.AutoHeight = ((bool)(resources.GetObject("chk_Approved.Properties.AutoHeight")));
            this.chk_Approved.Properties.AutoWidth = true;
            this.chk_Approved.Properties.Caption = resources.GetString("chk_Approved.Properties.Caption");
            this.chk_Approved.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("chk_Approved.Properties.GlyphAlignment")));
            this.chk_Approved.TabStop = false;
            // 
            // bookId
            // 
            resources.ApplyResources(this.bookId, "bookId");
            this.bookId.EnterMoveNextControl = true;
            this.bookId.MenuManager = this.barManager1;
            this.bookId.Name = "bookId";
            this.bookId.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.bookId.Properties.Appearance.Options.UseTextOptions = true;
            this.bookId.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.bookId.Properties.AutoHeight = ((bool)(resources.GetObject("bookId.Properties.AutoHeight")));
            this.bookId.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("bookId.Properties.Buttons"))))});
            this.bookId.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("bookId.Properties.GlyphAlignment")));
            this.bookId.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.ImageComboBoxItem[] {
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("bookId.Properties.Items"), ((object)(resources.GetObject("bookId.Properties.Items1"))), ((int)(resources.GetObject("bookId.Properties.Items2")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("bookId.Properties.Items3"), ((object)(resources.GetObject("bookId.Properties.Items4"))), ((int)(resources.GetObject("bookId.Properties.Items5")))),
            new DevExpress.XtraEditors.Controls.ImageComboBoxItem(resources.GetString("bookId.Properties.Items6"), ((object)(resources.GetObject("bookId.Properties.Items7"))), ((int)(resources.GetObject("bookId.Properties.Items8"))))});
            this.bookId.Properties.NullValuePrompt = resources.GetString("bookId.Properties.NullValuePrompt");
            // 
            // labelControl55
            // 
            resources.ApplyResources(this.labelControl55, "labelControl55");
            this.labelControl55.Name = "labelControl55";
            // 
            // labelControl29
            // 
            resources.ApplyResources(this.labelControl29, "labelControl29");
            this.labelControl29.Name = "labelControl29";
            // 
            // txt_EtaxValue
            // 
            resources.ApplyResources(this.txt_EtaxValue, "txt_EtaxValue");
            this.txt_EtaxValue.EnterMoveNextControl = true;
            this.txt_EtaxValue.Name = "txt_EtaxValue";
            this.txt_EtaxValue.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_EtaxValue.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_EtaxValue.Properties.AutoHeight = ((bool)(resources.GetObject("txt_EtaxValue.Properties.AutoHeight")));
            this.txt_EtaxValue.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_EtaxValue.Properties.Mask.EditMask = resources.GetString("txt_EtaxValue.Properties.Mask.EditMask");
            this.txt_EtaxValue.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_EtaxValue.Properties.Mask.IgnoreMaskBlank")));
            this.txt_EtaxValue.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_EtaxValue.Properties.Mask.MaskType")));
            this.txt_EtaxValue.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_EtaxValue.Properties.Mask.SaveLiteral")));
            this.txt_EtaxValue.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_EtaxValue.Properties.Mask.ShowPlaceHolders")));
            this.txt_EtaxValue.Properties.NullValuePrompt = resources.GetString("txt_EtaxValue.Properties.NullValuePrompt");
            // 
            // txt_Subtotal
            // 
            resources.ApplyResources(this.txt_Subtotal, "txt_Subtotal");
            this.txt_Subtotal.EnterMoveNextControl = true;
            this.txt_Subtotal.Name = "txt_Subtotal";
            this.txt_Subtotal.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_Subtotal.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_Subtotal.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Subtotal.Properties.AutoHeight")));
            this.txt_Subtotal.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_Subtotal.Properties.Mask.EditMask = resources.GetString("txt_Subtotal.Properties.Mask.EditMask");
            this.txt_Subtotal.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Subtotal.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Subtotal.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Subtotal.Properties.Mask.MaskType")));
            this.txt_Subtotal.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Subtotal.Properties.Mask.SaveLiteral")));
            this.txt_Subtotal.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Subtotal.Properties.Mask.ShowPlaceHolders")));
            this.txt_Subtotal.Properties.NullValuePrompt = resources.GetString("txt_Subtotal.Properties.NullValuePrompt");
            // 
            // labelControl40
            // 
            resources.ApplyResources(this.labelControl40, "labelControl40");
            this.labelControl40.Name = "labelControl40";
            // 
            // txt_SubAfterTax
            // 
            resources.ApplyResources(this.txt_SubAfterTax, "txt_SubAfterTax");
            this.txt_SubAfterTax.EnterMoveNextControl = true;
            this.txt_SubAfterTax.Name = "txt_SubAfterTax";
            this.txt_SubAfterTax.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_SubAfterTax.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_SubAfterTax.Properties.AutoHeight = ((bool)(resources.GetObject("txt_SubAfterTax.Properties.AutoHeight")));
            this.txt_SubAfterTax.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_SubAfterTax.Properties.Mask.EditMask = resources.GetString("txt_SubAfterTax.Properties.Mask.EditMask");
            this.txt_SubAfterTax.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_SubAfterTax.Properties.Mask.IgnoreMaskBlank")));
            this.txt_SubAfterTax.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_SubAfterTax.Properties.Mask.MaskType")));
            this.txt_SubAfterTax.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_SubAfterTax.Properties.Mask.SaveLiteral")));
            this.txt_SubAfterTax.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_SubAfterTax.Properties.Mask.ShowPlaceHolders")));
            this.txt_SubAfterTax.Properties.NullValuePrompt = resources.GetString("txt_SubAfterTax.Properties.NullValuePrompt");
            this.txt_SubAfterTax.Properties.ReadOnly = true;
            this.txt_SubAfterTax.Spin += new DevExpress.XtraEditors.Controls.SpinEventHandler(this.txt_CusTaxV_Spin);
            this.txt_SubAfterTax.Modified += new System.EventHandler(this.txt_CusTaxV_Modified);
            this.txt_SubAfterTax.Leave += new System.EventHandler(this.txt_CusTaxV_Leave);
            // 
            // labelControl56
            // 
            resources.ApplyResources(this.labelControl56, "labelControl56");
            this.labelControl56.Name = "labelControl56";
            // 
            // txt_total_b4_Discounts
            // 
            resources.ApplyResources(this.txt_total_b4_Discounts, "txt_total_b4_Discounts");
            this.txt_total_b4_Discounts.EnterMoveNextControl = true;
            this.txt_total_b4_Discounts.Name = "txt_total_b4_Discounts";
            this.txt_total_b4_Discounts.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_total_b4_Discounts.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_total_b4_Discounts.Properties.AutoHeight = ((bool)(resources.GetObject("txt_total_b4_Discounts.Properties.AutoHeight")));
            this.txt_total_b4_Discounts.Properties.Mask.EditMask = resources.GetString("txt_total_b4_Discounts.Properties.Mask.EditMask");
            this.txt_total_b4_Discounts.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_total_b4_Discounts.Properties.Mask.IgnoreMaskBlank")));
            this.txt_total_b4_Discounts.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_total_b4_Discounts.Properties.Mask.SaveLiteral")));
            this.txt_total_b4_Discounts.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_total_b4_Discounts.Properties.Mask.ShowPlaceHolders")));
            this.txt_total_b4_Discounts.Properties.NullValuePrompt = resources.GetString("txt_total_b4_Discounts.Properties.NullValuePrompt");
            this.txt_total_b4_Discounts.TabStop = false;
            // 
            // lbl_total_b4_Discounts
            // 
            resources.ApplyResources(this.lbl_total_b4_Discounts, "lbl_total_b4_Discounts");
            this.lbl_total_b4_Discounts.Name = "lbl_total_b4_Discounts";
            // 
            // txt_CommercialDiscounts
            // 
            resources.ApplyResources(this.txt_CommercialDiscounts, "txt_CommercialDiscounts");
            this.txt_CommercialDiscounts.EnterMoveNextControl = true;
            this.txt_CommercialDiscounts.Name = "txt_CommercialDiscounts";
            this.txt_CommercialDiscounts.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_CommercialDiscounts.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_CommercialDiscounts.Properties.AutoHeight = ((bool)(resources.GetObject("txt_CommercialDiscounts.Properties.AutoHeight")));
            this.txt_CommercialDiscounts.Properties.Mask.EditMask = resources.GetString("txt_CommercialDiscounts.Properties.Mask.EditMask");
            this.txt_CommercialDiscounts.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_CommercialDiscounts.Properties.Mask.IgnoreMaskBlank")));
            this.txt_CommercialDiscounts.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_CommercialDiscounts.Properties.Mask.SaveLiteral")));
            this.txt_CommercialDiscounts.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_CommercialDiscounts.Properties.Mask.ShowPlaceHolders")));
            this.txt_CommercialDiscounts.Properties.NullValuePrompt = resources.GetString("txt_CommercialDiscounts.Properties.NullValuePrompt");
            this.txt_CommercialDiscounts.TabStop = false;
            // 
            // lbl_CommercialDiscounts
            // 
            resources.ApplyResources(this.lbl_CommercialDiscounts, "lbl_CommercialDiscounts");
            this.lbl_CommercialDiscounts.Name = "lbl_CommercialDiscounts";
            // 
            // lbl_totalAfterCommercial_Disc
            // 
            resources.ApplyResources(this.lbl_totalAfterCommercial_Disc, "lbl_totalAfterCommercial_Disc");
            this.lbl_totalAfterCommercial_Disc.Name = "lbl_totalAfterCommercial_Disc";
            // 
            // txt_totalAfterCommercial_Disc
            // 
            resources.ApplyResources(this.txt_totalAfterCommercial_Disc, "txt_totalAfterCommercial_Disc");
            this.txt_totalAfterCommercial_Disc.EnterMoveNextControl = true;
            this.txt_totalAfterCommercial_Disc.Name = "txt_totalAfterCommercial_Disc";
            this.txt_totalAfterCommercial_Disc.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_totalAfterCommercial_Disc.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_totalAfterCommercial_Disc.Properties.AutoHeight = ((bool)(resources.GetObject("txt_totalAfterCommercial_Disc.Properties.AutoHeight")));
            this.txt_totalAfterCommercial_Disc.Properties.Mask.EditMask = resources.GetString("txt_totalAfterCommercial_Disc.Properties.Mask.EditMask");
            this.txt_totalAfterCommercial_Disc.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_totalAfterCommercial_Disc.Properties.Mask.IgnoreMaskBlank")));
            this.txt_totalAfterCommercial_Disc.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_totalAfterCommercial_Disc.Properties.Mask.SaveLiteral")));
            this.txt_totalAfterCommercial_Disc.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_totalAfterCommercial_Disc.Properties.Mask.ShowPlaceHolders")));
            this.txt_totalAfterCommercial_Disc.Properties.NullValuePrompt = resources.GetString("txt_totalAfterCommercial_Disc.Properties.NullValuePrompt");
            this.txt_totalAfterCommercial_Disc.TabStop = false;
            // 
            // grd_SubTaxes
            // 
            resources.ApplyResources(this.grd_SubTaxes, "grd_SubTaxes");
            this.grd_SubTaxes.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grd_SubTaxes.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grd_SubTaxes.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grd_SubTaxes.EmbeddedNavigator.Anchor")));
            this.grd_SubTaxes.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grd_SubTaxes.EmbeddedNavigator.BackgroundImageLayout")));
            this.grd_SubTaxes.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grd_SubTaxes.EmbeddedNavigator.ImeMode")));
            this.grd_SubTaxes.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grd_SubTaxes.EmbeddedNavigator.TextLocation")));
            this.grd_SubTaxes.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grd_SubTaxes.EmbeddedNavigator.ToolTipIconType")));
            this.grd_SubTaxes.MainView = this.gv_SubTaxes;
            this.grd_SubTaxes.Name = "grd_SubTaxes";
            this.grd_SubTaxes.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.lkp_SubTaxes});
            this.grd_SubTaxes.TabStop = false;
            this.grd_SubTaxes.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gv_SubTaxes});
            // 
            // gv_SubTaxes
            // 
            this.gv_SubTaxes.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gv_SubTaxes.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gv_SubTaxes.Appearance.Row.Options.UseTextOptions = true;
            this.gv_SubTaxes.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gv_SubTaxes.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.Value,
            this.SubTaxId,
            this.col_Rate});
            this.gv_SubTaxes.GridControl = this.grd_SubTaxes;
            this.gv_SubTaxes.HorzScrollStep = 2;
            this.gv_SubTaxes.Name = "gv_SubTaxes";
            this.gv_SubTaxes.OptionsBehavior.Editable = false;
            this.gv_SubTaxes.OptionsBehavior.ReadOnly = true;
            this.gv_SubTaxes.OptionsCustomization.AllowFilter = false;
            this.gv_SubTaxes.OptionsCustomization.AllowGroup = false;
            this.gv_SubTaxes.OptionsCustomization.AllowQuickHideColumns = false;
            this.gv_SubTaxes.OptionsMenu.EnableColumnMenu = false;
            this.gv_SubTaxes.OptionsMenu.EnableFooterMenu = false;
            this.gv_SubTaxes.OptionsMenu.EnableGroupPanelMenu = false;
            this.gv_SubTaxes.OptionsMenu.ShowDateTimeGroupIntervalItems = false;
            this.gv_SubTaxes.OptionsMenu.ShowGroupSortSummaryItems = false;
            this.gv_SubTaxes.OptionsNavigation.EnterMoveNextColumn = true;
            this.gv_SubTaxes.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gv_SubTaxes.OptionsView.AnimationType = DevExpress.XtraGrid.Views.Base.GridAnimationType.NeverAnimate;
            this.gv_SubTaxes.OptionsView.EnableAppearanceEvenRow = true;
            this.gv_SubTaxes.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Bottom;
            this.gv_SubTaxes.OptionsView.ShowDetailButtons = false;
            this.gv_SubTaxes.OptionsView.ShowGroupPanel = false;
            // 
            // Value
            // 
            resources.ApplyResources(this.Value, "Value");
            this.Value.FieldName = "Value";
            this.Value.Name = "Value";
            // 
            // SubTaxId
            // 
            this.SubTaxId.AppearanceHeader.Options.UseTextOptions = true;
            this.SubTaxId.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.SubTaxId, "SubTaxId");
            this.SubTaxId.ColumnEdit = this.lkp_SubTaxes;
            this.SubTaxId.FieldName = "SubTaxId";
            this.SubTaxId.Name = "SubTaxId";
            // 
            // lkp_SubTaxes
            // 
            this.lkp_SubTaxes.Appearance.Options.UseTextOptions = true;
            this.lkp_SubTaxes.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkp_SubTaxes.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.lkp_SubTaxes.AppearanceDisabled.Options.UseTextOptions = true;
            this.lkp_SubTaxes.AppearanceDisabled.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkp_SubTaxes.AppearanceDisabled.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.lkp_SubTaxes.AppearanceDisabled.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.lkp_SubTaxes.AppearanceDropDownHeader.Options.UseTextOptions = true;
            this.lkp_SubTaxes.AppearanceDropDownHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkp_SubTaxes.AppearanceDropDownHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            resources.ApplyResources(this.lkp_SubTaxes, "lkp_SubTaxes");
            this.lkp_SubTaxes.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_SubTaxes.Buttons"))))});
            this.lkp_SubTaxes.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_SubTaxes.Columns"), resources.GetString("lkp_SubTaxes.Columns1")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_SubTaxes.Columns2"), resources.GetString("lkp_SubTaxes.Columns3")),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkp_SubTaxes.Columns4"), resources.GetString("lkp_SubTaxes.Columns5"), ((int)(resources.GetObject("lkp_SubTaxes.Columns6"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkp_SubTaxes.Columns7"))), resources.GetString("lkp_SubTaxes.Columns8"), ((bool)(resources.GetObject("lkp_SubTaxes.Columns9"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkp_SubTaxes.Columns10"))))});
            this.lkp_SubTaxes.Name = "lkp_SubTaxes";
            // 
            // col_Rate
            // 
            this.col_Rate.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Rate.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.col_Rate, "col_Rate");
            this.col_Rate.FieldName = "Rate";
            this.col_Rate.Name = "col_Rate";
            // 
            // labelControl57
            // 
            resources.ApplyResources(this.labelControl57, "labelControl57");
            this.labelControl57.Name = "labelControl57";
            // 
            // txt_bounsDiscount
            // 
            resources.ApplyResources(this.txt_bounsDiscount, "txt_bounsDiscount");
            this.txt_bounsDiscount.EnterMoveNextControl = true;
            this.txt_bounsDiscount.Name = "txt_bounsDiscount";
            this.txt_bounsDiscount.Properties.Appearance.Options.UseTextOptions = true;
            this.txt_bounsDiscount.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.txt_bounsDiscount.Properties.AutoHeight = ((bool)(resources.GetObject("txt_bounsDiscount.Properties.AutoHeight")));
            this.txt_bounsDiscount.Properties.EditValueChangedFiringMode = DevExpress.XtraEditors.Controls.EditValueChangedFiringMode.Default;
            this.txt_bounsDiscount.Properties.Mask.EditMask = resources.GetString("txt_bounsDiscount.Properties.Mask.EditMask");
            this.txt_bounsDiscount.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_bounsDiscount.Properties.Mask.IgnoreMaskBlank")));
            this.txt_bounsDiscount.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_bounsDiscount.Properties.Mask.MaskType")));
            this.txt_bounsDiscount.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_bounsDiscount.Properties.Mask.SaveLiteral")));
            this.txt_bounsDiscount.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_bounsDiscount.Properties.Mask.ShowPlaceHolders")));
            this.txt_bounsDiscount.Properties.NullValuePrompt = resources.GetString("txt_bounsDiscount.Properties.NullValuePrompt");
            // 
            // frm_SL_Invoice
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.labelControl57);
            this.Controls.Add(this.txt_bounsDiscount);
            this.Controls.Add(this.grd_SubTaxes);
            this.Controls.Add(this.labelControl56);
            this.Controls.Add(this.labelControl40);
            this.Controls.Add(this.labelControl55);
            this.Controls.Add(this.chk_Approved);
            this.Controls.Add(this.chk_Offer);
            this.Controls.Add(this.chk_IsOutTrns);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.txt_AttnMr);
            this.Controls.Add(this.labelControl41);
            this.Controls.Add(this.grp_ExtraRevenues);
            this.Controls.Add(this.cmbPayMethod);
            this.Controls.Add(this.btnAddCustomer);
            this.Controls.Add(this.lkp_Customers);
            this.Controls.Add(this.labelControl35);
            this.Controls.Add(this.txt_Subtotal);
            this.Controls.Add(this.labelControl29);
            this.Controls.Add(this.txt_EtaxValue);
            this.Controls.Add(this.bookId);
            this.Controls.Add(this.labelControl37);
            this.Controls.Add(this.labelControl38);
            this.Controls.Add(this.labelControl39);
            this.Controls.Add(this.labelControl42);
            this.Controls.Add(this.labelControl43);
            this.Controls.Add(this.labelControl44);
            this.Controls.Add(this.labelControl45);
            this.Controls.Add(this.labelControl46);
            this.Controls.Add(this.txt_retentionR);
            this.Controls.Add(this.txt_RetentionV);
            this.Controls.Add(this.txt_AdvancePayR);
            this.Controls.Add(this.txt_AdvancePayV);
            this.Controls.Add(this.labelControl27);
            this.Controls.Add(this.labelControl28);
            this.Controls.Add(this.labelControl32);
            this.Controls.Add(this.labelControl34);
            this.Controls.Add(this.txt_CusTaxR);
            this.Controls.Add(this.txt_SubAfterTax);
            this.Controls.Add(this.txt_CusTaxV);
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.labelControl8);
            this.Controls.Add(this.xtraTabControl1);
            this.Controls.Add(this.labelControl16);
            this.Controls.Add(this.labelControl15);
            this.Controls.Add(this.labelControl23);
            this.Controls.Add(this.labelControl25);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.labelControl20);
            this.Controls.Add(this.labelControl7);
            this.Controls.Add(this.panelControl2);
            this.Controls.Add(this.labelControl14);
            this.Controls.Add(this.labelControl22);
            this.Controls.Add(this.labelControl6);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.labelControl21);
            this.Controls.Add(this.labelControl11);
            this.Controls.Add(this.labelControl12);
            this.Controls.Add(this.labelControl19);
            this.Controls.Add(this.txt_CommercialDiscounts);
            this.Controls.Add(this.txt_totalAfterCommercial_Disc);
            this.Controls.Add(this.txt_total_b4_Discounts);
            this.Controls.Add(this.txt_Total);
            this.Controls.Add(this.lbl_totalAfterCommercial_Disc);
            this.Controls.Add(this.lbl_CommercialDiscounts);
            this.Controls.Add(this.lbl_total_b4_Discounts);
            this.Controls.Add(this.labelControl18);
            this.Controls.Add(this.txtNet);
            this.Controls.Add(this.panelControl1);
            this.Controls.Add(this.labelControl13);
            this.Controls.Add(this.labelControl36);
            this.Controls.Add(this.btnNext);
            this.Controls.Add(this.btnPrevious);
            this.Controls.Add(this.labelControl9);
            this.Controls.Add(this.txtExpenses);
            this.Controls.Add(this.txtDiscountRatio);
            this.Controls.Add(this.txtDiscountValue);
            this.Controls.Add(this.txt_TaxValue);
            this.Controls.Add(this.txt_DeductTaxR);
            this.Controls.Add(this.txt_DeductTaxV);
            this.Controls.Add(this.txt_AddTaxR);
            this.Controls.Add(this.txt_AddTaxV);
            this.Controls.Add(this.txtExpensesR);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.KeyPreview = true;
            this.Name = "frm_SL_Invoice";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_SL_Invoice_FormClosing);
            this.Load += new System.EventHandler(this.frm_SL_Invoice_Load);
            this.Shown += new System.EventHandler(this.frm_SL_Invoice_Shown);
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.frm_SL_Invoice_KeyUp);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtInvoiceCode.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtInvoiceDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtInvoiceDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl1)).EndInit();
            this.panelControl1.ResumeLayout(false);
            this.panelControl1.PerformLayout();
            this.flowLayoutPanel1.ResumeLayout(false);
            this.pnlBook.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtTserial.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_InvoiceBook.Properties)).EndInit();
            this.pnlInvCode.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtTinvCode.Properties)).EndInit();
            this.pnlDate.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtTdate.Properties)).EndInit();
            this.pnlBranch.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtTstore.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpStore.Properties)).EndInit();
            this.pnlAgeDate.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtTdueDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DueDays.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DueDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DueDate.Properties)).EndInit();
            this.pnlDeliveryDate.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtTdeliverDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtDeliverDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtDeliverDate.Properties)).EndInit();
            this.pnlCurrency.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtCurrency.Properties)).EndInit();
            this.pnlPostStore.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txt_Post_Date.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Post_Date.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_IsPosted.Properties)).EndInit();
            this.pnlPO.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtTpo.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_PO_No.Properties)).EndInit();
            this.pnlSalesEmp.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtTSalesEmp.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_SalesEmp.Properties)).EndInit();
            this.pnlCostCenter.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.textEdit9.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpCostCenter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv_CostCenter)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNotes.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Shipping.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_IsOutTrns.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cmbPayMethod.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Drawers.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Remains.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_paid.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Customers.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.panelControl2)).EndInit();
            this.panelControl2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.grdPrInvoice)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repSpin)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repTaxTypes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView9)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repCompany)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repCategory)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit2View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repDiscountRatio)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repUOM)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repItems)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_storee)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_expireDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Batch)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repspin_PiecesCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repManufactureDate.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repManufactureDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Libra)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_btnAddTaxes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_store)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemSpinEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Location)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtNet.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Total.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdLastPrices)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtExpenses.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Drawers2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_PayAcc1_Paid.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_PayAcc2_Paid.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.page_AccInfo.ResumeLayout(false);
            this.page_AccInfo.PerformLayout();
            this.Page_LastPrices.ResumeLayout(false);
            this.tabExtraData.ResumeLayout(false);
            this.tabExtraData.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_Cars.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtScaleSerial.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDestination.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtVehicleNumber.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDriverName.Properties)).EndInit();
            this.tabpg_CustomerData.ResumeLayout(false);
            this.tabpg_CustomerData.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.lkpDelivery.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Sales.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Address.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Phone.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Mobile.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit3.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_JOStatus.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_JOPriority.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit5.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_JOSalesEmp.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit8.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_JODept.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit4.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_JOJob.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_JODeliveryDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_JODeliveryDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit7.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit6.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_JORegDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_JORegDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_JOCode.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_AttnMr.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscountRatio.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtDiscountValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_TaxValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DeductTaxR.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_DeductTaxV.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_AddTaxR.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_AddTaxV.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtExpensesR.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_Offer.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_CusTaxR.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_CusTaxV.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_retentionR.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_RetentionV.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_AdvancePayR.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_AdvancePayV.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grp_ExtraRevenues)).EndInit();
            this.grp_ExtraRevenues.ResumeLayout(false);
            this.grp_ExtraRevenues.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txt_ShiftAdd.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_transportation.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Handing.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chk_Approved.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.bookId.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_EtaxValue.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Subtotal.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_SubAfterTax.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_total_b4_Discounts.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_CommercialDiscounts.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_totalAfterCommercial_Disc.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grd_SubTaxes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gv_SubTaxes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_SubTaxes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_bounsDiscount.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnSave;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtnHelp;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraEditors.TextEdit txtInvoiceCode;
        private DevExpress.XtraEditors.SimpleButton btnNext;
        private DevExpress.XtraEditors.SimpleButton btnPrevious;
        private DevExpress.XtraBars.BarButtonItem batBtnList;
        private DevExpress.XtraBars.BarButtonItem barBtnNew;
        private DevExpress.XtraEditors.LabelControl labelControl35;
        private DevExpress.XtraEditors.DateEdit dtInvoiceDate;
        private DevExpress.XtraEditors.PanelControl panelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl36;
        private DevExpress.XtraEditors.MemoEdit txtNotes;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.ImageComboBoxEdit cmbPayMethod;
        private DevExpress.XtraEditors.LabelControl labelControl9;

        private DevExpress.XtraEditors.GridLookUpEdit lkp_Customers;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraEditors.PanelControl panelControl2;
        private DevExpress.XtraGrid.GridControl grdPrInvoice;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn29;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn28;
        private DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit repSpin;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn col_CommercialDiscountValue;
        private DevExpress.XtraGrid.Columns.GridColumn colPurchasePrice;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit repUOM;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit repItems;
        private DevExpress.XtraGrid.Views.Grid.GridView repositoryItemGridLookUpEdit1View;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn31;
        private DevExpress.XtraGrid.Columns.GridColumn col_CurrentQty;
        private DevExpress.XtraGrid.Columns.GridColumn col_TotalSellPrice;
        private DevExpress.XtraEditors.TextEdit txtNet;
        private DevExpress.XtraEditors.LabelControl labelControl13;
        private DevExpress.XtraEditors.LabelControl lbl_Paid;
        private DevExpress.XtraEditors.TextEdit txt_paid;
        private DevExpress.XtraEditors.LabelControl lbl_remains;
        private DevExpress.XtraEditors.TextEdit txt_Remains;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private DevExpress.XtraEditors.LookUpEdit lkp_Drawers;
        private DevExpress.XtraEditors.LabelControl labelControl17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn41;
        private DevExpress.XtraEditors.TextEdit txt_Total;
        private DevExpress.XtraEditors.LabelControl labelControl18;
        private DevExpress.XtraEditors.LabelControl labelControl19;
        private DevExpress.XtraBars.BarButtonItem barBtnCancel;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repDiscountRatio;
        private DevExpress.XtraEditors.LookUpEdit lkpStore;
        private DevExpress.XtraEditors.SimpleButton btnAddCustomer;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem mi_frm_IC_Item;
        private DevExpress.XtraBars.BarButtonItem barBtnNotesReceivable;
        private DevExpress.XtraGrid.GridControl grdLastPrices;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn colTotalPurchasePrice;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraGrid.Columns.GridColumn colUOM;
        private DevExpress.XtraGrid.Columns.GridColumn colQty;
        private DevExpress.XtraGrid.Columns.GridColumn colCustNameAr;
        private DevExpress.XtraGrid.Columns.GridColumn colInvoiceDate;
        private DevExpress.XtraGrid.Columns.GridColumn colInvoiceCode;
        private System.Windows.Forms.ToolStripMenuItem mi_CustLastPrices;
        private System.Windows.Forms.ToolStripMenuItem mi_LastPrices;
        private DevExpress.XtraEditors.SpinEdit txtExpenses;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl20;
        private DevExpress.XtraEditors.LabelControl txt_Balance_After;
        private DevExpress.XtraEditors.LabelControl txt_Balance_Before;
        private DevExpress.XtraEditors.LabelControl txt_MaxCredit;
        private DevExpress.XtraEditors.LabelControl lbl_IsCredit_After;
        private DevExpress.XtraEditors.LabelControl lblBlncAftr;
        private DevExpress.XtraEditors.LabelControl lbl_IsCredit_Before;
        private DevExpress.XtraEditors.LabelControl labelControl24;
        private DevExpress.XtraEditors.LabelControl labelControl10;
        private DevExpress.XtraGrid.Columns.GridColumn col_Expire;
        private DevExpress.XtraGrid.Columns.GridColumn col_Batch;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_expireDate;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn24;
        private DevExpress.XtraEditors.LookUpEdit lkp_SalesEmp;
        private DevExpress.XtraBars.BarSubItem barSubItemPrint;
        private DevExpress.XtraBars.BarButtonItem barbtnPrint;
        private DevExpress.XtraBars.BarButtonItem barbtnPrintF;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraEditors.GroupControl groupControl1;
        private DevExpress.XtraEditors.LookUpEdit lkp_Drawers2;
        private DevExpress.XtraEditors.SpinEdit txt_PayAcc1_Paid;
        private DevExpress.XtraEditors.LabelControl labelControl30;
        private DevExpress.XtraEditors.LabelControl labelControl31;
        private DevExpress.XtraEditors.SpinEdit txt_PayAcc2_Paid;
        private DevExpress.XtraEditors.LabelControl labelControl33;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage Page_LastPrices;
        private DevExpress.XtraTab.XtraTabPage page_AccInfo;
        private DevExpress.XtraBars.BarButtonItem barBtnLoad_Sl_Qoute;
        private DevExpress.XtraEditors.TextEdit txt_PO_No;
        private DevExpress.XtraEditors.DateEdit dtDeliverDate;
        private DevExpress.XtraEditors.DateEdit txt_DueDate;
        private DevExpress.XtraEditors.SpinEdit txt_DueDays;
        private DevExpress.XtraEditors.TextEdit txt_AttnMr;
        private DevExpress.XtraEditors.LabelControl labelControl41;
        private DevExpress.XtraGrid.Columns.GridColumn col_Length;
        private DevExpress.XtraGrid.Columns.GridColumn col_Width;
        private DevExpress.XtraGrid.Columns.GridColumn col_Height;
        private DevExpress.XtraEditors.TextEdit txtTdueDate;
        private DevExpress.XtraEditors.TextEdit txtTdeliverDate;
        private DevExpress.XtraEditors.TextEdit txtTserial;
        private DevExpress.XtraEditors.TextEdit txtTinvCode;
        private DevExpress.XtraEditors.TextEdit txtTdate;
        private DevExpress.XtraEditors.TextEdit txtTstore;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraGrid.Columns.GridColumn col_TotalQty;
        private DevExpress.XtraEditors.TextEdit txtTSalesEmp;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.LabelControl lblShipTo;
        private DevExpress.XtraEditors.TextEdit txtTpo;
        private DevExpress.XtraEditors.MemoEdit txt_Shipping;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.LabelControl labelControl12;
        private DevExpress.XtraGrid.Columns.GridColumn col_ItemDescription;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn25;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn26;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn27;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn30;
        private DevExpress.XtraBars.BarButtonItem barBtnLoad_SalesOrder;
        private DevExpress.XtraBars.BarButtonItem barBtnLoad_IC_OutTrns;
        private DevExpress.XtraGrid.Columns.GridColumn col_PiecesCount;
        private DevExpress.XtraEditors.CheckEdit chk_IsOutTrns;
        private System.Windows.Forms.ToolStripMenuItem mi_PasteRows;
        private DevExpress.XtraGrid.Columns.GridColumn col_ItemDescriptionEn;
        private DevExpress.XtraTab.XtraTabPage page_JobOrder;
        private DevExpress.XtraEditors.TextEdit textEdit4;
        private DevExpress.XtraEditors.TextEdit txt_JOJob;
        private DevExpress.XtraEditors.TextEdit textEdit2;
        private DevExpress.XtraEditors.DateEdit txt_JODeliveryDate;
        private DevExpress.XtraEditors.TextEdit textEdit7;
        private DevExpress.XtraEditors.TextEdit textEdit6;
        private DevExpress.XtraEditors.DateEdit txt_JORegDate;
        private DevExpress.XtraEditors.TextEdit txt_JOCode;
        private DevExpress.XtraEditors.TextEdit textEdit3;
        private DevExpress.XtraEditors.LookUpEdit lkp_JOStatus;
        private DevExpress.XtraEditors.TextEdit textEdit1;
        private DevExpress.XtraEditors.LookUpEdit lkp_JOPriority;
        private DevExpress.XtraEditors.TextEdit textEdit5;
        private DevExpress.XtraEditors.LookUpEdit lkp_JOSalesEmp;
        private DevExpress.XtraEditors.TextEdit textEdit8;
        private DevExpress.XtraEditors.LookUpEdit lkp_JODept;
        private DevExpress.XtraBars.BarButtonItem barBtnLoad_JO;
        private DevExpress.XtraGrid.Columns.GridColumn col_SalesTax;
        private DevExpress.XtraEditors.LabelControl labelControl16;
        private DevExpress.XtraEditors.LabelControl labelControl15;
        private DevExpress.XtraEditors.LabelControl labelControl14;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraBars.BarButtonItem barBtnLoad_PR_Invoice;
        private DevExpress.XtraGrid.Columns.GridColumn col_DiscountRatio2;
        private DevExpress.XtraGrid.Columns.GridColumn col_DiscountRatio3;
        private System.Windows.Forms.ToolStripMenuItem mi_ExportData;
        private DevExpress.XtraEditors.LookUpEdit lkp_InvoiceBook;
        private DevExpress.XtraEditors.LabelControl labelControl25;
        private DevExpress.XtraEditors.LabelControl labelControl23;
        private DevExpress.XtraEditors.LabelControl labelControl22;
        private DevExpress.XtraEditors.LabelControl labelControl21;
        private DevExpress.XtraEditors.SpinEdit txtDiscountRatio;
        private DevExpress.XtraEditors.SpinEdit txtDiscountValue;
        private DevExpress.XtraEditors.SpinEdit txt_TaxValue;
        private DevExpress.XtraEditors.SpinEdit txt_DeductTaxR;
        private DevExpress.XtraEditors.SpinEdit txt_DeductTaxV;
        private DevExpress.XtraEditors.SpinEdit txt_AddTaxR;
        private DevExpress.XtraEditors.SpinEdit txt_AddTaxV;
        private DevExpress.XtraEditors.SpinEdit txtExpensesR;
        private System.Windows.Forms.ToolStripMenuItem mi_InvoiceStaticDisc;
        private uc_Currency uc_Currency1;
        private DevExpress.XtraEditors.TextEdit txtCurrency;
        private System.Windows.Forms.Panel pnlDeliveryDate;
        private System.Windows.Forms.Panel pnlAgeDate;
        private System.Windows.Forms.Panel pnlBook;
        private System.Windows.Forms.Panel pnlInvCode;
        private System.Windows.Forms.Panel pnlDate;
        private System.Windows.Forms.Panel pnlBranch;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel1;
        private System.Windows.Forms.Panel pnlCurrency;
        private System.Windows.Forms.Panel pnlPO;
        private System.Windows.Forms.Panel pnlSalesEmp;
        private DevExpress.XtraEditors.LabelControl lbl_Validate_MaxLimit;
        private System.Windows.Forms.Panel pnlCostCenter;
        private DevExpress.XtraEditors.TextEdit textEdit9;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_Batch;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView6;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn33;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn34;
        private System.Windows.Forms.ToolStripMenuItem mi_InvoiceStaticDimensions;
        private DevExpress.XtraBars.BarButtonItem barBtnLoad_IC_Transfer;
        private DevExpress.XtraTab.XtraTabPage tabExtraData;
        private DevExpress.XtraEditors.TextEdit txtDriverName;
        private DevExpress.XtraEditors.LabelControl lblDriverName;
        private DevExpress.XtraEditors.TextEdit txtDestination;
        private DevExpress.XtraEditors.LabelControl lblDestination;
        private DevExpress.XtraEditors.TextEdit txtVehicleNumber;
        private DevExpress.XtraEditors.LabelControl lblVehicleNumber;
        private DevExpress.XtraBars.BarButtonItem barBtn_OutTrns;
        private DevExpress.XtraBars.BarButtonItem barBtn_CashNote;
        private DevExpress.XtraGrid.Columns.GridColumn col_CompanyNameAr;
        private DevExpress.XtraGrid.Columns.GridColumn col_CategoryNameAr;
        private DevExpress.XtraEditors.TextEdit txtScaleSerial;
        private DevExpress.XtraEditors.LabelControl labelControl26;
        private System.Windows.Forms.ToolStripMenuItem mi_ImportExcel;
        private DevExpress.XtraGrid.Columns.GridColumn col_Serial;
        private DevExpress.XtraGrid.Columns.GridColumn grdcol_branch;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit lkp_store;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit lkp_storee;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView7;
        private System.Windows.Forms.Panel pnlPostStore;
        private DevExpress.XtraEditors.DateEdit txt_Post_Date;
        private DevExpress.XtraEditors.CheckEdit chk_IsPosted;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn32;
        private DevExpress.XtraEditors.Repository.RepositoryItemDateEdit repManufactureDate;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn35;
        private DevExpress.XtraEditors.CheckEdit chk_Offer;
        private DevExpress.XtraEditors.LabelControl labelControl27;
        private DevExpress.XtraEditors.LabelControl labelControl28;
        private DevExpress.XtraEditors.LabelControl labelControl32;
        private DevExpress.XtraEditors.LabelControl labelControl34;
        private DevExpress.XtraEditors.SpinEdit txt_CusTaxR;
        private DevExpress.XtraEditors.SpinEdit txt_CusTaxV;
        private DevExpress.XtraGrid.Columns.GridColumn col_CusTax;
        private DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit repositoryItemSpinEdit1;
        private DevExpress.XtraBars.BarButtonItem btnAttachments;
        private DevExpress.XtraBars.BarButtonItem barbtnvisanote;
        private DevExpress.XtraEditors.LabelControl labelControl37;
        private DevExpress.XtraEditors.LabelControl labelControl38;
        private DevExpress.XtraEditors.LabelControl labelControl39;
        private DevExpress.XtraEditors.LabelControl labelControl42;
        private DevExpress.XtraEditors.LabelControl labelControl43;
        private DevExpress.XtraEditors.LabelControl labelControl44;
        private DevExpress.XtraEditors.LabelControl labelControl45;
        private DevExpress.XtraEditors.LabelControl labelControl46;
        private DevExpress.XtraEditors.SpinEdit txt_retentionR;
        private DevExpress.XtraEditors.SpinEdit txt_RetentionV;
        private DevExpress.XtraEditors.SpinEdit txt_AdvancePayR;
        private DevExpress.XtraEditors.SpinEdit txt_AdvancePayV;
        private DevExpress.XtraEditors.SimpleButton btn_ShowAccStatement;
        private DevExpress.XtraGrid.Columns.GridColumn col_Serial2;
        private DevExpress.XtraGrid.Columns.GridColumn col_AudiencePrice;
        private DevExpress.XtraBars.BarButtonItem barbtnPrintStore;
        private DevExpress.XtraBars.BarSubItem barbtnSamplesPr;
        private DevExpress.XtraGrid.Columns.GridColumn col_Location;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit rep_Location;
        private DevExpress.XtraGrid.Columns.GridColumn col_Libra;
        private DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit rep_Libra;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn36;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn37;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn38;
        private DevExpress.XtraEditors.GroupControl grp_ExtraRevenues;
        private DevExpress.XtraEditors.SpinEdit txt_transportation;
        private DevExpress.XtraEditors.LabelControl labelControl49;
        private DevExpress.XtraEditors.SpinEdit txt_Handing;
        private DevExpress.XtraEditors.LabelControl labelControl50;
        private DevExpress.XtraGrid.Columns.GridColumn col_kg_Weight_libra;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn39;
        private DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit repspin_PiecesCount;
        private DevExpress.XtraTab.XtraTabPage tabpg_CustomerData;
        private DevExpress.XtraEditors.LabelControl labelControl52;
        private DevExpress.XtraEditors.TextEdit txt_Address;
        private DevExpress.XtraEditors.LabelControl labelControl51;
        private DevExpress.XtraEditors.TextEdit txt_Phone;
        private DevExpress.XtraEditors.LabelControl labelControl48;
        private DevExpress.XtraEditors.TextEdit txt_Mobile;
        private DevExpress.XtraEditors.LabelControl labelControl47;
        private DevExpress.XtraEditors.LookUpEdit txt_Sales;
        private DevExpress.XtraEditors.LookUpEdit lkp_Cars;
        private DevExpress.XtraGrid.Columns.GridColumn col_ActualPiecesCount;
        private DevExpress.XtraEditors.SpinEdit txt_ShiftAdd;
        private DevExpress.XtraEditors.LabelControl labelControl53;
        private DevExpress.XtraEditors.SimpleButton btnCeil;
        private DevExpress.XtraGrid.Columns.GridColumn ol_Index;
        private DevExpress.XtraBars.BarButtonItem barbtn_EditPerm;
        private DevExpress.XtraBars.BarButtonItem barbtn_DelPerm;
        private DevExpress.XtraBars.BarButtonItem barbtn_PrintPerm;
        private System.Windows.Forms.Timer timer1;
        private DevExpress.XtraEditors.LabelControl labelControl54;
        private DevExpress.XtraEditors.LookUpEdit lkpDelivery;
        private DevExpress.XtraGrid.Columns.GridColumn col_SellDiscountRatio;
        private DevExpress.XtraEditors.CheckEdit chk_Approved;
        private DevExpress.XtraGrid.Columns.GridColumn col_itemperoffer;
        private DevExpress.XtraGrid.Columns.GridColumn col_QC;
        private DevExpress.XtraGrid.Columns.GridColumn col_Pack;
        private DevExpress.XtraEditors.GridLookUpEdit lkpCostCenter;
        private DevExpress.XtraGrid.Views.Grid.GridView gv_CostCenter;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn43;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn42;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn40;
        private DevExpress.XtraGrid.Columns.GridColumn col_ItemNameF;
        private DevExpress.XtraEditors.ImageComboBoxEdit bookId;
        private DevExpress.XtraEditors.LabelControl labelControl55;
        private DevExpress.XtraGrid.Columns.GridColumn Col_Company;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit repCompany;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView8;
        private DevExpress.XtraGrid.Columns.GridColumn ColCompanyNameAr;
        private DevExpress.XtraGrid.Columns.GridColumn Col_CompanyCode;
        private DevExpress.XtraGrid.Columns.GridColumn CompanyId;
        private DevExpress.XtraGrid.Columns.GridColumn Col_Category;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit repCategory;
        private DevExpress.XtraGrid.Views.Grid.GridView repositoryItemGridLookUpEdit2View;
        private DevExpress.XtraGrid.Columns.GridColumn ColCatNumber;
        private DevExpress.XtraGrid.Columns.GridColumn ColCategoryNameAr;
        private DevExpress.XtraGrid.Columns.GridColumn ColCategoryId;
        private DevExpress.XtraGrid.Columns.GridColumn ColLength;
        private DevExpress.XtraGrid.Columns.GridColumn ColWidth;
        private DevExpress.XtraGrid.Columns.GridColumn ColHeight;
        private DevExpress.XtraGrid.Columns.GridColumn colBonusDiscount;
        private DevExpress.XtraGrid.Columns.GridColumn colETaxValue;
        private DevExpress.XtraGrid.Columns.GridColumn colETaxRatio;
        private DevExpress.XtraGrid.Columns.GridColumn Col_ETaxType;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit repTaxTypes;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView9;
        private DevExpress.XtraGrid.Columns.GridColumn ColDescriptionAr;
        private DevExpress.XtraGrid.Columns.GridColumn ColCode;
        private DevExpress.XtraGrid.Columns.GridColumn colE_TaxableTypeId;
        private DevExpress.XtraGrid.Columns.GridColumn col_SellPrice;
        private DevExpress.XtraEditors.LabelControl labelControl29;
        private DevExpress.XtraEditors.SpinEdit txt_EtaxValue;
        private DevExpress.XtraBars.BarButtonItem barBtnDelete;
        private DevExpress.XtraEditors.SpinEdit txt_Subtotal;
        private DevExpress.XtraEditors.LabelControl labelControl56;
        private DevExpress.XtraEditors.SpinEdit txt_SubAfterTax;
        private DevExpress.XtraEditors.LabelControl labelControl40;
        private DevExpress.XtraEditors.TextEdit txt_CommercialDiscounts;
        private DevExpress.XtraEditors.TextEdit txt_total_b4_Discounts;
        private DevExpress.XtraEditors.LabelControl lbl_CommercialDiscounts;
        private DevExpress.XtraEditors.LabelControl lbl_total_b4_Discounts;
        private DevExpress.XtraEditors.TextEdit txt_totalAfterCommercial_Disc;
        private DevExpress.XtraEditors.LabelControl lbl_totalAfterCommercial_Disc;
        private DevExpress.XtraGrid.Columns.GridColumn btn_AddTaxes;
        private DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit rep_btnAddTaxes;
        private DevExpress.XtraGrid.Columns.GridColumn TotalTaxes;
        private DevExpress.XtraGrid.Columns.GridColumn totalTaxesRatio;
        private DevExpress.XtraGrid.Columns.GridColumn DiscountTax;
        private DevExpress.XtraGrid.Columns.GridColumn DiscountTaxRatio;
        private DevExpress.XtraGrid.Columns.GridColumn salePriceWithTaxTable;
        private DevExpress.XtraGrid.Columns.GridColumn totalTableTaxes;
        private DevExpress.XtraGrid.GridControl grd_SubTaxes;
        private DevExpress.XtraGrid.Views.Grid.GridView gv_SubTaxes;
        private DevExpress.XtraGrid.Columns.GridColumn Value;
        private DevExpress.XtraGrid.Columns.GridColumn SubTaxId;
        private DevExpress.XtraEditors.Repository.RepositoryItemLookUpEdit lkp_SubTaxes;
        private DevExpress.XtraGrid.Columns.GridColumn col_Rate;
        private DevExpress.XtraGrid.Columns.GridColumn addTaxValue;
        private DevExpress.XtraGrid.Columns.GridColumn tableTaxValue;
        private DevExpress.XtraEditors.LabelControl labelControl57;
        private DevExpress.XtraEditors.SpinEdit txt_bounsDiscount;
        private DevExpress.XtraGrid.Columns.GridColumn col_TaxValue;
        private DevExpress.XtraGrid.Columns.GridColumn col_TotalSubCustomTax;
        private DevExpress.XtraGrid.Columns.GridColumn col_TotalSubAddTax;
        private DevExpress.XtraGrid.Columns.GridColumn col_TotalSubDiscountTax;
    }
}