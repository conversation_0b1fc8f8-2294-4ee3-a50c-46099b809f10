using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using DAL;
using System.Linq;
using System.Data;
using DevExpress.XtraEditors;

using System.Windows.Forms;

namespace Reports
{
    public partial class rpt_PR_PurchaseOrder : DevExpress.XtraReports.UI.XtraReport
    {
        string vendor, serial, number, date, store, notes,
            total, tax, discountR, discountV, net, userName,
            AttnMr, AttnMr_Job, Ven_Tel, Ven_Fax, DeliverDate, shipTo;
        string _outTrnsItemQty, _invItemQty;
        DataTable dt_inv_details;
        IList InTrns, PrInv;
        int CrncId;

        VendorInfo vendor_; ACC_CostCenter _CostCenter; int cmdProcess; string SourceCode;

        public rpt_PR_PurchaseOrder()
        {
            InitializeComponent();
        }
        //public rpt_PR_PurchaseOrder(string _vendor, string _serial, string _number, string _date, string _store, 
        //    string _notes, string _total, string _tax, string _discountR, string _discountV, string _net,
        //    DataTable dt, string userName, string _AttnMr, string _AttnMr_Job, string _Ven_Tel,
        //    string _Ven_Fax, string _DeliverDate, string shipTo, IList inTrns, IList prInv, string _outTrnsItemQty, string _invItemQty)
        //{
        //    InitializeComponent();            
        //    vendor=_vendor;
        //    serial=_serial;
        //    number=_number; 
        //    date=_date;
        //    store=_store;            
        //    notes=_notes;
        //    total=_total; 
        //    tax=_tax; 
        //    discountR=_discountR;
        //    discountV = _discountV;            
        //    net=_net;            
        //    this.userName = userName;

        //    AttnMr = _AttnMr;
        //    AttnMr_Job = _AttnMr_Job;

        //    Ven_Tel = _Ven_Tel;
        //    Ven_Fax = _Ven_Fax;
        //    DeliverDate = _DeliverDate;
        //    this.shipTo = shipTo;

        //    dt_inv_details = dt;
        //    this.InTrns = inTrns;
        //    this.PrInv = prInv;

        //    this._outTrnsItemQty = _outTrnsItemQty;
        //    this._invItemQty = _invItemQty;

        //    //this.DataSource = dt_inv_details;
        //    getReportHeader();
        //    //LoadData();            
        //}

        public rpt_PR_PurchaseOrder(string _vendor, string _serial, string _number, string _date, string _store,
          string _notes, string _total, string _tax, string _discountR, string _discountV, string _net,
          DataTable dt, string userName, string _AttnMr, string _AttnMr_Job, string _Ven_Tel,
          string _Ven_Fax, string _DeliverDate, string shipTo, IList inTrns, IList prInv, string _outTrnsItemQty, string _invItemQty,
          VendorInfo vendor_, ACC_CostCenter _CostCenter, int cmdProcess, string SourceCode, int _CrncId)
        {
            InitializeComponent();

            this.vendor_ = vendor_;
            this._CostCenter = _CostCenter;
            this.cmdProcess = cmdProcess; this.SourceCode = SourceCode;

            vendor = _vendor;
            serial = _serial;
            number = _number;
            date = _date;
            store = _store;
            notes = _notes;
            total = _total;
            tax = _tax;
            discountR = _discountR;
            discountV = _discountV;
            net = _net;
            this.userName = userName;

            AttnMr = _AttnMr;
            AttnMr_Job = _AttnMr_Job;

            Ven_Tel = _Ven_Tel;
            Ven_Fax = _Ven_Fax;
            DeliverDate = _DeliverDate;
            this.shipTo = shipTo;

            dt_inv_details = dt;
            var c = dt.Rows.Count;
            this.InTrns = inTrns;
            this.PrInv = prInv;

            this._outTrnsItemQty = _outTrnsItemQty;
            this._invItemQty = _invItemQty;
            CrncId = _CrncId;
            //this.DataSource = dt_inv_details;
            //LoadData();            
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                lblCompName.Text = comp.CmpNameAr;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        public void LoadData()
        {
            ERPDataContext db = new ERPDataContext();
            if (_CostCenter != null)
            {
                txt_CostCenter.Text = Shared.IsEnglish ? _CostCenter.CostCenterNameEn : _CostCenter.CostCenterName;
                txt_CostCenterCode.Text = _CostCenter.CostCenterCode.ToString();
            }
            txtReferenceNo.Text = cmdProcess == (int)DAL.Process.Pr_Qoute ? SourceCode : "";
            txt_VendorEmail.Text = vendor_.Email;
            if (cmdProcess == (int)DAL.Process.Pr_Qoute)
                txt_QutationDate.Text = db.PR_Quotes.Where(x => x.InvoiceCode == SourceCode).
                Select(x => x.InvoiceDate.ToShortDateString()).SingleOrDefault();

            lbl_date.Text = date;
            lbl_DiscountR.Text = discountR;
            lbl_DiscountV.Text = discountV;

            lbl_Net.Text = net;

            lblTotalWords.Text = Shared.IsEnglish ? HelperAcc.ConvertMoneyToText(net, CrncId, Shared.lstCurrency) :
                                   HelperAcc.ConvertMoneyToArabicText(net, CrncId, Shared.lstCurrency);

            lbl_notes.Text = notes;
            lbl_Number.Text = number;
            lbl_Serial.Text = serial;
            lbl_store.Text = store;
            lbl_tax.Text = tax;
            lbl_Total.Text = total;
            lbl_Vendor.Text = vendor;
            lbl_User.Text = userName;

            lbl_AttnMr.Text = AttnMr;
            lbl_AttnMr_Job.Text = AttnMr_Job;

            lbl_VenTel.Text = Ven_Tel;
            lbl_VenFax.Text = Ven_Fax;

            lbl_DeliverDate.Text = DeliverDate;
            lbl_ShipTo.Text = shipTo;

            DetailReport_Po_Order.DataSource = dt_inv_details;
            cell_code.DataBindings.Add("Text", dt_inv_details, "ItemCode1");
            cell_code2.DataBindings.Add("Text", dt_inv_details, "ItemCode2");
            cell_Disc.DataBindings.Add("Text", dt_inv_details, "DiscountValue");
            cell_Price.DataBindings.Add("Text", dt_inv_details, "PurchasePrice");
            cell_Qty.DataBindings.Add("Text", dt_inv_details, "Qty");
            cell_Total.DataBindings.Add("Text", dt_inv_details, "TotalPurchasePrice");
            cell_ItemName.DataBindings.Add("Text", dt_inv_details, "ItemName");


            cell_UOM.DataBindings.Add("Text", dt_inv_details, "UOM");
            cell_Height.DataBindings.Add("Text", dt_inv_details, "Height");
            cell_Width.DataBindings.Add("Text", dt_inv_details, "Width");
            cell_Length.DataBindings.Add("Text", dt_inv_details, "Length");
            cell_TotalQty.DataBindings.Add("Text", dt_inv_details, "TotalQty");
            cell_Factor.DataBindings.Add("Text", dt_inv_details, "Factor");
            Cell_MUOM.DataBindings.Add("Text", dt_inv_details, "MUOM");
            Cell_MUOM_Factor.DataBindings.Add("Text", dt_inv_details, "MUOM_Factor");

            //In trns
            DetailReport_InTrns.DataSource = InTrns;
            col_OutTrns_ItemCode1.DataBindings.Add("Text", this.InTrns, "ItemCode1");
            col_OutTrns_ItemCode2.DataBindings.Add("Text", this.InTrns, "ItemCode2");
            col_OutTrns_ItemName.DataBindings.Add("Text", this.InTrns, "ItemId");
            col_OutTrns_UOM.DataBindings.Add("Text", this.InTrns, "UOM");
            col_OutTrns_Qty.DataBindings.Add("Text", this.InTrns, "Qty");
            col_OuTrans_Expire.DataBindings.Add("Text", this.InTrns, "Expire");
            col_OutTrns_Batch.DataBindings.Add("Text", this.InTrns, "Batch");
            col_OutTrns_Length.DataBindings.Add("Text", this.InTrns, "Length");
            col_OutTrns_Width.DataBindings.Add("Text", this.InTrns, "Width");
            col_OutTrns_Height.DataBindings.Add("Text", this.InTrns, "Height");
            col_OutTrns_TotlQty.DataBindings.Add("Text", this.InTrns, "TotalQty");
            col_OutTrns_PcCount.DataBindings.Add("Text", this.InTrns, "PiecesCount");
            col_OutTrns_Qc.DataBindings.Add("Text", this.InTrns, "QC");
            col_OutTrns_Code.DataBindings.Add("Text", this.InTrns, "OutTrnsCode");
            col_OutTrns_Store.DataBindings.Add("Text", this.InTrns, "StoreId");
            col_OutTrns_Date.DataBindings.Add("Text", this.InTrns, "OutTrnsDate");

            cell_BillQtySum.Text = _outTrnsItemQty;

            //Pr Inv
            DetailReport_PrInv.DataSource = PrInv;
            col_SlInv_Code1.DataBindings.Add("Text", this.PrInv, "ItemCode1");
            col_SlInv_Code2.DataBindings.Add("Text", this.PrInv, "ItemCode2");
            col_SlInv_ItemName.DataBindings.Add("Text", this.PrInv, "ItemId");
            col_SlInv_UOM.DataBindings.Add("Text", this.PrInv, "UOM");
            col_SlInv_Qty.DataBindings.Add("Text", this.PrInv, "Qty");
            col_SlInv_Expire.DataBindings.Add("Text", this.PrInv, "Expire");
            col_SlInv_Batch.DataBindings.Add("Text", this.PrInv, "Batch");
            col_SlInv_Length.DataBindings.Add("Text", this.PrInv, "Length");
            col_SlInv_Width.DataBindings.Add("Text", this.PrInv, "Width");
            col_SlInv_Height.DataBindings.Add("Text", this.PrInv, "Height");
            col_SlInv_TotlQty.DataBindings.Add("Text", this.PrInv, "TotalQty");
            col_SlInv_PcCount.DataBindings.Add("Text", this.PrInv, "PiecesCount");
            col_SlInv_Qc.DataBindings.Add("Text", this.PrInv, "QC");
            col_SlInv_InvCode.DataBindings.Add("Text", this.PrInv, "OutTrnsCode");
            col_SlInv_Branch.DataBindings.Add("Text", this.PrInv, "StoreId");
            col_SlInv_Date.DataBindings.Add("Text", this.PrInv, "OutTrnsDate");

            cell_InvQtySum.Text = _invItemQty;

            getReportHeader();

        }

    }
}
