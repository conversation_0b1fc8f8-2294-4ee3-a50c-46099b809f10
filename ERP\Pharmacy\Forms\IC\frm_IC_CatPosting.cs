﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Base;
using DAL;
using DAL.Res;

namespace Pharmacy.Forms
{
    public partial class frm_IC_CatPosting : DevExpress.XtraEditors.XtraForm
    {
        ERPDataContext DB = new ERPDataContext();
        List<IC_Category> lstChildCats = new List<IC_Category>();

        public frm_IC_CatPosting()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);            
            
            rep_Acc.DisplayMember = "AccName";
            rep_Acc.ValueMember = "AccId";
            rep_Acc.DataSource = HelperAcc.LoadAccountsTree(0, true);

            lstChildCats = MyHelper.GetChildCategoriesList();

            grdPrInvoice.DataSource = lstChildCats;            

            ErpUtils.ColumnChooser(grdPrInvoice);
        }
        

        private void frm_InvChangePrices_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);
        }

        private void barBtnSave_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //((GridView)grdPrInvoice.FocusedView).FocusedRowHandle += 1;
            gridView2.CloseEditor();
            gridView2.UpdateCurrentRow();

            var dbCats = DB.IC_Categories;
            foreach(var c in dbCats)
            {
                IC_Category childCat = lstChildCats.Where(x=> x.CategoryId == c.CategoryId).FirstOrDefault();
                if(childCat != null)
                {
                    c.SellAcc = childCat.SellAcc;
                    c.SellReturnAcc = childCat.SellReturnAcc;
                    c.COGSAcc = childCat.COGSAcc;
                    c.InvAcc = childCat.InvAcc;
                }
            }

            DB.SubmitChanges();

            XtraMessageBox.Show(
                Shared.IsEnglish == true ? ResEn.MsgSave : ResAr.MsgSave
                , "", MessageBoxButtons.OK, MessageBoxIcon.Information);

        }

        private void barButtonItem1_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }        
    }    
}