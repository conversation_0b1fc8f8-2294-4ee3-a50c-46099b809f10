﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="lkp_Crnc.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="lkp_Crnc.Location" type="System.Drawing.Point, System.Drawing">
    <value>59, 0</value>
  </data>
  <data name="lkp_Crnc.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>0, 0, 0, 0</value>
  </data>
  <data name="lkp_Crnc.Properties.AppearanceDisabled.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="lkp_Crnc.Properties.AppearanceDisabled.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>64, 64, 64</value>
  </data>
  <assembly alias="DevExpress.Utils.v15.1" name="DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="lkp_Crnc.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Combo</value>
  </data>
  <data name="lkp_Crnc.Properties.Columns" xml:space="preserve">
    <value>CrncId</value>
  </data>
  <data name="lkp_Crnc.Properties.Columns1" xml:space="preserve">
    <value>CrncId</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="lkp_Crnc.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="lkp_Crnc.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_Crnc.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Crnc.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_Crnc.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_Crnc.Properties.Columns7" xml:space="preserve">
    <value>crncName</value>
  </data>
  <data name="lkp_Crnc.Properties.Columns8" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="lkp_Crnc.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="lkp_Crnc.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v15.1">
    <value>None</value>
  </data>
  <data name="lkp_Crnc.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Crnc.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_Crnc.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Far</value>
  </data>
  <data name="lkp_Crnc.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_Crnc.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="lkp_Crnc.Size" type="System.Drawing.Size, System.Drawing">
    <value>71, 20</value>
  </data>
  <data name="lkp_Crnc.TabIndex" type="System.Int32, mscorlib">
    <value>110</value>
  </data>
  <data name="&gt;&gt;lkp_Crnc.Name" xml:space="preserve">
    <value>lkp_Crnc</value>
  </data>
  <data name="&gt;&gt;lkp_Crnc.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkp_Crnc.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lkp_Crnc.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="txtRate.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left</value>
  </data>
  <data name="txtRate.EditValue" type="System.Decimal, mscorlib">
    <value>99.9</value>
  </data>
  <data name="txtRate.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="txtRate.Properties.AppearanceReadOnly.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>WhiteSmoke</value>
  </data>
  <data name="txtRate.Properties.AppearanceReadOnly.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Gray</value>
  </data>
  <data name="txtRate.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v15.1">
    <value>Ellipsis</value>
  </data>
  <data name="txtRate.Properties.Buttons1" xml:space="preserve">
    <value />
  </data>
  <data name="txtRate.Properties.Buttons2" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="txtRate.Properties.Buttons3" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtRate.Properties.Buttons4" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtRate.Properties.Buttons5" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <assembly alias="DevExpress.XtraEditors.v15.1" name="DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="txtRate.Properties.Buttons6" type="DevExpress.XtraEditors.ImageLocation, DevExpress.XtraEditors.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="txtRate.Properties.Buttons7" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtRate.Properties.Buttons8" xml:space="preserve">
    <value />
  </data>
  <data name="txtRate.Properties.Buttons9" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtRate.Properties.Buttons10" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtRate.Properties.Buttons11" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtRate.Properties.Mask.EditMask" xml:space="preserve">
    <value>#####.######</value>
  </data>
  <data name="txtRate.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtRate.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="txtRate.Size" type="System.Drawing.Size, System.Drawing">
    <value>59, 20</value>
  </data>
  <data name="txtRate.TabIndex" type="System.Int32, mscorlib">
    <value>111</value>
  </data>
  <data name="&gt;&gt;txtRate.Name" xml:space="preserve">
    <value>txtRate</value>
  </data>
  <data name="&gt;&gt;txtRate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txtRate.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;txtRate.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="$this.Size" type="System.Drawing.Size, System.Drawing">
    <value>130, 19</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>uc_Currency_Reports</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.XtraUserControl, DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
</root>