﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;
using DAL.Res;
using System.Linq;
using DevExpress.XtraPrinting;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Base;
using System.Threading;
using Reports;

namespace Pharmacy.Forms
{
    public partial class frm_SL_Delivery : DevExpress.XtraEditors.XtraForm
    {
        ERPDataContext DB = new ERPDataContext();
        UserPriv prvlg;

        public frm_SL_Delivery()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
        }
        private void barBtnSave_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                DB.SubmitChanges();
                XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResICEn.MsgSave : ResICAr.MsgSave, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResICEn.MsgIncorrectData : ResICAr.MsgIncorrectData, "", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void frm_SL_Delivery_Load(object sender, EventArgs e)
        {

            LoadPrivilege();
            BindingSource();
        }

        private void BindingSource()
        {
            gcDelivery.DataSource = from d in DB.SL_Deliveries
                                      select d;

            ReportsUtils.ColumnChooser(gcDelivery);
        }

        private void frm_SL_Delivery_FormClosing(object sender, FormClosingEventArgs e)
        {
        }

        private void gridView3_KeyDown(object sender, KeyEventArgs e)
        {
            GridView view = sender as GridView;
            if (e.KeyCode == Keys.Delete && e.Modifiers == Keys.Control)
            {

                if (MessageBox.Show(Shared.IsEnglish ? ResICEn.MsgDelRow : ResICAr.MsgDelRow, Shared.IsEnglish ? ResICEn.MsgTQues : ResICAr.MsgTQues, MessageBoxButtons.YesNo, MessageBoxIcon.Question) !=
                  DialogResult.Yes)
                    return;

                view.DeleteRow(view.FocusedRowHandle);
            }
        }

        private void gridView3_ValidateRow(object sender, ValidateRowEventArgs e)
        {
            try
            {
                ColumnView view = sender as ColumnView;

                if (view.GetRowCellValue(e.RowHandle, view.Columns["Delivery"]) == null || view.GetRowCellValue(e.RowHandle, view.Columns["Delivery"]).ToString().Trim() == string.Empty)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["Delivery"], Shared.IsEnglish ? "You should enter Delivery way." : "يجب إدخال طريقة التسليم");
                }
            }
            catch
            {
                e.Valid = false;
            }
        }


        private void gridView3_InvalidRowException(object sender, InvalidRowExceptionEventArgs e)
        {
            e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.NoAction;
        }

        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                
                prvlg = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.SL_SalesOrder).FirstOrDefault();

                if (!prvlg.CanDel)
                    gridView3.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
                if (!prvlg.CanAdd)
                    gridView3.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
                
                if (!prvlg.CanEdit)
                    gridView3.OptionsBehavior.ReadOnly = true;

            }
        }

    }
}