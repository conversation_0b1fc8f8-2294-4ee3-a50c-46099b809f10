namespace Reports
{
    partial class rpt_ACC_Statment
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(rpt_ACC_Statment));
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.xrTable2 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow2 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_balance = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_credit = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_debit = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_JCode = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_process = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_notes = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_insertDate = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_index = new DevExpress.XtraReports.UI.XRTableCell();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.xrLabel3 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_CurrencyName = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLine1 = new DevExpress.XtraReports.UI.XRLine();
            this.lbl_User = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel6 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_toDate = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel4 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_accountName = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel7 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_accountType = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel2 = new DevExpress.XtraReports.UI.XRLabel();
            this.lblReportName = new DevExpress.XtraReports.UI.XRLabel();
            this.lblCompName = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_fromDate = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel8 = new DevExpress.XtraReports.UI.XRLabel();
            this.picLogo = new DevExpress.XtraReports.UI.XRPictureBox();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.PageHeader = new DevExpress.XtraReports.UI.PageHeaderBand();
            this.xrTable1 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow1 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell9 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell6 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell10 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell7 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell5 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell8 = new DevExpress.XtraReports.UI.XRTableCell();
            this.ReportFooter = new DevExpress.XtraReports.UI.ReportFooterBand();
            this.xrTable4 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow5 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_JNumber = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_CrncId = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_CrncRate = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_fDebit = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_fCredit = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrLabel1 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrTable3 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow3 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_header_P5 = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_header_P4 = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_header_P3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_header_P2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_header_P1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_header_P0 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableRow4 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_P5 = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_P4 = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_P3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_P2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_P1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_P0 = new DevExpress.XtraReports.UI.XRTableCell();
            this.lbl_TotalCredit = new DevExpress.XtraReports.UI.XRLabel();
            this.lblTotal = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_TotalDebit = new DevExpress.XtraReports.UI.XRLabel();
            this.xrPageInfo1 = new DevExpress.XtraReports.UI.XRPageInfo();
            this.xrCrossBandBox1 = new DevExpress.XtraReports.UI.XRCrossBandBox();
            this.xrCrossBandBox2 = new DevExpress.XtraReports.UI.XRCrossBandBox();
            this.xrCrossBandLine1 = new DevExpress.XtraReports.UI.XRCrossBandLine();
            this.xrCrossBandLine2 = new DevExpress.XtraReports.UI.XRCrossBandLine();
            this.xrCrossBandLine3 = new DevExpress.XtraReports.UI.XRCrossBandLine();
            this.xrCrossBandLine5 = new DevExpress.XtraReports.UI.XRCrossBandLine();
            this.xrCrossBandLine6 = new DevExpress.XtraReports.UI.XRCrossBandLine();
            this.lblAcNumber = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel5 = new DevExpress.XtraReports.UI.XRLabel();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable2});
            resources.ApplyResources(this.Detail, "Detail");
            this.Detail.Name = "Detail";
            this.Detail.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            // 
            // xrTable2
            // 
            this.xrTable2.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.xrTable2, "xrTable2");
            this.xrTable2.Name = "xrTable2";
            this.xrTable2.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow2});
            this.xrTable2.StylePriority.UseBorders = false;
            this.xrTable2.StylePriority.UseFont = false;
            this.xrTable2.StylePriority.UseTextAlignment = false;
            // 
            // xrTableRow2
            // 
            resources.ApplyResources(this.xrTableRow2, "xrTableRow2");
            this.xrTableRow2.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_balance,
            this.cell_credit,
            this.cell_debit,
            this.cell_JCode,
            this.cell_process,
            this.cell_notes,
            this.cell_insertDate,
            this.cell_index});
            this.xrTableRow2.Name = "xrTableRow2";
            this.xrTableRow2.StylePriority.UseBackColor = false;
            this.xrTableRow2.StylePriority.UseFont = false;
            // 
            // cell_balance
            // 
            this.cell_balance.Borders = DevExpress.XtraPrinting.BorderSide.Top;
            resources.ApplyResources(this.cell_balance, "cell_balance");
            this.cell_balance.Name = "cell_balance";
            this.cell_balance.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_balance.StylePriority.UseBorders = false;
            this.cell_balance.StylePriority.UsePadding = false;
            this.cell_balance.StylePriority.UseTextAlignment = false;
            // 
            // cell_credit
            // 
            this.cell_credit.Borders = DevExpress.XtraPrinting.BorderSide.Top;
            resources.ApplyResources(this.cell_credit, "cell_credit");
            this.cell_credit.Name = "cell_credit";
            this.cell_credit.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_credit.StylePriority.UseBorders = false;
            this.cell_credit.StylePriority.UsePadding = false;
            this.cell_credit.StylePriority.UseTextAlignment = false;
            // 
            // cell_debit
            // 
            this.cell_debit.Borders = DevExpress.XtraPrinting.BorderSide.Top;
            resources.ApplyResources(this.cell_debit, "cell_debit");
            this.cell_debit.Name = "cell_debit";
            this.cell_debit.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_debit.StylePriority.UseBorders = false;
            this.cell_debit.StylePriority.UsePadding = false;
            this.cell_debit.StylePriority.UseTextAlignment = false;
            // 
            // cell_JCode
            // 
            this.cell_JCode.Borders = DevExpress.XtraPrinting.BorderSide.Top;
            resources.ApplyResources(this.cell_JCode, "cell_JCode");
            this.cell_JCode.Name = "cell_JCode";
            this.cell_JCode.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_JCode.StylePriority.UseBorders = false;
            this.cell_JCode.StylePriority.UsePadding = false;
            this.cell_JCode.StylePriority.UseTextAlignment = false;
            // 
            // cell_process
            // 
            this.cell_process.Borders = DevExpress.XtraPrinting.BorderSide.Top;
            resources.ApplyResources(this.cell_process, "cell_process");
            this.cell_process.Name = "cell_process";
            this.cell_process.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_process.StylePriority.UseBorders = false;
            this.cell_process.StylePriority.UsePadding = false;
            this.cell_process.StylePriority.UseTextAlignment = false;
            // 
            // cell_notes
            // 
            this.cell_notes.Borders = DevExpress.XtraPrinting.BorderSide.Top;
            resources.ApplyResources(this.cell_notes, "cell_notes");
            this.cell_notes.Name = "cell_notes";
            this.cell_notes.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_notes.StylePriority.UseBorders = false;
            this.cell_notes.StylePriority.UsePadding = false;
            this.cell_notes.StylePriority.UseTextAlignment = false;
            // 
            // cell_insertDate
            // 
            this.cell_insertDate.Borders = DevExpress.XtraPrinting.BorderSide.Top;
            resources.ApplyResources(this.cell_insertDate, "cell_insertDate");
            this.cell_insertDate.Name = "cell_insertDate";
            this.cell_insertDate.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_insertDate.StylePriority.UseBorders = false;
            this.cell_insertDate.StylePriority.UsePadding = false;
            this.cell_insertDate.StylePriority.UseTextAlignment = false;
            // 
            // cell_index
            // 
            this.cell_index.Borders = DevExpress.XtraPrinting.BorderSide.Top;
            resources.ApplyResources(this.cell_index, "cell_index");
            this.cell_index.Name = "cell_index";
            this.cell_index.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_index.StylePriority.UseBorders = false;
            this.cell_index.StylePriority.UsePadding = false;
            this.cell_index.StylePriority.UseTextAlignment = false;
            this.cell_index.BeforePrint += new System.Drawing.Printing.PrintEventHandler(this.cell_index_BeforePrint);
            // 
            // TopMargin
            // 
            this.TopMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLabel5,
            this.xrLabel3,
            this.lbl_CurrencyName,
            this.xrLine1,
            this.lbl_User,
            this.xrLabel6,
            this.lbl_toDate,
            this.xrLabel4,
            this.lbl_accountName,
            this.xrLabel7,
            this.lbl_accountType,
            this.xrLabel2,
            this.lblReportName,
            this.lblCompName,
            this.lbl_fromDate,
            this.xrLabel8,
            this.picLogo,
            this.lblAcNumber});
            resources.ApplyResources(this.TopMargin, "TopMargin");
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            // 
            // xrLabel3
            // 
            resources.ApplyResources(this.xrLabel3, "xrLabel3");
            this.xrLabel3.Name = "xrLabel3";
            this.xrLabel3.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel3.StylePriority.UseFont = false;
            this.xrLabel3.StylePriority.UseTextAlignment = false;
            // 
            // lbl_CurrencyName
            // 
            resources.ApplyResources(this.lbl_CurrencyName, "lbl_CurrencyName");
            this.lbl_CurrencyName.Name = "lbl_CurrencyName";
            this.lbl_CurrencyName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_CurrencyName.StylePriority.UseFont = false;
            this.lbl_CurrencyName.StylePriority.UseTextAlignment = false;
            // 
            // xrLine1
            // 
            resources.ApplyResources(this.xrLine1, "xrLine1");
            this.xrLine1.BorderWidth = 0F;
            this.xrLine1.LineWidth = 0;
            this.xrLine1.Name = "xrLine1";
            this.xrLine1.StylePriority.UseBackColor = false;
            this.xrLine1.StylePriority.UseBorderColor = false;
            this.xrLine1.StylePriority.UseBorderWidth = false;
            this.xrLine1.StylePriority.UseForeColor = false;
            // 
            // lbl_User
            // 
            this.lbl_User.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lbl_User, "lbl_User");
            this.lbl_User.Name = "lbl_User";
            this.lbl_User.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_User.StylePriority.UseBorders = false;
            this.lbl_User.StylePriority.UseFont = false;
            this.lbl_User.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel6
            // 
            this.xrLabel6.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.xrLabel6, "xrLabel6");
            this.xrLabel6.Name = "xrLabel6";
            this.xrLabel6.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel6.StylePriority.UseBorders = false;
            this.xrLabel6.StylePriority.UseFont = false;
            this.xrLabel6.StylePriority.UseTextAlignment = false;
            // 
            // lbl_toDate
            // 
            resources.ApplyResources(this.lbl_toDate, "lbl_toDate");
            this.lbl_toDate.Multiline = true;
            this.lbl_toDate.Name = "lbl_toDate";
            this.lbl_toDate.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_toDate.StylePriority.UseFont = false;
            this.lbl_toDate.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel4
            // 
            resources.ApplyResources(this.xrLabel4, "xrLabel4");
            this.xrLabel4.Name = "xrLabel4";
            this.xrLabel4.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel4.StylePriority.UseFont = false;
            this.xrLabel4.StylePriority.UseTextAlignment = false;
            // 
            // lbl_accountName
            // 
            resources.ApplyResources(this.lbl_accountName, "lbl_accountName");
            this.lbl_accountName.Name = "lbl_accountName";
            this.lbl_accountName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_accountName.StylePriority.UseFont = false;
            this.lbl_accountName.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel7
            // 
            resources.ApplyResources(this.xrLabel7, "xrLabel7");
            this.xrLabel7.Name = "xrLabel7";
            this.xrLabel7.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel7.StylePriority.UseFont = false;
            this.xrLabel7.StylePriority.UseTextAlignment = false;
            // 
            // lbl_accountType
            // 
            resources.ApplyResources(this.lbl_accountType, "lbl_accountType");
            this.lbl_accountType.Name = "lbl_accountType";
            this.lbl_accountType.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_accountType.StylePriority.UseFont = false;
            this.lbl_accountType.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel2
            // 
            resources.ApplyResources(this.xrLabel2, "xrLabel2");
            this.xrLabel2.Name = "xrLabel2";
            this.xrLabel2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel2.StylePriority.UseFont = false;
            this.xrLabel2.StylePriority.UseTextAlignment = false;
            // 
            // lblReportName
            // 
            resources.ApplyResources(this.lblReportName, "lblReportName");
            this.lblReportName.Name = "lblReportName";
            this.lblReportName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblReportName.StylePriority.UseFont = false;
            this.lblReportName.StylePriority.UseTextAlignment = false;
            // 
            // lblCompName
            // 
            resources.ApplyResources(this.lblCompName, "lblCompName");
            this.lblCompName.Name = "lblCompName";
            this.lblCompName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblCompName.StylePriority.UseFont = false;
            this.lblCompName.StylePriority.UseTextAlignment = false;
            // 
            // lbl_fromDate
            // 
            resources.ApplyResources(this.lbl_fromDate, "lbl_fromDate");
            this.lbl_fromDate.Name = "lbl_fromDate";
            this.lbl_fromDate.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_fromDate.StylePriority.UseFont = false;
            this.lbl_fromDate.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel8
            // 
            resources.ApplyResources(this.xrLabel8, "xrLabel8");
            this.xrLabel8.Multiline = true;
            this.xrLabel8.Name = "xrLabel8";
            this.xrLabel8.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel8.StylePriority.UseFont = false;
            this.xrLabel8.StylePriority.UseTextAlignment = false;
            // 
            // picLogo
            // 
            resources.ApplyResources(this.picLogo, "picLogo");
            this.picLogo.Name = "picLogo";
            this.picLogo.Sizing = DevExpress.XtraPrinting.ImageSizeMode.StretchImage;
            // 
            // BottomMargin
            // 
            resources.ApplyResources(this.BottomMargin, "BottomMargin");
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            // 
            // PageHeader
            // 
            this.PageHeader.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable1});
            resources.ApplyResources(this.PageHeader, "PageHeader");
            this.PageHeader.Name = "PageHeader";
            // 
            // xrTable1
            // 
            this.xrTable1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.xrTable1, "xrTable1");
            this.xrTable1.Name = "xrTable1";
            this.xrTable1.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow1});
            this.xrTable1.StylePriority.UseBorders = false;
            this.xrTable1.StylePriority.UseFont = false;
            this.xrTable1.StylePriority.UseTextAlignment = false;
            // 
            // xrTableRow1
            // 
            resources.ApplyResources(this.xrTableRow1, "xrTableRow1");
            this.xrTableRow1.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell9,
            this.xrTableCell2,
            this.xrTableCell6,
            this.xrTableCell10,
            this.xrTableCell3,
            this.xrTableCell7,
            this.xrTableCell5,
            this.xrTableCell8});
            this.xrTableRow1.Name = "xrTableRow1";
            this.xrTableRow1.StylePriority.UseBackColor = false;
            this.xrTableRow1.StylePriority.UseFont = false;
            // 
            // xrTableCell9
            // 
            resources.ApplyResources(this.xrTableCell9, "xrTableCell9");
            this.xrTableCell9.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell9.Name = "xrTableCell9";
            this.xrTableCell9.StylePriority.UseBackColor = false;
            this.xrTableCell9.StylePriority.UseBorders = false;
            this.xrTableCell9.StylePriority.UseFont = false;
            this.xrTableCell9.StylePriority.UseTextAlignment = false;
            // 
            // xrTableCell2
            // 
            resources.ApplyResources(this.xrTableCell2, "xrTableCell2");
            this.xrTableCell2.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell2.Name = "xrTableCell2";
            this.xrTableCell2.StylePriority.UseBackColor = false;
            this.xrTableCell2.StylePriority.UseBorders = false;
            this.xrTableCell2.StylePriority.UseFont = false;
            this.xrTableCell2.StylePriority.UseTextAlignment = false;
            // 
            // xrTableCell6
            // 
            resources.ApplyResources(this.xrTableCell6, "xrTableCell6");
            this.xrTableCell6.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell6.Name = "xrTableCell6";
            this.xrTableCell6.StylePriority.UseBackColor = false;
            this.xrTableCell6.StylePriority.UseBorders = false;
            this.xrTableCell6.StylePriority.UseFont = false;
            this.xrTableCell6.StylePriority.UseTextAlignment = false;
            // 
            // xrTableCell10
            // 
            resources.ApplyResources(this.xrTableCell10, "xrTableCell10");
            this.xrTableCell10.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell10.Name = "xrTableCell10";
            this.xrTableCell10.StylePriority.UseBackColor = false;
            this.xrTableCell10.StylePriority.UseBorders = false;
            this.xrTableCell10.StylePriority.UseFont = false;
            this.xrTableCell10.StylePriority.UseTextAlignment = false;
            // 
            // xrTableCell3
            // 
            resources.ApplyResources(this.xrTableCell3, "xrTableCell3");
            this.xrTableCell3.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell3.Name = "xrTableCell3";
            this.xrTableCell3.StylePriority.UseBackColor = false;
            this.xrTableCell3.StylePriority.UseBorders = false;
            this.xrTableCell3.StylePriority.UseFont = false;
            this.xrTableCell3.StylePriority.UseTextAlignment = false;
            // 
            // xrTableCell7
            // 
            resources.ApplyResources(this.xrTableCell7, "xrTableCell7");
            this.xrTableCell7.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell7.Name = "xrTableCell7";
            this.xrTableCell7.StylePriority.UseBackColor = false;
            this.xrTableCell7.StylePriority.UseBorders = false;
            this.xrTableCell7.StylePriority.UseFont = false;
            this.xrTableCell7.StylePriority.UseTextAlignment = false;
            // 
            // xrTableCell5
            // 
            resources.ApplyResources(this.xrTableCell5, "xrTableCell5");
            this.xrTableCell5.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell5.Name = "xrTableCell5";
            this.xrTableCell5.StylePriority.UseBackColor = false;
            this.xrTableCell5.StylePriority.UseBorders = false;
            this.xrTableCell5.StylePriority.UseFont = false;
            this.xrTableCell5.StylePriority.UseTextAlignment = false;
            // 
            // xrTableCell8
            // 
            resources.ApplyResources(this.xrTableCell8, "xrTableCell8");
            this.xrTableCell8.Borders = DevExpress.XtraPrinting.BorderSide.Bottom;
            this.xrTableCell8.Name = "xrTableCell8";
            this.xrTableCell8.StylePriority.UseBackColor = false;
            this.xrTableCell8.StylePriority.UseBorders = false;
            this.xrTableCell8.StylePriority.UseFont = false;
            this.xrTableCell8.StylePriority.UseTextAlignment = false;
            // 
            // ReportFooter
            // 
            this.ReportFooter.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable4,
            this.xrLabel1,
            this.xrTable3,
            this.lbl_TotalCredit,
            this.lblTotal,
            this.lbl_TotalDebit,
            this.xrPageInfo1});
            resources.ApplyResources(this.ReportFooter, "ReportFooter");
            this.ReportFooter.Name = "ReportFooter";
            // 
            // xrTable4
            // 
            resources.ApplyResources(this.xrTable4, "xrTable4");
            this.xrTable4.Name = "xrTable4";
            this.xrTable4.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow5});
            // 
            // xrTableRow5
            // 
            this.xrTableRow5.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_JNumber,
            this.cell_CrncId,
            this.cell_CrncRate,
            this.cell_fDebit,
            this.cell_fCredit});
            resources.ApplyResources(this.xrTableRow5, "xrTableRow5");
            this.xrTableRow5.Name = "xrTableRow5";
            // 
            // cell_JNumber
            // 
            resources.ApplyResources(this.cell_JNumber, "cell_JNumber");
            this.cell_JNumber.Name = "cell_JNumber";
            // 
            // cell_CrncId
            // 
            resources.ApplyResources(this.cell_CrncId, "cell_CrncId");
            this.cell_CrncId.Name = "cell_CrncId";
            // 
            // cell_CrncRate
            // 
            resources.ApplyResources(this.cell_CrncRate, "cell_CrncRate");
            this.cell_CrncRate.Name = "cell_CrncRate";
            // 
            // cell_fDebit
            // 
            resources.ApplyResources(this.cell_fDebit, "cell_fDebit");
            this.cell_fDebit.Name = "cell_fDebit";
            // 
            // cell_fCredit
            // 
            resources.ApplyResources(this.cell_fCredit, "cell_fCredit");
            this.cell_fCredit.Name = "cell_fCredit";
            // 
            // xrLabel1
            // 
            resources.ApplyResources(this.xrLabel1, "xrLabel1");
            this.xrLabel1.Name = "xrLabel1";
            this.xrLabel1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel1.StylePriority.UseFont = false;
            this.xrLabel1.StylePriority.UseTextAlignment = false;
            // 
            // xrTable3
            // 
            this.xrTable3.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.xrTable3, "xrTable3");
            this.xrTable3.Name = "xrTable3";
            this.xrTable3.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow3,
            this.xrTableRow4});
            this.xrTable3.StylePriority.UseBorders = false;
            this.xrTable3.StylePriority.UseTextAlignment = false;
            // 
            // xrTableRow3
            // 
            this.xrTableRow3.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_header_P5,
            this.cell_header_P4,
            this.cell_header_P3,
            this.cell_header_P2,
            this.cell_header_P1,
            this.cell_header_P0});
            resources.ApplyResources(this.xrTableRow3, "xrTableRow3");
            this.xrTableRow3.Name = "xrTableRow3";
            // 
            // cell_header_P5
            // 
            resources.ApplyResources(this.cell_header_P5, "cell_header_P5");
            this.cell_header_P5.Name = "cell_header_P5";
            this.cell_header_P5.StylePriority.UseBackColor = false;
            this.cell_header_P5.StylePriority.UseFont = false;
            this.cell_header_P5.StylePriority.UseTextAlignment = false;
            // 
            // cell_header_P4
            // 
            resources.ApplyResources(this.cell_header_P4, "cell_header_P4");
            this.cell_header_P4.Name = "cell_header_P4";
            this.cell_header_P4.StylePriority.UseBackColor = false;
            this.cell_header_P4.StylePriority.UseFont = false;
            this.cell_header_P4.StylePriority.UseTextAlignment = false;
            // 
            // cell_header_P3
            // 
            resources.ApplyResources(this.cell_header_P3, "cell_header_P3");
            this.cell_header_P3.Name = "cell_header_P3";
            this.cell_header_P3.StylePriority.UseBackColor = false;
            this.cell_header_P3.StylePriority.UseFont = false;
            this.cell_header_P3.StylePriority.UseTextAlignment = false;
            // 
            // cell_header_P2
            // 
            resources.ApplyResources(this.cell_header_P2, "cell_header_P2");
            this.cell_header_P2.Name = "cell_header_P2";
            this.cell_header_P2.StylePriority.UseBackColor = false;
            this.cell_header_P2.StylePriority.UseFont = false;
            this.cell_header_P2.StylePriority.UseTextAlignment = false;
            // 
            // cell_header_P1
            // 
            resources.ApplyResources(this.cell_header_P1, "cell_header_P1");
            this.cell_header_P1.Name = "cell_header_P1";
            this.cell_header_P1.StylePriority.UseBackColor = false;
            this.cell_header_P1.StylePriority.UseFont = false;
            this.cell_header_P1.StylePriority.UseTextAlignment = false;
            // 
            // cell_header_P0
            // 
            resources.ApplyResources(this.cell_header_P0, "cell_header_P0");
            this.cell_header_P0.Name = "cell_header_P0";
            this.cell_header_P0.StylePriority.UseBackColor = false;
            this.cell_header_P0.StylePriority.UseFont = false;
            this.cell_header_P0.StylePriority.UseTextAlignment = false;
            // 
            // xrTableRow4
            // 
            this.xrTableRow4.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_P5,
            this.cell_P4,
            this.cell_P3,
            this.cell_P2,
            this.cell_P1,
            this.cell_P0});
            resources.ApplyResources(this.xrTableRow4, "xrTableRow4");
            this.xrTableRow4.Name = "xrTableRow4";
            // 
            // cell_P5
            // 
            resources.ApplyResources(this.cell_P5, "cell_P5");
            this.cell_P5.Name = "cell_P5";
            this.cell_P5.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_P5.StylePriority.UseFont = false;
            this.cell_P5.StylePriority.UsePadding = false;
            this.cell_P5.StylePriority.UseTextAlignment = false;
            // 
            // cell_P4
            // 
            resources.ApplyResources(this.cell_P4, "cell_P4");
            this.cell_P4.Name = "cell_P4";
            this.cell_P4.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_P4.StylePriority.UseFont = false;
            this.cell_P4.StylePriority.UsePadding = false;
            this.cell_P4.StylePriority.UseTextAlignment = false;
            // 
            // cell_P3
            // 
            resources.ApplyResources(this.cell_P3, "cell_P3");
            this.cell_P3.Name = "cell_P3";
            this.cell_P3.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_P3.StylePriority.UseFont = false;
            this.cell_P3.StylePriority.UsePadding = false;
            this.cell_P3.StylePriority.UseTextAlignment = false;
            // 
            // cell_P2
            // 
            resources.ApplyResources(this.cell_P2, "cell_P2");
            this.cell_P2.Name = "cell_P2";
            this.cell_P2.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_P2.StylePriority.UseFont = false;
            this.cell_P2.StylePriority.UsePadding = false;
            this.cell_P2.StylePriority.UseTextAlignment = false;
            // 
            // cell_P1
            // 
            resources.ApplyResources(this.cell_P1, "cell_P1");
            this.cell_P1.Name = "cell_P1";
            this.cell_P1.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_P1.StylePriority.UseFont = false;
            this.cell_P1.StylePriority.UsePadding = false;
            this.cell_P1.StylePriority.UseTextAlignment = false;
            // 
            // cell_P0
            // 
            resources.ApplyResources(this.cell_P0, "cell_P0");
            this.cell_P0.Name = "cell_P0";
            this.cell_P0.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_P0.StylePriority.UseFont = false;
            this.cell_P0.StylePriority.UsePadding = false;
            this.cell_P0.StylePriority.UseTextAlignment = false;
            // 
            // lbl_TotalCredit
            // 
            this.lbl_TotalCredit.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lbl_TotalCredit, "lbl_TotalCredit");
            this.lbl_TotalCredit.Name = "lbl_TotalCredit";
            this.lbl_TotalCredit.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_TotalCredit.StylePriority.UseBorders = false;
            this.lbl_TotalCredit.StylePriority.UseFont = false;
            this.lbl_TotalCredit.StylePriority.UsePadding = false;
            this.lbl_TotalCredit.StylePriority.UseTextAlignment = false;
            // 
            // lblTotal
            // 
            resources.ApplyResources(this.lblTotal, "lblTotal");
            this.lblTotal.Name = "lblTotal";
            this.lblTotal.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblTotal.StylePriority.UseBackColor = false;
            this.lblTotal.StylePriority.UseFont = false;
            this.lblTotal.StylePriority.UseTextAlignment = false;
            // 
            // lbl_TotalDebit
            // 
            this.lbl_TotalDebit.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lbl_TotalDebit, "lbl_TotalDebit");
            this.lbl_TotalDebit.Name = "lbl_TotalDebit";
            this.lbl_TotalDebit.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_TotalDebit.StylePriority.UseBorders = false;
            this.lbl_TotalDebit.StylePriority.UseFont = false;
            this.lbl_TotalDebit.StylePriority.UsePadding = false;
            this.lbl_TotalDebit.StylePriority.UseTextAlignment = false;
            // 
            // xrPageInfo1
            // 
            resources.ApplyResources(this.xrPageInfo1, "xrPageInfo1");
            this.xrPageInfo1.Name = "xrPageInfo1";
            this.xrPageInfo1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrPageInfo1.StylePriority.UseTextAlignment = false;
            // 
            // xrCrossBandBox1
            // 
            this.xrCrossBandBox1.BorderWidth = 1F;
            resources.ApplyResources(this.xrCrossBandBox1, "xrCrossBandBox1");
            this.xrCrossBandBox1.EndBand = this.ReportFooter;
            this.xrCrossBandBox1.Name = "xrCrossBandBox1";
            this.xrCrossBandBox1.StartBand = this.PageHeader;
            this.xrCrossBandBox1.WidthF = 725.9587F;
            // 
            // xrCrossBandBox2
            // 
            this.xrCrossBandBox2.BorderWidth = 1F;
            resources.ApplyResources(this.xrCrossBandBox2, "xrCrossBandBox2");
            this.xrCrossBandBox2.EndBand = this.ReportFooter;
            this.xrCrossBandBox2.Name = "xrCrossBandBox2";
            this.xrCrossBandBox2.StartBand = this.PageHeader;
            this.xrCrossBandBox2.WidthF = 141.9312F;
            // 
            // xrCrossBandLine1
            // 
            resources.ApplyResources(this.xrCrossBandLine1, "xrCrossBandLine1");
            this.xrCrossBandLine1.EndBand = this.ReportFooter;
            this.xrCrossBandLine1.Name = "xrCrossBandLine1";
            this.xrCrossBandLine1.StartBand = this.PageHeader;
            this.xrCrossBandLine1.WidthF = 1.000015F;
            // 
            // xrCrossBandLine2
            // 
            resources.ApplyResources(this.xrCrossBandLine2, "xrCrossBandLine2");
            this.xrCrossBandLine2.EndBand = this.ReportFooter;
            this.xrCrossBandLine2.Name = "xrCrossBandLine2";
            this.xrCrossBandLine2.StartBand = this.PageHeader;
            this.xrCrossBandLine2.WidthF = 1F;
            // 
            // xrCrossBandLine3
            // 
            resources.ApplyResources(this.xrCrossBandLine3, "xrCrossBandLine3");
            this.xrCrossBandLine3.EndBand = this.ReportFooter;
            this.xrCrossBandLine3.Name = "xrCrossBandLine3";
            this.xrCrossBandLine3.StartBand = this.PageHeader;
            this.xrCrossBandLine3.WidthF = 1F;
            // 
            // xrCrossBandLine5
            // 
            resources.ApplyResources(this.xrCrossBandLine5, "xrCrossBandLine5");
            this.xrCrossBandLine5.EndBand = this.ReportFooter;
            this.xrCrossBandLine5.Name = "xrCrossBandLine5";
            this.xrCrossBandLine5.StartBand = this.PageHeader;
            this.xrCrossBandLine5.WidthF = 1F;
            // 
            // xrCrossBandLine6
            // 
            resources.ApplyResources(this.xrCrossBandLine6, "xrCrossBandLine6");
            this.xrCrossBandLine6.EndBand = this.ReportFooter;
            this.xrCrossBandLine6.Name = "xrCrossBandLine6";
            this.xrCrossBandLine6.StartBand = this.PageHeader;
            this.xrCrossBandLine6.WidthF = 1F;
            // 
            // lblAcNumber
            // 
            resources.ApplyResources(this.lblAcNumber, "lblAcNumber");
            this.lblAcNumber.Name = "lblAcNumber";
            this.lblAcNumber.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblAcNumber.StylePriority.UseFont = false;
            this.lblAcNumber.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel5
            // 
            resources.ApplyResources(this.xrLabel5, "xrLabel5");
            this.xrLabel5.Name = "xrLabel5";
            this.xrLabel5.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel5.StylePriority.UseFont = false;
            this.xrLabel5.StylePriority.UseTextAlignment = false;
            // 
            // rpt_ACC_Statment
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail,
            this.TopMargin,
            this.BottomMargin,
            this.PageHeader,
            this.ReportFooter});
            this.CrossBandControls.AddRange(new DevExpress.XtraReports.UI.XRCrossBandControl[] {
            this.xrCrossBandLine6,
            this.xrCrossBandLine5,
            this.xrCrossBandLine3,
            this.xrCrossBandLine2,
            this.xrCrossBandLine1,
            this.xrCrossBandBox2,
            this.xrCrossBandBox1});
            resources.ApplyResources(this, "$this");
            this.ExportOptions.Csv.EncodingType = ((DevExpress.XtraPrinting.EncodingType)(resources.GetObject("rpt_ACC_Statment.ExportOptions.Csv.EncodingType")));
            this.ExportOptions.Csv.Separator = resources.GetString("rpt_ACC_Statment.ExportOptions.Csv.Separator");
            this.Version = "15.1";
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.PageHeaderBand PageHeader;
        private DevExpress.XtraReports.UI.ReportFooterBand ReportFooter;
        private DevExpress.XtraReports.UI.XRTable xrTable2;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow2;
        private DevExpress.XtraReports.UI.XRTableCell cell_balance;
        private DevExpress.XtraReports.UI.XRTableCell cell_credit;
        private DevExpress.XtraReports.UI.XRTableCell cell_debit;
        private DevExpress.XtraReports.UI.XRTableCell cell_JCode;
        private DevExpress.XtraReports.UI.XRTableCell cell_process;
        private DevExpress.XtraReports.UI.XRTableCell cell_notes;
        private DevExpress.XtraReports.UI.XRTableCell cell_insertDate;
        private DevExpress.XtraReports.UI.XRTableCell cell_index;
        private DevExpress.XtraReports.UI.XRTable xrTable4;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow5;
        private DevExpress.XtraReports.UI.XRTableCell cell_JNumber;
        private DevExpress.XtraReports.UI.XRTableCell cell_CrncId;
        private DevExpress.XtraReports.UI.XRTableCell cell_CrncRate;
        private DevExpress.XtraReports.UI.XRTableCell cell_fDebit;
        private DevExpress.XtraReports.UI.XRTableCell cell_fCredit;
        private DevExpress.XtraReports.UI.XRLabel xrLabel1;
        private DevExpress.XtraReports.UI.XRTable xrTable3;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow3;
        private DevExpress.XtraReports.UI.XRTableCell cell_header_P5;
        private DevExpress.XtraReports.UI.XRTableCell cell_header_P4;
        private DevExpress.XtraReports.UI.XRTableCell cell_header_P3;
        private DevExpress.XtraReports.UI.XRTableCell cell_header_P2;
        private DevExpress.XtraReports.UI.XRTableCell cell_header_P1;
        private DevExpress.XtraReports.UI.XRTableCell cell_header_P0;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow4;
        private DevExpress.XtraReports.UI.XRTableCell cell_P5;
        private DevExpress.XtraReports.UI.XRTableCell cell_P4;
        private DevExpress.XtraReports.UI.XRTableCell cell_P3;
        private DevExpress.XtraReports.UI.XRTableCell cell_P2;
        private DevExpress.XtraReports.UI.XRTableCell cell_P1;
        private DevExpress.XtraReports.UI.XRTableCell cell_P0;
        private DevExpress.XtraReports.UI.XRLabel lbl_TotalCredit;
        private DevExpress.XtraReports.UI.XRLabel lblTotal;
        private DevExpress.XtraReports.UI.XRLabel lbl_TotalDebit;
        private DevExpress.XtraReports.UI.XRPageInfo xrPageInfo1;
        private DevExpress.XtraReports.UI.XRLabel xrLabel3;
        private DevExpress.XtraReports.UI.XRLabel lbl_CurrencyName;
        private DevExpress.XtraReports.UI.XRLine xrLine1;
        private DevExpress.XtraReports.UI.XRLabel lbl_User;
        private DevExpress.XtraReports.UI.XRLabel xrLabel6;
        private DevExpress.XtraReports.UI.XRLabel lbl_toDate;
        private DevExpress.XtraReports.UI.XRLabel xrLabel4;
        private DevExpress.XtraReports.UI.XRLabel lbl_accountName;
        private DevExpress.XtraReports.UI.XRLabel lbl_fromDate;
        private DevExpress.XtraReports.UI.XRLabel xrLabel7;
        private DevExpress.XtraReports.UI.XRLabel xrLabel8;
        private DevExpress.XtraReports.UI.XRLabel lbl_accountType;
        private DevExpress.XtraReports.UI.XRLabel xrLabel2;
        private DevExpress.XtraReports.UI.XRLabel lblReportName;
        private DevExpress.XtraReports.UI.XRPictureBox picLogo;
        private DevExpress.XtraReports.UI.XRLabel lblCompName;
        private DevExpress.XtraReports.UI.XRTable xrTable1;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow1;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell9;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell2;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell6;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell10;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell3;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell7;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell5;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell8;
        private DevExpress.XtraReports.UI.XRCrossBandBox xrCrossBandBox1;
        private DevExpress.XtraReports.UI.XRCrossBandBox xrCrossBandBox2;
        private DevExpress.XtraReports.UI.XRCrossBandLine xrCrossBandLine1;
        private DevExpress.XtraReports.UI.XRCrossBandLine xrCrossBandLine2;
        private DevExpress.XtraReports.UI.XRCrossBandLine xrCrossBandLine3;
        private DevExpress.XtraReports.UI.XRCrossBandLine xrCrossBandLine5;
        private DevExpress.XtraReports.UI.XRCrossBandLine xrCrossBandLine6;
        private DevExpress.XtraReports.UI.XRLabel lblAcNumber;
        private DevExpress.XtraReports.UI.XRLabel xrLabel5;
    }
}
