﻿namespace Pharmacy.Forms
{
    partial class uc_LinkAccount
    {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(uc_LinkAccount));
            this.grp_Account = new System.Windows.Forms.GroupBox();
            this.rdo_LinkedAcc = new DevExpress.XtraEditors.CheckEdit();
            this.rdo_SeparateAcc = new DevExpress.XtraEditors.CheckEdit();
            this.lkp_LinkedAcc = new DevExpress.XtraEditors.GridLookUpEdit();
            this.gridLookUpEdit1View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colAccId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colAccNumber = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colAccName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.grp_Account.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.rdo_LinkedAcc.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rdo_SeparateAcc.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_LinkedAcc.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridLookUpEdit1View)).BeginInit();
            this.SuspendLayout();
            // 
            // grp_Account
            // 
            this.grp_Account.Controls.Add(this.rdo_LinkedAcc);
            this.grp_Account.Controls.Add(this.rdo_SeparateAcc);
            this.grp_Account.Controls.Add(this.lkp_LinkedAcc);
            resources.ApplyResources(this.grp_Account, "grp_Account");
            this.grp_Account.Name = "grp_Account";
            this.grp_Account.TabStop = false;
            // 
            // rdo_LinkedAcc
            // 
            resources.ApplyResources(this.rdo_LinkedAcc, "rdo_LinkedAcc");
            this.rdo_LinkedAcc.Name = "rdo_LinkedAcc";
            this.rdo_LinkedAcc.Properties.Appearance.Options.UseTextOptions = true;
            this.rdo_LinkedAcc.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.rdo_LinkedAcc.Properties.AppearanceDisabled.BackColor = System.Drawing.Color.White;
            this.rdo_LinkedAcc.Properties.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.rdo_LinkedAcc.Properties.AppearanceDisabled.Options.UseBackColor = true;
            this.rdo_LinkedAcc.Properties.AppearanceDisabled.Options.UseForeColor = true;
            this.rdo_LinkedAcc.Properties.Caption = resources.GetString("rdo_LinkedAcc.Properties.Caption");
            this.rdo_LinkedAcc.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Radio;
            this.rdo_LinkedAcc.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("rdo_LinkedAcc.Properties.GlyphAlignment")));
            this.rdo_LinkedAcc.Properties.RadioGroupIndex = 10;
            this.rdo_LinkedAcc.TabStop = false;
            this.rdo_LinkedAcc.Modified += new System.EventHandler(this.rdo_SeparateAcc_Modified);
            this.rdo_LinkedAcc.CheckedChanged += new System.EventHandler(this.rdo_SeparateAcc_CheckedChanged);
            // 
            // rdo_SeparateAcc
            // 
            resources.ApplyResources(this.rdo_SeparateAcc, "rdo_SeparateAcc");
            this.rdo_SeparateAcc.Name = "rdo_SeparateAcc";
            this.rdo_SeparateAcc.Properties.Appearance.Options.UseTextOptions = true;
            this.rdo_SeparateAcc.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.rdo_SeparateAcc.Properties.AppearanceDisabled.BackColor = System.Drawing.Color.White;
            this.rdo_SeparateAcc.Properties.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.rdo_SeparateAcc.Properties.AppearanceDisabled.Options.UseBackColor = true;
            this.rdo_SeparateAcc.Properties.AppearanceDisabled.Options.UseForeColor = true;
            this.rdo_SeparateAcc.Properties.Caption = resources.GetString("rdo_SeparateAcc.Properties.Caption");
            this.rdo_SeparateAcc.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Radio;
            this.rdo_SeparateAcc.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("rdo_SeparateAcc.Properties.GlyphAlignment")));
            this.rdo_SeparateAcc.Properties.RadioGroupIndex = 10;
            this.rdo_SeparateAcc.TabStop = false;
            this.rdo_SeparateAcc.Modified += new System.EventHandler(this.rdo_SeparateAcc_Modified);
            this.rdo_SeparateAcc.CheckedChanged += new System.EventHandler(this.rdo_SeparateAcc_CheckedChanged);
            // 
            // lkp_LinkedAcc
            // 
            resources.ApplyResources(this.lkp_LinkedAcc, "lkp_LinkedAcc");
            this.lkp_LinkedAcc.EnterMoveNextControl = true;
            this.lkp_LinkedAcc.Name = "lkp_LinkedAcc";
            this.lkp_LinkedAcc.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkp_LinkedAcc.Properties.Appearance.Options.UseTextOptions = true;
            this.lkp_LinkedAcc.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkp_LinkedAcc.Properties.AppearanceDisabled.BackColor = System.Drawing.Color.White;
            this.lkp_LinkedAcc.Properties.AppearanceDisabled.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lkp_LinkedAcc.Properties.AppearanceDisabled.Options.UseBackColor = true;
            this.lkp_LinkedAcc.Properties.AppearanceDisabled.Options.UseForeColor = true;
            this.lkp_LinkedAcc.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkp_LinkedAcc.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkp_LinkedAcc.Properties.Buttons"))))});
            this.lkp_LinkedAcc.Properties.ImmediatePopup = true;
            this.lkp_LinkedAcc.Properties.NullText = resources.GetString("lkp_LinkedAcc.Properties.NullText");
            this.lkp_LinkedAcc.Properties.View = this.gridLookUpEdit1View;
            this.lkp_LinkedAcc.Modified += new System.EventHandler(this.rdo_SeparateAcc_Modified);
            this.lkp_LinkedAcc.EditValueChanging += new DevExpress.XtraEditors.Controls.ChangingEventHandler(this.lkp_LinkedAcc_EditValueChanging);
            // 
            // gridLookUpEdit1View
            // 
            this.gridLookUpEdit1View.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridLookUpEdit1View.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridLookUpEdit1View.Appearance.Row.Options.UseTextOptions = true;
            this.gridLookUpEdit1View.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridLookUpEdit1View.ColumnPanelRowHeight = 30;
            this.gridLookUpEdit1View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colAccId,
            this.colAccNumber,
            this.colAccName});
            this.gridLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridLookUpEdit1View.Name = "gridLookUpEdit1View";
            this.gridLookUpEdit1View.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridLookUpEdit1View.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridLookUpEdit1View.OptionsBehavior.Editable = false;
            this.gridLookUpEdit1View.OptionsBehavior.FocusLeaveOnTab = true;
            this.gridLookUpEdit1View.OptionsBehavior.ReadOnly = true;
            this.gridLookUpEdit1View.OptionsCustomization.AllowGroup = false;
            this.gridLookUpEdit1View.OptionsCustomization.AllowQuickHideColumns = false;
            this.gridLookUpEdit1View.OptionsMenu.EnableColumnMenu = false;
            this.gridLookUpEdit1View.OptionsMenu.EnableFooterMenu = false;
            this.gridLookUpEdit1View.OptionsMenu.EnableGroupPanelMenu = false;
            this.gridLookUpEdit1View.OptionsMenu.ShowDateTimeGroupIntervalItems = false;
            this.gridLookUpEdit1View.OptionsMenu.ShowGroupSortSummaryItems = false;
            this.gridLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridLookUpEdit1View.OptionsView.ShowAutoFilterRow = true;
            this.gridLookUpEdit1View.OptionsView.ShowGroupPanel = false;
            this.gridLookUpEdit1View.OptionsView.ShowIndicator = false;
            // 
            // colAccId
            // 
            resources.ApplyResources(this.colAccId, "colAccId");
            this.colAccId.FieldName = "AccId";
            this.colAccId.Name = "colAccId";
            this.colAccId.OptionsColumn.ShowInCustomizationForm = false;
            // 
            // colAccNumber
            // 
            resources.ApplyResources(this.colAccNumber, "colAccNumber");
            this.colAccNumber.FieldName = "AccNumber";
            this.colAccNumber.Name = "colAccNumber";
            // 
            // colAccName
            // 
            resources.ApplyResources(this.colAccName, "colAccName");
            this.colAccName.FieldName = "AccName";
            this.colAccName.Name = "colAccName";
            // 
            // uc_LinkAccount
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.grp_Account);
            this.Name = "uc_LinkAccount";
            this.Load += new System.EventHandler(this.uc_LinkAccount_Load);
            this.grp_Account.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.rdo_LinkedAcc.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rdo_SeparateAcc.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkp_LinkedAcc.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridLookUpEdit1View)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        public DevExpress.XtraEditors.CheckEdit rdo_LinkedAcc;
        public DevExpress.XtraEditors.CheckEdit rdo_SeparateAcc;
        public System.Windows.Forms.GroupBox grp_Account;
        private DevExpress.XtraGrid.Views.Grid.GridView gridLookUpEdit1View;
        private DevExpress.XtraGrid.Columns.GridColumn colAccId;
        private DevExpress.XtraGrid.Columns.GridColumn colAccNumber;
        private DevExpress.XtraGrid.Columns.GridColumn colAccName;
        public DevExpress.XtraEditors.GridLookUpEdit lkp_LinkedAcc;
    }
}
