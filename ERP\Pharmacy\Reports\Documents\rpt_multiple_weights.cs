using System;
using System.Data;
using System.Data.Linq;
using System.Linq;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using DAL;

namespace Reports
{
    public partial class rpt_multiple_weights : DevExpress.XtraReports.UI.XtraReport
    {
        public rpt_multiple_weights(DataTable dt_Weights)
        {
            InitializeComponent();

            this.DataSource = dt_Weights;
            lbl_Item.DataBindings.Add("Text", this.DataSource, "item");
            lbl_PiecesCount.DataBindings.Add("Text", this.DataSource, "Count");
            lbl_Weight.DataBindings.Add("Text", this.DataSource, "Weight");
        }

    }
}
