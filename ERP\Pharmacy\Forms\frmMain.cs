﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using Pharmacy.Forms;
using Pharmacy.HR;
using DevExpress.XtraEditors;
using DAL;
using DAL.Res;
using System.IO;
using System.Globalization;
using Reports;
using System.Threading;
using TableDependency.EventArgs;
using TableDependency.SqlClient;
using System.Security.Cryptography.X509Certificates;
using System.Net.Http;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Models_1.ViewModels.ResponseVM.GetDocumentVM;
using static Pharmacy.Forms.ErpHelper;
using Pharmacy.Properties;
using Tulpep.NotificationWindow;

namespace Pharmacy.Forms
{
    public partial class frmMain : DevExpress.XtraEditors.XtraForm
    {
        Icon icon = Properties.Resources.ggIco;
        static string openedJobForm = "";
        static string openedJobListForm = "";

        public frmMain()
        {
            RTL.EnCulture(Shared.IsEnglish);

            InitializeComponent();
            DevExpress.UserSkins.BonusSkins.Register();

            Shared.st_comp = GetAvialableModules();
            //load privilege

            EnableDisableModules();
            LoadUserPriv();

           // Properties.Settings.Default.RecievedDocumentsLastSyncDate = null;
        }

        #region Notificatios

        public static void StartDependency()
        {
            try
            {
                var con = Properties.Settings.Default.ERPConnectionString;// System.Configuration.ConfigurationManager.ConnectionStrings["Pharmacy.Properties.Settings.ERPConnectionString"].ConnectionString;
                var jobOrderDependency = new SqlTableDependency<JO_JobOrder>(con);
                var purchaseOrderDependency = new SqlTableDependency<PR_PurchaseOrder>(con);
                var salesOrderDependency = new SqlTableDependency<SL_SalesOrder>(con);

                if (Shared.LstUserPrvlg == null ||
                      Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.frm_JO_JobOrder).FirstOrDefault() != null)
                {
                    jobOrderDependency.OnChanged += Changed;
                    jobOrderDependency.Start();

                }
                if (Shared.LstUserPrvlg == null ||
                      Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.PR_PurchaseOrder).FirstOrDefault() != null)
                {
                    purchaseOrderDependency.OnChanged += Changed;
                    purchaseOrderDependency.Start();

                }
                if (Shared.LstUserPrvlg == null ||
                      Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.SL_SalesOrder).FirstOrDefault() != null)
                {
                    salesOrderDependency.OnChanged += Changed;
                    salesOrderDependency.Start();

                }
            }
            catch (Exception ex)
            {

            }
        }


        public static void Changed(object sender, RecordChangedEventArgs<JO_JobOrder> e)
        {

            //var changedEntity = e.Entity;
            var changeType = e.ChangeType.ToString();
            if (e.Entity.UserId != Shared.UserId && changeType == "Insert")
            //if (changeType == "Insert")
            {
                string body = "لديك أمر عمل جديد";
                SendMessage(body);
            }

            //StartDependency();

        }
        public static void Changed(object sender, RecordChangedEventArgs<SL_SalesOrder> e)
        {
            //var changedEntity = e.Entity;
            var changeType = e.ChangeType.ToString();
            if (e.Entity.UserId != Shared.UserId && changeType == "Insert")
            {
                string body = "لديك أمر بيع جديد";
                SendMessage(body);
            }
            //StartDependency();

        }
        public static void Changed(object sender, RecordChangedEventArgs<PR_PurchaseOrder> e)
        {
            //var changedEntity = e.Entity;
            var changeType = e.ChangeType.ToString();
            if (e.Entity.UserId != Shared.UserId && changeType == "Insert")
            {
                string body = "لديك أمر شراء جديد";
                SendMessage(body);
            }
            //StartDependency();

        }

        private static void SendMessage(string message)
        {


            NotifyIcon notifyIcon = new NotifyIcon();

            notifyIcon.Icon = SystemIcons.Information; //new Icon("appicon.ico");  
            notifyIcon.Visible = true;
            notifyIcon.BalloonTipText = message;
            notifyIcon.BalloonTipTitle = "رسالة";
            notifyIcon.ShowBalloonTip(1000);

        }

        #endregion

        private void frmMain_Load(object sender, EventArgs e)
        {

            //StartDependency();
            ERPDataContext DB = new ERPDataContext();

            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            if (Shared.ActiveNavBarGroup_Loaded != null)
                navBarControl1.ActiveGroup = navBarControl1.Groups[Shared.ActiveNavBarGroup_Loaded];
            else
                navBarControl1.ActiveGroup = navBarControl1.Groups[0];

            if (!string.IsNullOrEmpty(Shared.StyleName))
                ChangeStyle(Shared.StyleName);

            //load accounts settings
            //load user settings
            int revAcc = 0;
            int expAcc = 0;
            HelperAcc.SettingsAccounts = HelperAcc.Get_Accounts(out revAcc, out expAcc);
            HelperAcc.ExpensesAcc = expAcc;
            HelperAcc.RevenuesAcc = revAcc;

            //load user settings
            Shared.st_Store = MyHelper.GetStoreSettings(out Shared.lstCurrency, out Shared.lstIncomeTaxDiscount, out Shared.lstIncomeTaxLevel);

            if (Shared.st_Store.UseHeightDimension == true ||
                Shared.st_Store.UseWidthDimension == true ||
                Shared.st_Store.UseLengthDimension == true)
                Shared.DimensionsAvailable = true;

            Get_Dongle_SN();


            LoadImage();

            //get reports designs files' path
            Shared.ReportsPath = ErpUtils.GetReportsPath(Shared.st_Store);
            Shared.ReceiptPrinterName = Properties.Settings.Default.ReceiptPrinterName.Trim();
            Shared.AttachmentsPath = ErpUtils.GetAttachmentsPath(Shared.st_Store);
            barUserName.Caption = Shared.UserName;

            //get compName name
            this.Text = Shared.CompName;
            this.Text += " " + Shared.TrialVersion;

            //   if(Shared.st_Store.StoreSales==false)



            Show(picLogo.Image != null,Shared.ShowMainChart_);
            UpdateRejectedEInvoiceStateAsync();
            UpdateRecievedDocumentsStateAsync();

        }


        private void UpdateRecievedDocumentsStateAsync()
        {
            var minutesInterval = Settings.Default.RecievedDocumentsMinutesInterval;
            var timer = new System.Windows.Forms.Timer
            {
                Interval = minutesInterval * 60000  //Imnterval in millliseconds
            };
            timer.Enabled = true;
            timer.Tick += new System.EventHandler(OnUpdateDocumentTimerEvent);

        }

        private void UpdateRejectedEInvoiceStateAsync()
        {

            var timer = new System.Windows.Forms.Timer
            {
                Interval =  180000  //30 Minutes
            };
            timer.Enabled = true;
            timer.Tick += new System.EventHandler(OnTimerEvent);
           
        }


        private async void OnUpdateDocumentTimerEvent(object sender, EventArgs e)
        {
            await GetRecievedDocuments();
        }

        private async void OnTimerEvent(object sender, EventArgs e)
        {         
              await GetRejectedInvoicesByReviewer();
        }

        private async Task GetRejectedInvoicesByReviewer()
        {
            var dataContext = new ERPDataContext();
            var httpClient = new HttpClient();
            UriBuilder uriBuilder = new UriBuilder(Properties.Settings.Default.BackEndPoint);
            uriBuilder.Port = Properties.Settings.Default.BackEndPort;
            httpClient.BaseAddress = uriBuilder.Uri;
            httpClient.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));

            try
            {

                var invoicesInReviewPeriod = (from i in dataContext.SL_Invoices
                                             
                                              where i.EstatusCode == (int)InvoiceStatus.Valid
                                              where i.InvoiceDate >= DateTime.Today && i.InvoiceDate <= DateTime.Today.AddDays(2)
                                           
                                              select i
                                              ).ToList();

                for (int i = 0; i < invoicesInReviewPeriod.Count; i++)
                {
                    var invoice = invoicesInReviewPeriod[i];                  
                    var response = await httpClient.GetAsync($"api/Einvoice/GetDocument?uuid={invoice.uuid}");

                    if (response.IsSuccessStatusCode)
                    {
                        var jsonResult = await response.Content.ReadAsStringAsync();
                        var deserializedInvoice = JsonConvert.DeserializeObject<GetDocumentResponse>(jsonResult);

                        if (deserializedInvoice.status == "rejected")
                        {
                            invoice.Estatus = "rejected";
                            invoice.EstatusCode = (int)InvoiceStatus.Rejected;
                            dataContext.SubmitChanges();

                            XtraMessageBox.Show(
                                this,
                                Shared.IsEnglish ? "There are rejected invoices" : "تم رفض بعض الفواتير المتزامنة",
                                "",
                                MessageBoxButtons.OK,
                                MessageBoxIcon.Information
                                );

                        }

                    }
                }

            }

            catch (Exception ex)
            {
                throw;
            }
            finally
            {
                httpClient.Dispose();
                dataContext.Dispose();
            }
         
        }


        string lastRecievedDocumentSyncDate;
        private async Task GetRecievedDocuments()
        {
            var dataContext = new ERPDataContext();
            var httpClient = new HttpClient();
            UriBuilder uriBuilder = new UriBuilder(Properties.Settings.Default.BackEndPoint);
            uriBuilder.Port = Properties.Settings.Default.BackEndPort;
            httpClient.BaseAddress = uriBuilder.Uri;
            httpClient.DefaultRequestHeaders.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("application/json"));

            try
            {

                var documentsInDb = dataContext.E_RecievedDocuments;
                if (!documentsInDb.Any())
                {
                  
                    var dateFrom = DateTime.Today.AddDays(-30).ToString("M/d/yyyy");
                    var dateTo = DateTime.Today.ToString("M/d/yyyy");

                    var documents = await GetRecievedDocumentsAsync(dateFrom, dateTo);
                    PopulateRecievedDocumentsTable(documents, out bool isNewAdded);

                    lastRecievedDocumentSyncDate = DateTime.Now.ToString("yyyy-MM-dd")+"T"+DateTime.Now.ToString("HH:mm:ss");

                    //Save settings 
                    var recieveDocumentsSyncInterval = Properties.Settings.Default?.RecievedDocumentsMinutesInterval ?? 15;
                    Properties.Settings.Default.RecievedDocumentsLastSyncDate = string.IsNullOrWhiteSpace(lastRecievedDocumentSyncDate) ? DateTime.Now.AddMinutes(-recieveDocumentsSyncInterval).ToString("yyyy-MM-dd") + "T" + DateTime.Now.ToString("HH:mm:ss"): lastRecievedDocumentSyncDate;
                    Properties.Settings.Default.Save();
                }

                else
                {
                    var dateTimeNow = DateTime.Now.ToString("yyyy-MM-dd") + "T" + DateTime.Now.ToString("HH:mm:ss"); 
                    lastRecievedDocumentSyncDate = Settings.Default.RecievedDocumentsLastSyncDate;
                    var documents = await GetRecievedDocumentsAsync(lastRecievedDocumentSyncDate, dateTimeNow);
                    if(documents.Any())
                    {
                        PopulateRecievedDocumentsTable(documents, out bool isNewAdded);

                        if (isNewAdded)
                        {
                            var msg = Shared.IsEnglish ? "Recieved Documents Updated!" : "تم تحديث المستندات المستلمة";
                            MessageBox.Show(msg);
                        }
                    }

                }

            }

            catch (Exception ex)
            {
                throw;
            }
            finally
            {
                httpClient.Dispose();
                dataContext.Dispose();
            }

        }



        private void Show(bool showLogo, bool showChart)
        {
            if (showChart)
                mi_ShowHideChart.Text = Shared.IsEnglish == true ? ResEn.mi_HideCharts : ResAr.mi_HideCharts; //"اخفاء الاحصائيات";
            else
                mi_ShowHideChart.Text = Shared.IsEnglish == true ? ResEn.mi_ShowCharts : ResAr.mi_ShowCharts;//"عرض الاحصائيات";


            uc_Charts uc_Chart = null;

            if (showChart)
            {
                uc_Chart = new uc_Charts();
                uc_Chart.Dock = System.Windows.Forms.DockStyle.Fill;
                uc_Chart.Location = new System.Drawing.Point(2, 2);
                uc_Chart.Name = "uc_Chart";
                uc_Chart.Size = new System.Drawing.Size(648, 550);
                uc_Chart.TabIndex = 0;
            }

            if (showChart && !showLogo)
            {
                this.pnlLarge.Controls.Clear();
                this.pnlLarge.Controls.Add(uc_Chart);
            }
            if (!showChart && showLogo)
            {
                this.pnlLarge.Controls.Clear();
                picLogo.Dock = DockStyle.Fill;
                picLogo.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Clip;
                this.pnlLarge.Controls.Add(picLogo);
            }
            if (!showChart && !showLogo)
            {
                this.pnlLarge.Controls.Clear();
            }

            if (showChart && showLogo)
            {
                this.pnlLarge.Controls.Clear();

                pnlImage.Dock = DockStyle.Top;
                this.pnlLarge.Controls.Add(pnlImage);

                this.pnlSmall.Anchor =
                    ((System.Windows.Forms.AnchorStyles)
                    (((
                    (System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));

                this.pnlLarge.Controls.Add(pnlSmall);

                this.pnlSmall.Controls.Add(uc_Chart);

                picLogo.Dock = DockStyle.Fill;
                picLogo.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Zoom;
                this.pnlImage.Controls.Add(picLogo);
            }
        }

        private void frmMain_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyData == Keys.W && e.Control == true)
            {
                Application.Exit();
            }
            if (e.KeyData == Keys.F1)
                Help.ShowHelp(null, @"ERP.chm");

            if (e.KeyCode == Keys.F12 && e.Shift == true)
                new frm_Excel_Import().Show();
        }

        private void frmMain_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (XtraMessageBox.Show(Shared.IsEnglish ? ResEn.AppClose : ResAr.AppClose,
    Shared.IsEnglish ? ResPrEn.MsgTWarn : ResPrAr.MsgTWarn, MessageBoxButtons.YesNo, MessageBoxIcon.Information, MessageBoxDefaultButton.Button2)
    == DialogResult.No)
            {
                e.Cancel = true;
                return;
            }

            //Save user profile
            ERPDataContext DB = new ERPDataContext();
            var user = DB.HR_Users.Where(u => u.UserId == Shared.UserId).FirstOrDefault();
            if (user != null)
            {
                user.IC_Transfer_FromDate = Shared.IC_Transfer_FromDate;
                user.IC_Transfer_ToDate = Shared.IC_Transfer_ToDate;
                user.IC_InTrns_FromDate = Shared.IC_InTrns_FromDate;
                user.IC_InTrns_ToDate = Shared.IC_InTrns_ToDate;
                user.IC_OutTrns_FromDate = Shared.IC_OutTrns_FromDate;
                user.IC_OutTrns_ToDate = Shared.IC_OutTrns_ToDate;
                user.IC_Damage_FromDate = Shared.IC_Damage_FromDate;
                user.IC_Damage_ToDate = Shared.IC_Damage_ToDate;

                user.PR_I_FromDate = Shared.PR_I_FromDate;
                user.PR_I_ToDate = Shared.PR_I_ToDate;
                user.PR_R_FromDate = Shared.PR_R_FromDate;
                user.PR_R_ToDate = Shared.PR_R_ToDate;

                user.SL_I_FromDate = Shared.SL_I_FromDate;
                user.SL_I_ToDate = Shared.SL_I_ToDate;
                user.SL_R_FromDate = Shared.SL_R_FromDate;
                user.SL_R_ToDate = Shared.SL_R_ToDate;
                user.SL_Q_FromDate = Shared.SL_Q_FromDate;
                user.SL_Q_ToDate = Shared.SL_Q_ToDate;

                Shared.Mr_InDrctSl_FromDate = user.Mr_InDrctSl_FromDate;
                Shared.Mr_InDrctSl_ToDate = user.Mr_InDrctSl_ToDate;

                user.Man_FromDate = Shared.Man_FromDate;
                user.Man_ToDate = Shared.Man_ToDate;
                user.Man_QC_FromDate = Shared.Man_QC_FromDate;
                user.Man_QC_ToDate = Shared.Man_QC_ToDate;

                user.Cash_FromDate = Shared.Cash_FromDate;
                user.Cash_ToDate = Shared.Cash_ToDate;

                user.Jrnl_FromDate = Shared.Jrnl_FromDate;
                user.Jrnl_ToDate = Shared.Jrnl_ToDate;

                user.att_clWeekEnd = Shared.att_clWeekEnd;
                user.att_clFormalVacation = Shared.att_clFormalVacation;
                user.att_clEmpVacation = Shared.att_clEmpVacation;
                user.att_clEmpAbsence = Shared.att_clEmpAbsence;
                user.att_clDelay = Shared.att_clDelay;

                user.HR_vacation_FromDate = Shared.HR_vacation_FromDate;
                user.HR_vacation_ToDate = Shared.HR_vacation_ToDate;
                user.HR_Absence_FromDate = Shared.HR_Absence_FromDate;
                user.HR_Absence_ToDate = Shared.HR_Absence_ToDate;
                user.HR_Delay_FromDate = Shared.HR_Delay_FromDate;
                user.HR_Delay_ToDate = Shared.HR_Delay_ToDate;
                user.HR_OverTime_FromDate = Shared.HR_OverTime_FromDate;
                user.HR_OverTime_ToDate = Shared.HR_OverTime_ToDate;
                user.HR_Reward_FromDate = Shared.HR_Reward_FromDate;
                user.HR_Reward_ToDate = Shared.HR_Reward_ToDate;
                user.HR_Penality_FromDate = Shared.HR_Penality_FromDate;
                user.HR_Penality_ToDate = Shared.HR_Penality_ToDate;
                user.HR_Pay_FromDate = Shared.HR_Pay_FromDate;
                user.HR_Pay_ToDate = Shared.HR_Pay_ToDate;
                user.Loan_FromDate = Shared.HR_Loan_FromDate;
                user.Loan_ToDate = Shared.HR_Loan_ToDate;

                user.HR_Eval_FromDate = Shared.HR_Eval_FromDate;
                user.HR_Eval_ToDate = Shared.HR_Eval_ToDate;
                user.HR_Prom_FromDate = Shared.HR_Prom_FromDate;
                user.HR_Prom_ToDate = Shared.HR_Prom_ToDate;
                user.HR_SponsrChng_FromDate = Shared.HR_SponsrChng_FromDate;
                user.HR_SponsrChng_ToDate = Shared.HR_SponsrChng_ToDate;
                user.HR_Train_FromDate = Shared.HR_Train_FromDate;
                user.HR_Train_ToDate = Shared.HR_Train_ToDate;
                user.HR_Train_FromDate = Shared.HR_Train_FromDate;
                user.HR_Train_ToDate = Shared.HR_Train_ToDate;
                user.ShiftReplace_FromDate = Shared.HR_ShiftReplace_FromDate;
                user.ShiftReplace_ToDate = Shared.HR_ShiftReplace_ToDate;

                user.Acc_DebitNote_FromDate = Shared.Acc_DebitNote_FromDate;
                user.Acc_DebitNote_ToDate = Shared.Acc_DebitNote_ToDate;
                user.Acc_CreditNote_FromDate = Shared.Acc_CreditNote_FromDate;
                user.Acc_CreditNote_ToDate = Shared.Acc_CreditNote_ToDate;

                user.Weight_FromDate = Shared.Weight_FromDate;
                user.Weight_ToDate = Shared.Weight_ToDate;

                user.Acc_CashTransfer_FromDate = Shared.Acc_CashTransfer_FromDate;
                user.Acc_CashTransfer_ToDate = Shared.Acc_CashTransfer_ToDate;
                user.Acc_Rev_FromDate = Shared.Acc_Rev_FromDate;
                user.Acc_Rev_ToDate = Shared.Acc_Rev_ToDate;
                user.Acc_Exp_FromDate = Shared.Acc_Exp_FromDate;
                user.Acc_Exp_ToDate = Shared.Acc_Exp_ToDate;

                user.Acc_RecNote_FromDate = Shared.Acc_RecNote_FromDate;
                user.Acc_RecNote_ToDate = Shared.Acc_RecNote_ToDate;
                user.Acc_PayNote_FromDate = Shared.Acc_PayNote_FromDate;
                user.Acc_PayNote_ToDate = Shared.Acc_PayNote_ToDate;

                user.Acc_PayNote_Still = Shared.Acc_PayNote_Still;
                user.Acc_PayNote_Paid = Shared.Acc_PayNote_Paid;
                user.Acc_PayNote_Rejected = Shared.Acc_PayNote_Rejected;
                user.Acc_PayNote_Overdue = Shared.Acc_PayNote_Overdue;
                user.Acc_ReceiveNote_Still = Shared.Acc_ReceiveNote_Still;
                user.Acc_ReceiveNote_Paid = Shared.Acc_ReceiveNote_Paid;
                user.Acc_ReceiveNote_Rejected = Shared.Acc_ReceiveNote_Rejected;
                user.Acc_ReceiveNote_Overdue = Shared.Acc_ReceiveNote_Overdue;

                user.JO_Reg_FromDate = Shared.JO_Reg_FromDate;
                user.JO_Reg_ToDate = Shared.JO_Reg_ToDate;
                user.JO_Due_FromDate = Shared.JO_Due_FromDate;
                user.JO_Due_ToDate = Shared.JO_Due_ToDate;

                user.ActiveNavBarGroup = navBarControl1.ActiveGroup.Name;
                user.StyleName = Shared.StyleName;
                user.ShowMainChart = Shared.ShowMainChart_;
            }
            DB.SubmitChanges();
        }


        #region Settings
        private void NBI_ST_PharmacyInfo_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            OpenPharmacyInfo();
        }
        private void mi_ST_PharmacyInfo_Click(object sender, EventArgs e)
        {
            OpenPharmacyInfo();
        }
        private void OpenPharmacyInfo()
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_ST_CompInfo)))
            {
                frm_ST_CompInfo frm_st_PharmacyInfo = new frm_ST_CompInfo();
                frm_st_PharmacyInfo.Icon = icon;
                frm_st_PharmacyInfo.BringToFront();
                frm_st_PharmacyInfo.ShowDialog();
                Shared.st_comp = GetAvialableModules();
            }
            else
            {
                if (Application.OpenForms["frm_ST_CompInfo"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_ST_CompInfo"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_ST_CompInfo"].BringToFront();
            }

            LoadImage();
            this.Text = Shared.CompName;
        }

        private void NBI_Styles_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            OpenStyles();
        }
        private void mi_Styles_Click(object sender, EventArgs e)
        {
            OpenStyles();
        }
        private void OpenStyles()
        {
            if (popupStyle.Visible == false)
            {
                popupStyle.Show();

                //select current style

            }
            else
                popupStyle.Hide();
        }

        private void miExit_Click(object sender, EventArgs e)
        {
            this.Close();
        }
        #endregion

        #region data
        private void NBI_IC_Item_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            OpenIC_ItemsList();
        }
        private void mi_IC_Item_Click(object sender, EventArgs e)
        {
            OpenIC_ItemsList();
        }
        private void OpenIC_ItemsList()
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_IC_ItemsList)))
            {
                frm_IC_ItemsList frm_ic_ItemsList = new frm_IC_ItemsList();
                frm_ic_ItemsList.BringToFront();
                frm_ic_ItemsList.Show();
            }
            else
            {
                if (Application.OpenForms["frm_IC_ItemsList"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_IC_ItemsList"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_IC_ItemsList"].BringToFront();
            }
        }

        private void mi_Matrix_Click(object sender, EventArgs e)
        {
            OpenIC_MatrixList();
        }
        private void NBI_Mtrx_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            OpenIC_MatrixList();
        }
        private void OpenIC_MatrixList()
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_IC_MatrixList)))
            {
                frm_IC_MatrixList frm_mtrx = new frm_IC_MatrixList();
                frm_mtrx.BringToFront();
                frm_mtrx.Show();
            }
            else
            {
                if (Application.OpenForms["frm_IC_MatrixList"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_IC_MatrixList"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_IC_MatrixList"].BringToFront();

            }
        }

        private void NBI_Company_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            OpenIC_CompaniesList();
        }
        private void mi_Company_Click(object sender, EventArgs e)
        {
            OpenIC_CompaniesList();
        }
        private void OpenIC_CompaniesList()
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_IC_CompaniesList)))
            {
                frm_IC_CompaniesList frm_ic_CompaniesList = new frm_IC_CompaniesList();
                frm_ic_CompaniesList.BringToFront();
                frm_ic_CompaniesList.Show();
            }
            else
            {
                if (Application.OpenForms["frm_IC_CompaniesList"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_IC_CompaniesList"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_IC_CompaniesList"].BringToFront();
            }
        }

        private void NBI_Category_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            OpenIC_CategoriesList();
        }
        private void mi_Category_Click(object sender, EventArgs e)
        {
            OpenIC_CategoriesList();
        }
        private void OpenIC_CategoriesList()
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_IC_Category)))
            {
                frm_IC_Category frm_ic_CategoriesList = new frm_IC_Category();
                frm_ic_CategoriesList.BringToFront();
                frm_ic_CategoriesList.Show();
            }
            else
            {
                if (Application.OpenForms["frm_IC_Category"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_IC_Category"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_IC_Category"].BringToFront();
            }
        }


        private void NBI_IC_Store_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            OpenIC_StoresList();
        }
        private void mi_IC_Store_Click(object sender, EventArgs e)
        {
            OpenIC_StoresList();
        }
        private void OpenIC_StoresList()
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_IC_StoresList)))
            {
                frm_IC_StoresList frm_ic_StoresList = new frm_IC_StoresList();
                frm_ic_StoresList.BringToFront();
                frm_ic_StoresList.Show();
            }
            else
            {
                if (Application.OpenForms["frm_IC_StoresList"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_IC_StoresList"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_IC_StoresList"].BringToFront();
            }
        }


        private void mi_UOM_Click(object sender, EventArgs e)
        {
            OpenIC_UOM();
        }

        private void OpenIC_UOM()
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_IC_UOM)))
            {
                frm_IC_UOM frm_ic_ItemsList = new frm_IC_UOM();
                frm_ic_ItemsList.BringToFront();
                frm_ic_ItemsList.Show();
            }
            else
            {
                if (Application.OpenForms["frm_IC_UOM"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_IC_UOM"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_IC_UOM"].BringToFront();
            }
        }

        #endregion

        #region Sell
        private void mi_SL_Invoice_Click(object sender, EventArgs e)
        {
            OpenSL_Invoice();
        }
        private void NBI_SL_Invoice_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            OpenSL_Invoice();
        }
        public static void OpenSL_Invoice()
        {
            // if (Shared.CarsAvailable == false)
            // {
            if (!ErpUtils.IsFormOpen(typeof(frm_SL_Invoice)))
            {
                frm_SL_Invoice f = new frm_SL_Invoice();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_SL_Invoice"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_SL_Invoice"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_SL_Invoice"].BringToFront();
            }
            // }
            //else
            //{
            //    if (!ErpUtils.IsFormOpen(typeof(frm_Cars_SL_Invoices)))
            //    {
            //        frm_Cars_SL_Invoices f = new frm_Cars_SL_Invoices();
            //        f.BringToFront();
            //        f.Show();
            //    }
            //    else
            //    {
            //        if (Application.OpenForms["frm_Cars_SL_Invoices"].WindowState == FormWindowState.Minimized)
            //            Application.OpenForms["frm_Cars_SL_Invoices"].WindowState = FormWindowState.Normal;

            //        Application.OpenForms["frm_Cars_SL_Invoices"].BringToFront();
            //    }
            //}
        }

        private void mi_SL_InvoiceList_Click(object sender, EventArgs e)
        {
            OpenSL_InvoiceList();
        }
        public static void OpenSL_InvoiceList()
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_SL_InvoiceList)))
            {
                frm_SL_InvoiceList f = new frm_SL_InvoiceList();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_SL_InvoiceList"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_SL_InvoiceList"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_SL_InvoiceList"].BringToFront();
            }
        }

        private void mi_sl_customersGroups_Click(object sender, EventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_SL_CustomerCategory)))
            {
                frm_SL_CustomerCategory f = new frm_SL_CustomerCategory();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_SL_CustomerGroup"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_SL_CustomerGroup"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_SL_CustomerGroup"].BringToFront();
            }
        }

        private void mi_SL_Customer_Click_1(object sender, EventArgs e)
        {
            OpenSL_Customer();
        }
        private void NBI_SL_Customer_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            OpenSL_Customer();
        }

        public static void OpenSL_Customer()
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_SL_CustomerList)))
            {
                frm_SL_CustomerList f = new frm_SL_CustomerList();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_SL_CustomerList"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_SL_CustomerList"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_SL_CustomerList"].BringToFront();
            }
        }

        private void mi_SL_Return_Click(object sender, EventArgs e)
        {
            OpenSL_return();
        }
        private void NBI_SL_Return_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            OpenSL_return();
        }
        private void OpenSL_return()
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_SL_Return)))
            {
                frm_SL_Return f = new frm_SL_Return();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_SL_Return"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_SL_Return"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_SL_Return"].BringToFront();
            }
        }

        private void mi_SL_ReturnList_Click(object sender, EventArgs e)
        {
            OpenSL_returnList();
        }
        public static void OpenSL_returnList()
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_SL_ReturnList)))
            {
                frm_SL_ReturnList f = new frm_SL_ReturnList();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_SL_ReturnList"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_SL_ReturnList"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_SL_ReturnList"].BringToFront();
            }
        }
        #endregion



        #region Help
        private void mi_GlobalHelp_Click(object sender, EventArgs e)
        {
            //new frm_ReEvaluate().Show();
            Help.ShowHelp(null, @"ERP.chm");
        }

        private void mi_HowToStart_Click(object sender, EventArgs e)
        {
            try
            {
                System.Diagnostics.Process.Start(@"ERP User Guide.pdf", @"/a");
            }
            catch
            {
                XtraMessageBox.Show(Shared.IsEnglish == true ? ResEn.MsgFileNotExist : ResAr.MsgFileNotExist);//"الملف غير موجود"
            }
        }

        private void mi_SendEMail_Click(object sender, EventArgs e)
        {
            System.Diagnostics.Process.Start("https://www.linkitsys.com");
        }

        private void mi_GlobalProducts_Click(object sender, EventArgs e)
        {
            System.Diagnostics.Process.Start("https://www.linkitsys.com/ar/");
        }

        private void mi_GlobalGrid_Click(object sender, EventArgs e)
        {
            System.Diagnostics.Process.Start("https://www.linkitsys.com");
        }

        private void mi_globalERPRegistration_Click(object sender, EventArgs e)
        {
            new frmActivation().ShowDialog();


            this.Text = Shared.CompName;
            this.Text += " " + Shared.TrialVersion;
        }

        private void mi_About_Click(object sender, EventArgs e)
        {
            new frmAbout().ShowDialog();
        }

        #endregion


        private void NBI_ReportCenter_LinkClicked(object sender, DevExpress.XtraNavBar.NavBarLinkEventArgs e)
        {
            OpenReportViewer();
        }
        private void mi_reportCenter_Click(object sender, EventArgs e)
        {
            OpenReportViewer();
        }
        private void OpenReportViewer()
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_ReportViewer)))
            {
                frm_ReportViewer frm_ReportViewer = new frm_ReportViewer(ErpHelper.OpenSourceProcess, ErpHelper.OpenJobOrder);
                frm_ReportViewer.BringToFront();
                frm_ReportViewer.Show();
            }
            else
            {
                if (Application.OpenForms["frm_ReportViewer"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_ReportViewer"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_ReportViewer"].BringToFront();
            }

        }

        private void mi_Windows_DropDownOpening(object sender, EventArgs e)
        {
            LoadMenuItems();
        }
        private void LoadMenuItems()
        {
            this.mi_Windows.DropDownItems.Clear();
            if (Application.OpenForms.Count < 4)
                return;

            foreach (Form frm in Application.OpenForms)
            {
                if (frm.Name == "frmSplash" ||
                    frm.Name == "frmLogin" ||
                    frm.Name == "frmMain")
                    continue;
                if (frm.Text.Trim() == string.Empty)
                    continue;

                ToolStripMenuItem mi_1 = new ToolStripMenuItem();
                mi_1.Name = frm.Name;
                //mi_1.Size = new System.Drawing.Size(62, 20);
                mi_1.Text = frm.Text;
                mi_1.BackColor = Color.White;

                mi_1.Click += new EventHandler(mi_1_Click);
                this.mi_Windows.DropDownItems.Add(mi_1);
            }

            ToolStripSeparator mi_Line = new ToolStripSeparator();
            this.mi_Windows.DropDownItems.Add(mi_Line);

            ToolStripMenuItem mi_cascade = new ToolStripMenuItem();
            mi_cascade.Name = "mi_Cascade";
            mi_cascade.Text = Shared.IsEnglish == true ? ResEn.mi_ShowAll : ResAr.mi_ShowAll;// "عرض الكل"
            mi_cascade.Click += new EventHandler(mi_cascade_Click);
            this.mi_Windows.DropDownItems.Add(mi_cascade);

            ToolStripMenuItem mi_CloseAll = new ToolStripMenuItem();
            mi_CloseAll.Name = "mi_CloseAll";
            mi_CloseAll.Text = Shared.IsEnglish == true ? ResEn.mi_CloseAll : ResAr.mi_CloseAll;//"غلق الكل"
            mi_CloseAll.Click += new EventHandler(mi_CloseAll_Click);
            this.mi_Windows.DropDownItems.Add(mi_CloseAll);

        }

        void mi_cascade_Click(object sender, EventArgs e)
        {
            this.LayoutMdi(MdiLayout.Cascade);
            Form[] forms = Application.OpenForms.Cast<Form>().ToArray();
            foreach (Form thisForm in forms)
            {
                thisForm.BringToFront();
            }
        }

        void mi_CloseAll_Click(object sender, EventArgs e)
        {
            Form[] forms = Application.OpenForms.Cast<Form>().ToArray();
            foreach (Form thisForm in forms)
            {
                if (thisForm.Name != "frmSplash" &&
                    thisForm.Name != "frmLogin" &&
                    thisForm.Name != "frmMain")
                    thisForm.Close();
            }
        }

        void mi_1_Click(object sender, EventArgs e)
        {
            Application.OpenForms[((ToolStripMenuItem)sender).Name].BringToFront();
        }

        private void rdoStyles_SelectedIndexChanged(object sender, EventArgs e)
        {
            string styleName = rdoStyles.EditValue?.ToString();
            ChangeStyle(styleName);
        }
        private void ChangeStyle(string styleName)
        {
            Shared.StyleName = styleName;
            if (styleName == "Flat")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetFlatStyle();
                rdoStyles.SelectedIndex = 0;
            }
            if (styleName == "Ultra Flat")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetUltraFlatStyle();
                rdoStyles.SelectedIndex = 1;
            }
            if (styleName == "Style 3D")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetStyle3D();
                rdoStyles.SelectedIndex = 2;
            }
            if (styleName == "Office 2003")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetOffice2003Style();
                rdoStyles.SelectedIndex = 3;
            }
            if (styleName == "Windows XP")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetWindowsXPStyle();
                rdoStyles.SelectedIndex = 4;
            }
            if (styleName == "Black")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle.Skin, false, false,
                    "Black");
                rdoStyles.SelectedIndex = 5;
            }
            if (styleName == "Blue")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle.Skin, false, false,
                    "Blue");
                rdoStyles.SelectedIndex = 6;
            }
            if (styleName == "Caramel")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle.Skin, false, false,
                    "Caramel");
                rdoStyles.SelectedIndex = 7;
            }
            if (styleName == "iMaginary")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle.Skin, false, false,
                    "iMaginary");
                rdoStyles.SelectedIndex = 8;
            }
            if (styleName == "Lilian")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle.Skin, false, false,
                    "Lilian");
                rdoStyles.SelectedIndex = 9;
            }
            if (styleName == "Money Twins")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle.Skin, false, false,
                    "Money Twins");
                rdoStyles.SelectedIndex = 10;
            }
            if (styleName == "The Asphalt World")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle.Skin, false, false,
                    "The Asphalt World");
                rdoStyles.SelectedIndex = 11;
            }
            if (styleName == "Coffee")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle.Skin, false, false,
                    "Coffee");
                rdoStyles.SelectedIndex = 12;
            }
            if (styleName == "Dark Side")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle.Skin, false, false,
                    "Dark Side");
                rdoStyles.SelectedIndex = 13;
            }
            if (styleName == "Darkroom")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle.Skin, false, false,
                    "Darkroom");
                rdoStyles.SelectedIndex = 14;
            }
            if (styleName == "DevExpress Style")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle.Skin, false, false,
                    "DevExpress Style");
                rdoStyles.SelectedIndex = 15;
            }
            if (styleName == "Foggy")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle.Skin, false, false,
                    "Foggy");
                rdoStyles.SelectedIndex = 16;
            }
            if (styleName == "Glass Oceans")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle.Skin, false, false,
                    "Glass Oceans");
                rdoStyles.SelectedIndex = 17;
            }
            if (styleName == "High Contrast")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle.Skin, false, false,
                    "High Contrast");
                rdoStyles.SelectedIndex = 18;
            }
            if (styleName == "Liquid Sky")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle.Skin, false, false,
                    "Liquid Sky");
                rdoStyles.SelectedIndex = 19;
            }
            if (styleName == "London Liquid Sky")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle.Skin, false, false,
                    "London Liquid Sky");
                rdoStyles.SelectedIndex = 20;
            }
            if (styleName == "McSkin")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle.Skin, false, false,
                    "McSkin");
                rdoStyles.SelectedIndex = 21;
            }
            if (styleName == "Pumpkin")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle.Skin, false, false,
                    "Pumpkin");
                rdoStyles.SelectedIndex = 22;
            }
            if (styleName == "Seven")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle.Skin, false, false,
                    "Seven");
                rdoStyles.SelectedIndex = 23;
            }
            if (styleName == "Seven Classic")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle.Skin, false, false,
                    "Seven Classic");
                rdoStyles.SelectedIndex = 24;
            }
            if (styleName == "Sharp")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle.Skin, false, false,
                    "Sharp");
                rdoStyles.SelectedIndex = 25;
            }
            if (styleName == "Sharp Plus")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle.Skin, false, false,
                    "Sharp Plus");
                rdoStyles.SelectedIndex = 26;
            }

            if (styleName == "Springtime")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle.Skin, false, false,
                    "Springtime");
                rdoStyles.SelectedIndex = 27;
            }
            if (styleName == "Stardust")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle.Skin, false, false,
                    "Stardust");
                rdoStyles.SelectedIndex = 28;
            }
            if (styleName == "Summer 2008")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle.Skin, false, false,
                    "Summer 2008");
                rdoStyles.SelectedIndex = 29;
            }
            if (styleName == "Xmas 2008 Blue")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle.Skin, false, false,
                    "Xmas 2008 Blue");
                rdoStyles.SelectedIndex = 30;
            }
            if (styleName == "Valentine")
            {
                this.defaultLookAndFeel1.LookAndFeel.UseDefaultLookAndFeel = false;
                this.defaultLookAndFeel1.LookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle.Skin, false, false,
                    "Valentine");
                rdoStyles.SelectedIndex = 31;
            }
        }

        private void btnClosePopup_Click(object sender, EventArgs e)
        {
            popupStyle.Hide();
        }

        private void LoadUserPriv()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            if(DB.HR_Users.FirstOrDefault(x=>x.UserId==Shared.UserId).E_InvoiceSetter==true)
            {
                mi_E_InvoiceSettings.Visible = true;
            }
        }

        private void LoadImage()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            var logo = (from d in DB.ST_CompanyInfos
                        select d.Logo).FirstOrDefault();

            if (logo == null)
            {
                picLogo.Text = string.Empty;
                picLogo.Visible = false;
            }
            else
            {
                picLogo.Text = string.Empty;
                picLogo.Visible = true;
                picLogo.Image = ErpUtils.byteArrayToImage(logo.ToArray());
            }

        }

        private ST_CompanyInfo GetAvialableModules()
        {
            ERPDataContext DB = new ERPDataContext();

            ST_CompanyInfo comp = (from u in DB.ST_CompanyInfos
                                   select u).First();

            Shared.CompName = comp.CmpNameAr;

            Shared.MaufacturingAvailable = false;
            Shared.LibraAvailabe = false;
            Shared.AccountingAvailable = false;
            Shared.HRAvailable = false;
            Shared.CarsAvailable = false;
            Shared.PayslipAvailable = false;
            Shared.POSAvailable = false;
            Shared.PriceListAvailable = false;
            Shared.ContractAvailable = false;
            Shared.JobOrderAvailable = false;
            Shared.LaundryAvailable = false;
            Shared.SalesOrderAvailable = false;
            Shared.MarkettingAvailable = false;
            Shared.ChecksAvailable = false;
            Shared.LcAvailable = false;
            Shared.BascolAvailable = false;
            Shared.ItemsPostingAvailable = false;
            Shared.SalesManAvailable = false;
            Shared.RealEstateAvailable = false;
            Shared.SysModelIsERP = false;
            Shared.SalesOnlyAvailable = true;
            Shared.FA = false;
            Shared.Mall = false;
            Shared.Imp_exp = false;
            Shared.Shareholder = false;
            Shared.Pay_Rec = false;
            Shared.InventoryAvailable = false;


            if (!string.IsNullOrEmpty(comp.Tax))
            {
                if (Crypto.DecryptStringAES(comp.Tax, Crypto.Key) == "Tax-Yes")
                    Shared.TaxAvailable = true;
                else if (Crypto.DecryptStringAES(comp.Tax, Crypto.Key) == "Tax-No")
                    Shared.TaxAvailable = false;
                else
                    Shared.TaxAvailable = false;
            }
            else
                Shared.TaxAvailable = false;

            if (!string.IsNullOrEmpty(comp.ItemMatrix))
            {
                if (Crypto.DecryptStringAES(comp.ItemMatrix, Crypto.Key) == "ItemMatrix-Yes")
                    Shared.ItemMatrixAvailable = true;
                else if (Crypto.DecryptStringAES(comp.ItemMatrix, Crypto.Key) == "ItemMatrix-No")
                    Shared.ItemMatrixAvailable = false;
                else
                    Shared.ItemMatrixAvailable = false;
            }
            else
                Shared.ItemMatrixAvailable = false;

            if (!string.IsNullOrEmpty(comp.EgyptPharmacyTax))
            {
                if (Crypto.DecryptStringAES(comp.EgyptPharmacyTax, Crypto.Key) == "EgyptPharmacyTax-Yes")
                    Shared.EgyptPharmacyTaxAvailable = true;
                else if (Crypto.DecryptStringAES(comp.EgyptPharmacyTax, Crypto.Key) == "EgyptPharmacyTax-No")
                    Shared.EgyptPharmacyTaxAvailable = false;
                else
                    Shared.EgyptPharmacyTaxAvailable = false;
            }
            else
                Shared.EgyptPharmacyTaxAvailable = false;

            if (!string.IsNullOrEmpty(comp.WoodTrade))
            {
                if (Crypto.DecryptStringAES(comp.WoodTrade, Crypto.Key) == "WoodTrade-Yes")
                    Shared.WoodTradeAvailable = true;
                else if (Crypto.DecryptStringAES(comp.WoodTrade, Crypto.Key) == "WoodTrade-No")
                    Shared.WoodTradeAvailable = false;
                else
                    Shared.WoodTradeAvailable = false;
            }
            else
                Shared.WoodTradeAvailable = false;


            if (!string.IsNullOrEmpty(comp.OfflinePostToGL))
            {
                if (Crypto.DecryptStringAES(comp.OfflinePostToGL, Crypto.Key) == "OfflinePostToGL-Yes")
                    Shared.OfflinePostToGL = true;
                else if (Crypto.DecryptStringAES(comp.OfflinePostToGL, Crypto.Key) == "OfflinePostToGL-No")
                    Shared.OfflinePostToGL = false;
                else
                    Shared.OfflinePostToGL = false;
            }
            else
                Shared.OfflinePostToGL = false;

            if (!string.IsNullOrEmpty(comp.InvoicePostToStore))
            {
                if (Crypto.DecryptStringAES(comp.InvoicePostToStore, Crypto.Key) == "InvoicePostToStore-Yes")
                    Shared.InvoicePostToStore = true;
                else if (Crypto.DecryptStringAES(comp.InvoicePostToStore, Crypto.Key) == "InvoicePostToStore-No")
                    Shared.InvoicePostToStore = false;
                else
                    Shared.InvoicePostToStore = true;
            }
            else
                Shared.InvoicePostToStore = true;

            if (!string.IsNullOrEmpty(comp.PrInvoicePostToStore))
            {
                if (Crypto.DecryptStringAES(comp.PrInvoicePostToStore, Crypto.Key) == "InvoicePostToStore-Yes")
                    Shared.PrInvoicePostToStore = true;
                else if (Crypto.DecryptStringAES(comp.PrInvoicePostToStore, Crypto.Key) == "InvoicePostToStore-No")
                    Shared.PrInvoicePostToStore = false;
                else
                    Shared.PrInvoicePostToStore = true;
            }
            else
                Shared.PrInvoicePostToStore = true;

            if (!string.IsNullOrEmpty(comp.Currencies))
            {
                if (Crypto.DecryptStringAES(comp.Currencies, Crypto.Key) == "Currency-Yes")
                    Shared.CurrencyAvailable = true;
                else if (Crypto.DecryptStringAES(comp.Currencies, Crypto.Key) == "Currency-No")
                    Shared.CurrencyAvailable = false;
                else
                    Shared.CurrencyAvailable = false;
            }
            else
                Shared.CurrencyAvailable = false;

            if (!string.IsNullOrEmpty(comp.E_invoiceAvailable))
            {
                if (Crypto.DecryptStringAES(comp.E_invoiceAvailable, Crypto.Key) == "EInvoice-Yes")
                    Shared.E_invoiceAvailable = true;
                else if (Crypto.DecryptStringAES(comp.E_invoiceAvailable, Crypto.Key) == "EInvoice-No")
                    Shared.E_invoiceAvailable = false;
                else
                    Shared.E_invoiceAvailable = false;
            }
            else
                Shared.E_invoiceAvailable = false;
            Shared.StockIsPeriodic = comp.StockIsPeriodic;
            Shared.FpDependOnInOut = comp.FpDependOnInOut;

            return comp;
        }

        private void EnableDisableModules()
        {


        }

        private void frmMain_Shown(object sender, EventArgs e)
        {
            Thread t1 = new Thread(new ThreadStart(threadBegin));
            t1.Start();
            //         if (Shared.LstUserPrvlg == null /*admin*/||
            //             Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.ACC_PayNote).FirstOrDefault() != null /*user has privilage*/)
            //         {
            //             if (Shared.user.NotesPayableAlertDays.HasValue)
            //             {
            //                 if ((from x in new ERPDataContext().ACC_NotesPayables
            //                      where x.ResponseType == (byte)PayNoteResponseType.Still
            //                      && x.DueDate.Date < MyHelper.Get_Server_DateTime().Date.AddDays(Shared.user.NotesPayableAlertDays.Value)
            //                      select x).Count() > 0)
            //                 {
            //                     frm_ACC_PayNoteList f = new frm_ACC_PayNoteList(Shared.user.NotesPayableAlertDays.Value);
            //                     f.BringToFront();
            //                     f.Show();
            //                 }
            //             }
            //         }

            //         if (Shared.LstUserPrvlg == null /*admin*/||
            //             Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.ACC_RecieveNote).FirstOrDefault() != null /*user has privilage*/)
            //         {
            //             if (Shared.user.NotesReceivableAlertDays.HasValue)
            //             {
            //                 if ((from x in new ERPDataContext().ACC_NotesReceivables
            //                      where x.ResponseType == (byte)ReceiveNoteResponseType.Still
            //                      && x.DueDate.Date < MyHelper.Get_Server_DateTime().Date.AddDays(Shared.user.NotesReceivableAlertDays.Value)
            //                      select x).Count() > 0)
            //                 {
            //                     frm_ACC_RecieveNoteList f1 = new frm_ACC_RecieveNoteList(Shared.user.NotesReceivableAlertDays.Value);
            //                     f1.BringToFront();
            //                     f1.Show();
            //                 }
            //             }
            //         }

            //         if (Shared.LstUserPrvlg == null /*admin*/||
            //              Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.frm_SL_Warranty).FirstOrDefault() != null /*user has privilage*/)
            //         {
            //             if (Shared.user.AlarmWarrantyItemsDays > 0)
            //             {
            //                 DateTime date1 = DateTime.Now;
            //                 DateTime date2 = DateTime.Now.AddDays(Shared.user.AlarmWarrantyItemsDays);
            //                 Reports.frm_SL_Warranty rprt = new Reports.frm_SL_Warranty(Shared.IsEnglish ? ResRptEn.frm_SL_Warranty : ResRptAr.frm_SL_Warranty,
            //                     date1.ToShortDateString() + " - " + date2.ToShortDateString(), string.Empty,
            //         0, 0, 0,
            //         2, date1, date2,
            //         0, 0, 0,
            //         0, string.Empty,
            //         0, string.Empty,
            //         0, 0, 0,
            //         0, 0, string.Empty, 0, 0);
            //                 if (rprt.UserCanOpen && rprt.count > 0)
            //                     rprt.Show();

            //             }
            //         }

            //         if (Shared.LstUserPrvlg == null /*admin*/||
            //  Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.rpt_IC_ItemsExpired).FirstOrDefault() != null /*user has privilage*/)
            //         {
            //             if (Shared.user.AlarmExpiredItemsDays > 0)
            //             {
            //                 DateTime date2 = DateTime.Now.AddDays(Shared.user.AlarmExpiredItemsDays);
            //                 Reports.frm_IC_ItemsExpired rprt = new Reports.frm_IC_ItemsExpired(Shared.IsEnglish ? ResRptEn.rpt_IC_ItemsExpired : ResRptAr.rpt_IC_ItemsExpired,
            //                     date2.ToShortDateString(), string.Empty,
            //         0, 0, 0,
            //         0, 0, 0, string.Empty, 0, 0, 0, date2);
            //                 if (rprt.UserCanOpen && rprt.count > 0)
            //                     rprt.Show();

            //             }
            //         }
            //         //update 13/9/2017
            //         if (Shared.LstUserPrvlg == null /*admin*/||
            // Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.rpt_Ic_ItemsReorder).FirstOrDefault() != null /*user has privilage*/)
            //         {
            //             if (Shared.user.AlarmItemsReorder > 0)
            //             {
            //                 DateTime date2 = DateTime.Now.AddDays(Shared.user.AlarmItemsReorder);
            //                 Reports.frm_IC_ItemsReorder rprt = new Reports.frm_IC_ItemsReorder(Shared.IsEnglish ? ResRptEn.rpt_IC_ItemsReorder : ResRptAr.rpt_IC_ItemsReorder,
            //                     date2.ToShortDateString(), string.Empty,
            //         0, 0, 0,
            //         0, 0, 0, string.Empty, 0, 0, 0/*, date2*/);
            //                 if (rprt.UserCanOpen && rprt.count > 0)
            //                     rprt.Show();

            //             }
            //         }

            //         if (Shared.LstUserPrvlg == null /*admin*/||
            //Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.rpt_IC_ItemsMinLevel).FirstOrDefault() != null /*user has privilage*/)
            //         {
            //             if (Shared.user.AlarmItemsMinLimit > 0)
            //             {
            //                 DateTime date2 = DateTime.Now.AddDays(Shared.user.AlarmItemsReorder);
            //                 frm_IC_ItemsMinLevel rprt = new frm_IC_ItemsMinLevel(Shared.IsEnglish ? ResRptEn.rpt_IC_ItemsMinLevel : ResRptAr.rpt_IC_ItemsMinLevel,
            //                     date2.ToShortDateString(), string.Empty,
            //         0, 0, 0,
            //         0, 0, 0, string.Empty, 0, 0, 0/*, date2*/);
            //                 if (rprt.UserCanOpen && rprt.count > 0)
            //                     rprt.Show();

            //             }
            //         }

        }



        private void mi_ChangePass_Click(object sender, EventArgs e)
        {
            new frm_ST_ChangeUserPass().ShowDialog();
        }


        //convert interface from arabic to eglish and vice versa
        //private void mi_Arabic_Click(object sender, EventArgs e)
        //{
        //    if (((ToolStripMenuItem)sender).Name == "mi_Arabic")
        //    {
        //        if (Shared.IsEnglish == false)
        //            return;

        //        Shared.IsEnglish = false;
        //        RTL.EnCulture(Shared.IsEnglish);

        //        InitializeComponent();
        //        RTL.LTRLayout(this);                
        //    }

        //    else if (((ToolStripMenuItem)sender).Name == "mi_English")
        //    {
        //        if (Shared.IsEnglish == true)
        //            return;

        //        Shared.IsEnglish = true;
        //        RTL.EnCulture(Shared.IsEnglish);

        //        InitializeComponent();

        //        RTL.LTRLayout(this);
        //    }
        //}



        private delegate void InvokeHandler();
        private void threadBegin()
        {
            //this.Invoke(new InvokeHandler(delegate ()
            //{
            //    //samar

            //    if (Shared.LstUserPrvlg == null /*admin*/||
            //            Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.frm_Sl_JobOrderStatus).FirstOrDefault() != null /*user has privilage*/)
            //    {
            //        if (Shared.user.JO_Alert == true)
            //        {
            //            var DB = new ERPDataContext();
            //            var daysAttention = DB.HR_Users.Where(a => a.UserId == Shared.UserId && a.JO_Alert == true).FirstOrDefault().JO_AlertDays.GetValueOrDefault(0);
            //            DateTime date2 = DateTime.Now;
            //            var date1 = date2.AddDays(-daysAttention);

            //            frm_Sl_JobOrderStatus f = new frm_Sl_JobOrderStatus(true, "أوامر العمل المسموح فيها بالتنبيه", "", "", date1, date2, 2, ErpHelper.OpenJobOrder);

            //            if (frm_Sl_JobOrderStatus.findData)
            //            {
            //                f.BringToFront();
            //                f.Show();
            //                frm_Sl_JobOrderStatus.findData = false;
            //            }




            //        }
            //    }
            //}));
        }

        private void mi_SL_InvoiceArchive_Click(object sender, EventArgs e)
        {
            OpenSLArchive();
        }

        private void OpenSLArchive()
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_SL_InvoiceArchiveList)))
            {
                frm_SL_InvoiceArchiveList f = new frm_SL_InvoiceArchiveList();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_SL_InvoiceArchiveList"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_SL_InvoiceArchiveList"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_SL_InvoiceArchiveList"].BringToFront();
            }

        }


        private void picLogo_EditValueChanged(object sender, EventArgs e)
        {

        }

        private void mi_Sl_ReturnArchive_Click(object sender, EventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_SL_ReturnArchiveList)))
            {
                frm_SL_ReturnArchiveList f = new frm_SL_ReturnArchiveList();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_SL_ReturnArchiveList"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_SL_ReturnArchiveList"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_SL_ReturnArchiveList"].BringToFront();
            }

        }

        private void newPermissionToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_HR_EmpPermissiontoattendandleave)))
            {
                frm_HR_EmpPermissiontoattendandleave f = new frm_HR_EmpPermissiontoattendandleave(0);
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_HR_EmpPermissiontoattendandleave"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_HR_EmpPermissiontoattendandleave"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_HR_EmpPermissiontoattendandleave"].BringToFront();
            }
        }

        private void attendanToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_HR_InOutRule)))
            {
                frm_HR_InOutRule f = new frm_HR_InOutRule();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_HR_InOutRule"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_HR_InOutRule"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_HR_InOutRule"].BringToFront();
            }
        }

        private void mi_permissions_Click(object sender, EventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_HR_EmpPermissiontoattendandleaveList)))
            {
                frm_HR_EmpPermissiontoattendandleaveList f = new frm_HR_EmpPermissiontoattendandleaveList();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_HR_EmpPermissiontoattendandleaveList"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_HR_EmpPermissiontoattendandleaveList"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_HR_EmpPermissiontoattendandleaveList"].BringToFront();
            }
        }

        private void salesInvoicesToolStripMenuItem_Click(object sender, EventArgs e)
        {

        }

        private void salesReturnInvoicesToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_SL_ReturnList)))
            {
                frm_SL_ReturnList f = new frm_SL_ReturnList(true, true);
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_SL_ReturnList"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_SL_ReturnList"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_SL_ReturnList"].BringToFront();
            }

        }

        private void mi_CustomersGroups_Click(object sender, EventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_SL_CustomerGroup)))
            {
                frm_SL_CustomerGroup f = new frm_SL_CustomerGroup();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_SL_CustomerGroup"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_SL_CustomerGroup"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_SL_CustomerGroup"].BringToFront();
            }
        }


        private void CTA_LinkedIn_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (Properties.Settings.Default.Lnkd != null && Properties.Settings.Default.Lnkd != string.Empty)
                System.Diagnostics.Process.Start(Properties.Settings.Default.Lnkd);
            else
                System.Diagnostics.Process.Start("https://www.linkedin.com/company/10936339/");
        }

        private void CTA_Twitter_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (Properties.Settings.Default.Tw != null && Properties.Settings.Default.Tw != string.Empty)
                System.Diagnostics.Process.Start(Properties.Settings.Default.Tw);
            else
                System.Diagnostics.Process.Start("https://twitter.com/linkitsys");
        }

        private void CTA_Facebook_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (Properties.Settings.Default.fb != null && Properties.Settings.Default.fb != string.Empty)
                System.Diagnostics.Process.Start(Properties.Settings.Default.fb);
            else
                System.Diagnostics.Process.Start("https://www.facebook.com/LinkITsys");
        }

        private void authenticationToolStripMenuItem_Click(object sender, EventArgs e)
        {

            if (!ErpUtils.IsFormOpen(typeof(frm_HR_Authorization)))
            {
                frm_HR_Authorization frmMan
                    = new frm_HR_Authorization();
                frmMan.BringToFront();
                frmMan.Show();
            }
            else
            {
                if (Application.OpenForms["frm_HR_Authorization"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_HR_Authorization"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_HR_Authorization"].BringToFront();
            }
        }

        private void deliveryToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_SL_Delivery)))
            {
                frm_SL_Delivery frmMan
                    = new frm_SL_Delivery();
                frmMan.BringToFront();
                frmMan.Show();
            }
            else
            {
                if (Application.OpenForms["frm_SL_Delivery"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_SL_Delivery"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_SL_Delivery"].BringToFront();
            }
        }


        private void mi_Branches_Click(object sender, EventArgs e)
        {
            OpenBranches();
        }
        public static void OpenBranches()
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_HR_BranchList)))
            {
                frm_HR_BranchList f = new frm_HR_BranchList();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_HR_BranchList"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_HR_BranchList"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_HR_BranchList"].BringToFront();
            }

        }

        private void apexToolStripMenuItem_Click(object sender, EventArgs e)
        {
            //new Apex1().ShowDialog();
        }


        private void mi_BusinessTax_Click(object sender, EventArgs e)
        {

            if (!ErpUtils.IsFormOpen(typeof(frm_HR_BusinessTax)))
            {
                frm_HR_BusinessTax f = new frm_HR_BusinessTax();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_HR_BusinessTax"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_HR_BusinessTax"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_HR_BusinessTax"].BringToFront();
            }
        }

        private void pnlSmall_Paint(object sender, PaintEventArgs e)
        {

        }

        private void showNotificationToolStripMenuItem_Click(object sender, EventArgs e)
        {

        }

        private void mi_CustomerItems_Click(object sender, EventArgs e)
        {

            if (!ErpUtils.IsFormOpen(typeof(frm_IC_Customer_Items)))
            {
                frm_IC_Customer_Items f = new frm_IC_Customer_Items();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_IC_Customer_Items"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_IC_Customer_Items"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_IC_Customer_Items"].BringToFront();
            }
        }

        private void mi_Intencive_Click(object sender, EventArgs e)
        {

            if (!ErpUtils.IsFormOpen(typeof(frm_HR_Incentives)))
            {
                frm_HR_Incentives f = new frm_HR_Incentives();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_HR_Incentives"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_HR_Incentives"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_HR_Incentives"].BringToFront();
            }
        }

        private void mi_ImportExcelFiles_Click(object sender, EventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(btnJournal)))
            {
                btnJournal f = new btnJournal();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["btnJournal"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["btnJournal"].WindowState = FormWindowState.Normal;

                Application.OpenForms["btnJournal"].BringToFront();
            }

        }

        private void mi_payRecieveNotes_Click(object sender, EventArgs e)
        {

        }

        private void mi_Import_Export_Click(object sender, EventArgs e)
        {

        }

        private void salesApprveToolStripMenuItem_Click(object sender, EventArgs e)
        {

        }

        private void Scr_UnitOfMeaure_Click(object sender, EventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_E_UOM)))
            {
                frm_E_UOM f = new frm_E_UOM();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_E_UOM"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_E_UOM"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_E_UOM"].BringToFront();
            }
        }

        private void scr_Currency_Click(object sender, EventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_E_Currency)))
            {
                frm_E_Currency f = new frm_E_Currency();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_E_Currency"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_E_Currency"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_E_Currency"].BringToFront();
            }
        }

        private void scr_Item_Codes_Click(object sender, EventArgs e)
        {

            if (!ErpUtils.IsFormOpen(typeof(frm_E_ItemCodes)))
            {
                frm_E_ItemCodes f = new frm_E_ItemCodes();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_E_ItemCodes"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_E_ItemCodes"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_E_ItemCodes"].BringToFront();
            }
        }

        private void src_Countries_Click(object sender, EventArgs e)
        {
            OpenCountry();
        }

        public static void OpenCountry()
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_HR_Country)))
            {
                frm_HR_Country f = new frm_HR_Country();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_HR_Country"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_HR_Country"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_HR_Country"].BringToFront();
            }

        }

        private void mi_EinvoiceSync_Click(object sender, EventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_E_InvoiceList)))
            {
                frm_E_InvoiceList f = new frm_E_InvoiceList();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_E_InvoiceList"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_E_InvoiceList"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_E_InvoiceList"].BringToFront();
            }
        }

        private void eInvoicesUnsyncToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_E_InvoiceList_Rejected)))
            {
                frm_E_InvoiceList_Rejected f = new frm_E_InvoiceList_Rejected();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_E_InvoiceList_Rejected"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_E_InvoiceList_Rejected"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_E_InvoiceList_Rejected"].BringToFront();
            }
        }

        private void mi_ST_Store_Click(object sender, EventArgs e)
        {
            OpenST_Store();
        }
        private void OpenST_Store()
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_ST_Store)))
            {
                frm_ST_Store frm_st_Store = new frm_ST_Store();
                frm_st_Store.BringToFront();
                frm_st_Store.Icon = icon;
                frm_st_Store.ShowDialog();

                //load user settings
                int revAcc = 0;
                int expAcc = 0;
                HelperAcc.SettingsAccounts = HelperAcc.Get_Accounts(out revAcc, out expAcc);
                HelperAcc.ExpensesAcc = expAcc;
                HelperAcc.RevenuesAcc = revAcc;

                Shared.st_Store = MyHelper.GetStoreSettings(out Shared.lstCurrency, out Shared.lstIncomeTaxDiscount, out Shared.lstIncomeTaxLevel);
                Shared.ReportsPath = ErpUtils.GetReportsPath(Shared.st_Store);
                if (Shared.st_Store.UseHeightDimension == true ||
                    Shared.st_Store.UseWidthDimension == true ||
                    Shared.st_Store.UseLengthDimension == true)
                    Shared.DimensionsAvailable = true;

            }
            else
            {
                if (Application.OpenForms["frm_ST_Store"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_ST_Store"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_ST_Store"].BringToFront();
            }
        }



        private void E_taxableTypes_Click(object sender, EventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_E_TaxableTypes)))
            {
                frm_E_TaxableTypes f = new frm_E_TaxableTypes();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_E_TaxableTypes"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_E_TaxableTypes"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_E_TaxableTypes"].BringToFront();
            }
        }

        private void E_activityTypes_Click(object sender, EventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_E_ActivityTypes)))
            {
                frm_E_ActivityTypes f = new frm_E_ActivityTypes();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_E_ActivityTypes"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_E_ActivityTypes"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_E_ActivityTypes"].BringToFront();
            }
        }

        private void E_storesCodes_Click(object sender, EventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_E_StoreCode)))
            {
                frm_E_StoreCode f = new frm_E_StoreCode();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_E_StoreCode"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_E_StoreCode"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_E_StoreCode"].BringToFront();
            }

        }

        private void releaseNotesToolStripMenuItem_Click(object sender, EventArgs e)
        {
            Get_Dongle_SN();
            //SignWithThisCert(cert);
        }

        private static void Get_Dongle_SN()
        {
            try
            {
                ERPDataContext db = new ERPDataContext();
                X509Store store = new X509Store(StoreLocation.CurrentUser);
                store.Open(OpenFlags.OpenExistingOnly | OpenFlags.ReadOnly);
                X509Certificate2 cert = null;
                //manually chose the certificate in the store
                X509Certificate2Collection sel = X509Certificate2UI.SelectFromCollection(store.Certificates, null, null, X509SelectionFlag.MultiSelection);
                if (sel.Count == 0)
                    MyHelper.UpdateST_UserLog(db, DateTime.Now.ToString(), "empty sel",
                                    (int)FormAction.None, (int)FormsNames.Company);
                else
                {
                    //foreach(var sr in sel)
                    //    MessageBox.Show("serialNumber: "+ sr.SerialNumber);
                    foreach (var s in sel)
                    {
                        Utilities.save_Log("FriendlyName: "+ s.FriendlyName, null);
                        Utilities.save_Log("GetIssuerName: " + s.GetIssuerName(), null);
                        Utilities.save_Log("GetName: " + s.GetName(), null);
                        Utilities.save_Log("GetNameInfo: " + s.GetNameInfo(  X509NameType.SimpleName,true), null);
                        Utilities.save_Log("GetPublicKeyString: " + s.GetPublicKeyString(), null);
                        Utilities.save_Log("GetSerialNumberString: " + s.GetSerialNumberString(), null);
                        Utilities.save_Log("Issuer: " + s.Issuer, null);
                        Utilities.save_Log("IssuerName.Name: " + s.IssuerName.Name, null);
                        //Utilities.save_Log("PrivateKey.ToXmlString: " + s.PrivateKey.ToXmlString(true), null);
                        Utilities.save_Log("PublicKey: " + s.PublicKey.Key.ToString(), null);
                        Utilities.save_Log("SerialNumber: " + s.SerialNumber, null);
                        Utilities.save_Log("Subject: " + s.Subject, null);
                        Utilities.save_Log("SubjectName: " + s.SubjectName, null);
                    }
                    Shared.Dongle_SN = db.ST_CompanyInfos.Select(x => x.TokenSerial).FirstOrDefault();// sel[0].SerialNumber;
                    //MessageBox.Show(sel[0].SerialNumber);
                    MyHelper.UpdateST_UserLog(db, Shared.Dongle_SN, "تم قراءة الدونجل بنجاح",
                                    (int)FormAction.None, (int)FormsNames.Dongle_SN);
                    db.SubmitChanges();
                    //Utilities.save_Log(DateTime.Now.ToString(), null);
                    //foreach (var s in sel)
                    //{
                    //    Utilities.save_Log(s.ToString(), null);
                    //    Utilities.save_Log("PubK :" + s.GetPublicKeyString(), null);
                    //    Utilities.save_Log("SerialNumber :" + s.SerialNumber, null);
                    //    Utilities.save_Log("HasPrivateKey :" + s.HasPrivateKey, null);

                    //    Utilities.save_Log("================", null);
                    //}
                    //MessageBox.Show("Certificate not found");
                    //return;
                }
            }
            catch (Exception exc)
            {
                Utilities.save_Log(exc.Message, exc);
            }
        }

        private void E_TaxableSubtypes_Click(object sender, EventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_E_TaxableSubtypes)))
            {
                frm_E_TaxableSubtypes f = new frm_E_TaxableSubtypes();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_E_TaxableSubtypes"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_E_TaxableSubtypes"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_E_TaxableSubtypes"].BringToFront();
            }
        }

        private void mi_unSyncedRtrnInces_Click(object sender, EventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_DebitNotesList)))
            {
                frm_DebitNotesList f = new frm_DebitNotesList();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_DebitNotesList"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_DebitNotesList"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_DebitNotesList"].BringToFront();
            }
        }

        private void mi_SL_Add_Click(object sender, EventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_SL_Add)))
            {
                frm_SL_Add f = new frm_SL_Add();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_SL_Add"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_SL_Add"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_SL_Add"].BringToFront();
            }
        }

        private void mi_SL_AddList_Click(object sender, EventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_SL_AddList)))
            {
                frm_SL_AddList f = new frm_SL_AddList();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_SL_AddList"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_SL_AddList"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_SL_AddList"].BringToFront();
            }

        }

        private void mi_UnSyncAddNotes_Click(object sender, EventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_AddNotesList)))
            {
                frm_AddNotesList f = new frm_AddNotesList();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_AddNotesList"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_AddNotesList"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_AddNotesList"].BringToFront();
            }

        }

        private void mi_ShowHideChart_Click(object sender, EventArgs e)
        {
            if (Shared.ShowMainChart_)
            {
                Shared.ShowMainChart_ = false;
                Show(picLogo.Image != null, false);
            }
            else
            {
                Shared.ShowMainChart_ = true;
                Show(picLogo.Image != null, true);
            }
        }

        private void recievedDocuments_Click(object sender, EventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_E_RecievedInvoices)))
            {
                frm_E_RecievedInvoices f = new frm_E_RecievedInvoices();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_E_RecievedInvoices"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_E_RecievedInvoices"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_E_RecievedInvoices"].BringToFront();
            }
        }

        private void mi_E_InvoiceSettings_Click(object sender, EventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_ST_E_InvoiceInfo)))
            {
                frm_ST_E_InvoiceInfo f = new frm_ST_E_InvoiceInfo();
                f.BringToFront();
                f.Show();
            }
            else
            {
                if (Application.OpenForms["frm_ST_E_InvoiceInfo"].WindowState == FormWindowState.Minimized)
                    Application.OpenForms["frm_ST_E_InvoiceInfo"].WindowState = FormWindowState.Normal;

                Application.OpenForms["frm_ST_E_InvoiceInfo"].BringToFront();
            }
        }
    }
}


//private void multiToolStripMenuItem_Click(bool isPay, IsVendor isVendor, int dealerId, byte? processId, int? sourceId)
//{
//    if (!ErpUtils.IsFormOpen(typeof(frm_ACC_CashNote)))
//    {
//        if (dealerId == 0)
//        {
//            frm_ACC_CashNote_MultiSource f = new frm_ACC_CashNote_MultiSource(isPay, 0, FormAction.Add);
//            f.BringToFront();
//            f.Show();
//        }
//        else
//        {
//            frm_ACC_CashNote_MultiSource f = new frm_ACC_CashNote_MultiSource(isPay, isVendor, dealerId, processId, sourceId);
//            f.BringToFront();
//            f.Show();
//        }
//    }
//    else
//    {
//        if (isPay && Application.OpenForms["frm_ACC_CashNote_MultiSource"].Text ==
//            (Shared.IsEnglish == true ? ResEn.mi_NewPayNote : ResAr.mi_NewPayNote))//"سند دفع نقدي")
//            Application.OpenForms["frm_ACC_CashNote_MultiSource"].BringToFront();
//        else if (!isPay && Application.OpenForms["frm_ACC_CashNote_MultiSource"].Text ==
//            (Shared.IsEnglish == true ? ResEn.mi_NewReceiveNote : ResAr.mi_NewReceiveNote))//"سند قبض نقدي")
//            Application.OpenForms["frm_ACC_CashNote_MultiSource"].BringToFront();
//        else if (isPay && Application.OpenForms["frm_ACC_CashNote_MultiSource"].Text !=
//            (Shared.IsEnglish == true ? ResEn.mi_NewPayNote : ResAr.mi_NewPayNote)//"سند دفع نقدي"
//              || !isPay && Application.OpenForms["frm_ACC_CashNote_MultiSource"].Text !=
//            (Shared.IsEnglish == true ? ResEn.mi_NewReceiveNote : ResAr.mi_NewReceiveNote))//"سند قبض نقدي")
//        {
//            Application.OpenForms["frm_ACC_CashNote_MultiSource"].Close();
//            if (dealerId == 0)
//            {
//                frm_ACC_CashNote_MultiSource f = new frm_ACC_CashNote_MultiSource(isPay, 0, FormAction.Add);
//                f.BringToFront();
//                f.Show();
//            }
//            else
//            {
//                frm_ACC_CashNote_MultiSource f = new frm_ACC_CashNote_MultiSource(isPay, isVendor, dealerId, processId, sourceId);
//                f.BringToFront();
//                f.Show();
//            }
//        }
//        else
//        {
//            if (dealerId == 0)
//            {
//                frm_ACC_CashNote_MultiSource f = new frm_ACC_CashNote_MultiSource(isPay, 0, FormAction.Add);
//                f.BringToFront();
//                f.Show();
//            }
//            else
//            {
//                frm_ACC_CashNote_MultiSource f = new frm_ACC_CashNote_MultiSource(isPay, isVendor, dealerId, null, null);
//                f.BringToFront();
//                f.Show();
//            }
//        }
//    }


//}

