﻿using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using DAL;
using System.Linq;
using System.Data;
using DevExpress.XtraEditors;

using System.Windows.Forms;

namespace Reports
{
    public partial class rpt_SL_Invoice : DevExpress.XtraReports.UI.XtraReport
    {
        string customer, serial, number, date, store, paymethod, drawer, notes,
            total, taxR, taxV, discountR, discountV, expensesR, expensesV, net, paied, remains, userName, SalesEmp,
            Shipping, PurchaseOrderNo, DeliverDate, salesEmp_Job, DeductTaxV, AddTaxV,
            BalanceBefore, BalanceAfter, DriverName, VehicleNumber, Destination, ProcessName, SourceCode, scalWeightSerial,
            CusTaxV, CostCenter, retention, advancepayment, dueDate, TaxFileNumber, TaxCardNumber, totalPieces, Delivery
          , address, tel, managerName, AttnMr,
            total_b4_Discounts, CommercialDiscounts, totalAfterCommercial_Disc, TotalETax, Total_BounsDiscount;

        DataTable dt_Weights;
        DataTable dt_subTaxes;
        //private void xrSubreport2_BeforePrint(object sender, System.Drawing.Printing.PrintEventArgs e)
        //{
        //    if (dt_Weights.Rows.Count > 0)
        //        xrSubreport2.ReportSource = new rpt_multiple_weights(dt_Weights);
        //}

        int CustomerId = 0;

        DataTable dt_inv_details;
        int currId;
        decimal[] trans_hand;

        public rpt_SL_Invoice()
        {
            InitializeComponent();
        }
        public rpt_SL_Invoice(string _customer, string _serial, string _number, string _date, string _store, string _paymethod,
            string _drawer, string _notes, string _total, string _taxR, string _taxV, string _discountR, string _discountV,
            string _expensesR, string _expensesV, string _net, string _paied, string _remains, DataTable dt, string userName,
            string _salesEmp, string _Shipping, string _PurchaseOrderNo, string _DeliverDate, string _salesEmp_Job, string _DeductTaxV,
            string _AddTaxV, int _currId, string _BalanceBefore, string _BalanceAfter, string DriverName, string VehicleNumber,
            string Destination, string ProcessName, string SourceCode, string scalWeightSerial, string _CusR, string _CusV,
            string _CostCenter, string _Retention, string _AdvancePayment, string _dueDate, int CustomerId, decimal[] trans_hand,
            string _totalPieces, string _address, string _tel, string _managerName, string AttnMr,
            string total_b4_Discounts, string CommercialDiscounts, string totalAfterCommercial_Disc, string TotalETax , DataTable dt_PrintTableSubTaxDetails,string totalBounsDiscount)
        {
            InitializeComponent();
            customer = _customer;
            serial = _serial;
            number = _number;
            date = _date;
            store = _store;
            paymethod = _paymethod;
            drawer = _drawer;
            notes = _notes;
            total = _total;
            taxR = _taxR;
            taxV = _taxV;
            CusTaxV = _CusV;
            discountR = _discountR;
            discountV = _discountV;
            expensesR = _expensesR;
            expensesV = _expensesV;
            net = _net;
            paied = _paied;
            remains = _remains;
            this.userName = userName;
            SalesEmp = _salesEmp;
            salesEmp_Job = _salesEmp_Job;

            Shipping = _Shipping;
            PurchaseOrderNo = _PurchaseOrderNo;
            DeliverDate = _DeliverDate;
            dueDate = _dueDate; ;
            DeductTaxV = _DeductTaxV;
            AddTaxV = _AddTaxV;
            CostCenter = _CostCenter;
            retention = _Retention;
            advancepayment = _AdvancePayment;
            this.DriverName = DriverName;
            this.VehicleNumber = VehicleNumber;
            this.Destination = Destination;
            this.ProcessName = ProcessName;
            this.SourceCode = SourceCode;
            this.scalWeightSerial = scalWeightSerial;
            this.AttnMr = AttnMr;

            this.total_b4_Discounts = total_b4_Discounts;
            this.CommercialDiscounts = CommercialDiscounts;
            this.totalAfterCommercial_Disc = totalAfterCommercial_Disc;
            this.TotalETax = TotalETax;

            currId = _currId;
            this.CustomerId = CustomerId;

            BalanceBefore = _BalanceBefore;
            BalanceAfter = _BalanceAfter;

            this.trans_hand = trans_hand;

            dt_inv_details = dt;
            dt_subTaxes = dt_PrintTableSubTaxDetails;
            //this.DataSource = dt_inv_details;
            totalPieces = _totalPieces;
            //======samar
            address = _address;
            tel = _tel;
            managerName = _managerName;
            //dt_Weights = _dt_Weights;
            Total_BounsDiscount = totalBounsDiscount;
            getReportHeader();

            //LoadData();            
        }


        public rpt_SL_Invoice(string _customer, string _TaxCardNumber, string _TaxFileNumber, string _serial, string _number, string _date, string _store, string _paymethod,
            string _drawer, string _notes, string _total, string _taxR, string _taxV, string _discountR, string _discountV,
            string _expensesR, string _expensesV, string _net, string _paied, string _remains, DataTable dt, string userName,
            string _salesEmp, string _Shipping, string _PurchaseOrderNo, string _DeliverDate, string _salesEmp_Job, string _DeductTaxV,
            string _AddTaxV, int _currId, string _BalanceBefore, string _BalanceAfter, string DriverName, string VehicleNumber,
            string Destination, string ProcessName, string SourceCode, string scalWeightSerial, string _CusR, string _CusV,
            string _CostCenter, string _Retention, string _AdvancePayment, string _dueDate, int CustomerId, decimal[] trans_hand, string _totalPieces, DataTable _dt_Weights,
            string _Delivery, string _address, string _tel, string _managerName, string txt_AttnMr,
            string total_b4_Discounts, string totalAfterCommercial_Disc, string commercialDiscount, string TotalETax, DataTable dt_PrintTableSubTaxDetails, string totalBounsDiscount)
        {
            InitializeComponent();
            customer = _customer;
            TaxCardNumber = _TaxCardNumber;
            TaxFileNumber = _TaxFileNumber;
            serial = _serial;
            number = _number;
            date = _date;
            store = _store;
            paymethod = _paymethod;
            drawer = _drawer;
            notes = _notes;
            total = _total;
            taxR = _taxR;
            taxV = _taxV;
            CusTaxV = _CusV;
            discountR = _discountR;
            discountV = _discountV;
            expensesR = _expensesR;
            expensesV = _expensesV;
            net = _net;
            paied = _paied;
            remains = _remains;
            this.userName = userName;
            SalesEmp = _salesEmp;
            salesEmp_Job = _salesEmp_Job;

            dt_Weights = _dt_Weights;

            Shipping = _Shipping;
            PurchaseOrderNo = _PurchaseOrderNo;
            DeliverDate = _DeliverDate;
            dueDate = _dueDate; ;
            DeductTaxV = _DeductTaxV;
            AddTaxV = _AddTaxV;
            CostCenter = _CostCenter;
            retention = _Retention;
            advancepayment = _AdvancePayment;
            this.DriverName = DriverName;
            this.VehicleNumber = VehicleNumber;
            this.Destination = Destination;
            this.ProcessName = ProcessName;
            this.SourceCode = SourceCode;
            this.scalWeightSerial = scalWeightSerial;

            currId = _currId;
            this.CustomerId = CustomerId;

            BalanceBefore = _BalanceBefore;
            BalanceAfter = _BalanceAfter;
            Total_BounsDiscount = totalBounsDiscount;
            this.AttnMr = txt_AttnMr;

            this.total_b4_Discounts = total_b4_Discounts;
            this.CommercialDiscounts = commercialDiscount;
            this.totalAfterCommercial_Disc = totalAfterCommercial_Disc;
            this.TotalETax = TotalETax;

            this.trans_hand = trans_hand;
            this.Delivery = _Delivery;
            dt_inv_details = dt;
            dt_subTaxes = dt_PrintTableSubTaxDetails;
            //this.DataSource = dt_inv_details;
            totalPieces = _totalPieces;
            getReportHeader();
            //======samar
            address = _address;
            tel = _tel;
            managerName = _managerName;
            //LoadData(); 
          // SubTaxDetails.DataSource = dt_PrintTableSubTaxDetails;
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;
            if (comp != null)
            {
                lblCompName.Text = Shared.IsEnglish ? comp.CmpNameEn : comp.CmpNameAr;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        public void LoadData()
        {
            lbl_date.Text = date;
            lbl_DiscountR.Text = discountR;
            lbl_DiscountV.Text = discountV;
            lbl_Drawer.Text = drawer;
            lbl_ExpensesR.Text = expensesR;
            lbl_ExpensesV.Text = expensesV;
            lbl_Net.Text = net;
            lbl_notes.Text = notes;
            lbl_Number.Text = number;
            xrBarCode_Voucher.Text = number;
            lbl_Paied.Text = paied;
            lbl_Paymethod.Text = paymethod;
            lbl_Remains.Text = remains;
            lbl_Serial.Text = serial;
            lbl_store.Text = store;
            lbl_TaxR.Text = taxR;
            lbl_TaxV.Text = taxV;
            lbl_CusTaxV.Text = CusTaxV;
            lbl_Total.Text = total;
            lbl_Customer.Text = customer;
            lbl_TaxCardNumber.Text = TaxCardNumber;
            lbl_TaxFileNumber.Text = TaxFileNumber;
            lbl_User.Text = userName;
            lbl_DeductTaxV.Text = DeductTaxV;
            lbl_BounsDiscount.Text = AddTaxV;

            lbl_SalesEmp.Text = SalesEmp;
            lbl_salesEmp_Job.Text = salesEmp_Job;

            txtDelivery.Text = Delivery;
            lbl_DeliverDate.Text = DeliverDate;
            lbl_Shipping.Text = Shipping;
            lbl_PurchaseOrderNo.Text = PurchaseOrderNo;
            lbl_BalanceBefore.Text = BalanceBefore;
            lbl_BalanceAfter.Text = BalanceAfter;

            lbl_DriverName.Text = DriverName;
            lbl_VehicleNumber.Text = VehicleNumber;
            lbl_Destination.Text = Destination;
            lbl_ScaleWeightSerial.Text = scalWeightSerial;

            lbl_ProcessName.Text = ProcessName;
            lbl_SourceCode.Text = SourceCode;
            txt_advancepayment.Text = advancepayment;
            txt_retention.Text = retention;
            //samar
            //======samar
            Address.Text = address;
            Tel.Text = tel;
            ManagerName.Text = managerName;
            txt_AttnMr.Text = AttnMr;

            //====================//

            lbl_total_b4_Discounts_Taxes.Text = total_b4_Discounts;
            lbl_commercialDiscount.Text = CommercialDiscounts;
            lbl_totalAfterCommercial_Disc.Text = totalAfterCommercial_Disc;
            lbl_TotalETax.Text = TotalETax;
            lbl_BounsDiscount.Text = Total_BounsDiscount;
            if (trans_hand.Count() > 0)
                lbl_trans.Text = trans_hand[0].ToString();
            if (trans_hand.Count() > 1)
                lbl_Handing.Text = trans_hand[1].ToString();

            if (trans_hand.Count() > 2)
                lblShift.Text = trans_hand[2].ToString();

            // Adel DueDate 18/03/2020
            lbl_DueDate.Text = dueDate;
            try
            {
                lblSubTotal.Text = (Convert.ToDecimal(lbl_Total.Text) - Convert.ToDecimal(lbl_DiscountV.Text)).ToString();
            }
            catch { }
            lblTotalWords.Text = Shared.IsEnglish ? HelperAcc.ConvertMoneyToText(net, currId, Shared.lstCurrency) :
                                   HelperAcc.ConvertMoneyToArabicText(net, currId, Shared.lstCurrency);

            DetailReport.DataSource = dt_inv_details;

            ERPDataContext db = new ERPDataContext();
            var _customer = db.SL_Customers.Where(x => x.CustomerId == CustomerId).FirstOrDefault();
            if (_customer != null)
            {
                lbl_Cust_Address.Text = _customer.Address;
                lbl_Cust_Tel.Text = _customer.Tel;
                lbl_Cust_Mobile.Text = _customer.Mobile;
                lbl_Neighborhood.Text = _customer.Neighborhood;
                lbl_Street.Text = _customer.Street;
            }

            string updated = db.SL_Invoices.FirstOrDefault(s => s.InvoiceCode == number).LastUpdateDate != null ? "معدل" : "";
            lbl_Updated.Text = updated;

            decimal totalQty = 0;
            decimal totalP = 0;
            decimal total_Packs = 0;
            //totalQty = dt_inv_details.Compute("Sum(Qty)", string.Empty);
            foreach (DataRow row in dt_inv_details.Rows)
            {
                totalQty += Convert.ToDecimal(row["Qty"]);
                try
                {
                    if (Convert.ToBoolean(row["IsOffer"]) == true)
                        continue;
                }
                catch { }
                totalP += Convert.ToDecimal(row["PiecesCount"]);
                if (row["Pack"] != null && row["Pack"] != DBNull.Value && row["Pack"].ToString() != "")
                    total_Packs += Convert.ToDecimal(row["Pack"]);
            }
            //===================================================//
            var companyName = "";
            var CompanyTaxNumber = "";
            var company = db.ST_CompanyInfos.Where(a => a.Company_Id == Shared.st_comp.Company_Id).FirstOrDefault();
            if (company != null)
            {
                companyName = Shared.IsEnglish ? company.CmpNameEn : company.CmpNameAr;
                CompanyTaxNumber = company.TaxCard;
                lbl_CompanyTaxNumber.Text = company.TaxCard;
                lbl_CommercialBook.Text = company.CommercialBook;
            }
            qrCode.Text =
                "Seller Name     : " + companyName + System.Environment.NewLine +
                "Tax Reg. Number : " + CompanyTaxNumber + System.Environment.NewLine +
                "Customer        : " + _customer.CusNameAr + System.Environment.NewLine +
                "Customer Tax No.: " + _customer.TaxCardNumber + System.Environment.NewLine +
                "Invoice Date    : " + Convert.ToDateTime(date).ToShortDateString() + System.Environment.NewLine +
                "Total Amount    : " + total + System.Environment.NewLine +
                "VAT             : " + TotalETax + System.Environment.NewLine +
                "Net             : " + net;

            // qrCode.Text = GenerateQRCode(companyName, CompanyTaxNumber, Convert.ToDateTime(date), Convert.ToDecimal(total), Convert.ToDecimal(AddTaxV));

            //===================================================//

            //qrCode.Alignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            //if (_customer.csType == 0)//===========طبيعي
            {
                //txtCardNum.Visible = false;
                //txtTaxCardNumber.Visible = false;

                //txtFileNum.Visible = false;
                //txtTaxFileNumber.Visible = false;
                //======================//
                Regs_ID.Text = _customer.Rep_ID;
            }
            //else if(_customer.csType == 1)//======اعتباري
            {
                //txtregisterId.Visible = false;
                //Regs_ID.Visible = false;
                //=================//
                txtTaxCardNumber.Text = _customer.TaxCardNumber;
                txtTaxFileNumber.Text = _customer.TaxFileNumber;

            }
            lbl_Customer_City.Text = _customer.City;
            lbl_Customer_ZIP.Text = _customer.Zip;
            //else
            //{
            //    txtCardNum.Visible = false;
            //    txtTaxCardNumber.Visible = false;

            //    txtFileNum.Visible = false;
            //    txtTaxFileNumber.Visible = false;

            //    txtregisterId.Visible = false;
            //    Regs_ID.Visible = false;
            //}
            //====================================================//
            DetailReport.DataSource = dt_inv_details;
            lbl_TotalQty.Text = totalQty.ToString();
            lbl_totalPieces.Text = totalP.ToString();
            lbl_TotalPacks.Text = total_Packs.ToString();
            lbl_total_after_tax.Text = Convert.ToString(Convert.ToDecimal(total) + Convert.ToDecimal(AddTaxV));
            cell_code.DataBindings.Add("Text", DetailReport.DataSource, "ItemCode1");
            cell_code2.DataBindings.Add("Text", DetailReport.DataSource, "ItemCode2");
            cell_Disc.DataBindings.Add("Text", DetailReport.DataSource, "DiscountValue");
            cell_Expire.DataBindings.Add("Text", DetailReport.DataSource, "Expire");
            cell_EtaxValue.DataBindings.Add("Text", DetailReport.DataSource, "Batch");
            cell_Price.DataBindings.Add("Text", DetailReport.DataSource, "SellPrice");
            cell_Qty.DataBindings.Add("Text", DetailReport.DataSource, "Qty");
            cell_Total.DataBindings.Add("Text", DetailReport.DataSource, "TotalSellPrice");
            cell_ItemName.DataBindings.Add("Text", DetailReport.DataSource, "ItemName");
            cell_UOM.DataBindings.Add("Text", DetailReport.DataSource, "UOM");

            cell_Height.DataBindings.Add("Text", DetailReport.DataSource, "Height");
            cell_Width.DataBindings.Add("Text", DetailReport.DataSource, "Width");
            cell_Length.DataBindings.Add("Text", DetailReport.DataSource, "Length");
            cell_TotalQty.DataBindings.Add("Text", DetailReport.DataSource, "TotalQty");
            cell_ItemDescription.DataBindings.Add("Text", DetailReport.DataSource, "ItemDescription");
            cell_AudiencePrice.DataBindings.Add("Text", DetailReport.DataSource, "AudiencePrice");
            cell_Factor.DataBindings.Add("Text", DetailReport.DataSource, "Factor");
            Cell_MUOM.DataBindings.Add("Text", DetailReport.DataSource, "MUOM");
            Cell_MUOM_Factor.DataBindings.Add("Text", DetailReport.DataSource, "MUOM_Factor");
            cell_DiscountRatio.DataBindings.Add("Text", DetailReport.DataSource, "DiscountRatio");
            cell_DiscountRatio2.DataBindings.Add("Text", DetailReport.DataSource, "DiscountRatio2");
            cell_DiscountRatio3.DataBindings.Add("Text", DetailReport.DataSource, "DiscountRatio3");
            cell_SalesTaxRatio.DataBindings.Add("Text", DetailReport.DataSource, "SalesTaxRatio");
            cell_SalesTax.DataBindings.Add("Text", DetailReport.DataSource, "SalesTax");
            //cell_Serial.DataBindings.Add("Text", this.DataSource, "Serial");
            cell_ManufactureDate.DataBindings.Add("Text", DetailReport.DataSource, "ManufactureDate");
            cell_PiecesCount.DataBindings.Add("Text", DetailReport.DataSource, "PiecesCount");
            cell_Weight_KG.DataBindings.Add("Text", DetailReport.DataSource, "kg_Weight_libra");
            cell_Serial.DataBindings.Add("Text", DetailReport.DataSource, "Index");
            cell_Location.DataBindings.Add("Text", DetailReport.DataSource, "Location");
            cell_Pack.DataBindings.Add("Text", DetailReport.DataSource, "Pack");
            cell_ItemDescriptionEn.DataBindings.Add("Text", DetailReport.DataSource, "ItemDescriptionEn");
            cell_SerialNo.DataBindings.Add("Text", DetailReport.DataSource, "Serial");
            cell_SerialNo2.DataBindings.Add("Text", DetailReport.DataSource, "Serial2");
            lbl_itemNameF.DataBindings.Add("Text", DetailReport.DataSource, "ItemIdF");
            cell_EtaxValue.DataBindings.Add("Text", DetailReport.DataSource, "EtaxValue");

            cell_addTaxValue.DataBindings.Add("Text", DetailReport.DataSource, "addTaxValue");
            cell_tableTaxValue.DataBindings.Add("Text", DetailReport.DataSource, "tableTaxValue");
            cell_bonusDiscount.DataBindings.Add("Text", DetailReport.DataSource, "bonusDiscount");

            //==============subTaxes=====================//
            SubTaxDetails.DataSource = dt_subTaxes;
            cell_subtaxId.DataBindings.Add("Text", SubTaxDetails.DataSource, "SubTaxId");
            cell_Value.DataBindings.Add("Text", SubTaxDetails.DataSource, "Value");
            cell_Rate.DataBindings.Add("Text", SubTaxDetails.DataSource, "Rate");
            

            var comp = Shared.st_comp;
            if (comp != null)
            {
                lblCompName.Text = Shared.IsEnglish ? comp.CmpNameEn : comp.CmpNameAr;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
            //this.pic_ItemPic.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
            //new DevExpress.XtraReports.UI.XRBinding("ImageUrl", this.DataSource, "PicPath")});

            //if (dt_Weights.Rows.Count > 0)
            //{
            //    xrSubreport2.ReportSource = new rpt_multiple_weights(dt_Weights);
            //    if (xrSubreport3.Visible == true)
            //    {
            //        var dt = dt_Weights.AsEnumerable().GroupBy(r => new { Col1 = r["item"] }).Select(g => g.First()).CopyToDataTable();
            //        xrSubreport3.ReportSource = new rpt_multiple_weights_dup(dt_Weights);
            //    }
            //}


            #region Print sum of Qty according to Category
            //var categories = db.IC_Categories;

            //var sums = from DataRow s in dt_inv_details.Rows
            //           group s by s["CategoryId"] into grp
            //           select new
            //           {
            //               Category = categories.Where(x => x.CategoryId == Convert.ToInt32(grp.Key)).
            //               Select(x => Shared.IsEnglish ? x.CategoryNameEn : x.CategoryNameAr).First(),
            //               Qtys = grp.Select(x => Convert.ToDouble(x["Qty"])).Sum(),
            //           };

            //DetailReport_TotalQtyPerCategory.DataSource = sums;

            ////grd.DataSource = sums;

            //cell_Qtys.DataBindings.Add("Text", DetailReport_TotalQtyPerCategory.DataSource, "Qtys");
            //cell_Category.DataBindings.Add("Text", DetailReport_TotalQtyPerCategory.DataSource, "Category");
            #endregion
        }
        public static string GenerateQRCode(string sellername, string vatregistration, DateTime timestamp, decimal invoiceamount, decimal vatAmount)
        {
            var seller = GetTVLvalue("1", sellername);
            var VATnumber = GetTVLvalue("2", vatregistration.ToString());
            var time = GetTVLvalue("3", timestamp.ToString("s") + "Z");
            var amount = GetTVLvalue("4", invoiceamount.ToString());
            var vatamt = GetTVLvalue("5", vatAmount.ToString());
            var result = seller.Concat(VATnumber).Concat(time).Concat(amount).Concat(vatamt).ToArray();

            var output = Convert.ToBase64String(result);

            return output;
        }
        public static byte[] GetTVLvalue(String tagnums, String tagvalue)
        {
            string[] tagnums_array = { tagnums };
            var tagvalue1 = tagvalue;

            var tagnum = tagnums_array.Select(s => Byte.Parse(s)).ToArray();



            var tagvalueb = System.Text.Encoding.UTF8.GetBytes(tagvalue1);
            string[] taglengths = { tagvalueb.Length.ToString() };
            var tagvaluelengths = taglengths.Select(s => Byte.Parse(s)).ToArray();
            var tlvVAlue = tagnum.Concat(tagvaluelengths).Concat(tagvalueb).ToArray();


            return tlvVAlue;
        }
    }
}
