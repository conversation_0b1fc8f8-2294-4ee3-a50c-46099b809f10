using System;
using System.Data;
using System.Data.Linq;
using System.Linq;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using DAL;

namespace Reports
{
    public partial class rpt_IC_Multiple_Weights_Sub : DevExpress.XtraReports.UI.XtraReport
    {
        public rpt_IC_Multiple_Weights_Sub(string item, DataTable dt)
        {
            InitializeComponent();
            
            DataTable dataTable = new DataTable();
            dataTable.Rows.Clear();
            dataTable.Columns.Clear();
            dataTable.Columns.Add("Weight");
            dataTable.Columns.Add("Count");

            foreach (DataRow row in dt.AsEnumerable().Where(x => x.Field<string>("item") == item).CopyToDataTable().Rows)
            {
                DataRow r = dataTable.NewRow();
                r["Weight"] = row["Weight"];
                r["Count"] = row["Count"];
                dataTable.Rows.Add(r);
            }

            this.DataSource = dataTable;
            lbl_Qty.DataBindings.Add("Text", DataSource, "Weight");
            lbl_PiecesCount.DataBindings.Add("Text", DataSource, "Count");
        }

    }
}
