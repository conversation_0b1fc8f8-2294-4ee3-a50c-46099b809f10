﻿namespace Reports
{
    partial class frm_Sl_JobOrderStatus
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_Sl_JobOrderStatus));
            this.barManager1 = new DevExpress.XtraBars.BarManager();
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtnPrint = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnPreview = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnRefresh = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController();
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.grdST_jobOrder = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_JOCode = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_CustomerId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_RegDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_SalesEmp = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Job = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_DeliveryDate = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Dept = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Status = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Priority = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_DeliveryEmp = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ArCaption = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_EnCaption = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_EnText = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ArText = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_UserName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_JobOrderId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_MtrxParent = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.repositoryItemGridLookUpEdit1View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_cat = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn3 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_comp = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_mtrx = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView4 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.rep_Vendor = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.gridView5 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repositoryItemMemoEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit();
            this.lblReportName = new DevExpress.XtraEditors.TextEdit();
            this.lblDateFilter = new DevExpress.XtraEditors.TextEdit();
            this.lblFilter = new DevExpress.XtraEditors.TextEdit();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdST_jobOrder)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_MtrxParent)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_cat)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_comp)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_mtrx)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Vendor)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblReportName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDateFilter.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFilter.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnPreview,
            this.barBtnClose,
            this.barBtnPrint,
            this.barBtnRefresh});
            this.barManager1.MaxItemId = 29;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(567, 147);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnPrint),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnPreview),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnRefresh),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtnPrint
            // 
            resources.ApplyResources(this.barBtnPrint, "barBtnPrint");
            this.barBtnPrint.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnPrint.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barBtnPrint.Id = 27;
            this.barBtnPrint.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.P));
            this.barBtnPrint.Name = "barBtnPrint";
            this.barBtnPrint.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnPrint.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnPrint_ItemClick);
            // 
            // barBtnPreview
            // 
            resources.ApplyResources(this.barBtnPreview, "barBtnPreview");
            this.barBtnPreview.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnPreview.Glyph = global::Pharmacy.Properties.Resources.srch;
            this.barBtnPreview.Id = 1;
            this.barBtnPreview.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.O));
            this.barBtnPreview.Name = "barBtnPreview";
            this.barBtnPreview.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnPreview.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Preview_ItemClick);
            // 
            // barBtnRefresh
            // 
            resources.ApplyResources(this.barBtnRefresh, "barBtnRefresh");
            this.barBtnRefresh.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnRefresh.Glyph = global::Pharmacy.Properties.Resources.refresh;
            this.barBtnRefresh.Id = 28;
            this.barBtnRefresh.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.R));
            this.barBtnRefresh.Name = "barBtnRefresh";
            this.barBtnRefresh.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnRefresh.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnRefresh_ItemClick);
            // 
            // barBtnClose
            // 
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 25;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Close_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlTop.Appearance.FontSizeDelta")));
            this.barDockControlTop.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlTop.Appearance.FontStyleDelta")));
            this.barDockControlTop.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlTop.Appearance.GradientMode")));
            this.barDockControlTop.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlTop.Appearance.Image")));
            this.barDockControlTop.CausesValidation = false;
            // 
            // barDockControlBottom
            // 
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlBottom.Appearance.FontSizeDelta")));
            this.barDockControlBottom.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlBottom.Appearance.FontStyleDelta")));
            this.barDockControlBottom.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlBottom.Appearance.GradientMode")));
            this.barDockControlBottom.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlBottom.Appearance.Image")));
            this.barDockControlBottom.CausesValidation = false;
            // 
            // barDockControlLeft
            // 
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlLeft.Appearance.FontSizeDelta")));
            this.barDockControlLeft.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlLeft.Appearance.FontStyleDelta")));
            this.barDockControlLeft.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlLeft.Appearance.GradientMode")));
            this.barDockControlLeft.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlLeft.Appearance.Image")));
            this.barDockControlLeft.CausesValidation = false;
            // 
            // barDockControlRight
            // 
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlRight.Appearance.FontSizeDelta")));
            this.barDockControlRight.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlRight.Appearance.FontStyleDelta")));
            this.barDockControlRight.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlRight.Appearance.GradientMode")));
            this.barDockControlRight.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlRight.Appearance.Image")));
            this.barDockControlRight.CausesValidation = false;
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemTextEdit1.Mask.AutoComplete")));
            this.repositoryItemTextEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.BeepOnError")));
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemTextEdit1.Mask.MaskType")));
            this.repositoryItemTextEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemTextEdit1.Mask.PlaceHolder")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // grdST_jobOrder
            // 
            resources.ApplyResources(this.grdST_jobOrder, "grdST_jobOrder");
            this.grdST_jobOrder.Cursor = System.Windows.Forms.Cursors.Default;
            this.grdST_jobOrder.EmbeddedNavigator.AccessibleDescription = resources.GetString("grdST_jobOrder.EmbeddedNavigator.AccessibleDescription");
            this.grdST_jobOrder.EmbeddedNavigator.AccessibleName = resources.GetString("grdST_jobOrder.EmbeddedNavigator.AccessibleName");
            this.grdST_jobOrder.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grdST_jobOrder.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grdST_jobOrder.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grdST_jobOrder.EmbeddedNavigator.Anchor")));
            this.grdST_jobOrder.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grdST_jobOrder.EmbeddedNavigator.BackgroundImage")));
            this.grdST_jobOrder.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grdST_jobOrder.EmbeddedNavigator.BackgroundImageLayout")));
            this.grdST_jobOrder.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grdST_jobOrder.EmbeddedNavigator.ImeMode")));
            this.grdST_jobOrder.EmbeddedNavigator.Margin = ((System.Windows.Forms.Padding)(resources.GetObject("grdST_jobOrder.EmbeddedNavigator.Margin")));
            this.grdST_jobOrder.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grdST_jobOrder.EmbeddedNavigator.MaximumSize")));
            this.grdST_jobOrder.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grdST_jobOrder.EmbeddedNavigator.TextLocation")));
            this.grdST_jobOrder.EmbeddedNavigator.ToolTip = resources.GetString("grdST_jobOrder.EmbeddedNavigator.ToolTip");
            this.grdST_jobOrder.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grdST_jobOrder.EmbeddedNavigator.ToolTipIconType")));
            this.grdST_jobOrder.EmbeddedNavigator.ToolTipTitle = resources.GetString("grdST_jobOrder.EmbeddedNavigator.ToolTipTitle");
            this.grdST_jobOrder.MainView = this.gridView1;
            this.grdST_jobOrder.Name = "grdST_jobOrder";
            this.grdST_jobOrder.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.rep_MtrxParent,
            this.rep_cat,
            this.rep_comp,
            this.rep_mtrx,
            this.rep_Vendor,
            this.repositoryItemMemoEdit1});
            this.grdST_jobOrder.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            this.grdST_jobOrder.Click += new System.EventHandler(this.grdST_jobOrder_Click);
            // 
            // gridView1
            // 
            this.gridView1.Appearance.FooterPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.FooterPanel.FontSizeDelta")));
            this.gridView1.Appearance.FooterPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.FooterPanel.FontStyleDelta")));
            this.gridView1.Appearance.FooterPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.FooterPanel.GradientMode")));
            this.gridView1.Appearance.FooterPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.FooterPanel.Image")));
            this.gridView1.Appearance.FooterPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.GroupPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.GroupPanel.FontSizeDelta")));
            this.gridView1.Appearance.GroupPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.GroupPanel.FontStyleDelta")));
            this.gridView1.Appearance.GroupPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.GroupPanel.GradientMode")));
            this.gridView1.Appearance.GroupPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.GroupPanel.Image")));
            this.gridView1.Appearance.GroupPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.GroupPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.GroupRow.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.GroupRow.FontSizeDelta")));
            this.gridView1.Appearance.GroupRow.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.GroupRow.FontStyleDelta")));
            this.gridView1.Appearance.GroupRow.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.GroupRow.GradientMode")));
            this.gridView1.Appearance.GroupRow.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.GroupRow.Image")));
            this.gridView1.Appearance.GroupRow.Options.UseTextOptions = true;
            this.gridView1.Appearance.GroupRow.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.HeaderPanel.FontSizeDelta")));
            this.gridView1.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.HeaderPanel.FontStyleDelta")));
            this.gridView1.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.HeaderPanel.GradientMode")));
            this.gridView1.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.HeaderPanel.Image")));
            this.gridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridView1.Appearance.HeaderPanel.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridView1.Appearance.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView1.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gridView1.Appearance.Row.FontSizeDelta")));
            this.gridView1.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.Appearance.Row.FontStyleDelta")));
            this.gridView1.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.Appearance.Row.GradientMode")));
            this.gridView1.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.Appearance.Row.Image")));
            this.gridView1.Appearance.Row.Options.UseTextOptions = true;
            this.gridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.Row.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridView1.Appearance.Row.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView1.AppearancePrint.FooterPanel.BackColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.BackColor")));
            this.gridView1.AppearancePrint.FooterPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.BorderColor")));
            this.gridView1.AppearancePrint.FooterPanel.Font = ((System.Drawing.Font)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.Font")));
            this.gridView1.AppearancePrint.FooterPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.FontSizeDelta")));
            this.gridView1.AppearancePrint.FooterPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.FontStyleDelta")));
            this.gridView1.AppearancePrint.FooterPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.ForeColor")));
            this.gridView1.AppearancePrint.FooterPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.GradientMode")));
            this.gridView1.AppearancePrint.FooterPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.FooterPanel.Image")));
            this.gridView1.AppearancePrint.FooterPanel.Options.UseBackColor = true;
            this.gridView1.AppearancePrint.FooterPanel.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.FooterPanel.Options.UseFont = true;
            this.gridView1.AppearancePrint.FooterPanel.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.GroupFooter.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.BorderColor")));
            this.gridView1.AppearancePrint.GroupFooter.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.FontSizeDelta")));
            this.gridView1.AppearancePrint.GroupFooter.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.FontStyleDelta")));
            this.gridView1.AppearancePrint.GroupFooter.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.ForeColor")));
            this.gridView1.AppearancePrint.GroupFooter.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.GradientMode")));
            this.gridView1.AppearancePrint.GroupFooter.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.GroupFooter.Image")));
            this.gridView1.AppearancePrint.GroupFooter.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.GroupFooter.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.GroupRow.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupRow.BorderColor")));
            this.gridView1.AppearancePrint.GroupRow.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.GroupRow.FontSizeDelta")));
            this.gridView1.AppearancePrint.GroupRow.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.GroupRow.FontStyleDelta")));
            this.gridView1.AppearancePrint.GroupRow.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.GroupRow.ForeColor")));
            this.gridView1.AppearancePrint.GroupRow.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.GroupRow.GradientMode")));
            this.gridView1.AppearancePrint.GroupRow.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.GroupRow.Image")));
            this.gridView1.AppearancePrint.GroupRow.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.GroupRow.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.BackColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.BackColor")));
            this.gridView1.AppearancePrint.HeaderPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.BorderColor")));
            this.gridView1.AppearancePrint.HeaderPanel.Font = ((System.Drawing.Font)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.Font")));
            this.gridView1.AppearancePrint.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.FontSizeDelta")));
            this.gridView1.AppearancePrint.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.FontStyleDelta")));
            this.gridView1.AppearancePrint.HeaderPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.ForeColor")));
            this.gridView1.AppearancePrint.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.GradientMode")));
            this.gridView1.AppearancePrint.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.HeaderPanel.Image")));
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseBackColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseFont = true;
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.AppearancePrint.HeaderPanel.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridView1.AppearancePrint.Lines.BackColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Lines.BackColor")));
            this.gridView1.AppearancePrint.Lines.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.Lines.FontSizeDelta")));
            this.gridView1.AppearancePrint.Lines.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.Lines.FontStyleDelta")));
            this.gridView1.AppearancePrint.Lines.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Lines.ForeColor")));
            this.gridView1.AppearancePrint.Lines.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.Lines.GradientMode")));
            this.gridView1.AppearancePrint.Lines.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.Lines.Image")));
            this.gridView1.AppearancePrint.Lines.Options.UseBackColor = true;
            this.gridView1.AppearancePrint.Lines.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.Row.BorderColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Row.BorderColor")));
            this.gridView1.AppearancePrint.Row.Font = ((System.Drawing.Font)(resources.GetObject("gridView1.AppearancePrint.Row.Font")));
            this.gridView1.AppearancePrint.Row.FontSizeDelta = ((int)(resources.GetObject("gridView1.AppearancePrint.Row.FontSizeDelta")));
            this.gridView1.AppearancePrint.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gridView1.AppearancePrint.Row.FontStyleDelta")));
            this.gridView1.AppearancePrint.Row.ForeColor = ((System.Drawing.Color)(resources.GetObject("gridView1.AppearancePrint.Row.ForeColor")));
            this.gridView1.AppearancePrint.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gridView1.AppearancePrint.Row.GradientMode")));
            this.gridView1.AppearancePrint.Row.Image = ((System.Drawing.Image)(resources.GetObject("gridView1.AppearancePrint.Row.Image")));
            this.gridView1.AppearancePrint.Row.Options.UseBorderColor = true;
            this.gridView1.AppearancePrint.Row.Options.UseFont = true;
            this.gridView1.AppearancePrint.Row.Options.UseForeColor = true;
            this.gridView1.AppearancePrint.Row.Options.UseTextOptions = true;
            this.gridView1.AppearancePrint.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.gridView1, "gridView1");
            this.gridView1.ColumnPanelRowHeight = 55;
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_JOCode,
            this.col_CustomerId,
            this.col_RegDate,
            this.col_SalesEmp,
            this.col_Job,
            this.col_DeliveryDate,
            this.col_Dept,
            this.col_Status,
            this.col_Priority,
            this.col_DeliveryEmp,
            this.col_ArCaption,
            this.col_EnCaption,
            this.col_EnText,
            this.col_ArText,
            this.col_UserName,
            this.col_JobOrderId,
            this.gridColumn11});
            this.gridView1.CustomizationFormBounds = new System.Drawing.Rectangle(730, 324, 208, 308);
            this.gridView1.GridControl = this.grdST_jobOrder;
            this.gridView1.GroupSummary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridGroupSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("gridView1.GroupSummary"))), resources.GetString("gridView1.GroupSummary1"), ((DevExpress.XtraGrid.Columns.GridColumn)(resources.GetObject("gridView1.GroupSummary2"))), resources.GetString("gridView1.GroupSummary3"))});
            this.gridView1.HorzScrollStep = 2;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.AllowAddRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView1.OptionsBehavior.AllowDeleteRows = DevExpress.Utils.DefaultBoolean.False;
            this.gridView1.OptionsBehavior.Editable = false;
            this.gridView1.OptionsPrint.ExpandAllGroups = false;
            this.gridView1.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView1.OptionsSelection.EnableAppearanceFocusedRow = false;
            this.gridView1.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView1.OptionsView.GroupFooterShowMode = DevExpress.XtraGrid.Views.Grid.GroupFooterShowMode.VisibleAlways;
            this.gridView1.OptionsView.RowAutoHeight = true;
            this.gridView1.OptionsView.ShowAutoFilterRow = true;
            this.gridView1.OptionsView.ShowFooter = true;
            this.gridView1.OptionsView.ShowIndicator = false;
            this.gridView1.RowClick += new DevExpress.XtraGrid.Views.Grid.RowClickEventHandler(this.gridView1_RowClick);
            this.gridView1.CustomUnboundColumnData += new DevExpress.XtraGrid.Views.Base.CustomColumnDataEventHandler(this.gridView1_CustomUnboundColumnData);
            this.gridView1.DoubleClick += new System.EventHandler(this.gridView1_DoubleClick);
            // 
            // col_JOCode
            // 
            resources.ApplyResources(this.col_JOCode, "col_JOCode");
            this.col_JOCode.FieldName = "JOCode";
            this.col_JOCode.Name = "col_JOCode";
            this.col_JOCode.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // col_CustomerId
            // 
            resources.ApplyResources(this.col_CustomerId, "col_CustomerId");
            this.col_CustomerId.FieldName = "CustomerId";
            this.col_CustomerId.Name = "col_CustomerId";
            this.col_CustomerId.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // col_RegDate
            // 
            resources.ApplyResources(this.col_RegDate, "col_RegDate");
            this.col_RegDate.FieldName = "RegDate";
            this.col_RegDate.Name = "col_RegDate";
            this.col_RegDate.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            // 
            // col_SalesEmp
            // 
            resources.ApplyResources(this.col_SalesEmp, "col_SalesEmp");
            this.col_SalesEmp.FieldName = "SalesEmp";
            this.col_SalesEmp.Name = "col_SalesEmp";
            this.col_SalesEmp.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.False;
            // 
            // col_Job
            // 
            resources.ApplyResources(this.col_Job, "col_Job");
            this.col_Job.FieldName = "Job";
            this.col_Job.Name = "col_Job";
            this.col_Job.OptionsColumn.AllowGroup = DevExpress.Utils.DefaultBoolean.True;
            // 
            // col_DeliveryDate
            // 
            resources.ApplyResources(this.col_DeliveryDate, "col_DeliveryDate");
            this.col_DeliveryDate.FieldName = "DeliveryDate";
            this.col_DeliveryDate.Name = "col_DeliveryDate";
            // 
            // col_Dept
            // 
            resources.ApplyResources(this.col_Dept, "col_Dept");
            this.col_Dept.FieldName = "Dept";
            this.col_Dept.Name = "col_Dept";
            // 
            // col_Status
            // 
            resources.ApplyResources(this.col_Status, "col_Status");
            this.col_Status.FieldName = "Status";
            this.col_Status.Name = "col_Status";
            // 
            // col_Priority
            // 
            resources.ApplyResources(this.col_Priority, "col_Priority");
            this.col_Priority.FieldName = "Priority";
            this.col_Priority.Name = "col_Priority";
            // 
            // col_DeliveryEmp
            // 
            resources.ApplyResources(this.col_DeliveryEmp, "col_DeliveryEmp");
            this.col_DeliveryEmp.FieldName = "DeliveryEmp";
            this.col_DeliveryEmp.FilterMode = DevExpress.XtraGrid.ColumnFilterMode.DisplayText;
            this.col_DeliveryEmp.Name = "col_DeliveryEmp";
            // 
            // col_ArCaption
            // 
            resources.ApplyResources(this.col_ArCaption, "col_ArCaption");
            this.col_ArCaption.FieldName = "ArCaption";
            this.col_ArCaption.Name = "col_ArCaption";
            // 
            // col_EnCaption
            // 
            resources.ApplyResources(this.col_EnCaption, "col_EnCaption");
            this.col_EnCaption.FieldName = "EnCaption";
            this.col_EnCaption.Name = "col_EnCaption";
            // 
            // col_EnText
            // 
            resources.ApplyResources(this.col_EnText, "col_EnText");
            this.col_EnText.FieldName = "EnText";
            this.col_EnText.Name = "col_EnText";
            // 
            // col_ArText
            // 
            resources.ApplyResources(this.col_ArText, "col_ArText");
            this.col_ArText.FieldName = "ArText";
            this.col_ArText.Name = "col_ArText";
            // 
            // col_UserName
            // 
            resources.ApplyResources(this.col_UserName, "col_UserName");
            this.col_UserName.FieldName = "UserName";
            this.col_UserName.Name = "col_UserName";
            // 
            // col_JobOrderId
            // 
            resources.ApplyResources(this.col_JobOrderId, "col_JobOrderId");
            this.col_JobOrderId.FieldName = "JobOrderId";
            this.col_JobOrderId.Name = "col_JobOrderId";
            // 
            // gridColumn11
            // 
            resources.ApplyResources(this.gridColumn11, "gridColumn11");
            this.gridColumn11.FieldName = "Customer";
            this.gridColumn11.Name = "gridColumn11";
            this.gridColumn11.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // rep_MtrxParent
            // 
            resources.ApplyResources(this.rep_MtrxParent, "rep_MtrxParent");
            this.rep_MtrxParent.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_MtrxParent.Buttons"))))});
            this.rep_MtrxParent.Name = "rep_MtrxParent";
            this.rep_MtrxParent.View = this.repositoryItemGridLookUpEdit1View;
            // 
            // repositoryItemGridLookUpEdit1View
            // 
            resources.ApplyResources(this.repositoryItemGridLookUpEdit1View, "repositoryItemGridLookUpEdit1View");
            this.repositoryItemGridLookUpEdit1View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn1,
            this.gridColumn2});
            this.repositoryItemGridLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.repositoryItemGridLookUpEdit1View.Name = "repositoryItemGridLookUpEdit1View";
            this.repositoryItemGridLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn1
            // 
            resources.ApplyResources(this.gridColumn1, "gridColumn1");
            this.gridColumn1.FieldName = "ItemNameAr";
            this.gridColumn1.Name = "gridColumn1";
            // 
            // gridColumn2
            // 
            resources.ApplyResources(this.gridColumn2, "gridColumn2");
            this.gridColumn2.FieldName = "ItemId";
            this.gridColumn2.Name = "gridColumn2";
            // 
            // rep_cat
            // 
            resources.ApplyResources(this.rep_cat, "rep_cat");
            this.rep_cat.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_cat.Buttons"))))});
            this.rep_cat.Name = "rep_cat";
            this.rep_cat.View = this.gridView2;
            // 
            // gridView2
            // 
            resources.ApplyResources(this.gridView2, "gridView2");
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn3,
            this.gridColumn4});
            this.gridView2.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn3
            // 
            resources.ApplyResources(this.gridColumn3, "gridColumn3");
            this.gridColumn3.FieldName = "CategoryNameAr";
            this.gridColumn3.Name = "gridColumn3";
            // 
            // gridColumn4
            // 
            resources.ApplyResources(this.gridColumn4, "gridColumn4");
            this.gridColumn4.FieldName = "CategoryId";
            this.gridColumn4.Name = "gridColumn4";
            // 
            // rep_comp
            // 
            resources.ApplyResources(this.rep_comp, "rep_comp");
            this.rep_comp.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_comp.Buttons"))))});
            this.rep_comp.Name = "rep_comp";
            this.rep_comp.View = this.gridView3;
            // 
            // gridView3
            // 
            resources.ApplyResources(this.gridView3, "gridView3");
            this.gridView3.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn5,
            this.gridColumn6});
            this.gridView3.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn5
            // 
            resources.ApplyResources(this.gridColumn5, "gridColumn5");
            this.gridColumn5.FieldName = "CompanyNameAr";
            this.gridColumn5.Name = "gridColumn5";
            // 
            // gridColumn6
            // 
            resources.ApplyResources(this.gridColumn6, "gridColumn6");
            this.gridColumn6.FieldName = "CompanyId";
            this.gridColumn6.Name = "gridColumn6";
            // 
            // rep_mtrx
            // 
            resources.ApplyResources(this.rep_mtrx, "rep_mtrx");
            this.rep_mtrx.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_mtrx.Buttons"))))});
            this.rep_mtrx.Name = "rep_mtrx";
            this.rep_mtrx.ShowDropDown = DevExpress.XtraEditors.Controls.ShowDropDown.Never;
            this.rep_mtrx.View = this.gridView4;
            // 
            // gridView4
            // 
            resources.ApplyResources(this.gridView4, "gridView4");
            this.gridView4.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn7,
            this.gridColumn8});
            this.gridView4.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView4.Name = "gridView4";
            this.gridView4.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView4.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn7
            // 
            resources.ApplyResources(this.gridColumn7, "gridColumn7");
            this.gridColumn7.FieldName = "MDName";
            this.gridColumn7.Name = "gridColumn7";
            // 
            // gridColumn8
            // 
            resources.ApplyResources(this.gridColumn8, "gridColumn8");
            this.gridColumn8.FieldName = "MatrixDetailId";
            this.gridColumn8.Name = "gridColumn8";
            // 
            // rep_Vendor
            // 
            resources.ApplyResources(this.rep_Vendor, "rep_Vendor");
            this.rep_Vendor.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("rep_Vendor.Buttons"))))});
            this.rep_Vendor.Name = "rep_Vendor";
            this.rep_Vendor.View = this.gridView5;
            // 
            // gridView5
            // 
            resources.ApplyResources(this.gridView5, "gridView5");
            this.gridView5.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn9,
            this.gridColumn10});
            this.gridView5.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView5.Name = "gridView5";
            this.gridView5.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView5.OptionsView.ShowGroupPanel = false;
            // 
            // gridColumn9
            // 
            resources.ApplyResources(this.gridColumn9, "gridColumn9");
            this.gridColumn9.FieldName = "VenNameAr";
            this.gridColumn9.Name = "gridColumn9";
            // 
            // gridColumn10
            // 
            resources.ApplyResources(this.gridColumn10, "gridColumn10");
            this.gridColumn10.FieldName = "VendorId";
            this.gridColumn10.Name = "gridColumn10";
            // 
            // repositoryItemMemoEdit1
            // 
            resources.ApplyResources(this.repositoryItemMemoEdit1, "repositoryItemMemoEdit1");
            this.repositoryItemMemoEdit1.Name = "repositoryItemMemoEdit1";
            // 
            // lblReportName
            // 
            resources.ApplyResources(this.lblReportName, "lblReportName");
            this.lblReportName.MenuManager = this.barManager1;
            this.lblReportName.Name = "lblReportName";
            this.lblReportName.Properties.AccessibleDescription = resources.GetString("lblReportName.Properties.AccessibleDescription");
            this.lblReportName.Properties.AccessibleName = resources.GetString("lblReportName.Properties.AccessibleName");
            this.lblReportName.Properties.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("lblReportName.Properties.Appearance.Font")));
            this.lblReportName.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lblReportName.Properties.Appearance.FontSizeDelta")));
            this.lblReportName.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lblReportName.Properties.Appearance.FontStyleDelta")));
            this.lblReportName.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lblReportName.Properties.Appearance.GradientMode")));
            this.lblReportName.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lblReportName.Properties.Appearance.Image")));
            this.lblReportName.Properties.Appearance.Options.UseFont = true;
            this.lblReportName.Properties.Appearance.Options.UseTextOptions = true;
            this.lblReportName.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblReportName.Properties.AutoHeight = ((bool)(resources.GetObject("lblReportName.Properties.AutoHeight")));
            this.lblReportName.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("lblReportName.Properties.Mask.AutoComplete")));
            this.lblReportName.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("lblReportName.Properties.Mask.BeepOnError")));
            this.lblReportName.Properties.Mask.EditMask = resources.GetString("lblReportName.Properties.Mask.EditMask");
            this.lblReportName.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("lblReportName.Properties.Mask.IgnoreMaskBlank")));
            this.lblReportName.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("lblReportName.Properties.Mask.MaskType")));
            this.lblReportName.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("lblReportName.Properties.Mask.PlaceHolder")));
            this.lblReportName.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("lblReportName.Properties.Mask.SaveLiteral")));
            this.lblReportName.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("lblReportName.Properties.Mask.ShowPlaceHolders")));
            this.lblReportName.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("lblReportName.Properties.Mask.UseMaskAsDisplayFormat")));
            this.lblReportName.Properties.NullValuePrompt = resources.GetString("lblReportName.Properties.NullValuePrompt");
            this.lblReportName.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lblReportName.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // lblDateFilter
            // 
            resources.ApplyResources(this.lblDateFilter, "lblDateFilter");
            this.lblDateFilter.MenuManager = this.barManager1;
            this.lblDateFilter.Name = "lblDateFilter";
            this.lblDateFilter.Properties.AccessibleDescription = resources.GetString("lblDateFilter.Properties.AccessibleDescription");
            this.lblDateFilter.Properties.AccessibleName = resources.GetString("lblDateFilter.Properties.AccessibleName");
            this.lblDateFilter.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lblDateFilter.Properties.Appearance.FontSizeDelta")));
            this.lblDateFilter.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lblDateFilter.Properties.Appearance.FontStyleDelta")));
            this.lblDateFilter.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lblDateFilter.Properties.Appearance.GradientMode")));
            this.lblDateFilter.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lblDateFilter.Properties.Appearance.Image")));
            this.lblDateFilter.Properties.Appearance.Options.UseTextOptions = true;
            this.lblDateFilter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblDateFilter.Properties.AutoHeight = ((bool)(resources.GetObject("lblDateFilter.Properties.AutoHeight")));
            this.lblDateFilter.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("lblDateFilter.Properties.Mask.AutoComplete")));
            this.lblDateFilter.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.BeepOnError")));
            this.lblDateFilter.Properties.Mask.EditMask = resources.GetString("lblDateFilter.Properties.Mask.EditMask");
            this.lblDateFilter.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.IgnoreMaskBlank")));
            this.lblDateFilter.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("lblDateFilter.Properties.Mask.MaskType")));
            this.lblDateFilter.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("lblDateFilter.Properties.Mask.PlaceHolder")));
            this.lblDateFilter.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.SaveLiteral")));
            this.lblDateFilter.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.ShowPlaceHolders")));
            this.lblDateFilter.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("lblDateFilter.Properties.Mask.UseMaskAsDisplayFormat")));
            this.lblDateFilter.Properties.NullValuePrompt = resources.GetString("lblDateFilter.Properties.NullValuePrompt");
            this.lblDateFilter.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lblDateFilter.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // lblFilter
            // 
            resources.ApplyResources(this.lblFilter, "lblFilter");
            this.lblFilter.MenuManager = this.barManager1;
            this.lblFilter.Name = "lblFilter";
            this.lblFilter.Properties.AccessibleDescription = resources.GetString("lblFilter.Properties.AccessibleDescription");
            this.lblFilter.Properties.AccessibleName = resources.GetString("lblFilter.Properties.AccessibleName");
            this.lblFilter.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lblFilter.Properties.Appearance.FontSizeDelta")));
            this.lblFilter.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lblFilter.Properties.Appearance.FontStyleDelta")));
            this.lblFilter.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lblFilter.Properties.Appearance.GradientMode")));
            this.lblFilter.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lblFilter.Properties.Appearance.Image")));
            this.lblFilter.Properties.Appearance.Options.UseTextOptions = true;
            this.lblFilter.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lblFilter.Properties.AutoHeight = ((bool)(resources.GetObject("lblFilter.Properties.AutoHeight")));
            this.lblFilter.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("lblFilter.Properties.Mask.AutoComplete")));
            this.lblFilter.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("lblFilter.Properties.Mask.BeepOnError")));
            this.lblFilter.Properties.Mask.EditMask = resources.GetString("lblFilter.Properties.Mask.EditMask");
            this.lblFilter.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("lblFilter.Properties.Mask.IgnoreMaskBlank")));
            this.lblFilter.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("lblFilter.Properties.Mask.MaskType")));
            this.lblFilter.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("lblFilter.Properties.Mask.PlaceHolder")));
            this.lblFilter.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("lblFilter.Properties.Mask.SaveLiteral")));
            this.lblFilter.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("lblFilter.Properties.Mask.ShowPlaceHolders")));
            this.lblFilter.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("lblFilter.Properties.Mask.UseMaskAsDisplayFormat")));
            this.lblFilter.Properties.NullValuePrompt = resources.GetString("lblFilter.Properties.NullValuePrompt");
            this.lblFilter.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lblFilter.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // frm_Sl_JobOrderStatus
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.grdST_jobOrder);
            this.Controls.Add(this.lblFilter);
            this.Controls.Add(this.lblDateFilter);
            this.Controls.Add(this.lblReportName);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.KeyPreview = true;
            this.Name = "frm_Sl_JobOrderStatus";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_ImExp_Fines_FormClosing);
            this.Load += new System.EventHandler(this.frm_ImExp_Fines_Load);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdST_jobOrder)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_MtrxParent)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_cat)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_comp)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_mtrx)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.rep_Vendor)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemMemoEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblReportName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblDateFilter.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lblFilter.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnPreview;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraGrid.GridControl grdST_jobOrder;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraBars.BarButtonItem barBtnPrint;
        private DevExpress.XtraEditors.TextEdit lblDateFilter;
        private DevExpress.XtraEditors.TextEdit lblReportName;
        private DevExpress.XtraEditors.TextEdit lblFilter;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_MtrxParent;
        private DevExpress.XtraGrid.Views.Grid.GridView repositoryItemGridLookUpEdit1View;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_comp;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_cat;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_mtrx;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraBars.BarButtonItem barBtnRefresh;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit rep_Vendor;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView5;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        private DevExpress.XtraEditors.Repository.RepositoryItemMemoEdit repositoryItemMemoEdit1;
        private DevExpress.XtraGrid.Columns.GridColumn col_JOCode;
        private DevExpress.XtraGrid.Columns.GridColumn col_CustomerId;
        private DevExpress.XtraGrid.Columns.GridColumn col_RegDate;
        private DevExpress.XtraGrid.Columns.GridColumn col_SalesEmp;
        private DevExpress.XtraGrid.Columns.GridColumn col_Job;
        private DevExpress.XtraGrid.Columns.GridColumn col_DeliveryDate;
        private DevExpress.XtraGrid.Columns.GridColumn col_Dept;
        private DevExpress.XtraGrid.Columns.GridColumn col_Status;
        private DevExpress.XtraGrid.Columns.GridColumn col_Priority;
        private DevExpress.XtraGrid.Columns.GridColumn col_DeliveryEmp;
        private DevExpress.XtraGrid.Columns.GridColumn col_ArCaption;
        private DevExpress.XtraGrid.Columns.GridColumn col_EnCaption;
        private DevExpress.XtraGrid.Columns.GridColumn col_EnText;
        private DevExpress.XtraGrid.Columns.GridColumn col_ArText;
        private DevExpress.XtraGrid.Columns.GridColumn col_UserName;
        private DevExpress.XtraGrid.Columns.GridColumn col_JobOrderId;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
    }
}