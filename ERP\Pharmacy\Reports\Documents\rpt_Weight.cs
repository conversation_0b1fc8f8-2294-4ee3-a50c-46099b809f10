using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using System.Linq;
using System.Data;
using DAL;

namespace Reports
{
    public partial class rpt_Weight : DevExpress.XtraReports.UI.XtraReport
    {
        string IsIN, IsVendor, DealerName, CarNo, TrailerNo, Destination, DriverName, ItemName, Notes, MoneyAmount,
               FirstWeight, FirstDate, FirstTime, FirstUser,
               SecondWeight, SecondDate, SecondTime, SecondUser, NetWeight,
               Serial, VenCode, Expenses,
               BillFirstWeight, BillSecondWeight, BillNetWeight;

        public rpt_Weight()
        {
            InitializeComponent();
        }
        public rpt_Weight(string IsIN, string IsVendor, string DealerName, string CarNo, string TrailerNo, string Destination, string DriverName,
            string ItemName, string Notes, string MoneyAmount,
               string FirstWeight, string FirstDate, string FirstTime, string FirstUser,
               string SecondWeight, string SecondDate, string SecondTime, string SecondUser, string NetWeight,
               string Serial, string VenCode, string Expenses,
               string BillFirstWeight, string BillSecondWeight, string BillNetWeight)
        {
            InitializeComponent();
            getReportHeader();

            this.IsIN = IsIN;
            this.IsVendor = IsVendor;
            this.DealerName = DealerName;
            this.CarNo = CarNo;
            this.TrailerNo = TrailerNo;
            this.Destination = Destination;
            this.DriverName = DriverName;
            this.ItemName = ItemName;
            this.Notes = Notes;
            this.MoneyAmount = MoneyAmount;
            this.FirstWeight = FirstWeight;
            this.FirstDate = FirstDate;
            this.FirstTime = FirstTime;
            this.FirstUser = FirstUser;
            this.SecondWeight = SecondWeight;
            this.SecondDate = SecondDate;
            this.SecondTime = SecondTime;
            this.SecondUser = SecondUser;
            this.NetWeight = NetWeight;

            this.Serial = Serial;
            this.VenCode = VenCode;
            this.Expenses = Expenses;
            this.BillFirstWeight = BillFirstWeight;
            this.BillSecondWeight = BillSecondWeight;
            this.BillNetWeight = BillNetWeight;
        }

        public void LoadData()
        {
            lbl_IsIN.Text = IsIN;
            lbl_IsVendor.Text = IsVendor;
            lbl_DealerName.Text = DealerName;
            lbl_CarNo.Text = CarNo;
            lbl_TrailerNo.Text = TrailerNo;
            lbl_Destination.Text = Destination;
            lbl_DriverName.Text = DriverName;
            lbl_ItemName.Text = ItemName;
            lbl_Notes.Text = Notes;
            lbl_MoneyAmount.Text = MoneyAmount;
            lbl_FirstWeight.Text = FirstWeight;
            lbl_FirstDate.Text = FirstDate;
            lbl_FirstTime.Text = FirstTime;
            lbl_FirstUser.Text = FirstUser;
            lbl_SecondWeight.Text = SecondWeight;
            lbl_SecondDate.Text = SecondDate;
            lbl_SecondTime.Text = SecondTime;
            lbl_SecondUser.Text = SecondUser;
            lbl_NetWeight.Text = NetWeight;

            lbl_Serial.Text = Serial;
            lbl_VenCode.Text = VenCode;
            lbl_Expenses.Text = Expenses;

            txt_BillFirstWeight.Text = BillFirstWeight;
            txt_BillSecondWeight.Text = BillSecondWeight;
            txt_BillNetWeight.Text = BillNetWeight;
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                lblCompName.Text = comp.CmpNameAr;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }
    }
}
