﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;

namespace Pharmacy.Forms
{
    public partial class frm_IC_Company : DevExpress.XtraEditors.XtraForm
    {
        int CompId, FirstCompId, LastCompId;

        string CompanyCode = "0";
        string CompanyNameAr = string.Empty;
        string CompanyNameEn = string.Empty;
        string Address = string.Empty;        
        string Tel = string.Empty;

        FormAction action = FormAction.None;
        UserPriv prvlg;

        public frm_IC_Company(int comp_ID, FormAction action)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            CompId = comp_ID;
            this.action = action;
        }

        private void frm_IC_Company_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            LoadPrivilege();
            GetFirstAndLast();

            if (CompId == 0)
            {
                if (action == FormAction.None)
                {
                    if (LastCompId == 0 && FirstCompId == 0)
                    {
                        action = FormAction.Add;
                        NewComp();
                    }
                    else if (LastCompId >= FirstCompId)
                    {
                        CompId = LastCompId;
                        LoadComp(false);
                    }
                }
                else if (action == FormAction.Add)
                    NewComp();
            }
            else
            {
                LoadComp(true);
            }
        }

        private void frm_IC_Company_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.PageUp)
            {
                btnPrev.PerformClick();
            }
            if (e.KeyCode == Keys.PageDown)
            {
                btnNext.PerformClick();
            }
        }

        private void frm_IC_Company_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                e.Cancel = true;
        }


        private void btnPrev_Click(object sender, EventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            if (FirstCompId != 0)
            {
                if (CompId == FirstCompId || CompId == 0)
                {
                    CompId = LastCompId;
                    LoadComp(false);
                }
                else
                {
                    CompId -= 1;
                    LoadComp(false);
                }
            }
        }

        private void btnNext_Click(object sender, EventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            if (LastCompId != 0)
            {
                if (CompId >= LastCompId)
                {
                    CompId = FirstCompId;
                    LoadComp(true);
                }
                else
                {
                    CompId += 1;
                    LoadComp(true);
                }
            }
        }


        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_List_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_IC_CompaniesList)))
            {
                frm_IC_CompaniesList frm = new frm_IC_CompaniesList();
                frm.BringToFront();
                frm.Show();
            }
            else
                Application.OpenForms["frm_IC_CompaniesList"].BringToFront();
        }        

        private void barBtn_Save_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Focus();

            if(!ValidData())
                return;

            SaveData();
        }
                       
        private void barBtn_Delete_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (action == FormAction.Add)
                return;

            if (CompId <= 0)
                return;

            DialogResult DR = XtraMessageBox.Show(
                Shared.IsEnglish == true ? ResICEn.MsgDelComp : ResICAr.MsgDelComp//"هل انت متأكد انك تريد حذف هذه الشركة؟"
                , "", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
            if (DR == DialogResult.Yes)
            {
                DAL.ERPDataContext pharm = new DAL.ERPDataContext();
                var items = (from i in pharm.IC_Items
                             where i.Company == CompId
                             select i).ToList();
                if (items.Count > 0)
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgDelCompDenied : ResICAr.MsgDelCompDenied//"عفواً، يوجد أصناف مرتبطة بهذه الشركة، لا يمكن حذف الشركة"
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                else
                {
                    var comp = (from c in pharm.IC_Companies
                                where c.CompanyId == CompId
                                select c).SingleOrDefault();
                    pharm.IC_Companies.DeleteOnSubmit(comp);

                    MyHelper.UpdateST_UserLog(pharm, comp.CompanyCode.ToString(), comp.CompanyNameAr,
            (int)FormAction.Delete, (int)FormsNames.Company);

                    pharm.SubmitChanges();
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgDel : ResICAr.MsgDel//"تم الحذف بنجاح"
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    GetFirstAndLast();
                    if (LastCompId == 0 && FirstCompId == 0) // no records remains
                        Reset();
                    if (CompId > LastCompId)//when delete last record
                        btnPrev.PerformClick();
                    else
                        btnNext.PerformClick();
                }
            }
        }

        private void barBtnNew_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            NewComp();            
        }


        private void txt_CompanyNameEn_Leave(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(txtCompanyNameEn.Text) && action == FormAction.Add)
            {
                DAL.ERPDataContext pharm = new DAL.ERPDataContext();
                var name = (from n in pharm.IC_Companies
                            where n.CompanyNameEn == txtCompanyNameEn.Text
                            select n.CompanyNameEn).Count();
                if (name>0)
                { XtraMessageBox.Show(Shared.IsEnglish == true ? ResICEn.MsgNameExist : ResICAr.MsgNameExist,//"هذا الاسم مسجل من قبل",
                    Shared.IsEnglish == true ? ResICEn.MsgTWarn : ResICAr.MsgTWarn//"تنبيه"
                    , MessageBoxButtons.OK, MessageBoxIcon.Warning); txtCompanyNameEn.Focus(); }
            }
        }

        private void txtCompanyNameAr_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (!char.IsLetterOrDigit(e.KeyChar))
                e.Handled = e.KeyChar != (char)Keys.Back;
        }

               
        private void NewComp()
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            Reset();
            action = FormAction.Add;
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            CompId =
                    new DAL.ERPDataContext().IC_Companies.Select(x => x.CompanyId).ToList().DefaultIfEmpty(0).Max() + 1;
            txtCompanyCode.Text = (new DAL.ERPDataContext().IC_Companies.Select(x => x.CompanyCode).ToList().DefaultIfEmpty(0).Max() + 1).ToString();
            txtCompanyCode.Focus();
        }

        void LoadComp(bool isNext)
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            var d = DB.IC_Companies.Where(x => x.CompanyId == CompId).SingleOrDefault();
            if (d == null)
            {
                if (isNext)
                    CompId += 1;
                else
                    CompId -= 1;

                LoadComp(isNext);
            }
            else
            {
                CompId = d.CompanyId;
                txtCompanyCode.Text = d.CompanyCode.ToString();
                txtCompanyNameAr.Text = CompanyNameAr = d.CompanyNameAr;
                txtCompanyNameEn.Text = CompanyNameEn = d.CompanyNameEn;
                //txtAddress.Text = Address = d.Address;
                //txtTel.Text = Tel = d.Tel;

                action = FormAction.Edit;
            }
        }

        void Reset()
        {
            txtCompanyCode.Text = "0";
            txtCompanyNameAr.Text = CompanyNameAr =
            txtCompanyNameEn.Text = CompanyNameEn = string.Empty;
            //txtAddress.Text = Address =
            //txtTel.Text = Tel = 
        }

        void GetFirstAndLast()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            FirstCompId = DB.IC_Companies.Select(d => d.CompanyId).FirstOrDefault();
            LastCompId = DB.IC_Companies.Select(d => d.CompanyId).ToList().DefaultIfEmpty(0).Max();
        }

        DialogResult ChangesMade()
        {
            if (CompId > 0 &&
            (
            txtCompanyNameAr.Text.Trim() != CompanyNameAr.Trim() ||
            txtCompanyNameEn.Text != CompanyNameEn.Trim() //||
            //txtAddress.Text != Address.Trim() ||
            //txtTel.Text != Tel.Trim()
            )
            )
            {
                DialogResult r = XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgDataModified : ResICAr.MsgDataModified//"لقد قمت بعمل بعض التغييرات, هل تريد الحفظ أولا "                    
                    , "", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                if (r == DialogResult.Yes)
                {
                    if (!ValidData())
                        return DialogResult.Cancel;

                    SaveData();
                    return r;
                }
                else if (r == DialogResult.No)
                {
                    // no thing made, continue closing or do next or do previous
                    return r;
                }
                else if (r == DialogResult.Cancel)
                {
                    //cancel form action
                    return r;
                }
            }
            return DialogResult.No;
        }

        private void SaveData()
        {
            DAL.ERPDataContext pharm = new DAL.ERPDataContext();

            if (action == FormAction.Add)
            {
                IC_Company comp = new IC_Company();
                comp.CompanyCode = Convert.ToInt32(txtCompanyCode.EditValue);
                comp.CompanyNameAr = Utilities.CleanSingle(txtCompanyNameAr.Text);
                comp.CompanyNameEn = txtCompanyNameEn.Text;
                comp.Address = string.Empty;
                comp.Tel = string.Empty;

                pharm.IC_Companies.InsertOnSubmit(comp);

                MyHelper.UpdateST_UserLog(pharm, comp.CompanyCode.ToString(), comp.CompanyNameAr,
(int)FormAction.Add, (int)FormsNames.Company);

                pharm.SubmitChanges();
                CompId = comp.CompanyId;

                GetFirstAndLast();
            }
            else if (action == FormAction.Edit)
            {
                var comp = (from i in pharm.IC_Companies
                            where i.CompanyId == CompId
                            select i).SingleOrDefault();
                comp.CompanyCode = Convert.ToInt32(txtCompanyCode.EditValue);
                comp.CompanyNameAr = Utilities.CleanSingle(txtCompanyNameAr.Text);
                comp.CompanyNameEn = txtCompanyNameEn.Text;
                comp.Address = string.Empty;
                comp.Tel = string.Empty;

                MyHelper.UpdateST_UserLog(pharm, comp.CompanyCode.ToString(), comp.CompanyNameAr,
(int)FormAction.Edit, (int)FormsNames.Company);

                pharm.SubmitChanges();
            }

            XtraMessageBox.Show(
                Shared.IsEnglish == true ? ResICEn.MsgSave : ResICAr.MsgSave//"تم الحفظ بنجاح"                
                , "", MessageBoxButtons.OK, MessageBoxIcon.Information);

            CompanyCode = txtCompanyCode.Text;
            CompanyNameAr = txtCompanyNameAr.Text.Trim();
            CompanyNameEn = txtCompanyNameEn.Text.Trim();
            //Address = txtAddress.Text.Trim();
            //Tel = txtTel.Text.Trim();

            action = FormAction.Edit;
        }

        private bool ValidData()
        {
            if (action == FormAction.Add)
            {
                if (prvlg != null && !prvlg.CanAdd)
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgPrvNew : ResICAr.MsgPrvNew//"عفوا, انت لا تمتلك صلاحية انشاء بيان جديد"
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            if (action == FormAction.Edit)
            {
                if (prvlg != null && !prvlg.CanEdit)
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgPrvEdit : ResICAr.MsgPrvEdit//"عفوا, انت لا تمتلك صلاحية تعديل هذا البيان"                        
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }

            if (Validate_Company_Code() == false)
                return false;
            if (Validate_Company_ArName() == false)
                return false;

            return true;
        }        

        private bool Validate_Company_ArName()
        {
            try
            {
                DAL.ERPDataContext pharm = new DAL.ERPDataContext();
                if (string.IsNullOrEmpty(txtCompanyNameAr.Text))
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgCompName : ResICAr.MsgCompName//"يرجى إدخال اسم الشركة"
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCompanyNameAr.Focus();
                    return false;
                }
                if (action == FormAction.Add)
                {                    
                    var name = (from n in pharm.IC_Companies
                                where n.CompanyNameAr == txtCompanyNameAr.Text
                                select n.CompanyNameAr).Count();
                    if (name > 0)
                    { 
                        XtraMessageBox.Show(
                            Shared.IsEnglish == true ? ResICEn.MsgNameExist : ResICAr.MsgNameExist//"هذا الاسم مسجل من قبل"                            
                            , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtCompanyNameAr.Focus();
                        return false;
                    }
                }
            }
            catch
            {
                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgIncorrectData : ResICAr.MsgIncorrectData//"تأكد من صحة البيانات"                    
                    , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCompanyNameAr.Focus();
                return false;
            }
            return true;
        }

        private bool Validate_Company_Code()
        {
            try
            {
                DAL.ERPDataContext pharm = new DAL.ERPDataContext();
                if (string.IsNullOrEmpty(txtCompanyCode.Text))
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgCompCode : ResICAr.MsgCompCode//"يرجى إدخال كود الشركة"
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCompanyCode.Focus();
                    return false;
                }

                int comp_code = Convert.ToInt32(txtCompanyCode.EditValue);
                if (comp_code == 0)
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgZeroCode : ResICAr.MsgZeroCode//"الكود لايمكن أن يساوي صفر"                        
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCompanyCode.Focus();
                    return false;
                }
                if (comp_code != 0 && action == FormAction.Add)
                {
                    var code_exist = pharm.IC_Companies.Where(c => c.CompanyCode == comp_code).Select(c => c.CompanyCode).Count();
                    if (code_exist > 0)
                    {
                        XtraMessageBox.Show(
                            Shared.IsEnglish == true ? ResICEn.MsgCodeExist : ResICAr.MsgCodeExist//"هذا الكود مسجل من قبل"                            
                            , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtCompanyCode.Focus();
                        return false;
                    }
                }
            }
            catch
            {
                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgIncorrectData : ResICAr.MsgIncorrectData//"تأكد من صحة البيانات"                    
                    , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCompanyCode.Focus();
                return false;
            }
            return true;
        }
                       
        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                prvlg = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.Company).FirstOrDefault();
                
                if (!prvlg.CanDel)
                    barBtnDelete.Enabled = false;
                if (!prvlg.CanAdd)
                    barBtnNew.Enabled = false;
            }
        }

        private void barBtn_Help_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "الشركات");
        }
    }
}