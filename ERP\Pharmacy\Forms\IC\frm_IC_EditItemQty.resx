﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="gridColumn11.Caption" xml:space="preserve">
    <value>Company F Name</value>
  </data>
  <data name="lkpStore.Properties.Columns1" xml:space="preserve">
    <value>Store Name</value>
  </data>
  <data name="txt_Width.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;gridColumn19.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpCompany.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;gridColumn17.Name" xml:space="preserve">
    <value>gridColumn17</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="gridColumn20.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="lkpCompany.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="gridColumn5.VisibleIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="lbl_Height.TabIndex" type="System.Int32, mscorlib">
    <value>49</value>
  </data>
  <data name="repSpin.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;gridColumn20.Name" xml:space="preserve">
    <value>gridColumn20</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="lbl_Height.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;gridColumn9.Name" xml:space="preserve">
    <value>gridColumn9</value>
  </data>
  <data name="&gt;&gt;linqServerModeSource1.Name" xml:space="preserve">
    <value>linqServerModeSource1</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="lbl_Length.Size" type="System.Drawing.Size, System.Drawing">
    <value>33, 13</value>
  </data>
  <data name="col_Length.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;barBtn_Help.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpStore.Properties.Columns9" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="colItemCode1.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="lkpCompany.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Width.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;grdEditItemQty.Name" xml:space="preserve">
    <value>grdEditItemQty</value>
  </data>
  <data name="labelControl15.Text" xml:space="preserve">
    <value>Item Code/Name</value>
  </data>
  <data name="repDate.VistaTimeProperties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;txt_Width.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repUOM.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpCompany.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repDate.VistaTimeProperties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn21.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;gridColumn6.Name" xml:space="preserve">
    <value>gridColumn6</value>
  </data>
  <data name="txt_Width.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <assembly alias="DevExpress.XtraEditors.v14.1" name="DevExpress.XtraEditors.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="grdEditItemQty.EmbeddedNavigator.TextLocation" type="DevExpress.XtraEditors.NavigatorButtonsTextLocation, DevExpress.XtraEditors.v14.1">
    <value>Center</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="gridColumn14.Width" type="System.Int32, mscorlib">
    <value>29</value>
  </data>
  <data name="gridColumn23.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;lbl_Width.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="gridColumn18.Caption" xml:space="preserve">
    <value>StoreQty</value>
  </data>
  <data name="&gt;&gt;txt_Height.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Length.Size" type="System.Drawing.Size, System.Drawing">
    <value>81, 20</value>
  </data>
  <data name="&gt;&gt;labelControl1.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;gridColumn19.Name" xml:space="preserve">
    <value>gridColumn19</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.XtraForm, DevExpress.Utils.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>6, 13</value>
  </data>
  <data name="&gt;&gt;colItemCode1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>1144, 471</value>
  </data>
  <data name="txt_Width.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barDockControlLeft.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Left</value>
  </data>
  <data name="lkpStore.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpVendor.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="&gt;&gt;repSpin.Name" xml:space="preserve">
    <value>repSpin</value>
  </data>
  <data name="col_Width.Caption" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="colItemCode1.Caption" xml:space="preserve">
    <value>Code1</value>
  </data>
  <data name="gridColumn7.Width" type="System.Int32, mscorlib">
    <value>77</value>
  </data>
  <data name="labelControl6.Location" type="System.Drawing.Point, System.Drawing">
    <value>592, 78</value>
  </data>
  <data name="btnSearch.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <assembly alias="DevExpress.Utils.v14.1" name="DevExpress.Utils.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="repDate.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v14.1">
    <value>Combo</value>
  </data>
  <data name="lkpStore.Properties.Columns8" xml:space="preserve">
    <value>Store Code</value>
  </data>
  <data name="&gt;&gt;labelControl5.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="groupBox1.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="lkpItems.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v14.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;labelControl15.Name" xml:space="preserve">
    <value>labelControl15</value>
  </data>
  <data name="btnSearch.Size" type="System.Drawing.Size, System.Drawing">
    <value>75, 42</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>frm_IC_EditItemQty</value>
  </data>
  <data name="gridColumn21.Width" type="System.Int32, mscorlib">
    <value>146</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v14.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;colItemNameEn.Name" xml:space="preserve">
    <value>colItemNameEn</value>
  </data>
  <data name="gridColumn22.Caption" xml:space="preserve">
    <value>F Name</value>
  </data>
  <data name="repDate.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="gridColumn20.Caption" xml:space="preserve">
    <value>Vendor Code</value>
  </data>
  <data name="txt_Length.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Length.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;barBtn_Save.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Width.Location" type="System.Drawing.Point, System.Drawing">
    <value>806, 52</value>
  </data>
  <data name="&gt;&gt;grdEditItemQty.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.GridControl, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repDate.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v14.1">
    <value>DateTime</value>
  </data>
  <data name="gridColumn2.Caption" xml:space="preserve">
    <value>Item Name</value>
  </data>
  <data name="&gt;&gt;grdEditItemQty.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v14.1">
    <value>None</value>
  </data>
  <data name="colExpire.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;txt_Length.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="txt_Height.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v14.1">
    <value>Numeric</value>
  </data>
  <data name="txt_Width.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="&gt;&gt;lbl_Height.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="&gt;&gt;gridColumn4.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repositoryItemTextEdit1.Name" xml:space="preserve">
    <value>repositoryItemTextEdit1</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn22.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="repDate.VistaTimeProperties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Length.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;lbl_Length.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="txt_Width.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;grdEditItemQty.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="lbl_Height.Location" type="System.Drawing.Point, System.Drawing">
    <value>1022, 56</value>
  </data>
  <data name="gridColumn4.Caption" xml:space="preserve">
    <value>Current Qty</value>
  </data>
  <data name="lkpCompany.Location" type="System.Drawing.Point, System.Drawing">
    <value>369, 75</value>
  </data>
  <data name="repUOM.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repUOM.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v14.1">
    <value>Combo</value>
  </data>
  <data name="gridColumn20.Width" type="System.Int32, mscorlib">
    <value>80</value>
  </data>
  <data name="lkpItems.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;lkpVendor.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="gridColumn13.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="repDate.VistaTimeProperties.Mask.EditMask" xml:space="preserve">
    <value>T</value>
  </data>
  <data name="repDate.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn6.Caption" xml:space="preserve">
    <value>Cause</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAUAAAAAAAEAIABzjQAAVgAAADAwAAABACAAqCUAAMmNAAAgIAAAAQAgAKgQAABxswAAGBgAAAEA
        IACICQAAGcQAABAQAAABACAAaAQAAKHNAACJUE5HDQoaCgAAAA1JSERSAAABAAAAAQAIBgAAAFxyqGYA
        ACAASURBVHic7L15kCXJfd/3+dU7+vUxPT09PefOzs7eJxaHgCUJkiAEQgTvS1TYlsLUQd0OW2GHHQ5J
        VkgK/UHJERSDEkVaFumQJZkMIhgmKROUCAESCAIgASyIJbDYxd67s3PP9P369Tuq8uc/sqpeVlbWe69n
        Z6Zndt939m1VZWVlZVXX78xf/hKmmGKKKaaYYooppphiincQZL87MMVkUNWldLcDGBGJ97M/U7w9EO13
        B6aYGKeBU8Ac0NrnvkzxNsFUA7hNoKp1VV0EFkVkWVVPisgScBxoqerdIgKwpapdEbkMvAw8p6prURS1
        97H7U9yhqO93B6awUNWmiCwDJ4EHROS9wBngfVimsJDVFZE28DrwKaAtIl1gygCm2DOmDGAfYIxZFpGm
        qr5PROaA78Sq9mdUtZWWLanqXKoF1FWVVAMAqxGcFpEPASvAvwSu7MezTHFnY8oA9gEi0gLmROQ+YBH4
        CLAAnHGInKp9oC4ii1i/QBNYvvm9nuLtiCkDuIlQ1RZwFOu8ewR4N/CAqp4RkTlVXRCRKNuOauuldYMI
        HJsTagJzDQFYVtVFETmuqsvA1nR0YIq9YMoAbhCMMZmUzgi5jpXOC1gpvwKcSon/DFYDAKx0V1UAFFBN
        t85+e6BEArEBiXJtIBKRJtBU1bqnJUwxxVhMGcCNwyKWEL8nVc//O6xdf59bqYpIE4VODK9vGZ5fM7yy
        YbjcUT53IWa9B+td5VBL+Gff1eLIrPAtJ2ru5Q+KyAeBzwAbN+Phpnh7YsoArhPGmGZmy2PH5Y9jpfoZ
        4EBa1gxdm6RifXsARpXtPvQSZbWrXNxR3tw2XOkY1rrQTyA2mmoDSidW+kmpyWbaj2lcxxR7wpQBXCdE
        ZAV4DHg/8LiqfkRETk5ybTe2Ev+z52LaA+Uz5w2ru4YvXTIYVQxgjFX9Y2OvUYVBAi9vGIwW6VxVj4vI
        w1gNYIopJsaUAUwIY0yUeu3ngIewNv392KG7k+nQXfEaVRJjVfvNvrLZUzoDOL9j2I2Vb64ZujGcbxva
        fcX+c3wA1i2Q+wMMsNZVjs5p4T5pjMAy07/nFHvE9IOZEKnt/iFsoM5fwzKCZedcCarQTeDijuGb64YX
        1gxvtpUvX0pY61nmoGoJO3MCutdmhJ8hNnCurazM+nV1WUROMf17TrFHTD+YANLhu6aqPpRG571fVedF
        5H1Yr/4SFe/u9S1DP4HXtgw7feWFdcP2QFndtTb+Vt/a8RmBgyPhlXK5ow0YhU6s9DwfQDoSMPUBTLFn
        TBlAGE1gQUSeBO4F/kIakbcw+jK40FZ2BsrnLySs7ho+ez6xhA35NjHD+rqHrVHoJdA3RQ2AqRNwiuvE
        lAEwnIgjIidV9T3AnwCeBI6mobkreO+qlyg7Azi7ZXIVf6OnvLqpDAysdpV+oiUp79v1iuaSP98yJHic
        6/oGvn4tYaU8F/AU1idR8kNMMcUoTBlAinRIb1lEHlbV94vId6TlwfqJsdJ4tau82Va+fs1wZVe5tKPE
        JiPaYXCPu1OS3wQkfqCuMcpa1w4fephjqgFMcR14xzMAVf0L2HDdv4V9Hy0C8+1Vldc2lY2+8nvnYy60
        lWevGQbGSvxeArHanyvVXTveeMeZKVComx67ZgOASUcIjMLuQDm3bTjQFA7OFBjUcVU9parnomjKC6YY
        j3ccA1DVCIhS1R6s6nwM690HhlLfqHXWDYwlvK10KG+1a3/Xuloi8vw+/laH26EpkF6v5Tp4ddx2FRgY
        TRlIcZJQOq34ut7NFO88vBMZwHtE5KSI/D0s0Z+qqvvapmGjDx9/YcBGT/nqVVNS30OEHhq/dzWAXKq7
        Nr+WJX/BD+C024mVs9vKqQVYni1057iIbGFzBRimmGIM3vYMwBhTx9rHC9jEGo9gCX8BL1Q3Nko3thNv
        dgbwyqay0bNSf2fgSXgt71si1eF+ds5jCq7k94f6/PaG7Q73jUI3VmItSfom03RhU+wBb3sGkE7MeQT4
        VuDbgaeokPpbfeW1LeXpSwnPXDW8spGw0bfnqhx3rjQHR5oTkOp40p2if8AwQgNgyAS6MVzqwOGi9Ac4
        wjQz0BR7wNuWAahqU1UfwhL7n8TOyjuDN5bfGdjAmle3DFc7yjfXE97YUq7uGnqm6MEfaddrMYzXpJK/
        QNAZkUNBA8idgAWNoDyEmO3HCtt9pZcU2VKaQWgBOxowNQGmGIu3LQPAzsz7MDYJx1+uqrTZh7Wu4ROv
        xZxvG756ZUg3VU49Vxq7kj/bqgYI3NmW5vuHzAGKhO/2YZAOP3a84cA0WKkz7sVMMUWGtxUDUNUsYOdH
        sfPzfwDr5S/hmSsJnVj5/fMJuwk8t5qwkxJUydHnECU6JPY9SX5Nh/K0WJaZAmR1K4YQXW2jb5S1rqET
        l4b6FrBZgq7j7U3xTsTbigFgib4F/Ega2ffBqoovbxjWe8onzyYkRumMSKTlq+AQ9vRn5aXxf8rE7GsS
        oXbdrXsuNqQ5BEpdXQC602HAKSbF24IBGGMeS+fi/zWsh/8RESk822rXsLqrfPZ8wutbhpfWDd3E+gBK
        qrZH3EV1vzxlN3feOVI9SS9OPKluyMrCWoLrQAwxCLCEf2lH2e6XJP1ymhpsGgU0xUR4WzAAZwGN92Ad
        faXn6ic2hPbVTcM3Vq3DLw4Ql4sqphCqU2AaY6S6e62vrYckv3/OqJ1mPPDcfGkQUGtqAkwxKe5oBqCq
        HwQ+BnyPqj6WJuUoPNNrm4bffSPm+TXD168l9BIbgpsU2km3FLduEA5oLtVdB91IyZ9m98m1BDPUFvCv
        deqOMjlGIdV69pQcVFXPYE2nD2L9JQ9ip0IX4glExKjqF0WkA3wNuCIi35z4RlPclrijGIAj2ZrAXOr0
        Oyoiy+l4P5Dm0NNhJp71rg3oaQ8CUtglfvUJz5m3r0VHnFIsz6V5qvKHQn8L2oHTQJABuX3xOm0U+knK
        kIrvp57mBhgLVc0mDx3F+g6OAoeBlTRM2g8oMiJyDDvKcFRVTfr++9lPRKZDj3cY7igGgP1gW9jlsv5+
        moDzAb/Sa5s2Vv/n/7hPu69c2KkWnyFp6wf3uLZ+ycFnik7AxLPjE6U0DJi1W6jr9yE7DnR9o6f8waWE
        D54sZAZGRJ5w3tM4/E3gBPA/+SdGaBDf4dTpAGvAJ4HPp9tzE9x3itsIdwwDSBfObAJPYCP7lvHCXjd6
        SmxsFh6bf88G+RRoSIvHRYff8GRB8uMRvavC59vAcKDbvss48rJMzyjfx732RkJVl1L1/u40l+H1NpUx
        45Oq+qiIXFPVk6p6VkT6IrJ2wzo9xU3DHcMAgMdU9aiI/Aw2tr+UgffrVxM2+/DPnukzMOX4fQhIWMp2
        dym811Pz/RDeglTP6jjSPTQJKOuDK/n3QvzCdS/t/ESqOf14mkfwepFNm/5eEfleVf0JEWmLyE8D17Aa
        wRS3OW57BqCqK6raBH4kzc+3hDeJ540tw1pX+fLlhI0edJM04WahnXTrH6fS21f3x0v8jNA1TNwBBjFs
        1xl6DGgYlcMSKa6H8I0x2QpFT6nqu7EpzwCb3ejstqEzgGu71reQ5B0c3vTYnNCqwd0HIhoRtOqFtQsz
        De1jwIYx5qyIdETk7HV0d4pbhNueAQAn04/rb2Ilf6nPz68Znlsz/O4bCes9raSfkL3vEzwU7XkoJ+xw
        Cbpyiq+G67rtu31Sp0Oj6D/T2GWPKkDKPE8DHxORj+C8x24Mz1w1XNoxfO2a0o2VvilqIQI8dTzi0Ixw
        oCnM1YsMgOECpT8JdETky9gVi6cM4DbGbcsAjDFHU8L/i9ix/RapcytJs/C8smmX0fryZTuBZyd2JtA4
        H29Jza+w2X1m4DrrYBgIZLw6QSIPaBF+2LDb1wITGIO9aACquoCNkfgo8LF0uDR3El5oGy7uKL/+4oBO
        bNcdyDSAXCNJ/7/ZNzQj+PLlhGPzwsOHajx+OOLBgxHNGtSGaxY2gZ8CXlXVS8CaiEwdhLchblsGkBL/
        cVX9cOrdzvuq2CCYc9uGL15KeH7NrqMXgiu1IWAKUK3yj6oT0ghG1Q31xe3POLU/fy+TVXPRxDKA9wI/
        mBJ/zgDWe3Cpo/zhpfII3vCZ7FDlxR27/dJlwz2LwmoXDjTgngMR9Ro4YxJ14MPpfX8jLZsygNsQtx0D
        SMeWTwJ/MSX+B0j7aVTZjW0038dfGnChrby+Zdj14vgLBFwi7rC3Pmyru1v1NIFi3fBQX8XEHr+feyR+
        kbFOwPelmY7fh01r/hFskE/+996Nbd9+/pleKcmo351QtOKFtvKf3oh55krCv3p2wF95oskjyxH3LAqN
        VBNQ1VMi8neB/6CqF4C2iHQne9opbgVuRwbQTKX/GV/ygyWa9sCm317rKlv9ina8bdV5e09vG2ijVGcP
        dUP39O89EcTzAVRjCWiKyP3YPAhP+BWy+ITXt63NLzK6L/6pbmKZyFZfebMtXN1VTsee38AGEz2gqifS
        AKXb7nt7p+O2mTaWjvM/BvwI1uFXWF233VfWe8o/+MMe2wPljc2hJ320Wh+29Sdz2tlKhem66mkJlNu7
        0ZIfUonvMIBI4CcfbfC33jvDXfPCoVbhT5nlBKirauROjNqNrf/kf/tCl3Nt5atXTNAnYZ9Ci89N8Znc
        bEcHZ4RmBL/2/bMcmxNOHcitDIMNHf6PwP8nIl+Y/KmnuNnYd45sjMmWtlrAqv7ZAhcRDIlwvWcl/vZA
        2R0MP8YMZUYwOhw3JNHzLDzql49oB+98Ftzj3ztr16m7F/gSPzMBahLUBvIFQrKhvthYE2a9a5cm2+jB
        Vk9Lw335u3P6W2IM3vMbtanKe5FNVFKP4NSBvMksYGgFWEiXVY+nYcO3B/adAWCl/FNptt6fTSVWLj52
        Y/uBZZL/tc0y5bhBPVBhx6e/LHTXH+Lzg3sI1AkRf1VOQBi2W+rndRJ/ZvtnONwSHl2ebObvpR27gMm/
        eX7AM1cNlztmuPS4Vzd/j9nWe35X8mfxFtsDe9U/+mKPQy3h3/9wYZGiR9JfW0TawLPA1kQdn+KmYl8Z
        QDohZU5VnxKRB3G804mxUujVTUN7MJT8vtTPdgoagD/clhErZQ0gn+PvEHTJez9CkxiWa0nCu3Wg2Fah
        /yn2ao9d6yrPXkto1YVGBPMNiEQQIFHrH+kn1k6/sKOcbytXOiZ3AGom5rP/FzQoR2sZIfn9iU/rPdv2
        uW1DswZH54YMSlWXROQ08DJTBnBbYL81gFPY2Xz/BEv8UaayDoz9ffylAa9uWps/CX2ggW1w0o4vsbO6
        OqKOqwFQof57dYMMIuvfHiU/lL39rjbw1SsJP//HA04tCMst4YGliLmGrdCNla9eTbjcMTxz1XCto1zd
        1WIbSNEZ6vxKz+9qAAy1gMI0aOC5VcNsDT51NubIrPAD9w0ZgOPU/Ro2SGiKfcZ+M4CPYm3DguRPFL5+
        zXB22/DmlrK2qyU1Pyyhy2G5rtoa1AoyR5/HBFQpMAZImYZm27ATMkj8jmbgPsP1ImMIq13lG6sJF9rC
        bAOeW4toRtZBODBwYcdqT1c76ZLkXgfcmEmfwWXvxN9m78OoFhjA8LmtafH5830eWKrxA/c13K4fx/6t
        l1PtrzNNX7a/2G8G8CPYeehDBqA25dXTlxN+/0LC2S2bugvKkj7bdyV/XuYTf4WUd6VakOAp2/UhKRlk
        TIH+7gUh2hBne7lj05dnIwQRSbpNj50Rg6r2Cv2s0ID8d+QSvq8BoMoggU+9MWCta4DC4gWnKK5k3KXo
        JpniFmNfGICqfm86dfQhN5EH2Lj+3zsX57HpfVMkOHt99lFqgeDyD9dQYALFD1YLzrnCAp3O1m2vKNU1
        1wR8iVkl+d+KxK+Sj5qeUx0SdoHhpM8oDLfZebfN/N15/S0wRbXaQkbkmeMvD2/OH1bBJChwdUc5v6V8
        5VKflbmIexbrzj31gTSfwGewyUSm2CfsFwN4QkQexaqEhTn9F3dsWOrlHcNWfzLi8SWtrwHkH7V37Nc1
        gXNunap7uduqsreCUZJbnK1/f6moU9KgKrQaf2Qj9K5M/rCO/qXKTl/Z7ApvbhtqItxTYPMcw2oC0+Sl
        +4xbygBU9X0p9/8x4CGcQJ/zbcNvv2Zz972+Ze3IAjEWPsqK4B5viC/PuJNKqlHz+N3hQF+6uwExZH3y
        mUXgXOAFlMvG2MCa/89WVeeEAMiw31G6HwGa7ruSP9SpUJzC8J1o8P1m7xPNpgwqaDKshD232VU+80aX
        D97V5D3Hhr4AEXkIG/fx8ZEPP8VNx63WABZE5CjWBiws2NFPrPRf72oo3z1QlsKl84G6/rmgxB4j3UfV
        GdePW4nMHPCl/SgWU9KGCL8HXwMoXaXOcbofG2WzZ+jEpTcyByykS7VPsY+4JQzAGHMKG4/+14AfxFH9
        urHym6/EnN02/ParcclmdiV/aDrtpJI/JMUgrN4O7WIt98F/towxhKTrJC/HNeJDp6BAwdl9MkI3qD2t
        khO7pDfPmjVOR0L9d0dRqgOiNH15LsGbtJKCyTy1aR01bHWE33st4eScAPPubR8D7vPXbpji1uOm/gGs
        g0jrIrKgqsexal9+z15sl+Ne3bVLcBcSZfgqtWqpvMAIHELMnFKlITlfijkEnTm6SkOLeHUDTKBK26is
        UIGsls8OMsLWdMd1/g2bDk3oKTMWP11K+N3okM7djhWI399mFw/LVKGfGGJTev6mqhoRiVR1VBLSKW4y
        bjYDaIrICvA9IvL38FbmfX7NsNZTfvm5QfnavI3hcUny40h6qsN7Qyv3BINcGJYROC70y+vfzUJO/BTV
        +owwI1fld6S+ADJCKwkFLuXvIn+4LFY4yaX68A+S7js2f6Eu0I+V81uw0Z3xu7GSEn02Q3DEwmxT3Ezc
        bBVsTlUfEZG7sH/sCNK8/cDz64b1blGyu9CQ5PekfkFyaTEQqHiufDxstzj5xb021LcqTSA/5z7AJHD1
        eqcNcU4XnHnOeV/gu3Ul0MMwA3AnQbkvJ/PEGu84k/LpYucZc8A9N7z37sBwdSdmoRkx2yiY/UvYYcBr
        FW9mipuMm8oAUun/57ATQfKBoH5ipfWvvjBgvVf8SEOSP9u6Uj2XWAQ0AKdOJvVLCTvcdp0flDUAfz90
        fLNRxWwyKS8eNwiZETB8r/6IxpBoHaluXMJP31rOST3Jn/sAisQPsN5J+MbVPg8sNzjVKMwNeChd1m3K
        APYJN4UBpN7d+7CE/x48j/9XriRc7SjtvmUGoSG04LLbhMN7g1l+HGZhvP2sffAkIcW+hPwBpWcNv4DJ
        XtSY6/Ij10auaNotdnlBXp5pUvmh+3A6lO75vlOmZQdfyQ9gHJaq7p0YMospbjvcLA0gYjgF9H3+yS9e
        MrywbtjulxfoLEh8Rof3Vkp1LWoLQU+/dx+cczjn3O3tgMq+aLiOVu2oJ919+x6GUt1kXDojeFcT8K7x
        1P8xvZ5in3HDGYCqLmHHef9GOv0zP/fKhuHijuGbawmvblriHxJdOazXl+pKcRgvn5CS1guGqjptqDra
        AsX75JpH/iBjGMDN9v6V7hW+ny9sndLisXpP46r3rgYwUr33JL/n9S+8wQIXMrf2fU0xMW6GBtDCevuf
        8ttf7ylvbCtXdjXP3x8islC5T8ju5xaU6gHiL1zj1S3c6477VkdIXF/VR73yEFH7nn1f5Xfay+uOYJe+
        LTXFbYOboQH8ODbWeynL7LMbK9t9+PyFhE+/GXN5R52htYrkHZ7ELwb5DKeihob43CW6QUcH+3jfpL3k
        Vn6ofgdGEHOhyCG4khrgE3lWz7XzsxeQDM+VJLs7CqCO1hCQ+iO0lKkP4PbFDWcA6Qo0y27bRiE2Npvv
        es/OVc8w7rMofNZVlUNaZ0X7o+5353yiIf3Ir1Kl1gS2PkEXhvTcOpTrjjaUJjg3xX7ihjEAVX0MS/j/
        PXaOf46nLyf8m+djzm4Ztpxhv8wet9d7Q3ye5I9NKvmdupnkTzLJ707t9eoGh7+yG4+VwmPwViLZxkn8
        EBEGJbxb7jr0qojbse99e74U3OOYAO69R7075500I1hq1Zipl97TFtPUYPuKG6kBrGBX8oncTLSdGNoD
        awbEmubd9TVTyup5rqbn+/aiQh31r9GhFuu3QznMt3gwARMI0vmYhPp5tVH11Nv1dBm3bCQT8MsCRD/S
        lk/ruKZAoc4eGICDerqQqLN0GAAi0lXV6UIh+4gbyQD+G+A73AQfm33lc+cTnr6c8I1VQybvvc+7HNyj
        Q3s+GyZ0Pf2uBhDKS+eua+fb/JWEVCirQogD6Jjz41ClAQT6mT1M4ZxLqD6hZ3Ucwjeeel8K8x2hJRT6
        U9V/r1hgaSbikSOz5SqqL0dRNM0NuI94ywzAGLMoIgtY9X8JiFSV2MBOH17asOv25eGmFIVWRvRQJP5h
        cg7Ny/I6FIk/vNyXs+S3TzghdZrwYflkFZFLOPi+0G7g/J4lvh9w4zv2HAIuDfW5kj3bd8b6Q3XzPvjH
        bped43z6otKoRSzN1TkwU8PDBjBdG+A2wFtmACJyErhPVe8TkVNZeTeByx3DfzqbsDMID/kVUk65hE31
        pJ2x3n+n3N4su7P38ep1fHsZoeX2rZ+HZ9zM+1HlVQTvayghwvQkdmXEHmX7Ppfufpiv1xdf3a9kBuRL
        GM024PFjc5xYbOLhHCkTqHgpU9wi3AgT4CHgu9O4f8AS4jNXEl7fNmz1DAN3Dgme5NcisedE7kj+TPVH
        hw4/Y2yd4jXZN+x+9O4W55jyx1sFd1XO7AGKO46NP8HifSE1uqAJOBzRZwSloThXYgdU9+y4JP2zob1A
        HV8DKGkfVc+RvQd7br7R4D0n57j7YNO7TC+IyBWGwZtT7BNuBAN4gOHqs4Al0q+v2qg/f+VZcD5z5zvP
        NQC8Y0+qu+cKfgOnbpj4vY94QtrP644z71WdGTl78QVoeFvqX9WzjNIAPIIvMImK6D6/ncK9gx1z4ExH
        RJhvCO8+MccpjwGkxP86Uwaw77huBqCqy8AZVX0UOJ2uBEs3VnYGymfOxXkqqFyYMZTeLuEWpbuTb169
        7D5oHubrhvtmmX6DnuxMxRj2nLxTxSeiTLjOWlwqRSLIcnFn9fI2U19AXneE41C9fhVUJM12bJmrugcJ
        WwPPbYpbNQ7Bu0E+zjavy7BOpZnivwPNNaG5mQZH5hs8dfcBDs2WPrOvAs8wzQi873grGsAcNqvvsuv5
        j40N9HmzrSRemF1I1rnfPO6xL+2dfX8S0PB7dFr2pdpIuD2a0K6vPLVXDSDQheLO8LiSYXjMIqgBmUA5
        zvV4dZzykO3vHruaT3rcrAmzzYiTi02aNfEu0SsicpapBrDveCsM4DFV/V9E5AG38HffiFnrKu3+MP9+
        9smMCtl1c/e5v8zmz9JKxcbVDLwPtxTAouWPeCxDSM+LK9UzZPZ9NHwg8Cbl++P9zrnQvQv+Cee4UvK7
        EtzTAPK63iy+0PTdkJ8gu3+JSVS9M+cd5SuXCq268I9/4F6OHWhyYKbmvEq9IiIbIvK6qp4VkakTcJ+x
        ZwaQEl2E1QCOkub1z4hxs69s9ofLThc+IS1KdlsUyMOnzs8775eVpKDrPKuSZOOYQJBYs2E+55xrHuRJ
        +8gJoYAQUyi9HOd8VlYprd3n1fKxXyfoQwi8o1CdQmed9xFgllEk1CPhxGKT5blGIfhHRPqq2hGRfhRF
        U/X/NsD1aACL2Jl+3y4ij2WFu7GVyv/vyzFXOqaokKbfs7/gRJIKptGS39YtSv6sYTP8QXncO7v5xKaA
        0+GgF99PwuUl6pDCziQ3KjOBvN8jwnFdmz8k+f3gHtc/UHhHgXbdc5WS333Oohbwp588wspCkx9+fCV0
        0e+IyG8Br072fqa42bgeBlDHBv0UFoDf7iu7MQyMFoi0JPELwkcDZeFrgpI/KO2cC30JOikTKGkAmWPP
        2c81AScAyNcE0uolFCS+pxmEJH+VVNfAcbovAq2aIAgR1nmaGEOsYEzgnYQkfql/zvvxcKBVp1mPOH2o
        xcp8ady/gx33v4aN/Z+q/rcJrlcD+KE08Ccv/OLlhLPbylpX2UmH/txvNNMAlHLuvlwDAJK03J8MlNW1
        Dbve7IBUc9XdDGPt2QxDR5ZzMcWcvGM0AXCiAseFD/tlVUzLeaYSg/M1AEMjEh5eadGIYL4pbHQGbOwO
        uLKd0Okbcv9baKLPyH6G8S1nDnJqqcXf/ugZDs83/NPPAb+lqp+MouhLEzc6xU3HxAwgzfHfAhZT4i/M
        +LvaUd5Il/Sy9YvEX9hqYGkv9Z2E6o39u+qpTwwhbcDZ+vb1OJTou8KBl3m/C5pBuj/qfu65ksSfQPJn
        RO/b/ml7jxxpcWCmxscePEg9gmYNdvsJO/2E7W5MLzYMkgRVWNvpERtltd2nHxvavZjdQcLuIKE7SJyR
        nCIjazUiDraanDw4w5EDM3zn/UscX5zxZ/zFWMl/FviaiEyTf95m2JMGkMb8r4jIB/1zL28Y/uiKoZc4
        U25HMQGKocChaEBXa9Dh/8pEX5JgAcKZGFoU+AWNoEqqj5L2E9yv0N+suOqZ3Od1x+zJ2/joA4scP9Dg
        734kj8wuYasbYxSeOb/FTi/my2e3WO8MOLu2y8XNLpe3+1zZ7tExzjptjlZ0sNXg0eMLfPThw3z7fUu8
        68RCSfKraldEXge+IiL/fu/vZoqbjb0wgAh4PzbyL8fVXcNG16b52uiZQjx+ts1ot7TYZPod56aBYZjn
        L/95QT6l4BalPH4diGXH3XWOS/asP/yndtjPNQvUUCD2wpCh4yPwblVCSavwCdwp9xlEXt3TCFBadWGm
        NpoZzdQjVOG+w7MMEmVptkEvNmztxrT7MZ1+wpXtPp1BwuZubDUBEWbqEaeWWsw3ayzPNzmz3OLkwRZz
        zUK+f5OG+54FfhF4eWRnptg37JUBPAbc7RZudG2ev7Wu0h5Ua+KufFRNyAAAIABJREFUPMt/OjxnvDrB
        IJ+Qihy0Xd0LQ0S2R5R8Alk7IT+BXz628cnPh5iAW56emKlHNGuj192cqdvzpw/Zabr3r8yV6lzZ7tEZ
        GM6td/M4jIWZGu8/fXBMn0FELgHfFJF/N7byFPuGvTKAH8Nb3uv1LeULFxPWu+VpuzAMBHJn8eUTejLJ
        r6A6HD1IdOgYLAX5+IExQVMgRWHySgWh5cQjxXp+iGsm3WEYCFTQBLzRAdeEKN90uM01ktAzhPrsawkj
        mGARbaw3fpE0dmMcDs42WJhRDrbqmPQ91aOxzO3fAWvArzHN9nPbY6+jAGewS3zl2B4olzvFJb1L5qy3
        xTmurOtK/mw7oQT0GrhFeIsaQKm7N7z/MZYBLIyrmCHTEuaapfn8o/ACcEVEvrCXi6bYH0zEAIwxj2A/
        nOOk6/vtxnZF32evJXz2fEw/Karx+RBfKt0zia4MJ/IUQ4FTyZ9fE5j2mmkEmt6hyhSYeMjPhYYPfWeg
        e9Jfj0vTennMABUKwB76FRoRKGk9E+GbwOeA/4C1yefSlZufxGoE71LVxTSnw1Hs33qJam3hGjag52xq
        738auJC2PR3nv0MwqQYwl/7y+omBXqJ0E+imf+6CRE9VW9fzj7tN/6elOn6Yb+DDJ9Cg2+j1aAJVNn7I
        seeeE78OFDSByvtrxT7hZ3uLUNW+iHSw0vmsMWZBROrYadxzwFER6WMJvpluI4bEnDkVTLq09wZW1b8m
        IpeBC6p6TkS2ppl+7hxMxABE5AeBE27ZxY7yufOGN7aKK87nQ3vONs/gk03hZagBqFIIBMq/+cqJPT4T
        GGczvxWMUus9JjCqTrDdqrIbT/whRFHUTnc/k25/p9Qj1aNYTeFUyiwA2iLyDGCmhH7nY1IN4CBWHczR
        j5XVXUPXmfM/3GqJCRS2ualQvnY43u9cVCn5Pe3A3d5oAioMA4a0goq6I22BW4dskRbd23vpYyX/FsNv
        pXNjezbFfmIiBqCqT4nIGbfsUkf5vfMJlztWCLjTR1xi9yf6ZBpBvnXqaN5CNsEnpP67zACKOzdXahYx
        SrrfQmLPUnCNgaoiIpEEJzlVNS0b6e6l6+vcFLc7RjIAVc3s/oX0R6J20s92347978bDgJ6Q8HZ9VUNz
        QEtlpQsKBO80POwdQ2kPt5b4K5BrBsGTFfW947f0GNYUubhVnmmbRnEeZ8IhwCneGRinASxiCX8p/ZEY
        2OgpV7vKxR0tSP4MJbWfouQvlDsMolC7pOIzQq0PlL+V1XpCuNHt5bgeihcv+YgUzr282qUXl9pdwkZx
        LvonpnjnYhwDOIMnNfoJvL5puLabEr9H1FnASHCCj2cK2Gt0SPyFxJWuNAxpBe4H7s/egUYk3HOwznxT
        OLFQY3m2xuHZiIVGRLMmHGxF1Dyi3o0Na7uGTqx0B4bVXcNurLywFtOLlQvtm+DzuhHaS/741xOHMMU7
        GeNMgKMicp9bb2Cs/b/ZG1rs/taV8HkZxTgBt26hgYJa77VcFQiUZeBxyuuRcGqxweHZiCeONrjnYJ17
        lxocma0x34w4eaBWylW33k14YzNhfTdhs2d4bWPAZk/Z7u+y3Tc3hwFkuG6nZaoJ5Lk53AjGKaYYjZEM
        IHX8vRcn+m8nVp6+nPDalil69HU40QeGC3X6WX1dh18+0zSX/NkiFY6Un4AwFmciZuvCR860ONSK+FP3
        ztKsCccXajQiYaEptGpCqyE0IqEmdr06HwuNiPsPCfFijdjA+47PkKjyQw/OERvllY2Y1V3DJ17a5Xw7
        4dmrgZznNxQuu5wcl9sxrUYpem9BVY9n2ZunmALGawALIrKEFwC01rMLfkLY/x4M+iH8KWtJ0pdOlJFL
        fFunGQmz9Yh7DjY4Nl/jO+9u0agJizOjJ8T4aNSERmAW3b1LlsEdW6hzYTvhjy4N2BkEVO2b5ifYG7qx
        oZeUtJV6Svx7eylTvK0xTgN4FPigqray4aOdgfLlSwm9WHOpDp6a79j8CWXbf+gDyKS8KRJ8FVNI0apH
        HJmr8YETTb7r9AzfdleTe5fqtOpCJHYl2puBuw/UuGuhxhNHDrG6a3htI+a3Xtrl0290eWPT0B7sVVr7
        Ev76JH6ONCrxzc1+HsfvYDH9laf9TfGORZABGGMiEYlUtSkiLREhm60XG+gnSpwRv+fRz+CWlTL75nU9
        3aDsEChARJht2BVnVuYijsxFHJ2LWJmrsTK3pwkre4aINR1qWE0hUTg6V+PoXMTRuRqru0pslG4yAfOp
        0m6qnn8i/0A2U1Ewxg7XDhIlErLMvBlHqKtqHbs45wTtTvF2RpABiMhx4GSa9msO7Cf5wrry6qay1R9K
        /kzRDC7hxVDiu7b/MMBPvY/ePy50igNN4X986gBnFmv86EOzzNRgtrE/Gu2hVsShVsQTRw/yd78d/umX
        tvn61ZiPv9Cls2dNwMUophjwfgqg5RyF/Ri+cXmXg60a9y7PDFu3Yb1PYCcHdd9CR6d4G6DKBGip6pKI
        FKb+bvaU9kALkh+KNn9Q0mvxWN2LCw2UO1JLh7wfO9zgYEs4c7DG8fka9QiiCgkWa4zB0NEdEhIGOiAm
        JtbyJLW61ImImJM56tRpSYuIGpHsjbHcvVhjYOCuhYitnuYRkiMRmvTjakYhya8hRhCoBuwODLON4jtK
        /6ZTX8AUQDUDOJ5KiTxoRBWeWxsOhbnSHXeLr+b75gCejT/iY8dK+Hok/PSHD3KoJXzbXTPBei52tE1P
        e7yQPE9Hd7hmrrJhNtjUjVLdg7LEDC0ebjzCQVnintoZZqRFa48Bc3/mEWtaX+kYLu0YfuW53ckuDDGB
        0HHhXNWJoRmQKFxuDwjk7ziEje14brIOTvF2RhUDmFPVI64GoMBa17DZ14KKD46U98pKmX2zum70kK/a
        pucXGnaJqT95usnh2RqnF2vMe9Kspz162uOcOcu6WeOCOU9Pe1w1V4h1wBW9TF/7tHWbru6yq2WibMks
        DRq8kDzHrMxyKFpmUQ6yKAe5p3aGxeggp6LTNKTBjIxnPt9xqsnqrvLJ13oMzDBeYvii8od0MIEjcI9W
        RaLK1Z2Y+WKuPtI5/yupH2CKdziqPoJFETmJEwGo2ACgta4GCH3oC8jqauBcwfbPalYQxVIrYmU24i89
        OccjhxucXoxKKn9Xu2zoOl/sf4EXkuf5bP8zbOoGZ5PXbYVJfVwecR2LjnMsOs73Nn+Qe+v3s9hcZJ6F
        iRjAjz00S7tv+Bd/tMNO37DppkraSyeup4qTxiw2cH57wIFWzasiy8DdzvTeKd7BqGQAqbNoqAcrnG8b
        tvvFnP6ZGeDb+FW5/gvqf0b8w4ggFprC0kzEjz04wwdONnj0cJ3lWUGARBM2dJ2r5ipPD77I2eR1Xkpe
        5EJyjg1d55K5SJ/e3oLh1KmXdmNLNxkkA363/wkODBb5g/7naMksPzn7U8zJLA/WHx7ZZLMm/I33zvHa
        RsLPfWWH2CiDUS6BiTSDEShFQguJCqs7MduLJQa0gF3ZacoApqg2AURkBScC0JoAyq43ySQU7FMyZ7Pz
        BdvfayAta9VhZTbiAycb/NADLRYakieiVFXa2uZCco7PDX6P5wbP8tX4aXu5S/RvMRp2V3fZZZe1eBWA
        ZwZ/RFNm+HDzuzkoS2MZQD2C77m3xTOXB8w8Y6fPD8w4Yt4D8echvw5DdacFi9W62n3DbnlEoollAlMn
        4BSVDGAZeAiPAby6afK8fa56P5TwTtafggbg5vbXcrYfrLd/tgbfcVeTP/tYi/cea7DQsGPvAH/Q/zzr
        Zo1/*****************************************/1tDkXLfKz//TzReBff3/rhYFMCHJ+PeHi5
        xp95pMXzqzGfO+dM0a1y/I1DadKPe8J9EBuxeXE75t7dkgawjB3+m2oAU1R+BDMEIsY6ceCbDUn7QFll
        SborCPUIDrWEB5frLLWiQgrqDbPOVXOFr8d/zEAHdNjJLqze7sUH4BO/Q2OGhEThRfNNls1hHqw/xNHo
        WGVzIkKrjp10tFDjYnuEH2BSJpD3y80zKIVN3vGU+fYSDWkedaY5AaZIUWAAqroEnMZmhc3x8oZhZ+Cv
        1edoADgagLX0i5OAMv3f9/rn3EI5fbDGX3n3HO871uBdK/Xcn/Xp3ie5Zq7yP2/9D+xqh544sSvZhx95
        x/6+f+zTxAji97Gua/xa5//h5eaLDOjzVPPbeHfjvcG6R2Yj/uxjs9QEfvuVXrjBqnUHR3XC7WheTbEv
        QkGEngpfu9Ln1FLJcXl6OiloigwFO1BVIyxTKDCGWG0IcGmSj7O14b5DmzQjeh11EQoCMzVseO9sxIGm
        UIsEQ0Jf+6yaa1w2l+hohy4B4h8l+SVw7JfvxXwQ+5wDBuyk8QUd7WDUEMq1FwnM1oWZuuQBTcX2xqko
        IS7mdFDcNpzjtCxOl2ovdcuOAEx9AFMUCT0d91/GUxE7fS17/9Nt4tj8MJT8BVNAXZtfC6uHNiL4trua
        vPdonb/87qHVcclcZM2s8rPt/52vx388TIKTd9bZjiJyH57HPwhPww5pDN+Mn+Pszhu0mOWx+uPMyTwN
        iotjztSFuxdrnDoQcfdijdVOwnZfhw+SvaBQXj//3pJKd1VnNmRaIcpWKhq+V6Ow1SfkBLSh3ZbZT/EO
        h+8DCDKAzb4NaAlP6PE0gVzy45zUwNbWq0XCA0s17jpQHK++lFzk1eSVoaNvElyv93+Utl0BgzLQAeu6
        zvnkHKdr99CQRrBuPRJm69loxh5vFOzc5O0MDGz1Epo1oeXMEBSRuTQoaLp81x2GVNuMsPQ6x3Ath2yx
        lzXsZK+xyVx9BrAIPK6qK+5MsZc2DNd2TdH2D2zB0Q5yDpFm+FVNvf8MOQbQqsGfe3yWpZki1X6h//v8
        h95vc9lcLvZwlG3vHu9Fu94rTQKJxnSI+Wb8Df5L/***************************************
        tANkOGJSWIgk0yRMsRnHLOjEyqvrA47N1zhxoCD0T2PTfj+z96efYp8Rpb/j2LR9p4FlVX1QROaAzwMb
        wK+Pa6hqFCCoHoaJP+wczJ196vzycrs90IClGeHuAzVm0zn8He3QNm0uJhc4F5+lrxXOM3hrw35V7Y1x
        Avp1Ns0G5+I36TarY/9n68KRuYhz26H2ZKjW66hOZA4/DTgO3WuKSFToxkpcDkRqqurUEbiPUNUmlgZX
        VDXzzcyRxmmkkt6Nx6mnkZz19NpDacKeZaCVxe6kiXw6xpiTInINeEZVr0VRdMXvQ4gBhIkfyhqAut5/
        p54Gr8QlfrDEf3g24v5Dw250zA6XzAXOJq/zWvKqLRxly1f5AKquc7vlt6eEack32506a2aN15NXg/MM
        Msw1hLsWary0FgFJ0XGXEzRDAve9/G6fXSZQ0CDCD2uUlAGUOFpzOhKw78hU9/tS4l/AEvzdWGctwJMM
        J+U1gcdSNb9ykVcReT925aYfx2p4/xJ4FhjLABZV9aGUy+R4bdNwccd4c/61OASYawCZLZDl+VMK3CP3
        A8CTR5scnSvym3WzzguD59kw6cy9ccQfKt8r9jAM6KMhDWZlbsLpwxmhhqR3qG4Gz1lYYBRQWIy00IQU
        mc0UtxTOqNpJVX1ERI6q6rKI3I2V9Ivp+WzVrWyq9ly6kAsUw7YjbJTuJB9blLb1APDngV9V1edUNY6i
        KNcHfQbQcpOAZFjtKld3U8XesfVDx4XCMbjrQK3EAHa1wxVzuShRq7z/2TagAciYD17F6WOFxj0JE6hR
        pylNonGjauIM0xUCefybph0qLUNGWVvw+18yGWS4ndL/fqEOrIjIY6mNfkpVn0hV90UqNO4blK0pW/z1
        W4E/xJoQBmfu3mRLg1HO6JM58nPFPhNC+WKeZlixwgR48kijxAAG9GmbbWLSrKOhYb0bbftn2IuTPq17
        IDrA8drxkTMFN3t2bYH1bsjplxF06ujLhwRDTEKK1xTa8LmCfWHbA+WF1QHzDeHepcIoxb1YifOZCZ94
        igmRasEtbDj9P0qd6qdTkyuz8zNH3vXjylW4cBEuXYHtbTh7Dno9+PEfhtYMnLmH7H5pfo+fAD4LvJ41
        MTED8A98OrkeDWChIcw3i9RrUBISDGYozcdpAM7Wlfzuvno9zs5p7nmnWv13ynzBXJcaTZkZqQHEBnZj
        UkecK41dH0Bgmz9XxiS0eA3+sd9vmxhkNy7PRkwTvU4ThN48RKkz7jERWcSJrr1huRj7fWjvwPo6rG/A
        uXOw24VOyR8VkfoXfMevzwAye6RQqd1XtnrDLMCZRuA6AYeRcI6dXxgBKDOFxVbEwarU3SH7PvQrVAkT
        f3bsM4FKVDkEA5cfr53kifqTLEh4CHD8jdKGS0wgchhp1gnBvnmvY/6ogPOBxQY2upqv4jysIotMcwLe
        LDSBvyoi9wL3USXpt9swiOGllyFJoLMDaxtWomd/z288b+upwmAAL7xor9lu22F1k9hoPGNsG6qwvgkn
        j8PP/mP3bg+lSX6fweaDBMoMIAsuKJT3E+hm81kcf5PvB7gRiIjyPH1jbX9nmxH8KA0gqBGM8sGNcAhm
        NNaUBnMyT00myEos/o5n61dqApnWoKABe97XAMR9zqnxvw+IGAboDIl/uw27u1ZyG2OP4xguXoI4ge4u
        bGzC6urQ1t7cgp2ONQ8HsZXwcWxV/Sy2xjhxNtk1B0qDBHmgkFtYmgwkIo/5V768aXhlo0gF1zuj1aL6
        o5yP5jlZP8X8YL7swA4whEyd8hlAlRMwZAoUynyTG+/Ye95T0Wk+0PwW5pivfKbc+SdiJwjkL82R8pmq
        X/CVpHWyTmS+Alf1B6rUf8s4ouH+FLcKEfABrAd/iE/8R/jPn4Xnnoetbau+m5JtZrf5kFu2xFYy1KZ9
        gs+22WIwn/4M3H+f36fjInJcVQt92sOc8GEqsGrJryUC2SvmZJ7j0QmWo8MsykF2aJOQhLUBD+M8/zcE
        Kf3VqNFkhhlpUadBNLx3V1WNa18btWspBHOC5EzF5y4BblNw9k2gnvj7NwDpmhGZM8vNGp1JO9evkDm6
        mky+IIkBYqCjql0RuYCNWLzT1jFYSn0Aw5JuzxJ+r2/VeZeAQvvlGPtyWcnUzupMRoiTMQClTPzetlDi
        c4bckTVCYqU4Fh3nYPMgfzD4HK8nr/KyeZEOnaEJHJj6K+m/bD8Ev3xif0DhGcjprkWLw9ERDsoS81KQ
        /tfSoZbTWUFsbC6FQe4EzBozqUofcPrlBJ5JiIDzrzQe6PbXkf43cBgwDVg5yjBoBYB0Idk54BTks6Iy
        j/dKWj4J+kAHeFlELgK/CVzCMoU7BRFwphRotbEB587D1pZlAiFUTbTJzzkSv7DPUFvIzIIJMLkG4Nr+
        6h0X/zcCRYm11VNateI1NWrMMMO3Nr6dJVni1/q/wkVznqtyZe9EexMxHy1wd3QPi9Gif2qRYo5UOrFN
        qNqJA1I+C+Kpku7usKBfJzSLEByCFxo162htlZcK2wLWQ89mjDmeLg6zgiXgk6QO4jRk9SCeVE8j0zIn
        snuzrN4SkyHGMoEVoK2qB0VkXVX/T1Xti8htzwjSIJ6z2G/heH7i8GG474y1/UMMIKReu4QOReLPzxWP
        s2n5k/D8PaeFKgUCFXofQElLHRZs9gwznu+sLnXq1PmO5od4qvmt/LF+FU0M18zVwpCdK/FdDeCmIOAX
        mJcFztTvZVEO+rVLHGE3Vi510jayab2l9pU8oUeJCWTnfSZA9V851QAatYhDs7XSeomqui0i5YUSgDQj
        9HuAd2EJ8f1YFf/MrVbD0/Hrroj8iqpucWdoAgY4i5dYhyMr8MD98NIrsLk5vhWX8KtUfnXOFaT+DTQB
        spDfcNN7sfuLauuL6wkbvfDFDZrUqPPnZ36KdV3nH3T/Dju0uaDnndYm/xiHi5j6vb8+reJgtMQjtUdZ
        jg5PUDvkxWSozpdi+/POUZD2ad2FmRqnF4dCthnZKccnFiKaNedeIjx4qMZjh8sRlyLyKvAqgKrOAe9T
        1ftE5LuxRL+CldrZFPH9yiG4jP0EP5JOc/3kPvVjYqRaypexwVbfk5+452775/z8H1jPPwyJON8PqPkh
        DcCY0vCfSaVzxgYm+YNN/EetJJOJ6CeTVkWb9dquEkm4gXqatv699feTkHC4f5ia1rjA+bTF0Tb/DYE/
        CuCgRYsjtaPMTRxLU2GH54I+I3KvPO/I0FRo1oSj88M/3VzdpiJ/8FCdOXfxFBFOH7CLl/qLqmCni15L
        95tY6f4ngB9Px4ub/gX7hOwF34f1O9wJMMA5/P4eWrJ/55lA1GjIueeW++eqHIDsQR4zaSSgo/a7qn/l
        EGApMCXcq8+d65ckk4855lDgZ2d/gSt6mb+++5fo02Nd124eEwj11ys7HB3mffUPjEwOWm6guH983iZC
        PTEHB5rCw4ciGjWhFdlzSzPCXJoZ+aFDtXyZr1okuOt9iKSu9pqUlgJr1OxqyrXyK/pfVbVtjPkn6RoQ
        P62qcyIyerLJYACra9DtwqYzv3ljw45NX702TA2dJHbce3PLhq0WHFyp7YoO66uBegNmmvATPwrf9pR7
        53cTmM12myIGPqWqGyLSSWfvNTl8GJaW4OQJuHzFeScT/JI0ECcbDsykv0nT0elwcl7C5D7ft64B+AiG
        sWZ2bLFb231ltj665SgNsLk7uoemmaEpTYwmY8f937LD0Lf7i8oLdakzH0gDVtlYwHZuRCmBNoXFGeHo
        fI1mBHN14dQBmyNxoWHV+8dXatky3zcKJ0WkCyyo6iJwaiL7XjUNROlb4s6wvW2HudbWbVAL2Hqdji07
        f7GoxmZbX91tNKDVgp0d77aaBdbc9kjf45YxpoOdlmsfuFG3v2bDPieMkKKUz/sOOK/8er74PY8C5CbL
        uLt5C1UUtmmHtwagXfjXz3Y5Ph/xvfcWtM5s1lId7CKes9Ecvzr7GzydfIl/2Ps7dm3ANJq1Kurvhowc
        ZEOQ2FGKBTnASu0op2tnhvdUbae234tpv781O/fhu+v864/NsdyCxabw5IpdWnyf8UC6/TmK4/lWOl29
        Bp/6L/DGm/DM12wE2oXUbh3HiPyhrFLAiifN3PPLyzaMdbtdaFJETnPnrWWwBvymqj4gIvn3wPvfCyuH
        4d99HOLdsO1f9a5cyZ/WSbCvO/OOJiiCMn4huz2+0BtASkWIYNSuqNuq6IkzLxpBmKHFAgusyBE2WKen
        tzacPeuDL/lFpJ8OU63hDQM2a7A0AwcawlydGy3JS9jRofQ0GBKNURSDSbUWO31ZRAja+5ubcPZNuLZm
        97PAlQyheA5fkoUCV0LbbD/7zTTh4CI0i11Kg4LutLkLMXa4tdjv2VlYmC8JxJF+AA3XVeeXiTtPWR2J
        iWcDurZ/EH6mW9fpVbKnh93bTYRf/OMe33aizn/9SEHDixhmRQFs8o2Hag/Tkhl+ir/O78ef4XfjT6R3
        G20CVJ0XpJgboApplZa0eLj+CMejE36Ncynx/waWAXx/duLMYsSZxVsn8b+aPJ0/X0d3uKZX6dKlR5dv
        r30XZ6J7WeAANWqZZC3iE5+EX/wlK/WTZBhMFE3wDK7kr3Johc5nH/mZ0/BD32c95kU8o6p3ig8AgHSY
        9dPYb/nD+YnHH4G774Jf/rfldxEK8/XPZxoAQzU5s/3Bkz5jcJNUqqpglnI1BXYGcK2rPHstYbklnFwo
        fGivYiPDHiMNMJljngeiB3lZXmCOOfr0bbhwoWnJTYEbZgaMOrZDZnVsDPhe/gYj0dVdEgwd3SEhYU1X
        SUjoapeYAX36dLRDX/vEDFCUF8zz+TP3tc82mwzSf3PMs2qu8lT9gyxUOdWNgf5g+BFmfp2ClPe8oqUA
        kREOLfd8vp9uZ2fteHmraO6LyBvcOU7ADH3gQineYnHR+gDcdO5VjNL/pYSf/SzxDx2ACujcLDo7G+pL
        jBdH8dYZQC758wJvuG90wIoCV7vw0oby6y8NeOp4rcAAVPUzInIOa7O2AFaiI/yp6Pu4YM7zX5JPsWqu
        scNQ7fWJP8QEXB9ByV9Q8ll4x1HweTJJWppM9VawoRv06XHWvEFXd/mS+UN2dZereoUt3WRD13kzeYN1
        Xaet20NGWBgNHJpQLyUvcFd0iodqj7BQlVZuMLAOvkzyh4YofUXTfb1VxO5KMbfcbeDQEjz6sN0WmtQv
        pBrWnYQ28DQ2qGqIU3fZbS0wgzSkEUApLmBI/OQ+gFzqHFmB5UN+yx1gwzejrp8BVAztDT1mk0ldEWyo
        aiNio6d0y3FeC1jp+hyWAeQEdkSO8q7oPXyNr9Ixw/UDQsR/4zWB67fj+9rHYLiqV4g15ppeISZmR9vs
        sMO2brGqq7R1izZtEo3Z1E0GxFzQcwwY0NEdetqjq122dJMuuzaJyhhc0PNsJ9v0RmVbnpmBAwfsUF8c
        D5mAMZPZ/RBWXX2Pf34ulV3GWA3g+DG79bqOjV2402CwfoCz2O94GCl68oT1eZw9V/2OwL6fVPK7xG8c
        ye9qACwdtFpGEV3s+yvEIO+ZAYgrFR061/K6V4xmApK3t9Sq0aqnK9l4a1mlMeaLqvpcOtkkZwArcoQn
        ak/ypr7BBTmPuzxXFRMIIfcHyJiRg3RYU+StRR5kJst5c46edvmmPkdXu1zVy1zTq1zQ87yUvMAlvYgb
        wViKZiz5Vsbf+5Je5DKX6DOKATRh8QDEA8sAXA3A1wYyVKr2E/gA3GeZm4VjxQhaABG5dKctYpJ+T0ZV
        21gGUMdlACeOWVMgYwAw3hzAUfUpEn8+bezgQfv3KyJjADfOBHCH+ZdmoBkJK60oHyWaa8C9B4fq/OGW
        sNi0K+dGAo8eqlGPYLYhNCM4OCMcmQ1HrKU5zwsi7nC0wiP6GF+Q3/f6Zf8ZzMTmwKTaQZddnkue5SR3
        8VzyLMfkOIejFbrazYk6ZsCqXqNHjytMjlriAAAgAElEQVR6mbZus6bXWNd1tnWLHXaINWZbtzAYtnUr
        t+v79OjSZVtDiwiMQQVDyBmj82pfNa8AwqO1x8vtzM3B4WU7tt/ZHRJ8ZrOOYwDuNiTxs/Pu/swMHD0S
        Ul2z6cA3zK+yDzDpFPHAqQqG6Tj7Mg0Ahuq+u80cgZkXrMak+vceGUAhNsYTCK2aXf7q8Kzky3ovNuHR
        5aGdc3JBODIbMd+wQTAfPFFzYtfDSId/OgyfM0eLWQ5HK8wwg5XMhesqiT80IuAygZJ8dw4zR9yqXmNN
        V/PJQAkxMTEbukZPe5znTTq6w2v6GmtmlQt6jovmAmus0tY2ScqINefqWmBCGfNy+3gjsaEbrOlq+GS9
        biWx66TayySg0FBfVXm2X6/Z4b9WKdanjbVf72iISJkJhIb3fATelec1CW6vlwGsqerTInIKZxrjB09E
        3LsoPHyoxlwdHl+JmK0Lh2asRF9oCvXUX1R3gmYi7AKZ+c0EalHqyhfLBCbAT2DVlno6FTU/cVkv8Ufx
        06w5YcHuK5pUE3CjCasmDfn1/jD5PH919ydp0qQujZyQrSceErFj7wMdYDApe4gx6b8q+PcPbsd9AcMO
        F7YZkxERruhlGloRxdhswPycZQBJYrdVH2jpARzJnx2H/AAwtP1VrUPswEKIAXSwNvSdrAGMRlDqZ5Lf
        njOp7W8lv+aSP6HoBFSgfvIEUjaj2qp6LhWmOfzVgWNV7eA5ChabQjeGI7PCQhNOLVgpfrgVWSYwswfp
        sEekySuzfWAoNQdql+mOSVLizbQULdS9Hk0gZOW7DGCXXXZ117IYHZZXT08u+0SG5q9XXkH0hXrjmIA3
        EutuVZWYAQN1gntcRJElSH/4by9MYJTdX9g6+1EU0jRGqM9vEwTt/UA5xaxcrqQvHDcbNuS4iDiNVC0w
        Uj8n4AUR+S3g+3Ay2vzT7yp5ZPcVCTEdOnzdPMNvJB9n1VzzCC7KX5V12AmqWiK0DCFNwC3PEMo0PEk2
        omqUHXvq/ANybaEk+bXQTHHr77uagAARXDQXiKOKqfVHVuCxR+CFl+zHlwUDTfxYFfa+vzhsaSTA7zik
        E2nqvNUc+rctPCLPfSZOKLBqQcq7Nn+2zc4BRHefIjp6pHgX1a00SclIDaCLnSKahS82uUUvPtGYXSdi
        0mBQlL72MBj69FGUnnbp02dLN7lozudDaj4mH4jcmxPw+jFs37X7x26VMvEXm6s2AVx4moCihVGTAmo1
        6532sxNPqgHYhxxuKzUCp70kGU4ocrtt02rd6ep/hF2Vp0hLSTqnf4QGEPqzV0r+9MuRZnM42ShFOiGp
        xygNADuH+dexmUyexPoBbskc7DZtvmGeBew3uqu79OnzWvIKu3Q4r+foa4+XzIts6xZv6Ov5tWF13iXs
        ooT3MS5YyMeNmobsD+35/7Ly/IFGfQXFhrOOFssEQssKlLAwD8ePWlUyG/sXGUrwUdrAJMN+/lryYIn/
        K8/4U4DBrq4Dd7YG0CSw3gY7OzYzcME/4qj/qo6wqNYA/FDg6MQxouViQuJ0stoFRmkAqa0VY6X/Fk7S
        xwyJJnTZJSampz3atOk4k09W9VpJInd1l22qh7UEoaOWyLNPa6ADYmyQTF/7rLNOrDEbuk73Bk8AulHB
        QoU2RjQzSu3P/xVsZMpEH8j+pN5xBnHL06xju+ky7EE0m6kTsGY/TH80YNSowCRj/jgfuY9BDO22HRYs
        SrEVY0wziqJr5YtuT6RLeC9RzK5kYyuM2jUANjfLWpFjMg3fWDH6rxD6S/HTkIV5ZK6YqEZE+lhHYHUc
        QLpqaF9VN4ALqnrSd74kxKzpKjvaZo01XkteLaTpejr5In36BSl5yVzkVX252CHnfMi2HnUMdgGRcTZ9
        oQ3Pgei2fSMjBkMjDFVOvmw/KP19Qs/+4lD8axcbHn2ctSPY1ZerZiXOzdoElo16WfUfxwRCpkKpDe+c
        i92unYV4eNlnAA+kocB3DANguD7g/bhZkXt9a/JcuGiTq0BASzIFE8Al+Criz+pGhw8THSxGAqpqR0Su
        pYwgRzAOQFWXgDNubvtYY36m/9PExKzrGgMd0MWGobadoJVLeomEuEDgO7ozEVFPUud2QtVwok/g/jW+
        BmAwQyelT+QuA0jL1T0ud6oIcWgsfZ2isJVsVfsADh2CWt0GBBm1F2R1R4UCF/pRoQGM461ra/Dsc/Cu
        x/3Vbd6PJf6nx7RwO2EZ+NPAE4XSV16zkj+LsgzMAMyy/PhE726H6n9xMlD9xAlkobhQjYi0Uyf/+LkA
        6dDbKRybJSHhlwf/B4kmhYk3MJpQx2XuGXVuXOju5NiLS7B6FKCqL5MyqnGqv1OxQPCFbVVZ9U2HcGz/
        HdOubuDgYjEox/UDTBIUNGrIbxw2NuHlV0vTgVX13SJyaYIWbicsAR/FXyHozXPpcmCONl4aDUmLAz8T
        OIezrR1dQcpzKdoicgUvN0FVJGAbO/XyJKndIghHOEpPeoWEE1WYxFF2u0v4KlRJfJcZTKr22/+0LO29
        v66GGEKVKeDCCczK1P+JWeHiIqys2NVnk2Tohhs1LBjSCnyVf5R/4NXX4bc+Afffa7WA7DFEPgRcUdX3
        A1si8uKkj3GrYYypp/19BDhDRmf9viX6L34ZnnvBmgLuKIlS0ADsJ6EFR5/r8LMraTufy2wLogiZn0da
        eT6gOI2m3cbS9chRgPwirLew4DCYlbnCH3OUl3wcAwidv1MZgouQ1jCOGXgni9tJz1V3qNrbPw7NBsy2
        YKPC1t9rcE6QOXhl29vwxlnrCCziOPZ7Lc8Uus2QDvedBE7gjqIZY/MlXr5qVwhKkqomcuzpM6jVoV5D
        GnWknpN25tjPAoEKqGIA54AvYVWYJbBOtx+s/wiX9CKvDF6uuGwyIr5ZhF6tsu/VZHir9yueL219lTjg
        2lX3nLv1NYBRcCV/dpz9Jrl++ZDNz3flCgzUaSdruKKBvI+epIfwsYv2jp2A9Nkv2AzBf/I7bf68tEfA
        LwN/ZIz5hyJyIc0VcdtAVb9XVZdF5Ofw6esrz9jlvp/7pk2SmsTDiT7eT7Uo+UOBQL5/oHHfGaKFeZ8x
        t7HLgQeTqVQ5Afup0yBfkFEQFuUg27o9kVS/GZI/JDlL4+VV9fcQxDKKsPfqfyio+e71IeMt1QJD5cEy
        d+tjRCgwmk5e0opIwAyNRprD3vEk5n6AEfcu9M+pNMnfIIt+a7etQ3AQu/eOgJV0hdslYCtd7nqYeXef
        kKr9UUr8K7h2f+bg20kzJPd6qQPQOZ9u1dmG/sT+1+5upTUTsv3jdAQg+MeucgJ+DTsF932k2WMjIv5U
        /ft4PvkGvzD4uVHv4qZjEuJ3a99KlBx6BPqX5CeqfyHJ70vtcY/tE7+z8tgz/a+ONw0eedDWeeZrNr23
        m/JdmdwZOApVbfzWJ+A/fsoupTXbsk5Ji7qIPAV8Avg48IvYTMz7mi4stflPppK/6PTb2LROv3//O/aX
        Of9GhEv70j0k8QtJQIDmu5+kfvKEn7txQ0T+UFXPhvpdZQL0sapDN91vAjRpMiMtZpkjIaZPxQqnDD/6
        sUk49jjuXqlWO4RX5o/ha0e1F7qu6rh0TgN1/Es8glfvuFCHwDbU5ij4zGCS6xvpIh1+KHApAexNgDGW
        UF57HVozNpV2rWanKltWZrABNk9gl2QHWLtVi4em94tUdTFNWnMfcA8uTSWJdfRdvQYvvmQXT/FDo21j
        6d86+waz7xnnePxPZlulAKBUi9/2x/8zVGkA14BrqnoOy1mPi0j9mBxnUze4L7qfDV3nvN468+utEu2o
        8sk0ifD1I499QvOluh/N57N5t05IA5gUITMgCwkehaUlOH48nLvuRsPXBHp9+/unPw/z8/CbvwILCwVN
        QFU/KiIfAX4J+DzwO9hc/Dcd+Wo/dv2H9wP/LcOw5eEzvHEWPv0Z+KX/25oA9uKhtM8ZQhr4k3v/y7a+
        VpRlv/rpUzTvv6/wLkWkDbyoqsH3Mi4hyDXgdSynrQPMyAz3RQ/wpnmDc/qmvYkz9OVPpx0VJLMX6V9F
        aCHJH7L99yLFQ/eourbStzBq2M7R+grnRoV4jdIEMoQIOiT5q+r6mG1ZoosqJP6tmKK7mU4Q+t1P20Sh
        P/Axe99GI4vujIDHsvUN0hWEnwX66QKoNwTp37mVhveexPoiHhCRdwEPqOqSuJrR5iZcXYX//HvW6dfr
        2xGAwrBfxgiKWkGI6KuUQ/dXW1mhfuJ44e+SDgFWplMbxwBewS4d9UQ6K4s55nl/7Slq1Piq+Ur5RbG3
        wJhxda9fYt9Y9XRiBlJFtFVE7P98w87XANzrQ/fOXqd4ZXslfrCReIeX7ZyA/YAIXL1qt7/wr2yy0I98
        yJoBNkw4M3Y/lNrgH8Karf8cqwncMAaQYkFEloAPAu8FfhTrjFwohMwbA5euwIsvw//1b21y1d3d4eo+
        I5hApgFMSvwuGvecpvnwQyEN4OU0z0cJ4xjAyykH+YmsoCWzPBo9zmUzDMoaJ+VDdfxrx2GklC+VacpM
        y1rCuHZH3SsP2Ck2EN4GVPegxMfbTir5QwQd8sy7GsBeceK4NQMak6x/eJOQfcwXL9shwl/4JbjrJHz3
        h+2sxWLMe5Y38r/C+gW+RVU3ReR17OS2LSxj6JBGxInIucyex47ZH2XIWLLZsMeBpqr+iVTtP6WqKykz
        KC5h9Orr1mH6q79u4/w7u8OQ31DYbyD011f/QyG/7lbm55GZJtH8HDKbB++adE7Phoi0q3wj4xjAuZSD
        5BfP0ORMdC8rcmTEZTcHe7HV2UPdG1KnSqr75yBs14c0gVC7Vff2Cd0vux4mcDh1ZtfHfSY3GSKWmLbb
        1ov++KPwnietaVJkANliAh/BvsEPpVNgv6Sql0TkPNak3WCYZixzZEXp6sin0tRzETYD9WGsiTEnIvlq
        T5UZii5csur/f/rPdn2Ffn84jTr/W+pwWxErUSX1CWyZbREtLCCtFtFw6XGTqv3tUUuqjfzLppMHrgCX
        sPMCVuo0OCLHuC+6nw9E38qb+gaX9GLaoaGU94nV9xOMwyQqd/EejgR37P5RvgO/3ZBfIS/XQiPVOplT
        pqFzvuQfxQT8e4bgx+RkhD4hsb+SvEyTJnfXTldXWjlsI/Su7vNEvDi2E2kuX7HzBR57GN77bv7/9t48
        SpLjvu/8RGRWVVd19X1OT899YmYwuEEQB0ESBwleJmVRWssyD1OUVpaltU1pJVuPPp6eJe9KtiTv2s+S
        bC4tS0uvROsiBQoQDYkQDhIEwAEwuIHB3Jjp6e7p6bO6Kitj/4jMqsisyKzsnh5gBuzvvJrsiozMjMzK
        +P1+8Tu55WbYtkXrLJoKS4les48LIboDLXgVzfkbFXICnQHBpM8DpaBmIkEwXNhmnysLCzqC8VuPaFPf
        w49prj8/T2tuPz8qAYRtgUegzu/Xmv3H5PjhPvOT2zSOu3kTMuoDUEXrQo6lPdJUAiClXATwfb9RmFEK
        SQcddIoyA3KQc/WVm19XoifIekbbNVZ/tpRj47tsIrmtLfyeRjhsZP4SY0HN47Wrul3I25J1XjxMv4Is
        UEpPuIUFmJzSk6inR1cSsrvVhunESrad0aGI1O+J8Oqay588pcX/V1/TBCGc/HrgkU1UAgj/bJUAskJ2
        lnD6enVmZWNkwEzS2j9EJtlOCPEVYAT452HbBjnG+5y7derr+uvJx2YIjskCG6eOPybV0KL6LX2zmAxb
        OH/85bTF5MfE+ogLb5p4Hz82iQC0e0yme2+7fpbtPmd/+4N379ChwSdOvjWafxNxD8RwrErB4ee1ff2P
        /wyKJbj1Zh3A9KM/rAnWjm1rP565eTj0HEyc05P9hZe1VFJZ0sRgeVlPfLP0ue8H6b/qzX2Bn4MKdQA0
        M/yGHx/TBVhFXh1TEihcvZ/SnbfjRKsBVdCm0VRFaNbF3SwxZUeOHN2imwIFQ+TXP9BqnXzSELevX4yz
        T8v54vtafNfbbyPifrxPOxE/7Xytt4gUAomDIySO0Nl7dUr2HAKBJzztvSt8lFDURR1f+NRFXVdCNoiA
        KzIo+AoF7YwTTv61IgLtuH8YcNTihhxsa56edAgtdk9OaW58blKXFuvr1XoCx9XecVJoi4YUzXsw9Ru+
        H5Uk6vVAfK/r36BW04rI6fPNz/kZu4OPLcov3h7eZvB/Fh5g6yM6tA4AJ+IB6NNm/Q/ZCcDDxHIDbpZb
        2Sy38or/Et/xHw3Sc3vG0KKTfzX+/vE9SX2ycvek62VS8JlPPC4JQPK63rf0sdVxTvr1LegUZbYXdrAx
        t4nt+R0UZZG8zHNtxw10yS6eqz6Dpzzm1RwX/Asc949yrH6UY/5RFlmgJmpgzcCdgJ07tKj9tW/o7zZC
        kCXSbzWIRx2Gk0mGW6m17hUJDz6k+/7R17TlYnwjDPTpDMeDg7rk2PgY9HZr5yLH0ftCnJ/R3DzEa69r
        N94jRzVnf/KQJgqLhlQdivkh4YhwfqU5f0hYPK8ZEahUwzoQrvVN7u8RlQDia3+MbX7XDkq3vRvZ2Zyi
        gf//X0CKuy4ZCUBgCpRoxyCXpraVbtHDBrGRE+pYJM989OVaqSSgLO9OMscOv2chDFmchlouaePmwXdl
        6xPvGyfhtLYr2y9r7B/OjZATOcZzmyjLMpvzWxlyhtiQG6MgC7jCZcAZoChKbHTHqSufJbXAglqg7Hcx
        6Ayx0d/EJOdYVAvMi9mWkurodHCLQEfo9wHo/IBLnfG+a4skYhHn+mBYNSz7wmN8X6/F5+a1Tb6yrD3x
        5uf05C8VQUg9wcNjZud0mi6EvsapN7Uyb2pKl0sPJ3DL0tD06As+oXOPGeUXMwOafCHZw09Z2oLbLxa1
        6a9U0uv/5pxbFEIsBkF9qa7RmQiAlHIi8ID6Nnry3x7uu0ru5yPux/lq7b8zrxKSTK5o8tuPSRPfW4/M
        wNVXOoQkzm8jy2bfuDQAq+L87+28iwF3kJ8e/Md0iCIj7kjisA/GqlGbeLr2XU77p3i69l0WWytuzQoh
        XkLXhGiaBbZs0ua2Rm1Ay4nNMvFrGTMQTvCQ00qpryFFUxIwMxaZ1zx7Vn9ef0O3h4VHhGguA8J7CveZ
        9Q/N84VtSZPfKgEY3N+UAII+odNPvL6fTytRsL1O7sYxCgf2kds8jlOOcP+jQojjUsq2xVRXYuD10WaF
        UQwCMCiG2CX3UKKUyk1tS4CVTNKs6/esfVMJSnziWya9MveZfdo59ySdJ3bdTlmmQ3RwQ+kmemQPd5Xv
        oex00SW7yLF6x5whOUxeFHBxbcFcM+jfuIRJADo7my94lmWDzSEpCfGgmKQ+cWISTvowpkHQXBbE9QZm
        WfPGpJYgFA1uH+8TnidOAMwxJHn0oeyEwIz9V6FTj2qZ8PqjrFKB+V32dpPftlWv/w0ERDxToE5mAhCk
        DP8bdHjwZ8L2jWIT3U4PXaJnxVz3YvuvzDFoRRdq3Wbh/PF226fdeQP0yl4G3EE+1fdZdhR2sb9wtVb4
        XSQ2OVvYBBxwD7bsU0pNBr/xKHB9Y0dPd1DUMWXtHxHPg0m1UhOfubXtj3PgUAcQwg/6hCnLrMuHgMs3
        pIaACofEI+wTz39ou7apzGvUO2wV9a1EQNmr/dg+YJcAnMFBOg7u1ybAKL5DxuzJK5YAAkIwQ+AgURIl
        crjslweossyh+tNUWc7k8ruaibuS82R2Jopz/HBrmbgK7L+Kn3BcCgGwvesj7ihb89t4f/keDnQc5Jri
        dXTL7vjz9NA53icCT7fQuWUCrfTpDxxbOtDcfBDt4jqKHVXgSJAH4gml1G0RO/joMCx12ydDXJOoVKsH
        Yhzxyd6wlzfbbIeJ+Do/zt1lIAo4snV84ZjDQqfRmHlQMjpuk/unpT8PRbh68DKEnN/cmh/fbxT6DE17
        tlLfSToB8zVzh4fouPYgTmtJ9WfRlru2yEwApH5gx33f70XnCigBpYIoUKDAJrmZOWY5XH8mXe3Iyib+
        arn7qvwObBy63b40rr4K9Dp97OrYw3vK7+M9ne9N6uahTTyn0SL7PHoSvwwsKqW2onlaTxClth1NsNMI
        wEngmBDitZbQ0d5eKC63TpokNIiAMDwVUzj7SmCTABp6AqLivMmh45w/bIs75cjY+eOSQAvxSpAAItxf
        RcR/czLH/7aJ/BB9rcKt7Okmv3ULotSSBeg4+p1oixU7eQe5Ar6ELh328bD9fe49XKdu5EHvGyyohdVN
        QAsuFbFo9E2auOZ3v0noW36dBO6eqPBLQL87wE3Fd3F753v4W90/wJDbkvvSR1P1/4Tm5rei1+l7jdRt
        i4BvaPBdggSvQWmoh5VSxwMJrj/Y/0xw3odCt1jgTaXUs0KIrYD2LhECrrlaR7W9+nrr5LHZ6+OwKdCC
        9jTaGz+HwJAGggktoEmgHAkIkH5USogoAWV0nOH3xtIAbSWAVr1HY5DhZKc58cPAn9CHwFD8+QH394K3
        LzSahyY/vVURRyCTMISXdvp6KRzYT2HPbp0DsEmYT9Ik5u34MLAKAhCYFo5hVjoBBsQgnaKMpHWduuZr
        9LVGfHhWsTXhe1rfFdx2TuQYzg2zMTfOjsIuWxcf/aO+SuDLHnD30TS3VaHrwU8EgSHT6JfjHHrZ4KIT
        Rs4KIV4wDqsG/Q0TkmjqAuJr6yQumQWrsBCEUnrkHOYYfEMCUQocizQQ5+Qt+4L1gBAJv3E48WMSQEQp
        GDUPxuv8xT/QeimbEEoujzPQjyx3mtl/Qf+mFaCSlAEojtVIANPA7wWOBh9Ec4hyGB14o3MzZ9SbPFF/
        fKWnvqRI1fTb2pMmtk3Ej/+CWQhKAFc47C3s42DHNfyr4V+mKFvs7a+gxbmOIAvNfwja8xn91UtKqc3A
        uBCCIKLND3QEAD+CDp09jFYcvQzsosmYYrBoyhMncTBJ4mK7seaPP7a4AJV0ZmF8QEsEIpAOGqMMOWPd
        MPGZ5j/TvBgGEZlSQnifNj1Hi/bfkACgmczU8/SaXzVz+JucX9HM8W86/5gKwvg2PzZK79/5QTr2RBMQ
        AX8BnML6u9mxGgLgox1GKmhFVIcQAhmISz2ilwpLLWbAS45Mc4FsnLzd31nIdQZoc7Rk0B2izxmgKEvk
        m6655gR0ARkEtjQcdMIY8kaxSeVry5YMFFpBHL/QCpzIAt4gHuXg/P3BOWeAbiFEh0Ek9PkKBe1qmyQB
        pCG+P8YRk7btpUdhzXeiwmsK0VwuNJR2xniSljGQvpRJ+5j3F+H6rU49WNpsRLExCsdBFPI4Pd1m8Y8Q
        82hT7qUjAAaOAn8ghLgbnRcNgE/lPsd5Nc0Dtfsv4tRvMVYyodPe1nDb/p1toFOW+ZXRf0unLNPt9Ji9
        JtAi+2a0wrX1t1qu6mwzx07o6j1z83rt2dOjJ+u+PbrAZ2eqF18p+NwCEcIQjYgTAm6+Ac6c1fntzZc9
        3A/phMBi6jO5W/g9bIPsj9LcynAbjLFxF6azjwjW+qH/vKkLCO/HZgmIEBOD8zccgaK6AF+pCFe3SQBx
        zm9b+/sAjkNu00YKe/fQ/YF7bI/jL9EWgEtPAAKX0WPo9WOjvVf0IoA++vHwmFOZrBErR5JiZrVImdAt
        77SNVK8Co+4G+p1+umQXHSKiyfWNpJMuOlmF3uN5sFRBnZ3QwS9nJ+DNs4jZ2WYYarmsq/qcOaNLffd0
        622hAOWS/ru7W4u90QKc6Zib10E3K4GNM5pbWjm+7dHaHrEI+setjk2JQEsH4WwIJQERljwPrRWm8s+0
        IJgWhvgypkEAtORlEgTz/uJOPTZtf5IFIPocFMJxyG0cwx2MZh0nCPsN3X+tv0MCVk0Agoos9yulbjDb
        9zhXsaAWOOAcZFZd4Jn69y5+cmYV7y8WaW9d0j1kubeE8d/e+R6G3GG25XeYzT5NTX4/2nwnhRCooLiE
        OnoM/0/+HJ76HjzyOGJhEVGrxTic0BO8VNRBMCNDOsXXnl0wNAjXXat9/K/el+EG0C/38RM6203jvjKI
        /uGx5tq/jTLMJgEkXUXE/g4nvICGOjpc+0hTT2CaA/XOQGwwdADxPkn3pGhKAEaAT3gf8QAfc+3fJBJ2
        HwCTlYuOPF13v4/Czsj7glLqhSAB6pks7r8mLmYJENqiX0XHCOwlCBJycHiXcyun/JM8460BAVgJVkos
        3sqxxXB98SaGnBZz30RQxGFMCBE69KCWl2F+gfof/on2b3/023DqNKKyjFCGmQuaOgCBfjHnApPwUkWH
        s5Y74Y1jWhL49hM6JLavV2fU6WtxKtEJOGo1HRW3ZESXWtb1LXZ1y1rbvsZVkcmfJGSZV7SJ/9Csf0KL
        dCCatVFCc6JZ6LTRORT/g0bznVLBf+G9xZYCoZNPPbgnM66/dXJHY/ztTkDBHefzyHKZ0g3X4Q5H0/EF
        /iDPE6v8mwUXIwFU0crAw0CfkSQRF5f3u/fwQv0wX1G/u9pLNGH71VXs+1qd/1IiNtbbS3faCMAZIcQj
        6LLS443l1VIFNTWN/1tfgtlZ5JGjTS7X0HCHEoCh6fY8mJ3VnzelzlRrrmt7A33Brh06B+AeiwnyzBmt
        b3jplWSO3yIiG22WtT8kc/6Wta+xPw4bEYguA6KSgAq2MhiPiN+PxL72N3UAJqGLOQDFJ7PNuy/sY0v9
        lbQEEMUOZE83XXfcZsvReBR4Elqju9phLbI9Hgl8A+4NGySS3c5eBIJ3O7dzVp3hSN1SUDT+iyZN5hYK
        nNLPRixsWIlIH2dZ7RAfRww78rvokl0MOyP0OY31nId2yBkEPqiUGjN1K/Xf+hJMTiGOnUDUag1FV4uD
        iykJmG1JSq2lJa3Zf/V1OH4SjrzROuDFpaayC6KToIXrx/42w1+tor+KTHab2Ss8xtzGH7e5ldgJgo9q
        lBRygr0N4hBMZBGu+c3nF4dx3yr4Huf8NgVfczlgL/wZJQhRXUjnNVeT2zCKKBURomHQCbX+LwMvBJa5
        FWEtCMBE4C4+DXgAACAASURBVGzScD2UQjIiRplXc2x3dlKv1zlCckXhNUP4S1+qc2dBok2qiWFnmEF3
        iC7ZTWfT7h+WZC9jcv7w8g8/BtPnEefPNzTdkclvs1+bhCBpW/P0p7Ks22wxZPGJHd8Xb0/qE341tvbl
        gH0JkEazoxO9uQIyBcVw8kujT+Oc8fGnrf3DbYuZz67cs0k3aZKP7V5z4xvJjY4g85HEXBU0AQhjQlaM
        tSAAYQ3BrwBPAz9O4D46LEb4bOHz/Hn1T3mi9nj7SZQkEdgmdnwpELYpy9+2/e2wFssC8600cKDjGnbm
        d5GLpuOaV0o9GfjtN8K7/EceR52fgSefRiwsNI35cY4fiv4Q9dlPmvxpSBLdbVw/3m56xpmcX7XmtK8H
        D9nm9JI0IZJ+FpPjxz/mpDe3YQ0rMIiGiroa256WjUiF9xJ17rFz/ni+v+jW5isAg5/5UdzWoJ8XgG9w
        EQVQLpoAGI5BE2gnlYbiMi/ybJBjdIuexOMzIwt3XysJ4BJOftC2/x6nFxmL8AtqMETMOOrCLExNI+bm
        YKnSPCJJpLeJr/FJn82DMIp2a/80CSB+SMLf7b63IwJxxPUBJv1P2mZ5xeLfs0oytmsm3VOLBDA6gtPb
        Eva7iPYXWbHoH2ItKz58HU1YP42unTaWp8AmuYXbcu/hJwr/kEdrD3O4/mz2M9qeSpIkEO8Tlwzi50uT
        Q1cC21uWNI4AtxTfza3FO8iLiCfXoFLqY8RcfNV//X/h6UOI5eXWyR8R/2ld+9uQJp4nTVyrn3uc07dy
        ftMuHuXw0TDYNM7fTJ+oz5/0E5kSgAz+iusEZOxv/fPoszUVhaJxnvATeVyxT1yTHzfxhQE+Ns4f7duU
        kKDJRXs+cDe54SEK27cjm1F/FbTb9qPAly/G63bNCEAgCfhKqUm0JNBQYhUpMSSHKYlOJBJfZXZUCk5u
        /J1E0s2+8cm/Fhw9DbbxWCQABwdHOOREPiL+B0pUH53HXiuoax74de3pV6loF1/Q3nntOH87ZPHWs7Vl
        2VrMfZDMIeNtrd/bOwbFH78yeug2EdsXvQ6Acl2EUqh6PfbKRLlHOw4fvYfkfH62+4vcW/Abu329uEOD
        CClNz8xqELI9L6Vc4WSK4lLUfPottCb7d8KGfe5+trnbmfaneNl7kXnmqZuBZiuF+aTiKmBb30ulGIxP
        +DbXG3FHGXSHGHQG6ZKNHO6eEOIVdIHJrY3OZ85o0f/4CTj1ZpMbmbZ+oGmvvgi0MdelavxTo+Fa7dxR
        7pldAjDTlyYRgXBrKgFBc3dF1AxoHkMuh9y4AbWwAOemIsfGhcc4kbKNM87dTe5vk3xsBESX+crT/8M/
        SOe1BxGFiPLvuBDiN9G5IC4Ka04AgnJiFaXUbFiVRSBxyTHmjLPfPcBh7zlm/QttzwUkc3CbqJ1F9M98
        Ixn7pUkYsX0doki37MGJVpny0a6cUd/7C7Oo02d0NtqW82bg9qYmu52ImIUAhNuWv1Xw5hqcP6YdD7dJ
        Ci47d0zn/Gl35Az0g9BcXzoOslBAujlkLodTKiLyOZyODpACp6cHpMQpFfGPHsc/F41ibc1rYSdi5ndo
        9Qew909+HrmhQXKDA7i9Pabo76N1RDPopB/RxC2rwJoTACnlE0qpshDiODrIZHtO5MiR47bcHQzKQX59
        /v9glowEwDahbX1SlG4rxkoJhnnNphtay3j63X625ra1+P0DrwkhRoEDjSG8cQyeOoSYn29d+4d/N9qI
        EoO4GavdOn9FEoClzTzG8BdI4/RJ+5P6muvjNH4gAGf/VQgp9Tq/VCQ3PIzs6cbp7cXZshmnrxdnbBSR
        z+NeexBVraJOn6H2tfupPPK4de3feBQJ9xSXAGzefWkhvnFiWDx4gPIN11HYvo3ccMNZzEev/Y8KIb6Z
        MMQV4VKVffWAh9DKwO1h4wY5Ro4c29wdLKolTvknMMt4tUVGTmvd3+6cJglOQ5r+oU2fUAJwjeSegatv
        L7HCK0xNw7HjiOXAPh+PzGu5h2DSK2ObZbIntduIRpjxNuT4FvNgszyb+XKrFk5J7LvJFSNcM5cDKXE3
        jyNcFzk4qEth9/XqbUcR2dONNGoX5rZuRgQSgMjldOKMQof2puvt0eJ1d1czB4DjQE83YmwD4uABOHMW
        f+Jc67MzxplMqNq795p9wnM2tvk8slikuGcX5ZtvxIkGa1XQMf/PWQe3ClwSAhCkqHoAXV75B8P2jc4m
        Njqb2OHsZkEtcKZ6emUEIPGCtIr7K5EEsnL8JI1//LrmPqNPURbpdXpxjbTegdhvJwBHj0PFov1PvA8L
        EQiPC/enTfCWLcnH2awBxqMwP7a1rq2f7UMhD65Lbs9uRLFIYd9eZFeZ3I5tyO5unP4+nRe/vyVCLjOE
        4yD6epHjYzjXX0P96WdQMQIQH3M7SSWdAETPZf4tCwXcvl6KV+2l69ZbcMqRUO4K8Kdo09+a4FJJAD5w
        CO3aej86pr0h3t6Vv4f97gG+V3uKqm9Z4yZA2CZcSyfL/vhkJbYv7XyJg4kd0878Z9MsabjAVoiW6BVT
        06ijx7QPPmTT7kOrBGALZTW3kbbwbwsBCLdJnnBWzp8s3kPrRLBNFlWpgJD0/P1PIbvKuKMj4Lqaq+dy
        iHxeV8ZZCxQKMDiAKhWtbMlG1Eyubt6/LdIvSRIylwDFvbvp/eC9lA7s02v/aL6/M2hnu1Xb/eO4JAQg
        ME2cDqoJvYQOaW0QgO3uLkbUBtxLRn8syEIEssKm/W93rSTDskZvEP/fbFlcRMxcaOa4h/ZEIC4BxNvC
        7+Y2HG/YlkQs2kkAxqlsnL5lqAnbln6e9qHruOFaZE8PTk93Qs81gOsiOjshl2sR6LJILe322+5Vxb7n
        hocoX38NuZHhuNvvLDCzWpffJFzqGTihlPqvQoiPobPY5gF3k9yEj8+niz/GpH+O31v8cvMI25tgU2bH
        54T5dFcr/q+UENgQTnLLWDe5m7k+fxNdoss8QqJTcEXPU1nWteriYnwcynxVaV44SfMfKvJWY9s3zmGb
        wK2icDTEN0kCMLmjXasOC996BGdwgK77GjFnaTgUHOqhueV0kBV5Hp0zb1YptS3IufC/Rm7N+JDwd1Sq
        ac3tH5cAbNINRE2bsqtMce9uum69he733xnRaQAopX6VjMU+VoJLSgACXcAkOl6gce+hB9ygHORivJiS
        L8yls/2bSJIEEiSAvCjQKTttFX5aE+4rv7muXomTTztY2XEbImDrm3LaeI+0X7idBBDCn5tHFFpy4NnP
        qbPjhARgEc05Z9Hms5PB9xLQklC/3TiTvpvtSX3T7lE4Dk5nJ7LcidttlXLOsAZmvzguKQGQUlbRS4Gv
        A5NKqb9nhg3/o87/nfP+NH+1/E3m/TmO1F+3nyhFhlSxdXVEIkgT023nz0KLspzL1kfAaf8kz9YOsc3Z
        Qa+0JN6wXSYtJDXte1L/DKK8jSgniezhNonzx7mgydXbicvmTzh3/wO4G0bp+aEfMIc1iZ4YvyiE+LP0
        BxCF7/v/QAhhJQDtxHqT88fX/GmhvfH7B0AInJ5uyjfdwM7f/r9xuiLSIUqpLwfZt/5aSrlma/8QGUu9
        XDSqNKlwJN+8RDLubGbYGUEitdPmSt6M2Na6NE36JW3nMQ+zSL8XAx9FXdW5qDoJaRM47ZPQN6xQq8K/
        ld19lYRtc79q/Gvft/W4dn39uXn8uXlUva5To2mEWZLzSqm8Uirz+xzkW4zI2apeRy1VwKtbxmkfr+05
        Nfe1fx5ISW50BHdgAJHLNU2T2q3eC+bMVCDRrDneEi1c4BR03Pf94cDX/XZ0dRvKootf7flNnqo+wYve
        Cyz7y1TImHgy5LYq9h1aubD566Tty4okX9EkCUHAMhXmmaMeWf2lXCBt3R/X7idp++PfY3n5zKHHX2JS
        2uMvdBJXt5kAkxyBkvoDLD78CO7GMfy5eXDd0DzWG3y2oi1NJ8moIQ90AJsjbXNz1F95FX9qquUek+4r
        yQyY1Nc8L4AslRj/+S+QGx0hv3HMHM6iEKICPAAcUkpdhO98Mt4qCQAgzF12WCkV6gQQCEqixKAc4qB7
        HRud8WRO3e5DdGtjgo0+NrJ9iVGlyoJayEgALIhP8CTubqlMq5RChZF5KjlLbVxZFe8TNWkpTL5vPk7b
        RLBLAMk/IfH99Tq1k6eoT07Fn0wPmqGshKG5aKV0E76vvQKNCMYs44xPdGLPJN433OY3jJIfH6OwdQv5
        DS1lG48CT6CXOZUrWgIwcAgdyHA3mmJLKSRDYpi6W+fvlj7FQ5W/5PXaq+0npo3zxiWCeN+LQbu4gnbX
        ELCg5plSk9TI7vvQgGnas+2LbxMkgCSOjWVLwve0PlnotO3atvPEr6OqVZaefBp34xj5rRHmPYaukRhW
        UWqLQAkYdSDw6qjFJVSt1nrt2N9xwpmVN5nfO689SG54iJ733mEb4sPoZB+vBdW4LgneagIwj17/fwN4
        DfghgnVYWXRxTe46putTvJx7kRPecab9GKVPIwrxyW/x2rOZEVPPtxYwlgpzapYz9TepquXmZXS59Vn0
        b9H0BiwVdcLO+YVmyuk0ET/WrtqI+FFTXLbinO0ef/L5kyZM1CU2SfwPz+97HtU3jtmSYg4C24J1/aqR
        JIWEY01zcjIlJBK24TnDmn59H7mP3OBgfBin0dz/e2gfmhUn+lwJ3lICENhh55VS30RLAx8jJACyzNXy
        Gs7VJ3ghd5gFf6GVALSDjfPHiECk71o5BpnXS2gTAuaZ45w6S81I+hPY/2fQz6FJAIoBAahUmgSgMT47
        d7d9zyq+xtviaPdIskgUWa6dBuXVqR0/0eIMFGSk3kpcpF8lbFJJ2vOLb9s9T6ezjCx20PvBe8gNDMQv
        P4FOs39YCHHJE2m+1RJAiFfQVO7rBFlwwx1X566hvzxAjhw1VeVM/U2W1XK6XGrzujN/CdM5J4s78SXC
        tD/FvJpnxp9hQS1QokSgFB0mro8ZHICtW+D8TDNhJ1gne/w24som+wucEpCC/dFkkQDCv9MkgHgwTDuz
        YOPatRpLh1/QgTwGhBBjaAIa9Z65CKSJ8DZ3X1OqIXZcHEN/55MU9+4mNziILDaGXEXb+Z8Afh8d7nvJ
        8bYQgHBNo5Q6EnhoNfYNOcMMOcOMO5vokb2cq2eIe8jC3ZMkgYtBisa/8afxd4UKFVVhmQoeNXRKSgHx
        tSg0JQCntdx6Ayk2yiyc39Y3qU87ZOGAWa6deg3fpz45Rf1CS/GbUHJa0/fZNs7497R7SkLxqj103Xwj
        TrGIyDWG7KGl4zNCiJeI5Ya8VHi7JIAQvymE6EebBUsYocOf6vwcHy1+gl84/094qfYCb3qnaeV1BuIS
        QRIRINaeYRKvNb7nPcUyy3wo/1EKSUxr724t+j/zHEyfj+5LUegltScln6gnHJt18icdZzeH2Tm/zf03
        fg96sHWqJ09RmGpZGvajzYEdSikXnWWp3dCHgfHIvSwt4Z08hT87F3lG8UCf1vtKll7MT99HP0TXbe+m
        5/13Utg0Dm6EuB8B/kVQ4qsSv/VLhbeVAAghpn3f9wPdQEQE7pW9lGSJbtlNUZQQ6CTOmd/MJKuAzUqQ
        dGzafow+Ft+DxPdPwKJaZM6fbbxYVnQUoLsLnOAnsoj65hCSOXD78tNJ58mKrGPI0tc2JvNv4XlaWx/U
        7Qsmepjv0yWjaTsIvoq+/76PqtUaZsD0e7JP8vh49aCFzmnQ20t+wyhOuYyMujWHLstn0MV235LJD2+/
        BBAqBj8H3Az8TpAgQxZliSIl/mXvL7OoFvnbZz/MvD+frdpw0i8RlwySJrpNeliBRGCd/MY1/6r2l3yv
        /iQfK/wARbs3KlxzNVy1B373KzA5qXUBlmFGOVVru41bQfKLHX+RsyLp2uG+LJzfdi82eNPnWXj4UV0q
        e8d2c9dBtB7g4TanCPUGW802tbCIf+xEpmfkW9ptfQFK+/fRc/f76P/Ex+h+z22RcQSp834KXRLusbQx
        Xwq87QQA/SzDCievBEuChldEQXSgUOxwdzHrX+D56nMoVHpm4SRfAGXZZjneZl4M/6b1vGkme4AltYSj
        XJZZpqqq5G3WKyn0+r+/D4aHdH0APzTaNS+b/AKunPPH24XrgpSIjkIjLl3Vta1cBytFH2BWzt9OGkiT
        AC4W7YLPksYfttkIpK0v6Ofn9HSTGxkmP74Rp9xSin1SCDGDVv6tqKrvWuFtJwCBuHMkSHP8E8AngZ8J
        929ytcPHrw38e87Wz/Cpsz9MTdVYZCH5rYhPUJu7cLwPlr7x72lKxDhxScFr9VfA18FBNVllPOqRqtHR
        oXnZB+6CfXvh3/zbZnIQkjm/uS/pEz8maeKJvl5EZwn3qj2NSDx/bo7aU4e0x9xi02U7zu3Nc69UAkgi
        AmsEaXysSCNMacQrDrevj94Pf4DuO+9g6DM/2nodpb4qhJgQQty/sltYO7ztBCBEkBt/Qin1YlAddyeG
        JNAr+1DAXcV7maif5TuVx/Dxo261l+BtyYQESaAdXqw/z6Q6x7i0EIAQW7dAuQxdXShnURfqpJ3oqaz7
        4tyrpV0KcFyc8THk2Abc3TsRfX04Y6OQC9KYLVVwdu2kfvwk1ae+h5qbx19cNM6pr2Cb5K3jtI/D1jfE
        xepmlVK9QR6ABgFQNQ9/agp/5kICd7e3Jb1uQghyw0PkN2+i67ZbKOyMLFNQSk0LISpCiKe4BDH+K8Fl
        QwCklIvAK77vS7TH4OcwCMC4u4lhNcLP9v1Tvlv5Ds8tP8MyFeoqg199FsKwElffJOkh3JfxLX2w9g2G
        xDD35O5L7nTzDdoaMDIMFy7A4lIm7pO0L41w4LjQWSJ3y80U7r2L/Pveg7Nlk3VYlQe+if9v/h2114+g
        IgQg+ZNVUkm6p7VAkH15GMNnQFWXqb/0CvXTb0b6phGi1PFJSemqPRT372P4xz5rG8NRtNj/1WAJ8Lbh
        siEAIYQQE+i8Z6NorejdaGchHBxGnTEO5q/lc90/wfcqT/F45RFqqmYnBHFR3mzPEsVn0xuY/fyEfYrW
        RD7x8yh4znuGfjHAkfrrFCmywYlEg2kU8nqt/SOfRJybxP+N/5h4i6vlrCKXQ2wax9mxjfzd78XdvQtn
        53ZEX3JNR1Eu42zfijc5hTpxirgDzErEfdtyJAkKEB0F3LFRnO4Wj8BJ4HQbE6AMFM0t5zXHgOV7mgQV
        IjcyjCiVGPz038UdGopfeybwe/kfaFf4NY/vXykuRwIwjU7h1I0mANcTEgDhMOwM0yE6KIgCKHh6+Unq
        qt4+wi7OmW3fSegTJxhZXYqTxhBsX/Feokf2cso/QY/oZQMWAhDkhRMf+SDq9JvwG/8xE3dM6mNtz+WQ
        m8dx33UjpR//+4iOAqIj3alOdJZwNo0jXnolct4kEd/cprkNZ0I+T254OF4tJ3x3JmhvAZAthVgsY7AR
        13ZwB/pxenvp+/jHbE5cs8Ey90Ep5ZMZT3lJcdkRAAMvod0hB4GNaMVgHqAkSmzNbeej5U+wJbeNP5z7
        Ck9XnqTiL5GaZtz2KyaZ+9LW9eY2iRiEYfdGBa+WPsCSv8j/Vfl3XOXsZ4ezE5ecJm5xbNsCfX3wj38K
        cfQ46o+/1nJbq0k0krv7fciRIYo//ZM6135nKVpeXOMQmlvdEjbIvl7cq/cjv/dMw8HHnNArcfKxxcnb
        7kVIB3dwEHdgABkd52wwvlnaRwOOK6V2B3oAjcoytWcPUz9+IuXq6RJKx969OH09DP+jf4gz0K/HF63l
        VwH+DHggWAJcFrhsCUCwNppRSr2AVpR4BATAFS7dopst7jYKosC3Fh8iJ3IsXyqJKguXtxGKDOf1hMfz
        9eco0IGHh0xSTnd1aZPcwQNruj52xseQG8fIv+vGtG6TxKLSRKGAHOiHwJc9bSkSb7e1ZbonIZClIrKj
        oLPnNFENxlcVQrRLnFEWQgxgKgHrddT0edT8QuaxxFeXbl8v7sgwpRuuwx3oj4/PF0JUgWPo5e3bYvKz
        4bIlAAYeRv9YZbTy5l+FOwacAXqdXn6u/xf5dPfn+OK5n+eN2hHmfOP5pv2aSTb9eJ8kCcAsAyZifcPX
        yyYJhGxEgI/PKe8EC/48n5r7IT6S/zif7/gH9vEWi4hPfhxx5+2oW27Cf+B/4n/1T2BpCWrN977dOhog
        f+/7ET3dlH/tl9PjDTRm4zEbolTCGR9DdJUTufpKOH/ccmGi8bgLeUq33Ezhqj3xLkfQwWVZQmfDqMsm
        pa3XtfvvUpOBtFuehD9j8eAB8nt2MfhTP07H/qtwenvjEpQH/LVS6reEEIcDX/+3zNOvHS57AhCkRcL3
        /YngBVwkyObiCAcHhz7Zh3Chx+mlq97For9AW2chiE7etF/Z1pbVXyC2tSkH69SpqCXO+meY9qdYUAvk
        yTfqJoQTT0gJnZ2onipiw6iOGOzu1ierVKBWw6zQY4XjaEmiv09XwxloX1EnyLUXfVek1FxOOta1vk0R
        Ge+jt+m5CCK0V0icni5kayGQqpEJuB1cLC7DyvNaw67N/fEGKZGui9PVhTs4oJcm0dj+MD3AolJqRghx
        hjUo573WuOwJQAgp5R8ppUroNd41wM+G+8ZzmxhnE1/a8PtUVZW/d+qHWPDnebn6YraTp2nMkpSA4Tb8
        xC0CMtZXxo4xrytgwV/gefUc5/1pvuU9xI8VfpK78vfSSbmlgIro74M7bsW96Qbcf/oFvAf+J+rFl1Ff
        +QOYOAczyYVXc7ffgty2leIvfRFnfGOmxyOE+Hi8TZZ1CmsRhOauxL03zQkpDvPRy+4uRn7hC4hii4Ly
        FSHEo0GquVQopcaFEKHLsG6rVPCee556Qj3AOASQ37OLzg/cTddHP0T5/XfGu/hoqeQ14F8LIc68FbH9
        q8EVQwAC+Ggt7yl0ToF+AgsBQI48QgiuKuxjtj7LhKeTb8z78ytUM2Of8O36m32T2JhNEjD6VlWVWf8C
        x/w3eME7zA5nF52Uw9wB0WsG7sJicAA2bURedw2cn0GdflMHtCwu6Qv5dRBSV8zdsR25fSsi3+p+7CmP
        ZbXMsqqw7C/T5/TRISOxChU0KWse7Lq6pFabIJpouz1fXhpyo6PaxFaIrP99tIg9A0xkWP8TpAIvRawA
        vo+/sICqLKfrLRwHWcgjxzaQ37Gd3NYttkpFi2idxCF0ktLZQAF4WeKKIgDBcuBBpdRhtGLqw8D/Eu4f
        dDUt+D+Hf4MJ7yz/fPIXeLN2micrT1zchVeiFTIlAmjl/Ka0YJEEJv1zjc9X5X/nZ4v/jB3OLvY7V+MQ
        W6vn85DP49ylOZD7uU8BUP/WI6hKBf/Fl1HVmnbUKRQQxQ7c++7F2bPLegvz/hzHvWMcrb7Bidox7it/
        hO35nWaXM+jJ37BXyq4unA2j+GfPwlLFyv1NPQC0SgBZ0PuJj5IbGSYXTZ65iJ78jwshvp7xVKNAxAqg
        qlW8V1/Hr1Yb4zK3oH8qp6uMO76Rvi/8DLkd2yndcavt/K8FY/o0Wvl32U5+uMIIgIFF4DWl1HeBXiHE
        AfRLKQHyIk+308P7S/dyvHYUT3mc9c7wpnc6+xu3Qm1+Yt802VYGkkBADITRd8HXsQ5/Vf0mh+WzVPIV
        SqLIbmcvAklO5BJOjNYP1GpIx4F6HVWtgpuDnIsIONZMfYY6Hq9VX6GqarzpnWLOn+VU7RRT9UnOe1Pc
        2HEz4+4mckGwkhCiTPydKeQR5TJMTlnjAKISQHqm3Jb7AGSxiHAcitddg9uqrzgNhMwgFUGdyrxSqsPM
        HejPzaPmF1DLVahHBYjG6q1UgpxL8SP34QwNkt+9Eyfm5KOUmgwm+/3AOcBTqp0S6u3HFUkAAhPht5VS
        E+ilwP+GpuwSoChLFGWJz/f9JC8vv0hRFHlk8WFNAEJk5epxzX/8WJueIH6OpPOakkCs30z9PDPiPP/P
        0m+DAE/VGJCDbCpuwcUlRzIBkLsDrr3/qsQ+Z+tnWFYV/mj+D5mtz/Lw0kMs1BeY9M41xvLhro+xt7AP
        R7hhObOWDJais4Qc7EedPp3K/U0JIP740uD0dCM7Oui+796Wqjlobvs/lFJZ0meV0MyiGyMDk5qaxp+e
        QS0tWcciQAdFlYr0f+FnEMUieYsEFVTvmQR+PfBmvSJwRRIAA5PoOgP/nxDiMPAhYnqBYXeEe8r3sdEd
        Z1/hAN+af4g3akdYVpWG/jkxQtTkyvEJnuQxKC19w/42U6Fx7UiZs9i1H1r+JiVR5FT9JN2ih53ubgbE
        IKNyA52ik7woMCSHyYs8S/4SHjUm/LMs+UtM+9PM+7PM+XOc9c4w789xrHaUZbXMy8svUVNVpr0par7X
        vB8F0940p2on2ZnfZatn2EBc8Re2Nbf2sthpk1/mcgjXofdvfYT8pnGc7m6zNmBY8PMVIcSTQf3JduhG
        ZwCKmBDqJ0/hT0Tna/jzuFs2I7u7Kf3IJ5F9fbgbx2wZiV9DO6z9abDNlJb8csEVTQDCUmNKqYfRnoMH
        0NOqP9jS5/RzU/FdjDgjbM/v5OjyG5z2TlFlmbaec/GJbK7zbftMrh4/pmXwRC0H8X2x8x/ynkIgeN47
        zJAc5l35W9kud3KVu58BMUhZlumT/eTJU2WZZbXM6fopLtRnOOYdZaJ+lrP1M7y4/DyT9XOcrp2ipmqt
        4XoGZusXmKpPsq2Zqc36iJLasor6NgjXRRbylG9/N8UD+5GdJUTTX8EDpoUQJ4QQr2Q5n1KqJISIBAEB
        +JNT+EGylbhKxh0ZxhnbQPkTH8MZGUb2tsZGKKVOCyEOAQ8GufyuKFzRBCCEUupkIHb9IlrD+y+C9WrD
        vW3YHaXb6eEXhr7IVH2SL5//HSa9czy2+GhwEvOEwdbQ1offzf0ivkSwLQfiCkBoWqBt7sGmpSB+nyjm
        xTzL9WVm639BkSIlyuTI4eBQEiUkEk95KOWzqBbxlEfFX6KqqlT9Kov1Baqqhud7Ubk8/N4jwQAADQpJ
        REFUHIffvOej1aN0yi6u7riWoiVvKWgloBwZQRVebRi+m6dN5/xWkTvnIvMFuu66k9KB/ZRuvJ78xo1x
        55qjwK+j1/+ZEEz+69C5AxuoPvMcfmj+cx1EoYPiHe8mf+u7yN98I87YBpxNG21Wk8PA14UQ30FLoafj
        Ha4EvCMIQBBKvKiUehaQQcBF1TSbdcgOOuhgX8d+aqrGg3NGDoY0Rd1KXYBtxyT1sRGKNjoFT3l4eCzV
        l9JZbVKbbSGOvW3BX+BCfQY/LeTadXVq6zBjkOXSK4KQiJxLbmSEjl07cfv7cLqimXQCr8RX0CbhrOhA
        T/7IO69mLuDPzjUcjUTOxdkwSv7q/eSv3o8ztiHpfDPA82gfhMvSxp8F7wgCYCA0uXxeCNEL/IhSar8Q
        opGOpShKdAjFvxz5FXx8Ptz1J5ypvcmvTfwKnqqxpGKFSVM0VtbS5Eluw+byIS4RqFhfP9Y3CWks1cZ2
        bYt0o13F+izVF5nzZlMDrGRvD874RlRHRyNPvu2TNMwGHAens0TvR+5j+Mc+S2H7VlvE34xS6heBk0qp
        J7LY/UMEDkA3E1Nk1h79NjgOHffdQ+76ayl+5keRPd269kDrev8ocFwp9avoHH6HL1XRzrcK7ygCYHD8
        2SD324QQYiRwxcwDeSGEFAjZ5WiN8pA7RF15DLpDLPmL1Ot1HV7cLtFIinNPuDuV6ycRlqQ+SbBx/aR9
        CVKBircF27ryqKpqqq5ECO2MFC5ZVHDwStb/Ip9HdhTIDQ2SGxzEHRrA6eoyi2aANv3OB9LdtJRyTfLm
        i4L2pZDDQ8ihQZyhQe1sZGTtDd2MlVITQojJcAyXu40/C95RBMBEoCD8klJqVAjxl8AH0FaCUYwSXB/o
        +hB1VefjPX+b7y5+h/889Z94qfICry4buqUkJV24Lz5ZTVfgEKZ1oG70M481l7km92/nY5B1CWBu4xKA
        JVrn+PJxFutLVP3kuZZ06axLAZHP0/ehD9Cxdw9jP/9PNDEoWTMlfwk4K4T4asrpkq+jzXRPoJcBDYeC
        rl/6IqJUInfzDWmH/3Zw7X/PFeDcsxK8YwmAgSraXPgq+gXYrZTqF0KME7i0CsDBpc/pZ3+H9rjLiwJn
        aqeZ9xeoquXkbLK2yR+K05Y+kbkcP9ZmQWgnCSSJ/+HWtizAGF8b6SCLFBI9pP0BTk83IpcjP7YBUSxS
        vHo/hc2bdKEMpyUc+ihav/PqRVbJnUe7kEcmr+jtaUl+opTygvDdM0F8wetos2Oo53zH4B1PAIKX5gml
        1NNoLvIhIcRepdRPB5phpHAoO2VuKN3EDaWbGhLA7079F56vHOZU9SQexlIvPqmSzIEm4pp/mxkwzvFX
        KgGY7UnLgbgSMMmIH5cKMgwhC8cHKB3YT25okKHPfxanp4eu225J6/57wMtCiD8IJuVq8Qpah3BnEAwE
        QO7agy0dA+lxGvgvQohngb8WQlzSKr1vF97xBMBAGDjyWlCM4X6gXyn1nqBCTGNZ0Of0sy2/nbu7Psi+
        jgN8Z+FxFuoLPL2UkMXJxu1tir1QTxBzBBLmse30BWnXTll4N9b5tslu26dgzpsNdAEZEq8aEKDX1YU8
        ztCQDpkdGsDp6UGWOyke2I/T20N+y2ZksUXcD0N7X0O7+j6FDqq5WM5bQfsOHEI7A91Ia03GSbQ/yRGl
        1FEhxNPBta9oRV8avm8IQBArHkZp4ft+RQhRFkLsU0rlA78BAEZyo4zkRtmW34FHjf829WWmvMlkAmCi
        3WQ1J3adVk3/SiZ/0rWTJIA0Ud/SZ7o2xZw3R30Vim5ZKmovuoMHyG/ZTOnagxR2bic/vpHcyEhcwdcc
        pp78k8CDwKNKqceklBedOVc0S9P/FXopsJNWAnAS+GPgiWDyZ8kwdEXj+4YAxBHYkfPALwX+4fegA4oa
        zkN5mSenXN7bdRcVf4mSLHGhfoHH5v+Gc945jiwH5l/bJE2z74f7Q31B3MXYNCtmgLJN6kgHy740CSD4
        XqWKh5daw7Cwc4cO2CkV8c5O4PT1guvgjo4i8jncwQGcclnn8evpxuksmRVxTUwADwf58p5Di+ynyZbl
        ZyV4IfDem0UTgF1oxnAMLQG8Emj7q7zD1vs2fD8TgDMASqmvom3DvUqpq4UQDQKQEzkQcKB4NUophnLD
        vFk7zTlvAllxNAHIwqGTuHlcfxC2ZSpvGTvGtk3bl7Z4D7ZeIzYg+SZzG0Zw+/uQpRL+3By58TGt4NuS
        UuzEjlngcaXU4aAwzCXhvoE14KTv+y8FZuPb0UTmabSG/x0/6U1k5DHvXBjprobREsD1Sqk7hBC3oE2G
        paAfy2qZmqoyUTvLOW+C1yqv8tjCIxxafJojldeZq7fJ9WgLEIq3Jyn+bIQi6XvSvgyT3rZ8+NpVD7Kl
        sI0dxZ3EoXxfJ9SoLEO9rl1mpYhXv41jBs11/ww98f8cPQmPB9tZ9GRMO8dFwbDqdKM5/TzQmnTlHY7v
        WwkghKEbOBnoBfJCiB1o01OjzrwQgg6h3Ym7nG76vAFKspMT1eMccV7HFRkeZZLFwNYnrW/a5Ldd07ZN
        6xPDkr/Eor9g3SekBClxyit6lTwCsTtwqPm2UsqXUr5l9nXTaeytuubliO8vctcGvu9LIUQoEbho8XAc
        nW+gHyMTjlI+PgpP1fBUnTeWX2e2PstXpv8b573z/PH5P0yfmGlP/mK4f1L7SkyGsWO+tOv3GM9v4rae
        95h7Z4DTgaOVLbPoDJqrHkZnEnoVeAFt1w/X9h6a+1bh+4/7Xg74vpcATAQZW0NzIb7vTwepo04ClSCb
        jAuUhZDSQVcrKgDdTo+uXOSOkBcFRnMb8FWdpXoFT9WohPkH2mn3ExyLWpCFCLRbCiS1xY6p+TWqqsUE
        7wXmVEkzU7NUSoXr6Gk0ATgTfEL32Rml1Oxbye3XkYx1kpsRga7gM2iJ4OfQL7zVluUrnxPV40zUJnjw
        wv08v/gcfz37EEv+om0iJaOdA1DS96x94vsS+vzc+D9jJDfKT479dPMQpZ4UQvw+8CSay29Hr6dPohV4
        WbL0rONtxroEsDKcDMKMHw5qF44KIfqVUt2BZNBAhyzS4/Swo7ATFxeBZNqbZLY+y3nvPDVVZdI7R135
        djt7mpSwmiVAWj9zn6XPQn2BOTkXHZ6+3270O+Sh19IeTdF+HVcA1glARgRi7YNBcsmH0Jll71JK3S6E
        uNFYHiCFZCQ3wkhuhN3FZhWbw4vP8sbyER6fe5Qpb5IHzt9Pxa8wpyx6qJVo+dPQjghkONfJ5RM2JWAZ
        2IZ2mplHp8ZaxxWGdQKwcnjosNSTwN8IIU4ppR4TQuxEc8QbaZafimDQHUIicXFZ8BfYkt9KVVV5o/I6
        FVXhXO0c8/U5ZrwZZrxpnRk4RBYxv127bV87AiBg2puyuQN3AMNBsZZ1XKFYJwArRCAJzAafBtdTSt2L
        1g9sRoecthCA0fwGRvMb2Fc60Gir+lW+M/8YM955nl18hlPLpzhSeY1XfY+Fut301oJVcPWVSBHnqhMs
        OVGHvCDH3pjpQr2OKw/rBGDtcBhdDmoSnZfwJnQa6uvRnobjtoNc4bC7Yy9VVWVLYRuL/iJz9TnmvAss
        +Uscrx5jyV/i5PIJ6srjvHeeir/Eudo55rxZZrwZqmpZc+jVSARJaGeKXMc7AusEYI0ghAiTQr7m+74b
        +JqPoj0MJQkEQAqHkbyudrOp0Oo+e2jhaebqszy38CxVVeXN5VPMerNIHISSLNYX8ahRJyFibzWTP471
        yf+OxToBuDTwgEeCKMOH0cuBfnQE2ibgYPD9Zsw6exbs7thLnTp7ivtQyqematRVnapapqY8PL+GdklS
        HK8cY1lVmPfmCWf+TG2GI5XXVzb6YML3uf10yA7u6LmTTicq6Qd5Fg6xssSc67jMsE4ALgGkzpAbqvYn
        lVKhUjB0MhonY8hPydE6tjCHYRrKTpklf4kLtWb07KRzjiV/KfmgJO4udL7EktPJzuKuFgJAoAwl8OJb
        x5WJdeHuLYAReCKNrVRKuUGy0lvRFoRrlFJjwE4hxHb0EiIzfEspOhX8Ww2CKAhkMOzQVTeIkfg68EWl
        1BkpZZbKPOu4DLEuAbwFMHzcIxWzjBxzM8H2bNA/jyYS8+glgqTpdegG312llAu4gTsuUqw0jnhlCJyg
        qkKIybAY5js9YcY7HesSwGWKwPVYovUF3cE2D2wMPA+30wxhLtFGl7AG8NHBPMeBX0GnXM9Ulmsdly/W
        JYDLG35Q06CqlHotkAzCAKXTNFNc5wNpoIyWCFylFIFkkMfik5AFAcf30Mk0vaD23bkgqOeKKoK5DjvW
        JYB3EJRSu9E+CL3BssMNUqDvX+X5zgbZcJ8EFoUQR9ZwuOu4DLAuAbyzMI22PswQKByD1GerUtIFXN4L
        cuStr/XXsY51rGMd61jHOtaxjnWsYx3rWMc61rGOdazjSsP/D4rCpUP7fyy2AAAAAElFTkSuQmCCKAAA
        ADAAAABgAAAAAQAgAAAAAACAJQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA////
        AP///xz+/v5S/v7+gf7+/qf///+/////zf///83///+//v7+Xv///wIAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AP///xb///9w////yeb55/W07rf/huOL/2DZZv9H00//Os9B/zrPQf9R1Vf/9v336////xAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAD///8W////h/H88uuj66f/Q9dL/w7MGP8ByQv/AMgJ/wDHCf8Axwr/AMUJ/wDECf8lzC3/9/33
        5////07///8u////BgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAA////Bv///2L1/fblnOug/yTTLf8BzAv/AM0K/wDQCv8A0gr/ANIK/wDRCv8Azwr/AMwJ
        /wDJCv9D1Ur//f79/c/N9f/Y1vb5+fn92f///3T///8OAAAAAAAAAAAAAAAA////Gv///zL///8CAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAD///8i////scn1y/062kH/Ac8K/wDQCv8A0gr/ANYK/wDZCv8A3Av/ANwL
        /wDbCv8A2Ar/ANUK/wDQCv9f32X//////0lB2/8SCc3/PTXU/7Ov7v/8/P7D////GgAAAAD///8C////
        x+Df+/n+/v6d////FgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAA////AP///0j2/fbhg+qI/wvUFf8A0gr/ANQK/wDWCv8A2gv/AN4L
        /wDiC/8C5g3/Meo6/27udf9k62r/MOE5/wfXEf+A6YX//////zwz3f8KAM7/CgDK/xAGyv+Tj+f//v7+
        tf///wT+/v4E/v7+1UtC6v+dmfL/+fn+2f///zQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///8C////XOn76vVO41b/ANYL/wDWC/8A1wv/ANkL
        /wDdC/8A4Qv/AOUL/wnqFP+Q9pX/8vn4/7Ov/P9tZvb/q6r1/9D40v/c+d77/v7++ycd3v8LANT/CgDO
        /woAy/8VDM7/z831+////0j///8A////o2pj7f8MAd7/aGLm//Hw/Ov///9M/v7+AAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP7+/gL+/v5c5fvm9zbhPv8A2Qr/ANkL
        /wDaCv8A3Ar/AN8L/wDiCv8A5gv/AekN/4z2kv/6+v7/b2j8/w0A9v8MAPH/Ewfs/6Wg9v/7+/7x6Of8
        /RgO4f8KANr/CgDU/woAz/8KAM3/enTk/////5MAAAAA////fJCL8v8LAN7/CgDW/1BI3P/s6/v1////
        RAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP///1Dm/Oj3NuQ+
        /wDcC/8A3Qr/AN8L/wDgC/8A4Qr/AOML/wDlC/8A6Av/Le03//b+9v+xrf7/DgH9/wwA+f8MAPT/DADu
        /w0B6v9LQu7/SkHt/w0B5f8LAOD/CwDa/wsA1P8LAND/X1jg//7+/q8AAAAA////XK2p9v8LAOH/CwDY
        /woAzv9UTdj/8/L85////yoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/v7+
        JPT99OdG6U7/AOAL/wDgC/8A4gv/AOQL/wDlC/8A5Qv/AOUL/wDmC/8A5wv/fvOE//7+/vtZUP//DgH+
        /w0A/f8NAPv/DAD2/wwA8v8MAO//DADv/wwA7f8MAOn/CwDh/wsA2v8LANP/g33n/////4kAAAAA////
        RMPA+P8NAeT/CwDc/woA0f8KAMj/dG7e//z8/sv///8MAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAD///8G////v3rxf/8A4wv/AOMK/wDlC/8A5wv/AOgL/wDoC/8A5wv/AOUK/wDkCv8A4wr/rfaw
        //////8+NP//EQT//xIF//8RBP7/DwL+/w0A/P8NAPr/DAD5/wwA9v8MAPD/DADo/wsA3/8OA9f/wb7z
        /f7+/kwAAAAA////OM3L+v8OAuj/CwDh/woA1/8KAM3/DgTH/6+s7P////+HAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAD///9owPnD/wfnEv8A5gv/AOcL/wDoDP8A6gz/AOsM/wDqDP8A5wv/AOML
        /wDhC/8A3wr/tva5//7+/vdGPP7/EgX//xYJ//8YDP7/Fwr+/xUI//8SBf//EAP+/w0A/f8MAPb/DADu
        /wsA4/8eFN3/09H3//v7/v3+/v7l////z9XT+/8PA+z/DADp/wsA4P8KANX/CgDL/yYezP/p6Pn1////
        MAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP///xb0/vXlNe0//wDoDP8A6Qv/AOoL/wDrC/8A7Qz/AO0L
        /wDrC/8A5wv/AOIK/wDeCv8A2wr/k/CY//7+/u19d/3/EAP+/xcK/v8cEP7/HhL+/x0R/v8bD/7/Fwv+
        /xMG/v8NAP3/DAD0/wsA6v8LAOH/DgPc/xII3f8rIeX/SkHv/0pB9f8MAPX/DADz/wwA6/8LAOD/CgDU
        /woAyv95dOD//v7+rwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP7+/oua95//AOsM/wDrDP8A6wz/AOwM
        /wDuDP8A7wz/AO8M/wDsDP8A6Av/AOIL/wDcCv8A2Av/S+NS//3+/e3c2v75Ixf8/xMG//8aDv//HxP/
        /zAl//87MP//HhL//xgM//8RBP7/DQD7/wwA8v8LAOr/CwDn/wsA6P8MAOz/DAD0/w0A+/8QA/7/DwL9
        /w0A+f8MAO3/CwDf/woA0/8aEM7/4N/49////zIAAAAAAAAAAAAAAAAAAAAA////HO/+7+8j8S3/AO8M
        /wDvDP8A7wz/AO8M/wDwDP8A8Az/APAM/wDtDP8A6Qv/AOML/wDcC/8A1wv/CNYT/8T1x/3+/v7v2Nb9
        /YaA/v+Xkv//ysf+//Dv/vH09P/5RDr+/xoO//8UCP//DwL+/wwA+v8MAPT/DADz/wwA9v8NAPr/EQT+
        /xcL//8cEP7/HBD+/xYJ/v8OAfr/DADt/wsA3f8LANL/hoHm/////5UAAAAAAAAAAAAAAAAAAAAA/v7+
        fp75ov8F8RH/BPEQ/wTxEP8D8Q//A/AP/wLxDv8B8Q3/APAM/wDvDP8A6wz/AOYL/wDfC/8A2Qv/ANQK
        /yvaNP/X+Nn9////3////8/////V////2f///3z8/P/vLyT+/xgM//8VCf//EQT//w0A/v8NAP3/DgH9
        /xEE/v8WCv7/HRH+/yQZ/v8pHv7/KR7+/yQY//8YDP7/DQH5/wsA6f8LANr/LSTY//j4/ef///8KAAAA
        AAAAAAD///8K+v764Tf0Qf8K8RX/CvEW/wrxFv8K8RX/CPEU/wfxE/8F8RH/A/EP/wHwDf8A7gz/AOoL
        /wDlC/8A3wr/ANkK/wDVCv8U1h3/dOd6/63ysf+i8qb/Y+9q//7+/vHi4P/9HRD//xUI/v8UB///EgX+
        /xAD//8QA///FAf//yEV//9SSf//gHn//5ON//97dP//UEb//zAl/v8lGf//FQn+/wwA9f8LAOP/DwTY
        /8XD9P3+/v5GAAAAAAAAAAD///9MxPvH/RDyG/8N8hj/DvEZ/w/yGv8O8hn/DfIY/wvxFv8I8RT/BvES
        /wPwD/8B8A3/AO8M/wDsC/8A6Av/AOIL/wDdCv8A2Av/ANYL/wDXCv8A2wv/AOIK//n++f++u///FAf/
        /xMG/v8UB///EgX+/xEE/v8TBv//Vk3+/9LP/vv8/P/R/v7+nf///4v///+t9PT/57Wx/v9ANv//HxP+
        /w8C/P8MAOz/CwDd/4R+6v////+JAAAAAAAAAAD+/v6hdPd6/w3xGP8P8hr/EfEc/xLxHf8S8h3/EPEb
        /w7xGf8K8Rb/B/ET/wXxEf8D8Q//AvEO/wHwDf8A7wz/AOwM/wDnC/8A4Qv/AN0L/wDcCv8A3gv/AuMN
        //////+Xkf//Ewb//xQH/v8UB///EgX//xAD/v9XTv//8/L/7////1T///8GAAAAAAAAAAAAAAAA////
        FP///5XRzv/9NSr//xQH/v8MAPP/CwDj/01E4//////DAAAAAAAAAAD////lPfRG/xbyIf8S8h3/EPEb
        /xHyHP8S8R3/EvId/w/yGv8L8Rb/CPEU/wbwEv8E8BD/BPEQ/wXwEf8F8RH/A/AP/wDuDP8A6Qv/AOUL
        /wDjC/8A4wv/Fukh//7+/v9yav//Fwr+/xcL/v8WCv//Ewb+/xoO/v/V0/75////UAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAP///wT///+hnJb+/xgM/v8NAPn/CwDp/x8U4P/7+/7x////BP///xD+/fzv9/zz
        /9393v/N/M//wPvD/7f7uv+h+aX/dPd6/xLyHf8L8Rb/B/ET/wTxEP8D8Q//BPEQ/wfxE/8J8RX/CfEU
        /wXxEf8B8A3/AOwM/wDpC/8A6Qv/Ke8z//7+/v9PRv//HBD//x0R/v8aDv//FQn+/zov/v/9/f/b////
        BAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD+/v4w3dv/+SMX/v8QA/3/DADx/xQI5f/i4fv7////
        IP///0r74r7/86Y0//WyUf/2vWr/98qJ//vivv/////d9f718S7zOP8H8RP/BPEQ/wLwDv8A8Az/MvM8
        /6/6s/+O+ZT/ZPZr/znzQ/8P8Rr/BPAQ/wDvDP8A7wz/SfRR//////83LP//JBj//yQY//8gFP//Gg7/
        /0A1///+/v/X/v7+AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///8m4eD/9yoe//8VCP7/DQD5
        /w8E7f/Myvr9/v7+Ov///1D637f/8JAD/++NAP/rjAD/6osA//XHhP/////xvPu//wjxFP8F8RH/AvAO
        /wDwDP8A7gz/UfNZ//7+/uX+/Pj3/vny///////5/vn/1vzY/7T6t/+U+Jn/xfvI//7+/vtNQ/7/KR3+
        /ykd/v8mGv//HxP+/ycc///m5f7x/v7+IAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD+/v5QxcL+
        /yQY//8bD///EAP+/w0A9v+8uPr/////TP///1D74Lj/8ZEE/+6NAP/pigD/6I8P//zx4v/T/NX/G/Im
        /wTxEP8E8RD/AvEO/wDwDP8A7Az/Ke0z//b+9//76Mv/7Y0B/+2ZH//tp0L/7rRg//C/d//11KL/////
        z////2b////f9/b+/+Xk///U0v//xsL+/8TB///y8v/p////GAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAD///+lg3z+/ygc//8hFf//GAv//w8C/f+5tv3/////UP///0z74bz/8ZIH/++OAP/oiQD/6Jcg
        //P46f8s7Db/AO4M/wLwDv8D8A//AvAO/wDvDP8A6wz/A+gP/+D84f377Nf96Y0I/+WIAP/hhQD/34QA
        /92DAP/lnTP//v7+0wAAAAD+/v4E////EP///yb///86/v7+SP7+/kj///8g////AAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAD////HiYP//1VN/v8+M///JBj+/xcK/v/Avf7/////Sv///zj86s/98pUP
        //CQA//qiwD/55EU/+3y2f8e6Sj/AOoM/wDvDP8B8A3/APAM/wDuDP8A6gz/AOcL/7b3uf/8797/544N
        /+KFAP/egwD/2oEA/9iAAP/joUH//v7+xQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///9A/v7/rfr6/8/29v/n8fD/+c/M///39/7p////
        EP///xz99Of78poa//GSCf/vjgH/6IoA//TPmP9y8Xj/AOkL/wDtC/8A7wz/AO8M/wDtDP8A6Qz/CecU
        /9j72f/66tH/5IkG/9+EAP/bggD/138A/9R9AP/iqlj//v7+rwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA////AP7+/gb///8O/v7+
        Fv///z7///8aAAAAAP7+/gL+/v3r86Mv//GVD//xkQX/7YwA/+mUGP/w6Mf/e/OB/xXrIP8A6wz/AOsL
        /wDqDP8d6if/ovWm//3+/f/xwXz/44YA/96EAP/ZgQD/1H4A/897AP/luXr//v7+iwAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///+79rhf//KYFf/xlAv/8I8C/+mKAP/nlBr/9dit
        /+n75v/E+cf/rvey/8T5xv/u/e///v38//XVpv/mjAr/5IcA/+CEAP/agQD/1H4A/816AP/rzKD/////
        ZgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD+/v56+dOb//KaG//ylhL/8ZIH
        /+2MAP/liAD/4YcE/+aiQP/xzZn/+OjP//nq1f/0163/6q1V/+OJBf/khwD/5IcA/+GFAP/bggD/1H4A
        /8x6Av/x3sH9////QgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD+/v44/O3X
        +/OgJ//ymhn/8ZUO//CQA//qigD/4oYA/92DAP/bggD/24IA/9uCAP/cgwD/34QA/+KGAP/liAD/5YgA
        /+KGAP/cggD/1H0A/8x8Bv/16db7////LP///w7///8m/v7+JP7+/ggAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAD///8E/v791/W1V//ynCD/8pgW//GUC//vjgH/54kA/+GFAP/dgwD/3IMA/92DAP/ehAD/4YUA
        /+SHAP/niAD/54gA/+OGAP/dgwD/1X4A/8x8B//269n9/v7+y/v48+H27d/99u3h+/38+sn///82AAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAA////dPrcsf/zoCf/8pwe//KYFP/xkgj/7o0A/+eJAP/ihgD/4IUA
        /+CFAP/ihgD/5IcA/+eJAP/oigD/6IoA/+aIAP/ghQD/2IAA/897Af/Wm0T/0JdD/8OBIv+3bgP/uG8E
        /9OkXv/8+fTl/v7+IgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA////Fv779un1sU7/858m//KcHf/ylxP/8ZII
        /+6OAf/pigD/5ogA/+aIAP/miAD/6IkA/+qLAP/rjAD/64sA/+mKAP/liAD/34QA/9iAAP/QewD/yXcA
        /8JzAP++cAD/vG8A/71wAP/jwI7/////jQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP///3774Lr/86Mu
        //OgJv/ynB7/8pcU//GTCv/wjwP/7o0A/+yMAP/sjAD/7IwA/+2NAP/ujQD/7o0A/+yMAP/qiwD/54kA
        /+OGAP/ehAD/2IAA/9F8AP/LeAD/xnUA/8R0AP/Skzb//v38z////wIAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AP///w7+/vzT98N2//OjLf/zoCf/8pwf//KYFv/xlQ3/8ZIH//GQA//wjwH/8I4A//COAP/wjgD/8I4A
        /++NAP/vjQD/7o0A/+2NAP/sjAD/6IoA/+KGAP/bggD/1X4A/9B7AP/Wjib//fn03////woAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAD///9A/fbr9/WyUP/zoy7/86Ao//KdIP/ymRj/8ZUP//GTCv/xkQX/8JAD
        //CPAf/wjgD/8I4A//COAP/wjwH/8JAD//GSBv/xkgj/8ZIH//CQA//sjAD/5ogA/96EAP/hmTH//vz6
        1f7+/gQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD+/v4A////cPzs1f30q0H/86Mv//OhKf/ynSH/8poZ
        //KXEv/xlAz/8ZII//GQBP/xjwH/8I4A//COAP/xkAL/8ZIG//GUDP/xlhL/8pgU//GXEv/xlAz/8JAF
        /+yMAP/wu2z//v7+oQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA////Bv7+/p386Mz/9KxD
        //OkMP/zoSr/8p4j//KbHP/ymBX/8ZUP//GTCv/xkQX/8I8B//GPAP/xjwH/8ZcT//vlxf/74r7/9bRV
        //KbG//ymRf/8ZYQ//KZGP/87df5////QgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AP7+/gr+/v6f/OnN//WvSv/zpTH/86It//OgJ//ynSD/8poZ//KXEv/xlAv/8ZEF//CPAf/xjwH/8pkY
        //7+/fP///9g/vr18/W0Vf/zoCf/9KtA//vhvP/+/v6d/v7+BAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAD+/v4K/v7+if3x3/n2u2X/9KUz//OjL//zoSr/858k//KcHf/ymBX/8ZQM
        //GRBv/xjwH/8pob//7+/u3+/v4A////VP758uX98+Xz/vr13////3r+/v4MAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA////Av7+/lz++/bn+dSe//SsQv/zpTL/86Mu
        //OgJ//ynB7/8pgV//GUDP/xkQX/86Uz//7+/tMAAAAAAAAAAP///xL+/v4g////DgAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///8i////
        pf3y4fn4yIP/9Ko///OjLv/zoCf/8psd//KXE//xkwn/9bNT/////7cAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAP///0D///+7/fLj+fnSmP/1r0v/86An//KaGf/xlQ//98R5/////5MAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA////NP///5P+/Pnn/OjK/fjIgf/zpjb/+tak
        /////2oAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///8K////
        Sv///53+/v3j/vz67f7+/iwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAD+/v4C////BgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAP///////wAA///AD///AAD//gAP//8AAP/4AAP//wAA/+AAAOP/AAD/wAAAQf8A
        AP+AAAAA/wAA/gAAACB/AAD8AAAAID8AAPwAAAAgHwAA+AAAACAPAADwAAAAIA8AAPAAAAAABwAA4AAA
        AAAHAADgAAAAAAMAAMAAAAAAAwAAwAAAAAABAACAAAAAAAEAAIAAAAAAAQAAgAAAABwBAACAAAAAfgAA
        AAAAAAB/AAAAAAAAAP8AAAAAAAAAfwAAAAAAAAB/AAAAAAAAgP8AAAAAAAD//wAAAAAAAP//wQAAAAAA
        ////AACAAAD///8AAIAAAP///wAAgAAAD///AACAAAAH//8AAMAAAAP//wAAwAAAA///AADgAAAB//8A
        AOAAAAH//wAA8AAAAf//AAD4AAAD//8AAPgAAAP//wAA/AAAA///AAD+AAIH//8AAP8AAx///wAA/8AD
        ////AAD/8AP///8AAP/8A////wAA//8D////AAD//+f///8AACgAAAAgAAAAQAAAAAEAIAAAAAAAgBAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAD+/v4A/v7+DP///x7///8y////Mv7+/hIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAD///8c/v7+eN/44MGw7bPtkeWV/3zfgv9834H/1PXW4f///wgAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAD///8a9/33l6Tsp/c81kT/B8wQ/wDLCv8AyQr/AMcK/wDFCv/B8MPz/f3+hf///0z///8GAAAA
        AAAAAAD///8A////AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAD///8C/v7+Xrzyv+0x2Dn/AM8K/wDTCv8A1wr/ANoL/wDYCv8A1Ar/A84N/+H44v9ZUt3/bWff
        /9bU9tn///8uAAAAAP///2Ts6/2x////JAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAA////CPX99p926Hz9BNQP/wDVCv8A2Qr/AN8L/wHlDf9b72L/me2i/3Dggf8/4Ef/9f32
        /Sgf2f8KAMz/HRTN/9TS9t3///8M////h1JK6v++u/Xt/v7+SgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAP///wjv/PCxS+RS/wDYC/8A2gv/AN0L/wDiC/8A5wz/jvaU/7q3/f8jF/b/Jxzv
        /9TT+v/08/31FQve/woA1P8KAM3/Ylvf/////1b///9YXlbr/xEG2f+hnez5////WgAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAD///8C9P31pUnnUf8A3gv/AOAL/wDiC/8A4wv/AOYL/yztNv/x9Pv/JBj+
        /wwA+f8MAPL/Fgvs/zow7v8MAOb/CwDe/wsA1P9IQNz/////bv7+/jp8dfD/CwDb/w4Ezv+sqev1////
        OgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP///2Rw73b9AOML/wDkC/8A5wv/AOcL/wDlC/8A5Av/bPBz
        /9HO//0QA///EAP+/w4B/f8MAPn/DAD2/wwA8v8LAOj/CwDb/3p05/////8+////KIyH9P8LAOL/CgDT
        /xkQyv/U0/Tb////DAAAAAAAAAAAAAAAAAAAAAD///8at/i77wPnDv8A6Av/AOoM/wDsDP8A6Qz/AOQL
        /wDfC/907Xr/2df+9RIF/v8XC///GQ3//xYK//8SBv7/DgH8/wwA8f8LAOP/bmjo/7Sw8/nMyfnjgXv2
        /wwA7P8LAOD/CgDP/0tD1f/7+v6HAAAAAAAAAAAAAAAAAAAAAPb+95kv7jn/AOoM/wDrDP8A7gz/AO8M
        /wDrDP8A4wv/ANwL/z/iR//8/f7zQjf9/xYK//8eEv//LSL//yMX//8VCP7/DQD7/wwA7v8LAOX/CwDm
        /wwA7/8OAfn/DwL7/wwA8/8LAN//CwDO/7Sx7vH///8WAAAAAAAAAAD///8govmm9wHvDf8B7w3/APAM
        /wDwDP8A8Az/AO0M/wDlC/8A3Av/A9YO/7zzv/vs6/7vrqr++8/M/+v08//JfXf//xgM//8RBP7/DAD6
        /wwA9v8NAPn/Egb+/xwQ//8gFP//Fwr+/w0A8/8LAN3/TUXc//7+/nIAAAAAAAAAAPv++4s49EH/CfEV
        /wjxFP8H8RP/BfER/wPxD/8A8Az/AOoL/wDiC/8A2Qr/ENYa/43rkv3B9cTjofSl+f7+/uVcU///FQn/
        /xMG//8PAv7/EQT+/yUZ//9YT///bGT//0tC//8rH///Fwr+/wwA7v8QBdn/29r4yf7+/gL///8IxPvH
        4w/yGv8P8hr/EPIb/w7yGf8L8Rb/B/ET/wPwD/8B8A3/AOwM/wDmC/8A3gv/ANgL/wDZC/8A4Av/+fv8
        /zMo//8UB///Ewb//xEE//9mXv//5OP/xf///3D///9g+Pf/mbOv//UsIf//DwL5/wsA4v+hne/5////
        Fv///zqF+Ir/E/Id/xDyG/8S8h3/EvId/w7yGf8J8RT/BfER/wTxEP8E8BD/AvAO/wDrDP8A5Av/AOEL
        /wvmFv/u7f//HBD//xYK//8UB///OzD///j3/qf+/v4EAAAAAAAAAAD///8A/v7/Qqah//sUB/7/CwDr
        /29p6/////9G////ZPnoyf/j78f/1vfM/8z8zv+l+qr3F/Ih/wfxE/8D8Q//CPEU/yTyL/8P8hr/BfAR
        /wDuDP8A6wv/H+8q/9DO//8eEv//HhL//xgM//99dv//////PAAAAAAAAAAAAAAAAAAAAAD+/v4A4+L/
        wx8T//8NAPb/SkHs/////2z///+L86An/++PA//tkg7/+dmr/9z93usP8Rv/A/EP/wDwDP818j//+v76
        7+r64//G+8j/mPmd/3L2ef+K+JD/x8T//Ssf//8nG///HxP//2lh//////9WAAAAAAAAAAAAAAAAAAAA
        AP7+/gLY1v/TIxf//xEF/f80KvT/////hf///4vzoSn/7YwA/+iPDf/s9t//MvI8/wPxD/8C8Q7/AO8M
        /xPsH//2/vf98KU3/+qWG//pojv/66xS//vu3OH///9c+fn/r+Xk/8fU0v/Z2tj/1////zwAAAAAAAAA
        AAAAAAAAAAAA////IqKd/v8nG///HBD//zIn/f////+L////fPSpO//ujgH/55AQ/7Lxqv8A6gv/AfAN
        /wHwDf8A7gz/AOgL/9X71//tqUb/4YUA/9yCAP/ZgQD/9+XL2wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAD///8Q6un/u8nG/+Oxrf/5p6L///7+/mL+/v5e9rhd//GSCP/riwD/59GS
        /yrrNP8A7Az/AO4M/wDrDP8Z6SP/7fzs/+meMP/egwD/138A/9N+A//57+DDAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///8A/v7+Bv///w7///8k////Av7+/jD4zIz/8ZYS
        //CPA//pkA3/6daf/571of93833/jfSS/+X85f/0z5n/5IcA/96EAP/WfgD/0YYX//z48p8AAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA////
        CPvjwenymhv/8ZQL/+yMAP/ihwH/5aA7/+3Cgv/uxYj/6KdJ/+OIAv/khwD/4IQA/9Z/AP/VkzL/////
        fAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAA/vr1ofSpO//ymRf/8JEG/+iJAP/fhAD/3IIA/92DAP/ghAD/5YcA/+aIAP/hhQD/138A
        /9eaQf/+/v63+vbwqfbu4sP+/fty////AgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAD///88+dCV/fKeIv/ylxT/8JAE/+iJAP/ihgD/4oYA/+WHAP/oigD/6YoA
        /+WIAP/cggD/0YEM/8+PMf+/eBD/uG4B/9eqaP/+/v1yAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP///wL99uy/9Ko+//KeIv/ylxP/8JEG/+2MAP/qiwD/64sA
        /+yMAP/tjQD/64sA/+aIAP/fhAD/1n8A/8x5AP/EdAD/w3UC//Tl0NX///8CAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP///yz737f386Mv//KeJP/ymRb/8ZML
        //GQBP/wjwH/8I4A//COAP/vjgD/744A/+6OAf/sjAH/5YgA/9uCAP/TfQD/8tq46f///wYAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA////AP7+/W74zIz986Mu
        //OfJf/ymRn/8ZUO//GSB//xkAL/8I4A//CPAP/xkQX/8ZQM//GWEf/xlA3/748E/+eLBP/67dvL////
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/v7+
        Av78+pf4yIP/86Qw//OgKP/ymx3/8pcT//GTCv/xkAP/8Y8A//GVD//87dfv+dOc+/KcH//ylxT/9r5r
        /////2YAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAA////Av79+4/50pr99Kc3//OiLP/yniP/8pkY//GUDP/xkAP/8ZYR/////5/+/v5c++K+
        8fvgue/+/fqL////BAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAA////AP///1r86Mzr9rhd//OkMP/zoCf/8poZ//GUC//zoCn//v7+
        iQAAAAD///8Q////DgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP///xT+/PqT++K98/a8aP/zoSn/8pgV
        //WxTv////9oAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///8W/v7+
        cv3x38v51J37+taj/////z4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAA////Av7+/iL///9O/v7+BgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA//g////AH///AAf//AACP/gAAB/wAAAP4AAA
        B+AAAAPAAAADwAAAAYAAAAGAAAAAAAAAAAAAAcAAAAPgAAADwAAAA8AAAP/AAAD/8AAA//8AAP//gAAP
        /4AAD/+AAAf/wAAH/+AAD//gAA//8AAP//wBP//+Af///4H////h//8oAAAAGAAAADAAAAABACAAAAAA
        AGAJAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///8A////
        HP///0r///9i/v7+Yv///xgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAA////Bvf992Kp7KzPad9v/TnTQf8gyyn/LM00//j9+I3+/v4OAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///82sPCz
        0TDXOP8A0Ar/ANUK/wDXCv8A0gr/KNMx/8XC8/+DfuTz3dv3kf///wb+/v447u39cv///wYAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAA////APH98mZt53T3AtUN/wDYCv8A3wv/Juow/5Hgqf9rt6T/jeyR
        /5eT7v0KAM//MSjS/+fm+n7///9eWFDq/8bE9b3///8UAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8P3x
        alPnWv0A2wv/AN4L/wDjC/8L6Rb/y+fh/yYa+v8NAfD/fHbz+1VN7P8LANr/CgDQ/6Wh7c/+/v42VU3q
        /xwS1v+/vPDJ////CgAAAAAAAAAAAAAAAAAAAAD+/v46ae5w+QDiC/8A5Qv/AOYL/wDlCv9K7VL/paD/
        /w8C/v8OAfv/DAD2/wwA8/8LAOj/CwDZ/7268rX+/v4ga2Pw/wsA2f8lHM7/3Nv2mQAAAAAAAAAAAAAA
        AP7+/gas97DTAegN/wDpC/8A7Az/AOkL/wDhCv9S6Fn/rqr++RQH/v8aDv//GAv+/xIF/v8MAPX/EAXj
        /3x27P+VkPPtT0b0/wsA6v8KANX/Y13c/f///zgAAAAAAAAAAPX+9WQv8Tn/AO0M/wDuDP8A7wz/AOoL
        /wDfC/8V2h//5vPv9WVd/v9nX///koz/+SUZ//8QA/3/DADz/wwA7v8NAfb/FAf9/xMH/f8MAO3/DgTU
        /8jG868AAAAA////Aqb6qtcH8RP/B/ES/wXxEf8C8A7/AO4M/wDlC/8A2Qr/Rd9N/8P1xeu7977r9fX/
        1x4S/v8TBv7/DwL+/xUI/v9BN/7/V07+/zMo/v8YDP3/CwDn/3t16Pn+/v4U////PFX1Xf8P8Rr/EPEb
        /w3xGP8I8RT/A/AP/wHvDf8A6Qv/AN8L/wDaCv8A3wv/09L9/xQH/v8TBv//Ixf//7q2/8/9/f9c////
        Tt7d/6VZUP//DwL2/zkw4/////9S/v79eIr4jv9s93P/X/Zm/yrzNP8J8RX/BPEQ/wXxEf8G8RL/Ae4N
        /wDnC/8Q6Rr/r6v//xoO/v8WCv//hX7/9f///xQAAAAAAAAAAP///wLQzf6zFgr9/xIH6P/w7/2F/OjK
        p/KdIv/xpz3/++jN83j3f/sF8RH/APAM/4n3j/m7+br9kPiV/2P1a/9o9nD/oJv//SYb/v8gFP//jYf/
        8f7+/ggAAAAAAAAAAAAAAADb2v+bHxP+/w4C9v/Qzvuh/OjKp/CPA//ojgv/u/S1/wjwFP8C8Q7/AO4M
        /4D0hv/zvGv/6JMY/+aeNv/12K3p////Uu/v/o3X1f+h4d/+lf///wYAAAAAAAAAAAAAAAC4tP/bNyz+
        /xgM/v/Myf6n/fHgk/GUDf/qjQX/nOeE/wDsDP8A8Az/AOsM/2bwbP/wvHD/3oQA/9d/AP/uy5fdAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD+/v46+Pj/cuTj/pP4+P9E/v7+avOiLP/wkAT/6qc+
        /5vsjf9c8mT/c/J6/+Xx0f/nlyH/3IMA/9F8AP/v1rK7AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAA////LPe+bf/xlhD/64wB/+KNEf/orlr/6bJg/+OQFv/lhwD/3oQA
        /9B8Av/16NWb////DP7+/gwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA////
        APretNPynB//8ZMK/+eJAP/fhAD/34QA/+SHAP/oiQD/4YUA/9J9Av/lxJLx2rN69967ifH9+vdOAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP78+V71tVf/8pwd//GTCv/sjAD/6YoA
        /+uLAP/sjAD/6YoA/+CFAP/UfgD/x3YA/8FyAP/pzKHX/v7+AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAP///wT86MvD9KY0//KcH//xlQ//8ZEF//CPAf/wjgD/8I4A/++PAv/ujgT/5okB
        /9qBAP/rw4nt/v7+BAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///8e+tyx
        5/OlM//yniL/8pcU//GTCf/wjwL/8I8B//SpO//1sU7/8pgU//CTC//54b63AAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/v7+LPretOH0qj3/86Ao//KbG//xlAz/8Y8C
        //jKiPf+/Plq+c+S9frcr9X+/v4qAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAP7+/hj87dir98J1/fOkMP/ynB7/8ZQL//nQleMAAAAA/v7+DP///wQAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/v7+
        Pvzq0K/4y4r59Kc4//rds78AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD///8U/v7+YP79+0gAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/4P/APwB/wD4AA8A8AAH
        AOAAAwDAAAMAgAABAIAAAQAAAAAAAAAAAAAAYAAAAHAAAABwAAAP8AAAD/8AAAP/AIAB/wCAAf8AgAD/
        AMAB/wDgAf8A8BP/APwf/wD/H/8AKAAAABAAAAAgAAAAAQAgAAAAAABABAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAA////JsbyyG6X5puTsOyzif///wIAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAD+/v4Yl+ubpzfZP/0B0gz/AM8K/2jfbv2Qi+ez3t33RP///xrv7v02AAAA
        AAAAAAAAAAAAAAAAAAAAAADw/PEwX+Zm5wHYC/8A4Av/aeCA/1WAwv++6cz9FArW/1NM2/f///9QXlfp
        +8bE82gAAAAAAAAAAAAAAAD///8aXuxl6QDiC/8A5Av/Juow/313/f8OAfn/Gg7y/wsA6P82Ld3//v7+
        Qkc/6P82LtX93933SAAAAAAAAAAAlfaaqQDpDP8A7Az/AOcL/y3iNv+Igv75GQ3//x4S/v8PAvr/JBrn
        /2Jb8PcqH/b/CwDg/29p4N3///8G/P78KjjzQf0E8BD/AvAO/wDrC/8A2wv/kOiY+bfX2fGtqf/rFAj+
        /w4B+/8nHP3/PTL//xkN+/8dEuD/6Of6Tq76sYkQ8hv/EPIb/wrxFf8D8A//Ae0N/wDhC/8C4A3/joj+
        /xQH//9dVP/p7u3+Tvr6/j6Lhf7NDgLx/5qW8JX416O75cJp/9LzwPcM8Rj/EPEb/4T3iPtZ9WH/R/JQ
        /3hx//8fE///koz+pQAAAAAAAAAA3tz/ZhgM/P99dvW998N4weuOB/908XT/AvAO/wXsEP/q0ZL/5JAV
        /+29eO/7+/9C3Nv/aOLg/0QAAAAAAAAAAMfE/3psZP/3m5X+u/nPk6PvkQf/ucld/0XxTf9i8Wn/67xt
        /9qBAP/isGfZAAAAAAAAAAAAAAAAAAAAAAAAAAD///8A////BP///wr87ddk8pwe/+qLAv/jmi//5Z00
        /+WIAP/bggD/5b6Ezfjy6Vr+/fseAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA////EPfAcO/xlxP/6osB
        /+eJAP/riwD/5IcA/9WFD//CdQT/3rR40f///wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD869Jk9KxD
        //KZGP/xkQb/8I4A//CPAf/vkQj/6IsE/+iwXe3///8CAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA/v7+
        APvkw4n1r0r/8p0g//GUDP/xkgn//OrQufa8aPf63bJ8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAD///8A/e/cVvjJhOHzpjT/858m/////zz///8IAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAD///8G/fbqUPrcsZv///8QAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAD4PwAA4AcAAMADAACAAQAAgAAAAAAAAAAAAAAAABgAAAAYAAAA/AAAAD8AAAA/AACAHwAAwD8A
        AOB/AADw/wAA
</value>
  </data>
  <data name="gridColumn23.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_Height.VisibleIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="&gt;&gt;gridView2.Name" xml:space="preserve">
    <value>gridView2</value>
  </data>
  <data name="gridColumn4.VisibleIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="repDate.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;gridColumn2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn21.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="&gt;&gt;repDate.Name" xml:space="preserve">
    <value>repDate</value>
  </data>
  <data name="&gt;&gt;lkpStore.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LookUpEdit, DevExpress.XtraEditors.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colItemCode2.Name" xml:space="preserve">
    <value>colItemCode2</value>
  </data>
  <data name="&gt;&gt;gridView2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpItems.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="lkpStore.Properties.Columns7" xml:space="preserve">
    <value>StoreCode</value>
  </data>
  <data name="&gt;&gt;colExpire.Name" xml:space="preserve">
    <value>colExpire</value>
  </data>
  <data name="gridColumn1.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn8.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Width.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v14.1">
    <value>Numeric</value>
  </data>
  <data name="gridColumn1.VisibleIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="lkpVendor.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;barBtnClose.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarButtonItem, DevExpress.XtraBars.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Length.Location" type="System.Drawing.Point, System.Drawing">
    <value>665, 52</value>
  </data>
  <data name="grdEditItemQty.EmbeddedNavigator.ToolTip" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;gridView3.Name" xml:space="preserve">
    <value>gridView3</value>
  </data>
  <data name="&gt;&gt;gridColumn16.Name" xml:space="preserve">
    <value>gridColumn16</value>
  </data>
  <data name="txt_Width.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_Height.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpStore.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="txt_Length.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v14.1">
    <value>Default</value>
  </data>
  <data name="gridColumn23.Width" type="System.Int32, mscorlib">
    <value>86</value>
  </data>
  <data name="txt_Length.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="txt_Height.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;txt_Length.Name" xml:space="preserve">
    <value>txt_Length</value>
  </data>
  <data name="grdEditItemQty.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="col_OldPiecesCount.Width" type="System.Int32, mscorlib">
    <value>55</value>
  </data>
  <data name="lkpItems.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="labelControl15.Size" type="System.Drawing.Size, System.Drawing">
    <value>81, 13</value>
  </data>
  <data name="&gt;&gt;gridColumn8.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repSpin.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Width.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;bar2.Name" xml:space="preserve">
    <value>bar2</value>
  </data>
  <data name="&gt;&gt;txt_Height.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="repUOM.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;barAndDockingController1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarAndDockingController, DevExpress.XtraBars.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn9.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;lbl_Height.Name" xml:space="preserve">
    <value>lbl_Height</value>
  </data>
  <data name="colBatch.Caption" xml:space="preserve">
    <value>Batch</value>
  </data>
  <data name="labelControl5.Location" type="System.Drawing.Point, System.Drawing">
    <value>592, 53</value>
  </data>
  <data name="gridColumn19.Caption" xml:space="preserve">
    <value>VendorId</value>
  </data>
  <data name="&gt;&gt;gridColumn20.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn8.Width" type="System.Int32, mscorlib">
    <value>82</value>
  </data>
  <data name="repSpin.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gridColumn9.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn5.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn15.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colItemCode2.Caption" xml:space="preserve">
    <value>Code2</value>
  </data>
  <data name="lkpVendor.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repDate.VistaTimeProperties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_Width.Text" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="&gt;&gt;gridColumn22.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn13.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Width.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlTop.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Top</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;gridColumn18.Name" xml:space="preserve">
    <value>gridColumn18</value>
  </data>
  <data name="col_OldPiecesCount.Caption" xml:space="preserve">
    <value>Old Pieces Count</value>
  </data>
  <data name="&gt;&gt;gridColumn4.Name" xml:space="preserve">
    <value>gridColumn4</value>
  </data>
  <data name="gridColumn2.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;gridColumn7.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn9.Caption" xml:space="preserve">
    <value>P Price</value>
  </data>
  <data name="labelControl1.Text" xml:space="preserve">
    <value>Store</value>
  </data>
  <data name="gridColumn22.Width" type="System.Int32, mscorlib">
    <value>146</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Width.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v14.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;repositoryItemTextEdit1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemTextEdit, DevExpress.XtraEditors.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lbl_Width.Name" xml:space="preserve">
    <value>lbl_Width</value>
  </data>
  <data name="barDockControlTop.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="col_NewPiecesCount.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>592, 28</value>
  </data>
  <data name="barBtn_Save.Caption" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="&gt;&gt;lbl_Length.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <assembly alias="DevExpress.Data.v14.1" name="DevExpress.Data.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="lkpStore.Properties.Columns6" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v14.1">
    <value>Far</value>
  </data>
  <data name="bar2.Text" xml:space="preserve">
    <value>Custom 3</value>
  </data>
  <data name="gridColumn23.Caption" xml:space="preserve">
    <value>Balance</value>
  </data>
  <data name="lbl_Length.Text" xml:space="preserve">
    <value>Length</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit1View.Name" xml:space="preserve">
    <value>gridLookUpEdit1View</value>
  </data>
  <data name="gridColumn21.Caption" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="gridColumn4.Width" type="System.Int32, mscorlib">
    <value>101</value>
  </data>
  <data name="&gt;&gt;repositoryItemGridLookUpEdit1View.Name" xml:space="preserve">
    <value>repositoryItemGridLookUpEdit1View</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;lkpCompany.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="repSpin.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;btnSearch.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="&gt;&gt;txt_Length.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="gridColumn16.Caption" xml:space="preserve">
    <value>Factor</value>
  </data>
  <data name="&gt;&gt;gridColumn13.Name" xml:space="preserve">
    <value>gridColumn13</value>
  </data>
  <data name="col_OldPiecesCount.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;btnSearch.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SimpleButton, DevExpress.XtraEditors.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn14.Caption" xml:space="preserve">
    <value>CompanyId</value>
  </data>
  <data name="labelControl6.TabIndex" type="System.Int32, mscorlib">
    <value>46</value>
  </data>
  <data name="col_Length.VisibleIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;barBtn_Save.Name" xml:space="preserve">
    <value>barBtn_Save</value>
  </data>
  <data name="&gt;&gt;lkpItems.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpCompany.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="gridColumn11.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;colItemCode1.Name" xml:space="preserve">
    <value>colItemCode1</value>
  </data>
  <data name="&gt;&gt;btnSearch.Name" xml:space="preserve">
    <value>btnSearch</value>
  </data>
  <data name="gridColumn6.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;btnSearch.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;gridColumn16.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn5.Caption" xml:space="preserve">
    <value>New Qty</value>
  </data>
  <data name="&gt;&gt;colItemId.Name" xml:space="preserve">
    <value>colItemId</value>
  </data>
  <data name="txt_Width.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;gridColumn12.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;txt_Height.Name" xml:space="preserve">
    <value>txt_Height</value>
  </data>
  <data name="groupBox1.Size" type="System.Drawing.Size, System.Drawing">
    <value>1121, 104</value>
  </data>
  <data name="colItemNameAr.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="groupBox1.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>Yes</value>
  </data>
  <data name="labelControl6.Size" type="System.Drawing.Size, System.Drawing">
    <value>45, 13</value>
  </data>
  <data name="colBatch.Width" type="System.Int32, mscorlib">
    <value>68</value>
  </data>
  <data name="&gt;&gt;gridView1.Name" xml:space="preserve">
    <value>gridView1</value>
  </data>
  <data name="gridColumn7.Caption" xml:space="preserve">
    <value>UOM</value>
  </data>
  <data name="gridColumn2.VisibleIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="repDate.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="bar1.Text" xml:space="preserve">
    <value>Tools</value>
  </data>
  <data name="labelControl5.Text" xml:space="preserve">
    <value>Vendor</value>
  </data>
  <data name="&gt;&gt;gridColumn18.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Height.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colExpire.Caption" xml:space="preserve">
    <value>Expire Date</value>
  </data>
  <data name="&gt;&gt;gridColumn14.Name" xml:space="preserve">
    <value>gridColumn14</value>
  </data>
  <data name="&gt;&gt;gridColumn8.Name" xml:space="preserve">
    <value>gridColumn8</value>
  </data>
  <data name="&gt;&gt;col_NewPiecesCount.Name" xml:space="preserve">
    <value>col_NewPiecesCount</value>
  </data>
  <data name="&gt;&gt;gridColumn15.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="groupBox1.Text" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="lbl_Width.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="lkpStore.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="txt_Height.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Properties.Columns12" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="barBtnClose.Caption" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="lkpStore.Properties.Columns10" type="DevExpress.Utils.FormatType, DevExpress.Data.v14.1">
    <value>None</value>
  </data>
  <data name="lkpStore.Properties.Columns11" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;gridColumn23.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;col_Width.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barDockControlRight.Location" type="System.Drawing.Point, System.Drawing">
    <value>1144, 26</value>
  </data>
  <data name="colItemCode2.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="gridColumn1.Caption" xml:space="preserve">
    <value>Code1</value>
  </data>
  <data name="&gt;&gt;labelControl1.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpCompany.Name" xml:space="preserve">
    <value>lkpCompany</value>
  </data>
  <data name="&gt;&gt;colItemNameEn.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repositoryItemGridLookUpEdit1View.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lbl_Width.TabIndex" type="System.Int32, mscorlib">
    <value>49</value>
  </data>
  <data name="lkpStore.Properties.Columns5" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Length.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="colItemNameEn.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="grdEditItemQty.EmbeddedNavigator.ToolTipIconType" type="DevExpress.Utils.ToolTipIconType, DevExpress.Utils.v14.1">
    <value>None</value>
  </data>
  <data name="lkpItems.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;groupBox1.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;repUOM.Name" xml:space="preserve">
    <value>repUOM</value>
  </data>
  <data name="col_Length.Caption" xml:space="preserve">
    <value>Length</value>
  </data>
  <data name="gridColumn16.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="labelControl5.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="barBtnClose.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.Name" xml:space="preserve">
    <value>barDockControlBottom</value>
  </data>
  <data name="&gt;&gt;labelControl5.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lkpStore.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="colExpire.Width" type="System.Int32, mscorlib">
    <value>105</value>
  </data>
  <data name="txt_Width.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="&gt;&gt;labelControl6.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="&gt;&gt;gridLookUpEdit1View.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="colItemNameAr.Caption" xml:space="preserve">
    <value>Item Name</value>
  </data>
  <data name="gridColumn20.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpStore.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v14.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;gridColumn23.Name" xml:space="preserve">
    <value>gridColumn23</value>
  </data>
  <data name="&gt;&gt;col_Height.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;repDate.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemDateEdit, DevExpress.XtraEditors.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lbl_Height.Size" type="System.Drawing.Size, System.Drawing">
    <value>31, 13</value>
  </data>
  <data name="grdEditItemQty.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="&gt;&gt;col_OldPiecesCount.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpCompany.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="btnSearch.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 27</value>
  </data>
  <data name="lkpItems.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="&gt;&gt;bar1.Name" xml:space="preserve">
    <value>bar1</value>
  </data>
  <data name="grdEditItemQty.EmbeddedNavigator.ToolTipTitle" xml:space="preserve">
    <value />
  </data>
  <data name="col_NewPiecesCount.Caption" xml:space="preserve">
    <value>New Pieces Count</value>
  </data>
  <data name="colItemCode2.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="grdEditItemQty.EmbeddedNavigator.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>Inherit</value>
  </data>
  <data name="&gt;&gt;gridColumn14.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;lbl_Length.Name" xml:space="preserve">
    <value>lbl_Length</value>
  </data>
  <data name="groupBox1.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 30</value>
  </data>
  <data name="lkpStore.Location" type="System.Drawing.Point, System.Drawing">
    <value>369, 25</value>
  </data>
  <data name="&gt;&gt;lbl_Width.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colItemCode2.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpItems.Location" type="System.Drawing.Point, System.Drawing">
    <value>665, 25</value>
  </data>
  <data name="&gt;&gt;colItemNameAr.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colBatch.Name" xml:space="preserve">
    <value>colBatch</value>
  </data>
  <data name="&gt;&gt;gridView1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn13.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;col_Width.Name" xml:space="preserve">
    <value>col_Width</value>
  </data>
  <data name="repDate.VistaTimeProperties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repSpin.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="barBtn_Save.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="colItemNameEn.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpVendor.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="lkpCompany.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v14.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;gridColumn11.Name" xml:space="preserve">
    <value>gridColumn11</value>
  </data>
  <data name="txt_Height.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="gridColumn1.Width" type="System.Int32, mscorlib">
    <value>101</value>
  </data>
  <data name="&gt;&gt;labelControl15.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.Name" xml:space="preserve">
    <value>barDockControlLeft</value>
  </data>
  <data name="lkpStore.Properties.Columns4" xml:space="preserve">
    <value />
  </data>
  <data name="barDockControlBottom.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 471</value>
  </data>
  <data name="txt_Length.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="col_Height.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Width.Size" type="System.Drawing.Size, System.Drawing">
    <value>81, 20</value>
  </data>
  <data name="&gt;&gt;col_NewPiecesCount.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;colExpire.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="labelControl15.TabIndex" type="System.Int32, mscorlib">
    <value>24</value>
  </data>
  <data name="&gt;&gt;bar1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Bar, DevExpress.XtraBars.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repDate.VistaTimeProperties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl15.Location" type="System.Drawing.Point, System.Drawing">
    <value>1022, 28</value>
  </data>
  <data name="lkpVendor.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="groupBox1.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="lkpStore.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="&gt;&gt;txt_Width.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="repDate.Mask.EditMask" xml:space="preserve">
    <value>y</value>
  </data>
  <data name="gridColumn17.Caption" xml:space="preserve">
    <value>Index</value>
  </data>
  <data name="&gt;&gt;lbl_Height.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn15.Caption" xml:space="preserve">
    <value>UOM</value>
  </data>
  <data name="lbl_Height.Text" xml:space="preserve">
    <value>Height</value>
  </data>
  <data name="&gt;&gt;lbl_Length.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Width.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="colBatch.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repDate.VistaTimeProperties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v14.1">
    <value>DateTime</value>
  </data>
  <data name="gridColumn11.Width" type="System.Int32, mscorlib">
    <value>135</value>
  </data>
  <data name="gridColumn5.Width" type="System.Int32, mscorlib">
    <value>97</value>
  </data>
  <data name="labelControl6.Text" xml:space="preserve">
    <value>Company</value>
  </data>
  <data name="&gt;&gt;col_Length.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="grdEditItemQty.EmbeddedNavigator.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>Tile</value>
  </data>
  <data name="lbl_Length.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="repSpin.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v14.1">
    <value>Numeric</value>
  </data>
  <data name="barBtn_Help.Caption" xml:space="preserve">
    <value>Help</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Name" xml:space="preserve">
    <value>barDockControlTop</value>
  </data>
  <data name="&gt;&gt;gridColumn11.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Height.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v14.1">
    <value>Default</value>
  </data>
  <data name="txt_Length.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txt_Height.RightToLeft" type="System.Windows.Forms.RightToLeft, System.Windows.Forms">
    <value>No</value>
  </data>
  <data name="&gt;&gt;gridColumn1.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.Name" xml:space="preserve">
    <value>barDockControlRight</value>
  </data>
  <data name="txt_Length.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;gridColumn21.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repDate.VistaTimeProperties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v14.1">
    <value>Default</value>
  </data>
  <data name="repDate.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;col_OldPiecesCount.Name" xml:space="preserve">
    <value>col_OldPiecesCount</value>
  </data>
  <data name="&gt;&gt;lkpItems.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="repSpin.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn8.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="gridColumn22.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lbl_Width.Location" type="System.Drawing.Point, System.Drawing">
    <value>891, 56</value>
  </data>
  <data name="colBatch.VisibleIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="&gt;&gt;labelControl15.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;barAndDockingController1.Name" xml:space="preserve">
    <value>barAndDockingController1</value>
  </data>
  <data name="&gt;&gt;barDockControlLeft.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="repDate.VistaTimeProperties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="labelControl6.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="colItemCode1.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;barManager1.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarManager, DevExpress.XtraBars.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;barDockControlRight.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="&gt;&gt;labelControl1.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="lkpVendor.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="col_OldPiecesCount.VisibleIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="gridColumn12.Caption" xml:space="preserve">
    <value>Company Name</value>
  </data>
  <data name="grdEditItemQty.EmbeddedNavigator.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="gridColumn16.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;lkpVendor.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="gridColumn10.Caption" xml:space="preserve">
    <value>ItemId</value>
  </data>
  <data name="lkpStore.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;gridColumn21.Name" xml:space="preserve">
    <value>gridColumn21</value>
  </data>
  <data name="&gt;&gt;labelControl15.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barDockControlBottom.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Bottom</value>
  </data>
  <data name="&gt;&gt;repSpin.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit, DevExpress.XtraEditors.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn4.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpStore.Properties.Columns" xml:space="preserve">
    <value>StoreNameAr</value>
  </data>
  <data name="&gt;&gt;lkpStore.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;colItemId.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpStore.Size" type="System.Drawing.Size, System.Drawing">
    <value>217, 19</value>
  </data>
  <data name="gridColumn12.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;lkpVendor.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.GridLookUpEdit, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl5.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="lkpVendor.Location" type="System.Drawing.Point, System.Drawing">
    <value>369, 50</value>
  </data>
  <data name="&gt;&gt;barBtnClose.Name" xml:space="preserve">
    <value>barBtnClose</value>
  </data>
  <data name="lbl_Length.TabIndex" type="System.Int32, mscorlib">
    <value>49</value>
  </data>
  <data name="txt_Height.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkpVendor.Properties.NullText" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;groupBox1.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="&gt;&gt;txt_Height.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="repDate.VistaTimeProperties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;barManager1.Name" xml:space="preserve">
    <value>barManager1</value>
  </data>
  <data name="labelControl5.TabIndex" type="System.Int32, mscorlib">
    <value>30</value>
  </data>
  <data name="txt_Length.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Length.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_NewPiecesCount.VisibleIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;txt_Width.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="lbl_Length.Location" type="System.Drawing.Point, System.Drawing">
    <value>750, 56</value>
  </data>
  <data name="&gt;&gt;linqServerModeSource1.Type" xml:space="preserve">
    <value>DevExpress.Data.Linq.LinqServerModeSource, DevExpress.Data.v14.1.Linq, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;gridColumn10.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpVendor.Size" type="System.Drawing.Size, System.Drawing">
    <value>217, 19</value>
  </data>
  <data name="&gt;&gt;gridColumn1.Name" xml:space="preserve">
    <value>gridColumn1</value>
  </data>
  <data name="txt_Height.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repDate.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repDate.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v14.1">
    <value>Default</value>
  </data>
  <data name="txt_Height.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="barDockControlRight.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Right</value>
  </data>
  <data name="&gt;&gt;gridColumn5.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn9.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;labelControl6.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.LabelControl, DevExpress.XtraEditors.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn9.Width" type="System.Int32, mscorlib">
    <value>72</value>
  </data>
  <data name="&gt;&gt;gridColumn22.Name" xml:space="preserve">
    <value>gridColumn22</value>
  </data>
  <data name="txt_Length.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="gridColumn13.Width" type="System.Int32, mscorlib">
    <value>74</value>
  </data>
  <data name="&gt;&gt;groupBox1.Name" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="lkpCompany.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;lkpVendor.Name" xml:space="preserve">
    <value>lkpVendor</value>
  </data>
  <data name="&gt;&gt;lkpStore.Name" xml:space="preserve">
    <value>lkpStore</value>
  </data>
  <data name="grdEditItemQty.Size" type="System.Drawing.Size, System.Drawing">
    <value>1121, 319</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>Edit Item Quantity</value>
  </data>
  <data name="txt_Length.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="lkpItems.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;col_Height.Name" xml:space="preserve">
    <value>col_Height</value>
  </data>
  <data name="repDate.VistaTimeProperties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelControl1.TabIndex" type="System.Int32, mscorlib">
    <value>48</value>
  </data>
  <data name="lbl_Width.Size" type="System.Drawing.Size, System.Drawing">
    <value>28, 13</value>
  </data>
  <data name="&gt;&gt;gridColumn12.Name" xml:space="preserve">
    <value>gridColumn12</value>
  </data>
  <data name="&gt;&gt;barDockControlBottom.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="gridColumn12.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;gridColumn7.Name" xml:space="preserve">
    <value>gridColumn7</value>
  </data>
  <data name="gridColumn6.Width" type="System.Int32, mscorlib">
    <value>129</value>
  </data>
  <data name="gridColumn2.Width" type="System.Int32, mscorlib">
    <value>230</value>
  </data>
  <data name="&gt;&gt;barBtn_Help.Name" xml:space="preserve">
    <value>barBtn_Help</value>
  </data>
  <data name="txt_Height.Location" type="System.Drawing.Point, System.Drawing">
    <value>935, 52</value>
  </data>
  <data name="txt_Height.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.Columns3" type="DevExpress.Utils.FormatType, DevExpress.Data.v14.1">
    <value>None</value>
  </data>
  <data name="barDockControlLeft.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 26</value>
  </data>
  <data name="repDate.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="&gt;&gt;bar2.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.Bar, DevExpress.XtraBars.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="repSpin.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v14.1">
    <value>Default</value>
  </data>
  <data name="&gt;&gt;gridColumn10.Name" xml:space="preserve">
    <value>gridColumn10</value>
  </data>
  <data name="lkpStore.Properties.Columns2" type="System.Int32, mscorlib">
    <value>20</value>
  </data>
  <data name="&gt;&gt;lbl_Height.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;txt_Length.Type" xml:space="preserve">
    <value>DevExpress.XtraEditors.SpinEdit, DevExpress.XtraEditors.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="&gt;&gt;labelControl1.Name" xml:space="preserve">
    <value>labelControl1</value>
  </data>
  <data name="&gt;&gt;labelControl5.Name" xml:space="preserve">
    <value>labelControl5</value>
  </data>
  <data name="&gt;&gt;txt_Width.Name" xml:space="preserve">
    <value>txt_Width</value>
  </data>
  <data name="&gt;&gt;labelControl6.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="labelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>26, 13</value>
  </data>
  <data name="col_Width.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="colItemNameAr.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;colBatch.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="txt_Length.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v14.1">
    <value>Numeric</value>
  </data>
  <data name="lkpCompany.Size" type="System.Drawing.Size, System.Drawing">
    <value>217, 19</value>
  </data>
  <data name="col_Height.Caption" xml:space="preserve">
    <value>Height</value>
  </data>
  <data name="txt_Height.EditValue" type="System.Decimal, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlTop.Size" type="System.Drawing.Size, System.Drawing">
    <value>1144, 26</value>
  </data>
  <data name="gridColumn12.Width" type="System.Int32, mscorlib">
    <value>143</value>
  </data>
  <data name="&gt;&gt;gridColumn5.Name" xml:space="preserve">
    <value>gridColumn5</value>
  </data>
  <data name="barDockControlLeft.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 445</value>
  </data>
  <data name="&gt;&gt;barDockControlTop.Type" xml:space="preserve">
    <value>DevExpress.XtraBars.BarDockControl, DevExpress.XtraBars.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpItems.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpCompany.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Height.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>CenterScreen</value>
  </data>
  <data name="barDockControlBottom.Size" type="System.Drawing.Size, System.Drawing">
    <value>1144, 0</value>
  </data>
  <data name="&gt;&gt;labelControl6.Name" xml:space="preserve">
    <value>labelControl6</value>
  </data>
  <data name="txt_Width.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn7.Visible" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn6.VisibleIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="lkpCompany.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="lkpVendor.Properties.Buttons" type="DevExpress.XtraEditors.Controls.ButtonPredefines, DevExpress.Utils.v14.1">
    <value>Combo</value>
  </data>
  <data name="&gt;&gt;colItemNameAr.Name" xml:space="preserve">
    <value>colItemNameAr</value>
  </data>
  <data name="&gt;&gt;lkpItems.Name" xml:space="preserve">
    <value>lkpItems</value>
  </data>
  <data name="&gt;&gt;gridColumn15.Name" xml:space="preserve">
    <value>gridColumn15</value>
  </data>
  <data name="btnSearch.Text" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="gridColumn11.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="repSpin.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="barDockControlRight.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 445</value>
  </data>
  <data name="&gt;&gt;gridColumn2.Name" xml:space="preserve">
    <value>gridColumn2</value>
  </data>
  <data name="txt_Height.Size" type="System.Drawing.Size, System.Drawing">
    <value>81, 20</value>
  </data>
  <data name="col_Width.VisibleIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="labelControl15.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="&gt;&gt;gridColumn17.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="gridColumn7.VisibleIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="repSpin.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="colItemNameEn.Caption" xml:space="preserve">
    <value>Item F Name</value>
  </data>
  <data name="lkpStore.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;groupBox1.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lbl_Width.Parent" xml:space="preserve">
    <value>groupBox1</value>
  </data>
  <data name="&gt;&gt;gridColumn6.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Columns.GridColumn, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="lkpItems.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="colExpire.VisibleIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="grdEditItemQty.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 140</value>
  </data>
  <data name="txt_Height.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="gridColumn15.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="lkpStore.Properties.Columns13" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v14.1">
    <value>Far</value>
  </data>
  <data name="gridColumn8.Caption" xml:space="preserve">
    <value>S Price</value>
  </data>
  <data name="lkpItems.Size" type="System.Drawing.Size, System.Drawing">
    <value>351, 19</value>
  </data>
  <data name="repositoryItemTextEdit1.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Length.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="&gt;&gt;col_Length.Name" xml:space="preserve">
    <value>col_Length</value>
  </data>
  <data name="grdEditItemQty.EmbeddedNavigator.AllowHtmlTextInToolTip" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v14.1">
    <value>Default</value>
  </data>
  <data name="gridColumn13.Caption" xml:space="preserve">
    <value>Company code</value>
  </data>
  <data name="&gt;&gt;gridView3.Type" xml:space="preserve">
    <value>DevExpress.XtraGrid.Views.Grid.GridView, DevExpress.XtraGrid.v14.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a</value>
  </data>
  <data name="barBtn_Help.Hint" xml:space="preserve">
    <value> </value>
  </data>
  <data name="labelControl5.Size" type="System.Drawing.Size, System.Drawing">
    <value>34, 13</value>
  </data>
  <metadata name="linqServerModeSource1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>315, 17</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>54</value>
  </metadata>
  <metadata name="barAndDockingController1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>134, 17</value>
  </metadata>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="barManager1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="$this.Language" type="System.Globalization.CultureInfo, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>Arabic (Egypt)</value>
  </metadata>
</root>