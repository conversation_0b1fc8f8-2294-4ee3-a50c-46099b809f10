﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;

using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;

using DevExpress.XtraReports.UI;

namespace Reports
{
    public partial class rpt_SL_CustomerDiscount : DevExpress.XtraEditors.XtraForm
    {
        public bool UserCanOpen;

        string reportName, dateFilter, otherFilters;

        int customerId1, customerId2, custGroupId, salesEmpId;
        byte  fltrTyp_Date, FltrTyp_Customer,FltrTyp_InvBook;
        DateTime date1, date2;

        string custGroupAccNumber;
        List<int> lstStores = new List<int>();
        List<int> lst_invBooksId = new List<int>();

        public rpt_SL_CustomerDiscount(string reportName, string dateFilter, string otherFilters,
            byte fltrTyp_Date, DateTime date1, DateTime date2,
            byte FltrTyp_Customer, int customerId1, int customerId2,
            int custGroupId, string custGroupAccNumber, int salesEmpId,
            byte FltrTyp_InvBook, string InvBooks)
        {
            UserCanOpen = LoadPrivilege();
            if (UserCanOpen == false)
                return;

            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();

            this.reportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;
            this.fltrTyp_Date = fltrTyp_Date;
            this.FltrTyp_Customer = FltrTyp_Customer;

            this.date1 = date1;
            this.date2 = date2;

            this.customerId1 = customerId1;
            this.customerId2 = customerId2;

            this.custGroupId = custGroupId;
            this.custGroupAccNumber = custGroupAccNumber;
            this.salesEmpId = salesEmpId;
            this.FltrTyp_InvBook = FltrTyp_InvBook;
            Utilities.Get_ChkLst_Items(InvBooks, lst_invBooksId);

            getReportHeader();


            LoadData();

            ReportsUtils.ColumnChooser(grdCategory);
        }

        private void frm_SL_InvoiceList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                ReportsRTL.LTRLayout(this);

            ReportsUtils.Load_Grid_Layout(grdCategory, this.Name.Replace("frm_", "Rpt_"));
            //LoadPrivilege();
        }

        private void frm_Rep_FormClosing(object sender, FormClosingEventArgs e)
        {
            ReportsUtils.save_Grid_Layout(grdCategory, this.Name.Replace("frm_", "Rpt_"), true);
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Preview_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCategory, true).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        private void barBtnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadData();
        }


        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grdCategory.MinimumSize = grdCategory.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grdCategory, true, true).ShowPreview();
                grdCategory.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void barbtnPrintP_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grdCategory.MinimumSize = grdCategory.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grdCategory, false, true).ShowPreview();
                grdCategory.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void barbtnPrint_P_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grdCategory.MinimumSize = grdCategory.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grdCategory, false).ShowPreview();
                grdCategory.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                //lblCompName.Text = comp.CmpNameAr;
                lblReportName.Text = this.Text = reportName;
                this.Text = reportName;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        void LoadData()
        {
            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;

            ERPDataContext DB = new ERPDataContext();

            
                var data = (
                    (from c in DB.SL_Customers
                    //join a in DB.ACC_Accounts
                    //on c.AccountId equals a.AccountId
                    //where custGroupId == 0 ? true : a.AcNumber.StartsWith(custGroupAccNumber)

                    where FltrTyp_Customer == 1 ? c.CustomerId == customerId1 : true
                    where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 != 0) ?
                    c.CustomerId >= customerId1 && c.CustomerId <= customerId2 : true
                    where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 == 0) ?
                    c.CustomerId >= customerId1 : true
                    where (FltrTyp_Customer == 2 && customerId1 == 0 && customerId2 != 0) ?
                    c.CustomerId <= customerId2 : true

                    join i in DB.SL_Invoices
                    on c.CustomerId equals i.CustomerId
                    join d in DB.SL_InvoiceDetails
                    on i.SL_InvoiceId equals d.SL_InvoiceId

                    where fltrTyp_Date == 1 ? i.InvoiceDate.Date == date1 : true
                    where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                    i.InvoiceDate >= date1 && i.InvoiceDate <= date2 : true
                    where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                    i.InvoiceDate >= date1 : true
                    where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                    i.InvoiceDate <= date2 : true

                    where salesEmpId == 0 ? true : i.SalesEmpId == salesEmpId

                    where FltrTyp_InvBook == 0 ? true : (i.InvoiceBookId.HasValue && lst_invBooksId.Contains(i.InvoiceBookId.Value))

                    select new
                    {
                        c.CusNameAr,
                        DisValue = d.DiscountValue + i.DiscountValue

                    }).Union(from c in DB.SL_Customers
                        join a in DB.ACC_Accounts
                        on c.AccountId equals a.AccountId
                        where custGroupId == 0 ? true : a.AcNumber.StartsWith(custGroupAccNumber)

                        where FltrTyp_Customer == 1 ? c.CustomerId == customerId1 : true
                        where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 != 0) ?
                        c.CustomerId >= customerId1 && c.CustomerId <= customerId2 : true
                        where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 == 0) ?
                        c.CustomerId >= customerId1 : true
                        where (FltrTyp_Customer == 2 && customerId1 == 0 && customerId2 != 0) ?
                        c.CustomerId <= customerId2 : true

                        //join i in DB.ACC_CashNotes
                        //on c.CustomerId equals i.DealerId
                        //where i.IsPay== false
                        //where i.IsVendor == (byte)DAL.IsVendor.Customer

                        //     where fltrTyp_Date == 1 ? i.NoteDate.Date == date1 : true
                        //where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                        //i.NoteDate >= date1 && i.NoteDate <= date2 : true
                        //where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                        //i.NoteDate >= date1 : true
                        //where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                        //i.NoteDate <= date2 : true

                        //where salesEmpId == 0 ? true : i.EmpId == salesEmpId

                        //where FltrTyp_InvBook == 0 ? true : (i.BookId.HasValue && lst_invBooksId.Contains(i.BookId.Value))

                        select new
                        {
                            c.CusNameAr,
                            DisValue = (decimal)0

                        })
                        ).GroupBy(x => x.CusNameAr).ToList();




            var query = (from d in data
                            select new
                            {
                                CusNameAr = d.Key,
                                DiscountValue = d.Select(x => x.DisValue).Sum()
                            }).ToList();
                grdCategory.DataSource = query;
        }

        private void gridView1_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            if (e.ListSourceRowIndex < 0)
                return;

            if (e.Column.FieldName == "Index")
                e.Value = e.RowHandle() + 1;
        }

        public bool LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.rpt_SL_CustomerDiscount).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            return true;
        }
    }
}