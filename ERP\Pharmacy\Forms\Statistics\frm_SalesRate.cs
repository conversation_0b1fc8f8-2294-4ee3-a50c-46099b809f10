﻿using DAL;
using DAL.Res;
using DevExpress.XtraCharts;
using DevExpress.XtraPrinting;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Pharmacy.Forms
{
    public partial class frm_SalesRate : DevExpress.XtraEditors.XtraForm
    {
        ERPDataContext DB = new ERPDataContext();
        UserPriv prvlg;
        DataTable dt_Graph = new DataTable();
        public frm_SalesRate()
        {
            RTL.EnCulture(Shared.IsEnglish);

            InitializeComponent();
            RTL.RTL_BarManager(barManager1);
        }

        private void barBtnOk_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            DateTime From = Convert.ToDateTime(dtFromDate.EditValue);
            DateTime To = Convert.ToDateTime(dtToDate.EditValue);
            DateTime Prev_date = Shared.minDate;
            //var Result = (from i in DB.SL_Invoices
            //              where dtFromDate.EditValue == null ? true : (i.InvoiceDate.Date >= From.Date)
            //              where dtToDate.EditValue == null ? true : (i.InvoiceDate.Date <= To.Date)
            //              group i by i.InvoiceDate.Date into grp
            //              select new
            //              {
            //                  Date = grp.Select(a => a.InvoiceDate.Date).FirstOrDefault(),
            //                  Total = grp.Sum(a => a.Net),

            //              }).OrderBy(a => a.Date).ToList();
            dt_Graph.Rows.Clear();
            DateTime date_from = dtFromDate.DateTime == DateTime.MinValue ? new DateTime(MyHelper.Get_Server_DateTime().Year, 1, 1) : dtFromDate.DateTime.Date;
            DateTime date_to = dtToDate.DateTime == DateTime.MinValue ? MyHelper.Get_Server_DateTime().AddDays(1) : dtToDate.DateTime.Date;
            XYDiagram diagram = (XYDiagram)chartControl1.Diagram;
            if (cb_PeriodUnit.EditValue.ToString() == "Day")
            {
                diagram.AxisX.DateTimeScaleOptions.MeasureUnit = DateTimeMeasureUnit.Day;

                diagram.AxisX.DateTimeScaleOptions.GridAlignment = DateTimeGridAlignment.Day;
                Prev_date = date_from.AddDays(-1);
                for (DateTime d = date_from; d <= date_to; d = d.AddDays(Convert.ToInt32(txt_Period.EditValue)))
                {
                    DataRow dr = dt_Graph.NewRow();
                    dr["Date"] = d.Date/*.Month*/;
                    dr["Total"] = 0;
                   


                    dt_Graph.Rows.Add(dr);
                }
                //diagram.AxisX.DateTimeOptions.Format = DateTimeFormat.ShortDate;
                diagram.AxisX.DateTimeOptions.Format = DateTimeFormat.Custom;
                diagram.AxisX.DateTimeOptions.FormatString = "d MMMM";
                // diagram.AxisX.Label.TextPattern = "d MMMM";
            }
            else if (cb_PeriodUnit.EditValue.ToString() == "Week")
            {
                diagram.AxisX.DateTimeScaleOptions.MeasureUnit = DateTimeMeasureUnit.Week;
                diagram.AxisX.DateTimeScaleOptions.GridAlignment = DateTimeGridAlignment.Week;
                Prev_date = date_from.AddDays(-7);
                for (DateTime d = date_from; d <= date_to; d = d.AddDays(Convert.ToInt32(txt_Period.EditValue) * 7))
                {
                    DataRow dr = dt_Graph.NewRow();
                    dr["Date"] = d.Date/*.Month*/;
                    dr["Total"] = 0;
                    //dr["Cost"] = 0;
                    //dr["Profit"] = 0;



                    dt_Graph.Rows.Add(dr);
                }
                //diagram.AxisX.DateTimeOptions.Format = DateTimeFormat.ShortDate;
                diagram.AxisX.DateTimeOptions.Format = DateTimeFormat.Custom;
                diagram.AxisX.DateTimeOptions.FormatString = "d MMMM";
                //diagram.AxisX.Label.TextPattern = "d MMMM";
            }
            else if (cb_PeriodUnit.EditValue.ToString() == "Month")
            {
                Prev_date = date_from;
                for (DateTime d = date_from; d <= date_to; d = d.AddMonths(Convert.ToInt32(txt_Period.EditValue)))
                {
                    DataRow dr = dt_Graph.NewRow();
                    dr["Date"] = d.Date.AddMonths(1).AddDays(-1)/*.Month*/;
                    dr["Total"] = 0;
                    //dr["Cost"] = 0;
                    //dr["Profit"] = 0;



                    dt_Graph.Rows.Add(dr);
                }
                diagram.AxisX.DateTimeScaleOptions.MeasureUnit = DateTimeMeasureUnit.Month;
                diagram.AxisX.DateTimeScaleOptions.GridAlignment = DateTimeGridAlignment.Month;
                diagram.AxisX.Label.DateTimeOptions.Format = DateTimeFormat.Custom;
                diagram.AxisX.DateTimeOptions.FormatString = "MMMM";
                // diagram.AxisX.Label.TextPattern = "MMMM";
            }

           
            int index = 0;
            foreach (DataRow dr in dt_Graph.Rows)
            {

                //Prev_date = Convert.ToDateTime(dr["Date"]).Date;
               
        
                     var Result = (from i in DB.SL_Invoices
                                       //where dtFromDate.EditValue == null ? true : (i.InvoiceDate.Date >= From.Date)
                                       //where dtToDate.EditValue == null ? true : (i.InvoiceDate.Date <= To.Date)

                                   where i.InvoiceDate.Date < Convert.ToDateTime(dr["Date"]).Date
                                   where i.InvoiceDate.Date >= Prev_date.Date
                                   group i by i.InvoiceDate.Date into grp
                                   select new
                                   {
                                       Date = grp.Select(a => a.InvoiceDate.Date).FirstOrDefault(),
                                       Total = grp.Sum(a => a.Net),

                                   }).OrderBy(a => a.Date).ToList();


                Prev_date = Convert.ToDateTime(dr["Date"]).Date;
                //index++;
                //if (index == 1) continue;


                if (Result.Count() > 0)
                {
                    dr["Total"] = Result.DefaultIfEmpty().Sum(a => a.Total);
                   

                }




            }



            chartControl1.DataSource = dt_Graph;
                         
        }

        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void frm_SalesRate_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);
            dtFromDate.EditValue = MyHelper.Get_Server_DateTime();
            dtToDate.EditValue = MyHelper.Get_Server_DateTime();


            if (Shared.IsEnglish)
                RTL.LTRLayout(this);
            LoadPrivilege();
            dtFromDate.EditValue = MyHelper.Get_Server_DateTime();
            dtToDate.EditValue = MyHelper.Get_Server_DateTime();
            dt_Graph.Columns.Clear();
            dt_Graph.Columns.Add("Date", typeof(DateTime));
            dt_Graph.Columns.Add("Total", typeof(decimal));
           

            chartControl1.DataSource = dt_Graph;
            XYDiagram diagram = (XYDiagram)chartControl1.Diagram;
        }
        private void printableComponentLink1_CreateReportHeaderArea(object sender, DevExpress.XtraPrinting.CreateAreaEventArgs e)
        {
            string ReportName = this.Text;
            string dateFilters = string.Empty;
            string otherFilters = string.Empty;

            //create filters line
            if (dtFromDate.EditValue != null && dtToDate.EditValue != null)
                dateFilters = (Shared.IsEnglish == true ? ResAccEn.txtFrom : ResAccAr.txtFrom) +
                    dtFromDate.DateTime.ToShortDateString() +
                    (Shared.IsEnglish == true ? ResAccEn.txtTo : ResAccAr.txtTo) +
                    dtToDate.DateTime.ToShortDateString();

            else if (dtFromDate.EditValue != null && dtToDate.EditValue == null)
                dateFilters =
                    (Shared.IsEnglish == true ? ResAccEn.txtFromDate : ResAccAr.txtFromDate) +
                    dtFromDate.DateTime.ToShortDateString();
            else if (dtFromDate.EditValue == null && dtToDate.EditValue != null)
                dateFilters = (Shared.IsEnglish == true ? ResAccEn.txtToDate : ResAccAr.txtToDate) +
                    dtToDate.DateTime.ToShortDateString();
            else
                dateFilters = "";


            ErpUtils.CreateReportHeader(e, ReportName, dateFilters, otherFilters);
        }
        private void printableComponentLink1_CreateReportFooter(object sender, DevExpress.XtraPrinting.CreateAreaEventArgs e)
        {
            RectangleF recTotal = new RectangleF((float)10, (float)17, 740, (float)25);

            e.Graph.StringFormat = Shared.IsEnglish ? new BrickStringFormat(StringAlignment.Near) : new BrickStringFormat(StringAlignment.Far);
            e.Graph.Font = new Font("Times New Roman", 13, FontStyle.Regular);
            e.Graph.ForeColor = Color.Black;
            e.Graph.DefaultBrickStyle.BorderColor = Color.Transparent;
            e.Graph.BackColor = Color.Snow;


            //string total = txtTotal.Text;

            //e.Graph.DrawString(total, recTotal);            
        }


        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            
            if (this.Width==1376)
           chartControl1.Width = this.Width -300;
        
            PrintingSystem printSystem = new PrintingSystem(this.components);
            PrintableComponentLink printLink;
            if (this.components == null)
                printLink = new PrintableComponentLink();
            else
                printLink = new PrintableComponentLink(this.components);

            ((System.ComponentModel.ISupportInitialize)(printSystem)).BeginInit();

            printSystem.Links.AddRange(new object[] {
            printLink});
           
            printLink.Component = this.chartControl1;

            printLink.PaperKind = System.Drawing.Printing.PaperKind.A4;
            printLink.Landscape = true;
            printLink.Margins = new System.Drawing.Printing.Margins(5, 5, 135, 50);
            printLink.PrintingSystem = printSystem;
            printLink.PrintingSystemBase = printSystem;

            printLink.CreateMarginalHeaderArea +=
                new DevExpress.XtraPrinting.CreateAreaEventHandler(this.printableComponentLink1_CreateReportHeaderArea);
            printLink.CreateReportFooterArea +=
                new DevExpress.XtraPrinting.CreateAreaEventHandler(this.printableComponentLink1_CreateReportFooter);

            ((System.ComponentModel.ISupportInitialize)(printSystem)).EndInit();

            printLink.CreateDocument();
            printLink.ShowPreview();
            chartControl1.Width = this.Width-51;
        }

        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                prvlg = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.frm_SalesRate).FirstOrDefault();

                if (!prvlg.CanPrint)
                    barBtnPrint.Enabled = false;
                if (!prvlg.CanAdd)
                    barBtnOk.Enabled = false;
            }
        }
    }
}
