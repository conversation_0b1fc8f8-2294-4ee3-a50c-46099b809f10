namespace Reports
{
    partial class rpt_PR_ReturnInvoice
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(rpt_PR_ReturnInvoice));
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.xrTable2 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow2 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_Total = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Disc = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Price = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Batch = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Expire = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Qty = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_UOM = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_ItemName = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_code2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_code = new DevExpress.XtraReports.UI.XRTableCell();
            this.lbl_Paied = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Net = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Remains = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_DiscountR = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Tax = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Total = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Expenses = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel17 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel23 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel24 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel19 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel20 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel15 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel16 = new DevExpress.XtraReports.UI.XRLabel();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.lbl_User = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel6 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_notes = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel4 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Paymethod = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Drawer = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel11 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel12 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_date = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_store = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel7 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel8 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Number = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Serial = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Vendor = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel3 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel2 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel1 = new DevExpress.XtraReports.UI.XRLabel();
            this.lblReportName = new DevExpress.XtraReports.UI.XRLabel();
            this.picLogo = new DevExpress.XtraReports.UI.XRPictureBox();
            this.lblCompName = new DevExpress.XtraReports.UI.XRLabel();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.xrPageInfo1 = new DevExpress.XtraReports.UI.XRPageInfo();
            this.PageHeader = new DevExpress.XtraReports.UI.PageHeaderBand();
            this.xrTable1 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow1 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell9 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell4 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell7 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell6 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell5 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell10 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell8 = new DevExpress.XtraReports.UI.XRTableCell();
            this.ReportFooter = new DevExpress.XtraReports.UI.ReportFooterBand();
            this.lbl_TotalQty = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_totalPieces = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_TotalPacks = new DevExpress.XtraReports.UI.XRLabel();
            this.xrSubreport2 = new DevExpress.XtraReports.UI.XRSubreport();
            this.xrLabel10 = new DevExpress.XtraReports.UI.XRLabel();
            this.txt_custax = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_ScaleWeightSerial = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Destination = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_VehicleNumber = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_DriverName = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_AddTaxV = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_DeductTaxV = new DevExpress.XtraReports.UI.XRLabel();
            this.xrTable4 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow4 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_Pack = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_SalesTax = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_DiscountRatio = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_DiscountRatio2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_DiscountRatio3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_SalesTaxRatio = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_ManufactureDate = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTable3 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow3 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_Height = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Width = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Length = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_TotalQty = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Serial = new DevExpress.XtraReports.UI.XRTableCell();
            this.lbl_DiscountV = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel9 = new DevExpress.XtraReports.UI.XRLabel();
            this.lblTotalWords = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel5 = new DevExpress.XtraReports.UI.XRLabel();
            this.cell_PiecesCount = new DevExpress.XtraReports.UI.XRTableCell();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // Detail
            // 
            this.Detail.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable2});
            resources.ApplyResources(this.Detail, "Detail");
            this.Detail.Name = "Detail";
            this.Detail.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            // 
            // xrTable2
            // 
            this.xrTable2.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.xrTable2, "xrTable2");
            this.xrTable2.Name = "xrTable2";
            this.xrTable2.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow2});
            this.xrTable2.StylePriority.UseBorders = false;
            this.xrTable2.StylePriority.UseFont = false;
            this.xrTable2.StylePriority.UseTextAlignment = false;
            // 
            // xrTableRow2
            // 
            resources.ApplyResources(this.xrTableRow2, "xrTableRow2");
            this.xrTableRow2.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_Total,
            this.cell_Disc,
            this.cell_Price,
            this.cell_Batch,
            this.cell_Expire,
            this.cell_Qty,
            this.cell_UOM,
            this.cell_ItemName,
            this.cell_code2,
            this.cell_code});
            this.xrTableRow2.Name = "xrTableRow2";
            this.xrTableRow2.StylePriority.UseBackColor = false;
            this.xrTableRow2.StylePriority.UseFont = false;
            // 
            // cell_Total
            // 
            resources.ApplyResources(this.cell_Total, "cell_Total");
            this.cell_Total.Name = "cell_Total";
            // 
            // cell_Disc
            // 
            resources.ApplyResources(this.cell_Disc, "cell_Disc");
            this.cell_Disc.Name = "cell_Disc";
            // 
            // cell_Price
            // 
            resources.ApplyResources(this.cell_Price, "cell_Price");
            this.cell_Price.Name = "cell_Price";
            // 
            // cell_Batch
            // 
            resources.ApplyResources(this.cell_Batch, "cell_Batch");
            this.cell_Batch.Name = "cell_Batch";
            // 
            // cell_Expire
            // 
            resources.ApplyResources(this.cell_Expire, "cell_Expire");
            this.cell_Expire.Name = "cell_Expire";
            // 
            // cell_Qty
            // 
            resources.ApplyResources(this.cell_Qty, "cell_Qty");
            this.cell_Qty.Name = "cell_Qty";
            // 
            // cell_UOM
            // 
            resources.ApplyResources(this.cell_UOM, "cell_UOM");
            this.cell_UOM.Name = "cell_UOM";
            // 
            // cell_ItemName
            // 
            resources.ApplyResources(this.cell_ItemName, "cell_ItemName");
            this.cell_ItemName.Name = "cell_ItemName";
            // 
            // cell_code2
            // 
            resources.ApplyResources(this.cell_code2, "cell_code2");
            this.cell_code2.Name = "cell_code2";
            // 
            // cell_code
            // 
            resources.ApplyResources(this.cell_code, "cell_code");
            this.cell_code.Name = "cell_code";
            // 
            // lbl_Paied
            // 
            this.lbl_Paied.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.lbl_Paied, "lbl_Paied");
            this.lbl_Paied.Name = "lbl_Paied";
            this.lbl_Paied.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Paied.StylePriority.UseBorders = false;
            this.lbl_Paied.StylePriority.UseFont = false;
            this.lbl_Paied.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Net
            // 
            this.lbl_Net.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.lbl_Net, "lbl_Net");
            this.lbl_Net.Name = "lbl_Net";
            this.lbl_Net.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Net.StylePriority.UseBorders = false;
            this.lbl_Net.StylePriority.UseFont = false;
            this.lbl_Net.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Remains
            // 
            this.lbl_Remains.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.lbl_Remains, "lbl_Remains");
            this.lbl_Remains.Name = "lbl_Remains";
            this.lbl_Remains.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Remains.StylePriority.UseBorders = false;
            this.lbl_Remains.StylePriority.UseFont = false;
            this.lbl_Remains.StylePriority.UseTextAlignment = false;
            // 
            // lbl_DiscountR
            // 
            this.lbl_DiscountR.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.lbl_DiscountR, "lbl_DiscountR");
            this.lbl_DiscountR.Name = "lbl_DiscountR";
            this.lbl_DiscountR.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_DiscountR.StylePriority.UseBorders = false;
            this.lbl_DiscountR.StylePriority.UseFont = false;
            this.lbl_DiscountR.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Tax
            // 
            this.lbl_Tax.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.lbl_Tax, "lbl_Tax");
            this.lbl_Tax.Name = "lbl_Tax";
            this.lbl_Tax.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Tax.StylePriority.UseBorders = false;
            this.lbl_Tax.StylePriority.UseFont = false;
            this.lbl_Tax.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Total
            // 
            this.lbl_Total.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.lbl_Total, "lbl_Total");
            this.lbl_Total.Name = "lbl_Total";
            this.lbl_Total.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Total.StylePriority.UseBorders = false;
            this.lbl_Total.StylePriority.UseFont = false;
            this.lbl_Total.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Expenses
            // 
            this.lbl_Expenses.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.lbl_Expenses, "lbl_Expenses");
            this.lbl_Expenses.Name = "lbl_Expenses";
            this.lbl_Expenses.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Expenses.StylePriority.UseBorders = false;
            this.lbl_Expenses.StylePriority.UseFont = false;
            this.lbl_Expenses.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel17
            // 
            this.xrLabel17.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.xrLabel17, "xrLabel17");
            this.xrLabel17.Name = "xrLabel17";
            this.xrLabel17.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel17.StylePriority.UseBorders = false;
            this.xrLabel17.StylePriority.UseFont = false;
            this.xrLabel17.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel23
            // 
            this.xrLabel23.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.xrLabel23, "xrLabel23");
            this.xrLabel23.Name = "xrLabel23";
            this.xrLabel23.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel23.StylePriority.UseBorders = false;
            this.xrLabel23.StylePriority.UseFont = false;
            this.xrLabel23.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel24
            // 
            this.xrLabel24.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.xrLabel24, "xrLabel24");
            this.xrLabel24.Name = "xrLabel24";
            this.xrLabel24.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel24.StylePriority.UseBorders = false;
            this.xrLabel24.StylePriority.UseFont = false;
            this.xrLabel24.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel19
            // 
            this.xrLabel19.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.xrLabel19, "xrLabel19");
            this.xrLabel19.Name = "xrLabel19";
            this.xrLabel19.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel19.StylePriority.UseBorders = false;
            this.xrLabel19.StylePriority.UseFont = false;
            this.xrLabel19.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel20
            // 
            this.xrLabel20.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.xrLabel20, "xrLabel20");
            this.xrLabel20.Name = "xrLabel20";
            this.xrLabel20.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel20.StylePriority.UseBorders = false;
            this.xrLabel20.StylePriority.UseFont = false;
            this.xrLabel20.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel15
            // 
            this.xrLabel15.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.xrLabel15, "xrLabel15");
            this.xrLabel15.Name = "xrLabel15";
            this.xrLabel15.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel15.StylePriority.UseBorders = false;
            this.xrLabel15.StylePriority.UseFont = false;
            this.xrLabel15.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel16
            // 
            this.xrLabel16.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.xrLabel16, "xrLabel16");
            this.xrLabel16.Name = "xrLabel16";
            this.xrLabel16.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel16.StylePriority.UseBorders = false;
            this.xrLabel16.StylePriority.UseFont = false;
            this.xrLabel16.StylePriority.UseTextAlignment = false;
            // 
            // TopMargin
            // 
            this.TopMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.lbl_User,
            this.xrLabel6,
            this.lbl_notes,
            this.xrLabel4,
            this.lbl_Paymethod,
            this.lbl_Drawer,
            this.xrLabel11,
            this.xrLabel12,
            this.lbl_date,
            this.lbl_store,
            this.xrLabel7,
            this.xrLabel8,
            this.lbl_Number,
            this.lbl_Serial,
            this.lbl_Vendor,
            this.xrLabel3,
            this.xrLabel2,
            this.xrLabel1,
            this.lblReportName,
            this.picLogo,
            this.lblCompName});
            resources.ApplyResources(this.TopMargin, "TopMargin");
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            // 
            // lbl_User
            // 
            this.lbl_User.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lbl_User, "lbl_User");
            this.lbl_User.Name = "lbl_User";
            this.lbl_User.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_User.StylePriority.UseBorders = false;
            this.lbl_User.StylePriority.UseFont = false;
            this.lbl_User.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel6
            // 
            this.xrLabel6.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.xrLabel6, "xrLabel6");
            this.xrLabel6.Name = "xrLabel6";
            this.xrLabel6.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel6.StylePriority.UseBorders = false;
            this.xrLabel6.StylePriority.UseFont = false;
            this.xrLabel6.StylePriority.UseTextAlignment = false;
            // 
            // lbl_notes
            // 
            resources.ApplyResources(this.lbl_notes, "lbl_notes");
            this.lbl_notes.Multiline = true;
            this.lbl_notes.Name = "lbl_notes";
            this.lbl_notes.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_notes.StylePriority.UseFont = false;
            this.lbl_notes.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel4
            // 
            resources.ApplyResources(this.xrLabel4, "xrLabel4");
            this.xrLabel4.Name = "xrLabel4";
            this.xrLabel4.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel4.StylePriority.UseFont = false;
            this.xrLabel4.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Paymethod
            // 
            resources.ApplyResources(this.lbl_Paymethod, "lbl_Paymethod");
            this.lbl_Paymethod.Name = "lbl_Paymethod";
            this.lbl_Paymethod.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Paymethod.StylePriority.UseFont = false;
            this.lbl_Paymethod.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Drawer
            // 
            resources.ApplyResources(this.lbl_Drawer, "lbl_Drawer");
            this.lbl_Drawer.Name = "lbl_Drawer";
            this.lbl_Drawer.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Drawer.StylePriority.UseFont = false;
            this.lbl_Drawer.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel11
            // 
            resources.ApplyResources(this.xrLabel11, "xrLabel11");
            this.xrLabel11.Name = "xrLabel11";
            this.xrLabel11.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel11.StylePriority.UseFont = false;
            this.xrLabel11.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel12
            // 
            resources.ApplyResources(this.xrLabel12, "xrLabel12");
            this.xrLabel12.Name = "xrLabel12";
            this.xrLabel12.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel12.StylePriority.UseFont = false;
            this.xrLabel12.StylePriority.UseTextAlignment = false;
            // 
            // lbl_date
            // 
            resources.ApplyResources(this.lbl_date, "lbl_date");
            this.lbl_date.Name = "lbl_date";
            this.lbl_date.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_date.StylePriority.UseFont = false;
            this.lbl_date.StylePriority.UseTextAlignment = false;
            // 
            // lbl_store
            // 
            resources.ApplyResources(this.lbl_store, "lbl_store");
            this.lbl_store.Name = "lbl_store";
            this.lbl_store.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_store.StylePriority.UseFont = false;
            this.lbl_store.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel7
            // 
            resources.ApplyResources(this.xrLabel7, "xrLabel7");
            this.xrLabel7.Name = "xrLabel7";
            this.xrLabel7.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel7.StylePriority.UseFont = false;
            this.xrLabel7.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel8
            // 
            resources.ApplyResources(this.xrLabel8, "xrLabel8");
            this.xrLabel8.Name = "xrLabel8";
            this.xrLabel8.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel8.StylePriority.UseFont = false;
            this.xrLabel8.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Number
            // 
            resources.ApplyResources(this.lbl_Number, "lbl_Number");
            this.lbl_Number.Name = "lbl_Number";
            this.lbl_Number.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Number.StylePriority.UseFont = false;
            this.lbl_Number.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Serial
            // 
            resources.ApplyResources(this.lbl_Serial, "lbl_Serial");
            this.lbl_Serial.Name = "lbl_Serial";
            this.lbl_Serial.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Serial.StylePriority.UseFont = false;
            this.lbl_Serial.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Vendor
            // 
            resources.ApplyResources(this.lbl_Vendor, "lbl_Vendor");
            this.lbl_Vendor.Name = "lbl_Vendor";
            this.lbl_Vendor.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Vendor.StylePriority.UseFont = false;
            this.lbl_Vendor.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel3
            // 
            resources.ApplyResources(this.xrLabel3, "xrLabel3");
            this.xrLabel3.Name = "xrLabel3";
            this.xrLabel3.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel3.StylePriority.UseFont = false;
            this.xrLabel3.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel2
            // 
            resources.ApplyResources(this.xrLabel2, "xrLabel2");
            this.xrLabel2.Name = "xrLabel2";
            this.xrLabel2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel2.StylePriority.UseFont = false;
            this.xrLabel2.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel1
            // 
            resources.ApplyResources(this.xrLabel1, "xrLabel1");
            this.xrLabel1.Name = "xrLabel1";
            this.xrLabel1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel1.StylePriority.UseFont = false;
            this.xrLabel1.StylePriority.UseTextAlignment = false;
            // 
            // lblReportName
            // 
            resources.ApplyResources(this.lblReportName, "lblReportName");
            this.lblReportName.Name = "lblReportName";
            this.lblReportName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblReportName.StylePriority.UseFont = false;
            this.lblReportName.StylePriority.UseTextAlignment = false;
            // 
            // picLogo
            // 
            resources.ApplyResources(this.picLogo, "picLogo");
            this.picLogo.Name = "picLogo";
            this.picLogo.Sizing = DevExpress.XtraPrinting.ImageSizeMode.StretchImage;
            // 
            // lblCompName
            // 
            resources.ApplyResources(this.lblCompName, "lblCompName");
            this.lblCompName.Name = "lblCompName";
            this.lblCompName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblCompName.StylePriority.UseFont = false;
            this.lblCompName.StylePriority.UseTextAlignment = false;
            // 
            // BottomMargin
            // 
            this.BottomMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrPageInfo1});
            resources.ApplyResources(this.BottomMargin, "BottomMargin");
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            // 
            // xrPageInfo1
            // 
            resources.ApplyResources(this.xrPageInfo1, "xrPageInfo1");
            this.xrPageInfo1.Name = "xrPageInfo1";
            this.xrPageInfo1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrPageInfo1.StylePriority.UseTextAlignment = false;
            // 
            // PageHeader
            // 
            this.PageHeader.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable1});
            resources.ApplyResources(this.PageHeader, "PageHeader");
            this.PageHeader.Name = "PageHeader";
            // 
            // xrTable1
            // 
            this.xrTable1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.xrTable1, "xrTable1");
            this.xrTable1.Name = "xrTable1";
            this.xrTable1.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow1});
            this.xrTable1.StylePriority.UseBorders = false;
            this.xrTable1.StylePriority.UseFont = false;
            this.xrTable1.StylePriority.UseTextAlignment = false;
            // 
            // xrTableRow1
            // 
            resources.ApplyResources(this.xrTableRow1, "xrTableRow1");
            this.xrTableRow1.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell1,
            this.xrTableCell9,
            this.xrTableCell4,
            this.xrTableCell2,
            this.xrTableCell7,
            this.xrTableCell6,
            this.xrTableCell5,
            this.xrTableCell3,
            this.xrTableCell10,
            this.xrTableCell8});
            this.xrTableRow1.Name = "xrTableRow1";
            this.xrTableRow1.StylePriority.UseBackColor = false;
            this.xrTableRow1.StylePriority.UseFont = false;
            // 
            // xrTableCell1
            // 
            resources.ApplyResources(this.xrTableCell1, "xrTableCell1");
            this.xrTableCell1.Name = "xrTableCell1";
            // 
            // xrTableCell9
            // 
            resources.ApplyResources(this.xrTableCell9, "xrTableCell9");
            this.xrTableCell9.Name = "xrTableCell9";
            // 
            // xrTableCell4
            // 
            resources.ApplyResources(this.xrTableCell4, "xrTableCell4");
            this.xrTableCell4.Name = "xrTableCell4";
            // 
            // xrTableCell2
            // 
            resources.ApplyResources(this.xrTableCell2, "xrTableCell2");
            this.xrTableCell2.Name = "xrTableCell2";
            // 
            // xrTableCell7
            // 
            resources.ApplyResources(this.xrTableCell7, "xrTableCell7");
            this.xrTableCell7.Name = "xrTableCell7";
            // 
            // xrTableCell6
            // 
            resources.ApplyResources(this.xrTableCell6, "xrTableCell6");
            this.xrTableCell6.Name = "xrTableCell6";
            // 
            // xrTableCell5
            // 
            resources.ApplyResources(this.xrTableCell5, "xrTableCell5");
            this.xrTableCell5.Name = "xrTableCell5";
            // 
            // xrTableCell3
            // 
            resources.ApplyResources(this.xrTableCell3, "xrTableCell3");
            this.xrTableCell3.Name = "xrTableCell3";
            // 
            // xrTableCell10
            // 
            resources.ApplyResources(this.xrTableCell10, "xrTableCell10");
            this.xrTableCell10.Name = "xrTableCell10";
            // 
            // xrTableCell8
            // 
            resources.ApplyResources(this.xrTableCell8, "xrTableCell8");
            this.xrTableCell8.Name = "xrTableCell8";
            // 
            // ReportFooter
            // 
            this.ReportFooter.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.lbl_TotalQty,
            this.lbl_totalPieces,
            this.lbl_TotalPacks,
            this.xrSubreport2,
            this.xrLabel10,
            this.txt_custax,
            this.lbl_ScaleWeightSerial,
            this.lbl_Destination,
            this.lbl_VehicleNumber,
            this.lbl_DriverName,
            this.lbl_AddTaxV,
            this.lbl_DeductTaxV,
            this.xrTable4,
            this.xrTable3,
            this.lbl_DiscountV,
            this.xrLabel9,
            this.lblTotalWords,
            this.xrLabel5,
            this.lbl_Expenses,
            this.lbl_Net,
            this.lbl_Remains,
            this.lbl_DiscountR,
            this.xrLabel16,
            this.lbl_Total,
            this.lbl_Paied,
            this.xrLabel17,
            this.xrLabel23,
            this.xrLabel24,
            this.xrLabel19,
            this.xrLabel20,
            this.xrLabel15,
            this.lbl_Tax});
            resources.ApplyResources(this.ReportFooter, "ReportFooter");
            this.ReportFooter.Name = "ReportFooter";
            // 
            // lbl_TotalQty
            // 
            this.lbl_TotalQty.CanGrow = false;
            resources.ApplyResources(this.lbl_TotalQty, "lbl_TotalQty");
            this.lbl_TotalQty.Name = "lbl_TotalQty";
            this.lbl_TotalQty.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_TotalQty.StylePriority.UseBorders = false;
            this.lbl_TotalQty.StylePriority.UseFont = false;
            this.lbl_TotalQty.StylePriority.UsePadding = false;
            this.lbl_TotalQty.StylePriority.UseTextAlignment = false;
            // 
            // lbl_totalPieces
            // 
            this.lbl_totalPieces.CanGrow = false;
            resources.ApplyResources(this.lbl_totalPieces, "lbl_totalPieces");
            this.lbl_totalPieces.Name = "lbl_totalPieces";
            this.lbl_totalPieces.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_totalPieces.StylePriority.UseBorders = false;
            this.lbl_totalPieces.StylePriority.UseFont = false;
            this.lbl_totalPieces.StylePriority.UsePadding = false;
            this.lbl_totalPieces.StylePriority.UseTextAlignment = false;
            // 
            // lbl_TotalPacks
            // 
            this.lbl_TotalPacks.CanGrow = false;
            resources.ApplyResources(this.lbl_TotalPacks, "lbl_TotalPacks");
            this.lbl_TotalPacks.Name = "lbl_TotalPacks";
            this.lbl_TotalPacks.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_TotalPacks.StylePriority.UseBorders = false;
            this.lbl_TotalPacks.StylePriority.UseFont = false;
            this.lbl_TotalPacks.StylePriority.UsePadding = false;
            this.lbl_TotalPacks.StylePriority.UseTextAlignment = false;
            // 
            // xrSubreport2
            // 
            resources.ApplyResources(this.xrSubreport2, "xrSubreport2");
            this.xrSubreport2.Name = "xrSubreport2";
            // 
            // xrLabel10
            // 
            this.xrLabel10.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.xrLabel10, "xrLabel10");
            this.xrLabel10.Name = "xrLabel10";
            this.xrLabel10.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel10.StylePriority.UseBorders = false;
            this.xrLabel10.StylePriority.UseFont = false;
            this.xrLabel10.StylePriority.UseTextAlignment = false;
            // 
            // txt_custax
            // 
            this.txt_custax.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.txt_custax, "txt_custax");
            this.txt_custax.Name = "txt_custax";
            this.txt_custax.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.txt_custax.StylePriority.UseBorders = false;
            this.txt_custax.StylePriority.UseFont = false;
            this.txt_custax.StylePriority.UseTextAlignment = false;
            // 
            // lbl_ScaleWeightSerial
            // 
            this.lbl_ScaleWeightSerial.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lbl_ScaleWeightSerial, "lbl_ScaleWeightSerial");
            this.lbl_ScaleWeightSerial.Name = "lbl_ScaleWeightSerial";
            this.lbl_ScaleWeightSerial.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_ScaleWeightSerial.StylePriority.UseBorders = false;
            this.lbl_ScaleWeightSerial.StylePriority.UseFont = false;
            this.lbl_ScaleWeightSerial.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Destination
            // 
            this.lbl_Destination.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lbl_Destination, "lbl_Destination");
            this.lbl_Destination.Name = "lbl_Destination";
            this.lbl_Destination.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Destination.StylePriority.UseBorders = false;
            this.lbl_Destination.StylePriority.UseFont = false;
            this.lbl_Destination.StylePriority.UseTextAlignment = false;
            // 
            // lbl_VehicleNumber
            // 
            this.lbl_VehicleNumber.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lbl_VehicleNumber, "lbl_VehicleNumber");
            this.lbl_VehicleNumber.Name = "lbl_VehicleNumber";
            this.lbl_VehicleNumber.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_VehicleNumber.StylePriority.UseBorders = false;
            this.lbl_VehicleNumber.StylePriority.UseFont = false;
            this.lbl_VehicleNumber.StylePriority.UseTextAlignment = false;
            // 
            // lbl_DriverName
            // 
            this.lbl_DriverName.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lbl_DriverName, "lbl_DriverName");
            this.lbl_DriverName.Name = "lbl_DriverName";
            this.lbl_DriverName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_DriverName.StylePriority.UseBorders = false;
            this.lbl_DriverName.StylePriority.UseFont = false;
            this.lbl_DriverName.StylePriority.UseTextAlignment = false;
            // 
            // lbl_AddTaxV
            // 
            this.lbl_AddTaxV.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.lbl_AddTaxV, "lbl_AddTaxV");
            this.lbl_AddTaxV.Name = "lbl_AddTaxV";
            this.lbl_AddTaxV.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_AddTaxV.StylePriority.UseBorders = false;
            this.lbl_AddTaxV.StylePriority.UseFont = false;
            this.lbl_AddTaxV.StylePriority.UseTextAlignment = false;
            // 
            // lbl_DeductTaxV
            // 
            this.lbl_DeductTaxV.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.lbl_DeductTaxV, "lbl_DeductTaxV");
            this.lbl_DeductTaxV.Name = "lbl_DeductTaxV";
            this.lbl_DeductTaxV.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_DeductTaxV.StylePriority.UseBorders = false;
            this.lbl_DeductTaxV.StylePriority.UseFont = false;
            this.lbl_DeductTaxV.StylePriority.UseTextAlignment = false;
            // 
            // xrTable4
            // 
            resources.ApplyResources(this.xrTable4, "xrTable4");
            this.xrTable4.Name = "xrTable4";
            this.xrTable4.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow4});
            this.xrTable4.StylePriority.UseTextAlignment = false;
            // 
            // xrTableRow4
            // 
            this.xrTableRow4.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_Pack,
            this.cell_SalesTax,
            this.cell_DiscountRatio,
            this.cell_DiscountRatio2,
            this.cell_DiscountRatio3,
            this.cell_SalesTaxRatio,
            this.cell_ManufactureDate});
            resources.ApplyResources(this.xrTableRow4, "xrTableRow4");
            this.xrTableRow4.Name = "xrTableRow4";
            // 
            // cell_Pack
            // 
            resources.ApplyResources(this.cell_Pack, "cell_Pack");
            this.cell_Pack.Name = "cell_Pack";
            // 
            // cell_SalesTax
            // 
            resources.ApplyResources(this.cell_SalesTax, "cell_SalesTax");
            this.cell_SalesTax.Name = "cell_SalesTax";
            // 
            // cell_DiscountRatio
            // 
            resources.ApplyResources(this.cell_DiscountRatio, "cell_DiscountRatio");
            this.cell_DiscountRatio.Name = "cell_DiscountRatio";
            // 
            // cell_DiscountRatio2
            // 
            resources.ApplyResources(this.cell_DiscountRatio2, "cell_DiscountRatio2");
            this.cell_DiscountRatio2.Name = "cell_DiscountRatio2";
            // 
            // cell_DiscountRatio3
            // 
            resources.ApplyResources(this.cell_DiscountRatio3, "cell_DiscountRatio3");
            this.cell_DiscountRatio3.Name = "cell_DiscountRatio3";
            // 
            // cell_SalesTaxRatio
            // 
            resources.ApplyResources(this.cell_SalesTaxRatio, "cell_SalesTaxRatio");
            this.cell_SalesTaxRatio.Name = "cell_SalesTaxRatio";
            // 
            // cell_ManufactureDate
            // 
            resources.ApplyResources(this.cell_ManufactureDate, "cell_ManufactureDate");
            this.cell_ManufactureDate.Name = "cell_ManufactureDate";
            // 
            // xrTable3
            // 
            resources.ApplyResources(this.xrTable3, "xrTable3");
            this.xrTable3.Name = "xrTable3";
            this.xrTable3.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow3});
            this.xrTable3.StylePriority.UseTextAlignment = false;
            // 
            // xrTableRow3
            // 
            this.xrTableRow3.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_PiecesCount,
            this.cell_Height,
            this.cell_Width,
            this.cell_Length,
            this.cell_TotalQty,
            this.cell_Serial});
            resources.ApplyResources(this.xrTableRow3, "xrTableRow3");
            this.xrTableRow3.Name = "xrTableRow3";
            // 
            // cell_Height
            // 
            resources.ApplyResources(this.cell_Height, "cell_Height");
            this.cell_Height.Name = "cell_Height";
            // 
            // cell_Width
            // 
            resources.ApplyResources(this.cell_Width, "cell_Width");
            this.cell_Width.Name = "cell_Width";
            // 
            // cell_Length
            // 
            resources.ApplyResources(this.cell_Length, "cell_Length");
            this.cell_Length.Name = "cell_Length";
            // 
            // cell_TotalQty
            // 
            resources.ApplyResources(this.cell_TotalQty, "cell_TotalQty");
            this.cell_TotalQty.Name = "cell_TotalQty";
            // 
            // cell_Serial
            // 
            resources.ApplyResources(this.cell_Serial, "cell_Serial");
            this.cell_Serial.Name = "cell_Serial";
            // 
            // lbl_DiscountV
            // 
            this.lbl_DiscountV.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.lbl_DiscountV, "lbl_DiscountV");
            this.lbl_DiscountV.Name = "lbl_DiscountV";
            this.lbl_DiscountV.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_DiscountV.StylePriority.UseBorders = false;
            this.lbl_DiscountV.StylePriority.UseFont = false;
            this.lbl_DiscountV.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel9
            // 
            this.xrLabel9.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.xrLabel9, "xrLabel9");
            this.xrLabel9.Name = "xrLabel9";
            this.xrLabel9.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel9.StylePriority.UseBorders = false;
            this.xrLabel9.StylePriority.UseFont = false;
            this.xrLabel9.StylePriority.UseTextAlignment = false;
            // 
            // lblTotalWords
            // 
            resources.ApplyResources(this.lblTotalWords, "lblTotalWords");
            this.lblTotalWords.Name = "lblTotalWords";
            this.lblTotalWords.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblTotalWords.StylePriority.UseFont = false;
            this.lblTotalWords.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel5
            // 
            resources.ApplyResources(this.xrLabel5, "xrLabel5");
            this.xrLabel5.Name = "xrLabel5";
            this.xrLabel5.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel5.StylePriority.UseFont = false;
            this.xrLabel5.StylePriority.UseTextAlignment = false;
            // 
            // cell_PiecesCount
            // 
            resources.ApplyResources(this.cell_PiecesCount, "cell_PiecesCount");
            this.cell_PiecesCount.Name = "cell_PiecesCount";
            // 
            // rpt_PR_ReturnInvoice
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail,
            this.TopMargin,
            this.BottomMargin,
            this.PageHeader,
            this.ReportFooter});
            resources.ApplyResources(this, "$this");
            this.Version = "15.1";
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.XRLabel lblReportName;
        private DevExpress.XtraReports.UI.XRPictureBox picLogo;
        private DevExpress.XtraReports.UI.XRLabel lblCompName;
        private DevExpress.XtraReports.UI.XRPageInfo xrPageInfo1;
        private DevExpress.XtraReports.UI.XRLabel xrLabel1;
        private DevExpress.XtraReports.UI.XRLabel xrLabel3;
        private DevExpress.XtraReports.UI.XRLabel xrLabel2;
        private DevExpress.XtraReports.UI.XRLabel lbl_date;
        private DevExpress.XtraReports.UI.XRLabel lbl_store;
        private DevExpress.XtraReports.UI.XRLabel xrLabel7;
        private DevExpress.XtraReports.UI.XRLabel xrLabel8;
        private DevExpress.XtraReports.UI.XRLabel lbl_Number;
        private DevExpress.XtraReports.UI.XRLabel lbl_Serial;
        private DevExpress.XtraReports.UI.XRLabel lbl_Vendor;
        private DevExpress.XtraReports.UI.XRLabel lbl_Paymethod;
        private DevExpress.XtraReports.UI.XRLabel lbl_Drawer;
        private DevExpress.XtraReports.UI.XRLabel xrLabel11;
        private DevExpress.XtraReports.UI.XRLabel xrLabel12;
        private DevExpress.XtraReports.UI.XRLabel xrLabel15;
        private DevExpress.XtraReports.UI.XRLabel xrLabel16;
        private DevExpress.XtraReports.UI.XRLabel xrLabel19;
        private DevExpress.XtraReports.UI.XRLabel xrLabel20;
        private DevExpress.XtraReports.UI.XRLabel xrLabel23;
        private DevExpress.XtraReports.UI.XRLabel xrLabel24;
        private DevExpress.XtraReports.UI.XRLabel lbl_Paied;
        private DevExpress.XtraReports.UI.XRLabel lbl_Net;
        private DevExpress.XtraReports.UI.XRLabel lbl_Remains;
        private DevExpress.XtraReports.UI.XRLabel lbl_DiscountR;
        private DevExpress.XtraReports.UI.XRLabel lbl_Tax;
        private DevExpress.XtraReports.UI.XRLabel lbl_Total;
        private DevExpress.XtraReports.UI.XRLabel lbl_Expenses;
        private DevExpress.XtraReports.UI.XRLabel xrLabel17;
        private DevExpress.XtraReports.UI.PageHeaderBand PageHeader;
        private DevExpress.XtraReports.UI.XRTable xrTable1;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow1;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell1;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell4;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell2;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell5;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell3;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell6;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell9;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell7;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell8;
        private DevExpress.XtraReports.UI.XRTable xrTable2;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow2;
        private DevExpress.XtraReports.UI.XRTableCell cell_Total;
        private DevExpress.XtraReports.UI.XRTableCell cell_Disc;
        private DevExpress.XtraReports.UI.XRTableCell cell_Price;
        private DevExpress.XtraReports.UI.XRTableCell cell_Batch;
        private DevExpress.XtraReports.UI.XRTableCell cell_Expire;
        private DevExpress.XtraReports.UI.XRTableCell cell_Qty;
        private DevExpress.XtraReports.UI.XRTableCell cell_UOM;
        private DevExpress.XtraReports.UI.XRTableCell cell_ItemName;
        private DevExpress.XtraReports.UI.XRTableCell cell_code;
        private DevExpress.XtraReports.UI.XRLabel lbl_notes;
        private DevExpress.XtraReports.UI.XRLabel xrLabel4;
        private DevExpress.XtraReports.UI.ReportFooterBand ReportFooter;
        private DevExpress.XtraReports.UI.XRTableCell cell_code2;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell10;
        private DevExpress.XtraReports.UI.XRLabel lblTotalWords;
        private DevExpress.XtraReports.UI.XRLabel xrLabel5;
        private DevExpress.XtraReports.UI.XRLabel lbl_User;
        private DevExpress.XtraReports.UI.XRLabel xrLabel6;
        private DevExpress.XtraReports.UI.XRLabel lbl_DiscountV;
        private DevExpress.XtraReports.UI.XRLabel xrLabel9;
        private DevExpress.XtraReports.UI.XRTable xrTable3;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow3;
        private DevExpress.XtraReports.UI.XRTableCell cell_Height;
        private DevExpress.XtraReports.UI.XRTableCell cell_Width;
        private DevExpress.XtraReports.UI.XRTableCell cell_Length;
        private DevExpress.XtraReports.UI.XRTableCell cell_TotalQty;
        private DevExpress.XtraReports.UI.XRTable xrTable4;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow4;
        private DevExpress.XtraReports.UI.XRTableCell cell_DiscountRatio;
        private DevExpress.XtraReports.UI.XRTableCell cell_DiscountRatio2;
        private DevExpress.XtraReports.UI.XRTableCell cell_DiscountRatio3;
        private DevExpress.XtraReports.UI.XRTableCell cell_SalesTaxRatio;
        private DevExpress.XtraReports.UI.XRLabel lbl_DeductTaxV;
        private DevExpress.XtraReports.UI.XRTableCell cell_SalesTax;
        private DevExpress.XtraReports.UI.XRLabel lbl_AddTaxV;
        private DevExpress.XtraReports.UI.XRLabel lbl_Destination;
        private DevExpress.XtraReports.UI.XRLabel lbl_VehicleNumber;
        private DevExpress.XtraReports.UI.XRLabel lbl_DriverName;
        private DevExpress.XtraReports.UI.XRLabel lbl_ScaleWeightSerial;
        private DevExpress.XtraReports.UI.XRTableCell cell_Serial;
        private DevExpress.XtraReports.UI.XRTableCell cell_ManufactureDate;
        private DevExpress.XtraReports.UI.XRLabel xrLabel10;
        private DevExpress.XtraReports.UI.XRLabel txt_custax;
        private DevExpress.XtraReports.UI.XRSubreport xrSubreport2;
        private DevExpress.XtraReports.UI.XRTableCell cell_Pack;
        private DevExpress.XtraReports.UI.XRLabel lbl_TotalQty;
        private DevExpress.XtraReports.UI.XRLabel lbl_totalPieces;
        private DevExpress.XtraReports.UI.XRLabel lbl_TotalPacks;
        private DevExpress.XtraReports.UI.XRTableCell cell_PiecesCount;
    }
}
