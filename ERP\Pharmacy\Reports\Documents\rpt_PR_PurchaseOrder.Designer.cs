namespace Reports
{
    partial class rpt_PR_PurchaseOrder
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(rpt_PR_PurchaseOrder));
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.xrLabel33 = new DevExpress.XtraReports.UI.XRLabel();
            this.txt_VendorEmail = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel21 = new DevExpress.XtraReports.UI.XRLabel();
            this.txt_CostCenter = new DevExpress.XtraReports.UI.XRLabel();
            this.txt_CostCenterCode = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel19 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel16 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel5 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_DeliverDate = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_AttnMr = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel1 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLine1 = new DevExpress.XtraReports.UI.XRLine();
            this.xrLabel4 = new DevExpress.XtraReports.UI.XRLabel();
            this.lblCompName = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_AttnMr_Job = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel2 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel14 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_store = new DevExpress.XtraReports.UI.XRLabel();
            this.lblReportName = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Number = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_notes = new DevExpress.XtraReports.UI.XRLabel();
            this.picLogo = new DevExpress.XtraReports.UI.XRPictureBox();
            this.xrLabel6 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_date = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_User = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel7 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel8 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_ShipTo = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel12 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_VenFax = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_VenTel = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel3 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Serial = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Vendor = new DevExpress.XtraReports.UI.XRLabel();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.PageHeader = new DevExpress.XtraReports.UI.PageHeaderBand();
            this.ReportFooter = new DevExpress.XtraReports.UI.ReportFooterBand();
            this.xrLabel18 = new DevExpress.XtraReports.UI.XRLabel();
            this.lblTotalWords = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel15 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_tax = new DevExpress.XtraReports.UI.XRLabel();
            this.xrTable5 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow5 = new DevExpress.XtraReports.UI.XRTableRow();
            this.col_OutTrns_ItemCode2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_OuTrans_Expire = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_OutTrns_Batch = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_OutTrns_Length = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_OutTrns_Width = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_OutTrns_Height = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_OutTrns_TotlQty = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_OutTrns_PcCount = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_OutTrns_Qc = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTable6 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow6 = new DevExpress.XtraReports.UI.XRTableRow();
            this.col_SlInv_Code2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_SlInv_Expire = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_SlInv_Batch = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_SlInv_Length = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_SlInv_Width = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_SlInv_Height = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_SlInv_TotlQty = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_SlInv_PcCount = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_SlInv_Qc = new DevExpress.XtraReports.UI.XRTableCell();
            this.lbl_DiscountV = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Total = new DevExpress.XtraReports.UI.XRLabel();
            this.xrPageInfo2 = new DevExpress.XtraReports.UI.XRPageInfo();
            this.xrLine2 = new DevExpress.XtraReports.UI.XRLine();
            this.lbl_Net = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel9 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel10 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_DiscountR = new DevExpress.XtraReports.UI.XRLabel();
            this.xrTable4 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow4 = new DevExpress.XtraReports.UI.XRTableRow();
            this.Cell_MUOM = new DevExpress.XtraReports.UI.XRTableCell();
            this.Cell_MUOM_Factor = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Factor = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTable3 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow3 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_Height = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Width = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Length = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_TotalQty = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_code2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.DetailReport_Po_Order = new DevExpress.XtraReports.UI.DetailReportBand();
            this.Detail1 = new DevExpress.XtraReports.UI.DetailBand();
            this.xrTable2 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow2 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_Total = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Disc = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Price = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Qty = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_UOM = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_ItemName = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_code = new DevExpress.XtraReports.UI.XRTableCell();
            this.ReportHeader = new DevExpress.XtraReports.UI.ReportHeaderBand();
            this.xrTable1 = new DevExpress.XtraReports.UI.XRTable();
            this.tableRow1 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableRow1 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell9 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell7 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell6 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell5 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell8 = new DevExpress.XtraReports.UI.XRTableCell();
            this.GroupFooter1 = new DevExpress.XtraReports.UI.GroupFooterBand();
            this.xrLabel31 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel32 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel23 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel24 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel25 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel26 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel27 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel28 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel29 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel30 = new DevExpress.XtraReports.UI.XRLabel();
            this.txtPayments = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel20 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel13 = new DevExpress.XtraReports.UI.XRLabel();
            this.txtReferenceNo = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel17 = new DevExpress.XtraReports.UI.XRLabel();
            this.txt_QutationDate = new DevExpress.XtraReports.UI.XRLabel();
            this.DetailReport_InTrns = new DevExpress.XtraReports.UI.DetailReportBand();
            this.Detail2 = new DevExpress.XtraReports.UI.DetailBand();
            this.table2 = new DevExpress.XtraReports.UI.XRTable();
            this.tableRow4 = new DevExpress.XtraReports.UI.XRTableRow();
            this.col_OutTrns_Qty = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_OutTrns_UOM = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_OutTrns_ItemName = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_OutTrns_ItemCode1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_OutTrns_Store = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_OutTrns_Code = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_OutTrns_Date = new DevExpress.XtraReports.UI.XRTableCell();
            this.ReportHeader1 = new DevExpress.XtraReports.UI.ReportHeaderBand();
            this.table1 = new DevExpress.XtraReports.UI.XRTable();
            this.tableRow2 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableRow3 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell4 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell5 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell6 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell7 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell8 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell9 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell10 = new DevExpress.XtraReports.UI.XRTableCell();
            this.ReportFooter1 = new DevExpress.XtraReports.UI.ReportFooterBand();
            this.xrTable7 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow7 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_BillQtySum = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell4 = new DevExpress.XtraReports.UI.XRTableCell();
            this.DetailReport_PrInv = new DevExpress.XtraReports.UI.DetailReportBand();
            this.Detail3 = new DevExpress.XtraReports.UI.DetailBand();
            this.table4 = new DevExpress.XtraReports.UI.XRTable();
            this.tableRow7 = new DevExpress.XtraReports.UI.XRTableRow();
            this.col_SlInv_Qty = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_SlInv_UOM = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_SlInv_ItemName = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_SlInv_Code1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_SlInv_Branch = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_SlInv_InvCode = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_SlInv_Date = new DevExpress.XtraReports.UI.XRTableCell();
            this.ReportHeader2 = new DevExpress.XtraReports.UI.ReportHeaderBand();
            this.table3 = new DevExpress.XtraReports.UI.XRTable();
            this.tableRow5 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell18 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableRow6 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell19 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell20 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell21 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell22 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell23 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell24 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell25 = new DevExpress.XtraReports.UI.XRTableCell();
            this.ReportFooter2 = new DevExpress.XtraReports.UI.ReportFooterBand();
            this.xrTable8 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow8 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_InvQtySum = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell10 = new DevExpress.XtraReports.UI.XRTableCell();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.table2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.table1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.table4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.table3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // Detail
            // 
            resources.ApplyResources(this.Detail, "Detail");
            this.Detail.Name = "Detail";
            this.Detail.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            // 
            // TopMargin
            // 
            this.TopMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLabel33,
            this.txt_VendorEmail,
            this.xrLabel21,
            this.txt_CostCenter,
            this.txt_CostCenterCode,
            this.xrLabel19,
            this.xrLabel16,
            this.xrLabel5,
            this.lbl_DeliverDate,
            this.lbl_AttnMr,
            this.xrLabel1,
            this.xrLine1,
            this.xrLabel4,
            this.lblCompName,
            this.lbl_AttnMr_Job,
            this.xrLabel2,
            this.xrLabel14,
            this.lbl_store,
            this.lblReportName,
            this.lbl_Number,
            this.lbl_notes,
            this.picLogo,
            this.xrLabel6,
            this.lbl_date,
            this.lbl_User,
            this.xrLabel7,
            this.xrLabel8,
            this.lbl_ShipTo,
            this.xrLabel12,
            this.lbl_VenFax,
            this.lbl_VenTel,
            this.xrLabel3,
            this.lbl_Serial,
            this.lbl_Vendor});
            resources.ApplyResources(this.TopMargin, "TopMargin");
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            // 
            // xrLabel33
            // 
            resources.ApplyResources(this.xrLabel33, "xrLabel33");
            this.xrLabel33.Name = "xrLabel33";
            this.xrLabel33.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel33.StylePriority.UseFont = false;
            this.xrLabel33.StylePriority.UseTextAlignment = false;
            // 
            // txt_VendorEmail
            // 
            resources.ApplyResources(this.txt_VendorEmail, "txt_VendorEmail");
            this.txt_VendorEmail.Name = "txt_VendorEmail";
            this.txt_VendorEmail.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.txt_VendorEmail.StylePriority.UseFont = false;
            this.txt_VendorEmail.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel21
            // 
            resources.ApplyResources(this.xrLabel21, "xrLabel21");
            this.xrLabel21.Name = "xrLabel21";
            this.xrLabel21.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel21.StylePriority.UseFont = false;
            this.xrLabel21.StylePriority.UseTextAlignment = false;
            // 
            // txt_CostCenter
            // 
            resources.ApplyResources(this.txt_CostCenter, "txt_CostCenter");
            this.txt_CostCenter.Name = "txt_CostCenter";
            this.txt_CostCenter.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.txt_CostCenter.StylePriority.UseFont = false;
            this.txt_CostCenter.StylePriority.UseTextAlignment = false;
            // 
            // txt_CostCenterCode
            // 
            resources.ApplyResources(this.txt_CostCenterCode, "txt_CostCenterCode");
            this.txt_CostCenterCode.Name = "txt_CostCenterCode";
            this.txt_CostCenterCode.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.txt_CostCenterCode.StylePriority.UseFont = false;
            this.txt_CostCenterCode.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel19
            // 
            resources.ApplyResources(this.xrLabel19, "xrLabel19");
            this.xrLabel19.Name = "xrLabel19";
            this.xrLabel19.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel19.StylePriority.UseFont = false;
            this.xrLabel19.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel16
            // 
            resources.ApplyResources(this.xrLabel16, "xrLabel16");
            this.xrLabel16.Name = "xrLabel16";
            this.xrLabel16.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel16.StylePriority.UseFont = false;
            this.xrLabel16.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel5
            // 
            resources.ApplyResources(this.xrLabel5, "xrLabel5");
            this.xrLabel5.Name = "xrLabel5";
            this.xrLabel5.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel5.StylePriority.UseFont = false;
            this.xrLabel5.StylePriority.UseTextAlignment = false;
            // 
            // lbl_DeliverDate
            // 
            resources.ApplyResources(this.lbl_DeliverDate, "lbl_DeliverDate");
            this.lbl_DeliverDate.Name = "lbl_DeliverDate";
            this.lbl_DeliverDate.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_DeliverDate.StylePriority.UseFont = false;
            this.lbl_DeliverDate.StylePriority.UseTextAlignment = false;
            // 
            // lbl_AttnMr
            // 
            resources.ApplyResources(this.lbl_AttnMr, "lbl_AttnMr");
            this.lbl_AttnMr.Name = "lbl_AttnMr";
            this.lbl_AttnMr.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_AttnMr.StylePriority.UseFont = false;
            this.lbl_AttnMr.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel1
            // 
            resources.ApplyResources(this.xrLabel1, "xrLabel1");
            this.xrLabel1.Name = "xrLabel1";
            this.xrLabel1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel1.StylePriority.UseFont = false;
            this.xrLabel1.StylePriority.UseTextAlignment = false;
            // 
            // xrLine1
            // 
            resources.ApplyResources(this.xrLine1, "xrLine1");
            this.xrLine1.BorderWidth = 0F;
            this.xrLine1.LineWidth = 0;
            this.xrLine1.Name = "xrLine1";
            this.xrLine1.StylePriority.UseBackColor = false;
            this.xrLine1.StylePriority.UseBorderColor = false;
            this.xrLine1.StylePriority.UseBorderWidth = false;
            this.xrLine1.StylePriority.UseForeColor = false;
            // 
            // xrLabel4
            // 
            resources.ApplyResources(this.xrLabel4, "xrLabel4");
            this.xrLabel4.Name = "xrLabel4";
            this.xrLabel4.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel4.StylePriority.UseFont = false;
            this.xrLabel4.StylePriority.UseTextAlignment = false;
            // 
            // lblCompName
            // 
            resources.ApplyResources(this.lblCompName, "lblCompName");
            this.lblCompName.Name = "lblCompName";
            this.lblCompName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblCompName.StylePriority.UseFont = false;
            this.lblCompName.StylePriority.UseTextAlignment = false;
            // 
            // lbl_AttnMr_Job
            // 
            resources.ApplyResources(this.lbl_AttnMr_Job, "lbl_AttnMr_Job");
            this.lbl_AttnMr_Job.Name = "lbl_AttnMr_Job";
            this.lbl_AttnMr_Job.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_AttnMr_Job.StylePriority.UseFont = false;
            this.lbl_AttnMr_Job.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel2
            // 
            resources.ApplyResources(this.xrLabel2, "xrLabel2");
            this.xrLabel2.Name = "xrLabel2";
            this.xrLabel2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel2.StylePriority.UseFont = false;
            this.xrLabel2.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel14
            // 
            resources.ApplyResources(this.xrLabel14, "xrLabel14");
            this.xrLabel14.Name = "xrLabel14";
            this.xrLabel14.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel14.StylePriority.UseFont = false;
            this.xrLabel14.StylePriority.UseTextAlignment = false;
            // 
            // lbl_store
            // 
            resources.ApplyResources(this.lbl_store, "lbl_store");
            this.lbl_store.Name = "lbl_store";
            this.lbl_store.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_store.StylePriority.UseFont = false;
            this.lbl_store.StylePriority.UseTextAlignment = false;
            // 
            // lblReportName
            // 
            resources.ApplyResources(this.lblReportName, "lblReportName");
            this.lblReportName.Name = "lblReportName";
            this.lblReportName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblReportName.StylePriority.UseFont = false;
            this.lblReportName.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Number
            // 
            resources.ApplyResources(this.lbl_Number, "lbl_Number");
            this.lbl_Number.Name = "lbl_Number";
            this.lbl_Number.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Number.StylePriority.UseFont = false;
            this.lbl_Number.StylePriority.UseTextAlignment = false;
            // 
            // lbl_notes
            // 
            this.lbl_notes.CanGrow = false;
            resources.ApplyResources(this.lbl_notes, "lbl_notes");
            this.lbl_notes.Multiline = true;
            this.lbl_notes.Name = "lbl_notes";
            this.lbl_notes.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_notes.StylePriority.UseFont = false;
            this.lbl_notes.StylePriority.UseTextAlignment = false;
            // 
            // picLogo
            // 
            resources.ApplyResources(this.picLogo, "picLogo");
            this.picLogo.Name = "picLogo";
            this.picLogo.Sizing = DevExpress.XtraPrinting.ImageSizeMode.StretchImage;
            // 
            // xrLabel6
            // 
            this.xrLabel6.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.xrLabel6, "xrLabel6");
            this.xrLabel6.Name = "xrLabel6";
            this.xrLabel6.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel6.StylePriority.UseBorders = false;
            this.xrLabel6.StylePriority.UseFont = false;
            this.xrLabel6.StylePriority.UseTextAlignment = false;
            // 
            // lbl_date
            // 
            resources.ApplyResources(this.lbl_date, "lbl_date");
            this.lbl_date.Name = "lbl_date";
            this.lbl_date.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_date.StylePriority.UseFont = false;
            this.lbl_date.StylePriority.UseTextAlignment = false;
            // 
            // lbl_User
            // 
            this.lbl_User.Borders = DevExpress.XtraPrinting.BorderSide.None;
            resources.ApplyResources(this.lbl_User, "lbl_User");
            this.lbl_User.Name = "lbl_User";
            this.lbl_User.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_User.StylePriority.UseBorders = false;
            this.lbl_User.StylePriority.UseFont = false;
            this.lbl_User.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel7
            // 
            resources.ApplyResources(this.xrLabel7, "xrLabel7");
            this.xrLabel7.Name = "xrLabel7";
            this.xrLabel7.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel7.StylePriority.UseFont = false;
            this.xrLabel7.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel8
            // 
            resources.ApplyResources(this.xrLabel8, "xrLabel8");
            this.xrLabel8.Name = "xrLabel8";
            this.xrLabel8.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel8.StylePriority.UseFont = false;
            this.xrLabel8.StylePriority.UseTextAlignment = false;
            // 
            // lbl_ShipTo
            // 
            resources.ApplyResources(this.lbl_ShipTo, "lbl_ShipTo");
            this.lbl_ShipTo.Multiline = true;
            this.lbl_ShipTo.Name = "lbl_ShipTo";
            this.lbl_ShipTo.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_ShipTo.StylePriority.UseFont = false;
            this.lbl_ShipTo.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel12
            // 
            resources.ApplyResources(this.xrLabel12, "xrLabel12");
            this.xrLabel12.Name = "xrLabel12";
            this.xrLabel12.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel12.StylePriority.UseFont = false;
            this.xrLabel12.StylePriority.UseTextAlignment = false;
            // 
            // lbl_VenFax
            // 
            resources.ApplyResources(this.lbl_VenFax, "lbl_VenFax");
            this.lbl_VenFax.Name = "lbl_VenFax";
            this.lbl_VenFax.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_VenFax.StylePriority.UseFont = false;
            this.lbl_VenFax.StylePriority.UseTextAlignment = false;
            // 
            // lbl_VenTel
            // 
            resources.ApplyResources(this.lbl_VenTel, "lbl_VenTel");
            this.lbl_VenTel.Name = "lbl_VenTel";
            this.lbl_VenTel.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_VenTel.StylePriority.UseFont = false;
            this.lbl_VenTel.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel3
            // 
            resources.ApplyResources(this.xrLabel3, "xrLabel3");
            this.xrLabel3.Name = "xrLabel3";
            this.xrLabel3.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel3.StylePriority.UseFont = false;
            this.xrLabel3.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Serial
            // 
            resources.ApplyResources(this.lbl_Serial, "lbl_Serial");
            this.lbl_Serial.Name = "lbl_Serial";
            this.lbl_Serial.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Serial.StylePriority.UseFont = false;
            this.lbl_Serial.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Vendor
            // 
            resources.ApplyResources(this.lbl_Vendor, "lbl_Vendor");
            this.lbl_Vendor.Name = "lbl_Vendor";
            this.lbl_Vendor.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Vendor.StylePriority.UseFont = false;
            this.lbl_Vendor.StylePriority.UseTextAlignment = false;
            // 
            // BottomMargin
            // 
            resources.ApplyResources(this.BottomMargin, "BottomMargin");
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            // 
            // PageHeader
            // 
            resources.ApplyResources(this.PageHeader, "PageHeader");
            this.PageHeader.Name = "PageHeader";
            // 
            // ReportFooter
            // 
            this.ReportFooter.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLabel18,
            this.lblTotalWords,
            this.xrLabel15,
            this.lbl_tax,
            this.xrTable5,
            this.xrTable6,
            this.lbl_DiscountV,
            this.lbl_Total,
            this.xrPageInfo2,
            this.xrLine2,
            this.lbl_Net,
            this.xrLabel9,
            this.xrLabel10,
            this.lbl_DiscountR,
            this.xrTable4,
            this.xrTable3});
            resources.ApplyResources(this.ReportFooter, "ReportFooter");
            this.ReportFooter.Name = "ReportFooter";
            this.ReportFooter.PrintAtBottom = true;
            // 
            // xrLabel18
            // 
            resources.ApplyResources(this.xrLabel18, "xrLabel18");
            this.xrLabel18.Name = "xrLabel18";
            this.xrLabel18.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel18.StylePriority.UseBackColor = false;
            this.xrLabel18.StylePriority.UseFont = false;
            this.xrLabel18.StylePriority.UseTextAlignment = false;
            // 
            // lblTotalWords
            // 
            resources.ApplyResources(this.lblTotalWords, "lblTotalWords");
            this.lblTotalWords.Name = "lblTotalWords";
            this.lblTotalWords.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblTotalWords.StylePriority.UseBackColor = false;
            this.lblTotalWords.StylePriority.UseFont = false;
            this.lblTotalWords.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel15
            // 
            this.xrLabel15.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel15.CanGrow = false;
            resources.ApplyResources(this.xrLabel15, "xrLabel15");
            this.xrLabel15.Name = "xrLabel15";
            this.xrLabel15.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel15.StylePriority.UseBorders = false;
            this.xrLabel15.StylePriority.UseFont = false;
            this.xrLabel15.StylePriority.UsePadding = false;
            this.xrLabel15.StylePriority.UseTextAlignment = false;
            // 
            // lbl_tax
            // 
            this.lbl_tax.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_tax.CanGrow = false;
            resources.ApplyResources(this.lbl_tax, "lbl_tax");
            this.lbl_tax.Name = "lbl_tax";
            this.lbl_tax.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_tax.StylePriority.UseBorders = false;
            this.lbl_tax.StylePriority.UseFont = false;
            this.lbl_tax.StylePriority.UsePadding = false;
            this.lbl_tax.StylePriority.UseTextAlignment = false;
            // 
            // xrTable5
            // 
            resources.ApplyResources(this.xrTable5, "xrTable5");
            this.xrTable5.Name = "xrTable5";
            this.xrTable5.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow5});
            this.xrTable5.StylePriority.UseTextAlignment = false;
            // 
            // xrTableRow5
            // 
            this.xrTableRow5.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.col_OutTrns_ItemCode2,
            this.col_OuTrans_Expire,
            this.col_OutTrns_Batch,
            this.col_OutTrns_Length,
            this.col_OutTrns_Width,
            this.col_OutTrns_Height,
            this.col_OutTrns_TotlQty,
            this.col_OutTrns_PcCount,
            this.col_OutTrns_Qc});
            resources.ApplyResources(this.xrTableRow5, "xrTableRow5");
            this.xrTableRow5.Name = "xrTableRow5";
            // 
            // col_OutTrns_ItemCode2
            // 
            resources.ApplyResources(this.col_OutTrns_ItemCode2, "col_OutTrns_ItemCode2");
            this.col_OutTrns_ItemCode2.Name = "col_OutTrns_ItemCode2";
            // 
            // col_OuTrans_Expire
            // 
            resources.ApplyResources(this.col_OuTrans_Expire, "col_OuTrans_Expire");
            this.col_OuTrans_Expire.Name = "col_OuTrans_Expire";
            // 
            // col_OutTrns_Batch
            // 
            resources.ApplyResources(this.col_OutTrns_Batch, "col_OutTrns_Batch");
            this.col_OutTrns_Batch.Name = "col_OutTrns_Batch";
            // 
            // col_OutTrns_Length
            // 
            resources.ApplyResources(this.col_OutTrns_Length, "col_OutTrns_Length");
            this.col_OutTrns_Length.Name = "col_OutTrns_Length";
            // 
            // col_OutTrns_Width
            // 
            resources.ApplyResources(this.col_OutTrns_Width, "col_OutTrns_Width");
            this.col_OutTrns_Width.Name = "col_OutTrns_Width";
            // 
            // col_OutTrns_Height
            // 
            resources.ApplyResources(this.col_OutTrns_Height, "col_OutTrns_Height");
            this.col_OutTrns_Height.Name = "col_OutTrns_Height";
            // 
            // col_OutTrns_TotlQty
            // 
            resources.ApplyResources(this.col_OutTrns_TotlQty, "col_OutTrns_TotlQty");
            this.col_OutTrns_TotlQty.Name = "col_OutTrns_TotlQty";
            // 
            // col_OutTrns_PcCount
            // 
            resources.ApplyResources(this.col_OutTrns_PcCount, "col_OutTrns_PcCount");
            this.col_OutTrns_PcCount.Name = "col_OutTrns_PcCount";
            // 
            // col_OutTrns_Qc
            // 
            resources.ApplyResources(this.col_OutTrns_Qc, "col_OutTrns_Qc");
            this.col_OutTrns_Qc.Name = "col_OutTrns_Qc";
            // 
            // xrTable6
            // 
            resources.ApplyResources(this.xrTable6, "xrTable6");
            this.xrTable6.Name = "xrTable6";
            this.xrTable6.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow6});
            this.xrTable6.StylePriority.UseTextAlignment = false;
            // 
            // xrTableRow6
            // 
            this.xrTableRow6.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.col_SlInv_Code2,
            this.col_SlInv_Expire,
            this.col_SlInv_Batch,
            this.col_SlInv_Length,
            this.col_SlInv_Width,
            this.col_SlInv_Height,
            this.col_SlInv_TotlQty,
            this.col_SlInv_PcCount,
            this.col_SlInv_Qc});
            resources.ApplyResources(this.xrTableRow6, "xrTableRow6");
            this.xrTableRow6.Name = "xrTableRow6";
            // 
            // col_SlInv_Code2
            // 
            resources.ApplyResources(this.col_SlInv_Code2, "col_SlInv_Code2");
            this.col_SlInv_Code2.Name = "col_SlInv_Code2";
            // 
            // col_SlInv_Expire
            // 
            resources.ApplyResources(this.col_SlInv_Expire, "col_SlInv_Expire");
            this.col_SlInv_Expire.Name = "col_SlInv_Expire";
            // 
            // col_SlInv_Batch
            // 
            resources.ApplyResources(this.col_SlInv_Batch, "col_SlInv_Batch");
            this.col_SlInv_Batch.Name = "col_SlInv_Batch";
            // 
            // col_SlInv_Length
            // 
            resources.ApplyResources(this.col_SlInv_Length, "col_SlInv_Length");
            this.col_SlInv_Length.Name = "col_SlInv_Length";
            // 
            // col_SlInv_Width
            // 
            resources.ApplyResources(this.col_SlInv_Width, "col_SlInv_Width");
            this.col_SlInv_Width.Name = "col_SlInv_Width";
            // 
            // col_SlInv_Height
            // 
            resources.ApplyResources(this.col_SlInv_Height, "col_SlInv_Height");
            this.col_SlInv_Height.Name = "col_SlInv_Height";
            // 
            // col_SlInv_TotlQty
            // 
            resources.ApplyResources(this.col_SlInv_TotlQty, "col_SlInv_TotlQty");
            this.col_SlInv_TotlQty.Name = "col_SlInv_TotlQty";
            // 
            // col_SlInv_PcCount
            // 
            resources.ApplyResources(this.col_SlInv_PcCount, "col_SlInv_PcCount");
            this.col_SlInv_PcCount.Name = "col_SlInv_PcCount";
            // 
            // col_SlInv_Qc
            // 
            resources.ApplyResources(this.col_SlInv_Qc, "col_SlInv_Qc");
            this.col_SlInv_Qc.Name = "col_SlInv_Qc";
            // 
            // lbl_DiscountV
            // 
            this.lbl_DiscountV.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_DiscountV.CanGrow = false;
            resources.ApplyResources(this.lbl_DiscountV, "lbl_DiscountV");
            this.lbl_DiscountV.Name = "lbl_DiscountV";
            this.lbl_DiscountV.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_DiscountV.StylePriority.UseBorders = false;
            this.lbl_DiscountV.StylePriority.UseFont = false;
            this.lbl_DiscountV.StylePriority.UsePadding = false;
            this.lbl_DiscountV.StylePriority.UseTextAlignment = false;
            // 
            // lbl_Total
            // 
            this.lbl_Total.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_Total.CanGrow = false;
            resources.ApplyResources(this.lbl_Total, "lbl_Total");
            this.lbl_Total.Name = "lbl_Total";
            this.lbl_Total.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_Total.StylePriority.UseBorders = false;
            this.lbl_Total.StylePriority.UseFont = false;
            this.lbl_Total.StylePriority.UsePadding = false;
            this.lbl_Total.StylePriority.UseTextAlignment = false;
            // 
            // xrPageInfo2
            // 
            resources.ApplyResources(this.xrPageInfo2, "xrPageInfo2");
            this.xrPageInfo2.Name = "xrPageInfo2";
            this.xrPageInfo2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrPageInfo2.StylePriority.UseTextAlignment = false;
            // 
            // xrLine2
            // 
            resources.ApplyResources(this.xrLine2, "xrLine2");
            this.xrLine2.Name = "xrLine2";
            // 
            // lbl_Net
            // 
            this.lbl_Net.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_Net.CanGrow = false;
            resources.ApplyResources(this.lbl_Net, "lbl_Net");
            this.lbl_Net.Name = "lbl_Net";
            this.lbl_Net.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_Net.StylePriority.UseBorders = false;
            this.lbl_Net.StylePriority.UseFont = false;
            this.lbl_Net.StylePriority.UsePadding = false;
            this.lbl_Net.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel9
            // 
            this.xrLabel9.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel9.CanGrow = false;
            resources.ApplyResources(this.xrLabel9, "xrLabel9");
            this.xrLabel9.Name = "xrLabel9";
            this.xrLabel9.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel9.StylePriority.UseBorders = false;
            this.xrLabel9.StylePriority.UseFont = false;
            this.xrLabel9.StylePriority.UsePadding = false;
            this.xrLabel9.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel10
            // 
            this.xrLabel10.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel10.CanGrow = false;
            resources.ApplyResources(this.xrLabel10, "xrLabel10");
            this.xrLabel10.Name = "xrLabel10";
            this.xrLabel10.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel10.StylePriority.UseBorders = false;
            this.xrLabel10.StylePriority.UseFont = false;
            this.xrLabel10.StylePriority.UseTextAlignment = false;
            // 
            // lbl_DiscountR
            // 
            this.lbl_DiscountR.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_DiscountR.CanGrow = false;
            resources.ApplyResources(this.lbl_DiscountR, "lbl_DiscountR");
            this.lbl_DiscountR.Name = "lbl_DiscountR";
            this.lbl_DiscountR.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_DiscountR.StylePriority.UseBorders = false;
            this.lbl_DiscountR.StylePriority.UseFont = false;
            this.lbl_DiscountR.StylePriority.UseTextAlignment = false;
            // 
            // xrTable4
            // 
            resources.ApplyResources(this.xrTable4, "xrTable4");
            this.xrTable4.Name = "xrTable4";
            this.xrTable4.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow4});
            // 
            // xrTableRow4
            // 
            this.xrTableRow4.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.Cell_MUOM,
            this.Cell_MUOM_Factor,
            this.cell_Factor});
            resources.ApplyResources(this.xrTableRow4, "xrTableRow4");
            this.xrTableRow4.Name = "xrTableRow4";
            // 
            // Cell_MUOM
            // 
            resources.ApplyResources(this.Cell_MUOM, "Cell_MUOM");
            this.Cell_MUOM.Name = "Cell_MUOM";
            // 
            // Cell_MUOM_Factor
            // 
            resources.ApplyResources(this.Cell_MUOM_Factor, "Cell_MUOM_Factor");
            this.Cell_MUOM_Factor.Name = "Cell_MUOM_Factor";
            // 
            // cell_Factor
            // 
            resources.ApplyResources(this.cell_Factor, "cell_Factor");
            this.cell_Factor.Name = "cell_Factor";
            // 
            // xrTable3
            // 
            resources.ApplyResources(this.xrTable3, "xrTable3");
            this.xrTable3.Name = "xrTable3";
            this.xrTable3.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow3});
            this.xrTable3.StylePriority.UseTextAlignment = false;
            // 
            // xrTableRow3
            // 
            this.xrTableRow3.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_Height,
            this.cell_Width,
            this.cell_Length,
            this.cell_TotalQty,
            this.cell_code2});
            resources.ApplyResources(this.xrTableRow3, "xrTableRow3");
            this.xrTableRow3.Name = "xrTableRow3";
            // 
            // cell_Height
            // 
            resources.ApplyResources(this.cell_Height, "cell_Height");
            this.cell_Height.Name = "cell_Height";
            // 
            // cell_Width
            // 
            resources.ApplyResources(this.cell_Width, "cell_Width");
            this.cell_Width.Name = "cell_Width";
            // 
            // cell_Length
            // 
            resources.ApplyResources(this.cell_Length, "cell_Length");
            this.cell_Length.Name = "cell_Length";
            // 
            // cell_TotalQty
            // 
            resources.ApplyResources(this.cell_TotalQty, "cell_TotalQty");
            this.cell_TotalQty.Name = "cell_TotalQty";
            // 
            // cell_code2
            // 
            resources.ApplyResources(this.cell_code2, "cell_code2");
            this.cell_code2.Name = "cell_code2";
            // 
            // DetailReport_Po_Order
            // 
            this.DetailReport_Po_Order.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail1,
            this.ReportHeader,
            this.GroupFooter1});
            resources.ApplyResources(this.DetailReport_Po_Order, "DetailReport_Po_Order");
            this.DetailReport_Po_Order.Level = 0;
            this.DetailReport_Po_Order.Name = "DetailReport_Po_Order";
            // 
            // Detail1
            // 
            this.Detail1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable2});
            resources.ApplyResources(this.Detail1, "Detail1");
            this.Detail1.Name = "Detail1";
            // 
            // xrTable2
            // 
            this.xrTable2.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.xrTable2, "xrTable2");
            this.xrTable2.Name = "xrTable2";
            this.xrTable2.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow2});
            this.xrTable2.StylePriority.UseBorders = false;
            this.xrTable2.StylePriority.UseFont = false;
            this.xrTable2.StylePriority.UseTextAlignment = false;
            // 
            // xrTableRow2
            // 
            resources.ApplyResources(this.xrTableRow2, "xrTableRow2");
            this.xrTableRow2.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_Total,
            this.cell_Disc,
            this.cell_Price,
            this.cell_Qty,
            this.cell_UOM,
            this.cell_ItemName,
            this.cell_code});
            this.xrTableRow2.Name = "xrTableRow2";
            this.xrTableRow2.StylePriority.UseBackColor = false;
            this.xrTableRow2.StylePriority.UseFont = false;
            // 
            // cell_Total
            // 
            this.cell_Total.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.cell_Total.CanGrow = false;
            resources.ApplyResources(this.cell_Total, "cell_Total");
            this.cell_Total.Multiline = true;
            this.cell_Total.Name = "cell_Total";
            this.cell_Total.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_Total.StylePriority.UseBorders = false;
            this.cell_Total.StylePriority.UseFont = false;
            this.cell_Total.StylePriority.UsePadding = false;
            this.cell_Total.StylePriority.UseTextAlignment = false;
            // 
            // cell_Disc
            // 
            this.cell_Disc.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.cell_Disc.CanGrow = false;
            resources.ApplyResources(this.cell_Disc, "cell_Disc");
            this.cell_Disc.Multiline = true;
            this.cell_Disc.Name = "cell_Disc";
            this.cell_Disc.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_Disc.StylePriority.UseBorders = false;
            this.cell_Disc.StylePriority.UseFont = false;
            this.cell_Disc.StylePriority.UsePadding = false;
            this.cell_Disc.StylePriority.UseTextAlignment = false;
            // 
            // cell_Price
            // 
            this.cell_Price.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.cell_Price.CanGrow = false;
            resources.ApplyResources(this.cell_Price, "cell_Price");
            this.cell_Price.Multiline = true;
            this.cell_Price.Name = "cell_Price";
            this.cell_Price.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_Price.StylePriority.UseBorders = false;
            this.cell_Price.StylePriority.UseFont = false;
            this.cell_Price.StylePriority.UsePadding = false;
            this.cell_Price.StylePriority.UseTextAlignment = false;
            // 
            // cell_Qty
            // 
            this.cell_Qty.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.cell_Qty.CanGrow = false;
            resources.ApplyResources(this.cell_Qty, "cell_Qty");
            this.cell_Qty.Multiline = true;
            this.cell_Qty.Name = "cell_Qty";
            this.cell_Qty.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_Qty.StylePriority.UseBorders = false;
            this.cell_Qty.StylePriority.UseFont = false;
            this.cell_Qty.StylePriority.UsePadding = false;
            this.cell_Qty.StylePriority.UseTextAlignment = false;
            // 
            // cell_UOM
            // 
            this.cell_UOM.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.cell_UOM.CanGrow = false;
            resources.ApplyResources(this.cell_UOM, "cell_UOM");
            this.cell_UOM.Multiline = true;
            this.cell_UOM.Name = "cell_UOM";
            this.cell_UOM.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_UOM.StylePriority.UseBorders = false;
            this.cell_UOM.StylePriority.UseFont = false;
            this.cell_UOM.StylePriority.UsePadding = false;
            this.cell_UOM.StylePriority.UseTextAlignment = false;
            // 
            // cell_ItemName
            // 
            this.cell_ItemName.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.cell_ItemName.CanGrow = false;
            resources.ApplyResources(this.cell_ItemName, "cell_ItemName");
            this.cell_ItemName.Multiline = true;
            this.cell_ItemName.Name = "cell_ItemName";
            this.cell_ItemName.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_ItemName.StylePriority.UseBorders = false;
            this.cell_ItemName.StylePriority.UseFont = false;
            this.cell_ItemName.StylePriority.UsePadding = false;
            this.cell_ItemName.StylePriority.UseTextAlignment = false;
            // 
            // cell_code
            // 
            this.cell_code.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.cell_code.CanGrow = false;
            resources.ApplyResources(this.cell_code, "cell_code");
            this.cell_code.Multiline = true;
            this.cell_code.Name = "cell_code";
            this.cell_code.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_code.StylePriority.UseBorders = false;
            this.cell_code.StylePriority.UseFont = false;
            this.cell_code.StylePriority.UsePadding = false;
            this.cell_code.StylePriority.UseTextAlignment = false;
            // 
            // ReportHeader
            // 
            this.ReportHeader.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable1});
            resources.ApplyResources(this.ReportHeader, "ReportHeader");
            this.ReportHeader.Name = "ReportHeader";
            // 
            // xrTable1
            // 
            this.xrTable1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.xrTable1, "xrTable1");
            this.xrTable1.Name = "xrTable1";
            this.xrTable1.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.tableRow1,
            this.xrTableRow1});
            this.xrTable1.StylePriority.UseBorders = false;
            this.xrTable1.StylePriority.UseFont = false;
            this.xrTable1.StylePriority.UseTextAlignment = false;
            // 
            // tableRow1
            // 
            this.tableRow1.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.tableCell1});
            resources.ApplyResources(this.tableRow1, "tableRow1");
            this.tableRow1.Name = "tableRow1";
            // 
            // tableCell1
            // 
            resources.ApplyResources(this.tableCell1, "tableCell1");
            this.tableCell1.Name = "tableCell1";
            this.tableCell1.StylePriority.UseBackColor = false;
            this.tableCell1.StylePriority.UseFont = false;
            // 
            // xrTableRow1
            // 
            resources.ApplyResources(this.xrTableRow1, "xrTableRow1");
            this.xrTableRow1.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell1,
            this.xrTableCell9,
            this.xrTableCell7,
            this.xrTableCell6,
            this.xrTableCell5,
            this.xrTableCell3,
            this.xrTableCell8});
            this.xrTableRow1.Name = "xrTableRow1";
            this.xrTableRow1.StylePriority.UseBackColor = false;
            this.xrTableRow1.StylePriority.UseFont = false;
            // 
            // xrTableCell1
            // 
            resources.ApplyResources(this.xrTableCell1, "xrTableCell1");
            this.xrTableCell1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTableCell1.Name = "xrTableCell1";
            this.xrTableCell1.StylePriority.UseBackColor = false;
            this.xrTableCell1.StylePriority.UseBorders = false;
            // 
            // xrTableCell9
            // 
            resources.ApplyResources(this.xrTableCell9, "xrTableCell9");
            this.xrTableCell9.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTableCell9.Name = "xrTableCell9";
            this.xrTableCell9.StylePriority.UseBackColor = false;
            this.xrTableCell9.StylePriority.UseBorders = false;
            // 
            // xrTableCell7
            // 
            resources.ApplyResources(this.xrTableCell7, "xrTableCell7");
            this.xrTableCell7.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTableCell7.Name = "xrTableCell7";
            this.xrTableCell7.StylePriority.UseBackColor = false;
            this.xrTableCell7.StylePriority.UseBorders = false;
            // 
            // xrTableCell6
            // 
            resources.ApplyResources(this.xrTableCell6, "xrTableCell6");
            this.xrTableCell6.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTableCell6.Name = "xrTableCell6";
            this.xrTableCell6.StylePriority.UseBackColor = false;
            this.xrTableCell6.StylePriority.UseBorders = false;
            // 
            // xrTableCell5
            // 
            resources.ApplyResources(this.xrTableCell5, "xrTableCell5");
            this.xrTableCell5.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTableCell5.Name = "xrTableCell5";
            this.xrTableCell5.StylePriority.UseBackColor = false;
            this.xrTableCell5.StylePriority.UseBorders = false;
            // 
            // xrTableCell3
            // 
            resources.ApplyResources(this.xrTableCell3, "xrTableCell3");
            this.xrTableCell3.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTableCell3.Name = "xrTableCell3";
            this.xrTableCell3.StylePriority.UseBackColor = false;
            this.xrTableCell3.StylePriority.UseBorders = false;
            // 
            // xrTableCell8
            // 
            resources.ApplyResources(this.xrTableCell8, "xrTableCell8");
            this.xrTableCell8.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTableCell8.Name = "xrTableCell8";
            this.xrTableCell8.StylePriority.UseBackColor = false;
            this.xrTableCell8.StylePriority.UseBorders = false;
            // 
            // GroupFooter1
            // 
            this.GroupFooter1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrLabel31,
            this.xrLabel32,
            this.xrLabel23,
            this.xrLabel24,
            this.xrLabel25,
            this.xrLabel26,
            this.xrLabel27,
            this.xrLabel28,
            this.xrLabel29,
            this.xrLabel30,
            this.txtPayments,
            this.xrLabel20,
            this.xrLabel13,
            this.txtReferenceNo,
            this.xrLabel17,
            this.txt_QutationDate});
            resources.ApplyResources(this.GroupFooter1, "GroupFooter1");
            this.GroupFooter1.Name = "GroupFooter1";
            // 
            // xrLabel31
            // 
            resources.ApplyResources(this.xrLabel31, "xrLabel31");
            this.xrLabel31.Name = "xrLabel31";
            this.xrLabel31.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel31.StylePriority.UseFont = false;
            this.xrLabel31.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel32
            // 
            resources.ApplyResources(this.xrLabel32, "xrLabel32");
            this.xrLabel32.Name = "xrLabel32";
            this.xrLabel32.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel32.StylePriority.UseFont = false;
            this.xrLabel32.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel23
            // 
            resources.ApplyResources(this.xrLabel23, "xrLabel23");
            this.xrLabel23.Name = "xrLabel23";
            this.xrLabel23.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel23.StylePriority.UseFont = false;
            this.xrLabel23.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel24
            // 
            resources.ApplyResources(this.xrLabel24, "xrLabel24");
            this.xrLabel24.Name = "xrLabel24";
            this.xrLabel24.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel24.StylePriority.UseFont = false;
            this.xrLabel24.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel25
            // 
            resources.ApplyResources(this.xrLabel25, "xrLabel25");
            this.xrLabel25.Name = "xrLabel25";
            this.xrLabel25.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel25.StylePriority.UseFont = false;
            this.xrLabel25.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel26
            // 
            resources.ApplyResources(this.xrLabel26, "xrLabel26");
            this.xrLabel26.Name = "xrLabel26";
            this.xrLabel26.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel26.StylePriority.UseFont = false;
            this.xrLabel26.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel27
            // 
            resources.ApplyResources(this.xrLabel27, "xrLabel27");
            this.xrLabel27.Name = "xrLabel27";
            this.xrLabel27.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel27.StylePriority.UseFont = false;
            this.xrLabel27.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel28
            // 
            resources.ApplyResources(this.xrLabel28, "xrLabel28");
            this.xrLabel28.Name = "xrLabel28";
            this.xrLabel28.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel28.StylePriority.UseFont = false;
            this.xrLabel28.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel29
            // 
            resources.ApplyResources(this.xrLabel29, "xrLabel29");
            this.xrLabel29.Name = "xrLabel29";
            this.xrLabel29.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel29.StylePriority.UseFont = false;
            this.xrLabel29.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel30
            // 
            resources.ApplyResources(this.xrLabel30, "xrLabel30");
            this.xrLabel30.Name = "xrLabel30";
            this.xrLabel30.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel30.StylePriority.UseFont = false;
            this.xrLabel30.StylePriority.UseTextAlignment = false;
            // 
            // txtPayments
            // 
            resources.ApplyResources(this.txtPayments, "txtPayments");
            this.txtPayments.Multiline = true;
            this.txtPayments.Name = "txtPayments";
            this.txtPayments.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.txtPayments.StylePriority.UseFont = false;
            this.txtPayments.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel20
            // 
            resources.ApplyResources(this.xrLabel20, "xrLabel20");
            this.xrLabel20.Name = "xrLabel20";
            this.xrLabel20.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel20.StylePriority.UseFont = false;
            this.xrLabel20.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel13
            // 
            resources.ApplyResources(this.xrLabel13, "xrLabel13");
            this.xrLabel13.Name = "xrLabel13";
            this.xrLabel13.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel13.StylePriority.UseFont = false;
            this.xrLabel13.StylePriority.UseTextAlignment = false;
            // 
            // txtReferenceNo
            // 
            resources.ApplyResources(this.txtReferenceNo, "txtReferenceNo");
            this.txtReferenceNo.Name = "txtReferenceNo";
            this.txtReferenceNo.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.txtReferenceNo.StylePriority.UseFont = false;
            this.txtReferenceNo.StylePriority.UseTextAlignment = false;
            // 
            // xrLabel17
            // 
            resources.ApplyResources(this.xrLabel17, "xrLabel17");
            this.xrLabel17.Name = "xrLabel17";
            this.xrLabel17.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel17.StylePriority.UseFont = false;
            this.xrLabel17.StylePriority.UseTextAlignment = false;
            // 
            // txt_QutationDate
            // 
            resources.ApplyResources(this.txt_QutationDate, "txt_QutationDate");
            this.txt_QutationDate.Name = "txt_QutationDate";
            this.txt_QutationDate.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.txt_QutationDate.StylePriority.UseFont = false;
            this.txt_QutationDate.StylePriority.UseTextAlignment = false;
            // 
            // DetailReport_InTrns
            // 
            this.DetailReport_InTrns.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail2,
            this.ReportHeader1,
            this.ReportFooter1});
            resources.ApplyResources(this.DetailReport_InTrns, "DetailReport_InTrns");
            this.DetailReport_InTrns.Level = 1;
            this.DetailReport_InTrns.Name = "DetailReport_InTrns";
            // 
            // Detail2
            // 
            this.Detail2.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.table2});
            resources.ApplyResources(this.Detail2, "Detail2");
            this.Detail2.Expanded = false;
            this.Detail2.Name = "Detail2";
            // 
            // table2
            // 
            this.table2.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.table2, "table2");
            this.table2.Name = "table2";
            this.table2.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.tableRow4});
            this.table2.Scripts.OnBeforePrint = "table2_BeforePrint";
            this.table2.StylePriority.UseBorders = false;
            this.table2.StylePriority.UseFont = false;
            this.table2.StylePriority.UseTextAlignment = false;
            // 
            // tableRow4
            // 
            resources.ApplyResources(this.tableRow4, "tableRow4");
            this.tableRow4.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.col_OutTrns_Qty,
            this.col_OutTrns_UOM,
            this.col_OutTrns_ItemName,
            this.col_OutTrns_ItemCode1,
            this.col_OutTrns_Store,
            this.col_OutTrns_Code,
            this.col_OutTrns_Date});
            this.tableRow4.Name = "tableRow4";
            this.tableRow4.StylePriority.UseBackColor = false;
            this.tableRow4.StylePriority.UseFont = false;
            // 
            // col_OutTrns_Qty
            // 
            this.col_OutTrns_Qty.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.col_OutTrns_Qty.CanGrow = false;
            resources.ApplyResources(this.col_OutTrns_Qty, "col_OutTrns_Qty");
            this.col_OutTrns_Qty.Multiline = true;
            this.col_OutTrns_Qty.Name = "col_OutTrns_Qty";
            this.col_OutTrns_Qty.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.col_OutTrns_Qty.StylePriority.UseBorders = false;
            this.col_OutTrns_Qty.StylePriority.UseFont = false;
            this.col_OutTrns_Qty.StylePriority.UsePadding = false;
            this.col_OutTrns_Qty.StylePriority.UseTextAlignment = false;
            // 
            // col_OutTrns_UOM
            // 
            this.col_OutTrns_UOM.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.col_OutTrns_UOM.CanGrow = false;
            resources.ApplyResources(this.col_OutTrns_UOM, "col_OutTrns_UOM");
            this.col_OutTrns_UOM.Multiline = true;
            this.col_OutTrns_UOM.Name = "col_OutTrns_UOM";
            this.col_OutTrns_UOM.StylePriority.UseBorders = false;
            this.col_OutTrns_UOM.StylePriority.UseFont = false;
            this.col_OutTrns_UOM.StylePriority.UsePadding = false;
            this.col_OutTrns_UOM.StylePriority.UseTextAlignment = false;
            // 
            // col_OutTrns_ItemName
            // 
            this.col_OutTrns_ItemName.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.col_OutTrns_ItemName.CanGrow = false;
            resources.ApplyResources(this.col_OutTrns_ItemName, "col_OutTrns_ItemName");
            this.col_OutTrns_ItemName.Multiline = true;
            this.col_OutTrns_ItemName.Name = "col_OutTrns_ItemName";
            this.col_OutTrns_ItemName.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.col_OutTrns_ItemName.StylePriority.UseBorders = false;
            this.col_OutTrns_ItemName.StylePriority.UseFont = false;
            this.col_OutTrns_ItemName.StylePriority.UsePadding = false;
            this.col_OutTrns_ItemName.StylePriority.UseTextAlignment = false;
            // 
            // col_OutTrns_ItemCode1
            // 
            this.col_OutTrns_ItemCode1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.col_OutTrns_ItemCode1.CanGrow = false;
            resources.ApplyResources(this.col_OutTrns_ItemCode1, "col_OutTrns_ItemCode1");
            this.col_OutTrns_ItemCode1.Multiline = true;
            this.col_OutTrns_ItemCode1.Name = "col_OutTrns_ItemCode1";
            this.col_OutTrns_ItemCode1.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.col_OutTrns_ItemCode1.StylePriority.UseBorders = false;
            this.col_OutTrns_ItemCode1.StylePriority.UseFont = false;
            this.col_OutTrns_ItemCode1.StylePriority.UsePadding = false;
            this.col_OutTrns_ItemCode1.StylePriority.UseTextAlignment = false;
            // 
            // col_OutTrns_Store
            // 
            this.col_OutTrns_Store.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.col_OutTrns_Store.CanGrow = false;
            resources.ApplyResources(this.col_OutTrns_Store, "col_OutTrns_Store");
            this.col_OutTrns_Store.Multiline = true;
            this.col_OutTrns_Store.Name = "col_OutTrns_Store";
            this.col_OutTrns_Store.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.col_OutTrns_Store.StylePriority.UseBorders = false;
            this.col_OutTrns_Store.StylePriority.UseFont = false;
            this.col_OutTrns_Store.StylePriority.UsePadding = false;
            this.col_OutTrns_Store.StylePriority.UseTextAlignment = false;
            // 
            // col_OutTrns_Code
            // 
            this.col_OutTrns_Code.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.col_OutTrns_Code.CanGrow = false;
            resources.ApplyResources(this.col_OutTrns_Code, "col_OutTrns_Code");
            this.col_OutTrns_Code.Multiline = true;
            this.col_OutTrns_Code.Name = "col_OutTrns_Code";
            this.col_OutTrns_Code.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.col_OutTrns_Code.StylePriority.UseBorders = false;
            this.col_OutTrns_Code.StylePriority.UseFont = false;
            this.col_OutTrns_Code.StylePriority.UsePadding = false;
            this.col_OutTrns_Code.StylePriority.UseTextAlignment = false;
            // 
            // col_OutTrns_Date
            // 
            this.col_OutTrns_Date.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.col_OutTrns_Date.CanGrow = false;
            resources.ApplyResources(this.col_OutTrns_Date, "col_OutTrns_Date");
            this.col_OutTrns_Date.Multiline = true;
            this.col_OutTrns_Date.Name = "col_OutTrns_Date";
            this.col_OutTrns_Date.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.col_OutTrns_Date.Scripts.OnBeforePrint = "col_OutTrns_Date_BeforePrint";
            this.col_OutTrns_Date.StylePriority.UseBorders = false;
            this.col_OutTrns_Date.StylePriority.UseFont = false;
            this.col_OutTrns_Date.StylePriority.UsePadding = false;
            this.col_OutTrns_Date.StylePriority.UseTextAlignment = false;
            // 
            // ReportHeader1
            // 
            this.ReportHeader1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.table1});
            resources.ApplyResources(this.ReportHeader1, "ReportHeader1");
            this.ReportHeader1.Expanded = false;
            this.ReportHeader1.Name = "ReportHeader1";
            // 
            // table1
            // 
            this.table1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.table1, "table1");
            this.table1.Name = "table1";
            this.table1.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.tableRow2,
            this.tableRow3});
            this.table1.StylePriority.UseBorders = false;
            this.table1.StylePriority.UseFont = false;
            this.table1.StylePriority.UseTextAlignment = false;
            // 
            // tableRow2
            // 
            this.tableRow2.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.tableCell2});
            resources.ApplyResources(this.tableRow2, "tableRow2");
            this.tableRow2.Name = "tableRow2";
            // 
            // tableCell2
            // 
            resources.ApplyResources(this.tableCell2, "tableCell2");
            this.tableCell2.Name = "tableCell2";
            this.tableCell2.StylePriority.UseBackColor = false;
            this.tableCell2.StylePriority.UseFont = false;
            // 
            // tableRow3
            // 
            resources.ApplyResources(this.tableRow3, "tableRow3");
            this.tableRow3.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.tableCell4,
            this.tableCell5,
            this.tableCell6,
            this.tableCell7,
            this.tableCell8,
            this.tableCell9,
            this.tableCell10});
            this.tableRow3.Name = "tableRow3";
            this.tableRow3.StylePriority.UseBackColor = false;
            this.tableRow3.StylePriority.UseFont = false;
            // 
            // tableCell4
            // 
            resources.ApplyResources(this.tableCell4, "tableCell4");
            this.tableCell4.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell4.Name = "tableCell4";
            this.tableCell4.StylePriority.UseBackColor = false;
            this.tableCell4.StylePriority.UseBorders = false;
            // 
            // tableCell5
            // 
            resources.ApplyResources(this.tableCell5, "tableCell5");
            this.tableCell5.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell5.Name = "tableCell5";
            this.tableCell5.StylePriority.UseBackColor = false;
            this.tableCell5.StylePriority.UseBorders = false;
            // 
            // tableCell6
            // 
            resources.ApplyResources(this.tableCell6, "tableCell6");
            this.tableCell6.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell6.Name = "tableCell6";
            this.tableCell6.StylePriority.UseBackColor = false;
            this.tableCell6.StylePriority.UseBorders = false;
            // 
            // tableCell7
            // 
            resources.ApplyResources(this.tableCell7, "tableCell7");
            this.tableCell7.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell7.Name = "tableCell7";
            this.tableCell7.StylePriority.UseBackColor = false;
            this.tableCell7.StylePriority.UseBorders = false;
            // 
            // tableCell8
            // 
            resources.ApplyResources(this.tableCell8, "tableCell8");
            this.tableCell8.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell8.Name = "tableCell8";
            this.tableCell8.StylePriority.UseBackColor = false;
            this.tableCell8.StylePriority.UseBorders = false;
            // 
            // tableCell9
            // 
            resources.ApplyResources(this.tableCell9, "tableCell9");
            this.tableCell9.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell9.Name = "tableCell9";
            this.tableCell9.StylePriority.UseBackColor = false;
            this.tableCell9.StylePriority.UseBorders = false;
            // 
            // tableCell10
            // 
            resources.ApplyResources(this.tableCell10, "tableCell10");
            this.tableCell10.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell10.Name = "tableCell10";
            this.tableCell10.StylePriority.UseBackColor = false;
            this.tableCell10.StylePriority.UseBorders = false;
            // 
            // ReportFooter1
            // 
            this.ReportFooter1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable7});
            resources.ApplyResources(this.ReportFooter1, "ReportFooter1");
            this.ReportFooter1.Expanded = false;
            this.ReportFooter1.Name = "ReportFooter1";
            // 
            // xrTable7
            // 
            this.xrTable7.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.xrTable7, "xrTable7");
            this.xrTable7.Name = "xrTable7";
            this.xrTable7.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow7});
            this.xrTable7.Scripts.OnBeforePrint = "table2_BeforePrint";
            this.xrTable7.StylePriority.UseBorders = false;
            this.xrTable7.StylePriority.UseFont = false;
            this.xrTable7.StylePriority.UseTextAlignment = false;
            // 
            // xrTableRow7
            // 
            resources.ApplyResources(this.xrTableRow7, "xrTableRow7");
            this.xrTableRow7.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_BillQtySum,
            this.xrTableCell4});
            this.xrTableRow7.Name = "xrTableRow7";
            this.xrTableRow7.StylePriority.UseBackColor = false;
            this.xrTableRow7.StylePriority.UseFont = false;
            // 
            // cell_BillQtySum
            // 
            this.cell_BillQtySum.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.cell_BillQtySum.CanGrow = false;
            resources.ApplyResources(this.cell_BillQtySum, "cell_BillQtySum");
            this.cell_BillQtySum.Multiline = true;
            this.cell_BillQtySum.Name = "cell_BillQtySum";
            this.cell_BillQtySum.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_BillQtySum.StylePriority.UseBorders = false;
            this.cell_BillQtySum.StylePriority.UseFont = false;
            this.cell_BillQtySum.StylePriority.UsePadding = false;
            this.cell_BillQtySum.StylePriority.UseTextAlignment = false;
            // 
            // xrTableCell4
            // 
            this.xrTableCell4.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrTableCell4.CanGrow = false;
            resources.ApplyResources(this.xrTableCell4, "xrTableCell4");
            this.xrTableCell4.Multiline = true;
            this.xrTableCell4.Name = "xrTableCell4";
            this.xrTableCell4.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 0, 0, 0, 100F);
            this.xrTableCell4.StylePriority.UseBorders = false;
            this.xrTableCell4.StylePriority.UseFont = false;
            this.xrTableCell4.StylePriority.UsePadding = false;
            this.xrTableCell4.StylePriority.UseTextAlignment = false;
            // 
            // DetailReport_PrInv
            // 
            this.DetailReport_PrInv.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail3,
            this.ReportHeader2,
            this.ReportFooter2});
            resources.ApplyResources(this.DetailReport_PrInv, "DetailReport_PrInv");
            this.DetailReport_PrInv.Level = 2;
            this.DetailReport_PrInv.Name = "DetailReport_PrInv";
            // 
            // Detail3
            // 
            this.Detail3.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.table4});
            resources.ApplyResources(this.Detail3, "Detail3");
            this.Detail3.Name = "Detail3";
            // 
            // table4
            // 
            this.table4.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.table4, "table4");
            this.table4.Name = "table4";
            this.table4.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.tableRow7});
            this.table4.StylePriority.UseBorders = false;
            this.table4.StylePriority.UseFont = false;
            this.table4.StylePriority.UseTextAlignment = false;
            // 
            // tableRow7
            // 
            resources.ApplyResources(this.tableRow7, "tableRow7");
            this.tableRow7.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.col_SlInv_Qty,
            this.col_SlInv_UOM,
            this.col_SlInv_ItemName,
            this.col_SlInv_Code1,
            this.col_SlInv_Branch,
            this.col_SlInv_InvCode,
            this.col_SlInv_Date});
            this.tableRow7.Name = "tableRow7";
            this.tableRow7.StylePriority.UseBackColor = false;
            this.tableRow7.StylePriority.UseFont = false;
            // 
            // col_SlInv_Qty
            // 
            this.col_SlInv_Qty.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.col_SlInv_Qty.CanGrow = false;
            resources.ApplyResources(this.col_SlInv_Qty, "col_SlInv_Qty");
            this.col_SlInv_Qty.Multiline = true;
            this.col_SlInv_Qty.Name = "col_SlInv_Qty";
            this.col_SlInv_Qty.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.col_SlInv_Qty.StylePriority.UseBorders = false;
            this.col_SlInv_Qty.StylePriority.UseFont = false;
            this.col_SlInv_Qty.StylePriority.UsePadding = false;
            this.col_SlInv_Qty.StylePriority.UseTextAlignment = false;
            // 
            // col_SlInv_UOM
            // 
            this.col_SlInv_UOM.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.col_SlInv_UOM.CanGrow = false;
            resources.ApplyResources(this.col_SlInv_UOM, "col_SlInv_UOM");
            this.col_SlInv_UOM.Multiline = true;
            this.col_SlInv_UOM.Name = "col_SlInv_UOM";
            this.col_SlInv_UOM.StylePriority.UseBorders = false;
            this.col_SlInv_UOM.StylePriority.UseFont = false;
            this.col_SlInv_UOM.StylePriority.UsePadding = false;
            this.col_SlInv_UOM.StylePriority.UseTextAlignment = false;
            // 
            // col_SlInv_ItemName
            // 
            this.col_SlInv_ItemName.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.col_SlInv_ItemName.CanGrow = false;
            resources.ApplyResources(this.col_SlInv_ItemName, "col_SlInv_ItemName");
            this.col_SlInv_ItemName.Multiline = true;
            this.col_SlInv_ItemName.Name = "col_SlInv_ItemName";
            this.col_SlInv_ItemName.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.col_SlInv_ItemName.StylePriority.UseBorders = false;
            this.col_SlInv_ItemName.StylePriority.UseFont = false;
            this.col_SlInv_ItemName.StylePriority.UsePadding = false;
            this.col_SlInv_ItemName.StylePriority.UseTextAlignment = false;
            // 
            // col_SlInv_Code1
            // 
            this.col_SlInv_Code1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.col_SlInv_Code1.CanGrow = false;
            resources.ApplyResources(this.col_SlInv_Code1, "col_SlInv_Code1");
            this.col_SlInv_Code1.Multiline = true;
            this.col_SlInv_Code1.Name = "col_SlInv_Code1";
            this.col_SlInv_Code1.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.col_SlInv_Code1.StylePriority.UseBorders = false;
            this.col_SlInv_Code1.StylePriority.UseFont = false;
            this.col_SlInv_Code1.StylePriority.UsePadding = false;
            this.col_SlInv_Code1.StylePriority.UseTextAlignment = false;
            // 
            // col_SlInv_Branch
            // 
            this.col_SlInv_Branch.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.col_SlInv_Branch.CanGrow = false;
            resources.ApplyResources(this.col_SlInv_Branch, "col_SlInv_Branch");
            this.col_SlInv_Branch.Multiline = true;
            this.col_SlInv_Branch.Name = "col_SlInv_Branch";
            this.col_SlInv_Branch.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.col_SlInv_Branch.StylePriority.UseBorders = false;
            this.col_SlInv_Branch.StylePriority.UseFont = false;
            this.col_SlInv_Branch.StylePriority.UsePadding = false;
            this.col_SlInv_Branch.StylePriority.UseTextAlignment = false;
            // 
            // col_SlInv_InvCode
            // 
            this.col_SlInv_InvCode.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.col_SlInv_InvCode.CanGrow = false;
            resources.ApplyResources(this.col_SlInv_InvCode, "col_SlInv_InvCode");
            this.col_SlInv_InvCode.Multiline = true;
            this.col_SlInv_InvCode.Name = "col_SlInv_InvCode";
            this.col_SlInv_InvCode.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.col_SlInv_InvCode.StylePriority.UseBorders = false;
            this.col_SlInv_InvCode.StylePriority.UseFont = false;
            this.col_SlInv_InvCode.StylePriority.UsePadding = false;
            this.col_SlInv_InvCode.StylePriority.UseTextAlignment = false;
            // 
            // col_SlInv_Date
            // 
            this.col_SlInv_Date.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.col_SlInv_Date.CanGrow = false;
            resources.ApplyResources(this.col_SlInv_Date, "col_SlInv_Date");
            this.col_SlInv_Date.Multiline = true;
            this.col_SlInv_Date.Name = "col_SlInv_Date";
            this.col_SlInv_Date.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.col_SlInv_Date.StylePriority.UseBorders = false;
            this.col_SlInv_Date.StylePriority.UseFont = false;
            this.col_SlInv_Date.StylePriority.UsePadding = false;
            this.col_SlInv_Date.StylePriority.UseTextAlignment = false;
            // 
            // ReportHeader2
            // 
            this.ReportHeader2.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.table3});
            resources.ApplyResources(this.ReportHeader2, "ReportHeader2");
            this.ReportHeader2.Name = "ReportHeader2";
            // 
            // table3
            // 
            this.table3.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.table3, "table3");
            this.table3.Name = "table3";
            this.table3.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.tableRow5,
            this.tableRow6});
            this.table3.StylePriority.UseBorders = false;
            this.table3.StylePriority.UseFont = false;
            this.table3.StylePriority.UseTextAlignment = false;
            // 
            // tableRow5
            // 
            this.tableRow5.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.tableCell18});
            resources.ApplyResources(this.tableRow5, "tableRow5");
            this.tableRow5.Name = "tableRow5";
            // 
            // tableCell18
            // 
            resources.ApplyResources(this.tableCell18, "tableCell18");
            this.tableCell18.Name = "tableCell18";
            this.tableCell18.StylePriority.UseBackColor = false;
            this.tableCell18.StylePriority.UseFont = false;
            // 
            // tableRow6
            // 
            resources.ApplyResources(this.tableRow6, "tableRow6");
            this.tableRow6.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.tableCell19,
            this.tableCell20,
            this.tableCell21,
            this.tableCell22,
            this.tableCell23,
            this.tableCell24,
            this.tableCell25});
            this.tableRow6.Name = "tableRow6";
            this.tableRow6.StylePriority.UseBackColor = false;
            this.tableRow6.StylePriority.UseFont = false;
            // 
            // tableCell19
            // 
            resources.ApplyResources(this.tableCell19, "tableCell19");
            this.tableCell19.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell19.Name = "tableCell19";
            this.tableCell19.StylePriority.UseBackColor = false;
            this.tableCell19.StylePriority.UseBorders = false;
            // 
            // tableCell20
            // 
            resources.ApplyResources(this.tableCell20, "tableCell20");
            this.tableCell20.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell20.Name = "tableCell20";
            this.tableCell20.StylePriority.UseBackColor = false;
            this.tableCell20.StylePriority.UseBorders = false;
            // 
            // tableCell21
            // 
            resources.ApplyResources(this.tableCell21, "tableCell21");
            this.tableCell21.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell21.Name = "tableCell21";
            this.tableCell21.StylePriority.UseBackColor = false;
            this.tableCell21.StylePriority.UseBorders = false;
            // 
            // tableCell22
            // 
            resources.ApplyResources(this.tableCell22, "tableCell22");
            this.tableCell22.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell22.Name = "tableCell22";
            this.tableCell22.StylePriority.UseBackColor = false;
            this.tableCell22.StylePriority.UseBorders = false;
            // 
            // tableCell23
            // 
            resources.ApplyResources(this.tableCell23, "tableCell23");
            this.tableCell23.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell23.Name = "tableCell23";
            this.tableCell23.StylePriority.UseBackColor = false;
            this.tableCell23.StylePriority.UseBorders = false;
            // 
            // tableCell24
            // 
            resources.ApplyResources(this.tableCell24, "tableCell24");
            this.tableCell24.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell24.Name = "tableCell24";
            this.tableCell24.StylePriority.UseBackColor = false;
            this.tableCell24.StylePriority.UseBorders = false;
            // 
            // tableCell25
            // 
            resources.ApplyResources(this.tableCell25, "tableCell25");
            this.tableCell25.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell25.Name = "tableCell25";
            this.tableCell25.StylePriority.UseBackColor = false;
            this.tableCell25.StylePriority.UseBorders = false;
            // 
            // ReportFooter2
            // 
            this.ReportFooter2.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable8});
            resources.ApplyResources(this.ReportFooter2, "ReportFooter2");
            this.ReportFooter2.Name = "ReportFooter2";
            // 
            // xrTable8
            // 
            this.xrTable8.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            resources.ApplyResources(this.xrTable8, "xrTable8");
            this.xrTable8.Name = "xrTable8";
            this.xrTable8.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow8});
            this.xrTable8.Scripts.OnBeforePrint = "table2_BeforePrint";
            this.xrTable8.StylePriority.UseBorders = false;
            this.xrTable8.StylePriority.UseFont = false;
            this.xrTable8.StylePriority.UseTextAlignment = false;
            // 
            // xrTableRow8
            // 
            resources.ApplyResources(this.xrTableRow8, "xrTableRow8");
            this.xrTableRow8.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_InvQtySum,
            this.xrTableCell10});
            this.xrTableRow8.Name = "xrTableRow8";
            this.xrTableRow8.StylePriority.UseBackColor = false;
            this.xrTableRow8.StylePriority.UseFont = false;
            // 
            // cell_InvQtySum
            // 
            this.cell_InvQtySum.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.cell_InvQtySum.CanGrow = false;
            resources.ApplyResources(this.cell_InvQtySum, "cell_InvQtySum");
            this.cell_InvQtySum.Multiline = true;
            this.cell_InvQtySum.Name = "cell_InvQtySum";
            this.cell_InvQtySum.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_InvQtySum.StylePriority.UseBorders = false;
            this.cell_InvQtySum.StylePriority.UseFont = false;
            this.cell_InvQtySum.StylePriority.UsePadding = false;
            this.cell_InvQtySum.StylePriority.UseTextAlignment = false;
            // 
            // xrTableCell10
            // 
            this.xrTableCell10.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrTableCell10.CanGrow = false;
            resources.ApplyResources(this.xrTableCell10, "xrTableCell10");
            this.xrTableCell10.Multiline = true;
            this.xrTableCell10.Name = "xrTableCell10";
            this.xrTableCell10.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 0, 0, 0, 100F);
            this.xrTableCell10.StylePriority.UseBorders = false;
            this.xrTableCell10.StylePriority.UseFont = false;
            this.xrTableCell10.StylePriority.UsePadding = false;
            this.xrTableCell10.StylePriority.UseTextAlignment = false;
            // 
            // rpt_PR_PurchaseOrder
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail,
            this.TopMargin,
            this.BottomMargin,
            this.PageHeader,
            this.ReportFooter,
            this.DetailReport_Po_Order,
            this.DetailReport_InTrns,
            this.DetailReport_PrInv});
            resources.ApplyResources(this, "$this");
            this.ExportOptions.Csv.EncodingType = ((DevExpress.XtraPrinting.EncodingType)(resources.GetObject("rpt_PR_PurchaseOrder.ExportOptions.Csv.EncodingType")));
            this.ExportOptions.Csv.Separator = resources.GetString("rpt_PR_PurchaseOrder.ExportOptions.Csv.Separator");
            this.Version = "15.1";
            ((System.ComponentModel.ISupportInitialize)(this.xrTable5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.table2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.table1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.table4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.table3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.XRLabel lbl_Serial;
        private DevExpress.XtraReports.UI.XRLabel lbl_Vendor;
        private DevExpress.XtraReports.UI.PageHeaderBand PageHeader;
        private DevExpress.XtraReports.UI.ReportFooterBand ReportFooter;
        private DevExpress.XtraReports.UI.XRLabel xrLabel12;
        private DevExpress.XtraReports.UI.XRLabel lbl_VenFax;
        private DevExpress.XtraReports.UI.XRLabel lbl_VenTel;
        private DevExpress.XtraReports.UI.XRLabel xrLabel3;
        private DevExpress.XtraReports.UI.XRTable xrTable3;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow3;
        private DevExpress.XtraReports.UI.XRTableCell cell_Height;
        private DevExpress.XtraReports.UI.XRTableCell cell_Width;
        private DevExpress.XtraReports.UI.XRTableCell cell_Length;
        private DevExpress.XtraReports.UI.XRTableCell cell_TotalQty;
        private DevExpress.XtraReports.UI.XRTable xrTable4;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow4;
        private DevExpress.XtraReports.UI.XRTableCell Cell_MUOM;
        private DevExpress.XtraReports.UI.XRTableCell Cell_MUOM_Factor;
        private DevExpress.XtraReports.UI.XRTableCell cell_Factor;
        private DevExpress.XtraReports.UI.XRLabel lbl_ShipTo;
        private DevExpress.XtraReports.UI.XRLabel xrLabel5;
        private DevExpress.XtraReports.UI.XRLabel lbl_DeliverDate;
        private DevExpress.XtraReports.UI.XRLabel lbl_AttnMr;
        private DevExpress.XtraReports.UI.XRLabel xrLabel1;
        private DevExpress.XtraReports.UI.XRLine xrLine1;
        private DevExpress.XtraReports.UI.XRLabel xrLabel4;
        private DevExpress.XtraReports.UI.XRLabel lblCompName;
        private DevExpress.XtraReports.UI.XRLabel lbl_AttnMr_Job;
        private DevExpress.XtraReports.UI.XRLabel xrLabel2;
        private DevExpress.XtraReports.UI.XRLabel xrLabel14;
        private DevExpress.XtraReports.UI.XRLabel lbl_store;
        private DevExpress.XtraReports.UI.XRLabel lblReportName;
        private DevExpress.XtraReports.UI.XRLabel lbl_Number;
        private DevExpress.XtraReports.UI.XRLabel lbl_notes;
        private DevExpress.XtraReports.UI.XRPictureBox picLogo;
        private DevExpress.XtraReports.UI.XRLabel xrLabel6;
        private DevExpress.XtraReports.UI.XRLabel lbl_date;
        private DevExpress.XtraReports.UI.XRLabel lbl_User;
        private DevExpress.XtraReports.UI.XRLabel xrLabel7;
        private DevExpress.XtraReports.UI.XRLabel xrLabel8;
        private DevExpress.XtraReports.UI.DetailReportBand DetailReport_Po_Order;
        private DevExpress.XtraReports.UI.DetailBand Detail1;
        private DevExpress.XtraReports.UI.ReportHeaderBand ReportHeader;
        private DevExpress.XtraReports.UI.DetailReportBand DetailReport_InTrns;
        private DevExpress.XtraReports.UI.DetailBand Detail2;
        private DevExpress.XtraReports.UI.ReportHeaderBand ReportHeader1;
        private DevExpress.XtraReports.UI.DetailReportBand DetailReport_PrInv;
        private DevExpress.XtraReports.UI.DetailBand Detail3;
        private DevExpress.XtraReports.UI.ReportHeaderBand ReportHeader2;
        private DevExpress.XtraReports.UI.XRTable xrTable1;
        private DevExpress.XtraReports.UI.XRTableRow tableRow1;
        private DevExpress.XtraReports.UI.XRTableCell tableCell1;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow1;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell1;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell9;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell7;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell6;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell5;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell3;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell8;
        private DevExpress.XtraReports.UI.XRTable xrTable2;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow2;
        private DevExpress.XtraReports.UI.XRTableCell cell_Total;
        private DevExpress.XtraReports.UI.XRTableCell cell_Disc;
        private DevExpress.XtraReports.UI.XRTableCell cell_Price;
        private DevExpress.XtraReports.UI.XRTableCell cell_Qty;
        private DevExpress.XtraReports.UI.XRTableCell cell_UOM;
        private DevExpress.XtraReports.UI.XRTableCell cell_ItemName;
        private DevExpress.XtraReports.UI.XRTableCell cell_code;
        private DevExpress.XtraReports.UI.XRTable table1;
        private DevExpress.XtraReports.UI.XRTableRow tableRow2;
        private DevExpress.XtraReports.UI.XRTableCell tableCell2;
        private DevExpress.XtraReports.UI.XRTableRow tableRow3;
        private DevExpress.XtraReports.UI.XRTableCell tableCell4;
        private DevExpress.XtraReports.UI.XRTableCell tableCell5;
        private DevExpress.XtraReports.UI.XRTableCell tableCell6;
        private DevExpress.XtraReports.UI.XRTableCell tableCell7;
        private DevExpress.XtraReports.UI.XRTableCell tableCell8;
        private DevExpress.XtraReports.UI.XRTableCell tableCell9;
        private DevExpress.XtraReports.UI.XRTableCell tableCell10;
        private DevExpress.XtraReports.UI.XRTable table2;
        private DevExpress.XtraReports.UI.XRTableRow tableRow4;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_Qty;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_UOM;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_ItemName;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_ItemCode1;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_Store;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_Code;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_Date;
        private DevExpress.XtraReports.UI.XRTable table3;
        private DevExpress.XtraReports.UI.XRTableRow tableRow5;
        private DevExpress.XtraReports.UI.XRTableCell tableCell18;
        private DevExpress.XtraReports.UI.XRTableRow tableRow6;
        private DevExpress.XtraReports.UI.XRTableCell tableCell19;
        private DevExpress.XtraReports.UI.XRTableCell tableCell20;
        private DevExpress.XtraReports.UI.XRTableCell tableCell21;
        private DevExpress.XtraReports.UI.XRTableCell tableCell22;
        private DevExpress.XtraReports.UI.XRTableCell tableCell23;
        private DevExpress.XtraReports.UI.XRTableCell tableCell24;
        private DevExpress.XtraReports.UI.XRTableCell tableCell25;
        private DevExpress.XtraReports.UI.XRTable table4;
        private DevExpress.XtraReports.UI.XRTableRow tableRow7;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_Qty;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_UOM;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_ItemName;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_Code1;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_Branch;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_InvCode;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_Date;
        private DevExpress.XtraReports.UI.XRLabel lbl_DiscountV;
        private DevExpress.XtraReports.UI.XRLabel lbl_Total;
        private DevExpress.XtraReports.UI.XRPageInfo xrPageInfo2;
        private DevExpress.XtraReports.UI.XRLine xrLine2;
        private DevExpress.XtraReports.UI.XRLabel lbl_Net;
        private DevExpress.XtraReports.UI.XRLabel xrLabel9;
        private DevExpress.XtraReports.UI.XRLabel xrLabel10;
        private DevExpress.XtraReports.UI.XRLabel lbl_DiscountR;
        private DevExpress.XtraReports.UI.XRTable xrTable5;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow5;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_ItemCode2;
        private DevExpress.XtraReports.UI.XRTableCell col_OuTrans_Expire;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_Batch;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_Length;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_Width;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_Height;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_TotlQty;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_PcCount;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_Qc;
        private DevExpress.XtraReports.UI.XRTable xrTable6;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow6;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_Code2;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_Expire;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_Batch;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_Length;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_Width;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_Height;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_TotlQty;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_PcCount;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_Qc;
        private DevExpress.XtraReports.UI.XRLabel xrLabel15;
        private DevExpress.XtraReports.UI.XRLabel lbl_tax;
        private DevExpress.XtraReports.UI.XRTableCell cell_code2;
        private DevExpress.XtraReports.UI.ReportFooterBand ReportFooter1;
        private DevExpress.XtraReports.UI.XRTable xrTable7;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow7;
        private DevExpress.XtraReports.UI.XRTableCell cell_BillQtySum;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell4;
        private DevExpress.XtraReports.UI.ReportFooterBand ReportFooter2;
        private DevExpress.XtraReports.UI.XRTable xrTable8;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow8;
        private DevExpress.XtraReports.UI.XRTableCell cell_InvQtySum;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell10;
        private DevExpress.XtraReports.UI.GroupFooterBand GroupFooter1;
        private DevExpress.XtraReports.UI.XRLabel xrLabel31;
        private DevExpress.XtraReports.UI.XRLabel xrLabel32;
        private DevExpress.XtraReports.UI.XRLabel xrLabel23;
        private DevExpress.XtraReports.UI.XRLabel xrLabel24;
        private DevExpress.XtraReports.UI.XRLabel xrLabel25;
        private DevExpress.XtraReports.UI.XRLabel xrLabel26;
        private DevExpress.XtraReports.UI.XRLabel xrLabel27;
        private DevExpress.XtraReports.UI.XRLabel xrLabel28;
        private DevExpress.XtraReports.UI.XRLabel xrLabel29;
        private DevExpress.XtraReports.UI.XRLabel xrLabel30;
        private DevExpress.XtraReports.UI.XRLabel txtPayments;
        private DevExpress.XtraReports.UI.XRLabel xrLabel20;
        private DevExpress.XtraReports.UI.XRLabel xrLabel13;
        private DevExpress.XtraReports.UI.XRLabel txtReferenceNo;
        private DevExpress.XtraReports.UI.XRLabel xrLabel17;
        private DevExpress.XtraReports.UI.XRLabel txt_QutationDate;
        private DevExpress.XtraReports.UI.XRLabel xrLabel16;
        private DevExpress.XtraReports.UI.XRLabel xrLabel33;
        private DevExpress.XtraReports.UI.XRLabel txt_VendorEmail;
        private DevExpress.XtraReports.UI.XRLabel xrLabel21;
        private DevExpress.XtraReports.UI.XRLabel txt_CostCenter;
        private DevExpress.XtraReports.UI.XRLabel txt_CostCenterCode;
        private DevExpress.XtraReports.UI.XRLabel xrLabel19;
        private DevExpress.XtraReports.UI.XRLabel xrLabel18;
        private DevExpress.XtraReports.UI.XRLabel lblTotalWords;
    }
}
