﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DevExpress.XtraGrid.Views.Grid;
using DAL;
using DAL.Res;

namespace Pharmacy.Forms
{
    public partial class frm_IC_Store : DevExpress.XtraEditors.XtraForm
    {
        int StoreId, lastStoreId, firstStoreId;
        FormAction action = FormAction.None;
        UserPriv prvlg;
        bool DataModified;
        bool ExpireDate;//expire:mahmoud
        List<acc> lstAccounts = new List<acc>();

        public frm_IC_Store(int store_ID, FormAction action)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            StoreId = store_ID;
            this.action = action;
        }

        private void frm_IC_Store_Load(object sender, EventArgs e)
        {
            ERPDataContext DB = new ERPDataContext();

            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            if (!Shared.LibraAvailabe)
                lbl_PriceList.Visible = lkpPriceLevel.Visible = false;
            else
            {
                lkpPriceLevel.Properties.ValueMember = "PriceLevelId";
                lkpPriceLevel.Properties.DisplayMember = "PLName";
                lkpPriceLevel.Properties.DataSource = (from st in DB.IC_PriceLevels.AsEnumerable()
                                                       select new
                                                       {
                                                           st.PriceLevelId,
                                                           st.PLName,
                                                           IsRatio = st.IsRatio == true ?
                                                           (Shared.IsEnglish ? ResICEn.FixedRatio : ResICAr.FixedRatio) :
                                                           (Shared.IsEnglish ? ResICEn.PerItem : ResICAr.PerItem),
                                                           st.IsRatioIncrease,
                                                           st.Ratio,
                                                           Details = st.IsRatio == true && st.IsRatioIncrease == true ?
                                                           (decimal.ToDouble(st.Ratio)).ToString() + "%" :
                                                           (st.IsRatio == true && st.IsRatioIncrease == false ?
                                                           (decimal.ToDouble((st.Ratio * -1))).ToString() + "%" :
                                                           (Shared.IsEnglish ? ResICEn.variesPerItem : ResICAr.variesPerItem))
                                                       }).ToList();
            }

            if (Shared.StockIsPeriodic)
                tab_PerpetualAcc.PageVisible = false;
            else
                tab_PeriodicAcc.PageVisible = false;

            if (Shared.ItemsPostingAvailable)
            {
                tab_PerpetualAcc.PageVisible = false;
                tab_PeriodicAcc.PageVisible = false;
                //tab_CostCenter.PageVisible = true;
            }

            #region Bind Stores
            lkpStore.Properties.DisplayMember = "StoreNameAr";
            lkpStore.Properties.ValueMember = "StoreId";
            #endregion

            BindAccounts();
            LoadPrivilege();
            GetFirstAndLast();

            //commented by mohammad ********
            //Expire: mahmoud 29-10-2012
            //check if erp supports expire date 
            //ExpireDate = DB.ST_Stores.First().ExpireDate;
            //cb_CostMethod.Enabled = !ExpireDate;

            if (StoreId == 0)
            {
                if (action == FormAction.None)
                {
                    if (lastStoreId == 0 && firstStoreId == 0)
                    {
                        action = FormAction.Add;
                        NewStore();
                    }
                    else if (lastStoreId >= firstStoreId)
                    {
                        StoreId = lastStoreId;
                        GetStoreData(false);
                    }
                }
                else if (action == FormAction.Add)
                    NewStore();
            }
            else
            {
                GetStoreData(true);
            }
        }

        private void BindAccounts()
        {
            #region Bind Accs
            lstAccounts = HelperAcc.LoadAccountsTree(0, true);
            lkp_PerPetualSalesAcc.Properties.DataSource =
            lkp_PerPetualSalesReturnAcc.Properties.DataSource =
            lkp_PerPetualCostOfSoldGoods.Properties.DataSource =
            lkp_PerPetualInventoryAcc.Properties.DataSource =
            lkp_PerPetualSalesDiscount.Properties.DataSource =
            lkp_PerPetualPurchaseDiscountAcc.Properties.DataSource =

                lkp_PeriodicCloseInvAcc.Properties.DataSource =
                lkp_PeriodicOpenInvAcc.Properties.DataSource =
                lkp_PeriodicPrDiscAcc.Properties.DataSource =
                lkp_PeriodicPrReturnAcc.Properties.DataSource =
                lkp_PeriodicPurchaseAcc.Properties.DataSource =
                lkp_PeriodicSalesAcc.Properties.DataSource =
                lkp_PeriodicSalesDiscAcc.Properties.DataSource =
                lkp_PeriodicSalesReturnAcc.Properties.DataSource =
            lstAccounts;

            lkp_PerPetualSalesAcc.Properties.ValueMember =
            lkp_PerPetualSalesReturnAcc.Properties.ValueMember =
            lkp_PerPetualCostOfSoldGoods.Properties.ValueMember =
            lkp_PerPetualInventoryAcc.Properties.ValueMember =
            lkp_PerPetualSalesDiscount.Properties.ValueMember =
            lkp_PerPetualPurchaseDiscountAcc.Properties.ValueMember =

                lkp_PeriodicCloseInvAcc.Properties.ValueMember =
                lkp_PeriodicOpenInvAcc.Properties.ValueMember =
                lkp_PeriodicPrDiscAcc.Properties.ValueMember =
                lkp_PeriodicPrReturnAcc.Properties.ValueMember =
                lkp_PeriodicPurchaseAcc.Properties.ValueMember =
                lkp_PeriodicSalesAcc.Properties.ValueMember =
                lkp_PeriodicSalesDiscAcc.Properties.ValueMember =
                lkp_PeriodicSalesReturnAcc.Properties.ValueMember =
            "AccId";

            lkp_PerPetualSalesAcc.Properties.DisplayMember =
            lkp_PerPetualSalesReturnAcc.Properties.DisplayMember =
            lkp_PerPetualCostOfSoldGoods.Properties.DisplayMember =
            lkp_PerPetualInventoryAcc.Properties.DisplayMember =
            lkp_PerPetualSalesDiscount.Properties.DisplayMember =
            lkp_PerPetualPurchaseDiscountAcc.Properties.DisplayMember =

                lkp_PeriodicCloseInvAcc.Properties.DisplayMember =
                lkp_PeriodicOpenInvAcc.Properties.DisplayMember =
                lkp_PeriodicPrDiscAcc.Properties.DisplayMember =
                lkp_PeriodicPrReturnAcc.Properties.DisplayMember =
                lkp_PeriodicPurchaseAcc.Properties.DisplayMember =
                lkp_PeriodicSalesAcc.Properties.DisplayMember =
                lkp_PeriodicSalesDiscAcc.Properties.DisplayMember =
                lkp_PeriodicSalesReturnAcc.Properties.DisplayMember =
            "AccName";
            #endregion
        }

        private void frm_IC_Store_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.PageUp)
            {
                btnPrev.PerformClick();
            }
            if (e.KeyCode == Keys.PageDown)
            {
                btnNext.PerformClick();
            }
        }

        private void frm_IC_Store_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                e.Cancel = true;
        }


        private void btnPrev_Click(object sender, EventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            if (firstStoreId != 0)
            {
                if (StoreId == firstStoreId || StoreId == 0)
                {
                    StoreId = lastStoreId;
                    GetStoreData(false);
                }
                else
                {
                    StoreId -= 1;
                    GetStoreData(false);
                }
            }
        }

        private void btnNext_Click(object sender, EventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            if (lastStoreId != 0)
            {
                if (StoreId >= lastStoreId)
                {
                    StoreId = firstStoreId;
                    GetStoreData(true);
                }
                else
                {
                    StoreId += 1;
                    GetStoreData(true);
                }
            }
        }


        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_List_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_IC_StoresList)))
            {
                frm_IC_StoresList frm = new frm_IC_StoresList();
                frm.BringToFront();
                frm.Show();
            }
            else
                Application.OpenForms["frm_IC_StoresList"].BringToFront();
        }

        private void barBtn_Delete_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (action == FormAction.Add)
                return;

            if (StoreId > 0)
            {
                if (StoreId == 1)
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgMainStoreDel : ResICAr.MsgMainStoreDel
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                DialogResult DR = XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgDelStore : ResICAr.MsgDelStore//"هل أنت متأكد انك تريد حذف هذا المخزن"
                    , "", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2);
                if (DR == DialogResult.Yes)
                {
                    DAL.ERPDataContext DB = new DAL.ERPDataContext();
                    var store = (from i in DB.IC_Stores
                                 where i.StoreId == StoreId
                                 select i).First();
                    var items = (from i in DB.IC_ItemStores
                                 where i.StoreId == StoreId
                                 select i).ToList();

                    var userSettings = (from i in DB.HR_Users
                                        where i.DefaultStore == StoreId
                                        select i).Count();

                    int processes = DB.ACC_Journals.Where(x => x.StoreId == store.StoreId).Count() +
                    DB.IC_InTrns.Where(x => x.StoreId == store.StoreId).Count() +
                    DB.IC_OutTrns.Where(x => x.StoreId == store.StoreId).Count() +
                    DB.IC_Damageds.Where(x => x.StoreId == store.StoreId).Count() +
                    DB.IC_StoreMoves.Where(x => x.SourceStoreId == store.StoreId || x.DestinationStoreId == store.StoreId).Count() +
                    DB.SL_Invoices.Where(x => x.StoreId == store.StoreId).Count() +
                    DB.SL_Returns.Where(x => x.StoreId == store.StoreId).Count() +
                    DB.SL_SalesOrders.Where(x => x.StoreId == store.StoreId).Count() +
                    DB.SL_Quotes.Where(x => x.StoreId == store.StoreId).Count() +

                    DB.PR_Invoices.Where(x => x.StoreId == store.StoreId).Count() +
                    DB.PR_Returns.Where(x => x.StoreId == store.StoreId).Count() +
                    DB.PR_Quotes.Where(x => x.StoreId == store.StoreId).Count() +
                    DB.PR_PurchaseOrders.Where(x => x.StoreId == store.StoreId).Count() +
                    DB.PR_PurchaseRequests.Where(x => x.StoreId == store.StoreId).Count() +
                    DB.HR_Pays.Where(x => x.StoreId == store.StoreId).Count() +
                    DB.HR_Loans.Where(x => x.StoreId == store.StoreId).Count() +
                    DB.ACC_CashNotes.Where(x => x.StoreId == store.StoreId).Count() +
                    DB.ACC_NotesReceivables.Where(x => x.StoreId == store.StoreId).Count() +
                    DB.ACC_NotesPayables.Where(x => x.StoreId == store.StoreId).Count() +
                    DB.ACC_CashTransfers.Where(x => x.StoreId == store.StoreId).Count() +
                    DB.ACC_RevExpEntries.Where(x => x.StoreId == store.StoreId).Count() +
                    DB.Manufacturings.Where(x => x.ProductStoreId == store.StoreId).Count();

                    if (DB.IC_Stores.Where(s => s.ParentId == store.StoreId).Count() > 0)
                    {
                        XtraMessageBox.Show(
                            Shared.IsEnglish == true ? ResICEn.delBranchDenied : ResICAr.delBranchDenied
                            , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    if (store.ParentId == null &&
                        processes > 0)
                    {
                        XtraMessageBox.Show(
                            Shared.IsEnglish == true ? ResICEn.MsgStoreJournals : ResICAr.MsgStoreJournals//"ًيجب حذف القيود الخاصه بهذا المخزن اولا"
                            , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }
                    if (items.Count > 0)
                    {
                        XtraMessageBox.Show(
                            Shared.IsEnglish == true ? ResICEn.MsgStoreItems : ResICAr.MsgStoreItems//"عفواً، يوجد أصناف داخل هذا المخزن, لا يمكن حذف المخزن"
                            , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }
                    if (userSettings > 0)
                    {
                        XtraMessageBox.Show(
                            Shared.IsEnglish == true ? ResICEn.msgUserDefaultStore : ResICAr.msgUserDefaultStore
                            , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    DB.IC_Stores.DeleteOnSubmit(DB.IC_Stores.Where(x => x.StoreId == StoreId).SingleOrDefault());

                    MyHelper.UpdateST_UserLog(DB, txt_StoreCode.Text, txtStoreNameAr.Text,
(int)FormAction.Delete, (int)FormsNames.Store);

                    DB.SubmitChanges();
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgDel : ResICAr.MsgDel//"تم الحذف بنجاح"                            
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    GetFirstAndLast();
                    if (lastStoreId == 0 && firstStoreId == 0) // no records remains
                        Reset();
                    if (StoreId > lastStoreId)//when delete last record
                        btnPrev.PerformClick();
                    else
                        btnNext.PerformClick();
                }
            }
        }

        private void barBtnNew_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            NewStore();
        }

        private void barBtn_Save_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Focus();
            if (!ValidData())
                return;

            SaveData();
        }


        private void txt_StoreCode_Modified(object sender, EventArgs e)
        {
            DataModified = true;
        }


        void DoValidate()
        {
            txt_StoreCode.DoValidate();
            txtStoreNameAr.DoValidate();
            txtStoreNameEn.DoValidate();
            txtAddress.DoValidate();
            txtTel.DoValidate();
            txt_Manager.DoValidate();
         

            cb_CostMethod.DoValidate();
            lkpStore.DoValidate();

            lkp_PerPetualSalesAcc.DoValidate();
            lkp_PerPetualSalesReturnAcc.DoValidate();
            lkp_PerPetualCostOfSoldGoods.DoValidate();
            lkp_PerPetualInventoryAcc.DoValidate();
            lkp_PerPetualPurchaseDiscountAcc.DoValidate();
            lkp_PerPetualSalesDiscount.DoValidate();
        }

        private void NewStore()
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            Reset();
            action = FormAction.Add;

            chk_autoCreateAccs.Enabled = true;
            chk_autoCreateAccs.Checked = true;

            StoreId = new DAL.ERPDataContext().IC_Stores.Select(x => x.StoreId).ToList().DefaultIfEmpty(0).Max() + 1;

            txt_StoreCode.Text = StoreId.ToString();
            txt_StoreCode.Focus();

            #region Load Main Stores
            var stores_table = (from s in new ERPDataContext().IC_Stores
                                where s.ParentId == null
                                //where StoreId >0? s.StoreId != StoreId: true
                                select s
                    ).ToList();

            stores_table.Insert(0, new IC_Store
            {
                StoreId = 0,
                StoreCode = "",
                StoreNameAr = "",
                StoreNameEn = "",
                Address = "",
                Tel = "",
                Mobile = "",
                CloseInventoryAccount = 0,
                OpenInventoryAccount = 0,
                PurchaseAccount = 0,
                PurchaseReturnAccount = 0,
                SellReturnAccount = 0,
                SellAccount = 0,
                ParentId = 0,
                CostMethod = 0,
                ManagerName = "",
                SalesDiscountAcc = 0,
                PurchaseDiscountAcc = 0,
                CostOfSoldGoodsAcc = null,
                IsStopped = false,

            });
            lkpStore.Properties.DataSource = stores_table;
            lkpStore.Enabled = true;
            #endregion

            DoValidate();

            cb_CostMethod.EditValue = (byte)(2);
            cb_CostMethod.Enabled = true;

            //commented by mohammad 15-09-2020
            //expire:mahmoud
            //if (ExpireDate)
            //{
            //    cb_CostMethod.EditValue = (byte)CostMethod.FIFO;
            //    cb_CostMethod.Enabled = false;
            //}
        }

        private void GetStoreData(bool isNext)
        {
            DAL.ERPDataContext pharm = new DAL.ERPDataContext();
            var store = (from i in pharm.IC_Stores
                         where i.StoreId == StoreId
                         select i).SingleOrDefault();
            if (store == null)
            {
                if (isNext)
                    StoreId += 1;
                else
                    StoreId -= 1;

                GetStoreData(isNext);
            }
            else
            {

                chk_autoCreateAccs.Enabled = false;
                chk_autoCreateAccs.Checked = false;
               
                lkpStore.EditValue = store.ParentId == null ? 0 : store.ParentId;
                lkpStore.Enabled = false;

                if (store.ParentId != null && Shared.StockIsPeriodic)
                    xtraTabControl1.Enabled = false;
                else
                    xtraTabControl1.Enabled = true;

                Mobile.Text= store.Mobile;
                if (store.IsStopped == null)
                {
                    IsStopped.Checked = false;
                }
                else
                {
                    IsStopped.Checked = store.IsStopped.Value;
                }
                txt_StoreCode.Text = store.StoreCode.ToString();
                txtStoreNameAr.Text = store.StoreNameAr;
                txtStoreNameEn.Text = store.StoreNameEn;
                txtAddress.Text = store.Address;
                txtTel.Text = store.Tel;



                txt_Manager.Text = store.ManagerName;

                lkpPriceLevel.EditValue = store.pricelistId;
                //accounts
                if (Shared.StockIsPeriodic == false)
                {
                    lkp_PerPetualSalesAcc.EditValue = store.SellAccount;
                    lkp_PerPetualSalesReturnAcc.EditValue = store.SellReturnAccount;
                    lkp_PerPetualCostOfSoldGoods.EditValue = store.CostOfSoldGoodsAcc;
                    lkp_PerPetualInventoryAcc.EditValue = store.PurchaseAccount;
                    lkp_PerPetualPurchaseDiscountAcc.EditValue = store.PurchaseDiscountAcc;
                    lkp_PerPetualSalesDiscount.EditValue = store.SalesDiscountAcc;
                }
                else
                {
                    lkp_PeriodicCloseInvAcc.EditValue = store.CloseInventoryAccount;
                    lkp_PeriodicOpenInvAcc.EditValue = store.OpenInventoryAccount;
                    lkp_PeriodicPrDiscAcc.EditValue = store.PurchaseDiscountAcc;
                    lkp_PeriodicPrReturnAcc.EditValue = store.PurchaseReturnAccount;
                    lkp_PeriodicPurchaseAcc.EditValue = store.PurchaseAccount;
                    lkp_PeriodicSalesAcc.EditValue = store.SellAccount;
                    lkp_PeriodicSalesDiscAcc.EditValue = store.SalesDiscountAcc;
                    lkp_PeriodicSalesReturnAcc.EditValue = store.SellReturnAccount;
                }

                #region Load Main Stores
                var stores_table = (from s in new ERPDataContext().IC_Stores
                                    where s.ParentId == null
                                    where StoreId > 0 ? s.StoreId != StoreId : true
                                    select s
                        ).ToList();

                stores_table.Insert(0, new IC_Store
                {
                    StoreId = 0,
                    StoreCode = "",
                    StoreNameAr = "",
                    StoreNameEn = "",
                    Address = "",
                    Tel = "",
                    CloseInventoryAccount = 0,
                    OpenInventoryAccount = 0,
                    PurchaseAccount = 0,
                    PurchaseReturnAccount = 0,
                    SellReturnAccount = 0,
                    SellAccount = 0,
                    ParentId = 0,
                    CostMethod = 0,
                    ManagerName = "",
                    PurchaseDiscountAcc = 0,
                    SalesDiscountAcc = 0,
                    CostOfSoldGoodsAcc = null

                });
                lkpStore.Properties.DataSource = stores_table;
                #endregion

                StoreId = store.StoreId;
                cb_CostMethod.EditValue = store.CostMethod;

                action = FormAction.Edit;
                DoValidate();
                DataModified = false;

                //var StoreItemsCount = pharm.IC_ItemStores.Where(s => s.StoreId == store.StoreId).Count();
                //if (StoreItemsCount > 0)
                //    cb_CostMethod.Enabled = false;
                //else
                //    cb_CostMethod.Enabled = true;
            }

           //Commented by Mohammad ********
            ////expire:mahmoud
            //if (ExpireDate)
            //{
            //    cb_CostMethod.EditValue = (byte)CostMethod.FIFO;
            //    cb_CostMethod.Enabled = false;
            //}
        }

        private bool Validate_Store_ArName()
        {
            try
            {
                DAL.ERPDataContext pharm = new DAL.ERPDataContext();
                if (string.IsNullOrEmpty(txtStoreNameAr.Text))
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgStoreName : ResICAr.MsgStoreName//"يرجى إدخال اسم المخزن"
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtStoreNameAr.Focus();
                    return false;
                }
                if (action == FormAction.Add)
                {
                    var name = (from n in pharm.IC_Stores
                                where n.StoreNameAr == txtStoreNameAr.Text
                                select n.StoreNameAr).Count();
                    if (name > 0)
                    {
                        XtraMessageBox.Show(
                            Shared.IsEnglish == true ? ResICEn.MsgNameExist : ResICAr.MsgNameExist,//"هذا الاسم مسجل من قبل",
                            Shared.IsEnglish == true ? ResICEn.MsgTWarn : ResICAr.MsgTWarn//"تنبيه"
                            , MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtStoreNameAr.Focus();
                        return false;
                    }
                }
            }
            catch
            {
                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgIncorrectData : ResICAr.MsgIncorrectData//"تأكد من صحة البيانات"                    
                    , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtStoreNameAr.Focus();
                return false;
            }
            return true;
        }

        private bool Validate_Store_Code()
        {
            try
            {
                DAL.ERPDataContext pharm = new DAL.ERPDataContext();
                if (string.IsNullOrEmpty(txt_StoreCode.Text.Trim()))
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgStoreCode : ResICAr.MsgStoreCode//"يرجى إدخال كود المخزن"
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txt_StoreCode.Focus();
                    return false;
                }

                if (txt_StoreCode.Text == "0")
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgZeroCode : ResICAr.MsgZeroCode//"الكود لايمكن أن يساوي صفر"                        
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txt_StoreCode.Focus();
                    return false;
                }
                if (action == FormAction.Add)
                {
                    var code_exist = pharm.IC_Stores.Where(c => c.StoreCode.Trim() == txt_StoreCode.Text.Trim().ToString()).Select(c => c.StoreCode).Count();
                    if (code_exist > 0)
                    {
                        XtraMessageBox.Show(
                            Shared.IsEnglish == true ? ResICEn.MsgCodeExist : ResICAr.MsgCodeExist//"هذا الكود مسجل من قبل"                            
                            , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txt_StoreCode.Focus();
                        return false;
                    }
                }
            }
            catch
            {
                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgIncorrectData : ResICAr.MsgIncorrectData//"تأكد من صحة البيانات"
                    , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txt_StoreCode.Focus();
                return false;
            }
            return true;
        }

        private bool Validate_Store_EnName()
        {
            try
            {
                DAL.ERPDataContext pharm = new DAL.ERPDataContext();
                if (action == FormAction.Add && !string.IsNullOrEmpty(txtStoreNameEn.Text.Trim()))
                {
                    var name = (from n in pharm.IC_Stores
                                where n.StoreNameEn == txtStoreNameEn.Text
                                select n.StoreNameEn).Count();
                    if (name > 0)
                    {
                        XtraMessageBox.Show(
                            Shared.IsEnglish == true ? ResICEn.MsgFNameExist : ResICAr.MsgFNameExist//"الاسم الاجنبي مسجل من قبل"
                            , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtStoreNameEn.Focus();
                        return false;
                    }
                }
            }
            catch
            {
                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgIncorrectData : ResICAr.MsgIncorrectData//"تأكد من صحة البيانات"                    
                    , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtStoreNameEn.Focus();
                return false;
            }
            return true;
        }

        private bool ValidData()
        {
            if (action == FormAction.Add)
            {
                if (prvlg != null && !prvlg.CanAdd)
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgPrvNew : ResICAr.MsgPrvNew//"عفوا, انت لا تمتلك صلاحية انشاء بيان جديد"
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            if (action == FormAction.Edit)
            {
                if (prvlg != null && !prvlg.CanEdit)
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResICEn.MsgPrvEdit : ResICAr.MsgPrvEdit//"عفوا, انت لا تمتلك صلاحية تعديل هذا البيان"                        
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }

            //if (Shared.InvoicePostToStore && Convert.ToInt32(lkpStore.EditValue) != 0)
            //{
            //    XtraMessageBox.Show(
            //     Shared.IsEnglish == true ? ResICEn.MsgSubStore : ResICAr.MsgSubStore
            //     , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
            //    return false;
            //}
            ERPDataContext DB = new ERPDataContext();

            if (Convert.ToInt32(lkpStore.EditValue) != 0)
            {
                IC_Store parent = DB.IC_Stores.Where(s => s.StoreId == Convert.ToInt32(lkpStore.EditValue)).Select(s => s).First();

                if (parent.ParentId.HasValue)
                {
                    XtraMessageBox.Show(
                         Shared.IsEnglish == true ? ResICEn.MsgBranch : ResICAr.MsgBranch
                         , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }

                int parentItemStoresCount = DB.IC_ItemStores.Where(s => s.StoreId == parent.StoreId).Count();
                if (parentItemStoresCount > 0)
                {
                    XtraMessageBox.Show(
                         Shared.IsEnglish == true ? ResICEn.MsgBranchItems : ResICAr.MsgBranchItems
                         , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }

            //you can't change store level after creation
            if (action == FormAction.Edit)
            {
                IC_Store store = DB.IC_Stores.Where(s => s.StoreId == StoreId).Select(s => s).First();

                if ((store.ParentId.HasValue == false && lkpStore.EditValue != null && Convert.ToInt32(lkpStore.EditValue) != 0) ||
                    (store.ParentId.HasValue == true && (lkpStore.EditValue == null || Convert.ToInt32(lkpStore.EditValue) == 0))
                    )
                {
                    XtraMessageBox.Show(
                         Shared.IsEnglish == true ? ResICEn.storeLevel : ResICAr.storeLevel
                         , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }


            if (Validate_Store_Code() == false)
                return false;
            if (Validate_Store_ArName() == false)
                return false;
            if (Validate_Store_EnName() == false)
                return false;

            //if (!Shared.ItemsPostingAvailable)
            //{
            //    #region Validate Accounts
            //    if (Shared.StockIsPeriodic == true)
            //    {
            //        if (chk_autoCreateAccs.Checked == false)
            //        {
            //            if (lkp_PeriodicCloseInvAcc.EditValue == null || lkp_PeriodicOpenInvAcc.EditValue == null ||
            //                lkp_PeriodicPrDiscAcc.EditValue == null || lkp_PeriodicPrReturnAcc.EditValue == null ||
            //                lkp_PeriodicPurchaseAcc.EditValue == null || lkp_PeriodicSalesAcc.EditValue == null ||
            //                lkp_PeriodicSalesDiscAcc.EditValue == null || lkp_PeriodicSalesReturnAcc.EditValue == null)
            //            {
            //                XtraMessageBox.Show(
            //                    Shared.IsEnglish == true ? ResICEn.StockAccs : ResICAr.StockAccs
            //                    , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
            //                return false;
            //            }
            //        }
            //    }


            //    if (Shared.StockIsPeriodic == false)
            //    {
            //        if (lkp_PerPetualSalesAcc.EditValue == null || lkp_PerPetualSalesReturnAcc.EditValue == null ||
            //            lkp_PerPetualCostOfSoldGoods.EditValue == null || lkp_PerPetualInventoryAcc.EditValue == null ||
            //            lkp_PerPetualSalesDiscount.EditValue == null || lkp_PerPetualPurchaseDiscountAcc.EditValue == null)
            //        {
            //            XtraMessageBox.Show(
            //                Shared.IsEnglish == true ? ResICEn.StockAccs : ResICAr.StockAccs
            //                , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
            //            return false;
            //        }
            //    }
            //    if (Shared.StockIsPeriodic &&
            //        (Shared.st_Store.SalesDiscountAcc.HasValue == false || Shared.st_Store.PurchaseDiscountAcc.HasValue == false))
            //    {
            //        //check sales discount Account

            //        XtraMessageBox.Show(
            //                Shared.IsEnglish == true ? ResEn.MsgDiscountAcc : ResAr.MsgDiscountAcc,
            //                Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
            //                MessageBoxButtons.OK, MessageBoxIcon.Warning);

            //        return false;
            //    }
            //    #endregion
            //}
            //else
            //    {
            //        if (lkpCostCenter.EditValue == null || Convert.ToInt32(lkpCostCenter.EditValue) == 0 )
            //        {
            //            XtraMessageBox.Show(
            //Shared.IsEnglish == true ? ResAccEn.ValTxtCstCntr : ResAccAr.ValTxtCstCntr,
            //Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
            //MessageBoxButtons.OK, MessageBoxIcon.Warning);

            //            return false;
            //        }
            //}
            return true;
        }

        private void SaveData()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var stor = (from i in DB.IC_Stores
                        where i.StoreId == StoreId
                        select i).SingleOrDefault();

            if (action == FormAction.Add)
            {
                stor = new IC_Store();

                #region create accounts
                if (!Shared.ItemsPostingAvailable)
                {
                    if (Shared.StockIsPeriodic && lkpStore.EditValue.ToString() == "0")
                    {
                        if (chk_autoCreateAccs.Checked)
                        {
                            #region Auto Create accounts
                            ACC_Account PurAc = new ACC_Account();
                            PurAc.AcNameAr = (Shared.IsEnglish ? ResEn.Purchases : ResAr.Purchases) + " - " + txtStoreNameAr.Text.Trim();//مشتريات
                            PurAc.AcNameEn = (Shared.IsEnglish ? ResAr.Purchases : ResEn.Purchases) + " - " + txtStoreNameEn.Text.Trim();
                            PurAc.AcType = false; //Debit                    
                            PurAc.AllowChild = false;
                            PurAc.AllowEdit = true;

                            var purparentAcc = DB.ACC_Accounts.Where(x => x.AccountId == Shared.st_Store.PurchasesAcc.Value).FirstOrDefault();
                            PurAc.ParentActId = purparentAcc.AccountId;
                            PurAc.Level = purparentAcc.Level + 1;
                            PurAc.AcNumber = HelperAcc.AccNumGenerated(purparentAcc);

                            PurAc.AccSecurityLevel = 1;   //default

                            DB.ACC_Accounts.InsertOnSubmit(PurAc);
                            DB.SubmitChanges();
                            lkp_PeriodicPurchaseAcc.EditValue = PurAc.AccountId;

                            ACC_Account PurRetAc = new ACC_Account();
                            PurRetAc.AcNameAr = (Shared.IsEnglish ? ResEn.PurchasesReturn : ResAr.PurchasesReturn) + " - " + txtStoreNameAr.Text.Trim();//مردود مشتريات 
                            PurRetAc.AcNameEn = (Shared.IsEnglish ? ResAr.PurchasesReturn : ResEn.PurchasesReturn) + " - " + txtStoreNameEn.Text.Trim();
                            PurRetAc.AcType = true; //Credit                    
                            PurRetAc.AllowChild = false;
                            PurRetAc.AllowEdit = true;

                            var purretparentAcc = DB.ACC_Accounts.Where(x => x.AccountId == Shared.st_Store.PurchasesReturnAcc.Value).FirstOrDefault();
                            PurRetAc.ParentActId = purretparentAcc.AccountId;
                            PurRetAc.Level = purretparentAcc.Level + 1;
                            PurRetAc.AcNumber = HelperAcc.AccNumGenerated(purretparentAcc);

                            PurRetAc.AccSecurityLevel = 1;   //default

                            DB.ACC_Accounts.InsertOnSubmit(PurRetAc);
                            DB.SubmitChanges();
                            lkp_PeriodicPrReturnAcc.EditValue = PurRetAc.AccountId;

                            ACC_Account SellAc = new ACC_Account();
                            SellAc.AcNameAr = (Shared.IsEnglish ? ResEn.Sales : ResAr.Sales) + " - " + txtStoreNameAr.Text.Trim();//مبيعات
                            SellAc.AcNameEn = (Shared.IsEnglish ? ResAr.Sales : ResEn.Sales) + " - " + txtStoreNameEn.Text.Trim();
                            SellAc.AcType = true; //credit                      
                            SellAc.AllowChild = false;
                            SellAc.AllowEdit = true;

                            var sellparentAcc = DB.ACC_Accounts.Where(x => x.AccountId == Shared.st_Store.SalesAcc.Value).FirstOrDefault();
                            SellAc.ParentActId = sellparentAcc.AccountId;
                            SellAc.Level = sellparentAcc.Level + 1;
                            SellAc.AcNumber = HelperAcc.AccNumGenerated(sellparentAcc);

                            SellAc.AccSecurityLevel = 1;   //default

                            DB.ACC_Accounts.InsertOnSubmit(SellAc);
                            DB.SubmitChanges();
                            lkp_PeriodicSalesAcc.EditValue = SellAc.AccountId;

                            ACC_Account SellRetAc = new ACC_Account();
                            SellRetAc.AcNameAr = (Shared.IsEnglish ? ResEn.SalesReturn : ResAr.SalesReturn) + " - " + txtStoreNameAr.Text.Trim();//مردود مبيعات
                            SellRetAc.AcNameEn = (Shared.IsEnglish ? ResAr.SalesReturn : ResEn.SalesReturn) + " - " + txtStoreNameEn.Text.Trim();
                            SellRetAc.AcType = false; //Debit                     
                            SellRetAc.AllowChild = false;
                            SellRetAc.AllowEdit = true;

                            var sellretparentAcc = DB.ACC_Accounts.Where(x => x.AccountId == Shared.st_Store.SalesReturnAcc.Value).FirstOrDefault();
                            SellRetAc.ParentActId = sellretparentAcc.AccountId;
                            SellRetAc.Level = sellretparentAcc.Level + 1;
                            SellRetAc.AcNumber = HelperAcc.AccNumGenerated(sellretparentAcc);

                            SellRetAc.AccSecurityLevel = 1;   //default

                            DB.ACC_Accounts.InsertOnSubmit(SellRetAc);
                            DB.SubmitChanges();
                            lkp_PeriodicSalesReturnAcc.EditValue = SellRetAc.AccountId;

                            ACC_Account OpenAc = new ACC_Account();
                            OpenAc.AcNameAr = (Shared.IsEnglish ? ResEn.OpenInventory : ResAr.OpenInventory) + " - " + txtStoreNameAr.Text.Trim();//اول المده 
                            OpenAc.AcNameEn = (Shared.IsEnglish ? ResAr.OpenInventory : ResEn.OpenInventory) + " - " + txtStoreNameEn.Text.Trim();
                            OpenAc.AcType = false; //Debit                     
                            OpenAc.AllowChild = false;
                            OpenAc.AllowEdit = true;

                            var openparentAcc = DB.ACC_Accounts.Where(x => x.AccountId == Shared.st_Store.OpenInventoryAcc.Value).FirstOrDefault();
                            OpenAc.ParentActId = openparentAcc.AccountId;
                            OpenAc.Level = openparentAcc.Level + 1;
                            OpenAc.AcNumber = HelperAcc.AccNumGenerated(openparentAcc);

                            OpenAc.AccSecurityLevel = 1;   //default
                            DB.ACC_Accounts.InsertOnSubmit(OpenAc);
                            DB.SubmitChanges();
                            lkp_PeriodicOpenInvAcc.EditValue = OpenAc.AccountId;

                            ACC_Account CloseAc = new ACC_Account();
                            CloseAc.AcNameAr = (Shared.IsEnglish ? ResEn.CloseInventory : ResAr.CloseInventory) + " - " + txtStoreNameAr.Text.Trim();//نهاية المده 
                            CloseAc.AcNameEn = (Shared.IsEnglish ? ResAr.CloseInventory : ResEn.CloseInventory) + " - " + txtStoreNameEn.Text.Trim();
                            CloseAc.AcType = true; //credit                    
                            CloseAc.AllowChild = false;
                            CloseAc.AllowEdit = true;

                            var closeparentAcc = DB.ACC_Accounts.Where(x => x.AccountId == Shared.st_Store.CloseInventoryAcc.Value).FirstOrDefault();
                            CloseAc.ParentActId = closeparentAcc.AccountId;
                            CloseAc.Level = closeparentAcc.Level + 1;
                            CloseAc.AcNumber = HelperAcc.AccNumGenerated(closeparentAcc);

                            CloseAc.AccSecurityLevel = 1;   //default

                            DB.ACC_Accounts.InsertOnSubmit(CloseAc);
                            DB.SubmitChanges();
                            lkp_PeriodicCloseInvAcc.EditValue = CloseAc.AccountId;

                            lkp_PeriodicSalesDiscAcc.EditValue = Shared.st_Store.SalesDiscountAcc;
                            lkp_PeriodicPrDiscAcc.EditValue = Shared.st_Store.PurchaseDiscountAcc;
                            BindAccounts();
                            #endregion
                        }

                        stor.PurchaseAccount = Convert.ToInt32(lkp_PeriodicPurchaseAcc.EditValue);
                        stor.PurchaseReturnAccount = Convert.ToInt32(lkp_PeriodicPrReturnAcc.EditValue);
                        stor.SellAccount = Convert.ToInt32(lkp_PeriodicSalesAcc.EditValue);
                        stor.SellReturnAccount = Convert.ToInt32(lkp_PeriodicSalesReturnAcc.EditValue);
                        stor.OpenInventoryAccount = Convert.ToInt32(lkp_PeriodicOpenInvAcc.EditValue);
                        stor.CloseInventoryAccount = Convert.ToInt32(lkp_PeriodicCloseInvAcc.EditValue);
                        stor.SalesDiscountAcc = Convert.ToInt32(lkp_PeriodicSalesDiscAcc.EditValue);
                        stor.PurchaseDiscountAcc = Convert.ToInt32(lkp_PeriodicPrDiscAcc.EditValue);
                    }
                    else if (Shared.StockIsPeriodic && lkpStore.EditValue.ToString() != "0")
                    {
                        #region Integrate with parent accounts
                        int parent = Convert.ToInt32(lkpStore.EditValue);
                        var parentStore = DB.IC_Stores.Where(s => s.StoreId == parent).First();

                        stor.CloseInventoryAccount = parentStore.CloseInventoryAccount;
                        stor.OpenInventoryAccount = parentStore.OpenInventoryAccount;
                        stor.SellReturnAccount = parentStore.SellReturnAccount;
                        stor.SellAccount = parentStore.SellAccount;
                        stor.PurchaseReturnAccount = parentStore.PurchaseReturnAccount;
                        stor.PurchaseAccount = parentStore.PurchaseAccount;
                        stor.PurchaseDiscountAcc = parentStore.PurchaseDiscountAcc;
                        stor.SalesDiscountAcc = parentStore.SalesDiscountAcc;


                        lkp_PeriodicPurchaseAcc.EditValue = stor.PurchaseAccount;
                        lkp_PeriodicPrReturnAcc.EditValue = stor.PurchaseReturnAccount;
                        lkp_PeriodicSalesAcc.EditValue = stor.SellAccount;
                        lkp_PeriodicSalesReturnAcc.EditValue = stor.SellReturnAccount;
                        lkp_PeriodicOpenInvAcc.EditValue = stor.OpenInventoryAccount;
                        lkp_PeriodicCloseInvAcc.EditValue = stor.CloseInventoryAccount;
                        lkp_PeriodicSalesDiscAcc.EditValue = stor.SalesDiscountAcc;
                        lkp_PeriodicPrDiscAcc.EditValue = stor.PurchaseDiscountAcc;
                        #endregion
                    }
                    else if (Shared.StockIsPeriodic == false)
                    {
                        stor.SellAccount = Convert.ToInt32(lkp_PerPetualSalesAcc.EditValue);
                        stor.SellReturnAccount = Convert.ToInt32(lkp_PerPetualSalesReturnAcc.EditValue);
                        stor.CostOfSoldGoodsAcc = Convert.ToInt32(lkp_PerPetualCostOfSoldGoods.EditValue);
                        stor.PurchaseAccount = Convert.ToInt32(lkp_PerPetualInventoryAcc.EditValue);
                        stor.PurchaseReturnAccount = Convert.ToInt32(lkp_PerPetualInventoryAcc.EditValue);
                        stor.PurchaseDiscountAcc = Convert.ToInt32(lkp_PerPetualPurchaseDiscountAcc.EditValue);
                        stor.SalesDiscountAcc = Convert.ToInt32(lkp_PerPetualSalesDiscount.EditValue);
                    }
                }
                else
                {
                    stor.PurchaseAccount =
                    stor.PurchaseReturnAccount =
                    stor.SellAccount =
                    stor.SellReturnAccount =
                    stor.OpenInventoryAccount =
                    stor.CloseInventoryAccount = 0;

                    stor.CostOfSoldGoodsAcc =
                    stor.SalesDiscountAcc =
                    stor.PurchaseDiscountAcc = 0;
                }

                #endregion
            }
            stor.IsStopped = IsStopped.Checked;
            stor.Address = txtAddress.Text;
            stor.StoreCode = txt_StoreCode.Text.Trim();
            stor.StoreNameAr = txtStoreNameAr.Text;
            stor.StoreNameEn = txtStoreNameEn.Text;
            stor.Tel = txtTel.Text;
            stor.Mobile = Mobile.Text;
            stor.ManagerName = txt_Manager.Text;

            if (lkpPriceLevel.EditValue != null)
                stor.pricelistId = Convert.ToInt32(lkpPriceLevel.EditValue);
            else
                stor.pricelistId = null;

            if (lkpStore.EditValue.ToString() == "0")
                stor.ParentId = null;
            else
                stor.ParentId = Convert.ToInt32(lkpStore.EditValue);

            stor.CostMethod = Convert.ToByte(cb_CostMethod.EditValue);

            if (action == FormAction.Add)
            {
                DB.IC_Stores.InsertOnSubmit(stor);

                MyHelper.UpdateST_UserLog(DB, txt_StoreCode.Text, txtStoreNameAr.Text,
                     (int)FormAction.Add, (int)FormsNames.Store);
            }
            else if (action == FormAction.Edit)
            {
                //#region Accounts
                //if (!Shared.ItemsPostingAvailable)
                //{
                //    if (Shared.StockIsPeriodic)
                //    {
                //        #region update accounts
                //        if (lkpStore.EditValue.ToString() == "0")
                //        {
                //            //var PurAc = (from i in DB.ACC_Accounts
                //            //             where i.AccountId == Convert.ToInt32(lkp_PeriodicPurchaseAcc.EditValue)
                //            //             select i).SingleOrDefault();
                //            //PurAc.AcNameAr = (Shared.IsEnglish ? ResEn.Purchases : ResAr.Purchases) + " - " + txtStoreNameAr.Text.Trim();//مشتريات
                //            //PurAc.AcNameEn = (Shared.IsEnglish ? ResAr.Purchases : ResEn.Purchases) + " - " + txtStoreNameEn.Text.Trim();
                //            //if (lkpCostCenter.EditValue != null)
                //            //    PurAc.CostCenter = false;//optional

                //            //var PurRetAc = (from i in DB.ACC_Accounts
                //            //                where i.AccountId == Convert.ToInt32(lkp_PeriodicPrReturnAcc.EditValue)
                //            //                select i).SingleOrDefault();
                //            //PurRetAc.AcNameAr = (Shared.IsEnglish ? ResEn.PurchasesReturn : ResAr.PurchasesReturn) + " - " + txtStoreNameAr.Text.Trim();//مردود مشتريات 
                //            //PurRetAc.AcNameEn = (Shared.IsEnglish ? ResAr.PurchasesReturn : ResEn.PurchasesReturn) + " - " + txtStoreNameEn.Text.Trim();
                //            //if (lkpCostCenter.EditValue != null)
                //            //    PurRetAc.CostCenter = false;//optional

                //            //var SellAc = (from i in DB.ACC_Accounts
                //            //              where i.AccountId == Convert.ToInt32(lkp_PeriodicSalesAcc.EditValue)
                //            //              select i).SingleOrDefault();
                //            //SellAc.AcNameAr = (Shared.IsEnglish ? ResEn.Sales : ResAr.Sales) + " - " + txtStoreNameAr.Text.Trim();//مبيعات
                //            //SellAc.AcNameEn = (Shared.IsEnglish ? ResAr.Sales : ResEn.Sales) + " - " + txtStoreNameEn.Text.Trim();
                //            //if (lkpCostCenter.EditValue != null)
                //            //    SellAc.CostCenter = false;//optional

                //            //var SellRetAc = (from i in DB.ACC_Accounts
                //            //                 where i.AccountId == Convert.ToInt32(lkp_PeriodicSalesReturnAcc.EditValue)
                //            //                 select i).SingleOrDefault();
                //            //SellRetAc.AcNameAr = (Shared.IsEnglish ? ResEn.SalesReturn : ResAr.SalesReturn) + " - " + txtStoreNameAr.Text.Trim();//مردود مبيعات
                //            //SellRetAc.AcNameEn = (Shared.IsEnglish ? ResAr.SalesReturn : ResEn.SalesReturn) + " - " + txtStoreNameEn.Text.Trim();
                //            //if (lkpCostCenter.EditValue != null)
                //            //    SellRetAc.CostCenter = false;//optional

                //            //var OpenAc = (from i in DB.ACC_Accounts
                //            //              where i.AccountId == Convert.ToInt32(lkp_PeriodicOpenInvAcc.EditValue)
                //            //              select i).SingleOrDefault();
                //            //OpenAc.AcNameAr = (Shared.IsEnglish ? ResEn.OpenInventory : ResAr.OpenInventory) + " - " + txtStoreNameAr.Text.Trim();//اول المده 
                //            //OpenAc.AcNameEn = (Shared.IsEnglish ? ResAr.OpenInventory : ResEn.OpenInventory) + " - " + txtStoreNameEn.Text.Trim();
                //            //if (lkpCostCenter.EditValue != null)
                //            //    OpenAc.CostCenter = false;//optional

                //            //var CloseAc = (from i in DB.ACC_Accounts
                //            //               where i.AccountId == Convert.ToInt32(lkp_PeriodicCloseInvAcc.EditValue)
                //            //               select i).SingleOrDefault();
                //            //CloseAc.AcNameAr = (Shared.IsEnglish ? ResEn.CloseInventory : ResAr.CloseInventory) + " - " + txtStoreNameAr.Text.Trim();//نهاية المده 
                //            //CloseAc.AcNameEn = (Shared.IsEnglish ? ResAr.CloseInventory : ResEn.CloseInventory) + " - " + txtStoreNameEn.Text.Trim();
                //            //if (lkpCostCenter.EditValue != null)
                //            //    CloseAc.CostCenter = false;//optional
                //        }
                //        #endregion

                //        stor.PurchaseAccount = Convert.ToInt32(lkp_PeriodicPurchaseAcc.EditValue);
                //        stor.PurchaseReturnAccount = Convert.ToInt32(lkp_PeriodicPrReturnAcc.EditValue);
                //        stor.SellAccount = Convert.ToInt32(lkp_PeriodicSalesAcc.EditValue);
                //        stor.SellReturnAccount = Convert.ToInt32(lkp_PeriodicSalesReturnAcc.EditValue);
                //        stor.OpenInventoryAccount = Convert.ToInt32(lkp_PeriodicOpenInvAcc.EditValue);
                //        stor.CloseInventoryAccount = Convert.ToInt32(lkp_PeriodicCloseInvAcc.EditValue);
                //        stor.SalesDiscountAcc = Convert.ToInt32(lkp_PeriodicSalesDiscAcc.EditValue);
                //        stor.PurchaseDiscountAcc = Convert.ToInt32(lkp_PeriodicPrDiscAcc.EditValue);
                //    }
                //    else if (Shared.StockIsPeriodic == false)
                //    {
                //        stor.SellAccount = Convert.ToInt32(lkp_PerPetualSalesAcc.EditValue);
                //        stor.SellReturnAccount = Convert.ToInt32(lkp_PerPetualSalesReturnAcc.EditValue);
                //        stor.CostOfSoldGoodsAcc = Convert.ToInt32(lkp_PerPetualCostOfSoldGoods.EditValue);
                //        stor.PurchaseAccount = Convert.ToInt32(lkp_PerPetualInventoryAcc.EditValue);
                //        stor.PurchaseReturnAccount = Convert.ToInt32(lkp_PerPetualInventoryAcc.EditValue);
                //        stor.PurchaseDiscountAcc = Convert.ToInt32(lkp_PerPetualPurchaseDiscountAcc.EditValue);
                //        stor.SalesDiscountAcc = Convert.ToInt32(lkp_PerPetualSalesDiscount.EditValue);

                //        //updates cost center for accounts
                //    }
                //}
                //else
                //{
                //    stor.PurchaseAccount =
                //    stor.PurchaseReturnAccount =
                //    stor.SellAccount =
                //    stor.SellReturnAccount =
                //    stor.OpenInventoryAccount =
                //    stor.CloseInventoryAccount = 0;

                //    stor.CostOfSoldGoodsAcc =
                //    stor.SalesDiscountAcc =
                //    stor.PurchaseDiscountAcc = 0;
                //}
                //#endregion

                MyHelper.UpdateST_UserLog(DB, txt_StoreCode.Text, txtStoreNameAr.Text,
                    (int)FormAction.Edit, (int)FormsNames.Store);
            }

            stor.CostCenter = (int?)null;// Convert.ToInt32(lkpCostCenter.EditValue) == 0 ? (int?)null : Convert.ToInt32(lkpCostCenter.EditValue);

            DB.SubmitChanges();

            XtraMessageBox.Show(
                Shared.IsEnglish == true ? ResICEn.MsgSave : ResICAr.MsgSave//"تم الحفظ بنجاح"                
                , "", MessageBoxButtons.OK, MessageBoxIcon.Information);

            action = FormAction.Edit;
            StoreId = stor.StoreId;
            DoValidate();
            DataModified = false;
            GetFirstAndLast();
        }


        void GetFirstAndLast()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            firstStoreId = DB.IC_Stores.Select(d => d.StoreId).FirstOrDefault();
            lastStoreId = DB.IC_Stores.Select(d => d.StoreId).ToList().DefaultIfEmpty(0).Max();
        }

        void Reset()
        {
            IsStopped.Checked = false;
            txtStoreNameAr.Focus();
            txt_StoreCode.Text =
                txtStoreNameAr.Text =
                txtStoreNameEn.Text =
                txtAddress.Text =
                Mobile.Text=
            txtTel.Text = txt_Manager.Text = string.Empty;
            lkpStore.EditValue = 0;

            DataModified = false;

            cb_CostMethod.EditValue = (byte)(2);
            lkp_PerPetualSalesAcc.EditValue = lkp_PerPetualSalesReturnAcc.EditValue =
            lkp_PerPetualCostOfSoldGoods.EditValue = lkp_PerPetualInventoryAcc.EditValue =
                lkp_PerPetualPurchaseDiscountAcc.EditValue = lkp_PerPetualSalesDiscount.EditValue = null;
        }

        DialogResult ChangesMade()
        {

            if (StoreId > 0 && DataModified)
            {
                DialogResult r = XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgDataModified : ResICAr.MsgDataModified//"لقد قمت بعمل بعض التغييرات, هل تريد الحفظ أولا "                    
                    , "", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                if (r == DialogResult.Yes)
                {
                    if (!ValidData())
                        return DialogResult.Cancel;

                    SaveData();
                    return r;
                }
                else if (r == DialogResult.No)
                {
                    // no thing made, continue closing or do next or do previous
                    return r;
                }
                else if (r == DialogResult.Cancel)
                {
                    //cancel form action
                    return r;
                }
            }
            return DialogResult.No;
        }

        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                prvlg = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.Store).FirstOrDefault();

                if (!prvlg.CanDel)
                    barBtnDelete.Enabled = false;
                if (!prvlg.CanAdd)
                    barBtnNew.Enabled = false;
            }
        }

        private void barBtnHelp_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "قائمة المخازن");
        }

        private void lookUpEdit2_EditValueChanged(object sender, EventArgs e)
        {

        }

        private void labelControl10_Click(object sender, EventArgs e)
        {

        }

        private void BranchAddress_EditValueChanged(object sender, EventArgs e)
        {

        }

        private void chk_autoCreateAccs_CheckedChanged(object sender, EventArgs e)
        {
            if (chk_autoCreateAccs.Checked)
            {
                //check Marchendaising Accounts for periodic stock
                if (Shared.StockIsPeriodic && (Shared.st_Store.PurchasesAcc.HasValue == false ||
                    Shared.st_Store.PurchasesReturnAcc.HasValue == false ||
                    Shared.st_Store.SalesAcc.HasValue == false ||
                    Shared.st_Store.SalesReturnAcc.HasValue == false ||
                    Shared.st_Store.OpenInventoryAcc.HasValue == false ||
                    Shared.st_Store.CloseInventoryAcc.HasValue == false ||
                    Shared.st_Store.PurchaseDiscountAcc.HasValue == false ||
                    Shared.st_Store.SalesDiscountAcc.HasValue == false))
                {
                    XtraMessageBox.Show(
                            Shared.IsEnglish == true ? ResICEn.MsgMerchendaisingAcc : ResICAr.MsgMerchendaisingAcc,
                            Shared.IsEnglish == true ? ResICEn.MsgTWarn : ResICAr.MsgTWarn,
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);

                    chk_autoCreateAccs.Checked = false;
                }

                lkp_PeriodicCloseInvAcc.Enabled =
                lkp_PeriodicOpenInvAcc.Enabled =
                lkp_PeriodicPrDiscAcc.Enabled =
                lkp_PeriodicPrReturnAcc.Enabled =
                lkp_PeriodicPurchaseAcc.Enabled =
                lkp_PeriodicSalesAcc.Enabled =
                lkp_PeriodicSalesDiscAcc.Enabled =
                lkp_PeriodicSalesReturnAcc.Enabled = false;

                lkp_PeriodicCloseInvAcc.EditValue =
                lkp_PeriodicOpenInvAcc.EditValue =
                lkp_PeriodicPrDiscAcc.EditValue =
                lkp_PeriodicPrReturnAcc.EditValue =
                lkp_PeriodicPurchaseAcc.EditValue =
                lkp_PeriodicSalesAcc.EditValue =
                lkp_PeriodicSalesDiscAcc.EditValue =
                lkp_PeriodicSalesReturnAcc.EditValue = null;

            }
            else
            {
                lkp_PeriodicCloseInvAcc.Enabled =
                lkp_PeriodicOpenInvAcc.Enabled =
                lkp_PeriodicPrDiscAcc.Enabled =
                lkp_PeriodicPrReturnAcc.Enabled =
                lkp_PeriodicPurchaseAcc.Enabled =
                lkp_PeriodicSalesAcc.Enabled =
                lkp_PeriodicSalesDiscAcc.Enabled =
                lkp_PeriodicSalesReturnAcc.Enabled = true;

            }
        }

        private void lkpStore_EditValueChanged(object sender, EventArgs e)
        {
            if (lkpStore.EditValue.ToString() == "0")
            {
                chk_autoCreateAccs.Enabled = true;
            }
            else
            {
                chk_autoCreateAccs.Checked = true;
                chk_autoCreateAccs.Enabled = false;
            }
        }
    }
}