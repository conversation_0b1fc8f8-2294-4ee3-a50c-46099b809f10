﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;
using DAL.Res;
using System.Linq;
using DevExpress.XtraPrinting;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Base;

namespace Pharmacy.Forms
{
    public partial class frm_E_ActivityTypes : DevExpress.XtraEditors.XtraForm
    {        
        ERPDataContext DB = new ERPDataContext();

        public frm_E_ActivityTypes()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
        }

        private void frm_E_ActivityTypes_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            var _E_ActivityTypes = DB.E_ActivityTypes.Select(x => x);
            grdUom.DataSource = _E_ActivityTypes;

        }        

        

        private void barBtnSave_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
               
                grdUom.FocusedView.PostEditor();
                grdUom.FocusedView.UpdateCurrentRow();
                DB.SubmitChanges();
                XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResSLEn.MsgSave : ResSLAr.MsgSave, "", MessageBoxButtons.OK, MessageBoxIcon.Information);

            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);                
            }
        }


        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void gridView1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Delete && e.Modifiers == Keys.Control)
            {
                if (MessageBox.Show(
                    Shared.IsEnglish == true ? ResICEn.MsgDelRow : ResICAr.MsgDelRow, //"حذف صف ؟"
                    Shared.IsEnglish == true ? ResICEn.MsgTQues : ResICAr.MsgTQues,
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question) !=
                  DialogResult.Yes)
                    return;

                GridView view = sender as GridView;

                if (view.GetFocusedRowCellValue(col_E_ActivityTypeId) == null)
                    return;

                int pId = Convert.ToInt32(view.GetFocusedRowCellValue(col_E_ActivityTypeId));
                if (DB.IC_Items.Where(x => x.SmallUOM == pId || x.MediumUOM == pId || x.LargeUOM == pId).Count() > 0)
                {
                    MessageBox.Show(
                         Shared.IsEnglish == true ? ResEn.DelEntryDenied : ResAr.DelEntryDenied,
                    Shared.IsEnglish == true ? ResEn.MsgDelRow : ResAr.MsgDelRow,
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }
                else
                    view.DeleteRow(view.FocusedRowHandle);
            }
        }

        private void gvPriority_ValidateRow(object sender, DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs e)
        {
            try
            {
                ColumnView view = sender as ColumnView;
                if (view.GetRowCellValue(e.RowHandle, view.Columns["Code"]).ToString() == string.Empty)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["Code"], "*");
                }

                if (view.GetRowCellValue(e.RowHandle, view.Columns["DescriptionAr"]).ToString() == string.Empty)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["DescriptionAr"], "*");
                }
            }
            catch
            {
                e.Valid = false;
            }
        }

        private void gvPriority_InvalidRowException(object sender, InvalidRowExceptionEventArgs e)
        {
            e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.NoAction;
        }

        private void barBtnHelp_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "تسجيل عميل جديد");
        }

       
    }
}