﻿namespace Pharmacy.Forms
{
    partial class frm_IC_EditItemQty
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_IC_EditItemQty));
            this.labelControl15 = new DevExpress.XtraEditors.LabelControl();
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtn_Help = new DevExpress.XtraBars.BarButtonItem();
            this.barBtn_Save = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.txt_Length = new DevExpress.XtraEditors.SpinEdit();
            this.txt_Width = new DevExpress.XtraEditors.SpinEdit();
            this.txt_Height = new DevExpress.XtraEditors.SpinEdit();
            this.lbl_Length = new DevExpress.XtraEditors.LabelControl();
            this.lbl_Width = new DevExpress.XtraEditors.LabelControl();
            this.lbl_Height = new DevExpress.XtraEditors.LabelControl();
            this.lkpItems = new DevExpress.XtraEditors.GridLookUpEdit();
            
            this.gridLookUpEdit1View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.colItemId = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colItemCode1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colItemCode2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colItemNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colItemNameEn = new DevExpress.XtraGrid.Columns.GridColumn();
            this.lkpStore = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.btnSearch = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.lkpCompany = new DevExpress.XtraEditors.GridLookUpEdit();
            this.gridView2 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn11 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn12 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn13 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn14 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.lkpVendor = new DevExpress.XtraEditors.GridLookUpEdit();
            this.gridView3 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn19 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn20 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn21 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn22 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn23 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.grdEditItemQty = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn9 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn8 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn6 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn7 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repUOM = new DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit();
            this.repositoryItemGridLookUpEdit1View = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.gridColumn15 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn16 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn17 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colBatch = new DevExpress.XtraGrid.Columns.GridColumn();
            this.colExpire = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repDate = new DevExpress.XtraEditors.Repository.RepositoryItemDateEdit();
            this.gridColumn5 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.repSpin = new DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit();
            this.gridColumn4 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn10 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.gridColumn18 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_OldPiecesCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_NewPiecesCount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Length = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Width = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Height = new DevExpress.XtraGrid.Columns.GridColumn();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            this.groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Length.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Width.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Height.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpItems.Properties)).BeginInit();
            
            ((System.ComponentModel.ISupportInitialize)(this.gridLookUpEdit1View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpStore.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpCompany.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpVendor.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdEditItemQty)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repUOM)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repDate)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repDate.VistaTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repSpin)).BeginInit();
            this.SuspendLayout();
            // 
            // labelControl15
            // 
            this.labelControl15.AccessibleDescription = null;
            this.labelControl15.AccessibleName = null;
            resources.ApplyResources(this.labelControl15, "labelControl15");
            this.labelControl15.Name = "labelControl15";
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtn_Save,
            this.barBtn_Help,
            this.barBtnClose});
            this.barManager1.MaxItemId = 26;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(377, 152);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_Help),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_Save),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtn_Help
            // 
            this.barBtn_Help.AccessibleDescription = null;
            this.barBtn_Help.AccessibleName = null;
            this.barBtn_Help.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtn_Help, "barBtn_Help");
            this.barBtn_Help.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtn_Help.Id = 2;
            this.barBtn_Help.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtn_Help.Name = "barBtn_Help";
            this.barBtn_Help.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtn_Help.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Help_ItemClick);
            // 
            // barBtn_Save
            // 
            this.barBtn_Save.AccessibleDescription = null;
            this.barBtn_Save.AccessibleName = null;
            this.barBtn_Save.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            resources.ApplyResources(this.barBtn_Save, "barBtn_Save");
            this.barBtn_Save.Glyph = global::Pharmacy.Properties.Resources.sve;
            this.barBtn_Save.Id = 0;
            this.barBtn_Save.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.S));
            this.barBtn_Save.Name = "barBtn_Save";
            this.barBtn_Save.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtn_Save.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Save_ItemClick);
            // 
            // barBtnClose
            // 
            this.barBtnClose.AccessibleDescription = null;
            this.barBtnClose.AccessibleName = null;
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 25;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtnClose_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            // 
            // barDockControlTop
            // 
            this.barDockControlTop.AccessibleDescription = null;
            this.barDockControlTop.AccessibleName = null;
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Font = null;
            // 
            // barDockControlBottom
            // 
            this.barDockControlBottom.AccessibleDescription = null;
            this.barDockControlBottom.AccessibleName = null;
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Font = null;
            // 
            // barDockControlLeft
            // 
            this.barDockControlLeft.AccessibleDescription = null;
            this.barDockControlLeft.AccessibleName = null;
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Font = null;
            // 
            // barDockControlRight
            // 
            this.barDockControlRight.AccessibleDescription = null;
            this.barDockControlRight.AccessibleName = null;
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Font = null;
            // 
            // repositoryItemTextEdit1
            // 
            this.repositoryItemTextEdit1.AccessibleDescription = null;
            this.repositoryItemTextEdit1.AccessibleName = null;
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemTextEdit1.Mask.AutoComplete")));
            this.repositoryItemTextEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.BeepOnError")));
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemTextEdit1.Mask.MaskType")));
            this.repositoryItemTextEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemTextEdit1.Mask.PlaceHolder")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // groupBox1
            // 
            this.groupBox1.AccessibleDescription = null;
            this.groupBox1.AccessibleName = null;
            resources.ApplyResources(this.groupBox1, "groupBox1");
            this.groupBox1.BackgroundImage = null;
            this.groupBox1.Controls.Add(this.txt_Length);
            this.groupBox1.Controls.Add(this.txt_Width);
            this.groupBox1.Controls.Add(this.txt_Height);
            this.groupBox1.Controls.Add(this.lbl_Length);
            this.groupBox1.Controls.Add(this.lbl_Width);
            this.groupBox1.Controls.Add(this.lbl_Height);
            this.groupBox1.Controls.Add(this.lkpItems);
            this.groupBox1.Controls.Add(this.lkpStore);
            this.groupBox1.Controls.Add(this.labelControl1);
            this.groupBox1.Controls.Add(this.btnSearch);
            this.groupBox1.Controls.Add(this.labelControl6);
            this.groupBox1.Controls.Add(this.labelControl5);
            this.groupBox1.Controls.Add(this.labelControl15);
            this.groupBox1.Controls.Add(this.lkpCompany);
            this.groupBox1.Controls.Add(this.lkpVendor);
            this.groupBox1.Font = null;
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.TabStop = false;
            // 
            // txt_Length
            // 
            resources.ApplyResources(this.txt_Length, "txt_Length");
            this.txt_Length.BackgroundImage = null;
            this.txt_Length.EnterMoveNextControl = true;
            this.txt_Length.MenuManager = this.barManager1;
            this.txt_Length.Name = "txt_Length";
            this.txt_Length.Properties.AccessibleDescription = null;
            this.txt_Length.Properties.AccessibleName = null;
            this.txt_Length.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Length.Properties.AutoHeight")));
            this.txt_Length.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txt_Length.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_Length.Properties.Mask.AutoComplete")));
            this.txt_Length.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_Length.Properties.Mask.BeepOnError")));
            this.txt_Length.Properties.Mask.EditMask = resources.GetString("txt_Length.Properties.Mask.EditMask");
            this.txt_Length.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Length.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Length.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Length.Properties.Mask.MaskType")));
            this.txt_Length.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_Length.Properties.Mask.PlaceHolder")));
            this.txt_Length.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Length.Properties.Mask.SaveLiteral")));
            this.txt_Length.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Length.Properties.Mask.ShowPlaceHolders")));
            this.txt_Length.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_Length.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_Length.Properties.NullValuePrompt = resources.GetString("txt_Length.Properties.NullValuePrompt");
            this.txt_Length.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_Length.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // txt_Width
            // 
            resources.ApplyResources(this.txt_Width, "txt_Width");
            this.txt_Width.BackgroundImage = null;
            this.txt_Width.EnterMoveNextControl = true;
            this.txt_Width.MenuManager = this.barManager1;
            this.txt_Width.Name = "txt_Width";
            this.txt_Width.Properties.AccessibleDescription = null;
            this.txt_Width.Properties.AccessibleName = null;
            this.txt_Width.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Width.Properties.AutoHeight")));
            this.txt_Width.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txt_Width.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_Width.Properties.Mask.AutoComplete")));
            this.txt_Width.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_Width.Properties.Mask.BeepOnError")));
            this.txt_Width.Properties.Mask.EditMask = resources.GetString("txt_Width.Properties.Mask.EditMask");
            this.txt_Width.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Width.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Width.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Width.Properties.Mask.MaskType")));
            this.txt_Width.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_Width.Properties.Mask.PlaceHolder")));
            this.txt_Width.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Width.Properties.Mask.SaveLiteral")));
            this.txt_Width.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Width.Properties.Mask.ShowPlaceHolders")));
            this.txt_Width.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_Width.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_Width.Properties.NullValuePrompt = resources.GetString("txt_Width.Properties.NullValuePrompt");
            this.txt_Width.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_Width.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // txt_Height
            // 
            resources.ApplyResources(this.txt_Height, "txt_Height");
            this.txt_Height.BackgroundImage = null;
            this.txt_Height.EnterMoveNextControl = true;
            this.txt_Height.MenuManager = this.barManager1;
            this.txt_Height.Name = "txt_Height";
            this.txt_Height.Properties.AccessibleDescription = null;
            this.txt_Height.Properties.AccessibleName = null;
            this.txt_Height.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Height.Properties.AutoHeight")));
            this.txt_Height.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txt_Height.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_Height.Properties.Mask.AutoComplete")));
            this.txt_Height.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_Height.Properties.Mask.BeepOnError")));
            this.txt_Height.Properties.Mask.EditMask = resources.GetString("txt_Height.Properties.Mask.EditMask");
            this.txt_Height.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Height.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Height.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Height.Properties.Mask.MaskType")));
            this.txt_Height.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_Height.Properties.Mask.PlaceHolder")));
            this.txt_Height.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Height.Properties.Mask.SaveLiteral")));
            this.txt_Height.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Height.Properties.Mask.ShowPlaceHolders")));
            this.txt_Height.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_Height.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_Height.Properties.NullValuePrompt = resources.GetString("txt_Height.Properties.NullValuePrompt");
            this.txt_Height.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_Height.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // lbl_Length
            // 
            this.lbl_Length.AccessibleDescription = null;
            this.lbl_Length.AccessibleName = null;
            resources.ApplyResources(this.lbl_Length, "lbl_Length");
            this.lbl_Length.Name = "lbl_Length";
            // 
            // lbl_Width
            // 
            this.lbl_Width.AccessibleDescription = null;
            this.lbl_Width.AccessibleName = null;
            resources.ApplyResources(this.lbl_Width, "lbl_Width");
            this.lbl_Width.Name = "lbl_Width";
            // 
            // lbl_Height
            // 
            this.lbl_Height.AccessibleDescription = null;
            this.lbl_Height.AccessibleName = null;
            resources.ApplyResources(this.lbl_Height, "lbl_Height");
            this.lbl_Height.Name = "lbl_Height";
            // 
            // lkpItems
            // 
            resources.ApplyResources(this.lkpItems, "lkpItems");
            this.lkpItems.BackgroundImage = null;
            this.lkpItems.EditValue = null;
            this.lkpItems.EnterMoveNextControl = true;
            this.lkpItems.MenuManager = this.barManager1;
            this.lkpItems.Name = "lkpItems";
            this.lkpItems.Properties.AccessibleDescription = null;
            this.lkpItems.Properties.AccessibleName = null;
            this.lkpItems.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkpItems.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpItems.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpItems.Properties.AutoHeight = ((bool)(resources.GetObject("lkpItems.Properties.AutoHeight")));
            this.lkpItems.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkpItems.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpItems.Properties.Buttons"))))});            
            this.lkpItems.Properties.DisplayMember = "ItemNameAr";
            this.lkpItems.Properties.ImmediatePopup = true;
            this.lkpItems.Properties.NullText = resources.GetString("lkpItems.Properties.NullText");
            this.lkpItems.Properties.NullValuePrompt = resources.GetString("lkpItems.Properties.NullValuePrompt");
            this.lkpItems.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkpItems.Properties.NullValuePromptShowForEmptyValue")));
            this.lkpItems.Properties.PopupFilterMode = DevExpress.XtraEditors.PopupFilterMode.Contains;
            
            this.lkpItems.Properties.ValueMember = "ItemId";
            this.lkpItems.Properties.View = this.gridLookUpEdit1View;            
            // 
            // gridLookUpEdit1View
            // 
            resources.ApplyResources(this.gridLookUpEdit1View, "gridLookUpEdit1View");
            this.gridLookUpEdit1View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.colItemId,
            this.colItemCode1,
            this.colItemCode2,
            this.colItemNameAr,
            this.colItemNameEn});
            this.gridLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridLookUpEdit1View.Name = "gridLookUpEdit1View";
            this.gridLookUpEdit1View.OptionsBehavior.Editable = false;
            this.gridLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridLookUpEdit1View.OptionsView.EnableAppearanceEvenRow = true;
            this.gridLookUpEdit1View.OptionsView.EnableAppearanceOddRow = true;
            this.gridLookUpEdit1View.OptionsView.ShowAutoFilterRow = true;
            this.gridLookUpEdit1View.OptionsView.ShowDetailButtons = false;
            this.gridLookUpEdit1View.OptionsView.ShowGroupExpandCollapseButtons = false;
            this.gridLookUpEdit1View.OptionsView.ShowGroupPanel = false;
            this.gridLookUpEdit1View.OptionsView.ShowIndicator = false;
            // 
            // colItemId
            // 
            resources.ApplyResources(this.colItemId, "colItemId");
            this.colItemId.FieldName = "ItemId";
            this.colItemId.Name = "colItemId";
            // 
            // colItemCode1
            // 
            resources.ApplyResources(this.colItemCode1, "colItemCode1");
            this.colItemCode1.FieldName = "ItemCode1";
            this.colItemCode1.Name = "colItemCode1";
            this.colItemCode1.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            // 
            // colItemCode2
            // 
            resources.ApplyResources(this.colItemCode2, "colItemCode2");
            this.colItemCode2.FieldName = "ItemCode2";
            this.colItemCode2.Name = "colItemCode2";
            this.colItemCode2.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // colItemNameAr
            // 
            resources.ApplyResources(this.colItemNameAr, "colItemNameAr");
            this.colItemNameAr.FieldName = "ItemNameAr";
            this.colItemNameAr.Name = "colItemNameAr";
            this.colItemNameAr.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // colItemNameEn
            // 
            resources.ApplyResources(this.colItemNameEn, "colItemNameEn");
            this.colItemNameEn.FieldName = "ItemNameEn";
            this.colItemNameEn.Name = "colItemNameEn";
            this.colItemNameEn.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            // 
            // lkpStore
            // 
            resources.ApplyResources(this.lkpStore, "lkpStore");
            this.lkpStore.BackgroundImage = null;
            this.lkpStore.EditValue = null;
            this.lkpStore.EnterMoveNextControl = true;
            this.lkpStore.MenuManager = this.barManager1;
            this.lkpStore.Name = "lkpStore";
            this.lkpStore.Properties.AccessibleDescription = null;
            this.lkpStore.Properties.AccessibleName = null;
            this.lkpStore.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkpStore.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpStore.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpStore.Properties.AutoHeight = ((bool)(resources.GetObject("lkpStore.Properties.AutoHeight")));
            this.lkpStore.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpStore.Properties.Buttons"))))});
            this.lkpStore.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns"), resources.GetString("lkpStore.Properties.Columns1"), ((int)(resources.GetObject("lkpStore.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns3"))), resources.GetString("lkpStore.Properties.Columns4"), ((bool)(resources.GetObject("lkpStore.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns7"), resources.GetString("lkpStore.Properties.Columns8"), ((int)(resources.GetObject("lkpStore.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns10"))), resources.GetString("lkpStore.Properties.Columns11"), ((bool)(resources.GetObject("lkpStore.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns13"))))});
            this.lkpStore.Properties.NullText = resources.GetString("lkpStore.Properties.NullText");
            this.lkpStore.Properties.NullValuePrompt = resources.GetString("lkpStore.Properties.NullValuePrompt");
            this.lkpStore.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkpStore.Properties.NullValuePromptShowForEmptyValue")));
            this.lkpStore.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            // 
            // labelControl1
            // 
            this.labelControl1.AccessibleDescription = null;
            this.labelControl1.AccessibleName = null;
            resources.ApplyResources(this.labelControl1, "labelControl1");
            this.labelControl1.Name = "labelControl1";
            // 
            // btnSearch
            // 
            this.btnSearch.AccessibleDescription = null;
            this.btnSearch.AccessibleName = null;
            resources.ApplyResources(this.btnSearch, "btnSearch");
            this.btnSearch.BackgroundImage = null;
            this.btnSearch.Name = "btnSearch";
            this.btnSearch.Click += new System.EventHandler(this.btnSearch_Click);
            // 
            // labelControl6
            // 
            this.labelControl6.AccessibleDescription = null;
            this.labelControl6.AccessibleName = null;
            resources.ApplyResources(this.labelControl6, "labelControl6");
            this.labelControl6.Name = "labelControl6";
            // 
            // labelControl5
            // 
            this.labelControl5.AccessibleDescription = null;
            this.labelControl5.AccessibleName = null;
            resources.ApplyResources(this.labelControl5, "labelControl5");
            this.labelControl5.Name = "labelControl5";
            // 
            // lkpCompany
            // 
            resources.ApplyResources(this.lkpCompany, "lkpCompany");
            this.lkpCompany.BackgroundImage = null;
            this.lkpCompany.EditValue = null;
            this.lkpCompany.EnterMoveNextControl = true;
            this.lkpCompany.MenuManager = this.barManager1;
            this.lkpCompany.Name = "lkpCompany";
            this.lkpCompany.Properties.AccessibleDescription = null;
            this.lkpCompany.Properties.AccessibleName = null;
            this.lkpCompany.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkpCompany.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpCompany.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpCompany.Properties.AutoHeight = ((bool)(resources.GetObject("lkpCompany.Properties.AutoHeight")));
            this.lkpCompany.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkpCompany.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpCompany.Properties.Buttons"))))});
            this.lkpCompany.Properties.ImmediatePopup = true;
            this.lkpCompany.Properties.NullText = resources.GetString("lkpCompany.Properties.NullText");
            this.lkpCompany.Properties.NullValuePrompt = resources.GetString("lkpCompany.Properties.NullValuePrompt");
            this.lkpCompany.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkpCompany.Properties.NullValuePromptShowForEmptyValue")));
            this.lkpCompany.Properties.PopupFilterMode = DevExpress.XtraEditors.PopupFilterMode.Contains;
            this.lkpCompany.Properties.View = this.gridView2;
            // 
            // gridView2
            // 
            this.gridView2.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView2.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView2.Appearance.Row.Options.UseTextOptions = true;
            this.gridView2.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.gridView2, "gridView2");
            this.gridView2.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn11,
            this.gridColumn12,
            this.gridColumn13,
            this.gridColumn14});
            this.gridView2.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView2.Name = "gridView2";
            this.gridView2.OptionsBehavior.Editable = false;
            this.gridView2.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView2.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView2.OptionsView.EnableAppearanceOddRow = true;
            this.gridView2.OptionsView.RowAutoHeight = true;
            this.gridView2.OptionsView.ShowAutoFilterRow = true;
            this.gridView2.OptionsView.ShowDetailButtons = false;
            this.gridView2.OptionsView.ShowGroupPanel = false;
            this.gridView2.OptionsView.ShowIndicator = false;
            // 
            // gridColumn11
            // 
            resources.ApplyResources(this.gridColumn11, "gridColumn11");
            this.gridColumn11.FieldName = "CompanyNameEn";
            this.gridColumn11.Name = "gridColumn11";
            // 
            // gridColumn12
            // 
            resources.ApplyResources(this.gridColumn12, "gridColumn12");
            this.gridColumn12.FieldName = "CompanyNameAr";
            this.gridColumn12.Name = "gridColumn12";
            // 
            // gridColumn13
            // 
            resources.ApplyResources(this.gridColumn13, "gridColumn13");
            this.gridColumn13.FieldName = "CompanyCode";
            this.gridColumn13.Name = "gridColumn13";
            // 
            // gridColumn14
            // 
            resources.ApplyResources(this.gridColumn14, "gridColumn14");
            this.gridColumn14.FieldName = "CompanyId";
            this.gridColumn14.Name = "gridColumn14";
            // 
            // lkpVendor
            // 
            resources.ApplyResources(this.lkpVendor, "lkpVendor");
            this.lkpVendor.BackgroundImage = null;
            this.lkpVendor.EditValue = null;
            this.lkpVendor.EnterMoveNextControl = true;
            this.lkpVendor.MenuManager = this.barManager1;
            this.lkpVendor.Name = "lkpVendor";
            this.lkpVendor.Properties.AccessibleDescription = null;
            this.lkpVendor.Properties.AccessibleName = null;
            this.lkpVendor.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.lkpVendor.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpVendor.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpVendor.Properties.AutoHeight = ((bool)(resources.GetObject("lkpVendor.Properties.AutoHeight")));
            this.lkpVendor.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkpVendor.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpVendor.Properties.Buttons"))))});
            this.lkpVendor.Properties.ImmediatePopup = true;
            this.lkpVendor.Properties.NullText = resources.GetString("lkpVendor.Properties.NullText");
            this.lkpVendor.Properties.NullValuePrompt = resources.GetString("lkpVendor.Properties.NullValuePrompt");
            this.lkpVendor.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkpVendor.Properties.NullValuePromptShowForEmptyValue")));
            this.lkpVendor.Properties.PopupFilterMode = DevExpress.XtraEditors.PopupFilterMode.Contains;
            this.lkpVendor.Properties.View = this.gridView3;
            // 
            // gridView3
            // 
            this.gridView3.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView3.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView3.Appearance.Row.Options.UseTextOptions = true;
            this.gridView3.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.gridView3, "gridView3");
            this.gridView3.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn19,
            this.gridColumn20,
            this.gridColumn21,
            this.gridColumn22,
            this.gridColumn23});
            this.gridView3.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.gridView3.Name = "gridView3";
            this.gridView3.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gridView3.OptionsView.EnableAppearanceEvenRow = true;
            this.gridView3.OptionsView.EnableAppearanceOddRow = true;
            this.gridView3.OptionsView.ShowAutoFilterRow = true;
            this.gridView3.OptionsView.ShowGroupPanel = false;
            this.gridView3.OptionsView.ShowIndicator = false;
            // 
            // gridColumn19
            // 
            resources.ApplyResources(this.gridColumn19, "gridColumn19");
            this.gridColumn19.FieldName = "VendorId";
            this.gridColumn19.Name = "gridColumn19";
            // 
            // gridColumn20
            // 
            resources.ApplyResources(this.gridColumn20, "gridColumn20");
            this.gridColumn20.FieldName = "VenCode";
            this.gridColumn20.Name = "gridColumn20";
            // 
            // gridColumn21
            // 
            resources.ApplyResources(this.gridColumn21, "gridColumn21");
            this.gridColumn21.FieldName = "VenNameAr";
            this.gridColumn21.Name = "gridColumn21";
            // 
            // gridColumn22
            // 
            resources.ApplyResources(this.gridColumn22, "gridColumn22");
            this.gridColumn22.FieldName = "VenNameEn";
            this.gridColumn22.Name = "gridColumn22";
            // 
            // gridColumn23
            // 
            resources.ApplyResources(this.gridColumn23, "gridColumn23");
            this.gridColumn23.FieldName = "Credit";
            this.gridColumn23.Name = "gridColumn23";
            // 
            // grdEditItemQty
            // 
            this.grdEditItemQty.AccessibleDescription = null;
            this.grdEditItemQty.AccessibleName = null;
            resources.ApplyResources(this.grdEditItemQty, "grdEditItemQty");
            this.grdEditItemQty.BackgroundImage = null;
            this.grdEditItemQty.EmbeddedNavigator.AccessibleDescription = null;
            this.grdEditItemQty.EmbeddedNavigator.AccessibleName = null;
            this.grdEditItemQty.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grdEditItemQty.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grdEditItemQty.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grdEditItemQty.EmbeddedNavigator.Anchor")));
            this.grdEditItemQty.EmbeddedNavigator.BackgroundImage = null;
            this.grdEditItemQty.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grdEditItemQty.EmbeddedNavigator.BackgroundImageLayout")));
            this.grdEditItemQty.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grdEditItemQty.EmbeddedNavigator.ImeMode")));
            this.grdEditItemQty.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grdEditItemQty.EmbeddedNavigator.TextLocation")));
            this.grdEditItemQty.EmbeddedNavigator.ToolTip = resources.GetString("grdEditItemQty.EmbeddedNavigator.ToolTip");
            this.grdEditItemQty.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grdEditItemQty.EmbeddedNavigator.ToolTipIconType")));
            this.grdEditItemQty.EmbeddedNavigator.ToolTipTitle = resources.GetString("grdEditItemQty.EmbeddedNavigator.ToolTipTitle");
            this.grdEditItemQty.Font = null;
            this.grdEditItemQty.MainView = this.gridView1;
            this.grdEditItemQty.MenuManager = this.barManager1;
            this.grdEditItemQty.Name = "grdEditItemQty";
            this.grdEditItemQty.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repSpin,
            this.repUOM,
            this.repDate});
            this.grdEditItemQty.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // gridView1
            // 
            this.gridView1.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gridView1.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.gridView1.Appearance.Row.Options.UseTextOptions = true;
            this.gridView1.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            resources.ApplyResources(this.gridView1, "gridView1");
            this.gridView1.ColumnPanelRowHeight = 40;
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn9,
            this.gridColumn8,
            this.gridColumn6,
            this.gridColumn7,
            this.colBatch,
            this.colExpire,
            this.gridColumn5,
            this.gridColumn4,
            this.gridColumn2,
            this.gridColumn1,
            this.gridColumn10,
            this.gridColumn18,
            this.col_OldPiecesCount,
            this.col_NewPiecesCount,
            this.col_Length,
            this.col_Width,
            this.col_Height});
            this.gridView1.GridControl = this.grdEditItemQty;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsBehavior.FocusLeaveOnTab = true;
            this.gridView1.OptionsView.NewItemRowPosition = DevExpress.XtraGrid.Views.Grid.NewItemRowPosition.Bottom;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            this.gridView1.OptionsView.ShowIndicator = false;
            this.gridView1.FocusedRowChanged += new DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventHandler(this.gridView1_FocusedRowChanged);
            this.gridView1.FocusedColumnChanged += new DevExpress.XtraGrid.Views.Base.FocusedColumnChangedEventHandler(this.gridView1_FocusedColumnChanged);
            this.gridView1.CellValueChanged += new DevExpress.XtraGrid.Views.Base.CellValueChangedEventHandler(this.gridView1_CellValueChanged);
            this.gridView1.InvalidRowException += new DevExpress.XtraGrid.Views.Base.InvalidRowExceptionEventHandler(this.gridView1_InvalidRowException);
            this.gridView1.ValidateRow += new DevExpress.XtraGrid.Views.Base.ValidateRowEventHandler(this.gridView1_ValidateRow);
            // 
            // gridColumn9
            // 
            this.gridColumn9.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn9.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn9.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn9.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn9.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn9.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn9.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn9.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn9, "gridColumn9");
            this.gridColumn9.FieldName = "PurchasePrice";
            this.gridColumn9.Name = "gridColumn9";
            this.gridColumn9.OptionsColumn.AllowEdit = false;
            // 
            // gridColumn8
            // 
            this.gridColumn8.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn8.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn8.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn8.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn8.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn8.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn8.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn8.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn8, "gridColumn8");
            this.gridColumn8.FieldName = "SellPrice";
            this.gridColumn8.Name = "gridColumn8";
            this.gridColumn8.OptionsColumn.AllowEdit = false;
            // 
            // gridColumn6
            // 
            this.gridColumn6.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn6.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn6.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn6.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn6.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn6.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn6.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn6.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn6, "gridColumn6");
            this.gridColumn6.FieldName = "Notes";
            this.gridColumn6.Name = "gridColumn6";
            // 
            // gridColumn7
            // 
            this.gridColumn7.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn7.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn7.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn7.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn7.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn7.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn7.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn7.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn7, "gridColumn7");
            this.gridColumn7.ColumnEdit = this.repUOM;
            this.gridColumn7.FieldName = "UOM";
            this.gridColumn7.Name = "gridColumn7";
            // 
            // repUOM
            // 
            this.repUOM.AccessibleDescription = null;
            this.repUOM.AccessibleName = null;
            resources.ApplyResources(this.repUOM, "repUOM");
            this.repUOM.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repUOM.Buttons"))))});
            this.repUOM.Name = "repUOM";
            this.repUOM.View = this.repositoryItemGridLookUpEdit1View;
            // 
            // repositoryItemGridLookUpEdit1View
            // 
            this.repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.repositoryItemGridLookUpEdit1View.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.repositoryItemGridLookUpEdit1View.Appearance.Row.Options.UseTextOptions = true;
            this.repositoryItemGridLookUpEdit1View.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.repositoryItemGridLookUpEdit1View.BestFitMaxRowCount = 10;
            resources.ApplyResources(this.repositoryItemGridLookUpEdit1View, "repositoryItemGridLookUpEdit1View");
            this.repositoryItemGridLookUpEdit1View.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.gridColumn15,
            this.gridColumn16,
            this.gridColumn17});
            this.repositoryItemGridLookUpEdit1View.FocusRectStyle = DevExpress.XtraGrid.Views.Grid.DrawFocusRectStyle.RowFocus;
            this.repositoryItemGridLookUpEdit1View.Name = "repositoryItemGridLookUpEdit1View";
            this.repositoryItemGridLookUpEdit1View.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.EnableAppearanceEvenRow = true;
            this.repositoryItemGridLookUpEdit1View.OptionsView.EnableAppearanceOddRow = true;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowDetailButtons = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowGroupPanel = false;
            this.repositoryItemGridLookUpEdit1View.OptionsView.ShowIndicator = false;
            // 
            // gridColumn15
            // 
            resources.ApplyResources(this.gridColumn15, "gridColumn15");
            this.gridColumn15.FieldName = "Uom";
            this.gridColumn15.Name = "gridColumn15";
            // 
            // gridColumn16
            // 
            resources.ApplyResources(this.gridColumn16, "gridColumn16");
            this.gridColumn16.FieldName = "Factor";
            this.gridColumn16.Name = "gridColumn16";
            // 
            // gridColumn17
            // 
            resources.ApplyResources(this.gridColumn17, "gridColumn17");
            this.gridColumn17.FieldName = "Index";
            this.gridColumn17.Name = "gridColumn17";
            // 
            // colBatch
            // 
            this.colBatch.AppearanceCell.Options.UseTextOptions = true;
            this.colBatch.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.colBatch.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colBatch.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.colBatch.AppearanceHeader.Options.UseTextOptions = true;
            this.colBatch.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.colBatch.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colBatch.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.colBatch, "colBatch");
            this.colBatch.FieldName = "Batch";
            this.colBatch.Name = "colBatch";
            this.colBatch.OptionsColumn.AllowEdit = false;
            // 
            // colExpire
            // 
            this.colExpire.AppearanceCell.Options.UseTextOptions = true;
            this.colExpire.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.colExpire.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colExpire.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.colExpire.AppearanceHeader.Options.UseTextOptions = true;
            this.colExpire.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.colExpire.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.colExpire.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.colExpire, "colExpire");
            this.colExpire.ColumnEdit = this.repDate;
            this.colExpire.DisplayFormat.FormatString = "y";
            this.colExpire.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.colExpire.FieldName = "Expire";
            this.colExpire.Name = "colExpire";
            this.colExpire.OptionsColumn.AllowEdit = false;
            // 
            // repDate
            // 
            this.repDate.AccessibleDescription = null;
            this.repDate.AccessibleName = null;
            resources.ApplyResources(this.repDate, "repDate");
            this.repDate.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("repDate.Buttons"))))});
            this.repDate.DisplayFormat.FormatString = "y";
            this.repDate.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.repDate.EditFormat.FormatString = "y";
            this.repDate.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.repDate.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repDate.Mask.AutoComplete")));
            this.repDate.Mask.BeepOnError = ((bool)(resources.GetObject("repDate.Mask.BeepOnError")));
            this.repDate.Mask.EditMask = resources.GetString("repDate.Mask.EditMask");
            this.repDate.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repDate.Mask.IgnoreMaskBlank")));
            this.repDate.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repDate.Mask.MaskType")));
            this.repDate.Mask.PlaceHolder = ((char)(resources.GetObject("repDate.Mask.PlaceHolder")));
            this.repDate.Mask.SaveLiteral = ((bool)(resources.GetObject("repDate.Mask.SaveLiteral")));
            this.repDate.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repDate.Mask.ShowPlaceHolders")));
            this.repDate.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repDate.Mask.UseMaskAsDisplayFormat")));
            this.repDate.Name = "repDate";
            this.repDate.VistaTimeProperties.AccessibleDescription = null;
            this.repDate.VistaTimeProperties.AccessibleName = null;
            this.repDate.VistaTimeProperties.AutoHeight = ((bool)(resources.GetObject("repDate.VistaTimeProperties.AutoHeight")));
            this.repDate.VistaTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.repDate.VistaTimeProperties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repDate.VistaTimeProperties.Mask.AutoComplete")));
            this.repDate.VistaTimeProperties.Mask.BeepOnError = ((bool)(resources.GetObject("repDate.VistaTimeProperties.Mask.BeepOnError")));
            this.repDate.VistaTimeProperties.Mask.EditMask = resources.GetString("repDate.VistaTimeProperties.Mask.EditMask");
            this.repDate.VistaTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repDate.VistaTimeProperties.Mask.IgnoreMaskBlank")));
            this.repDate.VistaTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repDate.VistaTimeProperties.Mask.MaskType")));
            this.repDate.VistaTimeProperties.Mask.PlaceHolder = ((char)(resources.GetObject("repDate.VistaTimeProperties.Mask.PlaceHolder")));
            this.repDate.VistaTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("repDate.VistaTimeProperties.Mask.SaveLiteral")));
            this.repDate.VistaTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repDate.VistaTimeProperties.Mask.ShowPlaceHolders")));
            this.repDate.VistaTimeProperties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repDate.VistaTimeProperties.Mask.UseMaskAsDisplayFormat")));
            this.repDate.VistaTimeProperties.NullValuePrompt = resources.GetString("repDate.VistaTimeProperties.NullValuePrompt");
            this.repDate.VistaTimeProperties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("repDate.VistaTimeProperties.NullValuePromptShowForEmptyValue")));
            this.repDate.CustomDisplayText += new DevExpress.XtraEditors.Controls.CustomDisplayTextEventHandler(this.rep_ExpireDate_CustomDisplayText);
            // 
            // gridColumn5
            // 
            this.gridColumn5.AppearanceCell.Font = new System.Drawing.Font("Tahoma", 8F, System.Drawing.FontStyle.Bold);
            this.gridColumn5.AppearanceCell.Options.UseFont = true;
            this.gridColumn5.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn5.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn5.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn5.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn5.AppearanceHeader.Font = new System.Drawing.Font("Tahoma", 8F, System.Drawing.FontStyle.Bold);
            this.gridColumn5.AppearanceHeader.Options.UseFont = true;
            this.gridColumn5.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn5.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn5.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn5.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn5, "gridColumn5");
            this.gridColumn5.ColumnEdit = this.repSpin;
            this.gridColumn5.FieldName = "NewQty";
            this.gridColumn5.Name = "gridColumn5";
            // 
            // repSpin
            // 
            this.repSpin.AccessibleDescription = null;
            this.repSpin.AccessibleName = null;
            resources.ApplyResources(this.repSpin, "repSpin");
            this.repSpin.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.repSpin.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repSpin.Mask.AutoComplete")));
            this.repSpin.Mask.BeepOnError = ((bool)(resources.GetObject("repSpin.Mask.BeepOnError")));
            this.repSpin.Mask.EditMask = resources.GetString("repSpin.Mask.EditMask");
            this.repSpin.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repSpin.Mask.IgnoreMaskBlank")));
            this.repSpin.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repSpin.Mask.MaskType")));
            this.repSpin.Mask.PlaceHolder = ((char)(resources.GetObject("repSpin.Mask.PlaceHolder")));
            this.repSpin.Mask.SaveLiteral = ((bool)(resources.GetObject("repSpin.Mask.SaveLiteral")));
            this.repSpin.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repSpin.Mask.ShowPlaceHolders")));
            this.repSpin.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repSpin.Mask.UseMaskAsDisplayFormat")));
            this.repSpin.Name = "repSpin";
            // 
            // gridColumn4
            // 
            this.gridColumn4.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn4.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn4.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn4.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn4.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn4.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn4.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn4.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn4, "gridColumn4");
            this.gridColumn4.ColumnEdit = this.repSpin;
            this.gridColumn4.FieldName = "Qty";
            this.gridColumn4.Name = "gridColumn4";
            this.gridColumn4.OptionsColumn.AllowEdit = false;
            // 
            // gridColumn2
            // 
            this.gridColumn2.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn2.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn2.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn2.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn2.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn2.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn2.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn2.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn2, "gridColumn2");
            this.gridColumn2.FieldName = "ItemNameAr";
            this.gridColumn2.Name = "gridColumn2";
            this.gridColumn2.OptionsColumn.AllowEdit = false;
            // 
            // gridColumn1
            // 
            this.gridColumn1.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn1.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn1.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn1.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn1.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn1.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn1.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn1.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn1, "gridColumn1");
            this.gridColumn1.FieldName = "ItemCode1";
            this.gridColumn1.Name = "gridColumn1";
            this.gridColumn1.OptionsColumn.AllowEdit = false;
            // 
            // gridColumn10
            // 
            this.gridColumn10.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn10.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn10.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn10.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn10.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn10.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn10.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn10.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn10, "gridColumn10");
            this.gridColumn10.FieldName = "ItemId";
            this.gridColumn10.Name = "gridColumn10";
            // 
            // gridColumn18
            // 
            this.gridColumn18.AppearanceCell.Options.UseTextOptions = true;
            this.gridColumn18.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn18.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn18.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.gridColumn18.AppearanceHeader.Options.UseTextOptions = true;
            this.gridColumn18.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gridColumn18.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.gridColumn18.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.gridColumn18, "gridColumn18");
            this.gridColumn18.FieldName = "StoreQty";
            this.gridColumn18.Name = "gridColumn18";
            // 
            // col_OldPiecesCount
            // 
            this.col_OldPiecesCount.AppearanceCell.Options.UseTextOptions = true;
            this.col_OldPiecesCount.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_OldPiecesCount.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_OldPiecesCount.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_OldPiecesCount.AppearanceHeader.Options.UseTextOptions = true;
            this.col_OldPiecesCount.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_OldPiecesCount.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_OldPiecesCount.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_OldPiecesCount, "col_OldPiecesCount");
            this.col_OldPiecesCount.ColumnEdit = this.repSpin;
            this.col_OldPiecesCount.FieldName = "OldPiecesCount";
            this.col_OldPiecesCount.Name = "col_OldPiecesCount";
            this.col_OldPiecesCount.OptionsColumn.AllowEdit = false;
            // 
            // col_NewPiecesCount
            // 
            this.col_NewPiecesCount.AppearanceCell.Font = new System.Drawing.Font("Tahoma", 8F, System.Drawing.FontStyle.Bold);
            this.col_NewPiecesCount.AppearanceCell.Options.UseFont = true;
            this.col_NewPiecesCount.AppearanceCell.Options.UseTextOptions = true;
            this.col_NewPiecesCount.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_NewPiecesCount.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_NewPiecesCount.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_NewPiecesCount.AppearanceHeader.Font = new System.Drawing.Font("Tahoma", 8F, System.Drawing.FontStyle.Bold);
            this.col_NewPiecesCount.AppearanceHeader.Options.UseFont = true;
            this.col_NewPiecesCount.AppearanceHeader.Options.UseTextOptions = true;
            this.col_NewPiecesCount.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_NewPiecesCount.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_NewPiecesCount.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_NewPiecesCount, "col_NewPiecesCount");
            this.col_NewPiecesCount.ColumnEdit = this.repSpin;
            this.col_NewPiecesCount.FieldName = "NewPiecesCount";
            this.col_NewPiecesCount.Name = "col_NewPiecesCount";
            // 
            // col_Length
            // 
            this.col_Length.AppearanceCell.Options.UseTextOptions = true;
            this.col_Length.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Length.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Length.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_Length.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Length.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Length.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Length.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_Length, "col_Length");
            this.col_Length.FieldName = "Length";
            this.col_Length.Name = "col_Length";
            this.col_Length.OptionsColumn.AllowEdit = false;
            // 
            // col_Width
            // 
            this.col_Width.AppearanceCell.Options.UseTextOptions = true;
            this.col_Width.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Width.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Width.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_Width.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Width.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Width.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Width.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_Width, "col_Width");
            this.col_Width.FieldName = "Width";
            this.col_Width.Name = "col_Width";
            this.col_Width.OptionsColumn.AllowEdit = false;
            // 
            // col_Height
            // 
            this.col_Height.AppearanceCell.Options.UseTextOptions = true;
            this.col_Height.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Height.AppearanceCell.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Height.AppearanceCell.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.col_Height.AppearanceHeader.Options.UseTextOptions = true;
            this.col_Height.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.col_Height.AppearanceHeader.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            this.col_Height.AppearanceHeader.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            resources.ApplyResources(this.col_Height, "col_Height");
            this.col_Height.FieldName = "Height";
            this.col_Height.Name = "col_Height";
            this.col_Height.OptionsColumn.AllowEdit = false;
            // 
            // frm_IC_EditItemQty
            // 
            this.AccessibleDescription = null;
            this.AccessibleName = null;
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.grdEditItemQty);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.KeyPreview = true;
            this.Name = "frm_IC_EditItemQty";
            this.Load += new System.EventHandler(this.frm_IC_EditQty_Load);
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.frm_IC_EditQty_KeyUp);
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frm_IC_EditItemQty_FormClosing);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Length.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Width.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Height.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpItems.Properties)).EndInit();
            
            ((System.ComponentModel.ISupportInitialize)(this.gridLookUpEdit1View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpStore.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpCompany.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpVendor.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdEditItemQty)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repUOM)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemGridLookUpEdit1View)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repDate.VistaTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repDate)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repSpin)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private DevExpress.XtraEditors.LabelControl labelControl15;
        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtn_Save;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtn_Help;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private System.Windows.Forms.GroupBox groupBox1;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraGrid.GridControl grdEditItemQty;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn4;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn5;
        private DevExpress.XtraEditors.Repository.RepositoryItemSpinEdit repSpin;
        private DevExpress.XtraEditors.SimpleButton btnSearch;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn9;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn8;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn7;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn6;
        private DevExpress.XtraEditors.LookUpEdit lkpStore;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn10;
        
        private DevExpress.XtraEditors.GridLookUpEdit lkpItems;
        private DevExpress.XtraGrid.Views.Grid.GridView gridLookUpEdit1View;
        private DevExpress.XtraGrid.Columns.GridColumn colItemId;
        private DevExpress.XtraGrid.Columns.GridColumn colItemCode1;
        private DevExpress.XtraGrid.Columns.GridColumn colItemNameAr;
        private DevExpress.XtraGrid.Columns.GridColumn colItemNameEn;
        private DevExpress.XtraEditors.GridLookUpEdit lkpCompany;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView2;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn11;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn12;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn13;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn14;
        private DevExpress.XtraEditors.Repository.RepositoryItemGridLookUpEdit repUOM;
        private DevExpress.XtraGrid.Views.Grid.GridView repositoryItemGridLookUpEdit1View;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn15;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn16;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn17;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn18;
        private DevExpress.XtraEditors.GridLookUpEdit lkpVendor;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView3;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn19;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn20;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn21;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn22;
        private DevExpress.XtraGrid.Columns.GridColumn gridColumn23;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraGrid.Columns.GridColumn colItemCode2;
        private DevExpress.XtraGrid.Columns.GridColumn colExpire;
        private DevExpress.XtraGrid.Columns.GridColumn colBatch;
        private DevExpress.XtraEditors.Repository.RepositoryItemDateEdit repDate;
        private DevExpress.XtraGrid.Columns.GridColumn col_OldPiecesCount;
        private DevExpress.XtraGrid.Columns.GridColumn col_NewPiecesCount;
        private DevExpress.XtraEditors.LabelControl lbl_Height;
        private DevExpress.XtraEditors.SpinEdit txt_Length;
        private DevExpress.XtraEditors.SpinEdit txt_Width;
        private DevExpress.XtraEditors.SpinEdit txt_Height;
        private DevExpress.XtraEditors.LabelControl lbl_Length;
        private DevExpress.XtraEditors.LabelControl lbl_Width;
        private DevExpress.XtraGrid.Columns.GridColumn col_Length;
        private DevExpress.XtraGrid.Columns.GridColumn col_Width;
        private DevExpress.XtraGrid.Columns.GridColumn col_Height;
    }
}