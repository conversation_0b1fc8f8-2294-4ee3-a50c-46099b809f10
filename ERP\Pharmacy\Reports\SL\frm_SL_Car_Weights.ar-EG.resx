﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="barBtnPrint.Caption" xml:space="preserve">
    <value>معاينة بيانات</value>
  </data>
  <data name="barBtnPreview.Caption" xml:space="preserve">
    <value>معاينة</value>
  </data>
  <data name="btn_Landscape.Caption" xml:space="preserve">
    <value>أفقي</value>
  </data>
  <data name="btn_Portrait.Caption" xml:space="preserve">
    <value>رأسي</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="popupMenu1.MenuAppearance.HeaderItemAppearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="popupMenu1.MenuAppearance.HeaderItemAppearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="popupMenu1.MenuAppearance.HeaderItemAppearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="popupMenu1.MenuAppearance.HeaderItemAppearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barBtnRefresh.Caption" xml:space="preserve">
    <value>تحديث</value>
  </data>
  <data name="barBtnClose.Caption" xml:space="preserve">
    <value>غلق</value>
  </data>
  <data name="barDockControlTop.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlTop.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barDockControlTop.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barDockControlTop.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlTop.Size" type="System.Drawing.Size, System.Drawing">
    <value>1107, 28</value>
  </data>
  <data name="barDockControlBottom.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlBottom.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barDockControlBottom.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barDockControlBottom.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlBottom.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 499</value>
  </data>
  <data name="barDockControlBottom.Size" type="System.Drawing.Size, System.Drawing">
    <value>1107, 0</value>
  </data>
  <data name="barDockControlLeft.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlLeft.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barDockControlLeft.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barDockControlLeft.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlLeft.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 471</value>
  </data>
  <data name="barDockControlRight.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlRight.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barDockControlRight.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barDockControlRight.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlRight.Location" type="System.Drawing.Point, System.Drawing">
    <value>1107, 28</value>
  </data>
  <data name="barDockControlRight.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 471</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>1107, 499</value>
  </data>
  <data name="picLogo.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="picLogo.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="picLogo.Size" type="System.Drawing.Size, System.Drawing">
    <value>73, 64</value>
  </data>
  <data name="mi_CopyRows.Size" type="System.Drawing.Size, System.Drawing">
    <value>141, 22</value>
  </data>
  <data name="mi_CopyRows.Text" xml:space="preserve">
    <value>نسخ الصفوف</value>
  </data>
  <data name="contextMenuStrip1.Size" type="System.Drawing.Size, System.Drawing">
    <value>142, 26</value>
  </data>
  <data name="grdCategory.EmbeddedNavigator.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grdCategory.EmbeddedNavigator.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="grdCategory.EmbeddedNavigator.AllowHtmlTextInToolTip" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>Default</value>
  </data>
  <data name="grdCategory.EmbeddedNavigator.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left</value>
  </data>
  <data name="grdCategory.EmbeddedNavigator.BackgroundImage" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="grdCategory.EmbeddedNavigator.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>Tile</value>
  </data>
  <data name="grdCategory.EmbeddedNavigator.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>Inherit</value>
  </data>
  <data name="grdCategory.EmbeddedNavigator.MaximumSize" type="System.Drawing.Size, System.Drawing">
    <value>0, 0</value>
  </data>
  <assembly alias="DevExpress.XtraEditors.v15.1" name="DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="grdCategory.EmbeddedNavigator.TextLocation" type="DevExpress.XtraEditors.NavigatorButtonsTextLocation, DevExpress.XtraEditors.v15.1">
    <value>Center</value>
  </data>
  <data name="grdCategory.EmbeddedNavigator.ToolTip" xml:space="preserve">
    <value />
  </data>
  <assembly alias="DevExpress.Utils.v15.1" name="DevExpress.Utils.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="grdCategory.EmbeddedNavigator.ToolTipIconType" type="DevExpress.Utils.ToolTipIconType, DevExpress.Utils.v15.1">
    <value>None</value>
  </data>
  <data name="grdCategory.EmbeddedNavigator.ToolTipTitle" xml:space="preserve">
    <value />
  </data>
  <data name="gridView1.Appearance.FooterPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.Appearance.FooterPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView1.Appearance.FooterPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView1.Appearance.FooterPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView1.Appearance.GroupFooter.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.Appearance.GroupFooter.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView1.Appearance.GroupFooter.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView1.Appearance.GroupFooter.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView1.Appearance.GroupPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.Appearance.GroupPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView1.Appearance.GroupPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView1.Appearance.GroupPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView1.Appearance.GroupRow.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.Appearance.GroupRow.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView1.Appearance.GroupRow.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView1.Appearance.GroupRow.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView1.Appearance.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.Appearance.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView1.Appearance.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView1.Appearance.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView1.Appearance.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.Appearance.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView1.Appearance.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView1.Appearance.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView1.AppearancePrint.FooterPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.AppearancePrint.FooterPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView1.AppearancePrint.FooterPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView1.AppearancePrint.FooterPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView1.AppearancePrint.GroupFooter.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.AppearancePrint.GroupFooter.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView1.AppearancePrint.GroupFooter.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView1.AppearancePrint.GroupFooter.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView1.AppearancePrint.GroupRow.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.AppearancePrint.GroupRow.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView1.AppearancePrint.GroupRow.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView1.AppearancePrint.GroupRow.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView1.AppearancePrint.HeaderPanel.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.AppearancePrint.HeaderPanel.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView1.AppearancePrint.HeaderPanel.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView1.AppearancePrint.HeaderPanel.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView1.AppearancePrint.Lines.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.AppearancePrint.Lines.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView1.AppearancePrint.Lines.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView1.AppearancePrint.Lines.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView1.AppearancePrint.Preview.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.AppearancePrint.Preview.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView1.AppearancePrint.Preview.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView1.AppearancePrint.Preview.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="gridView1.AppearancePrint.Row.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.AppearancePrint.Row.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="gridView1.AppearancePrint.Row.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="gridView1.AppearancePrint.Row.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="col_InvoiceDate.Caption" xml:space="preserve">
    <value>تاريخ الفاتورة</value>
  </data>
  <data name="col_InvoiceDate.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="col_InvoiceDate.VisibleIndex" type="System.Int32, mscorlib">
    <value>-1</value>
  </data>
  <data name="col_Qty.Caption" xml:space="preserve">
    <value>كمية</value>
  </data>
  <data name="col_Qty.VisibleIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="col_Qty.Width" type="System.Int32, mscorlib">
    <value>96</value>
  </data>
  <data name="col_ItemNameAr.Caption" xml:space="preserve">
    <value>الصنف</value>
  </data>
  <data name="col_ItemNameAr.VisibleIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="col_ItemNameAr.Width" type="System.Int32, mscorlib">
    <value>220</value>
  </data>
  <data name="colVehicleNumber.Caption" xml:space="preserve">
    <value>رقم العربة</value>
  </data>
  <data name="colVehicleNumber.VisibleIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="gridView1.GroupPanelText" xml:space="preserve">
    <value>اسحب أحد الأعمدة هنا لتجميع البيانات على أساسه</value>
  </data>
  <data name="gridView1.GroupSummary1" xml:space="preserve">
    <value>Qty</value>
  </data>
  <data name="gridView1.GroupSummary2" xml:space="preserve">
    <value>{0:n2}</value>
  </data>
  <data name="rep_MtrxParent.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_cat.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_comp.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="rep_mtrx.AutoHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repositoryItemCheckedComboBoxEdit1.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="repositoryItemCheckedComboBoxEdit1.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repositoryItemCheckedComboBoxEdit1.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="repositoryItemCheckedComboBoxEdit1.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repositoryItemCheckedComboBoxEdit1.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="repositoryItemCheckedComboBoxEdit1.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="repositoryItemCheckedComboBoxEdit1.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repositoryItemCheckedComboBoxEdit1.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repositoryItemCheckedComboBoxEdit1.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="grdCategory.Size" type="System.Drawing.Size, System.Drawing">
    <value>1098, 396</value>
  </data>
  <data name="lblFilter.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblFilter.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblFilter.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lblFilter.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lblFilter.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lblFilter.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblFilter.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblFilter.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="lblFilter.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblFilter.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="lblFilter.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblFilter.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lblFilter.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="lblFilter.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblFilter.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblFilter.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblFilter.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lblFilter.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblDateFilter.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblDateFilter.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblDateFilter.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lblDateFilter.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lblDateFilter.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lblDateFilter.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblDateFilter.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblDateFilter.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="lblDateFilter.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblDateFilter.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="lblDateFilter.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblDateFilter.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lblDateFilter.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="lblDateFilter.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblDateFilter.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblDateFilter.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblDateFilter.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lblDateFilter.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblReportName.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblReportName.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblReportName.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lblReportName.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lblReportName.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lblReportName.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lblReportName.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblReportName.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="lblReportName.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblReportName.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="lblReportName.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblReportName.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="lblReportName.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="lblReportName.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblReportName.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lblReportName.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblReportName.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lblReportName.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAA
        AAAfbf8AH2z/AB9r/x8fa/+rH2v/9h9r/9AfbP9HH2//AB9u/wAfcP8AAAAAAB9p/wAfZv8AH2D/Ah9j
        /2cfY//iH2P/7R9j/4YfY/8KH2P/AB9j/wAAAAAADw4QAA4NDwAODQ4ADQwORwwMDdAMCw32CwsMqwsL
        DB8MCw0ADAsNAB9u/wAfbf8WH23/qx9t//8fbv//H27//x9u/9gfb/89H3D/AB9y/wAfcf8AH23/AB9n
        /wAfZ/9cH2X/6x9j//8fY///H2P/9x9j/38fY/8GH2P/AB5e8QAPDhAADw4QAA4NDz0NDQ/YDQ0O/w0M
        Dv8MDA3/DAsNqwwLDRYMCw0AH2//FR9v/6Ifb///H3D//x9x//8fcf//H3H//x9x/9Mfcv82IHD/AiBw
        /wQfcP8DH2z/VB9q/+gfZ///H2X//x9j//8fY///H2P/9x9j/3cfY/8FH2P+ABAPEQAQDxE1Dw4Q0w8O
        EP8ODg//Dg0P/w0NDv8MDA3/DAsNogwLDRUfcP+MH3H//R9y//8ec///HnP//x50//8edP//HnP//x5z
        /9cec/+pH3L/qR9x/6wfbv/mH23//x9q//8faP//H2X//x9j//8fY///H2P/8h9j/04bTb8AERASERAQ
        ErsQEBL/EA8R/w8PEf8PDhD/Dg0P/w0ND/8MDA79DAwNjB5z/9QedP//HnX//x52//8edv//Hnb//x52
        //8edv//Hnb//x51//8edP//HnP//x9x//8fb///H23//x9r//8faP//H2X//x9j//8fY///H2P/gxg4
        hAASERM1ERET7hERE/8REBL/EBAS/xAPEf8PDhD/Dg4Q/w0ND/8NDA7THnX/wx52//8ed///Hnj//x54
        //8eef//Hnn//x54//8eeP/9Hnf/8h52//Iedf/zHnP//h9x//8fb///H23//x9q//8fZ///H2T//x9j
        //8fY/90GkGeABISFCYTEhThEhIU/xIRE/8RERP/EBAS/xAPEf8PDhD/Dg4Q/w4ND78ed/9UHnj/5x55
        //8eev//Hnr//x57//8ee///Hnr/+x56/5AeeP88Hnf/PB52/0Eedf+wHnP//x9x//8fb///H2z//x9p
        //8fZv//H2T/zB9j/yQdV9wAExIVAxMTFYMTEhX8ExIU/xISFP8RERP/ERAS/xAPEf8PDhDkDg4QTx50
        /wAeev9dHnv/7R58//8efP//Hn3//x59//0efP+UHnv/DB55/wAed/8AHnb/AB52/x8edf+5HnP//x9x
        //8fbv//H2v//x9o/9YfZf85H2f/AAAAAAAUExYAFBMWDRQTFpQUExX9ExIV/xISFP8SERP/ERAS6hAP
        EVYTDhQAHnr/AB58/wEefP9nHn3/7x5+//8efv/9Hn7/nR59/xAefP8AHnv/AB52/wAedf8AHnX/AB51
        /yUedP2+H3P//x9x//8fbPvbH2r/Px9o/wAfZv8AH2P/ABQTFgAUExYAFBQWEBQUFp0UExb9ExIV/xIS
        FOwRERNfIxkMABAQEQAee/8AHn3/AB5+/wcefv+xHn///x5//+oef/8tHn//AB59/wAefP8AAAAAAB52
        /wAeV/8AGkKGABk+eUYaQoPzG0KG/xo0ZZIbRZkAH2v/AB9o/wAAAAAAFBQWABQUFgAVFBcAFRQXLRUU
        F+oUExb/ExMVqxMTFAUSERQAEBASAAAAAAAegP8AHn//Ax6A/6kegP//HoD/5x6A/ycegP8AHoD/AAAA
        AAAAAAAAAAAAABpCgQAXFRcAFhMSOhcVFvAXFRb/GBUWjhgYHgAcO3gAAAAAAAAAAAAAAAAAFxYZABYV
        GAAWFRgnFRUX5xUUF/8UExajExIVAhQTFgAAAAAAHoD/AB6A/wAegP8IHoD/sx6B//8egf/rHoH/Lx6B
        /wAegv8AHoL/AAAAAAAXFhkAFBQXABcXGgAXFxpIGBca9BgYG/8ZGBuTGRkcABoaHQAbGx4AAAAAABkZ
        HAAYGBsAFxcZABcWGS8WFhjrFRUX/xUUF6wUFBYGFBMWABQTFQAef/8AHoD/Ah6A/20egf/xHoH//x6B
        //4egv+kHoL/Ex6C/wAegv8AFhYYABsZIAAXFxoAFxcaKRgXGsMYGBv/GRkc/xoZHN4bGh1FHBoeABwb
        HwAdHCAAGhkdABkZHAAYGBsTGBcapBcWGf4WFRj/FRQX7xQUFmYTEBMBExMVAB59/wIegP9kHoD/8B6B
        //8egv//HoL//x6C//8egv+cHoL/EB6B/wAaRX4AFxYZABcWGSQXFxq/GBgb/xkZHP8aGR3/Gxoe/xwb
        HtkcGx89HBwfABoYIQAbGh0AGxodEBkZHJwYGBv/Fxca/xYWGf8VFRf/FBQW7RQTFVwBABAAHn//Vx5/
        /+kegP//HoH//x6C//8egv//HoL//x6C//4egf+JHoD/BBgxUQAWFhgSFxYZsRgXGv8ZGBv/Ghkc/xsa
        Hf8cGx7/HBwf/x0cIM8eHSAmHRwgAB0cHwQbGh6JGhkc/hkYG/8YFxr/FxYZ/xYVGP8VFBf/ExMV5hMS
        FFIefv/EHn///x6A//8egf//HoL//x6C//8egv//HoL//x6B/+Megf8nGUB0ABYWGEwXFhn4GBca/xkY
        G/8aGh3/Gxse/xwcH/8dHCD/Hh0h/x4dIXUdHCAAHBsfJxwbHuMaGh3/GRkc/xgXGv8XFhn/FhUY/xUU
        F/8UExX/ExIUwR5+/9Uef///HoD//x6B//8egf//HoL//x6C//8egf//HoH/7h6B/zQaRX4AFhYZWhcW
        GfwYGBv/GRkc/xsaHf8cGx7/HRwg/x4dIf8fHiL/Hx4igx4dIQAdHB80HBsf7hsaHf8aGRz/GBgb/xcX
        Gf8WFRj/FRQX/xQTFf8TEhXSHn7/jR5///0egP//HoD//x6B//8egf//HoH//x6B//8egf+4HoD/EBk3
        YAAWFhkqFxYZ2xgYG/8ZGRz/Gxod/xwbHv8dHCD/Hh0h/x8eIu8gHyNKHx4iABwcHxAcGx64Gxod/xoZ
        HP8YGBv/Fxca/xYVGP8VFBf/FBMV/BMSFYgefv8VHn7/oh5///8egP//HoD//x6B//8egf//HoD/0B6A
        /zIegf8AFxIRABgaHQAXFxpQGBca5hkYHP8aGh3/Gxse/x0cH/8dHSD1Hh0hch8eIwQfHiIAGxseABwb
        HjIaGh3QGRkc/xgXG/8XFhn/FhUY/xUUF/8UExafExIVEx5+/wAefv8XHn7/qx5///8ef///Hn///x6A
        /9UegP85HoD/AB5//wAYIzUAFxcaABgYGwAYFxpXGRgb6hoZHP8bGh7/HBse+B0cH3weHSEFHh0hAB8e
        IgAbGh0AGxodABoZHDkZGBvVGBca/xcWGf8WFRj/FRQXqBQUFhUUExYAHn7/AB5+/wAefv8dHn7/yR5+
        //8efv/yHn//Sh5//wAef/8AHoD/AAAAAAAYFxoAGBcaABUXGgAZGBtUGRkcwxoaHc0bGh5yHBseCB0c
        HwAeHSEAAAAAABsaHQAaGRwAGRgbABgYG0oYFxryFxYZ/xYVGMUVFBcbFRQXABQUFgAefv8AHnz/AB56
        /wIefP+oHn3//x59/+ceff8lHn3/AB5//wAAAAAAAAAAAAAAAAAYGBoAGBgbABgXGgAaGRwNGhkdERkZ
        HAAbGh4AGxoeAAAAAAAAAAAAAAAAABkZHAAXFxoAFxcaJRcWGecWFRj/FRUXohMSFQEVFBcAFRQXAB50
        /wAed/8AHnn/CR56/7Qee///Hnv/6x57/y4eev8AHnj/AB53/wAAAAAAFRQXABYVGAAWFRgAFxYZJBgX
        GnEYFxp6GRgbNBkOEQAYGBsAGBcaAAAAAAAWFhgAFhYYABYWGQAXFhkuFhYY6xUVF/8VFBeuFBQWBxMT
        FQASERQAHnL/AB11/wMedv9zHnf/8x54//8eef/+Hnn/oR55/xEed/8AHnb/ABMSFgAbFx0AFRQXABUV
        Fy4WFhjJFxYZ/xgXGv8YFxreGBcaShcXGgAYFxoAFxYZABYVGAAWFRgAFhYYERYWGKEWFRj+FRQX/xQT
        FvETEhVuEhETAxIREwAgb/8CH3L/aR50//Iedf//Hnb//x53//8ed//+Hnf/lx52/w0edf8AGEOMABQT
        FgAUExYnFRQXxBUVF/8WFhj/FxYZ/xcXGv8XFxrdFxcaQRYWGQAWFRgAFhUYABYVGA0WFRiXFhUY/hUU
        F/8UExb/ExMV/xISFPARERNlEQ4RAh9t/1sfb//sH3H//x9y//8ec///HnT//x50//8edP/9HnT/hx9y
        /wUVKUwAEhIUFBMSFbYUExb/FBQW/xERE/8QEBL/FhUY/xYWGf8XFhnXFhYYYBYVGE4WFRhOFhUYmBUV
        F/wVFBf/FBMW/xMTFf8SEhT/ERET/xEQEuoQDxFXH2v/xh9s//8fbv//H2///x9w//8fcf//H3H//x9x
        //8fcf/kH3D/KBc3cQASERNOEhIU+BMSFf8SERT/DAwN/woJC/8QDxH/FRUX/xYVGP8WFRj7FRUX+BUU
        F/gVFBf/FBQW/xQTFv8TEhX/EhIU/xIRE/8REBL/EA8R/w8PEMMfZ//UH2n//x9r//8fbP//H23//x9u
        //8fbv//H27//x9u/+4fbv80Fzl6ABEQE1oRERP8EhIU/xERE/8ODg//DAwN/xAPEf8UExb/FBQW/xQU
        Fv8UFBb/FBMW/xQTFv8TExX/ExIU/xISFP8SERP/ERAS/xAQEv8PDxH/Dg4Q0R9l/4kfZf/8H2f//x9o
        //8faf//H2r//x9q//8fav//H2v/sh9r/w4VKlcAEBASKRAQEtoRERP/EhET/xEQEv8QEBL/EhIU/xMT
        Ff8TExXyExMVsxMTFagTEhWoExIU1BISFP8SERP/ERET/xEQEv8QDxH/Dw8R/w4OEPwODQ+EH2P/Ex9j
        /6AfZP//H2T//x9l//8fZv//H2b//x9n/8gfaP8qH2j/AA0CAAAREBIAEA8RThAQEuUREBL/ERET/xIR
        E/8SERP/EhEU9hISFHYTExUIEhEUAxMRFQISERMxERETzREQEv8QEBL/EA8R/w8OEP8ODhD/Dg0Pnw0N
        DxIfY/8AH2P/Fh9j/6kfY///H2P//x9j//8fY//MH2T/MB9m/wAfVP8AERgnABAPEQAQERMAEA8RVRAP
        EegQEBL/EBAS/xEQEvcRERN9EhETBhISFAATEhUAExIVABEREwAQEBI2EA8R0g8PEf8PDhD/Dg4P/w4N
        D6gNDQ8VDg0PAB9j/wAfY/8AH2P/HR9j/6QfY//nH2P/wh9j/zofZP8AH2P/AB9m/wAAAAAAEA8RAA8P
        EQAKDhIBDw8QYA8PEdcQDxHiEA8RgRAPEQkREBIAEhETAAAAAAARERMAEA8RABAPEQAPDhBADg4Qxg4N
        D+cODQ+iDQ0OHA0NDwAODQ8AACAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAGAIBw
        DgEAIAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAQAAHAOAAAgBAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgBAA=
</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>نقلات السيارات</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="repositoryItemTextEdit1.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
</root>