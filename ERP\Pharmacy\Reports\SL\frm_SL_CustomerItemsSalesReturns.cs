﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;

using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;

using DevExpress.XtraReports.UI;

namespace Reports
{
    public partial class frm_SL_CustomerItemsSalesReturns : DevExpress.XtraEditors.XtraForm
    {
        public bool UserCanOpen;

        string reportName, dateFilter, otherFilters;

        int itemId1, itemId2, customerId1, customerId2, custGroupId,
            storeId1, storeId2, salesEmpId;
        string categoryNum;
        byte FltrTyp_item, fltrTyp_Date, FltrTyp_Customer, FltrTyp_SellPrice, FltrTyp_Category,
            FltrTyp_Store, FltrTyp_InvBook;

        byte FltrTyp_Company;
        int companyId;

        DateTime date1, date2;

        private void prnt_LandScape_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCategory, true).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        private void prnt_Vertical_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCategory, false).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        decimal SellPrice1, SellPrice2;
        string custGroupAccNumber;
        List<int> lstStores = new List<int>();
        List<int> lst_invBooksId = new List<int>();

        public frm_SL_CustomerItemsSalesReturns(string reportName, string dateFilter, string otherFilters,
            byte fltrTyp_item, int itemId1, int itemId2,
            byte fltrTyp_Date, DateTime date1, DateTime date2,
            byte FltrTyp_Customer, int customerId1, int customerId2,
            byte FltrTyp_SellPrice, decimal SellPrice1, decimal SellPrice2,
            byte FltrTyp_Category, string categoryNum,
            int custGroupId, string custGroupAccNumber,
            byte FltrTyp_Store, int storeId1, int storeId2,
            int salesEmpId, byte FltrTyp_InvBook, string InvBooks, byte FltrTyp_Company, int companyId)
        {
            UserCanOpen = LoadPrivilege();
            if (UserCanOpen == false)
                return;

            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();

            this.reportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;

            this.FltrTyp_Company = FltrTyp_Company;
            this.companyId = companyId;

            this.FltrTyp_item = fltrTyp_item;
            this.fltrTyp_Date = fltrTyp_Date;
            this.FltrTyp_Customer = FltrTyp_Customer;
            this.FltrTyp_SellPrice = FltrTyp_SellPrice;
            this.FltrTyp_Category = FltrTyp_Category;
            this.FltrTyp_Store = FltrTyp_Store;

            this.itemId1 = itemId1;
            this.itemId2 = itemId2;

            this.date1 = date1;
            this.date2 = date2;

            this.customerId1 = customerId1;
            this.customerId2 = customerId2;
            this.categoryNum = categoryNum;

            this.SellPrice1 = SellPrice1;
            this.SellPrice2 = SellPrice2;

            this.custGroupId = custGroupId;
            this.custGroupAccNumber = custGroupAccNumber;
            this.storeId1 = storeId1;
            this.storeId2 = storeId2;
            this.salesEmpId = salesEmpId;

            this.FltrTyp_InvBook = FltrTyp_InvBook;
            Utilities.Get_ChkLst_Items(InvBooks, lst_invBooksId);

            getReportHeader();

            LoadData();
            col_PiecesCount.Visible = Shared.st_Store.PiecesCount;
            col_PiecesCount.Caption = Shared.IsEnglish ? Shared.st_Store.PiecesCountNameEn : Shared.st_Store.PiecesCountNameAr;
            ReportsUtils.ColumnChooser(grdCategory);
        }

        private void frm_SL_InvoiceList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                ReportsRTL.LTRLayout(this);

            col_LibraQty.OptionsColumn.ShowInCustomizationForm = col_kg_weight_libra.OptionsColumn.ShowInCustomizationForm = Shared.LibraAvailabe;
            col_PiecesCount.Caption = Shared.IsEnglish ? Shared.st_Store.PiecesCountNameEn : Shared.st_Store.PiecesCountNameAr;

            ReportsUtils.Load_Grid_Layout(grdCategory, this.Name.Replace("frm_", "Rpt_"));
            //LoadPrivilege();
        }

        private void frm_Rep_FormClosing(object sender, FormClosingEventArgs e)
        {
            ReportsUtils.save_Grid_Layout(grdCategory, this.Name.Replace("frm_", "Rpt_"), true);
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Preview_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCategory, true).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        private void barBtnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadData();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grdCategory.MinimumSize = grdCategory.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grdCategory, true, true).ShowPreview();
                grdCategory.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                //lblCompName.Text = comp.CmpNameAr;
                lblReportName.Text = this.Text = reportName;
                this.Text = reportName;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        void LoadData()
        {
            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;

            ERPDataContext DB = new ERPDataContext();

            var stores = DB.IC_Stores.ToList();
            foreach (var store in stores)
            {
                if (FltrTyp_Store == 2)
                {
                    if (store.StoreId <= storeId2 && store.StoreId >= storeId1)
                    {
                        lstStores.Add(store.StoreId);
                    }
                }
                else if (FltrTyp_Store == 0)
                {
                    lstStores.Add(store.StoreId);
                }
                else if (storeId1 > 0 && (store.StoreId == storeId1 || store.ParentId == storeId1))
                    lstStores.Add(store.StoreId);
                //else if (store_id2 > 0 && (store.StoreId == store_id2 || store.ParentId == store_id2))
                //    lstStores.Add(store.StoreId);
            }
            var defaultCategories = DB.IC_User_Categories.Where(a => a.UserId == Shared.UserId).Select(a => a.CategoryId).ToList();

            #region sales
            var data_sales = (
                from c in DB.SL_Customers
                join a in DB.ACC_Accounts
                on c.AccountId equals a.AccountId
                where custGroupId == 0 ? true : a.AcNumber.StartsWith(custGroupAccNumber)

                where FltrTyp_Customer == 1 ? c.CustomerId == customerId1 : true
                where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 != 0) ?
                c.CustomerId >= customerId1 && c.CustomerId <= customerId2 : true
                where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 == 0) ?
                c.CustomerId >= customerId1 : true
                where (FltrTyp_Customer == 2 && customerId1 == 0 && customerId2 != 0) ?
                c.CustomerId <= customerId2 : true

                join i in DB.SL_Invoices on c.CustomerId equals i.CustomerId

                where lstStores.Count > 0 ? lstStores.Contains(i.StoreId) : true

                where salesEmpId == 0 ? true : i.SalesEmpId == salesEmpId

                where FltrTyp_InvBook == 0 ? true : (i.InvoiceBookId.HasValue && lst_invBooksId.Contains(i.InvoiceBookId.Value))

                where fltrTyp_Date == 1 ? i.InvoiceDate.Date == date1 : true
                where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                i.InvoiceDate >= date1 && i.InvoiceDate <= date2 : true
                where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                i.InvoiceDate >= date1 : true
                where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                i.InvoiceDate <= date2 : true

                join s in DB.SL_InvoiceDetails on i.SL_InvoiceId equals s.SL_InvoiceId

                where FltrTyp_item == 1 ? s.ItemId == itemId1 : true
                where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 != 0) ?
                s.ItemId >= itemId1 && s.ItemId <= itemId2 : true
                where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 == 0) ?
                s.ItemId >= itemId1 : true
                where (FltrTyp_item == 2 && itemId1 == 0 && itemId2 != 0) ?
                s.ItemId <= itemId2 : true

                where FltrTyp_SellPrice == 1 ? s.SellPrice == SellPrice1 : true
                where (FltrTyp_SellPrice == 2 && SellPrice1 != 0 && SellPrice2 != 0) ?
                s.SellPrice >= SellPrice1 && s.SellPrice <= SellPrice2 : true
                where (FltrTyp_SellPrice == 2 && SellPrice1 != 0 && SellPrice2 == 0) ?
                s.SellPrice >= SellPrice1 : true
                where (FltrTyp_SellPrice == 2 && SellPrice1 == 0 && SellPrice2 != 0) ?
                s.SellPrice <= SellPrice2 : true

                join t in DB.IC_Items on s.ItemId equals t.ItemId
                where FltrTyp_Company == 1 ? t.Company == companyId : true

                join g in DB.IC_Categories on t.Category equals g.CategoryId
                where FltrTyp_Category == 1 ? g.CatNumber.StartsWith(categoryNum) : true
                where defaultCategories.Count() > 0 ? defaultCategories.Contains(g.CategoryId) : true
                join u in DB.IC_UOMs on s.UOMId equals u.UOMId
                join p in DB.IC_Companies on t.Company equals p.CompanyId
                join ic in DB.IC_Categories on t.Category equals ic.CategoryId
              
                let cat = DB.SL_CustomerGroups //on c.GroupId equals cat.CustomerGroupId
                let groups = DB.SL_Group_Customers
                select new
                {
                    ProcessId = (int)Process.SellInvoice,
                    Process = Shared.IsEnglish ? ResEn.Sales : ResAr.Sales,
                    CusNameAr = c.CusNameAr,
                    InvoiceCode = i.InvoiceCode,
                    InvoiceDate = i.InvoiceDate,
                    ItemNameAr = t.ItemNameAr,
                    Qty = s.Qty,
                    UOM = u.UOM,
                    PiecesCount = s.PiecesCount,
                    SellPrice = s.SellPrice,
                    TotalSellValue = s.Qty * s.SellPrice,
                    s.DiscountRatio,
                    s.DiscountRatio2,
                    s.DiscountRatio3,
                    DicVal1 = (s.Qty * s.SellPrice) * s.DiscountRatio,
                    DicVal2 = ((s.Qty * s.SellPrice) - ((s.Qty * s.SellPrice) * s.DiscountRatio)) * s.DiscountRatio2,
                    DicVal3 =
                    ((s.Qty * s.SellPrice) -
                    ((s.Qty * s.SellPrice) * s.DiscountRatio) -
                    (((s.Qty * s.SellPrice) - ((s.Qty * s.SellPrice) * s.DiscountRatio)) * s.DiscountRatio2)) * s.DiscountRatio3,
                    s.DiscountValue,
                    s.SalesTax,
                    //TotalSellPrice = ((s.Qty * s.SellPrice) - s.DiscountValue) +
                    //(Shared.st_Store.PriceIncludeSalesTax ? s.SalesTax * -1 : s.SalesTax)
                    TotalSellPrice = s.TotalSellPrice,
                    i.DriverName,
                    i.VehicleNumber,
                    i.Destination,
                    p.CompanyNameAr,
                    //Store = i.StoreId == 0 ? DB.IC_Stores.SingleOrDefault(c => c.StoreId == s.StoreId).StoreNameAr : DB.IC_Stores.SingleOrDefault(c => c.StoreId == i.StoreId).StoreNameAr
                    SalesEmp = DB.HR_Employees.SingleOrDefault(m => m.EmpId == i.SalesEmpId).EmpName,

                    //mohammad 13-09-2018
                    CrncId = s.SL_Invoice.CrncId,
                    CrncRate = s.SL_Invoice.CrncRate,
                    TotalSellPrice_Local = s.TotalSellPrice * s.SL_Invoice.CrncRate,
                    CustomerCategory = cat.Where(x => x.CustomerGroupId == c.CategoryId).Select(x => Shared.IsEnglish ? x.CGNameEn : x.CGNameAr).FirstOrDefault(),
                    i.Notes,
                    ItemCategory = Shared.IsEnglish ? ic.CategoryNameEn : ic.CategoryNameAr,

                    //mohammad 10-11-2019
                    CustomerGroup = groups.Where(x => x.GroupId == c.GroupId).Select(x => Shared.IsEnglish ? x.NameEn : x.NameAr).FirstOrDefault(),
                    /////
                    t.ItemCode1,
                    t.ItemCode2,
                    s.kg_Weight_libra,
                    s.LibraQty,
                    c.City
                }).ToList().OrderBy(x => x.InvoiceDate).ThenBy(x => x.CusNameAr).ThenBy(x => x.ItemNameAr).ToList();
            #endregion

            #region return
            var data_returns = (
              from c in DB.SL_Customers
              join a in DB.ACC_Accounts
                on c.AccountId equals a.AccountId
              where custGroupId == 0 ? true : a.AcNumber.StartsWith(custGroupAccNumber)

              where FltrTyp_Customer == 1 ? c.CustomerId == customerId1 : true
              where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 != 0) ?
              c.CustomerId >= customerId1 && c.CustomerId <= customerId2 : true
              where (FltrTyp_Customer == 2 && customerId1 != 0 && customerId2 == 0) ?
              c.CustomerId >= customerId1 : true
              where (FltrTyp_Customer == 2 && customerId1 == 0 && customerId2 != 0) ?
              c.CustomerId <= customerId2 : true

              join i in DB.SL_Returns on c.CustomerId equals i.CustomerId

              where lstStores.Count > 0 ? lstStores.Contains(i.StoreId) : true

              where salesEmpId == 0 ? true : i.SalesEmpId == salesEmpId

              where FltrTyp_InvBook == 0 ? true : (i.InvoiceBookId.HasValue && lst_invBooksId.Contains(i.InvoiceBookId.Value))

              where fltrTyp_Date == 1 ? i.ReturnDate.Date == date1.Date : true
              where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
              i.ReturnDate.Date >= date1.Date && i.ReturnDate.Date <= date2.Date : true
              where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
              i.ReturnDate.Date >= date1.Date : true
              where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
              i.ReturnDate.Date <= date2.Date : true

              join s in DB.SL_ReturnDetails on i.SL_ReturnId equals s.SL_ReturnId

              where FltrTyp_item == 1 ? s.ItemId == itemId1 : true
              where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 != 0) ?
              s.ItemId >= itemId1 && s.ItemId <= itemId2 : true
              where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 == 0) ?
              s.ItemId >= itemId1 : true
              where (FltrTyp_item == 2 && itemId1 == 0 && itemId2 != 0) ?
              s.ItemId <= itemId2 : true

              where FltrTyp_SellPrice == 1 ? s.SellPrice == SellPrice1 : true
              where (FltrTyp_SellPrice == 2 && SellPrice1 != 0 && SellPrice2 != 0) ?
              s.SellPrice >= SellPrice1 && s.SellPrice <= SellPrice2 : true
              where (FltrTyp_SellPrice == 2 && SellPrice1 != 0 && SellPrice2 == 0) ?
              s.SellPrice >= SellPrice1 : true
              where (FltrTyp_SellPrice == 2 && SellPrice1 == 0 && SellPrice2 != 0) ?
              s.SellPrice <= SellPrice2 : true

              join t in DB.IC_Items on s.ItemId equals t.ItemId
              where FltrTyp_Company == 1 ? t.Company == companyId : true

              join g in DB.IC_Categories on t.Category equals g.CategoryId
              where FltrTyp_Category == 1 ? g.CatNumber.StartsWith(categoryNum) : true
              where defaultCategories.Count() > 0 ? defaultCategories.Contains(g.CategoryId) : true
              join u in DB.IC_UOMs on s.UOMId equals u.UOMId
              join p in DB.IC_Companies on t.Company equals p.CompanyId
              join ic in DB.IC_Categories on t.Category equals ic.CategoryId
          
              //Mohammad 13-09-2018
              //join cat in DB.SL_CustomerGroups on c.GroupId equals cat.CustomerGroupId
              let cat = DB.SL_CustomerGroups //on c.GroupId equals cat.CustomerGroupId

              let groups = DB.SL_Group_Customers

              select new
              {
                  ProcessId = (int)Process.SellReturn,
                  Process = Shared.IsEnglish ? ResEn.SalesReturn : ResAr.SalesReturn,
                  CusNameAr = c.CusNameAr,
                  InvoiceCode = i.ReturnCode,
                  InvoiceDate = i.ReturnDate,
                  ItemNameAr = t.ItemNameAr,
                  Qty = s.Qty * -1,
                  UOM = u.UOM,
                  PiecesCount = s.PiecesCount * -1,
                  SellPrice = s.SellPrice * -1,
                  TotalSellValue = s.Qty * s.SellPrice * -1,
                  s.DiscountRatio,
                  s.DiscountRatio2,
                  s.DiscountRatio3,
                  DicVal1 = ((s.Qty * s.SellPrice) * s.DiscountRatio) * -1,
                  DicVal2 = (((s.Qty * s.SellPrice) - ((s.Qty * s.SellPrice) * s.DiscountRatio)) * s.DiscountRatio2) * -1,
                  DicVal3 =
                        (((s.Qty * s.SellPrice) -
                        ((s.Qty * s.SellPrice) * s.DiscountRatio) -
                        (((s.Qty * s.SellPrice) - ((s.Qty * s.SellPrice) * s.DiscountRatio)) * s.DiscountRatio2)) * s.DiscountRatio3) * -1,
                  DiscountValue = s.DiscountValue * -1,
                  SalesTax = s.SalesTax * -1,
                  //TotalSellPrice = ((s.Qty * s.SellPrice) - s.DiscountValue) +
                  //(Shared.st_Store.PriceIncludeSalesTax ? s.SalesTax * -1 : s.SalesTax)
                  TotalSellPrice = s.TotalSellPrice * -1,
                  i.DriverName,
                  i.VehicleNumber,
                  i.Destination,
                  p.CompanyNameAr,
                  //Store= DB.IC_Stores.SingleOrDefault(c => c.StoreId == i.StoreId).StoreNameAr
                  SalesEmp = DB.HR_Employees.SingleOrDefault(m => m.EmpId == i.SalesEmpId).EmpName,

                  //mohammad 13-09-2018
                  CrncId = s.SL_Return.CrncId,
                  CrncRate = s.SL_Return.CrncRate,
                  TotalSellPrice_Local = s.TotalSellPrice * s.SL_Return.CrncRate * -1,
                  CustomerCategory = cat.Where(x => x.CustomerGroupId == c.CategoryId).Select(x => Shared.IsEnglish ? x.CGNameEn : x.CGNameAr).FirstOrDefault(),

                  i.Notes,
                  ItemCategory = Shared.IsEnglish ? ic.CategoryNameEn : ic.CategoryNameAr,

                  //mohammad 10-11-2019
                  CustomerGroup = groups.Where(x => x.GroupId == c.GroupId).Select(x => Shared.IsEnglish ? x.NameEn : x.NameAr).FirstOrDefault(),
                  ////////
                  ///
                  t.ItemCode1,
                  t.ItemCode2,
                  s.kg_Weight_libra,
                  LibraQty = t.is_libra ==  true ? s.LibraQty : null,
                  c.City

              }).ToList().OrderBy(x => x.InvoiceDate).ThenBy(x => x.CusNameAr).ThenBy(x => x.ItemNameAr).ToList();

            #endregion

            grdCategory.DataSource = data_sales.Concat(data_returns).ToList().OrderBy(x => x.InvoiceDate).ThenBy(x => x.CusNameAr).ThenBy(x => x.ItemNameAr).ToList();

            rep_Currency.DataSource = Shared.lstCurrency;
            rep_Currency.ValueMember = "CrncId";
            rep_Currency.DisplayMember = "crncName";

            if (FltrTyp_Customer == 1)
                col_CusNameAr.Visible = false;

            if (FltrTyp_item == 1)
                col_ItemNameAr.Visible = false;
        }

        private void gridView1_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            if (e.ListSourceRowIndex < 0)
                return;

            if (e.Column.FieldName == "Index")
                e.Value = e.RowHandle() + 1;
        }

        public bool LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.rpt_SL_CustomerItemsSalesReturns).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            return true;
        }

        private void gridView1_RowCellStyle(object sender, RowCellStyleEventArgs e)
        {
            //if (e.RowHandle < 0)
            //    return;

            //if (Convert.ToInt32(gridView1.GetRowCellValue(e.RowHandle, "ProcessId")) == (int)Process.SellReturn)
            //    e.Appearance.ForeColor = Color.Red;
            //else
            //    e.Appearance.ForeColor = Color.Green;
        }

    }
}