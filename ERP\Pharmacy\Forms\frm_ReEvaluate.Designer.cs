﻿namespace Pharmacy.Forms
{
    partial class frm_ReEvaluate
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_ReEvaluate));
            this.txt_End = new DevExpress.XtraEditors.DateEdit();
            this.txt_Start = new DevExpress.XtraEditors.DateEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.btn_ReEvaluate = new DevExpress.XtraEditors.SimpleButton();
            this.progressBar1 = new System.Windows.Forms.ProgressBar();
            this.btn_PrInv = new DevExpress.XtraEditors.SimpleButton();
            this.btn_SLReturnInv = new DevExpress.XtraEditors.SimpleButton();
            this.btn_Replacement = new DevExpress.XtraEditors.SimpleButton();
            this.btn_StockTaking = new DevExpress.XtraEditors.SimpleButton();
            this.btn_RepostAllProcess = new DevExpress.XtraEditors.SimpleButton();
            this.btn_IC_Trns_ALL = new DevExpress.XtraEditors.SimpleButton();
            this.btnRe_CodingCostCenter = new DevExpress.XtraEditors.SimpleButton();
            this.btn_ReCodingAccounts = new DevExpress.XtraEditors.SimpleButton();
            this.btn_Re_SerialMontlyJournalCode = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.txt_End.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_End.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Start.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Start.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // txt_End
            // 
            resources.ApplyResources(this.txt_End, "txt_End");
            this.txt_End.EnterMoveNextControl = true;
            this.txt_End.Name = "txt_End";
            this.txt_End.Properties.AccessibleDescription = resources.GetString("txt_End.Properties.AccessibleDescription");
            this.txt_End.Properties.AccessibleName = resources.GetString("txt_End.Properties.AccessibleName");
            this.txt_End.Properties.AutoHeight = ((bool)(resources.GetObject("txt_End.Properties.AutoHeight")));
            this.txt_End.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("txt_End.Properties.Buttons"))))});
            this.txt_End.Properties.CalendarTimeProperties.AccessibleDescription = resources.GetString("txt_End.Properties.CalendarTimeProperties.AccessibleDescription");
            this.txt_End.Properties.CalendarTimeProperties.AccessibleName = resources.GetString("txt_End.Properties.CalendarTimeProperties.AccessibleName");
            this.txt_End.Properties.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("txt_End.Properties.CalendarTimeProperties.AutoHeight")));
            this.txt_End.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txt_End.Properties.CalendarTimeProperties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_End.Properties.CalendarTimeProperties.Mask.AutoComplete")));
            this.txt_End.Properties.CalendarTimeProperties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_End.Properties.CalendarTimeProperties.Mask.BeepOnError")));
            this.txt_End.Properties.CalendarTimeProperties.Mask.EditMask = resources.GetString("txt_End.Properties.CalendarTimeProperties.Mask.EditMask");
            this.txt_End.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_End.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.txt_End.Properties.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_End.Properties.CalendarTimeProperties.Mask.MaskType")));
            this.txt_End.Properties.CalendarTimeProperties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_End.Properties.CalendarTimeProperties.Mask.PlaceHolder")));
            this.txt_End.Properties.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_End.Properties.CalendarTimeProperties.Mask.SaveLiteral")));
            this.txt_End.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_End.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.txt_End.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_End.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat")));
            this.txt_End.Properties.CalendarTimeProperties.NullValuePrompt = resources.GetString("txt_End.Properties.CalendarTimeProperties.NullValuePrompt");
            this.txt_End.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_End.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue")));
            this.txt_End.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_End.Properties.Mask.AutoComplete")));
            this.txt_End.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_End.Properties.Mask.BeepOnError")));
            this.txt_End.Properties.Mask.EditMask = resources.GetString("txt_End.Properties.Mask.EditMask");
            this.txt_End.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_End.Properties.Mask.IgnoreMaskBlank")));
            this.txt_End.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_End.Properties.Mask.MaskType")));
            this.txt_End.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_End.Properties.Mask.PlaceHolder")));
            this.txt_End.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_End.Properties.Mask.SaveLiteral")));
            this.txt_End.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_End.Properties.Mask.ShowPlaceHolders")));
            this.txt_End.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_End.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_End.Properties.NullValuePrompt = resources.GetString("txt_End.Properties.NullValuePrompt");
            this.txt_End.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_End.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // txt_Start
            // 
            resources.ApplyResources(this.txt_Start, "txt_Start");
            this.txt_Start.EnterMoveNextControl = true;
            this.txt_Start.Name = "txt_Start";
            this.txt_Start.Properties.AccessibleDescription = resources.GetString("txt_Start.Properties.AccessibleDescription");
            this.txt_Start.Properties.AccessibleName = resources.GetString("txt_Start.Properties.AccessibleName");
            this.txt_Start.Properties.AutoHeight = ((bool)(resources.GetObject("txt_Start.Properties.AutoHeight")));
            this.txt_Start.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("txt_Start.Properties.Buttons"))))});
            this.txt_Start.Properties.CalendarTimeProperties.AccessibleDescription = resources.GetString("txt_Start.Properties.CalendarTimeProperties.AccessibleDescription");
            this.txt_Start.Properties.CalendarTimeProperties.AccessibleName = resources.GetString("txt_Start.Properties.CalendarTimeProperties.AccessibleName");
            this.txt_Start.Properties.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("txt_Start.Properties.CalendarTimeProperties.AutoHeight")));
            this.txt_Start.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.txt_Start.Properties.CalendarTimeProperties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_Start.Properties.CalendarTimeProperties.Mask.AutoComplete")));
            this.txt_Start.Properties.CalendarTimeProperties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_Start.Properties.CalendarTimeProperties.Mask.BeepOnError")));
            this.txt_Start.Properties.CalendarTimeProperties.Mask.EditMask = resources.GetString("txt_Start.Properties.CalendarTimeProperties.Mask.EditMask");
            this.txt_Start.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Start.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.txt_Start.Properties.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Start.Properties.CalendarTimeProperties.Mask.MaskType")));
            this.txt_Start.Properties.CalendarTimeProperties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_Start.Properties.CalendarTimeProperties.Mask.PlaceHolder")));
            this.txt_Start.Properties.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Start.Properties.CalendarTimeProperties.Mask.SaveLiteral")));
            this.txt_Start.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Start.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.txt_Start.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_Start.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat")));
            this.txt_Start.Properties.CalendarTimeProperties.NullValuePrompt = resources.GetString("txt_Start.Properties.CalendarTimeProperties.NullValuePrompt");
            this.txt_Start.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_Start.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue")));
            this.txt_Start.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("txt_Start.Properties.Mask.AutoComplete")));
            this.txt_Start.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("txt_Start.Properties.Mask.BeepOnError")));
            this.txt_Start.Properties.Mask.EditMask = resources.GetString("txt_Start.Properties.Mask.EditMask");
            this.txt_Start.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("txt_Start.Properties.Mask.IgnoreMaskBlank")));
            this.txt_Start.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("txt_Start.Properties.Mask.MaskType")));
            this.txt_Start.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("txt_Start.Properties.Mask.PlaceHolder")));
            this.txt_Start.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("txt_Start.Properties.Mask.SaveLiteral")));
            this.txt_Start.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("txt_Start.Properties.Mask.ShowPlaceHolders")));
            this.txt_Start.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("txt_Start.Properties.Mask.UseMaskAsDisplayFormat")));
            this.txt_Start.Properties.NullValuePrompt = resources.GetString("txt_Start.Properties.NullValuePrompt");
            this.txt_Start.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txt_Start.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl1
            // 
            resources.ApplyResources(this.labelControl1, "labelControl1");
            this.labelControl1.Name = "labelControl1";
            // 
            // labelControl2
            // 
            resources.ApplyResources(this.labelControl2, "labelControl2");
            this.labelControl2.Name = "labelControl2";
            // 
            // btn_ReEvaluate
            // 
            resources.ApplyResources(this.btn_ReEvaluate, "btn_ReEvaluate");
            this.btn_ReEvaluate.Name = "btn_ReEvaluate";
            this.btn_ReEvaluate.Click += new System.EventHandler(this.btn_ReEvaluate_Click);
            // 
            // progressBar1
            // 
            resources.ApplyResources(this.progressBar1, "progressBar1");
            this.progressBar1.Name = "progressBar1";
            this.progressBar1.Step = 1;
            // 
            // btn_PrInv
            // 
            resources.ApplyResources(this.btn_PrInv, "btn_PrInv");
            this.btn_PrInv.Name = "btn_PrInv";
            this.btn_PrInv.Click += new System.EventHandler(this.mi_ReSavePRInvoices_Click);
            // 
            // btn_SLReturnInv
            // 
            resources.ApplyResources(this.btn_SLReturnInv, "btn_SLReturnInv");
            this.btn_SLReturnInv.Name = "btn_SLReturnInv";
            this.btn_SLReturnInv.Click += new System.EventHandler(this.btn_SLReturnInv_Click);
            
            // 
            // btn_RepostAllProcess
            // 
            resources.ApplyResources(this.btn_RepostAllProcess, "btn_RepostAllProcess");
            this.btn_RepostAllProcess.Name = "btn_RepostAllProcess";
            this.btn_RepostAllProcess.Click += new System.EventHandler(this.btn_RepostAllProcess_Click);
            // 
            // btn_IC_Trns_ALL
            // 
            resources.ApplyResources(this.btn_IC_Trns_ALL, "btn_IC_Trns_ALL");
            this.btn_IC_Trns_ALL.Name = "btn_IC_Trns_ALL";
            this.btn_IC_Trns_ALL.Click += new System.EventHandler(this.btn_IC_Trns_ALL_Click);
            // 
            // btnRe_CodingCostCenter
            // 
            resources.ApplyResources(this.btnRe_CodingCostCenter, "btnRe_CodingCostCenter");
            this.btnRe_CodingCostCenter.Name = "btnRe_CodingCostCenter";
            this.btnRe_CodingCostCenter.Click += new System.EventHandler(this.btnRe_CodingCostCenter_Click);
            // 
            // btn_ReCodingAccounts
            // 
            resources.ApplyResources(this.btn_ReCodingAccounts, "btn_ReCodingAccounts");
            this.btn_ReCodingAccounts.Name = "btn_ReCodingAccounts";
            this.btn_ReCodingAccounts.Click += new System.EventHandler(this.btn_ReCodingAccounts_Click);
            // 
            // btn_Re_SerialMontlyJournalCode
            // 
            resources.ApplyResources(this.btn_Re_SerialMontlyJournalCode, "btn_Re_SerialMontlyJournalCode");
            this.btn_Re_SerialMontlyJournalCode.Name = "btn_Re_SerialMontlyJournalCode";
            this.btn_Re_SerialMontlyJournalCode.Click += new System.EventHandler(this.btn_Re_SerialMontlyJournalCode_Click);
            // 
            // frm_ReEvaluate
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.btn_Re_SerialMontlyJournalCode);
            this.Controls.Add(this.btn_ReCodingAccounts);
            this.Controls.Add(this.btnRe_CodingCostCenter);
            this.Controls.Add(this.btn_IC_Trns_ALL);
            this.Controls.Add(this.btn_RepostAllProcess);
            this.Controls.Add(this.btn_StockTaking);
            this.Controls.Add(this.btn_Replacement);
            this.Controls.Add(this.btn_SLReturnInv);
            this.Controls.Add(this.btn_PrInv);
            this.Controls.Add(this.progressBar1);
            this.Controls.Add(this.btn_ReEvaluate);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.txt_Start);
            this.Controls.Add(this.txt_End);
            this.Name = "frm_ReEvaluate";
            ((System.ComponentModel.ISupportInitialize)(this.txt_End.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_End.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Start.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txt_Start.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.DateEdit txt_End;
        private DevExpress.XtraEditors.DateEdit txt_Start;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SimpleButton btn_ReEvaluate;
        private System.Windows.Forms.ProgressBar progressBar1;
        private DevExpress.XtraEditors.SimpleButton btn_PrInv;
        private DevExpress.XtraEditors.SimpleButton btn_SLReturnInv;
        private DevExpress.XtraEditors.SimpleButton btn_Replacement;
        private DevExpress.XtraEditors.SimpleButton btn_StockTaking;
        private DevExpress.XtraEditors.SimpleButton btn_RepostAllProcess;
        private DevExpress.XtraEditors.SimpleButton btn_IC_Trns_ALL;
        private DevExpress.XtraEditors.SimpleButton btnRe_CodingCostCenter;
        private DevExpress.XtraEditors.SimpleButton btn_ReCodingAccounts;
        private DevExpress.XtraEditors.SimpleButton btn_Re_SerialMontlyJournalCode;
    }
}