﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;
using DAL.Res;
using System.Linq;
using Pharmacy.HR;
using DevExpress.XtraGrid.Views.Grid;

namespace Pharmacy.Forms
{
    public partial class frm_SL_Customer : DevExpress.XtraEditors.XtraForm
    {
        int CustId;
        DataTable dt_SalesEmps = new DataTable();
        DataTable dt_VisitsDays = new DataTable();

        UserPriv prvlg;
        FormAction action = FormAction.None;
        List<SL_CustomerPhoto> lstNewCustPhotos = new List<SL_CustomerPhoto>();
        List<SL_CustomerCategoryInfo> lst_groups = new List<SL_CustomerCategoryInfo>();
        string dfltCstGrpNum;

        bool DataModified;

        bool hasSeparateAcc;
        int accountId;

        public frm_SL_Customer()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
            action = FormAction.Add;
        }

        public frm_SL_Customer(int customer_id)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);
            CustId = customer_id;
            action = FormAction.Edit;
        }

        private void frm_SL_Customer_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            xtraTabControl1.SelectedTabPage = tab_main;
            if (Shared.TaxAvailable == false)
                chk_IsTaxable.Checked = chk_IsTaxable.Enabled = false;

            //check customers Account
            if (!Shared.st_Store.CustomersAcc.HasValue)
            {
                XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.MsgCustomersAcc : ResAr.MsgCustomersAcc,
                        Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);

                this.BeginInvoke(new MethodInvoker(this.Close));
            }

            LoadPrivilege();
            BindDataSources();
            GetCustData();
        }

        private void frm_SL_Customer_Shown(object sender, EventArgs e)
        {
            xtraTabControl1.SelectedTabPage = tab_main;
            txtCusCode.Focus();
        }

        private void frm_SL_Customer_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.PageUp)
            {
                btnPrev.PerformClick();
            }
            if (e.KeyCode == Keys.PageDown)
            {
                btnNext.PerformClick();
            }
        }

        private void frm_SL_Customer_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                e.Cancel = true;
        }


        private void btnPrev_Click(object sender, EventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            ERPDataContext DB = new ERPDataContext();

            int lastCust = (from x in DB.SL_Customers
                            where x.CustomerId < CustId
                            orderby x.CustomerId descending
                            select x.CustomerId).FirstOrDefault();

            if (lastCust != 0)
            {
                CustId = lastCust;
                GetCustData();
            }
            else
            {
                lastCust = (from x in DB.SL_Customers
                            where x.CustomerId == CustId
                            orderby x.CustomerId descending
                            select x.CustomerId).FirstOrDefault();

                if (lastCust != 0)
                {
                    CustId = lastCust;
                    GetCustData();
                }
            }
        }

        private void btnNext_Click(object sender, EventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            ERPDataContext DB = new ERPDataContext();

            int lastCust = (from x in DB.SL_Customers
                            where x.CustomerId > CustId
                            orderby x.CustomerId ascending
                            select x.CustomerId).FirstOrDefault();

            if (lastCust != 0)
            {
                CustId = lastCust;
                GetCustData();
            }
            else
            {
                lastCust = (from x in DB.SL_Customers
                            orderby x.CustomerId ascending
                            select x.CustomerId).FirstOrDefault();

                if (lastCust != 0)
                {
                    CustId = lastCust;
                    GetCustData();
                }
            }
        }


        private void barBtn_List_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_SL_CustomerList)))
            {
                frm_SL_CustomerList frm = new frm_SL_CustomerList();
                frm.BringToFront();
                frm.Show();
            }
            else
                Application.OpenForms["frm_SL_CustomerList"].BringToFront();

        }

        private void barBtn_Save_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (!ValidData())
                return;
            SaveData();
            XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgSave : ResSLAr.MsgSave, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void barBtn_Delete_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (action == FormAction.Add)
                return;

            if (CustId <= 0)
                return;

            /*if (CustId == 1 && txtCusNameAr.Text.Trim() == "عميل عام")
            {
                if(XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.txtCantDeleteGCustomer: ResSLAr.txtCantDeleteGCustomer,
                    Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.YesNo, MessageBoxIcon.Warning)
                    == DialogResult.No)
                return;
            }*/


            DialogResult DR = XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgAskDeleteCust : ResSLAr.MsgAskDeleteCust,
                Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2);
            if (DR == DialogResult.Yes)
            {
                DAL.ERPDataContext DB = new DAL.ERPDataContext();
                var cust = (from c in DB.SL_Customers
                            where c.CustomerId == CustId
                            select c).SingleOrDefault();

                string accNum = DB.ACC_Accounts.Where(x => x.AccountId == cust.AccountId).Select(x => x.AcNumber).FirstOrDefault();
                var custPhotos = from c in DB.SL_CustomerPhotos
                                 where c.CustId == CustId
                                 select c;

                //var customerVists = DB.SL_CustomerVisits.Where(x => x.idCustomer == CustId);

                //if (customerVists.Any())
                //    DB.SL_CustomerVisits.DeleteAllOnSubmit(DB.SL_CustomerVisits.Where(x => x.idCustomer == CustId));

                //test if there is JobOrders, SalesOrders, SalesQoutes , Inventory Transactions for this customer
                //if (DB.SL_Quotes.Where(x => x.CustomerId == CustId).Select(x => x.SL_QuoteId).Union(
                //    DB.SL_SalesOrders.Where(x => x.CustomerId == CustId).Select(x => x.SL_SalesOrderId)).Union(
                //    DB.SL_Invoices.Where(x => x.CustomerId == CustId).Select(x => x.SL_InvoiceId)).Union(
                //    DB.SL_Returns.Where(x => x.CustomerId == CustId).Select(x => x.SL_ReturnId)).Union(

                //    DB.JO_JobOrders.Where(x => x.CustomerId == CustId).Select(x => x.JobOrderId)).Union(
                //    DB.IC_InTrns.Where(x => x.IsVendor == (byte)IsVendor.Customer && x.VendorId == CustId).Select(x => x.InTrnsId)).Union(
                //    DB.IC_OutTrns.Where(x => x.IsVendor == (byte)IsVendor.Customer && x.CustomerId == CustId).Select(x => x.OutTrnsId)).Union(

                //    DB.ACC_CashNotes.Where(x => x.IsVendor == (byte)IsVendor.Customer && x.DealerId == CustId).Select(x => x.NoteId)).Union(
                //    DB.ACC_NotesPayables.Where(x => x.IsVendor == false && x.DealerId == CustId).Select(x => x.PayNoteId)).Union(
                //    DB.ACC_NotesReceivables.Where(x => x.IsVendor == false && x.DealerId == CustId).Select(x => x.ReceiveId)).Count() > 0)
                //{
                //    XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgDelCustDenied : ResSLAr.MsgDelCustDenied,
                //                           "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                //    return;
                //}

                // can't delete customer in other group, if group is mandatory for user
                if (Shared.user.DefaultCustGrp != 0 &&
                    (cust.CategoryId == null ||
                    (cust.CategoryId != null && accNum.StartsWith(dfltCstGrpNum) == false)))
                {

                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResSLEn.DfltCustGrp : ResSLAr.DfltCustGrp,
                        Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);

                    return;
                }

                if (cust.AccountId != null)
                {
                    var Account = (from a in DB.ACC_Accounts
                                   where a.AccountId == cust.AccountId
                                   select a).Single();
                    var journalDetails = (from a in DB.ACC_JournalDetails
                                          where a.AccountId == cust.AccountId
                                          select a.AccountId).Count();

                    if (hasSeparateAcc && journalDetails < 1)
                    {
                        if (DB.SL_Customers.Where(c => c.AccountId == cust.AccountId).Count() > 1 ||
                            DB.PR_Vendors.Where(c => c.AccountId == cust.AccountId).Count() > 0)
                        {
                            XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.msgDelLinked : ResSLAr.msgDelLinked,
                              "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            return;
                        }
                        else
                        {
                            DB.SL_CustomerPhotos.DeleteAllOnSubmit(custPhotos);
                            DB.ACC_Accounts.DeleteOnSubmit(Account);
                            DB.SL_Customers.DeleteOnSubmit(cust);

                            MyHelper.UpdateST_UserLog(DB, cust.CusCode.ToString(), cust.CusNameAr,
                                (int)FormAction.Delete, (int)FormsNames.SL_Customer);

                            DB.SubmitChanges();
                            XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgDel : ResSLAr.MsgDel,
                                "", MessageBoxButtons.OK, MessageBoxIcon.Information);

                            NewCustomer();
                        }
                    }
                    else if (!hasSeparateAcc)
                    {
                        //DB.SL_CustomerPhotos.DeleteAllOnSubmit(custPhotos);
                        DB.SL_Customers.DeleteOnSubmit(cust);

                        MyHelper.UpdateST_UserLog(DB, cust.CusCode.ToString(), cust.CusNameAr,
                            (int)FormAction.Delete, (int)FormsNames.SL_Customer);

                        DB.SubmitChanges();
                        XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgDel : ResSLAr.MsgDel,
                            "", MessageBoxButtons.OK, MessageBoxIcon.Information);

                        NewCustomer();
                    }
                    else
                    {
                        XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgDeleteJornalsFirst : ResSLAr.MsgDeleteJornalsFirst,
                            Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                else
                {
                    //DB.SL_CustomerPhotos.DeleteAllOnSubmit(custPhotos);
                    DB.SL_Customers.DeleteOnSubmit(cust);

                    MyHelper.UpdateST_UserLog(DB, cust.CusCode.ToString(), cust.CusNameAr,
                        (int)FormAction.Delete, (int)FormsNames.SL_Customer);

                    DB.SubmitChanges();
                    XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgDel : ResSLAr.MsgDel, "", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    NewCustomer();
                }
            }
        }

        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtnNew_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            NewCustomer();
        }


        private void txtCusNameAr_KeyPress(object sender, KeyPressEventArgs e)
        {
            //if (!char.IsLetterOrDigit(e.KeyChar) && e.KeyChar != ' ')
            //e.Handled = e.KeyChar != (char)Keys.Back;
        }

        private void txtCusNameEn_Leave(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(txtCusNameEn.Text) && action == FormAction.Add)
            {
                DAL.ERPDataContext pharm = new DAL.ERPDataContext();
                var name = (from n in pharm.SL_Customers
                            where n.CusNameEn == txtCusNameEn.Text
                            select n.CusNameEn).Count();
                if (name > 0)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgNameExist : ResSLAr.MsgNameExist,
                    Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning); txtCusNameEn.Focus();
                }
            }
        }

        private void txtMaxCredit_KeyPress(object sender, KeyPressEventArgs e)
        {
            //accepts only integer values           
            if (char.IsNumber(e.KeyChar) || e.KeyChar == '.')
            {
            }
            else
            {
                e.Handled = e.KeyChar != (char)Keys.Back;
            }
        }

        private void txtDays_KeyPress(object sender, KeyPressEventArgs e)
        {
            //accepts only integer values           
            if (char.IsNumber(e.KeyChar))
            {
            }
            else
            {
                e.Handled = e.KeyChar != (char)Keys.Back;
            }
        }

        private void txtOpenDebit_Spin(object sender, DevExpress.XtraEditors.Controls.SpinEventArgs e)
        {
            if (e.IsSpinUp == false)
            {
                if (Convert.ToDecimal(((TextEdit)sender).Text) == 0)
                    e.Handled = true;
            }
        }

        private void txtVenCode_Modified(object sender, EventArgs e)
        {
            DataModified = true;
        }


        private void BindDataSources()
        {
            ERPDataContext DB = new ERPDataContext();

            //lkpPriceLevel.Properties.ValueMember = "PriceLevelId";
            //lkpPriceLevel.Properties.DisplayMember = "PLName";
            //lkpPriceLevel.Properties.DataSource = (from st in DB.IC_PriceLevels.AsEnumerable()
            //                                       select new
            //                                       {
            //                                           st.PriceLevelId,
            //                                           st.PLName,
            //                                           IsRatio = st.IsRatio == true ?
            //                                           (Shared.IsEnglish ? ResICEn.FixedRatio : ResICAr.FixedRatio) :
            //                                           (Shared.IsEnglish ? ResICEn.PerItem : ResICAr.PerItem),
            //                                           st.IsRatioIncrease,
            //                                           st.Ratio,
            //                                           Details = st.IsRatio == true && st.IsRatioIncrease == true ?
            //                                           (decimal.ToDouble(st.Ratio)).ToString() + "%" :
            //                                           (st.IsRatio == true && st.IsRatioIncrease == false ?
            //                                           (decimal.ToDouble((st.Ratio * -1))).ToString() + "%" :
            //                                           (Shared.IsEnglish ? ResICEn.variesPerItem : ResICAr.variesPerItem))
            //                                       }).ToList();

            //#region SalesEmp
            //MyHelper.GetSalesEmps(dt_SalesEmps, false, true, Shared.user.DefaultSalesRep);
            //lkp_SalesEmp.Properties.ValueMember = "EmpId";
            //lkp_SalesEmp.Properties.DisplayMember = "EmpName";
            //lkp_SalesEmp.Properties.DataSource = dt_SalesEmps;
            //#endregion

            //#region Collection Employee
            //lkp_CollectEmp.Properties.ValueMember = "EmpId";
            //lkp_CollectEmp.Properties.DisplayMember = "EmpName";
            //lkp_CollectEmp.Properties.DataSource = dt_SalesEmps;
            //#endregion

            //#region Customer Category
            //lkp_Category.Properties.ValueMember = "CustomerGroupId";
            //lkp_Category.Properties.DisplayMember = "CGNameAr";

            //lkp_Category.EditValue = null;
            //lst_groups = MyHelper.GetCustomersCategories();
            //lkp_Category.Properties.DataSource = lst_groups;

            //if (Shared.user.DefaultCustGrp > 0)
            //    dfltCstGrpNum = (from c in DB.SL_CustomerGroups
            //                     join a in DB.ACC_Accounts
            //                     on c.AccountId equals a.AccountId
            //                     where c.CustomerGroupId == Shared.user.DefaultCustGrp
            //                     select a.AcNumber).First();

            //else
            //    dfltCstGrpNum = string.Empty;
            //#endregion

            //#region Delivery
            //lkpDelivery.Properties.ValueMember = "idDelivery";
            //lkpDelivery.Properties.DisplayMember = Shared.IsEnglish ? "DeliveryF" : "Delivery";

            //lkpDelivery.Properties.DataSource = DB.SL_Deliveries;
            //lkpDelivery.EditValue = 0;

            //#endregion

            //#region Customer Group
            //lkp_Group.Properties.DataSource = DB.SL_Group_Customers;
            //lkp_Group.Properties.ValueMember = "GroupId";
            //lkp_Group.Properties.DisplayMember = Shared.IsEnglish ? "NameEn" : "NameAr";
            //#endregion


            //#region Customer Regions
            //lkp_Regions.Properties.DataSource = DB.SL_CustomerRegions;
            //lkp_Regions.Properties.ValueMember = "IdRegion";
            //lkp_Regions.Properties.DisplayMember = Shared.IsEnglish ? "RegionNameF" : "RegionName";
            //#endregion

            #region Customer COUNTRY
            lkp_country.Properties.DataSource = DB.HR_Countries;
            lkp_country.Properties.ValueMember = "CountryId";
            lkp_country.Properties.DisplayMember = "CountryName";
            #endregion

            //FillVisitsGrid();
        }

        void DoValidate()
        {
            txtCusCode.DoValidate();
            txtCusNameAr.DoValidate();
            txtCusNameEn.DoValidate();
            txtAddress.DoValidate();
            txtTel.DoValidate();
            txtMobile.DoValidate();
            txtCity.DoValidate();
            txt_Bank.DoValidate();
            txt_BankAccNum.DoValidate();
            txt_IdNumber.DoValidate();

            txtMaxCredit.DoValidate();
            txtDiscRatio.DoValidate();

            cmbIsCredit.DoValidate();
            dtOpenBalance.DoValidate();
            txtOpenAmount.DoValidate();

            txtEmail.DoValidate();
            txtZip.DoValidate();
            txtFax.DoValidate();
            txtShipping.DoValidate();
            txtManagerName.DoValidate();

            txt_Representative.DoValidate();
            txt_RepresentativeJob.DoValidate();
            txtRepFName.DoValidate();
            txt_RepFJob.DoValidate();


            lkp_SalesEmp.DoValidate();
            lkp_CollectEmp.DoValidate();
            txt_DueDays.DoValidate();

            txtTaxCardNumber.DoValidate();
            txtTaxDepartment.DoValidate();
            txtTaxFileNumber.DoValidate();
            txtTradeRegistry.DoValidate();
            chk_IsTaxable.DoValidate();

            uc_LinkAccount1.lkp_LinkedAcc.DoValidate();
        }

        private void NewCustomer()
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            Reset();
            action = FormAction.Add;

            xtraTabControl1.SelectedTabPage = tab_main;
            txtCusCode.Focus();

            ERPDataContext DB = new ERPDataContext();

            CustId = DB.SL_Customers.Select(x => x.CustomerId).ToList().DefaultIfEmpty(0).Max() + 1;

            try
            {
                int? grpId = null;
                if (lkp_Category.EditValue == null)
                    grpId = null;
                else if (Convert.ToInt32(lkp_Category.EditValue) == 0)
                    grpId = null;
                else
                    grpId = Convert.ToInt32(lkp_Category.EditValue);

                var last_custCode = (from d in DB.SL_Customers
                                     where grpId.HasValue ? d.CategoryId == grpId : d.CategoryId == null
                                     select d.CusCode).ToList();
                int x = last_custCode.DefaultIfEmpty(0).Max() + 1;
                if (x == 1)
                {
                    if (grpId != null)
                    {
                        var group = DB.SL_CustomerGroups.Where(g => g.CustomerGroupId == grpId).First();
                        txtCusCode.Text = group.CustomerGroupCode + "0001";
                    }
                    else
                        txtCusCode.Text = x.ToString();
                }
                else
                    txtCusCode.Text = x.ToString();
            }
            catch
            {
                txtCusCode.Text =
                    (DB.SL_Customers.Select(x => x.CusCode).ToList().DefaultIfEmpty(0).Max() + 1).ToString();
            }

            uc_LinkAccount1.Enabled = true;
            uc_LinkAccount1.rdo_SeparateAcc.Checked = hasSeparateAcc = true;
            accountId = 0;
            chk_IsActive.Checked = true;

            txtCusCode.Focus();
            DoValidate();
            uc_LinkAccount1.DoValidate();
            Validate_Can_Edit_Posted_Journal(null);
        }

        private void GetCustData()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            var cust = (from i in DB.SL_Customers
                        where i.CustomerId == CustId
                        select i).SingleOrDefault();
            if (cust == null)
            {
                NewCustomer();
            }
            else
            {
                CustId = cust.CustomerId;
                txtCusCode.Text = cust.CusCode.ToString();
                txtCusNameAr.Text = cust.CusNameAr;

                if (!cust.CategoryId.HasValue)
                    lkp_Category.EditValue = 0;
                else
                    lkp_Category.EditValue = cust.CategoryId.Value;

                // get default customer delivery
                if (!cust.Delivery.HasValue)
                    lkpDelivery.EditValue = null;
                else
                    lkpDelivery.EditValue = cust.Delivery.Value;

                //mohammad 10-11-2019
                if (!cust.GroupId.HasValue)
                    lkp_Group.EditValue = 0;
                else
                    lkp_Group.EditValue = cust.GroupId.Value;
                /////////////////////////////////////////////////////
                
                //mohammad 10-11-2019
                if (!cust.csType.HasValue)
                    cmbCsType.EditValue = 0;
                else
                    cmbCsType.EditValue = cust.csType.Value;
                /////////////////////////////////////////////////////

                if (!cust.IdRegion.HasValue)
                    lkp_Regions.EditValue = null;
                else
                    lkp_Regions.EditValue = cust.IdRegion.Value;
                /////////////////////////////////////////////////////
                txtCusNameEn.Text = cust.CusNameEn;
                txtAddress.EditValue = cust.Address;
                txtTel.EditValue = cust.Tel;
                txtMobile.EditValue = cust.Mobile;
                txtCity.EditValue = cust.City;
                txt_Bank.EditValue = cust.BankName;
                txt_BankAccNum.EditValue = cust.BankAccNum;
                txtEmail.EditValue = cust.Email;
                txtZip.EditValue = cust.Zip;
                txtFax.EditValue = cust.Fax;
                txt_IdNumber.EditValue = cust.IdNumber;

                txtShipping.EditValue = cust.Shipping;
                txtManagerName.EditValue = cust.Manager;

                txt_Representative.Text = cust.Representative;
                txt_RepresentativeJob.Text = cust.Representative_Job;
                txtRepFName.Text = cust.RepFNAme;
                txt_RepFJob.Text = cust.RepFJob;

                txtMaxCredit.EditValue = cust.MaxCredit.ToString();
                txtDiscRatio.EditValue = (decimal.ToDouble(cust.DiscountRatio) * 100).ToString();
                lkpPriceLevel.EditValue = cust.PriceLevel;

                txt_DueDays.EditValue = cust.DueDaysCount.HasValue ? cust.DueDaysCount.Value : 0;
                lkp_SalesEmp.EditValue = cust.SalesEmpId;
                lkp_CollectEmp.EditValue = cust.CollectEmpId;

                //Tax
                chk_IsTaxable.Checked = cust.IsTaxable;
                txtTaxCardNumber.Text = cust.TaxCardNumber;
                txtTaxDepartment.Text = cust.TaxDepartment;
                txtTaxFileNumber.Text = cust.TaxFileNumber;
                txtTradeRegistry.Text = cust.TradeRegistry;
               
                txt_Street.EditValue = cust.Street;
                txt_Neighborhood.EditValue = cust.Neighborhood;
                //accountId = cust.AccountId.Value;
                //============added data===============//
                txt_Governate.Text = cust.Governate;
                txt_BuildingNumber.Text = cust.BuildingNumber;
                lkp_country.EditValue = cust.CountryId;
                //=======================//
                #region openBalance
                //if (cust.HasSeparateAccount)
                //{
                //    decimal balance = 0;
                //    DateTime? openBlncDate = (DateTime?)null;
                //    if (cust.AccountId.HasValue == true)
                //        balance = HelperAcc.Get_Account_OpenBalance(cust.AccountId.Value, out openBlncDate);

                //    if (balance > 0)
                //    {
                //        cmbIsCredit.EditValue = true;
                //        txtOpenAmount.Text = balance.ToString();
                //        dtOpenBalance.EditValue = openBlncDate;//xxx
                //    }
                //    else if (balance < 0)
                //    {
                //        cmbIsCredit.EditValue = false;
                //        txtOpenAmount.Text = (balance * -1).ToString();
                //        dtOpenBalance.EditValue = openBlncDate;//xxx
                //    }
                //    else
                //        cmbIsCredit.EditValue = null;


                //    //if (txtOpenCredit.Text == txtOpenDebit.Text)
                //    //    rd_credit_or_depit.EditValue = null;
                //}
                #endregion

                #region UC_Account
                //if (cust.HasSeparateAccount)
                //{
                //    uc_LinkAccount1.rdo_SeparateAcc.Checked = hasSeparateAcc = true;
                //    uc_LinkAccount1.lkp_LinkedAcc.EditValue = null;
                //}
                //else
                //{
                //    uc_LinkAccount1.rdo_LinkedAcc.Checked = true;
                //    hasSeparateAcc = false;
                //    uc_LinkAccount1.lkp_LinkedAcc.EditValue = cust.AccountId;
                //}

                ////disable if account has transactions
                //var journalsCount = (from a in DB.ACC_JournalDetails
                //                     where a.AccountId == cust.AccountId
                //                     select a.AccountId).Count();
                //if (journalsCount > 0 && cust.HasSeparateAccount)
                //    uc_LinkAccount1.Enabled = false;
                //else
                //    uc_LinkAccount1.Enabled = true;

                #endregion

                #region Load Photos
                lstPhotos.Items.Clear();
                lstNewCustPhotos.Clear();
                //var codes = (from c in DB.SL_CustomerPhotos
                //             where c.CustId == CustId
                //             select c).ToList();
                //foreach (var c in codes)
                //{
                //    lstPhotos.Items.Add(c.Desc);
                //}
                #endregion

                //Block Customer
                chk_IsBlocked.Checked = cust.Is_Blocked;
                //Block Customer
                chk_IsActive.Checked = cust.Is_Active;

                txt_Rep_Phone.Text = cust.Rep_Mobile;
                txt_Rep_ID.Text = cust.Rep_ID;

                txtImageDesc.Text = txtImagePath.Text = string.Empty;
                action = FormAction.Edit;
                DoValidate();
                uc_LinkAccount1.DoValidate();
                DataModified = false;
                uc_LinkAccount1.dataModified = false;
            }

            //var visitDays = DB.SL_CustomerVisits.Where(s => s.idCustomer == CustId);
            //if (visitDays.Count() > 0)
            //{
            //    foreach(var day in visitDays)
            //    {
            //        bandedGridView1.SelectRow(day.idDay);
            //        bandedGridView1.SetRowCellValue(day.idDay, col_Field, day.Field);
            //        bandedGridView1.SetRowCellValue(day.idDay, col_Telephone, day.Telephone);
            //        bandedGridView1.SetRowCellValue(day.idDay, col_AM, day.AM);
            //        bandedGridView1.SetRowCellValue(day.idDay, col_PM, day.PM);
            //        bandedGridView1.SetRowCellValue(day.idDay, col_Collect, day.Collect);
            //        bandedGridView1.SetRowCellValue(day.idDay, col_Sell, day.Sell);
            //        bandedGridView1.SetRowCellValue(day.idDay, col_Notes, day.Notes);
            //    }
            //}
            //Validate_Can_Edit_Posted_Journal(cust);
        }

        private bool Validate_Customer_ArName()
        {
            try
            {
                DAL.ERPDataContext pharm = new DAL.ERPDataContext();
                if (string.IsNullOrEmpty(txtCusNameAr.Text.Trim()))
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgEnterCustomerName : ResSLAr.MsgEnterCustomerName,
                        Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCusNameAr.Focus();
                    return false;
                }
                if (action == FormAction.Add)
                {
                    var name = (from n in pharm.SL_Customers
                                where n.CusNameAr == txtCusNameAr.Text
                                select n.CusNameAr).Count();
                    if (name > 0)
                    {
                        XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgNameExist : ResSLAr.MsgNameExist,
                            Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtCusNameAr.Focus();
                        return false;
                    }
                }
            }
            catch
            {
                XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgIncorrectData : ResSLAr.MsgIncorrectData,
                    Shared.IsEnglish ? ResSLEn.MsgTError : ResSLAr.MsgTError, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCusNameAr.Focus();
                return false;
            }
            return true;
        }

        private bool Validate_Customer_Code()
        {
            try
            {
                DAL.ERPDataContext pharm = new DAL.ERPDataContext();
                if (string.IsNullOrEmpty(txtCusCode.Text))
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgEnterCustomerCode : ResSLAr.MsgEnterCustomerCode,
                        Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCusCode.Focus();
                    return false;
                }

                int cus_code = Convert.ToInt32(txtCusCode.EditValue);
                if (cus_code == 0)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgValidateCustomerCode : ResSLAr.MsgValidateCustomerCode,
                        Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCusCode.Focus();
                    return false;
                }
                if (cus_code != 0 && action == FormAction.Add)
                {
                    var code_exist = pharm.SL_Customers.Where(c => c.CusCode == cus_code).Select(c => c.CusCode).Count();
                    if (code_exist > 0)
                    {
                        XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgNumExist : ResSLAr.MsgNumExist,
                            Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtCusCode.Focus();
                        return false;
                    }
                }
            }
            catch
            {
                XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgIncorrectData : ResSLAr.MsgIncorrectData,
                    Shared.IsEnglish ? ResSLEn.MsgTError : ResSLAr.MsgTError, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCusCode.Focus();
                return false;
            }
            return true;
        }

        private bool Validate_Customer_Mobiles()
        {
            try
            {
                DAL.ERPDataContext pharm = new DAL.ERPDataContext();
                
                //==========================Repeated In Same User====================//
                if (!string.IsNullOrEmpty(txtTel.Text))
                {
                    if (!string.IsNullOrEmpty(txtMobile.Text))
                    {
                        if (txtTel.Text == txtMobile.Text)
                        {
                            XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.repeatPhoneNumber : ResSLAr.repeatPhoneNumber,
                            Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            txtTel.Focus();
                            txtTel.ErrorText = "*";
                            return false;
                        }
                    }
                    if (!string.IsNullOrEmpty(txtFax.Text))
                    {
                        if (txtTel.Text == txtFax.Text)
                        {
                            XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.repeatPhoneNumber : ResSLAr.repeatPhoneNumber,
                           Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            txtTel.Focus();
                            txtTel.ErrorText = "*";
                            return false;
                        }
                    }
                }
                if (!string.IsNullOrEmpty(txtMobile.Text))
                {
                    if (!string.IsNullOrEmpty(txtTel.Text))
                    {
                        if (txtMobile.Text== txtTel.Text)
                        {
                            XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.repeatPhoneNumber : ResSLAr.repeatPhoneNumber,
                            Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            txtMobile.Focus();
                            txtMobile.ErrorText = "*";
                            return false;
                        }
                    }
                    if (!string.IsNullOrEmpty(txtFax.Text))
                    {
                        if (txtFax.Text == txtMobile.Text)
                        {
                            XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.repeatPhoneNumber : ResSLAr.repeatPhoneNumber,
                            Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);

                            txtMobile.Focus();
                            txtMobile.ErrorText = "*"; 
                            return false;
                        }
                    }
                }
                if (!string.IsNullOrEmpty(txtFax.Text))
                {
                    if (!string.IsNullOrEmpty(txtMobile.Text))
                    {
                        if (txtMobile.Text == txtFax.Text)
                        {
                            XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.repeatPhoneNumber : ResSLAr.repeatPhoneNumber,
                           Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            txtFax.Focus();
                            txtFax.ErrorText = "*";
                            return false;
                        }
                    }
                    if (!string.IsNullOrEmpty(txtTel.Text))
                    {
                        if (txtFax.Text == txtTel.Text)
                        {
                            XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.repeatPhoneNumber : ResSLAr.repeatPhoneNumber,
                            Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            txtFax.Focus();
                            txtFax.ErrorText = "*";
                            return false;
                        }
                    }
                }
                //==========================Other User======================//
                if (!string.IsNullOrEmpty(txtTel.Text)) { 
                var Telephone = pharm.SL_Customers.Where(c => (c.Tel == txtTel.Text || c.Mobile == txtTel.Text || c.Fax == txtTel.Text) && c.CustomerId != CustId).Select(c => c.Tel).Count();
                if (Telephone > 0)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.repeatPhoneNumber : ResSLAr.repeatPhoneNumber,
                           Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtTel.Focus();
                        txtTel.ErrorText = "*";
                        return false;

                }
            }
                if (!string.IsNullOrEmpty(txtMobile.Text))
                {
                    var Telephone = pharm.SL_Customers.Where(c => (c.Mobile == txtMobile.Text||c.Tel== txtMobile.Text||c.Fax== txtMobile.Text) && c.CustomerId != CustId).Select(c => c.Mobile).Count();
                    if (Telephone > 0)
                    {
                        XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.repeatPhoneNumber : ResSLAr.repeatPhoneNumber,
                               Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtMobile.Focus();
                        txtMobile.ErrorText = "*";
                        return false;

                    }
                }
                if (!string.IsNullOrEmpty(txtFax.Text))
                {
                    var Telephone = pharm.SL_Customers.Where(c => (c.Mobile == txtFax.Text || c.Tel == txtFax.Text || c.Fax == txtFax.Text) && c.CustomerId != CustId).Select(c => c.Mobile).Count();
                    if (Telephone > 0)
                    {
                        XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.repeatPhoneNumber : ResSLAr.repeatPhoneNumber,
                               Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtFax.Focus();
                        txtFax.ErrorText = "*";
                        return false;

                    }
                }

            }
            catch
            {
                XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgIncorrectData : ResSLAr.MsgIncorrectData,
                    Shared.IsEnglish ? ResSLEn.MsgTError : ResSLAr.MsgTError, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                
                return false;
            }
            return true;
        }
        public void SaveData()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            var customer = (from i in DB.SL_Customers
                            where i.CustomerId == CustId
                            select i).SingleOrDefault();
            if (customer == null)
            {
                action = FormAction.Add;
                customer = new SL_Customer();
            }

            customer.Address = txtAddress.Text;
            customer.CusCode = Convert.ToInt32(txtCusCode.EditValue);
            customer.CusNameAr = txtCusNameAr.Text;//Utilities.CleanSingle(txtCusNameAr.Text);
            customer.CusNameEn = txtCusNameEn.Text;
            customer.Tel = txtTel.Text;
            customer.Mobile = txtMobile.Text;
            customer.City = txtCity.Text;
            customer.BankName = txt_Bank.Text;
            customer.BankAccNum = txt_BankAccNum.Text;

            customer.Email = txtEmail.Text;
            customer.Zip = txtZip.Text;
            customer.Fax = txtFax.Text;
            customer.IdNumber = txt_IdNumber.Text;

            customer.Shipping = txtShipping.Text;
            customer.Manager = txtManagerName.Text;

            customer.Representative = txt_Representative.Text;
            customer.Representative_Job = txt_RepresentativeJob.Text;
            customer.RepFNAme = txtRepFName.Text;
            customer.RepFJob = txt_RepFJob.Text;

            customer.MaxCredit = Convert.ToDecimal(txtMaxCredit.EditValue);
            customer.DiscountRatio = Convert.ToDecimal(txtDiscRatio.EditValue) / (decimal)100;
            customer.DueDaysCount = txt_DueDays.EditValue == null ? 0 : Convert.ToInt32(txt_DueDays.EditValue);

            if (Convert.ToInt32(lkp_Category.EditValue) == 0)
                customer.CategoryId = null;
            else
                customer.CategoryId = Convert.ToInt32(lkp_Category.EditValue);
            
            if (lkpDelivery.EditValue != null)
                customer.Delivery = Convert.ToInt32(lkpDelivery.EditValue);

            //mohammad 10-11-2019
            if (Convert.ToInt32(lkp_Group.EditValue) == 0)
                customer.GroupId = null;
            else
                customer.GroupId = Convert.ToInt32(lkp_Group.EditValue);
            //////////////////////////////////////////////////////////
            customer.csType = Convert.ToInt32(cmbCsType.EditValue); // Customer Type
            //////////////////////////////////////////////////////////
            if (lkp_Regions.EditValue == null) // Customer Region
                customer.IdRegion = null;
            else
                customer.IdRegion = Convert.ToInt32(lkp_Regions.EditValue);
            //////////////////////////////////////////////////////////
            if (lkp_SalesEmp.EditValue == null)
                customer.SalesEmpId = null;
            else
                customer.SalesEmpId = Convert.ToInt32(lkp_SalesEmp.EditValue);
            
            //////////////////////////////////////////////////////////
            if (lkp_CollectEmp.EditValue == null)
                customer.CollectEmpId = null;
            else
                customer.CollectEmpId = Convert.ToInt32(lkp_CollectEmp.EditValue);

            if (lkpPriceLevel.EditValue == null)
                customer.PriceLevel = null;
            else
                customer.PriceLevel = Convert.ToInt32(lkpPriceLevel.EditValue);

            //Tax
            customer.IsTaxable = chk_IsTaxable.Checked;
            customer.TaxCardNumber = txtTaxCardNumber.Text;
            customer.TaxDepartment = txtTaxDepartment.Text;
            customer.TaxFileNumber = txtTaxFileNumber.Text;
            customer.TradeRegistry = txtTradeRegistry.Text;

            //Block Customer
            customer.Is_Blocked = chk_IsBlocked.Checked;

            //Active Customer
            customer.Is_Active = chk_IsActive.Checked;

            customer.Rep_ID = txt_Rep_ID.Text;
            customer.Rep_Mobile = txt_Rep_Phone.Text;

            customer.Street= txt_Street.Text ;
            customer.Neighborhood= txt_Neighborhood.Text ;
            //=======================added data====//
            if (lkp_country.EditValue != null)
                customer.CountryId = Convert.ToInt32(lkp_country.EditValue);
            else
                customer.CountryId = DB.HR_Countries.FirstOrDefault().CountryId;
            customer.Governate = txt_Governate.Text;
            customer.BuildingNumber = txt_BuildingNumber.Text;
            //=======================================//
            if (action == FormAction.Add)
            {
                DB.SL_Customers.InsertOnSubmit(customer);

                MyHelper.UpdateST_UserLog(DB, customer.CusCode.ToString(), customer.CusNameAr,
                (int)FormAction.Add, (int)FormsNames.SL_Customer);
            }
            else
                MyHelper.UpdateST_UserLog(DB, customer.CusCode.ToString(), customer.CusNameAr,
                    (int)FormAction.Edit, (int)FormsNames.SL_Customer);

            //Save new photos
            foreach (SL_CustomerPhoto ph in lstNewCustPhotos)
            {
                ph.CustId = CustId;
                DB.SL_CustomerPhotos.InsertOnSubmit(ph);
            }

            //#region Save Account  
            int AccId = 0;

            //get group dat
            int grpAccId = Convert.ToInt32(lkp_Category.GetColumnValue("AccountId"));

            if (grpAccId == 0)//no group
                grpAccId = Shared.st_Store.CustomersAcc.Value; //parent account is customers account

            if (action == FormAction.Add)
            {
                //if (uc_LinkAccount1.rdo_SeparateAcc.Checked == true)
                {
                    //if (HelperAcc.AccNumGenerated(DB.ACC_Accounts.Where(a => a.AccountId == grpAccId).First()) == "-1")
                    //{
                    //    XtraMessageBox.Show(Shared.IsEnglish == true ? ResAccEn.MsgAccLevel : ResAccAr.MsgAccLevel
                    //, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    //    return;
                    //}

                    customer.HasSeparateAccount = hasSeparateAcc = true;

                    DB.SubmitChanges();
                    CustId = customer.CustomerId;

                    //AccId = HelperAcc.Get_CustomerAccount_Id(CustId, grpAccId, Shared.st_Store.CustomersAcc);
                    uc_LinkAccount1.lkp_LinkedAcc.EditValue = null;
                }
                //else
                //{
                //    customer.AccountId = AccId = Convert.ToInt32(uc_LinkAccount1.lkp_LinkedAcc.EditValue);
                //    customer.HasSeparateAccount = hasSeparateAcc = false;
                //    DB.SubmitChanges();
                //}
            }
            //else if (action == FormAction.Edit)
            //{
            //    if (!hasSeparateAcc)
            //    {
            //        if (uc_LinkAccount1.rdo_SeparateAcc.Checked == true)
            //        {
            //            customer.AccountId = null;
            //            customer.HasSeparateAccount = hasSeparateAcc = true;
            //            DB.SubmitChanges();

            //            AccId = HelperAcc.Get_CustomerAccount_Id(CustId, grpAccId, Shared.st_Store.CustomersAcc);
            //            uc_LinkAccount1.lkp_LinkedAcc.EditValue = null;
            //        }
            //        else
            //        {
            //            customer.AccountId = AccId = Convert.ToInt32(uc_LinkAccount1.lkp_LinkedAcc.EditValue);//linked account changed
            //            customer.HasSeparateAccount = hasSeparateAcc = false;

            //            DB.SubmitChanges();
            //        }
            //    }
            //    else if (hasSeparateAcc)
            //    {
            //        if (uc_LinkAccount1.rdo_SeparateAcc.Checked == false)
            //        {
            //            var Account = (from a in DB.ACC_Accounts
            //                           where a.AccountId == customer.AccountId
            //                           select a).Single();
            //            var journalDetails = (from a in DB.ACC_JournalDetails
            //                                  where a.AccountId == customer.AccountId
            //                                  select a.AccountId).Count();

            //            //account not used :- then you can change it
            //            if (journalDetails < 1)
            //            {
            //                DB.ACC_Accounts.DeleteOnSubmit(Account);
            //                customer.AccountId = AccId = Convert.ToInt32(uc_LinkAccount1.lkp_LinkedAcc.EditValue);//linked account changed
            //                customer.HasSeparateAccount = hasSeparateAcc = false;

            //                DB.SubmitChanges();
            //            }
            //            else
            //            {
            //                //account is used :- then you can't change it
            //                XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.msgLinkAcc : ResSLAr.msgLinkAcc,
            //                Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Information);
            //                hasSeparateAcc = true;
            //                AccId = Account.AccountId;
            //                return;
            //            }
            //        }
            //        else
            //        {
            //            //check if account group changed
            //            var Account = (from a in DB.ACC_Accounts
            //                           where a.AccountId == customer.AccountId
            //                           select a).Single();

            //            if (Account.ParentActId != grpAccId)//تم تغيير الفئة
            //            {
            //                //get new parent, than change account number upon
            //                var NewParentAcc = (from a in DB.ACC_Accounts
            //                                    where a.AccountId == grpAccId
            //                                    select a).Single();

            //                Account.ParentActId = NewParentAcc.AccountId;
            //                Account.AcNumber = HelperAcc.AccNumGenerated(NewParentAcc);

            //                DB.SubmitChanges();
            //                AccId = Account.AccountId;
            //            }
            //            else //rename account
            //            {
            //                var AccountRen = (from i in DB.ACC_Accounts
            //                                  where i.AccountId == customer.AccountId
            //                                  select i).SingleOrDefault();
            //                AccountRen.AcNameAr = customer.CusNameAr.Trim();
            //                AccountRen.AcNameEn = customer.CusNameEn.Trim();
            //                AccId = AccountRen.AccountId;

            //                DB.SubmitChanges();
            //            }

            //            hasSeparateAcc = true;
            //        }
            //    }
            //}
            //#endregion            

            //if (uc_LinkAccount1.rdo_SeparateAcc.Checked)
            //{
            //    HelperAcc.Set_Customer_OpenBalance(AccId, Convert.ToDecimal(txtOpenAmount.Text),
            //        dtOpenBalance.EditValue == null ? (DateTime?)null : dtOpenBalance.DateTime,
            //        cmbIsCredit.EditValue == null ? (bool?)null : Convert.ToBoolean(cmbIsCredit.EditValue),
            //        Shared.OfflinePostToGL, Shared.st_Store.CapitalAcc, Shared.st_Store.CustomersAcc);
            //}

            //dt_VisitsDays.AcceptChanges();
            //(gridVisits.FocusedView as GridView).UpdateCurrentRow();

            if ((gridVisits.FocusedView as GridView) != null)
                ++(gridVisits.FocusedView as GridView).FocusedRowHandle;
            gridVisits.DefaultView.PostEditor();
            gridVisits.DefaultView.UpdateCurrentRow();
            dt_VisitsDays.AcceptChanges();

            //DB.SL_CustomerVisits.DeleteAllOnSubmit(DB.SL_CustomerVisits.Where(x => x.idCustomer == CustId));

            //foreach(DataRow d in dt_VisitsDays.Rows)
            //{
            //    if (Convert.ToBoolean(d["dayId"]))
            //    {
            //        SL_CustomerVisit csVisit = new SL_CustomerVisit();
            //        csVisit.idCustomer = CustId;
            //        csVisit.idDay = Convert.ToInt32(d["ID"]);
            //        csVisit.Field = Convert.ToBoolean(d["Field"]);
            //        csVisit.Telephone = Convert.ToBoolean(d["Telephone"]);
            //        csVisit.AM = Convert.ToBoolean(d["AM"]);
            //        csVisit.PM = Convert.ToBoolean(d["PM"]);
            //        csVisit.Collect = Convert.ToBoolean(d["Collect"]);
            //        csVisit.Sell = Convert.ToBoolean(d["Sell"]);
            //        csVisit.Notes = Convert.ToString(d["Notes"]);
            //        DB.SL_CustomerVisits.InsertOnSubmit(csVisit);
            //    }
            //}
            
            DB.SubmitChanges();


            //Validate_Can_Edit_Posted_Journal(customer);

            action = FormAction.Edit;
            lstNewCustPhotos.Clear();
            DoValidate();
            uc_LinkAccount1.DoValidate();
            DataModified = false;
            uc_LinkAccount1.dataModified = false;
        }

        private bool ValidData()
        {

            if (action == FormAction.Add)
            {
                if (prvlg != null && !prvlg.CanAdd)
                {
                    // "عفوا, انت لا تمتلك صلاحية انشاء بيان جديد"
                    XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgPrvNew : ResSLAr.MsgPrvNew, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            if (action == FormAction.Edit)
            {
                if (prvlg != null && !prvlg.CanEdit)
                {
                    //"عفوا, انت لا تمتلك صلاحية تعديل هذا البيان"
                    XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgPrvEdit : ResSLAr.MsgPrvEdit, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }

            /*if (CustId == 1 && action == FormAction.Edit && txtCusNameAr.Text.Trim() == "عميل عام")
            {
                if (XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.txtCantEditGCustomer : ResSLAr.txtCantEditGCustomer,
                    Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.YesNo, MessageBoxIcon.Question)
                    == DialogResult.No)
                    return false;
            }*/

            if (Shared.user.DefaultCustGrp != 0 &&
                    (Convert.ToInt32(lkp_Category.EditValue) == 0 ||
                    (Convert.ToInt32(lkp_Category.EditValue) != 0 &&
                    lst_groups.Where(x => x.CustomerGroupId == Convert.ToInt32(lkp_Category.EditValue)).Select(x => x.AcNumber).First().
                    StartsWith(dfltCstGrpNum) == false
                    )))
            {
                XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResSLEn.DfltCustGrp : ResSLAr.DfltCustGrp,
                        Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);

                return false;
            }
            if (Validate_Customer_Mobiles() == false)
                return false;
            if (Convert.ToDecimal(txtOpenAmount.EditValue) > 0 && Shared.st_Store.CapitalAcc.HasValue == false)
            {
                //يجب تحديد حساب رأس المال

                XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.MsgCapitalAcc : ResAr.MsgCapitalAcc,
                        Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);

                return false;
            }
            if (Convert.ToDecimal(txtOpenAmount.EditValue) > 0 && dtOpenBalance.EditValue == null)
            {
                XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResEn.openBlncDate : ResAr.openBlncDate,
                    Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                dtOpenBalance.Focus();
                return false;
            }

            if (Validate_Customer_Code() == false)
                return false;
            if (Validate_Customer_ArName() == false)
                return false;
          
          
            if (uc_LinkAccount1.rdo_LinkedAcc.Checked &&
                (
                uc_LinkAccount1.lkp_LinkedAcc.EditValue == null ||
                string.IsNullOrEmpty(uc_LinkAccount1.lkp_LinkedAcc.EditValue.ToString()) ||
                string.IsNullOrEmpty(uc_LinkAccount1.lkp_LinkedAcc.Text.ToString()))
                )
            {
                uc_LinkAccount1.lkp_LinkedAcc.Focus();
                uc_LinkAccount1.lkp_LinkedAcc.ErrorText = "*";
                return false;
            }


            if (uc_LinkAccount1.rdo_LinkedAcc.Checked && Convert.ToDecimal(txtOpenAmount.Text) > 0)
            {
                XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.msgLinkAccOpen : ResSLAr.msgLinkAccOpen, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return false;
            }

            //if (action == FormAction.Edit && uc_LinkAccount1.rdo_SeparateAcc.Checked)
            //{
            //    int grpAccId = Convert.ToInt32(lkp_Group.GetColumnValue("AccountId"));
            //    if (grpAccId == 0)//no group
            //        grpAccId = HelperAcc.CustomersAcc.Value; //parent account is customers account

            //    ERPDataContext DB = new ERPDataContext();

            //    //check if account group changed
            //    var custo = (from z in  DB.SL_Customers
            //                 where z.CustomerId == CustId
            //                 select z).Single();

            //    var Account = (from a in DB.ACC_Accounts
            //                   where a.AccountId == custo.AccountId
            //                   select a).Single();

            //    if (Account.ParentActId != grpAccId)//تم تغيير الفئة
            //    {
            //        var journalDetails = (from a in DB.ACC_JournalDetails
            //                              where a.AccountId == Account.AccountId
            //                              select a.AccountId).Count();

            //        if (journalDetails > 0)
            //        {
            //            XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgChangeGroup : ResSLAr.MsgChangeGroup, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
            //            return false;   
            //        }
            //    }
            //}

            if (Shared.st_Store.chk_CsTypeValidation == true && cmbCsType.SelectedIndex == 0 && string.IsNullOrEmpty(txt_IdNumber.EditValue.ToString()))
            {
                txt_IdNumber.ErrorText = Shared.IsEnglish ? "You should enter Number ID" : "يجب ادخال الرقم القومى";
                return false;
            }
            else if (Shared.st_Store.chk_CsTypeValidation == true && cmbCsType.SelectedIndex == 1 
                && (string.IsNullOrEmpty(txtTaxCardNumber.EditValue.ToString())))
            {
                xtraTabControl1.SelectedTabPage = tab_main;
                txtTaxCardNumber.ErrorText = Shared.IsEnglish ? "You should enter Tax Card Number" : "يجب ادخال رقم البطاقة الضريبية";
                return false;
            }
            else if (Shared.st_Store.chk_CsTypeValidation == true && cmbCsType.SelectedIndex == 1 
                && (string.IsNullOrEmpty(txtTaxFileNumber.EditValue.ToString())))
            {
                xtraTabControl1.SelectedTabPage = tab_docs;
                txtTaxFileNumber.ErrorText = Shared.IsEnglish ? "You should enter Tax File Number" : "يجب ادخال رقم السجل التجارى";
                return false;
            }

            return true;
        }

        void Reset()
        {
            txtCusCode.Focus();
            txtCusCode.Text =
            txtCusNameAr.Text =
            txtCusNameEn.Text =
            txtAddress.Text =
            txtTel.Text =
            txtCity.Text =
            txt_Bank.Text =
            txt_BankAccNum.Text =
            txtMobile.Text =
            txtEmail.Text =
            txt_IdNumber.Text =
            txtZip.Text =
            txtFax.Text =
            txtShipping.Text =
            txtManagerName.Text =
            txt_Representative.Text =
            txt_RepresentativeJob.Text =
            txtRepFName.Text =
            txt_RepFJob.Text =

            txtTaxCardNumber.Text =
            txtTaxDepartment.Text =
            txtTaxFileNumber.Text =
            txtTradeRegistry.Text = txt_BuildingNumber.Text=txt_Governate.Text =
            string.Empty;

            txtMaxCredit.Text = txtDiscRatio.Text = txt_DueDays.Text = "0";
            lkpPriceLevel.EditValue = null;

            cmbIsCredit.EditValue = null;

            lkpDelivery.EditValue = null;
            lkp_Category.EditValue = 0;
            uc_LinkAccount1.lkp_LinkedAcc.EditValue = null;
            lkp_SalesEmp.EditValue = null;
            lkp_CollectEmp.EditValue = null;
            chk_IsTaxable.Checked = false;
            lkp_country.EditValue = null;
            txtImageDesc.Text = txtImagePath.Text = string.Empty;
            lstPhotos.Items.Clear();
            lstNewCustPhotos.Clear();

            DataModified = false;
            uc_LinkAccount1.dataModified = false;

            dt_VisitsDays.Rows.Clear();
        }

        DialogResult ChangesMade()
        {

            if (DataModified || uc_LinkAccount1.dataModified)
            {
                DialogResult r = XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgDataModified : ResSLAr.MsgDataModified, "", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                if (r == DialogResult.Yes)
                {
                    if (!ValidData())
                        return DialogResult.Cancel;

                    SaveData();
                    return r;
                }
                else if (r == DialogResult.No)
                {
                    // no thing made, continue closing or do next or do previous
                    return r;
                }
                else if (r == DialogResult.Cancel)
                {
                    //cancel form action
                    return r;
                }
            }
            return DialogResult.No;
        }

        #region Photos

        private void btnBrowse_Click(object sender, EventArgs e)
        {
            OpenFileDialog OFD = new OpenFileDialog();
            OFD.Filter = "Images|*.jpg;*.jpeg;*.png;*.bmp;*.gif";
            if (OFD.ShowDialog() == DialogResult.OK)
            {
                txtImagePath.Text = OFD.FileName;
                txtImageDesc.Focus();
            }
        }

        private void btnShowPhotoes_Click(object sender, EventArgs e)
        {
            if (CustId < 1)
                return;

            if (!ErpUtils.IsFormOpen(typeof(frm_HR_EmpPhotos)))
            {
                frm_HR_EmpPhotos f = new frm_HR_EmpPhotos(CustId, 2);
                f.BringToFront();
                f.Show();
            }
            else
                Application.OpenForms["frm_HR_EmpPhotos"].BringToFront();
        }

        private void btnAddEmpPhoho_Click(object sender, EventArgs e)
        {
            if (txtImageDesc.Text.Trim() != string.Empty &&
                txtImagePath.Text != string.Empty)
            {
                System.IO.FileInfo f = new System.IO.FileInfo(txtImagePath.Text.Trim());
                if (f.Length > (10240 * 1024))
                {
                    MessageBox.Show(
                        Shared.IsEnglish == true ? ResHREn.PicSize : ResHRAr.PicSize//"لا يمكن تحميل صوره اكبر من 10 ميجابايت"
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                if (lstPhotos.Items.Count > 0)
                {
                    if (lstPhotos.Items.Contains(txtImageDesc.Text.Trim()))
                    {
                        MessageBox.Show(
                            Shared.IsEnglish == true ? ResHREn.PicExist : ResHRAr.PicExist//"يوجد صوره بهذا الاسم, يجب تغيير اسم الصوره"
                            , "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }
                }
                Image srcImage = Image.FromFile(txtImagePath.Text.Trim());
                SL_CustomerPhoto ai = new SL_CustomerPhoto();
                ai.Photo = ErpUtils.imageToByteArray(srcImage);
                ai.CustId = CustId;
                ai.PhotoDate = MyHelper.Get_Server_DateTime();
                ai.Desc = txtImageDesc.Text;
                lstNewCustPhotos.Add(ai);

                lstPhotos.Items.Add(txtImageDesc.Text);
                txtImagePath.Text = txtImageDesc.Text = string.Empty;
            }
            else
                MessageBox.Show(
                    Shared.IsEnglish == true ? ResHREn.PicSelect : ResHRAr.PicSelect//"يرجى اختيار صورة وإدخال وصف مناسب لها"
                    , "", MessageBoxButtons.OK, MessageBoxIcon.Warning);

        }

        private void btnEditPhoto_Click(object sender, EventArgs e)
        {
            if (lstPhotos.SelectedIndex < 0)
                return;
            if (lstPhotos.Items.Count > 0)
            {
                DialogResult dr = XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResHREn.PicEdit : ResHRAr.PicEdit
                    , "", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning);

                if (dr == DialogResult.OK)
                {



                    ERPDataContext DB = new ERPDataContext();
                    var empPhoto = (from ic in DB.SL_CustomerPhotos
                                    where ic.CustId == CustId &&
                                    ic.Desc == lstPhotos.SelectedItem.ToString()
                                    select ic).FirstOrDefault();
                    if (empPhoto != null)
                    {
                        if (txtImagePath.Text != string.Empty)
                        {
                            Image srcImage = Image.FromFile(txtImagePath.Text.Trim());
                            empPhoto.Photo = ErpUtils.imageToByteArray(srcImage);
                        }

                        empPhoto.CustId = CustId;
                        empPhoto.PhotoDate = MyHelper.Get_Server_DateTime();

                        if (txtImageDesc.Text != string.Empty)
                            empPhoto.Desc = txtImageDesc.Text;

                        DB.SubmitChanges();

                        #region Load Photos
                        lstPhotos.Items.Clear();
                        var codes = (from c in DB.SL_CustomerPhotos
                                     where c.CustId == CustId
                                     select c.Desc).ToList();
                        foreach (var c in codes)
                            lstPhotos.Items.Add(c);
                        #endregion
                    }
                }
            }
        }

        private void btnDeleteEmpPhoto_Click(object sender, EventArgs e)
        {
            DeleteEmpPhoto();
        }

        void DeleteEmpPhoto()
        {
            if (lstPhotos.SelectedIndex < 0)
                return;
            if (lstPhotos.Items.Count > 0)
            {
                DialogResult dr = XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResHREn.PicDel : ResHRAr.PicDel//"هل تريد بالغعل حذف هذه الصوره؟"
                    , "", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning);

                if (dr == DialogResult.OK)
                {
                    ERPDataContext DB = new ERPDataContext();
                    var empPhoto = (from ic in DB.SL_CustomerPhotos
                                    where ic.CustId == CustId &&
                                    ic.Desc == lstPhotos.SelectedItem.ToString()
                                    select ic).FirstOrDefault();
                    if (empPhoto != null)
                    {
                        DB.SL_CustomerPhotos.DeleteOnSubmit(empPhoto);
                        DB.SubmitChanges();
                    }
                    lstPhotos.Items.Remove(lstPhotos.SelectedItem);
                }
            }
        }
        #endregion

        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                prvlg = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.SL_Customer).FirstOrDefault();

                if (!prvlg.CanDel)
                    barBtnDelete.Enabled = false;
                if (!prvlg.CanAdd)
                    barBtnNew.Enabled = false;
            }
        }

        private void barBtnHelp_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "تسجيل عميل جديد");
        }

        private void Validate_Can_Edit_Posted_Journal(SL_Customer cust)
        {
            ERPDataContext DB = new ERPDataContext();

            bool Edit_enabled = (cust != null
                && cust.AccountId != null
                    && Shared.OfflinePostToGL == true
                    && Shared.user.UserEditPostedBills == false
                    && (from jd in DB.ACC_JournalDetails
                        join j in DB.ACC_Journals on jd.JournalId equals j.JournalId
                        where jd.AccountId == cust.AccountId
                        && j.ProcessId == (int)Process.OpenBalance
                        select j.IsPosted).FirstOrDefault() == true);

            cmbIsCredit.Properties.ReadOnly = Edit_enabled;
            dtOpenBalance.Properties.ReadOnly = Edit_enabled;
            txtOpenAmount.Properties.ReadOnly = Edit_enabled;
        }

        private void lkp_Group_EditValueChanged(object sender, EventArgs e)
        {
            //get account type, upon customer group
            if (action == FormAction.Add)
            {
                SL_CustomerCategoryInfo g = lst_groups.Where(x => x.CustomerGroupId == Convert.ToInt32(lkp_Category.EditValue)).Select(x => x).FirstOrDefault();
                if (g != null)
                {
                    if (g.CustomersHaveSeparateAccount)
                    {
                        uc_LinkAccount1.rdo_SeparateAcc.Checked = true;
                        uc_LinkAccount1.rdo_LinkedAcc.Checked = false;
                        uc_LinkAccount1.lkp_LinkedAcc.EditValue = null;
                    }
                    else if (g.CustomersHaveSeparateAccount == false)
                    {
                        uc_LinkAccount1.rdo_SeparateAcc.Checked = false;
                        uc_LinkAccount1.rdo_LinkedAcc.Checked = true;
                        uc_LinkAccount1.lkp_LinkedAcc.EditValue = g.CustomersDefaultAccount.HasValue ?
                            g.CustomersDefaultAccount.Value : 1;
                    }
                }
            }

            ERPDataContext DB = new ERPDataContext();
            try
            {
                //if (action == FormAction.Edit)
                //    return;

                int? grpId = null;
                if (lkp_Category.EditValue == null)
                    grpId = null;
                else if (Convert.ToInt32(lkp_Category.EditValue) == 0)
                    grpId = null;
                else
                    grpId = Convert.ToInt32(lkp_Category.EditValue);

                //عند فتح بيانات عميل، وتغيير الفئه، ثم العوده للفئه المسجله له، لايجب ان يتغير كود العميل
                if (action == FormAction.Edit)
                {
                    SL_Customer cust = DB.SL_Customers.Where(s => s.CustomerId == CustId).First();
                    if (cust.CategoryId == grpId)
                    {
                        txtCusCode.Text = cust.CusCode.ToString();
                        return;
                    }
                }

                var last_custCode = (from d in DB.SL_Customers
                                     where grpId.HasValue ? d.CategoryId == grpId : d.CategoryId == null
                                     select d.CusCode).ToList();
                int x = last_custCode.DefaultIfEmpty(0).Max() + 1;

                if (x == 1)
                {
                    if (grpId != null)
                    {
                        var group = DB.SL_CustomerGroups.Where(g => g.CustomerGroupId == grpId).First();
                        txtCusCode.Text = group.CustomerGroupCode + "0001";
                    }
                    else
                        txtCusCode.Text = x.ToString();
                }
                else
                    txtCusCode.Text = x.ToString();
            }
            catch
            {
                txtCusCode.Text =
                    (DB.SL_Customers.Select(x => x.CusCode).ToList().DefaultIfEmpty(0).Max() + 1).ToString();
            }
        }

        private void cmbIsCredit_EditValueChanged(object sender, EventArgs e)
        {
            if (cmbIsCredit.EditValue == null)
            {
                dtOpenBalance.Enabled = txtOpenAmount.Enabled = false;
                dtOpenBalance.EditValue = null;
                txtOpenAmount.Text = "0";
            }
            else
            {
                dtOpenBalance.Enabled = txtOpenAmount.Enabled = true;
            }
        }

        public void addRows( List<SL_CustomerVisit> custVisits)
        {
            for (int i = 0; i < 7; i++)
            {
                string dayName;
                if (i == Convert.ToInt32(DayOfWeek.Saturday))
                    dayName = Shared.IsEnglish == true ? ResEn.Sat : ResAr.Sat;//"السبت"
                else if (i == Convert.ToInt32(DayOfWeek.Sunday))
                    dayName = Shared.IsEnglish == true ? ResEn.Sun : ResAr.Sun;//"الاحد"
                else if (i == Convert.ToInt32(DayOfWeek.Monday))
                    dayName = Shared.IsEnglish == true ? ResEn.Mon : ResAr.Mon;//"الاتنين"
                else if (i == Convert.ToInt32(DayOfWeek.Tuesday))
                    dayName = Shared.IsEnglish == true ? ResEn.Tus : ResAr.Tus;//"الثلاثاء"
                else if (i == Convert.ToInt32(DayOfWeek.Wednesday))
                    dayName = Shared.IsEnglish == true ? ResEn.Wed : ResAr.Wed;//"الاربعاء"
                else if (i == Convert.ToInt32(DayOfWeek.Thursday))
                    dayName = Shared.IsEnglish == true ? ResEn.Thu : ResAr.Thu;//"الخميس"
                else
                    dayName = Shared.IsEnglish == true ? ResEn.Fri : ResAr.Fri;//"الجمعه"

                DataRow d0 = dt_VisitsDays.NewRow();
                d0["ID"] = i;
                d0["day"] = dayName;
                SL_CustomerVisit csVisit = custVisits.Where(x => x.idDay == i).FirstOrDefault();
                if (csVisit != null)
                {
                    d0["dayId"] = true;
                    d0["AM"] = csVisit.AM.Value;
                    d0["PM"] = csVisit.PM.Value;
                    d0["Sell"] = csVisit.Sell.Value;
                    d0["Collect"] = csVisit.Collect.Value;
                    d0["Field"] = csVisit.Field.Value;
                    d0["Telephone"] = csVisit.Telephone.Value;
                    d0["Notes"] = csVisit.Notes != null ? csVisit.Notes : String.Empty;
                }

                dt_VisitsDays.Rows.Add(d0);
            }
        }

        private void btn_AddRegion_Click(object sender, EventArgs e)
        {
            if (!ErpUtils.IsFormOpen(typeof(frm_CustomerRegion)))
            {
                frm_CustomerRegion frm = new frm_CustomerRegion();
                frm.BringToFront();
                frm.ShowDialog();
                lkp_Regions.Properties.DataSource = new ERPDataContext().SL_CustomerRegions;
            }
            else
                Application.OpenForms["frm_CustomerRegion"].BringToFront();

        }

        private void btnCustomerItems_Click(object sender, EventArgs e)
        {
            if (CustId > 0)
            {
                if (!ErpUtils.IsFormOpen(typeof(frm_IC_Customer_Items)))
                {
                    frm_IC_Customer_Items f = new frm_IC_Customer_Items(CustId);
                    f.BringToFront();
                    f.Show();
                }
                else
                {
                    if (Application.OpenForms["frm_IC_Customer_Items"].WindowState == FormWindowState.Minimized)
                        Application.OpenForms["frm_IC_Customer_Items"].WindowState = FormWindowState.Normal;

                    Application.OpenForms["frm_IC_Customer_Items"].BringToFront();
                }
            }
        }

        private void labelControl40_Click(object sender, EventArgs e)
        {

        }

        private void labelControl39_Click(object sender, EventArgs e)
        {

        }
    }
}