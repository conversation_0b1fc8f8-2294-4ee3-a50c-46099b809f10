<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="cell_Total.Weight" type="System.Double, mscorlib">
    <value>0.20948219382031902</value>
  </data>
  <data name="cell_Disc.Weight" type="System.Double, mscorlib">
    <value>0.2840740064718234</value>
  </data>
  <data name="cell_Price.Weight" type="System.Double, mscorlib">
    <value>0.26110793037072721</value>
  </data>
  <data name="cell_UOM.Weight" type="System.Double, mscorlib">
    <value>0.18880758326829</value>
  </data>
  <data name="cell_Qty.Weight" type="System.Double, mscorlib">
    <value>0.1699268742386546</value>
  </data>
  <data name="cell_ItemName.Weight" type="System.Double, mscorlib">
    <value>0.45816519892853463</value>
  </data>
  <data name="cell_code.Weight" type="System.Double, mscorlib">
    <value>0.11278916442292035</value>
  </data>
  <data name="Detail.HeightF" type="System.Single, mscorlib">
    <value>30</value>
  </data>
  <data name="cell_MediumUOMQty.Text" xml:space="preserve">
    <value>الكمية الفرعية</value>
  </data>
  <data name="cell_MediumUOMQty.Weight" type="System.Double, mscorlib">
    <value>0.20002279248727634</value>
  </data>
  <data name="cell_MediumUOM.Text" xml:space="preserve">
    <value>الوحدة الفرعية</value>
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="Currency.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>600.343262, 269.9583</value>
  </data>
  <data name="Currency.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>TopRight</value>
  </data>
  <data name="xrLabel11.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>702.2186, 269.9583</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="xrLabel11.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>51.39087, 24.49997</value>
  </data>
  <data name="xrLabel11.Text" xml:space="preserve">
    <value>العملة</value>
  </data>
  <data name="lbl_Updated.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_Updated.Text" xml:space="preserve">
    <value>..</value>
  </data>
  <data name="lbl_Updated.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_Updated.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="xrLabel4.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>683.5415, 196.000015</value>
  </data>
  <data name="lbl_notes.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>294.723663, 196.000015</value>
  </data>
  <data name="xrLabel6.Text" xml:space="preserve">
    <value>المستخدم</value>
  </data>
  <data name="lbl_Paymethod.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>120.755989, 171.500031</value>
  </data>
  <data name="xrLabel1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>683.5415, 171.500031</value>
  </data>
  <data name="xrLabel1.Text" xml:space="preserve">
    <value>المورد</value>
  </data>
  <data name="lbl_Serial.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>195.755951, 171.500031</value>
  </data>
  <data name="lblReportName.Text" xml:space="preserve">
    <value>فاتورة مشتريات</value>
  </data>
  <data name="xrLabel12.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>181.296844, 195.999985</value>
  </data>
  <data name="lbl_Drawer.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>118.796837, 195.999985</value>
  </data>
  <data name="xrLabel8.Text" xml:space="preserve">
    <value>الفرع</value>
  </data>
  <data name="xrLabel7.Text" xml:space="preserve">
    <value>التاريخ</value>
  </data>
  <data name="lbl_Vendor.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>294.723663, 171.500031</value>
  </data>
  <data name="TopMargin.HeightF" type="System.Single, mscorlib">
    <value>311.166656</value>
  </data>
  <data name="xrTable5.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>80.7351456, 9.999974</value>
  </data>
  <data name="cell_SalesTax.Text" xml:space="preserve">
    <value>ضريبة المبيعات</value>
  </data>
  <data name="cell_code2.Text" xml:space="preserve">
    <value>كود 2</value>
  </data>
  <data name="cell_Expire.Text" xml:space="preserve">
    <value>تاريخ الانتهاء</value>
  </data>
  <data name="cell_DiscountRatio2.Text" xml:space="preserve">
    <value>نسبة الخصم 2</value>
  </data>
  <data name="cell_DiscountRatio3.Text" xml:space="preserve">
    <value>نسبة الخصم 3</value>
  </data>
  <data name="cell_SalesTaxRatio.Text" xml:space="preserve">
    <value>نسبة الضريبة</value>
  </data>
  <data name="cell_PiecesCount.Text" xml:space="preserve">
    <value>cell_PiecesCount</value>
  </data>
  <data name="cell_ManufactureDate.Text" xml:space="preserve">
    <value>تاريخ الصنع</value>
  </data>
  <data name="cell_ManufactureDate.Weight" type="System.Double, mscorlib">
    <value>0.47820884760122917</value>
  </data>
  <data name="xrTable5.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>614.9886, 25</value>
  </data>
  <data name="BottomMargin.HeightF" type="System.Single, mscorlib">
    <value>50.2808571</value>
  </data>
  <data name="lbl_salesEmp_Job.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>595.7237, 48.0831223</value>
  </data>
  <data name="lbl_ExpensesR.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>495.7237, 48.0831223</value>
  </data>
  <data name="xrTable3.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>162.531921, 23.0831146</value>
  </data>
  <data name="cell_Weight_KG.Text" xml:space="preserve">
    <value>cell_Weight_KG</value>
  </data>
  <data name="cell_ItemDescription.Text" xml:space="preserve">
    <value>وصف الصنف</value>
  </data>
  <data name="cell_ItemDescription.Weight" type="System.Double, mscorlib">
    <value>0.5950170257151528</value>
  </data>
  <data name="cell_AudiencePrice.Text" xml:space="preserve">
    <value>Audience Price</value>
  </data>
  <data name="cell_Height.Text" xml:space="preserve">
    <value>الارتفاع</value>
  </data>
  <data name="cell_Width.Text" xml:space="preserve">
    <value>العرض</value>
  </data>
  <data name="cell_Length.Text" xml:space="preserve">
    <value>الطول</value>
  </data>
  <data name="cell_TotalQty.Text" xml:space="preserve">
    <value>إجمالي الكمية</value>
  </data>
  <data name="cell_DiscountRatio.Weight" type="System.Double, mscorlib">
    <value>0.20276700059737479</value>
  </data>
  <data name="xrTable3.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>613.483765, 25</value>
  </data>
  <data name="lbl_TaxR.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>395.7237, 48.0831223</value>
  </data>
  <data name="xrTable4.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>270.7237, 48.0831223</value>
  </data>
  <data name="cell_Pack.Text" xml:space="preserve">
    <value>Pack</value>
  </data>
  <data name="Cell_MUOM.Weight" type="System.Double, mscorlib">
    <value>0.20563037969499964</value>
  </data>
  <data name="xrTable4.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_DeductTaxV.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>540.5153, 77.9584961</value>
  </data>
  <data name="lbl_DeductTaxV.Text" xml:space="preserve">
    <value>قيمة ضريبة الخصم</value>
  </data>
  <data name="lbl_DiscountR.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>263.063019, 77</value>
  </data>
  <data name="lbl_DriverName.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>325.563019, 77</value>
  </data>
  <data name="lbl_VehicleNumber.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>375.563019, 77</value>
  </data>
  <data name="lbl_Destination.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>438.063019, 77</value>
  </data>
  <data name="lbl_ScaleWeightSerial.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>488.063019, 77</value>
  </data>
  <data name="xrTable1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>8.58332348, 0</value>
  </data>
  <data name="xrTableCell1.Text" xml:space="preserve">
    <value>الإجمالي</value>
  </data>
  <data name="xrTableCell1.Weight" type="System.Double, mscorlib">
    <value>0.21333718746556168</value>
  </data>
  <data name="xrTableCell9.Text" xml:space="preserve">
    <value>قيمة الخصم</value>
  </data>
  <data name="xrTableCell9.Weight" type="System.Double, mscorlib">
    <value>0.28930169767667541</value>
  </data>
  <data name="xrTableCell7.Text" xml:space="preserve">
    <value>السعر</value>
  </data>
  <data name="xrTableCell7.Weight" type="System.Double, mscorlib">
    <value>0.26591283959677692</value>
  </data>
  <data name="xrTableCell5.Text" xml:space="preserve">
    <value>وحدة القياس</value>
  </data>
  <data name="xrTableCell5.Weight" type="System.Double, mscorlib">
    <value>0.19228227233642878</value>
  </data>
  <data name="xrTableCell6.Text" xml:space="preserve">
    <value>الكمية</value>
  </data>
  <data name="xrTableCell6.Weight" type="System.Double, mscorlib">
    <value>0.17305389000300109</value>
  </data>
  <data name="xrTableCell3.Text" xml:space="preserve">
    <value>اسم الصنف</value>
  </data>
  <data name="xrTableCell3.Weight" type="System.Double, mscorlib">
    <value>0.46890429012780815</value>
  </data>
  <data name="xrTableCell8.Text" xml:space="preserve">
    <value>كود</value>
  </data>
  <data name="xrTableCell8.Weight" type="System.Double, mscorlib">
    <value>0.11015392830429736</value>
  </data>
  <data name="xrTable1.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>742.3751, 36.125</value>
  </data>
  <data name="lbl_TotalPacks.Text" xml:space="preserve">
    <value>lbl_TotalPacks</value>
  </data>
  <data name="lbl_TotalPacks.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_BalanceAfter.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_BalanceAfter.Text" xml:space="preserve">
    <value> </value>
  </data>
  <data name="lbl_BalanceAfter.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lbl_BalanceAfter.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_BalanceBefore.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_BalanceBefore.Text" xml:space="preserve">
    <value> </value>
  </data>
  <data name="lbl_BalanceBefore.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lbl_BalanceBefore.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_totalPieces.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>474.963837, 123.879562</value>
  </data>
  <data name="lbl_totalPieces.Text" xml:space="preserve">
    <value>lbl_VendorAddress</value>
  </data>
  <data name="lbl_totalPieces.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_VendorAddress.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>307.570465, 123.879562</value>
  </data>
  <data name="lbl_VendorAddress.Text" xml:space="preserve">
    <value>lbl_VendorAddress</value>
  </data>
  <data name="lbl_VendorAddress.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_TotalQty.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>436.490875, 176.046249</value>
  </data>
  <data name="lbl_TotalQty.Text" xml:space="preserve">
    <value>lbl_TotalQty</value>
  </data>
  <data name="lbl_TotalQty.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_VendorMobile.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>293.28595, 162.5</value>
  </data>
  <data name="lbl_VendorMobile.Text" xml:space="preserve">
    <value>lbl_VendorMobile</value>
  </data>
  <data name="lbl_VendorMobile.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_CusTax.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>7.54165649, 149.401413</value>
  </data>
  <data name="lbl_CusTax.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>93.50001, 25.45842</value>
  </data>
  <data name="xrLabel10.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>101.785439, 150.359955</value>
  </data>
  <data name="xrLabel10.Text" xml:space="preserve">
    <value>ض الجدول</value>
  </data>
  <data name="xrLabel3.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>101.785439, 99.21338</value>
  </data>
  <data name="xrLabel3.Text" xml:space="preserve">
    <value>ضريبة القيمة المضافة</value>
  </data>
  <data name="xrLabel2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>101.785439, 48.1499062</value>
  </data>
  <data name="xrLabel2.Text" xml:space="preserve">
    <value>الإجمالي بعد الخصم</value>
  </data>
  <data name="lblSubTotal.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>7.54165649, 48.5218048</value>
  </data>
  <data name="lblSubTotal.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>93.50001, 25.4584351</value>
  </data>
  <data name="xrLabel5.Text" xml:space="preserve">
    <value>فقط وقدره</value>
  </data>
  <data name="lbl_Remains.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>6.87498236, 263.068268</value>
  </data>
  <data name="lbl_Remains.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>94.1666946, 25.4584045</value>
  </data>
  <data name="lbl_Total.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>7.54165649, 0</value>
  </data>
  <data name="lbl_Total.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>93.50001, 25.45843</value>
  </data>
  <data name="xrLabel16.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>101.785439, 73.68167</value>
  </data>
  <data name="xrLabel16.Text" xml:space="preserve">
    <value>الضريبة</value>
  </data>
  <data name="lbl_Tax.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>7.54165649, 73.960556</value>
  </data>
  <data name="lbl_Tax.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>93.50001, 25.4584274</value>
  </data>
  <data name="lbl_Net.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>6.87498236, 212.151337</value>
  </data>
  <data name="lbl_Net.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>94.1666946, 25.4584656</value>
  </data>
  <data name="lbl_Expenses.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>7.54165649, 123.879562</value>
  </data>
  <data name="lbl_Expenses.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>93.50001, 25.54158</value>
  </data>
  <data name="xrLabel20.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>101.785439, 124.745087</value>
  </data>
  <data name="xrLabel20.Text" xml:space="preserve">
    <value>مصروفات</value>
  </data>
  <data name="xrLabel19.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>101.785439, 22.6182022</value>
  </data>
  <data name="xrLabel19.Text" xml:space="preserve">
    <value>الخصم</value>
  </data>
  <data name="lbl_DiscountV.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>7.54165649, 23.0831146</value>
  </data>
  <data name="lbl_DiscountV.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>93.50001, 25.4584312</value>
  </data>
  <data name="xrLabel15.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>101.785439, 0</value>
  </data>
  <data name="xrLabel15.Text" xml:space="preserve">
    <value>الإجمالي</value>
  </data>
  <data name="xrLabel17.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>101.952423, 263.0682</value>
  </data>
  <data name="xrLabel17.Text" xml:space="preserve">
    <value>المتبقي</value>
  </data>
  <data name="lbl_Paied.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>6.87498236, 237.609863</value>
  </data>
  <data name="lbl_Paied.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>94.1666946, 25.458374</value>
  </data>
  <data name="xrLabel24.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>101.952423, 237.609863</value>
  </data>
  <data name="xrLabel24.Text" xml:space="preserve">
    <value>المدفوع</value>
  </data>
  <data name="xrLabel23.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>101.952423, 212.1514</value>
  </data>
  <data name="xrLabel23.Text" xml:space="preserve">
    <value>الصافي</value>
  </data>
  <data name="lbl_AddTaxV.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>7.54165649, 99.3993149</value>
  </data>
  <data name="lbl_AddTaxV.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>93.50001, 24.4999771</value>
  </data>
  <data name="ReportFooter.HeightF" type="System.Single, mscorlib">
    <value>373.943359</value>
  </data>
  <data name="xrCrossBandLine6.EndPointFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>100, 31.25</value>
  </data>
  <data name="xrCrossBandLine3.EndPointFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>340.625, 31.25</value>
  </data>
  <data name="xrCrossBandBox2.EndPointFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>7.54165649, 32.2916679</value>
  </data>
  <data name="xrCrossBandBox2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>7.54165649, 0</value>
  </data>
  <data name="xrCrossBandBox2.StartPointFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>7.54165649, 0</value>
  </data>
  <data name="xrCrossBandLine7.EndPointFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>500, 32.2916679</value>
  </data>
  <data name="xrCrossBandLine7.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>500, 2.08333325</value>
  </data>
  <data name="xrCrossBandLine7.StartPointFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>500, 2.08333325</value>
  </data>
  <data name="xrCrossBandLine8.EndPointFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>702.21875, 33.2500153</value>
  </data>
  <data name="xrCrossBandLine10.EndPointFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>425, 32.2916679</value>
  </data>
  <data name="xrCrossBandLine10.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>425, 1.04166663</value>
  </data>
  <data name="xrCrossBandLine10.StartPointFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>425, 1.04166663</value>
  </data>
  <data name="xrCrossBandLine1.EndPointFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>225, 31.25</value>
  </data>
  <data name="xrCrossBandLine1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>225, 0</value>
  </data>
  <data name="xrCrossBandLine1.StartPointFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>225, 0</value>
  </data>
  <data name="$this.Margins" type="System.Drawing.Printing.Margins, System.Drawing">
    <value>29, 36, 311, 50</value>
  </data>
</root>