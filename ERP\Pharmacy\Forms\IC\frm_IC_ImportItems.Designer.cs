﻿namespace Pharmacy.Forms
{
    partial class frm_IC_ImportItems
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_IC_ImportItems));
            this.btnSave = new DevExpress.XtraEditors.SimpleButton();
            this.btn_Openfile = new DevExpress.XtraEditors.SimpleButton();
            this.gridControl1 = new DevExpress.XtraGrid.GridControl();
            this.gridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_Code1 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Code2 = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ItemName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Balance = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_PurchasePrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_SellPrice = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_ReorderLevel = new DevExpress.XtraGrid.Columns.GridColumn();
            this.lkpCategory = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.lkpComp = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl6 = new DevExpress.XtraEditors.LabelControl();
            this.chkAutoCode1 = new DevExpress.XtraEditors.CheckEdit();
            this.lkpStore = new DevExpress.XtraEditors.LookUpEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.dtInvoiceDate = new DevExpress.XtraEditors.DateEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.chkIsExpire = new DevExpress.XtraEditors.CheckEdit();
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpCategory.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpComp.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkAutoCode1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpStore.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtInvoiceDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtInvoiceDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkIsExpire.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // btnSave
            // 
            resources.ApplyResources(this.btnSave, "btnSave");
            this.btnSave.Name = "btnSave";
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // btn_Openfile
            // 
            resources.ApplyResources(this.btn_Openfile, "btn_Openfile");
            this.btn_Openfile.Name = "btn_Openfile";
            this.btn_Openfile.Click += new System.EventHandler(this.btn_Openfile_Click);
            // 
            // gridControl1
            // 
            resources.ApplyResources(this.gridControl1, "gridControl1");
            this.gridControl1.Cursor = System.Windows.Forms.Cursors.Default;
            this.gridControl1.EmbeddedNavigator.AccessibleDescription = resources.GetString("gridControl1.EmbeddedNavigator.AccessibleDescription");
            this.gridControl1.EmbeddedNavigator.AccessibleName = resources.GetString("gridControl1.EmbeddedNavigator.AccessibleName");
            this.gridControl1.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("gridControl1.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.gridControl1.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("gridControl1.EmbeddedNavigator.Anchor")));
            this.gridControl1.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("gridControl1.EmbeddedNavigator.BackgroundImage")));
            this.gridControl1.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("gridControl1.EmbeddedNavigator.BackgroundImageLayout")));
            this.gridControl1.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("gridControl1.EmbeddedNavigator.ImeMode")));
            this.gridControl1.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("gridControl1.EmbeddedNavigator.MaximumSize")));
            this.gridControl1.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("gridControl1.EmbeddedNavigator.TextLocation")));
            this.gridControl1.EmbeddedNavigator.ToolTip = resources.GetString("gridControl1.EmbeddedNavigator.ToolTip");
            this.gridControl1.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("gridControl1.EmbeddedNavigator.ToolTipIconType")));
            this.gridControl1.EmbeddedNavigator.ToolTipTitle = resources.GetString("gridControl1.EmbeddedNavigator.ToolTipTitle");
            this.gridControl1.MainView = this.gridView1;
            this.gridControl1.Name = "gridControl1";
            this.gridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gridView1});
            // 
            // gridView1
            // 
            resources.ApplyResources(this.gridView1, "gridView1");
            this.gridView1.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_Code1,
            this.col_Code2,
            this.col_ItemName,
            this.col_Balance,
            this.col_PurchasePrice,
            this.col_SellPrice,
            this.col_ReorderLevel});
            this.gridView1.GridControl = this.gridControl1;
            this.gridView1.Name = "gridView1";
            this.gridView1.OptionsCustomization.AllowColumnMoving = false;
            this.gridView1.OptionsCustomization.AllowFilter = false;
            this.gridView1.OptionsCustomization.AllowGroup = false;
            this.gridView1.OptionsCustomization.AllowQuickHideColumns = false;
            this.gridView1.OptionsCustomization.AllowSort = false;
            this.gridView1.OptionsView.ShowAutoFilterRow = true;
            this.gridView1.OptionsView.ShowGroupPanel = false;
            // 
            // col_Code1
            // 
            resources.ApplyResources(this.col_Code1, "col_Code1");
            this.col_Code1.FieldName = "Code1";
            this.col_Code1.Name = "col_Code1";
            // 
            // col_Code2
            // 
            resources.ApplyResources(this.col_Code2, "col_Code2");
            this.col_Code2.FieldName = "Code2";
            this.col_Code2.Name = "col_Code2";
            // 
            // col_ItemName
            // 
            resources.ApplyResources(this.col_ItemName, "col_ItemName");
            this.col_ItemName.FieldName = "ItemName";
            this.col_ItemName.Name = "col_ItemName";
            // 
            // col_Balance
            // 
            resources.ApplyResources(this.col_Balance, "col_Balance");
            this.col_Balance.FieldName = "Balance";
            this.col_Balance.Name = "col_Balance";
            // 
            // col_PurchasePrice
            // 
            resources.ApplyResources(this.col_PurchasePrice, "col_PurchasePrice");
            this.col_PurchasePrice.FieldName = "PurchasePrice";
            this.col_PurchasePrice.Name = "col_PurchasePrice";
            // 
            // col_SellPrice
            // 
            resources.ApplyResources(this.col_SellPrice, "col_SellPrice");
            this.col_SellPrice.FieldName = "SellPrice";
            this.col_SellPrice.Name = "col_SellPrice";
            // 
            // col_ReorderLevel
            // 
            resources.ApplyResources(this.col_ReorderLevel, "col_ReorderLevel");
            this.col_ReorderLevel.FieldName = "ReorderLevel";
            this.col_ReorderLevel.Name = "col_ReorderLevel";
            // 
            // lkpCategory
            // 
            resources.ApplyResources(this.lkpCategory, "lkpCategory");
            this.lkpCategory.EnterMoveNextControl = true;
            this.lkpCategory.Name = "lkpCategory";
            this.lkpCategory.Properties.AccessibleDescription = resources.GetString("lkpCategory.Properties.AccessibleDescription");
            this.lkpCategory.Properties.AccessibleName = resources.GetString("lkpCategory.Properties.AccessibleName");
            this.lkpCategory.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkpCategory.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkpCategory.Properties.Appearance.FontSizeDelta")));
            this.lkpCategory.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpCategory.Properties.Appearance.FontStyleDelta")));
            this.lkpCategory.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpCategory.Properties.Appearance.GradientMode")));
            this.lkpCategory.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkpCategory.Properties.Appearance.Image")));
            this.lkpCategory.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpCategory.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpCategory.Properties.AppearanceDropDown.FontSizeDelta = ((int)(resources.GetObject("lkpCategory.Properties.AppearanceDropDown.FontSizeDelta")));
            this.lkpCategory.Properties.AppearanceDropDown.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpCategory.Properties.AppearanceDropDown.FontStyleDelta")));
            this.lkpCategory.Properties.AppearanceDropDown.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpCategory.Properties.AppearanceDropDown.GradientMode")));
            this.lkpCategory.Properties.AppearanceDropDown.Image = ((System.Drawing.Image)(resources.GetObject("lkpCategory.Properties.AppearanceDropDown.Image")));
            this.lkpCategory.Properties.AppearanceDropDown.Options.UseTextOptions = true;
            this.lkpCategory.Properties.AppearanceDropDown.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpCategory.Properties.AppearanceDropDownHeader.FontSizeDelta = ((int)(resources.GetObject("lkpCategory.Properties.AppearanceDropDownHeader.FontSizeDelta")));
            this.lkpCategory.Properties.AppearanceDropDownHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpCategory.Properties.AppearanceDropDownHeader.FontStyleDelta")));
            this.lkpCategory.Properties.AppearanceDropDownHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpCategory.Properties.AppearanceDropDownHeader.GradientMode")));
            this.lkpCategory.Properties.AppearanceDropDownHeader.Image = ((System.Drawing.Image)(resources.GetObject("lkpCategory.Properties.AppearanceDropDownHeader.Image")));
            this.lkpCategory.Properties.AppearanceDropDownHeader.Options.UseTextOptions = true;
            this.lkpCategory.Properties.AppearanceDropDownHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkpCategory.Properties.AppearanceFocused.FontSizeDelta = ((int)(resources.GetObject("lkpCategory.Properties.AppearanceFocused.FontSizeDelta")));
            this.lkpCategory.Properties.AppearanceFocused.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpCategory.Properties.AppearanceFocused.FontStyleDelta")));
            this.lkpCategory.Properties.AppearanceFocused.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpCategory.Properties.AppearanceFocused.GradientMode")));
            this.lkpCategory.Properties.AppearanceFocused.Image = ((System.Drawing.Image)(resources.GetObject("lkpCategory.Properties.AppearanceFocused.Image")));
            this.lkpCategory.Properties.AppearanceFocused.Options.UseTextOptions = true;
            this.lkpCategory.Properties.AppearanceFocused.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkpCategory.Properties.AutoHeight = ((bool)(resources.GetObject("lkpCategory.Properties.AutoHeight")));
            this.lkpCategory.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkpCategory.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpCategory.Properties.Buttons"))))});
            this.lkpCategory.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpCategory.Properties.Columns"), resources.GetString("lkpCategory.Properties.Columns1"), ((int)(resources.GetObject("lkpCategory.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpCategory.Properties.Columns3"))), resources.GetString("lkpCategory.Properties.Columns4"), ((bool)(resources.GetObject("lkpCategory.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpCategory.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpCategory.Properties.Columns7"), resources.GetString("lkpCategory.Properties.Columns8"), ((int)(resources.GetObject("lkpCategory.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpCategory.Properties.Columns10"))), resources.GetString("lkpCategory.Properties.Columns11"), ((bool)(resources.GetObject("lkpCategory.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpCategory.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpCategory.Properties.Columns14"), resources.GetString("lkpCategory.Properties.Columns15"), ((int)(resources.GetObject("lkpCategory.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpCategory.Properties.Columns17"))), resources.GetString("lkpCategory.Properties.Columns18"), ((bool)(resources.GetObject("lkpCategory.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpCategory.Properties.Columns20")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpCategory.Properties.Columns21"), resources.GetString("lkpCategory.Properties.Columns22"), ((int)(resources.GetObject("lkpCategory.Properties.Columns23"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpCategory.Properties.Columns24"))), resources.GetString("lkpCategory.Properties.Columns25"), ((bool)(resources.GetObject("lkpCategory.Properties.Columns26"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpCategory.Properties.Columns27"))))});
            this.lkpCategory.Properties.NullText = resources.GetString("lkpCategory.Properties.NullText");
            this.lkpCategory.Properties.NullValuePrompt = resources.GetString("lkpCategory.Properties.NullValuePrompt");
            this.lkpCategory.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkpCategory.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl11
            // 
            resources.ApplyResources(this.labelControl11, "labelControl11");
            this.labelControl11.Name = "labelControl11";
            // 
            // lkpComp
            // 
            resources.ApplyResources(this.lkpComp, "lkpComp");
            this.lkpComp.EnterMoveNextControl = true;
            this.lkpComp.Name = "lkpComp";
            this.lkpComp.Properties.AccessibleDescription = resources.GetString("lkpComp.Properties.AccessibleDescription");
            this.lkpComp.Properties.AccessibleName = resources.GetString("lkpComp.Properties.AccessibleName");
            this.lkpComp.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkpComp.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkpComp.Properties.Appearance.FontSizeDelta")));
            this.lkpComp.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpComp.Properties.Appearance.FontStyleDelta")));
            this.lkpComp.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpComp.Properties.Appearance.GradientMode")));
            this.lkpComp.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkpComp.Properties.Appearance.Image")));
            this.lkpComp.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpComp.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpComp.Properties.AppearanceDropDown.FontSizeDelta = ((int)(resources.GetObject("lkpComp.Properties.AppearanceDropDown.FontSizeDelta")));
            this.lkpComp.Properties.AppearanceDropDown.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpComp.Properties.AppearanceDropDown.FontStyleDelta")));
            this.lkpComp.Properties.AppearanceDropDown.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpComp.Properties.AppearanceDropDown.GradientMode")));
            this.lkpComp.Properties.AppearanceDropDown.Image = ((System.Drawing.Image)(resources.GetObject("lkpComp.Properties.AppearanceDropDown.Image")));
            this.lkpComp.Properties.AppearanceDropDown.Options.UseTextOptions = true;
            this.lkpComp.Properties.AppearanceDropDown.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpComp.Properties.AppearanceDropDownHeader.FontSizeDelta = ((int)(resources.GetObject("lkpComp.Properties.AppearanceDropDownHeader.FontSizeDelta")));
            this.lkpComp.Properties.AppearanceDropDownHeader.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpComp.Properties.AppearanceDropDownHeader.FontStyleDelta")));
            this.lkpComp.Properties.AppearanceDropDownHeader.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpComp.Properties.AppearanceDropDownHeader.GradientMode")));
            this.lkpComp.Properties.AppearanceDropDownHeader.Image = ((System.Drawing.Image)(resources.GetObject("lkpComp.Properties.AppearanceDropDownHeader.Image")));
            this.lkpComp.Properties.AppearanceDropDownHeader.Options.UseTextOptions = true;
            this.lkpComp.Properties.AppearanceDropDownHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkpComp.Properties.AppearanceFocused.FontSizeDelta = ((int)(resources.GetObject("lkpComp.Properties.AppearanceFocused.FontSizeDelta")));
            this.lkpComp.Properties.AppearanceFocused.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpComp.Properties.AppearanceFocused.FontStyleDelta")));
            this.lkpComp.Properties.AppearanceFocused.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpComp.Properties.AppearanceFocused.GradientMode")));
            this.lkpComp.Properties.AppearanceFocused.Image = ((System.Drawing.Image)(resources.GetObject("lkpComp.Properties.AppearanceFocused.Image")));
            this.lkpComp.Properties.AppearanceFocused.Options.UseTextOptions = true;
            this.lkpComp.Properties.AppearanceFocused.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.lkpComp.Properties.AutoHeight = ((bool)(resources.GetObject("lkpComp.Properties.AutoHeight")));
            this.lkpComp.Properties.BestFitMode = DevExpress.XtraEditors.Controls.BestFitMode.BestFitResizePopup;
            this.lkpComp.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpComp.Properties.Buttons"))))});
            this.lkpComp.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpComp.Properties.Columns"), resources.GetString("lkpComp.Properties.Columns1"), ((int)(resources.GetObject("lkpComp.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpComp.Properties.Columns3"))), resources.GetString("lkpComp.Properties.Columns4"), ((bool)(resources.GetObject("lkpComp.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpComp.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpComp.Properties.Columns7"), resources.GetString("lkpComp.Properties.Columns8"), ((int)(resources.GetObject("lkpComp.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpComp.Properties.Columns10"))), resources.GetString("lkpComp.Properties.Columns11"), ((bool)(resources.GetObject("lkpComp.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpComp.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpComp.Properties.Columns14"), resources.GetString("lkpComp.Properties.Columns15"), ((int)(resources.GetObject("lkpComp.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpComp.Properties.Columns17"))), resources.GetString("lkpComp.Properties.Columns18"), ((bool)(resources.GetObject("lkpComp.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpComp.Properties.Columns20"))))});
            this.lkpComp.Properties.NullText = resources.GetString("lkpComp.Properties.NullText");
            this.lkpComp.Properties.NullValuePrompt = resources.GetString("lkpComp.Properties.NullValuePrompt");
            this.lkpComp.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkpComp.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl6
            // 
            resources.ApplyResources(this.labelControl6, "labelControl6");
            this.labelControl6.Name = "labelControl6";
            // 
            // chkAutoCode1
            // 
            resources.ApplyResources(this.chkAutoCode1, "chkAutoCode1");
            this.chkAutoCode1.Name = "chkAutoCode1";
            this.chkAutoCode1.Properties.AccessibleDescription = resources.GetString("chkAutoCode1.Properties.AccessibleDescription");
            this.chkAutoCode1.Properties.AccessibleName = resources.GetString("chkAutoCode1.Properties.AccessibleName");
            this.chkAutoCode1.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("chkAutoCode1.Properties.Appearance.FontSizeDelta")));
            this.chkAutoCode1.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("chkAutoCode1.Properties.Appearance.FontStyleDelta")));
            this.chkAutoCode1.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("chkAutoCode1.Properties.Appearance.GradientMode")));
            this.chkAutoCode1.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("chkAutoCode1.Properties.Appearance.Image")));
            this.chkAutoCode1.Properties.Appearance.Options.UseTextOptions = true;
            this.chkAutoCode1.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chkAutoCode1.Properties.AutoHeight = ((bool)(resources.GetObject("chkAutoCode1.Properties.AutoHeight")));
            this.chkAutoCode1.Properties.Caption = resources.GetString("chkAutoCode1.Properties.Caption");
            this.chkAutoCode1.Properties.DisplayValueChecked = resources.GetString("chkAutoCode1.Properties.DisplayValueChecked");
            this.chkAutoCode1.Properties.DisplayValueGrayed = resources.GetString("chkAutoCode1.Properties.DisplayValueGrayed");
            this.chkAutoCode1.Properties.DisplayValueUnchecked = resources.GetString("chkAutoCode1.Properties.DisplayValueUnchecked");
            this.chkAutoCode1.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("chkAutoCode1.Properties.GlyphAlignment")));
            // 
            // lkpStore
            // 
            resources.ApplyResources(this.lkpStore, "lkpStore");
            this.lkpStore.EnterMoveNextControl = true;
            this.lkpStore.Name = "lkpStore";
            this.lkpStore.Properties.AccessibleDescription = resources.GetString("lkpStore.Properties.AccessibleDescription");
            this.lkpStore.Properties.AccessibleName = resources.GetString("lkpStore.Properties.AccessibleName");
            this.lkpStore.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.lkpStore.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("lkpStore.Properties.Appearance.FontSizeDelta")));
            this.lkpStore.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("lkpStore.Properties.Appearance.FontStyleDelta")));
            this.lkpStore.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("lkpStore.Properties.Appearance.GradientMode")));
            this.lkpStore.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("lkpStore.Properties.Appearance.Image")));
            this.lkpStore.Properties.Appearance.Options.UseTextOptions = true;
            this.lkpStore.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.lkpStore.Properties.AutoHeight = ((bool)(resources.GetObject("lkpStore.Properties.AutoHeight")));
            this.lkpStore.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("lkpStore.Properties.Buttons"))))});
            this.lkpStore.Properties.Columns.AddRange(new DevExpress.XtraEditors.Controls.LookUpColumnInfo[] {
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns"), resources.GetString("lkpStore.Properties.Columns1"), ((int)(resources.GetObject("lkpStore.Properties.Columns2"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns3"))), resources.GetString("lkpStore.Properties.Columns4"), ((bool)(resources.GetObject("lkpStore.Properties.Columns5"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns6")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns7"), resources.GetString("lkpStore.Properties.Columns8"), ((int)(resources.GetObject("lkpStore.Properties.Columns9"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns10"))), resources.GetString("lkpStore.Properties.Columns11"), ((bool)(resources.GetObject("lkpStore.Properties.Columns12"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns13")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns14"), resources.GetString("lkpStore.Properties.Columns15"), ((int)(resources.GetObject("lkpStore.Properties.Columns16"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns17"))), resources.GetString("lkpStore.Properties.Columns18"), ((bool)(resources.GetObject("lkpStore.Properties.Columns19"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns20")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns21"), resources.GetString("lkpStore.Properties.Columns22"), ((int)(resources.GetObject("lkpStore.Properties.Columns23"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns24"))), resources.GetString("lkpStore.Properties.Columns25"), ((bool)(resources.GetObject("lkpStore.Properties.Columns26"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns27")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns28"), resources.GetString("lkpStore.Properties.Columns29"), ((int)(resources.GetObject("lkpStore.Properties.Columns30"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns31"))), resources.GetString("lkpStore.Properties.Columns32"), ((bool)(resources.GetObject("lkpStore.Properties.Columns33"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns34")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns35"), resources.GetString("lkpStore.Properties.Columns36"), ((int)(resources.GetObject("lkpStore.Properties.Columns37"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns38"))), resources.GetString("lkpStore.Properties.Columns39"), ((bool)(resources.GetObject("lkpStore.Properties.Columns40"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns41")))),
            new DevExpress.XtraEditors.Controls.LookUpColumnInfo(resources.GetString("lkpStore.Properties.Columns42"), resources.GetString("lkpStore.Properties.Columns43"), ((int)(resources.GetObject("lkpStore.Properties.Columns44"))), ((DevExpress.Utils.FormatType)(resources.GetObject("lkpStore.Properties.Columns45"))), resources.GetString("lkpStore.Properties.Columns46"), ((bool)(resources.GetObject("lkpStore.Properties.Columns47"))), ((DevExpress.Utils.HorzAlignment)(resources.GetObject("lkpStore.Properties.Columns48"))))});
            this.lkpStore.Properties.NullText = resources.GetString("lkpStore.Properties.NullText");
            this.lkpStore.Properties.NullValuePrompt = resources.GetString("lkpStore.Properties.NullValuePrompt");
            this.lkpStore.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("lkpStore.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl2
            // 
            resources.ApplyResources(this.labelControl2, "labelControl2");
            this.labelControl2.Name = "labelControl2";
            // 
            // dtInvoiceDate
            // 
            resources.ApplyResources(this.dtInvoiceDate, "dtInvoiceDate");
            this.dtInvoiceDate.EnterMoveNextControl = true;
            this.dtInvoiceDate.Name = "dtInvoiceDate";
            this.dtInvoiceDate.Properties.AccessibleDescription = resources.GetString("dtInvoiceDate.Properties.AccessibleDescription");
            this.dtInvoiceDate.Properties.AccessibleName = resources.GetString("dtInvoiceDate.Properties.AccessibleName");
            this.dtInvoiceDate.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.False;
            this.dtInvoiceDate.Properties.AutoHeight = ((bool)(resources.GetObject("dtInvoiceDate.Properties.AutoHeight")));
            this.dtInvoiceDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("dtInvoiceDate.Properties.Buttons"))))});
            this.dtInvoiceDate.Properties.CalendarTimeProperties.AccessibleDescription = resources.GetString("dtInvoiceDate.Properties.CalendarTimeProperties.AccessibleDescription");
            this.dtInvoiceDate.Properties.CalendarTimeProperties.AccessibleName = resources.GetString("dtInvoiceDate.Properties.CalendarTimeProperties.AccessibleName");
            this.dtInvoiceDate.Properties.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.AutoHeight")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.AutoComplete")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.BeepOnError = ((bool)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.BeepOnError")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.EditMask = resources.GetString("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.EditMask");
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.MaskType")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.PlaceHolder = ((char)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.PlaceHolder")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.SaveLiteral")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat")));
            this.dtInvoiceDate.Properties.CalendarTimeProperties.NullValuePrompt = resources.GetString("dtInvoiceDate.Properties.CalendarTimeProperties.NullValuePrompt");
            this.dtInvoiceDate.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dtInvoiceDate.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue")));
            this.dtInvoiceDate.Properties.DisplayFormat.FormatString = "g";
            this.dtInvoiceDate.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.dtInvoiceDate.Properties.EditFormat.FormatString = "g";
            this.dtInvoiceDate.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
            this.dtInvoiceDate.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dtInvoiceDate.Properties.Mask.AutoComplete")));
            this.dtInvoiceDate.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("dtInvoiceDate.Properties.Mask.BeepOnError")));
            this.dtInvoiceDate.Properties.Mask.EditMask = resources.GetString("dtInvoiceDate.Properties.Mask.EditMask");
            this.dtInvoiceDate.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dtInvoiceDate.Properties.Mask.IgnoreMaskBlank")));
            this.dtInvoiceDate.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dtInvoiceDate.Properties.Mask.MaskType")));
            this.dtInvoiceDate.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("dtInvoiceDate.Properties.Mask.PlaceHolder")));
            this.dtInvoiceDate.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("dtInvoiceDate.Properties.Mask.SaveLiteral")));
            this.dtInvoiceDate.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dtInvoiceDate.Properties.Mask.ShowPlaceHolders")));
            this.dtInvoiceDate.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dtInvoiceDate.Properties.Mask.UseMaskAsDisplayFormat")));
            this.dtInvoiceDate.Properties.NullValuePrompt = resources.GetString("dtInvoiceDate.Properties.NullValuePrompt");
            this.dtInvoiceDate.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dtInvoiceDate.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl3
            // 
            resources.ApplyResources(this.labelControl3, "labelControl3");
            this.labelControl3.Name = "labelControl3";
            // 
            // chkIsExpire
            // 
            resources.ApplyResources(this.chkIsExpire, "chkIsExpire");
            this.chkIsExpire.Name = "chkIsExpire";
            this.chkIsExpire.Properties.AccessibleDescription = resources.GetString("chkIsExpire.Properties.AccessibleDescription");
            this.chkIsExpire.Properties.AccessibleName = resources.GetString("chkIsExpire.Properties.AccessibleName");
            this.chkIsExpire.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("chkIsExpire.Properties.Appearance.FontSizeDelta")));
            this.chkIsExpire.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("chkIsExpire.Properties.Appearance.FontStyleDelta")));
            this.chkIsExpire.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("chkIsExpire.Properties.Appearance.GradientMode")));
            this.chkIsExpire.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("chkIsExpire.Properties.Appearance.Image")));
            this.chkIsExpire.Properties.Appearance.Options.UseTextOptions = true;
            this.chkIsExpire.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chkIsExpire.Properties.AutoHeight = ((bool)(resources.GetObject("chkIsExpire.Properties.AutoHeight")));
            this.chkIsExpire.Properties.Caption = resources.GetString("chkIsExpire.Properties.Caption");
            this.chkIsExpire.Properties.DisplayValueChecked = resources.GetString("chkIsExpire.Properties.DisplayValueChecked");
            this.chkIsExpire.Properties.DisplayValueGrayed = resources.GetString("chkIsExpire.Properties.DisplayValueGrayed");
            this.chkIsExpire.Properties.DisplayValueUnchecked = resources.GetString("chkIsExpire.Properties.DisplayValueUnchecked");
            this.chkIsExpire.Properties.GlyphAlignment = ((DevExpress.Utils.HorzAlignment)(resources.GetObject("chkIsExpire.Properties.GlyphAlignment")));
            // 
            // frm_IC_ImportItems
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.chkIsExpire);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.dtInvoiceDate);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.lkpStore);
            this.Controls.Add(this.chkAutoCode1);
            this.Controls.Add(this.lkpComp);
            this.Controls.Add(this.labelControl6);
            this.Controls.Add(this.lkpCategory);
            this.Controls.Add(this.labelControl11);
            this.Controls.Add(this.gridControl1);
            this.Controls.Add(this.btn_Openfile);
            this.Controls.Add(this.btnSave);
            this.Name = "frm_IC_ImportItems";
            this.Load += new System.EventHandler(this.frm_IC_ImportItems_Load);
            ((System.ComponentModel.ISupportInitialize)(this.gridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridView1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpCategory.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpComp.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkAutoCode1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lkpStore.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtInvoiceDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dtInvoiceDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkIsExpire.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraEditors.SimpleButton btnSave;
        private DevExpress.XtraEditors.SimpleButton btn_Openfile;
        private DevExpress.XtraGrid.GridControl gridControl1;
        private DevExpress.XtraGrid.Views.Grid.GridView gridView1;
        private DevExpress.XtraEditors.LookUpEdit lkpCategory;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private DevExpress.XtraEditors.LookUpEdit lkpComp;
        private DevExpress.XtraEditors.LabelControl labelControl6;
        private DevExpress.XtraEditors.CheckEdit chkAutoCode1;
        private DevExpress.XtraEditors.LookUpEdit lkpStore;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.DateEdit dtInvoiceDate;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.CheckEdit chkIsExpire;
        private DevExpress.XtraGrid.Columns.GridColumn col_Code1;
        private DevExpress.XtraGrid.Columns.GridColumn col_Code2;
        private DevExpress.XtraGrid.Columns.GridColumn col_ItemName;
        private DevExpress.XtraGrid.Columns.GridColumn col_Balance;
        private DevExpress.XtraGrid.Columns.GridColumn col_PurchasePrice;
        private DevExpress.XtraGrid.Columns.GridColumn col_SellPrice;
        private DevExpress.XtraGrid.Columns.GridColumn col_ReorderLevel;
    }
}