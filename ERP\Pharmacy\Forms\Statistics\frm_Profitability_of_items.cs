﻿using DAL;
using DAL.Res;
using DevExpress.XtraPrinting;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Pharmacy.Forms
{
    public partial class frm_Profitability_of_items : DevExpress.XtraEditors.XtraForm
    {
        ERPDataContext DB = new ERPDataContext();
        UserPriv prvlg;
        DataTable dt_Graph = new DataTable();
        
        public frm_Profitability_of_items()
        {
           
            RTL.EnCulture(Shared.IsEnglish);

            InitializeComponent();
            RTL.RTL_BarManager(barManager1);
        }

        private void frm_Profitability_of_items_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
            RTL.LTRLayout(this);
            LoadPrivilege();
            dtFromDate.EditValue = MyHelper.Get_Server_DateTime();
            dtToDate.EditValue = MyHelper.Get_Server_DateTime();
            var lstItm = (from i in DB.IC_Items
                          where i.ItemType != (int)DAL.ItemType.MatrixParent &&
                          i.ItemType != (int)DAL.ItemType.Subtotal
                          select new { i.ItemNameAr, i.ItemNameEn, i.ItemId }).ToList();



            chkLst.Properties.DisplayMember = "ItemNameAr";
            chkLst.Properties.ValueMember = "ItemId";
            chkLst.Properties.DataSource = lstItm;

        }

        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                prvlg = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.frm_Profitability_of_items).FirstOrDefault();

                if (!prvlg.CanPrint)
                    barBtnPrint.Enabled = false;
                if (!prvlg.CanAdd)
                    barBtnOk.Enabled = false;
            }
        }

        private void LoadData()
        {
            try
            {
                DateTime From = Convert.ToDateTime(dtFromDate.EditValue);
                DateTime To = Convert.ToDateTime(dtToDate.EditValue);
                //DateTime date_from = dtFromDate.DateTime == DateTime.MinValue ? new DateTime(MyHelper.Get_Server_DateTime().Year, 1, 1) : dtFromDate.DateTime.Date;
                //DateTime date_to = dtToDate.DateTime == DateTime.MinValue ? MyHelper.Get_Server_DateTime().AddDays(1) : dtToDate.DateTime.Date;
                var ItemList = chkLst.Properties.GetItems().GetCheckedValues();


                var Data = (from i in DB.IC_ItemStores
                            where i.ProcessId == (int)Process.SellInvoice
                            where dtFromDate.EditValue == null ? true : (i.InsertTime.Date >= From.Date)
                            where dtToDate.EditValue == null? true : (i.InsertTime.Date <= To.Date)
                            where ItemList.Count()>0? ItemList.Contains(i.ItemId):true
                            join item in DB.IC_Items on i.ItemId equals item.ItemId

                            select new
                            {
                                itemid = i.ItemId,
                                itemNameEn = item.ItemNameEn,
                                itemNameAr = item.ItemNameAr,
                                //CategoryId=ic.CategoryId,

                                SellPrice = i.SellPrice,
                                PurchasePrice = i.PurchasePrice,
                                Profit = i.SellPrice - i.PurchasePrice

                            }).ToList();
                var Result = (from r in Data
                              group r by r.itemid into grp
                              select new
                              {
                                  itemName = Shared.IsEnglish ? grp.Select(a => a.itemNameEn).FirstOrDefault() : grp.Select(a => a.itemNameAr).FirstOrDefault(),
                                  SellPrice = grp.Select(a => a.SellPrice).Sum(),
                                  PurchasePrice = grp.Select(a => a.PurchasePrice).Sum(),
                                  Profit = grp.Select(a => a.SellPrice).Sum() - grp.Select(a => a.PurchasePrice).Sum()
                              }).ToList();
                chartControl1.DataSource = Result;
            }
            catch(Exception ex) { }
        }

        private void barBtnOk_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadData();
        }

        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }
        private void printableComponentLink1_CreateReportHeaderArea(object sender, DevExpress.XtraPrinting.CreateAreaEventArgs e)
        {
            string ReportName = this.Text;
            string dateFilters = string.Empty;
            string otherFilters = string.Empty;

            //create filters line
            if (dtFromDate.EditValue != null && dtToDate.EditValue != null)
                dateFilters = (Shared.IsEnglish == true ? ResAccEn.txtFrom : ResAccAr.txtFrom) +
                    dtFromDate.DateTime.ToShortDateString() +
                    (Shared.IsEnglish == true ? ResAccEn.txtTo : ResAccAr.txtTo) +
                    dtToDate.DateTime.ToShortDateString();

            else if (dtFromDate.EditValue != null && dtToDate.EditValue == null)
                dateFilters =
                    (Shared.IsEnglish == true ? ResAccEn.txtFromDate : ResAccAr.txtFromDate) +
                    dtFromDate.DateTime.ToShortDateString();
            else if (dtFromDate.EditValue == null && dtToDate.EditValue != null)
                dateFilters = (Shared.IsEnglish == true ? ResAccEn.txtToDate : ResAccAr.txtToDate) +
                    dtToDate.DateTime.ToShortDateString();
            else
                dateFilters = "";


            ErpUtils.CreateReportHeader(e, ReportName, dateFilters, otherFilters);
        }
        private void printableComponentLink1_CreateReportFooter(object sender, DevExpress.XtraPrinting.CreateAreaEventArgs e)
        {
            RectangleF recTotal = new RectangleF((float)10, (float)17, 740, (float)25);

            e.Graph.StringFormat = Shared.IsEnglish ? new BrickStringFormat(StringAlignment.Near) : new BrickStringFormat(StringAlignment.Far);
            e.Graph.Font = new Font("Times New Roman", 13, FontStyle.Regular);
            e.Graph.ForeColor = Color.Black;
            e.Graph.DefaultBrickStyle.BorderColor = Color.Transparent;
            e.Graph.BackColor = Color.Snow;


            //string total = txtTotal.Text;

            //e.Graph.DrawString(total, recTotal);            
        }
        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
           
            if (this.Width == 1376)
                chartControl1.Width = this.Width - 300;
            //chartControl1.Height = this.Height - 130;
            PrintingSystem printSystem = new PrintingSystem(this.components);
            PrintableComponentLink printLink;
            if (this.components == null)
                printLink = new PrintableComponentLink();
            else
                printLink = new PrintableComponentLink(this.components);

            ((System.ComponentModel.ISupportInitialize)(printSystem)).BeginInit();

            printSystem.Links.AddRange(new object[] {
            printLink});

            printLink.Component = this.chartControl1;

            printLink.PaperKind = System.Drawing.Printing.PaperKind.A4;
            printLink.Landscape = true;
            printLink.Margins = new System.Drawing.Printing.Margins(5, 5, 135, 50);
            printLink.PrintingSystem = printSystem;
            printLink.PrintingSystemBase = printSystem;

            printLink.CreateMarginalHeaderArea +=
                new DevExpress.XtraPrinting.CreateAreaEventHandler(this.printableComponentLink1_CreateReportHeaderArea);
            printLink.CreateReportFooterArea +=
                new DevExpress.XtraPrinting.CreateAreaEventHandler(this.printableComponentLink1_CreateReportFooter);

            ((System.ComponentModel.ISupportInitialize)(printSystem)).EndInit();

            printLink.CreateDocument();
            printLink.ShowPreview();
            chartControl1.Width = this.Width - 51;

        }
    }
}
