﻿namespace Reports
{
    partial class rpt_SL_SalesOrder
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.Detail = new DevExpress.XtraReports.UI.DetailBand();
            this.TopMargin = new DevExpress.XtraReports.UI.TopMarginBand();
            this.lbl_Updated = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Tel = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Address = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Mobile = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_date = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel1 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel6 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_User = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel8 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Customer = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel4 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLine1 = new DevExpress.XtraReports.UI.XRLine();
            this.xrLabel2 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_SalesEmp = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_DeliverDate = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel7 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_AttnMr = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel14 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel12 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel10 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_notes = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_AttnMr_Job = new DevExpress.XtraReports.UI.XRLabel();
            this.lblReportName = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_store = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Number = new DevExpress.XtraReports.UI.XRLabel();
            this.picLogo = new DevExpress.XtraReports.UI.XRPictureBox();
            this.lblCompName = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Serial = new DevExpress.XtraReports.UI.XRLabel();
            this.BottomMargin = new DevExpress.XtraReports.UI.BottomMarginBand();
            this.PageHeader = new DevExpress.XtraReports.UI.PageHeaderBand();
            this.ReportFooter = new DevExpress.XtraReports.UI.ReportFooterBand();
            this.lbl_totalPieces = new DevExpress.XtraReports.UI.XRLabel();
            this.cell_PiecesCountSum = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLine2 = new DevExpress.XtraReports.UI.XRLine();
            this.xrTable6 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow6 = new DevExpress.XtraReports.UI.XRTableRow();
            this.col_SlInv_Code2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_SlInv_Expire = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_SlInv_Batch = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_SlInv_Length = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_SlInv_Width = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_SlInv_Height = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_SlInv_TotlQty = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_SlInv_PcCount = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_SlInv_Qc = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_Index = new DevExpress.XtraReports.UI.XRTableCell();
            this.Cell_Location = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTable5 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow5 = new DevExpress.XtraReports.UI.XRTableRow();
            this.col_OutTrns_ItemCode2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_OuTrans_Expire = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_OutTrns_Batch = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_OutTrns_Length = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_OutTrns_Width = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_OutTrns_Height = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_OutTrns_TotlQty = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_OutTrns_PcCount = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_OutTrns_Qc = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrPageInfo2 = new DevExpress.XtraReports.UI.XRPageInfo();
            this.xrLabel5 = new DevExpress.XtraReports.UI.XRLabel();
            this.lblTotalWords = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_DiscountV = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Total = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Net = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel9 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_DiscountR = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel11 = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel3 = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_salesEmp_Job = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_Shipping = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_ExpensesR = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_EndDate = new DevExpress.XtraReports.UI.XRLabel();
            this.lbl_TaxR = new DevExpress.XtraReports.UI.XRLabel();
            this.xrTable4 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow4 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_ItemDescription = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_code2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Height = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Width = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Length = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_TotalQty = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Factor = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTable3 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow3 = new DevExpress.XtraReports.UI.XRTableRow();
            this.Cell_MUOM = new DevExpress.XtraReports.UI.XRTableCell();
            this.Cell_MUOM_Factor = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_Expire = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_Batch = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_IsExpire = new DevExpress.XtraReports.UI.XRTableCell();
            this.DetailReport_SL_Order = new DevExpress.XtraReports.UI.DetailReportBand();
            this.Detail1 = new DevExpress.XtraReports.UI.DetailBand();
            this.xrTable2 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow2 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_Total = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Disc = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_DiscountRatio = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Price = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_Qty = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_UOM = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_ItemName = new DevExpress.XtraReports.UI.XRTableCell();
            this.cell_code = new DevExpress.XtraReports.UI.XRTableCell();
            this.ReportHeader = new DevExpress.XtraReports.UI.ReportHeaderBand();
            this.xrTable1 = new DevExpress.XtraReports.UI.XRTable();
            this.tableRow1 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableRow1 = new DevExpress.XtraReports.UI.XRTableRow();
            this.xrTableCell1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell9 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell4 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell7 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell6 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell5 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell3 = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell8 = new DevExpress.XtraReports.UI.XRTableCell();
            this.DetailReport_OutTrns = new DevExpress.XtraReports.UI.DetailReportBand();
            this.Detail2 = new DevExpress.XtraReports.UI.DetailBand();
            this.table2 = new DevExpress.XtraReports.UI.XRTable();
            this.tableRow4 = new DevExpress.XtraReports.UI.XRTableRow();
            this.col_OutTrns_Qty = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_OutTrns_UOM = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_OutTrns_ItemName = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_OutTrns_ItemCode1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_OutTrns_Store = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_OutTrns_Code = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_OutTrns_Date = new DevExpress.XtraReports.UI.XRTableCell();
            this.ReportHeader1 = new DevExpress.XtraReports.UI.ReportHeaderBand();
            this.table1 = new DevExpress.XtraReports.UI.XRTable();
            this.tableRow2 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableRow3 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell4 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell5 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell6 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell7 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell8 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell9 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell10 = new DevExpress.XtraReports.UI.XRTableCell();
            this.ReportFooter1 = new DevExpress.XtraReports.UI.ReportFooterBand();
            this.xrTable7 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow7 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_BillQtySum = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell2 = new DevExpress.XtraReports.UI.XRTableCell();
            this.DetailReport_SlInv = new DevExpress.XtraReports.UI.DetailReportBand();
            this.Detail3 = new DevExpress.XtraReports.UI.DetailBand();
            this.table4 = new DevExpress.XtraReports.UI.XRTable();
            this.tableRow7 = new DevExpress.XtraReports.UI.XRTableRow();
            this.col_SlInv_Qty = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_SlInv_UOM = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_SlInv_ItemName = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_SlInv_Code1 = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_SlInv_Branch = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_SlInv_InvCode = new DevExpress.XtraReports.UI.XRTableCell();
            this.col_SlInv_Date = new DevExpress.XtraReports.UI.XRTableCell();
            this.ReportHeader2 = new DevExpress.XtraReports.UI.ReportHeaderBand();
            this.table3 = new DevExpress.XtraReports.UI.XRTable();
            this.tableRow5 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell18 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableRow6 = new DevExpress.XtraReports.UI.XRTableRow();
            this.tableCell19 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell20 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell21 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell22 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell23 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell24 = new DevExpress.XtraReports.UI.XRTableCell();
            this.tableCell25 = new DevExpress.XtraReports.UI.XRTableCell();
            this.ReportFooter2 = new DevExpress.XtraReports.UI.ReportFooterBand();
            this.xrTable8 = new DevExpress.XtraReports.UI.XRTable();
            this.xrTableRow8 = new DevExpress.XtraReports.UI.XRTableRow();
            this.cell_InvQtySum = new DevExpress.XtraReports.UI.XRTableCell();
            this.xrTableCell10 = new DevExpress.XtraReports.UI.XRTableCell();
            this.GroupFooter1 = new DevExpress.XtraReports.UI.GroupFooterBand();
            this.lbl_itms_totalQty = new DevExpress.XtraReports.UI.XRLabel();
            this.xrLabel13 = new DevExpress.XtraReports.UI.XRLabel();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.table2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.table1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.table4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.table3)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable8)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this)).BeginInit();
            // 
            // Detail
            // 
            this.Detail.HeightF = 19F;
            this.Detail.Name = "Detail";
            this.Detail.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.Detail.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // TopMargin
            // 
            this.TopMargin.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.lbl_Updated,
            this.lbl_Tel,
            this.lbl_Address,
            this.lbl_Mobile,
            this.lbl_date,
            this.xrLabel1,
            this.xrLabel6,
            this.lbl_User,
            this.xrLabel8,
            this.lbl_Customer,
            this.xrLabel4,
            this.xrLine1,
            this.xrLabel2,
            this.lbl_SalesEmp,
            this.lbl_DeliverDate,
            this.xrLabel7,
            this.lbl_AttnMr,
            this.xrLabel14,
            this.xrLabel12,
            this.xrLabel10,
            this.lbl_notes,
            this.lbl_AttnMr_Job,
            this.lblReportName,
            this.lbl_store,
            this.lbl_Number,
            this.picLogo,
            this.lblCompName,
            this.lbl_Serial});
            this.TopMargin.HeightF = 348F;
            this.TopMargin.Name = "TopMargin";
            this.TopMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.TopMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // lbl_Updated
            // 
            this.lbl_Updated.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_Updated.LocationFloat = new DevExpress.Utils.PointFloat(160.5009F, 252F);
            this.lbl_Updated.Name = "lbl_Updated";
            this.lbl_Updated.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Updated.SizeF = new System.Drawing.SizeF(38.37524F, 24.5F);
            this.lbl_Updated.StylePriority.UseFont = false;
            this.lbl_Updated.StylePriority.UseTextAlignment = false;
            this.lbl_Updated.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.lbl_Updated.Visible = false;
            // 
            // lbl_Tel
            // 
            this.lbl_Tel.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_Tel.LocationFloat = new DevExpress.Utils.PointFloat(122.1257F, 252F);
            this.lbl_Tel.Name = "lbl_Tel";
            this.lbl_Tel.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Tel.SizeF = new System.Drawing.SizeF(38.37524F, 24.5F);
            this.lbl_Tel.StylePriority.UseFont = false;
            this.lbl_Tel.StylePriority.UseTextAlignment = false;
            this.lbl_Tel.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.lbl_Tel.Visible = false;
            // 
            // lbl_Address
            // 
            this.lbl_Address.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_Address.LocationFloat = new DevExpress.Utils.PointFloat(83.75047F, 252.5F);
            this.lbl_Address.Name = "lbl_Address";
            this.lbl_Address.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Address.SizeF = new System.Drawing.SizeF(38.37524F, 24.5F);
            this.lbl_Address.StylePriority.UseFont = false;
            this.lbl_Address.StylePriority.UseTextAlignment = false;
            this.lbl_Address.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.lbl_Address.Visible = false;
            // 
            // lbl_Mobile
            // 
            this.lbl_Mobile.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_Mobile.LocationFloat = new DevExpress.Utils.PointFloat(45.37524F, 252.5F);
            this.lbl_Mobile.Name = "lbl_Mobile";
            this.lbl_Mobile.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Mobile.SizeF = new System.Drawing.SizeF(38.37524F, 24.5F);
            this.lbl_Mobile.StylePriority.UseFont = false;
            this.lbl_Mobile.StylePriority.UseTextAlignment = false;
            this.lbl_Mobile.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.lbl_Mobile.Visible = false;
            // 
            // lbl_date
            // 
            this.lbl_date.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_date.LocationFloat = new DevExpress.Utils.PointFloat(7F, 115F);
            this.lbl_date.Name = "lbl_date";
            this.lbl_date.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_date.SizeF = new System.Drawing.SizeF(175F, 24.49999F);
            this.lbl_date.StylePriority.UseFont = false;
            this.lbl_date.StylePriority.UseTextAlignment = false;
            this.lbl_date.Text = "1/2/2013";
            this.lbl_date.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel1
            // 
            this.xrLabel1.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel1.LocationFloat = new DevExpress.Utils.PointFloat(182F, 202.5F);
            this.xrLabel1.Name = "xrLabel1";
            this.xrLabel1.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel1.SizeF = new System.Drawing.SizeF(80.54031F, 24.49998F);
            this.xrLabel1.StylePriority.UseFont = false;
            this.xrLabel1.StylePriority.UseTextAlignment = false;
            this.xrLabel1.Text = "تاريخ التسليم";
            this.xrLabel1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel6
            // 
            this.xrLabel6.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel6.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.xrLabel6.LocationFloat = new DevExpress.Utils.PointFloat(182F, 165F);
            this.xrLabel6.Name = "xrLabel6";
            this.xrLabel6.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel6.SizeF = new System.Drawing.SizeF(80.83298F, 24.49998F);
            this.xrLabel6.StylePriority.UseBorders = false;
            this.xrLabel6.StylePriority.UseFont = false;
            this.xrLabel6.StylePriority.UseTextAlignment = false;
            this.xrLabel6.Text = "المستخدم";
            this.xrLabel6.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_User
            // 
            this.lbl_User.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_User.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_User.LocationFloat = new DevExpress.Utils.PointFloat(6.999999F, 165F);
            this.lbl_User.Name = "lbl_User";
            this.lbl_User.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_User.SizeF = new System.Drawing.SizeF(175F, 24.49998F);
            this.lbl_User.StylePriority.UseBorders = false;
            this.lbl_User.StylePriority.UseFont = false;
            this.lbl_User.StylePriority.UseTextAlignment = false;
            this.lbl_User.Text = "..";
            this.lbl_User.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel8
            // 
            this.xrLabel8.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel8.LocationFloat = new DevExpress.Utils.PointFloat(182F, 140F);
            this.xrLabel8.Name = "xrLabel8";
            this.xrLabel8.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel8.SizeF = new System.Drawing.SizeF(80.83298F, 24.49998F);
            this.xrLabel8.StylePriority.UseFont = false;
            this.xrLabel8.StylePriority.UseTextAlignment = false;
            this.xrLabel8.Text = "الفرع";
            this.xrLabel8.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_Customer
            // 
            this.lbl_Customer.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_Customer.LocationFloat = new DevExpress.Utils.PointFloat(281.5414F, 202.5F);
            this.lbl_Customer.Name = "lbl_Customer";
            this.lbl_Customer.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Customer.SizeF = new System.Drawing.SizeF(399.9999F, 24.49994F);
            this.lbl_Customer.StylePriority.UseFont = false;
            this.lbl_Customer.StylePriority.UseTextAlignment = false;
            this.lbl_Customer.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel4
            // 
            this.xrLabel4.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel4.LocationFloat = new DevExpress.Utils.PointFloat(681.5414F, 277.5F);
            this.xrLabel4.Name = "xrLabel4";
            this.xrLabel4.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel4.SizeF = new System.Drawing.SizeF(68.45868F, 24.49997F);
            this.xrLabel4.StylePriority.UseFont = false;
            this.xrLabel4.StylePriority.UseTextAlignment = false;
            this.xrLabel4.Text = "ملاحظات";
            this.xrLabel4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLine1
            // 
            this.xrLine1.BackColor = System.Drawing.Color.DarkGray;
            this.xrLine1.BorderColor = System.Drawing.Color.DarkGray;
            this.xrLine1.BorderWidth = 0F;
            this.xrLine1.ForeColor = System.Drawing.Color.DarkGray;
            this.xrLine1.LineWidth = 0;
            this.xrLine1.LocationFloat = new DevExpress.Utils.PointFloat(0.9999911F, 193F);
            this.xrLine1.Name = "xrLine1";
            this.xrLine1.SizeF = new System.Drawing.SizeF(749F, 3.541748F);
            this.xrLine1.StylePriority.UseBackColor = false;
            this.xrLine1.StylePriority.UseBorderColor = false;
            this.xrLine1.StylePriority.UseBorderWidth = false;
            this.xrLine1.StylePriority.UseForeColor = false;
            // 
            // xrLabel2
            // 
            this.xrLabel2.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel2.LocationFloat = new DevExpress.Utils.PointFloat(681.5414F, 202.5F);
            this.xrLabel2.Name = "xrLabel2";
            this.xrLabel2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel2.SizeF = new System.Drawing.SizeF(68.45856F, 24.49998F);
            this.xrLabel2.StylePriority.UseFont = false;
            this.xrLabel2.StylePriority.UseTextAlignment = false;
            this.xrLabel2.Text = "العميل";
            this.xrLabel2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_SalesEmp
            // 
            this.lbl_SalesEmp.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_SalesEmp.LocationFloat = new DevExpress.Utils.PointFloat(7F, 227.5F);
            this.lbl_SalesEmp.Name = "lbl_SalesEmp";
            this.lbl_SalesEmp.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_SalesEmp.SizeF = new System.Drawing.SizeF(175F, 24.49997F);
            this.lbl_SalesEmp.StylePriority.UseFont = false;
            this.lbl_SalesEmp.StylePriority.UseTextAlignment = false;
            this.lbl_SalesEmp.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_DeliverDate
            // 
            this.lbl_DeliverDate.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_DeliverDate.LocationFloat = new DevExpress.Utils.PointFloat(7F, 202.5F);
            this.lbl_DeliverDate.Name = "lbl_DeliverDate";
            this.lbl_DeliverDate.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_DeliverDate.SizeF = new System.Drawing.SizeF(175F, 24.49998F);
            this.lbl_DeliverDate.StylePriority.UseFont = false;
            this.lbl_DeliverDate.StylePriority.UseTextAlignment = false;
            this.lbl_DeliverDate.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel7
            // 
            this.xrLabel7.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel7.LocationFloat = new DevExpress.Utils.PointFloat(182F, 115F);
            this.xrLabel7.Name = "xrLabel7";
            this.xrLabel7.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel7.SizeF = new System.Drawing.SizeF(80.83298F, 24.49998F);
            this.xrLabel7.StylePriority.UseFont = false;
            this.xrLabel7.StylePriority.UseTextAlignment = false;
            this.xrLabel7.Text = "التاريخ";
            this.xrLabel7.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_AttnMr
            // 
            this.lbl_AttnMr.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_AttnMr.LocationFloat = new DevExpress.Utils.PointFloat(281.5414F, 227.5F);
            this.lbl_AttnMr.Name = "lbl_AttnMr";
            this.lbl_AttnMr.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_AttnMr.SizeF = new System.Drawing.SizeF(399.9999F, 24.49997F);
            this.lbl_AttnMr.StylePriority.UseFont = false;
            this.lbl_AttnMr.StylePriority.UseTextAlignment = false;
            this.lbl_AttnMr.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel14
            // 
            this.xrLabel14.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel14.LocationFloat = new DevExpress.Utils.PointFloat(681.5414F, 252.5F);
            this.xrLabel14.Name = "xrLabel14";
            this.xrLabel14.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel14.SizeF = new System.Drawing.SizeF(68.45862F, 24.49997F);
            this.xrLabel14.StylePriority.UseFont = false;
            this.xrLabel14.StylePriority.UseTextAlignment = false;
            this.xrLabel14.Text = "الوظيفة";
            this.xrLabel14.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel12
            // 
            this.xrLabel12.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel12.LocationFloat = new DevExpress.Utils.PointFloat(681.5414F, 227.5F);
            this.xrLabel12.Name = "xrLabel12";
            this.xrLabel12.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel12.SizeF = new System.Drawing.SizeF(68.45868F, 24.49997F);
            this.xrLabel12.StylePriority.UseFont = false;
            this.xrLabel12.StylePriority.UseTextAlignment = false;
            this.xrLabel12.Text = "عناية السيد";
            this.xrLabel12.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel10
            // 
            this.xrLabel10.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel10.LocationFloat = new DevExpress.Utils.PointFloat(182F, 227.5F);
            this.xrLabel10.Name = "xrLabel10";
            this.xrLabel10.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel10.SizeF = new System.Drawing.SizeF(80.83295F, 24.49998F);
            this.xrLabel10.StylePriority.UseFont = false;
            this.xrLabel10.StylePriority.UseTextAlignment = false;
            this.xrLabel10.Text = "مندوب بيع";
            this.xrLabel10.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_notes
            // 
            this.lbl_notes.CanGrow = false;
            this.lbl_notes.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_notes.LocationFloat = new DevExpress.Utils.PointFloat(281.5414F, 277.5F);
            this.lbl_notes.Multiline = true;
            this.lbl_notes.Name = "lbl_notes";
            this.lbl_notes.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_notes.SizeF = new System.Drawing.SizeF(399.9998F, 50.95831F);
            this.lbl_notes.StylePriority.UseFont = false;
            this.lbl_notes.StylePriority.UseTextAlignment = false;
            this.lbl_notes.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight;
            // 
            // lbl_AttnMr_Job
            // 
            this.lbl_AttnMr_Job.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_AttnMr_Job.LocationFloat = new DevExpress.Utils.PointFloat(281.5414F, 252.5F);
            this.lbl_AttnMr_Job.Name = "lbl_AttnMr_Job";
            this.lbl_AttnMr_Job.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_AttnMr_Job.SizeF = new System.Drawing.SizeF(399.9998F, 24.49997F);
            this.lbl_AttnMr_Job.StylePriority.UseFont = false;
            this.lbl_AttnMr_Job.StylePriority.UseTextAlignment = false;
            this.lbl_AttnMr_Job.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lblReportName
            // 
            this.lblReportName.Font = new System.Drawing.Font("Times New Roman", 16F, System.Drawing.FontStyle.Bold);
            this.lblReportName.LocationFloat = new DevExpress.Utils.PointFloat(182F, 77.49999F);
            this.lblReportName.Name = "lblReportName";
            this.lblReportName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblReportName.SizeF = new System.Drawing.SizeF(80.83292F, 30F);
            this.lblReportName.StylePriority.UseFont = false;
            this.lblReportName.StylePriority.UseTextAlignment = false;
            this.lblReportName.Text = "أمر بيع";
            this.lblReportName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_store
            // 
            this.lbl_store.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_store.LocationFloat = new DevExpress.Utils.PointFloat(7F, 140F);
            this.lbl_store.Name = "lbl_store";
            this.lbl_store.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_store.SizeF = new System.Drawing.SizeF(175F, 24.49998F);
            this.lbl_store.StylePriority.UseFont = false;
            this.lbl_store.StylePriority.UseTextAlignment = false;
            this.lbl_store.Text = "الرئيسي";
            this.lbl_store.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_Number
            // 
            this.lbl_Number.Font = new System.Drawing.Font("Times New Roman", 16F, System.Drawing.FontStyle.Bold);
            this.lbl_Number.LocationFloat = new DevExpress.Utils.PointFloat(6.999999F, 77.49999F);
            this.lbl_Number.Name = "lbl_Number";
            this.lbl_Number.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Number.SizeF = new System.Drawing.SizeF(175F, 30F);
            this.lbl_Number.StylePriority.UseFont = false;
            this.lbl_Number.StylePriority.UseTextAlignment = false;
            this.lbl_Number.Text = "123";
            this.lbl_Number.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // picLogo
            // 
            this.picLogo.LocationFloat = new DevExpress.Utils.PointFloat(613.5F, 115F);
            this.picLogo.Name = "picLogo";
            this.picLogo.SizeF = new System.Drawing.SizeF(136.5F, 74.49996F);
            this.picLogo.Sizing = DevExpress.XtraPrinting.ImageSizeMode.StretchImage;
            // 
            // lblCompName
            // 
            this.lblCompName.Font = new System.Drawing.Font("Times New Roman", 16F, System.Drawing.FontStyle.Bold);
            this.lblCompName.LocationFloat = new DevExpress.Utils.PointFloat(288.5F, 77.49999F);
            this.lblCompName.Name = "lblCompName";
            this.lblCompName.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblCompName.SizeF = new System.Drawing.SizeF(461.5F, 30F);
            this.lblCompName.StylePriority.UseFont = false;
            this.lblCompName.StylePriority.UseTextAlignment = false;
            this.lblCompName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopRight;
            // 
            // lbl_Serial
            // 
            this.lbl_Serial.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_Serial.LocationFloat = new DevExpress.Utils.PointFloat(7.000001F, 252.5F);
            this.lbl_Serial.Name = "lbl_Serial";
            this.lbl_Serial.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Serial.SizeF = new System.Drawing.SizeF(38.37524F, 24.5F);
            this.lbl_Serial.StylePriority.UseFont = false;
            this.lbl_Serial.StylePriority.UseTextAlignment = false;
            this.lbl_Serial.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.lbl_Serial.Visible = false;
            // 
            // BottomMargin
            // 
            this.BottomMargin.HeightF = 45F;
            this.BottomMargin.Name = "BottomMargin";
            this.BottomMargin.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 0, 0, 0, 100F);
            this.BottomMargin.TextAlignment = DevExpress.XtraPrinting.TextAlignment.TopLeft;
            // 
            // PageHeader
            // 
            this.PageHeader.HeightF = 23F;
            this.PageHeader.Name = "PageHeader";
            // 
            // ReportFooter
            // 
            this.ReportFooter.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.lbl_totalPieces,
            this.cell_PiecesCountSum,
            this.xrLine2,
            this.xrTable6,
            this.xrTable5,
            this.xrPageInfo2,
            this.xrLabel5,
            this.lblTotalWords,
            this.lbl_DiscountV,
            this.lbl_Total,
            this.lbl_Net,
            this.xrLabel9,
            this.lbl_DiscountR,
            this.xrLabel11,
            this.xrLabel3,
            this.lbl_salesEmp_Job,
            this.lbl_Shipping,
            this.lbl_ExpensesR,
            this.lbl_EndDate,
            this.lbl_TaxR,
            this.xrTable4,
            this.xrTable3});
            this.ReportFooter.HeightF = 139F;
            this.ReportFooter.Name = "ReportFooter";
            this.ReportFooter.PrintAtBottom = true;
            // 
            // lbl_totalPieces
            // 
            this.lbl_totalPieces.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_totalPieces.CanGrow = false;
            this.lbl_totalPieces.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_totalPieces.LocationFloat = new DevExpress.Utils.PointFloat(223.7286F, 60.00004F);
            this.lbl_totalPieces.Name = "lbl_totalPieces";
            this.lbl_totalPieces.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_totalPieces.SizeF = new System.Drawing.SizeF(99.95872F, 25.45843F);
            this.lbl_totalPieces.StylePriority.UseBorders = false;
            this.lbl_totalPieces.StylePriority.UseFont = false;
            this.lbl_totalPieces.StylePriority.UsePadding = false;
            this.lbl_totalPieces.StylePriority.UseTextAlignment = false;
            this.lbl_totalPieces.Text = "total Pieces";
            this.lbl_totalPieces.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // cell_PiecesCountSum
            // 
            this.cell_PiecesCountSum.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.cell_PiecesCountSum.CanGrow = false;
            this.cell_PiecesCountSum.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_PiecesCountSum.LocationFloat = new DevExpress.Utils.PointFloat(235.8197F, 34.54158F);
            this.cell_PiecesCountSum.Name = "cell_PiecesCountSum";
            this.cell_PiecesCountSum.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_PiecesCountSum.SizeF = new System.Drawing.SizeF(99.95872F, 25.45843F);
            this.cell_PiecesCountSum.StylePriority.UseBorders = false;
            this.cell_PiecesCountSum.StylePriority.UseFont = false;
            this.cell_PiecesCountSum.StylePriority.UsePadding = false;
            this.cell_PiecesCountSum.StylePriority.UseTextAlignment = false;
            this.cell_PiecesCountSum.Text = " ";
            this.cell_PiecesCountSum.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLine2
            // 
            this.xrLine2.LocationFloat = new DevExpress.Utils.PointFloat(12.5F, 5F);
            this.xrLine2.Name = "xrLine2";
            this.xrLine2.SizeF = new System.Drawing.SizeF(739.7921F, 4.083325F);
            // 
            // xrTable6
            // 
            this.xrTable6.LocationFloat = new DevExpress.Utils.PointFloat(281.5414F, 60.00004F);
            this.xrTable6.Name = "xrTable6";
            this.xrTable6.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow6});
            this.xrTable6.SizeF = new System.Drawing.SizeF(490.0412F, 25F);
            this.xrTable6.StylePriority.UseTextAlignment = false;
            this.xrTable6.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrTable6.Visible = false;
            // 
            // xrTableRow6
            // 
            this.xrTableRow6.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.col_SlInv_Code2,
            this.col_SlInv_Expire,
            this.col_SlInv_Batch,
            this.col_SlInv_Length,
            this.col_SlInv_Width,
            this.col_SlInv_Height,
            this.col_SlInv_TotlQty,
            this.col_SlInv_PcCount,
            this.col_SlInv_Qc,
            this.col_Index,
            this.Cell_Location});
            this.xrTableRow6.Name = "xrTableRow6";
            this.xrTableRow6.Weight = 1D;
            // 
            // col_SlInv_Code2
            // 
            this.col_SlInv_Code2.Name = "col_SlInv_Code2";
            this.col_SlInv_Code2.Text = "col_SlInv_Code2";
            this.col_SlInv_Code2.Weight = 0.38411454068938106D;
            // 
            // col_SlInv_Expire
            // 
            this.col_SlInv_Expire.Name = "col_SlInv_Expire";
            this.col_SlInv_Expire.Text = "col_SlInv_Expire";
            this.col_SlInv_Expire.Weight = 0.3651273844681181D;
            // 
            // col_SlInv_Batch
            // 
            this.col_SlInv_Batch.Name = "col_SlInv_Batch";
            this.col_SlInv_Batch.Text = "col_SlInv_Batch";
            this.col_SlInv_Batch.Weight = 0.40329334968879033D;
            // 
            // col_SlInv_Length
            // 
            this.col_SlInv_Length.Name = "col_SlInv_Length";
            this.col_SlInv_Length.Text = "col_SlInv_Length";
            this.col_SlInv_Length.Weight = 0.36151696618817708D;
            // 
            // col_SlInv_Width
            // 
            this.col_SlInv_Width.Name = "col_SlInv_Width";
            this.col_SlInv_Width.Text = "col_SlInv_Width";
            this.col_SlInv_Width.Weight = 0.30379643630373065D;
            // 
            // col_SlInv_Height
            // 
            this.col_SlInv_Height.Name = "col_SlInv_Height";
            this.col_SlInv_Height.Text = "col_SlInv_Height";
            this.col_SlInv_Height.Weight = 0.3505075623538727D;
            // 
            // col_SlInv_TotlQty
            // 
            this.col_SlInv_TotlQty.Name = "col_SlInv_TotlQty";
            this.col_SlInv_TotlQty.Text = "col_SlInv_TotlQty";
            this.col_SlInv_TotlQty.Weight = 0.35848026634451308D;
            // 
            // col_SlInv_PcCount
            // 
            this.col_SlInv_PcCount.Name = "col_SlInv_PcCount";
            this.col_SlInv_PcCount.Text = "col_SlInv_PcCount";
            this.col_SlInv_PcCount.Weight = 0.26239968314728668D;
            // 
            // col_SlInv_Qc
            // 
            this.col_SlInv_Qc.Name = "col_SlInv_Qc";
            this.col_SlInv_Qc.Text = "col_SlInv_Qc";
            this.col_SlInv_Qc.Weight = 0.55898746438768687D;
            // 
            // col_Index
            // 
            this.col_Index.Name = "col_Index";
            this.col_Index.Text = "col_Index";
            this.col_Index.Weight = 0.55898746438768687D;
            // 
            // Cell_Location
            // 
            this.Cell_Location.Name = "Cell_Location";
            this.Cell_Location.Text = "Cell_Location";
            this.Cell_Location.Weight = 0.55898746438768687D;
            // 
            // xrTable5
            // 
            this.xrTable5.LocationFloat = new DevExpress.Utils.PointFloat(253.5699F, 9.083335F);
            this.xrTable5.Name = "xrTable5";
            this.xrTable5.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow5});
            this.xrTable5.SizeF = new System.Drawing.SizeF(367.3745F, 25F);
            this.xrTable5.StylePriority.UseTextAlignment = false;
            this.xrTable5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrTable5.Visible = false;
            // 
            // xrTableRow5
            // 
            this.xrTableRow5.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.col_OutTrns_ItemCode2,
            this.col_OuTrans_Expire,
            this.col_OutTrns_Batch,
            this.col_OutTrns_Length,
            this.col_OutTrns_Width,
            this.col_OutTrns_Height,
            this.col_OutTrns_TotlQty,
            this.col_OutTrns_PcCount,
            this.col_OutTrns_Qc});
            this.xrTableRow5.Name = "xrTableRow5";
            this.xrTableRow5.Weight = 1D;
            // 
            // col_OutTrns_ItemCode2
            // 
            this.col_OutTrns_ItemCode2.Name = "col_OutTrns_ItemCode2";
            this.col_OutTrns_ItemCode2.Text = "col_OutTrns_ItemCode2";
            this.col_OutTrns_ItemCode2.Weight = 0.38411454068938106D;
            // 
            // col_OuTrans_Expire
            // 
            this.col_OuTrans_Expire.Name = "col_OuTrans_Expire";
            this.col_OuTrans_Expire.Text = "col_OuTrans_Expire";
            this.col_OuTrans_Expire.Weight = 0.3651273844681181D;
            // 
            // col_OutTrns_Batch
            // 
            this.col_OutTrns_Batch.Name = "col_OutTrns_Batch";
            this.col_OutTrns_Batch.Text = "col_OutTrns_Batch";
            this.col_OutTrns_Batch.Weight = 0.40329334968879033D;
            // 
            // col_OutTrns_Length
            // 
            this.col_OutTrns_Length.Name = "col_OutTrns_Length";
            this.col_OutTrns_Length.Text = "col_OutTrns_Length";
            this.col_OutTrns_Length.Weight = 0.36151696618817708D;
            // 
            // col_OutTrns_Width
            // 
            this.col_OutTrns_Width.Name = "col_OutTrns_Width";
            this.col_OutTrns_Width.Text = "col_OutTrns_Width";
            this.col_OutTrns_Width.Weight = 0.30379643630373065D;
            // 
            // col_OutTrns_Height
            // 
            this.col_OutTrns_Height.Name = "col_OutTrns_Height";
            this.col_OutTrns_Height.Text = "col_OutTrns_Height";
            this.col_OutTrns_Height.Weight = 0.3505075623538727D;
            // 
            // col_OutTrns_TotlQty
            // 
            this.col_OutTrns_TotlQty.Name = "col_OutTrns_TotlQty";
            this.col_OutTrns_TotlQty.Text = "col_OutTrns_TotlQty";
            this.col_OutTrns_TotlQty.Weight = 0.35848026634451308D;
            // 
            // col_OutTrns_PcCount
            // 
            this.col_OutTrns_PcCount.Name = "col_OutTrns_PcCount";
            this.col_OutTrns_PcCount.Text = "col_OutTrns_PcCount";
            this.col_OutTrns_PcCount.Weight = 0.26239968314728668D;
            // 
            // col_OutTrns_Qc
            // 
            this.col_OutTrns_Qc.Name = "col_OutTrns_Qc";
            this.col_OutTrns_Qc.Text = "col_OutTrns_Qc";
            this.col_OutTrns_Qc.Weight = 0.55898746438768687D;
            // 
            // xrPageInfo2
            // 
            this.xrPageInfo2.Format = "Page {0} of {1} ";
            this.xrPageInfo2.LocationFloat = new DevExpress.Utils.PointFloat(637.2496F, 102.9583F);
            this.xrPageInfo2.Name = "xrPageInfo2";
            this.xrPageInfo2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrPageInfo2.SizeF = new System.Drawing.SizeF(109.375F, 23F);
            this.xrPageInfo2.StylePriority.UseTextAlignment = false;
            this.xrPageInfo2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel5
            // 
            this.xrLabel5.BackColor = System.Drawing.Color.Silver;
            this.xrLabel5.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel5.LocationFloat = new DevExpress.Utils.PointFloat(524.7496F, 102.9583F);
            this.xrLabel5.Name = "xrLabel5";
            this.xrLabel5.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel5.SizeF = new System.Drawing.SizeF(66.29163F, 24.49995F);
            this.xrLabel5.StylePriority.UseBackColor = false;
            this.xrLabel5.StylePriority.UseFont = false;
            this.xrLabel5.StylePriority.UseTextAlignment = false;
            this.xrLabel5.Text = "فقط وقدره";
            this.xrLabel5.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lblTotalWords
            // 
            this.lblTotalWords.BackColor = System.Drawing.Color.Silver;
            this.lblTotalWords.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.lblTotalWords.LocationFloat = new DevExpress.Utils.PointFloat(12.2496F, 102.9583F);
            this.lblTotalWords.Name = "lblTotalWords";
            this.lblTotalWords.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lblTotalWords.SizeF = new System.Drawing.SizeF(510.6673F, 24.49995F);
            this.lblTotalWords.StylePriority.UseBackColor = false;
            this.lblTotalWords.StylePriority.UseFont = false;
            this.lblTotalWords.StylePriority.UseTextAlignment = false;
            this.lblTotalWords.Text = "..";
            this.lblTotalWords.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_DiscountV
            // 
            this.lbl_DiscountV.Borders = ((DevExpress.XtraPrinting.BorderSide)(((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_DiscountV.CanGrow = false;
            this.lbl_DiscountV.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_DiscountV.LocationFloat = new DevExpress.Utils.PointFloat(12.24958F, 38.50003F);
            this.lbl_DiscountV.Name = "lbl_DiscountV";
            this.lbl_DiscountV.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_DiscountV.SizeF = new System.Drawing.SizeF(99.95872F, 25.4584F);
            this.lbl_DiscountV.StylePriority.UseBorders = false;
            this.lbl_DiscountV.StylePriority.UseFont = false;
            this.lbl_DiscountV.StylePriority.UsePadding = false;
            this.lbl_DiscountV.StylePriority.UseTextAlignment = false;
            this.lbl_DiscountV.Text = " ";
            this.lbl_DiscountV.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_Total
            // 
            this.lbl_Total.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_Total.CanGrow = false;
            this.lbl_Total.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_Total.LocationFloat = new DevExpress.Utils.PointFloat(12.24958F, 13.04167F);
            this.lbl_Total.Name = "lbl_Total";
            this.lbl_Total.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_Total.SizeF = new System.Drawing.SizeF(99.95872F, 25.45843F);
            this.lbl_Total.StylePriority.UseBorders = false;
            this.lbl_Total.StylePriority.UseFont = false;
            this.lbl_Total.StylePriority.UsePadding = false;
            this.lbl_Total.StylePriority.UseTextAlignment = false;
            this.lbl_Total.Text = " ";
            this.lbl_Total.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_Net
            // 
            this.lbl_Net.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_Net.CanGrow = false;
            this.lbl_Net.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.lbl_Net.LocationFloat = new DevExpress.Utils.PointFloat(11.20801F, 71.99999F);
            this.lbl_Net.Name = "lbl_Net";
            this.lbl_Net.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.lbl_Net.SizeF = new System.Drawing.SizeF(101.0003F, 25.45847F);
            this.lbl_Net.StylePriority.UseBorders = false;
            this.lbl_Net.StylePriority.UseFont = false;
            this.lbl_Net.StylePriority.UsePadding = false;
            this.lbl_Net.StylePriority.UseTextAlignment = false;
            this.lbl_Net.Text = " ";
            this.lbl_Net.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel9
            // 
            this.xrLabel9.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel9.CanGrow = false;
            this.xrLabel9.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrLabel9.LocationFloat = new DevExpress.Utils.PointFloat(150.7496F, 38.50003F);
            this.xrLabel9.Name = "xrLabel9";
            this.xrLabel9.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel9.SizeF = new System.Drawing.SizeF(39.69333F, 25.45843F);
            this.xrLabel9.StylePriority.UseBorders = false;
            this.xrLabel9.StylePriority.UseFont = false;
            this.xrLabel9.StylePriority.UseTextAlignment = false;
            this.xrLabel9.Text = "خصم";
            this.xrLabel9.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_DiscountR
            // 
            this.lbl_DiscountR.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.lbl_DiscountR.CanGrow = false;
            this.lbl_DiscountR.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.lbl_DiscountR.LocationFloat = new DevExpress.Utils.PointFloat(113.2496F, 38.50003F);
            this.lbl_DiscountR.Name = "lbl_DiscountR";
            this.lbl_DiscountR.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_DiscountR.SizeF = new System.Drawing.SizeF(37.49999F, 25.45843F);
            this.lbl_DiscountR.StylePriority.UseBorders = false;
            this.lbl_DiscountR.StylePriority.UseFont = false;
            this.lbl_DiscountR.StylePriority.UseTextAlignment = false;
            this.lbl_DiscountR.Text = " ";
            this.lbl_DiscountR.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel11
            // 
            this.xrLabel11.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel11.CanGrow = false;
            this.xrLabel11.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrLabel11.LocationFloat = new DevExpress.Utils.PointFloat(113.25F, 72.00002F);
            this.xrLabel11.Name = "xrLabel11";
            this.xrLabel11.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel11.SizeF = new System.Drawing.SizeF(77.25003F, 25.4584F);
            this.xrLabel11.StylePriority.UseBorders = false;
            this.xrLabel11.StylePriority.UseFont = false;
            this.xrLabel11.StylePriority.UsePadding = false;
            this.xrLabel11.StylePriority.UseTextAlignment = false;
            this.xrLabel11.Text = "الصافي ";
            this.xrLabel11.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // xrLabel3
            // 
            this.xrLabel3.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrLabel3.CanGrow = false;
            this.xrLabel3.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrLabel3.LocationFloat = new DevExpress.Utils.PointFloat(113.2496F, 13.04167F);
            this.xrLabel3.Name = "xrLabel3";
            this.xrLabel3.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.xrLabel3.SizeF = new System.Drawing.SizeF(77.19334F, 25.45843F);
            this.xrLabel3.StylePriority.UseBorders = false;
            this.xrLabel3.StylePriority.UseFont = false;
            this.xrLabel3.StylePriority.UsePadding = false;
            this.xrLabel3.StylePriority.UseTextAlignment = false;
            this.xrLabel3.Text = "الاجمالي";
            this.xrLabel3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            // 
            // lbl_salesEmp_Job
            // 
            this.lbl_salesEmp_Job.LocationFloat = new DevExpress.Utils.PointFloat(453.3755F, 37.00005F);
            this.lbl_salesEmp_Job.Name = "lbl_salesEmp_Job";
            this.lbl_salesEmp_Job.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_salesEmp_Job.SizeF = new System.Drawing.SizeF(31.08289F, 22.99996F);
            this.lbl_salesEmp_Job.Text = "lbl_salesEmp_Job";
            this.lbl_salesEmp_Job.Visible = false;
            // 
            // lbl_Shipping
            // 
            this.lbl_Shipping.LocationFloat = new DevExpress.Utils.PointFloat(367.3339F, 37.00005F);
            this.lbl_Shipping.Name = "lbl_Shipping";
            this.lbl_Shipping.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_Shipping.SizeF = new System.Drawing.SizeF(28.66656F, 23F);
            this.lbl_Shipping.Text = "lbl_Shipping";
            this.lbl_Shipping.Visible = false;
            // 
            // lbl_ExpensesR
            // 
            this.lbl_ExpensesR.LocationFloat = new DevExpress.Utils.PointFloat(426.2922F, 37.00005F);
            this.lbl_ExpensesR.Name = "lbl_ExpensesR";
            this.lbl_ExpensesR.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_ExpensesR.SizeF = new System.Drawing.SizeF(27.08334F, 22.99996F);
            this.lbl_ExpensesR.Text = "lbl_ExpensesR";
            this.lbl_ExpensesR.Visible = false;
            // 
            // lbl_EndDate
            // 
            this.lbl_EndDate.LocationFloat = new DevExpress.Utils.PointFloat(484.4584F, 37.00005F);
            this.lbl_EndDate.Name = "lbl_EndDate";
            this.lbl_EndDate.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_EndDate.SizeF = new System.Drawing.SizeF(27.87463F, 22.99996F);
            this.lbl_EndDate.Text = "lbl_EndDate";
            this.lbl_EndDate.Visible = false;
            // 
            // lbl_TaxR
            // 
            this.lbl_TaxR.LocationFloat = new DevExpress.Utils.PointFloat(396.0004F, 37.00005F);
            this.lbl_TaxR.Name = "lbl_TaxR";
            this.lbl_TaxR.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_TaxR.SizeF = new System.Drawing.SizeF(30.29178F, 22.99996F);
            this.lbl_TaxR.Text = "lbl_TaxR";
            this.lbl_TaxR.Visible = false;
            // 
            // xrTable4
            // 
            this.xrTable4.LocationFloat = new DevExpress.Utils.PointFloat(524.7496F, 35.00001F);
            this.xrTable4.Name = "xrTable4";
            this.xrTable4.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow4});
            this.xrTable4.SizeF = new System.Drawing.SizeF(221.8749F, 25F);
            this.xrTable4.StylePriority.UseTextAlignment = false;
            this.xrTable4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.xrTable4.Visible = false;
            // 
            // xrTableRow4
            // 
            this.xrTableRow4.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_ItemDescription,
            this.cell_code2,
            this.cell_Height,
            this.cell_Width,
            this.cell_Length,
            this.cell_TotalQty,
            this.cell_Factor});
            this.xrTableRow4.Name = "xrTableRow4";
            this.xrTableRow4.Weight = 1D;
            // 
            // cell_ItemDescription
            // 
            this.cell_ItemDescription.Name = "cell_ItemDescription";
            this.cell_ItemDescription.Text = "ItemDescription";
            this.cell_ItemDescription.Weight = 0.26069694747848332D;
            // 
            // cell_code2
            // 
            this.cell_code2.Name = "cell_code2";
            this.cell_code2.Text = "cell_code2";
            this.cell_code2.Weight = 0.28044313380738151D;
            // 
            // cell_Height
            // 
            this.cell_Height.Name = "cell_Height";
            this.cell_Height.Text = "Height";
            this.cell_Height.Weight = 0.27607639914954496D;
            // 
            // cell_Width
            // 
            this.cell_Width.Name = "cell_Width";
            this.cell_Width.Text = "Width";
            this.cell_Width.Weight = 0.3037961197481438D;
            // 
            // cell_Length
            // 
            this.cell_Length.Name = "cell_Length";
            this.cell_Length.Text = "Length";
            this.cell_Length.Weight = 0.350507521828352D;
            // 
            // cell_TotalQty
            // 
            this.cell_TotalQty.Name = "cell_TotalQty";
            this.cell_TotalQty.Text = "TotalQty";
            this.cell_TotalQty.Weight = 0.25803744702410114D;
            // 
            // cell_Factor
            // 
            this.cell_Factor.Name = "cell_Factor";
            this.cell_Factor.Text = "cell_Factor";
            this.cell_Factor.Weight = 0.29259348170083821D;
            // 
            // xrTable3
            // 
            this.xrTable3.LocationFloat = new DevExpress.Utils.PointFloat(637.2496F, 9.083335F);
            this.xrTable3.Name = "xrTable3";
            this.xrTable3.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow3});
            this.xrTable3.SizeF = new System.Drawing.SizeF(109.679F, 25F);
            // 
            // xrTableRow3
            // 
            this.xrTableRow3.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.Cell_MUOM,
            this.Cell_MUOM_Factor,
            this.col_Expire,
            this.col_Batch,
            this.col_IsExpire});
            this.xrTableRow3.Name = "xrTableRow3";
            this.xrTableRow3.Visible = false;
            this.xrTableRow3.Weight = 1D;
            // 
            // Cell_MUOM
            // 
            this.Cell_MUOM.Name = "Cell_MUOM";
            this.Cell_MUOM.Weight = 0.14733194903942998D;
            // 
            // Cell_MUOM_Factor
            // 
            this.Cell_MUOM_Factor.Name = "Cell_MUOM_Factor";
            this.Cell_MUOM_Factor.Weight = 0.10557554172887314D;
            // 
            // col_Expire
            // 
            this.col_Expire.Name = "col_Expire";
            this.col_Expire.Text = "col_Expire";
            this.col_Expire.Visible = false;
            this.col_Expire.Weight = 0.1655656100813355D;
            // 
            // col_Batch
            // 
            this.col_Batch.Name = "col_Batch";
            this.col_Batch.Text = "col_Batch";
            this.col_Batch.Visible = false;
            this.col_Batch.Weight = 0.082782805040667748D;
            // 
            // col_IsExpire
            // 
            this.col_IsExpire.Name = "col_IsExpire";
            this.col_IsExpire.Text = "col_IsExpire";
            this.col_IsExpire.Visible = false;
            this.col_IsExpire.Weight = 0.082782805040667748D;
            // 
            // DetailReport_SL_Order
            // 
            this.DetailReport_SL_Order.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail1,
            this.ReportHeader,
            this.GroupFooter1});
            this.DetailReport_SL_Order.Level = 0;
            this.DetailReport_SL_Order.Name = "DetailReport_SL_Order";
            // 
            // Detail1
            // 
            this.Detail1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable2});
            this.Detail1.HeightF = 29.16667F;
            this.Detail1.Name = "Detail1";
            // 
            // xrTable2
            // 
            this.xrTable2.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable2.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable2.LocationFloat = new DevExpress.Utils.PointFloat(12.5F, 0F);
            this.xrTable2.Name = "xrTable2";
            this.xrTable2.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow2});
            this.xrTable2.SizeF = new System.Drawing.SizeF(739.7921F, 29.16667F);
            this.xrTable2.StylePriority.UseBorders = false;
            this.xrTable2.StylePriority.UseFont = false;
            this.xrTable2.StylePriority.UseTextAlignment = false;
            this.xrTable2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow2
            // 
            this.xrTableRow2.BackColor = System.Drawing.Color.Empty;
            this.xrTableRow2.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_Total,
            this.cell_Disc,
            this.cell_DiscountRatio,
            this.cell_Price,
            this.cell_Qty,
            this.cell_UOM,
            this.cell_ItemName,
            this.cell_code});
            this.xrTableRow2.Font = new System.Drawing.Font("Times New Roman", 10F, System.Drawing.FontStyle.Bold);
            this.xrTableRow2.Name = "xrTableRow2";
            this.xrTableRow2.StylePriority.UseBackColor = false;
            this.xrTableRow2.StylePriority.UseFont = false;
            this.xrTableRow2.Weight = 0.54901959587545957D;
            // 
            // cell_Total
            // 
            this.cell_Total.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.cell_Total.CanGrow = false;
            this.cell_Total.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_Total.Multiline = true;
            this.cell_Total.Name = "cell_Total";
            this.cell_Total.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_Total.StylePriority.UseBorders = false;
            this.cell_Total.StylePriority.UseFont = false;
            this.cell_Total.StylePriority.UsePadding = false;
            this.cell_Total.StylePriority.UseTextAlignment = false;
            this.cell_Total.Text = "الاجمالي";
            this.cell_Total.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_Total.Weight = 0.2921051470164624D;
            // 
            // cell_Disc
            // 
            this.cell_Disc.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.cell_Disc.CanGrow = false;
            this.cell_Disc.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_Disc.Multiline = true;
            this.cell_Disc.Name = "cell_Disc";
            this.cell_Disc.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_Disc.StylePriority.UseBorders = false;
            this.cell_Disc.StylePriority.UseFont = false;
            this.cell_Disc.StylePriority.UsePadding = false;
            this.cell_Disc.StylePriority.UseTextAlignment = false;
            this.cell_Disc.Text = "قيمة خصم";
            this.cell_Disc.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_Disc.Weight = 0.21827298036155546D;
            // 
            // cell_DiscountRatio
            // 
            this.cell_DiscountRatio.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.cell_DiscountRatio.CanGrow = false;
            this.cell_DiscountRatio.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_DiscountRatio.Multiline = true;
            this.cell_DiscountRatio.Name = "cell_DiscountRatio";
            this.cell_DiscountRatio.StylePriority.UseBorders = false;
            this.cell_DiscountRatio.StylePriority.UseFont = false;
            this.cell_DiscountRatio.StylePriority.UsePadding = false;
            this.cell_DiscountRatio.StylePriority.UseTextAlignment = false;
            this.cell_DiscountRatio.Text = "نسبة خصم";
            this.cell_DiscountRatio.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.cell_DiscountRatio.Weight = 0.17970735324389264D;
            // 
            // cell_Price
            // 
            this.cell_Price.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.cell_Price.CanGrow = false;
            this.cell_Price.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_Price.Multiline = true;
            this.cell_Price.Name = "cell_Price";
            this.cell_Price.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_Price.StylePriority.UseBorders = false;
            this.cell_Price.StylePriority.UseFont = false;
            this.cell_Price.StylePriority.UsePadding = false;
            this.cell_Price.StylePriority.UseTextAlignment = false;
            this.cell_Price.Text = "السعر";
            this.cell_Price.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_Price.Weight = 0.25540705873346431D;
            // 
            // cell_Qty
            // 
            this.cell_Qty.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.cell_Qty.CanGrow = false;
            this.cell_Qty.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_Qty.Multiline = true;
            this.cell_Qty.Name = "cell_Qty";
            this.cell_Qty.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_Qty.StylePriority.UseBorders = false;
            this.cell_Qty.StylePriority.UseFont = false;
            this.cell_Qty.StylePriority.UsePadding = false;
            this.cell_Qty.StylePriority.UseTextAlignment = false;
            this.cell_Qty.Text = "كمية";
            this.cell_Qty.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_Qty.Weight = 0.19656506779058763D;
            // 
            // cell_UOM
            // 
            this.cell_UOM.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.cell_UOM.CanGrow = false;
            this.cell_UOM.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_UOM.Multiline = true;
            this.cell_UOM.Name = "cell_UOM";
            this.cell_UOM.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_UOM.StylePriority.UseBorders = false;
            this.cell_UOM.StylePriority.UseFont = false;
            this.cell_UOM.StylePriority.UsePadding = false;
            this.cell_UOM.StylePriority.UseTextAlignment = false;
            this.cell_UOM.Text = "وحدة قياس";
            this.cell_UOM.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_UOM.Weight = 0.24212770901568875D;
            // 
            // cell_ItemName
            // 
            this.cell_ItemName.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.cell_ItemName.CanGrow = false;
            this.cell_ItemName.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_ItemName.Multiline = true;
            this.cell_ItemName.Name = "cell_ItemName";
            this.cell_ItemName.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_ItemName.StylePriority.UseBorders = false;
            this.cell_ItemName.StylePriority.UseFont = false;
            this.cell_ItemName.StylePriority.UsePadding = false;
            this.cell_ItemName.StylePriority.UseTextAlignment = false;
            this.cell_ItemName.Text = "اســـم الصنف";
            this.cell_ItemName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_ItemName.Weight = 0.60496327259945248D;
            // 
            // cell_code
            // 
            this.cell_code.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.cell_code.CanGrow = false;
            this.cell_code.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_code.Multiline = true;
            this.cell_code.Name = "cell_code";
            this.cell_code.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_code.StylePriority.UseBorders = false;
            this.cell_code.StylePriority.UseFont = false;
            this.cell_code.StylePriority.UsePadding = false;
            this.cell_code.StylePriority.UseTextAlignment = false;
            this.cell_code.Text = "كود";
            this.cell_code.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_code.Weight = 0.12857687506459745D;
            // 
            // ReportHeader
            // 
            this.ReportHeader.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable1});
            this.ReportHeader.HeightF = 85F;
            this.ReportHeader.Name = "ReportHeader";
            // 
            // xrTable1
            // 
            this.xrTable1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable1.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable1.LocationFloat = new DevExpress.Utils.PointFloat(12.5F, 10.75001F);
            this.xrTable1.Name = "xrTable1";
            this.xrTable1.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.tableRow1,
            this.xrTableRow1});
            this.xrTable1.SizeF = new System.Drawing.SizeF(739.7921F, 72.25F);
            this.xrTable1.StylePriority.UseBorders = false;
            this.xrTable1.StylePriority.UseFont = false;
            this.xrTable1.StylePriority.UseTextAlignment = false;
            this.xrTable1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // tableRow1
            // 
            this.tableRow1.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.tableCell1});
            this.tableRow1.Name = "tableRow1";
            this.tableRow1.Weight = 1D;
            // 
            // tableCell1
            // 
            this.tableCell1.BackColor = System.Drawing.Color.Transparent;
            this.tableCell1.Font = new System.Drawing.Font("Times New Roman", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tableCell1.Name = "tableCell1";
            this.tableCell1.StylePriority.UseBackColor = false;
            this.tableCell1.StylePriority.UseFont = false;
            this.tableCell1.Text = "تفاصيل أمر البيع";
            this.tableCell1.Weight = 2.1347400296713905D;
            // 
            // xrTableRow1
            // 
            this.xrTableRow1.BackColor = System.Drawing.Color.Moccasin;
            this.xrTableRow1.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.xrTableCell1,
            this.xrTableCell9,
            this.xrTableCell4,
            this.xrTableCell7,
            this.xrTableCell6,
            this.xrTableCell5,
            this.xrTableCell3,
            this.xrTableCell8});
            this.xrTableRow1.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrTableRow1.Name = "xrTableRow1";
            this.xrTableRow1.StylePriority.UseBackColor = false;
            this.xrTableRow1.StylePriority.UseFont = false;
            this.xrTableRow1.Weight = 1D;
            // 
            // xrTableCell1
            // 
            this.xrTableCell1.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTableCell1.Name = "xrTableCell1";
            this.xrTableCell1.StylePriority.UseBackColor = false;
            this.xrTableCell1.StylePriority.UseBorders = false;
            this.xrTableCell1.Text = "الاجمالي";
            this.xrTableCell1.Weight = 0.294452000674701D;
            // 
            // xrTableCell9
            // 
            this.xrTableCell9.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell9.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTableCell9.Name = "xrTableCell9";
            this.xrTableCell9.StylePriority.UseBackColor = false;
            this.xrTableCell9.StylePriority.UseBorders = false;
            this.xrTableCell9.Text = "قيمة خصم";
            this.xrTableCell9.Weight = 0.21974292694909528D;
            // 
            // xrTableCell4
            // 
            this.xrTableCell4.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell4.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTableCell4.Name = "xrTableCell4";
            this.xrTableCell4.StylePriority.UseBackColor = false;
            this.xrTableCell4.StylePriority.UseBorders = false;
            this.xrTableCell4.Text = "نسبة خصم";
            this.xrTableCell4.Weight = 0.18143496158043104D;
            // 
            // xrTableCell7
            // 
            this.xrTableCell7.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell7.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTableCell7.Name = "xrTableCell7";
            this.xrTableCell7.StylePriority.UseBackColor = false;
            this.xrTableCell7.StylePriority.UseBorders = false;
            this.xrTableCell7.Text = "السعر";
            this.xrTableCell7.Weight = 0.25749641654004785D;
            // 
            // xrTableCell6
            // 
            this.xrTableCell6.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell6.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTableCell6.Name = "xrTableCell6";
            this.xrTableCell6.StylePriority.UseBackColor = false;
            this.xrTableCell6.StylePriority.UseBorders = false;
            this.xrTableCell6.Text = "كمية";
            this.xrTableCell6.Weight = 0.19810691296013921D;
            // 
            // xrTableCell5
            // 
            this.xrTableCell5.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell5.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTableCell5.Name = "xrTableCell5";
            this.xrTableCell5.StylePriority.UseBackColor = false;
            this.xrTableCell5.StylePriority.UseBorders = false;
            this.xrTableCell5.Text = "وحدة قياس";
            this.xrTableCell5.Weight = 0.24407314996416712D;
            // 
            // xrTableCell3
            // 
            this.xrTableCell3.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell3.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTableCell3.Name = "xrTableCell3";
            this.xrTableCell3.StylePriority.UseBackColor = false;
            this.xrTableCell3.StylePriority.UseBorders = false;
            this.xrTableCell3.Text = "اســـم الصنف";
            this.xrTableCell3.Weight = 0.60982388236714358D;
            // 
            // xrTableCell8
            // 
            this.xrTableCell8.BackColor = System.Drawing.Color.Silver;
            this.xrTableCell8.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTableCell8.Name = "xrTableCell8";
            this.xrTableCell8.StylePriority.UseBackColor = false;
            this.xrTableCell8.StylePriority.UseBorders = false;
            this.xrTableCell8.Text = "كود";
            this.xrTableCell8.Weight = 0.12960977863566553D;
            // 
            // DetailReport_OutTrns
            // 
            this.DetailReport_OutTrns.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail2,
            this.ReportHeader1,
            this.ReportFooter1});
            this.DetailReport_OutTrns.Level = 1;
            this.DetailReport_OutTrns.Name = "DetailReport_OutTrns";
            // 
            // Detail2
            // 
            this.Detail2.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.table2});
            this.Detail2.HeightF = 29.16667F;
            this.Detail2.Name = "Detail2";
            // 
            // table2
            // 
            this.table2.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.table2.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.table2.LocationFloat = new DevExpress.Utils.PointFloat(12.5F, 0F);
            this.table2.Name = "table2";
            this.table2.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.tableRow4});
            this.table2.Scripts.OnBeforePrint = "table2_BeforePrint";
            this.table2.SizeF = new System.Drawing.SizeF(739.7921F, 29.16667F);
            this.table2.StylePriority.UseBorders = false;
            this.table2.StylePriority.UseFont = false;
            this.table2.StylePriority.UseTextAlignment = false;
            this.table2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // tableRow4
            // 
            this.tableRow4.BackColor = System.Drawing.Color.Empty;
            this.tableRow4.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.col_OutTrns_Qty,
            this.col_OutTrns_UOM,
            this.col_OutTrns_ItemName,
            this.col_OutTrns_ItemCode1,
            this.col_OutTrns_Store,
            this.col_OutTrns_Code,
            this.col_OutTrns_Date});
            this.tableRow4.Font = new System.Drawing.Font("Times New Roman", 10F, System.Drawing.FontStyle.Bold);
            this.tableRow4.Name = "tableRow4";
            this.tableRow4.StylePriority.UseBackColor = false;
            this.tableRow4.StylePriority.UseFont = false;
            this.tableRow4.Weight = 0.54901959587545957D;
            // 
            // col_OutTrns_Qty
            // 
            this.col_OutTrns_Qty.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.col_OutTrns_Qty.CanGrow = false;
            this.col_OutTrns_Qty.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.col_OutTrns_Qty.Multiline = true;
            this.col_OutTrns_Qty.Name = "col_OutTrns_Qty";
            this.col_OutTrns_Qty.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.col_OutTrns_Qty.StylePriority.UseBorders = false;
            this.col_OutTrns_Qty.StylePriority.UseFont = false;
            this.col_OutTrns_Qty.StylePriority.UsePadding = false;
            this.col_OutTrns_Qty.StylePriority.UseTextAlignment = false;
            this.col_OutTrns_Qty.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.col_OutTrns_Qty.Weight = 0.28542448169523627D;
            // 
            // col_OutTrns_UOM
            // 
            this.col_OutTrns_UOM.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.col_OutTrns_UOM.CanGrow = false;
            this.col_OutTrns_UOM.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.col_OutTrns_UOM.Multiline = true;
            this.col_OutTrns_UOM.Name = "col_OutTrns_UOM";
            this.col_OutTrns_UOM.StylePriority.UseBorders = false;
            this.col_OutTrns_UOM.StylePriority.UseFont = false;
            this.col_OutTrns_UOM.StylePriority.UsePadding = false;
            this.col_OutTrns_UOM.StylePriority.UseTextAlignment = false;
            this.col_OutTrns_UOM.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.col_OutTrns_UOM.Weight = 0.22495368747459604D;
            // 
            // col_OutTrns_ItemName
            // 
            this.col_OutTrns_ItemName.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.col_OutTrns_ItemName.CanGrow = false;
            this.col_OutTrns_ItemName.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.col_OutTrns_ItemName.Multiline = true;
            this.col_OutTrns_ItemName.Name = "col_OutTrns_ItemName";
            this.col_OutTrns_ItemName.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.col_OutTrns_ItemName.StylePriority.UseBorders = false;
            this.col_OutTrns_ItemName.StylePriority.UseFont = false;
            this.col_OutTrns_ItemName.StylePriority.UsePadding = false;
            this.col_OutTrns_ItemName.StylePriority.UseTextAlignment = false;
            this.col_OutTrns_ItemName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.col_OutTrns_ItemName.Weight = 0.43511437018554255D;
            // 
            // col_OutTrns_ItemCode1
            // 
            this.col_OutTrns_ItemCode1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.col_OutTrns_ItemCode1.CanGrow = false;
            this.col_OutTrns_ItemCode1.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.col_OutTrns_ItemCode1.Multiline = true;
            this.col_OutTrns_ItemCode1.Name = "col_OutTrns_ItemCode1";
            this.col_OutTrns_ItemCode1.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.col_OutTrns_ItemCode1.StylePriority.UseBorders = false;
            this.col_OutTrns_ItemCode1.StylePriority.UseFont = false;
            this.col_OutTrns_ItemCode1.StylePriority.UsePadding = false;
            this.col_OutTrns_ItemCode1.StylePriority.UseTextAlignment = false;
            this.col_OutTrns_ItemCode1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.col_OutTrns_ItemCode1.Weight = 0.22340102557145741D;
            // 
            // col_OutTrns_Store
            // 
            this.col_OutTrns_Store.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.col_OutTrns_Store.CanGrow = false;
            this.col_OutTrns_Store.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.col_OutTrns_Store.Multiline = true;
            this.col_OutTrns_Store.Name = "col_OutTrns_Store";
            this.col_OutTrns_Store.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.col_OutTrns_Store.StylePriority.UseBorders = false;
            this.col_OutTrns_Store.StylePriority.UseFont = false;
            this.col_OutTrns_Store.StylePriority.UsePadding = false;
            this.col_OutTrns_Store.StylePriority.UseTextAlignment = false;
            this.col_OutTrns_Store.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.col_OutTrns_Store.Weight = 0.40481866103443387D;
            // 
            // col_OutTrns_Code
            // 
            this.col_OutTrns_Code.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.col_OutTrns_Code.CanGrow = false;
            this.col_OutTrns_Code.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.col_OutTrns_Code.Multiline = true;
            this.col_OutTrns_Code.Name = "col_OutTrns_Code";
            this.col_OutTrns_Code.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.col_OutTrns_Code.StylePriority.UseBorders = false;
            this.col_OutTrns_Code.StylePriority.UseFont = false;
            this.col_OutTrns_Code.StylePriority.UsePadding = false;
            this.col_OutTrns_Code.StylePriority.UseTextAlignment = false;
            this.col_OutTrns_Code.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.col_OutTrns_Code.Weight = 0.25798999331110861D;
            // 
            // col_OutTrns_Date
            // 
            this.col_OutTrns_Date.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.col_OutTrns_Date.CanGrow = false;
            this.col_OutTrns_Date.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.col_OutTrns_Date.Multiline = true;
            this.col_OutTrns_Date.Name = "col_OutTrns_Date";
            this.col_OutTrns_Date.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.col_OutTrns_Date.Scripts.OnBeforePrint = "col_OutTrns_Date_BeforePrint";
            this.col_OutTrns_Date.StylePriority.UseBorders = false;
            this.col_OutTrns_Date.StylePriority.UseFont = false;
            this.col_OutTrns_Date.StylePriority.UsePadding = false;
            this.col_OutTrns_Date.StylePriority.UseTextAlignment = false;
            this.col_OutTrns_Date.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.col_OutTrns_Date.Weight = 0.28602324455332645D;
            // 
            // ReportHeader1
            // 
            this.ReportHeader1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.table1});
            this.ReportHeader1.HeightF = 93F;
            this.ReportHeader1.Name = "ReportHeader1";
            // 
            // table1
            // 
            this.table1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.table1.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.table1.LocationFloat = new DevExpress.Utils.PointFloat(12.5F, 19.74999F);
            this.table1.Name = "table1";
            this.table1.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.tableRow2,
            this.tableRow3});
            this.table1.SizeF = new System.Drawing.SizeF(739.7921F, 72.25F);
            this.table1.StylePriority.UseBorders = false;
            this.table1.StylePriority.UseFont = false;
            this.table1.StylePriority.UseTextAlignment = false;
            this.table1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // tableRow2
            // 
            this.tableRow2.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.tableCell2});
            this.tableRow2.Name = "tableRow2";
            this.tableRow2.Weight = 1D;
            // 
            // tableCell2
            // 
            this.tableCell2.BackColor = System.Drawing.Color.Transparent;
            this.tableCell2.Font = new System.Drawing.Font("Times New Roman", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tableCell2.Name = "tableCell2";
            this.tableCell2.StylePriority.UseBackColor = false;
            this.tableCell2.StylePriority.UseFont = false;
            this.tableCell2.Text = "أذونات الصرف";
            this.tableCell2.Weight = 2.1347400296713905D;
            // 
            // tableRow3
            // 
            this.tableRow3.BackColor = System.Drawing.Color.Moccasin;
            this.tableRow3.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.tableCell4,
            this.tableCell5,
            this.tableCell6,
            this.tableCell7,
            this.tableCell8,
            this.tableCell9,
            this.tableCell10});
            this.tableRow3.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.tableRow3.Name = "tableRow3";
            this.tableRow3.StylePriority.UseBackColor = false;
            this.tableRow3.StylePriority.UseFont = false;
            this.tableRow3.Weight = 1D;
            // 
            // tableCell4
            // 
            this.tableCell4.BackColor = System.Drawing.Color.Silver;
            this.tableCell4.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell4.Name = "tableCell4";
            this.tableCell4.StylePriority.UseBackColor = false;
            this.tableCell4.StylePriority.UseBorders = false;
            this.tableCell4.Text = "الكمية";
            this.tableCell4.Weight = 0.28771774542315853D;
            // 
            // tableCell5
            // 
            this.tableCell5.BackColor = System.Drawing.Color.Silver;
            this.tableCell5.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell5.Name = "tableCell5";
            this.tableCell5.StylePriority.UseBackColor = false;
            this.tableCell5.StylePriority.UseBorders = false;
            this.tableCell5.Text = "وحدة القياس";
            this.tableCell5.Weight = 0.22603834585085944D;
            // 
            // tableCell6
            // 
            this.tableCell6.BackColor = System.Drawing.Color.Silver;
            this.tableCell6.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell6.Name = "tableCell6";
            this.tableCell6.StylePriority.UseBackColor = false;
            this.tableCell6.StylePriority.UseBorders = false;
            this.tableCell6.Text = "اسم الصنف";
            this.tableCell6.Weight = 0.43937021447025715D;
            // 
            // tableCell7
            // 
            this.tableCell7.BackColor = System.Drawing.Color.Silver;
            this.tableCell7.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell7.Name = "tableCell7";
            this.tableCell7.StylePriority.UseBackColor = false;
            this.tableCell7.StylePriority.UseBorders = false;
            this.tableCell7.Text = "كود1";
            this.tableCell7.Weight = 0.22515852448054335D;
            // 
            // tableCell8
            // 
            this.tableCell8.BackColor = System.Drawing.Color.Silver;
            this.tableCell8.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell8.Name = "tableCell8";
            this.tableCell8.StylePriority.UseBackColor = false;
            this.tableCell8.StylePriority.UseBorders = false;
            this.tableCell8.Text = "المخزن";
            this.tableCell8.Weight = 0.40807135152850671D;
            // 
            // tableCell9
            // 
            this.tableCell9.BackColor = System.Drawing.Color.Silver;
            this.tableCell9.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell9.Name = "tableCell9";
            this.tableCell9.StylePriority.UseBackColor = false;
            this.tableCell9.StylePriority.UseBorders = false;
            this.tableCell9.Text = "كود إذن الصرف";
            this.tableCell9.Weight = 0.26006278524300852D;
            // 
            // tableCell10
            // 
            this.tableCell10.BackColor = System.Drawing.Color.Silver;
            this.tableCell10.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell10.Name = "tableCell10";
            this.tableCell10.StylePriority.UseBackColor = false;
            this.tableCell10.StylePriority.UseBorders = false;
            this.tableCell10.Text = "التاريخ";
            this.tableCell10.Weight = 0.28832106267505681D;
            // 
            // ReportFooter1
            // 
            this.ReportFooter1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable7});
            this.ReportFooter1.HeightF = 31F;
            this.ReportFooter1.Name = "ReportFooter1";
            // 
            // xrTable7
            // 
            this.xrTable7.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable7.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable7.LocationFloat = new DevExpress.Utils.PointFloat(12.5F, 0F);
            this.xrTable7.Name = "xrTable7";
            this.xrTable7.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow7});
            this.xrTable7.Scripts.OnBeforePrint = "table2_BeforePrint";
            this.xrTable7.SizeF = new System.Drawing.SizeF(739.7921F, 29.16667F);
            this.xrTable7.StylePriority.UseBorders = false;
            this.xrTable7.StylePriority.UseFont = false;
            this.xrTable7.StylePriority.UseTextAlignment = false;
            this.xrTable7.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow7
            // 
            this.xrTableRow7.BackColor = System.Drawing.Color.Empty;
            this.xrTableRow7.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_BillQtySum,
            this.xrTableCell2});
            this.xrTableRow7.Font = new System.Drawing.Font("Times New Roman", 10F, System.Drawing.FontStyle.Bold);
            this.xrTableRow7.Name = "xrTableRow7";
            this.xrTableRow7.StylePriority.UseBackColor = false;
            this.xrTableRow7.StylePriority.UseFont = false;
            this.xrTableRow7.Weight = 0.54901959587545957D;
            // 
            // cell_BillQtySum
            // 
            this.cell_BillQtySum.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.cell_BillQtySum.CanGrow = false;
            this.cell_BillQtySum.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_BillQtySum.Multiline = true;
            this.cell_BillQtySum.Name = "cell_BillQtySum";
            this.cell_BillQtySum.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_BillQtySum.StylePriority.UseBorders = false;
            this.cell_BillQtySum.StylePriority.UseFont = false;
            this.cell_BillQtySum.StylePriority.UsePadding = false;
            this.cell_BillQtySum.StylePriority.UseTextAlignment = false;
            this.cell_BillQtySum.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_BillQtySum.Weight = 0.28542448169523627D;
            // 
            // xrTableCell2
            // 
            this.xrTableCell2.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrTableCell2.CanGrow = false;
            this.xrTableCell2.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrTableCell2.Multiline = true;
            this.xrTableCell2.Name = "xrTableCell2";
            this.xrTableCell2.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 0, 0, 0, 100F);
            this.xrTableCell2.StylePriority.UseBorders = false;
            this.xrTableCell2.StylePriority.UseFont = false;
            this.xrTableCell2.StylePriority.UsePadding = false;
            this.xrTableCell2.StylePriority.UseTextAlignment = false;
            this.xrTableCell2.Text = "اجمالي الكمية";
            this.xrTableCell2.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrTableCell2.Weight = 1.8323009821304652D;
            // 
            // DetailReport_SlInv
            // 
            this.DetailReport_SlInv.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail3,
            this.ReportHeader2,
            this.ReportFooter2});
            this.DetailReport_SlInv.Level = 2;
            this.DetailReport_SlInv.Name = "DetailReport_SlInv";
            // 
            // Detail3
            // 
            this.Detail3.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.table4});
            this.Detail3.HeightF = 29.16667F;
            this.Detail3.Name = "Detail3";
            // 
            // table4
            // 
            this.table4.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.table4.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.table4.LocationFloat = new DevExpress.Utils.PointFloat(12.5F, 0F);
            this.table4.Name = "table4";
            this.table4.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.tableRow7});
            this.table4.SizeF = new System.Drawing.SizeF(739.7921F, 29.16667F);
            this.table4.StylePriority.UseBorders = false;
            this.table4.StylePriority.UseFont = false;
            this.table4.StylePriority.UseTextAlignment = false;
            this.table4.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // tableRow7
            // 
            this.tableRow7.BackColor = System.Drawing.Color.Empty;
            this.tableRow7.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.col_SlInv_Qty,
            this.col_SlInv_UOM,
            this.col_SlInv_ItemName,
            this.col_SlInv_Code1,
            this.col_SlInv_Branch,
            this.col_SlInv_InvCode,
            this.col_SlInv_Date});
            this.tableRow7.Font = new System.Drawing.Font("Times New Roman", 10F, System.Drawing.FontStyle.Bold);
            this.tableRow7.Name = "tableRow7";
            this.tableRow7.StylePriority.UseBackColor = false;
            this.tableRow7.StylePriority.UseFont = false;
            this.tableRow7.Weight = 0.54901959587545957D;
            // 
            // col_SlInv_Qty
            // 
            this.col_SlInv_Qty.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.col_SlInv_Qty.CanGrow = false;
            this.col_SlInv_Qty.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.col_SlInv_Qty.Multiline = true;
            this.col_SlInv_Qty.Name = "col_SlInv_Qty";
            this.col_SlInv_Qty.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.col_SlInv_Qty.StylePriority.UseBorders = false;
            this.col_SlInv_Qty.StylePriority.UseFont = false;
            this.col_SlInv_Qty.StylePriority.UsePadding = false;
            this.col_SlInv_Qty.StylePriority.UseTextAlignment = false;
            this.col_SlInv_Qty.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.col_SlInv_Qty.Weight = 0.28542456905471342D;
            // 
            // col_SlInv_UOM
            // 
            this.col_SlInv_UOM.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.col_SlInv_UOM.CanGrow = false;
            this.col_SlInv_UOM.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.col_SlInv_UOM.Multiline = true;
            this.col_SlInv_UOM.Name = "col_SlInv_UOM";
            this.col_SlInv_UOM.StylePriority.UseBorders = false;
            this.col_SlInv_UOM.StylePriority.UseFont = false;
            this.col_SlInv_UOM.StylePriority.UsePadding = false;
            this.col_SlInv_UOM.StylePriority.UseTextAlignment = false;
            this.col_SlInv_UOM.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            this.col_SlInv_UOM.Weight = 0.22495357827524964D;
            // 
            // col_SlInv_ItemName
            // 
            this.col_SlInv_ItemName.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.col_SlInv_ItemName.CanGrow = false;
            this.col_SlInv_ItemName.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.col_SlInv_ItemName.Multiline = true;
            this.col_SlInv_ItemName.Name = "col_SlInv_ItemName";
            this.col_SlInv_ItemName.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.col_SlInv_ItemName.StylePriority.UseBorders = false;
            this.col_SlInv_ItemName.StylePriority.UseFont = false;
            this.col_SlInv_ItemName.StylePriority.UsePadding = false;
            this.col_SlInv_ItemName.StylePriority.UseTextAlignment = false;
            this.col_SlInv_ItemName.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.col_SlInv_ItemName.Weight = 0.43511439202541186D;
            // 
            // col_SlInv_Code1
            // 
            this.col_SlInv_Code1.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.col_SlInv_Code1.CanGrow = false;
            this.col_SlInv_Code1.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.col_SlInv_Code1.Multiline = true;
            this.col_SlInv_Code1.Name = "col_SlInv_Code1";
            this.col_SlInv_Code1.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.col_SlInv_Code1.StylePriority.UseBorders = false;
            this.col_SlInv_Code1.StylePriority.UseFont = false;
            this.col_SlInv_Code1.StylePriority.UsePadding = false;
            this.col_SlInv_Code1.StylePriority.UseTextAlignment = false;
            this.col_SlInv_Code1.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.col_SlInv_Code1.Weight = 0.223401069251196D;
            // 
            // col_SlInv_Branch
            // 
            this.col_SlInv_Branch.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.col_SlInv_Branch.CanGrow = false;
            this.col_SlInv_Branch.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.col_SlInv_Branch.Multiline = true;
            this.col_SlInv_Branch.Name = "col_SlInv_Branch";
            this.col_SlInv_Branch.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.col_SlInv_Branch.StylePriority.UseBorders = false;
            this.col_SlInv_Branch.StylePriority.UseFont = false;
            this.col_SlInv_Branch.StylePriority.UsePadding = false;
            this.col_SlInv_Branch.StylePriority.UseTextAlignment = false;
            this.col_SlInv_Branch.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.col_SlInv_Branch.Weight = 0.40481857367495666D;
            // 
            // col_SlInv_InvCode
            // 
            this.col_SlInv_InvCode.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.col_SlInv_InvCode.CanGrow = false;
            this.col_SlInv_InvCode.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.col_SlInv_InvCode.Multiline = true;
            this.col_SlInv_InvCode.Name = "col_SlInv_InvCode";
            this.col_SlInv_InvCode.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.col_SlInv_InvCode.StylePriority.UseBorders = false;
            this.col_SlInv_InvCode.StylePriority.UseFont = false;
            this.col_SlInv_InvCode.StylePriority.UsePadding = false;
            this.col_SlInv_InvCode.StylePriority.UseTextAlignment = false;
            this.col_SlInv_InvCode.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.col_SlInv_InvCode.Weight = 0.25798999331110872D;
            // 
            // col_SlInv_Date
            // 
            this.col_SlInv_Date.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.col_SlInv_Date.CanGrow = false;
            this.col_SlInv_Date.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.col_SlInv_Date.Multiline = true;
            this.col_SlInv_Date.Name = "col_SlInv_Date";
            this.col_SlInv_Date.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.col_SlInv_Date.StylePriority.UseBorders = false;
            this.col_SlInv_Date.StylePriority.UseFont = false;
            this.col_SlInv_Date.StylePriority.UsePadding = false;
            this.col_SlInv_Date.StylePriority.UseTextAlignment = false;
            this.col_SlInv_Date.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.col_SlInv_Date.Weight = 0.286023288233065D;
            // 
            // ReportHeader2
            // 
            this.ReportHeader2.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.table3});
            this.ReportHeader2.HeightF = 86F;
            this.ReportHeader2.Name = "ReportHeader2";
            // 
            // table3
            // 
            this.table3.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.table3.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.table3.LocationFloat = new DevExpress.Utils.PointFloat(12.5F, 12.5F);
            this.table3.Name = "table3";
            this.table3.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.tableRow5,
            this.tableRow6});
            this.table3.SizeF = new System.Drawing.SizeF(739.7921F, 72.25F);
            this.table3.StylePriority.UseBorders = false;
            this.table3.StylePriority.UseFont = false;
            this.table3.StylePriority.UseTextAlignment = false;
            this.table3.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // tableRow5
            // 
            this.tableRow5.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.tableCell18});
            this.tableRow5.Name = "tableRow5";
            this.tableRow5.Weight = 1D;
            // 
            // tableCell18
            // 
            this.tableCell18.BackColor = System.Drawing.Color.Transparent;
            this.tableCell18.Font = new System.Drawing.Font("Times New Roman", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tableCell18.Name = "tableCell18";
            this.tableCell18.StylePriority.UseBackColor = false;
            this.tableCell18.StylePriority.UseFont = false;
            this.tableCell18.Text = "فواتير البيع";
            this.tableCell18.Weight = 2.1347400296713905D;
            // 
            // tableRow6
            // 
            this.tableRow6.BackColor = System.Drawing.Color.Moccasin;
            this.tableRow6.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.tableCell19,
            this.tableCell20,
            this.tableCell21,
            this.tableCell22,
            this.tableCell23,
            this.tableCell24,
            this.tableCell25});
            this.tableRow6.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.tableRow6.Name = "tableRow6";
            this.tableRow6.StylePriority.UseBackColor = false;
            this.tableRow6.StylePriority.UseFont = false;
            this.tableRow6.Weight = 1D;
            // 
            // tableCell19
            // 
            this.tableCell19.BackColor = System.Drawing.Color.Silver;
            this.tableCell19.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell19.Name = "tableCell19";
            this.tableCell19.StylePriority.UseBackColor = false;
            this.tableCell19.StylePriority.UseBorders = false;
            this.tableCell19.Text = "الكمية";
            this.tableCell19.Weight = 0.28771774542315853D;
            // 
            // tableCell20
            // 
            this.tableCell20.BackColor = System.Drawing.Color.Silver;
            this.tableCell20.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell20.Name = "tableCell20";
            this.tableCell20.StylePriority.UseBackColor = false;
            this.tableCell20.StylePriority.UseBorders = false;
            this.tableCell20.Text = "وحدة القياس";
            this.tableCell20.Weight = 0.22603834585085944D;
            // 
            // tableCell21
            // 
            this.tableCell21.BackColor = System.Drawing.Color.Silver;
            this.tableCell21.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell21.Name = "tableCell21";
            this.tableCell21.StylePriority.UseBackColor = false;
            this.tableCell21.StylePriority.UseBorders = false;
            this.tableCell21.Text = "اسم الصنف";
            this.tableCell21.Weight = 0.43937021447025715D;
            // 
            // tableCell22
            // 
            this.tableCell22.BackColor = System.Drawing.Color.Silver;
            this.tableCell22.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell22.Name = "tableCell22";
            this.tableCell22.StylePriority.UseBackColor = false;
            this.tableCell22.StylePriority.UseBorders = false;
            this.tableCell22.Text = "كود1";
            this.tableCell22.Weight = 0.22515852448054335D;
            // 
            // tableCell23
            // 
            this.tableCell23.BackColor = System.Drawing.Color.Silver;
            this.tableCell23.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell23.Name = "tableCell23";
            this.tableCell23.StylePriority.UseBackColor = false;
            this.tableCell23.StylePriority.UseBorders = false;
            this.tableCell23.Text = "الفرع";
            this.tableCell23.Weight = 0.40807135152850671D;
            // 
            // tableCell24
            // 
            this.tableCell24.BackColor = System.Drawing.Color.Silver;
            this.tableCell24.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell24.Name = "tableCell24";
            this.tableCell24.StylePriority.UseBackColor = false;
            this.tableCell24.StylePriority.UseBorders = false;
            this.tableCell24.Text = "رقم الفاتورة";
            this.tableCell24.Weight = 0.26006278524300852D;
            // 
            // tableCell25
            // 
            this.tableCell25.BackColor = System.Drawing.Color.Silver;
            this.tableCell25.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.tableCell25.Name = "tableCell25";
            this.tableCell25.StylePriority.UseBackColor = false;
            this.tableCell25.StylePriority.UseBorders = false;
            this.tableCell25.Text = "التاريخ";
            this.tableCell25.Weight = 0.28832106267505681D;
            // 
            // ReportFooter2
            // 
            this.ReportFooter2.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.xrTable8});
            this.ReportFooter2.HeightF = 32F;
            this.ReportFooter2.Name = "ReportFooter2";
            // 
            // xrTable8
            // 
            this.xrTable8.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrTable8.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.xrTable8.LocationFloat = new DevExpress.Utils.PointFloat(12.5F, 0F);
            this.xrTable8.Name = "xrTable8";
            this.xrTable8.Rows.AddRange(new DevExpress.XtraReports.UI.XRTableRow[] {
            this.xrTableRow8});
            this.xrTable8.Scripts.OnBeforePrint = "table2_BeforePrint";
            this.xrTable8.SizeF = new System.Drawing.SizeF(739.7921F, 29.16667F);
            this.xrTable8.StylePriority.UseBorders = false;
            this.xrTable8.StylePriority.UseFont = false;
            this.xrTable8.StylePriority.UseTextAlignment = false;
            this.xrTable8.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrTableRow8
            // 
            this.xrTableRow8.BackColor = System.Drawing.Color.Empty;
            this.xrTableRow8.Cells.AddRange(new DevExpress.XtraReports.UI.XRTableCell[] {
            this.cell_InvQtySum,
            this.xrTableCell10});
            this.xrTableRow8.Font = new System.Drawing.Font("Times New Roman", 10F, System.Drawing.FontStyle.Bold);
            this.xrTableRow8.Name = "xrTableRow8";
            this.xrTableRow8.StylePriority.UseBackColor = false;
            this.xrTableRow8.StylePriority.UseFont = false;
            this.xrTableRow8.Weight = 0.54901959587545957D;
            // 
            // cell_InvQtySum
            // 
            this.cell_InvQtySum.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.cell_InvQtySum.CanGrow = false;
            this.cell_InvQtySum.Font = new System.Drawing.Font("Times New Roman", 12F);
            this.cell_InvQtySum.Multiline = true;
            this.cell_InvQtySum.Name = "cell_InvQtySum";
            this.cell_InvQtySum.Padding = new DevExpress.XtraPrinting.PaddingInfo(0, 3, 0, 0, 100F);
            this.cell_InvQtySum.StylePriority.UseBorders = false;
            this.cell_InvQtySum.StylePriority.UseFont = false;
            this.cell_InvQtySum.StylePriority.UsePadding = false;
            this.cell_InvQtySum.StylePriority.UseTextAlignment = false;
            this.cell_InvQtySum.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleRight;
            this.cell_InvQtySum.Weight = 0.28542448169523627D;
            // 
            // xrTableCell10
            // 
            this.xrTableCell10.Borders = DevExpress.XtraPrinting.BorderSide.None;
            this.xrTableCell10.CanGrow = false;
            this.xrTableCell10.Font = new System.Drawing.Font("Times New Roman", 12F, System.Drawing.FontStyle.Bold);
            this.xrTableCell10.Multiline = true;
            this.xrTableCell10.Name = "xrTableCell10";
            this.xrTableCell10.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 0, 0, 0, 100F);
            this.xrTableCell10.StylePriority.UseBorders = false;
            this.xrTableCell10.StylePriority.UseFont = false;
            this.xrTableCell10.StylePriority.UsePadding = false;
            this.xrTableCell10.StylePriority.UseTextAlignment = false;
            this.xrTableCell10.Text = "اجمالي الكمية";
            this.xrTableCell10.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleLeft;
            this.xrTableCell10.Weight = 1.8323009821304652D;
            // 
            // GroupFooter1
            // 
            this.GroupFooter1.Controls.AddRange(new DevExpress.XtraReports.UI.XRControl[] {
            this.lbl_itms_totalQty,
            this.xrLabel13});
            this.GroupFooter1.HeightF = 24.49995F;
            this.GroupFooter1.Name = "GroupFooter1";
            // 
            // lbl_itms_totalQty
            // 
            this.lbl_itms_totalQty.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.lbl_itms_totalQty.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.lbl_itms_totalQty.LocationFloat = new DevExpress.Utils.PointFloat(12.5F, 0F);
            this.lbl_itms_totalQty.Name = "lbl_itms_totalQty";
            this.lbl_itms_totalQty.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.lbl_itms_totalQty.SizeF = new System.Drawing.SizeF(160.646F, 24.49995F);
            this.lbl_itms_totalQty.StylePriority.UseBorders = false;
            this.lbl_itms_totalQty.StylePriority.UseFont = false;
            this.lbl_itms_totalQty.StylePriority.UseTextAlignment = false;
            this.lbl_itms_totalQty.Text = " ";
            this.lbl_itms_totalQty.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // xrLabel13
            // 
            this.xrLabel13.Borders = ((DevExpress.XtraPrinting.BorderSide)((((DevExpress.XtraPrinting.BorderSide.Left | DevExpress.XtraPrinting.BorderSide.Top) 
            | DevExpress.XtraPrinting.BorderSide.Right) 
            | DevExpress.XtraPrinting.BorderSide.Bottom)));
            this.xrLabel13.Font = new System.Drawing.Font("Times New Roman", 14F);
            this.xrLabel13.LocationFloat = new DevExpress.Utils.PointFloat(173.146F, 0F);
            this.xrLabel13.Name = "xrLabel13";
            this.xrLabel13.Padding = new DevExpress.XtraPrinting.PaddingInfo(2, 2, 0, 0, 100F);
            this.xrLabel13.SizeF = new System.Drawing.SizeF(111.2285F, 24.49995F);
            this.xrLabel13.StylePriority.UseBorders = false;
            this.xrLabel13.StylePriority.UseFont = false;
            this.xrLabel13.StylePriority.UseTextAlignment = false;
            this.xrLabel13.Text = "إجمالي الكمية";
            this.xrLabel13.TextAlignment = DevExpress.XtraPrinting.TextAlignment.MiddleCenter;
            // 
            // rpt_SL_SalesOrder
            // 
            this.Bands.AddRange(new DevExpress.XtraReports.UI.Band[] {
            this.Detail,
            this.TopMargin,
            this.BottomMargin,
            this.PageHeader,
            this.ReportFooter,
            this.DetailReport_SL_Order,
            this.DetailReport_OutTrns,
            this.DetailReport_SlInv});
            this.Margins = new System.Drawing.Printing.Margins(28, 39, 348, 45);
            this.PageHeight = 1169;
            this.PageWidth = 827;
            this.PaperKind = System.Drawing.Printing.PaperKind.A4;
            this.Version = "15.1";
            ((System.ComponentModel.ISupportInitialize)(this.xrTable6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.table2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.table1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.table4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.table3)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.xrTable8)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this)).EndInit();

        }

        #endregion

        private DevExpress.XtraReports.UI.DetailBand Detail;
        private DevExpress.XtraReports.UI.TopMarginBand TopMargin;
        private DevExpress.XtraReports.UI.BottomMarginBand BottomMargin;
        private DevExpress.XtraReports.UI.XRLabel lbl_Serial;
        private DevExpress.XtraReports.UI.PageHeaderBand PageHeader;
        private DevExpress.XtraReports.UI.ReportFooterBand ReportFooter;
        private DevExpress.XtraReports.UI.XRLabel lbl_date;
        private DevExpress.XtraReports.UI.XRLabel xrLabel1;
        private DevExpress.XtraReports.UI.XRLabel xrLabel6;
        private DevExpress.XtraReports.UI.XRLabel lbl_User;
        private DevExpress.XtraReports.UI.XRLabel xrLabel8;
        private DevExpress.XtraReports.UI.XRLabel lbl_Customer;
        private DevExpress.XtraReports.UI.XRLabel xrLabel4;
        private DevExpress.XtraReports.UI.XRLine xrLine1;
        private DevExpress.XtraReports.UI.XRLabel xrLabel2;
        private DevExpress.XtraReports.UI.XRLabel lbl_SalesEmp;
        private DevExpress.XtraReports.UI.XRLabel lbl_DeliverDate;
        private DevExpress.XtraReports.UI.XRLabel xrLabel7;
        private DevExpress.XtraReports.UI.XRLabel lbl_AttnMr;
        private DevExpress.XtraReports.UI.XRLabel xrLabel14;
        private DevExpress.XtraReports.UI.XRLabel xrLabel12;
        private DevExpress.XtraReports.UI.XRLabel xrLabel10;
        private DevExpress.XtraReports.UI.XRLabel lbl_notes;
        private DevExpress.XtraReports.UI.XRLabel lbl_AttnMr_Job;
        private DevExpress.XtraReports.UI.XRLabel lblReportName;
        private DevExpress.XtraReports.UI.XRLabel lbl_store;
        private DevExpress.XtraReports.UI.XRLabel lbl_Number;
        private DevExpress.XtraReports.UI.XRPictureBox picLogo;
        private DevExpress.XtraReports.UI.XRLabel lblCompName;
        private DevExpress.XtraReports.UI.XRLabel lbl_salesEmp_Job;
        private DevExpress.XtraReports.UI.XRLabel lbl_Shipping;
        private DevExpress.XtraReports.UI.XRLabel lbl_ExpensesR;
        private DevExpress.XtraReports.UI.XRLabel lbl_EndDate;
        private DevExpress.XtraReports.UI.XRTable xrTable3;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow3;
        private DevExpress.XtraReports.UI.XRTableCell Cell_MUOM;
        private DevExpress.XtraReports.UI.XRTableCell Cell_MUOM_Factor;
        private DevExpress.XtraReports.UI.XRLabel lbl_TaxR;
        private DevExpress.XtraReports.UI.XRTable xrTable4;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow4;
        private DevExpress.XtraReports.UI.XRTableCell cell_ItemDescription;
        private DevExpress.XtraReports.UI.XRTableCell cell_code2;
        private DevExpress.XtraReports.UI.XRTableCell cell_Height;
        private DevExpress.XtraReports.UI.XRTableCell cell_Width;
        private DevExpress.XtraReports.UI.XRTableCell cell_Length;
        private DevExpress.XtraReports.UI.XRTableCell cell_TotalQty;
        private DevExpress.XtraReports.UI.XRTableCell cell_Factor;
        private DevExpress.XtraReports.UI.XRLabel lbl_DiscountV;
        private DevExpress.XtraReports.UI.XRLabel lbl_Total;
        private DevExpress.XtraReports.UI.XRLabel lbl_Net;
        private DevExpress.XtraReports.UI.XRLabel xrLabel9;
        private DevExpress.XtraReports.UI.XRLabel lbl_DiscountR;
        private DevExpress.XtraReports.UI.XRLabel xrLabel11;
        private DevExpress.XtraReports.UI.XRLabel xrLabel3;
        private DevExpress.XtraReports.UI.XRPageInfo xrPageInfo2;
        private DevExpress.XtraReports.UI.XRLabel xrLabel5;
        private DevExpress.XtraReports.UI.XRLabel lblTotalWords;
        private DevExpress.XtraReports.UI.DetailReportBand DetailReport_SL_Order;
        private DevExpress.XtraReports.UI.DetailBand Detail1;
        private DevExpress.XtraReports.UI.ReportHeaderBand ReportHeader;
        private DevExpress.XtraReports.UI.DetailReportBand DetailReport_OutTrns;
        private DevExpress.XtraReports.UI.DetailBand Detail2;
        private DevExpress.XtraReports.UI.ReportHeaderBand ReportHeader1;
        private DevExpress.XtraReports.UI.XRTable xrTable2;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow2;
        private DevExpress.XtraReports.UI.XRTableCell cell_Total;
        private DevExpress.XtraReports.UI.XRTableCell cell_Disc;
        private DevExpress.XtraReports.UI.XRTableCell cell_DiscountRatio;
        private DevExpress.XtraReports.UI.XRTableCell cell_Price;
        private DevExpress.XtraReports.UI.XRTableCell cell_Qty;
        private DevExpress.XtraReports.UI.XRTableCell cell_UOM;
        private DevExpress.XtraReports.UI.XRTableCell cell_ItemName;
        private DevExpress.XtraReports.UI.XRTableCell cell_code;
        private DevExpress.XtraReports.UI.XRTable xrTable1;
        private DevExpress.XtraReports.UI.XRTableRow tableRow1;
        private DevExpress.XtraReports.UI.XRTableCell tableCell1;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow1;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell1;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell9;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell4;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell7;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell6;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell5;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell3;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell8;
        private DevExpress.XtraReports.UI.XRTable table2;
        private DevExpress.XtraReports.UI.XRTableRow tableRow4;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_Qty;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_UOM;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_ItemName;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_ItemCode1;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_Store;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_Code;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_Date;
        private DevExpress.XtraReports.UI.XRTable table1;
        private DevExpress.XtraReports.UI.XRTableRow tableRow2;
        private DevExpress.XtraReports.UI.XRTableCell tableCell2;
        private DevExpress.XtraReports.UI.XRTableRow tableRow3;
        private DevExpress.XtraReports.UI.XRTableCell tableCell4;
        private DevExpress.XtraReports.UI.XRTableCell tableCell5;
        private DevExpress.XtraReports.UI.XRTableCell tableCell6;
        private DevExpress.XtraReports.UI.XRTableCell tableCell7;
        private DevExpress.XtraReports.UI.XRTableCell tableCell8;
        private DevExpress.XtraReports.UI.XRTableCell tableCell9;
        private DevExpress.XtraReports.UI.XRTableCell tableCell10;
        private DevExpress.XtraReports.UI.DetailReportBand DetailReport_SlInv;
        private DevExpress.XtraReports.UI.DetailBand Detail3;
        private DevExpress.XtraReports.UI.XRTable table4;
        private DevExpress.XtraReports.UI.XRTableRow tableRow7;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_Qty;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_UOM;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_ItemName;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_Code1;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_Branch;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_InvCode;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_Date;
        private DevExpress.XtraReports.UI.ReportHeaderBand ReportHeader2;
        private DevExpress.XtraReports.UI.XRTable table3;
        private DevExpress.XtraReports.UI.XRTableRow tableRow5;
        private DevExpress.XtraReports.UI.XRTableCell tableCell18;
        private DevExpress.XtraReports.UI.XRTableRow tableRow6;
        private DevExpress.XtraReports.UI.XRTableCell tableCell19;
        private DevExpress.XtraReports.UI.XRTableCell tableCell20;
        private DevExpress.XtraReports.UI.XRTableCell tableCell21;
        private DevExpress.XtraReports.UI.XRTableCell tableCell22;
        private DevExpress.XtraReports.UI.XRTableCell tableCell23;
        private DevExpress.XtraReports.UI.XRTableCell tableCell24;
        private DevExpress.XtraReports.UI.XRTableCell tableCell25;
        private DevExpress.XtraReports.UI.XRTable xrTable5;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow5;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_ItemCode2;
        private DevExpress.XtraReports.UI.XRTableCell col_OuTrans_Expire;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_Batch;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_Length;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_Width;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_Height;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_TotlQty;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_PcCount;
        private DevExpress.XtraReports.UI.XRTableCell col_OutTrns_Qc;
        private DevExpress.XtraReports.UI.XRTable xrTable6;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow6;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_Code2;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_Expire;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_Batch;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_Length;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_Width;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_Height;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_TotlQty;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_PcCount;
        private DevExpress.XtraReports.UI.XRTableCell col_SlInv_Qc;
        private DevExpress.XtraReports.UI.XRLine xrLine2;
        private DevExpress.XtraReports.UI.ReportFooterBand ReportFooter1;
        private DevExpress.XtraReports.UI.ReportFooterBand ReportFooter2;
        private DevExpress.XtraReports.UI.XRTable xrTable7;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow7;
        private DevExpress.XtraReports.UI.XRTableCell cell_BillQtySum;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell2;
        private DevExpress.XtraReports.UI.XRTable xrTable8;
        private DevExpress.XtraReports.UI.XRTableRow xrTableRow8;
        private DevExpress.XtraReports.UI.XRTableCell cell_InvQtySum;
        private DevExpress.XtraReports.UI.XRTableCell xrTableCell10;
        private DevExpress.XtraReports.UI.XRTableCell col_Expire;
        private DevExpress.XtraReports.UI.XRTableCell col_Batch;
        private DevExpress.XtraReports.UI.XRTableCell col_IsExpire;
        private DevExpress.XtraReports.UI.XRLabel lbl_Tel;
        private DevExpress.XtraReports.UI.XRLabel lbl_Address;
        private DevExpress.XtraReports.UI.XRLabel lbl_Mobile;
        private DevExpress.XtraReports.UI.XRLabel cell_PiecesCountSum;
        private DevExpress.XtraReports.UI.XRTableCell col_Index;
        private DevExpress.XtraReports.UI.XRLabel lbl_totalPieces;
        private DevExpress.XtraReports.UI.XRLabel lbl_Updated;
        private DevExpress.XtraReports.UI.XRTableCell Cell_Location;
        private DevExpress.XtraReports.UI.GroupFooterBand GroupFooter1;
        private DevExpress.XtraReports.UI.XRLabel lbl_itms_totalQty;
        private DevExpress.XtraReports.UI.XRLabel xrLabel13;
    }
}
