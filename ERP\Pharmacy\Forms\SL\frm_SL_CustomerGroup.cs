﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;
using DAL.Res;
using System.Linq;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Views.Base;
using DevExpress.XtraGrid.Columns;
using Reports;
using DevExpress.XtraReports.UI;


namespace Pharmacy.Forms
{
    public partial class frm_SL_CustomerGroup : DevExpress.XtraEditors.XtraForm
    {
        int custGroupId;
        List<SL_Group_Customer> lstGrps = new List<SL_Group_Customer>();

        bool DataModified;
        FormAction action = FormAction.None;
        UserPriv prvlg;

        public frm_SL_CustomerGroup()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(barManager1);
        }

        private void frm_SL_CustomerGroup_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            //check customers Account
            if (!Shared.st_Store.CustomersAcc.HasValue)
            {
                XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.MsgCustomersAcc : ResAr.MsgCustomersAcc,
                        Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);

                this.BeginInvoke(new MethodInvoker(this.Close));
                return;
            }

            LoadPrivilege();
            LoadGrpTree();

        }

        private void frm_SL_CustomerGroup_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                e.Cancel = true;
        }

        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadGrpTree();
        }

        private void barBtn_Delete_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (action == FormAction.Add)
                return;

            if (custGroupId > 0)
            {

                DAL.ERPDataContext DB = new DAL.ERPDataContext();
                if (XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResSLEn.MsgDelCustGroup : ResSLAr.MsgDelCustGroup,
                    Shared.IsEnglish == true ? ResEn.MsgTWarn : ResAr.MsgTWarn,
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2)
                    == DialogResult.Yes)
                {
                    var custGroup = (from d in DB.SL_Group_Customers
                                     where d.GroupId == custGroupId
                                     select d).SingleOrDefault();
                   
                    var customers = (from a in DB.SL_Customers
                                     where a.CategoryId == custGroup.GroupId
                                     select a.CustomerId).Count();

                    var childGroups = (from a in DB.SL_Group_Customers
                                       where a.ParentId == custGroup.GroupId
                                       select a.GroupId).Count();

                    //var mr_grpItems = (from a in DB.Mr_CustGrpItems
                    //                   where a.CustGrpId == custGroup.GroupId
                    //                   select a.CustGrpId).Count();

                    if (childGroups > 0)
                        return;

                    if (customers > 0)
                    {
                        XtraMessageBox.Show(
                            Shared.IsEnglish == true ? ResSLEn.MsgDelCustGroup2 : ResSLAr.MsgDelCustGroup2,
                            Shared.IsEnglish == true ? ResEn.MsgTInfo : ResAr.MsgTInfo,
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    //if (mr_grpItems > 0)
                    //{
                    //    XtraMessageBox.Show(
                    //        Shared.IsEnglish == true ? ResSLEn.DelCustGrpOnMr : ResSLAr.DelCustGrpOnMr,
                    //        Shared.IsEnglish == true ? ResEn.MsgTInfo : ResAr.MsgTInfo,
                    //        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    //    return;
                    //}

                    
                    DB.SL_Group_Customers.DeleteOnSubmit(custGroup);

                    MyHelper.UpdateST_UserLog(DB, custGroup.Code.ToString(), custGroup.NameAr,
                    (int)FormAction.Delete, (int)FormsNames.frm_SL_CustomerGroup);

                    DB.SubmitChanges();
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.MsgDel : ResAr.MsgDel
                        , "", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    TreeNode parentNode = treeView1.Nodes.Find(custGroupId.ToString(), true).First().Parent;
                    LoadGrpTree();
                    treeView1.Focus();

                    treeView1.SelectedNode = treeView1.Nodes.Find(parentNode.Name.ToString(), true).First();
                }
            }
        }

        private void barBtnNew_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            NewCustGroup();
        }

        private void NewCustGroup()
        {
            Reset();
            action = FormAction.Add;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            custGroupId = new ERPDataContext().SL_Group_Customers.Select(a => a.GroupId).ToList().DefaultIfEmpty(0).Max() + 1;
            txtCode.Text = custGroupId.ToString();
            txtName.Focus();

            if (treeView1.SelectedNode.Name == "0")
                lkp_ParentGrpId.EditValue = null;
            else
                lkp_ParentGrpId.EditValue = Convert.ToInt32(treeView1.SelectedNode.Name);
        }

        private void barBtn_Save_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (!ValidData())
                return;

            SaveData();
        }


        private void txtOpenBalance_KeyPress(object sender, KeyPressEventArgs e)
        {
            //accepts only integer values           
            if (char.IsNumber(e.KeyChar) || e.KeyChar == '.')
            {
            }
            else
            {
                e.Handled = e.KeyChar != (char)Keys.Back;
            }
        }


        void Reset()
        {
            txtName.Text = string.Empty;
            txtFName.Text = string.Empty;
          
            lkp_ParentGrpId.EditValue = null;

        

            DataModified = false;
            
            DoValidate();
        }

        void LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                prvlg = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.SL_Group_Customer).FirstOrDefault();

                if (!prvlg.CanDel)
                    barBtnDelete.Enabled = false;
                if (!prvlg.CanAdd)
                    barBtnNew.Enabled = false;
                if (!prvlg.CanPrint)
                    barBtnPrint.Enabled = false;
            }
        }

        DialogResult ChangesMade()
        {
            if (DataModified)
            {
                DialogResult r = XtraMessageBox.Show(
                    Shared.IsEnglish == true ? ResEn.MsgDataModified : ResAr.MsgDataModified,
                    Shared.IsEnglish == true ? ResEn.MsgTQues : ResAr.MsgTQues,
                    MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                if (r == DialogResult.Yes)
                {
                    barBtnSave.PerformClick();
                    return r;
                }
                else if (r == DialogResult.No)
                {
                    // no thing made, continue closing or do next or do previous
                    return r;
                }
                else if (r == DialogResult.Cancel)
                {
                    //cancel form action
                    return r;
                }
            }
            return DialogResult.No;
        }

        private void SaveData()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            if (action == FormAction.Add)
            {
                //create group
                SL_Group_Customer d = new SL_Group_Customer();
                d.Code = txtCode.Text.Trim();
                d.NameAr = txtName.Text.Trim();
                d.NameEn = txtFName.Text.Trim();

                if (lkp_ParentGrpId.EditValue == null)
                    d.ParentId = null;
                else
                    d.ParentId = Convert.ToInt32(lkp_ParentGrpId.EditValue);

                DB.SL_Group_Customers.InsertOnSubmit(d);

                MyHelper.UpdateST_UserLog(DB, d.Code.ToString(), d.NameAr,
                (int)FormAction.Add, (int)FormsNames.SL_Group_Customer);

                DB.SubmitChanges();

                custGroupId = d.GroupId;
            }
            else if (action == FormAction.Edit)
            {
                var d = (from i in DB.SL_Group_Customers
                         where i.GroupId == custGroupId
                         select i).FirstOrDefault();

                d.Code = txtCode.Text.Trim();
                d.NameAr = txtName.Text.Trim();
                d.NameEn = txtFName.Text.Trim();

                MyHelper.UpdateST_UserLog(DB, d.Code.ToString(), d.NameAr,
                    (int)FormAction.Edit, (int)FormsNames.SL_Group_Customer);
            }

            DB.SubmitChanges();

            XtraMessageBox.Show(
                Shared.IsEnglish == true ? ResEn.MsgSave : ResAr.MsgSave
                , "", MessageBoxButtons.OK, MessageBoxIcon.Information);

            DataModified = false;
            DoValidate();
            action = FormAction.Edit;

            treeView1.Focus();
            LoadGrpTree();

            treeView1.SelectedNode = treeView1.Nodes.Find(custGroupId.ToString(), true).First();
        }

        private bool ValidData()
        {
            if (action == FormAction.Add)
            {
                if (prvlg != null && !prvlg.CanAdd)
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.MsgPrvNew : ResAr.MsgPrvNew,
                        "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            if (action == FormAction.Edit)
            {
                if (prvlg != null && !prvlg.CanEdit)
                {
                    XtraMessageBox.Show(
                        Shared.IsEnglish == true ? ResEn.MsgPrvEdit : ResAr.MsgPrvEdit
                        ,
                        "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }

            if (Validate_Code() == false)
                return false;

            //test code can't change after customers assigned to group
            if (action == FormAction.Edit)
            {
                ERPDataContext DB = new ERPDataContext();

                var grp = (from i in DB.SL_Group_Customers
                           where i.GroupId == custGroupId
                           select i).FirstOrDefault();

                int groupCustomers = DB.SL_Customers.Where(s => s.CategoryId == grp.GroupId).Count();
                if (grp.Code.Trim() != txtCode.Text.Trim() && groupCustomers > 0)
                {
                    XtraMessageBox.Show(Shared.IsEnglish == true ? ResSLEn.MsgCustGrpCode : ResSLAr.MsgCustGrpCode,
                        "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }

            if (Validate__ArName() == false)
                return false;
            //if (Validate__EnName() == false)
            //    return false;

            return true;
        }

        private bool Validate__EnName()
        {
            try
            {
                DAL.ERPDataContext pharm = new DAL.ERPDataContext();

                if (string.IsNullOrEmpty(txtFName.Text))
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResEn.MsgNameRequired : ResAr.MsgNameRequired,
                        Shared.IsEnglish ? ResSLEn.MsgTWarn : ResAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtName.Focus();
                    return false;
                }

                if (action == FormAction.Add)
                {
                    var name = (from n in pharm.SL_Group_Customers
                                where n.NameEn == txtName.Text.Trim()
                                select n.NameEn).Count();
                    if (name > 0)
                    {
                        XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgNameExist : ResSLAr.MsgNameExist,
                            Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtFName.Focus();
                        return false;
                    }
                }
            }
            catch
            {
                XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgIncorrectData : ResSLAr.MsgIncorrectData,
                    Shared.IsEnglish ? ResSLEn.MsgTError : ResSLAr.MsgTError, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtFName.Focus();
                return false;
            }
            return true;
        }

        private void barBtnHelp_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            //Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "الخزانات");
        }

        private void txtDrawerNameAr_Modified(object sender, EventArgs e)
        {
            DataModified = true;
        }

        void DoValidate()
        {
            txtName.DoValidate();
            txtFName.DoValidate();
        }

        private bool Validate__ArName()
        {
            try
            {
                DAL.ERPDataContext pharm = new DAL.ERPDataContext();

                if (string.IsNullOrEmpty(txtName.Text))
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResEn.MsgNameRequired : ResAr.MsgNameRequired,
                        Shared.IsEnglish ? ResSLEn.MsgTWarn : ResAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtName.Focus();
                    return false;
                }

                if (action == FormAction.Add)
                {
                    var name = (from n in pharm.SL_Group_Customers
                                where n.NameAr == txtName.Text.Trim()
                                select n.NameAr).Count();
                    if (name > 0)
                    {
                        XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgNameExist : ResSLAr.MsgNameExist,
                            Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtName.Focus();
                        return false;
                    }
                }
            }
            catch
            {
                XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgIncorrectData : ResSLAr.MsgIncorrectData,
                    Shared.IsEnglish ? ResSLEn.MsgTError : ResSLAr.MsgTError, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }
            return true;
        }

        private bool Validate_Code()
        {
            try
            {
                DAL.ERPDataContext pharm = new DAL.ERPDataContext();
                if (string.IsNullOrEmpty(txtCode.Text))
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResEn.MsgEnterCode : ResAr.MsgEnterCode,
                        Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCode.Focus();
                    return false;
                }

                if (action == FormAction.Add)
                {
                    var code_exist = pharm.SL_Group_Customers.Where(c => c.Code.Trim() == txtCode.Text.Trim()).Count();
                    if (code_exist > 0)
                    {
                        XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgNumExist : ResSLAr.MsgNumExist,
                            Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtCode.Focus();
                        return false;
                    }
                }

                if (action == FormAction.Edit)
                {
                    var code_exist = pharm.SL_Group_Customers.Where(c => c.Code.Trim() == txtCode.Text.Trim()
                        && c.GroupId != custGroupId).Count();
                    if (code_exist > 0)
                    {
                        XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgNumExist : ResSLAr.MsgNumExist,
                            Shared.IsEnglish ? ResSLEn.MsgTWarn : ResSLAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtCode.Focus();
                        return false;
                    }
                }
            }
            catch
            {
                XtraMessageBox.Show(Shared.IsEnglish ? ResSLEn.MsgIncorrectData : ResSLAr.MsgIncorrectData,
                    Shared.IsEnglish ? ResSLEn.MsgTError : ResSLAr.MsgTError, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCode.Focus();
                return false;
            }
            return true;
        }

        private void LoadGrpTree()
        {
            ERPDataContext DB = new ERPDataContext();
            lstGrps = DB.SL_Group_Customers.ToList();


            treeView1.Nodes.Clear();
            treeView1.Nodes.Add("0", this.Text);
            foreach (var c in lstGrps)
            {
                if (c.ParentId == null)
                    treeView1.Nodes[0].Nodes.Add(c.GroupId.ToString(), Shared.IsEnglish ? c.NameEn : c.NameAr);
                else
                {
                    TreeNode node = treeView1.Nodes.Find(c.ParentId.Value.ToString(), true).First();
                    if (node.Nodes[c.GroupId.ToString()] == null)
                        node.Nodes.Add(c.GroupId.ToString(), Shared.IsEnglish ? c.NameEn : c.NameAr);
                }
            }
            treeView1.Nodes[0].Expand();


            lkp_ParentGrpId.Properties.DataSource = lstGrps;
            lkp_ParentGrpId.Properties.ValueMember = "GroupId";
            lkp_ParentGrpId.Properties.DisplayMember = Shared.IsEnglish ? "NameEn" : "NameAr";
        }

        private void treeView1_AfterSelect(object sender, TreeViewEventArgs e)
        {
            if (ChangesMade() == DialogResult.Cancel)
                return;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            if (e.Node.Name != "0")
            {
                var d = (from i in DB.SL_Group_Customers
                         where i.GroupId == Convert.ToInt32(e.Node.Name)
                         select i).Single();
                custGroupId = d.GroupId;

                txtCode.Text = d.Code;
                txtName.Text = d.NameAr;
                txtFName.Text = d.NameEn;
                lkp_ParentGrpId.EditValue = d.ParentId;

                action = FormAction.Edit;
            }
            else
            {
                custGroupId = 0;
                Reset();
                action = FormAction.Add;
            }

            DataModified = false;
            DoValidate();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            DataTable dt = new DataTable();

            dt.Columns.Add("serial");
            dt.Columns.Add("code");
            dt.Columns.Add("name");

            int serial = 1;
            foreach (var a in lstGrps)
            {
                dt.Rows.Add(serial, a.Code, Shared.IsEnglish ? a.NameEn : a.NameAr);
                serial++;
            }

            rpt_Sub_Tree r = new rpt_Sub_Tree(dt);
            new Reports.rpt_Template(this.Text, "", "", r).ShowPreview();
        }
    }
}