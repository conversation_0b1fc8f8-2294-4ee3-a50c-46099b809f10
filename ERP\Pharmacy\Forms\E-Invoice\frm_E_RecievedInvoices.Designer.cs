﻿namespace Pharmacy.Forms
{
    partial class frm_E_RecievedInvoices
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frm_E_RecievedInvoices));
            this.barManager1 = new DevExpress.XtraBars.BarManager(this.components);
            this.bar1 = new DevExpress.XtraBars.Bar();
            this.barBtn_Help = new DevExpress.XtraBars.BarButtonItem();
            this.barbtn_ImportInvoice = new DevExpress.XtraBars.BarButtonItem();
            this.barMnu_Print = new DevExpress.XtraBars.BarSubItem();
            this.barBtn_Print1 = new DevExpress.XtraBars.BarButtonItem();
            this.barBtn_PrintData = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnRefresh = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnOpen = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnNew = new DevExpress.XtraBars.BarButtonItem();
            this.barBtnClose = new DevExpress.XtraBars.BarButtonItem();
            this.barAndDockingController1 = new DevExpress.XtraBars.BarAndDockingController(this.components);
            this.barDockControlTop = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlBottom = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlLeft = new DevExpress.XtraBars.BarDockControl();
            this.barDockControlRight = new DevExpress.XtraBars.BarDockControl();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.bar2 = new DevExpress.XtraBars.Bar();
            this.grdDocuments = new DevExpress.XtraGrid.GridControl();
            this.contextMenuStrip1 = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.mi_Reject = new System.Windows.Forms.ToolStripMenuItem();
            this.gv_Documents = new DevExpress.XtraGrid.Views.Grid.GridView();
            this.col_uuid = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_issuerName = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_typeNameEn = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_dateTimeRecieved = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_status = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_totalDiscount = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Net = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Total = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_id = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_typeNameAr = new DevExpress.XtraGrid.Columns.GridColumn();
            this.col_Url = new DevExpress.XtraGrid.Columns.GridColumn();
            this.btnClearSearch = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.dt2 = new DevExpress.XtraEditors.DateEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.dt1 = new DevExpress.XtraEditors.DateEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.popup = new DevExpress.XtraBars.PopupControlContainer(this.components);
            this.xtraTabControl1 = new DevExpress.XtraTab.XtraTabControl();
            this.tab_RejectDocument = new DevExpress.XtraTab.XtraTabPage();
            this.btn_cancelReject = new DevExpress.XtraEditors.SimpleButton();
            this.txtRejectReasons = new DevExpress.XtraEditors.MemoEdit();
            this.btn_rejectDocument = new DevExpress.XtraEditors.SimpleButton();
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdDocuments)).BeginInit();
            this.contextMenuStrip1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.gv_Documents)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dt2.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dt2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dt1.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dt1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.popup)).BeginInit();
            this.popup.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).BeginInit();
            this.xtraTabControl1.SuspendLayout();
            this.tab_RejectDocument.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtRejectReasons.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // barManager1
            // 
            this.barManager1.AllowCustomization = false;
            this.barManager1.AllowQuickCustomization = false;
            this.barManager1.AutoSaveInRegistry = true;
            this.barManager1.Bars.AddRange(new DevExpress.XtraBars.Bar[] {
            this.bar1});
            this.barManager1.Controller = this.barAndDockingController1;
            this.barManager1.DockControls.Add(this.barDockControlTop);
            this.barManager1.DockControls.Add(this.barDockControlBottom);
            this.barManager1.DockControls.Add(this.barDockControlLeft);
            this.barManager1.DockControls.Add(this.barDockControlRight);
            this.barManager1.Form = this;
            this.barManager1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.barBtnNew,
            this.barBtnOpen,
            this.barBtn_Help,
            this.barBtnClose,
            this.barBtnRefresh,
            this.barMnu_Print,
            this.barBtn_Print1,
            this.barBtn_PrintData,
            this.barbtn_ImportInvoice});
            this.barManager1.MaxItemId = 34;
            this.barManager1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1});
            // 
            // bar1
            // 
            this.bar1.BarItemHorzIndent = 10;
            this.bar1.BarName = "Tools";
            this.bar1.DockCol = 0;
            this.bar1.DockRow = 0;
            this.bar1.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            this.bar1.FloatLocation = new System.Drawing.Point(567, 147);
            this.bar1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_Help),
            new DevExpress.XtraBars.LinkPersistInfo(DevExpress.XtraBars.BarLinkUserDefines.PaintStyle, this.barbtn_ImportInvoice, DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph),
            new DevExpress.XtraBars.LinkPersistInfo(this.barMnu_Print),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnRefresh),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnOpen),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnNew),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtnClose)});
            this.bar1.OptionsBar.AllowQuickCustomization = false;
            this.bar1.OptionsBar.DisableCustomization = true;
            this.bar1.OptionsBar.UseWholeRow = true;
            resources.ApplyResources(this.bar1, "bar1");
            // 
            // barBtn_Help
            // 
            resources.ApplyResources(this.barBtn_Help, "barBtn_Help");
            this.barBtn_Help.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtn_Help.Glyph = global::Pharmacy.Properties.Resources.hlp;
            this.barBtn_Help.Id = 2;
            this.barBtn_Help.ItemShortcut = new DevExpress.XtraBars.BarShortcut(System.Windows.Forms.Keys.F1);
            this.barBtn_Help.Name = "barBtn_Help";
            this.barBtn_Help.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtn_Help.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Help_ItemClick);
            // 
            // barbtn_ImportInvoice
            // 
            resources.ApplyResources(this.barbtn_ImportInvoice, "barbtn_ImportInvoice");
            this.barbtn_ImportInvoice.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barbtn_ImportInvoice.Glyph = ((System.Drawing.Image)(resources.GetObject("barbtn_ImportInvoice.Glyph")));
            this.barbtn_ImportInvoice.Id = 33;
            this.barbtn_ImportInvoice.LargeGlyph = ((System.Drawing.Image)(resources.GetObject("barbtn_ImportInvoice.LargeGlyph")));
            this.barbtn_ImportInvoice.Name = "barbtn_ImportInvoice";
            this.barbtn_ImportInvoice.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            this.barbtn_ImportInvoice.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barbtn_ImportInvoice_ItemClick);
            // 
            // barMnu_Print
            // 
            resources.ApplyResources(this.barMnu_Print, "barMnu_Print");
            this.barMnu_Print.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barMnu_Print.Glyph = global::Pharmacy.Properties.Resources.prnt;
            this.barMnu_Print.Id = 29;
            this.barMnu_Print.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_Print1),
            new DevExpress.XtraBars.LinkPersistInfo(this.barBtn_PrintData)});
            this.barMnu_Print.MenuAppearance.HeaderItemAppearance.FontSizeDelta = ((int)(resources.GetObject("barMnu_Print.MenuAppearance.HeaderItemAppearance.FontSizeDelta")));
            this.barMnu_Print.MenuAppearance.HeaderItemAppearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barMnu_Print.MenuAppearance.HeaderItemAppearance.FontStyleDelta")));
            this.barMnu_Print.MenuAppearance.HeaderItemAppearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barMnu_Print.MenuAppearance.HeaderItemAppearance.GradientMode")));
            this.barMnu_Print.MenuAppearance.HeaderItemAppearance.Image = ((System.Drawing.Image)(resources.GetObject("barMnu_Print.MenuAppearance.HeaderItemAppearance.Image")));
            this.barMnu_Print.Name = "barMnu_Print";
            this.barMnu_Print.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barMnu_Print.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            // 
            // barBtn_Print1
            // 
            resources.ApplyResources(this.barBtn_Print1, "barBtn_Print1");
            this.barBtn_Print1.Id = 30;
            this.barBtn_Print1.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.P));
            this.barBtn_Print1.Name = "barBtn_Print1";
            this.barBtn_Print1.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Print1_ItemClick);
            // 
            // barBtn_PrintData
            // 
            resources.ApplyResources(this.barBtn_PrintData, "barBtn_PrintData");
            this.barBtn_PrintData.Id = 31;
            this.barBtn_PrintData.Name = "barBtn_PrintData";
            this.barBtn_PrintData.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_PrintData_ItemClick);
            // 
            // barBtnRefresh
            // 
            resources.ApplyResources(this.barBtnRefresh, "barBtnRefresh");
            this.barBtnRefresh.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnRefresh.Glyph = global::Pharmacy.Properties.Resources.refresh;
            this.barBtnRefresh.Id = 26;
            this.barBtnRefresh.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.R));
            this.barBtnRefresh.Name = "barBtnRefresh";
            this.barBtnRefresh.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnRefresh.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            this.barBtnRefresh.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Refresh_ItemClick);
            // 
            // barBtnOpen
            // 
            resources.ApplyResources(this.barBtnOpen, "barBtnOpen");
            this.barBtnOpen.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnOpen.Glyph = global::Pharmacy.Properties.Resources.open;
            this.barBtnOpen.Id = 1;
            this.barBtnOpen.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.O));
            this.barBtnOpen.Name = "barBtnOpen";
            this.barBtnOpen.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnOpen.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            // 
            // barBtnNew
            // 
            resources.ApplyResources(this.barBtnNew, "barBtnNew");
            this.barBtnNew.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnNew.Glyph = global::Pharmacy.Properties.Resources._new;
            this.barBtnNew.Id = 0;
            this.barBtnNew.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.N));
            this.barBtnNew.Name = "barBtnNew";
            this.barBtnNew.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnNew.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            this.barBtnNew.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_New_ItemClick);
            // 
            // barBtnClose
            // 
            resources.ApplyResources(this.barBtnClose, "barBtnClose");
            this.barBtnClose.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barBtnClose.Glyph = global::Pharmacy.Properties.Resources.clse;
            this.barBtnClose.Id = 25;
            this.barBtnClose.ItemShortcut = new DevExpress.XtraBars.BarShortcut((System.Windows.Forms.Keys.Control | System.Windows.Forms.Keys.W));
            this.barBtnClose.Name = "barBtnClose";
            this.barBtnClose.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            this.barBtnClose.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barBtn_Close_ItemClick);
            // 
            // barAndDockingController1
            // 
            this.barAndDockingController1.PaintStyleName = "Skin";
            this.barAndDockingController1.PropertiesBar.AllowLinkLighting = false;
            this.barAndDockingController1.PropertiesBar.DefaultGlyphSize = new System.Drawing.Size(16, 16);
            this.barAndDockingController1.PropertiesBar.DefaultLargeGlyphSize = new System.Drawing.Size(32, 32);
            // 
            // barDockControlTop
            // 
            resources.ApplyResources(this.barDockControlTop, "barDockControlTop");
            this.barDockControlTop.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlTop.Appearance.FontSizeDelta")));
            this.barDockControlTop.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlTop.Appearance.FontStyleDelta")));
            this.barDockControlTop.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlTop.Appearance.GradientMode")));
            this.barDockControlTop.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlTop.Appearance.Image")));
            this.barDockControlTop.CausesValidation = false;
            // 
            // barDockControlBottom
            // 
            resources.ApplyResources(this.barDockControlBottom, "barDockControlBottom");
            this.barDockControlBottom.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlBottom.Appearance.FontSizeDelta")));
            this.barDockControlBottom.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlBottom.Appearance.FontStyleDelta")));
            this.barDockControlBottom.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlBottom.Appearance.GradientMode")));
            this.barDockControlBottom.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlBottom.Appearance.Image")));
            this.barDockControlBottom.CausesValidation = false;
            // 
            // barDockControlLeft
            // 
            resources.ApplyResources(this.barDockControlLeft, "barDockControlLeft");
            this.barDockControlLeft.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlLeft.Appearance.FontSizeDelta")));
            this.barDockControlLeft.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlLeft.Appearance.FontStyleDelta")));
            this.barDockControlLeft.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlLeft.Appearance.GradientMode")));
            this.barDockControlLeft.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlLeft.Appearance.Image")));
            this.barDockControlLeft.CausesValidation = false;
            // 
            // barDockControlRight
            // 
            resources.ApplyResources(this.barDockControlRight, "barDockControlRight");
            this.barDockControlRight.Appearance.FontSizeDelta = ((int)(resources.GetObject("barDockControlRight.Appearance.FontSizeDelta")));
            this.barDockControlRight.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("barDockControlRight.Appearance.FontStyleDelta")));
            this.barDockControlRight.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("barDockControlRight.Appearance.GradientMode")));
            this.barDockControlRight.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("barDockControlRight.Appearance.Image")));
            this.barDockControlRight.CausesValidation = false;
            // 
            // repositoryItemTextEdit1
            // 
            resources.ApplyResources(this.repositoryItemTextEdit1, "repositoryItemTextEdit1");
            this.repositoryItemTextEdit1.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("repositoryItemTextEdit1.Mask.AutoComplete")));
            this.repositoryItemTextEdit1.Mask.BeepOnError = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.BeepOnError")));
            this.repositoryItemTextEdit1.Mask.EditMask = resources.GetString("repositoryItemTextEdit1.Mask.EditMask");
            this.repositoryItemTextEdit1.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.IgnoreMaskBlank")));
            this.repositoryItemTextEdit1.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("repositoryItemTextEdit1.Mask.MaskType")));
            this.repositoryItemTextEdit1.Mask.PlaceHolder = ((char)(resources.GetObject("repositoryItemTextEdit1.Mask.PlaceHolder")));
            this.repositoryItemTextEdit1.Mask.SaveLiteral = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.SaveLiteral")));
            this.repositoryItemTextEdit1.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.ShowPlaceHolders")));
            this.repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat")));
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // bar2
            // 
            this.bar2.BarName = "Custom 3";
            this.bar2.DockCol = 0;
            this.bar2.DockStyle = DevExpress.XtraBars.BarDockStyle.Top;
            resources.ApplyResources(this.bar2, "bar2");
            // 
            // grdDocuments
            // 
            resources.ApplyResources(this.grdDocuments, "grdDocuments");
            this.grdDocuments.ContextMenuStrip = this.contextMenuStrip1;
            this.grdDocuments.Cursor = System.Windows.Forms.Cursors.Default;
            this.grdDocuments.EmbeddedNavigator.AccessibleDescription = resources.GetString("grdDocuments.EmbeddedNavigator.AccessibleDescription");
            this.grdDocuments.EmbeddedNavigator.AccessibleName = resources.GetString("grdDocuments.EmbeddedNavigator.AccessibleName");
            this.grdDocuments.EmbeddedNavigator.AllowHtmlTextInToolTip = ((DevExpress.Utils.DefaultBoolean)(resources.GetObject("grdDocuments.EmbeddedNavigator.AllowHtmlTextInToolTip")));
            this.grdDocuments.EmbeddedNavigator.Anchor = ((System.Windows.Forms.AnchorStyles)(resources.GetObject("grdDocuments.EmbeddedNavigator.Anchor")));
            this.grdDocuments.EmbeddedNavigator.BackgroundImage = ((System.Drawing.Image)(resources.GetObject("grdDocuments.EmbeddedNavigator.BackgroundImage")));
            this.grdDocuments.EmbeddedNavigator.BackgroundImageLayout = ((System.Windows.Forms.ImageLayout)(resources.GetObject("grdDocuments.EmbeddedNavigator.BackgroundImageLayout")));
            this.grdDocuments.EmbeddedNavigator.ImeMode = ((System.Windows.Forms.ImeMode)(resources.GetObject("grdDocuments.EmbeddedNavigator.ImeMode")));
            this.grdDocuments.EmbeddedNavigator.Margin = ((System.Windows.Forms.Padding)(resources.GetObject("grdDocuments.EmbeddedNavigator.Margin")));
            this.grdDocuments.EmbeddedNavigator.MaximumSize = ((System.Drawing.Size)(resources.GetObject("grdDocuments.EmbeddedNavigator.MaximumSize")));
            this.grdDocuments.EmbeddedNavigator.TextLocation = ((DevExpress.XtraEditors.NavigatorButtonsTextLocation)(resources.GetObject("grdDocuments.EmbeddedNavigator.TextLocation")));
            this.grdDocuments.EmbeddedNavigator.ToolTip = resources.GetString("grdDocuments.EmbeddedNavigator.ToolTip");
            this.grdDocuments.EmbeddedNavigator.ToolTipIconType = ((DevExpress.Utils.ToolTipIconType)(resources.GetObject("grdDocuments.EmbeddedNavigator.ToolTipIconType")));
            this.grdDocuments.EmbeddedNavigator.ToolTipTitle = resources.GetString("grdDocuments.EmbeddedNavigator.ToolTipTitle");
            this.grdDocuments.MainView = this.gv_Documents;
            this.grdDocuments.Name = "grdDocuments";
            this.grdDocuments.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.gv_Documents});
            this.grdDocuments.DoubleClick += new System.EventHandler(this.grdDocuments_DoubleClick);
            // 
            // contextMenuStrip1
            // 
            resources.ApplyResources(this.contextMenuStrip1, "contextMenuStrip1");
            this.contextMenuStrip1.ImageScalingSize = new System.Drawing.Size(24, 24);
            this.contextMenuStrip1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.mi_Reject});
            this.contextMenuStrip1.Name = "contextMenuStrip1";
            // 
            // mi_Reject
            // 
            resources.ApplyResources(this.mi_Reject, "mi_Reject");
            this.mi_Reject.Name = "mi_Reject";
            this.mi_Reject.Click += new System.EventHandler(this.mi_Reject_Click);
            // 
            // gv_Documents
            // 
            this.gv_Documents.Appearance.FooterPanel.FontSizeDelta = ((int)(resources.GetObject("gv_Documents.Appearance.FooterPanel.FontSizeDelta")));
            this.gv_Documents.Appearance.FooterPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gv_Documents.Appearance.FooterPanel.FontStyleDelta")));
            this.gv_Documents.Appearance.FooterPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gv_Documents.Appearance.FooterPanel.GradientMode")));
            this.gv_Documents.Appearance.FooterPanel.Image = ((System.Drawing.Image)(resources.GetObject("gv_Documents.Appearance.FooterPanel.Image")));
            this.gv_Documents.Appearance.FooterPanel.Options.UseTextOptions = true;
            this.gv_Documents.Appearance.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gv_Documents.Appearance.GroupPanel.FontSizeDelta = ((int)(resources.GetObject("gv_Documents.Appearance.GroupPanel.FontSizeDelta")));
            this.gv_Documents.Appearance.GroupPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gv_Documents.Appearance.GroupPanel.FontStyleDelta")));
            this.gv_Documents.Appearance.GroupPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gv_Documents.Appearance.GroupPanel.GradientMode")));
            this.gv_Documents.Appearance.GroupPanel.Image = ((System.Drawing.Image)(resources.GetObject("gv_Documents.Appearance.GroupPanel.Image")));
            this.gv_Documents.Appearance.GroupPanel.Options.UseTextOptions = true;
            this.gv_Documents.Appearance.GroupPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gv_Documents.Appearance.GroupRow.FontSizeDelta = ((int)(resources.GetObject("gv_Documents.Appearance.GroupRow.FontSizeDelta")));
            this.gv_Documents.Appearance.GroupRow.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gv_Documents.Appearance.GroupRow.FontStyleDelta")));
            this.gv_Documents.Appearance.GroupRow.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gv_Documents.Appearance.GroupRow.GradientMode")));
            this.gv_Documents.Appearance.GroupRow.Image = ((System.Drawing.Image)(resources.GetObject("gv_Documents.Appearance.GroupRow.Image")));
            this.gv_Documents.Appearance.GroupRow.Options.UseTextOptions = true;
            this.gv_Documents.Appearance.GroupRow.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gv_Documents.Appearance.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gv_Documents.Appearance.HeaderPanel.FontSizeDelta")));
            this.gv_Documents.Appearance.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gv_Documents.Appearance.HeaderPanel.FontStyleDelta")));
            this.gv_Documents.Appearance.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gv_Documents.Appearance.HeaderPanel.GradientMode")));
            this.gv_Documents.Appearance.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gv_Documents.Appearance.HeaderPanel.Image")));
            this.gv_Documents.Appearance.HeaderPanel.Options.UseTextOptions = true;
            this.gv_Documents.Appearance.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gv_Documents.Appearance.Row.FontSizeDelta = ((int)(resources.GetObject("gv_Documents.Appearance.Row.FontSizeDelta")));
            this.gv_Documents.Appearance.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gv_Documents.Appearance.Row.FontStyleDelta")));
            this.gv_Documents.Appearance.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gv_Documents.Appearance.Row.GradientMode")));
            this.gv_Documents.Appearance.Row.Image = ((System.Drawing.Image)(resources.GetObject("gv_Documents.Appearance.Row.Image")));
            this.gv_Documents.Appearance.Row.Options.UseTextOptions = true;
            this.gv_Documents.Appearance.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gv_Documents.AppearancePrint.FooterPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("gv_Documents.AppearancePrint.FooterPanel.BorderColor")));
            this.gv_Documents.AppearancePrint.FooterPanel.FontSizeDelta = ((int)(resources.GetObject("gv_Documents.AppearancePrint.FooterPanel.FontSizeDelta")));
            this.gv_Documents.AppearancePrint.FooterPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gv_Documents.AppearancePrint.FooterPanel.FontStyleDelta")));
            this.gv_Documents.AppearancePrint.FooterPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gv_Documents.AppearancePrint.FooterPanel.ForeColor")));
            this.gv_Documents.AppearancePrint.FooterPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gv_Documents.AppearancePrint.FooterPanel.GradientMode")));
            this.gv_Documents.AppearancePrint.FooterPanel.Image = ((System.Drawing.Image)(resources.GetObject("gv_Documents.AppearancePrint.FooterPanel.Image")));
            this.gv_Documents.AppearancePrint.FooterPanel.Options.UseBorderColor = true;
            this.gv_Documents.AppearancePrint.FooterPanel.Options.UseForeColor = true;
            this.gv_Documents.AppearancePrint.FooterPanel.Options.UseTextOptions = true;
            this.gv_Documents.AppearancePrint.FooterPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gv_Documents.AppearancePrint.GroupFooter.BorderColor = ((System.Drawing.Color)(resources.GetObject("gv_Documents.AppearancePrint.GroupFooter.BorderColor")));
            this.gv_Documents.AppearancePrint.GroupFooter.FontSizeDelta = ((int)(resources.GetObject("gv_Documents.AppearancePrint.GroupFooter.FontSizeDelta")));
            this.gv_Documents.AppearancePrint.GroupFooter.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gv_Documents.AppearancePrint.GroupFooter.FontStyleDelta")));
            this.gv_Documents.AppearancePrint.GroupFooter.ForeColor = ((System.Drawing.Color)(resources.GetObject("gv_Documents.AppearancePrint.GroupFooter.ForeColor")));
            this.gv_Documents.AppearancePrint.GroupFooter.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gv_Documents.AppearancePrint.GroupFooter.GradientMode")));
            this.gv_Documents.AppearancePrint.GroupFooter.Image = ((System.Drawing.Image)(resources.GetObject("gv_Documents.AppearancePrint.GroupFooter.Image")));
            this.gv_Documents.AppearancePrint.GroupFooter.Options.UseBorderColor = true;
            this.gv_Documents.AppearancePrint.GroupFooter.Options.UseForeColor = true;
            this.gv_Documents.AppearancePrint.GroupFooter.Options.UseTextOptions = true;
            this.gv_Documents.AppearancePrint.GroupFooter.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gv_Documents.AppearancePrint.GroupRow.BorderColor = ((System.Drawing.Color)(resources.GetObject("gv_Documents.AppearancePrint.GroupRow.BorderColor")));
            this.gv_Documents.AppearancePrint.GroupRow.FontSizeDelta = ((int)(resources.GetObject("gv_Documents.AppearancePrint.GroupRow.FontSizeDelta")));
            this.gv_Documents.AppearancePrint.GroupRow.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gv_Documents.AppearancePrint.GroupRow.FontStyleDelta")));
            this.gv_Documents.AppearancePrint.GroupRow.ForeColor = ((System.Drawing.Color)(resources.GetObject("gv_Documents.AppearancePrint.GroupRow.ForeColor")));
            this.gv_Documents.AppearancePrint.GroupRow.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gv_Documents.AppearancePrint.GroupRow.GradientMode")));
            this.gv_Documents.AppearancePrint.GroupRow.Image = ((System.Drawing.Image)(resources.GetObject("gv_Documents.AppearancePrint.GroupRow.Image")));
            this.gv_Documents.AppearancePrint.GroupRow.Options.UseBorderColor = true;
            this.gv_Documents.AppearancePrint.GroupRow.Options.UseForeColor = true;
            this.gv_Documents.AppearancePrint.GroupRow.Options.UseTextOptions = true;
            this.gv_Documents.AppearancePrint.GroupRow.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gv_Documents.AppearancePrint.HeaderPanel.BorderColor = ((System.Drawing.Color)(resources.GetObject("gv_Documents.AppearancePrint.HeaderPanel.BorderColor")));
            this.gv_Documents.AppearancePrint.HeaderPanel.FontSizeDelta = ((int)(resources.GetObject("gv_Documents.AppearancePrint.HeaderPanel.FontSizeDelta")));
            this.gv_Documents.AppearancePrint.HeaderPanel.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gv_Documents.AppearancePrint.HeaderPanel.FontStyleDelta")));
            this.gv_Documents.AppearancePrint.HeaderPanel.ForeColor = ((System.Drawing.Color)(resources.GetObject("gv_Documents.AppearancePrint.HeaderPanel.ForeColor")));
            this.gv_Documents.AppearancePrint.HeaderPanel.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gv_Documents.AppearancePrint.HeaderPanel.GradientMode")));
            this.gv_Documents.AppearancePrint.HeaderPanel.Image = ((System.Drawing.Image)(resources.GetObject("gv_Documents.AppearancePrint.HeaderPanel.Image")));
            this.gv_Documents.AppearancePrint.HeaderPanel.Options.UseBorderColor = true;
            this.gv_Documents.AppearancePrint.HeaderPanel.Options.UseForeColor = true;
            this.gv_Documents.AppearancePrint.HeaderPanel.Options.UseTextOptions = true;
            this.gv_Documents.AppearancePrint.HeaderPanel.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.gv_Documents.AppearancePrint.Lines.BackColor = ((System.Drawing.Color)(resources.GetObject("gv_Documents.AppearancePrint.Lines.BackColor")));
            this.gv_Documents.AppearancePrint.Lines.FontSizeDelta = ((int)(resources.GetObject("gv_Documents.AppearancePrint.Lines.FontSizeDelta")));
            this.gv_Documents.AppearancePrint.Lines.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gv_Documents.AppearancePrint.Lines.FontStyleDelta")));
            this.gv_Documents.AppearancePrint.Lines.ForeColor = ((System.Drawing.Color)(resources.GetObject("gv_Documents.AppearancePrint.Lines.ForeColor")));
            this.gv_Documents.AppearancePrint.Lines.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gv_Documents.AppearancePrint.Lines.GradientMode")));
            this.gv_Documents.AppearancePrint.Lines.Image = ((System.Drawing.Image)(resources.GetObject("gv_Documents.AppearancePrint.Lines.Image")));
            this.gv_Documents.AppearancePrint.Lines.Options.UseBackColor = true;
            this.gv_Documents.AppearancePrint.Lines.Options.UseForeColor = true;
            this.gv_Documents.AppearancePrint.Row.BorderColor = ((System.Drawing.Color)(resources.GetObject("gv_Documents.AppearancePrint.Row.BorderColor")));
            this.gv_Documents.AppearancePrint.Row.FontSizeDelta = ((int)(resources.GetObject("gv_Documents.AppearancePrint.Row.FontSizeDelta")));
            this.gv_Documents.AppearancePrint.Row.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("gv_Documents.AppearancePrint.Row.FontStyleDelta")));
            this.gv_Documents.AppearancePrint.Row.ForeColor = ((System.Drawing.Color)(resources.GetObject("gv_Documents.AppearancePrint.Row.ForeColor")));
            this.gv_Documents.AppearancePrint.Row.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("gv_Documents.AppearancePrint.Row.GradientMode")));
            this.gv_Documents.AppearancePrint.Row.Image = ((System.Drawing.Image)(resources.GetObject("gv_Documents.AppearancePrint.Row.Image")));
            this.gv_Documents.AppearancePrint.Row.Options.UseBorderColor = true;
            this.gv_Documents.AppearancePrint.Row.Options.UseForeColor = true;
            this.gv_Documents.AppearancePrint.Row.Options.UseTextOptions = true;
            this.gv_Documents.AppearancePrint.Row.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            resources.ApplyResources(this.gv_Documents, "gv_Documents");
            this.gv_Documents.ColumnPanelRowHeight = 35;
            this.gv_Documents.Columns.AddRange(new DevExpress.XtraGrid.Columns.GridColumn[] {
            this.col_uuid,
            this.col_issuerName,
            this.col_typeNameEn,
            this.col_dateTimeRecieved,
            this.col_status,
            this.col_totalDiscount,
            this.col_Net,
            this.col_Total,
            this.col_id,
            this.col_typeNameAr,
            this.col_Url});
            this.gv_Documents.CustomizationFormBounds = new System.Drawing.Rectangle(959, 364, 210, 277);
            this.gv_Documents.GridControl = this.grdDocuments;
            this.gv_Documents.Name = "gv_Documents";
            this.gv_Documents.OptionsBehavior.Editable = false;
            this.gv_Documents.OptionsSelection.EnableAppearanceFocusedCell = false;
            this.gv_Documents.OptionsView.EnableAppearanceEvenRow = true;
            this.gv_Documents.OptionsView.GroupFooterShowMode = DevExpress.XtraGrid.Views.Grid.GroupFooterShowMode.VisibleAlways;
            this.gv_Documents.OptionsView.ShowAutoFilterRow = true;
            this.gv_Documents.OptionsView.ShowFooter = true;
            this.gv_Documents.OptionsView.ShowIndicator = false;
            // 
            // col_uuid
            // 
            resources.ApplyResources(this.col_uuid, "col_uuid");
            this.col_uuid.FieldName = "uuid";
            this.col_uuid.Name = "col_uuid";
            this.col_uuid.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // col_issuerName
            // 
            resources.ApplyResources(this.col_issuerName, "col_issuerName");
            this.col_issuerName.FieldName = "issuerName";
            this.col_issuerName.Name = "col_issuerName";
            this.col_issuerName.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // col_typeNameEn
            // 
            resources.ApplyResources(this.col_typeNameEn, "col_typeNameEn");
            this.col_typeNameEn.FieldName = "typeNameEn";
            this.col_typeNameEn.Name = "col_typeNameEn";
            this.col_typeNameEn.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Contains;
            this.col_typeNameEn.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // col_dateTimeRecieved
            // 
            resources.ApplyResources(this.col_dateTimeRecieved, "col_dateTimeRecieved");
            this.col_dateTimeRecieved.FieldName = "dateTimeReceived";
            this.col_dateTimeRecieved.Name = "col_dateTimeRecieved";
            this.col_dateTimeRecieved.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // col_status
            // 
            resources.ApplyResources(this.col_status, "col_status");
            this.col_status.FieldName = "status";
            this.col_status.Name = "col_status";
            this.col_status.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // col_totalDiscount
            // 
            resources.ApplyResources(this.col_totalDiscount, "col_totalDiscount");
            this.col_totalDiscount.DisplayFormat.FormatString = "n2";
            this.col_totalDiscount.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_totalDiscount.FieldName = "totalDiscount";
            this.col_totalDiscount.Name = "col_totalDiscount";
            this.col_totalDiscount.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // col_Net
            // 
            resources.ApplyResources(this.col_Net, "col_Net");
            this.col_Net.DisplayFormat.FormatString = "n2";
            this.col_Net.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_Net.FieldName = "net";
            this.col_Net.GroupFormat.FormatString = "n2";
            this.col_Net.GroupFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_Net.Name = "col_Net";
            this.col_Net.OptionsFilter.AutoFilterCondition = DevExpress.XtraGrid.Columns.AutoFilterCondition.Equals;
            this.col_Net.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem(((DevExpress.Data.SummaryItemType)(resources.GetObject("col_Net.Summary"))), resources.GetString("col_Net.Summary1"), resources.GetString("col_Net.Summary2"))});
            // 
            // col_Total
            // 
            resources.ApplyResources(this.col_Total, "col_Total");
            this.col_Total.DisplayFormat.FormatString = "n2";
            this.col_Total.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.col_Total.FieldName = "total";
            this.col_Total.Name = "col_Total";
            this.col_Total.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // col_id
            // 
            resources.ApplyResources(this.col_id, "col_id");
            this.col_id.FieldName = "id";
            this.col_id.Name = "col_id";
            this.col_id.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // col_typeNameAr
            // 
            resources.ApplyResources(this.col_typeNameAr, "col_typeNameAr");
            this.col_typeNameAr.FieldName = "typeNameAr";
            this.col_typeNameAr.Name = "col_typeNameAr";
            this.col_typeNameAr.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // col_Url
            // 
            resources.ApplyResources(this.col_Url, "col_Url");
            this.col_Url.FieldName = "publicUrl";
            this.col_Url.Name = "col_Url";
            this.col_Url.Summary.AddRange(new DevExpress.XtraGrid.GridSummaryItem[] {
            new DevExpress.XtraGrid.GridColumnSummaryItem()});
            // 
            // btnClearSearch
            // 
            resources.ApplyResources(this.btnClearSearch, "btnClearSearch");
            this.btnClearSearch.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.Flat;
            this.btnClearSearch.Name = "btnClearSearch";
            this.btnClearSearch.TabStop = false;
            // 
            // labelControl3
            // 
            resources.ApplyResources(this.labelControl3, "labelControl3");
            this.labelControl3.Appearance.Font = ((System.Drawing.Font)(resources.GetObject("labelControl3.Appearance.Font")));
            this.labelControl3.Appearance.FontSizeDelta = ((int)(resources.GetObject("labelControl3.Appearance.FontSizeDelta")));
            this.labelControl3.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("labelControl3.Appearance.FontStyleDelta")));
            this.labelControl3.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("labelControl3.Appearance.ForeColor")));
            this.labelControl3.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("labelControl3.Appearance.GradientMode")));
            this.labelControl3.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("labelControl3.Appearance.Image")));
            this.labelControl3.Name = "labelControl3";
            // 
            // dt2
            // 
            resources.ApplyResources(this.dt2, "dt2");
            this.dt2.EnterMoveNextControl = true;
            this.dt2.Name = "dt2";
            this.dt2.Properties.AccessibleDescription = resources.GetString("dt2.Properties.AccessibleDescription");
            this.dt2.Properties.AccessibleName = resources.GetString("dt2.Properties.AccessibleName");
            this.dt2.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.dt2.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("dt2.Properties.Appearance.FontSizeDelta")));
            this.dt2.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("dt2.Properties.Appearance.FontStyleDelta")));
            this.dt2.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("dt2.Properties.Appearance.GradientMode")));
            this.dt2.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("dt2.Properties.Appearance.Image")));
            this.dt2.Properties.Appearance.Options.UseTextOptions = true;
            this.dt2.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.dt2.Properties.AutoHeight = ((bool)(resources.GetObject("dt2.Properties.AutoHeight")));
            this.dt2.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("dt2.Properties.Buttons"))))});
            this.dt2.Properties.CalendarTimeProperties.AccessibleDescription = resources.GetString("dt2.Properties.CalendarTimeProperties.AccessibleDescription");
            this.dt2.Properties.CalendarTimeProperties.AccessibleName = resources.GetString("dt2.Properties.CalendarTimeProperties.AccessibleName");
            this.dt2.Properties.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("dt2.Properties.CalendarTimeProperties.AutoHeight")));
            this.dt2.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dt2.Properties.CalendarTimeProperties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dt2.Properties.CalendarTimeProperties.Mask.AutoComplete")));
            this.dt2.Properties.CalendarTimeProperties.Mask.BeepOnError = ((bool)(resources.GetObject("dt2.Properties.CalendarTimeProperties.Mask.BeepOnError")));
            this.dt2.Properties.CalendarTimeProperties.Mask.EditMask = resources.GetString("dt2.Properties.CalendarTimeProperties.Mask.EditMask");
            this.dt2.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dt2.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.dt2.Properties.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dt2.Properties.CalendarTimeProperties.Mask.MaskType")));
            this.dt2.Properties.CalendarTimeProperties.Mask.PlaceHolder = ((char)(resources.GetObject("dt2.Properties.CalendarTimeProperties.Mask.PlaceHolder")));
            this.dt2.Properties.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("dt2.Properties.CalendarTimeProperties.Mask.SaveLiteral")));
            this.dt2.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dt2.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.dt2.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dt2.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat")));
            this.dt2.Properties.CalendarTimeProperties.NullValuePrompt = resources.GetString("dt2.Properties.CalendarTimeProperties.NullValuePrompt");
            this.dt2.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dt2.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue")));
            this.dt2.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dt2.Properties.Mask.AutoComplete")));
            this.dt2.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("dt2.Properties.Mask.BeepOnError")));
            this.dt2.Properties.Mask.EditMask = resources.GetString("dt2.Properties.Mask.EditMask");
            this.dt2.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dt2.Properties.Mask.IgnoreMaskBlank")));
            this.dt2.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dt2.Properties.Mask.MaskType")));
            this.dt2.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("dt2.Properties.Mask.PlaceHolder")));
            this.dt2.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("dt2.Properties.Mask.SaveLiteral")));
            this.dt2.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dt2.Properties.Mask.ShowPlaceHolders")));
            this.dt2.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dt2.Properties.Mask.UseMaskAsDisplayFormat")));
            this.dt2.Properties.NullValuePrompt = resources.GetString("dt2.Properties.NullValuePrompt");
            this.dt2.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dt2.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl2
            // 
            resources.ApplyResources(this.labelControl2, "labelControl2");
            this.labelControl2.Name = "labelControl2";
            // 
            // dt1
            // 
            resources.ApplyResources(this.dt1, "dt1");
            this.dt1.EnterMoveNextControl = true;
            this.dt1.Name = "dt1";
            this.dt1.Properties.AccessibleDescription = resources.GetString("dt1.Properties.AccessibleDescription");
            this.dt1.Properties.AccessibleName = resources.GetString("dt1.Properties.AccessibleName");
            this.dt1.Properties.AllowNullInput = DevExpress.Utils.DefaultBoolean.True;
            this.dt1.Properties.Appearance.FontSizeDelta = ((int)(resources.GetObject("dt1.Properties.Appearance.FontSizeDelta")));
            this.dt1.Properties.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("dt1.Properties.Appearance.FontStyleDelta")));
            this.dt1.Properties.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("dt1.Properties.Appearance.GradientMode")));
            this.dt1.Properties.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("dt1.Properties.Appearance.Image")));
            this.dt1.Properties.Appearance.Options.UseTextOptions = true;
            this.dt1.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.dt1.Properties.AutoHeight = ((bool)(resources.GetObject("dt1.Properties.AutoHeight")));
            this.dt1.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(((DevExpress.XtraEditors.Controls.ButtonPredefines)(resources.GetObject("dt1.Properties.Buttons"))))});
            this.dt1.Properties.CalendarTimeProperties.AccessibleDescription = resources.GetString("dt1.Properties.CalendarTimeProperties.AccessibleDescription");
            this.dt1.Properties.CalendarTimeProperties.AccessibleName = resources.GetString("dt1.Properties.CalendarTimeProperties.AccessibleName");
            this.dt1.Properties.CalendarTimeProperties.AutoHeight = ((bool)(resources.GetObject("dt1.Properties.CalendarTimeProperties.AutoHeight")));
            this.dt1.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.dt1.Properties.CalendarTimeProperties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dt1.Properties.CalendarTimeProperties.Mask.AutoComplete")));
            this.dt1.Properties.CalendarTimeProperties.Mask.BeepOnError = ((bool)(resources.GetObject("dt1.Properties.CalendarTimeProperties.Mask.BeepOnError")));
            this.dt1.Properties.CalendarTimeProperties.Mask.EditMask = resources.GetString("dt1.Properties.CalendarTimeProperties.Mask.EditMask");
            this.dt1.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dt1.Properties.CalendarTimeProperties.Mask.IgnoreMaskBlank")));
            this.dt1.Properties.CalendarTimeProperties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dt1.Properties.CalendarTimeProperties.Mask.MaskType")));
            this.dt1.Properties.CalendarTimeProperties.Mask.PlaceHolder = ((char)(resources.GetObject("dt1.Properties.CalendarTimeProperties.Mask.PlaceHolder")));
            this.dt1.Properties.CalendarTimeProperties.Mask.SaveLiteral = ((bool)(resources.GetObject("dt1.Properties.CalendarTimeProperties.Mask.SaveLiteral")));
            this.dt1.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dt1.Properties.CalendarTimeProperties.Mask.ShowPlaceHolders")));
            this.dt1.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dt1.Properties.CalendarTimeProperties.Mask.UseMaskAsDisplayFormat")));
            this.dt1.Properties.CalendarTimeProperties.NullValuePrompt = resources.GetString("dt1.Properties.CalendarTimeProperties.NullValuePrompt");
            this.dt1.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dt1.Properties.CalendarTimeProperties.NullValuePromptShowForEmptyValue")));
            this.dt1.Properties.Mask.AutoComplete = ((DevExpress.XtraEditors.Mask.AutoCompleteType)(resources.GetObject("dt1.Properties.Mask.AutoComplete")));
            this.dt1.Properties.Mask.BeepOnError = ((bool)(resources.GetObject("dt1.Properties.Mask.BeepOnError")));
            this.dt1.Properties.Mask.EditMask = resources.GetString("dt1.Properties.Mask.EditMask");
            this.dt1.Properties.Mask.IgnoreMaskBlank = ((bool)(resources.GetObject("dt1.Properties.Mask.IgnoreMaskBlank")));
            this.dt1.Properties.Mask.MaskType = ((DevExpress.XtraEditors.Mask.MaskType)(resources.GetObject("dt1.Properties.Mask.MaskType")));
            this.dt1.Properties.Mask.PlaceHolder = ((char)(resources.GetObject("dt1.Properties.Mask.PlaceHolder")));
            this.dt1.Properties.Mask.SaveLiteral = ((bool)(resources.GetObject("dt1.Properties.Mask.SaveLiteral")));
            this.dt1.Properties.Mask.ShowPlaceHolders = ((bool)(resources.GetObject("dt1.Properties.Mask.ShowPlaceHolders")));
            this.dt1.Properties.Mask.UseMaskAsDisplayFormat = ((bool)(resources.GetObject("dt1.Properties.Mask.UseMaskAsDisplayFormat")));
            this.dt1.Properties.NullValuePrompt = resources.GetString("dt1.Properties.NullValuePrompt");
            this.dt1.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("dt1.Properties.NullValuePromptShowForEmptyValue")));
            // 
            // labelControl1
            // 
            resources.ApplyResources(this.labelControl1, "labelControl1");
            this.labelControl1.Name = "labelControl1";
            // 
            // popup
            // 
            resources.ApplyResources(this.popup, "popup");
            this.popup.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.popup.Controls.Add(this.xtraTabControl1);
            this.popup.Manager = this.barManager1;
            this.popup.Name = "popup";
            // 
            // xtraTabControl1
            // 
            resources.ApplyResources(this.xtraTabControl1, "xtraTabControl1");
            this.xtraTabControl1.AppearancePage.Header.FontSizeDelta = ((int)(resources.GetObject("xtraTabControl1.AppearancePage.Header.FontSizeDelta")));
            this.xtraTabControl1.AppearancePage.Header.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("xtraTabControl1.AppearancePage.Header.FontStyleDelta")));
            this.xtraTabControl1.AppearancePage.Header.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("xtraTabControl1.AppearancePage.Header.GradientMode")));
            this.xtraTabControl1.AppearancePage.Header.Image = ((System.Drawing.Image)(resources.GetObject("xtraTabControl1.AppearancePage.Header.Image")));
            this.xtraTabControl1.AppearancePage.Header.Options.UseTextOptions = true;
            this.xtraTabControl1.AppearancePage.Header.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
            this.xtraTabControl1.Name = "xtraTabControl1";
            this.xtraTabControl1.SelectedTabPage = this.tab_RejectDocument;
            this.xtraTabControl1.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.tab_RejectDocument});
            // 
            // tab_RejectDocument
            // 
            resources.ApplyResources(this.tab_RejectDocument, "tab_RejectDocument");
            this.tab_RejectDocument.Controls.Add(this.btn_cancelReject);
            this.tab_RejectDocument.Controls.Add(this.txtRejectReasons);
            this.tab_RejectDocument.Controls.Add(this.btn_rejectDocument);
            this.tab_RejectDocument.Name = "tab_RejectDocument";
            // 
            // btn_cancelReject
            // 
            resources.ApplyResources(this.btn_cancelReject, "btn_cancelReject");
            this.btn_cancelReject.Name = "btn_cancelReject";
            this.btn_cancelReject.Click += new System.EventHandler(this.btn_cancelReject_Click);
            // 
            // txtRejectReasons
            // 
            resources.ApplyResources(this.txtRejectReasons, "txtRejectReasons");
            this.txtRejectReasons.MenuManager = this.barManager1;
            this.txtRejectReasons.Name = "txtRejectReasons";
            this.txtRejectReasons.Properties.AccessibleDescription = resources.GetString("txtRejectReasons.Properties.AccessibleDescription");
            this.txtRejectReasons.Properties.AccessibleName = resources.GetString("txtRejectReasons.Properties.AccessibleName");
            this.txtRejectReasons.Properties.NullValuePrompt = resources.GetString("txtRejectReasons.Properties.NullValuePrompt");
            this.txtRejectReasons.Properties.NullValuePromptShowForEmptyValue = ((bool)(resources.GetObject("txtRejectReasons.Properties.NullValuePromptShowForEmptyValue")));
            this.txtRejectReasons.Properties.ScrollBars = System.Windows.Forms.ScrollBars.Both;
            // 
            // btn_rejectDocument
            // 
            resources.ApplyResources(this.btn_rejectDocument, "btn_rejectDocument");
            this.btn_rejectDocument.Appearance.FontSizeDelta = ((int)(resources.GetObject("btn_rejectDocument.Appearance.FontSizeDelta")));
            this.btn_rejectDocument.Appearance.FontStyleDelta = ((System.Drawing.FontStyle)(resources.GetObject("btn_rejectDocument.Appearance.FontStyleDelta")));
            this.btn_rejectDocument.Appearance.ForeColor = ((System.Drawing.Color)(resources.GetObject("btn_rejectDocument.Appearance.ForeColor")));
            this.btn_rejectDocument.Appearance.GradientMode = ((System.Drawing.Drawing2D.LinearGradientMode)(resources.GetObject("btn_rejectDocument.Appearance.GradientMode")));
            this.btn_rejectDocument.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("btn_rejectDocument.Appearance.Image")));
            this.btn_rejectDocument.Appearance.Options.UseForeColor = true;
            this.btn_rejectDocument.Name = "btn_rejectDocument";
            this.btn_rejectDocument.Click += new System.EventHandler(this.btn_rejectDocument_Click);
            // 
            // frm_E_RecievedInvoices
            // 
            resources.ApplyResources(this, "$this");
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.popup);
            this.Controls.Add(this.btnClearSearch);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.dt2);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.dt1);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.grdDocuments);
            this.Controls.Add(this.barDockControlLeft);
            this.Controls.Add(this.barDockControlRight);
            this.Controls.Add(this.barDockControlBottom);
            this.Controls.Add(this.barDockControlTop);
            this.KeyPreview = true;
            this.Name = "frm_E_RecievedInvoices";
            this.Load += new System.EventHandler(this.frm_E_InvoiceList_Load);
            this.KeyUp += new System.Windows.Forms.KeyEventHandler(this.frm_E_InvoiceList_KeyUp);
            ((System.ComponentModel.ISupportInitialize)(this.barManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.barAndDockingController1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.grdDocuments)).EndInit();
            this.contextMenuStrip1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.gv_Documents)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dt2.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dt2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dt1.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dt1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.popup)).EndInit();
            this.popup.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.xtraTabControl1)).EndInit();
            this.xtraTabControl1.ResumeLayout(false);
            this.tab_RejectDocument.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtRejectReasons.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.BarManager barManager1;
        private DevExpress.XtraBars.Bar bar1;
        private DevExpress.XtraBars.BarButtonItem barBtnNew;
        private DevExpress.XtraBars.BarButtonItem barBtnOpen;
        private DevExpress.XtraBars.BarDockControl barDockControlTop;
        private DevExpress.XtraBars.BarDockControl barDockControlBottom;
        private DevExpress.XtraBars.BarDockControl barDockControlLeft;
        private DevExpress.XtraBars.BarDockControl barDockControlRight;
        private DevExpress.XtraBars.BarButtonItem barBtn_Help;
        private DevExpress.XtraBars.BarAndDockingController barAndDockingController1;
        private DevExpress.XtraBars.Bar bar2;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraGrid.GridControl grdDocuments;
        private DevExpress.XtraGrid.Views.Grid.GridView gv_Documents;
        private DevExpress.XtraBars.BarButtonItem barBtnClose;
        private DevExpress.XtraBars.BarButtonItem barBtnRefresh;
        private DevExpress.XtraGrid.Columns.GridColumn col_typeNameEn;
        private DevExpress.XtraGrid.Columns.GridColumn col_Net;
        private DevExpress.XtraEditors.SimpleButton btnClearSearch;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.DateEdit dt2;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.DateEdit dt1;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private System.Windows.Forms.ContextMenuStrip contextMenuStrip1;
        private System.Windows.Forms.ToolStripMenuItem mi_Reject;
        private DevExpress.XtraBars.BarSubItem barMnu_Print;
        private DevExpress.XtraBars.BarButtonItem barBtn_Print1;
        private DevExpress.XtraBars.BarButtonItem barBtn_PrintData;
        private DevExpress.XtraGrid.Columns.GridColumn col_Total;
        private DevExpress.XtraGrid.Columns.GridColumn col_uuid;
        private DevExpress.XtraGrid.Columns.GridColumn col_status;
        private DevExpress.XtraGrid.Columns.GridColumn col_issuerName;
        private DevExpress.XtraGrid.Columns.GridColumn col_dateTimeRecieved;
        private DevExpress.XtraBars.BarButtonItem barbtn_ImportInvoice;
        private DevExpress.XtraBars.PopupControlContainer popup;
        private DevExpress.XtraTab.XtraTabControl xtraTabControl1;
        private DevExpress.XtraTab.XtraTabPage tab_RejectDocument;
        private DevExpress.XtraEditors.SimpleButton btn_rejectDocument;
        private DevExpress.XtraEditors.MemoEdit txtRejectReasons;
        private DevExpress.XtraEditors.SimpleButton btn_cancelReject;
        private DevExpress.XtraGrid.Columns.GridColumn col_id;
        private DevExpress.XtraGrid.Columns.GridColumn col_typeNameAr;
        private DevExpress.XtraGrid.Columns.GridColumn col_totalDiscount;
        private DevExpress.XtraGrid.Columns.GridColumn col_Url;
    }
}