﻿using System;
using System.Drawing;
using System.Collections;
using System.ComponentModel;
using DevExpress.XtraReports.UI;
using DAL;
using System.Linq;
using System.Data;
using DevExpress.XtraEditors;

using System.Windows.Forms;

namespace Reports
{
    public partial class rpt_SL_Invoice : DevExpress.XtraReports.UI.XtraReport
    {
        string customer, serial, number, date, store, paymethod, drawer, notes,
            total, taxR, taxV, discountR, discountV, expensesR, expensesV, net, paied, remains, userName, SalesEmp,
            Shipping, PurchaseOrderNo, DeliverDate, salesEmp_Job, DeductTaxV, AddTaxV,
            BalanceBefore, BalanceAfter, DriverName, VehicleNumber, Destination, ProcessName, SourceCode, scalWeightSerial,
            CusTaxV, CostCenter, retention, advancepayment, dueDate, TaxFileNumber, TaxCardNumber, totalPieces, Delivery;

        DataTable dt_Weights;

        //private void xrSubreport2_BeforePrint(object sender, System.Drawing.Printing.PrintEventArgs e)
        //{
        //    if (dt_Weights.Rows.Count > 0)
        //        xrSubreport2.ReportSource = new rpt_multiple_weights(dt_Weights);
        //}

        int CustomerId = 0;

        DataTable dt_inv_details;
        int currId;
        decimal[] trans_hand;

        public rpt_SL_Invoice()
        {
            InitializeComponent();
        }
        public rpt_SL_Invoice(string _customer, string _serial, string _number, string _date, string _store, string _paymethod,
            string _drawer, string _notes, string _total, string _taxR, string _taxV, string _discountR, string _discountV,
            string _expensesR, string _expensesV, string _net, string _paied, string _remains, DataTable dt, string userName,
            string _salesEmp, string _Shipping, string _PurchaseOrderNo, string _DeliverDate, string _salesEmp_Job, string _DeductTaxV,
            string _AddTaxV, int _currId, string _BalanceBefore, string _BalanceAfter, string DriverName, string VehicleNumber,
            string Destination, string ProcessName, string SourceCode, string scalWeightSerial, string _CusR, string _CusV,
            string _CostCenter, string _Retention, string _AdvancePayment, string _dueDate, int CustomerId, decimal[] trans_hand, string _totalPieces)
        {
            InitializeComponent();
            customer = _customer;
            serial = _serial;
            number = _number;
            date = _date;
            store = _store;
            paymethod = _paymethod;
            drawer = _drawer;
            notes = _notes;
            total = _total;
            taxR = _taxR;
            taxV = _taxV;
            CusTaxV = _CusV;
            discountR = _discountR;
            discountV = _discountV;
            expensesR = _expensesR;
            expensesV = _expensesV;
            net = _net;
            paied = _paied;
            remains = _remains;
            this.userName = userName;
            SalesEmp = _salesEmp;
            salesEmp_Job = _salesEmp_Job;

            Shipping = _Shipping;
            PurchaseOrderNo = _PurchaseOrderNo;
            DeliverDate = _DeliverDate;
            dueDate = _dueDate; ;
            DeductTaxV = _DeductTaxV;
            AddTaxV = _AddTaxV;
            CostCenter = _CostCenter;
            retention = _Retention;
            advancepayment = _AdvancePayment;
            this.DriverName = DriverName;
            this.VehicleNumber = VehicleNumber;
            this.Destination = Destination;
            this.ProcessName = ProcessName;
            this.SourceCode = SourceCode;
            this.scalWeightSerial = scalWeightSerial;

            currId = _currId;
            this.CustomerId = CustomerId;

            BalanceBefore = _BalanceBefore;
            BalanceAfter = _BalanceAfter;

            this.trans_hand = trans_hand;

            dt_inv_details = dt;
            //this.DataSource = dt_inv_details;
            totalPieces = _totalPieces;

            //dt_Weights = _dt_Weights;

            getReportHeader();

            //LoadData();            
        }


        public rpt_SL_Invoice(string _customer, string _TaxCardNumber, string _TaxFileNumber, string _serial, string _number, string _date, string _store, string _paymethod,
            string _drawer, string _notes, string _total, string _taxR, string _taxV, string _discountR, string _discountV,
            string _expensesR, string _expensesV, string _net, string _paied, string _remains, DataTable dt, string userName,
            string _salesEmp, string _Shipping, string _PurchaseOrderNo, string _DeliverDate, string _salesEmp_Job, string _DeductTaxV,
            string _AddTaxV, int _currId, string _BalanceBefore, string _BalanceAfter, string DriverName, string VehicleNumber,
            string Destination, string ProcessName, string SourceCode, string scalWeightSerial, string _CusR, string _CusV,
            string _CostCenter, string _Retention, string _AdvancePayment, string _dueDate, int CustomerId, decimal[] trans_hand, string _totalPieces, DataTable _dt_Weights, string _Delivery)
        {
            InitializeComponent();
            customer = _customer;
            TaxCardNumber = _TaxCardNumber;
            TaxFileNumber = _TaxFileNumber;
            serial = _serial;
            number = _number;
            date = _date;
            store = _store;
            paymethod = _paymethod;
            drawer = _drawer;
            notes = _notes;
            total = _total;
            taxR = _taxR;
            taxV = _taxV;
            CusTaxV = _CusV;
            discountR = _discountR;
            discountV = _discountV;
            expensesR = _expensesR;
            expensesV = _expensesV;
            net = _net;
            paied = _paied;
            remains = _remains;
            this.userName = userName;
            SalesEmp = _salesEmp;
            salesEmp_Job = _salesEmp_Job;

            dt_Weights = _dt_Weights;

            Shipping = _Shipping;
            PurchaseOrderNo = _PurchaseOrderNo;
            DeliverDate = _DeliverDate;
            dueDate = _dueDate; ;
            DeductTaxV = _DeductTaxV;
            AddTaxV = _AddTaxV;
            CostCenter = _CostCenter;
            retention = _Retention;
            advancepayment = _AdvancePayment;
            this.DriverName = DriverName;
            this.VehicleNumber = VehicleNumber;
            this.Destination = Destination;
            this.ProcessName = ProcessName;
            this.SourceCode = SourceCode;
            this.scalWeightSerial = scalWeightSerial;

            currId = _currId;
            this.CustomerId = CustomerId;

            BalanceBefore = _BalanceBefore;
            BalanceAfter = _BalanceAfter;

            this.trans_hand = trans_hand;
            this.Delivery = _Delivery;
            dt_inv_details = dt;
            //this.DataSource = dt_inv_details;
            totalPieces = _totalPieces;
            getReportHeader();

            //LoadData();            
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;
            if (comp != null)
            {
                lblCompName.Text = Shared.IsEnglish ? comp.CmpNameEn : comp.CmpNameAr;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        public void LoadData()
        {
            lbl_date.Text = date;
            lbl_DiscountR.Text = discountR;
            lbl_DiscountV.Text = discountV;
            lbl_Drawer.Text = drawer;
            lbl_ExpensesR.Text = expensesR;
            lbl_ExpensesV.Text = expensesV;
            lbl_Net.Text = net;
            lbl_notes.Text = notes;
            lbl_Number.Text = number;
            xrBarCode_Voucher.Text = number;
            lbl_Paied.Text = paied;
            lbl_Paymethod.Text = paymethod;
            lbl_Remains.Text = remains;
            lbl_Serial.Text = serial;
            lbl_store.Text = store;
            lbl_TaxR.Text = taxR;
            lbl_TaxV.Text = taxV;
            lbl_CusTaxV.Text = CusTaxV;
            lbl_Total.Text = total;
            lbl_Customer.Text = customer;
            lbl_TaxCardNumber.Text = TaxCardNumber;
            lbl_TaxFileNumber.Text = TaxFileNumber;
            lbl_User.Text = userName;
            lbl_DeductTaxV.Text = DeductTaxV;
            lbl_AddTaxV.Text = AddTaxV;

            lbl_SalesEmp.Text = SalesEmp;
            lbl_salesEmp_Job.Text = salesEmp_Job;

            txtDelivery.Text = Delivery;
            lbl_DeliverDate.Text = DeliverDate;
            lbl_Shipping.Text = Shipping;
            lbl_PurchaseOrderNo.Text = PurchaseOrderNo;
            lbl_BalanceBefore.Text = BalanceBefore;
            lbl_BalanceAfter.Text = BalanceAfter;

            lbl_DriverName.Text = DriverName;
            lbl_VehicleNumber.Text = VehicleNumber;
            lbl_Destination.Text = Destination;
            lbl_ScaleWeightSerial.Text = scalWeightSerial;

            lbl_ProcessName.Text = ProcessName;
            lbl_SourceCode.Text = SourceCode;
            txt_advancepayment.Text = advancepayment;
            txt_retention.Text = retention;

            if (trans_hand.Count() > 0)
                lbl_trans.Text = trans_hand[0].ToString();
            if (trans_hand.Count() > 1)
                lbl_Handing.Text = trans_hand[1].ToString();

            if (trans_hand.Count() > 2)
                lblShift.Text = trans_hand[2].ToString();

            // Adel DueDate 18/03/2020
            lbl_DueDate.Text = dueDate;
            try
            {
                lblSubTotal.Text = (Convert.ToDecimal(lbl_Total.Text) - Convert.ToDecimal(lbl_DiscountV.Text)).ToString();
            }
            catch { }
            lblTotalWords.Text = Shared.IsEnglish ? HelperAcc.ConvertMoneyToText(net, currId, Shared.lstCurrency) :
                                   HelperAcc.ConvertMoneyToArabicText(net, currId, Shared.lstCurrency);

            DetailReport.DataSource = dt_inv_details;

            ERPDataContext db = new ERPDataContext();
            var _customer = db.SL_Customers.Where(x => x.CustomerId == CustomerId).FirstOrDefault();
            if (_customer != null)
            {
                lbl_Cust_Address.Text = _customer.Address;
                lbl_Cust_Tel.Text = _customer.Tel;
                lbl_Cust_Mobile.Text = _customer.Mobile;
            }

            string updated = db.SL_Invoices.FirstOrDefault(s => s.InvoiceCode == number).LastUpdateDate != null ? "معدل" : "";
            lbl_Updated.Text = updated;

            decimal totalQty = 0;
            decimal totalP = 0;
            //totalQty = dt_inv_details.Compute("Sum(Qty)", string.Empty);
            foreach (DataRow row in dt_inv_details.Rows)
            {
                totalQty += Convert.ToDecimal(row["Qty"]);
                try
                {
                    if (Convert.ToBoolean(row["IsOffer"]) == true)
                        continue;
                }
                catch { }
                totalP += Convert.ToDecimal(row["PiecesCount"]);
            }
            lbl_TotalQty.Text = totalQty.ToString();
            lbl_totalPieces.Text = totalP.ToString();
            cell_code.DataBindings.Add("Text", DetailReport.DataSource, "ItemCode1");
            cell_code2.DataBindings.Add("Text", DetailReport.DataSource, "ItemCode2");
            cell_Disc.DataBindings.Add("Text", DetailReport.DataSource, "DiscountValue");
            cell_Expire.DataBindings.Add("Text", DetailReport.DataSource, "Expire");
            cell_Batch.DataBindings.Add("Text", DetailReport.DataSource, "Batch");
            cell_Price.DataBindings.Add("Text", DetailReport.DataSource, "SellPrice");
            cell_Qty.DataBindings.Add("Text", DetailReport.DataSource, "Qty");
            cell_Total.DataBindings.Add("Text", DetailReport.DataSource, "TotalSellPrice");
            cell_ItemName.DataBindings.Add("Text", DetailReport.DataSource, "ItemName");
            cell_UOM.DataBindings.Add("Text", DetailReport.DataSource, "UOM");

            cell_Height.DataBindings.Add("Text", DetailReport.DataSource, "Height");
            cell_Width.DataBindings.Add("Text", DetailReport.DataSource, "Width");
            cell_Length.DataBindings.Add("Text", DetailReport.DataSource, "Length");
            cell_TotalQty.DataBindings.Add("Text", DetailReport.DataSource, "TotalQty");
            cell_ItemDescription.DataBindings.Add("Text", DetailReport.DataSource, "ItemDescription");
            cell_AudiencePrice.DataBindings.Add("Text", DetailReport.DataSource, "AudiencePrice");
            cell_Factor.DataBindings.Add("Text", DetailReport.DataSource, "Factor");
            Cell_MUOM.DataBindings.Add("Text", DetailReport.DataSource, "MUOM");
            Cell_MUOM_Factor.DataBindings.Add("Text", DetailReport.DataSource, "MUOM_Factor");
            cell_DiscountRatio.DataBindings.Add("Text", DetailReport.DataSource, "DiscountRatio");
            cell_DiscountRatio2.DataBindings.Add("Text", DetailReport.DataSource, "DiscountRatio2");
            cell_DiscountRatio3.DataBindings.Add("Text", DetailReport.DataSource, "DiscountRatio3");
            cell_SalesTaxRatio.DataBindings.Add("Text", DetailReport.DataSource, "SalesTaxRatio");
            cell_SalesTax.DataBindings.Add("Text", DetailReport.DataSource, "SalesTax");
            //cell_Serial.DataBindings.Add("Text", DetailReport.DataSource, "Serial");
            cell_ManufactureDate.DataBindings.Add("Text", DetailReport.DataSource, "ManufactureDate");
            cell_PiecesCount.DataBindings.Add("Text", DetailReport.DataSource, "PiecesCount");
            cell_Weight_KG.DataBindings.Add("Text", DetailReport.DataSource, "kg_Weight_libra");
            cell_Serial.DataBindings.Add("Text", DetailReport.DataSource, "Index");
            cell_Location.DataBindings.Add("Text", DetailReport.DataSource, "Location");
            cell_Pack.DataBindings.Add("Text", DetailReport.DataSource, "Pack");
            cell_ItemDescriptionEn.DataBindings.Add("Text", DetailReport.DataSource, "ItemDescriptionEn");

            var comp = Shared.st_comp;
            if (comp != null)
            {
                lblCompName.Text = Shared.IsEnglish ? comp.CmpNameEn : comp.CmpNameAr;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
            //this.pic_ItemPic.DataBindings.AddRange(new DevExpress.XtraReports.UI.XRBinding[] {
            //new DevExpress.XtraReports.UI.XRBinding("ImageUrl", this.DataSource, "PicPath")});

            if (dt_Weights.Rows.Count > 0)
            {
                xrSubreport2.ReportSource = new rpt_multiple_weights(dt_Weights);
                if (xrSubreport3.Visible == true)
                {
                    var dt = dt_Weights.AsEnumerable().GroupBy(r => new { Col1 = r["item"] }).Select(g => g.First()).CopyToDataTable();
                    xrSubreport3.ReportSource = new rpt_multiple_weights_dup(dt_Weights);
                }
            }


            #region Print sum of Qty according to Category
            var categories = db.IC_Categories;

            var sums = from DataRow s in dt_inv_details.Rows
                       group s by s["CategoryId"] into grp
                       select new
                       {
                           Category = categories.Where(x => x.CategoryId == Convert.ToInt32(grp.Key)).
                           Select(x => Shared.IsEnglish ? x.CategoryNameEn : x.CategoryNameAr).First(),
                           Qtys = grp.Select(x => Convert.ToDouble(x["Qty"])).Sum(),
                       };

            DetailReport_TotalQtyPerCategory.DataSource = sums;

            //grd.DataSource = sums;

            cell_Qtys.DataBindings.Add("Text", DetailReport_TotalQtyPerCategory.DataSource, "Qtys");
            cell_Category.DataBindings.Add("Text", DetailReport_TotalQtyPerCategory.DataSource, "Category");
            #endregion
        }
        
    }
}
