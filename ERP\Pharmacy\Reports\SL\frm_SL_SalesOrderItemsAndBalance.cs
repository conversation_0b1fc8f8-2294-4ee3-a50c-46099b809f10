﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using System.Linq;
using DAL;
using DAL.Res;

using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraNavBar;
using Reports;
using DevExpress.XtraPrinting;

using DevExpress.XtraReports.UI;

namespace Reports
{
    public partial class frm_SL_SalesOrderItemsAndBalance : DevExpress.XtraEditors.XtraForm
    {
        public bool UserCanOpen;
        string reportName, dateFilter, otherFilters;

        int itemId1, itemId2, store_id1, store_id2;

        byte FltrTyp_Store, FltrTyp_item, fltrTyp_Date;
        DateTime date1, date2;
        List<int> lstStores = new List<int>();

        public frm_SL_SalesOrderItemsAndBalance(
            string reportName, string dateFilter, string otherFilters,
            byte fltrTyp_item, int itemId1, int itemId2,
            byte fltrTyp_Date, DateTime date1, DateTime date2,
            byte FltrTyp_Store, int store_id1, int store_id2)
        {

            ReportsRTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            ReportsRTL.RTL_BarManager(this.barManager1);

            this.reportName = reportName;
            this.dateFilter = dateFilter;
            this.otherFilters = otherFilters;

            this.FltrTyp_item = fltrTyp_item;
            this.fltrTyp_Date = fltrTyp_Date;
            this.itemId1 = itemId1;
            this.itemId2 = itemId2;
            this.date1 = date1;
            this.date2 = date2;


            this.FltrTyp_Store = FltrTyp_Store;
            this.store_id1 = store_id1;
            this.store_id2 = store_id2;

            getReportHeader();

            LoadData();
            ReportsUtils.ColumnChooser(grdCategory);
        }


        private void frm_SL_InvoiceList_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                ReportsRTL.LTRLayout(this);

            ReportsUtils.Load_Grid_Layout(grdCategory, this.Name.Replace("frm_", ""));
        }

        private void frm_Rep_FormClosing(object sender, FormClosingEventArgs e)
        {
            ReportsUtils.save_Grid_Layout(grdCategory, this.Name.Replace("frm_", ""), true);
        }

        private void barBtn_Close_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtn_Preview_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            grdCategory.MinimumSize = grdCategory.Size;
            new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                    lblFilter.Text.Trim(), "", grdCategory, true).ShowPreview();
            grdCategory.MinimumSize = new Size(0, 0);
        }

        private void barBtnRefresh_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            LoadData();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                grdCategory.MinimumSize = grdCategory.Size;
                new Reports.rpt_Template(lblReportName.Text.Trim(), lblDateFilter.Text.Trim(),
                        lblFilter.Text.Trim(), "", grdCategory, true, true).ShowPreview();
                grdCategory.MinimumSize = new Size(0, 0);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        void getReportHeader()
        {
            var comp = Shared.st_comp;

            if (comp != null)
            {
                //lblCompName.Text = comp.CmpNameAr;
                lblReportName.Text = this.Text = reportName;
                if (comp.Logo != null)
                    picLogo.Image = ReportsUtils.byteArrayToImage(comp.Logo.ToArray());
            }
        }

        void LoadData()
        {
            lblDateFilter.Text = dateFilter;
            lblFilter.Text = otherFilters;

            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            var stores = DB.IC_Stores.ToList();

            foreach (var store in stores)
            {
                if (FltrTyp_Store == 2)
                {
                    if (store.StoreId <= store_id2 && store.StoreId >= store_id1)
                    {
                        lstStores.Add(store.StoreId);
                    }
                }
                else if (FltrTyp_Store == 0)
                {
                    lstStores.Add(store.StoreId);
                }
                else if (store_id1 > 0 && (store.StoreId == store_id1 || store.ParentId == store_id1))
                    lstStores.Add(store.StoreId);
                //else if (store_id2 > 0 && (store.StoreId == store_id2 || store.ParentId == store_id2))
                //    lstStores.Add(store.StoreId);
            }
            var defaultCategories = DB.IC_User_Categories.Where(a => a.UserId == Shared.UserId).Select(a => a.CategoryId).ToList();
            var data_sl = (from p in DB.SL_SalesOrders.Where(p => !p.Is_OutTrns.HasValue || p.Is_OutTrns != true)
                           where lstStores.Count > 0 ? lstStores.Contains(p.StoreId) : true

                           join pd in DB.SL_SalesOrderDetails on p.SL_SalesOrderId equals pd.SL_SalesOrderId

                           where fltrTyp_Date == 1 ? p.SalesOrderDate.Date == date1 : true
                           where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 != Shared.minDate) ?
                           p.SalesOrderDate >= date1 && p.SalesOrderDate <= date2 : true
                           where (fltrTyp_Date == 2 && date1 != Shared.minDate && date2 == Shared.minDate) ?
                           p.SalesOrderDate >= date1 : true
                           where (fltrTyp_Date == 2 && date1 == Shared.minDate && date2 != Shared.minDate) ?
                           p.SalesOrderDate <= date2 : true

                           where FltrTyp_item == 1 ? pd.ItemId == itemId1 : true
                           where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 != 0) ?
                           pd.ItemId >= itemId1 && pd.ItemId <= itemId2 : true
                           where (FltrTyp_item == 2 && itemId1 != 0 && itemId2 == 0) ?
                           pd.ItemId >= itemId1 : true
                           where (FltrTyp_item == 2 && itemId1 == 0 && itemId2 != 0) ?
                           pd.ItemId <= itemId2 : true

                           group pd by pd.ItemId into grp
                           join t in DB.IC_Items on grp.Key equals t.ItemId
                           join g in DB.IC_Categories on t.Category equals g.CategoryId

                           where defaultCategories.Count() > 0 ? defaultCategories.Contains(g.CategoryId) : true
                           let SalesOrderQty = (from x in grp
                                                let factor = x.UOMIndex == 0 ? 1 : x.UOMIndex == 1 ? t.MediumUOMFactorDecimal : t.LargeUOMFactorDecimal
                                                select x.Qty * factor).Select(d => d == null ? 0 : d).Sum()

                           let CurrentQty = (from d in DB.IC_ItemStores
                                             where d.ItemId == grp.Key
                                             && d.ProcessId != (int)Process.SalesOrder
                                             let factor = (decimal?)1
                                             select d.IsInTrns ? d.Qty * factor : d.Qty * factor * -1).Select(d => d == null ? 0 : d).Sum()
                           let PurchasesOrderQty = (from d in DB.PR_PurchaseOrderDetails
                                                    where d.PR_PurchaseOrder.Is_InTrns.HasValue == false || d.PR_PurchaseOrder.Is_InTrns != true
                                                    where d.ItemId == grp.Key
                                                    let factor = d.UOMIndex == 0 ? 1 : d.UOMIndex == 1 ? t.MediumUOMFactorDecimal : t.LargeUOMFactorDecimal
                                                    select d.Qty * factor).Select(d => d == null ? 0 : d).Sum()

                           select new
                           {
                               ItemCode1 = t.ItemCode1,
                               ItemCode2 = t.ItemCode2,
                               ItemNameAr = t.ItemNameAr,
                               SalesOrderQty,
                               CurrentQty,
                               Shortage = CurrentQty - SalesOrderQty,
                               PurchasesOrderQty,

                               TotalQty = CurrentQty + PurchasesOrderQty,
                               TotalShortage = CurrentQty + PurchasesOrderQty - SalesOrderQty
                           }).ToList();

            grdCategory.DataSource = data_sl;
        }

        private void gridView1_CustomUnboundColumnData(object sender, DevExpress.XtraGrid.Views.Base.CustomColumnDataEventArgs e)
        {
            if (e.ListSourceRowIndex < 0)
                return;
            if (e.Column.FieldName == "colIndex")
                e.Value = e.RowHandle() + 1;

        }

        public bool LoadPrivilege()
        {
            if (Shared.LstUserPrvlg != null)
            {
                UserPriv p = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.rpt_frm_SL_SalesOrderItemsAndBalance).FirstOrDefault();
                if (p == null)
                {
                    XtraMessageBox.Show(Shared.IsEnglish ? ResRptEn.MsgPrv : ResRptAr.MsgPrv, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            return true;
        }

    }
}