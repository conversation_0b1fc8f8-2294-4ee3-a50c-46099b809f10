﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="labelControl15.Location" type="System.Drawing.Point, System.Drawing">
    <value>558, 120</value>
  </data>
  <data name="labelControl15.Size" type="System.Drawing.Size, System.Drawing">
    <value>29, 13</value>
  </data>
  <data name="labelControl15.Text" xml:space="preserve">
    <value>تليفون</value>
  </data>
  <data name="txtTel.Location" type="System.Drawing.Point, System.Drawing">
    <value>362, 117</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="txtTel.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtTel.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="txtTel.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <assembly alias="DevExpress.XtraEditors.v15.1" name="DevExpress.XtraEditors.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="txtTel.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtTel.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTel.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txtTel.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtTel.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtTel.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtTel.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtTel.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtTel.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTel.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txtTel.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtTel.Size" type="System.Drawing.Size, System.Drawing">
    <value>182, 20</value>
  </data>
  <data name="barBtnHelp.Caption" xml:space="preserve">
    <value>مساعدة</value>
  </data>
  <data name="barBtnNew.Caption" xml:space="preserve">
    <value>جديد</value>
  </data>
  <data name="barBtnDelete.Caption" xml:space="preserve">
    <value>حذف</value>
  </data>
  <data name="barBtnSave.Caption" xml:space="preserve">
    <value>حفظ</value>
  </data>
  <data name="barBtnList.Caption" xml:space="preserve">
    <value>القائمة</value>
  </data>
  <data name="barBtnClose.Caption" xml:space="preserve">
    <value>غلق</value>
  </data>
  <data name="barDockControlTop.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlTop.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barDockControlTop.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barDockControlTop.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlTop.Size" type="System.Drawing.Size, System.Drawing">
    <value>665, 31</value>
  </data>
  <data name="barDockControlBottom.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlBottom.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barDockControlBottom.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barDockControlBottom.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlBottom.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 540</value>
  </data>
  <data name="barDockControlBottom.Size" type="System.Drawing.Size, System.Drawing">
    <value>665, 0</value>
  </data>
  <data name="barDockControlLeft.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlLeft.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barDockControlLeft.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barDockControlLeft.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlLeft.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 31</value>
  </data>
  <data name="barDockControlLeft.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 509</value>
  </data>
  <data name="barDockControlRight.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="barDockControlRight.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="barDockControlRight.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="barDockControlRight.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="barDockControlRight.Location" type="System.Drawing.Point, System.Drawing">
    <value>665, 31</value>
  </data>
  <data name="barDockControlRight.Size" type="System.Drawing.Size, System.Drawing">
    <value>0, 509</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>665, 540</value>
  </data>
  <data name="IsStopped.Location" type="System.Drawing.Point, System.Drawing">
    <value>192, 37</value>
  </data>
  <data name="IsStopped.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="IsStopped.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="IsStopped.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="IsStopped.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="IsStopped.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="IsStopped.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="IsStopped.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="IsStopped.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="IsStopped.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="IsStopped.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="Mobile.Location" type="System.Drawing.Point, System.Drawing">
    <value>47, 117</value>
  </data>
  <data name="Mobile.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="Mobile.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="Mobile.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="Mobile.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="Mobile.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="Mobile.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="Mobile.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="Mobile.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="Mobile.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="Mobile.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="Mobile.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="Mobile.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="Mobile.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="Mobile.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="Mobile.Size" type="System.Drawing.Size, System.Drawing">
    <value>191, 20</value>
  </data>
  <data name="labelControl10.Location" type="System.Drawing.Point, System.Drawing">
    <value>258, 113</value>
  </data>
  <data name="labelControl10.Size" type="System.Drawing.Size, System.Drawing">
    <value>28, 13</value>
  </data>
  <data name="labelControl10.Text" xml:space="preserve">
    <value>الهاتف</value>
  </data>
  <data name="lkpPriceLevel.Location" type="System.Drawing.Point, System.Drawing">
    <value>47, 232</value>
  </data>
  <data name="lkpPriceLevel.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpPriceLevel.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpPriceLevel.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpPriceLevel.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpPriceLevel.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpPriceLevel.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpPriceLevel.Properties.AppearanceDropDown.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpPriceLevel.Properties.AppearanceDropDown.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpPriceLevel.Properties.AppearanceDropDown.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpPriceLevel.Properties.AppearanceDropDown.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpPriceLevel.Properties.AppearanceDropDownHeader.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpPriceLevel.Properties.AppearanceDropDownHeader.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpPriceLevel.Properties.AppearanceDropDownHeader.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpPriceLevel.Properties.AppearanceDropDownHeader.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpPriceLevel.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpPriceLevel.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkpPriceLevel.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpPriceLevel.Size" type="System.Drawing.Size, System.Drawing">
    <value>222, 20</value>
  </data>
  <data name="lbl_PriceList.Location" type="System.Drawing.Point, System.Drawing">
    <value>275, 235</value>
  </data>
  <data name="lbl_PriceList.Size" type="System.Drawing.Size, System.Drawing">
    <value>61, 13</value>
  </data>
  <data name="lbl_PriceList.Text" xml:space="preserve">
    <value>قائمة الأسعار</value>
  </data>
  <data name="xtraTabControl1.AppearancePage.Header.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="xtraTabControl1.AppearancePage.Header.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="xtraTabControl1.AppearancePage.Header.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="xtraTabControl1.AppearancePage.Header.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="xtraTabControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>25, 273</value>
  </data>
  <data name="chk_autoCreateAccs.Location" type="System.Drawing.Point, System.Drawing">
    <value>192, 8</value>
  </data>
  <data name="chk_autoCreateAccs.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_autoCreateAccs.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_autoCreateAccs.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="chk_autoCreateAccs.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="chk_autoCreateAccs.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="chk_autoCreateAccs.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="chk_autoCreateAccs.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="chk_autoCreateAccs.Properties.Caption" xml:space="preserve">
    <value>إنشاء الحسابات بشكل تلقائي</value>
  </data>
  <data name="chk_autoCreateAccs.Properties.DisplayValueChecked" xml:space="preserve">
    <value />
  </data>
  <data name="chk_autoCreateAccs.Properties.DisplayValueGrayed" xml:space="preserve">
    <value />
  </data>
  <data name="chk_autoCreateAccs.Properties.DisplayValueUnchecked" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>28, 158</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicCloseInvAcc.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblPurchasesReturnAccount.Location" type="System.Drawing.Point, System.Drawing">
    <value>457, 111</value>
  </data>
  <data name="lblPurchasesReturnAccount.Size" type="System.Drawing.Size, System.Drawing">
    <value>92, 13</value>
  </data>
  <data name="lblPurchasesReturnAccount.Text" xml:space="preserve">
    <value>ح/ مردود المشتريات</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>28, 108</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicPrReturnAcc.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblCloseInventoryAccount.Location" type="System.Drawing.Point, System.Drawing">
    <value>457, 161</value>
  </data>
  <data name="lblCloseInventoryAccount.Size" type="System.Drawing.Size, System.Drawing">
    <value>93, 13</value>
  </data>
  <data name="lblCloseInventoryAccount.Text" xml:space="preserve">
    <value>ح/ بضاعة نهاية المدة</value>
  </data>
  <data name="lblPurchasesAccount.Location" type="System.Drawing.Point, System.Drawing">
    <value>457, 86</value>
  </data>
  <data name="lblPurchasesAccount.Size" type="System.Drawing.Size, System.Drawing">
    <value>63, 13</value>
  </data>
  <data name="lblPurchasesAccount.Text" xml:space="preserve">
    <value>ح/ المشتريات</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>28, 33</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicSalesAcc.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>28, 83</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicPurchaseAcc.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblSalesAccount.Location" type="System.Drawing.Point, System.Drawing">
    <value>457, 36</value>
  </data>
  <data name="lblSalesAccount.Size" type="System.Drawing.Size, System.Drawing">
    <value>50, 13</value>
  </data>
  <data name="lblSalesAccount.Text" xml:space="preserve">
    <value>ح/المبيعات</value>
  </data>
  <data name="labelControl12.Location" type="System.Drawing.Point, System.Drawing">
    <value>457, 186</value>
  </data>
  <data name="labelControl12.Size" type="System.Drawing.Size, System.Drawing">
    <value>92, 13</value>
  </data>
  <data name="labelControl12.Text" xml:space="preserve">
    <value>ح/ الخصم المكتسب</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>28, 58</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicSalesReturnAcc.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>28, 183</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicPrDiscAcc.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblSalesReturnAccount.Location" type="System.Drawing.Point, System.Drawing">
    <value>457, 61</value>
  </data>
  <data name="lblSalesReturnAccount.Size" type="System.Drawing.Size, System.Drawing">
    <value>82, 13</value>
  </data>
  <data name="lblSalesReturnAccount.Text" xml:space="preserve">
    <value>ح/ مردود المبيعات</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>28, 208</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicSalesDiscAcc.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="labelControl14.Location" type="System.Drawing.Point, System.Drawing">
    <value>457, 211</value>
  </data>
  <data name="labelControl14.Size" type="System.Drawing.Size, System.Drawing">
    <value>103, 13</value>
  </data>
  <data name="labelControl14.Text" xml:space="preserve">
    <value>ح/ الخصم المسموح به</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>28, 133</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PeriodicOpenInvAcc.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblOpenInventoryAccount.Location" type="System.Drawing.Point, System.Drawing">
    <value>457, 136</value>
  </data>
  <data name="lblOpenInventoryAccount.Size" type="System.Drawing.Size, System.Drawing">
    <value>87, 13</value>
  </data>
  <data name="lblOpenInventoryAccount.Text" xml:space="preserve">
    <value>ح/ بضاعة أول المدة</value>
  </data>
  <data name="tab_PeriodicAcc.Size" type="System.Drawing.Size, System.Drawing">
    <value>606, 236</value>
  </data>
  <data name="tab_PeriodicAcc.Text" xml:space="preserve">
    <value>الحسابات</value>
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="xtraTabControl1.ShowTabHeader" type="DevExpress.Utils.DefaultBoolean, DevExpress.Data.v15.1">
    <value>True</value>
  </data>
  <data name="xtraTabControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>612, 264</value>
  </data>
  <data name="labelControl8.Location" type="System.Drawing.Point, System.Drawing">
    <value>457, 69</value>
  </data>
  <data name="labelControl8.Size" type="System.Drawing.Size, System.Drawing">
    <value>110, 13</value>
  </data>
  <data name="labelControl8.Text" xml:space="preserve">
    <value>ح/تكلفة البضاعة المباعة</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>28, 16</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PerPetualSalesAcc.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Location" type="System.Drawing.Point, System.Drawing">
    <value>28, 66</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PerPetualCostOfSoldGoods.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblSales.Location" type="System.Drawing.Point, System.Drawing">
    <value>457, 19</value>
  </data>
  <data name="lblSales.Size" type="System.Drawing.Size, System.Drawing">
    <value>50, 13</value>
  </data>
  <data name="lblSales.Text" xml:space="preserve">
    <value>ح/المبيعات</value>
  </data>
  <data name="lblPurchaseDiscount.Location" type="System.Drawing.Point, System.Drawing">
    <value>457, 119</value>
  </data>
  <data name="lblPurchaseDiscount.Size" type="System.Drawing.Size, System.Drawing">
    <value>76, 13</value>
  </data>
  <data name="lblPurchaseDiscount.Text" xml:space="preserve">
    <value>ح/خصم مكتسب</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>28, 41</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PerPetualSalesReturnAcc.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>28, 116</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PerPetualPurchaseDiscountAcc.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblSalesReturn.Location" type="System.Drawing.Point, System.Drawing">
    <value>457, 44</value>
  </data>
  <data name="lblSalesReturn.Size" type="System.Drawing.Size, System.Drawing">
    <value>79, 13</value>
  </data>
  <data name="lblSalesReturn.Text" xml:space="preserve">
    <value>ح/مردود المبيعات</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Location" type="System.Drawing.Point, System.Drawing">
    <value>28, 141</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PerPetualSalesDiscount.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Location" type="System.Drawing.Point, System.Drawing">
    <value>28, 91</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.Columns15" xml:space="preserve">
    <value>رقم الحساب</value>
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkp_PerPetualInventoryAcc.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lblSalesDiscount.Location" type="System.Drawing.Point, System.Drawing">
    <value>457, 144</value>
  </data>
  <data name="lblSalesDiscount.Size" type="System.Drawing.Size, System.Drawing">
    <value>87, 13</value>
  </data>
  <data name="lblSalesDiscount.Text" xml:space="preserve">
    <value>ح/خصم مسموح به</value>
  </data>
  <data name="lblInventory.Location" type="System.Drawing.Point, System.Drawing">
    <value>457, 94</value>
  </data>
  <data name="lblInventory.Size" type="System.Drawing.Size, System.Drawing">
    <value>48, 13</value>
  </data>
  <data name="lblInventory.Text" xml:space="preserve">
    <value>ح/المخزون</value>
  </data>
  <data name="tab_PerpetualAcc.Size" type="System.Drawing.Size, System.Drawing">
    <value>606, 236</value>
  </data>
  <data name="tab_PerpetualAcc.Text" xml:space="preserve">
    <value>الحسابات</value>
  </data>
  <data name="lkpStore.Location" type="System.Drawing.Point, System.Drawing">
    <value>47, 202</value>
  </data>
  <data name="lkpStore.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpStore.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpStore.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="lkpStore.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="lkpStore.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="lkpStore.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="lkpStore.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="lkpStore.Properties.Columns1" xml:space="preserve">
    <value>اسم المخزن</value>
  </data>
  <data name="lkpStore.Properties.Columns8" xml:space="preserve">
    <value>كود المخزن</value>
  </data>
  <data name="lkpStore.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="lkpStore.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lkpStore.Size" type="System.Drawing.Size, System.Drawing">
    <value>497, 20</value>
  </data>
  <data name="labelControl7.Location" type="System.Drawing.Point, System.Drawing">
    <value>555, 205</value>
  </data>
  <data name="labelControl7.Size" type="System.Drawing.Size, System.Drawing">
    <value>17, 13</value>
  </data>
  <data name="labelControl7.Text" xml:space="preserve">
    <value>يتبع</value>
  </data>
  <data name="labelControl5.Location" type="System.Drawing.Point, System.Drawing">
    <value>555, 176</value>
  </data>
  <data name="labelControl5.Size" type="System.Drawing.Size, System.Drawing">
    <value>52, 13</value>
  </data>
  <data name="labelControl5.Text" xml:space="preserve">
    <value>اسم المدير</value>
  </data>
  <data name="txt_Manager.Location" type="System.Drawing.Point, System.Drawing">
    <value>47, 173</value>
  </data>
  <data name="txt_Manager.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Manager.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_Manager.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Manager.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_Manager.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Manager.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txt_Manager.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Manager.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_Manager.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_Manager.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Manager.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_Manager.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Manager.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_Manager.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_Manager.Size" type="System.Drawing.Size, System.Drawing">
    <value>497, 20</value>
  </data>
  <data name="cb_CostMethod.Location" type="System.Drawing.Point, System.Drawing">
    <value>365, 228</value>
  </data>
  <data name="cb_CostMethod.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cb_CostMethod.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cb_CostMethod.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="cb_CostMethod.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="cb_CostMethod.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="cb_CostMethod.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="cb_CostMethod.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="cb_CostMethod.Properties.GlyphAlignment" type="DevExpress.Utils.HorzAlignment, DevExpress.Data.v15.1">
    <value>Near</value>
  </data>
  <data name="cb_CostMethod.Properties.Items" xml:space="preserve">
    <value>الداخل اولا يخرج اولا</value>
  </data>
  <data name="cb_CostMethod.Properties.Items3" xml:space="preserve">
    <value>الداخل اخرا يخرج أولا</value>
  </data>
  <data name="cb_CostMethod.Properties.Items6" xml:space="preserve">
    <value>المتوسط المرجح</value>
  </data>
  <data name="cb_CostMethod.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="cb_CostMethod.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cb_CostMethod.Size" type="System.Drawing.Size, System.Drawing">
    <value>179, 20</value>
  </data>
  <data name="labelControl1.Location" type="System.Drawing.Point, System.Drawing">
    <value>559, 40</value>
  </data>
  <data name="labelControl1.Size" type="System.Drawing.Size, System.Drawing">
    <value>78, 13</value>
  </data>
  <data name="labelControl1.Text" xml:space="preserve">
    <value>كود الفرع/المخزن</value>
  </data>
  <data name="txt_StoreCode.Location" type="System.Drawing.Point, System.Drawing">
    <value>385, 40</value>
  </data>
  <data name="txt_StoreCode.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_StoreCode.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_StoreCode.Properties.Appearance.FontSizeDelta" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="txt_StoreCode.Properties.Appearance.FontStyleDelta" type="System.Drawing.FontStyle, System.Drawing">
    <value>Regular</value>
  </data>
  <data name="txt_StoreCode.Properties.Appearance.GradientMode" type="System.Drawing.Drawing2D.LinearGradientMode, System.Drawing">
    <value>Horizontal</value>
  </data>
  <data name="txt_StoreCode.Properties.Appearance.Image" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txt_StoreCode.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_StoreCode.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txt_StoreCode.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_StoreCode.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txt_StoreCode.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_StoreCode.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txt_StoreCode.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txt_StoreCode.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_StoreCode.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txt_StoreCode.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_StoreCode.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txt_StoreCode.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txt_StoreCode.Size" type="System.Drawing.Size, System.Drawing">
    <value>159, 20</value>
  </data>
  <data name="btnNext.Location" type="System.Drawing.Point, System.Drawing">
    <value>67, 32</value>
  </data>
  <data name="btnNext.ToolTip" xml:space="preserve">
    <value>التالي</value>
  </data>
  <data name="btnPrev.Location" type="System.Drawing.Point, System.Drawing">
    <value>35, 32</value>
  </data>
  <data name="btnPrev.ToolTip" xml:space="preserve">
    <value>السابق</value>
  </data>
  <data name="labelControl3.Location" type="System.Drawing.Point, System.Drawing">
    <value>555, 95</value>
  </data>
  <data name="labelControl3.Size" type="System.Drawing.Size, System.Drawing">
    <value>98, 13</value>
  </data>
  <data name="labelControl3.Text" xml:space="preserve">
    <value>اسم  الفرع/المخزن ج</value>
  </data>
  <data name="txtStoreNameEn.Location" type="System.Drawing.Point, System.Drawing">
    <value>47, 92</value>
  </data>
  <data name="txtStoreNameEn.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtStoreNameEn.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtStoreNameEn.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtStoreNameEn.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtStoreNameEn.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtStoreNameEn.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txtStoreNameEn.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtStoreNameEn.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtStoreNameEn.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtStoreNameEn.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtStoreNameEn.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtStoreNameEn.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtStoreNameEn.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txtStoreNameEn.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtStoreNameEn.Size" type="System.Drawing.Size, System.Drawing">
    <value>497, 20</value>
  </data>
  <data name="labelControl4.Location" type="System.Drawing.Point, System.Drawing">
    <value>555, 69</value>
  </data>
  <data name="labelControl4.Size" type="System.Drawing.Size, System.Drawing">
    <value>88, 13</value>
  </data>
  <data name="labelControl4.Text" xml:space="preserve">
    <value>اسم  الفرع/المخزن</value>
  </data>
  <data name="txtStoreNameAr.Location" type="System.Drawing.Point, System.Drawing">
    <value>47, 66</value>
  </data>
  <data name="txtStoreNameAr.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtStoreNameAr.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtStoreNameAr.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtStoreNameAr.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtStoreNameAr.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtStoreNameAr.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txtStoreNameAr.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtStoreNameAr.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtStoreNameAr.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtStoreNameAr.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtStoreNameAr.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtStoreNameAr.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtStoreNameAr.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txtStoreNameAr.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtStoreNameAr.Size" type="System.Drawing.Size, System.Drawing">
    <value>497, 20</value>
  </data>
  <data name="labelControl9.Location" type="System.Drawing.Point, System.Drawing">
    <value>550, 231</value>
  </data>
  <data name="labelControl9.Size" type="System.Drawing.Size, System.Drawing">
    <value>71, 13</value>
  </data>
  <data name="labelControl9.Text" xml:space="preserve">
    <value>حساب المخزون</value>
  </data>
  <data name="labelControl2.Location" type="System.Drawing.Point, System.Drawing">
    <value>558, 146</value>
  </data>
  <data name="labelControl2.Size" type="System.Drawing.Size, System.Drawing">
    <value>31, 13</value>
  </data>
  <data name="labelControl2.Text" xml:space="preserve">
    <value>العنوان</value>
  </data>
  <data name="txtAddress.Location" type="System.Drawing.Point, System.Drawing">
    <value>47, 143</value>
  </data>
  <data name="txtAddress.Properties.AccessibleDescription" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtAddress.Properties.AccessibleName" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="txtAddress.Properties.AutoHeight" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtAddress.Properties.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="txtAddress.Properties.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtAddress.Properties.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="txtAddress.Properties.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtAddress.Properties.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="txtAddress.Properties.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="txtAddress.Properties.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtAddress.Properties.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="txtAddress.Properties.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtAddress.Properties.NullValuePrompt" xml:space="preserve">
    <value />
  </data>
  <data name="txtAddress.Properties.NullValuePromptShowForEmptyValue" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="txtAddress.Size" type="System.Drawing.Size, System.Drawing">
    <value>497, 20</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAICAAAAEAIACoEAAAFgAAACgAAAAgAAAAQAAAAAEAIAAAAAAAABAAAAAAAAAAAAAAAAAAAAAA
        AAAfbf8AH2z/AB9r/x8fa/+rH2v/9h9r/9AfbP9HH2//AB9u/wAfcP8AAAAAAB9p/wAfZv8AH2D/Ah9j
        /2cfY//iH2P/7R9j/4YfY/8KH2P/AB9j/wAAAAAADw4QAA4NDwAODQ4ADQwORwwMDdAMCw32CwsMqwsL
        DB8MCw0ADAsNAB9u/wAfbf8WH23/qx9t//8fbv//H27//x9u/9gfb/89H3D/AB9y/wAfcf8AH23/AB9n
        /wAfZ/9cH2X/6x9j//8fY///H2P/9x9j/38fY/8GH2P/AB5e8QAPDhAADw4QAA4NDz0NDQ/YDQ0O/w0M
        Dv8MDA3/DAsNqwwLDRYMCw0AH2//FR9v/6Ifb///H3D//x9x//8fcf//H3H//x9x/9Mfcv82IHD/AiBw
        /wQfcP8DH2z/VB9q/+gfZ///H2X//x9j//8fY///H2P/9x9j/3cfY/8FH2P+ABAPEQAQDxE1Dw4Q0w8O
        EP8ODg//Dg0P/w0NDv8MDA3/DAsNogwLDRUfcP+MH3H//R9y//8ec///HnP//x50//8edP//HnP//x5z
        /9cec/+pH3L/qR9x/6wfbv/mH23//x9q//8faP//H2X//x9j//8fY///H2P/8h9j/04bTb8AERASERAQ
        ErsQEBL/EA8R/w8PEf8PDhD/Dg0P/w0ND/8MDA79DAwNjB5z/9QedP//HnX//x52//8edv//Hnb//x52
        //8edv//Hnb//x51//8edP//HnP//x9x//8fb///H23//x9r//8faP//H2X//x9j//8fY///H2P/gxg4
        hAASERM1ERET7hERE/8REBL/EBAS/xAPEf8PDhD/Dg4Q/w0ND/8NDA7THnX/wx52//8ed///Hnj//x54
        //8eef//Hnn//x54//8eeP/9Hnf/8h52//Iedf/zHnP//h9x//8fb///H23//x9q//8fZ///H2T//x9j
        //8fY/90GkGeABISFCYTEhThEhIU/xIRE/8RERP/EBAS/xAPEf8PDhD/Dg4Q/w4ND78ed/9UHnj/5x55
        //8eev//Hnr//x57//8ee///Hnr/+x56/5AeeP88Hnf/PB52/0Eedf+wHnP//x9x//8fb///H2z//x9p
        //8fZv//H2T/zB9j/yQdV9wAExIVAxMTFYMTEhX8ExIU/xISFP8RERP/ERAS/xAPEf8PDhDkDg4QTx50
        /wAeev9dHnv/7R58//8efP//Hn3//x59//0efP+UHnv/DB55/wAed/8AHnb/AB52/x8edf+5HnP//x9x
        //8fbv//H2v//x9o/9YfZf85H2f/AAAAAAAUExYAFBMWDRQTFpQUExX9ExIV/xISFP8SERP/ERAS6hAP
        EVYTDhQAHnr/AB58/wEefP9nHn3/7x5+//8efv/9Hn7/nR59/xAefP8AHnv/AB52/wAedf8AHnX/AB51
        /yUedP2+H3P//x9x//8fbPvbH2r/Px9o/wAfZv8AH2P/ABQTFgAUExYAFBQWEBQUFp0UExb9ExIV/xIS
        FOwRERNfIxkMABAQEQAee/8AHn3/AB5+/wcefv+xHn///x5//+oef/8tHn//AB59/wAefP8AAAAAAB52
        /wAeV/8AGkKGABk+eUYaQoPzG0KG/xo0ZZIbRZkAH2v/AB9o/wAAAAAAFBQWABQUFgAVFBcAFRQXLRUU
        F+oUExb/ExMVqxMTFAUSERQAEBASAAAAAAAegP8AHn//Ax6A/6kegP//HoD/5x6A/ycegP8AHoD/AAAA
        AAAAAAAAAAAAABpCgQAXFRcAFhMSOhcVFvAXFRb/GBUWjhgYHgAcO3gAAAAAAAAAAAAAAAAAFxYZABYV
        GAAWFRgnFRUX5xUUF/8UExajExIVAhQTFgAAAAAAHoD/AB6A/wAegP8IHoD/sx6B//8egf/rHoH/Lx6B
        /wAegv8AHoL/AAAAAAAXFhkAFBQXABcXGgAXFxpIGBca9BgYG/8ZGBuTGRkcABoaHQAbGx4AAAAAABkZ
        HAAYGBsAFxcZABcWGS8WFhjrFRUX/xUUF6wUFBYGFBMWABQTFQAef/8AHoD/Ah6A/20egf/xHoH//x6B
        //4egv+kHoL/Ex6C/wAegv8AFhYYABsZIAAXFxoAFxcaKRgXGsMYGBv/GRkc/xoZHN4bGh1FHBoeABwb
        HwAdHCAAGhkdABkZHAAYGBsTGBcapBcWGf4WFRj/FRQX7xQUFmYTEBMBExMVAB59/wIegP9kHoD/8B6B
        //8egv//HoL//x6C//8egv+cHoL/EB6B/wAaRX4AFxYZABcWGSQXFxq/GBgb/xkZHP8aGR3/Gxoe/xwb
        HtkcGx89HBwfABoYIQAbGh0AGxodEBkZHJwYGBv/Fxca/xYWGf8VFRf/FBQW7RQTFVwBABAAHn//Vx5/
        /+kegP//HoH//x6C//8egv//HoL//x6C//4egf+JHoD/BBgxUQAWFhgSFxYZsRgXGv8ZGBv/Ghkc/xsa
        Hf8cGx7/HBwf/x0cIM8eHSAmHRwgAB0cHwQbGh6JGhkc/hkYG/8YFxr/FxYZ/xYVGP8VFBf/ExMV5hMS
        FFIefv/EHn///x6A//8egf//HoL//x6C//8egv//HoL//x6B/+Megf8nGUB0ABYWGEwXFhn4GBca/xkY
        G/8aGh3/Gxse/xwcH/8dHCD/Hh0h/x4dIXUdHCAAHBsfJxwbHuMaGh3/GRkc/xgXGv8XFhn/FhUY/xUU
        F/8UExX/ExIUwR5+/9Uef///HoD//x6B//8egf//HoL//x6C//8egf//HoH/7h6B/zQaRX4AFhYZWhcW
        GfwYGBv/GRkc/xsaHf8cGx7/HRwg/x4dIf8fHiL/Hx4igx4dIQAdHB80HBsf7hsaHf8aGRz/GBgb/xcX
        Gf8WFRj/FRQX/xQTFf8TEhXSHn7/jR5///0egP//HoD//x6B//8egf//HoH//x6B//8egf+4HoD/EBk3
        YAAWFhkqFxYZ2xgYG/8ZGRz/Gxod/xwbHv8dHCD/Hh0h/x8eIu8gHyNKHx4iABwcHxAcGx64Gxod/xoZ
        HP8YGBv/Fxca/xYVGP8VFBf/FBMV/BMSFYgefv8VHn7/oh5///8egP//HoD//x6B//8egf//HoD/0B6A
        /zIegf8AFxIRABgaHQAXFxpQGBca5hkYHP8aGh3/Gxse/x0cH/8dHSD1Hh0hch8eIwQfHiIAGxseABwb
        HjIaGh3QGRkc/xgXG/8XFhn/FhUY/xUUF/8UExafExIVEx5+/wAefv8XHn7/qx5///8ef///Hn///x6A
        /9UegP85HoD/AB5//wAYIzUAFxcaABgYGwAYFxpXGRgb6hoZHP8bGh7/HBse+B0cH3weHSEFHh0hAB8e
        IgAbGh0AGxodABoZHDkZGBvVGBca/xcWGf8WFRj/FRQXqBQUFhUUExYAHn7/AB5+/wAefv8dHn7/yR5+
        //8efv/yHn//Sh5//wAef/8AHoD/AAAAAAAYFxoAGBcaABUXGgAZGBtUGRkcwxoaHc0bGh5yHBseCB0c
        HwAeHSEAAAAAABsaHQAaGRwAGRgbABgYG0oYFxryFxYZ/xYVGMUVFBcbFRQXABQUFgAefv8AHnz/AB56
        /wIefP+oHn3//x59/+ceff8lHn3/AB5//wAAAAAAAAAAAAAAAAAYGBoAGBgbABgXGgAaGRwNGhkdERkZ
        HAAbGh4AGxoeAAAAAAAAAAAAAAAAABkZHAAXFxoAFxcaJRcWGecWFRj/FRUXohMSFQEVFBcAFRQXAB50
        /wAed/8AHnn/CR56/7Qee///Hnv/6x57/y4eev8AHnj/AB53/wAAAAAAFRQXABYVGAAWFRgAFxYZJBgX
        GnEYFxp6GRgbNBkOEQAYGBsAGBcaAAAAAAAWFhgAFhYYABYWGQAXFhkuFhYY6xUVF/8VFBeuFBQWBxMT
        FQASERQAHnL/AB11/wMedv9zHnf/8x54//8eef/+Hnn/oR55/xEed/8AHnb/ABMSFgAbFx0AFRQXABUV
        Fy4WFhjJFxYZ/xgXGv8YFxreGBcaShcXGgAYFxoAFxYZABYVGAAWFRgAFhYYERYWGKEWFRj+FRQX/xQT
        FvETEhVuEhETAxIREwAgb/8CH3L/aR50//Iedf//Hnb//x53//8ed//+Hnf/lx52/w0edf8AGEOMABQT
        FgAUExYnFRQXxBUVF/8WFhj/FxYZ/xcXGv8XFxrdFxcaQRYWGQAWFRgAFhUYABYVGA0WFRiXFhUY/hUU
        F/8UExb/ExMV/xISFPARERNlEQ4RAh9t/1sfb//sH3H//x9y//8ec///HnT//x50//8edP/9HnT/hx9y
        /wUVKUwAEhIUFBMSFbYUExb/FBQW/xERE/8QEBL/FhUY/xYWGf8XFhnXFhYYYBYVGE4WFRhOFhUYmBUV
        F/wVFBf/FBMW/xMTFf8SEhT/ERET/xEQEuoQDxFXH2v/xh9s//8fbv//H2///x9w//8fcf//H3H//x9x
        //8fcf/kH3D/KBc3cQASERNOEhIU+BMSFf8SERT/DAwN/woJC/8QDxH/FRUX/xYVGP8WFRj7FRUX+BUU
        F/gVFBf/FBQW/xQTFv8TEhX/EhIU/xIRE/8REBL/EA8R/w8PEMMfZ//UH2n//x9r//8fbP//H23//x9u
        //8fbv//H27//x9u/+4fbv80Fzl6ABEQE1oRERP8EhIU/xERE/8ODg//DAwN/xAPEf8UExb/FBQW/xQU
        Fv8UFBb/FBMW/xQTFv8TExX/ExIU/xISFP8SERP/ERAS/xAQEv8PDxH/Dg4Q0R9l/4kfZf/8H2f//x9o
        //8faf//H2r//x9q//8fav//H2v/sh9r/w4VKlcAEBASKRAQEtoRERP/EhET/xEQEv8QEBL/EhIU/xMT
        Ff8TExXyExMVsxMTFagTEhWoExIU1BISFP8SERP/ERET/xEQEv8QDxH/Dw8R/w4OEPwODQ+EH2P/Ex9j
        /6AfZP//H2T//x9l//8fZv//H2b//x9n/8gfaP8qH2j/AA0CAAAREBIAEA8RThAQEuUREBL/ERET/xIR
        E/8SERP/EhEU9hISFHYTExUIEhEUAxMRFQISERMxERETzREQEv8QEBL/EA8R/w8OEP8ODhD/Dg0Pnw0N
        DxIfY/8AH2P/Fh9j/6kfY///H2P//x9j//8fY//MH2T/MB9m/wAfVP8AERgnABAPEQAQERMAEA8RVRAP
        EegQEBL/EBAS/xEQEvcRERN9EhETBhISFAATEhUAExIVABEREwAQEBI2EA8R0g8PEf8PDhD/Dg4P/w4N
        D6gNDQ8VDg0PAB9j/wAfY/8AH2P/HR9j/6QfY//nH2P/wh9j/zofZP8AH2P/AB9m/wAAAAAAEA8RAA8P
        EQAKDhIBDw8QYA8PEdcQDxHiEA8RgRAPEQkREBIAEhETAAAAAAARERMAEA8RABAPEQAPDhBADg4Qxg4N
        D+cODQ+iDQ0OHA0NDwAODQ8AACAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAGAIBw
        DgEAIAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAQAAHAOAAAgBAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgBAA=
</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>بيانات  الفرع/المخزن</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.AutoComplete" type="DevExpress.XtraEditors.Mask.AutoCompleteType, DevExpress.XtraEditors.v15.1">
    <value>Default</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.BeepOnError" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.EditMask" xml:space="preserve">
    <value />
  </data>
  <data name="repositoryItemTextEdit1.Mask.IgnoreMaskBlank" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.MaskType" type="DevExpress.XtraEditors.Mask.MaskType, DevExpress.XtraEditors.v15.1">
    <value>None</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.PlaceHolder" type="System.Char, mscorlib" xml:space="preserve">
    <value>_</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.SaveLiteral" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.ShowPlaceHolders" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="repositoryItemTextEdit1.Mask.UseMaskAsDisplayFormat" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
</root>