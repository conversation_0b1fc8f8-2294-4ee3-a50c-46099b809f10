﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="mscorlib" name="mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="Detail.HeightF" type="System.Single, mscorlib">
    <value>0</value>
  </data>
  <data name="Detail.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <assembly alias="DevExpress.Data.v15.1" name="DevExpress.Data.v15.1, Version=15.1.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
  <data name="xrTable2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>9.999998, 0</value>
  </data>
  <data name="cell_Total.Weight" type="System.Double, mscorlib">
    <value>0.20066914095262461</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="cell_bonusDiscount.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="cell_bonusDiscount.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="cell_Disc.Weight" type="System.Double, mscorlib">
    <value>0.089690607521587126</value>
  </data>
  <data name="cell_DiscountRatio.Weight" type="System.Double, mscorlib">
    <value>0.13428194462437337</value>
  </data>
  <data name="cell_tableTaxValue.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="cell_tableTaxValue.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="cell_addTaxValue.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="cell_addTaxValue.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="cell_Price.Weight" type="System.Double, mscorlib">
    <value>0.12664737512804838</value>
  </data>
  <data name="xrTable2.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>744.8333, 29.1666718</value>
  </data>
  <data name="qrCode.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>248.639175, 251.583328</value>
  </data>
  <data name="qrCode.Text" xml:space="preserve">
    <value>LinkIT Information Technology</value>
  </data>
  <data name="xrLabel4.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>704.729065, 326.0417</value>
  </data>
  <data name="lbl_notes.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>296.667328, 326.0417</value>
  </data>
  <data name="lbl_Paymethod.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>540.4874, 230.2083</value>
  </data>
  <data name="xrLabel1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>704.7292, 301.0417</value>
  </data>
  <data name="lbl_Customer.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>296.667328, 301.0417</value>
  </data>
  <data name="lbl_Serial.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>615.4874, 230.2083</value>
  </data>
  <data name="xrLabel12.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>477.987427, 230.2083</value>
  </data>
  <data name="lbl_Drawer.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>410.17218, 230.208267</value>
  </data>
  <data name="TopMargin.HeightF" type="System.Single, mscorlib">
    <value>510.541656</value>
  </data>
  <data name="BottomMargin.HeightF" type="System.Single, mscorlib">
    <value>39.16664</value>
  </data>
  <data name="xrTable1.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>7.41616869, 9.999974</value>
  </data>
  <data name="xrTableCell1.Weight" type="System.Double, mscorlib">
    <value>0.21473569236996964</value>
  </data>
  <data name="xrTableCell10.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="xrTableCell10.Text" xml:space="preserve">
    <value>خصم البونص</value>
  </data>
  <data name="xrTableCell10.Weight" type="System.Double, mscorlib">
    <value>0.084646442584579984</value>
  </data>
  <data name="xrTableCell9.Weight" type="System.Double, mscorlib">
    <value>0.090174097837400408</value>
  </data>
  <data name="xrTableCell4.Weight" type="System.Double, mscorlib">
    <value>0.13500580124709533</value>
  </data>
  <data name="xrTableCell11.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="xrTableCell11.Text" xml:space="preserve">
    <value>ضريبة جدول
</value>
  </data>
  <data name="xrTableCell2.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="xrTableCell2.Text" xml:space="preserve">
    <value>ضريبة قيمة مضافة
</value>
  </data>
  <data name="xrTableCell7.Weight" type="System.Double, mscorlib">
    <value>0.12768354099024995</value>
  </data>
  <data name="lbl_TotalETax.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_TotalETax.Text" xml:space="preserve">
    <value>lbl_TotalETax</value>
  </data>
  <data name="lbl_TotalETax.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="lbl_TotalPacks.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_TotalPacks.Text" xml:space="preserve">
    <value>lbl_TotalPacks</value>
  </data>
  <data name="lbl_TotalPacks.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_TotalPacks.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_totalPieces.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_totalPieces.Text" xml:space="preserve">
    <value>lbl_totalPieces</value>
  </data>
  <data name="lbl_totalPieces.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_totalPieces.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_TotalQty.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_TotalQty.Text" xml:space="preserve">
    <value> lbl_TotalQty</value>
  </data>
  <data name="lbl_TotalQty.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_TotalQty.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="lbl_BalanceBefore.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_BalanceBefore.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_BalanceAfter.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_BalanceAfter.Text" xml:space="preserve">
    <value> رصيد بعد الفاتورة</value>
  </data>
  <data name="lbl_BalanceAfter.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel21.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrLabel21.Text" xml:space="preserve">
    <value>الرصيد بعد الفاتورة</value>
  </data>
  <data name="xrLabel21.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="xrLabel14.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrLabel14.Text" xml:space="preserve">
    <value>الرصيد قبل الفاتورة</value>
  </data>
  <data name="xrLabel14.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="txt_Handing.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="txt_Handing.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>305.8328, 173.7292</value>
  </data>
  <data name="txt_Handing.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>101.7917, 25.45837</value>
  </data>
  <data name="txt_Handing.Text" xml:space="preserve">
    <value> </value>
  </data>
  <data name="txt_Handing.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_Updated.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="lbl_Updated.Text" xml:space="preserve">
    <value>..</value>
  </data>
  <data name="lbl_Updated.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="lbl_Updated.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="xrLabel9.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>113.348389, 134.145859</value>
  </data>
  <data name="xrLabcus.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>9.375061, 134.145859</value>
  </data>
  <data name="xrLabcus.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>101.7917, 25.45837</value>
  </data>
  <data name="xrLabel2.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>112.723389, 108.6874</value>
  </data>
  <data name="xrLabaddtax.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>9.375061, 108.60437</value>
  </data>
  <data name="xrLabaddtax.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>101.7917, 25.45837</value>
  </data>
  <data name="xrTable3.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>300.520844, 12.5</value>
  </data>
  <data name="cell_PiecesCount.Text" xml:space="preserve">
    <value>cell_PiecesCount</value>
  </data>
  <data name="xrTable3.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>448.437469, 25</value>
  </data>
  <data name="cell_Pack.Text" xml:space="preserve">
    <value>Pack</value>
  </data>
  <data name="Cell_MUOM.Weight" type="System.Double, mscorlib">
    <value>0.20563037969499964</value>
  </data>
  <data name="lbl_Remains.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>101.7917, 25.45837</value>
  </data>
  <data name="lbl_Total.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>9.375061, 2</value>
  </data>
  <data name="lbl_Total.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>101.7917, 25.45837</value>
  </data>
  <data name="xrLabel16.Text" xml:space="preserve">
    <value>اجمالي ض</value>
  </data>
  <data name="lbl_DiscountV.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>9.375061, 54</value>
  </data>
  <data name="lbl_DiscountV.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>101.7917, 25.45837</value>
  </data>
  <data name="lbl_Net.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>9.375061, 159.875229</value>
  </data>
  <data name="lbl_Net.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>101.7917, 25.45837</value>
  </data>
  <data name="lbl_TaxV.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>280.208466, 213.229309</value>
  </data>
  <data name="lbl_TaxV.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>101.7917, 25.45837</value>
  </data>
  <data name="lbl_TaxV.Visible" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="xrLabel20.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>113.348419, 80.08302</value>
  </data>
  <data name="lbl_ExpensesV.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>9.375061, 79.9999847</value>
  </data>
  <data name="lbl_ExpensesV.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>101.7917, 25.45837</value>
  </data>
  <data name="xrLabel23.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>113.348389, 159.875229</value>
  </data>
  <data name="ReportFooter.HeightF" type="System.Single, mscorlib">
    <value>282.958618</value>
  </data>
  <data name="Detail1.HeightF" type="System.Single, mscorlib">
    <value>29.7916412</value>
  </data>
  <data name="ReportHeader.HeightF" type="System.Single, mscorlib">
    <value>46.1249733</value>
  </data>
  <data name="xrTable7.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="xrTable7.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>12.2970181, 0</value>
  </data>
  <data name="xrTableRow7.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Moccasin</value>
  </data>
  <data name="cell_Value.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="cell_Rate.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="cell_subtaxId.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>White</value>
  </data>
  <data name="cell_subtaxId.Weight" type="System.Double, mscorlib">
    <value>0.81197049144019839</value>
  </data>
  <data name="xrTableRow7.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrTable7.SizeF" type="System.Drawing.SizeF, System.Drawing">
    <value>747.077942, 36.125</value>
  </data>
  <data name="xrTable7.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="Detail2.HeightF" type="System.Single, mscorlib">
    <value>37.5</value>
  </data>
  <data name="xrTable6.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt</value>
  </data>
  <data name="xrTable6.LocationFloat" type="DevExpress.Utils.PointFloat, DevExpress.Data.v15.1">
    <value>12.5828819, 104.166664</value>
  </data>
  <data name="xrTableRow6.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Moccasin</value>
  </data>
  <data name="xrTableCell18.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="xrTableCell18.Text" xml:space="preserve">
    <value>القيمة</value>
  </data>
  <data name="xrTableCell19.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="xrTableCell19.Text" xml:space="preserve">
    <value>النسبة</value>
  </data>
  <data name="xrTableCell20.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>Silver</value>
  </data>
  <data name="xrTableCell20.Text" xml:space="preserve">
    <value>الضريبة</value>
  </data>
  <data name="xrTableRow6.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 12pt, style=Bold</value>
  </data>
  <data name="xrTable6.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleCenter</value>
  </data>
  <data name="xrLine2.BackColor" type="System.Drawing.Color, System.Drawing">
    <value>DarkGray</value>
  </data>
  <data name="xrLine2.BorderColor" type="System.Drawing.Color, System.Drawing">
    <value>DarkGray</value>
  </data>
  <data name="xrLine2.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>DarkGray</value>
  </data>
  <data name="xrLabel26.Font" type="System.Drawing.Font, System.Drawing">
    <value>Times New Roman, 18pt</value>
  </data>
  <data name="xrLabel26.ForeColor" type="System.Drawing.Color, System.Drawing">
    <value>Navy</value>
  </data>
  <data name="xrLabel26.Text" xml:space="preserve">
    <value>الضرايب</value>
  </data>
  <data name="xrLabel26.TextAlignment" type="DevExpress.XtraPrinting.TextAlignment, DevExpress.Data.v15.1">
    <value>MiddleRight</value>
  </data>
  <data name="ReportHeader1.HeightF" type="System.Single, mscorlib">
    <value>142.708328</value>
  </data>
  <data name="$this.Margins" type="System.Drawing.Printing.Margins, System.Drawing">
    <value>24, 39, 511, 39</value>
  </data>
</root>