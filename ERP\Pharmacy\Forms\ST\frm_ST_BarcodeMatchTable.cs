﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Linq;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DAL;
using DAL.Res;
using System.Data.OleDb;
using DevExpress.XtraGrid.Views.Grid;

namespace Pharmacy.Forms
{

    public partial class frm_ST_BarcodeMatchTable : DevExpress.XtraEditors.XtraForm
    {
        DataTable dt = new DataTable();
        ERPDataContext DbBind = new ERPDataContext();
        public frm_ST_BarcodeMatchTable()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();

            if (Shared.IsEnglish)
                RTL.LTRLayout(this);
        }

        private void frm_IC_ImportItems_Load(object sender, EventArgs e)
        {
            grd_MatchTable.DataSource = DbBind.ST_BarcodeMatches.Select(x => x);
        }

        private void btn_Openfile_Click(object sender, EventArgs e)
        {
            OpenFileDialog ofd = new OpenFileDialog();
            ofd.Filter = "Excel File(*.xls)|*.xls|Excel File(*.xlsx)|*.xlsx";
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    dt = ErpUtils.exceldata(ofd.FileName);
                    if (dt != null)
                    {
                        gv_import.Columns.Clear();

                        grd_Import.DataSource = dt;
                    }
                    else
                        return;
                }
                catch
                { }
            }
            else
                return;
        }

        private void btnSaveImport_Click(object sender, EventArgs e)
        {
            gv_import.UpdateCurrentRow();
            gv_import.CloseEditor();

            ERPDataContext DB = new ERPDataContext();            

            #region Insert Items
            List<MatchTable> lstItemsBalance = new List<MatchTable>();

            //validate
            foreach (DataRow dr in dt.Rows)
            {
                if (dr.RowState == DataRowState.Deleted)
                    continue;

                if (dr["Serial"] == null || dr["Serial"] == DBNull.Value || dr["Serial"].ToString() == string.Empty)
                {
                    MessageBox.Show("please check your data");
                    return;
                }
                if (dr["Barcode"] == null || dr["Barcode"] == DBNull.Value || dr["Barcode"].ToString() == string.Empty)
                {
                    MessageBox.Show("please check your data");
                    return;
                }
            }

            //insert
            foreach (DataRow dr in dt.Rows)
            {
                if (dr.RowState == DataRowState.Deleted)
                    continue;

                #region IC_Item
                ST_BarcodeMatch item = new ST_BarcodeMatch();

                item.Serial = dr["Serial"].ToString();
                item.Barcode = dr["Barcode"].ToString();
                DB.ST_BarcodeMatches.InsertOnSubmit(item);

                #endregion
            }
            DB.SubmitChanges();

            #endregion

            MessageBox.Show("No.of Imported Barcodes: "+ dt.Rows.Count.ToString());
            btn_SaveImport.Enabled = false;

            grd_MatchTable.DataSource = DbBind.ST_BarcodeMatches.Select(x=>x);
        }

        private void gv_MatchTable_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Delete && e.Modifiers == Keys.Control)
            {
                if (MessageBox.Show(
                    "Delete Row?",
                    "Warning",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) !=
                  DialogResult.Yes)
                    return;

                GridView view = sender as GridView;

                view.DeleteRow(view.FocusedRowHandle);
            }
        }

        private void btn_SaveMatchTable_Click(object sender, EventArgs e)
        {
            try
            {
                gv_MatchTable.UpdateCurrentRow();
                gv_MatchTable.CloseEditor();
                DbBind.SubmitChanges();
                MessageBox.Show("Data Saved Successfully");

            }
            catch(Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        private void btn_delete_Click(object sender, EventArgs e)
        {
            if(MessageBox.Show("are you sure you want to delete all rows","warning", MessageBoxButtons.YesNo, MessageBoxIcon.Warning, 
                MessageBoxDefaultButton.Button2 ) == System.Windows.Forms.DialogResult.Yes)
            {
                DbBind.ST_BarcodeMatches.DeleteAllOnSubmit(DbBind.ST_BarcodeMatches);
                DbBind.SubmitChanges();
                grd_MatchTable.DataSource = DbBind.ST_BarcodeMatches.Select(x => x);
                MessageBox.Show("data deleted successfully");
            }
        }
    }

    class MatchTable
    {
        public string Serial { get; set; }
        public string Barcode { get; set; }
    }
}