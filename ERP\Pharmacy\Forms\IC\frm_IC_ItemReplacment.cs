﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Base;
using DAL;

using DevExpress.XtraPrinting;
using System.IO;
using DAL.Res;

namespace Pharmacy.Forms
{
    public partial class frm_IC_ItemReplacment : DevExpress.XtraEditors.XtraForm
    {
        List<DAL.IC_UOM> uom_list;

        DataTable dtPR_Details = new DataTable();
        DataTable dtCompanies = new DataTable();
        DataTable dtVendors = new DataTable();
        DataTable dtUOM = new DataTable();
        DataTable dtDrawers = new DataTable();

        List<VendorInfo> lst_Vendors = new List<VendorInfo>();

        IC_StockTaking StockTacking;
        bool Deleting = false;
        DAL.ERPDataContext DB;
        ST_Store st_Store;

        int StoreId = 0;
        int StockTakingID = 0;
        bool isCommited;
        bool IsNew = true;
        bool dataModified;
        UserPriv prvlg;

        decimal Shortage = 0, Profit = 0;

        public frm_IC_ItemReplacment()
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

        }

        public frm_IC_ItemReplacment(int StoreId, int StockTakingID, DateTime Date, string Notes, bool isCommited)
        {
            RTL.EnCulture(Shared.IsEnglish);
            InitializeComponent();
            RTL.RTL_BarManager(this.barManager1);

            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            StockTacking = (from s in DB.IC_StockTakings
                            where s.IC_StockTaking_ID == StockTakingID
                            select s).SingleOrDefault();

            this.StoreId = StoreId;
            this.StockTakingID = StockTakingID;
            this.isCommited = isCommited;
            lkp_Account.EditValue = StockTacking.AccountId;
            dt_ST_Date.EditValue = Date;
            txtNote.Text = Notes;
            IsNew = false;

            dt_ST_Date.Properties.ReadOnly = lkp_Store.Properties.ReadOnly = true;

            if (isCommited == true)
            {
                barBtnCommit.Enabled = barBtnSave.Enabled = barBtnDelete.Enabled = false;
                barBtnCommit.Enabled = barBtnSave.Enabled = barBtnDelete.Enabled = Shared.user.AccessType == (byte)AccessType.Admin;
            }
        }

        private void frm_PR_Invoice_Load(object sender, EventArgs e)
        {
            if (Shared.IsEnglish)
                RTL.LTRLayout(this);

            st_Store = MyHelper.GetStoreSettings();
            grdPrInvoice.ProcessGridKey += new KeyEventHandler(grid_ProcessGridKey);
            //Utilities.Allow_Incremental_Search(repItems);
            ErpUtils.Allow_Incremental_Search(repItems);

            DB = new DAL.ERPDataContext();
            uom_list = (from u in DB.IC_UOMs
                        select u).ToList();

            Reset();

            if (IsNew)
                //dt_ST_Date.EditValue = Utilities.Get_Server_DateTime().Date;
                dt_ST_Date.EditValue = MyHelper.Get_Server_DateTime();


            BindDataSources();

            if (IsNew == false)
            {
                lkp_Store.EditValue = StoreId;
                GetStockTackingDetails();
            }
            //grdPrInvoice.DataSource = dtPR_Details;
            dtPR_Details.Columns["Supposed"].AllowDBNull = false;

            if (!Shared.StockIsPeriodic)
            {
                pnlAccount.Visible = true;
                lkp_Account.Properties.DataSource = HelperAcc.LoadAccountsTree(0, false);
                lkp_Account.Properties.ValueMember = "AccId";
                lkp_Account.Properties.DisplayMember = "AccName";
            }
            else
            {
                pnlAccount.Visible = false;
            }

            col_ActualPieces.Caption = Shared.IsEnglish ? "Replaced " + Shared.st_Store.PiecesCountNameEn : Shared.st_Store.PiecesCountNameAr + " المبدل";
            col_PiecesCount.Caption = Shared.IsEnglish ? "Current " + Shared.st_Store.PiecesCountNameEn : Shared.st_Store.PiecesCountNameAr + " الحالى";
            ErpUtils.Load_Grid_Layout(grdPrInvoice, this.Name.Replace("frm_", ""));

        }

        private void frm_PR_Invoice_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Insert)
            {
                grdPrInvoice.Focus();
                var view = (grdPrInvoice.FocusedView as DevExpress.XtraGrid.Views.Grid.GridView);
                //view.FocusedColumn = view.Columns[st_Store.InvoicesUseSearchItems ? "ItemId" : "ItemCode1"];//mahmoud
                view.FocusedColumn = view.Columns[Shared.user.InvoicesUseSearchItems ? "ItemId" : "ItemCode1"];
            }
            if (e.KeyCode == Keys.Home && e.Modifiers == Keys.Control)
                txtNote.Focus();
        }

        private void frm_IC_ItemReplacment_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (!isCommited)
            {
                if (ChangesMade() == DialogResult.Cancel)
                    e.Cancel = true;
                else
                    e.Cancel = false;
            }
            ErpUtils.save_Grid_Layout(grdPrInvoice, this.Name.Replace("frm_", ""), true);
        }


        private void gridView1_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            DB = new DAL.ERPDataContext();
            DAL.IC_Item item = null;

            GridView view = grdPrInvoice.FocusedView as GridView;
            DataRow row = view.GetFocusedDataRow();

            #region GetItem
            if (e.Column.FieldName == "ItemCode1")
            {
                if (view.GetFocusedRowCellValue("ItemCode1").ToString() != string.Empty)
                {
                    //---------mahmoud
                    int code1 = 0;
                    Int32.TryParse(view.GetFocusedRowCellValue("ItemCode1").ToString(), out code1);
                    if (code1 > 0)
                    {
                        //item = (from i in DB.IC_Items
                        //        where i.ItemType != (int)BL.ItemType.Service
                        //        && i.ItemType != (int)BL.ItemType.MatrixParent
                        //        && i.ItemType != (int)BL.ItemType.Subtotal
                        //        where i.ItemCode1 == code1
                        //        select i).FirstOrDefault();



                        item = (from i in DB.IC_Items
                                where i.ItemType != (int)ItemType.Service
                                && i.ItemType != (int)ItemType.MatrixParent
                                && i.ItemType != (int)ItemType.Subtotal
                                where i.ItemCode1 == code1
                                select i).FirstOrDefault();

                        //  row["ItemId"] = item.ItemId;

                    }
                    //---------



                }
                else
                {
                    view.DeleteRow(e.RowHandle);
                }
            }

            else if (e.Column.FieldName == "ItemId")
            {
                if (view.GetFocusedRowCellValue("ItemId").ToString() != string.Empty)
                {
                    //item = (from i in DB.IC_Items
                    //        where i.ItemType != (int)BL.ItemType.Service
                    //            && i.ItemType != (int)BL.ItemType.MatrixParent
                    //            && i.ItemType != (int)BL.ItemType.Subtotal
                    //        where i.ItemId == Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"))
                    //        select i).SingleOrDefault();



                    item = (from i in DB.IC_Items
                            where i.ItemType != (int)ItemType.Service
                                && i.ItemType != (int)ItemType.MatrixParent
                                && i.ItemType != (int)ItemType.Subtotal
                            where i.ItemId == Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"))
                            select i).SingleOrDefault();


                }
                else
                {
                    view.DeleteRow(e.RowHandle);
                }
            }

            if (e.Column.FieldName == "ItemCode1" || e.Column.FieldName == "ItemCode2"
                || e.Column.FieldName == "ItemId")
            {
                if (item != null && item.ItemId > 0)
                {
                    LoadItemRow(item, row);
                    //view.FocusedColumn = view.Columns["UOM"];
                }
                else
                {
                    //view.FocusedColumn = view.Columns["ItemId"];
                }
            }
            #endregion

            #region Get Price
            if (e.Column.FieldName == "UOM")
            {
                item = (from i in DB.IC_Items
                        where i.ItemId == Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"))
                        select i).SingleOrDefault();

                int uomIndex = repUOM.GetIndexByKeyValue(view.GetRowCellValue(e.RowHandle, "UOM"));

                if (uomIndex == 0)//small
                {
                    view.SetFocusedRowCellValue("SellPrice", item.SmallUOMPrice);
                    view.SetFocusedRowCellValue("PurchasePrice", (item.PurchasePrice));
                }
                if (uomIndex == 1)//medium
                {
                    //view.SetFocusedRowCellValue("PurchasePrice", (item.PurchasePrice * Utilities.FractionToDouble(item.MediumUOMFactor)));
                    view.SetFocusedRowCellValue("PurchasePrice", (item.PurchasePrice * MyHelper.FractionToDouble(item.MediumUOMFactor)));

                    view.SetFocusedRowCellValue("SellPrice", item.MediumUOMPrice);
                }
                if (uomIndex == 2)//large
                {
                    //view.SetFocusedRowCellValue("PurchasePrice", (item.PurchasePrice * Utilities.FractionToDouble(item.LargeUOMFactor)));
                    view.SetFocusedRowCellValue("PurchasePrice", (item.PurchasePrice * MyHelper.FractionToDouble(item.LargeUOMFactor)));

                    view.SetFocusedRowCellValue("SellPrice", item.LargeUOMPrice);
                }
                view.SetFocusedRowCellValue("UomIndex", uomIndex);

                if (item.is_libra == true)
                {
                    view.SetFocusedRowCellValue("SellPrice", MyHelper.GetPriceLevelSellPrice(null, item, item.DfltPrchsUomIndx));
                    view.SetFocusedRowCellValue("SellPrice", MyHelper.GetPriceLevelSellPrice(null, item, item.DfltPrchsUomIndx));
                }
            }
            #endregion

            #region Get Store Qty
            if (e.Column.FieldName == "UOM" || e.Column.FieldName == "ItemId"
                || e.Column.FieldName == "ItemCode1"
                /* e.Column.FieldName == "ItemCode2" || */)
            {
                if (view.GetFocusedRowCellValue("ItemId") == DBNull.Value)
                {
                    //MessageBox.Show(RTL.IsEnglish?ResICEn.MsgItemdoesntExist:ResICAr.MsgItemdoesntExist, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    MessageBox.Show(Shared.IsEnglish ? ResICEn.MsgItemdoesntExist : ResICAr.MsgItemdoesntExist, "", MessageBoxButtons.OK, MessageBoxIcon.Information);


                    view.DeleteRow(e.RowHandle);
                    return;
                }
                int itmId = Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"));

                if (e.Column.FieldName == "ItemCode1")
                {
                    view.SetFocusedRowCellValue("Supposed", null);
                }
                byte uomIndex = Convert.ToByte((from DataRow dr in dtUOM.Rows
                                                where dr["UomId"] == view.GetFocusedRowCellValue("UOM")
                                                select dr["Index"]).FirstOrDefault());

                decimal medium = 1, large = 1;
                if (view.GetFocusedRowCellValue("MediumUOMFactor").ToString() != string.Empty)
                    medium = Convert.ToDecimal(view.GetFocusedRowCellValue("MediumUOMFactor"));
                if (view.GetFocusedRowCellValue("LargeUOMFactor").ToString() != string.Empty)
                    large = Convert.ToDecimal(view.GetFocusedRowCellValue("LargeUOMFactor"));

                //decimal currentQty = MyHelper.GetItemQty(itmId, Convert.ToInt32(lkp_Store.EditValue));

                decimal currentQty = MyHelper.GetItemQty(dt_ST_Date.DateTime, itmId, Convert.ToInt32(lkp_Store.EditValue));

                decimal piecesCount = MyHelper.GetItemPieces(dt_ST_Date.DateTime, itmId, Convert.ToInt32(lkp_Store.EditValue));

                if (Shared.LibraAvailabe)
                {
                    if (item == null & view.GetRowCellValue(e.RowHandle, "ItemId") != DBNull.Value)
                        item = (from i in DB.IC_Items
                                where i.ItemId == Convert.ToInt32(view.GetRowCellValue(e.RowHandle, "ItemId"))
                                select i).FirstOrDefault();
                    if (item == null) return;

                    int _PiecesCount = 0;
                    if (view.GetFocusedRowCellValue("ActualPieces") != DBNull.Value)
                        _PiecesCount = Convert.ToInt32(view.GetFocusedRowCellValue("ActualPieces"));
                    else
                        view.SetFocusedRowCellValue("ActualPieces", 0);
                    if (_PiecesCount < 0)
                    {
                        _PiecesCount = 0;
                        view.SetFocusedRowCellValue("ActualPieces", 0);
                    }

                    if (item != null && item.VariableWeight == true)
                    {
                        currentQty = Math.Round(currentQty, 2);
                        view.SetFocusedRowCellValue("kg_Weight_libra", currentQty);
                    }

                    else if (item != null && item.VariableWeight == false && item.PricingWithSmall == true)
                    {
                        try
                        {
                            decimal f = 0;
                            //if (xxx == null)
                            {
                                var _uom = view.GetFocusedRowCellValue("UOM");
                                if (_uom != DBNull.Value)
                                {
                                    byte uom = Convert.ToByte(_uom);
                                    f = item.SmallUOM == uom ? 1 : (item.MediumUOM == uom ? item.MediumUOMFactorDecimal.Value : item.LargeUOMFactorDecimal.Value);
                                }
                            }
                            //else
                            //    f = Convert.ToDecimal(((DataRowView)xxx)["Factor"]);

                            view.SetFocusedRowCellValue(col_kg_Weight_libra, Convert.ToDouble(piecesCount * f));
                            //view.SetFocusedRowCellValue("Supposed", Convert.ToDouble(piecesCount * f));
                            view.SetFocusedRowCellValue("Supposed", 0);
                            view.SetFocusedRowCellValue("Qty", currentQty);
                        }
                        catch
                        { }
                    }

                    else if (item != null && item.is_libra == true && e.Column.FieldName != "UOM")
                    {
                        //view.SetFocusedRowCellValue(col_LibraQty, currentQty);
                        view.SetFocusedRowCellValue(col_kg_Weight_libra,
                            Math.Round(currentQty / MyHelper.FractionToDouble(Shared.st_Store.KG_PR_Factor), 3));
                        view.SetFocusedRowCellValue("UOM", item.SmallUOM);

                    }
                }
                else
                    currentQty = Math.Round(MyHelper.getCalculatedUomQty(currentQty, uomIndex, medium, large), 2);
                view.SetFocusedRowCellValue("Qty", currentQty);
                view.SetFocusedRowCellValue("PiecesCount", piecesCount);
            }
            #endregion

            #region Set Supposed

            if (item == null)
                item = (from i in DB.IC_Items
                        where i.ItemType != (int)ItemType.Service
                            && i.ItemType != (int)ItemType.MatrixParent
                            && i.ItemType != (int)ItemType.Subtotal
                        where i.ItemId == Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"))
                        select i).SingleOrDefault();

            if (e.Column.FieldName == "LibraQty")
            {
                if (item.is_libra != true) return;

                var med_price = view.GetRowCellValue(e.RowHandle, "LibraQty");
                if (med_price == DBNull.Value || med_price == null) return;
                view.SetRowCellValue(e.RowHandle, "Supposed", Math.Round((Convert.ToDecimal(med_price) / MyHelper.FractionToDouble(Shared.st_Store.KG_PR_Factor)), 3));
                view.SetRowCellValue(e.RowHandle, "ActualWeight", Math.Round((Convert.ToDecimal(med_price) / MyHelper.FractionToDouble(Shared.st_Store.KG_PR_Factor)), 3));
            }

            else if (e.Column.FieldName == "ActualPieces")
            {
                if (item != null && item.VariableWeight == false && item.PricingWithSmall == true)
                {
                    try
                    {
                        int piecesCount = Convert.ToInt32(view.GetFocusedRowCellValue("ActualPieces"));
                        decimal f = 0;
                        //if (xxx == null)
                        {
                            var _uom = view.GetFocusedRowCellValue("UOM");
                            if (_uom != DBNull.Value)
                            {
                                byte uom = Convert.ToByte(_uom);
                                f = item.SmallUOM == uom ? 1 : (item.MediumUOM == uom ? item.MediumUOMFactorDecimal.Value : item.LargeUOMFactorDecimal.Value);
                            }
                        }
                        //else
                        //    f = Convert.ToDecimal(((DataRowView)xxx)["Factor"]);

                        view.SetFocusedRowCellValue(col_ActualWeight, Convert.ToDouble(piecesCount * f));
                        view.SetFocusedRowCellValue("Supposed", Convert.ToDouble(piecesCount * f));
                        //view.SetFocusedRowCellValue("Qty", currentQty);
                    }
                    catch
                    { }
                }
            }
            else if (e.Column == col_ActualWeight && item.VariableWeight == true)
            {
                view.SetFocusedRowCellValue("Supposed", view.GetFocusedRowCellValue(col_ActualWeight));
            }

            #endregion
        }

        private void gridView1_FocusedColumnChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedColumnChangedEventArgs e)
        {
            if (e.FocusedColumn != null)
            {
                GridView view = grdPrInvoice.FocusedView as GridView;
                DataRow row = view.GetFocusedDataRow();

                if (e.FocusedColumn.FieldName == "UOM")
                {
                    DB = new DAL.ERPDataContext();
                    DAL.IC_Item item = new DAL.IC_Item();

                    if (row != null && row["ItemId"].ToString() != string.Empty)
                    {
                        item = (from i in DB.IC_Items
                                where i.ItemId == Convert.ToInt32(row["ItemId"])
                                select i).SingleOrDefault();

                        MyHelper.GetUOMs(item, dtUOM, uom_list);

                        if (string.IsNullOrEmpty(row["UOM"].ToString()))
                            view.SetFocusedRowCellValue("UOM", 0);
                    }
                }
            }
        }

        private void gridView1_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Delete && e.Modifiers == Keys.Control)
            {
                if (MessageBox.Show(Shared.IsEnglish ? ResICEn.MsgDelRow : ResICAr.MsgDelRow, Shared.IsEnglish ? ResICEn.MsgTQues : ResICAr.MsgTQues, MessageBoxButtons.YesNo, MessageBoxIcon.Question) !=
                  DialogResult.Yes)
                    return;
                GridView view = sender as GridView;
                Deleting = true;
                view.DeleteRow(view.FocusedRowHandle);
                Deleting = false;
            }
        }

        private void gridView1_ValidateRow(object sender, DevExpress.XtraGrid.Views.Base.ValidateRowEventArgs e)
        {
            try
            {
                ColumnView view = sender as ColumnView;

                if (view.GetRowCellValue(e.RowHandle, view.Columns["ItemId"]).ToString().Trim() == string.Empty)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["ItemId"], Shared.IsEnglish ? ResICEn.txtValidateItem : ResICAr.txtValidateItem);//"يجب اختيار الصنف";
                }

                if (view.GetFocusedRowCellValue("ItemId") != null)
                {
                    int itmid = Convert.ToInt32(view.GetFocusedRowCellValue("ItemId"));
                    DataRow row = dtPR_Details.Select("ItemId=" + itmid.ToString()).SingleOrDefault();
                    if (row != null && view.GetDataSourceRowIndex(e.RowHandle) != dtPR_Details.Rows.IndexOf(row))
                    {
                        view.SetColumnError(view.Columns["ItemId"], Shared.IsEnglish ? ResICEn.txtItemExist : ResICAr.txtItemExist);
                        e.Valid = false;
                    }
                }

                if (view.GetRowCellValue(e.RowHandle, view.Columns["UOM"]).ToString().Trim() == string.Empty)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["UOM"], Shared.IsEnglish ? ResICEn.txtValidateUom : ResICAr.txtValidateUom);//"يجب اختيار وحدة القياس");
                }
                if (view.GetRowCellValue(e.RowHandle, view.Columns["Supposed"]) == null ||
                    view.GetRowCellValue(e.RowHandle, view.Columns["Supposed"]).ToString().Trim() == string.Empty)
                {
                    e.Valid = false;
                    view.SetColumnError(view.Columns["Supposed"], Shared.IsEnglish ? ResICEn.txtValidateQty : ResICAr.txtValidateQty);//"الكمية يجب ان تكون اكبر من الصفر");
                }

                if (Shared.LibraAvailabe)
                {
                    IC_Item item = null;
                    if (item == null & view.GetRowCellValue(e.RowHandle, "ItemId") != DBNull.Value)
                        item = (from i in DB.IC_Items
                                where i.ItemId == Convert.ToInt32(view.GetRowCellValue(e.RowHandle, "ItemId"))
                                select i).FirstOrDefault();
                    if (item != null && item.VariableWeight == true)
                    {

                        if (view.GetRowCellValue(e.RowHandle, view.Columns["ActualPieces"]) == null ||
                            view.GetRowCellValue(e.RowHandle, view.Columns["ActualPieces"]).ToString().Trim() == string.Empty)
                        {
                            e.Valid = false;
                            view.SetColumnError(view.Columns["ActualPieces"], Shared.IsEnglish ? "Must add pieces Count" : "يجب اضافة الوزن");
                        }
                        if (view.GetRowCellValue(e.RowHandle, view.Columns["ActualWeight"]) == null ||
                            view.GetRowCellValue(e.RowHandle, view.Columns["ActualWeight"]).ToString().Trim() == string.Empty)
                        {
                            e.Valid = false;
                            view.SetColumnError(view.Columns["ActualWeight"], Shared.IsEnglish ? "Must add weight" : "يجب اضافة الوزن");
                        }
                    }
                    if (item != null && item.VariableWeight == false &&
                    (item.VariableWeight == true || item.PricingWithSmall == true))
                    {
                        if (view.GetRowCellValue(e.RowHandle, view.Columns["ActualPieces"]) == null ||
                            view.GetRowCellValue(e.RowHandle, view.Columns["ActualPieces"]).ToString().Trim() == string.Empty)
                        {
                            e.Valid = false;
                            view.SetColumnError(view.Columns["ActualPieces"], Shared.IsEnglish ? "Must add pieces Count" : "يجب اضافة الوزن");
                        }
                    }

                    if (item != null && item.VariableWeight == false &&
                   item.VariableWeight == false && item.PricingWithSmall == false && item.is_libra == false)
                    {
                        if (view.GetRowCellValue(e.RowHandle, view.Columns["ActualPieces"]) == null ||
                            view.GetRowCellValue(e.RowHandle, view.Columns["ActualPieces"]).ToString().Trim() == string.Empty)
                        {
                            e.Valid = false;
                            view.SetColumnError(view.Columns["ActualPieces"], Shared.IsEnglish ? "Must add pieces Count" : "يجب اضافة الوزن");
                        }
                    }

                    if (item.is_libra == true)
                    {

                        if (view.GetRowCellValue(e.RowHandle, view.Columns["ActualPieces"]) == null ||
                            view.GetRowCellValue(e.RowHandle, view.Columns["ActualPieces"]).ToString().Trim() == string.Empty)
                        {
                            e.Valid = false;
                            view.SetColumnError(view.Columns["ActualPieces"], Shared.IsEnglish ? "Must add pieces Count" : "يجب اضافة الوزن");
                        }
                        if (view.GetRowCellValue(e.RowHandle, view.Columns["LibraQty"]) == null ||
                            view.GetRowCellValue(e.RowHandle, view.Columns["LibraQty"]).ToString().Trim() == string.Empty)
                        {
                            e.Valid = false;
                            view.SetColumnError(view.Columns["LibraQty"], Shared.IsEnglish ? "Must add pieces Count" : "يجب اضافة الوزن");
                        }
                    }
                }

                if (Deleting)
                    return;
                //////////////////////////////////////////////////////////////////////////
                /*CHECK FOR ROW DUPLICATION*/
                // CASE: iTEM+EXPIREDATE
                //if (e.RowHandle >= 0) return;

            }
            catch
            {
            }
        }

        private void gridView1_InvalidRowException(object sender, DevExpress.XtraGrid.Views.Base.InvalidRowExceptionEventArgs e)
        {
            e.ExceptionMode = DevExpress.XtraEditors.Controls.ExceptionMode.NoAction;
        }

        private void gridView1_FocusedRowChanged(object sender, DevExpress.XtraGrid.Views.Base.FocusedRowChangedEventArgs e)
        {
            //Get_TotalAccount();
            //grd_FocusOnItemId(st_Store.InvoicesUseSearchItems ? "ItemId" : "ItemCode1");//mahmoud


            grd_FocusOnItemId(Shared.user.InvoicesUseSearchItems ? "ItemId" : "ItemCode1");//mahmoud

        }

        private void barBtn_Save_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if ((grdPrInvoice.FocusedView as GridView) != null)
                ++(grdPrInvoice.FocusedView as GridView).FocusedRowHandle;
            grdPrInvoice.DefaultView.PostEditor();
            grdPrInvoice.DefaultView.UpdateCurrentRow();

            if (!ValidData())
                return;
            XtraMessageBox.Show(Shared.IsEnglish ? ResICEn.Msgstocktaking_save : ResICAr.Msgstocktaking_save,
                Shared.IsEnglish ? ResICEn.MsgTWarn : ResICAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Information);


            if (isCommited)
            {
                //XtraMessageBox.Show(Shared.IsEnglish ? ResICEn.Msgstocktaking_cant_upadte : ResICAr.Msgstocktaking_cant_upadte,
                //    Shared.IsEnglish ? ResICEn.MsgTWarn : ResICAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                //return;

                DialogResult dr = XtraMessageBox.Show(Shared.IsEnglish ? "Are you sure you want to edit already commited Stocktacking? In case you choosed to proceed, the commited quatities will be deleted" : "هل تريد حقا حفظ جرد تم اعتماده مسبقا؟ في حال الاستمرار؛ سيتم حذف الأرصدة المعتمدة",
                          Shared.IsEnglish ? ResICEn.MsgTWarn : ResICAr.MsgTWarn, MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
                if (dr == DialogResult.No)
                    return;
            }

            SaveStockTacking();


            XtraMessageBox.Show(Shared.IsEnglish ? ResICEn.MsgSave : ResICAr.MsgSave, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void barBtnCommit_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            dtPR_Details.AcceptChanges();
            if (dt_ST_Date.EditValue == null)
            {
                XtraMessageBox.Show(Shared.IsEnglish ? "Please select Date" : "برجاء ادخال تاريخ التبديل", "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                dt_ST_Date.Focus();
                return;
            }
            if (dtPR_Details.Rows.Count < 1)
            {
                XtraMessageBox.Show(Shared.IsEnglish ? ResICEn.txtValidateNoRows : ResICAr.txtValidateNoRows,
                    Shared.IsEnglish ? ResICEn.MsgTWarn : ResICAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            if (!Shared.StockIsPeriodic && (lkp_Account.EditValue == null || Convert.ToInt32(lkp_Account.EditValue) == 0))
            {
                XtraMessageBox.Show(Shared.IsEnglish ? "Please select Account" : "برجاء اختيار الحساب", "", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                lkp_Account.Focus();
                return;
            }
            if (XtraMessageBox.Show(Shared.IsEnglish ? ResICEn.Msgstocktaking_commit : ResICAr.Msgstocktaking_commit,
                Shared.IsEnglish ? ResICEn.MsgTWarn : ResICAr.MsgTWarn, MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                if (isCommited)
                {
                    DialogResult dr = XtraMessageBox.Show(Shared.IsEnglish ? "Are you sure you want to edit already commited Stocktacking? In case you choosed to proceed, the commited quatities will be deleted" : "هل تريد حقا حفظ تبديل تم اعتماده مسبقا؟ في حال الاستمرار؛ سيتم حذف الأرصدة المعتمدة",
                             Shared.IsEnglish ? ResICEn.MsgTWarn : ResICAr.MsgTWarn, MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
                    if (dr == DialogResult.No)
                        return;
                }
                //if (StockTacking != null && StockTacking.IsCommited)
                //{
                //    XtraMessageBox.Show(Shared.IsEnglish ? ResICEn.Msgstocktaking_cant_upadte : ResICAr.Msgstocktaking_cant_upadte,
                //Shared.IsEnglish ? ResICEn.MsgTWarn : ResICAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                //    return;
                //}

                Commit();
                XtraMessageBox.Show(Shared.IsEnglish ? ResICEn.MsgstocktakingCommited : ResICAr.MsgstocktakingCommited, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                if (Shortage > Profit)
                {
                    XtraMessageBox.Show((Shared.IsEnglish ? ResICEn.Msgstocktaking_lose : ResICAr.Msgstocktaking_lose) + " " + decimal.Round((Shortage - Profit), 4), "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else if (Profit > Shortage)
                {
                    XtraMessageBox.Show((Shared.IsEnglish ? ResICEn.Msgstocktaking_win : ResICAr.Msgstocktaking_win) + " " + decimal.Round((Profit - Shortage), 4), "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                isCommited = true;
                barBtnCommit.Enabled = barBtnSave.Enabled = barBtnDelete.Enabled = false;
            }
        }

        public void Commit()
        {
            SaveStockTacking();
            frm_WaitingScreen ws = new frm_WaitingScreen();
            ws.Show();

            DAL.ERPDataContext DB = new DAL.ERPDataContext();

            Shortage = 0; Profit = 0;
            decimal f = 0;
            //DateTime insertTime = Utilities.Get_Server_DateTime();
            DateTime insertTime = dt_ST_Date.DateTime;// MyHelper.Get_Server_DateTime();
            int storeid = Convert.ToInt32(lkp_Store.EditValue);

            byte store_costmethod = DB.IC_Stores.Where(x => x.StoreId == storeid).Select(x => x.CostMethod).FirstOrDefault();

            foreach (DataRow row in dtPR_Details.Rows)
            {
                if (row.RowState != DataRowState.Deleted)
                {
                    decimal crnt = 0, suposd = 0;
                    int itmID = Convert.ToInt32(row["ItemId"]);
                    byte uom = Convert.ToByte(row["UomIndex"]);

                    decimal MediumUOMFactor = 1, LargeUOMFactor = 1;
                    if (row["MediumUOMFactor"] != DBNull.Value)
                        MediumUOMFactor = Convert.ToDecimal(row["MediumUOMFactor"]);
                    if (row["LargeUOMFactor"] != DBNull.Value)
                        LargeUOMFactor = Convert.ToDecimal(row["LargeUOMFactor"]);

                    //update 23/10/2017
                    string QC = row["Qc"].ToString();
                    string Serial = row["Serial"].ToString();
                    string batch = row["batch"].ToString();


                    int? vendorId = null;
                    if (row["vendorId"] != DBNull.Value)
                        vendorId = Convert.ToInt32(row["vendorId"]);

                    DateTime? expireDate = null;
                    if (row["Expire"] != DBNull.Value)
                        expireDate = Convert.ToDateTime(row["Expire"]);




                    IC_Item item = DB.IC_Items.Where(x => x.ItemId == itmID).Select(x => x).SingleOrDefault();
                    decimal PurchasePrice = Convert.ToDecimal(row["PurchasePrice"]);



                    crnt = Convert.ToDecimal(row["Qty"]); suposd = Convert.ToDecimal(row["Qty"]) + Convert.ToDecimal(row["Supposed"]);// Convert.ToDecimal(gridView2.GetRowCellValue(dtPR_Details.Rows.IndexOf(row), "Diff"));

                    if (item.is_libra.HasValue && item.is_libra.Value)
                    {
                        suposd = Convert.ToDecimal(row["Qty"]) + Convert.ToDecimal(row["LibraQty"]);
                    }

                    else if (item.PricingWithSmall == true)
                    {
                        var _uom = row["UOM"];
                        if (_uom != DBNull.Value)
                        {
                            byte uom1 = Convert.ToByte(_uom);
                            f = item.SmallUOM == uom1 ? 1 : (item.MediumUOM == uom1 ? item.MediumUOMFactorDecimal.Value : item.LargeUOMFactorDecimal.Value);
                        }
                        crnt = crnt / f; suposd = suposd / f;
                    }
                    //if (Shared.LibraAvailabe)
                    //{
                    //    decimal? f = uom == 0 ? 1 : (uom == 1 ? MediumUOMFactor : LargeUOMFactor);
                    //    if (item.PricingWithSmall.HasValue && item.PricingWithSmall.Value)
                    //        crnt = Convert.ToDecimal(Convert.ToDouble(row["Qty"]) / Convert.ToDouble(f));
                    //}

                    if (crnt > suposd)//Subtract from ItemStore
                    {
                        decimal qty = crnt - suposd;


                        decimal ActualPieces = 0, PiecesCount = 0;
                        if (row["ActualPieces"] != null && row["ActualPieces"] != DBNull.Value)
                            ActualPieces = Convert.ToDecimal(row["ActualPieces"]);
                        if (row["PiecesCount"] != null && row["PiecesCount"] != DBNull.Value)
                            PiecesCount = Convert.ToDecimal(row["PiecesCount"]);

                        int ActualPacks = 0, Pack = 0;
                        if (row["ActualPacks"] != null && row["ActualPacks"] != DBNull.Value)
                            ActualPacks = Convert.ToInt32(row["ActualPacks"]);
                        else
                            row["ActualPacks"] = 0;
                        if (row["Pack"] != null && row["Pack"] != DBNull.Value)
                            Pack = Convert.ToInt32(row["Pack"]);

                        //decimal piecesCount = ActualPieces > PiecesCount ? ActualPieces - PiecesCount : PiecesCount - ActualPieces;
                        decimal piecesCount = Convert.ToDecimal(row["ActualPieces"]) > 0 ? Convert.ToDecimal(row["ActualPieces"]) : (Convert.ToDecimal(row["ActualPieces"]) * -1);
                        int packs = Convert.ToInt32(row["ActualPacks"]) > 0 ? Convert.ToInt32(row["ActualPacks"]) : (Convert.ToInt32(row["ActualPacks"]) * -1);


                        //decimal piecesCount = (row["ActualPieces"] != null && row["ActualPieces"] != DBNull.Value ? Convert.ToDecimal(row["ActualPieces"]) : 0)
                        //- (row["PiecesCount"] != null && row["PiecesCount"] != DBNull.Value ? Convert.ToDecimal(row["PiecesCount"]) : 0);

                        //MyHelper.Subtract_from_store(itmID, StoreId, 0, (int)Process.AdujstOut,
                        //    uom, qty, MediumUOMFactor, LargeUOMFactor, store_costmethod, insertTime);

                        var itemSt = DB.IC_ItemStores.Where(x => x.ItemId == item.ItemId);
                        bool has_no_dimensions = true;
                        if (itemSt.Count() > 0)
                        {
                            has_no_dimensions = itemSt.Where(x => x.Height > 0 || x.Length > 0 || x.Width > 0).Count() <= 0;
                        }

                        else
                        {
                            has_no_dimensions = !(Shared.st_Store.UseHeightDimension || Shared.st_Store.UseWidthDimension || Shared.st_Store.UseLengthDimension);
                        }
                        if (has_no_dimensions)
                            Shortage += MyHelper.Subtract_from_store(itmID, storeid, StockTacking.IC_StockTaking_ID, (int)Process.AdujstOut,
                               uom, qty, MediumUOMFactor, LargeUOMFactor, store_costmethod, insertTime,
                               expireDate, batch, 0, 0, 0, piecesCount, item.mtrxParentItem, item.mtrxAttribute1, item.mtrxAttribute2,
                               item.mtrxAttribute3, 0, vendorId, QC, !Shared.StockIsPeriodic, Serial, packs);
                        else
                            Shortage += MyHelper.Subtract_from_store(itmID, storeid, StockTacking.IC_StockTaking_ID, (int)Process.AdujstOut,
                               uom, qty, MediumUOMFactor, LargeUOMFactor, store_costmethod, insertTime,
                               expireDate, batch, item.Length, item.Width, item.Height, piecesCount, item.mtrxParentItem, item.mtrxAttribute1, item.mtrxAttribute2,
                               item.mtrxAttribute3, 0, vendorId, QC, !Shared.StockIsPeriodic, Serial, packs);

                        //if (item.is_libra == true)
                        //{
                        //    Shortage = MyHelper.GetPriceLevelSellPrice(null, item, item.DfltSellUomIndx);
                        //}
                        //if (item.is_libra == true)
                        //    Shortage += (PurchasePrice / MyHelper.FractionToDouble(item.MediumUOMFactor)) * qty;
                        //else

                        //    Shortage += PurchasePrice * qty;
                    }
                    else if (suposd > crnt)//Add to ItemStore
                    {
                        decimal qty = suposd - crnt;

                        decimal ActualPieces = 0, PiecesCount = 0;
                        if (row["ActualPieces"] != null && row["ActualPieces"] != DBNull.Value)
                            ActualPieces = Convert.ToDecimal(row["ActualPieces"]);
                        if (row["PiecesCount"] != null && row["PiecesCount"] != DBNull.Value)
                            PiecesCount = Convert.ToDecimal(row["PiecesCount"]);

                        int ActualPacks = 0, Pack = 0;
                        if (row["ActualPacks"] != null && row["ActualPacks"] != DBNull.Value)
                            ActualPacks = Convert.ToInt32(row["ActualPacks"]);
                        else
                            row["ActualPacks"] = 0;
                        if (row["Pack"] != null && row["Pack"] != DBNull.Value)
                            Pack = Convert.ToInt32(row["Pack"]);

                        //decimal piecesCount = ActualPieces > PiecesCount ? ActualPieces - PiecesCount : PiecesCount - ActualPieces;
                        decimal piecesCount = Convert.ToDecimal(row["ActualPieces"]) > 0 ? Convert.ToDecimal(row["ActualPieces"]) : (Convert.ToDecimal(row["ActualPieces"]) * -1);
                        int pack = Convert.ToInt32(row["ActualPacks"]) > 0 ? Convert.ToInt32(row["ActualPacks"]) : (Convert.ToInt32(row["ActualPacks"]) * -1);

                        //    (row["ActualPieces"] != null && row["ActualPieces"] != DBNull.Value ? Convert.ToDecimal(row["ActualPieces"]) : 0)
                        //- (row["ActualPieces"] != null && row["ActualPieces"] != DBNull.Value ? Convert.ToDecimal(row["ActualPieces"]) : 0);

                        //MyHelper.AddItemToStore(itmID, StoreId, qty, uom, MediumUOMFactor, LargeUOMFactor, null,
                        //    0, (int)Process.AdujstIn, true, PurchasePrice * qty, insertTime);
                        var itemSt = DB.IC_ItemStores.Where(x => x.ItemId == item.ItemId);

                        bool has_no_dimensions = true;
                        if (itemSt.Count() > 0)
                        {
                            has_no_dimensions = itemSt.Where(x => x.Height > 0 || x.Length > 0 || x.Width > 0).Count() <= 0;
                        }

                        else
                        {
                            has_no_dimensions = !(Shared.st_Store.UseHeightDimension || Shared.st_Store.UseWidthDimension || Shared.st_Store.UseLengthDimension);
                        }
                        if (has_no_dimensions)
                        {
                            if (item.is_libra == true)
                                MyHelper.AddItemToStore(itmID, storeid, qty, uom, MediumUOMFactor, LargeUOMFactor, null,
                             StockTacking.IC_StockTaking_ID, (int)Process.AdujstIn, true, PurchasePrice / MyHelper.FractionToDouble(item.MediumUOMFactor) * qty, insertTime,
                              expireDate, batch, 0, 0, 0, piecesCount, item.mtrxParentItem, item.mtrxAttribute1, item.mtrxAttribute2,
                              item.mtrxAttribute3, 0, QC, Serial, pack);
                            else
                                MyHelper.AddItemToStore(itmID, storeid, qty, uom, MediumUOMFactor, LargeUOMFactor, null,
                            StockTacking.IC_StockTaking_ID, (int)Process.AdujstIn, true, PurchasePrice * qty * (item.PricingWithSmall == true ? f : 1), insertTime,
                             expireDate, batch, 0, 0, 0, piecesCount, item.mtrxParentItem, item.mtrxAttribute1, item.mtrxAttribute2,
                             item.mtrxAttribute3, 0, QC, Serial, pack);
                        }

                        else
                            MyHelper.AddItemToStore(itmID, storeid, qty, uom, MediumUOMFactor, LargeUOMFactor, null,
                             StockTacking.IC_StockTaking_ID, (int)Process.AdujstIn, true, PurchasePrice * qty * (item.PricingWithSmall == true ? f : 1), insertTime,
                              expireDate, batch, item.Length, item.Width, item.Height, piecesCount, item.mtrxParentItem, item.mtrxAttribute1, item.mtrxAttribute2,
                              item.mtrxAttribute3, 0, QC, Serial, pack);
                        //if (item.is_libra == true)
                        //{
                        //    PurchasePrice = MyHelper.GetPriceLevelPurchasePrice(null, item, item.DfltPrchsUomIndx);
                        //}
                        if (item.is_libra == true)
                            Profit += (PurchasePrice / MyHelper.FractionToDouble(item.MediumUOMFactor)) * qty;
                        else if (item.PricingWithSmall == true)
                        {
                            var _uom = row["UOM"];
                            if (_uom != DBNull.Value)
                            {
                                byte uom1 = Convert.ToByte(_uom);
                                f = item.SmallUOM == uom1 ? 1 : (item.MediumUOM == uom1 ? item.MediumUOMFactorDecimal.Value : item.LargeUOMFactorDecimal.Value);
                            }
                            Profit += (PurchasePrice * MyHelper.FractionToDouble(item.MediumUOMFactor)) * qty;
                        }
                        else
                            Profit += PurchasePrice * qty;
                    }
                }
            }

            var ST = DB.IC_StockTakings.Where(x => x.IC_StockTaking_ID == StockTacking.IC_StockTaking_ID).Select(x => x).SingleOrDefault();
            ST.IsCommited = true;
            ws.Dispose();
            CreateJournal(DB, ST, Shortage, Profit);

            if (Shortage > Profit)
            {
                ST.Shortage = Shortage - Profit;
            }
            else if (Profit > Shortage)
            {
                ST.Profit = Profit - Shortage;
            }
            StockTacking = ST;
            DB.SubmitChanges();
        }

        private void CreateJournal(ERPDataContext DB, IC_StockTaking pr, decimal Shortage, decimal Profit)
        {


            /*اذن اضافة تبديل دوري*/
            if (Shared.StockIsPeriodic)
                return;

            /*تبديل مستمر و لا يوجد حساب توجيه*/
            if (Shared.StockIsPeriodic == false && (pr.AccountId == null || pr.AccountId == 0))
                return;

            int processId = (int)Process.Stocking;

            DateTime today = MyHelper.Get_Server_DateTime();
            string note = "";

            note += (Shared.IsEnglish == true ? "Stocking No" : " تبديل رقم") + " " + pr.IC_StockTaking_ID;

            int BranchId = Convert.ToInt32(lkp_Store.EditValue);
            if (lkp_Store.GetColumnValue("ParentId") != null)
                BranchId = Convert.ToInt32(lkp_Store.GetColumnValue("ParentId"));

            /*Delete All Jornal details First To Store Them Again */
            DB.ACC_JournalDetails.DeleteAllOnSubmit(DB.ACC_JournalDetails.Where(x => x.JournalId == pr.JornalId));

            #region Save_Jornal
            DAL.ACC_Journal jornal;
            if (pr.JornalId > 0)
                jornal = DB.ACC_Journals.Where(x => x.JournalId == pr.JornalId).FirstOrDefault();
            else
            {
                jornal = new ACC_Journal();
                jornal.InsertDate = pr.Date;
                jornal.InsertUser = Shared.UserId;
                jornal.JCode = HelperAcc.Get_Jornal_Code();
                jornal.IsPosted = !Shared.OfflinePostToGL;
                jornal.SourceId = pr.IC_StockTaking_ID;
                jornal.StoreId = BranchId;

                jornal.Monthly_Code = HelperAcc.Get_Jornal_Monthly_Code(jornal.InsertDate, jornal.ProcessId);

                DB.ACC_Journals.InsertOnSubmit(jornal);
            }

            jornal.JNumber = pr.IC_StockTaking_ID.ToString();
            jornal.LastUpdateDate = today;
            jornal.LastUpdateUser = Shared.UserId;
            jornal.JNotes = note;
            jornal.ProcessId = processId;
           
            //jornal.InsertDate = dtInvoiceDate.DateTime;
            jornal.CrncId = 0;
            jornal.CrncRate = 1;
            DB.SubmitChanges();
            #endregion

            pr.JornalId = jornal.JournalId;

            #region JournalDetails
            var ininstore = Profit - Shortage;
            var outstore = Shortage - Profit;
            DAL.ACC_JournalDetail jornal_Detail_1 = new DAL.ACC_JournalDetail();
            jornal_Detail_1.JournalId = jornal.JournalId;
            //jornal_Detail_1.AccountId = Convert.ToInt32(lkp_Store.GetColumnValue("PurchaseAccount"));      // حساب المخزون = حساب المشتريات
            jornal_Detail_1.CostCenter = null;
            //jornal_Detail_1.Credit = 0;
            // jornal_Detail_1.Debit = total_purchase;
            jornal_Detail_1.Notes = note;
            jornal_Detail_1.CrncId = 0;
            jornal_Detail_1.CrncRate = 1;

            if (ininstore < 0)
            {
                jornal_Detail_1.Credit = outstore;
                jornal_Detail_1.Debit = 0;
                jornal_Detail_1.AccountId = Convert.ToInt32(lkp_Store.GetColumnValue("PurchaseAccount"));
            }
            else
            {
                jornal_Detail_1.Credit = ininstore;
                jornal_Detail_1.Debit = 0;
                jornal_Detail_1.AccountId = pr.AccountId.Value;
            }



            DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_1);





            DAL.ACC_JournalDetail jornal_Detail_2 = new DAL.ACC_JournalDetail();
            jornal_Detail_2.JournalId = jornal.JournalId;
            //jornal_Detail_2.AccountId = pr.AccountId.Value;                                                // حساب التوجيه 
            jornal_Detail_2.CostCenter = null;

            jornal_Detail_2.Notes = note;
            jornal_Detail_2.CrncId = 0;
            jornal_Detail_2.CrncRate = 1;

            if (ininstore < 0)
            {
                jornal_Detail_2.Credit = 0;
                jornal_Detail_2.Debit = outstore;
                jornal_Detail_2.AccountId = pr.AccountId.Value;
            }
            else
            {
                jornal_Detail_2.Credit = 0;
                jornal_Detail_2.Debit = ininstore;
                jornal_Detail_2.AccountId = Convert.ToInt32(lkp_Store.GetColumnValue("PurchaseAccount"));
            }
            DB.ACC_JournalDetails.InsertOnSubmit(jornal_Detail_2);
            DB.SubmitChanges();
            #endregion

        }

        private void barBtnClose_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            this.Close();
        }

        private void barBtnDelete_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            if (IsNew)
                return;
            //if (isCommited)
            //{
            //    XtraMessageBox.Show(Shared.IsEnglish ? ResICEn.Msgstocktaking_cant_upadte : ResICAr.Msgstocktaking_cant_upadte, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
            //    return;
            //}

            if (XtraMessageBox.Show(Shared.IsEnglish ? ResICEn.MsgDeleteInv : ResICAr.MsgDeleteInv,
                Shared.IsEnglish ? ResICEn.MsgTWarn : ResICAr.MsgTWarn, MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.No)
                return;

            ERPDataContext DB = new ERPDataContext();
            var sd = DB.IC_StockTaking_Details.Where(s => s.StockTaking_ID == StockTakingID);

            if (isCommited)
            {
                var st = DB.IC_StockTakings.Where(s => s.IC_StockTaking_ID == StockTakingID).FirstOrDefault();
                var journal = DB.ACC_Journals.Where(x => x.JournalId == st.JornalId).FirstOrDefault();
                var jDetails = DB.ACC_JournalDetails.Where(x => x.JournalId == st.JornalId);

                var itemstore = DB.IC_ItemStores.Where(x => (x.ProcessId == (int)Process.AdujstIn || x.ProcessId == (int)Process.AdujstOut) && x.SourceId == st.IC_StockTaking_ID);

                DB.ACC_Journals.DeleteOnSubmit(journal);
                DB.ACC_JournalDetails.DeleteAllOnSubmit(jDetails);
                DB.IC_ItemStores.DeleteAllOnSubmit(itemstore);
            }

            DB.IC_StockTaking_Details.DeleteAllOnSubmit(sd);

            DB.IC_StockTakings.DeleteAllOnSubmit(DB.IC_StockTakings.Where(s => s.IC_StockTaking_ID == StockTakingID));
            DB.SubmitChanges();
            XtraMessageBox.Show(Shared.IsEnglish ? ResICEn.MsgDel : ResICAr.MsgDel, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
            this.Close();
        }

        private void barBtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            PrintingSystem printSystem = new PrintingSystem();
            PrintableComponentLink printLink = new PrintableComponentLink();

            ((System.ComponentModel.ISupportInitialize)(printSystem)).BeginInit();

            printSystem.Links.AddRange(new object[] {
            printLink});
            printLink.Component = this.grdPrInvoice;

            printLink.PaperKind = System.Drawing.Printing.PaperKind.A4;
            printLink.Margins = new System.Drawing.Printing.Margins(5, 5, 115, 50);
            printLink.PrintingSystem = printSystem;
            printLink.PrintingSystemBase = printSystem;

            printLink.CreateMarginalHeaderArea +=
                new DevExpress.XtraPrinting.CreateAreaEventHandler(this.printableComponentLink1_CreateReportHeaderArea);

            printLink.CreateReportHeaderArea +=
                new DevExpress.XtraPrinting.CreateAreaEventHandler(this.printableComponentLink1_CreateReportMainInfo);

            printLink.CreateReportFooterArea +=
                new DevExpress.XtraPrinting.CreateAreaEventHandler(this.printableComponentLink1_CreateReportFooterInfo);

            ((System.ComponentModel.ISupportInitialize)(printSystem)).EndInit();

            printLink.CreateDocument();
            printLink.ShowPreview();
        }

        private void printableComponentLink1_CreateReportHeaderArea(object sender, DevExpress.XtraPrinting.CreateAreaEventArgs e)
        {
            //string ReportName = (Shared.IsEnglish ? (ResICEn.txtIC_stocktaking + " " + ResICEn.txtInvDate) : (ResICAr.txtIC_stocktaking + " " + ResICAr.txtInvDate))
            //    + " " + dt_ST_Date.DateTime.ToShortDateString();
            
                string ReportName = (Shared.IsEnglish ? (ResICEn.txtIC_replacement + " " + ResICEn.txtInvDate) : (ResICAr.txtIC_replacement + " " + ResICAr.txtInvDate))
                + " " + dt_ST_Date.DateTime.ToShortDateString();
            //Utilities.CreateReportHeader(e, ReportName, string.Empty, string.Empty);
            ErpUtils.CreateReportHeader(e, ReportName, string.Empty, string.Empty);

        }

        private void printableComponentLink1_CreateReportFooterInfo(object sender, DevExpress.XtraPrinting.CreateAreaEventArgs e)
        {
            //RectangleF ShortageRec = new RectangleF((float)585, (float)17, 165, 19);
            RectangleF ProfitRec = new RectangleF((float)348, (float)17, 165, 19);

            e.Graph.StringFormat = new BrickStringFormat(StringAlignment.Far);
            e.Graph.Font = new Font("Times New Roman", 13, FontStyle.Regular);
            e.Graph.BorderColor = Color.Gray;
            e.Graph.DefaultBrickStyle.BorderColor = Color.Gray;
            e.Graph.BackColor = Color.Snow;
            e.Graph.ForeColor = Color.Black;

            if (StockTakingID <= 0)
            {
                XtraMessageBox.Show(Shared.IsEnglish ? ResEn.MsgAskToSave : ResAr.MsgAskToSave, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            IC_StockTaking st = DB.IC_StockTakings.FirstOrDefault(x => x.IC_StockTaking_ID == StockTakingID);
            decimal shortage = 0, profit = 0;
            string rsult = "";
            if (st.Shortage != null)
            {
                shortage = st.Shortage.Value;

                rsult = string.Format("{0} {1} {2}", (Shared.IsEnglish ? "Shortage : " : "خسارة "), shortage.ToString("#.##"), Shared.st_Store.MainCurrencyName != null ? Shared.st_Store.MainCurrencyName : "");
            }
            else if (st.Profit != null)
            {
                profit = st.Profit.Value;

                rsult = string.Format("{0} {1} {2}", (Shared.IsEnglish ? "Profit : " : "ربح "), profit.ToString("#.##"), Shared.st_Store.MainCurrencyName != null ? Shared.st_Store.MainCurrencyName : "");
            }

            //if (rsult != "")
                //e.Graph.DrawString(rsult, Color.Gray, ProfitRec, BorderSide.All);

            e.Graph.BackColor = Color.White;
            e.Graph.DrawString("", Color.Black, new RectangleF((float)10, (float)124, 1, 15), BorderSide.None);

            RectangleF recRes = new RectangleF((float)20, (float)60, 100, 19);
            RectangleF recRev = new RectangleF((float)600, (float)60, 100, 19);

            e.Graph.DrawString("مسئول المخزن", Color.Black, recRes, BorderSide.None);
            e.Graph.DrawString("المراجع", Color.Black, recRev, BorderSide.None);
        }

        private void printableComponentLink1_CreateReportMainInfo(object sender, DevExpress.XtraPrinting.CreateAreaEventArgs e)
        {
            string CompName = string.Empty;
            Image image = null;
            //Utilities.GetReportHeaderData(out CompName, out image);
            ErpUtils.GetReportHeaderData(out CompName, out image);


            RectangleF recNotes = new RectangleF((float)395, (float)42, 355, 19);
            RectangleF recDate = new RectangleF((float)585, (float)17, 165, 19);
            RectangleF recStore = new RectangleF((float)395, (float)17, 165, 19);

            e.Graph.StringFormat = new BrickStringFormat(StringAlignment.Far);
            e.Graph.Font = new Font("Times New Roman", 13, FontStyle.Regular);
            e.Graph.BorderColor = Color.Gray;
            e.Graph.DefaultBrickStyle.BorderColor = Color.Gray;
            e.Graph.BackColor = Color.Snow;
            e.Graph.ForeColor = Color.Black;

            string date = (Shared.IsEnglish ? ResICEn.txtInvDate : ResICAr.txtInvDate) + " " + dt_ST_Date.Text;

            string store = (Shared.IsEnglish ? ResICEn.txtStore : ResICAr.txtStore) + " " + lkp_Store.Text;

            string notes = (Shared.IsEnglish ? ResICEn.txtNotes : ResICAr.txtNotes) + " " + txtNote.Text;

            e.Graph.DrawString(notes, Color.Gray, recNotes, BorderSide.All);
            e.Graph.DrawString(date, Color.Gray, recDate, BorderSide.All);
            e.Graph.DrawString(store, Color.Gray, recStore, BorderSide.All);

            e.Graph.BackColor = Color.White;
            e.Graph.DrawString("", Color.Black, new RectangleF((float)10, (float)124, 1, 15), BorderSide.None);
        }



        private void txtDiscountRatio_EditValueChanged(object sender, EventArgs e)
        {
            //Get_TotalAccount();
        }

        private void grid_ProcessGridKey(object sender, KeyEventArgs e)
        {
            try
            {
                DevExpress.XtraGrid.GridControl grid = sender as DevExpress.XtraGrid.GridControl;
                var view = (grid.FocusedView as DevExpress.XtraGrid.Views.Grid.GridView);
                if (e.KeyCode == Keys.Enter)
                {
                    var focused_column = (grid.FocusedView as DevExpress.XtraGrid.Views.Base.ColumnView).FocusedColumn;
                    int focused_row_handle = (grid.FocusedView as DevExpress.XtraGrid.Views.Base.ColumnView).FocusedRowHandle;

                    if (view.FocusedColumn == view.Columns["ItemId"]
                        || view.FocusedColumn == view.Columns["ItemCode1"])
                    {
                        string temp = view.FocusedColumn.FieldName;
                        view.FocusedColumn = view.Columns["UOM"];
                        if (view.GetFocusedRowCellValue(temp) == null || string.IsNullOrEmpty(view.GetFocusedRowCellValue(temp).ToString()))
                            view.FocusedColumn = view.Columns[temp];
                        return;
                    }
                    if (view.FocusedColumn == view.Columns["UOM"])
                    {
                        if (Shared.LibraAvailabe)
                            view.FocusedColumn = view.Columns["ActualPieces"];
                        else
                            view.FocusedColumn = view.Columns["Supposed"];
                        return;
                    }
                    if (view.FocusedColumn == view.Columns["Supposed"])
                    {
                        if (!Shared.LibraAvailabe)
                            grid_ProcessGridKey(sender, new KeyEventArgs(Keys.Tab));
                        else
                            view.FocusedColumn = view.Columns["ActualPieces"];

                        if (view.GetFocusedRowCellValue("Supposed") == null || string.IsNullOrEmpty(view.GetFocusedRowCellValue("Supposed").ToString()))
                            view.FocusedColumn = view.Columns["Supposed"];
                    }
                    if (Shared.LibraAvailabe &&
                        (view.FocusedColumn == view.Columns["ActualPieces"] ||
                        view.FocusedColumn == view.Columns["LibraQty"]))
                    {
                        view.FocusedColumn = view.Columns["ActualWeight"];
                    }
                    else if (view.FocusedColumn == view.Columns["ActualWeight"])
                        grid_ProcessGridKey(sender, new KeyEventArgs(Keys.Tab));

                    if (view.FocusedRowHandle < 0)//|| view.FocusedRowHandle == view.RowCount)
                    {
                        view.AddNewRow();
                        //view.FocusedColumn = view.Columns[st_Store.InvoicesUseSearchItems ? "ItemId" : "ItemCode1"];//mahmoud
                        view.FocusedColumn = view.Columns[Shared.user.InvoicesUseSearchItems ? "ItemId" : "ItemCode1"];//mahmoud

                    }
                    else
                    {
                        view.FocusedRowHandle = focused_row_handle + 1;
                        //view.FocusedColumn = view.Columns[frmMain.st_Store.InvoicesUseSearchItems ? "ItemId" : "ItemCode1"];                        
                        view.FocusedColumn = view.Columns[Shared.user.InvoicesUseSearchItems ? "ItemId" : "ItemCode1"];

                    }

                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == Keys.Tab && e.Modifiers != Keys.Shift)
                {
                    if (view.FocusedColumn.VisibleIndex == 0)
                        view.FocusedColumn = view.VisibleColumns[view.VisibleColumns.Count - 1];
                    else
                        view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex - 1];
                    e.Handled = true;
                    return;
                }
                if (e.KeyCode == Keys.Tab && e.Modifiers == Keys.Shift)
                {
                    if (view.FocusedColumn.VisibleIndex == view.VisibleColumns.Count)
                        view.FocusedColumn = view.VisibleColumns[0];
                    else
                        view.FocusedColumn = view.VisibleColumns[view.FocusedColumn.VisibleIndex + 1];
                    e.Handled = true;
                    return;
                }
            }
            catch
            { }
        }

        private void repUOM_CustomDisplayText(object sender, DevExpress.XtraEditors.Controls.CustomDisplayTextEventArgs e)
        {
            try
            {
                if (e.Value != null)
                    e.DisplayText = uom_list.Where(x => x.UOMId == Convert.ToInt32(e.Value)).FirstOrDefault().UOM;
            }
            catch (Exception ex)
            {

            }
        }

        private void gridView1_CustomRowFilter(object sender, RowFilterEventArgs e)
        {
            var view = sender as GridView;
            bool visible = Convert.ToBoolean(view.GetRowCellValue(e.ListSourceRow, "Visible"));
            if (visible == false)
            {
                e.Visible = false;
                e.Handled = true;
            }
        }

        private void lkp_Store_EditValueChanged(object sender, EventArgs e)
        {
            if ((IsNew && dtPR_Details.Rows.Count > 0))
            {
                DialogResult dr = MessageBox.Show(Shared.IsEnglish ? ResICEn.MsgDataModified : ResICAr.MsgDataModified,
                    Shared.IsEnglish ? ResICEn.MsgTQues : ResICAr.MsgTQues, MessageBoxButtons.YesNo, MessageBoxIcon.Warning);
                if (dr == DialogResult.Yes)
                    SaveStockTacking();
                Reset();
            }
            StoreId = Convert.ToInt32(lkp_Store.EditValue);
        }

        private void RepXpireDate_CustomDisplayText(object sender, DevExpress.XtraEditors.Controls.CustomDisplayTextEventArgs e)
        {
            try
            {
                if (Convert.ToDateTime(e.Value).ToShortDateString() != "01/01/0001")
                    e.DisplayText = Convert.ToDateTime(e.Value).ToShortDateString();
            }
            catch { }
        }

        private void lkp_Store_Modified(object sender, EventArgs e)
        {
            dataModified = true;
        }


        void SaveStockTacking()
        {
            DAL.ERPDataContext DB = new DAL.ERPDataContext();
            var max_st = DB.IC_StockTakings.Select(x => x.IC_StockTaking_ID).Count() == 0 ? 0 :
                DB.IC_StockTakings.Select(x => x.IC_StockTaking_ID).Max();

            var st = DB.IC_StockTakings.Where(s => s.IC_StockTaking_ID == StockTakingID).SingleOrDefault();
            if (st == null)
            {
                st = new IC_StockTaking();
                DB.IC_StockTakings.InsertOnSubmit(st);
            }


            if (st.IsCommited)
            {
                //XtraMessageBox.Show(Shared.IsEnglish ? ResICEn.Msgstocktaking_cant_upadte : ResICAr.Msgstocktaking_cant_upadte,
                //    Shared.IsEnglish ? ResICEn.MsgTWarn : ResICAr.MsgTWarn, MessageBoxButtons.OK, MessageBoxIcon.Warning);
                //return;


                st.IsCommited = false;
                st.Profit = st.Shortage = null;
                var journal = DB.ACC_Journals.Where(x => x.JournalId == st.JornalId).FirstOrDefault();
                var jDetails = DB.ACC_JournalDetails.Where(x => x.JournalId == st.JornalId);

                var itemstore = DB.IC_ItemStores.Where(x => (x.ProcessId == (int)Process.AdujstIn || x.ProcessId == (int)Process.AdujstOut) && x.SourceId == st.IC_StockTaking_ID);

                DB.ACC_Journals.DeleteOnSubmit(journal);
                DB.ACC_JournalDetails.DeleteAllOnSubmit(jDetails);
                DB.IC_ItemStores.DeleteAllOnSubmit(itemstore);
                st.JornalId = null;
                MyHelper.UpdateST_UserLog(DB, StockTakingID.ToString(), "تعديل تبديل معتمد", (int)FormAction.Edit, (int)FormsNames.IC_StockTaking);

            }

            st.Replacement = true;
            st.Date = (DateTime)dt_ST_Date.EditValue;
            st.Notes = txtNote.Text;
            st.StorId = Convert.ToInt32(lkp_Store.EditValue);// (int)lkp_Store.EditValue;                
            st.IsCommited = false;
            isCommited = false;
            st.AccountId = Convert.ToInt32(lkp_Account.EditValue);
            st.UserID = Shared.UserId;

            DB.SubmitChanges();

            StockTakingID = st.IC_StockTaking_ID;

            var ST = DB.IC_StockTaking_Details.Select(x => x).Where(x => x.StockTaking_ID == StockTakingID);
            if (ST.ToList().Count > 0)//The StockTaking will be Overriden
            {
                DB.IC_StockTaking_Details.DeleteAllOnSubmit(ST);
                DB.SubmitChanges();
            }
            var itms = linqServerModeSource1.QueryableSource as IEnumerable<IC_Item>;

            foreach (DataRow r in dtPR_Details.Rows)
            {
                if (r.RowState != DataRowState.Deleted)
                {
                    IC_StockTaking_Detail std = new IC_StockTaking_Detail();
                    std.CurrentQty = Math.Round(Convert.ToDecimal(r["Qty"]), 2);
                    std.SupposedQty = Math.Round(Convert.ToDecimal(r["Supposed"]), 2);
                    std.ItemID = Convert.ToInt32(r["ItemId"]);
                    if (r["UOM"] != DBNull.Value)
                        std.UOM = Convert.ToByte(r["UOM"]);

                    if (r["UomIndex"] != DBNull.Value)
                        std.UomIndex = Convert.ToByte(r["UomIndex"]);

                    if (Shared.LibraAvailabe)
                    {
                        if (r["LibraQty"] != DBNull.Value)
                            std.LibraQty = Convert.ToDecimal(r["LibraQty"]);

                        if (r["ActualWeight"] != DBNull.Value)
                            std.ActualWeight = Convert.ToDecimal(r["ActualWeight"]);

                        //if (r["PricingWithSmall"] != DBNull.Value && r["PricingWithSmall"] != null &&
                        //    Convert.ToBoolean(r["PricingWithSmall"]) == true)
                        //{
                        //    var itm = itms.Where(x => x.ItemId == std.ItemID).First();
                        //   decimal f = itm.SmallUOM == std.UomIndex ? 1 : (itm.MediumUOM == std.UomIndex ? itm.MediumUOMFactorDecimal.Value : itm.LargeUOMFactorDecimal.Value);

                        //}
                    }

                    if (r["ActualPieces"] != DBNull.Value)
                        std.ActualPieces = Convert.ToDecimal(r["ActualPieces"]);
                    //==================Samar========================//
                    if (r["Expire"] != DBNull.Value)
                        std.Expire =Convert.ToDateTime(r["Expire"]);
                    

                    std.PurchasePrice = Convert.ToDecimal(r["PurchasePrice"]);
                    std.StockTaking_ID = st.IC_StockTaking_ID;
                    DB.IC_StockTaking_Details.InsertOnSubmit(std);
                }
            }

            if (max_st < st.IC_StockTaking_ID)
            {
                MyHelper.UpdateST_UserLog(DB, StockTakingID.ToString(), "حفظ تبديل جديد", (int)FormAction.Add, (int)FormsNames.IC_StockTaking);
            }
            else
            {
                MyHelper.UpdateST_UserLog(DB, StockTakingID.ToString(), "تعديل تبديل", (int)FormAction.Edit, (int)FormsNames.IC_StockTaking);
            }


            DB.SubmitChanges();
            StockTakingID = st.IC_StockTaking_ID;
            StockTacking = st;
            //XtraMessageBox.Show(Shared.IsEnglish ? ResICEn.MsgSave : ResICAr.MsgSave, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
            IsNew = false;

            DoValidate();
            dataModified = false;
            dtPR_Details.AcceptChanges();
        }

        void grd_FocusOnItemId(string columnName)
        {
            GridView view = grdPrInvoice.FocusedView as GridView;
            view.FocusedColumn = view.Columns[columnName];
        }

        private void Reset()
        {
            dtPR_Details.Rows.Clear();
            dtUOM.Rows.Clear();
            int defaultStoreId = 0;
            if (StockTakingID != 0)
            {
                var storeId = DB.IC_StockTakings.Where(a => a.IC_StockTaking_ID == StockTakingID).FirstOrDefault().StorId;
                var stores_table = MyHelper.Get_StoresNotStopped(storeId, true, out defaultStoreId, Shared.user.UserChangeStore, Shared.user.DefaultStore,
                Shared.UserId);
                lkp_Store.Properties.DataSource = stores_table;
            }
            else
            {
                var stores_table = MyHelper.Get_StoresNotStopped(0, true, out defaultStoreId, Shared.user.UserChangeStore, Shared.user.DefaultStore,
                Shared.UserId);

                lkp_Store.Properties.DataSource = stores_table;
            }
        }

        private void BindDataSources()
        {
            DB = new DAL.ERPDataContext();

            #region Get Stores
            int DefaultStore;
            //var stores_table = MyHelper.Get_Stores(true, out DefaultStore, Shared.user.UserChangeStore, Shared.user.DefaultStore, Shared.UserId);
            var stores_table = MyHelper.Get_StoresNotStopped(0, true, out DefaultStore, Shared.user.UserChangeStore, Shared.user.DefaultStore, Shared.UserId);

            lkp_Store.Properties.DataSource = stores_table;
            int? defaultStoreId = null;

            //mahmoud:27-1-2013

            //if (st_Store.UserId > 0 && st_Store.DefaultStore != 0)
            //    defaultStoreId = st_Store.DefaultStore;


            if (Shared.user.UserId > 0 && Shared.user.DefaultStore != 0)
                defaultStoreId = Shared.user.DefaultStore;
            else
                defaultStoreId = stores_table.FirstOrDefault().StoreId;

            lkp_Store.Properties.DisplayMember = "StoreNameAr";
            lkp_Store.Properties.ValueMember = "StoreId";
            lkp_Store.EditValue = defaultStoreId;
            #endregion

            linqServerModeSource1.ElementType = typeof(DAL.IC_Item);
            linqServerModeSource1.KeyExpression = "ItemId";
            linqServerModeSource1.QueryableSource = from i in new DAL.ERPDataContext().IC_Items
                                                    where i.ItemType != (int)ItemType.MatrixParent
                                                       && i.ItemType != (int)ItemType.Service
                                                       && i.ItemType != (int)ItemType.Subtotal
                                                    select i;

            #region dtPR_Details
            dtPR_Details.Columns.Clear();
            dtPR_Details.Columns.Add("ItemId");//0
            dtPR_Details.Columns.Add("ItemCode1");//1
            dtPR_Details.Columns.Add("UOM");//2
            dtPR_Details.Columns.Add("Qty");//5
            dtPR_Details.Columns.Add("MediumUOMFactor");//6
            dtPR_Details.Columns.Add("LargeUOMFactor");//7
            dtPR_Details.Columns.Add("UomIndex");//8
            dtPR_Details.Columns.Add("Supposed");//9
            dtPR_Details.Columns.Add("PurchasePrice");//10
            dtPR_Details.Columns.Add("Batch");
            dtPR_Details.Columns.Add("QC");
            dtPR_Details.Columns.Add("Serial");
            dtPR_Details.Columns.Add("VendorId");
            dtPR_Details.Columns.Add("Expire");
            dtPR_Details.Columns.Add("PricingWithSmall");
            dtPR_Details.Columns.Add("VariableWeight");
            dtPR_Details.Columns.Add("kg_Weight_libra");
            dtPR_Details.Columns.Add("LibraQty");
            dtPR_Details.Columns.Add("PiecesCount");
            dtPR_Details.Columns.Add("ActualPieces");
            dtPR_Details.Columns.Add("ActualWeight");
            // dtPR_Details.Columns.Add("Diff");
            dtPR_Details.Columns.Add("Pack");
            dtPR_Details.Columns.Add("ActualPacks");




            grdPrInvoice.DataSource = dtPR_Details;
            #endregion

            #region UOM
            repUOM.DisplayMember = "Uom";
            repUOM.ValueMember = "UomId";
            repUOM.DataSource = MyHelper.GetUomDataTable(dtUOM);
            #endregion

            DB = new ERPDataContext();
            repItems.DataSource = (from i in DB.IC_Items
                                   where i.ItemType != (int)ItemType.MatrixParent
                                   where Shared.st_Store.BuyAssembly == false ?
                                                              i.ItemType != (int)ItemType.Assembly : true
                                   // where invoiceId == 0 ? i.IsDeleted == false : true
                                   select new
                                   {
                                       ItemCode1 = i.ItemCode1,
                                       ItemCode2 = i.ItemCode2,
                                       ItemId = i.ItemId,
                                       ItemNameAr = i.ItemNameAr,
                                       ItemNameEn = i.ItemNameEn,
                                       i.MaxQty,
                                       i.MinQty,
                                       i.ReorderLevel,
                                       i.IsExpire,
                                       i.PurchasePrice,
                                       SellPrice = i.SmallUOMPrice,
                                       i.PicPath,
                                       i.MediumUOM,
                                       i.LargeUOM,
                                       i.IC_Category.CategoryNameAr,
                                       i.IC_Company.CompanyNameAr
                                   }).ToList();

            repItems.DisplayMember = "ItemNameAr";
            repItems.ValueMember = "ItemId";


            #region Get Vendors
            int lastVenId = 0;
            int? goldenVendor = MyHelper.GetVendors(out lst_Vendors, out lastVenId);
            repVendor.DisplayMember = "VenNameAr";
            repVendor.ValueMember = "VendorId";
            repVendor.DataSource = lst_Vendors;

            #endregion


        }

        private void LoadItemRow(DAL.IC_Item item, DataRow row)
        {
            if (item != null && item.ItemId > 0)
            {
                row["ItemId"] = item.ItemId;
                row["ItemCode1"] = item.ItemCode1;
                row["Qty"] = "";
                //row["MediumUOMFactor"] = Utilities.FractionToDouble(item.MediumUOMFactor);
                //row["LargeUOMFactor"] = Utilities.FractionToDouble(item.LargeUOMFactor);

                row["MediumUOMFactor"] = MyHelper.FractionToDouble(item.MediumUOMFactor);
                row["LargeUOMFactor"] = MyHelper.FractionToDouble(item.LargeUOMFactor);

                MyHelper.GetUOMs(item, dtUOM, uom_list);
                row["UOM"] = dtUOM.Rows[item.DfltSellUomIndx]["UomId"];
                row["UomIndex"] = item.DfltSellUomIndx;

                row["PurchasePrice"] = item.PurchasePrice;

                RepBatch.DataSource = (from m in DB.ManfDetails
                                       where m.ItemId == item.ItemId
                                       select new
                                       {
                                           m.Batch
                                       }
                                   ).Distinct().ToList();
                RepBatch.ValueMember = "Batch";
                RepBatch.DisplayMember = "Batch";


                repSerial.DataSource = (from m in DB.ManfDetails
                                        where m.ItemId == item.ItemId
                                        select new
                                        {
                                            m.Serial
                                        }
                                  ).Distinct().ToList();
                RepBatch.ValueMember = "Serial";
                RepBatch.DisplayMember = "Serial";

                repQC.DataSource = (from m in DB.ManfDetails
                                    where m.ItemId == item.ItemId
                                    select new
                                    {
                                        m.QC
                                    }
                                  ).Distinct().ToList();
                RepBatch.ValueMember = "QC";
                RepBatch.DisplayMember = "QC";


                //RepXpire.DataSource = (from m in DB.ManfDetails
                //                    where m.ItemId == item.ItemId
                //                    select new
                //                    {
                //                        m.Expire
                //                    }
                //                 ).Distinct().ToList();
                //RepXpire.ValueMember = "Expire";
                //RepXpire.DisplayMember = "Expire";

                row["PricingWithSmall"] = item.PricingWithSmall;
                row["VariableWeight"] = item.VariableWeight;

            }
        }

        private void GetStockTackingDetails()
        {
            var Qty = from st in DB.IC_StockTaking_Details
                      join i in DB.IC_Items on st.ItemID equals i.ItemId
                      where st.StockTaking_ID == StockTakingID
                      select new
                      {
                          ItemId = st.ItemID,
                          Code = i.ItemCode1,
                          Name = i.ItemNameEn,
                          Current = st.CurrentQty,
                          Supposed = st.SupposedQty,
                          UomId = st.UOM,
                          UomIndex = st.UomIndex,
                          MediumUOMFactor = i.MediumUOMFactor,
                          LargeUOMFactor = i.LargeUOMFactor,
                          st.PurchasePrice,
                          st.ActualWeight,
                          st.ActualPieces,
                          st.LibraQty,
                          i.is_libra,
                          i.PricingWithSmall,
                          i.VariableWeight,
                          st.Expire,
                      };

            dtPR_Details.Rows.Clear();

            foreach (var q in Qty)
            {
                DataRow row = dtPR_Details.NewRow();

                row["ItemId"] = q.ItemId;
                row["ItemCode1"] = q.Code;
                row["UOM"] = q.UomId;
                row["UomIndex"] = q.UomIndex;
                row["kg_Weight_libra"] = row["Qty"] = Math.Round(q.Current, 2);
                if (q.is_libra == true)
                    row["kg_Weight_libra"] = Math.Round(q.Current / MyHelper.FractionToDouble(q.MediumUOMFactor), 3);
                //row["MediumUOMFactor"] = Utilities.FractionToDouble(q.MediumUOMFactor);
                //row["LargeUOMFactor"] = Utilities.FractionToDouble(q.LargeUOMFactor);

                row["MediumUOMFactor"] = MyHelper.FractionToDouble(q.MediumUOMFactor);
                row["LargeUOMFactor"] = MyHelper.FractionToDouble(q.LargeUOMFactor);

                row["Supposed"] = Math.Round(Convert.ToDecimal(q.Supposed), 2);
                row["PurchasePrice"] = Math.Round(Convert.ToDecimal(q.PurchasePrice), 2);
                row["ActualWeight"] = Math.Round(Convert.ToDecimal(q.ActualWeight), 2);
                row["ActualPieces"] = Math.Round(Convert.ToDecimal(q.ActualPieces), 2);
                row["LibraQty"] = Math.Round(Convert.ToDecimal(q.LibraQty), 2);
                row["PiecesCount"] = MyHelper.GetItemPieces(dt_ST_Date.DateTime, q.ItemId, Convert.ToInt32(lkp_Store.EditValue));
                row["PricingWithSmall"] = q.PricingWithSmall;
                row["VariableWeight"] = q.VariableWeight;
                //=======================Samar=====================//
                row["Expire"] = q.Expire;
                
                dtPR_Details.Rows.Add(row);
                gridView1.UpdateCurrentRow();
            }
            dtPR_Details.AcceptChanges();
        }

        void DoValidate()
        {
            txtNote.DoValidate();
            dt_ST_Date.DoValidate();
            lkp_Store.DoValidate();
            dtPR_Details.AcceptChanges();
        }

        private bool ValidData()
        {
            if (IsNew)
            {
                if (prvlg != null && !prvlg.CanAdd)
                {
                    // "عفوا, انت لا تمتلك صلاحية انشاء بيان جديد"
                    XtraMessageBox.Show(Shared.IsEnglish ? ResICEn.MsgPrvNew : ResICAr.MsgPrvNew, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }
            else
            {
                if (prvlg != null && !prvlg.CanEdit)
                {
                    //"عفوا, انت لا تمتلك صلاحية تعديل هذا البيان"
                    XtraMessageBox.Show(Shared.IsEnglish ? ResICEn.MsgPrvEdit : ResICAr.MsgPrvEdit, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
            }

            if (!grdPrInvoice.DefaultView.UpdateCurrentRow())
                return false;

            if (dtPR_Details.Rows.Count < 1)
            {
                XtraMessageBox.Show(this.LookAndFeel, Shared.IsEnglish ? ResICEn.txtValidateNoRows : ResICAr.txtValidateNoRows, "", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return false;
            }
            return true;
        }

        DialogResult ChangesMade()
        {
            if (
                dataModified ||
                dtPR_Details.GetChanges(DataRowState.Added) != null ||
                dtPR_Details.GetChanges(DataRowState.Modified) != null ||
                dtPR_Details.GetChanges(DataRowState.Deleted) != null
                )
            {
                DialogResult r = XtraMessageBox.Show(Shared.IsEnglish ? ResICEn.MsgDataModified : ResICAr.MsgDataModified, "", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);
                if (r == DialogResult.Yes)
                {
                    if (!ValidData())
                        return DialogResult.Cancel;

                    barBtnSave.PerformClick();
                    return r;
                }
                else if (r == DialogResult.No)
                {
                    // no thing made, continue closing or do next or do previous
                    return r;
                }
                else if (r == DialogResult.Cancel)
                {
                    //cancel form action
                    return r;
                }
            }
            return DialogResult.No;
        }

        void LoadPrivilege()
        {
            //if (frmMain.LstUserPrvlg != null)
            //{
            //    prvlg = frmMain.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.IC_StockTaking).FirstOrDefault();

            //    if (!prvlg.CanPrint)
            //        barBtnPrint.Enabled = false;
            //}

            if (Shared.LstUserPrvlg != null)
            {
                prvlg = Shared.LstUserPrvlg.Where(x => x.PId == (int)FormsNames.IC_StockTaking).FirstOrDefault();

                if (!prvlg.CanPrint)
                    barBtnPrint.Enabled = false;
            }
        }

        private void frm_IC_ItemReplacment_Shown(object sender, EventArgs e)
        {
            grdPrInvoice.Focus();
            var view = (grdPrInvoice.FocusedView as DevExpress.XtraGrid.Views.Grid.GridView);
            //view.FocusedColumn = view.Columns[st_Store.InvoicesUseSearchItems ? "ItemId" : "ItemCode1"];//mahmoud

            view.FocusedColumn = view.Columns[Shared.user.InvoicesUseSearchItems ? "ItemId" : "ItemCode1"];

        }

        private void barBtnHelp_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Help.ShowHelp(null, @"ERP.chm", HelpNavigator.KeywordIndex, "تبديل المخازن");
        }

        private void gridView2_CustomUnboundColumnData(object sender, CustomColumnDataEventArgs e)
        {
            GridView view = sender as GridView;
            var supposed = view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Supposed");
            var qty = view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Qty");

            if (supposed != null && (supposed != DBNull.Value) && qty != DBNull.Value && qty != null && qty.ToString() != string.Empty)
                if (e.Column.FieldName == "Diff" && e.IsGetData) e.Value = Convert.ToDecimal(view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Supposed")) +
                            Convert.ToDecimal(view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Qty"));
        }

        private void gridView2_ShowingEditor(object sender, CancelEventArgs e)
        {
            GridView view = sender as GridView;
            if (view.FocusedColumn.FieldName == "ActualWeight")
            {
                try
                {
                    var PricingWithSmall = (grdPrInvoice.FocusedView as GridView).GetFocusedRowCellValue("PricingWithSmall");
                    if (PricingWithSmall != null && PricingWithSmall != DBNull.Value && Convert.ToBoolean(PricingWithSmall))
                    {
                        e.Cancel = true;
                    }
                }
                catch (Exception ex)
                {
                    Utilities.save_Log(ex.Message, ex);
                }
            }
        }
    }
}